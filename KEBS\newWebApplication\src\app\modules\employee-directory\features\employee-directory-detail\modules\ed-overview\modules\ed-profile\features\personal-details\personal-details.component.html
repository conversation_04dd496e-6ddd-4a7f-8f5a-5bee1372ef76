<div class="container-fluid p-0 pr-1 mt-2 personal-details-styles">
  <ng-container *ngIf="loaderObject.isComponentLoading">
    <div class="d-flex justify-content-center mt-3">
      <mat-spinner matTooltip="Please wait..." diameter="30"> </mat-spinner>
    </div>
  </ng-container>
  <ng-container *ngIf="!loaderObject.isComponentLoading">
    <!--First Card UI-->
    <div class="row pt-3">
      <mat-card class="p-0 mat-card slide-from-down" style="min-height: 25vh">
         <div class="col-12 px-2 py-2">
          <div class="row">
            <div class="col-2 p-0 justify-content-center img-section">
              <app-user-image class="profile-img"
                [id]="employeePersonalDetails?.oid"
                imgWidth="100px"
                imgHeight="100px"
                [hasDefaultImg]="true"
                [defaultImg]="employeePersonalDetails?.default_profile_logo_url 
                ? employeePersonalDetails?.default_profile_logo_url 
                : 'https://assets.kebs.app/images/kebs_favicon.png'"
                [fromGraphAPI]="graphApiNeeded"
              >
              </app-user-image>

              <!-- <ng-template #noImg>
                <div class="circular">
                  <img
                    [src]="employeePersonalDetails?.default_profile_logo_url ? employeePersonalDetails?.default_profile_logo_url : 'https://assets.kebs.app/images/kebs_favicon.png'"
                    alt="No Img"
                  />
                </div>
              </ng-template> -->
            </div>
            <div class="col-10 p-0">
              <div class="row pt-2">
                <div class="col-3 emp-heading p-0">
                  <div class="row" class="emp-name-heading">
                    <span
                      matTooltip="{{
                        employeePersonalDetails?.employee_full_name
                          ? employeePersonalDetails?.employee_full_name
                          : '-'
                      }}"
                    >
                      {{
                        employeePersonalDetails?.employee_full_name
                          ? employeePersonalDetails?.employee_full_name
                          : "-"
                      }}</span
                    >
                  </div>
                </div>
                <div class="col-3 p-0">
                  <div class="row">
                    <span class="card1-title-heading">DOJ</span>
                  </div>
                  <div class="row" class="item-value">
                    <span
                      matTooltip="{{
                        employeePersonalDetails?.date_of_joining
                          ? (employeePersonalDetails?.date_of_joining
                            | date: 'd MMM yyyy')
                          : '-'
                      }}"
                    >
                      {{
                        employeePersonalDetails?.date_of_joining
                          ? (employeePersonalDetails?.date_of_joining
                            | date: "d MMM yyyy")
                          : "-"
                      }}
                    </span>
                  </div>
                </div>
                <div class="col-3 p-0">
                  <div class="row">
                    <span class="card1-title-heading">Direct Manager</span>
                  </div>
                  <div class="row" class="item-value">
                    <span
                      matTooltip="{{
                        employeePersonalDetails?.direct_manager
                          ? employeePersonalDetails?.direct_manager
                          : '-'
                      }}"
                    >
                      {{
                        employeePersonalDetails?.direct_manager
                          ? employeePersonalDetails?.direct_manager
                          : "-"
                      }}
                    </span>
                  </div>
                </div>
                <div class="col-3 p-0">
                  <div class="row">
                    <span class="card1-title-heading">Work Mail ID</span>
                  </div>
                  <div
                    class="row work-mail"
                  >
                  <!-- style="text-transform: none !important;overflow: visible !important;word-break: break-all;white-space: normal !important;" -->
                   <div class="item-value"
                   style="text-transform: none !important;max-width:78%;">
                  <span
                      matTooltip="{{
                        employeePersonalDetails?.work_email && !($isEmpRetired | async)
                          ? employeePersonalDetails?.work_email
                          : '-'
                      }}"
                    >
                      {{
                        employeePersonalDetails?.work_email && !($isEmpRetired | async)
                          ? employeePersonalDetails?.work_email 
                          : "-"
                      }}
                    </span>
                  </div>
                  <div class="content-copy">
                    <button mat-icon-button (click)="showClipBoardToast()" onclick="this.blur()" [cdkCopyToClipboard]="employeePersonalDetails?.work_email
                    ? employeePersonalDetails?.work_email
                    : '-'" style="height: 22px;width: 30px;"> <mat-icon style="font-size: 18px;
                    line-height: 5px;">content_copy</mat-icon></button>
                  </div>
                  </div>
                </div>
              </div>
              <div class="row pt-2">
                <div class="col-3 p-0">
                  <div class="row" class="item-value">
                    <span
                      matTooltip="{{
                        employeePersonalDetails?.associate_id
                          ? (employeePersonalDetails?.prefix_active?employeePersonalDetails.prefix:'') + employeePersonalDetails?.associate_id
                          : '-'
                      }}"
                    >
                      Emp ID
                      {{
                        employeePersonalDetails?.associate_id
                          ? (employeePersonalDetails?.prefix_active?employeePersonalDetails.prefix:'') + employeePersonalDetails?.associate_id
                          : "-"
                      }}</span
                    >
                  </div>
                  <div class="row" class="card1-title-heading">
                    <span
                      matTooltip="{{
                        employeePersonalDetails?.deparment_name
                          ? employeePersonalDetails?.deparment_name
                          : '-'
                      }}"
                    >
                      {{
                        employeePersonalDetails?.deparment_name
                          ? employeePersonalDetails?.deparment_name
                          : "-"
                      }}</span
                    >
                  </div>
                </div>
                <div class="col-3 p-0" *ngIf="($isEmpRetired | async)">
                  <div class="row">
                    <span class="card1-title-heading">Exit Date</span>
                  </div>
                  <div class="row" class="item-value">
                    <span
                      matTooltip="{{
                        employeePersonalDetails?.start_date
                          ? (employeePersonalDetails?.start_date
                            | date: 'd MMM YYYY')
                          : '-'
                      }}"
                    >
                      {{
                        employeePersonalDetails?.start_date
                          ? (employeePersonalDetails?.start_date
                            | date: "d MMM YYYY")
                          : "-"
                      }}
                    </span>
                  </div>
                </div>
                <div class="col-3 p-0" *ngIf="tenantFields?.noticePeriod?.isActiveField">
                  <div class="row">
                    <span class="title-heading">LWD</span>
                  </div>
                  <div class="row">
                    <span
                      class="item-value"
                      matTooltip="{{
                        (employeePersonalDetails?.employee_leave_date_time
                          ? (employeePersonalDetails?.employee_leave_date_time | date: 'd MMM yyyy')
                          : '-')
                      }}"
                    >
                      {{
                        (employeePersonalDetails?.employee_leave_date_time
                          ? (employeePersonalDetails?.employee_leave_date_time | date: 'd MMM yyyy')
                          : "-")
                      }}
                    </span>
                  </div>
                </div>    
                <div class="col-3 p-0" *ngIf="tenantFields?.bu?.isActiveField">
                  <div class="row">
                    <span class="card1-title-heading">BU</span>
                  </div>
                  <div class="row" class="item-value">
                    <span
                      matTooltip="{{
                        buDetails?.bu_name
                          ? buDetails?.bu_name
                          : '-'
                      }}"
                    >
                      {{
                        buDetails?.bu_name
                        ? buDetails?.bu_name
                          : "-"
                      }}
                    </span>
                  </div>
                </div>
                <div class="col-3 p-0" *ngIf="tenantFields?.phoneNumber?.isActiveField && !entity_restriction">
                  <div class="row" class="card1-title-heading">
                    <span>Contact</span>
                  </div>
                  <div class="row" class="item-value">
                    <span
                      matTooltip="{{
                        employeePersonalDetails?.contact_number
                          ? employeePersonalDetails?.contact_number
                          : '-'
                      }}"
                    >
                      <span
                        class="pr-1"
                        *ngIf="employeePersonalDetails?.country_code_id"
                        >+{{ employeePersonalDetails?.country_code_id }}</span
                      >
                      {{
                        employeePersonalDetails?.contact_number
                          ? employeePersonalDetails?.contact_number
                          : "-"
                      }}
                    </span>
                  </div>
                </div>
                <div class="col-3 p-0" *ngIf="tenantFields?.profilePosition?.isActiveField">
                  <div class="row" class="card1-title-heading">
                    <span>Position</span>
                  </div>
                  <div class="row" class="item-value">
                    <span
                      matTooltip="{{
                        employeePersonalDetails?.position
                          ? employeePersonalDetails?.position
                          : '-'
                      }}"
                    >
                      {{
                        employeePersonalDetails?.position
                          ? employeePersonalDetails?.position
                          : "-"
                      }}
                    </span>
                  </div>
                </div>
              </div>
              <div class="row pt-2">
                <div class="col-3 p-0">
                  <div class="row" class="item-value">
                    <span
                      matTooltip="{{
                        employeePersonalDetails?.employment_type
                          ? employeePersonalDetails?.employment_type
                          : '-'
                      }}"
                    >
                      {{
                        employeePersonalDetails?.employment_type
                          ? employeePersonalDetails?.employment_type
                          : "-"
                      }}
                    </span>
                  </div>
                  <div class="row" class="card1-title-heading">
                    <span
                      matTooltip="{{
                        employeePersonalDetails?.work_location
                          ? employeePersonalDetails?.work_location
                          : '-'
                      }}"
                    >
                      {{
                        employeePersonalDetails?.work_location
                          ? employeePersonalDetails?.work_location
                          : "-"
                      }}
                    </span>
                  </div>
                </div>
                <div class="col-3 p-0" *ngIf="tenantFields?.sbu?.isActiveField" >
                  <div class="row">
                    <span class="card1-title-heading">SBU</span>
                  </div>
                  <div class="row" class="item-value">
                    <span
                      matTooltip="{{
                        buDetails?.sbu_name
                          ? buDetails?.sbu_name
                          : '-'
                      }}"
                    >
                      {{
                        buDetails?.sbu_name
                        ? buDetails?.sbu_name
                          : "-"
                      }}
                    </span>
                  </div>
                </div>
                <div class="col-3 p-0"  *ngIf="tenantFields?.experience?.isActiveField" >
                  <div class="row" class="card1-title-heading">
                    <span
                      matTooltip="Years In {{ tenantName ? tenantName : '' }}"
                      >Years In {{ tenantName ? tenantName : "" }}</span
                    >
                  </div>
                  <div class="row" class="item-value">
                    <span matTooltip="{{ yearsOfExperience ? yearsOfExperience : '-'}}">{{
                      yearsOfExperience ? yearsOfExperience : '-'
                    }}</span>
                  </div>
                </div>
                <div class="col-3 p-0"  *ngIf="tenantFields?.tenantExperience?.isActiveField" >
                  <div class="row" class="card1-title-heading">
                    <span
                      matTooltip="Relevant Experience"
                      >Relevant Experience</span
                    >
                  </div>
                  <div class="row" class="item-value">
                    <span matTooltip="{{ yearsOfExternal ? yearsOfExternal  : '-'}}">{{
                      yearsOfExternal ? yearsOfExternal  : '-'
                    }}</span>
                  </div>
                </div>
                <div class="col-3 p-0"  *ngIf="tenantFields?.totalExperience?.isActiveField" >
                  <div class="row" class="card1-title-heading">
                    <span
                      matTooltip="Total Experience"
                      >Total Experience</span
                    >
                  </div>
                  <div class="row" class="item-value">
                    <span matTooltip="{{ totalYears ? totalYears : '-'}}">{{
                      totalYears ? totalYears : '-'
                    }}</span>
                  </div>
                </div>
                <div class="col-3 p-0">
                  <div class="row" class="card1-title-heading">
                    <span>Status</span>
                  </div>
                  <div class="row">
                    <button
                      mat-flat-button
                      class="status"
                      style="pointer-events: none;"
                      [ngStyle]="{
                        'background-color':
                          employeePersonalDetails?.employee_status_color
                            ? employeePersonalDetails?.employee_status_color
                            : '#000'
                      }"
                    >
                      <span
                        class="status-text"
                        matTooltip="{{
                          employeePersonalDetails?.employee_status
                            ? employeePersonalDetails?.employee_status
                            : '-'
                        }}"
                      >
                        {{
                          employeePersonalDetails?.employee_status
                            ? employeePersonalDetails?.employee_status
                            : "-"
                        }}
                      </span>
                    </button>
                  </div>
                </div>
              </div>
              <div class="row pt-2 pb-1" >
                <div class="row" *ngIf="tenantFields?.profileValidate?.isActiveField">
                  <button
                    mat-flat-button
                    class="status"
                    style="background-color:#ee4961"
                    (click)="openValidationDialog()" 
                    *ngIf="!isProfileValidated"
                    [ngStyle]="{'pointer-events' : isFromMyProfile ? 'visible' : 'none' }"
                  >
                    <span
                      class="status-text"
                    >
                     Validate Profile
                    </span>
                    <span class="pl-0">
                      <mat-icon style="font-size: 14px;
                      line-height: 24px;">error</mat-icon>
                    </span>
                  </button>
                  <button *ngIf="isProfileValidated"
                  mat-flat-button
                  class="status"
                  style="background-color:#459433;pointer-events: none;"
                >
                  <span
                    class="status-text"
                  >
                   Profile Validated
                  </span>
                  <span class="pl-0">
                    <mat-icon style="font-size: 14px;
                    line-height: 24px;">done</mat-icon>
                  </span>
                </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </mat-card>
    </div>
    <!--Second Card UI-->

    <div class="row pt-3" *ngIf="approvalPendings.length>0 && isFromMyProfile">
      <mat-card class="pl-0 pt-0 mat-card slide-from-down" style="height: 6vh;
      background: #FFFBE7;">
      <div class="col-12 px-3 pt-2">
        <div class="row">
          <div class="d-flex justify-content-center align-items-center">
            <span><mat-icon class="approval-pending-icon">info_outline</mat-icon>
            </span>
              <span class="approval-pending-text">{{approvalPendings[0]?.submission_item?.approval_fields.length}} Data Update is under Validation .</span>
          <span class="approval-pending-text pl-1"  (click)="openApprovalPendingDialog()" style="text-decoration:underline;font-weight:500;cursor:pointer;">View</span>
          </div>
        </div>
        </div>

      </mat-card>
    </div>
    <div class="row pt-0 pb-2" [ngClass]="approvalPendings.length > 0 ? 'pt-0' : 'pt-3'">
      <mat-card class="pl-0 mat-card slide-from-down" style="min-height: 75vh">
        <div class="col-12 px-2 py-2">
          <!--Employment Details-->
          <div class="row">
            <div class="col-10">
              <span class="section-heading">Employee Details</span>
            </div>
            <div class="col-2">
              <button
                class="edit-button"
                mat-button
                #menuTrigger="matMenuTrigger"
                [matMenuTriggerFor]="editMenu"
              >
                <mat-icon class="edit-icon"> edit </mat-icon>
                <span class="pl-0 edit-text"> Edit </span>
                <mat-icon class="pl-0 drop-down-icon">
                  keyboard_arrow_down
                </mat-icon>
              </button>
              <mat-menu #editMenu="matMenu">
                <button  disabled="{{!isEditAccess || !isWorkflowNotActive ||  ($isEmpRetired | async) }}" mat-menu-item (click)="editPersoanlDetails()">
                  Edit
                </button>
                <button mat-menu-item (click)="openEditHistory()">
                  Edit History
                </button>
              </mat-menu>
            </div>
          </div>
          <div class="row pt-2">
            <div class="col-4">
              <div class="row">
                <span class="title-heading">Employee ID</span>
              </div>
              <div class="row">
                <span
                  class="item-value"
                  matTooltip="{{
                         employeePersonalDetails?.associate_id
                      ? (employeePersonalDetails?.prefix_active?employeePersonalDetails.prefix:'') + employeePersonalDetails?.associate_id
                      : '-'
                  }}"
                >
                  {{
                     employeePersonalDetails?.associate_id
                      ? (employeePersonalDetails?.prefix_active?employeePersonalDetails.prefix:'') + employeePersonalDetails?.associate_id
                      : "-"
                  }}
                </span>
              </div>
            </div>
            <div class="col-4">
              <div class="row">
                <span class="title-heading">Date Of Joining</span>
              </div>
              <div class="row">
                <span
                  class="item-value"
                  matTooltip="{{
                    (employeePersonalDetails?.date_of_joining
                      ? (employeePersonalDetails?.date_of_joining | date: 'd MMM yyyy')
                      : '-')
                  }}"
                >
                  {{
                    (employeePersonalDetails?.date_of_joining
                      ? (employeePersonalDetails?.date_of_joining | date: 'd MMM yyyy')
                      : "-")
                  }}
                </span>
              </div>
            </div>
            <div class="col-4" *ngIf="tenantFields?.employeeClass?.isActiveField">
              <div class="row">
                <span class="title-heading">Employee Class</span>
              </div>
              <div class="row">
                <span
                class="item-value"
                matTooltip="{{
                  (employeePersonalDetails?.employee_class
                    ? (employeePersonalDetails?.employee_class)
                    : '-')
                }}"
              >
                {{
                  (employeePersonalDetails?.employee_class
                    ? (employeePersonalDetails?.employee_class)
                    : "-")
                }}
                </span>
              </div>
            </div>
            
            <div class="col-4" *ngIf="tenantFields?.internalDate?.isActiveField && isEditAccess">
              <div class="row">
                <span class="title-heading">Rehire/Transfer Date</span>
              </div>
              <div class="row">
                <span
                  class="item-value"
                  matTooltip="{{
                    (employeePersonalDetails?.internal_doj
                      ? (employeePersonalDetails?.internal_doj)
                      : '-')
                  }}"
                >
                  {{
                    (employeePersonalDetails?.internal_doj
                      ? (employeePersonalDetails?.internal_doj)
                      : "-")
                  }}
                </span>
              </div>
            </div>
          </div>
          <div class="row pt-3" >
            <div class="col-4" *ngIf="tenantFields?.PreviousAssociateID?.isActiveField">
              <div class="row">
                <span class="title-heading">Previous Associate Id</span>
              </div>
              <div class="row">
                <span
                  class="item-value"
                  matTooltip="{{
                    (employeePersonalDetails?.previous_associate_id
                      ? (employeePersonalDetails?.previous_associate_id)
                      : '-')
                  }}"
                >
                  {{
                    (employeePersonalDetails?.previous_associate_id
                      ? (employeePersonalDetails?.previous_associate_id)
                      : "-")
                  }}
                </span>
              </div>
            </div>
            <div class="col-4">
              <div class="title-heading" *ngIf="tenantFieldsLable?.external_id?.isActiveField; else defaultExternalIdlable" >
                {{tenantFieldsLable.external_id.fieldLable}} 
              </div>
               <ng-template #defaultExternalIdlable>
                  <div class="title-heading">
                 External ID
                  </div>
               </ng-template>
              <div class="row">
                <span
                  class="item-value"
                  matTooltip="{{
                    (employeePersonalDetails?.external_id
                      ? (employeePersonalDetails?.external_id)
                      : '-')
                  }}"
                >
                  {{
                    (employeePersonalDetails?.external_id
                      ? (employeePersonalDetails?.external_id)
                      : "-")
                  }}
                </span>
              </div>
            </div>
          </div>
          <div class="row pt-3" >
            <div class="col-4"*ngIf="tenantFields?.sourceOfHire?.isActiveField">
              <div class="row">
                <span class="title-heading">Source Of Hire</span>
              </div>
              <div class="row">
                <div
                  class="col-12 p-0 item-value"
                  *ngFor="
                    let source_of_hire of employeePersonalDetails?.source_of_hire
                  "
                >
                  <span matTooltip="{{ source_of_hire.source_of_hire_name }}">{{
                    source_of_hire.source_of_hire_name
                  }}</span>
                </div>
                <!-- <span
                  class="item-value"
                  matTooltip="{{
                    employeePersonalDetails?.source_of_hire
                      ? employeePersonalDetails?.source_of_hire
                      : '-'
                  }}"
                >
                  {{
                    employeePersonalDetails?.source_of_hire
                      ? employeePersonalDetails?.source_of_hire
                      : "-"
                  }}
                </span> -->
              </div>
            </div>
            <div class="col-4">
              <div class="row">
                <span class="title-heading">Employment Type</span>
              </div>
              <div class="row">
                <span
                  class="item-value"
                  matTooltip="{{
                    employeePersonalDetails?.employment_type
                      ? employeePersonalDetails?.employment_type
                      : '-'
                  }}"
                >
                  {{
                    employeePersonalDetails?.employment_type
                      ? employeePersonalDetails?.employment_type
                      : "-"
                  }}
                </span>
              </div>
            </div>
            <div class="col-4">
              <div class="row">
                <span class="title-heading">Employment Status</span>
              </div>
              <div class="row">
                <button
                  mat-flat-button
                  class="status"
                  style="pointer-events: none;"
                  [ngStyle]="{
                    'background-color':
                      employeePersonalDetails?.employee_status_color
                        ? employeePersonalDetails?.employee_status_color
                        : '#000'
                  }"
                >
                  <span
                    class="status-text"
                    matTooltip="{{
                      employeePersonalDetails?.employee_status
                        ? employeePersonalDetails?.employee_status
                        : '-'
                    }}"
                  >
                    {{
                      employeePersonalDetails?.employee_status
                        ? employeePersonalDetails?.employee_status
                        : "-"
                    }}
                  </span>
                </button>
              </div>
            </div>
            <div class="col-4" *ngIf="tenantFields?.workerType?.isActiveField">
              <div class="row">
                <span class="title-heading">Worker Type</span>
              </div>
              <div class="row">
                <span
                class="item-value"
                matTooltip="{{
                  (employeePersonalDetails?.employment_type ? employeePersonalDetails?.employment_type : '-') + ' ' +
                  (employeePersonalDetails?.time_type ? employeePersonalDetails?.time_type : '-')
                }}"
              >
                {{
                  (employeePersonalDetails?.employment_type ? employeePersonalDetails?.employment_type : '-') + ' ' +
                  (employeePersonalDetails?.time_type ? employeePersonalDetails?.time_type : '-')
                }}
              </span>
              
              </div>
            </div>
          </div>
          <div class="row pt-3">
          <div class="col-4" *ngIf="tenantFields?.PreviousAssociateID?.isActiveField && isEditAccess">
            <div class="row">
              <span class="title-heading">Previous Associate Id</span>
            </div>
            <div class="row">
              <span
                class="item-value"
                matTooltip="{{
                  (employeePersonalDetails?.previous_associate_id
                    ? (employeePersonalDetails?.previous_associate_id)
                    : '-')
                }}"
              >
                {{
                  (employeePersonalDetails?.previous_associate_id
                    ? (employeePersonalDetails?.previous_associate_id)
                    : "-")
                }}
              </span>
            </div>
          </div>
        </div>
          <div class="row pt-3" *ngIf="tenantFields?.duration?.isActiveField">
            <div class="col-4"
            *ngIf="
            employeePersonalDetails?.is_probation
          ">
              <div class="row">
                <span class="title-heading">Probation Ends On</span>
              </div>
              <div class="row">
                <span
                  class="item-value"
                  matTooltip="{{
                    employeePersonalDetails?.probation_ends_on
                      ? (employeePersonalDetails?.probation_ends_on | date: 'd MMM YYYY')
                      : '-'
                  }}"
                >
                  {{
                    employeePersonalDetails?.probation_ends_on
                      ? (employeePersonalDetails?.probation_ends_on | date: 'd MMM YYYY')
                      : "-"
                  }}
                </span>
              </div>
            </div>
            <div
              class="col-4"
              *ngIf="
              employeePersonalDetails?.is_contract
            "
            >
              <div class="row">
                <span class="title-heading">Contract Ends On</span>
              </div>
              <div class="row">
                <span
                  class="item-value"
                  matTooltip="{{
                    employeePersonalDetails?.contract_ends_on
                      ? (employeePersonalDetails?.contract_ends_on | date: 'd MMM YYYY')
                      : '-'
                  }}"
                >
                  {{
                    employeePersonalDetails?.contract_ends_on
                      ? (employeePersonalDetails?.contract_ends_on | date: 'd MMM YYYY')
                      : "-"
                  }}
                </span>
              </div>
            </div>
            
            <div
              class="col-4"
              *ngIf="tenantFields?.contractType?.isActiveField && employeePersonalDetails?.contract_type"
            >
              <div class="row">
                <span class="title-heading">Contract Type</span>
              </div>
              <div class="row">
                <span
                  class="item-value"
                  matTooltip="{{
                    employeePersonalDetails?.contract_type
                      ? employeePersonalDetails.contract_type
                      : '-'
                  }}"
                >
                  {{
                    employeePersonalDetails?.contract_type
                    ? employeePersonalDetails.contract_type
                    : '-'
                  }}
                </span>
              </div>
            </div>
          </div>
          
          <!--Personal Details-->
          <div class="row pt-4">
            <div class="col-10">
              <span class="section-heading">Personal Details</span>
            </div>
          </div>
          <div class="row pt-3">
            <!-- <div class="col-1 pr-0">
              <div class="row">
                <span class="title-heading">Salutation</span>
              </div>
              <div class="row">
                <span
                  class="item-value"
                  matTooltip="{{
                    employeePersonalDetails?.salutation
                      ? employeePersonalDetails?.salutation
                      : '-'
                  }}"
                >
                  {{
                    employeePersonalDetails?.salutation
                      ? employeePersonalDetails?.salutation
                      : "-"
                  }}
                </span>
              </div>
            </div> -->
            <div class="col-4">
              <div class="row">
                <span class="title-heading">First Name</span>
              </div>
              <div class="row">
                <span
                  class="item-value"
                  matTooltip="{{
                    employeePersonalDetails?.first_name
                      ? employeePersonalDetails?.first_name
                      : '-'
                  }}"
                >
                  {{
                    employeePersonalDetails?.first_name
                      ? employeePersonalDetails?.first_name
                      : "-"
                  }}
                </span>
              </div>
            </div>
            <div class="col-4">
              <div class="row">
                <span class="title-heading">Middle Name</span>
              </div>
              <div class="row">
                <span
                  class="item-value"
                  matTooltip="{{
                    employeePersonalDetails?.middle_name
                      ? employeePersonalDetails?.middle_name
                      : '-'
                  }}"
                >
                  {{
                    employeePersonalDetails?.middle_name
                      ? employeePersonalDetails?.middle_name
                      : "-"
                  }}
                </span>
              </div>
            </div>
            <div class="col-4">
              <div class="row">
                <span class="title-heading">Last Name</span>
              </div>
              <div class="row">
                <span
                  class="item-value"
                  matTooltip="{{
                    employeePersonalDetails?.last_name
                      ? employeePersonalDetails?.last_name
                      : '-'
                  }}"
                >
                  {{
                    employeePersonalDetails?.last_name
                      ? employeePersonalDetails?.last_name
                      : "-"
                  }}
                </span>
              </div>
            </div>
          </div>
          <div class="row pt-3" *ngIf="tenantFields?.displayName?.isActiveField || tenantFields?.dateOfBirth?.isActiveField || tenantFields?.age?.isActiveField ">
            <div class="col-4" *ngIf="tenantFields?.displayName?.isActiveField">
              <div class="row">
                <span class="title-heading">Display Name</span>
              </div>
              <div class="row">
                <span
                  class="item-value"
                  matTooltip="{{
                    employeePersonalDetails?.display_name
                      ? employeePersonalDetails?.display_name
                      : '-'
                  }}"
                >
                  {{
                    employeePersonalDetails?.display_name
                      ? employeePersonalDetails?.display_name
                      : "-"
                  }}
                </span>
              </div>
            </div>
            <div class="col-4" *ngIf="tenantFields?.dateOfBirth?.isActiveField && !entity_restriction">
              <div class="row" >
                <span class="title-heading">DOB</span>
              </div>
              <div class="row">
                <span
                  class="item-value"
                  matTooltip="{{
                    employeePersonalDetails?.date_of_birth
                      ? (employeePersonalDetails?.date_of_birth
                        | date: 'd MMM YYYY')
                      : '-'
                  }}"
                >
                  {{
                    employeePersonalDetails?.date_of_birth
                      ? (employeePersonalDetails?.date_of_birth
                        | date: "d MMM YYYY")
                      : "-"
                  }}
                </span>
              </div>
            </div>
            <div class="col-4" *ngIf="tenantFields?.age?.isActiveField && !entity_restriction">
              <div class="row">
                <span class="title-heading">Age</span>
              </div>
              <div class="row">
                <span
                  class="item-value"
                  matTooltip="{{
                    employeePersonalDetails?.age
                      ? employeePersonalDetails?.age
                      : '-'
                  }}"
                >
                  {{
                    employeePersonalDetails?.age
                      ? employeePersonalDetails?.age
                      : "-"
                  }}
                </span>
              </div>
            </div>
          </div>
          <div class="row pt-3"  >
            <div class="col-4" *ngIf="tenantFields?.bloodGroup?.isActiveField && !entity_restriction">
              <div class="row">
                <span class="title-heading">Blood Group</span>
              </div>
              <div class="row">
                <span
                  class="item-value"
                  matTooltip="{{
                    employeePersonalDetails?.blood_group
                      ? employeePersonalDetails?.blood_group
                      : '-'
                  }}"
                >
                  {{
                    employeePersonalDetails?.blood_group
                      ? employeePersonalDetails?.blood_group
                      : "-"
                  }}
                </span>
              </div>
            </div>
            <div class="col-4" *ngIf="tenantFields?.gender?.isActiveField && !entity_restriction">
              <div class="row">
                <span class="title-heading">Gender</span>
              </div>
              <div class="row" >
                <span
                  class="item-value"
                  matTooltip="{{
                    employeePersonalDetails?.gender_name
                      ? employeePersonalDetails?.gender_name
                      : '-'
                  }}"
                >
                  {{
                    employeePersonalDetails?.gender_name
                      ? employeePersonalDetails?.gender_name
                      : "-"
                  }}
                </span>
              </div>
            </div>
            <div class="col-4" *ngIf="tenantFields?.birthMark?.isActiveField && !entity_restriction">
              <div class="row">
                <span class="title-heading">Birth Mark</span>
              </div>
              <div class="row">
                <span
                  class="item-value"
                  matTooltip="{{
                    employeePersonalDetails?.birth_mark
                      ? employeePersonalDetails?.birth_mark
                      : '-'
                  }}"
                >
                  {{
                    employeePersonalDetails?.birth_mark
                      ? employeePersonalDetails?.birth_mark
                      : "-"
                  }}
                </span>
              </div>
            </div>
          </div>
          <div class="row pt-3">
            <div class="col-4" *ngIf="tenantFields?.nationality?.isActiveField && !entity_restriction">
              <div class="row">
                <span class="title-heading">Nationality</span>
              </div>
              <div class="row">
                <span
                  class="item-value"
                  matTooltip="{{
                    employeePersonalDetails?.nationality
                      ? employeePersonalDetails?.nationality
                      : '-'
                  }}"
                >
                  {{
                    employeePersonalDetails?.nationality
                      ? employeePersonalDetails?.nationality
                      : "-"
                  }}
                </span>
              </div>
            </div>
            <div class="col-4" *ngIf="tenantFields?.nationalIdType?.isActiveField && !entity_restriction">
              <div class="row">
                <span class="title-heading">National ID Type</span>
              </div>
              <div class="row">
                <span
                  class="item-value"
                  matTooltip="{{
                    employeePersonalDetails?.national_id_type
                      ? employeePersonalDetails?.national_id_type
                      : '-'
                  }}"
                >
                  {{
                    employeePersonalDetails?.national_id_type
                      ? employeePersonalDetails?.national_id_type
                      : "-"
                  }}
                </span>
              </div>
            </div>
            <div class="col-4" *ngIf="tenantFields?.nationalId?.isActiveField && !entity_restriction">
              <div class="row">
                <span class="title-heading">National ID</span>
              </div>
              <div class="row">
                <span
                  class="item-value"
                  matTooltip="{{
                    employeePersonalDetails?.national_id
                      ? employeePersonalDetails?.national_id
                      : '-'
                  }}"
                >
                  {{
                    employeePersonalDetails?.national_id
                      ? employeePersonalDetails?.national_id
                      : "-"
                  }}
                </span>
              </div>
            </div>
          </div>
          <div class="row pt-3" >
            <div class="col-4" *ngIf="tenantFields?.maritalStatus?.isActiveField && !entity_restriction">
              <div class="row">
                <span class="title-heading">Marital Status</span>
              </div>
              <div class="row">
                <span
                  class="item-value"
                  matTooltip="{{
                    employeePersonalDetails?.marital_status
                      ? employeePersonalDetails?.marital_status
                      : '-'
                  }}"
                >
                  {{
                    employeePersonalDetails?.marital_status
                      ? employeePersonalDetails?.marital_status
                      : "-"
                  }}
                </span>
              </div>
            </div>
            <div
              class="col-4"
              *ngIf="employeePersonalDetails?.marital_status == 'Married'"
            >
              <div class="row">
                <span class="title-heading">Marriage Date</span>
              </div>
              <div class="row">
                <span
                  class="item-value"
                  matTooltip="{{
                    employeePersonalDetails?.married_date
                      ? (employeePersonalDetails?.married_date
                        | date: 'd MMM YYYY')
                      : '-'
                  }}"
                >
                  {{
                    employeePersonalDetails?.married_date
                      ? (employeePersonalDetails?.married_date
                        | date: "d MMM YYYY")
                      : "-"
                  }}
                </span>
              </div>
            </div>
            <div class="col-4"  *ngIf="tenantFields?.subscribeToCelebration?.isActiveField && !entity_restriction">
              <div class="row">
                <span class="title-heading">Subscribe To Celebrations</span>
              </div>
              <div class="row">
                <span
                  class="item-value"
                  matTooltip="{{
                    employeePersonalDetails?.subscribe_to_celeberations
                      ? employeePersonalDetails?.subscribe_to_celeberations
                      : '-'
                  }}"
                >
                  {{
                    employeePersonalDetails?.subscribe_to_celeberations
                      ? employeePersonalDetails?.subscribe_to_celeberations
                      : "-"
                  }}
                </span>
              </div>
            </div>
          </div>
          <div class="row pt-3">
            <div class="col-4" *ngIf="tenantFields?.personalEmailId?.isActiveField && !entity_restriction">
              <div class="row">
                <span class="title-heading">Personal Email ID</span>
              </div>
              <div class="row">
                <span
                  class="item-value"
                  style="text-transform: none !important"
                  matTooltip="{{
                    employeePersonalDetails?.personal_email
                      ? employeePersonalDetails?.personal_email
                      : '-'
                  }}"
                >
                  {{
                    employeePersonalDetails?.personal_email
                      ? employeePersonalDetails?.personal_email
                      : "-"
                  }}
                </span>
              </div>
            </div>
            <div class="col-4" *ngIf="tenantFields?.phoneNumber?.isActiveField && !entity_restriction">
              <div class="row">
                <span class="title-heading">Phone Number</span>
              </div>
              <div class="row">
                <span
                  class="item-value"
                  matTooltip="{{
                    employeePersonalDetails?.contact_number
                      ? employeePersonalDetails?.contact_number
                      : '-'
                  }}"
                >
                  <span
                    class="pr-1"
                    *ngIf="employeePersonalDetails?.country_code_id"
                    >+{{ employeePersonalDetails?.country_code_id }}</span
                  >
                  {{
                    employeePersonalDetails?.contact_number
                      ? employeePersonalDetails?.contact_number
                      : "-"
                  }}
                </span>
              </div>
            </div>
            <div class="col-4" *ngIf="tenantFields?.linkedinId?.isActiveField && !entity_restriction">
              <div class="row">
                <span class="title-heading">LinkedIn Id</span>
              </div>
              <div class="row">
                <span
                  class="item-value"
                  matTooltip="{{
                    employeePersonalDetails?.linkedin_id
                      ? employeePersonalDetails?.linkedin_id
                      : '-'
                  }}"
                >
                  {{
                    employeePersonalDetails?.linkedin_id
                      ? employeePersonalDetails?.linkedin_id
                      : "-"
                  }}
                </span>
              </div>
            </div>
            <!-- <div class="col-4">
              <div class="row">
                <span class="title-heading">Referred By</span>
              </div>
              <div class="row">
                <span
                  class="item-value"
                  matTooltip="{{
                    employeePersonalDetails?.referred_by?.referee_name
                      ? employeePersonalDetails?.referred_by?.referee_name
                      : '-'
                  }}"
                >
                  {{
                    employeePersonalDetails?.referred_by?.referee_name
                      ? employeePersonalDetails?.referred_by?.referee_name
                      : "-"
                  }}
                </span>
              </div>
            </div> -->
          </div>
          <div class="row pt-3" *ngIf="(tenantFields?.veteranStatus?.isActiveField || tenantFields?.disablity?.isActiveField) && !showSensitiveFields && !entity_restriction">
            <div class="col-4" *ngIf="tenantFields?.veteranStatus?.isActiveField">
              <div class="row">
                <span class="title-heading">Veteran Status</span>
              </div>
              <div class="row">
                <span
                  class="item-value"
                  matTooltip="{{
                    employeePersonalDetails?.veteran_status
                      ? 'Yes'
                      : 'No'
                  }}"
                >
                  {{
                    employeePersonalDetails?.veteran_status
                      ? 'Yes'
                      : "No"
                  }}
                </span>
              </div>
            </div>
            <div class="col-4" *ngIf="tenantFields?.disablity?.isActiveField && !entity_restriction">
              <div class="row">
                <span class="title-heading">Disability</span>
              </div>
              <div class="row">
                <span
                  class="item-value"
                  matTooltip="{{
                    employeePersonalDetails?.disablity
                      ? 'Yes'
                      : 'No'
                  }}"
                >
                  {{
                    employeePersonalDetails?.disablity
                      ? 'Yes'
                      : 'No'
                  }}
                </span>
              </div>
            </div>
          </div>

          <!--Office Credentials-->
          <div class="row pt-4">
            <div class="col-10">
              <span class="section-heading">Office Credentials</span>
            </div>
          </div>
          <div class="row pt-3">
            <div class="col-4">
              <div class="row">
                <span class="title-heading">Work Mail ID</span>
              </div>
              <div class="row">
                <span
                  class="item-value"
                  style="text-transform: none !important"
                  matTooltip="{{
                    employeePersonalDetails?.work_email && !($isEmpRetired | async)
                      ? employeePersonalDetails?.work_email 
                      : '-'
                  }}"
                >
                  {{
                    employeePersonalDetails?.work_email && !($isEmpRetired | async)
                      ? employeePersonalDetails?.work_email
                      : "-"
                  }}
                </span>
              </div>
            </div>
          </div>

          <!--Address-->
          <div class="row pt-4">
            <div class="col-10" *ngIf="(tenantFields?.permAddress?.isActiveField || tenantFields?.commAddress?.isActiveField || tenantFields?.permExternalAddress?.isActiveField) && !entity_restriction">
              <span class="section-heading">Address</span>
            </div>
          </div>
          <div class="row pt-3" *ngIf="(tenantFields?.permAddress?.isActiveField || tenantFields?.commAddress?.isActiveField || tenantFields?.permExternalAddress?.isActiveField) && !entity_restriction">
            <div class="col-4">
              <div class="row">
                <span class="title-heading">Permanent</span>
              </div>
              <div class="row" *ngIf="tenantFields?.permAddress?.isActiveField">
                <span
                  class="item-value"
                  style="white-space: normal !important;overflow: visible;word-break: break-all;"
                  matTooltip="{{
                    employeePersonalDetails?.permanent_address
                      ? employeePersonalDetails?.permanent_address
                      : '-'
                  }}"
                >
                  {{
                    employeePersonalDetails?.permanent_address
                      ? employeePersonalDetails?.permanent_address
                      : "-"
                  }}
                </span>
              </div>
              <div class="row" *ngIf="tenantFields?.permExternalAddress?.isActiveField">
                <span
                  class="item-value"
                  style="white-space: normal !important;overflow: visible;word-break: break-all;"
                  matTooltip="{{
                    employeePersonalDetails?.permanent_external_address
                      ? employeePersonalDetails?.permanent_external_address
                      : '-'
                  }}"
                >
                  {{
                    employeePersonalDetails?.permanent_external_address
                      ? employeePersonalDetails?.permanent_external_address
                      : "-"
                  }}
                </span>
              </div>
            </div>
            <div class="col-4">
              <div class="row">
                <span class="title-heading">Communication</span>
              </div>
              <div class="row" *ngIf="employeePersonalDetails?.is_perm_equals_comm && !tenantFields?.permExternalAddress?.isActiveField">
                <span
                  class="item-value"
                  style="white-space: normal !important;overflow: visible;word-break: break-all;"
                  matTooltip="{{
                    employeePersonalDetails?.permanent_address
                      ? employeePersonalDetails?.permanent_address
                      : '-'
                  }}"
                >
                  {{
                    employeePersonalDetails?.permanent_address
                      ? employeePersonalDetails?.permanent_address
                      : "-"
                  }}
                </span>
              </div>
              <div class="row" *ngIf="!employeePersonalDetails?.is_perm_equals_comm  && !tenantFields?.permExternalAddress?.isActiveField">
                <span
                  class="item-value"
                  style="white-space: normal !important;overflow: visible;word-break: break-all;"
                  matTooltip="{{
                    employeePersonalDetails?.communication_address
                      ? employeePersonalDetails?.communication_address
                      : '-'
                  }}"
                >
                  {{
                    employeePersonalDetails?.communication_address
                      ? employeePersonalDetails?.communication_address
                      : "-"
                  }}
                </span>
              </div>

              <div class="row" *ngIf="employeePersonalDetails?.is_perm_equals_comm && tenantFields?.permExternalAddress?.isActiveField">
                <span
                  class="item-value"
                  style="white-space: normal !important;overflow: visible;word-break: break-all;"
                  matTooltip="{{
                    employeePersonalDetails?.permanent_external_address
                      ? employeePersonalDetails?.permanent_external_address
                      : '-'
                  }}"
                >
                  {{
                    employeePersonalDetails?.permanent_external_address
                      ? employeePersonalDetails?.permanent_external_address
                      : "-"
                  }}
                </span>
              </div>
              <div class="row" *ngIf="!employeePersonalDetails?.is_perm_equals_comm  && tenantFields?.permExternalAddress?.isActiveField">
                <span
                  class="item-value"
                  style="white-space: normal !important;overflow: visible;word-break: break-all;"
                  matTooltip="{{
                    employeePersonalDetails?.communication_external_address
                      ? employeePersonalDetails?.communication_external_address
                      : '-'
                  }}"
                >
                  {{
                    employeePersonalDetails?.communication_external_address
                      ? employeePersonalDetails?.communication_external_address
                      : "-"
                  }}
                </span>
              </div>
            </div>
          </div>

          <!--Emergency Contact-->
          <div
            class="row pt-4"
            *ngIf="
              employeePersonalDetails?.emergency_contact_arr &&
              employeePersonalDetails?.emergency_contact_arr.length > 0 &&
              tenantFields?.emergencyContactDetails?.name || tenantFields?.emergencyContactDetails?.contactNumber || tenantFields?.emergencyContactDetails?.relationship && !entity_restriction
            "
          >
            <div class="col-10" *ngIf="tenantFields?.name?.isActiveField || tenantFields?.relationship?.isActiveField && !entity_restriction">
              <span class="section-heading">Emergency Contact</span>
            </div>
          </div>
          <div class="row pt-3" *ngIf="(tenantFields?.name?.isActiveField || tenantFields?.relationship?.isActiveField || tenantFields?.relationship?.isActiveField) && !entity_restriction" >
            <div
              class="col-4"
              *ngFor="
                let emgContact of employeePersonalDetails?.emergency_contact_arr
              "
            >
              <div class="row">
                <span
                  class="title-heading"
                  matTooltip="{{
                    emgContact?.relationship ? emgContact?.relationship : '-'
                  }}"
                  >{{ emgContact?.relationship }}</span
                >
              </div>
              <div class="row" *ngIf="!entity_restriction">
                <span
                  class="item-value"
                  matTooltip="{{ emgContact?.name ? emgContact?.name : '-' }}"
                  >{{ emgContact?.name }}</span
                >
              </div>
              <div class="row">
                <span
                  class="item-value"
                  matTooltip="{{
                    emgContact?.contact_number
                      ? emgContact?.contact_number
                      : '-'
                  }}"
                >
                  <span class="pr-1" *ngIf="emgContact?.country_code_id && !entity_restriction"
                    >+{{ emgContact?.country_code_id }}</span
                  >
                  {{
                    emgContact?.contact_number
                      ? emgContact?.contact_number
                      : "-"
                  }}
                </span>
              </div>
            </div>
          </div>

          <!--Personal Interest-->
          <div class="row pt-4" *ngIf="tenantFields?.hobbies?.isActiveField || tenantFields?.roleModel?.isActiveField || tenantFields?.bucketList?.isActiveField">
            <div class="col-10">
              <span class="section-heading">Personal Interest</span>
            </div>
          </div>

          <div class="row pt-3" *ngIf="tenantFields?.hobbies?.isActiveField || tenantFields?.roleModel?.isActiveField || tenantFields?.bucketList?.isActiveField">
            <div class="col-4">
              <div class="row">
                <span class="title-heading">Hobbies</span>
              </div>
              <div class="row">
                <div
                  class="col-12 p-0 item-value"
                  *ngFor="let _hobbies of hobbies"
                >
                  <span matTooltip="{{ _hobbies }}">{{ _hobbies }}</span>
                </div>
              </div>
            </div>
            <div class="col-4">
              <div class="row">
                <span class="title-heading">Role Model</span>
              </div>
              <div class="row">
                <div
                  class="col-12 p-0 item-value"
                  *ngFor="let _roleModel of roleModel"
                >
                  <span matTooltip="{{ _roleModel }}">{{ _roleModel }}</span>
                </div>
              </div>
            </div>
            <div class="col-4">
                <div class="row">
                    <div class="title-heading" *ngIf="tenantFieldsLable?.bucketList?.isActiveField; else defaultbucketListlable" >
                      {{tenantFieldsLable.bucketList.fieldLable}} 
                    </div>
                     <ng-template #defaultbucketListlable>
                        <div class="title-heading">
                       Bucket List
                        </div>
                     </ng-template>
                </div>
              <div
                class="row"
                class="item-value"
                style="white-space: normal !important; word-break: break-all"
              >
                <span
                  matTooltip="{{
                    employeePersonalDetails?.bucket_list
                      ? employeePersonalDetails?.bucket_list
                      : '-'
                  }}"
                >
                  {{
                    employeePersonalDetails?.bucket_list
                      ? employeePersonalDetails?.bucket_list
                      : "-"
                  }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </mat-card>
    </div>
  </ng-container>
</div>