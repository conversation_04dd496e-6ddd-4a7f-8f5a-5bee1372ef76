import {
  Component,
  Input,
  NgModule,
  OnInit,
  AfterViewInit,
  ViewChild,
} from '@angular/core';
import * as _ from 'underscore';
import * as moment from 'moment';
import { SubSink } from 'subsink';
import { LoginService } from 'src/app/services/login/login.service';
@Component({
  selector: 'app-skill-details',
  templateUrl: './skill-details.component.html',
  styleUrls: ['./skill-details.component.scss'],
})
export class SkillDetailsComponent implements OnInit {
  @Input() associateId: any;

  subs = new SubSink();

  isNoDataFound = false;

  @ViewChild('menuTrigger') trigger;

  employeeSkillDetails: any = [];
  employeeLanguagesKnownDetails: any = [];
  employeeExtraCurricularDetails: any = [];
  employeeLegalAndAchievementsDetails: any = [];
  $isEmpRetired: Observable<boolean>;

  showAddButton = false;
  showEditButton = false;

  tenantFields = {};

  loaderObject = {
    isComponentLoading: false,
  };

  skillDisplayedColumns = [];
  extraCurricularActivitiesDisplayedColumns = [];
  languagesKnownDisplayedColumns = [];
  isWorkflowNotActive : any = true;
  skillTableData: skillDetailsDataType[];
  extraCurricularActivitiesTableData: extraCurricularActivitiesDetailsDataType[];
  languagesKnownTableData: languagesKnownDetailsDataType[];
  isFromMyProfile : boolean = false;
  approvalPendings: any = [];
  skillTableDataSource: any;
  extraCurricularActivitiesTableDataSource: any;
  languagesKnownTableDataSource: any;

  @ViewChild('skillTbSort') skillTbSort: MatSort;
  @ViewChild('extraCurricularTbSort') extraCurricularTbSort: MatSort;
  @ViewChild('languagesKnownTbSort') languagesKnownTbSort: MatSort;

  //Table declarations
  skillColumns = [
    {
      colKey: 'skill_type',
      colName: 'type',
      className: 'dark-txt',
      isToolTip: true,
      show: false,
      cell: (element: skillDetailsDataType) =>
        `${element.skill_type ? element.skill_type : '-'}`,
      toolTipVal: (element) =>
        `${element.skill_type ? element.skill_type : '-'}`,
    },
    {
      colKey: 'skill_library',
      colName: 'Skill Library',
      className: 'dark-txt',
      isToolTip: true,
      show: false,
      cell: (element: skillDetailsDataType) =>
        `${element.skill_type ? element.skill_type : '-'}`,
      toolTipVal: (element) =>
        `${element.skill_type ? element.skill_type : '-'}`,
    },
    {
      colKey: 'skill_name',
      colName: 'skill name',
      className: 'dark-txt',
      isToolTip: true,
      show: true,
      cell: (element: skillDetailsDataType) =>
        `${element.skill_name ? element.skill_name : '-'}`,
      toolTipVal: (element) =>
        `${element.skill_name ? element.skill_name : '-'}`,
    },
    {
      colKey: 'skill_level',
      colName: 'skill level',
      className: 'dark-txt',
      isToolTip: true,
      show: true,
      cell: (element: skillDetailsDataType) =>
        `${element.skill_level ? element.skill_level : '-'}`,
      toolTipVal: (element) =>
        `${element.skill_level ? element.skill_level : '-'}`,
    },
    {
      colKey: 'experience',
      colName: 'experience',
      className: 'dark-txt',
      isToolTip: true,
      show: false,
      cell: (element: skillDetailsDataType) =>
        `${element.experience ? element.experience : '-'}`,
      toolTipVal: (element) =>
        `${element.experience ? element.experience : '-'}`,
    },
    {
      colKey: 'skill_group',
      colName: 'Skill Group',
      className: 'dark-txt',
      isToolTip: true,
      show:false,
      cell: (element: skillDetailsDataType) =>
        `${element.skill_group ? element.skill_group : '-'}`,
      toolTipVal: (element) =>
        `${element.skill_group ? element.skill_group : '-'}`,
    },
    {
      colKey: 'skill_category',
      colName: 'Skill Category',
      className: 'dark-txt',
      isToolTip: true,
      show: false,
      cell: (element: skillDetailsDataType) =>
        `${element.skill_category ? element.skill_category: '-'}`,
      toolTipVal: (element) =>
        `${element.skill_category ? element.skill_category : '-'}`,
    },
    {
      colKey: 'endorse',
      colName: 'endrose',
      className: 'dark-txt',
      isToolTip: true,
      show: false,
      cell: (element: skillDetailsDataType) =>
        `${element.endorse ? element.endorse : '-'}`,
      toolTipVal: (element) => `${element.endorse ? element.endorse : '-'}`,
    },
    {
      colKey: 'isPrimarySkill',
      colName: 'Is Primary Skill',
      className: 'dark-txt',
      isToolTip: true,
      show: false,
      cell: (element: skillDetailsDataType) =>
        `${element.isPrimarySkill ? 'Yes' : 'No'}`,
      toolTipVal: (element) => `${element.isPrimarySkill ? 'Yes' : 'No'}`,
    },
    {
      colKey: 'sapActivate',
      colName: 'SAP Activate',
      className: 'dark-txt',
      isToolTip: true,
      show: false,
      cell: (element: skillDetailsDataType) =>
        `${element.sapActivate ? element.sapActivate : '-'}`,
      toolTipVal: (element) =>
        `${element.sapActivate ? element.sapActivate : '-'}`,
    },
    { 
      colKey: 'action',
      colName: 'Action',
      className: 'dark-txt',
      isToolTip: true,
      show:false,
      cell: (element: skillDetailsDataType) =>
      `${element.record_id ? element.record_id : ''}`,
      toolTipVal:(element) =>
      `${element.record_id ? element.record_id : ''}`,

    },
    { 
      colKey: 'remove',
      colName: 'Remove',
      className: 'dark-txt',
      isToolTip: true,
      show:false,
      cell: (element: skillDetailsDataType) =>
      `${element.record_id ? element.record_id : ''}`,
      toolTipVal:(element) =>
      `${element.record_id ? element.record_id : ''}`,

    },
  ];

  extraCurricularActivitiesColumns = [
    {
      colKey: 'activity_type_name',
      colName: 'Type',
      className: 'dark-txt',
      isToolTip: true,
      cell: (element: extraCurricularActivitiesDetailsDataType) =>
        `${element.activity_type_name ? element.activity_type_name : '-'}`,
      toolTipVal: (element) =>
        `${element.activity_type_name ? element.activity_type_name : '-'}`,
    },
    {
      colKey: 'activity_name',
      colName: 'Activity Name',
      className: 'dark-txt',
      isToolTip: true,
      cell: (element: extraCurricularActivitiesDetailsDataType) =>
        `${element.activity_name ? element.activity_name : '-'}`,
      toolTipVal: (element) =>
        `${element.activity_name ? element.activity_name : '-'}`,
    },
  ];

  languagesKnownColumns = [
    {
      colKey: 'language_name',
      colName: 'language',
      className: 'dark-txt',
      isToolTip: true,
      show: false,
      cell: (element: languagesKnownDetailsDataType) =>
        `${element.language_name ? element.language_name : '-'}`,
      toolTipVal: (element) =>
        `${element.language_name ? element.language_name : '-'}`,
    },
    {
      colKey: 'can_read',
      colName: 'read',
      className: 'dark-txt',
      isToolTip: true,
      show: false,
      cell: (element: languagesKnownDetailsDataType) =>
        `${element.can_read ? element.can_read : '-'}`,
      toolTipVal: (element) => `${element.can_read ? element.can_read : '-'}`,
    },
    {
      colKey: 'can_write',
      colName: 'write',
      className: 'dark-txt',
      isToolTip: true,
      show: false,
      cell: (element: languagesKnownDetailsDataType) =>
        `${element.can_write ? element.can_write : '-'}`,
      toolTipVal: (element) => `${element.can_write ? element.can_write : '-'}`,
    },
    {
      colKey: 'can_speak',
      colName: 'speak',
      className: 'dark-txt',
      isToolTip: true,
      show: false,
      cell: (element: languagesKnownDetailsDataType) =>
        `${element.can_speak ? element.can_speak : '-'}`,
      toolTipVal: (element) => `${element.can_speak ? element.can_speak : '-'}`,
    },
   
  ];

  constructor(
    private _edService: EmployeeDirectoryService,
    private _toaster: ToasterService,
    private dialog: MatDialog,
    private _loginService : LoginService
  ) {}

  async ngOnInit() {
    this.skillTableData = [];
    this.isFromMyProfile = this._loginService.getProfile().profile.aid == this.associateId;
    this.extraCurricularActivitiesTableData = [];
    this.languagesKnownTableData = [];

    this.loaderObject.isComponentLoading = true;

    this.employeeSkillDetails = await this.getEmployeeSkillDetails(
      this.associateId
    );
    // console.log(this.employeeSkillDetails)
    this.employeeLanguagesKnownDetails =
      await this.getEmployeeLanguagesKnownDetails(this.associateId);
    this.employeeExtraCurricularDetails =
      await this.getEmployeeExtraCurricularActivityDetails(this.associateId);
    this.employeeLegalAndAchievementsDetails =
      await this.getEmployeeLegalAndAchievementsDetails(this.associateId);

    await this.handleTenantWiseFieldConfig();
    this.isWorkflowNotActive = await this.CheckWorkflowStatus();

    let checkAccess = this._edService.checkViewAndEditAccess(245);
    this.showAddButton = checkAccess;
    this.showEditButton = checkAccess;
    this.skillTableData = this.employeeSkillDetails;
    this.skillTableDataSource = new MatTableDataSource(this.skillTableData);
    this.skillDisplayedColumns = this.skillColumns
      .filter((c) => !!c.show)
      .map((c) => c.colKey);
    

    this.extraCurricularActivitiesTableData =
      this.employeeExtraCurricularDetails;
    await this.checkColoumnVisibility();

    this.extraCurricularActivitiesTableDataSource = new MatTableDataSource(
      this.extraCurricularActivitiesTableData
    );
    this.extraCurricularActivitiesDisplayedColumns =
      this.extraCurricularActivitiesColumns.map((c) => c.colKey);

    this.languagesKnownTableData = this.employeeLanguagesKnownDetails;
    this.languagesKnownTableDataSource = new MatTableDataSource(
      this.languagesKnownTableData
    );
    this.languagesKnownDisplayedColumns = this.languagesKnownColumns.map(
      (c) => c.colKey
    );
    this.$isEmpRetired = this._edService.getEmployeeRetiredStatus();
    this.isNoDataFound =
      this.employeeSkillDetails.length > 0 ||
      this.employeeLanguagesKnownDetails.length > 0 ||
      this.employeeExtraCurricularDetails.length > 0 ||
      this.employeeLegalAndAchievementsDetails.length > 0
        ? false
        : true;
   if(this.isFromMyProfile){
          this.approvalPendings = await this.getApprovalPendingDetailsDetails();}

    this.loaderObject.isComponentLoading = false;
  }
  CheckWorkflowStatus(){
    return new Promise((resolve, reject) => {
      this.subs.sink = this._edService.checkWorkflowStatus(parseInt(this.associateId),'skill_details').subscribe(
        (res: any) => {
          resolve(res.allow_edit);
        },
        (err) => {
          console.log(err);
          reject(err);
        }
      );
    });
  }

  /**
   * @description get field tenant config
   */
  getFieldTenantConfig(tab_key) {
    return new Promise((resolve, reject) => {
      this.subs.sink = this._edService.getFieldTenantConfig(tab_key).subscribe(
        (res: any) => {
          resolve(res.data);
        },
        (err) => {
          console.log(err);
          reject(err);
        }
      );
    });
  }
  async removeSkill(row){
    let details={
    associateId:this.associateId,
    recordId:row?.id
    }
    this._edService.removeSkillRecord(details).subscribe(
    (res: any) => {
      if (!res.err) {
        this._toaster.showSuccess(
          'Success',
          'Skill details Removed successfully !',
          2000
        );
        this.ngOnInit();
      } else {
        this._toaster.showError(
          'Error',
          'Failed to save., Kindly contact KEBS team to resolve',
          2000
        );
      }
    },
    (err) => {
      this._toaster.showError(
        'Error',
        'Failed to save. Kindly contact KEBS team to resolve',
        2000
      );
      console.log(err);
      // this.loaderObject.isFormSubmitLoading = false;
    })

  }

  /**
   * @description handle tenant wise config
   */
  async handleTenantWiseFieldConfig() {
    let fieldList: any = await this.getFieldTenantConfig('skill_details');
    if (fieldList.length > 0) {
      fieldList.forEach((val, index) => {
        this.tenantFields[val.field] = {
          isMandatory: val.is_mandatory ? true : false,
          isActiveField: val.is_active_field ? true : false,
        };
        if (val.is_active_field)
          this.skillColumns.forEach((l) => {
            if (l.colKey == val.field) {
              // if(l.colKey=="action"){
              //   if(this.showEditButton){
              //     l.show = true;
              //   }else{
              //     l.show = false;
              //   }
              // }
              // else{
                l.show = true;
              // }
            }
          });
      });
    }
    console.log(this.tenantFields);
  }
  checkColoumnVisibility(){
    if(this.showEditButton){
    this.skillDisplayedColumns.forEach((l) => {
      if (l.colKey == 'action') {
        l.show = true;
      }
      if (l.colKey == 'remove') {
        l.show = true;
      }

    });
  }
}
  /**
   * @description get skill details
   */
  getEmployeeSkillDetails(associateId) {
    if (associateId) {
      return new Promise((resolve, reject) => {
        this.subs.sink = this._edService
          .getEmployeeSkillDetails(associateId)
          .subscribe(
            (res: any) => {
              resolve(res.data);
            },
            (err) => {
              this._toaster.showError(
                'Error',
                'Failed to retrieve employee !',
                2000
              );
              console.log(err);
              reject(err);
            }
          );
      });
    }
  }

  /**
   * @description get languages known details
   */
  getEmployeeLanguagesKnownDetails(associateId) {
    if (associateId) {
      return new Promise((resolve, reject) => {
        this.subs.sink = this._edService
          .getEmployeeLanguagesKnownDetails(associateId)
          .subscribe(
            (res: any) => {
              resolve(res.data);
            },
            (err) => {
              this._toaster.showError(
                'Error',
                'Failed to retrieve employee !',
                2000
              );
              console.log(err);
              reject(err);
            }
          );
      });
    }
  }
 
  /**
   * @description get extra curricular activities details
   */
  getEmployeeExtraCurricularActivityDetails(associateId) {
    if (associateId) {
      return new Promise((resolve, reject) => {
        this.subs.sink = this._edService
          .getEmployeeExtraCurricularActivityDetails(associateId)
          .subscribe(
            (res: any) => {
              resolve(res.data);
            },
            (err) => {
              this._toaster.showError(
                'Error',
                'Failed to retrieve employee !',
                2000
              );
              console.log(err);
              reject(err);
            }
          );
      });
    }
  }

  /**
   * @description get Legal and achievements details
   */
  getEmployeeLegalAndAchievementsDetails(associateId) {
    if (associateId) {
      return new Promise((resolve, reject) => {
        this.subs.sink = this._edService
          .getEmployeeLegalAndAchievementsDetails(associateId)
          .subscribe(
            (res: any) => {
              resolve(res.data);
            },
            (err) => {
              this._toaster.showError(
                'Error',
                'Failed to retrieve employee !',
                2000
              );
              console.log(err);
              reject(err);
            }
          );
      });
    }
  }

  skillTableSortData() {
    console.log("sort")
    console.log(this.skillTableDataSource.sort)
    console.log(this.skillTbSort)
    this.skillTableDataSource.sort = this.skillTbSort;
  }

  extraCurricularTableSortData() {
    this.extraCurricularActivitiesTableDataSource.sort =
      this.extraCurricularTbSort;
  }

  languagesKnownTableSortData() {
    this.languagesKnownTableDataSource.sort = this.languagesKnownTbSort;
  }

  async editSkillDetails(record?:any) {
    this.trigger?.closeMenu();
    let record_id=record?.id;
    let modalParams = {
      associateId: parseInt(this.associateId),
      isFromModal: true,
      record_id:record_id?record_id:null
    };

    const { SkillDetailsComponent } = await import(
      'src/app/modules/employee-directory/features/employee-directory-creation/features/skill-details/skill-details.component'
    );

    const skillDetailsComponent = this.dialog.open(SkillDetailsComponent, {
      height: '75%',
      width: '80vw',
      panelClass: 'e360-skill-details-modalbox',
      data: { modalParams: modalParams },
    });

    skillDetailsComponent.afterClosed().subscribe(
      (res) => {
        if (res == 'Updated') {
          this.ngOnInit();
        }
      },
      (err) => {
        this._toaster.showError('Error', 'Failed to retrieve employee !', 2000);
      }
    );
  }

  async openEditHistory() {
    this.trigger?.closeMenu();
    let modalParams = {
      associateId: parseInt(this.associateId),
      tabKey: 'skill_details',
    };
    const { EditHistoryModalComponent } = await import(
      'src/app/modules/employee-directory/shared-components/edit-history-modal/edit-history-modal.component'
    );
    this.dialog.open(EditHistoryModalComponent, {
      height: '100%',
      width: '40%',
      position: {
        right: '0px',
      },
      data: { modalParams: modalParams },
    });
  }

  ngOnDestroy(): void {
    this.subs.unsubscribe();
  }
  getApprovalPendingDetailsDetails(){
    //  this.loaderObject.isFormSubmitLoading = true;
      // let req = {
      //   "associate_id": this.associateId,
      // }
      return new Promise((resolve, reject) => {
        this.subs.sink = this._edService
          .getPendingApprovalsForEmployeeAID(this.associateId,'skill_details')
          .subscribe(
            (res: any) => {
              if(!res['err']){
              //this.loaderObject.isFormSubmitLoading = false;
              resolve(res.approval_list);
              }
            },
            (err) => {
              //this.loaderObject.isFormSubmitLoading = false;
              this._toaster.showError(
                'Error',
                'Failed to Update Skill Validation!',
                2000
              );
              console.log(err);
              reject(err);
            }
          );
      });
    }

    async openApprovalPendingDialog(){
      let modalParams = {
        associateId: parseInt(this.associateId),
        approvalPendings : this.approvalPendings[0]?.submission_item?.approval_fields,
        pendingMetaData : this.approvalPendings[0]
      };
      const { ApprovalPendingDetailScreenComponent } = await import(
        'src/app/modules/employee-directory/shared-components/approval-pending-detail-screen/approval-pending-detail-screen.component'
      );
     const approvalPendingDialogCompnent = this.dialog.open(ApprovalPendingDetailScreenComponent, {
        width: '700px',
        panelClass: 'e360-approval-pending-dialog-box',
        autoFocus: false,
        // minHeight: 'calc(100vh - 90px)',
       // height : '300px',
        data: { modalParams: modalParams },
      });
  
      approvalPendingDialogCompnent.afterClosed().subscribe(
        (res) => {
          // if (res == 'success') {
          //   this.isProfileValidated = true;
          // }
        },
        (err) => {
          this._toaster.showError('Error', 'Failed to retrieve employee !', 2000);
        }
      );
    }
}

export interface skillDetailsDataType {
  skill_name: string;
  skill_type: string;
  skill_level: string;
  endorse: string;
  experience: string;
  isPrimarySkill: boolean;
  sapActivate: string;
  record_id:number;
  skill_category:string;
  skill_group:string;
}
export interface languagesKnownDetailsDataType {
  language_name: string;
  can_read: string;
  can_write: string;
  can_speak: string;
}
export interface extraCurricularActivitiesDetailsDataType {
  activity_type_name: string;
  activity_name: string;
}

import { CommonModule } from '@angular/common';

import { SharedComponentsModule } from 'src/app/app-shared/app-shared-components/components.module';

//Angular material
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTooltipModule } from '@angular/material/tooltip';
import { EmployeeDirectoryService } from 'src/app/modules/employee-directory/services/employee-directory.service';
import { ToasterService } from 'src/app/services/toaster-service/toaster.service';
import { MatMenuModule } from '@angular/material/menu';
import { MatTableModule } from '@angular/material/table';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { CdkTableModule } from '@angular/cdk/table';
import { MatDividerModule } from '@angular/material/divider';
import { MatDialog } from '@angular/material/dialog';
import { Observable } from 'rxjs';
@NgModule({
  declarations: [SkillDetailsComponent],
  imports: [
    CommonModule,
    SharedComponentsModule,
    MatIconModule,
    MatButtonModule,
    MatProgressSpinnerModule,
    MatTooltipModule,
    MatMenuModule,
    MatTableModule,
    MatSortModule,
    CdkTableModule,
    MatDividerModule,
  ],
  exports: [],
})
export class SkillDetailsModule {}
