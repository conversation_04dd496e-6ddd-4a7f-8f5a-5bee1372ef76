<div class="container-fluid p-0 pr-1 mt-2 time-details-styles">
  <ng-container *ngIf="loaderObject.isComponentLoading">
    <div class="d-flex justify-content-center mt-3">
      <mat-spinner matTooltip="Please wait..." diameter="30"> </mat-spinner>
    </div>
  </ng-container>
  <ng-container *ngIf="!loaderObject.isComponentLoading">
    <!--First Card UI-->

    <div class="row pt-3 pb-2">
      <mat-card class="pl-0 mat-card slide-from-down" style="min-height: 75vh">
        <div class="col-12 px-2 py-2">
          <!--Time Details-->
          <div class="row">
            <div class="col-10">
              <span class="section-heading">Time Details</span>
            </div>
            <div class="col-2">
              <button
                class="edit-button"
                mat-button
                #menuTrigger="matMenuTrigger"
                [matMenuTriggerFor]="editMenu"
                [disabled]="isNoDataFound"
              >
                <mat-icon class="edit-icon"> edit </mat-icon>
                <span class="pl-0 edit-text"> Edit </span>
                <mat-icon class="pl-0 drop-down-icon">
                  keyboard_arrow_down
                </mat-icon>
              </button>
              <mat-menu #editMenu="matMenu">
                <button
                  mat-menu-item
                  (click)="editTimeDetails()"
                  disabled="{{!showEditButton ||  ($isEmpRetired | async) }}"
                >
                  Edit
                </button>
                <button mat-menu-item (click)="openEditHistory()">
                  Edit History
                </button>
              </mat-menu>
            </div>
          </div>
          <div
            class="row pt-5 pb-2 justify-content-center"
            *ngIf="isNoDataFound"
          >
            <div class="col-4">
              <div
                class="d-flex justify-content-center align-items-center slide-from-down"
              >
              <img
              src="https://assets.kebs.app/images/empty_re_opql.png"
              class="mt-2"
              height="150"
              width="200"
            />
              </div>
              <div class="pt-3 no-data-found-text">
                <span
                  class="item-value"
                  style="
                    font-size: 15px !important;
                    font-weight: 700 !important;
                  "
                  >No Data Here</span
                >
              </div>
              <div class="pt-2 no-data-found-text" *ngIf="showAddButton">
                <span
                  class="item-value"
                  style="
                    white-space: normal !important;
                    text-align: center;
                    font-weight: 400 !important;
                  "
                  >Add Time Details By Clicking the Button Below</span
                >
              </div>
              <div class="pt-3 no-data-found-text" *ngIf="showAddButton">
                <button
                  mat-raised-button
                  class="ml-3"
                  (click)="editTimeDetails()"
                  disabled="{{ $isEmpRetired | async }}"
                  [ngClass]=" ($isEmpRetired | async) ? 'disabled-add-button' : 'add-details-btn'"
                >
                  Add Details >
                </button>
              </div>
            </div>
          </div>
          <div class="col-12 p-0" *ngIf="!isNoDataFound">
            <div class="row pt-2">
              <div class="col-4">
                <div class="row">
                  <span class="title-heading">In Time</span>
                </div>
                <div class="row d-flex">
                  <span
                    class="item-value"
                    matTooltip="{{
                      employeeTimeDetails?.in_time
                        ? employeeTimeDetails?.in_time
                        : '-'
                    }}"
                  >
                    {{
                      employeeTimeDetails?.in_time
                        ? employeeTimeDetails?.in_time
                        : "-"
                    }}
                  </span>
                  <span
                    class="pl-2 creacent-icon"
                    *ngIf="employeeTimeDetails?.is_next_calender_day"
                  >
                    <mat-icon style="font-size: 20px">dark_mode</mat-icon></span
                  >
                </div>
              </div>
              <div class="col-4">
                <div class="row">
                  <span class="title-heading">Out Time</span>
                </div>
                <div class="row d-flex">
                  <span
                    class="item-value"
                    matTooltip="{{
                      employeeTimeDetails?.out_time
                        ? employeeTimeDetails?.out_time
                        : '-'
                    }}"
                  >
                    {{
                      employeeTimeDetails?.out_time
                        ? employeeTimeDetails?.out_time
                        : "-"
                    }}
                  </span>
                  <span
                    class="pl-2 creacent-icon"
                    *ngIf="employeeTimeDetails?.is_next_calender_day"
                  >
                    <mat-icon style="font-size: 20px">dark_mode</mat-icon></span
                  >
                </div>
              </div>
             
            </div>
            <div class="row pt-3">
              <div class="col-4">
                <div class="row">
                  <span class="title-heading">Daily Hours</span>
                </div>
                <div class="row">
                  <span
                    class="item-value"
                    matTooltip="{{
                      employeeTimeDetails?.daily_working_hours
                        ? employeeTimeDetails?.daily_working_hours
                        : '-'
                    }}"
                  >
                    {{
                      employeeTimeDetails?.daily_working_hours
                        ? employeeTimeDetails?.daily_working_hours
                        : "-"
                    }}
                  </span>
                </div>
              </div>
              <div class="col-4">
                <div class="row">
                  <span class="title-heading">Monthly Hours</span>
                </div>
                <div class="row">
                  <span
                    class="item-value"
                    matTooltip="{{
                      employeeTimeDetails?.monthly_working_hours
                        ? employeeTimeDetails?.monthly_working_hours
                        : '-'
                    }}"
                  >
                    {{
                      employeeTimeDetails?.monthly_working_hours
                        ? employeeTimeDetails?.monthly_working_hours
                        : "-"
                    }}
                  </span>
                </div>
              </div>
            </div>
            <div class="row pt-3">
              <div class="col-4">
                <div class="row">
                  <span class="title-heading">Work Schedule</span>
                </div>
                <div class="row">
                  <span
                    class="item-value"
                    matTooltip="{{
                      employeeTimeDetails?.work_schedule
                        ? employeeTimeDetails?.work_schedule
                        : '-'
                    }}"
                  >
                    {{
                      employeeTimeDetails?.work_schedule
                        ? employeeTimeDetails?.work_schedule
                        : "-"
                    }}
                  </span>
                </div>
              </div>
              <div class="col-4">
                <div class="row">
                  <span class="title-heading">Holiday Calendar</span>
                </div>
                <div class="row">
                  <span
                    class="item-value"
                    matTooltip="{{
                      employeeTimeDetails?.holiday_calendar
                        ? employeeTimeDetails?.holiday_calendar
                        : '-'
                    }}"
                  >
                    {{
                      employeeTimeDetails?.holiday_calendar
                        ? employeeTimeDetails?.holiday_calendar
                        : "-"
                    }}
                  </span>
                </div>
              </div>
              <div class="col-4">
                <div class="row">
                  <span class="title-heading">Work Location</span>
                </div>
                <div class="row">
                  <span
                    class="item-value"
                    matTooltip="{{
                      employeeTimeDetails?.work_location
                        ? employeeTimeDetails?.work_location
                        : '-'
                    }}"
                  >
                    {{
                      employeeTimeDetails?.work_location
                        ? employeeTimeDetails?.work_location
                        : "-"
                    }}
                  </span>
                </div>
              </div>
            </div>
            <div class="row pt-3">
              <div class="col-4">
                <div class="row">
                  <span class="title-heading">Work Classification</span>
                </div>
                <div class="row">
                  <span
                    class="item-value"
                    matTooltip="{{
                      employeeTimeDetails?.work_classification
                        ? employeeTimeDetails?.work_classification
                        : '-'
                    }}"
                  >
                    {{
                      employeeTimeDetails?.work_classification
                        ? employeeTimeDetails?.work_classification
                        : "-"
                    }}
                  </span>
                </div>
              </div>
            </div>
            <div class="row pt-3">
              <div class="col-4">
                <div class="row">
                  <span class="title-heading">Auto Timesheet Submission</span>
                </div>
                <div class="row">
                  <span
                    class="item-value"
                    matTooltip="{{
                      employeeTimeDetails?.auto_timesheet_submission
                        ? employeeTimeDetails?.auto_timesheet_submission
                        : '-'
                    }}"
                  >
                    {{
                      employeeTimeDetails?.auto_timesheet_submission
                        ? employeeTimeDetails?.auto_timesheet_submission
                        : "-"
                    }}
                  </span>
                </div>
              </div>
              <div class="col-4">
                <div class="row">
                  <span class="title-heading">Timesheet Submission</span>
                </div>
                <div class="row">
                  <span
                    class="item-value"
                    matTooltip="{{
                      employeeTimeDetails?.timesheet_submission
                        ? employeeTimeDetails?.timesheet_submission
                        : '-'
                    }}"
                  >
                    {{
                      employeeTimeDetails?.timesheet_submission
                        ? employeeTimeDetails?.timesheet_submission
                        : "-"
                    }}
                  </span>
                </div>
              </div>

              <!-- <div class="col-4" >
                <div class="row">
                  <span class="title-heading">Leave Auto Approval</span>
                </div>
                <div class="row">
                  <span
                    class="item-value"
                    matTooltip="{{
                      employeeTimeDetails?.leave_auto_approve
                        ? employeeTimeDetails?.leave_auto_approve
                        : '-'
                    }}"
                  >
                    {{
                      employeeTimeDetails?.leave_auto_approve
                        ? employeeTimeDetails?.leave_auto_approve
                        : "-"
                    }}
                  </span>
                </div>
              </div> -->

              <div class="col-4">
                <div class="row">
                  <span class="title-heading">Project Available Hours</span>
                </div>
                <div class="row">
                  <span
                    class="item-value"
                    matTooltip="{{
                      employeeTimeDetails?.project_available_hours
                        ? employeeTimeDetails?.project_available_hours
                        : '-'
                    }}"
                  >
                    {{
                      employeeTimeDetails?.project_available_hours
                        ? employeeTimeDetails?.project_available_hours
                        : "-"
                    }}
                  </span>
                </div>
              </div>
            </div>
            <!-- <div class="row pt-3">
              <div class="col-4">
                <div class="row">
                  <span class="title-heading">Timesheet Schedule</span>
                </div>
                <div class="row">
                  <span
                    class="item-value"
                    matTooltip="{{
                      employeeTimeDetails?.timesheet_schedule
                        ? employeeTimeDetails?.timesheet_schedule
                        : '-'
                    }}"
                  >
                    {{
                      employeeTimeDetails?.timesheet_schedule
                        ? employeeTimeDetails?.timesheet_schedule
                        : "-"
                    }}
                  </span>
                </div>
              </div>
              <div class="col-4">
                <div class="row">
                  <span class="title-heading">Timesheet Notification</span>
                </div>
                <div class="row">
                  <span
                    class="item-value"
                    matTooltip="{{
                      employeeTimeDetails?.ts_notif_flag
                        ? employeeTimeDetails?.ts_notif_flag
                        : '-'
                    }}"
                  >
                    {{
                      employeeTimeDetails?.ts_notif_flag
                        ? employeeTimeDetails?.ts_notif_flag
                        : "-"
                    }}
                  </span>
                </div>
              </div>
              <div class="col-4">
                <div class="row">
                  <span class="title-heading">Allow Mapped Cost Center</span>
                </div>
                <div class="row">
                  <span
                    class="item-value"
                    matTooltip="{{
                      employeeTimeDetails?.restrict_mapped_cc_in_ts
                        ? employeeTimeDetails?.restrict_mapped_cc_in_ts
                        : '-'
                    }}"
                  >
                    {{
                      employeeTimeDetails?.restrict_mapped_cc_in_ts
                        ? employeeTimeDetails?.restrict_mapped_cc_in_ts
                        : "-"
                    }}
                  </span>
                </div>
              </div>
            </div> -->

              <div class="row pt-3">
              <div class="col-4">
                <div class="row">
                  <span class="title-heading">Leave Auto Approval</span>
                </div>
                <div class="row">
                  <span
                    class="item-value"
                    matTooltip="{{
                      employeeTimeDetails?.leave_auto_approve
                        ? employeeTimeDetails?.leave_auto_approve
                        : '-'
                    }}"
                  >
                    {{
                      employeeTimeDetails?.leave_auto_approve
                        ? employeeTimeDetails?.leave_auto_approve
                        : "-"
                    }}
                  </span>
                </div>
              </div>

              <div class="col-4">
                <div class="row">
                  <span class="title-heading">Allow Editing Of Timesheet</span>
                </div>
                <div class="row">
                  <span
                    class="item-value"
                    matTooltip="{{
                      employeeTimeDetails?.is_timesheet_edit_allowed
                        ? employeeTimeDetails?.is_timesheet_edit_allowed
                        : '-'
                    }}"
                  >
                    {{
                      employeeTimeDetails?.is_timesheet_edit_allowed
                        ? employeeTimeDetails?.is_timesheet_edit_allowed
                        : "-"
                    }}
                  </span>
                </div>
              </div>
        
            </div>
          </div>
        </div>
      </mat-card>
    </div>
  </ng-container>
</div>
