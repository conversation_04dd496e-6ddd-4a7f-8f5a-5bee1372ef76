import { Component, Input, NgModule, OnInit, ViewChild } from '@angular/core';
import * as _ from 'underscore';
import * as moment from 'moment';
import { SubSink } from 'subsink';

@Component({
  selector: 'app-time-details',
  templateUrl: './time-details.component.html',
  styleUrls: ['./time-details.component.scss'],
})
export class TimeDetailsComponent implements OnInit {
  @Input() associateId: any;

  subs = new SubSink();

  isNoDataFound = false;
  showAddButton = false;
  showEditButton = false;
  $isEmpRetired : Observable<boolean>;

  employeeTimeDetails: any;
  @ViewChild('menuTrigger') trigger;

  loaderObject = {
    isComponentLoading: false,
  };
  tenantFields: any;

  constructor(
    private _edService: EmployeeDirectoryService,
    private _toaster: ToasterService,
    private dialog: MatDialog
  ) {}

  async ngOnInit() {
    this.loaderObject.isComponentLoading = true;
    this.employeeTimeDetails = await this.getEmployeeTimeDetails(
      this.associateId
    );
    let checkAccess = this._edService.checkViewAndEditAccess(189);
    this.showAddButton = checkAccess;
    this.showEditButton = checkAccess;
    this.$isEmpRetired = this._edService.getEmployeeRetiredStatus();
    this.isNoDataFound = this.employeeTimeDetails.length > 0 ? false : true;
    this.employeeTimeDetails = this.employeeTimeDetails[0];
    this.loaderObject.isComponentLoading = false;
  }

  /**
   * @description get employee time details
   */
  getEmployeeTimeDetails(associateId) {
    if (associateId) {
      return new Promise((resolve, reject) => {
        this.subs.sink = this._edService
          .getEmployeeTimeDetails(associateId)
          .subscribe(
            (res: any) => {
              resolve(res.data);
            },
            (err) => {
              this._toaster.showError(
                'Error',
                'Failed to retrieve employee !',
                2000
              );
              console.log(err);
              reject(err);
            }
          );
      });
    }
  }

  async editTimeDetails() {
    this.trigger.closeMenu();
    let modalParams = {
      associateId: parseInt(this.associateId),
      isFromModal: true,
    };

    const { TimeDetailsComponent } = await import(
      'src/app/modules/employee-directory/features/employee-directory-creation/features/time-details/time-details.component'
    );

    const timeDetailsComponent = this.dialog.open(TimeDetailsComponent, {
      height: '75%',
        width: '80vw',
      panelClass: 'e360-time-details-modalbox',
      data: { modalParams: modalParams },
    });

    timeDetailsComponent.afterClosed().subscribe(
      (res) => {
        if (res == 'Updated') {
          this.ngOnInit();
        }
      },
      (err) => {
        this._toaster.showError('Error', 'Failed to retrieve employee !', 2000);
      }
    );
  }

  async openEditHistory() {
    this.trigger.closeMenu();
    let modalParams = {
      associateId: parseInt(this.associateId),
      tabKey: 'time_details',
    };
    const { EditHistoryModalComponent } = await import(
      'src/app/modules/employee-directory/shared-components/edit-history-modal/edit-history-modal.component'
    );
    this.dialog.open(EditHistoryModalComponent, {
      height: '100%',
      width: '40%',
      position: {
        right: '0px',
      },
      data: { modalParams: modalParams },
    });
  }

  async handleTenantWiseFieldConfig() {
    let timeDeatilsField:any = await this.getFieldTenantConfig('time_details');
    if(timeDeatilsField.length > 0) {
      timeDeatilsField.forEach((val,index) => {
        this.tenantFields[val.field] = {
          isMandatory : val.is_mandatory ? true : false,
          isActiveField: val.is_active_field ? true : false
        }
      })
    }
  
  }
  getFieldTenantConfig(tab_key) {
    return new Promise((resolve,reject) => {
      this.subs.sink = this._edService.getFieldTenantConfig(tab_key)
      .subscribe((res: any) => {
        resolve(res.data)
      }, (err) => {
        console.log(err)
        reject(err)
      })
    })
  }

  ngOnDestroy(): void {
    this.subs.unsubscribe();
  }
}

import { CommonModule } from '@angular/common';

import { SharedComponentsModule } from 'src/app/app-shared/app-shared-components/components.module';

//Angular material
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTooltipModule } from '@angular/material/tooltip';
import { EmployeeDirectoryService } from 'src/app/modules/employee-directory/services/employee-directory.service';
import { ToasterService } from 'src/app/services/toaster-service/toaster.service';
import { MatMenuModule } from '@angular/material/menu';
import { MatDialog } from '@angular/material/dialog';
import { Observable } from 'rxjs';

@NgModule({
  declarations: [TimeDetailsComponent],
  imports: [
    CommonModule,
    SharedComponentsModule,
    MatIconModule,
    MatButtonModule,
    MatProgressSpinnerModule,
    MatTooltipModule,
    MatMenuModule,
  ],
  exports: [],
})
export class TimeDetailsModule {}
