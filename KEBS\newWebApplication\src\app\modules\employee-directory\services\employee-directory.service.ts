import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable, Subject } from 'rxjs';
import { RolesService } from 'src/app/services/acl/roles.service';
import { TenantService } from 'src/app/services/tenant-service/tenant.service';
import * as _ from 'underscore';
import * as moment from 'moment';
import { DetailActivitiesComponent } from '../../compliance-management/sub-modules/compliance-detail/features/detail-activities/detail-activities.component';

@Injectable({
  providedIn: 'root',
})
export class EmployeeDirectoryService {
  routeLinks = {
    homePage: '/main/employee-central/employeeCentralHome',
    creationPage: '/main/employee-central/employeeCentralCreation/',
    detailPage: '/main/employee-central/employeeCentralDetail/',
    adminPage:'/main/employee-central/employeeCentralHome/admin'
  };

 // tenantInfo: any;

  roleList: any;
  ed_aid:any;
  constructor(
    private _http: HttpClient,
    private roleService: RolesService,
    private tenantService: TenantService
  ) {
    // this.getTenantInfo();
  }

  activitySubject = new Subject<any>();
  public getActivityObservable = this.activitySubject.asObservable();

  isTermsAndCoditionAgreedSubject = new Subject<any>();
  public getIsTermsAndCoditionAgreedActivity = this.isTermsAndCoditionAgreedSubject.asObservable();
  
  private employeeIdDataSource: BehaviorSubject<string> =
    new BehaviorSubject<string>('');
  
  private employeeRetireStatusDataSource: BehaviorSubject<boolean> =
    new BehaviorSubject<boolean>(false);

    setActivityObservable(data) {
      this.activitySubject.next(data);
    }

    setIsTermsAndCoditionAgreedObservable(data) {
      this.isTermsAndCoditionAgreedSubject.next(data);
    }

  getEmployeeId(): Observable<any> {
    return this.employeeIdDataSource.asObservable();
  }

  getEmployeeRetiredStatus(): Observable<boolean> {
    return this.employeeRetireStatusDataSource.asObservable();
  }

  sendEmployeeRetiredStatus(data: boolean) {
    this.employeeRetireStatusDataSource.next(data);
  }

  getEffectiveDate(doj) {
    let resultDate
    if(moment(doj).format('YYYY-MM-DD') > moment().format('YYYY-MM-DD')) {
      resultDate = moment(doj).format('YYYY-MM-DD')
    }
    else {
      resultDate =  moment().format('YYYY-MM-DD')
    }
    return resultDate
  }

  addLocalTimeOffset(data) {
    data = moment(data).add(moment().local().utcOffset(),'m')
    return moment(data).toISOString()
  }

  hasEDAccess() {
    let accessList = _.where(this.roleService.roles, { application_id: 323, object_id: 6 });
    console.log(accessList)
    return accessList.length > 0;
  }
  
  getTenantInfo() {
    return new Promise((resolve, reject) => {
      this.tenantService.getTenantInfo().then(
        async (tenantInfo: any) => {
         // this.tenantInfo = tenantInfo;
          resolve(tenantInfo.tenant_name);
        },
        (err) => {
          reject(err);
        }
      );
    });
  }

  sendEmployeeId(data: string) {
    this.employeeIdDataSource.next(data);
  }

  getHomePageNavLink() {
    return this.routeLinks.homePage;
  }

  getNewEmpCreationNavLink(creationMode) {
    return this.routeLinks.creationPage + creationMode;
  }

  getCompleteProfileNavLink(creationMode, empId) {
    return this.routeLinks.creationPage + creationMode + '/' + empId;
  }

  getDetailPageNavLink(empId, featureModule) {
    return this.routeLinks.detailPage + empId;
  }

  getEmploymentType() {
    return this._http.post('/api/employee360/masterData/getEmployeeType', {});
  }
  getContractType() {
    return this._http.post('/api/employee360/masterData/getContractType', {});
  }
  getExitType() {
    return this._http.post('/api/employee360/masterData/getExitType', {});
  }

  getEmploymentStatus() {
    return this._http.post('/api/employee360/masterData/getEmployeeStatus', {});
  }

  getSourceOfHire() {
    return this._http.post('/api/employee360/masterData/getSourceOfHire', {});
  }

  getSalutation() {
    return this._http.post('/api/employee360/masterData/getSalutation', {});
  }

  getGender() {
    return this._http.post('/api/employee360/masterData/getGender', {});
  }

  getBloodGroupType() {
    return this._http.post('/api/employee360/masterData/getBloodGroupType', {});
  }

  getNationality() {
    return this._http.post('/api/employee360/masterData/getNationality', {});
  }

  getCountry() {
    return this._http.post('/api/employee360/masterData/getCountry', {});
  }

  getEntities() {
    return this._http.post('/api/employee360/masterData/getEntities', {});
  }
  getTimeSheetSchedule() {
    return this._http.post('/api/employee360/masterData/getTimeSheetSchedule', {});
  }
  getDepartments() {
    return this._http.post('/api/employee360/masterData/getDepartments', {});
  }

  getWorkLocation() {
    return this._http.post('/api/employee360/masterData/getWorkLocation', {});
  }

  getEmergencyContactRelationship() {
    return this._http.post(
      '/api/employee360/masterData/getEmergencyContactRelationship',
      {}
    );
  }

  getDivision(entity) {
    return this._http.post('/api/employee360/masterData/getDivision', {
      entity_id: entity,
    });
  }

  getSubDivision(entity, division) {
    return this._http.post('/api/employee360/masterData/getSubDivision', {
      entity_id: entity,
      division_id: division,
    });
  }
  getDirectManagerBasedOnDepartment(department) {
    return this._http.post('/api/employee360/employeeDetails/getDirectManagerBasedOnDepartment', {
      department: department
    });
  }

  getJobType() {
    return this._http.post('/api/employee360/masterData/getJobType', {});
  }

  getPositionType() {
    return this._http.post('/api/employee360/masterData/getPositionType', {});
  }

  getCostCenter() {
    return this._http.post('/api/employee360/masterData/getCostCenter', {});
  }
  getVendorList() {
    return this._http.post('api/purchaseRequest/getVendor', {});
  }

  getPhoneCode() {
    return this._http.post('/api/employee360/masterData/getPhoneCode', {});
  }

  executeQuickHire(finalResponse) {
    return this._http.post(
      '/api/employee360/employeeDetails/executeQuickHire',
      {
        empDetails: finalResponse,
      }
    );
  }

  getEmployeeDirectoryData(bodyParams) {
    return this._http.post(
      '/api/employee360/employeeDetails/getAllEmpDetailsForReport',
      {
        filterConfig: bodyParams,
      }
    );
  }

  getEmployeeSummaryCardUdrf(bodyParams) {
    return this._http.post(
      '/api/employee360/employeeDetails/getAllEmpCountForReport',
      {
        filterConfig: bodyParams,
      }
    );
  }
  getEmployeeBuSbu(details) {
    return this._http.post(
      '/api/employee360/employeeDetails/getBuSbubasedonEntity',{details:details}
      
    );
  }

  getIdProofTypes(tab_key) {
    return this._http.post('/api/employee360/masterData/getIdProofTypes', {
      tab_key: tab_key
    });
  }

  getPersonalDetailsCP(associateId) {
    return this._http.post(
      '/api/employee360/employeeDetails/getPersonalDetailsCP',
      {
        associate_id: associateId,
      }
    );
  }
  getWorkLocationDetails() {
    return this._http.post('/api/employee360/admin/getWorkLocationDetails', {});
  }
  getDivisionDetails() {
    return this._http.post('/api/employee360/admin/getDivisionDetails', {});
  }
  getSubDivisionDetails() {
    return this._http.post('/api/employee360/admin/getSubDivisionDetails', {});
  }
  updateWorkLocationMasterList(rowValue) {
    return this._http.post('/api/employee360/admin/updateWorkLocationMasterList', {params : rowValue});
  }
  insertRoleMasterList(rowValue) {
    return this._http.post('/api/employee360/admin/insertRoleMasterList', {params : rowValue});
  }
  getRoleDetails() {
    return this._http.post('/api/employee360/admin/getRoleDetails', {});
  }
  updateRoleMasterList(rowValue) {
    return this._http.post('/api/employee360/admin/updateRoleMasterList', {params : rowValue});
  }
  insertWorkLocationMasterList(rowValue) {
    return this._http.post('/api/employee360/admin/insertWorkLocationMasterList', {params : rowValue});
  }
  updateDivisionMasterList(rowValue) {
    return this._http.post('/api/employee360/admin/updateDivisionMasterList', {params : rowValue});
  }
  insertDivisionMasterList(rowValue) {
    return this._http.post('/api/employee360/admin/insertDivisionMasterList', {params : rowValue});
  }
  updateSubDivisionMasterList(rowValue) {
    return this._http.post('/api/employee360/admin/updateSubDivisionMasterList', {params : rowValue});
  }
  insertSubDivisionMasterList(rowValue) {
    return this._http.post('/api/employee360/admin/insertSubDivisionMasterList', {params : rowValue});
  }
  insertPositionMasterList(rowValue) {
    return this._http.post('/api/employee360/admin/insertPositionMasterList', {params : rowValue});
  }
  getPositionDetails() {
    return this._http.post('/api/employee360/admin/getPositionDetails', {});
  }
  updatePositionMasterList(rowValue) {
    return this._http.post('/api/employee360/admin/updatePositionMasterList', {params : rowValue});
  }
  insertOrganizationMasterList(rowValue) {
    return this._http.post('/api/employee360/admin/insertOrganizationMasterList', {params : rowValue});
  }
  getOrganizationDetails() {
    return this._http.post('/api/employee360/admin/getOrganizationDetails', {});
  }
  updateOrganizationMasterList(rowValue) {
    return this._http.post('/api/employee360/admin/updateOrganizationMasterList', {params : rowValue});
  }
  insertOrgMapMasterList(rowValue) {
    return this._http.post('/api/employee360/admin/insertOrgMapMasterList', {params : rowValue});
  }
  getOrgMapDetails() {
    return this._http.post('/api/employee360/admin/getOrgMapDetails', {});
  }
  updateOrgMapMasterList(rowValue) {
    return this._http.post('/api/employee360/admin/updateOrgMapMasterList', {params : rowValue});
  }
  insertEntityMasterList(rowValue) {
    return this._http.post('/api/employee360/admin/insertEntityMasterList', {params : rowValue});
  }
  getEntityDetails() {
    return this._http.post('/api/employee360/admin/getEntityDetails', {});
  }
  updateEntityMasterList(rowValue) {
    return this._http.post('/api/employee360/admin/updateEntityMasterList', {params : rowValue});
  }
  getDivisionData(){
    return this._http.get('api/employee360/masterData/getDivisionData')
  }
  getSubDivisionData(){
    return this._http.get('api/employee360/masterData/getSubdivisionData')
  }
  getLocationOfficeData(){
    return this._http.get('api/employee360/masterData/getLocationOfficeData')
  }
  getTimesheetLocationData(){
    return this._http.get('api/employee360/masterData/getTimesheetLocationData')
  }


  getEmployeeCostCenterDetails(associateId) {
    return this._http.post(
      '/api/employee360/organizationDetails/getEmployeeCostCenterDetails',
      {
        associate_id: associateId,
      }
    );
  }
  getEmployeeCostCenterDetailsCp(associateId,recordId?) {
    return this._http.post(
      '/api/employee360/organizationDetails/getEmployeeCostCenterDetailscp',
      {
        associate_id: associateId,
        record_id:recordId ? recordId:null
      }
    );
  }
  updateEmployeeCostCenterDetails(details) {
    return this._http.post(
      '/api/employee360/organizationDetails/updateEmployeeCostCenterDetails',
      {
    details:details
      }
    );
  }

 
  getMaritalStatus() {
    return this._http.post('/api/employee360/masterData/getMaritalStatus', {});
  }

  savePersonalDetailsCP(details) {
    return this._http.post(
      '/api/employee360/employeeDetails/savePersonalDetailsCP',
      {
        details: details,
      }
    );
  }

  getCompleteProfileSectionStatus(associateId) {
    return this._http.post(
      '/api/employee360/employeeDetails/getCompleteProfileSectionStatus',
      {
        associate_id: associateId,
      }
    );
  }

  saveFamilyDetailsCP(details) {
    return this._http.post(
      '/api/employee360/employeeDetails/saveFamilyDetailsCP',
      {
        details: details,
      }
    );
  }

  getFamilyDetailsCP(associateId) {
    return this._http.post(
      '/api/employee360/employeeDetails/getFamilyDetailsCP',
      {
        associate_id: associateId,
      }
    );
  }

  getOccupation() {
    return this._http.post('/api/employee360/masterData/getOccupation', {});
  }

  getEmployeeNameById(employeeId) {
    return this._http.post(
      '/api/employee360/profileDetails/getEmployeeNameById',
      {
        employeeId: employeeId,
      }
    );
  }

  getQuickActions() {
    return this._http.post('/api/employee360/masterData/getQuickActions', {});
  }

  getEmployeePersonalDetails(associateId) {
    return this._http.post(
      '/api/employee360/profileDetails/getEmployeePersonalDetails',
      {
        associate_id: associateId,
      }
    );
  }

  getManagerialDetailsCP(associateId) {
    return this._http.post(
      '/api/employee360/employeeDetails/getManagerialDetailsCP',
      {
        associate_id: associateId,
      }
    );
  }

  getOrganizationDetailsCP(associateId) {
    return this._http.post(
      '/api/employee360/employeeDetails/getOrganizationDetailsCP',
      {
        associate_id: associateId,
      }
    );
  }

  saveOrganizationDetailsCP(details) {
    return this._http.post(
      '/api/employee360/employeeDetails/saveOrganizationDetailsCP',
      {
        details: details,
      }
    );
  }

  saveDelegationDetailsCP(details) {
    return this._http.post(
      '/api/employee360/employeeDetails/saveDelegationDetailsCP',
      {
        details: details,
      }
    );
  }

  saveManagerialDetailsCP(details) {
    return this._http.post(
      '/api/employee360/employeeDetails/saveManagerialDetailsCP',
      {
        details: details,
      }
    );
  }

  getTimeDetailsCP(associateId) {
    return this._http.post(
      '/api/employee360/employeeDetails/getTimeDetailsCP',
      {
        associate_id: associateId,
      }
    );
  }

  saveTimeDetailsCP(details) {
    return this._http.post(
      '/api/employee360/employeeDetails/saveTimeDetailsCP',
      {
        details: details,
      }
    );
  }

  getWorkSchedule() {
    return this._http.post('/api/employee360/masterData/getWorkSchedule', {});
  }

  getKebsRole() {
    return this._http.post('/api/employee360/masterData/getKebsRole', {});
  }
  getKebsRoleBasedOnDivision(division_id) {
    return this._http.post('/api/employee360/masterData/getKebsRoleBasedOnDivision', {division_id});
  }

  getCertificationType() {
    return this._http.post(
      '/api/employee360/masterData/getCertificationType',
      {}
    );
  }

  getCertifiedByList() {
    return this._http.post(
      '/api/employee360/masterData/getCertifiedByList',
      {}
    );
  }

  getCertificationMode() {
    return this._http.post(
      '/api/employee360/masterData/getCertificationMode',
      {}
    );
  }

  getCertificationSponser() {
    return this._http.post(
      '/api/employee360/masterData/getCertificationSponser',
      {}
    );
  }

  getCertificationDetailsCP(associateId) {
    return this._http.post(
      '/api/employee360/employeeDetails/getCertificationDetailsCP',
      {
        associate_id: associateId,
      }
    );
  }

  getEmployeeFamilyDetails(associateId) {
    return this._http.post(
      '/api/employee360/profileDetails/getEmployeeFamilyDetails',
      {
        associate_id: associateId,
      }
    );
  }

  rerunTimesheet(details){
    return this._http.post(
      '/api/bgjPrimary/doAutoTimesheetSave',
        details
       )}
  

  saveCertificationDetailsCP(details) {
    return this._http.post(
      '/api/employee360/employeeDetails/saveCertificationDetailsCP',
      {
        details: details,
      }
    );
  }

  SaveUpdatedEmployeeDetails(details){
    return this._http.post(
      '/api/employee360/employeeDetails/SaveUpdatedEmployeeDetails',
      {
        details: details,
      }
    );
  }

  getEmployeePreviousJobDetails(associateId) {
    return this._http.post(
      '/api/employee360/profileDetails/getEmployeePreviousJobDetails',
      {
        associate_id: associateId,
      }
    );
  }

  getCostCenterForProject(project_id) {
    return this._http.post(
      '/api/employee360/masterData/getCostCenterForProject',
      {
        project_id: project_id,
      }
    );
  }

  getEmployeeReferenceDetails(associateId) {
    return this._http.post(
      '/api/employee360/profileDetails/getEmployeeJobReferenceDetails',
      {
        associate_id: associateId,
      }
    );
  }

  getProjectLocation() {
    return this._http.post(
      '/api/employee360/masterData/getProjectLocation',
      {}
    );
  }

  getProjectIndustryType() {
    return this._http.post(
      '/api/employee360/masterData/getProjectIndustryType',
      {}
    );
  }
  getProjectrole() {
    return this._http.post(
      '/api/employee360/masterData/getprojectrolemaster',
      {}
    );
  }

  getProjectDetailsCP(associateId) {
    return this._http.post(
      '/api/employee360/employeeDetails/getProjectDetailsCP',
      {
        associate_id: associateId,
      }
    );
  }

  getEmployeeIdentityProofDetails(associateId) {
    return this._http.post(
      '/api/employee360/profileDetails/getEmployeeIdentityProofDetails',
      {
        associate_id: associateId,
      }
    );
  }

  saveProjectDetailsCP(details) {
    return this._http.post(
      '/api/employee360/employeeDetails/saveProjectDetailsCP',
      {
        details: details,
      }
    );
  }

  getEmployeeEducationDetails(associateId) {
    return this._http.post(
      '/api/employee360/profileDetails/getEmployeeEducationDetails',
      {
        associate_id: associateId,
      }
    );
  }

  getReasonForAction(action_type) {
    return this._http.post('/api/employee360/masterData/getReasonForAction', {
      action_type: action_type,
    });
  }

  getSkillType() {
    return this._http.post('/api/employee360/masterData/getSkillType', {});
  }

  getSkillLevel() {
    return this._http.post('/api/employee360/masterData/getSkillLevel', {});
  }

  getSkillExperience() {
    return this._http.post(
      '/api/employee360/masterData/getSkillExperience',
      {}
    );
  }
  getSkillCategory() {
    return this._http.post(
      '/api/employee360/masterData/getSkillCategory',
      {}
    );
  }
  getSkillGroup() {
    return this._http.post(
      '/api/employee360/masterData/getSkillGroup',
      {}
    );
  }

  getExtraCurricularActivity() {
    return this._http.post(
      '/api/employee360/masterData/getExtraCurricularActivity',
      {}
    );
  }

  saveSkillDetails(details) {
    return this._http.post(
      '/api/employee360/employeeDetails/saveSkillDetailsCP',
      {
        details: details,
      }
    );
  }

  getEmployeeCertificationDetails(associateId) {
    return this._http.post(
      '/api/employee360/profileDetails/getEmployeeCertificationDetails',
      {
        associate_id: associateId,
      }
    );
  }

  getEmployeeSkillDetails(associateId) {
    return this._http.post(
      '/api/employee360/profileDetails/getEmployeeSkillDetails',
      {
        associate_id: associateId,
      }
    );
  }

  getEmployeeLanguagesKnownDetails(associateId) {
    return this._http.post(
      '/api/employee360/profileDetails/getEmployeeLanguagesKnownDetails',
      {
        associate_id: associateId,
      }
    );
  }

  getSkillDetails(associate_id,record_id?) {
    return this._http.post(
      '/api/employee360/employeeDetails/getSkillDetailsCP',
      {
        associate_id: associate_id,
        record_id:record_id?record_id:null
      }
    );
  }
  removeSkillRecord(data) {
    return this._http.post(
      '/api/employee360/employeeDetails/removeSkillRecord',
      {
       details:data
      }
    );
  }

  getEmployeeExtraCurricularActivityDetails(associateId) {
    return this._http.post(
      '/api/employee360/profileDetails/getEmployeeExtraCurricularActivityDetails',
      {
        associate_id: associateId,
      }
    );
  }

  getVaccinationTypes() {
    return this._http.post(
      '/api/employee360/masterData/getVaccinationTypes',
      {}
    );
  }

  getVaccinationDetails(associate_id) {
    return this._http.post(
      '/api/employee360/employeeDetails/getVaccinationDetails',
      {
        associate_id: associate_id,
      }
    );
  }

  getEmployeeLegalAndAchievementsDetails(associateId) {
    return this._http.post(
      '/api/employee360/profileDetails/getEmployeeLegalAndAchievementsDetails',
      {
        associate_id: associateId,
      }
    );
  }

  getEmployeeOrganisationDetails(associateId) {
    return this._http.post(
      '/api/employee360/organizationDetails/getEmployeeOrgDetails',
      {
        associate_id: associateId,
      }
    );
  }

  saveVaccinationDetailsCP(details) {
    return this._http.post(
      '/api/employee360/employeeDetails/saveVaccinationDetailsCP',
      {
        details: details,
      }
    );
  }

  getEmployeeManagerialDetails(associateId) {
    return this._http.post(
      '/api/employee360/organizationDetails/getEmployeeManagerialDetails',
      {
        associate_id: associateId,
      }
    );
  }

  getEmployeeVaccinationDetails(associateId) {
    return this._http.post(
      '/api/employee360/organizationDetails/getEmployeeVaccinationDetails',
      {
        associate_id: associateId,
      }
    );
  }

  // getTenantConfig(funcitonCode) {
  //   return this._http.post('/api/employee360/masterData/getTenantConfig', {
  //     function_code: funcitonCode,
  //   });
  // }

  fetchOutTime() {
    return this._http.post('/api/employee360/masterData/fetchOutTime', {});
  }

  fetchInTime() {
    return this._http.post('/api/employee360/masterData/fetchInTime', {});
  }

  getHolidayCalender() {
    return this._http.post(
      '/api/employee360/masterData/getHolidayCalender',
      {}
    );
  }

  getPrevJobDetailsCP(associateId) {
    return this._http.post(
      '/api/employee360/employeeDetails/getPrevJobDetailsCP',
      {
        associate_id: associateId,
      }
    );
  }

  savePrevJobDetails(details) {
    return this._http.post(
      '/api/employee360/employeeDetails/savePrevJobDetailsCP',
      {
        details: details,
      }
    );
  }

  saveIdentityDocumentsCP(details) {
    return this._http.post(
      '/api/employee360/employeeDetails/saveIdentityDocumentsCP',
      {
        details: details,
      }
    );
  }

  getEmployeeTimeDetails(associateId) {
    return this._http.post(
      '/api/employee360/organizationDetails/getEmployeeTimeDetails',
      {
        associate_id: associateId,
      }
    );
  }

  getIdentityDocumentDetailsCP(associateId) {
    return this._http.post(
      '/api/employee360/employeeDetails/getIdentityDocumentDetailsCP',
      {
        associate_id: associateId,
      }
    );
  }

  getSpecializationList(degree) {
    return this._http.post(
      '/api/employee360/masterData/getSpecializationList',
      {
        degree : degree
      }
    );
  }

  getDegreeList() {
    return this._http.post('/api/employee360/masterData/getDegreeList', {});
  }

  getEducationDetailsCP(associateId) {
    return this._http.post(
      '/api/employee360/employeeDetails/getEducationDetailsCP',
      {
        associate_id: associateId,
      }
    );
  }

  saveEducationDetailsCP(details) {
    return this._http.post(
      '/api/employee360/employeeDetails/saveEducationDetailsCP',
      {
        details: details,
      }
    );
  }

  getEmployeeProjectDetails(associateId) {
    return this._http.post(
      '/api/employee360/organizationDetails/getEmployeeProjectDetails',
      {
        associate_id: associateId,
      }
    );
  }
  getBgvVerifStatus() {
    return this._http.post('/api/employee360/masterData/getBgvVerifStatus', {});
  }

  getBgvClassification() {
    return this._http.post(
      '/api/employee360/masterData/getBgvClassification',
      {}
    );
  }

  getBackgroundVerificationDetailsCP(associateId) {
    return this._http.post(
      '/api/employee360/employeeDetails/getBackgroundVerificationDetailsCP',
      {
        associate_id: associateId,
      }
    );
  }

  saveBackgroundVerificationDetails(details) {
    return this._http.post(
      '/api/employee360/employeeDetails/saveBackgroundVerificationDetails',
      {
        details: details,
      }
    );
  }

  getEmployeeBackgroundVerificationDetails(associateId) {
    return this._http.post(
      '/api/employee360/organizationDetails/getEmployeeBackgroundVerificationDetails',
      {
        associate_id: associateId,
      }
    );
  }
  saveBenefitDetailsCP(details) {
    return this._http.post(
      '/api/employee360/employeeDetails/saveBenefitDetailsCP',
      {
        details: details,
      }
    );
  }

  getBenefitDetailsCP(associateId) {
    return this._http.post(
      '/api/employee360/employeeDetails/getBenefitDetailsCP',
      {
        associate_id: associateId,
      }
    );
  }

  getBenefitDependents() {
    return this._http.post(
      '/api/employee360/masterData/getBenefitDependents',
      {}
    );
  }

  getEmployeeBenefitsDetails(associateId) {
    return this._http.post(
      '/api/employee360/insuranceDetails/getEmployeeBenefitsDetails',
      {
        associate_id: associateId,
      }
    );
  }

  getAllRoleAccess() {
    let accessList = _.where(this.roleService.roles, { application_id: 323 });
    this.roleList = accessList;
    return accessList;
  }

  getAllRoleAccessForExit() {
    let accessList = _.where(this.roleService.roles, { application_id: 557 });
    this.roleList.push(...accessList);
    return accessList;
  }

  checkViewAndEditAccess(objectId) {
    console.log(this.roleList);
    let access = _.where(this.roleList, { object_id: objectId });
    if (access.length > 0) return true;
    else return false;
  }

  fetchEditHistory(associateId, tabKey) {
    return this._http.post('/api/employee360/employeeDetails/fetchEditLogs', {
      associate_id: associateId,
      tab_key: tabKey,
    });
  }

  getEmployeeLeaveDetails(associateId) {
    return this._http.post(
      '/api/employee360/organizationDetails/getEmployeeLeaveDetails',
      {
        associate_id: associateId,
      }
    );
  }

  getEmployeeLeaveBalanceDetails(associateId) {
    return this._http.post(
      '/api/employee360/organizationDetails/getEmployeeLeaveBalanceDetails',
      {
        associate_id: associateId,
      }
    );
  }

  getLeaveDetails(associateId,leaveId) {
    return this._http.post('/api/employee360/employeeDetails/getLeaveDetails', {
      associate_id: associateId,
      leave_id:leaveId
    });
  }

  saveLeaveDetails(details) {
    return this._http.post(
      '/api/employee360/employeeDetails/saveLeaveDetails',
      {
        details: details,
      }
    );
  }

  saveHrDocuments(details) {
    return this._http.post('/api/employee360/employeeDetails/saveHrDocuments', {
      details: details,
    });
  }

  getHrDocuments(associateId) {
    return this._http.post('/api/employee360/employeeDetails/getHrDocuments', {
      associate_id: associateId,
    });
  }

  getE360Config() {
    return this._http.post('/api/employee360/utilData/getE360Config', {});
  }
  getLeaveType() {
    return this._http.post('/api/employee360/masterData/getLeaveType', {});
  }

  getInitiateExitDetails(associateId) {
    return this._http.post(
      '/api/employee360/quickAction/getinitiateExitDetails',
      {
        associate_id: associateId,
      }
    );
  }

  initiateExitProcess(details) {
    return this._http.post('/api/employee360/quickAction/initiateExit', {
      details: details,
    });
  }

  getLeaveBalanceDetails(associateId) {
    return this._http.post(
      '/api/employee360/employeeDetails/getLeaveBalanceDetails',
      {
        associate_id: associateId,
      }
    );
  }

  executeInitiateTransfer(empDetails) {
    return this._http.post(
      '/api/employee360/quickAction/executeInitiateTransfer',
      {
        empDetails: empDetails,
      }
    );
  }

  saveLeaveBalanceDetails(details) {
    return this._http.post(
      '/api/employee360/employeeDetails/saveLeaveBalanceDetails',
      {
        details: details,
      }
    );
  }

  getInitiateTransferDetails(associateId) {
    return this._http.post(
      '/api/employee360/quickAction/getInitiateTransferDetails',
      {
        associate_id: associateId,
      }
    );
  }

  determineCostCenter(org_id) {
    return this._http.post(
      '/api/employee360/employeeDetails/determineCostCenter',
      {
        org_id: org_id,
      }
    );
  }

  getDateOfJoining(associateId) {
    return this._http.post(
      '/api/employee360/employeeDetails/getDateOfJoining',
      {
        associate_id: associateId,
      }
    );
  }

  getInitiateSystemAssignmentDetails(associateId) {
    return this._http.post(
      '/api/employee360/quickAction/getSystemAssignmentDetails',
      {
        associate_id: associateId,
      }
    );
  }

  executeInitiateSystemAssignment(empDetails) {
    return this._http.post(
      '/api/employee360/quickAction/initiateSystemAssignment',
      {
        details: empDetails,
      }
    );
  }

  getInitiateAdminAssignmentDetails(associateId) {
    return this._http.post(
      '/api/employee360/quickAction/getAdminAssignmentDetails',
      {
        associate_id: associateId,
      }
    );
  }

  executeInitiateAdminAssignment(empDetails) {
    return this._http.post(
      '/api/employee360/quickAction/initiateAdminAssignment',
      {
        details: empDetails,
      }
    );
  }

  getEmployeeStatusByAssociteId(associateId) {
    return this._http.post(
      '/api/employee360/employeeDetails/getEmployeeStatusByAssociteId',
      {
        associate_id: associateId,
      }
    );
  }

  getVisaTypes() {
    return this._http.post('/api/employee360/masterData/getVisaTypes', {});
  }

  getOrgRegion() {
    return this._http.post('/api/employee360/masterData/getOrgRegion', {});
  }

  getOrgLevel() {
    return this._http.post('/api/employee360/masterData/getOrgLevel', {});
  }

  getCountryList() {
    return this._http.post('/api/employee360/masterData/getCountryList', {});
  }

  getStatesOfCountry(country_id) {
    return this._http.post('/api/employee360/masterData/getStatesOfCountry', {
      country_id: country_id,
    });
  }

  getCitiesOfState(state_id) {
    return this._http.post('/api/employee360/masterData/getCitiesOfState', {
      state_id: state_id,
    });
  }

  getFieldTenantConfig(tab_key) {
    return this._http.post('/api/employee360/employeeDetails/getFieldTenantConfig', {
      tab_key: tab_key
    });
  }
  getFieldLableTenantConfig(tab_key) {
    return this._http.post('/api/employee360/employeeDetails/getFieldLableTenantConfig', {
      tab_key: tab_key
    });
  }

  getLeaveTypeBasedOnassociateGroup(associateId) {
    return this._http.post(
      '/api/employee360/employeeDetails/getLeaveTypeBasedOnAssociateGroup',
      {
        associate_id: associateId,
      }
    );
  }

  getSapActivateMaster() {
    return this._http.post('/api/employee360/masterData/getSapActivateMaster', {
    });
  }

  getSkillName(skill_type_id) {
    return this._http.post('/api/employee360/masterData/getSkillName', {
      skill_type_id: skill_type_id,
    });
  }

  getBgvReportStatus() {
    return this._http.post('/api/employee360/masterData/getBgvReportStatus', {
    });
  }

  getAwaitingApprovalsForInbox(skip,limit) {
    return this._http.post('/api/employee360/approvalDetail/getApproverPendingItems', {
      skip: skip,
      limit: limit
    });
  }

  getPreviousApprovalsForInbox(skip,limit) {
    return this._http.post('/api/employee360/approvalDetail/getApprovalHistoryItems', {
      skip: skip,
      limit: limit
    });
  }

  approveOrRejectRequest(params) {
    return this._http.post('/api/employee360/approvalDetail/triggerApprovalAction',params);
  }
  checkUserAccess(aid){
    return this._http.post('/api/employee360/auth/checkUserAccess', {
      aid : aid
    });
  }
  checkWorkflowStatus(associate_id,tab_key){
    return this._http.post('/api/employee360/approvalDetail/getWfApprovalStatus',{
      "tab_key":tab_key,
      "associate_id":associate_id
    });
  }

  getTermsAndConditionDetails(){
    return this._http.post('/api/employee360/profileDetails/getTermsPoliciesDetail', {
    });
  }

  agreeTermsAndCondition(params){
    return this._http.post('/api/employee360/profileDetails/submitTermsPoliciesAgreement',params);
  }

  checkTermsAndConditionStatus(params){
    return this._http.post('/api/employee360/profileDetails/hasAgreedTermsPolicies',params);
  }

  validateProfile(params){
    return this._http.post('/api/employee360/profileDetails/executeProfileValidation',params);
  }

  checkValidateProfileStatus(params){
    return this._http.post('/api/employee360/profileDetails/hasProfileValidatedByUser',params);
  }

  // declineTermsAndCondition(){
  //   return this._http.post('/api/employee360/approvalDetail/getApproverPendingItems', {
  //   });
  // }

  getProjPracticeList() {
    return this._http.get('api/employee360/masterData/getProjPracticeList',{})
  }
  
  getProbationDuration() {
    return this._http.post('/api/employee360/masterData/getProbationDuration', {});
  }

  validateAdminDetailViewAccess() {
    return this._http.post('/api/employee360/profileDetails/validateAdminDetailViewAccess',{});
  }
  
  getInitiateEmpConfirmationDetails(associateId) {
    return this._http.post(
      '/api/employee360/quickAction/getInitiateEmployeeConfirmationDetails',
      {
        associate_id: associateId,
      }
    );
  }


  executeInitiateEmpConfirmation(empDetails) {
    return this._http.post(
      '/api/employee360/quickAction/initiateEmployeeConfirmation',
      {
        empDetails: empDetails,
      }
    );
  }

  getDefaultLeaveDates(){
    return this._http.post(
      '/api/employee360/employeeDetails/getEmployeeDefaultLeaveDates',
      {
      }
    );
  }

  getLeaveBalanceQuotaForLeaveTypes(associate_id,leave_type){
    return this._http.post(
      '/api/employee360/employeeDetails/getEmployeeLeaveBalanceBasedOnAssociateId',
      {
        associate_id:associate_id,
        leave_type : leave_type
      }
    );
  }

  getInitiateEmpPromotionDetails(associateId) {
    return this._http.post(
      '/api/employee360/quickAction/getInitiateEmpPromotionDetails',
      {
        associate_id: associateId,
      }
    );
  }


  executeInitiateEmpPromotion(empDetails) {
    return this._http.post(
      '/api/employee360/quickAction/executeInitiateEmpPromotion',
      {
        empDetails: empDetails,
      }
    );
  }

  getInitiateInactiveDetails(associateId) {
    return this._http.post(
      '/api/employee360/quickAction/getInitiateInactiveDetails',
      {
        associate_id: associateId,
      }
    );
  }

  initiateInactiveProcess(details) {
    return this._http.post('/api/employee360/quickAction/initiateInactiveProcess', {
      details: details,
    });
  }

  
  getInitiateActiveDetails(associateId) {
    return this._http.post(
      '/api/employee360/quickAction/getInitiateActiveDetails',
      {
        associate_id: associateId,
      }
    );
  }
  initiateActiveProcess(details) {
    return this._http.post('/api/employee360/quickAction/initiateActiveProcess', {
      details: details,
    });
  }

  getInitiateProbationExtensionDetails(associateId) {
    return this._http.post(
      '/api/employee360/quickAction/getInitiateProbationExtensionDetails',
      {
        associate_id: associateId,
      }
    );
  }

  initiateProationExtensionProcess(details) {
    return this._http.post('/api/employee360/quickAction/initiateProationExtensionProcess', {
      details: details,
    });
  }

  getPendingApprovalsForEmployeeAID(associate_id,tab_key) {
    return this._http.post('/api/employee360/approvalDetail/getPendingApprovalsForEmployeeAID', {
      associate_id : associate_id,
      tab_key : tab_key
    });
  }

  getOIDOfEmployee(associate_id){
    return this._http.post('/api/employee360/employeeDetails/getOIDOfEmployee', {
      associate_id: associate_id,
    });
  }

  checkLeaveBalance(empOid,leave_start_date,leave_end_date){
    return this._http.post('/api/leaveapp/leavePrimary/checkIfLeavesAllowed', {
      oid: empOid,
      leave_start_date: leave_start_date,
      leave_end_date: leave_end_date
    });
  }

  getLanguagesList() {
    return this._http.post('/api/employee360/masterData/getLanguagesList', {});
  }

  getECReportData(bodyParams) {
    return this._http.post(
      '/api/employee360/ecReport/getECReportData',
      {
        filterConfig: bodyParams,
      }
    );
  }

  getECReportMasterTypes() {
    return this._http.post('/api/employee360/masterData/getECReportMasterTypes', {});
  }
  getLeaveReportMaster() {
    return this._http.post('/api/employee360/masterData/getLeaveReportMaster', {});
  }

  validateWorkEmailAddress(emailAddress,associateId) {
    return this._http.post('/api/employee360/profileDetails/validateWorkEmailAddress', {
      email : emailAddress,
      associate_id: associateId
    })
  }

  checkIfUserHasAccessForPayrollUpdation(associateId, oid) {
    console.log(associateId);
    console.log(oid);
    return this._http.post('/api/payrollapp/payrollPrimary/payrollUserAuthenication',{
      associateId: associateId,
      oid: oid
    });
  }

  getPayrollMasterDetails() {
    return this._http.post('/api/payrollapp/payrollPrimary/getPayrollMasterData',{
    });
  }

  getEmployeeDetails(associateId, oid) {
    return this._http.post('/api/payrollapp/payrollPrimary/getEmployeeDetails',{
      associateId: associateId,
      oid: oid
    });
  }

  getPayscaleSplit(data) {
    return this._http.post('/api/payrollapp/payrollPrimary/calculatePayrollSplitUp',{
      payrollCalculationInput: data
    });
  }

  updatePayrollMasterData(payrollData, ops) {
    return this._http.post('/api/payrollapp/payrollPrimary/updateTheEmployeeCtcDetails',{
      payrollData: payrollData,
      operation: ops
    });
  }

  getEmployeePayscaleDetails(associateId){
    return this._http.post('/api/payrollapp/payrollPrimary/getEmployeePayscaleDetails',{
      associateId: associateId
    });
  }

  getEmployeeViewPayscaleDetails(associateId) {
    return this._http.post('/api/payrollapp/payrollPrimary/getEmployeeViewPayscaleDetails',{
      associateId: associateId,
    });
  }
  
  getinitiateContractExtensionDetails(associateId) {
    return this._http.post(
      '/api/employee360/quickAction/getinitiateContractExtensionDetails',
      {
        associate_id: associateId,
      }
    );
  }

  initiateContractExtensionProcess(details) {
    return this._http.post('/api/employee360/quickAction/initiateContractExtensionProcess', {
      details: details,
    });
  }

  getEmployeeAwardDetails(oid) {
    return this._http.post(
      '/api/employee360/organizationDetails/getEmployeeAwardsDetails',
      {
        oid: oid,
      }
    );
  }
  
  fetchResult = (apiUri, bodyParams) => {
    return this._http.post(apiUri, bodyParams)
  }

  getAwardsImageDetails() {
    return this._http.post(
      '/api/employee360/organizationDetails/getAwardsImageDetails',
      {
      }
    );
  }

  getDefaultLeaveIds() {
    return this._http.post('/api/employee360/profileDetails/getDefaultLeaveIds', {
      
    })
  }


  getAssociateIdConfig() {
    return this._http.post('/api/employee360/masterData/getAssociateIdConfig', {});
  }

  getPayrollOnsiteConfig(oid, associateId){
    return this._http.post('/api/payrollapp/payrollPrimary/getPayrollOnsiteConfig', {
      oid: oid,
      associateId: associateId
    })
  }
  getAssetsCategory() {
    return this._http.post('/api/employee360/masterData/getAssetsCategory', {});
  } 
  checkBssUserRole(params) {
    return this._http.post('/api/employee360/masterData/checkBssUserRole', {
      param:params
    });
  }
  getWorkClassification() {
    return this._http.post('/api/employee360/masterData/getWorkClassification', {});
  }
  getEmployeeDelegationDetails(associateId) {
    return this._http.post(
      '/api/employee360/organizationDetails/getEmployeeDelegationDetails',
      {
        associate_id: associateId,
      }
    );
  }

  
}



