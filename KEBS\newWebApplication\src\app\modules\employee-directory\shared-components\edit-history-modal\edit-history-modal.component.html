<div class="container-fluid edit-history-modal px-4">
  <ng-container *ngIf="isLoading">
    <div class="d-flex justify-content-center mt-3">
      <mat-spinner matTooltip="Please wait..." diameter="30"> </mat-spinner>
    </div>
  </ng-container>

  <ng-container *ngIf="!isLoading">
    <div class="d-flex justify-content-between my-4">
      <div class="main-header">Edit History</div>
      <div class="close-btn" (click)="closeDialog()">close</div>
    </div>

    <div class="mt-4" *ngIf="editHistoryData.length == 0">
      <div class="d-flex justify-content-center">
        <img
          src="https://assets.kebs.app/images/empty_re_opql.png"
          class="mt-2"
          height="150"
          width="200"
        />
      </div>
      <div class="d-flex mt-4 justify-content-center">
        <span style="font-weight: 500; font-size: 16px; color: #26303e"
          >No Edit Logs Found !</span
        >
      </div>
    </div>

    <div class="row" *ngFor="let e of editHistoryData; let i = index">
      <div class="col-6 py-4" style="border-left: 1px solid #e8e9ee">
        <div class="row date-txt">
          {{ e.updated_on ? (e.updated_on | date: 'd MMM yyyy, h:mm:ss a' : 'UTC') : '-' }}
        </div>
        <div class="row action-txt">
          {{ e.action_text ? e.action_text : "-" }}
        </div>
        <div class="row action-txt updated_field_list">

          <ul>
            <li *ngFor="let field of e.edited_fields">{{ field | camelCaseToFriendly}}</li>
          </ul>
        </div>
      </div>
      <div class="col-6 py-4" style="border-bottom: 1px solid #dadce2">
        <div class="row mb-1" style="color: #111434; font-size: 14px">
          Action By
        </div>
        <div class="row">
          <app-user-profile type="small" [oid]="e.action_by">
          </app-user-profile>
        </div>
      </div>
    </div>
  </ng-container>
</div>
