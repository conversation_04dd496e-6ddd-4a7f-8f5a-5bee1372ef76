<div *ngIf="!external" class="dialog">
  <div class="align-items-btw" style="margin-bottom: 8px">
    <div class="dialog-title">Summary</div>
    <div class="icon">
      <mat-icon class="icon" (click)="onDialogClose()">clear</mat-icon>
    </div>
  </div>
  <div class="sub-text">Head Count Of Employee {{ data.columnData.length }}</div>
  
  <dx-data-grid
    class="data-grid"
    [dataSource]="data.columnData"
    [showBorders]="true"
    [showColumnLines]="true"
    [showRowLines]="false"
    [allowColumnResizing]="true"
    [columnAutoWidth]="true"
    height="400"
  >
    <dxo-header-filter [visible]="true" [alignment]="'right'"></dxo-header-filter>
    <dxo-paging pageSize="10"></dxo-paging>
    <dxo-search-panel [visible]="true" width="250"></dxo-search-panel>
    <dxo-export [enabled]="true"></dxo-export>

    <dxi-column dataField="associate_id" caption="Associate Id"></dxi-column>
    <dxi-column dataField="employee_name" caption="Employee Name"></dxi-column>
    <dxi-column dataField="entity_name" caption="Entity"></dxi-column>
    <dxi-column dataField="division_name" caption="Division"></dxi-column>
    <dxi-column dataField="sub_division_name" caption="Sub Division"></dxi-column>
    <dxi-column dataField="Date_Of_Exit" caption="Date Of Exit" [dataType]="'date'" [visible]="isDateVisible1()"></dxi-column>
    <dxi-column dataField="SType" caption="Type" [visible]="isColumnVisible1()"></dxi-column>
    <dxi-column dataField="Type" caption="{{ getCaption() }}" [visible]="isColumnVisible()"></dxi-column>
    <dxi-column dataField="gender_name" caption="Gender Name" [visible]="isGenderVisible()"></dxi-column>
    <dxi-column dataField="status" caption="Allocation Status" [visible]="isStatusVisible()"></dxi-column>
  </dx-data-grid>
</div>

<div *ngIf="external" class="dialog">
  <div class="align-items-btw" style="margin-bottom: 8px">
    <div class="dialog-title">Project Details</div>
    <div class="icon">
      <mat-icon class="icon" (click)="onDialogClose()">clear</mat-icon>
    </div>
  </div>  
  <dx-data-grid
    class="data-grid"
    [dataSource]="data.columnData"
    [showBorders]="true"
    [showColumnLines]="true"
    [showRowLines]="false"
    [allowColumnResizing]="true"
    [columnAutoWidth]="true"
    height="400"
  >
    <dxo-header-filter [visible]="true" [alignment]="'right'"></dxo-header-filter>
    <dxo-paging pageSize="10"></dxo-paging>
    <dxo-search-panel [visible]="true" width="250"></dxo-search-panel>
    <dxo-export [enabled]="true" fileName="project_details_export"></dxo-export>

    <dxi-column dataField="start_date" caption="Start Date" dataType="date" 
            format="dd-MMM-yyyy"></dxi-column>
<dxi-column dataField="end_date" caption="End Date" dataType="date" 
            format="dd-MMM-yyyy"></dxi-column>
    <dxi-column dataField="project_name" caption="Project Name"></dxi-column>
    <dxi-column dataField="industry_type" caption="Industry Type"></dxi-column>
    <dxi-column dataField="cost_center" caption="Cost Center"></dxi-column>
    <dxi-column dataField="projectType" caption="Project Type"></dxi-column>
    <dxi-column dataField="clientName" caption="Client Name"></dxi-column>
    <dxi-column dataField="billable" caption="Billable" dataType="boolean"></dxi-column>
    <dxi-column dataField="incentive" caption="Incentive"></dxi-column>
    <dxi-column dataField="project_role" caption="Project Role"></dxi-column>
    <dxi-column dataField="pmo" caption="PMO"></dxi-column>
    <dxi-column dataField="lineManager" caption="Project Manager"></dxi-column>
    <dxi-column dataField="split_percentage" caption="Split Percentage" dataType="number"></dxi-column>
  </dx-data-grid>
</div>
