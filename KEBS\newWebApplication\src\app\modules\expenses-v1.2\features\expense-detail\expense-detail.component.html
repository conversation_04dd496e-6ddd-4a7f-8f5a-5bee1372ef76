<div class="container-fluid expense-details-styles pl-0 pr-0" *ngIf="expenseDetail">
  <div class="row">
    <!-- Expense details column  -->
    <div class="col-3 pl-0 slide-in-right">
      <div class="card expense-details-card">
        <div class="card-body p-0">
          <!-- Header details  -->
          <div class="pt-2 pb-2 pl-3 pr-0" [ngClass]="{
              'is-submitted-bg': expenseDetail.status == 'Submitted',
              'is-approved-bg': expenseDetail.status == 'Approved',
              'is-verified-bg': expenseDetail.status == 'Verified',
              'is-rejected-bg': expenseDetail.status == 'Rejected',
              'is-payed-bg': expenseDetail.status == 'Closed'
            }">
            <div class="row pt-1">
              <div class="col-9 p-0">
                <div class="header-start" (click)="routeExpenseHome()" style="cursor: pointer;">
                  <div class="mt-1 back-icon">
                    <mat-icon class="icn-class">navigate_before</mat-icon>
                  </div>
                  <span class="header-text">Back</span>
                </div>
              </div>
              <div class="col-3">
                <span *ngIf="expenseDetail.expense_type === 'C' " class="pl-1">
                  <span *ngIf="expenseDetail.is_billable == 1" class="pl-1 d-flex">
                    <span *ngFor="let entity of billingConfig" class="expense-bill-item-button my-auto non-billable-icon">
                      <span *ngIf="expenseDetail.is_billable == entity.id" class="expense-item-button-icon"    
                      style="line-height: 15px; font-size: 20px !important; vertical-align: text-top;" 
                      [matTooltip]="entity.billing_type ">
                      <svg width="22" height="21" viewBox="0 0 22 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                       <mask id="mask0_8286_37841" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="22" height="21">
                       <rect x="0.380859" width="18" height="18" fill="#D9D9D9"/>
                       </mask>
                       <g mask="url(#mask0_8286_37841)">
                       <path d="M9.00874 8.62488C8.23494 8.62488 7.57252 8.34949 7.02148 7.79871C6.47044 7.24792 6.19492 6.58582 6.19492 5.81238C6.19492 5.03894 6.47044 4.37683 7.02148 3.82605C7.57252 3.27527 8.23494 2.99988 9.00874 2.99988C9.78254 2.99988 10.445 3.27527 10.996 3.82605C11.547 4.37683 11.8226 5.03894 11.8226 5.81238C11.8226 6.58582 11.547 7.24792 10.996 7.79871C10.445 8.34949 9.78254 8.62488 9.00874 8.62488ZM3.3811 14.2499V12.2811C3.3811 11.8827 3.48369 11.5165 3.68886 11.1825C3.89404 10.8485 4.16663 10.5936 4.50663 10.4178C5.23353 10.0546 5.97216 9.7821 6.72251 9.60046C7.47286 9.41882 8.23494 9.328 9.00874 9.328C9.78254 9.328 10.5446 9.41882 11.295 9.60046C12.0453 9.7821 12.7839 10.0546 13.5108 10.4178C13.8509 10.5936 14.1234 10.8485 14.3286 11.1825C14.5338 11.5165 14.6364 11.8827 14.6364 12.2811V14.2499H3.3811ZM4.78801 12.8436H13.2295V12.2811C13.2295 12.1522 13.1972 12.035 13.1327 11.9296C13.0683 11.8241 12.9833 11.7421 12.8777 11.6835C12.2446 11.3671 11.6057 11.1298 10.9608 10.9716C10.316 10.8134 9.6653 10.7343 9.00874 10.7343C8.35218 10.7343 7.70149 10.8134 7.05665 10.9716C6.41182 11.1298 5.77285 11.3671 5.13974 11.6835C5.03422 11.7421 4.94922 11.8241 4.88474 11.9296C4.82025 12.035 4.78801 12.1522 4.78801 12.2811V12.8436ZM9.00874 7.21863C9.39564 7.21863 9.72685 7.08093 10.0024 6.80554C10.2779 6.53015 10.4156 6.1991 10.4156 5.81238C10.4156 5.42566 10.2779 5.0946 10.0024 4.81921C9.72685 4.54382 9.39564 4.40613 9.00874 4.40613C8.62184 4.40613 8.29063 4.54382 8.01511 4.81921C7.73959 5.0946 7.60183 5.42566 7.60183 5.81238C7.60183 6.1991 7.73959 6.53015 8.01511 6.80554C8.29063 7.08093 8.62184 7.21863 9.00874 7.21863Z" fill="#1890FF"/>
                       <path d="M18.2193 9.07019L16.2074 11.0811C16.1511 11.1374 16.0878 11.1796 16.0175 11.2077C15.9471 11.2358 15.8768 11.2499 15.8065 11.2499C15.7361 11.2499 15.6658 11.2358 15.5954 11.2077C15.5251 11.1796 15.4618 11.1374 15.4055 11.0811L12.9223 8.5991C12.8707 8.54753 12.8308 8.48777 12.8027 8.4198C12.7746 8.35183 12.7605 8.28035 12.7605 8.20535V6.18738C12.7605 6.03269 12.8156 5.90027 12.9258 5.79011C13.036 5.67996 13.1685 5.62488 13.3233 5.62488H15.3422C15.4172 5.62488 15.4899 5.64011 15.5602 5.67058C15.6306 5.70105 15.6916 5.74207 15.7431 5.79363L18.2193 8.27566C18.2756 8.33191 18.3166 8.39519 18.3424 8.4655C18.3682 8.53582 18.3811 8.60613 18.3811 8.67644C18.3811 8.74675 18.3682 8.81589 18.3424 8.88386C18.3166 8.95183 18.2756 9.01394 18.2193 9.07019ZM14.0267 7.31238C14.144 7.31238 14.2436 7.27136 14.3257 7.18933C14.4078 7.1073 14.4488 7.00769 14.4488 6.8905C14.4488 6.77332 14.4078 6.67371 14.3257 6.59167C14.2436 6.50964 14.144 6.46863 14.0267 6.46863C13.9095 6.46863 13.8098 6.50964 13.7277 6.59167C13.6457 6.67371 13.6046 6.77332 13.6046 6.8905C13.6046 7.00769 13.6457 7.1073 13.7277 7.18933C13.8098 7.27136 13.9095 7.31238 14.0267 7.31238Z" fill="#1890FF"/>
                       </g>
                       </svg>
                       
                     </span>
                    </span>
                    <button *ngIf="expenseDetail.role == 'Approver' && !expenseDetail.approvalHistory && !expenseDetail.adminView " class="expense-bill-item-button my-auto" mat-icon-button>
                      <mat-icon class="mt-2 expense-item-button-icon" [matMenuTriggerFor]="billingMenu"
                        style="line-height: 1px">{{expandBillable ? "keyboard_arrow_up" : "keyboard_arrow_down"}}</mat-icon>
                      <mat-menu #billingMenu="matMenu">
                        <button mat-menu-item *ngFor="let entity of billingConfig"
                          (click)="setCustomerBilling(entity.id)">{{entity.billing_type}}</button>
                      </mat-menu>
                    </button>
                  </span>
                  <span *ngIf="expenseDetail.is_billable == 2" class="pl-1 d-flex">
                    <span *ngFor="let entity of billingConfig" class="expense-bill-item-button my-auto sow-billable-icon">
                      <span *ngIf="expenseDetail.is_billable == entity.id" class="expense-item-button-icon"    
                        style="line-height: 15px; font-size: 20px !important; vertical-align: text-top;" 
                        [matTooltip]="entity.billing_type ">
                      <svg width="19" height="18" viewBox="0 0 19 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <mask id="mask0_8286_38013" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="19" height="18">
                          <rect x="0.380859" width="18" height="18" fill="#D9D9D9" />
                        </mask>
                        <g mask="url(#mask0_8286_38013)">
                          <path
                            d="M16.4306 10.6876L11.0681 16.0501C10.9181 16.2001 10.7494 16.3126 10.5619 16.3876C10.3744 16.4626 10.1869 16.5001 9.99936 16.5001C9.81186 16.5001 9.62436 16.4626 9.43686 16.3876C9.24936 16.3126 9.08062 16.2001 8.93061 16.0501L2.31187 9.43137C2.17437 9.29387 2.06812 9.1345 1.99312 8.95325C1.91812 8.772 1.88062 8.58137 1.88062 8.38137V3.00012C1.88062 2.58762 2.02749 2.2345 2.32124 1.94075C2.61499 1.647 2.96812 1.50012 3.38062 1.50012H8.76186C8.96186 1.50012 9.15561 1.54075 9.34311 1.622C9.53061 1.70325 9.69311 1.81262 9.83062 1.95012L16.4306 8.56887C16.5806 8.71887 16.69 8.88762 16.7587 9.07512C16.8275 9.26262 16.8619 9.45012 16.8619 9.63762C16.8619 9.82512 16.8275 10.0095 16.7587 10.1907C16.69 10.372 16.5806 10.5376 16.4306 10.6876ZM5.25561 6.00012C5.56811 6.00012 5.83374 5.89075 6.05249 5.672C6.27124 5.45325 6.38061 5.18762 6.38061 4.87512C6.38061 4.56262 6.27124 4.297 6.05249 4.07825C5.83374 3.8595 5.56811 3.75012 5.25561 3.75012C4.94311 3.75012 4.67749 3.8595 4.45874 4.07825C4.23999 4.297 4.13061 4.56262 4.13061 4.87512C4.13061 5.18762 4.23999 5.45325 4.45874 5.672C4.67749 5.89075 4.94311 6.00012 5.25561 6.00012Z"
                            fill="#526179" />
                        </g>
                      </svg>
                    </span> 
                    </span>
                    <button *ngIf="expenseDetail.role == 'Approver' && !expenseDetail.approvalHistory && !expenseDetail.adminView " class="expense-bill-item-button my-auto" mat-icon-button>
                      <mat-icon class="mt-2 expense-item-button-icon" [matMenuTriggerFor]="billingMenu"
                        style="line-height: 1px">{{expandBillable ? "keyboard_arrow_up" : "keyboard_arrow_down"}}</mat-icon>
                      <mat-menu #billingMenu="matMenu">
                        <button mat-menu-item *ngFor="let entity of billingConfig"
                          (click)="setCustomerBilling(entity.id)">{{entity.billing_type}}</button>
                      </mat-menu>
                    </button>
                  </span>
                  <span *ngIf="expenseDetail.is_billable==3" class="pl-1">
                    <!-- <span *ngFor="let entity of billingConfig">
                      <img src="https://assets.kebs.app/images/non billable.svg" style="width:20px !important"
                        *ngIf="expenseDetail.is_billable == entity.id && expenseDetail.role != 'Approver'" [matTooltip]="entity.billing_type " />
                    </span> -->
                    <img src="https://assets.kebs.app/images/non billable.svg" class="pt-1" style="width:20px !important"
                      *ngIf="billingConfigIdIndex && !billingConfigIdIndex[expenseDetail?.is_billable]" matTooltip="Customer Billing - Not Applicable"/>
                    <!-- <img src="https://assets.kebs.app/images/non billable.svg" class="pt-1" style="width:20px !important"
                    *ngIf="billingConfigIdIndex && !billingConfigIdIndex[expenseDetail?.is_billable] 
                    && expenseDetail?.role == 'Approver'" matTooltip="Customer Billing - Not Applicable"> -->

                    <!-- <img src="https://assets.kebs.app/images/non billable.svg" class="pt-1" style="width:20px !important"
                    *ngIf="expenseDetail.is_billable == 3 && expenseDetail.role == 'Approver'" matTooltip="Customer Billing" /> -->
                    <!-- <button *ngIf="expenseDetail.role == 'Approver' && !expenseDetail.approvalHistory && !expenseDetail.adminView " class="expense-bill-item-button my-auto" mat-icon-button>
                      <mat-icon class="mt-2 expense-item-button-icon" [matMenuTriggerFor]="billingMenu"
                        style="line-height: 12px; font-size: 20px;">{{expandBillable ? "keyboard_arrow_up" : "keyboard_arrow_down"}}</mat-icon>
                      <mat-menu #billingMenu="matMenu">
                        <button mat-menu-item *ngFor="let entity of billingConfig"
                          (click)="setCustomerBilling(entity.id)">{{entity.billing_type}}</button>
                      </mat-menu>
                    </button> -->
                  </span>
                </span>
              </div>
            </div>
        

            <div *ngIf="fieldConfig?.expenseTag?.field_label" class="row pt-3">
              <div class="col-4 d-flex pl-0">
                <span class="heading my-auto" [matTooltip]="fieldConfig?.expenseTag?.field_label ? fieldConfig.expenseTag.field_label :'Expense Tag'">
                  {{fieldConfig?.expenseTag?.field_label ? fieldConfig.expenseTag.field_label :'Expense Tag'}}
                </span>
              </div>
              <div *ngIf="(expenseDetail.role == 'Approver' || (expenseDetail.role == 'Treasurer' && expenseDetail.status == 'Approved')) && !expenseDetail.approvalHistory && !expenseDetail.adminView" class="col-8 d-flex pl-2 pr-0">
                <button mat-button (click)="expenseTagMenu.toggle()" class="content pl-0 my-auto" style="width:20px !important;"
                  style="text-decoration: underline; color:#40488f;cursor: pointer;" [satPopoverAnchor]="expenseTagMenu"
                  matTooltip="{{  expenseDetail.expense_tag }}">
                  {{ expenseDetail.expense_tag }}
                </button>
                <!-- <span class="content my-auto" style="text-decoration: underline; color:blue;cursor: pointer;"
                (click)="expenseTagMenu.toggle()" [satPopoverAnchor]="expenseTagMenu">
                  {{ expenseDetail.expense_tag }}
                </span> -->
                <sat-popover #expenseTagMenu horizontalAlign="center" verticalAlign="below" hasBackdrop>
                  <div style="max-height: 280px; width: 250px; background: #FFFFFF; overflow-y: auto; overflow-x: hidden; ">
                    <div style="color:#1F2347; font-weight: 600;" class="pt-2 pl-3">{{fieldConfig?.expenseTag?.field_label ? fieldConfig?.expenseTag?.field_label : "Expense Tag"}}</div>
                    <div class="d-flex ml-2 mr-2 m-2" style="height: 35px; border: 3px solid rgb(160, 160, 160); border-radius: 8px;">
                    <input
                      class="mt-1 ml-1"
                      matInput
                      placeholder="Search"
                      value="Search"
                      [formControl]="expenseTag"
                      (input)="searchExpenseTag()"
                    />
                    <mat-icon class="mt-1 mr-2" style="color:#a5a4a4">search</mat-icon>
                    </div>
                    <button mat-menu-item *ngFor="let entity of expenseTagList" (click)="fetchReportData(expenseTagMenu); setExpenseTag(entity.id, entity.name, expenseDetail.expense_header_id)">{{entity.name}}</button>
                  </div>
                </sat-popover>
              </div>
              
              <div *ngIf="!( (expenseDetail.role === 'Approver' || (expenseDetail.role === 'Treasurer' && expenseDetail.status === 'Approved')) 
              && !expenseDetail.approvalHistory && !expenseDetail.adminView )" 
     class="col-8 d-flex pl-2 pr-0">
                <span class="content my-auto" matTooltip="  {{ expenseDetail.expense_tag }}">
                  {{ expenseDetail.expense_tag }}
                </span>
              </div>
            </div>

            <!-- cost center section -->
            <div *ngIf="expenseDetail.role == 'Approver' " class="row pt-2">
              <div class="col-4 d-flex pl-0">
                <span class="heading my-auto" [matTooltip]="fieldConfig?.costCenterCode?.field_label ? fieldConfig.costCenterCode.field_label :'Cost Center'">
                  {{fieldConfig?.costCenterCode?.field_label ? fieldConfig.costCenterCode.field_label :'Cost Center'}}
                </span>
              </div>
              <div class="col-8 d-flex pl-2 pr-0">
                <span class="content my-auto" matTooltip="{{ expenseDetail.cost_centre }}-{{
                  expenseDetail.cost_centre_description
                }}">
                  {{ expenseDetail.cost_centre }}-{{
                    expenseDetail.cost_centre_description
                  }}
                </span>
              </div>
            </div>
            <div *ngIf="expenseDetail.role != 'Approver' " class="row pt-3">
              <div class="col-4 d-flex pl-0">
                <span class="heading my-auto" [matTooltip]="fieldConfig?.costCenterCode?.field_label ? fieldConfig.costCenterCode.field_label :'Cost Center'">
                  {{fieldConfig?.costCenterCode?.field_label ? fieldConfig.costCenterCode.field_label :'Cost Center'}}
                </span>
              </div>
              <div class="col-8 d-flex pl-2 pr-0">
                <span class="content my-auto" matTooltip="{{ expenseDetail.cost_centre }}-{{
                  expenseDetail.cost_centre_description
                }}">
                  {{ expenseDetail.cost_centre }}-{{
                    expenseDetail.cost_centre_description
                  }}
                </span>
              </div>
            </div>
            <!--  End of cost center section -->

                        <!-- Legal Entity section -->
                        <div class="row pt-3">
                          <div class="col-4 d-flex pl-0">
                            <span class="heading my-auto" [matTooltip]="fieldConfig?.legalEntityCode?.field_label ? fieldConfig.legalEntityCode.field_label:'Legal Entity'">
                            {{fieldConfig?.legalEntityCode?.field_label ? fieldConfig.legalEntityCode.field_label:'Legal Entity'}}
                            </span>
                          </div>
                          <div class="col-8 d-flex pl-2 pr-0">
                            <span class="content my-auto" [matTooltip]="expenseDetail?.entity_name">
                              {{ expenseDetail.entity_name }}
                            </span>
                          </div>
                        </div>
                        <!--  End of Legal Entity section -->

                        <div class="row pt-3">
                          <div class="col-4 d-flex pl-0">
                            <span class="heading my-auto" [matTooltip]="fieldConfig?.headerDescription?.field_label ? fieldConfig.headerDescription?.field_label:'Description'">
                            {{fieldConfig?.headerDescription?.field_label ? fieldConfig.headerDescription?.field_label:'Description'}}
                            </span>
                          </div>
                          <div class="col-8 d-flex pl-2 pr-0">
                            <span class="content my-auto" [matTooltip]="expenseDetail?.description">
                              {{ expenseDetail.description }}
                            </span>
                          </div>
                        </div>

                        <div class="row pt-3" *ngIf = "expenseDetail.spent_from">
                          <div class="col-4 d-flex pl-0">
                            <span class="heading my-auto" [matTooltip]="fieldConfig?.spentFrom?.field_label ? fieldConfig.spentFrom.field_label:'Spent From'">
                              {{fieldConfig?.spentFrom?.field_label ? fieldConfig.spentFrom?.field_label:'Corporate Card'}}
                            </span>
                          </div>
                          <div class="col-8 d-flex pl-2 pr-0">
                            <span class="content my-auto" [matTooltip]="expenseDetail?.spent_from">
                              {{ expenseDetail.spent_from }}
                            </span>
                          </div>
                        </div>

                        <div class="row pt-3" *ngIf = "expenseDetail.corporate_card_name">
                          <div class="col-4 d-flex pl-0">
                            <span class="heading my-auto" [matTooltip]="fieldConfig?.corporateCard?.field_label ? fieldConfig.corporateCard.field_label:'Corporate Card'">
                              {{fieldConfig?.corporateCard?.field_label ? fieldConfig.corporateCard?.field_label:'Corporate Card'}}
                            </span>
                          </div>
                          <div class="col-8 d-flex pl-2 pr-0">
                            <span class="content my-auto" [matTooltip]="expenseDetail?.corporate_card_name">
                              {{ expenseDetail.corporate_card_name }}
                            </span>
                          </div>
                        </div>

            <!-- Approvers section -->

            
            <!-- Cost Center Approvers section -->
            <div class="row pt-3">
              <div class="col-4 d-flex pl-0">
                <span class="heading my-auto">
                 Approvers
                </span>
              </div>
              <div class="col-8 d-flex pl-2 pr-0">
                <span class="content my-auto">
                  <!-- Approvers image -->
                  <!-- Remove the Image , while integrating with API, , but give same height and width given here -->
                  <ng-container *ngFor="let approverItem of approvers">
                    <app-user-image [tooltip]="approverTooltip" content-type="template" placement="top"
                      style="margin: 2px;" [id]="approverItem.oid" imgHeight="28px" imgWidth="28px" borderStyle="solid"
                      borderWidth="2px" [borderColor]="
                      approverItem.approvalStatus == 'Approved'
                      ? '#009432'
                      : approverItem.approvalStatus == 'Rejected'
                      ? '#af0505'
                      : approverItem.approvalStatus == 'Escalated'
                      ? '#009432'
                      : approverItem.approvalStatus == 'Escalated'
                      ? '#009432'
                      : 'white'
                      ">
                    </app-user-image>
                    <ng-template #approverTooltip>
                      <div class="row tooltip-text">
                        {{ approverItem.name }}
                      </div>
                      <div class="row tooltip-text">
                        {{ approverItem.role }}
                      </div>
                      <div class="row tooltip-text">
                        Level {{ approverItem.level }}
                      </div>
                      <div class="row mx-auto">
                        <mat-icon class="tooltip-status-indicator p-0 mt-0 mb-0 ml-2" [ngStyle]="{
                          color:
                            approverItem.approvalStatus == 'Submitted'
                              ? '#ffa502'
                              : approverItem.approvalStatus == 'Approved'
                              ? '#009432'
                              : approverItem.approvalStatus == 'Rejected'
                              ? '#af0505'
                              : approverItem.approvalStatus == 'Recalled'
                              ? '#ff7200'
                              : approverItem.approvalStatus == 'Escalated'
                              ? '#009432'
                              : '#ffa502'
                        }">fiber_manual_record</mat-icon>
                        <div class="tooltip-text">
                          {{ approverItem.approvalStatus?approverItem.approvalStatus:'Awaiting Approval'}}
                        </div>
                        <div class="row tooltip-text pl-1">
                          {{ approverItem["approved/rejected_on"]?(approverItem["approved/rejected_on"] | date: 'dd-MMM-yyyy' ):'' }}
                        </div>
                      </div>
                    </ng-template>
                  </ng-container>
                </span>
              </div>
            </div>
            <!--  End of Cost center Approvers section -->

                        <!-- Treasury Approvers section -->
                        <div class="row pt-3" *ngIf = "showTreasuryApprovers()">
                          <div class="col-4 d-flex pl-0">
                            <span class="heading my-auto">
                              <!-- <span *ngIf="isTreasuryNameBasedOnConfig && isTreasuryNameBasedOnConfig == 1 && treasuryDisplayName">{{treasuryDisplayName}}</span> -->
                              <span *ngIf="isTreasuryNameBasedOnConfig && isTreasuryNameBasedOnConfig == true && treasuryDisplayName">{{treasuryDisplayName}}</span>
                              <span *ngIf="(isTreasuryNameBasedOnConfig && isTreasuryNameBasedOnConfig == true && treasuryDisplayName) == false">Treasury</span> Approvers
                            </span>
                          </div>
                          <div class="col-8 d-flex pl-2 pr-0">
                            <span class="content my-auto">
                              <!-- Approvers image -->
                              <!-- Remove the Image , while integrating with API, , but give same height and width given here -->
                              <ng-container *ngFor="let approverItem of treasuryApprovers">
                                <span *ngIf="expenseDetail.status != 'Closed' && expenseDetail.status != 'Rejected' ; else treasuryStatesBeforeClosure">
                                <app-user-image [tooltip]="approverTooltip" content-type="template" placement="top"
                                  style="margin: 2px;" [id]="approverItem.oid" imgHeight="28px" imgWidth="28px" borderStyle="solid"
                                  borderWidth="2px" [borderColor]="
                                  approverItem.approvalStatus == 'Closed'|| approverItem.approvalStatus == 'Approved'
                                  ? '#009432'
                                  : approverItem.approvalStatus == 'Verified'
                                  ? '#1890FF'
                                  : approverItem.approvalStatus == 'Rejected'
                                  ? '#af0505'
                                  : approverItem.approvalStatus == 'Escalated'
                                  ? '#009432'
                                  : 'white'
                                  ">
                                </app-user-image>
                                <ng-template #approverTooltip>
                                  <div class="row tooltip-text">
                                    {{ approverItem.name }}
                                  </div>
                                  <div class="row tooltip-text">
                                    {{ approverItem.role }}
                                  </div>
                                  <!-- <div class="row tooltip-text">
                                    Level {{ approverItem.level }}
                                  </div> -->
                                  <div class="row mx-auto">
                                    <mat-icon class="tooltip-status-indicator p-0 mt-0 mb-0" [ngStyle]="{
                                      color:
                                      approverItem.approvalStatus == 'Submitted'
                                          ? '#ffa502'
                                          : approverItem.approvalStatus == 'Verified'
                                          ? '#1890FF'
                                          : approverItem.approvalStatus == 'Rejected'
                                          ? '#af0505'
                                          : approverItem.approvalStatus == 'Closed'
                                          ? '#009432'
                                          : approverItem.approvalStatus == 'Escalated'
                                          ? '#009432'
                                          : '#ffa502'
                                    }">fiber_manual_record</mat-icon>
                                    <div class="row tooltip-text">
                                      {{ expenseDetail.status != 'Verified' ? 'Awaiting Approval' : approverItem.approvalStatus == 'Verified' ? 'Verified, Awaiting Closure' : 'Awaiting Closure' }}
                                    </div>
                                    <br>
                                    <div class="row tooltip-text">
                                      {{ approverItem["approved/rejected_on"]?(approverItem["approved/rejected_on"] | date: 'dd-MMM-yyyy' ):'' }}</div>
                            
                                  </div>
                                </ng-template>
                              </span>
                              <ng-template #treasuryStatesBeforeClosure>
                                <span *ngIf="approverItem.approvalStatus == 'Closed' || approverItem.approvalStatus == 'Rejected' || approverItem.approvalStatus == 'Verified'">
                                <app-user-image [tooltip]="approverTooltip" content-type="template" placement="top"
                                  style="margin: 2px;" [id]="approverItem.oid" imgHeight="28px" imgWidth="28px" borderStyle="solid"
                                  borderWidth="2px" [borderColor]="
                                  approverItem.approvalStatus == 'Closed'|| approverItem.approvalStatus == 'Verified'
                                  ? '#009432'
                                  : approverItem.approvalStatus == 'Rejected'
                                  ? '#af0505'
                                  : approverItem.approvalStatus == 'Escalated'
                                  ? '#009432'
                                  : 'white'
                                  ">
                                </app-user-image>
                                <ng-template #approverTooltip>
                                  <div class="row tooltip-text">
                                    {{ approverItem.name }}
                                  </div>
                                  <div class="row tooltip-text">
                                    {{ approverItem.role }}
                                  </div>
                                  <!-- <div class="row tooltip-text">
                                    Level {{ approverItem.level }}
                                  </div> -->
                                  <div class="row mx-auto">
                                    <mat-icon class="tooltip-status-indicator p-0 mt-0 mb-0" [ngStyle]="{
                                      color:
                                        approverItem.approvalStatus == 'Submitted'
                                          ? '#ffa502'
                                          : approverItem.approvalStatus == 'Verified'
                                          ? '#009432'
                                          : approverItem.approvalStatus == 'Rejected'
                                          ? '#af0505'
                                          : approverItem.approvalStatus == 'Closed'
                                          ? '#009432'
                                          : approverItem.approvalStatus == 'Escalated'
                                          ? '#009432'
                                          : '#ffa502'
                                    }">fiber_manual_record</mat-icon>
                                    <div class="row tooltip-text">
                                      {{ approverItem.approvalStatus ? (expenseConfig && expenseConfig[expenseDetail.status]?.display_name) 
                                         : expenseDetail.status == 'Verified' ? 'Awaiting Closure' : 'Awaiting Approval' }}
                                    </div>
                                    <br>
                                    <div class="row tooltip-text pl-1">
                                      {{ approverItem["approved/rejected_on"]?(approverItem["approved/rejected_on"] | date: 'dd-MMM-yyyy' ):'' }}</div>
                            
                                  </div>
                                </ng-template>
                                </span>
                              </ng-template>                                
                              </ng-container>
                            </span>
                          </div>
                        </div>
                        <!--  End of Approvers section -->

            <!-- requester section -->
            <div class="row pt-3">
              <div class="col-4 d-flex pl-0">
                <span class="heading my-auto">
                  Requester
                </span>
              </div>
              <div class="col-6 d-flex pr-0" style="padding-left: 12px; overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
              display: flex;
              align-items: center;
              min-width: 0;"  [tooltip]="expenseDetail.claimed_by_name">
                <app-user-profile type="expense-card-data" [oid]="expenseDetail.claimed_by" imgHeight="28px"
                  imgWidth="28px"></app-user-profile>
                  <!-- <span class="pt-1 pl-1 aid-field">AID : {{expenseDetail.init_assoc_id}}</span> -->
              </div>
            </div>
            <!--  End of Requester section -->
            <!-- created by section -->
            <div class="row pt-3">
              <div class="col-4 d-flex pl-0">
                <span class="heading my-auto">
                  Created by
                </span>
              </div>
              <div class="col-6 d-flex pr-0" style="padding-left: 12px; overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
              display: flex
          ;
              align-items: center;
              min-width: 0;"  [tooltip]="expenseDetail.created_by_name">
                <app-user-profile type="expense-card-data" [oid]="expenseDetail.created_by_oid" imgHeight="28px"
                  imgWidth="28px"></app-user-profile>
                  <!-- <span class="pt-1 pl-1 aid-field">AID : {{expenseDetail.created_by_aid}}</span> -->
              </div>
            </div>
            <!-- Created on section -->
            <div class="row pt-3">
              <div class="col-4 d-flex pl-0">
                <span class="heading my-auto">
                  Created on
                </span>
              </div>
              <div class="col-8 d-flex pl-2 pr-0">
                <span class="content my-auto">
                  {{ expenseDetail.date_of_submission | date: 'dd-MMM-yyyy' }}
                </span>
              </div>
            </div>
            <!--  End of Created on  section -->

            <!-- Duration section -->
            <!-- <div class="row pt-3">
              <div class="col-4 d-flex pl-0">
                <span class="heading my-auto">
                  Duration
                </span>
              </div>
              <div class="col-8 d-flex pl-2 pr-0">
                <span class="content my-auto" matTooltip="{{ expenseDetail.start_date }} to {{ expenseDetail.end_date }}" >
                  {{ expenseDetail.start_date }} to {{ expenseDetail.end_date }}
                </span>
              </div>
            </div> -->
            <!--  End of Duration  section -->

            <!-- Days elasped section -->
            <div class="row pt-3">
              <div class="col-4 d-flex pl-0">
                <span class="heading my-auto" [matTooltip]="treasuryDisplayNameTooltip">
                  <span *ngIf="isTreasuryNameBasedOnConfig && isTreasuryNameBasedOnConfig == true && treasuryDisplayName">{{treasuryDisplayName}}</span>
                  <span *ngIf="(isTreasuryNameBasedOnConfig && isTreasuryNameBasedOnConfig == true && treasuryDisplayName) == false">Treasurer</span> Approval Days Elapsed
                </span>
              </div>
              <div class="col-8 d-flex pl-2 pr-0">
                <span class="content my-auto">
                  {{ expenseDetail.days_elapsed + " " }} days
                </span>
              </div>
            </div>
            <!--  End of Days elasped  section -->

            <!--  Expense code section -->
            <div class="row pt-3">
              <div class="col-4 d-flex pl-0">
                <span class="heading my-auto">
                  Exp-code
                </span>
              </div>
              <div class="col-8 d-flex pl-2 pr-0">
                <span class="content my-auto">
                  #{{ expenseDetail.expense_type === "C" ? "CL/" : "AD/"
                  }}{{ expenseDetail.expense_header_id }}
                </span>
              </div>
            </div>
            <!--  End of Expense code section -->

             <!--  Expense Start section -->
             <!-- <div class="row pt-3">
              <div class="col-4 d-flex pl-0">
                <span class="heading my-auto">
                  Start Date
                </span>
              </div>
              <div class="col-8 d-flex pl-2 pr-0">
                <span class="content my-auto">
                  {{ expenseDetail.start_date | ddMmmYy }}
                </span>
              </div>
            </div> -->
            <!--  End of Expense Start section -->

             <!--  Expense End section -->
             <!-- <div class="row pt-3">
              <div class="col-4 d-flex pl-0">
                <span class="heading my-auto">
                  End Date
                </span>
              </div>
              <div class="col-8 d-flex pl-2 pr-0">
                <span class="content my-auto">
                  {{ expenseDetail.end_date | ddMmmYy }}
                </span>
              </div>
            </div> -->
            <!--  End of Expense End section -->

             <!--  Expense People Involved section -->
             <!-- <div class="row pt-2">
              <div class="col-4 d-flex pl-0">
                <span class="heading my-auto" matTooltip="People Involved">
                  People Involved
                </span>
              </div>
              <div class="col-8 d-flex pl-2 pr-0">
                <span class="content my-auto">
                
                  <ng-container *ngFor="let peopleInvolvedItem of peopleInvolved">
                    <app-user-image [tooltip]="peopleInvolvedTooltip" content-type="template" placement="top"
                      style="margin: 2px;" [id]="peopleInvolvedItem.id" imgHeight="25px" imgWidth="25px" borderStyle="solid"
                      borderWidth="2px" borderColor="white">
                    </app-user-image>
                    <ng-template #peopleInvolvedTooltip>
                      <div class="row tooltip-text">
                        {{ peopleInvolvedItem.displayName }}
                      </div>
                    </ng-template>
                  </ng-container>
                  <ng-container *ngIf="actualPeopleInvolved.length>0">
                    <button style ="line-height: 13px;" mat-icon-button [matMenuTriggerFor]="menu">
                     
                      <strong style="vertical-align: text-bottom;">+{{actualPeopleInvolved.length}}</strong>
                    </button> 
                    <mat-menu #menu="matMenu" yPosition="below">
                      <div class="row" style="height: 20rem;" (click)="$event.stopPropagation();">
                        <div class="col-12 p-0">
                          <div style="    font-size: 15px;
                          border-bottom: solid 1px #f7a0a0;
                          padding-bottom: 5px;
                          font-weight: 500;" class="d-flex justify-content-center mt-3 mb-3  menu-title">
                            People Involved
                          </div>
                          <div class="row mt-2 mb-2 pb-2 border-bottom solid"
                            *ngFor="let emp of actualPeopleInvolved; let empind = index">
                            <div class="col-3 pr-0">
                              <app-user-image [id]="emp.id"></app-user-image>
                            </div>
                            <div class="col-9 pl-3">
                              <div class="row">
                                <div class="col-12 pl-0" style="white-space: nowrap;
                                text-overflow: ellipsis;
                                overflow: hidden;
                                padding-top: 0.6em;" matTooltip="{{emp.displayName}}">
                                  {{emp.displayName}}
                                </div>
                              </div>
                             
                            </div>
                
                          </div>
                        </div>
                      </div>
                    </mat-menu>
                  </ng-container>
                </span>
              </div>
            </div> -->
            <!--  End of Expense People Involved section -->
          </div>
          <!-- End of Header details  -->

          <!-- Status section -->
          <div class="row pt-2 pl-3">
            <div class="col-4 d-flex pl-0">
              <span class="heading my-auto">
                Status
              </span>
            </div>
            <div class="col-8 d-flex pl-2 pr-0">
              <div class="status-circular" [ngStyle]="{
                  'background-color': getColor(expenseDetail.status)
                }"></div>
              <span class="content my-auto ml-3">
                <!-- {{ expenseConfig[expenseDetail.status]?.display_name || 'Closed ***' }} -->
                {{ expenseConfig && expenseConfig[expenseDetail.status]?.display_name || expenseDetail.status }}

              </span>
            </div>
          </div>
          <!-- End of Status section -->

          <!-- Amount requested section -->
          <div class="row pt-3 pl-3">
            <span class="heading">Amount requested </span>
          </div>

          <div class="row pt-2 pl-3">
            <app-expense-currency 
            [fieldConfig]="fieldConfig" 
            [currencyList]="totalAmount" 
            class="flex-1" 
            [expenseCurrency]="expenseCurrency" [entityCurrencyCode]="entityCurrencyCode"
            type="simpleAndBold">
            </app-expense-currency>

          </div>
          <!-- End of  Amount requested section -->

          <!-- Amount Claimed section -->
          <div class="row pt-3 pl-3">
            <span class="heading">Amount Paid </span>
          </div>

          <div class="row pt-2 pl-3 pb-2">
            <app-expense-currency [fieldConfig]="fieldConfig" [currencyList]="
                expenseDetail.amount_claimed
                  ? parseVal(expenseDetail.amount_claimed)
                  : zeroCurrency
              " class="flex-1" type="simpleAndBold"></app-expense-currency>
          </div>
          <!-- End of  Amount Claimed section -->

          <!-- <div class="row pt-2 pl-3">
            <div class="col-4 d-flex pl-0">
              <span class="heading my-auto" matTooltip="Customer Billing">
                Customer Billing
              </span>
            </div>
            <div class="col-8 d-flex pl-2 pr-0">
              <span class="content my-auto ml-3" *ngIf="expenseDetail.role != 'Approver' ">
                  <span *ngIf="billingConfig">
                    <span *ngIf="expenseDetail.is_billable!=null">
                      <span *ngFor="let entity of billingConfig">
                        <span class="p-0 pt-1"
                        *ngIf="entity.id==expenseDetail.is_billable">
                        {{entity.billing_type}}</span>
                      </span>
                    </span>
                </span>
              </span>
              <span *ngIf="expenseDetail.role == 'Approver' ">
              <span *ngFor="let entity of billingConfig">
                <button mat-button [matMenuTriggerFor]="billingMenu" class="pt-1"
                *ngIf="entity.id==expenseDetail.is_billable">
                {{entity.billing_type}}</button>
              </span>
              <mat-menu #billingMenu="matMenu">
                <button mat-menu-item *ngFor="let entity of billingConfig" (click)="setCustomerBilling(entity.id)">{{entity.billing_type}}</button>
              </mat-menu>
              </span>
            </div>
          </div> -->
        </div>
      </div>
    </div>

    <!-- End of Expense details column  -->
    <!-- Status tracker -->
    <div class="col-9 pl-0 pr-1" style="max-height: 91vh; overflow: scroll;">
      <router-outlet></router-outlet>
    </div>
  </div>
</div>