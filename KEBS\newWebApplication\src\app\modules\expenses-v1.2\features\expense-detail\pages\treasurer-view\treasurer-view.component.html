<!-- Submitted status -->
<div class="treasurer-view">
  <div class="row pt-2">
    <!-- <div class="col-12">
      <div class="d-flex flex-row-reverse">
        <button
          mat-icon-button
          class="ml-auto chat-button mt-0"
          matTooltip="comment"
          (click)="openCommentBox()"
        >
          <mat-icon class="chat-Icon">forum</mat-icon>
        </button>
      </div>
    </div> -->
    <div class="d-flex">
      <div [appStatusColor]="expenseDetail?.status" [indicator]="1" class="status-circular-in-thread"></div>
      <span class="status-in-thread ml-3 mr-2">
        {{ getStatusText(1, expenseDetail?.status) }}
      </span>
      <span class="status-in-thread">
        {{ expenseDetail.date_of_submission | date: 'dd-MMM-yyyy' }}
      </span>
    </div>
  
    <!-- <button mat-icon-button [matTooltip]="isDownloading ? 'Downloading' : 'Download all Attachments'"
      class="view-button-inactive mt-0 mr-6" [disabled]="isDownloading" style="margin-left: 1%"
      (click)="downloadAllAttachments()">
      <mat-icon *ngIf="!isDownloading" class="iconButton">cloud_download</mat-icon>
      <mat-spinner *ngIf="isDownloading" class="spinner-align" diameter="18"></mat-spinner>
    </button> -->
    <button *ngIf="isCommentsExist == false" mat-icon-button class="chat-button mt-0 ml-auto mr-3" [matTooltip]="fieldConfig?.comment?.field_label ? fieldConfig?.comment?.field_label : 'Comment' " (click)="openCommentBox()">
      <mat-icon class="chat-Icon">chat</mat-icon>
    </button>
    <button *ngIf="isCommentsExist == true" mat-icon-button class="chat-button mt-0 ml-auto mr-3" [matTooltip]="fieldConfig?.comment?.field_label ? fieldConfig?.comment?.field_label : 'Comment' " (click)="openCommentBox()">
      <mat-icon class="chat-Icon-active">mark_unread_chat_alt</mat-icon>
    </button>
  </div>
  <!-- line -->
  <div class="row" style="position: absolute;">
    <div class="col-12" style="padding-left: 6px;">
      <div class="row">
        <div [appStatusLine]="expenseDetail?.status" [indicator]="1" class="col-12"></div>
      </div>
    </div>
  </div>
  <!-- line ends -->
  <div *ngIf="(expenseDetail.status === 'Submitted' || expenseDetail.status === 'Rejected') && showExpenseCard == true">
    <form [formGroup]="approvalItemForm">
    <div class="row pt-1 pb-1">
    <div class="col-12 pl-0">
      <div class="slide-in-top">
        <div class="d-flex mt-1" style="box-shadow: 0 3px 9px 1px rgba(0, 0, 0, .2), 0 1px 1px 0 rgba(0, 0, 0, .14), 0 1px 3px 0 rgba(0, 0, 0, .12);
            border: 1px solid #e3dfdf;
              border-radius: 4px;
              background: white;">
            <div style="width:18%;">
              <div class="p-2 card-header border-bottom" style="border-right: 1px solid #b9c0ca">
                {{fieldConfig?.category?.field_label ? fieldConfig?.category?.field_label : 'Category'}}
              </div>
              <ng-container *ngIf="loading == false">
                <div class="d-flex" *ngFor="let item of approvalItemList; let i = index"
                  style="border-right: 1px solid #b9c0ca; width: 100%;">
                  <div class="p-2 elip border-bottom" [matTooltip]="item.expense_category_name" style="width:100%">
                    {{ item.expense_category_name }}
                  </div>
                  <ng-container *ngIf="(item.expense_category_id === 18 || 
                  item.expense_category_id === 8) && 
                  (
                  item.to_location || 
                  item.vehicle_type || 
                  item.vehicle_engine_type ||
                  item.miles ||
                  item.perdiem_start_date ||
                  item.perdiem_start_time ||
                  item.perdiem_end_date  ||
                  item.perdiem_end_time ||
                  item.days ||
                  item.location)">
                  
                  <span>
                    <button mat-icon-button
                    style="margin-left: 5px; padding: 0; height: 20px; width: 20px; display: inline-flex; align-items: center;"
                    (click)="toggleOverlay(i)" #triggerOriginRef="cdkOverlayOrigin" 
                        [cdkOverlayOrigin]="triggerOriginRef"
                        style="height: 14px; width: 25px;">
                        <mat-icon style="font-size: 14px; color: #474444;">
                            info
                        </mat-icon>
                    </button>
                </span>
                
                <ng-template
                    cdkConnectedOverlay
                    [cdkConnectedOverlayOrigin]="triggerOriginRef"
                    [cdkConnectedOverlayOpen]="openOverlayIndex === i"
                    [cdkConnectedOverlayHasBackdrop]="true"
                    (backdropClick)="openOverlayIndex = null"
                    cdkConnectedOverlayBackdropClass="overLayClass"
                    [cdkConnectedOverlayFlexibleDimensions]="true"
                    [cdkConnectedOverlayPositions]="[
                        { originX: 'start', originY: 'bottom', overlayX: 'start', overlayY: 'top' },
                        { originX: 'start', originY: 'top', overlayX: 'start', overlayY: 'bottom' }
                    ]">
                    
                    <div class="card balance-planned-log-pop-over">
                        <!-- Header Section -->
                        <div class="header">
                            <span class="width-class">Claim Details</span>
                            <span class="close-icon">
                                <mat-icon style="font-size: 18px; cursor: pointer;" (click)="openOverlayIndex = null">close</mat-icon>
                            </span>
                        </div>
                        
                        <hr class="divider" >
    
                        <div *ngIf="item.expense_category_id === 18">
                        <!-- Claim Details -->
                        
    
                        <div class="row" *ngIf= "fieldConfig?.fromLocation?.is_to_show_in_treasury_details">
                          <div class="col-5 label-class"> {{ fieldConfig?.fromLocation?.field_label ? fieldConfig?.fromLocation?.field_label : 'From Location' }}</div>
                          <div class="col-7 value-class">{{ item.from_location || '-' }}</div>
                      </div>
                
                        <div class="row" *ngIf= "fieldConfig?.toLocation?.is_to_show_in_treasury_details">
                            <div class="col-5 label-class"> {{ fieldConfig?.toLocation?.field_label ? fieldConfig?.toLocation?.field_label : 'To Location' }}</div>
                            <div class="col-7 value-class">{{ item.to_location || '-' }}</div>
                        </div>
                
                        <div class="row" *ngIf= "fieldConfig?.vehicleType?.is_to_show_in_treasury_details">
                            <div class="col-5 label-class">{{ fieldConfig?.vehicleType?.field_label ? fieldConfig?.vehicleType?.field_label : 'Vehicle Type' }}</div>
                            <div class="col-7 value-class">{{ item.vehicle_type || '-' }}</div>
                        </div>
                
                        <div class="row" *ngIf= "fieldConfig?.vehicleEngineType?.is_to_show_in_treasury_details">
                            <div class="col-5 label-class">{{ fieldConfig?.vehicleEngineType?.field_label ? fieldConfig?.vehicleEngineType?.field_label : 'Vehicle Engine Type' }}</div>
                            <div class="col-7 value-class">{{ item.vehicle_engine_type || '-' }}</div>
                        </div>
    
                        <div class="row" *ngIf= "fieldConfig?.miles?.is_to_show_in_treasury_details">
                          <div class="col-5 label-class">{{ fieldConfig?.miles?.field_label ? fieldConfig?.miles?.field_label : 'Miles' }}</div>
                          <div class="col-7 value-class">{{ item.miles || '-' }}  {{ item.unit || '-' }}</div>
                      </div>
                    </div>
    
                    <div *ngIf="item.expense_category_id === 8">
                      <!-- Claim Details -->
                       
                      <div class="row" *ngIf= "fieldConfig?.perDiemstartDate?.is_to_show_in_treasury_details">
                          <div class="col-5 label-class"> {{ fieldConfig?.perDiemstartDate?.field_label ? fieldConfig?.perDiemstartDate?.field_label : 'Per Diem Start Date' }}</div>
                          <div class="col-7 value-class">{{ item.perdiem_start_date || '-' | date: 'dd-MMM-yyyy'}}</div>
                      </div>
    
                      <div class="row" *ngIf= "fieldConfig?.perDiemStartTime?.is_to_show_in_treasury_details">
                        <div class="col-5 label-class"> {{ fieldConfig?.perDiemStartTime?.field_label ? fieldConfig?.perDiemStartTime?.field_label : 'Ped Diem Start Time' }}</div>
                        <div class="col-7 value-class">{{ item.perdiem_start_time || '-'  }}</div>
                    </div>
              
                      <div class="row" *ngIf= "fieldConfig?.perDiemEndDate?.is_to_show_in_treasury_details">
                          <div class="col-5 label-class"> {{ fieldConfig?.perDiemEndDate?.field_label ? fieldConfig?.perDiemEndDate?.field_label : 'Per Diem End Date' }}</div>
                          <div class="col-7 value-class">{{ item.perdiem_end_date || '-' | date: 'dd-MMM-yyyy'}}</div>
                      </div>
              
                      <div class="row" *ngIf= "fieldConfig?.perDiemEndTime?.is_to_show_in_treasury_details">
                          <div class="col-5 label-class">{{ fieldConfig?.perDiemEndTime?.field_label ? fieldConfig?.perDiemEndTime?.field_label : 'Per Diem End Time' }}</div>
                          <div class="col-7 value-class">{{ item.perdiem_end_time || '-'  }}</div>
                      </div>
              
                      <div class="row" *ngIf= "fieldConfig?.days?.is_to_show_in_treasury_details">
                          <div class="col-5 label-class">{{ fieldConfig?.days?.field_label ? fieldConfig?.days?.field_label : 'Days' }}</div>
                          <div class="col-7 value-class">{{ item.days || '-' }}</div>
                      </div>
    
                      <div class="row" *ngIf= "fieldConfig?.location?.is_to_show_in_treasury_details">
                        <div class="col-5 label-class">{{ fieldConfig?.location?.field_label ? fieldConfig?.location?.field_label : 'Location' }}</div>
                        <div class="col-7 value-class">{{ item.location || '-' }}</div>
                    </div>
                 
                  </div>
                      
                    </div>
                </ng-template>
                  </ng-container>
                </div>
              </ng-container>
            </div>
            <div style="overflow-x: auto; width: 50%; overflow-y: hidden">
              <div class="d-flex">
                <div style="min-width:180px;" class="p-2 border-bottom card-header">
                  <span>{{fieldConfig?.amountInDetailsPage?.field_label ? fieldConfig?.amountInDetailsPage?.field_label
                    : 'Amount'}}</span>
                </div>
                <div *ngIf="fieldConfig?.invoiceNo?.is_visible" style="min-width:180px;" class="p-2 border-bottom elip card-header">
                  {{fieldConfig?.invoiceNo?.field_label ? fieldConfig?.invoiceNo.field_label : 'Invoice No.'}}
                </div>

                <div *ngIf="fieldConfig?.invoiceDate?.is_visible" style="min-width:180px;" class="p-2 border-bottom elip card-header">
                  {{fieldConfig?.invoiceDate?.field_label ? fieldConfig?.invoiceDate.field_label : 'Receipt Date .'}}
                </div>

                <div style="min-width:180px;" class="p-2 border-bottom elip card-header">
                  <span> {{fieldConfig?.description?.field_label ? fieldConfig?.description.field_label :
                    'Description'}}</span>
                </div>
                <div style="min-width:180px;" class="p-2 border-bottom card-header">
                  <span>{{fieldConfig?.attachment?.field_label ? fieldConfig?.attachment.field_label :
                    'Attachment'}}</span>
                </div>
                <div style="min-width:180px;" class="p-2 border-bottom card-header">
                  <span>{{fieldConfig?.peopleInvolved?.field_label ? fieldConfig?.peopleInvolved.field_label : 'People
                    Involved'}}</span>
                </div>
              </div>
              <ng-container *ngIf="loading == false">
                <div class="d-flex" *ngFor="let item of approvalItemList; let i = index" style="height: 36px;">
                  <div class="d-flex">
                    <div style="min-width:180px;" class="pt-2 pl-2 pr-2 pb-0 border-bottom">
                      <app-expense-currency [fieldConfig]="fieldConfig" [currencyList]="parseJsonAmount(item.item_amount)"
                        class="flex-1" type="simple" [taxPercentage]="item.tax_type" [taxRateType]="item.taxrate_type"
                        [isToShowTooltip]="isToShowTooltip">
                      </app-expense-currency>
                    </div>
                    <div *ngIf="fieldConfig?.invoiceNo?.is_visible" style="min-width:180px;" class="pt-2 pl-2 pr-2 pb-0 border-bottom elip">
                      <span *ngIf="item.invoice_no" [matTooltip]="item?.invoice_no">
                        {{ item?.invoice_no }}
                      </span>
                      <span *ngIf="!item.invoice_no">
                        -
                      </span>
                    </div>

                    <div *ngIf="fieldConfig?.invoiceDate?.is_visible" style="min-width:180px;" class="pt-2 pl-2 pr-2 pb-0 border-bottom elip">
                      <span *ngIf="item.invoice_date" [matTooltip]="item?.invoice_date | date: 'dd-MMM-yyyy' ">
                        {{ item?.invoice_date  | date: 'dd-MMM-yyyy'}}
                      </span>
                      <span *ngIf="!item.invoice_date">
                        -
                      </span>
                    </div>

                    <div style="min-width: 180px; max-width: 180px; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;" 
                    class="pt-2 pl-2 pr-2 pb-0 border-bottom elip">
                 <span class="mt-1" 
                       [tooltip]="descItem" 
                       content-type="template" 
                       max-width="2000" 
                       placement="top">
                   {{ item.description }}
                   
                   <ng-template #descItem>
                     {{ item.description }}
                   </ng-template>
                 </span>
               </div>
               
                    <div style="min-width:180px;" class="pb-0 border-bottom">
                      <div class="pl-2 pr-2" *ngIf="item.context_id; else fileAttachmentOld">
                        <app-expense-upload-btn [destinationBucket]="'kebs-expenses'" [routingKey]="'expenses'"
                          [contextId]="item.context_id" [allowEdit]="false" (change)="changeInAttachment($event,i)"
                          [expHeaderId]="expenseDetail.expense_header_id" [btnColor]="primaryColor">
                        </app-expense-upload-btn>
                      </div>
                      <ng-template #fileAttachmentOld>
                        <div class="pt-2 pb-2" *ngIf="item?.attachment; else noAttachmentsFound">
          
                          <span class="mt-1" *ngIf="!item.isAttachmentLoading" style="cursor: pointer;"
                            [matTooltip]="item?.attachment[0]?.fileName"
                            (click)="openFile(i)">{{item?.attachment[0]?.fileName}}</span>
                          <mat-spinner *ngIf="item.isAttachmentLoading" diameter="20" class="m-auto"></mat-spinner>
                        </div>
                        <ng-template #noAttachmentsFound>
                          <div class="pt-2">
                          <span class="content" style="padding-left:12px !important;padding-right:16px !important;"
                            class="people-involved-counter pt-2">
                            {{ 'No Attachment Found !'}}
                          </span>
                          </div>
                        </ng-template>
                      </ng-template>
                    </div>
                    <div style="min-width:180px;" class="pl-2 pr-2 border-bottom">
                      <span class="images-container stack-from-left" (click)="openExpensePeopleInvolved(item.people_involved)">
                        <span class="circular-image" *ngFor="let element of item?.people_involved_display_array; let j=index">
                          <ng-container>
                            <span *ngIf="j<2; else peopleInvolvedCount">
                              <span *ngIf="element.profile_image_url">
                                <app-expense-user-image  content-type="template" placement="top"
                                  style="margin: 2px;" [imgUrl]="element.profile_image_url" imgHeight="31px" imgWidth="31px"
                                  borderStyle="solid" borderWidth="2px" [borderColor]=" 'white' ">
                                </app-expense-user-image>
                                <ng-template #peopleInvolvedTooltip>
                                  <div class="row tooltip-text">{{ element.displayName }}</div>
                                  <div class="row tooltip-text">AID : {{ element.associate_id }}</div>
                                </ng-template>
                              </span>
                              <span *ngIf="!element.profile_image_url">
                                <app-user-image  [id]="element.oid" imgWidth="31px"
                                  imgHeight="31px"></app-user-image>
                                <ng-template #peopleInvolvedTooltip>
                                  <div class="row tooltip-text">{{ element.displayName }}</div>
                                  <div class="row tooltip-text">AID : {{ element.associate_id }}</div>
                                </ng-template>
                              </span>
                            </span>
                            <ng-template #peopleInvolvedCount>
                              <div *ngIf="item.people_involved.length -2 > 0" class="pt-1 d-flex justify-content-center people-involved-counter">
                                <span style="font-size: 14px;">+{{item.people_involved.length -2}}</span>
                              </div>
                            </ng-template>
                          </ng-container>
                        </span>
                      </span>
                      <div *ngIf="!item.people_involved || item.people_involved==' ' " class="pl-3"> - </div>
                    </div>
                    </div>
                </div>
              </ng-container>
            </div>
            <div style="width:16%;">
              <div class="p-2 border-bottom card-header" style="border-left: 1px solid #b9c0ca;">{{fieldConfig?.approverStatus?.field_label ? fieldConfig?.approverStatus.field_label : 'Approver status'}}</div>
              <ng-container *ngIf="loading == false" formArrayName="approvalItems">
                <div class="d-flex border-bottom" style="border-left: 1px solid #b9c0ca" *ngFor="
                let item of approvalItemFormData.approvalItems['controls']; let i = index"
                 [formGroupName]="i">
                  <div>
                    <div class="d-flex align-items-center content p-2 elip"
                      [ngStyle]="{'color': getTxtColor(approvalItemForm.value.approvalItems[i].cc_status)}" 
                      content-type="template"
                      [tooltip]="expRejectCommentTooltip" 
                      *ngIf="approvalItemForm.value.approvalItems[i].cc_reject_comment"
                       [max-width]="1200" placement="bottom">
                        <span class="pr-2">{{ approvalItemForm.value.approvalItems[i].cc_status }}</span>
                        <img src="https://assets.kebs.app/message-notifSVG.svg" style="height: 18px;">
                    </div>
              
                    <div class="d-flex align-items-center content p-2 elip"
                      [ngStyle]="{'color': getTxtColor(approvalItemForm.value.approvalItems[i].cc_status)}"
                      *ngIf="approvalItemForm.value.approvalItems[i].cc_reject_comment == null">
                      {{ approvalItemForm.value.approvalItems[i].cc_status }}
                    </div>
                    <ng-template #expRejectCommentTooltip>
                      <div style="width: 380px; background-color: #111434 !important; padding: 0% !important; margin: 0% !important;"
                            class="pb-2">
                            <div class="row d-flex pt-3 pl-4">
                              <div class="col p-0 tooltip-class-header"><span>Rejected By</span></div>
                              <div class="col p-0 tooltip-class-header">Rejected On</div>
                            </div>
                            <div class="row d-flex pt-1 pl-4">
                              <div class="col p-0 tooltip-class">
                                <span>{{ approvalItemForm.value.approvalItems[i].cc_reject_comment.rejected_by }}</span>
                              </div>
                              <div class="col p-0 tooltip-class">
                                <span>{{ approvalItemForm.value.approvalItems[i].cc_reject_comment.rejected_on | date: 'dd-MMM-yyyy' }}</span>
                              </div>
                            </div>
                            <div class="row d-flex pt-3 pl-4">
                              <div class="col p-0 tooltip-class-header"><span>Reason</span></div>
                            </div>
                            <div class="row d-flex pt-1 pl-4 pr-4 pb-3">
                              <div class="col p-0 tooltip-class">
                                <span>{{approvalItemForm.value.approvalItems[i].cc_reject_comment.comment}}</span>
                              </div>
                            </div>
                          </div>
                    </ng-template>
                  </div>
                </div>
              </ng-container>
            </div>
            <div style="width:16%;">
              <div class="p-2 border-bottom elip card-header">{{fieldConfig?.treasuryStatus?.field_label ? fieldConfig?.treasuryStatus?.field_label : 'Treasury status'}}</div>
              <div>
              <ng-container *ngIf="loading == false" formArrayName="approvalItems">
                <div class="d-flex border-bottom" *ngFor="
                  let item of approvalItemFormData.approvalItems['controls'];
                  let i = index
                " [formGroupName]="i">
                <div class="d-flex align-items-center content p-2 elip" *ngIf="
                            approvalItemForm.value.approvalItems[i].cc_status ==
                            'Approved';
                          ">
                          <div *ngIf="
                              approvalItemForm.value.approvalItems[i].t_is_curr_appr ===
                                1 &&
                                approvalItemForm.value.approvalItems[i]
                                  .treasuryStatus === 'Submitted';
                              else showStatus
                            ">
                            <span>Awaiting Approval</span>

                          </div>
                          </div>          
                          <!-- <div class="d-flex align-items-center content pt-2 pb-2" 
                          *ngIf="approvalItemForm.value.approvalItems[i].cc_status == 'Approved'; 
                                  else showStatus">
                        </div> -->
               
                        <ng-template #showStatus>
                          <div class="mt-2"
                            [ngStyle]="{'color': getTxtColor(approvalItemForm.value.approvalItems[i].treasuryStatus)}"
                            content-type="template" [tooltip]="expRejectCommentTreasurerTooltip"
                            *ngIf="approvalItemForm.value.approvalItems[i].t_reject_comment" [max-width]="1200" placement="bottom">
                            <span class="pr-2">{{approvalItemForm.value.approvalItems[i].treasuryStatus}}</span>
                            <img src="https://assets.kebs.app/message-notifSVG.svg" style="height: 18px;">
                          </div>
      
                          <div class="mt-2"
                            [ngStyle]="{'color': getTxtColor(approvalItemForm.value.approvalItems[i].treasuryStatus)}"
                            *ngIf="approvalItemForm.value.approvalItems[i].t_reject_comment == null">{{
                            approvalItemForm.value.approvalItems[i].treasuryStatus}}</div>
                        </ng-template>
      
                        <ng-template #expRejectCommentTreasurerTooltip>
                          <div style="width: 380px; background-color: #111434 !important; padding: 0% !important; margin: 0% !important;"
                          class="pb-2">
                          <div class="row d-flex pt-3 pl-4">
                            <div class="col p-0 tooltip-class-header"><span>Rejected By</span></div>
                            <div class="col p-0 tooltip-class-header">Rejected On</div>
                          </div>
                          <div class="row d-flex pt-1 pl-4">
                            <div class="col p-0 tooltip-class">
                              <span>{{ approvalItemForm.value.approvalItems[i].t_reject_comment.rejected_by }}</span>
                            </div>
                            <div class="col p-0 tooltip-class">
                              <span>{{ approvalItemForm.value.approvalItems[i].t_reject_comment.rejected_on | date: 'dd-MMM-yyyy'  }}</span>
                            </div>
                          </div>
                          <div class="row d-flex pt-3 pl-4">
                            <div class="col p-0 tooltip-class-header"><span>Reason</span></div>
                          </div>
                          <div class="row d-flex pt-1 pl-4 pr-4 pb-3">
                            <div class="col p-0 tooltip-class">
                              <span>{{approvalItemForm.value.approvalItems[i].t_reject_comment.comment}}</span>
                            </div>
                          </div>
                        </div>
                        </ng-template>
                        <div class="d-flex align-items-center content pt-2 elip" 
                        *ngIf="approvalItemForm.value.approvalItems[i].cc_status !='Approved'"
                        [ngStyle]="{'color': getTxtColor(approvalItemForm.value.approvalItems[i].treasuryStatus)}" 
                       style="height: 36px;">
                      <span>{{approvalItemForm.value.approvalItems[i].treasuryStatus}}</span>
      
                     </div>
                        </div>
                        </ng-container>
              </div>
            </div>
          </div>
      </div>
    </div>
  </div>
  </form>
  </div>
  <!-- line -->
  <div class="row" style="height: 10px; position: absolute;">
    <div class="col-12" style="padding-left: 6px;">
      <div class="row">
        <div [appStatusLine]="expenseDetail?.status" [indicator]="1" class="col-12"></div>
      </div>
    </div>
  </div>
  
  <!-- line ends -->
  <!-- Approved status -->
  <div class="row pt-2">
    <div class="d-flex">
      <div [appStatusColor]="expenseDetail?.status" [indicator]="2" class="status-circular-in-thread"></div>
      <span class="status-in-thread ml-3 mr-2">
        {{ getStatusText(2, expenseDetail?.status) }}
      </span>
      <span class="status-in-thread">
        {{
        expenseDetail.cc_action_on ? (expenseDetail.cc_action_on | date: 'dd-MMM-yyyy') : ""
        }}
      </span>
    </div>
  </div>
  
  <!--End of Approved status -->
  <!-- line -->
  <div class="row" style="height: 10px;">
    <div class="col-12" style="padding-left: 6px;">
      <div class="row">
        <div [appStatusLine]="expenseDetail?.status" [indicator]="2" class="col-12"></div>
      </div>
    </div>
  </div>
  <!-- Submitted items -->
  <div *ngIf="expenseDetail?.status === 'Approved' && showExpenseCard == true">
  <form [formGroup]="approvalItemForm">
    <div class="d-flex mt-1" style="box-shadow: 0 3px 9px 1px rgba(0, 0, 0, .2), 0 1px 1px 0 rgba(0, 0, 0, .14), 0 1px 3px 0 rgba(0, 0, 0, .12);
    border: 1px solid #b9c0ca;
      border-radius: 4px;
      background: white;">
      <div style="width:18%;">
        <div class="p-2 card-header border-bottom" style="border-right: 1px solid #b9c0ca">{{fieldConfig?.category?.field_label ? fieldConfig?.category?.field_label : 'Category'}}</div>
        <ng-container *ngIf="loading == false" formArrayName="approvalItems">
          <div class="d-flex" *ngFor="
          let item of approvalItemFormData.approvalItems['controls'];
          let i = index
        " [formGroupName]="i" style="border-right: 1px solid #b9c0ca;">
            <div class="p-2 elip border-bottom" [matTooltip]="approvalItemForm.value.approvalItems[i].expense_category_name"
            style="width:100%">
                          {{
                          approvalItemForm.value.approvalItems[i]
                          .expense_category_name
                          }}
            </div>
            <ng-container *ngIf="(approvalItemForm?.value?.approvalItems[i].expense_category_id === 18 || 
                     approvalItemForm?.value?.approvalItems[i].expense_category_id === 8) &&  
                     (approvalItemForm.value.approvalItems[i].from_location ||
                     approvalItemForm.value.approvalItems[i].to_location ||
                     approvalItemForm.value.approvalItems[i].vehicle_type ||
                     approvalItemForm.value.approvalItems[i].vehicle_engine_type ||
                     approvalItemForm.value.approvalItems[i].miles ||
                     approvalItemForm.value.approvalItems[i].perdiem_start_date ||
                     approvalItemForm.value.approvalItems[i].perdiem_start_time ||
                     approvalItemForm.value.approvalItems[i].perdiem_end_date ||
                     approvalItemForm.value.approvalItems[i].perdiem_end_time ||
                     approvalItemForm.value.approvalItems[i].days ||
                     approvalItemForm.value.approvalItems[i].location)">
            <span >
              <button mat-icon-button 
              style="margin-left: 5px;  padding: 0; height: 20px; width: 20px; display: inline-flex; align-items: center;"
              (click)="toggleOverlay(i)" #triggerOriginRef="cdkOverlayOrigin" 
                  [cdkOverlayOrigin]="triggerOriginRef"
                  style="height: 14px; width: 25px;">
                  <mat-icon style="font-size: 14px; color: #474444; ">
                      info
                  </mat-icon>
              </button>
          </span>
          
          <ng-template
              cdkConnectedOverlay
              [cdkConnectedOverlayOrigin]="triggerOriginRef"
              [cdkConnectedOverlayOpen]="openOverlayIndex === i"
              [cdkConnectedOverlayHasBackdrop]="true"
              (backdropClick)="openOverlayIndex = null"
              cdkConnectedOverlayBackdropClass="overLayClass"
              [cdkConnectedOverlayFlexibleDimensions]="true"
              [cdkConnectedOverlayPositions]="[
                  { originX: 'start', originY: 'bottom', overlayX: 'start', overlayY: 'top' },
                  { originX: 'start', originY: 'top', overlayX: 'start', overlayY: 'bottom' }
              ]">
              
              <div class="card balance-planned-log-pop-over">
                  <!-- Header Section -->
                  <div class="header">
                      <span class="width-class">Claim Details</span>
                      <span class="close-icon">
                          <mat-icon style="font-size: 18px; cursor: pointer;" (click)="openOverlayIndex = null">close</mat-icon>
                      </span>
                  </div>
                  
                  <hr class="divider" >

                  <div *ngIf="approvalItemForm.value.approvalItems[i].expense_category_id === 18">
                  <!-- Claim Details -->
                 

                  <div class="row" *ngIf= "fieldConfig?.fromLocation?.is_to_show_in_treasury_details">
                    <div class="col-5 label-class"> {{ fieldConfig?.fromLocation?.field_label ? fieldConfig?.fromLocation?.field_label : 'From Location' }}</div>
                    <div class="col-7 value-class">{{ approvalItemForm.value.approvalItems[i].from_location || '-' }}</div>
                </div>
          
                  <div class="row" *ngIf= "fieldConfig?.toLocation?.is_to_show_in_treasury_details">
                      <div class="col-5 label-class"> {{ fieldConfig?.toLocation?.field_label ? fieldConfig?.toLocation?.field_label : 'To Location' }}</div>
                      <div class="col-7 value-class">{{ approvalItemForm.value.approvalItems[i].to_location || '-' }}</div>
                  </div>
          
                  <div class="row" *ngIf= "fieldConfig?.vehicleType?.is_to_show_in_treasury_details">
                      <div class="col-5 label-class">{{ fieldConfig?.vehicleType?.field_label ? fieldConfig?.vehicleType?.field_label : 'Vehicle Type' }}</div>
                      <div class="col-7 value-class">{{ approvalItemForm.value.approvalItems[i].vehicle_type || '-' }}</div>
                  </div>
          
                  <div class="row" *ngIf= "fieldConfig?.vehicleEngineType?.is_to_show_in_treasury_details">
                      <div class="col-5 label-class">{{ fieldConfig?.vehicleEngineType?.field_label ? fieldConfig?.vehicleEngineType?.field_label : 'Vehicle Engine Type' }}</div>
                      <div class="col-7 value-class">{{ approvalItemForm.value.approvalItems[i].vehicle_engine_type || '-' }}</div>
                  </div>

                  <div class="row" *ngIf= "fieldConfig?.miles?.is_to_show_in_treasury_details">
                    <div class="col-5 label-class">{{ fieldConfig?.miles?.field_label ? fieldConfig?.miles?.field_label : 'Miles' }}</div>
                    <div class="col-7 value-class">{{ approvalItemForm.value.approvalItems[i].miles || '-' }}  {{ approvalItemForm.value.approvalItems[i].unit || '-' }}</div>
                </div>
              </div>

              <div *ngIf="approvalItemForm.value.approvalItems[i].expense_category_id === 8">
                <!-- Claim Details -->
                 
                <div class="row" *ngIf= "fieldConfig?.perDiemstartDate?.is_to_show_in_treasury_details">
                    <div class="col-5 label-class"> {{ fieldConfig?.perDiemstartDate?.field_label ? fieldConfig?.perDiemstartDate?.field_label : 'Per Diem Start Date' }}</div>
                    <div class="col-7 value-class">{{ approvalItemForm.value.approvalItems[i].perdiem_start_date || '-'  | date: 'dd-MMM-yyyy'}}</div>
                </div>

                <div class="row" *ngIf= "fieldConfig?.perDiemStartTime?.is_to_show_in_treasury_details">
                  <div class="col-5 label-class"> {{ fieldConfig?.perDiemStartTime?.field_label ? fieldConfig?.perDiemStartTime?.field_label : 'Ped Diem Start Time' }}</div>
                  <div class="col-7 value-class">{{ approvalItemForm.value.approvalItems[i].perdiem_start_time || '-' }}</div>
              </div>
        
                <div class="row" *ngIf= "fieldConfig?.perDiemEndDate?.is_to_show_in_treasury_details">
                    <div class="col-5 label-class"> {{ fieldConfig?.perDiemEndDate?.field_label ? fieldConfig?.perDiemEndDate?.field_label : 'Per Diem End Date' }}</div>
                    <div class="col-7 value-class">{{ approvalItemForm.value.approvalItems[i].perdiem_end_date || '-' | date: 'dd-MMM-yyyy'}}</div>
                </div>
        
                <div class="row" *ngIf= "fieldConfig?.perDiemEndTime?.is_to_show_in_treasury_details">
                    <div class="col-5 label-class">{{ fieldConfig?.perDiemEndTime?.field_label ? fieldConfig?.perDiemEndTime?.field_label : 'Per Diem End Time' }}</div>
                    <div class="col-7 value-class">{{ approvalItemForm.value.approvalItems[i].perdiem_end_time || '-' }}</div>
                </div>
        
                <div class="row" *ngIf= "fieldConfig?.days?.is_to_show_in_treasury_details">
                    <div class="col-5 label-class">{{ fieldConfig?.days?.field_label ? fieldConfig?.days?.field_label : 'Days' }}</div>
                    <div class="col-7 value-class">{{ approvalItemForm.value.approvalItems[i].days || '-' }}</div>
                </div>

                <div class="row" *ngIf= "fieldConfig?.location?.is_to_show_in_treasury_details">
                  <div class="col-5 label-class">{{ fieldConfig?.location?.field_label ? fieldConfig?.location?.field_label : 'Location' }}</div>
                  <div class="col-7 value-class">{{ approvalItemForm.value.approvalItems[i].location || '-' }}</div>
              </div>
           
            </div>
                
              </div>
          </ng-template>
            </ng-container>
          </div>
        </ng-container>
      </div>
      <div style="overflow-x: auto; width: 50%; overflow-y: hidden;">
        <div class="d-flex">
          <div style="min-width:180px;" class="p-2 border-bottom card-header">
            <span>{{fieldConfig?.amountInDetailsPage?.field_label ? fieldConfig?.amountInDetailsPage?.field_label
              : 'Amount'}}</span>
          </div>
          <div *ngIf="fieldConfig?.invoiceNo?.is_visible" style="min-width:180px;" class="p-2 border-bottom elip card-header">
            {{fieldConfig?.invoiceNo?.field_label ? fieldConfig?.invoiceNo.field_label : 'Invoice No.'}}
          </div>

          <div *ngIf="fieldConfig?.invoiceDate?.is_visible" style="min-width:180px;" class="p-2 border-bottom elip card-header">
            {{fieldConfig?.invoiceDate?.field_label ? fieldConfig?.invoiceDate.field_label : 'Receipt Date .'}}
          </div>

          <div style="min-width:180px;" class="p-2 border-bottom elip card-header">
            <span> {{fieldConfig?.description?.field_label ? fieldConfig?.description.field_label :
              'Description'}}</span>
          </div>
          <div style="min-width:180px;" class="p-2 border-bottom card-header">
            <span>{{fieldConfig?.attachment?.field_label ? fieldConfig?.attachment.field_label :
              'Attachment'}}</span>
          </div>
          <div style="min-width:180px;" class="p-2 border-bottom card-header">
            <span>{{fieldConfig?.peopleInvolved?.field_label ? fieldConfig?.peopleInvolved.field_label : 'People
              Involved'}}</span>
          </div>
        </div>
        <ng-container *ngIf="loading == false" formArrayName="approvalItems">
          <div class="d-flex" *ngFor="
          let item of approvalItemFormData.approvalItems['controls'];
          let i = index
        " [formGroupName]="i" style="height: 37px;">
            <div class="d-flex">
              <div style="min-width:180px;" class="pt-2 pl-2 border-bottom">
                <app-expense-currency [fieldConfig]="fieldConfig" [currencyList]="
                parseJsonAmount(
                                approvalItemForm.value.approvalItems[i].item_amount
                              )
                            " class="flex-1" type="simple" [taxPercentage]="approvalItemForm.value.approvalItems[i].taxPercentage"
                            [isToShowTooltip]="isToShowTooltip"></app-expense-currency>
              </div>
              <div *ngIf="fieldConfig?.invoiceNo?.is_visible" style="min-width:180px;" class="p-2 border-bottom elip">
                <span *ngIf="approvalItemForm.value.approvalItems[i].invoice_no" [matTooltip]="approvalItemForm.value.approvalItems[i]?.invoice_no">
                  {{ approvalItemForm.value.approvalItems[i]?.invoice_no }}
                </span>
                <span *ngIf="!approvalItemForm.value.approvalItems[i].invoice_no">
                  -
                </span>
              </div>

              <div *ngIf="fieldConfig?.invoiceDate?.is_visible" style="min-width:180px;" class="p-2 border-bottom elip">
                <span *ngIf="approvalItemForm.value.approvalItems[i].invoice_date" [matTooltip]="approvalItemForm.value.approvalItems[i]?.invoice_date | date: 'dd-MMM-yyyy'">
                  {{ approvalItemForm.value.approvalItems[i]?.invoice_date | date: 'dd-MMM-yyyy'}}
                </span>
                <span *ngIf="!approvalItemForm.value.approvalItems[i].invoice_date">
                  -
                </span>
              </div>


              <div style="min-width: 180px; max-width: 180px; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;" 
     class="p-2 border-bottom elip">
  <span class="mt-1" 
        [tooltip]="descItem" 
        content-type="template" 
        max-width="2000" 
        placement="top">
    {{ approvalItemForm.value.approvalItems[i].description }}
    
    <ng-template #descItem>
      {{ approvalItemForm.value.approvalItems[i].description }}
    </ng-template>
  </span>
</div>


              <div style="min-width:180px;" class="border-bottom">
                <div  *ngIf="approvalItemForm.value.approvalItems[i].context_id ; else fileTypeOld">
                  <app-expense-upload-btn
                  [destinationBucket]="'kebs-expenses'"
                  [routingKey]="'expenses'"
                  [contextId]="approvalItemForm.value.approvalItems[i].context_id"
                  [allowEdit]="false"
                  (change)="changeInAttachment($event,i)"
                  [expHeaderId] = "expenseDetail.expense_header_id"
                  [btnColor]="primaryColor"
                  ></app-expense-upload-btn>
                  </div>
                  <ng-template #fileTypeOld>
                    <div  class="pt-2 pb-2"
                  *ngIf="approvalItemForm?.value?.approvalItems[i].attachment?.fileName; else noAttachmentsFound">
                  <span class="mt-1" style="cursor: pointer;" *ngIf="
                      !approvalItemForm.value.approvalItems[i]
                        .isAttachmentLoading
                    " (click)="openFile(i,approvalItemForm.value.approvalItems[i].attachment)" [matTooltip]="
                      approvalItemForm.value.approvalItems[i].attachment
                        .fileName
                    ">
                    {{
                    approvalItemForm.value.approvalItems[i].attachment
                    .fileName
                    }}</span>
                  <mat-spinner *ngIf="
                      approvalItemForm.value.approvalItems[i]
                        .isAttachmentLoading
                    " diameter="20" class="m-auto"></mat-spinner>
                </div>
                  </ng-template>
                  <ng-template #noAttachmentsFound>
                    <div style="width:100%" class="pt-2">
                      <span style="padding-left:12px !important;padding-right:18px !important;"
                        class="content people-involved-counter">
                        {{ 'No Attachment Found !'}}
                      </span>
                    </div>
                  </ng-template>
              </div>
              <div style="min-width:180px;" class="pl-2 border-bottom">
                <span class="images-container stack-from-left" (click)="openExpensePeopleInvolved(approvalItemForm.value.approvalItems[i].people_involved)">
                  <span class="circular-image" *ngFor="let element of approvalItemForm.value.approvalItems[i].people_involved_display_array; let j=index" >
                    <ng-container>
                    <span *ngIf="j<2; else peopleInvolvedCount">
                      <span *ngIf="element.profile_image_url">
                      <app-expense-user-image  content-type="template" placement="top" style="margin: 2px;"
                    [imgUrl]="element.profile_image_url" imgHeight="31px" imgWidth="31px" borderStyle="solid" borderWidth="2px" [borderColor]=" 'white' ">
                    </app-expense-user-image>
                    <ng-template #peopleInvolvedTooltip>
                      <div class="row tooltip-text">{{ element.displayName }}</div>
                      <div class="row tooltip-text">AID : {{ element.associate_id }}</div>
                    </ng-template>
                  </span>
                  <span *ngIf="!element.profile_image_url">
                    <app-user-image  [id]="element.oid" imgWidth="31px" imgHeight="31px"></app-user-image>
                    <ng-template #peopleInvolvedTooltip>
                      <div class="row tooltip-text">{{ element.displayName }}</div>
                      <div class="row tooltip-text">AID : {{ element.associate_id }}</div>
                    </ng-template>
                  </span>
                    </span>
                    <ng-template #peopleInvolvedCount>
                      <div *ngIf="approvalItemForm.value.approvalItems[i].people_involved.length -2 > 0" class="pt-1 d-flex justify-content-center people-involved-counter">
                        <span style="font-size: 14px;">+{{approvalItemForm.value.approvalItems[i].people_involved.length -2}}</span>
                      </div>
                    </ng-template>
                    </ng-container>
                </span>
              </span>
              <div *ngIf="!approvalItemForm.value.approvalItems[i].people_involved || approvalItemForm.value.approvalItems[i].people_involved==' ' " class="pl-3"> - </div>
              </div>
            </div>
          </div>
        </ng-container>
      </div>
      <div style="width:16%;">
        <div class="p-2 border-bottom card-header" style="border-left: 1px solid #b9c0ca;">{{fieldConfig?.approverStatus?.field_label ? fieldConfig?.approverStatus.field_label : 'Approver status'}}</div>
        <ng-container *ngIf="loading == false" formArrayName="approvalItems">
          <div class="d-flex border-bottom" style="border-left: 1px solid #b9c0ca;" *ngFor="
            let item of approvalItemFormData.approvalItems['controls'];
            let i = index
          " [formGroupName]="i">
            <div>
              <div class="d-flex align-items-center content p-2 elip"
                [ngStyle]="{'color': getTxtColor(approvalItemForm.value.approvalItems[i].cc_status)}" content-type="template"
                [tooltip]="expRejectCommentTooltip" *ngIf="approvalItemForm.value.approvalItems[i].cc_reject_comment" [max-width]="1200" placement="bottom">
                  <span class="pr-2">{{ approvalItemForm.value.approvalItems[i].cc_status }}</span>
                  <img src="https://assets.kebs.app/message-notifSVG.svg" style="height: 18px;">
              </div>
        
              <div class="d-flex align-items-center content p-2 elip"
                [ngStyle]="{'color': getTxtColor(approvalItemForm.value.approvalItems[i].cc_status)}"
                *ngIf="approvalItemForm.value.approvalItems[i].cc_reject_comment == null">
                {{ approvalItemForm.value.approvalItems[i].cc_status }}
              </div>
              <ng-template #expRejectCommentTooltip>
                <div style="width: 380px; background-color: #111434 !important; padding: 0% !important; margin: 0% !important;"
                      class="pb-2">
                      <div class="row d-flex pt-3 pl-4">
                        <div class="col p-0 tooltip-class-header"><span>Rejected By</span></div>
                        <div class="col p-0 tooltip-class-header">Rejected On</div>
                      </div>
                      <div class="row d-flex pt-1 pl-4">
                        <div class="col p-0 tooltip-class">
                          <span>{{ approvalItemForm.value.approvalItems[i].cc_reject_comment.rejected_by }}</span>
                        </div>
                        <div class="col p-0 tooltip-class">
                          <span>{{ approvalItemForm.value.approvalItems[i].cc_reject_comment.rejected_on | date: 'dd-MMM-yyyy' }}</span>
                        </div>
                      </div>
                      <div class="row d-flex pt-3 pl-4">
                        <div class="col p-0 tooltip-class-header"><span>Reason</span></div>
                      </div>
                      <div class="row d-flex pt-1 pl-4 pr-4 pb-3">
                        <div class="col p-0 tooltip-class">
                          <span>{{approvalItemForm.value.approvalItems[i].cc_reject_comment.comment}}</span>
                        </div>
                      </div>
                    </div>
              </ng-template>
            </div>
          </div>
        </ng-container>
      </div>
      <div style="width:16%;">
        <div class="p-2 border-bottom elip card-header">{{fieldConfig?.treasuryStatus?.field_label ? fieldConfig?.treasuryStatus?.field_label : 'Treasury status'}}</div>
        <div>
        <ng-container *ngIf="loading == false" formArrayName="approvalItems">
          <div class="d-flex border-bottom" *ngFor="
            let item of approvalItemFormData.approvalItems['controls'];
            let i = index
          " [formGroupName]="i">
          <div class="d-flex align-items-center content pt-2 pb-2" *ngIf="
                      approvalItemForm.value.approvalItems[i].cc_status ==
                      'Approved';
                    ">
                    <div *ngIf="
                        approvalItemForm.value.approvalItems[i].t_is_curr_appr ===
                          1 &&
                          approvalItemForm.value.approvalItems[i]
                            .treasuryStatus === 'Submitted';
                        else showStatus
                      ">
                      <span>Awaiting Approval</span>

                    </div>
                    </div>          
                    <!-- <div class="d-flex align-items-center content pt-2 pb-2" 
                    *ngIf="approvalItemForm.value.approvalItems[i].cc_status == 'Approved'; 
                            else showStatus">
                  </div> -->
         
                  <ng-template #showStatus>
                    <div class="mt-2"
                      [ngStyle]="{'color': getTxtColor(approvalItemForm.value.approvalItems[i].treasuryStatus)}"
                      content-type="template" [tooltip]="expRejectCommentTreasurerTooltip"
                      *ngIf="approvalItemForm.value.approvalItems[i].t_reject_comment" [max-width]="1200" placement="bottom">
                      <span class="pr-2">{{approvalItemForm.value.approvalItems[i].treasuryStatus}}</span>
                      <img src="https://assets.kebs.app/message-notifSVG.svg" style="height: 18px;">
                    </div>

                    <div class="mt-2"
                      [ngStyle]="{'color': getTxtColor(approvalItemForm.value.approvalItems[i].treasuryStatus)}"
                      *ngIf="approvalItemForm.value.approvalItems[i].t_reject_comment == null">{{
                      approvalItemForm.value.approvalItems[i].treasuryStatus}}</div>
                  </ng-template>

                  <ng-template #expRejectCommentTreasurerTooltip>
                    <div style="width: 380px; background-color: #111434 !important; padding: 0% !important; margin: 0% !important;"
                    class="pb-2">
                    <div class="row d-flex pt-3 pl-4">
                      <div class="col p-0 tooltip-class-header"><span>Rejected By</span></div>
                      <div class="col p-0 tooltip-class-header">Rejected On</div>
                    </div>
                    <div class="row d-flex pt-1 pl-4">
                      <div class="col p-0 tooltip-class">
                        <span>{{ approvalItemForm.value.approvalItems[i].t_reject_comment.rejected_by }}</span>
                      </div>
                      <div class="col p-0 tooltip-class">
                        <span>{{ approvalItemForm.value.approvalItems[i].t_reject_comment.rejected_on | date: 'dd-MMM-yyyy' }}</span>
                      </div>
                    </div>
                    <div class="row d-flex pt-3 pl-4">
                      <div class="col p-0 tooltip-class-header"><span>Reason</span></div>
                    </div>
                    <div class="row d-flex pt-1 pl-4 pr-4 pb-3">
                      <div class="col p-0 tooltip-class">
                        <span>{{approvalItemForm.value.approvalItems[i].t_reject_comment.comment}}</span>
                      </div>
                    </div>
                  </div>
                  </ng-template>
                  <div class="d-flex align-items-center content pt-1 pb-2" 
                  *ngIf="approvalItemForm.value.approvalItems[i].cc_status !='Approved'"
                  [ngStyle]="{'color': getTxtColor(approvalItemForm.value.approvalItems[i].treasuryStatus)}" 
                 style="height: 36px;">
                <span>{{approvalItemForm.value.approvalItems[i].treasuryStatus}}</span>

               </div>
                  </div>
                  </ng-container>
        </div>
      </div>
      <ng-container *ngIf="loading == true">
        <div class="row divider sticky-top bg-white">
          <div class="col-3 table-heading pr-0 pt-2"></div>
          <div class="col-1 table-heading pt-2 pl-0 d-flex"></div>
          <div class="col-2 table-heading d-flex pt-2"></div>
          <div class="col-2 table-heading d-flex pb-2 pt-2">
            <mat-spinner diameter="20" class="mt-2"></mat-spinner>
          </div>
          <div class="col-2 table-heading d-flex pt-2"></div>
          <div class="col-2 table-heading d-flex pt-2"></div>
        </div>
      </ng-container>
    </div>
    
    <!-- <div style="width: 100%; overflow-x: auto;">
      <div class="row d-flex" style="min-width: 1080px;">
        <div style="width: 180px; position: sticky; left: 0; z-index: 1; background-color: white;">
          {{fieldConfig?.category?.field_label ? fieldConfig?.category?.field_label : 'Category'}} 
        </div>
    
        <div style="display: flex; overflow-x: auto; white-space: nowrap;">
          <div style="width: 180px;">
            <span class="mt-1">{{fieldConfig?.amountInDetailsPage?.field_label ? fieldConfig?.amountInDetailsPage?.field_label : 'Amount'}}</span>
          </div>
          <div style="width: 180px;">
            {{fieldConfig?.invoiceNo?.field_label ? fieldConfig?.invoiceNo.field_label : 'Invoice No.'}}
          </div>
          <div style="width: 180px;">
            <span class="mt-1"> {{fieldConfig?.description?.field_label ? fieldConfig?.description.field_label : 'Description'}}</span>
          </div>
          <div style="width: 180px;">
            <span class="mt-1">{{fieldConfig?.attachment?.field_label ? fieldConfig?.attachment.field_label : 'Attachment'}}</span>
          </div>
          <div style="width: 180px;">
            <span class="mt-1">{{fieldConfig?.peopleInvolved?.field_label ? fieldConfig?.peopleInvolved.field_label : 'People Involved'}}</span>
          </div>
        </div>
    
        <div style="width: 180px; position: sticky; right: 0; z-index: 1; background-color: white;">
          <div style="width: 180px;">
            <span class="mt-1">{{fieldConfig?.approverStatus?.field_label ? fieldConfig?.approverStatus.field_label : 'Approver status'}}</span>
          </div>
          <div style="width: 180px;">
            <span class="mt-1">{{fieldConfig?.treasuryStatus?.field_label ? fieldConfig?.treasuryStatus?.field_label : 'Treasury status'}}</span>
          </div>
        </div>
      </div>
    </div> -->
    <!-- End of Submitted items -->
  </form>
  </div>
  <!-- End of Submitted items -->
  <div *ngIf="expenseDetail?.status === 'Approved'" class="row" style="height: 10px;">
    <div class="col-12" style="padding-left: 6px;">
      <div class="row">
        <div [appStatusLine]="expenseDetail?.status" [indicator]="2" class="col-12"></div>
      </div>
    </div>
  </div>
  <div *ngIf="expenseDetail?.status === 'Approved'" class="row" style="height: 10px; position: absolute;">
    <div class="col-12" style="padding-left: 6px;">
      <div class="row">
        <div [appStatusLine]="expenseDetail?.status" [indicator]="2" class="col-12"></div>
      </div>
    </div>
  </div>
  <!-- line ends -->
  
  <!-- Verified status -->
  <div class="row">
    <div class="d-flex">
      <div [appStatusColor]="expenseDetail?.status" [indicator]="3" class="status-circular-in-thread"></div>
      <span class="status-in-thread ml-3 mr-2">
        {{ getStatusText(3, expenseDetail?.status) }}
      </span>
      <span class="status-in-thread">
        {{
        expenseDetail.t_action_on ? (expenseDetail.t_action_on | date: 'dd-MMM-yyyy') : ""
        }}
      </span>
    </div>
  </div>
  
  <!-- single line -->
  <div class="row" style="height: 10px; position: absolute;"
    *ngIf="checkCommentVisibility(3, expenseDetail?.status) == true">
    <div class="col-12" style="padding-left: 6px;">
      <div class="row">
        <div [appStatusLine]="expenseDetail?.status" [indicator]="3" class="col-12"></div>
      </div>
    </div>
  </div>
  
  <!-- single line ends -->
  <!-- Submitted items -->
  <div *ngIf="(expenseDetail?.status === 'Verified'|| expenseDetail?.status === 'Closed') && showExpenseCard == true">
    <form [formGroup]="approvalItemForm">
      <div class="d-flex mt-3" style="box-shadow: 0 3px 9px 1px rgba(0, 0, 0, .2), 0 1px 1px 0 rgba(0, 0, 0, .14), 0 1px 3px 0 rgba(0, 0, 0, .12);
      border: 1px solid #e3dfdf;
        border-radius: 4px;
        background: white;">
        <div style="width:18%;">
          <div class="p-2 card-header border-bottom" style="border-right: 1px solid #b9c0ca;">{{fieldConfig?.category?.field_label ? fieldConfig?.category?.field_label : 'Category'}}</div>
          <ng-container *ngIf="loading == false" formArrayName="approvalItems">
            <div class="d-flex" *ngFor="
            let item of approvalItemFormData.approvalItems['controls'];
            let i = index
          " [formGroupName]="i" style="border-right: 1px solid #b9c0ca;">
              <div class="p-2 elip border-bottom" [matTooltip]="approvalItemForm.value.approvalItems[i].expense_category_name" style="width: 100%">
                            {{
                            approvalItemForm.value.approvalItems[i]
                            .expense_category_name
                            }}
              </div>
              
              <ng-container *ngIf="(approvalItemForm?.value?.approvalItems[i].expense_category_id === 18 || 
              approvalItemForm?.value?.approvalItems[i].expense_category_id === 8) &&  
              (approvalItemForm.value.approvalItems[i].from_location ||
              approvalItemForm.value.approvalItems[i].to_location ||
              approvalItemForm.value.approvalItems[i].vehicle_type ||
              approvalItemForm.value.approvalItems[i].vehicle_engine_type ||
              approvalItemForm.value.approvalItems[i].miles ||
              approvalItemForm.value.approvalItems[i].perdiem_start_date  ||
              approvalItemForm.value.approvalItems[i].perdiem_start_time ||
              approvalItemForm.value.approvalItems[i].perdiem_end_date ||
              approvalItemForm.value.approvalItems[i].perdiem_end_time ||
              approvalItemForm.value.approvalItems[i].days ||
              approvalItemForm.value.approvalItems[i].location)">
              <span>
                <button mat-icon-button 
                style="margin-left: 5px; padding: 0; height: 20px; width: 20px; display: inline-flex; align-items: center;"
                (click)="toggleOverlay(i)" #triggerOriginRef="cdkOverlayOrigin" 
                    [cdkOverlayOrigin]="triggerOriginRef"
                    style="height: 14px; width: 25px;">
                    <mat-icon style="font-size: 14px; color: #474444;">
                        info
                    </mat-icon>
                </button>
            </span>
            
            <ng-template
                cdkConnectedOverlay
                [cdkConnectedOverlayOrigin]="triggerOriginRef"
                [cdkConnectedOverlayOpen]="openOverlayIndex === i"
                [cdkConnectedOverlayHasBackdrop]="true"
                (backdropClick)="openOverlayIndex = null"
                cdkConnectedOverlayBackdropClass="overLayClass"
                [cdkConnectedOverlayFlexibleDimensions]="true"
                [cdkConnectedOverlayPositions]="[
                    { originX: 'start', originY: 'bottom', overlayX: 'start', overlayY: 'top' },
                    { originX: 'start', originY: 'top', overlayX: 'start', overlayY: 'bottom' }
                ]">
                
                <div class="card balance-planned-log-pop-over">
                    <!-- Header Section -->
                    <div class="header">
                        <span class="width-class">Claim Details</span>
                        <span class="close-icon">
                            <mat-icon style="font-size: 18px; cursor: pointer;" (click)="openOverlayIndex = null">close</mat-icon>
                        </span>
                    </div>
                    
                    <hr class="divider" >

                    <div *ngIf="approvalItemForm.value.approvalItems[i].expense_category_id === 18">
                    <!-- Claim Details -->
                   
                    <div class="row" *ngIf= "fieldConfig?.fromLocation?.is_to_show_in_treasury_details">
                      <div class="col-5 label-class"> {{ fieldConfig?.fromLocation?.field_label ? fieldConfig?.fromLocation?.field_label : 'From Location' }}</div>
                      <div class="col-7 value-class">{{ approvalItemForm.value.approvalItems[i].from_location || '-' }}</div>
                  </div>
            
                    <div class="row" *ngIf= "fieldConfig?.toLocation?.is_to_show_in_treasury_details">
                        <div class="col-5 label-class"> {{ fieldConfig?.toLocation?.field_label ? fieldConfig?.toLocation?.field_label : 'To Location' }}</div>
                        <div class="col-7 value-class">{{ approvalItemForm.value.approvalItems[i].to_location || '-' }}</div>
                    </div>
            
                    <div class="row" *ngIf= "fieldConfig?.vehicleType?.is_to_show_in_treasury_details">
                        <div class="col-5 label-class">{{ fieldConfig?.vehicleType?.field_label ? fieldConfig?.vehicleType?.field_label : 'Vehicle Type' }}</div>
                        <div class="col-7 value-class">{{ approvalItemForm.value.approvalItems[i].vehicle_type || '-' }}</div>
                    </div>
            
                    <div class="row" *ngIf= "fieldConfig?.vehicleEngineType?.is_to_show_in_treasury_details">
                        <div class="col-5 label-class">{{ fieldConfig?.vehicleEngineType?.field_label ? fieldConfig?.vehicleEngineType?.field_label : 'Vehicle Engine Type' }}</div>
                        <div class="col-7 value-class">{{ approvalItemForm.value.approvalItems[i].vehicle_engine_type || '-' }}</div>
                    </div>

                    <div class="row" *ngIf= "fieldConfig?.miles?.is_to_show_in_treasury_details">
                      <div class="col-5 label-class">{{ fieldConfig?.miles?.field_label ? fieldConfig?.miles?.field_label : 'Miles' }}</div>
                      <div class="col-7 value-class">{{ approvalItemForm.value.approvalItems[i].miles || '-' }}  {{ approvalItemForm.value.approvalItems[i].unit || '-' }} </div>
                  </div>
                </div>

                <div *ngIf="approvalItemForm.value.approvalItems[i].expense_category_id === 8">
                  <!-- Claim Details -->
                   
                  <div class="row" *ngIf= "fieldConfig?.perDiemstartDate?.is_to_show_in_treasury_details">
                      <div class="col-5 label-class"> {{ fieldConfig?.perDiemstartDate?.field_label ? fieldConfig?.perDiemstartDate?.field_label : 'Per Diem Start Date' }}</div>
                      <div class="col-7 value-class">{{ approvalItemForm.value.approvalItems[i].perdiem_start_date || '-' | date: 'dd-MMM-yyyy'}}</div>
                  </div>

                  <div class="row" *ngIf= "fieldConfig?.perDiemStartTime?.is_to_show_in_treasury_details">
                    <div class="col-5 label-class"> {{ fieldConfig?.perDiemStartTime?.field_label ? fieldConfig?.perDiemStartTime?.field_label : 'Ped Diem Start Time' }}</div>
                    <div class="col-7 value-class">{{ approvalItemForm.value.approvalItems[i].perdiem_start_time || '-' }}</div>
                </div>
          
                  <div class="row" *ngIf= "fieldConfig?.perDiemEndDate?.is_to_show_in_treasury_details">
                      <div class="col-5 label-class"> {{ fieldConfig?.perDiemEndDate?.field_label ? fieldConfig?.perDiemEndDate?.field_label : 'Per Diem End Date' }}</div>
                      <div class="col-7 value-class">{{ approvalItemForm.value.approvalItems[i].perdiem_end_date || '-' | date: 'dd-MMM-yyyy' }}</div>
                  </div>
          
                  <div class="row" *ngIf= "fieldConfig?.perDiemEndTime?.is_to_show_in_treasury_details">
                      <div class="col-5 label-class">{{ fieldConfig?.perDiemEndTime?.field_label ? fieldConfig?.perDiemEndTime?.field_label : 'Per Diem End Time' }}</div>
                      <div class="col-7 value-class">{{ approvalItemForm.value.approvalItems[i].perdiem_end_time || '-' }}</div>
                  </div>
          
                  <div class="row" *ngIf= "fieldConfig?.days?.is_to_show_in_treasury_details">
                      <div class="col-5 label-class">{{ fieldConfig?.days?.field_label ? fieldConfig?.days?.field_label : 'Days' }}</div>
                      <div class="col-7 value-class">{{ approvalItemForm.value.approvalItems[i].days || '-' }}</div>
                  </div>

                  <div class="row" *ngIf= "fieldConfig?.location?.is_to_show_in_treasury_details">
                    <div class="col-5 label-class">{{ fieldConfig?.location?.field_label ? fieldConfig?.location?.field_label : 'Location' }}</div>
                    <div class="col-7 value-class">{{ approvalItemForm.value.approvalItems[i].location || '-' }}</div>
                </div>
             
              </div>
                  
                </div>
            </ng-template>
              </ng-container>
            </div>
          </ng-container>
        </div>
        <div style="overflow-x: auto; width: 50%; overflow-y: hidden;">
          <div class="d-flex">
            <div style="min-width:180px;" class="p-2 border-bottom card-header">
              <span>{{fieldConfig?.amountInDetailsPage?.field_label ? fieldConfig?.amountInDetailsPage?.field_label
                : 'Amount'}}</span>
            </div>
            <div *ngIf="fieldConfig?.invoiceNo?.is_visible" style="min-width:180px;" class="p-2 border-bottom elip card-header">
              {{fieldConfig?.invoiceNo?.field_label ? fieldConfig?.invoiceNo.field_label : 'Invoice No.'}}
            </div>

            <div *ngIf="fieldConfig?.invoiceDate?.is_visible" style="min-width:180px;" class="p-2 border-bottom elip card-header">
              {{fieldConfig?.invoiceDate?.field_label ? fieldConfig?.invoiceDate.field_label : 'Receipt Date .'}}
            </div>

            <div style="min-width:180px;" class="p-2 border-bottom elip card-header">
              <span> {{fieldConfig?.description?.field_label ? fieldConfig?.description.field_label :
                'Description'}}</span>
            </div>
            <div style="min-width:180px;" class="p-2 border-bottom card-header">
              <span>{{fieldConfig?.attachment?.field_label ? fieldConfig?.attachment.field_label :
                'Attachment'}}</span>
            </div>
            <div style="min-width:180px;" class="p-2 border-bottom card-header">
              <span>{{fieldConfig?.peopleInvolved?.field_label ? fieldConfig?.peopleInvolved.field_label : 'People
                Involved'}}</span>
            </div>
          </div>
          <ng-container *ngIf="loading == false" formArrayName="approvalItems">
            <div class="d-flex" *ngFor="
            let item of approvalItemFormData.approvalItems['controls'];
            let i = index
          " [formGroupName]="i" style="height: 36px;">
              <div class="d-flex">
                <div style="min-width:180px;" class="pt-2 pl-2 pr-2 border-bottom">
                  <app-expense-currency [fieldConfig]="fieldConfig" [currencyList]="
                  parseJsonAmount(
                                  approvalItemForm.value.approvalItems[i].item_amount
                                )
                              " class="flex-1" type="simple"
                              [taxPercentage]="approvalItemForm.value.approvalItems[i].taxPercentage"
                    [isToShowTooltip]="isToShowTooltip"></app-expense-currency>
                </div>
                <div *ngIf="fieldConfig?.invoiceNo?.is_visible" style="min-width:180px;" class="pt-2 pl-2 pr-2 border-bottom elip">
                  <span *ngIf="approvalItemForm.value.approvalItems[i].invoice_no" [matTooltip]="approvalItemForm.value.approvalItems[i]?.invoice_no">
                    {{ approvalItemForm.value.approvalItems[i]?.invoice_no }}
                  </span>
                  <span *ngIf="!approvalItemForm.value.approvalItems[i].invoice_no">
                    -
                  </span>
                </div>

                <div *ngIf="fieldConfig?.invoiceDate?.is_visible" style="min-width:180px;" class="pt-2 pl-2 pr-2 border-bottom elip">
                  <span *ngIf="approvalItemForm.value.approvalItems[i].invoice_date" [matTooltip]="approvalItemForm.value.approvalItems[i]?.invoice_date | date: 'dd-MMM-yyyy'">
                    {{ approvalItemForm.value.approvalItems[i]?.invoice_date | date: 'dd-MMM-yyyy'}}
                  </span>
                  <span *ngIf="!approvalItemForm.value.approvalItems[i].invoice_date">
                    -
                  </span>
                </div>

                <div style="min-width: 180px; max-width: 180px; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;" 
     class="pt-2 pl-2 pr-2 border-bottom elip">
                  <span class="mt-1" [tooltip]="descItem" content-type="template" max-width="2000" placement="top">{{
                    approvalItemForm.value.approvalItems[i].description
                    }}
                    <ng-template #descItem>
                      {{
                      approvalItemForm.value.approvalItems[i].description
                      }}
                    </ng-template>
                    </span>
                </div>
                <div style="min-width:180px;" class="border-bottom">
                  <div  *ngIf="approvalItemForm.value.approvalItems[i].context_id ; else fileTypeOld">
                    <app-expense-upload-btn
                    [destinationBucket]="'kebs-expenses'"
                    [routingKey]="'expenses'"
                    [contextId]="approvalItemForm.value.approvalItems[i].context_id"
                    [allowEdit]="false"
                    (change)="changeInAttachment($event,i)"
                    [expHeaderId] = "expenseDetail.expense_header_id"
                    [btnColor]="primaryColor"
                    ></app-expense-upload-btn>
                    </div>
                    <ng-template #fileTypeOld>
                      <div  class="pt-2 pb-2"
                    *ngIf="approvalItemForm?.value?.approvalItems[i].attachment?.fileName; else noAttachmentsFound">
                    <span class="mt-1" style="cursor: pointer;" *ngIf="
                        !approvalItemForm.value.approvalItems[i]
                          .isAttachmentLoading
                      " (click)="openFile(i,approvalItemForm.value.approvalItems[i].attachment)" [matTooltip]="
                        approvalItemForm.value.approvalItems[i].attachment
                          .fileName
                      ">
                      {{
                      approvalItemForm.value.approvalItems[i].attachment
                      .fileName
                      }}</span>
                    <mat-spinner *ngIf="
                        approvalItemForm.value.approvalItems[i]
                          .isAttachmentLoading
                      " diameter="20" class="m-auto"></mat-spinner>
                  </div>
                    </ng-template>
                    <ng-template #noAttachmentsFound>
                      <div style="width:100%" class="pt-2">
                        <span style="padding-left:12px !important;padding-right:18px !important;"
                          class="content people-involved-counter">
                          {{ 'No Attachment Found !'}}
                        </span>
                      </div>
                    </ng-template>
                    </div>
                <div style="min-width:180px;" class="pl-2 pr-2 border-bottom">
                  <span class="images-container stack-from-left" (click)="openExpensePeopleInvolved(approvalItemForm.value.approvalItems[i].people_involved)">
                    <span class="circular-image" *ngFor="let element of approvalItemForm.value.approvalItems[i].people_involved_display_array; let j=index" >
                      <ng-container>
                      <span *ngIf="j<2; else peopleInvolvedCount">
                        <span *ngIf="element.profile_image_url">
                        <app-expense-user-image  content-type="template" placement="top" style="margin: 2px;"
                      [imgUrl]="element.profile_image_url" imgHeight="31px" imgWidth="31px" borderStyle="solid" borderWidth="2px" [borderColor]=" 'white' ">
                      </app-expense-user-image>
                      <ng-template #peopleInvolvedTooltip>
                        <div class="row tooltip-text">{{ element.displayName }}</div>
                        <div class="row tooltip-text">AID : {{ element.associate_id }}</div>
                      </ng-template>
                    </span>
                    <span *ngIf="!element.profile_image_url">
                      <app-user-image  [id]="element.oid" imgWidth="31px" imgHeight="31px"></app-user-image>
                      <ng-template #peopleInvolvedTooltip>
                        <div class="row tooltip-text">{{ element.displayName }}</div>
                        <div class="row tooltip-text">AID : {{ element.associate_id }}</div>
                      </ng-template>
                    </span>
                      </span>
                      <ng-template #peopleInvolvedCount>
                        <div *ngIf="approvalItemForm.value.approvalItems[i].people_involved.length -2 > 0" class="pt-1 d-flex justify-content-center people-involved-counter">
                          <span style="font-size: 14px;">+{{approvalItemForm.value.approvalItems[i].people_involved.length -2}}</span>
                        </div>
                      </ng-template>
                      </ng-container>
                  </span>
                </span>
                <div *ngIf="!approvalItemForm.value.approvalItems[i].people_involved || approvalItemForm.value.approvalItems[i].people_involved==' ' " class="pl-3"> - </div>
                </div>
              </div>
            </div>
          </ng-container>
        </div>
        <div style="width:16%;">
          <div class="p-2 border-bottom card-header" style="border-left: 1px solid #b9c0ca;">{{fieldConfig?.approverStatus?.field_label ? fieldConfig?.approverStatus.field_label : 'Approver status'}}</div>
          <ng-container *ngIf="loading == false" formArrayName="approvalItems">
            <div class="d-flex border-bottom" style="border-left: 1px solid #b9c0ca;" *ngFor="
              let item of approvalItemFormData.approvalItems['controls'];
              let i = index
            " [formGroupName]="i">
              <div>
                <div class="d-flex align-items-center content p-2 elip"
                  [ngStyle]="{'color': getTxtColor(approvalItemForm.value.approvalItems[i].cc_status)}" content-type="template"
                  [tooltip]="expRejectCommentTooltip" *ngIf="approvalItemForm.value.approvalItems[i].cc_reject_comment" [max-width]="1200" placement="bottom">
                  <span class="pr-2">{{ approvalItemForm.value.approvalItems[i].cc_status }}</span>
                  <img src="https://assets.kebs.app/message-notifSVG.svg" style="height: 18px;">
                </div>
          
                <div class="d-flex align-items-center content p-2 elip"
                  [ngStyle]="{'color': getTxtColor(approvalItemForm.value.approvalItems[i].cc_status)}"
                  *ngIf="approvalItemForm.value.approvalItems[i].cc_reject_comment == null">
                  {{ approvalItemForm.value.approvalItems[i].cc_status }}
                </div>
                <ng-template #expRejectCommentTooltip>
                  <div style="width: 380px; background-color: #111434 !important; padding: 0% !important; margin: 0% !important;"
                      class="pb-2">
                      <div class="row d-flex pt-3 pl-4">
                        <div class="col p-0 tooltip-class-header"><span>Rejected By</span></div>
                        <div class="col p-0 tooltip-class-header">Rejected On</div>
                      </div>
                      <div class="row d-flex pt-1 pl-4">
                        <div class="col p-0 tooltip-class">
                          <span>{{ approvalItemForm.value.approvalItems[i].cc_reject_comment.rejected_by }}</span>
                        </div>
                        <div class="col p-0 tooltip-class">
                          <span>{{ approvalItemForm.value.approvalItems[i].cc_reject_comment.rejected_on | date: 'dd-MMM-yyyy' }}</span>
                        </div>
                      </div>
                      <div class="row d-flex pt-3 pl-4">
                        <div class="col p-0 tooltip-class-header"><span>Reason</span></div>
                      </div>
                      <div class="row d-flex pt-1 pl-4 pr-4 pb-3">
                        <div class="col p-0 tooltip-class">
                          <span>{{approvalItemForm.value.approvalItems[i].cc_reject_comment.comment}}</span>
                        </div>
                      </div>
                    </div>
                </ng-template>
              </div>
            </div>
          </ng-container>
        </div>
        <div style="width:16%;">
          <div class="p-2 border-bottom elip card-header">{{fieldConfig?.treasuryStatus?.field_label ? fieldConfig?.treasuryStatus?.field_label : 'Treasury status'}}</div>
          <div>
          <ng-container *ngIf="loading == false" formArrayName="approvalItems">
            <div class="d-flex border-bottom" *ngFor="
              let item of approvalItemFormData.approvalItems['controls'];
              let i = index
            " [formGroupName]="i">
            <div class="d-flex align-items-center content pb-2" *ngIf="
                        approvalItemForm.value.approvalItems[i].cc_status ==
                        'Approved';
                      ">
                      <div *ngIf="
                      (approvalItemForm.value.approvalItems[i].t_is_curr_appr === 1 || expenseDetail.adminView) && 
                      approvalItemForm.value.approvalItems[i].treasuryStatus === 'Submitted';   
                          else showStatus
                        ">
                        <span>Awaiting Approval</span>

                      </div>
                      </div>          
                      <!-- <div class="d-flex align-items-center content pt-2 pb-2" 
                      *ngIf="approvalItemForm.value.approvalItems[i].cc_status == 'Approved'; 
                              else showStatus">
                    </div> -->
           
                    <ng-template #showStatus>
                      <div class="mt-2"
                        [ngStyle]="{'color': getTxtColor(approvalItemForm.value.approvalItems[i].treasuryStatus)}"
                        content-type="template" [tooltip]="expRejectCommentTreasurerTooltip"
                        *ngIf="approvalItemForm.value.approvalItems[i].t_reject_comment" [max-width]="1200" placement="bottom">
                        <span class="pr-2">{{approvalItemForm.value.approvalItems[i].treasuryStatus}}</span>
                        <img src="https://assets.kebs.app/message-notifSVG.svg" style="height: 18px;">
                      </div>
  
                      <div class="mt-2"
                        [ngStyle]="{'color': getTxtColor(approvalItemForm.value.approvalItems[i].treasuryStatus)}"
                        *ngIf="approvalItemForm.value.approvalItems[i].t_reject_comment == null">{{
                        approvalItemForm.value.approvalItems[i].treasuryStatus}}</div>
                    </ng-template>
  
                    <ng-template #expRejectCommentTreasurerTooltip>
                      <div style="width: 380px; background-color: #111434 !important; padding: 0% !important; margin: 0% !important;"
                      class="pb-2">
                      <div class="row d-flex pt-3 pl-4">
                        <div class="col p-0 tooltip-class-header"><span>Rejected By</span></div>
                        <div class="col p-0 tooltip-class-header">Rejected On</div>
                      </div>
                      <div class="row d-flex pt-1 pl-4">
                        <div class="col p-0 tooltip-class">
                          <span>{{ approvalItemForm.value.approvalItems[i].t_reject_comment.rejected_by }}</span>
                        </div>
                        <div class="col p-0 tooltip-class">
                          <span>{{ approvalItemForm.value.approvalItems[i].t_reject_comment.rejected_on | date: 'dd-MMM-yyyy' }}</span>
                        </div>
                      </div>
                      <div class="row d-flex pt-3 pl-4">
                        <div class="col p-0 tooltip-class-header"><span>Reason</span></div>
                      </div>
                      <div class="row d-flex pt-1 pl-4 pr-4 pb-3">
                        <div class="col p-0 tooltip-class">
                          <span>{{approvalItemForm.value.approvalItems[i].t_reject_comment.comment}}</span>
                        </div>
                      </div>
                    </div>
                    </ng-template>
                    <div class="d-flex align-items-center content pt-1 pb-2" 
                    *ngIf="approvalItemForm.value.approvalItems[i].cc_status !='Approved'"
                    [ngStyle]="{'color': getTxtColor(approvalItemForm.value.approvalItems[i].treasuryStatus)}" 
                   style="height: 36px;">
                  <span>{{approvalItemForm.value.approvalItems[i].treasuryStatus}}</span>
  
                 </div>
                    </div>
                    </ng-container>
          </div>
        </div>
      </div>
      <ng-container *ngIf="loading == true">
        <div class="row divider sticky-top bg-white">
          <div class="col-3 table-heading pr-0 pt-2"></div>
          <div class="col-1 table-heading pt-2 pl-0 d-flex"></div>
          <div class="col-2 table-heading d-flex pt-2"></div>
          <div class="col-2 table-heading d-flex pb-2 pt-2">
            <mat-spinner diameter="20" class="mt-2"></mat-spinner>
          </div>
          <div class="col-2 table-heading d-flex pt-2"></div>
          <div class="col-2 table-heading d-flex pt-2"></div>
        </div>
      </ng-container>
      <!-- <div style="width: 100%; overflow-x: auto;">
        <div class="row d-flex" style="min-width: 1080px;">
          <div style="width: 180px; position: sticky; left: 0; z-index: 1; background-color: white;">
            {{fieldConfig?.category?.field_label ? fieldConfig?.category?.field_label : 'Category'}} 
          </div>
      
          <div style="display: flex; overflow-x: auto; white-space: nowrap;">
            <div style="width: 180px;">
              <span class="mt-1">{{fieldConfig?.amountInDetailsPage?.field_label ? fieldConfig?.amountInDetailsPage?.field_label : 'Amount'}}</span>
            </div>
            <div style="width: 180px;">
              {{fieldConfig?.invoiceNo?.field_label ? fieldConfig?.invoiceNo.field_label : 'Invoice No.'}}
            </div>
            <div style="width: 180px;">
              <span class="mt-1"> {{fieldConfig?.description?.field_label ? fieldConfig?.description.field_label : 'Description'}}</span>
            </div>
            <div style="width: 180px;">
              <span class="mt-1">{{fieldConfig?.attachment?.field_label ? fieldConfig?.attachment.field_label : 'Attachment'}}</span>
            </div>
            <div style="width: 180px;">
              <span class="mt-1">{{fieldConfig?.peopleInvolved?.field_label ? fieldConfig?.peopleInvolved.field_label : 'People Involved'}}</span>
            </div>
          </div>
      
          <div style="width: 180px; position: sticky; right: 0; z-index: 1; background-color: white;">
            <div style="width: 180px;">
              <span class="mt-1">{{fieldConfig?.approverStatus?.field_label ? fieldConfig?.approverStatus.field_label : 'Approver status'}}</span>
            </div>
            <div style="width: 180px;">
              <span class="mt-1">{{fieldConfig?.treasuryStatus?.field_label ? fieldConfig?.treasuryStatus?.field_label : 'Treasury status'}}</span>
            </div>
          </div>
        </div>
      </div> -->
      <!-- End of Submitted items -->
    </form>
    </div>
    <!-- End of Submitted items -->
  
  <div *ngIf="checkCommentVisibility(3, expenseDetail?.status) == true" class="row pt-2">
    <div class="col-12 pl-0 pr-0">
      <div class="card slide-in-top" *ngIf="expenseDetail.t_action_by">
        <div class="row d-flex pt-2 pb-2">
          <span class="ml-4 my-auto">
            <mat-icon class="done-icon">
              done
            </mat-icon>
          </span>
          <span class="content my-auto ml-3" style="padding-right: 11px;">
            Verified by
          </span>
          <span>
            <app-user-profile *ngIf="expenseDetail" type="expense-card-data" [oid]="expenseDetail.t_action_by"
              imgHeight="28px" imgWidth="28px">
            </app-user-profile>
          </span>
        </div>
      </div>
    </div>
  </div>
  <!--End of Verified status -->
  
  <!-- start of knock-off-history-line -->
  <div class="row" *ngIf="knockOffHistoryDetail.length>0" style="height: 10px; position: absolute;">
    <div class="col-12" style="padding-left: 6px;">
      <div class="row">
        <div [appStatusLine]="expenseDetail?.status" [indicator]="3" class="col-12"></div>
      </div>
    </div>
  </div>
  
  <!-- knockoff history card -->
  <div *ngIf="knockOffHistoryDetail.length>0" class="row pt-2">
    <div class="col-12 pl-0">
      <div class="card slide-in-top">
        <div class="row pt-2">
          <div class="col-12" style="color: #cf0001; font-weight: 500;">
            Knock Off History
          </div>
        </div>
        <div class="row d-flex pt-2 pb-2">
          <div class="col-12">
            <table class="table table-hover">
              <thead style="background: whitesmoke;">
                <tr>
                  <th scope="col">Expense Id</th>
                  <th scope="col">Knock Off Value</th>
                  <th scope="col">Knock Off Balance</th>
                  <th scope="col">Knocked off Date</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let item of knockOffHistoryDetail">
                  <th scope="row"> {{ item.expense_type === "C" ? "CL/" : "AD/"
                    }}{{item.id}}</th>
                    <td>{{item.knocked_off_value}} {{expenseDetail.entity_currency_code}}</td>
                    <td>{{item.knocked_off_balance}} {{expenseDetail.entity_currency_code}}</td>
                    <td>{{item.knocked_off_on | date: 'dd-MMM-yyyy'}}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- end of knock off history card -->
  
  
  
  <!-- line starts -->
  <div class="row" style="height: 10px; position: absolute;"
    *ngIf="checkCommentVisibility(3, expenseDetail?.status) == true">
    <div class="col-12" style="padding-left: 6px;">
      <div class="row">
        <div [appStatusLine]="expenseDetail?.status" [indicator]="3" class="col-12"></div>
      </div>
    </div>
  </div>
  <!-- line ends -->
  <!-- treasurer entries -->
  <div *ngIf="checkCommentVisibility(3, expenseDetail?.status) == true" class="row card pt-2 pb-2">
    <div class="col-12 pl-0">
    <form *ngIf="claimPaymentForm" [formGroup]="claimPaymentForm">
      <div class="row" style="height: 50px;">
        <div class="col-11">
          <mat-icon class="pt-1 info-icon">
            info_outline
          </mat-icon>
          <span class="treasury-title ml-1"> Ledger Entry </span>
        </div>
        <div class="col-1">
       
            <div *ngIf = "expenseDetail?.status != 'Verified' && expenseDetail?.status != 'Closed' && !expenseDetail.adminView ">
            <button mat-icon-button class="trend-button-inactive" matTooltip="save" (click)="savepayments()"
            [disabled]="expenseDetail?.status === 'Verified' || expenseDetail?.status === 'Closed'"
            [ngStyle]="{
              'color': expenseDetail?.status === 'Verified' || expenseDetail?.status === 'Closed' ? 'grey' : '#79BA44'}">
              <mat-icon style = "font-size: 18px;">save</mat-icon>
            </button>
          </div>
          <div *ngIf = "expenseDetail?.status == 'Verified' || expenseDetail?.status == 'Closed' || !expenseDetail.adminView ">
            <button mat-icon-button 
            [disabled]="expenseDetail?.status === 'Verified' || expenseDetail?.status === 'Closed'"
            [ngStyle]="{
              'color': expenseDetail?.status === 'Verified' || expenseDetail?.status === 'Closed' ? 'grey' : '#79BA44'}">
              <mat-icon style = "font-size: 18px;"></mat-icon>
            </button>
          </div>
          
        </div>
      </div>
      <div class="row border-bottom">
        <div class="col-12 pl-0">
          <div class="row">
            <div *ngIf="fieldConfig?.employee?.is_visible" class="col-4">
              <app-input-huge-search [label]="fieldConfig?.employee?.field_label ? fieldConfig?.employee?.field_label : 'Employee *' " [optionLabel]="['employee_name','associate_id']"
               [apiUri]="'/api/exPrimary2/getLedgerAccount'" (ngModelChange)="onledgerAccountChange()"
               [isReadOnly]="expenseDetail.status == 'Verified' || expenseDetail.status == 'Closed' ||
               fieldConfig?.employee?.is_disabled ? true : false"
               [bgColorOnDisable] = "expenseDetail.status == 'Verified' || expenseDetail.status == 'Closed' ||
               fieldConfig?.employee?.is_disabled ? true : false"
                  formControlName="ledgerAccount"></app-input-huge-search>
            </div>
            <div *ngIf="fieldConfig?.narration?.is_visible ? true : false" class="col-8">
              <mat-form-field appearance="outline" class="create-claim-field">
                <mat-label>{{fieldConfig?.narration?.field_label ? fieldConfig?.narration?.field_label : 'Description *'}}</mat-label>
                <input matInput [readonly]="expenseDetail.status == 'Verified' || expenseDetail.status == 'Closed'" 
                [placeholder]="fieldConfig?.narration?.field_label ? fieldConfig?.narration?.field_label : 'Description *'" formControlName="ledgerAccountDescription" />
              </mat-form-field>
            </div>
          </div>
          <div class="row">
            <div *ngIf="fieldConfig?.expenseAccount?.is_visible ? true : false" class="col-4">
              <app-input-huge-search [label]="fieldConfig?.expenseAccount?.field_label ? fieldConfig?.expenseAccount?.field_label : 'Account *' " [optionLabel]="['company_code']"
                [apiUri]="'/api/exPrimary2/getTargetEntity'" 
                [isReadOnly] = "isTargetEntityReadOnly"
                [bgColorOnDisable] = "isTargetEntityReadOnly"
                [isReadOnly]="expenseDetail.status == 'Verified' || expenseDetail.status == 'Closed' ||
                fieldConfig?.expenseAccount?.is_disabled ? true : false"
                [bgColorOnDisable] = "expenseDetail.status == 'Verified' || expenseDetail.status == 'Closed' ||
               fieldConfig?.expenseAccount?.is_disabled ? true : false"
                formControlName="targetEntity">
              </app-input-huge-search>
            </div>
  
            <div *ngIf="fieldConfig?.creditLedger?.is_visible && fieldConfig?.creditLedger?.is_visible && creditLedgerType != 'CORP_CARD'" class="col-3">   
              <mat-form-field appearance="outline" class="create-claim-field"
              [ngClass]="{
                'disabled-field': expenseDetail.status == 'Verified' || expenseDetail.status == 'Closed'}">
                <mat-label>{{fieldConfig?.creditLedger?.field_label ? fieldConfig?.creditLedger?.field_label : 'Credit Ledger *'}}</mat-label>
                <input matInput [placeholder]="fieldConfig?.creditLedger?.field_label ? fieldConfig?.creditLedger?.field_label : 'Credit Ledger *' " formControlName="creditLedger" 
                [readonly]="(expenseDetail.status == 'Verified' || expenseDetail.status == 'Closed') || !(enableCreditLedger)"
                [disabled] = "(expenseDetail.status == 'Verified' || expenseDetail.status == 'Closed') || !(enableCreditLedger)" 
                [matTooltip]="claimPaymentForm.get('creditLedger')?.value || 'Enter Credit Ledger'" />
                <mat-icon matSuffix *ngIf="creditLedgerType == 'EL' && creditLedgerMasterData?.length > 0" [ngStyle]="(expenseDetail.status == 'Verified' || expenseDetail.status == 'Closed') || !(enableCreditLedger) ? {'pointer-events': 'none'} : {'cursor': 'pointer'}"
                [matMenuTriggerFor]="menu" style="vertical-align: top;">expand_more</mat-icon>
                <mat-menu #menu="matMenu">
                  <button mat-menu-item *ngFor="let item of creditLedgerMasterData" (click)="changeCreditLedgervalue(item)">
                    <span>{{item.employee_ledger_name}}</span>
                  </button>
                </mat-menu>
              </mat-form-field>
            </div>

            <div *ngIf="fieldConfig?.creditLedger?.is_visible && creditLedgerType == 'CORP_CARD'" class="col-3">
              <mat-form-field appearance="outline" class="create-claim-field"
              [ngClass]="{
                'disabled-field': expenseDetail.status == 'Verified' || expenseDetail.status == 'Closed'}">
                <mat-label>{{ fieldConfig?.creditLedger?.field_label || 'Credit Ledger *' }}</mat-label>
                <mat-select [formControl]="claimPaymentForm.get('creditLedger')" 
                  placeholder="Credit Ledger"
                  [readonly]="(expenseDetail.status == 'Verified' || expenseDetail.status == 'Closed')"
                  [disabled] = "(expenseDetail.status == 'Verified' || expenseDetail.status == 'Closed')" 
                  [matTooltip]="claimPaymentForm.get('creditLedger')?.value || 'Enter Credit Ledger'">
                  <mat-option *ngFor="let item of creditLedgerMasterData" [value]="item.employee_ledger_name" 
                  (click)="changeCreditLedgervalue(item)">
                    {{ item.employee_ledger_name }}
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </div>     
            
            <div *ngIf="fieldConfig?.costCenterCode?.is_visible ? fieldConfig?.costCenterCode?.is_visible : 0" class="col-3">
              <app-exp-input-search class="create-claim-field-inputsearch font-family-class"
              [placeholder]="fieldConfig?.costCenterCode?.field_label ? fieldConfig.costCenterCode.field_label + ' *' : 'Cost Center *'"
                [list]="costCenterList" formControlName="costCenterCode" (change)='changeCostCenter($event)'
                [bgColorOnDisable]="expenseDetail.status == 'Verified' || expenseDetail.status == 'Closed'" 
                [disabled]="expenseDetail.status == 'Verified' || expenseDetail.status == 'Closed'"
                [hideMatLabel]="false">
              </app-exp-input-search>
            </div>

            <div *ngIf="costCentreType == 'I' && fieldConfig?.customerBilling?.is_visible ? fieldConfig?.customerBilling?.is_visible : 0" class="col-2">
              <mat-form-field appearance="outline" style="width:100%" *ngIf="billingConfig"
              [ngClass]="{
                'disabled-field': true }">
                <mat-label>{{fieldConfig?.customerBilling?.field_label ? fieldConfig?.customerBilling?.field_label : 'Customer Billing *'}}</mat-label>
                <mat-select [formControl]="claimPaymentForm.get('customerBilling')" 
                [disabled]="expenseDetail?.status === 'Verified' || expenseDetail?.status === 'Closed'"
                 [required]="fieldConfig?.customerBilling?.is_mandatory ? true : false"  [placeholder]="fieldConfig?.customerBilling?.field_label">
                  <mat-option *ngFor="let entity of billingConfig" [value]=entity.id>{{entity.billing_type}}</mat-option>
                </mat-select>
              </mat-form-field>
            </div>

            <div *ngIf="costCentreType !== 'I' && fieldConfig?.customerBilling?.is_visible" class="col-2">
              <mat-form-field appearance="outline" style="width: 100%" *ngIf="billingConfig"
              [ngClass]="{
                'disabled-field': expenseDetail?.status === 'Verified' || expenseDetail?.status === 'Closed' }">
                <mat-label>{{ fieldConfig?.customerBilling?.field_label || 'Customer Billing *' }}</mat-label>
                <mat-select [disabled]="true" [value]="'Not Applicable'">
                  <mat-option [value]="'Not Applicable'">Not Applicable</mat-option>
                </mat-select>
              </mat-form-field>
            </div>

          </div>
        </div>
      </div>
      <div class="row" *ngIf="isExpenseEntryLoaded == true">
        <div class="col pr-0">
          <div class="row">
            <div class="col-12 pl-0 pr-1 d-flex">
              <div class="pt-2 pb-2">
                <mat-icon class="pt-1 info-icon">
                  info_outline
                </mat-icon>
                <span class="treasury-title ml-1"> Expense Entry </span>
              </div>
            </div>
          </div>
          <div class="main-card">
            <div class="row" formArrayName="expenseEntry">
  
              <div class="d-flex mt-2">
                <div class="treasury-item-header pt-2 pl-2" style="width: 30px"> </div>
                <div class="treasury-item-header border-right pt-2 pl-2" style="width: 100px">{{fieldConfig?.category?.field_label ? fieldConfig?.category?.field_label : 'Category'}}</div>
                <div class="treasury-item-header border-right pt-2 pl-2 d-flex justify-content-center" style="width: 150px">{{fieldConfig?.attachment?.field_label ? fieldConfig?.attachment.field_label : 'Attachment'}}</div>
                <div class="treasury-item-header border-right pt-2 pl-2 d-flex justify-content-center" style="width: 120px">{{fieldConfig?.claimRaisedCurrency?.field_label ? fieldConfig?.claimRaisedCurrency.field_label : 'Claim amount'}}</div>
                <div class="treasury-item-header border-right pt-2 pl-2 d-flex justify-content-center" style="width: 150px">{{fieldConfig?.settlementAmountInDetailsPage?.field_label ? fieldConfig?.settlementAmountInDetailsPage?.field_label : 'Amount'}}</div>
                <div class="treasury-item-header border-right pt-2 pl-2 d-flex justify-content-center" style="width: 150px">
                  <span *ngIf="expenseDetail?.status == 'Approved'">
                    Verification Amount
                  </span>
                  <span *ngIf="expenseDetail?.status == 'Verified' || expenseDetail?.status == 'Closed'">
                    Verified Amount
                  </span>
                </div>
                <div class="treasury-item-header border-right pt-2 pl-2 d-flex justify-content-center" style="width: 100px">Action</div>
                <div *ngIf="fieldConfig?.taxCredit?.is_visible" class="treasury-item-header border-right pt-2 pl-2 d-flex justify-content-center" style="width: 150px">{{fieldConfig?.taxCredit?.field_label ? fieldConfig?.taxCredit?.field_label : 'Tax Credit'}}</div>
                <div *ngIf="fieldConfig?.taxCreditledger?.is_visible" class="treasury-item-header pt-2 pl-2 d-flex justify-content-center" style="width: 200px">{{fieldConfig?.taxCreditledger?.field_label ? fieldConfig?.taxCreditledger.field_label : 'Tax Credit Ledger'}}</div>
              </div>
            </div>
            <form [formGroup]="ExpenseEntryFormData">
              <div *ngFor="let item of ExpenseEntryFormData.controls; let j = index" [formGroupName]="j"
                style="border-bottom: 1.5px solid  #FaFaFa; padding: 0%;" class="row">
            
                <div class="d-flex mt-2">
                  <div class="treasury-item pt-2 pl-2 d-flex justify-content-center" style="width: 30px">#{{j+1}}</div>
                  <div class="treasury-item border-right pt-1.5 pl-2" style="width: 100px; overflow: hidden;
                          text-overflow: ellipsis;
                          white-space: nowrap;
                          line-height: 38px;" [matTooltip]="this.claimPaymentForm.value.expenseEntry[j].expense_category_name">
                          <span >
                    {{ this.claimPaymentForm.value.expenseEntry[j].expense_category_name }}
                  </span>
                    <ng-container *ngIf="(claimPaymentForm.value.expenseEntry[j].expense_category_id === 18 || 
                                          claimPaymentForm.value.expenseEntry[j].expense_category_id === 8) &&
                                          (claimPaymentForm.value.expenseEntry[j].from_location ||
                                          claimPaymentForm.value.expenseEntry[j].to_location ||
                                          claimPaymentForm.value.expenseEntry[j].vehicle_type ||
                                          claimPaymentForm.value.expenseEntry[j].vehicle_engine_type ||
                                          claimPaymentForm.value.expenseEntry[j].miles ||
                                          claimPaymentForm.value.expenseEntry[j].perdiem_start_date ||
                                          claimPaymentForm.value.expenseEntry[j].perdiem_start_time ||
                                          claimPaymentForm.value.expenseEntry[j].perdiem_end_date ||
                                          claimPaymentForm.value.expenseEntry[j].perdiem_end_time ||
                                          claimPaymentForm.value.expenseEntry[j].days ||
                                          claimPaymentForm.value.expenseEntry[j].location)">
                                          <span style="display: inline-block; line-height: 20px;">
                        <button mat-icon-button
                        style="margin-left: 5px;  padding: 0; height: 20px; width: 20px; display: inline-flex; align-items: center;"
                        (click)="toggleClaimOverlay(j)" #triggerOriginRef="cdkOverlayOrigin"
                          [cdkOverlayOrigin]="triggerOriginRef" style="height: 14px; width: 25px;">
                          <mat-icon style="font-size: 14px; color: #474444;">
                            info
                          </mat-icon>
                        </button>
                      </span>
                    
                      <ng-template cdkConnectedOverlay [cdkConnectedOverlayOrigin]="triggerOriginRef"
                        [cdkConnectedOverlayOpen]="openClaimOverlayIndex === j" [cdkConnectedOverlayHasBackdrop]="true"
                        (backdropClick)="openClaimOverlayIndex = null" cdkConnectedOverlayBackdropClass="overLayClass"
                        [cdkConnectedOverlayFlexibleDimensions]="true" [cdkConnectedOverlayPositions]="[
                                        { originX: 'start', originY: 'bottom', overlayX: 'start', overlayY: 'top' },
                                        { originX: 'start', originY: 'top', overlayX: 'start', overlayY: 'bottom' }
                                     ]">
                    
                        <div class="card balance-planned-log-pop-over">
                          <!-- Header Section -->
                          <div class="header">
                            <span class="width-class">Claim Details</span>
                            <span class="close-icon">
                              <mat-icon style="font-size: 18px; cursor: pointer;" (click)="openClaimOverlayIndex = null">close</mat-icon>
                            </span>
                          </div>
                    
                          <hr class="divider">
                    
                          <div *ngIf="claimPaymentForm.value.expenseEntry[j].expense_category_id === 18">
                            <!-- Claim Details for category 18 -->
                            
                            <div class="row" *ngIf= "fieldConfig?.fromLocation?.is_to_show_in_treasury_verification_details">
                              <div class="col-5 label-class">{{ fieldConfig?.fromLocation?.field_label || 'From Location' }}</div>
                              <div class="col-7 value-class">{{ claimPaymentForm.value.expenseEntry[j].from_location || '-' }}</div>
                            </div>
                    
                            <div class="row" *ngIf= "fieldConfig?.toLocation?.is_to_show_in_treasury_verification_details">
                              <div class="col-5 label-class">{{ fieldConfig?.toLocation?.field_label || 'To Location' }}</div>
                              <div class="col-7 value-class">{{ claimPaymentForm.value.expenseEntry[j].to_location || '-' }}</div>
                            </div>
                    
                            <div class="row" *ngIf= "fieldConfig?.vehicleType?.is_to_show_in_treasury_verification_details">
                              <div class="col-5 label-class">{{ fieldConfig?.vehicleType?.field_label || 'Vehicle Type' }}</div>
                              <div class="col-7 value-class">{{ claimPaymentForm.value.expenseEntry[j].vehicle_type || '-' }}</div>
                            </div>
                    
                            <div class="row"  *ngIf= "fieldConfig?.vehicleEngineType?.is_to_show_in_treasury_verification_details">
                              <div class="col-5 label-class">{{ fieldConfig?.vehicleEngineType?.field_label || 'Vehicle Engine Type' }}
                              </div>
                              <div class="col-7 value-class">{{ claimPaymentForm.value.expenseEntry[j].vehicle_engine_type || '-' }}</div>
                            </div>
                    
                            <div class="row"  *ngIf= "fieldConfig?.miles?.is_to_show_in_treasury_verification_details">
                              <div class="col-5 label-class">{{ fieldConfig?.miles?.field_label || 'Miles' }}</div>
                              <div class="col-7 value-class">{{ claimPaymentForm.value.expenseEntry[j].miles || '-' }}  {{ claimPaymentForm.value.expenseEntry[j].unit || '-' }}</div>
                            </div>
                          </div>
                    
                          <div *ngIf="claimPaymentForm.value.expenseEntry[j].expense_category_id === 8">
                            <!-- Claim Details for category 8 -->
                            <div class="row"  *ngIf= "fieldConfig?.perDiemstartDate?.is_to_show_in_treasury_verification_details">
                              <div class="col-5 label-class">{{ fieldConfig?.perDiemstartDate?.field_label || 'Per Diem Start Date' }}</div>
                              <div class="col-7 value-class">{{ claimPaymentForm.value.expenseEntry[j].perdiem_start_date || '-'  | date: 'dd-MMM-yyyy'}}</div>
                            </div>
                    
                            <div class="row"  *ngIf= "fieldConfig?.perDiemStartTime?.is_to_show_in_treasury_verification_details">
                              <div class="col-5 label-class">{{ fieldConfig?.perDiemStartTime?.field_label || 'Per Diem Start Time' }}</div>
                              <div class="col-7 value-class">{{ claimPaymentForm.value.expenseEntry[j].perdiem_start_time || '-' }}</div>
                            </div>
                    
                            <div class="row" *ngIf= "fieldConfig?.perDiemEndDate?.is_to_show_in_treasury_verification_details">
                              <div class="col-5 label-class">{{ fieldConfig?.perDiemEndDate?.field_label || 'Per Diem End Date' }}</div>
                              <div class="col-7 value-class">{{ claimPaymentForm.value.expenseEntry[j].perdiem_end_date || '-'  | date: 'dd-MMM-yyyy'}}</div>
                            </div>
                    
                            <div class="row" *ngIf= "fieldConfig?.perDiemEndTime?.is_to_show_in_treasury_verification_details">
                              <div class="col-5 label-class">{{ fieldConfig?.perDiemEndTime?.field_label || 'Per Diem End Time' }}</div>
                              <div class="col-7 value-class">{{ claimPaymentForm.value.expenseEntry[j].perdiem_end_time || '-' }}</div>
                            </div>
                    
                            <div class="row" *ngIf= "fieldConfig?.days?.is_to_show_in_treasury_verification_details">
                              <div class="col-5 label-class">{{ fieldConfig?.days?.field_label || 'Days' }}</div>
                              <div class="col-7 value-class">{{ claimPaymentForm.value.expenseEntry[j].days || '-' }}</div>
                            </div>
                    
                            <div class="row" *ngIf= "fieldConfig?.location?.is_to_show_in_treasury_verification_details">
                              <div class="col-5 label-class">{{ fieldConfig?.location?.field_label || 'Location' }}</div>
                              <div class="col-7 value-class">{{ claimPaymentForm.value.expenseEntry[j].location || '-' }}</div>
                            </div>
                          </div>
                        </div>
                      </ng-template>
                    </ng-container>

                  </div>
                  <div class="treasury-item border-right" style="width: 150px">
                    <app-expense-upload-btn [destinationBucket]="'kebs-expenses'" [routingKey]="'expenses'"
                      [contextId]="approvalItemForm.value.approvalItems[this.claimPaymentForm.value.expenseEntry[j].item_index].context_id" [allowEdit]="false"
                      (change)="changeInAttachment($event,j)"
                      [expHeaderId]="expenseDetail.expense_header_id" [btnColor]="primaryColor"></app-expense-upload-btn>
                  </div>
                  

                  <div class="treasury-item border-right pt-2 pl-2 d-flex justify-content-center" style="width: 120px">
                    <span style="max-width: 54%;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;" [matTooltip]="getClaimRaisedAmount(item)">
                      {{getClaimRaisedAmount(item)}}</span>
                       <span class="ml-1"> {{getClaimRaisedCurrency(item)}}</span>
                  </div>
                  
                  <div class="treasury-item border-right pt-2 pl-2 d-flex justify-content-center" style="width: 150px">
                    <span style="max-width: 54%;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;" [matTooltip]="getEntitySettlementAmount(item)">
                      {{getEntitySettlementAmount(item)}}</span> <span class="ml-1">{{expenseDetail?.entity_currency_code}}</span>
                    
                    <span>
                      <button mat-icon-button (click)="insertBelow.toggle()" style="height: 14px; width: 14px;">
                        <mat-icon [satPopoverAnchor]="insertBelow" style="font-size: 14px;
                                color: #474444;
                                vertical-align: middle; line-height: 0%;">info</mat-icon>
                      </button>
                    </span>
                    <sat-popover #insertBelow horizontalAlign="center" verticalAlign="below" hasBackdrop>
                      <div style="background-color: #111434; color: white; width: 700px; height: auto; max-height: 500px; overflow-y: auto; border-radius: 8px;"
                      class="container-fluid pt-2 pb-2">                  
                        <div class="row">
                          <div class="col p-0">
                            <div class="row pt-2 pl-2" style="color: #C0C0C0; font-weight: 500; font-size: 16px">
                              <div class="col-11">Claim Details</div>
                              <div class="col-1" style="color: white">
                                <mat-icon style="font-size: 18px; cursor: pointer;"
                                  (click)="fetchReport(insertBelow);">close</mat-icon>
                              </div>
                            </div>
                            <div class="row d-flex pt-1 pl-4">
                              <!-- <div *ngIf="fieldConfig?.invoiceNo?.is_visible" class="col p-0 tooltip-class-header"><span>{{fieldConfig?.invoiceNo?.field_label ?
                                  fieldConfig?.invoiceNo.field_label : 'Invoice No.'}}</span></div> -->
                              <div class="col p-0 tooltip-class-header"><span>{{fieldConfig?.invoiceDate?.field_label ?
                                  fieldConfig?.invoiceDate.field_label : 'Invoice Date'}}</span></div>
                              <div class="col p-0 tooltip-class-header">
                                <span>{{fieldConfig?.settlementAmountInDetailsPage?.field_label ?
                                  fieldConfig?.settlementAmountInDetailsPage.field_label : 'Amount'}}</span></div>
                                  <div class="col p-0 tooltip-class-header"> </div>
                            </div>
                            <div class="row d-flex pt-1 pl-4">
                              <!-- <div *ngIf="fieldConfig?.invoiceNo?.is_visible" class="col p-0 tooltip-class"><span>
                                  {{expenseItemData[claimPaymentForm.value.expenseEntry[j].expense_item_id].invoice_no ?
                                  expenseItemData[claimPaymentForm.value.expenseEntry[j].expense_item_id].invoice_no : '-'}}
                                </span></div> -->
                              <div class="col p-0 tooltip-class"><span>
                                  {{expenseItemData[claimPaymentForm.value.expenseEntry[j].expense_item_id].invoice_date ?
                                  expenseItemData[claimPaymentForm.value.expenseEntry[j].expense_item_id].invoice_date : '-'}}
                                </span></div>
                              <div class="col p-0 tooltip-class">
                                <!-- <app-expense-currency [fieldConfig]="fieldConfig"
                                  [currencyList]="parseJson(this.claimPaymentForm.value.expenseEntry[j].item_amount)" class="flex-1"
                                  type="simple"
                                ></app-expense-currency> -->
                                <span>
                                {{getEntitySettlementAmount(item)}}</span> <span class="ml-1">{{expenseDetail?.entity_currency_code}}</span>
                              </div>
                              <div class="col p-0 tooltip-class-header"> </div>
                            </div>
                            <!-- <div class="row d-flex pt-1 pl-4">
                                      <div class="col p-0 tooltip-class">
                                        <span>
                                            <app-expense-currency [currencyList]="parseJson(this.claimPaymentForm.value.expenseEntry[j].item_amount)" class="flex-1" type="simple"></app-expense-currency>
                                        </span>
                                      </div>
                                    </div> -->
                            <div class="row d-flex pt-3 pl-4">
                              <div class="col p-0 tooltip-class-header">{{fieldConfig?.systemConversionRate?.field_label ?
                                fieldConfig?.systemConversionRate.field_label : 'Claim Conversion Rate'}}</div>
                              <div class="col p-0 tooltip-class-header">{{fieldConfig?.userConversionRate?.field_label ?
                                fieldConfig?.userConversionRate.field_label : 'Conversion Rate'}}</div>
                              <div class="col p-0 tooltip-class-header"> </div>
                            </div>
                            <div class="row d-flex pt-1 pl-4 pb-2">
                              <div class="col p-0 tooltip-class">
                                {{expenseItemData[claimPaymentForm.value.expenseEntry[j].expense_item_id].system_defined_conversion_rate}}
                              </div>
                              <div class="col p-0 tooltip-class">
                                {{expenseItemData[claimPaymentForm.value.expenseEntry[j].expense_item_id].user_defined_conversion_rate}}
                              </div>
                              <div class="col p-0 tooltip-class"> </div>
                            </div>
                            <div *ngIf="fieldConfig?.taxPercentage?.is_to_show_in_cards || fieldConfig?.taxAmount?.is_to_show_in_cards" class="row d-flex pt-3 pl-4">
                              <div *ngIf="fieldConfig?.taxPercentage?.is_to_show_in_cards" class="col p-0 tooltip-class-header">{{fieldConfig?.taxPercentage?.field_label ?
                                fieldConfig?.taxPercentage.field_label : 'Tax Percentage'}}</div>
                              <div *ngIf="fieldConfig?.taxAmount?.is_to_show_in_cards" class="col p-0 tooltip-class-header">{{fieldConfig?.taxAmount?.field_label ?
                                fieldConfig?.taxAmount.field_label : 'Tax Amount'}}</div>
                              <div class="col p-0 tooltip-class-header"> </div>
                            </div>
                            <div *ngIf="fieldConfig?.taxPercentage?.is_to_show_in_cards || fieldConfig?.taxAmount?.is_to_show_in_cards" class="row d-flex pt-1 pl-4 pb-2">
                              <div *ngIf="fieldConfig?.taxPercentage?.is_to_show_in_cards" class="col p-0 tooltip-class">
                                {{expenseItemData[claimPaymentForm.value.expenseEntry[j].expense_item_id].tax_type}}
                              </div>
                              <div *ngIf="fieldConfig?.taxAmount?.is_to_show_in_cards && expenseItemData[claimPaymentForm.value.expenseEntry[j].expense_item_id].tax_amount" class="col p-0 tooltip-class">
                                {{gettaxAmountForPopOver(item)}}
                                {{expenseItemData[claimPaymentForm.value.expenseEntry[j].expense_item_id].entity_currency}}
                              </div>
                              <div class="col p-0 tooltip-class"> </div>
                            </div>
                            <div class="row d-flex pt-1 pl-4">
                              <div class="col p-0 tooltip-class-header">
                                <span>
                                  {{fieldConfig?.description?.field_label ?
                                  fieldConfig?.description.field_label : 'Description'}}
                                </span>
                              </div>
                            </div>
                            <div class="row d-flex pt-1 pl-4 pb-2">
                              <div class="col p-0 tooltip-class">
                                {{expenseItemData[claimPaymentForm.value.expenseEntry[j].expense_item_id].description ?
                                expenseItemData[claimPaymentForm.value.expenseEntry[j].expense_item_id].description : '-'}}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </sat-popover>
                  </div>
                  <div class="treasury-item border-right pt-2 pl-2 pr-2 d-flex justify-content-center" style="width: 150px; overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;" [matTooltip]="getVerifiedAmountInEntityCurrency(categoryTotals[j],item.get('ledgerAmountDistribution').at(0).get('currency_code').value)">
                    <!-- <app-expense-currency [currencyList]="parseJson(this.claimPaymentForm.value.expenseEntry[j].item_amount)"
                      class="flex-1" type="simple"></app-expense-currency> -->
                      {{getVerifiedAmountInEntityCurrency(categoryTotals[j],item.get('ledgerAmountDistribution').at(0).get('currency_code').value)}} {{item.get('ledgerAmountDistribution').at(0).get('currency_code').value}}
                      <!-- {{ categoryTotals[j] | number:'1.2-2'  }}     {{ item.get('ledgerAmountDistribution').at(0).get('currency_code').value }}     -->
                  </div>
                  <div class="treasury-item border-right pl-2 d-flex justify-content-center" style="width: 100px">
                    <span
                      *ngIf="approvalItemForm.value.approvalItems[this.claimPaymentForm.value.expenseEntry[j].item_index].cc_status === 'Approved'"
                      class="pt-2">
                      <span
                        *ngIf="approvalItemForm.value.approvalItems[this.claimPaymentForm.value.expenseEntry[j].item_index].t_is_curr_appr === 1 && approvalItemForm.value.approvalItems[this.claimPaymentForm.value.expenseEntry[j].item_index].treasuryStatus === 'Submitted'; else showStatus">
                        <span *ngIf="checkAccountValidity(j) || approvalItemForm.value.approvalItems[this.claimPaymentForm.value.expenseEntry[j].item_index].isItemBeingRejected">
                          <span
                          *ngIf="approvalItemForm.value.approvalItems[claimPaymentForm.value.expenseEntry[j].item_index].isItemBeingApproved == false"
                          class="mt-3 pt-1 pl-1 expense-item-table-button-bordered"
                          mat-icon-button
                          matTooltip="Approve"
                          [ngClass]="{
                            'disabled': checkAccountValidity(j) ||
                                        approvalItemForm.value.approvalItems[claimPaymentForm.value.expenseEntry[j].item_index].isItemBeingRejected ||
                                        approvalItemForm.value.approvalItems[claimPaymentForm.value.expenseEntry[j].item_index].isItemBeingApproved
                          }"
                          [disabled]="checkAccountValidity(j) ||
                                      approvalItemForm.value.approvalItems[claimPaymentForm.value.expenseEntry[j].item_index].isItemBeingRejected ||
                                      approvalItemForm.value.approvalItems[claimPaymentForm.value.expenseEntry[j].item_index].isItemBeingApproved"
                          style="border: 2px solid #52C41A; color:#52C41A; border-radius: 6px; height: 18px; width: 18px; cursor: not-allowed"
                        >
                          <mat-icon class="expense-item-table-icon" style="width: 20px; height: 20px;"
                            *ngIf="approvalItemForm.value.approvalItems[this.claimPaymentForm.value.expenseEntry[j].item_index].isItemBeingApproved == false">done</mat-icon>
                          </span>
                        </span>
                        <span *ngIf="!checkAccountValidity(j) && !approvalItemForm.value.approvalItems[this.claimPaymentForm.value.expenseEntry[j].item_index].isItemBeingRejected">
                          <span
                          *ngIf="approvalItemForm.value.approvalItems[claimPaymentForm.value.expenseEntry[j].item_index].isItemBeingApproved === false"
                          class="mt-3 pt-1 pl-1 expense-item-table-button-bordered"
                          mat-icon-button
                          matTooltip="Approve"
                          (click)="approveItem(j, claimPaymentForm.value.expenseEntry[j].item_index)"
                          [ngClass]="{
                            'disabled': checkAccountValidity(j) ||
                                        approvalItemForm.value.approvalItems[claimPaymentForm.value.expenseEntry[j].item_index].isItemBeingRejected ||
                                        approvalItemForm.value.approvalItems[claimPaymentForm.value.expenseEntry[j].item_index].isItemBeingApproved
                          }"
                          [disabled]="
                            checkAccountValidity(j) ||
                            approvalItemForm.value.approvalItems[claimPaymentForm.value.expenseEntry[j].item_index].isItemBeingRejected ||
                            approvalItemForm.value.approvalItems[claimPaymentForm.value.expenseEntry[j].item_index].isItemBeingApproved
                          "
                          style="border: 2px solid #52C41A; color: #52C41A; border-radius: 6px; height: 18px; width: 18px; cursor: pointer;"
                        >
                                                
                          <mat-icon class="expense-item-table-icon" style="width: 20px; height: 20px;"
                            *ngIf="approvalItemForm.value.approvalItems[this.claimPaymentForm.value.expenseEntry[j].item_index].isItemBeingApproved == false">done</mat-icon>
                          </span>
                        </span>
                        
                        <mat-spinner
                          *ngIf="approvalItemForm.value.approvalItems[this.claimPaymentForm.value.expenseEntry[j].item_index].isItemBeingApproved == true"
                          diameter="20"
                          color="primary"></mat-spinner>
  
                          <span
                          *ngIf="
                            !approvalItemForm.value.approvalItems[claimPaymentForm.value.expenseEntry[j].item_index].isItemBeingApproved &&
                            !approvalItemForm.value.approvalItems[claimPaymentForm.value.expenseEntry[j].item_index].isItemBeingRejected
                          "
                          class="ml-2 mt-1 pt-1 pl-1 expense-item-table-button-bordered-reject"
                          mat-icon-button
                          matTooltip="Reject"
                          (click)="openRejectCommentDialog(claimPaymentForm.value.expenseEntry[j].item_index)"
                          [ngClass]="{
                            'restict-cursor':
                              approvalItemForm.value.approvalItems[claimPaymentForm.value.expenseEntry[j].item_index].isItemBeingApproved ||
                              approvalItemForm.value.approvalItems[claimPaymentForm.value.expenseEntry[j].item_index].isItemBeingRejected
                          }"
                          [disabled]="
                            approvalItemForm.value.approvalItems[claimPaymentForm.value.expenseEntry[j].item_index].isItemBeingApproved ||
                            approvalItemForm.value.approvalItems[claimPaymentForm.value.expenseEntry[j].item_index].isItemBeingRejected
                          "
                          style="border: 2px solid #FF3A46; color: #FF3A46; border-radius: 6px; height: 18px; width: 18px; cursor: pointer;"
                        >
                          <mat-icon class="expense-item-table-icon" style="width: 20px; height: 20px;"
                            *ngIf="approvalItemForm.value.approvalItems[this.claimPaymentForm.value.expenseEntry[j].item_index].isItemBeingRejected == false">clear</mat-icon>
                        </span>
                        <span *ngIf="approvalItemForm.value.approvalItems[this.claimPaymentForm.value.expenseEntry[j].item_index].isItemBeingRejected == true">
                        <mat-spinner
                          diameter="20"></mat-spinner>
                        </span>
                      </span>
                      <ng-template #showStatus>
                        <div class="mt-1"
                          [ngStyle]="{'color': getTxtColor(approvalItemForm.value.approvalItems[this.claimPaymentForm.value.expenseEntry[j].item_index].treasuryStatus)}"
                          content-type="template" [tooltip]="expRejectCommentTreasurerTooltip" [max-width]="1200" placement="bottom"
                          *ngIf="approvalItemForm.value.approvalItems[this.claimPaymentForm.value.expenseEntry[j].item_index].t_reject_comment">
                          <span class="pr-2">{{approvalItemForm.value.approvalItems[this.claimPaymentForm.value.expenseEntry[j].item_index].treasuryStatus}}</span>
                          <img src="https://assets.kebs.app/message-notifSVG.svg" style="height: 18px;">
                        </div>
            
                        <div class="mt-1"
                          [ngStyle]="{'color': getTxtColor(approvalItemForm.value.approvalItems[this.claimPaymentForm.value.expenseEntry[j].item_index].treasuryStatus)}"
                          *ngIf="approvalItemForm.value.approvalItems[this.claimPaymentForm.value.expenseEntry[j].item_index].t_reject_comment == null">
                          {{
                          approvalItemForm.value.approvalItems[this.claimPaymentForm.value.expenseEntry[j].item_index].treasuryStatus
                          }}
                        </div>
                      </ng-template>
            
                      <ng-template #expRejectCommentTreasurerTooltip>
                        <div style="width: 380px; background-color: #111434 !important; padding: 0% !important; margin: 0% !important;"
                      class="pb-2">
                      <div class="row d-flex pt-3 pl-4">
                        <div class="col p-0 tooltip-class-header"><span>Rejected By</span></div>
                        <div class="col p-0 tooltip-class-header">Rejected On</div>
                      </div>
                      <div class="row d-flex pt-1 pl-4">
                        <div class="col p-0 tooltip-class">
                          <span>{{ approvalItemForm.value.approvalItems[this.claimPaymentForm.value.expenseEntry[j].item_index].t_reject_comment.rejected_by }}</span>
                        </div>
                        <div class="col p-0 tooltip-class">
                          <span>{{ approvalItemForm.value.approvalItems[this.claimPaymentForm.value.expenseEntry[j].item_index].t_reject_comment.rejected_on
                            | date: 'dd-MMM-yyyy' }}</span>
                        </div>
                      </div>
                      <div class="row d-flex pt-3 pl-4">
                        <div class="col p-0 tooltip-class-header"><span>Reason</span></div>
                      </div>
                      <div class="row d-flex pt-1 pl-4 pr-4 pb-3">
                        <div class="col p-0 tooltip-class">
                          <span>{{approvalItemForm.value.approvalItems[this.claimPaymentForm.value.expenseEntry[j].item_index].t_reject_comment.comment}}</span>
                        </div>
                      </div>
                    </div>
                      </ng-template>
                    </span>
                  </div>
                  <div *ngIf="fieldConfig?.taxCredit?.is_visible" class="treasury-item border-right pl-2 pr-2 d-flex justify-content-center" style="width: 150px">
                    <mat-form-field appearance="outline" style="width:100%"
                    [ngClass]="{
                      'disabled-field': expenseDetail?.status === 'Verified' || expenseDetail?.status === 'Closed'}">
                      <mat-label>{{fieldConfig?.taxCredit?.field_label ? fieldConfig?.taxCredit?.field_label : 'Tax
                        Credit'}}</mat-label>
                      <div class="d-flex">
                        <input matInput type="number" formControlName="taxCredit"
                          (focusout)="onTaxCreditValueChange(j)" [readonly]="isReadOnly || expenseDetail?.status === 'Verified' || expenseDetail?.status === 'Closed'"/>
                        <span class="currency-indicator"
                          *ngIf="item?.get('ledgerAmountDistribution').at(0).get('currency_code').value">
                          {{ item.get('ledgerAmountDistribution').at(0).get('currency_code').value }}
                        </span>
                      </div>
                      <mat-hint class="mat-hint mt-1" *ngIf="
                      this.ExpenseEntryFormData.controls[j]['controls'].taxCredit.invalid &&
                      (this.ExpenseEntryFormData.controls[j]['controls'].taxCredit.touched ||
                      this.ExpenseEntryFormData.controls[j]['controls'].taxCredit.dirty)
                          ">
                        <span *ngIf="
                              !this.ExpenseEntryFormData.controls[j]['controls'].taxCredit
                                .errors.validAmount
                            ">
                            Invalid Tax credit amount
                        </span>
                        <!-- <span *ngIf="
                        this.ExpenseEntryFormData.controls[j]['controls'].taxCredit
                                .errors.required
                            ">
                          ,empty
                        </span> -->
                      </mat-hint>
                    </mat-form-field>
                    <div>
                    </div>
                  </div>
                  <div *ngIf="fieldConfig?.taxCreditledger?.is_visible" class="treasury-item pl-2 pr-2 d-flex justify-content-center" style="width: 200px">
                    <span style="width:100%">
                      <app-input-huge-search
                        [label]="fieldConfig?.taxCreditledger?.field_label ? fieldConfig?.taxCreditledger.field_label : 'Tax Credit Ledger' "
                        [optionLabel]="['gl_name', 'id']" [apiUri]="'/api/exPrimary2/getExpenseLedgerByEntityParams'"
                        [bodyParams]="getExpenseLedgerByEntityParams()" formControlName="taxLedger"
                        [isReadOnly]="isReadOnly || expenseDetail?.status === 'Verified' || expenseDetail?.status === 'Closed' ||
                        fieldConfig?.taxCreditledger?.is_disabled ? true : false"
                        [bgColorOnDisable] = "expenseDetail.status == 'Verified' || expenseDetail.status == 'Closed' ||
                        fieldConfig?.taxCreditledger?.is_disabled ? true : false">
                      </app-input-huge-search>
                    </span>
                  </div>
                </div>
                <!-- <div class="d-flex mt-2">
                        <div class="treasury-item pt-2 mt-1 pl-3 d-flex justify-content-center" style="width: 30px">#{{j+1}}</div>
                        <div class="treasury-item pt-2 mt-1 pl-4" style="width: 200px;overflow: hidden;
                        text-overflow: ellipsis;white-space: nowrap; color: #EE4961; font-weight: 700;"
                        [matTooltip]="this.claimPaymentForm.value.expenseEntry[j].expense_category_name">
                          {{ this.claimPaymentForm.value.expenseEntry[j].expense_category_name }}
                        </div>
                        <div class="treasury-item pl-4" style="width: 150px">
                          <span>
                            {{ categoryTotals[j] | number:'1.2-2'  }}     {{ item.get('ledgerAmountDistribution').at(0).get('currency_code').value }}
                          </span>
                          <span>
                            <button mat-icon-button (click)="insertBelow.toggle()" style="height: 14px; width: 14px;">
                              <mat-icon [satPopoverAnchor]="insertBelow" style="font-size: 14px;
                              color: #474444;
                              vertical-align: top;">info</mat-icon>
                            </button>
                          </span>
                        </div>
                        <sat-popover #insertBelow horizontalAlign="center" verticalAlign="below" hasBackdrop>
                          <div style="background-color: #111434; color: white; width: 400px; height: fit-content"
                            class="container-fluid">
                            <div class="row">
                              <div class="col p-0">
                                <div class="row pt-2 pl-2" style="color: #EE4961; font-weight: 500; font-size: 14px">
                                  <div class="col-11">Claim Details</div>
                                  <div class="col-1" style="color: white">
                                    <mat-icon style="font-size: 18px; cursor: pointer;"
                                      (click)="fetchReport(insertBelow);">close</mat-icon>
                                  </div>
                                </div>
                                <div class="row d-flex pt-2 pl-4">
                                  <div class="col p-0 tooltip-class-header"><span>{{fieldConfig?.invoiceNo?.field_label ?
                                      fieldConfig?.invoiceNo.field_label : 'Invoice No.'}}</span></div>
                                  <div class="col p-0 tooltip-class-header"><span>{{fieldConfig?.invoiceDate?.field_label ?
                                      fieldConfig?.invoiceDate.field_label : 'Invoice Date'}}</span></div>
                                  <div class="col p-0 tooltip-class-header"><span>{{fieldConfig?.invoiceNo?.field_label ?
                                      fieldConfig?.invoiceNo.field_label : 'Invoice No.'}}</span></div>
                                </div>
                                <div class="row d-flex pt-1 pl-4">
                                  <div class="col p-0 tooltip-class"><span>
                                      {{expenseItemData[claimPaymentForm.value.expenseEntry[j].expense_item_id].invoice_no ? 
                                        expenseItemData[claimPaymentForm.value.expenseEntry[j].expense_item_id].invoice_no : '-'}}
                                    </span></div>
                                  <div class="col p-0 tooltip-class"><span>
                                      {{expenseItemData[claimPaymentForm.value.expenseEntry[j].expense_item_id].invoice_date ? 
                                        expenseItemData[claimPaymentForm.value.expenseEntry[j].expense_item_id].invoice_date : '-'}}
                                    </span></div>
                                </div>
                                <div class="row d-flex pt-1 pl-4">
                                  <div class="col p-0 tooltip-class-header">
                                    <span>
                                      {{fieldConfig?.description?.field_label ?
                                        fieldConfig?.description.field_label : 'Description'}}
                                    </span>
                                  </div>
                                </div>
                                <div class="row d-flex pt-1 pl-4">
                                  <div class="col p-0 tooltip-class">
                                    <span>
                                      {{expenseItemData[claimPaymentForm.value.expenseEntry[j].expense_item_id].description ?
                                        expenseItemData[claimPaymentForm.value.expenseEntry[j].expense_item_id].description : '-'}}
                                    </span>
                                  </div>
                                </div>
                                <div class="row d-flex pt-3 pl-4">
                                  <div class="col p-0 tooltip-class-header">{{fieldConfig?.systemConversionRate?.field_label ?
                                    fieldConfig?.systemConversionRate.field_label : 'Claim Conversion Rate'}}</div>
                                  <div class="col p-0 tooltip-class-header">{{fieldConfig?.userConversionRate?.field_label ?
                                    fieldConfig?.userConversionRate.field_label : 'Conversion Rate'}}</div>
                                </div>
                                <div class="row d-flex pt-1 pl-4 pb-2">
                                  <div class="col p-0 tooltip-class">
                                    {{expenseItemData[claimPaymentForm.value.expenseEntry[j].expense_item_id].user_defined_conversion_rate}}
                                  </div>
                                  <div class="col p-0 tooltip-class">
                                    {{expenseItemData[claimPaymentForm.value.expenseEntry[j].expense_item_id].system_defined_conversion_rate}}
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </sat-popover>
                        <div class="treasury-item pt-2 mt-1 pl-2" style="width: 150px">   
                          <app-expense-currency [currencyList]="parseJson(this.claimPaymentForm.value.expenseEntry[j].item_amount)" class="flex-1" type="simple"></app-expense-currency>
                        </div>
                        <div class="treasury-item mt-2 pl-4 d-flex" style="width: 100px">
                            <span class="mt-1 expense-item-table-button-bordered" mat-icon-button matTooltip="Approve"
                            (click)="approveItem(this.claimPaymentForm.value.expenseEntry[j].item_index)"
                            [ngClass]="{'restict-cursor': checkAccountValidity(j) || approvalItemForm.value.approvalItems[this.claimPaymentForm.value.expenseEntry[j].item_index].isItemBeingRejected}"
                            style="border: 2px solid #52C41A; color:#52C41A; border-radius: 8px;height: 20px; width: 20px;">
                              <mat-icon class="expense-item-table-icon" style="width: 20px; height: 20px;"
                              [ngClass]="{'disabled': checkAccountValidity(j) || approvalItemForm.value.approvalItems[this.claimPaymentForm.value.expenseEntry[j].item_index].isItemBeingRejected}"
                              *ngIf="approvalItemForm.value.approvalItems[this.claimPaymentForm.value.expenseEntry[j].item_index].isItemBeingApproved == false">done</mat-icon>
                            </span>
                            <mat-spinner
                                *ngIf="approvalItemForm.value.approvalItems[this.claimPaymentForm.value.expenseEntry[j].item_index].isItemBeingApproved == true"
                                diameter="20"></mat-spinner>
                            <span class="ml-2 mt-1 expense-item-table-button-bordered-reject" mat-icon-button matTooltip="Reject"
                            (click)="openRejectCommentDialog(this.claimPaymentForm.value.expenseEntry[j].item_index)"
                            [ngClass]="{'restict-cursor': approvalItemForm.value.approvalItems[this.claimPaymentForm.value.expenseEntry[j].item_index].isItemBeingApproved == true }"
                            [disabled]="approvalItemForm.value.approvalItems[this.claimPaymentForm.value.expenseEntry[j].item_index].isItemBeingApproved == true"
                            style="border:2px solid #FF3A46; color:#FF3A46; border-radius: 8px;height: 20px;width: 20px;">
                            <mat-icon class="expense-item-table-icon" style="width: 20px; height: 20px;"
                                *ngIf="approvalItemForm.value.approvalItems[this.claimPaymentForm.value.expenseEntry[j].item_index].isItemBeingRejected == false">clear</mat-icon>
                            </span>
                            <mat-spinner
                                *ngIf="approvalItemForm.value.approvalItems[this.claimPaymentForm.value.expenseEntry[j].item_index].isItemBeingRejected == true"
                                diameter="20"></mat-spinner>
                        </div>
                        <div class="treasury-item d-flex justify-content-center pl-1 pr-1" style="width: 150px">
                          <mat-form-field appearance="outline" style="width:100%">
                            <mat-label>Tax Credit Amount</mat-label>
                            <div class="d-flex">
                              <input matInput type="number" formControlName="taxCredit" />
                            </div>
                          </mat-form-field>
                        </div>
                        <div class="treasury-item d-flex justify-content-center mr-2 pl-1 pr-1" style="width: 150px">
                          <span style="width:100%">
                            <app-input-search-huge-input [label]="'Tax Credit Ledger *'" [optionLabel]="['expense_type', 'id']"
                                  [apiUri]="'/api/exPrimary/getExpenseLedgerByEntityParams'"
                                  [bodyParams]="getExpenseLedgerByEntityParams()" formControlName="taxLedger">
                            </app-input-search-huge-input>
                          </span>
                        </div>
                      </div> -->
                      <div class="row" *ngIf="item?.get('taxCreditWarning')?.value">
                        <mat-icon class="ml-2 mr-2" style="font-size: 22px;
                      vertical-align: middle; color: #cf0001">warning</mat-icon><span style="color:#cf0001">{{item?.get('taxCreditWarning')?.value}}</span>
                      </div>
                <div class="row" style="width:100%; background-color: #f2f3f6;">
                  <div class="col-12" style="background-color: #f2f3f6;" style="width:100%">
                    <div class="row">
                      <div class="col-12">
                        <span class="pt-2"
                          style="margin-left: -12px; color: #111434; font-weight: 500; font-size: 12px; display: inline-block;">
                          Ledger - Amount Distribution </span>
                          <div>
            
            
                            <div
                              *ngIf="approvalItemForm.value.approvalItems[this.claimPaymentForm.value.expenseEntry[j].item_index].cc_status === 'Approved'">
                              <span
                                *ngIf="approvalItemForm.value.approvalItems[this.claimPaymentForm.value.expenseEntry[j].item_index].t_is_curr_appr === 1 && approvalItemForm.value.approvalItems[this.claimPaymentForm.value.expenseEntry[j].item_index].treasuryStatus === 'Submitted'; else showStatus">
                                <button mat-icon-button class="custom-add-button" style="margin-left: -3px;"
                                  [disabled]="this.initialItemAmount[j] < this.categoryTotals[j]"
                                  (click)="addLedgerAmountdistribution(j)">
                                  <mat-icon class="custom-icon">add</mat-icon>
                                </button>
                              </span>
                            </div>
                          </div>
                      </div>
                    </div>
            
                    <div formArrayName="ledgerAmountDistribution">
                      <div *ngFor="let item of ledgerAmountDistributionFormData[j].controls; let m = index" [formGroupName]="m">
                        <div class="col-12">
                          <div class="row pt-2">
            
                            <!-- Expense Account -->
                            <div class="col" style="margin-left: -28px;">
                              <app-input-huge-search [label]="'Account *'" [optionLabel]="['expense_type', 'id']"
                                [apiUri]="'/api/exPrimary2/getExpenseTypeLedger'" [bodyParams]="getExpenseLedgerParams()"
                                [isReadOnly]="isReadOnly || expenseDetail?.status === 'Verified' || expenseDetail?.status === 'Closed'"
                                [bgColorOnDisable] = "expenseDetail.status == 'Verified' || expenseDetail.status == 'Closed'"
                                formControlName="expense_account">
                              </app-input-huge-search>
                            </div>
                            <!-- Splitup Amount -->
            
                            <div class="col">
                              <div class="row">
                                <mat-form-field appearance="outline" style="width: 100%" class="field-input" 
                                [ngClass]="{
                                  'disabled-field': (expenseDetail?.status == 'Verified' || expenseDetail?.status == 'Closed')}">
                                                    <mat-label>Splitup Amount *</mat-label>
                                  <div class="d-flex">
                                    <input matInput type="number" formControlName="ledger_item_amount"
                                      [value]="parseAndFilterExpenseCurrency(this.claimPaymentForm.value.expenseEntry[j].item_amount).value.toFixed(2)"
                                      (input)="showErrorIfInvalid(j)" (blur)="formatAmount(j, m)"
                                      [readonly]="isReadOnly || expenseDetail?.status === 'Verified' || expenseDetail?.status === 'Closed'" />
                                    <span class="currency-indicator">{{ item.get('currency_code').value }}</span>
                                  </div>
                                  <mat-hint
                                    *ngIf="item.get('ledger_item_amount').invalid && (item.get('ledger_item_amount').touched || item.get('ledger_item_amount').dirty)">
                                    <span *ngIf="item.get('ledger_item_amount').hasError('validAmount')">Enter a valid
                                      amount</span>
                                  </mat-hint>
                                </mat-form-field>
                                <div class="row">
                                  <div class="col-12" *ngIf="isExcessAmount(j, m)">
                                    <mat-icon style="color: #FF3A46; font-size: 10px; vertical-align: left;">
                                      error_outline
                                    </mat-icon>
                                    <span class="excess-amount" style="color: #FF3A46; font-size: 10px;font-weight: 600;">
                                      You have entered excess amount
                                    </span>
                                  </div>
                                </div>
                              </div>
            
                            </div>
            
            
            
                            <!-- Narration -->
                            <div class="col">
                              <mat-form-field appearance="outline" style="width: 100%" class="field-input"
                              [ngClass]="{
                                'disabled-field': (expenseDetail?.status == 'Verified' || expenseDetail?.status == 'Closed')}">
                                <mat-label>Narration</mat-label>
                                <div class="d-flex">
                                  <input matInput formControlName="narration" maxlength="500"
                                    (input)="updateRemainingCharacters($event.target.value, j, m)" (focus)="setFocused(j, m)"
                                    (blur)="setBlurred()"
                                    [readonly]="isReadOnly || expenseDetail?.status === 'Verified' || expenseDetail?.status === 'Closed'" />
                                </div>
                                <mat-hint align="start" [style.color]="'grey'"
                                *ngIf="expenseDetail?.status != 'Verified' && expenseDetail?.status != 'Closed' && !expenseDetail.adminView && isFocused(j, m)">{{
                                  getRemainingCharacters(j, m)
                                  }}/500 characters</mat-hint>
                              </mat-form-field>
                            </div>
                            <div class="col-1">
                              <!-- Add and Remove Buttons -->
                              <div
                                *ngIf="approvalItemForm.value.approvalItems[this.claimPaymentForm.value.expenseEntry[j].item_index].cc_status === 'Approved'">
                                <span
                                  *ngIf="approvalItemForm.value.approvalItems[this.claimPaymentForm.value.expenseEntry[j].item_index].t_is_curr_appr === 1 && approvalItemForm.value.approvalItems[this.claimPaymentForm.value.expenseEntry[j].item_index].treasuryStatus === 'Submitted'; else showStatus">
            
                                  <div class="col-3 pt-1" style="margin-left: -16px; margin-top: 10px">
                                    <button mat-icon-button class="custom-remove-button" *ngIf="m > 0"
                                      (click)="removeLedgerAmountdistribution(j, m)">
                                      <mat-icon class="custom-remove-icon">remove</mat-icon>
                                    </button>
                                  </div>
                                </span>
                              </div>
                            </div>
            
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
            
                
              </div>
            </form>
        </div>
          </div>
      </div>
    </form>
  </div>
  </div>
  
  
  <!-- line -->
  
  <div class="row" style="height: 10px;" *ngIf = "expenseDetail?.status != 'Rejected'">
    <div class="col-12" style="padding-left: 6px;">
      <div class="row">
        <div [appStatusLine]="expenseDetail?.status" [indicator]="3" class="col-12"></div>
      </div>
    </div>
  </div>
  
  <!-- line ends -->
  <div class="row" style="height: 10px; position: absolute;" *ngIf = "expenseDetail?.status != 'Rejected'">
    <div class="col-12" style="padding-left: 6px;">
      <div class="row">
        <div [appStatusLine]="expenseDetail?.status" [indicator]="3" class="col-12"></div>
      </div>
    </div>
  </div>
  <!-- line ends -->
  
  <!-- Closed status -->
  <div class="row pt-1" *ngIf = "expenseDetail?.status != 'Rejected'">
    <div class="d-flex">
      <div [appStatusColor]="expenseDetail?.status" [indicator]="4" class="status-circular-in-thread"></div>
      <span class="status-in-thread ml-3 mr-2">
        {{ getStatusText(4, expenseDetail?.status) }}
      </span>
      <span class="status-in-thread">
        {{ expenseDetail.closed_on ? (expenseDetail.closed_on | date: 'dd-MMM-yyyy') : "" }}
      </span>
    </div>
  </div>
  
  <div class="row" style="height: 10px; position: absolute;"
    *ngIf="checkCommentVisibility(4, expenseDetail?.status) == true">
    <div class="col-12" style="padding-left: 6px;">
      <div class="row">
        <div [appStatusLine]="expenseDetail?.status" [indicator]="4" class="col-12"></div>
      </div>
    </div>
  </div>
  
  <div *ngIf="checkCommentVisibility(4, expenseDetail?.status) == true" class="row pt-2 pb-2">
    <div class="col-12 pl-0">
      <div class="card slide-in-top">
        <div *ngIf = "is_to_view_payment_done_by == 1">
  
        <div class="row d-flex pt-2 pb-2">
          <span class="ml-4 my-auto">
            <mat-icon class="done-icon">
              done
            </mat-icon>
          </span>
          <span class="content my-auto ml-3">
            Payment done by
          </span>
          <app-user-profile *ngIf="expenseDetail" style="padding-left: 11px;" type="expense-card-data"
            [oid]="expenseDetail.closed_by" imgHeight="28px" imgWidth="28px"></app-user-profile>
        </div>
        </div>
        <div class="row d-flex pt-2 pb-2">
          <span class="ml-4 my-auto">
            <mat-icon class="done-icon">
              info_outline
            </mat-icon>
          </span>
          <span class="grey-heading my-auto ml-3">
            Payment mode
          </span>
          <span class="content my-auto">
            <span *ngIf="fieldConfig?.cheque?.field_label"> - {{fieldConfig?.cheque?.field_label}}</span>
            <span *ngIf="!fieldConfig?.cheque?.field_label">Cheque</span> . Payment will get reflected in {{claimPaymentForm?.controls?.paymentDays?.value}}
          </span>
        </div>
      </div>
    </div>
  </div>
  </div>
  <!--End of Closed status -->
  
  <!-- <pre>{{ claimPaymentForm.value | json }}</pre> -->
  
  <!-- {{ approvalItemForm.value | json }} -->
  <!-- {{ claimPaymentForm.value | json }} -->
  <!-- <pre>{{expenseDetail|json}} </pre> 
  <pre>{{ ExpenseEntryFormData.value | json }}</pre>  -->
