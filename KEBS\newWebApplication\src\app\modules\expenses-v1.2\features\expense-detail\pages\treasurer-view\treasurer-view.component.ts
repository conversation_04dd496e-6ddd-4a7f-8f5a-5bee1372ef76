import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@angular/core";
import { ExpenseDetailService } from "../../services/expense-detail.service";
import { Router } from "@angular/router";
import { LoginService } from "src/app/services/login/login.service";
import { tap } from "rxjs/operators";
import { BanksComponent } from "../../components/banks/banks.component";
import {
  FormGroup,
  FormBuilder,
  FormArray,
  FormControl,
  Validators,
  AbstractControl, ValidationErrors
} from "@angular/forms";
import { Subscription } from "rxjs";
import * as _ from "underscore";
import {
  MatBottomSheet,
  MatBottomSheetRef,
} from "@angular/material/bottom-sheet";
import { ExpenseCreationService } from "src/app/modules/main/services/expense-services/expense-main.service";
import { ExpenseUtilityService } from "../../../../shared/services/expense-utility.service";
import { MatDialog } from "@angular/material/dialog";
// import { ImagePdfViewerComponent } from "src/app/shared/components/image-pdf-viewer/image-pdf-viewer.component";
import { FileSaverService } from "src/app/services/fileSaver/file-saver.service";
import * as moment from 'moment';
import { ExpenseRejectDialogComponent } from "../../../../lazy-loaded-components/expense-reject-dialog/expense-reject-dialog.component";
import {PeopleInvolvedComponent} from "../../../../shared/components/people-involved/people-involved.component";
import { MatSnackBar } from '@angular/material/snack-bar';
import { SatPopover } from '@ncstate/sat-popover';
import { ContactsHomeModule } from "src/app/modules/contacts/features/contacts-home/contacts-home.module";
import { CurrencyConversionModule } from "src/app/modules/admin-programs/features/currency-conversion/currency-conversion.module";
import { NgxSpinnerService } from 'ngx-spinner';
@Component({
  selector: "app-treasurer-view",
  templateUrl: "./treasurer-view.component.html",
  styleUrls: ["./treasurer-view.component.scss"],
})
export class TreasurerViewComponent implements OnInit {
  checkAll = new FormControl();
  expenseDetail: any;

  approvItems: any;
  checkAllSubscription: Subscription;
  itemDetailSubscription: Subscription;
  formArrayChangeSubscription: Subscription;
  approvalUpdateSubscription: Subscription;
  isMultipleApprovalAllowed: boolean = false;
  isAllItemBeingApproved: boolean = false;
  isAllItemBeingRejected: boolean = false;
  knockOffHistoryDetail = [];
  loading: boolean = true;
  isDownloading: boolean = false;
  approvalItemList: any = [];
  //forms
  approvalItemForm: FormGroup;
  claimPaymentForm: FormGroup;
  profile: any;
  isExpenseEntryLoaded: boolean = false;
  totalVerifiedAmount: number = 0;
  categoryTotals: number[] = [];
  accountLedgerdata : any;
  availableBanks: any;
  defaultLedgerAccount: any;
  logPopUp = false;
  triggerOrigin: any;
  //forms
  openOverlayIndex: number | null = null;
  openClaimOverlayIndex: number | null = null;
  isViewConversionRate: any;
  isReadOnly: Boolean = true // Used to disable expense ledger account , when target entity id is not chosen
  isTargetEntityReadOnly: Boolean = false;
  initialAmount: any;
  initialItemAmount: number[] = [];
  paymentDaysArray: any;
  billingConfig: any;
  verifiedAmount: number[] = []; 
  showClaimClosureActionIcons = false;
  remainingCharacters: number[][] = [];
  ledgerItemTotal : number[] = [];
  excessAmount: boolean[][] = [];
  showHint: boolean = false;
  showExpenseCard = false;
  costCenterList: { cost_centre: string; cost_centre_type: string }[] = [];
  costCentreType : any;
  constructor(
    private $router: Router,
    private _detailService: ExpenseDetailService,
    private _auth: LoginService,
    private fb: FormBuilder,
    private _creationService: ExpenseCreationService,
    private _bottomSheet: MatBottomSheet,
    private _util: ExpenseUtilityService,
    private _fileSaver: FileSaverService,
    public dialog: MatDialog,
    private snackBar: MatSnackBar,
    private _spinnerService: NgxSpinnerService
  ) {
    if (window.localStorage)
      this.expenseDetail = this.getExpenseDetailFromSession();
    this.getFormFieldConfig();
    this.getCustomerBillingConfig()
    this.createForm();
    this.createClaimPaymentForm();
    this.detectCheckAll();
    this.getExpenseStatusConfig();

    this.profile = this._auth.getProfile().profile;
    this.toDetermineTargetEntityIsReadOnly()
    this.getPaymentTerm();
    this.getAccountLedgerDescription();
  }

  getCustomerBillingConfig() {
    this._detailService
      .getExpenseBillingConfig()
      .subscribe(
        (res) => {
          this.billingConfig = res['data'];
        },
        (err) => {
          console.error(err);
        }
      );
  }

  async getPaymentTerm() {
    let paymentDays = await this.getPaymentDaysConfig();
    this.paymentDaysArray = paymentDays;
  }


  toDetermineTargetEntityIsReadOnly() {
    if (
      this.expenseDetail.status == "Verified" ||
      this.expenseDetail.status == "Closed") {
      this.isTargetEntityReadOnly = true;
    }

  }
  getLedgerAccountDetail() {

    return new Promise((resolve, reject) => {
      this._detailService.getLedgerAccountDetail(this.expenseDetail.claimed_by).subscribe(res => {
        resolve(res);
      },
        (err) => {
          console.error(err);
          reject(err);
        }
      );
    });
  }

  getTargetEntityBasedOnCostCenter() {

    return new Promise((resolve, reject) => {
      this._detailService.getTargetEntityBasedOnCostCenter(this.expenseDetail.cost_centre, this.expenseDetail.expense_header_id).subscribe(res => {
        resolve(res["data"]);
      },
        (err) => {
          console.error(err);
          reject(err);
        }
      );
    });
  }

  onledgerAccountChange = async () => {
    // if(this.creditLedgerType && (this.creditLedgerType != 'EL' || this.creditLedgerType != 'AID_NAME' || this.creditLedgerType == 'CORP_CARD') ){
    //   let creditLedger = this.claimPaymentForm.controls.ledgerAccount.value != null ? JSON.parse(this.claimPaymentForm.controls.ledgerAccount.value).associate_id : null;
    //   this.claimPaymentForm.controls.creditLedger.patchValue(creditLedger);
    //   this.claimPaymentForm.controls.creditLedgerCode.patchValue(this.creditLedgerType);
    // }
      if(this.claimPaymentForm.value.ledgerAccount){
        let ledgerAccount = JSON.parse(this.claimPaymentForm.value.ledgerAccount);
        let getCreditLedgerTenantDetails = await this._detailService.getCreditLedgerTenantDetails(ledgerAccount.associate_id, this.expenseDetail.expense_type, this.approvalItemList[0].expense_category_code, this.expenseDetail.expense_header_id);
        this.getCreditLedgerMasterData(ledgerAccount.associate_id);
        if (getCreditLedgerTenantDetails['messType'] == 'S') {
          this.creditLedgerType = getCreditLedgerTenantDetails['data']['credit_ledger_type'];
          this.enableCreditLedger = getCreditLedgerTenantDetails['data']['is_editable'];
          this.claimPaymentForm.controls.creditLedgerCode.patchValue(this.creditLedgerType);

          if (this.creditLedgerType == 'EL' || this.creditLedgerType == 'AID_NAME' || this.creditLedgerType == 'CORP_CARD') {
            if(getCreditLedgerTenantDetails['data']['credit_ledger_name']){
              let creditLedgerName  = getCreditLedgerTenantDetails['data']['credit_ledger_name'];
          
            this.claimPaymentForm.controls.creditLedger.patchValue(creditLedgerName);
            this.claimPaymentForm.controls.creditLedgerCode.patchValue(this.creditLedgerType);
            this.isToDisplayExpenseGl = true;
            }
            else{
              this.claimPaymentForm.controls.creditLedger.patchValue(ledgerAccount.associate_id);
              this.claimPaymentForm.controls.creditLedgerCode.patchValue("EL");
            }
            
          }
          else {
            this.isToDisplayExpenseGl = false;
            let creditLedgerName = this.creditLedgerType == 'Name' ? ledgerAccount.employee_name : ledgerAccount.associate_id;
            this.claimPaymentForm.controls.creditLedger.patchValue("EL");
          }
        }
        else {
          this.claimPaymentForm.controls.creditLedger.patchValue(ledgerAccount.associate_id);
          this.claimPaymentForm.controls.creditLedgerCode.patchValue("EL");
          this.creditLedgerMasterData = [];
        }
      }
    
  }

  detectCheckAll = () => {
    this.checkAllSubscription = this.checkAll.valueChanges.subscribe((res) => {
      if (res === true) this.checkAllItems();
      else this.uncheckAllItems();
    });
  };
  checkAllItems = () => {
    this.approvalItemForm.value.approvalItems.forEach((item, index) => {
      if (this.approvalItemForm.value.approvalItems[index].cc_status ===
        'Approved')  //! we can select only those are approved by approvers for verification
        this.approvalItemForm.controls["approvalItems"]["controls"][index][
          "controls"
        ]["check"].patchValue(true);
    });
    // this.isMultipleApprovalAllowed = true;
  };
  uncheckAllItems = () => {
    this.approvalItemForm.value.approvalItems.forEach((item, index) => {
      this.approvalItemForm.controls["approvalItems"]["controls"][index][
        "controls"
      ]["check"].patchValue(false);
    });
    // this.isMultipleApprovalAllowed = false;
  };

  createForm = () => {
    this.approvalItemForm = this.fb.group({
      approvalItems: this.fb.array([]),
    });
  };

  createClaimPaymentForm = async () => {
    this.claimPaymentForm = this.fb.group({
      ledgerAccount: [await this.getLedgerAccountDetail(), Validators.required],
      ledgerAccountDescription: ["", [Validators.required, Validators.maxLength(255)]],
      // targetEntity: ["", Validators.required],
      targetEntity: ["", Validators.required],
      paymentMode: ["", Validators.required],
      expenseEntry: this.fb.array([]),
      cheques: this.fb.array([]),
      paymentDays: ["2-3 working days", Validators.required],
      creditLedger: ["", Validators.required],
      creditLedgerCode: [""],
      costCenterCodeBeforeUpdate : [this.expenseDetail.cost_centre,  Validators.required],
      costCenterCode : [this.expenseDetail.cost_centre,  Validators.required],
      costCenter: [""],
      costCenterDepartmentBeforeUpdate: [this.expenseDetail.department],
      costCenterDepartment: [this.expenseDetail.department],
      customerBillingBeforeUpdate: [this.expenseDetail.is_billable],
      customerBilling: [this.expenseDetail.is_billable ? this.expenseDetail.is_billable : 3,  Validators.required]
    });
    console.log('Initial claimPaymentForm:', this.claimPaymentForm.value);
  
  };

  detectPaymentModeChanges = () => {
    this.claimPaymentForm.get("paymentMode").valueChanges.subscribe((res) => {
      if (res == "cheque") this.setExpenseChequeFormData();
    });
  };

  

  creditLedgerType : any;
  isToDisplayExpenseGl : boolean = false;

  async ngOnInit() {
    this.getExpenseConfigfunction();
    this.getCurrencyConversionTooltip();
    this.getExpenseGeneralConfig();
    if (window.localStorage) {
      // this.expenseDetail = this.getExpenseDetailFromSession();

      this.knockOffHistoryDetail = this.expenseDetail.knocked_off_history ?
        typeof (this.expenseDetail.knocked_off_history) == 'string' ?
          JSON.parse(this.expenseDetail.knocked_off_history) : this.expenseDetail.knocked_off_history : [];

      this.expenseDetail == null ? this.redirectToHomePage() : {};
      console.log(this.expenseDetail);

      this.setApprovItems();
      this.getCreditLedgerMasterData(this.expenseDetail.claimer_aid);

      await this._detailService.getAllCostCenter().then((res: any)=>{
        this.costCenterList = res;
        this.costCentreType = this.costCenterList
          .find(costCentre => costCentre.cost_centre == this.expenseDetail.cost_centre)
          ?.cost_centre_type || "D";

        // this.costCentreType = this.costCenterList.find(costCentre => costCentre.cost_centre == this.expenseDetail.cost_centre)?.cost_centre_type || "D";
      })
    } else {
      console.log("local storage not supported!");
    }

    this.getExpenseItemData();
    this.getExpenseUiConfig();
    this.getCommentBoxData();
  }

  primaryColor: any
  async getExpenseUiConfig(){
    this._detailService.getExpenseUiMasterConfig(1)
    .subscribe(
      (res) => {
        if(res['messType']=="S"){
          console.log("fontFamily")
          console.log( res['data'][0]['font_family'])

          this.primaryColor = res['data'][0]['primary_colour'];
          document.documentElement.style.setProperty(
            '--expenseColor',
            this.primaryColor
          );
        }
      }
      )
    }

    async changeCostCenter(event: any){
      console.log("changeCostCenter")
      console.log(event)
      this.claimPaymentForm.controls.costCenter.patchValue(event);
      this.claimPaymentForm.controls.customerBilling.patchValue(3);
      let costcenter = this.claimPaymentForm.value.costCenter;
      console.log("costcenter")
      console.log(costcenter)
      this.costCentreType = this.parseJSON(costcenter)?.cost_centre_type;
      if (this.costCentreType == "I") {
               console.log("inside if elseinside else")
        this.claimPaymentForm.get("customerBilling").setValidators([
            Validators.required,
            Validators.pattern("^[12]$") // Ensures value is either 1 or 2
        ]);
    } else {
       console.log("inside elseinside elseinside else")
        this.claimPaymentForm.get("customerBilling").setValidators([
            Validators.required // Default required validator
        ]);
    }
      let costCenterCode =  this.parseJSON(costcenter)?.cost_centre ;
      await this.getDepartmentMaster(costCenterCode);
    }

    getDepartmentMaster = (costCenter) => {
  
      this._detailService.getDepartmentMaster(costCenter).subscribe(
        async (res: any) => {
  
          if (res && res.length == 1) {
            this.claimPaymentForm.get("costCenterDepartment").patchValue(res[0].department_name);
          }
  
        },
        (err) => {
          console.error(err);
        }
      );
    };

    toggleOverlay(index: number) {
      this.openOverlayIndex = this.openOverlayIndex == index ? null : index;
    }

    toggleClaimOverlay(index: number) {
      this.openClaimOverlayIndex = this.openClaimOverlayIndex === index ? null : index;
    }


  expenseItemData = {};
  async getExpenseItemData(){
    let expenseData = await this._detailService.getExpenseItemData(this.expenseDetail.expense_header_id);
    console.log("expenseData")
    console.log(expenseData)
    if(expenseData["data"] && expenseData["data"].length > 0){
      expenseData["data"].forEach((element) => {
        if(element && element.item_amount && typeof(element.item_amount) == "string"){
          let val = this.parseJson(element.item_amount);
          let claim_amount = _.filter(val,{"currency_code": element.claim_raised_currency});
          if(claim_amount.length > 0){
            element.claim_amount = this.fixNumberOnUI(claim_amount[0].claim_amount, element.claim_raised_currency);
            element.tax_amount = this.fixNumberOnUI(claim_amount[0].tax_amount, element.claim_raised_currency);
          }
        }
        element.invoice_date = moment(element.invoice_date).format("DD-MMM-YYYY");
        this.expenseItemData[element.id] = element;
        
        console.log("expenseItemData")
        console.log(this.expenseItemData)
      });
    }
  }

  fetchReport(popover: SatPopover){
    popover.close();
  }

  isToShowTooltip = false;
  async getCurrencyConversionTooltip(){
    let expenseConfig = await this._detailService.getExpenseConfig();

    if(expenseConfig){
      let expenseConfigData = JSON.parse(expenseConfig['data'][0]['expense_application_config'])
      if(expenseConfigData!=null){
      if(expenseConfigData.isToShowTooltip==1){
        this.isToShowTooltip=true;
      };
    }
    }
  }

  expenseGeneralConfig: any;
  treasuryDisplayName: any;
  isTreasuryNameBasedOnConfig = false;
  async getExpenseGeneralConfig(){
    let expenseConfig = await this._detailService.getExpenseGeneralConfig();
    console.log("expenseConfig")
    console.log(expenseConfig)
    if(expenseConfig["messType"] == "S"){
      this.expenseGeneralConfig = expenseConfig["data"];
      if(this.expenseGeneralConfig){
        console.log("expenseGeneralConfig")
        console.log(this.expenseGeneralConfig)
        this.treasuryDisplayName = this.expenseGeneralConfig.treasury_display_name.config_value;
        if(this.expenseGeneralConfig.is_treasury_name_based_on_config.config_value && parseInt(this.expenseGeneralConfig.is_treasury_name_based_on_config.config_value) == 1){
          console.log("this.expenseGeneralConfig.is_treasury_name_based_on_config")
          console.log(this.expenseGeneralConfig.is_treasury_name_based_on_config.config_value)
          this.isTreasuryNameBasedOnConfig = true
        }
      }
    }
  }

  creditLedgerMasterData: any;
  async getCreditLedgerMasterData(claimer_aid){
    let creditLedgerData = await this._detailService.getCreditLedgerMasterData(claimer_aid, this.expenseDetail.expense_header_id);
    this.creditLedgerMasterData = creditLedgerData['data'];
  }

  focusedItem: { j: number, m: number } = { j: -1, m: -1 };

isFocused(j: number, m: number): boolean {
  return this.focusedItem.j === j && this.focusedItem.m === m;
}

setFocused(j: number, m: number): void {
  this.focusedItem = { j, m };
}

setBlurred(): void {
  this.focusedItem = { j: -1, m: -1 };
}

  setApprovItems = () => {
    let headerId = this.expenseDetail["expense_header_id"];
    let role = this.expenseDetail["role"];
    let userOid = this.profile.oid;
    if (headerId && role && userOid)
      this.itemDetailSubscription = this._detailService
        .getApprovalItemDetail(headerId, role, userOid)
        .pipe(
          tap((arr) => {
            if (arr) this.loading = false;
          })
        )
        .subscribe(
          (res) => {
            console.log(res);
            this.approvalItemList = res;
            this.setApprovalItemArray();
            this.getCreditLedgerTenantDetails();
          },
          (err) => {
            console.error(err);
          }
        );
    else console.error("some params missing!");
  };

  enableCreditLedger = true;
  async getCreditLedgerTenantDetails(){
    let getCreditLedgerTenantDetails = await this._detailService.getCreditLedgerTenantDetails(this.expenseDetail.claimer_aid, this.expenseDetail.expense_type, this.approvalItemList[0].expense_category_code, this.expenseDetail.expense_header_id);
    if(getCreditLedgerTenantDetails['messType'] == 'S'){
      this.creditLedgerType = getCreditLedgerTenantDetails['data']['credit_ledger_type'];
      this.enableCreditLedger = getCreditLedgerTenantDetails['data']['is_editable'];

      if(!this.claimPaymentForm.controls.creditLedger.valid){
        if (this.creditLedgerType == 'EL' || this.creditLedgerType == 'AID_NAME' || this.creditLedgerType == 'CORP_CARD') {
          if(getCreditLedgerTenantDetails['data']['credit_ledger_name']){
            
          let creditLedgerName  = getCreditLedgerTenantDetails['data']['credit_ledger_name'];
          let creditLedgerCode = getCreditLedgerTenantDetails['data']['credit_ledger_type'];
        
          this.claimPaymentForm.controls.creditLedger.patchValue(creditLedgerName);
          this.claimPaymentForm.controls.creditLedgerCode.patchValue(creditLedgerCode);
          this.isToDisplayExpenseGl = true;
          }
          else{
            this.claimPaymentForm.controls.creditLedger.patchValue(this.expenseDetail.claimer_aid);
            this.claimPaymentForm.controls.creditLedgerCode.patchValue("EL");
          }
        }
        else {
          this.isToDisplayExpenseGl = false;
          let creditLedgerName = this.creditLedgerType == 'Name' ? this.expenseDetail.claimed_by_name : this.expenseDetail.claimer_aid;
          this.claimPaymentForm.controls.creditLedger.patchValue(creditLedgerName);
          this.claimPaymentForm.controls.creditLedgerCode.patchValue("EL");

        }
      }
    }
    else{
      if(!this.claimPaymentForm.controls.creditLedger.valid){
        this.claimPaymentForm.controls.creditLedger.patchValue(this.expenseDetail.claimer_aid);
        this.claimPaymentForm.controls.creditLedgerCode.patchValue("EL");
      }
    }
  }

  changeCreditLedgervalue(val){
    this.claimPaymentForm.controls.creditLedger.patchValue(val.employee_ledger_name);
    this.claimPaymentForm.controls.creditLedgerCode.patchValue(val.credit_ledger_type);
  }

  getExpenseDetailFromSession = () => {
    return JSON.parse(sessionStorage.getItem("expenseDetail"));
  };
  redirectToHomePage = () => {
    this.$router.navigateByUrl("/main/expenses-v1.2");
  };
  get approvalItemFormData() {
    let approvalItemArray = this.approvalItemForm.controls;
    return approvalItemArray;
  }

  // isExcessAmount(j: number, m: number, index: number): boolean {
  //     this.ledgerItemTotal = [];
  //     const expenseEntries = this.ExpenseEntryFormData.controls;
  //     expenseEntries.forEach((entry, index) => {
  //       let total = 0;
  //       const ledgerAmountDistribution = entry.get('ledgerAmountDistribution') as FormArray;
  //       ledgerAmountDistribution.controls.forEach(item => {
  //         const amount = parseFloat(item.get('ledger_item_amount').value);
  //        {
  //           total += amount;
  //         }
  //       });
        
  //       this.ledgerItemTotal[index] = total;
  //       console.log("this.ledgerItemTotal[index]")
  //       console.log(this.ledgerItemTotal[index])
  //       console.log("this.categoryTotals[index]")
  //       console.log(this.categoryTotals[index])
  //       console.log("this.initialItemAmount[index]")
  //       console.log(this.initialItemAmount[index])
  //     });
  //     if (this.initialItemAmount[index] < this.categoryTotals[index]){
  //       return true
  //     }
  //     return false
  //   }


  setApprovalItemArray = async () => {
    const control = <FormArray>this.approvalItemFormData.approvalItems;
    this.approvalItemList.forEach((item, index) => {
      item.people_involved = item.people_involved!=null ? JSON.parse(item.people_involved) : null;
      item.people_involved_display_array = item.people_involved!=null ? item.people_involved.slice(0,3) : null
      let itemAmount = this.parseJson(item.item_amount);
        let entityTaxAmount = null;
        if(this.expenseDetail.legal_entity_currency){
          entityTaxAmount = _.filter(itemAmount,{currency_code: this.expenseDetail.legal_entity_currency});
        }
        else{
          entityTaxAmount = _.filter(itemAmount,{"is_legal_entity_currency": 1});
        }
      control.push(
        this.fb.group({
          check: false,
          treasury_wf_header_id: item.treasury_wf_header_id,
          description: item.description,
          expense_category_id: item.expense_category_id,
          expense_category_name: item.expense_category_name,
          expense_category_description: item.expense_category_description,
          expense_item_id: item.expense_item_id,
          invoice_date: item.invoice_date,
          t_is_curr_appr: item.t_is_curr_appr,
          item_amount: item.item_amount,
          cc_status: item.cc_status == "S" ? "Awaiting Approval" : item.cc_status == "A" ? "Approved" : "Rejected",
          treasuryStatus: item.treasury_status == "S" ? "Submitted" : item.treasury_status == "R" ? "Rejected" : "Approved",
          status: item.status,
          attachment: JSON.parse(item.attachment),
          context_id: item.context_id,
          isAttachmentLoading: false,
          isItemBeingApproved: false,
          isItemBeingRejected: false,
          cc_reject_comment: item.cc_reject_comment ? typeof item.cc_reject_comment == "string" ? JSON.parse(item.cc_reject_comment) : item.cc_reject_comment : null,
          t_reject_comment: item.t_reject_comment ? typeof item.t_reject_comment == "string" ? JSON.parse(item.t_reject_comment) : item.t_reject_comment : null,
          is_billable: item.is_billable,
          people_involved: null,
          people_involved_display_array: null,
          invoice_no: item.invoice_no,
          user_defined_conversion_rate : item.user_defined_conversion_rate,
          system_defined_conversion_rate : item.system_defined_conversion_rate,
          claim_raised_currency : item.claim_raised_currency,
          entity_currency : item.entity_currency,
          taxCredit: [item.taxCredit, [validateAmount]],
          taxLedger: item.taxLedger,
          taxCreditWarning: null,
          taxPercentage: [item.tax_type ? item.tax_type : null],
          taxAmount: [entityTaxAmount && entityTaxAmount.length > 0 ? entityTaxAmount[0].value : null],
          perdiem_start_date: item.perdiem_start_date,
          perdiem_start_time: item.perdiem_start_time,
          perdiem_end_date: item.perdiem_end_date,
          perdiem_end_time: item.perdiem_end_time,
          days: item.days,
          location: item.location,
          from_location: item.from_location,
          to_location: item.to_location,
          unit: item.unit,
          vehicle_type: item.vehicle_type,
          vehicle_engine_type: item.vehicle_engine_type,
          miles: item.miles
        })
      );
      this.approvalItemForm.controls.approvalItems['controls'][index].controls.people_involved.patchValue(item.people_involved);
      this.approvalItemForm.controls.approvalItems['controls'][index].controls.people_involved_display_array.patchValue(item.people_involved_display_array);
    });
    this.detectFormArrayChanges();
    if (this.expenseDetail.status == "Approved" ||
      this.expenseDetail.status == "Verified" ||
      this.expenseDetail.status == "Closed"
    ) {
      let savedPaymentDetails = await this.getPaymentDetails();

      if (savedPaymentDetails == null) {

        this.setExpenseEntryFormArrayData();
        this.setTargetEntityInitially();
        this.setPaymentDaysInitially();

      }
      else {
        this.patchDetailsInClaimForm(savedPaymentDetails);
      }
    }
  };

  async setTargetEntityInitially() {
    let targetEntity = await this.getTargetEntityBasedOnCostCenter();
    this.claimPaymentForm.patchValue({
      targetEntity: targetEntity
    })
  }

  setPaymentDaysInitially() {
    this.claimPaymentForm.addControl("paymentDays", this.fb.control('', [Validators.required]))
  }

  patchDetailsInClaimForm = (detail) => {
    console.log(detail)
    this.claimPaymentForm.patchValue({
      ledgerAccount: typeof(detail.ledgerAccount) == "string" ? detail.ledgerAccount : JSON.stringify(detail.ledgerAccount),
      ledgerAccountDescription: detail.ledgerAccountDescription,
      targetEntity: detail.targetEntity,
      paymentMode: detail.paymentMode,
      paymentDays: detail.paymentDays ? detail.paymentDays : "2-3 working days",
      creditLedger: detail.creditLedger,
      creditLedgerCode : detail.creditLedgerCode
    });
    let expenseEntries = detail.expenseEntry;
    if (expenseEntries.length > 0) {
      this.addDetailInExpenseEntryArray(expenseEntries);
    } else {
      console.error("expense entries 0");
      this.setExpenseEntryFormArrayData();
    }
    let chequeEntries = detail.cheques;
    if (chequeEntries.length > 0 && detail.paymentMode == "cheque") {
      this.addDetailInChequeEntryArray(chequeEntries);
    } else {
      this.setExpenseChequeFormData();
      console.error("Check Entries 0");
    }
  };

  addDetailInExpenseEntryArray = (entries) => {
    const control = <FormArray>this.claimPaymentForm.controls.expenseEntry;
    entries.forEach((item, index) => {
      const ledgerAmountDistributionArray = this.fb.array(
        item.ledgerAmountDistribution.map(ledgerItem => 
          this.fb.group({
            narration: ledgerItem.narration,
            currency_code: ledgerItem.currency_code,
            expense_account: ledgerItem.expense_account,
            ledger_item_amount: ledgerItem.ledger_item_amount
          })
        )
      );
      control.push(
        this.fb.group({
          expense_item_id: item.expense_item_id,
          item_index: item.item_index ? item.item_index : _.indexOf(_.pluck(this.approvalItemList, "expense_item_id"), item.expense_item_id),
          expense_category_name: item.expense_category_name,
          item_amount: item.item_amount,
          // expense_account: [item.expense_account, Validators.required],
          // description: [item.description, Validators.required],
          narration: [item.narration],
          ledgerAmountDistribution: ledgerAmountDistributionArray,
          taxCredit: [item.taxCredit ? item.taxCredit : null,[validateAmount]],
          taxLedger: [item.taxLedger ? item.taxLedger : null],
          taxCreditWarning: null,
          perdiem_start_date: item.perdiem_start_date,
          perdiem_start_time: item.perdiem_start_time,
          perdiem_end_date: item.perdiem_end_date,
          perdiem_end_time: item.perdiem_end_time,
          days: item.days,
          location: item.location,
          from_location: item.from_location,
          to_location: item.to_location,
          unit: item.unit,
          vehicle_type: item.vehicle_type,
          vehicle_engine_type: item.vehicle_engine_type,
          miles: item.miles,
          expense_category_id: item.expense_category_id
        })
      );
      this.calculateTotalVerifiedAmount();
    });
    this.isExpenseEntryLoaded = true;
  };

  addDetailInChequeEntryArray = (entries) => {
    const control = <FormArray>this.claimPaymentForm.controls.cheques;
    entries.forEach((item, index) => {
      control.push(
        this.fb.group({
          bank: [item.bank, Validators.required],
          accNo: [item.accNo, Validators.required],
          branch: [item.branch, Validators.required],
          bankLedgerName: [item.bankLedgerName, Validators.required],
          chequeNo: [item.chequeNo, [Validators.required,Validators.min(1)]],
          chequeAmount: [item.chequeAmount < 0 ? 0 : item.chequeAmount, [Validators.required, validateAmount]],
          chequeStatus: [item.chequeStatus],
          isBankSelected: item.isBankSelected,
        })
      );
    });
  };

  setExpenseEntryFormArrayData = () => {
    console.log(this.approvalItemList);
    const control = this.claimPaymentForm.get('expenseEntry') as FormArray;
    this.approvalItemForm.value.approvalItems.forEach((item, index) => {
      if (item.cc_status === 'Approved') {
        const parsedResult = this.parseAndFilterExpenseCurrency(item.item_amount);
        const initialLedgerItemAmount: string | null = parsedResult ? parsedResult?.value?.toFixed(2) : null;
        const currencyCode = parsedResult ? parsedResult.currency_code : '';
  
        let itemAmount = this.parseJson(item.item_amount);
        let entityTaxAmount = null;
        if(this.expenseDetail.legal_entity_currency){
          entityTaxAmount = _.filter(itemAmount,{currency_code: this.expenseDetail.legal_entity_currency});
        }
        else{
          entityTaxAmount = _.filter(itemAmount,{"is_legal_entity_currency": 1});
        }
       
        control.push(this.fb.group({
            expense_item_id: item.expense_item_id,
            item_index: index,
            expense_category_name: item.expense_category_name,
            user_defined_conversion_rate :item.user_defined_conversion_rate,
            system_defined_conversion_rate : item.system_defined_conversion_rate,
            claim_raised_currency : item.claim_raised_currency,
            entity_currency : item.entity_currency,
            item_amount: item.item_amount,
            taxCredit: [item?.taxCredit ? item?.taxCredit : null, [validateAmount]],
            taxLedger: [item?.taxLedger ? item?.taxLedger : null],
            taxCreditWarning: [false],
            ledgerAmountDistribution: this.fb.array([this.fb.group({
            ledger_item_amount: [initialLedgerItemAmount, [Validators.required, this.validAmountValidator]],
            expense_account: ["", Validators.required],
            narration: [""],
            currency_code: [currencyCode],
          })]),
          taxPercentage: [item.tax_type ? item.tax_type : null],
          taxAmount: [entityTaxAmount && entityTaxAmount.length > 0 ? entityTaxAmount[0].tax_amount : null],
          context_id: item.context_id,
          perdiem_start_date: item.perdiem_start_date,
          perdiem_start_time: item.perdiem_start_time,
          perdiem_end_date: item.perdiem_end_date,
          perdiem_end_time: item.perdiem_end_time,
          days: item.days,
          location: item.location,
          from_location: item.from_location,
          to_location: item.to_location,
          unit: item.unit,
          vehicle_type: item.vehicle_type,
          vehicle_engine_type: item.vehicle_engine_type,
          miles: item.miles,
          expense_category_id: item.expense_category_id
        }));
      }
    });
    this.isExpenseEntryLoaded = true;
    this. getInitialAmount();
    this.detectPaymentModeChanges();
    this.calculateTotalVerifiedAmountOnFormChanges();
  };
  



  initializeForm(): void {
    const expenseEntryArray = this.claimPaymentForm.get('expenseEntry') as FormArray;
    expenseEntryArray.controls.forEach((entry, index) => {
      console.log('entry.value?.item_amount:', entry.value?.item_amount);
      const ledgerAmountDistributionArray = entry.get('ledgerAmountDistribution') as FormArray;
      ledgerAmountDistributionArray.controls.forEach((item) => {
        const itemAmountControl = item.get('ledger_item_amount');
        if (itemAmountControl) {
          const jsonString = this.claimPaymentForm.value.expenseEntry[index].item_amount;
          const parsedResult = this.parseAndFilterExpenseCurrency(jsonString);
          if (parsedResult) {
            itemAmountControl.setValue(parseFloat(parsedResult.value.toFixed(2)));
            if (item instanceof FormGroup) {
              item.addControl('currency_code', this.fb.control(parsedResult.currency_code));
            }
          }
        }
      });
    });
  }
  
  getInitialAmount(): void {
    this.initialItemAmount = [];
    const expenseEntries = this.ExpenseEntryFormData.controls;
  
    expenseEntries.forEach((entry, index) => {
      const initialAmountString = entry.get('item_amount')?.value || '[]';
      const parsedAmount = this.parseAndFilterExpenseCurrency(initialAmountString)?.value || 0;
      this.initialItemAmount[index] = parsedAmount;
  
      console.log("this.initialItemAmount[index]");
      console.log(this.initialItemAmount[index]);
    });
  }

  
  addLedgerAmountdistribution(j: number, m: number): void {
    const control = (this.claimPaymentForm.get('expenseEntry') as FormArray).at(j).get('ledgerAmountDistribution') as FormArray;
  
    const expenseEntry = this.ExpenseEntryFormData.at(j) as FormGroup;
    const ledgerAmountDistribution = expenseEntry.get('ledgerAmountDistribution') as FormArray;
    const initialAmountString = expenseEntry.get('item_amount')?.value || '[]';
    this.initialAmount = this.parseAndFilterExpenseCurrency(initialAmountString)?.value || 0;
  

    const usedAmount = ledgerAmountDistribution.controls.reduce((sum, control) => {
      const value = parseFloat(control.get('ledger_item_amount')?.value || 0);
      return sum + (isNaN(value) ? 0 : value);
    }, 0);
    const remainingAmount = (this.initialAmount - usedAmount).toFixed(2);
    console.log("remainingAmount")
    console.log(remainingAmount)

   
    const ledgerDistributionGroup = this.fb.group({
      ledger_item_amount: [remainingAmount, [Validators.required, this.validAmountValidator]],// Initial amount can be zero, or modify as needed
      expense_account: ["", Validators.required],
      narration: [""],
      currency_code: [this.parseAndFilterExpenseCurrency(initialAmountString)?.currency_code || ''] // Prefill currency_code
    });
  
    control.push(ledgerDistributionGroup);
    this.calculateTotalVerifiedAmountOnFormChanges();
  }
  


  validAmountValidator(control: any) {
    const value = control.value;
    return value > 0 ? null : { validAmount: true };
  }

  showErrorIfInvalid(j: number): void {
    if (this.initialAmount < this.categoryTotals[j]) {
      this._util.showMessage("Verified amount cannot be greater than claimed amount", "Dismiss", 3000);
    }
  }

  // removeLedgerAmountdistribution = (index) => {
  //   const control = <FormArray>this.claimPaymentForm.controls.ledgerAmountDistribution;
  //   control.removeAt(index);
  // };

  removeLedgerAmountdistribution = (expenseEntryIndex: number, ledgerDistributionIndex: number) => {
    const expenseEntryArray = this.claimPaymentForm.get('expenseEntry') as FormArray;
    const ledgerAmountDistributionArray = expenseEntryArray.at(expenseEntryIndex).get('ledgerAmountDistribution') as FormArray;
    
    if (ledgerAmountDistributionArray) {
      ledgerAmountDistributionArray.removeAt(ledgerDistributionIndex);
    } else {
      console.error(`ledgerAmountDistribution array is not found for expense entry at index ${expenseEntryIndex}`);
    }
  };
  
  getExpenseLedgerParams() {

    // if (this.claimPaymentForm.get("targetEntity").valid) {

    //   let targetEntity = this.claimPaymentForm.get("targetEntity").value;
    //   targetEntity = JSON.parse(targetEntity);
    //   let targetEntityId = targetEntity.entity_id;

    //   return {
    //     'legal_entity_id': targetEntityId
    //   }
    // }
    // else {
      return {
        'legal_entity_id': this.expenseDetail.legal_entity_id,
      }
    // }
  }

  getExpenseLedgerByEntityParams() {

    // if (this.claimPaymentForm.get("targetEntity").valid) {

    //   let targetEntity = this.claimPaymentForm.get("targetEntity").value;
    //   targetEntity = JSON.parse(targetEntity);
    //   let targetEntityId = targetEntity.entity_id;

    //   return {
    //     'legal_entity_id': targetEntityId
    //   }
    // }
    // else {
      return {
        'legal_entity_id': this.expenseDetail.legal_entity_id,
      }
    // }
  }

  roundOffToTwoDigits(value: number): number {
    return parseFloat(value.toFixed(2));
  }

  getLegalEntityId() {

    return {
      'legalEntityId': this.expenseDetail.legal_entity_id,
    }

  }

  checkAccountValidity(index: number): boolean {
    let expenseEntry = (this.claimPaymentForm.get('expenseEntry') as FormArray).at(index) as FormGroup;
    let ledgerAmountDistribution = expenseEntry.get('ledgerAmountDistribution') as FormArray;
  
    for (let i = 0; i < ledgerAmountDistribution.length; i++) {
      let ledgerGroup = ledgerAmountDistribution.at(i) as FormGroup;
      let expenseAccount = ledgerGroup.get('expense_account');
      let expenseAccountValue = expenseAccount?.value?.trim();
      let isAccountInvalid = !expenseAccountValue || expenseAccount?.invalid;
      let ledgerItemAmount = ledgerGroup.get('ledger_item_amount').value;
      if (expenseAccount?.invalid || isAccountInvalid|| !ledgerItemAmount || parseFloat(ledgerItemAmount ||  this.initialItemAmount[index] < this.categoryTotals[index]) <= 0) {
        return true;
      }
    }

 
    return false;
  }
  

  // checkAccountValidity(index) {
  //   return this.claimPaymentForm["controls"].expenseEntry["controls"][index]["controls"]["expense_account"].invalid;
  // }

  async setExpenseChequeFormData() {

    console.log("setExpenseChequeFormData");


    const control = <FormArray>this.claimPaymentForm.controls.cheques;
    let i = control.length - 1;
    while (control.length != 0) {
      control.removeAt(i);
      i--;
    }
    let totalChequeAmount = await this.findAmountToBeAddedToCheque()
    control.push(
      this.fb.group({
        bank: ["", Validators.required],
        accNo: ["", Validators.required],
        branch: ["", Validators.required],
        bankLedgerName: ["", Validators.required],
        chequeNo: ["", [Validators.required,Validators.min(1)]],
        chequeAmount: [
          totalChequeAmount>0 ? totalChequeAmount : 0,
          [Validators.required, validateAmount]
        ],
        chequeStatus: [""],
        isBankSelected: false,
      })
    );

    this.findAmountToBeAddedToCheque();
    await this.setDefaultSourceEntityBank(0);
  };

  get ExpenseEntryFormData() {
    let control = <FormArray>this.claimPaymentForm.controls.expenseEntry;
    return control;
  }

  get chequeFormData() {
    let control = <FormArray>this.claimPaymentForm.controls.cheques;
    return control;
  }


  // get ledgerAmountDistributionFormData() {
  //   let control = <FormArray>this.claimPaymentForm.controls.ledgerAmountDistribution;
  //   return control;
  // }

  get ledgerAmountDistributionFormData() {
    return (this.claimPaymentForm.get('expenseEntry') as FormArray).controls.map((control) => control.get('ledgerAmountDistribution') as FormArray);
  }

  // parseAndFilterExpenseCurrency(jsonString: string): { value: number | null, currency_code: string | null } | null {
  //   try {
  //     const amounts = JSON.parse(jsonString);
  //     // const expenseCurrency = amounts.find((item: any) => item.is_expense_currency);
  //     // return expenseCurrency ? { value: expenseCurrency.value, currency_code: expenseCurrency.currency_code } : null;
  //     let expenseCurrency = [];
  //     if(this.expenseDetail.legal_entity_currency){
  //       expenseCurrency = _.filter(amounts,{"currency_code": this.expenseDetail.legal_entity_currency});
  //     }
  //     else{
  //       expenseCurrency = _.filter(amounts,{"is_legal_entity_currency": 1});
  //     }
  //     return expenseCurrency && expenseCurrency.length > 0 ? { value: expenseCurrency[0].value, currency_code: expenseCurrency[0].currency_code } : null;
  //   } catch (error) {
  //     console.error('Error parsing or filtering JSON:', error);
  //     return null;
  //   }
  // }
  

  parseAndFilterExpenseCurrency(jsonString: string): { value: number | null, currency_code: string | null } | null {
    try {
      const amounts = JSON.parse(jsonString);
      const expenseCurrency = amounts.find((item: any) => item.is_expense_currency);
      return expenseCurrency ? { value: expenseCurrency.value, currency_code: expenseCurrency.currency_code } : null;
    } catch (error) {
      console.error('Error parsing or filtering JSON:', error);
      return null;
    }
  }

  calculateTotalVerifiedAmount(): void {
    this.categoryTotals = [];
    const expenseEntries = this.ExpenseEntryFormData.controls;

    expenseEntries.forEach((entry, index) => {
      let total = 0;
      console.log("index in verified")
      console.log(index)
      const ledgerAmountDistribution = entry.get('ledgerAmountDistribution') as FormArray;
      console.log("ledgerAmountDistributionin veri")
      console.log(ledgerAmountDistribution)
      ledgerAmountDistribution.controls.forEach(item => {
        const amount = parseFloat(item.get('ledger_item_amount').value);
        console.log("amountinver")
        console.log(amount)
        if (!isNaN(amount)) {
          total += amount;
        }
      });
      if(this.categoryTotals){
        this.categoryTotals[index] = total;
      }
     
    });
  }
  
  calculateTotalVerifiedAmountOnFormChanges(): void {
    this.claimPaymentForm.valueChanges.subscribe(() => {
      this.calculateTotalVerifiedAmount();
    });
  }

  detectFormArrayChanges = () => {
    this.formArrayChangeSubscription = this.approvalItemForm
      .get("approvalItems")
      .valueChanges.subscribe((res: any) => {
        setTimeout(() => {
          let expenseItemSelectedArr = _.where(res, {
            check: true,
            t_is_curr_appr: 1,
          });
          if (expenseItemSelectedArr.length >= 1)
            this.isMultipleApprovalAllowed = true;
          else this.isMultipleApprovalAllowed = false;
        }, 500);
      });
    //Subscribe Target Legal Entity
    this.claimPaymentForm.get('targetEntity').valueChanges.subscribe(async val => {

      console.log("targetEntity subscribe")
      console.log(val)
      if (val == "" || val == null){
        // this.isReadOnly = true;
        this.isReadOnly = false;
      }
      else if (val && this.expenseDetail.status != "Verified" && this.expenseDetail.status != "Closed")
        this.isReadOnly = false;

    })
  };


  approveAll = async () => {
    let expenseItemSelectedArr = _.where(this.approvalItemForm.value.approvalItems,
      {
        check: true,
        t_is_curr_appr: 1,
      });
    let finalArr = expenseItemSelectedArr.map((item, index) => {
      let obj = {
        approverOid: this.profile.oid,
        tWorkflowHeaderId: item.treasury_wf_header_id,
        status: "A",
        // targetEntity: this.claimPaymentForm.value.targetEntity,
        // sourceEntityId: this.expenseDetail.legal_entity_id
      };
      return obj;
    });
    console.log(finalArr);
    this.isAllItemBeingApproved = true; // starting spinner
    let res = await this.updateApprovalStatus(finalArr, null);
    this.isAllItemBeingApproved = false; // stoppimg spinner

    console.log(res);
    if (res === "Submitted") {
      this.updateStatusOnForm();
    } else if (res === "Verified") {
      // change as veri comp
      this.knockOffClaim();
      this.updateStatusOnForm();
      this.updateStatusInSession();
    }
  };

  getAccountLedgerDescription = () => {
    let claimId = this.expenseDetail['expense_header_id'];

    return new Promise((resolve, reject) => {
     this._detailService.accountLedgerDescription(
       claimId
     ).subscribe(res => {
       if(res && res!=null){
         console.log("Account Ledger description")
       console.log(res['data']);
       this.accountLedgerdata = res['data'] ?? [];
       let ledgerData = this.accountLedgerdata.length > 0 ? this.accountLedgerdata[0].result : null;
       console.log("ledgerData")
       console.log(ledgerData)
       this.claimPaymentForm.controls.ledgerAccountDescription.patchValue(ledgerData);
       resolve(res['data'])
       }
       else{
         resolve(res)
       }
     }, err => {
       reject(err)
     })

   })
 }


  knockOffClaim = () => {
    let init_oid = this.expenseDetail['claimed_by'];
    let claimId = this.expenseDetail['expense_header_id'];

    return new Promise((resolve, reject) => {

      this._detailService.claimKnockOff(
        init_oid,
        claimId
      ).subscribe(res => {
        if(res && res!=null){
        console.log(res['data']);
        this.changeToClosedStateByKnockOff(res['data'])
        resolve(res['data'])
        }
        else{
          resolve(res)
        }
      }, err => {
        reject(err)
      })

    })
  }

  getPaymentDaysConfig() {
    return new Promise((resolve, reject) => {

      this._detailService.getExpensePaymentTerms().subscribe(res => {
        console.log(res['data']);
        return resolve(res['data'])
      }, err => {
        return reject(err)
      })

    })
  }

  changeToClosedStateByKnockOff = (data) => {
    if (data && data.length > 0) {
      let claimObj = _.where(data, { claim_id: this.expenseDetail.expense_header_id })

      this.knockOffHistoryDetail = claimObj[0].knocked_off_history;
      if (claimObj[0].header_status == 'C') {
        this.moveToClosedStateLocally(claimObj[0].amount_claimed);
      }
    }
  }

  rejectAll = async () => {
    let expenseItemSelectedArr = _.where(
      this.approvalItemForm.value.approvalItems,
      {
        check: true,
        t_is_curr_appr: 1,
      }
    );
    let finalArr = expenseItemSelectedArr.map((item, index) => {
      let obj = {
        approverOid: this.profile.oid,
        tWorkflowHeaderId: item.treasury_wf_header_id,
        status: "R",
      };
      return obj;
    });
    console.log(finalArr);
    this.isAllItemBeingRejected = true;
    let res = await this.updateApprovalStatus(finalArr, null);
    this.isAllItemBeingRejected = false;

    if (res === "Submitted") {
      this.updateStatusOnForm();
    }
  };

  getExpenseConfigfunction = () => {
    return new Promise((resolve, reject) => {
      this._detailService.getExpenseConfigfunction().subscribe(
        res => {

          if (res['data']) {
            if(res['data']!=null){
              let expenseConfigData = JSON.parse(res['data'][0]['expense_application_config']);
              if(expenseConfigData!=null){
                if(expenseConfigData.is_currency_conversion_applicable == 1){
                  this.isViewConversionRate = true;
                  console.log("this.isViewConversionRate")
                  console.log(this.isViewConversionRate)
                }
              }
            }
            resolve(res["data"])
          }

        }, err => {
          console.log(err)
          reject(err)
        }
      )
    })
  }

  approveItem = async (index, item_index) => {
    console.log("taxCreditWarning")
    console.log(this.taxCreditWarning)
      
    this.approvalItemForm.controls["approvalItems"]["controls"][index][
      "controls"
    ]["isItemBeingApproved"].patchValue(true); //!starting spinner

    if(this.taxCreditWarning == true)
      return this._util.showMessage("Tax amount should not be greater than verified amount !", "Dismiss", 3000)
    
    if (this.ExpenseEntryFormData.controls[index]['controls'].taxCredit.invalid 
      // &&
      // (this.ExpenseEntryFormData.controls[index]['controls'].taxCredit.touched ||
      //   this.ExpenseEntryFormData.controls[index]['controls'].taxCredit.dirty)
      ) {
      if (!this.ExpenseEntryFormData?.controls[index]['controls'].taxCredit?.errors?.validAmount) {
          
    this.approvalItemForm.controls["approvalItems"]["controls"][index][
      "controls"
    ]["isItemBeingApproved"].patchValue(false); //!starting spinner
        return this._util.showMessage("Tax credit amount is Invalid!", "Dismiss", 3000);
      }
    }

    if (this.claimPaymentForm.get("ledgerAccount").invalid){
      this.approvalItemForm.controls["approvalItems"]["controls"][index][
        "controls"
      ]["isItemBeingApproved"].patchValue(false); //!starting spinner
      return this._util.showMessage("Choose Ledger Account !", "Dismiss", 3000)
    }

     if (this.claimPaymentForm.get("ledgerAccount").invalid){
      this.approvalItemForm.controls["approvalItems"]["controls"][index][
        "controls"
      ]["isItemBeingApproved"].patchValue(false); //!starting spinner
      return this._util.showMessage("Choose Ledger Account !", "Dismiss", 3000)
    }
     
     if ( this.initialItemAmount[index] < this.categoryTotals[index]){
      this.approvalItemForm.controls["approvalItems"]["controls"][index][
        "controls"
      ]["isItemBeingApproved"].patchValue(false); //!starting spinner
      return this._util.showMessage("Verfied amount is greater than Claimed amount !", "Dismiss", 3000)
    }

    if (this.claimPaymentForm.get("ledgerAccountDescription").invalid){
      if(this.claimPaymentForm.get("ledgerAccountDescription").hasError('required')){
        this.approvalItemForm.controls["approvalItems"]["controls"][index][
          "controls"
        ]["isItemBeingApproved"].patchValue(false); //!starting spinner
        return this._util.showMessage("Please fill the Description !", "Dismiss", 3000)
      }
      else if(this.claimPaymentForm.get("ledgerAccountDescription").hasError('maxlength')){
        this.approvalItemForm.controls["approvalItems"]["controls"][index][
          "controls"
        ]["isItemBeingApproved"].patchValue(false); //!starting spinner
        return this._util.showMessage("Description cannot be more than 255 characters !", "Dismiss", 3000)
      }
    }

    if (this.claimPaymentForm.get("targetEntity").invalid){
      this.approvalItemForm.controls["approvalItems"]["controls"][index][
        "controls"
      ]["isItemBeingApproved"].patchValue(false); //!starting spinner
      return this._util.showMessage("Choose Target Entity !", "Dismiss", 3000)
    }

    if (this.claimPaymentForm.get("creditLedger").invalid){
      this.approvalItemForm.controls["approvalItems"]["controls"][index][
        "controls"
      ]["isItemBeingApproved"].patchValue(false); //!starting spinner
      return this._util.showMessage("Fill the Credit Ledger !", "Dismiss", 3000)
    }

    if (this.claimPaymentForm.get("customerBilling").invalid){
      this.approvalItemForm.controls["approvalItems"]["controls"][index][
        "controls"
      ]["isItemBeingApproved"].patchValue(false); //!starting spinner
      return this._util.showMessage("Fill the Customer Billing !", "Dismiss", 3000)
    }

    if (this.claimPaymentForm.get("costCenterCode").invalid){
      this.approvalItemForm.controls["approvalItems"]["controls"][index][
        "controls"
      ]["isItemBeingApproved"].patchValue(false); //!starting spinner
      return this._util.showMessage("Choose the Cost Center !", "Dismiss", 3000)
    }

    if ( this.initialItemAmount[index] > this.categoryTotals[index])    {
      let result = await this._util.openConfirmationSweetAlert("Verified amount is less than claim amount !", "Are you sure you want to proceed?");
      if (result == true){
          this.approvalCode(item_index);
          }
    }
    else{
      this.approvalCode(item_index);
    }

   
  };


  updateRemainingCharacters(value: string, j: number, m: number): void {
    if (!this.remainingCharacters[j]) {
        this.remainingCharacters[j] = [];
    }
    this.remainingCharacters[j][m] = value.length;
}

getRemainingCharacters(j: number, m: number): number {
  if (this.remainingCharacters[j]?.[m] !== undefined) {
      return this.remainingCharacters[j][m];
  }
  return 0; // Default value if not initialized or value is undefined
}




  formatAmount(j: number, m: number): void {
    const ledgerAmountDistributionArray = (this.claimPaymentForm.get('expenseEntry') as FormArray).at(j).get('ledgerAmountDistribution') as FormArray;
    const ledgerItemAmountControl = ledgerAmountDistributionArray.at(m).get('ledger_item_amount') as FormControl;
    
    if (ledgerItemAmountControl.value !== null && ledgerItemAmountControl.value !== undefined) {
      const roundedValue = parseFloat(ledgerItemAmountControl.value).toFixed(2);
      ledgerItemAmountControl.setValue(roundedValue, { emitEvent: false }); 
    }
    //  if (this.initialItemAmount[j] < this.categoryTotals[j]) {
    //   if (!this.excessAmount[j]) {
    //     this.excessAmount[j] = [];
    //   }
    //   this.excessAmount[j][m] = true;
    // } else {
    //   if (this.excessAmount[j]) {
    //     this.excessAmount[j][m] = false;
    //   }
    // }
    const totalAmount = ledgerAmountDistributionArray.controls.reduce((total, control) => {
      return total + parseFloat(control.get('ledger_item_amount').value || 0);
    }, 0);
  
    if (totalAmount > this.initialItemAmount[j]) {
      this.excessAmount[j] = ledgerAmountDistributionArray.controls.map((control, m) => totalAmount > this.initialItemAmount[j]);
    } else {
      this.excessAmount[j] = ledgerAmountDistributionArray.controls.map((control, m) => false);
    }
  }


  isExcessAmount(j: number, m: number): boolean {
    return this.excessAmount[j] && this.excessAmount[j][m];
  }


  async approvalCode(index){
    let getExpenseItem = _.findWhere(this.claimPaymentForm.value.expenseEntry, { item_index: index });

    // let expenseItemLedger = getExpenseItem ? getExpenseItem.expense_account : null;
    
    // console.log(expenseItemLedger);
    let expenseEntries = this.claimPaymentForm.value.expenseEntry;
  let finalArr = [];

  let entry = expenseEntries[index];
  let invoiceDate = this.approvalItemList[index].invoice_date; 
  
  let expenseItemLedger = getExpenseItem.ledgerAmountDistribution.map(dist => {
      return {
        ledger_item_amount: dist.ledger_item_amount,
        expense_account: dist.expense_account,
        currency_code: dist.currency_code,
        bill_date: invoiceDate,
        narration: dist.narration
      };
    });

    let obj = {
      approverOid: this.profile.oid,
      tWorkflowHeaderId: this.approvalItemForm.value.approvalItems[index]
        .treasury_wf_header_id,
      status: "A",
      expenseItemLedger: expenseItemLedger,
      sourceEntityCurrency: this.expenseDetail.entity_currency_code,
      expenseItemId: getExpenseItem.expense_item_id
    };
    finalArr.push(obj);
  
    // this.approvalItemForm.controls["approvalItems"]["controls"][index][
    //   "controls"
    // ]["isItemBeingApproved"].patchValue(true); //!starting spinner

    let res = await this.updateApprovalStatus(finalArr, index);

    this.approvalItemForm.controls["approvalItems"]["controls"][index][
      "controls"
    ]["isItemBeingApproved"].patchValue(false); // stopping spinner

    console.log(res);

    if (res === "Submitted") {
      this.approvalItemList[index].treasury_status = "A";

      this.approvalItemForm.controls["approvalItems"]["controls"][index][
        "controls"
      ]["status"].patchValue("Approved");
      this.approvalItemForm.controls["approvalItems"]["controls"][index][
        "controls"
      ]["treasuryStatus"].patchValue("Approved");
      this.approvalItemForm.controls["approvalItems"]["controls"][index][
        "controls"
      ]["t_is_curr_appr"].patchValue(0);
    }
    else if (res === "Verified") {
      this.approvalItemList[index].treasury_status = "A";

      this.approvalItemForm.controls["approvalItems"]["controls"][index][
        "controls"
      ]["status"].patchValue("Approved");
      this.approvalItemForm.controls["approvalItems"]["controls"][index][
        "controls"
      ]["treasuryStatus"].patchValue("Approved");
      this.approvalItemForm.controls["approvalItems"]["controls"][index][
        "controls"
      ]["t_is_curr_appr"].patchValue(0);
      this.updateStatusInSession();

      let tallyUpdate = await this.updateExpenseToTally();
      console.log("tallyUpdate")
      console.log(tallyUpdate)

      let knockOff = await this.knockOffClaim();
      console.log("knockOff")
      console.log(knockOff);

      this.claimPaymentForm.patchValue({
        paymentMode: "cheque"
      })

      this.toDetermineTargetEntityIsReadOnly();

    }
  }

  rejectItem = async (index) => {

    if (this.claimPaymentForm.get("targetEntity").invalid)
      return this._util.showMessage("Please choose Target Entity !", "Dismiss", 3000)

    let getExpenseItem = _.findWhere(this.claimPaymentForm.value.expenseEntry, { item_index: index });

      
    let expenseItemLedger = getExpenseItem.ledgerAmountDistribution.map(dist => {
      return {
        ledger_item_amount: dist.ledger_item_amount,
        expense_account: dist.expense_account,
        currency_code: dist.currency_code
      };
    });

    let obj = {
      approverOid: this.profile.oid,
      tWorkflowHeaderId: this.approvalItemForm.value.approvalItems[index]
        .treasury_wf_header_id,
      status: "R",
      expenseItemId: getExpenseItem.expense_item_id,
      expenseItemLedger: expenseItemLedger,
      sourceEntityCurrency: this.expenseDetail.entity_currency_code,
    };

    let finalArr = [obj];
    this.approvalItemForm.controls["approvalItems"]["controls"][index][
      "controls"
    ]["isItemBeingRejected"].patchValue(true); //starting spinner

    let res = await this.updateApprovalStatus(finalArr, index);
    
    this.approvalItemForm.controls["approvalItems"]["controls"][index][
      "controls"
    ]["isItemBeingRejected"].patchValue(false); // stopping spinner

    console.log(res);

    if (res === "Submitted" || res === "Rejected") {
    
      if (res === "Rejected")
        this.updateTreasuryRejectStatusInSession();

      this.approvalItemForm.controls["approvalItems"]["controls"][index][
        "controls"
      ]["status"].patchValue("Rejected");
      this.approvalItemForm.controls["approvalItems"]["controls"][index][
        "controls"
      ]["treasuryStatus"].patchValue("Rejected");
      this.approvalItemForm.controls["approvalItems"]["controls"][index][
        "controls"
      ]["t_is_curr_appr"].patchValue(0);
    }

    else if (res === "Verified") {
      // this.knockOffClaim();
      this.updateStatusInSession();
      this.approvalItemForm.controls["approvalItems"]["controls"][index][
        "controls"
      ]["status"].patchValue("Rejected");
      this.approvalItemForm.controls["approvalItems"]["controls"][index][
        "controls"
      ]["treasuryStatus"].patchValue("Rejected");
      this.approvalItemForm.controls["approvalItems"]["controls"][index][
        "controls"
      ]["t_is_curr_appr"].patchValue(0);

      let tallyUpdate = await this.updateExpenseToTally();

      console.log("tallyUpdate")
      console.log(tallyUpdate)

      let knockOff = await this.knockOffClaim();
      console.log("knockOff")
      console.log(knockOff)

      this.claimPaymentForm.patchValue({
        paymentMode: "cheque"
      })

      this.toDetermineTargetEntityIsReadOnly();

    }
  };


  updateTreasuryRejectStatusInSession = async () => {

    this.expenseDetail.status = "Rejected";
    this.expenseDetail.t_action_by = this.profile.oid;
    this.expenseDetail.t_action_on = new Date();
    sessionStorage.removeItem("expenseDetail")
    sessionStorage.setItem("expenseDetail", JSON.stringify(this.expenseDetail));
    let msg = {
      msgCode: "rfC",
      data: {
        status: "Rejected",
        amountRemaining: null,
      }, //refresh Component
    };
    this.redirectToHomePage();
    this._util.showSweetalert("Rejected successfully!", "success");
  }

  updateApprovalStatus = async (itemArr, index) => {
    await this.savepayments() 
    return new Promise((resolve, reject) => {
      let headerId = this.expenseDetail["expense_header_id"];
      let targetLegalEntity = this.parseJson(this.claimPaymentForm.get("targetEntity").value);

      let targetlegalEntityId = targetLegalEntity && targetLegalEntity.entity_id ? targetLegalEntity.entity_id : 0;

      targetlegalEntityId = targetlegalEntityId 
                      ? targetlegalEntityId 
                      : (this.expenseDetail.legal_entity_id ? this.expenseDetail.legal_entity_id : 0);
      // let targetlegalEntityId = this.expenseDetail.legal_entity_id;
      if (this.approvalUpdateSubscription)
        this.approvalUpdateSubscription.unsubscribe();
      this.approvalUpdateSubscription = this._detailService
        .updateTreasuryApprovalStatus(headerId, itemArr, this.expenseDetail, targetlegalEntityId)
        .subscribe(
          (res: any) => {

            if (res)

            resolve(res.headerStatusName);
          },
          (err) => {
            this.approvalItemForm.controls["approvalItems"]["controls"][index][
              "controls"
            ]["isItemBeingApproved"].patchValue(false);
            if(err?.error?.errMessage?.errMsg){
              this.snackBar.open(err?.error?.errMessage?.errMsg, "Dismiss", {
                duration: 5000
              });
            }
            else{
              this.snackBar.open(err?.error["messText"], "Dismiss", {
                duration: 5000
              });
            }
            console.error(err);
            reject(err);
          }
        );
    });
  };

  updateExpenseToTally = () => {
    let details = {
      expense_header_id: this.expenseDetail["expense_header_id"],
      legal_entity_id: this.expenseDetail.legal_entity_id,
      // target_legal_entity_id: this.claimPaymentForm.get("targetEntity").value,
      target_legal_entity_id: this.expenseDetail.legal_entity_id,
      ledger_account: this.claimPaymentForm.get("ledgerAccount").value,
      ledger_description: this.claimPaymentForm.get("ledgerAccountDescription").value,
      credit_ledger: this.claimPaymentForm.get("creditLedger").value
    }
    return new Promise((resolve, reject) => {
      this._detailService.updateExpenseToTally(details).subscribe(res => {

        resolve(res);
      }, err => {

        reject(err);
      })
    })

  }

  updateStatusOnForm = () => {
    let expenseItemSelectedArr = _.where(
      this.approvalItemForm.value.approvalItems,
      {
        check: true,
        t_is_curr_appr: 1,
      }
    );
    expenseItemSelectedArr.forEach((element) => {
      let i = this.approvalItemForm.value.approvalItems
        .map((item, index) => {
          return item.expense_item_id;
        })
        .indexOf(element.expense_item_id);
      this.approvalItemForm.controls["approvalItems"]["controls"][i][
        "controls"
      ]["status"].patchValue("Approved");
      this.approvalItemForm.controls["approvalItems"]["controls"][i][
        "controls"
      ]["treasuryStatus"].patchValue("Approved");
      this.approvalItemForm.controls["approvalItems"]["controls"][i][
        "controls"
      ]["t_is_curr_appr"].patchValue(0);
    });
  };

  getStatusText = (indicator, status) => {
    if (indicator == 1) {
      if (
        status == "Submitted" ||
        status == "Approved" ||
        status == "Verified" ||
        status == "Closed"
      )
        return "Submitted";
      else if (status == "Rejected")
        return "Submitted";
      else
        return "Awaiting for submission";
    } else if (indicator == 2) {
      if (status == "Approved" || status == "Verified" || status == "Closed")
        return "Approved";
      else if (status == "Rejected")
        return "Approvers action ";
      else return "Awaiting for Approval";
    } else if (indicator == 3) {
      if (status == "Verified" || status == "Closed")
        return "Verified";
      else if (status == "Rejected")
        return "Treasurer action";
      else return "Awaiting for verification";
    } else if (indicator == 4) {
      if (status == "Closed"){
        return this.expenseDetail && this.expenseDetail[status] && this.expenseDetail[status]?.display_name ? this.expenseDetail[status]?.display_name : status;
      }
      else return "Waiting for Payment Processing";
    }
  };

  expenseStatusConfig : any;
  expenseConfig : any;
  async getExpenseStatusConfig(){
    await this._detailService
    .getExpenseStatusConfig()
    .subscribe(
      (res) => {
        this.expenseStatusConfig = res['data'];
        this.expenseConfig = {};
        if(this.expenseStatusConfig){
          this.expenseStatusConfig.forEach((element) => {
          this.expenseConfig[element.status_name] = element
        });
      }
      },
      (err) => {
        console.error(err);
      }
    );
  }

  checkCommentVisibility = (indicator, status) => {
    if (indicator == 3) {
      if (status == "Approved" || status == "Verified" || status == "Closed")
        return true;
      else
        return false;
    }
    else if (indicator == 4) {
      if (status == "Closed")
        return true;
      else
        return false;
    }
    else if (indicator == 5) {
      if (status == "Closed")
        return true;
      else
        return false;
    }
  };

  openCommentBox = async () => {
    let msg = {
      inputData: {
        application_id: 65,
        application_name: "Expenses",
        title: "Expense - CL/" + this.expenseDetail["expense_header_id"],
        unique_id_1: this.expenseDetail["expense_header_id"],
        unique_id_2: "",
      },

      context: {
        "Role": "Treasurer",
        "Sent By" : this.profile.name,
        "Expense ID": "Expense - #EX" + `${this.expenseDetail["expense_header_id"]}`,
        "Description": this.expenseDetail["description"],
          "Expense type": this.expenseDetail.expense_type == "C" ? "Claim" : "Advance",
          "Expense Status": this.expenseConfig[this.expenseDetail?.status]?.display_name,
      },
      
      commentBoxHeight: '100vh',
      commentBoxScrollHeight: '80%',
      primaryColor: this.primaryColor
    }
    const { ChatCommentContextModalComponent } = await import('../../../../lazy-loaded-components/chat-comment-context-modal/chat-comment-context-modal.component')
    const openChatCommentContextModalComponent = this.dialog.open(ChatCommentContextModalComponent, {
      height: '100%',
      width: '50%',
      position: { right: '0px' },
      data: { modalParams: msg }
    });

    this._creationService.sendMsg(msg);
  };

  updateStatusInSession = async () => {
    // this.setExpenseEntryFormArrayData();
    this.expenseDetail.status = "Verified";
    this.expenseDetail.t_action_by = this.profile.oid;
    this.expenseDetail.t_action_on = new Date();
    sessionStorage.removeItem("expenseDetail")
    sessionStorage.setItem("expenseDetail", JSON.stringify(this.expenseDetail));
    let msg = {
      msgCode: "rfC",
      data: {
        status: "Verified",
        amountRemaining: null,
      }, //refresh Component
    };
    this._detailService.sendMsg(msg);
    this._detailService.getUpdatedTreasuryValue(this.expenseDetail.status, this.expenseDetail.t_action_by, this.expenseDetail.t_action_on);
  };



  parseJsonAmount = (data) => {
    if (!data) return [];
  
    let parsed = JSON.parse(data);
  
    let expenseCurrencyItem = parsed.find(x => x.is_expense_currency == true);
  
    if (expenseCurrencyItem) {
      parsed = [expenseCurrencyItem, ...parsed.filter(x => x !== expenseCurrencyItem)];
    }  
  
    return parsed;

  };


  parseJson = (data) => {
    if (data) return JSON.parse(data);
  };

  parseJSON(value) {
    try {
  
      return JSON.parse(value);
  
    } catch (err) {
      return value;
    }
  }

  changePaymentMode = async (mode) => {
    this.claimPaymentForm.get("paymentMode").patchValue(mode);
    let getDefaultBank = await this.setDefaultSourceEntityBank(0);
  };

  getExpenseDefaultBank = async (index) => {
    this._detailService.getExpenseDefaultBank().subscribe(
      (res) => {
        this.patchBankDetail(index, res)
      },
      (err) => {
        console.error(err);
      }
    );
  }

  changeChequeStatus = async (status, index) => {
    if (status == "Moved to bank") {

      if (await this.findAmountToBeAddedToCheque() >= 0) {
        let result = await this._util.openConfirmationSweetAlert("Are you sure?", "You wont be able to revert it!");
        if (result == true) {
          let headerId = this.expenseDetail["expense_header_id"];
          let chequeTotal = await this.getCheckTotal(this.claimPaymentForm.value.cheques[index].chequeAmount);
          let chequeAmount = this.claimPaymentForm.value.cheques[index].chequeAmount;
          let bankLedgerName = this.claimPaymentForm.value.cheques[index].bankLedgerName;
          console.log(headerId);
          console.log(chequeTotal);

          //api
          if (this.claimPaymentForm.valid) {
            let paymentCurrencyCode = this.expenseDetail.entity_currency_code;

            this.claimPaymentForm.controls["cheques"]["controls"][index][
              "controls"
            ]["chequeStatus"].patchValue("Moved to bank");
            let targetLegalEntity = JSON.parse(this.claimPaymentForm.get("targetEntity").value);
            let targetlegalEntityId = targetLegalEntity["entity_id"];
            this._detailService
              .moveToBank(headerId, this.claimPaymentForm.value, chequeTotal, chequeAmount, paymentCurrencyCode, bankLedgerName,
                targetlegalEntityId, this.expenseDetail)
              .subscribe(
                (res) => {
                  console.log(res);
                  this._util.showSweetalert("Moved to Bank ! ", "success");
                  this.moveToClosedStateLocally(res['amount_claimed']);
                  this._util.showMessage(res['messText'], "Dismiss", 1000);
                },
                (err) => {
                  this._util.showSweetalert("Failed", "info");
                  this.claimPaymentForm.controls["cheques"]["controls"][index][
                    "controls"
                  ]["chequeStatus"].patchValue("Awaiting Sign");
                }
              );
          } else
            this._util.showMessage(
              "Please Check all the required fields",
              "dismiss"
            );
        }
      }
      else {
        this._util.showMessage("You are exceeding the approved amount", "dismiss")
      }
    } else {
      if (await this.findAmountToBeAddedToCheque() >= 0) {
        console.log(this.claimPaymentForm)
        console.log(this.claimPaymentForm.controls['cheques']['controls'][index]['controls']['chequeAmount'].errors)
        if (this.claimPaymentForm.valid) {
          this.claimPaymentForm.controls["cheques"]["controls"][index][
            "controls"
          ]["chequeStatus"].patchValue("Awaiting Sign");
          this.savepayments();
        } else {
          this._util.showMessage(
            "Please check all the required fields!",
            "dismiss"
          );
        }
      }
      else {
        this._util.showMessage("You are exceeding the approved amount", "dismiss")
      }
    }
  };

  getCheckTotal = (cheqAmt) => {
    return new Promise((resolve, reject) => {
      // let chequeAmountArr = _.pluck(
      //   this.claimPaymentForm.value.cheques,
      //   "chequeAmount"
      // );
      // let chequeTotal = _.reduce(
      //   chequeAmountArr,
      //   (memory, current) => {
      //     return memory + (current != "" ? current : 0);
      //   },
      //   0
      // );
      // resolve(chequeTotal);
      let amtClaimed = this.expenseDetail.amount_claimed ? typeof (this.expenseDetail.amount_claimed) == "string" ? JSON.parse(this.expenseDetail.amount_claimed) : this.expenseDetail.amount_claimed : null;
      let amtClaimedVal = amtClaimed ? _.findWhere(amtClaimed, { currency_code: this.expenseDetail.entity_currency_code }).value : 0;
      resolve(amtClaimedVal + cheqAmt);
    });
  };

  getDefaultSourceEntityBank() {
    return new Promise((resolve, reject) => {

      this._detailService.getSourceEntityDefaultBank(this.expenseDetail.legal_entity_id).subscribe(res => {

        resolve(res)
      }, err => {
        reject(err)
      })

    })
  }
  async setDefaultSourceEntityBank(index) {
    let bankDetails = await this.getDefaultSourceEntityBank();

    if (bankDetails['messType'] == "S") {
      this.patchBankDetail(index, bankDetails['data'])
    }

  }


  chooseBank = (index) => {
    const bottomSheetRef = this._bottomSheet.open(BanksComponent, {
      panelClass: "custom-bank-bottom-sheet",
    });
    bottomSheetRef.afterDismissed().subscribe((res) => {
      if (res) this.patchBankDetail(index, res);
    });
  };

  patchBankDetail = (index, bank) => {
    console.log("patchbankDetail", index, bank)
    this.claimPaymentForm.controls["cheques"]["controls"][index]["controls"][
      "bank"
    ].patchValue(bank.bank_name);
    this.claimPaymentForm.controls["cheques"]["controls"][index]["controls"][
      "accNo"
    ].patchValue(bank.bank_acc_no);
    this.claimPaymentForm.controls["cheques"]["controls"][index]["controls"][
      "branch"
    ].patchValue(bank.bank_branch);
    this.claimPaymentForm.controls["cheques"]["controls"][index]["controls"][
      "bankLedgerName"
    ].patchValue(bank.bank_ledger_name);
    this.claimPaymentForm.controls["cheques"]["controls"][index]["controls"][
      "isBankSelected"
    ].patchValue(true);
  };


  addCheque = async (index) => {
    const control = <FormArray>this.claimPaymentForm.controls.cheques;
    let amtToBeAdded = await this.findAmountToBeAddedToCheque();
    if (amtToBeAdded == 0)
      this._util.showMessage("Total Approved Amount Reached!", "dismiss", 1000);
    else {
      let chequeAmountToBeFilled = await this.findAmountToBeAddedToCheque()
      control.push(
        this.fb.group({
          bank: ["", Validators.required],
          accNo: ["", Validators.required],
          branch: ["", Validators.required],
          bankLedgerName: ["", Validators.required],
          chequeNo: ["", [Validators.required, Validators.min(1)]],
          chequeAmount: [
            chequeAmountToBeFilled>0 ? chequeAmountToBeFilled : 0
            ,
            [Validators.required, validateAmount, Validators.min(1)]
          ],
          chequeStatus: [""],
          isBankSelected: false,
        })
      );
      index = index + 1;
      // await this.getExpenseDefaultBank(index);
      await this.setDefaultSourceEntityBank(index);
    }
  };

  removeCheque = (index) => {
    const control = <FormArray>this.claimPaymentForm.controls.cheques;
    if (control.length > 1)
      if (
        this.claimPaymentForm.value.cheques[index].chequeStatus ==
        "Moved to bank"
      )
        this._util.showMessage("Moved Cheques cant be removed!", "dismiss");
      else control.removeAt(index);
  };

  findAmountToBeAddedToCheque = async () => {

    if (this.claimPaymentForm.get("paymentMode").invalid)
      this.claimPaymentForm.patchValue({ paymentMode: "cheque" })

    return new Promise(async (resolve, reject) => {
      let totalApprovedAmount = (await this.findTotalAmount()) + "";
      let chequeAmountArr = _.pluck(this.claimPaymentForm.value.cheques, "chequeAmount");
      let chequeArrSum = _.reduce(chequeAmountArr, (memory, current) => {
        return memory + (current != "" ? current : 0);
      },
        0
      );

      // let claimKnockOffTotal = await this.calculateKnockOffTotal();

      let amountClaimed: any = await this.getCurrentAmountClaimed();

      amountClaimed = amountClaimed > 0 ? parseFloat(amountClaimed) : 0;

      console.log(`amount Claimed - ${amountClaimed} , totalApprovedAmount - ${totalApprovedAmount}, chequeArrSum -${chequeArrSum}`);

      // let amtToBeAdded = parseFloat(totalApprovedAmount) - (claimKnockOffTotal + chequeArrSum)
      let amtToBeDeducted = amountClaimed > chequeArrSum ? amountClaimed + chequeArrSum : chequeArrSum;
      let amtToBeAdded: any = parseFloat(totalApprovedAmount) - amtToBeDeducted;
      console.log(amtToBeAdded)
      console.log(amountClaimed)
      console.log(amtToBeDeducted)
      // amtToBeAdded = amtToBeAdded > 0 ? amtToBeAdded : 0;


      resolve(amtToBeAdded);

    });
  };


  calculateKnockOffTotal() {

    return new Promise((resolve, reject) => {
      if (this.knockOffHistoryDetail.length > 0) {
        // let parsedKnockOffHistory = typeof (this.expenseDetail.knocked_off_history) == 'string' ?
        //   JSON.parse(this.expenseDetail.knocked_off_history) : this.expenseDetail.knocked_off_history;
        let knockOffValArr = _.pluck(this.knockOffHistoryDetail, "knocked_off_value");
        let knockOffValSum = _.reduce(knockOffValArr, (memory, current) => {
          return memory + (current != "" ? current : 0);
        },
          0
        );
        resolve(knockOffValSum);
      }
      else
        resolve(0);

    })
  }

  getCurrentAmountClaimed() {
    return new Promise((resolve, reject) => {

      this._detailService.getCurrentAmountClaimed(this.expenseDetail.expense_header_id, this.expenseDetail.entity_currency_code).subscribe(res => {

        resolve(res['data']);

      }, err => {
        console.log(err);
      })
    })
  }

  findTotalAmount = async () => {
    return new Promise((resolve, reject) => {

      let filterTreasuryApprovedAmount = _.filter(this.approvalItemList, l => {
        if (l.treasury_status == "A")
          return l;
      })

      let amountsarr = _.pluck(filterTreasuryApprovedAmount, "item_amount");

      let sum = _.reduce(amountsarr, (memory, current) => {
        let intArr = _.where(JSON.parse(current), { currency_code: this.expenseDetail.entity_currency_code });
        return memory + intArr[0].value;
      },
        0
      );
      resolve(sum.toFixed(2));
      // let amtClaimed = typeof (this.expenseDetail.amount_claimed) == 'string' ? JSON.parse(this.expenseDetail.amount_claimed) : this.expenseDetail.amount_claimed;
      // resolve(_.findWhere(amtClaimed, { currency_code: "INR" }).value);
    });
  };

  savepayments = () => {
    return new Promise((resolve, reject) => {
    let headerId = this.expenseDetail["expense_header_id"];

    if (this.claimPaymentForm.value.expenseEntry.length == 0) {
      this._util.showMessage("Please check all the required fields!", "dismiss");
    }
    else {
      console.log("expenseDetail")
      console.log(this.expenseDetail)
      this.claimPaymentForm.controls.costCenterCodeBeforeUpdate.patchValue(this.expenseDetail.cost_centre); 
      this.claimPaymentForm.controls.costCenterDepartmentBeforeUpdate.patchValue(this.expenseDetail.department); 
      this.claimPaymentForm.controls.customerBillingBeforeUpdate.patchValue(this.expenseDetail.is_billable);
      this._detailService
        .savePaymentDetails(headerId, this.claimPaymentForm.value, this.expenseDetail.claimer_aid)
        .subscribe(
          (res) => {
            this._util.showMessage("Details saved!", "Dismiss", 1000);  
            this.updateTreasurerChangesInSession();
            console.log("expenseDetail")
            console.log(this.expenseDetail)
            resolve(res); 
          },
          (err) => {
            console.error(err);
            this._util.showMessage("Oops something went wrong!", "Dismiss", 1000);
            reject(err);
          }
        );

    }
  });
  };

  updateTreasurerChangesInSession = async () => {
    console.log("updateTreasurerChangesInSession")
    this.expenseDetail.is_billable =  this.claimPaymentForm.value.customerBilling;
    this.expenseDetail.cost_centre = this.claimPaymentForm.value.costCenterCode;
    this.expenseDetail.cost_centre_description = this.claimPaymentForm.value.costCenter.cost_centre_description;
    this.expenseDetail.department =this.claimPaymentForm.value.costCenterDepartment;
    this.expenseDetail.status = this.expenseDetail.status;
    sessionStorage.removeItem("expenseDetail")
    sessionStorage.setItem("expenseDetail", JSON.stringify(this.expenseDetail));
    let msg = {
      msgCode: "rfC",
      data: {
        is_billable: this.claimPaymentForm.value.customerBilling,
        cost_centre: this.claimPaymentForm.value.costCenterCode,
        cost_centre_description: this.claimPaymentForm.value.costCenter.cost_centre_description,
        department : this.claimPaymentForm.value.costCenterDepartment,
        status : this.expenseDetail.status
      }, //refresh Component
    };
    this._detailService.sendMsg(msg);
    this._detailService.getUpdatedTreasurerChanges(this.expenseDetail.is_billable, this.expenseDetail.cost_centre, 
      this.expenseDetail.cost_centre_description,this.expenseDetail.department, this.expenseDetail.status );
  };

  getPaymentDetails = () => {
    let headerId = this.expenseDetail["expense_header_id"];
    return new Promise((resolve, reject) => {
      this._detailService.retrievePaymentDetail(headerId).subscribe(
        (res) => {
          resolve(res);
        },
        (err) => {
          reject(err);
        }
      );
    });
  };

  moveToClosedStateLocally = (amount) => {
    this.expenseDetail.amount_claimed = amount;
    this.expenseDetail.status = "Closed";
    this.expenseDetail.closed_by = this.profile.oid;
    this.expenseDetail.closed_on = new Date();
    sessionStorage.removeItem("expenseDetail")
    sessionStorage.setItem("expenseDetail", JSON.stringify(this.expenseDetail));
    let msg = {
      msgCode: "rfC",
      data: {
        status: "Closed",
        amountRemaining: amount,
      }, //refresh Component
    };
    this._detailService.sendMsg(msg);
    this._detailService.getUpdatedTreasuryValue(this.expenseDetail.status, this.expenseDetail.closed_by, this.expenseDetail.closed_on);
  };

  ngOnDestroy() {
    if (this.formArrayChangeSubscription)
      this.formArrayChangeSubscription.unsubscribe();
    if (this.checkAllSubscription) this.checkAllSubscription.unsubscribe();
    if (this.itemDetailSubscription) this.itemDetailSubscription.unsubscribe();
  }

  openFile = (index, attachment) => {

    let keyName = attachment.key;

    let fileName = attachment.fileName;

    let fileType = attachment.type;

    this.approvalItemForm.value.approvalItems[index]
      .isAttachmentLoading = true;

    this._detailService.getFileDataFromS3(keyName)
      .subscribe(async res => {

        this.approvalItemForm.value.approvalItems[index]
          .isAttachmentLoading = false;
        if (res["messType"] == 'S') {

          if (fileType == 'image/png' || fileType == 'image/jpg' || fileType == 'image/jpeg') {

            let height = fileType == 'application/pdf' ? "90vh" : "auto";
            let width = fileType == 'application/pdf' ? "90vw" : "auto";
            let isFilePDF = fileType == 'application/pdf' ? true : false;

            const { AttachmentViewerComponent } = await import('../../../../lazy-loaded-components/attachment-viewer/attachment-viewer.component')

            this.dialog.open(AttachmentViewerComponent, {
              width: width,
              height: height,
              maxWidth: "200vh",
              maxHeight: "100vh",
              disableClose: false,
              data: {
                fileData: 'data:' + fileType + ';base64,' + res["data"]["fileData"],
                fileName: fileName,
                isFilePDF: isFilePDF,
                fileType: fileType,
                rawData: res["data"]["fileData"]
              }
            });

          }
          else if (fileType == 'application/pdf') {
            let pdfWindow = window.open("")
            pdfWindow.document.write(
              "<iframe width='100%' height='100%' src='data:application/pdf;base64, " +
              res["data"]["fileData"] + "'></iframe>"
            )
            this._fileSaver.saveAsFile(res["data"]["fileData"], fileName, fileType);
          }

          else
            this._fileSaver.saveAsFile(res["data"]["fileData"], fileName, fileType);

        }

        else
          this._util.showMessage(res["messText"], "Dismiss", 3000);

      },
        err => {
          this.approvalItemList[index].isAttachmentLoading = false;
          this._util.showMessage("Error in Downloading !", "Dismiss", 3000);
        });

  };

  downloadAllAttachments = async () => {

    for (let item of this.approvalItemList) {
   
      item.attachment = typeof item.attachment == 'string' ? JSON.parse(item.attachment) : item.attachment;

      this.isDownloading = true;

      if (item.attachment != null) {
        let keyName = item.attachment[0].key;

        let fileName = item.attachment[0].fileName;

        let fileType = item.attachment[0].type;
        this._detailService.getFileDataFromS3(keyName)
          .subscribe(async res => {

            this.isDownloading = false;

            if (res["messType"] == 'S')
              this._fileSaver.saveAsFile(res["data"]["fileData"], fileName, fileType);

            else
              this._util.showMessage(res["messText"], "Dismiss", 3000);

          },
            err => {
              this.isDownloading = false;
              this._util.showMessage("Error in Downloading !", "Dismiss", 3000);
            });

      }


    }

  }

  getTxtColor = (statusName) => {
    if (statusName == 'Rejected')
      return "#cf0001"
    else if (statusName == 'Submitted')
      return "#ffa502"
    else if (statusName == 'Approved')
      return "#009432"
    else if (statusName == 'Verified')
      return "#1890FF"
    else if (statusName == 'Awaiting Approval')
      return "#45546E"
  }

  getBackGroundColor = (statusName) => {
    if (statusName == 'Rejected')
      return "#FFEBEC"
    else if (statusName == 'Submitted')
      return "#FFF3E8"
    else if (statusName == 'Approved')
      return "#EEF9E8"
    else if (statusName == 'Verified')
      return "#E8F4FF"
    else if (statusName == 'Awaiting Approval')
      return "#E8E9EE"
  }

  openRejectCommentDialog = (index) => {
 
    if (this.claimPaymentForm.get("ledgerAccount").invalid)
      return this._util.showMessage("Please choose Ledger Account !", "Dismiss", 3000)

    if (this.claimPaymentForm.get("ledgerAccountDescription").invalid)
      return this._util.showMessage("Please fill the Description !", "Dismiss", 3000)

    if (this.claimPaymentForm.get("targetEntity").invalid)
      return this._util.showMessage("Please choose Target Entity !", "Dismiss", 3000)

    if (this.claimPaymentForm.get("creditLedger").invalid)
      return this._util.showMessage("Please fill the Credit Ledger !", "Dismiss", 3000)


    let rejectItemDetails = this.approvalItemForm.value.approvalItems[index];

    rejectItemDetails.rejectType = "T" //Treasurer Rejection
    rejectItemDetails.expHeaderId = this.expenseDetail.expense_header_id;
    rejectItemDetails.claimed_by = this.expenseDetail.claimed_by;
    rejectItemDetails.expenseType = "CL/"


    let dialogRef = this.dialog.open(ExpenseRejectDialogComponent, {
      // height: '36%',
      width: '50%',
      data: {
        rejectItemDetails,
        primaryColor: this.primaryColor
      }
    });

    dialogRef.afterClosed().subscribe(res => {

      if (res == "success") {

        this.rejectItem(index);

      }
    })
  }
  changeInAttachment(event, i) {
    this.approvalItemForm.controls.approvalItems[i].context_id.patchValue(event);
  }

  openExpensePeopleInvolved(peopleInvolvedData){
    this.dialog.open(PeopleInvolvedComponent,{
      data : {
        peopleInvolvedData,
        primaryColor: this.primaryColor
      },
      height : '50%',
      width : '20%'
    })
  }

  formFieldConfig: any = {};
  fieldConfig: any;
  async getFormFieldConfig(){
    this._spinnerService.show();
    this.fieldConfig = {};
    await this._detailService
    .getFormFieldConfig(this.expenseDetail.legal_entity_id)
    .subscribe(
      (res) => {
        this.showExpenseCard = true;
        this.formFieldConfig = res['data'];
        console.log(this.formFieldConfig)
        // this.spinnerService.hide();
        this._spinnerService.hide();
        if(this.formFieldConfig){
          this.formFieldConfig.forEach((element) => {
          this.fieldConfig[element.field_key] = element
        });
      }
      },
      (err) => {
        this.showExpenseCard = true;
        // this.spinnerService.hide();
        this._spinnerService.hide();
        console.error(err);
      }
    );
  }

  fixNumberOnUI(no, currency) {
    if (typeof (no) == "string") {
      return no
    }
    else {
      if (no == 0 || no == '0' || no == '') {
        return '0.00';
      }
      let num = parseFloat(no);
      if (currency == "INR")
        return new Intl.NumberFormat('en-IN', { minimumFractionDigits: 2, maximumFractionDigits: 2 }).format(Number(num));
      else
        return new Intl.NumberFormat('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }).format(Number(num));
    }
  }

  taxCreditWarning = false;
  onTaxCreditValueChange(index){
    console.log("onTaxCreditValueChange")
    console.log(this.ExpenseEntryFormData.value)
    console.log(index)

    let taxCreditAmount = this.ExpenseEntryFormData.value[index].taxCredit;
    console.log(taxCreditAmount)

    let ledgerAmountDistribution = this.ExpenseEntryFormData.value[index].ledgerAmountDistribution;
    let total_verfied_amount = 0;
    _.each(ledgerAmountDistribution, l=>{
      let value = l.ledger_item_amount;
      value = value ? typeof(value) == "string" ? parseFloat(value.replace(/,/g, '')) : parseFloat(value) : 0;
      total_verfied_amount += value;
    })
    console.log("total_verfied_amount")
    console.log(total_verfied_amount)

    if(taxCreditAmount > total_verfied_amount){
      let warningText = `Tax amount should not be greater than verified amount`
      this.ExpenseEntryFormData.controls[index]["controls"].taxCreditWarning.patchValue(warningText);
      this.taxCreditWarning = true;
    }
    else{
      this.ExpenseEntryFormData.controls[index]["controls"].taxCreditWarning.patchValue(null);
      this.taxCreditWarning = false;
    }
  }

  getEntitySettlementAmount(item){
    let amount = this.parseJson(item.value.item_amount);
    let entityAmount = _.filter(amount,{currency_code: this.expenseDetail.entity_currency_code});
    let amountValue = entityAmount.length > 0 ? entityAmount[0].claim_amount : 0;
    if(this.expenseDetail.entity_currency_code == "INR"){
      amountValue = new Intl.NumberFormat('en-IN',{
      minimumFractionDigits: 2, maximumFractionDigits: 2}).format(Number(amountValue));
    }
    else{
      amountValue = new Intl.NumberFormat('en-US',{
      minimumFractionDigits: 2, maximumFractionDigits: 2}).format(Number(amountValue));
    }
    
    return amountValue;
  }


  gettaxAmountForPopOver(item){
    let amount = this.parseJson(item.value.item_amount);
    let taxAmount = _.filter(amount,{currency_code: this.expenseDetail.entity_currency_code});
    let amountValue = taxAmount.length > 0 ? taxAmount[0].tax_amount : 0;
    if(this.expenseDetail.entity_currency_code == "INR"){
      amountValue = new Intl.NumberFormat('en-IN',{
      minimumFractionDigits: 2, maximumFractionDigits: 2}).format(Number(amountValue));
    }
    else{
      amountValue = new Intl.NumberFormat('en-US',{
      minimumFractionDigits: 2, maximumFractionDigits: 2}).format(Number(amountValue));
    }
    
    return amountValue;
  }


  getClaimRaisedAmount(item){
    let amount = this.parseJson(item.value.item_amount);
    let entityAmount = _.filter(amount,{is_expense_currency: true});
    let amountValue = entityAmount.length > 0 ? entityAmount[0].claim_amount : 0;
    if(this.expenseDetail.entity_currency_code == "INR"){
      amountValue = new Intl.NumberFormat('en-IN',{
      minimumFractionDigits: 2, maximumFractionDigits: 2}).format(Number(amountValue));
    }
    else{
      amountValue = new Intl.NumberFormat('en-US',{
      minimumFractionDigits: 2, maximumFractionDigits: 2}).format(Number(amountValue));
    }
    
    return amountValue;
  }

  getClaimRaisedCurrency(item){
    let amount = this.parseJson(item.value.item_amount);
    let Currency = _.filter(amount,{is_expense_currency: true});
    let legalEntityCurrency = Currency.length > 0 ? Currency[0].currency_code : 0;
    return legalEntityCurrency;
  }

  getVerifiedAmountInEntityCurrency(amountValue, currency){
    amountValue = amountValue ? amountValue : 0;
    // let amount = this.parseJson(item.value.item_amount);
    // let entityAmount = _.filter(amount,{currency_code: this.expenseDetail.entity_currency_code});
    // let amountValue = entityAmount.length > 0 ? entityAmount[0].claim_amount : 0;
    if(currency == "INR"){
      amountValue = new Intl.NumberFormat('en-IN',{
      minimumFractionDigits: 2, maximumFractionDigits: 2}).format(Number(amountValue));
    }
    else{
      amountValue = new Intl.NumberFormat('en-US',{
      minimumFractionDigits: 2, maximumFractionDigits: 2}).format(Number(amountValue));
    }
    
    return amountValue;
  }

  isCommentsExist = false;
  async getCommentBoxData(){
    let inputData = {
      application_id: 65,
      application_name: "Expenses",
      title: "Expense - #EX/" + this.expenseDetail["expense_header_id"],
      unique_id_1: this.expenseDetail["expense_header_id"],
      unique_id_2: "",
    };
    let getCommentsData = await this._detailService.getComments(inputData);
      if(getCommentsData["messType"] == "S" && 
        Array.isArray(JSON.parse(getCommentsData["result"]["comments"])) && 
        JSON.parse(getCommentsData["result"]["comments"]).length > 0){
      this.isCommentsExist = true;
    }
  }
}


function validateAmount(control: AbstractControl): ValidationErrors {
  let amount = typeof control.value == "string" ? parseFloat(control.value.replace(/,/g, '')) : control.value;
  if (amount != null && typeof amount === 'number' && amount < 0) {
    return { validAmount: false };
  }
  
  const regex = /^[^~!@#$%\^&*()_+=-{}|[\]\\:';"<>?,/]+(\.[^~!@#$%\^&*()_+={}|[\]\\:';"<>?,/]+)?$/;
  if (!regex.test(amount)) {
    return { validAmount: false };
  }

  return null;
}

