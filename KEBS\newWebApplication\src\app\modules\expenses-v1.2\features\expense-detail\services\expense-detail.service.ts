import { Injectable } from "@angular/core";
import { HttpClient } from "@angular/common/http";
import { BehaviorSubject, Observable, Subject } from "rxjs";
@Injectable({
  providedIn: "root",
})
export class ExpenseDetailService {
  private msgSub = new Subject<any>();

  public $reloadTreasury = new BehaviorSubject('');
  public $reloadApprove = new BehaviorSubject('');

  constructor(private $http: HttpClient) { }
  getApprovalItemDetail = (headerId, userRole, userOid) => {
    return this.$http.post("/api/exPrimary2/getExpenseItems", {
      expHeaderId: headerId,
      role: userRole,
      oid: userOid,
    });
  };

  updateApprovalStatus = (expenseHeaderId, itemArr, initiatorOId, costCenter, costCenterDescription, customerBilling) => {
    return this.$http.post("/api/exPrimary2/UpdateExpenseStatus", {
      expHeaderId: expenseHeaderId,
      expItems: itemArr,
      subApplicationId: 2,
      initiatorOId: initiatorOId,
      costCenter: costCenter,
      costCenterDescription: costCenterDescription,
      customerBilling: customerBilling

    });
  };

  accountLedgerDescription = (claimId: any) => {
    return this.$http.post("/api/exPrimary2/accountLedgerDescription", {
     claimId : claimId
    });
  };

  approveExpenseItem = (expenseHeaderId,itemArr,initiatorOId,costCenter,costCenterDescription,customerBilling) =>{
    return this.$http.post("/api/exPrimary2/approveExpenseItem", {
      expHeaderId: expenseHeaderId,
      expItems: itemArr,
      subApplicationId: 2,
      initiatorOId: initiatorOId,
      costCenter: costCenter,
      costCenterDescription: costCenterDescription,
      customerBilling: customerBilling
    });
  }

  getExpenseWorkFlowProperties = () => {
    return this.$http.post("/api/exPrimary2/getWorkflowProperties", {
      subApplicationId: 2,
    });
  };

  getExpenseApproversHierarchy = (hierarchyParams) => {
    return this.$http.post(
      "/api/wfPrimary/getApproversHierarchy",
      hierarchyParams
    );
  };

  updateTreasuryApprovalStatus = (headerId, itemArr, expenseDetail, targetLegalEntityId) => {
    return this.$http.post("/api/exPrimary2/UpdateTreasuryExpenseStatus", {
      expHeaderId: headerId,
      expItems: itemArr,
      expenseDetail: expenseDetail,
      targetLegalEntityId: targetLegalEntityId,
      claimType: "C"
    });
  };

  //? trigger treasury wf
  
  triggerTreasuryWf = (expHeaderId,approvers) =>{
    return this.$http.post("/api/exPrimary2/treasuryWorkflowTrigger",{
      expHeaderId :expHeaderId,
      approvers : approvers
    })
  }


  //? get what are the banks available for a legal entity
  getAvailableBanks = (legalEntityId) => {
    return this.$http.post("/api/invoice2/bankCrudOperations", {
      legalEntityId: legalEntityId,
      crudId: 2,
    });
  };

  //move cheque to bank
  moveToBank = (eHeaderID, paymentDetailJSON, totalChequeVal,chequeAmount,paymentCurrencyCode, bankLedgerName, targetlegalEntityId, expenseDetail) => {
    return this.$http.post("/api/exPrimary2/movedToBank", {
      expHeaderId: eHeaderID,
      paymentDetails: paymentDetailJSON,
      amountClaimedValue: totalChequeVal,
      claimCurrency: paymentCurrencyCode,
      chequeAmount : chequeAmount,
      bankLedgerName: bankLedgerName,
      targetlegalEntityId: targetlegalEntityId, 
      expenseDetail: expenseDetail
    });
  };

  savePaymentDetails = (eHeaderId, paymentDetailJSON, associateID) => {
    return this.$http.post("/api/exPrimary2/savePaymentDetailsOnly", {
      expHeaderId: eHeaderId,
      paymentDetails: paymentDetailJSON,
      associateID: associateID
    });
  };

  retrievePaymentDetail = (eHeaderID) => {
    return this.$http.post("/api/exPrimary2/getPaymentDetails", {
      expHeaderId: eHeaderID,
    });
  };

  getFileDataFromS3 = (keyName) => {

    return this.$http.post('/api/exPrimary2/getFileDataFromS3', {
      keyName: keyName
    });

  };

  getExpenseDefaultBank = () =>{
    return this.$http.post("/api/invoice/getExpenseDefaultBank", {});
  }

  // To get default expense legal entity 
  getExpenseDefaultLegalEntity = () =>{
    return this.$http.post("/api/exPrimary2/getDefaultlegalEntity", {});
  }

  sendMsg = (msg) => {
    this.msgSub.next(msg);
  };
  getMsg = () => {
    return this.msgSub.asObservable();
  };

  claimKnockOff = (oid,claimId) =>{
    return this.$http.post("/api/exPrimary2/newKnockOff", {
      oid : oid,
      claimId : claimId
    });
  }

  getLedgerAccountDetail = (oid) =>{

    return this.$http.post("/api/exPrimary2/getLedgerAccountDetail", {
      oid : oid
    });

  }

  getTargetEntityBasedOnCostCenter = (cost_center, expense_header_id) =>{
    return this.$http.post("/api/exPrimary2/getTargetEntityBasedOnCostCenter", {
      cost_center : cost_center,
      expense_header_id : expense_header_id
    });

  }

  updateExpenseToTally = (details) =>{
    return this.$http.post("/api/exPrimary2/updateExpenseToTally", {
      details : details
    });
  }

  getCurrentAmountClaimed = (expHeaderId, entityCurrencyCode) =>{

  return this.$http.post("/api/exPrimary2/getCurrentAmountClaimed", {
    expHeaderId : expHeaderId,
    entityCurrencyCode : entityCurrencyCode
  });

}

getSourceEntityDefaultBank = (legalEntityId) =>{

  return this.$http.post("/api/exPrimary2/getSourceEntityDefaultBank ", {
    legal_entity_id : legalEntityId
  });

}

getExpensePaymentTerms = () =>{
  return this.$http.post("/api/exPrimary2/getExpensePaymentTerms ", { });
}

getExpenseBillingConfig = () =>{
  return this.$http.post("/api/exPrimary2/getExpenseBillingConfig ", { });
}

getExpensesOfApprover = (oid, sortOrder, startIndex, numberOfRecords) => {
  return this.$http.post("/api/exPrimary2/getExpensesOfApprover", {
    associateOId: oid,
    sortOrder: sortOrder,
    startIndex: startIndex,
    numberOfRecords: numberOfRecords,
  });
};

saveExpenseRejectComment = (rejectCommentDetails) => {
  return this.$http.post('/api/exPrimary2/saveExpenseRejectComment', {
    rejectCommentDetails: rejectCommentDetails
  });
}

getCCApprovedExpenses = (oid, sortOrder, startIndex, numberOfRecords) => {
  return this.$http.post('/api/exPrimary2/getCCApprovedExpenses', {
    associateOId: oid,
    sortOrder: sortOrder,
    startIndex: startIndex,
    numberOfRecords: numberOfRecords,
  })
}

updateExpenseCustomerBilling = (is_billable,expense_header_id) => {
  return this.$http.post('/api/exPrimary2/updateExpenseCustomerBilling', {
    is_billable :is_billable,
    expense_header_id: expense_header_id
  })
}

getUpdatedTreasuryValue(status, t_action_by, t_action_on){
  let treasuryData = {
    status, t_action_by, t_action_on
  }
  this.$reloadTreasury.next(JSON.stringify(treasuryData));
}


getUpdatedTreasurerChanges(is_billable, cost_centre, cost_centre_description, department, status){
  let treasuryData = {
    is_billable, cost_centre, cost_centre_description, department, status
  }
  this.$reloadTreasury.next(JSON.stringify(treasuryData));
}

getUpdatedDetailPageChanges(is_billable){
  let treasuryData = {
    is_billable
  }
  this.$reloadApprove.next(JSON.stringify(treasuryData));
}

getCreditLedgerTenantDetails(claimerAid, expenseType, expenseCategory, expense_header_id){
  try{
    return new Promise((resolve,reject)=>{
      this.$http.post('/api/exPrimary2/getCreditLedgerTenantDetails',{
        claimerAid, expenseType, expenseCategory, expense_header_id
      })
      .subscribe((res)=>{
        return resolve(res);
      },(err)=>{
        return reject(err);
      })
    })
  }
  catch(err){
    return Promise.reject(err);
  }
}

getCreditLedgerMasterData(claimerAid, expense_header_id){
  try{
    return new Promise((resolve,reject)=>{
      this.$http.post('/api/exPrimary2/getCreditLedgerMasterData',{
        claimerAid,
        expense_header_id
      })
      .subscribe((res)=>{
        return resolve(res);
      },(err)=>{
        return reject(err);
      })
    })
  }
  catch(err){
    return Promise.reject(err);
  }
}

getExpenseConfigfunction = () =>{
  return this.$http.post("/api/exPrimary2/getExpenseConfig ", { });
}

getExpenseGeneralConfig = () =>{
  return new Promise((resolve, reject)=>{
    this.$http.post("/api/exPrimary2/getExpenseGeneralConfig",{
    }).subscribe((res)=>{
      resolve(res)
    },(err)=>{
      reject(err)
    })
  })
}

getExpenseConfig = () =>{
  return new Promise((resolve, reject)=>{
    this.$http.post("/api/exPrimary2/getExpenseConfig",{
    }).subscribe((res)=>{
      resolve(res)
    },(err)=>{
      reject(err)
    })
  })
}

getExpenseTag = (legalEntityId) =>{
  return new Promise((resolve, reject)=>{
    this.$http.post("/api/exPrimary2/getExpenseTag",{legalEntityId: legalEntityId}).subscribe((res)=>{
      resolve(res)
    },(err)=>{
      reject(err)
    })
  })
}

updateExpenseTag = (id, headerId) =>{
  return this.$http.post('/api/exPrimary2/updateExpenseTag', {
    expenseTagId: id,
      expenseHeaderId: headerId
  })
}

getFormFieldConfig = (legalEntity) => {
  return this.$http.post('/api/exPrimary2/getDetailsPageFormFieldConfig', {
    legalEntity: legalEntity
  });
}

getExpenseItemData = (expenseHeaderId) =>{
  return new Promise((resolve, reject)=>{
    this.$http.post("/api/exPrimary2/getExpenseItemData",{expenseHeaderId: expenseHeaderId}).subscribe((res)=>{
      resolve(res)
    },(err)=>{
      reject(err)
    })
  })
}

getExpenseStatusConfig = () =>{
  return this.$http.post("/api/exPrimary2/getExpenseStatusConfig ", { });
}

getExpenseUiMasterConfig = (pageId) => {
  return this.$http.post("/api/exPrimary2/getExpenseMasterUiConfig", {
  pageId : pageId
  });
};

getComments(data) {
  return new Promise((resolve, reject) => {
    this.$http.post('/api/general/getComments', data).subscribe(
      (res) => {
        // // console.log(res);
        return resolve(res);
      },
      (err) => {
        return reject(err);
      }
    );
  });
}

getAllCostCenter = () =>{
  return new Promise((resolve, reject)=>{
    this.$http.post("/api/exPrimary2/getAllCostCenter",{}).subscribe((res)=>{
      resolve(res)
    },(err)=>{
      reject(err)
    })
  })
}

getDepartmentMaster = (costCent) => {
  return this.$http.post("/api/exPrimary2/getDepartment", {
    costCenter: costCent,
  });
};

getExpenseCurrencies = () =>{
  return new Promise((resolve, reject)=>{
    this.$http.post("/api/exPrimary2/getExpenseReportingCurrencies",{
    }).subscribe((res)=>{
      resolve(res)
    },(err)=>{
      reject(err)
    })
  })
}

}
