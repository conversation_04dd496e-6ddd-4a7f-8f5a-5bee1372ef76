<div class="create-billable-claims-popup-styles row">

    <div class="col-3 p-0 left-half">

        <div class="col-12 p-0 pl-3 pr-3 pt-3" style="height:100%">
          
            <div style="height:75%">
            <p class="create-milestone col-12 p-0 m-0">Create Milestone</p>

            <p class="milestone-header col-12 p-0 pt-4 m-0 mb-1">Milestone Name<span class="mandatory-red">*</span></p>

            <mat-form-field appearance="outline" style="width: 100%; height: 45px;">
                <input matInput style="padding-bottom: 1px !important;" placeholder="Milestone Name"
                    [(ngModel)]="milestoneName" autocomplete="off" />
                <mat-icon matSuffix>
                    <button mat-button *ngIf="milestoneName && milestoneName != ''" matSuffix mat-icon-button
                        style="height: 30px; width: 30px; line-height: 1" (click)="clearMilestoneName()">
                        <mat-icon style="font-size: 20px !important; color: #66615b !important" matTooltip="Clear">
                            close
                        </mat-icon>
                    </button>
                </mat-icon>
            </mat-form-field>

            <p class="milestone-header col-12 p-0 pt-3 m-0 mb-1">{{costCenterFieldLabel ? costCenterFieldLabel : "Cost center"}}</p>
            <div>
                <mat-form-field appearance="outline" class="ml-auto mr-auto" style="width: 100%;">
                    <span matPrefix>
                        <mat-icon style="font-size: 20px !important; color: black !important">search</mat-icon>
                    </span>
                    <input matInput style="padding-bottom: 1px !important;" placeholder="Search"
                        [(ngModel)]="costCenterSearchParameter" (keyup.enter)="callSearchApi()"
                        (keyup)="onSearchCostCenter($event)" autocomplete="off" />
                    <mat-icon matSuffix>
                        <button mat-button *ngIf="costCenterSearchParameter && costCenterSearchParameter != ''" matSuffix
                            mat-icon-button style="height: 30px; width: 30px; line-height: 1"
                            (click)="clearCostCenterSearch()">
                            <mat-icon style="font-size: 20px !important; color: #66615b !important"
                                matTooltip="Clear Search">
                                close
                            </mat-icon>
                        </button>
                    </mat-icon>
                </mat-form-field>
            </div>
            <div class="col-12 p-0 {{(!isProjectAndItemApisLoading && isProjectAndItemVisible) ? 'claim-list-with-project' : 'claim-list'}}"
            *ngIf="costCenterClaimList.length > 0" infinite-scroll
            [infiniteScrollDistance]="2" (scrolled)="getCostCenterList()" [scrollWindow]="false"
            cdkScrollable [stopPropagation]="true">
                <div> </div>
           
                <div>
                    <div *ngFor="let item of costCenterClaimList" class="cost-centre-item">
                    <div class="row pt-2 pb-2">
                    <div class="col p-0 pl-1" matTooltip="{{item?.name && item?.name != null ? item?.name : ''}} - {{item?.description && item?.description != null ? item?.description : ''}} ({{item?.claim_count && item?.claim_count != null ? item?.claim_count : ''}})"
                    (click)="showBillableClaims(item)" style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis; width: 100%;">
                        <span>{{item?.name}} - <span style="color:#45546e">{{item?.description}}</span></span>
                        <span class="pl-1" style="color: #cf0001;">({{item?.claim_count}})</span>
                    </div>
                    </div>
                    <hr *ngIf="costCenterClaimList.length>1" style="margin-top: 0%; margin-bottom: 0%;">
                    </div>
                </div>
            </div>
            <!-- <app-input-search-huge-input [placeholder]="'Search'" style="height: 45px;"
                [ngClasses]="'small-100'" class="app-input-search-class mb-0"
                matTooltip="{{costCentreTemp && costCentreTemp.name != null ? costCentreTemp.name : ''}}{{costCentreTemp && costCentreTemp.name != null ? ' - ' : ''}}{{costCentreTemp && costCentreTemp.description != null ? costCentreTemp.description : ''}}"
                [optionLabel]="['name', 'description']" [(ngModel)]="costCentre" (ngModelChange)="onCostCentreChange()"
                [apiUri]="'/api/exPrimary/searchProjectCostCentres'">
            </app-input-search-huge-input> -->
            </div>
            <div style="height:25%">
            <mat-spinner *ngIf="isProjectAndItemApisLoading && !isProjectAndItemVisible" class="mt-4 ml-auto mr-auto"
                diameter="25" matTooltip="Loading ..."> </mat-spinner>

                <p class="milestone-header col-12 p-0 pt-3 m-0 mb-1" *ngIf="!isProjectAndItemApisLoading && isProjectAndItemVisible">
                    Portfolio Name</p>
                <p [matTooltip]="projectName" class="milestone-description col-12 p-0 m-0"
                    *ngIf="!isProjectAndItemApisLoading && isProjectAndItemVisible">
                    {{projectName}}</p>
                
                <p class="milestone-header col-12 p-0 pt-3 m-0 mb-1" *ngIf="!isProjectAndItemApisLoading && isProjectAndItemVisible">
                    Project Name</p>
                <p [matTooltip]="itemName" class="milestone-description col-12 p-0 m-0"
                    *ngIf="!isProjectAndItemApisLoading && isProjectAndItemVisible">
                    {{itemName}}</p>
            </div>
        </div>

    </div>

    <div class="col-9 p-0">

        <div class="col-12 p-0 row pl-3 pt-2 pb-3">

            <mat-form-field appearance="outline" style="width: 35%; height: 45px;" class="mr-4"
                *ngIf="!isProjectAndItemApisLoading && isProjectAndItemVisible">
                <span matPrefix>
                    <mat-icon style="font-size: 20px !important; color: black !important">search</mat-icon>
                </span>
                <input matInput style="padding-bottom: 1px !important;" placeholder="Search and press Enter"
                    [(ngModel)]="searchParameter" (keyup.enter)="callSearchApi()"
                    (ngModelChange)="onSearchParameterChange()" autocomplete="off" />
                <mat-icon matSuffix>
                    <button mat-button *ngIf="searchParameter && searchParameter != ''" matSuffix mat-icon-button
                        style="height: 30px; width: 30px; line-height: 1" (click)="clearSearch()">
                        <mat-icon style="font-size: 20px !important; color: #66615b !important"
                            matTooltip="Clear Search">
                            close
                        </mat-icon>
                    </button>
                </mat-icon>
            </mat-form-field>

            <!-- <kebs-mul-sel-inf-search *ngIf="!isProjectAndItemApisLoading && isProjectAndItemVisible"
                style="width: 26%; height: 45px;" class="mr-4" label="Category" [token]="token" [optionLabel]="['name']"
                (selectedValues)="selectCategory($event)" [API_URL]="searchClaimCategories" [lazyLoadingCount]="10">
            </kebs-mul-sel-inf-search> -->
            <app-expense-multi-select *ngIf="!isProjectAndItemApisLoading && isProjectAndItemVisible"
                style="width: 26%; height: 45px;" class="mr-4" label="Claim type" [token]="token" [optionLabel]="['name']"
                (selectedValues)="selectCategory($event)" [API_URL]="searchClaimCategories" [lazyLoadingCount]="20">
            </app-expense-multi-select>

            <input *ngIf="!isProjectAndItemApisLoading && isProjectAndItemVisible" matTooltip="Duration" type="text"
                style="width: 26%; height: 41px;" class="mr-3 dp-class" [showCustomRangeLabel]="true"
                [locale]="{applyLabel: 'Apply', format: 'DD MMM YYYY', customRangeLabel: 'Custom Duration'}"
                [alwaysShowCalendars]="true" [showCancel]="true" [ranges]="durationRanges" [linkedCalendars]="true"
                ngxDaterangepickerMd [(ngModel)]="duration" placeholder="Duration" (change)="selectDuration($event)" />

            <button mat-icon-button matTooltip="Close" class="align-right mr-2">
                <mat-icon (click)="closeModal()" class="close-button">close</mat-icon>
            </button>

        </div>

        <div *ngIf="isProjectAndItemApisLoading && !isProjectAndItemVisible" class="col-12 p-0 milestone-list-loading">
            <mat-spinner class="mt-auto mb-auto ml-auto mr-auto" diameter="25" matTooltip="Loading ..."> </mat-spinner>
        </div>

        <div class="col-12 p-0 pl-3 pr-3 {{areExpenseMilestonesLoading ? 'milestone-list-loading-more' : 'milestone-list'}}"
            *ngIf="!isProjectAndItemApisLoading && isProjectAndItemVisible && expenseList.length > 0" infinite-scroll
            [infiniteScrollDistance]="2" (scrolled)="getExpenseListBasedOnCostCentre(false)" [scrollWindow]="false"
            cdkScrollable>

            <span (click)="handleCheckBox('')">
                <mat-checkbox [checked]="isAllChecked" class="mt-1 ml-2" style="cursor: pointer;" disabled>
                    <span style="color: #66615b; font-weight: 400; font-size: 12px;" disabled>Select All</span>
                </mat-checkbox>
            </span>

            <div class="col-12 p-0 mb-2 milestone-list-item" *ngFor="let expenseListItem of expenseList"
                (click)="selectExpenseListItem(expenseListItem)">

                <div class="col-12 p-2 row">

                    <mat-checkbox [checked]="expenseListItem.isChecked" class="mt-1 mr-3" style="cursor: pointer;"
                        disabled></mat-checkbox>

                    <p [matTooltip]="expenseListItem.expense_category" class="milestone-category col-3 p-0 m-0">
                        {{expenseListItem.expense_category}}</p>

                    <!-- <p class="milestone-department p-0 m-0 col-1"> - </p>

                    <p [matTooltip]="expenseListItem.department" class="milestone-department col-7 p-0 m-0">
                        {{expenseListItem.department}}</p> -->

                </div>

                <div class="col-12 p-2 row milestone-list-bottom">

                    <div class="col-2-5 milestone-item">
                        <p class="milestone-item-header col-12 p-0 m-0">Expense Code</p>
                        <p [matTooltip]="expenseListItem.expense_code"
                            class="milestone-item-description col-12 p-0 m-0">{{expenseListItem.expense_code}}</p>
                    </div>

                    <div class="col-2-5 milestone-item ml-2">
                        <p class="milestone-item-header col-12 p-0 m-0">Amount Requested</p>
                        <p [matTooltip]="expenseListItem.expense_request_amount"
                            class="milestone-item-description col-12 p-0 m-0">{{expenseListItem.expense_request_amount}}
                        </p>
                    </div>

                    <div class="col-2-5 milestone-item ml-2">
                        <p class="milestone-item-header col-12 p-0 m-0">Amount Verified</p>
                        <p [matTooltip]="expenseListItem.expense_claim_amount"
                            class="milestone-item-description col-12 p-0 m-0">{{expenseListItem.expense_claim_amount}}
                        </p>
                    </div>

                    <div class="col-2-5 milestone-item ml-2">
                        <p class="milestone-item-header col-12 p-0 m-0">Requested By</p>

                        <div class="col-12 p-0 row" [matTooltip]="expenseListItem.requested_by"
                            style="align-items: center;">
                            <app-user-image class="mr-2" [id]="expenseListItem ? expenseListItem.requested_by_oid : ''"
                                content-type="template" [imgWidth]="'25px'" [imgHeight]="'25px'"></app-user-image>
                            <p class="milestone-item-description m-0 col-9 p-0">{{expenseListItem.requested_by}}</p>
                        </div>
                    </div>

                    <div class="col-2-5 ml-2">
                        <p class="milestone-item-header col-12 p-0 m-0">Claim Created On</p>
                        <p [matTooltip]="expenseListItem.billed_on" class="milestone-item-description col-12 p-0 m-0">
                            {{expenseListItem.billed_on}}</p>
                    </div>

                </div>

            </div>

        </div>

        <mat-spinner *ngIf="areExpenseMilestonesLoading==true" class="mt-3 ml-auto mr-auto" diameter="25"
            matTooltip="Loading ..."> </mat-spinner>

        <div class="col-12 row p-0 pl-3 pr-3 mt-3"
            *ngIf="!isProjectAndItemApisLoading && isProjectAndItemVisible && expenseList.length > 0">

            <div class="align-left col-8 p-0" *ngIf="areAnyExpensesSelected">
                <p class="total-milestone-header col-12 p-0 m-0">Total Claim Value</p>
                <p class="total-milestone-description col-12 p-0 m-0">{{totalClaimValue}}
                </p>
            </div>

            <mat-spinner *ngIf="isCreatingMilestone" class="mr-4 mt-2 align-right" diameter="25"
                matTooltip="Creating Milestone ..."> </mat-spinner>

            <button *ngIf="!isCreatingMilestone" mat-flat-button class="pl-0 pr-0 mr-2 red-btn align-right"
                (click)="createBillableClaim()" [disabled]="!areAnyExpensesSelected">Create<mat-icon
                    class="save-btn-icon" matListIcon>navigate_next
                </mat-icon></button>

        </div>

        <div class="col-12 p-0 milestone-list-loading row" style="text-align: center;"
            *ngIf="!isProjectAndItemApisLoading && !isProjectAndItemVisible">

            <img src="https://assets.kebs.app/images/no_claims_here.png" />

            <p class="create-milestone col-12 p-0 m-0">No Claims Here</p>

            <p class="milestone-header-bigger col-12 p-0 m-0">Populate claim list by selecting a {{costCenterFieldLabel ? costCenterFieldLabel : "Cost center"}}!</p>

        </div>

    </div>
    <ngx-spinner size="medium" type="ball-clip-rotate" bdColor="rgba(236, 233, 233, 0.8)" color="#cf0001"></ngx-spinner>

</div>