<div class="container-fluid expense-claim-creation" [formGroup]="claimForm">
  <div class="row pt-2 pb-2 pl-2 align-items-center" style="font-weight: 800; font-size: 16px;">
    <div class="col-6 d-flex align-items-center">
      <span>New Claim</span>
      <span *ngIf="!isExpenseCreationAllowed && defaultLegalEntityBasedOnClaimer"
        class="d-flex align-items-center ml-4">
        <mat-icon class="blinking-icon pt-1">info</mat-icon>
        <span style="font-weight: 500; font-size: 14px; color: #007bff; margin-left: 5px;">
          {{ expense_creation_restriction_message }}
        </span>
      </span>
    </div>

    <div class="col-6 d-flex justify-content-end">
      <div class="header-start" (click)="closeForm()" style="cursor: pointer;">
        <span class="header-text ml-2">Close</span>
      </div>
    </div>
  </div>

  <div class="row border-top">
    <div class="col-3 pl-3 pr-0 pt-3">
      <div class="row p-0 pl-2 pb-2" [style.color]="theme_color1" style="font-size: 15px; font-weight: 500;">
        Claim Type
      </div>
      <div class="row pt-2" *ngIf="claimType.length > 0">
        <div class="col-12 pl-0 pr-0 scrollable-content-side-nav">
          <div class="d-flex mb-1" *ngFor="let type of claimType; let i = index">
            <button mat-raised-button class="mr-1" [ngClass]="type.active ? 'btn-active' : 'btn-not-active'"
              (click)="selectClaimType(i)" matTooltip="{{type.description}}" *ngIf="i<showIndex">
              <div class="icon-circle">
                <mat-icon class="claim-icons">
                  {{ type.icon }}
                </mat-icon>
              </div>
              <div class="category">
                {{ type.name }}
              </div>
            </button>
            <span *ngIf="type?.count" style="width: 30px;
            height: 30px;
            color: rgb(95, 108, 129);
            border-radius: 100%;
            font-size: 12px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            text-align: center; /* Center the text horizontally */
            line-height: 30px;
            margin-top: 2px;
            margin-left: 9px;
            " [style.border]="'1px solid ' + theme_color1">{{type?.count}}</span>
            <!-- <span class="ml-2 pt-1 pb-1 pl-2 pr-2" *ngIf="type?.count" [style.border]="'1px solid ' + theme_color1" style="color: #5F6C81; border-radius: 100%; font-size: 12px;overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;">{{type?.count}}</span> -->
          </div>
        </div>
        <!-- <div *ngIf = "claimType.length > 16" >
          <button mat-button (click) = "showIndex == 16 ? showIndex = claimType.length : showIndex = 16">{{showIndex == 16 ? "more..." : "show less"}}</button>
        </div> -->
      </div>
    </div>
    <div class="col pl-0 pr-0 border-left">
      <div *ngIf="showClaimCreation == false" class="mt-3 spinner-border d-flex justify-content-center" role="status"
        style="margin-left: 350px; color: #79BA44;">
        <span class="sr-only" [ngStyle]="{'color': '#79BA44', 'background-color': '#79BA44'}">Loading...</span>
      </div>
      <div *ngIf="showClaimCreation == true">
        <div class="row pt-3">
          <div *ngIf="fieldConfig?.claimedBy?.is_visible ? fieldConfig?.claimedBy?.is_visible : 0" class="col-4">
            <div class="label pl-2">{{fieldConfig?.claimedBy?.field_label ? fieldConfig.claimedBy.field_label:'Claimed
              By *'}}
              <ng-template
                *ngIf="claimForm.get('claimedBy')?.hasError('required') && claimForm.get('claimedBy')?.touched"
                [ngTemplateOutlet]="requiredTemplate">
              </ng-template>
            </div>
            <app-expense-search-user class="font-family-class" [ngClass]="{
            'app-input-search-field-highlight': 
              claimForm.get('claimedBy')?.hasError('required') && 
              claimForm.get('claimedBy')?.touched
          }" [isAutocomplete]="true" formControlName="claimedBy" [readonly]="isDisableClaimedBy"
              [disabled]="fieldConfig?.claimedBy?.is_disabled ? true : false"
              [bgColorOnDisable]="fieldConfig?.claimedBy?.is_disabled ? true : false"
              (change)="onClaimedByChange($event)">
            </app-expense-search-user>
          </div>
          <div *ngIf="fieldConfig?.claimDate?.is_visible ? fieldConfig?.claimDate?.is_visible : 0" class="col-4">
            <div class="label pl-2">{{fieldConfig?.claimDate?.field_label ? fieldConfig.claimDate.field_label:'Date of
              submission'}}</div>
            <mat-form-field appearance="outline" class="create-claim-field" #myFormField style="width: 100%" [ngClass]="{
            'disabled-field': fieldConfig?.claimDate?.is_disabled == 1
          }">
              <!-- <mat-label>{{fieldConfig?.claimDate?.field_label ? fieldConfig.claimDate.field_label:'Date of submission'}}</mat-label> -->
              <input matInput required="true" formControlName="claimDate"
                [readonly]="fieldConfig?.claimDate?.is_disabled ? true : false"
                [required]="fieldConfig?.claimDate?.is_mandatory ? true : false" />
            </mat-form-field>
          </div>
          <div class="col-4">
            <div class="label pl-2">{{fieldConfig?.legalEntityCode?.field_label ?
              fieldConfig.legalEntityCode.field_label:'Legal Entity'}}
              <ng-template
                *ngIf="claimForm.get('legalEntity')?.hasError('required') && claimForm.get('legalEntity')?.touched"
                [ngTemplateOutlet]="requiredTemplate">
              </ng-template>
            </div>
            <app-exp-input-search class="create-claim-field-inputsearch font-family-class" [ngClass]="{
            'app-input-search-field-highlight': 
              claimForm.get('legalEntity')?.hasError('required') && 
              claimForm.get('legalEntity')?.touched
          }" required="true"
              [placeholder]="fieldConfig?.legalEntityCode?.field_label ? fieldConfig.legalEntityCode.field_label:'Legal Entity'"
              [list]="legalEntityList" formControlName="legalEntityCode" (change)="changeLegalEntity($event)"
              [disabled]="fieldConfig?.legalEntityCode?.is_disabled ? true : false"
              [bgColorOnDisable]="fieldConfig?.legalEntityCode?.is_disabled ? true : false" [hideMatLabel]="true">
            </app-exp-input-search>
          </div>
          <!-- <div *ngIf="fieldConfig?.legalEntityCurrency?.is_visible ? fieldConfig?.legalEntityCurrency?.is_visible : 0" class="col-4">
          <mat-form-field appearance="outline" class="create-claim-field" style="width: 100%" #myFormField>
            <mat-label>{{fieldConfig?.legalEntityCurrency?.field_label? fieldConfig.legalEntityCurrency?.field_label:'Entity Currency'}}</mat-label>
            <input matInput [placeholder]="fieldConfig?.legalEntityCurrency?.field_label ? fieldConfig.legalEntityCurrency?.field_label:'legalEntityCurrency'" formControlName="legalEntityCurrency" readonly/>
          </mat-form-field>
        </div> -->
          <div *ngIf="fieldConfig?.costCenterCode?.is_visible ? fieldConfig?.costCenterCode?.is_visible : 0"
            class="col-4">
            <div class="label pl-2">{{fieldConfig?.costCenterCode?.field_label ? fieldConfig.costCenterCode.field_label
              :'Cost Center'}} *
              <ng-template
                *ngIf="claimForm.get('costCenter')?.hasError('required') && claimForm.get('costCenter')?.touched"
                [ngTemplateOutlet]="requiredTemplate">
              </ng-template>
            </div>
            <app-exp-input-search class="create-claim-field-inputsearch font-family-class" [ngClass]="{
            'app-input-search-field-highlight': claimForm.get('costCenter')?.hasError('required') && claimForm.get('costCenter')?.touched
          }" [placeholder]="fieldConfig?.costCenterCode?.field_label ? fieldConfig.costCenterCode.field_label :'Cost Center'"
              [list]="costCenterList" formControlName="costCenterCode" (change)='changeCostCenter($event)'
              [disabled]="fieldConfig?.costCenterCode?.is_disabled ? true : false"
              [bgColorOnDisable]="fieldConfig?.costCenterCode?.is_disabled ? true : false" [hideMatLabel]="true">
            </app-exp-input-search>
          </div>
          <div *ngIf="fieldConfig?.department?.is_visible ? fieldConfig?.department?.is_visible : 0" class="col-4">
            <div class="label pl-2">{{fieldConfig?.department?.field_label ?
              fieldConfig.department.field_label:'Department *'}}</div>
            <mat-form-field appearance="outline" class="create-claim-field" #myFormField style="width: 100%" [ngClass]="{
            'disabled-field': fieldConfig?.department?.is_disabled == 1
          }">
              <!-- <mat-label>{{fieldConfig?.department?.field_label ? fieldConfig.department.field_label:'Department *'}}</mat-label> -->
              <input matInput
                [placeholder]="fieldConfig?.department?.field_label ? fieldConfig.department.field_label:'Department'"
                formControlName="department" [readonly]="fieldConfig?.department?.is_disabled ? true : false"
                [required]="fieldConfig?.department?.is_mandatory ? true : false" />
            </mat-form-field>
          </div>
          <div *ngIf="fieldConfig?.expenseTag?.is_visible ? fieldConfig?.expenseTag?.is_visible : 0" class="col-4">
            <div class="label pl-2">
              {{fieldConfig?.expenseTag?.field_label ? fieldConfig.expenseTag.field_label : 'Expense Tag'}} *
              <ng-template
                *ngIf="claimForm.get('expenseTag')?.hasError('required') && claimForm.get('expenseTag')?.touched"
                [ngTemplateOutlet]="requiredTemplate">
              </ng-template>
            </div>

            <app-exp-input-search class="create-claim-field-inputsearch" [ngClass]="{
              'app-input-search-field-highlight': 
                claimForm.get('expenseTag')?.hasError('required') && 
                claimForm.get('expenseTag')?.touched
            }" required="true"
              [placeholder]="fieldConfig?.expenseTag?.field_label ? fieldConfig.expenseTag.field_label : 'Expense Tag'"
              [list]="expenseTagList" formControlName="expenseTag" (input)="onExpenseTagChange($event)"
              [disabled]="fieldConfig?.expenseTag?.is_disabled ? true : false"
              [bgColorOnDisable]="fieldConfig?.expenseTag?.is_disabled ? true : false" [hideMatLabel]="true">
            </app-exp-input-search>
          </div>

          <div class="col-3" *ngIf="isManualApproverSelectionApplicable == true && isHeaderLevelApprover == true">
            <div class="label pl-2">{{fieldConfig?.manualApprover?.field_label ?
              fieldConfig.manualApprover.field_label:'Select Approver *'}}
              <span *ngIf="fieldConfig?.manualApprover?.is_mandatory">*</span>

              <mat-icon class="info-icon" tooltip="Select a cost center to enable approver selection"
                content-type="html">
                info_outline
              </mat-icon>

              <ng-template
                *ngIf="claimForm.get('headerLevelApprover')?.hasError('required') && claimForm.get('headerLevelApprover')?.touched"
                [ngTemplateOutlet]="requiredTemplate">
              </ng-template>

            </div>
            <div [ngClass]="{
  'disabled-field': !approversList?.length,
    'app-input-search-field-highlight': 
    claimForm.get('headerLevelApprover')?.hasError('required') && 
    claimForm.get('headerLevelApprover')?.touched
}">
              <mat-form-field appearance="outline" style="width:100%" #myFormField>
                <!-- <mat-label>{{fieldConfig?.manualApprover?.field_label ? fieldConfig.manualApprover.field_label:'Select Approver *'}}</mat-label> -->
                <mat-select formControlName="headerLevelApproverList"
                  [disabled]="fieldConfig?.manualApprover?.is_disabled ? true : false"
                  (selectionChange)="onManualHeaderApproverSelection($event)"
                  [placeholder]="fieldConfig?.manualApprover?.field_label"
                  [panelStyle]="headerApproverSelect ? { 'max-height': '200px', 'overflow-y': 'auto' } : null">
                  <mat-option *ngFor="let entity of approversList" [value]="entity?.oid"
                    [matTooltip]="entity?.name + ' - ' + entity?.role">
                    {{entity?.name}} - {{entity?.role}}<span>

                    </span>
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </div>
          </div>
          <div *ngIf="isManualApproverSelectionApplicable == true && isHeaderLevelApprover == true"
            class="col-1 pt-4 d-flex">
            <ng-container *ngIf="claimForm.value.headerLevelApprover != ''">
              <!-- <p class="mr-3 header my-auto">Approvers</p> -->
              <ng-container *ngFor="let approverItem of claimForm.value.headerLevelApprover">
                <app-user-image class="font-family-class" [tooltip]="approverTooltip" content-type="template"
                  placement="top" style="margin: 2px;" [id]="approverItem.oid" imgHeight="32px" imgWidth="32px"
                  borderStyle="solid" borderWidth="2px">
                </app-user-image>
                <ng-template #approverTooltip>
                  <div class="row tooltip-text">
                    {{ approverItem.name }}
                  </div>
                  <div class="row tooltip-text">
                    {{ approverItem.designation }}
                  </div>
                  <div class="row tooltip-text">
                    Level {{ approverItem.level }}
                  </div>
                </ng-template>
              </ng-container>
            </ng-container>
          </div>

          <!-- Header level description -->
          <div *ngIf="(fieldConfig?.headerDescription?.is_visible ? fieldConfig?.headerDescription?.is_visible : 0)"
            class="col-4">
            <div class="label pl-2">{{fieldConfig?.headerDescription?.field_label ?
              fieldConfig.headerDescription.field_label:'Description'}}
              <span *ngIf="fieldConfig?.headerDescription?.is_mandatory" style="color: #cf0001;">*</span>

              <ng-template
                *ngIf="claimForm.get('headerDescription')?.hasError('required') && claimForm.get('headerDescription')?.touched"
                [ngTemplateOutlet]="requiredTemplate">
              </ng-template>
            </div>
            <mat-form-field appearance="outline" class="create-claim-field" style="width: 100%" #myFormField [ngClass]="{
                    'disabled-field': fieldConfig?.headerDescription?.is_disabled == 1,
                     'app-input-search-field-highlight': 
                claimForm.get('headerDescription')?.hasError('required') && 
                claimForm.get('headerDescription')?.touched
                  }">
              <!-- <mat-label>{{fieldConfig?.headerDescription?.field_label ? fieldConfig.headerDescription.field_label:'Description'}}</mat-label> -->
              <input matInput
                [placeholder]="fieldConfig?.headerDescription?.field_label ? fieldConfig.headerDescription.field_label:'headerDescription'"
                formControlName="headerDescription"
                [required]="fieldConfig?.headerDescription?.is_mandatory ? true : false"
                [readonly]="fieldConfig?.headerDescription?.is_disabled ? true : false" />

              <mat-hint *ngIf="claimForm.get('headerDescription')?.value?.length > 255">
                Maximum length is 255 characters
              </mat-hint>

            </mat-form-field>
          </div>

        </div>
        <div class="row border-bottom pb-2">

          <div
            *ngIf="costCenterType == 'I' && (fieldConfig?.customerBilling?.is_visible ? fieldConfig?.customerBilling?.is_visible : 0)"
            class="col-4">
            <div class="label pl-2">
              {{ fieldConfig?.customerBilling?.field_label ? fieldConfig.customerBilling.field_label : 'Customer Billing
              *' }}
              <span *ngIf="fieldConfig?.customerBilling?.is_mandatory" style="color: #cf0001;">*</span>
              <ng-template *ngIf="(claimForm.get('customerBilling')?.hasError('required') && claimForm.get('customerBilling')?.touched) || 
          (costCenterType == 'I' && claimForm.get('customerBilling').value == 3)"
                [ngTemplateOutlet]="requiredTemplate"></ng-template>
            </div>
            <div [ngClass]="{'app-input-search-field-highlight': (claimForm.get('customerBilling')?.hasError('required') && claimForm.get('customerBilling')?.touched) || 
        (costCenterType == 'I' && claimForm.get('customerBilling').value == 3)}">

              <mat-form-field appearance="outline" style="width:100%" *ngIf="billingConfig" #myFormField [ngClass]="{
          'disabled-field': fieldConfig?.customerBilling?.is_disabled == 1 }">
                <mat-select formControlName="customerBilling"
                  [disabled]="fieldConfig?.customerBilling?.is_disabled ? true : false"
                  [required]="fieldConfig?.customerBilling?.is_mandatory ? true : false"
                  [placeholder]="fieldConfig?.customerBilling?.field_label">
                  <mat-option *ngFor="let entity of billingConfig" [value]="entity.id">{{ entity.billing_type
                    }}</mat-option>
                </mat-select>
              </mat-form-field>
            </div>
          </div>

            <div  *ngIf="(fieldConfig?.spentFrom?.is_visible ? fieldConfig?.spentFrom?.is_visible : 0) && isCorporateCardApplicable"
            class="col-4">
              <div class="label pl-2"> 
                {{fieldConfig?.spentFrom?.field_label ? fieldConfig.spentFrom.field_label:'Spent From'}}
                <span *ngIf="fieldConfig?.spentFrom?.is_mandatory">*</span>
                <ng-template
                  *ngIf="claimForm.get('spentFrom')?.hasError('required') && claimForm.get('spentFrom')?.touched"
                  [ngTemplateOutlet]="requiredTemplate">
                </ng-template>
              </div>
            <app-exp-input-search class="create-claim-field-inputsearch font-family-class" 
            [ngClass]="{'app-input-search-field-highlight': claimForm.get('spentFrom')?.hasError('required') && claimForm.get('spentFrom')?.touched}" 
            [placeholder]="'Spent From'" 
            [list]="SpentFromList" 
            formControlName="spentFrom" 
            (change)='changeSpentFrom($event)'
            [disabled]="fieldConfig?.spentFrom?.is_disabled ? true : false"
            [bgColorOnDisable]="fieldConfig?.spentFrom?.is_disabled ? true : false" 
            [hideMatLabel]="true">
              </app-exp-input-search>
            </div>


            <div    *ngIf="(fieldConfig?.corporateCard?.is_visible ? fieldConfig?.corporateCard?.is_visible : 0) && isCorporateCardApplicable" class="col-4">
              <div class="label pl-2">{{fieldConfig?.corporateCard?.field_label ? fieldConfig.corporateCard.field_label:'Corporate Card'}}
                <span *ngIf="(fieldConfig?.corporateCard?.is_mandatory && corporateCardList?.length)">*</span>

                <mat-icon class="info-icon" tooltip="This field will be enabled based on the 'Spent From' selection." content-type="html">
                  info_outline
                </mat-icon>     
                <ng-template
                  *ngIf="claimForm.get('corporateCard')?.hasError('required') && claimForm.get('corporateCard')?.touched"
                  [ngTemplateOutlet]="requiredTemplate">
                </ng-template>
              </div>
              <app-exp-input-search class="create-claim-field-inputsearch font-family-class" 
              [ngClass]="{'app-input-search-field-highlight': claimForm.get('corporateCard')?.hasError('required') && claimForm.get('corporateCard')?.touched}" 
              [placeholder]="'Select Corporate Card '" 
              [list]="corporateCardList" 
              formControlName="corporateCard"
              [disabled]="fieldConfig?.corporateCard?.is_disabled || !corporateCardList?.length"
              [bgColorOnDisable]="fieldConfig?.corporateCard?.is_disabled || !corporateCardList?.length"            
              [hideMatLabel]="true">
              </app-exp-input-search>
            </div>

        </div>


        <ng-template #requiredTemplate>
          <span style="color: #cf0001; font-size: 8px; padding-left: 1%;" matTooltip="This field is required">This field
            is required</span>
        </ng-template>

        <ng-template #highlightTemplate>
          <span class="app-input-search-field-highlight-inside"> Highlites</span>
        </ng-template>


        <ng-template #billingDateErrorTemplate>
          <span style="color: #cf0001; font-size: 8px; padding-left: 1%;">
            Last {{restrictionBilledDate}} days, no future dates allowed
          </span>
        </ng-template>


        <ng-template #futureDateErrorTemplate>
          <span style="color: #cf0001; font-size: 8px; padding-left: 1%;"
          matTooltip="Future dates are not allowed.">
            Future dates are not allowed
          </span>
        </ng-template>

        
        <ng-template #invalidEndDateTemplate>
          <span style="color: #cf0001; font-size: 8px; padding-left: 1%;" matTooltip="End date and time cannot be earlier than the Start date and time.">
            End date and time cannot be earlier than the Start date and time.
          </span>
        </ng-template>
        

        <div class="row">
          <ng-container formArrayName="claims">
            <div class="scrollable-content">
              <div class="row" *ngFor="let item of claimFormData.claims['controls'];let i = index" [formGroupName]="i"
                style="border-bottom: 1px solid #d3d3d3; background: #F7F9FB;">
                <div class="row pt-2 pb-1" [ngClass]="{
                'duplicate-error': item.get('hasDuplicateError')?.value == true 
              }">
                  <div class="col p-0">
                    <div class="row">


                      <div class="col-12 p-0">
                        <div class="row">
                          <div class="col mt-1" style="color:#1F2347; font-weight: 600;">
                            {{claimForm.get('claims').at(i)?.get('categoryName')?.value}} Receipt Details
                          </div>
                          <div class="col-3 d-flex justify-content-end">
                            <div>
                              <button color="primary" class="remove-icon" mat-icon-button (click)="addClaim()">
                                <mat-icon style="color: #1F2347 !important">add_circle</mat-icon>
                              </button>
                              <button color="primary" class="remove-icon" mat-icon-button (click)="deleteClaim(i)">
                                <mat-icon style="color: #1F2347 !important">remove_circle</mat-icon>
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="row">
                      <div *ngIf="fieldConfig?.invoiceDate?.is_visible ? fieldConfig?.invoiceDate?.is_visible : 0"
                        class="col-4">
                        <div class="label pl-2">{{fieldConfig?.invoiceDate?.field_label ?
                          fieldConfig.invoiceDate.field_label:'Billed Date'}}
                          <span *ngIf="fieldConfig?.invoiceDate?.is_mandatory">*</span>
                          <ng-template *ngIf="claimForm.get('claims').at(i)?.get('invoiceDate').hasError('required') && 
                           claimForm.get('claims').at(i)?.get('invoiceDate').touched"
                            [ngTemplateOutlet]="requiredTemplate">
                          </ng-template>

                          <!-- Billing Date Error -->
                          <ng-template *ngIf="claimForm.get('claims').at(i)?.get('invoiceDate').hasError('invalidInvoiceDate') && 
                           claimForm.get('claims').at(i)?.get('invoiceDate').touched"
                            [ngTemplateOutlet]="billingDateErrorTemplate">
                          </ng-template>
                        </div>
                        <mat-form-field appearance="outline" class="create-claim-field" style="width: 100%" #myFormField
                          [ngClass]="{
                                'disabled-field': fieldConfig?.invoiceDate?.is_disabled == 1,
                                  'app-input-search-field-highlight': 
                                  claimForm.get('claims').at(i)?.get('invoiceDate')?.hasError('required') && 
                                  claimForm.get('claims').at(i)?.get('invoiceDate')?.touched
                              }">
                          <!-- <mat-label>{{fieldConfig?.invoiceDate?.field_label ? fieldConfig.invoiceDate.field_label:'Billed Date'}}</mat-label> -->
                          <input matInput [matDatepicker]="invoiceDate" [min]="billingStartDate" [max]="tomorrow"
                            [required]="fieldConfig?.invoiceDate?.is_mandatory ? true : false"
                            formControlName="invoiceDate" (dateChange)="onInvoiceDateChange($event,i)"
                            [disabled]="fieldConfig?.invoiceDate?.is_disabled ? true : false" />
                          <mat-datepicker-toggle matSuffix [for]="invoiceDate"></mat-datepicker-toggle>
                          <mat-datepicker #invoiceDate></mat-datepicker>
                        </mat-form-field>
                      </div>

                      <div *ngIf="fieldConfig?.invoiceNo?.is_visible ? fieldConfig?.invoiceNo?.is_visible : 0"
                        class="col-3">
                        <div class="label pl-2">{{fieldConfig?.invoiceNo?.field_label ?
                          fieldConfig.invoiceNo.field_label:'Invoice No'}}
                          <span *ngIf="fieldConfig?.invoiceNo?.is_mandatory">*</span>
                        </div>
                        <mat-form-field appearance="outline" style="width:100%" #myFormField [ngClass]="{
                      'disabled-field': fieldConfig?.invoiceNo?.is_disabled == 1
                      }">
                          <!-- <mat-label>{{fieldConfig?.invoiceNo?.field_label ? fieldConfig.invoiceNo.field_label:'Invoice No'}}</mat-label> -->
                          <input matInput formControlName="invoiceNo"
                            [placeholder]="fieldConfig?.invoiceNo?.field_label ? fieldConfig.invoiceNo.field_label:'invoiceNo *'"
                            [readonly]="fieldConfig?.invoiceNo?.is_disabled ? true : false"
                            [required]="fieldConfig?.invoiceNo?.is_mandatory ? true : false">
                        </mat-form-field>
                      </div>

                      <div *ngIf="fieldConfig?.description?.is_visible ? fieldConfig?.description?.is_visible : 0"
                        class="col-8">
                        <div class="label pl-2">{{fieldConfig?.description?.field_label ?
                          fieldConfig.description.field_label:'Description'}}
                          <span *ngIf="fieldConfig?.description?.is_mandatory">*</span>
                          <ng-template *ngIf="claimForm.get('claims').at(i)?.get('description').hasError('required') && 
                                    claimForm.get('claims').at(i)?.get('description').touched"
                            [ngTemplateOutlet]="requiredTemplate">
                          </ng-template>
                        </div>
                        <mat-form-field appearance="outline" class="create-claim-field" style="width: 100%" #myFormField
                          [ngClass]="{
                  'disabled-field': fieldConfig?.description?.is_disabled == 1,
                  'app-input-search-field-highlight': 
                  claimForm.get('claims').at(i)?.get('description')?.hasError('required') && 
                  claimForm.get('claims').at(i)?.get('description')?.touched
                }">
                          <!-- <mat-label>{{fieldConfig?.description?.field_label ? fieldConfig.description.field_label:'Description'}}</mat-label> -->
                          <input matInput
                            [placeholder]="fieldConfig?.description?.field_label ? fieldConfig.description.field_label:'Description'"
                            formControlName="description"
                            [required]="fieldConfig?.description?.is_mandatory ? true : false"
                            [readonly]="fieldConfig?.description?.is_disabled ? true : false" />
                        </mat-form-field>
                      </div>
                      <div *ngIf="fieldConfig?.category?.is_visible ? fieldConfig?.category?.is_visible : 0"
                        class="col-4">
                        <div class="label pl-2">Claim Type
                          <span *ngIf="fieldConfig?.category?.is_mandatory">*</span>
                          <ng-template *ngIf="claimForm.get('claims').at(i)?.get('category').hasError('required') && 
                                    claimForm.get('claims').at(i)?.get('category').touched"
                            [ngTemplateOutlet]="requiredTemplate">
                          </ng-template>
                        </div>
                        <app-exp-input-search class="create-claim-field-inputsearch" [ngClass]="{
                  'app-input-search-field-highlight': 
                  claimForm.get('claims').at(i)?.get('category')?.hasError('required') && 
                  claimForm.get('claims').at(i)?.get('category')?.touched
                }" required="true" placeholder="Claim Type" [list]="claimType" formControlName="category"
                          (change)='changeClaimType($event,i)'
                          [disabled]="fieldConfig?.category?.is_disabled ? true : false"
                          [bgColorOnDisable]="fieldConfig?.category?.is_disabled ? true : false" [hideMatLabel]="true">
                        </app-exp-input-search>
                      </div>
                      <div class="col-3"
                        *ngIf="isManualApproverSelectionApplicable == true && isHeaderLevelApprover == false">
                        <div class="label pl-2">{{fieldConfig?.manualApprover?.field_label ?
                          fieldConfig.manualApprover.field_label:'Select Approver *'}}
                          <mat-icon class="info-icon" tooltip="Select cost centre so that you can select approvers"
                            content-type="html">
                            info_outline
                          </mat-icon>
                        </div>
                        <mat-form-field appearance="outline" style="width:100%" #myFormField [ngClass]="{
                  'disabled-field': !approversList?.length
                }">

                          <!-- <mat-label>{{fieldConfig?.manualApprover?.field_label ? fieldConfig.manualApprover.field_label:'Select Approver *'}}</mat-label> -->
                          <mat-select formControlName="approverMenu"
                            [disabled]="fieldConfig?.manualApprover?.is_disabled ? true : false"
                            [required]="fieldConfig?.manualApprover?.is_mandatory ? true : false"
                            [placeholder]="fieldConfig?.manualApprover?.field_label">
                            <mat-option *ngFor="let entity of approversList" [value]=entity.oid
                              (click)="onManualApproverSelection(entity,i)">
                              {{entity.manager_type}} - {{entity.name}} - <span>
                                <!-- <app-user-image class="font-family-class" [id]="entity.oid" imgHeight="32px" imgWidth="32px"
                        borderStyle="solid" borderWidth="2px">
                      </app-user-image> -->
                              </span>
                            </mat-option>
                          </mat-select>
                        </mat-form-field>
                      </div>
                      <div *ngIf="isManualApproverSelectionApplicable == true && isHeaderLevelApprover == false"
                        class="col-1 pt-4 d-flex">
                        <ng-container *ngIf="item.approvers != ''">
                          <!-- <p class="mr-3 header my-auto">Approvers</p> -->
                          <ng-container *ngFor="let approverItem of claimForm.value.claims[i].approvers">
                            <app-user-image class="font-family-class" [tooltip]="approverTooltip"
                              content-type="template" placement="top" style="margin: 2px;" [id]="approverItem.oid"
                              imgHeight="32px" imgWidth="32px" borderStyle="solid" borderWidth="2px">
                            </app-user-image>
                            <ng-template #approverTooltip>
                              <div class="row tooltip-text">
                                {{ approverItem.name }}
                              </div>
                              <div class="row tooltip-text">
                                {{ approverItem.designation }}
                              </div>
                              <div class="row tooltip-text">
                                Level {{ approverItem.level }}
                              </div>
                            </ng-template>
                          </ng-container>
                        </ng-container>
                      </div>
                      <div *ngIf="isManualApproverSelectionApplicable == false" class="col-3 d-flex">
                        <ng-container *ngIf="item.approvers != ''">
                          <p class="mr-3 header my-auto">Approvers</p>
                          <ng-container *ngFor="let approverItem of claimForm.value.claims[i].approvers">
                            <app-user-image class="font-family-class" [tooltip]="approverTooltip"
                              content-type="template" placement="top" style="margin: 2px;" [id]="approverItem.oid"
                              imgHeight="32px" imgWidth="32px" borderStyle="solid" borderWidth="2px">
                            </app-user-image>
                            <ng-template #approverTooltip>
                              <div class="row tooltip-text">
                                {{ approverItem.name }}
                              </div>
                              <div class="row tooltip-text">
                                {{ approverItem.designation }}
                              </div>
                              <div class="row tooltip-text">
                                Level {{ approverItem.level }}
                              </div>
                            </ng-template>
                          </ng-container>
                        </ng-container>
                      </div>

                      <div *ngIf="fieldConfig?.taxRatetype?.is_visible ? fieldConfig?.taxRatetype?.is_visible : 0"
                        class="col-4">
                        <div class="label pl-2">{{fieldConfig?.taxRatetype?.field_label ?
                          fieldConfig.taxRatetype.field_label:'taxRatetype'}}
                          <span *ngIf="fieldConfig?.taxRatetype?.is_mandatory">*</span>
                        </div>
                        <app-exp-input-search class="create-claim-field-inputsearch" required="false"
                          [placeholder]="fieldConfig?.taxRatetype?.field_label ? fieldConfig.taxRatetype.field_label:'taxRatetype'"
                          [list]="getTaxRateType" formControlName="taxRatetype" (change)="ontaxRatetypeChange(i)"
                          [disabled]="fieldConfig?.taxRatetype?.is_disabled ? true : false"
                          [bgColorOnDisable]="fieldConfig?.taxRatetype?.is_disabled ? true : false"
                          [hideMatLabel]="true">
                        </app-exp-input-search>
                      </div>
                      <div *ngIf="fieldConfig?.currency?.is_visible ? fieldConfig?.currency?.is_visible : 0"
                        class="col-4">
                        <div class="label pl-2">{{fieldConfig?.currency?.field_label ?
                          fieldConfig.currency.field_label:'currency'}}
                          <span *ngIf="fieldConfig?.currency?.is_mandatory">*</span>
                          <ng-template *ngIf="claimForm.get('claims').at(i)?.get('currencyCode').hasError('required') && 
                claimForm.get('claims').at(i)?.get('currencyCode').touched" [ngTemplateOutlet]="requiredTemplate">
                          </ng-template>
                        </div>
                        <app-exp-input-search class="create-claim-field-inputsearch" [ngClass]="{
                                    'app-input-search-field-highlight': 
                                    claimForm.get('claims').at(i)?.get('currencyCode')?.hasError('required') && 
                                    claimForm.get('claims').at(i)?.get('currencyCode')?.touched
                                  }" required="true"
                          [placeholder]="fieldConfig?.currency?.field_label ? fieldConfig.currency.field_label:'currency'"
                          [list]="this.currencyList" formControlName="currencyCode" (change)="itemCurrencyChange(i)"
                          [disabled]="fieldConfig?.currency?.is_disabled ? true : false"
                          [bgColorOnDisable]="fieldConfig?.currency?.is_disabled ? true : false" [hideMatLabel]="true">
                        </app-exp-input-search>
                      </div>

                      <div *ngIf="fieldConfig?.amount?.is_visible ? fieldConfig?.amount?.is_visible : 0" class="col-4">
                        <div class="label pl-2"
                          matTooltip="{{ fieldConfig?.amount?.field_label ? fieldConfig.amount.field_label : 'Amount *' }}">
                          {{
                          fieldConfig?.amount?.field_label
                          ? fieldConfig.amount.field_label
                          : 'Amount *'
                          }}
                          <span *ngIf="fieldConfig?.amount?.is_mandatory">*</span>
                          <ng-template *ngIf="claimForm.get('claims').at(i)?.get('amount').hasError('required') && 
                                      claimForm.get('claims').at(i)?.get('amount').touched"
                            [ngTemplateOutlet]="requiredTemplate">
                          </ng-template>
                        </div>
                        <mat-form-field appearance="outline" class="create-claim-field" style="width: 100%" [ngClass]="{
                                      'disabled-field': fieldConfig?.amount?.is_disabled == 1,
                                      'app-input-search-field-highlight': 
                                      claimForm.get('claims').at(i)?.get('amount')?.hasError('required') && 
                                      claimForm.get('claims').at(i)?.get('amount')?.touched
                                    }">
                          <!-- <mat-label>{{
                                        fieldConfig?.amount?.field_label
                                        ? fieldConfig.amount.field_label
                                        : 'Amount *'
                                        }}</mat-label> -->
                          <div class="d-flex">
                            <input matInput class="pr-3" [placeholder]="
                                          fieldConfig?.amount?.field_label
                                            ? fieldConfig.amount.field_label
                                            : 'Amount *'
                                        " formControlName="amount" (input)="itemAmountChange($event, i)"
                              (focusout)="onPaymentMadeChange(i)" [maxlength]="15"
                              [readonly]="fieldConfig?.amount?.is_disabled ? true : false" />
                            <span class="currency-indicator" *ngIf="claimForm.value.claims[i].currenyCodeValue">
                              {{ claimForm.value.claims[i].currenyCodeValue }}
                            </span>
                          </div>
                          <mat-hint class="mat-hint mt-1" *ngIf="
                                        claimForm.controls['claims']['controls'][i]['controls']['amount']
                                          .invalid &&
                                        (claimForm.controls['claims']['controls'][i]['controls']['amount']
                                          .touched ||
                                          claimForm.controls['claims']['controls'][i]['controls']['amount']
                                            .dirty)
                                      ">
                            <span *ngIf="
                                          !claimForm.controls['claims']['controls'][i]['controls']['amount']
                                            .errors.validAmount
                                        ">
                              Amount can't be 0, -ve
                            </span>
                            <span *ngIf="
                                          claimForm.controls['claims']['controls'][i]['controls']['amount']
                                            .errors.required
                                        ">
                              ,empty
                            </span>
                            <span
                              *ngIf="claimForm.controls['claims']['controls'][i]['controls']['amount'].errors?.maxDigitsExceeded">
                              , cannot exceed 15 digits before the decimal.
                            </span>
                          </mat-hint>
                        </mat-form-field>
                        <!-- <mat-form-field appearance="outline" class="create-claim-field" style="width: 100%" #myFormField [ngClass]="{
                                                  'disabled-field': fieldConfig?.amount?.is_disabled == 1
                                                }">
                                                  <mat-label>{{fieldConfig?.amount?.field_label ? fieldConfig.amount.field_label:'Amount *'}}</mat-label>
                                                  <div class="d-flex">
                                                    <input matInput type="number" [placeholder]="fieldConfig?.amount?.field_label ? fieldConfig.amount.field_label:'Amount *'" formControlName="amount" (input)=itemAmountChange($event,i)
                                                    [readonly]="fieldConfig?.amount?.is_disabled ? true : false"/>
                                                    <span class="currency-indicator" *ngIf="claimForm.value.claims[i].currenyCodeValue">{{claimForm.value.claims[i].currenyCodeValue}}</span>
                                                  </div>
                                                  <mat-hint class="mat-hint mt-1" *ngIf="claimForm.controls['claims']['controls'][i]['controls'][
                                                    'amount'
                                                  ].invalid && (claimForm.controls['claims']['controls'][i]['controls'][
                                                  'amount'
                                                ].touched|| claimForm.controls['claims']['controls'][i]['controls'][
                                                'amount'
                                              ].dirty)">
                                                    <span *ngIf="!claimForm.controls['claims']['controls'][i]['controls'][
                                                      'amount'
                                                    ].errors.validAmount">
                                                      Amount can't be 0, -ve
                                                    </span>
                                                    <span *ngIf="claimForm.controls['claims']['controls'][i]['controls'][
                                                      'amount'
                                                    ].errors.required">
                                                      ,empty
                                                    </span>
                                                  </mat-hint>
                                                </mat-form-field> -->
                      </div>
                      <div *ngIf="fieldConfig?.taxPercentage?.is_visible ? fieldConfig?.taxPercentage?.is_visible : 0"
                        class="col-4">
                        <div class="label pl-2">{{fieldConfig?.taxPercentage?.field_label ?
                          fieldConfig.taxPercentage.field_label:'Tax Percentage'}}
                          <span *ngIf="fieldConfig?.taxPercentage?.is_mandatory">*</span>
                          <ng-template *ngIf="claimForm.get('claims').at(i)?.get('taxPercentage').hasError('required') && 
                claimForm.get('claims').at(i)?.get('taxPercentage').touched" [ngTemplateOutlet]="requiredTemplate">
                          </ng-template>
                        </div>
                        <app-exp-input-search class="create-claim-field-inputsearch font-family-class" [ngClass]="{
                'app-input-search-field-highlight': 
                claimForm.get('claims').at(i)?.get('taxPercentage')?.hasError('required') && 
                claimForm.get('claims').at(i)?.get('taxPercentage')?.touched
              }" [placeholder]="fieldConfig?.taxPercentage?.field_label ? fieldConfig.taxPercentage.field_label:'Tax Percentage'"
                          [list]="defaulttaxPercentage" formControlName="taxPercentage"
                          (change)="ontaxPercentageChange(i); updateTaxAmountValidator(i)"
                          [disabled]="fieldConfig?.taxPercentage?.is_disabled ? true : false"
                          [bgColorOnDisable]="fieldConfig?.taxPercentage?.is_disabled ? true : false"
                          [hideMatLabel]="true">
                        </app-exp-input-search>
                      </div>
                      <div *ngIf="fieldConfig?.taxAmount?.is_visible ? fieldConfig?.taxAmount?.is_visible : 0"
                        class="col-4">
                        <div class="label pl-2">{{fieldConfig?.taxAmount?.field_label?
                          fieldConfig.taxAmount.field_label:'taxAmount'}}
                          <span *ngIf="fieldConfig?.taxAmount?.is_mandatory">*</span>
                          <ng-template *ngIf="claimForm.get('claims').at(i)?.get('taxAmount').hasError('required') && 
                          claimForm.get('claims').at(i)?.get('taxAmount').touched"
                            [ngTemplateOutlet]="requiredTemplate">
                          </ng-template>
                        </div>
                        <mat-form-field appearance="outline" class="create-claim-field" style="width: 100%" #myFormField
                          [ngClass]="{
                                        'disabled-field': claimForm.value.claims[i].taxPercentage != 1000,
                                        'app-input-search-field-highlight': 
                                        claimForm.get('claims').at(i)?.get('taxAmount')?.hasError('required') && 
                                        claimForm.get('claims').at(i)?.get('taxAmount')?.touched
                                      }">
                          <!-- <mat-label>{{fieldConfig?.taxAmount?.field_label? fieldConfig.taxAmount.field_label:'taxAmount'}}</mat-label> -->
                          <div class="d-flex">
                            <input matInput
                              [placeholder]="fieldConfig?.taxAmount?.field_label ? fieldConfig.taxAmount.field_label:'taxAmount'"
                              formControlName="taxAmount"
                              [readonly]="claimForm.value.claims[i].taxPercentage == 1000 ? false : true"
                              (focusout)="onTaxAmountFieldValueChange(i)">
                            <span class="currency-indicator"
                              *ngIf="claimForm.value.claims[i].currenyCodeValue">{{claimForm.value.claims[i].currenyCodeValue}}</span>
                          </div>
                          <mat-hint class="mat-hint mt-1" *ngIf="
                          claimForm.controls['claims']['controls'][i]['controls']['taxAmount']
                          .invalid &&
                          (claimForm.controls['claims']['controls'][i]['controls']['taxAmount']
                          .touched ||
                          claimForm.controls['claims']['controls'][i]['controls']['taxAmount']
                          .dirty)
                          ">
                            <span *ngIf="
                          !claimForm.controls['claims']['controls'][i]['controls']['taxAmount']
                          .errors.validAmount
                          ">
                              Amount can't be 0, -ve
                            </span>
                            <span *ngIf="
                              claimForm.controls['claims']['controls'][i]['controls']['taxAmount'].errors?.required &&
                              claimForm.value.claims[i].taxPercentage == 1000">
                              ,empty
                            </span>
                          </mat-hint>
                        </mat-form-field>
                      </div>

                          <!-- People Involved -->
                          <div class="col-4 d-flex" style="width:100%">
                            <div
                              *ngIf="fieldConfig?.peopleInvolved?.is_visible ? fieldConfig?.peopleInvolved?.is_visible : 0">
                              <!-- <kebs-mul-sel-search label="People Involved" [token]="token" [optionLabel]="['displayName','associate_id']"
                              (selectedValues)="getPeopleInvolved($event,i)"  [API_URL]="employeeSearchUrl">
                            </kebs-mul-sel-search> -->
                              <div class="label pl-2">{{fieldConfig?.peopleInvolved?.field_label ?
                                fieldConfig.peopleInvolved.field_label:'People Involved'}}
                                <span *ngIf="fieldConfig?.peopleInvolved?.is_mandatory">*</span>
                              </div>
                              <app-input-search-people-involved-dropdown class="font-family-class" [token]="token"
                                [optionLabel]="['displayName','associate_id']" style="width:100%"
                                (selectedValues)="getPeopleInvolved($event,i)" [API_URL]="employeeSearchUrl"
                                [fieldValue]="claimForm.controls['claims']['controls'][i]['controls']['peopleInvolved'].value"
                                [disabled]="fieldConfig?.peopleInvolved?.is_disabled ? true : false"
                                [bgColorOnDisable]="fieldConfig?.peopleInvolved?.is_disabled ? true : false"
                                [placeholder]="fieldConfig?.peopleInvolved?.field_label ? fieldConfig.peopleInvolved.field_label:'People Involved'"></app-input-search-people-involved-dropdown>
                            </div>
    
                            <div content-type="template" max-width="300" placement="top" class="people-display">
                              <div style="margin-top: 9px; margin-left: 10px;"
                                *ngIf="claimForm.controls['claims']['controls'][i].controls['peopleInvolved'].value && claimForm.controls['claims']['controls'][i].controls['peopleInvolved'].value?.length >= 3">
                                <div class="row mt-4">
                                  <div
                                    *ngFor="let emp of claimForm.controls['claims']['controls'][i].controls['peopleInvolved'].value.slice(0, 2); let empind = index"
                                    style="cursor: pointer" class="pl-1"
                                    (click)="openExpensePeopleInvolved(claimForm.controls['claims']['controls'][i].controls['peopleInvolved'].value)">
                                    <div class="row image-circle">
                                      <div>
                                        <img class="first-div" src="https://assets.kebs.app/images/User.png"
                                          [style.width.px]="width" [style.height.px]="height" alt="profile"
                                          matTooltip="{{emp.displayName}}">
                                      </div>
                                    </div>
                                  </div>
                                  <div class="third-div people-involved-counter">
                                    +{{ claimForm.controls['claims']['controls'][i].controls['peopleInvolved'].value.length
                                    - 2 }}
                                  </div>
                                </div>
                              </div>
    
                              <div
                                *ngIf="claimForm.controls['claims']['controls'][i].controls['peopleInvolved'].value?.length < 3">
                                <div class="row mt-4" style="margin-top: 2px;">
                                  <div
                                    *ngFor="let emp of claimForm.controls['claims']['controls'][i].controls['peopleInvolved'].value?.slice(0, 2); let empind = index"
                                    style="cursor: pointer" class="pl-1">
                                    <div class="row image-circle">
                                      <div>
                                        <img class="first-div" src="https://assets.kebs.app/images/User.png"
                                          [style.width.px]="width" [style.height.px]="height" alt="profile"
                                          matTooltip="{{emp.displayName}}">
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
    
    
                            </div>
                          </div>
                  
                      <!-- Perdiem Start Date -->
                      <div
                        *ngIf="claimForm.get('claims').at(i)?.get('claimTypeId')?.value == 8 && fieldConfig?.perDiemstartDate?.is_visible ? fieldConfig?.perDiemstartDate?.is_visible : 0"
                        class="col-2">
                        <div class="label pl-2">
                          {{ fieldConfig?.perDiemstartDate?.field_label ? fieldConfig.perDiemstartDate.field_label :
                          'Start Date' }}
                          <span *ngIf="fieldConfig?.perDiemstartDate?.is_mandatory">*</span>

                          <ng-template *ngIf="claimForm.get('claims').at(i)?.get('perDiemstartDate')?.hasError('required') && 
                                              claimForm.get('claims').at(i)?.get('perDiemstartDate')?.touched"
                            [ngTemplateOutlet]="requiredTemplate">
                          </ng-template>

                          <!-- Billing Date Error -->
                          <ng-template *ngIf="claimForm.get('claims').at(i)?.get('perDiemstartDate')?.hasError('invalidInvoiceDate') && 
                                              claimForm.get('claims').at(i)?.get('perDiemstartDate')?.touched"
                                              [ngTemplateOutlet]="futureDateErrorTemplate">
                          </ng-template>
                        </div>

                        <mat-form-field appearance="outline" class="create-claim-field" style="width: 100%" #myFormField
                          [ngClass]="{
                                'disabled-field': fieldConfig?.perDiemstartDate?.is_disabled == 1,
                                'app-input-search-field-highlight': 
                                  claimForm.get('claims').at(i)?.get('perDiemstartDate')?.hasError('required') && 
                                  claimForm.get('claims').at(i)?.get('perDiemstartDate')?.touched
                              }">

                          <input matInput [matDatepicker]="perDiemstartDate"
                          placeholder="{{ fieldConfig?.perDiemstartDate?.field_label ? fieldConfig.perDiemstartDate.field_label :
                            'Start Date' }}" 
                          [max]="tomorrow"
                            [required]="fieldConfig?.perDiemstartDate?.is_mandatory ? true : false"
                            [readonly]="fieldConfig?.perDiemstartDate?.is_disabled ? true : false"
                            formControlName="perDiemstartDate"
                            [disabled]="fieldConfig?.perDiemstartDate?.is_disabled ? true : false"
                            (dateChange)="calculateNoOfDays(i); validateEndDateTime(i)"  />

                          <mat-datepicker-toggle matSuffix [for]="perDiemstartDate"
                            style="color: #B9C0CA;  position: relative; top: 4px;"></mat-datepicker-toggle>
                          <mat-datepicker #perDiemstartDate></mat-datepicker>
                        </mat-form-field>
                      </div>

                      <!-- Perdiem Start Time -->
                      <div
                        *ngIf="claimForm.get('claims').at(i)?.get('claimTypeId')?.value == 8 && fieldConfig?.perDiemStartTime?.is_visible ? fieldConfig?.perDiemStartTime?.is_visible : 0"
                        class="col-2">
                        <div class="label pl-2">
                          {{ fieldConfig?.perDiemStartTime?.field_label ? fieldConfig.perDiemStartTime.field_label :
                          'Start Time' }}
                          <span *ngIf="fieldConfig?.perDiemStartTime?.is_mandatory">*</span>
                        </div>

                        <mat-form-field appearance="outline" class="create-claim-field" style="width: 100%" #myFormField
                          [ngClass]="{
                                              'disabled-field': fieldConfig?.perDiemStartTime?.is_disabled == 1,
                                              'app-input-search-field-highlight': 
                                              claimForm.get('claims').at(i)?.get('perDiemStartTime')?.hasError('required') && 
                                              claimForm.get('claims').at(i)?.get('perDiemStartTime')?.touched}">
                          <input matInput type="text" placeholder="HH:MM" maxlength="5"
                            formControlName="perDiemStartTime" [required]="true" [readonly]="false"
                            (keydown)="onKeyDownPreventInvalidChars($event)" (paste)="preventPaste($event)"
                            (keyup)="onInputChangeFormatTime($event, i)"
                            (change)="calculateNoOfDays(i); validateEndDateTime(i)" 
                             #perDiemStartTimeInput />
                          <mat-icon matSuffix style="color: #B9C0CA;">alarm</mat-icon>
                        </mat-form-field>

                      </div>

                      <!-- Perdiem End Date -->
                      <div
                      *ngIf="claimForm.get('claims').at(i)?.get('claimTypeId')?.value == 8 && fieldConfig?.perDiemEndDate?.is_visible ? fieldConfig?.perDiemEndDate?.is_visible : 0"
                      class="col-2">
                      <div class="label pl-2">
                        {{ fieldConfig?.perDiemEndDate?.field_label ? fieldConfig.perDiemEndDate.field_label :
                        'Start Date' }}
                        <span *ngIf="fieldConfig?.perDiemEndDate?.is_mandatory">*</span>

                        <ng-template *ngIf="claimForm.get('claims').at(i)?.get('perDiemEndDate')?.hasError('required') && 
                                            claimForm.get('claims').at(i)?.get('perDiemEndDate')?.touched"
                          [ngTemplateOutlet]="requiredTemplate">
                        </ng-template>

                        <!-- Billing Date Error -->
                        <ng-template *ngIf="claimForm.get('claims').at(i)?.get('perDiemEndDate')?.hasError('invalidInvoiceDate') && 
                                            claimForm.get('claims').at(i)?.get('perDiemEndDate')?.touched"
                                            [ngTemplateOutlet]="futureDateErrorTemplate">
                        </ng-template>
                      </div>

                      <mat-form-field appearance="outline" class="create-claim-field" style="width: 100%" #myFormField
                        [ngClass]="{
                              'disabled-field': fieldConfig?.perDiemEndDate?.is_disabled == 1,
                              'app-input-search-field-highlight': 
                                claimForm.get('claims').at(i)?.get('perDiemEndDate')?.hasError('required') && 
                                claimForm.get('claims').at(i)?.get('perDiemEndDate')?.touched
                            }">

                        <input matInput [matDatepicker]="perDiemEndDate"
                        placeholder="{{ fieldConfig?.perDiemEndDate?.field_label ? fieldConfig.perDiemEndDate.field_label :
                          'End Date' }}" 
                        [max]="tomorrow"
                          [required]="fieldConfig?.perDiemEndDate?.is_mandatory ? true : false"
                          [readonly]="fieldConfig?.perDiemEndDate?.is_disabled ? true : false"
                          formControlName="perDiemEndDate"
                          [disabled]="fieldConfig?.perDiemEndDate?.is_disabled ? true : false"
                          (dateChange)="calculateNoOfDays(i); validateEndDateTime(i)"  />

                        <mat-datepicker-toggle matSuffix [for]="perDiemEndDate"
                          style="color: #B9C0CA;  position: relative; top: 4px;"></mat-datepicker-toggle>
                        <mat-datepicker #perDiemEndDate></mat-datepicker>
                      </mat-form-field>
                    </div>

                      <!-- Perdiem End Time -->
                      <div
                        *ngIf="claimForm.get('claims').at(i)?.get('claimTypeId')?.value == 8 && fieldConfig?.perDiemEndTime?.is_visible ? fieldConfig?.perDiemEndTime?.is_visible : 0"
                        class="col-2">
                        <div class="label pl-2">
                          {{ fieldConfig?.perDiemEndTime?.field_label ? fieldConfig.perDiemEndTime.field_label : 'End
                          Time' }}
                          <span *ngIf="fieldConfig?.perDiemEndTime?.is_mandatory">*</span>

                          <ng-template *ngIf="claimForm.get('claims').at(i)?.get('perDiemEndDate')?.hasError('invalidEndDateTime') && 
                                                    claimForm.get('claims').at(i)?.get('perDiemEndDate')?.touched"
                            [ngTemplateOutlet]="invalidEndDateTemplate">
                          </ng-template>

                        </div>

                        <mat-form-field appearance="outline" class="create-claim-field" style="width: 100%" #myFormField
                          [ngClass]="{
                                            'disabled-field': fieldConfig?.perDiemEndTime?.is_disabled == 1,
                                            'app-input-search-field-highlight': 
                                            claimForm.get('claims').at(i)?.get('perDiemEndTime')?.hasError('required') && 
                                            claimForm.get('claims').at(i)?.get('perDiemEndTime')?.touched}">
                          <input matInput type="text" placeholder="HH:MM" maxlength="5" formControlName="perDiemEndTime"
                            [required]="true" [readonly]="false" (keydown)="onKeyDownPreventInvalidChars($event)"
                            (paste)="preventPaste($event)" (keyup)="onInputChangeFormatEndTime($event, i)"
                            (change)="calculateNoOfDays(i); validateEndDateTime(i)"
                            #perDiemEndTimeInput />
                          <mat-icon matSuffix style="color: #B9C0CA;">alarm</mat-icon>
                        </mat-form-field>
                      </div>

                      <!-- Perdiem Days -->
                      <div
                        *ngIf="claimForm.get('claims').at(i)?.get('claimTypeId')?.value == 8 && fieldConfig?.days?.is_visible ? fieldConfig?.days?.is_visible : 0"
                        class="col-4">
                        <div class="label pl-2">
                          {{ fieldConfig?.days?.field_label ? fieldConfig.days.field_label : 'Total Days' }}
                          <span *ngIf="fieldConfig?.days?.is_mandatory">*</span>
                        </div>
                        <span class="perdiem-total-days">{{ claimForm.get('claims').at(i)?.get('noOfDays')?.value }} DAY(S)</span>
                      </div>

                          <!-- Perdiem Location -->
                          <div
                          *ngIf="claimForm.get('claims').at(i)?.get('claimTypeId')?.value == 8 && fieldConfig?.location?.is_visible ? fieldConfig?.location?.is_visible : 0"
                          class="col-4">
                          <div class="label pl-2">
                            {{fieldConfig?.location?.field_label?fieldConfig.location.field_label:'Location'}}
                            <span *ngIf="fieldConfig?.location?.is_mandatory">*</span>
                            <ng-template *ngIf="claimForm.get('claims').at(i)?.get('location')?.hasError('required') &&
                                                claimForm.get('claims').at(i)?.get('location')?.touched"
                              [ngTemplateOutlet]="requiredTemplate">
                            </ng-template>
                          </div>
  
                          <mat-form-field appearance="outline" class="create-claim-field" style="width: 100%" #myFormField
                            [ngClass]="{
                              'disabled-field': fieldConfig?.location?.is_disabled == 1,
                              'app-input-search-field-highlight': 
                              claimForm.get('claims').at(i)?.get('location')?.hasError('required') && 
                              claimForm.get('claims').at(i)?.get('location')?.touched
                            }">
  
                            <input matInput placeholder="Enter Location" formControlName="location"
                              [required]="fieldConfig?.location.is_mandatory ? true : false"
                              [readonly]="fieldConfig?.description?.is_disabled ? true : false" />
  
                            <mat-icon matSuffix style="color: #B9C0CA;">location_on</mat-icon>
                            <mat-hint *ngIf="claimForm.get('claims').at(i)?.get('location')?.value?.length > 100">
                              Maximum length is 100 characters
                            </mat-hint>
                          </mat-form-field>
                        </div>
  
                      <div *ngIf="fieldConfig?.startDate?.is_visible ? fieldConfig?.startDate?.is_visible : 0"
                        class="col-4 d-flex">
                        <div class="label pl-2">{{fieldConfig?.startDate?.field_label ?
                          fieldConfig.startDate.field_label:'Expense Duration *'}}</div>
                        <mat-form-field appearance="outline" style="width: 100%;" #myFormField [ngClass]="{
                      'disabled-field': fieldConfig?.startDate?.is_disabled === 1
                      }">
                          <!-- <mat-label>{{fieldConfig?.startDate?.field_label ? fieldConfig.startDate.field_label:'Expense Duration *'}}</mat-label> -->
                          <mat-date-range-input [rangePicker]="picker" [min]="billingStartDate"
                            [disabled]="fieldConfig?.startDate?.is_disabled ? true : false">
                            <input matStartDate formControlName="startDate" placeholder="Start date"
                              [readonly]="fieldConfig?.startDate?.is_disabled ? true : false">
                            <input matEndDate formControlName="endDate" placeholder="End date"
                              [readonly]="fieldConfig?.endDate?.is_disabled ? true : false">
                          </mat-date-range-input>
                          <mat-datepicker-toggle matSuffix [for]="picker"
                            [disabled]="fieldConfig?.startDate?.is_disabled ? true : false"></mat-datepicker-toggle>
                          <mat-date-range-picker #picker></mat-date-range-picker>
                        </mat-form-field>
                      </div>

                  

                      <!--Mileage - From Location -->
                      <div
                        *ngIf="claimForm.get('claims').at(i)?.get('claimTypeId')?.value == 18 && fieldConfig?.fromLocation?.is_visible ? fieldConfig?.fromLocation?.is_visible : 0"
                        class="col-6">
                        <div class="label pl-2">
                          {{ fieldConfig?.fromLocation?.field_label ? fieldConfig.fromLocation.field_label : 'From Location' }}
                          <span *ngIf="fieldConfig?.fromLocation?.is_mandatory">*</span>
                          <ng-template *ngIf="claimForm.get('claims').at(i)?.get('fromLocation')?.hasError('required') &&
                                              claimForm.get('claims').at(i)?.get('fromLocation')?.touched"
                            [ngTemplateOutlet]="requiredTemplate">
                          </ng-template>
                        </div>

                        <mat-form-field appearance="outline" class="create-claim-field" style="width: 100%" #myFormField
                          [ngClass]="{
                            'disabled-field': fieldConfig?.fromLocation?.is_disabled == 1,
                            'app-input-search-field-highlight': 
                            claimForm.get('claims').at(i)?.get('fromLocation')?.hasError('required') && 
                            claimForm.get('claims').at(i)?.get('fromLocation')?.touched
                          }">

                          <input matInput placeholder="Enter From Location" formControlName="fromLocation"
                            [required]="fieldConfig?.fromLocation.is_mandatory ? true : false"
                            [readonly]="fieldConfig?.fromLocation?.is_disabled ? true : false" />

                            <mat-hint *ngIf="claimForm.get('claims').at(i)?.get('fromLocation')?.value?.length > 100">
                              Maximum length is 100 characters
                            </mat-hint>

                          <mat-icon matSuffix style="color: #B9C0CA;">location_on</mat-icon>

                        </mat-form-field>
                      </div>

                      <!--Mileage - To Location -->
                      <div
                        *ngIf="claimForm.get('claims').at(i)?.get('claimTypeId')?.value == 18 && fieldConfig?.toLocation?.is_visible ? fieldConfig?.toLocation?.is_visible : 0"
                        class="col-6">
                        <div class="label pl-2">
                          {{ fieldConfig?.toLocation?.field_label ? fieldConfig.toLocation.field_label : 'To Location'
                          }}
                          <span *ngIf="fieldConfig?.toLocation?.is_mandatory">*</span>
                          <ng-template *ngIf="claimForm.get('claims').at(i)?.get('toLocation')?.hasError('required') &&
                                                claimForm.get('claims').at(i)?.get('toLocation')?.touched"
                            [ngTemplateOutlet]="requiredTemplate">
                          </ng-template>
                        </div>

                        <mat-form-field appearance="outline" class="create-claim-field" style="width: 100%" #myFormField
                          [ngClass]="{
                              'disabled-field': fieldConfig?.toLocation?.is_disabled == 1,
                              'app-input-search-field-highlight': 
                              claimForm.get('claims').at(i)?.get('toLocation')?.hasError('required') && 
                              claimForm.get('claims').at(i)?.get('toLocation')?.touched
                            }">

                          <input matInput placeholder="Enter To Location" formControlName="toLocation"
                            [required]="fieldConfig?.toLocation.is_mandatory ? true : false"
                            [readonly]="fieldConfig?.toLocation?.is_disabled ? true : false" />

                            <mat-hint *ngIf="claimForm.get('claims').at(i)?.get('toLocation')?.value?.length > 100">
                              Maximum length is 100 characters
                            </mat-hint>
                            
                          <mat-icon matSuffix style="color: #B9C0CA;">location_on</mat-icon>

                        </mat-form-field>
                      </div>

                      <!--Mileage - Vehicle Type -->
                      <div
                        *ngIf="claimForm.get('claims').at(i)?.get('claimTypeId')?.value == 18 && fieldConfig?.vehicleType?.is_visible ? fieldConfig?.vehicleType?.is_visible : 0"
                        class="col-4">
                        <div class="label pl-2">
                          {{fieldConfig?.vehicleType?.field_label ? fieldConfig.vehicleType.field_label:'Vehicle Type'}}
                          <span *ngIf="fieldConfig?.vehicleType?.is_mandatory">*</span>
                          <ng-template *ngIf="claimForm.get('claims').at(i)?.get('vehicleType')?.hasError('required') &&
                                              claimForm.get('claims').at(i)?.get('vehicleType')?.touched"
                            [ngTemplateOutlet]="requiredTemplate">
                          </ng-template>
                        </div>

                        <app-exp-input-search class="create-claim-field-inputsearch font-family-class" [ngClass]="{
                            'app-input-search-field-highlight': 
                            claimForm.get('claims').at(i)?.get('vehicleType')?.hasError('required') && 
                            claimForm.get('claims').at(i)?.get('vehicleType')?.touched
                          }" [placeholder]="'Select Vehicle Type'" [list]="vehicleTypeList"
                          (change)='changeVehicleType($event, i)'
                          formControlName="vehicleType"
                          [disabled]="fieldConfig?.vehicleType?.is_disabled ? true : false"
                          [bgColorOnDisable]="fieldConfig?.vehicleType?.is_disabled ? true : false"
                          [hideMatLabel]="true">
                        </app-exp-input-search>
                      </div>

                      <!--Mileage - Vehicle Engine Type -->
                      <div
                        *ngIf="claimForm.get('claims').at(i)?.get('claimTypeId')?.value == 18 && fieldConfig?.vehicleEngineType?.is_visible ? fieldConfig?.vehicleEngineType?.is_visible : 0"
                        class="col-4">
                        <div class="label pl-2">
                          {{fieldConfig?.vehicleEngineType?.field_label ?
                          fieldConfig.vehicleEngineType.field_label:'Vehicle Engine Type'}}
                          <span *ngIf="(fieldConfig?.vehicleEngineType?.is_mandatory && vehicleEngineTypeList?.length)">*</span>

                          <mat-icon class="info-icon" tooltip="This field will be enabled when a Vehicle Type is selected." content-type="html">
                            info_outline
                          </mat-icon>                          

                          <ng-template *ngIf="claimForm.get('claims').at(i)?.get('vehicleEngineType')?.hasError('required') &&
                                              claimForm.get('claims').at(i)?.get('vehicleEngineType')?.touched"
                            [ngTemplateOutlet]="requiredTemplate">
                          </ng-template>
                        </div>

                        <app-exp-input-search class="create-claim-field-inputsearch font-family-class" [ngClass]="{
                            'app-input-search-field-highlight': 
                            claimForm.get('claims').at(i)?.get('vehicleEngineType')?.hasError('required') && 
                            claimForm.get('claims').at(i)?.get('vehicleEngineType')?.touched
                          }" [placeholder]="'Select Vehicle engine Type & Size'" [list]="vehicleEngineTypeList"
                          formControlName="vehicleEngineType"
                          [disabled]="fieldConfig?.vehicleEngineType?.is_disabled || !vehicleEngineTypeList?.length"
                          [bgColorOnDisable]="fieldConfig?.vehicleEngineTypeList?.is_disabled || !vehicleEngineTypeList?.length"            
                          [hideMatLabel]="true">
                        </app-exp-input-search>
                      </div>

                      <!--Mileage - No of Miles/Kms -->
                      <div *ngIf="claimForm.get('claims').at(i)?.get('claimTypeId')?.value == 18 && fieldConfig?.miles?.is_visible"
     class="col-4">
  <div class="label pl-2">
    {{ fieldConfig?.miles?.field_label ? fieldConfig.miles.field_label : 'No of Miles/Kms' }}
    <span *ngIf="fieldConfig?.miles?.is_mandatory">*</span>

    <!-- Show error if either 'miles' or 'unit' is required and touched -->
    <ng-template *ngIf="(claimForm.get('claims').at(i)?.get('miles')?.hasError('required') && 
                         claimForm.get('claims').at(i)?.get('miles')?.touched) || 
                        (claimForm.get('claims').at(i)?.get('unit')?.hasError('required') &&
                         claimForm.get('claims').at(i)?.get('unit')?.touched)" 
                 [ngTemplateOutlet]="requiredTemplate">
    </ng-template>
  </div>

  <mat-form-field appearance="outline" class="create-claim-field" style="width: 100%" #myFormField 
                  [ngClass]="{
                    'disabled-field': fieldConfig?.miles?.is_disabled == 1,
                    'app-input-search-field-highlight': 
                      claimForm.get('claims').at(i)?.get('miles')?.hasError('required') && 
                      claimForm.get('claims').at(i)?.get('miles')?.touched ||
                      claimForm.get('claims').at(i)?.get('unit')?.hasError('required') && 
                      claimForm.get('claims').at(i)?.get('unit')?.touched
                  }">
    <div class="split-input">
      <!-- Number Input for Miles -->
      <input matInput type="number" placeholder="Enter Distance" formControlName="miles"
             [disabled]="fieldConfig?.miles?.is_disabled"
             (input)="onMilesInput(i)" (blur)="roundMiles(i)" />

      <!-- Vertical Line Divider -->
      <mat-divider vertical></mat-divider>

      <!-- Unit Dropdown -->
      <mat-select formControlName="unit" placeholder="Unit">
        <mat-option *ngFor="let unit of unitList" [value]="unit?.id" [matTooltip]="unit?.name">
          {{ unit?.name }}
        </mat-option>
      </mat-select>
    </div>

    <mat-hint>
      <!-- Show error if 'miles' is less than minimum required -->
      <span *ngIf="claimForm.get('claims').at(i)?.get('miles')?.hasError('min')">
        Distance must be greater than 0.
      </span>
      <!-- Show error if max digits exceeded -->
      <span *ngIf="claimForm.get('claims').at(i)?.get('miles')?.hasError('maxDigitsExceeded') || 
                   claimForm.get('claims').at(i)?.get('amount')?.hasError('validAmount')">
        Distance cannot exceed 15 digits before the decimal.
      </span>
      <!-- Show error if 'unit' is required -->
      <span *ngIf="claimForm.get('claims').at(i)?.get('unit')?.hasError('required') && 
                   claimForm.get('claims').at(i)?.get('unit')?.touched">
        Unit is required.
      </span>
    </mat-hint>
  </mat-form-field>
</div>



                    </div>

                    <div class="row" *ngIf="isToShowConversionDetails == true">
                      <div class="col-12 pt-2 pb-2" style="color:#1F2347; font-weight: 600;">
                        Exchange Rate Details
                      </div>
                      <div
                        *ngIf="fieldConfig?.fromToConverstionRate?.is_visible ? fieldConfig?.fromToConverstionRate?.is_visible : 0"
                        class="col-3 pt-2">
                        <mat-form-field appearance="outline" style="width:40%">
                          <span>1</span>
                          <span class="currency-indicator"
                            *ngIf="claimForm.value.legalEntityCurrency">{{claimForm.value.legalEntityCurrency}} </span>
                        </mat-form-field>
                        <mat-icon style="width:20%">arrow_forward</mat-icon>
                        <mat-form-field appearance="outline" style="width:40%">
                          <span>{{claimForm.value.claims[i].conversionRate}}</span>
                          <span class="currency-indicator"
                            *ngIf="claimForm.value.claims[i].currenyCodeValue">{{claimForm.value.claims[i].currenyCodeValue}}</span>
                        </mat-form-field>
                      </div>
                      <div *ngIf="fieldConfig?.conversionRate?.is_visible ? fieldConfig?.conversionRate?.is_visible : 0"
                        class="col-4 pt-2">
                        <div class="label pl-2">{{fieldConfig?.conversionRate?.field_label?
                          fieldConfig.conversionRate.field_label:'conversionRate'}}
                          <span *ngIf="fieldConfig?.conversionRate?.is_mandatory">*</span>
                          <ng-template
                            *ngIf="claimForm.get('claims').at(i)?.get('conversionRate')?.hasError('required') && claimForm.get('claims').at(i)?.get('conversionRate')?.touched"
                            [ngTemplateOutlet]="requiredTemplate">
                          </ng-template>
                        </div>
                        <mat-form-field appearance="outline" class="create-claim-field" style="width: 100%" #myFormField
                          [ngClass]="{
                'disabled-field': claimForm.value.claims[i].currenyCodeValue == claimForm.value.legalEntityCurrency,
                'app-input-search-field-highlight': 
                claimForm.get('claims').at(i)?.get('conversionRate')?.hasError('required') && 
                claimForm.get('claims').at(i)?.get('conversionRate')?.touched
              }">
                          <!-- <mat-label>{{fieldConfig?.conversionRate?.field_label? fieldConfig.conversionRate.field_label:'conversionRate'}}</mat-label> -->
                          <input matInput
                            [placeholder]="fieldConfig?.conversionRate?.field_label ? fieldConfig.conversionRate.field_label:'conversionRate'"
                            formControlName="conversionRate" (input)="onConversionRateInput($event, i)"
                            [readonly]="claimForm.value.claims[i].currenyCodeValue == claimForm.value.legalEntityCurrency"
                            (focusout)="onConversionRateChange(i)" />
                          <mat-hint class="mat-hint mt-1" *ngIf="
                            claimForm.controls['claims']['controls'][i]['controls']['conversionRate']
                            .invalid &&
                            (claimForm.controls['claims']['controls'][i]['controls']['conversionRate']
                            .touched ||
                            claimForm.controls['claims']['controls'][i]['controls']['conversionRate']
                            .dirty)
                            ">
                            <span *ngIf="
                            !claimForm.controls['claims']['controls'][i]['controls']['conversionRate']
                            .errors.validAmount
                            ">
                              Amount can't be 0, -ve
                            </span>
                            <span *ngIf="
                            claimForm.controls['claims']['controls'][i]['controls']['conversionRate']
                            .errors.required
                            ">
                              ,empty
                            </span>
                          </mat-hint>
                        </mat-form-field>
                      </div>
                      <div *ngIf="fieldConfig?.finalamount?.is_visible ? fieldConfig?.finalamount?.is_visible : 0"
                        class="col-4 pt-2">
                        <div class="label pl-2">{{fieldConfig?.finalamount?.field_label?
                          fieldConfig?.finalamount?.field_label:'finalamount'}}
                          <span *ngIf="fieldConfig?.finalamount?.is_mandatory">*</span>
                        </div>
                        <mat-form-field appearance="outline" class="create-claim-field" style="width: 100%" #myFormField
                          [ngClass]="{
                'disabled-field': fieldConfig?.finalamount?.is_disabled === 1
              }">
                          <!-- <mat-label>{{fieldConfig?.finalamount?.field_label? fieldConfig?.finalamount?.field_label:'finalamount'}}</mat-label> -->
                          <div class="d-flex">
                            <input matInput
                              [placeholder]="fieldConfig?.finalamount?.field_label ? fieldConfig?.finalamount?.field_label:'finalamount'"
                              formControlName="finalamount"
                              [readonly]="fieldConfig?.finalamount?.is_disabled ? true : false" />
                            <span class="currency-indicator"
                              *ngIf="claimForm.value.claims[i].currenyCodeValue">{{claimForm.value.claims[i].currenyCodeValue}}</span>
                          </div>
                        </mat-form-field>
                      </div>
                      <div *ngIf="fieldConfig?.settlement?.is_visible ? fieldConfig?.settlement?.is_visible : 0"
                        class="col-4 pt-2">
                        <div class="label pl-2">{{fieldConfig?.settlement?.field_label ?
                          fieldConfig?.settlement?.field_label: 'Settlement Amount'}}
                          <span *ngIf="fieldConfig?.settlement?.is_mandatory">*</span>
                        </div>
                        <mat-form-field appearance="outline" class=" create-claim-field" style="width: 100%"
                          #myFormField [ngClass]="{
                'disabled-field': fieldConfig?.settlement?.is_disabled === 1
              }">
                          <!-- <mat-label>{{fieldConfig?.settlement?.field_label ? fieldConfig?.settlement?.field_label: 'Settlement Amount'}}</mat-label> -->
                          <div class="d-flex">
                            <input matInput
                              [placeholder]="fieldConfig?.settlement?.field_label ? fieldConfig?.settlement?.field_label:'settlement'"
                              formControlName="settlement"
                              [readonly]="fieldConfig?.settlement?.is_disabled ? true : false" />
                            <span class="currency-indicator"
                              *ngIf="claimForm?.value?.legalEntityCurrency">{{claimForm.value.legalEntityCurrency}}</span>
                          </div>
                        </mat-form-field>
                      </div>

                    </div>
                  </div>

                  <div class="col-12">
                    <div class="row">
                      <div class="col p-0 pt-2">
                        <span *ngIf="item['controls']['hasDuplicateError'].value == true" class="pt-2">
                          <mat-icon style="color: #cf0001; font-size: 20px; vertical-align: middle;">
                            error_outline
                          </mat-icon>
                          <span class="pl-1"
                            style="color: #CF0001; font-weight: 700; font-size: 12px">{{item['controls']['duplicationError'].value}}</span>
                        </span>
                      </div>

                      <div *ngIf="isExpenseCreationAllowed" class="col-3 p-0 d-flex justify-content-end">
                        <div style="max-width: 200px; height: 80%">
                          <div class="d-flex" [ngClass]="{'invalid-attachment': claimForm.controls['claims']['controls'][i]['controls'][
                            'contextId'
                          ].errors?.required, 
                            'attachment-btn': !claimForm.controls['claims']['controls'][i]['controls'][
                            'contextId'
                          ].errors?.required}">
                            <div class="pt-2 pl-2 content-muliple"
                              [matTooltip]="getAttahmentAlertMessage(item.controls.category.value)">
                              {{getAttahmentAlertMessage(item.controls.category.value)}}</div>
                            <div mat-icon-button class="close-button ml-auto"
                              *ngIf="item?.controls?.contextId?.value || draftInfo==null || !(draftInfo.claims[i]) || (!(item?.controls?.contextId?.value) && !(item?.controls?.attachment?.value)); else attachmentOld ">
                              <app-expense-upload-btn style="color: black" [destinationBucket]="'kebs-expenses'"
                                [routingKey]="'expenses'" [contextId]=" item['controls']['contextId'].value"
                                [allowEdit]="true" (change)="changeInFiles($event,i)"></app-expense-upload-btn>
                            </div>
                            <ng-template #attachmentOld>
                              <button mat-icon-button class="close-button ml-auto">
                                <mat-icon style="font-size: 21px !important;"
                                  *ngIf="!item['controls']['uploadInProgress'].value && !item['controls']['attachment'].value[0]"
                                  matTooltip="Attach File" (click)="multiFileInput.click()">attachment
                                  <input #multiFileInput type="file" name="file" ng2FileSelect [uploader]="uploader"
                                    [accept]="allowedMimeType.toString()" (change)="onFileAdd(i)"
                                    style="display:none;" />
                                </mat-icon>
                              </button>

                              <mat-spinner *ngIf="item['controls']['uploadInProgress'].value" class="m-auto"
                                diameter="20">
                              </mat-spinner>
                              <span class="ml-2 my-auto file-name"
                                [matTooltip]="item['controls']['attachment'].value[0]?.fileName"
                                style="cursor: pointer;" (click)="openFile(i)">
                                {{item["controls"]["attachment"].value[0]?.fileName}}</span>

                              <mat-icon
                                *ngIf="item['controls']['attachment'].value[0]?.fileName != '' && item['controls']['attachment'].value[0]?.fileName != null"
                                matTooltip="Attach File" class="m-auto edit-icon" (click)="multiFileInput.click()">edit
                                <input #multiFileInput type="file" name="file" ng2FileSelect [uploader]="uploader"
                                  [accept]="allowedMimeType.toString()" (change)="onFileAdd(i)" style="display:none;" />
                              </mat-icon>
                            </ng-template>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

            </div>
          </ng-container>
        </div>

        <div class="row">
          <div class="col">
            <div class="col-8 pt-3 dialog dialog-info" *ngIf="isDialogOpen" (click)="closeMessage()">
              <span class="msg-text"><span class="icon">
                  <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px"
                    fill="#1890ff">
                    <path
                      d="M440-280h80v-240h-80v240Zm40-320q17 0 28.5-11.5T520-640q0-17-11.5-28.5T480-680q-17 0-28.5 11.5T440-640q0 17 11.5 28.5T480-600Zm0 520q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Zm0-80q134 0 227-93t93-227q0-134-93-227t-227-93q-134 0-227 93t-93 227q0 134 93 227t227 93Zm0-320Z" />
                  </svg>
                </span>
                {{ payment_terms_message }}

              </span>

            </div>
          </div>
          <div *ngIf="isExpenseCreationAllowed" class="col-2 p-0 pt-2 icon-postion">
            <button mat-icon-button class="iconbtn ml-3 mt-1 mb-1"
              [ngClass]="{'restict-cursor': isBeingDrafted || isBeingClaimed || isBeingWorkflowDetermined || restrictClaimCreation}"
              matTooltip="Save as Draft" type="submit" (click)="saveAsDraft()"
              [disabled]="isBeingDrafted || isBeingClaimed || isBeingWorkflowDetermined || isBeingCheckedForDuplicate"
              [style.background-color]="(isBeingDrafted || isBeingClaimed || isBeingWorkflowDetermined || isBeingCheckedForDuplicate || restrictClaimCreation) ? '#d3d3d3' : '#1F2347'">
              <mat-icon *ngIf="isBeingDrafted == false; else showDraftSpinner" style="color: white"> save</mat-icon>
              <ng-template #showDraftSpinner>
                <!-- <mat-spinner diameter='30' class='create-claim-styles btn-spinner' [style.background-color]="theme_color1"></mat-spinner> -->
              </ng-template>

            </button>
            <!-- [ngClass]="{'restict-cursor': isBeingDrafted || isBeingClaimed || isBeingWorkflowDetermined}"  -->

            <button mat-icon-button class="iconbtn ml-3 mt-1 mb-1" matTooltip="Create Claim" type="submit"
              (click)="saveExpense()" [disabled]="isBeingDrafted || isBeingClaimed || isBeingWorkflowDetermined || isBeingCheckedForDuplicate || restrictClaimCreation"
              [style.background-color]="(isBeingDrafted || isBeingClaimed || isBeingWorkflowDetermined || isBeingCheckedForDuplicate || restrictClaimCreation) ? '#d3d3d3' : theme_color1">
              <mat-icon *ngIf="isBeingDrafted == false; else showDraftSpinner" style="color: white"> done_all</mat-icon>
              <ng-template #showDraftSpinner>
                <!-- <mat-spinner diameter='30' class='create-claim-styles btn-spinner' [style.background-color]="theme_color1"></mat-spinner> -->
              </ng-template>

            </button>

            <!-- <button mat-icon-button class="iconbtn ml-3 mr-2 mt-1 mb-1" matTooltip="Create Claim" type="submit"
              (click)="saveExpense()"
              [disabled]="isBeingClaimed || isBeingDrafted || isBeingWorkflowDetermined"
              [style.background-color]="theme_color1">
              <mat-icon *ngIf="isBeingClaimed == false; else showSubmitSpinner" style="color: white"> done_all</mat-icon>
              <ng-template #showSubmitSpinner>
                <mat-spinner diameter='30' class='btn-spinner'></mat-spinner>
              </ng-template>
              </button> -->
            <!-- <button mat-icon-button class="iconbtn-submit ml-3 mr-2 mt-1 mb-1" matTooltip="Create Claim" type="submit"
           [ngClass]="{'restict-cursor': isBeingDrafted || isBeingClaimed || isBeingWorkflowDetermined}" (click)="saveExpense()"
           [disabled]="isBeingClaimed || isBeingDrafted || isBeingWorkflowDetermined"
           [style.color]="theme_color1">
           <mat-icon *ngIf="isBeingClaimed == false; else showSubmitSpinner" style="color: white"> done_all</mat-icon>
           <ng-template #showSubmitSpinner >
             <mat-spinner diameter='30' class='create-claim-styles btn-spinner' [style.color]="theme_color1"></mat-spinner>
           </ng-template>
           </button> -->
          </div>

        </div>
      </div>
    </div>
  </div>
</div>



<!-- <ngx-spinner  bdColor = "rgba(0, 0, 0, 0.8)" size = "medium" color = "#fff" type = "ball-clip-rotate-pulse" [fullScreen] = "true"></ngx-spinner> -->
<!-- <div>{{claimForm.value | json}}</div> -->