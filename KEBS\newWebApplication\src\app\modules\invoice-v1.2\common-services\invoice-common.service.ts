import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Subject, throwError } from 'rxjs';
import { RolesService } from 'src/app/services/acl/roles.service';
import * as _ from 'underscore';
@Injectable({
  providedIn: 'root'
})
export class InvoiceCommonService {

  static invoiceAuthGaurd(oid: any, aid: any, applicationId: any, objectId: any) {
    throw new Error('Method not implemented.');
  }

  private messageSharingSubject = new Subject<any>();
  constructor(private http: HttpClient,
    private roleService: RolesService) { }

  viewInvoice(billingId) {
    return this.http.post("api/invoice/viewInvoice", {
      billingId: billingId,
    });
  }

  roleCheck() {
    return this.http.post("api/invoice/roleCheck", {});
  }

  //? other pdf detail
  getTranslatedPdfDetail = (billingId, langId, billIdFlag) => {
    return this.http.post("/api/invoice/otherLanguagePdfDetails", {
      billingId: billingId,
      languageId: langId,
      billingIdFlag: billIdFlag
    })
  }

  //getAvailableBankAddress - used in stepper - invoice creation
  getAvailableBankAddress = (legalEntityId) => {
    return this.http.post("/api/invoice/stepperAddressChange", {
      legalEntityId: legalEntityId,
      id: 3,
    });
  };

  getInvoiceListActivityList(milestoneId) {
    return this.http.post("/api/invoice/getActivitiesInPopUp", {
      milestoneId: milestoneId,
    });
  }

  updateInvoiceActivityList(milestoneId, activities, oldExpectedOn, newExpectedDate) {
    return this.http.post("/api/invoice/updateActivities", {
      milestoneId: milestoneId,
      activities: activities,
      oldPlannedOn: oldExpectedOn,
      newPlannedOn: newExpectedDate
    });
  }

  saveEditedInvoice = (billingId, formVal, langId) => {
    return this.http.post("/api/invoice/editInvoicePdf", {
      billingId: billingId,
      pdfDetails: formVal,
      languageId: langId
    })
  }

  //? get what are the banks available for a legal entity
  getAvailableBanks = (legalEntityId) => {
    return this.http.post("/api/invoice/bankCrudOperations", {
      legalEntityId: legalEntityId,
      crudId: 2,
    });
  };

  //?getAvailableFromAddress - used in stepper - invoice creation
  getAvailableFromAddress = (lId) => {
    return this.http.post("/api/invoice/stepperAddressChange", {
      legalEntityId: lId,
      id: 1,
    });
  };

  //?getAvailableToAddress - used in stepper - invoice creation
  getAvailableToAddress = (customerId) => {
    return this.http.post("/api/invoice/stepperAddressChange", {
      customerId: customerId,
      id: 2,
    });
  };

  getTranslatedAddressForEnAddress = (legId, addressId, langId) => {
    return this.http.post("/api/invoice/getFromAddressDetails", {
      legalEntityId: legId,
      fromAddressId: addressId,
      languageId: langId
    })
  }
  getTranslatedToAddressForEnAddress = (customerId, addressId, langId) => {
    return this.http.post("/api/invoice/getToAddressDetails", {
      customerId: customerId,
      toAddressId: addressId,
      languageId: langId
    })
  }

  getTranslatedAddressForAbank = (legId, bankId, langId) => {
    return this.http.post("/api/invoice/getBankLanguageDetails", {
      legalEntityId: legId,
      bankId: bankId,
      languageId: langId
    })
  }

  sendMsg = (message: String) => {
    this.messageSharingSubject.next(message);
  }
  getMsg = () => {
    return this.messageSharingSubject.asObservable();
  }

  generateEngPdf = (pdfDetail) => {
      var mediaType = 'application/pdf';
      this.http.post("/api/invoice/generateEnglishPDF", { pdf_details: pdfDetail}, { responseType: 'blob' }).subscribe(
          (response) => {
              var blob = new Blob([response], { type: mediaType });
              var fileURL = URL.createObjectURL(blob);
              window.open(fileURL);
              // saveAs(blob, 'report.pdf');
          },
          e => { throwError(e); }
      );
    }

    generateArabicPdf = (pdfDetail) =>{
      var mediaType = 'application/pdf';
      this.http.post("/api/invoice/generateArabicPDF", { pdf_details: pdfDetail}, { responseType: 'blob' }).subscribe(
          (response) => {
              var blob = new Blob([response], { type: mediaType });
              var fileURL = URL.createObjectURL(blob);
              window.open(fileURL);
              // saveAs(blob, 'report.pdf');
          },
          e => { throwError(e); }
      );
    }

     /** 
  * @description get pdf config
  */
    getInvoicePdfConfig(fromCompanyCode,itemID,projectID,customerID,legalEntityId) {
      return this.http.post("/api/invoice/getInvoicePdfConfig", {
        fromCompanyCode: fromCompanyCode,
        itemId:itemID,
        projectId:projectID,
        customerId:customerID,
        legalEntityId:legalEntityId
       
      });
    }

    /** 
  * @description get tenant role details
  */
    getInvoiceTenantCheckDetail(tenantName,checkType) {
      return this.http.post("/api/invoice/getInvoiceTenantApplicationCheck", {
        tenantName : tenantName,
        checkType : checkType
      });
    }


    getPaymentTerms() {
      return new Promise((resolve, reject) => {
        this.http.post("/api/master/getPaymentTerms", {}).subscribe(
          res => {
            return resolve(res);
          },
          err => {
            console.log(err)
            return resolve([]);
          }
        );
      });
    }

    invoiceUndoAccess() {

      let accessList = _.where(this.roleService.roles, { application_id: 907, object_id: 320 });
  
      if (accessList.length > 0)
        return true;
  
      else
        return false;
    }

    getconsultantDetail(otherMilestoneDataToPdf){
      return this.http.post("/api/invoice/getFteDetails", {
     otherMilestoneDataToPdf
      });
    }
    getFteDetailForViewInvoice(otherMilestoneDataToPdf){
      return this.http.post("/api/invoice/getFteDetailForViewInvoice", {
     otherMilestoneDataToPdf
      });
    }

  getLegalEntityQRConfig(legalEntityId) {
          return this.http.post("/api/invoice/v2/getLegalEntityQRConfig", {
            legalEntityId:legalEntityId
           
          });
        }
    getTenantDateFormats = () =>{
      return this.http.post("/api/invoice/v2/getTenantDateFormats",{});
    }

    syncInvoiceWithSharepoint = (billingId, token) =>{
      return this.http.post("/api/invoice/syncInvoiceWithSharepoint",{
        billingId : billingId,
        token: token
      });
    }
    getCurrencyDetails() {
      return new Promise((resolve, reject) => {
        this.http
          .post("/api/invoice/getCurrencyDetails", { })
          .subscribe((res) => {
            console.log(res);
            return resolve(res['data']);
          });
      });
    }

    invoiceAuthGaurd(oid, aid, application_id, object_id = null) {
      return this.http.post('/api/invoice/checkRoleAccessForUser', {
        oid: oid,
        aid: aid,
        application_id: application_id,
        application_object: object_id
      });
    }
  
}
