@import '~@angular/material/prebuilt-themes/indigo-pink.css';

  .cardHeight{
    height: var(--dynamicHeight);
  }

  .subHeadingHeight{
    height: var(--dynamicSubHeight);
  }

  .paymentHistoryHeight{
    height: var(--dynamicPaymentHistoryHeight);
  }

  .card {
    background-color: #ffffff;
    border: 1px solid #dddddd;
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
    padding: 16px;
    overflow-y: auto;
    // height: 80vh;
  }

  .icon {
    border-radius: 50%;
    height: 33px;
    display: flex;
    line-height: 9px;
    text-align: center;
    justify-content: center;
    vertical-align: middle;
    width: 32px;
    transition: transform 0.3s ease-in-out;
  }

  .iconClass {
    font-size: 16px;
  }

  .labelName {
    font-size: 11px;
    color: #515965;
    font-family: "DM Sans";
    font-weight: 400;
    line-height: 16px;
    /* 145.455% */
    letter-spacing: 0.22px;
    text-transform: capitalize;
    font-style: normal;
  }

  .value {
    color: var(--Blue-Grey-90, #526179);
    font-family: "DM Sans";
    font-size: 12px;
    font-style: normal;
    font-weight: 600;
    line-height: 16px;
    /* 133.333% */
    letter-spacing: 0.24px;
    text-transform: capitalize;
  }

  .header {
    color: var(--Black-100, #111434);
    font-family: "DM Sans";
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 16px;
    letter-spacing: 0.32px;
    text-transform: capitalize;
  }


::ng-deep .mat-tab-label {
  color: #52C41A;
}

 ::ng-deep .mat-ink-bar {
  background-color: #52C41A;
}

::ng-deep .mat-tab-group.mat-primary .mat-ink-bar {
  background-color: #52C41A;
}
.paymentLabel{
  color: var(--Blue-Grey-100, #45546E);
font-family: "DM Sans";
font-size: 12px;
font-style: normal;
font-weight: 500;
line-height: 16px; /* 133.333% */
letter-spacing: 0.24px;
text-transform: capitalize;
}

.mx-2 {
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}

.le-circle {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: #515965;
  position: absolute;
  left: 10px;
}

.cus-circle {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 2px solid #FFBD3D;
  position: absolute;
  left: 10px;
}

.line {
  width: 1px;
  background-color: #DADCE2;
  position: absolute;
  left: 17px;
  top: 34px;
  height: 31px;
}

.labelName, .value {
  margin-left: 20px; /* Adjust this value to align text with circle */
}

.smallCardIcon{
  font-size: 20px;
}

.invoice-no{
  font-size: 16px;
  color: #111434;
  font-family: "DM Sans";
  font-weight: 500;
  line-height: 16px;
}

.go-to-prj-icon{
  font-size: 16px;
}

.amount-label{
  font-size: 12px;
  color: #515965;
  font-family: "DM Sans";
  font-weight: 400;
  line-height: 16px;
}

.amount-value{
  font-size: 14px;
  color:#52C41A;
  font-family: "DM Sans";
  font-weight: 598;
  line-height: 16px;
}
.dollar-icon {
  width: 33px;
  height: 34px;
  border-radius: 50%;
  text-align: center;
  justify-content: center;
  vertical-align: middle;
  margin-top: -3px;
}

.cal-icon{
  color: #1890FF;
  font-size: 24px;
  margin-top: 10px;
}

.cal-circle {
  width: 39px;
  height: 43px;
  border-radius: 50%;
  text-align: center;
  justify-content: center;
  vertical-align: middle;
}

.d-flex {
  display: flex;
  align-items: center;
}

.progress-circle {
  --size: 35px;
  --percentage: 50; /* Default value, will be overridden by Angular binding */
  --thickness: 4px; /* Adjust this value for border thickness */

  position: relative;
  width: var(--size);
  height: var(--size);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.progress-circle::before {
  content: '';
  position: absolute;
  width: 55px;
  height: 55px;
  border-radius: 50%;
  background: conic-gradient(red calc(var(--percentage) * 1%), transparent 0);
  transform: rotate(180deg); /* Rotate the circle to fill anti-clockwise */
}


.progress-circle-inner {
  position: absolute;
  width: calc(var(--size) - 10px);
  height: calc(var(--size) - 10px);
  border-radius: 50%;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  color: black;
}

.ml-3 {
  margin-left: 16px; /* Adjust the margin as needed */
}

.collection-label{
  font-size: 12px;
  color: #1B2140;
  font-family: "DM Sans";
  font-weight: 400;
  line-height: 16px;
}

.inprogress-label {
  font-size: 12px;
  color: #7D838B;
  font-family: "DM Sans";
  font-weight: 400;
  line-height: 16px;
}

.circular-progress{
  position: relative;
  height: 55px;
  width: 55px;
  border-radius: 50%;
}

.circular-progress::before{
  content: "";
  position: absolute;
  height: 87%;
  width: 90%;
  border-radius: 50%;
  margin-top: 4.1px;
  margin-left: 2.6px;
  background: #F7F9FB;
  display: flex;
  background: #fff;
  align-items: center;
  justify-content: center;
  transform: rotate(180deg);
}
.progress-value{
  position: relative;
  font-size: 12px;
  font-weight: bold;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
  color: #272A47;
}

.payment-label{
  font-size: 12px;
  color: #45546E;
  font-family: "DM Sans";
  font-weight: 500;
  line-height: 16px;
}

.submit {
  font-weight: normal;
  font-family: "DM Sans";
  font-size: 14px !important;
  line-height: 31px;
  padding: 0 14px;
  background-color: #52C41A;
  border: #52C41A;
  color:white
}

.button-disabled{
  font-weight: normal;
  font-size: 14px !important;
  line-height: 31px;
  padding: 0 14px;
  background-color: #d18c96;
  border: #cf0001;
  color:white
}

.text-wrap{
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 300px;
  display: inline-block;
}

.payment-history-value{
  font-size: 12px;
  color: #45546E;
  font-family: "DM Sans";
  font-weight: 500;
  line-height: 16px;
}

.payment-history-label{
  font-size: 12px;
  color:  #8B95A5  ;
  font-family: "DM Sans";
  font-weight: 400;
  line-height: 16px;
}

// .payment-history-amount{
//   border: 1px solid #52C41A;
//   border-radius: 40px;
//   height: 26px;
//   width: 66px;
//   z-index: 10;
//   position: absolute;
//   top: -10px;
//   left: 30px;
//   background-color: #EEF9E8;
//   font-size: 12px;
//   line-height: 16px;
//   font-weight: 500;
//   color: #52C41A;
//   display: flex;
//   align-items: center;
//   justify-content: center;
// }

.payment-history-container {
  display: flex;
  flex-direction: column; /* Align children in a column */
  padding-top: 30px; /* Adjust space for the absolute element */
}

.payment-history-amount {
  border: 1px solid #52C41A;
  border-radius: 40px;
  height: 26px;
  width: 66px;
  background-color: #EEF9E8;
  font-size: 12px;
  line-height: 16px;
  font-weight: 500;
  color: #52C41A;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px; /* Add space below */
  top: -28px;
  left: 10px;
  position: relative;
}


.currency-suffix {
  display: flex;
  align-items: center;
  padding-left: 8px;
  padding-right: 8px;
  border-left: 1px solid #DADCE2;
  height: 48px;
  top: -4px !important;
  font-size: 14px;
  line-height: 24px;
  font-weight: 400;
  color: #45546E;
  background-color: #F7F9FB;
  position: relative;
  width: 49px;
  border-bottom-right-radius: 4px;
  border-top-right-radius: 4px;
  border: 1px solid lightgray;
  padding-bottom: 4px;

}

// .upload-container {
//   display: flex;
//   flex-direction: column;
//   align-items: center;
//   justify-content: center;
//   width: 100px; /* Set the desired width */
//   height: 73px; /* Set the desired height */
//   border: 2px dashed rgba(185, 192, 202, 1); /* Adjust the border color and make it dashed */
//   border-radius: 4px; /* Optional: round the corners */
//   padding: 10px; /* Adjust the padding as needed */
//   text-align: center; /* Center the text */
//   box-sizing: border-box; /* Include padding and border in the element's total width and height */
// }

// .upload-container mat-icon {
//   font-size: 16px; /* Adjust the icon size as needed */
//   margin-bottom: 8px; /* Add space between the icon and the text */
//   color: rgba(139, 149, 165, 1);
// }

// .upload-container span {
//   font-size: 14px; /* Adjust the text size as needed */
//   color: rgba(139, 149, 165, 1);
// }

::ng-deep .tooltip-feedback {
  background-color: #1B2140 !important; 
  color: white;
}



.mat-form-field-appearance-outline .mat-form-field-prefix, .mat-form-field-appearance-outline .mat-form-field-suffix {
  top: 0;
}

::ng-deep .mat-form-field .mat-form-field-infix {
  border-bottom: 0.37em solid transparent !important;
}

.file-item {
  margin-right: 10px;
  white-space: nowrap;
  align-items: center;
  height: 28px;
}

.filename {
  margin-right: 10px;
  white-space: nowrap;
  max-width: 188px;
  display: block;
  text-overflow: ellipsis;
  overflow: hidden;
  align-items: center;
  font-size: 14px;
}

.upload-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100px;
  height: 73px;
  border: 2px dashed rgba(185, 192, 202, 1);
  border-radius: 4px;
  padding: 10px;
  text-align: center;
  box-sizing: border-box;
  position: relative; /* Enable positioning for the file count */
}

.upload-container mat-icon {
  font-size: 16px;
  margin-bottom: 8px;
  color: rgba(139, 149, 165, 1);
}

.upload-container span {
  font-size: 14px;
  color: rgba(139, 149, 165, 1);
}

.file-count {
  position: absolute;
  top: -8px;
  right: -8px;
  background-color: #52C41A;
  color: white !important;
  border-radius: 50%;
  width: 21px;
  height: 21px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px !important;
  font-weight: bold;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.2); /* Optional: add shadow for better visibility */
}

.addDocument{
  background-color: #52C41A;
  color: white !important;
}

.custom-table {
  width: 100%;
  display: block !important;
}

mat-header-cell,
mat-cell {
  display: flex;
  align-items: center;
  flex: 1; /* Ensure each column takes its proportional width */
  box-sizing: border-box;
  padding: 8px 16px; /* Add padding for better spacing */
}

mat-header-row,
mat-row {
  display: flex;
}

mat-header-cell[mat-sort-header] {
  justify-content: flex-start;
}

.amount-received{
  ::ng-deep .mat-form-field-appearance-outline .mat-form-field-flex {
    padding: 0 0 0 .75em !important;
  }
  ::ng-deep .mat-form-field-appearance-outline .mat-form-field-flex {
    padding: 0 0 0 .75em !important;
  }
}

.sticky-header {
  position: sticky;
  top: 0;
  z-index: 1000;
  background: white;
}

.selectValue{
  ::ng-deep .mat-select-value-text{
    color: rgba(0, 0, 0, 0.54);
  } 
  ::ng-deep .mat-select-value-text{
    color:  rgba(0, 0, 0, 0.54);
  }
}

.textArea {
  width: 57%;
}

.table-container {
  height: var(--dynamicPaymentHistoryHeight); /* Adjust the height as needed */
  overflow: auto;
  width: 100%;
}

// ::ng-deep .mat-select-value-text{
//   color:  rgba(0, 0, 0, 0.54);
// }

.icon:hover {
  transform: scale(1.3); /* Zoom-in effect */
  transform-origin: center;
}

.hover-text {
  visibility: hidden;
  opacity: 0;
  color: var(--Blue-Grey-90, #526179);
  font-family: "DM Sans";
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 16px;
  transform: translateX(-35%);
  transition: opacity 0.3s ease-in-out;
  width: 80px; /* Adjust width */
  display: block;
  white-space: normal; /* Allow word wrap */
  word-wrap: break-word; /* Break long words */
  line-height: 1.2; /* Set line height for multi-line text */
  margin-top: 3px;
}

.icon-container:hover .hover-text {
  visibility: visible;
  opacity: 1;
}

.icon-container {
  position: relative;
  display: inline-block;
  text-align: center;
}

.payment-history-filename{
  max-width: 188px;
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.green-icon {
  color: #52C41A !important;
}

.checkmark {
  position: absolute;
  top: 0;
  left: 27;
  color: green; /* Adjust the color as needed */
  font-size: 11px; /* Adjust the size as needed */
  transform: translate(39%, -12%);
  z-index: 2;
}

.toggle-btn {
  min-width: 5rem;
  color: black;
  height: 39px;
  align-items: center;
  display: flex;
  font-family: "DM Sans";
}

.btn-toggle-selected {
  font-size: 12px !important;
  background-color:  #EE4961!important;
  color: #ffffff;
  height: 39px;
  align-items: center;
  display: flex;
}

::ng-deep .date-picker .mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon-button, .mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon-button {
  height: 2em !important;
}

::ng-deep .date-picker .mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon-button {
  height: 2em !important;
}
::ng-deep .date-picker .mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-datepicker-toggle-default-icon {
  height: 3em !important;
}
