<!-- <p>invoice-datepicker works!</p> -->
<div class="inv-input" [formGroup]="form">
    <div class="row label-name">
        {{ field.label_name }}<span *ngIf="field.is_mandatory == 1" class="field-mandatory">&nbsp;*</span>
    </div>
    <div class="row label-name">
        <mat-form-field appearance="outline" style="width:100%" class="full-width">
            <input matInput [min]="minDate" [max]="maxDate" [formControlName]="field.key_name" [placeholder]="field.label_name"
                [matDatepicker]="picker" [required]="field.is_mandatory == 1" [matTooltip]="getFieldValue(field.key_name)"
                [readonly]="field.key_name == 'dueDate'" (keyup)="validateAndModify($event,field.key_name)">
            <mat-datepicker-toggle matSuffix [for]="picker" *ngIf="field.key_name != 'dueDate'"></mat-datepicker-toggle>
            <mat-datepicker #picker></mat-datepicker>
        </mat-form-field>
    </div>
</div>

<!-- (dateInput)="onDateChange($event,field.key_name,picker)" -->