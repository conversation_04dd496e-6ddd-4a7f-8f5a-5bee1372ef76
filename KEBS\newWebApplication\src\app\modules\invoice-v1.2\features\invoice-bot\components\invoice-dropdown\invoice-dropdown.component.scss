

  .drop-down {
    .full-width {
        width: 100% !important;
        color:  #5F6C81;
    }

    .non-editable {
        pointer-events: none;
        color: #6e7b8f;
    }

    .form-field {
        border: 2px solid #B9C0CA;
    }

    .label-name {
        color: #6e7b8f;
        font-size: 12px;
        font-weight: 500;
    }

    .field-mandatory {
        color: #cf0001;
    }


    ::ng-deep .custom-mat-select-options {
      margin-top: 27px;
      min-width: 0 !important;
      overflow: hidden !important;
      max-height: 100% !important;
      // width: 160px;
      // margin-left: 30px;
    }
  
   ::ng-deep .mat-select-value-text {
      color:#6e7b8f !important ; /* Change to your desired color */
    }
  
    ::ng-deep .mat-option-text {
      color:#6e7b8f !important ;
      font-size: 12px;
    }
}



