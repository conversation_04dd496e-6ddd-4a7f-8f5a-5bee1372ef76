import { Component, OnInit, Input } from '@angular/core';
import { FormGroup, Validators, FormControl } from '@angular/forms';
import {InvoiceBotService} from './../../services/invoice-bot.service';
@Component({
  selector: 'app-invoice-dropdown',
  templateUrl: './invoice-dropdown.component.html',
  styleUrls: ['./invoice-dropdown.component.scss']
})
export class InvoiceDropdownComponent implements OnInit {
  @Input() field:any;
  @Input() form:FormGroup;
  @Input() prefilldata:any;
  @Input() stepIndex;
  @Input() projectCurrencyMaster;
  @Input() legalEntityId;
  optionList:any;
  selectedItem:any=null;
  fieldConfig: any;

  constructor( private botService: InvoiceBotService) { }

  async ngOnInit(){
    await this.dropdownList(this.field)
    if(this.field.key_name == 'invoiceTemplateName' && !this.optionList?.some(option => option.name == this.form?.get('invoiceTemplateName')?.value)){
      let name = this.form.get('invoiceTemplateName')?.value;
      let id = this.form.get('serviceTypeGroupId')?.value
      this.setFormValue(name,id)
    }
    if (this.field.key_name == 'paymentTerm' && this.form.contains('paymentTerm') && this.form.get('paymentTerm')?.value) {
      let paymentTermValue = this.form.get('paymentTerm')?.value;
      const matchedOption = this.optionList.find(option => option.name === paymentTermValue);
      if (matchedOption) {
        if (this.form.contains('paymentTermId')) {
          // If the control already exists, update its value
          this.form.get('paymentTermId').setValue(matchedOption?.id);
        } else {
          // If the control doesn't exist, add it to the form
          this.form.addControl('paymentTermId', new FormControl(matchedOption?.id));
        }
      }
    }
  }


  async dropdownList(value){
    if(value.options != null){
      this.optionList=JSON.parse(value.options)
    }
    else if(value.key_name == "currency"){
      let data = this.projectCurrencyMaster
      this.optionList = data.map(item => ({ name: item.currency_code, id: item.currency_id, currency_description: item?.currency_description }));
    }
    else{
      if(value.master_api != null){
        this.optionList= await this.botService.getdropdownMaster(value.master_api) 

        if (value.key_name == 'pdfTemplateName') {
          const filteredData = this.optionList.filter(item => item.entity_id == this.form?.get('legalEntityId')?.value);
          this.optionList = filteredData; // Update optionList with the filtered data
        }
        }
    }
  }

  optionClicked(val,key){
    if(val.id && this.field.key_name != 'invoiceTemplateName' && key != 'paymentTerm'  ){
      if (this.form.contains(this.field.key_name + 'Id')) {
        // If the control already exists, update its value
        this.form.get(this.field.key_name + 'Id').setValue(val.id);
      } else {
        // If the control doesn't exist, add it to the form
        this.form.addControl(this.field.key_name + 'Id', new FormControl(val.id));
      }
    }
    if(val.id && this.field.key_name == 'invoiceTemplateName' ){

      if (this.form.contains('serviceTypeGroupId')) {
        // If the control already exists, update its value
        this.form.get('serviceTypeGroupId').setValue(val.id);

      } 
    }
    if(key=='paymentTerm'){
      if (this.form.contains('paymentTermDays')) {
        // If the control already exists, update its value
        this.form.get('paymentTermDays').setValue(val.days);
      } else {
        // If the control doesn't exist, add it to the form
        this.form.addControl('paymentTermDays', new FormControl(val.days));
      }
      if (this.form.contains('paymentTermId')) {
        // If the control already exists, update its value
        this.form.get('paymentTermId').setValue(val?.id);
      } else {
        // If the control doesn't exist, add it to the form
        this.form.addControl('paymentTermId', new FormControl(val?.id));
      }
    }
    if(this.field.key_name == 'currency'){
      if (this.form.contains('currencyDescription')) {
        // If the control already exists, update its value
        this.form.get('currencyDescription').setValue(val?.currency_description);
      } else {
        // If the control doesn't exist, add it to the form
        this.form.addControl('currencyDescription', new FormControl(val?.currency_description));
      }
    }
    if(this.field.key_name == 'placeOfSupply'){
      if (this.form.contains('placeOfSupplyCode')) {
        // If the control already exists, update its value
        this.form.get('placeOfSupplyCode').setValue(val?.code);
      } else {
        // If the control doesn't exist, add it to the form
        this.form.addControl('placeOfSupplyCode', new FormControl(val?.code));
      }
      if (this.form.contains('gstinStateCode')) {
        // If the control already exists, update its value
        this.form.get('gstinStateCode').setValue(val?.gstin_state_code);
      } else {
        // If the control doesn't exist, add it to the form
        this.form.addControl('gstinStateCode', new FormControl(val?.gstin_state_code));
      }
    }
  }

  getFieldValue(fieldName) {
    const formControl = this.form.get(fieldName);
    if (formControl) {
      return formControl.value;
    }
    return '';
  }

// Setting Invoice Template name when the service type is not in option like License service type
  setFormValue(type: string, typeId: number) {
    let selectedValue = type; // Default to the "type" value

    // Check if the "type" is not in the options and typeId is valid
    if (!this.optionList.some(option => option.name == type) && typeId) {
      // Find the corresponding option based on typeId
      const matchingOption = this.optionList.find(option => option.id == typeId);
      if (matchingOption) {
        selectedValue = matchingOption.name;
      }
    }
    // Set the form control value
    this.form.get('invoiceTemplateName')?.setValue(selectedValue);
  }
}


