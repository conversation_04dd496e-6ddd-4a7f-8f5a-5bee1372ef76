<!-- <p>invoice-field-builder works!</p> -->
<div [formGroup]="form" class="Main-field">
  <div class="pt-3" [ngSwitch]="field.field_type" style="width:100%">
    <app-invoice-input *ngSwitchCase="'input'" [field]='field' [form]="form" [isTenantExistingInvoiceAllowed]="isTenantExistingInvoiceAllowed"></app-invoice-input>
    <app-invoice-dropdown *ngSwitchCase="'dropdown'" [field]="field" [form]="form" [prefilldata]="prefilldata" [stepIndex]="stepIndex" [projectCurrencyMaster]="projectCurrencyMaster" [legalEntityId] = 'legalEntityId'></app-invoice-dropdown>
    <ng-container *ngSwitchCase="'text'">
      <div>
        <div class="row label-name">
          {{ field.label_name }}<span *ngIf="field.is_mandatory == 1"class="field-mandatory">&nbsp;*</span>
        </div>
        <div class="row label-name">
          <mat-form-field  *ngIf="field.key_name != 'milestoneValue' && field.key_name != 'invoiceNoType' && field.key_name != 'po_value' && field.key_name != 'remainingPoValue'" appearance="outline" style="width:100%" class="full-width">
            <input matInput [formControlName]="field.key_name" [placeholder]="field.label_name" [required]="field.is_mandatory == 1" [matTooltip]="getFieldValue(field.key_name)" readonly>
          </mat-form-field>

          <mat-form-field *ngIf="field.key_name === 'milestoneValue' ||  field.key_name === 'po_value'||  field.key_name === 'remainingPoValue'" appearance="outline" style="width:100%" class="full-width">
            <div class="input-with-currency">
              <input matInput [formControlName]="field.key_name" [placeholder]="field.label_name" [required]="field.is_mandatory == 1" [matTooltip]="getFieldValue(field.key_name)" readonly style="text-align: right;">
              <span class="currency-indicator">{{this.form.get("currency").value}}</span>
            </div>
          </mat-form-field>
          <mat-form-field *ngIf="field.key_name === 'invoiceNoType'" appearance="outline" style="width:100%" class="full-width" [class.non-editable]="stepIndex == 1">
              <mat-select formControlName="invoiceNoType" [matTooltip]="getFieldValue(field.key_name)" >
                <mat-option [value]="'new'" *ngIf="isTenantExistingInvoiceAllowed === 1 || isTenantExistingInvoiceAllowed ===0">
                  Auto Generated
                </mat-option>
                <!-- <mat-option [value]="'automated'" *ngIf="isTenantExistingInvoiceAllowed === 0">
                  Automated
                </mat-option> -->
                <mat-option [value]="'existing'" *ngIf="isTenantExistingInvoiceAllowed === 1">
                 Previous Invoice No
                </mat-option>
              </mat-select>
          </mat-form-field>

        </div>
      </div>
    </ng-container>
    <app-invoice-datepicker *ngSwitchCase="'datepicker'" [field]="field" [form]="form" [dateFormats]="dateFormats"></app-invoice-datepicker>
    <ng-container *ngSwitchCase="'textarea'">
      <div class="row label-name">
        {{ field.label_name }}<span *ngIf="field.is_mandatory === 1" class="field-mandatory">&nbsp;*</span>
      </div>
      <div class="row full-width">
      <textarea rows="5" [formControlName]="field.key_name" [placeholder]="field.label_name" [required]="field.is_mandatory == 1"></textarea>
      </div>
    </ng-container>
  </div>

</div>
<!-- {{"field builder"}}
{{field|json}} -->
<!-- {{form}}  -->