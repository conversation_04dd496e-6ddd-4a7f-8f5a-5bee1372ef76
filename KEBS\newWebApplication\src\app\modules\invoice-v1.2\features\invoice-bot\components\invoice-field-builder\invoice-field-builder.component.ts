import { Component, OnInit, Input, OnChanges, SimpleChanges } from '@angular/core';
import { FormGroup } from '@angular/forms';
@Component({
  selector: 'app-invoice-field-builder',
  templateUrl: './invoice-field-builder.component.html',
  styleUrls: ['./invoice-field-builder.component.scss']
})
export class InvoiceFieldBuilderComponent implements OnInit  {
  @Input() field:any;
  @Input() form:any;
  @Input() prefilldata :any;
  @Input() stepIndex;
  @Input() projectCurrencyMaster;
  @Input() isTenantExistingInvoiceAllowed;
  @Input() legalEntityId :any;
  @Input() dateFormats: any;
  constructor() { 
  }

  ngOnInit(): void {

  }
  
  getFieldValue(fieldName) {
    const currency = this.form.get('currency').value
    const formControl = this.form.get(fieldName);
    if (fieldName !='milestoneValue' && fieldName !='po_value' && fieldName !='invoiceNoType'){
      if (formControl) {
        return formControl.value;
      }
    }

    if(formControl && (fieldName =='milestoneValue' || fieldName =='po_value') && fieldName !='invoiceNoType'){
      return `${formControl.value} ${currency}`;
    }

    if(fieldName=='invoiceNoType'){
      if (formControl) {
        return formControl.value == 'new'? ' Auto Generated' : 'Previous Invoice No';
      }
    }

    return '';
  }

}
