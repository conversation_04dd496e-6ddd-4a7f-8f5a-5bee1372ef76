import { Component, OnInit, Input, EventEmitter, Output, ViewChild } from '@angular/core';
import { FormGroup, FormControl, FormBuilder, FormArray, Validators, ValidationErrors, ValidatorFn, AbstractControl } from '@angular/forms';
import { InvoiceConfigService } from '../../../invoice-config/services/invoice-config.service';
import { debounceTime } from 'rxjs/operators';
import * as moment from 'moment';
import { InvoiceGenerationService } from '../../../invoice-generation/services/invoice-generation.service';
import { MatBottomSheet } from '@angular/material/bottom-sheet';
import { InvoiceBotService } from '../../services/invoice-bot.service';
import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';
import { LoginService } from 'src/app/services/login/login.service';
const { toWords } = require('number-to-words');
var curValue = require('multilingual-number-to-words');
import * as _ from 'underscore';
import { InvoiceBillingService } from "../../../invoice-billing/services/invoice-billing.service";
import { MatSnackBar } from '@angular/material/snack-bar';
import { NgxSpinnerService } from 'ngx-spinner';
import { MatMenuTrigger } from '@angular/material/menu';
import swal from "sweetalert2";
import { ActivatedRoute, Router } from '@angular/router';
import { takeUntil} from "rxjs/operators";
import { Subject } from 'rxjs';
import { MatSelect } from '@angular/material/select';
import {AttachFilesComponent} from "../attach-files/attach-files.component";
import { MatDialog } from '@angular/material/dialog';

@Component({
  selector: 'app-invoice-form-builder',
  templateUrl: './invoice-form-builder.component.html',
  styleUrls: ['./invoice-form-builder.component.scss']
})
export class InvoiceFormBuilderComponent implements OnInit {
  @Input() legalEntityid;
  @Input() showPartnerMargin: boolean;
  @Output() formValidityChange = new EventEmitter<boolean>();
  @Input() stepIndex;
  @Input() serviceTypeGroupId;
  @Input() isTenantExistingInvoiceAllowed;
  @Input() currencyDetails;
  @Input() showWarmMsg;
  @Input() restrictSavePoOnAlert;
  @Input() poData: any;
  @Input() formControlForPoAlert: String;
  @Input() invoiceTenantDetails: any;
  @Input() dateFormats: any;
  @Output() onFormValueChanges = new EventEmitter<{ form: any, stepIndex: number }>();
  @Output() entityFieldData = new EventEmitter<{ entityConfig: any, stepIndex: number }>();
  @Output() itemFieldData = new EventEmitter<{ itemConfig: any, stepIndex: number }>();
  @Output() fteFieldData = new EventEmitter<{ fteConfig: any, stepIndex: number }>();
  @ViewChild('MenuTrigger') MenuTrigger: MatMenuTrigger;
  @ViewChild('onScroll', { static: false }) onScroll: MatSelect;
  integerPartInWords: any
  footerFields: any
  rateValues: any
  worksummaryAmountValues: any
  fteRateValues: any;
  subLineItemdata: any;
  conversionRates: any
  entityFieldConfig: any;
form: FormGroup;
  show: boolean = false;
  showPoAlert: boolean = false;
  staticFields: any;
  headerFields: any;
  itemFields: any;
  totalFields: any;
  poDetailsFields:any;
  fieldConfig: any;
  getTdsOptions: any;
  getTcsOptions: any;
  config: any;
  itemConfig: any;
  showFteDetails: boolean = false;
  ftedata: any;
  masterFteData: any;
  invoiceWorkLocation: any;
  skillRoleList: any;
  fteconfig: any;
  optionList: any;
  termsConditionsConfig: any;
  legal_entity_details: any;
  taxresultArray: any;
  contextId: any;
  billingId: any;
  projectFields = [
    'Customer Name',
    'Project Name',
    'Milestone Name',
    'Milestone Value',
    'Cost Center',
    'Department',
    'Payment Terms',
    'Currency',
    'PO Reference',
    'PO Date',
    'Service Details',
    'FTE Details'
  ]
  touchedFormControl = false;
  @Input() taxMaster: any;
  @Input() groupTaxMaster: any;
  @Input() prefilldata: any;
  @Input() projectCurrencyMaster: any;
  @Input () chargeTypes: any;
  filterTaxGroup: any;
  dropdownOptions: any;
  productServiceOptions: any = [];
  isTSTextarea: boolean = false;
  poValueForIntercompany: any;
  poErrorMessage: any;
  poError: boolean = false;
  RemainingPoValue :any;
  totalPoValue:any;
  poAlertText: any;
  searchCtrl = new FormControl();
  startIndex = 0;
  offSet = 50;
  protected _onDestroy = new Subject<void>();
  invoiceNo: FormControl;
  isReadonly: boolean = true;
  isPreviousNumberAvailable: boolean = false;
  isManualNumberApplicable: boolean = false;
  allowedCharacters: any;
  isBillable: boolean = false;
  isFromDraft: boolean = false;
  projectAttachmentsCount: any;
  invoiceAttachmentsCount: any;
  constructor(private formBuilder: FormBuilder,
    private invConfigService: InvoiceConfigService,
    private invoiceService: InvoiceGenerationService,
    private _bottomSheet: MatBottomSheet,
    private botService: InvoiceBotService,
    private authService: LoginService,
    private invoiceBillService: InvoiceBillingService,
    private _snackBar: MatSnackBar,
    private spinner: NgxSpinnerService,
    private router: Router,
    private dialog: MatDialog
  ) {

  }

  async ngOnInit() {
    this.getTdsOptions = await this.invoiceService.getTaxCodeDetails();
    this.getTcsOptions = await this.invoiceService.getTCSDetails();
    this.dropdownOptions = {
      tds: this.getTdsOptions['data'],
      tcs: this.getTcsOptions['data']
    };
    this.botService.touchFormControl$.subscribe((value) => {
      this.touchedFormControl = value;
      if (this.touchedFormControl) {
        this.markFormGroupTouched();
      }
    });

    if(this.poData){
      if(this.poData["messType"] == 'S'){
        this.RemainingPoValue = this.poData['data']
        this.totalPoValue = this.poData['total']
      }
      else if(this.poData["messType"] == 'E'){
        this.poError = true
        this.poErrorMessage = this.poData["messText"]
      }
    }

    this.searchCtrl.valueChanges
    .pipe(takeUntil(this._onDestroy), debounceTime(700))
    .subscribe(async val => {
      if (val != null && val != '') {
        this.productServiceOptions=[];
        this.startIndex=0;
        this.offSet=50;
        let searchValue = await this.getProductServiceDropdown();
      }
    })

    if (this.invoiceTenantDetails?.hasOwnProperty('is_manual_invoice_no_applicable') && this.invoiceTenantDetails['is_manual_invoice_no_applicable']){
      this.isManualNumberApplicable = true
    }
  
  }


  // To create Form based on config(@input fields)

  async createForm(fields: any[]) {
    let formGroupConfig = {};

    // To create formcontrol using key_name in the fields config(Entity Level)
    for (let field of fields) {
      const validators = field.is_mandatory == 1 ? [Validators.required] : null;
      formGroupConfig[field.key_name] = new FormControl('', validators);
    }

    // To create additional formcontrols 
    formGroupConfig['isTSTextarea'] = new FormControl(false);
    formGroupConfig['subTotal'] = new FormControl("");
    formGroupConfig['totalAmount'] = new FormControl("");
    formGroupConfig['retentionAmount'] = new FormControl("");
    formGroupConfig['discountAmount'] = new FormControl("");
    formGroupConfig['discountInputAmount'] = new FormControl("");
    formGroupConfig['discountAmountType'] = new FormControl("");
    formGroupConfig['retentionInputAmount'] = new FormControl("");
    formGroupConfig['retentionAmountType'] = new FormControl("");
    formGroupConfig['totalTaxAmount'] = new FormControl("");
    formGroupConfig['workSummary'] = this.formBuilder.array([]);
    formGroupConfig['fteDetails'] = this.formBuilder.array([]);
    formGroupConfig['taxDetails'] = this.formBuilder.array([]);
    formGroupConfig['taxResultDetails'] = this.formBuilder.array([]);
    formGroupConfig['totalTdsTcsExclusive'] = new FormControl("");
    this.form = this.formBuilder.group(formGroupConfig);

    // this.form.get('discountInputAmount')?.setValidators(this.getDiscountValidators('percentage'));
    this.setupValueChanges(); // To set up the value changes of the form
    if (this.invoiceTenantDetails?.hasOwnProperty('no_of_characters_allowed_for_invoice_no') && this.invoiceTenantDetails['no_of_characters_allowed_for_invoice_no']){
      this.allowedCharacters = this.invoiceTenantDetails['no_of_characters_allowed_for_invoice_no']
    }
    else{
      this.allowedCharacters = 16;
    }
    this.isFromDraft = this.prefilldata?.isFromDraft
    this.prefillFormData(this.prefilldata) // To patch the preset field details
    this.isBillable = this.prefilldata?.is_billable == 1 ? true : false
    let milestoneId = this.prefilldata?.milestoneId
    let ganttId = this.prefilldata?.ganttId
    if(this.isBillable){
      this.contextId = `PRJ_${milestoneId}${ganttId}`
    }
    this.projectAttachmentsCount = this.prefilldata?.project_attachment_count || 0
    this.invoiceAttachmentsCount = this.prefilldata?.invoice_attachment_count || 0
  }

  // To patch the preset fields details in to form
  async prefillFormData(data) {
    if (data) {
      this.form.addControl('fromCompanyCode', new FormControl(data.From_company_code));
      this.form.addControl('toCompanyCode', new FormControl(data.To_company_code));
      this.form.addControl('fromName', new FormControl(data.entity_name));
      this.form.addControl('fromAddressId', new FormControl(data.from_address_id));
      this.form.addControl('fromLineOneAddress', new FormControl(data.from_address_line_1));
      this.form.addControl('fromLineTwoAddress', new FormControl(data.from_address_line_2));
      this.form.addControl('fromLineThreeAddress', new FormControl(data.from_address_line_3));
      this.form.addControl('fromLineFourAddress', new FormControl(data.from_address_line_4));
      this.form.addControl('fromLineFiveAddress', new FormControl(data.from_address_line_5));
      this.form.addControl('toAddressId', new FormControl(data.to_address_id));
      this.form.addControl('toLineOneAddress', new FormControl(data.to_address_line_1));
      this.form.addControl('toLineTwoAddress', new FormControl(data.to_address_line_2));
      this.form.addControl('toLineThreeAddress', new FormControl(data.to_address_line_3));
      this.form.addControl('toLineFourAddress', new FormControl(data.to_address_line_4));
      this.form.addControl('toLineFiveAddress', new FormControl(data.to_address_line_5));
      this.form.get('customerGSTIN')?.setValue(data.customerGSTIN);
      this.form.addControl('legalEntityId', new FormControl(data.legal_entity_id));
      this.form.addControl('customerId', new FormControl(data.customer_id));
      this.form.addControl('paymentMode', new FormControl("Bank Transfer"));
      this.form.addControl('billingId', new FormControl(data.billing_id || null));
      this.form.get('projectName')?.setValue(data.project_name);
      this.form.addControl('projectId', new FormControl(data.project_id));
      this.form.addControl('itemId', new FormControl(data.itemId));
      this.form.get('itemName')?.setValue(data.itemName);
      this.form.get('milestoneName')?.setValue(data.milestone_name);
      this.form.addControl('milestoneId', new FormControl(data.milestoneId));
      this.form.get('profitCenter')?.setValue(data.profitCenter);
      this.form.addControl('profitCenterId', new FormControl(data.profitCenterId));
      this.form.addControl('serviceTypeId', new FormControl(data.service_type_id));
      this.form.get('serviceTypeName')?.setValue(data.service_type_name);
      if(!this.isFromDraft){
        this.form.get('invoiceTemplateName')?.setValue(data.service_type_name);
      }
      else if(this.isFromDraft){
        let staticFields = this.entityFieldConfig.filter(field => field.pdf_display_section === "Standard");
        let getInvoiceTemplateOptions = staticFields?.filter(field => field.key_name == "invoiceTemplateName");
        this.optionList=JSON.parse(getInvoiceTemplateOptions[0]?.options)
 
        const matchingOption = this.optionList.find(option => option.id == this.serviceTypeGroupId);
        if (matchingOption) {
          this.form.get('invoiceTemplateName')?.setValue(matchingOption.name);
        }
      }

      this.form.addControl('parentServiceTypeGroupId', new FormControl(this.serviceTypeGroupId));
      this.form.addControl('serviceTypeGroupId', new FormControl(this.serviceTypeGroupId));
      this.form.get('currency')?.setValue(data.billing_currency);
      this.form.addControl('currencyId', new FormControl(data.billing_currency_id));
      this.form.addControl('currencyDescription', new FormControl(data.billing_currency_description));
      this.form.get('department')?.setValue(data.department_name);
      this.form.get('poRef')?.setValue(data.poRef || "");
      this.form.get('poNumber')?.setValue(data.poNumber || "");
      this.form.get('poDate')?.setValue(data.poDate || "");
      this.form.get('customerName')?.setValue(data.To_company_code || "");
      this.form.addControl('unit', new FormControl("Hours"));
      this.form.get('partner_margin')?.setValue(data.partnerPercent || "");
      this.form.addControl('invoiceRaisedOn', new FormControl(moment(new Date()).format('YYYY-MM-DD')));
      this.form.addControl('tdsValue', new FormControl(data.tdsValue || 0.00));
      this.form.addControl('fromEmail', new FormControl(data.from_email));
      this.form.addControl('fromFax', new FormControl(data.from_fax));
      this.form.addControl('fromTelephone', new FormControl(data.from_telephone));
      this.form.addControl('fromWebsite', new FormControl(data.from_website));
      this.form.addControl('cin', new FormControl(data.CIN));
      this.form.addControl('toEmail', new FormControl(data.to_email));
      this.form.addControl('toFax', new FormControl(data.to_fax));
      this.form.addControl('toTelephone', new FormControl(data.to_telephone));
      this.form.addControl('toWebsite', new FormControl(data.to_website));
      this.form.addControl('entityCurrencyCode', new FormControl(data.entity_currency_code));
      this.form.addControl('purchaseLedgerName', new FormControl(data.purchase_ledger_name));
      this.form.addControl('salesLedgerName', new FormControl(data.sales_ledger_name));
      this.form.addControl('tallyToName', new FormControl(data.tally_to_name));
      this.form.addControl('tallyFromName', new FormControl(data.tally_from_name));
      let invoice_no_data = data?.invoice_no ? data?.invoice_no : 'XXX-XXX-XXX-XXXX';
      this.isPreviousNumberAvailable = data?.invoice_no ? true : false;
      if (this.form.contains('invoiceNoType')) {
        this.form.addControl(
          'invoiceNo',
          new FormControl(invoice_no_data, [
            Validators.required,
            Validators.maxLength(this.allowedCharacters)
          ])
        );
      }
      let invoiceType = data?.invoice_no ? 'existing' : 'new'
      if(invoiceType == 'existing'){
        this.isTenantExistingInvoiceAllowed = true;
      }
      this.form.get('invoiceNoType')?.setValue(invoiceType);
      this.form.get('placeOfSupply')?.setValue(data.place_of_supply);
      this.form.addControl('placeOfSupplyCode', new FormControl(data.place_of_supply_code));
      this.form.addControl('gstinStateCode', new FormControl(data.gstin_state_code));
      if (this.stepIndex == 0)
        this.form.get('paymentTerm')?.setValue(data.payment_terms || null);
      else
        this.form.get('paymentTerm')?.setValue('Back to back with clients');
      this.form.addControl('paymentTermDays', new FormControl(data.payment_terms_days == 0 ? 0 : data.payment_terms_days));
      this.form.addControl('totalAmountInwords', new FormControl(""));
      this.form.addControl('milestoneValueArr', new FormControl(data?.milestone_value));
      let milestone_value = data?.milestone_value?.find(item => item?.currency_code === data?.billing_currency);
      this.form.addControl('poValueArr', new FormControl(this.totalPoValue));
      let po_value = this.totalPoValue?.find(item => item.currency_code === data.billing_currency);
      let remaining_po_value = this.RemainingPoValue?.find(item => item.currency_code === data.billing_currency);
      this.form.get("milestoneValue")?.setValue(milestone_value ? this.fixNumberOnUI(milestone_value.value, data.billing_currency) : 0.00)
      this.form.get('po_value')?.setValue(po_value ? this.fixNumberOnUI(po_value?.value, data.billing_currency) : "")
      this.form.get('remainingPoValue')?.setValue(remaining_po_value ? this.fixNumberOnUI(remaining_po_value?.value, data?.billing_currency) : "")
      this.form.addControl('invoiceRaisedBy', new FormControl(this.authService.getProfile().profile.oid,));
      this.form.addControl('selectedOption', new FormControl(data.selectedOption||'tds'));
      this.form.addControl('selectedDropdownOption', new FormControl(data.selectedDropdownOption));
      this.form.addControl('tdsDescription', new FormControl(data.tdsDescription));
      this.form.addControl('tdsId', new FormControl(data.tdsId));
      this.form.addControl('tdsPercentage', new FormControl(data.tdsPercentage));
      this.form.addControl('tcsDescription', new FormControl(data.tcsDescription));
      this.form.addControl('tcsId', new FormControl(data.tcsId));
      this.form.addControl('tcsPercentage', new FormControl(data.tcsPercentage));
      this.form.addControl('tcsValue', new FormControl(data.tcsValue || 0.00));
      this.form.get('discountAmountType')?.setValue('percentage');
      this.form.get('retentionAmountType')?.setValue('percentage');
      this.form.addControl('MilestoneUpdatedBy', new FormControl(data?.milestone_updated_by || ""));
      this.form.addControl('MilestoneUpdatedDate', new FormControl(data?.milestone_updated_date || ""));
      this.form.addControl('industryName', new FormControl(data.industry_name || ''));
      this.form.addControl('isBillable', new FormControl(!!data?.is_billable));
      this.form.addControl('financialBookName', new FormControl(data.financial_book_name));
      this.form.addControl('customerEmail', new FormControl(data.customer_email));
      this.form.get('pdfTemplateName')?.setValue(data.pdfTemplateName);
      this.form.addControl('pdfTemplateNameId', new FormControl(data.pdfTemplateNameId));
      this.patchInvoiceDate()
      if(this.isFromDraft){
        this.form.get('discountAmountType')?.setValue(data?.discountAmountType || 'percentage');
        this.form.get('discountInputAmount')?.setValue(data?.discountInputAmount);
        this.form.get('discountAmount')?.setValue(data?.discountAmount);

        this.form.get('retentionAmountType')?.setValue(data?.retentionAmountType ||'percentage');
        this.form.get('retentionInputAmount')?.setValue(data?.retentionInputAmount);
        this.form.get('retentionAmount')?.setValue(data?.retentionAmount);
      }
      this.form.get('note')?.setValue(data?.note);
      this.form.get('narration')?.setValue(data?.narration);
    }

    // To get the terms and condition config from legal entity table
    this.termsConditionsConfig = await this.getTermsConditionsConfig(this.form.get('legalEntityId')?.value)
    this.patchLegalEntityDetails();
    this.setTermsAndConditions(data, this.stepIndex)

  }

  // To divide the field config based on section( for showing section wise in html) 
  getStaticField() {
    this.staticFields = this.entityFieldConfig.filter(field => field.pdf_display_section === "Standard");
    if (!this.showPartnerMargin) {
      // Remove the partner_margin field from the staticFields array
      this.staticFields = this.staticFields.filter(field => field.key_name !== "partner_margin");
    }
    this.poDetailsFields = this.entityFieldConfig.filter(field => field.pdf_display_section === "PO");
    this.headerFields = this.entityFieldConfig.filter(field => field.pdf_display_section === "Header");
    this.itemFields = this.entityFieldConfig.filter(field => field.pdf_display_section === "Items");
    this.totalFields = this.entityFieldConfig.filter(field => field.pdf_display_section === "Total");
    this.footerFields = this.entityFieldConfig.filter(field => field.pdf_display_section === "Footer");
    this.footerFields.forEach(field => {
      if (field.key_name === 'termsAndConditions' && field.field_type === 'textarea') {
        this.isTSTextarea = true;
        this.form?.get('isTSTextarea')?.setValue(true)
        if(this.prefilldata["termsAndConditions"]){
          this.form?.get('termsAndConditions')?.setValue(this.prefilldata["termsAndConditions"] || '')
        }
        else{
          this.form?.get('termsAndConditions')?.setValue(field.default_value || '')
        }
      }
    });

  }

  patchInvoiceDate() {
    let staticFields = this.entityFieldConfig?.filter(field => field.pdf_display_section === "Standard");

    if(!this.isFromDraft){
      staticFields?.forEach(field => {
        if (field.key_name === 'invoiceDate') {
          if (field.default_value) {
            let default_value = JSON.parse(field.default_value);
            if (default_value.is_to_show_invoice_date == true) {
              this.form?.get('invoiceDate')?.setValue(this.prefilldata.invoice_date);
            }
            else{
              this.form?.get('invoiceDate')?.setValue(moment().format("YYYY-MM-DD"));
            }
          }
        }
      });
    }


    if(this.isFromDraft){
      this.form?.get('invoiceDate')?.setValue(this.prefilldata.invoice_date);
    }
  }

  // To populate work summary based on service type id 
  populateWorkSummaryArray() {
    // const workSummaryArray = this.form.get('workSummary') as FormArray;
    let currency = this.form.get('currency').value;


    if (this.form.get("serviceTypeGroupId")?.value == 2) {
      const workSummaryArray = this.form.get('workSummary') as FormArray;
      workSummaryArray.clear();
      const workSummaryFormGroup = this.formBuilder.group({}); // formgroup inside workSummary formarray is created
      this.itemConfig.forEach((configItem, index) => {   // Formcontrol created using key_name from config
        const keyName = configItem.key_name;
        let controlValue = keyName === 'lineItemNo' ? (index + 1) : '';
        const validators = configItem.is_mandatory == 1 ? [Validators.required] : null;
        workSummaryFormGroup.addControl(keyName, this.formBuilder.control(controlValue, validators));
      });

      let milestonevalue = this.form.get('milestoneValue').value
      workSummaryFormGroup.get('rate').setValue(milestonevalue);
      workSummaryFormGroup.get('quantity').setValue(1);
      let rate = this.parseValue(workSummaryFormGroup.get('rate').value);
      let quantity = this.parseValue(workSummaryFormGroup.get('quantity').value);
      workSummaryFormGroup.get('amount').setValue(this.fixNumberOnUI((quantity * rate), currency));
      workSummaryFormGroup.get('description').setValue(this.form.get("milestoneName").value)
      workSummaryFormGroup.addControl('taxId', this.formBuilder.control(this.prefilldata.tax_id || null));
      workSummaryFormGroup.addControl('taxPercentage', this.formBuilder.control(this.prefilldata.tax_percentage|| null));
      workSummaryFormGroup.get('tax')?.setValue(this.prefilldata.tax_description|| null);
      workSummaryFormGroup.addControl('taxType', this.formBuilder.control(this.prefilldata?.tax_type|| null));
      workSummaryFormGroup.addControl('chargeDescription', this.formBuilder.control(''));
      workSummaryFormGroup.addControl('chargeType', this.formBuilder.control(''));
      workSummaryFormGroup.addControl('chargeTypeName', this.formBuilder.control(''));
      workSummaryArray.push(workSummaryFormGroup);
      let amount = this.parseValue(workSummaryFormGroup.get('amount').value);
      let id = workSummaryFormGroup.get('taxId')?.value;
      let type = workSummaryFormGroup.get('taxType')?.value;
      if (workSummaryFormGroup.get('taxId')?.value) {
        if(type.toLowerCase() == 'tax'){
          let item = this.taxMaster.find(obj => obj.id === id);
          workSummaryFormGroup.addControl('taxDetails', new FormControl(item));
          this.populateTaxArray(item, "tax", amount, 1)
        }
        else{
          let item = this.groupTaxMaster.find(obj => obj.id === id);
          workSummaryFormGroup.addControl('taxDetails', new FormControl(item));
          this.populateTaxArray(item, "group", amount, 1)
        }
      }
      this.updateSubtotal();
      this.updateTotalTaxAmount();
      this.updateTotal();
      this.show = true;

      // When rate changes
      workSummaryFormGroup.get('rate').valueChanges.subscribe(() => {
        let currency = this.form.get('currency').value;
        const quantity = this.parseValue(workSummaryFormGroup.get('quantity').value);
        const rate = this.parseValue(workSummaryFormGroup.get('rate').value);
        workSummaryFormGroup.get('amount').setValue(this.fixNumberOnUI((quantity * rate), currency));
        this.updateSubtotal();
        this.updateTaxAmountonRateQuantityChange();
        this.updateTotalTaxAmount();
        this.updateTdsTcs();
        this.updateTotal();
        if (workSummaryFormGroup.get('taxType').value == "tax") {
          let lineItemno = workSummaryFormGroup.get('lineItemNo')?.value;
          let id = workSummaryFormGroup.get('taxId')?.value;
          let item = this.taxMaster.find(obj => obj.id === id);
          let amount = this.parseValue(workSummaryFormGroup.get('amount')?.value);
          this.populateTaxArray(item, "tax", amount, lineItemno)
        }
        if (workSummaryFormGroup.get('taxType').value == "group") {
          let lineItemno = workSummaryFormGroup.get('lineItemNo')?.value;
          let id = workSummaryFormGroup.get('taxId')?.value;
          let item = this.groupTaxMaster.find(obj => obj.id === id);
          let amount = this.parseValue(workSummaryFormGroup.get('amount')?.value);
          this.populateTaxArray(item, "group", amount, lineItemno)
        }
      });

      // When quantity changes
      workSummaryFormGroup.get('quantity').valueChanges.subscribe(() => {
        let currency = this.form.get('currency').value;
        const quantity = this.parseValue(workSummaryFormGroup.get('quantity').value);
        const rate = this.parseValue(workSummaryFormGroup.get('rate').value);
        workSummaryFormGroup.get('amount').setValue(this.fixNumberOnUI((quantity * rate), currency));
        this.updateSubtotal();
        this.updateTaxAmountonRateQuantityChange();
        this.updateTotalTaxAmount();
        this.updateTdsTcs();
        this.updateTotal();
        if (workSummaryFormGroup.get('taxType').value == "tax") {
          let lineItemno = workSummaryFormGroup.get('lineItemNo')?.value;
          let id = workSummaryFormGroup.get('taxId')?.value;
          let item = this.taxMaster.find(obj => obj.id === id);
          let amount = this.parseValue(workSummaryFormGroup.get('amount')?.value);
          this.populateTaxArray(item, "tax", amount, lineItemno)
        }
        if (workSummaryFormGroup.get('taxType').value == "group") {
          let lineItemno = workSummaryFormGroup.get('lineItemNo')?.value;
          let id = workSummaryFormGroup.get('taxId')?.value;
          let item = this.groupTaxMaster.find(obj => obj.id === id);
          let amount = this.parseValue(workSummaryFormGroup.get('amount')?.value);
          this.populateTaxArray(item, "group", amount, lineItemno)
        }
      });

    }
    if (this.form.get("serviceTypeGroupId")?.value == 1) {
      this.unitClicked("Hours")
      if (this.stepIndex == 0) {
        this.populateFteDetails(this.ftedata)
      }
  
      if(this.invoiceTenantDetails && this.invoiceTenantDetails?.show_fte_in_worksummary && this.ftedata && this.ftedata.length > 0){
        const workSummaryArray = this.form.get('workSummary') as FormArray;
        workSummaryArray.clear();
        if (this.ftedata && this.ftedata.length > 0) {
          this.ftedata.forEach((fte, index) => {
            const workSummaryFormGroup = this.formBuilder.group({});
  
            this.itemConfig.forEach(configItem => {
              const keyName = configItem.key_name;
              let controlValue = null;
              // let controlValue = keyName === 'lineItemNo' ? (index + 1) : '';
              switch (keyName) {
                case 'lineItemNo':
                  controlValue = index + 1;
                  break;
                case 'description':
                  controlValue = fte.name != null ? fte.name : "";
                  break;
                case 'quantity':
                  controlValue = fte.actualWorkingDays != null ? fte.actualWorkingDays : "";
                  break;
                case 'rate':
                  controlValue = fte.perDayRate != null ? fte.perDayRate : "";
                  break;
                case 'productServicetype':
                  controlValue = "";
                  break;
                  case 'chargeType':
                    controlValue = "";
                    break;
              }
              const validators = configItem.is_mandatory == 1 ? [Validators.required] : null;
              workSummaryFormGroup.addControl(keyName, this.formBuilder.control(controlValue, validators));
            });
            const quantity = this.parseValue(workSummaryFormGroup.get('quantity').value);
            const rate = this.parseValue(workSummaryFormGroup.get('rate').value);
            workSummaryFormGroup.get('amount').setValue(this.fixNumberOnUI((quantity * rate), this.form.get('currency').value));
  
            workSummaryFormGroup.addControl('productServiceTypeId', this.formBuilder.control(''));
            workSummaryFormGroup.addControl('taxId', this.formBuilder.control(this.prefilldata.tax_id || null));
            workSummaryFormGroup.addControl('taxPercentage', this.formBuilder.control(this.prefilldata.tax_percentage || null));
            workSummaryFormGroup.addControl('chargeDescription', this.formBuilder.control(''));
            workSummaryFormGroup.addControl('chargeType', this.formBuilder.control(''));
            workSummaryFormGroup.addControl('chargeTypeName', this.formBuilder.control(''));
            workSummaryFormGroup.get('tax')?.setValue(this.prefilldata.tax_description || null);
            workSummaryFormGroup.addControl('taxType', this.formBuilder.control(this.prefilldata?.tax_type || null));
            let amount = this.parseValue(workSummaryFormGroup.get('amount').value);
            let id = workSummaryFormGroup.get('taxId')?.value;
            let type = workSummaryFormGroup.get('taxType')?.value;
            let lineItemNo = workSummaryFormGroup.get('lineItemNo')?.value;
            if (workSummaryFormGroup.get('taxId')?.value) {
              if(type.toLowerCase() == 'tax'){
                let item = this.taxMaster.find(obj => obj.id === id);
                workSummaryFormGroup.addControl('taxDetails', new FormControl(item));
                this.populateTaxArray(item, "tax", amount, lineItemNo)
              }
              else{
                let item = this.groupTaxMaster.find(obj => obj.id === id);
                workSummaryFormGroup.addControl('taxDetails', new FormControl(item));
                this.populateTaxArray(item, "group", amount, lineItemNo)
              }
            }
            workSummaryArray.push(workSummaryFormGroup);
            this.updateSubtotal();
            this.aggregateTax();
            this.updateTotalTaxAmount();
            this.updateTdsTcs();
            this.updateTotal();
            // this.aggregateTax()
            this.show = true;

    
            // When rate changes
            workSummaryFormGroup.get('rate').valueChanges.subscribe(() => {
              let currency = this.form.get('currency').value;
              const quantity = this.parseValue(workSummaryFormGroup.get('quantity').value);
              const rate = this.parseValue(workSummaryFormGroup.get('rate').value);
              workSummaryFormGroup.get('amount').setValue(this.fixNumberOnUI((quantity * rate), currency));
              this.updateSubtotal();
              this.updateTaxAmountonRateQuantityChange();
              this.updateTdsTcs();
              this.updateTotalTaxAmount();
              this.updateTotal();
              if (workSummaryFormGroup.get('taxType').value == "tax") {
                let lineItemno = workSummaryFormGroup.get('lineItemNo')?.value;
                let id = workSummaryFormGroup.get('taxId')?.value;
                let item = this.taxMaster.find(obj => obj.id === id);
                let amount = this.parseValue(workSummaryFormGroup.get('amount')?.value);
                this.populateTaxArray(item, "tax", amount, lineItemno)
              }
              if (workSummaryFormGroup.get('taxType').value == "group") {
                let lineItemno = workSummaryFormGroup.get('lineItemNo')?.value;
                let id = workSummaryFormGroup.get('taxId')?.value;
                let item = this.groupTaxMaster.find(obj => obj.id === id);
                let amount = this.parseValue(workSummaryFormGroup.get('amount')?.value);
                this.populateTaxArray(item, "group", amount, lineItemno)
              }
            });
  
            // When quantity changes
            workSummaryFormGroup.get('quantity').valueChanges.subscribe(() => {
              let currency = this.form.get('currency').value;
              const quantity = this.parseValue(workSummaryFormGroup.get('quantity').value);
              const rate = this.parseValue(workSummaryFormGroup.get('rate').value);
              workSummaryFormGroup.get('amount').setValue(this.fixNumberOnUI((quantity * rate), currency));
              this.updateSubtotal();
              this.updateTaxAmountonRateQuantityChange();
              this.updateTdsTcs();
              this.updateTotalTaxAmount();
              this.updateTotal();
              if (workSummaryFormGroup.get('taxType').value == "tax") {
                let lineItemno = workSummaryFormGroup.get('lineItemNo')?.value;
                let id = workSummaryFormGroup.get('taxId')?.value;
                let item = this.taxMaster.find(obj => obj.id === id);
                let amount = this.parseValue(workSummaryFormGroup.get('amount')?.value);
                this.populateTaxArray(item, "tax", amount, lineItemno)
              }
              if (workSummaryFormGroup.get('taxType').value == "group") {
                let lineItemno = workSummaryFormGroup.get('lineItemNo')?.value;
                let id = workSummaryFormGroup.get('taxId')?.value;
                let item = this.groupTaxMaster.find(obj => obj.id === id);
                let amount = this.parseValue(workSummaryFormGroup.get('amount')?.value);
                this.populateTaxArray(item, "group", amount, lineItemno)
              }
            });
  
          });
        }
      }
      else{
      const workSummaryArray = this.form.get('workSummary') as FormArray;
      workSummaryArray.clear();

      const workSummaryFormGroup = this.formBuilder.group({}); // formgroup inside workSummary formarray is created
      this.itemConfig.forEach((configItem, index) => {   // Formcontrol created using key_name from config
        const keyName = configItem.key_name;
        let controlValue = keyName === 'lineItemNo' ? (index + 1) : '';
        const validators = configItem.is_mandatory == 1 ? [Validators.required] : null;
        workSummaryFormGroup.addControl(keyName, this.formBuilder.control(controlValue, validators));
      });
      workSummaryFormGroup.get('description').setValue(this.form.get("milestoneName").value)

      this.getFteTotalValue()
      // workSummaryFormGroup.addControl('quantity', this.formBuilder.control(1));
      workSummaryFormGroup.addControl('taxId', this.formBuilder.control(this.prefilldata.tax_id || null));
      workSummaryFormGroup.addControl('taxPercentage', this.formBuilder.control(this.prefilldata.tax_percentage || null));
      workSummaryFormGroup.get('tax')?.setValue(this.prefilldata.tax_description || null);
      workSummaryFormGroup.addControl('taxType', this.formBuilder.control(this.prefilldata.tax_type || null));
      workSummaryFormGroup.addControl('chargeDescription', this.formBuilder.control(''));
      workSummaryFormGroup.addControl('chargeType', this.formBuilder.control(''));
      workSummaryFormGroup.addControl('chargeTypeName', this.formBuilder.control(''));
      let rate = this.parseValue(workSummaryFormGroup.get('rate').value);
      let quantity;
      if(workSummaryFormGroup?.get('quantity')){
        quantity = this.parseValue(workSummaryFormGroup.get('quantity')?.value);
        workSummaryFormGroup.get('amount').setValue(this.fixNumberOnUI((quantity * rate), currency));
      }
      else{
        workSummaryFormGroup.get('amount').setValue(this.fixNumberOnUI((rate), currency));
      }
      workSummaryArray.push(workSummaryFormGroup);
      let amount = this.parseValue(workSummaryFormGroup.get('amount').value);
      let id = workSummaryFormGroup.get('taxId')?.value;
      let type = workSummaryFormGroup.get('taxType')?.value;
      if (workSummaryFormGroup.get('taxId')?.value) {
        if(type.toLowerCase() == 'tax'){
          let item = this.taxMaster.find(obj => obj.id === id);
          workSummaryFormGroup.addControl('taxDetails', new FormControl(item));
          this.populateTaxArray(item, "tax", amount, 1)
        }
        else{
          let item = this.groupTaxMaster.find(obj => obj.id === id);
          workSummaryFormGroup.addControl('taxDetails', new FormControl(item));
          this.populateTaxArray(item, "group", amount, 1)
        }
      }
      this.updateSubtotal();
      this.updateTotal();


      // When rate changes
      workSummaryFormGroup.get('rate')?.valueChanges.subscribe(() => {
        let currency = this.form.get('currency').value;
        const rate = this.parseValue(workSummaryFormGroup.get('rate').value);
        let quantity;
        if(workSummaryFormGroup?.get('quantity')){
          quantity = this.parseValue(workSummaryFormGroup.get('quantity')?.value);
          workSummaryFormGroup.get('amount').setValue(this.fixNumberOnUI((quantity * rate), currency));
        }
        else{
          workSummaryFormGroup.get('amount').setValue(this.fixNumberOnUI((rate), currency));
        }
        this.updateSubtotal();
        this.updateTaxAmountonRateQuantityChange();
        this.updateTotalTaxAmount();
        this.updateTdsTcs();
        this.updateTotal();
        if (workSummaryFormGroup.get('taxType').value == "tax") {
          let lineItemno = workSummaryFormGroup.get('lineItemNo')?.value;
          let id = workSummaryFormGroup.get('taxId')?.value;
          let item = this.taxMaster.find(obj => obj.id === id);
          let amount = this.parseValue(workSummaryFormGroup.get('amount')?.value);
          this.populateTaxArray(item, "tax", amount, lineItemno)
        }
        if (workSummaryFormGroup.get('taxType').value == "group") {
          let lineItemno = workSummaryFormGroup.get('lineItemNo')?.value;
          let id = workSummaryFormGroup.get('taxId')?.value;
          let item = this.groupTaxMaster.find(obj => obj.id === id);
          let amount = this.parseValue(workSummaryFormGroup.get('amount')?.value);
          this.populateTaxArray(item, "group", amount, lineItemno)
        }
      });

      // When quantity changes
      workSummaryFormGroup.get('quantity')?.valueChanges.subscribe(() => {
        let currency = this.form.get('currency').value;
        const rate = this.parseValue(workSummaryFormGroup.get('rate').value);
        let quantity;
        if(workSummaryFormGroup?.get('quantity')){
          quantity = this.parseValue(workSummaryFormGroup.get('quantity')?.value);
          workSummaryFormGroup.get('amount').setValue(this.fixNumberOnUI((quantity * rate), currency));
        }
        else{
          workSummaryFormGroup.get('amount').setValue(this.fixNumberOnUI((rate), currency));
        }

        this.updateSubtotal();
        this.updateTaxAmountonRateQuantityChange();
        this.updateTdsTcs();
        this.updateTotalTaxAmount();
        this.updateTdsTcs();
        this.updateTotal();
        if (workSummaryFormGroup.get('taxType').value == "tax") {
          let lineItemno = workSummaryFormGroup.get('lineItemNo')?.value;
          let id = workSummaryFormGroup.get('taxId')?.value;
          let item = this.taxMaster.find(obj => obj.id === id);
          let amount = this.parseValue(workSummaryFormGroup.get('amount')?.value);
          this.populateTaxArray(item, "tax", amount, lineItemno)
        }
        if (workSummaryFormGroup.get('taxType').value == "group") {
          let lineItemno = workSummaryFormGroup.get('lineItemNo')?.value;
          let id = workSummaryFormGroup.get('taxId')?.value;
          let item = this.groupTaxMaster.find(obj => obj.id === id);
          let amount = this.parseValue(workSummaryFormGroup.get('amount')?.value);
          this.populateTaxArray(item, "group", amount, lineItemno)
        }
      });

      this.getFteTotalValue();
      this.updateSubtotal();
      this.updateTotal();
      this.updateTdsTcs();
      this.updateTotalTaxAmount();
      this.show = true;

      }

    }
    if (this.form.get("serviceTypeGroupId")?.value == 3) {
      const workSummaryArray = this.form.get('workSummary') as FormArray;
      workSummaryArray.clear();
      const workSummaryFormGroup = this.formBuilder.group({}); // formgroup inside workSummary formarray is created
      this.itemConfig.forEach((configItem, index) => {   // Formcontrol created using key_name from config
        const keyName = configItem.key_name;
        let controlValue = keyName === 'lineItemNo' ? (index + 1) : '';
        const validators = configItem.is_mandatory == 1 ? [Validators.required] : null;
        workSummaryFormGroup.addControl(keyName, this.formBuilder.control(controlValue, validators));
      });

      let rate = this.parseValue(workSummaryFormGroup.get('rate').value);
      let hours = this.parseValue(workSummaryFormGroup.get('quantity')?.value);
      let extraHours = this.parseValue(workSummaryFormGroup.get('extraHours')?.value);
      if (!isNaN(hours) && !isNaN(rate) && !isNaN(extraHours)) {
        workSummaryFormGroup.get('amount').setValue(this.fixNumberOnUI((extraHours + hours) * rate, currency));
      } else {
        workSummaryFormGroup.get('amount').setValue(0.00);
      }
      workSummaryFormGroup.get('description').setValue(this.form.get("milestoneName").value)
      // workSummaryFormGroup.addControl('quantity', this.formBuilder.control(1));
      workSummaryFormGroup.addControl('taxId', this.formBuilder.control(this.prefilldata.tax_id || null));
      workSummaryFormGroup.addControl('taxPercentage', this.formBuilder.control(this.prefilldata.tax_percentage || null));
      workSummaryFormGroup.get('tax')?.setValue(this.prefilldata.tax_description || null);
      workSummaryFormGroup.addControl('taxType', this.formBuilder.control(this.prefilldata?.tax_type || null));
      workSummaryFormGroup.addControl('chargeDescription', this.formBuilder.control(''));
      workSummaryFormGroup.addControl('chargeType', this.formBuilder.control(''));
      workSummaryFormGroup.addControl('chargeTypeName', this.formBuilder.control(''));
      workSummaryArray.push(workSummaryFormGroup);
      let amount = this.parseValue(workSummaryFormGroup.get('amount').value);
      let id = workSummaryFormGroup.get('taxId')?.value;
      let type = workSummaryFormGroup.get('taxType')?.value;
      if (workSummaryFormGroup.get('taxId')?.value) {
        if(type.toLowerCase() == 'tax'){
          let item = this.taxMaster.find(obj => obj.id === id);
          workSummaryFormGroup.addControl('taxDetails', new FormControl(item));
          this.populateTaxArray(item, "tax", amount, 1)
        }
        else{
          let item = this.groupTaxMaster.find(obj => obj.id === id);
          workSummaryFormGroup.addControl('taxDetails', new FormControl(item));
          this.populateTaxArray(item, "group", amount, 1)
        }
      }
      this.updateSubtotal();
      this.updateTotalTaxAmount();
      this.updateTotal();
      this.show = true;

      // When rate changes
      workSummaryFormGroup.get('rate').valueChanges.subscribe(() => {
        let currency = this.form.get('currency').value;
        let rate = this.parseValue(workSummaryFormGroup.get('rate').value);
        let hours = this.parseValue(workSummaryFormGroup.get('quantity')?.value);
        let extraHours = this.parseValue(workSummaryFormGroup.get('extraHours')?.value);
        if (!isNaN(hours) && !isNaN(rate) && !isNaN(extraHours)) {
          workSummaryFormGroup.get('amount').setValue(this.fixNumberOnUI((extraHours + hours) * rate, currency));
        }
        else if (!isNaN(hours) && !isNaN(rate)) {
          workSummaryFormGroup.get('amount').setValue(this.fixNumberOnUI((hours) * rate, currency));
        }
        else {
          workSummaryFormGroup.get('amount').setValue(0.00);
        }
        this.updateSubtotal();
        this.updateTaxAmountonRateQuantityChange();
        this.updateTotalTaxAmount();
        this.updateTdsTcs();
        this.updateTotal();
        if (workSummaryFormGroup.get('taxType').value == "tax") {
          let lineItemno = workSummaryFormGroup.get('lineItemNo')?.value;
          let id = workSummaryFormGroup.get('taxId')?.value;
          let item = this.taxMaster.find(obj => obj.id === id);
          let amount = this.parseValue(workSummaryFormGroup.get('amount')?.value);
          this.populateTaxArray(item, "tax", amount, lineItemno)
        }
        if (workSummaryFormGroup.get('taxType').value == "group") {
          let lineItemno = workSummaryFormGroup.get('lineItemNo')?.value;
          let id = workSummaryFormGroup.get('taxId')?.value;
          let item = this.groupTaxMaster.find(obj => obj.id === id);
          let amount = this.parseValue(workSummaryFormGroup.get('amount')?.value);
          this.populateTaxArray(item, "group", amount, lineItemno)
        }
      });

      // When hours changes
      workSummaryFormGroup.get('quantity')?.valueChanges.subscribe(() => {
        let currency = this.form.get('currency').value;
        let rate = this.parseValue(workSummaryFormGroup.get('rate').value);
        let hours = this.parseValue(workSummaryFormGroup.get('quantity')?.value);
        let extraHours = this.parseValue(workSummaryFormGroup.get('extraHours')?.value);
        if (!isNaN(hours) && !isNaN(rate) && !isNaN(extraHours)) {
          workSummaryFormGroup.get('amount').setValue(this.fixNumberOnUI((extraHours + hours) * rate, currency));
        }
        else if (!isNaN(hours) && !isNaN(rate)) {
          workSummaryFormGroup.get('amount').setValue(this.fixNumberOnUI((hours) * rate, currency));
        }
        else {
          workSummaryFormGroup.get('amount').setValue(0.00);
        }
        this.updateSubtotal();
        this.updateTaxAmountonRateQuantityChange();
        this.updateTotalTaxAmount();
        this.updateTdsTcs();
        this.updateTotal();
        if (workSummaryFormGroup.get('taxType').value == "tax") {
          let lineItemno = workSummaryFormGroup.get('lineItemNo')?.value;
          let id = workSummaryFormGroup.get('taxId')?.value;
          let item = this.taxMaster.find(obj => obj.id === id);
          let amount = this.parseValue(workSummaryFormGroup.get('amount')?.value);
          this.populateTaxArray(item, "tax", amount, lineItemno)
        }
        if (workSummaryFormGroup.get('taxType').value == "group") {
          let lineItemno = workSummaryFormGroup.get('lineItemNo')?.value;
          let id = workSummaryFormGroup.get('taxId')?.value;
          let item = this.groupTaxMaster.find(obj => obj.id === id);
          let amount = this.parseValue(workSummaryFormGroup.get('amount')?.value);
          this.populateTaxArray(item, "group", amount, lineItemno)
        }
      });

      // When extra hours changes
      workSummaryFormGroup.get('extraHours')?.valueChanges.subscribe(() => {
        let currency = this.form.get('currency').value;
        let rate = this.parseValue(workSummaryFormGroup.get('rate').value);
        let hours = this.parseValue(workSummaryFormGroup.get('quantity')?.value);
        let extraHours = this.parseValue(workSummaryFormGroup.get('extraHours')?.value);
        if (!isNaN(hours) && !isNaN(rate) && !isNaN(extraHours)) {
          workSummaryFormGroup.get('amount').setValue(this.fixNumberOnUI((extraHours + hours) * rate, currency));
        }
        else if (!isNaN(hours) && !isNaN(rate)) {
          workSummaryFormGroup.get('amount').setValue(this.fixNumberOnUI((hours) * rate, currency));
        }
        else {
          workSummaryFormGroup.get('amount').setValue(0.00);
        }
        this.updateSubtotal();
        this.updateTaxAmountonRateQuantityChange();
        this.updateTotalTaxAmount();
        this.updateTdsTcs();
        this.updateTotal();
        if (workSummaryFormGroup.get('taxType').value == "tax") {
          let lineItemno = workSummaryFormGroup.get('lineItemNo')?.value;
          let id = workSummaryFormGroup.get('taxId')?.value;
          let item = this.taxMaster.find(obj => obj.id === id);
          let amount = this.parseValue(workSummaryFormGroup.get('amount')?.value);
          this.populateTaxArray(item, "tax", amount, lineItemno)
        }
        if (workSummaryFormGroup.get('taxType').value == "group") {
          let lineItemno = workSummaryFormGroup.get('lineItemNo')?.value;
          let id = workSummaryFormGroup.get('taxId')?.value;
          let item = this.groupTaxMaster.find(obj => obj.id === id);
          let amount = this.parseValue(workSummaryFormGroup.get('amount')?.value);
          this.populateTaxArray(item, "group", amount, lineItemno)
        }
      });

    }
    if (this.form.get("serviceTypeGroupId")?.value == 4) {
      const workSummaryArray = this.form.get('workSummary') as FormArray;
      workSummaryArray.clear();

      if (this.subLineItemdata && this.subLineItemdata.length > 0) {
        this.subLineItemdata.forEach((subLineItem, index) => {
          const workSummaryFormGroup = this.formBuilder.group({});

          this.itemConfig.forEach(configItem => {
            const keyName = configItem.key_name;
            let controlValue = null;
            // let controlValue = keyName === 'lineItemNo' ? (index + 1) : '';
            switch (keyName) {
              case 'lineItemNo':
                controlValue = index + 1;
                break;
              case 'description':
                controlValue = subLineItem.description != null ? subLineItem.description : "";
                break;
              case 'quantity':
                controlValue = subLineItem.quantity ? subLineItem.quantity : 1;
                break;
              case 'rate':
                const selectedCurrency = this.form.get('currency').value; // Replace 'currencyControlName' with the actual control name for the selected currency
                // const matchingCurrency = subLineItem?.milestone_value?.find(currency => currency.currency_code === selectedCurrency);
                controlValue = subLineItem.rate ? subLineItem.rate: 0.00;
                break;
              case 'productServiceType':
                controlValue = "";
                break;
                case 'chargeType':
                  controlValue = "";
                  break;
            }
            
            const validators = configItem.is_mandatory == 1 ? [Validators.required] : null;
            workSummaryFormGroup.addControl(keyName, this.formBuilder.control(controlValue, validators));
          });
          const quantity = this.parseValue(workSummaryFormGroup.get('quantity').value);
          const rate = this.parseValue(workSummaryFormGroup.get('rate').value);
          workSummaryFormGroup.get('amount').setValue(this.fixNumberOnUI((quantity * rate), this.form.get('currency').value));

          workSummaryFormGroup.addControl('rpaId', this.formBuilder.control(subLineItem?.id));
          workSummaryFormGroup.addControl('productServiceTypeId', this.formBuilder.control(''));
          workSummaryFormGroup.addControl('taxId', this.formBuilder.control(this.prefilldata.tax_id || null));
          workSummaryFormGroup.addControl('taxPercentage', this.formBuilder.control(this.prefilldata.tax_percentage || null));
          workSummaryFormGroup.get('tax')?.setValue(this.prefilldata.tax_description || null);
          workSummaryFormGroup.addControl('taxType', this.formBuilder.control(this.prefilldata?.tax_type || null));
          workSummaryFormGroup.addControl('chargeDescription', this.formBuilder.control(''));
          workSummaryFormGroup.addControl('chargeType', this.formBuilder.control(''));
          workSummaryFormGroup.addControl('chargeTypeName', this.formBuilder.control(''));
          let amount = this.parseValue(workSummaryFormGroup.get('amount').value);
          let id = workSummaryFormGroup.get('taxId')?.value;
          let type = workSummaryFormGroup.get('taxType')?.value;
          let lineItemNo = workSummaryFormGroup.get('lineItemNo')?.value;
          if (workSummaryFormGroup.get('taxId')?.value) {
            if(type.toLowerCase() == 'tax'){
              let item = this.taxMaster.find(obj => obj.id === id);
              workSummaryFormGroup.addControl('taxDetails', new FormControl(item));
              this.populateTaxArray(item, "tax", amount, lineItemNo)
            }
            else{
              let item = this.groupTaxMaster.find(obj => obj.id === id);
              workSummaryFormGroup.addControl('taxDetails', new FormControl(item));
              this.populateTaxArray(item, "group", amount, lineItemNo)
            }
          }
          workSummaryArray.push(workSummaryFormGroup);
          this.updateSubtotal();
          this.aggregateTax();
          this.updateTotalTaxAmount();
          this.updateTdsTcs();
          this.updateTotal();
          // this.aggregateTax()
          this.show = true;


          // When rate changes
          workSummaryFormGroup.get('rate').valueChanges.subscribe(() => {
            let currency = this.form.get('currency').value;
            const quantity = this.parseValue(workSummaryFormGroup.get('quantity').value);
            const rate = this.parseValue(workSummaryFormGroup.get('rate').value);
            workSummaryFormGroup.get('amount').setValue(this.fixNumberOnUI((quantity * rate), currency));
            this.updateSubtotal();
            this.updateTaxAmountonRateQuantityChange();
            this.updateTdsTcs();
            this.updateTotalTaxAmount();
            this.updateTotal();
            if (workSummaryFormGroup.get('taxType').value == "tax") {
              let lineItemno = workSummaryFormGroup.get('lineItemNo')?.value;
              let id = workSummaryFormGroup.get('taxId')?.value;
              let item = this.taxMaster.find(obj => obj.id === id);
              let amount = this.parseValue(workSummaryFormGroup.get('amount')?.value);
              this.populateTaxArray(item, "tax", amount, lineItemno)
            }
            if (workSummaryFormGroup.get('taxType').value == "group") {
              let lineItemno = workSummaryFormGroup.get('lineItemNo')?.value;
              let id = workSummaryFormGroup.get('taxId')?.value;
              let item = this.groupTaxMaster.find(obj => obj.id === id);
              let amount = this.parseValue(workSummaryFormGroup.get('amount')?.value);
              this.populateTaxArray(item, "group", amount, lineItemno)
            }
          });

          // When quantity changes
          workSummaryFormGroup.get('quantity').valueChanges.subscribe(() => {
            let currency = this.form.get('currency').value;
            const quantity = this.parseValue(workSummaryFormGroup.get('quantity').value);
            const rate = this.parseValue(workSummaryFormGroup.get('rate').value);
            workSummaryFormGroup.get('amount').setValue(this.fixNumberOnUI((quantity * rate), currency));
            this.updateSubtotal();
            this.updateTaxAmountonRateQuantityChange();
            this.updateTdsTcs();
            this.updateTotalTaxAmount();
            this.updateTotal();
            if (workSummaryFormGroup.get('taxType').value == "tax") {
              let lineItemno = workSummaryFormGroup.get('lineItemNo')?.value;
              let id = workSummaryFormGroup.get('taxId')?.value;
              let item = this.taxMaster.find(obj => obj.id === id);
              let amount = this.parseValue(workSummaryFormGroup.get('amount')?.value);
              this.populateTaxArray(item, "tax", amount, lineItemno)
            }
            if (workSummaryFormGroup.get('taxType').value == "group") {
              let lineItemno = workSummaryFormGroup.get('lineItemNo')?.value;
              let id = workSummaryFormGroup.get('taxId')?.value;
              let item = this.groupTaxMaster.find(obj => obj.id === id);
              let amount = this.parseValue(workSummaryFormGroup.get('amount')?.value);
              this.populateTaxArray(item, "group", amount, lineItemno)
            }
          });

        });

      }
      else {
        // If subLineItemdata is empty or null, add an empty form group to the FormArray
        const workSummaryFormGroup = this.formBuilder.group({});
        this.itemConfig.forEach((configItem, index) => {   // Formcontrol created using key_name from config
          const keyName = configItem.key_name;
          let controlValue = keyName === 'lineItemNo' ? (index + 1) : '';
          const validators = configItem.is_mandatory == 1 ? [Validators.required] : null;
          workSummaryFormGroup.addControl(keyName, this.formBuilder.control(controlValue, validators));
        });
        workSummaryFormGroup.addControl('productServiceTypeId', this.formBuilder.control(''));
        workSummaryFormGroup.addControl('taxId', this.formBuilder.control(this.prefilldata.tax_id || null));
        workSummaryFormGroup.addControl('taxPercentage', this.formBuilder.control(this.prefilldata.tax_percentage || null));
        workSummaryFormGroup.addControl('chargeDescription', this.formBuilder.control(''));
        workSummaryFormGroup.addControl('chargeType', this.formBuilder.control(''));
        workSummaryFormGroup.addControl('chargeTypeName', this.formBuilder.control(''));
        // workSummaryFormGroup.addControl('tax', this.formBuilder.control(this.prefilldata.tax_description || null));
        workSummaryFormGroup.get('tax')?.setValue(this.prefilldata.tax_description || null);
        // workSummaryFormGroup.get('tax').setValue(this.prefilldata.tax_description);
        workSummaryFormGroup.addControl('taxType', this.formBuilder.control(this.prefilldata?.tax_type || null));
        let amount = this.parseValue(workSummaryFormGroup.get('amount').value);
        let id = workSummaryFormGroup.get('taxId')?.value;
        let type = workSummaryFormGroup.get('taxType')?.value;
        if (workSummaryFormGroup.get('taxId')?.value) {
          if(type.toLowerCase() == 'tax'){
            let item = this.taxMaster.find(obj => obj.id === id);
            workSummaryFormGroup.addControl('taxDetails', new FormControl(item));
            this.populateTaxArray(item, "tax", amount, 1)
          }
          else{
            let item = this.groupTaxMaster.find(obj => obj.id === id);
            workSummaryFormGroup.addControl('taxDetails', new FormControl(item));
            this.populateTaxArray(item, "group", amount, 1)
          }
        }

        workSummaryArray.push(workSummaryFormGroup);

        // let amount = workSummaryFormGroup.get('amount').value;
        // let id = workSummaryFormGroup.get('taxId')?.value;
        // if (workSummaryFormGroup.get('taxId')?.value) {
        //   let item = this.taxMaster.find(obj => obj.id === id);
        //   this.populateTaxArray(item, "tax", amount, 1)
        // }
        this.show = true;

        // When rate changes
        workSummaryFormGroup.get('rate')?.valueChanges.subscribe(() => {
          let currency = this.form.get('currency').value;
          const quantity = this.parseValue(workSummaryFormGroup.get('quantity').value);
          const rate = this.parseValue(workSummaryFormGroup.get('rate').value);
          workSummaryFormGroup.get('amount').setValue(this.fixNumberOnUI((quantity * rate), currency));
          this.updateSubtotal();
          this.updateTaxAmountonRateQuantityChange();
          this.updateTotalTaxAmount();
          this.updateTdsTcs();
          this.updateTotal();
          if (workSummaryFormGroup.get('taxType').value == "tax") {
            let lineItemno = workSummaryFormGroup.get('lineItemNo')?.value;
            let id = workSummaryFormGroup.get('taxId')?.value;
            let item = this.taxMaster.find(obj => obj.id === id);
            let amount = this.parseValue(workSummaryFormGroup.get('amount')?.value);
            this.populateTaxArray(item, "tax", amount, lineItemno)
          }
          if (workSummaryFormGroup.get('taxType').value == "group") {
            let lineItemno = workSummaryFormGroup.get('lineItemNo')?.value;
            let id = workSummaryFormGroup.get('taxId')?.value;
            let item = this.groupTaxMaster.find(obj => obj.id === id);
            let amount = this.parseValue(workSummaryFormGroup.get('amount')?.value);
            this.populateTaxArray(item, "group", amount, lineItemno)
          }
          this.aggregateTax()
        });

        // When quantity changes
        workSummaryFormGroup.get('quantity')?.valueChanges.subscribe(() => {
          let currency = this.form.get('currency').value;
          const quantity = this.parseValue(workSummaryFormGroup.get('quantity').value);
          const rate = this.parseValue(workSummaryFormGroup.get('rate').value);
          workSummaryFormGroup.get('amount').setValue(this.fixNumberOnUI((quantity * rate), currency));
          this.updateSubtotal();
          this.updateTaxAmountonRateQuantityChange();
          this.updateTdsTcs();
          this.updateTotalTaxAmount();
          this.updateTotal();
          if (workSummaryFormGroup.get('taxType').value == "tax") {
            let lineItemno = workSummaryFormGroup.get('lineItemNo')?.value;
            let id = workSummaryFormGroup.get('taxId')?.value;
            let item = this.taxMaster.find(obj => obj.id === id);
            let amount = this.parseValue(workSummaryFormGroup.get('amount')?.value);
            this.populateTaxArray(item, "tax", amount, lineItemno)
          }
          if (workSummaryFormGroup.get('taxType').value == "group") {
            let lineItemno = workSummaryFormGroup.get('lineItemNo')?.value;
            let id = workSummaryFormGroup.get('taxId')?.value;
            let item = this.groupTaxMaster.find(obj => obj.id === id);
            let amount = this.parseValue(workSummaryFormGroup.get('amount')?.value);
            this.populateTaxArray(item, "group", amount, lineItemno)
          }
        });
      }

    }

    this.botService.spinnerService.hide();
  }

  // To add the additional line items in workSummary
  addWorkSummary() {
    const workSummaryArray = this.form.get('workSummary') as FormArray;

    const workSummaryFormGroup = this.formBuilder.group({});
    const index = workSummaryArray.length;
    this.itemConfig.forEach((configItem) => {
      const keyName = configItem.key_name;
      let initialValue = keyName === 'lineItemNo' ? (index + 1) : null;
      const validators = configItem.is_mandatory == 1 ? [Validators.required] : null;
      workSummaryFormGroup.addControl(keyName, this.formBuilder.control(initialValue, validators));
    });

    workSummaryFormGroup.get('rate')?.valueChanges.subscribe(() => {
      const quantity = this.parseValue(workSummaryFormGroup.get('quantity').value);
      const rate = this.parseValue(workSummaryFormGroup.get('rate').value);
      workSummaryFormGroup.get('amount').setValue(this.fixNumberOnUI((quantity * rate), this.form.get('currency').value));
      this.updateSubtotal();
      this.updateTotalTaxAmount();
      this.updateTaxAmountonRateQuantityChange();
      this.updateTdsTcs();
      this.updateTotal();
      if (workSummaryFormGroup.get('taxType').value == "tax") {
        let lineItemno = workSummaryFormGroup.get('lineItemNo')?.value;
        let id = workSummaryFormGroup.get('taxId')?.value;
        let item = this.taxMaster.find(obj => obj.id === id);
        let amount = this.parseValue(workSummaryFormGroup.get('amount')?.value);
        this.populateTaxArray(item, "tax", amount, lineItemno)
      }
      if (workSummaryFormGroup.get('taxType').value == "group") {
        let lineItemno = workSummaryFormGroup.get('lineItemNo')?.value;
        let id = workSummaryFormGroup.get('taxId')?.value;
        let item = this.groupTaxMaster.find(obj => obj.id === id);
        let amount = this.parseValue(workSummaryFormGroup.get('amount')?.value);
        this.populateTaxArray(item, "group", amount, lineItemno)
      }
    });

    workSummaryFormGroup.get('quantity')?.valueChanges.subscribe(() => {
      const quantity = this.parseValue(workSummaryFormGroup.get('quantity').value);
      const rate = this.parseValue(workSummaryFormGroup.get('rate').value);
      workSummaryFormGroup.get('amount').setValue(quantity * rate);
      this.updateSubtotal();
      this.updateTotalTaxAmount();
      this.updateTaxAmountonRateQuantityChange();
      this.updateTdsTcs();
      this.updateTotal();
      if (workSummaryFormGroup.get('taxType').value == "tax") {
        let lineItemno = workSummaryFormGroup.get('lineItemNo')?.value;
        let id = workSummaryFormGroup.get('taxId')?.value;
        let item = this.taxMaster.find(obj => obj.id === id);
        let amount = this.parseValue(workSummaryFormGroup.get('amount')?.value);
        this.populateTaxArray(item, "tax", amount, lineItemno)
      }
      if (workSummaryFormGroup.get('taxType').value == "group") {
        let lineItemno = workSummaryFormGroup.get('lineItemNo')?.value;
        let id = workSummaryFormGroup.get('taxId')?.value;
        let item = this.groupTaxMaster.find(obj => obj.id === id);
        let amount = this.parseValue(workSummaryFormGroup.get('amount')?.value);
        this.populateTaxArray(item, "group", amount, lineItemno)
      }
    });

    workSummaryArray.push(workSummaryFormGroup);
    workSummaryFormGroup.addControl('taxId', this.formBuilder.control(null));
    workSummaryFormGroup.addControl('taxPercentage', this.formBuilder.control(null));
    workSummaryFormGroup.addControl('taxType', this.formBuilder.control(null));
    workSummaryFormGroup.addControl('chargeType', this.formBuilder.control(null));
    workSummaryFormGroup.addControl('chargeDescription', this.formBuilder.control(null));
    workSummaryFormGroup.addControl('chargeTypeName', this.formBuilder.control(null));
  }

  get workSummaryArray(): FormArray {
    return this.form.get('workSummary') as FormArray;
  }

  // To delete the line items in workSummary
  deleteWorkSummary(index: number): void {
    const workSummaryArray = this.form.get('workSummary') as FormArray;
    workSummaryArray.removeAt(index);
    for (let i = index; i < workSummaryArray.length; i++) {
      const workSummaryFormGroup = workSummaryArray.at(i) as FormGroup;
      workSummaryFormGroup.get('lineItemNo').setValue(i + 1);
    }
    this.updateSubtotal()
    this.deleteTax(index)
  }

  // To update subtotal 
  updateSubtotal(): void {
    const workSummaryArray = this.form.get('workSummary') as FormArray;
    let subtotal = 0;
    let id;
    let taxType;
    let lineItemNo;

    for (let workSummary of workSummaryArray.controls) {
      // const amount = parseFloat(workSummary.get('amount').value.replace(/,/g, ''));
      const amount = this.parseValue(workSummary.get('amount').value)
      subtotal += amount;
      const chargeTypeControl = workSummary.get('chargeTypeName').value;
      if(chargeTypeControl  == "minus"){
      subtotal -= amount;
      }
      else{
        subtotal += amount; 
      }
      id = workSummary.get('taxId')?.value;
      taxType = workSummary.get('taxType')?.value;
      lineItemNo = workSummary.get('lineItemNo')?.value;
    }
    // Update the subtotal form control value
    this.form.get('subTotal').setValue(subtotal);

    if (id && workSummaryArray.length == 1) {
      if (taxType) {
        if (taxType == 'tax') {
          let item = this.taxMaster.find(obj => obj.id === id);
          this.populateTaxArray(item, "tax", subtotal, 1)
        }
        else if (taxType == 'group') {
          let item = this.groupTaxMaster.find(obj => obj.id === id);
          this.populateTaxArray(item, "group", subtotal, 1)
        }
      }
      else {
        let item = this.taxMaster.find(obj => obj.id === id);
        this.populateTaxArray(item, "tax", subtotal, 1)
      }
    }
    if (this.form.get('serviceTypeGroupId').value == 4) {
      this.aggregateTax()
    }


    this.updateTotal()

  }
  // To update total tax amount 
  updateTotalTaxAmount(): void {
    let totalTaxAmount = 0;

    for (let tax of this.taxresultArray) {
      const amount = tax.amount;
      totalTaxAmount = totalTaxAmount + amount;
    }

    // Update the subtotal form control value
    this.form.get('totalTaxAmount').setValue(totalTaxAmount);
  }

  // To update discount
  updateDiscount(): void {
    const discountAmount = this.form.get('discountInputAmount').value;
    const subtotal = this.form.get('subTotal').value;
    const discountAmountType = this.form.get('discountAmountType').value;
    let calculatedDiscount: number = 0;
    if (discountAmount != 0 && discountAmount != "") {
      if (discountAmountType === 'percentage') {
        calculatedDiscount = subtotal * (discountAmount / 100);
      } else if (discountAmountType === 'rupees') {
        calculatedDiscount = discountAmount;
      } else {
        calculatedDiscount = 0;
      }
    }
    this.form.get('discountAmount').setValue(calculatedDiscount);

  }
  // To update retention
  updateRetention(): void {
    const retentionAmount = this.form.get('retentionInputAmount').value;
    const subtotal = this.form.get('subTotal').value;
    const retentionAmountType = this.form.get('retentionAmountType').value;

    let calculatedRetention: number = 0;
    if (retentionAmount != 0 && retentionAmount != "") {
      if (retentionAmountType === 'percentage') {
        calculatedRetention = subtotal * (retentionAmount / 100);
      } else if (retentionAmountType === 'rupees') {
        calculatedRetention = retentionAmount;
      } else {
        calculatedRetention = 0;
      }

    }
    this.form.get('retentionAmount').setValue(calculatedRetention);

  }

  // To update total amount
  async updateTotal() {
    let subtotal = this.parseValue(this.form.get('subTotal').value);
    let discountAmount = this.parseValue(this.form.get('discountAmount').value);
    let retentionAmount = this.form.get('retentionAmount').value;
    let totalTaxAmount = this.parseValue(this.form.get('totalTaxAmount').value);
    let calculateTotal = 0;
    if (!isNaN(subtotal) && !isNaN(discountAmount) && !isNaN(totalTaxAmount)) {
      calculateTotal = subtotal - discountAmount + totalTaxAmount
    }
    else if (!isNaN(subtotal) && !isNaN(totalTaxAmount)) {
      calculateTotal = subtotal + totalTaxAmount
    }
    else if (!isNaN(subtotal) && !isNaN(discountAmount)) {
      calculateTotal = subtotal - discountAmount
    }
    else {
      calculateTotal = subtotal
    }
    let totalTdsTcsExclusive = calculateTotal
    this.form.get('totalTdsTcsExclusive')?.setValue(totalTdsTcsExclusive);
    let tdsOrTcs = this.form.get('selectedOption')?.value
    let tdsValue = this.parseValue(this.form.get('tdsValue')?.value)
    let tcsValue = this.parseValue(this.form.get('tcsValue')?.value)
    if (tdsOrTcs == 'tds')
      calculateTotal = calculateTotal - tdsValue
    if (tdsOrTcs == 'tcs')
      calculateTotal = calculateTotal + tcsValue

    // calculateTotal = subtotal - discountAmount + totalTaxAmount
    this.form.get('totalAmount').setValue(calculateTotal);
    if (calculateTotal != 0) {
      let amountInWords = await this.CurrencyDetails()
      this.form.get('totalAmountInwords').setValue(amountInWords)
    }


      this.calculatePoForAlert()


  }

  // To trigger the corresponding function whenever there is value change in respective formcontrols
  setupValueChanges(): void {


    const discountInputAmountControl = this.form.get('discountInputAmount');
    const discountAmountTypeControl = this.form.get('discountAmountType');

    discountAmountTypeControl.valueChanges.subscribe(type => {
      if (type === 'percentage') {
        discountInputAmountControl.setValidators([Validators.min(0)]);
      } else if (type === 'rupees') {
        discountInputAmountControl.setValidators([Validators.min(0)]);
      }

      // Clear any previous validators and update the control's validation
      discountInputAmountControl.updateValueAndValidity();
    });

    const retentionInputAmountControl = this.form.get('retentionInputAmount');
    const retentionAmountTypeControl = this.form.get('retentionAmountType');

    retentionAmountTypeControl.valueChanges.subscribe(type => {
      if (type === 'percentage') {
        retentionInputAmountControl.setValidators([Validators.min(0)]);
      } else if (type === 'rupees') {
        retentionInputAmountControl.setValidators([Validators.min(0)]);
      }

      // Clear any previous validators and update the control's validation
      retentionInputAmountControl.updateValueAndValidity();
    });


    this.form.get('discountInputAmount')?.valueChanges.subscribe(() => {
      this.updateDiscount();
      this.updateTdsTcs();
      this.updateTotal();
    });

    this.form.get('subTotal')?.valueChanges.subscribe(() => {
      this.updateDiscount();
      this.updateRetention();
      this.updateTotalTaxAmount();
      this.updateTdsTcs();
      this.updateTotal();
    });

    this.form.get('discountAmountType')?.valueChanges.subscribe((value) => {
      this.updateDiscount();
      this.updateTdsTcs();
      this.updateTotal();
      this.clearInvalidValue();
    });

    this.form.get('retentionAmountType')?.valueChanges.subscribe(() => {
      this.updateRetention();
      this.updateTdsTcs();
      this.updateTotal();
      this.clearRetentionInvalidValue();
    });

    this.form.get('retentionInputAmount')?.valueChanges.subscribe(() => {
      this.updateRetention();
      this.updateTdsTcs();
      this.updateTotal();
    });

    this.form.get('invoiceDate')?.valueChanges.subscribe(() => {
      this.getDueDate();
      this.getConversionRateForDate();
    });



    this.form.get('paymentTerm')?.valueChanges.pipe(
      debounceTime(200) // Adjust the delay time (in milliseconds) as needed
    ).subscribe(() => {
      this.getDueDate();
    });


    this.form.get('invoiceTemplateName')?.valueChanges.pipe(
      debounceTime(500) // Adjust the delay time (in milliseconds) as needed
    ).subscribe(() => {
      this.getServiceGroupId();
    });

    this.form.get('currency')?.valueChanges.subscribe((value) => {
      this.convertRatesBasedOnCurrency();
      this.changeWorkSummaryCurrency();
      this.changeFTECurrency();
      this.getCurrencySymbol(value);
      this.clearRetentionInvalidValue()
    });


    this.form.valueChanges.subscribe(() => {
      this.emitFormValidity();
      this.onFormValueChanges.emit({ form: this.form, stepIndex: this.stepIndex });
    });

  }

  // To calculate tax amount for every line item tax
  calculateTaxAmount(item, itemData, flag, lineItemno) {
    let percentage = item.tax_percentage
    let amount = itemData.get('amount')?.value
    // const taxAmount = (amount * percentage) / 100;
    itemData.get('taxPercentage').setValue(percentage);
    itemData.get('taxId').setValue(item?.id);
    // itemData.get('taxAmount').setValue(taxAmount);
    itemData.get('tax').setValue(item.description);
    itemData.get('taxType').setValue(flag);
    this.populateTaxArray(item, flag, amount, lineItemno)
    this.updateTotalTaxAmount();
    this.updateTotal();
  }

  // To update tax amount of line item whenever there is change in rate and quantity
  updateTaxAmountonRateQuantityChange() {

    const workSummaryArray = this.form.get('workSummary') as FormArray;

    for (let workSummary of workSummaryArray.controls) {
      const amount = workSummary.get('amount').value;
      const percentage = workSummary.get('taxPercentage').value
      // let taxAmount = (amount * percentage) / 100;
      // workSummary.get('taxAmount').setValue(taxAmount);
    }

  }


  onSubmit() {
  }

  // Function will return when save is clicked
  getFormData(): FormGroup {
    return this.form;
  }

  // To retrieve work summary template
  getServiceTemplate(serviceTypeGroupId) {
    return new Promise((resolve, reject) => {
      this.invConfigService
        .getTemplateConfig(serviceTypeGroupId).subscribe(
          (res: any) => {
            this.config = res.data
            resolve(res.data);

          },
          (err) => {
            console.log(err);
            reject(err);
          }
        );
    });

  }

  // To retrieve work summary template whenever there is change in service type
  async getServiceGroupId() {
    this.config = await this.getServiceTemplate(this.form.get('serviceTypeGroupId').value);
    this.itemConfig = this.config.filter((field) => field.key_name !== "fteDetails");
    if (this.form.get('serviceTypeGroupId').value == 4) {
      if(!this.isFromDraft){
        this.subLineItemdata = await this.getSubLineItemData(this.form.get("milestoneId").value)
      }
    }
    let showfteDetail = this.config.find((item) => item.key_name === "fteDetails");
    let showItemType = this.config.find((item) => item.key_name === "productServiceType");
    if (showfteDetail && showfteDetail.active_status === 1) {
      this.showFteDetails = true;
      const fteDetailsArray = this.form.get('fteDetails') as FormArray;
      fteDetailsArray.clear();
      this.fteconfig = await this.getFteDetailsConfig()
      // if(this.form.get("milestoneId").value != '' && this.stepIndex == 0)
      this.masterFteData = await this.masterTmGeneralAndConsultantInfo(this.form.get("milestoneId").value)
      if (!this.masterFteData || this.masterFteData.length == 0) {
        swal.fire({
          title: 'Internal stakeholders details not found!',
          icon: 'info',
          showCancelButton: true,
          confirmButtonText: 'Go to Billed', // Button 1 text
          cancelButtonText: 'Go to YTB', // Button 2 text
          confirmButtonColor: '#cf0001',
          cancelButtonColor: '#cf0001',
          allowOutsideClick: false,
          allowEscapeKey: false
        }).then((result) => {
          if (result.isConfirmed) {
            this.moveToBilled();
          } else if (result.dismiss === swal.DismissReason.cancel) {
            this.moveToYTB();
          }
        });
      }
      if(!this.isFromDraft){
        this.ftedata = await this.tmGeneralAndConsultantInfo(this.form.get("milestoneId").value)
      }
      else if(this.isFromDraft){
        this.ftedata = this.prefilldata['fteDetails']
      }

      const taxDetailsArray = this.form.get('taxDetails') as FormArray;
      taxDetailsArray.clear();
      this.taxresultArray = [];
      const taxResultArray = this.form.get('taxResultDetails') as FormArray;
      taxResultArray.clear()
      this.form.get('subtotal')?.setValue(0.00)
      this.form.get('totalTaxAmount')?.setValue(0.00)
      this.form.get('totalAmount')?.setValue(0.00)
      this.form.get('totalAmountInwords')?.setValue('')
      if(!this.isFromDraft){
        this.populateWorkSummaryArray();
      }
      else if(this.isFromDraft){
        this.populateWorkSummaryArrayFromDraft();
      }


      this.fteFieldData.emit({ fteConfig: this.fteconfig, stepIndex: this.stepIndex })
    }
    else {
      this.showFteDetails = false;
      const taxDetailsArray = this.form.get('taxDetails') as FormArray;
      taxDetailsArray.clear();
      this.taxresultArray = [];
      const fteDetailsArray = this.form.get('fteDetails') as FormArray;
      fteDetailsArray.clear();
      const taxResultArray = this.form.get('taxResultDetails') as FormArray;
      taxResultArray.clear()
      this.form.get('subtotal')?.setValue(0.00)
      this.form.get('totalTaxAmount')?.setValue(0.00)
      this.form.get('totalAmount')?.setValue(0.00)
      this.form.get('totalAmountInwords')?.setValue('')
      if(!this.isFromDraft){
        this.populateWorkSummaryArray();
      }
      else if(this.isFromDraft){
        this.populateWorkSummaryArrayFromDraft();
      }

    }
    if(showItemType && showItemType.active_status === 1){
      this.productServiceOptions = []
      let result = await this.getProductServiceDropdown()
    }

    this.itemFieldData.emit({ itemConfig: this.itemConfig, stepIndex: this.stepIndex })
    return this.form.get('serviceTypeGroupId').value
  }


  // To populate due date based on invoice date and payment terms
  getDueDate() {

    const invoiceDate = this.form.get('invoiceDate')?.value as Date;
    const paymentTermDays = this.form.get('paymentTermDays')?.value;
    // this.form.get('invoiceRaisedOn').setValue(invoiceDate)
    if (this.stepIndex != 0) {
      this.form.get('dueDate')?.setValue(invoiceDate)
      return
    }
    if (paymentTermDays == 0 && invoiceDate) {
      this.form.get('dueDate')?.setValue(invoiceDate)
    }
    else if (invoiceDate && paymentTermDays) {
      const modifiedDate = moment(invoiceDate).add(paymentTermDays, 'days').toDate();
      // Set the modified date to the form control
      this.form.get('dueDate')?.setValue(modifiedDate);
    }
    else {
      this.form.get('dueDate')?.setValue('');
    }
  }

  // To change the one address to other address
  editFromAddress = () => {

    let legalEntityId = this.form.get('legalEntityId').value
    this.invoiceService.getAvailableFromAddress(legalEntityId).subscribe(
      async (res) => {
        console.log(res);
        const { AvailableFromAddressComponent } = await import(
          '../../../../lazy-loaded-components/available-from-address/available-from-address.component'
        );
        const bottomSheetRef = this._bottomSheet.open(
          AvailableFromAddressComponent,
          {
            data: { data: res, legalEntityId: legalEntityId },
            panelClass: 'custom-bank-bottom-sheet',
          }
        );
        bottomSheetRef.afterDismissed().subscribe((res) => {
          this.patchFromAddressAfterEdit(res);
        });
      },
      (err) => {
        console.error(err);
      }
    );
  };

  // patch the latest address in form
  patchFromAddressAfterEdit(res) {

    this.form.get("fromAddressId").setValue(res.address_id);
    this.form.get("fromLineOneAddress").setValue(res.legalEntityName);
    this.form.get("fromLineTwoAddress").setValue(res.addressLine1);
    this.form.get("fromLineThreeAddress").setValue(res.addressLine2);
    this.form.get("fromLineFourAddress").setValue(res.addressLine3);
    this.form.get("fromLineFiveAddress").setValue(res.addressLine4);
  }

  // to change the to address 
  editToAddress = (index) => {
    let customerId = this.form.get('customerId').value

    this.invoiceService.getAvailableToAddress(customerId).subscribe(
      async (res) => {
        console.log(res);
        if (res['messType'] == 'E') {
          this._snackBar.open(res["messText"], 'dismiss', { duration: 2000 });
        }
        else {
          const { AvailableToAddressComponent } = await import(
            '../../../../lazy-loaded-components/available-to-address/available-to-address.component'
          );
          const bottomSheetRef = this._bottomSheet.open(
            AvailableToAddressComponent,
            {
              data: { data: res, customerId: customerId },
              panelClass: 'custom-bank-bottom-sheet',
            }
          );
          bottomSheetRef.afterDismissed().subscribe((res) => {
            console.log(res);
            this.patchToAddressAfterEdit(res);
          });
        }
      },
      (err) => {
        console.error(err);
      }
    );
  };
  // patch the latest address
  patchToAddressAfterEdit(res) {

    this.form.get("toAddressId").setValue(res.address_id);
    this.form.get("toLineOneAddress").setValue(res?.legalEntityName || res?.customerName);
    this.form.get("toLineTwoAddress").setValue(res.addressLine1);
    this.form.get("toLineThreeAddress").setValue(res.addressLine2);
    this.form.get("toLineFourAddress").setValue(res.addressLine3);
    this.form.get("toLineFiveAddress").setValue(res.addressLine4);
  }

  // To get FTE Details Config
  getFteDetailsConfig() {
    return new Promise((resolve, reject) => {
      this.invConfigService
        .getfteConfig().subscribe(
          (res: any) => {
            this.fteconfig = res.data
            resolve(res.data);

          },
          (err) => {
            console.log(err);
            reject(err);
          }
        );
    });

  }
  // To get FTE consultant details 
  tmGeneralAndConsultantInfo(milestone_id) {
    return new Promise((resolve, reject) => {
      this.invoiceService
        .tmGeneralAndConsultantInfo(milestone_id).subscribe(
          (res: any) => {
            this.ftedata = res.consultantsInfo
            resolve(res.consultantsInfo);

          },
          (err) => {
            console.log(err);
            reject(err);
          }
        );
    });

  }
  masterTmGeneralAndConsultantInfo(milestone_id) {
    return new Promise((resolve, reject) => {
      this.botService
        .masterTmGeneralAndConsultantInfo(milestone_id).subscribe(
          (res: any) => {
            resolve(res['data']);

          },
          (err) => {
            console.log(err);
            reject(err);
          }
        );
    });

  }

  // To populate FTE Details config based on config
  async populateFteDetails(data) {
    const fteDetailsArray = this.form.get('fteDetails') as FormArray;
    fteDetailsArray.clear();
    let currency = this.form.get('currency').value;
    if (data.length === 0 || data == 'undefined') {
      // Execute the code with default values for an empty data array
      const fteDetailsFormGroup = this.formBuilder.group({});

      this.fteconfig.forEach((configItem) => {
        const keyName = configItem.key_name;
        let fieldValidators = [];
        if (configItem.field_type === 'dropdown') {
          if (configItem.key_name === 'workLocation') {
            this.workLocationdropdownList(configItem)
          }
          if (configItem.key_name === 'title') {
            this.titledropdownList(configItem)
          }

        }
        if (keyName === 'perDayRate' || keyName === 'actualWorkingDays') {
          // Add Validators.min(1) for the specific field
          fieldValidators.push(Validators.min(0));
        }
        if (configItem.is_mandatory == 1) {
          fieldValidators.push(Validators.required);
        }
        // const validators = configItem.is_mandatory == 1 ? [Validators.required] : null;
        fteDetailsFormGroup.addControl(keyName, this.formBuilder.control('', fieldValidators));
      });
      fteDetailsFormGroup.addControl("associateOid", this.formBuilder.control(""));
      fteDetailsFormGroup.addControl("associateId", this.formBuilder.control(""));
      fteDetailsFormGroup.addControl("consultantName", this.formBuilder.control(""));
      fteDetailsFormGroup.addControl("entityId", this.formBuilder.control(""));
      fteDetailsFormGroup.addControl("positionId", this.formBuilder.control(""));
      fteDetailsFormGroup.addControl("isaId", this.formBuilder.control(""));
      fteDetailsFormGroup.addControl("subDivisionId", this.formBuilder.control(""));
      fteDetailsFormGroup.addControl("divisionId", this.formBuilder.control(""));
      fteDetailsArray.push(fteDetailsFormGroup);
      const actual = this.parseValue(fteDetailsFormGroup.get('actualWorkingDays').value);
      const planned = this.parseValue(fteDetailsFormGroup.get('perDayRate').value);
      fteDetailsFormGroup.get('fteTotalAmount').setValue(this.fixNumberOnUI((actual * planned), currency));

      fteDetailsFormGroup.get('perDayRate').valueChanges.subscribe(() => {
        let currency = this.form.get('currency').value;
        const actual = this.parseValue(fteDetailsFormGroup.get('actualWorkingDays').value);
        const planned = this.parseValue(fteDetailsFormGroup.get('perDayRate').value);
        fteDetailsFormGroup.get('fteTotalAmount').setValue(this.fixNumberOnUI((actual * planned), currency));
      });

      fteDetailsFormGroup.get('actualWorkingDays').valueChanges.subscribe(() => {
        let currency = this.form.get('currency').value;
        const actual = this.parseValue(fteDetailsFormGroup.get('actualWorkingDays').value);
        const planned = this.parseValue(fteDetailsFormGroup.get('perDayRate').value);
        fteDetailsFormGroup.get('fteTotalAmount').patchValue(this.fixNumberOnUI((actual * planned), currency));
      });

      fteDetailsFormGroup.get('fteTotalAmount').valueChanges.subscribe(() => {
        this.getFteTotalValue()
      });

      this.getFteTotalValue()
    }
    else {
      for (let item of data) {
        const fteDetailsFormGroup = this.formBuilder.group({});
        // Find the work location config
        const workLocationConfig = this.fteconfig.find(configItem => configItem.key_name === 'workLocation');
        if (workLocationConfig) {
          await this.workLocationdropdownList(workLocationConfig); // Wait for dropdown data
        }
        this.fteconfig.forEach((configItem) => {
          const keyName = configItem.key_name;
          let fieldValidators = []
          let initialValue
          if (keyName != 'workLocation')
            initialValue = item[keyName]; // Get the value from the data object

          if (configItem.field_type === 'dropdown') {
            if (configItem.key_name === 'workLocation') {
              let indexWorkLocationByID = this.invoiceWorkLocation.find(i => i.id == item.workLocation)
              initialValue = indexWorkLocationByID ? indexWorkLocationByID.shift_name : "";
            }
            if (configItem.key_name === 'title') {
              this.titledropdownList(configItem)
            }

          }
          if (keyName === 'perDayRate' || keyName === 'actualWorkingDays') {
            // Add Validators.min(1) for the specific field
            fieldValidators.push(Validators.min(0));
          }
          if (configItem.is_mandatory == 1) {
            fieldValidators.push(Validators.required);
          }
          // const validators = configItem.is_mandatory == 1 ? [Validators.required] : null;
          fteDetailsFormGroup.addControl(keyName, this.formBuilder.control(initialValue, fieldValidators));
        });
        fteDetailsFormGroup.addControl("associateId", this.formBuilder.control(item.id));
        fteDetailsFormGroup.addControl("consultantName", this.formBuilder.control(item.name));
        fteDetailsFormGroup.addControl("entityId", this.formBuilder.control(item.entity));
        fteDetailsFormGroup.addControl("positionId", this.formBuilder.control(item.position));
        fteDetailsFormGroup.addControl("isaId", this.formBuilder.control(item.isaId));
        fteDetailsFormGroup.addControl("subDivisionId", this.formBuilder.control(item.subDivsion));
        fteDetailsFormGroup.addControl("divisionId", this.formBuilder.control(item.divsionId));
        fteDetailsArray.push(fteDetailsFormGroup);



        const actual = this.parseValue(fteDetailsFormGroup.get('actualWorkingDays').value);
        const planned = this.parseValue(fteDetailsFormGroup.get('perDayRate').value);
        fteDetailsFormGroup.get('fteTotalAmount').setValue(this.fixNumberOnUI((actual * planned), currency));

        fteDetailsFormGroup.get('perDayRate').valueChanges.subscribe(() => {
          let currency = this.form.get('currency').value;
          const actual = this.parseValue(fteDetailsFormGroup.get('actualWorkingDays').value);
          const planned = this.parseValue(fteDetailsFormGroup.get('perDayRate').value);
          fteDetailsFormGroup.get('fteTotalAmount').setValue(this.fixNumberOnUI((actual * planned), currency));
        });

        fteDetailsFormGroup.get('actualWorkingDays').valueChanges.subscribe(() => {
          let currency = this.form.get('currency').value;
          const actual = this.parseValue(fteDetailsFormGroup.get('actualWorkingDays').value);
          const planned = this.parseValue(fteDetailsFormGroup.get('perDayRate').value);
          fteDetailsFormGroup.get('fteTotalAmount').setValue(this.fixNumberOnUI((actual * planned), currency));
        });

        fteDetailsFormGroup.get('fteTotalAmount').valueChanges.subscribe(() => {
          this.getFteTotalValue()
        });


      }
      this.getFteTotalValue()
      this.showWarningMsg()
    }
  }

  // dropdown for work location
  async workLocationdropdownList(value) {
    if (value.options != null) {
      this.invoiceWorkLocation = JSON.parse(value.options)
    }
    else {
      if (value.master_api != null) {
        this.invoiceWorkLocation = await this.botService.getdropdownMaster(value.master_api)
      }
    }
  }

  // dropdown for title
  async titledropdownList(value) {
    if (value.options != null) {
      this.skillRoleList = JSON.parse(value.options)
    }
    else {
      if (value.master_api != null) {
        this.skillRoleList = await this.botService.getdropdownMaster(value.master_api)
        this.skillRoleList = _.uniq(this.skillRoleList, 'role_description');
      }
    }

  }

  // assigned to
  selectAssignedTo(id, fteData) {
    fteData.get('consultantName').setValue(id?.name);
    fteData.get('associateId').setValue(id?.id);
    fteData.get('associateOid').setValue(id?.associateOid);
    fteData.get('entityId').setValue(id?.entity);
    fteData.get('positionId').setValue(id?.position);
    fteData.get('isaId').setValue(id?.isaId);
    fteData.get('subDivisionId').setValue(id?.subDivsion);
    fteData.get('divisionId').setValue(id?.divsionId);

  }

  // To add fte
  addFte() {
    const fteDetailsArray = this.form.get('fteDetails') as FormArray;
    const fteDetailsFormGroup = this.formBuilder.group({});
    let currency = this.form.get('currency').value;
    this.fteconfig.forEach((configItem) => {
      const keyName = configItem.key_name;
      const initialValue = ""; // Get the value from the data object
      const validators = configItem.is_mandatory == 1 ? [Validators.required] : null;
      fteDetailsFormGroup.addControl(keyName, this.formBuilder.control(initialValue, validators));
    });
    fteDetailsFormGroup.addControl("associateOid", this.formBuilder.control(""));
    fteDetailsFormGroup.addControl("associateId", this.formBuilder.control(""));
    fteDetailsFormGroup.addControl("consultantName", this.formBuilder.control(""));
    fteDetailsFormGroup.addControl("entityId", this.formBuilder.control(""));
    fteDetailsFormGroup.addControl("positionId", this.formBuilder.control(""));
    fteDetailsFormGroup.addControl("isaId", this.formBuilder.control(""));
    fteDetailsFormGroup.addControl("subDivisionId", this.formBuilder.control(""));
    fteDetailsFormGroup.addControl("divisionId", this.formBuilder.control(""));
    fteDetailsArray.push(fteDetailsFormGroup);

    const actual = this.parseValue(fteDetailsFormGroup.get('actualWorkingDays').value);
    const planned = this.parseValue(fteDetailsFormGroup.get('perDayRate').value);
    fteDetailsFormGroup.get('fteTotalAmount').setValue(this.fixNumberOnUI((actual * planned), currency));

    fteDetailsFormGroup.get('perDayRate').valueChanges.subscribe(() => {
      let currency = this.form.get('currency').value;
      const actual = this.parseValue(fteDetailsFormGroup.get('actualWorkingDays').value);
      const planned = this.parseValue(fteDetailsFormGroup.get('perDayRate').value);
      fteDetailsFormGroup.get('fteTotalAmount').setValue(this.fixNumberOnUI((actual * planned), currency));
    });

    fteDetailsFormGroup.get('actualWorkingDays').valueChanges.subscribe(() => {
      let currency = this.form.get('currency').value;
      const actual = this.parseValue(fteDetailsFormGroup.get('actualWorkingDays').value);
      const planned = this.parseValue(fteDetailsFormGroup.get('perDayRate').value);
      fteDetailsFormGroup.get('fteTotalAmount').setValue(this.fixNumberOnUI((actual * planned), currency));
    });

    fteDetailsFormGroup.get('fteTotalAmount').valueChanges.subscribe(() => {
      this.getFteTotalValue()
    });

  }

  // To delete fte
  deleteFte(index: number): void {
    const fteDetailsArray = this.form.get('fteDetails') as FormArray;
    fteDetailsArray.removeAt(index);
    this.getFteTotalValue();
  }

  // function to retrieve terms and conditions config based on legal entity
  getTermsConditionsConfig(legal_entity_id) {
    return new Promise((resolve, reject) => {
      this.invConfigService
        .getTermsConditionsConfig(legal_entity_id).subscribe(
          (res: any) => {
            this.legal_entity_details = res['legal_entity_details'];
            resolve(res.data);

          },
          (err) => {
            console.log(err);
            reject(err);
          }
        );
    });

  }

  // To change the bank address of entity
  openBottomSheet(text: string): void {
    if ((text = 'bank')) {
      let legalEntityId = this.form.get('legalEntityId').value
      this.invoiceService.getAvailableBanks(legalEntityId).subscribe(
        async (res) => {
          const { AvailableBankForSelectionComponent } = await import(
            '../../../../lazy-loaded-components/available-bank-for-selection/available-bank-for-selection.component'
          );
          const bottomSheetRef = this._bottomSheet.open(
            AvailableBankForSelectionComponent,
            {
              data: { data: res },
              panelClass: 'custom-bank-bottom-sheet',
            }
          );
          bottomSheetRef.afterDismissed().subscribe((res) => {
            console.log(res);
            this.patchBankDetail(res);
          });
        },
        (err) => {
          console.error(err);
        }
      );
    }
  }
  // to patch the latest bank details
  patchBankDetail = (data) => {
    this.form.get("bankId")?.setValue(data.bank_id);
    this.form.get("bankAccount")?.setValue(data.bank_acc_no);
    this.form.get("bankBranch")?.setValue(data.bank_branch);
    this.form.get("bankName")?.setValue(data.bank_name);
    this.form.get("bankAddress")?.setValue(data.bank_address);
    this.form.get("IBAN")?.setValue(data.IBAN);
    this.form.get("swiftCode")?.setValue(data.swift_code);
    this.form.get("ifscCode")?.setValue(data.ifsc_code);
    this.form.get("serviceTaxNo")?.setValue(data.service_tax_no);
    this.form.get("panCardNo")?.setValue(data.pan_card_no);
  };


  async ngOnChanges() {
    this.entityFieldConfig = await this.FormFieldConfig(this.legalEntityid)
    if(this.poData){
      if(this.poData["messType"] == 'S'){
        console.log(this.poData)
        console.log("this.poData")
        this.RemainingPoValue = this.poData['data']
        this.totalPoValue = this.poData['total']
      }
      else if(this.poData["messType"] == 'E'){
        this.poError = true
        this.poErrorMessage = this.poData["messText"]
      }
    }
    this.createForm(this.entityFieldConfig);
    if (!this.entityFieldConfig || this.entityFieldConfig.length == 0) {
      swal.fire({
        title: 'Invoice Form Config Not Found!',
        icon: 'info',
        showCancelButton: true,
        confirmButtonText: 'Go to Billed', // Button 1 text
        cancelButtonText: 'Go to YTB', // Button 2 text
        confirmButtonColor: '#cf0001',
        cancelButtonColor: '#cf0001',
        allowOutsideClick: false,
        allowEscapeKey: false
      }).then((result) => {
        if (result.isConfirmed) {
          this.moveToBilled();
        } else if (result.dismiss === swal.DismissReason.cancel) {
          this.moveToYTB();
        }
      });
    }
    this.getStaticField()
    this.filterTaxGroups(this.groupTaxMaster)
    this.form.valueChanges.subscribe(() => {
      this.emitFormValidity();
    });
    this.entityFieldData.emit({ entityConfig: this.entityFieldConfig, stepIndex: this.stepIndex })
  }

  // To get invoice creation form field config based on entity 
  FormFieldConfig(entity_id) {
    return new Promise((resolve, reject) => {
      this.invConfigService.FormFieldConfig(entity_id).subscribe(
        (res: any) => {
          resolve(res.data);
        },
        (err) => {
          console.log(err);
          reject(err);
        }
      );
    });

  }

  // To cummulate all fte total value to rate in workSummary
  getFteTotalValue() {
    let totalFteTotalAmount = 0;
    const workSummaryArray = this.form.get('workSummary') as FormArray;
    const fteDetailsArray = this.form.get('fteDetails') as FormArray;
    for (let fteFormGroup of fteDetailsArray.controls) {
      // const fteTotalAmount = parseFloat(fteFormGroup.get('fteTotalAmount').value.toString().replace(/,/g, ''));
      const fteTotalAmount = this.parseValue(fteFormGroup.get('fteTotalAmount').value)
      totalFteTotalAmount += fteTotalAmount;
    }

    if (workSummaryArray.length === 1 && !workSummaryArray?.controls[0]?.get('quantity')) {
      workSummaryArray.controls[0].get('rate').setValue(this.fixNumberOnUI(totalFteTotalAmount, this.form.get('currency').value));
    }
  }
  patchLegalEntityDetails() {
    if (this.legal_entity_details) {
      let data = this.legal_entity_details[0]
      this.form.get('lutArn')?.setValue(this.prefilldata["lutArn"] ? this.prefilldata["lutArn"] : data.lutArn || '');
      this.form.get('rcmApplicable')?.setValue(this.prefilldata["rcmApplicable"] ? this.prefilldata["rcmApplicable"] : data.rcmApplicable || '');
      this.form.get('GSTIN')?.setValue(this.prefilldata["GSTIN"] ? this.prefilldata["GSTIN"] :data.GSTIN || '');
      this.form.get('iecCode')?.setValue(this.prefilldata["iecCode"] ? this.prefilldata["iecCode"] : data.iecCode || '');
      this.form.get('sacCode')?.setValue(this.prefilldata["sacCode"] ? this.prefilldata["sacCode"] : data.sacCode || '');
      this.form.get('hsnCode')?.setValue(this.prefilldata["hsnCode"] ? this.prefilldata["hsnCode"] : data.hsnCode || '');
    }

  }

  unitClicked(value) {
    this.form.get('unit').setValue(value)
    this.fteconfig.forEach(field => {
      if (field.key_name == "plannedWorkingDays")
        field.label_name = `Planned Working ${value}`;
      else if (field.key_name == "perDayRate")
        field.label_name = `Per ${value} Rate`;
      else if (field.key_name == "actualWorkingDays")
        field.label_name = `Actual Working ${value}`
    });
  }

  // onRowMoved(event: CdkDragDrop<string[]>) {
  //   const worksummaryArray = this.form.get('workSummary') as FormArray;
  //   moveItemInArray(worksummaryArray.controls, event.previousIndex, event.currentIndex);
  // }

  calculateGroupTaxAmount(item, itemData, flag, lineItemno) {
    let percentage = item.tax_percentage
    let amount = itemData.get('amount')?.value
    // const taxAmount = (amount * percentage) / 100;
    itemData.get('taxPercentage').setValue(percentage);
    itemData.get('taxId').setValue(item?.id);
    // itemData.get('taxAmount').setValue(taxAmount);
    itemData.get('tax').setValue(item.description);
    itemData.get('taxType').setValue(flag);
    if (itemData.contains('taxGroupPercentage')) {
      itemData.get('taxGroupPercentage')?.setValue(item?.tax_group_percentage);
    } else {
      itemData.addControl('taxGroupPercentage', new FormControl(item?.tax_group_percentage));
    }
    this.populateTaxArray(item, flag, amount, lineItemno)
    this.updateTotalTaxAmount();
    this.updateTotal();
  }
  filterTaxGroups(value) {
    let uniqDesc = {}
    // this.filterTaxGroup = [...new Set(value.map(item => item.description))];
    value?.forEach(item => {
      if (!uniqDesc[item.description]) {
        uniqDesc[item.description] = item;
      }
    });

    this.filterTaxGroup = Object.values(uniqDesc);
  }
  populateTaxArray(item, flag, amount, index) {
    const taxDetailsArray = this.form.get('taxDetails') as FormArray;


    // Find indices of existing group tax entries for the line item
    const existingGroupIndices = taxDetailsArray.controls.reduce((indices, control, currentIndex) => {
      const controlValue = control.value;
      if (controlValue.lineItemno === index) {
        indices.push(currentIndex);
      }
      // && controlValue.flag === 'group'
      return indices;
    }, []);

    // Remove existing group tax entries for the line item
    existingGroupIndices.reverse().forEach(existingIndex => {
      taxDetailsArray.removeAt(existingIndex);
    });

    if (flag == 'tax') {
      const taxDetailsFormGroup = this.formBuilder.group({
        tax_description: item.description,
        tax_id: item.id,
        tax_percentage: item.tax_percentage,
        amount: amount,
        lineItemno: index,
        flag: flag
      });
      taxDetailsArray.push(taxDetailsFormGroup);
    }

    if (flag === 'group') {
      const groupTaxId = item.group_id;
      const groupTaxItems = this.groupTaxMaster.filter(groupItem => groupItem.group_id === groupTaxId);
      // Add new group tax entries for the line item
      groupTaxItems.forEach(groupItem => {
        const groupTaxDetailsFormGroup = this.formBuilder.group({
          tax_description: groupItem.tax_long_description,
          tax_id: groupItem.id,
          tax_percentage: groupItem.tax_percentage,
          amount: amount,
          lineItemno: index,
          flag: flag,
          group_id: groupItem.group_id
        });
        taxDetailsArray.push(groupTaxDetailsFormGroup);
      });
    }

    const aggregatedAmounts = {};

    for (const control of taxDetailsArray.controls) {
      const item = control.value;
      const { tax_description, amount, tax_percentage, lineItemno } = item;

      if (parseFloat(amount) === 0) {
        if (!aggregatedAmounts.hasOwnProperty(tax_description)) {
          aggregatedAmounts[tax_description] = {
            tax_description,
            tax_percentage,
            amount: 0, // Set amount to 0
            lineItemno,
          };
        }
      } else if (!isNaN(parseFloat(amount)) && parseFloat(amount) !== 0) {
        if (aggregatedAmounts.hasOwnProperty(tax_description)) {
          aggregatedAmounts[tax_description].amount += this.parseValue(amount) * (tax_percentage / 100);
        } else {
          aggregatedAmounts[tax_description] = {
            tax_description,
            tax_percentage,
            amount: this.parseValue(amount) * (tax_percentage / 100),
            lineItemno
          };
        }
      }
    }

    this.taxresultArray = Object.values(aggregatedAmounts);

    const taxResultArray = this.form.get('taxResultDetails') as FormArray;
    taxResultArray.clear()
    for (let aggregatedAmount of this.taxresultArray) {
      const taxFormGroup = this.formBuilder.group({
        tax_description: aggregatedAmount.tax_description,
        tax_percentage: aggregatedAmount.tax_percentage,
        amount: aggregatedAmount.amount,
        lineItemno: aggregatedAmount.lineItemno
      });

      taxResultArray.push(taxFormGroup);
    }

  }

  emitFormValidity() {
    if (this.restrictSavePoOnAlert) {
      if (this.showPoAlert || !this.form.valid) {
        this.formValidityChange.emit(false);
      } else {
        this.formValidityChange.emit(true);
      }
    } else {
      if (this.form.valid) {
        this.formValidityChange.emit(true);
      } else {
        this.formValidityChange.emit(false);
      }
    }

  }

  setFormDataSpecific(data) {
    this.form.get('serviceTypeGroupId').setValue(data.serviceTypeGroupId, { emitEvent: false })
    // this.form.patchValue(data)
    this.form.get('invoiceTemplateName').setValue(data.invoiceTemplateName, { emitEvent: false })
    this.form.get('serviceTypeName').setValue(data.serviceTypeName, { emitEvent: false })
    this.form.get('serviceTypeId').setValue(data.serviceTypeName, { emitEvent: false })
    this.form.get('profitCenter').setValue(data.profitCenter, { emitEvent: false })
    this.form.get('department').setValue(data.department, { emitEvent: false })
    this.form.get('milestoneId').setValue(data.milestoneId, { emitEvent: false })
    this.form.get('invoiceNoType').setValue(data.invoiceNoType, { emitEvent: false })
    this.conversionRates = data.conversionRates
    let currency = this.form.get('currency').value
    const getServiceGroupIdPromise = new Promise<void>(async (resolve) => {
      await this.getServiceGroupId();
      resolve();
    });

    getServiceGroupIdPromise.then(() => {

      // Clear existing values in the workSummary FormArray
      const worksummaryFormArray = this.form.get('workSummary') as FormArray;
      worksummaryFormArray.clear();
      const fteDetailsArray = this.form.get('fteDetails') as FormArray;
      fteDetailsArray.clear();



      // Iterate through the workSummary array in data and add each value to the workSummary FormArray
      if (data.workSummary && Array.isArray(data.workSummary)) {
        data.workSummary.forEach(workSummaryItem => {
          const formControls = {
            lineItemNo: [workSummaryItem.lineItemNo],
            description: [workSummaryItem.description],
            rate: [(workSummaryItem.rate)],
            tax: [workSummaryItem.tax],
            taxAmount: [(workSummaryItem.taxAmount)],
            amount: [(workSummaryItem.amount)],
            taxId: [this.prefilldata.tax_id],
            taxPercentage: [this.prefilldata.tax_percentage],
            taxType: [this.prefilldata.tax != "" && this.prefilldata.tax != undefined ? 'tax' : ""]
          };

          if (workSummaryItem.quantity !== undefined) {
            formControls['quantity'] = [this.fixNumberOnUI(this.parseValue(workSummaryItem.quantity), currency)];
          }

          if (workSummaryItem.hours !== undefined) {
            formControls['hours'] = [this.fixNumberOnUI(this.parseValue(workSummaryItem.hours), currency)];
          }
          if (workSummaryItem.extraHours !== undefined) {
            formControls['extraHours'] = [this.fixNumberOnUI(this.parseValue(workSummaryItem.extraHours), currency)];
          }
          if (workSummaryItem.productServiceType !== undefined) {
            formControls['productServiceType'] = [workSummaryItem.productServiceType];
          }

          if (workSummaryItem.rate !== undefined && data.partnerMargin !== undefined) {
            let calculatedRate = this.parseValue(workSummaryItem.rate) * (data.partnerMargin / 100);
            let currency = this.form.get('currency').value
            this.rateValues = data.conversionRates.map(conversionRate => {
              return {

                value: (calculatedRate * (conversionRate.value ? conversionRate.value : 1)).toFixed(2),
                currency_code: conversionRate.currency_code
              };
            });
            let conversionRate = this.rateValues.find(rate => rate.currency_code === currency)
            calculatedRate = conversionRate.value ? conversionRate.value : 1
            formControls['rate'] = [this.fixNumberOnUI(this.parseValue(calculatedRate), currency)];
          }

          if (workSummaryItem.amount !== undefined && data.partnerMargin !== undefined) {
            let calculatedAmount = this.parseValue(workSummaryItem.amount) * (data.partnerMargin / 100);
            let currency = this.form.get('currency').value
            this.worksummaryAmountValues = data.conversionRates.map(conversionRate => {
              return {

                value: (calculatedAmount * (conversionRate.value ? conversionRate.value : 1)).toFixed(2),
                currency_code: conversionRate.currency_code
              };
            });

            let conversionRate = this.worksummaryAmountValues.find(rate => rate.currency_code === currency)
            calculatedAmount = conversionRate.value ? conversionRate.value : 1
            formControls['amount'] = [this.fixNumberOnUI(this.parseValue(calculatedAmount), currency)];
          }
          const workSummaryFormGroup = this.formBuilder.group(formControls);
          worksummaryFormArray.push(workSummaryFormGroup);
          this.updateSubtotal();
          this.updateTaxAmountonRateQuantityChange();
          this.updateTotalTaxAmount();
        });
      }

      if (data.fteDetails && Array.isArray(data.fteDetails)) {
        data.fteDetails.forEach(item => {
          const formControls = {
            assignTo: [item.assignTo],
            workLocation: [item.workLocation],
            title: [item.title],
            plannedWorkingDays: [this.fixNumberOnUI(this.parseValue(item.plannedWorkingDays), currency)],
            actualWorkingDays: [this.fixNumberOnUI(this.parseValue(item.actualWorkingDays), currency)],
            perDayRate: [(item.perDayRate)],
            fteTotalAmount: [(item.fteTotalAmount)],
            consultantName: [item.consultantName],
            associateId: [item.associateId],
            associateOid: [item.associateOid]
          }

          if (item.perDayRate !== undefined && data.partnerMargin !== undefined) {
            let calculatedRate = this.parseValue(item.perDayRate) * (data.partnerMargin / 100);
            let currency = this.form.get('currency').value
            this.fteRateValues = data.conversionRates.map(conversionRate => {
              return {

                value: (calculatedRate * (conversionRate.value ? conversionRate.value : 1)).toFixed(2),
                currency_code: conversionRate.currency_code
              };
            });
            let conversionRate = this.fteRateValues.find(rate => rate.currency_code === currency)
            calculatedRate = conversionRate.value ? conversionRate.value : 1
            formControls['perDayRate'] = [this.fixNumberOnUI(this.parseValue(calculatedRate), currency)];
            formControls['fteTotalAmount'] = [this.fixNumberOnUI((this.parseValue(calculatedRate) * this.parseValue(item.actualWorkingDays)), currency)]
          }
          const fteDetailsFormGroup = this.formBuilder.group(formControls);
          fteDetailsArray.push(fteDetailsFormGroup);

        });

      }

      if (this.form.get('serviceTypeGroupId').value == 1)
        this.getFteTotalValue()





    });
    if (this.form.get('serviceTypeGroupId').value == 1) {
      this.fteconfig.forEach((configItem) => {
        if (configItem.field_type === 'dropdown') {
          if (configItem.key_name === 'workLocation') {
            this.workLocationdropdownList(configItem)
          }
          if (configItem.key_name === 'title') {
            this.titledropdownList(configItem)

          }

        }

      });
    }

    this.entityFieldData.emit({ entityConfig: this.entityFieldConfig, stepIndex: this.stepIndex })

  }

  // To get sub line item data
  getSubLineItemData(milestone_id) {
    return new Promise((resolve, reject) => {
      this.botService
        .getSubLineItemData(milestone_id).subscribe(
          (res: any) => {
            console.log(res.data)
            resolve(res.data);

          },
          (err) => {
            console.log(err);
            reject(err);
          }
        );
    });

  }

  // To get product service options data
  getProductServiceDropdown() {
    let entityId = this.form.get('legalEntityId')?.value
    let searchValue = this.searchCtrl.value;
    return new Promise((resolve, reject) => {
      this.botService
        .getProductServiceDropdown(entityId, this.startIndex, this.offSet, searchValue).subscribe(
          (res: any) => {
            if(res && res.data){
              this.productServiceOptions = this.productServiceOptions.concat(res.data)
            }
            resolve(res.data);

          },
          (err) => {
            console.log(err);
            reject(err);
          }
        );
    });

  }

  convertRatesBasedOnCurrency() {
    if (this.stepIndex == 1) {
      let currency = this.form.get('currency').value;
      let milestone_value = this.prefilldata.milestone_value.find(item => item.currency_code === currency)
      this.form.get("milestoneValue").setValue(milestone_value ? this.fixNumberOnUI(milestone_value.value, currency) : 0.00)
      let po_value = this.totalPoValue?.find(item => item.currency_code === currency)
      this.form.get('po_value').setValue(po_value ? this.fixNumberOnUI(po_value.value, currency) : "")
      let remaining_po_value = this.RemainingPoValue?.find(item => item.currency_code === currency)
      this.form.get('remainingPoValue')?.setValue(remaining_po_value ? this.fixNumberOnUI(remaining_po_value?.value, currency) :"")
    }
    else {
      let currency = this.form.get('currency').value;
      let milestone_value = this.prefilldata?.milestone_value.find(item => item.currency_code === currency)
      this.form.get("milestoneValue").setValue(milestone_value ? this.fixNumberOnUI(milestone_value.value, currency) : 0.00)
      let po_value = this.totalPoValue?.find(item => item.currency_code === currency)
      this.form.get('po_value').setValue(po_value ? this.fixNumberOnUI(po_value.value, currency) : "")
      let remaining_po_value = this.RemainingPoValue?.find(item => item.currency_code === currency)
      this.form.get('remainingPoValue')?.setValue(remaining_po_value ? this.fixNumberOnUI(remaining_po_value?.value, currency) : "")
      if (this.form.get('serviceTypeGroupId').value == 2) {
        const workSummaryArray = this.form.get('workSummary') as FormArray;
        if (workSummaryArray.length === 1) {
          workSummaryArray.controls[0].get('rate').setValue(milestone_value ? this.fixNumberOnUI(milestone_value.value, currency) : 0.00);
        }

      }
    }

  }

  fixNumberOnUI(no, currency) {
    if (no == 0 || no == '0' || no == '') {
      return '0.00';
    }
    let num = parseFloat(no);
    if (currency == "INR")
      return new Intl.NumberFormat('en-IN', { minimumFractionDigits: 2, maximumFractionDigits: 2 }).format(Number(num));
    else
      return new Intl.NumberFormat('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }).format(Number(num));
  }
  fixNumberFormat(no, currency) {
    if (no == 0 || no == '0' || no == '') {
      return '0.00';
    }
    let num = parseFloat(no).toFixed(2);
    if (currency == "INR")
      return new Intl.NumberFormat('en-IN', {}).format(Number(num));
    else
      return new Intl.NumberFormat('en-US', {}).format(Number(num));
  }

  formatInput(event: Event, keyName, formgroup) {
    let currency = this.form.get('currency')?.value;
    const inputElement = event.target as HTMLInputElement;
    const inputValue = inputElement.value.replace(/,/g, ''); // Remove existing commas
    // const formattedValue = this.fixNumberOnUI(inputValue, this.form.get("currency").value);

    const formattedValue = inputValue !== '' ? this.fixNumberFormat(inputValue, this.form.get("currency").value) : '';

    inputElement.value = formattedValue; // Update the input value

  }
  formatfInput(event: Event, keyName, formgroup, keyevent: KeyboardEvent) {
    const inputElement = event.target as HTMLInputElement;
    let inputValue = inputElement.value;
    const key = keyevent['inputType'];
    const cursorPosition = inputElement.selectionStart;
    // Remove commas and leading zeros
    inputValue = inputValue.replace(/,/g, '').replace(/^0+(?=\d)/, '0')


    if (!/^\d*\.?\d*$/.test(inputValue)) {
      inputElement.value = '';
      formgroup?.get(keyName)?.setValue('');
      return
    }
    // Check if inputValue can be parsed as a number
    const numericValue = parseFloat(inputValue);

    // Check if numericValue is a valid number and the input value contains only digits
    if (!isNaN(numericValue)) {

      // Ensure that the input value is not empty
      if (inputValue === '') {
        inputElement.value = '';
        formgroup?.get(keyName)?.setValue('');
        return;
      }

      inputValue = inputValue.trim();
      const parts = inputValue.split('.');
      let integerPart = parts[0];
      let decimalPart = parts[1] !== undefined ? '.' + parts[1] : '';

      // Add commas to the integer part
      let currency = this.form.get('currency').value;
      if (currency == "INR")
        integerPart = new Intl.NumberFormat('en-IN').format(Number(integerPart));
      else
        integerPart = new Intl.NumberFormat('en-US').format(Number(integerPart));

      // Combine integer and decimal parts with a dot
      inputValue = decimalPart ? `${integerPart}${decimalPart}` : integerPart;
      inputElement.value = inputValue;
      if (key === 'deleteContentBackward' || key === 'deleteContentForward') {
        inputElement.setSelectionRange(cursorPosition, cursorPosition);
      }
    } else {
      // If inputValue cannot be parsed as a number or contains non-digit characters, set it to an empty string
      inputElement.value = '';
      formgroup?.get(keyName)?.setValue('');
    }

  }



  CurrencyDetails() {
    let currencyCode = this.form.get('currency').value
    for (let i of this.currencyDetails) {

      if (currencyCode == i.currency_code) {
        return (this.titleCase(this.decimalToWords((this.fixNumberForWords(this.form.get("totalAmount").value)), i.currency_description_for_words, i.sub_currency_name, i.currency_code)))
      }
    }
  }

  fixNumberForWords(no) {
    return parseFloat(no).toFixed(2);
  }

  decimalToWords(decimal, integer_currency, decimal_currency, currency_code) {

    let integerPart = Math.floor(decimal);
    // this.integerPartInWords = toWords(integerPart);
    if (currency_code != "INR")
      this.integerPartInWords = curValue.millionWord(integerPart)[0];
    else
      this.integerPartInWords = curValue.lakhWord(integerPart)[0];

    this.integerPartInWords = `${this.integerPartInWords}`;
    const fractionalPart = decimal.toString().split('.')[1];


    if (!fractionalPart) {
      return this.integerPartInWords = this.integerPartInWords ? `${this.integerPartInWords} ${integer_currency}` : "-";;
    }

    const digits = fractionalPart.split('');
    if (parseInt(digits[0]) == 0 && parseInt(digits[1]) == 0) {
      var decimal_text = `${this.integerPartInWords} ${integer_currency} `;

      return decimal_text;
    }
    else {

      var decimal_text = `${this.integerPartInWords} ${integer_currency}`;
      if (decimal_text == null || decimal_text == undefined || decimal_text == ' ') {
        decimal_text = `${decimal_text}`
      }
      else {
        decimal_text = `${decimal_text} and `
      }
      _.each(digits, l => {
        let digit_word = toWords(parseInt(l));
        decimal_text += `${digit_word} `
      })
      decimal_text = `${decimal_text}${decimal_currency}`
      return decimal_text;
    }
  }
  titleCase(str) {

    var splitStr = str.toLowerCase().split(' ');
    for (var i = 0; i < splitStr.length; i++) {
      splitStr[i] = splitStr[i].charAt(0).toUpperCase() + splitStr[i].substring(1);
    }
    return splitStr.join(' ');
  }

  getFteFieldValue(fieldName, fteFormGroup) {
    const formControl = fteFormGroup.get(fieldName);
    if (formControl) {
      return formControl.value;
    }
    return '';
  }

  getWsFieldValue(fieldName, workSummaryFormGroup) {
    const formControl = workSummaryFormGroup.get(fieldName);

    if (formControl) {
        if (fieldName !== 'tax') {
            return formControl.value;
        } else {
            let percentage = workSummaryFormGroup.get('taxPercentage')?.value;
            let type = workSummaryFormGroup.get('taxType')?.value;

            if (type === 'tax' && percentage !== undefined) {
                return `${formControl.value} - ${percentage}%`;
            } else {
                return formControl.value;
            }
        }
    }
    return '';
}

  isAnyToAddressAvailable(): boolean {
    const toLineOne = this.form.get('toLineOneAddress').value;
    const toLineTwo = this.form.get('toLineTwoAddress').value;
    const toLineThree = this.form.get('toLineThreeAddress').value;
    const toLineFour = this.form.get('toLineFourAddress').value;
    const toLineFive = this.form.get('toLineFiveAddress').value;

    return toLineOne || toLineTwo || toLineThree || toLineFour || toLineFive;
  }

  // selectedTaxType: string = 'TCS';
  //   selectedDropdownValue: string = '';
  //   favoriteSeason: any;
  //   tcsOptions: string[] = ['Option 1 (TCS)', 'Option 2 (TCS)', 'Option 3 (TCS)'];
  //   tdsOptions: string[] = ['Option A (TDS)', 'Option B (TDS)', 'Option C (TDS)'];

  //   onTaxTypeChange() {
  //     this.selectedDropdownValue = ''; // Reset dropdown value when tax type changes
  //   }

  parseValue(input: string | number): number {
    if (typeof input === 'string') {
      const sanitizedInput = input.trim(); // Remove leading/trailing spaces
      return sanitizedInput === '' ? 0.00 : parseFloat(sanitizedInput.replace(/,/g, ''));
      // return parseFloat(input.replace(/,/g, ''));
    } else if (typeof input === 'number') {
      return input;
    } else {
      return 0.00;
    }
  }


  changeWorkSummaryCurrency() {
    const workSummaryArray = this.form.get('workSummary') as FormArray;
    let currency = this.form.get('currency').value
    for (let workSummary of workSummaryArray.controls) {

      if (workSummary.get("rate").value) {
        workSummary.get("rate")?.setValue(this.fixNumberOnUI(this.parseValue(workSummary.get("rate").value), currency))
      }

      if (workSummary.get("quantity")?.value) {
        workSummary.get("quantity")?.setValue(this.fixNumberOnUI(this.parseValue(workSummary.get("quantity").value), currency))
      }

      if (workSummary.get("amount")?.value) {
        workSummary.get("amount")?.setValue(this.fixNumberOnUI(this.parseValue(workSummary.get("amount").value), currency))
      }

      if (workSummary.get("hours")?.value) {
        workSummary.get("hours")?.setValue(this.fixNumberOnUI(this.parseValue(workSummary.get("hours").value), currency))
      }
      if (workSummary.get("extraHours")?.value) {
        workSummary.get("extraHours")?.setValue(this.fixNumberOnUI(this.parseValue(workSummary.get("extraHours").value), currency))
      }
      this.updateTotal();
    }

    // if(this.form.get("serviceTypeGroupId")?.value == 4){
    //   for (let workSummary of workSummaryArray.controls) {
    //     if (this.subLineItemdata && this.subLineItemdata.length > 0) {
    //         let rpaId = workSummary.get('rpaId').value
    //         let rpaAmount = this.subLineItemdata.find(m => m.id === rpaId);
    //         const selectedCurrency = this.form.get('currency').value; // Replace 'currencyControlName' with the actual control name for the selected currency
    //         const matchingCurrency = rpaAmount?.milestone_value?.find(currency => currency.currency_code === selectedCurrency);
    //         let controlValue = matchingCurrency ? this.fixNumberOnUI(matchingCurrency.value, currency) : 0.00;
    //         workSummary.get('rate').setValue(controlValue);
    //         const quantity = this.parseValue(workSummary.get('quantity').value);
    //         const rate = this.parseValue(workSummary.get('rate').value);
    //         workSummary.get('amount').setValue(this.fixNumberOnUI((quantity * rate), this.form.get('currency').value));  
    //     }
    //   } 
    //   this.updateSubtotal();
    //   this.aggregateTax();
    //   this.updateTotalTaxAmount();
    //   this.updateTdsTcs();
    //   this.updateTotal();
    // }
  }
  changeFTECurrency() {

    if (this.form.get('serviceTypeGroupId').value == 1) {
      const fteArray = this.form.get('fteDetails') as FormArray;
      let currency = this.form.get('currency').value
      for (let fte of fteArray.controls) {

        if (fte.get("perDayRate")?.value) {
          fte.get("perDayRate")?.setValue(this.fixNumberOnUI(this.parseValue(fte.get("perDayRate").value), currency))
        }

        if (fte.get("actualWorkingDays")?.value) {
          fte.get("actualWorkingDays")?.setValue(this.fixNumberOnUI(this.parseValue(fte.get("actualWorkingDays").value), currency))
        }

        if (fte.get("plannedWorkingDays")?.value) {
          fte.get("plannedWorkingDays")?.setValue(this.fixNumberOnUI(this.parseValue(fte.get("plannedWorkingDays").value), currency))
        }

        if (fte.get("fteTotalAmount")?.value) {
          fte.get("fteTotalAmount")?.setValue(this.fixNumberOnUI(this.parseValue(fte.get("fteTotalAmount").value), currency))
        }



      }
    }
  }

  clearTax(itemData, index) {
    itemData.get("taxId").setValue("");
    itemData.get('taxPercentage').setValue("");
    itemData.get('tax').setValue("");
    itemData.get('taxType').setValue("");
    this.deleteTax(index)
  }

  clearChargeType(itemData, index) {
    itemData.get("chargeType").setValue("");
    itemData.get('chargeDescription').setValue("");
    itemData.get('chargeTypeName').setValue("");
    this.deleteTax(index)
  }

  showStandardFields = true;
  showfteDetails = true;
  showServiceDetails = true;
  showPoDetails = true;

  // In your component class
  toggleStandardFields() {
    this.showStandardFields = !this.showStandardFields;
  }

  toggleFteDetails() {
    this.showfteDetails = !this.showfteDetails;
  }

  toggleServiceDetails() {
    this.showServiceDetails = !this.showServiceDetails;
  }

  togglePoDetails() {
    this.showPoDetails = !this.showPoDetails;
  }

  selectedOption: string;
  selectedDropdownOption: string;



  onRadioChange() {
    this.selectedDropdownOption = null; // Reset the selected option when radio button changes

    this.form.get('selectedDropdownOption')?.setValue('')

    this.form.get('tdsDescription')?.setValue('')
    this.form.get('tdsPercentage')?.setValue(0.00)
    this.form.get('tdsValue')?.setValue(0.00)
    this.form.get('tdsId')?.setValue('')

    this.form.get('tcsDescription')?.setValue('')
    this.form.get('tcsPercentage')?.setValue(0.00)
    this.form.get('tcsValue')?.setValue(0.00)
    this.form.get('tcsId')?.setValue('')
    this.updateTotal();
  }

  getDropdownOptions() {
    return this.dropdownOptions[this.form.get('selectedOption')?.value] || [];
  }

  calculateTdsOrTCS(option) {
    let type = this.form.get('selectedOption')?.value;
    if (type == 'tds') {
      this.form.get('tdsDescription').setValue(option.description)
      this.form.get('tdsId').setValue(option.id)
      this.form.get('tdsPercentage').setValue(option.tax_percentage)
      let subtotal = this.parseValue(this.form.get('subTotal').value);
      let discountAmount = this.parseValue(this.form.get('discountAmount').value);
      let tdsValue = (subtotal - discountAmount) * (option.tax_percentage / 100)
      this.form.get('tdsValue').setValue(this.fixNumberOnUI(tdsValue, this.form.get('currency').value))
      this.updateTotal()
    }
    if (type == 'tcs') {
      this.form.get('tcsDescription').setValue(option.description)
      this.form.get('tcsId').setValue(option.id)
      this.form.get('tcsPercentage').setValue(option.tax_percentage)
      let subtotal = this.parseValue(this.form.get('subTotal')?.value);
      let discountAmount = this.parseValue(this.form.get('discountAmount')?.value);
      let tcsValue = (subtotal - discountAmount) * (option.tax_percentage / 100)
      this.form.get('tcsValue').setValue(this.fixNumberOnUI(tcsValue, this.form.get('currency').value))
      this.updateTotal()
    }
  }

  updateTdsTcs() {

    if (this.form.get('selectedOption')?.value == 'tds') {
      let tdspercentage = this.form.get('tdsPercentage')?.value
      let subtotal = this.parseValue(this.form.get('subTotal')?.value);
      let discountAmount = this.parseValue(this.form.get('discountAmount')?.value);
      let tdsValue = (subtotal - discountAmount) * (tdspercentage / 100)
      this.form.get('tdsValue').setValue(this.fixNumberOnUI(tdsValue, this.form.get('currency').value))
    }
    if (this.form.get('selectedOption')?.value == 'tcs') {
      let tcspercentage = this.form.get('tcsPercentage')?.value
      let subtotal = this.parseValue(this.form.get('subTotal')?.value);
      let discountAmount = this.parseValue(this.form.get('discountAmount')?.value);
      let tcsValue = (subtotal - discountAmount) * (tcspercentage / 100)
      this.form.get('tcsValue').setValue(this.fixNumberOnUI(tcsValue, this.form.get('currency').value))
    }
  }

  getDiscountErrorMessage() {
    const discountInputAmountControl = this.form.get('discountInputAmount');

    if (discountInputAmountControl.hasError('min')) {
      return "Discount value can't be '-ve'";
    }

    if (discountInputAmountControl.hasError('max')) {
      const maxAllowed = discountInputAmountControl.getError('max').max;
      if (maxAllowed === 99.9) {
        return 'Max allowed discount % is 99.9%';
      } else {
        return `Max allowed discount value is ${maxAllowed}`;
      }
    }

    return 'Invalid discount value';
  }

  getRetentionErrorMessage() {
    const retentionInputAmountControl = this.form.get('retentionInputAmount');

    if (retentionInputAmountControl.hasError('min')) {
      return "Retention value can't be '-ve'";
    }

    if (retentionInputAmountControl.hasError('max')) {
      const maxAllowed = retentionInputAmountControl.getError('max').max;
      if (maxAllowed === 99.9) {
        return 'Max allowed retention % is 99.9%';
      } else {
        return `Max allowed retention value is ${maxAllowed}`;
      }
    }

    return 'Invalid retention value';
  }

  changeInAttachment(id) {
    this.contextId = null;
    this.contextId = id;
    this.billingId = this.form.get('billingId').value;
    if (this.contextId) {
      this.invoiceBillService
        .updateInvoiceAttachments(this.contextId, this.billingId)
        .subscribe(
          res => {
            console.log(res);
          },
          err => {
            console.log(err);
          }
        );
    }
  }

  getCurrencySymbol(selectedCurrency: string): string {
    const currencyDetail = this.currencyDetails.find(detail => detail.currency_code == selectedCurrency);
    return currencyDetail ? currencyDetail.currency_symbol : selectedCurrency;
  }

  clearInvalidValue() {
    const discountInputAmountControl = this.form.get('discountInputAmount');
    const inputValue = discountInputAmountControl.value;
    const amountType = this.form.get('discountAmountType').value;
    const containsNonNumeric = /[^0-9.-]|[+\-]{2,}/.test(inputValue);
    if (amountType == 'percentage') {
      if (containsNonNumeric || isNaN(inputValue) || inputValue < 0 || inputValue > 100) {
        discountInputAmountControl.setValue(''); // Clear the input value
      }
    }
    if (amountType == 'rupees') {
      if (containsNonNumeric || inputValue < 0 || isNaN(inputValue)) {
        discountInputAmountControl.setValue(''); // Clear the input value
      }
    }

  }

  clearRetentionInvalidValue() {
    const discountInputAmountControl = this.form.get('retentionInputAmount');
    const inputValue = discountInputAmountControl.value;
    let subtotal = this.form.get('subTotal').value
    const amountType = this.form.get('retentionAmountType').value;
    if(inputValue){
    const containsNonNumeric = /[^0-9.-]|[+\-]{2,}/.test(inputValue);
    if (amountType == 'percentage') {
      if (containsNonNumeric || isNaN(inputValue) || inputValue < 0 || inputValue > 100) {
        discountInputAmountControl.setValue(''); // Clear the input value
      }
    }
    if (amountType == 'rupees') {
      if (containsNonNumeric || inputValue < 0 || isNaN(inputValue)) {
        this._snackBar.open(`Invalid retention amount`, 'dismiss', { duration: 3000 });
        discountInputAmountControl.setValue(''); // Clear the input value
      }
      if(subtotal < inputValue){
        this._snackBar.open(`Retention amount should not be greater than subtotal`, 'dismiss', { duration: 3000 });
        discountInputAmountControl.setValue('');
      }

    }
  }
  }

  markFormGroupTouched() {
    this.form.markAllAsTouched()
    this.markFormArrayControlsAsTouched();


  }
  markFormArrayControlsAsTouched() {
    (<FormArray>this.form?.get('fteDetails'))?.controls.forEach((group: FormGroup) => {
      (<any>Object).values(group.controls).forEach((control: FormControl) => {
        control.markAsTouched();
      })
    });
    (<FormArray>this.form?.get('workSummary'))?.controls.forEach((group: FormGroup) => {
      (<any>Object).values(group.controls).forEach((control: FormControl) => {
        control.markAsTouched();
      })
    });

  }
  deleteTax(index) {
    const taxArray = this.form.get('taxDetails') as FormArray;
    for (let i = taxArray.length - 1; i >= 0; i--) {
      const item = taxArray.at(i);
      if (item.get('lineItemno').value == index + 1) {
        taxArray.removeAt(i);
      }
    }

    this.aggregateTax()

  }

  aggregateTax() {
    const aggregatedAmounts = {};
    const taxDetailsArray = this.form.get('taxDetails') as FormArray;
    for (const control of taxDetailsArray.controls) {
      const item = control.value;
      const { tax_description, amount, tax_percentage, lineItemno } = item;

      if (parseFloat(amount) === 0) {
        if (!aggregatedAmounts.hasOwnProperty(tax_description)) {
          aggregatedAmounts[tax_description] = {
            tax_description,
            tax_percentage,
            amount: 0, // Set amount to 0
            lineItemno,
          };
        }
      } else if (!isNaN(parseFloat(amount)) && parseFloat(amount) !== 0) {
        if (aggregatedAmounts.hasOwnProperty(tax_description)) {
          aggregatedAmounts[tax_description].amount += this.parseValue(amount) * (tax_percentage / 100);
        } else {
          aggregatedAmounts[tax_description] = {
            tax_description,
            tax_percentage,
            amount: this.parseValue(amount) * (tax_percentage / 100),
            lineItemno
          };
        }
      }
    }

    this.taxresultArray = Object.values(aggregatedAmounts);

    const taxResultArray = this.form.get('taxResultDetails') as FormArray;
    taxResultArray.clear()
    for (let aggregatedAmount of this.taxresultArray) {
      const taxFormGroup = this.formBuilder.group({
        tax_description: aggregatedAmount.tax_description,
        tax_percentage: aggregatedAmount.tax_percentage,
        amount: aggregatedAmount.amount,
        lineItemno: aggregatedAmount.lineItemno
      });

      taxResultArray.push(taxFormGroup);
    }
    this.updateTotalTaxAmount()
    this.updateTotal()
  }

  showWarningMsg() {
    if (this.showWarmMsg) {
      const fteArray = this.form.get('fteDetails') as FormArray;
      for (let fte of fteArray.controls) {
        let planned = this.parseValue(fte.get("plannedWorkingDays").value)
        let actual = this.parseValue(fte.get("actualWorkingDays").value)
        let units = this.form.get("unit").value
        if (!units) {
          units = "hours"; // Default to "hours"
        }
        if (actual < planned) {
          this._snackBar.open(`Actual ${units.toLowerCase()} should not be lesser than Planned ${units.toLowerCase()}`, 'dismiss', { duration: 3000 });
          fte.get('actualWorkingDays').setValue('')
        }
      }
    }
  }

  formatNumberForDisplay(number, currency) {
    // Check if the number should be displayed in exponential notation
    let no = this.parseValue(number)
    if (Math.abs(no) >= 1e+12) {
      return no.toExponential();
    } else {
      // return no.toString();
      return this.fixNumberOnUI(no, currency)
    }
  }


  setTermsAndConditions(data, i) {
    for (let x of this.termsConditionsConfig?.terms_and_condition) {
      if (x.isFomrVisible && (x.value !== 'paymentMode' && x.value !== 'paymentTerm')) {
        this.form.addControl(x.value, new FormControl(''));
      }
    }
    for (let x of this.termsConditionsConfig?.terms_and_condition) {
      if (x.isFomrVisible) {
        if (x.extraField == 'Bank') {
          if (data?.bankDetails && data.bankDetails.length > 0) {
            this.form.get(x.value).setValue(data?.bankDetails[0][x.backendFieldName] ? data?.bankDetails[0][x.backendFieldName] : '');
          }
        }
      }
    }
    for (let x of this.termsConditionsConfig?.terms_and_condition) {
      if (x.isFomrVisible) {
        if (x.extraField == 'Bank') {
          if(data?.bankDetails && data.bankDetails.length > 0){
          this.form.get(x.value).setValue(data?.bankDetails[0][x.backendFieldName] ? data?.bankDetails[0][x.backendFieldName] : '');
        }
        }
      }
    } 
  }
  moveToBilled() {
    this.router.navigateByUrl("/main/invoice-v1.2/invoicelist/1");
  }

  moveToYTB() {
    this.router.navigateByUrl("/main/invoice-v1.2/invoicelist/0");
  }

  onTaxSelectionChange(event, group, index){
    let id = event?.value?.id
    if(id == null || event?.value?.type == "none"){
      this.clearTax(group,index-1)
    }
    else{
      let taxType = event.value.type
      let type = taxType.toLowerCase();  
      if(type == 'tax'){
        let item =_.findWhere(this.taxMaster, { id:  id});
        this.calculateTaxAmount(item, group,'tax',index)
      }
      if(type == 'group_tax'){
        let item = _.findWhere(this.filterTaxGroup, { id:  id});
        this.calculateGroupTaxAmount(item, group,'group',index)
      }
    }

  }

  onChargeTypeSelectionChange(event, group, index){
    let id = event?.value?.id
    if(id == null || event?.value?.type == "none"){
      this.clearChargeType(group,index-1)
    }
    else{
      let selectedChargeType = event.value
      group.get('chargeDescription').setValue(selectedChargeType.description);
      group.get('chargeTypeName').setValue(selectedChargeType.type);
    }
    this.updateSubtotal();
    this.aggregateTax();
    this.updateTotalTaxAmount();
    this.updateTdsTcs();
    this.updateTotal();
  }

  calculatePoForAlert(){
    let value;
    if(this.formControlForPoAlert == 'subTotal'){
      value = this.parseValue(this.form.get('subTotal')?.value);
      this.poAlertText = 'Subtotal exceeds remaining PO value'
    }
    else if(this.formControlForPoAlert == 'totalAmount'){
      value = this.parseValue(this.form.get('totalAmount')?.value);
      this.poAlertText = 'Total(TDS/TCS Inclusive) exceeds remaining PO value'
    }
    else if(this.formControlForPoAlert == 'totalTdsTcsExclusive'){
      value = this.parseValue(this.form.get('totalTdsTcsExclusive')?.value);
      this.poAlertText = 'Total(TDS/TCS Exclusive) exceeds remaining PO value'
    }
    let remaning_po_value = this.parseValue(this.form.get('remainingPoValue')?.value);
    if(value > remaning_po_value && value != 0){
      this.showPoAlert = true;
    }
    else{
      this.showPoAlert = false;
    }
  }
  getconversionRates:any;
  async getConversionRateForDate() {
    let projectId = this.form.get('projectId').value;
    let currencyId = this.form.get('currencyId').value;
    let currency = this.form.get('currency').value;
    this.getconversionRates = await this.convertCurrency(projectId, currencyId, 1)
    const selectedRate = this.RemainingPoValue?.find(rate => rate.currency_code === currency);
    let updated_remaining = this.getconversionRates?.map(conversionRate => {
      return {

        value: selectedRate?.value
        ? (selectedRate.value * (conversionRate?.value || 1)).toFixed(2)
        : '',
        currency_code: conversionRate.currency_code
      };
    });

    this.RemainingPoValue = updated_remaining
    let remaining_po_value = this.RemainingPoValue?.find(item => item.currency_code === currency)
    this.form.get('remainingPoValue')?.setValue(remaining_po_value ? this.fixNumberOnUI(remaining_po_value?.value, currency) : "")
    console.log(updated_remaining)
  }
  convertCurrency(projectId, CurrencyId, value) {
    return new Promise((resolve, reject) => {
      this.invoiceService.convertCurrency(projectId, CurrencyId, value).subscribe(
        (res: any) => {
          resolve(res);
        },
        (err) => {
          console.log(err);
          reject(err);
        }
      );
    });
  }
  async cancelSearchValue() {
    this.searchCtrl.patchValue('');
    this.startIndex = 0;
    this.offSet = 50;
    this.productServiceOptions=[];
    let res = await this.getProductServiceDropdown()
    this.productServiceOptions = res;

  }

  // Infinite scroll in mat select
  onOpenedChange(event: any, select: string) {
    if (event) {
      this[select].panel.nativeElement.addEventListener(
        'scroll',
        (event: any) => {
          let scrollTop = Math.ceil(this[select].panel.nativeElement.scrollTop);
          let scrollHeight = this[select].panel.nativeElement.scrollHeight - this[select].panel.nativeElement.offsetHeight;
          if (
            scrollTop - 1 == scrollHeight || scrollTop == scrollHeight || scrollTop + 1 == scrollHeight
          ) {
            this.startIndex = this.startIndex + this.offSet;
            // this.offSet+=15;
            let searchResults = this.getProductServiceDropdown();
          }
        }
      );
    }
  }

  unSelectProductService(formgroup){
    formgroup.get('productServiceType').setValue('')
    formgroup.get('productServiceTypeId').setValue('')
  }
  selectionChanged(value, formgroup){
    if(!value){
      formgroup.get('productServiceTypeId').setValue('')
    }
    let id = this.productServiceOptions?.find(item => item?.name === value)?.id;
    if (!formgroup.get('productServiceTypeId')) {
      formgroup.addControl('productServiceTypeId', new FormControl());
    }
    formgroup.get('productServiceTypeId')?.setValue(id)
  }
  onInvoiceTypeChange(selectedType: string): void {

    if (selectedType === 'new') {
      this.form.get('invoiceNo')?.setValue('XXX-XXX-XXX-XXXX');
      this.invoiceNo?.setValue('XXX-XXX-XXX-XXXX');
      this.isReadonly = true;
    } 
    else if(selectedType === 'existing'){
      this.form.get('invoiceNo')?.setValue(this.prefilldata?.invoice_no);
      this.isReadonly = true;
    }
    else {
      this.form.get('invoiceNo')?.setValue('');
      this.invoiceNo?.setValue('');
      this.isReadonly = false;
    }
  }

  getTooltip(value){
    if(value == 'new'){
      return 'Auto Generated'
    }
    else if(value == 'existing'){
      return 'Previous Invoice No'
    }
    else if(value == 'manual'){
      return 'Manual'
    }
  }

  enforceMaxDiscountPercentage(event: any) {
    const discountType = this.form.get('discountAmountType').value;
  
    if (discountType === 'percentage' && event.target.value > 100) {
      event.target.value = 100;
      this.form.get('discountInputAmount').setValue(100);
    }
  }
  
  enforceMaxRetentionPercentage(event: any) {
    const retentionType = this.form.get('retentionAmountType').value;
  
    if (retentionType === 'percentage' && event.target.value > 100) {
      event.target.value = 100;
      this.form.get('retentionInputAmount').setValue(100);
    }
  }
  // To populate work summary based on service type id 
  populateWorkSummaryArrayFromDraft() {
    let currency = this.form.get('currency').value;

    if (this.form.get("serviceTypeGroupId")?.value == 1) {
      this.unitClicked("Hours")
      if (this.stepIndex == 0) {
        this.populateFteDetailsFromDraft(this.ftedata)
      }
  
      if(this.invoiceTenantDetails && this.invoiceTenantDetails?.show_fte_in_worksummary && this.ftedata && this.ftedata.length > 0){
        const workSummaryArray = this.form.get('workSummary') as FormArray;
        workSummaryArray.clear();
        let workSummaryFromDraft = this.prefilldata['workSummary'] ? this.prefilldata['workSummary'] : ''
        if (workSummaryFromDraft && workSummaryFromDraft.length > 0) {
          workSummaryFromDraft.forEach((fte, index) => {
            const workSummaryFormGroup = this.formBuilder.group({});
  
            this.itemConfig.forEach(configItem => {
              const keyName = configItem.key_name;
              let controlValue = null;
              // let controlValue = keyName === 'lineItemNo' ? (index + 1) : '';
              switch (keyName) {
                case 'lineItemNo':
                  controlValue = fte.lineItemNo ? fte.lineItemNo: index + 1;
                  break;
                case 'description':
                  controlValue = fte.description != null ? fte.description : "";
                  break;
                case 'quantity':
                  controlValue = fte.quantity != null ? fte.quantity : "";
                  break;
                case 'rate':
                  controlValue = fte.rate != null ? fte.rate : "";
                  break;
                case 'productServiceType':
                  controlValue = fte.productServiceType != null ? fte.productServiceType : "";
                  break;
              }
              const validators = configItem.is_mandatory == 1 ? [Validators.required] : null;
              workSummaryFormGroup.addControl(keyName, this.formBuilder.control(controlValue, validators));
            });
            const quantity = this.parseValue(workSummaryFormGroup.get('quantity').value);
            const rate = this.parseValue(workSummaryFormGroup.get('rate').value);
            workSummaryFormGroup.get('amount').setValue(this.fixNumberOnUI((quantity * rate), this.form.get('currency').value));
  
            workSummaryFormGroup.addControl('productServiceTypeId', this.formBuilder.control(fte?.productServiceTypeId));
            workSummaryFormGroup.addControl('chargeDescription', this.formBuilder.control(fte?.chargeDescription));
            workSummaryFormGroup.addControl('chargeType', this.formBuilder.control(fte?.chargeType));
            workSummaryFormGroup.addControl('chargeTypeName', this.formBuilder.control(fte?.chargeTypeName));    
            const taxConfig = this.itemConfig.find(configItem => configItem.key_name === 'tax');
            if(taxConfig){
              workSummaryFormGroup.addControl('taxId', this.formBuilder.control(fte?.taxId || null));
              workSummaryFormGroup.addControl('taxPercentage', this.formBuilder.control(fte?.taxPercentage || null));
              workSummaryFormGroup.get('tax')?.setValue(fte?.tax || null);
              workSummaryFormGroup.addControl('taxType', this.formBuilder.control(fte?.taxType || null));
            }
            let amount = this.parseValue(workSummaryFormGroup.get('amount').value);
            let id = workSummaryFormGroup.get('taxId')?.value;
            let type = workSummaryFormGroup.get('taxType')?.value;
            let lineItemNo = workSummaryFormGroup.get('lineItemNo')?.value;
            if (workSummaryFormGroup.get('taxId')?.value) {
              if(type.toLowerCase() == 'tax'){
                let item = this.taxMaster.find(obj => obj.id === id);
                workSummaryFormGroup.addControl('taxDetails', new FormControl(item));
                this.populateTaxArray(item, "tax", amount, lineItemNo)
              }
              else{
                let item = this.groupTaxMaster.find(obj => obj.id === id);
                workSummaryFormGroup.addControl('taxDetails', new FormControl(item));
                this.populateTaxArray(item, "group", amount, lineItemNo)
              }
            }
            workSummaryArray.push(workSummaryFormGroup);
            this.updateSubtotal();
            this.aggregateTax();
            this.updateTotalTaxAmount();
            this.updateTdsTcs();
            this.updateTotal();
            // this.aggregateTax()
            this.show = true;
            // When rate changes
            workSummaryFormGroup.get('rate').valueChanges.subscribe(() => {
              let currency = this.form.get('currency').value;
              const quantity = this.parseValue(workSummaryFormGroup.get('quantity').value);
              const rate = this.parseValue(workSummaryFormGroup.get('rate').value);
              workSummaryFormGroup.get('amount').setValue(this.fixNumberOnUI((quantity * rate), currency));
              this.updateSubtotal();
              this.updateTaxAmountonRateQuantityChange();
              this.updateTdsTcs();
              this.updateTotalTaxAmount();
              if (workSummaryFormGroup.get('taxType').value == "tax") {
                let lineItemno = workSummaryFormGroup.get('lineItemNo')?.value;
                let id = workSummaryFormGroup.get('taxId')?.value;
                let item = this.taxMaster.find(obj => obj.id === id);
                let amount = this.parseValue(workSummaryFormGroup.get('amount')?.value);
                this.populateTaxArray(item, "tax", amount, lineItemno)
              }
              if (workSummaryFormGroup.get('taxType').value == "group") {
                let lineItemno = workSummaryFormGroup.get('lineItemNo')?.value;
                let id = workSummaryFormGroup.get('taxId')?.value;
                let item = this.groupTaxMaster.find(obj => obj.id === id);
                let amount = this.parseValue(workSummaryFormGroup.get('amount')?.value);
                this.populateTaxArray(item, "group", amount, lineItemno)
              }
            });
  
            // When quantity changes
            workSummaryFormGroup.get('quantity').valueChanges.subscribe(() => {
              let currency = this.form.get('currency').value;
              const quantity = this.parseValue(workSummaryFormGroup.get('quantity').value);
              const rate = this.parseValue(workSummaryFormGroup.get('rate').value);
              workSummaryFormGroup.get('amount').setValue(this.fixNumberOnUI((quantity * rate), currency));
              this.updateSubtotal();
              this.updateTaxAmountonRateQuantityChange();
              this.updateTdsTcs();
              this.updateTotalTaxAmount();
              if (workSummaryFormGroup.get('taxType').value == "tax") {
                let lineItemno = workSummaryFormGroup.get('lineItemNo')?.value;
                let id = workSummaryFormGroup.get('taxId')?.value;
                let item = this.taxMaster.find(obj => obj.id === id);
                let amount = this.parseValue(workSummaryFormGroup.get('amount')?.value);
                this.populateTaxArray(item, "tax", amount, lineItemno)
              }
              if (workSummaryFormGroup.get('taxType').value == "group") {
                let lineItemno = workSummaryFormGroup.get('lineItemNo')?.value;
                let id = workSummaryFormGroup.get('taxId')?.value;
                let item = this.groupTaxMaster.find(obj => obj.id === id);
                let amount = this.parseValue(workSummaryFormGroup.get('amount')?.value);
                this.populateTaxArray(item, "group", amount, lineItemno)
              }
            });
  
          });
        }
      }
      else{
        let workSummaryFromDraft = this.prefilldata['workSummary'] ? this.prefilldata['workSummary'][0] : ''
        const workSummaryArray = this.form.get('workSummary') as FormArray;
        workSummaryArray.clear();
  
        const workSummaryFormGroup = this.formBuilder.group({}); // formgroup inside workSummary formarray is created
        this.itemConfig.forEach((configItem, index) => {   // Formcontrol created using key_name from config
          const keyName = configItem.key_name;
          let controlValue = workSummaryFromDraft[keyName] ? workSummaryFromDraft[keyName] : '';
          const validators = configItem.is_mandatory == 1 ? [Validators.required] : null;
          workSummaryFormGroup.addControl(keyName, this.formBuilder.control(controlValue, validators));
        });
        // workSummaryFormGroup.get('description').setValue(this.form.get("milestoneName").value)
  
        this.getFteTotalValue()
        workSummaryFormGroup.addControl('productServiceTypeId', this.formBuilder.control(workSummaryFromDraft?.productServiceTypeId));
        const taxConfig = this.itemConfig.find(configItem => configItem.key_name === 'tax');

        workSummaryFormGroup.addControl('chargeDescription', this.formBuilder.control(workSummaryFromDraft?.chargeDescription));
        workSummaryFormGroup.addControl('chargeType', this.formBuilder.control(workSummaryFromDraft?.chargeType));
        workSummaryFormGroup.addControl('chargeTypeName', this.formBuilder.control(workSummaryFromDraft?.chargeTypeName));        if(taxConfig){
          workSummaryFormGroup.addControl('taxId', this.formBuilder.control(workSummaryFromDraft?.taxId || null));
          workSummaryFormGroup.addControl('taxPercentage', this.formBuilder.control(workSummaryFromDraft?.taxPercentage || null));
          workSummaryFormGroup.get('tax')?.setValue(workSummaryFromDraft?.tax || null);
          workSummaryFormGroup.addControl('taxType', this.formBuilder.control(workSummaryFromDraft?.taxType || null));
        }
        // workSummaryFormGroup.addControl('quantity', this.formBuilder.control(1));
        let rate = this.parseValue(workSummaryFormGroup.get('rate').value);
        let quantity;
        if(workSummaryFormGroup?.get('quantity')){
          quantity = this.parseValue(workSummaryFormGroup.get('quantity')?.value);
          workSummaryFormGroup.get('amount').setValue(this.fixNumberOnUI((quantity * rate), currency));
        }
        else{
          workSummaryFormGroup.get('amount').setValue(this.fixNumberOnUI((rate), currency));
        }
        workSummaryArray.push(workSummaryFormGroup);
        let amount = this.parseValue(workSummaryFormGroup.get('amount').value);
        let id = workSummaryFormGroup.get('taxId')?.value;
        let type = workSummaryFormGroup.get('taxType')?.value;
        if (workSummaryFormGroup.get('taxId')?.value) {
          if(type.toLowerCase() == 'tax'){
            let item = this.taxMaster.find(obj => obj.id === id);
            workSummaryFormGroup.addControl('taxDetails', new FormControl(item));
            this.populateTaxArray(item, "tax", amount, 1)
          }
          else{
            let item = this.groupTaxMaster.find(obj => obj.id === id);
            workSummaryFormGroup.addControl('taxDetails', new FormControl(item));
            this.populateTaxArray(item, "group", amount, 1)
          }
        }
        this.updateSubtotal();
        this.updateTotal();
  
  
        // When rate changes
        workSummaryFormGroup.get('rate')?.valueChanges.subscribe(() => {
          let currency = this.form.get('currency').value;
          const rate = this.parseValue(workSummaryFormGroup.get('rate').value);
          let quantity;
          if(workSummaryFormGroup?.get('quantity')){
            quantity = this.parseValue(workSummaryFormGroup.get('quantity')?.value);
            workSummaryFormGroup.get('amount').setValue(this.fixNumberOnUI((quantity * rate), currency));
          }
          else{
            workSummaryFormGroup.get('amount').setValue(this.fixNumberOnUI((rate), currency));
          }
          this.updateSubtotal();
          this.updateTaxAmountonRateQuantityChange();
          this.updateTotalTaxAmount();
          this.updateTdsTcs();
          this.updateTotal();
          if (workSummaryFormGroup.get('taxType').value == "tax") {
            let lineItemno = workSummaryFormGroup.get('lineItemNo')?.value;
            let id = workSummaryFormGroup.get('taxId')?.value;
            let item = this.taxMaster.find(obj => obj.id === id);
            let amount = this.parseValue(workSummaryFormGroup.get('amount')?.value);
            this.populateTaxArray(item, "tax", amount, lineItemno)
          }
          if (workSummaryFormGroup.get('taxType').value == "group") {
            let lineItemno = workSummaryFormGroup.get('lineItemNo')?.value;
            let id = workSummaryFormGroup.get('taxId')?.value;
            let item = this.groupTaxMaster.find(obj => obj.id === id);
            let amount = this.parseValue(workSummaryFormGroup.get('amount')?.value);
            this.populateTaxArray(item, "group", amount, lineItemno)
          }
        });
  
        // When quantity changes
        workSummaryFormGroup.get('quantity')?.valueChanges.subscribe(() => {
          let currency = this.form.get('currency').value;
          const rate = this.parseValue(workSummaryFormGroup.get('rate').value);
          let quantity;
          if(workSummaryFormGroup?.get('quantity')){
            quantity = this.parseValue(workSummaryFormGroup.get('quantity')?.value);
            workSummaryFormGroup.get('amount').setValue(this.fixNumberOnUI((quantity * rate), currency));
          }
          else{
            workSummaryFormGroup.get('amount').setValue(this.fixNumberOnUI((rate), currency));
          }
  
          this.updateSubtotal();
          this.updateTaxAmountonRateQuantityChange();
          this.updateTdsTcs();
          this.updateTotalTaxAmount();
          if (workSummaryFormGroup.get('taxType').value == "tax") {
            let lineItemno = workSummaryFormGroup.get('lineItemNo')?.value;
            let id = workSummaryFormGroup.get('taxId')?.value;
            let item = this.taxMaster.find(obj => obj.id === id);
            let amount = this.parseValue(workSummaryFormGroup.get('amount')?.value);
            this.populateTaxArray(item, "tax", amount, lineItemno)
          }
          if (workSummaryFormGroup.get('taxType').value == "group") {
            let lineItemno = workSummaryFormGroup.get('lineItemNo')?.value;
            let id = workSummaryFormGroup.get('taxId')?.value;
            let item = this.groupTaxMaster.find(obj => obj.id === id);
            let amount = this.parseValue(workSummaryFormGroup.get('amount')?.value);
            this.populateTaxArray(item, "group", amount, lineItemno)
          }
        });
  
        this.getFteTotalValue();
        this.updateSubtotal();
        this.updateTotal();
        this.updateTdsTcs();
        this.updateTotalTaxAmount();
        this.show = true;
  
        }

    }

    if (this.form.get("serviceTypeGroupId")?.value == 2) {
      let workSummaryFromDraft = this.prefilldata['workSummary'] ? this.prefilldata['workSummary'][0] : ''
      const workSummaryArray = this.form.get('workSummary') as FormArray;
      workSummaryArray.clear();
      const workSummaryFormGroup = this.formBuilder.group({}); // formgroup inside workSummary formarray is created
      this.itemConfig.forEach((configItem, index) => {   // Formcontrol created using key_name from config
        const keyName = configItem.key_name;
        let controlValue = workSummaryFromDraft[keyName] ? workSummaryFromDraft[keyName] : '';
        const validators = configItem.is_mandatory == 1 ? [Validators.required] : null;
        workSummaryFormGroup.addControl(keyName, this.formBuilder.control(controlValue, validators));
      });

      let milestonevalue = this.form.get('milestoneValue').value
      workSummaryFormGroup.get('rate').setValue(milestonevalue);
      let rate = this.parseValue(workSummaryFormGroup.get('rate').value);
      let quantity = this.parseValue(workSummaryFormGroup.get('quantity').value);
      workSummaryFormGroup.get('amount').setValue(this.fixNumberOnUI((quantity * rate), currency));
      // workSummaryFormGroup.get('description').setValue(this.form.get("milestoneName").value)
      const taxConfig = this.itemConfig.find(configItem => configItem.key_name === 'tax');
      workSummaryFormGroup.addControl('chargeDescription', this.formBuilder.control(workSummaryFromDraft?.chargeDescription));
      workSummaryFormGroup.addControl('chargeType', this.formBuilder.control(workSummaryFromDraft?.chargeType));
      workSummaryFormGroup.addControl('chargeTypeName', this.formBuilder.control(workSummaryFromDraft?.chargeTypeName));
      if(taxConfig){
        workSummaryFormGroup.addControl('taxId', this.formBuilder.control(workSummaryFromDraft?.taxId || null));
        workSummaryFormGroup.addControl('taxPercentage', this.formBuilder.control(workSummaryFromDraft?.taxPercentage || null));
        workSummaryFormGroup.get('tax')?.setValue(workSummaryFromDraft?.tax || null);
        workSummaryFormGroup.addControl('taxType', this.formBuilder.control(workSummaryFromDraft?.taxType || null));
      }
      workSummaryFormGroup.addControl('productServiceTypeId', this.formBuilder.control(workSummaryFromDraft?.productServiceTypeId));
      workSummaryFormGroup.addControl('chargeDescription', this.formBuilder.control(workSummaryFromDraft?.chargeDescription));
      workSummaryFormGroup.addControl('chargeType', this.formBuilder.control(workSummaryFromDraft?.chargeType));
      workSummaryFormGroup.addControl('chargeTypeName', this.formBuilder.control(workSummaryFromDraft?.chargeTypeName));
      workSummaryArray.push(workSummaryFormGroup);
      let amount = this.parseValue(workSummaryFormGroup.get('amount').value);
      let id = workSummaryFormGroup.get('taxId')?.value;
      let type = workSummaryFormGroup.get('taxType')?.value;
      if (workSummaryFormGroup.get('taxId')?.value) {
        if(type.toLowerCase() == 'tax'){
          let item = this.taxMaster.find(obj => obj.id === id);
          workSummaryFormGroup.addControl('taxDetails', new FormControl(item));
          this.populateTaxArray(item, "tax", amount, 1)
        }
        else{
          let item = this.groupTaxMaster.find(obj => obj.id === id);
          workSummaryFormGroup.addControl('taxDetails', new FormControl(item));
          this.populateTaxArray(item, "group", amount, 1)
        }
      }
      this.updateSubtotal();
      this.updateTotalTaxAmount();
      this.updateTotal();
      this.show = true;

      // When rate changes
      workSummaryFormGroup.get('rate').valueChanges.subscribe(() => {
        let currency = this.form.get('currency').value;
        const quantity = this.parseValue(workSummaryFormGroup.get('quantity').value);
        const rate = this.parseValue(workSummaryFormGroup.get('rate').value);
        workSummaryFormGroup.get('amount').setValue(this.fixNumberOnUI((quantity * rate), currency));
        this.updateSubtotal();
        this.updateTaxAmountonRateQuantityChange();
        this.updateTotalTaxAmount();
        this.updateTdsTcs();
        this.updateTotal();
        if (workSummaryFormGroup.get('taxType').value == "tax") {
          let lineItemno = workSummaryFormGroup.get('lineItemNo')?.value;
          let id = workSummaryFormGroup.get('taxId')?.value;
          let item = this.taxMaster.find(obj => obj.id === id);
          let amount = this.parseValue(workSummaryFormGroup.get('amount')?.value);
          this.populateTaxArray(item, "tax", amount, lineItemno)
        }
        if (workSummaryFormGroup.get('taxType').value == "group") {
          let lineItemno = workSummaryFormGroup.get('lineItemNo')?.value;
          let id = workSummaryFormGroup.get('taxId')?.value;
          let item = this.groupTaxMaster.find(obj => obj.id === id);
          let amount = this.parseValue(workSummaryFormGroup.get('amount')?.value);
          this.populateTaxArray(item, "group", amount, lineItemno)
        }
      });

      // When quantity changes
      workSummaryFormGroup.get('quantity').valueChanges.subscribe(() => {
        let currency = this.form.get('currency').value;
        const quantity = this.parseValue(workSummaryFormGroup.get('quantity').value);
        const rate = this.parseValue(workSummaryFormGroup.get('rate').value);
        workSummaryFormGroup.get('amount').setValue(this.fixNumberOnUI((quantity * rate), currency));
        this.updateSubtotal();
        this.updateTaxAmountonRateQuantityChange();
        this.updateTotalTaxAmount();
        this.updateTdsTcs();
        this.updateTotal();
        if (workSummaryFormGroup.get('taxType').value == "tax") {
          let lineItemno = workSummaryFormGroup.get('lineItemNo')?.value;
          let id = workSummaryFormGroup.get('taxId')?.value;
          let item = this.taxMaster.find(obj => obj.id === id);
          let amount = this.parseValue(workSummaryFormGroup.get('amount')?.value);
          this.populateTaxArray(item, "tax", amount, lineItemno)
        }
        if (workSummaryFormGroup.get('taxType').value == "group") {
          let lineItemno = workSummaryFormGroup.get('lineItemNo')?.value;
          let id = workSummaryFormGroup.get('taxId')?.value;
          let item = this.groupTaxMaster.find(obj => obj.id === id);
          let amount = this.parseValue(workSummaryFormGroup.get('amount')?.value);
          this.populateTaxArray(item, "group", amount, lineItemno)
        }
      });

    }

    if (this.form.get("serviceTypeGroupId")?.value == 3) {
      let workSummaryFromDraft = this.prefilldata['workSummary'] ? this.prefilldata['workSummary'][0] : ''
      const workSummaryArray = this.form.get('workSummary') as FormArray;
      workSummaryArray.clear();
      const workSummaryFormGroup = this.formBuilder.group({}); // formgroup inside workSummary formarray is created
      this.itemConfig.forEach((configItem, index) => {   // Formcontrol created using key_name from config
        const keyName = configItem.key_name;
        let controlValue = workSummaryFromDraft[keyName] ? workSummaryFromDraft[keyName] : '';
        const validators = configItem.is_mandatory == 1 ? [Validators.required] : null;
        workSummaryFormGroup.addControl(keyName, this.formBuilder.control(controlValue, validators));
      });

      let rate = this.parseValue(workSummaryFormGroup.get('rate').value);
      let hours = this.parseValue(workSummaryFormGroup.get('quantity')?.value);
      let extraHours = this.parseValue(workSummaryFormGroup.get('extraHours')?.value);
      if (!isNaN(hours) && !isNaN(rate) && !isNaN(extraHours)) {
        workSummaryFormGroup.get('amount').setValue(this.fixNumberOnUI((extraHours + hours) * rate, currency));
      } else {
        workSummaryFormGroup.get('amount').setValue(0.00);
      }
      // workSummaryFormGroup.get('description').setValue(this.form.get("milestoneName").value)
      // workSummaryFormGroup.addControl('quantity', this.formBuilder.control(1));
      const taxConfig = this.itemConfig.find(configItem => configItem.key_name === 'tax');
      if(taxConfig){
        workSummaryFormGroup.addControl('taxId', this.formBuilder.control(workSummaryFromDraft?.taxId || null));
        workSummaryFormGroup.addControl('taxPercentage', this.formBuilder.control(workSummaryFromDraft?.taxPercentage || null));
        workSummaryFormGroup.get('tax')?.setValue(workSummaryFromDraft?.tax || null);
        workSummaryFormGroup.addControl('taxType', this.formBuilder.control(workSummaryFromDraft?.taxType || null));
      }
      workSummaryFormGroup.addControl('productServiceTypeId', this.formBuilder.control(workSummaryFromDraft?.productServiceTypeId));
      workSummaryArray.push(workSummaryFormGroup);
      let amount = this.parseValue(workSummaryFormGroup.get('amount').value);
      let id = workSummaryFormGroup.get('taxId')?.value;
      let type = workSummaryFormGroup.get('taxType')?.value;
      if (workSummaryFormGroup.get('taxId')?.value) {
        if(type.toLowerCase() == 'tax'){
          let item = this.taxMaster.find(obj => obj.id === id);
          workSummaryFormGroup.addControl('taxDetails', new FormControl(item));
          this.populateTaxArray(item, "tax", amount, 1)
        }
        else{
          let item = this.groupTaxMaster.find(obj => obj.id === id);
          workSummaryFormGroup.addControl('taxDetails', new FormControl(item));
          this.populateTaxArray(item, "group", amount, 1)
        }
      }
      this.updateSubtotal();
      this.updateTotalTaxAmount();
      this.updateTotal();
      this.show = true;

      // When rate changes
      workSummaryFormGroup.get('rate').valueChanges.subscribe(() => {
        let currency = this.form.get('currency').value;
        let rate = this.parseValue(workSummaryFormGroup.get('rate').value);
        let hours = this.parseValue(workSummaryFormGroup.get('quantity')?.value);
        let extraHours = this.parseValue(workSummaryFormGroup.get('extraHours')?.value);
        if (!isNaN(hours) && !isNaN(rate) && !isNaN(extraHours)) {
          workSummaryFormGroup.get('amount').setValue(this.fixNumberOnUI((extraHours + hours) * rate, currency));
        }
        else if (!isNaN(hours) && !isNaN(rate)) {
          workSummaryFormGroup.get('amount').setValue(this.fixNumberOnUI((hours) * rate, currency));
        }
        else {
          workSummaryFormGroup.get('amount').setValue(0.00);
        }
        this.updateSubtotal();
        this.updateTaxAmountonRateQuantityChange();
        this.updateTotalTaxAmount();
        this.updateTdsTcs();
        this.updateTotal();
        if (workSummaryFormGroup.get('taxType').value == "tax") {
          let lineItemno = workSummaryFormGroup.get('lineItemNo')?.value;
          let id = workSummaryFormGroup.get('taxId')?.value;
          let item = this.taxMaster.find(obj => obj.id === id);
          let amount = this.parseValue(workSummaryFormGroup.get('amount')?.value);
          this.populateTaxArray(item, "tax", amount, lineItemno)
        }
        if (workSummaryFormGroup.get('taxType').value == "group") {
          let lineItemno = workSummaryFormGroup.get('lineItemNo')?.value;
          let id = workSummaryFormGroup.get('taxId')?.value;
          let item = this.groupTaxMaster.find(obj => obj.id === id);
          let amount = this.parseValue(workSummaryFormGroup.get('amount')?.value);
          this.populateTaxArray(item, "group", amount, lineItemno)
        }
      });

      // When hours changes
      workSummaryFormGroup.get('quantity')?.valueChanges.subscribe(() => {
        let currency = this.form.get('currency').value;
        let rate = this.parseValue(workSummaryFormGroup.get('rate').value);
        let hours = this.parseValue(workSummaryFormGroup.get('quantity')?.value);
        let extraHours = this.parseValue(workSummaryFormGroup.get('extraHours')?.value);
        if (!isNaN(hours) && !isNaN(rate) && !isNaN(extraHours)) {
          workSummaryFormGroup.get('amount').setValue(this.fixNumberOnUI((extraHours + hours) * rate, currency));
        }
        else if (!isNaN(hours) && !isNaN(rate)) {
          workSummaryFormGroup.get('amount').setValue(this.fixNumberOnUI((hours) * rate, currency));
        }
        else {
          workSummaryFormGroup.get('amount').setValue(0.00);
        }
        this.updateSubtotal();
        this.updateTaxAmountonRateQuantityChange();
        this.updateTotalTaxAmount();
        this.updateTdsTcs();
        this.updateTotal();
        if (workSummaryFormGroup.get('taxType').value == "tax") {
          let lineItemno = workSummaryFormGroup.get('lineItemNo')?.value;
          let id = workSummaryFormGroup.get('taxId')?.value;
          let item = this.taxMaster.find(obj => obj.id === id);
          let amount = this.parseValue(workSummaryFormGroup.get('amount')?.value);
          this.populateTaxArray(item, "tax", amount, lineItemno)
        }
        if (workSummaryFormGroup.get('taxType').value == "group") {
          let lineItemno = workSummaryFormGroup.get('lineItemNo')?.value;
          let id = workSummaryFormGroup.get('taxId')?.value;
          let item = this.groupTaxMaster.find(obj => obj.id === id);
          let amount = this.parseValue(workSummaryFormGroup.get('amount')?.value);
          this.populateTaxArray(item, "group", amount, lineItemno)
        }
      });

      // When extra hours changes
      workSummaryFormGroup.get('extraHours')?.valueChanges.subscribe(() => {
        let currency = this.form.get('currency').value;
        let rate = this.parseValue(workSummaryFormGroup.get('rate').value);
        let hours = this.parseValue(workSummaryFormGroup.get('quantity')?.value);
        let extraHours = this.parseValue(workSummaryFormGroup.get('extraHours')?.value);
        if (!isNaN(hours) && !isNaN(rate) && !isNaN(extraHours)) {
          workSummaryFormGroup.get('amount').setValue(this.fixNumberOnUI((extraHours + hours) * rate, currency));
        }
        else if (!isNaN(hours) && !isNaN(rate)) {
          workSummaryFormGroup.get('amount').setValue(this.fixNumberOnUI((hours) * rate, currency));
        }
        else {
          workSummaryFormGroup.get('amount').setValue(0.00);
        }
        this.updateSubtotal();
        this.updateTaxAmountonRateQuantityChange();
        this.updateTotalTaxAmount();
        this.updateTdsTcs();
        this.updateTotal();
        if (workSummaryFormGroup.get('taxType').value == "tax") {
          let lineItemno = workSummaryFormGroup.get('lineItemNo')?.value;
          let id = workSummaryFormGroup.get('taxId')?.value;
          let item = this.taxMaster.find(obj => obj.id === id);
          let amount = this.parseValue(workSummaryFormGroup.get('amount')?.value);
          this.populateTaxArray(item, "tax", amount, lineItemno)
        }
        if (workSummaryFormGroup.get('taxType').value == "group") {
          let lineItemno = workSummaryFormGroup.get('lineItemNo')?.value;
          let id = workSummaryFormGroup.get('taxId')?.value;
          let item = this.groupTaxMaster.find(obj => obj.id === id);
          let amount = this.parseValue(workSummaryFormGroup.get('amount')?.value);
          this.populateTaxArray(item, "group", amount, lineItemno)
        }
      });

    }
    if (this.form.get("serviceTypeGroupId")?.value == 4) {
      const workSummaryArray = this.form.get('workSummary') as FormArray;
      workSummaryArray.clear();
      let workSummaryFromDraft = this.prefilldata['workSummary'] ? this.prefilldata['workSummary'] : '';
      if (workSummaryFromDraft && workSummaryFromDraft.length > 0) {
        workSummaryFromDraft.forEach((subLineItem, index) => {
          const workSummaryFormGroup = this.formBuilder.group({});

          this.itemConfig.forEach(configItem => {
            const keyName = configItem.key_name;
            let controlValue = null;
            switch (keyName) {
              case 'lineItemNo':
                controlValue = subLineItem.lineItemNo ? subLineItem.lineItemNo: index + 1;
                break;
              case 'description':
                controlValue = subLineItem.description != null ? subLineItem.description : "";
                break;
              case 'quantity':
                controlValue = subLineItem.quantity ? subLineItem.quantity : 1;
                break;
              case 'rate':
                controlValue = subLineItem.rate ? subLineItem.rate: 0.00;
                break;
              case 'productServiceType':
                controlValue = subLineItem.productServiceType ? subLineItem.productServiceType: "";
                break;
                case 'chargeType':
                  controlValue = "";
                  break;
            }
            const validators = configItem.is_mandatory == 1 ? [Validators.required] : null;
            workSummaryFormGroup.addControl(keyName, this.formBuilder.control(controlValue, validators));
          });
          const quantity = this.parseValue(workSummaryFormGroup.get('quantity').value);
          const rate = this.parseValue(workSummaryFormGroup.get('rate').value);
          workSummaryFormGroup.get('amount').setValue(this.fixNumberOnUI((quantity * rate), this.form.get('currency').value));

          workSummaryFormGroup.addControl('rpaId', this.formBuilder.control(subLineItem?.id));
          workSummaryFormGroup.addControl('productServiceTypeId', this.formBuilder.control(subLineItem?.productServiceTypeId));
          const taxConfig = this.itemConfig.find(configItem => configItem.key_name === 'tax');
          workSummaryFormGroup.addControl('chargeDescription', this.formBuilder.control(subLineItem?.chargeDescription));
          workSummaryFormGroup.addControl('chargeType', this.formBuilder.control(subLineItem?.chargeType));
          workSummaryFormGroup.addControl('chargeTypeName', this.formBuilder.control(subLineItem?.chargeTypeName));
          if(taxConfig){
            workSummaryFormGroup.addControl('taxId', this.formBuilder.control(subLineItem?.taxId || null));
            workSummaryFormGroup.addControl('taxPercentage', this.formBuilder.control(subLineItem?.taxPercentage || null));
            workSummaryFormGroup.get('tax')?.setValue(subLineItem?.tax || null);
            workSummaryFormGroup.addControl('taxType', this.formBuilder.control(subLineItem?.taxType || null));
          }
          let amount = this.parseValue(workSummaryFormGroup.get('amount').value);
          let id = workSummaryFormGroup.get('taxId')?.value;
          let type = workSummaryFormGroup.get('taxType')?.value;
          let lineItemNo = workSummaryFormGroup.get('lineItemNo')?.value;
          if (workSummaryFormGroup.get('taxId')?.value) {
            if(type.toLowerCase() == 'tax'){
              let item = this.taxMaster.find(obj => obj.id === id);
              workSummaryFormGroup.addControl('taxDetails', new FormControl(item));
              this.populateTaxArray(item, "tax", amount, lineItemNo)
            }
            else{
              let item = this.groupTaxMaster.find(obj => obj.id === id);
              workSummaryFormGroup.addControl('taxDetails', new FormControl(item));
              this.populateTaxArray(item, "group", amount, lineItemNo)
            }
          }
          workSummaryArray.push(workSummaryFormGroup);
          this.updateSubtotal();
          this.aggregateTax();
          this.updateTotalTaxAmount();
          this.updateTdsTcs();
          this.updateTotal();
          // this.aggregateTax()
          this.show = true;


          // When rate changes
          workSummaryFormGroup.get('rate').valueChanges.subscribe(() => {
            let currency = this.form.get('currency').value;
            const quantity = this.parseValue(workSummaryFormGroup.get('quantity').value);
            const rate = this.parseValue(workSummaryFormGroup.get('rate').value);
            workSummaryFormGroup.get('amount').setValue(this.fixNumberOnUI((quantity * rate), currency));
            this.updateSubtotal();
            this.updateTaxAmountonRateQuantityChange();
            this.updateTdsTcs();
            this.updateTotalTaxAmount();
            if (workSummaryFormGroup.get('taxType').value == "tax") {
              let lineItemno = workSummaryFormGroup.get('lineItemNo')?.value;
              let id = workSummaryFormGroup.get('taxId')?.value;
              let item = this.taxMaster.find(obj => obj.id === id);
              let amount = this.parseValue(workSummaryFormGroup.get('amount')?.value);
              this.populateTaxArray(item, "tax", amount, lineItemno)
            }
            if (workSummaryFormGroup.get('taxType').value == "group") {
              let lineItemno = workSummaryFormGroup.get('lineItemNo')?.value;
              let id = workSummaryFormGroup.get('taxId')?.value;
              let item = this.groupTaxMaster.find(obj => obj.id === id);
              let amount = this.parseValue(workSummaryFormGroup.get('amount')?.value);
              this.populateTaxArray(item, "group", amount, lineItemno)
            }
          });

          // When quantity changes
          workSummaryFormGroup.get('quantity').valueChanges.subscribe(() => {
            let currency = this.form.get('currency').value;
            const quantity = this.parseValue(workSummaryFormGroup.get('quantity').value);
            const rate = this.parseValue(workSummaryFormGroup.get('rate').value);
            workSummaryFormGroup.get('amount').setValue(this.fixNumberOnUI((quantity * rate), currency));
            this.updateSubtotal();
            this.updateTaxAmountonRateQuantityChange();
            this.updateTdsTcs();
            this.updateTotalTaxAmount();
            if (workSummaryFormGroup.get('taxType').value == "tax") {
              let lineItemno = workSummaryFormGroup.get('lineItemNo')?.value;
              let id = workSummaryFormGroup.get('taxId')?.value;
              let item = this.taxMaster.find(obj => obj.id === id);
              let amount = this.parseValue(workSummaryFormGroup.get('amount')?.value);
              this.populateTaxArray(item, "tax", amount, lineItemno)
            }
            if (workSummaryFormGroup.get('taxType').value == "group") {
              let lineItemno = workSummaryFormGroup.get('lineItemNo')?.value;
              let id = workSummaryFormGroup.get('taxId')?.value;
              let item = this.groupTaxMaster.find(obj => obj.id === id);
              let amount = this.parseValue(workSummaryFormGroup.get('amount')?.value);
              this.populateTaxArray(item, "group", amount, lineItemno)
            }
          });

        });

      }
      else {
        // If subLineItemdata is empty or null, add an empty form group to the FormArray
        const workSummaryFormGroup = this.formBuilder.group({});
        this.itemConfig.forEach((configItem, index) => {   // Formcontrol created using key_name from config
          const keyName = configItem.key_name;
          let controlValue = keyName === 'lineItemNo' ? (index + 1) : '';
          const validators = configItem.is_mandatory == 1 ? [Validators.required] : null;
          workSummaryFormGroup.addControl(keyName, this.formBuilder.control(controlValue, validators));
        });
        workSummaryFormGroup.addControl('productServiceTypeId', this.formBuilder.control(''));
        workSummaryFormGroup.addControl('chargeDescription', this.formBuilder.control(''));
        workSummaryFormGroup.addControl('chargeType', this.formBuilder.control(''));
        workSummaryFormGroup.addControl('chargeTypeName', this.formBuilder.control(''));
        workSummaryFormGroup.addControl('taxId', this.formBuilder.control(this.prefilldata.tax_id || null));
        workSummaryFormGroup.addControl('taxPercentage', this.formBuilder.control(this.prefilldata.tax_percentage || null));
        workSummaryFormGroup.get('tax')?.setValue(this.prefilldata.tax_description || null);
        workSummaryFormGroup.addControl('taxType', this.formBuilder.control(this.prefilldata?.tax_type || null));
        let amount = this.parseValue(workSummaryFormGroup.get('amount').value);
        let id = workSummaryFormGroup.get('taxId')?.value;
        let type = workSummaryFormGroup.get('taxType')?.value;
        if (workSummaryFormGroup.get('taxId')?.value) {
          if(type.toLowerCase() == 'tax'){
            let item = this.taxMaster.find(obj => obj.id === id);
            workSummaryFormGroup.addControl('taxDetails', new FormControl(item));
            this.populateTaxArray(item, "tax", amount, 1)
          }
          else{
            let item = this.groupTaxMaster.find(obj => obj.id === id);
            workSummaryFormGroup.addControl('taxDetails', new FormControl(item));
            this.populateTaxArray(item, "group", amount, 1)
          }
        }

        workSummaryArray.push(workSummaryFormGroup);
        this.show = true;

        // When rate changes
        workSummaryFormGroup.get('rate')?.valueChanges.subscribe(() => {
          let currency = this.form.get('currency').value;
          const quantity = this.parseValue(workSummaryFormGroup.get('quantity').value);
          const rate = this.parseValue(workSummaryFormGroup.get('rate').value);
          workSummaryFormGroup.get('amount').setValue(this.fixNumberOnUI((quantity * rate), currency));
          this.updateSubtotal();
          this.updateTaxAmountonRateQuantityChange();
          this.updateTotalTaxAmount();
          if (workSummaryFormGroup.get('taxType').value == "tax") {
            let lineItemno = workSummaryFormGroup.get('lineItemNo')?.value;
            let id = workSummaryFormGroup.get('taxId')?.value;
            let item = this.taxMaster.find(obj => obj.id === id);
            let amount = this.parseValue(workSummaryFormGroup.get('amount')?.value);
            this.populateTaxArray(item, "tax", amount, lineItemno)
          }
          if (workSummaryFormGroup.get('taxType').value == "group") {
            let lineItemno = workSummaryFormGroup.get('lineItemNo')?.value;
            let id = workSummaryFormGroup.get('taxId')?.value;
            let item = this.groupTaxMaster.find(obj => obj.id === id);
            let amount = this.parseValue(workSummaryFormGroup.get('amount')?.value);
            this.populateTaxArray(item, "group", amount, lineItemno)
          }
          this.aggregateTax()
        });

        // When quantity changes
        workSummaryFormGroup.get('quantity')?.valueChanges.subscribe(() => {
          let currency = this.form.get('currency').value;
          const quantity = this.parseValue(workSummaryFormGroup.get('quantity').value);
          const rate = this.parseValue(workSummaryFormGroup.get('rate').value);
          workSummaryFormGroup.get('amount').setValue(this.fixNumberOnUI((quantity * rate), currency));
          this.updateSubtotal();
          this.updateTaxAmountonRateQuantityChange();
          this.updateTdsTcs();
          this.updateTotalTaxAmount();
          if (workSummaryFormGroup.get('taxType').value == "tax") {
            let lineItemno = workSummaryFormGroup.get('lineItemNo')?.value;
            let id = workSummaryFormGroup.get('taxId')?.value;
            let item = this.taxMaster.find(obj => obj.id === id);
            let amount = this.parseValue(workSummaryFormGroup.get('amount')?.value);
            this.populateTaxArray(item, "tax", amount, lineItemno)
          }
          if (workSummaryFormGroup.get('taxType').value == "group") {
            let lineItemno = workSummaryFormGroup.get('lineItemNo')?.value;
            let id = workSummaryFormGroup.get('taxId')?.value;
            let item = this.groupTaxMaster.find(obj => obj.id === id);
            let amount = this.parseValue(workSummaryFormGroup.get('amount')?.value);
            this.populateTaxArray(item, "group", amount, lineItemno)
          }
        });
      }

    }



    this.botService.spinnerService.hide();
  }

  async populateFteDetailsFromDraft(data) {
    const fteDetailsArray = this.form.get('fteDetails') as FormArray;
    fteDetailsArray.clear();
    let currency = this.form.get('currency').value;
    if (data.length === 0 || data == 'undefined') {
      // Execute the code with default values for an empty data array
      const fteDetailsFormGroup = this.formBuilder.group({});

      this.fteconfig.forEach((configItem) => {
        const keyName = configItem.key_name;
        let fieldValidators = [];
        if (configItem.field_type === 'dropdown') {
          if (configItem.key_name === 'workLocation') {
            this.workLocationdropdownList(configItem)
          }
          if (configItem.key_name === 'title') {
            this.titledropdownList(configItem)
          }

        }
        if (keyName === 'perDayRate' || keyName === 'actualWorkingDays') {
          // Add Validators.min(1) for the specific field
          fieldValidators.push(Validators.min(0));
        }
        if (configItem.is_mandatory == 1) {
          fieldValidators.push(Validators.required);
        }
        // const validators = configItem.is_mandatory == 1 ? [Validators.required] : null;
        fteDetailsFormGroup.addControl(keyName, this.formBuilder.control('', fieldValidators));
      });
      fteDetailsFormGroup.addControl("associateOid", this.formBuilder.control(""));
      fteDetailsFormGroup.addControl("associateId", this.formBuilder.control(""));
      fteDetailsFormGroup.addControl("consultantName", this.formBuilder.control(""));
      fteDetailsFormGroup.addControl("entityId", this.formBuilder.control(""));
      fteDetailsFormGroup.addControl("positionId", this.formBuilder.control(""));
      fteDetailsFormGroup.addControl("isaId", this.formBuilder.control(""));
      fteDetailsFormGroup.addControl("subDivisionId", this.formBuilder.control(""));
      fteDetailsFormGroup.addControl("divisionId", this.formBuilder.control(""));
      fteDetailsArray.push(fteDetailsFormGroup);
      const actual = this.parseValue(fteDetailsFormGroup.get('actualWorkingDays').value);
      const planned = this.parseValue(fteDetailsFormGroup.get('perDayRate').value);
      fteDetailsFormGroup.get('fteTotalAmount').setValue(this.fixNumberOnUI((actual * planned), currency));

      fteDetailsFormGroup.get('perDayRate').valueChanges.subscribe(() => {
        let currency = this.form.get('currency').value;
        const actual = this.parseValue(fteDetailsFormGroup.get('actualWorkingDays').value);
        const planned = this.parseValue(fteDetailsFormGroup.get('perDayRate').value);
        fteDetailsFormGroup.get('fteTotalAmount').setValue(this.fixNumberOnUI((actual * planned), currency));
      });

      fteDetailsFormGroup.get('actualWorkingDays').valueChanges.subscribe(() => {
        let currency = this.form.get('currency').value;
        const actual = this.parseValue(fteDetailsFormGroup.get('actualWorkingDays').value);
        const planned = this.parseValue(fteDetailsFormGroup.get('perDayRate').value);
        fteDetailsFormGroup.get('fteTotalAmount').patchValue(this.fixNumberOnUI((actual * planned), currency));
      });

      fteDetailsFormGroup.get('fteTotalAmount').valueChanges.subscribe(() => {
        this.getFteTotalValue()
      });

      this.getFteTotalValue()
    }
    else {
      for (let item of data) {
        const fteDetailsFormGroup = this.formBuilder.group({});
        // Find the work location config
        const workLocationConfig = this.fteconfig.find(configItem => configItem.key_name === 'workLocation');
        if (workLocationConfig) {
          await this.workLocationdropdownList(workLocationConfig); // Wait for dropdown data
        }

        this.fteconfig.forEach((configItem) => {
          const keyName = configItem.key_name;
          let fieldValidators = []
          let initialValue;
          initialValue = item[keyName]; // Get the value from the data object

          if (keyName === 'perDayRate' || keyName === 'actualWorkingDays') {
            // Add Validators.min(1) for the specific field
            fieldValidators.push(Validators.min(0));
          }
          if (configItem.is_mandatory == 1) {
            fieldValidators.push(Validators.required);
          }
          if (configItem.key_name === 'title') {
            this.titledropdownList(configItem)
          }
          fteDetailsFormGroup.addControl(keyName, this.formBuilder.control(initialValue, fieldValidators));
        });
        fteDetailsFormGroup.addControl("associateId", this.formBuilder.control(item.id));
        fteDetailsFormGroup.addControl("consultantName", this.formBuilder.control(item.name));
        fteDetailsFormGroup.addControl("entityId", this.formBuilder.control(item.entity));
        fteDetailsFormGroup.addControl("positionId", this.formBuilder.control(item.position));
        fteDetailsFormGroup.addControl("isaId", this.formBuilder.control(item.isaId));
        fteDetailsFormGroup.addControl("subDivisionId", this.formBuilder.control(item.subDivsion));
        fteDetailsFormGroup.addControl("divisionId", this.formBuilder.control(item.divsionId));
        fteDetailsArray.push(fteDetailsFormGroup);



        const actual = this.parseValue(fteDetailsFormGroup.get('actualWorkingDays').value);
        const planned = this.parseValue(fteDetailsFormGroup.get('perDayRate').value);
        fteDetailsFormGroup.get('fteTotalAmount').setValue(this.fixNumberOnUI((actual * planned), currency));

        fteDetailsFormGroup.get('perDayRate').valueChanges.subscribe(() => {
          let currency = this.form.get('currency').value;
          const actual = this.parseValue(fteDetailsFormGroup.get('actualWorkingDays').value);
          const planned = this.parseValue(fteDetailsFormGroup.get('perDayRate').value);
          fteDetailsFormGroup.get('fteTotalAmount').setValue(this.fixNumberOnUI((actual * planned), currency));
        });

        fteDetailsFormGroup.get('actualWorkingDays').valueChanges.subscribe(() => {
          let currency = this.form.get('currency').value;
          const actual = this.parseValue(fteDetailsFormGroup.get('actualWorkingDays').value);
          const planned = this.parseValue(fteDetailsFormGroup.get('perDayRate').value);
          fteDetailsFormGroup.get('fteTotalAmount').setValue(this.fixNumberOnUI((actual * planned), currency));
        });

        fteDetailsFormGroup.get('fteTotalAmount').valueChanges.subscribe(() => {
          this.getFteTotalValue()
        });


      }
      this.getFteTotalValue()
      this.showWarningMsg()
    }
  }

  async addAttachment(){
    let milestone_id = this.form.get('milestoneId').value
    let gantt_id = this.prefilldata?.ganttId
    let context_id = this.prefilldata?.context_id ? this.prefilldata?.context_id : "INV_" + milestone_id + gantt_id
    const dialogRef = this.dialog.open(AttachFilesComponent, {
      data: { 
      contextId: context_id,
      destinationBucket: 'kebs-invoices',
      routingKey: 'invoices',
      showUpload: true,
      enableFileRemoval: true 
      },
      disableClose: true
    });
    dialogRef.afterClosed().subscribe(result => {
      // Change the flag after dialog closes
      this.prefilldata.is_attachment = result["result"] ? true : false;
      this.invoiceAttachmentsCount = result?.fileCount || 0;
    });
  }

  async DisplayAttachment(){
    let milestone_id = this.form.get('milestoneId').value
    let gantt_id = this.prefilldata?.ganttId
    let context_id = "PRJ_" + milestone_id + gantt_id
    const dialogRef = this.dialog.open(AttachFilesComponent, {
      data: { 
      contextId: context_id,
      destinationBucket: 'kebs-project-s3',
      routingKey: 'project',
      showUpload: false,
      enableFileRemoval: false 
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      // Change the flag after dialog closes
      this.prefilldata.is_project_attachment = result["result"] ? true : false;
      this.projectAttachmentsCount = result?.fileCount || 0;
    });
  }
}



