
import {
  Component,
  OnInit,
  Input,
  SimpleChanges,
  EventEmitter,
  Output,
  Inject
} from "@angular/core";
import * as html2pdf from "html2pdf.js";
import { PdfViewerService } from "../../../invoice-generation/services/pdf-viewer.service";
import * as moment from "moment";
import { InvoiceGenerationService } from "../../../invoice-generation/services/invoice-generation.service";
import { ActivatedRoute, Router } from "@angular/router";
import { InvoiceCommonService } from "../../../../common-services/invoice-common.service";
import * as _ from "underscore";
import swal from "sweetalert2";
import { UtilityService } from 'src/app/services/utility/utility.service';
import { HexBase64BinaryEncoding } from "crypto";
import { NgxSpinnerService } from "ngx-spinner";
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';

@Component({
  selector: 'app-invoice-pdf',
  templateUrl: './invoice-pdf.component.html',
  styleUrls: ['./invoice-pdf.component.scss']
})
export class InvoicePdfComponent implements OnInit {

  invoice_pdf_config: any;
  legal_entity_details:any;
  title:any;
  consultantDetails:any;
  // @Input() otherMilestoneDataToPdf: any;
  // @Input() usedIn: any;
  // @Input() invoiceTenantDetails: any;
  // @Input() serviceTypeGroupId : any
  otherMilestoneDataToPdf:any
  usedIn:any;
  invoiceTenantDetails:any;
  serviceTypeGroupId:any;
  otherMilestoneData: any;
  milestoneId: any;
  otherMile = [];
  serviceTypeId: any;
  saveButtonEnabled = true;
  step = 0;
  enableArabic = false;
  fteD: any;
  invoiceTenantLogoHeight:any;
  invoiceTenantLogoWidth:any;
  invoice_information: any = {
    title_company: "Kaar Technologies India Pvt Ltd",
    from_address:
      " #136, Level-8 Shyamala Towers, Arcot Road, Chennai-93, TN, India.",
    cin: "U72200TN2005PTC087065",
    ph: "+91(44)4065 1500",
    fx: "+91(44)4065 1500",
    inv_dt: "10-Jan-15",
    inv_no: "KT/KTQ/DUCAB/0115/1",
    po_dt: "11 Nov 2014 ",
    po_ref: "Kaar/KTUAE/2014-15/007",
    iec: "**********",
    gst_in: "33AACCK7322E2ZX",
    hsn_code: "********",
    sac_code: "998313",
    rcm: "No",
    payment_terms: "Back to Back With Clients",
    payment_mode: "bank transfer",
    payment_currency: "USD",
    favouring: "Kaar Technologies India Pvt Ltd",
    bank_accno: "****************",
    bank_name: "IDBI BANK LIMITED ",
    swift_code: "IBLIKFG56774 ",
    ifsc_code: "IBLIKFG56774 ",
    bank_branch: "Branch Chennai, T.Nagar Branch",
    bank_addr: "Branch Chennai, T.Nagar Branch",
    to: "Kaar Technologies Qatar L.L.C",
    toaddr: "Commercial Plaza, Level 15 West Bay, Po Box 27111 Doha, Qatar.",
    table_data: {
      subtotal: "21212",
      tax_name: "VAT",
      tax_percent: "5",
      tax_amt: "1000.00",
      total: "100120.00",
      total_in_words: "Five Thousand Six Hundred Eighty five only",
      items: [
        {
          description: "	Invoice for Nupco Feb'19 to Jan'20 Support",
          rate: "4685.00",
          quantity: "1",
          amount: "	4685.00"
        },
        {
          description: "	Invoice for Nupco Feb'19 to Jan'20 Support",
          rate: "4685.00",
          quantity: "1",
          amount: "	4685.00"
        }
      ]
    }
  };

  staticTranslations: any = {
    invoice: "فاتورة",
    billTo: "فاتورة الى",
    invoiceDate: "تاريخ الفاتورة",
    invoiceNo: "رقم الفاتورة",
    poDt: "تاريخ أمر الشراء",
    poRef: "مرجع أمر الشراء",
    iecCode: "رمز IEC",
    hsnCode: "رمز HSN",
    sacCode: "رمز SAC",
    rcmApp: "RCM ينطبق",
    amtChargInWords: "المبلغ تحميلها في الكلمات",
    subTotal: "حاصل الجمع",
    tax: "ضريبة",
    total: "مجموع",
    paymentTerm: "شروط الدفع",
    PaymentMode: "طريقة الدفع",
    PaymentCurrency: "عملة الدفع",
    Favouring: "صالح",
    bankAccNo: "رقم الحساب المصرفي",
    bankName: "اسم البنك",
    swiftCode: "رمز البنك السريع",
    ifscCode: "رمز البنك IFSC",
    bankBranch: "فرع بنك",
    bankAddress: "عنوان البنك",
    rate: "معدل",
    quantity: "كمية",
    amount: "السعر",
    description: "وصف"
  };
  @Output() nextButtonClickedFlag: EventEmitter<any> = new EventEmitter<any>();
  @Output() pdfDataOut: EventEmitter<any> = new EventEmitter<any>();
  // @Input() invoiceInfo: any;
  // @Input() pdfNextClicked;
  invoiceInfo:any;
  pdfNextClicked:any;
  today = moment(new Date()).format("DD-MM-YYYY");

  entityLogo: any;

  image: any;

  logo: any;

  companyCode: any;

  fte = [];
  
  showSpinner = false;

  currencyDetails:any;
  qr_code_applicable:any;
  show_qr_code: boolean = false;

  dateFormats:any;
  logoWidth : any;
  logoHeight: any;
  showPONumberInPDF: boolean = false;
  constructor(
    public pdfService: PdfViewerService,
    private invoiceService: InvoiceGenerationService,
    private invoiceCommonService: InvoiceCommonService,
    private router: Router,
    private route: ActivatedRoute,
    private utilityService: UtilityService,
    private SpinnerService: NgxSpinnerService,
    public dialogRef: MatDialogRef<InvoicePdfComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    // if(this.pdfService.data[0].customerName.includes( 'BaasKaar IT Company')){
    //   this.enableArabic = true;
    // }
  }

  //accordian function
  async setStep(index: number, invoiceInfo,i:any) {
    console.log("inside setStep")
    console.log(invoiceInfo)
    this.SpinnerService.show(); 
    this.step = index;
    this.invoice_pdf_config = await this.getInvoicePDFConfig(invoiceInfo.fromCompanyCode, invoiceInfo.itemId, invoiceInfo.projectId, invoiceInfo.customerId, invoiceInfo.legalEntityId);
    this.qr_code_applicable = await this.getLegalEntityQRConfig(invoiceInfo?.legalEntityId)
    this.show_qr_code = this.qr_code_applicable[0]?.is_to_show_qr_code_in_pdf
    let logoData = await this.getEntityLogo(invoiceInfo.fromCompanyCode);
    this.image = logoData["logo"]
    this.logoHeight = logoData['height']
    this.logoWidth = logoData['width']
    this.consultantDetails = await this.getconsultantDetail(this.otherMilestoneDataToPdf)
    this.title=this.legal_entity_details && this.legal_entity_details[0] && this.legal_entity_details[0].title
    ? this.legal_entity_details[0].title
    : "Invoice";
    this.invoiceInfo[i].panelOpenState=1;
    this.SpinnerService.hide();  

  }

  closeStep(index:number, invoiceInfo){
    this.invoiceInfo[index].panelOpenState=0;
  }
  openAnnexureStep(index:number){
    this.otherMilestoneDataToPdf[index].annexureOpenState=1;

  }
  closeAnnexureStep(index:number){
    this.otherMilestoneDataToPdf[index].annexureOpenState=0;

  }
  currencyConversionCheck(){
        this.showSpinner=true;
        this.invoiceService.currencyConversionCheckFunc(this.invoiceInfo).subscribe(
          res => {
            this.showSpinner=false;
            console.log(res)
            if(res["messType"] == "C"){
              console.log(res["data"].join(' & '))
              swal.fire({ title: "Please add the missing currency conversion rate for " + res["fromCurrency"] +" to " +res["data"].join(', ')+ " for the period " + res["invoiceRaisedPeriod"] +" !" , icon: "info", showConfirmButton: true });
            }
            else if(res["messType"] == "S"){
                 this.save()
            }
            else if(res["messType"] == "E"){
              swal.fire({ title: "Currency Conversion Check Failed !", icon: "info", showConfirmButton: true });
              this.utilityService.showMessage(res["messText"], 'dismiss', 3000);
            }
          },
          err => {
            this.showSpinner=false;
            swal.fire({ title: "Currency Conversion Check Failed !", icon: "info", showConfirmButton: true });
          }
        );
      }

  save() {
    this.moveToBilled();
    this.saveButtonEnabled = false;
    let token = ''
    this.invoiceService.saveInvoiceStepperDetails(this.invoiceInfo,token).subscribe(
      res => {
        if (res["messType"] == "S") {
          let swalTemplate = {
            customClass: {
              title: "title-class",
              confirmButton: "confirm-button-class"
            },
            type: "success",
            title: "Milestone Billed successfully"
          }
          swal.fire({ title: "Milestone Billed successfully !", icon: "success", showConfirmButton: true });
        }
        else if (res["messType"] == "E") {
          swal.fire({ title: "Milestone Billing Failed !", icon: "info", showConfirmButton: true });
          this.utilityService.showMessage(res["messText"], 'dismiss', 3000);
        }
      },
      err => {
        console.log(err);
        swal.fire({ title: "Milestone Billing Failed !", icon: "info", showConfirmButton: true });
      }
    );
  }

  prevStep() {
    this.step--;
  }

  generatePdf(i, from, to) {
    var element = document.getElementById(i);
    var opt1 = {
      filename: from + " to " + to + ".pdf",
      margin: 0,
      format: "a4",
      orientation: "portrait",
      type: "image",
      quality: "100",
      zoomFactor: "1"
      // format: { format: "a4", orientation: "portrait" },
      // html2canvas: { scale: 1 },
      //pdf: { type: "pdf", quality: 100 }
    };
    // New Promise-based usage:
    html2pdf()
      .from(element)
      .set(opt1)
      .save();
  }

  generatePdfAnnex(j, from, to) {
    var element = document.getElementById(j);
    var opt1 = {
      filename: from + " to " + to + "-annexure.pdf",
      margin: 0,
      html2canvas: { scale: 2 },
      image: { type: "jpeg", quality: 1 }
    };
    // New Promise-based usage:
    html2pdf()
      .from(element)
      .set(opt1)
      .save();
  }

  // getDate(date) {
  //   if (date == null) {
  //     return "-";
  //   } else return moment(date).format(this.invoiceTenantDetails.invoice_pdf_date_format.toUpperCase());
  // }
  getDate(date) {
    if (date == null) {
      return "-";
    } else {
      let invoice_date_format = this.dateFormats?.pdf_date_format ? this.dateFormats?.pdf_date_format : "DD-MMM-YYYY"
      return moment(date).format(invoice_date_format);
    }
  }
  getTotal(data) {
    let total = 0;
    data.forEach((element, index) => {
      const value = element.milestone_value[0].value.toString().replace(/,/g, '');
      const parsedValue = parseFloat(value);
      if (!isNaN(parsedValue)) {
        total += parsedValue;
      }
    });
    return total;
  }

  getFteTotal(data) {
    let total = 0;
    console.log(data)
    data.forEach((element) => {
      const value = element.actualWorkingDays.toString().replace(/,/g, '');
      const rate_value = element.perDayRate.toString().replace(/,/g, '');
      const parsedValue = parseFloat(value);
      const parsedRate = parseFloat(rate_value);
      
      if (!isNaN(parsedValue) && !isNaN(parsedRate)) {
        total += parsedValue * parsedRate;
      }
    });
    return total;
  }

  moveToBilled() {
    this.router.navigateByUrl("/main/invoice/invoicelist/1");
  }

  // author : A.Amal Anush
  // the function disables the rate column for the invoice sent to KTUK / KTUS --- as said by RAVI,Finance

  checkConditionForKtusAndKtuk(invoice) {
    if (
      invoice.toCompanyCode == "KTUS" ||
      invoice.toCompanyCode == "KTUK" ||
      invoice.fromCompanyCode == "KTUS" ||
      invoice.fromCompanyCode == "KTUK"
    )
      return true;
    else return false;
  }

  nextInPdfClicked() {
    this.pdfDataOut.emit(this.invoiceInfo);
    // if(this.pdfNextClicked == 1){
    //   setTimeout(()=>{

    //     swal.fire({
    //       customClass:{
    //         title: "title-class",
    //         confirmButton: 'confirm-button-class',
    //       },
    //       title: "No Payment activity found for this customer",
    //       text:"Please proceed with default template",
    //       type: "info"
    //     }

    //     )

    //   },1000)
    // }
  }
  getMonthFormat(date) {
    let invoice_date_format = this.dateFormats?.pdf_date_format ? this.dateFormats?.pdf_date_format : "DD-MMM-YYYY"
    return moment(date).format(invoice_date_format);
    // return moment(date).format("MMMM Do YYYY");
  }
  fixNumberOnUI(no,currency) {
    // console.log(no)
    let num = parseFloat(no).toFixed(2);
    // console.log(num)
    if(currency == "INR")
        return  new Intl.NumberFormat('en-IN', { minimumFractionDigits: 2, maximumFractionDigits: 2 }).format(Number(num)); 
    else
        return  new Intl.NumberFormat('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }).format(Number(num)); 
  }

  parseValue(input){
    if (typeof input === 'string') {
      const sanitizedInput = input.trim(); // Remove leading/trailing spaces
      return sanitizedInput === '' ? 0.00 : parseFloat(sanitizedInput.replace(/,/g, ''));
      // return parseFloat(input.replace(/,/g, ''));
    } 

    return input

  }


  async ngOnInit() {
    // this.invoiceTenantLogoHeight = this.invoiceTenantDetails.invoice_logo_height;
    // this.invoiceTenantLogoWidth = this.invoiceTenantDetails.invoice_logo_width;
    this.serviceTypeId = this.route.snapshot.params["serviceTypeId"];
    // this.invoiceInfo = this.pdfService.data;
    this.milestoneId = this.route.snapshot.params["milestoneId"];
    let invoiceNumberArray;
    let legalEntityArray = _.pluck(this.invoiceInfo, "legalEntityId");
    this.otherMilestoneDataToPdf = this.data.otherMilestoneDataToPdf;
    this.usedIn = this.data.usedIn;
    this.invoiceTenantDetails = this.data.invoiceTenantDetails;
    if(this.invoiceTenantDetails && this.invoiceTenantDetails.hasOwnProperty('is_to_show_po_number_in_pdf')){
      this.showPONumberInPDF = this.invoiceTenantDetails['is_to_show_po_number_in_pdf']
    }
    this.serviceTypeGroupId = this.data.serviceTypeGroupId
    this.invoiceInfo = this.data.invoiceInfo;
    this.getCurrencyDetails()
    this.dateFormats = await this.getTenantDateFormats()
    this.otherMilestoneDataToPdf?.forEach((item)=>{
      item.annexureOpenState = 0;
    })   
    this.invoiceInfo?.forEach((item)=>{
      item.panelOpenState = 0;
    })
    
    // console.log(typeof JSON.stringify(legalEntityArray));

    // this.invoiceService.getInvoiceNumber(JSON.stringify(legalEntityArray)).subscribe(res =>{
    //   invoiceNumberArray = res;
    //   invoiceNumberArray.forEach((element, index) => {
    //     this.invoiceInfo[index].invoiceNo = element;
    //   });
    // })
  }



  ngOnChanges(changes: SimpleChanges): void {
    console.log("inside ng onchanges")
    console.log(this.invoiceInfo)
    console.log(this.usedIn)
    if (this.invoiceInfo != null) {
      console.log("on changes", this.invoiceInfo);
      this.invoiceInfo.forEach((item)=>{
        item.panelOpenState = 0;
      })
    }


    if (this.otherMilestoneDataToPdf != null &&
      this.otherMilestoneDataToPdf.length == 1) {
        console.log("other milestone data")
        console.log(this.otherMilestoneDataToPdf)
      this.otherMilestoneData = this.otherMilestoneDataToPdf; 
      console.log(this.otherMilestoneData)
      this.otherMilestoneData.forEach((item)=>{
            item.annexureOpenState = 0;
          })
    }
    if (
      this.otherMilestoneDataToPdf != null &&
      this.otherMilestoneDataToPdf.length == 2
    ) {
      console.log(this.otherMilestoneDataToPdf);
      this.otherMilestoneData = this.otherMilestoneDataToPdf;
      this.otherMilestoneData = this.otherMilestoneDataToPdf; 
      this.otherMilestoneData.forEach((item)=>{
            item.annexureOpenState = 0;
          })             
    }
    if (
      this.otherMilestoneDataToPdf != null &&
      this.otherMilestoneDataToPdf.length == 2 &&
      this.invoiceInfo != null
    ) {
      console.log(this.invoiceInfo);
      this.otherMilestoneData.forEach((element, index) => {
        element.invoiceNo = this.invoiceInfo[index].invoiceNo;
        element.annexureOpenState=0;
        console.log(this.otherMilestoneData);
      });
    }

    this.fteD = this.invoiceInfo && this.invoiceInfo.length > 0 ? this.invoiceInfo[0].FTEDetails ? this.invoiceInfo[0].FTEDetails[0].per_hour_rate : 0 : 0;

    if (this.fteD != 0)
      this.fteD = parseFloat(this.fteD).toFixed(2);
    
  }

  /** 
  * @description getInvoicePDFConfig
  */
  getInvoicePDFConfig = (companyCode, itemId, projectId, customerId, legalEntityId) => {
    return new Promise((resolve, reject) => {
      this.invoiceCommonService.getInvoicePdfConfig(companyCode, itemId, projectId, customerId, legalEntityId).subscribe(res => {
        //this.invoice_pdf_config = res['data'];
        this.legal_entity_details=res['legal_entity_details'];
        console.log(res);
        resolve(res['data']);
      },
        (err) => {
          console.error(err);
          reject(err);
        }
      );
    });
  };

  /** 
 * @description getInvoiceEntityLogo
 */
  getEntityLogo = (companyCode) => {
    return new Promise((resolve, reject) => {
      this.invoiceService.getEntityLogo(companyCode).subscribe(res => {
        resolve(res['data']);
      },
        (err) => {
          console.error(err);
          reject(err);
        }
      );
    });
  }; 


getconsultantDetail = (otherMilestoneDataToPdf) => {
  return new Promise((resolve, reject) => {
    this.invoiceCommonService.getconsultantDetail(otherMilestoneDataToPdf).subscribe(res => {
      resolve(res['data']);
    },
      (err) => {
        console.error(err);
        reject(err);
      }
    );
  });
};

closeDialog() {
  this.dialogRef.close();
}

async getCurrencyDetails() {
  this.currencyDetails = await this.invoiceService.getCurrencyDetails();
}

getCurrencySymbol(selectedCurrency: string): string {
  const currencyDetail = this.currencyDetails?.find(detail => detail.currency_code == selectedCurrency);
  return currencyDetail ? currencyDetail.currency_symbol : "";
}
getUnit(unit) {
  if (unit == '') {
    return 'hour';
  }
  if (unit == 'Days') {
    return 'day';
  }
  if (unit == 'Hours') {
    return 'hour';
  }
  if (unit == 'Month') {
    return 'month';
  }
  if(unit == 'Page'){
    return 'page';
  }
}
  //Get Tenant Date Formats
  getTenantDateFormats() {
    return new Promise((resolve, reject) => {
      this.invoiceCommonService.getTenantDateFormats().subscribe(
        (res: any) => {
          resolve(res.data);
        },
        (err) => {
          console.log(err);
          resolve(err);
        }
      );
    });

  }

      /** 
  * @description getLegalEntityQRConfig
  */
      getLegalEntityQRConfig = (legalEntityId) => {
        return new Promise((resolve, reject) => {
          this.invoiceService.getLegalEntityQRConfig(legalEntityId).subscribe(res => {
            //this.invoice_pdf_config = res['data'];
            resolve(res['data']);
          },
            (err) => {
              console.error(err);
              reject(err);
            }
          );
        });
       };

  calculateColspan(item: any, type): number {
      if (this.invoiceTenantDetails?.is_to_show_product_service_template && item.workSummary[0]?.productServiceType) {
        return this.itemHasTax(item) ? 9 : 8;
      }
      else if(this.itemHasTax(item)){
        return 6;
      }
      else{
        return 5;
      }
  }
  itemHasTax(item: any): boolean {
    return item.workSummary?.some(ws => ws.tax);
  }

  normalizeUnit(unit: string): string {
    if (!unit) {
      return 'hour';
    }
    unit = unit.toLowerCase();
    if (unit === 'hours' || unit === 'hour') {
      return 'hour';
    }
    if (unit === 'days' || unit === 'day') {
      return 'day';
    }
    if (unit == 'month' || unit == 'months') {
      return 'month';
    }
    if (unit == 'page' || unit == 'pages') {
      return 'page';
    }
    return unit;
  }

}

