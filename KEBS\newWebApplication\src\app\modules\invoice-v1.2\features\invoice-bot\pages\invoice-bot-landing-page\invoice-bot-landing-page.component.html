<div class="Main">
  <ng-container *ngIf="show == true">

    <div class="row">
      <div class="col invoice-title pt-3 ml-4">Invoice Creation</div>
      <div class="col-5 d-flex justify-content-end pr-5">
        <div class="mt-5 d-flex justify-content-end btn-tools">
          <div *ngIf="invoiceFormValue && stepper?.selectedIndex <= data.length - 1" class="justify-content-end mr-4">
            <div class="row justify-content-center">
              <span style="font-size: 10px; color: #6E7B8F; font-weight: 400;line-height: 16px; letter-spacing: 0.2px;">
                TOTAL AMOUNT
              </span>
            </div>
            <div class="row" [matMenuTriggerFor]="menu"
              style="color:#cf0001; text-decoration: underline; cursor: pointer; justify-content: center; font-weight: 600; line-height: 16px; letter-spacing: 0.28px;">
              <span>{{formatNumberForDisplay(invoiceFormValue?.totalAmount,invoiceFormValue?.currency)}}
                {{invoiceFormValue?.currency}}</span>
            </div>
            <mat-menu class="my-class label-name mt-1" #menu [overlapTrigger]="false" [yPosition]="'below'"
              [xPosition]="'before'">
              <div class="container">
                <div class="row pt-1 tax-data" style="font-weight: 500;">
                  <div class="col-6 mt-2 p-0" style="font-size: 15px;">Invoice Breakdown</div>
                  <div class="col d-flex p-0 justify-content-end">
                    <button mat-button [matMenuTriggerFor]="menu" #MenuTrigger="matMenuTrigger">
                      <mat-icon style="color:#cf0001; font-size: 18px;">close</mat-icon>
                    </button>
                  </div>
                </div>
                <hr class="p-0 m-0">
                <div class="row pt-1 tax-data">
                  <div class="col-4 p-0">Subtotal</div>
                  <div class="col d-flex justify-content-end">
                    {{formatNumberForDisplay(parseValue(invoiceFormValue?.subTotal),invoiceFormValue?.currency)}}</div>
                </div>
                <div class="row pt-1 tax-data">
                  <div class="col-4 p-0">Total Tax</div>
                  <div class="col d-flex justify-content-end">
                    {{formatNumberForDisplay(parseValue(totalTaxAmount),invoiceFormValue?.currency)}}
                  </div>
                </div>
                <div class="row pt-1 tax-data">
                  <div class="col-4 p-0">Discount</div>
                  <div class="col d-flex justify-content-end">-
                    {{formatNumberForDisplay(parseValue(invoiceFormValue?.discountAmount),invoiceFormValue?.currency)}}
                  </div>
                </div>
                <div class="row pt-1 tax-data">
                  <div class="col-4 p-0">Retention</div>
                  <div class="col d-flex justify-content-end">
                    {{formatNumberForDisplay(parseValue(invoiceFormValue?.retentionAmount),invoiceFormValue?.currency)}}
                  </div>
                </div>
                <div class="row pt-1 tax-data" *ngIf="invoiceFormValue?.selectedOption == 'tcs'">
                  <div class="col-4 p-0">TCS</div>
                  <div class="col d-flex justify-content-end">-
                    {{formatNumberForDisplay(parseValue(invoiceFormValue?.tcsValue),invoiceFormValue?.currency)}}</div>
                </div>
                <div class="row pt-1 tax-data" *ngIf="invoiceFormValue?.selectedOption == 'tds'">
                  <div class="col-4 p-0">TDS</div>
                  <div class="col d-flex justify-content-end">-
                    {{formatNumberForDisplay(parseValue(invoiceFormValue?.tdsValue),invoiceFormValue?.currency)}}</div>
                </div>
                <hr class="p-0 m-0">
                <div class="row pt-2 pb-3 tax-data">
                  <div class="col-4 p-0" style="font-weight: 500;">Total ({{invoiceFormValue?.currency}})</div>
                  <div class="col d-flex justify-content-end">
                    {{formatNumberForDisplay(parseValue(invoiceFormValue?.totalAmount),invoiceFormValue?.currency)}}
                  </div>
                </div>
              </div>
            </mat-menu>
          </div>
          <button class="mr-2 btn-class" *ngIf="stepper?.selectedIndex > 0" (click)="previousStepClicked()">
            <span>Previous</span>
          </button>
          <button class="btn-class" *ngIf="stepper?.selectedIndex < stepper.steps.length - 1"
            (click)="nextStepClicked()" [ngClass]="{
        buttonOff: !isCurrentStepValid(),
        buttonOn: isCurrentStepValid()
      }">
            <span>Next</span>
          </button>
          <button class="mr-2 btn-class pl-2 pr-2" *ngIf="stepper?.selectedIndex === stepper.steps.length - 1" (click)="saveAsDraft()">
            <span>Save</span>
          </button>
          <button class="mr-2 btn-save" *ngIf="stepper?.selectedIndex === stepper.steps.length - 1" (click)="save()"
            [ngClass]="{
        buttonOff: !isCurrentStepValid(),
        buttonOn: isCurrentStepValid()
      }">
            <span>Submit</span>
          </button>
          <button class="mr-2 preview-btn-class" *ngIf="stepper?.selectedIndex === stepper.steps.length - 1" (click)="preview()"
            [disabled]="!isCurrentStepValid()" [ngClass]="{
      'preview-button-off': !isCurrentStepValid(),
      'preview-button-on': isCurrentStepValid()
    }">
            <!-- <span>Preview</span> -->
            <span class="material-icons">
              visibility
              </span>
          </button>
          <button class="mr-2 preview-btn-class" (click)="addNotes()" [ngClass]="{
            'comment-button-off': !isCommentPresent,
            'comment-button-on': isCommentPresent
          }">
          <span class="material-icons">
            chat_bubble_outline
            </span>
        </button>
          <span *ngIf="stepper?.selectedIndex <= data.length - 1">
            <button mat-icon-button [matMenuTriggerFor]="infoMenu">
              <mat-icon class="invoice-info">help_outline</mat-icon>
            </button>
            <mat-menu #infoMenu class="my-class-info label-name mt-1 pl-2 pt-2" [yPosition]="'below'"
              [xPosition]="'before'">
              <div class="row pt-1 tax-data" style="font-weight: 500;">
                <div class="col-10 mt-0 pb-1 p-0" style="font-size: 15px;">Field Validation</div>
              </div>
              <hr class="p-0 pb-2 m-0">
              <!-- InValid items -->
              <div *ngFor="let item of invalidItems">
                <div class="item pb-1 mr-3">
                  <div class="circle-icon invalid">
                    <mat-icon style="color: white">close</mat-icon>
                  </div>
                  <span class="label-name">{{ item.label_name }}</span>
                </div>
              </div>
              <!-- Valid items -->
              <div *ngFor="let item of validItems">
                <div class="item pb-1 mr-3">
                  <div class="circle-icon valid">
                    <mat-icon style="color: white;">done</mat-icon>
                  </div>
                  <span class="label-name">{{ item.label_name }}</span>
                </div>
              </div>



            </mat-menu>
          </span>

        </div>
      </div>
    </div>

    <div class="row">
      <div class="col-12">
        <mat-horizontal-stepper linear #stepper class="mat-stepper pr-0">
          <div *ngFor="let item of data; let i = index">
            <mat-step [completed]="false">
              <ng-template matStepLabel>
                <span class="truncate-text" style="color:#526179; font-size: 13px;"  matTooltip = "{{ item.From_company_code }} To {{ item.To_company_code}}" matTooltipClass="my-tooltip-max-width">{{ item.From_company_code }} To {{ item.To_company_code}}</span>
              </ng-template>

              <div class="mw-100">
                <app-invoice-form-builder #formBuilder [legalEntityid]="item.legal_entity_id" [taxMaster]="taxMaster"
                [chargeTypes] = "chargeTypeMaster"
                  [groupTaxMaster]="groupTaxMaster" [projectCurrencyMaster]="projectCurrencyMaster" [prefilldata]="item"
                  (formValidityChange)="onFormValidityChange(i, $event)" [showPartnerMargin]="!isLastStep(i)"
                  [stepIndex]="i" [serviceTypeGroupId]="serviceTypeGroupId" [showWarmMsg]="showWarningFoTM"
                  [isTenantExistingInvoiceAllowed]="isTenantExistingInvoiceAllowed" [currencyDetails]="currencyDetails"
                  (onFormValueChanges)="onFormValueChanges($event.form, $event.stepIndex)"
                  (entityFieldData)="onEntityFieldDataChanges($event.entityConfig, $event.stepIndex)"
                  (itemFieldData)="onItemFieldDataChanges($event.itemConfig, $event.stepIndex)"
                  (fteFieldData)="onFteFieldDataChanges($event.fteConfig, $event.stepIndex)" [invoiceTenantDetails] = "invoiceTenantDetails" [restrictSavePoOnAlert]="restrictSavePoOnAlert"[poData]="poData" [formControlForPoAlert]="formControlForPoAlert" [dateFormats]="dateFormats"></app-invoice-form-builder>
              </div>

            </mat-step>
          </div>
          <mat-step *ngIf="showAdaptTemplate">
            <ng-template matStepLabel>
              <span style="color:#526179; font-size: 13px;">Adapt Template</span>
            </ng-template>
            <app-adapt-payment-template [customerId]="customerId" [usedIn]="0" (isAdaptClicked)="isAdaptClicked($event)"
              [formDetailsFromInvoice]="adaptData"></app-adapt-payment-template>
          </mat-step>
        </mat-horizontal-stepper>
      </div>

    </div>


  </ng-container>
  <ngx-spinner *ngIf="!showSaveSpinner" bdColor="rgb(245,245,245,0.6)" size="medium" color="#cf0001"
    type="ball-clip-rotate" [fullScreen]="true">
    <p style="color: #cf0001"> Getting Things Ready ... </p>
  </ngx-spinner>

  <ngx-spinner *ngIf="showSaveSpinner" bdColor="rgb(245,245,245,0.6)" size="medium" color="#cf0001"
    type="ball-clip-rotate" [fullScreen]="true" #saveSpinner>
    <p style="color:  #cf0001">Creating Invoice...</p>
  </ngx-spinner>
</div>