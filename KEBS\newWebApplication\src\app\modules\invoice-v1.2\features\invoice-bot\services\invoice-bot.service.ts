import { Injectable } from '@angular/core';
import { HttpClient } from "@angular/common/http";
import { NgxSpinnerService } from 'ngx-spinner';
import { BehaviorSubject } from 'rxjs';
@Injectable({
  providedIn: 'root'
})
export class InvoiceBotService {

  constructor(private $http: HttpClient,public spinnerService: NgxSpinnerService) {}
  getdropdownMaster(apiPath: any){
    return new Promise((resolve, reject) => {
      this.$http.post('/api/' + apiPath, {}).subscribe(
        res =>  {
          return resolve(res);
        },
        err => {
          return reject(err);
        }
      );
    });
  }

  getSubLineItemData(milestoneId) {
    return this.$http.post("api/pm/financial/getRPAProjectFTEDetails", {
      milestoneId: milestoneId
    });
  }

  getProductServiceDropdown(entityId, startIndex, offSet, searchVal) {
    return this.$http.post("api/invoice/v2/getProductServiceMaster", {
      entityId:entityId,
      startIndex: startIndex,
      offSet:offSet,
      searchText: searchVal
    });
  }

  private touchFormControlSubject = new BehaviorSubject<boolean>(false);
  touchFormControl$ = this.touchFormControlSubject.asObservable();

  setFlag(value: boolean) {
    this.touchFormControlSubject.next(value);
  }

  masterTmGeneralAndConsultantInfo(milestoneId) {
    return this.$http.post("api/invoice/v2/masterFteDetails", {
      milestoneId: milestoneId

    });
  }

  getQBCustomerMasterData(bookId) {
    return new Promise((resolve, reject) => {
      this.$http
        .post("/api/integration/qb/customers/list", {book_id: bookId})
        .subscribe(
          res => {
            return resolve(res);
          },
          (err) => {
            console.error(err);
            reject(err);
          }
        );
    });
  }

  getZBCustomerMasterData(bookId) {
    return new Promise((resolve, reject) => {
      this.$http
        .post("/api/integration/zb/customers/list", {book_id: bookId})
        .subscribe(
          res => {
            return resolve(res);
          },
          (err) => {
            console.error(err);
            reject(err);
          }
        );
    });
  }

  getQBCostCenterMasterData(bookId) {
    return new Promise((resolve, reject) => {
      this.$http
        .post("/api/integration/qb/costcentre/list", {book_id: bookId})
        .subscribe(
          res => {
            return resolve(res);
          },
          (err) => {
            console.error(err);
            reject(err);
          }
        );
    });
  }

  getZBCostCenterMasterData(bookId) {
    return new Promise((resolve, reject) => {
      this.$http
        .post("/api/integration/zb/costcentre/list", {book_id: bookId})
        .subscribe(
          res => {
            return resolve(res);
          },
          (err) => {
            console.error(err);
            reject(err);
          }
        );
    });
  }

  getInvoiceNumber(array, milestoneId, invoiceType) {
    return this.$http.post("api/invoice/v2/getInvoiceGenerationNumber", {
      legalEntityDetails: array,
      milestoneId: milestoneId,
      invoiceType: invoiceType
    });
  }

  updateZBCustomerInMappingTable(book_id, customer_id, value) {
    return new Promise((resolve, reject) => {
      this.$http
        .post("/api/integration/zb/customers/map/update", {book_id: book_id, kebs_internal_id: customer_id, external_id: value})
        .subscribe(
          res => {
            return resolve(res);
          },
          (err) => {
            console.error(err);
            reject(err);
          }
        );
    });
  }

  updateQBCustomerInMappingTable(book_id, customer_id, value) {
    return new Promise((resolve, reject) => {
      this.$http
        .post("/api/integration/qb/customers/map/update", {book_id: book_id, kebs_internal_id: customer_id, external_id: value})
        .subscribe(
          res => {
            return resolve(res);
          },
          (err) => {
            console.error(err);
            reject(err);
          }
        );
    });
  }

  updateZBCCInMappingTable(book_id, cc_id, value) {
    return new Promise((resolve, reject) => {
      this.$http
        .post("/api/integration/zb/costcenter/map/update", {book_id: book_id, kebs_internal_id: cc_id, external_id: value})
        .subscribe(
          res => {
            return resolve(res);
          },
          (err) => {
            console.error(err);
            reject(err);
          }
        );
    });
  }

  updateQBCCInMappingTable(book_id, cc_id, value) {
    return new Promise((resolve, reject) => {
      this.$http
        .post("/api/integration/qb/costcenter/map/update", {book_id: book_id, kebs_internal_id: cc_id, external_id: value})
        .subscribe(
          res => {
            return resolve(res);
          },
          (err) => {
            console.error(err);
            reject(err);
          }
        );
    });
  }
}
