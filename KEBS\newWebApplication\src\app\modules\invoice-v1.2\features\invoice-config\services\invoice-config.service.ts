import { Injectable } from '@angular/core';
import { HttpClient } from "@angular/common/http";
@Injectable({
  providedIn: 'root'
})
export class InvoiceConfigService {

  constructor(private $http: HttpClient) { }

  FormFieldConfig = (entity_id) => {
    return this.$http.post('/api/invoice/v2/invoiceFormConfig', {
      entity_id: entity_id
    });
  }

  updateadminFieldConfig =(params) => {
    return this.$http.post('/api/invoice/v2/UpdateAdminFormConfig', {
      params:params
    });
  }

  getLegalEntity = () =>{
    return this.$http.post("/api/invoice/v2/getLegalEntity",{});
  }

  getPDFSections = () => {
    return this.$http.post('/api/invoice/v2/getPDFSections', {
    });
  }

  getTaxMaster = (legal_entity_id : any) =>{
    return this.$http.post("/api/invoice/v2/getTaxMaster",{
      legal_entity_id: legal_entity_id
    });
  }

  getServiceType = () =>{
    return this.$http.post("/api/invoice/v2/getServiceType",{});
  }

  getTemplateConfig = (service_type_id) => {
    return this.$http.post('/api/invoice/v2/invoiceTemplateConfig', {
      service_type_id: service_type_id
    });
  }

  updateTemplateConfig =(params) => {
    return this.$http.post('/api/invoice/v2/updateTemplateConfig', {
      params:params
    });
  }

  getfteConfig = () => {
    return this.$http.post('/api/invoice/v2/invoicefteConfig', { });
  }

  updatefteConfig =(params) => {
    return this.$http.post('/api/invoice/v2/updatefteConfig', {
      params:params
    });
  }

  getTermsConditionsConfig = (legal_entity_id) => {
    return this.$http.post('/api/invoice/v2/getTermsConditionsConfig', {
      legal_entity_id: legal_entity_id
    });
  }

  getGroupTaxMaster = (legal_entity_id : any) =>{
    return this.$http.post("/api/invoice/v2/getGroupTaxMaster",{
      legal_entity_id: legal_entity_id
    });
  }

  getProjectCurrencyMaster = (project_id,item_id) => {
    return this.$http.post('/api/invoice/v2/getProjectCurrencyMaster', {
      projectId: project_id,
      itemId: item_id
    });
  }

  chargeTypeMaster = () =>{
    return this.$http.post("/api/invoice/v2/chargeTypeMaster",{
    });
  }

  importFieldConfig = (entity_id) => {
    return this.$http.post('/api/invoice/v2/importMasterConfiguration', {
      legalEntityId: entity_id
    });
  }



}
