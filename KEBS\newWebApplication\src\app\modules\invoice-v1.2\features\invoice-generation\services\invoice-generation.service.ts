import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { PdfViewerService } from "./pdf-viewer.service"
import * as _ from "underscore"
import { RolesService } from 'src/app/services/acl/roles.service';

@Injectable({
  providedIn: 'root'
})
export class InvoiceGenerationService {
  public skillRoleList: any = [];
  constructor(
    private http: HttpClient,
    private pdfService: PdfViewerService,
    private _roleService: RolesService
  ) { }

  getInvoiceInfoForStepper(pId, mId, sTypeId, itemId) {
    return this.http.post("api/invoice/getInvoiceInfo", {
      projectId: pId,
      milestoneId: mId,
      serviceTypeId: sTypeId,
      itemId: itemId
    });
  }
  //?getAvailableFromAddress - used in stepper - invoice creation
  getAvailableFromAddress = (lId) => {
    return this.http.post("/api/invoice/stepperAddressChange", {
      legalEntityId: lId,
      id: 1,
    });
  };

  convertCurrency(projectId, currencyId, value) {
    return this.http.post("api/invoice/callConvertCurrency", {
      projectId: projectId,
      currencyId: currencyId,
      value: value,
    });
  }
  getSkillRoleList() {
    return new Promise((resolve, reject) => {
      if (this.skillRoleList && this.skillRoleList.length > 0) {
        return resolve(this.skillRoleList);
      } else {
        this.http.post("/api/master/getMasterSkillRole", "").subscribe(
          res => {
            this.skillRoleList = _.map(res, skillRole => {
              skillRole.name =
                skillRole.skill_description + "-" + skillRole.role_description;
              skillRole.id = skillRole.skill_role_id;
              return skillRole;
            });
            return resolve(res);
          },
          err => {
            console.log(err);
            return reject(err);
          }
        );
      }
    });
  }

  //?getAvailableToAddress - used in stepper - invoice creation
  getAvailableToAddress = (customerId) => {
    return this.http.post("/api/invoice/stepperAddressChange", {
      customerId: customerId,
      id: 2,
    });
  };


  //getAvailableBankAddress - used in stepper - invoice creation
  getAvailableBankAddress = (legalEntityId) => {
    return this.http.post("/api/invoice/stepperAddressChange", {
      legalEntityId: legalEntityId,
      id: 3,
    });
  };

  getAnnexureDetails(
    milestoneId,
    projectId,
    projectName,
    information,
    serviceTypeId,
    annexureHeader,
    serviceTypeGroupId
  ) {
    return this.http.post("api/invoice/InvoiceAnnexure", {
      milestoneId: milestoneId,
      projectId: projectId,
      projectName: projectName,
      annexureDetails: information,
      serviceTypeId: serviceTypeId,
      serviceTypeGroupId: serviceTypeGroupId,
      annexureHeader: annexureHeader,
    });
  }
  //? get what are the banks available for a legal entity
  getAvailableBanks = (legalEntityId) => {
    return this.http.post("/api/invoice/bankCrudOperations", {
      legalEntityId: legalEntityId,
      crudId: 2,
    });
  };

  getInvoiceNumber(array, milestoneId, invoiceType) {
    return this.http.post("api/invoice/v2/getInvoiceGenerationNumber", {
      legalEntityDetails: array,
      milestoneId: milestoneId,
      invoiceType: invoiceType
    });
  }

  getStepperDetails(ProjectId, milestoneId) {
    return this.http.post("/api/invoice/getInvoiceStepperDetails", {
      projectId: ProjectId,
      milestoneId: milestoneId,
    });

  }
  getStepperDetailsV2(ProjectId, milestoneId) {
    return this.http.post("/api/invoice/v2/getInvoiceStepperDetailsV2", {
      projectId: ProjectId,
      milestoneId: milestoneId,
    });

  }

  sendFormData(data) {
    this.pdfService.setData(data);
  }
  setPaymentMilestoneActivity(paymentFormDetails, milestone_id, item_id) {
    return new Promise((resolve, reject) => {
      this.http
        .post("/api/itemPayment/setPaymentMilestoneActivity", {
          paymentFormDetails,
          milestone_id,
          item_id
        })
        .subscribe(
          res => {
            return resolve(res);
          },
          err => {
            console.log(err);
          }
        );
      // return resolve();
    });
  }

  getPaymentActivityList(methodologyId, customerId) {
    return new Promise((resolve, reject) => {
      this.http
        .post("/api/master/getPaymentActivityList", {
          methodologyId,
          customerId
        })
        .subscribe(
          res => {
            return resolve(res);
          },
          err => console.log(err)
        );
    });
  }


  currencyConversionCheck(data) {
    return new Promise((resolve, reject) => {
      this.http
      .post("api/invoice/currencyConversionCheck", {
        invoiceAttachmentFormDetails: data,     
      }).subscribe(
          res => {
            return resolve(res);
          },
          err =>{
            console.log(err);
            return reject(err);
          } 

        );
    });
  }

  currencyConversionCheckFunc(data) {
    return this.http.post("api/invoice/currencyConversionCheck", {
      invoiceAttachmentFormDetails: data,     
    });  
  }
  
  saveInvoiceStepperDetails(data,token) {
    return this.http.post("api/invoice/saveInvoiceDetails", {
      invoiceAttachmentFormDetails: data,
      microsoft_token: token
      
    }); 
  }


  getTranslatedData(data) {
    return new Promise((resolve, reject) => {
      this.http
        .post("/api/invoice/getTranslatedData", { text: data })
        .subscribe((res) => {
          console.log(res);
          return resolve(res);
        });
    });
  }

  /** 
 * @description get entity logo
 */
  getEntityLogo(fromCompanyCode) {
    return this.http.post("/api/invoice/getEntityLogo", {
      fromCompanyCode: fromCompanyCode,

    });
  }


  tmGeneralAndConsultantInfo(milestoneId) {
    return this.http.post("api/invoice/tmConsultantInfo", {
      milestoneId: milestoneId

    });
  }

  invoiceWorkLocationMaster() {
    return this.http.post("api/invoice/invoiceWorkLocationMaster", {

    });
  }

  getLabelForExpense = (application_id, id) => {
    console.log(this._roleService.label)
    let val = _.where(this._roleService.label, { application_id: application_id, id: id })
    return val.length > 0 ? val[0]['label_name'] : ""
  }

  getTenantExistingInvoiceConf(legalEntityData: any, milestoneId: any) {
    return this.http.post("api/invoice/getTenantInvoiceConf", { legalEntityData, milestoneId });
  }

  getTaxCodeDetails() {
    return new Promise((resolve, reject) => {
      this.http
        .post("/api/invoice/getTaxCodeDetails", {
        })
        .subscribe(
          res => {
            return resolve(res);
          },
          err => console.log(err)
        );
    });
  }

  getTCSDetails() {
    return new Promise((resolve, reject) => {
      this.http
        .post("/api/invoice/v2/getTCSMaster", {
        })
        .subscribe(
          res => {
            return resolve(res);
          },
          err => console.log(err)
        );
    });
  }

  getPlaceOfSupply() {
    return new Promise((resolve, reject) => {
      this.http
        .post("/api/invoice/getPlaceOfSupply", {
        })
        .subscribe(
          res => {
            return resolve(res);
          },
          err => console.log(err)
        );
    });
  }
  getInvoiceConfig() {
    return new Promise((resolve, reject) => {
      this.http
        .post("/api/invoice/getInvoiceConfigDetails", { })
        .subscribe((res) => {
          console.log(res);
          return resolve(res);
        });
    });
  }
  getCurrencyDetails() {
    return new Promise((resolve, reject) => {
      this.http
        .post("/api/invoice/getCurrencyDetails", { })
        .subscribe((res) => {
          console.log(res);
          return resolve(res['data']);
        });
    });
  }

  getLegalEntityQRConfig(legalEntityId) {
    return this.http.post("/api/invoice/v2/getLegalEntityQRConfig", {
      legalEntityId:legalEntityId
     
    });
  }

  saveInvoiceStepperDetailsAsDraft(data,token) {
    return this.http.post("api/invoice/v2/saveInvoiceDetailsAsDraft", {
      invoiceAttachmentFormDetails: data,
      microsoft_token: token
    });
  }

  getPoValues(itemId, milestoneId) {
    return this.http.post("/api/invoice/v2/getPoValues", {
      itemId: itemId,
      milestoneId: milestoneId,
    });
  }

  generateAndUploadPDFInS3 = (pdfData) => {
    return this.http.post("/api/invoice/v2/generateAndUploadPDFInS3", {
      pdfData: pdfData
    });
  };
  

  invoiceIntegrationSimulationAPI(data) {
    return new Promise((resolve, reject) => {
      this.http
        .post("/api/invoice/invoiceIntegrationSimulationAPI", {
          invoice_details: data
        })
        .subscribe(
          res => {
            return resolve(res);
          },
          err => console.log(err)
        );
    });
  }

  getCommentStatus(milestone_id) {
    return new Promise((resolve, reject) => {
      this.http
        .post("/api/invoice/v2/getCommentStatus", { milestoneId: milestone_id})
        .subscribe((res) => {
          console.log(res);
          return resolve(res);
        });
    });
  }

}
