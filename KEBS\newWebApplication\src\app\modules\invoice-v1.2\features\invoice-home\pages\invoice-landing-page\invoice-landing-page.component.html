<div class="container-fluid invoices-css">
    <div class="row pt-1">
      <div class="col-12 col-md-12 col-lg-12 pl-2 pr-1">
        <div class="row pt-0 pb-0">
          <div
            class="pl-1 pt-3  col-2 title "
            data-step="1"
            data-intro="Yet to be Billed , Billed, Partial Payment,
           Partial Received Invoices are di splayed in 
          this section "
            data-position="right"
          >
            Total Invoices :
            <span class="heading pl-2">{{
              smallCardsDetails ? (smallCardsDetails|filter:searchText).length : "-"
            }}</span>
          </div>
          <div class="col-10 pr-0">
            <div class="row">
              <div class="d-flex col col-md-7 search-bar">
                <mat-form-field appearance="outline" class="ml-auto">
                  <span matPrefix
                    ><mat-icon
                      style="font-size: 21px !important; color: #66615b !important"
                      >search</mat-icon
                    ></span
                  >
                  <input
                    matInput
                    type="search"
                    name="search"
                    placeholder="Search Invoice"
                    [(ngModel)]="searchText"
                    (ngModelOptions)="({ debounce: 200 })"
                  />
                  <mat-icon matSuffix>
                    <button
                      mat-button
                      *ngIf="searchText"
                      matSuffix
                      mat-icon-button
                      aria-label="Clear"
                      (click)="searchText = ''"
                      style="height: 30px; width: 30px; line-height: 1"
                    >
                      <mat-icon
                        matTooltip="Clear search"
                        style="font-size: 18px !important; color: #66615b !important"
                        >close</mat-icon
                      >
                    </button>
                  </mat-icon>
                </mat-form-field>
              </div>
  
              <div class="col-md-5 col " style="padding-top: 9px; padding-left: 0">
                <mat-button-toggle-group
                  #group="matButtonToggleGroup"
                  class="quad"
                  [value]="paymentCategory"
                >
                  <mat-button-toggle
                    aria-label="Text align left"
                    (click)="toggleClicked('YTB')"
                    value="ytb"
                    matTooltip="Yet to Bill"
                  >
                    YTB
                  </mat-button-toggle>
                  <mat-button-toggle
                    data-step="2"
                    data-intro="
                  click here to view the Billed Invoices"
                    data-position="right"
                    value="billed"
                    aria-label="Text align center"
                    (click)="toggleClicked('BILLED')"
                  >
                    Billed
                  </mat-button-toggle>
                  <mat-button-toggle
                    data-step="3"
                    data-intro="
                  click here to view the partially paid items"
                    data-position="left"
                    value="pp"
                    aria-label="Text align right"
                    matTooltip="Partial Payment"
                    (click)="toggleClicked('PP')"
                  >
                    PP
                  </mat-button-toggle>
                  <mat-button-toggle
                    data-step="4"
                    data-intro="
                  click here to view the received payments"
                    data-position="left"
                    value="pr"
                    aria-label="Text align justify"
                    matTooltip="Payment Received"
                    (click)="toggleClicked('PR')"
                  >
                    PR
                  </mat-button-toggle>
                </mat-button-toggle-group>
                <mat-button-toggle-group
                  #group="matButtonToggleGroup"
                  class="duo ml-2"
                  value="left"
                >
                  <mat-button-toggle
                    value="left"
                    aria-label="Text align left"
                    (click)="viewCard()"
                    matTooltip="Card view"
                  >
                    <i class="material-icons">
                      view_module
                    </i>
                  </mat-button-toggle>
                  <mat-button-toggle
                    value="center"
                    aria-label="Text align center"
                    (click)="viewList('listviewactivated')"
                    matTooltip="List view"
                  >
                    <i class="material-icons">
                      view_list
                    </i>
                  </mat-button-toggle>
                </mat-button-toggle-group>
                <mat-button-toggle-group class="ml-2">
                  <mat-button-toggle
                  *ngIf="_udrfService.udrfUiData.showUdrfModalButton"
                  (click)="openUdrfFilterModal()"
                  class="view-button-inactive-NonGhost"
                  matTooltip="Apply Filters And Sort"
                  [disabled]="_udrfService.udrfData.areSortAndFiltersLoading"
                >
                  <mat-icon
                    *ngIf="!_udrfService.udrfData.areSortAndFiltersLoading"
                    class="iconButton"
                  >
                    filter_list
                  </mat-icon>
                  <span class="spinner-container" *ngIf="_udrfService.udrfData.areSortAndFiltersLoading">
                  <mat-spinner
                    diameter="22"
                  ></mat-spinner></span>
                </mat-button-toggle>
                </mat-button-toggle-group>
              </div>
            </div>
          </div>
        </div>
        <div class="filter mx-5 mt-1" [@slideInOut]="filterVisible ? 'in' : 'out'">   
          <div class="row pb-3 scrolling-wrapper-flexbox">
            <div class="col-4">
              <div class="row">
                <mat-selection-list
                  #pnllist
                  [(ngModel)]="pnlOptions"
                  (ngModelChange)="onFilterChange($event, 'pnlSelected')"
                >
                  <h2 mat-subheader class="p-0 mb-0">P & L</h2>
                  <mat-list-option
                    checkboxPosition="before"
                    *ngFor="let item of pnlNames"
                    [value]="item"
                  >
                    {{ item ? item : '-'}}
                  </mat-list-option>
                </mat-selection-list>
              </div>
            </div>
            <div class="col-4">
              <div class="row d-flex">
                <mat-selection-list
                  #entityList
                  [(ngModel)]="entityOptions"
                  (ngModelChange)="onFilterChange($event, 'entitySelected')"
                >
                  <h2 mat-subheader class="p-0 mb-0">Entity</h2>
                  <mat-list-option
                    checkboxPosition="before"
                    *ngFor="let item of entityNames"
                    [value]="item"
                  >
                    {{ item }}
                  </mat-list-option>
                </mat-selection-list>
              </div>
            </div>
            <div class="col-3">
              <div class="row d-flex">
                <mat-selection-list
                  #serviceTypeList
                  [(ngModel)]="serviceTypeOptions"
                  (ngModelChange)="onFilterChange($event, 'serviceSelected')"
                >
                  <h2 mat-subheader class="p-0 mb-1">Service Type</h2>
                  <mat-list-option
                    checkboxPosition="before"
                    *ngFor="let item of serviceTypeNames"
                    [value]="item"
                  >
                    {{ item }}
                  </mat-list-option>
                </mat-selection-list>
              </div>
            </div>
            <div class="col-3">
              <div class="row d-flex">
                <span
                  style="font-size: 14px;color:rgba(0, 0, 0, 0.54);font-weight: 500;font-family: Roboto, 'Helvetica Neue', sans-serif;"
                  >Sort By</span
                >
                <div class="col-12">
                  <div class="row">
                    <button mat-icon-button
                    (click) = "sortDataByDate();">
                      <mat-icon style="font-size: 17px;" *ngIf="sortedByDate">arrow_upward</mat-icon>
                      <mat-icon style="font-size: 17px;" *ngIf="!sortedByDate">arrow_downward</mat-icon>
                    </button>
                    <span
                      style="padding-left: 2px;
                  padding-top: 8px;
                  font-size: 14px;"
                      >Date</span>
                  </div>
                </div>
              </div>
              <div class="row pt-1" *ngIf="curentlyActiveScreen == 'billed' || curentlyActiveScreen == 'partialPayment'">
                <span
                  style="font-size: 14px;color:rgba(0, 0, 0, 0.54);font-weight: 500;font-family: Roboto, 'Helvetica Neue', sans-serif;"
                  >Sort By Planned On</span>
                <div class="col-12">
                  <div class="row">
                    <button mat-icon-button
                    (click) = "sortDataByPlannedOn();">
                      <mat-icon style="font-size: 17px;" *ngIf="sortedByPlannedOnDate">arrow_upward</mat-icon>
                      <mat-icon style="font-size: 17px;" *ngIf="!sortedByPlannedOnDate">arrow_downward</mat-icon>
                    </button>
                    <span
                      style="padding-left: 2px;
                  padding-top: 8px;
                  font-size: 14px;"
                      >Date</span>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-3">
             
            <div class="row pt-1 d-flex">
              <span class="pb-2" style="font-size: 14px;color:rgba(0, 0, 0, 0.54);font-weight: 500;font-family: Roboto, 'Helvetica Neue', sans-serif;">Filter by Date</span>
              <div class="col-12 p-0">
               <mat-form-field appearance="outline" class="create-account-field" style="height: 34px">
                 <input [(ngModel)]="filterDate" matInput [matDatepicker]="picker" placeholder="Choose a date"
                 (dateInput)="FilterByDateEvent('input', $event)" (dateChange)="FilterByDateEvent('change', $event)"
                 >
                 <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
                 <mat-datepicker #picker></mat-datepicker>
               </mat-form-field>
              </div>
            </div>

              <!-- Planned On Date Range for Billed Alone Begins ! -->
              <div class="row ml-0 pt-4" *ngIf="curentlyActiveScreen == 'billed' || curentlyActiveScreen == 'partialPayment'">
                <div class="pl-0 col-10">
                  <span
                  style="font-size: 14px;color:rgba(0, 0, 0, 0.54);font-weight: 500;font-family: Roboto, 'Helvetica Neue', sans-serif;"
                  >Custom Date Range</span> 
                </div>
                 <div class="pl-0 col-2">
                  <span class = "ml-0">
                    <button mat-icon-button class="ml-auto trend-button-inactive" (click)="resetDateRange()">
                    <mat-icon class="iconButton">clear</mat-icon>
                  </button>
                </span> 
                 </div>
              </div>
             
              <div class="row pt-2" *ngIf="curentlyActiveScreen == 'billed' || curentlyActiveScreen == 'partialPayment'">
                <mat-form-field appearance="outline" class="create-account-field" style="height: 34px">
                  <mat-label>Enter a date range</mat-label>
                  <mat-date-range-input [formGroup]="range" [rangePicker]="picker1">
                    <input matStartDate formControlName="start" placeholder="Start date">
                    <input matEndDate formControlName="end" placeholder="End date">
                  </mat-date-range-input>
                  <mat-datepicker-toggle matSuffix [for]="picker1"></mat-datepicker-toggle>
                  <mat-date-range-picker #picker1></mat-date-range-picker>
                </mat-form-field>
            </div>
             
             <!-- Planned On Date Range for Billed Alone Ends ! -->
            </div>
          </div>
        </div>
        <div *ngIf="cardviewactivated == 1">
          <div class="row mh-100 pl-1">
            <div
              class=" col-lg-3 col-md-12 col-12 pl-0 pb-3"
              *ngFor="
                let item of smallCardsDetails
                  | filter: searchText
                  | paginate: { itemsPerPage: 16, currentPage: p }
              "
            >
              <div
                class="card card-inv"
                [ngStyle]="{
                  'border-left-color': getColor(curentlyActiveScreen)
                }"
                style="width: 18rem !important;  border-left-width: 3px !important"
              >
                <div class="card-body smallCardBody" id="thumbnail">
                  <div
                    class="row"
                    (click)="
                      tinyCardClick(
                        item.project_id,
                        item.id,
                        item.item_id,
                        item.milestone_name,
                        item.project_name,
                        item
                      )
                    "
                    style="padding-bottom: 1px"
                  >
                    <div class="col-4 Card-title">Milestone Name</div>
                    <div
                      class="col-8 card-content"
                      style="color:#cf0001; font-weight: 500"
                      [matTooltip]="
                        getLength(item.milestone_name) > 24
                          ? item.milestone_name
                          : ''
                      "
                    >
                      {{ item.milestone_name }}
                    </div>
                  </div>
                  <div
                    class="row"
                    (click)="
                      tinyCardClick(
                        item.project_id,
                        item.id,
                        item.item_id,
                        item.milestone_name,
                        item.project_name,
                        item
                      )
                    "
                    style="padding-bottom: 1px"
                  >
                    <div class="col-4 Card-title">In Item</div>
                    <div
                      class="col-8 card-content"
                      [matTooltip]="
                        getLength(item.item_name) > 24 ? item.item_name : ''
                      "
                    >
                      {{ item.item_name }}
                    </div>
                  </div>
                  <div
                    class="row"
                    (click)="
                      tinyCardClick(
                        item.project_id,
                        item.id,
                        item.item_id,
                        item.milestone_name,
                        item.project_name,
                        item
                      )
                    "
                    style="padding-bottom: 1px"
                  >
                    <div class="col-4 Card-title">
                      <span>{{ getValueInCard() }}</span>
                    </div>
                    <div class="col-8 card-content">
                      <app-currency
                        [currencyList]="item.invoice_value"
                        class=""
                        [type]="showFullValue ? 'full-value' : 'small'"
                      ></app-currency>
                    </div>
                  </div>
                
                  <!-- Small Card Details Changes for Billed Begins !  -->
                  <div
                    *ngIf="curentlyActiveScreen == 'billed'"
                    class="row"
                    (click)="
                      tinyCardClick(
                        item.project_id,
                        item.id,
                        item.item_id,
                        item.milestone_name,
                        item.project_name,
                        item
                      )
                    "
                    style="padding-bottom: 3px"
                  >
                    <div class="col-4 Card-title">Raised On</div>
                    <div class="col-8 card-content">
                      {{ changeDate(item.f_date) }} 
                    </div>
                  </div>
                  <div
                    *ngIf="curentlyActiveScreen == 'billed'"
                    class="row"
                    (click)="
                      tinyCardClick(
                        item.project_id,
                        item.id,
                        item.item_id,
                        item.milestone_name,
                        item.project_name,
                        item
                      )
                    "
                    style="padding-bottom: 3px"
                    [matTooltip]="getLength(item.end_customer_invoice_no) > 20
                    ? item.end_customer_invoice_no
                    : ''
                "
                  >
                    <div class="col-4 Card-title">Invoice No</div>
                    <div class="col-8 card-content">
                      {{ item.end_customer_invoice_no
                      }}
                    </div>
                  </div>
                 <!-- Small Card Details Changes for Billed Ends !  -->
                  
                  <!-- Small Card Details Changes for YTB Or Payment Received Begins !  -->
                  <div *ngIf="curentlyActiveScreen == 'YTB' || curentlyActiveScreen  == 'PaymentReceived'"
                    class="row"
                    (click)="
                      tinyCardClick(
                        item.project_id,
                        item.id,
                        item.item_id,
                        item.milestone_name,
                        item.project_name,
                        item
                      )
                    "
                    style="padding-bottom: 3px"
                  >
                    <div class="col-4 Card-title">{{ headerName }}</div>
                    <div class="col-8 card-content">
                      {{ changeDate(item.filterDate|localTime) }}
                    </div>
                  </div>
                 <!-- Small Card Details Changes for YTB Ends !  -->


                 <!-- Small Card Details Changes for Partial Payment Begins !  -->
                 <div *ngIf="curentlyActiveScreen == 'partialPayment'"
                 class="row"
                 (click)="
                   tinyCardClick(
                     item.project_id,
                     item.id,
                     item.item_id,
                     item.milestone_name,
                     item.project_name,
                     item
                   )
                 "
                 style="padding-bottom: 1px"
               >
                 <div class="col-4 Card-title">
                   <span>Balance Due</span>
                 </div>
                 <div class="col-8 card-content">
                   <app-currency
                     [currencyList]="item.amount_to_be_collected"
                     class=""
                     [type]="showFullValue ? 'full-value' : 'small'"
                   ></app-currency>
                 </div>
               </div>
                  <div *ngIf="curentlyActiveScreen == 'partialPayment'"
                  class="row"
                  (click)="
                    tinyCardClick(
                      item.project_id,
                      item.id,
                      item.item_id,
                      item.milestone_name,
                      item.project_name,
                      item
                    )
                  "
                  style="padding-bottom: 3px"
                >
                  <div class="col-4 Card-title">Raised On</div>
                  <div class="col-8 card-content">
                    {{ changeDate(item.invoice_raised_on) }}
                  </div>
                </div>
                <div *ngIf="curentlyActiveScreen == 'partialPayment' || curentlyActiveScreen == 'PaymentReceived'"
                class="row"
                (click)="
                  tinyCardClick(
                    item.project_id,
                    item.id,
                    item.item_id,
                    item.milestone_name,
                    item.project_name,
                    item
                  )
                "
                style="padding-bottom: 3px"
                [matTooltip]="
                getLength(item.end_customer_invoice_no) > 20
                  ? item.end_customer_invoice_no
                  : ''
              "
              >
                <div class="col-4 Card-title">Invoice No</div>
                <div class="col-8 card-content">
                  {{item.end_customer_invoice_no}}
                </div>
              </div>
               <!-- Small Card Details Changes for Partial Payment Ends !  -->

                  <div class="row border-top solid smallCardBottom">
                    <div class="col-4 cost-center" [matTooltip]="">
                      #{{ item.profit_center }}
                    </div>
                    <div class="col-8 d-flex">
                      <!-- <button
                        mat-icon-button
                        matTooltip="Print"
                        class="icon-tray-button"
                        *ngIf="printRestriction == false"
                      >
                        <mat-icon class="smallCardIcon">local_printshop</mat-icon>
                      </button> -->
  
                      <!-- <button
                        mat-icon-button
                        matTooltip="Download"
                        class="icon-tray-button"
                        *ngIf="getAppRestriction == false"
                      >
                        <mat-icon class="smallCardIcon">get_app</mat-icon>
                      </button> -->
                      <div class="ml-auto"></div>
                      <button
                        mat-icon-button
                        matTooltip="Undo"
                        class="icon-tray-button "
                        *ngIf="undoRestriction == false && roleCheck == true"
                        (click)="undoInvoice(item.id, item.billing_id)"
                      >
                        <mat-icon class="smallCardIcon">undo</mat-icon>
                      </button>
                      <button
                        mat-icon-button
                        matTooltip="Payment Reversal"
                        class="icon-tray-button "
                        *ngIf="(curentlyActiveScreen == 'partialPayment' || curentlyActiveScreen == 'PaymentReceived' ) && roleCheck == true"
                        (click)="paymentReversal(item.id)"
                      >
                        <mat-icon class="smallCardIcon">undo</mat-icon>
                      </button>
                      <button
                        mat-icon-button
                        matTooltip="View Invoice"
                        class="icon-tray-button"
                        *ngIf="receiptRestriction == false"
                        (click)="viewInvoice(item.billing_id, item.id)"
                      >
                        <mat-icon class="smallCardIcon">receipt</mat-icon>
                      </button>
                    
  
                      <button
                        mat-icon-button
                        matTooltip="View Annexure"
                        class="icon-tray-button"
                        *ngIf="descRestriction == false"
                        (click)="
                          viewAnnexure(item.billing_id, item.service_type_group_id)
                        "
                      >
                        <mat-icon class="smallCardIcon">description</mat-icon>
                      </button>

                    <button mat-icon-button matTooltip="Send Mail" class="icon-tray-button"
                    *ngIf="(curentlyActiveScreen == 'billed' )"
                    (click)="sendMail(item.billing_id,item.id,item.gantt_id)">
                      <div class="sent-icon">
                        <mat-icon class="mailCardIcon">send</mat-icon>
                        <mat-icon class="checkmark" *ngIf="item?.mail_sent_counter > 0 && item?.mail_sent_status == 1">check_circle</mat-icon>
                      </div>
                    </button>
                    <button mat-icon-button matTooltip="Mark as Sent" class="icon-tray-button"
                    *ngIf="(curentlyActiveScreen == 'billed' )"
                    (click)="markAsSent(item.billing_id)">
                      <div class="sent-icon">
                        <mat-icon class="mailCardIcon" [ngClass]="{'green-icon': item?.mail_sent_status == 2}">mark_email_read</mat-icon>
                      </div>
                    </button>
                    
                      <button
                      mat-icon-button
                      matTooltip="Change date"
                      class="icon-tray-button"
                      *ngIf="undoRestriction == false && showChangeDate == true"
                     (click) = "viewActivities(item.id, item.milestone_name, item.item_name,item.planned_on)"
                    >
                      <mat-icon class="smallCardIcon">toc</mat-icon>
                    </button>
  
                      <button
                        mat-icon-button
                        matTooltip="Go to project"
                        class="icon-tray-button"
                      >
                        <mat-icon
                          class="smallCardIcon"
                          (click)="
                            launchProjectFromInvoice(
                              item.project_id,
                              item.project_name,
                              item.item_id,
                              item.item_name
                            )
                          "
                          >launch</mat-icon
                        >
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="row mh-100 pl-1" *ngIf="smallCardsDetails.length == 0">
            <div
              class=" col-lg-3 col-md-12 col-12 pl-0 pb-3"
              *ngFor="let item of shimmArr"
            >
              <div
                class="card card-inv"
                [ngStyle]="{
                  'border-left-color': getColor(curentlyActiveScreen)
                }"
                style="width: 18rem !important;  border-left-width: 3px !important"
              >
                <div class="card-body smallCardBody" id="thumbnail">
                  <div class="row" style="padding-bottom: 1px">
                    <div class="col-4 Card-title">
                      <ngx-content-loading
                        [speed]="'1500ms'"
                        [width]="1000"
                        [height]="100"
                        [primaryColor]="'#ebebeb'"
                        [secondaryColor]="'#f7f7f7'"
                      >
                        <svg:g
                          ngx-rect
                          width="800"
                          height="70"
                          y="50"
                          x="0"
                          rx="5"
                          ry="5"
                        ></svg:g>
                      </ngx-content-loading>
                    </div>
                    <div
                      class="col-8 card-content"
                      style="color:#cf0001; font-weight: 500"
                    >
                      <ngx-content-loading
                        [speed]="'1500ms'"
                        [width]="1000"
                        [height]="100"
                        [primaryColor]="'#ebebeb'"
                        [secondaryColor]="'#f7f7f7'"
                      >
                        <svg:g
                          ngx-rect
                          width="1000"
                          height="70"
                          y="50"
                          x="0"
                          rx="5"
                          ry="5"
                        ></svg:g>
                      </ngx-content-loading>
                    </div>
                  </div>
                  <div class="row" style="padding-bottom: 1px">
                    <div class="col-4 Card-title">
                      <ngx-content-loading
                        [speed]="'1500ms'"
                        [width]="1000"
                        [height]="100"
                        [primaryColor]="'#ebebeb'"
                        [secondaryColor]="'#f7f7f7'"
                      >
                        <svg:g
                          ngx-rect
                          width="800"
                          height="70"
                          y="50"
                          x="0"
                          rx="5"
                          ry="5"
                        ></svg:g>
                      </ngx-content-loading>
                    </div>
                    <div class="col-8 card-content">
                      <ngx-content-loading
                        [speed]="'1500ms'"
                        [width]="1000"
                        [height]="100"
                        [primaryColor]="'#ebebeb'"
                        [secondaryColor]="'#f7f7f7'"
                      >
                        <svg:g
                          ngx-rect
                          width="1000"
                          height="70"
                          y="50"
                          x="0"
                          rx="5"
                          ry="5"
                        ></svg:g>
                      </ngx-content-loading>
                    </div>
                  </div>
                  <div class="row" style="padding-bottom: 1px">
                    <div class="col-4 Card-title">
                      <span>
                        <ngx-content-loading
                          [speed]="'1500ms'"
                          [width]="1000"
                          [height]="100"
                          [primaryColor]="'#ebebeb'"
                          [secondaryColor]="'#f7f7f7'"
                        >
                          <svg:g
                            ngx-rect
                            width="800"
                            height="70"
                            y="50"
                            x="0"
                            rx="5"
                            ry="5"
                          ></svg:g>
                        </ngx-content-loading>
                      </span>
                    </div>
                    <div class="col-8 card-content">
                      <ngx-content-loading
                        [speed]="'1500ms'"
                        [width]="1000"
                        [height]="100"
                        [primaryColor]="'#ebebeb'"
                        [secondaryColor]="'#f7f7f7'"
                      >
                        <svg:g
                          ngx-rect
                          width="1000"
                          height="70"
                          y="50"
                          x="0"
                          rx="5"
                          ry="5"
                        ></svg:g>
                      </ngx-content-loading>
                    </div>
                  </div>
                  <div class="row" style="padding-bottom: 3px">
                    <div class="col-4 Card-title">
                      <ngx-content-loading
                        [speed]="'1500ms'"
                        [width]="1000"
                        [height]="100"
                        [primaryColor]="'#ebebeb'"
                        [secondaryColor]="'#f7f7f7'"
                      >
                        <svg:g
                          ngx-rect
                          width="800"
                          height="70"
                          y="50"
                          x="0"
                          rx="5"
                          ry="5"
                        ></svg:g>
                      </ngx-content-loading>
                    </div>
                    <div class="col-8 card-content">
                      <ngx-content-loading
                        [speed]="'1500ms'"
                        [width]="1000"
                        [height]="100"
                        [primaryColor]="'#ebebeb'"
                        [secondaryColor]="'#f7f7f7'"
                      >
                        <svg:g
                          ngx-rect
                          width="1000"
                          height="70"
                          y="50"
                          x="0"
                          rx="5"
                          ry="5"
                        ></svg:g>
                      </ngx-content-loading>
                    </div>
                  </div>
                  <div class="row border-top solid smallCardBottom">
                    <div class="col-4 cost-center">
                      <ngx-content-loading
                        [speed]="'1500ms'"
                        [width]="1000"
                        [height]="100"
                        [primaryColor]="'#ebebeb'"
                        [secondaryColor]="'#f7f7f7'"
                      >
                        <svg:g
                          ngx-rect
                          width="1000"
                          height="70"
                          y="50"
                          x="0"
                          rx="5"
                          ry="5"
                        ></svg:g>
                      </ngx-content-loading>
                    </div>
                    <div class="col-8 d-flex">
                      <div class="ml-auto"></div>
                      <ngx-content-loading
                        [speed]="'1500ms'"
                        [width]="1000"
                        [height]="100"
                        [primaryColor]="'#ebebeb'"
                        [secondaryColor]="'#f7f7f7'"
                      >
                        <svg:g
                          ngx-rect
                          width="1000"
                          height="70"
                          y="50"
                          x="0"
                          rx="5"
                          ry="5"
                        ></svg:g>
                      </ngx-content-loading>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div>
            <pagination-controls
              *ngIf="smallCardsDetails.length != 0"
              (pageChange)="p = $event"
              class="invoicePagination"
            ></pagination-controls>
          </div>
        </div>
        <div *ngIf="smallCardsDetails.length == 0 && shimmArr.length == 0">
          <div>
            <h4 class=" d-flex justify-content-center align-items-center mt-5">
              {{ isDataAvailable }}
            </h4>
          </div>
          <div class="d-flex justify-content-center align-items-center">
            <img
              src="assets\images\nomilestone.png"
              class="mt-4"
              height="350"
              width="400"
            />
          </div>
        </div>
  
        <!--Invoice List View Content from here-->
        <div *ngIf="viewlistactivated != 0">
          <div *ngIf="smallCardsDetails.length != 0">
            <div
              class="row card-header-row pl-0 pb-1"
              *ngIf="smallCardsDetails.length != 0"
            >
              <div class="col-1">Cost Center</div>
              <div class="col-3 d-flex">
                <span class="pl-2"> Milestone Name</span>
              </div>
              <div class="col-2 d-flex">
                <span class="pl-2">Project Name </span>
              </div>
              <div class="pr-2 col-1 d-flex pl-1">
                <span>{{ getValueInCard() }}</span>
              </div>
              <div class="pr-2 col-1 d-flex pl-1" *ngIf="curentlyActiveScreen == 'partialPayment'">
                <span>Balance Due</span>
              </div>
              <div class="col-1x pl-2 d-flex">
                <span>{{ headerName }}</span>
              </div>
              <div
                class="col-2x pl-0 d-flex"
                *ngIf="curentlyActiveScreen == 'billed' || curentlyActiveScreen  == 'partialPayment' || curentlyActiveScreen  == 'PaymentReceived'">
                <span>Invoice No </span>
              </div>
            </div>
            <div
              *ngFor="
                let item of smallCardsDetails
                  | filter: searchText
                  | paginate: { itemsPerPage: 15, currentPage: p }
              "
              class=" row "
            >
              <div class="col-12 pl-0">
                <div
                  class="card listcard"
                  [ngStyle]="{
                    'border-left-color': getColor(curentlyActiveScreen)
                  }"
                  style=" border-left-width:3px;"
                >
                  <div class="card-body" style="padding: 2px !important;">
                    <div class="row card-details p-0">
                      <div class="col-1 cost-center pl-2 mt-1">
                        #{{ item.profit_center }}
                      </div>
                      <div
                        class="col-3 mt-1 card-content"
                        style="color:#cf0001; font-weight: 500"
                        (click)="
                          tinyCardClick(
                            item.project_id,
                            item.id,
                            item.item_id,
                            item.milestone_name,
                            item.project_name,
                            item
                          )
                        "
                        [matTooltip]="
                          getLength(item.milestone_name) > 24
                            ? item.milestone_name
                            : ''
                        "
                      >
                        {{ item.milestone_name }}
                      </div>
                      <div
                        class="col-2 mt-1 card-content"
                        (click)="
                          tinyCardClick(
                            item.project_id,
                            item.id,
                            item.item_id,
                            item.milestone_name,
                            item.project_name,
                            item
                          )
                        "
                        [matTooltip]="
                          getLength(item.project_name) > 24
                            ? item.project_name
                            : ''
                        "
                      >
                        {{ item.project_name }}
                      </div>
                      <div
                        class="col-1 mt-1 card-content"
                        (click)="
                          tinyCardClick(
                            item.project_id,
                            item.id,
                            item.item_id,
                            item.milestone_name,
                            item.project_name,
                            item
                          )
                        "
                      >
                        <app-currency
                          [currencyList]="item.invoice_value"
                          class=""
                          type="small"
                        ></app-currency>
                      </div>
                      <div *ngIf="curentlyActiveScreen == 'partialPayment'"
                      class="col-1 mt-1 card-content"
                      (click)="
                        tinyCardClick(
                          item.project_id,
                          item.id,
                          item.item_id,
                          item.milestone_name,
                          item.project_name,
                          item
                        )
                      "
                    >
                      <app-currency
                        [currencyList]="item.amount_to_be_collected"
                        class=""
                        type="small"
                      ></app-currency>
                    </div>
                      <div
                        class="col-1x  pl-3 mt-1 card-content"
                        (click)="
                          tinyCardClick(
                            item.project_id,
                            item.id,
                            item.item_id,
                            item.milestone_name,
                            item.project_name,
                            item
                          )
                        "
                      >

                      <span *ngIf="curentlyActiveScreen == 'YTB' || curentlyActiveScreen  == 'PaymentReceived'">  {{ changeDate(item.filterDate|localTime) }}</span>
                    <span *ngIf="curentlyActiveScreen == 'partialPayment'">  {{ changeDate(item.invoice_raised_on) }}</span>
                  <span *ngIf="curentlyActiveScreen == 'billed'">  {{ changeDate(item.f_date) }}  </span>  
                  </div>
                      
                      <div class="col-2x mt-1 d-flex  card-content">
                        <span *ngIf="curentlyActiveScreen == 'billed' || curentlyActiveScreen  == 'partialPayment' || curentlyActiveScreen  == 'PaymentReceived' ">
                          {{item?.end_customer_invoice_no}}</span>
                        <div class="ml-auto">
                          <button
                          mat-icon-button
                          matTooltip="Undo"
                          class="icon-tray-button "
                          *ngIf="undoRestriction == false && roleCheck == true"
                          (click)="undoInvoice(item.id, item.billing_id)"
                        >
                          <mat-icon class="smallCardIcon">undo</mat-icon>
                        </button>
                        <button
                          mat-icon-button
                          matTooltip="Payment Reversal"
                          class="icon-tray-button "
                          *ngIf="(curentlyActiveScreen == 'partialPayment' || curentlyActiveScreen == 'PaymentReceived' ) && roleCheck == true"
                          (click)="paymentReversal(item.id)"
                        >
                          <mat-icon class="smallCardIcon">undo</mat-icon>
                        </button>
                        <button
                          mat-icon-button
                          matTooltip="View Invoice"
                          class="icon-tray-button"
                          *ngIf="receiptRestriction == false"
                          (click)="viewInvoice(item.billing_id, item.id)"
                        >
                          <mat-icon class="smallCardIcon">receipt</mat-icon>
                        </button>
                      
    
                        <button
                          mat-icon-button
                          matTooltip="View Annexure"
                          class="icon-tray-button"
                          *ngIf="descRestriction == false"
                          (click)="
                            viewAnnexure(item.billing_id, item.service_type_group_id)
                          "
                        >
                          <mat-icon class="smallCardIcon">description</mat-icon>
                        </button>

                        <button mat-icon-button matTooltip="Send Mail" class="icon-tray-button"
                        *ngIf="(curentlyActiveScreen == 'billed' )"
                        (click)="sendMail(item.billing_id,item.id,item.gantt_id)">
                          <div class="sent-icon">
                            <mat-icon class="mailCardIcon">send</mat-icon>
                            <mat-icon class="checkmark" *ngIf="item?.mail_sent_counter > 0 && item?.mail_sent_status == 1">check_circle</mat-icon>
                          </div>
                        </button>
                        <button mat-icon-button matTooltip="Mark as Sent" class="icon-tray-button"
                        *ngIf="(curentlyActiveScreen == 'billed' )"
                        (click)="markAsSent(item.billing_id)">
                          <div class="sent-icon">
                            <mat-icon class="mailCardIcon" [ngClass]="{'green-icon': item?.mail_sent_status == 2}">mark_email_read</mat-icon>
                          </div>
                        </button>
                      
                        <button
                        mat-icon-button
                        matTooltip="Change date"
                        class="icon-tray-button"
                        *ngIf="undoRestriction == false && showChangeDate == true"
                       (click) = "viewActivities(item.id, item.milestone_name, item.item_name,item.planned_on)"
                      >
                        <mat-icon class="smallCardIcon">toc</mat-icon>
                      </button>
    
                        <button
                          mat-icon-button
                          matTooltip="Go to project"
                          class="icon-tray-button"
                        >
                          <mat-icon
                            class="smallCardIcon"
                            (click)="
                              launchProjectFromInvoice(
                                item.project_id,
                                item.project_name,
                                item.item_id,
                                item.item_name
                              )
                            "
                            >launch</mat-icon
                          >
                        </button>
  
                        </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
            <div class="pt-2">
              <pagination-controls
                *ngIf="smallCardsDetails.length != 0"
                (pageChange)="p = $event"
                class="invoicePagination"
              ></pagination-controls>
            </div>
          
          <div *ngIf="smallCardsDetails.length == 0">
            <div
              class="row card-header-row pl-0 pb-1"
              *ngIf="smallCardsDetails.length == 0"
            >
              <div class="col-1"></div>
              <div class="col-3 d-flex">
                <span class="pl-2"> Milestone Name</span>
              </div>
              <div class="col-3 d-flex">
                <span class="pl-2">Project Name </span>
              </div>
              <div class="pr-2 col-1 d-flex pl-1">
                <span> Value </span>
              </div>
              <div class="col-1x pl-2 d-flex">
                <span>{{ headerName }}</span>
              </div>
              <div
                class="col-2x pl-0 d-flex"
                *ngIf="curentlyActiveScreen == 'billed'"
              >
                <span>Created by </span>
              </div>
            </div>
            <div
              *ngFor="
                let item of shimmArr
                  | filter: searchText
                  | paginate: { itemsPerPage: 15, currentPage: p }
              "
              class=" row "
            >
              <div class="col-12 pl-0">
                <div
                  class="card listcard"
                  [ngStyle]="{
                    'border-left-color': getColor(curentlyActiveScreen)
                  }"
                  style=" border-left-width:3px;"
                >
                  <div class="card-body" style="padding: 2px !important;">
                    <div class="row card-details p-0">
                      <div class="col-1 cost-center pl-2 mt-1">
                        <ngx-content-loading
                          [speed]="'1500ms'"
                          [width]="1000"
                          [height]="100"
                          [primaryColor]="'#ebebeb'"
                          [secondaryColor]="'#f7f7f7'"
                        >
                          <svg:g
                            ngx-rect
                            width="1000"
                            height="70"
                            y="50"
                            x="0"
                            rx="5"
                            ry="5"
                          ></svg:g>
                        </ngx-content-loading>
                      </div>
                      <div
                        class="col-3 mt-1 card-content"
                        style="color:#cf0001; font-weight: 500"
                      >
                        <ngx-content-loading
                          [speed]="'1500ms'"
                          [width]="1000"
                          [height]="100"
                          [primaryColor]="'#ebebeb'"
                          [secondaryColor]="'#f7f7f7'"
                        >
                          <svg:g
                            ngx-rect
                            width="1000"
                            height="70"
                            y="50"
                            x="0"
                            rx="5"
                            ry="5"
                          ></svg:g>
                        </ngx-content-loading>
                      </div>
                      <div class="col-3 mt-1 card-content">
                        <ngx-content-loading
                          [speed]="'1500ms'"
                          [width]="1000"
                          [height]="100"
                          [primaryColor]="'#ebebeb'"
                          [secondaryColor]="'#f7f7f7'"
                        >
                          <svg:g
                            ngx-rect
                            width="1000"
                            height="70"
                            y="50"
                            x="0"
                            rx="5"
                            ry="5"
                          ></svg:g>
                        </ngx-content-loading>
                      </div>
                      <div class="col-1 mt-1 card-content">
                        <ngx-content-loading
                          [speed]="'1500ms'"
                          [width]="1000"
                          [height]="100"
                          [primaryColor]="'#ebebeb'"
                          [secondaryColor]="'#f7f7f7'"
                        >
                          <svg:g
                            ngx-rect
                            width="1000"
                            height="70"
                            y="50"
                            x="0"
                            rx="5"
                            ry="5"
                          ></svg:g>
                        </ngx-content-loading>
                      </div>
                      <div class="col-1x  pl-3 mt-1 card-content">
                        <ngx-content-loading
                          [speed]="'1500ms'"
                          [width]="1000"
                          [height]="100"
                          [primaryColor]="'#ebebeb'"
                          [secondaryColor]="'#f7f7f7'"
                        >
                          <svg:g
                            ngx-rect
                            width="1000"
                            height="70"
                            y="50"
                            x="0"
                            rx="5"
                            ry="5"
                          ></svg:g>
                        </ngx-content-loading>
                      </div>
                      <div class="col-2x mt-1 d-flex  card-content">
                        <!-- <app-user-profile type="name" role="Invoice R. By" [oid]="item.invoice_raised_by"></app-user-profile> -->
                        <div
                          class="ml-auto"
                          *ngIf="curentlyActiveScreen == 'billed'"
                        >
                          <ngx-content-loading
                            [speed]="'1500ms'"
                            [width]="1000"
                            [height]="100"
                            [primaryColor]="'#ebebeb'"
                            [secondaryColor]="'#f7f7f7'"
                          >
                            <svg:g
                              ngx-rect
                              width="1000"
                              height="70"
                              y="50"
                              x="0"
                              rx="5"
                              ry="5"
                            ></svg:g>
                          </ngx-content-loading>
                        </div>
                        <ngx-content-loading
                          [speed]="'1500ms'"
                          [width]="1000"
                          [height]="100"
                          [primaryColor]="'#ebebeb'"
                          [secondaryColor]="'#f7f7f7'"
                        >
                          <svg:g
                            ngx-rect
                            width="1000"
                            height="70"
                            y="50"
                            x="0"
                            rx="5"
                            ry="5"
                          ></svg:g>
                        </ngx-content-loading>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="pt-2">
              <pagination-controls
                *ngIf="smallCardsDetails.length != 0"
                (pageChange)="p = $event"
                class="invoicePagination"
              ></pagination-controls>
            </div>
          </div>
        </div>
      </div>
      <!-- <div class="col-12 col-md-3 col-lg-3 pt-2 ">
        <div class="card mw-100">
          <div class="card-body" style="padding: 12px">
            <div class="row border-bottom solid">
              <div
                class="row"
                *ngFor="let item of cardRightData"
                style="padding-bottom: 11px !important"
              >
                <div class="row">
                  <div class="col-12 pb-0 pl-2 Rightside-card-title">
                    {{ item.title }}
                  </div>
                </div>
                <div class="row pt-1 pl-2">
                  <div class="col-12 pl-0 Rightside-card-content">
                    {{ item.value }}
                  </div>
                </div>
              </div>
            </div>
            <div class="row">
            </div>
          </div>
        </div>
      </div> -->
    </div>
  </div>
  