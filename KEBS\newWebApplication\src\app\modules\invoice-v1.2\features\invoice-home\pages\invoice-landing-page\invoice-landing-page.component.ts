import {
  Component,
  OnInit,
  ViewChild
} from "@angular/core";
import { UtilityService } from "src/app/services/utility/utility.service";
import { Router, RouterEvent, ActivatedRoute} from "@angular/router";
import { ReplaySubject } from "rxjs";
import { takeUntil } from "rxjs/operators"
import { MatDatepickerInputEvent } from '@angular/material/datepicker';
import {
  MatDialog,
} from "@angular/material/dialog";
import * as moment from "moment";
import { InvoiceHomeService } from "../../services/invoice-home.service";
import swal from "sweetalert2";
import * as _ from "underscore";
import {
  trigger,
  style,
  transition,
  animate,
  state,
  query,
  stagger
} from "@angular/animations";
import { MatSelectionList } from '@angular/material/list';
import { RolesService } from 'src/app/services/acl/roles.service';
import { FormBuilder, Validators, FormGroup } from "@angular/forms";
import { TenantService } from 'src/app/services/tenant-service/tenant.service';
import { InvoiceCommonService } from "../../../../common-services/invoice-common.service";
import { MailUtilityService } from 'src/app/app-shared/app-shared-components/mail-box-modal/services/mail-utility.service';
import { GraphApiService } from 'src/app/services/graph/graph-api.service';
import { DunningService } from 'src/app/modules/collector/services/dunning-service/dunning.service';
import { LoginService } from 'src/app/services/login/login.service';
import { MatSnackBar } from '@angular/material/snack-bar';
// import { MailUtilityService } from 'mail-box-modal'

import { UdrfService } from 'src/app/services/udrf/udrf.service';
import { DocViewerComponent } from '../../../../../shared-lazy-loaded-components/attachment-mgmt/components/doc-viewer/doc-viewer.component';
import { SharedLazyLoadedComponentsService } from '../../../../../shared-lazy-loaded-components/services/shared-lazy-loaded-components.service';
@Component({
  selector: 'app-invoice-landing-page',
  templateUrl: './invoice-landing-page.component.html',
  styleUrls: ['./invoice-landing-page.component.scss'],
  animations: [
    trigger("slideInOut", [
      state("in", style({ height: "*", overflow: "hidden" })),
      state("out", style({ height: 0, overflow: "hidden" })),
      transition("* => in", [
        style({ height: 0 }),
        animate(250, style({ height: "*" }))
      ]),
      transition("in=> *", [
        style({ height: "*" }),
        animate(250, style({ height: 0 }))
      ])
    ]),
    trigger("smallCardAnimation", [
      transition("* => *", [
        // each time the binding value changes
        query(
          ":leave",
          [stagger(100, [animate("0.5s", style({ opacity: 0 }))])],
          { optional: true }
        ),
        query(
          ":enter",
          [
            style({ opacity: 0 }),
            stagger(100, [animate("0.5s", style({ opacity: 1 }))])
          ],
          { optional: true }
        )
      ])
    ])
  ]
})
export class InvoiceLandingPageComponent implements OnInit {
  //object declarations
  private $destroyed: ReplaySubject<boolean> = new ReplaySubject(1);
  //viewchilds
  @ViewChild('pnllist') pnllist: MatSelectionList;
  @ViewChild('entityList') entityList: MatSelectionList;
  @ViewChild('serviceTypeList') serviceTypeList: MatSelectionList;

  //Date Range for Planned On in Billed Tab Form
  range: FormGroup = this.fb.group({
    start: [""],
    end: [""],
  })

  //Declarations
  plannedOnStartDate: any = false
  plannedOnEndDate: any = false
  filterDate: any = ''
  mailOpened: any = false

  //shimmer
  shimmArr = [
    1,
    2,
    2,
    1,
    1,
    2,
    1,
    2,
    1,
    1,
    1,
    1,
    1,
    1,
    1,
    1,
    1,
    1,
    1,
    1,
    1,
    1,
    1
  ];
  date: any;
  //filter related declarations
  pnlNames: any;
  entityNames:any;
  serviceTypeNames: any;
  selectedPnls: any = [];
  selectedEntity: any =[];
  selectedServiceTypes: any = [];
  tempCardDetails: any;
  pnlOptions: string[];
  entityOptions: string[];
  serviceTypeOptions: string[];
  isDataAvailable = "";
  paymentCategory = "ytb";
  searchText: any;
  p: number = 1;
  smallCardsLength = 0;
  smallCardDate: any;
  smallCardsDetails: any = [];
  curentlyActiveScreen: string = "YTB";
  viewlistactivated = 0;
  cardviewactivated = 1;

  //---------------------------------------


  //---------------BOOLEAN DECLARATIONS
  sortedByDate: boolean = false;
  sortedByPlannedOnDate: boolean = false;
  filterVisible: boolean = false;
  roleCheck: boolean = false;
  // icon restrictions declaration
  receiptRestriction: boolean = false; //icon1
  undoRestriction: boolean = false; //ic2
  getAppRestriction: boolean = false; //ic3
  descRestriction: boolean = false; //ic4
  printRestriction: boolean = false; //ic5
  launchRestriction: boolean = false; //ic6
  activityVisisblityRestriction: boolean = false;
  paymentTermsList: any;
  url:any;
  isNewVersion: boolean = false;
  isInvoiceDialogOpen:boolean = false;
  isAnnexureDialogOpen:boolean = false;
  showChangeDate:boolean = false;
  showFullValue: boolean = false;
  ytbClicked: boolean = false;
  billedClicked: boolean = false;
  ppClicked: boolean = false;
  prClicked: boolean = false;
  generateAndStorePDFInS3: boolean = false;

  // header name Whether "created on / raised on etc......"
  headerName: string = "Created on";
  title = 'app';
  userId = 999;

  applicationName = "Invoice V1.2";

  orgCodes = [];

  invoiceTenantDetails: any;
  
  dateFormats:any;

  // Filter
  applicationId = 907;
  // Columns 
  udrfBodyColumns = []
  filterConfig: any;
  ytbApplicationId = 1019;
  billedApplicationId = 1020;
  ppApplicationId = 1021;
  prApplicationId = 1022;
  mailTemplate:any;
  mailBillingDetails:any;

  constructor(
    private fb: FormBuilder,
    public dialog: MatDialog,
    private router: Router,
    private invoiceService: InvoiceHomeService,
    // private InvoiveListServiceService: InvoiveListServiceService,
    private route: ActivatedRoute,
    public utilityService: UtilityService,
    private rolesService: RolesService,
    private tenantService: TenantService,
    private invoiceCommonService: InvoiceCommonService,
    private dunningService: DunningService,
    private _graphService: GraphApiService,
    private _loginService: LoginService,
    public mailUtilityService: MailUtilityService,
    private snackBar: MatSnackBar,
    public _udrfService: UdrfService,
    private _sharedService: SharedLazyLoadedComponentsService
  ) {

    console.log(":::::::::::::::::::::::")
    console.log(this.route)
    console.log(this.route.snapshot.params)
    console.log(this.route['_routerState'].snapshot.url)
    this.url = this.route['_routerState'].snapshot.url;

    this.initPlannedOnDateRangeForm();
  }

  initPlannedOnDateRangeForm() {
    this.range.get('start').valueChanges.subscribe(res => {
      if (res) {
        this.plannedOnStartDate = res;
        this.onFilterChange("", 'date')
      }
    })

    this.range.get('end').valueChanges.subscribe(res => {
      if (res) {
        this.plannedOnEndDate = res;
        this.onFilterChange("", 'date')
      }
    })

  }

  resetDateRange() {
    this.range.get("start").reset();
    this.range.get("end").reset();
    this.plannedOnStartDate = null;
    this.plannedOnEndDate = null;
    this.onFilterChange("", "");
  }

  toggleFilter() {
    this.filterVisible = !this.filterVisible;
  }
  getColor(curentlyActiveScreen) {
    switch (curentlyActiveScreen) {
      case "YTB":
        return "#648588";
      case "billed":
        return "#FF7200";
      case "partialPayment":
        return "#00e64d";
      case "PaymentReceived":
        return "#009432";
    }
  }

  viewList(text: string) {
    if (text == "listviewactivated") this.viewlistactivated = 1;
    this.cardviewactivated = 0;
  }

  FilterByDateEvent(type: string, event: MatDatepickerInputEvent<Date>) {
    this.smallCardsDetails = _.where(this.smallCardsDetails, { filterDate: moment(event.value).format('YYYY-MM-DD') });
  }

  //triggered to desect selection list in filter 
  deselect() {

    if (this.pnllist != null) {
      this.pnllist.deselectAll();
    }
    if (this.entityList != null) {
      this.entityList.deselectAll();
    }

    if (this.serviceTypeList != null) {
      this.serviceTypeList.deselectAll();
    }

    //Reset Date Picker
    this.filterDate = undefined;

    //Reset Date Range 
    this.range.patchValue({ start: undefined, end: undefined });

    this.plannedOnStartDate = null;
    this.plannedOnEndDate = null;

    localStorage.removeItem("localStorePlannedOnStartDate");
    localStorage.removeItem("localStorePlannedOnEndDate");

    //Refresh
    this.smallCardsDetails = this.tempCardDetails;
  }


  viewCard() {
    this.cardviewactivated = 1;
    this.viewlistactivated = 0;
  }
  tinyCardClick(
    projectId,
    milestoneId,
    itemId,
    milestoneName,
    projectName,
    item
  ) {
    console.log(item);
    if (this.curentlyActiveScreen == "YTB") {
      console.log(this.url)
        this.isNewVersion = true
        console.log("inside else")
        this.router.navigateByUrl(
          "/main/invoice-v1.2/invoicelist/0/generateInvoiceV1.2/" +
          projectId +
          "/" +
          milestoneId +
          "/" +
          itemId +
          "/" +
          this.encodeURIComponent(projectName) +
          "/" +
          this.encodeURIComponent(milestoneName) +
          "/" +
          item["service_type_id"] +
          "/" +
          item["service_type_group_id"]
        );
      

    } else if (this.curentlyActiveScreen == "billed") {
        let routeUrl = "main/invoice-v1.2/invoicelist/1/invoiceBilled/" +
        projectId + "/" + milestoneId + "/" + itemId + "/billed" + "/" + item.billing_id
      this.router.navigateByUrl(routeUrl);
    } else if (this.curentlyActiveScreen == "partialPayment") {
      console.log("partial payment");
        this.router.navigateByUrl(
          "/main/invoice-v1.2/invoicelist/1/invoiceBilled/" +
          projectId +
          "/" +
          milestoneId +
          "/" +
          itemId +
          "/" +
          "PP" +
          "/" +
          item.billing_id
  
        );
    } else if (this.curentlyActiveScreen == "PaymentReceived") {
      console.log("received payments screen");
      // this.InvoiveListServiceService.sendDataToBilledData(item);

        this.router.navigateByUrl(
          "/main/invoice-v1.2/invoicelist/1/invoiceBilled/" +
          projectId +
          "/" +
          milestoneId +
          "/" +
          itemId +
          "/" +
          "PR" +
          "/" +
          item.billing_id
        );
    }
  }
  encodeURIComponent(str) {
    return encodeURIComponent(str).replace(/[!'()*]/g, function (c) {
      return "%" + c.charCodeAt(0).toString(16);
    });
  }

  changeDate(date) {
    let invoice_date_format = this.dateFormats?.invoice_general_date_format ? this.dateFormats?.invoice_general_date_format : "DD-MM-YY"
    return moment(date).format(invoice_date_format);
  }

  async ngOnInit() {
    //...................intro js section.....................
    // intro.js called only if localStorage has entry yes

    console.log("ngOnit");

    this.getTenantInfo();
    this.getPaymentTerms();
    this.dateFormats = await this.getTenantDateFormats()


    let tzOffset = new Date().getTimezoneOffset();
    console.log("tzOffset", tzOffset)

    this.orgCodes = this.rolesService.getUserRoleOrgCodes(this.applicationName);
    this.roleCheckFunc(); //Used to enable Payment Reversal & Billing Reversal option 
    let localStorePnl = JSON.parse(sessionStorage.getItem('localStoreSelectedPnl'));
    let localStoreEntity = JSON.parse(sessionStorage.getItem('localStoreSelectedEntity'));
    let localStoreStype = JSON.parse(sessionStorage.getItem('localStorageSelectedSTypes'));
    let localStorePlannedStartDate = localStorage.getItem('localStorePlannedOnStartDate');
    let localStorePlannedEndDate = localStorage.getItem('localStorePlannedOnEndDate');

    if (localStorePnl)
      this.selectedPnls = localStorePnl;
    if(localStoreEntity)
      this.selectedEntity = localStoreEntity;
    if (localStoreStype)
      this.selectedServiceTypes = localStoreStype;
    if (localStorePlannedStartDate) {
      this.range.patchValue({ start: new Date(localStorePlannedStartDate) });
      this.plannedOnStartDate = localStorePlannedStartDate;
    }
    if (localStorePlannedEndDate) {
      this.range.patchValue({ end: new Date(localStorePlannedEndDate) });
      this.plannedOnEndDate = localStorePlannedEndDate;
    }

    //......................................................

    if (this.route.snapshot.params["type"] == "1") {
      this.paymentCategory = "billed";
      this.curentlyActiveScreen = "billed";
      this.smallCardsDetails = [];
      this.toggleClicked('BILLED')
      // this.billedlist('usedInOnInit');
    }
    // billed list
    if (this.route.snapshot.params["type"] == "0") {
      this.smallCardsDetails = [];
      this.curentlyActiveScreen = "YTB";
      setTimeout(() => {
        this.toggleClicked('YTB')
        // this.ytblist('usedInOnInit');
      }, 0);
    }
    if (this.route.snapshot.params["type"] == "2") {
      this.paymentCategory = "pp";
      this.curentlyActiveScreen = "partialPayment";
      this.toggleClicked('PP')
      // this.patialpaymentlist('usedInOnInit');
    }
    if (this.route.snapshot.params["type"] == "3") {
      this.paymentCategory = "pr";
      this.curentlyActiveScreen = "PaymentReceived";
      this.toggleClicked('PR')
      // this.paymentReceivedlist('usedInOnInit');
    }
    
    let udrfData = await this.initUdrf();
    this.getInvoiceConfig();
  }


  launchProjectFromInvoice(projectId, projectName, itemId, itemName) {
    console.log(window.location.origin);
    let navigationUrl =
      window.location.origin +
      "/main/project-management/" +
      projectId +
      "/" +
      this.encodeURIComponent(projectName) + "/" + itemId + "/" + this.encodeURIComponent(itemName) + "/overview" ;
      console.log("url")
    console.log(navigationUrl)
    window.open(navigationUrl);
  }

  roleCheckFunc() {
    // this.invoiceService.roleCheck().subscribe(
    //   res => {
    //     console.log("cbfRoleCheck func result")
    //     console.log(res);
    //     if (res == true)
    //       this.roleCheck = true;
    //     else
    //       this.roleCheck = false;
    //   },
    //   err => {
    //     console.log("cbfRoleCheck func error")
    //     console.log(err);
    //   }
    // )
    this.roleCheck = this.invoiceService.invoiceUndoAccess()
  }

  async ytblist(filterConfig) {
    // if(!text)
    // // this.deselect();
    this.ytbClicked = true;
    this.billedClicked = false;
    this.ppClicked = false;
    this.prClicked= false;
    this.invoiceService.getYtbMilestonesV2(this.orgCodes,filterConfig).pipe(takeUntil(this.$destroyed)).subscribe(
      (res: any) => {
        if(this.ytbClicked == true){
        this.smallCardsDetails = res;
        this.shimmArr = [];
        this.smallCardsDetails.length == 0
          ? (this.isDataAvailable = "No Data Available !")
          : "";
        this.tempCardDetails = this.smallCardsDetails; //flag for filter
        this.curentlyActiveScreen = "YTB";
        this.smallCardsLength = this.smallCardsDetails.length;
        this.receiptRestriction = true;
        this.undoRestriction = true;
        this.getAppRestriction = true;
        this.activityVisisblityRestriction = true;
        this.descRestriction = true;
        this.printRestriction = true;
        this.headerName = "Created On";
        this.getFilterData(this.smallCardsDetails);
        this.applyFilterIfAnythingExistInSession();
        }
      },
      err => {
        console.log(err);
      }
    );
  }

  getFilterData(data) {
    console.log(data);
    this.pnlNames = _.sortBy(_.uniq(_.compact((_.pluck(data, "pl_name")))));
    this.serviceTypeNames = _.sortBy(_.uniq(_.compact(_.pluck(data, "service_type_name"))));
    this.entityNames=_.sortBy(_.uniq(_.compact(_.pluck(data, "entity_name"))));
    this.serviceTypeOptions = this.selectedServiceTypes;
    this.pnlOptions = this.selectedPnls;
    this.entityOptions = this.selectedEntity;
  }
  // undo invoice
  undoInvoice(milestoneId, billingId) {
    console.log(milestoneId);
    console.log(billingId);
    let undoConfirm = {
      customClass: {
        title: "title-class",
        confirmButton: "confirm-button-class",
        cancelButton: "confirm-button-class"
      },
      title: "Are you sure want to Undo this Invoice?",
      text: "You won't be able to revert!",
      type: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes"
    }
    swal
      .fire(undoConfirm)
      .then(result => {
        if (result.value) {
          this.invoiceService.undoInvoice(milestoneId, billingId).pipe(takeUntil(this.$destroyed)).subscribe(
            res => {
              console.log(res);
              this.toggleClicked('BILLED')
              // this.billedlist(null);
              if (res == "undo success") {
                let template = {
                  customClass: {
                    title: "title-class",
                    confirmButton: "confirm-button-class"
                  },
                  title: "Undo successful!",
                  icon: "success"
                }
                swal.fire({ title: "Undo successful !", icon: "success", showConfirmButton: true });
              }
              else {
                let template = {
                  customClass: {
                    title: "title-class",
                    confirmButton: "confirm-button-class"
                  },
                  title: "Undo failed",
                  type: "info"
                }
                swal.fire({ title: "Undo failed !", icon: "info", showConfirmButton: true });
              }

            },
            err => {
              console.error(err);
            }
          );
        }
      });
  }

  //Payment Reversal
  paymentReversal(milestoneId) {
    let swa = {
      customClass: {
        title: "title-class",
        confirmButton: "confirm-button-class",
        cancelButton: "confirm-button-class"
      },
      title: "Are you sure want to Delete Payment?",
      text: "You won't be able to revert!",
      type: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes"
    }
    swal
      .fire(swa)
      .then(result => {
        if (result.value) {
          this.invoiceService.paymentReversal(milestoneId).pipe(takeUntil(this.$destroyed)).subscribe(
            res => {
              console.log(res);
              if (this.curentlyActiveScreen == 'partialPayment'){
                this.toggleClicked('PP')
              }
              else if (this.curentlyActiveScreen == "PaymentReceived"){
                this.toggleClicked('PR')
              }
              let template = {
                customClass: {
                  title: "title-class",
                  confirmButton: "confirm-button-class"
                },
                title: "Milestone moved to Billed!",
                type: "success"
              }
              swal.fire({ title: "Milestone moved to Billed !", icon: "success", showConfirmButton: true });
            },
            err => {
              let template = {
                customClass: {
                  title: "title-class",
                  confirmButton: "confirm-button-class"
                },
                title: "Payment Reversal Failed !",
                type: "info"
              }
              swal.fire({ title: "Payment Reversal Failed !", icon: "success", showConfirmButton: true });
              console.error(err);
            }
          );
        }
      });
  }


  // function executed on something changed in filter
  onFilterChange = (event, text) => {
    console.log(text);
    this.smallCardsDetails = this.tempCardDetails;
    let pnltemp = [];
    if (text == "pnlSelected") {
      this.selectedPnls = event;
      sessionStorage.setItem("localStoreSelectedPnl", JSON.stringify(this.selectedPnls));
    }
    if (text == "entitySelected") {
      this.selectedEntity = event;
      sessionStorage.setItem("localStoreSelectedEntity", JSON.stringify(this.selectedEntity));
    }
    if (text == "serviceSelected") {
      this.selectedServiceTypes = event;
      sessionStorage.setItem("localStorageSelectedSTypes", JSON.stringify(this.selectedServiceTypes));
    }
    // console.log(this.selectedPnls);
    if (this.selectedPnls.length > 0) {
      _.each(this.selectedPnls, element1 => {
        let arr = _.where(this.smallCardsDetails, { pl_name: element1 });
        _.each(arr, x => {
          pnltemp.push(x);
        });
      });
      this.smallCardsDetails = pnltemp;
      console.log(pnltemp);
    }
    if (this.selectedEntity.length > 0) {
      pnltemp = [];
      _.each(this.selectedEntity, element1 => {
        let arr = _.where(this.smallCardsDetails, { entity_name: element1 });
        _.each(arr, x => {
          pnltemp.push(x);
        });
      });
      this.smallCardsDetails = pnltemp;
      console.log(pnltemp);
    }

    if (this.selectedServiceTypes.length > 0) {
      pnltemp = [];
      _.each(this.selectedServiceTypes, element1 => {
        let arr = _.where(this.smallCardsDetails, {
          service_type_name: element1
        });
        _.each(arr, x => {
          pnltemp.push(x);
        });
      });
      this.smallCardsDetails = pnltemp;
    }

    if (this.plannedOnStartDate)
      localStorage.setItem("localStorePlannedOnStartDate", this.plannedOnStartDate);

    if (this.plannedOnEndDate)
      localStorage.setItem("localStorePlannedOnEndDate", this.plannedOnEndDate);

    if (this.plannedOnStartDate && this.plannedOnEndDate && this.selectedPnls.length == 0 && this.selectedServiceTypes.length == 0) {

      console.log("if")
      this.plannedOnStartDate = new Date(this.plannedOnStartDate);
      this.plannedOnEndDate = new Date(this.plannedOnEndDate);


      this.smallCardsDetails = _.filter(this.tempCardDetails, l => {
        l.planned_on = new Date(l.planned_on)
        if (this.plannedOnStartDate <= l.planned_on && this.plannedOnEndDate >= l.planned_on) {
          return l;
        }

      })

      this.smallCardsDetails = _.sortBy(this.smallCardsDetails, "planned_on")
    }
    else if ((this.plannedOnStartDate && this.plannedOnEndDate && this.selectedPnls.length > 0)
      || (this.plannedOnStartDate && this.plannedOnEndDate && this.selectedServiceTypes.length > 0) ||
      (this.plannedOnStartDate && this.plannedOnEndDate && this.selectedPnls.length > 0 && this.selectedServiceTypes.length > 0)) {

      console.log("else if")
      console.log(this.plannedOnStartDate, this.plannedOnEndDate);

      this.plannedOnStartDate = new Date(this.plannedOnStartDate);
      this.plannedOnEndDate = new Date(this.plannedOnEndDate);

      this.smallCardsDetails = _.filter(this.smallCardsDetails, l => {
        l.planned_on = new Date(l.planned_on)
        if (this.plannedOnStartDate <= l.planned_on && this.plannedOnEndDate >= l.planned_on) {
          return l;
        }

      })

      this.smallCardsDetails = _.sortBy(this.smallCardsDetails, "planned_on")
      console.log(this.smallCardsDetails)
    }



  };

  applyFilterIfAnythingExistInSession = () => {
    let pnltemp = [];
    if (this.selectedPnls.length > 0) {
      _.each(this.selectedPnls, element1 => {
        let arr = _.where(this.smallCardsDetails, { pl_name: element1 });
        _.each(arr, x => {
          pnltemp.push(x);
        });
      });
      this.smallCardsDetails = pnltemp;
      console.log(pnltemp);
    }
    if (this.selectedEntity.length > 0) {
      pnltemp = [];
      _.each(this.selectedEntity, element1 => {
        let arr = _.where(this.smallCardsDetails, { entity_name: element1 });
        _.each(arr, x => {
          pnltemp.push(x);
        });
      });
      this.smallCardsDetails = pnltemp;
      console.log(pnltemp);
    }
    if (this.selectedServiceTypes.length > 0) {
      pnltemp = [];
      _.each(this.selectedServiceTypes, element1 => {
        let arr = _.where(this.smallCardsDetails, {
          service_type_name: element1
        });
        _.each(arr, x => {
          pnltemp.push(x);
        });
      });
      this.smallCardsDetails = pnltemp;
    }

    if (this.plannedOnStartDate && this.plannedOnEndDate && this.selectedPnls.length == 0 && this.selectedServiceTypes.length == 0) {

      console.log("if")
      this.plannedOnStartDate = new Date(this.plannedOnStartDate);
      this.plannedOnEndDate = new Date(this.plannedOnEndDate);


      this.smallCardsDetails = _.filter(this.tempCardDetails, l => {
        l.planned_on = new Date(l.planned_on)
        if (this.plannedOnStartDate <= l.planned_on && this.plannedOnEndDate >= l.planned_on) {
          return l;
        }

      })

      this.smallCardsDetails = _.sortBy(this.smallCardsDetails, "planned_on")
    }
    else if ((this.plannedOnStartDate && this.plannedOnEndDate && this.selectedPnls.length > 0)
      || (this.plannedOnStartDate && this.plannedOnEndDate && this.selectedServiceTypes.length > 0) ||
      (this.plannedOnStartDate && this.plannedOnEndDate && this.selectedPnls.length > 0 && this.selectedServiceTypes.length > 0)) {

      console.log("else if")
      console.log(this.plannedOnStartDate, this.plannedOnEndDate);

      this.plannedOnStartDate = new Date(this.plannedOnStartDate);
      this.plannedOnEndDate = new Date(this.plannedOnEndDate);

      this.smallCardsDetails = _.filter(this.smallCardsDetails, l => {
        l.planned_on = new Date(l.planned_on)
        if (this.plannedOnStartDate <= l.planned_on && this.plannedOnEndDate >= l.planned_on) {
          return l;
        }

      })

      this.smallCardsDetails = _.sortBy(this.smallCardsDetails, "planned_on")
      console.log(this.smallCardsDetails)
    }


  }
  getLength(text) {
    if (text)
      return text.length;
    else
      return 1;
  }


  sortDataByDate() {
    this.smallCardsDetails = this.smallCardsDetails.reverse();
    this.sortedByDate = !(this.sortedByDate);
  }

  sortDataByPlannedOn() {

    if (this.sortedByPlannedOnDate == true)
      this.smallCardsDetails = _.sortBy(this.smallCardsDetails, "planned_on")
    else {
      this.smallCardsDetails = _.sortBy(this.smallCardsDetails, "planned_on");
      this.smallCardsDetails = this.smallCardsDetails.reverse();
    }

    this.sortedByPlannedOnDate = !(this.sortedByPlannedOnDate);

  }


  async billedlist(filterConfig) {
    this.ytbClicked = false;
    this.billedClicked = true;
    this.ppClicked = false;
    this.prClicked= false;
    this.invoiceService.getBilledMilestonesV2(this.orgCodes, filterConfig).pipe(takeUntil(this.$destroyed)).subscribe(
      (res: any) => {
        console.log(res);
        if(this.billedClicked == true){
        this.curentlyActiveScreen = "billed";
        this.smallCardsDetails = res.billedMilestones;
        this.shimmArr = []
        this.smallCardsDetails.length == 0
          ? (this.isDataAvailable = "No Data Available !")
          : "";
        this.tempCardDetails = this.smallCardsDetails;
        this.smallCardsLength = this.smallCardsDetails.length;
        this.receiptRestriction = false;
        this.activityVisisblityRestriction = false;
        this.undoRestriction = false;
        this.getAppRestriction = false;
        this.descRestriction = false;
        this.printRestriction = false;
        this.headerName = "Raised On";
        this.getFilterData(this.smallCardsDetails);
        this.applyFilterIfAnythingExistInSession();
        }
      },
      err => {
        console.log(err);
      }
    );
  }

  clearFilter() {
    this.deselect();
    sessionStorage.clear();
    this.pnlOptions = [];
    this.entityOptions = [];
    this.entityOptions = [];
    this.serviceTypeOptions = [];
  }


  async patialpaymentlist(filterConfig) {
    // if(!text)
    // this.deselect();
    this.ytbClicked = false;
    this.billedClicked = false;
    this.ppClicked = true;
    this.prClicked= false;
    this.invoiceService
      .getPartialPaymentMilestonesV2(this.orgCodes, filterConfig).pipe(takeUntil(this.$destroyed))
      .subscribe(
        (res: any) => {
          console.log("partial Payment response")
          console.log(res);
          if(this.ppClicked == true){
          this.smallCardsDetails = res;
          this.shimmArr = []
          this.smallCardsDetails.length == 0
            ? (this.isDataAvailable = "No Data Available !")
            : "";
          this.tempCardDetails = this.smallCardsDetails;
          this.curentlyActiveScreen = "partialPayment";
          this.smallCardsLength = this.smallCardsDetails.length;
          this.receiptRestriction = false;
          this.undoRestriction = true;
          this.getAppRestriction = false;
          this.descRestriction = false;
          this.activityVisisblityRestriction = false;
          this.printRestriction = false;
          this.launchRestriction = false;
          this.headerName = "Raised On";
          this.getFilterData(this.smallCardsDetails);
          this.applyFilterIfAnythingExistInSession();
          }
        },
        err => {
          console.log(err);
        }
      );
  }
  toggleClicked(text) {
    if (text == "YTB") {
      this.smallCardsDetails = [];
      this.shimmer()
      this.curentlyActiveScreen = "YTB";
      this._udrfService.resetUdrfData();
      let udrfData = this.initUdrf();
      this.filterDate = undefined;
      this.sortedByDate = false;
    }
    if (text == "BILLED") {
      this.smallCardsDetails = [];
      this.shimmer()
      this.curentlyActiveScreen = "billed";
      this._udrfService.resetUdrfData();

    let paymentExpectedDurationRanges = [
      {
        checkboxId: 'CDCRD',
        checkboxName: 'Current Year',
        checkboxStartValue: moment().startOf('year'),
        checkboxEndValue: moment().endOf('year'),
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxId: 'CDCRD2',
        checkboxName: 'This Week',
        checkboxStartValue: this.utilityService.getFormattedDate(
          moment().startOf('week'),
          moment(moment().startOf('week')).date,
          15,
          0,
          0,
          0
        ),
        checkboxEndValue: this.utilityService.getFormattedDate(
          moment().endOf('week'),
          moment(moment().endOf('week')).date,
          15,
          0,
          0,
          0
        ),
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxId: 'CDCRD3',
        checkboxName: 'This Month',
        checkboxStartValue: this.utilityService.getFormattedDate(
          moment().startOf('month'),
          moment(moment().startOf('month')).date,
          15,
          0,
          0,
          0
        ),
        checkboxEndValue: this.utilityService.getFormattedDate(
          moment().endOf('month'),
          moment(moment().endOf('month')).date,
          15,
          0,
          0,
          0
        ),
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxId: 'CDCRD4',
        checkboxName: 'Next Month',
        checkboxStartValue: this.utilityService.getFormattedDate(
          moment().add(1, 'month').startOf('month'),
          moment(moment().add(1, 'month').startOf('month')).date,
          15,
          0,
          0,
          0
        ),
        checkboxEndValue: this.utilityService.getFormattedDate(
          moment().add(1, 'month').endOf('month'),
          moment(moment().add(1, 'month').endOf('month')).date,
          15,
          0,
          0,
          0
        ),
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxId: 'CDCRD5',
        checkboxName: 'Upcoming 3 Months',
        checkboxStartValue: this.utilityService.getFormattedDate(
          moment().startOf('month'),
          moment(moment().startOf('month')).date,
          15,
          0,
          0,
          0
        ),
        checkboxEndValue: this.utilityService.getFormattedDate(
          moment().add(2, 'month').endOf('month'),
          moment(moment().add(2, 'month').endOf('month')).date,
          15,
          0,
          0,
          0
        ),
        isCheckboxDefaultSelected: false,
      }, {
        checkboxId: 'CDCRD6',
        checkboxName: 'All',
        checkboxStartValue: moment('1920-01-01'),
        checkboxEndValue: moment('2100-12-12'),
        isCheckboxDefaultSelected: false,
      }
    ];

    this._udrfService.udrfFunctions.constructCustomRangeData(
      8,
      'date',
      paymentExpectedDurationRanges
    );

    // Invoice Raised On (Duration)
    let durationRanges = [
      {
        checkboxId: 'CDCRD',
        checkboxName: 'Current Year',
        checkboxStartValue: moment().startOf('year'),
        checkboxEndValue: moment().endOf('year'),
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxId: 'CDCRD2',
        checkboxName: 'This Week',
        checkboxStartValue: this.utilityService.getFormattedDate(
          moment().startOf('week'),
          moment(moment().startOf('week')).date,
          15,
          0,
          0,
          0
        ),
        checkboxEndValue: this.utilityService.getFormattedDate(
          moment().endOf('week'),
          moment(moment().endOf('week')).date,
          15,
          0,
          0,
          0
        ),
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxId: 'CDCRD3',
        checkboxName: 'This Month',
        checkboxStartValue: this.utilityService.getFormattedDate(
          moment().startOf('month'),
          moment(moment().startOf('month')).date,
          15,
          0,
          0,
          0
        ),
        checkboxEndValue: this.utilityService.getFormattedDate(
          moment().endOf('month'),
          moment(moment().endOf('month')).date,
          15,
          0,
          0,
          0
        ),
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxId: 'CDCRD4',
        checkboxName: 'Previous Month',
        checkboxStartValue: this.utilityService.getFormattedDate(
          moment().subtract(1, 'months').startOf('month'),
          moment(moment().subtract(1, 'months').startOf('month')).date,
          15,
          0,
          0,
          0
        ),
        checkboxEndValue: this.utilityService.getFormattedDate(
          moment().subtract(1, 'months').endOf('month'),
          moment(moment().subtract(1, 'months').endOf('month')).date,
          15,
          0,
          0,
          0
        ),
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxId: 'CDCRD5',
        checkboxName: 'All',
        checkboxStartValue: moment('1920-01-01'),
        checkboxEndValue: moment('2100-12-12'),
        isCheckboxDefaultSelected: true
      },
      {
        checkboxId: 'CDCRD6',
        checkboxName: 'Next Month',
        checkboxStartValue: this.utilityService.getFormattedDate(
          moment().add(1, 'month').startOf('month'),
          moment(moment().add(1, 'month').startOf('month')).date,
          15,
          0,
          0,
          0
        ),
        checkboxEndValue: this.utilityService.getFormattedDate(
          moment().add(1, 'month').endOf('month'),
          moment(moment().add(1, 'month').endOf('month')).date,
          15,
          0,
          0,
          0
        ),
        isCheckboxDefaultSelected: false,
      },
    ];

    this._udrfService.udrfFunctions.constructCustomRangeData(
      11,
      'date',
      durationRanges
    );

      let udrfData = this.initUdrf();
      this.filterDate = undefined;
      this.sortedByDate = false;
      // this.billedlist(null);

    }
    if (text == "PP") {
      this.smallCardsDetails = [];
      this.shimmer()
      this.curentlyActiveScreen = "partialPayment";
      this._udrfService.resetUdrfData();

      let paymentExpectedDurationRanges = [
        {
          checkboxId: 'CDCRD',
          checkboxName: 'Current Year',
          checkboxStartValue: moment().startOf('year'),
          checkboxEndValue: moment().endOf('year'),
          isCheckboxDefaultSelected: false,
        },
        {
          checkboxId: 'CDCRD2',
          checkboxName: 'This Week',
          checkboxStartValue: this.utilityService.getFormattedDate(
            moment().startOf('week'),
            moment(moment().startOf('week')).date,
            15,
            0,
            0,
            0
          ),
          checkboxEndValue: this.utilityService.getFormattedDate(
            moment().endOf('week'),
            moment(moment().endOf('week')).date,
            15,
            0,
            0,
            0
          ),
          isCheckboxDefaultSelected: false,
        },
        {
          checkboxId: 'CDCRD3',
          checkboxName: 'This Month',
          checkboxStartValue: this.utilityService.getFormattedDate(
            moment().startOf('month'),
            moment(moment().startOf('month')).date,
            15,
            0,
            0,
            0
          ),
          checkboxEndValue: this.utilityService.getFormattedDate(
            moment().endOf('month'),
            moment(moment().endOf('month')).date,
            15,
            0,
            0,
            0
          ),
          isCheckboxDefaultSelected: false,
        },
        {
          checkboxId: 'CDCRD4',
          checkboxName: 'Next Month',
          checkboxStartValue: this.utilityService.getFormattedDate(
            moment().add(1, 'month').startOf('month'),
            moment(moment().add(1, 'month').startOf('month')).date,
            15,
            0,
            0,
            0
          ),
          checkboxEndValue: this.utilityService.getFormattedDate(
            moment().add(1, 'month').endOf('month'),
            moment(moment().add(1, 'month').endOf('month')).date,
            15,
            0,
            0,
            0
          ),
          isCheckboxDefaultSelected: false,
        },
        {
          checkboxId: 'CDCRD5',
          checkboxName: 'Upcoming 3 Months',
          checkboxStartValue: this.utilityService.getFormattedDate(
            moment().startOf('month'),
            moment(moment().startOf('month')).date,
            15,
            0,
            0,
            0
          ),
          checkboxEndValue: this.utilityService.getFormattedDate(
            moment().add(2, 'month').endOf('month'),
            moment(moment().add(2, 'month').endOf('month')).date,
            15,
            0,
            0,
            0
          ),
          isCheckboxDefaultSelected: false,
        }, {
          checkboxId: 'CDCRD6',
          checkboxName: 'All',
          checkboxStartValue: moment('1920-01-01'),
          checkboxEndValue: moment('2100-12-12'),
          isCheckboxDefaultSelected: false,
        }
      ];
  
      this._udrfService.udrfFunctions.constructCustomRangeData(
        8,
        'date',
        paymentExpectedDurationRanges
      );
      let paymentReceviedDurationRanges = [
        {
          checkboxId: 'CDCRD',
          checkboxName: 'Current Year',
          checkboxStartValue: moment().startOf('year'),
          checkboxEndValue: moment().endOf('year'),
          isCheckboxDefaultSelected: false,
        },
        {
          checkboxId: 'CDCRD2',
          checkboxName: 'This Week',
          checkboxStartValue: this.utilityService.getFormattedDate(
            moment().startOf('week'),
            moment(moment().startOf('week')).date,
            15,
            0,
            0,
            0
          ),
          checkboxEndValue: this.utilityService.getFormattedDate(
            moment().endOf('week'),
            moment(moment().endOf('week')).date,
            15,
            0,
            0,
            0
          ),
          isCheckboxDefaultSelected: false,
        },
        {
          checkboxId: 'CDCRD3',
          checkboxName: 'This Month',
          checkboxStartValue: this.utilityService.getFormattedDate(
            moment().startOf('month'),
            moment(moment().startOf('month')).date,
            15,
            0,
            0,
            0
          ),
          checkboxEndValue: this.utilityService.getFormattedDate(
            moment().endOf('month'),
            moment(moment().endOf('month')).date,
            15,
            0,
            0,
            0
          ),
          isCheckboxDefaultSelected: false,
        },
        {
          checkboxId: 'CDCRD4',
          checkboxName: 'Next Month',
          checkboxStartValue: this.utilityService.getFormattedDate(
            moment().add(1, 'month').startOf('month'),
            moment(moment().add(1, 'month').startOf('month')).date,
            15,
            0,
            0,
            0
          ),
          checkboxEndValue: this.utilityService.getFormattedDate(
            moment().add(1, 'month').endOf('month'),
            moment(moment().add(1, 'month').endOf('month')).date,
            15,
            0,
            0,
            0
          ),
          isCheckboxDefaultSelected: false,
        },
        {
          checkboxId: 'CDCRD5',
          checkboxName: 'Upcoming 3 Months',
          checkboxStartValue: this.utilityService.getFormattedDate(
            moment().startOf('month'),
            moment(moment().startOf('month')).date,
            15,
            0,
            0,
            0
          ),
          checkboxEndValue: this.utilityService.getFormattedDate(
            moment().add(2, 'month').endOf('month'),
            moment(moment().add(2, 'month').endOf('month')).date,
            15,
            0,
            0,
            0
          ),
          isCheckboxDefaultSelected: false,
        }, {
          checkboxId: 'CDCRD6',
          checkboxName: 'All',
          checkboxStartValue: moment('1920-01-01'),
          checkboxEndValue: moment('2100-12-12'),
          isCheckboxDefaultSelected: false,
        }
      ];
  
      this._udrfService.udrfFunctions.constructCustomRangeData(
        9,
        'date',
        paymentReceviedDurationRanges
      );

      // Invoice Raised On (Duration)
      let durationRanges = [
        {
          checkboxId: 'CDCRD',
          checkboxName: 'Current Year',
          checkboxStartValue: moment().startOf('year'),
          checkboxEndValue: moment().endOf('year'),
          isCheckboxDefaultSelected: false,
        },
        {
          checkboxId: 'CDCRD2',
          checkboxName: 'This Week',
          checkboxStartValue: this.utilityService.getFormattedDate(
            moment().startOf('week'),
            moment(moment().startOf('week')).date,
            15,
            0,
            0,
            0
          ),
          checkboxEndValue: this.utilityService.getFormattedDate(
            moment().endOf('week'),
            moment(moment().endOf('week')).date,
            15,
            0,
            0,
            0
          ),
          isCheckboxDefaultSelected: false,
        },
        {
          checkboxId: 'CDCRD3',
          checkboxName: 'This Month',
          checkboxStartValue: this.utilityService.getFormattedDate(
            moment().startOf('month'),
            moment(moment().startOf('month')).date,
            15,
            0,
            0,
            0
          ),
          checkboxEndValue: this.utilityService.getFormattedDate(
            moment().endOf('month'),
            moment(moment().endOf('month')).date,
            15,
            0,
            0,
            0
          ),
          isCheckboxDefaultSelected: false,
        },
        {
          checkboxId: 'CDCRD4',
          checkboxName: 'Previous Month',
          checkboxStartValue: this.utilityService.getFormattedDate(
            moment().subtract(1, 'months').startOf('month'),
            moment(moment().subtract(1, 'months').startOf('month')).date,
            15,
            0,
            0,
            0
          ),
          checkboxEndValue: this.utilityService.getFormattedDate(
            moment().subtract(1, 'months').endOf('month'),
            moment(moment().subtract(1, 'months').endOf('month')).date,
            15,
            0,
            0,
            0
          ),
          isCheckboxDefaultSelected: false,
        },
        {
          checkboxId: 'CDCRD5',
          checkboxName: 'All',
          checkboxStartValue: moment('1920-01-01'),
          checkboxEndValue: moment('2100-12-12'),
          isCheckboxDefaultSelected: true
        }
      ];

      this._udrfService.udrfFunctions.constructCustomRangeData(
        11,
        'date',
        durationRanges
      );
      let udrfData = this.initUdrf();
      this.filterDate = undefined;
      this.sortedByDate = false;
      // this.patialpaymentlist(null);
    }
    if (text == "PR") {
      this.smallCardsDetails = [];
      this.shimmer()
      this.curentlyActiveScreen = "PaymentReceived";
      this._udrfService.resetUdrfData();
      
      let paymentExpectedDurationRanges = [
        {
          checkboxId: 'CDCRD',
          checkboxName: 'Current Year',
          checkboxStartValue: moment().startOf('year'),
          checkboxEndValue: moment().endOf('year'),
          isCheckboxDefaultSelected: false,
        },
        {
          checkboxId: 'CDCRD2',
          checkboxName: 'This Week',
          checkboxStartValue: this.utilityService.getFormattedDate(
            moment().startOf('week'),
            moment(moment().startOf('week')).date,
            15,
            0,
            0,
            0
          ),
          checkboxEndValue: this.utilityService.getFormattedDate(
            moment().endOf('week'),
            moment(moment().endOf('week')).date,
            15,
            0,
            0,
            0
          ),
          isCheckboxDefaultSelected: false,
        },
        {
          checkboxId: 'CDCRD3',
          checkboxName: 'This Month',
          checkboxStartValue: this.utilityService.getFormattedDate(
            moment().startOf('month'),
            moment(moment().startOf('month')).date,
            15,
            0,
            0,
            0
          ),
          checkboxEndValue: this.utilityService.getFormattedDate(
            moment().endOf('month'),
            moment(moment().endOf('month')).date,
            15,
            0,
            0,
            0
          ),
          isCheckboxDefaultSelected: false,
        },
        {
          checkboxId: 'CDCRD4',
          checkboxName: 'Next Month',
          checkboxStartValue: this.utilityService.getFormattedDate(
            moment().add(1, 'month').startOf('month'),
            moment(moment().add(1, 'month').startOf('month')).date,
            15,
            0,
            0,
            0
          ),
          checkboxEndValue: this.utilityService.getFormattedDate(
            moment().add(1, 'month').endOf('month'),
            moment(moment().add(1, 'month').endOf('month')).date,
            15,
            0,
            0,
            0
          ),
          isCheckboxDefaultSelected: false,
        },
        {
          checkboxId: 'CDCRD5',
          checkboxName: 'Upcoming 3 Months',
          checkboxStartValue: this.utilityService.getFormattedDate(
            moment().startOf('month'),
            moment(moment().startOf('month')).date,
            15,
            0,
            0,
            0
          ),
          checkboxEndValue: this.utilityService.getFormattedDate(
            moment().add(2, 'month').endOf('month'),
            moment(moment().add(2, 'month').endOf('month')).date,
            15,
            0,
            0,
            0
          ),
          isCheckboxDefaultSelected: false,
        }, {
          checkboxId: 'CDCRD6',
          checkboxName: 'All',
          checkboxStartValue: moment('1920-01-01'),
          checkboxEndValue: moment('2100-12-12'),
          isCheckboxDefaultSelected: false,
        }
      ];
  
      this._udrfService.udrfFunctions.constructCustomRangeData(
        8,
        'date',
        paymentExpectedDurationRanges
      );
      let paymentReceviedDurationRanges = [
        {
          checkboxId: 'CDCRD',
          checkboxName: 'Current Year',
          checkboxStartValue: moment().startOf('year'),
          checkboxEndValue: moment().endOf('year'),
          isCheckboxDefaultSelected: false,
        },
        {
          checkboxId: 'CDCRD2',
          checkboxName: 'This Week',
          checkboxStartValue: this.utilityService.getFormattedDate(
            moment().startOf('week'),
            moment(moment().startOf('week')).date,
            15,
            0,
            0,
            0
          ),
          checkboxEndValue: this.utilityService.getFormattedDate(
            moment().endOf('week'),
            moment(moment().endOf('week')).date,
            15,
            0,
            0,
            0
          ),
          isCheckboxDefaultSelected: false,
        },
        {
          checkboxId: 'CDCRD3',
          checkboxName: 'This Month',
          checkboxStartValue: this.utilityService.getFormattedDate(
            moment().startOf('month'),
            moment(moment().startOf('month')).date,
            15,
            0,
            0,
            0
          ),
          checkboxEndValue: this.utilityService.getFormattedDate(
            moment().endOf('month'),
            moment(moment().endOf('month')).date,
            15,
            0,
            0,
            0
          ),
          isCheckboxDefaultSelected: false,
        },
        {
          checkboxId: 'CDCRD4',
          checkboxName: 'Next Month',
          checkboxStartValue: this.utilityService.getFormattedDate(
            moment().add(1, 'month').startOf('month'),
            moment(moment().add(1, 'month').startOf('month')).date,
            15,
            0,
            0,
            0
          ),
          checkboxEndValue: this.utilityService.getFormattedDate(
            moment().add(1, 'month').endOf('month'),
            moment(moment().add(1, 'month').endOf('month')).date,
            15,
            0,
            0,
            0
          ),
          isCheckboxDefaultSelected: false,
        },
        {
          checkboxId: 'CDCRD5',
          checkboxName: 'Upcoming 3 Months',
          checkboxStartValue: this.utilityService.getFormattedDate(
            moment().startOf('month'),
            moment(moment().startOf('month')).date,
            15,
            0,
            0,
            0
          ),
          checkboxEndValue: this.utilityService.getFormattedDate(
            moment().add(2, 'month').endOf('month'),
            moment(moment().add(2, 'month').endOf('month')).date,
            15,
            0,
            0,
            0
          ),
          isCheckboxDefaultSelected: false,
        }, {
          checkboxId: 'CDCRD6',
          checkboxName: 'All',
          checkboxStartValue: moment('1920-01-01'),
          checkboxEndValue: moment('2100-12-12'),
          isCheckboxDefaultSelected: false,
        }
      ];
  
      this._udrfService.udrfFunctions.constructCustomRangeData(
        9,
        'date',
        paymentReceviedDurationRanges
      );

      // Invoice Raised On (Duration)
      let durationRanges = [
        {
          checkboxId: 'CDCRD',
          checkboxName: 'Current Year',
          checkboxStartValue: moment().startOf('year'),
          checkboxEndValue: moment().endOf('year'),
          isCheckboxDefaultSelected: false,
        },
        {
          checkboxId: 'CDCRD2',
          checkboxName: 'This Week',
          checkboxStartValue: this.utilityService.getFormattedDate(
            moment().startOf('week'),
            moment(moment().startOf('week')).date,
            15,
            0,
            0,
            0
          ),
          checkboxEndValue: this.utilityService.getFormattedDate(
            moment().endOf('week'),
            moment(moment().endOf('week')).date,
            15,
            0,
            0,
            0
          ),
          isCheckboxDefaultSelected: false,
        },
        {
          checkboxId: 'CDCRD3',
          checkboxName: 'This Month',
          checkboxStartValue: this.utilityService.getFormattedDate(
            moment().startOf('month'),
            moment(moment().startOf('month')).date,
            15,
            0,
            0,
            0
          ),
          checkboxEndValue: this.utilityService.getFormattedDate(
            moment().endOf('month'),
            moment(moment().endOf('month')).date,
            15,
            0,
            0,
            0
          ),
          isCheckboxDefaultSelected: false,
        },
        {
          checkboxId: 'CDCRD4',
          checkboxName: 'Previous Month',
          checkboxStartValue: this.utilityService.getFormattedDate(
            moment().subtract(1, 'months').startOf('month'),
            moment(moment().subtract(1, 'months').startOf('month')).date,
            15,
            0,
            0,
            0
          ),
          checkboxEndValue: this.utilityService.getFormattedDate(
            moment().subtract(1, 'months').endOf('month'),
            moment(moment().subtract(1, 'months').endOf('month')).date,
            15,
            0,
            0,
            0
          ),
          isCheckboxDefaultSelected: false,
        },
        {
          checkboxId: 'CDCRD5',
          checkboxName: 'All',
          checkboxStartValue: moment('1920-01-01'),
          checkboxEndValue: moment('2100-12-12'),
          isCheckboxDefaultSelected: true
        }
      ];

      this._udrfService.udrfFunctions.constructCustomRangeData(
        11,
        'date',
        durationRanges
      );
      let udrfData = this.initUdrf();
      this.filterDate = undefined;
      this.sortedByDate = false;
      // this.paymentReceivedlist(null);
    }
  }

  async paymentReceivedlist(filterConfig) {
    // if(!text)
    // this.deselect();
    this.ytbClicked = false;
    this.billedClicked = false;
    this.ppClicked = false;
    this.prClicked= true;
    this.invoiceService
      .getPaymentReceivedMilestonesV2(this.orgCodes, filterConfig).pipe(takeUntil(this.$destroyed))
      .subscribe(
        (res: any) => {
          console.log(res);
          if(this.prClicked == true){
          this.smallCardsDetails = res;
          this.shimmArr = []
          this.smallCardsDetails.length == 0
            ? (this.isDataAvailable = "No Data Available !")
            : "";
          this.tempCardDetails = this.smallCardsDetails;
          this.curentlyActiveScreen = "PaymentReceived";
          this.smallCardsLength = this.smallCardsDetails.length;
          this.receiptRestriction = false;
          this.activityVisisblityRestriction = false;
          this.undoRestriction = true;
          this.getAppRestriction = false;
          this.descRestriction = false;
          this.printRestriction = false;
          this.launchRestriction = false;
          this.headerName = "Collected On";
          this.getFilterData(this.smallCardsDetails);
          this.applyFilterIfAnythingExistInSession();
          }
        },
        err => {
          console.log(err);
        }
      );
  }

  getValueInCard() {
    switch (this.curentlyActiveScreen) {
      case "YTB":
        return "Milestone Value";
      case "billed":
        return "Billed Value";
      case "partialPayment":
        return "Collected Value";
      case "PaymentReceived":
        return "Collected Value";
    }
  }
  async viewInvoice(billingId, milestoneId) {
    if (this.isInvoiceDialogOpen) {
      return; // Exit the function if the dialog is open
    }
  
    this.isInvoiceDialogOpen = true;
    console.log(this.paymentTermsList)
    if(this.generateAndStorePDFInS3){
      let bucket = 'kebs-invoices';
      let res = await this.invoiceService.retrieveUploadedObjects(bucket,milestoneId).toPromise();
      if (res && res["data"]?.length > 0) {
        this.viewFile(res["data"][0]);
      }
      else{
        // send error message to kebs team
        this.snackBar.open('Kindly contact KEBS team to resolve', 'Close', {
          duration: 5000
        });
        this.isInvoiceDialogOpen = false;
      }
    }
    else{
    const { ViewInvoiceComponent } = await import('../../../../lazy-loaded-components/view-invoice/view-invoice.component')
    this.invoiceService.viewInvoice(billingId).subscribe(
      res => {
        let result = [];
        result.push(res)
        console.log(res);
        if (result && result.length > 0 && res != null) {
          const openInvoice = this.dialog.open(ViewInvoiceComponent, {
            width: "65%",
            height: "100%",
            autoFocus: false,
            maxWidth: "90vw",
            data: {
              pdfData: res,
              billingId: billingId,
              invoiceTenantDetails: this.invoiceTenantDetails,
              paymentTermsList: this.paymentTermsList
            }
          });
          openInvoice.afterClosed().subscribe(() => {
            // Reset the flag when the dialog is closed
            this.isInvoiceDialogOpen = false;
          });
        }
        else {
          this.utilityService.showMessage("No Data found for billing ID", 'Dismiss')
          this.isInvoiceDialogOpen = false;
        }
      },
      err => {
        console.error(err);
        this.isInvoiceDialogOpen = false;
      }
    );
  }
  }
  async viewActivities(milestoneId, milestoneName, itemName, plannedOn) {
    const { PaymentActivityComponent } = await import("../../../../lazy-loaded-components/payment-activity/payment-activity.component")
    const openActivityDialog = this.dialog.open(PaymentActivityComponent, {
      width: "90%",
      height: "100%",
      autoFocus: false,
      maxWidth: "90vw",
      data: {
        milestoneId: milestoneId,
        milestoneName: milestoneName,
        itemName: itemName,
        plannedOn: plannedOn
      }
    });
  }
  async viewAnnexure(billingId, serviceTypeId) {
    if (this.isAnnexureDialogOpen) {
      return; // Exit the function if the dialog is open
    }
    this.dateFormats = await this.getTenantDateFormats()
    this.isAnnexureDialogOpen = true;
    this.invoiceService.viewInvoice(billingId).subscribe(
      res => {
        let result = [];
        result.push(res);
        console.log(res)
        if (result && result.length > 0 && res != null) {
          let invoicePdf = res;
          this.invoiceService
            .viewAnnexure(billingId,serviceTypeId)
            .subscribe(async res => {
              console.log(res);
              if (res != null) {
                let arr = [];
                arr.push({
                  serviceTypeId: res['serviceTypeId'],
                  data: res['data'],
                  invoiceNo: _.pluck(invoicePdf, "invoiceNo").reverse(),
                  dateFormats: this.dateFormats,
                  invoiceTenantDetails: this.invoiceTenantDetails
                });
                const { ViewAnnexureComponent } = await import("../../../../lazy-loaded-components/view-annexure/view-annexure.component")
                const openAnnexure = this.dialog.open(ViewAnnexureComponent, {
                  width: "65%",
                  height: "100%",
                  autoFocus: false,
                  maxWidth: "90vw",
                  data: arr
                });
                openAnnexure.afterClosed().subscribe(() => {
                  // Reset the flag when the dialog is closed
                  this.isAnnexureDialogOpen = false;
                });
              }
              else {
                this.utilityService.showMessage("No Data found for annexure", 'Dismiss')
                this.isAnnexureDialogOpen = false;
              }
            });
        }
        else {
          this.utilityService.showMessage("No Data found", 'Dismiss')
          this.isAnnexureDialogOpen = false;
        }
      },
      err => {
        this.isAnnexureDialogOpen = false;
        console.error(err);
      }
    );
  }

  /** 
 * @description getTenantInfo
 */
  async getTenantInfo() {
    this.tenantService.getTenantInfo().then(async (tenantInfo: any) => {
      let tenant = tenantInfo
      this.invoiceTenantDetails = await this.getInvoiceTenantRoleCheckDetail(tenant.tenant_name, 'Role');
      if(this.invoiceTenantDetails && this.invoiceTenantDetails.data){
      let data = this.invoiceTenantDetails['data']
      if(data && data.hasOwnProperty('is_to_show_change_date') && data.is_to_show_change_date){
        this.showChangeDate = true
      }
      if(data && data.hasOwnProperty('is_to_show_full_value') && data.is_to_show_full_value){
        this.showFullValue = true
        console.log("showFullValue")
        console.log(this.showFullValue)
      }
    }
    },
      err => {
        console.log("cbfRoleCheck func error")
        console.log(err);
      })
  }

  /** 
  * @description getInvoiceTenantRoleCheckDetails
  */
  getInvoiceTenantRoleCheckDetail = (tenantName, checkType) => {
    return new Promise((resolve, reject) => {
      this.invoiceCommonService.getInvoiceTenantCheckDetail(tenantName, checkType).subscribe(res => {
        resolve(res);
      },
        (err) => {
          console.error(err);
          reject(err);
        }
      );
    });
  };

  //Get Payment Terms
  getPaymentTerms() {
    this.invoiceCommonService.getPaymentTerms().then((res: any) => {
      for (let r of res) {
        r['id'] = r['name']
      }
      this.paymentTermsList = res
    }, (err) => {
      console.log(err)
    })
  }


  // Send Mail to customer
  async sendMail(billingId:any, milestoneId:any, ganttId){

    if(this.mailOpened)
      return 

    this.mailOpened = true
    this.initMailBox()

    let billingDetails: any;
    let attachmentDetails: any;
    let mailTemplate: any = await this.invoiceService.getMailTemplate();
    billingDetails = await this.invoiceService.getCustomerBillingDetails(billingId);
    this.mailBillingDetails = billingDetails;
    this.mailTemplate = mailTemplate;
    this.dateFormats = await this.getTenantDateFormats()
    let formattedData =this.invoiceService.formatDataForMailComponent(billingDetails, mailTemplate, this.dateFormats)
     this.mailUtilityService.mUtilityData['newMailTemplateData']
    .push(formattedData)

    this.mailUtilityService.mUtilityData.authorizedMailSenders = formattedData['authorizedMailSenders']
    this.mailUtilityService.mUtilityData.saveRecipientMailIdType['uniqueId'] = formattedData['uniqueId']

    this.mailUtilityService.mUtilityData['formatTableForTemplate'] = true
    if (mailTemplate && mailTemplate.length > 0 && mailTemplate[0].table_auto_structure) {
      this.mailUtilityService.mUtilityData['isTableLayoutAuto'] = mailTemplate[0]?.table_auto_structure == 1 ? true : false;
    } else {
      this.mailUtilityService.mUtilityData['isTableLayoutAuto'] = false;
    }
    this.mailUtilityService.mUtilityData.currentMailMode = { mode: 'create' }
    this.mailUtilityService.mUtilityData.o365Token = { token: (await this._graphService.getO365Token()) }

    this.mailUtilityService.mUtilityData['saveHistoryInTable'] = true
    this.mailUtilityService.mUtilityData.saveHistoryInKebsApiData = {
      url:"/api/invoice/saveMailHistoryInTable",
      jwtToken:this._loginService.getJwtToken(),
      paramsArr:[
        {
          billingId: billingId
        }
      ]
    }

    this.mailUtilityService.mUtilityData.initiateNewMailTemplateData = {
      url:"/api/invoice/refreshMailTemplate",
      jwtToken:this._loginService.getJwtToken(),
      paramsArr:[
        {
          billingId: billingId
        }
      ]
    }

    this.mailUtilityService.mailUiData.showHistoryButton = true
    this.mailUtilityService.mUtilityData.retrieveHistoryFromApi = {
      url: "/api/invoice/retrieveHistoryFromTable",
      jwtToken: this._loginService.getJwtToken(),
      paramsArr: [
        {
          billingId: billingId
        }
      ]
    }

    if (mailTemplate && mailTemplate.length > 0 && mailTemplate[0].is_to_show_spoc) {
      this.mailUtilityService.mailUiData.showSpocName = true
    }
    if (mailTemplate && mailTemplate.length > 0 && mailTemplate[0].is_to_show_title) {
      this.mailUtilityService.mailUiData.showTitle = true
      let customername = _.uniq(_.pluck(billingDetails?.result, 'customerName'));
      if (customername) {
        this.mailUtilityService.mailUiData.title = `Mail to ${customername}`
      }
    }

    let data = this.invoiceTenantDetails['data']
    if (data && data.hasOwnProperty('is_mail_auto_attachment_applicable') && data.is_mail_auto_attachment_applicable) {
      this.mailUtilityService.mUtilityData.autoAttachmentApplicable = true
      this.mailUtilityService.mUtilityData.destinationBucket = 'kebs-invoices-mail-attachments'
      this.mailUtilityService.mUtilityData.routingKey = 'invoices-mail-attachments'
      this.mailUtilityService.mUtilityData.contextId = `INV_${milestoneId}${ganttId}`
      this.mailUtilityService.mUtilityData.attachmentRetrievalDetails = [
        { destinationBucket: 'kebs-invoices', routingKey: 'invoice-pdf', contextId: `INV_${milestoneId}`},
        { destinationBucket: 'kebs-invoices-mail-attachments', routingKey: 'invoices-mail-attachments', contextId: `INV_${milestoneId}${ganttId}` },
      ]
      attachmentDetails = await this.invoiceService.getAttachmentDetails(milestoneId);
      if (attachmentDetails.messType == 'S') {
        attachmentDetails = attachmentDetails['data']
        this.mailUtilityService.mUtilityData.attachmentRetrievalDetails.push(attachmentDetails?.AttachmentsDetails)
      }
      // else {
      //   swal.fire({ title: "Attachment Details not Found !", icon: "info", showConfirmButton: true });
      //   this.mailOpened = false
      //   return
      // }
    }


    const { ViewMailComponent } = await import('src/app/app-shared/app-shared-components/mail-box-modal/view-mail.component');
    const openViewMailComponent = this.dialog.open(ViewMailComponent, {
      width: "96%",
      height: "97%",
      maxWidth: "100vw",
      maxHeight: "100vw",
      data: {},
      disableClose: true
    });

    openViewMailComponent.afterClosed().subscribe(res => {
      this.mailUtilityService.resetMailData()
      this.toggleClicked('BILLED')
      this.mailOpened = false
    })

  
  }

  /**
  * Init mail box 
  */
  initMailBox() {
    let user = this._loginService.getProfile().profile;
    this.mailUtilityService.mUtilityData.saveRecipientMailIds = this.saveMailIds.bind(this)
    this.mailUtilityService.mUtilityData.applicationId = 10
    this.mailUtilityService.mUtilityData.isCcRecipientFieldHasSaveButton = true
    this.mailUtilityService.mUtilityData.isBccRecipientFieldHasSaveButton = true
    this.mailUtilityService.mUtilityData.isToRecipientFieldHasSaveButton = true
    this.mailUtilityService.mUtilityData['hasInitiateNewMailTemplate'] = true
    this.mailUtilityService.mUtilityData['formatTableForTemplate'] = true
    this.mailUtilityService.mUtilityData.currentUserMailId = user['email']
    this.mailUtilityService.mUtilityData.saveSpocName = this.saveSpocNameForCustomer.bind(this)
  }

  /**
  * Save mail recipients - used in mail box
  */
  saveMailIds() {

    let type = this.mailUtilityService.mUtilityData.saveRecipientMailIdType['type']

    let recipientType =
      type === "toMailId" ? "To" :
        type === "ccMailId" ? "CC" :
          type === "bccMailIds" ? "Bcc" : "";


    this.mailUtilityService.sendSweetAlert(`Are you sure you want to save the ${recipientType} recipients against customer?`, "")
      .then((Confirm) => {
        if (Confirm.value) {
          let mailFields = this.mailUtilityService.mUtilityData.saveRecipientMailIdType['mailFields']
          let customer_id = this.mailUtilityService.mUtilityData.saveRecipientMailIdType['uniqueId']
          console.log('customer', customer_id)
          let emailIds = []

          if (type == 'toMailId') {
            mailFields.value.toRecipients.length > 0
              ? _.each(mailFields.value.toRecipients, (item) => { emailIds.push(item.name) })
              : emailIds = []
          }
          else if (type == 'ccMailId') {
            mailFields.value.ccRecipients.length > 0
              ? _.each(mailFields.value.ccRecipients, (item) => { emailIds.push(item.name) })
              : emailIds = []
          }
          else if (type == 'bccMailIds') {
            mailFields.value.bccRecipients.length > 0
              ? _.each(mailFields.value.bccRecipients, (item) => { emailIds.push(item.name) })
              : emailIds = []
          }

          this.dunningService
            .saveDunningRecipientEmail(type, emailIds, customer_id)
            .subscribe((res: any) => {
              if (res.messType == 'S') {
                this.snackBar.open(res.userMess, "Dismiss", { duration: 2000 })
              }
              else {
                console.log(res)
              }
            }, (err) => {
              console.log(err)
            })
        }
        else {
          return
        }
      })
  }

  /**
* Save SPOC Name- used in mail box
*/
  saveSpocNameForCustomer() {
    this.mailUtilityService.mUtilityData.spocSaveEnabled = true
    let customer_id = this.mailUtilityService.mUtilityData.selectedNewMailTemplateData['uniqueId']
    let spoc_name = this.mailUtilityService.mailUiData.mailInputFields.value.spocName
    this.mailBillingDetails['spoc_name'] = this.mailUtilityService.mailUiData.mailInputFields.value.spocName
    this.dunningService
      .saveDunningSpocName(spoc_name, customer_id)
      .subscribe((res: any) => {
        if (res.messType == 'S') {
          this.snackBar.open(res.userMess, "Dismiss", { duration: 2000 })
        }
        else {
          console.log(res)
        }
      }, (err) => {
        console.log(err)
      })
    let body = this.mailUtilityService.mailUiData.mailInputFields.value.body
    body = body.replace(/<a[^>]*>/, '<a>');
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = body;
    const spanElement = tempDiv.querySelector('p > a');
    if (spanElement) {
      spanElement.textContent = this.mailUtilityService.mailUiData.mailInputFields.value.spocName;
    }
    const modifiedBody = tempDiv.innerHTML;

    let formattedData = this.invoiceService.formatDataForMailComponent(this.mailBillingDetails, this.mailTemplate,this.dateFormats);
    formattedData['mailBody'] = modifiedBody
    this.mailUtilityService.mUtilityData['newMailTemplateData'] = []
    this.mailUtilityService.mUtilityData['newMailTemplateData'].push(formattedData);
    this.mailUtilityService.mUtilityData.currentMailMode = { mode: 'not selected' };
    this.mailUtilityService.initiateNewMail()

}

  //Get Tenant Date Formats
  getTenantDateFormats() {
    return new Promise((resolve, reject) => {
      this.invoiceCommonService.getTenantDateFormats().subscribe(
        (res: any) => {
          resolve(res.data);
        },
        (err) => {
          console.log(err);
          resolve(err);
        }
      );
    });

  }

  // Filter V1.2

  async openUdrfFilterModal() {
    const { UdrfModalComponent } = await import('src/app/modules/shared-lazy-loaded-components/udrf-modal/udrf-modal.component');

    this.dialog.open(UdrfModalComponent, {
      minWidth: "100%",
      height: "84%",
      position: { top: '0px', left: '77px' },
      disableClose: true
    });
  }

  initUdrf() {
    this._udrfService.getNotifyReleasesUDRF();
    this._udrfService.udrfBodyData = [];
    this._udrfService.udrfData.applicationId = this.applicationId;
    this._udrfService.udrfUiData.showNewReleasesButton = true;
    this._udrfService.udrfUiData.showItemDataCount = true
    this._udrfService.udrfUiData.itemDataType = "Bills"
    this._udrfService.udrfUiData.totalItemDataCount = 0
    this._udrfService.udrfUiData.showSearchBar = true
    this._udrfService.udrfUiData.showActionButtons = true
    this._udrfService.udrfUiData.showUdrfModalButton = true
    this._udrfService.udrfUiData.showCreateNewComponentButton = false;
    this._udrfService.udrfUiData.showSettingsModalButton = false
    this._udrfService.udrfUiData.isReportDownloading = false
    this._udrfService.udrfUiData.showColumnConfigButton = true
    this._udrfService.udrfUiData.summaryCardsItem = {}
    this._udrfService.udrfUiData.showHierarchyData = {}
    this._udrfService.udrfUiData.inlineEditData = {}
    this._udrfService.udrfUiData.openCommentsData = {}
    this._udrfService.udrfUiData.udrfBodyColumns = this.udrfBodyColumns
    this._udrfService.udrfUiData.udrfVisibleBodyColumns = this._udrfService.udrfUiData.udrfVisibleBodyColumns
    this._udrfService.udrfUiData.udrfInvisibleBodyColumns = this._udrfService.udrfUiData.udrfInvisibleBodyColumns
    this._udrfService.udrfUiData.variant = 0;
    this._udrfService.udrfUiData.itemHasQuickCta = false
    this._udrfService.udrfUiData.itemHasComments = false
    this._udrfService.udrfUiData.itemHasHierarchyView = false;
    this._udrfService.udrfUiData.itemHasInfoButton = false;
    this._udrfService.udrfUiData.itemHasMoreActions = false;
    this._udrfService.udrfUiData.showCollapseButton = false;
    this._udrfService.udrfUiData.horizontalScroll = true;
    this._udrfService.udrfUiData.showGroupByButton = false;
    this._udrfService.udrfUiData.isMultipleView = true;
    this._udrfService.udrfUiData.showReportDownloadButton = false;
    console.log("his.curentlyActiveScreen")
    console.log(this.curentlyActiveScreen)
    if(this.curentlyActiveScreen == 'YTB'){
      this._udrfService.getAppUdrfConfig(this.ytbApplicationId, this.initReport.bind(this));
    }
    else if(this.curentlyActiveScreen == 'billed'){
      console.log("inside billed")
      this._udrfService.getAppUdrfConfig(this.billedApplicationId, this.initReport.bind(this));
      console.log(this._udrfService.udrfData.myFiltersArray)
    }
    else if(this.curentlyActiveScreen == 'partialPayment'){
      this._udrfService.getAppUdrfConfig(this.ppApplicationId, this.initReport.bind(this));
    }
    else if(this.curentlyActiveScreen == 'PaymentReceived'){
      this._udrfService.getAppUdrfConfig(this.prApplicationId, this.initReport.bind(this));
    }

    this._udrfService.getNotifyReleasesUDRF();
    this._udrfService.udrfUiData.ghostButtonUI = false;
    return Promise.resolve();
  }

/**
* Init UDRF report
*/
    async initReport() {
      if(this.curentlyActiveScreen == 'YTB'){
        this.filterConfig = {
          startIndex: 0,
          txTableDetails: this._udrfService.udrfData.txTableDetails,
          mainFilterArray: this._udrfService.udrfData.mainFilterArray,
          startDate: this._udrfService.udrfData.mainApiDateRangeStart,
          endDate: this._udrfService.udrfData.mainApiDateRangeEnd,
          mainSearchParameter: this._udrfService.udrfData.mainSearchParameter,
          searchTableDetails: this._udrfService.udrfData.searchTableDetails,
        };
        let data = await this.ytblist(
          this.filterConfig
        );
      }
      if(this.curentlyActiveScreen == 'billed'){
        let invoiceStartDate; 
        let invoiceEndDate;
        let paymentStartDate; 
        let paymentEndDate;
        let mainFilterArray = JSON.parse(JSON.stringify(this._udrfService.udrfData.mainFilterArray));
        invoiceStartDate = this._udrfService.udrfData.mainApiDateRangeStart ? moment(this._udrfService.udrfData.mainApiDateRangeStart).format("YYYY-MM-DD") : this._udrfService.udrfData.mainApiDateRangeStart;
        invoiceEndDate = this._udrfService.udrfData.mainApiDateRangeEnd ? moment(this._udrfService.udrfData.mainApiDateRangeEnd).format("YYYY-MM-DD") : this._udrfService.udrfData.mainApiDateRangeEnd;
        if (mainFilterArray.length > 0) {
          for (let items of mainFilterArray) {
            if (items.filterId == 11 && !items.isCustomButtonActivated) {
              let dateValues = this.seperateMultipleDate(items.checkboxValues);
              invoiceStartDate = dateValues.startDate ? moment(dateValues.startDate).format("YYYY-MM-DD") : dateValues.startDate;
              invoiceEndDate = dateValues.endDate ? moment(dateValues.endDate).format("YYYY-MM-DD") : dateValues.endDate;
            }
            else if (items.filterId == 8 && !items.isCustomButtonActivated) {
              let dateValues = this.seperateMultipleDate(items.checkboxValues);
              paymentStartDate = dateValues.startDate ? moment(dateValues.startDate).format("YYYY-MM-DD") : dateValues.startDate;
              paymentEndDate = dateValues.endDate ? moment(dateValues.endDate).format("YYYY-MM-DD") : dateValues.endDate;
            }
          }
        }
        this.filterConfig = {
          startIndex: 0,
          txTableDetails: this._udrfService.udrfData.txTableDetails,
          mainFilterArray: this._udrfService.udrfData.mainFilterArray,
          invoiceRaisedStartDate: invoiceStartDate,
          invoiceRaisedEndDate: invoiceEndDate,
          mainSearchParameter: this._udrfService.udrfData.mainSearchParameter,
          searchTableDetails: this._udrfService.udrfData.searchTableDetails,
          paymentExpectedStartDate: paymentStartDate, 
          paymentExpectedEndDate: paymentEndDate
        };
        let data = await this.billedlist(
          this.filterConfig
        );
      }
      if(this.curentlyActiveScreen == 'partialPayment'){
        let invoiceStartDate; 
        let invoiceEndDate;
        let paymentStartDate; 
        let paymentEndDate;
        let paymentReceivedStartDate;
        let paymentReceivedEndDate;
        let mainFilterArray = JSON.parse(JSON.stringify(this._udrfService.udrfData.mainFilterArray));
        invoiceStartDate = this._udrfService.udrfData.mainApiDateRangeStart ? moment(this._udrfService.udrfData.mainApiDateRangeStart).format("YYYY-MM-DD") : this._udrfService.udrfData.mainApiDateRangeStart;
        invoiceEndDate = this._udrfService.udrfData.mainApiDateRangeEnd ? moment(this._udrfService.udrfData.mainApiDateRangeEnd).format("YYYY-MM-DD") : this._udrfService.udrfData.mainApiDateRangeEnd;
        if (mainFilterArray.length > 0) {
          for (let items of mainFilterArray) {
            if (items.filterId == 11 && !items.isCustomButtonActivated) {
              let dateValues = this.seperateMultipleDate(items.checkboxValues);
              invoiceStartDate = dateValues.startDate ? moment(dateValues.startDate).format("YYYY-MM-DD") : dateValues.startDate;
              invoiceEndDate = dateValues.endDate ? moment(dateValues.endDate).format("YYYY-MM-DD") : dateValues.endDate;
            }
            else if (items.filterId == 8 && !items.isCustomButtonActivated) {
              let dateValues = this.seperateMultipleDate(items.checkboxValues);
              paymentStartDate = dateValues.startDate ? moment(dateValues.startDate).format("YYYY-MM-DD") : dateValues.startDate;
              paymentEndDate = dateValues.endDate ? moment(dateValues.endDate).format("YYYY-MM-DD") : dateValues.endDate;
            }
            else if (items.filterId == 9 && !items.isCustomButtonActivated) {
              let dateValues = this.seperateMultipleDate(items.checkboxValues);
              paymentReceivedStartDate = dateValues.startDate ? moment(dateValues.startDate).format("YYYY-MM-DD") : dateValues.startDate;
              paymentReceivedEndDate = dateValues.endDate ? moment(dateValues.endDate).format("YYYY-MM-DD") : dateValues.endDate;
            }
          }
        }

        this.filterConfig = {
          startIndex: 0,
          txTableDetails: this._udrfService.udrfData.txTableDetails,
          mainFilterArray: this._udrfService.udrfData.mainFilterArray,
          invoiceRaisedStartDate: invoiceStartDate,
          invoiceRaisedEndDate: invoiceEndDate,
          mainSearchParameter: this._udrfService.udrfData.mainSearchParameter,
          searchTableDetails: this._udrfService.udrfData.searchTableDetails,
          paymentExpectedStartDate: paymentStartDate, 
          paymentExpectedEndDate: paymentEndDate,
          paymentReceivedStartDate: paymentReceivedStartDate,
          paymentReceivedEndDate: paymentReceivedEndDate
        };

        let data = await this.patialpaymentlist(
          this.filterConfig
        );
      }
      if(this.curentlyActiveScreen == 'PaymentReceived'){
        let invoiceStartDate; 
        let invoiceEndDate;
        let paymentStartDate; 
        let paymentEndDate;
        let paymentReceivedStartDate;
        let paymentReceivedEndDate;
        let mainFilterArray = JSON.parse(JSON.stringify(this._udrfService.udrfData.mainFilterArray));
        invoiceStartDate = this._udrfService.udrfData.mainApiDateRangeStart ? moment(this._udrfService.udrfData.mainApiDateRangeStart).format("YYYY-MM-DD") : this._udrfService.udrfData.mainApiDateRangeStart;
        invoiceEndDate = this._udrfService.udrfData.mainApiDateRangeEnd ? moment(this._udrfService.udrfData.mainApiDateRangeEnd).format("YYYY-MM-DD") : this._udrfService.udrfData.mainApiDateRangeEnd;
        if (mainFilterArray.length > 0) {
          for (let items of mainFilterArray) {
            if (items.filterId == 11 && !items.isCustomButtonActivated) {
              let dateValues = this.seperateMultipleDate(items.checkboxValues);
              invoiceStartDate = dateValues.startDate ? moment(dateValues.startDate).format("YYYY-MM-DD") : dateValues.startDate;
              invoiceEndDate = dateValues.endDate ? moment(dateValues.endDate).format("YYYY-MM-DD") : dateValues.endDate;
            }
            else if (items.filterId == 8 && !items.isCustomButtonActivated) {
              let dateValues = this.seperateMultipleDate(items.checkboxValues);
              paymentStartDate = dateValues.startDate ? moment(dateValues.startDate).format("YYYY-MM-DD") : dateValues.startDate;
              paymentEndDate = dateValues.endDate ? moment(dateValues.endDate).format("YYYY-MM-DD") : dateValues.endDate;
            }
            else if (items.filterId == 9 && !items.isCustomButtonActivated) {
              let dateValues = this.seperateMultipleDate(items.checkboxValues);
              paymentReceivedStartDate = dateValues.startDate ? moment(dateValues.startDate).format("YYYY-MM-DD") : dateValues.startDate;
              paymentReceivedEndDate = dateValues.endDate ? moment(dateValues.endDate).format("YYYY-MM-DD") : dateValues.endDate;
            }
          }
        }

        this.filterConfig = {
          startIndex: 0,
          txTableDetails: this._udrfService.udrfData.txTableDetails,
          mainFilterArray: this._udrfService.udrfData.mainFilterArray,
          invoiceRaisedStartDate: invoiceStartDate,
          invoiceRaisedEndDate: invoiceEndDate,
          mainSearchParameter: this._udrfService.udrfData.mainSearchParameter,
          searchTableDetails: this._udrfService.udrfData.searchTableDetails,
          paymentExpectedStartDate: paymentStartDate, 
          paymentExpectedEndDate: paymentEndDate,
          paymentReceivedStartDate: paymentReceivedStartDate,
          paymentReceivedEndDate: paymentReceivedEndDate
        };

        let data = await this.paymentReceivedlist(
          this.filterConfig
        );
      }

    }
    async getYtbMilestones(filterConfig){
      this.invoiceService.getYtbMilestonesV2(this.orgCodes, filterConfig).pipe(takeUntil(this.$destroyed)).subscribe(
        (res: any) => {
          if(this.ytbClicked == true){
          this.smallCardsDetails = res;
          this.shimmArr = [];
          this.smallCardsDetails.length == 0
            ? (this.isDataAvailable = "No Data Available !")
            : "";
          this.tempCardDetails = this.smallCardsDetails; //flag for filter
          this.curentlyActiveScreen = "YTB";
          this.smallCardsLength = this.smallCardsDetails.length;
          this.receiptRestriction = true;
          this.undoRestriction = true;
          this.getAppRestriction = true;
          this.activityVisisblityRestriction = true;
          this.descRestriction = true;
          this.printRestriction = true;
          this.headerName = "Created On";
          this.getFilterData(this.smallCardsDetails);
          this.applyFilterIfAnythingExistInSession();
          }
        },
        err => {
          console.log(err);
        }
      );
    }

    async getBilledMilestones(filterConfig){
      this.invoiceService.getBilledMilestonesV2(this.orgCodes, filterConfig).pipe(takeUntil(this.$destroyed)).subscribe(
        (res: any) => {
          if(this.ytbClicked == true){
          this.smallCardsDetails = res;
          this.shimmArr = [];
          this.smallCardsDetails.length == 0
            ? (this.isDataAvailable = "No Data Available !")
            : "";
          this.tempCardDetails = this.smallCardsDetails; //flag for filter
          this.curentlyActiveScreen = "YTB";
          this.smallCardsLength = this.smallCardsDetails.length;
          this.receiptRestriction = true;
          this.undoRestriction = true;
          this.getAppRestriction = true;
          this.activityVisisblityRestriction = true;
          this.descRestriction = true;
          this.printRestriction = true;
          this.headerName = "Created On";
          this.getFilterData(this.smallCardsDetails);
          this.applyFilterIfAnythingExistInSession();
          }
        },
        err => {
          console.log(err);
        }
      );
    }

    async getPPMilestones(filterConfig){
      this.invoiceService
      .getPartialPaymentMilestonesV2(this.orgCodes, filterConfig).pipe(takeUntil(this.$destroyed))
      .subscribe(
        (res: any) => {
          console.log("partial Payment response")
          console.log(res);
          if(this.ppClicked == true){
          this.smallCardsDetails = res;
          this.shimmArr = []
          this.smallCardsDetails.length == 0
            ? (this.isDataAvailable = "No Data Available !")
            : "";
          this.tempCardDetails = this.smallCardsDetails;
          this.curentlyActiveScreen = "partialPayment";
          this.smallCardsLength = this.smallCardsDetails.length;
          this.receiptRestriction = false;
          this.undoRestriction = true;
          this.getAppRestriction = false;
          this.descRestriction = false;
          this.activityVisisblityRestriction = false;
          this.printRestriction = false;
          this.launchRestriction = false;
          this.headerName = "Raised On";
          this.getFilterData(this.smallCardsDetails);
          this.applyFilterIfAnythingExistInSession();
          }
        },
        err => {
          console.log(err);
        }
      );
    }

    async getPRMilestones(filterConfig){
      this.invoiceService
      .getPaymentReceivedMilestonesV2(this.orgCodes, filterConfig).pipe(takeUntil(this.$destroyed))
      .subscribe(
        (res: any) => {
          console.log(res);
          if(this.prClicked == true){
          this.smallCardsDetails = res;
          this.shimmArr = []
          this.smallCardsDetails.length == 0
            ? (this.isDataAvailable = "No Data Available !")
            : "";
          this.tempCardDetails = this.smallCardsDetails;
          this.curentlyActiveScreen = "PaymentReceived";
          this.smallCardsLength = this.smallCardsDetails.length;
          this.receiptRestriction = false;
          this.activityVisisblityRestriction = false;
          this.undoRestriction = true;
          this.getAppRestriction = false;
          this.descRestriction = false;
          this.printRestriction = false;
          this.launchRestriction = false;
          this.headerName = "Collected On";
          this.getFilterData(this.smallCardsDetails);
          this.applyFilterIfAnythingExistInSession();
          }
        },
        err => {
          console.log(err);
        }
      );
    }

    shimmer(){
      this.shimmArr = [
        1,
        2,
        2,
        1,
        1,
        2,
        1,
        2,
        1,
        1,
        1,
        1,
        1,
        1,
        1,
        1,
        1,
        1,
        1,
        1,
        1,
        1,
        1
      ];
    }
  // Mark as Sent
  async markAsSent(billingId){
    let markConfirmation = {
      customClass: {
        title: "title-class",
        confirmButton: "confirm-button-class",
        cancelButton: "confirm-button-class"
      },
      title: "Are you sure want to mark this Invoice as sent?",
      type: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes"
    }
    swal
      .fire(markConfirmation)
      .then(result => {
        if (result.value) {
    return new Promise((resolve, reject) => {
      this.invoiceService.markInvoiceAsSent(billingId).subscribe(
        (res: any) => {
          this.toggleClicked('BILLED')
          swal.fire({ title: "Invoice marked as sent successfully !", icon: "success", showConfirmButton: true });
        },
        (err) => {
          console.log(err);
          this.toggleClicked('BILLED')
          swal.fire({ title: "Failed to mark invoice as sent! !", icon: "success", showConfirmButton: true });
        }
      );
    });
  }
})
  }

  seperateMultipleDate(DateArray) {
    let dateData;

    for (let dateTypeArrayItem of DateArray) {
      if (dateTypeArrayItem.isCheckboxSelected) {
        dateData =
        {
          startDate: dateTypeArrayItem.checkboxStartValue,
          endDate: dateTypeArrayItem.checkboxEndValue,
        }
      }
    }
    return dateData;
  }

  async getInvoiceConfig(){
    let invoiceConfig = await this.invoiceService.getInvoiceConfig();
    if(invoiceConfig['data'][0] && invoiceConfig['data'][0].config!=null){
      let invoiceConfigDetails = JSON.parse(invoiceConfig['data'][0].config);
      this.generateAndStorePDFInS3 = invoiceConfigDetails.hasOwnProperty('generate_store_pdf_in_s3') && invoiceConfigDetails['generate_store_pdf_in_s3'] ? invoiceConfigDetails['generate_store_pdf_in_s3'] : false;
    }   
  }

  retrieveUploadedFiles(destinationBucket, contextId){
    return new Promise((resolve, reject)=>{
      this._sharedService.retrieveUploadedObjects(destinationBucket, contextId).subscribe((res: any) => {
            if(!res['err'])
              resolve(res['data'])
            else
              resolve([])
        },
        (err) => {
            reject(err)
        }
      );
    })
    
  }

  viewFile(file) {
    let cdn_link = file.cdn_link;
    let uploadPopUpDialogRef = null;
      // this.isDialogOpened = true;
      this._sharedService.getDownloadUrl(cdn_link).subscribe((res: any) => {
        uploadPopUpDialogRef = this.dialog.open(DocViewerComponent, {
          width: '58%',
          height: '100%',
          data: {
            selectedFileUrl: res.data,
            fileFormat: file.file_format,
            expHeaderId: ""
          },
        });
        uploadPopUpDialogRef.afterClosed().subscribe((res: any) => {
          // this.isDialogOpened = false;
          this.isInvoiceDialogOpen = false;
        });
      });
  }
}




