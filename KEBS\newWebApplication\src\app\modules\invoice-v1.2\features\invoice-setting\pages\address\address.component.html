<div>
  <mat-drawer-container
  class="account-address-style drawer"
  hasBackdrop="false"
  style="min-height: 82vh !important"
>
  <mat-drawer #drawer mode="over" class="side-drawer" position="end">
    <div class="container-fluid">
      <div class="row pt-1 pb-1" style="border-bottom: #d6d6d6 1px solid">
        <div class="col-10 pt-2 address-name">
          {{ mode == "creation" ? "Create Address " : "Edit Address" }}
        </div>
        <div class="col-2 d-flex">
          <button
            mat-icon-button
            class="ml-auto close-button"
            (click)="drawer.toggle()"
          >
            <mat-icon class="close-Icon">close</mat-icon>
          </button>
        </div>
      </div>
      <form [formGroup]="addressForm">
        <div class="row pt-3">
          <div class="col-12">
            <div class="row">
              <div class="col-12">
                <app-input-search
                  class="create-account-field-inputsearch"
                  required="true"
                  placeholder="Address Type"
                  [list]="addressTypes"
                  [formControl]="addressTypeFormControl"
                >
                </app-input-search>
              </div>
            </div>
            <div class="row" *ngIf="formMode == 'legal entity'">
              <div class="col-12">
                <app-input-search
                  class="create-account-field-inputsearch"
                  required="true"
                  placeholder="Legal Entity"
                  [list]="legalEntities"
                  formControlName="legalEntityName"
                  [disabled]="mode === 'edit' ? true : false"
                >
                </app-input-search>
              </div>
            </div>
            <div class="row" *ngIf="formMode == 'customer'">
              <div class="col-12">
                <app-input-search
                  class="create-account-field-inputsearch"
                  required="true"
                  placeholder="Customer"
                  [list]="customersAvailable"
                  formControlName='customerId'
                  (change)="setCustomerValue($event)"
                  [disabled]="mode === 'edit' ? true : false"
                >
                </app-input-search>
              </div>
            </div>
            <div class="row" *ngIf="formMode == 'customer'">
              <div class="col-12">
                <mat-form-field
                  appearance="outline"
                  class="create-account-field"
                >
                  <mat-label>Billing Address Name</mat-label>
                  <input
                    matInput
                    required="true"
                    placeholder="Billing address name"
                    formControlName="billingAddressName"
                  />
                </mat-form-field>
              </div>
            </div>
            <div class="row">
              <div class="col-12">
                <mat-form-field
                  appearance="outline"
                  class="create-account-field"
                >
                  <mat-label>Address line 1</mat-label>
                  <input
                    matInput
                    required="true"
                    placeholder="Address line 1"
                    formControlName="addressLine1"
                  />
                </mat-form-field>
              </div>
            </div>
            <div class="row">
              <div class="col-12">
                <mat-form-field
                  appearance="outline"
                  class="create-account-field"
                >
                  <mat-label>Address line 2</mat-label>
                  <input
                    matInput
                    required="true"
                    placeholder="Address line 2"
                    formControlName="addressLine2"
                  />
                </mat-form-field>
              </div>
            </div>
            <div class="row">
              <div class="col-12">
                <mat-form-field
                  appearance="outline"
                  class="create-account-field"
                >
                  <mat-label>Address line 3</mat-label>
                  <input
                    matInput
                    placeholder="Address line 3"
                    formControlName="addressLine3"
                  />
                </mat-form-field>
              </div>
            </div>
            <div class="row">
              <div class="col-12">
                <mat-form-field
                  appearance="outline"
                  class="create-account-field"
                >
                  <mat-label>Address line 4</mat-label>
                  <input
                    matInput
                    placeholder="Address line 4"
                    formControlName="addressLine4"
                  />
                </mat-form-field>
              </div>
            </div>
          </div>
          <div class="col-12">
            <!-- <div class="row pt-2" *ngIf="formMode == 'customer'">
              <div class="col-8">
                <app-input-search
                  class="create-account-field-inputsearch"
                  placeholder="Country"
                  [list]="countries"
                  formControlName="country"
                >
                </app-input-search>
              </div>
            </div>
            <div class="row" *ngIf="formMode == 'customer' && states != null">
              <div class="col-8">
                <app-input-search
                  class="create-account-field-inputsearch"
                  placeholder="State"
                  [list]="states"
                  formControlName="state"
                >
                </app-input-search>
              </div>
            </div> -->
            <div class="row" *ngIf="formMode == 'customer'">
              <div class="col-8">
                <mat-form-field
                  appearance="outline"
                  class="create-account-field"
                >
                  <mat-label>Pin Code</mat-label>
                  <input
                    matInput
                    placeholder="Pin Code"
                    formControlName="pinCode"
                  />
                </mat-form-field>
              </div>
            </div>
            <div class="row" *ngIf="formMode == 'customer'">
              <div class="col-8">
                <mat-form-field
                  appearance="outline"
                  class="create-account-field"
                >
                  <mat-label>Telephone#</mat-label>
                  <input
                    matInput
                    placeholder="Telephone"
                    formControlName="telephone"
                  />
                </mat-form-field>
              </div>
            </div>
          </div>
        </div>
        <div class="row pl-5">
          <div class="col-6 pl-0">
            <div class="footer-text mt-3">
              <!-- <mat-checkbox
                formControlName="is_billing"
                *ngIf="formMode == 'customer'"
                >Billing address</mat-checkbox
              > -->
            </div>
          </div>
          <div class="col-3">
            <button
              mat-icon-button
              class="iconbtn ml-auto mr-3 mt-2"
              matTooltip="Create address"
              (click)="saveForm()"
              [disabled] = "!addressForm.valid"
              [ngClass]="{ 'restrict-cursor': !addressForm.valid }"
            >
              <mat-icon> done_all</mat-icon>
            </button>
          </div>
        </div>
      </form>
    </div>
  </mat-drawer>
  <mat-drawer-content>
    <div class="row pt-1 pl-2 pb-1">
      <div class="col-3">
        <button mat-icon-button color="primary" (click)="moveBack()">
          <mat-icon>keyboard_arrow_left</mat-icon>
        </button>
        <!-- <ng-container *ngIf="bank">
          <div *ngIf="bank.length != 0">
            <span class="tileName">
              Total Address :
            </span>
            <span class="pl-2 addresses">
              -
            </span>
          </div>
        </ng-container> -->
      </div>
      <div class="col-3 search-bar d-flex">
        <!-- <mat-form-field
          appearance="outline"
          class="ml-auto create-account-field"
        >
          <span matPrefix>
            <mat-icon
              style="font-size: 21px !important; color: #66615b !important;"
              >search</mat-icon
            >
          </span>
          <input
            matInput
            type="search"
            name="search"
            placeholder="Search Address"
            [(ngModel)]="searchText"
            (ngModelOptions)="({ debounce: 200 })"
          />
          <mat-icon matSuffix>
            <button
              mat-button
              matSuffix
              mat-icon-button
              aria-label="Clear"
              style="height: 30px; width: 30px; line-height: 1;"
              (click)="searchText = ''"
            >
              <mat-icon
                matTooltip="Clear search"
                style="font-size: 18px !important; color: #66615b !important;"
                >close
              </mat-icon>
            </button>
          </mat-icon>
        </mat-form-field> -->
      </div>
      <div class="col-2 d-flex">
        <div class="row">
          <div class="col-12">
            <app-input-search
              class="create-account-field"
              [list]="addressTypes"
              placeholder="Address Type"
              [formControl]="viewAddressTypeFormControl"
            >
            </app-input-search>
          </div>
        </div>
      </div>
      <div class="col-3 d-flex">
        <div class="row">
          <div class="col-10">
            <app-input-search
              *ngIf="legalEntityFormControlIsVisible == true"
              class="create-account-field"
              [list]="legalEntities"
              placeholder="Legal Entity"
              [formControl]="legalEntityFormControl"
            >
            </app-input-search>
            <app-input-search
              *ngIf="customerNameFormControlIsVisible == true"
              class="create-account-field"
              [list]="customersAvailable"
              placeholder="Customer"
              [formControl]="customerNameFormControl"
            >
            </app-input-search>
          </div>
          <div class="col-2 d-flex pr-0">
            <button
              mat-icon-button
              matTooltip="New address"
              class="more-button ml-auto my-auto mr-5"
              (click)="
                drawer.toggle();
                mode = 'creation';
                addressForm.reset();
                addressTypeFormControl.reset();
                formMode = null
              "
            >
              <mat-icon class="close-Icon">add</mat-icon>
            </button>
          </div>
        </div>
      </div>
    </div>
    <div class="row pt-2" *ngIf="address_not_found == false">
      <div class="col-4 pb-2" *ngFor="let address of addresses">
        <div class="card address-card">
          <div class="card-body p-2">
            <div class="row pt-1" *ngIf="address.type == 'customer'">
              <div class="col-5 address-title">Customer name</div>
              <div class="col-7 address-name pl-0" [matTooltip]="address.customerName">
                <div class="truncate-text">{{ address.customerName }}</div>
              </div>
            </div>
            <div class="row pt-1" *ngIf="address.type != 'customer'">
              <div class="col-5 address-title">Legal Entity Name</div>
              <div class="col-7 address-name pl-0" [matTooltip]="address.legalEntityName">
                <div class="truncate-text">{{ address.legalEntityName }}</div> 
              </div>
            </div>
            <div class="row pt-1 pb-1">
              <div class="col-5 address-title">Address line 1</div>
              <div class="col-7 addresses pl-0">
                {{ address.addressLine1 }}
              </div>
            </div>
            <div class="row pt-1 pb-1">
              <div class="col-5 address-title">Address line 2</div>
              <div class="col-7 addresses pl-0">
                {{ address.addressLine2 }}
              </div>
            </div>
            <div class="row pt-1 pb-1">
              <div class="col-5 address-title">Address line 3</div>
              <div class="col-7 addresses pl-0">
                {{ address.addressLine3 }}
              </div>
            </div>
            <div class="row pt-1 pb-1">
              <div class="col-5 address-title">Address line 4</div>
              <div class="col-7 addresses pl-0">
                {{ address.addressLine4 }}
              </div>
            </div>
            <div class="row pt-1 pb-1" *ngIf="address.type == 'customer'">
              <div class="col-5 address-title">Pincode</div>
              <div class="col-7 addresses pl-0">
                {{ address.pinCode }}
              </div>
            </div>
            <div
              class="row pt-1 pb-1 border-bottom solid"
              *ngIf="address.type == 'customer'"
            >
              <div class="col-5 address-title">Phone#</div>
              <div class="col-7 addresses pl-0">
                {{ address.telephone }}
              </div>
            </div>
            <div class="row pt-1">
              <div class="col-5 addresses pr-0 pt-1">
                <!-- <mat-checkbox [checked]="address.is_billing == 1" disabled
                *ngIf="formMode == 'customer'">Billing address</mat-checkbox
                > -->
              </div>
              <div class="col-7 pl-0 pt-1">
                <span class="footer-text">
                  <mat-checkbox
                    *ngIf="address.is_default == 1"
                    [checked]="true"
                    [attr.disabled]="true"
                    >Default</mat-checkbox
                  >
                  <mat-checkbox
                    *ngIf="address.is_default == 0"
                    (change)="changeIsDefault(address)"
                    >Default</mat-checkbox
                  >
                </span>
                <span class="my-auto" style="padding-left: 44px"
                  ><button
                    mat-icon-button
                    (click)="editAddress(address)"
                    matTooltip="Edit"
                    class="icon-tray-button mr-2"
                  >
                    <mat-icon class="smallCardIcon">edit</mat-icon>
                  </button></span
                >

                <span class="my-auto mr-2"
                  ><button
                    mat-icon-button
                    class="icon-tray-button"
                    matTooltip="Delete"
                    (click)="deleteAddress(address)"
                  >
                    <mat-icon class="smallCardIcon">delete</mat-icon>
                  </button></span
                >
                <!-- <span class="my-auto"
                  ><button
                    mat-icon-button
                    class="icon-tray-button"
                    matTooltip="Other Language Translation"
                    (click)="getOtherLanguageTranslationForThisAddress(address)"
                  >
                    <mat-icon class="smallCardIcon">language</mat-icon>
                  </button></span
                > -->
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div *ngIf ="address_not_found">
      <div>
        <div>
          <h1
            class="d-flex justify-content-center align-items-center mt-5 slide-in-top"
          >
            No Address found !
          </h1>
        </div>
        <div
          class="d-flex justify-content-center align-items-center slide-from-down"
        >
          <img
            src="assets/images/nomilestone.png"
            class="mt-4"
            height="300"
            width="350"
          />
        </div>
      </div>
    
    </div>
    <!-- for test -->
    <!-- {{ addressForm.value | json }} -->
  </mat-drawer-content>
</mat-drawer-container>
</div>

