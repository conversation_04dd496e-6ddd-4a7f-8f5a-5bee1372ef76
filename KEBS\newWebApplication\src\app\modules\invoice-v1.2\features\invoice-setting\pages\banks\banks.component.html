<mat-drawer-container
  class="account-address-style drawer"
  hasBackdrop="false"
  style="min-height: 82vh !important"
>
  <mat-drawer #drawer mode="over" class="side-drawer" position="end">
    <div class="container-fluid">
      <div class="row pt-1 pb-1" style="border-bottom: #d6d6d6 1px solid">
        <div class="col-10 pt-2 address-name">
          {{ mode == "creation" ? "Create Bank " : "Edit Bank" }}
        </div>
        <div class="col-2 d-flex">
          <button
            mat-icon-button
            class="ml-auto close-button"
            (click)="drawer.toggle()"
          >
            <mat-icon class="close-Icon">close</mat-icon>
          </button>
        </div>
      </div>
      <form [formGroup]="bankForm">
        <div class="row pt-3">
          <div class="col-12">
            <div class="row pt-2">
              <div class="col-12">
                <app-input-search
                  class="create-account-field"
                  required="true"
                  [list]="legalEntities"
                  placeholder="Legal Entity"
                  formControlName="legal_entity"
                  [disabled]="mode === 'edit' ? true : false"
                >
                </app-input-search>
              </div>
            </div>
            <div class="row">
              <div class="col-12">
                <mat-form-field
                  appearance="outline"
                  class="create-account-field"
                >
                  <mat-label>Bank Name</mat-label>
                  <input
                    matInput
                    placeholder="Bank Name"
                    formControlName="bank_name"
                  />
                </mat-form-field>
              </div>
            </div>
            <div class="row">
              <div class="col-12">
                <mat-form-field
                  appearance="outline"
                  class="create-account-field"
                >
                  <mat-label>Account No</mat-label>
                  <input
                    matInput
                    placeholder="Account No"
                    formControlName="bank_acc_no"
                  />
                </mat-form-field>
              </div>
            </div>
            <div class="row">
              <div class="col-12">
                <mat-form-field
                  appearance="outline"
                  class="create-account-field"
                >
                  <mat-label>Branch</mat-label>
                  <input
                    matInput
                    placeholder="Branch"
                    formControlName="bank_branch"
                  />
                </mat-form-field>
              </div>
            </div>
            <div class="row">
              <div class="col-12">
                <mat-form-field
                  appearance="outline"
                  class="create-account-field"
                >
                  <mat-label>Address</mat-label>
                  <input
                    matInput
                    placeholder="Address"
                    formControlName="bank_address"
                  />
                </mat-form-field>
              </div>
            </div>
            <div class="row">
              <div class="col-12">
                <mat-form-field
                  appearance="outline"
                  class="create-account-field"
                >
                  <mat-label>IBAN</mat-label>
                  <input matInput placeholder="IBAN" formControlName="IBAN" />
                </mat-form-field>
              </div>
            </div>
            <div class="row">
              <div class="col-12">
                <mat-form-field
                  appearance="outline"
                  class="create-account-field"
                >
                  <mat-label>IFSC</mat-label>
                  <input
                    matInput
                    placeholder="IFSC"
                    formControlName="ifsc_code"
                  />
                </mat-form-field>
              </div>
            </div>
            <div class="row">
              <div class="col-12">
                <mat-form-field
                  appearance="outline"
                  class="create-account-field"
                >
                  <mat-label>Bank Swift code</mat-label>
                  <input
                    matInput
                    placeholder="Bank swift code"
                    formControlName="swift_code"
                  />
                </mat-form-field>
              </div>
            </div>
            <div class="row pl-5">
              <div class="col-6 pl-0">
                <div class="footer-text mt-3"></div>
              </div>
              <div class="col-4 d-flex">
                <button
                  mat-icon-button
                  class="iconbtn ml-auto mr-3 mt-2"
                  (click)="saveForm()"
                >
                  <mat-icon> done_all</mat-icon>
                </button>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
  </mat-drawer>
  <!-- mat-drawer content starts -->
  <mat-drawer-content>
    <div class="row pt-2 pl-2 pb-1">
      <div class="col-3">
        <button mat-icon-button color="primary" (click)="moveBack()">
          <mat-icon>keyboard_arrow_left</mat-icon>
        </button>
        <!--
          <ng-container *ngIf="bank">
            <div *ngIf="bank.length != 0">
              <span class="tileName">
                Total Banks :
              </span>
              <span class="pl-2 addresses">
                {{ availableBanksForSelectedLegalEntity.length }}
              </span>
            </div>

          </ng-container> -->
      </div>
      <div class="col-5 search-bar d-flex">
        <mat-form-field appearance="outline" class="ml-auto">
          <span matPrefix>
            <mat-icon
              style="font-size: 21px !important; color: #66615b !important"
              >search</mat-icon
            >
          </span>
          <input
            matInput
            type="search"
            name="search"
            placeholder="Search Bank"
            [(ngModel)]="searchText"
            (ngModelOptions)="({ debounce: 200 })"
          />
          <mat-icon matSuffix>
            <!-- <mat-spinner *ngIf="isloading == true" diameter="18" class="mt-2"></mat-spinner> -->
            <button
              mat-button
              matSuffix
              mat-icon-button
              aria-label="Clear"
              style="height: 30px; width: 30px; line-height: 1"
              (click)="searchText = ''"
            >
              <mat-icon
                matTooltip="Clear search"
                style="font-size: 18px !important; color: #66615b !important"
                >close
              </mat-icon>
            </button>
          </mat-icon>
        </mat-form-field>
      </div>
      <div class="col-3 d-flex">
        <div class="row">
          <div class="col-10">
            <app-input-search
              class="create-account-field"
              [list]="legalEntities"
              placeholder="Legal Entity"
              [formControl]="selectedLegalEntity"
            >
            </app-input-search>
          </div>
          <div class="col-2 d-flex pr-0">
            <button
              mat-icon-button
              matTooltip="New address"
              class="more-button ml-auto my-auto mr-5"
              (click)="drawer.toggle(); mode = 'creation'; bankForm.reset()"
            >
              <mat-icon class="close-Icon">add</mat-icon>
            </button>
          </div>
        </div>
      </div>
    </div>
    <div class="row">
      <div
        class="col-4 pb-2"
        *ngFor="
          let bank of availableBanksForSelectedLegalEntity | filter: searchText
        "
      >
        <div class="card address-card slide-from-down">
          <div class="card-body p-2">
            <div class="row pt-1">
              <div class="col-5 address-title">Bank name</div>
              <div class="col-7 address-name pl-0" [matTooltip]=" bank.bank_name">
                <div class="truncate-text">{{ bank.bank_name }}</div>
              </div>
            </div>
            <div class="row pt-1">
              <div class="col-5 address-title">Account No</div>
              <div class="col-7 addresses pl-0">
                {{ bank.bank_acc_no }}
              </div>
            </div>
            <div class="row pt-1">
              <div class="col-5 address-title">Branch</div>
              <div class="col-7 addresses pl-0">
                {{ bank.bank_branch }}
              </div>
            </div>
            <div class="row pt-1">
              <div class="col-5 address-title">Address</div>
              <div
                class="col-7 addresses pl-0"
                [matTooltip]="bank.bank_address ? bank.bank_address : '-'"
              >
                {{ bank.bank_address }}
              </div>
            </div>
            <div class="row pt-1">
              <div class="col-5 address-title">IBAN</div>
              <div class="col-7 addresses pl-0">
                {{ bank.IBAN }}
              </div>
            </div>
            <div class="row pt-1">
              <div class="col-5 address-title">Bank Swift Code</div>
              <div class="col-7 addresses pl-0">
                {{ bank.swift_code }}
              </div>
            </div>
            <div class="row pt-1 border-bottom solid">
              <div class="col-5 address-title">Entity</div>
              <div class="col-7 addresses pl-0">
                {{ bank.legal_entity_name }}
              </div>
            </div>
            <div class="row pt-1">
              <div class="col-5 addresses pr-0 pt-1">
                {{ bank.company_code }}
              </div>
              <div class="col-7 pl-0 pt-1 d-flex">
                <span class="footer-text my-auto mt-1 mr-5">
                  <mat-checkbox
                    *ngIf="bank.is_default == 1"
                    [checked]="true"
                    [attr.disabled]="true"
                    [disabled]="true"
                    >Default</mat-checkbox
                  >
                  <mat-checkbox
                    *ngIf="bank.is_default == 0"
                    (change)="changeIsDefault(bank)"
                    >Default
                  </mat-checkbox>
                </span>
                <!-- <span class="my-auto"
                  ><button
                    mat-icon-button
                    (click)="viewTranslatedAddressByOpeningBottomSheet(bank)"
                    matTooltip="View translated address"
                    class="icon-tray-button mr-3"
                  >
                    <mat-icon class="smallCardIcon">language</mat-icon>
                  </button></span
                > -->
                <span class="my-auto"
                  ><button
                    mat-icon-button
                    (click)="editAddress(bank)"
                    matTooltip="Edit"
                    class="icon-tray-button mr-3"
                  >
                    <mat-icon class="smallCardIcon">edit</mat-icon>
                  </button></span
                >

                <span class="my-auto"
                  ><button
                    mat-icon-button
                    class="icon-tray-button"
                    matTooltip="Delete"
                    (click)="deleteAddress(bank)"
                  >
                    <mat-icon class="smallCardIcon">delete</mat-icon>
                  </button></span
                >
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- {{ bankForm.value | json }} -->
  </mat-drawer-content>
</mat-drawer-container>
