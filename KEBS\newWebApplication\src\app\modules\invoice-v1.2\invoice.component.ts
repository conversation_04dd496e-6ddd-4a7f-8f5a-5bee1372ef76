import { Component, OnInit } from '@angular/core';
import {InvoiceHomeService} from './features/invoice-home/services/invoice-home.service'
@Component({
  selector: 'app-invoice',
  templateUrl: './invoice.component.html',
  styleUrls: ['./invoice.component.scss']
})
export class InvoiceComponent implements OnInit {
  tabLinks: any = [
    { label: "Invoices", path: "invoicelist" },
    { label: "Upcoming payments", path: "upcomings" },
    // { label: "Dunning", path: "dunning" },
    { label:"Settings",path:"setting"},
    // {label: "Intercompany Invoices", path: "intercompany-invoice"}
  ];
  roleCheck:any;
  constructor(private invoiceHomeService: InvoiceHomeService,) { }

  ngOnInit(): void {
    this.roleCheck = this.invoiceHomeService.intercompanyTabAccess()
    if(this.roleCheck){
      this.tabLinks.push({ label: "Intercompany Invoices", path: "intercompany-invoice" });
    }
  }

}
