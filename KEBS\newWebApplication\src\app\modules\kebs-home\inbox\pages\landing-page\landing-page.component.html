<div class="bg-container">
  <div
    class="header"
    [ngStyle]="{
      background: homePageUiConfig['HEADER-BACKGROUND-001'],
      visibility: isLoading ? 'hidden' : ''
    }"
  >
    <div
      class="svg"
      [innerHTML]="
        homePageUiConfig['HEADER-ICON-001-01'] || '' | svgSecurityBypass
      "
      (click)="openHomeScreen()"
    ></div>
    <div class="bar-text"></div>
    <div
      class="svg"
      [innerHTML]="
        homePageUiConfig['HEADER-ICON-002-02'] || '' | svgSecurityBypass
      "
      (click)="openApprovalsScreen()"
    ></div>
    <div class="approvals-text" (click)="openApprovalsScreen()">
      {{ homePageUiConfig["HEADER-ICON-TEXT-001"] || "" }}
    </div>
  </div>
  <ng-container *ngIf="isLoading">
    <div class="empty-state">
      <img class="image" [src]="loadingGif || ''" />
      <div class="loading-wrapper">
        <div class="loading">Loading...</div>
      </div>
    </div>
  </ng-container>
  <ng-container *ngIf="!isLoading">
    <ng-container *ngIf="applications.length == 0">
      <div class="empty-state">
        <img
          class="empty-state-image"
          [src]="homePageUiConfig['INBOX-EMPTY-STATE-IMAGE'] || ''"
        />
        <div class="empty-state-text">
          {{ homePageUiConfig["INBOX-EMPTY-STATE-TEXT"] || "" }}
        </div>
      </div>
    </ng-container>
    <ng-container *ngIf="applications.length > 0">
      <div class="inbox-container">
        <div
          class="inbox-container-header"
          [ngStyle]="{
            'pointer-events':
              isLoading ||
              isListViewLoading ||
              isRejectApiInProgress ||
              isApproveApiInProgress ||
              isRejectApprovedDataApiInProgress
                ? 'none'
                : ''
          }"
        >
          <div class="inbox-container-header-1">
            <div
              *ngFor="let item of applications; let i = index"
              class="application"
              [ngClass]="{ 'selected-application': item?.isSelected }"
              [ngStyle]="{
                'margin-left': i == 0 ? '0px' : '',
                'margin-right': i == applications.length - 1 ? '0px' : ''
              }"
              (click)="switchApplication(i)"
            >
              {{ item.application_name }}
              {{ item?.count ? "(" + item?.count + ")" : "" }}
            </div>
          </div>
          <div class="inbox-container-header-2">
            <div
              (click)="switchToPendingRequest()"
              [ngClass]="{
                'selected-button': isPendingRequestSelected,
                'unselected-button': !isPendingRequestSelected
              }"
            >
              {{ homePageUiConfig["REQUEST-TEXT-001"] }}
              {{
                applications &&
                applications.length > 0 &&
                applications[currentSelectedApplicationIndex]["count"]
                  ? "(" +
                    applications[currentSelectedApplicationIndex]["count"] +
                    ")"
                  : ""
              }}
            </div>
            <div
              *ngIf="
                applications[currentSelectedApplicationIndex][
                  'header_customization'
                ]['is_previous_requests_visible']
              "
              (click)="switchToPreviousRequest()"
              [ngClass]="{
                'selected-button': !isPendingRequestSelected,
                'unselected-button': isPendingRequestSelected
              }"
            >
              {{ homePageUiConfig["REQUEST-TEXT-002"] }}
            </div>
          </div>
        </div>
        <div
          class="inbox-container-sub-header"
          [ngStyle]="{
            'pointer-events':
              isLoading ||
              isListViewLoading ||
              isRejectApiInProgress ||
              isApproveApiInProgress ||
              isRejectApprovedDataApiInProgress
                ? 'none'
                : '',
            'justify-content': countSelected ? 'space-between' : 'end'
          }"
        >
          <div *ngIf="countSelected" class="inbox-container-sub-header-1">
            <div class="request-selected-text">
              {{ countSelected }}
              {{ countSelected && countSelected > 1 ? "Requests" : "Request" }}
              Selected
            </div>
            <div class="approve-btn" (click)="onBulkApprove()">Approve</div>
          </div>
          <div class="inbox-container-sub-header-2">
            <ng-container
              *ngIf="
                applications[currentSelectedApplicationIndex][
                  'header_customization'
                ]['is_search_visible']
              "
            >
              <div *ngIf="isSearchBarVisible" class="search-ui">
                <div *ngIf="searchParams == ''">
                  <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                    <g clip-path="url(#clip0_1512_23213)">
                      <path
                        d="M12.0194 11.0767L14.8747 13.9314L13.9314 14.8747L11.0767 12.0194C10.0145 12.8708 8.69337 13.334 7.33203 13.332C4.02003 13.332 1.33203 10.644 1.33203 7.33203C1.33203 4.02003 4.02003 1.33203 7.33203 1.33203C10.644 1.33203 13.332 4.02003 13.332 7.33203C13.334 8.69337 12.8708 10.0145 12.0194 11.0767ZM10.682 10.582C11.5281 9.71196 12.0006 8.54565 11.9987 7.33203C11.9987 4.75336 9.91003 2.66536 7.33203 2.66536C4.75336 2.66536 2.66536 4.75336 2.66536 7.33203C2.66536 9.91003 4.75336 11.9987 7.33203 11.9987C8.54565 12.0006 9.71196 11.5281 10.582 10.682L10.682 10.582Z"
                        fill="#B9C0CA"
                      />
                    </g>
                    <defs>
                      <clipPath id="clip0_1512_23213">
                        <rect width="16" height="16" fill="white" />
                      </clipPath>
                    </defs>
                  </svg>
                </div>
                <div class="search-bar">
                  <input
                    id="inputSearchField"
                    type="text"
                    [(ngModel)]="searchParams"
                    placeholder="Search"
                    (keydown.enter)="onEnterSearch()"
                  />
                </div>
                <div
                  *ngIf="searchParams != ''"
                  class="icon"
                  (click)="onClearSearch()"
                >
                  <svg width="10" height="10" viewBox="0 0 10 10" fill="none">
                    <path
                      d="M1.22756 9.8355L0.164062 8.772L3.93706 4.999L0.164062 1.251L1.22756 0.1875L5.00056 3.9605L8.74856 0.1875L9.81206 1.251L6.03906 4.999L9.81206 8.772L8.74856 9.8355L5.00056 6.0625L1.22756 9.8355Z"
                      fill="#45546E"
                    />
                  </svg>
                </div>
              </div>
              <div
                *ngIf="!isSearchBarVisible"
                class="icon"
                (click)="onClickSearchIcon()"
              >
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                  <g clip-path="url(#clip0_1540_292)">
                    <path
                      d="M12.0482 11.0737L15 14.0248L14.0248 15L11.0737 12.0482C9.9757 12.9285 8.60993 13.4072 7.20262 13.4052C3.77877 13.4052 1 10.6265 1 7.20262C1 3.77877 3.77877 1 7.20262 1C10.6265 1 13.4052 3.77877 13.4052 7.20262C13.4072 8.60993 12.9285 9.9757 12.0482 11.0737ZM10.6657 10.5624C11.5404 9.66291 12.0289 8.45722 12.0269 7.20262C12.0269 4.53687 9.86768 2.37836 7.20262 2.37836C4.53687 2.37836 2.37836 4.53687 2.37836 7.20262C2.37836 9.86768 4.53687 12.0269 7.20262 12.0269C8.45722 12.0289 9.66291 11.5404 10.5624 10.6657L10.6657 10.5624Z"
                      fill="#515965"
                    />
                  </g>
                  <defs>
                    <clipPath id="clip0_1540_292">
                      <rect width="16" height="16" fill="white" />
                    </clipPath>
                  </defs>
                </svg>
              </div>
            </ng-container>
            <div
              *ngIf="
                applications[currentSelectedApplicationIndex][
                  'header_customization'
                ]['is_group_by_visible']
              "
              class="icon"
            >
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <g clip-path="url(#clip0_1512_23123)">
                  <circle cx="7.5" cy="7.5" r="3" fill="#515965" />
                  <circle cx="7.5" cy="16.5" r="3" fill="#515965" />
                  <circle cx="16.5" cy="7.5" r="3" fill="#515965" />
                  <circle cx="16.5" cy="16.5" r="3" fill="#515965" />
                </g>
                <defs>
                  <clipPath id="clip0_1512_23123">
                    <rect width="24" height="24" fill="white" />
                  </clipPath>
                </defs>
              </svg>
            </div>
            <div
              *ngIf="
                applications[currentSelectedApplicationIndex][
                  'header_customization'
                ]['is_column_customization_visible']
              "
              class="icon"
              (click)="
                openCustomizationOverlay(triggerColumnCustomizationField)
              "
              cdkOverlayOrigin
              #triggerColumnCustomization="cdkOverlayOrigin"
              #triggerColumnCustomizationField
            >
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <mask
                  id="mask0_1512_23124"
                  style="mask-type: alpha"
                  maskUnits="userSpaceOnUse"
                  x="0"
                  y="0"
                  width="24"
                  height="24"
                >
                  <rect width="24" height="24" fill="#D9D9D9" />
                </mask>
                <g mask="url(#mask0_1512_23124)">
                  <path
                    d="M5.9215 19.75C5.70892 19.75 5.53083 19.6781 5.38725 19.5343C5.24367 19.3906 5.17187 19.2125 5.17187 19V12.6923H3.92185C3.70935 12.6923 3.53123 12.6203 3.38748 12.4766C3.24374 12.3327 3.17188 12.1545 3.17188 11.942C3.17188 11.7294 3.24374 11.5513 3.38748 11.4077C3.53123 11.2641 3.70935 11.1923 3.92185 11.1923H7.92185C8.13435 11.1923 8.31247 11.2642 8.4562 11.408C8.59995 11.5518 8.67182 11.73 8.67182 11.9426C8.67182 12.1552 8.59995 12.3333 8.4562 12.4769C8.31247 12.6205 8.13435 12.6923 7.92185 12.6923H6.67182V19C6.67182 19.2125 6.59992 19.3906 6.4561 19.5343C6.3123 19.6781 6.1341 19.75 5.9215 19.75ZM5.9215 8.80765C5.70892 8.80765 5.53083 8.73578 5.38725 8.59203C5.24367 8.44829 5.17187 8.27017 5.17187 8.05768V4.99998C5.17187 4.78748 5.24377 4.60935 5.38757 4.4656C5.53139 4.32187 5.70959 4.25 5.92218 4.25C6.13478 4.25 6.31287 4.32187 6.45645 4.4656C6.60003 4.60935 6.67182 4.78748 6.67182 4.99998V8.05768C6.67182 8.27017 6.59992 8.44829 6.4561 8.59203C6.3123 8.73578 6.1341 8.80765 5.9215 8.80765ZM9.99878 8.80765C9.78627 8.80765 9.60816 8.73574 9.46443 8.59193C9.32067 8.44813 9.2488 8.26993 9.2488 8.05733C9.2488 7.84474 9.32067 7.66666 9.46443 7.52308C9.60816 7.37949 9.78627 7.3077 9.99878 7.3077H11.2488V4.99998C11.2488 4.78748 11.3207 4.60935 11.4645 4.4656C11.6083 4.32187 11.7865 4.25 11.9991 4.25C12.2117 4.25 12.3898 4.32187 12.5334 4.4656C12.677 4.60935 12.7487 4.78748 12.7487 4.99998V7.3077H13.9988C14.2113 7.3077 14.3894 7.3796 14.5331 7.5234C14.6769 7.66722 14.7488 7.84542 14.7488 8.058C14.7488 8.2706 14.6769 8.44869 14.5331 8.59228C14.3894 8.73586 14.2113 8.80765 13.9988 8.80765H9.99878ZM11.9985 19.75C11.7859 19.75 11.6078 19.6781 11.4642 19.5343C11.3206 19.3906 11.2488 19.2125 11.2488 19V11.9423C11.2488 11.7298 11.3207 11.5517 11.4645 11.4079C11.6083 11.2642 11.7865 11.1923 11.9991 11.1923C12.2117 11.1923 12.3898 11.2642 12.5334 11.4079C12.677 11.5517 12.7487 11.7298 12.7487 11.9423V19C12.7487 19.2125 12.6768 19.3906 12.533 19.5343C12.3892 19.6781 12.211 19.75 11.9985 19.75ZM18.0754 19.75C17.8628 19.75 17.6847 19.6781 17.5411 19.5343C17.3975 19.3906 17.3257 19.2125 17.3257 19V16.6923H16.0757C15.8632 16.6923 15.6851 16.6203 15.5414 16.4766C15.3976 16.3327 15.3257 16.1545 15.3257 15.942C15.3257 15.7294 15.3976 15.5513 15.5414 15.4077C15.6851 15.2641 15.8632 15.1923 16.0757 15.1923H20.0757C20.2882 15.1923 20.4663 15.2642 20.6101 15.408C20.7538 15.5518 20.8257 15.73 20.8257 15.9426C20.8257 16.1552 20.7538 16.3333 20.6101 16.4769C20.4663 16.6205 20.2882 16.6923 20.0757 16.6923H18.8257V19C18.8257 19.2125 18.7538 19.3906 18.61 19.5343C18.4662 19.6781 18.288 19.75 18.0754 19.75ZM18.0754 12.8077C17.8628 12.8077 17.6847 12.7358 17.5411 12.592C17.3975 12.4483 17.3257 12.2702 17.3257 12.0577V4.99998C17.3257 4.78748 17.3976 4.60935 17.5414 4.4656C17.6852 4.32187 17.8635 4.25 18.076 4.25C18.2886 4.25 18.4667 4.32187 18.6103 4.4656C18.7539 4.60935 18.8257 4.78748 18.8257 4.99998V12.0577C18.8257 12.2702 18.7538 12.4483 18.61 12.592C18.4662 12.7358 18.288 12.8077 18.0754 12.8077Z"
                    fill="#45546E"
                  />
                </g>
              </svg>
            </div>
            <div
              *ngIf="
                applications[currentSelectedApplicationIndex][
                  'header_customization'
                ]['is_filter_visible']
              "
              (click)="openFilterDialog()"
              class="icon"
            >
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                <path
                  d="M7.38573 15.5C7.13445 15.5 6.92419 15.4153 6.75496 15.2461C6.58573 15.0769 6.50111 14.8666 6.50111 14.6153V8.82688L0.903059 1.71538C0.710759 1.45896 0.682876 1.19229 0.819409 0.915376C0.955942 0.63846 1.18638 0.5 1.51073 0.5H14.4914C14.8158 0.5 15.0462 0.63846 15.1828 0.915376C15.3193 1.19229 15.2914 1.45896 15.0991 1.71538L9.50106 8.82688V14.6153C9.50106 14.8666 9.41644 15.0769 9.24721 15.2461C9.07798 15.4153 8.86772 15.5 8.61643 15.5H7.38573ZM8.00108 8.29998L12.9511 1.99998H3.05108L8.00108 8.29998Z"
                  fill="#45546E"
                />
              </svg>
            </div>
          </div>
        </div>
        <div
          class="filter-display"
          *ngIf="currentFilterSubApplicationId && !isListViewLoading"
        >
          <app-filter-display
            [applicationId]="currentSelectedApplicationId"
            [internalApplicationId]="currentFilterSubApplicationId"
          ></app-filter-display>
        </div>
        <ng-container *ngIf="isListViewLoading">
          <div class="list-view-empty-state">
            <img class="loading-image" [src]="loadingGif || ''" />
            <div class="loading-wrapper">
              <div class="loading">Loading...</div>
            </div>
          </div>
        </ng-container>
        <ng-container *ngIf="!isListViewLoading">
          <ng-container *ngIf="currentData.length == 0">
            <div class="list-view-empty-state">
              <img
                class="empty-state-image"
                [src]="homePageUiConfig['INBOX-APP-EMPTY-STATE-IMAGE'] || ''"
              />
              <div class="empty-state-text">
                {{ homePageUiConfig["INBOX-APP-EMPTY-STATE-TEXT-001"] || "" }}
              </div>
              <div class="empty-state-sub-text">
                {{ homePageUiConfig["INBOX-APP-EMPTY-STATE-TEXT-002"] || "" }}
              </div>
            </div>
          </ng-container>
          <ng-container *ngIf="currentData.length > 0">
            <div
              class="list-view"
              infinite-scroll
              [infiniteScrollDistance]="3"
              [infiniteScrollThrottle]="100"
              (scrolled)="onDataScroll()"
              [scrollWindow]="false"
            >
              <div class="header-sticky">
                <div class="row header-row-main">
                  <ng-container
                    *ngFor="let item of currentColumnConfig; let i = index"
                  >
                    <div
                      *ngIf="item.is_visible"
                      class="header-row"
                      [class]="
                        item?.is_pinned_left
                          ? 'header-row-left-pin'
                          : item?.is_pinned_right
                          ? 'header-row-right-pin'
                          : ''
                      "
                      [ngStyle]="{
                        'min-width': item.width,
                        'max-width': item.width,
                        left: item?.is_pinned_left
                          ? (currentColumnConfig
                            | calculatePositionOfPinnedColumns : i : 'left')
                          : '',
                        right: item?.is_pinned_right
                          ? (currentColumnConfig
                            | calculatePositionOfPinnedColumns : i : 'right')
                          : '',
                        'border-left':
                          item?.is_pinned_right &&
                          !currentColumnConfig[i - 1]?.is_pinned_right
                            ? '1px solid #E8E9EE'
                            : '',
                        'border-right':
                          item?.is_pinned_left &&
                          !currentColumnConfig[i + 1]?.is_pinned_left
                            ? '1px solid #E8E9EE'
                            : ''
                      }"
                    >
                      <div class="header-row-content">
                        <div
                          *ngIf="
                            isPendingRequestSelected &&
                            i == 0 &&
                            applications[currentSelectedApplicationIndex][
                              'header_customization'
                            ]['is_bulk_approve_allowed']
                          "
                          class="checkbox"
                          [ngStyle]="{
                            'pointer-events':
                              isLoading ||
                              isListViewLoading ||
                              isRejectApiInProgress ||
                              isApproveApiInProgress ||
                              isRejectApprovedDataApiInProgress
                                ? 'none'
                                : ''
                          }"
                        >
                          <mat-checkbox
                            [(ngModel)]="pendingCheckAll"
                            (ngModelChange)="onChangePendingCheckAll()"
                          ></mat-checkbox>
                        </div>
                        <span
                          *ngIf="
                            !(
                              item?.is_search_active &&
                              item?.is_search_icon_clicked
                            )
                          "
                          class="list-title"
                          [matTooltip]="item.label"
                        >
                          {{ item.label }}
                        </span>
                        <div class="d-flex align-items-center">
                          <div
                            *ngIf="item.is_sort_active && item.sort_order == 0"
                            class="svg"
                            (click)="onClickSort(1, i)"
                          >
                            <svg
                              width="12"
                              height="12"
                              viewBox="0 0 12 12"
                              fill="none"
                            >
                              <g clip-path="url(#clip0_1314_18487)">
                                <path
                                  d="M6 4.5H4.0005L4 10.5H3V4.5H1L3.5 2L6 4.5ZM11 8.5L8.5 11L6 8.5H8V2.5H9V8.5H11Z"
                                  fill="#6E7B8F"
                                />
                              </g>
                              <defs>
                                <clipPath id="clip0_1314_18487">
                                  <rect width="12" height="12" fill="white" />
                                </clipPath>
                              </defs>
                            </svg>
                          </div>
                          <div
                            *ngIf="item.is_sort_active && item.sort_order == 1"
                            class="svg"
                            (click)="onClickSort(2, i)"
                          >
                            <svg
                              height="10px"
                              width="8px"
                              fill="#6E7B8F"
                              version="1.1"
                              id="Layer_1"
                              viewBox="0 0 512 512"
                              enable-background="new 0 0 512 512"
                              xml:space="preserve"
                            >
                              <polygon
                                points="245,0 74.3,213.3 202.3,213.3 202.3,512 287.7,512 287.7,213.3 415.7,213.3 "
                              />
                            </svg>
                          </div>
                          <div
                            *ngIf="item.is_sort_active && item.sort_order == 2"
                            class="svg"
                            (click)="onClickSort(0, i)"
                          >
                            <svg
                              height="10px"
                              width="8px"
                              fill="#6E7B8F"
                              version="1.1"
                              id="Layer_1"
                              viewBox="0 0 512 512"
                              enable-background="new 0 0 512 512"
                              xml:space="preserve"
                            >
                              <polygon
                                points="283.7,298.7 283.7,0 198.3,0 198.3,298.7 70.3,298.7 241,512 411.7,298.7 "
                              />
                            </svg>
                          </div>
                        </div>
                      </div>
                    </div>
                  </ng-container>
                </div>
              </div>
              <ng-container
                *ngFor="let item of currentData; let itemIndex = index"
              >
                <div class="content-sticky">
                  <div class="row content-row-main">
                    <ng-container
                      *ngFor="
                        let field of currentColumnConfig;
                        let fieldIndex = index
                      "
                    >
                      <div
                        *ngIf="field.is_visible"
                        class="content-row"
                        [class]="
                          field?.is_pinned_left
                            ? 'content-row-left-pin'
                            : field?.is_pinned_right
                            ? 'content-row-right-pin'
                            : ''
                        "
                        [ngStyle]="{
                          'min-width': field.width,
                          'max-width': field.width,
                          left: field?.is_pinned_left
                            ? (currentColumnConfig
                              | calculatePositionOfPinnedColumns
                                : fieldIndex
                                : 'left')
                            : '',
                          right: field?.is_pinned_right
                            ? (currentColumnConfig
                              | calculatePositionOfPinnedColumns
                                : fieldIndex
                                : 'right')
                            : '',
                          'border-left':
                            field?.is_pinned_right &&
                            !currentColumnConfig[fieldIndex - 1]
                              ?.is_pinned_right
                              ? '1px solid #E8E9EE'
                              : '',
                          'border-right':
                            field?.is_pinned_left &&
                            !currentColumnConfig[fieldIndex + 1]?.is_pinned_left
                              ? '1px solid #E8E9EE'
                              : '',
                          'justify-content':
                            field?.column_type == 'currency' ? 'end' : 'start',
                          'padding-right':
                            field?.column_type == 'currency' ? '10px' : '',
                          cursor: field.click_actions ? 'pointer' : ''
                        }"
                        (click)="
                          columnClickAction(
                            field.click_actions,
                            item,
                            item[field.click_actions.column_key]
                          )
                        "
                      >
                        <div
                          *ngIf="
                            isPendingRequestSelected &&
                            fieldIndex == 0 &&
                            applications[currentSelectedApplicationIndex][
                              'header_customization'
                            ]['is_bulk_approve_allowed']
                          "
                          class="checkbox"
                          [ngStyle]="{
                            'pointer-events':
                              isLoading ||
                              isListViewLoading ||
                              isRejectApiInProgress ||
                              isApproveApiInProgress ||
                              isRejectApprovedDataApiInProgress
                                ? 'none'
                                : ''
                          }"
                        >
                          <mat-checkbox
                            [(ngModel)]="item.isChecked"
                            (ngModelChange)="onIndividualCheckboxSelected()"
                            (click)="stopParentPropagation($event); onIndividualCheckboxSelected()"
                          ></mat-checkbox>
                        </div>
                        <ng-container *ngIf="field.column_type == 'text'">
                          <span
                            class="normal-text"
                            [matTooltip]="item[field.column_key['0']] || '-'"
                          >
                            {{ item[field.column_key["0"]] || "-" }}
                          </span>
                        </ng-container>
                        <ng-container
                          *ngIf="field.column_type == 'time-format'"
                        >
                          <span
                            class="normal-text"
                            [matTooltip]="
                              item[field.column_key['0']] | hoursWorkedSplit
                            "
                          >
                            {{ item[field.column_key["0"]] | hoursWorkedSplit }}
                          </span>
                        </ng-container>
                        <ng-container *ngIf="field.column_type == 'text-color'">
                          <span
                            class="normal-text"
                            [matTooltip]="item[field.column_key['0']] || '-'"
                            [ngStyle]="{ color: item[field.column_key['1']] }"
                          >
                            {{ item[field.column_key["0"]] || "-" }}
                          </span>
                        </ng-container>
                        <ng-container *ngIf="field.column_type == 'number'">
                          <span
                            class="normal-text"
                            [matTooltip]="
                              [null, undefined, false].includes(
                                item[field.column_key['0']]
                              )
                                ? '-'
                                : item[field.column_key['0']]
                            "
                          >
                            {{
                              [null, undefined, false].includes(
                                item[field.column_key["0"]]
                              )
                                ? "-"
                                : item[field.column_key["0"]]
                            }}
                          </span>
                        </ng-container>
                        <ng-container *ngIf="field.column_type == 'date'">
                          <span
                            class="normal-text"
                            [matTooltip]="
                              item[field.column_key['0']]
                                | dateFormat
                                  : field.column_key['1'] || 'DD MMM YYYY'
                            "
                          >
                            {{
                              item[field.column_key["0"]]
                                | dateFormat
                                  : field.column_key["1"] || "DD MMM YYYY"
                            }}
                          </span>
                        </ng-container>
                        <ng-container *ngIf="field.column_type == 'chip'">
                          <div
                            class="chip"
                            [ngStyle]="{
                              'background-color': item[field.column_key['1']],
                              color: item[field.column_key['2']]
                            }"
                            [matTooltip]="item[field.column_key['0']] || '-'"
                          >
                            {{ item[field.column_key["0"]] || "-" }}
                          </div>
                        </ng-container>
                        <ng-container *ngIf="field.column_type == 'hyperlink'">
                          <span
                            *ngIf="
                              item[field.column_key['0']] &&
                              item[field.column_key['1']]
                            "
                            class="hyperlink-text"
                            [matTooltip]="item[field.column_key['0']]"
                            (click)="openHyperLink(item[field.column_key['1']])"
                          >
                            {{ item[field.column_key["0"]] }}
                          </span>
                          <span
                            *ngIf="!item[field.column_key['0']]"
                            class="normal-text"
                          >
                            -
                          </span>
                        </ng-container>
                        <ng-container *ngIf="field.column_type == 'date-range'">
                          <span
                            class="normal-text"
                            [matTooltip]="
                              item[field.column_key['0']]
                                | dateFormat
                                  : field.column_key['2'] ||
                                      'DD MMM YYYY' +
                                        ' - ' +
                                        item[field.column_key['1']]
                                | dateFormat
                                  : field.column_key['2'] || 'DD MMM YYYY'
                            "
                          >
                            {{
                              item[field.column_key["0"]]
                                | dateFormat
                                  : field.column_key["2"] || "DD MMM YYYY"
                            }}
                            -
                            {{
                              item[field.column_key["1"]]
                                | dateFormat
                                  : field.column_key["2"] || "DD MMM YYYY"
                            }}
                          </span>
                        </ng-container>
                        <ng-container
                          *ngIf="field.column_type == 'employee-display'"
                        >
                          <app-user-image
                            style="padding-right: 8px"
                            [oid]="item[field.column_key['1']]"
                            imgWidth="24px"
                            imgHeight="24px"
                          ></app-user-image>
                          <span
                            class="normal-text"
                            [matTooltip]="item[field.column_key['0']] || '-'"
                          >
                            {{ item[field.column_key["0"]] || "-" }}
                          </span>
                        </ng-container>
                        <ng-container
                          *ngIf="field.column_type == 'employee-display-aid'"
                        >
                          <app-user-image
                            style="padding-right: 8px"
                            [oid]="item[field.column_key['3']]"
                            imgWidth="24px"
                            imgHeight="24px"
                          ></app-user-image>
                          <div
                            class="d-flex flex-column"
                            style="width: 70%"
                          >
                            <span
                              class="employee-text"
                              [matTooltip]="item[field.column_key['0']] || '-'"
                            >
                              {{ item[field.column_key["0"]] || "-" }}
                            </span>
                            <span
                              class="employee-aid"
                              [matTooltip]="
                                field.column_key['2'] +
                                ' ' +
                                (item[field.column_key['1']] || '-')
                              "
                            >
                              {{ field.column_key["2"] }}
                              {{ item[field.column_key["1"]] || "-" }}
                            </span>
                          </div>
                        </ng-container>
                        <ng-container *ngIf="field.column_type == 'actions'">
                          <div class="actions">
                            <ng-container
                              *ngFor="
                                let icon of field.click_actions;
                                let iconIndex = index
                              "
                            >
                              <div
                                *ngIf="
                                  !icon?.showLoader ||
                                  icon?.showLoader == false ||
                                  (icon?.showLoader == true &&
                                    !item[
                                      field?.id + '_' + iconIndex + '_loader'
                                    ])
                                "
                              >
                                <div
                                  style="cursor: pointer"
                                  *ngIf="
                                    (icon.type == 'svg' &&
                                      icon.action_type == 'hyperlink' &&
                                      item[icon.click_action]) ||
                                    (icon.type == 'svg' &&
                                      icon.action_type != 'hyperlink')
                                  "
                                  [innerHTML]="icon.icon | svgSecurityBypass"
                                  [matTooltip]="icon.label"
                                  (click)="
                                    onClickActionsIcon(
                                      item,
                                      icon.action_type,
                                      icon.click_action,
                                      icon?.file_name,
                                      itemIndex,
                                      fieldIndex,
                                      iconIndex
                                    )
                                  "
                                ></div>
                              </div>
                              <div
                                *ngIf="
                                  icon?.showLoader == true &&
                                  item[field?.id + '_' + iconIndex + '_loader']
                                "
                                class="ml-2"
                              >
                                <div
                                  class="spinner-border spinner-border-sm spinner"
                                  role="status"
                                >
                                  <span class="sr-only">Loading...</span>
                                </div>
                              </div>
                              <div
                                *ngIf="
                                  !icon?.showLoader ||
                                  icon?.showLoader == false ||
                                  (icon?.showLoader == true &&
                                    !item[
                                      field?.id + '_' + iconIndex + '_loader'
                                    ])
                                "
                              >
                                <div
                                  *ngIf="
                                    (icon.type == 'mat-icon' &&
                                      icon.action_type == 'hyperlink' &&
                                      item[icon.click_action]) ||
                                    (icon.type == 'mat-icon' &&
                                      icon.action_type != 'hyperlink')
                                  "
                                >
                                  <mat-icon
                                    style="cursor: pointer"
                                    [ngStyle]="{
                                      'font-size': icon.font_size,
                                      width: icon.font_size,
                                      height: icon.font_size,
                                      color: icon.color
                                    }"
                                    [matTooltip]="icon.label"
                                    (click)="
                                      onClickActionsIcon(
                                        item,
                                        icon.action_type,
                                        icon.click_action,
                                        icon?.file_name,
                                        itemIndex,
                                        fieldIndex,
                                        iconIndex
                                      )
                                    "
                                  >
                                    {{ icon.icon }}
                                  </mat-icon>
                                </div>
                              </div>
                            </ng-container>
                          </div>
                        </ng-container>
                        <ng-container *ngIf="field.column_type == 'currency'">
                          <span
                            class="normal-text"
                            [matTooltip]="
                              item[field.column_key['1']] &&
                              item[field.column_key['0']]
                                ? item[field.column_key['1']] +
                                  ' ' +
                                  item[field.column_key['0']]
                                : '-'
                            "
                          >
                            {{ item[field.column_key["1"]] }}
                            {{
                              item[field.column_key["0"]]
                                | currencyFormat
                                  : item[field.column_key["1"]]
                                  : field.column_key["2"]
                            }}
                          </span>
                        </ng-container>
                        <ng-container
                          *ngIf="field.column_type == 'approve-reject-button'"
                        >
                          <div class="approve-reject-buttons">
                            <div
                              class="reject-btn"
                              [ngClass]="{
                                'disable-btn':
                                  pendingCheckAll ||
                                  countSelected ||
                                  isRejectApiInProgress ||
                                  isApproveApiInProgress
                              }"
                              (click)="onSingleDataReject(itemIndex)"
                            >
                              Reject
                            </div>
                            <div
                              class="approve-btn"
                              [ngClass]="{
                                'disable-btn':
                                  pendingCheckAll ||
                                  countSelected ||
                                  isRejectApiInProgress ||
                                  isApproveApiInProgress
                              }"
                              (click)="onSingleDataApprove(itemIndex)"
                            >
                              Approve
                            </div>
                          </div>
                        </ng-container>
                        <ng-container
                          *ngIf="
                            field.column_type == 'reject-approved-item-button'
                          "
                        >
                          <mat-icon
                            [matMenuTriggerFor]="rejectMenu"
                            *ngIf="item?.status === 'Approved'"
                            class="reject-more-vert"
                            [ngStyle]="{
                              'pointer-events':
                                isLoading ||
                                isListViewLoading ||
                                isRejectApiInProgress ||
                                isApproveApiInProgress ||
                                isRejectApprovedDataApiInProgress
                                  ? 'none'
                                  : '',
                              opacity:
                                isLoading ||
                                isListViewLoading ||
                                isRejectApiInProgress ||
                                isApproveApiInProgress ||
                                isRejectApprovedDataApiInProgress
                                  ? 0.3
                                  : null
                            }"
                          >
                            more_vert
                          </mat-icon>
                          <mat-menu
                            #rejectMenu="matMenu"
                            yPosition="below"
                            xPosition="before"
                            class="custom-menu"
                          >
                            <button
                              class="menu-item"
                              [ngStyle]="{
                                'border-bottom': 'none'
                              }"
                              mat-menu-item
                              (click)="onRejectApprovedItem(itemIndex)"
                            >
                              Reject
                            </button>
                          </mat-menu>
                        </ng-container>
                        <ng-container *ngIf="field.column_type == 'approvers'">
                          <span *ngIf="item[field.column_key['0']]">
                            <app-people-icon-display
                              [peopleList]="item[field.column_key['0']]"
                              [count]="item[field.column_key['0']]?.length"
                            >
                            </app-people-icon-display>
                          </span>
                        </ng-container>
                      </div>
                    </ng-container>
                  </div>
                </div>
              </ng-container>
            </div>
          </ng-container>
        </ng-container>
      </div>
    </ng-container>
  </ng-container>
</div>

<!-- Column Customization Overlay -->
<ng-template
  #triggerColumnCustomizationTemplateRef
  cdkConnectedOverlay
  [cdkConnectedOverlayOrigin]="triggerColumnCustomization"
>
  <app-inbox-column-customization
    [customization]="currentColumnConfig"
    (onApply)="onApplyColumnCustomization($event)"
  ></app-inbox-column-customization>
</ng-template>
