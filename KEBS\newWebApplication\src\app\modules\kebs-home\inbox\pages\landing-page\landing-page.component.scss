.bg-container {
  background-color: #fff;
  height: var(--homePageDynamicHeight);
}

.header {
  display: flex;
  align-items: center;
  height: 36px;
  padding: 6px 0px 6px 24px;
  gap: 16px;
  background: linear-gradient(90deg, #ffffff 0%, #fbdbe1 100%);

  .bar-text {
    height: 100%;
    width: 0.5px;
    background: #d4d6d8;
  }

  .approvals-text {
    font-family: var(--kebsFontFamily);
    font-size: 12px;
    font-weight: 400;
    color: var(--kebsPrimaryColor);
    cursor: pointer;
  }

  .svg {
    cursor: pointer;
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
  height: var(--homePageInboxDynamicHeight);

  .empty-state-image {
    width: 50%;
    height: 50%;
  }

  .image {
    height: 60px;
    width: 60px;
  }

  .loading-wrapper {
    display: flex;
    vertical-align: center;
    justify-content: center;
    align-items: center;
  }

  .loading {
    color: rgba(0, 0, 0, 0.3);
    font-size: 16px;
  }

  .loading::before {
    content: "Loading...";
    position: absolute;
    overflow: hidden;
    max-width: 7em;
    white-space: nowrap;
    background: linear-gradient(
      270deg,
      var(--kebsPrimaryColor) 0%,
      var(--kebsPrimaryColor) 105.29%
    );
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: loading 6s linear infinite;
  }

  @keyframes loading {
    0% {
      max-width: 0;
    }
  }

  .empty-state-text {
    font-family: var(--kebsFontFamily);
    font-size: 12px;
    font-weight: 700;
    color: #45546e;
  }
}

.list-view-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
  height: var(--homePageInboxListDynamicHeight);

  .empty-state-image {
    height: calc(var(--homePageInboxListDynamicHeight) - 200px);
    width: calc(var(--homePageInboxListDynamicHeight) - 200px +((var(--homePageInboxListDynamicHeight) - 200px) / 2));
  }

  .loading-image {
    height: 60px;
    width: 60px;
  }

  .loading-wrapper {
    display: flex;
    vertical-align: center;
    justify-content: center;
    align-items: center;
  }

  .loading {
    color: rgba(0, 0, 0, 0.3);
    font-size: 16px;
  }

  .loading::before {
    content: "Loading...";
    position: absolute;
    overflow: hidden;
    max-width: 7em;
    white-space: nowrap;
    background: linear-gradient(
      270deg,
      var(--kebsPrimaryColor) 0%,
      var(--kebsPrimaryColor) 105.29%
    );
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: loading 6s linear infinite;
  }

  @keyframes loading {
    0% {
      max-width: 0;
    }
  }

  .empty-state-text {
    font-family: var(--kebsFontFamily);
    font-size: 14px;
    font-weight: 500;
    color: #1b2140;
  }

  .empty-state-sub-text {
    font-family: var(--kebsFontFamily);
    font-size: 14px;
    font-weight: 400;
    color: #8b95a5;
  }
}

.inbox-container {
  display: flex;
  flex-direction: column;
  padding: 12px 24px;
  height: var(--homePageInboxDynamicHeight);
  gap: 8px;

  .inbox-container-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 36px;

    .inbox-container-header-1 {
      display: flex;
      align-items: center;
      border-bottom: 0.5px solid #e8e9ee;
      margin-left: 24px;

      .application {
        font-family: var(--kebsFontFamily);
        font-size: 14px;
        font-weight: 500;
        color: #8b95a5;
        padding-bottom: 4px;
        margin: 0px 12px;
        cursor: pointer;
      }

      .selected-application {
        border-bottom: 2px solid var(--kebsPrimaryColor);
        color: var(--kebsPrimaryColor);
      }
    }

    .inbox-container-header-2 {
      display: flex;
      align-items: center;
      gap: 10px;

      .unselected-button {
        font-family: var(--kebsFontFamily);
        font-size: 12px;
        font-weight: 500;
        color: #8b95a5;
        padding: 6px 12px;
        background-color: #fff;
        border-radius: 8px;
        cursor: pointer;
      }

      .selected-button {
        font-family: var(--kebsFontFamily);
        font-size: 12px;
        font-weight: 500;
        color: #ffffff;
        padding: 6px 12px;
        background-color: var(--kebsPrimaryColor);
        border-radius: 8px;
      }
    }
  }

  .inbox-container-sub-header {
    display: flex;
    align-items: center;
    height: 36px;

    .inbox-container-sub-header-1 {
      display: flex;
      align-items: center;
      gap: 16px;

      .request-selected-text {
        font-family: var(--kebsFontFamily);
        font-size: 14px;
        font-weight: 700;
        color: #1b2140;
      }

      .approve-btn {
        font-family: var(--kebsFontFamily);
        font-size: 14px;
        font-weight: 700;
        color: #fff;
        border-radius: 4px;
        padding: 6px 16px;
        cursor: pointer;
        background-color: #52c41a;
      }
    }

    .inbox-container-sub-header-2 {
      display: flex;
      align-items: center;
      gap: 16px;
    }

    .icon {
      cursor: pointer;
    }

    .search-ui {
      display: flex;
      align-items: center;
      width: 300px;
      height: 36px;
      padding: 0px 12px;
      border: 1px solid #dadce2;
      border-radius: 8px;
      gap: 8px;
      cursor: text;

      .search-bar {
        width: -webkit-fill-available;

        input {
          height: 100%;
          width: 100%;
          font-family: var(--kebsFontFamily);
          font-size: 12px;
          font-weight: 400;
          color: #45546e;
          outline: none;
          border: none;
        }

        input::placeholder {
          font-family: var(--kebsFontFamily);
          font-size: 12px;
          font-weight: 400;
          color: #b9c0ca;
        }
      }
    }
  }

  .filter-display {
    width: -webkit-fill-available;
  }
}

.list-view {
  display: flex;
  flex-direction: column;
  overflow: auto;
  height: var(--homePageInboxListDynamicHeight);

  .header-sticky {
    position: sticky;
    top: 0;
    z-index: 99;
  }

  .header-row-main {
    flex-wrap: nowrap;
    float: left;
    min-width: -webkit-fill-available;
    -ms-overflow-style: none;
    scrollbar-width: none;
    overflow: unset;

    .header-row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      min-height: 32px;
      background: white;
      border-bottom: 0.5px solid #e8e9ee;
      padding-left: 15px;
      background: #f2f3f6;

      .header-row-content {
        display: flex;
        align-items: center;
        width: 98%;
        gap: 8px;
      }

      .list-title {
        font-family: var(--kebsFontFamily);
        font-size: 12px;
        font-weight: 400;
        color: #5f6c81;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .svg {
        cursor: pointer;
      }

      .vertical-divider-wrapper {
        width: 2%;
        cursor: ew-resize;
      }

      .vertical-divider {
        display: flex;
        align-items: center;
        justify-content: end;
        color: #dadce2;
        border-right-width: 2px;
        height: 20px;
      }
    }

    .header-row-left-pin {
      position: sticky;
      z-index: 10;
    }

    .header-row-right-pin {
      position: sticky;
      z-index: 10;
    }
  }

  .content-sticky {
    z-index: 1;
    position: sticky;
  }

  .content-row-main {
    flex-wrap: nowrap;
    float: left;
    min-width: -webkit-fill-available;
    transition: all 0.25s linear;
    height: auto;

    .content-row {
      display: flex;
      align-items: center;
      min-height: 48px;
      border-bottom: 0.5px solid #d4d6d8;
      padding-left: 15px;
      background-color: white;

      .normal-text {
        font-family: var(--kebsFontFamily);
        font-size: 12px;
        font-weight: 400;
        color: #272a47;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
      }

      .employee-text {
        font-family: var(--kebsFontFamily);
        font-size: 12px;
        font-weight: 500;
        color: #272a47;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
      }

      .employee-aid {
        font-family: var(--kebsFontFamily);
        font-size: 12px;
        font-weight: 400;
        color: #7d838b;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
      }

      .hyperlink-text {
        font-family: var(--kebsFontFamily);
        font-size: 14px;
        font-weight: 400;
        color: #1890ff;
        text-decoration: underline;
        cursor: pointer;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
      }

      .chip {
        border-radius: 2px;
        text-align: center;
        padding: 4px 12px 4px 12px;
        font-family: var(--kebsFontFamily);
        font-size: 12px;
        font-weight: 500;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
      }

      .actions {
        display: flex;
        align-items: center;
        gap: 8px;

        .svg {
          cursor: pointer;
        }
      }

      .approve-reject-buttons {
        display: flex;
        align-items: center;
        gap: 8px;

        .reject-btn {
          font-family: var(--kebsFontFamily);
          font-size: 12px;
          font-weight: 400;
          color: #45546e;
          border: 1px solid #45546e;
          border-radius: 4px;
          padding: 3px 7px;
          cursor: pointer;
        }

        .approve-btn {
          font-family: var(--kebsFontFamily);
          font-size: 12px;
          font-weight: 400;
          color: #fff;
          border-radius: 4px;
          padding: 4px 8px;
          cursor: pointer;
          background-color: #52c41a;
        }

        .disable-btn {
          background-color: #e8e9ee;
          color: #b9c0ca;
          padding: 4px 8px;
          border: none;
          pointer-events: none;
        }
      }
    }

    .content-row-left-pin {
      position: sticky;
      z-index: 3;
    }

    .content-row-right-pin {
      position: sticky;
      z-index: 3;
    }
  }
}

.spinner {
  color: var(--kebsPrimaryColor);
  width: 20px;
  height: 20px;
  font-weight: 100;
}

.reject-more-vert {
  color: #45546e;
  cursor: pointer;
}

.reject-menu {
  overflow: hidden;
  padding: 0px 16px;

  .reject-menu-item {
    font-family: var(--kebsFontFamily);
    cursor: pointer;
    font-size: 11px;
    font-weight: 400;
    color: #6e7b8f;
    margin: 0;
  }
}

.menu-item {
  font-family: var(--kebsFontFamily);
  font-size: 12px;
  font-weight: 400;
  color: #526179;
  border-bottom: 1px solid #e8e9ee;
  height: 48px;
  display: flex;
  align-items: center;
}

.checkbox {
  width: 16px;
  height: 16px;
  margin-right: 8px;

  ::ng-deep .mat-checkbox-checked.mat-accent .mat-checkbox-background {
    background-color: var(--kebsPrimaryColor) !important;
  }

  ::ng-deep .mat-checkbox-indeterminate .mat-checkbox-background {
    background-color: var(--kebsPrimaryColor) !important;
  }

  ::ng-deep .mat-checkbox-inner-container {
    width: 16px !important;
    height: 16px !important;
  }

  ::ng-deep .mat-checkbox-frame {
    border-color: #dadce2 !important;
  }
}
