import { Pipe, PipeTransform } from '@angular/core';
import * as moment from 'moment';

@Pipe({
  name: 'dateFormat',
})
export class DateFormatPipe implements PipeTransform {
  transform(value: any, format: string, isLocalValue: boolean = true): string {
    if (value) {
      if (isLocalValue) {
        return moment(value, 'YYYY-MM-DD HH:mm:ss')
          .utc(value)
          .local()
          .format(format);
      } else {
        return moment(value).format(format);
      }
    } else {
      return '-';
    }
  }
}
