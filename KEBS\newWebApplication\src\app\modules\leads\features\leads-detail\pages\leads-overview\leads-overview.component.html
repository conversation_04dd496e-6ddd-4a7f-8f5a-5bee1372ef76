<div class="container-fluid leads-overview-styles pt-2">
    <div class="row mt-2 pl-2 pb-2">
      <div class="col-5 pl-2">
        <span class="tileName">
          Leads Info
        </span>
      </div>
      <div class="col-7 pl-3 d-flex">
        <span class="tileName">
          Requirement
        </span>

        
        <button mat-icon-button matTooltip="View more" class="more-button ml-auto" [matMenuTriggerFor]="options">
          <mat-icon class="smallCardIcon">more_vert</mat-icon>
        </button>
        <mat-menu #options="matMenu">
          <button mat-menu-item class="drop-btn" (click)="editLead()">
              <mat-icon class="smallCardIcon">edit</mat-icon> <span>Edit Lead</span>
          </button>
          <button mat-menu-item class="drop-btn" (click)="viewLineofBusiness()">
            <mat-icon class="smallCardIcon">business_center</mat-icon> <span>View heirarchy</span>
          </button>
        </mat-menu>
      </div>
    </div>
    <div class="row">
      <div class="col-5 pr-0">
        <div class="row">
          <div class="col-12 pl-0">
            <ng-container *ngIf="this.leadDetails">
              <div class="card" [ngClass]="[
            'salesStatusBorder_' + this.leadDetails.status_id
              ]" style="min-height: 510px !important;">
                <div class="card-body pt-3 pl-2 pr-2 pb-2">
                  <div class="row pt-2 pb-1">
                    <div class="col-12">
                      <div class="card d-flex slide-in-top">
                        <div class="card-body p-2">
                          <div class="row">
                            <div class="col-3" style="border-right: 1px solid #d6d6d6;">
                              <div class="title mx-auto pt-2">
                                -
                              </div>
                              <div class="sub-heading pb-2 pt-1">
                                Contacts
                              </div>
                            </div>
                            <div class="col-3" style="border-right: 1px solid #d6d6d6;">
                              <div class="title mx-auto pt-2">
                                {{this.leadDetails.meetings_count}}
                              </div>
                              <div class="sub-heading pb-2 pt-1">
                                Meetings
                              </div>
                            </div>
                            <div class="col-3" style="border-right: 1px solid #d6d6d6;">
                              <div class="title mx-auto pt-2">
  
                                {{this.leadDetails.call_log_count}}
                              </div>
                              <div class="sub-heading pb-2 pt-1">
                                Call log
                              </div>
                            </div>
                            <div class="col-3">
                              <div class="title mx-auto pt-2">
  
                                {{this.leadDetails.mail_count}}
                              </div>
                              <div class="sub-heading pb-2 pt-1">
                                Mail
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="row pl-3 pt-3 slide-from-down">
                    <div class="col-12 pl-0">
                      <div class="card leads-overview-card-bg slide-in-top">
                          <div class="card-body p-2">
                              <div class="row">
                                  <div class="col-12 pl-4">
                                      <div class="row leads-name" style="cursor: pointer;" 
                                      interActiveInlineEdit (click)="activateInlineEdit('Lead name', 'simple-text',[],leadDetails)" 
                                      [matTooltip]="getLength(this.leadDetails.lead_name) > 30? this.leadDetails.lead_name: ''">
                                        {{this.leadDetails.lead_name?this.leadDetails.lead_name:'-'}}
                                        
                                      </div>
                                      <div class="row pl-0 pt-1"  *ngIf="'leadValueUSD' | tenantLead: formFieldData : 'isActive'">
                                        <div class="col-6 leads-info-title pl-0">
                                          {{'leadValueUSD' | tenantLead: formFieldData : 'label'}}
                                        </div>
                                        <div class="col-6 pl-0 leads-info-title">
                                          <app-currency
                                          [currencyList]="this.leadDetails?this.leadDetails.lead_value:''"
                                          class="flex-1" type="small"></app-currency>
                                        </div>
                                      </div>
              
                                      <div class="row pl-0 pt-1" *ngIf="'inAccount' | tenantLead: formFieldData : 'isActive'">
                                          <div class="col-6 leads-info-title pl-0">
                                              {{'inAccount' | tenantLead: formFieldData : 'label'}}
                                          </div>
                                          <div class="col-6 pl-0 leads-info-in-account" style="cursor: pointer;" (click)="navigateToAccounts()">
                                            {{this.leadDetails.account_name?this.leadDetails.account_name:'-'}}
                                          </div>
                                      </div>
                                      
                                      <div class="row pl-0 pt-1" *ngIf="'leadSource' | tenantLead: formFieldData : 'isActive'">
                                        <div class="col-6 leads-info-title pl-0">
                                            {{'leadSource' | tenantLead: formFieldData : 'label'}}
                                        </div>
                                        <div class="col-6 pl-0 leads-info-details " style="cursor: pointer;"  interActiveInlineEdit (click)="activateInlineEdit('Lead source', 'minimal-dropdown',leadSourceMaster,'')">
                                          {{this.leadDetails.lead_source_name?this.leadDetails.lead_source_name:'-'}}
                                        </div>
                                    </div>

                                      <div class="row pl-0 pt-1" *ngIf="'leadScore' | tenantLead: formFieldData : 'isActive'">
                                          <div class="col-6 leads-info-title pl-0">
                                            {{'leadScore' | tenantLead: formFieldData : 'label'}}
                                          </div>
                                          <div class="col-6 pl-0 leads-info-details " style="cursor: pointer;"  interActiveInlineEdit (click)="activateInlineEdit('Lead score', 'minimal-dropdown',leadScoreMaster,'')" >
                                            {{this.leadDetails.leads_score?this.leadDetails.leads_score:'-'}}
                                          </div>
                                      </div>
                                      <div class="row pl-0 pt-1" *ngIf="'leadsChannel' | tenantLead: formFieldData : 'isActive'">
                                          <div class="col-6 leads-info-title pl-0">
                                            {{'leadsChannel' | tenantLead: formFieldData : 'label'}}
                                          </div>
                                          <div class="col-6 pl-0 leads-info-details" style="cursor: pointer;"  interActiveInlineEdit (click)="activateInlineEdit('Lead channel', 'minimal-dropdown',leadsChannelList,'')">
                                            {{this.leadDetails.channel_name?this.leadDetails.channel_name:'-'}}
                                          </div>
                                      </div>
                                      <div class="row pl-0 pt-1" *ngIf="this.leadDetails.channel_name && 'leadChannelDes' | tenantLead: formFieldData : 'isActive'">
                                        <div class="col-6 leads-info-title pl-0">
                                          {{'leadChannelDes' | tenantLead: formFieldData : 'label'}}
                                        </div>
                                        <div class="col-6 pl-0 leads-info-details" style="cursor: pointer;">
                                          {{this.leadDetails.channel_name?this.leadDetails.channel_description:'-'}}
                                        </div>
                                    </div>
                                    <div class="pl-0 pt-2 d-flex flex-row-reverse justify-content-start">
                                      <button mat-mini-fab *ngIf="mailAccessForLeads"
                                      style="background-color: #cf0001;color: #FAFAFA; margin-right: 60px;"
                                      
                                      [ngStyle]="{transform: 'scale(0.8)'}"
                                      matTooltip="Open mail">
                                        <mat-icon>email</mat-icon>
                                        </button>
                                        <button mat-mini-fab mat-button (click)="createNewTask()"
                                        style="background-color: #cf0001;color: #FAFAFA;"
                                        [ngStyle]="{transform: 'scale(0.8)'}"
                                        matTooltip="Create Task" matRipple>
                                        <mat-icon>assignment</mat-icon>
                                    </button>
                                      <button mat-mini-fab 
                                      style="background-color: #cf0001;color: #FAFAFA;"
                                      (click)="openMeetingModal()"
                                      matTooltip="Schedule meeting"
                                      [ngStyle]="{transform: 'scale(0.8)'}">
                                        <mat-icon>event_available</mat-icon>
                                      </button>
                                      <button mat-mini-fab 
                                      style="background-color: #cf0001;color: #FAFAFA;"
                                      matTooltip="Add Note"
                                      (click)="addNotes()"
                                      [ngStyle]="{transform: 'scale(0.8)'}">
                                        <mat-icon>note_add</mat-icon>
                                        </button>
                                        <button mat-mini-fab mat-button [matMenuTriggerFor]="menu"
                                      style="background-color: #cf0001;color: #FAFAFA;"
                                      matTooltip="Add Logs" matRipple
                                      [ngStyle]="{transform: 'scale(0.8)'}">
                                      <mat-icon>add_circle</mat-icon>
                                      </button>
                                      <mat-menu #menu="matMenu">
                                      <button mat-menu-item (click)="createCallLog()">Log a call</button>
                                      <button mat-menu-item (click)="createMail()">Log an email</button>
                                      <button mat-menu-item (click)="createMeeting()">Log a meeting</button>
                                    </mat-menu>
                                    </div>
                                  
                                      
                                </div>
                              </div>
                          </div>
                      </div>
                  </div>
                  </div>

                  <div class="row pt-3">
                    <div class="col-12 leads-sub-headings">
                      More info
                    </div>
                  </div>
                  
                  <div class="row">
                      <div class="col-12">
                          <div class="row pl-3 pt-2 slide-from-down" *ngIf="'requirement' | tenantLead: formFieldData : 'isActive'">
                            <div class="col-1">
                                <span>
                                    <mat-icon class="leads-info-icons">
                                      assessment
                                    </mat-icon>
                                </span>
                            </div>
                            <div class="col-6 leads-info-title" >
                              {{'requirement' | tenantLead: formFieldData : 'label'}}
                            </div>
                            <div class="col-5 leads-info-details"  [matTooltip]="this.leadDetails.requirement_name?this.leadDetails.requirement_name:'-'">
                              {{this.leadDetails.requirement_name?this.leadDetails.requirement_name:'-'}}
                            </div>
                          </div>
                          <div class="row pl-3 pt-2 slide-from-down" *ngIf="secondary_industries_names.length==0 && 'secondaryRequirements' | tenantLead: formFieldData : 'isActive'">
                            <div class="col-1">
                                <span>
                                    <mat-icon class="leads-info-icons">
                                      assessment
                                    </mat-icon>
                                </span>
                            </div>
                            <div class="col-6 leads-info-title">
                              {{'secondaryRequirements' | tenantLead: formFieldData : 'label'}}
                            </div>
                            <div class="col-5 leads-info-details" >
                              -
                            </div>
                          </div>
                          <div class="row pl-3 pt-2 slide-from-down" *ngFor="let i of secondary_industries_names;let index=index;">
                            <div class="col-1">
                                <span>
                                    <mat-icon class="leads-info-icons">
                                      assessment
                                    </mat-icon>
                                </span>
                            </div>
                            <div class="col-6 leads-info-title">
                               Secondary Req {{index+1}}
                            </div>
                            <div class="col-5 leads-info-details" [matTooltip]="i">
                              {{i}}
                            </div>
                          </div>
                          <div class="row pl-3 pt-2 slide-from-down" *ngIf="'salesUnit' | tenantLead: formFieldData : 'isActive'">
                            <div class="col-1">
                                <span>
                                    <mat-icon class="leads-info-icons">
                                        domain
                                    </mat-icon>
                                </span>
                            </div>
                            <div class="col-6 leads-info-title">
                               {{'salesUnit' | tenantLead: formFieldData : 'label'}}
                            </div>
                            <div class="col-5 leads-info-details"  >
                              {{this.leadDetails.sales_unit_name?this.leadDetails.sales_unit_name:'-'}}
                            </div>
                          </div>
                          <div class="row pl-3 pt-2 slide-from-down" *ngIf="'leadsClosureDate' | tenantLead: formFieldData : 'isActive'">
                            <div class="col-1">
                                <span>
                                    <mat-icon class="leads-info-icons">
                                        event
                                    </mat-icon>
                                </span>
                            </div>
                            <div class="col-6 leads-info-title">
                              {{'leadsClosureDate' | tenantLead: formFieldData : 'label'}}
                            </div>
                            <div class="col-5 leads-info-details" style="cursor: pointer;" interActiveInlineEdit (click)="activateInlineEdit('Closure date', 'date-picker', [],leadDetails)">
                              {{this.leadDetails?.leads_closure_date?(this.leadDetails?.leads_closure_date|DDMMYYYY):'-'}}
                              
                            </div>
                          </div>
                      </div>
                  </div>
                
                  <div class="row pt-2" style="border-bottom: #d6d6d6 1px solid;">

                  </div>

                  <div class="row pt-3">
                    <div class="col-12 leads-sub-headings">
                      Contact
                    </div>
                  </div>

                  <div class="row">
                    <div class="col-12">
                        <div class="row pl-3 pt-2 slide-from-down" *ngif="'contactFirstName' | tenantLead: formFieldData : 'isActive'">
                          <div class="col-1">
                              <span>
                                  <mat-icon class="leads-info-icons">
                                      person
                                  </mat-icon>
                              </span>
                          </div>
                          <div class="col-6 leads-info-title">
                             {{'contactFirstName' | tenantLead: formFieldData : 'label'}}
                          </div>
                          <div class="col-5 leads-info-details" style="cursor: pointer;" interActiveInlineEdit (click)="activateInlineEdit('Name', 'simple-text', [],leadDetails)">
                            {{this.leadDetails.first_name?this.leadDetails.first_name:'-'}}
                            {{this.leadDetails.last_name?this.leadDetails.last_name:' '}}
                          </div>
                        </div>
                        
                        <div class="row pl-3 pt-2 slide-from-down" *ngIf="'jobTitle' | tenantLead: formFieldData : 'isActive'">
                          <div class="col-1">
                              <span>
                                  <mat-icon class="leads-info-icons">
                                    business
                                  </mat-icon>
                              </span>
                          </div>
                          <div class="col-6 leads-info-title" >
                             {{'jobTitle' | tenantLead: formFieldData : 'label'}}
                          </div>
                          <div class="col-5 leads-info-details" style="cursor: pointer;" interActiveInlineEdit (click)="activateInlineEdit('Job title', 'simple-text',[],leadDetails)">
                            {{this.leadDetails.job_title?this.leadDetails.job_title:'-'}}
                          </div>
                        </div>

                        <div class="row pl-3 pt-2 slide-from-down" *ngif="'contactEmail' | tenantLead: formFieldData : 'isActive'">
                          <div class="col-1">
                              <span>
                                  <mat-icon class="leads-info-icons">
                                      mail
                                  </mat-icon>
                              </span>
                          </div>
                          <div class="col-6 leads-info-title" >
                             {{'contactEmail' | tenantLead: formFieldData : 'label'}}
                          </div>
                          <div class="col-5 leads-info-details" style="cursor: pointer;" interActiveInlineEdit (click)="activateInlineEdit('Email', 'simple-text', [],leadDetails)">
  
                            {{this.leadDetails.email?this.leadDetails.email:'-'}}
                        
                          </div>
                        </div>

                        <div class="row pl-3 pt-2 slide-from-down" *ngIf="'contactTelephone' | tenantLead: formFieldData : 'isActive'">
                          <div class="col-1">
                              <span>
                                  <mat-icon class="leads-info-icons">
                                      phone
                                  </mat-icon>
                              </span>
                          </div>
                          <div class="col-6 leads-info-title" >
                            {{'contactTelephone' | tenantLead: formFieldData : 'label'}}
                          </div>
                          <div class="col-5 leads-info-details" >
                            {{this.leadDetails.telephone?this.leadDetails.telephone:'-'}}
                          </div>
                        </div>

                        <div class="row pl-3 pt-2 slide-from-down" *ngIf="'phone' | tenantLead: formFieldData : 'isActive'">
                          <div class="col-1">
                              <span>
                                  <mat-icon class="leads-info-icons">
                                    phone_iphone
                                  </mat-icon>
                              </span>
                          </div>
                          <div class="col-6 leads-info-title" >
                              {{'phone' | tenantLead: formFieldData : 'label'}}
                          </div>
                          <div class="col-5 leads-info-details" style="cursor: pointer;"  interActiveInlineEdit (click)="activateInlineEdit('Phone', 'simple-text', [],leadDetails)">
                            {{this.leadDetails.mobile_number?this.leadDetails.mobile_number:'-'}}
                          </div>
                        </div>
                    </div>
                </div>

                <div class="row pt-2" style="border-bottom: #d6d6d6 1px solid;">

                </div>

                <div class="row pt-3">
                  <div class="col-12 leads-sub-headings">
                    {{'Owner' | tenantLead: formFieldData : 'label'}}
                  </div>
                </div>

                <div class="row">
                  <div class="col-12">
                      <div class="row pl-3 pt-2 slide-from-down" *ngIf="'leadOwner' | tenantLead: formFieldData : 'isActive'">
                        <div class="col-1">
                            <span>
                                <mat-icon class="leads-info-icons">
                                  account_circle
                                </mat-icon>
                            </span>
                        </div>
                        <div class="col-6 leads-info-title">
                          {{'leadOwner' | tenantLead: formFieldData : 'label'}}
                        </div>
                        <div class="col-5 leads-info-details" style="cursor: pointer;" interActiveInlineEdit (click)="activateInlineEdit('Lead owner', 'search-dropdown', [], '')">
                          <app-user-profile type="name" oid="{{this.leadDetails?this.leadDetails.lead_owner_oid:'-'}}">
                          </app-user-profile>
                        </div>
                      </div>
                      
                      <div class="row pl-3 pt-2 slide-from-down" *ngIf="'salesOwner' | tenantLead: formFieldData : 'isActive'">
                        <div class="col-1">
                            <span>
                                <mat-icon class="leads-info-icons">
                                  account_circle
                                </mat-icon>
                            </span>
                        </div>
                        <div class="col-6 leads-info-title" >
                           {{'salesOwner' | tenantLead: formFieldData : 'label'}}
                        </div>
                        <div class="col-5 leads-info-details" style="cursor: pointer;" interActiveInlineEdit (click)="activateInlineEdit('Sales owner', 'search-dropdown', [], '')">
                          <app-user-profile type="name" oid="{{this.leadDetails?this.leadDetails.sales_owner_oid:'-'}}">
                          </app-user-profile>
                        </div>
                      </div>

                      <div class="row pl-3 pt-2 slide-from-down" *ngIf="'marketingOwner' | tenantLead: formFieldData : 'isActive'">
                        <div class="col-1">
                            <span>
                                <mat-icon class="leads-info-icons">
                                  account_circle
                                </mat-icon>
                            </span>
                        </div>
                        <div class="col-6 leads-info-title" >
                          {{'marketingOwner' | tenantLead: formFieldData : 'label'}}
                        </div>
                        <div class="col-5 leads-info-details" style="cursor: pointer;" interActiveInlineEdit (click)="activateInlineEdit('Marketing owner', 'search-dropdown', [], '')">
                          <app-user-profile type="name" oid="{{this.leadDetails.marketing_owner_oid?this.leadDetails.marketing_owner_oid:'-'}}">
                          </app-user-profile>
                        </div>
                      </div>


                     
                  </div>
              </div>

              
              <div class="row pt-2" style="border-bottom: #d6d6d6 1px solid;">

              </div>

              <div class="row pt-3">
                <div class="col-12 leads-sub-headings">
                  Others
                </div>
              </div>

              <div class="row">
                <div class="col-12">
                    <div class="row pl-3 pt-2 slide-from-down">
                      <div class="col-1">
                          <span class="leads-info-icons">
                              #
                          </span>
                      </div>
                      <div class="col-6 leads-info-title">
                         Lead Id
                      </div>
                      <div class="col-5 leads-info-details" >
                        LE{{this.leadDetails.lead_id?this.leadDetails.lead_id:'-'}}
                      </div>
                    </div>
                    <div class="row pl-3 pt-2 slide-from-down">
                      <div class="col-1">
                          <span>
                              <mat-icon class="leads-info-icons">
                                  event
                              </mat-icon>
                          </span>
                      </div>
                      <div class="col-6 leads-info-title">
                         Lead created on
                      </div>
                      <div class="col-5 leads-info-details">
                        {{this.leadDetails.created_on?(this.leadDetails.created_on|DDMMYYYY):'-'}}
                      </div>
                    </div>
                    

                   
                </div>
            </div>

              

                  
                 
                  
                </div>
              </div>
            </ng-container>
          </div>
        </div>
      </div>
      <div class="col-7 pl-2">
        <div class="row pt-2 pb-2">
          <div class="col-12">
            <div class="card slide-in-top">
              <div class="card-body p-3">
                <ng-container *ngIf="this.leadDetails">
                  <div class="row">
                    <div class="col-1 pr-0">
                      <div class="status-circular-2"></div>
                    </div>
                    <div class="col-11 pl-0  lead-info-details">
                      {{this.leadDetails.requirement_name?this.leadDetails.requirement_name:'-'}}
                    </div>
                  </div>
                </ng-container>
              </div>
            </div>
          </div>
        </div>
        
        <div class="row pb-2" *ngIf="module_list.length!=0">
          <div class="col-12 pl-3 pr-3">
            <span class="tileName pr-2">
              Modules
            </span>
          </div>


            <div class="col-12">
              <div class="card slide-in-top">
                <div class="card-body p-3">
                  <ng-container *ngIf="this.leadDetails">
                    <div class="row" *ngFor="let module of module_list">
                      <div class="col-1 pr-0">
                        <div class="status-circular"></div>
                      </div>
                      <div class="col-11 pl-0  lead-info-details">
                        {{module}}
                      </div>
                    </div>
                  </ng-container>
                </div>
              </div>
            </div>
        </div>
        <div class="row pb-2" style="padding-top: 20px;" *ngIf="productCategory.length!=0 && line_of_business_access">
               
          <div class="col-12 d-flex">
            <span class="tileName pl-1">
                Line of Business
    
            </span>
        
            <button mat-icon-button matTooltip="View more" class="more-button ml-auto" [matMenuTriggerFor]="options" *ngIf="viewScopeAccess">
                <mat-icon class="smallCardIcon">more_vert</mat-icon>
            </button>
            <mat-menu #options="matMenu">
                <button mat-menu-item class="drop-btn" (click)="viewLineofBusiness()">
                    <mat-icon class="smallCardIcon">business_center</mat-icon> <span>View heirarchy</span>
                </button>
            </mat-menu>
        </div>
         


          <div class="col-12">
              <div class="card slide-in-top">
              <div class="card-body p-3">
                <div class="row">
                  <div class="col-1 pr-0">
                      
                  </div>
                  <div class="col-3 pl-2 lead-info-title">
                      Product Name
                  </div>
                  <!-- <div class="col-2 pl-2 lead-info-title">
                      Percent
                  </div> -->
                  <div class="col-2 pl-2 lead-info-title">
                    Value
                  </div>
                  <div class="col-2 pl-2 lead-info-title">
                      FTE Count
                  </div>
                  <div class="col-2 pl-2 lead-info-title">
                    Shift
                </div>
                  
                </div>
                  <div class="row" *ngFor="let product of productCategory">
                      <div class="col-1 pr-0">
                          <div class="status-circular-1"></div>
                      </div>
                      <div class="col-3 pl-2 lead-info-details"
                          style="font-weight: 500; color:#cf0001; " [matTooltip]="product.name">
                          {{product.name}}
                      </div>
                      <!-- <div class="col-2 pl-2 lead-info-details">
                          {{product.percent}}
                      </div> -->
                      <div class="col-2 pl-2 lead-info-details">
                        <app-currency
                        [currencyList]="product.currency_value"
                        class="flex-1" type="small"></app-currency>
                      </div>
                      <div class="col-2 pl-2 lead-info-details" [matTooltip]="product.FTE_Count">
                        {{product.FTE_Count}}
                      </div>
                      <div class="col-2 pl-2 lead-info-details" [matTooltip]="product.shiftName">
                        {{product.shiftName}}
                    </div>
                  </div>
                 
              </div>
              </div>
          </div>
      </div>
        <div>
          <crm-notes appId="35" [elementId]="elementId" #noteComponent></crm-notes>
        </div>
     </div>
  </div>