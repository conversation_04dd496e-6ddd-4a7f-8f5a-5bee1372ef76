<div class="approval-landing-page-styles col-12 pt-5 pl-3">
    <div class="pt-2">
        <mat-button-toggle-group #group="matButtonToggleGroup" (change)="selectToggle($event)">
            <mat-button-toggle value="leaveReq"
                [ngClass]="leaveSelected ? 'toggle-btn-selected' : 'toggle-btn-unselected'">Leave Requests ( {{approvalCount}}
                )
            </mat-button-toggle>
            <mat-button-toggle value="coReq" [ngClass]="coSelected ? 'toggle-btn-selected' : 'toggle-btn-unselected'" *ngIf="showCompOff">CO
                Claim Requests ( {{coCount}} )</mat-button-toggle>
        </mat-button-toggle-group>

        <button mat-stroked-button class="team-calender" (click)="shouldTabBeVisible()" [routerLink]="'teams-calendar'">Team Calendar</button>
    </div>
    
    <div class="row mt-2 tempCheck">
        <div class="col-12 pl-3 pt-2" style="background:#F1F3F8; opacity: 0.4; height: 30px; ">
            <span class="headerName">Approvals</span>
        </div>
        <div class="col-12">
            <mat-tab-group (selectedTabChange)="setClickedTab($event)">
                <mat-tab label="Pending">
                    <div class="col-12" style="padding-top: 5px;">
                        <div class="row">
                            <div class="col-2 subHeaderName" *ngIf = "showBulkApproveCheckbox">
                                <mat-checkbox [(ngModel)]="selectAllActivated" (change)="selectAllLeaveRequest()"
                                    [ngStyle]="index1 ? { 'color': '#45546E'} : { 'color': '#B9C0CA'}">
                                    REQUESTED BY
                                </mat-checkbox>
                                <mat-icon class="iconButtonSort" style="line-height: 12px;"
                                    (click)="sortApproverUi(0, 0)"
                                    [ngStyle]="index1 ? { 'color': '#45546E'} : { 'color': '#B9C0CA'}">arrow_upward
                                </mat-icon>
                                <mat-icon class="iconButtonSort" style="line-height: 12px;"
                                    (click)="sortApproverUi(0, 1)"
                                    [ngStyle]="index1 ? { 'color': '#45546E'} : { 'color': '#B9C0CA'}">arrow_downwards
                                </mat-icon>
                            </div>
                            <div class="col-2 subHeaderName" *ngIf = "!showBulkApproveCheckbox" 
                            [ngStyle]="index1 ? { 'color': '#45546E'} : { 'color': '#B9C0CA'}">
                                REQUESTED BY
                                <mat-icon class="iconButtonSort" style="line-height: 12px; padding-top: 4px;"
                                    (click)="sortApproverUi(0, 0)"
                                    [ngStyle]="index1 ? { 'color': '#45546E'} : { 'color': '#B9C0CA'}">arrow_upward
                                </mat-icon>
                                <mat-icon class="iconButtonSort" style="line-height: 12px; padding-top: 4px"
                                    (click)="sortApproverUi(0, 1)"
                                    [ngStyle]="index1 ? { 'color': '#45546E'} : { 'color': '#B9C0CA'}">arrow_downwards
                                </mat-icon>
                            </div>
                            <div class="col-2 pt-1 subHeaderName">
                                LEAVE TYPE
                            </div>
                            <div class="col-2  subHeaderName"
                                [ngStyle]="index2 ? { 'color': '#45546E'} : { 'color': '#B9C0CA'}">
                                REQUESTED DATE
                                <mat-icon class="iconButtonSort" (click)="sortApproverUi(2, 0)"
                                    [ngStyle]="index2 ? { 'color': '#45546E'} : { 'color': '#B9C0CA'}">arrow_upward
                                </mat-icon>
                                <mat-icon class="iconButtonSort" (click)="sortApproverUi(2, 1)"
                                    [ngStyle]="index2 ? { 'color': '#45546E'} : { 'color': '#B9C0CA'}">arrow_downwards
                                </mat-icon>
                            </div>
                            <div class="col-1  pl-0 pr-0 subHeaderName"
                                [ngStyle]="index3 ? { 'color': '#45546E'} : { 'color': '#B9C0CA'}">
                                REQUEST ID
                                <mat-icon class="iconButtonSort" (click)="sortApproverUi(3, 0)"
                                    [ngStyle]="index3 ? { 'color': '#45546E'} : { 'color': '#B9C0CA'}">arrow_upward
                                </mat-icon>
                                <mat-icon class="iconButtonSort" (click)="sortApproverUi(3, 1)"
                                    [ngStyle]="index3 ? { 'color': '#45546E'} : { 'color': '#B9C0CA'}">arrow_downwards
                                </mat-icon>
                            </div>
                            <div class="col-2 pt-1 subHeaderName">
                                ATTACHMENTS
                            </div>
                            <div class="col-2 pl-0 pr-0 subHeaderName"
                                [ngStyle]="index4 ? { 'color': '#45546E'} : { 'color': '#B9C0CA'}">
                                SUBMITTED ON
                                <mat-icon class="iconButtonSort" (click)="sortApproverUi(5, 0)"
                                    [ngStyle]="index4 ? { 'color': '#45546E'} : { 'color': '#B9C0CA'}">arrow_upward
                                </mat-icon>
                                <mat-icon class="iconButtonSort" (click)="sortApproverUi(5, 1)"
                                    [ngStyle]="index4 ? { 'color': '#45546E'} : { 'color': '#B9C0CA'}">arrow_downwards
                                </mat-icon>
                            </div>
                        </div>
                    </div>
                    <mat-divider></mat-divider>
                    <div class=" itemData col-12 pt-2" *ngIf="leaveData.length > 0">
                        <div *ngFor="let items of leaveData; let ind = index">
                            <div class="row pt-2 pb-2">
                                <div class="col-2 itemName">
                                    <!-- <mat-checkbox [(ngModel)]="items.checked"
                                        (change)="showMultipleApprovalModal(items)"> -->
                                        {{items.employeeName}}
                                        <div class="innerText">EMP ID {{items.empId}}</div>
                                    <!-- </mat-checkbox> -->
                                </div>
                                <div class="col-2 itemName" style="text-decoration: underline; cursor: pointer;" (click)="openDetailModal(items, 'Pending')">
                                    {{items.leaveId}}
                                </div>
                                <div class="col-2 itemName">
                                    {{items.date}}
                                    <div class="innerText" style="color: #F27A6C;;">
                                        {{items.noOfDays}} Day(s)
                                    </div>
                                </div>
                                <div class="col-1 itemName">
                                    REQ {{items.reqId}}
                                </div>
                                <div class="col-2 pl-1 itemName" style="display: flex;">
                                    <div *ngIf="items.attachments != 'No Attachment Found'">
                                        <attachment-upload-btn
                                            [destinationBucket]="attachmentPluginConfig?.destinationBucket"
                                            [routingKey]="attachmentPluginConfig?.routingKey"
                                            [allowEdit]="false"
                                            [contextId]="getFormControlValue('attachments', items.attachments)"
                                            ></attachment-upload-btn>
                                    </div>
                                    <div style="opacity: 0.5" *ngIf="items.attachments == 'No Attachment Found'">
                                        {{items.attachments}}
                                    </div>
                                </div>
                                <div class="col-1 pl-0 itemName">
                                    {{items.submittedOn}}
                                </div>
                                <div class="col-1 pl-5" style="display: flex; ">
                                    <button class="reject" (click)="openRejectModal(items)" [disabled]="disableBtn">Reject</button>
                                    <button class="approve" *ngIf="items.isApprovalAllowed" (click)="approveLeaveRequest(items)"
                                        [disabled]="disableBtn">Approve</button>
                                    <button class="approve" *ngIf="!items.isApprovalAllowed" (click)="approveLeaveRequest(items)"
                                        [disabled]="disableBtn || !items.isApprovalAllowed" [tooltip]="disableReasonTooltip" content-type="template"
                                        max-width="300" placement="left" [class.disabled]="!items.isApprovalAllowed">Approve</button>
                                    <ng-template #disableReasonTooltip placement="left">
                                        <div class="row tooltip-text">
                                            {{ items.approvalDisableReason }}</div>
                                    </ng-template>
                                </div>
                            </div>
                            <mat-divider></mat-divider>
                        </div>
                    </div>
                    <div *ngIf="leaveData.length == 0">
                        <div style="display: flex; justify-content: center; padding-top: 15px;">
                            <img src="https://assets.kebs.app/images/no_milestones_v3.png" width="300" height="250">
                        </div>
                        <div class="noLeave">
                            No Approvals Pending
                        </div>
                    </div>
                </mat-tab>
                <mat-tab label="Approved">
                    <div class="col-12" style="height: 35px; padding-top: 5px;">
                        <div class="row">
                            <div class="col-2 subHeaderName"
                                [ngStyle]="index1 ? { 'color': '#45546E'} : { 'color': '#B9C0CA'}">
                                REQUESTED BY
                                <mat-icon class="iconButtonSort" (click)="sortApproverUi(0, 0)"
                                    [ngStyle]="index1 ? { 'color': '#45546E'} : { 'color': '#B9C0CA'}">arrow_upward
                                </mat-icon>
                                <mat-icon class="iconButtonSort" (click)="sortApproverUi(0, 1)"
                                    [ngStyle]="index1 ? { 'color': '#45546E'} : { 'color': '#B9C0CA'}">arrow_downwards
                                </mat-icon>
                            </div>
                            <div class="col-2 pt-1 subHeaderName">
                                LEAVE TYPE
                            </div>
                            <div class="col-2  subHeaderName"
                                [ngStyle]="index2 ? { 'color': '#45546E'} : { 'color': '#B9C0CA'}">
                                REQUESTED DATE
                                <mat-icon class="iconButtonSort" (click)="sortApproverUi(2, 0)"
                                    [ngStyle]="index2 ? { 'color': '#45546E'} : { 'color': '#B9C0CA'}">arrow_upward
                                </mat-icon>
                                <mat-icon class="iconButtonSort" (click)="sortApproverUi(2, 1)"
                                    [ngStyle]="index2 ? { 'color': '#45546E'} : { 'color': '#B9C0CA'}">arrow_downwards
                                </mat-icon>
                            </div>
                            <div class="col-1 pl-0 pr-0 subHeaderName"
                                [ngStyle]="index3 ? { 'color': '#45546E'} : { 'color': '#B9C0CA'}">
                                REQUEST ID
                                <mat-icon class="iconButtonSort" (click)="sortApproverUi(3, 0)"
                                    [ngStyle]="index3 ?  { 'color': '#45546E'} : { 'color': '#B9C0CA'}">arrow_upward
                                </mat-icon>
                                <mat-icon class="iconButtonSort" (click)="sortApproverUi(3, 1)"
                                    [ngStyle]="index3 ? { 'color': '#45546E'} : { 'color': '#B9C0CA'}">arrow_downwards
                                </mat-icon>
                            </div>
                            <div class="col-2 pt-1  subHeaderName">
                                ATTACHMENTS
                            </div>
                            <div class="col-2 pl-0 subHeaderName"
                                [ngStyle]="index4 ? { 'color': '#45546E'} : { 'color': '#B9C0CA'}">
                                SUBMITTED ON
                                <mat-icon class="iconButtonSort" (click)="sortApproverUi(5, 0)"
                                    [ngStyle]="index4 ? { 'color': '#45546E'} : { 'color': '#B9C0CA'}">arrow_upward
                                </mat-icon>
                                <mat-icon class="iconButtonSort" (click)="sortApproverUi(5, 1)"
                                    [ngStyle]="index4 ? { 'color': '#45546E'} : { 'color': '#B9C0CA'}">arrow_downwards
                                </mat-icon>
                            </div>
                        </div>
                    </div>
                    <mat-divider></mat-divider>
                    <div class="itemData col-12 pt-2" *ngIf="leaveData.length > 0">
                        <div *ngFor="let items of leaveData; let ind = index">
                            <div class="row pt-2 pb-2">
                                <div class="col-2 itemName">
                                    {{items.employeeName}}
                                    <div class="innerText">EMP ID {{items.empId}}</div>
                                </div>
                                <div class="col-2 itemName" style="text-decoration: underline; cursor: pointer;" (click)="openDetailModal(items, 'Approved')">
                                    {{items.leaveId}}
                                </div>
                                <div class="col-2 itemName">
                                    {{items.date}}
                                    <div class="innerText" style="color: #F27A6C;;">
                                        {{items.noOfDays}} Days
                                    </div>
                                </div>
                                <div class="col-1 itemName">
                                    REQ {{items.reqId}}
                                </div>
                                <div class="col-2 itemName" style="display: flex;">
                                    <mat-icon  *ngIf="items.attachments == 'No Attachment Found'" class="iconButton">attachment</mat-icon>
                                    <div *ngIf="items.attachments != 'No Attachment Found'">
                                        <attachment-upload-btn
                                        [destinationBucket]="attachmentPluginConfig?.destinationBucket"
                                        [routingKey]="attachmentPluginConfig?.routingKey"
                                        [allowEdit]="false"
                                        [contextId]="getFormControlValue('attachments', items.attachments)"
                                    ></attachment-upload-btn>
                                    </div>
                                    <div style="opacity: 0.5" *ngIf="items.attachments == 'No Attachment Found'">
                                        {{items.attachments}}
                                    </div>
                                </div>
                                <div class="col-1 pl-0 itemName">
                                    {{items.submittedOn}}
                                </div>
                                <div class="col-1 pl-5">
                                    <button class="approve" style="display: flex">
                                        <mat-icon style="font-size: 18px; padding-right: 20px">done_all</mat-icon>
                                        Approved
                                    </button>
                                </div>
                            </div>
                            <mat-divider></mat-divider>
                        </div>
                    </div>
                    <div *ngIf="leaveData.length == 0">
                        <div style="display: flex; justify-content: center; padding-top: 15px;">
                            <img src="https://assets.kebs.app/images/no_milestones_v3.png" width="300" height="250">
                        </div>
                        <div class="noLeave">
                            No Approvals Pending?!!!
                        </div>
                    </div>
                </mat-tab>
                <mat-tab label="Rejected">
                    <div class="col-12" style="height: 35px; padding-top: 5px;">
                        <div class="row">
                            <div class="col-2 subHeaderName"
                                [ngStyle]="index1 ? { 'color': '#45546E'} : { 'color': '#B9C0CA'}">
                                REQUESTED BY
                                <mat-icon class="iconButtonSort" (click)="sortApproverUi(0, 0)"
                                    [ngStyle]="index1 ? { 'color': '#45546E'} : { 'color': '#B9C0CA'}">arrow_upward
                                </mat-icon>
                                <mat-icon class="iconButtonSort" (click)="sortApproverUi(0, 1)"
                                    [ngStyle]="index1 ? { 'color': '#45546E'} : { 'color': '#B9C0CA'}">arrow_downwards
                                </mat-icon>
                            </div>
                            <div class="col-2 pt-1 subHeaderName">
                                LEAVE TYPE
                            </div>
                            <div class="col-2 subHeaderName"
                                [ngStyle]="index2 ? { 'color': '#45546E'} : { 'color': '#B9C0CA'}">
                                REQUESTED DATE
                                <mat-icon class="iconButtonSort" (click)="sortApproverUi(2, 0)"
                                    [ngStyle]="index2 ? { 'color': '#45546E'} : { 'color': '#B9C0CA'}">arrow_upward
                                </mat-icon>
                                <mat-icon class="iconButtonSort" (click)="sortApproverUi(2, 1)"
                                    [ngStyle]="index2 ? { 'color': '#45546E'} : { 'color': '#B9C0CA'}">arrow_downwards
                                </mat-icon>
                            </div>
                            <div class="col-1 pl-0 pr-0 subHeaderName"
                                [ngStyle]="index3 ? { 'color': '#45546E'} : { 'color': '#B9C0CA'}">
                                REQUEST ID
                                <mat-icon class="iconButtonSort" (click)="sortApproverUi(3, 0)"
                                    [ngStyle]="index3 ? { 'color': '#45546E'} : { 'color': '#B9C0CA'}">arrow_upward
                                </mat-icon>
                                <mat-icon class="iconButtonSort" (click)="sortApproverUi(3, 1)"
                                    [ngStyle]="index3 ? { 'color': '#45546E'} : { 'color': '#B9C0CA'}">arrow_downwards
                                </mat-icon>
                            </div>
                            <div class="col-2 pt-1 subHeaderName">
                                ATTACHMENTS
                            </div>
                            <div class="col-2 pl-0 subHeaderName"
                                [ngStyle]="index4 ? { 'color': '#45546E'} : { 'color': '#B9C0CA'}">
                                SUBMITTED ON
                                <mat-icon class="iconButtonSort" (click)="sortApproverUi(5, 0)"
                                    [ngStyle]="index4 ? { 'color': '#45546E'} : { 'color': '#B9C0CA'}">arrow_upward
                                </mat-icon>
                                <mat-icon class="iconButtonSort" (click)="sortApproverUi(5, 1)"
                                    [ngStyle]="index4 ? { 'color': '#45546E'} : { 'color': '#B9C0CA'}">arrow_downwards
                                </mat-icon>
                            </div>
                        </div>
                    </div>
                    <mat-divider></mat-divider>
                    <div class=" itemData col-12 pt-2" *ngIf="leaveData.length > 0">
                        <div *ngFor="let items of leaveData; let ind = index">
                            <div class="row pt-2 pb-2">
                                <div class="col-2 itemName">
                                    {{items.employeeName}}
                                    <div class="innerText">EMP ID {{items.empId}}</div>
                                </div>
                                <div class="col-2 itemName" style="text-decoration: underline; cursor: pointer;" (click)="openDetailModal(items, 'Rejected')">
                                    {{items.leaveId}}
                                </div>
                                <div class="col-2 itemName">
                                    {{items.date}}
                                    <div class="innerText" style="color: #F27A6C;;">
                                        {{items.noOfDays}} Days
                                    </div>
                                </div>
                                <div class="col-1 itemName">
                                    REQ {{items.reqId}}
                                </div>
                                <div class="col-2 itemName" style="display: flex;">
                                    <mat-icon *ngIf="items.attachments == 'No Attachment Found'" class="iconButton">attachment</mat-icon>
                                    <div *ngIf="items.attachments != 'No Attachment Found'">
                                        <attachment-upload-btn
                                        [destinationBucket]="attachmentPluginConfig?.destinationBucket"
                                        [routingKey]="attachmentPluginConfig?.routingKey"
                                        [allowEdit]="false"
                                        [contextId]="getFormControlValue('attachments', items.attachments)"
                                    ></attachment-upload-btn>
                                    </div>
                                    <div style="opacity: 0.5" *ngIf="items.attachments == 'No Attachment Found'">
                                        {{items.attachments}}
                                    </div>
                                </div>
                                <div class="col-1 pl-0 itemName">
                                    {{items.submittedOn}}
                                </div>
                                <div class="col-1 pl-5">
                                    <button class="reject" style="display:flex; border: 1px solid #FF3A46;
                                    border-radius: 4px; color: #FF3A46;">
                                        <mat-icon style="font-size: 18px; padding-right: 20px">cancel</mat-icon>
                                        Rejected
                                    </button>
                                </div>
                            </div>
                            <mat-divider></mat-divider>
                        </div>
                    </div>
                    <div *ngIf="leaveData.length == 0">
                        <div style="display: flex; justify-content: center; padding-top: 15px;">
                            <img src="https://assets.kebs.app/images/no_milestones_v3.png" width="300" height="250">
                        </div>
                        <div class="noLeave">
                            No Approvals Pending?!!!
                        </div>
                    </div>
                </mat-tab>
            </mat-tab-group>
        </div>
    </div>
</div>
<router-outlet></router-outlet>