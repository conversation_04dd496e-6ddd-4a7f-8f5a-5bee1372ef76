.approval-landing-page-styles{

  .noLeave{
    color: #45546E;
    font-weight: 700;
    font-size: 14px;
    text-align: center;
    text-transform: capitalize;
    font-family: 'Roboto';
    font-style: normal;
    padding-top: 10px;
  }

  .header{
    position: absolute;
    width: 1260px;
    height: 455px;
    background: #FFFFFF;
    box-shadow: 0px 2px 1px rgba(0, 0, 0, 0.12), 0px 4px 6px rgba(0, 0, 0, 0.12);
    border-radius: 4px;
    overflow-y: auto;
  }
  .headerName{
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 16px;
    letter-spacing: 0.02em;
    text-transform: capitalize;
    color: rgb(69, 84, 110);
    padding-top: 15px;
  }

  .subHeaderName{
    color: #B9C0CA;
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 400;
    font-size: 11px;
    line-height: 16px;
    letter-spacing: 0.02em;
    text-transform: uppercase;
  }

  ::ng-deep .mat-tab-label-content{
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 16px;
    letter-spacing: 0.02em;
    text-transform: capitalize;
    color: #CF0001
  }

  .itemName{
    color: #45546E;
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 400;
    font-size: 12px;
    line-height: 16px;
    letter-spacing: 0.02em;
    text-transform: capitalize;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .innerText{
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 400;
    font-size: 10px;
    line-height: 16px;
    letter-spacing: 0.02em;
    text-transform: capitalize;
    color: #B9C0CA;
  }
  .reject{
    border: 1px solid #45546E;
    border-radius: 4px;
    letter-spacing: -0.02em;
    text-transform: capitalize;
    color: #45546E;
    background: #FFFFFF;
    font-weight: 700;
    height: 25px;
  }
  .approve{
    margin-left: 10px;
    border: 1px solid #52C41A;
    border-radius: 4px;
    letter-spacing: -0.02em;
    text-transform: capitalize;
    color: #52C41A;
    background: #FFFFFF;
    font-weight: 700;
    height: 25px;
  }
  ::ng-deep .mat-checkbox-layout{
    display: inline-block;
  }
  .iconButton{
    color: #5F6C81;
    font-size: 18px;
    vertical-align: middle;
  }

  .iconButtonSort{
    color: #B9C0CA;
    font-size: 12px;
    width: 12px;
    vertical-align: middle;
    line-height: 23px;
    cursor: pointer;
  }

  ::ng-deep .mat-checkbox-checked.mat-accent .mat-checkbox-background,.mat-checkbox-indeterminate.mat-accent .mat-checkbox-background {
    background-color: #EF4A61
  }

  ::ng-deep .mat-button-toggle-checked {
    background-color: #EF4A61 !important; 
  }
}

.toggle-btn-selected{
  background: #EE4961;
  border-radius: 4px 0px 0px 4px;
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-size: 12px;
  line-height: 16px;
  letter-spacing: -0.02em;
  text-transform: capitalize;
  color: #FFFFFF;
}
.toggle-btn-unselected{
  background: #FFFFFF;
  border-radius: 4px 0px 0px 4px;
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-size: 12px;
  line-height: 16px;
  letter-spacing: -0.02em;
  text-transform: capitalize;
  color: #45546E;
}

.tempCheck{

  background: #FFFFFF;
  box-shadow: 0px 2px 1px rgba(0, 0, 0, 0.12), 0px 4px 6px rgba(0, 0, 0, 0.12);
  border-radius: 4px;
}
.itemData{
  height: 305px;
  overflow-y: auto;
}

.team-calender{
  float:right;
  color: #5F6C81;
  border: 1px solid #5F6C81;
}
.disabled{
  opacity: 0.6;
  cursor: not-allowed;
}