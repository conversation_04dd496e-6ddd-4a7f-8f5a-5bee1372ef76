import { Component, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ErrorService } from 'src/app/services/error/error.service';
import { LoginService } from 'src/app/services/login/login.service';
import { LeaveAppService } from '../../../services/leave-app.service';
import * as moment from 'moment';
import _ from 'underscore';
import { UtilityService } from 'src/app/services/utility/utility.service';
import { FileSaverService } from 'src/app/services/fileSaver/file-saver.service';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { ToasterService } from 'src/app/services/toaster-service/toaster.service';
import { ToastService } from '../../lazy-loaded/toast-message/toast.service';
import { ErrorPopupComponent } from '../../lazy-loaded/error-popup/error-popup.component';
@Component({
  selector: 'app-approval-landing-page',
  templateUrl: './approval-landing-page.component.html',
  styleUrls: ['./approval-landing-page.component.scss']
})
export class ApprovalLandingPageComponent implements OnInit {

  leaveSelected: boolean = true;
  coSelected: boolean = false;
  leaveData = [];
  headerTitleData = [
    {
      titleName: 'REQUESTED BY',
      class: 'col-2',
      includeCheckBox: true,
      sortOrder: 'NS',
      isSorted: false,
      sortedColumnName: 'TEPD.first_name'
    },
    { 
      titleName:'LEAVE ID', 
      class: 'col-1',
      includeCheckBox: false,
      sortOrder: 'NS',
      isSorted: false,
      sortedColumnName: null
    },
    {
      titleName:'REQUESTED DATE',
      class: 'col-2',
      includeCheckBox: false,
      sortOrder: 'NS',
      isSorted: false,
      sortedColumnName: 'TLAD.dates'
    },
    { 
      titleName:'REQUEST ID',
      class: 'col-1',
      includeCheckBox: false,
      sortOrder: 'NS',
      isSorted: false,
      sortedColumnName: 'TLAD.id' 
    },
    {
      titleName:'ATTACHMENTS',
      class: 'col-2',
      includeCheckBox: false,
      sortOrder: 'NS',
      isSorted: false,
      sortedColumnName: null
    },
    { 
      titleName:'SUBMITTED ON',
      class: 'col-2',
      includeCheckBox: false,
      sortOrder: 'NS',
      isSorted: false,
      sortedColumnName: 'TLAD.applied_on'
}];

deafaultLeaveStatus: string = "Pending";

statusId: number = 1;

isClaim: number = 0;

statusArray = [];

currentUser: any;

currentUserOid: string;

selectedToggle: string;

coCount: number = 0;

approvalCount: number = 0;

selectAllActivated: boolean = false;

multiApproveLeaveData = [];

sortOrder: string = null;

index1 = false; index2 = false; index3 = false; index4 = false;

uiConfigData: any;

s3BucketConfig = {};

attachmentPluginConfig = {};

disableBtn: boolean = false;

showCompOff: boolean = false;

validForApproval: number = 0;

showBulkApproveCheckbox: boolean = true;

protected _onDestroy = new Subject<void>();


  constructor(public matDialog: MatDialog,  
    private _leaveAppService: LeaveAppService,
    private errorService: ErrorService,
    private authService: LoginService,
    private utilityService: UtilityService, 
    private _fileSaver : FileSaverService,
    private toastService: ToasterService,
    private toastmessage: ToastService) { }

  ngOnInit(): void {

    this.currentUser = this.authService.getProfile().profile;

    this.currentUserOid = this.currentUser.oid;

    this.getUIConfig();

    this.getAllStatus();

    this.getAllLeaveApprovalDetails(this.sortOrder);
    
  }
  // Function to display multiple approve ui based on checkbox selected by the user
  async showMultipleApprovalModal(items)
  {
    if(items.checked)
    {
      this.multiApproveLeaveData.push(items);
    }
    else{
      let index = _.findIndex(this.multiApproveLeaveData ,{reqId: items.reqId})
      this.multiApproveLeaveData.splice(index, 1);
    }

    let modalParams = {
      noOfLeaves: this.multiApproveLeaveData.length,
      data: this.multiApproveLeaveData
    }
    if(this.multiApproveLeaveData.length > 0) {
    const { MultiApproveRejectModalComponent } = await import('../components/multi-approve-reject-modal/multi-approve-reject-modal.component');

    const MultiApproveRejectModalComponentData = this.matDialog.open(MultiApproveRejectModalComponent, {
      minWidth: '50%',
      position: { left: '400px', bottom: '0px' },
      data: { modalParams: modalParams },
    });

    MultiApproveRejectModalComponentData.afterClosed().subscribe(res => {
      if (res.event == "close") {
          this.getAllLeaveApprovalDetails(this.sortOrder);
      }
    });
  }

    
  }
  // Function to open the reject-dialog ui upon clicking of reject button
  async openRejectModal(data)
  { 
    let modalParams = {
    data: data,
  }

  const { ApproverRejectDialogComponent } = await import('../components/approver-reject-dialog/approver-reject-dialog.component');

  const ApproverRejectDialogComponentData = this.matDialog.open(ApproverRejectDialogComponent, {
    height: '40%',
    minWidth: '40%',
    position: { left: '400px', bottom: '400px', right: '400px', top: '200px' },
    data: { modalParams: modalParams }
  });

  ApproverRejectDialogComponentData.afterClosed().subscribe(res => {
    if (res.event == "close") {
        this.getAllLeaveApprovalDetails(this.sortOrder);
    }
  });

  }
  // Function to open leave detail view in approver tab 
  async openDetailModal(item, status)
  {
    let modalParams = {
      data: item,
      s3BucketConfig: this.s3BucketConfig,
      status: status
    }
  
    const { ApproverDetailModalComponent } = await import('../components/approver-detail-modal/approver-detail-modal.component');
  
    const ApproverDetailModalComponentData = this.matDialog.open(ApproverDetailModalComponent, {
      height: '100%',
      minWidth: '50%',
      position: { right: '0px' },
      data: { modalParams: modalParams }
    });

    ApproverDetailModalComponentData.afterClosed().subscribe(res => {
      if (res.event == "close") {
          this.getAllLeaveApprovalDetails(this.sortOrder);
      }
    });
  
  
  }
  // Function to get all status of the leave app status like Approved, Pending, rejected
  getAllStatus()
  {
    this._leaveAppService.getAllStatus()
    .pipe(takeUntil(this._onDestroy))
    .subscribe(async(res) =>{
      if(res['messType'] == 'S' && res['data'] && res['data'].length > 0)
      {
        this.statusArray = res['data'];
      }
      else{
        this.toastmessage.showError(res['messText']);
      }
    }, err => {
        let modalParams = err.error;
        this.matDialog.open(ErrorPopupComponent, {
          width:'40%',
          minHeight:'250px',
          data: { modalParams: modalParams }
        });
    })
  }
  // Function to get all leave request details that is raised by the user.
  async getAllLeaveApprovalDetails(sortOrder)
  {
    this.leaveData = [];
    this.selectAllActivated = false;
    this._leaveAppService.getLeaveRequests(this.currentUserOid, this.statusId, this.isClaim, sortOrder, null)
    .pipe(takeUntil(this._onDestroy))
    .subscribe(async(res) =>{
      if(res['messType'] == 'S' && res['data'] && res['data'].length > 0)
      {
        this.leaveData = [];
        if(this.statusId == 1){
        this.approvalCount = res['count'].approvalCount;
        this.coCount = res['count'].claimCount
        }
     
        for(let items of res['data'])
        {
          let dateArray = JSON.parse(items.dates).sort(function(a, b){
            return new Date(a).valueOf() - new Date(b).valueOf()
          });
          this.leaveData.push({
            employeeName: items.employee_name,
            empId: items.associate_id,
            leaveId: items.leave_type + ' ' + items.leave_description + (items?.session_id == 1 ? "(FD)" :  items?.session_id == 2 ? "(FN)" : items?.session_id == 3 ? "(AN)" : ""), 
            date:  moment(dateArray[0]).format("DD MMM YYYY") + ' - ' + moment(dateArray[dateArray.length - 1]).format("DD MMM YYYY"),
            noOfDays: items.is_half_day_cog == 1 ? (JSON.parse(items.dates).length * 0.5) : (items.session_id == 2 || items.session_id == 3) ? (JSON.parse(items.dates).length * items.leave_value_to_be_considered * 0.5) : (JSON.parse(items.dates).length * items.leave_value_to_be_considered),
            reqId: items.id,
            attachments: items.attachments != null ? JSON.parse(items.attachments) : 'No Attachment Found' ,
            submittedOn: moment(items.applied_on).format("DD MMM YYYY"),
            team: items.practice_name,
            reason: JSON.parse(items.reason),
            approvers: JSON.parse(items.approvers),
            leaveName: items.leave_description,
            workflowHeaderId: items.workflow_header_id,
            associateOid: items.associate_oid,
            checked: false,
            leaveBalance: items.leave_balnce,
            leaveQuota: items.leave_quota,
            isApprovalAllowed: items.isApprovalAllowed,
            approvalDisableReason: items.approvalDisableReason,
          });

          if(((_.pluck(_.filter(this.leaveData, {isApprovalAllowed: 1}), "workflowHeaderId")).length) == 0){
            this.showBulkApproveCheckbox = false;
          }else{
            this.showBulkApproveCheckbox = true;
          }

        }
      }else{
        this.showBulkApproveCheckbox = false;
        this.coCount = res['count'].claimCount;
        this.approvalCount = res['count'].approvalCount;
        this.leaveData = [];
      }
    }, err => {
      let modalParams = err.error;
      this.matDialog.open(ErrorPopupComponent, {
        width:'40%',
        minHeight:'250px',
        data: { modalParams: modalParams }
      });

    })
  }
  // Function to select the particular leave status like Approved, Pending, Rejected 
   setClickedTab(selectedItem)
  {
    this.sortOrder = null;
    this.index1 = false, this.index2 = false, this.index3 = false, this.index4 = false;
    this.statusId = _.filter(this.statusArray, {status_name: selectedItem.tab.textLabel}).length > 0 ? 
    _.filter(this.statusArray, {status_name: selectedItem.tab.textLabel})[0].id : 1;
    this.isClaim = this.selectedToggle == "coReq" ? 1 : 0;
    this.getAllLeaveApprovalDetails(this.sortOrder);
  }
   // Function to toggle between claim and leave request ui
   selectToggle(selectedObj)
  {
     
      this.selectedToggle = selectedObj.value;
      if(this.selectedToggle == "coReq")
      {
        this.isClaim = 1;
        this.leaveSelected = false;
        this.coSelected = true;
      }
      else{
        this.leaveSelected = true;
        this.coSelected = false;
        this.isClaim = 0;
      } 
      this.getAllLeaveApprovalDetails(this.sortOrder);
  }
  // Function to download the attachment that is uploadedby the user 
  downloadFile(index, arrayIndex)
  {
    let file = this.leaveData[arrayIndex].attachments[index]

    this._leaveAppService.getLeaveAttachment(file.key)
    .pipe(takeUntil(this._onDestroy))
      .subscribe(async res => {
        if (res["messType"] == 'S') {
          if (file.type == 'image/png' || file.type == 'image/jpg' || file.type == 'image/jpeg' || file.type == 'application/pdf') {
            let pdfWindow = window.open("");
            pdfWindow.document.write(`<iframe width=100% height=100% src=data:${file.type};base64,${res["data"]["fileData"]}></iframe>`);
            this._fileSaver.saveAsFile(res["data"]["fileData"], file.fileName, file.type);
          }
          else
            this._fileSaver.saveAsFile(res["data"]["fileData"], file.fileName, file.type);
        }
        else{
          this.toastmessage.showError(res['messText']);
        }
      },
      err => {
        let modalParams = err.error;
        this.matDialog.open(ErrorPopupComponent, {
          width:'40%',
          minHeight:'250px',
          data: { modalParams: modalParams }
        });
      });
  }
  // Function to approve the leave request
   approveLeaveRequest(items)
  {
    this.disableBtn = true;
    this._leaveAppService.updateLeaveRequestStatus(items.associateOid, items.workflowHeaderId, 'A', null)
    .pipe(takeUntil(this._onDestroy))
    .subscribe(async(res) =>{
      if(res['messType'] == 'S')
      {
        this.getAllLeaveApprovalDetails(this.sortOrder);
        this.disableBtn = false;
        this.toastmessage.showSuccess(res['messText'], 5000);
      }
      else{
        this.disableBtn = false;
        this.toastmessage.showError(res['messText']);
      }
    },
    err => {
      let modalParams = err.error;
      this.matDialog.open(ErrorPopupComponent, {
        width:'40%',
        minHeight:'250px',
        data: { modalParams: modalParams }
      });

    })
  }
  // Function to select all leave request for the multi-approval
  async selectAllLeaveRequest()
  {
    this.validForApproval =  (_.pluck(_.filter(this.leaveData, {isApprovalAllowed: 1}), "workflowHeaderId")).length;
    if (this.selectAllActivated){
    _.each(this.leaveData, value => {
      value.checked = true;
    });

    if(this.leaveData.length == 0)
    {
      this.selectAllActivated = false;
      return this.toastmessage.showWarning("No Leave Request Pending For Approval",5000) 
    }
    
    if(this.validForApproval == 0)
    {
      this.selectAllActivated = false;
      return this.toastmessage.showWarning("No Request is valid for Approval",5000) 
    }
    let modalParams = {
      noOfLeaves: this.leaveData.length,
      data: this.leaveData
    }

    const { MultiApproveRejectModalComponent } = await import('../components/multi-approve-reject-modal/multi-approve-reject-modal.component');

    const MultiApproveRejectModalComponentData = this.matDialog.open(MultiApproveRejectModalComponent, {
      minWidth: '50%',
      position: { left: '400px', bottom: '0px' },
      data: { modalParams: modalParams }
    });

    MultiApproveRejectModalComponentData.afterClosed().subscribe(res => {
      this.selectAllActivated = false;
      if (res.event == "close") {
          this.getAllLeaveApprovalDetails(this.sortOrder);
      }
    });
  }
  else{
    _.each(this.leaveData, value => {
      value.checked = false;
    });
  }
  }
  // Function to sort the field that is present in the approval ui
  sortApproverUi(index, ops)
  {

    this.sortOrder = "ORDER BY "
   
    if(ops == 0 && this.headerTitleData[index].sortOrder != 'ASC')
    {
      index == 0 ? this.index1 = true : index == 2 ? this.index2 = true : index == 3 ? this.index3 = true : index == 5 ? this.index4 = true : false; 
      this.headerTitleData[index].sortOrder = 'ASC';
      this.headerTitleData[index].isSorted = true;
    }
    else if(ops == 0 && this.headerTitleData[index].sortOrder == 'ASC')
    {
      index == 0 ? this.index1 = false : index == 2 ? this.index2 = false : index == 3 ? this.index3 = false : index == 5 ? this.index4 = false : false; 
      this.headerTitleData[index].sortOrder = 'NS';
      this.headerTitleData[index].isSorted = false;
      this.sortOrder = null;
    }
    else if(ops == 1 && this.headerTitleData[index].sortOrder != 'DESC'){
      index == 0 ? this.index1 = true : index == 2 ? this.index2 = true : index == 3 ? this.index3 = true : index == 5 ? this.index4 = true : false; 
      this.headerTitleData[index].sortOrder = 'DESC';
      this.headerTitleData[index].isSorted = true;
    }
    else if(ops == 1 && this.headerTitleData[index].sortOrder == 'DESC')
    {
      index == 0 ? this.index1 = false : index == 2 ? this.index2 = false : index == 3 ? this.index3 = false : index == 5 ? this.index4 = false : false; 
      this.headerTitleData[index].sortOrder = 'NS';
      this.headerTitleData[index].isSorted = false;
      this.sortOrder = null;
    }

     let sortedArrayList = _.filter(this.headerTitleData, {isSorted: true});

    if(sortedArrayList.length > 0)
    {
      for(let i = 0 ; i < sortedArrayList.length; i ++)
      {
        if(i < sortedArrayList.length - 1)
          this.sortOrder += sortedArrayList[i].sortedColumnName + ' ' + sortedArrayList[i].sortOrder + ' , ';
        else 
          this.sortOrder += sortedArrayList[i].sortedColumnName + ' ' + sortedArrayList[i].sortOrder + ' ';
      }
    }

    this.getAllLeaveApprovalDetails(this.sortOrder);
    
  }
  
  // Function to Get UI Configuration Details For Leave App & store it in respective variables
  getUIConfig()
  {
    this._leaveAppService.uiConfig(this.currentUser.aid, this.currentUser.oid)
    .pipe(takeUntil(this._onDestroy))
    .subscribe(async res => {
      if(res['data'])
      {
        this.uiConfigData = res['data'][0];
        this.showCompOff = this.uiConfigData.compensation_off == 1 ? true : false ;
        this.s3BucketConfig = JSON.parse(this.uiConfigData.s3_bucket_config);
        this.attachmentPluginConfig = this.s3BucketConfig;
      }
    },err => {
      let modalParams = err.error;
      this.matDialog.open(ErrorPopupComponent, {
        width:'40%',
        minHeight:'250px',
        data: { modalParams: modalParams }
      });
    });
  }

  getFormControlValue(formControlName, data) {
    let val = data;
    return val ? val : null;
  }

  shouldTabBeVisible() {
    this._leaveAppService.shouldTabBeVisibleChange(1);
  }

  ngOnDestroy() {
    this._onDestroy.next();
    this._onDestroy.complete();
  }

   //Function that navigate to teams-Calender Page

  //  async teamscalender(){
  //   const { TeamsCalendarComponent } = await import('../components/teams-calendar/teams-calendar.component');

  //   const CompensationOffComponentData = this.matDialog.open(TeamsCalendarComponent, {
  //     maxWidth: '100vw',
  //     maxHeight: '100vh',
  //     height: '100%',
  //     width: '100%',
  //     panelClass: 'full-screen-modal'
  //   });
    
  // }

}
