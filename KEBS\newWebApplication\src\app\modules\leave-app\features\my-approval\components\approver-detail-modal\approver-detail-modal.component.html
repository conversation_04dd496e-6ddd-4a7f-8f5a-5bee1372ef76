<div class="container-fluild detail-view-styles ">
  <div class="col-12 pl-0 pr-0 pt-4">
    <div class="row pl-2 pr-2">
      <div class="col-10">
        <span class="empName">{{deatilViewData.employeeName}}</span>
        <div class="empId">EMP ID {{deatilViewData.empId}}</div>
      </div>
      <div class="col-2">
        <button class="view-button-inactive" matTooltip="Close" (click)="closeForm()">
          Close
        </button>
      </div>
    </div>
    <div class="row pl-2 pr-2">
      <div class="col-3 pt-2">
        <span class="subHead">{{ deatilViewData.leaveName }}</span>
        <div class="info">
          {{deatilViewData.noOfDays}} Day
        </div>

      </div>
      <div class="col-3 pt-2">
        <span class="subHead">Day Requested</span>
        <div class="info">
          {{deatilViewData.date}}
        </div>

      </div>
      <div class="col-3 pt-2">
        <span class="subHead">Approvers</span>
        <div *ngFor="let appr of deatilViewData.approvers">
          <app-user-image [tooltip]="approverTooltip" content-type="template" max-width="300" placement="top"
            style="margin: 2px;" imgWidth="32px" imgHeight="32px" [id]="appr.oid"></app-user-image>
          <ng-template #approverTooltip placement="top">
            <div class="row tooltip-text">
              {{ appr.name }}
            </div>
            <div class="row tooltip-text">
              {{ appr.role }}
            </div>
            <div class="row tooltip-text"> Level : {{appr.level}}</div>
          </ng-template>
        </div>

      </div>
      <div class="col-3 pt-2" style="display: flex;" *ngIf="status == 'Pending'">
        <button class="btn" style="border: 1px solid #45546E;color: #45546E; background: #ffff;"
          (click)="openReject()">Reject</button>
        <button *ngIf = "deatilViewData.isApprovalAllowed" class="btn" style="background: #52C41A; color: #FFFFFF; margin-left: 10px;"
          [disabled]="disableBtn"
          (click)="updateLeaveRequest('A')" >Approve</button>
          <button *ngIf = "!deatilViewData.isApprovalAllowed" class="btn" style="background: #52C41A; color: #FFFFFF; margin-left: 10px;"
          [disabled]="disableBtn || !deatilViewData.isApprovalAllowed" [tooltip]="disableReasonTooltip" content-type="template" max-width="300"
          placement="left" [class.disabled] = "!deatilViewData.isApprovalAllowed"
          (click)="updateLeaveRequest('A')" >Approve</button>
          <ng-template #disableReasonTooltip placement="left">
            <div class="row tooltip-text">
                {{ deatilViewData.approvalDisableReason }}</div>
        </ng-template>
      </div>
    </div>
    <div *ngIf="rejectClicked" class="row pt-2 pb-2">
      <div class="col-12 rejectModal pl-0 pr-0">
        <div class="row">
          <div class="col-12 pt-2 subHead">
            Reason
          </div>
        </div>
        <div class="col-12 pl-0">
          <div class="col-12">
            <mat-form-field appearance="outline" floatLabel="always"
              style="width: 650px;border: 1px solid #FACAC4; border-radius: 8px;">
              <textarea matInput rows="4" cols="88" placeholder="Reason" [(ngModel)]="reason" required></textarea>
            </mat-form-field>
          </div>
          <div class="row pb-3">
            <div class="col-9"></div>
            <div class="col-3" style="display: flex;">
              <button class="btn" style="border: 1px solid #45546E; border-radius: 4px; background: #ffff;"
                (click)="closeForm()">Cancel</button>
              <button class="btn" style="background: #FF3A46; border-radius: 4px; margin-left: 10px; color: #FFFF;"
              [disabled]="disableBtn"  
              (click)="updateLeaveRequest('R')">Reject</button>
            </div>
          </div>
        </div>

      </div>
    </div>
    <div class="row pt-3 pb-3 pl-2 pr-2">
      <div class="col-12 reasonText">
        {{deatilViewData.reason}}
      </div>
    </div>
    <div class="row pb-3 pl-2 pr-2">
      <div class="col-12 reasonText">
        <div *ngIf="deatilViewData.attachments != 'No Attachment Found'">
          <attachment-upload-btn [destinationBucket]="attachmentPluginConfig?.destinationBucket"
            [routingKey]="attachmentPluginConfig?.routingKey" [allowEdit]="false"
            [contextId]="getFormControlValue('attachments' , deatilViewData.attachments)">
          </attachment-upload-btn>
          <!-- [contextId]="getFormControlValue('attachments')" (change)="changeInattachments($event, 'attachments')"></attachment-upload-btn> -->
        </div>
        <div style="opacity: 0.5" *ngIf="deatilViewData.attachments == 'No Attachment Found'">
          {{deatilViewData.attachments}}
        </div>
      </div>
    </div>
    <mat-divider style="margin-left: 20px; margin-right: 20px"></mat-divider>
    <div class="row pt-2 pb-2 pl-2 pr-2">
      <div class="col-4 ">
        <span class="subHead">Applied On</span>
        <div class="text">{{deatilViewData.submittedOn}}</div>
      </div>

      <div class="col-4 ">
        <span class="subHead">Department</span>
        <div class="text">{{deatilViewData.team}}</div>
      </div>

      <div class="col-4 ">

        <span class="subHead">{{ deatilViewData.leaveName }} Balance</span>

        <div *ngIf="deatilViewData.leaveBalance != null && deatilViewData.leaveQuota != null" class="text">{{
          deatilViewData.leaveBalance }} / {{ deatilViewData.leaveQuota }} Days</div>

        <div *ngIf="deatilViewData.leaveBalance == null || deatilViewData.leaveQuota == null" class="text">Leave
          Balance missing!</div>

      </div>
    </div>
    <mat-divider style="margin-left: 20px; margin-right: 20px"></mat-divider>
    <mat-tab-group (selectedTabChange)="setClickedTab($event)">
      <mat-tab label="Leave Balance Details">
        <div class="d-flex justify-content-center mt-3" *ngIf="isloading">
          <mat-spinner matTooltip="Please wait..." diameter="30"> </mat-spinner>
        </div>
        <div class="row pt-3" *ngIf="!isNoDataFound">
          <div class="col-12 p-0">
            <mat-table [dataSource]="tableDataSource" matSort (matSortChange)="sortData()"
              class="mat-elevation-z0 slide-from-down" style="margin-left: 20px; margin-right: 20px;">
              <ng-container *ngFor="let col of columns" [matColumnDef]="col.colKey">
                <mat-header-cell *cdkHeaderCellDef mat-sort-header>
                  <span class="header-text">{{
                    col.colName
                    }}</span>
                </mat-header-cell>
                <mat-cell [ngClass]="col.className" [matTooltip]="col.isToolTip ? col?.toolTipVal(row) : ''"
                  *cdkCellDef="let row">
                  <span class="overflow-ctrl">{{ col.cell(row) }}</span>
                </mat-cell>
              </ng-container>

              <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
              <mat-row *matRowDef="let row; columns: displayedColumns"></mat-row>
            </mat-table>
          </div>
        </div>
        <div class="row pt-3" *ngIf="isNoDataFound && !isloading">
          <div style="display: flex; justify-content: center; padding-top: 15px;">
            <img src="https://assets.kebs.app/images/no_milestones_v3.png" width="100" height="150">
          </div>
        </div>
      </mat-tab>
      <mat-tab label="Leave History">
        <div class="d-flex justify-content-center mt-3" *ngIf="isloading">
          <mat-spinner matTooltip="Please wait..." diameter="30"> </mat-spinner>
        </div>
        <div class="row pt-3" *ngIf="!isNoDataFound">
          <div class="col-12 p-0">
            <mat-table [dataSource]="tableDataSource1" matSort (matSortChange)="sortDataHistory()"  style="margin-left: 20px; margin-right: 20px;"
              class="mat-elevation-z0 slide-from-down">
              <ng-container *ngFor="let col of columns1" [matColumnDef]="col.colKey">
                <mat-header-cell *cdkHeaderCellDef mat-sort-header>
                  <span class="header-text">{{
                    col.colName
                    }}</span>
                </mat-header-cell>
                <mat-cell [ngClass]="col.className" [matTooltip]="col.isToolTip ? col?.toolTipVal(row) : ''"
                  *cdkCellDef="let row">
                  <span class="overflow-ctrl">{{ col.cell(row) }}</span>
                </mat-cell>
              </ng-container>

              <mat-header-row *matHeaderRowDef="displayedColumns1"></mat-header-row>
              <mat-row *matRowDef="let row; columns: displayedColumns1"></mat-row>
            </mat-table>
          </div>
        </div>
        <div class="row pt-3" *ngIf="isNoDataFound && !isloading">
          <div style="display: flex; justify-content: center; padding-top: 75px; padding-left: 150px;">
            <img src="https://assets.kebs.app/images/no_milestones_v3.png" width="200" height="150">
          </div>
        </div>

      </mat-tab>
    </mat-tab-group>
  </div>
</div>