import { Component, Inject, OnInit, ViewChild } from '@angular/core';
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { LeaveAppService } from 'src/app/modules/leave-app/services/leave-app.service';
import { ErrorService } from 'src/app/services/error/error.service';
import { FileSaverService } from 'src/app/services/fileSaver/file-saver.service';
import { UtilityService } from 'src/app/services/utility/utility.service';
import { takeUntil } from 'rxjs/operators';
import { Subject } from 'rxjs';
import { ToasterService } from 'src/app/services/toaster-service/toaster.service';
import {MatSort, Sort} from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { ToastService } from '../../../lazy-loaded/toast-message/toast.service';
import { ErrorPopupComponent } from '../../../lazy-loaded/error-popup/error-popup.component';
import * as moment from 'moment';

export interface leaveBalanceDetailsDataType {
  leave_type: string;
  leave_balance: number;
  leaves_taken: number;
}

export interface leaveDetailsDataType {
  leave_type: string;
  leave_start_date: Date;
  leave_end_date: Date;
  no_of_absent_days: number;

}
@Component({
  selector: 'app-approver-detail-modal',
  templateUrl: './approver-detail-modal.component.html',
  styleUrls: ['./approver-detail-modal.component.scss']
})
export class ApproverDetailModalComponent implements OnInit {

  deatilViewData;

  rejectClicked: boolean = false;

  reason: string ="";

  uiConfigData: any;

  attachmentPluginConfig = {
    'destinationBucket': '',
    'routingKey': '',
    'allowEdit': false,
  };

  disableBtn: boolean = false;

  protected _onDestroy = new Subject<void>();

  displayedColumns = [];
  displayedColumns1 = [];
  tableData: leaveBalanceDetailsDataType[];
  tableData1: leaveDetailsDataType[];
  tableDataSource: any;
  tableDataSource1: any;
  @ViewChild(MatSort) sort: MatSort;
  @ViewChild(MatSort) sort1: MatSort;

  columns = [
    {
      colKey: 'leave_type',
      colName: 'Leave Type',
      className: 'tab-text',
      isToolTip: true,
      cell: (element: leaveBalanceDetailsDataType) =>
        `${element.leave_type ? element.leave_type : '-'}`,
      toolTipVal: (element) => `${element.leave_type}`,
    },
    {
      colKey: 'leave_balance',
      colName: 'Leave Balance',
      className: 'tab-text',
      isToolTip: true,
      cell: (element: leaveBalanceDetailsDataType) =>
        `${element.leave_balance ? element.leave_balance : '-'}`,
      toolTipVal: (element) =>
        `${element.leave_balance ? element.leave_balance : '-'}`,
    },
    {
      colKey: 'leaves_taken',
      colName: 'Leave Taken',
      className: 'tab-text',
      isToolTip: true,
      cell: (element: leaveBalanceDetailsDataType) =>
        `${element.leaves_taken ? element.leaves_taken : '-'}`,
      toolTipVal: (element) =>
        `${element.leaves_taken ? element.leaves_taken : '-'}`,
    },
  ];

  columns1 = [
    {
      colKey: 'leave_type',
      colName: 'Leave Type',
      className: 'tab-text',
      isToolTip: true,
      cell: (element: leaveDetailsDataType) =>
        `${element.leave_type ? element.leave_type : ''}`,
      toolTipVal: (element) => `${element.leave_type}`,
    },
    {
      colKey: 'leave_start_date',
      colName: 'Start Date',
      className: 'tab-text',
      isToolTip: true,
      cell: (element: leaveDetailsDataType) =>
        `${
          element.leave_start_date
            ? moment(element.leave_start_date).format('DD MMM YYYY')
            : ''
        }`,
      toolTipVal: (element) =>
        `${
          element.leave_start_date
            ? moment(element.leave_start_date).format('DD MMM YYYY')
            : ''
        }`,
    },
    {
      colKey: 'leave_end_date',
      colName: 'End Date',
      className: 'tab-text',
      isToolTip: true,
      cell: (element: leaveDetailsDataType) =>
        `${
          element.leave_end_date
            ? moment(element.leave_end_date).format('DD MMM YYYY')
            : ''
        }`,
      toolTipVal: (element) =>
        `${
          element.leave_end_date
            ? moment(element.leave_end_date).format('DD MMM YYYY')
            : ''
        }`,
    },
    {
      colKey: 'no_of_absent_days',
      colName: 'Absent Days',
      className: 'tab-text',
      isToolTip: true,
      cell: (element: leaveDetailsDataType) =>
        `${element.no_of_absent_days ? element.no_of_absent_days : ''}`,
      toolTipVal: (element) =>
        `${element.no_of_absent_days ? element.no_of_absent_days : ''}`,
    },
  ];

  employeeLeaveBalanceDetails: any = [];

  employeeLeaveHistoryDetails: any = [];

  isNoDataFound: boolean = false;

  isloading: boolean = true;

  status: String;


  constructor(private dialogRef: MatDialogRef<ApproverDetailModalComponent>,
    @Inject(MAT_DIALOG_DATA) public deatilModalData: any,
    public dialog: MatDialog,
    private _leaveAppService: LeaveAppService,
    private errorService: ErrorService,
    private utilityService: UtilityService, 
    private _fileSaver : FileSaverService,
    private toastService: ToasterService,
    private toastmessage: ToastService) { }

  ngOnInit(): void {

    this.displayedColumns = [];

    this.displayedColumns1 = [];

    this.deatilViewData = this.deatilModalData.modalParams.data;

    this.attachmentPluginConfig = this.deatilModalData.modalParams.s3BucketConfig;

    this.status = this.deatilModalData.modalParams.status;

    this.isloading = true;

    this.getEmployeeLeaveBalanceFromE360();
    
  }
  // Function to close the approval-detail-dialog
  closeForm()
  {
    this.dialogRef.close();
  }
  // Function to click the reject option
  openReject()
  {
    this.rejectClicked = true;
  }
  // Function to download the attachment uploaded by the user
  downloadFile(index)
  {
    let file = this.deatilViewData.attachments[index]

    this._leaveAppService.getLeaveAttachment(file.key)
    .pipe(takeUntil(this._onDestroy))
      .subscribe(async res => {
        if (res["messType"] == 'S') {
          if (file.type == 'image/png' || file.type == 'image/jpg' || file.type == 'image/jpeg' || file.type == 'application/pdf') {
            let pdfWindow = window.open("");
            pdfWindow.document.write(`<iframe width=100% height=100% src=data:${file.type};base64,${res["data"]["fileData"]}></iframe>`);
            this._fileSaver.saveAsFile(res["data"]["fileData"], file.fileName, file.type);
          }
          else
            this._fileSaver.saveAsFile(res["data"]["fileData"], file.fileName, file.type);
        }
        else{
          this.toastmessage.showError(res['messText']);
        }
      },
      err => {
        let modalParams = err.error;
        this.dialog.open(ErrorPopupComponent, {
          width:'40%',
          minHeight:'250px',
          data: { modalParams: modalParams }
        });
      });
  }
  // Funcion to approver / reject the leave request, if rejecting the leave request, ask for reason
  updateLeaveRequest(statusId){
    if(statusId == 'R')
    {
      if(this.reason != "")
      {
        this.approveOrRejectLeaveRequest(statusId);
      }
      else{
        this.toastmessage.showWarning("Kindly Enter The Reason For Rejection",5000);
      }
    }else{
      this.approveOrRejectLeaveRequest(statusId);
    }
    
  }
  // Function to approve / reject the leave request that is created by the user 
  approveOrRejectLeaveRequest(statusId)
  {
    this.disableBtn = true;
    this._leaveAppService.updateLeaveRequestStatus(this.deatilViewData.associateOid, this.deatilViewData.workflowHeaderId, statusId , this.reason)
    .pipe(takeUntil(this._onDestroy))
    .subscribe(async(res) =>{
      if(res['messType'] == 'S')
      {
        this.toastmessage.showSuccess(res['messText'], 5000);
        this.disableBtn = false;
        this.dialogRef.close({event: "close"});
      }
      else{
        this.disableBtn = false;
        this.toastmessage.showError(res['messText']);
      }
    },
    err => {
      let modalParams = err.error;
      this.dialog.open(ErrorPopupComponent, {
        width:'40%',
        minHeight:'250px',
        data: { modalParams: modalParams }
      });

    })
  }

  getFormControlValue(formControlName, data) {
    let val = data;
    return val ? val : null;
  }

  setClickedTab(selectedItem)
  {
    let selectedTab = selectedItem.tab.textLabel;
    if(selectedTab == "Leave Balance Details")
    {
      this.getEmployeeLeaveBalanceFromE360()
    }
    else{
      this.getEmployeeLeaveDetailsFromE360();
    }

  }

  getEmployeeLeaveBalanceFromE360(){
    this.displayedColumns = [];
    this.displayedColumns1 = [];
    this.isloading = true;
    this._leaveAppService.getEmployeeLeaveBalanceFromE360(this.deatilViewData.empId)
    .pipe(takeUntil(this._onDestroy))
    .subscribe(async(res) =>{
      if(res['data'] && res['messType'] == 'S' && res['data'].length > 0){
        this.isloading = false;
        this.employeeLeaveBalanceDetails = res['data'];
        this.isNoDataFound = false;
        this.tableData = this.employeeLeaveBalanceDetails;
        this.tableDataSource = new MatTableDataSource(this.tableData);
        this.displayedColumns = this.columns.map((c) => c.colKey);
      }else{
        this.isloading = false;
        this.toastmessage.showInfo(res['messText'], 5000);
        this.isNoDataFound = true;
      }
    }, err => {
      let modalParams = err.error;
      this.dialog.open(ErrorPopupComponent, {
        width:'40%',
        minHeight:'250px',
        data: { modalParams: modalParams }
      });

    })
  }

  getEmployeeLeaveDetailsFromE360(){
    this.displayedColumns1 = [];
    this.displayedColumns = [];
    this.isloading = true;
    this._leaveAppService.getEmployeeLeaveDetailsFromE360(this.deatilViewData.empId)
    .pipe(takeUntil(this._onDestroy))
    .subscribe(async(res) =>{
      if(res['data'] && res['messType'] == 'S' && res['data'].length > 0){
        this.isloading = false;
        this.employeeLeaveHistoryDetails = res['data'];
        this.isNoDataFound = false;
        this.tableData1 = this.employeeLeaveHistoryDetails;
        this.tableDataSource1 = new MatTableDataSource(this.tableData1);
        this.displayedColumns1 = this.columns1.map((c) => c.colKey);
      }else{
        this.isloading = false;
        this.toastmessage.showInfo(res['messText'], 5000);
        this.isNoDataFound = true;
      }
    }, err => {
      let modalParams = err.error;
      this.dialog.open(ErrorPopupComponent, {
        width:'40%',
        minHeight:'250px',
        data: { modalParams: modalParams }
      });

    })
  }

  sortData() {
    console.log(this.tableDataSource)
    this.tableDataSource.sort = this.sort;
  }

  sortDataHistory() {
    console.log(this.tableDataSource1)
    this.tableDataSource1.sort = this.sort1;
  }


  ngOnDestroy() {
    this._onDestroy.next();
    this._onDestroy.complete();
  }

}
