import { Component, Inject, OnInit } from '@angular/core';
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { LeaveAppService } from 'src/app/modules/leave-app/services/leave-app.service';
import { ErrorService } from 'src/app/services/error/error.service';
import { UtilityService } from 'src/app/services/utility/utility.service';
import { takeUntil } from 'rxjs/operators';
import { Subject } from 'rxjs';
import { ToasterService } from 'src/app/services/toaster-service/toaster.service';
import { ToastService } from '../../../lazy-loaded/toast-message/toast.service';
import { ErrorPopupComponent } from '../../../lazy-loaded/error-popup/error-popup.component';
@Component({
  selector: 'app-approver-reject-dialog',
  templateUrl: './approver-reject-dialog.component.html',
  styleUrls: ['./approver-reject-dialog.component.scss']
})
export class ApproverRejectDialogComponent implements OnInit {

  reason: string ="";

  deatilViewData;

  disableBtn: boolean = false;

  protected _onDestroy = new Subject<void>();

  constructor(private dialogRef: MatDialogRef<ApproverRejectDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public rejectModalData: any,
    public dialog: MatDialog,
    private _leaveAppService: LeaveAppService,
    private errorService: ErrorService,
    private utilityService: UtilityService,
    private toastService: ToasterService,
    private toastmessage: ToastService) { }

  ngOnInit(): void {
    this.deatilViewData = this.rejectModalData.modalParams.data;
    console.log(this.deatilViewData)
  }
  // Function to close the reject dialog
  closeModal()
  {
    this.dialogRef.close({ event: 'close' });
  }
  // Function to reject the leave request that is applied by the user 
  rejectLeaveRequest(){
    if(this.reason != ""){
    this._leaveAppService.updateLeaveRequestStatus(this.deatilViewData.associateOid, this.deatilViewData.workflowHeaderId, 'R', this.reason)
    .pipe(takeUntil(this._onDestroy))
    .subscribe(async(res) =>{
      if(res['messType'] == 'S')
      {
        this.dialogRef.close({ event: 'close' });
        this.toastmessage.showSuccess( res['messText'], 5000);
      }
      else{
        this.toastmessage.showError(res['messText']);
      }
    },
    err => {
      let modalParams = err.error;
      this.dialog.open(ErrorPopupComponent, {
        width:'40%',
        minHeight:'250px',
        data: { modalParams: modalParams }
      });

    })
  }
  else{
    this.toastmessage.showWarning('Kindly Enter the reason for rejection',5000);
  }
}

ngOnDestroy() {
  this._onDestroy.next();
  this._onDestroy.complete();
}

}
