<div class="container-fluild teams-calendar-styles">
    <div class="col-12 pl-3 pt-3 pb-3" style="display: flex;">
        <span class="headerName">Team Calendar</span>
        <div class="undoIcon">
            <mat-icon> undo </mat-icon>
        </div>
        <button matTooltip="Back To Leave Requests" (click)="closeDialog()" class="backToLeave">
            <span>Back To Leave Requests</span>
        </button>
    </div>

    <mat-divider></mat-divider>
    <div class="dialog">
        <div class="col-10">
            <div class="row" style="align-items: center!important;height:53px !important">
                <div class="col-3" style="display:flex;">
                    <button mat-icon-button matTooltip="Previous Month" [matTooltipPosition]="'above'"
                        class="iconsSize ml-2 my-auto" (click)="previousmonth()" style="color:#526179">
                        <mat-icon>
                            chevron_left
                        </mat-icon>
                    </button>
                    <div class="pl-3 pt-3"
                        style="font-family: 'Roboto';font-style: normal;font-weight: 700;font-size: 12px;line-height: 16px;letter-spacing: 0.02em;text-transform: capitalize;color: #6E7B8F;">
                        <p>{{date.format('MMMM ')}}{{date.format('YYYY ')}}</p>
                    </div>
                    <div class="pl-3 pt-1">
                        <button mat-icon-button matTooltip="Next Month" [matTooltipPosition]="'above'"
                            class="iconsSize ml-0 my-auto" (click)="nextmonth()" style="color:#526179">
                            <mat-icon class="pl-0 ml-0">
                                chevron_right
                            </mat-icon>
                        </button>
                    </div>
                </div>

                <!-- <div class="col-1 ml-2">

                </div> -->

                <div class="col-2" style="border:1px solid #DADCE2 !important; border-radius: 5px; color: #6E7B8F;">
                    <mat-form-field class="calendar-styles" style="width: 100% !important;">
                        <input id="monthYearDpInput" class="ib13" matInput [matDatepicker]="monthYearDp"
                            [formControl]="monthYearDate" readonly />
                        <mat-datepicker-toggle matPrefix matTooltip="Choose Month" [for]="monthYearDp">
                        </mat-datepicker-toggle>
                        <mat-datepicker #monthYearDp disabled="false" startView="year"
                            (yearSelected)="selectYear($event)" (monthSelected)="selectMonth($event, monthYearDp)"
                            panelClass="example-month-picker">
                        </mat-datepicker>
                    </mat-form-field>
                </div>
               
                <div class="col-3 d-flex">
                    <mat-label class="viewText">View</mat-label>
                    <button [matMenuTriggerFor]="menu"
                        style="border:1px solid #DADCE2 !important;background-color:transparent;border-radius:5px; color: #6E7B8F;">
                        <span class="d-flex">
                            <div class="allButton" [matTooltip]="displayName">{{displayName}}</div>
                            <div class="dropDownArrow" mat>
                                <mat-icon>arrow_drop_down</mat-icon>
                            </div>
                        </span>
                    </button>
                    <mat-menu #menu="matMenu" xPosition="before">
                        <div style="padding-left: 15px; padding-top: 10px; font-family: 'Roboto'; font-style: normal; font-weight: 500; font-size: 13px; line-height: 16px; letter-spacing: 0.02em; text-transform: capitalize; color: #8B95A5;">Leave Type</div>
                        <button mat-menu-item *ngFor="let items of leaveDetails" class="menu-text"
                            (click)="filterSelectedData(items, 0)">{{items.name}}</button>
                        <mat-divider style="margin-left: 10px; margin-right: 10px"></mat-divider>
                        <div style="padding-left: 15px; padding-top: 10px; font-family: 'Roboto'; font-style: normal; font-weight: 500; font-size: 13px; line-height: 16px; letter-spacing: 0.02em; text-transform: capitalize; color: #8B95A5;">Status</div>
                        <button mat-menu-item *ngFor="let items of statusDetails" class="menu-text"
                            (click)="filterSelectedData(items, 1)">{{items.status_name}}</button>
                        <mat-divider style="margin-left: 10px; margin-right: 10px"></mat-divider>
                        <button mat-menu-item class="menu-text" (click)="filterSelectedData('', 2)">Clear All</button>
                        <mat-divider style="margin-left: 10px; margin-right: 10px;"></mat-divider>
                    </mat-menu>
                </div>
                <div class="col-3 pt-2 d-flex">
                    <mat-form-field appearance="outline">
                        <mat-label>Employees</mat-label>
                        <mat-select #select multiple [(ngModel)]="employeeList" (selectionChange)="filterSelectedData($event,3)">
                          <div style="margin: 5px 17px;">
                              <mat-checkbox [(ngModel)]="selectedEmployees"
                                              [ngModelOptions]="{standalone: true}"
                                              (change)="toggleAllSelection()">Select All</mat-checkbox>
                          </div>
                          <mat-option (click)="optionClick(data)" *ngFor="let data of reportingEmployees" [value]="data.associate_id">
                            {{data.employee_name}}
                          </mat-option>
                        </mat-select>
                      </mat-form-field>
                </div>
            </div>
        </div>
        <div class="col-12 pt-4 d-flex">
            <div class="row">
                <div [ngClass]="days.class" *ngFor="let days of weekdays; let i = index">
                    <span>
                        {{days.dayName}}
                    </span>
                </div>
            </div>
        </div>

        <div class="col-12 pb-2">
            <div class="row">
                <div class="col-9">
                    <div class="container-fluid">
                        <div class="flex-container flex-center flex-wrap">
                            <div *ngFor="let calendarItem of calendar; let i = index">
                                <div class="flex-container">
                                    <div class="calendar-days" *ngFor="let day of calendarItem"
                                        [ngStyle]="{ 'background': showDetails && day.date == selectedDate ? '#F6F6F7' : 'none' }"
                                        (click)="displayLeaveDetailsOnClick(day.employeeLeaveDetails, day.date)">
                                        <button class="dateButton"
                                            [ngStyle]="{color: day.isWeekend && !day.isInMonth ? '#B9C0CA' : '#45546E'}">
                                            <div [ngClass]="day.className">
                                                <div [class.weekend]="day.isWeekend" style="display:flex;">
                                                    <div class="displayMonth">
                                                        {{day.monthName}}
                                                    </div>
                                                    <div class="displayDays">
                                                        {{day.day}}
                                                    </div>
                                                </div>

                                            </div>
                                        </button>
                                        <div style="text-align: left;"
                                            *ngFor="let employeeDetails of day.employeeLeaveDetails; let i = index">
                                            <div *ngIf="day.employeeLeaveDetails.length <= 3" style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                                                <span class="employee"
                                                    [ngStyle]="{color: employeeDetails.leaveColor}" [matTooltip]="employeeDetails.employee_name">{{employeeDetails.employee_name}}</span>
                                                <mat-divider *ngIf="day.employeeLeaveDetails.length > 1"></mat-divider>
                                            </div>
                                            <div
                                                *ngIf="day.employeeLeaveDetails.length > 3" style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                                                <span *ngIf="i < 2" class="employee"
                                                    [ngStyle]="{color: employeeDetails.leaveColor}" [matTooltip]="employeeDetails.employee_name">{{employeeDetails.employee_name}}</span>
                                                <mat-divider *ngIf="day.employeeLeaveDetails.length > 1"></mat-divider>
                                                <span class="employee" style="color: #5F6C81;"
                                                    *ngIf="i==2 && day.employeeLeaveDetails.length > 3">
                                                    {{day.employeeLeaveDetails.length - 2}} + More
                                                </span>

                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-3" style="padding-left: 80px;" *ngIf="showDetails">
                    <div class="d-flex">
                        <mat-icon style="color: #526179; font-size: 20px;">calendar_today</mat-icon>
                        <div class="dateValue">{{clickedDate}}</div>
                    </div>
                    <div class="pt-4" *ngFor="let leaves of selectedDateLeaveDetails | keyvalue">
                        <span class="leaveType">{{leaves.key}}</span>
                        <div class="pt-2 d-flex" *ngFor="let items of leaves.value">
                            <app-user-image content-type="template" max-width="300" placement="top" style="margin: 2px;"
                                imgWidth="23px" imgHeight="23px" [id]="items.oid">
                            </app-user-image>
                            <div style="padding-left: 10px;">
                                <div class="approverName">{{items.employee_name}}</div>
                                <div class="approverStatus" [ngStyle]="{'color': items.statusColor}">
                                    {{items.leaveStatus}}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-3" style="padding-left: 60px;" *ngIf="!showDetails">
                    <div style="padding-top: 200px; padding-left: 50px;">
                        <img src="https://assets.kebs.app/images/pointer001.png">
                        <div class="text">
                            Click a date to <br>
                            see the details
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>