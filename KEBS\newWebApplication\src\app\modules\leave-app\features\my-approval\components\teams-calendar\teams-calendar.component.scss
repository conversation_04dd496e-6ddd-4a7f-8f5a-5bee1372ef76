.teams-calendar-styles {

    background: white;

    .select-all{
        margin: 5px 17px;
      }

    .dialog {
        background: white;
        border-radius: 5px;
    }

    .calendar-days {
        width: 130px;
        height: 120px;
        background: #fff;
        border: 1px solid #DADCE2;
        padding: 3px;
        text-align: end;
        line-height: 1.9;
        font-family: '<PERSON><PERSON>';
    }

    .weekend {
        color: #B9C0CA;
        text-align: center;
        line-height: 2.5;
    }

    .sunday {
        width: 190px;
    }

    h1 {
        color: #00bcd4;
    }

    .fill-height-or-more {
        min-height: 100%;
    }

    .flex-footer-pos {
        flex: 1 0 auto;
    }

    .flex-container {
        display: -webkit-flex;
        display: flex;
    }

    .flex-1 {
        flex: 1;
    }

    .flex-left {
        -webkit-align-self: flex-start;
        align-self: flex-start;
    }

    .flex-column {
        -webkit-flex-direction: column;
        flex-direction: column;
    }

    .flex-center {
        -webkit-align-items: center;
        align-items: center;
    }

    .flex-center-horz {
        -webkit-justify-content: center;
        justify-content: center;
    }

    .flex-center-vert {
        -webkit-align-items: center;
        align-items: center;
    }

    .flex-left-row {
        -webkit-justify-content: flex-start;
        justify-content: flex-start;
    }

    .flex-right-row {
        -webkit-justify-content: flex-end;
        justify-content: flex-end;
    }

    .flex-right-column {
        -webkit-align-items: flex-end;
        align-items: flex-end;
    }

    .flex-left-column {
        -webkit-align-items: flex-start;
        align-items: flex-start;
    }

    .flex-wrap {
        flex-wrap: wrap;
    }

    .flex-space-between-row {
        justify-content: space-between;
        -webkit-justify-content: space-between;
    }

    .flex-space-between-column {
        align-items: space-between;
        -webkit-align-itemst: space-between;
    }

    .width-half {
        width: 50%;
    }

    .width-5 {
        width: 5%;
    }

    .width-10 {
        width: 10%;
    }

    .width-20 {
        width: 20%;
    }

    .width-25 {
        width: 25%;
    }

    .width-30px {
        width: 30px;
    }

    .width-30 {
        width: 30%;
    }

    .width-40 {
        width: 40%;
    }

    .width-60 {
        width: 60%;
    }

    .width-70 {
        width: 70%;
    }

    .width-75 {
        width: 75%;
    }

    .width-80 {
        width: 80%;
    }

    .width-90 {
        width: 90%;
    }

    .width-full {
        width: 100% !important;
    }

    .width-auto {
        width: auto;
    }

    .purple-bkgrd {
        background: #752c8f;
    }

    .text-fade {
        opacity: 0.4;
        color: #fff;
    }

    .white-Text {
        color: #fff;
    }

    .margin-bottom-20 {
        margin-bottom: 20px;
    }

    .marg-left-20 {
        margin-left: 20px;
    }

    .marg-left-35 {
        margin-left: 35px;
    }

    .marg-right-25 {
        margin-right: 25px;
    }

    .width-25 {
        width: 25%;
    }

    .width-100 {
        width: 100%;
    }

    .height-10 {
        height: 10%;
    }

    .height-20px {
        height: 20px;
    }

    .height-15 {
        height: 15%;
    }

    .height-20 {
        height: 20%;
    }

    .height-30 {
        height: 30%;
    }

    .height-25 {
        height: 25%;
    }

    .height-50 {
        height: 50%;
    }

    .height-70 {
        height: 70%;
    }

    .height-75 {
        height: 75%;
    }

    .height-80 {
        height: 80%;
    }

    .height-100 {
        height: 100%;
    }

    .padding-above-5 {
        padding-top: 5px;
    }

    .padding-above-15 {
        padding-top: 15px;
    }

    .padding-above-30 {
        padding-top: 30px;
    }

    .padding-above-50 {
        padding-top: 50px;
    }

    .padding-below-30 {
        padding-bottom: 30px;
    }

    .margin-top-5 {
        margin-top: 5px;
    }

    .margin-top-10 {
        margin-top: 10px;
    }

    .margin-top-15 {
        margin-top: 15px;
    }

    .margin-top-30 {
        margin-top: 30px;
    }

    .margin-left-20 {
        margin-left: 20px;
    }

    .bigitem {
        -webkit-flex: 5.5 0 0;
        flex: 5.5 0 0;
    }

    .smallitem {
        -webkit-flex: 1 0 0;
        flex: 1 0 0;
        min-width: 234px;
    }

    .icon-md {
        font-size: 20px;
    }

    .parentSelect {
        width: 170px;
    }


    /* remove the original arrow */



    .pointer {
        cursor: pointer;
    }

    .floatRight {
        float: right;
    }

    .error-text {
        color: #f6ae39 !important;
    }

    .bring-to-front {
        position: absolute;
        z-index: 1;
    }

    .overflow-hidden {
        overflow: hidden;
    }



    #title-heading {
        text-shadow: 2px 2px white;
    }

    .main-container {
        background: white;
    }

    .weekday {
        width: auto;
        height: 113px;
        line-height: 1.9;
    }

    .calendar-styles {
        font-size: 18px !important;

        ::ng-deep .mat-form-field-infix {
            padding: 0 !important;
            border-top: 0 !important;
            padding-bottom: 5px !important;
        }

        ::ng-deep .mat-form-field-wrapper {
            padding-bottom: 0px !important;

            ::ng-deep .mat-form-field-underline {
                height: 0 !important;
            }
        }
    }

    .ib13 {
        font-size: 14px;
        font-weight: normal;
        text-align: center;
        color: #6E7B8F;
        display: inline;
        padding-top: 10px;
    }

    .arrow-icons {
        color: black;
        font-size: 18px;
    }

    // .weekend{
    // background: #f7f7f7;
    // width: 195px;
    // height: 120px;
    // padding: 5px;
    // margin-top:5px;
    // text-align:center;
    // line-height: 2.5;
    // }
    // .weekend{
    //     background: #f7f7f7;
    //     width: 195px;
    //      height: 120px;
    //      padding: 5px;
    //      margin-top:5px;
    //       text-align:center;
    //     line-height: 2.5;
    // }

    .headerName {
        padding-left: 27px;
        height: 32px;
        font-family: 'Roboto';
        font-style: normal;
        font-weight: 700;
        font-size: 20px;
        line-height: 32px;
        align-items: center;
        text-transform: capitalize;
        color: #26303E;
    }

    .undoIcon {
        padding-top: 6px;
        padding-left: 30px;
        font-family: 'Roboto';
    }

    .backToLeave {
        font-family: 'Roboto';
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 24px;
        padding-top: 6px;
        letter-spacing: 0.02em;
        text-transform: capitalize;
        color: #5F6C81;
        padding-left: 10px;
        border: none;
        background-color: transparent;
        height: 20px;
    }

    ::placeholder {
        font-size: 10px;
    }

    .dateButton {
        border: none;
        cursor: pointer;
        background-color: transparent;
        text-align: right;
    }

    .previous-month,
    .next-month {
        color: gray
    }

    .in-month {
        color: #45546E;
    }

    ::ng-deep .ib13[_ngcontent-pqt-c655] {
        font-size: 14px;
        font-weight: normal;
        text-align: center;

        color: #1a1a1a;
        display: inline;
    }

    .viewText {
        font-family: 'Roboto';
        font-style: normal;
        font-weight: 500;
        font-size: 12px;
        line-height: 16px;
        letter-spacing: 0.02em;
        text-transform: capitalize;
        padding-top: 11px;
        padding-right: 10px;
        color: #5F6C81
    }

    .displayMonth {
        padding-right: 4px !important;
        height: 16px;
        font-family: 'Roboto';
        font-style: normal;
        font-weight: 500;
        font-size: 13px;
        line-height: 16px;
        text-align: right;
        letter-spacing: 0.02em;
        text-transform: capitalize;
    }

    .displayDays {
        height: 16px;
        font-family: 'Roboto';
        font-style: normal;
        font-weight: 500;
        font-size: 13px;
        line-height: 16px;
        text-align: right;
        letter-spacing: 0.02em;
        text-transform: capitalize;
    }

    .dropDownArrow {
        padding-left: 50px !important;
        padding-top: 2px !important;
    }

    .allButton {
        font-family: 'Roboto';
        font-style: normal;
        font-weight: 500;
        font-size: 13px;
        padding-top: 8px !important;
        line-height: 16px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        width: 105px;
    }

    .titleMenu {
        padding-left: 15px;
        padding-top: 10px;
        font-family: 'Roboto';
        font-style: normal;
        font-weight: 500;
        font-size: 13px;
        line-height: 16px;
        letter-spacing: 0.02em;
        text-transform: capitalize;
        color: #8B95A5;
    }

    .mat-menu-item {
        font-family: 'Roboto';
        font-style: normal;
        font-weight: 500;
        font-size: 12px;
        line-height: 16px;
        letter-spacing: 0.02em;
        text-transform: capitalize;
        color: #526179;
    }

    .days {
        font-family: 'Roboto';
        font-style: normal;
        font-weight: 500;
        font-size: 14px;
        line-height: 16px;
        letter-spacing: 0.02em;
        text-transform: capitalize;
        color: #8B95A5;
        text-align: center;
        width: 130px;
    }

    .tuesday {
        padding-left: 80px;
    }

    .employee {
        font-family: 'Roboto';
        font-style: normal;
        font-weight: 400;
        font-size: 10px;
        line-height: 12px;
        letter-spacing: 0.02em;
        text-transform: capitalize;
        text-align: left;
    }

    .approverName {
        font-family: 'Roboto';
        font-style: normal;
        font-weight: 500;
        font-size: 12px;
        line-height: 16px;
        letter-spacing: 0.02em;
        text-transform: capitalize;
        padding-left: 3px;
        color: #526179;
    }

    .approverStatus {
        font-family: 'Roboto';
        font-style: normal;
        font-weight: 400;
        font-size: 10px;
        line-height: 8px;
        letter-spacing: 0.02em;
        text-transform: capitalize;
        padding-left: 3px;
    }

    .leaveType {
        font-family: 'Roboto';
        font-style: normal;
        font-weight: 500;
        font-size: 10px;
        line-height: 16px;
        letter-spacing: 0.02em;
        text-transform: capitalize;
        color: #8B95A5;
    }

    .dateValue {
        font-family: 'Roboto';
        font-style: normal;
        font-weight: 400;
        font-size: 12px;
        line-height: 16px;
        letter-spacing: 0.02em;
        text-transform: capitalize;
        color: #526179;
        line-height: 25px;
        padding-left: 8px;

    }

    .text {
        font-family: 'Roboto';
        font-style: normal;
        font-weight: 400;
        font-size: 12px;
        line-height: 16px;
        padding-left: 10px;
        letter-spacing: 0.02em;
        color: #526179;
    }

    .menu-text {
        font-family: 'Roboto';
        font-style: normal;
        font-weight: 500;
        font-size: 12px;
        line-height: 16px;
        letter-spacing: 0.02em;
        text-transform: capitalize;
        color: #526179;
    }

    ::ng-deep.mat-menu-panel {
        width: 200px;
        height: 400px;
    }
}