import { Component, Inject, OnInit, ViewChild } from '@angular/core';
import { FormControl } from '@angular/forms';
import { <PERSON><PERSON><PERSON>picker, MatDatepickerInputEvent } from '@angular/material/datepicker';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { LeaveAppService } from '../../../../services/leave-app.service';
import * as moment from 'moment';
import { takeUntil } from 'rxjs/operators';
import { ErrorService } from 'src/app/services/error/error.service';
import { Subject } from 'rxjs';
import { ToasterService } from 'src/app/services/toaster-service/toaster.service';
import { MomentDateAdapter, MAT_MOMENT_DATE_ADAPTER_OPTIONS } from '@angular/material-moment-adapter';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE, MatOption } from '@angular/material/core';
import { LoginService } from 'src/app/services/login/login.service';
import _ from 'underscore';
import { Location } from '@angular/common';
import { ToastService } from '../../../lazy-loaded/toast-message/toast.service';
import { ErrorPopupComponent } from '../../../lazy-loaded/error-popup/error-popup.component';
import { MatDialog } from '@angular/material/dialog';
import { MatSelect } from '@angular/material/select';

export const MONTH_YEAR_DATE_FORMAT = {
  parse: {
    dateInput: 'MMMM YYYY',
  },
  display: {
    dateInput: 'MMMM YYYY',
    monthYearLabel: 'MMM YYYY',
    dateA11yLabel: 'LL',
    monthYearA11yLabel: 'MMMM YYYY',
  },
};
interface CalendarItem {
  day: string;
  dayName: string;
  className: string;
  isWeekend: boolean;
  date: string;
  employeeLeaveDetails: any
}
@Component({
  selector: 'app-teams-calendar',
  templateUrl: './teams-calendar.component.html',
  styleUrls: ['./teams-calendar.component.scss'],
  providers: [
    {
      provide: DateAdapter,
      useClass: MomentDateAdapter,
      deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS]
    },
    { provide: MAT_DATE_FORMATS, useValue: MONTH_YEAR_DATE_FORMAT },
  ]
})

export class TeamsCalendarComponent implements OnInit {
  date = moment();
  monthYearDate = new FormControl(moment());
  calendar: Array<CalendarItem[]> = [];
  datevalue: any;
  formattedMonthYearDate = "";
  searchText = new FormControl();
  monthData: any = [];
  protected _onDestroy = new Subject<void>();
  currentUser: any;
  clickedDate: string;
  selectedDateLeaveDetails;
  showDetails: boolean = false;
  selectedDate: string;
  leaveDetails = [];
  statusDetails = [];
  displayName: String = "All";
  reportingEmployees = [];
  selectedEmployees = false;
  @ViewChild('select') select: MatSelect;
  employeeList: any = [];
  selectedLeaveName: any;
  selectedStatusName: any;


  constructor(
    private _leaveAppService: LeaveAppService,
    private errorService: ErrorService,
    private toastService: ToasterService,
    private authService: LoginService,
    private location: Location,
    private toastmessage: ToastService,
    public matDialog: MatDialog
  ) { }
  weekdays = [
    {
      dayName: "Sunday",
      class: "pl-5 days"
    },
    {
      dayName: "Monday",
      class: "pl-5 days"
    },
    {
      dayName: "Tuesday",
      class: "pl-5 days"
    },
    {
      dayName: "Wednesday",
      class: "pl-5 days"
    },
    {
      dayName: "Thursday",
      class: "pl-5 days"
    },
    {
      dayName: "Friday",
      class: "pl-5 days"
    },
    {
      dayName: "Saturday",
      class: "pl-5 days"
    }
  ]

  ngOnInit() {

    this._leaveAppService.shouldTabBeVisibleChange(1);
    this.currentUser = this.authService.getProfile().profile;
    this.calendar = this.createCalendar(this.date);
    this.currentMonthData();
    this.getLeaveTypesAndStatus();
    this.getDirectReportingEmployees();

  }
  createCalendar(month: moment.Moment) {

    const daysInMonth = month.daysInMonth();
    const startOfMonth = month.startOf('months').format('ddd');
    const endOfMonth = month.endOf('months').format('ddd');
    const weekdaysShort = moment.weekdaysShort();
    const calendar: CalendarItem[] = [];

    const daysBefore = weekdaysShort.indexOf(startOfMonth);
    const daysAfter = weekdaysShort.length - 1 - weekdaysShort.indexOf(endOfMonth);

    const clone = month.startOf('months').clone();
    if (daysBefore > 0) {
      clone.subtract(daysBefore, 'days');
    }

    for (let i = 0; i < daysBefore; i++) {
      calendar.push(this.createCalendarItem(clone, 'previous-month'));
      clone.add(1, 'days');
    }

    for (let i = 0; i < daysInMonth; i++) {
      calendar.push(this.createCalendarItem(clone, 'in-month'));
      clone.add(1, 'days');
    }

    for (let i = 0; i < daysAfter; i++) {
      calendar.push(this.createCalendarItem(clone, 'next-month'));
      clone.add(1, 'days');
    }
    return calendar.reduce((pre: Array<CalendarItem[]>, curr: CalendarItem) => {
      if (pre[pre.length - 1].length < weekdaysShort.length) {
        pre[pre.length - 1].push(curr);
      } else {
        pre.push([curr]);
      }
      return pre;
    }, [[]]);
  }

  createCalendarItem(data: moment.Moment, className: string) {

    const dayName = data.format('dddd');
    const monthName = data.format('MMM')
    return {
      day: data.format('D'),
      dayName,
      className,
      date: moment(data).format('YYYY-MM-DD'),
      monthName,
      isWeekend: dayName === 'Saturday' || dayName === 'Sunday',
      isInMonth: className === 'in-month',
      employeeLeaveDetails: _.filter(this.monthData, { currentDate: moment(data).format('YYYY-MM-DD') }).length > 0 ? _.filter(this.monthData, { currentDate: moment(data).format('YYYY-MM-DD') }) : []
    };
  }

  public nextmonth() {
    this.filterSelectedData('', 2)
    this.date.add(1, 'months');
    this.calendar = this.createCalendar(this.date);
    this.monthYearDate.patchValue(this.date);
    this.currentMonthData();
    this.showDetails = false;
    this.employeeList = [];
    this.selectedEmployees = false;
    this.selectedLeaveName = undefined;
    this.selectedStatusName = undefined;
  }

  public previousmonth() {
    this.filterSelectedData('', 2);
    this.date.subtract(1, 'months');
    this.calendar = this.createCalendar(this.date);
    this.monthYearDate.patchValue(this.date);
    this.currentMonthData();
    this.showDetails = false;
    this.employeeList = [];
    this.selectedEmployees = false;
    this.selectedLeaveName = undefined;
    this.selectedStatusName = undefined;
  }

  selectYear(normalizedYear: moment.Moment) {

    const monthYearDate = this.monthYearDate.value;

    this.employeeList = [];
    this.selectedEmployees = false;
    this.selectedLeaveName = undefined;
    this.selectedStatusName = undefined;

    if (monthYearDate.year() != normalizedYear.year()) {

      monthYearDate.year(normalizedYear.year());

      this.changeMonthYear(monthYearDate);

    }

  }

  selectMonth(normalizedMonth: moment.Moment, datepicker: MatDatepicker<moment.Moment>) {

    var monthYearDate = this.monthYearDate.value;

    if (monthYearDate.month() != normalizedMonth.month()) {

      monthYearDate.month(normalizedMonth.month()).year(normalizedMonth.year()).date(1);

      this.changeMonthYear(monthYearDate);
    }

    this.employeeList = [];
    this.selectedEmployees = false;

    datepicker.close();

  }
  changeMonthYear(monthYearDate) {
    this.filterSelectedData('', 2)
    this.formattedMonthYearDate = monthYearDate.hour(15).format("YYYY-MM");
    this.monthYearDate.setValue(monthYearDate);
    this.date = moment(this.formattedMonthYearDate);
    this.calendar = this.createCalendar(moment(this.formattedMonthYearDate));
    this.currentMonthData();
    this.showDetails = false;
    this.employeeList = [];
    this.selectedEmployees = false;
  }

  closeDialog() {
    this.location.back();
  }
  displayLeaveDetailsOnClick(leaveDetails, date) {
    this.selectedDate = date;
    this.showDetails = true;
    this.clickedDate = moment(date).format("DD MMM YYYY");
    this.selectedDateLeaveDetails = _.groupBy(leaveDetails, "leaveDescription");
  }
  currentMonthData() {
    let startDay = moment(this.date).startOf('month').format('YYYY-MM-DD');
    let endDay = moment(this.date).endOf('month').format('YYYY-MM-DD');
    this._leaveAppService.getTeamsCalenderMonthData(this.currentUser.aid, this.currentUser.oid, startDay, endDay)
      .pipe(takeUntil(this._onDestroy))
      .subscribe(async res => {
        if (res['messType'] == 'S' && res && res['data'].length > 0) {
          this.monthData = res['data'];
          this.bindLeaveDataToCalendar(this.monthData)
          //   for(let leaveDetails of this.monthData){
          //     for(let items of this.calendar)
          //     {
          //       for(let innerItems of items)
          //       {
          //         if(innerItems.date == leaveDetails.currentDate)
          //         {
          //           innerItems.employeeLeaveDetails = _.filter(this.monthData, {currentDate: innerItems.date}).length > 0 ?  _.filter(this.monthData, {currentDate: innerItems.date}) : []

          //         }
          //       }
          //   }
          // }
        }
        else {
          this.monthData = [];
          this.toastmessage.showInfo(res['messText'], 5000);
        }
      }, err => {
        //this.errorService.userErrorAlert((err && err.code) ? err.code : (err && err.error) ? err.error.code : 'NIL', "Something Went Wrong Kindly Try After Some Time", (err && err.params) ? err.params : (err && err.error) ? err.error.params : {})
        let modalParams = err.error;
        this.matDialog.open(ErrorPopupComponent, {
          width:'40%',
          minHeight:'250px',
          data: { modalParams: modalParams }
        });
      })
  }

  ngOnDestroy() {
    this._leaveAppService.shouldTabBeVisibleChange(0);
    this._onDestroy.next();
    this._onDestroy.complete();
  }

  getLeaveTypesAndStatus() {
    this._leaveAppService.getLeaveTypesAndStatus(this.currentUser.oid, this.currentUser.aid)
      .pipe(takeUntil(this._onDestroy))
      .subscribe(async res => {
        if (res['messType'] == 'S') {
          this.leaveDetails = res['leaveDetails'];
          this.statusDetails = res['statusDetails'];
        }
        else {
          this.leaveDetails = res['leaveDetails'];
          this.statusDetails = res['statusDetails'];
          this.toastmessage.showError(res['messText'])
        }
      }, err => {
        //this.errorService.userErrorAlert((err && err.code) ? err.code : (err && err.error) ? err.error.code : 'NIL', "Something Went Wrong Kindly Try After Some Time", (err && err.params) ? err.params : (err && err.error) ? err.error.params : {})
        let modalParams = err.error;
        this.matDialog.open(ErrorPopupComponent, {
          width:'40%',
          minHeight:'250px',
          data: { modalParams: modalParams }
        });
      });
  }

  filterSelectedData(data, type) {
    this.showDetails = false;
    if (type == 0) {
      this.selectedLeaveName = data;
      this.displayName = data.name;
      let result_data = [];
      if(this.employeeList && this.employeeList.length > 0){
        for(let items of this.employeeList)
        {
          result_data = result_data.concat(_.filter(this.monthData, {associate_id: items}));
        }
      }
      this.selectedStatusName = undefined;
      this.bindLeaveDataToCalendar(_.filter(((result_data && result_data.length > 0) || (this.employeeList && this.employeeList.length > 0)) ? result_data : this.monthData, { leaveDescription: data.name }))
    }
    else if(type == 1) {
      let result_data = [];
      this.selectedStatusName = data;
      this.displayName = data.status_name;
      if(this.employeeList && this.employeeList.length > 0){
        for(let items of this.employeeList)
        {
          result_data = result_data.concat(_.filter(this.monthData, {associate_id: items}));
        }
      }
      this.selectedLeaveName = undefined;
      // this.monthData = _.filter(this.monthData, {leaveStatus: data.status_name});
      this.bindLeaveDataToCalendar(_.filter(((result_data && result_data.length > 0) || (this.employeeList && this.employeeList.length > 0)) ? result_data : this.monthData, { leaveStatus: data.status_name }))
    }
    else if(type == 3){
     let result_data = [];
     if(this.employeeList && this.employeeList.length > 0){
      for(let items of this.employeeList)
      {
        result_data = result_data.concat(_.filter(this.monthData, {associate_id: items}));
      }
      if(this.selectedLeaveName != undefined && result_data.length > 0)
      {
        result_data = _.filter((result_data && result_data.length > 0) ? result_data : this.monthData, { leaveDescription: this.selectedLeaveName.name });
      }
      if(this.selectedStatusName != undefined  && result_data.length > 0)
      {
        result_data = _.filter((result_data && result_data.length > 0) ? result_data : this.monthData, { leaveStatus: this.selectedStatusName.status_name });
      }
      this.bindLeaveDataToCalendar(result_data);
    }
    else{
      this.employeeList = [];
      this.selectedEmployees = false;
      if(this.selectedLeaveName != undefined)
      {
        result_data = _.filter((result_data && result_data.length > 0) ? result_data : this.monthData, { leaveDescription: this.selectedLeaveName.name });
      }
      if(this.selectedStatusName != undefined)
      {
        result_data = _.filter((result_data && result_data.length > 0) ? result_data : this.monthData, { leaveStatus: this.selectedStatusName.status_name });
      }
      this.bindLeaveDataToCalendar((this.selectedStatusName != undefined || this.selectedLeaveName != undefined) ? result_data : this.monthData);
    }
    }
    else{
      this.displayName = "All";
      this.showDetails = false;
      this.clickedDate = null;
      this.selectedDate = null;
      this.selectedDateLeaveDetails = [];
      this.selectedLeaveName = undefined;
      this.selectedStatusName = undefined;
      let result_data = [];
      if(this.employeeList && this.employeeList.length > 0){
        for(let items of this.employeeList)
        {
          result_data = result_data.concat(_.filter(this.monthData, {associate_id: items}));
        }
      }
      this.bindLeaveDataToCalendar(((result_data && result_data.length > 0) || (this.employeeList && this.employeeList.length > 0)) ? result_data  : this.monthData)
    } 
  }

  bindLeaveDataToCalendar(data) {
    for (let items of this.calendar) {
      for (let innerItems of items) {
        innerItems.employeeLeaveDetails = [];
      }
    }
    if (data.length > 0) {
      for (let items of this.calendar) {
        for (let innerItems of items) {
          if(_.filter(data, element => {return element.currentDate == innerItems.date}).length > 0) {
            innerItems.employeeLeaveDetails = _.filter(data, { currentDate: innerItems.date }).length > 0 ? _.filter(data, { currentDate: innerItems.date }) : []
          }
        }
      }
    }
    else {
      for (let items of this.calendar) {
        for (let innerItems of items) {
          innerItems.employeeLeaveDetails = [];
        }
      }
    }
  }

  getDirectReportingEmployees(){
    this._leaveAppService.getDirectReportingEmployees(this.currentUser.aid, this.currentUser.oid)
    .pipe(takeUntil(this._onDestroy))
    .subscribe(async(res)=>{
      if(res && res['messType'] == "S")
      {
        this.reportingEmployees = (res['data'] && res['data'].length > 0) ? res['data'] : []
      }
    }, (err)=>{
      let modalParams = err.error;
      this.matDialog.open(ErrorPopupComponent, {
        width:'40%',
        minHeight:'250px',
        data: { modalParams: modalParams }
      });
    })
  }

  toggleAllSelection() {
    if (this.selectedEmployees) {
      this.select.options.forEach((item: MatOption) => item.select());
    } else {
      this.select.options.forEach((item: MatOption) => item.deselect());
    }
  }
   optionClick(aidData) {
    let newStatus = true;
    this.select.options.forEach((item: MatOption) => {
      if (!item.selected) {
        newStatus = false;
      }
    });
    this.selectedEmployees = newStatus;
  }
}