<div class="container-fluild pl-2 pr-2 leave-detail-styles">
    <div class="row p-0">
        <div class="col-12 p-2">
            <div class="row border-bottom solid">
                <div class="col-11 pt-2 pl-0 d-flex">
                    <span class="name my-auto ml-2" [ngStyle]="{'color': leaveColor }">{{leaveName}}</span>
                </div>
                <div class="col-1 d-flex pt-2 pb-2">
                    <button class="view-button-inactive" matTooltip="Close" (click)="closeForm()">
                        Close
                    </button>
                </div>
                <div class="col-12 pl-2">
                    <span class="dateClass">{{uiFormattedDate}}</span>
                </div>
                <div class="col-10 p-2">
                    <button class="statusBtn" [ngStyle]="{'background': statusColor }">
                        {{status}}
                    </button>
                </div>
                <div *ngIf="statusId != 4 && statusId != 3" class="col-2 pl-0" style="padding-top:15px ;"
                    (click)="openWithdrawRequest()">
                    <span class="small-btn">
                        Withdraw Request
                    </span>
                </div>
                <div class="col-12 pl-2 pr-2 pb-2 pt-2 withdrawBox" *ngIf="showWithdrawRequest">
                    <div class="row">
                        <div class="col-12">
                            <mat-icon class="iconButton">info</mat-icon>
                            <span class="text">Do You really wish to cancel the leave request? This can't be
                                undone.</span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-1 pl-4 pb-2 pt-2">
                            <button class="no" (click)="showWithdrawRequest = !showWithdrawRequest" [disabled]="disableBtn">No</button>
                        </div>
                        <div class="col-2 pl-4 pb-2 pt-2">
                            <button class="yes" (click)="withdrawLeaveRequest(status)"  [disabled]="disableBtn" >Yes, Cancel Request</button>
                        </div>
                    </div>
                </div>
                <div class="col-12 pl-2 pr-2 pb-2 pt-2 withdrawBox" *ngIf="showWithdrawMessage">
                    <div class="row">
                        <div class="col-12">
                            <mat-icon class="iconButton">info</mat-icon>
                            <span class="text">{{ withdrawnMessage }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <span class="subHeader">Approvers</span>
        </div>
    </div>
    <div class="col-12 p-2">
        <div class="row">
            <div class="col-12 pl-2 pt-4">
                <div *ngFor="let appr of approvers" style="display: flex;">
                    <app-user-image [tooltip]="approverTooltip" content-type="template" max-width="300" placement="top"
                        style="margin: 2px;" imgWidth="28px" imgHeight="28px" [id]="appr.oid">
                    </app-user-image>
                    <div style="padding-left: 10px; padding-top: 5px;">
                        <div class="approverName">{{appr.name}}</div>
                        <div class="approverStatus" [ngStyle]="{'color': appr.colorCode}">
                            {{appr.status}}
                        </div>
                    </div>


                    <ng-template #approverTooltip placement="top">
                        <div class="row tooltip-text">
                            {{ appr.name }}
                        </div>
                        <div class="row tooltip-text">
                            {{ appr.role }}
                        </div>
                        <div class="row tooltip-text"> Level : {{appr.level}}</div>
                    </ng-template>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-12">
            <span class="subHeader">Reason</span>
        </div>
    </div>
    <div class="col-12 pt-4">
        <div class="row">
            <div class="col-12"
                style="border: 1px solid #DADCE2; border-radius: 5px; height: 70px; background: #F6F6F7; padding-top: 10px;">
                <span class="reasonText">{{reason}}</span>
            </div>
        </div>
    </div>


    <div class="row pb-2 pt-2">
        <div class="col-12">
            <span class="subHeader">Attachments</span>
        </div>
    </div>
    <div class="col-12 pt-3">
        <div class="row">
            <attachment-upload-btn [destinationBucket]="attachmentPluginConfig?.destinationBucket"
                [routingKey]="attachmentPluginConfig?.routingKey" [allowEdit]="false"
                [contextId]="getFormControlValue('attachments')" (change)="changeInattachments($event, 'attachments')">
            </attachment-upload-btn>
        </div>
    </div>

</div>