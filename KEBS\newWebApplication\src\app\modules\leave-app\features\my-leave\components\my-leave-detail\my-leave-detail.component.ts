import { Component, Inject, OnInit } from '@angular/core';
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { Subject } from 'rxjs';
import { LeaveAppService } from 'src/app/modules/leave-app/services/leave-app.service';
import { takeUntil } from "rxjs/operators";
import { UtilityService } from 'src/app/services/utility/utility.service';
import { ErrorService } from 'src/app/services/error/error.service';
import { FileSaverService } from 'src/app/services/fileSaver/file-saver.service';
import _ from 'underscore';
import { ToasterService } from 'src/app/services/toaster-service/toaster.service';
import { ToastService } from '../../../lazy-loaded/toast-message/toast.service';
import { ErrorPopupComponent } from '../../../lazy-loaded/error-popup/error-popup.component';

@Component({
  selector: 'app-my-leave-detail',
  templateUrl: './my-leave-detail.component.html',
  styleUrls: ['./my-leave-detail.component.scss']
})
export class MyLeaveDetailComponent implements OnInit {

  modalParams: any

  leaveName: string 

  leaveDuration: string;

  approvers = [];

  reason: string;

  attachments: any;

  status: string;

  leaveColor: string;

  statusColor: string;

  showWithdrawRequest: boolean = false;

  leaveId: number;

  workflowHeaderId: number;

  protected _onDestroy = new Subject<void>();

  showWithdrawMessage: boolean = false;

  withdrawnMessage: string = "";

  statusId: number;

  uiFormattedDate = [];

  currentUserOid: string;

  isClaim: boolean;

  withdrawReason: String = null;

  attachmentPluginConfig = {
    'destinationBucket': '',
    'routingKey': '',
    'allowEdit': false,
  };
  disableBtn: boolean = false;


  constructor(private dialogRef: MatDialogRef<MyLeaveDetailComponent>, 
    @Inject(MAT_DIALOG_DATA) public detailLeaveData: any,
    public dialog: MatDialog,
    private _leaveAppServie :LeaveAppService,
    private utilityService: UtilityService, 
    private errorService: ErrorService,
    private _fileSaver : FileSaverService,
    private toastService: ToasterService,
    private  toastmessage: ToastService ) { }

  ngOnInit(): void {

    this.modalParams = this.detailLeaveData.modalParams.leaveData;

    this.currentUserOid = this.detailLeaveData.modalParams.currentUserOid;

    this.attachmentPluginConfig = this.detailLeaveData.modalParams.s3BucketConfig;

    this.setDataFromModalParams();

  }
  // Function to set data that is passed from the component to leave-detail-dialog.
  setDataFromModalParams()
  {

    this.leaveName = this.modalParams.leaveType;

    this.approvers = this.modalParams.approvers;

    this.isClaim = this.modalParams.is_claim == 1 ? true : false;

    for(let items of this.approvers)
    {
      if(items.oid == _.filter(this.modalParams.approverStatus, {appr_oid: items.oid})[0].appr_oid)
      {
        items.status = _.filter(this.modalParams.approverStatus, {appr_oid: items.oid})[0].status == 'S' ? 'Pending' 
                      : _.filter(this.modalParams.approverStatus, {appr_oid: items.oid})[0].status == 'A' ? 'Approved' 
                      :  _.filter(this.modalParams.approverStatus, {appr_oid: items.oid})[0].status == 'R' ? 'Rejected' : 'Pending';
        items.colorCode = _.filter(this.modalParams.approverStatus, {appr_oid: items.oid})[0].status == 'S' ? '#FA8C16' 
        : _.filter(this.modalParams.approverStatus, {appr_oid: items.oid})[0].status == 'A' ? '#52C41A' 
        :  _.filter(this.modalParams.approverStatus, {appr_oid: items.oid})[0].status == 'R' ? '#EE4961' : '#FA8C16'
      }
    }

    this.status = this.modalParams.status;

    this.reason = this.modalParams.reason != null ? this.modalParams.reason : 'No Reason Entered';

    this.attachments = this.modalParams.attachments != null ? this.modalParams.attachments : [];

    this.leaveDuration = this.modalParams.dates;
    
    this.leaveColor = this.modalParams.leave_color_code;

    this.statusColor = this.modalParams.status_color_code;

    this.leaveId = parseInt(this.modalParams.id);

    this.workflowHeaderId = parseInt(this.modalParams.workflow_header_id);

    this.statusId = parseInt(this.modalParams.status_id);

    this.uiFormattedDate = this.modalParams.uiFormattedDate;


  }
  // Function to close the leave-detail-dialog
  closeForm()
  {
    this.dialogRef.close();
  }
  // Function to display withdraw ui screen when employee is trying to withdraw the leave request
  openWithdrawRequest()
  {
    this.showWithdrawRequest = true;
  }
  // Function to apply the withdraw leave request, if the leave request is approved, it will ask for reason, or else it will withdraw the leave request
  async withdrawLeaveRequest(status){
    this.disableBtn = true;
    if(status == 'Approved')
    { 
      const { WithdrawReasonModalComponent } = await import('../withdraw-reason-modal/withdraw-reason-modal.component');

      const WithdrawReasonModalComponentData = this.dialog.open(WithdrawReasonModalComponent, {
        height: '35%',
        minWidth: '20%',
        position: { left: '700px', bottom: '400px', right: '400px', top: '220px' },
      });
  
      WithdrawReasonModalComponentData.afterClosed().subscribe(res => {
        if (res.event == "submit") {
            this.withdrawReason = res.reason;
            this.withdrawTheLeaveRequest();   
        }
        if(res.event == "close"){
          this.disableBtn = false;
        }
      });
      WithdrawReasonModalComponentData.backdropClick().subscribe(() => {
        this.disableBtn = false;
      })
      
    }
    else{
      this.withdrawTheLeaveRequest();
  }
  }
  // Function to download the attachment from s3 bucket that is upload for the leave request by the employee
  downloadFile(index)
  {
    let file = this.attachments[index];

    this._leaveAppServie.getLeaveAttachment(file.key)
    .pipe(takeUntil(this._onDestroy))
      .subscribe(async res => {
        if (res["messType"] == 'S') {
          if (file.type == 'image/png' || file.type == 'image/jpg' || file.type == 'image/jpeg' || file.type == 'application/pdf') {
            let pdfWindow = window.open("");
            pdfWindow.document.write(`<iframe width=100% height=100% src=data:${file.type};base64,${res["data"]["fileData"]}></iframe>`);
            this._fileSaver.saveAsFile(res["data"]["fileData"], file.fileName, file.type);
          }
          else
            this._fileSaver.saveAsFile(res["data"]["fileData"], file.fileName, file.type);
        }
        else{
          this.toastmessage.showError(res["messText"]);
        }
      },
      err => {
        let modalParams = err.error;
        this.dialog.open(ErrorPopupComponent, {
          width:'40%',
          minHeight:'250px',
          data: { modalParams: modalParams }
        });
      });
  }
  // Function that perform the employee leave request withdrawal.
  withdrawTheLeaveRequest()
  {
    this._leaveAppServie.withdrawLeaveRequest(this.currentUserOid,this.leaveId, this.workflowHeaderId, this.statusId, this.isClaim, this.withdrawReason)
    .pipe(takeUntil(this._onDestroy))
    .subscribe(async res => {
      if(res['messType'] == 'S')
      {
        this.showWithdrawRequest = false;
        this.showWithdrawMessage = true;
        this.withdrawnMessage = res['messText'];
        this.toastmessage.showSuccess(this.withdrawnMessage, 5000);
        this.dialogRef.close({ event: 'submit', res: res, data: res['data'] });
        this.disableBtn = false;
      }
      else{
        this.toastmessage.showError(res['messText']);
        this.disableBtn = false;
      }
    },err => {
            let modalParams = err.error;
        this.dialog.open(ErrorPopupComponent, {
          width:'40%',
          minHeight:'250px',
          data: { modalParams: modalParams }
        });
    });
  }

  getFormControlValue(formControlName) {
    let val = this.attachments;
    return val ? val : null;
  }

  changeInattachments(e, fieldName) { 
    if (e) {
      if(fieldName == 'attachments') 
        this.attachments.push(e);
      else
        this.toastmessage.showWarning("Attachement configuration not found !",5000);
    }
  }

  ngOnDestroy() {
    this._onDestroy.next();
    this._onDestroy.complete();
  }


}
