<div class="container-fluild pl-2 pr-2 leave-landing-styles">
    <div [ngClass]="currentUserAdmin ? 'col-12 pl-0 pr-2 pt-5' : 'col-12 pl-0 pr-2' ">
        <div class="row">
            <div class="col-7 pl-0" style="display: flex;">
                <div style="display: flex; min-width: 100px; height: 40px; padding-left: 10px;" *ngIf="compensationOff">
                    <button class="tag-btn" matTooltip="Claim Compensation Off" (click)="openCompensationOff()" [disabled]="disableBtn">
                        Claim It
                    </button>
                </div>
                <div style="display: flex; min-width: 100px; height: 40px; padding-left: 10px;" >
                    <button class="tag-btn" matTooltip="Apply Work From Home" (click)="openWorkFromHome()" [disabled]="disableBtn">
                        Apply WFH
                    </button>
                </div>
                <div style="min-width: 120px; height: 40px;" *ngIf="leavePolicy">
                    <button mat-icon-button class="view-button-inactive" matTooltip="Leave Policy" (click)="openLeavePolicy()">
                        <mat-icon class="iconButton">
                            summarize
                        </mat-icon>
                        <span class="valueGrey14ForIcon">Leave Policy</span>
                    </button>
                </div>
                <div style="min-width: 170px; height: 40px;" *ngIf="howToApplyLeave">
                    <button mat-icon-button class="view-button-inactive" matTooltip="How To Apply Leave" (click)="openHowToApplyLeave()">
                        <mat-icon class="iconButton">
                            help
                        </mat-icon>
                        <span class="valueGrey14ForIcon">How To Apply Leave</span>
                    </button>
                </div>
                <div style="min-width: 190px; height: 40px;" *ngIf="showSummary">
                    <button mat-icon-button class="view-button-inactive" matTooltip="Summary">
                        <mat-icon class="iconButton">
                            auto_graph
                        </mat-icon>
                        <span class="valueGrey14ForIcon">Summary</span>
                    </button>
                </div>
            </div>
            <!-- <div  style="width: 460px;"></div> -->
            <div class="col-3">

            </div>
            <!-- <div style="width: 100px;">
                <button mat-flat-button class="leave-btn" (click)="applyLeave()">Apply Leave</button>
            </div> -->
            <div class="col-2 pl-4" *ngIf="applyLeaveBtn">
                <div style="min-width:100px; padding-left: 70px;">
                    <button mat-flat-button class="leave-btn" (click)="applyLeave()" [disabled]="disableBtn">Apply Leave</button>
                </div>
            </div>

        </div>
    </div>

    <div class="col-12 row pl-0 pr-2 pt-2" *ngIf="showSummaryCard">

        <!-- <mat-icon class="matIcon" (click)="appendLastCards()">arrow_back</mat-icon> -->

        <div class="col-2 pb-0 pr-0 pl-2 pt-0" *ngFor="let summaryCardsItem of summaryCards; let statusIndex = index">

            <div *ngIf="statusIndex == 0 && showLeftSummaryCardIcon" style="position: absolute; right: 99%; top: 35%; cursor: pointer;">
                <mat-icon class="matIcon" *ngIf="showSummaryScrollIcon" (click)="appendLastCards()" >arrow_back</mat-icon>
            </div>

            <div class="card data-type-card slide-in-top" *ngIf="summaryCardsItem.isVisible"
                [ngStyle]="{'border-left': '4px solid ' + summaryCardsItem.statusColor}">

                <div class="card-body p-1 cp" content-type="template" *ngIf="!summaryCardsItem.isWithDetailedTooltip"
                    this.displayShadowEffect=true;
                    [matTooltip]="summaryCardsItem.tootltipData">

                    <div class="row d-flex justify-content-left pt-2 pl-2">

                        <span *ngIf="summaryCardsItem.statusColor" class="pl-0 valueGrey14">{{ summaryCardsItem.dataType
                            }}</span>

                        <span *ngIf="!summaryCardsItem.statusColor" class="valueGrey14">{{ summaryCardsItem.dataType
                            }}</span>

                    </div>


                    <div class="row d-flex justify-content-left pt-2 pl-2">

                        <span class="headingBold">
                            {{ summaryCardsItem.dataTypeValue }}
                        </span>


                        <div class="status-circular  justify-content-right" *ngIf="summaryCardsItem.statusColor"
                            [ngStyle]="{ backgroundColor: summaryCardsItem.statusColor, 'position': 'absolute', 'right': '30px' }">
                        </div>


                    </div>

                    <!-- <div class="row d-flex justify-content-left pt-2 pl-2 pb-1">

                        <span class="valueGrey14"
                            [ngStyle]="displayShadowEffect && ind == statusIndex && summaryCardsItem.statusColor  ? {'color' : '#000', 'font-weight': '500'}: ''">
                            View
                        </span>
                        <mat-icon style="font-size: 16px; line-height: 21px; color: #6B7A99;">keyboard_arrow_right
                        </mat-icon>
                    </div>
 -->


                </div>
            </div>

            <div *ngIf="statusIndex == summaryCards.length - 1 && showRightSummaryCardIcon" style="position: absolute; top: 35%; left: 100%; cursor: pointer;">
                <mat-icon *ngIf="showSummaryScrollIcon" class="matIcon" (click)="appedFirstCards()">arrow_forward</mat-icon>
            </div>

        </div>

      

        
    </div>


    <div *ngIf="showLeaveHistory" class="row mt-2 tempCheck" style="margin-left: 12px !important; margin-right: 8px !important;">
        <div class="col-12" style="background:#fafafa; height: 45px;  ">
        <div style="width: 100px; float:left;display:flex;flex-direction: row;
        justify-content: center;
        align-items: center;">
            <span class="headerName">Leave History</span>
        </div>
        <!-- <div style="flex-direction: row; float: right;">
            <mat-form-field appearance="outline" style = "width:140px">
              <div style="display: flex; align-items: center; height: 17px">
                <input
                  matInput
                  [matDatepicker]="dp"
                  [formControl]="month"
                  placeholder="MMM YYYY"
                  (keydown)="onKeyDownMonthSelection($event)"
                />
                <mat-datepicker-toggle [for]="dp">
                  <mat-icon matDatepickerToggleIcon class="calendar-icon"
                    >calendar_today</mat-icon
                  >
                </mat-datepicker-toggle>
                <mat-datepicker
                  #dp
                  startView="multi-year"
                  [startAt]="startDate"
                  [selected]="selectedDate"
                  [opened]="monthSelected"
                  (monthSelected)="onSelectAwaitingRequests($event, dp)"
                >
                </mat-datepicker>
              </div>
            </mat-form-field>
            <mat-icon style="padding-top: 3px; cursor: pointer;"
            (click)="onClear()"
            >clear</mat-icon
          >
          </div> -->
        <div style="width: 100px; 
        float: left; margin-top: 10px; 
        display: flex; 
        flex-direction: row; 
        justify-content: center; 
        cursor: pointer;
        align-items: center;"
            
            (click)="openPendingRequestsUdrfModal()"
            class="filtericon"
          >
            <mat-icon  style="margin-top: 2px; font-size: 20px;">filter_list</mat-icon
            ><span style="font-weight: 700; margin-top: 3px;">Filter</span>
        </div>
        <div style="margin-right: 10px; display: flex; flex-direction: row; margin-top: 3px;"
        *ngIf="_udrfService.udrfData.mainFilterArray.length > 0"
      >
        <mat-icon
          (click)="goToPreviousFilter()"
          style="margin-right: 10px; margin-top: 6px; cursor: pointer"
          >keyboard_arrow_left</mat-icon
        >
        <div
          *ngFor="
            let items of _udrfService.udrfData.mainFilterArray;
            let i = index
          "
        >
          <div
            *ngIf="
              items.multiOptionSelectSearchValues[0] && displayedFilter == i
            "
            class="searchField"
            style="display: flex !important"
          >
            <div class="searchboxes tooltip" style="display: contents">
              <span
                class="searchtitle titlemargin filterfield"
                [matTooltip]="items.filterName"
                >{{ items.filterName }}</span
              >
              <span class="searchtitle boxstyle"
                ><span
                  class="filterval"
                  [matTooltip]="items.multiOptionSelectSearchValues[0]"
                  >{{ items.multiOptionSelectSearchValues[0] }}</span
                ><mat-icon
                  class="clearonefiltericn"
                  (click)="clearonefilter(items, i)"
                  >clear</mat-icon
                ></span
              >
              <mat-icon
                *ngIf="!dropdownflag"
                class="dropdownfilter"
                (click)="viewdroplist(i)"
                >keyboard_arrow_down</mat-icon
              >
              <mat-icon
                *ngIf="dropdownflag"
                class="dropdownfilter"
                (click)="closedroplist()"
                >keyboard_arrow_up</mat-icon
              >

              <mat-card
                class="tooltiptext dropdownborder"
                [ngClass]="
                  dropdownflag && i == selecteddropdown
                    ? 'droplistvisible'
                    : 'droplisthidden'
                "
              >
                <div
                  *ngFor="let item of items.checkboxValues; let j = index"
                >
                  <div style="display: inline-flex">
                    <p class="dropdata">{{ item.checkboxName }}</p>
                    <mat-checkbox
                      class="example-margin"
                      [checked]="item.isCheckboxSelected"
                      (change)="
                        checkboxvalue(
                          $event.checked,
                          i,
                          j,
                          item.checkboxName,
                          item.checkboxId
                        )
                      "
                    >
                    </mat-checkbox>
                  </div>
                </div>
              </mat-card>
            </div>
          </div>
        </div>
        <mat-icon
          (click)="goToNextFilter()"
          style="margin-left: 10px; margin-top: 6px; cursor: pointer"
          >keyboard_arrow_right</mat-icon
        >
        <div
        style="margin-right: 10px; margin-top: 3px; display: flex; flex-direction: row"
        *ngIf="_udrfService.udrfData.mainFilterArray.length > 0"
      >
        <mat-icon style="padding-top: 3px; cursor: pointer;"
                  (click)="onClear()"
                  >clear</mat-icon
                >
      </div>
      </div>
</div>

        <div class="col-12 pl-0 pt-2" style="height: 35px; padding-top: 12px;">
            <div class="row">
                <div class="col-1 subHeaderName">
                    REQUEST ID
                </div>
                <div class="col-2 subHeaderName">
                    REQUEST TYPE
                </div>
                <div class="col-2 subHeaderName">
                    INTERVAL
                </div>
                <div class="col-2 pl-2 subHeaderName">
                    TYPE
                </div>
                <div class="col-1 pr-0 subHeaderName">
                    ABSENCE DAYS
                </div>
                <div class="col-1 subHeaderName">
                    APPLIED ON
                </div>
                <div class="col-1 pl-2 subHeaderName">
                    APPROVERS
                </div>
                <div class="col-1 subHeaderName">
                    STATUS
                </div>
                <div class="col-1 subHeaderName">
                    ACTION
                </div>
            </div>
        </div>
        <mat-divider></mat-divider>
        <div class="col-12 pl-0 pt-2" style="height: 280px; padding-top: 12px;">
        <div class="approvals-spinner" [hidden]="!initialLoading" style="padding-top:100px">
            <mat-spinner class="main-spinner" diameter="40"></mat-spinner>
        </div>
        <div 
        [hidden]="initialLoading" >
        <div 
        infinite-scroll
        [infiniteScrollDistance]="0.1"
        [scrollWindow]="false"
        (scrolled)="getPendingApprovalsOnScroll()"
        class="pl-2 itemData"  *ngIf="leaveData.length > 0" >
            <div *ngFor="let items of leaveData"
                >
                <div class="row">
                    <div class="col-1 pl-2 itemName">
                        REQ {{items.id}}
                    </div>
                    <div class="col-2 pl-2 itemName">
                        {{items.leaveId}}
                    </div>
                    <div class="col-2 itemName" style="padding-left: 10px;">
                        {{items.uiFormattedDate}}
                    </div>
                    <div class="col-2 pl-2 itemName" [ngStyle]="{'color': items.leave_color_code, 'cursor': 'pointer', 'Padding-left' : '13px'}" (click)="showDetailView(items)">
                        {{items.leaveType}}
                    </div>
                    <div class="col-1  pl-3 itemName">
                        {{items.days}}
                    </div>
                    <div class="col-1 pl-3 itemName">
                        {{items.applied_on}}
                    </div>
                    <div class="col-1 itemName" style="padding-left: 13px;">
                        <div *ngFor="let appr of items.approvers">
                            <app-user-image [tooltip]="approverTooltip" content-type="template" max-width="300"
                                placement="top" style="margin: 2px;" imgWidth="23px" imgHeight="23px"
                                [id]="appr.oid"></app-user-image>
                            <ng-template #approverTooltip placement="top">
                                <div class="row tooltip-text">
                                    {{ appr.name }}
                                </div>
                                <div class="row tooltip-text">
                                    {{ appr.role }}
                                </div>
                                <div class="row tooltip-text"> Level : {{appr.level}}</div>
                            </ng-template>
                        </div>
                    </div>
                    <div class="col-1 pl-3 itemName pb-2 ">
                        <button class="status-btn"
                            [ngStyle]="{'background': items.status_color_code}">
                            {{items.status}}
                        </button>
                    </div>
                    <div *ngIf = "!items.disableWithdraw">
                        <div class="col-1 pl-4 itemName" style="text-decoration: underline; cursor: pointer;" 
                        (click)="showDetailView(items)" *ngIf ="items.status_id != 4 && items.status_id != 3 && (items.status_id != 2 && items.is_claim != 1)">
                            Withdraw
                        </div>
                        <div class="col-1 pl-4 itemName" style="text-decoration: underline; cursor: pointer;" 
                        (click)="showDetailView(items)" *ngIf ="items.status_id != 4 && items.status_id != 3 && (items.status_id == 1 && items.is_claim == 1)">
                            Withdraw
                        </div>
                        <div class="col-1 pl-4 itemName" style="text-decoration: underline; cursor: pointer;" 
                        (click)="showDetailView(items)" *ngIf ="items.status_id != 4 && items.status_id != 3 && (items.status_id == 2 && items.is_claim == 0)">
                            Withdraw
                        </div>
                    </div>
                </div>
                <mat-divider style="padding-bottom: 10px;"></mat-divider>
            </div>
            <div class="approvals-spinner" [hidden]="!pendingScrollApiInProgress">
                <mat-spinner class="main-spinner" diameter="40"></mat-spinner>
            </div>
        </div>
        <div class="col-12 itemData" *ngIf="leaveData.length == 0" style="padding-top: 40px;">
            <div style="display: flex; justify-content: center;">
                <img src="https://assets.kebs.app/images/no_milestones_v3.png" width="250" height="150">
            </div>
            <div class="noLeave">
                <!-- No Leaves?!!! -->
            </div>
            <div class="subHead">
                <!-- Well, we urge you to take <br> a break at times -->
            </div>
            <div *ngIf= "leaveData.length == 0 && _udrfService.udrfData.mainFilterArray.length == 0" style="display: flex; justify-content: center">
                <button class="applyLeaveBtn1" (click)="applyLeave()">
                    Apply Your First Leave
                </button>
            </div>
            <div *ngIf= "leaveData.length == 0 && _udrfService.udrfData.mainFilterArray.length > 0"style="display: flex; justify-content: center" class="headerName">
                No Leave History Found
            </div>
        </div>
        </div>
        </div>
    </div>
    <!-- <div class="col-12 pl-0 pr-2">
        <div class="header">
            <div class="col-12" style="background:#F1F3F8; opacity: 0.4; height: 45px; ">
                <div class="row">
                    <div class="col-6 headerName">
                        Leave History
                    </div>

                </div>
            </div>
            <div class="leaveHeader">
                <div class="col-12" style="height: 35px; padding-top: 12px;">
                    <div class="row">
                        <div class="col-2 subHeaderName">
                            LEAVE ID
                        </div>
                        <div class="col-2 subHeaderName">
                            TYPE
                        </div>
                        <div class="col-1 subHeaderName">
                            DAYS
                        </div>
                        <div class="col-2 subHeaderName">
                            DATE(S)
                        </div>
                        <div class="col-1 subHeaderName">
                            REQUEST ID
                        </div>
                        <div class="col-1 subHeaderName">
                            APPLIED ON
                        </div>
                        <div class="col-1 subHeaderName">
                            APPROVERS
                        </div>
                        <div class="col-1 subHeaderName">
                            STATUS
                        </div>
                        <div class="col-1 subHeaderName">
                            ACTION
                        </div>
                    </div>
                </div>
                <mat-divider></mat-divider>
                <div *ngIf="leaveData.length > 0">
                    <div class="col-12 pt-2" *ngFor="let items of leaveData"
                        >
                        <div class="row">
                            <div class="col-2 itemName">
                                {{items.leaveId}}
                            </div>
                            <div class="col-2 itemName" [ngStyle]="{'color': items.leave_color_code, 'cursor': 'pointer'}" (click)="showDetailView(items)">
                                {{items.leaveType}}
                            </div>
                            <div class="col-1 itemName">
                                {{items.days}}
                            </div>
                            <div class="col-2 itemName">
                                <div *ngFor = "let dates of items.uiFormattedDate">
                                {{dates}}
                                </div>
                            </div>
                            <div class="col-1 itemName">
                                REQ {{items.id}}
                            </div>
                            <div class="col-1 itemName">
                                {{items.applied_on}}
                            </div>
                            <div class="col-1 itemName">
                                <div *ngFor="let appr of items.approvers">
                                    <app-user-image [tooltip]="approverTooltip" content-type="template" max-width="300"
                                        placement="top" style="margin: 2px;" imgWidth="23px" imgHeight="23px"
                                        [id]="appr.oid"></app-user-image>
                                    <ng-template #approverTooltip placement="top">
                                        <div class="row tooltip-text">
                                            {{ appr.name }}
                                        </div>
                                        <div class="row tooltip-text">
                                            {{ appr.role }}
                                        </div>
                                        <div class="row tooltip-text"> Level : {{appr.level}}</div>
                                    </ng-template>
                                </div>
                            </div>
                            <div class="col-1 itemName pb-2 ">
                                <button class="status-btn"
                                    [ngStyle]="{'background': items.status_color_code}">
                                    {{items.status}}
                                </button>
                            </div>
                            <div class="col-1 itemName" style="text-decoration: underline; cursor: pointer;" 
                            (click)="showDetailView(items)" *ngIf ="items.status_id != 4">
                                Withdrawn
                            </div>
                        </div>
                        <mat-divider></mat-divider>
                    </div>
                </div>
                <div *ngIf="leaveData.length == 0">
                    <div style="display: flex; justify-content: center; padding-top: 15px;">
                        <img src="https://assets.kebs.app/images/no_milestones_v3.png" width="300" height="250">
                    </div>
                    <div class="noLeave">
                        No Leaves?!!!
                    </div>
                    <div class="subHead">
                        Well, we urge you to take <br> a break at times
                    </div>
                    <div style="display: flex; justify-content: center">
                        <button class="applyLeaveBtn1">
                            Apply Your First Leave
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div> -->
</div>