.leave-landing-styles{

    .textValue{
        font-size: 12px;
        color: #45546E,

    }

    .view-button-inactive {
        margin-top: 5px !important;
        line-height: 8px;
        width: 150px;
        height: 50px;
        margin-right: 10px !important;
        // box-shadow: 0 4px 4px -1px rgba(0, 0, 0, 0.2),
        //   0 1px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 14px 0 rgba(0, 0, 0, 0.12);
        // -webkit-animation: slide-top 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
        // animation: slide-top 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
        border-radius: 10px;
        .iconButton {
          color: #45546E;
          font-size: 18px;
          height: 25px;
        }
        .iconButtonWarn {
          color: #cf0001;
          font-size: 18px; 
        }
        .iconButtonWarnAnim {
          color: #cf0001;
          font-size: 18px;
          // animation: blink 1.1s cubic-bezier(.5, 0, 1, 1) infinite alternate; 
        }
      }

    .view-button-active
    {
        margin-top: 5px !important;
        line-height: 8px;
        width: 93px;
        height: 50px;
        border: 2px solid #45546E;
        font-size: 12px;
        border-radius: 5px;
    }

    .tag-btn{
        font-size: 11px;
        font-weight: bold;
        cursor: pointer;
        z-index: 5;
        position: relative;
        padding: 10px;
        margin: 0;
        line-height: 5px;
        -webkit-transition: all 0.2s ease-in-out;
        -moz-transition: all 0.2s ease-in-out;
        -o-transition: all 0.2s ease-in-out;
        -ms-transition: all 0.2s ease-in-out;
        transition: all 0.2s ease-in-out;
        border: 1px solid #45546E;
        box-shadow: none;
        height: 25px;
        border-radius: 5px;
        margin-top: 17px !important;
    }

      .leave-btn{
        width: 100px;
        height: 37px;
        color: white;
        font-size: 13px;
        /* Primary/Gradient */
        background: linear-gradient(270deg, #EF4A61 0%, #F27A6C 105.29%);
        border-radius: 6px;
        margin-top: 15px !important;
      }

      .valueGrey14ForIcon {
        color: #45546E;
        font-size: 12px;
        font-weight: 400;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        display: inline;
        font-family: 'Roboto';
        padding: 10px;
      }

      .compOff{
        display: flex;
      }

      .btn-small{
        width: 15px;
        height: 15px;
        font-weight: bold;
        font-size: 10px;
        // color: #45546E;
        // border-radius: 3px;
        // border: 5px solid #45546E;

        border: 2px solid #45546E;
        border-radius: 5px;
        color: #45546E;
        padding: 14px 28px;
        // font-size: 16px;
        cursor: pointer;
      }

      .header{
        position: absolute;
        width: 1260px;
        height: 360px;
        margin: 10px;
        margin-top: 30px !important;
        background: #FFFFFF;
        box-shadow: 0px 2px 1px rgba(0, 0, 0, 0.12), 0px 4px 6px rgba(0, 0, 0, 0.12);
        border-radius: 4px;
        overflow-y: auto;
      }
      .headerName{
        font-family: 'Roboto';
        font-style: normal;
        font-weight: 800;
        font-size: 14px;
        line-height: 16px;
        letter-spacing: 0.02em;
        text-transform: capitalize;
        color:#45546E;
        padding-top: 15px;
      }

      .subHeaderName{
        color: #B9C0CA;
        font-family: 'Roboto';
        font-style: normal;
        font-weight: 400;
        font-size: 11px;
        line-height: 16px;
        letter-spacing: 0.02em;
        text-transform: uppercase;
      }

      .itemName{
        color: #45546E;
        font-family: 'Roboto';
        font-style: normal;
        font-weight: 400;
        font-size: 12px;
        line-height: 16px;
        letter-spacing: 0.02em;
        text-transform: capitalize;
        white-space: nowrap;
      }
      .noLeave{
        color: #45546E;
        font-weight: 700;
        font-size: 14px;
        text-align: center;
        text-transform: capitalize;
        font-family: 'Roboto';
        font-style: normal;
        padding-top: 10px;
      }
      .applyLeaveBtn1{
        padding: 12px 16px;
        border: none;
        position: absolute;
        width: 171px;
        height: 40px;
        background: linear-gradient(270deg, #EF4A61 0%, #F27A6C 105.29%); 
        border-radius: 8px;
        color: white;
        text-align: center;
      }

      .subHead{
        font-family: 'Roboto';
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 16px;
        text-align: center;
        color: #8B95A5;
        padding: 5px;
      }

      .data-type-card {
        min-height: 15vh;
        transition: all 0.3s linear;
      }

      .status-btn{
        color: white; 
        border-radius: 30px;
        width: 65px; 
        height: 25px; 
        border: none

      }

      .headingBold {
        color: #1a1a1a;
        font-weight: 600;
        font-size: 22px;
      }
      
      .valueGrey14{
        color: #526179;
        font-size: 12px;
        font-weight: 400;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        display: inline;
        font-family: 'Roboto';
      }

      .matIcon{
        font-size: 15px;
        width: 10px;
        height: 10px;
        color: #45546E;
      }
      .tempCheck{

        background: #FFFFFF;
        box-shadow: 0px 2px 1px rgba(0, 0, 0, 0.12), 0px 4px 6px rgba(0, 0, 0, 0.12);
        border-radius: 4px;
      }
      .itemData{
        height: 280px;
        overflow-y: auto;
      }

      .clearbtn {
        font-weight: 600;
        font-size: 12px;
        line-height: 16px;
        border: 1px solid;
        height: 34px;
        border-radius: 4px;
        border-color: #ee4961;
        background-color: #f2d4cdad;
        color: #ee4961;
        flex-direction: row;
      }
      
      .searchField {
        display: inline-block;
        border: solid thin #dadce2;
        border-radius: 4px;
        height: 36px;
        margin-bottom: 8px;
      }
      
      .searchboxes {
        display: flex;
        align-items: center;
      }
      
      .titlemargin {
        margin-left: 0px !important;
        margin-right: 0px !important;

      }
      
      .searchtitle {
        font-weight: 400;
        font-size: 12px;
        line-height: 16px;
        margin: auto 5px auto 5px;
        padding: 2px;
        padding-left: 10px;
        padding-right: 10px;
      }
      
      .clearonefiltericn {
        font-size: 13px;
        display: inline-flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
      }
      
      .dropdownfilter {
        font-size: 13px;
        display: inline-flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        margin-top: 5px;
      }
      
      .boxstyle {
        background-color: #f2d4cdad;
        height: 1.5rem;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        margin-right: 0px;
        color: #526179;
      }
      
      .filterval {
        width: 100px;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
      }
      
      .filterfield {
        width: 100px;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
      }
      
      .searchFieldtxt {
        outline: none;
        border: none;
        font-weight: 400;
        font-size: 12px;
        line-height: 16px;
      }
      
      .positionFieldlable {
        margin-top: 19px;
        margin-left: 4px;
        color: #a8acb2;
        font-weight: 400;
        font-size: 12px;
        line-height: 16px;
      }
      
      .positionField {
        display: inline-block;
        margin-left: 10px;
        padding-top: 5px;
        width: 147px;
        margin-right: 20px;
      }
      
      .positionFieldtxt {
        border-radius: 4px;
        height: 40px;
        width: 200px;
        border-color: #b9c0ca;
      }
      
      .tooltip {
        position: relative;
        display: inline-block;
      }
      
      .droplistvisible {
        visibility: visible !important;
      }
      
      .droplisthidden {
        visibility: hidden !important;
      }
      
      .dropdata {
        font-size: 12px;
        line-height: 16px;
        letter-spacing: 0.02em;
        text-transform: capitalize;
        color: #111434;
        width: 175px;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
      }
      
      .example-margin {
        display: flex;
        right: 10px;
        position: absolute;
      }
      
      .dropdownborder {
        z-index: 1;
        width: 230px;
        border: 1px solid lightgrey;
        border-radius: 4px;
        margin-top: 35px;
        padding: 10px !important;
      }
      
      .tooltip .tooltiptext {
        visibility: hidden;
        background-color: #ffffff;
        border-radius: 6px;
        padding: 5px;
        padding-top: 7px;
        width: 250px;
        height: 200px;
        position: absolute;
        z-index: 2;
        border-radius: 4px;
        overflow-y: scroll;
        overflow-x: hidden;
      }
      
      .tooltip:active .tooltiptext {
        visibility: visible;
      }
      
      .filtericon
      {
        font-family: 'Roboto';
        font-style: normal;
        font-weight: 500;
        font-size: 14px;
        line-height: 16px;
        letter-spacing: 0.02em;
        text-transform: capitalize;
        color: #45546E;
      }
      .approvals-spinner {
        display: flex;
        justify-content: center;
        align-items: center;
        height: var(--dynamicHeight);
      }

}