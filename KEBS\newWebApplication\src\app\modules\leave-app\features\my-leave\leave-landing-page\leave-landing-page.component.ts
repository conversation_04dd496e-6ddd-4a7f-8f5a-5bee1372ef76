import { Component, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { LoginService } from 'src/app/services/login/login.service';
import { LeaveAppService } from '../../../services/leave-app.service';
import { takeUntil } from "rxjs/operators";
import { Subject } from "rxjs";
import * as moment from 'moment';
import _ from 'underscore';
import { ErrorService } from 'src/app/services/error/error.service';
import { ToasterService } from 'src/app/services/toaster-service/toaster.service';
import { ToastService } from '../../lazy-loaded/toast-message/toast.service';
import { ErrorPopupComponent } from '../../lazy-loaded/error-popup/error-popup.component';
import { height } from '@amcharts/amcharts4/.internal/core/utils/Utils';
import { UdrfService } from 'src/app/services/udrf/udrf.service';
import {
  MomentDateAdapter,
  MAT_MOMENT_DATE_ADAPTER_OPTIONS,
} from '@angular/material-moment-adapter';
import {
  DateAdapter,
  MAT_DATE_LOCALE,
  MAT_DATE_FORMATS,
} from '@angular/material/core';

export const MY_FORMATS = {
  parse: {
    dateInput: 'MMM YYYY',
  },
  display: {
    dateInput: 'MMM YYYY',
    monthYearLabel: 'MMM YYYY',
    dateA11yLabel: 'LL',
    monthYearA11yLabel: 'MMMM YYYY',
  },
};
@Component({
  selector: 'app-leave-landing-page',
  templateUrl: './leave-landing-page.component.html',
  styleUrls: ['./leave-landing-page.component.scss'],
  providers: [
    {
      provide: DateAdapter,
      useClass: MomentDateAdapter,
      deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS],
    },
    { provide: MAT_DATE_FORMATS, useValue: MY_FORMATS },
  ],
})
export class LeaveLandingPageComponent implements OnInit {


  uiConfigData: any;

  currentUserOid: string = "";

  currentUser: any;

  compensationOff: any = false;

  leavePolicy: boolean = true;

  applyLeaveBtn: boolean = true;

  howToApplyLeave: boolean = false;

  showSummary: boolean = false;

  leaveData = [];

  weekOffData = [];

  holidayList = [];

  leaveBalanceDetails = [];

  summaryCards = [];

  showRightSummaryCardIcon: boolean = false;

  showLeftSummaryCardIcon: boolean = false;

  protected _onDestroy = new Subject<void>();

  restrictWeekOff: number;

  currentUserAdmin: boolean = true;

  employeeDetails = [];

  showSummaryScrollIcon: boolean = false;

  showSummaryCard: boolean = false;

  showLeaveHistory: boolean = false;

  orgLeaveYearType: number = 1;

  approvedLeaveData = [];

  compOffLeaveData = [];

  s3BucketConfig = {};

  headerText: string;

  allowedDivision: any = [];

  daysCompOfConsidered: number = 0;

  flexiHolidayInLeaves: number = 0;

  isCompOffSplitUpAllowed: number = 0;

  isLOPAllowedAnyTime: number = 0;

  pendingRequestsApplicationId = 343;
  pendingRequestsFilterConfig: any;
  dropdownflag = false;
  selecteddropdown: any;
  displayedFilter = 0;
  monthSelected: false;
  monthStartDate: moment.Moment;
  monthEndDate: moment.Moment;
  initialLoading: boolean;
  pendingLimit:number = 0;
  pendingScrollApiInProgress: boolean;
  spinnerdisplay: boolean;
  getAllLeaveDetails = [];

  disableBtn: boolean = false;

  compOffTypeConfig: any;
  compOffNeedToAppliedAfterDoj: number = 0;

  sessionData = [{
    id: 1,
    name: "Full Day (FD)"
  },
  {
    id: 2,
    name: "Forenoon (FN)"
  },
  {
    id: 3,
    name: "Afternoon (AN)"
  }]

  constructor(public matDialog: MatDialog, private _leaveAppService: LeaveAppService, private authService: LoginService, private errorService: ErrorService, private toastService: ToasterService, public _udrfService: UdrfService, private toastmessage: ToastService) { }

 async ngOnInit() {

    this.currentUser = this.authService.getProfile().profile;

    this.currentUserOid = this.currentUser.oid;
    
    await this.configureUdrfForPendingApprovals();

    this.getUIConfig();
    
    this.getEmployeeDetails();

    this.getEmployeeWeekOff();

    this.getHolidayListOfMonth();

    this.getEmployeeLeaveDetails();

    //this.getLeaveDetails();


  }
  //Function to Open Apply ataLeave Dialog
  async applyLeave() {
    this.disableBtn = true;

    this.getEmployeeLeaveDetails();
    //this.getLeaveDetails();
    await this.getApprovedLeaveDetails();


    let modalParams = {
      //Pass oid, leave bal and quota 
      associateOid: this.currentUserOid,
      associateId: this.currentUser.aid,
      holidayList: this.holidayList,
      leaveDaysList: this.approvedLeaveData,
      employeeLeaveDetails: _.filter(this.leaveBalanceDetails, {is_req_leave: 0}),
      weekOffDetails: this.weekOffData,
      employeeDetails: this.employeeDetails,
      orgLeaveYearType: this.orgLeaveYearType,
      s3BucketConfig: this.s3BucketConfig,
      flexiHolidayInLeaves: this.flexiHolidayInLeaves,
      isLOPAllowedAnyTime: this.isLOPAllowedAnyTime,
      pendingRequestsFilterConfig: this.pendingRequestsFilterConfig
    }

    const { ApplyLeaveComponent } = await import('../components/apply-leave/apply-leave.component');

    const ApplyLeaveComponentComponent = this.matDialog.open(ApplyLeaveComponent, {
      height: '100%',
      minWidth: '50%',
      position: { right: '0px' },
      data: { modalParams: modalParams }
    });

    ApplyLeaveComponentComponent.afterClosed().subscribe(res => {
      if (res.event == "submit") {
        this.initPendingRequests();
      }
      //this.getLeaveDetails();     
      this.disableBtn = false; 
      this.getEmployeeLeaveDetails();
      this.getApprovedLeaveDetails();
      this.getComOffLeaveDetails();
      
    });
    ApplyLeaveComponentComponent.backdropClick().subscribe(() => {
      // Close the dialog
      //this.getLeaveDetails();
      this.disableBtn = false;
      this.getEmployeeLeaveDetails();
    })
  }
  // Function to Get UI Configuration Details For Leave App & store it in respective variables
  getUIConfig() {
    this._leaveAppService.uiConfig(this.currentUser.aid, this.currentUser.oid)
      .pipe(takeUntil(this._onDestroy))
      .subscribe(async res => {
        if (res['data']) {
          this.uiConfigData = res['data'][0];
          this.compensationOff = this.uiConfigData.compensation_off;
          //this.compensationOff = this.displayCompoffBasedOnE360Divison(JSON.parse(this.uiConfigData.claim_allowed_division));
          this.applyLeaveBtn = this.uiConfigData.apply_leave;
          this.howToApplyLeave = this.uiConfigData.how_to_apply_leave
          this.leavePolicy = this.uiConfigData.leave_policy;
          this.showSummary = this.uiConfigData.summary;
          this.restrictWeekOff = this.uiConfigData.apply_comp_off_config;
          this.showSummaryCard = this.uiConfigData.summary_card;
          this.showLeaveHistory = this.uiConfigData.leave_history;
          this.orgLeaveYearType = this.uiConfigData.org_leave_year_type;
          this.s3BucketConfig = JSON.parse(this.uiConfigData.s3_bucket_config);
          this.headerText = this.uiConfigData.headerText;
          this.allowedDivision = JSON.parse(this.uiConfigData.claim_allowed_division);
          this.daysCompOfConsidered = this.uiConfigData.days_comp_off_allowed ? this.uiConfigData.days_comp_off_allowed : 0;
          this.flexiHolidayInLeaves = this.uiConfigData.flexi_holiday_in_leaves ? this.uiConfigData.flexi_holiday_in_leaves : 0;
          this.isCompOffSplitUpAllowed = this.uiConfigData.is_comp_off_split_up_allowed ? this.uiConfigData.is_comp_off_split_up_allowed : 0;
          this.isLOPAllowedAnyTime = this.uiConfigData.is_lop_allowed_any_time ? this.uiConfigData.is_lop_allowed_any_time : 0;
          this.compOffTypeConfig = this.uiConfigData.comp_off_request_type;
          this.compOffNeedToAppliedAfterDoj = this.uiConfigData.comp_off_request_can_be_raised_after_doj;
        }
      }, err => {
        //this.errorService.userErrorAlert((err && err.code) ? err.code : (err && err.error) ? err.error.code : 'NIL', "Something Went Wrong Kindly Try After Some Time", (err && err.params) ? err.params : (err && err.error) ? err.error.params : {})
        let modalParams = err.error;
        this.matDialog.open(ErrorPopupComponent, {
          width:'40%',
          minHeight:'250px',
          data: { modalParams: modalParams }
        });
      });
  }
  // Function to get leave request that is applied by the user
  getLeaveDetails() {
    if(this.leaveData.length == 0)
      this.initialLoading = true;
    
    if(this.leaveData.length > 0)
    {
      this.pendingScrollApiInProgress = true;
    }
    this._leaveAppService.getLeaveDetails(this.currentUserOid,this.pendingRequestsFilterConfig,this.pendingLimit)
      .pipe(takeUntil(this._onDestroy))
      .subscribe(async res => {
        if (res['messType'] == 'S' && res['data'] && res['data'].length > 0) {
            if(this.pendingScrollApiInProgress)
              this.leaveData = this.leaveData.concat(res['data']);
            else
              this.leaveData = res['data'];
          for (let items of this.leaveData) {
            let dateArray = items.dates.sort(function (a, b) {
              return new Date(a).valueOf() - new Date(b).valueOf()
            })
            items.uiFormattedDate = moment(dateArray[0]).format("DD MMM YYYY") + ' - ' + moment(dateArray[dateArray.length - 1]).format("DD MMM YYYY");
          }
          this.pendingScrollApiInProgress = false;
        }
        else {
          if(this.leaveData.length == 0)
              this.leaveData = [];
          this.pendingScrollApiInProgress = false;
        }
        this.initialLoading = false;
      }, err => {
        //this.errorService.userErrorAlert((err && err.code) ? err.code : (err && err.error) ? err.error.code : 'NIL', "Something Went Wrong Kindly Try After Some Time", (err && err.params) ? err.params : (err && err.error) ? err.error.params : {})
        let modalParams = err.error;
        this.matDialog.open(ErrorPopupComponent, {
          width:'40%',
          minHeight:'250px',
          data: { modalParams: modalParams }
        });
      });
  }
  // Function to get approved leave request that is applied by the user
async  getApprovedLeaveDetails() {
    await this.getAllPendingLeaveDetails();
    this.approvedLeaveData = [];
    for (let single_leave_data of this.getAllLeaveDetails) {
        this.approvedLeaveData.push(single_leave_data);
    }
  }
  // Function to get Comp Off leave request that is applied by the user
async getComOffLeaveDetails() {
  this.compOffLeaveData = [];
    await this.getAllPendingLeaveDetails();
    for (let single_leave_data of this.getAllLeaveDetails) {
        this.compOffLeaveData.push(single_leave_data);
    }
  }
  // Function to get employee week off details
  getEmployeeWeekOff() {
    this._leaveAppService.getEmployeeWeekOff(this.currentUserOid, this.currentUser.aid)
      .pipe(takeUntil(this._onDestroy))
      .subscribe(async res => {
        if (res['messType'] == 'S' && res['data'] && res['data'].length > 0) {
          this.weekOffData = res['data'];
        }
        else {
          this.weekOffData = [];
          this.toastmessage.showError(res['messText'])
        }
      }, err => {
        //this.errorService.userErrorAlert((err && err.code) ? err.code : (err && err.error) ? err.error.code : 'NIL', "Something Went Wrong Kindly Try After Some Time", (err && err.params) ? err.params : (err && err.error) ? err.error.params : {})
        let modalParams = err.error;
        this.matDialog.open(ErrorPopupComponent, {
          width:'40%',
          minHeight:'250px',
          data: { modalParams: modalParams }
        });
      })
  }
  // Function to get holiday list of the organization and the applied holiday for the user
  getHolidayListOfMonth() {
    this._leaveAppService.getHolidayListOfMonth(moment().startOf('month').format("YYYY-MM-DD hh:mm"), this.currentUserOid, this.currentUser.aid)
      .pipe(takeUntil(this._onDestroy))
      .subscribe(async res => {
        if (res['messType'] == 'S' && res['data'] && res['data'].length > 0) {
          this.holidayList = res['data'];
        }
        else {
          this.holidayList = [];
        }
      }, err => {
        //this.errorService.userErrorAlert((err && err.code) ? err.code : (err && err.error) ? err.error.code : 'NIL', "Something Went Wrong Kindly Try After Some Time", (err && err.params) ? err.params : (err && err.error) ? err.error.params : {})
        let modalParams = err.error;
        this.matDialog.open(ErrorPopupComponent, {
          width:'40%',
          minHeight:'250px',
          data: { modalParams: modalParams }
        });
      })
  }
  //Function to get complete employee leave details like leave type, name, quota, balance, conditions...
  getEmployeeLeaveDetails() {
    this._leaveAppService.getEmployeeLeaveDetails(this.currentUser.aid, this.currentUserOid)
      .pipe(takeUntil(this._onDestroy))
      .subscribe(async res => {
        if (res['messType'] == 'S' && res['data'] && res['data'].length > 0) {
          this.leaveBalanceDetails = res['data'];
          if (_.filter(this.leaveBalanceDetails, { is_need_to_show_in_summary: 1 }).length > 6) {
            this.showSummaryScrollIcon = true;
          }
          else {
            this.showSummaryScrollIcon = false;
          }
          this.bindToSummaryCards(this.leaveBalanceDetails)
        }
        else {
          this.leaveBalanceDetails = [];
          this.toastmessage.showInfo(res['messText'], 5000);
        }
      }, err => {
        //this.errorService.userErrorAlert((err && err.code) ? err.code : (err && err.error) ? err.error.code : 'NIL', "Something Went Wrong Kindly Try After Some Time", (err && err.params) ? err.params : (err && err.error) ? err.error.params : {})
        let modalParams = err.error;
        this.matDialog.open(ErrorPopupComponent, {
          width:'40%',
          minHeight:'250px',
          data: { modalParams: modalParams }
        });
      })
  }
  // Function to display the detail view of the leave request that is created by the user
  async showDetailView(leaveData) {
    let modalParams = {
      //Pass oid, leave bal and quota 
      leaveData: leaveData,
      currentUserOid: this.currentUserOid,
      s3BucketConfig: this.s3BucketConfig
    }

    const { MyLeaveDetailComponent } = await import('../components/my-leave-detail/my-leave-detail.component');

    const MyLeaveDetailComponentData = this.matDialog.open(MyLeaveDetailComponent, {
      height: '100%',
      width: '752px',
      position: { right: '0px' },
      data: { modalParams: modalParams }
    });
    MyLeaveDetailComponentData.afterClosed().subscribe(res => {
      if (res.event == "submit") {
        this.initPendingRequests();
      }
      // if(!isclosed)
      //   this.initPendingRequests();
      this.getEmployeeLeaveDetails();
      this.getApprovedLeaveDetails();
      //this.getComOffLeaveDetails();
    });

  }
  // Function to bind data to the ui summary cards
  bindToSummaryCards(leaveDetails) {
    this.summaryCards = [];
    this.showRightSummaryCardIcon = true;
    this.showLeftSummaryCardIcon = false;
    let temp = _.filter(this.leaveBalanceDetails, { is_need_to_show_in_summary: 1 });

    for (let i = 0; i < (temp.length > 6 ? 6 : temp.length); i++) {
      if (temp[i].is_need_to_show_in_summary == 1) {
        this.summaryCards.push({
          dataType: temp[i].leaveName,
          dataTypeValue: temp[i].leaveBalance + (temp[i].AllocatedLeaveQuota == 0 ? ' Days' : "/" + temp[i].AllocatedLeaveQuota),
          tootltipData: temp[i].AllocatedLeaveQuota == 0 ? 'Leave Balance' : 'Leave Balance / Leave Quota',
          dataTypeCode: temp[i].leaveType,
          isVisible: true,
          statusColor: temp[i].colorCode
        });
      }
    }

  }
  // Function to move summary card based on the right click
  appedFirstCards() {
    this.summaryCards = [];

    let temp = _.filter(this.leaveBalanceDetails, { is_need_to_show_in_summary: 1 });

    for (let i = 6; i < temp.length; i++) {
      if (temp[i].is_need_to_show_in_summary) {
        this.summaryCards.push({
          dataType: temp[i].leaveName,
          dataTypeValue: temp[i].leaveBalance + (temp[i].AllocatedLeaveQuota == 0 ? ' Days' : "/" + temp[i].AllocatedLeaveQuota),
          tootltipData: temp[i].AllocatedLeaveQuota == 0 ? 'Leave Balance' : 'Leave Balance / Leave Quota',
          dataTypeCode: temp[i].leaveType,
          isVisible: true,
          statusColor: temp[i].colorCode
        })
      }

    }

    this.showRightSummaryCardIcon = false;
    this.showLeftSummaryCardIcon = true;

  }
  //Function to move summary card based on left click
  appendLastCards() {

    this.summaryCards = [];

    let temp = _.filter(this.leaveBalanceDetails, { is_need_to_show_in_summary: 1 });

    for (let i = 0; i < 6; i++) {
      if (temp[i].is_need_to_show_in_summary) {

        this.summaryCards.push({
          dataType: temp[i].leaveName,
          dataTypeValue: temp[i].leaveBalance + (temp[i].AllocatedLeaveQuota == 0 ? ' Days' : "/" + temp[i].AllocatedLeaveQuota),
          dataTypeCode: temp[i].leaveType,
          tootltipData: temp[i].AllocatedLeaveQuota == 0 ? 'Leave Balance' : 'Leave Balance / Leave Quota',
          isVisible: true,
          statusColor: temp[i].colorCode
        }


        )
      }

    }
    this.showRightSummaryCardIcon = true;
    this.showLeftSummaryCardIcon = false;


  }
  // Function to display how to apply leave UI modal
  async openHowToApplyLeave() {
    let modalParams = {
      displayText: this.uiConfigData.displayText,
      videoUrl: this.uiConfigData.videoUrl,
      headerText: this.uiConfigData.headerText
    }

    const { HowToApplyLeaveComponent } = await import('../components/how-to-apply-leave/how-to-apply-leave.component');

    const HowToApplyLeaveComponentData = this.matDialog.open(HowToApplyLeaveComponent, {
      height: '100%',
      width: '752px',
      position: { right: '0px' },
      data: { modalParams: modalParams }
    });
  }
  // Function to open claim screen ui
  async openCompensationOff() {
    //this.getLeaveDetails();
    this.disableBtn = true;
    await this.getComOffLeaveDetails();

    let modalParams = {
      associateOid: this.currentUserOid,
      restrictWeekOff: + this.restrictWeekOff,
      holidayList: this.holidayList,
      leaveDaysList: this.compOffLeaveData,
      compOffDetails: _.filter(this.compOffLeaveData, { is_claim: 1 }),
      weekOffDetails: this.weekOffData,
      employeeDetails: this.employeeDetails,
      orgLeaveYearType: this.orgLeaveYearType,
      daysCompOfConsidered: this.daysCompOfConsidered,
      isCompOffSplitUpAllowed : this.isCompOffSplitUpAllowed,
      pendingRequestsFilterConfig : this.pendingRequestsFilterConfig,
      compOffTypeConfig: this.compOffTypeConfig,
      compOffNeedToAppliedAfterDoj: this.compOffNeedToAppliedAfterDoj
    }

    const { CompensationOffComponent } = await import('../components/compensation-off/compensation-off.component');

    const CompensationOffComponentData = this.matDialog.open(CompensationOffComponent, {
      height: '100%',
      width: '752px',
      position: { right: '0px' },
      data: {
        modalParams: modalParams
      }
    });
    CompensationOffComponentData.afterClosed().subscribe(res => {
      if (res.event == "submit") {
        this.initPendingRequests();
      }
      this.disableBtn = false;
      //this.getLeaveDetails();
      this.getEmployeeLeaveDetails();
      //this.getApprovedLeaveDetails();
      this.getEmployeeLeaveDetails();
      //this.getComOffLeaveDetails();
    });
    CompensationOffComponentData.backdropClick().subscribe(() => {
      // Close the dialog
      //this.getLeaveDetails();
      this.disableBtn = false;
      this.getEmployeeLeaveDetails();
    });


  }
  // Function to open leave policy ui
  async openLeavePolicy() {

    let modalParams = {
      associateOid: this.currentUserOid
    }

    const { LeavePolicyComponent } = await import('../components/leave-policy/leave-policy.component');

    const LeavePolicyComponentData = this.matDialog.open(LeavePolicyComponent, {
      height: '100%',
      width: '752px',
      position: { right: '0px' },
      data: { modalParams: modalParams }
    });
  }
  // Function to get all basic employee details such as employee name, oid, aid, orgcode, isorghead, workschedule ...
  getEmployeeDetails() {
    this._leaveAppService.getEmployeeDetails(this.currentUserOid, this.currentUser.aid)
      .pipe(takeUntil(this._onDestroy))
      .subscribe(async res => {
        if (res['messType'] == 'S' && res['data']) {
          this.employeeDetails = res['data'];
          //this.compensationOff = this.compensationOff == 2 ? true : await this.displayCompoffBasedOnE360Divison();
          this.currentUserAdmin = res['data'][0].is_org_head == 1 ? true : false
        }
      }, err => {
        //this.errorService.userErrorAlert((err && err.code) ? err.code : (err && err.error) ? err.error.code : 'NIL', "Something Went Wrong Kindly Try After Some Time", (err && err.params) ? err.params : (err && err.error) ? err.error.params : {})
        let modalParams = err.error;
        this.matDialog.open(ErrorPopupComponent, {
          width:'40%',
          minHeight:'250px',
          data: { modalParams: modalParams }
        });
      })
  }

  async displayCompoffBasedOnE360Divison() {
    if (this.allowedDivision != null && this.allowedDivision.length > 0) {
      for (let items of this.allowedDivision) {
        if (items.division == this.employeeDetails[0].division && items.sub_division == this.employeeDetails[0].sub_division) {
          return true;
        }
      }
    }

    return false;
  }

  async openPendingRequestsUdrfModal() {
    this.dropdownflag = false;
    const { UdrfModalComponent } = await import(
      'src/app/modules/shared-lazy-loaded-components/udrf-modal/udrf-modal.component'
    );
    const openUdrfModalComponent = this.matDialog.open(UdrfModalComponent, {
      minWidth: '100%',
      height: '84%',
      position: { top: '0px', left: '77px' },
      disableClose: true,
    });
  }

  async configureUdrfForPendingApprovals() {
    this._udrfService.udrfData.applicationId =
      this.pendingRequestsApplicationId;
    this._udrfService.udrfData.isItemDataLoading = true;
    this._udrfService.udrfUiData.showItemDataCount = false;
    this._udrfService.udrfUiData.showSearchBar = false;
    this._udrfService.udrfUiData.showActionButtons = true;
    this._udrfService.udrfUiData.showUdrfModalButton = false;
    this._udrfService.udrfUiData.showColumnConfigButton = false;
    this._udrfService.udrfUiData.showSettingsModalButton = false;
    this._udrfService.udrfUiData.showNewReleasesButton = false;
    this._udrfService.udrfUiData.showReportDownloadButton = false;
    this._udrfService.udrfUiData.isReportDownloading = false;
    this._udrfService.udrfUiData.itemHasOpenInNewTab = false;
    this._udrfService.udrfUiData.horizontalScroll = false;
    this._udrfService.udrfUiData.itemHasQuickCta = false;
    this._udrfService.udrfUiData.collapseAll = false;
    this._udrfService.udrfUiData.showCollapseButton = false;
    this._udrfService.udrfUiData.countForOnlyThisReport = false;
    this._udrfService.udrfUiData.toggleChecked = false;
    this._udrfService.udrfUiData.countFlag = false;
    this._udrfService.udrfUiData.isMultipleView = false;
    this._udrfService.udrfUiData.itemHasDownloadButton = false;
    this._udrfService.udrfUiData.emailPluginVisible = false;
    this._udrfService.udrfUiData.itemHasDownloadButton = false;
    this._udrfService.udrfUiData.itemHasAttachFileButton = false;
    this._udrfService.udrfUiData.itemHasComments = false;
    this._udrfService.udrfUiData.isMoreOptionsNeeded = false;
    this._udrfService.udrfUiData.completeProfileBtn = false;
    this._udrfService.udrfUiData.searchPlaceholder = '';
    this._udrfService.getAppUdrfConfig(
      this.pendingRequestsApplicationId,
      await this.initPendingRequests.bind(this)
    );
  }

  
  async initPendingRequests() {
    this.pendingRequestsFilterConfig = {
      mainFilterArray: this._udrfService.udrfData.mainFilterArray,
      searchTableDetails: this._udrfService.udrfData.searchTableDetails,
      txTableDetails: this._udrfService.udrfData.txTableDetails,
    };
    this.leaveData = [];
    this.pendingLimit = 0;
    await this.getLeaveDetails();
  }

  async onClear() {
    this.dropdownflag = false;
    this.displayedFilter = 0;
    //this.pendingSearchParam = '';
    await this._udrfService.udrfFunctions.clearSearchAndConfig();
    await this.initPendingRequests();
  }

  async clearonefilter(filterItem, index) {
    this._udrfService.udrfFunctions.clearItemConfigApply(filterItem, 1);
    this._udrfService.udrfData.appliedFilterTypeArray.splice(index, 1);
    this._udrfService.udrfData.mainFilterArray.splice(index, 1);
    this.displayedFilter = 0;
    this.dropdownflag = false;
  }

  viewdroplist(val: any) {
    this.dropdownflag = true;
    this.selecteddropdown = val;
  }

  closedroplist() {
    this.dropdownflag = false;
    this.selecteddropdown = -1;
  }

  async checkboxvalue(checkboxval, i, j, name, id) {
    this._udrfService.udrfData.mainFilterArray[i].checkboxValues[
      j
    ].isCheckboxSelected = checkboxval;
    if (checkboxval) {
      this._udrfService.udrfData.mainFilterArray[
        i
      ].multiOptionSelectSearchValues.push(name);
      if (this._udrfService.udrfData.mainFilterArray[i].isIdBased)
        this._udrfService.udrfData.mainFilterArray[
          i
        ].multiOptionSelectSearchValuesWithId.push(id);
    } else {
      //this.udrfService.udrfData.mainFilterArray[i].multiOptionSelectSearchValues.splice()
      this._udrfService.udrfData.mainFilterArray[
        i
      ].multiOptionSelectSearchValues.forEach((item, index) => {
        if (item === name) {
          this._udrfService.udrfData.mainFilterArray[
            i
          ].multiOptionSelectSearchValues.splice(index, 1);
          if (this._udrfService.udrfData.mainFilterArray[i].isIdBased)
            this._udrfService.udrfData.mainFilterArray[
              i
            ].multiOptionSelectSearchValuesWithId.splice(index, 1);
        }
      });
    }
    if (
      this._udrfService.udrfData.mainFilterArray[i]
        .multiOptionSelectSearchValues.length == 0
    ) {
      this.dropdownflag = false;
      this.displayedFilter = 0;
      this.clearonefilter(this._udrfService.udrfData.mainFilterArray[i], i);
    } else {
      await this.initPendingRequests();
    }
  }

  goToPreviousFilter() {
    this.dropdownflag = false;
    if (this.displayedFilter == 0) {
      this.displayedFilter =
        this._udrfService.udrfData.mainFilterArray.length - 1;
    } else {
      this.displayedFilter--;
    }
  }

  goToNextFilter() {
    this.dropdownflag = false;
    if (
      this.displayedFilter ==
      this._udrfService.udrfData.mainFilterArray.length - 1
    ) {
      this.displayedFilter = 0;
    } else {
      this.displayedFilter++;
    }
  }

  // async onSelectAwaitingRequests(normalizedMonth: moment.Moment, dp) {
  //   dp.close();
  //   this.monthSelected = false;
  //   let month = moment(normalizedMonth);
  //   await this.getMonthStartAndEndDate(month);
  //   await this.getLeaveDetails();
  // }
  // async getMonthStartAndEndDate(month: moment.Moment) {
  //     this.monthStartDate = moment(month).startOf('month');
  //     this.monthEndDate = moment(month).endOf('month');
  // }

  async getPendingApprovalsOnScroll() {
    if(this.pendingScrollApiInProgress)
    {
      return
    }
    this.pendingLimit += 15;
    await this.getLeaveDetails();
  }

  // Function to get leave request that is applied by the user
  async getAllPendingLeaveDetails() {
        await this._leaveAppService.getAllPendingLeaveDetails(this.currentUserOid)
          .then(async res => {
            if (res['messType'] == 'S' && res['data'] && res['data'].length > 0) {
                  this.getAllLeaveDetails = res['data'];
              for (let items of this.getAllLeaveDetails) {
                let dateArray = items.dates.sort(function (a, b) {
                  return new Date(a).valueOf() - new Date(b).valueOf()
                })
                items.uiFormattedDate = moment(dateArray[0]).format("DD MMM YYYY") + ' - ' + moment(dateArray[dateArray.length - 1]).format("DD MMM YYYY");
              }
            }
            else {
              this.getAllLeaveDetails = [];
            }
          }, err => {
            this.errorService.userErrorAlert((err && err.code) ? err.code : (err && err.error) ? err.error.code : 'NIL', "Something Went Wrong Kindly Try After Some Time", (err && err.params) ? err.params : (err && err.error) ? err.error.params : {})
          });
  }
  /**
   * Work From Home Apply Function
   */
  async openWorkFromHome(){
    this.disableBtn = true;
    let modalParams = {
      associateOid: this.currentUserOid,
      restrictWeekOff: + this.restrictWeekOff,
      holidayList: this.holidayList,
      leaveDaysList: [],
      wfhDetails: _.filter(this.compOffLeaveData, { is_claim: 1 }),
      weekOffDetails: this.weekOffData,
      employeeDetails: this.employeeDetails,
      orgLeaveYearType: this.orgLeaveYearType,
      daysCompOfConsidered: this.daysCompOfConsidered,
      iswfhSplitUpAllowed : this.isCompOffSplitUpAllowed,
      pendingRequestsFilterConfig : this.pendingRequestsFilterConfig,
      wfhTypeConfig: this.sessionData,
      wfhNeedToAppliedAfterDoj: this.compOffNeedToAppliedAfterDoj,
      leaveBalanceData: _.filter(this.leaveBalanceDetails, {leaveId: 55})?.length > 0 ? _.filter(this.leaveBalanceDetails, {leaveId: 55}) : []
    }
    const { ApplyWfhComponent } = await import('../components/apply-wfh/apply-wfh.component');
    const applyWfhComponent = this.matDialog.open(ApplyWfhComponent, {
      height: '100%',
      width: '752px',
      position: { right: '0px' },
      data: {
        modalParams: modalParams
      }
    });
    applyWfhComponent.afterClosed().subscribe(res => {
      if (res.event == "submit") {
        this.initPendingRequests();
      }
      this.disableBtn = false;
      this.getEmployeeLeaveDetails();
    });
    applyWfhComponent.backdropClick().subscribe(() => {
      this.disableBtn = false;
      this.getEmployeeLeaveDetails();
    });
  }

  ngOnDestroy() {
    this._onDestroy.next();
    this._onDestroy.complete();
  }
}
