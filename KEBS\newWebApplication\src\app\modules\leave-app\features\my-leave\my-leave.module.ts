import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { MyLeaveRoutingModule } from './my-leave-routing.module';
import { LeaveLandingPageComponent } from './leave-landing-page/leave-landing-page.component';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatInputModule } from '@angular/material/input';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatCardModule } from '@angular/material/card';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { TooltipModule } from "ng2-tooltip-directive";
import { InfiniteScrollModule } from "ngx-infinite-scroll";
import { SharedComponentsModule } from "src/app/app-shared/app-shared-components/components.module";
import { MatDialogModule } from "@angular/material/dialog";
import { MatDatepickerModule } from '@angular/material/datepicker';
import { ApplyLeaveComponent } from './components/apply-leave/apply-leave.component';
import { MatDividerModule } from '@angular/material/divider'
import { MatTabsModule } from '@angular/material/tabs'
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { FileUploadModule } from 'ng2-file-upload';
import { MyLeaveDetailComponent } from './components/my-leave-detail/my-leave-detail.component';
import { LeavePolicyComponent } from './components/leave-policy/leave-policy.component';
import { HowToApplyLeaveComponent } from './components/how-to-apply-leave/how-to-apply-leave.component';
import { CompensationOffComponent } from './components/compensation-off/compensation-off.component';
import { WithdrawReasonModalComponent } from './components/withdraw-reason-modal/withdraw-reason-modal.component';
import { AttachmentMgmtModule } from 'src/app/modules/shared-lazy-loaded-components/attachment-mgmt/attachment-mgmt.module';
import { NgxDocViewerModule } from 'ngx-doc-viewer';
import {ClipboardModule} from '@angular/cdk/clipboard';
import { ApplyWfhComponent } from './components/apply-wfh/apply-wfh.component';

@NgModule({
  declarations: [LeaveLandingPageComponent, ApplyLeaveComponent, MyLeaveDetailComponent, LeavePolicyComponent, HowToApplyLeaveComponent, CompensationOffComponent, WithdrawReasonModalComponent, ApplyWfhComponent],
  imports: [
    CommonModule,
    MyLeaveRoutingModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatMenuModule,
    FormsModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatTooltipModule,
    MatCardModule,
    MatProgressSpinnerModule,
    TooltipModule,
    InfiniteScrollModule,
    SharedComponentsModule,
    MatDialogModule,
    MatDatepickerModule,
    MatDividerModule,
    FileUploadModule,
    MatTabsModule,
    AttachmentMgmtModule,
    NgxDocViewerModule,
    MatButtonToggleModule,
    MatCheckboxModule,
    ClipboardModule
  ],
  exports: [ LeaveLandingPageComponent ]
})
export class MyLeaveModule { }
