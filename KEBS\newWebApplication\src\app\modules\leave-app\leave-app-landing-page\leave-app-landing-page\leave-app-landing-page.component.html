<div *ngIf="shouldTabBeVisible">
    <div *ngIf="currentUserIsAdmin || currentUserIsLeaveAmin">
        <nav mat-tab-nav-bar>
            <div *ngFor="let link of leaveTabLinks">
                <a mat-tab-link [routerLink]="link.path" routerLinkActive #rla="routerLinkActive" [active]="rla.isActive"
                    *ngIf="link.toDisplay">
                    {{ link.label }}
                </a>
            </div>
        </nav>
    </div>
</div>
<router-outlet></router-outlet>

