import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import * as moment from 'moment';
import { Moment } from 'moment';
import { Subject } from 'rxjs';
@Injectable({
  providedIn: 'root'
})
export class LeaveAppService {

  currentUserIsAnAdmin: boolean;

  shouldTabBeVisibleSub = new Subject();

  constructor(private $http: HttpClient) { }

  uiConfig(associateId, oid)
  {
    return this.$http.post('/api/leaveapp/leavePrimary/getLeaveUIConfig', {
      associateId: associateId,
      associateOid: oid
    });
  }

  applyLeave(leaveDetails, isClaim, associateId,pendingRequestsFilterConfig)
  {
    return this.$http.post('/api/leaveapp/leavePrimary/applyLeave', {
      leaveDetails: leaveDetails,
      isClaim: isClaim,
      associateId: associateId,
      filterConfig:pendingRequestsFilterConfig,
    });
  }

  getLeaveDetails(associateOid,pendingRequestsFilterConfig,pendingLimit)
  {
    return this.$http.post('/api/leaveapp/leavePrimary/getLeaveDetails', {
      associateOid: associateOid,
      filterConfig:pendingRequestsFilterConfig,
      pendingLimit:pendingLimit
    });

  }

  getEmployeeLeaveDetails(associateId, associateOid)
  {
    return this.$http.post('/api/leaveapp/leavePrimary/getEmployeeLeaveDetails', {
      associateId: associateId,
      associateOid: associateOid
    });
  }

  getEmployeeWeekOff(associateOid, associateId)
  {
    return this.$http.post('/api/leaveapp/leavePrimary/getEmployeeWeekOff', {
      associateOid: associateOid,
      associateId: associateId
    });
  }

  getHolidayListOfMonth(currDateTime, associateOid, associateId) {

    return this.$http.post('/api/leaveapp/leavePrimary/getHolidayList', {
      currDateTime: currDateTime,
      associateOid: associateOid,
      associateId: associateId
    });

  }

  withdrawLeaveRequest(associateOid, id, workflowHeaderId, statusId, isClaim, withdrawReason)
  {
    return this.$http.post('/api/leaveapp/leavePrimary/withdrawLeaveRequest', {
      associateOid: associateOid,
      leaveId: id,
      workflowHeaderId: workflowHeaderId,
      statusId: statusId,
      isClaim: isClaim,
      withdrawReason: withdrawReason
    });
  }

  getLeaveAttachment(fileKey)
  {
    return this.$http.post('/api/leaveapp/leavePrimary/getLeaveAttachment', {
      keyName: fileKey
    });
  }


  formatDatesToLeaveApp(dates){
    
    const parseDate = (date) => moment(date, 'YYYY-MM-DD');
    
    const pushOrSetEnd = (results, last, prev, curr) => {
      if (moment.duration(parseDate(curr).diff(parseDate(prev))).asDays() <= 1) {
        last.end = curr;
      } else {
      results.push({ start: curr });
    }
  };
  
  const reduceDatesIntoRanges = (dates) =>
  dates.slice(1).reduce((acc, date) => {
    const last = acc[acc.length - 1];
    pushOrSetEnd(acc, last, last.end ? last.end : last.start, date);
    return acc;
  }, [{ start: dates[0] }]);
  
  const dateRanges = reduceDatesIntoRanges(dates);
  
  const formatDateWithYear = (date) => parseDate(date).format('DD MMM YYYY');
  
  const formatDate = (date) => parseDate(date).format('DD MMM YYYY');
  
  const formatDateRange = ({ start, end }) => `${formatDate(start)} - ${formatDate(end)}`;
  
  const formattedDateRanges = dateRanges.map(dateRange =>
  !dateRange.end ? formatDateWithYear(dateRange.start) : formatDateRange(dateRange));

  return formattedDateRanges

  }

  getLeavePolicy(associateId)
  {
    return this.$http.post('/api/tsPrimary/getTermsForTS',{
      associateId: associateId
    })
  }

  checkIfEmployeeIsApprover(associateOid)
  {
    return this.$http.post('/api/leaveapp/leavePrimary/checkIfEmployeeIsApprover', {
      associateOid: associateOid
    });
  }

  getAllStatus()
  {
    return this.$http.post('/api/leaveapp/leavePrimary/getAllStatus', {
    });
  }

  getLeaveRequests(associateOid, statusId, isClaim, sortOrder, index)
  {
    return this.$http.post('/api/leaveapp/leavePrimary/getLeaveRequests', {
      associateOid: associateOid,
      statusId: statusId, 
      isClaim: isClaim,
      sortOrder: sortOrder,
      index: index
    });
  }

  updateLeaveRequestStatus(associateOid, workflowHeaderId, statusCode, reason)
  {
    return this.$http.post('/api/leaveapp/leavePrimary/updateLeaveRequestStatus', {
      associateOid: associateOid,
      workflowHeaderId: workflowHeaderId,
      statusCode: statusCode,
      comments: reason
    });
  }

  getEmployeeDetails(associateOid, associateId)
  {
    return this.$http.post('/api/leaveapp/leavePrimary/getEmployeeDetails',{
      associateOid: associateOid,
      associateId: associateId
    });
  }

  joiningDateLeaveBckgProgram(associateId, date){
    return this.$http.post('/api/bgjPrimary/joiningDateLeaveBckgProgram',{
      associateId: associateId,
      date: date
    });
  }

  checkForEmployeeDurationCompleted(associateId, date)
  {
    return this.$http.post('/api/bgjPrimary/checkForEmployeeDurationCompleted',{
      associateId: associateId,
      date: date
    });
  }

  updateLeaveBalanceEveryMonth(associateId, date){
    return this.$http.post('/api/bgjPrimary/updateLeaveBalanceEveryMonth',{
      associateId: associateId,
      date: date
    });
  }

  getEmployeeLeaveBalanceFromE360(associateId){
    return this.$http.post('/api/leaveapp/leavePrimary/getLeaveBalanceOfEmployeeFromE360',{
      associateId: associateId
    });
  }

  getEmployeeLeaveDetailsFromE360(associateId){
    return this.$http.post('/api/leaveapp/leavePrimary/getLeaveHistoryOfEmployeeFromE360',{
      associateId: associateId
    });
  }

  updateCompOffEveryDay(date){
    return this.$http.post('/api/bgjPrimary/clearCompOffBalanceAfterExpired',{
      date: date
    });
  }

  getLeaveDetailsForInbox(associateOid, sortOrder, index){
    return this.$http.post('/api/leaveapp/leavePrimary/getLeaveDetailsForInbox',{
      associateOid: associateOid,
      sortOrder: sortOrder,
      index: index
    })
  }

  everyYearLeaveProgram(associateId, date = null){
    return this.$http.post('/api/bgjPrimary/leaveQuotaUpdationEveryYear',{
      associateId: associateId,
      date: date
    });
  }

  getHolidayList(associateId, assoicateOid){
    return this.$http.post('/api/leaveapp/leavePrimary/getHolidayListForEmployee',{
      associateId: associateId,
      oid: assoicateOid
    });
  }

  getTeamsCalenderMonthData(associateId, oid, startDay,endDay){
    return this.$http.post('/api/leaveapp/leavePrimary/getTeamsCalender',{
      associateId: associateId,
      oid: oid,
      startDay:startDay,
      endDay:endDay
    })
  }

  getLeaveTypesAndStatus(oid, associateId){
    return this.$http.post('/api/leaveapp/leavePrimary/getLeaveTypesAndStatus',{
      associateId: associateId,
      oid: oid
    });
  }

  shouldTabBeVisibleChange(value) {
    this.shouldTabBeVisibleSub.next(value)
  }

  getLeaveApprover(associateId){
    return this.$http.post('/api/leaveapp/leavePrimary/getLeaveApprover',{
      associateId: associateId
    });
  }

  getAllPendingLeaveDetails(associateOid)
  {
    return new Promise((resolve,reject)=>(
      this.$http.post('/api/leaveapp/leavePrimary/getAllLeaveDetails', {
        associateOid: associateOid
      }).subscribe(res=>{
        resolve(res);
      },
      err=>{
        reject(err);
      })
     )) 
  }  
  
  approvalNotificationRemainder(){
    return new Promise((resolve,reject)=>(
      this.$http.post('/api/leaveapp/leavePrimary/triggerapprovalremainder', {
      }).subscribe(res=>{
        resolve(res);
      },
      err=>{
        reject(err);
      })
     )) 
  }

  everyYearLeaveAutoApprovalProgram(){
    return new Promise((resolve,reject)=>(
      this.$http.post('/api/leaveapp/leavePrimary/leaveRequestUpdationEveryYear', {
      }).subscribe(res=>{
        resolve(res);
      },
      err=>{
        reject(err);
      })
     )) 
  }

  getLeaveTypeForSettings(){
    return this.$http.post('/api/leaveapp/leavePrimary/getLeaveTypeForSettings',{
    });
  }

  getLeavePoliciesForSettings(){
    return this.$http.post('/api/leaveapp/leavePrimary/getLeavePolicyForSettings',{
    });
  }

  updateLeaveTypeBlockStatus(items){
    return this.$http.post('/api/leaveapp/leavePrimary/updateLeaveTypeBlockStatus',{
      items: items
    });
  }

  insertLeaveType(leaveTypeDetails){
    return this.$http.post('/api/leaveapp/leavePrimary/insertLeaveType',{
      leaveTypeDetails: leaveTypeDetails
    });
  }

  getLeaveTypeDetails(leaveId){
    return this.$http.post('/api/leaveapp/leavePrimary/getLeaveTypeDetails',{
      leave_id: leaveId
    });
  }

  updateLeaveType(leaveTypeDetails,leave_id){
    return this.$http.post('/api/leaveapp/leavePrimary/updateLeaveType',{
      leaveTypeDetails: leaveTypeDetails,
      id:leave_id
    });
  }

  getLeaveRuleCreationMasterDetails(){
    return this.$http.post('/api/leaveapp/leavePrimary/getLeaveRuleCreationMasterDetails',{
    });
  }

  insertLeavePolicy(leaveRuleDetails){
    return this.$http.post('/api/leaveapp/leavePrimary/insertLeavePolicy',{
      leaveRuleDetails: leaveRuleDetails
    });
  }

  removeLeavePolicy(id){
    return this.$http.post('/api/leaveapp/leavePrimary/removeLeavePolicy',{
      id: id
    });
  }

  removeLeaveType(id){
    return this.$http.post('/api/leaveapp/leavePrimary/removeLeaveType',{
      id: id
    });
  }

  updateLeavePolicy(id,leavePolicyDetails){
    return this.$http.post('/api/leaveapp/leavePrimary/updateLeavePolicy',{
      id: id,
      leavePolicyDetails:leavePolicyDetails
    });
  }

  
  leaveBalanceCreditProgramBasedOnRule(jobIntervalType,currentDate,associateIds,ruleId){
    return this.$http.post('/api/leaveapp/leavePrimary/leaveBalanceCreditProgramBasedOnRule',{
      jobIntervalType: jobIntervalType,
      currentDate: currentDate,
      associateIds:associateIds,
      ruleId: ruleId
    })
  }

  getDirectReportingEmployees(aid, oid){
    return this.$http.post('/api/leaveapp/leavePrimary/getDirectReportingEmployees',{
      aid: aid,
      oid: oid
    })
  }
}
