import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';

import { Subject, Subscription } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

import { AiServiceService } from 'src/app/modules/main/services/ai-service/ai-service.service';
import { ToasterService } from 'src/app/modules/applicant-tracking-system/shared-components/ats-custom-toast/toaster.service';

@Component({
  selector: 'app-chat-ui',
  templateUrl: './chat-ui.component.html',
  styleUrls: ['./chat-ui.component.scss'],
})
export class ChatUiComponent implements OnInit {
  @Input() chatLoader: string = '';
  @Input() aiIconShort: string = '';
  @Input() aid: number = null;
  @Input() oid: string = '';
  @Input() currentThreadData: any = [];
  @Input() isThreadLoading: boolean = false;
  @Input() isPromptApiInProgress: boolean = false;
  @Input() chatResponseText: any = [];
  @Input() moduleList: any = [];
  @Input() customizePromptConfig: any = null;

  @Output() regenerateResponseEmitter: EventEmitter<any> =
    new EventEmitter<any>();

  @Output() generateResponseEmitter: EventEmitter<any> =
    new EventEmitter<any>();

  protected _onDestroy = new Subject<void>();

  constructor(
    private _aiService: AiServiceService,
    private _toaster: ToasterService
  ) {}

  ngOnInit() {
  }

  /**
   * @description Copy response to clipboard
   * @param {object} item
   */
  copyToClipboard(item: any): void {
    item.isCopying = true;

    // Reset isCopying back to false after 3 seconds
    setTimeout(() => {
      item.isCopying = false;
    }, 2000);
  }

  /**
   * @description Regenerate response
   * @param {object} data
   * @param {int} index
   */
  regenerateResponse(data, index,regenerate = true) {
    this.regenerateResponseEmitter.emit({ data: data, index: index,regenerate: regenerate,mode:data?.mode });
  }

  /**
   * @description To like and dislike a response
   * @param {boolean} value
   * @param {object} item
   */
  async likeAndDislikeResponse(value, item) {
    if (value === null) {
      return;
    } else {
      let payload = {
        aid: this.aid,
        oid: this.oid,
        chat_id: item._id,
        is_liked: value,
      };

      return new Promise((resolve, reject) => {
        this._aiService
          .likeAndDislikeResponse(payload)
          .pipe(takeUntil(this._onDestroy))
          .subscribe({
            next: (res: any) => {
              if (res['err'] == false) {
                item.is_liked = value;
              } else {
                this._toaster.showError('Error', res['msg'], 7000);
              }
              resolve(true);
            },
            error: (err) => {
              this._toaster.showError(
                'Error',
                'Error in Updating Response',
                7000
              );
              reject();
            },
          });
      });
    }
  }
}
