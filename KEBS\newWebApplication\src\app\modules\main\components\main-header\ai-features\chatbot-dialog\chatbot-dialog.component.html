<div class="bg-container">
  <div *ngIf="isHistoryVisible" class="history-container">
    <div class="header">
      <div class="main-title">History</div>
      <div class="svg-icon" tooltip="Close History" (click)="closeHistory()">
        <svg width="8" height="8" viewBox="0 0 8 8" fill="none">
          <path
            d="M1.59984 7.33359L0.666504 6.40026L3.0665 4.00026L0.666504 1.61693L1.59984 0.683594L3.99984 3.08359L6.38317 0.683594L7.3165 1.61693L4.9165 4.00026L7.3165 6.40026L6.38317 7.33359L3.99984 4.93359L1.59984 7.33359Z"
            fill="#111434"
          />
        </svg>
      </div>
    </div>
    <div class="search-ui">
      <div class="search-bar">
        <input
          type="text"
          [(ngModel)]="historySearchParams"
          placeholder="Search From Threads"
          (keydown.enter)="onEnterSearchHistory()"
        />
      </div>
      <div *ngIf="!historySearchParams" class="svg-icon">
        <svg
          (click)="onEnterSearchHistory()"
          width="16"
          height="16"
          viewBox="0 0 16 16"
          fill="none"
        >
          <mask
            id="mask0_1126_15698"
            style="mask-type: alpha"
            maskUnits="userSpaceOnUse"
            x="0"
            y="0"
            width="16"
            height="16"
          >
            <rect width="16" height="16" fill="#D9D9D9" />
          </mask>
          <g mask="url(#mask0_1126_15698)">
            <path
              d="M12.7116 13.3178L8.7411 9.34708C8.40777 9.60008 8.04227 9.79603 7.6446 9.93492C7.24693 10.0738 6.83654 10.1432 6.41343 10.1432C5.37366 10.1432 4.48971 9.77931 3.7616 9.05142C3.03349 8.32353 2.66943 7.43997 2.66943 6.40075C2.66943 5.36164 3.03338 4.47775 3.76127 3.74908C4.48916 3.02053 5.37271 2.65625 6.41193 2.65625C7.45104 2.65625 8.33493 3.0203 9.0636 3.74842C9.79216 4.47653 10.1564 5.36047 10.1564 6.40025C10.1564 6.83614 10.0849 7.25292 9.94177 7.65058C9.79854 8.04836 9.60471 8.40747 9.36027 8.72792L13.3308 12.6984L12.7116 13.3178ZM6.41293 9.27675C7.21638 9.27675 7.89671 8.99808 8.45393 8.44075C9.01127 7.88353 9.28993 7.20319 9.28993 6.39975C9.28993 5.59631 9.01127 4.91597 8.45393 4.35875C7.89671 3.80142 7.21638 3.52275 6.41293 3.52275C5.60949 3.52275 4.92916 3.80142 4.37193 4.35875C3.8146 4.91597 3.53593 5.59631 3.53593 6.39975C3.53593 7.20319 3.8146 7.88353 4.37193 8.44075C4.92916 8.99808 5.60949 9.27675 6.41293 9.27675Z"
              fill="#7D838B"
            />
          </g>
        </svg>
      </div>
      <mat-icon
        *ngIf="historySearchParams"
        (click)="historySearchParams = ''; onEnterSearchHistory()"
        class="close-icon"
      >
        close
      </mat-icon>
    </div>
    <div *ngIf="isHistoryLoading" class="loading-state">
      <div *ngFor="let item of repeatArray" class="loader"></div>
    </div>
    <div
      *ngIf="
        (!historyGroupedDateData || historyGroupedDateData.length == 0) &&
        (!pinnedHistoryData || pinnedHistoryData.length == 0) &&
        !isHistoryLoading
      "
      class="no-history"
    >
      -No History-
    </div>
    <div
      *ngIf="
        ((historyGroupedDateData && historyGroupedDateData.length > 0) ||
          (pinnedHistoryData && pinnedHistoryData.length > 0)) &&
        !isHistoryLoading
      "
      class="content"
      infinite-scroll
      [infiniteScrollDistance]="3"
      [infiniteScrollThrottle]="100"
      (scrolled)="onHistoryDataScroll()"
      [scrollWindow]="false"
    >
      <div *ngIf="pinnedHistoryData.length" class="single-date-log">
        <div class="date">
          Pinned
          <svg
            class="pin-icon"
            height="20px"
            viewBox="0 -960 960 960"
            width="20px"
            fill="#F27A6C"
          >
            <path
              d="m624-480 96 96v72H516v228l-36 36-36-36v-228H240v-72l96-96v-264h-48v-72h384v72h-48v264Zm-282 96h276l-66-66v-294H408v294l-66 66Zm138 0Z"
            />
          </svg>
        </div>
        <div
          class="single-history"
          [ngClass]="
            log?._id == historySelectedThreadId ? 'thread-selected' : ''
          "
          *ngFor="let log of pinnedHistoryData; let i = index"
        >
          <div
            *ngIf="!log?.isRenameInProgress"
            class="history-text"
            [matTooltip]="log?.short_description"
            (click)="setSelectedHistoryThread(log?._id)"
            [ngStyle]="{
              'pointer-events':
                isThreadLoading || isPromptApiInProgress ? 'none' : ''
            }"
          >
            {{ log?.short_description }}
          </div>
          <div *ngIf="log?.isRenameInProgress" class="inline-edit">
            <input
              [id]="'#pinnedHistoryInputSearch-' + i"
              type="text"
              maxlength="150"
              placeholder="Enter a description..."
              [(ngModel)]="log.short_description"
              (keydown.enter)="onRenameThread(log?._id, log?.short_description)"
            />
          </div>
          <div
            *ngIf="!log?.isRenameInProgress"
            class="svg-icon"
            [matMenuTriggerFor]="historyMenu"
            (click)="setSelectedHistoryThreadData(log, -1, i)"
          >
            <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
              <mask
                id="mask0_1126_15589"
                style="mask-type: alpha"
                maskUnits="userSpaceOnUse"
                x="0"
                y="0"
                width="12"
                height="12"
              >
                <rect width="12" height="12" fill="#D9D9D9" />
              </mask>
              <g mask="url(#mask0_1126_15589)">
                <path
                  d="M6 9.63448C5.79375 9.63448 5.61721 9.56103 5.47038 9.41411C5.32346 9.26728 5.25 9.09073 5.25 8.88448C5.25 8.67823 5.32346 8.50165 5.47038 8.35473C5.61721 8.2079 5.79375 8.13448 6 8.13448C6.20625 8.13448 6.38279 8.2079 6.52962 8.35473C6.67654 8.50165 6.75 8.67823 6.75 8.88448C6.75 9.09073 6.67654 9.26728 6.52962 9.41411C6.38279 9.56103 6.20625 9.63448 6 9.63448ZM6 6.74986C5.79375 6.74986 5.61721 6.6764 5.47038 6.52948C5.32346 6.38265 5.25 6.20611 5.25 5.99986C5.25 5.79361 5.32346 5.61707 5.47038 5.47023C5.61721 5.32332 5.79375 5.24986 6 5.24986C6.20625 5.24986 6.38279 5.32332 6.52962 5.47023C6.67654 5.61707 6.75 5.79361 6.75 5.99986C6.75 6.20611 6.67654 6.38265 6.52962 6.52948C6.38279 6.6764 6.20625 6.74986 6 6.74986ZM6 3.86523C5.79375 3.86523 5.61721 3.79182 5.47038 3.64498C5.32346 3.49807 5.25 3.32148 5.25 3.11523C5.25 2.90898 5.32346 2.73244 5.47038 2.58561C5.61721 2.43869 5.79375 2.36523 6 2.36523C6.20625 2.36523 6.38279 2.43869 6.52962 2.58561C6.67654 2.73244 6.75 2.90898 6.75 3.11523C6.75 3.32148 6.67654 3.49807 6.52962 3.64498C6.38279 3.79182 6.20625 3.86523 6 3.86523Z"
                  fill="#5F6C81"
                />
              </g>
            </svg>
          </div>
        </div>
      </div>
      <mat-divider
        *ngIf="pinnedHistoryData.length"
        class="divider"
      ></mat-divider>
      <ng-container
        *ngFor="let item of historyGroupedDateData; let dateIndex = index"
      >
        <div class="single-date-log">
          <div class="date">
            {{ item }}
          </div>
          <div
            class="single-history"
            [ngClass]="
              log?._id == historySelectedThreadId ? 'thread-selected' : ''
            "
            *ngFor="let log of historyGroupedData[item]; let i = index"
          >
            <div
              *ngIf="!log?.isRenameInProgress"
              class="history-text"
              [matTooltip]="log?.short_description"
              (click)="setSelectedHistoryThread(log?._id)"
              [ngStyle]="{
                'pointer-events':
                  isThreadLoading || isPromptApiInProgress ? 'none' : ''
              }"
            >
              {{ log?.short_description }}
            </div>
            <div *ngIf="log?.isRenameInProgress" class="inline-edit">
              <input
                [id]="'#historyInputSearch-' + dateIndex + '-' + i"
                type="text"
                maxlength="150"
                placeholder="Enter a description..."
                [(ngModel)]="log.short_description"
                (keydown.enter)="
                  onRenameThread(log?._id, log?.short_description)
                "
              />
            </div>
            <div
              *ngIf="!log?.isRenameInProgress"
              class="svg-icon"
              [matMenuTriggerFor]="historyMenu"
              (click)="setSelectedHistoryThreadData(log, dateIndex, i)"
            >
              <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
                <mask
                  id="mask0_1126_15589"
                  style="mask-type: alpha"
                  maskUnits="userSpaceOnUse"
                  x="0"
                  y="0"
                  width="12"
                  height="12"
                >
                  <rect width="12" height="12" fill="#D9D9D9" />
                </mask>
                <g mask="url(#mask0_1126_15589)">
                  <path
                    d="M6 9.63448C5.79375 9.63448 5.61721 9.56103 5.47038 9.41411C5.32346 9.26728 5.25 9.09073 5.25 8.88448C5.25 8.67823 5.32346 8.50165 5.47038 8.35473C5.61721 8.2079 5.79375 8.13448 6 8.13448C6.20625 8.13448 6.38279 8.2079 6.52962 8.35473C6.67654 8.50165 6.75 8.67823 6.75 8.88448C6.75 9.09073 6.67654 9.26728 6.52962 9.41411C6.38279 9.56103 6.20625 9.63448 6 9.63448ZM6 6.74986C5.79375 6.74986 5.61721 6.6764 5.47038 6.52948C5.32346 6.38265 5.25 6.20611 5.25 5.99986C5.25 5.79361 5.32346 5.61707 5.47038 5.47023C5.61721 5.32332 5.79375 5.24986 6 5.24986C6.20625 5.24986 6.38279 5.32332 6.52962 5.47023C6.67654 5.61707 6.75 5.79361 6.75 5.99986C6.75 6.20611 6.67654 6.38265 6.52962 6.52948C6.38279 6.6764 6.20625 6.74986 6 6.74986ZM6 3.86523C5.79375 3.86523 5.61721 3.79182 5.47038 3.64498C5.32346 3.49807 5.25 3.32148 5.25 3.11523C5.25 2.90898 5.32346 2.73244 5.47038 2.58561C5.61721 2.43869 5.79375 2.36523 6 2.36523C6.20625 2.36523 6.38279 2.43869 6.52962 2.58561C6.67654 2.73244 6.75 2.90898 6.75 3.11523C6.75 3.32148 6.67654 3.49807 6.52962 3.64498C6.38279 3.79182 6.20625 3.86523 6 3.86523Z"
                    fill="#5F6C81"
                  />
                </g>
              </svg>
            </div>
          </div>
        </div>
        <mat-divider
          *ngIf="historyGroupedDateData.length - 1 != dateIndex"
          class="divider"
        ></mat-divider>
      </ng-container>
    </div>
  </div>
  <div class="chatbot-container">
    <div class="chatbot-container-header">
      <div class="align-items-with-gap">
        <div *ngIf="!isHistoryVisible" class="svg-icon" tooltip="History" (click)="openHistory()">
          <svg width="24" height="25" viewBox="0 0 24 25" fill="none">
            <mask
              id="mask0_1555_823"
              style="mask-type: alpha"
              maskUnits="userSpaceOnUse"
              x="0"
              y="0"
              width="24"
              height="25"
            >
              <rect y="0.845703" width="24" height="24" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_1555_823)">
              <path
                d="M11.9805 21.3457C9.97029 21.3457 8.1985 20.7322 6.66517 19.5053C5.13184 18.2783 4.14915 16.7085 3.71712 14.7957C3.66327 14.5969 3.69564 14.4143 3.81422 14.2476C3.9328 14.0809 4.10074 13.9841 4.31802 13.9572C4.52252 13.9303 4.70586 13.9707 4.86805 14.0784C5.03021 14.1861 5.14335 14.3444 5.20745 14.5534C5.58823 16.0982 6.40843 17.3665 7.66804 18.3582C8.92764 19.3498 10.3651 19.8457 11.9805 19.8457C13.9305 19.8457 15.5847 19.1665 16.943 17.8082C18.3014 16.4498 18.9805 14.7957 18.9805 12.8457C18.9805 10.8957 18.3014 9.24151 16.943 7.88318C15.5847 6.52485 13.9305 5.84568 11.9805 5.84568C10.8882 5.84568 9.86453 6.0883 8.9094 6.57355C7.95428 7.0588 7.13184 7.72643 6.4421 8.57643H8.30749C8.51998 8.57643 8.6981 8.64834 8.84185 8.79215C8.9856 8.93595 9.05747 9.11415 9.05747 9.32675C9.05747 9.53934 8.9856 9.71742 8.84185 9.861C8.6981 10.0046 8.51998 10.0764 8.30749 10.0764H4.88444C4.62836 10.0764 4.4137 9.98978 4.24047 9.81653C4.06724 9.6433 3.98062 9.42864 3.98062 9.17255V5.74953C3.98062 5.53703 4.05252 5.3589 4.19632 5.21515C4.34014 5.0714 4.51834 4.99953 4.73092 4.99953C4.94352 4.99953 5.12161 5.0714 5.2652 5.21515C5.40878 5.3589 5.48057 5.53703 5.48057 5.74953V7.36878C6.2921 6.4111 7.26389 5.66783 8.39594 5.13898C9.52799 4.61013 10.7229 4.3457 11.9805 4.3457C13.1605 4.3457 14.2658 4.56879 15.2963 5.01495C16.3268 5.4611 17.2254 6.0675 17.992 6.83415C18.7587 7.60082 19.3651 8.49935 19.8113 9.52975C20.2574 10.5602 20.4805 11.6653 20.4805 12.8451C20.4805 14.025 20.2574 15.1303 19.8113 16.1611C19.3651 17.1918 18.7587 18.0905 17.992 18.8572C17.2254 19.6239 16.3268 20.2303 15.2963 20.6764C14.2658 21.1226 13.1605 21.3457 11.9805 21.3457ZM12.7594 12.5418L15.5094 15.2919C15.6478 15.4303 15.7187 15.6044 15.7219 15.814C15.7251 16.0236 15.6543 16.2008 15.5094 16.3457C15.3645 16.4905 15.1889 16.563 14.9825 16.563C14.7761 16.563 14.6004 16.4905 14.4556 16.3457L11.5305 13.4207C11.437 13.3271 11.3684 13.2251 11.3248 13.1147C11.2812 13.0044 11.2594 12.8903 11.2594 12.7726V8.59565C11.2594 8.38317 11.3313 8.20505 11.4751 8.0613C11.6189 7.91755 11.7971 7.84568 12.0097 7.84568C12.2223 7.84568 12.4004 7.91755 12.544 8.0613C12.6876 8.20505 12.7594 8.38317 12.7594 8.59565V12.5418Z"
                fill="#1C1B1F"
              />
            </g>
          </svg>
        </div>
        <div *ngIf="isHistoryVisible" class="svg-icon" tooltip="Close History" (click)="closeHistory()">
          <svg width="24" height="25" viewBox="0 0 24 25" fill="none">
            <rect width="24" height="25" fill="#464646" />
            <g clip-path="url(#clip0_1126_2981)">
              <rect
                width="1360"
                height="768"
                transform="translate(-807 -119)"
                fill="#F9F9F9"
              />
              <g clip-path="url(#clip1_1126_2981)">
                <rect
                  width="1295"
                  height="110"
                  transform="translate(-735 -50)"
                  fill="url(#paint0_linear_1126_2981)"
                />
                <g opacity="0.6"></g>
              </g>
              <rect
                opacity="0.2"
                x="-832"
                y="-119"
                width="1385"
                height="768"
                fill="black"
              />
              <g filter="url(#filter0_ddd_1126_2981)">
                <g clip-path="url(#clip2_1126_2981)">
                  <rect
                    x="-24"
                    y="-20"
                    width="577"
                    height="667"
                    rx="8"
                    fill="white"
                  />
                  <rect
                    opacity="0.3"
                    x="-24"
                    y="-20"
                    width="577"
                    height="56"
                    fill="#D9D9D9"
                  />
                  <g opacity="0.1"></g>
                  <mask id="path-5-inside-1_1126_2981" fill="white">
                    <path
                      d="M12 23.4688C6.07 23.4688 1.25 18.6488 1.25 12.7188C1.25 6.78875 6.07 1.96875 12 1.96875C17.93 1.96875 22.75 6.78875 22.75 12.7188C22.75 18.6488 17.93 23.4688 12 23.4688ZM12 3.46875C6.9 3.46875 2.75 7.61875 2.75 12.7188C2.75 17.8188 6.9 21.9688 12 21.9688C17.1 21.9688 21.25 17.8188 21.25 12.7188C21.25 7.61875 17.1 3.46875 12 3.46875Z"
                    />
                  </mask>
                  <path
                    d="M12 23.4688C6.07 23.4688 1.25 18.6488 1.25 12.7188C1.25 6.78875 6.07 1.96875 12 1.96875C17.93 1.96875 22.75 6.78875 22.75 12.7188C22.75 18.6488 17.93 23.4688 12 23.4688ZM12 3.46875C6.9 3.46875 2.75 7.61875 2.75 12.7188C2.75 17.8188 6.9 21.9688 12 21.9688C17.1 21.9688 21.25 17.8188 21.25 12.7188C21.25 7.61875 17.1 3.46875 12 3.46875Z"
                    fill="#292D32"
                    stroke="#5F6C81"
                    stroke-width="3"
                    mask="url(#path-5-inside-1_1126_2981)"
                  />
                  <mask id="path-6-inside-2_1126_2981" fill="white">
                    <path
                      d="M13.2599 16.9989C13.0699 16.9989 12.8799 16.9289 12.7299 16.7789L9.19992 13.2489C8.90992 12.9589 8.90992 12.4789 9.19992 12.1889L12.7299 8.65891C13.0199 8.36891 13.4999 8.36891 13.7899 8.65891C14.0799 8.94891 14.0799 9.42891 13.7899 9.71891L10.7899 12.7189L13.7899 15.7189C14.0799 16.0089 14.0799 16.4889 13.7899 16.7789C13.6499 16.9289 13.4599 16.9989 13.2599 16.9989Z"
                    />
                  </mask>
                  <path
                    d="M13.2599 16.9989C13.0699 16.9989 12.8799 16.9289 12.7299 16.7789L9.19992 13.2489C8.90992 12.9589 8.90992 12.4789 9.19992 12.1889L12.7299 8.65891C13.0199 8.36891 13.4999 8.36891 13.7899 8.65891C14.0799 8.94891 14.0799 9.42891 13.7899 9.71891L10.7899 12.7189L13.7899 15.7189C14.0799 16.0089 14.0799 16.4889 13.7899 16.7789C13.6499 16.9289 13.4599 16.9989 13.2599 16.9989Z"
                    fill="#292D32"
                    stroke="#5F6C81"
                    stroke-width="3"
                    mask="url(#path-6-inside-2_1126_2981)"
                  />
                </g>
                <rect
                  x="-23.5"
                  y="-19.5"
                  width="576"
                  height="666"
                  rx="7.5"
                  stroke="#E8E9EE"
                />
              </g>
            </g>
            <defs>
              <filter
                id="filter0_ddd_1126_2981"
                x="-51"
                y="-39"
                width="631"
                height="721"
                filterUnits="userSpaceOnUse"
                color-interpolation-filters="sRGB"
              >
                <feFlood flood-opacity="0" result="BackgroundImageFix" />
                <feColorMatrix
                  in="SourceAlpha"
                  type="matrix"
                  values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                  result="hardAlpha"
                />
                <feMorphology
                  radius="7"
                  operator="dilate"
                  in="SourceAlpha"
                  result="effect1_dropShadow_1126_2981"
                />
                <feOffset dy="8" />
                <feGaussianBlur stdDeviation="10" />
                <feComposite in2="hardAlpha" operator="out" />
                <feColorMatrix
                  type="matrix"
                  values="0 0 0 0 0.933333 0 0 0 0 0.286275 0 0 0 0 0.380392 0 0 0 0.2 0"
                />
                <feBlend
                  mode="normal"
                  in2="BackgroundImageFix"
                  result="effect1_dropShadow_1126_2981"
                />
                <feColorMatrix
                  in="SourceAlpha"
                  type="matrix"
                  values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                  result="hardAlpha"
                />
                <feOffset dy="1" />
                <feGaussianBlur stdDeviation="0.5" />
                <feComposite in2="hardAlpha" operator="out" />
                <feColorMatrix
                  type="matrix"
                  values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0"
                />
                <feBlend
                  mode="normal"
                  in2="effect1_dropShadow_1126_2981"
                  result="effect2_dropShadow_1126_2981"
                />
                <feColorMatrix
                  in="SourceAlpha"
                  type="matrix"
                  values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                  result="hardAlpha"
                />
                <feOffset dy="4" />
                <feGaussianBlur stdDeviation="2" />
                <feComposite in2="hardAlpha" operator="out" />
                <feColorMatrix
                  type="matrix"
                  values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
                />
                <feBlend
                  mode="normal"
                  in2="effect2_dropShadow_1126_2981"
                  result="effect3_dropShadow_1126_2981"
                />
                <feBlend
                  mode="normal"
                  in="SourceGraphic"
                  in2="effect3_dropShadow_1126_2981"
                  result="shape"
                />
              </filter>
              <linearGradient
                id="paint0_linear_1126_2981"
                x1="0"
                y1="55"
                x2="1295"
                y2="55"
                gradientUnits="userSpaceOnUse"
              >
                <stop stop-color="white" />
                <stop offset="1" stop-color="#FBDBE1" stop-opacity="0.8" />
              </linearGradient>
              <clipPath id="clip0_1126_2981">
                <rect
                  width="1360"
                  height="768"
                  fill="white"
                  transform="translate(-807 -119)"
                />
              </clipPath>
              <clipPath id="clip1_1126_2981">
                <rect
                  width="1295"
                  height="110"
                  fill="white"
                  transform="translate(-735 -50)"
                />
              </clipPath>
              <clipPath id="clip2_1126_2981">
                <rect
                  x="-24"
                  y="-20"
                  width="577"
                  height="667"
                  rx="8"
                  fill="white"
                />
              </clipPath>
            </defs>
          </svg>
        </div>
        <div>
          <img class="ai-icon" [src]="data?.aiThemeConfig['AI-ICON']" />
        </div>
      </div>
      <div class="align-items-with-gap">
        <div
          *ngIf="!isLoading && promptLibraryData.length > 0"
          class="svg-icon"
          tooltip="FAQ"
        >
          <svg
            *ngIf="!isPromptLibraryVisible"
            (click)="openPromptLibrary()"
            width="14"
            height="15"
            viewBox="0 0 14 15"
            fill="none"
          >
            <path
              d="M1.79492 10.9117C1.91026 10.857 2.02814 10.8111 2.14859 10.7739C2.26903 10.7367 2.39903 10.7182 2.53859 10.7182H3.25642V2.51302H2.53859C2.32781 2.51302 2.1512 2.58546 2.00876 2.73035C1.8662 2.87524 1.79492 3.05069 1.79492 3.25669V10.9117ZM2.53859 14.1797C2.05426 14.1797 1.64253 14.0114 1.30342 13.6749C0.964422 13.3383 0.794922 12.9296 0.794922 12.4489V3.25669C0.794922 2.77235 0.964422 2.36063 1.30342 2.02152C1.64253 1.68252 2.05426 1.51302 2.53859 1.51302H7.29492V2.51302H4.25642V10.7182H8.66675V8.34635H9.66675V11.7182H2.53859C2.33259 11.7182 2.15714 11.7881 2.01226 11.9279C1.86737 12.0677 1.79492 12.2411 1.79492 12.4479C1.79492 12.6545 1.86737 12.8282 2.01226 12.9689C2.15714 13.1094 2.33259 13.1797 2.53859 13.1797H11.1283V7.67969H12.1283V14.1797H2.53859ZM10.1283 7.67969C10.1283 6.70391 10.4676 5.87663 11.1464 5.19785C11.8252 4.51908 12.6525 4.17969 13.6283 4.17969C12.6525 4.17969 11.8252 3.8403 11.1464 3.16152C10.4676 2.48274 10.1283 1.65547 10.1283 0.679688C10.1283 1.65547 9.78887 2.48274 9.11009 3.16152C8.43131 3.8403 7.60403 4.17969 6.62825 4.17969C7.60403 4.17969 8.43131 4.51908 9.11009 5.19785C9.78887 5.87663 10.1283 6.70391 10.1283 7.67969Z"
              fill="url(#paint0_linear_2061_126464)"
            />
            <defs>
              <linearGradient
                id="paint0_linear_2061_126464"
                x1="13.6283"
                y1="7.42969"
                x2="0.11551"
                y2="7.42969"
                gradientUnits="userSpaceOnUse"
              >
                <stop stop-color="#EF4A61" />
                <stop offset="1" stop-color="#F27A6C" />
              </linearGradient>
            </defs>
          </svg>
          <svg
            *ngIf="isPromptLibraryVisible"
            (click)="openNewChat()"
            width="28"
            height="25"
            viewBox="0 0 28 25"
            fill="none"
          >
            <mask
              id="mask0_1534_29427"
              style="mask-type: alpha"
              maskUnits="userSpaceOnUse"
              x="6"
              y="3"
              width="17"
              height="17"
            >
              <rect
                x="6.46204"
                y="3.84497"
                width="16"
                height="16"
                fill="#D9D9D9"
              />
            </mask>
            <g mask="url(#mask0_1534_29427)">
              <path
                d="M11.2568 14.7167H12.2568V6.51156H11.2568V14.7167ZM20.1287 18.1782H10.539C10.0547 18.1782 9.64296 18.0087 9.30385 17.6697C8.96485 17.3306 8.79535 16.9189 8.79535 16.4346V7.25522C8.79535 6.77089 8.96485 6.35917 9.30385 6.02006C9.64296 5.68106 10.0547 5.51156 10.539 5.51156H14.6492C14.3526 5.87478 14.1212 6.28206 13.955 6.73339C13.7887 7.18461 13.7055 7.66622 13.7055 8.17822C13.7055 9.32689 14.0863 10.3177 14.8478 11.1507C15.6094 11.9836 16.5492 12.4573 17.6672 12.5719V15.7167H10.539C10.333 15.7167 10.1576 15.7866 10.0127 15.9264C9.86779 16.0663 9.79535 16.2396 9.79535 16.4464C9.79535 16.6531 9.86779 16.8267 10.0127 16.9674C10.1576 17.1079 10.333 17.1782 10.539 17.1782H19.1287V12.4847C19.3132 12.4419 19.4878 12.3883 19.6523 12.3237C19.8169 12.2592 19.9757 12.1816 20.1287 12.0911V18.1782ZM18.1287 11.6782C18.1287 10.7024 18.4681 9.87517 19.1468 9.19639C19.8256 8.51761 20.6529 8.17822 21.6287 8.17822C20.6529 8.17822 19.8256 7.83883 19.1468 7.16006C18.4681 6.48128 18.1287 5.654 18.1287 4.67822C18.1287 5.654 17.7893 6.48128 17.1105 7.16006C16.4317 7.83883 15.6045 8.17822 14.6287 8.17822C15.6045 8.17822 16.4317 8.51761 17.1105 9.19639C17.7893 9.87517 18.1287 10.7024 18.1287 11.6782Z"
                fill="url(#paint0_linear_1534_29427)"
              />
            </g>
            <line
              x1="11.962"
              y1="21.345"
              x2="16.962"
              y2="21.345"
              stroke="url(#paint1_linear_1534_29427)"
            />
            <defs>
              <linearGradient
                id="paint0_linear_1534_29427"
                x1="21.6287"
                y1="11.4282"
                x2="8.11594"
                y2="11.4282"
                gradientUnits="userSpaceOnUse"
              >
                <stop stop-color="#EF4A61" />
                <stop offset="1" stop-color="#F27A6C" />
              </linearGradient>
              <linearGradient
                id="paint1_linear_1534_29427"
                x1="16.962"
                y1="22.345"
                x2="11.6973"
                y2="22.345"
                gradientUnits="userSpaceOnUse"
              >
                <stop stop-color="#EF4A61" />
                <stop offset="1" stop-color="#F27A6C" />
              </linearGradient>
            </defs>
          </svg>
        </div>
        <div class="svg-icon" (click)="onDialogResizing()">
          <div tooltip="Collapse" *ngIf="dialogExpanded; else expandTemplate">
            <svg
              width="14"
              height="14"
              viewBox="0 0 14 14"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M6 7.84503V13.345H4.5V9.34503H0.5V7.84503H6ZM9.5 0.345032V4.34503H13.5V5.84503H8V0.345032H9.5Z"
                fill="#1C1B1F"
              />
            </svg>
          </div>
          <ng-template #expandTemplate>
            <div tooltip="Expand">
              <svg
              width="14"
              height="14"
              viewBox="0 0 14 14"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M0.5 13.345V7.84503H2V11.845H6V13.345H0.5ZM12 5.84503V1.84503H8V0.345032H13.5V5.84503H12Z"
                fill="#1C1B1F"
              />
            </svg>
            </div>
          </ng-template>
        </div>
        <div class="svg-icon" tooltip="Close" (click)="onDialogClose()">
          <svg width="24" height="25" viewBox="0 0 24 25" fill="none">
            <mask
              id="mask0_1555_843"
              style="mask-type: alpha"
              maskUnits="userSpaceOnUse"
              x="0"
              y="0"
              width="24"
              height="25"
            >
              <rect y="0.845703" width="24" height="24" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_1555_843)">
              <path
                d="M8.227 17.6813L7.16357 16.6178L10.9367 12.8447L7.16357 9.09663L8.227 8.0332L12.0001 11.8063L15.7482 8.0332L16.8116 9.09663L13.0385 12.8447L16.8116 16.6178L15.7482 17.6813L12.0001 13.9082L8.227 17.6813Z"
                fill="#1C1B1F"
              />
            </g>
          </svg>
        </div>
      </div>
    </div>
    <div
      class="chatbot-container-content"
      [ngStyle]="{
        height: isChatVisible
          ? 'calc(var(--kebsChatbotContentHeight) + 52px)'
          : 'calc(var(--kebsChatbotContentHeight) + 52px)'
      }"
    >
      <!-- Loading UI -->
      <ng-container *ngIf="isThreadLoading || isLoading">
        <div class="loading-container">
          <img class="gif" [src]="data?.aiThemeConfig['AI-MAIN-LOADER']" />
          <div *ngIf="!isLoading" class="carousel-text">
            <div class="text-content" [class.slide]="isSliding">
              <div class="main-text">
                {{ threadTexts[currentThreadLoadTextIndex].text }}
              </div>
              <div class="sub-text">
                {{ threadTexts[currentThreadLoadTextIndex].sub_text }}
              </div>
            </div>
          </div>
        </div>
      </ng-container>

      <!-- Main Content -->
      <div
        #chatScrollContainer
        *ngIf="!isThreadLoading && !isLoading && !isPromptLibraryVisible"
        class="main-content"
        [ngStyle]="{
          height: isChatVisible
            ? 'calc(var(--kebsChatbotContentHeight) + 26px)'
            : 'calc(var(--kebsChatbotContentHeight) + 28px)'
        }"
      >
        <!-- Home Screen UI -->
        <ng-container *ngIf="isHomeScreenVisible">
          <div class="header-content-alignment">
            <img
              class="ai-img-header"
              [src]="data?.aiThemeConfig['AI-ICON-SHORT']"
            />
            <div class="align-items-column">
              <div
                class="main-text"
                [ngClass]="dialogExpanded ? 'header-content' : ''"
              >
                {{
                  getGreetingWithName(
                    data?.aiThemeConfig["WELCOME-TEXT-001"],
                    profile?.name
                  )
                }}
              </div>
              <div
                class="sub-text"
                [ngClass]="dialogExpanded ? 'header-content' : ''"
              >
                {{ data?.aiThemeConfig["WELCOME-TEXT-002"] }}
              </div>
            </div>
          </div>

          <div
            class="section-title"
            *ngIf="
              data?.aiThemeConfig['FEATURE-LIST'] &&
              data?.aiThemeConfig['FEATURE-LIST'].length > 0
            "
          >
            {{ data?.aiThemeConfig["WELCOME-TEXT-007"] }}
          </div>

          <div
            class="features"
            *ngIf="
              data?.aiThemeConfig['FEATURE-LIST'] &&
              data?.aiThemeConfig['FEATURE-LIST'].length > 0
            "
          >
            <div
              class="scroll-button scroll-left"
              *ngIf="canScrollLeft"
              (click)="scroll(-1)"
            >
              <svg
                width="10"
                height="16"
                viewBox="0 0 10 16"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M2.64083 8.00001L8.91812 14.2773C9.08367 14.4429 9.1643 14.64 9.15999 14.8686C9.15569 15.0972 9.07076 15.2943 8.9052 15.46C8.73965 15.6256 8.54256 15.7083 8.31395 15.7083C8.08534 15.7083 7.88819 15.6256 7.72249 15.46L1.3427 9.0673C1.19215 8.91661 1.08055 8.74779 1.00791 8.56084C0.935128 8.3739 0.89874 8.18695 0.89874 8.00001C0.89874 7.81307 0.935128 7.62612 1.00791 7.43918C1.08055 7.25223 1.19215 7.08341 1.3427 6.93272L7.73541 0.54C7.90096 0.374497 8.0959 0.293791 8.3202 0.298093C8.54465 0.302399 8.73965 0.38733 8.9052 0.552924C9.07076 0.718517 9.15353 0.915644 9.15353 1.14425C9.15353 1.37286 9.07076 1.56999 8.9052 1.73559L2.64083 8.00001Z"
                  fill="#EE4961"
                />
              </svg>
            </div>
            <div class="card-items-wrapper" #cardWrapper *ngIf="data?.aiThemeConfig['FEATURE-LIST'] && data?.aiThemeConfig['FEATURE-LIST'].length > 0">
              <div
                class="feature-widget-class"
                *ngFor="
                  let feature of data?.aiThemeConfig['FEATURE-LIST'];
                  let i = index
                "
                (click)="
                  resetUiState('isChatVisible');
                  setVisibility(feature?.visibility)
                "
              >
                <div class="align-content-left">
                  <div
                    class="svg-container"
                    [innerHtml]="feature?.svg | safeHtml"
                  ></div>
                  <div class="feature-widget-text-class">
                    <div class="widget-header">
                      {{ feature?.header }}
                    </div>
                    {{ feature?.sub_text }}
                  </div>
                </div>
                <span>
                  <svg
                    width="16"
                    height="16"
                    viewBox="0 0 16 16"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M9.15542 8L6.87188 10.2838C6.79382 10.359 6.75479 10.4531 6.75479 10.566C6.75479 10.6791 6.79382 10.7762 6.87188 10.8573C6.95299 10.9492 7.05208 10.9925 7.16917 10.9871C7.28611 10.9818 7.38514 10.9385 7.46625 10.8573L9.8525 8.47125C9.98722 8.33653 10.0546 8.17944 10.0546 8C10.0546 7.82056 9.98722 7.66347 9.8525 7.52875L7.46146 5.13792C7.38035 5.05667 7.28208 5.01417 7.16667 5.01042C7.05125 5.00667 6.95299 5.05076 6.87188 5.14271C6.79382 5.22382 6.75667 5.32208 6.76042 5.4375C6.76417 5.55292 6.80667 5.65118 6.88792 5.73229L9.15542 8ZM8.00271 15.5C6.96562 15.5 5.99056 15.3032 5.0775 14.9096C4.16458 14.516 3.37042 13.9818 2.695 13.3071C2.01958 12.6324 1.48493 11.8389 1.09104 10.9267C0.697014 10.0146 0.5 9.03993 0.5 8.00271C0.5 6.96562 0.696805 5.99056 1.09042 5.0775C1.48403 4.16458 2.01819 3.37042 2.69292 2.695C3.36764 2.01958 4.16111 1.48493 5.07333 1.09104C5.98542 0.697014 6.96007 0.5 7.99729 0.5C9.03438 0.5 10.0094 0.696806 10.9225 1.09042C11.8354 1.48403 12.6296 2.01819 13.305 2.69292C13.9804 3.36764 14.5151 4.16111 14.909 5.07333C15.303 5.98542 15.5 6.96007 15.5 7.99729C15.5 9.03438 15.3032 10.0094 14.9096 10.9225C14.516 11.8354 13.9818 12.6296 13.3071 13.305C12.6324 13.9804 11.8389 14.5151 10.9267 14.909C10.0146 15.303 9.03993 15.5 8.00271 15.5ZM8 14.6667C9.86111 14.6667 11.4375 14.0208 12.7292 12.7292C14.0208 11.4375 14.6667 9.86111 14.6667 8C14.6667 6.13889 14.0208 4.5625 12.7292 3.27083C11.4375 1.97917 9.86111 1.33333 8 1.33333C6.13889 1.33333 4.5625 1.97917 3.27083 3.27083C1.97917 4.5625 1.33333 6.13889 1.33333 8C1.33333 9.86111 1.97917 11.4375 3.27083 12.7292C4.5625 14.0208 6.13889 14.6667 8 14.6667Z"
                      fill="#7D838B"
                    />
                  </svg>
                </span>
              </div>
            </div>
            <div
              class="scroll-button scroll-right"
              *ngIf="canScrollRight"
              (click)="scroll(1)"
            >
              <svg
                width="10"
                height="16"
                viewBox="0 0 10 16"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M7.35917 7.99999L1.08188 1.7227C0.916327 1.55714 0.835702 1.36006 0.840007 1.13145C0.844313 0.902837 0.929244 0.705684 1.0948 0.53999C1.26035 0.374434 1.45744 0.291656 1.68605 0.291656C1.91466 0.291656 2.11181 0.374434 2.27751 0.53999L8.6573 6.9327C8.80785 7.08339 8.91945 7.25221 8.99209 7.43916C9.06487 7.6261 9.10126 7.81305 9.10126 7.99999C9.10126 8.18693 9.06487 8.37388 8.99209 8.56082C8.91945 8.74777 8.80785 8.91659 8.6573 9.06728L2.26459 15.46C2.09904 15.6255 1.9041 15.7062 1.6798 15.7019C1.45535 15.6976 1.26035 15.6126 1.0948 15.4471C0.929244 15.2815 0.846466 15.0844 0.846466 14.8558C0.846466 14.6272 0.929244 14.4301 1.0948 14.2644L7.35917 7.99999Z"
                  fill="#EE4961"
                />
              </svg>
            </div>
          </div>

          <div
            *ngIf="modules && modules.length > 0"
            class="predefined-sections"
          >
            <div class="module-section">
              <div class="section-title">
                {{ data?.aiThemeConfig["WELCOME-TEXT-003"] }}
              </div>
              <div
                class="module-dropdown"
                (click)="
                  openModulesOverlay(
                    triggerModulesOverlayField,
                    triggerModulesOverlayChange
                  )
                "
                cdkOverlayOrigin
                #triggerModulesOverlay="cdkOverlayOrigin"
                #triggerModulesOverlayField
              >
                {{ currentSelectedModuleName }}
                <svg width="6" height="4" viewBox="0 0 6 4" fill="none">
                  <path
                    d="M2.64948 3.65033L0.232813 1.23366C0.199479 1.20033 0.174479 1.16421 0.157813 1.12533C0.141146 1.08644 0.132812 1.04477 0.132812 1.00033C0.132812 0.911437 0.163368 0.833659 0.224479 0.766992C0.28559 0.700326 0.366146 0.666992 0.466146 0.666992H5.53281C5.63281 0.666992 5.71337 0.700326 5.77448 0.766992C5.83559 0.833659 5.86615 0.911437 5.86615 1.00033C5.86615 1.02255 5.83281 1.10033 5.76615 1.23366L3.34948 3.65033C3.29392 3.70588 3.23837 3.74477 3.18281 3.76699C3.12726 3.78921 3.06615 3.80033 2.99948 3.80033C2.93281 3.80033 2.8717 3.78921 2.81615 3.76699C2.76059 3.74477 2.70503 3.70588 2.64948 3.65033Z"
                    fill="#5F6C81"
                  />
                </svg>
              </div>
            </div>
            <div class="section-content">
              <div
                class="section-content-align"
                [ngStyle]="{
                  'pointer-events':
                    isThreadLoading || isPromptApiInProgress ? 'none' : ''
                }"
                (click)="
                  onEnterSearchPrompt(
                    item?._id,
                    item?.prompt,
                    false,
                    null,
                    null,
                    null
                  )
                "
                *ngFor="let item of suggestedPrompts"
              >
                <svg
                  class="icon"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                >
                  <mask
                    id="mask0_1603_776"
                    style="mask-type: alpha"
                    maskUnits="userSpaceOnUse"
                    x="0"
                    y="0"
                    width="24"
                    height="24"
                  >
                    <rect width="24" height="24" fill="#D9D9D9" />
                  </mask>
                  <g mask="url(#mask0_1603_776)">
                    <path
                      d="M12 21.5769C11.4949 21.5769 11.0593 21.4019 10.6932 21.0519C10.3272 20.7019 10.1282 20.2743 10.0961 19.7692H13.9038C13.8718 20.2743 13.6727 20.7019 13.3067 21.0519C12.9407 21.4019 12.5051 21.5769 12 21.5769ZM8.25 18.3846V16.8846H15.75V18.3846H8.25ZM8.40382 15.5C7.35641 14.8487 6.52725 13.9977 5.91635 12.9471C5.30545 11.8964 5 10.7474 5 9.49998C5 7.55128 5.67948 5.89743 7.03845 4.53845C8.39743 3.17948 10.0513 2.5 12 2.5C13.9487 2.5 15.6025 3.17948 16.9615 4.53845C18.3205 5.89743 19 7.55128 19 9.49998C19 10.7474 18.6945 11.8964 18.0836 12.9471C17.4727 13.9977 16.6435 14.8487 15.5961 15.5H8.40382ZM8.84997 14H15.15C15.9 13.4666 16.4791 12.8083 16.8875 12.025C17.2958 11.2416 17.5 10.4 17.5 9.49998C17.5 7.96664 16.9666 6.66664 15.9 5.59998C14.8333 4.53331 13.5333 3.99998 12 3.99998C10.4666 3.99998 9.16664 4.53331 8.09997 5.59998C7.03331 6.66664 6.49997 7.96664 6.49997 9.49998C6.49997 10.4 6.70414 11.2416 7.11247 12.025C7.52081 12.8083 8.09997 13.4666 8.84997 14Z"
                      fill="#D4D6D8"
                    />
                  </g>
                </svg>
                <div
                  class="section-content-text"
                  [innerHTML]="highlightPromptKey(item.prompt)"
                ></div>
              </div>
            </div>
          </div>
        </ng-container>

        <!-- Chat UI -->
        <ng-container *ngIf="isChatVisible || isReportsVisible || isWidgetsVisible">
          <ng-container *ngIf="isAgentVisible">
            <div class="chat-ui-header-section">
              <div class="header" [ngClass]="dialogExpanded ? 'header-content' : ''">
                <span class="text-color">{{getFeatureValue('chat_header') ? getFeatureValue('chat_header') : ''}}</span>💡
              </div>
              
            </div>
          </ng-container>
          <ng-container *ngIf="isReportsVisible || isWidgetsVisible">
            <div class="chat-ui-header-section">
              <div class="header" [ngClass]="dialogExpanded ? 'header-content' : ''">
                <span class="text-color">{{getFeatureValue('chat_header') ? getFeatureValue('chat_header') : ''}}</span>💡
              </div>
              <div class="sub-header" [ngClass]="dialogExpanded ? 'header-content' : ''">
                {{getFeatureValue('sub_text') ? getFeatureValue('sub_text') : ''}}
              </div>
            </div>
          </ng-container>
          
          <div class="chat-container" *ngIf="isChatVisible">
            <app-chat-ui
              [aid]="profile.aid"
              [oid]="profile.oid"
              [chatLoader]="data?.aiThemeConfig['CHATBOT-CHAT-LOADER']"
              [aiIconShort]="data?.aiThemeConfig['AI-ICON-SHORT']"
              [currentThreadData]="currentThreadData"
              [isThreadLoading]="isThreadLoading"
              [isPromptApiInProgress]="isPromptApiInProgress"
              (regenerateResponseEmitter)="regenerateResponse($event)"
              [chatResponseText]="data?.aiThemeConfig['RESPONSE-TEXT']"
              [moduleList]="modules"
              [customizePromptConfig]="customizePromptData"
            ></app-chat-ui>
          </div>
        </ng-container>
      </div>

      <!-- Prompt Library -->
      <div
        *ngIf="!isLoading && isPromptLibraryVisible"
        class="prompt-library-content"
      >
        <div class="header">
          <div class="prompt-title">
            <mat-icon (click)="openNewChat()" class="prompt-title-icon"
              >arrow_back</mat-icon
            >
            <div class="prompt-title-text">AI Prompt Library</div>
          </div>
          <div class="search-bar">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
              <g clip-path="url(#clip0_1497_30370)">
                <path
                  d="M12.0207 11.0779L14.876 13.9326L13.9327 14.8759L11.078 12.0206C10.0158 12.8721 8.69468 13.3352 7.33334 13.3333C4.02134 13.3333 1.33334 10.6453 1.33334 7.33325C1.33334 4.02125 4.02134 1.33325 7.33334 1.33325C10.6453 1.33325 13.3333 4.02125 13.3333 7.33325C13.3353 8.69459 12.8722 10.0157 12.0207 11.0779ZM10.6833 10.5833C11.5294 9.71318 12.0019 8.54687 12 7.33325C12 4.75459 9.91134 2.66659 7.33334 2.66659C4.75468 2.66659 2.66668 4.75459 2.66668 7.33325C2.66668 9.91125 4.75468 11.9999 7.33334 11.9999C8.54696 12.0018 9.71327 11.5293 10.5833 10.6833L10.6833 10.5833Z"
                  fill="#B9C0CA"
                />
              </g>
              <defs>
                <clipPath id="clip0_1497_30370">
                  <rect width="16" height="16" fill="white" />
                </clipPath>
              </defs>
            </svg>
            <input
              type="text"
              placeholder="Search Prompts..."
              maxlength="50"
              [(ngModel)]="promptLibrarySearchParams"
              (ngModelChange)="onSearchParamsChange()"
            />
            <svg
              *ngIf="
                promptLibrarySearchParams &&
                promptLibrarySearchParams.length > 0
              "
              style="cursor: pointer"
              (click)="promptLibrarySearchParams = ''; onSearchParamsChange()"
              width="24"
              height="25"
              viewBox="0 0 24 25"
              fill="none"
            >
              <mask
                id="mask0_1555_843"
                maskUnits="userSpaceOnUse"
                x="0"
                y="0"
                width="24"
                height="25"
                style="mask-type: alpha"
              >
                <rect y="0.845703" width="24" height="24" fill="#D9D9D9"></rect>
              </mask>
              <g mask="url(#mask0_1555_843)">
                <path
                  d="M8.227 17.6813L7.16357 16.6178L10.9367 12.8447L7.16357 9.09663L8.227 8.0332L12.0001 11.8063L15.7482 8.0332L16.8116 9.09663L13.0385 12.8447L16.8116 16.6178L15.7482 17.6813L12.0001 13.9082L8.227 17.6813Z"
                  fill="#B9C0CA"
                ></path>
              </g>
            </svg>
          </div>
        </div>
        <div class="content">
          <div class="categories">
            <div class="header">
              <svg
                style="min-width: 16px"
                width="16"
                height="16"
                viewBox="0 0 16 16"
                fill="none"
              >
                <path
                  d="M11.3333 6.66658H12.6667C14 6.66658 14.6667 5.99992 14.6667 4.66659V3.33325C14.6667 1.99992 14 1.33325 12.6667 1.33325H11.3333C9.99999 1.33325 9.33333 1.99992 9.33333 3.33325V4.66659C9.33333 5.99992 9.99999 6.66658 11.3333 6.66658Z"
                  stroke="#111434"
                  stroke-width="1.5"
                  stroke-miterlimit="10"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M3.33333 14.6666H4.66666C5.99999 14.6666 6.66666 13.9999 6.66666 12.6666V11.3333C6.66666 9.99992 5.99999 9.33325 4.66666 9.33325H3.33333C1.99999 9.33325 1.33333 9.99992 1.33333 11.3333V12.6666C1.33333 13.9999 1.99999 14.6666 3.33333 14.6666Z"
                  stroke="#111434"
                  stroke-width="1.5"
                  stroke-miterlimit="10"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M3.99999 6.66658C5.47275 6.66658 6.66666 5.47268 6.66666 3.99992C6.66666 2.52716 5.47275 1.33325 3.99999 1.33325C2.52724 1.33325 1.33333 2.52716 1.33333 3.99992C1.33333 5.47268 2.52724 6.66658 3.99999 6.66658Z"
                  stroke="#111434"
                  stroke-width="1.5"
                  stroke-miterlimit="10"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M12 14.6666C13.4728 14.6666 14.6667 13.4727 14.6667 11.9999C14.6667 10.5272 13.4728 9.33325 12 9.33325C10.5272 9.33325 9.33333 10.5272 9.33333 11.9999C9.33333 13.4727 10.5272 14.6666 12 14.6666Z"
                  stroke="#111434"
                  stroke-width="1.5"
                  stroke-miterlimit="10"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
              <div class="categories-text">Categories</div>
              <div class="count">
                {{
                  (
                    promptLibraryData
                    | filterPromptLibrary : promptLibrarySearchParams
                  ).length > 9
                    ? (
                        promptLibraryData
                        | filterPromptLibrary : promptLibrarySearchParams
                      ).length
                    : "0" +
                      (
                        promptLibraryData
                        | filterPromptLibrary : promptLibrarySearchParams
                      ).length
                }}
              </div>
            </div>
            <div class="categories-list">
              <ng-container
                *ngFor="
                  let item of promptLibraryData
                    | filterPromptLibrary : promptLibrarySearchParams;
                  let i = index
                "
              >
                <div
                  [ngClass]="{
                    'selected-category-list':
                      currentSelectedPromptLibraryCategory == item.name
                  }"
                  class="category-list"
                  (click)="
                    setPromptLibraryIndex(i);
                    setPromptLibraryCategory(item.name)
                  "
                >
                  <svg
                    style="min-width: 14px"
                    width="14"
                    height="20"
                    viewBox="0 0 14 20"
                    fill="none"
                  >
                    <path
                      d="M7.00001 19.5769C6.49489 19.5769 6.05931 19.4019 5.69328 19.0519C5.32726 18.7019 5.12822 18.2743 5.09616 17.7692H8.90386C8.87179 18.2743 8.67275 18.7019 8.30673 19.0519C7.9407 19.4019 7.50512 19.5769 7.00001 19.5769ZM3.25003 16.3846V14.8846H10.75V16.3846H3.25003ZM3.40386 13.5C2.35644 12.8487 1.52728 11.9977 0.91638 10.9471C0.30548 9.89644 3.05176e-05 8.74741 3.05176e-05 7.49998C3.05176e-05 5.55128 0.679514 3.89743 2.03848 2.53845C3.39746 1.17948 5.05131 0.5 7.00001 0.5C8.94871 0.5 10.6025 1.17948 11.9615 2.53845C13.3205 3.89743 14 5.55128 14 7.49998C14 8.74741 13.6945 9.89644 13.0836 10.9471C12.4727 11.9977 11.6436 12.8487 10.5962 13.5H3.40386ZM3.85001 12H10.15C10.9 11.4666 11.4792 10.8083 11.8875 10.025C12.2958 9.24164 12.5 8.39998 12.5 7.49998C12.5 5.96664 11.9667 4.66664 10.9 3.59998C9.83334 2.53331 8.53334 1.99998 7.00001 1.99998C5.46667 1.99998 4.16667 2.53331 3.10001 3.59998C2.03334 4.66664 1.50001 5.96664 1.50001 7.49998C1.50001 8.39998 1.70417 9.24164 2.11251 10.025C2.52084 10.8083 3.10001 11.4666 3.85001 12Z"
                      fill="#D4D6D8"
                    />
                  </svg>
                  <div class="text" [matTooltip]="item.name">
                    {{ item.name }}
                  </div>
                  <div
                    [ngClass]="{
                      'selected-count':
                        currentSelectedPromptLibraryCategory == item.name
                    }"
                    class="count"
                  >
                    {{
                      (promptLibraryData
                        | filterPromptLibrary : promptLibrarySearchParams)[i][
                        "prompts"
                      ].length > 9
                        ? (promptLibraryData
                            | filterPromptLibrary : promptLibrarySearchParams)[
                            i
                          ]["prompts"].length
                        : "0" +
                          (promptLibraryData
                            | filterPromptLibrary : promptLibrarySearchParams)[
                            i
                          ]["prompts"].length
                    }}
                  </div>
                </div>
              </ng-container>
            </div>
          </div>
          <div
            *ngIf="
              (
                promptLibraryData
                | filterPromptLibrary : promptLibrarySearchParams
              ).length > 0
            "
            class="category-content"
          >
            <div
              *ngFor="
                let item of (promptLibraryData
                  | filterPromptLibrary : promptLibrarySearchParams)[
                  currentSelectedPromptLibraryCategoryIndex
                ]['prompts'];
                let i = index
              "
              class="single-prompt"
              (click)="copyPromptToClipboard(item)"
              [cdkCopyToClipboard]="item?.prompt || ''"
            >
              <div
                class="text"
                [innerHTML]="highlightPromptKey(item.prompt)"
              ></div>
              <div class="copy">
                <img
                  width="12px"
                  height="12px"
                  [src]="'https://assets.kebs.app/document-copy.png'"
                />
                {{ item?.isCopyInProgress ? "Copied!" : "Copy" }}
              </div>
            </div>
          </div>
          <div
            *ngIf="
              (
                promptLibraryData
                | filterPromptLibrary : promptLibrarySearchParams
              ).length == 0
            "
            class="category-content-empty-state"
          >
            No Categories Found!
          </div>
        </div>
      </div>

      <!-- Image Content -->
      <img class="bg-image-1" [src]="data?.aiThemeConfig['CHATBOT-BG-ICON']" />
      <img class="bg-image-2" [src]="data?.aiThemeConfig['CHATBOT-BG-ICON']" />
    </div>
    <div
      *ngIf="!isThreadLoading && !isLoading"
      class="chatbot-container-footer"
    >
      <div class="align-items-with-gap">
        <div
          *ngIf="!isHomeScreenVisible && !isPromptApiInProgress"
          class="svg-icon"
          (click)="openNewChat()"
          tooltip="New Chat"
        >
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path
              d="M22 8C22 4 20 2 16 2H8C4 2 2 4 2 8V21C2 21.55 2.45 22 3 22H16C20 22 22 20 22 16V12"
              stroke="#526179"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M14.5 12H15.5"
              stroke="#526179"
              stroke-width="1.5"
              stroke-miterlimit="10"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M8.5 12H12"
              stroke="#526179"
              stroke-width="1.5"
              stroke-miterlimit="10"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M12 15.5V8.5"
              stroke="#526179"
              stroke-width="1.5"
              stroke-miterlimit="10"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </div>
        <div *ngIf="isHomeScreenVisible || isPromptApiInProgress">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path
              d="M22 8C22 4 20 2 16 2H8C4 2 2 4 2 8V21C2 21.55 2.45 22 3 22H16C20 22 22 20 22 16V12"
              stroke="lightgrey"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M14.5 12H15.5"
              stroke="lightgrey"
              stroke-width="1.5"
              stroke-miterlimit="10"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M8.5 12H12"
              stroke="lightgrey"
              stroke-width="1.5"
              stroke-miterlimit="10"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M12 15.5V8.5"
              stroke="lightgrey"
              stroke-width="1.5"
              stroke-miterlimit="10"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </div>
        <div class="search-overlay-section" (mouseleave)="isTyping = false">
          <div
            class="overlay"
            *ngIf="isTyping && searchLazyLoadedList?.length > 0 && !isThreadLoading && !isLoading && !isPromptApiInProgress"
            (click)="$event.stopPropagation()"
          >
            <div
              class="prompt-content"
              *ngIf="searchLazyLoadedList?.length > 0"
              infinite-scroll
              [infiniteScrollDistance]="3"
              [infiniteScrollThrottle]="100"
              (scrolled)="onPromptDataScroll()"
              [scrollWindow]="false"
            >
              <div class="prompt-text" *ngFor="let prompt_search of searchLazyLoadedList; let i= index" (click)="selectSearchedPrompt($event,prompt_search, i)">
                <span>{{ prompt_search?.prompt }}</span>
              </div>
            </div>
          </div>
          <div class="search-ui">
            <div class="search-bar" (mouseenter)="setTypingTrue()">
              <input
                type="text"
                (focus)="setTypingTrue()" 
                (blur)="clearSearchInput()"
                maxlength="1000"
                [(ngModel)]="promptSearchParams"
                placeholder="Enter Your Prompt Here"
                (keydown.enter)="onEnterSearchPrompt(null, promptSearchParams, false, null, null, null)"
                (input)="setTypingTrue()"
              />
            </div>
            <div
              *ngIf="
                !isPromptApiInProgress &&
                promptSearchParams &&
                promptSearchParams.length > 0
              "
              class="svg-icon"
              (click)="
                onEnterSearchPrompt(
                  null,
                  promptSearchParams,
                  false,
                  null,
                  null,
                  null
                )
              "
            >
              <svg width="28" height="28" viewBox="0 0 36 36" fill="none">
                <rect
                  width="36"
                  height="36"
                  rx="18"
                  fill="url(#paint0_linear_1555_26595)"
                />
                <path
                  d="M11.807 21.3475L12.967 19.0275C13.287 18.3808 13.287 17.6275 12.967 16.9808L11.807 14.6541C10.8137 12.6675 12.9537 10.5675 14.9204 11.6075L15.947 12.1541C16.0937 12.2275 16.207 12.3475 16.267 12.4941L20.0604 20.9275C20.2137 21.2741 20.0737 21.6808 19.7404 21.8541L14.9137 24.3941C12.9537 25.4341 10.8137 23.3341 11.807 21.3475Z"
                  fill="white"
                />
                <path
                  opacity="0.4"
                  d="M20.8738 20.4003L18.3871 14.8803C18.1071 14.2603 18.7738 13.6336 19.3738 13.9536L23.2205 15.9803C24.8538 16.8403 24.8538 19.1736 23.2205 20.0336L21.8605 20.747C21.4938 20.9336 21.0471 20.7803 20.8738 20.4003Z"
                  fill="white"
                />
                <defs>
                  <linearGradient
                    id="paint0_linear_1555_26595"
                    x1="36"
                    y1="18"
                    x2="-1.90588"
                    y2="18"
                    gradientUnits="userSpaceOnUse"
                  >
                    <stop stop-color="#EF4A61" />
                    <stop offset="1" stop-color="#F27A6C" />
                  </linearGradient>
                </defs>
              </svg>
            </div>
            <div
              *ngIf="
                isPromptApiInProgress ||
                !promptSearchParams ||
                promptSearchParams.length == 0
              "
            >
              <svg width="28" height="28" viewBox="0 0 36 36" fill="none">
                <rect width="36" height="36" rx="18" fill="grey" />
                <path
                  d="M11.807 21.3475L12.967 19.0275C13.287 18.3808 13.287 17.6275 12.967 16.9808L11.807 14.6541C10.8137 12.6675 12.9537 10.5675 14.9204 11.6075L15.947 12.1541C16.0937 12.2275 16.207 12.3475 16.267 12.4941L20.0604 20.9275C20.2137 21.2741 20.0737 21.6808 19.7404 21.8541L14.9137 24.3941C12.9537 25.4341 10.8137 23.3341 11.807 21.3475Z"
                  fill="white"
                />
                <path
                  opacity="0.4"
                  d="M20.8738 20.4003L18.3871 14.8803C18.1071 14.2603 18.7738 13.6336 19.3738 13.9536L23.2205 15.9803C24.8538 16.8403 24.8538 19.1736 23.2205 20.0336L21.8605 20.747C21.4938 20.9336 21.0471 20.7803 20.8738 20.4003Z"
                  fill="white"
                />
                <defs>
                  <linearGradient
                    id="paint0_linear_1555_26595"
                    x1="36"
                    y1="18"
                    x2="-1.90588"
                    y2="18"
                    gradientUnits="userSpaceOnUse"
                  >
                    <stop stop-color="#EF4A61" />
                    <stop offset="1" stop-color="#F27A6C" />
                  </linearGradient>
                </defs>
              </svg>
            </div>
          </div>
        </div>
      </div>
      <div class="imp-text">Kais can make mistakes. Check important info.</div>
    </div>
  </div>
</div>

<!-- History Menu -->
<mat-menu #historyMenu="matMenu">
  <ng-template matMenuContent let-item="item">
    <div class="menu-content">
      <button class="menu-item" mat-menu-item (click)="renameThread()">
        <svg
          class="menu-icon"
          height="20px"
          viewBox="0 -960 960 960"
          width="20px"
          fill="#a8acb2"
        >
          <path
            d="M216-216h51l375-375-51-51-375 375v51Zm-72 72v-153l498-498q11-11 23.84-16 12.83-5 27-5 14.16 0 27.16 5t24 16l51 51q11 11 16 24t5 26.54q0 14.45-5.02 27.54T795-642L297-144H144Zm600-549-51-51 51 51Zm-127.95 76.95L591-642l51 51-25.95-25.05Z"
          />
        </svg>
        Rename
      </button>
      <mat-divider class="menu-divider"></mat-divider>
      <button
        class="menu-item"
        mat-menu-item
        (click)="archiveOrUnarchiveThread(true)"
      >
        <svg
          class="menu-icon"
          height="20px"
          viewBox="0 -960 960 960"
          width="20px"
          fill="#a8acb2"
        >
          <path
            d="M240-144v-600q0-29.7 21.15-50.85Q282.3-816 312-816h336q29.7 0 50.85 21.15Q720-773.7 720-744v600l-240-96-240 96Zm72-107 168-67 168 67v-493H312v493Zm0-493h336-336Z"
          />
        </svg>
        Archive
      </button>
      <mat-divider class="menu-divider"></mat-divider>
      <button class="menu-item" mat-menu-item (click)="deleteThread()">
        <svg
          class="menu-icon"
          height="20px"
          viewBox="0 -960 960 960"
          width="20px"
          fill="#a8acb2"
        >
          <path
            d="M312-144q-29.7 0-50.85-21.15Q240-186.3 240-216v-480h-48v-72h192v-48h192v48h192v72h-48v479.57Q720-186 698.85-165T648-144H312Zm336-552H312v480h336v-480ZM384-288h72v-336h-72v336Zm120 0h72v-336h-72v336ZM312-696v480-480Z"
          />
        </svg>
        Delete
      </button>
      <mat-divider class="menu-divider"></mat-divider>
      <button class="menu-item" mat-menu-item (click)="pinOrUnpinThread()">
        <svg
          *ngIf="selectedHistoryThreadData?.pinned"
          class="menu-icon"
          height="20px"
          viewBox="0 -960 960 960"
          width="20px"
          fill="#a8acb2"
        >
          <path
            d="M672-816v72h-48v307l-72-72v-235H408v91l-90-90-30-31v-42h384ZM480-48l-36-36v-228H240v-72l96-96v-42.46L90-768l51-51 678 679-51 51-222-223h-30v228l-36 36ZM342-384h132l-66-66-66 66Zm137-192Zm-71 126Z"
          />
        </svg>
        <svg
          *ngIf="!selectedHistoryThreadData?.pinned"
          class="menu-icon"
          height="20px"
          viewBox="0 -960 960 960"
          width="20px"
          fill="#a8acb2"
        >
          <path
            d="m624-480 96 96v72H516v228l-36 36-36-36v-228H240v-72l96-96v-264h-48v-72h384v72h-48v264Zm-282 96h276l-66-66v-294H408v294l-66 66Zm138 0Z"
          />
        </svg>
        {{ selectedHistoryThreadData?.pinned ? "Unpin" : "Pin" }}
      </button>
    </div>
  </ng-template>
</mat-menu>

<!-- Modules List Overlay -->
<ng-template
  #triggerModulesOverlayChange
  cdkConnectedOverlay
  [cdkConnectedOverlayOrigin]="triggerModulesOverlay"
>
  <div class="modules-overlay">
    <div class="search-ui">
      <input
        [id]="'#moduleInputSearch'"
        type="text"
        [(ngModel)]="moduleSearchParams"
        placeholder="Search"
      />
    </div>
    <div class="modules">
      <ng-container *ngIf="(modules | filter : moduleSearchParams).length == 0">
        <div class="single-module">No Modules Found!</div>
      </ng-container>
      <ng-container *ngIf="(modules | filter : moduleSearchParams).length > 0">
        <ng-container
          *ngFor="
            let item of modules | filter : moduleSearchParams;
            let i = index
          "
        >
          <div
            class="single-module"
            [ngClass]="{
              'selected-module': currentSelectedModuleId == item.id
            }"
            (click)="onClickModule(item.id, item.name)"
          >
            {{ item.name }}
          </div>
          <mat-divider
            *ngIf="(modules | filter : moduleSearchParams).length - 1 != i"
            class="divider"
          ></mat-divider>
        </ng-container>
      </ng-container>
    </div>
  </div>
</ng-template>
