.bg-container {
  display: flex;
}

.history-container {
  display: flex;
  flex-direction: column;
  width: 20vw;
  height: calc(100vh - var(--chatBotAdjustedHeight));
  border-right: 1px solid #e8e9ee;
  background-color: #f7f9fb;

  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0px 18px;
    height: 59px;
    background: #e6e6e6;

    .main-title {
      font-family: var(--kebsFontFamily);
      font-weight: 700;
      font-size: 14px;
      color: #111434;
    }

    .svg-icon {
      cursor: pointer;
    }
  }

  .search-ui {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 8px;
    border: 0.5px solid #d4d6d8;
    border-radius: 4px;
    height: 24px;
    padding-right: 4px;
    margin: 12px 16px;

    .search-bar {
      width: -webkit-fill-available;
      height: 100%;
      padding: 2px 6px;

      input {
        height: 100%;
        width: 100%;
        font-family: var(--kebsFontFamily);
        font-size: 10px;
        font-weight: 400;
        color: #45546e;
        outline: none;
        border: none;
        background-color: #f7f9fb;
      }

      input::placeholder {
        font-family: var(--kebsFontFamily);
        font-size: 10px;
        font-weight: 400;
        color: #d4d6d8;
      }
    }

    .svg-icon {
      cursor: pointer;
    }

    .close-icon {
      height: 12px;
      width: 12px;
      font-size: 12px;
      color: #7d838b;
      cursor: pointer;
    }
  }

  .no-history {
    font-family: var(--kebsFontFamily);
    font-size: 10px;
    font-weight: 400;
    color: #7d838b;
    width: 100%;
    text-align: center;
  }

  .loading-state {
    display: flex;
    flex-direction: column;
    height: var(--kebsChatbotHistoryContentHeight);
    overflow: hidden;
    padding: 0px 8px 0px 16px;
    margin-right: 8px;
    gap: 10px;

    .loader {
      min-height: 24px;
      width: 100%;
      border-radius: 6px;
      background: linear-gradient(
        90deg,
        #cdcdcd -24.18%,
        #f0f0f0 50.26%,
        #efefef 114.84%
      );
    }
  }

  .content {
    display: flex;
    flex-direction: column;
    height: var(--kebsChatbotHistoryContentHeight);
    overflow-y: auto;
    padding: 0px 8px 0px 16px;
    margin-right: 8px;
    gap: 10px;

    .divider {
      color: #d4d6d8;
    }

    .single-date-log {
      display: flex;
      flex-direction: column;
      gap: 4px;

      .date {
        display: flex;
        align-items: center;
        gap: 8px;
        font-family: var(--kebsFontFamily);
        font-weight: 700;
        font-size: 10px;
        color: #515965;
      }

      .pin-icon {
        font-size: 14px;
        width: 14px;
        height: 14px;
      }

      .single-history {
        display: flex;
        align-items: center;
        justify-content: space-between;
        min-height: 24px;
        gap: 8px;
        padding: 4px 4px;
        border: 1.5px solid #f7f9fb;

        &.thread-selected {
          border-radius: 4px;
          background-color: #e6e6e6;
          border: 1.5px solid #f7f9fb;
        }

        .history-text {
          font-family: var(--kebsFontFamily);
          font-weight: 400;
          font-size: 10px;
          color: #515965;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          cursor: pointer;
          width: 99%;
        }

        .inline-edit {
          width: 100%;
          height: 24px;

          input {
            height: 100%;
            width: 100%;
            font-family: var(--kebsFontFamily);
            font-size: 10px;
            font-weight: 400;
            color: #515965;
            outline: none;
            border: 0.7px solid #1890ff;
            border-radius: 4px;
          }

          input::placeholder {
            font-family: var(--kebsFontFamily);
            font-size: 10px;
            font-weight: 400;
            color: #6e7b8f;
          }
        }

        .svg-icon {
          cursor: pointer;
        }
      }

      .single-history:hover {
        border: 1.5px solid #e8e9ee;
        border-radius: 4px;
      }
    }
  }

  .content::-webkit-scrollbar {
    width: 6px !important;
    height: 6px !important;
  }

  .content::-webkit-scrollbar-thumb {
    min-height: 40px !important;
    max-height: 40px !important;
  }
}

.chatbot-container {
  display: flex;
  flex-direction: column;
  min-height: calc(-98px + 100vh);
  width: var(--kebsChatBotContentWidth);
  height: calc(-var(--chatBotAdjustedHeight) + 100vh);

  .chatbot-container-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 24px;
    background-color: #f3f3f3;

    .align-items-with-gap {
      display: flex;
      align-items: center;
      gap: 10px;

      .svg-icon {
        cursor: pointer;
      }

      .ai-icon {
        width: 60px;
        height: 28px;
      }
    }
  }

  .module-header {
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: var(--kebsFontFamily);
    font-size: 12px;
    font-weight: 500;
    color: #ffffff;
    text-transform: uppercase;
    height: 24px;
    background: #5f6c81;
  }

  .chatbot-container-content {
    height: var(--kebsChatbotContentHeight);
    padding: 12px 16px 12px 24px;
    overflow-y: auto;
    position: relative;

    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 8px;
      height: calc(var(--kebsChatbotContentHeight) - 40px);

      .gif {
        height: 80px;
        width: 80px;
      }

      .carousel-text {
        position: relative;
        height: 50px;
        width: 100%;
        overflow: hidden;
      }

      .text-content {
        position: absolute;
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        transition: transform 0.5s ease-in, opacity 0.5s ease-in;
        position: absolute;
        opacity: 1;
        transition: opacity 2s ease;

        .main-text {
          font-family: var(--kebsFontFamily);
          font-size: 14px;
          font-weight: 400;
          color: #7d838b;
        }

        .sub-text {
          font-family: var(--kebsFontFamily);
          font-size: 14px;
          font-weight: 600;
          color: #ee4961;
        }
      }

      .slide {
        animation: slideUp 2s ease forwards;
      }

      /* Keyframes to move text from bottom to top */
      @keyframes slideUp {
        0% {
          transform: translateY(100%); /* Start from bottom */
          opacity: 0;
        }
        20% {
          transform: translateY(0); /* Move to center */
          opacity: 1;
        }
        80% {
          transform: translateY(0); /* Stay in center */
          opacity: 1;
        }
        100% {
          transform: translateY(-100%); /* Move out from top */
          opacity: 0;
        }
      }
    }

    .main-content {
      display: flex;
      flex-direction: column;
      gap: 16px;
      height: calc(var(--kebsChatbotContentHeight) - 24px);
      overflow-y: auto;
      overflow-x: hidden;
      padding-right: 1%;
      position: relative;
      z-index: 1;

      .header-content-alignment {
        display: flex;
        gap: 6px;
        align-items: center;

        .ai-img-header {
          height: 60.12px;
          width: 58.5px;
        }
      }

      .align-items-column {
        display: flex;
        flex-direction: column;
        row-gap: 0px;

        .main-text {
          font-family: var(--kebsFontFamily);
          font-weight: 600;
          font-size: 16px;
          color: #ee4961;
          background: linear-gradient(
            270deg,
            #ffc12f -5.06%,
            #f16567 31.08%,
            #f16567 100.33%
          );
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;

          &.header-content {
            font-weight: 700;
            font-size: 21px;
            height: 30px;
          }
        }

        .sub-text {
          font-family: var(--kebsFontFamily);
          font-weight: 600;
          font-size: 16px;
          color: #b9c0ca;
          height: 20px;

          &.header-content {
            font-weight: 700;
            font-size: 23px;
          }
        }
      }

      .predefined-sections {
        display: flex;
        flex-direction: column;
        gap: 10px;

        .module-section {
          display: flex;
          align-items: center;
          gap: 8px;

          .module-dropdown {
            display: flex;
            align-items: center;
            gap: 6px;
            border: 1px solid #e8e9ee;
            border-radius: 4px;
            padding: 2px 8px;
            font-family: var(--kebsFontFamily);
            font-size: 13px;
            font-weight: 400;
            color: #1b2140;
            cursor: pointer;
          }

          .section-title {
            font-family: var(--kebsFontFamily);
            font-size: 14px;
            font-weight: 400;
            color: #515965;
          }
        }

        .section-content {
          display: flex;
          flex-direction: column;
          gap: 4px;

          .section-content-align {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            width: fit-content;

            .icon {
              min-width: 24px;
              min-height: 24px;
            }

            .section-content-text {
              font-family: var(--kebsFontFamily);
              font-size: 12px;
              font-weight: 400;
              color: #1b2140;
            }
          }
        }
      }

      .section-title {
        font-family: var(--kebsFontFamily);
        font-size: 14px;
        font-weight: 400;
        color: #515965;
      }

      .features {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        width: fit-content;
        gap: 12px;
        position: relative;
        width: calc(var(--kebsChatBotContentWidth) - 4vw);

        .svg-container {
          display: flex;
          align-items: center;
        }

        .report-feature {
          display: flex;
          align-items: center;
          justify-content: space-around;
          gap: 2%;
          padding: 8px;
          width: 48%;
          border-radius: 4px;
          border: 0.5px solid #e8e9ee;
          background-color: #ffeee8;
          cursor: pointer;
        }

        .widget-feature {
          display: flex;
          align-items: center;
          justify-content: space-around;
          gap: 2%;
          padding: 8px;
          width: 48%;
          border-radius: 4px;
          border: 0.5px solid #e8e9ee;
          background-color: #f1eafa;
          cursor: pointer;
        }

        .text {
          font-family: var(--kebsFontFamily);
          font-size: 14px;
          font-weight: 500;
          color: #111434;
        }

        .feature-widget-class {
          min-width: 256px;
          border-radius: 8px;
          border: 2px solid #e8e9ee;
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 2px 12px 0px;
          min-height: 54px;
          cursor: pointer;
          max-width: 260px;

          .align-content-left {
            display: flex;
            width: 95%;
            position: relative;
            padding-right: 8px;
            gap: 10px;
          }

          .feature-widget-text-class {
            display: flex;
            color: #6e7b8f;
            font-family: var(--kebsFontFamily);
            font-weight: 400;
            font-size: 10px;
            row-gap: 4px;
            width: 86%;
            position: relative;
            flex-direction: column;
            overflow: hidden;
            white-space: break-spaces;
            line-height: normal;

            .widget-header {
              color: #45546e;
              font-family: var(--kebsFontFamily);
              font-weight: 600;
              font-size: 12px;
              text-overflow: ellipsis;
              max-width: 99%;
              white-space: nowrap;
              overflow: hidden;
            }
          }
        }
      }

      .card-items-wrapper {
        display: flex;
        overflow-x: hidden;
        scroll-behavior: smooth;
        white-space: nowrap; /* Ensure cards are in a row */
        flex-grow: 1;
        gap: 12px;
      }

      .scroll-button {
        justify-content: center;
        cursor: pointer;
        top: 50%;
        transform: translateY(-50%);
        background-color: white;
        border: none;
        padding: 5px;
        height: 54px;
        display: flex;
        align-items: center;
        position: relative;
        border-radius: 8px;
        border: 1.5px solid #e8e9ee;
        width: 28px;
      }

      .scroll-right {
        right: 0;
      }

      .scroll-left {
        left: 0;
      }

      .scroll-button.disable {
        opacity: 0.3;
        pointer-events: none;
      }

      .chat-container {
        display: flex;
        flex-direction: column;
        gap: 24px;
      }

      .modules {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        gap: 20px;
        height: calc(var(--kebsChatbotContentHeight) - 20px);
        overflow-y: auto;

        .choose-module-text {
          font-family: var(--kebsFontFamily);
          font-size: 16px;
          font-weight: 700;
          text-align: center;
        }
      }

      .modules-content {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        width: 75%;
        gap: 12px;

        .module-chip {
          display: flex;
          align-items: center;
          gap: 8px;
          border: 1px solid #b9c0ca;
          border-radius: 4px;
          padding: 4px 8px;
          cursor: pointer;

          .icon {
            width: 18px;
            height: 18px;
            font-size: 18px;
            color: #b9c0ca;
          }

          .text {
            font-family: var(--kebsFontFamily);
            font-size: 14px;
            font-weight: 400;
            color: #7d838b;
          }
        }
      }
    }

    .main-content::-webkit-scrollbar {
      width: 6px !important;
      height: 6px !important;
    }

    .main-content::-webkit-scrollbar-thumb {
      min-height: 40px !important;
      max-height: 40px !important;
    }

    .prompt-library-content {
      display: flex;
      flex-direction: column;
      gap: 16px;
      height: calc(var(--kebsChatbotContentHeight) + 24px);
      overflow: hidden;
      position: relative;
      z-index: 1;

      .header {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .prompt-title {
          display: flex;
          align-items: center;
          gap: 12px;

          .prompt-title-icon {
            font-size: 16px;
            width: 16px;
            height: 16px;
            color: #45546e;
            cursor: pointer;
          }

          .prompt-title-text {
            font-family: var(--kebsFontFamily);
            font-size: 16px;
            font-weight: 600;
            color: #ee4961;
          }
        }

        .search-bar {
          display: flex;
          align-items: center;
          gap: 4px;
          border: 1px solid #b9c0ca;
          border-radius: 60px;
          width: 40%;
          padding: 2px 12px;
          height: 28px;

          input {
            height: 100%;
            width: 100%;
            font-family: var(--kebsFontFamily);
            font-size: 12px;
            font-weight: 400;
            color: #45546e;
            outline: none;
            border: none;
          }

          input::placeholder {
            font-family: var(--kebsFontFamily);
            font-size: 12px;
            font-weight: 400;
            color: #b9c0ca;
          }
        }
      }

      .content {
        display: flex;
        align-items: center;
        width: 100%;
        gap: 2%;

        .categories {
          display: flex;
          flex-direction: column;
          height: calc(var(--kebsChatbotContentHeight) - 20px);
          border: 0.5px solid #e8e9ee;
          border-radius: 8px;
          width: 35%;
          padding: 8px;
          gap: 8px;

          .header {
            display: flex;
            justify-content: start;
            align-items: center;
            gap: 8px;
            padding-bottom: 8px;
            border-bottom: 0.5px solid #dadce2;
            margin-bottom: 8px;

            .categories-text {
              font-family: var(--kebsFontFamily);
              font-size: 12px;
              font-weight: 400;
              color: #111434;
              text-overflow: ellipsis;
              white-space: nowrap;
              overflow: hidden;
              max-width: 70%;
            }

            .count {
              background-color: #45546e;
              padding: 2px 6px;
              border-radius: 10px;
              font-family: var(--kebsFontFamily);
              font-size: 10px;
              font-weight: 500;
              color: #fff;
            }
          }

          .categories-list {
            display: flex;
            flex-direction: column;
            gap: 8px;
            height: calc(var(--kebsChatbotContentHeight) - 68px);
            overflow-y: auto;
            padding-right: 2%;

            .category-list {
              display: flex;
              align-items: center;
              gap: 8px;
              cursor: pointer;
              border-radius: 4px;
              padding: 4px;

              .text {
                font-family: var(--kebsFontFamily);
                font-size: 12px;
                font-weight: 400;
                color: #111434;
                text-overflow: ellipsis;
                white-space: nowrap;
                overflow: hidden;
                max-width: 70%;
              }

              .count {
                padding: 2px 6px;
                border-radius: 10px;
                font-family: var(--kebsFontFamily);
                font-size: 10px;
                font-weight: 500;
                color: linear-gradient(270deg, #ef4a61 0%, #f27a6c 105.29%);
                background: linear-gradient(
                  270deg,
                  rgba(239, 74, 97, 0.1) 0%,
                  rgba(242, 122, 108, 0.1) 105.29%
                );
              }
            }

            .selected-category-list {
              background: linear-gradient(
                270deg,
                rgba(239, 74, 97, 0.1) 0%,
                rgba(242, 122, 108, 0.1) 105.29%
              );

              .selected-count {
                color: #fff;
                background: linear-gradient(
                  270deg,
                  #ef4a61 0%,
                  #f27a6c 105.29%
                );
              }
            }
          }

          .categories-list::-webkit-scrollbar {
            width: 6px !important;
            height: 6px !important;
          }

          .categories-list::-webkit-scrollbar-thumb {
            min-height: 40px !important;
            max-height: 40px !important;
          }
        }

        .category-content {
          display: flex;
          flex-direction: column;
          height: calc(var(--kebsChatbotContentHeight) - 20px);
          gap: 6px;
          width: 63%;
          padding-right: 1%;
          overflow-y: auto;

          .single-prompt {
            display: flex;
            flex-direction: row;
            align-items: start;
            justify-content: space-between;
            gap: 16px;
            border: 0.5px solid #dadce2;
            border-radius: 4px;
            padding: 8px;
            cursor: pointer;

            .text {
              font-family: var(--kebsFontFamily);
              font-size: 10px;
              font-weight: 400;
              color: #8b95a5;
              text-align: justify;
              line-height: 1.5;
            }

            .copy {
              height: 10px;
              display: none;
              align-items: center;
              justify-content: end;
              gap: 4px;
              font-family: var(--kebsFontFamily);
              font-size: 8px;
              font-weight: 500;
              color: #ef4a61;
            }
          }

          .single-prompt:hover {
            border: 0.5px solid #ef4a61;
            background: linear-gradient(
              270deg,
              rgba(239, 74, 97, 0.05) 0%,
              rgba(242, 122, 108, 0.05) 105.29%
            );
          }

          .single-prompt:hover .copy {
            display: flex;
          }
        }

        .category-content::-webkit-scrollbar {
          width: 6px !important;
          height: 6px !important;
        }

        .category-content::-webkit-scrollbar-thumb {
          min-height: 40px !important;
          max-height: 40px !important;
        }

        .category-content-empty-state {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: calc(var(--kebsChatbotContentHeight) - 20px);
          gap: 6px;
          width: 63%;
          padding-right: 1%;
          overflow: hidden;
          font-family: var(--kebsFontFamily);
          font-size: 14px;
          font-weight: 700;
          color: #45546e;
        }
      }
    }

    .bg-image-1 {
      position: absolute;
      top: 0px;
      right: 0px;
      height: 170px;
      width: 60px;
    }

    .bg-image-2 {
      position: absolute;
      bottom: 0px;
      left: 0px;
      height: 170px;
      width: 60px;
      transform: rotate(180deg);
    }
  }

  .chatbot-container-footer {
    display: flex;
    flex-direction: column;
    padding: 0px 24px 0px 24px;

    .imp-text {
      font-family: var(--kebsFontFamily);
      font-size: 10px;
      font-weight: 500;
      color: #b9c0ca;
      text-align: center;
    }

    .align-items-with-gap {
      display: flex;
      align-items: center;
      gap: 8px;

      .svg-icon {
        cursor: pointer;
      }
    }

    .module-btn {
      padding: 4px 12px;
      background-color: #ffebec;
      border: 1px solid #ee4961;
      border-radius: 16px;
      color: #ee4961;
      font-family: var(--kebsFontFamily);
      font-size: 14px;
      font-weight: 400;
      width: fit-content;
      cursor: pointer;
    }

    .module-btn-close {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 4px 12px;
      background-color: #ee4961;
      border: 1px solid #ee4961;
      border-radius: 16px;
      color: white;
      font-family: var(--kebsFontFamily);
      font-size: 14px;
      font-weight: 400;
      width: fit-content;
      cursor: pointer;
    }
    .search-overlay-section {
      display: inline-block;
      position: relative;
      width: 100%;
      .overlay {
        color: #515965;
        align-items: center;
        background: #ffffff;
        max-height: 181px;
        border-radius: 8px;
        box-shadow: 0px 2px 1px 0px #0000001f;
        border: 1px solid #e8e9ee;
        z-index: 5;
        overflow: hidden;
        padding-top: 20px;
        position: absolute;
        bottom: 20px;
        left: 0;
        width: 100%;
        background: #ffffff;
        border-radius: 8px;
        box-shadow: 0px 2px 1px 0px #0000001f;
        border: 1px solid #e8e9ee;
        z-index: 5;
        min-height: 62px;
        overflow-y: auto;
        display: flex;
        position: absolute;
        pointer-events: all !important;

        .prompt-content {
          overflow-y: auto;
          display: flex;
          flex-direction: column;
          padding: 0px 2px 24px 8px;
          margin-right: 2px;
          gap: 4px;
          max-height: 122px;
          width: 100%;
          position: relative;

          .prompt-text {
            font-family: var(--kebsFontFamily);
            font-weight: 400;
            font-size: 12px;
            padding: 6px 6px;
            border-radius: 3px;
            cursor: pointer;
            width: 100%;
          }
          .prompt-text:hover {
            background: #ffecee;
          }
        }
      }
      .search-ui {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 8px;
        border: 1px solid #b9c0ca;
        border-radius: 60px;
        height: 36px;
        width: 100%;
        padding-right: 4px;
        z-index: 10;
        position: relative;
        background: #ffff;

        .search-bar {
          width: -webkit-fill-available;
          height: 100%;
          padding: 4px 8px;

          input {
            height: 100%;
            width: 100%;
            font-family: var(--kebsFontFamily);
            font-size: 12px;
            font-weight: 400;
            color: #45546e;
            outline: none;
            border: none;
          }

          input::placeholder {
            font-family: var(--kebsFontFamily);
            font-size: 12px;
            font-weight: 400;
            color: #b9c0ca;
          }
        }
      }
      /* Target scrollbar inside overlay */
      .overlay::-webkit-scrollbar,
      .prompt-content::-webkit-scrollbar {
        width: 6px !important;
        height: 6px !important;
      }

      /* Scrollbar track */
      .overlay::-webkit-scrollbar-track,
      .prompt-content::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
        height: 68px !important;
      }

      /* Scrollbar thumb */
      .overlay::-webkit-scrollbar-thumb,
      .prompt-content::-webkit-scrollbar-thumb {
        background: #b9c0ca;
        border-radius: 3px;
        min-height: 50px;
      }

      /* Scrollbar thumb on hover */
      .overlay::-webkit-scrollbar-thumb:hover,
      .prompt-content::-webkit-scrollbar-thumb:hover {
        background: #999;
      }
    }
  }

  .chat-ui-header-section{
    .header{
      display: flex;
      gap: 2px;
      font-family: var(--kebsFontFamily);
      font-weight: 700;
      font-size: 16px;
      .text-color{
        background: linear-gradient(270deg, #FFC12F -5.06%, #F16567 31.08%, #F16567 100.33%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
      }

      &.header-content{
        font-weight: 700;
        font-size: 20px;
        height: 30px;
      }
    }
    .sub-header{
      color: #8B95A5;
      font-family: var(--kebsFontFamily);
      font-weight: 400;
      font-size: 14px;

      &.header-content{
        font-weight: 400;
        font-size: 16px;
        height: 30px;
      }
    }
  }
}

.menu-content {
  display: flex;
  flex-direction: column;
  padding: 0px 12px;
  gap: 4px;

  .menu-item {
    font-family: var(--kebsFontFamily);
    font-size: 10px;
    font-weight: 400;
    color: #515965;
    height: 20px;
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 0px;
  }

  .menu-icon {
    font-size: 14px;
    width: 14px;
    height: 14px;
    color: #a8acb2;
    margin: 0px;
  }

  .menu-divider {
    color: #e8e9ee;
  }
}

.modules-overlay {
  display: flex;
  flex-direction: column;
  border-radius: 4px;
  border: 1px solid #e8e9ee;
  min-width: 130px;
  background: white;

  .search-ui {
    background-color: #f7f9fb;
    height: 28px;

    input {
      height: 100%;
      width: 100%;
      font-family: var(--kebsFontFamily);
      font-size: 12px;
      font-weight: 400;
      color: #45546e;
      outline: none;
      border: none;
      background-color: #f7f9fb;
      padding: 0px 8px;
    }

    input::placeholder {
      font-family: var(--kebsFontFamily);
      font-size: 12px;
      font-weight: 400;
      color: #d4d6d8;
    }
  }

  .modules {
    display: flex;
    flex-direction: column;
    gap: 6px;
    padding-bottom: 6px;

    .single-module {
      font-family: var(--kebsFontFamily);
      font-size: 12px;
      font-weight: 400;
      color: #45546e;
      cursor: pointer;
      padding: 0px 8px;
    }

    .selected-module {
      color: #ee4961;
    }

    .divider {
      color: #e8e9ee;
    }
  }
}
