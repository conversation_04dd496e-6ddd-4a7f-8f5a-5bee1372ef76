import {
  Compo<PERSON>,
  ElementRef,
  <PERSON>L<PERSON><PERSON>,
  Inject,
  OnInit,
  TemplateRef,
  ViewChild,
  ViewContainerRef,
} from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';

import { Overlay, OverlayRef } from '@angular/cdk/overlay';
import { TemplatePortal } from '@angular/cdk/portal';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MAT_MENU_SCROLL_STRATEGY } from '@angular/material/menu';

import * as moment from 'moment';
import * as _ from 'underscore';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { FilterPromptLibraryPipe } from '../pipes/filter-prompt-library/filter-prompt-library.pipe';

import { AiServiceService } from 'src/app/modules/main/services/ai-service/ai-service.service';
import { StringSimilarityService } from 'src/app/modules/main/services/string-similarity/string-similarity.service';
import { LoginService } from 'src/app/services/login/login.service';
import { ToasterService } from 'src/app/modules/applicant-tracking-system/shared-components/ats-custom-toast/toaster.service';

@Component({
  selector: 'app-chatbot-dialog',
  templateUrl: './chatbot-dialog.component.html',
  styleUrls: ['./chatbot-dialog.component.scss'],
  providers: [
    {
      provide: MAT_MENU_SCROLL_STRATEGY,
      deps: [Overlay],
      useFactory: (overlay: Overlay) => () =>
        overlay.scrollStrategies.reposition(),
    },
    FilterPromptLibraryPipe,
  ],
})
export class ChatbotDialogComponent implements OnInit {
  protected _onDestroy = new Subject<void>();

  overlayRef: OverlayRef;
  @ViewChild('chatScrollContainer') private chatScrollContainer!: ElementRef;
  @ViewChild('triggerModulesOverlayChange', { static: false })
  private triggerModulesOverlayChange!: TemplateRef<HTMLElement>;
  triggerModulesOverlay: ElementRef;
  triggerModulesOverlayField: ElementRef;

  @ViewChild('cardWrapper', { static: false }) cardWrapper!: ElementRef;
  canScrollLeft: boolean = false;
  canScrollRight: boolean = false;

  profile: any = {};

  promptSearchParams: string = '';
  historySearchParams: string = '';
  moduleSearchParams: string = '';
  promptLibrarySearchParams: string = '';

  isHistoryVisible: boolean = false;
  isChatVisible: boolean = false;
  isHomeScreenVisible: boolean = true;
  isPromptLibraryVisible: boolean = false;
  isReportsVisible: boolean = false;
  isWidgetsVisible: boolean = false;

  isHistoryLoading: boolean = false;
  isThreadLoading: boolean = false;
  isLoading: boolean = true;
  isPromptApiInProgress: boolean = false;
  isAgentVisible: boolean=false;

  suggestedPrompts = [];
  modules = [];
  currentThreadData = [];

  historySkip: number = 0;
  historyLimit: number = 25;
  historyData = [];
  pinnedHistoryData = [];
  historyGroupedData = {};
  historyGroupedDateData = [];

  currentThreadLoadTextIndex = 0;
  threadTexts = [];
  isSliding: boolean = false;
  repeatArray = [];

  currentSelectedModuleId: number = null;
  currentSelectedModuleName: string = null;
  selectedHistoryThreadData: any;
  selectedHistoryDateIndex: number = null;
  selectedHistoryThreadIndex: number = null;

  promptLibraryData = [];
  currentSelectedPromptLibraryCategoryIndex: number = null;
  currentSelectedPromptLibraryCategory: string = null;

  dialogExpanded: boolean = false;
  historySelectedThreadId: string = null;

  isTyping: boolean = false;
  searchDefaultPrompts: Array<Object> = []
  searchPromptSkip: number = 0;
  searchLazyLoadedList: any = [];
  startIndex: number = 0;
  skip:number = 25;
  filteredPrompts: any = [];

  isCustomPrompt: boolean = false;
  customizePromptData: any = null;
  responsePromptData: any = null;
  customResponseId: any = '';
  responseCustomizeData: any = null;
  
  constructor(
    @Inject(MAT_DIALOG_DATA)
    public data: {
      aiThemeConfig: any;
    },
    private _dialogRef: MatDialogRef<ChatbotDialogComponent>,
    private _aiService: AiServiceService,
    private _loginService: LoginService,
    private _toaster: ToasterService,
    private _viewContainerRef: ViewContainerRef,
    private _overlay: Overlay,
    private _stringSimilarity: StringSimilarityService,
    private sanitizer: DomSanitizer,
    private filter: FilterPromptLibraryPipe,
    private elementRef: ElementRef
  ) {}

  async ngOnInit() {
    this.profile = this._loginService.getProfile().profile;
    this.threadTexts = this.data?.aiThemeConfig['THREAD-LOADER-TEXT'];
    this.onDialogResizing(true);
    setInterval(() => {
      this.currentThreadLoadTextIndex =
        (this.currentThreadLoadTextIndex + 1) % this.threadTexts.length;
      this.isSliding = true; // Start slide animation

      setTimeout(() => {
        // After the animation completes, update the text
        this.isSliding = false;
      }, 500); // Animation duration must match CSS
    }, 2000); // Change text every 2 seconds
    this.calculateDynamicContentHeight();

    Promise.all([
      this.defaultPrompts(),
      this.getPromptLibraryData(),
      this.getChatbotModulesBasedOnRoleAccess(),
    ])
      .then(async (res) => {
        await this.getDefaultPromptsBySearch();
        await this.onPromptDataScroll(true)
        this.isLoading = false;
      })
      .catch((err) => {
        this.isLoading = false;
        this.onDialogClose();
      });
  }

  ngAfterViewInit() {
    const observer = new MutationObserver(() => {
      if (this.cardWrapper?.nativeElement) {
        this.checkScroll();
        observer.disconnect(); // Stop observing once it's found
      }
    });
  
    observer.observe(document.body, { childList: true, subtree: true });
  }

  @HostListener('window:resize')
  onResize() {
    this.calculateDynamicContentHeight();
    this.checkScroll();
  }

  /**
   * @description Calculates dynamic height based on screen size
   */
  calculateDynamicContentHeight() {
    let adjustedHeight = 41;
    if(this.dialogExpanded){
      adjustedHeight = 0;
    }
    document.documentElement.style.setProperty(
      '--kebsChatbotContentHeight',
      window.innerHeight - 220 - adjustedHeight + 'px'
    );
    document.documentElement.style.setProperty(
      '--kebsChatbotHistoryContentHeight',
      window.innerHeight - 170 - adjustedHeight + 'px'
    );

    let historyLoadMaxHeight = window.innerHeight - 170 - adjustedHeight;
    let finalCount = Math.floor(historyLoadMaxHeight / 34);
    this.repeatArray = Array(finalCount);
  }

  /**
   * @description On close dialog
   */
  onDialogClose() {
    this._dialogRef.close();
  }

  /**
   * @description Scroll to bottom of the chat when opened
   */
  scrollToBottom() {
    const div = this.chatScrollContainer.nativeElement;
    div.scrollTop = div.scrollHeight;
  }

  /**
   * @description Group all history data based on date
   */
  groupHistoryData() {
    const today = moment().startOf('day');
    const yesterday = moment().subtract(1, 'days').startOf('day');
    const sevenDaysAgo = moment().subtract(7, 'days').startOf('day');
    const thirtyDaysAgo = moment().subtract(30, 'days').startOf('day');

    const groupedData = {};

    this.historyData.forEach((item) => {
      const itemDate = moment(item.created_at).startOf('day');

      if (itemDate.isSame(today, 'day')) {
        if (!groupedData['Today']) {
          groupedData['Today'] = [];
        }
        groupedData['Today'].push(item);
      } else if (itemDate.isSame(yesterday, 'day')) {
        if (!groupedData['Yesterday']) {
          groupedData['Yesterday'] = [];
        }
        groupedData['Yesterday'].push(item);
      } else if (itemDate.isAfter(sevenDaysAgo)) {
        if (!groupedData['Previous 7 Days']) {
          groupedData['Previous 7 Days'] = [];
        }
        groupedData['Previous 7 Days'].push(item);
      } else if (itemDate.isAfter(thirtyDaysAgo)) {
        if (!groupedData['Previous 30 Days']) {
          groupedData['Previous 30 Days'] = [];
        }
        groupedData['Previous 30 Days'].push(item);
      } else {
        // For dates older than 30 days, group by month name (e.g., "September 2024")
        const monthYear = itemDate.format('MMMM YYYY');
        if (!groupedData[monthYear]) {
          groupedData[monthYear] = [];
        }
        groupedData[monthYear].push(item);
      }
    });

    // Store the grouped data in your component state
    this.historyGroupedData = groupedData;

    // For date ordering (Today, Yesterday, etc.), we capture the keys in reverse order
    this.historyGroupedDateData = Object.keys(groupedData);
  }

  /**
   * @description Open history UI
   */
  openHistory() {
    this.isHistoryVisible = true;
    this.historyData = [];
    this.allHistory(true);
  }

  /**
   * @description Close history UI
   */
  closeHistory() {
    this.isHistoryVisible = false;
  }

  /**
   * @description On history data scroll
   */
  onHistoryDataScroll() {
    this.historySkip += 25;
    this.allHistory(false);
  }

  /**
   * @description Open Prompt Library UI
   */
  openPromptLibrary() {
    this.promptLibrarySearchParams = '';
    this.resetUiState(['isPromptLibraryVisible']);
    if (this.promptLibraryData && this.promptLibraryData.length > 0) {
      this.setPromptLibraryIndex(0);
      this.setPromptLibraryCategory(this.promptLibraryData[0]['name']);
    }
  }

  /**
   * @description Set Prompt Library ID
   * @param {int} index
   */
  setPromptLibraryIndex(index) {
    this.currentSelectedPromptLibraryCategoryIndex = index;
  }

  /**
   * @description Set Prompt Library Categort
   * @param {string} name
   */
  setPromptLibraryCategory(name) {
    this.currentSelectedPromptLibraryCategory = name;
  }

  /**
   * @description Find Prompt Library ID
   * @param {array} data
   * @param {int} id
   */
  findPromptLibraryIndex(data, id) {
    let index = data.findIndex((obj) => obj.id == id);
    let name = data.find((obj) => obj.id == id)?.name;
    if (index != -1) {
      this.setPromptLibraryIndex(index);
      this.setPromptLibraryCategory(name);
    }
  }

  /**
   * @description On search params for prompt library changes
   */
  onSearchParamsChange() {
    const filteredData = this.filter.transform(
      this.promptLibraryData,
      this.promptLibrarySearchParams
    );
    if (filteredData.length > 0) {
      let selected_name = filteredData.find((obj) => obj.name == this.currentSelectedPromptLibraryCategory)?.name || null;
      if(!selected_name){
        this.findPromptLibraryIndex(filteredData, filteredData[0]['id']);
      }
    }
    else {
      this.currentSelectedPromptLibraryCategoryIndex = 0;
      this.currentSelectedPromptLibraryCategory = null;
    }
  }

  /**
   * @description Copy prompt to clipboard
   * @param prompt
   */
  async copyPromptToClipboard(prompt) {
    prompt.isCopyInProgress = true;
    this.promptSearchParams = prompt?.prompt
    // Reset isCopying back to false after 1 second
    setTimeout(async () => {
      prompt.isCopyInProgress = false;
      await this.onEnterSearchPrompt(prompt?._id,prompt?.prompt,false,null,null,null);
    }, 1000);
    
  }

  /**
   * @description Highlight prompt text
   * @param prompt
   */
  highlightPromptKey(prompt) {
    if (!prompt) {
      return '-';
    }

    const bracesRegex = /[{[]([^}\]]+)[}\]]/g;
    const highlightedPrompt = prompt.replace(
      bracesRegex,
      (match, content) => `<span style="color: #ef4a61;">[${content}]</span>`
    );
    return this.sanitizer.bypassSecurityTrustHtml(highlightedPrompt);
  }

  /**
   * @description Find matching prompt
   * @param inputSentence
   * @param promptLibraryData
   * @param threshold
   */
  async findMatchingPrompt(
    inputSentence: string,
    promptLibraryData: any[],
    threshold: number = 0.9
  ) {
    let bestMatch = { libraryIndex: -1, promptIndex: -1, similarity: 0 };

    promptLibraryData.forEach((library, libraryIndex) => {
      library.prompts.forEach((promptObj: any, promptIndex: number) => {
        const similarityScore = this._stringSimilarity.similarity(
          inputSentence,
          promptObj.prompt
        );

        if (similarityScore > bestMatch.similarity) {
          bestMatch = {
            libraryIndex,
            promptIndex,
            similarity: similarityScore,
          };
        }
      });
    });

    // Check if the best match meets the threshold
    if (bestMatch.similarity >= threshold) {
      return bestMatch;
    } else {
      return null; // No match found
    }
  }

  /**
   * @description Set selected module
   */
  setSelectedModule(id, name) {
    this.currentSelectedModuleId = id;
    this.currentSelectedModuleName = name;
    this.defaultPrompts();
  }

  /**
   * @description Open New Chat
   */
  openNewChat() {
    this.resetUiState(['isHomeScreenVisible']);
    this.currentThreadData = [];
    this.defaultPrompts();
    this.resetThreadId();
  }

  /**
   * @description Regenerate response
   * @param {object} data
   */
  async regenerateResponse(data) {
    if(data?.regenerate){
      this.onEnterSearchPrompt(
        null,
        data?.data?.prompt,
        true,
        data?.data?._id,
        data?.data?.response_content,
        data.index
      );
    }
    else{
      this.isCustomPrompt = true;
      this.responseCustomizeData = data?.data
      if(data?.mode == 'E'){
        this.customizePromptData = data?.data?.currentChat?.filter_config?.config
        await this.onEnterSearchPrompt(
          null,
          data?.data?.currentChat?.prompt,
          true,
          data?.data?.currentChat?._id,
          data?.data?.currentChat?.response_content,
          data.index
        );
      }
      else{
        
        await this.onEnterSearchPrompt(
          this.customResponseId,
          this.responsePromptData?.prompt,
          false,
          null,
          null,
          data.index
        );
      }
      this.responseCustomizeData = null;
      this.isCustomPrompt = false;
    }
  }

  /**
   * @description Add new node for regeneration
   * @param {int} index
   * @param {string} targetId
   * @param {object} newNode
   */
  addNewNodeForRegeneration(index: number, targetId: string, newNode: any) {
    const obj = this.currentThreadData[index];
    this.findAndInsertNewNode(obj, targetId, newNode);
  }

  /**
   * @description Find and insert new node for regeneration
   * @param {object} node
   * @param {string} targetId
   * @param {object} newNode
   */
  findAndInsertNewNode(node: any, targetId: string, newNode: any): boolean {
    // Check if the current node's _id matches the target _id
    if (node._id === targetId) {
      if (!node.children) {
        node.children = []; // Create children array if it doesn't exist
      }
      node.children.push(newNode); // Append the new node at the end of children array
      return true;
    }

    // If the node has children, search recursively
    if (node.children && Array.isArray(node.children)) {
      for (let i = 0; i < node.children.length; i++) {
        const child = node.children[i];
        if (child._id === targetId) {
          node.children.splice(i + 1, 0, newNode); // Insert after the found index
          return true;
        }

        // Recursive search within the child nodes
        const found = this.findAndInsertNewNode(child, targetId, newNode);
        if (found) return true;
      }
    }

    // If _id is not found in this branch, return false
    return false;
  }

  /**
   * @description On search history
   */
  onEnterSearchHistory() {
    this.allHistory(true);
  }

  /**
   * @description Reset to new chat based on current thread
   * @param {array} ids
   */
  resetToNewChatBasedOnCurrentThread(ids) {
    if (!this.isHomeScreenVisible) {
      if (this.currentThreadData && this.currentThreadData.length > 0) {
        let currentThreadId = this.currentThreadData[0]['thread_id'];
        if (ids.includes(currentThreadId)) {
          this.openNewChat();
        }
      }
    }
  }

  /**
   * @description Rename Thread
   */
  renameThread() {
    let id = this.selectedHistoryThreadData?._id;
    let index = this.historyData.findIndex((obj) => obj._id == id);
    let pinnedIndex = this.pinnedHistoryData.findIndex((obj) => obj._id == id);
    if (index != -1) {
      this.historyData[index]['isRenameInProgress'] = true;
      this.groupHistoryData();
    } else if (pinnedIndex != -1) {
      this.pinnedHistoryData[pinnedIndex]['isRenameInProgress'] = true;
      this.groupHistoryData();
    }

    setTimeout((obj) => {
      if (index != -1) {
        const inputElement = document.getElementById(
          '#historyInputSearch-' +
            this.selectedHistoryDateIndex +
            '-' +
            this.selectedHistoryThreadIndex
        ) as HTMLInputElement;
        inputElement.focus();
        inputElement.select();
      } else if (pinnedIndex != -1) {
        const inputElement = document.getElementById(
          '#pinnedHistoryInputSearch-' + this.selectedHistoryThreadIndex
        ) as HTMLInputElement;
        inputElement.focus();
        inputElement.select();
      }
    }, 50);
  }

  /**
   * @description Set selected history Thread
   * @param {ObjectId} log
   * @param {int} dateIndex
   * @param {int} threadIndex
   */
  setSelectedHistoryThreadData(log, dateIndex, threadIndex) {
    this.selectedHistoryThreadData = log;
    this.selectedHistoryDateIndex = dateIndex;
    this.selectedHistoryThreadIndex = threadIndex;
  }

  /**
   * @description Reset UI
   * @param {array} value
   */
  async resetUiState(value) {
    let states = [
      'isChatVisible',
      'isHomeScreenVisible',
      'isPromptLibraryVisible',
    ];
    for (let i = 0; i < states.length; i++) {
      this[states[i]] = false;
    }

    for (let i = 0; i < value.length; i++) {
      this[value[i]] = true;
    }

    if (value == 'isHomeScreenVisible') {
      this.isReportsVisible = false;
      this.isWidgetsVisible = false;
      this.isAgentVisible = false;
    }
    await this.getDefaultPromptsBySearch();
    await this.onPromptDataScroll(true);
  }

  /**
   * @description Reset thread id
   */
  resetThreadId() {
    this.historySelectedThreadId = null;
  }

  /**
   * @description To open overlay
   * @param {id} triggerField
   * @param {template} template
   */
  openModulesOverlay(triggerField, template) {
    this.moduleSearchParams = '';
    if (!this.overlayRef?.hasAttached()) {
      const positionStrategyBuilder = this._overlay.position();

      const positionStrategy = positionStrategyBuilder
        .flexibleConnectedTo(triggerField)
        .withFlexibleDimensions(true)
        .withPush(true)
        .withViewportMargin(0)
        .withGrowAfterOpen(true)
        .withPositions([
          {
            originX: 'start',
            originY: 'bottom',
            overlayX: 'start',
            overlayY: 'top',
          },
        ]);
      positionStrategy.withDefaultOffsetY(-26);
      const scrollStrategy = this._overlay.scrollStrategies.close();

      this.overlayRef = this._overlay.create({
        positionStrategy,
        scrollStrategy,
        hasBackdrop: true,
        backdropClass: '',
        panelClass: ['pop-up'],
      });

      const templatePortal = new TemplatePortal(
        template,
        this._viewContainerRef
      );

      this.overlayRef.attach(templatePortal);

      this.overlayRef.backdropClick().subscribe(() => {
        this.closeOverlay();
      });
    }

    setTimeout((obj) => {
      const inputElement = document.getElementById(
        '#moduleInputSearch'
      ) as HTMLInputElement;
      inputElement.focus();
      inputElement.select();
    }, 50);
  }

  /**
   * @description Load prompts
   * @param id
   * @param name
   */
  onClickModule(id, name) {
    if (id == this.currentSelectedModuleId) {
      return;
    }
    this.setSelectedModule(id, name);
    this.closeOverlay();
  }

  /**
   * To close the Overlay
   */
  closeOverlay() {
    this.overlayRef?.dispose();
  }

  /**
   * @description Rename thread API
   * @param {string} id
   * @param {string} currentValue
   */
  onRenameThread(id, currentValue) {
    let payload = {
      aid: this.profile.aid,
      thread_id: id,
      short_description: currentValue,
    };

    return new Promise((resolve, reject) => {
      this._aiService
        .changeDescriptionOfThread(payload)
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res: any) => {
            if (res['err'] == false) {
              let index = this.historyData.findIndex((obj) => obj?._id == id);
              if (index != -1) {
                this.historyData[index]['isRenameInProgress'] = false;
                this.groupHistoryData();
              }
              let pinnedIndex = this.pinnedHistoryData.findIndex(
                (obj) => obj?._id == id
              );
              if (pinnedIndex != -1) {
                this.pinnedHistoryData[pinnedIndex]['isRenameInProgress'] =
                  false;
                this.groupHistoryData();
              }
            } else {
              this._toaster.showError('Error', res['msg'], 7000);
            }
            resolve(true);
          },
          error: (err) => {
            this._toaster.showError('Error', 'Error in Renaming Thread', 7000);
            reject();
          },
        });
    });
  }

  /**
   * @description Archive or Unarchive Thread
   * @param {boolean} isArchive
   */
  archiveOrUnarchiveThread(isArchive) {
    let payload = {
      aid: this.profile.aid,
      thread_id: [this.selectedHistoryThreadData?._id],
      archive: isArchive,
    };

    return new Promise((resolve, reject) => {
      this._aiService
        .archiveOrUnarchiveThread(payload)
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res: any) => {
            if (res['err'] == false) {
              let index = this.historyData.findIndex(
                (obj) => obj?._id == payload?.thread_id
              );
              if (index != -1) {
                this.historyData.splice(index, 1);
                this.groupHistoryData();
              }

              let pinnedHistoryIndex = this.pinnedHistoryData.findIndex(
                (obj) => obj?._id == payload?.thread_id
              );
              if (pinnedHistoryIndex != -1) {
                this.pinnedHistoryData.splice(pinnedHistoryIndex, 1);
              }

              this.resetToNewChatBasedOnCurrentThread(payload?.thread_id);
            } else {
              this._toaster.showError('Error', res['msg'], 7000);
            }
            resolve(true);
          },
          error: (err) => {
            this._toaster.showError('Error', 'Error in Archiving Thread', 7000);
            reject();
          },
        });
    });
  }

  /**
   * @description Pin or Unpin Thread
   */
  pinOrUnpinThread() {
    if (
      this.pinnedHistoryData.length == 15 &&
      !this.selectedHistoryThreadData?.pinned
    ) {
      return this._toaster.showInfo(
        'Info 📝',
        'You can pin up to 15 threads!',
        7000
      );
    }

    let payload = {
      aid: this.profile.aid,
      thread_id: [this.selectedHistoryThreadData?._id],
      pinned: this.selectedHistoryThreadData?.pinned ? false : true,
    };

    return new Promise((resolve, reject) => {
      this._aiService
        .pinOrUnpinThread(payload)
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res: any) => {
            if (res['err'] == false) {
              if (payload?.pinned) {
                let index = this.historyData.findIndex(
                  (obj) => obj?._id == payload?.thread_id
                );
                if (index != -1) {
                  let removedItem = this.historyData
                    .splice(index, 1)
                    .map((item) => ({
                      ...item,
                      pinned: true,
                    }));
                  this.pinnedHistoryData = [
                    ...this.pinnedHistoryData,
                    ...removedItem,
                  ];
                  this.groupHistoryData();
                }
              } else {
                this.allHistory(true);
              }
            } else {
              this._toaster.showError('Error', res['msg'], 7000);
            }
            resolve(true);
          },
          error: (err) => {
            this._toaster.showError('Error', 'Error in Pin/Unpin Thread', 7000);
            reject();
          },
        });
    });
  }

  /**
   * @description Delete Thread
   */
  deleteThread() {
    let payload = {
      aid: this.profile.aid,
      thread_id: this.selectedHistoryThreadData?._id,
    };

    return new Promise((resolve, reject) => {
      this._aiService
        .deleteThread(payload)
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res: any) => {
            if (res['err'] == false) {
              let index = this.historyData.findIndex(
                (obj) => obj?._id == payload?.thread_id
              );
              if (index != -1) {
                this.historyData.splice(index, 1);
                this.groupHistoryData();
              }

              let pinnedHistoryIndex = this.pinnedHistoryData.findIndex(
                (obj) => obj?._id == payload?.thread_id
              );
              if (pinnedHistoryIndex != -1) {
                this.pinnedHistoryData.splice(pinnedHistoryIndex, 1);
              }

              this.resetToNewChatBasedOnCurrentThread([
                this.selectedHistoryThreadData?._id,
              ]);
            } else {
              this._toaster.showError('Error', res['msg'], 7000);
            }
            resolve(true);
          },
          error: (err) => {
            this._toaster.showError('Error', 'Error in Deleting Thread', 7000);
            reject();
          },
        });
    });
  }

  /**
   * @description Set selected history Thread
   * @param {ObjedId} threadId
   */
  setSelectedHistoryThread(threadId) {
    this.historySelectedThreadId = threadId;
    if (this.isThreadLoading) {
      return;
    }
    this.isThreadLoading = true;

    let payload = {
      aid: this.profile.aid,
      oid: this.profile.oid,
      thread_id: threadId,
    };

    this.currentThreadData = [];

    this.isReportsVisible = false;
    this.isWidgetsVisible = false;

    return new Promise((resolve, reject) => {
      this._aiService
        .retrieveThreadHistory(payload)
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res: any) => {
            if (res['err'] == false) {
              this.currentThreadData = res['data'];
              this.resetUiState(['isChatVisible']);
              setTimeout(() => {
                this.scrollToBottom();
              }, 50);
            } else {
              this.resetUiState(['isHomeScreenVisible']);
              this._toaster.showWarning('Warning ⚠️', res['msg'], 7000);
            }
            this.isThreadLoading = false;
            resolve(true);
          },
          error: (err) => {
            this._toaster.showError(
              'Error',
              'Error in Fetching Session Data',
              7000
            );
            this.resetUiState(['isHomeScreenVisible']);
            this.isThreadLoading = false;
            reject();
          },
        });
    });
  }

  /**
   * @description Get default prompts
   */
  async defaultPrompts() {
    let payload = {
      aid: this.profile.aid,
      oid: this.profile.oid,
      module_id: this.currentSelectedModuleId,
      accessible_module_ids: _.pluck(this.modules, 'id'),
    };
    this.suggestedPrompts = [];

    return new Promise((resolve, reject) => {
      this._aiService
        .defaultPrompts(payload)
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res: any) => {
            if (res['err'] == false) {
              this.suggestedPrompts = res['data'];
            }
            resolve(true);
          },
          error: (err) => {
            this._toaster.showError(
              'Error',
              'Error in Fetching Default Prompts',
              7000
            );
            this.isLoading = false;
            reject();
          },
        });
    });
  }

  /**
   * @description Get default prompts
   */
  async getChatbotModulesBasedOnRoleAccess() {
    let payload = {
      aid: this.profile.aid,
      oid: this.profile.oid,
    };
    this.modules = [];

    return new Promise((resolve, reject) => {
      this._aiService
        .getChatbotModulesBasedOnRoleAccess(payload)
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res: any) => {
            if (res['err'] == false) {
              this.modules = res['data'];
              if (this.modules && this.modules.length > 0) {
                this.setSelectedModule(
                  this.modules[0]['id'],
                  this.modules[0]['name']
                );
              }
            }
            resolve(true);
          },
          error: (err) => {
            this._toaster.showError('Error', 'Error in Fetching Modules', 7000);
            this.isLoading = false;
            reject();
          },
        });
    });
  }

  /**
   * @description Get default prompts
   * @param {boolean} reset
   */
  async allHistory(reset) {
    if (reset) {
      this.isHistoryLoading = true;
      this.historySkip = 0;
      this.historyData = [];
      this.historyGroupedData = {};
      this.historyGroupedDateData = [];
    }

    let payload = {
      aid: this.profile.aid,
      oid: this.profile.oid,
      skip: this.historySkip,
      limit: this.historyLimit,
      searchParams: this.historySearchParams,
    };

    return new Promise((resolve, reject) => {
      this._aiService
        .allHistory(payload)
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res: any) => {
            if (res['err'] == false) {
              this.historyData = [...this.historyData, ...res['data']];
              if (this.historySkip == 0) {
                this.pinnedHistoryData = res['pinned_data'];
              }
            }
            this.groupHistoryData();
            this.isHistoryLoading = false;
            resolve(true);
          },
          error: (err) => {
            this._toaster.showError(
              'Error',
              'Error in Fetching History Data',
              7000
            );
            this.isHistoryLoading = false;
            reject();
          },
        });
    });
  }

  /**
   * @description On search prompt
   * @param {string} promptId
   * @param {string} prompt
   * @param {boolean} isRegenerated
   * @param {string} originalMessagePromptId
   * @param {string} originalMessagePromptResponse
   * @param {int} regenerationIndex
   */
  async onEnterSearchPrompt(
    promptId,
    prompt,
    isRegenerated,
    originalMessagePromptId,
    originalMessagePromptResponse,
    regenerationIndex,
    customPromptEnable = false
  ) {
    this.isTyping = false;
    if (this.isPromptApiInProgress) {
      return;
    }
    if (prompt && prompt.length > 0 && prompt.trim().length > 0) {
      this.resetUiState(['isChatVisible']);
      let isCustomPrompt = (this.isReportsVisible && customPromptEnable) ? true : false;

      if (!isRegenerated) {
        if(this.isCustomPrompt){
          this.currentThreadData[regenerationIndex]['isLoading'] = true;
        }
        else{
          this.currentThreadData.push({
            prompt: prompt,
            isLoading: isCustomPrompt ? false : true,
          });
          setTimeout(() => {
            this.scrollToBottom();
          }, 50);
        }
      }
      else {
        this.currentThreadData[regenerationIndex]['isLoading'] = true;
      }

      if(isCustomPrompt){
        this.isPromptApiInProgress = false;
        this.promptSearchParams = '';
        this.currentThreadData[this.currentThreadData.length - 1]['isCustomPrompt'] = true;
        this.currentThreadData[this.currentThreadData.length - 1]['isReady'] = false;
        this.currentThreadData[this.currentThreadData.length - 1]['isError'] = false;
        return;
      }

      let module_id = null;

      // Find prompt id if it exists
      let promptIdData = await this.findMatchingPrompt(
        prompt,
        this.promptLibraryData,
        0.9
      );
      if (promptIdData) {
        promptId =
          this.promptLibraryData[promptIdData['libraryIndex']]['prompts'][
            promptIdData['promptIndex']
          ]['id'];
        module_id =
          this.promptLibraryData[promptIdData['libraryIndex']]['id'];
      }
      else {
        module_id = this.currentSelectedModuleId || 2053;
      }

      let payload = {
        aid: this.profile.aid,
        oid: this.profile.oid,
        prompt_id: promptId,
        prompt: prompt,
        module_id: module_id,
        is_regenerated: isRegenerated,
        original_message_id: originalMessagePromptId,
        original_message_response: originalMessagePromptResponse,
        is_new_thread:
          this.currentThreadData.length == 1 &&
          !this.currentThreadData[0]?.thread_id
            ? true
            : false,
        thread_id:
          this.currentThreadData.length == 1 &&
          !this.currentThreadData[0]?.thread_id
            ? null
            : this.currentThreadData[0]?.thread_id,
        s3_link: 1,
        current_date: moment().format('YYYY-MM-DD'),
        is_report_prompt: this.isReportsVisible,
        is_widget_prompt: this.isWidgetsVisible, 
        is_agent_prompt: this.isAgentVisible,
        chat_history: this.currentThreadData,
        is_custom_prompt: this.isCustomPrompt,
        filter_config: {
          data: this.responseCustomizeData?.data,
          config: this.customizePromptData
        }
      };

      this.promptSearchParams = '';
      this.isPromptApiInProgress = true;

      return new Promise((resolve, reject) => {
        this._aiService
          .getPromptResponse(payload)
          .pipe(takeUntil(this._onDestroy))
          .subscribe({
            next: (res: any) => {
              if (res['err'] == false) {
                this.historySelectedThreadId = res['data']['thread_id'];
                if (!isRegenerated) {
                  if (this.isHistoryVisible && payload?.is_new_thread) {
                    this.historySearchParams = '';
                    this.allHistory(true);
                  }
                  this.currentThreadData[this.currentThreadData.length - 1] =
                    res['data'];
                  this.currentThreadData[this.currentThreadData.length - 1][
                    'isReady'
                  ] = true;
                  setTimeout(() => {
                    this.currentThreadData[this.currentThreadData.length - 1][
                      'isReady'
                    ] = false;
                  }, 1500);
                } else {
                  this.addNewNodeForRegeneration(
                    regenerationIndex,
                    originalMessagePromptId,
                    res['data']
                  );
                  this.currentThreadData[regenerationIndex]['isLoading'] =
                    false;
                  this.currentThreadData[regenerationIndex]['isReady'] = true;
                  setTimeout(() => {
                    this.currentThreadData[regenerationIndex]['isReady'] =
                      false;
                  }, 1500);
                }
              } else {
                if (isRegenerated) {
                  this.currentThreadData[regenerationIndex]['isLoading'] =
                    false;
                  this.currentThreadData[regenerationIndex]['isError'] = true;
                  this.currentThreadData[regenerationIndex]['ErrorCode'] = res?.error_code || 'ERR_API_001'
                } else {
                  this.currentThreadData[this.currentThreadData.length - 1][
                    'isLoading'
                  ] = false;
                  this.currentThreadData[this.currentThreadData.length - 1][
                    'isError'
                  ] = true;
                  this.currentThreadData[this.currentThreadData.length - 1]['ErrorCode'] = res?.error_code || 'ERR_API_001'
                }
              }
              this.resetCustomisePrompt();
              setTimeout(() => {
                this.isPromptApiInProgress = false;
              }, 1500);
              resolve(true);
            },
            error: (err) => {
              let error_code = 'ERR_API_001'
              if(err?.status == 504){
                error_code = 'ERR_SYSTEM_TIMEOUT'
              }
              if (isRegenerated) {
                this.currentThreadData[regenerationIndex]['isLoading'] = false;
                this.currentThreadData[regenerationIndex]['isError'] = true;
                this.currentThreadData[regenerationIndex]['ErrorCode'] = error_code
              } else {
                this.currentThreadData[this.currentThreadData.length - 1][
                  'isLoading'
                ] = false;
                this.currentThreadData[this.currentThreadData.length - 1][
                  'isError'
                ] = true;
                this.currentThreadData[this.currentThreadData.length - 1]['ErrorCode'] = error_code
              }
              this.resetCustomisePrompt();
              setTimeout(() => {
                this.isPromptApiInProgress = false;
              }, 1500);
              reject();
            },
          });
      });
    }
  }

  /**
   * @description Get prompt library data
   */
  async getPromptLibraryData() {
    let payload = {
      aid: this.profile.aid,
      oid: this.profile.oid,
    };
    this.promptLibraryData = [];

    return new Promise((resolve, reject) => {
      this._aiService
        .getPromptLibraryData(payload)
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res: any) => {
            if (res['err'] == false) {
              this.promptLibraryData = res['data'];
            }
            resolve(true);
          },
          error: (err) => {
            this._toaster.showError('Error', 'Error in Fetching Modules', 7000);
            this.isLoading = false;
            reject();
          },
        });
    });
  }

  /**
   * @description on Dialog Expansion or Collapse
   */
  onDialogResizing(initial= false){
    if(!initial){
      this.dialogExpanded = !this.dialogExpanded
    }
    this.calculateDynamicContentHeight();
    let width = '40vw'; //Default Width
    let height = '98px'; //Default Height for Adjusting the Chat Bot Height
    if(this.dialogExpanded){
      width = 'calc(-20vw - 84px + 100vw)';
      height = '55px'
    }
    document.documentElement.style.setProperty(
      '--kebsChatBotContentWidth',
      width
    );
    document.documentElement.style.setProperty(
      '--chatBotAdjustedHeight',
      height
    );
    this.checkScroll();
  }

  /**
   * @description Replacing name in Greeting
   * @param value 
   * @param replace_text 
   */
  getGreetingWithName(value,replace_text){
    const text = value || "Hello, ${name}";
    return text.replace("${name}", replace_text);
  }

  getSVGContent(svgValue){
    if(svgValue){
      return this.sanitizer.bypassSecurityTrustHtml(svgValue)
    }
  }

  /**
   * @description Scroll Content
   * @param direction 
   */
  scroll(direction: number) {
    if (this.cardWrapper && this.cardWrapper.nativeElement) {
      const wrapper = this.cardWrapper.nativeElement;
      const scrollAmount = wrapper.offsetWidth / 2; // Scroll by half the visible width
      wrapper.scrollBy({ left: direction * scrollAmount, behavior: 'smooth' });
      this.checkScroll();
    }
  }

  /**
   * @description Widgets Scroll Data
   */
  checkScroll() {
    if (this.cardWrapper && this.cardWrapper.nativeElement) {
      const wrapper = this.cardWrapper.nativeElement;
  
      // Check if scrolling is possible to the left
      this.canScrollLeft = wrapper.scrollLeft > 0;
  
      // Adjust the right scroll condition to avoid rounding errors
      const maxScrollLeft = wrapper.scrollWidth - wrapper.offsetWidth;
      this.canScrollRight = Math.ceil(wrapper.scrollLeft) < Math.floor(maxScrollLeft);
    }
  }

  /**
   * @description To set Feature Value to be visible
   * @param featureValue 
   */
  async setVisibility(featureValue){
    if(featureValue == 'isReportsVisible'){
      this.isReportsVisible = true
    }
    else if(featureValue == 'isWidgetsVisible'){
      this.isWidgetsVisible = true
    }
    else if(featureValue == 'isAgentVisible'){
      this.isAgentVisible = true
    }
    await this.getDefaultPromptsBySearch();
    await this.onPromptDataScroll(true)
  }

  /**
   * @description To Clear the Search Input
   */
  clearSearchInput(event?: MouseEvent) {
    if (!event) return; // Ensure event exists

    const target = event.target as HTMLElement;
    
    if (!target || target.closest('.overlay')) {
      return; // If clicking inside `.overlay`, don't close
    }
    setTimeout(() => {
      this.promptSearchParams = null;
      this.isTyping = false;
      this.searchPromptSkip = 0
    }, 100);
  }

  /**
   * @description On Prompt Data Scroll
   */
  async onPromptDataScroll(reset = false) {
    if (!this.searchDefaultPrompts?.length) {
      this.searchLazyLoadedList = [];
      return;
    }
  
    if (reset) {
      this.startIndex = 0;
      this.skip = 25;
      this.searchLazyLoadedList = [];
  
      const filterValue = this.promptSearchParams?.trim()?.toLowerCase() || '';
      this.filteredPrompts = filterValue
        ? this.searchDefaultPrompts.filter((filterItem) =>
            filterItem['prompt']?.toLowerCase().includes(filterValue)
          )
        : [...this.searchDefaultPrompts];
  
      this.assignSearchPromptsOnLazyLoading();
    } else {
      if (this.startIndex >= this.filteredPrompts?.length) return; // Stop when all data is loaded
      this.startIndex = this.skip;
      this.skip += 25;
      this.assignSearchPromptsOnLazyLoading();
    }
  }
  
  /**
   * @description Assigns prompts for lazy loading
   */
  assignSearchPromptsOnLazyLoading() {
    const newItems = this.filteredPrompts.slice(this.startIndex, this.skip);
    this.searchLazyLoadedList.push(...newItems);
  }
  

  /**
   * @description getting Default Prompt Search 
   * @returns 
   */
  async getDefaultPromptsBySearch() {
    this.searchDefaultPrompts = [];
    let payload = {
      aid: this.profile.aid,
      oid: this.profile.oid,
      skip: this.searchPromptSkip,
      limit: 25,
      module_id: _.pluck(this.modules, 'id'),
      is_general_prompt: !this.isReportsVisible,
      is_report_prompt: this.isReportsVisible,
      is_agent_prompt: this.isAgentVisible,
      search_params: this.promptSearchParams ? this.promptSearchParams : "",
    };

    return new Promise(async (resolve, reject) => {
      await this._aiService
        .getDefaultPromptsBySearch(payload)
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res: any) => {
            if (res['err'] == false) {
              this.searchDefaultPrompts = res.data
              // if(this.searchPromptSkip == 0){
              //   this.searchDefaultPrompts = res.data
              // }
              // else{
              //   this.searchDefaultPrompts = [...this.searchDefaultPrompts, ...res['data']];
              // }
            }
            resolve(true);
          },
          error: (err) => {
            this._toaster.showError(
              'Error',
              'Error in Fetching Search Prompt Data',
              7000
            );
            reject();
          },
        });
    });
  }

  /**
   * @description Updating the Selected Search Prompt
   */
  async selectSearchedPrompt(event,prompt, i){
    this.promptSearchParams = prompt?.prompt
    let is_custom_prompt = false;

    if (prompt?.is_custom_prompt) {
      is_custom_prompt = true;
      this.responsePromptData = prompt
      this.customizePromptData = prompt.custom_config;
      this.customResponseId = prompt?._id;
    }
      setTimeout(async () => {
        await this.onEnterSearchPrompt(prompt?._id,prompt?.prompt,false,null,null,null,is_custom_prompt);
      }, 500);
  }

  @HostListener('document:click', ['$event'])
  onClickOutside(event: Event) {
    const target = event.target as HTMLElement;

    if (!this.elementRef.nativeElement.contains(target) && !target.closest('.overlay')) {
      this.isTyping = false;
    }
  }

  /**
   * @description Fetching the Feature value
   * @param val 
   * @returns 
   */
  getFeatureValue(val) {
    if (this.isReportsVisible) {
      const feature = this.data?.aiThemeConfig['FEATURE-LIST'].find(
        data => data['visibility'] === 'isReportsVisible'
      );
      return feature ? feature[val] : null;
    }
  
    if (this.isWidgetsVisible) {
      const feature = this.data?.aiThemeConfig['FEATURE-LIST'].find(
        data => data['visibility'] === 'isWidgetsVisible'
      );
      return feature ? feature[val] : null;
    }
  
    if (this.isAgentVisible) {
      const feature = this.data?.aiThemeConfig['FEATURE-LIST'].find(
        data => data['visibility'] === 'isAgentVisible'
      );
      return feature ? feature[val] : null;
    }
    return null; // Default return if nothing matches
  }

  /**
   * @description Making the Search Prompt True to display default prompts
   */
  setTypingTrue() {
    this.isTyping = true;
    this.onPromptDataScroll(true);
  }

  resetCustomisePrompt(){
    this.isCustomPrompt = false;
    this.responsePromptData = null;
    this.customResponseId = '';
    this.customizePromptData = null;
    this.responseCustomizeData = null;
  }
}
