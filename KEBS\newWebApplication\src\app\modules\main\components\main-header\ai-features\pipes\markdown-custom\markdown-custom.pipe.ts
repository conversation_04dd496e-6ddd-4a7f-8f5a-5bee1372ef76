import { Pipe, PipeTransform } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';

@Pipe({
  name: 'markdownCustom',
})
export class MarkdownCustomPipe implements PipeTransform {
  constructor(private sanitizer: DomSanitizer) {}
  transform(value: string): any {
    if (!value) return value;

    // 1. Bold: **text** or __text__
    value = value.replace(
      /\*\*(.*?)\*\*/g,
      '<strong style="font-weight: 600;">$1</strong>'
    );
    value = value.replace(
      /__(.*?)__/g,
      '<strong style="font-weight: 600;">$1</strong>'
    );

    // 2. Italics: *text* or _text_
    value = value.replace(
      /(?<!\w)\*(?!\s)(.*?)(?<!\s)\*(?!\w)/g,
      '<em style="font-style: italic;">$1</em>'
    );

    value = value.replace(
      /(?<!\w)_(?!\s)(.*?)(?<!\s)_(?!\w)/g,
      '<em style="font-style: italic;">$1</em>'
    );

    // 3. Images: ![alt text](http://url)
    value = value.replace(
      /\!\[([^\]]+)\]\((https?:\/\/[^\)]+)\)/g,
      '<img src="$2" alt="$1" style="max-width: 100%; height: auto; display: block; margin: 0 auto;" />'
    );

    // 4. Links: [text](http://url)
    value = value.replace(
      /\[([^\]]+)\]\((https?:\/\/[^\s)]+)\)/g,
      '<a href="$2" target="_blank" style="color: #007bff; text-decoration: none;">$1</a>'
    );

    // 5. Headers: # Header, ## Subheader, etc.
    value = value.replace(/^(#{1,6})\s*(.*)/gm, (match, hashes, headerText) => {
      const level = hashes.length; // Get header level (1-6)
      return `<h${level} style="font-weight: bold; margin-top: 0.5rem; margin-bottom: 0.5rem;">${headerText}</h${level}>`;
    });

    // 6. Code blocks: inline `code` and block ```code```
    value = value.replace(
      /```([\s\S]*?)```/g,
      (match, p1) =>
        `<pre style="margin-top: 0.5rem; padding: 8px; border-radius: 0.375rem; border: 0.5px solid rgba(0, 0, 0, 0.15); background-color: #f9f9f9;"><code>${p1.replace(
          /\n/g,
          '<br>'
        )}</code></pre>`
    ); // Block code
    value = value.replace(
      /`([^`]+)`/g,
      '<code style="border-radius: 0.3em; padding: 0.1em; color: #383a42;">$1</code>'
    ); // Inline code

    // 7. Lists: ordered and unordered
    // Unordered lists (- or * at the beginning of a line)
    value = value.replace(
      /(?:\r\n|\r|\n|^)[\s]*([*-])\s*(.*?)(?=\r\n|\r|\n|$)/g,
      '<uli>$2</uli>'
    );
    value = value.replace(
      /(<uli>.*<\/uli>)+/g,
      '<ul style="padding-left: 1.625em; margin-top: 0; margin-bottom: 1.25em;">$&</ul>'
    );
    // Ordered lists (numbers followed by a dot)
    value = value.replace(
      /(?:\r\n|\r|\n|^)\s*(\d+)\.\s+(.*?)(?=\r\n|\r|\n|$)/g,
      '<oli>$2</oli>'
    );
    value = value.replace(
      /(<oli>.*<\/oli>)+/g,
      '<ol style="padding-left: 1.625em; margin-top: 0; margin-bottom: 1.25em;">$&</ol>'
    );
    // Replace all oli and uli
    value = value
      .replace(
        /<uli>/g,
        '<li style="padding-left: 0.375em; margin-bottom: 0.5em; margin-top: 0.5em;">'
      )
      .replace(/<\/uli>/g, '</li>')
      .replace(
        /<oli>/g,
        '<li style="padding-left: 0.375em; margin-bottom: 0.5em; margin-top: 0.5em;">'
      )
      .replace(/<\/oli>/g, '</li>');

    // 8. Table UI
    value = value.replace(
      // Match a Markdown table by capturing lines that start and end with '|' and allowing optional leading whitespace
      /((?:^\s*\|.*\|\s*$\n?)+)/gm,
      (tableMatch) => {
        // Split the matched table into rows
        const rows = tableMatch.trim().split('\n');

        // Process the header row and body rows separately
        const headerRow = rows[0];
        const separatorRow = rows[1];
        const bodyRows = rows.slice(2);

        // Inline styles for GPT-style table
        const tableStyle =
          'max-width: 100%; min-width: 30%; border-collapse: collapse; font-size: 16px; color: #333;';
        const thStyle =
          'background-color: #FAFAFA; color: #45546E; padding: 10px; text-align: left; border: 1px solid #DADCE2; font-size: 12px; font-weight: 600; white-space: nowrap;';
        const tdStyle =
          'background-color: #ffffff; color: #45546E; padding: 10px; text-align: left; border-bottom: 1px solid #D4D6D8; border-right: 1px solid #D4D6D8; font-size: 12px; font-weight: 500;';
        const trHoverStyle = 'background-color: #ffffff;';

        // Convert the header row to <th> cells
        const headerHtml = headerRow
          .split('|')
          .filter((cell) => cell.trim())
          .map((cell, index, array) => {
            const style =
              index === 0
                ? thStyle.replace('border: 1px solid #D4D6D8;', 'border: 1px solid #D4D6D8; border-left: none;')
                : thStyle;
            return `<th style="${style}">${cell.trim()}</th>`;
          })
          .join('');

        // Convert each body row to <td> cells
        const bodyHtml = bodyRows
          .map((row, index) => {
            const cells = row
              .split('|')
              .filter((cell) => cell.trim())
              .map((cell, i, arr) => {
                const style =
                  i === 0
                    ? tdStyle + " border-left: 1px solid #D4D6D8;"
                    : tdStyle;
                return `<td style="${style}">${cell.trim()}</td>`;
              })
              .join('');
            // Apply alternating row color
            const rowStyle = index % 2 === 0 ? '' : `style="${trHoverStyle}"`;
            return `<tr ${rowStyle}>${cells}</tr>`;
          })
          .join('');

        // Wrap in <table>, <thead>, and <tbody> tags
        return `<table style="${tableStyle}"><thead><tr>${headerHtml}</tr></thead><tbody>${bodyHtml}</tbody></table>`;
      }
    );

    // Single new line to be replaced as empty string
    // Multiple new line to be replaced with br tag
    value = value.replace(/\n\n+/g, '<div></div><br>').replace(/\n/g, '');
    value = value.replace(/\n/g, '');

    return this.sanitizer.bypassSecurityTrustHtml(value);
  }
}
