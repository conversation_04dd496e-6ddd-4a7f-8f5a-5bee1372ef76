<div class="bg-container">
  <div class="chatbot-container">
    <div class="row" style="height: 100%; width: 100%;">
      <!-- History Icon & New Chat Icon -->
      <div class="p-1" style="max-width: 65px !important; background-color: #FFF3F4;" *ngIf="!isHistoryVisible">
        <div class="row p-3">
          <img class="ai-icon" [src]="data?.aiThemeConfig['AI-ICON-SHORT']" />
        </div>
        <div class="row pt-2 pb-2 pl-3" style="width: 60px; height: 60px;">
          <div *ngIf="!isHistoryVisible" class="svg-icon pt-4" tooltip="History" (click)="openHistory()">
            <svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M22.9385 15V9C22.9385 4 20.9074 2 15.8298 2H9.73664C4.65901 2 2.62796 4 2.62796 9V15C2.62796 20 4.65901 22 9.73664 22H15.8298C20.9074 22 22.9385 20 22.9385 15Z"
                stroke="#EE4961" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
              <path d="M15.8298 2V22" stroke="#EE4961" stroke-width="1.5" stroke-linecap="round"
                stroke-linejoin="round" />
              <path d="M8.72113 9.43994L11.3209 11.9999L8.72113 14.5599" stroke="#EE4961" stroke-width="1.5"
                stroke-linecap="round" stroke-linejoin="round" />
            </svg>
          </div>
          <div class="pt-4" (click)="openNewChat()" style="cursor: pointer;">
            <svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M12.8137 22C18.3991 22 22.969 17.5 22.969 12C22.969 6.5 18.3991 2 12.8137 2C7.22831 2 2.65845 6.5 2.65845 12C2.65845 17.5 7.22831 22 12.8137 22Z"
                stroke="#EE4961" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
              <path d="M8.75159 12H16.8758" stroke="#EE4961" stroke-width="1.5" stroke-linecap="round"
                stroke-linejoin="round" />
              <path d="M12.8137 16V8" stroke="#EE4961" stroke-width="1.5" stroke-linecap="round"
                stroke-linejoin="round" />
            </svg>
          </div>
        </div>
      </div>
      <!-- History Container -->
      <div *ngIf="isHistoryVisible" class="history-container">
        <div class="header">
          <div>
            <img class="ai-icon-history" [src]="data?.aiThemeConfig['AI-ICON']" />
          </div>
          <div class="svg-icon" tooltip="Close History" (click)="closeHistory()">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M14.97 22.75H8.96997C3.53997 22.75 1.21997 20.43 1.21997 15V9C1.21997 3.57 3.53997 1.25 8.96997 1.25H14.97C20.4 1.25 22.72 3.57 22.72 9V15C22.72 20.43 20.41 22.75 14.97 22.75ZM8.96997 2.75C4.35997 2.75 2.71997 4.39 2.71997 9V15C2.71997 19.61 4.35997 21.25 8.96997 21.25H14.97C19.58 21.25 21.22 19.61 21.22 15V9C21.22 4.39 19.58 2.75 14.97 2.75H8.96997Z"
                fill="#EE4961" />
              <path
                d="M7.96997 22.75C7.55997 22.75 7.21997 22.41 7.21997 22V2C7.21997 1.59 7.55997 1.25 7.96997 1.25C8.37997 1.25 8.71997 1.59 8.71997 2V22C8.71997 22.41 8.38997 22.75 7.96997 22.75Z"
                fill="#EE4961" />
              <path
                d="M14.97 15.3099C14.78 15.3099 14.59 15.2399 14.44 15.0899L11.88 12.5299C11.59 12.2399 11.59 11.7599 11.88 11.4699L14.44 8.90988C14.73 8.61988 15.21 8.61988 15.5 8.90988C15.79 9.19988 15.79 9.67988 15.5 9.96988L13.48 11.9999L15.51 14.0299C15.8 14.3199 15.8 14.7999 15.51 15.0899C15.36 15.2399 15.17 15.3099 14.97 15.3099Z"
                fill="#EE4961" />
            </svg>
          </div>
        </div>
        <div class="pt-4 pl-4" (click)="openNewChat()">
          <button class="new-chat-btn" (click)="openNewChat()">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M12 2C6.49 2 2 6.49 2 12C2 17.51 6.49 22 12 22C17.51 22 22 17.51 22 12C22 6.49 17.51 2 12 2ZM16 12.75H12.75V16C12.75 16.41 12.41 16.75 12 16.75C11.59 16.75 11.25 16.41 11.25 16V12.75H8C7.59 12.75 7.25 12.41 7.25 12C7.25 11.59 7.59 11.25 8 11.25H11.25V8C11.25 7.59 11.59 7.25 12 7.25C12.41 7.25 12.75 7.59 12.75 8V11.25H16C16.41 11.25 16.75 11.59 16.75 12C16.75 12.41 16.41 12.75 16 12.75Z"
                fill="#EE4961" />
            </svg>
            <span class="label" (click)="openNewChat()">New Chat</span>
          </button>
        </div>


        <div class="search-ui-history">
          <div class="search-bar">
            <input type="text" [(ngModel)]="historySearchParams" placeholder="Search From Threads"
              (keydown.enter)="onEnterSearchHistory()" />
          </div>
          <div *ngIf="!historySearchParams" class="svg-icon">
            <svg (click)="onEnterSearchHistory()" width="16" height="16" viewBox="0 0 16 16" fill="none">
              <mask id="mask0_1126_15698" style="mask-type: alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="16"
                height="16">
                <rect width="16" height="16" fill="#D9D9D9" />
              </mask>
              <g mask="url(#mask0_1126_15698)">
                <path
                  d="M12.7116 13.3178L8.7411 9.34708C8.40777 9.60008 8.04227 9.79603 7.6446 9.93492C7.24693 10.0738 6.83654 10.1432 6.41343 10.1432C5.37366 10.1432 4.48971 9.77931 3.7616 9.05142C3.03349 8.32353 2.66943 7.43997 2.66943 6.40075C2.66943 5.36164 3.03338 4.47775 3.76127 3.74908C4.48916 3.02053 5.37271 2.65625 6.41193 2.65625C7.45104 2.65625 8.33493 3.0203 9.0636 3.74842C9.79216 4.47653 10.1564 5.36047 10.1564 6.40025C10.1564 6.83614 10.0849 7.25292 9.94177 7.65058C9.79854 8.04836 9.60471 8.40747 9.36027 8.72792L13.3308 12.6984L12.7116 13.3178ZM6.41293 9.27675C7.21638 9.27675 7.89671 8.99808 8.45393 8.44075C9.01127 7.88353 9.28993 7.20319 9.28993 6.39975C9.28993 5.59631 9.01127 4.91597 8.45393 4.35875C7.89671 3.80142 7.21638 3.52275 6.41293 3.52275C5.60949 3.52275 4.92916 3.80142 4.37193 4.35875C3.8146 4.91597 3.53593 5.59631 3.53593 6.39975C3.53593 7.20319 3.8146 7.88353 4.37193 8.44075C4.92916 8.99808 5.60949 9.27675 6.41293 9.27675Z"
                  fill="#7D838B" />
              </g>
            </svg>
          </div>
          <mat-icon *ngIf="historySearchParams" (click)="historySearchParams = ''; onEnterSearchHistory()"
            class="close-icon">
            close
          </mat-icon>
        </div>
        <div *ngIf="isHistoryLoading" class="loading-state">
          <div *ngFor="let item of repeatArray" class="loader"></div>
        </div>
        <div *ngIf="
                  (!historyGroupedDateData || historyGroupedDateData.length == 0) &&
                  (!pinnedHistoryData || pinnedHistoryData.length == 0) &&
                  !isHistoryLoading" class="no-history">
          -No History-
        </div>
        <div *ngIf="
                  ((historyGroupedDateData && historyGroupedDateData?.length > 0) ||
                    (pinnedHistoryData && pinnedHistoryData?.length > 0)) &&
                  !isHistoryLoading
                " class="content" infinite-scroll [infiniteScrollDistance]="3" [infiniteScrollThrottle]="100"
          (scrolled)="onHistoryDataScroll()" [scrollWindow]="false">
          <div *ngIf="pinnedHistoryData?.length" class="single-date-log">
            <div class="date">
              Favorites
              <!-- <svg class="pin-icon" height="20px" viewBox="0 -960 960 960" width="20px" fill="#F27A6C">
                <path
                  d="m624-480 96 96v72H516v228l-36 36-36-36v-228H240v-72l96-96v-264h-48v-72h384v72h-48v264Zm-282 96h276l-66-66v-294H408v294l-66 66Zm138 0Z" />
              </svg> -->
            </div>
            <div class="single-history" [ngClass]="
                      log?.thread_id == historySelectedThreadId ? 'thread-selected' : ''
                    " *ngFor="let log of pinnedHistoryData; let i = index">
              <div *ngIf="!log?.isRenameInProgress" [ngClass]="
              log?.thread_id == historySelectedThreadId ? 'selected-history-text' : 'history-text' " [matTooltip]="log?.heading"
                (click)="setSelectedHistoryThread(log?.thread_id)" [ngStyle]="{
                        'pointer-events':
                          isThreadLoading || isPromptApiInProgress ? 'none' : ''
                      }">
                {{ log?.heading || 'New Chat'}}
              </div>
              <div *ngIf="log?.isRenameInProgress" class="inline-edit">
                <input [id]="'#pinnedHistoryInputSearch-' + i" type="text" maxlength="150"
                  placeholder="Enter a description..." [(ngModel)]="log.heading"
                  (keydown.enter)="onRenameThread(log?._id, log?.heading, log?.thread_id)" />
              </div>
              <div *ngIf="!log?.isRenameInProgress" class="svg-icon" [matMenuTriggerFor]="historyMenu"
                (click)="setSelectedHistoryThreadData(log, -1, i)">
                <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
                  <mask id="mask0_1126_15589" style="mask-type: alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="12"
                    height="12">
                    <rect width="12" height="12" fill="#D9D9D9" />
                  </mask>
                  <g mask="url(#mask0_1126_15589)">
                    <path
                      d="M6 9.63448C5.79375 9.63448 5.61721 9.56103 5.47038 9.41411C5.32346 9.26728 5.25 9.09073 5.25 8.88448C5.25 8.67823 5.32346 8.50165 5.47038 8.35473C5.61721 8.2079 5.79375 8.13448 6 8.13448C6.20625 8.13448 6.38279 8.2079 6.52962 8.35473C6.67654 8.50165 6.75 8.67823 6.75 8.88448C6.75 9.09073 6.67654 9.26728 6.52962 9.41411C6.38279 9.56103 6.20625 9.63448 6 9.63448ZM6 6.74986C5.79375 6.74986 5.61721 6.6764 5.47038 6.52948C5.32346 6.38265 5.25 6.20611 5.25 5.99986C5.25 5.79361 5.32346 5.61707 5.47038 5.47023C5.61721 5.32332 5.79375 5.24986 6 5.24986C6.20625 5.24986 6.38279 5.32332 6.52962 5.47023C6.67654 5.61707 6.75 5.79361 6.75 5.99986C6.75 6.20611 6.67654 6.38265 6.52962 6.52948C6.38279 6.6764 6.20625 6.74986 6 6.74986ZM6 3.86523C5.79375 3.86523 5.61721 3.79182 5.47038 3.64498C5.32346 3.49807 5.25 3.32148 5.25 3.11523C5.25 2.90898 5.32346 2.73244 5.47038 2.58561C5.61721 2.43869 5.79375 2.36523 6 2.36523C6.20625 2.36523 6.38279 2.43869 6.52962 2.58561C6.67654 2.73244 6.75 2.90898 6.75 3.11523C6.75 3.32148 6.67654 3.49807 6.52962 3.64498C6.38279 3.79182 6.20625 3.86523 6 3.86523Z"
                      fill="#5F6C81" />
                  </g>
                </svg>
              </div>
            </div>
          </div>
          <mat-divider *ngIf="pinnedHistoryData?.length" class="divider"></mat-divider>
          <ng-container *ngFor="let item of historyGroupedDateData; let dateIndex = index">
            <div class="single-date-log">
              <div class="date">
                {{ item }}
              </div>
              <div class="single-history" [ngClass]="
                        log?.thread_id == historySelectedThreadId ? 'thread-selected' : ''
                      " *ngFor="let log of historyGroupedData[item]; let i = index">
                <div *ngIf="!log?.isRenameInProgress" [ngClass]="
                log?.thread_id == historySelectedThreadId ? 'selected-history-text' : 'history-text' "
                  [matTooltip]="log?.heading" (click)="setSelectedHistoryThread(log?.thread_id)" [ngStyle]="{
                          'pointer-events':
                            isThreadLoading || isPromptApiInProgress ? 'none' : ''
                        }">
                  {{ log?.heading || 'New Chat' }}
                </div>
                <div *ngIf="log?.isRenameInProgress" class="inline-edit">
                  <input [id]="'#historyInputSearch-' + dateIndex + '-' + i" type="text" maxlength="150"
                    placeholder="Enter a description..." [(ngModel)]="log.heading" (keydown.enter)="
                            onRenameThread(log?._id, log?.heading, log?.thread_id)
                          " />
                </div>
                <div *ngIf="!log?.isRenameInProgress" class="svg-icon" [matMenuTriggerFor]="historyMenu"
                  (click)="setSelectedHistoryThreadData(log, dateIndex, i)">
                  <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
                    <mask id="mask0_1126_15589" style="mask-type: alpha" maskUnits="userSpaceOnUse" x="0" y="0"
                      width="12" height="12">
                      <rect width="12" height="12" fill="#D9D9D9" />
                    </mask>
                    <g mask="url(#mask0_1126_15589)">
                      <path
                        d="M6 9.63448C5.79375 9.63448 5.61721 9.56103 5.47038 9.41411C5.32346 9.26728 5.25 9.09073 5.25 8.88448C5.25 8.67823 5.32346 8.50165 5.47038 8.35473C5.61721 8.2079 5.79375 8.13448 6 8.13448C6.20625 8.13448 6.38279 8.2079 6.52962 8.35473C6.67654 8.50165 6.75 8.67823 6.75 8.88448C6.75 9.09073 6.67654 9.26728 6.52962 9.41411C6.38279 9.56103 6.20625 9.63448 6 9.63448ZM6 6.74986C5.79375 6.74986 5.61721 6.6764 5.47038 6.52948C5.32346 6.38265 5.25 6.20611 5.25 5.99986C5.25 5.79361 5.32346 5.61707 5.47038 5.47023C5.61721 5.32332 5.79375 5.24986 6 5.24986C6.20625 5.24986 6.38279 5.32332 6.52962 5.47023C6.67654 5.61707 6.75 5.79361 6.75 5.99986C6.75 6.20611 6.67654 6.38265 6.52962 6.52948C6.38279 6.6764 6.20625 6.74986 6 6.74986ZM6 3.86523C5.79375 3.86523 5.61721 3.79182 5.47038 3.64498C5.32346 3.49807 5.25 3.32148 5.25 3.11523C5.25 2.90898 5.32346 2.73244 5.47038 2.58561C5.61721 2.43869 5.79375 2.36523 6 2.36523C6.20625 2.36523 6.38279 2.43869 6.52962 2.58561C6.67654 2.73244 6.75 2.90898 6.75 3.11523C6.75 3.32148 6.67654 3.49807 6.52962 3.64498C6.38279 3.79182 6.20625 3.86523 6 3.86523Z"
                        fill="#5F6C81" />
                    </g>
                  </svg>
                </div>
              </div>
            </div>
            <mat-divider *ngIf="historyGroupedDateData.length - 1 != dateIndex" class="divider"></mat-divider>
          </ng-container>
        </div>
      </div>
      <!-- Main Container -->
      <div class="align-items-with-gap main-container p-0">
        <!-- Loading UI -->
        <ng-container *ngIf="isThreadLoading || isLoading">
          <div class="loading-container">
            <img class="gif" [src]="data?.aiThemeConfig['AI-MAIN-LOADER']" />
            <div *ngIf="!isLoading" class="carousel-text">
              <div class="text-content" [class.slide]="isSliding">
                <div class="main-text">
                  {{ threadTexts[currentThreadLoadTextIndex].text }}
                </div>
                <div class="sub-text">
                  {{ threadTexts[currentThreadLoadTextIndex].sub_text }}
                </div>
              </div>
            </div>
          </div>
        </ng-container>

        <ng-container *ngIf="!isThreadLoading && !isLoading">
          <!-- Header container with FAQ & close -->
          <div class="row" style="display: flex; height: 60px;border-bottom: 1px solid #0000000D; box-shadow: 0px 4px 8px #0000000D; align-items: center;">
            <div class="col-11">

            </div>
            <div class="col-1" style="display: flex; justify-content: space-evenly; align-items: baseline;">
              <div *ngIf="!isLoading && promptLibraryData.length > 0" class="svg-icon" tooltip="FAQ">
                <svg *ngIf="!isPromptLibraryVisible" (click)="openPromptLibrary()" width="21" height="21"
                  viewBox="0 0 14 15" fill="none">
                  <path
                    d="M1.79492 10.9117C1.91026 10.857 2.02814 10.8111 2.14859 10.7739C2.26903 10.7367 2.39903 10.7182 2.53859 10.7182H3.25642V2.51302H2.53859C2.32781 2.51302 2.1512 2.58546 2.00876 2.73035C1.8662 2.87524 1.79492 3.05069 1.79492 3.25669V10.9117ZM2.53859 14.1797C2.05426 14.1797 1.64253 14.0114 1.30342 13.6749C0.964422 13.3383 0.794922 12.9296 0.794922 12.4489V3.25669C0.794922 2.77235 0.964422 2.36063 1.30342 2.02152C1.64253 1.68252 2.05426 1.51302 2.53859 1.51302H7.29492V2.51302H4.25642V10.7182H8.66675V8.34635H9.66675V11.7182H2.53859C2.33259 11.7182 2.15714 11.7881 2.01226 11.9279C1.86737 12.0677 1.79492 12.2411 1.79492 12.4479C1.79492 12.6545 1.86737 12.8282 2.01226 12.9689C2.15714 13.1094 2.33259 13.1797 2.53859 13.1797H11.1283V7.67969H12.1283V14.1797H2.53859ZM10.1283 7.67969C10.1283 6.70391 10.4676 5.87663 11.1464 5.19785C11.8252 4.51908 12.6525 4.17969 13.6283 4.17969C12.6525 4.17969 11.8252 3.8403 11.1464 3.16152C10.4676 2.48274 10.1283 1.65547 10.1283 0.679688C10.1283 1.65547 9.78887 2.48274 9.11009 3.16152C8.43131 3.8403 7.60403 4.17969 6.62825 4.17969C7.60403 4.17969 8.43131 4.51908 9.11009 5.19785C9.78887 5.87663 10.1283 6.70391 10.1283 7.67969Z"
                    fill="url(#paint0_linear_2061_126464)" />
                  <defs>
                    <linearGradient id="paint0_linear_2061_126464" x1="13.6283" y1="7.42969" x2="0.11551" y2="7.42969"
                      gradientUnits="userSpaceOnUse">
                      <stop stop-color="#EF4A61" />
                      <stop offset="1" stop-color="#F27A6C" />
                    </linearGradient>
                  </defs>
                </svg>
                <svg *ngIf="isPromptLibraryVisible" (click)="openNewChat()" width="34" height="35" viewBox="0 0 28 25"
                  fill="none">
                  <mask id="mask0_1534_29427" style="mask-type: alpha" maskUnits="userSpaceOnUse" x="6" y="3" width="17"
                    height="17">
                    <rect x="6.46204" y="3.84497" width="16" height="16" fill="#D9D9D9" />
                  </mask>
                  <g mask="url(#mask0_1534_29427)">
                    <path
                      d="M11.2568 14.7167H12.2568V6.51156H11.2568V14.7167ZM20.1287 18.1782H10.539C10.0547 18.1782 9.64296 18.0087 9.30385 17.6697C8.96485 17.3306 8.79535 16.9189 8.79535 16.4346V7.25522C8.79535 6.77089 8.96485 6.35917 9.30385 6.02006C9.64296 5.68106 10.0547 5.51156 10.539 5.51156H14.6492C14.3526 5.87478 14.1212 6.28206 13.955 6.73339C13.7887 7.18461 13.7055 7.66622 13.7055 8.17822C13.7055 9.32689 14.0863 10.3177 14.8478 11.1507C15.6094 11.9836 16.5492 12.4573 17.6672 12.5719V15.7167H10.539C10.333 15.7167 10.1576 15.7866 10.0127 15.9264C9.86779 16.0663 9.79535 16.2396 9.79535 16.4464C9.79535 16.6531 9.86779 16.8267 10.0127 16.9674C10.1576 17.1079 10.333 17.1782 10.539 17.1782H19.1287V12.4847C19.3132 12.4419 19.4878 12.3883 19.6523 12.3237C19.8169 12.2592 19.9757 12.1816 20.1287 12.0911V18.1782ZM18.1287 11.6782C18.1287 10.7024 18.4681 9.87517 19.1468 9.19639C19.8256 8.51761 20.6529 8.17822 21.6287 8.17822C20.6529 8.17822 19.8256 7.83883 19.1468 7.16006C18.4681 6.48128 18.1287 5.654 18.1287 4.67822C18.1287 5.654 17.7893 6.48128 17.1105 7.16006C16.4317 7.83883 15.6045 8.17822 14.6287 8.17822C15.6045 8.17822 16.4317 8.51761 17.1105 9.19639C17.7893 9.87517 18.1287 10.7024 18.1287 11.6782Z"
                      fill="url(#paint0_linear_1534_29427)" />
                  </g>
                  <line x1="11.962" y1="21.345" x2="16.962" y2="21.345" stroke="url(#paint1_linear_1534_29427)" />
                  <defs>
                    <linearGradient id="paint0_linear_1534_29427" x1="21.6287" y1="11.4282" x2="8.11594" y2="11.4282"
                      gradientUnits="userSpaceOnUse">
                      <stop stop-color="#EF4A61" />
                      <stop offset="1" stop-color="#F27A6C" />
                    </linearGradient>
                    <linearGradient id="paint1_linear_1534_29427" x1="16.962" y1="22.345" x2="11.6973" y2="22.345"
                      gradientUnits="userSpaceOnUse">
                      <stop stop-color="#EF4A61" />
                      <stop offset="1" stop-color="#F27A6C" />
                    </linearGradient>
                  </defs>
                </svg>
              </div>
              <div class="svg-icon" tooltip="Close" (click)="onDialogClose()">
                <svg width="34" height="35" viewBox="0 0 24 25" fill="none">
                  <mask id="mask0_1555_843" style="mask-type: alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="24"
                    height="25">
                    <rect y="0.845703" width="34" height="35" fill="#D9D9D9" />
                  </mask>
                  <g mask="url(#mask0_1555_843)">
                    <path
                      d="M8.227 17.6813L7.16357 16.6178L10.9367 12.8447L7.16357 9.09663L8.227 8.0332L12.0001 11.8063L15.7482 8.0332L16.8116 9.09663L13.0385 12.8447L16.8116 16.6178L15.7482 17.6813L12.0001 13.9082L8.227 17.6813Z"
                      fill="#1C1B1F" />
                  </g>
                </svg>
              </div>
            </div>

          </div>
          <!-- Main Greetings -->
          <div class="row pt-3" style="display: flex; justify-content: center; height: 160px;"
            *ngIf="isHomeScreenVisible">
            <div class="header-content-alignment">
              <img class="ai-img-header" [src]="data?.aiThemeConfig['AI-ICON-SHORT']" />
              <div class="align-items-column">
                <div class="main-text" [ngClass]="dialogExpanded ? 'header-content' : ''">
                  {{
                  getGreetingWithName(
                  data?.aiThemeConfig["WELCOME-TEXT-001"],
                  profile?.name
                  )
                  }}
                </div>
                <div class="sub-text" [ngClass]="dialogExpanded ? 'header-content' : ''">
                  {{ data?.aiThemeConfig["WELCOME-TEXT-002"] }}
                </div>
              </div>
            </div>
          </div>
          <!-- Textarea -->
          <div class="row text-area" *ngIf="isHomeScreenVisible">
            <div class="col-1"></div>
            <div class="col-10">
              <div class="search-ui-new">
                <div class="search-bar-new pb-0">
                  <textarea #textAreaRef type="text" (blur)="clearSearchInput()" [(ngModel)]="promptSearchParams" placeholder=""
                    (keydown.enter)="onSearchPrompt($event, promptSearchParams)" (input)="setTypingTrue(); adjustTextareaHeight(textAreaRef)"></textarea>
                </div>
                <div *ngIf="
                                !isPromptApiInProgress &&
                                promptSearchParams &&
                                promptSearchParams.length > 0
                              " style="display: flex; justify-content: flex-end; padding-right: 13px;"
                  (click)="onSearchPrompt($event, promptSearchParams)">
                  <span class="svg-icon">
                  <svg width="28" height="28" viewBox="0 0 36 36" fill="none">
                    <rect width="36" height="36" rx="18" fill="url(#paint0_linear_1555_26595)" />
                    <path
                      d="M11.807 21.3475L12.967 19.0275C13.287 18.3808 13.287 17.6275 12.967 16.9808L11.807 14.6541C10.8137 12.6675 12.9537 10.5675 14.9204 11.6075L15.947 12.1541C16.0937 12.2275 16.207 12.3475 16.267 12.4941L20.0604 20.9275C20.2137 21.2741 20.0737 21.6808 19.7404 21.8541L14.9137 24.3941C12.9537 25.4341 10.8137 23.3341 11.807 21.3475Z"
                      fill="white" />
                    <path opacity="0.4"
                      d="M20.8738 20.4003L18.3871 14.8803C18.1071 14.2603 18.7738 13.6336 19.3738 13.9536L23.2205 15.9803C24.8538 16.8403 24.8538 19.1736 23.2205 20.0336L21.8605 20.747C21.4938 20.9336 21.0471 20.7803 20.8738 20.4003Z"
                      fill="white" />
                    <defs>
                      <linearGradient id="paint0_linear_1555_26595" x1="36" y1="18" x2="-1.90588" y2="18"
                        gradientUnits="userSpaceOnUse">
                        <stop stop-color="#EF4A61" />
                        <stop offset="1" stop-color="#F27A6C" />
                      </linearGradient>
                    </defs>
                  </svg>
                  </span>
                </div>
                <div *ngIf="
                                isPromptApiInProgress ||
                                !promptSearchParams ||
                                promptSearchParams.length == 0
                              " 
                  style="display: flex; justify-content: flex-end; padding-right: 13px;">
                  <svg width="28" height="28" viewBox="0 0 36 36" fill="none">
                    <rect width="36" height="36" rx="18" fill="grey" />
                    <path
                      d="M11.807 21.3475L12.967 19.0275C13.287 18.3808 13.287 17.6275 12.967 16.9808L11.807 14.6541C10.8137 12.6675 12.9537 10.5675 14.9204 11.6075L15.947 12.1541C16.0937 12.2275 16.207 12.3475 16.267 12.4941L20.0604 20.9275C20.2137 21.2741 20.0737 21.6808 19.7404 21.8541L14.9137 24.3941C12.9537 25.4341 10.8137 23.3341 11.807 21.3475Z"
                      fill="white" />
                    <path opacity="0.4"
                      d="M20.8738 20.4003L18.3871 14.8803C18.1071 14.2603 18.7738 13.6336 19.3738 13.9536L23.2205 15.9803C24.8538 16.8403 24.8538 19.1736 23.2205 20.0336L21.8605 20.747C21.4938 20.9336 21.0471 20.7803 20.8738 20.4003Z"
                      fill="white" />
                    <defs>
                      <linearGradient id="paint0_linear_1555_26595" x1="36" y1="18" x2="-1.90588" y2="18"
                        gradientUnits="userSpaceOnUse">
                        <stop stop-color="#EF4A61" />
                        <stop offset="1" stop-color="#F27A6C" />
                      </linearGradient>
                    </defs>
                  </svg>
                </div>
              </div>
            </div>

            <div class="col-1"></div>

          </div>
          <!-- Prompt Library -->
          <div *ngIf="!isLoading && isPromptLibraryVisible" class="prompt-library-content  pt-2 pl-4 pr-4">
            <div class="header">
              <div class="prompt-title">
                <mat-icon (click)="openNewChat()" class="prompt-title-icon">arrow_back</mat-icon>
                <div class="prompt-title-text">AI Prompt Library</div>
              </div>
              <div class="search-bar">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                  <g clip-path="url(#clip0_1497_30370)">
                    <path
                      d="M12.0207 11.0779L14.876 13.9326L13.9327 14.8759L11.078 12.0206C10.0158 12.8721 8.69468 13.3352 7.33334 13.3333C4.02134 13.3333 1.33334 10.6453 1.33334 7.33325C1.33334 4.02125 4.02134 1.33325 7.33334 1.33325C10.6453 1.33325 13.3333 4.02125 13.3333 7.33325C13.3353 8.69459 12.8722 10.0157 12.0207 11.0779ZM10.6833 10.5833C11.5294 9.71318 12.0019 8.54687 12 7.33325C12 4.75459 9.91134 2.66659 7.33334 2.66659C4.75468 2.66659 2.66668 4.75459 2.66668 7.33325C2.66668 9.91125 4.75468 11.9999 7.33334 11.9999C8.54696 12.0018 9.71327 11.5293 10.5833 10.6833L10.6833 10.5833Z"
                      fill="#B9C0CA" />
                  </g>
                  <defs>
                    <clipPath id="clip0_1497_30370">
                      <rect width="16" height="16" fill="white" />
                    </clipPath>
                  </defs>
                </svg>
                <input type="text" placeholder="Search Prompts Templates..." maxlength="50"
                  [(ngModel)]="promptLibrarySearchParams" (ngModelChange)="onSearchParamsChange()" />
                <svg *ngIf="
                        promptLibrarySearchParams &&
                        promptLibrarySearchParams.length > 0
                      " style="cursor: pointer" (click)="promptLibrarySearchParams = ''; onSearchParamsChange()"
                  width="24" height="25" viewBox="0 0 24 25" fill="none">
                  <mask id="mask0_1555_843" maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="25"
                    style="mask-type: alpha">
                    <rect y="0.845703" width="24" height="24" fill="#D9D9D9"></rect>
                  </mask>
                  <g mask="url(#mask0_1555_843)">
                    <path
                      d="M8.227 17.6813L7.16357 16.6178L10.9367 12.8447L7.16357 9.09663L8.227 8.0332L12.0001 11.8063L15.7482 8.0332L16.8116 9.09663L13.0385 12.8447L16.8116 16.6178L15.7482 17.6813L12.0001 13.9082L8.227 17.6813Z"
                      fill="#B9C0CA"></path>
                  </g>
                </svg>
              </div>
            </div>
            <div class="content pb-4">
              <div class="categories">
                <div class="header">
                  <svg style="min-width: 16px" width="16" height="16" viewBox="0 0 16 16" fill="none">
                    <path
                      d="M11.3333 6.66658H12.6667C14 6.66658 14.6667 5.99992 14.6667 4.66659V3.33325C14.6667 1.99992 14 1.33325 12.6667 1.33325H11.3333C9.99999 1.33325 9.33333 1.99992 9.33333 3.33325V4.66659C9.33333 5.99992 9.99999 6.66658 11.3333 6.66658Z"
                      stroke="#111434" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round"
                      stroke-linejoin="round" />
                    <path
                      d="M3.33333 14.6666H4.66666C5.99999 14.6666 6.66666 13.9999 6.66666 12.6666V11.3333C6.66666 9.99992 5.99999 9.33325 4.66666 9.33325H3.33333C1.99999 9.33325 1.33333 9.99992 1.33333 11.3333V12.6666C1.33333 13.9999 1.99999 14.6666 3.33333 14.6666Z"
                      stroke="#111434" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round"
                      stroke-linejoin="round" />
                    <path
                      d="M3.99999 6.66658C5.47275 6.66658 6.66666 5.47268 6.66666 3.99992C6.66666 2.52716 5.47275 1.33325 3.99999 1.33325C2.52724 1.33325 1.33333 2.52716 1.33333 3.99992C1.33333 5.47268 2.52724 6.66658 3.99999 6.66658Z"
                      stroke="#111434" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round"
                      stroke-linejoin="round" />
                    <path
                      d="M12 14.6666C13.4728 14.6666 14.6667 13.4727 14.6667 11.9999C14.6667 10.5272 13.4728 9.33325 12 9.33325C10.5272 9.33325 9.33333 10.5272 9.33333 11.9999C9.33333 13.4727 10.5272 14.6666 12 14.6666Z"
                      stroke="#111434" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round"
                      stroke-linejoin="round" />
                  </svg>
                  <div class="categories-text">Categories</div>
                  <div class="count">
                    {{
                    (
                    promptLibraryData
                    | filterPromptLibrary : promptLibrarySearchParams
                    ).length > 9
                    ? (
                    promptLibraryData
                    | filterPromptLibrary : promptLibrarySearchParams
                    ).length
                    : "0" +
                    (
                    promptLibraryData
                    | filterPromptLibrary : promptLibrarySearchParams
                    ).length
                    }}
                  </div>
                </div>
                <div class="categories-list">
                  <ng-container *ngFor="
                          let item of promptLibraryData
                            | filterPromptLibrary : promptLibrarySearchParams;
                          let i = index
                        ">
                    <div [ngClass]="{
                            'selected-category-list':
                              currentSelectedPromptLibraryCategory == item.name
                          }" class="category-list" (click)="
                            setPromptLibraryIndex(i);
                            setPromptLibraryCategory(item.name)
                          ">
                      <svg style="min-width: 14px" width="14" height="20" viewBox="0 0 14 20" fill="none">
                        <path
                          d="M7.00001 19.5769C6.49489 19.5769 6.05931 19.4019 5.69328 19.0519C5.32726 18.7019 5.12822 18.2743 5.09616 17.7692H8.90386C8.87179 18.2743 8.67275 18.7019 8.30673 19.0519C7.9407 19.4019 7.50512 19.5769 7.00001 19.5769ZM3.25003 16.3846V14.8846H10.75V16.3846H3.25003ZM3.40386 13.5C2.35644 12.8487 1.52728 11.9977 0.91638 10.9471C0.30548 9.89644 3.05176e-05 8.74741 3.05176e-05 7.49998C3.05176e-05 5.55128 0.679514 3.89743 2.03848 2.53845C3.39746 1.17948 5.05131 0.5 7.00001 0.5C8.94871 0.5 10.6025 1.17948 11.9615 2.53845C13.3205 3.89743 14 5.55128 14 7.49998C14 8.74741 13.6945 9.89644 13.0836 10.9471C12.4727 11.9977 11.6436 12.8487 10.5962 13.5H3.40386ZM3.85001 12H10.15C10.9 11.4666 11.4792 10.8083 11.8875 10.025C12.2958 9.24164 12.5 8.39998 12.5 7.49998C12.5 5.96664 11.9667 4.66664 10.9 3.59998C9.83334 2.53331 8.53334 1.99998 7.00001 1.99998C5.46667 1.99998 4.16667 2.53331 3.10001 3.59998C2.03334 4.66664 1.50001 5.96664 1.50001 7.49998C1.50001 8.39998 1.70417 9.24164 2.11251 10.025C2.52084 10.8083 3.10001 11.4666 3.85001 12Z"
                          fill="#D4D6D8" />
                      </svg>
                      <div class="text" [matTooltip]="item.name">
                        {{ item.name }}
                      </div>
                      <div [ngClass]="{
                              'selected-count':
                                currentSelectedPromptLibraryCategory == item.name
                            }" class="count">
                        {{
                        (promptLibraryData
                        | filterPromptLibrary : promptLibrarySearchParams)[i][
                        "prompts"
                        ].length > 9
                        ? (promptLibraryData
                        | filterPromptLibrary : promptLibrarySearchParams)[
                        i
                        ]["prompts"].length
                        : "0" +
                        (promptLibraryData
                        | filterPromptLibrary : promptLibrarySearchParams)[
                        i
                        ]["prompts"].length
                        }}
                      </div>
                    </div>
                  </ng-container>
                </div>
              </div>
              <div *ngIf="
                      (
                        promptLibraryData
                        | filterPromptLibrary : promptLibrarySearchParams
                      ).length > 0
                    " class="category-content">
                <div *ngFor="
                        let item of (promptLibraryData
                          | filterPromptLibrary : promptLibrarySearchParams)[
                          currentSelectedPromptLibraryCategoryIndex
                        ]['prompts'];
                        let i = index
                      " class="single-prompt" (click)="copyPromptToClipboard(item)"
                  [cdkCopyToClipboard]="item?.prompt || ''">
                  <div class="text" [innerHTML]="highlightPromptKey(item.prompt)"></div>
                  <div class="copy">
                    <img width="12px" height="12px" [src]="'https://assets.kebs.app/document-copy.png'" />
                    {{ item?.isCopyInProgress ? "Copied!" : "Copy" }}
                  </div>
                </div>
              </div>
              <div *ngIf="
                      (
                        promptLibraryData
                        | filterPromptLibrary : promptLibrarySearchParams
                      ).length == 0
                    " class="category-content-empty-state">
                No Categories Found!
              </div>
            </div>
            <div class="search-ui-chat">
              <div class="search-bar-chat" (mouseenter)="setTypingTrue()">
                <textarea type="text" (blur)="clearSearchInput()" [(ngModel)]="promptSearchParams" placeholder=""
                (keydown.enter)="onSearchPromptFromLibrary(promptSearchParams); handleEnter($event)" (input)="setTypingTrue();" (focus)="setTypingTrue()"></textarea>
              </div>
              <div *ngIf="
                            !isPromptApiInProgress &&
                            promptSearchParams &&
                            promptSearchParams.length > 0
                          " class="svg-icon" (click)="onSearchPromptFromLibrary(promptSearchParams)" style="padding-top: 21px; padding-right: 13px;">
                <svg width="28" height="28" viewBox="0 0 36 36" fill="none">
                  <rect width="36" height="36" rx="18" fill="url(#paint0_linear_1555_26595)" />
                  <path
                    d="M11.807 21.3475L12.967 19.0275C13.287 18.3808 13.287 17.6275 12.967 16.9808L11.807 14.6541C10.8137 12.6675 12.9537 10.5675 14.9204 11.6075L15.947 12.1541C16.0937 12.2275 16.207 12.3475 16.267 12.4941L20.0604 20.9275C20.2137 21.2741 20.0737 21.6808 19.7404 21.8541L14.9137 24.3941C12.9537 25.4341 10.8137 23.3341 11.807 21.3475Z"
                    fill="white" />
                  <path opacity="0.4"
                    d="M20.8738 20.4003L18.3871 14.8803C18.1071 14.2603 18.7738 13.6336 19.3738 13.9536L23.2205 15.9803C24.8538 16.8403 24.8538 19.1736 23.2205 20.0336L21.8605 20.747C21.4938 20.9336 21.0471 20.7803 20.8738 20.4003Z"
                    fill="white" />
                  <defs>
                    <linearGradient id="paint0_linear_1555_26595" x1="36" y1="18" x2="-1.90588" y2="18"
                      gradientUnits="userSpaceOnUse">
                      <stop stop-color="#EF4A61" />
                      <stop offset="1" stop-color="#F27A6C" />
                    </linearGradient>
                  </defs>
                </svg>
              </div>
              <div *ngIf="
                            isPromptApiInProgress ||
                            !promptSearchParams ||
                            promptSearchParams.length == 0" style="padding-top: 21px; padding-right: 13px;">
                <svg width="28" height="28" viewBox="0 0 36 36" fill="none">
                  <rect width="36" height="36" rx="18" fill="grey" />
                  <path
                    d="M11.807 21.3475L12.967 19.0275C13.287 18.3808 13.287 17.6275 12.967 16.9808L11.807 14.6541C10.8137 12.6675 12.9537 10.5675 14.9204 11.6075L15.947 12.1541C16.0937 12.2275 16.207 12.3475 16.267 12.4941L20.0604 20.9275C20.2137 21.2741 20.0737 21.6808 19.7404 21.8541L14.9137 24.3941C12.9537 25.4341 10.8137 23.3341 11.807 21.3475Z"
                    fill="white" />
                  <path opacity="0.4"
                    d="M20.8738 20.4003L18.3871 14.8803C18.1071 14.2603 18.7738 13.6336 19.3738 13.9536L23.2205 15.9803C24.8538 16.8403 24.8538 19.1736 23.2205 20.0336L21.8605 20.747C21.4938 20.9336 21.0471 20.7803 20.8738 20.4003Z"
                    fill="white" />
                  <defs>
                    <linearGradient id="paint0_linear_1555_26595" x1="36" y1="18" x2="-1.90588" y2="18"
                      gradientUnits="userSpaceOnUse">
                      <stop stop-color="#EF4A61" />
                      <stop offset="1" stop-color="#F27A6C" />
                    </linearGradient>
                  </defs>
                </svg>
              </div>
            </div>
          </div>
          <!-- Chat Screen -->
          <div *ngIf="isChatVisible" class="p-3 chat-wrapper">
            <div class="chat-container p-1" #chatScrollContainer>
              <!-- Current Thread Data -->
              <ng-container *ngFor="let item of currentThreadData; let i = index">
                <div class="prompt-outer-ui">
                  <div class="prompt-column-align">
                    <div class="prompt-ui">
                      <div class="prompt-text">{{ item.prompt }}</div>
                      <app-user-image [oid]="oid" imgWidth="32px" imgHeight="32px"></app-user-image>
                    </div>
                  </div>
                </div>
                <ng-container *ngIf="!item?.isError">
                  <div *ngIf="item?.isLoading" class="compiling-ui pt-3">
                    <img class="ai-icon" [src]="data?.aiThemeConfig['AI-ICON-SHORT']" />
                    <div class="chat-loader">
                      <img width="44px" height="44px" [src]="data?.aiThemeConfig['CHATBOT-CHAT-LOADER']" />
                      <span class="compile-text">{{ loaderText }}</span>
                    </div>
                  </div>
                  <div *ngIf="item?.isReady" class="compiling-ui">
                    <img class="ai-icon" [src]="data?.aiThemeConfig['AI-ICON-SHORT']" />
                    <div class="chat-loader">
                      <!-- <img width="32px" height="32px" [src]="data?.aiThemeConfig['AI-ICON-SHORT']" /> -->
                      <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M10 0C4.48 0 0 4.48 0 10C0 15.52 4.48 20 10 20C15.52 20 20 15.52 20 10C20 4.48 15.52 0 10 0ZM8 15L3 10L4.41 8.59L8 12.17L15.59 4.58L17 6L8 15Z" fill="#52C41A"/>
                      </svg>
                      <span class="compile-text">Ready</span>
                    </div>
                  </div>
                  <div *ngIf="!item?.isLoading && !item?.isReady" class="response-ui">
                    <img class="ai-icon" [src]="data?.aiThemeConfig['AI-ICON-SHORT']" />
                    <div class="response-data">
                      <div class="response-text" [innerHTML]="(item?.response | markdownCustom)">
                      </div>
                      <div class="icons" *ngIf="item.response">
                        <div class="right-icons">
                          <!-- Regenerate Icon -->
                          <div *ngIf="!hideRegenerate">
                            <svg class="icon" width="14" height="15" viewBox="0 0 14 15" fill="none">
                              <path
                                d="M5.43583 14.5391C4.05875 14.1898 2.93354 13.4604 2.06021 12.3508C1.18674 11.2413 0.75 9.95745 0.75 8.49911C0.75 7.75023 0.880625 7.03418 1.14187 6.35099C1.40312 5.66766 1.76875 5.04078 2.23875 4.47036C2.35417 4.34106 2.49361 4.27425 2.65708 4.26995C2.82056 4.26564 2.97118 4.33245 3.10896 4.47036C3.22438 4.58564 3.28424 4.72634 3.28854 4.89245C3.29271 5.0587 3.23715 5.21495 3.12188 5.3612C2.75646 5.79717 2.47813 6.2828 2.28688 6.81807C2.09563 7.35335 2 7.9137 2 8.49911C2 9.6455 2.3459 10.665 3.03771 11.5577C3.72951 12.4503 4.61493 13.0429 5.69396 13.3356C5.83174 13.3752 5.945 13.4527 6.03375 13.5681C6.12236 13.6834 6.16667 13.8099 6.16667 13.9479C6.16667 14.1562 6.09479 14.3202 5.95104 14.4397C5.80743 14.5595 5.63569 14.5926 5.43583 14.5391ZM8.56417 14.5552C8.36431 14.6086 8.19257 14.5734 8.04896 14.4493C7.90521 14.3254 7.83333 14.1593 7.83333 13.951C7.83333 13.8217 7.87764 13.6994 7.96625 13.5841C8.055 13.4687 8.16826 13.3886 8.30604 13.3437C9.37979 13.0156 10.2639 12.4115 10.9583 11.5312C11.6528 10.6508 12 9.64009 12 8.49911C12 7.11023 11.5139 5.92967 10.5417 4.95745C9.56944 3.98523 8.38889 3.49911 7 3.49911H6.70521L7.39104 4.18495C7.51174 4.30578 7.57208 4.45217 7.57208 4.62411C7.57208 4.7962 7.51174 4.94259 7.39104 5.06328C7.27035 5.18398 7.12396 5.24432 6.95188 5.24432C6.77993 5.24432 6.63361 5.18398 6.51292 5.06328L4.85104 3.40141C4.77299 3.32335 4.71792 3.24106 4.68583 3.15453C4.65389 3.068 4.63792 2.97453 4.63792 2.87411C4.63792 2.7737 4.65389 2.68023 4.68583 2.5937C4.71792 2.50717 4.77299 2.42488 4.85104 2.34682L6.51292 0.684948C6.63361 0.564254 6.77993 0.503906 6.95188 0.503906C7.12396 0.503906 7.27035 0.564254 7.39104 0.684948C7.51174 0.805781 7.57208 0.952171 7.57208 1.12411C7.57208 1.2962 7.51174 1.44259 7.39104 1.56328L6.70521 2.24911H7C8.74361 2.24911 10.2212 2.85488 11.4327 4.06641C12.6442 5.27793 13.25 6.7555 13.25 8.49911C13.25 9.94356 12.8119 11.2213 11.9358 12.3324C11.0597 13.4436 9.93583 14.1845 8.56417 14.5552Z"
                                fill="#1C1B1F" />
                            </svg>
                          </div>
                          <!-- Like Icon -->
                          <svg *ngIf="item.is_liked == true || item.is_liked == null" (click)="
                                                        likeAndDislikeResponse(
                                                          item.is_liked === true ? null : true,
                                                          item,
                                                          i
                                                        )
                                                      " [ngStyle]="{
                                                        fill: item.is_liked === true ? '#1C1B1F' : '#7D838B',
                                                        cursor: item.is_liked === true ? 'default' : 'pointer',
                                                        'pointer-events': item.is_liked === true ? 'none' : 'auto'
                                                      }" width="14" height="13" viewBox="0 0 14 13">
                            <path
                              d="M12.7942 4.33308C13.1113 4.33308 13.3914 4.45469 13.6345 4.69791C13.8777 4.94102 13.9993 5.22114 13.9993 5.53825V6.61508C13.9993 6.68508 13.9903 6.76008 13.9723 6.84008C13.9545 6.91997 13.9344 6.99458 13.9122 7.06391L12.0015 11.5722C11.9061 11.7855 11.746 11.9656 11.5212 12.1126C11.2964 12.2596 11.0618 12.3331 10.8175 12.3331H3.80702V4.33308L7.68518 0.488247C7.81774 0.355803 7.97118 0.276748 8.14552 0.251081C8.31985 0.225414 8.4869 0.255303 8.64668 0.340748C8.80657 0.426303 8.92368 0.547692 8.99802 0.704914C9.07235 0.862136 9.08818 1.02497 9.04552 1.19341L8.32752 4.33308H12.7942ZM4.80701 4.75875V11.3331H10.8198C10.8668 11.3331 10.9149 11.3202 10.964 11.2946C11.0132 11.2689 11.0507 11.2262 11.0763 11.1664L12.9993 6.66641V5.53825C12.9993 5.47836 12.9801 5.42919 12.9417 5.39075C12.9032 5.3523 12.8541 5.33308 12.7942 5.33308H7.06352L7.89935 1.67925L4.80701 4.75875ZM1.87118 12.3331C1.53974 12.3331 1.25602 12.2151 1.02002 11.9791C0.784016 11.7431 0.666016 11.4594 0.666016 11.1279V5.53825C0.666016 5.2068 0.784016 4.92308 1.02002 4.68708C1.25602 4.45108 1.53974 4.33308 1.87118 4.33308H3.80702V5.33308H1.87118C1.81129 5.33308 1.76213 5.3523 1.72368 5.39075C1.68524 5.42919 1.66602 5.47836 1.66602 5.53825V11.1279C1.66602 11.1878 1.68524 11.237 1.72368 11.2754C1.76213 11.3139 1.81129 11.3331 1.87118 11.3331H3.80702V12.3331H1.87118Z" />
                          </svg>
                          <!-- Dislike Icon -->
                          <svg *ngIf="item.is_liked !== true" (click)="
                                                        likeAndDislikeResponse(
                                                          item.is_liked === false ? null : false,
                                                          item,
                                                          i
                                                        )
                                                      " [ngStyle]="{
                                                        fill: item.is_liked === false ? '#1C1B1F' : '#7D838B',
                                                        cursor: item.is_liked === false ? 'default' : 'pointer',
                                                        'pointer-events': item.is_liked === false ? 'none' : 'auto'
                                                      }" width="14" height="13" viewBox="0 0 14 13">
                            <path
                              d="M1.20517 8C0.888056 8 0.607944 7.87839 0.364833 7.63517C0.121611 7.39206 0 7.11194 0 6.79483V5.718C0 5.648 0.00899998 5.573 0.027 5.493C0.0448889 5.41311 0.0649445 5.3385 0.0871667 5.26917L1.99783 0.760834C2.09328 0.547612 2.25339 0.3675 2.47817 0.2205C2.70294 0.0734997 2.9375 0 3.18183 0H10.1923V8L6.31417 11.8448C6.18161 11.9773 6.02817 12.0563 5.85383 12.082C5.6795 12.1077 5.51244 12.0778 5.35267 11.9923C5.19278 11.9068 5.07567 11.7854 5.00133 11.6282C4.927 11.4709 4.91117 11.3081 4.95383 11.1397L5.67183 8H1.20517ZM9.19233 7.57433V1H3.1795C3.1325 1 3.08444 1.01283 3.03533 1.0385C2.98611 1.06417 2.94867 1.10689 2.923 1.16667L1 5.66667V6.79483C1 6.85472 1.01922 6.90389 1.05767 6.94233C1.09611 6.98078 1.14528 7 1.20517 7H6.93583L6.1 10.6538L9.19233 7.57433ZM12.1282 0C12.4596 0 12.7433 0.118 12.9793 0.354C13.2153 0.59 13.3333 0.873722 13.3333 1.20517V6.79483C13.3333 7.12628 13.2153 7.41 12.9793 7.646C12.7433 7.882 12.4596 8 12.1282 8H10.1923V7H12.1282C12.1881 7 12.2372 6.98078 12.2757 6.94233C12.3141 6.90389 12.3333 6.85472 12.3333 6.79483V1.20517C12.3333 1.14528 12.3141 1.09611 12.2757 1.05767C12.2372 1.01922 12.1881 1 12.1282 1H10.1923V0H12.1282Z" />
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>
                </ng-container>
                <ng-container *ngIf="item?.isError">
                  <div class="response-ui">
                    <img class="ai-icon" [src]="data?.aiThemeConfig['AI-ICON-SHORT']" />
                    <div class="response-data" *ngIf="!item?.isQuestion; else showMarkdown" [innerHTML]="errorMessage(item?.error_code) | safeHtml"></div>
                    <ng-template #showMarkdown>
                      <div class="response-data">
                      <div class="response-text" [innerHTML]="item?.response | markdownCustom"></div>
                      </div>
                    </ng-template>
                  </div>
                </ng-container>
              </ng-container>
            </div>
            <!-- Input Bar -->
            <div class="search-ui-chat">
              <div class="search-bar-chat" (mouseenter)="setTypingTrue()">
                <textarea type="text" (blur)="clearSearchInput()" [(ngModel)]="promptSearchParams" placeholder=""
                (keydown.enter)="onSearchPrompt($event, promptSearchParams); adjustCursorPosition($event)" (input)="setTypingTrue();"></textarea>
              </div>
              <div *ngIf="
                                          !isPromptApiInProgress &&
                                          promptSearchParams &&
                                          promptSearchParams.length > 0
                                        " class="svg-icon" (click)="onSearchPrompt($event,promptSearchParams)
                                        " style="padding-top: 25px; padding-right: 13px;">
                <svg width="28" height="28" viewBox="0 0 36 36" fill="none">
                  <rect width="36" height="36" rx="18" fill="url(#paint0_linear_1555_26595)" />
                  <path
                    d="M11.807 21.3475L12.967 19.0275C13.287 18.3808 13.287 17.6275 12.967 16.9808L11.807 14.6541C10.8137 12.6675 12.9537 10.5675 14.9204 11.6075L15.947 12.1541C16.0937 12.2275 16.207 12.3475 16.267 12.4941L20.0604 20.9275C20.2137 21.2741 20.0737 21.6808 19.7404 21.8541L14.9137 24.3941C12.9537 25.4341 10.8137 23.3341 11.807 21.3475Z"
                    fill="white" />
                  <path opacity="0.4"
                    d="M20.8738 20.4003L18.3871 14.8803C18.1071 14.2603 18.7738 13.6336 19.3738 13.9536L23.2205 15.9803C24.8538 16.8403 24.8538 19.1736 23.2205 20.0336L21.8605 20.747C21.4938 20.9336 21.0471 20.7803 20.8738 20.4003Z"
                    fill="white" />
                  <defs>
                    <linearGradient id="paint0_linear_1555_26595" x1="36" y1="18" x2="-1.90588" y2="18"
                      gradientUnits="userSpaceOnUse">
                      <stop stop-color="#EF4A61" />
                      <stop offset="1" stop-color="#F27A6C" />
                    </linearGradient>
                  </defs>
                </svg>
              </div>
              <div *ngIf="
                                          isPromptApiInProgress ||
                                          !promptSearchParams ||
                                          promptSearchParams.length == 0
                                        " style="padding-top: 25px; padding-right: 13px;">
                <svg width="28" height="28" viewBox="0 0 36 36" fill="none">
                  <rect width="36" height="36" rx="18" fill="grey" />
                  <path
                    d="M11.807 21.3475L12.967 19.0275C13.287 18.3808 13.287 17.6275 12.967 16.9808L11.807 14.6541C10.8137 12.6675 12.9537 10.5675 14.9204 11.6075L15.947 12.1541C16.0937 12.2275 16.207 12.3475 16.267 12.4941L20.0604 20.9275C20.2137 21.2741 20.0737 21.6808 19.7404 21.8541L14.9137 24.3941C12.9537 25.4341 10.8137 23.3341 11.807 21.3475Z"
                    fill="white" />
                  <path opacity="0.4"
                    d="M20.8738 20.4003L18.3871 14.8803C18.1071 14.2603 18.7738 13.6336 19.3738 13.9536L23.2205 15.9803C24.8538 16.8403 24.8538 19.1736 23.2205 20.0336L21.8605 20.747C21.4938 20.9336 21.0471 20.7803 20.8738 20.4003Z"
                    fill="white" />
                  <defs>
                    <linearGradient id="paint0_linear_1555_26595" x1="36" y1="18" x2="-1.90588" y2="18"
                      gradientUnits="userSpaceOnUse">
                      <stop stop-color="#EF4A61" />
                      <stop offset="1" stop-color="#F27A6C" />
                    </linearGradient>
                  </defs>
                </svg>
              </div>
            </div>
          </div>
          <!-- Image Content -->
          <img class="bg-image-1" [src]="data?.aiThemeConfig['CHATBOT-BG-ICON']" />
          <img class="bg-image-2" [src]="data?.aiThemeConfig['CHATBOT-BG-ICON']" />
          <div class="imp-text">Kais can make mistakes. Check important info.</div>
        </ng-container>
      </div>

    </div>
  </div>
</div>

<!-- History Menu -->
<mat-menu #historyMenu="matMenu">
  <ng-template matMenuContent let-item="item">
    <div class="menu-content">
      <button class="menu-item" mat-menu-item (click)="renameThread()">
        <svg class="menu-icon" height="20px" viewBox="0 -960 960 960" width="20px" fill="#a8acb2">
          <path
            d="M216-216h51l375-375-51-51-375 375v51Zm-72 72v-153l498-498q11-11 23.84-16 12.83-5 27-5 14.16 0 27.16 5t24 16l51 51q11 11 16 24t5 26.54q0 14.45-5.02 27.54T795-642L297-144H144Zm600-549-51-51 51 51Zm-127.95 76.95L591-642l51 51-25.95-25.05Z" />
        </svg>
        Rename
      </button>
      <mat-divider class="menu-divider"></mat-divider>
      <button class="menu-item" mat-menu-item (click)="deleteThread()">
        <svg class="menu-icon" height="20px" viewBox="0 -960 960 960" width="20px" fill="#a8acb2">
          <path
            d="M312-144q-29.7 0-50.85-21.15Q240-186.3 240-216v-480h-48v-72h192v-48h192v48h192v72h-48v479.57Q720-186 698.85-165T648-144H312Zm336-552H312v480h336v-480ZM384-288h72v-336h-72v336Zm120 0h72v-336h-72v336ZM312-696v480-480Z" />
        </svg>
        Delete
      </button>
      <mat-divider class="menu-divider"></mat-divider>
      <button class="menu-item" mat-menu-item (click)="pinOrUnpinThread()">
        <svg
          *ngIf="selectedHistoryThreadData?.is_pinned"
          class="menu-icon"
          height="20px"
          viewBox="0 -960 960 960"
          width="20px"
          fill="#a8acb2"
        >
          <path
            d="M672-816v72h-48v307l-72-72v-235H408v91l-90-90-30-31v-42h384ZM480-48l-36-36v-228H240v-72l96-96v-42.46L90-768l51-51 678 679-51 51-222-223h-30v228l-36 36ZM342-384h132l-66-66-66 66Zm137-192Zm-71 126Z"
          />
        </svg>
        <svg
          *ngIf="!selectedHistoryThreadData?.is_pinned"
          class="menu-icon"
          height="20px"
          viewBox="0 -960 960 960"
          width="20px"
          fill="#a8acb2"
        >
          <path
            d="m624-480 96 96v72H516v228l-36 36-36-36v-228H240v-72l96-96v-264h-48v-72h384v72h-48v264Zm-282 96h276l-66-66v-294H408v294l-66 66Zm138 0Z"
          />
        </svg>
        {{ selectedHistoryThreadData?.is_pinned ? "Unpin" : "Pin" }}
      </button>
    </div>
  </ng-template>
</mat-menu>