// Initial Container
.bg-container {
  display: flex;
}

.text-area{
  height: calc(100vh - var(--chatBotAdjustedHeight) - 249px);
  display: flex;
}

.main-container{
  height: calc(100vh - var(--chatBotAdjustedHeight));
  width: var(--chatBotWidth);
  overflow: hidden;
}

// Footer Text
.imp-text {
  font-family: var(--FontFamily);
  font-size: 10px;
  font-weight: 500;
  color: #b9c0ca;
  text-align: center;
}

// ai-icon throughtout UI
.ai-icon {
  width: 32px;
  height: 32px;
}

// Response Compiling UI
.compiling-ui {
  display: flex;
  gap: 8px;

  .ai-icon {
    width: 32px;
    height: 32px;
  }

  .chat-loader {
    display: flex;
    align-items: center;
    justify-content: center;
    width: fit-content;
    gap: 8px;
    height: 44px;
    border: 0.5px solid #e8e9ee;
    background-color: #f6f6f6;
    padding: 0px 10px;
    border-radius: 0px 8px 8px 8px;

    .compile-text {
      font-family: var(--FontFamily);
      font-size: 12px;
      font-weight: 400;
      color: #515965;
    }
  }
}

.bg-image-1 {
  position: absolute;
  top: 85px;
  right: 0px;
  height: 170px;
  width: 60px;
}

.bg-image-2 {
  position: absolute;
  bottom: -20px;
  height: 170px;
  width: 60px;
  transform: rotate(180deg);
}

// .response-data {
//   display: flex;
//   flex-direction: column;
//   gap: 8px;
//   padding: 0px 8px;
//   width: 100%;
//   overflow: hidden;

//   .response-text {
//     font-family: var(--FontFamily);
//     font-size: 12px;
//     font-weight: 400;
//     color: #45546e;
//     overflow: auto;
//   }

//   .icons {
//     display: flex;
//     align-items: center;
//     justify-content: space-between;
//     border: 1px solid #DADCE2;
//     border-radius: 6px;
//     padding: 6px 16px;
//     gap: 8px;
//     width: fit-content;

//     .left-icons {
//       display: flex;
//       align-items: center;
//       gap: 6px;

//       .icon {
//         font-size: 17px;
//         width: 17px;
//         height: 17px;
//         color: #1c1b1f;
//         cursor: pointer;
//       }

//       .text {
//         font-family: var(--FontFamily);
//         font-size: 12px;
//         font-weight: 500;
//         color: #45546e;
//       }
//     }

//     .right-icons {
//       display: flex;
//       align-items: center;
//       gap: 10px;

//       .icon {
//         cursor: pointer;
//       }

//       .tick-icon-copy {
//         max-width: 14px;
//       }
//     }
//   }

// }

// Response UI

.response-ui {
  display: flex;
  justify-content: space-between;
  gap: 8px;
  width: 98%;
  // height: calc(100vh - var(--chatBotAdjustedHeight) - 125px);
  padding-top: 9px;
  padding-bottom: 8px;

  .ai-icon {
    width: 32px;
    height: 32px;
  }

  .response-data {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 0px 8px;
    width: 100%;
    overflow: hidden;

    .response-text {
      font-family: var(--FontFamily);
      font-size: 14px;
      font-weight: 400;
      color: #45546e;
      overflow: auto;
      line-height: 24px;
    }

    .icons {
      display: flex;
      align-items: center;
      justify-content: space-between;
      border: 1px solid #DADCE2;
      border-radius: 6px;
      padding: 6px 16px;
      gap: 8px;
      width: fit-content;

      .left-icons {
        display: flex;
        align-items: center;
        gap: 6px;

        .icon {
          font-size: 17px;
          width: 17px;
          height: 17px;
          color: #1c1b1f;
          cursor: pointer;
        }

        .text {
          font-family: var(--FontFamily);
          font-size: 12px;
          font-weight: 500;
          color: #45546e;
        }
      }

      .right-icons {
        display: flex;
        align-items: center;
        gap: 10px;

        .icon {
          cursor: pointer;
        }

        .tick-icon-copy {
          max-width: 14px;
        }
      }
    }

  }

  ::ng-deep .align-items-same-line {
    display: flex;
    flex-direction: row;
    gap: 4px;
    align-items: center;

    .error-text-header {
      font-family: var(--FontFamily);
      font-weight: 700;
      font-size: 14px;
      color: #FA8C16;
    }

  }

  ::ng-deep.error-sub-text {
    font-family: var(--FontFamily);
    font-weight: 400;
    font-size: 14px;
    color: #272A47;
  }
}

// Chat Container
.chat-container {
  height: calc(100vh - var(--chatBotAdjustedHeight) - 187px);
  overflow-y: scroll;
}

.chat-container::-webkit-scrollbar {
  display: none;          
}

// Chat Outer container
.chat-wrapper {
  display: flex;
  flex-direction: column;
  height: calc(100vh - var(--chatBotAdjustedHeight) -60px);
  overflow: hidden;
  // width: 79%;
}

// Chat Container Prompt UI
.prompt-outer-ui {
  display: flex;
  justify-content: end;

  .prompt-column-align {
    display: flex;
    align-items: end;
    flex-direction: column;
    gap: 4px;
    width: 100%;

    .prompt-ui {
      display: flex;
      align-items: center;
      justify-content: end;
      gap: 8px;
      width: 85%;

      .prompt-text {
        font-family: var(--FontFamily);
        font-size: 14px;
        font-weight: 400;
        color: #45546E;
        border-radius: 8px;
        padding: 10px;
        max-width: 100%;
        background-color: #F6F6F6;
        text-align: justify;
        word-wrap: break-word;
        border: 1px solid #E8E9EE;
        line-height: 24px;
      }
    }

    .error-msg {
      display: flex;
      align-items: center;
      gap: 4px;
      font-family: var(--FontFamily);
      font-size: 12px;
      font-weight: 400;
      color: #45546e;

      .icon {
        font-size: 16px;
        width: 16px;
        height: 16px;
        color: #ee4961;
      }
    }
  }
}



.prompt-library-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: calc(100vh - var(--chatBotAdjustedHeight) - 80px);
  overflow: hidden;
  position: relative;
  z-index: 1;

  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .prompt-title {
      display: flex;
      align-items: center;
      gap: 12px;

      .prompt-title-icon {
        font-size: 16px;
        width: 16px;
        height: 16px;
        color: #45546e;
        cursor: pointer;
      }

      .prompt-title-text {
        font-family: var(--FontFamily);
        font-size: 16px;
        font-weight: 600;
        color: #ee4961;
      }
    }

    .search-bar {
      display: flex;
      align-items: center;
      gap: 4px;
      border: 1px solid #b9c0ca;
      border-radius: 8px;
      width: 25%;
      padding: 2px 12px;
      height: 28px;
      margin-right: 35px;

      input {
        height: 100%;
        width: 100%;
        font-family: var(--FontFamily);
        font-size: 12px;
        font-weight: 400;
        color: #45546e;
        outline: none;
        border: none;
      }

      input::placeholder {
        font-family: var(--FontFamily);
        font-size: 12px;
        font-weight: 400;
        color: #b9c0ca;
      }
    }
  }

  .content {
    display: flex;
    align-items: center;
    width: 100%;
    gap: 2%;

    .categories {
      display: flex;
      flex-direction: column;
      height: calc(var(--kebsChatbotContentHeight) - 55px);
      border: 0.5px solid #e8e9ee;
      border-radius: 8px;
      width: 20%;
      padding: 8px;
      gap: 8px;

      .header {
        display: flex;
        justify-content: start;
        align-items: center;
        gap: 8px;
        padding-bottom: 8px;
        border-bottom: 0.5px solid #dadce2;
        margin-bottom: 8px;

        .categories-text {
          font-family: var(--FontFamily);
          font-size: 12px;
          font-weight: 400;
          color: #111434;
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
          max-width: 70%;
        }

        .count {
          background-color: #45546e;
          padding: 2px 6px;
          border-radius: 10px;
          font-family: var(--FontFamily);
          font-size: 10px;
          font-weight: 500;
          color: #fff;
        }
      }

      .categories-list {
        display: flex;
        flex-direction: column;
        gap: 8px;
        height: calc(var(--kebsChatbotContentHeight) - 68px);
        overflow-y: auto;
        padding-right: 2%;

        .category-list {
          display: flex;
          align-items: center;
          gap: 8px;
          cursor: pointer;
          border-radius: 4px;
          padding: 4px;

          .text {
            font-family: var(--FontFamily);
            font-size: 12px;
            font-weight: 400;
            color: #111434;
            text-overflow: ellipsis;
            white-space: nowrap;
            overflow: hidden;
            max-width: 70%;
          }

          .count {
            padding: 2px 6px;
            border-radius: 10px;
            font-family: var(--FontFamily);
            font-size: 10px;
            font-weight: 500;
            color: linear-gradient(270deg, #ef4a61 0%, #f27a6c 105.29%);
            background: linear-gradient(270deg,
                rgba(239, 74, 97, 0.1) 0%,
                rgba(242, 122, 108, 0.1) 105.29%);
          }
        }

        .selected-category-list {
          background: linear-gradient(270deg,
              rgba(239, 74, 97, 0.1) 0%,
              rgba(242, 122, 108, 0.1) 105.29%);

          .selected-count {
            color: #fff;
            background: linear-gradient(270deg,
                #ef4a61 0%,
                #f27a6c 105.29%);
          }
        }
      }

      .categories-list::-webkit-scrollbar {
        width: 6px !important;
        height: 6px !important;
      }

      .categories-list::-webkit-scrollbar-thumb {
        min-height: 40px !important;
        max-height: 40px !important;
      }
    }

    .category-content {
      display: flex;
      flex-direction: column;
      height: calc(var(--kebsChatbotContentHeight) - 55px);
      gap: 6px;
      width: 77%;
      padding-right: 1%;
      overflow-y: auto;

      .single-prompt {
        display: flex;
        flex-direction: row;
        align-items: start;
        justify-content: space-between;
        gap: 16px;
        border: 0.5px solid #dadce2;
        border-radius: 8px;
        padding: 8px;
        cursor: pointer;

        .text {
          font-family: var(--FontFamily);
          font-size: 12px;
          font-weight: 400;
          color: #8b95a5;
          text-align: justify;
          line-height: 2.5;
        }

        .copy {
          height: 10px;
          display: none;
          align-items: center;
          justify-content: end;
          gap: 4px;
          font-family: var(--FontFamily);
          font-size: 8px;
          font-weight: 500;
          color: #ef4a61;
        }
      }

      .single-prompt:hover {
        border: 0.5px solid #ef4a61;
        background: linear-gradient(270deg,
            rgba(239, 74, 97, 0.05) 0%,
            rgba(242, 122, 108, 0.05) 105.29%);
      }

      .single-prompt:hover .copy {
        display: flex;
      }
    }

    .category-content::-webkit-scrollbar {
      width: 6px !important;
      height: 6px !important;
    }

    .category-content::-webkit-scrollbar-thumb {
      min-height: 40px !important;
      max-height: 40px !important;
    }

    .category-content-empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: calc(var(--kebsChatbotContentHeight) - 20px);
      gap: 6px;
      width: 63%;
      padding-right: 1%;
      overflow: hidden;
      font-family: var(--FontFamily);
      font-size: 14px;
      font-weight: 700;
      color: #45546e;
    }
  }
}

// Input bar UI
.search-ui {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  border: 1px solid #b9c0ca;
  border-radius: 60px;
  height: 36px;
  width: 100%;
  padding-right: 4px;
  z-index: 10;
  position: relative;
  background: #ffff;

  .search-bar {
    width: -webkit-fill-available;
    height: 100%;
    padding: 4px 8px;

    input {
      height: 100%;
      width: 100%;
      font-family: var(--FontFamily);
      font-size: 12px;
      font-weight: 400;
      color: #45546e;
      outline: none;
      border: none;
    }

    input::placeholder {
      font-family: var(--FontFamily);
      font-size: 12px;
      font-weight: 400;
      color: #b9c0ca;
    }
  }
}

// New Chat button in history
.new-chat-btn {
  background-color: #EE49611A;
  color: #EE4961;
  border: none;
  border-radius: 10px;
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  outline: none;
  transition: background-color 0.3s ease;
  width: 140px;
  height: 48px;
  font-family: var(--FontFamily);
}

// Textarea UI in home page
.search-ui-new {
  display: flex;
  flex-direction: column; // stack textarea and icon vertically
  border: 1px solid #b9c0ca;
  border-radius: 32px; // reduced from 60px for better vertical look
  width: 100%;
  padding: 8px;
  z-index: 10;
  position: relative;
  background: #fff;

  .search-bar-new {
    overflow-y: auto;
    width: 100%;
    padding: 4px 8px;

    textarea {
      max-height: 120px;
      width: 100%;
      resize: none;
      overflow-y: auto;
      font-family: var(--FontFamily);
      font-size: 16px;
      font-weight: 400;
      color: #45546e;
      outline: none;
      border: none;
    }
  }
}

// Grettings
.header-content-alignment {
  display: flex;
  gap: 6px;
  align-items: center;

  .ai-img-header {
    height: 60.12px;
    width: 58.5px;
  }
}

.align-items-column {
  display: flex;
  flex-direction: column;
  row-gap: 0px;

  .main-text {
    font-family: var(--FontFamily);
    font-weight: 600;
    font-size: 22px;
    color: #ee4961;
    padding-bottom: 9px;
    background: linear-gradient(270deg,
        #ffc12f -5.06%,
        #f16567 31.08%,
        #f16567 100.33%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;

    &.header-content {
      font-weight: 700;
      font-size: 21px;
      height: 30px;
    }
  }

  .sub-text {
    font-family: var(--FontFamily);
    font-weight: 400;
    font-size: 17px;
    color: #b9c0ca;
    height: 20px;

    &.header-content {
      font-weight: 700;
      font-size: 23px;
    }
  }
}

// History
.history-container {
  display: flex;
  flex-direction: column;
  width: 20vw;
  height: calc(100vh - var(--chatBotAdjustedHeight));
  border-right: 1px solid #e8e9ee;
  background-color: #FFF3F4;
  

  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0px 18px;
    height: 59px;
    padding-top: 20px;

    .main-title {
      font-family: var(--FontFamily);
      font-weight: 700;
      font-size: 14px;
      color: #111434;
    }

    .svg-icon {
      cursor: pointer;
    }

    .ai-icon-history{
      width: 69px;
      height: 32px;
    }
  }

  .search-ui-history {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 8px;
    border: 0.5px solid #d4d6d8;
    border-radius: 8px;
    height: 28px;
    padding-right: 4px;
    margin: 28px 16px;
    background-color: white;

    .search-bar {
      width: -webkit-fill-available;
      height: 100%;
      padding: 2px 6px;

      input {
        height: 100%;
        width: 100%;
        font-family: var(--FontFamily);
        font-size: 10px;
        font-weight: 400;
        color: #45546e;
        outline: none;
        border: none;
        background-color: #f7f9fb;
      }

      input::placeholder {
        font-family: var(--FontFamily);
        font-size: 10px;
        font-weight: 400;
        color: #d4d6d8;
      }
    }

    .svg-icon {
      cursor: pointer;
    }

    .close-icon {
      height: 12px;
      width: 12px;
      font-size: 12px;
      color: #7d838b;
      cursor: pointer;
    }
  }


  .no-history {
    font-family: var(--FontFamily);
    font-size: 10px;
    font-weight: 400;
    color: #7d838b;
    width: 100%;
    text-align: center;
  }

  .loading-state {
    display: flex;
    flex-direction: column;
    // height: var(--kebsChatbotHistoryContentHeight);
    overflow: hidden;
    padding: 0px 8px 0px 16px;
    margin-right: 8px;
    gap: 10px;

    .loader {
      min-height: 24px;
      width: 100%;
      border-radius: 6px;
      background: linear-gradient(90deg,
          #cdcdcd -24.18%,
          #f0f0f0 50.26%,
          #efefef 114.84%);
    }
  }

  .content {
    display: flex;
    flex-direction: column;
    // height: var(--kebsChatbotHistoryContentHeight);
    overflow-y: auto;
    padding: 0px 8px 0px 16px;
    margin-right: 8px;
    gap: 10px;

    .divider {
      color: #d4d6d8;
    }

    .single-date-log {
      display: flex;
      flex-direction: column;
      gap: 4px;

      .date {
        display: flex;
        align-items: center;
        gap: 8px;
        font-family: var(--FontFamily);
        font-weight: 700;
        font-size: 12px;
        color: #515965;
      }

      .pin-icon {
        font-size: 14px;
        width: 14px;
        height: 14px;
      }

      .single-history {
        display: flex;
        align-items: center;
        justify-content: space-between;
        min-height: 24px;
        gap: 8px;
        padding: 4px 4px;
        // border: 1.5px solid #f7f9fb;

        &.thread-selected {
          border-radius: 4px;
          background-color: #EE49611A;
          // border: 1.5px solid #f7f9fb;
        }

        .history-text {
          font-family: var(--FontFamily);
          font-weight: 400;
          font-size: 12px;
          color: #515965;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          cursor: pointer;
          width: 99%;
        }

        .selected-history-text {
          font-family: var(--FontFamily);
          font-weight: 700;
          font-size: 12px;
          color: #EE4961;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          cursor: pointer;
          width: 99%;
        }

        .inline-edit {
          width: 100%;
          height: 24px;

          input {
            height: 100%;
            width: 100%;
            font-family: var(--FontFamily);
            font-size: 12px;
            font-weight: 400;
            color: #515965;
            outline: none;
            border: 0.7px solid #1890ff;
            border-radius: 4px;
          }

          input::placeholder {
            font-family: var(--FontFamily);
            font-size: 10px;
            font-weight: 400;
            color: #6e7b8f;
          }
        }

        .svg-icon {
          cursor: pointer;
        }
      }

      // .single-history:hover {
      //   border: 1.5px solid #e8e9ee;
      //   border-radius: 4px;
      // }
    }
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 8px;
    height: calc(var(--kebsChatbotContentHeight) - 40px);

    .gif {
      height: 80px;
      width: 80px;
    }

    .carousel-text {
      position: relative;
      height: 50px;
      width: 100%;
      overflow: hidden;
    }

    .text-content {
      position: absolute;
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      transition: transform 0.5s ease-in, opacity 0.5s ease-in;
      position: absolute;
      opacity: 1;
      transition: opacity 2s ease;

      .main-text {
        font-family: var(--FontFamily);
        font-size: 14px;
        font-weight: 400;
        color: #7d838b;
      }

      .sub-text {
        font-family: var(--FontFamily);
        font-size: 14px;
        font-weight: 600;
        color: #ee4961;
      }
    }

    .slide {
      animation: slideUp 2s ease forwards;
    }

    /* Keyframes to move text from bottom to top */
    @keyframes slideUp {
      0% {
        transform: translateY(100%); /* Start from bottom */
        opacity: 0;
      }
      20% {
        transform: translateY(0); /* Move to center */
        opacity: 1;
      }
      80% {
        transform: translateY(0); /* Stay in center */
        opacity: 1;
      }
      100% {
        transform: translateY(-100%); /* Move out from top */
        opacity: 0;
      }
    }
  }

  .content::-webkit-scrollbar {
    width: 6px !important;
    height: 6px !important;
  }

  .content::-webkit-scrollbar-thumb {
    min-height: 40px !important;
    max-height: 40px !important;
  }
}

.search-bar {
  width: -webkit-fill-available;
  height: 100%;
  padding: 2px 6px;

  input {
    height: 100%;
    width: 100%;
    font-family: var(--FontFamily);
    font-size: 10px;
    font-weight: 400;
    color: #45546e;
    outline: none;
    border: none;
    background-color: #f7f9fb;
  }

  input::placeholder {
    font-family: var(--FontFamily);
    font-size: 10px;
    font-weight: 400;
    color: #d4d6d8;
  }
}

.svg-icon {
  right: 10px;
  bottom: 10px;
  cursor: pointer;
}

.disabled-icon {
  cursor: not-allowed;
  opacity: 0.6;
}

// Main Chatbot Container 
.chatbot-container {
  display: flex;
  flex-direction: column;
  min-height: calc(100vh - var(--chatBotAdjustedHeight));
  width: var(--kebsChatBotContentWidth);
  height: calc(100vh - var(--chatBotAdjustedHeight));

  .chatbot-container-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 24px;
    background-color: #ffffff;

    .align-items-with-gap {
      display: flex;
      align-items: center;
      gap: 10px;

      .svg-icon {
        cursor: pointer;
      }

      .ai-icon {
        width: 29px;
        height: 30px;
      }
    }
  }

  .svg-icon {
    cursor: pointer;
  }


  .chatbot-container-content {
    height: var(--kebsChatbotContentHeight);
    overflow-y: auto;
    position: relative;

    .history-content {
      display: flex;
      justify-content: space-between;
      padding: 16px 24px;
      background-color: #FFF3F4;
      height: calc(var(--kebsChatbotContentHeight) + 52px);

    }

    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 8px;
      height: calc(var(--kebsChatbotContentHeight) - 40px);

      .gif {
        height: 80px;
        width: 80px;
      }

      .carousel-text {
        position: relative;
        height: 50px;
        width: 100%;
        overflow: hidden;
      }

      .text-content {
        position: absolute;
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        transition: transform 0.5s ease-in, opacity 0.5s ease-in;
        position: absolute;
        opacity: 1;
        transition: opacity 2s ease;

        .main-text {
          font-family: var(--FontFamily);
          font-size: 14px;
          font-weight: 400;
          color: #7d838b;
        }

        .sub-text {
          font-family: var(--FontFamily);
          font-size: 14px;
          font-weight: 600;
          color: #ee4961;
        }
      }

      .slide {
        animation: slideUp 2s ease forwards;
      }

      /* Keyframes to move text from bottom to top */
      @keyframes slideUp {
        0% {
          transform: translateY(100%);
          /* Start from bottom */
          opacity: 0;
        }

        20% {
          transform: translateY(0);
          /* Move to center */
          opacity: 1;
        }

        80% {
          transform: translateY(0);
          /* Stay in center */
          opacity: 1;
        }

        100% {
          transform: translateY(-100%);
          /* Move out from top */
          opacity: 0;
        }
      }
    }


    .prompt-library-content {
      display: flex;
      flex-direction: column;
      gap: 16px;
      height: calc(var(--kebsChatbotContentHeight) + 24px);
      overflow: hidden;
      position: relative;
      z-index: 1;

      .header {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .prompt-title {
          display: flex;
          align-items: center;
          gap: 12px;

          .prompt-title-icon {
            font-size: 16px;
            width: 16px;
            height: 16px;
            color: #45546e;
            cursor: pointer;
          }

          .prompt-title-text {
            font-family: var(--FontFamily);
            font-size: 16px;
            font-weight: 600;
            color: #ee4961;
          }
        }

        .search-bar {
          display: flex;
          align-items: center;
          gap: 4px;
          border: 1px solid #b9c0ca;
          border-radius: 60px;
          width: 40%;
          padding: 2px 12px;
          height: 28px;

          input {
            height: 100%;
            width: 100%;
            font-family: var(--FontFamily);
            font-size: 12px;
            font-weight: 400;
            color: #45546e;
            outline: none;
            border: none;
          }

          input::placeholder {
            font-family: var(--FontFamily);
            font-size: 12px;
            font-weight: 400;
            color: #b9c0ca;
          }
        }
      }

      .content {
        display: flex;
        align-items: center;
        width: 100%;
        gap: 2%;

        .categories {
          display: flex;
          flex-direction: column;
          height: calc(var(--kebsChatbotContentHeight) - 20px);
          border: 0.5px solid #e8e9ee;
          border-radius: 8px;
          width: 35%;
          padding: 8px;
          gap: 8px;

          .header {
            display: flex;
            justify-content: start;
            align-items: center;
            gap: 8px;
            padding-bottom: 8px;
            border-bottom: 0.5px solid #dadce2;
            margin-bottom: 8px;

            .categories-text {
              font-family: var(--FontFamily);
              font-size: 12px;
              font-weight: 400;
              color: #111434;
              text-overflow: ellipsis;
              white-space: nowrap;
              overflow: hidden;
              max-width: 70%;
            }

            .count {
              background-color: #45546e;
              padding: 2px 6px;
              border-radius: 10px;
              font-family: var(--FontFamily);
              font-size: 10px;
              font-weight: 500;
              color: #fff;
            }
          }

          .categories-list {
            display: flex;
            flex-direction: column;
            gap: 8px;
            height: calc(var(--kebsChatbotContentHeight) - 68px);
            overflow-y: auto;
            padding-right: 2%;

            .category-list {
              display: flex;
              align-items: center;
              gap: 8px;
              cursor: pointer;
              border-radius: 4px;
              padding: 4px;

              .text {
                font-family: var(--FontFamily);
                font-size: 12px;
                font-weight: 400;
                color: #111434;
                text-overflow: ellipsis;
                white-space: nowrap;
                overflow: hidden;
                max-width: 70%;
              }

              .count {
                padding: 2px 6px;
                border-radius: 10px;
                font-family: var(--FontFamily);
                font-size: 10px;
                font-weight: 500;
                color: linear-gradient(270deg, #ef4a61 0%, #f27a6c 105.29%);
                background: linear-gradient(270deg,
                    rgba(239, 74, 97, 0.1) 0%,
                    rgba(242, 122, 108, 0.1) 105.29%);
              }
            }

            .selected-category-list {
              background: linear-gradient(270deg,
                  rgba(239, 74, 97, 0.1) 0%,
                  rgba(242, 122, 108, 0.1) 105.29%);

              .selected-count {
                color: #fff;
                background: linear-gradient(270deg,
                    #ef4a61 0%,
                    #f27a6c 105.29%);
              }
            }
          }

          .categories-list::-webkit-scrollbar {
            width: 6px !important;
            height: 6px !important;
          }

          .categories-list::-webkit-scrollbar-thumb {
            min-height: 40px !important;
            max-height: 40px !important;
          }
        }

        .category-content {
          display: flex;
          flex-direction: column;
          height: calc(var(--kebsChatbotContentHeight) - 20px);
          gap: 6px;
          width: 63%;
          padding-right: 1%;
          overflow-y: auto;

          .single-prompt {
            display: flex;
            flex-direction: row;
            align-items: start;
            justify-content: space-between;
            gap: 16px;
            border: 0.5px solid #dadce2;
            border-radius: 4px;
            padding: 8px;
            cursor: pointer;

            .text {
              font-family: var(--FontFamily);
              font-size: 10px;
              font-weight: 400;
              color: #8b95a5;
              text-align: justify;
              line-height: 1.5;
            }

            .copy {
              height: 10px;
              display: none;
              align-items: center;
              justify-content: end;
              gap: 4px;
              font-family: var(--FontFamily);
              font-size: 8px;
              font-weight: 500;
              color: #ef4a61;
            }
          }

          .single-prompt:hover {
            border: 0.5px solid #ef4a61;
            background: linear-gradient(270deg,
                rgba(239, 74, 97, 0.05) 0%,
                rgba(242, 122, 108, 0.05) 105.29%);
          }

          .single-prompt:hover .copy {
            display: flex;
          }
        }

        .category-content::-webkit-scrollbar {
          width: 6px !important;
          height: 6px !important;
        }

        .category-content::-webkit-scrollbar-thumb {
          min-height: 40px !important;
          max-height: 40px !important;
        }

        .category-content-empty-state {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: calc(var(--kebsChatbotContentHeight) - 20px);
          gap: 6px;
          width: 63%;
          padding-right: 1%;
          overflow: hidden;
          font-family: var(--FontFamily);
          font-size: 14px;
          font-weight: 700;
          color: #45546e;
        }
      }
    }
  }

}

// History Menu Options
.menu-content {
  display: flex;
  flex-direction: column;
  padding: 0px 12px;
  gap: 4px;

  .menu-item {
    font-family: var(--FontFamily);
    font-size: 10px;
    font-weight: 400;
    color: #515965;
    height: 20px;
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 0px;
  }

  .menu-icon {
    font-size: 14px;
    width: 14px;
    height: 14px;
    color: #a8acb2;
    margin: 0px;
  }

  .menu-divider {
    color: #e8e9ee;
  }
}


.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
  height: calc(100vh - var(--chatBotAdjustedHeight));

  .gif {
    height: 80px;
    width: 80px;
  }

  .carousel-text {
    position: relative;
    height: 50px;
    width: 100%;
    overflow: hidden;
  }

  .text-content {
    position: absolute;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transition: transform 0.5s ease-in, opacity 0.5s ease-in;
    position: absolute;
    opacity: 1;
    transition: opacity 2s ease;

    .main-text {
      font-family: var(--FontFamily);
      font-size: 14px;
      font-weight: 400;
      color: #7d838b;
    }

    .sub-text {
      font-family: var(--FontFamily);
      font-size: 14px;
      font-weight: 600;
      color: #ee4961;
    }
  }

  .slide {
    animation: slideUp 2s ease forwards;
  }

  /* Keyframes to move text from bottom to top */
  @keyframes slideUp {
    0% {
      transform: translateY(100%); /* Start from bottom */
      opacity: 0;
    }
    20% {
      transform: translateY(0); /* Move to center */
      opacity: 1;
    }
    80% {
      transform: translateY(0); /* Stay in center */
      opacity: 1;
    }
    100% {
      transform: translateY(-100%); /* Move out from top */
      opacity: 0;
    }
  }
}

.search-overlay-section {
  display: inline-block;
  position: relative;
  width: 100%;
  .overlay {
    color: #515965;
    align-items: center;
    background: #ffffff;
    max-height: 181px;
    border-radius: 8px;
    box-shadow: 0px 2px 1px 0px #0000001f;
    border: 1px solid #e8e9ee;
    z-index: 5;
    overflow: hidden;
    padding-top: 20px;
    position: absolute;
    bottom: 20px;
    left: 0;
    width: 100%;
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0px 2px 1px 0px #0000001f;
    border: 1px solid #e8e9ee;
    z-index: 5;
    min-height: 62px;
    overflow-y: auto;
    display: flex;
    position: absolute;
    pointer-events: all !important;

    .prompt-content {
      overflow-y: auto;
      display: flex;
      flex-direction: column;
      padding: 0px 2px 24px 8px;
      margin-right: 2px;
      gap: 4px;
      max-height: 122px;
      width: 100%;
      position: relative;

      .prompt-text {
        font-family: var(--FontFamily);
        font-weight: 400;
        font-size: 12px;
        padding: 6px 6px;
        border-radius: 3px;
        cursor: pointer;
        width: 100%;
      }
      .prompt-text:hover {
        background: #ffecee;
      }
    }
  }
  .search-ui {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 8px;
    border: 1px solid #b9c0ca;
    border-radius: 60px;
    height: 36px;
    width: 100%;
    padding-right: 4px;
    z-index: 10;
    position: relative;
    background: #ffff;

    .search-bar {
      width: -webkit-fill-available;
      height: 100%;
      padding: 4px 8px;

      input {
        height: 100%;
        width: 100%;
        font-family: var(--FontFamily);
        font-size: 12px;
        font-weight: 400;
        color: #45546e;
        outline: none;
        border: none;
      }

      input::placeholder {
        font-family: var(--FontFamily);
        font-size: 12px;
        font-weight: 400;
        color: #b9c0ca;
      }
    }
  }
  /* Target scrollbar inside overlay */
  .overlay::-webkit-scrollbar,
  .prompt-content::-webkit-scrollbar {
    width: 6px !important;
    height: 6px !important;
  }

  /* Scrollbar track */
  .overlay::-webkit-scrollbar-track,
  .prompt-content::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
    height: 68px !important;
  }

  /* Scrollbar thumb */
  .overlay::-webkit-scrollbar-thumb,
  .prompt-content::-webkit-scrollbar-thumb {
    background: #b9c0ca;
    border-radius: 3px;
    min-height: 50px;
  }

  /* Scrollbar thumb on hover */
  .overlay::-webkit-scrollbar-thumb:hover,
  .prompt-content::-webkit-scrollbar-thumb:hover {
    background: #999;
  }

}

.search-ui-chat {
  display: flex;
  // flex-direction: column; 
  border: 1px solid #b9c0ca;
  border-radius: 23px; 
  width: 100%;
  padding: 8px;
  z-index: 10;
  position: relative;
  background: #fff;

  .search-bar-chat{
    overflow-y: auto;
    width: 100%;
    padding: 4px 8px;

    textarea {
      max-height: 120px;
      width: 100%;
      resize: none;
      overflow-y: auto;
      font-family: var(--FontFamily);
      font-size: 16px;
      font-weight: 400;
      color: #45546e;
      outline: none;
      border: none;
    }
  }
}


