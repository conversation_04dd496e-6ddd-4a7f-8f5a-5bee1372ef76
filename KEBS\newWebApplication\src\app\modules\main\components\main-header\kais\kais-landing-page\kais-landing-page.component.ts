import {
  Component,
  ElementRef,
  <PERSON>L<PERSON><PERSON>,
  Inject,
  OnInit,
  TemplateRef,
  ViewChild,
  ViewContainerRef
} from '@angular/core';
import { AiServiceService } from 'src/app/modules/main/services/ai-service/ai-service.service';
import { LoginService } from 'src/app/services/login/login.service';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { Overlay, OverlayRef } from '@angular/cdk/overlay';
import { TemplatePortal } from '@angular/cdk/portal';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { ToasterService } from 'src/app/modules/applicant-tracking-system/shared-components/ats-custom-toast/toaster.service';
import moment from 'moment';
import * as _ from 'underscore';
import { DomSanitizer } from '@angular/platform-browser';
import { StringSimilarityService } from 'src/app/modules/main/services/string-similarity/string-similarity.service';
import { FilterPromptLibraryPipe } from '../pipes/filter-prompt-library/filter-prompt-library.pipe';
import { MAT_MENU_SCROLL_STRATEGY } from '@angular/material/menu';
import { v4 as uuidv4 } from 'uuid';
@Component({
  selector: 'app-kais-landing-page',
  templateUrl: './kais-landing-page.component.html',
  styleUrls: ['./kais-landing-page.component.scss'],
  providers: [
    {
      provide: MAT_MENU_SCROLL_STRATEGY,
      deps: [Overlay],
      useFactory: (overlay: Overlay) => () =>
        overlay.scrollStrategies.reposition(),
    },
    FilterPromptLibraryPipe,
  ],
})
export class KaisLandingPageComponent implements OnInit {
  protected _onDestroy = new Subject<void>();
  @ViewChild('chatScrollContainer') private chatScrollContainer!: ElementRef;

  // History
  isHistoryVisible: boolean = false;
  isHistoryLoading: boolean = false;
  historySearchParams: string = '';
  historySelectedThreadId: string = null;
  historySkip: number = 0;
  historyLimit: number = 25;
  historyData = [];
  pinnedHistoryData = [];
  historyGroupedData = {};
  historyGroupedDateData = [];
  selectedHistoryThreadData: any;
  selectedHistoryDateIndex: number = null;
  selectedHistoryThreadIndex: number = null;

  // Prompt Library
  promptSearchParams: string = '';
  searchDefaultPrompts: Array<Object> = []
  searchPromptSkip: number = 0;
  searchLazyLoadedList: any = [];
  startIndex: number = 0;
  skip: number = 25;
  filteredPrompts: any = [];
  promptLibraryData = [];
  isPromptLibraryVisible: boolean = false;
  currentSelectedPromptLibraryCategoryIndex: number = null;
  currentSelectedPromptLibraryCategory: string = null;
  promptLibrarySearchParams: string = '';

  // Home Page
  isHomeScreenVisible: boolean = true;
  profile: any;

  // Chat Container
  currentThreadData = [];
  currentThreadLoadTextIndex = 0;
  isChatVisible: boolean = false;
  dialogExpanded: boolean = false;


  repeatArray = [];

  // Loader data
  threadTexts = [];
  loaderTexts = [];
  isSliding: boolean = false;
  isThreadLoading: boolean = false;
  isLoading: boolean = true;
  isPromptApiInProgress: boolean = false;
  suggestedPrompts = [];

  modules = []; // should be removed
  isTyping: boolean = false; // should be removed
  currentIndex = 0; // should be removed
  flatList: any[] = [];
  childrenKey = 'children';

  // Like & Dislike
  likeStatusMap: { [key: number]: boolean | null } = {};
  childIndexMap: { [index: number]: number } = {};

  isCheckedUserIntent: boolean = false;
  isNewThread: boolean = true;

  // Chat Loader Text
  textInterval: any;
  loaderCurrentIndex = 0;
  currentLoadingText: string;
  isReportsVisible: boolean = false;
  hideRegenerate: boolean = true;
  processStarted: boolean = false;
  loaderText: string = 'Reviewing your request...';
  threadIdReference: string;
  constructor(
    @Inject(MAT_DIALOG_DATA)
    public data: {
      aiThemeConfig: any;
    },
    private _dialogRef: MatDialogRef<KaisLandingPageComponent>,
    private _aiService: AiServiceService,
    private _loginService: LoginService,
    private _toaster: ToasterService,
    private _viewContainerRef: ViewContainerRef,
    private _overlay: Overlay,
    private _stringSimilarity: StringSimilarityService,
    private sanitizer: DomSanitizer,
    private elementRef: ElementRef,
    private filter: FilterPromptLibraryPipe
  ) { }



  async ngOnInit() {
    this.profile = this._loginService.getProfile().profile;
    this.threadTexts = this.data?.aiThemeConfig['THREAD-LOADER-TEXT'];
    this.loaderTexts = this.data?.aiThemeConfig['CHAT-LOADER-TEXT'];
    this.onDialogResizing(true);
    setInterval(() => {
      this.currentThreadLoadTextIndex =
        (this.currentThreadLoadTextIndex + 1) % this.threadTexts.length;
      this.isSliding = true; // Start slide animation

      setTimeout(() => {
        // After the animation completes, update the text
        this.isSliding = false;
      }, 500); // Animation duration must match CSS
    }, 2000); // Change text every 2 seconds
    this.calculateDynamicContentHeight();

    Promise.all([
      // this.defaultPrompts(),
      this.getPromptLibraryData()
    ])
      .then(async (res) => {
        // await this.getDefaultPromptsBySearch();
        this.isLoading = false;
      })
      .catch((err) => {
        this.isLoading = false;
        this.onDialogClose();
      });
  }

  @HostListener('window:resize')
  onResize() {
    this.calculateDynamicContentHeight();
  }

  /**
 * @description on Dialog Expansion or Collapse
 */
  onDialogResizing(initial = false) {
    if (!initial) {
      this.dialogExpanded = !this.dialogExpanded
    }
    this.calculateDynamicContentHeight();
    let width = 'calc(100vw - 76px)'; //Default Width
    let height = '55px'; //Default Height for Adjusting the Chat Bot Height
    let chatContentwidth = 'calc(100vw - 150px)';
    if (this.dialogExpanded) {
      width = 'calc(-20vw - 84px + 100vw)';
      height = '55px';
      chatContentwidth = 'calc(-20vw - 84px + 100vw)'
    }
    document.documentElement.style.setProperty(
      '--kebsChatBotContentWidth',
      width
    );
    document.documentElement.style.setProperty(
      '--FontFamily',
      "DM Sans"
    );
    document.documentElement.style.setProperty(
      '--chatBotAdjustedHeight',
      height
    );
    document.documentElement.style.setProperty(
      '--chatBotWidth',
      chatContentwidth
    );
  }

  /**
* @description Calculates dynamic height based on screen size
*/
  calculateDynamicContentHeight() {
    let adjustedHeight = 41;
    if (this.dialogExpanded) {
      adjustedHeight = 0;
    }
    // Total available height for the chatbot content
    const chatbotContentHeight = window.innerHeight - 220 - adjustedHeight;

    // History is part of chatbot now — assign a portion (e.g., 30%)
    const historyContentHeight = chatbotContentHeight * 0.3;
    const remainingChatContentHeight = chatbotContentHeight - historyContentHeight;

    document.documentElement.style.setProperty('--kebsChatbotContentHeight', `${chatbotContentHeight}px`);
    document.documentElement.style.setProperty('--kebsChatbotHistoryContentHeight', `${historyContentHeight}px`);


    let historyLoadMaxHeight = window.innerHeight - 170 - adjustedHeight;
    let finalCount = Math.floor(historyLoadMaxHeight / 34);
    this.repeatArray = Array(finalCount);
  }

  /**
 * @description Open history UI
 */
  openHistory() {
    this.isHistoryVisible = true;
    this.historyData = [];
    this.allPinnedHistory();
    this.allHistory(true);
    document.documentElement.style.setProperty(
      '--chatBotWidth',
      'calc(-20vw - 84px + 100vw)'
    );
  }

  /**
   * @description Close history UI
   */
  closeHistory() {
    this.isHistoryVisible = false;
    document.documentElement.style.setProperty(
      '--chatBotWidth',
      'calc(100vw - 150px)'
    );
  }

  /**
* @description Get unpinned history
* @param {boolean} reset
*/
  async allHistory(reset) {
    if (reset) {
      this.isHistoryLoading = true;
      this.historySkip = 0;
      this.historyData = [];
      this.historyGroupedData = {};
      this.historyGroupedData = [];
    }

    let payload = {
      aid: this.profile.aid,
      oid: this.profile.oid,
      skip: this.historySkip,
      limit: this.historyLimit,
      searchParams: this.historySearchParams,
    };

    return new Promise((resolve, reject) => {
      this._aiService
        .allHistory(payload)
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res: any) => {
            if (res['err'] == false) {
              this.historyData = [...this.historyData, ...res['response']];
            }
            this.groupHistoryData();
            this.isHistoryLoading = false;
            resolve(true);
          },
          error: (err) => {
            this._toaster.showError(
              'Error',
              'Error in Fetching History Data',
              7000
            );
            let recordError = this._aiService.recordError({prompt: "From History", error: err })
            this.isHistoryLoading = false;
            reject();
          },
        });
    });
  }

/**
* @description Get pinned history
* @param {boolean} reset
*/
async allPinnedHistory() {
  let payload = {
    aid: this.profile.aid,
    oid: this.profile.oid,
    skip: 0,
    limit: 4,
    searchParams: this.historySearchParams,
  };

  return new Promise((resolve, reject) => {
    this._aiService
      .allPinnedHistory(payload)
      .pipe(takeUntil(this._onDestroy))
      .subscribe({
        next: (res: any) => {
          if (res['err'] == false) {
              this.pinnedHistoryData = res['response'];
          }
          this.isHistoryLoading = false;
          resolve(true);
        },
        error: (err) => {
          this._toaster.showError(
            'Error',
            'Error in Fetching History Data',
            7000
          );
          let recordError = this._aiService.recordError({prompt: "From Pinned History", error: err })
          this.isHistoryLoading = false;
          reject();
        },
      });
  });
}

  /**
 * @description Group all history data based on date
 */
  groupHistoryData() {
    const today = moment().startOf('day');
    const yesterday = moment().subtract(1, 'days').startOf('day');
    const sevenDaysAgo = moment().subtract(7, 'days').startOf('day');
    const thirtyDaysAgo = moment().subtract(30, 'days').startOf('day');

    const groupedData = {};
    this.historyData.forEach((item) => {
      const itemDate = moment(item.created_on).startOf('day');

      if (itemDate.isSame(today, 'day')) {
        if (!groupedData['Today']) {
          groupedData['Today'] = [];
        }
        groupedData['Today'].push(item);
      } else if (itemDate.isSame(yesterday, 'day')) {
        if (!groupedData['Yesterday']) {
          groupedData['Yesterday'] = [];
        }
        groupedData['Yesterday'].push(item);
      } else if (itemDate.isAfter(sevenDaysAgo)) {
        if (!groupedData['Previous 7 Days']) {
          groupedData['Previous 7 Days'] = [];
        }
        groupedData['Previous 7 Days'].push(item);
      } else if (itemDate.isAfter(thirtyDaysAgo)) {
        if (!groupedData['Previous 30 Days']) {
          groupedData['Previous 30 Days'] = [];
        }
        groupedData['Previous 30 Days'].push(item);
      } else {
        // For dates older than 30 days, group by month name (e.g., "September 2024")
        const monthYear = itemDate.format('MMMM YYYY');
        if (!groupedData[monthYear]) {
          groupedData[monthYear] = [];
        }
        groupedData[monthYear].push(item);
      }
    });

    // Store the grouped data in your component state
    this.historyGroupedData = groupedData;

    // For date ordering (Today, Yesterday, etc.), we capture the keys in reverse order
    this.historyGroupedDateData = Object.keys(groupedData);
  }

  /**
 * @description Get default prompts
 */
  async defaultPrompts() {
    let payload = {
      aid: this.profile.aid,
      oid: this.profile.oid,
      module_id: 915,
      accessible_module_ids: _.pluck(this.modules, 'id'),
    };
    this.suggestedPrompts = [];

    return new Promise((resolve, reject) => {
      this._aiService
        .defaultPrompts(payload)
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res: any) => {
            if (res['err'] == false) {
              this.suggestedPrompts = res['data'];
            }
            resolve(true);
          },
          error: (err) => {
            this._toaster.showError(
              'Error',
              'Error in Fetching Default Prompts',
              7000
            );
            this.isLoading = false;
            reject();
          },
        });
    });
  }

  /**
 * @description Get prompt library data
 */
  async getPromptLibraryData() {
    let payload = {
      aid: this.profile.aid,
      oid: this.profile.oid,
    };
    this.promptLibraryData = [];

    return new Promise((resolve, reject) => {
      this._aiService
        .getPromptLibraryData(payload)
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res: any) => {
            if (res['err'] == false) {
              this.promptLibraryData = res['data'];
            }
            resolve(true);
          },
          error: (err) => {
            this._toaster.showError('Error', 'Error in Fetching Modules', 7000);
            this.isLoading = false;
            reject();
          },
        });
    });
  }

  /**
* @description getting Default Prompt Search 
* @returns 
*/
  async getDefaultPromptsBySearch() {
    this.searchDefaultPrompts = [];
    let payload = {
      aid: this.profile.aid,
      oid: this.profile.oid,
      skip: this.searchPromptSkip,
      limit: 25,
      module_id: [460, 544, 915],
      is_general_prompt: !this.isReportsVisible,
      is_report_prompt: this.isReportsVisible,
      search_params: this.promptSearchParams ? this.promptSearchParams : "",
    };

    return new Promise(async (resolve, reject) => {
      await this._aiService
        .getDefaultPromptsBySearch(payload)
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res: any) => {
            if (res['err'] == false) {
              this.searchDefaultPrompts = res.data
              if (this.searchPromptSkip == 0) {
                this.searchDefaultPrompts = res.data
              }
              else {
                this.searchDefaultPrompts = [...this.searchDefaultPrompts, ...res['data']];
              }
            }
            resolve(true);
          },
          error: (err) => {
            this._toaster.showError(
              'Error',
              'Error in Fetching Search Prompt Data',
              7000
            );
            reject();
          },
        });
    });
  }


  /**
 * @description On close dialog
 */
  onDialogClose() {
    this._dialogRef.close();
  }

  /**
 * @description Open Prompt Library UI
 */
  openPromptLibrary() {
    this.promptLibrarySearchParams = '';
    this.promptSearchParams = '';
    this.resetUiState(['isPromptLibraryVisible']);
    if (this.promptLibraryData && this.promptLibraryData.length > 0) {
      this.setPromptLibraryIndex(0);
      this.setPromptLibraryCategory(this.promptLibraryData[0]['name']);
    }
  }

  /**
* @description Set Prompt Library ID
* @param {int} index
*/
  setPromptLibraryIndex(index) {
    this.currentSelectedPromptLibraryCategoryIndex = index;
  }

  /**
   * @description Set Prompt Library Categort
   * @param {string} name
   */
  setPromptLibraryCategory(name) {
    this.currentSelectedPromptLibraryCategory = name;
  }

  /**
 * @description Reset UI
 * @param {array} value
 */
  async resetUiState(value) {
    let states = [
      'isChatVisible',
      'isHomeScreenVisible',
      'isPromptLibraryVisible',
    ];
    for (let i = 0; i < states.length; i++) {
      this[states[i]] = false;
    }

    for (let i = 0; i < value.length; i++) {
      this[value[i]] = true;
    }

    // await this.getDefaultPromptsBySearch();
  }

  getGreetingWithName(value, replace_text) {
    const text = value || "Hello, ${name}";
    return text.replace("${name}", replace_text);
  }

  /**
* @description On search params for prompt library changes
*/
  onSearchParamsChange() {
    const filteredData = this.filter.transform(
      this.promptLibraryData,
      this.promptLibrarySearchParams
    );
    if (filteredData.length > 0) {
      this.findPromptLibraryIndex(filteredData, filteredData[0]['id']);
    } else {
      this.currentSelectedPromptLibraryCategoryIndex = 0;
      this.currentSelectedPromptLibraryCategory = null;
    }
  }

  /**
 * @description Find Prompt Library ID
 * @param {array} data
 * @param {int} id
 */
  findPromptLibraryIndex(data, id) {
    let index = data.findIndex((obj) => obj.id == id);
    let name = data.find((obj) => obj.id == id)?.name;
    if (index != -1) {
      this.setPromptLibraryIndex(index);
      this.setPromptLibraryCategory(name);
    }
  }

  /**
* @description Highlight prompt text
* @param prompt
*/
  highlightPromptKey(prompt) {
    if (!prompt) {
      return '-';
    }
    const bracesRegex = /[{[]([^}\]]+)[}\]]/g;
    const highlightedPrompt = prompt.replace(
      bracesRegex,
      (match, content) => `<span style="color: #ef4a61;">[${content}]</span>`
    );

    return this.sanitizer.bypassSecurityTrustHtml(highlightedPrompt);
  }


  openNewChat() {
    this.resetUiState(['isHomeScreenVisible']);
    this.currentThreadData = [];
    this.threadIdReference = '';
    this.isCheckedUserIntent = false;
    this.isNewThread = true;
    this.promptSearchParams = '';
    // this.defaultPrompts();
    this.resetThreadId();
  }
  /**
 * @description Reset thread id
 */
  resetThreadId() {
    this.historySelectedThreadId = null;
  }

  async copyPromptToClipboard(prompt) {
    prompt.isCopyInProgress = true;
    this.promptSearchParams = prompt?.prompt
    // Reset isCopying back to false after 1 second
    setTimeout(async () => {
      prompt.isCopyInProgress = false;
      await this.onSearchPromptFromLibrary(this.promptSearchParams);
    }, 1000);

  }


  /**
  * @description On search prompt with intent and agent
  * @param {string} prompt
  */

  async onSearchPrompt(event, prompt) {

    event.preventDefault();
    if(this.processStarted){
      return
    }
    this.processStarted = true;
 
    // Call intent
    if (prompt && prompt.length > 0 && prompt.trim().length > 0) {
      this.currentThreadData.push({
        prompt: prompt,
        isLoading: true
      });

      // this.startLoadingTextCycle();
      this.resetUiState(['isChatVisible']);
      setTimeout(() => {
        this.scrollToBottom();
      }, 500);
      this.promptSearchParams = '';
      this.isPromptApiInProgress = true;

      let triggerCacheResponse = this.triggerCacheResponse(prompt)
    }
    else {
      this.processStarted = false;
    }

  }

/**
* @description On search prompt from prompt library for FAQ's
* @param {string} prompt
*/
  async onSearchPromptFromLibrary(prompt) {
    // Call intent
    if (prompt && prompt.length > 0 && prompt.trim().length > 0) {
      this.currentThreadData.push({
        prompt: prompt,
        isLoading: true
      });
      // this.startLoadingTextCycle()
      this.loaderText = 'Reviewing your request...';
      this.resetUiState(['isChatVisible']);
      setTimeout(() => {
        this.scrollToBottom();
      }, 500);
      this.promptSearchParams = '';
      this.isPromptApiInProgress = true;
      this.triggerAgentExecutor('faq_agent', prompt, 1, false)

    }
  }
  


  /**
  * @description Agent Executor For Single Intent
  */

  async triggerAgentExecutor(response, prompt, response_flag, is_new_intent) {
    let thread_id = this.threadIdReference;
    if (!thread_id) {
      thread_id = uuidv4();
      this.threadIdReference = thread_id;
      this.currentThreadData[this.currentThreadData?.length - 1]['thread_id'] = thread_id;
      this.isNewThread = true;
    }
    else{
      // this.isNewThread = false;
      this.currentThreadData[this.currentThreadData.length - 1]['thread_id'] = thread_id;
    }
    let intents = _.pluck(Array.isArray(response?.intent) ? response.intent : [], 'intent');
    let payload = {
      chat_thread_id: thread_id,
      intent: response_flag == 0 ? intents[0] : response,
      prompt: prompt,
      is_deep_search: false,
      is_new_intent: is_new_intent
    }
    this.currentThreadData[this.currentThreadData.length - 1]['intent'] = response_flag == 0 ? intents[0] : response;

    if (thread_id) {
      let healthCheck = await this.getHealthStatus();
      this.isPromptApiInProgress = true;
      return new Promise((resolve, reject) => {
        this._aiService
          .getAgentResponse(payload)
          .pipe(takeUntil(this._onDestroy))
          .subscribe({
            next: async (res: any) => {
              if (res['err'] == false) {
                this.processStarted = false;
                this.loaderText = 'Getting everything in place...';
                if(this.isNewThread){
                  let getChatHeading = await this._aiService.getChatHeading({chat_thread_id: thread_id, prompt: prompt}).toPromise()
                  this.allHistory(true)
                }
                this.currentThreadData[this.currentThreadData.length - 1]['response'] =
                  res['response']['output'];

                this.currentThreadData[this.currentThreadData.length - 1]['chat_message_id'] =
                  res['response']['chat_message_id'];

                this.currentThreadData[this.currentThreadData.length - 1][
                  'isReady'
                ] = true;
                this.currentThreadData[this.currentThreadData.length - 1][
                  'isLoading'
                ] = false;
                setTimeout(() => {
                  this.currentThreadData[this.currentThreadData.length - 1][
                    'isReady'
                  ] = false;
                }, 1500);

              } else {
                this.processStarted = false;
                if(this.isNewThread){
                  let getChatHeading = await this._aiService.getChatHeading({chat_thread_id: thread_id, prompt: prompt}).toPromise()
                  this.allHistory(true)
                }
                this.currentThreadData[this.currentThreadData.length - 1]['isError'] = true;
                this.currentThreadData[this.currentThreadData.length - 1]['isQuestion'] = res?.response ? res['response']['isQuestion'] : false;
                this.currentThreadData[this.currentThreadData.length - 1]['error_code'] = res?.error_code || 'ERR_API_001'
                this.currentThreadData[this.currentThreadData.length - 1]['response'] = res?.response ? res['response']['output'] : "";
                this.currentThreadData[this.currentThreadData.length - 1]['chat_message_id'] = res?.response ? res['response']['chat_message_id'] : "";
                let recordError = this._aiService.recordError({prompt: prompt, error: res })
              }
              setTimeout(() => {
                this.isPromptApiInProgress = false;
                // this.scrollToBottom();
              }, 500);
              resolve(true);
            },
            error: async(err) => {
              this.processStarted = false;
              if (err?.status == 504 || err?.status == 503) {
                let errorTrigger = this._aiService.notifyError({ prompt: prompt, code: err?.status })
              }
              let recordError = this._aiService.recordError({prompt: prompt, error: err })
              if(this.isNewThread){
                let getChatHeading = await this._aiService.getChatHeading({chat_thread_id: thread_id, prompt: prompt}).toPromise()
                this.allHistory(true)
              }
              this.currentThreadData[this.currentThreadData.length - 1][
                'isLoading'
              ] = false;
              this.currentThreadData[this.currentThreadData.length - 1][
                'isError'
              ] = true;
              this.currentThreadData[this.currentThreadData.length - 1]['error_code'] = 'ERR_API_001'
              setTimeout(() => {
                this.isPromptApiInProgress = false;
              }, 1500);
              reject();
            },
          });
      });
    }

  }

  /**
  * @description Agent Executor For Multiple Intent
  */

  async triggerAgentExecutorForMultiple(prompt, intents, is_new_intent) {
    let payload = {
      intent: intents,
      prompt: prompt,
      is_new_intent: is_new_intent
    }
    this.isCheckedUserIntent = true;
    this.isPromptApiInProgress = true;
    return new Promise((resolve, reject) => {
      this._aiService
        .getAgentResponseForMultipleIntents(payload)
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res: any) => {
            if (res['err'] == false) {
              this.processStarted = false;
              this.currentThreadData[this.currentThreadData.length - 1]['response'] =
                res['response']['output'];

              this.currentThreadData[this.currentThreadData.length - 1]['intent'] =
                res['response']['intent'];

              this.currentThreadData[this.currentThreadData.length - 1]['chat_message_id'] =
                res['response']['chat_message_id'];

              this.currentThreadData[this.currentThreadData.length - 1][
                'isReady'
              ] = true;
              this.currentThreadData[this.currentThreadData.length - 1][
                'isLoading'
              ] = false;
              setTimeout(() => {
                this.currentThreadData[this.currentThreadData.length - 1][
                  'isReady'
                ] = false;
              }, 1500);

            } else {
              this.processStarted = false;
              this.currentThreadData[this.currentThreadData.length - 1]['isError'] = true;
              this.currentThreadData[this.currentThreadData.length - 1]['error_code'] = res?.error_code || 'ERR_API_001'
              this.currentThreadData[this.currentThreadData.length - 1]['response'] = res?.response ? res['response']['output'] : "";
              this.currentThreadData[this.currentThreadData.length - 1]['chat_message_id'] = res?.response ? res['response']['chat_message_id'] : "";
              let recordError = this._aiService.recordError({prompt: prompt, error: res })
            }
            setTimeout(() => {
              this.isPromptApiInProgress = false;
            }, 1500);
            resolve(true);
          },
          error: (err) => {
            this.processStarted = false;
            if (err?.status == 504 || err?.status == 503) {
              let errorTrigger = this._aiService.notifyError({ prompt: prompt, code: err?.status })
            }
            let recordError = this._aiService.recordError({prompt: prompt, error: err })
            this.currentThreadData[this.currentThreadData.length - 1][
              'isLoading'
            ] = false;
            this.currentThreadData[this.currentThreadData.length - 1][
              'isError'
            ] = true;
            this.currentThreadData[this.currentThreadData.length - 1]['error_code'] = 'ERR_API_001'
            setTimeout(() => {
              this.isPromptApiInProgress = false;
            }, 1500);
            reject();
          },
        });
    });


  }



  /**
* @description Find matching prompt
* @param inputSentence
* @param promptLibraryData
* @param threshold
*/
  async findMatchingPrompt(
    inputSentence: string,
    promptLibraryData: any[],
    threshold: number = 0.9
  ) {
    let bestMatch = { libraryIndex: -1, promptIndex: -1, similarity: 0 };

    promptLibraryData.forEach((library, libraryIndex) => {
      library.prompts.forEach((promptObj: any, promptIndex: number) => {
        const similarityScore = this._stringSimilarity.similarity(
          inputSentence,
          promptObj.prompt
        );

        if (similarityScore > bestMatch.similarity) {
          bestMatch = {
            libraryIndex,
            promptIndex,
            similarity: similarityScore,
          };
        }
      });
    });

    // Check if the best match meets the threshold
    if (bestMatch.similarity >= threshold) {
      return bestMatch;
    } else {
      return null; // No match found
    }
  }
  /**
 * @description Getting Error messages
 */
  errorMessage(error_code, enable_icon = false): string | null | boolean {
    error_code = error_code ? error_code : 'ERR_API_001'
    const errorConfig = this._aiService?.aiThemeConfig['RESPONSE-TEXT'].find(item => item?.info_code === error_code);
    if (enable_icon) {
      return errorConfig ? errorConfig?.icon_enable ?? true : true;
    }
    return errorConfig ? errorConfig?.info_html : null;
  }
  // should be removed
  setTypingTrue() {
    this.isTyping = true;
    this.onPromptDataScroll(true);
  }

  /**
  * @description On Prompt Data Scroll
  */
  async onPromptDataScroll(reset = false) {
    if (!this.searchDefaultPrompts?.length) {
      this.searchLazyLoadedList = [];
      return;
    }

    if (reset) {
      this.startIndex = 0;
      this.skip = 25;
      this.searchLazyLoadedList = [];

      const filterValue = this.promptSearchParams?.trim()?.toLowerCase() || '';
      this.filteredPrompts = filterValue
        ? this.searchDefaultPrompts.filter((filterItem) =>
          filterItem['prompt']?.toLowerCase().includes(filterValue)
        )
        : [...this.searchDefaultPrompts];

      this.assignSearchPromptsOnLazyLoading();
    } else {
      if (this.startIndex >= this.filteredPrompts?.length) return; // Stop when all data is loaded
      this.startIndex = this.skip;
      this.skip += 25;
      this.assignSearchPromptsOnLazyLoading();
    }
  }

  /**
 * @description Assigns prompts for lazy loading
 */
  assignSearchPromptsOnLazyLoading() {
    const newItems = this.filteredPrompts.slice(this.startIndex, this.skip);
    this.searchLazyLoadedList.push(...newItems);
  }

  /**
  * @description Set selected history Thread
  * @param {ObjectId} log
  * @param {int} dateIndex
  * @param {int} threadIndex
  */
  setSelectedHistoryThreadData(log, dateIndex, threadIndex) {
    this.selectedHistoryThreadData = log;
    this.selectedHistoryDateIndex = dateIndex;
    this.selectedHistoryThreadIndex = threadIndex;
    // this.setCurrentIndexToLatest();

  }

  /**
 * @description Set selected history Thread
 * @param {ObjedId} threadId
 */
  setSelectedHistoryThread(threadId) {
    this.historySelectedThreadId = threadId;
    if (this.isThreadLoading) {
      return;
    }
    this.isThreadLoading = true;

    let payload = {
      aid: this.profile.aid,
      oid: this.profile.oid,
      chat_thread_id: threadId,
    };

    this.currentThreadData = [];
    this.threadIdReference = '';
    this.promptSearchParams = '';
    this.isCheckedUserIntent = false;

    this.isReportsVisible = false;


    return new Promise((resolve, reject) => {
      this._aiService
        .retrieveThreadHistory(payload)
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res: any) => {
            if (res['err'] == false) {
              this.currentThreadData = res['response'];
              this.threadIdReference = res['response'] && res['response'][0] ? res['response'][0]?.thread_id : '';
              this.resetUiState(['isChatVisible']);
              setTimeout(() => {
                this.scrollToBottom();
              }, 500);
            } else {
              this.resetUiState(['isHomeScreenVisible']);
              this._toaster.showWarning('Warning ⚠️', res['msg'], 7000);
              let recordError = this._aiService.recordError({prompt: "while retrieving history threads", error: res })
            }
            this.isThreadLoading = false;
            resolve(true);
          },
          error: (err) => {
            this._toaster.showError(
              'Error',
              'Error in Fetching Session Data',
              7000
            );
            this.resetUiState(['isHomeScreenVisible']);
            this.isThreadLoading = false;
            reject();
          },
        });
    });
  }

  /**
* @description Rename Thread
*/
  renameThread() {
    let id = this.selectedHistoryThreadData?._id;
    let index = this.historyData?.findIndex((obj) => obj._id == id);
    let pinnedIndex = this.pinnedHistoryData?.findIndex((obj) => obj._id == id);
    if (index != -1) {
      this.historyData[index]['isRenameInProgress'] = true;
      this.groupHistoryData();
    } else if (pinnedIndex != -1) {
      this.pinnedHistoryData[pinnedIndex]['isRenameInProgress'] = true;
      this.groupHistoryData();
    }

    setTimeout((obj) => {
      if (index != -1) {
        const inputElement = document.getElementById(
          '#historyInputSearch-' +
          this.selectedHistoryDateIndex +
          '-' +
          this.selectedHistoryThreadIndex
        ) as HTMLInputElement;
        inputElement.focus();
        inputElement.select();
      } else if (pinnedIndex != -1) {
        const inputElement = document.getElementById(
          '#pinnedHistoryInputSearch-' + this.selectedHistoryThreadIndex
        ) as HTMLInputElement;
        inputElement.focus();
        inputElement.select();
      }
    }, 50);
  }

  /**
 * @description Rename thread API
 * @param {string} id
 * @param {string} currentValue
 */
  onRenameThread(id, currentValue, thread_id) {
    let payload = {
      aid: this.profile.aid,
      chat_thread_id: thread_id,
      heading: currentValue,
    };

    return new Promise((resolve, reject) => {
      this._aiService
        .changeDescriptionOfThread(payload)
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res: any) => {
            if (res['err'] == false) {
              let index = this.historyData.findIndex((obj) => obj?._id == id);
              if (index != -1) {
                this.historyData[index]['isRenameInProgress'] = false;
                this.groupHistoryData();
              }
              let pinnedIndex = this.pinnedHistoryData?.findIndex(
                (obj) => obj?._id == id
              );
              if (pinnedIndex != -1) {
                this.pinnedHistoryData[pinnedIndex]['isRenameInProgress'] =
                  false;
                this.groupHistoryData();
              }
            } else {
              this._toaster.showError('Error', res['msg'], 7000);
              let recordError = this._aiService.recordError({prompt: "while renaming the thread", error: res })
            }
            resolve(true);
          },
          error: (err) => {
            this._toaster.showError('Error', 'Error in Renaming Thread', 7000);
            reject();
          },
        });
    });
  }

  /**
* @description Archive or Unarchive Thread
* @param {boolean} isArchive
*/
  archiveOrUnarchiveThread(isArchive) {
    let payload = {
      aid: this.profile.aid,
      thread_id: [this.selectedHistoryThreadData?._id],
      archive: isArchive,
    };

    return new Promise((resolve, reject) => {
      this._aiService
        .archiveOrUnarchiveThread(payload)
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res: any) => {
            if (res['err'] == false) {
              let index = this.historyData.findIndex(
                (obj) => obj?._id == payload?.thread_id
              );
              if (index != -1) {
                this.historyData.splice(index, 1);
                this.groupHistoryData();
              }

              let pinnedHistoryIndex = this.pinnedHistoryData?.findIndex(
                (obj) => obj?._id == payload?.thread_id
              );
              if (pinnedHistoryIndex != -1) {
                this.pinnedHistoryData?.splice(pinnedHistoryIndex, 1);
              }

              this.resetToNewChatBasedOnCurrentThread(payload?.thread_id);
            } else {
              this._toaster.showError('Error', res['msg'], 7000);
            }
            resolve(true);
          },
          error: (err) => {
            this._toaster.showError('Error', 'Error in Archiving Thread', 7000);
            reject();
          },
        });
    });
  }
  /**
 * @description Reset to new chat based on current thread
 * @param {array} ids
 */
  resetToNewChatBasedOnCurrentThread(ids) {
    if (!this.isHomeScreenVisible) {
      if (this.currentThreadData && this.currentThreadData.length > 0) {
        let currentThreadId = this.currentThreadData[0]['thread_id'];
        if (ids.includes(currentThreadId)) {
          this.openNewChat();
        }
      }
    }
  }

  /**
* @description Delete Thread
*/
  deleteThread() {
    let payload = {
      aid: this.profile.aid,
      chat_thread_id: this.selectedHistoryThreadData?.thread_id,
    };

    return new Promise((resolve, reject) => {
      this._aiService
        .deleteThread(payload)
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res: any) => {
            if (res['err'] == false) {
              let index = this.historyData.findIndex(
                (obj) => obj?.thread_id == payload?.chat_thread_id
              );
              if (index != -1) {
                this.historyData.splice(index, 1);
                this.groupHistoryData();
              }

              let pinnedHistoryIndex = this.pinnedHistoryData?.findIndex(
                (obj) => obj?.thread_id == payload?.chat_thread_id
              );
              if (pinnedHistoryIndex != -1) {
                this.pinnedHistoryData?.splice(pinnedHistoryIndex, 1);
              }
              this.openNewChat();
              // this.resetToNewChatBasedOnCurrentThread([
              //   this.selectedHistoryThreadData?._id,
              // ]);
            } else {
              this._toaster.showError('Error', res['msg'], 7000);
              let recordError = this._aiService.recordError({prompt: "while deleting thread", error: res })
            }
            resolve(true);
          },
          error: (err) => {
            this._toaster.showError('Error', 'Error in Deleting Thread', 7000);
            reject();
          },
        });
    });
  }

  /**
 * @description Pin or Unpin Thread
 */
  pinOrUnpinThread() {
    if (
      this.pinnedHistoryData?.length == 4 &&
      !this.selectedHistoryThreadData?.is_pinned
    ) {
      return this._toaster.showInfo(
        'Info 📝',
        'You can pin up to 4 threads!',
        7000
      );
    }

    let payload = {
      aid: this.profile.aid,
      chat_thread_id: this.selectedHistoryThreadData?.thread_id,
      is_pinned: this.selectedHistoryThreadData?.is_pinned ? false : true,
    };

    return new Promise((resolve, reject) => {
      this._aiService
        .pinOrUnpinThread(payload)
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res: any) => {
            if (res['err'] == false) {
              if (payload?.is_pinned) {
                let index = this.historyData.findIndex(
                  (obj) => obj?.thread_id == payload?.chat_thread_id
                );
                if (index != -1) {
                  let removedItem = this.historyData
                    .splice(index, 1)
                    .map((item) => ({
                      ...item,
                      is_pinned: true,
                    }));
                  
                  this.pinnedHistoryData = [
                    ...(this.pinnedHistoryData || []),
                    ...removedItem,
                  ];
                  this.groupHistoryData();
                }
              } else {
                this.allHistory(true);
                this.allPinnedHistory();
              }
            } else {
              this._toaster.showError('Error', res['msg'], 7000);
              let recordError = this._aiService.recordError({prompt: "while pin and unpin thread", error: res })
            }
            resolve(true);
          },
          error: (err) => {
            this._toaster.showError('Error', 'Error in Pin/Unpin Thread', 7000);
            reject();
          },
        });
    });
  }

  /**
 * @description To Clear the Search Input
 */
  clearSearchInput(event?: MouseEvent) {
    if (!event) return; // Ensure event exists

    const target = event.target as HTMLElement;

    if (!target || target.closest('.overlay')) {
      return; // If clicking inside `.overlay`, don't close
    }
    setTimeout(() => {
      this.promptSearchParams = null;
      this.isTyping = false;
      this.searchPromptSkip = 0
    }, 100);
  }

  /**
 * @description Regenerate response
 * @param {object} data
 */
  async regenerateResponse(data, index) {
    // const childIndex = this.childIndexMap[index];
    // const targetItem = (childIndex !== undefined && data.children?.[childIndex - 1])
    //   ? data.children[childIndex - 1]
    //   : data;
  }

  /**
 * @description On search history
 */
  onEnterSearchHistory() {
    this.allHistory(true);
  }

  /**
 * @description On history data scroll
 */
  onHistoryDataScroll() {
    this.historySkip += 25;
    this.allHistory(false);
  }

  /**
   * @description To like and dislike a response
   * @param {boolean} value
   * @param {object} item
   */
  async likeAndDislikeResponse(value, item, index) {
    if (value === null) {
      return;
    } else {
      let payload = {
        chat_message_id: item.chat_message_id,
        chat_thread_id: item.thread_id,
        is_liked: value,
      };
      return new Promise((resolve, reject) => {
        this._aiService
          .likeAndDislikeResponse(payload)
          .pipe(takeUntil(this._onDestroy))
          .subscribe({
            next: (res: any) => {
              if (res['err'] == false) {
                item.is_liked = value;
              } else {
                this._toaster.showError('Error', res['msg'], 7000);
                let recordError = this._aiService.recordError({prompt: "while like and dislike", error: res })
              }
              resolve(true);
            },
            error: (err) => {
              this._toaster.showError(
                'Error',
                'Error in Updating Response',
                7000
              );
              reject();
            },
          });
      });


    }
  }


  startLoadingTextCycle() {
    this.loaderCurrentIndex = 0;
    this.currentLoadingText = this.loaderTexts[this.loaderCurrentIndex];
    this.textInterval = setInterval(() => {
      this.loaderCurrentIndex = (this.loaderCurrentIndex + 1) % this.loaderTexts?.length;
      this.currentLoadingText = this.loaderTexts[this.loaderCurrentIndex];
    }, 20000); 
  }


  adjustTextareaHeight(textArea: HTMLTextAreaElement): void {
    textArea.style.height = 'auto';
    textArea.style.height = `${textArea.scrollHeight}px`;
  }

  adjustCursorPosition(event: KeyboardEvent) {
    setTimeout(() => {
      const textarea = event.target as HTMLTextAreaElement;
      if (textarea) {
        textarea.focus();
        textarea.setSelectionRange(0, 0); 
      }
    }, 500);
  }
  
  
  /**
   * @description Scroll to bottom of the chat when opened
   */
  scrollToBottom() {
    const div = this.chatScrollContainer.nativeElement;
    div.scrollTop = div.scrollHeight;
  }

  /**
 * @description Gets UI configurations
 */
  async getHealthStatus() {
    return new Promise((resolve, reject) =>
      this._aiService
        .getHealthStatus()
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res) => {
            if (res['err'] == false) {
              resolve(true);
            }
            else{
              this._toaster.showError(
                'Error',
                'System is currently not available!',
                7000
              );
              let errorTrigger = this._aiService.notifyError({prompt: "From health status check", code: res })
              reject();
            }
          },
          error: (err) => {
            this._toaster.showError(
              'Error',
              'System is currently not available!',
              7000
            );
            reject();
            let recordError = this._aiService.recordError({prompt: "From health status check", error: err })
          },
        })
    );
  }

  /**
 * @description Trigger Intent Response
 */

  triggerIntentResponse(prompt){

    let payload = {
      prompt: prompt,
      chat_history: this.currentThreadData?.length > 1 
      ? this.currentThreadData.slice(0, this.currentThreadData.length - 1) 
      : ""
    }
    return new Promise((resolve, reject) => {
      this._aiService
        .getIntentResponse(payload)
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res: any) => {
            if (res['err'] == false) {

              if(res['response'] == 'response_intent_new'){
                let response_intent = [...this.currentThreadData]
                .slice(0, -1) // Exclude the last item
                .reverse()    // Reverse the remaining items
                .find(t => 
                  t.intent !== 'feedback_intent' && 
                  t.intent !== 'faq_agent' && 
                  t.intent !== 'response_intent'
                )?.intent;            
                this.currentThreadData[this.currentThreadData.length - 1]['is_new_intent'] = false;  
                this.triggerAgentExecutor(response_intent, prompt, 1, false)
              }
              else if(res['response'] == 'response_intent_follow_up'){
                this.currentThreadData[this.currentThreadData.length - 1]['is_new_intent'] = false;
                this.triggerAgentExecutor("response_intent", prompt, 1, false)
              }
              else if(res['response'] == 'feedback_intent'){
                // trigger feedback agent api
                this.currentThreadData[this.currentThreadData.length - 1]['is_new_intent'] = false;
                this.triggerAgentExecutor('feedback_intent', prompt, 1, false)
              }
              else if (res['is_multiple'] && !this.isCheckedUserIntent) {
                this.currentThreadData[this.currentThreadData.length - 1]['is_new_intent'] = true;
                let intents = _.pluck(res['intent'], 'intent');
                this.triggerAgentExecutorForMultiple(prompt, intents, true)
              }
              else {
                this.currentThreadData[this.currentThreadData.length - 1]['is_new_intent'] = true;
                this.triggerAgentExecutor(res['response'], prompt, 0, true)
              }
            }  else {
              this.processStarted = false;
              this.currentThreadData[this.currentThreadData.length - 1][
                'isError'
              ] = true;
              this.currentThreadData[this.currentThreadData.length - 1]['error_code'] = res?.error_code || 'ERR_API_001';
              this.isPromptApiInProgress = false;
              let recordError = this._aiService.recordError({prompt: prompt, error: res })
            }
            setTimeout(() => {
              // this.isPromptApiInProgress = false;
            }, 1500);
            resolve(true);
          },
          error: (err) => {
            this.processStarted = false;
            if(err?.status == 504 || err?.status == 503){
            let errorTrigger = this._aiService.notifyError({prompt: prompt, code: err?.status })
            }
            let recordError = this._aiService.recordError({prompt: prompt, error: err })
            this.currentThreadData[this.currentThreadData.length - 1][
              'isLoading'
            ] = false;
            this.currentThreadData[this.currentThreadData.length - 1][
              'isError'
            ] = true;
            this.currentThreadData[this.currentThreadData.length - 1]['error_code'] = 'ERR_API_001'
            setTimeout(() => {
              this.isPromptApiInProgress = false;
            }, 1500);
            reject();
          },
        });
    });
  }

  /**
 * @description Trigger Cache Response
 */

  async triggerCacheResponse(prompt){
    let thread_id = this.threadIdReference;
    if (!thread_id) {
      thread_id = uuidv4();
      this.threadIdReference = thread_id;
      this.currentThreadData[this.currentThreadData?.length - 1]['thread_id'] = thread_id;
      this.isNewThread = true;
    }
    else{
      this.isNewThread = false;
      this.currentThreadData[this.currentThreadData.length - 1]['thread_id'] = thread_id;
    }
    let payload = {
      chat_thread_id: thread_id,
      prompt: prompt
    }

    
    if (thread_id) {
      let healthCheck = await this.getHealthStatus();
      this.isPromptApiInProgress = true;
      return new Promise((resolve, reject) => {
        this._aiService
          .getCachedOutput(payload)
          .pipe(takeUntil(this._onDestroy))
          .subscribe({
            next: async (res: any) => {
              if (res['err'] == false) {
                this.processStarted = false;
                if(this.isNewThread){
                  let getChatHeading = await this._aiService.getChatHeading({chat_thread_id: thread_id, prompt: prompt}).toPromise()
                  this.allHistory(true)
                }

                this.currentThreadData[this.currentThreadData.length - 1]['intent'] = res['response']['intent'];

                this.currentThreadData[this.currentThreadData.length - 1]['response'] =
                  res['response']['output'];

                this.currentThreadData[this.currentThreadData.length - 1]['chat_message_id'] =
                  res['response']['chat_message_id'];

                this.currentThreadData[this.currentThreadData.length - 1][
                  'isReady'
                ] = true;
                this.currentThreadData[this.currentThreadData.length - 1][
                  'isLoading'
                ] = false;
                setTimeout(() => {
                  this.currentThreadData[this.currentThreadData.length - 1][
                    'isReady'
                  ] = false;
                  this.isPromptApiInProgress = false;
                }, 1500);

              } else {
                let triggerIntent = this.triggerIntentResponse(prompt)
              }
              resolve(true);
            },
            error: async(err) => {
              this.processStarted = false;
              if (err?.status == 504 || err?.status == 503) {
                let errorTrigger = this._aiService.notifyError({ prompt: prompt, code: err?.status })
              }
              let recordError = this._aiService.recordError({prompt: prompt, error: err })
              if(this.isNewThread){
                let getChatHeading = await this._aiService.getChatHeading({chat_thread_id: thread_id, prompt: prompt}).toPromise()
                this.allHistory(true)
              }
              this.currentThreadData[this.currentThreadData.length - 1][
                'isLoading'
              ] = false;
              this.currentThreadData[this.currentThreadData.length - 1][
                'isError'
              ] = true;
              this.currentThreadData[this.currentThreadData.length - 1]['error_code'] = 'ERR_API_001'
              setTimeout(() => {
                this.isPromptApiInProgress = false;
              }, 1500);
              reject();
            },
          });
      });
    }

  }





}





