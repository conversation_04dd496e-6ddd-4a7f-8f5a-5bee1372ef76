<div class="header-styles">
  <mat-toolbar>
    <mat-toolbar-row class="mt-1">
      <!-- <button class="alignit" mat-icon-button>
        <mat-icon style="color:#66615B !important; font-size: 21px !important">menu</mat-icon>
      </button> -->

      <span class="p-0 col-8 toolbar-spacer">
        <app-breadcrumb></app-breadcrumb>
      </span>

      <!-- <span class="col-2 server-name" *ngIf="serverName != ''" [matTooltip]="serverNameTooltip">{{ serverName }}</span>

      <span *ngIf="serverName != ''" class="col-2"></span>-->

      <!-- <span class="col-4"></span> -->

      <ng-container *appAcl="'AI Chatbot'">
        <div *ngIf="isAiIconVisible" class="ai-icon" (click)="openKaisChatbotDialog()" [ngStyle]="{'margin-right': isUpdateAvailable ? '18px' : ''}">
          <img class="image" [src]="aiThemeConfig['AI-ICON']"/>
        </div>
      </ng-container>

      <ng-container *ngIf="isHeaderRecordVisible && headerRecordData && headerRecordData['header_info']">
        <div class="header-main" [tooltip]="headerContent" [options]="{ 'maxWidth': 300 }" content-type="template" (click)="openRecord()">
          <div class="header-label">
            {{ headerRecordData['header_label'] }}
          </div>
          <div class="header-info">
            {{ headerRecordData['header_info'] }}
          </div>
        </div>
        <ng-template #headerContent style="background-color: #1e2733">
          <ng-container *ngIf="headerRecordData['meta_info']">
            <ng-container *ngFor="let item of headerRecordData['meta_info'] | keyvalue">
              <div class="row">
                {{ item.key }} : <span class="pl-1" style="display: -webkit-box; max-width: 12vw; -webkit-line-clamp: 2;
                -webkit-box-orient: vertical; overflow: hidden; text-overflow: ellipsis;"> {{ item.value }} </span>
              </div>
            </ng-container>
          </ng-container>
        </ng-template>
      </ng-container>
      <div
        *ngIf="isSearchBarVisible"
        class="search-a"
        id="searchbar"
        (click)="displayOverlay()"
      >
        <mat-icon>search</mat-icon>&nbsp;&nbsp;Search
      </div>

      <div
        class="badge pulsate"
        matTooltip="click on the update icon to download latest system updates"
        *ngIf="isUpdateAvailable"
      >
        !
      </div>

      <button
        class="alignit mr-2"
        mat-icon-button
        matTooltip="system update"
        (click)="hardRefresh()"
        *ngIf="isUpdateAvailable"
      >
        <mat-icon style="color: #66615b !important; font-size: 21px !important"
          >system_update</mat-icon
        >
      </button>

      <button
        class="alignit"
        *appAcl="'Golbal Create Button'"
        mat-icon-button
        (click)="routeCreate()"
        matTooltip="Create"
      >
        <mat-icon style="color: #66615b !important; font-size: 21px !important"
          >add_circle</mat-icon
        >
      </button>

      <button 
        *appAcl="'Inbox'"
        class="alignit mr-2"  
        mat-icon-button 
        (click)="openInbox()"
        matTooltip="Inbox">
        <mat-icon style="color: #66615b !important; font-size: 21px !important">
          inbox</mat-icon>
      </button>

      <button
        class="alignit mr-2"
        *appAcl="'Upload Button'"
        mat-icon-button
        matTooltip="Upload"
        (click)="openAdminPortal()"
      >
        <mat-icon style="color: #66615b !important; font-size: 21px !important"
          >cloud_upload</mat-icon
        >
      </button>

      <button
        class="alignit mr-2"
        mat-icon-button
        matTooltip="Admin"
        *ngIf="isAdminAccess"
        (click)="openAdminOnlyPortal()"
      >
        <mat-icon style="color: #66615b !important; font-size: 21px !important"
          >manage_accounts</mat-icon
        >
      </button>

      <button
        class="alignit mr-2"
        mat-icon-button
        (click)="enterMachineHours('in')"
        matTooltip="InTime"
        [disabled]="disableInButton"
        *ngIf="isRFIDDisplay"
      >
        <mat-icon style="color: #66615b !important; font-size: 21px !important"
          >login</mat-icon
        >
      </button>

      <button
        class="alignit mr-2"
        (click)="enterMachineHours('out')"
        mat-icon-button
        matTooltip="OutTime"
        [disabled]="disableOutButton"
        *ngIf="isRFIDDisplay"
      >
        <mat-icon style="color: #66615b !important; font-size: 21px !important"
          >logout</mat-icon
        >
      </button>

      <button
        class="alignit mr-2"
        mat-icon-button
        matTooltip="Notifications"
        (click)="openNotifications()"
      >
        <mat-icon style="color: #66615b !important; font-size: 21px !important"
          >notifications</mat-icon
        >
      </button>

      <button class="userimg" mat-icon-button [matMenuTriggerFor]="userItems">
        <header-user-image [id]="user.oid"></header-user-image>
      </button>

      <p class="profile" [matMenuTriggerFor]="userItems">
        {{ user.name }}
      </p>

      <mat-menu #userItems="matMenu">
        <button mat-menu-item class="drop-btn" routerLink="/main/help">
          <mat-icon
            style="font-size: 18px !important; margin-right: 6px !important"
            >help</mat-icon
          >
          <span>Help</span>
        </button>
        <button mat-menu-item (click)="logout()" class="drop-btn">
          <mat-icon
            style="font-size: 21px !important; margin-right: 6px !important"
            >exit_to_app</mat-icon
          >
          <span>Logout</span>
        </button>
        <button
          mat-menu-item
          (click)="syncMail(false)"
          class="drop-btn"
          *ngIf="enableMailSyncBtn"
        >
          <mat-icon
            style="font-size: 21px !important; margin-right: 6px !important"
            >cloud_sync</mat-icon
          >
          <span>Sync my mail box</span>
        </button>
      </mat-menu>
    </mat-toolbar-row>
  </mat-toolbar>
</div>
