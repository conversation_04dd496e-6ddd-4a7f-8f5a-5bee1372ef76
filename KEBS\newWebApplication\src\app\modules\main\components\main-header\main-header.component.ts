import { ApplicationRef, Component, OnInit } from '@angular/core';
import { DashboardService } from 'src/app/modules/dashboard/services/dashboard.service';
import { LoginService } from 'src/app/services/login/login.service';
import { AdminProgramService } from 'src/app/modules/admin-programs/services/admin-program.service';

// Services
import { MatDialog } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { O365LoginService } from 'src/app/services/login/o365-login.service';
import { GraphApiService } from 'src/app/services/graph/graph-api.service';
import { SwUpdate } from '@angular/service-worker';
import { interval, Subject } from 'rxjs';
import Swal from 'sweetalert2';
import { MatSnackBar } from '@angular/material/snack-bar';

import * as moment from 'moment';
import { TsSubmissionPrimaryService } from 'src/app/modules/timesheet/features/ts-submission/services/ts-submission-primary.service';
import { UtilityService } from 'src/app/services/utility/utility.service';
import { pluck, takeUntil } from 'rxjs/operators';
import { TsPrimaryService } from 'src/app/modules/timesheet/services/ts-primary.service';
import { Overlay, OverlayRef } from '@angular/cdk/overlay';
import { ComponentPortal } from '@angular/cdk/portal';
import { SearchSuggestPopupComponent } from './search-suggest-popup/search-suggest-popup.component';
import { SearchService } from 'src/app/services/search/search.service';

import { version } from '../../../../../../package.json';
import { UserExperienceCustomizationService } from '../../services/user-experience/user-experience-customization.service';
import { RolesService } from 'src/app/services/acl/roles.service';
import { LcdpService } from 'src/app/modules/lcdp/services/lcdp.service';

import { ToasterService } from 'src/app/modules/applicant-tracking-system/shared-components/ats-custom-toast/toaster.service';
import { AiServiceService } from '../../services/ai-service/ai-service.service';
import { KebsDialog } from 'src/app/modules/main/components/main-header/ai-features/custom-dialog/custom-dialog';

@Component({
  selector: 'main-header',
  templateUrl: './main-header.component.html',
  styleUrls: ['./main-header.component.scss'],
})
export class MainHeaderComponent implements OnInit {
  public user: any = {};
  quote: any;
  accountDetail: any;
  editDataForPatching: any;
  leadDetail: any;
  checked: boolean;
  isUpdateAvailable: boolean = false;

  serverName = '';
  serverNameTooltip = '';
  isAdminAccess: boolean = false;
  enableMailSyncBtn: boolean = false;
  disableInButton: boolean = false;
  disableOutButton: boolean = false;
  tsProperties: any;
  isRFIDDisplay: boolean = false;
  suggestionOverlayRef?: OverlayRef;
  protected _onDestroy = new Subject<void>();
  isSearchBarVisible: boolean = false;
  isHeaderRecordVisible: boolean = false;
  headerRecordData = {};

  isAiIconVisible = false;
  aiThemeConfig = {};

  constructor(
    private dashboardService: DashboardService,
    private _loginService: LoginService,
    public dialog: MatDialog,
    private router: Router,
    private _o365: O365LoginService,
    private _adminService: AdminProgramService,
    private _graphApiService: GraphApiService,
    private snackbar: MatSnackBar,
    private update: SwUpdate,
    private appRef: ApplicationRef,
    private tsSubmissionPrimaryService: TsSubmissionPrimaryService,
    private utilityService: UtilityService,
    private tsPrimaryService: TsPrimaryService,
    private overlay: Overlay,
    private _search: SearchService,
    private _usrExp: UserExperienceCustomizationService,
    private _role: RolesService,
    private _lcdpService: LcdpService,
    private _toaster: ToasterService,
    private _aiService: AiServiceService,
    private _customDialog: KebsDialog,
  ) {
    this.checkIfServerUrlContainsLocalOrDev();
    this.updateClient();
    this.checkUpdate();
  }

  displayOverlay() {
    if (this.suggestionOverlayRef?.hasAttached()) {
      this.suggestionOverlayRef.detach();
    }
    const target = document.querySelector('#searchbar') as HTMLElement;
    this.suggestionOverlayRef = this.overlay.create({
      hasBackdrop: true,
      backdropClass: 'cdk-overlay-transparent-backdrop',
      panelClass: 'mat-elevation-z1',
      positionStrategy: this.overlay
        .position()
        .flexibleConnectedTo(target)
        .withPositions([
          {
            offsetX:60,
            originX: 'end',
            originY: 'top',
            overlayX: 'end',
            overlayY: 'top',
            offsetY: 0,
          },
        ]),
    });

    const popupComponentPortal = new ComponentPortal(
      SearchSuggestPopupComponent
    );

    this.suggestionOverlayRef.attach(popupComponentPortal);
    this.suggestionOverlayRef
      .backdropClick()
      .subscribe(() => this.suggestionOverlayRef.detach());
  }

  updateClient() {
    if (!this.update.isEnabled) {
      console.log('Not Enabled');
      return;
    }
    this.update.available.subscribe((event) => {
      console.log(`current`, event.current, `available `, event.available);
      this.isUpdateAvailable = true;
    });

    this.update.activated.subscribe((event) => {
      console.log(`current`, event.previous, `available `, event.current);
    });
  }

  openDialog() {
    Swal.fire({
      title: 'Updates available',
      text: 'Install the latest update to continue Kebsing !',
      icon: 'info',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Install update',
      allowOutsideClick: false,
    }).then((result) => {
      if (result.isConfirmed) {
        this._usrExp.logVersionUpdate(version).subscribe((res) => {
          Swal.fire(
            'Updated',
            'Your have installed the latest update.',
            'success'
          ).then(() => {
            this.update.activateUpdate().then(() => {
              window.location.reload();
            });
          });
        });
      }
    });
  }

  checkUpdate() {
    this.appRef.isStable.subscribe((isStable) => {
      if (isStable) {
        const timeInterval = interval(8 * 60 * 60 * 1000);

        timeInterval.subscribe(() => {
          this.update.checkForUpdate().then(() => console.log('checked'));
          console.log('update checked');
        });
      }
    });
  }

  hardRefresh() {
    this.openDialog();
  }

  checkIfServerUrlContainsLocalOrDev() {
    if (window.location.href.includes('localhost')) {
      this.serverName = 'LOCAL : DEV';

      this.serverNameTooltip = 'LOCAL Server : DEV Database';
    } else if (window.location.href.includes('dev.kebs.app'))
      this.dashboardService.getServerEnvironment().subscribe((res) => {
        let dbName = '';

        if (res['messType'] == 'S') dbName = res['data'];

        this.serverName =
          'DEV' +
          (dbName == '' ? ' : ERROR' : dbName == 'dev' ? ' : DEV' : ' : PRD');

        this.serverNameTooltip =
          'DEV Server' +
          (dbName == ''
            ? ' : ERROR Database'
            : dbName == 'dev'
            ? ' : DEV Database'
            : ' : PRD Database');
      });
    else if (window.location.href.includes('kebs.app')) {
      this.serverName = '';
      this.serverNameTooltip = '';
    }
  }

  async ngOnInit(): Promise<void> {
    this.user = this._loginService.getProfile().profile;
    this.checkSearchBarVisibility();
    this._search.getAllRoleIndices(this.user).then(() =>{
      this._search.getIndexAuthFilters();
    })
    let req = {
      oid: this.user.oid,
    };
    this._adminService.checkForAdminAccess(req).then(
      (res: any) => {
        this.isAdminAccess = res.messType == 'S' ? true : false;
      },
      (err) => {
        console.log(err);
      }
    );

    this._adminService.checkForMailSyncAccess().then(
      (res: any) => {
        if (res && res.messType == 'Y') {
          this.enableMailSyncBtn = true;

          if (res.messData.is_auto_sync) {
            this.syncMail(true);
          }
        } else {
          this.enableMailSyncBtn = false;
        }
      },
      (err) => {
        console.log(err);
      }
    );

    this.tsPrimaryService
      .getTimesheetProperties(this.user.aid)
      .pipe(takeUntil(this._onDestroy))
      .subscribe(
        async (res) => {
          if (res['messType'] == 'S' && res['data'].length > 0) {
            this.tsProperties = res['data'][0];
            this.isRFIDDisplay =
              this.tsProperties.is_for_rfid == 1 ? true : false;
          }
        },
        (err) => {
          console.log(err);
        }
      );

    this.getLcdpRecord();

    this._lcdpService.getHeaderRecordObservable.pipe(takeUntil(this._onDestroy)).subscribe(res => {

      if (res)
        this.getLcdpRecord();

    });

    Promise.all([this.getUiConfiguration('aiTheme')]).then((res) => {
      this.isAiIconVisible = true;
    }).catch((err) => {
      this.isAiIconVisible = false;
    })

  }

  getLcdpRecord = () => {

    this.isHeaderRecordVisible = false;
    this.headerRecordData = {};

    if (this._role.roles && this._role.roles.length) {

      const headerRecordAuth = this._role.roles.find(val => val['application_id'] == 224 && val['object_id'] == 554);

      if (headerRecordAuth) {

        this._lcdpService.getHeaderRecordForEmployee()
          .pipe(takeUntil(this._onDestroy))
          .subscribe(res => {

            if (res["messType"] == "S" && res["data"]) {

              this.isHeaderRecordVisible = true;

              this.headerRecordData = res["data"];

              this.headerRecordData['metaInfo'] = "";

              if (this.headerRecordData['meta_info'] && Object.keys(this.headerRecordData['meta_info']).length) {

                const results = [];

                Object.keys(this.headerRecordData['meta_info']).reduce((prev, curr) => {
                  prev.push(`<p>${curr}: ${this.headerRecordData['meta_info'][curr]}</p>`);
                  return prev;
                }, results);

                this.headerRecordData['metaInfo'] = results.join('');

              }

            }

          },
            err => {
              this.isHeaderRecordVisible = false;
              console.log(err);
            });

      }

    }

  }

  logout() {
    let decodedToken: any = this._loginService.decodedToken;
    if (decodedToken.isO365) this._o365.oLogout();
    else
      this._loginService.logout().subscribe((res) => {
        this.router.navigateByUrl('/login');
      });
  }

  async openNotifications() {
    const { NotificationPanelComponent } = await import(
      'src/app/modules/shared-lazy-loaded-components/notification-panel/notification-panel.component'
    );
    const dialogRef = this.dialog.open(NotificationPanelComponent, {
      height: '100%',
      width: '30%',
      position: {
        right: '0px',
      },
    });
  }

  routeCreate = () => {
    this.router.navigate(['', { outlets: { auxName: 'aux' } }], {
      skipLocationChange: true,
    });
  };

  openAdminPortal = () => {
    this.router.navigateByUrl('/main/admin-programs');
  };

  openAdminOnlyPortal = () => {
    this.router.navigateByUrl('/main/admin-programs/adminSettings');
  };

  syncMail = async (isAutoSyncEnabled) => {
    let o365Token = await this._graphApiService.getO365Token();
    this._adminService.syncMailBox(o365Token).subscribe(
      (res: any) => {
        if (res.messType == 'S') {
          if (!isAutoSyncEnabled) {
            Swal.fire({
              icon: 'success',
              title: 'Your mail data has been synced with KEBS',
              showConfirmButton: false,
              timer: 1500,
            });
          }
        } else {
          this.snackbar.open(`Error while syncing mail with KEBS`, 'Dismiss', {
            duration: 3000,
          });
          console.log(res);
        }
      },
      (err) => {
        this.snackbar.open(`Error while syncing mail with KEBS`, 'Dismiss', {
          duration: 3000,
        });
        console.log(err);
      }
    );
  };
  /**
   * @param  {} data
   * @description To send the data of intime and outtime from UI to backend.
   */
  enterMachineHours(data) {
    let inTime, outTime, date, oid, inTimeForRFID, outTimeForRFID, text;
    if (data == 'in') {
      inTime = moment().format('HH:mm');
      inTimeForRFID = moment().format('YYYY-MM-DD hh:mm A');
      text = 'InTime';
    }
    if (data == 'out') {
      outTime = moment().format('HH:mm');
      outTimeForRFID = moment().format('YYYY-MM-DD hh:mm A');
      text = 'outTime';
    }
    date = moment().format('YYYY-MM-DD');
    oid = this.user.oid;

    this.tsSubmissionPrimaryService
      .insertMachineHours(
        inTime,
        outTime,
        date,
        oid,
        data,
        inTimeForRFID,
        outTimeForRFID
      )
      .pipe(takeUntil(this._onDestroy))
      .subscribe(
        async (res) => {
          if (res['messType'] == 'S' && res['data']) {
            this.utilityService.showToastMessage(
              'Your ' + text + ' has been updated successfully '
            );

            if (
              data == 'in' &&
              res['dataRes'][0].in_allowed &&
              res['dataRes'][0].out_allowed
            ) {
              res['dataRes'][0].in_allowed = false;
            } else if (
              data == 'out' &&
              res['dataRes'][0].in_allowed &&
              res['dataRes'][0].out_allowed
            ) {
              res['dataRes'][0].out_allowed = false;
            }
            if (res['dataRes']) {
              this.disableInButton = !res['dataRes'][0].in_allowed;
              this.disableOutButton = !res['dataRes'][0].out_allowed;
            }
            // else if(res['data'] == 'out')
            // {
            //   this.disableInButton = false;
            //   this.disableOutButton = true;
            // }
          } else if (res['messType'] == 'E') {
            this.snackbar.open(res['messText'], 'Dismiss', { duration: 3000 });
          }
        },
        (err) => {
          this.snackbar.open(`Error While Updating time details`, 'Dismiss', {
            duration: 3000,
          });
          console.log(err);
        }
      );
  }

  checkSearchBarVisibility = () => {
    let tid = this._loginService.getProfile().profile.tid;
    this._search
      .getSearchBarEnabledTenants()
      .pipe(pluck('data'))
      .subscribe((res: any) => {
        let featureEnabledTenants = res.feature_enabled_tenants;
        if(featureEnabledTenants.includes(tid) == true)
        this.isSearchBarVisible = true;
        // this._search.getSearchBarRoles().subscribe((result: any) => {
        //   if (
        //     result.messType == 'S' &&
        //     featureEnabledTenants.includes(tid) == true
        //   ) {
        //     this.isSearchBarVisible = true;
        //   }
        // });
      });
  };

  openInbox = () => {
    let kebsHomePage = 'KEBS Homepage'
    if (this._role.checkApplicationForRoles(kebsHomePage)) {
      this.router.navigateByUrl('/main/home/<USER>');
    } else {
      this.router.navigateByUrl('/main/inbox');
    }
  };
  changeInattachments(contextId) {
    console.log(contextId);
  }

  openRecord = () => {

    if (this.headerRecordData && this.headerRecordData['record_link']) {

      const navigationUrl = window.location.origin + this.headerRecordData['record_link'];

      window.open(navigationUrl);

    }

  }

  /**
   * @description Open AI Chatbot
   */
  async openAiChatbotDialog() {
    const { ChatbotDialogComponent } = await import(
      'src/app/modules/main/components/main-header/ai-features/chatbot-dialog/chatbot-dialog.component'
    );

    this._customDialog.open(ChatbotDialogComponent, {
      maxWidth:'100vw',
      data: { aiThemeConfig: this.aiThemeConfig },
      position: { right: '0px', bottom: '0px' },
      animation: {
        entryAnimation: {
          keyframes: [
            { transform: 'translateY(100%)' },
            { transform: 'translateY(0)' },
          ],
          keyframeAnimationOptions: {
            duration: 500,
            easing: 'ease-in-out',
          },
        },
        exitAnimation: {
          keyframes: [
            { transform: 'translateY(0)' },
            { transform: 'translateY(100%)' },
          ],
          keyframeAnimationOptions: {
            duration: 250,
            easing: 'ease-in-out',
          },
        },
      },
    });
  }

  /**
   * @description Gets UI configurations
   * @param {string} key
   */
  async getUiConfiguration(key) {
    return new Promise((resolve, reject) =>
      this._aiService
        .getUiConfiguration(key)
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res) => {
            if (res['err'] == false) {
              if (key == 'aiTheme') {
                this.aiThemeConfig = res['data'];
                this._aiService.aiThemeConfig = res['data']
              }
            }
            resolve(true);
          },
          error: (err) => {
            this._toaster.showError(
              'Error',
              err.message ? err.message : 'UI Configuration Retrieval Failed!',
              7000
            );
            reject();
          },
        })
    );
  }

  
  /**
 * @description Open AI Chatbot
 */
  async openKaisChatbotDialog() {

    let healthCheck = await this.getHealthStatus();
    const { KaisLandingPageComponent } = await import(
      'src/app/modules/main/components/main-header/kais/kais-landing-page/kais-landing-page.component'
    );
    this._customDialog.open(KaisLandingPageComponent, {
      maxWidth:'100vw',
      data: { aiThemeConfig: this.aiThemeConfig },
      position: { right: '0px', bottom: '0px' },
      animation: {
        entryAnimation: {
          keyframes: [
            { transform: 'translateY(100%)' },
            { transform: 'translateY(0)' },
          ],
          keyframeAnimationOptions: {
            duration: 500,
            easing: 'ease-in-out',
          },
        },
        exitAnimation: {
          keyframes: [
            { transform: 'translateY(0)' },
            { transform: 'translateY(100%)' },
          ],
          keyframeAnimationOptions: {
            duration: 250,
            easing: 'ease-in-out',
          },
        },
      },
    });
  }

  /**
 * @description Gets UI configurations
 */
  async getHealthStatus() {
    return new Promise((resolve, reject) =>
      this._aiService
        .getHealthStatus()
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res) => {
            if (res['err'] == false) {
              resolve(true);
            }
            else{
              this._toaster.showError(
                'Error',
                'System is currently not available!',
                7000
              );
              let errorTrigger = this._aiService.notifyError({prompt: "From health status check", code: res })
              reject();
            }
          },
          error: (err) => {
            this._toaster.showError(
              'Error',
              'System is currently not available!',
              7000
            );
            reject();
            let recordError = this._aiService.recordError({prompt: "From health status check", error: err })
          },
        })
    );
  }
}
