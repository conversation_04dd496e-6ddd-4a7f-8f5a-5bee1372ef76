import { Pipe, PipeTransform } from '@angular/core';
import { Router } from '@angular/router';

@Pipe({
  name: 'subSideNavSelectedApp',
  pure: false,
})
export class SubSideNavSelectedApp implements PipeTransform {
  constructor(private _router: Router) {}

  transform(item, isMainModule) {
    if (!item.link || item.link.trim() === '') return false;

    const currentUrl = this._router.url.split('?')[0];
    const currentRouterSegments = this.removeDynamicSegments(
      currentUrl.split('/').filter((segment) => segment !== '')
    );

    if (isMainModule) {
      if (item?.sub_menu && item?.sub_menu.length > 0) {
        for (let sub_menu_item of item?.sub_menu) {
          const incomingUrl = sub_menu_item.link.split('?')[0];
          const incomingRouterSegments = this.removeDynamicSegments(
            incomingUrl.split('/').filter((segment) => segment !== '')
          );

          if (
            this.isPrefixMatch(currentRouterSegments, incomingRouterSegments)
          ) {
            return true;
          }
        }
        return false;
      } else {
        const incomingUrl = item.link.split('?')[0];
        const incomingRouterSegments = this.removeDynamicSegments(
          incomingUrl.split('/').filter((segment) => segment !== '')
        );

        return this.isPrefixMatch(
          currentRouterSegments,
          incomingRouterSegments
        );
      }
    } else {
      const incomingUrl = item.link.split('?')[0];
      const incomingRouterSegments = this.removeDynamicSegments(
        incomingUrl.split('/').filter((segment) => segment !== '')
      );

      return this.isPrefixMatch(currentRouterSegments, incomingRouterSegments);
    }
  }

  removeDynamicSegments(segments: string[]): string[] {
    return segments.filter((segment) => !this.isDynamicSegment(segment));
  }

  isDynamicSegment(segment: string): boolean {
    // Check if the segment is only numbers or a mix of numbers and alphabets
    const onlyNumbersRegex = /^\d+$/;
    const mixedRegex = /^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z\d]+$/;
    return onlyNumbersRegex.test(segment) || mixedRegex.test(segment);
  }

  isSegmentsMatch(segments1: string[], segments2: string[]): boolean {
    return (
      segments1.every((segment) => segments2.includes(segment)) &&
      segments2.every((segment) => segments1.includes(segment))
    );
  }

  isPrefixMatch(segments1: string[], segments2: string[]): boolean {
    if (segments1.length < segments2.length) return false;

    for (let i = 0; i < segments2.length; i++) {
      if (segments1[i] !== segments2[i]) return false;
    }

    return true;
  }
}
