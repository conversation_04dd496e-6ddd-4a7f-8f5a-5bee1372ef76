import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { MainComponent } from './main.component';
import { MainRouteGuard } from './main-route.guard';
const routes: Routes = [
  {
    path: '',
    component: MainComponent,
    children: [
      {
        path: '',
        // canActivate: [MainRouteGuard],
        pathMatch: 'full',
        redirectTo: 'home'
      },
      {
        path: 'create',
        outlet: 'globalCreation',
        loadChildren: () =>
          import(
            '../main/components/main-header/features/global-creation/global-creation.module'
          ).then((m) => m.GlobalCreationModule),
      },
      {
        path: 'dashboard',
        loadChildren: () =>
          import('../dashboard/dashboard.module').then(
            (m) => m.DashboardModule
          ),
        data: {
          breadcrumb: 'Dashboard',
        },
      },
      {
        path: 'reports',
        loadChildren: () =>
          import('../reports/reports.module').then((m) => m.ReportsModule),
        data: {
          breadcrumb: 'Reports',
        },
      },
      {
        path: 'project',
        loadChildren: () =>
          import('../projects/projects.module').then((m) => m.ProjectsModule),
        data: {
          breadcrumb: 'Projects',
        },
      },
      // {
      //   path: 'project3',
      //   loadChildren: () =>
      //     import('../projects-v3/projects-v3.module').then((m) => m.ProjectsV3Module),
      //   data: {
      //     breadcrumb: 'Projects',
      //   },
      // },
      {
        path: 'timesheet',
        loadChildren: () =>
          import('../timesheet/timesheet.module').then(
            (m) => m.TimesheetModule
          ),
        data: {
          breadcrumb: 'Timesheet',
        },
      },
      {
        path: 'accounts',
        loadChildren: () =>
          import('../account-sales/account-sales.module').then(
            (m) => m.AccountSalesModule
          ),
        data: {
          breadcrumb: 'Accounts',
        },
      },
      {
        path: 'leads',
        loadChildren: () =>
          import('../leads/leads.module').then((m) => m.LeadsModule),
        data: {
          breadcrumb: 'Leads',
        },
      },
      {
        path: 'expenses',
        loadChildren: () =>
          import('../expenses/expenses.module').then((m) => m.ExpensesModule),
        data: {
          breadcrumb: 'Expenses',
        },
      },
      {
        path: 'invoice',
        loadChildren: () =>
          import('../invoice/invoice.module').then((m) => m.InvoiceModule),
        data: {
          breadcrumb: 'Invoices',
        },
      },
      {
        path: 'contacts',
        loadChildren: () =>
          import('../contacts/contacts.module').then((m) => m.ContactsModule),
        data: {
          breadcrumb: 'Contacts',
        },
      },
      {
        path: 'opportunities',
        loadChildren: () =>
          import('../opportunities/opportunities.module').then(
            (m) => m.OpportunitiesModule
          ),
        data: {
          breadcrumb: 'Opportunities',
        },
      },
      {
        path: 'cta',
        loadChildren: () =>
          import('../cta/cta.module').then((m) => m.CtaModule),
        data: {
          breadcrumb: 'CTA',
        },
      },
      {
        path: 'human-resource',
        loadChildren: () =>
          import('../human-resource/human-resource.module').then(
            (m) => m.HumanResourceModule
          ),
        data: {
          breadcrumb: 'HR',
        },
      },
      {
        path: 'admin-programs',
        loadChildren: () =>
          import('../admin-programs/admin-programs.module').then(
            (m) => m.AdminProgramsModule
          ),
        data: {
          breadcrumb: 'Admin Programs',
        },
      },
      {
        path: 'help',
        loadChildren: () =>
          import('../help/help.module').then((m) => m.HelpModule),
        data: {
          breadcrumb: 'Help',
        },
      },
      {
        path: 'appraisal',
        loadChildren: () =>
          import('../performance-appraisal/performance-appraisal.module').then(
            (m) => m.PerformanceAppraisalModule
          ),
        data: {
          breadcrumb: 'Appraisal',
        },
      },
      {
        path: 'ams',
        loadChildren: () =>
          import('../ams/ams.module').then((m) => m.AmsModule),
        data: {
          breadcrumb: 'AMS Tickets',
        },
      },
      {
        path: 'awards',
        loadChildren: () =>
          import('../performance-awards/performance-awards.module').then(
            (m) => m.PerformanceAwardsModule
          ),
        data: {
          breadcrumb: 'Awards',
        },
      },
      {
        path: 'app-builder',
        loadChildren: () =>
          import('../app-builder/app-builder.module').then(
            (m) => m.AppBuilderModule
          ),
        data: {
          breadcrumb: 'App Builder',
        },
      },
      {
        path: 'okr',
        loadChildren: () =>
          import('../app-okr/app-okr.module').then((m) => m.AppOkrModule),
        data: {
          breadcrumb: 'OKR',
        },
      },
      {
        path: 'isa',
        loadChildren: () =>
          import('../isa/isa.module').then((m) => m.InternalStakeHolderModule),
        data: {
          breadcrumb: 'Recruitment',
        },
      },
      {
        path: 'rmg',
        loadChildren: () =>
          import('../rmg/rmg.module').then((m) => m.RMGLandingModule),
        data: {
          breadcrumb: 'RMG',
        },
      },
      {
        path: 'p&t',
        loadChildren: () =>
          import('../payroll-tax/payroll-tax.module').then(
            (m) => m.PayrollTaxModule
          ),
        data: {
          breadcrumb: 'Payroll and Tax',
        },
      },
      {
        path: 'pms',
        loadChildren: () =>
          import('../pms/pms.module').then((m) => m.PmsModule),
        data: {
          breadcrumb: 'PMS',
        },
      },
      {
        path: 'lms-instructor',
        loadChildren: () =>
          import('../lms-instructor/lms-instructor.module').then(
            (m) => m.LMSInstructorModule
          ),
        data: {
          breadcrumb: 'Authorer',
        },
      },
      {
        path: 'lms-learner',
        loadChildren: () =>
          import('../lms-learner/lms-learner.module').then(
            (m) => m.LmsLearnerModule
          ),
        data: {
          breadcrumb: 'LMS',
        },
      },
      {
        path: 'compliance',
        loadChildren: () =>
          import('../compliance-management/compliance-management.module').then(
            (m) => m.ComplianceManagementModule
          ),
        data: {
          breadcrumb: 'Compliance',
        },
      },
      {
        path: 'collector',
        loadChildren: () =>
          import('../collector/collector.module').then(
            (m) => m.CollectorModule
          ),
        data: {
          breadcrumb: 'Collector',
        },
      },
      {
        path: 'lcdp/:appId/:label/:lcdpApplicationId',
        loadChildren: () =>
          import('../lcdp/lcdp.module').then((m) => m.LcdpModule),
      },
      {
        path: 'p2p',
        loadChildren: () =>
          import('../p2p/p2p.module').then((m) => m.P2pModule),
        data: {
          breadcrumb: 'Procure 2 Pay',
        },
      },
      {
        path: 'ils',
        loadChildren: () =>
          import('../integration-layer/integration-layer.module').then(
            (m) => m.IntegrationLayerModule
          ),
        data: {
          breadcrumb: 'Integration Layer',
        },
      },
      {
        path: 'employee-central',
        loadChildren: () =>
          import('../employee-directory/employee-directory.module').then(
            (m) => m.EmployeeDirectoryModule
          ),
        data: {
          breadcrumb: 'HR',
        },
      },
      {
        path: 'employee-central/employeeCentralDetail/:associateId',
        loadChildren: () =>
          import(
            '../employee-directory/features/employee-directory-detail/employee-directory-detail.module'
          ).then((m) => m.EmployeeDirectoryDetailModule),
        data: {
          breadcrumb: 'My Profile',
        },
      },
      {
        path: 'leave-manager',
        loadChildren: () =>
          import('../leave-app/leave-app.module').then((m) => m.LeaveAppModule),
        data: {
          breadcrumb: 'Leave Management',
        },
      },
      {
        path: 'inbox',
        loadChildren: () =>
          import('../inbox-application/inbox-application.module').then(
            (m) => m.InboxApplicationModule
          ),
        data: {
          breadcrumb: 'Inbox',
        },
      },
      {
        path: 'search/:searchTerm/:alias/:index',
        loadChildren: () =>
          import('../search-results/search-results.module').then(
            (m) => m.SearchResultsModule
          ),
        data: {
          breadcrumb: 'Search results',
        },
      },
      {
        path: 'people-allocation',
        loadChildren: () =>
          import('../resource-management/rm.module').then((m) => m.RmModule),
        data: {
          breadcrumb: 'People Allocation',
        },
      },
      {
        path: 'dash',
        loadChildren: () =>
          import('../dashboard-builder/dashboard-builder.module').then(
            (m) => m.DashboardBuilderModule
          ),
        data: {
          breadcrumb: 'Dashboard-Creator',
        },
      },
      {
        path: 'timesheetv2',
        loadChildren: () =>
          import('../timesheet-v2/timesheet-v2.module').then(
            (m) => m.TimesheetV2Module
          ),
        data: {
          breadcrumb: 'HRM',
        },
      },
      {
        path: 'gdadmin',
        loadChildren: () => 
          import('../gd-admin/global-db-admin.module').then((m) => m.GlobalDbAdminModule),
          data: {
            breadcrumb: 'gdadmin',
          },
      },
      {
        path: 'project-management',
        loadChildren: () => 
          import('../project-management/project-management.module').then((m) => m.ProjectManagementModule),
          data: {
            breadcrumb: 'Project Management',
          },
      },
      {
        path: 'org-structure',
        loadChildren: () =>
          import('../org-structure/org-structure.module').then(
            (m) => m.OrgStructureModule
          ),
        data: {
          breadcrumb: 'HR',
        },
      },
      {
        path: 'invoice-v1.2',
        loadChildren: () =>
          import('../invoice-v1.2/invoice.module').then((m) => m.InvoiceModule),
        data: {
          breadcrumb: 'Invoices V1.2',
        }
      },
      {
        path: 'integration',
        loadChildren: () =>
          import('../integration/integration.module').then((m) => m.IntegrationModule),
        data: {
          breadcrumb: 'Integration',
        },
      },
      {
        path: 'payment-app',
        loadChildren: () =>
          import('../payment-app/payment-app.module').then(
            (m) => m.PaymentAppModule
          ),
        data: {
          breadcrumb: 'Bills & Payments',
        },
      },
      {
        path: 'ats',
        loadChildren: () =>
          import('../applicant-tracking-system/applicant-tracking-system.module').then(
            (m) => m.ApplicantTrackingSystemModule
          ),
        data: {
          breadcrumb: 'ATS',
        }
      },
      {
        path: 'home',
        loadChildren: () =>
          import('../kebs-home/kebs-home.module').then(
            (m) => m.KebsHomeModule
          ),
        data: {
          breadcrumb: 'Home',
        },
      },
      {
        path: 'expenses-v1.2',
        loadChildren: () =>
          import('../expenses-v1.2/expenses.module').then((m) => m.ExpensesModule),
        data: {
          breadcrumb: 'ExpensesV1.2',
        },
      },
    ],
  },
];
@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class MainRoutingModule {}
