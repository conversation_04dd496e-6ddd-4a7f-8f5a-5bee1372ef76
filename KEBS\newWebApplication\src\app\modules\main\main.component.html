<app-custom-progress-bar-infinte *ngIf="showLoader"></app-custom-progress-bar-infinte>
<div class="nav-bar-component">
  <mat-sidenav-container style="z-index: 0 !important;" fullscreen>
    <mat-sidenav
      #sidenav
      class="example-sidenav"
      [mode]="isLargeScreen ? 'side' : 'over'"
      [opened]="isLargeScreen"
      *ngIf="!isFullScreenApp"
      [ngStyle]="{ 'background-color': navBarColor }"
    >

      <div class="flip-box">
        <div class="flip-box-inner">
          <div class="flip-box-front">
            <img
              [src]="tenant_logo"
              [height]="tenant_logo_height"
              [width]="tenant_logo_width"
              class="kaarlogo"
              style="margin-top: 8px;"
              routerLink="../main"
            />
            
          </div>
          <div class="flip-box-back">
            <img
              src="https://assets.kebs.app/images/KEBS_logomark.svg"
              height="65"
              width="65"
              class="kaarlogo"
              style="margin-top: 8px;"
              routerLink="../main"
            />
          </div>
        </div>
      </div>
      

      <mat-nav-list class="mt-3 example-list list-style" cdkDropList (cdkDropListDropped)="drop($event)"
        [ngStyle]="{ background: navBarColor }">
        <div *ngFor="let menu of _userExp.pinnedApps" [tooltip]="tooltipcontent" content-type="template"
          [options]="toolTipOptions" class="slide-from-down" [routerLink]="menu | navLink"
          (click)="showMoreAppsContainer = false" routerLinkActive="active-route" cdkDrag
          (contextmenu)="onContextMenu($event, menu)">
          <mat-list-item [class.disabled-icon]="menu.disabled == 'true'" *ngIf="menu.isPinned == true"
            [ngStyle]="{ height: isIconAlone ? '48px' : '65px' }">
            <div class="selector-bar"></div>
            <div class="side-item">
              <mat-icon matListIcon>{{ menu.icon }}</mat-icon>
              <p *ngIf="!isIconAlone" class="appName" matLine>{{ menu.label }}</p>
            </div>
          </mat-list-item>
          <ng-template #tooltipcontent style="background-color: #1e2733">
            <div class="row">
              <div class="col-12">
                {{ menu.label }}
              </div>
            </div>
          </ng-template>
        </div>
      </mat-nav-list>

      <div style="visibility: hidden; position: fixed" [style.left]="contextMenuPosition.x"
        [style.top]="contextMenuPosition.y" [matMenuTriggerFor]="contextMenu"></div>

      <mat-menu #contextMenu="matMenu">
        <ng-template matMenuContent let-item="item">
          <button mat-menu-item (click)="refresh()">Refresh</button>
          <button mat-menu-item (click)="openInNewTab(item)">
            Open in new tab
          </button>
          <button mat-menu-item (click)="unPinAllApps($event)">Unpin all apps</button>
        </ng-template>
      </mat-menu>

      <mat-nav-list class="mt-0 pt-0 example-list list-style" style="position: fixed; bottom: 0">
        <div class="mt-5" (click)="showMoreapps($event)">
          <mat-list-item class="more-icon">
            <div class="selector-bar"></div>
            <div class="side-item">
              <mat-icon matListIcon>apps</mat-icon>
              <p matLine>More apps</p>
            </div>
          </mat-list-item>
        </div>
      </mat-nav-list>
    </mat-sidenav>

    <mat-sidenav-content [ngStyle]="{ 'margin-left': isFullScreenApp ? '0px' : '76px' }"
      style="position: static !important">
      <div class="card" [ngClass]="
          showMoreAppsContainer ? 'more-items-open' : 'more-items-close'
        " [ngStyle]="{ background: navBarColor }">
        <div class="card-body p-1 more-apps-wrapper">
          <div (click)="onHeaderClick($event)" class="row d-flex justify-content-around mb-2">
            <div class="col-auto px-1 mt-2 mr-0 slide-from-down">
              <!-- <input type="text" name="search" placeholder="Search apps" [(ngModel)]="searchText"
                (ngModelOptions)="({ debounce: 200 })">    -->

              <mat-form-field floatLabel="never" appearance="outline" color="'warn'">
                <mat-icon matPrefix style="color: white">search</mat-icon>
                <input floatLabel="never" matInput placeholder="Search apps" [(ngModel)]="searchText" (ngModelChange)="onSearchBarClick($event)"
                  (ngModelOptions)="({ debounce: 200 })" style="color: white" />
                <mat-icon matSuffix *ngIf="searchText != ''" (click)="onClickCloseIcon($event)"
                  style="cursor: pointer; color: white">close</mat-icon>
              </mat-form-field>
            </div>
            <div class="
                col-md-1 col-xs-6
                my-auto
                p-0
                mr-0
                slide-from-down
                category-icon
              ">
              <button mat-icon-button [ngClass]="{
                  'header-btns': isCategoryMode == false,
                  'header-btns-acti': isCategoryMode == true
                }" (click)="changeToCategoryMode($event)" matTooltip="Apps by categories">
                <mat-icon class="menu-icons">format_align_left</mat-icon>
              </button>
            </div>
            <div class="col-md-1 col-xs-6 my-auto p-0 mr-0 slide-from-down">
              <button mat-icon-button class="header-btns info-btn" (click)="enableWebTour($event)" matTooltip="Info">
                <mat-icon class="menu-icons">info</mat-icon>
              </button>
            </div>
            <div class="
                col-md-1 col-xs-6
                my-auto
                p-0
                mr-0
                slide-from-down
                customize-icon
              ">
              <button mat-icon-button class="header-btns" matTooltip="Customize" [ngClass]="{
                  'header-btns': isCustomizeMode == false,
                  'header-btns-acti': isCustomizeMode == true
                }" (click)="customize($event)">
                <mat-icon class="menu-icons">palette</mat-icon>
              </button>
            </div>
            <div class="col-md-1 col-xs-6 my-auto p-0 mr-0 slide-from-down">
              <button mat-icon-button class="header-btns close-btn" (click)="closePannel()" matTooltip="Close">
                <mat-icon class="menu-icons">close</mat-icon>
              </button>
            </div>
          </div>

          <ng-container *ngIf="isCategoryMode == false && isCustomizeMode != true">
            <div class="row app-row slide-from-down" *ngFor="
                let apps of _userExp.menuAppList | filter: searchText;
                let appIndex = index
              " style="padding: 1px">
              <span class="col-2 my-auto" style="color: white">
                <mat-icon matListIcon *appAcl="apps.application_name" style="font-size: 21px"
                  [ngStyle]="{ color: apps.isPinned ? 'white' : '#b8b7b5' }"
                  [class.disabled-icon]="apps.disabled == 'true'">
                  {{ apps.icon }}
                </mat-icon>
              </span>
              <span class="col-8 pt-2 my-auto" (click)="moreAppClicked(apps)"
                [ngStyle]="{ color: apps.isPinned ? 'white' : '#b8b7b5' }">
                <p matLine>{{ apps.label }}</p>
              </span>
              <span class="col-1 pl-0 pt-1 my-auto">
                <button mat-icon-button class="pin-btn" style="color: white" (click)="pinCategorizedApp(apps.id,$event)"
                  matTooltip="Pin app" [ngStyle]="{
                    visibility: !apps.isPinned ? 'hidden' : 'visible'
                  }">
                  <mat-icon style="font-size: 21px" >push_pin</mat-icon>
                </button>
              </span>
            </div>

            <div style="color: white" *ngIf="(_userExp.menuAppList | filter: searchText).length === 0"
              class="d-flex justify-content-center">
              No match found
            </div>
          </ng-container>
          <ng-container *ngIf="isCategoryMode == true && isCustomizeMode != true">
            <div class="row ml-2 slide-from-down" *ngFor="let c of categorizedAppsKeys">
              <div class="col-12 p-0">
                <div class="row mb-2 pt-1">
                  <div class="col-12 pl-2" style="color: white">
                    {{ c }}
                  </div>
                </div>
                <div class="row app-row pt-1" *ngFor="
                    let apps of categorizedApps[c] | filter: searchText;
                    let appIndex = index
                  " style="padding: 1px">
                  <div class="col-2 my-auto" style="color: white; font-size: 21px">
                    <mat-icon matListIcon *appAcl="apps.application_name"
                      [ngStyle]="{ color: apps.isPinned ? 'white' : '#b8b7b5' }" style="font-size: 21px"
                      [class.disabled-icon]="apps.disabled == 'true'">
                      {{ apps.icon }}</mat-icon>
                  </div>
                  <div class="col-8 my-auto pt-2" (click)="moreAppClicked(apps)"
                    [ngStyle]="{ color: apps.isPinned ? 'white' : '#b8b7b5' }">
                    <p matLine>{{ apps.label }}</p>
                  </div>
                  <div class="col-1 pl-0">
                    <button mat-icon-button class="pin-btn pt-1 my-auto" style="color: white; font-size: 21px"
                      (click)="pinCategorizedApp(apps.id,$event)" matTooltip="Pin app" [ngStyle]="{
                        visibility: !apps.isPinned ? 'hidden' : 'visible'
                      }">
                      <mat-icon style="font-size: 21px">push_pin</mat-icon>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </ng-container>

          <ng-container *ngIf="isCustomizeMode == true">
            <div style="padding: 5px" (click)="onHeaderClick($event)">
              <div class="row mt-4 pl-3 slide-from-down" style="font-size: 16px; color: white">
                Choose your preferred
              </div>
              <div class="row mt-4 pl-3 slide-from-down" style="font-size: 22px; color: white">
                Sidebar color
              </div>

              <div class="container mt-4 slide-from-down color-palette">
                <div class="row d-flex justify-content-around">
                  <div class="circle col-xs-6" style="background: #1e2733" (click)="navColor('#1E2733', 1,$event)">
                    <span>
                      <mat-icon *ngIf="palette == 1" class="icon-select">check_circle</mat-icon>
                    </span>
                  </div>
                  <div class="circle col-xs-6" style="background: #cf0001" (click)="navColor('#CF0001', 2,$event)">
                    <span>
                      <mat-icon *ngIf="palette == 2" class="icon-select">check_circle</mat-icon>
                    </span>
                  </div>
                  <div class="circle col-xs-6" style="background: #040634" (click)="navColor('#040634', 3,$event)">
                    <span>
                      <mat-icon *ngIf="palette == 3" class="icon-select">check_circle</mat-icon>
                    </span>
                  </div>
                </div>
                <div class="row d-flex justify-content-around">
                  <div class="circle col-xs-6" style="background: #0c342c" (click)="navColor('#0C342C', 4,$event)">
                    <span>
                      <mat-icon *ngIf="palette == 4" class="icon-select">check_circle</mat-icon>
                    </span>
                  </div>
                  <div class="circle col-xs-6" style="background: #00917c" (click)="navColor('#00917C', 5,$event)">
                    <span>
                      <mat-icon *ngIf="palette == 5" class="icon-select">check_circle</mat-icon>
                    </span>
                  </div>
                  <div class="circle col-xs-6" style="background: #000000" (click)="navColor('#000000', 6,$event)">
                    <span>
                      <mat-icon *ngIf="palette == 6" class="icon-select">check_circle</mat-icon>
                    </span>
                  </div>
                </div>
                <div class="row d-flex justify-content-around">
                  <div class="circle col-xs-6" style="background: #9f2825" (click)="navColor('#9F2825', 7,$event)">
                    <span>
                      <mat-icon *ngIf="palette == 7" class="icon-select">check_circle</mat-icon>
                    </span>
                  </div>
                  <div class="circle col-xs-6" style="background: #4a3933" (click)="navColor('#4A3933', 8,$event)">
                    <span>
                      <mat-icon *ngIf="palette == 8" class="icon-select">check_circle</mat-icon>
                    </span>
                  </div>
                  <div class="circle col-xs-6" style="background: #87556f" (click)="navColor('#87556F', 9,$event)">
                    <span>
                      <mat-icon *ngIf="palette == 9" class="icon-select">check_circle</mat-icon>
                    </span>
                  </div>
                </div>
              </div>

              <div class="app-view-switch">
                <div class="row mt-5 pl-3 slide-from-down">
                  <mat-checkbox class="check-box py-2" [color]="'warn'" [checked]="isIconAlone === 0"  (click)="onClickCheckbox(0,$event)"
                   >Show app name and icon</mat-checkbox>
                </div>
                <div class="row pl-3 py-2 slide-from-down">
                  <mat-checkbox class="check-box" [color]="'warn'" [checked]="isIconAlone === 1" (click)="onClickCheckbox(1,$event)"
                    >Show icon alone</mat-checkbox>
                </div>
              </div>
            </div>
          </ng-container>
        </div>
      </div>

      <div *ngIf="isSideNavVisible" [ngStyle]="{'background-color': navBarColor}" [ngClass]="isSideNavVisible && isSideNavOpen ? 'sub-side-nav-open-brightness' : isSideNavVisible && !isSideNavOpen ? 'sub-side-nav-close-brightness' : ''"></div>
      <div *ngIf="isSideNavVisible" [ngClass]="isSideNavVisible && isSideNavOpen ? 'sub-side-nav-open' : isSideNavVisible && !isSideNavOpen ? 'sub-side-nav-close' : ''">
        <ng-container *ngIf="isSideNavVisible && isSideNavOpen">
          <div class="align-header-items">
            <div class="side-nav-title-text">
              {{sideNavAppName}}
            </div>
            <div class="header-btn" (click)="toggleSideNav()">
              <mat-icon class="header-icon">keyboard_arrow_left</mat-icon>
            </div>
          </div>
          <mat-form-field style="margin-left: 8px;" floatLabel="never" appearance="outline" class="search-field" [color]="navBarColor">
            <mat-icon matPrefix class="search-field-icon">search</mat-icon>
            <input floatLabel="never" matInput placeholder="Search" [(ngModel)]="searchSideNavText"
              (ngModelOptions)="({ debounce: 200 })" style="color: white"/>
          </mat-form-field>
          <div class="nav-items">
            <ng-container *ngIf="(sideNavApps | subSideNavFilter: searchSideNavText : true).length > 0">
              <div class="align-items-center" style="padding-bottom: 24px">
                <div class="pinned-icon-static">
                  <svg width="8" height="12" viewBox="0 0 8 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M6.0005 6.28189L7.05179 7.33317V7.99984H4.33384V11.3332L4.0005 11.6665L3.66717 11.3332V7.99984H0.949219V7.33317L2.0005 6.28189V1.33317H1.33384V0.666504H6.66717V1.33317H6.0005V6.28189ZM1.9005 7.33317H6.1005L5.33384 6.5665V1.33317H2.66717V6.5665L1.9005 7.33317Z" fill="#D4D6D8"/>
                  </svg>                
                </div>
                <div class="pinned-text">PINNED</div>
                <div class="category-split"></div>
              </div>
            </ng-container>
            <ng-container *ngFor="let item of sideNavApps | subSideNavFilter : searchSideNavText : true; let itemIndex = index;">
              <div class="align-items-center" [ngStyle]="{'padding-top': itemIndex == 0 ? '0px' : '24px'}">
                <div class="half-circle" [ngStyle]="{visibility: (item | subSideNavSelectedApp : true) ? '' : 'hidden'}"></div>
                <mat-icon class="icon" [ngStyle]="{color: (item | subSideNavSelectedApp : true) ? '#fff' : ''}" (click)="navigateSubSideNav(item?.link)">{{item.icon}}</mat-icon>
                <span class="title-text" [ngStyle]="{color: (item | subSideNavSelectedApp : true) ? '#fff' : ''}" [matTooltip]="item.label" (click)="navigateSubSideNav(item?.link)">{{item.label}}</span>
                <mat-icon [ngStyle]="{visibility : item?.sub_menu && item.sub_menu.length > 0 ? '' : 'hidden', color: (item | subSideNavSelectedApp : true) ? '#fff' : ''}" class="drop-down-icon" 
                  (click)="item.is_expanded = ! item.is_expanded"
                >
                  {{item.is_expanded ? 'arrow_drop_up' : 'arrow_drop_down'}}
                </mat-icon>
                <div class="pinned-icon" matTooltip="Remove From Favourites" (click)="pinSubSideNavApp(item.id)">
                  <svg width="8" height="12" viewBox="0 0 8 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M6.00001 6.28189L7.0513 7.33317V7.99984H4.33335V11.3332L4.00001 11.6665L3.66668 11.3332V7.99984H0.94873V7.33317L2.00001 6.28189V1.33317H1.33335V0.666504H6.66668V1.33317H6.00001V6.28189Z" fill="#F7F9FB"/>
                  </svg>                                
                </div>
              </div>
              <div *ngIf="item.is_expanded && item?.sub_menu && item.sub_menu.length > 0" class="sub-menu-nav">
                <div class="divider"></div>
                <div class="align-items-column">
                  <ng-container *ngFor="let subItem of item.sub_menu; let subItemIndex = index;">
                    <div class="d-flex align-items-center" style="margin-top: 16px;">
                      <div class="divider-highlight" [ngStyle]="{visibility: (subItem | subSideNavSelectedApp : false) ? '' : 'hidden'}"></div>
                      <div class="sub-title-text" [ngStyle]="{color: (subItem | subSideNavSelectedApp : false) ? '#fff' : ''}" [matTooltip]="subItem.label" (click)="navigateSubSideNav(subItem?.link)">{{subItem.label}}</div>  
                    </div>
                  </ng-container>
                </div>
              </div>
            </ng-container>
            <ng-container *ngIf="(sideNavApps | subSideNavFilter: searchSideNavText : true).length > 0">
              <div style="margin-top: 24px; margin-bottom: 24px" class="category-split"></div>
            </ng-container>
            <ng-container *ngFor="let item of sideNavApps | subSideNavFilter : searchSideNavText : false; let itemIndex = index;">
              <div class="align-items-center" [ngStyle]="{'padding-top': itemIndex == 0 ? '0px' : '24px'}">
                <div class="half-circle" [ngStyle]="{visibility: (item | subSideNavSelectedApp : true) ? '' : 'hidden'}"></div>
                <mat-icon class="icon" [ngStyle]="{color: (item | subSideNavSelectedApp : true) ? '#fff' : ''}" (click)="navigateSubSideNav(item?.link)">{{item.icon}}</mat-icon>
                <span class="title-text" [ngStyle]="{color: (item | subSideNavSelectedApp : true) ? '#fff' : ''}" [matTooltip]="item.label" (click)="navigateSubSideNav(item?.link)">{{item.label}}</span>
                <mat-icon [ngStyle]="{visibility : item?.sub_menu && item.sub_menu.length > 0 ? '' : 'hidden', color: (item | subSideNavSelectedApp : true) ? '#fff' : ''}" class="drop-down-icon" 
                  (click)="item.is_expanded = ! item.is_expanded"
                >
                  {{item.is_expanded ? 'arrow_drop_up' : 'arrow_drop_down'}}
                </mat-icon>
                <div class="pinned-icon" matTooltip="Add To Favourite" (click)="pinSubSideNavApp(item.id)">
                  <svg width="8" height="12" viewBox="0 0 8 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M6.0005 6.28189L7.05179 7.33317V7.99984H4.33384V11.3332L4.0005 11.6665L3.66717 11.3332V7.99984H0.949219V7.33317L2.0005 6.28189V1.33317H1.33384V0.666504H6.66717V1.33317H6.0005V6.28189ZM1.9005 7.33317H6.1005L5.33384 6.5665V1.33317H2.66717V6.5665L1.9005 7.33317Z" fill="#D4D6D8"/>
                  </svg>                
                </div>
              </div>
              <div *ngIf="item.is_expanded && item?.sub_menu && item.sub_menu.length > 0" class="sub-menu-nav">
                <div class="divider"></div>
                <div class="align-items-column">
                  <ng-container *ngFor="let subItem of item.sub_menu; let subItemIndex = index;">
                    <div class="d-flex align-items-center" style="margin-top: 16px;">
                      <div class="divider-highlight" [ngStyle]="{visibility: (subItem | subSideNavSelectedApp : false) ? '' : 'hidden'}"></div>
                      <div class="sub-title-text" [ngStyle]="{color: (subItem | subSideNavSelectedApp : false) ? '#fff' : ''}" [matTooltip]="subItem.label" (click)="navigateSubSideNav(subItem?.link)">{{subItem.label}}</div>  
                    </div>
                  </ng-container>
                </div>
              </div>
            </ng-container>
            <div class="no-result-found" *ngIf="(sideNavApps | subSideNavFilter : searchSideNavText : true).length === 0 && (sideNavApps | subSideNavFilter : searchSideNavText : false).length === 0">
              <svg width="122" height="83" viewBox="0 0 122 83" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                <rect opacity="0.5" width="122" height="82.96" fill="url(#pattern0)"/>
                <defs>
                <pattern id="pattern0" patternContentUnits="objectBoundingBox" width="1" height="1">
                <use xlink:href="#image0_8347_127795" transform="scale(0.002 0.00294118)"/>
                </pattern>
                <image id="image0_8347_127795" width="500" height="340" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAfQAAAFUCAYAAADSys5kAAAAAXNSR0IArs4c6QAAIABJREFUeF7tnUt0Vdd5x7+rB0jCevCQJR6CGDsBYsDCxtBiF8OKs1rsroIn1EzSYZhkmGSSAYNkkGSYiT2sO7CdSZqs1ZK2Xq2dZTsxTQyJMVSOTePQGAgCI2H0lm7Xd6+PdHR1H+ee5z57/+5aXmB0zt7f9/vvc//69tlnn4LwgQAEIAABCEAg9wQKuc+ABCAAAQhAAAIQEAydQQABCEAAAhCwgACGboGIpAABCEAAAhDA0BkDEIAABCAAAQsIYOgWiEgKEIAABCAAAQydMQABCEAAAhCwgACGboGIpAABCEAAAhDA0BkDEIAABCAAAQsIYOgWiEgKEIAABCAAAQydMQABCEAAAhCwgACGboGIpAABCEAAAhDA0BkDEIAABCAAAQsIYOgWiEgKEIAABCAAAQydMQABCEAAAhCwgACGboGIpAABCEAAAhDA0BkDEIAABCAAAQsIYOgWiEgKEIAABCAAAQydMQABCEAAAhCwgACGboGIpAABCEAAAhDA0BkDEIAABCAAAQsIYOgWiEgKEIAABCAAAQydMQABCEAAAhCwgACGboGIpAABCEAAAhDA0BkDEIAABCAAAQsIYOgWiEgKEIAABCAAAQydMQABCEAAAhCwgACGboGIpAABCEAAAhDA0BkDEIAABCAAAQsIYOgWiEgKEIAABCAAAQydMQABCEAAAhCwgACGboGIpAABCEAAAhDA0BkDEIAABCAAAQsIYOgWiEgKEIAABCAAAQydMQABCEAAAhCwgACGboGIpAABCEAAAhDA0BkDEIAABCAAAQsIYOgWiEgKEIAABCAAAQydMQABCEAAAhCwgACGboGIpAABCEAAAhDA0BkDEIAABCAAAQsIYOgWiEgKEIAABCAAAQydMQABCEAAAhCwgACGboGIpAABCEAAAhDA0BkDEIAABCAAAQsIYOgWiEgKEIAABCAAAQydMQABCEAAAhCwgACGboGIpAABCEAAAhDA0BkDEIAABCAAAQsIYOgWiEgKEIAABCAAAQydMQABCEAAAhCwgACGboGIpAABCEAAAhDA0BkDEIAABCAAAQsIYOgWiEgKEIAABCAAAQydMQABCEAAAhCwgACGboGIpAABCEAAAhDA0BkDEIAABCAAAQsIYOgWiEgKEIAABCAAAQydMQABCEAAAhCwgACGboGIpAABCEAAAhDA0BkDEIAABCAAAQsIYOgWiEgKEIAABCAAAQydMQABCEAAAhCwgACGboGIpAABCEAAAhDA0BkDEIAABCAAAQsIYOgWiEgKEIAABCAAAQydMQABCEAAAhCwgACGboGIpAABCEAAAhDA0BkDEIAABCAAAQsIYOgWiEgKEIAABCAAAQydMQABCEAAAhCwgACGboGIpAABCEAAAhDA0BkDEIAABCAAAQsIYOgWiEgKEIAABCAAAQydMQABCEAAAhCwgACGboGIpAABCEAAAhDA0BkDEIAABCAAAQsIYOgWiEgKEIAABCAAAQydMQABCEAAAhCwgACGboGIpAABCEAAAhDA0BkDEIAABCAAAQsIYOgWiEgKEIAABCAAAQydMQABCEAAAhCwgACGboGIpAABCEAAAhDA0BkDEIAABCAAAQsIYOgWiEgKEIAABCAAAQydMQABCEAAAhCwgACGboGIpAABCEAAAhDA0BkDEIAABCAAAQsIYOgWiEgKEIAABCAAAQydMQABCEAAAhCwgACGboGIpAABCEAAAhDA0BkDEMiQwJkzxWJn5w9kcvJbcuZMgesxQy3oGgJ5J8AXSN4VJH7jCPhNulFwO3a8UjpkZOT5RofK4OCLcv3610V/Afj2t7/NtduQGAdAwC0CfCm4pTfZxkxAzbuySTXpzs4+mZy8I4VCQTo6elf8XX/u/+ix3qfynGKxuNheLfNXsz99+jTXc8z60hwE8kSAL4A8qUWsmROoNHCvwtbAPCNOOsipqbHFXxK8viorfAw+aRVoHwLmEcDQzdOEiAwj4DfxZAxci/xol2KlyfsNHnM3bEARDgQSIhDtWyShoGgWAlkT8Ju4GmJfX1+pKo7z45lwnG16bWnbOlVfOUXPwrskaNMmBMwggKGboQNRGELAM3I18d7e3gDT6GGq62rnVN6Kj/fS1Hv0ldPymLshg44wIBATgXi/NWIKimYgkDYBz8h1Sr3xvfAV6+BChOtdelHaCn75asXufbRy95s7xh5CPk6BgIEEgn8jGBg8IUEgKgG/kWtby1efV1bSUcw3aqRBzw92SXur6ltbn5JLlzaWGsfYgzLmOAiYSSDY1W9m7EQFgdAEaht5Hky72bSrX+Ze1U7F3ixPjoeAmQQwdDN1IaqECNSfWk/XzGvtC7fyyfYkYCy/9L1FdFTsSbCmTQikQwBDT4czvWRMINg98pWGXmm6araNNmhtZMiNzq9E1ai9cGhXXvrVKnam4cPR5SwIZEEAQ8+COn2mSkDNvPFit8ZmnmrQITprzvj9l/7KVfdq7v/zP3+/GAXGHkIQToFAygQw9JSB0116BPxVeeVWq0tRVJ9mb7aKTi+raD3VNv3Kr4KiTE2NlzrzjB1Tj8aesyGQNAEMPWnCtJ8JgcZVee375baaef2p/FoVe9nYqdYzGcZ0CoGmCGDoTeHi4DwQ8My82apcc3PFzOubu/+n+hVBtZ6HcU+MEMDQGQPWEIgyxZ6UmXd1dUpb25oS497e1aU/29vbZXZ2dsWfY2ML0to6K/PzRRkfv5WJLtWn5Jc2wfFX60zBZyIRnUKgJgEMncFhBYEoVXkcZl427j7ZsGG16N87OztLhu192tpam+I8NzdfOn5iYkLm5uZkbGxc7tyZkPHxTxObRai2gn+5wZerdf1cuHBsMR+MvSlpORgCiRHA0BNDS8NpEahv5o2fLQ8zze4Z+NatfYvm7Zn2qlWrlqVeaGmRttaWQDjm5hdKxxUXyn96n5mZmdJf1ejV5MfH78qnn34md+7cWnwJS6AOahxUy8z9bDxzLxSKMjl5txzn59vIYupR6HMuBOIhgKHHw5FWMiKQppmvWtUuAwP3y+DgQKn6rjTw9va2mhS8aXY9IMjfKxuqZvRq8mrw4+Pj8vHH12R8/E6o6j2omZfNvSjFov6l/NWhW8h6m9Fg6hldBHQLgc8JYOgMhdwSSMPMW1tbZc2a+2X79g3S1dVVMnGvAq9Wefvvj8cFVu+3V/4i4LU9OztX+qtn7rdujcrVq5/IvXtTopV0o09zZq4VeXnhYNnUyx//m9ww9UbE+TkEkiOAoSfHlpYTJJC0mWs1vmXL5lI17jfxsrEuVeL+++QJpruiac/k/T/wzF3/7fr1G/LHP14v3XOv9Qlr5tre0r318leIfyMaTD3NkUBfEFgigKEzGnJHIEkz13vjauKVRm6CidcTqtLgPXPXxXQffvjHFffao5j5ckPX/1u5WA5Tz91lRcAWEMDQLRDRpRSSMvNaFbln5FlV4mG09Zu73nvXBXZq7CMj/1uzYq80eP//N179vhQlj7WFUYxzIBAPAQw9Ho60kgKBJMxc75H392+Q7du/sDi17r83nicjryaB39y1ah8dvSXvv39ZZmf1sbjq99gbmbm/n0aPtVGpp3Bh0AUEPieAoTMUckEgCTPv6dlYWuzW09NdWuhmk5FXilpp7Feu/EE++ujjFQvnmjHzagPHM3gq9VxcVgRpGQEM3TJBbUwnbjPXqnzbtqHSfXK9Z66fPE6th9HaM3adip+empJf//r8ihXxQV4RW8vMy4+2FT5fNFdc3ICGSj2MWpwDgeYIYOjN8eLolAnEbebd3WvlwQe3LFblrhh5rYpdp+EvXrws1679uTQFH2aTnXqVuv5scnJMRkaeF0w95YuH7pwjgKE7J3l+Eo7bzLUi13vlrlXltRT3T8NfvfonuXz5A1lYWIhl5zntszz9Xig90qY7yp06dYrvm/xcfkSaQwJcYDkUzYWQ1cwHB1+UwcFtVdJtvGGKv9LUKfZdu74kPT09JTN3tSpvZOz37k3IhQvvlbaVjePj33zmwoW/KTVJlR4HWdqAQHUCGDojw0gCaug7d74qHR29FfHVN/PKKWN9HO3LX97p/BR7I5G9al2n4N9550LobWT9/fgNnY1nGinAzyEQnQCGHp0hLcRMIOxUe6WZazX+8MO7pLOzo7SKXSvzvD+GFjPqFc2psaupnz//O7l9+9NI0+/Ld5Nb/pY2KvWklaR9Fwlg6C6qbnDOcZt5b29PKVvMPLjoXrWui+V0X/haz6s3anFp3/elI/VxtpaWw3Ly5Ca+exoB5OcQaJIAF1WTwDg8WQK1Db32VHtlZa7Pl+/evXXZ4jcq8+Z1U2OPaura69JrV/VxtqLMzAzLpUsbuZ/evCScAYG6BDB0BogxBMJU59Wm2YeH92LmMakal6n7jd17jE3/jan3mISiGQgsvtQYFBDImEB9My/ZwYoIMfN0RFNTP3/+Pbl+vfysetiP9xibnq+L5Jh6D0uS8yBQnQAVOiPDCAK1V7UHM3O9R66VOQvgkpFTTf2tt96J/Eib/9l0NXSm3pPRi1bdJIChu6m7UVk3W51XVuZq5rqa3duTnQVwycjrmfrY2N2IO8qVt4b1qnRMPRm9aNU9Ahi6e5obl3Ez1Xm1rUl3794l69at5dG0FJSdmJiQN954q/RoW9hP5Q5yY2Njcvr0ab6LwgLlPAh8ToCLiKGQKYFmqvNqZj40tFmGhrYs7gDHavbk5bx27YacO/dupCqdXeSS14ke3COAobunuTEZRzXz7u4+2bNnB2aegaK68Yzu/x7lUzb1ouiz6ez1HoUk50KgTABDZyRkRiDoVHu1ytxbBKcbx7A3e/oSxrFIrrzxTPl+Onu9p68hPdpHAEO3T9NcZBS0Oq/1Ok/um2cv8507Y/LLX56Tubm5xc1jmo3Kq9L1PH0+nTeyNUuQ4yGwRABDZzRkQiBKde7fCY4V7ZnIt9jpyMgHMjLyUegg/Pu9U6WHxsiJEGDKnTGQHQE19OHhn9cIoLx5SbXqXP9t//5HxZtqZxFcdhpqzzr1rqveJyYmqwbi7edeL0qt0gsFfQ97gSo9WznpPecEqNBzLmAeww+6xWs1Q9++/QuyZcsmWd3RIW2tLbw9zYABoKve//u/340UifcoG1V6JIyc7DgBDN3xAZBF+kFfwFJva1em2rNQrnafuovcrVu3QwdFlR4aHSdCYJEAhs5gSJVA0Oq8cspdK7g9e3bJ4OCAFFpaqM5TVa1xZ7rhzGuvvdH4wDpH+Kt0XtoSCSUnO0oAQ3dU+KzSDrsYrqurs7RXu/5JdZ6VevX7jfJsumfmhUJRpqd5vaqZChOV6QQwdNMVsiy+MIvh/NU5z5ybOyDiqdLLX0kzM4/IyZOb+H4yV24iM5AAF4yBotgaUtjpdqrz/IwI3RL2+vUboQMuv7RlnFerhibIiS4TwNBdVj/l3MMuhtNNZPTeOVPtKQsWorvR0Vvy9tvnQpxZPsXb453V7qERcqLDBDB0h8VPO/Xa98/Lz517H//q9tbWVjl4cD/3ztMWK0J/UVa8a4Xe1rYgc3MtPJMeQQNOdZMAhu6m7qlnHXS63W/m+uW+detmeeCBbbJmTVcpZjaSSV26pju8evX/5Pz595o+r1yhL/5ax/7uoQhykssEMHSX1U8x93Cr2wvy+OP72BUuRZ3i6Ep3j3vttddDvTPdW+3e0iKysFCUmZlhFsfFIQptOEEAQ3dC5uyTDLO6XRfD7d+/z9hd4XRquViclSeffDJ1wBfff1/G7nwmW7dukqGhodT7b9RhlEfYdJZmYaG8OI7XqjYizc8hsEQAQ2c0JE4g6HS7BuKfch8a2iw7dnzRuMVw+pax5547IRcvXiyxU0N96Z/+SXY//HDiLLWD73znO/Liiy+W+urp6ZHvfe978vzzz6fSd9BOom8HW36tKo+vBSXOcRDgfeiMgRQIqKEPDr4og4PbKnqrvRhOVzsfOGDedHulmXsJqan/53/+l/T19SZK1G/m/o5+8pOfZDJTUC/Zs2f/I9S0e7lN3pOe6ECicSsJUKFbKatZSVW/f17bzDX67u61sm/frtJiOJMWwv3gBz+QH/7wh1UBf/Ob35RvfetbicG/evWqPProo1Xb118o3n032gtS4g48yrS7f7U799HjVob2bCWAoduqrEF5NXpcrdZb1fTNaqY9e/7ggw/K+Ph4Vbo6/f2b37ybWJX+jW98Q1555ZWayv7oRz8yauo9ymp3TdLbZIb76AZdzIRiNAEM3Wh58h9cmM1kKrd6NaVC/9d/PSv/8A9fqyvKP/7jS/LMM8cSEa7eLxPaod5HV1M35RN1K9hCoVBaHDc5eUdOnTrFd5UpwhKHsQS4SIyVxo7Aghh6ZYWu98+feOKA9PZ2lyCYYui17l/7lfr6178u3/3ud2MXT1e1Hz1ypG67OkPw0Ucfxd53lAb18bWJicmQTSwtjLt0aaPwBraQGDnNGQIYujNSZ5Po97///eKOHXsrOq9//1wfVzt06KBx0+3Hjx+Xt99+uy7IQ4cOyU9/+tPYYetUu065N/r8/vcfJjbl36jvaj+Pch/dWxin7VKlh6HPOa4RwNBdUzzlfKuvcK9n6AUZGtpk5ONqR48eXXxUrRbGpKrkeovx/LGYtto9yn10b193zY/H11K+cOkulwQw9FzKlp+gG61wr7YgbseOB0vPdpu2IK6/vz8Q+Js3bwY6rpmD8mroo6O35e2332km1WXHqqlPTY2xwUxogpzoEgEM3SW1M8i1nqFXM3NTnz9XdBh6uAH0s5+dDXWiLo7UhXH6J1PuoRBykmMEMHTHBE8z3WYWxJW/vMuvz9QFcWvuWyNtrS3GLIhTbo1WmXtsqdCXj7IoG8x40+4YeppXLn3llQCGnlflchB3I0OvVaF/9atPGTfdrrizXBQXdMr9v15/PbUtaIMOwWivUy1/RXEPPShtjnOZAIbusvoJ595ohfvKx9V0b/I+OXhw2EhDb7Sxi+I8duyYvPTSS7GTffPNN+W5555r2G4SswMNO21wQJSV7l6FrobOo2tRleB82wlg6LYrnGF+jVa4V6vQ167dII89tttIQw/y6FhSu7XpHvJf/OJDddVM6pG5qENoZOQDGRkJ93y89zpVqvSoKnC+CwQwdBdUzijHehV6NTPXME029Hp7qXuIdT/1pF5n+rWvfU3Onq29wEw3tNGNbUz7RDF0zYWV7qYpSjymEsDQTVXGgrhWVuhLz5/7Db1chZUXxZn6yJonR71p96Sm272+6209m/Q+8lGGY9RXqbIFbBT6nOsSAQzdJbVTzrVWhV6rOtfwTDd0rdKPHDlS9QUtSVbnnnS1FuYl/aa3KEMn6rPo/h3jTpw4xndWFDE412oCXBxWy5ttckErdC9KrdR37jRzUxk/yWr30pO6d16pYLX3sZv2UpbKmOMydB5dy/Z6pnfzCWDo5muU2wibqdC959C9Cl33czf5o5X6yy+/XArxmWefTfVRMTX1V199RTSG3bt3G/XK1GqaRTV0b2Echm7yFUFsJhDA0E1QwdIYalXo1R5X8/4tL4ZuqWSJpBXd0MtfUxh6IvLQqEUEMHSLxDQtlWYqdC92DN00FaPHg6FHZ0gLEAhCAEMPQoljQhEIWqH7G8fQQ6E2+qS4DJ3d4oyWmeAMIIChGyCCrSFQoduqbHN5RTF07/65vnHtzp07cvr0ab6zmsPP0Q4R4OJwSOy0Uw1ToQ8ODsju3bvE9EVxabPMc39R3omueetz6C0tLXL37m05deoU31l5HgzEnigBLo5E8brdeJgK3dspDkO3Z+zEsVOc0mBRnD1jgkySIYChJ8OVVkUkTIWOods3dDB0+zQlIzMJYOhm6mJFVGEqdN0V7Omnn2LK3YoRUE7i3Ll35fr1G6Ez8t64xi5xoRFyoiMEMHRHhM4izTAVumfo7e1t0t7enkXY9BkzgSjvQ9dQ1NBZ4R6zKDRnJQEM3UpZzUlKTX3nzlelo6N3MahCYeklLZWR6pf3E08ckN7ebgzdHBkjRfKzn9V+Q1yQhltbF2Ri4lE5eXIT31dBgHGMswS4QJyVPp3EmzV0jWp4eI9s3DiAoacjUaK9zM7Oytmzr0Xqo7W1VSYm9mDokShysgsEMHQXVM4wRzX0HTtekc7OvkAVuh6km8ts3/4Ahp6hbnF1HeUZ9KUYCjI9/QiGHpcotGMtAQzdWmnNSGzJ0P1T7vVj05Xuf/EXwxi6GRJGiiL6Cnd9Dr0oxWKLsCgukhSc7AABDN0BkbNMMcxKd72P/swzXymFzcK4LNWL3nfUBXEagY4HnkGPrgUt2E8AQ7df40wzrLbSvfJta5UB2rowbnJqWv50/c9V9XjoC0OZ6pRU51EXxHlbv1KdJ6UQ7dpEAEO3SU1DcwmzMM6G++gf/uGqXPnjJ3Ll42vyp+s3ZWp6pq5CmwY2yNrebtmxbVC2bhuSTYP9kucLNI775zyyZuhFTVhGEsjz94WRQAlqJYEwhr5+/To5cODR3E2537kzJv/2i1/LxZErDQ280VhRc3941wPy1P690tvXmztzv3LlD3Lx4uVGadb9eVubyL17wyyIi0SRk10hgKG7onSGeYZZ6Z63HePUyP/539+S9z/4X9FKu6NjVYn4g9s2NU3+0zufye2x8WXnrevtkcf2fknyNDX/+utvyvj43abz95+gL2aZmmKFeySInOwMAQzdGamzSzTMSnc19OHh3bl5Hl2n1PWzebA/O9AG9TwxMSmvvfZ6DBEV5fjxZ/meioEkTdhPgAvFfo2NyLBy2r28MK72jnH684GBAdm3b0/upt2NAJ5xEHFMt2sK09NMt2csJd3niACGniOx8hxqmPvoOt36la/wopY86h7HdDv3z/OoPDFnSQBDz5K+Q32Hu48usnv3Lhka2kyVnqOxEtd0O1u+5kh0QjWCAIZuhAz2BxHuPrpIT0+3HDnypP2ALMow6u5wSyi4f27RsCCVFAhg6ClAposygWbvo3vcDh06KBs2rANjDgjoy1h0Mdzs7FzkaLl/HhkhDThGAEN3TPAs010+7V5eENdo1zg9Rvd2/6u/ejzL0Ok7IIG4FsNx/zwgcA6DgI8Ahs5wSI3ACy+8UBwc3Lasv3rvRtcDdetPNf3Dhw9JX9/SC15SC5qOmiKg1bneQ4/60fvnf/u3f833U1SQnO8UAS4Yp+TOPtlmVrt7Zq5R68K4ffv2Zp8AEdQkcPXq/8n58+/FQojp9lgw0ohjBDB0xwTPOt2Vq92LVafd/Wbuxfz000ekq6sz6xTovwaBuKpzptsZYhAIRwBDD8eNs0ISCPL2tWpmrt3p/u5PPHEwZM+cliSBOKtz3XCI3eGSVIu2bSWAoduqrMF5qakPD59dFmGQxXF6AivezRM2zpXtaubT0/t4GYt5MhNRDghg6DkQybYQwz6Trhx0yl2n3vmYQ0DfqKar2+P46HT7s88+w/dSHDBpwzkCXDjOSW5Gwv7FcUvVee293f1R67vSd+z4khmJOB6FvmXuF794OzYKLIaLDSUNOUgAQ3dQdBNSrlwc1+jxNX/M7e1t8tRTT7JAzgAh33rrHbl163YskVCdx4KRRhwmgKE7LH7WqVfeSw96H13jZkvYrNUTiW+L13IuVOfZa0oE+SaAoedbv1xHX3kvvRlD18SZes9O/rin2nlULTst6dkeAhi6PVrmMpMoVbomzKr39GXXVe1vvPFWLDvCedGzM1z6OtKjfQQwdPs0zVVGUat0vZ+uq97b29tzlXeegz137l25fv1GrCkcP87K9liB0piTBDB0J2U3K+moVTobzqSnZ1wvX/FH3NOzUY4e3cd3UXoy0pOlBLiILBU2b2lFNXX2ek9e8Xh3gyvHy1R78rrRgzsEMHR3tDY60yiPsXmJYerJSRz3IrhypAWZnn6EXeGSk42WHSOAoTsmuMnpRq3SNbd9+/bI0NAWk9PMXWxq5r/85TmZnZ2LNXaq81hx0hgEBENnEBhDYHmVXv0tbEGCxdSDUAp2jL7b/I033ozdzLV3njsPpgFHQSAoAQw9KCmOS4VAHFU6lXo8UiVVmWt07AoXj0a0AgE/AQyd8WAcgbhMnY1nwkubpJnr63FPnOAxtfDqcCYEqhPA0BkZxhHwT72Xd48L9tKWaomwUK55eXU1u75BLe575l4kPHPevCacAYEgBDD0IJQ4JnUCnql3d3fL3FyLNLstrD9gNfXdu3ex+UwAFZN4NM3fLc+cBxCBQyAQkgCGHhIcpyVPwD/1HsXQNVJ9mcvw8B7p6+tNPvAc9qDbuWpVfvXqnxKLnlXtiaGlYQiUCGDoDASjCcRp6rpNrFbqPNa2XHK9X37hwnsyPn43sbFQKBTk7/7uGN83iRGmYQhg6IyBHBCIa5Gcl+rg4EDpeXX2fxfRrVxHRn6f2P1yj/njjz8qb765wCYyObjeCDG/BPiNOb/aORO5GvrOna9KR0d5urxQCL9IzoPW1dUpw8N7ZcOGdc5w9Ceqz5frFHvcL1mpBnPt2g2yeXO//OY3q0s/PnlyE987To46kk6aABdW0oRpPxYCcS6S8wek1bpOw6vBu/IZGflArlz5OPGqXHkODPRLb+99smpVh8zOzsjvfteNqbsy0MgzdQIYeurI6TAsgRdeeKE4OLht8fSoj7R5Dem99e3bt8n27Q9YPQ0/OnpbLlz4XazvMa+nZW9vrwwMlGdA5uaKpV+adGbg8uU+TD3sRcB5EKhDAENneOSKQPl++s8Xn02Puvrdn7wazo4dD1m3aE6NXO+T37p1OzWte3ruk8HB/sX+ikXdyrcghUKLzM7OY+qpKUFHLhHA0F1S25Jc41z5Xg2JGvvQ0KbcV+zXrt0oLXpL08iVZ6WZ+xlrpd7e3oqpW3ItkoZZBDB0s/QgmoAE1NT37Tsb8Ohwh3lT8UNDQ7m5x67Pk+tCt5GRD1ONFo//AAAOw0lEQVSbWvfTrWfmepxW6N6HSj3cuOQsCNQigKEzNnJLwFv9ft9998nCQovoHuFJfXRjmq1bt4juOmfi425ajauR639JbdnaiO3996+Xvr6euodphd7WVijdU6dSb0SUn0OgOQIYenO8ONowAkktlKuXpq6M37jxflm/fn1mlbtW4npvPGsT9zh5q9mDDg/vXnozpq6/wPnbP3MmzhUUQSPnOAiYSwBDN1cbImuCgH7ZHzz4c5mdLX/nJ1mt+8PS++3r168rPc+epMF7Bq73w0dHbyW6q1sT2EuHNmvmXvv+Kl3/rdrqd7+J62LImZlHZNWq35b+vHRp42KomHuzqnG8jQQwdBtVdTQnv6mnZejVUKvB67S8Pn/d1dUlnZ1dpcPa2lpr7iWvj3Ppf/pR8x4fH5OJianSv42Pj2c2jV5vKBVaCjI0tFE6Vpc3jAn68Va86/HePXW9n+43dc+sPRPv7n5f2vQl6iWOfTI2dr2quWPsQVXgOBsJYOg2qupwTp6pz8wkeEPdYb5e6jozMbixX9paW0PRqGfqo6PTUixOLbY7Pz8lbW1rpFgsm773mZ7+TDo6Niyau/67/iKAqYeShJMsIIChWyAiKSwnoKa+f/+/yPy8f3gz1OMaJ0EWv9XryzNz/VM/8/OyuEBOzVw/fkMPErea++rV98no6DZMPQgwjrGSAN9yVspKUkogq/vqttLXx/g2brq/6Sn2IDz0fnrZzGdXVOJBztdjCoUOuXlzoHQ4lXpQahxnEwEM3SY1yWUFAb+pLyzolz6QwhDQKfbNmwdKu73F/dFKfXT0M5mfn6+5mNHrtt7aCK3S9T779PSMTEzswdTjFor2jCcQ/9VpfMoE6CIBz9jVNObn9b4v99iDjAOtynX1fk9PeWFfnB9vyl3NfGFhQbz/1z484/b//qD/Vvn/lfFola6fe/f+jKnHKRZt5YIAhp4LmQgyDgJMwQenqCvY161dK71994Ve+NaoN39l7j+20rirteM/plrVzvR7I/r83EYCGLqNqpJTXQI//vEnRX2W2fvE8X51m5BHXcEehEUtMw927lKlXsvYdWW8fubm5uTu3YeZfg8ClmNyTwBDz72EJBCWgFexLywUZW4ubCv2nKfmeOLEM6XvhE8+uV7UTWx0J7dicSHWJD0zr5xmb7aTSjPX6Xh/tc70e7NEOT7vBDD0vCtI/JEJULGLHD9eNnL/R7k89ti0zMwsPRMeFXaUyrxW39Wm6CuNndXvUZXj/DwQwNDzoBIxpkLAPWMvyPHjx+p+B3imPj09GXmFexJmXm1gVN5TZ/o9lcuHTgwggKEbIAIhmEXAZmP3qtlqFXktFTxT121o9U1pYT5pmbk/NqbfwyjFOXkmEO7qzHPGxA6BJgj4zb38yFReH3drXI3Xw6Ic9u69Wzqk2XvqWZh5PWNn+r2JC4BDc0UAQ8+VXASbJQHP3JeehTbf3JupxBuxDWPqWZt5+ReQpcyYfm+kMj/PMwEMPc/qEXtmBMys3NW5tBJfucAtLlDNmLoJZu7lzfR7XCOAdkwmgKGbrA6x5YaAGl1X13syN7fw+W5m6VXvSRp4NQGCmLpJZs70e24uIwKNSABDjwiQ0yFQj4Can77Le3p6TprfwKZccZf/W5Djx5815nqtZ+qmmjnT71yrthMw5gvCdtDkBwHbCFQzdZPNnOl320Yg+VQSwNAZExCAQGgCflOfnZ2XTz8tvzUtDx82n8mDSsTYDAEMvRlaHAsBCKwg4Jn6tWsTUijoO811ZXl6awjCSsLq97DkOM9UAhi6qcoQFwRyREBNvb//Rq4MvfyLxxJk9n7P0YAj1KoEMHQGBgQgEImAvuRm//5/lzVr7pdicWrZO8sjNZziyUy/pwibrhIjgKEnhpaGIeAGAdsMnc1n3Bi3NmaJoduoKjlBICUCaubDwz+X3t7BUnXufZZ200spkBi6Yfo9Bog0kSkBDD1T/HQOgXwTsMnQPSWYfs/3mHQ5egzdZfXJHQIRCHhmPjPziKxdOyKtrR2l1vJYnfsxsPo9wqDg1EwJYOiZ4qdzCOSXwPLqfPLzHe3yb+iqCNPv+R2XLkeOobusPrlDIAKBysVwNlTnlTiYfo8wQDg1dQIYeurI6RACdhBYbujlCj3v0+31DJ3V73aMW5uzwNBtVpfcIJAgAW/Kvaenb7EX2wyd6fcEBxBNx04AQ48dKQ1CwA0ClYZeKLSU3gpn64fpd1uVtScvDN0eLckEAqkTUFM/fPhXVlfoXnKsfk99eNFhkwQw9CaBcTgEILBEQA394MHXpKNjgxSLk1Kwcc7dJzir3xn9JhPA0E1Wh9ggYDiB5YZe3inOck8v5cj0u+ED09HwMHRHhSdtCMRFQE39iSdel7a2NVIszjtn6Kx+j2sk0U5UAhh6VIKcDwHHCVRW6S5U6NWqdP23e/f+LBMTe+TSpY1y5owrJBy/AAxKH0M3SAxCgUBeCaipHzjwH9LZ2Z3XFELHzfR7aHScGDMBDD1moDQHAVcJ/PjHnxRXrfqt9Paud2bqvbJSZ/rd1dFvRt4Yuhk6EAUErCDgmbq32Ywrk86sfrdi+OY+CQw99xKSAATMIuA3dds3m6kkz/S7WWPRtWgwdNcUJ18IpEBguamn0KEhXbD5jCFCOBoGhu6o8KQNgaQJYOr6TH75HfGsfk96tNG+EsDQGQcQgEBiBF5++eVia+tT0t8/IoVCpxT9JWxivZrRMNPvZujgUhQYuktqkysEMiCglfrq1b+Vnp51Vr+8pd79dFa/ZzDwHOwSQ3dQdFKGQNoEMHWm39Mecy72h6G7qDo5QyADAt70+8aNH8ncXKtTd/yYfs9gwDnYJYbuoOikDIGsCGil3tHxW+nuZvp9bm5O7t59mG1isxqMFvaLoVsoKilBwGQCWqkPDu6UYtF7O1uLE/fW2XzG5FFpR2wYuh06kgUEckXAv02syPzi60hd2FmO6fdcDdVcBYuh50ougoWAPQRYKCfC6nd7xrMJmWDoJqhADBBwlADT7ytXv588uYnvZUevh6hpM3CiEuR8CEAgEoHK6fdIjeXs5Mp9dkZHtwmGnjMRDQoXQzdIDEKBgKsEWP1ertRv3hxg1burF0EMeWPoMUCkCQhAIDoBpt+XTJ0qPfp4crEFDN1F1ckZAoYScHH63T/tPj39WenZdAzd0AFqeFgYuuECER4EXCPg4vS7Z+q66v3evSk5ceIY382uDfwY8mXQxACRJiAAgXgJuDb97q/SWRgX71hyqTUM3SW1yRUCOSLg2vS7mjoVeo4GqIGhYugGikJIEIBAmYBb0++6BW5Bbt7cwj10LoBQBDD0UNg4CQIQSIuAK9PvXoX+1a8e5Xs5rcFlWT8MHMsEJR0I2EjAlen3mzfZWMbG8ZtWThh6WqTpBwIQiETA9un31tZWOXz4AN/JkUaJ2yczeNzWn+whkCsCNk+/U53naigaGSyGbqQsBAUBCNQiYOP0O9U54z0OAhh6HBRpAwIQSJWAbZX60aN/yXdxqiPIzs4YRHbqSlYQsJ7AmTPF4uHDv5JCzr/FMHPrh2pqCeb8UkiNEx1BAAIGElBTf/rpd2R2tmhgdI1DwswbM+KI4AQw9OCsOBICEDCQgN5T7++/IcXi1GJ0eajaMXMDB1POQ8LQcy4g4UMAAiJaqR858o4U/ZuiGwwGMzdYnByHhqHnWDxChwAElghUVuqmVumYOaM2KQIYelJkaRcCEEidgOmVOmae+pBwqkMM3Sm5SRYC9hMwtVLHzO0fe1lniKFnrQD9QwACsRMwrVLHzGOXmAarEMDQGRYQgICVBEyp1DFzK4eXkUlh6EbKQlAQgEAcBLKu1DHzOFSkjaAEMPSgpDgOAhDIJYGsKnXMPJfDJddBY+i5lo/gIQCBIATSrtQx8yCqcEzcBDD0uInSHgQgYCSBtCp1zNxI+Z0ICkN3QmaShAAElEDSlTpmzjjLkgCGniV9+oYABFInkFSljpmnLiUdVhDA0BkSEICAcwTirtQxc+eGkJEJY+hGykJQEIBA0gTiqtQx86SVov2gBDD0oKQ4DgIQsI5AtEq9KEePHuI71LpRkd+EGIz51Y7IIQCBmAj4q/VCoUVEFhq2TGXeEBEHpEwAQ08ZON1BAAJmEgg6BY+Rm6kfUYlg6IwCCEAAAhCAgAUEMHQLRCQFCEAAAhCAAIbOGIAABCAAAQhYQABDt0BEUjCTgK6g1sjOnClwnZkpEVFBwCoCfNFYJSfJxE3AM+Uvf/laqelLlzaW/tT/r/d3/Xl//w25eXNg8bxG5/jb9Y7ll4G4FaU9CNhLAEO3V1syC0HAM3DPXNWUKz8bNqyW0dFp0T/1c/lyn+zadaf0d/+/t6/uLP3b7PTksn/3H1Pt7/pv+qn2y4Aa/cmTm7huQ2jLKRCwnQBfDLYrTH4NCVRW4Q89dFPa29tL56kpqyHrn+2t5aZm56X0d+9Pfwf6b1E//n60b/8vDfrLg350doDqPSppzoeAXQQwdLv0JJsQBPT5Yz1Nq+z29lZpb19VMuuSoX9u3CUjn56Urq7OxZ+F6CrUKbUMnko9FE5OgoC1BDB0a6UlsWYIqKmroWdh2M3E6R2780sPcO2GAcc5ELCYAF8KFotLauEIqLnvHS7fxzbpg4mbpAaxQMA8Ahi6eZoQkaEE1OgPHpyXyem5xCPEvBNHTAcQsI4Ahm6dpCQEAQhAAAIuEsDQXVSdnCEAAQhAwDoCGLp1kpIQBCAAAQi4SABDd1F1coYABCAAAesIYOjWSUpCEIAABCDgIgEM3UXVyRkCEIAABKwjgKFbJykJQQACEICAiwQwdBdVJ2cIQAACELCOAIZunaQkBAEIQAACLhLA0F1UnZwhAAEIQMA6Ahi6dZKSEAQgAAEIuEgAQ3dRdXKGAAQgAAHrCGDo1klKQhCAAAQg4CIBDN1F1ckZAhCAAASsI4ChWycpCUEAAhCAgIsEMHQXVSdnCEAAAhCwjgCGbp2kJAQBCEAAAi4SwNBdVJ2cIQABCEDAOgIYunWSkhAEIAABCLhI4P8BI9aCVEiaWrMAAAAASUVORK5CYII="/>
                </defs>
              </svg>
              <div class="no-result-found-text">
                No Result Found
              </div>
            </div>
          </div>
        </ng-container>
        <ng-container *ngIf="isSideNavVisible && !isSideNavOpen">
          <div [tooltip]="sideNavTooltipOpen" content-type="template" [options]="toolTipOptions" class="close-state-header-btn" (click)="toggleSideNav()">
            <mat-icon class="close-state-header-icon">keyboard_arrow_right</mat-icon>
            <ng-template #sideNavTooltipOpen style="background-color: #111434;">
              <span class="side-nav-tooltip">Click To View List of Modules</span>
            </ng-template>
          </div>
        </ng-container>
      </div>

      <main-header [ngStyle]="{'margin-left': isSideNavVisible && isSideNavOpen ? '200px' : isSideNavVisible && !isSideNavOpen ? '12px' : ''}" #headerNav *ngIf="!isFullScreenApp"></main-header>

      <div [ngStyle]="{'margin-left': isSideNavVisible && isSideNavOpen ? '200px' : ''}" [ngClass]="!isFullScreenApp ? 'content' : ''">
        <router-outlet></router-outlet>
      </div>
    </mat-sidenav-content>
  </mat-sidenav-container>
</div>