import { Component, NgZ<PERSON>, OnInit, ViewChild } from '@angular/core';
import { Title } from '@angular/platform-browser';
import {
  CdkDragDrop,
  moveItemInArray,
  transferArrayItem,
} from '@angular/cdk/drag-drop';
import { UserExperienceCustomizationService } from './services/user-experience/user-experience-customization.service';
import { LoginService } from 'src/app/services/login/login.service';
import { RolesService } from 'src/app/services/acl/roles.service';
import { MatSidenav } from '@angular/material/sidenav';
import { Subscription } from 'rxjs';
import { MatMenuTrigger } from '@angular/material/menu';
import { UtilityService } from 'src/app/services/utility/utility.service';
import { HostListener } from '@angular/core';
import {
  trigger,
  state,
  style,
  animate,
  transition,
} from '@angular/animations';
import {
  Router,
  RouterEvent,
  NavigationStart,
  NavigationEnd,
  NavigationCancel,
  NavigationError,
} from '@angular/router';
import * as _ from 'underscore';
import { every } from 'rxjs/operators';

//shepherd
import { steps as defaultSteps, defaultStepOptions } from './main.tour';

//pipe component
import { NavLinkPipe } from './main-pipes/nav-link.pipe';
import { TenantService } from 'src/app/services/tenant-service/tenant.service';
import { AtsUtilitiesService } from '../applicant-tracking-system/services/ats-utilities.service';
import { UtilitiesService } from '../kebs-home/services/utilities/utilities.service';

@Component({
  selector: 'app-main',
  templateUrl: './main.component.html',
  styleUrls: ['./main.component.scss'],
  animations: [
    trigger('slideInOut', [
      state(
        'in',
        style({
          transform: 'translate3d(0, 0, 0)',
        })
      ),
      state(
        'out',
        style({
          transform: 'translate3d(100%, 0, 0)',
        })
      ),
      transition('in => out', animate('400ms ease-in-out')),
      transition('out => in', animate('400ms ease-in-out')),
    ]),
  ],
})
export class MainComponent implements OnInit {
  
  isFullScreenApp: any = false;

  //viewChilds
  @ViewChild('sidenav2') sidenav: MatSidenav;
  @ViewChild(MatMenuTrigger) contextMenu: MatMenuTrigger;
  //private properties
  //!any
  menuItem: any = [];

  categorizedApps: any = [];
  categorizedAppsKeys: any = [];
  contextMenuPosition = { x: '0px', y: '0px' };
  isWebTourEnabled: number = 0;
  navBarColor = '#1E2733';
  screenHeight;
  //?strings
  currentlyActiveSideNav: string;
  searchText: string = '';
  //!booleans
  showMoreAppsContainer: boolean = false;
  isCategoryMode: boolean = false;
  isCustomizeMode: boolean = false;
  isIconAlone: number = 0;
  showLoader: boolean;
  palette: number;
  neededCount: number;
  //ToolTip config
  toolTipOptions = {
    placement: 'right',
    'show-delay': 100,
  };
  webTourIconClicked = false;
  appCountWithLabel = 6;
  appCountWithoutLabel = 6;
  //observable subscriptions
  expenseMsgSubscription: Subscription;
  pinned_arr: any = [];
  tenant_logo: any ="https://assets.kebs.app/images/KEBS_logomark.svg";
  tenant_logo_height: any= 65;
  tenant_logo_width: any = 65;
  tenant_fav_icon: any="https://assets.kebs.app/images/kebs_favicon.png";
  tenant_title: any = "KEBS"

  isSideNavVisible: boolean = false;
  isSideNavOpen: boolean = true;
  sideNavAppName: string = '';
  searchSideNavText: string = '';
  sideNavApps = [];

  private sideNavSubscription: Subscription;

  constructor(
    private _userExp: UserExperienceCustomizationService,
    private _loginService: LoginService,
    private $router: Router,
    private _utilityService: UtilityService,
    private rolesService: RolesService,
    private ngZone: NgZone,
    private _navLinkPipe: NavLinkPipe,
    private tenantService: TenantService,
    private title: Title,
    private _atsUtilitiesService: AtsUtilitiesService,
    private _utilitiesService: UtilitiesService
  ) {
    $router.events.subscribe((event: RouterEvent) => {
      this._navigationInterceptor(event);
    });
    this.$router.events.subscribe((event: any) => {
      if (event instanceof NavigationEnd) {
        this.triggerSideMenu(event);
      }
    });
  }

  async ngOnInit() {
    await this.tenantService.getTenantInfo().then((res)=>{
      this.tenant_logo = res ?  res['tenant_image'] : this.tenant_logo 
      this.tenant_logo_height = res ? res['tenant_height'] : this.tenant_logo_height
      this.tenant_logo_width = res ?  res['tenant_width'] : this.tenant_logo_width
      this.tenant_fav_icon = res ? res['tenant_fav_icon']? res['tenant_fav_icon'] :  this.tenant_fav_icon: this.tenant_fav_icon
      this.tenant_title = res? res['tenant_title'] ? res['tenant_title'] : this.tenant_title: this.tenant_title
      //console.log(this.tenant_logo, this.tenant_logo_height, this.tenant_logo_width, this.tenant_fav_icon) 
      
      this.title.setTitle(this.tenant_title)

      
      var link = document.getElementById("index-fav-icon");
      if (!link) {
          link = document.createElement('link');
          link['rel'] = 'icon';
          document.head.appendChild(link);
      }
      link['href'] = this.tenant_fav_icon;

      
  
    })

    this._utilitiesService.getKebsUiTheme();
    
    this._userExp.getNavConfig().then(
      (res: any) => {
        if (res.messType == 'S') {
          //Existing user
          this.navBarColor =
            res.messData.nav_bar_color && res.messData.nav_bar_color != null
              ? res.messData.nav_bar_color
              : '#1E2733';

          this.isIconAlone = res.messData.is_icon_alone;

          this._userExp.neededCount =
            this.isIconAlone == 1
              ? this.appCountWithoutLabel
              : this.appCountWithLabel;
        } else {
          // New User
          this._userExp.neededCount = this.appCountWithLabel;
          this.isIconAlone = 0;
          this.navBarColor = '#1E2733';
        }
        this.palettePosition();
        this.refresh();
      },
      (err) => {
        console.log(err);
      }
    );

    this._utilityService.getIsFullScreenAppValue.subscribe(
      (isFullScreenAppValue) => {
        this.isFullScreenApp = isFullScreenAppValue;
      }
    );
  }

  async ngAfterViewInit() {
    this._userExp.checkIfTourEnabled().then((res: any) => {
      if (res.messType == 'S') {
        this.isWebTourEnabled = res.messData.nav_web_tour;

        if (this.isWebTourEnabled == 1) {
          this._userExp.updateWebTour(0);
          this._userExp.startTour(defaultStepOptions, defaultSteps);
        }
      } else {
        console.log(res);
      }
    });
  }

  enableWebTour(event) {
    event.preventDefault()
    event.stopPropagation()
    this.webTourIconClicked = true;
    this.showMoreAppsContainer = false;
    this._userExp.startTour(defaultStepOptions, defaultSteps);
  }

  //private methods
  private _navigationInterceptor(event: RouterEvent): void {
    if (event instanceof NavigationStart) {
      this.ngZone.runOutsideAngular(() => {
        this.showLoader = true;
      });
    }
    if (event instanceof NavigationEnd) {
      this._hideSpinner();
    }
    if (event instanceof NavigationCancel) {
      this._hideSpinner();
    }
    if (event instanceof NavigationError) {
      this._hideSpinner();
    }
  }
  private _hideSpinner(): void {
    this.ngZone.runOutsideAngular(() => {
      this.showLoader = false;
    });
  }

  changeInsearch(event) {}

  async drop(event: CdkDragDrop<string[]>) {
    await moveItemInArray(
      this._userExp.pinnedApps,
      event.previousIndex,
      event.currentIndex
    );
    this.repositionPinnedApps();
    this.updateItems();
  }

  moreAppClicked(apps) {
    // console.log(apps);

    let navLink = this._navLinkPipe.transform(apps);

    this.$router.navigateByUrl('/main/' + navLink);

    this.showMoreAppsContainer = !this.showMoreAppsContainer;

    //For web tour (shepherd)
    if (this._userExp.checkIfTourIsActive()) {
      if (
        (this.isWebTourEnabled == 1 && this.showMoreAppsContainer == true) ||
        (this.webTourIconClicked == true && this.showMoreAppsContainer == true)
      ) {
        this._userExp.nextTour();
      }
    } else {
      this._userExp.completeTour();
    }
  }

  showMoreapps(event) {
    event.preventDefault()
    event.stopPropagation()
    this.showMoreAppsContainer = !this.showMoreAppsContainer;

    //For web tour (shepherd)
    if (this._userExp.checkIfTourIsActive()) {
      if (
        (this.isWebTourEnabled == 1 && this.showMoreAppsContainer == true) ||
        (this.webTourIconClicked == true && this.showMoreAppsContainer == true)
      ) {
        this._userExp.nextTour();
      }
    } else {
      this._userExp.completeTour();
    }
  }

  onContextMenu(event: MouseEvent, item: any) {
    event.preventDefault();
    event.stopPropagation();
    this.contextMenuPosition.x = event.clientX + 'px';
    this.contextMenuPosition.y = event.clientY + 'px';
    this.contextMenu.menuData = { item: item };
    this.contextMenu.menu.focusFirstItem('mouse');
    this.contextMenu.openMenu();
  }

  async refresh() {
    let oid = this._loginService.getProfile().profile.oid;
    this._userExp.menuAppList = await this._userExp.refreshList(oid);
    console.log('refresh', this._userExp.menuAppList);
    this.autoSetPinnedApps();
    // this._utilityService.showMessage("Applications Updated!", "Dismiss");
  }

  openInNewTab = (module) => {
    
    let navLink = this._navLinkPipe.transform(module);

    let navigationUrl = window.location.origin + '/main/' + navLink;

    window.open(navigationUrl);
    
  };

  autoSetPinnedApps = async () => {
    this._userExp.pinnedApps = await this._userExp.getPinnedApps();

    // If the user has pinned apps
    if (this._userExp.pinnedApps != null && this._userExp.pinnedApps.length > 0) {
      let menu_item_ids = _.pluck(this._userExp.menuAppList, 'id');

      /* To check whether the pinned apps are auth to user if the user role is changed in between.
        Example, if the user has the auth to opportunity application and pinned, later his
        access to the opportunity application is removed, this will remove the opportunity from pinned array
      */
      this._userExp.pinnedApps = _.filter(this._userExp.pinnedApps, (item) => {
        return menu_item_ids.includes(item.id);
      });

      let pinned_apps_ids = _.pluck(this._userExp.pinnedApps, 'id');

      for (let i = 0; i < this._userExp.menuAppList.length; i++) {
        for (let j = 0; j < pinned_apps_ids.length; j++) {
          if (this._userExp.menuAppList[i].id == pinned_apps_ids[j]) {
            this._userExp.menuAppList[i].isPinned = true;
            break;
          } else {
            this._userExp.menuAppList[i].isPinned = false;
          }
        }
      }

      this.repositionPinnedApps();
    }

    // If the user dont have pinned apps (New user)
    else {
      let pinnedApps = [];
      pinnedApps = _.where(this._userExp.menuAppList, { isPinned: true });
      let mainMenuAppsCount =
        this._userExp.menuAppList.length > this._userExp.neededCount
          ? this._userExp.neededCount
          : this._userExp.menuAppList.length;
      let pinnedAppsCount = pinnedApps.length;
      let toBePinnedCount = mainMenuAppsCount - pinnedAppsCount;

      if (toBePinnedCount < mainMenuAppsCount) {
        let count = 0;

        this._userExp.menuAppList.forEach((app, index) => {
          if (count == toBePinnedCount) return '';
          if (app.isPinned == false) {
            app.isPinned = true;
            count++;
          }
        });
      }

      if (toBePinnedCount == mainMenuAppsCount) {
        for (let i = 0; i < mainMenuAppsCount; i++) {
          this._userExp.menuAppList[i].isPinned = true;
        }
      }
      this._userExp.pinnedApps = _.where(this._userExp.menuAppList, { isPinned: true });
      this.repositionPinnedApps();
    }
    this._userExp.menuAppList = _.sortBy(
      _.sortBy(this._userExp.menuAppList, 'label').reverse(),
      (item) => {
        return item.isPinned;
      }
    ).reverse();

    // console.log('menuItems',this._userExp.menuAppList)
    // console.log('pinnedApps',this.pinnedApps)

    this.categorizedApps = _.groupBy(this._userExp.menuAppList, 'category');
    this.categorizedAppsKeys = Object.keys(this.categorizedApps);

    // this.updateItems();
  };

  get isLargeScreen() {
    const width =
      window.innerWidth ||
      document.documentElement.clientWidth ||
      document.body.clientWidth;
    if (width > 720) return true;
    else return false;
  }

  // pinItem(appIndex) {

  //   if (this._userExp.menuAppList[appIndex].isPinned == false || this._userExp.menuAppList[appIndex].isPinned == null || this._userExp.menuAppList[appIndex].isPinned == undefined) {
  //     let pinnedItems = _.where(this._userExp.menuAppList, { isPinned: true });
  //     if (pinnedItems.length == this._userExp.neededCount) {
  //       this._utilityService.showMessage(`You cannot pin more than ${this._userExp.neededCount} Apps`, "dismiss", 2000)
  //     }
  //     else {
  //       this._userExp.menuAppList[appIndex].isPinned = true;
  //       this.updateItems()
  //     }
  //   }
  //   else {
  //     this._userExp.menuAppList[appIndex].isPinned = false;
  //     this.updateItems()
  //   }
  // }

  changeToCategoryMode(event) {
    event.preventDefault()
    event.stopPropagation()
    this.isCategoryMode = !this.isCategoryMode;
    this.isCustomizeMode = false;
    // console.log(this.categorizedApps)
  }

  customize(event) {
    event.preventDefault()
    event.stopPropagation()
    this.isCustomizeMode = !this.isCustomizeMode;
    this.isCategoryMode = false;

    //For web tour (shepherd)
    if (this._userExp.checkIfTourIsActive()) {
      if (
        (this.isWebTourEnabled == 1 && this.isCustomizeMode == true) ||
        (this.webTourIconClicked == true && this.isCustomizeMode == true)
      ) {
        this._userExp.nextTour();
      }
    } else {
      this._userExp.completeTour();
    }

    this.isWebTourEnabled = 0;
    this.webTourIconClicked = false;
  }

  pinCategorizedApp(id,event) {
    event.preventDefault();
    event.stopPropagation()
    this._userExp.menuAppList.forEach((element) => {
      if (element.id == id) {
        if (
          element.isPinned == false ||
          element.isPinned == null ||
          element.isPinned == undefined
        ) {
          let pinnedItems = _.where(this._userExp.menuAppList, { isPinned: true });
          // console.log("needed count",this._userExp.neededCount)
          // console.log("pinned len",pinnedItems.length)
          if (pinnedItems.length >= this._userExp.neededCount) {
            this._utilityService.showMessage(
              `You cannot pin more than ${this._userExp.neededCount} Apps`,
              'dismiss',
              2000
            );
          } else {
            element.isPinned = true;
            this._userExp.pinnedApps.push(element);
            this._userExp.pinnedApps[this._userExp.pinnedApps.length - 1].position =
              this._userExp.pinnedApps.length - 1;
            this.repositionPinnedApps();
            this.updateItems();
          }
        } else {
          element.isPinned = false;
          this._userExp.pinnedApps = _.filter(this._userExp.pinnedApps, (item) => {
            return item.id != element.id;
          });
          this.repositionPinnedApps();
          this.updateItems();
        }
      }
    });
  }

  updateItems() {

    for (let pinnedItem of this._userExp.pinnedApps) {

      let defaultItem = _.findWhere(this._userExp.menuAppList, { id: pinnedItem['id'] });

      if (defaultItem) {

        pinnedItem['label'] = defaultItem['label'];
        pinnedItem['application_id'] = defaultItem['application_id'] ? defaultItem['application_id'] : null;
        pinnedItem['application_name'] = defaultItem['application_name'];
        pinnedItem['icon'] = defaultItem['icon'];
        pinnedItem['link'] = defaultItem['link'];
        pinnedItem['disabled'] = defaultItem['disabled'];
        pinnedItem['category'] = defaultItem['category'];

      }

    }

    // this._userExp
    //   .updateNavMenu(
    //     this._userExp.menuAppList,
    //     this.pinnedApps,
    //     this._loginService.getProfile().profile
    //   )
    //   .subscribe(
    //     (res: any) => {
    //       console.log(res);
    //     },
    //     (err) => {
    //       console.error(err);
    //     }
    //   );

    this._userExp
    .updateNavMenu(
      this._userExp.menuAppList,
      this._userExp.pinnedApps,
      this._loginService.getProfile().profile
    )

  }


  async navColor(color, selected,event) {
    event.preventDefault()
    event.stopPropagation()
    this.navBarColor = color;
    this.palette = selected;
    await this._userExp.updateColor(this.navBarColor);
  }

  /* On changing view from normal to category and vice versa, the pinned apps count
      needs to be changed
      pinned apps with label; count = 7
      pinned apps without label; count = 9 */
  async changeView(res, eve) {
    eve.preventDefault()
    eve.stopPropagation()
    this.isIconAlone = res;
    this._userExp.neededCount =
      this.isIconAlone == 1
        ? this.appCountWithoutLabel
        : this.appCountWithLabel;
    let pinned_apps = _.where(this._userExp.menuAppList, { isPinned: true });
    let removedAppsIds = [];
    if (eve.checked) {
      if (
        this.isIconAlone == 0 &&
        pinned_apps.length > this.appCountWithLabel
      ) {
        let reducePinnedApps = pinned_apps.length - this.appCountWithLabel;
        for (let i = this._userExp.menuAppList.length - 1; i > 0; i--) {
          if (reducePinnedApps > 0) {
            if (this._userExp.menuAppList[i].isPinned == true) {
              removedAppsIds.push(this._userExp.menuAppList[i].id);
              this._userExp.menuAppList[i].isPinned = false;
              reducePinnedApps -= 1;
            }
          } else {
            break;
          }
        }
        this._userExp.pinnedApps = _.filter(this._userExp.pinnedApps, (item) => {
          return removedAppsIds.indexOf(item.id) == -1;
        });
        this.repositionPinnedApps();
        this.updateItems();
      }
      await this._userExp.updateView(this.isIconAlone);
    }
  }

  palettePosition() {
    switch (this.navBarColor) {
      case '#1E2733':
        this.palette = 1;
        break;
      case '#CF0001':
        this.palette = 2;
        break;
      case '#040634':
        this.palette = 3;
        break;
      case '#0C342C':
        this.palette = 4;
        break;
      case '#00917C':
        this.palette = 5;
        break;
      case '#000000':
        this.palette = 6;
        break;
      case '#9F2825':
        this.palette = 7;
        break;
      case '#4A3933':
        this.palette = 8;
        break;
      case '#87556F':
        this.palette = 9;
        break;
    }
  }

  // @HostListener('window:resize', ['$event'])
  //   getScreenSize(event?) {
  //         this.screenHeight = window.innerHeight;
  //         if(this.screenHeight<600 && this.isIconAlone ){
  //           this._userExp.neededCount = 6
  //         }
  //         else if{
  //           this._userExp.neededCount = 9
  //         }
  //         console.log('screen height',this.screenHeight);
  //   }

  /* Position is used for drag and drop */
  repositionPinnedApps() {
    if (this._userExp.pinnedApps.length > 0) {
      for (let pos = 0; pos < this._userExp.pinnedApps.length; pos++) {
        this._userExp.pinnedApps[pos]['position'] = pos;
      }
    }
  }

  unPinAllApps(event) {
    event.preventDefault();
    event.stopPropagation()
    this._userExp.menuAppList.forEach((element) => {
      element.isPinned = false;
    });
    this._userExp.pinnedApps = [];

    this.repositionPinnedApps();
    this.updateItems();
  }

  closePannel() {
    this.showMoreAppsContainer = !this.showMoreAppsContainer;
    this._userExp.completeTour();
    this.isWebTourEnabled = 0;
    this.webTourIconClicked = false;
  }

  onClickCloseIcon(event){
    event.preventDefault()
    event.stopPropagation()
    this.searchText = ""
  }

  onSearchBarClick(event){
    event.preventDefault()
    event.stopPropagation()
  }

  onClickCheckbox(res,event){
    event.preventDefault()
    event.stopPropagation()
    this.changeView(res,event)
  }
  onHeaderClick(event){
    event.preventDefault()
    event.stopPropagation()
  }

  @HostListener('document:click', ['$event'])
  onCloseClick(event) {
   if(this.showMoreAppsContainer){
     this.closePannel()
   }
  }

  triggerSideMenu(event) {
    let segments = event.url.split('/');
    if (segments.length >= 3) {
      let payload = segments[2];
      // Mention application link here for API to hit
      if (['ats'].includes(payload)) {
        if (this.sideNavSubscription) {
          this.sideNavSubscription.unsubscribe();
        }

        this.sideNavSubscription = this._userExp.getAppSideNav(payload, this._loginService.getProfile().profile.aid).subscribe((res: any) => {
          if (res.messType == 'S') {
            this.resolvePinnedApps(res['data']);
            this.sideNavAppName = res['app_name'];
            this.isSideNavVisible = true;
            this.isSideNavOpen = this.isSideNavOpen ? true : false;
          } else {
            this.isSideNavVisible = false;
          }
        }, (err: any) => {
          this.isSideNavVisible = false;
        });
        // To setup master UI color config for ATS
        this._atsUtilitiesService.getAtsMasterUiConfig();
      } else {
        if (this.sideNavSubscription) {
          this.sideNavSubscription.unsubscribe();
        }
        this.isSideNavVisible = false;
      }
    } else {
      if (this.sideNavSubscription) {
        this.sideNavSubscription.unsubscribe();
      }
      this.isSideNavVisible = false;
    }
  }

  async navigateSubSideNav(navLink) {
    if (navLink) {
      this.$router.navigateByUrl(navLink);
    }
  }

  pinSubSideNavApp(selectedId) {
    let index = this.sideNavApps.findIndex((obj) => obj.id == selectedId);
    if (index != -1) {
      this.sideNavApps[index]['is_pinned'] = !this.sideNavApps[index]['is_pinned'];
      let data = _.pluck(this.sideNavApps.filter((obj) => obj.is_pinned == true), 'id');
      localStorage.setItem('sub-side-nav-' + this._loginService.getProfile().profile.aid, JSON.stringify(data));
      this.sideNavApps = [...this.sideNavApps];
    }
  }

  resolvePinnedApps(data) {
    let result = data;
    let localStorageData = localStorage.getItem('sub-side-nav-' + this._loginService.getProfile().profile.aid);
    let parsedData = JSON.parse(localStorageData);
    if (parsedData && parsedData.length > 0) {
      for (let i=0; i<parsedData.length; i++) {
        let index = result.findIndex((obj) => obj.id == parsedData[i]);
        if (index != -1) {
          result[index]['is_pinned'] = true;
        }
      }
      this.sideNavApps = result;
    } else {
      this.sideNavApps = result;
    }
  }

  toggleSideNav() {
    this.isSideNavOpen = !this.isSideNavOpen;
  }
}
