import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MatListModule } from '@angular/material/list';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { MatMenuModule } from '@angular/material/menu';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MainRoutingModule } from './main-routing.module';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatDialogModule } from '@angular/material/dialog';
import { Ng2SearchPipeModule } from 'ng2-search-filter';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { TooltipModule } from 'ng2-tooltip-directive';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
//userDefined modules
import { DirectiveModule } from './directives/directive.module';
import { MainPipesModule } from './main-pipes/main-pipes.module';

//components
import { MainComponent } from './main.component';
import { MainHeaderComponent } from './components/main-header/main-header.component';
import { BreadcrumbComponent } from './components/breadcrumb/breadcrumb.component';
import { HeaderUserImageComponent } from './components/header-user-image/user-image.component';
import { CustomProgressBarInfinteComponent } from './components/custom-progress-bar-infinte/custom-progress-bar-infinte.component';
import { FormsModule } from '@angular/forms';
import { ReactiveFormsModule } from '@angular/forms';
import { OtpInputModule } from 'kebs-lms-otp-input';
import { OverlayModule } from "@angular/cdk/overlay";
import { SearchSuggestPopupComponent } from './components/main-header/search-suggest-popup/search-suggest-popup.component';
import { AttachmentMgmtModule } from 'src/app/modules/shared-lazy-loaded-components/attachment-mgmt/attachment-mgmt.module';
import { SearchResultsModule } from '../search-results/search-results.module';
import { ChatbotDialogComponent } from './components/main-header/ai-features/chatbot-dialog/chatbot-dialog.component';
import { MatDividerModule } from '@angular/material/divider';
import { InfiniteScrollModule } from 'ngx-infinite-scroll';
import { ChatUiComponent } from './components/main-header/ai-features/chat-ui/chat-ui.component';
import { SharedComponentsModule } from 'src/app/app-shared/app-shared-components/components.module';
import { ClipboardModule } from '@angular/cdk/clipboard';
import { ChatResponseStructureComponent } from './components/main-header/ai-features/chat-response-structure/chat-response-structure.component';
import { MarkdownCustomPipe } from './components/main-header/ai-features/pipes/markdown-custom/markdown-custom.pipe';
import { FilterPromptLibraryPipe } from './components/main-header/ai-features/pipes/filter-prompt-library/filter-prompt-library.pipe';
import { SafeHtmlPipe } from './components/main-header/ai-features/pipes/safe-html/safe-html.pipe';
import { ChatCustomReportComponent } from './components/main-header/ai-features/chat-custom-report/chat-custom-report.component';
import { MatSelectModule } from '@angular/material/select';
import { KaisLandingPageComponent } from './components/main-header/kais/kais-landing-page/kais-landing-page.component';

@NgModule({
  declarations: [
    MainComponent,
    MainHeaderComponent,
    BreadcrumbComponent,
    HeaderUserImageComponent,
    CustomProgressBarInfinteComponent,
    SearchSuggestPopupComponent,
    ChatbotDialogComponent,
    ChatUiComponent,
    ChatResponseStructureComponent,
    MarkdownCustomPipe,
    FilterPromptLibraryPipe,
    SafeHtmlPipe,
    ChatCustomReportComponent,
    KaisLandingPageComponent
  ],
  imports: [
    CommonModule,
    MainRoutingModule,
    ReactiveFormsModule,
    OtpInputModule,
    OverlayModule,
    Ng2SearchPipeModule,
    MatIconModule,
    MatListModule,
    MatToolbarModule,
    MatSidenavModule,
    MatButtonModule,
    MatTooltipModule,
    DragDropModule,
    FormsModule,
    MatMenuModule,
    MatProgressSpinnerModule,
    MatDialogModule,
    DirectiveModule,
    MainPipesModule,
    MatCheckboxModule,
    TooltipModule,
    MatInputModule,
    MatFormFieldModule,
    AttachmentMgmtModule,
    SearchResultsModule,
    MatDividerModule,
    InfiniteScrollModule,
    SharedComponentsModule,
    ClipboardModule,
    MatSelectModule
  ],
})
export class MainModule {}
