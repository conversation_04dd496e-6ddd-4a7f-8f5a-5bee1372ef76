import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { of } from 'rxjs';
@Injectable({
  providedIn: 'root',
})
export class AiServiceService {
  constructor(private _http: HttpClient) {}

  aiThemeConfig: any = {}

  getUiConfiguration(key) {
    return this._http.post('api/insights/kebsHomePage/getUiConfiguration', {
      key: key,
    });
  }

  defaultPrompts(payload) {
    return this._http.post('api/atsai/chatbot/defaultPrompts', payload);
  }

  getChatbotModulesBasedOnRoleAccess(payload) {
    return this._http.post(
      'api/atsai/chatbot/getChatbotModulesBasedOnRoleAccess',
      payload
    );
  }

  allHistory(payload) {
    return this._http.post('api/atsai/agent/get_chat_thread_list', payload);
  }

  allPinnedHistory(payload) {
    return this._http.post('api/atsai/agent/getPinnedThreads', payload);
  }

  deleteThread(payload) {
    return this._http.post('api/atsai/agent/deleteThreadName', payload);
  }

  archiveOrUnarchiveThread(payload) {
    return this._http.post('api/atsai/chatbot/allUnarchiveAndArchive', payload);
  }

  getPromptResponse(payload) {
    return this._http.post('api/atsai/chatbot/prompts', payload);
  }

  likeAndDislikeResponse(payload) {
    return this._http.post('api/atsai/agent/updateFeedback', payload);
  }

  changeDescriptionOfThread(payload) {
    return this._http.post('api/atsai/agent/updateThreadName', payload);
  }

  retrieveThreadHistory(payload) {
    return this._http.post('api/atsai/agent/get_chat_history', payload);
  }

  pinOrUnpinThread(payload) {
    return this._http.post('api/atsai/agent/pinThread', payload);
  }

  getPromptLibraryData(payload) {
    return this._http.post('api/atsai/chatbot/getPromptLibraryData', payload);
  }

  getDefaultPromptsBySearch(payload) {
    return this._http.post('api/atsai/chatbot/getPromptsData', payload);
  }

  getMasterDataUsingApi(apiUrl: any) {
    return new Promise((resolve, reject) => {
      this._http.post(apiUrl, []).subscribe(
        (res: any) => {
          resolve(res);
        },
        (err) => {
          console.log(err);
          reject(err);
        }
      );
    });
  }

  // getChatHeading(payload) {
  //   return new Promise((resolve, reject) => {
  //     this._http.post('api/atsai/agent/get_chat_heading', payload).subscribe(
  //       (res: any) => {
  //         resolve(res);
  //       },
  //       (err) => {
  //         console.log(err);
  //         reject(err);
  //       }
  //     );
  //   });
  // }

  getChatHeading(payload) {
    return this._http.post('api/atsai/agent/get_chat_heading', payload);
  }


  getIntentResponse(payload) {
    // console.log(payload)
    // const mockResponse = {
    //   err: false,
    //   data: "Intent Classification Agent",
    //   response: {
    //     intent: ["unknown"],
    //     slot_labels: [],
    //     extracted_slots: [],
    //     confidence_score: 0.0,
    //     action: "no_match"
    //   }
    // };
  
    // return of(mockResponse); 
    return this._http.post('api/atsai/agent/intent_agent', payload);
  }

  getAgentResponse(payload){
    // const mockResponse = {
    //   err: false,
    //   data: "Intent Classification Agent",
    //   response: {
    //     output: "Got output from agent"
    //   }
    // };
  
    // return of(mockResponse); 
    return this._http.post('api/atsai/agent/chat', payload);
  }

  getAgentResponseForMultipleIntents(payload){
    // const mockResponse = {
    //   err: false,
    //   data: "Intent Classification Agent",
    //   response: {
    //     output: "Got output from agent"
    //   }
    // };
  
    // return of(mockResponse); 
    return this._http.post('api/atsai/agent/checkUserIntent', payload);
  }

  getHealthStatus(){
    return this._http.post('api/atsai/health/status', {});
  }

  notifyError(payload) {
    return new Promise((resolve, reject) => {
      this._http.post('api/pm/aierror/logAndNotify', payload).subscribe(
        res =>  {
          return resolve(res);
        },
        err => {
          return resolve(err);
        }
      );
    });
  }

  recordError(payload){
    return new Promise((resolve, reject) => {
      this._http.post('api/atsai/error/log', payload).subscribe(
        res =>  {
          return resolve(res);
        },
        err => {
          return resolve(err);
        }
      );
    });
  }

  getCachedOutput(payload){
    return this._http.post('api/atsai/agent/getCachedOutput', payload);
  }

}
