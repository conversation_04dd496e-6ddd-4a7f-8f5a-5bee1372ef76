<div class="flag-change-style">
    <div class="loader-container" *ngIf="loading">
        <div class="loader"></div>
    </div>
    <span *ngIf="!loading">
        <div class="row header">
            <div class="col-4"></div>
            <div class="header-title col-4">{{dialogData.label || 'Flag Change Wizard'}}</div>
            <div class="col-4">
                <mat-icon class="close-button" mat-icon-button [mat-dialog-close] matTooltip="Close">close</mat-icon>
            </div>
        </div>
        <div class="col-12 content" style="overflow: hidden; padding-bottom: 30px">
            <div class="risk-status-wrapper">
                <span class="risk">Risk Status -</span>
                <div class="rc-stat" [ngStyle]="{'background-color': dialogData.at_risk == 1 ? '#1890FF' : '#F27A6C'}">
                    <span class="status">{{
                        dialogData.at_risk == 1 ? "Secured" :dialogData?.riskLabel || 'At Risk'
                        }}</span>
                    <span class="arrow-down"></span>
                </div>
            </div>

            <div class="checklist-box" *ngIf="dialogData.at_risk == 1">
                <h3 class="tab-title">
                    Checklist
                </h3>

                <div class="scroll">
                    <!-- Dynamic Checklist Items from constraints_list -->
                    <div *ngFor="let constraint of constraints_list" class="checklist-item">
                        <div [ngClass]="
                        { 'checkbox1 checkbox-error': !constraint.status,
                            checkbox: constraint.status
                        }"></div>
                        <span class="item3">{{ constraint.constraint }}</span>
                    </div>
                </div>
            </div>
            <div class="checklist-boxn" *ngIf="dialogData.at_risk == 0">
                <div class="checklist-item3">
                    <div></div>
                    <span> Opportunity Already Secured! </span>
                </div>
            </div>

            <div class="divider"></div>
            <div class="row footer-buttons" *ngIf="dialogData.at_risk == 1">
                <div class="button-container d-flex align-items-center justify-content-end">
                    <button class="save-button" mat-raised-button *ngIf="hasRedirect" (click)="redirect()">
                        Redirect
                    </button>
                    <button class="save-button" mat-raised-button [disabled]="!flagcheck" (click)="proceed()">
                        {{ save }}
                    </button>
                </div>
            </div>
        </div>
    </span>
</div>