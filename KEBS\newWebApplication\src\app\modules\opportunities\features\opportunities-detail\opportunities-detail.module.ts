import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
//components
import { OpportunitiesDetailRoutingModule } from './opportunities-detail-routing.module';
import { OpportunityOverviewComponent } from './pages/opportunity-overview/opportunity-overview.component';
import { OpportunityAttachmentsComponent } from './pages/opportunity-attachments/opportunity-attachments.component';
import { OpportunityActivitiesComponent } from './pages/opportunity-activities/opportunity-activities.component';
import { OpportunityTabsComponent } from './pages/opportunity-tabs/opportunity-tabs.component';
import { OpportunityDetailedViewComponent } from '../opportunities-detail/components/opportunity-detailed-view/opportunity-detailed-view.component';
import { OpportunityIsaComponent } from './pages/opportunity-internalStakeHolder/opportunity-tabs-isa.component';

import { ApprovalDetailsComponent } from '../opportunities-detail/components/approval-details/approval-details.component';
import { ActivityTaskDetailComponent } from '../opportunities-detail/components/activity-task-detail/activity-task-detail.component';
import { CheckListDetailsComponent } from '../opportunities-detail/components/check-list-details/check-list-details.component';
import { OppActivityNotesComponent } from '../opportunities-detail/components/opp-activity-notes/opp-activity-notes.component';
import { ChatCommentComponent } from '../opportunities-detail/components/chat-comment/chat-comment.component';

import { InlineEdit2Component } from '../opportunities-detail/components/inline-edit2/inline-edit2.component';
import { CommonFilterComponent } from './components/common-filter/common-filter.component';
import { S3AttachmentComponent } from '../opportunities-detail/components/s3-attachment/s3-attachment.component';
import { ActivityCardGeneralComponent } from '../opportunities-detail/components/activity-card-general/activity-card-general.component';
import { ActivityCardMiniComponent } from '../opportunities-detail/components/activity-card-mini/activity-card-mini.component';
import { InlineFormFieldComponent } from '../opportunities-detail/components/inline-form-field/inline-form-field.component';
import { DirectiveModule } from "src/app/modules/main/directives/directive.module";
//Angular  modules

import { ScrollingModule } from '@angular/cdk/scrolling';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatSelectModule } from '@angular/material/select';
import { FileUploadModule } from 'ng2-file-upload';
import { MatListModule } from '@angular/material/list';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatTabsModule } from '@angular/material/tabs';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TooltipModule } from 'ng2-tooltip-directive';
import { NgxContentLoadingModule } from 'ngx-content-loading';
import { InfiniteScrollModule } from 'ngx-infinite-scroll';
import { SatPopoverModule } from "@ncstate/sat-popover";
import {
  MatNativeDateModule,
} from '@angular/material/core';
import { NgxDaterangepickerMd } from 'ngx-daterangepicker-material';
import { MomentModule } from 'ngx-moment';
import { MatTableModule } from '@angular/material/table';
import { OppUserNoteComponent } from './components/opp-user-note/opp-user-note.component'
import { NgxMatSelectSearchModule } from 'ngx-mat-select-search';

//Material
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { MatChipsModule } from '@angular/material/chips';
import { MatStepperModule } from '@angular/material/stepper';
import { MatOptionModule } from '@angular/material/core';

// DevExtereme
import {
  DxSelectBoxModule,
  DxTextAreaModule,
  DxDateBoxModule,
  DxFormModule,
  DxButtonModule,
  DxToolbarModule,
  DxHtmlEditorModule,
  DxPivotGridModule,
  DxDataGridModule,
  DxPopupModule,
  DxChartModule,
  DxTooltipModule,
} from 'devextreme-angular';

//pipes
import { DDMMMYYFormat } from './pipes/reportDateFormat.pipe';
import { SafeHtmlPipe } from './pipes/safeHtml.pipe';
import { LengthfixPipe } from './pipes/lengthfix.pipe'
import { multiDate } from './pipes/dateFormatMultiple.pipe';
import { DateDDMMYY } from './pipes/dateFormat2.pipe';
import { DateDDMMYYYY } from './pipes/dateFormat.pipe';
import { CurrencyComponent } from '../opportunities-detail/components/currency/currency.component';
import { InlineInputSearchComponent } from '../opportunities-detail/components/inline-input-search/inline-input-search.component';
import { InlineSearchUserComponent } from '../opportunities-detail/components/inline-search-user/inline-search-user.component';
import {
  MatDatepickerModule,
} from '@angular/material/datepicker';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatTreeModule } from '@angular/material/tree';


import { MatMomentDateModule } from '@angular/material-moment-adapter';
import { OpportunityTeamDetailsComponent } from './pages/opportunity-team-details/opportunity-team-details.component';
import { OpportunityTeamComponent } from './components/opportunity-team/opportunity-team.component';
import { UserProfileDetailComponent } from './components/user-profile-detail/user-profile-detail.component';
import { InputSearchComponent } from './components/input-search/input-search.component';
import { SearchUserOpportunityComponent } from './components/search-user-opportunity/search-user-opportunity.component'
import { UserImageComponent } from './components/user-image/user-image.component'
import { UserProfileComponent } from './components/user-profile/user-profile.component';
import { TemplateComponent } from './pages/template/template.component';
import { FileExplorerComponent } from './components/file-explorer/file-explorer.component';
import { FileViewerComponent } from './components/file-viewer/file-viewer.component';
import { DateNotes } from '../../shared/pipes/date-notes.pipe';
import { CrmNotesModule } from 'src/app/modules/account-sales/features/crm-notes/crm-notes.module';
import { DisplayActivitiesModule } from 'src/app/modules/account-sales/features/display-activities/display-activities.module';
import { OpportunityNotesComponent } from './pages/opportunity-notes/opportunity-notes.component';
import { PipesModule } from 'src/app/modules/create-components/create-opportunity/pipes/pipes.module';
import { InlineEditAccessPipe } from './pipes/inline-edit-access.pipe';
import { CurrencyInlineComponent } from '../opportunities-detail/components/currency-inline/currency-inline.component'
import { OpportunityAuditLogComponent } from './pages/opportunity-audit-log/opportunity-audit-log.component';

import { SearchUserE360Component } from './components/search-user-e360/search-user-e360.component';
import { InternalStakeholderModule } from './pages/internal-stakeholder/internal-stakeholder.module';
import { dynamicDatePipe } from 'src/app/modules/account-sales/features/account-sales-detail/pipes/accounts-pipes.module';
import { PipesModule as fileformatPipe } from 'src/app/app-shared/app-shared-components/mail-box-modal/pipes/pipes.module';
import { OpportunityDocumentManagerComponent } from './pages/opportunity-document-manager/opportunity-document-manager.component';
import { DocumentManagerModule } from 'src/app/modules/shared-lazy-loaded-components/document-manager/document-manager.module';
import { OpportunityFlagChangeWizardComponent } from './components/opportunity-flag-change-wizard/opportunity-flag-change-wizard.component'
// import { SharedComponentsModule } from 'src/app/app-shared/app-shared-components/components.module';
@NgModule({
  declarations: [
    OpportunityOverviewComponent,
    InlineEdit2Component,
    InlineSearchUserComponent,
    InlineInputSearchComponent,
    DDMMMYYFormat,
    OppActivityNotesComponent,
    CheckListDetailsComponent,
    ActivityTaskDetailComponent,
    ApprovalDetailsComponent,
    SafeHtmlPipe,
    multiDate,
    DateDDMMYY,
    DateDDMMYYYY,
    ActivityCardMiniComponent,
    InlineFormFieldComponent,
    ChatCommentComponent,
    CurrencyComponent,
    CurrencyInlineComponent,
    ActivityCardGeneralComponent,
    S3AttachmentComponent,
    CommonFilterComponent,
    OpportunityDetailedViewComponent,
    OpportunityAttachmentsComponent,
    OpportunityActivitiesComponent,
    OpportunityTabsComponent,
    OpportunityIsaComponent,
    LengthfixPipe,
    OpportunityTeamDetailsComponent,
    OpportunityTeamComponent,
    UserProfileDetailComponent,
    InputSearchComponent,
    SearchUserOpportunityComponent,
    UserImageComponent,
    UserProfileComponent,
    TemplateComponent,
    FileExplorerComponent,
    FileViewerComponent,
    OppUserNoteComponent,
    DateNotes,
    OpportunityNotesComponent,
    InlineEditAccessPipe,
    OpportunityAuditLogComponent,
    SearchUserE360Component,
    OpportunityDocumentManagerComponent,
    OpportunityFlagChangeWizardComponent
  ],
  imports: [
    CommonModule,
    OpportunitiesDetailRoutingModule,
    NgxContentLoadingModule,
    MatFormFieldModule,
    MatDatepickerModule,
    MatIconModule,
    MatMenuModule,
    TooltipModule,
    InfiniteScrollModule,
    MatNativeDateModule,
    NgxDaterangepickerMd.forRoot(),
    MatInputModule,
    FormsModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatTooltipModule,
    FileUploadModule,
    MatProgressSpinnerModule,
    SatPopoverModule,
    MatListModule,
    ScrollingModule,
    MatTabsModule,
    MatDialogModule,
    MatSelectModule,
    MatSidenavModule,
    MatProgressBarModule,
    MatCheckboxModule,
    MatCardModule,
    DxSelectBoxModule,
    DxTextAreaModule,
    DxDateBoxModule,
    MatAutocompleteModule,
    DxFormModule,
    DxButtonModule,
    DxToolbarModule,
    DxHtmlEditorModule,
    DxPivotGridModule,
    DxDataGridModule,
    DxPopupModule,
    DxChartModule,
    DxTooltipModule,
    MatMomentDateModule,
    MomentModule,
    DirectiveModule,
    MatTableModule,
    MatTreeModule,
    CrmNotesModule,
    DisplayActivitiesModule,
    PipesModule,
    InternalStakeholderModule,
    NgxMatSelectSearchModule,
    dynamicDatePipe,
    DocumentManagerModule,
    // SharedComponentsModule,
    MatButtonToggleModule,
    MatOptionModule,
    fileformatPipe
    
  ],
  // exports: [SearchUserOpportunityComponent],
  providers: [],
})
export class OpportunitiesDetailModule { }
