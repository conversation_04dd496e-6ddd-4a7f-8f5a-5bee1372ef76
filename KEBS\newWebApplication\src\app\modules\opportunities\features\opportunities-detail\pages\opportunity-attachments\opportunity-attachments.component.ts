import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { OpportunityService } from '../../services/OpportunityService';
import { UtilityService } from '../../../../../../services/utility/utility.service';
import * as _ from 'underscore';
import { ConstantPool } from '@angular/compiler';
import { Console } from 'console';
import { UploadPopupComponent } from 'src/app/modules/shared-lazy-loaded-components/attachment-mgmt/components/upload-popup/upload-popup.component';
import { MatDialog } from '@angular/material/dialog';
import { SharedLazyLoadedComponentsService } from 'src/app/modules/shared-lazy-loaded-components/services/shared-lazy-loaded-components.service';
import { pluck } from 'rxjs/operators';
import { DocViewerComponent } from 'src/app/modules/shared-lazy-loaded-components/attachment-mgmt/components/doc-viewer/doc-viewer.component';
import { MatSnackBar } from '@angular/material/snack-bar';
import { RolesService } from 'src/app/services/acl/roles.service';
import { ToasterService } from 'src/app/services/toaster-service/toaster.service';
import { DocumentManagerService } from '../../../../../shared-lazy-loaded-components/document-manager/service/document-manager.service';

@Component({
  selector: 'app-opportunity-attachments',
  templateUrl: './opportunity-attachments.component.html',
  styleUrls: ['./opportunity-attachments.component.scss'],
})
export class OpportunityAttachmentsComponent implements OnInit {
  fileArray: any = [];

  opportunityId: any;

  urls = {
    uploadUrl: '/api/opportunity/uploadOpportunityAttachment',
    downloadUrl: 'api/opportunity/downloadOpportunityAttachment',
  };

  crmAttachmentConfig: any;
  contextId = '36';
  appId = 36;
  attachmentCount = 0;
  filesUploaded: any = [];
  isLoading = true;

  editAccess: boolean = false;
  documentType: string = 'GENERAL';
  attachmentConfig: any;

  constructor(
    private UtilityService: UtilityService,
    private snackBar: MatSnackBar,
    private route: ActivatedRoute,
    private opportunityService: OpportunityService,
    private dialog: MatDialog,
    private _sharedService: SharedLazyLoadedComponentsService,
    private roleService: RolesService,
    private toasterService: ToasterService,
    private documentService: DocumentManagerService
  ) { }

  async ngOnInit() {

    this.editAccess = await this.checkEditAcess()
    await this.route.parent.params.subscribe(async (res) => {
      this.opportunityId = res['opportunityId'];
      this.contextId = this.contextId + this.opportunityId;
      console.log('contextId', this.contextId);
    });

    await this.opportunityService.getCRMAttachmentsConfigs().then((res) => {
      console.log(res);
      let data = res['data']['aws']['attachment_plugin_config'];
      this.crmAttachmentConfig = data;
      this.attachmentConfig = res
    });

    await this.retrieveUploadedObjects();

    // this.opportunityService.getOpportunityAttachments(this.opportunityId).subscribe(res => {
    //   if(res[0].attachments!=null){
    //     this.fileArray = JSON.parse(res[0].attachments);
    //   }
    //   console.log(this.fileArray);
    //   for(let i=0;i<this.fileArray.length;i++){
    //     let temp1 = this.fileArray[i][0].fileName.split("-/(");
    //     console.log(temp1)
    //     this.fileArray[i][0].name = temp1[0];
    //     this.fileArray[i][0].deleteInProgress = false;
    //     this.fileArray[i][0].size = ((this.fileArray[i][0].size/(1024)).toFixed(2)) + "KB";
    //   }

    // },
    //   err => {
    //     console.error(err);
    //   })
  }

  async retrieveUploadedObjects() {
    this._sharedService
      .retrievecrmUploadedObjects(
        this.crmAttachmentConfig['destination_bucket'],
        this.contextId, this.opportunityId, this.appId
      )
      .pipe(pluck('data'))
      .subscribe(
        (res: any) => {
          // this.attachmentCount = res['length'];
          this.filesUploaded.push(...res);
          this.filesUploaded = this.filesUploaded.filter(file =>
            !('document_id' in file) && !('document_status' in file) && !('document_type' in file)
          );
          this.attachmentCount = this.filesUploaded?.length;



          this.isLoading = false;
        },
        (err) => {
          console.error(err);
          this.isLoading = false;
        }
      );
  };

  viewFile = (file) => {
    let cdn_link = file.cdn_link;
    this._sharedService.getcrmDownloadUrl(cdn_link, this.opportunityId, this.appId).subscribe((res: any) => {

      if (this.attachmentConfig?.data?.viewer_option?.openInNewTabMozillaViewer && file.file_format == 'pdf') {
        window.open(res.mozUrl, '_blank')
        return
      }

      else if (this.attachmentConfig?.data?.viewer_option?.showBase64 && file.file_format == 'pdf') {
        this.toasterService.showInfo('Opening in New tab!', '', 2500);

        if (res.base64Url.startsWith('JVBER')) {
          const base64Data = res.base64Url;
          const byteCharacters = atob(base64Data);
          const byteNumbers = new Array(byteCharacters.length);

          for (let i = 0; i < byteCharacters.length; i++) {
            byteNumbers[i] = byteCharacters.charCodeAt(i);
          }

          const byteArray = new Uint8Array(byteNumbers);
          const pdfFile = new Blob([byteArray], { type: 'application/pdf' });
          const fileURL = URL.createObjectURL(pdfFile);
          window.open(fileURL);
        } else {
          window.open(res.base64Url, '_blank');
        }

        return;
      }

      else if (this.attachmentConfig?.data?.viewer_option?.showBase64iFrame && file.file_format == 'pdf') {
        this.toasterService.showInfo('Opening in New tab!', '', 2500);

        let base64Data = res.base64Url;

        let pdfWindow = window.open('');
        pdfWindow.document.write(`
          <iframe width="100%" height="100%" style="border:none;" src="data:application/pdf;base64,${base64Data}"></iframe>
        `);

        return;
      }

      else if (this.attachmentConfig?.data?.viewer_option?.justIFrame && file.file_format == 'pdf') {
        this.toasterService.showInfo('Opening in New tab!', '', 2500);

        let pdfWindow = window.open('');
        pdfWindow.document.write(`
          <iframe width="100%" height="100%" style="border:none;" src="${res.data}"></iframe>
        `);

        return;
      }

      else if (this.attachmentConfig?.data?.viewer_option?.openInNewTabGoogleViewer && file.file_format == 'pdf') {
        window.open(res.googleOpenInNewTab, '_blank')
        return
      }

      else if ((this.attachmentConfig?.data?.viewer_option?.openInNewTabMozillaViewer && this.isImageFormat(file.file_format)) || this.attachmentConfig?.data?.viewer_option?.showInGoogleViewer || this.attachmentConfig?.data?.viewer_option?.openInNewTabGoogleViewer || file.file_format != 'pdf')
        this.dialog.open(DocViewerComponent, {
          width: '100%',
          height: '90%',
          data: {
            selectedFileUrl: res.data,
            fileFormat: file.file_format,
          },
        });

      else {
        this.downloadFile(file);
        // this.toasterService.showWarning('Cannot show preview for the uploaded file type', '', 2000)
      }
    });
  };

  downloadFile(row) {
    this._sharedService.getDownloadUrl(row.cdn_link).subscribe((res: any) => {
      if (!res['err'])
        window.open(res['data']);
      else {
        this.toasterService.showError("Unable to download file", '', this.documentService.mediumInterval);
      }
    })
  }

  isImageFormat(fileFormat): boolean {
    return ['png', 'jpg', 'jpeg'].includes(fileFormat);
  }


  openSnackBar() {
    if (!this.editAccess) {
      return;
    }
    this.snackBar.open('File deleted Successfully', 'close', {
      duration: 1000,
    });
  }

  deleteFile = (file) => {
    if (!this.editAccess) {
      this.UtilityService.showMessage("Delete is restricted", 'Dismiss');
      return;
    }

    let index = file.index;
    this._sharedService.deletecrmObj(file, 't_app_attachments_meta', this.opportunityId, this.appId).subscribe(
      (res: any) => {
        this.filesUploaded.splice(0);
        this.retrieveUploadedObjects();
      },
      (err) => {
        console.error(err);
      }
    );
  };

  openPopUp() {
    console.log(this.crmAttachmentConfig);
    let data = {
      destinationBucket: this.crmAttachmentConfig['destination_bucket'],
      routingKey: this.crmAttachmentConfig['routing_key'],
      contextId: this.contextId,
      allowEdit: true,
      allowDeleteRename: this.editAccess,
      myFilesDefaultFolder: [],
      documentType: this.documentType
    };
    const uploadBtnDialogRef = this.dialog.open(UploadPopupComponent, {
      width: '100%',
      height: '90%',
      data: { data },
      disableClose: true,
    });
    uploadBtnDialogRef.afterClosed().subscribe((res: any) => {
      this.attachmentCount = res.fileCount;
      this.filesUploaded.splice(0);
      this.retrieveUploadedObjects();
    });
  }

  async checkEditAcess() {
    let adminAccess = _.where(this.roleService.roles, { application_id: 36, role_id: 1 });
    let editAccess = _.where(this.roleService.roles, { application_id: 36, object_id: 6, operation: "*" });
    console.log("accessList", editAccess)
    if (editAccess.length > 0) {
      return true;
    }
    else {
      return false;
    }
  }

  // updateOppAttachment(event){

  //    this.opportunityService.updateOpportunityAttachment(this.opportunityId, event).subscribe(res => {
  //     if(res[0].attachments!=null){
  //       this.fileArray = [];
  //       let temp = JSON.parse(res[0].attachments);
  //       for(let i=0;i<temp.length;i++){
  //         temp[i][0].deleteInProgress = false;
  //         let temp1 = temp[i][0].fileName.split("-/(");
  //         temp[i][0].name = temp1[0];
  //         temp[i][0].size = ((temp[i][0].size/(1024)).toFixed(2)) + "KB";
  //         this.fileArray.push(temp[i]);
  //       }
  //       this.UtilityService.showMessage(
  //         "Uploaded Successfully",
  //         "Dismiss",
  //         3000
  //       );
  //     }
  //   },
  //     err => {
  //       console.error(err);
  //     });

  // }

  // deleteOppAttachment(event){

  //   this.opportunityService.deleteOpportunityAttachment(this.opportunityId,event).subscribe(res => {
  //     if(res[0].attachments!=null){
  //       this.fileArray = [];
  //       let temp = JSON.parse(res[0].attachments);
  //       for(let i=0;i<temp.length;i++){
  //         temp[i][0].deleteInProgress = false;
  //         let temp1 = temp[i][0].fileName.split("-/(");
  //         temp[i][0].name = temp1[0];
  //         temp[i][0].size = ((temp[i][0].size/(1024)).toFixed(2)) + "KB";
  //         this.fileArray.push(temp[i]);
  //       }
  //     }
  //     else{
  //       this.fileArray = [];
  //     }
  //     this.UtilityService.showMessage(
  //       "Deleted Successfully",
  //       "Dismiss",
  //       3000
  //     );
  //   },
  //     err => {
  //       console.error(err);
  //     });
  // }
}
