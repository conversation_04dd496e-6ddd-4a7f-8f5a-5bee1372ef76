<div class="label my-2 ml-2">Monitor any changes made to your opportunity</div>
<ng-container>
  <div class="container-fluid">
    <form [formGroup]="filterFields">
      <div class="row">
        <div class="col-2 mr-4">
          <!-- <div class="row sectionLabel">
            Name
          </div> -->
          <div class="row fieldlabelwidth fieldstyle">
            <app-search-user-e360 [label]="'Select User'" [isAutocomplete]="true"
            formControlName="name"></app-search-user-e360>
          </div>
        </div>
        <div class="col-2">
          <!-- <div class="row sectionLabel">
            Action
          </div> -->
          <div class="row">
            <app-input-search
            [list]="actionList"
            style="width: 100%"
            formControlName="action"
           
            placeholder="Select Action"
           
            >
            </app-input-search>
          </div>
        </div>
        <div class="col-2">
          <!-- <div class="row sectionLabel">
            Duration
          </div> -->
          <div class="row">
            <app-input-search
            [list]="durationList"
            style="width: 100%"
            formControlName="duration"
           
            placeholder="Select Duration"
            
            >
            </app-input-search>
          </div>
        </div>
        <div class="col-2">
          <button mat-icon-button (click)="clearFilter()" matTooltip="Reset Filters">
            <mat-icon>
              restart_alt
            </mat-icon>
          </button>
        </div>
      </div>
      </form>
  </div>

</ng-container>

<section class="user-information">
<header class="col-12 user-header">
    <h2 class="col-2 user-title">User</h2>
    <h2 class="col-1 user-title mr-1">Type</h2>
    <h2 class="col-2 user-title mr-1">Email ID</h2>
    <h2 class="col-1 user-title mr-1">Action</h2>
    <h2 class="col-3 user-title mr-1">Description</h2>
    <h2 class="col-2 user-title ">Timestamp</h2>
  </header>
  
</section>
<span *ngIf="isLoading" class="ml-5 mt-5 mb-3 d-flex justify-content-center">
    <mat-spinner *ngIf="!generalConfig?.loaderConfig" [diameter]="25" class="text-center"></mat-spinner>
    <img *ngIf="generalConfig.loaderConfig"
    [src]="generalConfig.loaderConfig?.loaderUrl || 'https://assets.kebs.app/KEBS_MAIN_GIF.gif'"
    width="55" height="55"
    [matTooltip]="generalConfig.loaderConfig?.toolTipContent || 'Loading...'" alt="Loading..." />
</span>

<div *ngIf="(fieldListRes== null || fieldListRes.length==0) && !isLoading" class="d-flex justify-content-center ml-5 mt-5 mb-3">
    <div class="text-center fieldwidth">No Log found</div>
  </div>
<div class="card-container" infinite-scroll [infiniteScrollDistance]="2" (scrolled)="itemDataScrollDown()"
[scrollWindow]="false" cdkScrollable>
  
    <div *ngFor="let items of fieldListRes; let i = index">

        <div class="col-12 rowstyle">
          <div class="col-2 fieldNamewidth keyval mr-3">
            <app-user-profile type="small" [oid]="items?.oid"
            imgHeight="28px" imgWidth="28px">
            </app-user-profile>
          </div>
          <div class="col-1 fieldNamewidth keyval mr-4">
            {{ items.record_type }}
          </div>
          <div class="col-2 fieldNamewidth keyval mr-4" [matTooltip]="items?.actor ? items.actor : '-'">
            {{ items.actor }}
          </div>
          <div class="col-1 fieldNamewidth keyval mr-4"  [ngClass]="items?.action">
            {{ items.action }}
          </div>
          <div class="col-3 fieldNamewidth keyval mr-3"  [matTooltip]="items?.itemDescription ? items.itemDescription : items.description">
            {{ ( items.itemDescription != null && items.itemDescription != "") ?  items.itemDescription : items.description }}
          </div>
          <div class="col-3 fieldNamewidth keyval">
            {{ items.timestamp  | date: 'dd-MMM-yyyy HH:mm' }}
          </div>
        </div>
      </div>
</div>
