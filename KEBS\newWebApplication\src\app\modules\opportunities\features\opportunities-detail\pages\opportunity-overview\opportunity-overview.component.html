<mat-card class="mx-3 timeline" #timelineView style="height:42px;display:flex;align-items:center;">
    <div class='container-fluid row p-0' style="flex-wrap:nowrap;">
        <mat-icon style="color:#9A9A9A;align-self:center;cursor:pointer" (click)="scrollLeft()">keyboard_double_arrow_left</mat-icon>
        <div class='row w-100'  (scroll)="onScroll($event)"  #timelineContainer  style="display:flex;flex-wrap:nowrap;overflow-x:auto;overflow-y:hidden;scroll-behavior: smooth;padding:12px;">
            <ng-container *ngIf="statusLogObservable | async as statusLog">
                <div *ngFor="let log of statusLog;let i = index" style="align-items:center;display:flex;cursor:pointer" (click)="!log.current_status?editStatusFromTimeline(log):''" class="timeline-item">
                    <mat-icon style='font-size:medium' class="d-flex align-items-center" [style.color]="log.color">{{log.icon}}</mat-icon>
                    <span>
                        <p class="m-0" style='font-weight: 700; font-size:11px;white-space:nowrap' [style.color]="log.color">
                            {{log.status_name}}</p>
                        <p class="m-0" style="font-size:11px;color:#B9C0CA;font-weight:400;white-space:nowrap;">{{log.changed_on | date: 'dd-MMM-yyyy'}}</p>
                    </span>
                    <div *ngIf="i+1<statusLog.length" style="
                            font-size: xx-large;
                            color:#8B95A5;
                            margin-inline: 16px;">
                        &#8594;
                    </div>
                </div>
            </ng-container>
        </div>
        <mat-icon style="color:#9A9A9A;align-self:center;cursor:pointer" (click)="scrollRight()">keyboard_double_arrow_right</mat-icon>
    </div>
</mat-card>
<div class="container-fluid" class="opportunity-overview-styles">
    <div class="row mt-3 pl-2 pb-1">
        <div class="col-5">
            <span class="tileName">
                Opportunity Info
            </span>
            
        </div>
        <div class="col-7 d-flex">
          
            <button *ngIf="editAccess && oppEditAccess" mat-icon-button matTooltip="View more" class="more-button ml-auto" [matMenuTriggerFor]="options">
                <mat-icon class="smallCardIcon">more_vert</mat-icon>
            </button>
            <mat-menu #options="matMenu">
                <button mat-menu-item class="drop-btn" (click)="editOpportunity('GENERAL_EDIT')" *ngIf="editAccess && oppEditAccess && overviewDetails?.isOpen">
                    <mat-icon class="smallCardIcon">edit</mat-icon> <span>Edit Opportunity</span>
                </button>
                <button mat-menu-item class="drop-btn" (click)="reOpenOpportunity()" *ngIf="reOpenAccess && isDisableEdit && !overviewDetails?.isOpen">
                    <mat-icon class="smallCardIcon">lock_open</mat-icon> <span>Reopen Opportunity</span>
                </button>
                <button mat-menu-item class="drop-btn" (click)="deactivateOpportunity()" *ngIf="deactivateAccess && this.tempOverviewDetails?.sales_status_id != 13 && this.tempOverviewDetails?.sales_status_id != 17">
                    <mat-icon class="smallCardIcon">delete</mat-icon> <span>Deactivate Opportunity</span>
                </button>
                <button *ngIf="line_of_business_access" mat-menu-item class="drop-btn" (click)="viewLineofBusiness()">
                    <mat-icon class="smallCardIcon">business_center</mat-icon> <span>View heirarchy</span>
                </button>
                <button *ngIf="child_opportunity_creation_access && ( (opportunityProjectDetails.length>0) ?opportunityProjectDetails[0]?.is_child_opp_creation_allowed:false)" mat-menu-item class="drop-btn" (click)="editOpportunity('CHILD_CREATION')">
                    <mat-icon class="smallCardIcon">account_tree</mat-icon> <span>Create Child Opportunity</span>
                </button>
                <button *ngIf="('atRisk'| tenantOpportunity : formFieldData : 'isActive') && ('riskFlagChangeWizard'| tenantOpportunity : formFieldData : 'isActive') && flagChangeWizardAccess" mat-menu-item class="drop-btn" (click)="changeFlag()">
                    <mat-icon class="smallCardIcon">flag</mat-icon>
                     <span> {{ 'riskFlagChangeWizard' | tenantOpportunity : formFieldData : 'label' : 'Change Flag' }}</span>
                </button>
            </mat-menu>
        </div> 
    </div>
    
    <div class="row">
        <div class="col-5 pr-0">
            <div class="row">
                <div class="col-12 pl-2">
                    <ng-container *ngIf="overviewDetails">
                        <div class="card" [ngClass]="[
          'salesStatusBorder_' + this.overviewDetails.sales_status_id
            ]">
                            <div class="card-body pt-3 pl-2 pr-2 pb-2">
                                <div class="row">
                                    <div class="col-12">
                                        <div class="card opportunity-overview-card-bg slide-in-top">
                                            <div class="card-body p-2">
                                                <div class="row">
                                                    <div class="col-12 pl-4">
                                                        <div class="row">
                                                            <div class="col-10 p-0 opportunity-name" style="cursor: pointer;" [ngStyle]="{'pointer-events' : isDisableEdit || !oppEditAccess ? 'none':( 'opportunityName' | tenantOpportunity : formFieldData : 'isDisabled'  ) ? 'none'  : ''}" interActiveInlineEdit  
                                                            (click)="activateInlineEdit('Opportunity name', 'simple-text', [],overviewDetails)"
                                                            [matTooltip]="this.overviewDetails?.opportunity_name?this.overviewDetails?.opportunity_name:'-'">
                                                            {{this.tempOverviewDetails?.opportunity_name?this.tempOverviewDetails?.opportunity_name:'-'}}
                                                        </div>
                                                        <div *ngIf="('atRisk' | tenantOpportunity : formFieldData : 'isActive') && tempOverviewDetails?.at_risk == 1" class="if-at-risk col-2">
                                                            {{ 'atRisk' | tenantOpportunity : formFieldData : 'label' : 'Opportunity ID' }} 
                                                        </div>
                                                        <div *ngIf="('atRisk' | tenantOpportunity : formFieldData : 'isActive') && tempOverviewDetails?.at_risk == 0" class="if-not-at-risk col-2">
                                                            Secured
                                                        </div>
                                                        </div>
                                                        
                                                        <div class="row pl-0 pt-1" *ngIf="'opportunityID'| tenantOpportunity : formFieldData : 'isActive'">
                                                            <div class="col-6 opportunity-info-title pl-0">
                                                                {{ 'opportunityID' | tenantOpportunity : formFieldData : 'label' : 'Opportunity ID' }} 
                                                            </div>
                                                            <div class="col-6 pl-0 opportunity-info-details"
                                                                style="cursor: pointer;"
                                                                [matTooltip]="overviewDetails?.opportunity_id ? overviewDetails?.opportunity_id : '-'">
                                                                {{this.overviewDetails?.opportunity_id ? this.overviewDetails?.opportunity_id : '-'}}
                                                            </div>
                                                        </div>
                                                        <div class="row pl-0 pt-1" *ngIf="'opportunityCode'| tenantOpportunity : formFieldData : 'isActive'">
                                                            <div class="col-6 opportunity-info-title pl-0">
                                                                {{ 'opportunityCode' | tenantOpportunity : formFieldData : 'label'}}
                                                            </div>
                                                            <div class="col-6 pl-0 opportunity-info-details"
                                                                style="cursor: pointer;"
                                                                [matTooltip]="overviewDetails?.opportunity_code ? overviewDetails?.opportunity_code : '-'">
                                                                {{this.overviewDetails?.opportunity_code ? this.overviewDetails?.opportunity_code : '-'}}
                                                            </div>
                                                        </div>
                                                        <div class="row pl-0 pt-1" *ngIf="('opportunityValueMillion'
                                                        | tenantOpportunity : formFieldData : 'isActive') && checkFieldShouldbeRestricted('totalRevenue')">
                                                            <div class="col-6 opportunity-info-title pl-0">
                                                                {{ 'opportunityValueMillion' | tenantOpportunity : formFieldData : 'label'}}
                                                            </div>
                                                            <div class="col-6 pl-0 opportunity-info-details d-flex align-items-center justify-content-between">
                                                                <div style="cursor: pointer;" interActiveInlineEdit
                                                                class="mb-1"
                                                                    (click)="activateInlineEdit('Opportunity Value', 'simple-text', [],overviewDetails)"
                                                                    [ngStyle]="{'pointer-events' : isDisableEdit || !oppEditAccess ? 'none':( 'opportunityValueMillion' | tenantOpportunity : formFieldData : 'isDisabled'  ) ? 'none'  : ''}">
                                                                    <app-currency-inline [currencyList]="this.overviewDetails?this.overviewDetails.opportunity_value:''" [defaultCurrencyCode]="this.overviewDetails?.currency_code || 'USD'"
                                                                        class="flex-1" type="small">
                                                                    </app-currency-inline>
                                                                </div>
                                                                <!-- {{this.overviewDetails?.value_in_millions_USD?this.overviewDetails?.value_in_millions_USD:'-'}}Mn -->
                                                                <div class="d-flex align-items-center justify-content-between">
                                                                    <span *ngIf="quoteHasChangeRequest" tooltip={{dynoTooltipContent}} matTooltipClass="multi-line-tooltip-opp-overview"
                                                                        style="cursor: pointer;">
                                                                        <mat-icon class="smallCardIcon">info</mat-icon>
                                                                    </span>
                                                                    <span *ngIf="isQuoteEnabled && ('opportunityValueMillion' | tenantOpportunity:formFieldData:'is_audit') && ('opportunityValueMillion' | tenantOpportunity:formFieldData:'showHistoryButton')" 
                                                                        [matTooltip]="('opportunityValueMillion' | tenantOpportunity: formFieldData : 'label') + ' From Quote'+' history'"
                                                                        style="cursor: pointer;" (click)=" $event.stopPropagation();openOpportunityValuelog()">
                                                                        <mat-icon class="smallCardIcon">history</mat-icon>
                                                                    </span>
                                                                    <span [matTooltip]="('opportunityValueMillion' | tenantOpportunity: formFieldData : 'label') + ' history'"
                                                                        *ngIf="('opportunityValueMillion' | tenantOpportunity:formFieldData:'is_audit') && ('opportunityValueMillion' | tenantOpportunity:formFieldData:'showHistoryButton') && !isQuoteEnabled"
                                                                        style="cursor: pointer;"
                                                                        (click)="$event.stopPropagation();openOpportunityAuditlog('opportunityValueMillion')">
                                                                        <mat-icon class="smallCardIcon">history</mat-icon>
                                                                    </span>
                                                                    <span *ngIf="isQuoteEnabled && valueSyncButtonVisible" matTooltip="Sync with Quote Value" style="cursor: pointer;" (click)="updateOppValue()">
                                                                        <mat-icon class="smallCardIcon">sync</mat-icon>
                                                                    </span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="row pl-0 pt-1 slide-from-down" *ngIf="'estOpportunityValue'| tenantOpportunity : formFieldData : 'isActive'">
                                                            <div class="col-6 pl-0 opportunity-info-title">
                                                                {{ 'estOpportunityValue' | tenantOpportunity : formFieldData : 'label'}}
                                                            </div>
                                                            <div class="col-5 pl-0 opportunity-info-details"
                                                                [matTooltip]="overviewDetails?.est_opportunity_value ? overviewDetails?.est_opportunity_value : '-'">
                                                                {{this.overviewDetails?.est_opportunity_value ? this.overviewDetails?.est_opportunity_value :'-'}}
                                                            </div>
                                                        </div>
                                                        <!-- <div class="row pl-0 pt-1">
                              <div class="col-6 opportunity-info-title pl-0">
                                Opportunity value (INR)
                              </div>
                              <div class="col-6 opportunity-info-details">
                                {{this.overviewDetails?.expected_value_in_lakhs?this.overviewDetails?.expected_value_in_lakhs:'-'}}L
                              </div>
                            </div> -->
                                                        <div class="row pl-0 pt-1" *ngIf="'inAccount'| tenantOpportunity : formFieldData : 'isActive'">
                                                            <div class="col-6 opportunity-info-title pl-0">
                                                                {{ 'inAccount' | tenantOpportunity : formFieldData : 'label'}}
                                                            </div>
                                                            <div class="col-6 pl-0 opportunity-info-details"
                                                                style="font-weight: 500;cursor: pointer; color:#cf0001; "
                                                                [matTooltip]="this.overviewDetails?.customer_name?this.overviewDetails?.customer_name:'-'"
                                                                (click)="navigateToAccounts()">
                                                                {{this.overviewDetails?.customer_name?this.overviewDetails?.customer_name:'-'}}
                                                            </div>
                                                        </div>
                                                        <div class="row pl-0 pt-1" *ngIf="'paymentTerms'| tenantOpportunity : formFieldData : 'isActive'">
                                                            <div class="col-6 opportunity-info-title pl-0">
                                                                {{ 'paymentTerms' | tenantOpportunity : formFieldData : 'label'}}
                                                            </div>
                                                            <div class="col-6 pl-0 opportunity-info-details"
                                                                style="font-weight: 500; color:#cf0001; "
                                                                [matTooltip]="this.overviewDetails?.payment_terms?this.overviewDetails?.payment_terms:'-'" >
                                                                {{this.overviewDetails?.payment_terms?this.overviewDetails?.payment_terms:'-'}}
                                                            </div>
                                                        </div>
                                                        <div class="row pl-0 pt-1" *ngIf="'partyAccounts'| tenantOpportunity : formFieldData : 'isActive'">
                                                            <div class="col-6 opportunity-info-title pl-0">
                                                                {{ 'partyAccounts' | tenantOpportunity : formFieldData : 'label'}}
                                                            </div>
                                                            <div class="col-6 pl-0 opportunity-info-details"
                                                                style="font-weight: 500;cursor: pointer; color:#cf0001; "
                                                                [matTooltip]="this.overviewDetails?.party_account_name?this.overviewDetails?.party_account_name:'-'"
                                                                (click)="navigateToPartyAccounts()">
                                                                {{this.overviewDetails?.party_account_name?this.overviewDetails?.party_account_name:'-'}}
                                                            </div>
                                                        </div>
                                                        <div class="row pl-0 pt-1" *ngIf="'issuePO'| tenantOpportunity : formFieldData : 'isActive'">
                                                            <div class="col-6 opportunity-info-title pl-0">
                                                                {{ 'issuePO' | tenantOpportunity : formFieldData : 'label'}}
                                                            </div>
                                                            <div class="col-6 pl-0 opportunity-info-details" style="font-weight: 500; color:#cf0001; "
                                                                [matTooltip]="this.overviewDetails?.issue_po?this.overviewDetails?.issue_po:'-'">
                                                                {{this.overviewDetails?.issue_po?this.overviewDetails?.issue_po:'-'}}
                                                            </div>
                                                        </div>
                                                        <div class="row pl-0 pt-1" *ngIf="'legalEntity'| tenantOpportunity : formFieldData : 'isActive'">
                                                            <div class="col-6 opportunity-info-title pl-0">
                                                                {{ 'legalEntity' | tenantOpportunity : formFieldData : 'label'}}
                                                            </div>
                                                            <div class="col-6 pl-0 opportunity-info-details "
                                                                [matTooltip]="this.tempOverviewDetails?.legal_entity_name?this.tempOverviewDetails?.legal_entity_name:'-'">
                                                                {{this.tempOverviewDetails?.legal_entity_name?this.tempOverviewDetails?.legal_entity_name:'-'}}
                                                            </div>
                                                        </div>
                                                        <div class="row pl-0 pt-1" *ngIf="'accountCode'| tenantOpportunity : formFieldData : 'isActive'">
                                                            <div class="col-6 opportunity-info-title pl-0">
                                                                {{'accountCode'| tenantOpportunity : formFieldData : 'label'}}
                                                            </div>
                                                            <div class="col-6 pl-0 opportunity-info-details"
                                                                style="cursor: pointer;"
                                                                [matTooltip]="overviewDetails?.customer_code ? overviewDetails?.customer_code : '-'">
                                                                {{this.overviewDetails?.customer_code ? this.overviewDetails?.customer_code : '-'}}
                                                            </div>
                                                        </div>
                                                        <div class="row pl-0 pt-1" *ngIf="'serviceType'| tenantOpportunity : formFieldData : 'isActive'">
                                                            <div class="col-6 opportunity-info-title pl-0">
                                                                {{ 'serviceType' | tenantOpportunity : formFieldData : 'label'}}
                                                            </div>
                                                            <div class="col-6 pl-0 opportunity-info-details " style="cursor: pointer;"  
                                                                [ngStyle]="{'pointer-events' : (isDisableEdit || isActiveQuoteExists)  || !oppEditAccess ? 'none':( 'serviceType' | tenantOpportunity : formFieldData : 'isDisabled'  ) ? 'none' :  ''  }"
                                                                interActiveInlineEdit  (click)="activateInlineEdit('Servicetype', 'minimal-dropdown',serviceTypeMaster,'')"
                                                                [matTooltip]="this.tempOverviewDetails?.service_type_name?this.tempOverviewDetails?.service_type_name:'-'">
                                                                {{this.tempOverviewDetails?.service_type_name?this.tempOverviewDetails?.service_type_name:'-'}}
                                                            </div>
                                                        </div>
                                                        <div class="row pl-0 pt-1" *ngIf="'opportunityType'| tenantOpportunity : formFieldData : 'isActive'">
                                                            <div class="col-6 opportunity-info-title pl-0">
                                                                {{ 'opportunityType' | tenantOpportunity : formFieldData : 'label'}}
                                                            </div>
                                                            <div class="col-6 pl-0 opportunity-info-details " style="cursor: pointer;" 
                                                                [matTooltip]="this.tempOverviewDetails?.opportunity_type?this.tempOverviewDetails?.opportunity_type:'-'">
                                                                {{this.tempOverviewDetails?.opportunity_type?this.tempOverviewDetails?.opportunity_type:'-'}}
                                                            </div>
                                                        </div>
                                                        <div class="row pl-0 pt-1" *ngIf="'department'| tenantOpportunity : formFieldData : 'isActive'">
                                                            <div class="col-6 opportunity-info-title pl-0">
                                                                {{ 'department' | tenantOpportunity : formFieldData : 'label'}}
                                                            </div>
                                                            <div class="col-6 pl-0 opportunity-info-details " style="cursor: pointer;" 
                                                                [matTooltip]="this.tempOverviewDetails?.department?this.tempOverviewDetails?.department:'-'">
                                                                {{this.tempOverviewDetails?.department?this.tempOverviewDetails?.department:'-'}}
                                                            </div>
                                                        </div>
                                                        <div class="row pl-0 pt-1" *ngIf="'description'| tenantOpportunity : formFieldData : 'isActive'">
                                                            <div class="col-6 opportunity-info-title pl-0">
                                                                {{ 'description' | tenantOpportunity : formFieldData : 'label'}}
                                                            </div>
                                                            <div class="col-6 pl-0 opportunity-info-details " style="cursor: pointer;" 
                                                                [matTooltip]="this.tempOverviewDetails?.description?this.tempOverviewDetails?.description:'-'">
                                                                {{this.tempOverviewDetails?.description?this.tempOverviewDetails?.description:'-'}}
                                                            </div>
                                                        </div>
                                                        <div class="row pl-0 pt-1" *ngIf="'externalReferenceId'| tenantOpportunity : formFieldData : 'isActive'">
                                                            <div class="col-6 opportunity-info-title pl-0">
                                                                {{ 'externalReferenceId' | tenantOpportunity : formFieldData : 'label'}}
                                                            </div>
                                                            <div class="col-6 pl-0 opportunity-info-details " style="cursor: pointer;"
                                                                [matTooltip]="this.tempOverviewDetails?.external_reference_id||'-'">
                                                                {{this.tempOverviewDetails?.external_reference_id||'-'}}
                                                            </div>
                                                        </div>
                                                        <div class="row pl-0 pt-1" *ngIf="'bidType' | tenantOpportunity : formFieldData : 'isActive'">
                                                            <div class="col-6 opportunity-info-title pl-0">
                                                                {{ 'bidType' | tenantOpportunity : formFieldData : 'label'}}
                                                            </div>
                                                            <div class="col-6 pl-0 opportunity-info-details" style="cursor: pointer;"  
                                                                [ngStyle]="{'pointer-events' : isDisableEdit || !oppEditAccess ? 'none' :( 'bidType' | tenantOpportunity : formFieldData : 'isDisabled'  ) ? 'none': ''}"
                                                                interActiveInlineEdit  (click)="activateInlineEdit('Bidtype', 'minimal-dropdown',bidTypeMaster,'')">
                                                                {{this.tempOverviewDetails?.bid_type?this.tempOverviewDetails?.bid_type:'-'}}
                                                            </div>
                                                        </div>
                                                        <div class="row pl-0 pt-1" *ngIf="'requirementType' | tenantOpportunity : formFieldData : 'isActive'">
                                                            <div class="col-6 opportunity-info-title pl-0">
                                                                {{ 'requirementType' | tenantOpportunity : formFieldData : 'label'}}
                                                            </div>
                                                            <div class="col-6 pl-0 opportunity-info-details" style="cursor: pointer;"  
                                                                [ngStyle]="{'pointer-events' : isDisableEdit || !oppEditAccess ? 'none' :( 'requirementType' | tenantOpportunity : formFieldData : 'isDisabled'  ) ? 'none': ''}">
                                                                {{this.tempOverviewDetails?.requirement_type?this.tempOverviewDetails?.requirement_type:'-'}}
                                                            </div>
                                                        </div>
                                                        <div class="row pl-0 pt-1" *ngIf="'businessType' | tenantOpportunity : formFieldData : 'isActive'">
                                                            <div class="col-6 opportunity-info-title pl-0">
                                                                {{ 'businessType' | tenantOpportunity : formFieldData : 'label'}}
                                                            </div>
                                                            <div class="col-6 pl-0 opportunity-info-details" style="cursor: pointer;"  
                                                                [ngStyle]="{'pointer-events' : isDisableEdit || !oppEditAccess ? 'none' :( 'businessType' | tenantOpportunity : formFieldData : 'isDisabled'  ) ? 'none': ''}">
                                                                {{this.tempOverviewDetails?.business_name?this.tempOverviewDetails?.business_name:'-'}}
                                                            </div>
                                                        </div>
                                                        <div class="row pl-0 pt-1" *ngIf="'opportunityDetailedType' | tenantOpportunity : formFieldData : 'isActive'">
                                                            <div class="col-6 opportunity-info-title pl-0">
                                                                {{ 'opportunityDetailedType' | tenantOpportunity : formFieldData : 'label'}}
                                                            </div>
                                                            <div class="col-6 pl-0 opportunity-info-details" style="cursor: pointer;"  
                                                                [ngStyle]="{'pointer-events' : isDisableEdit || !oppEditAccess ? 'none' :( 'opportunityDetailedType' | tenantOpportunity : formFieldData : 'isDisabled'  ) ? 'none': ''}">
                                                                {{this.tempOverviewDetails?.opportunity_detail_type?this.tempOverviewDetails?.opportunity_detail_type:'-'}}
                                                            </div>
                                                        </div>
                                                        <div class="row pl-0 pt-1" *ngIf="'source' | tenantOpportunity : formFieldData : 'isActive'">
                                                            <div class="col-6 opportunity-info-title pl-0">
                                                                {{ 'source' | tenantOpportunity : formFieldData : 'label'}}
                                                            </div>
                                                            <div class="col-6 pl-0 opportunity-info-details"  style="cursor: pointer;"  
                                                                [ngStyle]="{'pointer-events' : isDisableEdit || !oppEditAccess ? 'none':( 'source' | tenantOpportunity : formFieldData : 'isDisabled'  ) ? 'none' : ''}"
                                                                interActiveInlineEdit  (click)="activateInlineEdit('Source', 'minimal-dropdown',sourceMaster,'')">
                                                                {{this.tempOverviewDetails?.source_name?this.tempOverviewDetails?.source_name:'-'}}
                                                            </div>
                                                        </div>
                                                        <div class="row pl-0 pt-1" *ngIf="'customerContact' | tenantOpportunity : formFieldData : 'isActive'">
                                                            <div class="col-6 opportunity-info-title pl-0">
                                                                {{ 'customerContact' | tenantOpportunity : formFieldData : 'label'}}
                                                            </div>
                                                            <div class="col-6 pl-0 opportunity-info-details" (click)="navigateToContacts()"  style="cursor: pointer;"  
                                                                [ngStyle]="{'pointer-events' : isDisableEdit || !oppEditAccess ? 'none':( 'source' | tenantOpportunity : formFieldData : 'isDisabled'  ) ? 'none' : ''}"
                                                                >
                                                                {{this.tempOverviewDetails?.account_contact_name || '-'}}
                                                            </div>
                                                        </div>
                                                        <!-- <div class="row pl-0 pt-1" *ngIf="isAdditionalField">
                                                            <div class="col-6 opportunity-info-title pl-0">
                                                                Pipeline
                                                            </div>
                                                            <div class="col-6 pl-0 opportunity-info-details">
                                                                {{this.overviewDetails?.funnel_pipe_status?this.overviewDetails?.funnel_pipe_status:'-'}}
                                                            </div>
                                                        </div> -->
                                                        <div class="pl-0 pt-2 d-flex flex-row-reverse justify-content-start">
                                                            <button mat-mini-fab *ngIf="mailAccessInOpportunity"
                                                            style="background-color: #cf0001;color: #FAFAFA; margin-right: 60px;"
                                                            (click)="openMail()"
                                                            [ngStyle]="{transform: 'scale(0.8)'}"
                                                            matTooltip="Open mail">
                                                              <mat-icon>email</mat-icon>
                                                              </button>
                                                              <button mat-mini-fab mat-button (click)="createNewTask()"
                                                              style="background-color: #cf0001;color: #FAFAFA;"
                                                              [ngStyle]="{transform: 'scale(0.8)'}"
                                                              matTooltip="Create Task" matRipple>
                                                              <mat-icon>assignment</mat-icon>
                                                          </button>
                                                            <!-- Teams Meeting Scheduling button. Feature need to be fixed. -->
                                                            <!-- <button mat-mini-fab 
                                                            style="background-color: #cf0001;color: #FAFAFA;"
                                                            (click)="openMeetingModal()"
                                                            matTooltip="Schedule meeting"
                                                            [ngStyle]="{transform: 'scale(0.8)'}">
                                                              <mat-icon>event_available</mat-icon>
                                                            </button> -->
                                                            <button mat-mini-fab 
                                                            style="background-color: #cf0001;color: #FAFAFA;"
                                                            matTooltip="Add Note"
                                                            (click)="addNotes()"
                                                            [ngStyle]="{transform: 'scale(0.8)'}">
                                                              <mat-icon>note_add</mat-icon>
                                                              </button>
                                                              <button mat-mini-fab mat-button [matMenuTriggerFor]="menu"
                                                            style="background-color: #cf0001;color: #FAFAFA;"
                                                            matTooltip="Add Logs" matRipple
                                                            [ngStyle]="{transform: 'scale(0.8)'}">
                                                            <mat-icon>add_circle</mat-icon>
                                                            </button>
                                                            <mat-menu #menu="matMenu">
                                                            <button mat-menu-item (click)="createCallLog()">Log a call</button>
                                                            <button mat-menu-item (click)="createMail()">Log an email</button>
                                                            <button mat-menu-item (click)="createMeeting()">Log a meeting</button>
                                                            <button mat-menu-item (click)="createDemo()" *ngIf="isDemoConfig">Log a demo</button>
                                                          </mat-menu>
                                                          </div>
                                                       
                                                            
                                                      
                                                    
                                                       
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row pt-3" *ngIf="('opportunityFormsSection'| tenantOpportunity : formFieldData : 'isActive')" >
                                    <div class="col-11 opportunity-sub-headings">
                                        {{ 'opportunityFormsSection' | tenantOpportunity : formFieldData : 'label' ||  "Forms"}}
                                    </div>
                                    <div class="col-1 opportunity-sub-headings">
                                        <span matTooltip="Add Form" style="cursor: pointer;" class="mt-3 icon-tray-visible" satPopoverAnchor #anchor="satPopoverAnchor" (click)="anchor.popover.toggle()">
                                            <mat-icon class="smallCardIcon" >add</mat-icon>
                                        </span>
                                        <sat-popover [anchor]="anchor">
                                            <div class="info-wrapper"
                                              style=" padding: 18px;
                                            border-radius: 10px !important; 
                                            position: absolute !important;
                                            background-color: white !important;
                                            z-index: 1 !important;
                                            transition: box-shadow 200ms cubic-bezier(0, 0, 0.2, 1) !important;
                                            box-shadow: 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12) !important;">
                                      
                                            <mat-form-field appearance="outline">
                                                <mat-label>Form Name</mat-label>
                                                <input matInput  placeholder="Eg: Internal Form" [formControl]='user_form_name'
                                                required />
                                                
                                            </mat-form-field>

                                            <mat-form-field appearance="outline">
                                                <mat-label>Form Id</mat-label>
                                                <input matInput  placeholder="Eg: 45df224gfadf" [formControl]='user_form_id'
                                                  required />
                                                
                                            </mat-form-field>


                              
                                              <div class="row">
                                                <button mat-raised-button color="warn" 
                                                  (click)="insertUserForm(); anchor.popover.toggle()">
                                                  Submit
                                                </button>
                                              </div>
                                            </div>
                                          </sat-popover>
                                    </div>
                                </div>
                                <div class="row" *ngIf="('opportunityFormsSection'| tenantOpportunity : formFieldData : 'isActive')">
                                    <div class="col-12">
                                        <div *ngFor="let form of opportunity_form">
                                            <div *ngIf="form.is_displayed_overview">
                                                <div class="row pl-3 pt-2 slide-from-down"  *ngIf="form.form_name=='Bid Percentage'">
                                                    <div class="col-1">
                                                        <span>
                                                            <mat-icon class="opportunity-info-icons">
                                                                {{form.mat_icon}}
                                                            </mat-icon>
                                                        </span>
                                                    </div>
                                                    <div class="col-6 opportunity-info-title">
                                                        {{form.form_name}}
                                                    </div>
                                                    <div class="col-5 opportunity-info-details" style="cursor: pointer;" 
                                                        [ngClass]="(this.overviewDetails.is_bid==1)? 'bid':'noBid'"
                                                        (click)="openForm()">
                                                        {{this.overviewDetails?.bid_perc?(this.overviewDetails?.bid_perc | number : '1.2-6'):'-'}} % 
                                                        {{this.bidStatus}}
                                                    </div>
                                                </div>

                                                <div class="row pl-3 pt-2 slide-from-down"  *ngIf="form.form_name=='Internal Feedback Score'">
                                                    <div class="col-1">
                                                        <span>
                                                            <mat-icon class="opportunity-info-icons">
                                                                {{form.mat_icon}}
                                                            </mat-icon>
                                                        </span>
                                                    </div>
                                                    <div class="col-6 opportunity-info-title">
                                                        {{form.form_name}}
                                                    </div>
                                                    <div class="col-5 opportunity-info-details" style="cursor: pointer;" 
                                                        (click)="openReviewForm()">
                                                        {{this.overviewDetails?.presales_internal_score?(this.overviewDetails?.presales_internal_score | number : '1.2-6'):'-'}} %
                                                    </div>
                                                </div>

                                                <div class="row pl-3 pt-2 slide-from-down"  *ngIf="form.form_name=='Win-Loss Form'">
                                                    <div class="col-1">
                                                        <span>
                                                            <mat-icon class="opportunity-info-icons">
                                                                {{form.mat_icon}}
                                                            </mat-icon>
                                                        </span>
                                                    </div>
                                                    <div class="col-6 opportunity-info-title">
                                                        {{form.form_name}}
                                                    </div>
                                                    <div class="col-5 opportunity-info-details" style="cursor: pointer;" 
                                                        (click)="openWinLossForm(form.form_id)">
                                                        {{form.form_value}} 
                                                    </div>
                                                </div>
                                                

                                                <div class="row pl-3 pt-2 slide-from-down deleteFormButton" *ngIf="form.form_name!='Bid Percentage' && form.form_name!='Internal Feedback Score' && form.form_name!='Win-Loss Form'">
                                                    <div class="col-1">
                                                        <span>
                                                            <mat-icon class="opportunity-info-icons">
                                                                {{form.mat_icon}}
                                                            </mat-icon>
                                                        </span>
                                                    </div>
                                                    <div class="col-6 opportunity-info-title">
                                                        {{form.form_name}}
                                                    </div>
                                                    <div class="col-4 opportunity-info-details" style="cursor: pointer;" 
                                                        (click)="openDynamicForm(form.form_id)">
                                                        {{form.form_value}} 
                                                    </div>
                                                    <div class="col-1 opportunity-info-details " *ngIf="!form.is_mandatory && userDeleteForm">

                                                        <div>
                                                            <span matTooltip="Delete" style="cursor: pointer;" class="mt-3 icon-tray-button"  (click)="deleteDynamicForm(form.form_name,form._id)">
                                                                <mat-icon class="smallCardIcon">delete</mat-icon>
                                                            </span>
                                                        
                                                        </div>
                                                        
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        
        
                                    </div>
                                </div>
                                <div class="row pt-3">
                                    <div class="col-12 opportunity-sub-headings">
                                        {{ 'moreinfo' | tenantOpportunity : formFieldData : 'label'}}
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-12">
                                        <div class="row pl-3 pt-2 slide-from-down" *ngIf="'probability'| tenantOpportunity : formFieldData : 'isActive'">
                                            <div class="col-1">
                                                <span>
                                                    <mat-icon class="opportunity-info-icons">
                                                        casino
                                                    </mat-icon>
                                                </span>
                                            </div>
                                            <div class="col-6 opportunity-info-title">
                                                {{ 'probability' | tenantOpportunity : formFieldData : 'label'}}
                                            </div>
                                            <div class="col-5 opportunity-info-details" style="cursor: pointer;" 
                                                [ngStyle]="{'pointer-events' : isDisableEdit || !oppEditAccess ? 'none' :( 'probability' | tenantOpportunity : formFieldData : 'isDisabled'  ) ? 'none' : ''}"
                                                interActiveInlineEdit  (click)="activateInlineEdit('Probability', 'minimal-dropdown',probabilityMaster,'')">
                                                {{this.tempOverviewDetails?.probability_perc_name?this.tempOverviewDetails?.probability_perc_name:'-'}}
                                            </div>
                                        </div>
                                        <div class="row pl-3 pt-2 slide-from-down" *ngIf="'confidenceLevel'| tenantOpportunity : formFieldData : 'isActive'">
                                            <div class="col-1">
                                                <span>
                                                    <mat-icon class="opportunity-info-icons">
                                                        casino
                                                    </mat-icon>
                                                </span>
                                            </div>
                                            <div class="col-6 opportunity-info-title">
                                                {{ 'confidenceLevel' | tenantOpportunity : formFieldData : 'label'}}
                                            </div>
                                            <div class="col-5 opportunity-info-details" style="cursor: pointer;" 
                                                [ngStyle]="{'pointer-events' : isDisableEdit || !oppEditAccess ? 'none' :( 'confidenceLevel' | tenantOpportunity : formFieldData : 'isDisabled'  ) ? 'none' : ''}">
                                                {{this.tempOverviewDetails?.confidence_level?this.tempOverviewDetails?.confidence_level:'-'}}
                                            </div>
                                        </div>
                                        <div class="row pl-3 pt-2 slide-from-down" *ngIf="'salesUnit'| tenantOpportunity : formFieldData : 'isActive'">
                                            <div class="col-1">
                                                <span>
                                                    <mat-icon class="opportunity-info-icons">
                                                        domain
                                                    </mat-icon>
                                                </span>
                                            </div>
                                            <div class="col-6 opportunity-info-title">
                                                {{ 'salesUnit' | tenantOpportunity : formFieldData : 'label'}}
                                            </div>
                                            <div  style="cursor:pointer;" class="col-5 opportunity-info-details" interActiveInlineEdit  
                                                [ngStyle]="{'pointer-events' : isDisableEdit || !oppEditAccess ? 'none' :( 'salesUnit' | tenantOpportunity : formFieldData : 'isDisabled'  ) ? 'none': ''}"
                                                (click)="activateInlineEdit('SalesOrg', 'minimal-dropdown',salesUnit,'')"
                                                [matTooltip]="overviewDetails?.sales_unit_name ? overviewDetails?.sales_unit_name : '-'">
                                                {{this.overviewDetails?.sales_unit_name?this.overviewDetails?.sales_unit_name:'-'}}
                                            </div>
                                        </div>
                                        <div class="row pl-3 pt-2 slide-from-down" *ngIf="'salesRegion'| tenantOpportunity : formFieldData : 'isActive'">
                                            <div class="col-1">
                                                <span>
                                                    <mat-icon class="opportunity-info-icons">
                                                        domain
                                                    </mat-icon>
                                                </span>
                                            </div>
                                            <div class="col-6 opportunity-info-title">
                                                {{ 'salesRegion' | tenantOpportunity : formFieldData : 'label'}}
                                            </div>
                                            <div style="cursor:pointer;" class="col-5 opportunity-info-details"  
                                                [ngStyle]="{'pointer-events' : isDisableEdit || !oppEditAccess ? 'none' :( 'salesRegion' | tenantOpportunity : formFieldData : 'isDisabled'  ) ? 'none': ''}"
                                                [matTooltip]="overviewDetails?.sales_region_name ? overviewDetails?.sales_region_name : '-'">
                                                {{ this.overviewDetails?.sales_region_name ? this.overviewDetails?.sales_region_name : '-' }}
                                            </div>
                                        </div>
                                        <div class="row pl-3 pt-2 slide-from-down" *ngIf="'perpetualContract'| tenantOpportunity : formFieldData : 'isActive'">
                                            <div class="col-1">
                                                <span>
                                                    <mat-icon class="opportunity-info-icons">
                                                        check_circle_outline
                                                    </mat-icon>
                                                </span>
                                            </div>
                                            <div class="col-6 opportunity-info-title">
                                                {{ 'perpetualContract' | tenantOpportunity : formFieldData : 'label'}}
                                            </div>
                                            <div class="col-5 opportunity-info-details"
                                                [matTooltip]="overviewDetails?.perpetual_contract ? (overviewDetails.perpetual_contract  == 1 ? 'Yes' : 'No')  : '-'">
                                                {{this.overviewDetails?.perpetual_contract  == 1 ? "Yes" : "No"}}
                                            </div>
                                        </div>
                                        <div class="row pl-3 pt-2 slide-from-down" *ngIf="'startDate'| tenantOpportunity : formFieldData : 'isActive'">
                                            <div class="col-1">
                                                <span>
                                                    <mat-icon class="opportunity-info-icons">
                                                        event
                                                    </mat-icon>
                                                </span>
                                            </div>
                                            <div class="col-6 opportunity-info-title">
                                                {{ 'startDate' | tenantOpportunity : formFieldData : 'label'}}
                                            </div>
                                            <div class="col-5 opportunity-info-details" style="cursor: pointer;" interActiveInlineEdit  
                                                [ngStyle]="{
                                                'pointer-events': isDisableEdit || !oppEditAccess ? 'none' : ('startDate' | tenantOpportunity : formFieldData : 'isDisabled') ? 'none' : '',
                                                'cursor': isDisableEdit || !oppEditAccess ? 'default' : ('startDate' | tenantOpportunity : formFieldData : 'isDisabled') ? 'default' : 'pointer'
                                            }"
                                                (click)="activateInlineEdit('Start date', 'date-picker', [],overviewDetails)"
                                                [matTooltip]="tempOverviewDetails?.processing_start_date ? (tempOverviewDetails?.processing_start_date | dynamicDatePipe) : '-'">
                                                {{this.tempOverviewDetails?.processing_start_date?(this.tempOverviewDetails?.processing_start_date|dynamicDatePipe):'-'}}
                                            </div>
                                        <!-- <div class="col-5 opportunity-only-date-info"
                                            [ngStyle]="{
                                                'pointer-events': isDisableEdit || !oppEditAccess ? 'none' : ('startDate' | tenantOpportunity : formFieldData : 'isDisabled') ? 'none' : '',
                                                'cursor': isDisableEdit || !oppEditAccess ? '' : ('startDate' | tenantOpportunity : formFieldData : 'isDisabled') ? '' : 'pointer'
                                            }"
                                            [matTooltip]="tempOverviewDetails?.processing_start_date ? (tempOverviewDetails?.processing_start_date | dynamicDatePipe) : '-'">
                                            <mat-form-field class="date">
                                                <mat-label *ngIf="!tempOverviewDetails?.processing_start_date" 
                                                    >-</mat-label>
                                                <input class="date-picker-readonly"
                                                    *ngIf="tempOverviewDetails?.processing_start_date"
                                                [ngStyle]="{
                                                    'pointer-events': isDisableEdit || !oppEditAccess ? 'none' : ('startDate' | tenantOpportunity : formFieldData : 'isDisabled') ? 'none' : '',
                                                    'cursor': isDisableEdit || !oppEditAccess ? 'default' : ('startDate' | tenantOpportunity : formFieldData : 'isDisabled') ? 'default' : 'pointer'
                                                }"    
                                                    matInput [matDatepicker]="picker1"
                                                    [value]="tempOverviewDetails?.processing_start_date"
                                                    (dateChange)="onDateChange('Start Date', tempOverviewDetails, $event.value)"
                                                    (click)="picker1.open()" readonly>
                                                <mat-datepicker #picker1></mat-datepicker>
                                            </mat-form-field>
                                        </div> -->

                                        </div>
                                        <div class="row pl-3 pt-2 slide-from-down" *ngIf="'originalEndDate'| tenantOpportunity : formFieldData : 'isActive'"> 
                                            <div class="col-1">
                                                <span>
                                                    <mat-icon class="opportunity-info-icons">
                                                        event
                                                    </mat-icon>
                                                </span>
                                            </div>
                                            <div class="col-6 opportunity-info-title">
                                                {{ 'originalEndDate' | tenantOpportunity : formFieldData : 'label'}}
                                            </div>
                                            <div class="col-5 opportunity-info-details" 
                                                [matTooltip]="tempOverviewDetails?.original_processing_end_date ? (tempOverviewDetails?.original_processing_end_date | dynamicDatePipe) : '-'">
                                                {{tempOverviewDetails?.original_processing_end_date?(tempOverviewDetails?.original_processing_end_date|dynamicDatePipe):'-'}}
                                            </div>
                                        </div>
                                        <div class="row pl-3 pt-2 slide-from-down" *ngIf="'endDate'| tenantOpportunity : formFieldData : 'isActive'">
                                            <div class="col-1">
                                                <span>
                                                    <mat-icon class="opportunity-info-icons">
                                                        event
                                                    </mat-icon>
                                                </span>
                                            </div>
                                            <div class="col-6 opportunity-info-title">
                                                {{ 'endDate' | tenantOpportunity : formFieldData : 'label'}}
                                            </div>
                                            <div class="col-5 opportunity-info-details" style="cursor: pointer;" interActiveInlineEdit
                                                [ngStyle]="{'pointer-events' : isDisableEdit || !oppEditAccess ? 'none':( 'endDate' | tenantOpportunity : formFieldData : 'isDisabled'  ) ? 'none' : ''}"
                                                (click)="activateInlineEdit('End date', 'date-picker', [],overviewDetails)"
                                                [matTooltip]="overviewDetails?.processing_end_date ? (overviewDetails?.processing_end_date | dynamicDatePipe) : '-'">
                                                {{this.tempOverviewDetails?.processing_end_date?(this.tempOverviewDetails?.processing_end_date|dynamicDatePipe):'-'}}
                                            </div>
                                            <!-- <div class="col-5 opportunity-only-date-info d-flex"
                                            [ngStyle]="{
                                                'pointer-events': isDisableEdit || !oppEditAccess ? 'none' : ('endDate' | tenantOpportunity : formFieldData : 'isDisabled') ? 'none' : '',
                                                'cursor': isDisableEdit || !oppEditAccess ? 'default' : ('endDate' | tenantOpportunity : formFieldData : 'isDisabled') ? 'default' : 'pointer'
                                            }">
                                            <div class="value-div"  [matTooltip]="tempOverviewDetails?.processing_end_date ? (tempOverviewDetails?.processing_end_date | dynamicDatePipe) : '-'">
                                                <mat-form-field class="date">
                                                    <mat-label *ngIf="!tempOverviewDetails?.processing_end_date"
                                                        >-</mat-label>
                                                    <input class="date-picker-readonly"
                                                    [ngStyle]="{
                                                        'pointer-events': isDisableEdit || !oppEditAccess ? 'none' : ('endDate' | tenantOpportunity : formFieldData : 'isDisabled') ? 'none' : '',
                                                        'cursor': isDisableEdit || !oppEditAccess ? 'default' : ('endDate' | tenantOpportunity : formFieldData : 'isDisabled') ? 'default' : 'pointer'
                                                    }"  
                                                        matInput [matDatepicker]="picker1"
                                                        [value]="tempOverviewDetails?.processing_end_date"
                                                        (dateChange)="onDateChange('End Date', tempOverviewDetails, $event.value)"
                                                        (click)="picker1.open()" readonly>
                                                    <mat-datepicker #picker1></mat-datepicker>
                                                </mat-form-field>
                                            </div>
                                            <div style=" cursor: pointer;
                                            z-index: 99;
                                            pointer-events: all;" *ngIf="('endDate' | tenantOpportunity:formFieldData:'is_audit' )&& ('endDate' | tenantOpportunity:formFieldData:'showHistoryButton')" [matTooltip]="('endDate' | tenantOpportunity: formFieldData : 'label') + ' history'">
                                                <span  style="cursor: pointer;" (click)="$event.stopPropagation();openOpportunityAuditlog('endDate')" >
                                                    <mat-icon class="smallCardIcon">history</mat-icon>
                                                    </span>
                                                </div>
                                        </div> -->

                                        </div>
                                        <div class="row pl-3 pt-2 slide-from-down" *ngIf="'deliveryStartDate'| tenantOpportunity : formFieldData : 'isActive'">
                                            <div class="col-1">
                                                <span>
                                                    <mat-icon class="opportunity-info-icons">
                                                        event
                                                    </mat-icon>
                                                </span>
                                            </div>
                                            <div class="col-6 opportunity-info-title">
                                                {{ 'deliveryStartDate' | tenantOpportunity : formFieldData : 'label'}}
                                            </div>
                                            <div class="col-5 opportunity-info-details"  style="cursor: pointer;" interActiveInlineEdit  
                                            [ngStyle]="{'pointer-events' : (isDisableEdit || !oppEditAccess || (this.opportunityProjectDetails?.length > 0 || isActiveQuoteExists) || ('deliveryStartDate' | tenantOpportunity : formFieldData : 'isDisabled')) ? 'none' : ''}"
                                            (click)="activateInlineEdit('Delivery Start Date', 'date-picker', [],overviewDetails)"
                                                [matTooltip]="overviewDetails?.deliveryStartDate ? (overviewDetails?.deliveryStartDate | dynamicDatePipe) : '-'">
                                                {{this.tempOverviewDetails?.deliveryStartDate?((this.tempOverviewDetails?.deliveryStartDate|dynamicDatePipe) == "Invalid date" ? "-" : (this.tempOverviewDetails?.deliveryStartDate|dynamicDatePipe)):'-'}}
                                            </div>
                                            <!-- <div class="col-5 opportunity-only-date-info" 
                                            [ngStyle]="{
                                                'pointer-events' : (isDisableEdit || !oppEditAccess || (this.opportunityProjectDetails?.length > 0) || ('deliveryStartDate' | tenantOpportunity : formFieldData : 'isDisabled')) ? 'none' : '',
                                                'cursor' : (isDisableEdit || !oppEditAccess || (this.opportunityProjectDetails?.length > 0) || ('deliveryStartDate' | tenantOpportunity : formFieldData : 'isDisabled')) ? 'default' : 'pointer'
                                            }"
                                            [matTooltip]="tempOverviewDetails?.deliveryStartDate ? (tempOverviewDetails?.deliveryStartDate | dynamicDatePipe) : '-'">
                                            <mat-form-field class="date">
                                                <mat-label *ngIf="!tempOverviewDetails?.deliveryStartDate" 
                                                    >-</mat-label>
                                                <input class="date-picker-readonly"
                                                [ngStyle]="{
                                                    'pointer-events': isDisableEdit || !oppEditAccess ? 'none' : ('deliveryStartDate' | tenantOpportunity : formFieldData : 'isDisabled') ? 'none' : '',
                                                    'cursor': isDisableEdit || !oppEditAccess ? 'default' : ('deliveryStartDate' | tenantOpportunity : formFieldData : 'isDisabled') ? 'default' : 'pointer'
                                                }"   
                                                    matInput [matDatepicker]="picker1"
                                                    [max]="tempOverviewDetails?.deliveryFinishDate"
                                                    [ngModel]="deliveryStartTempDate || tempOverviewDetails?.deliveryStartDate"
                                                    (dateChange)="onDateChange('Delivery Start Date', tempOverviewDetails, $event.value)"
                                                    (click)="picker1.open()" readonly>
                                                <mat-datepicker #picker1></mat-datepicker>
                                            </mat-form-field>
                                        </div> -->
                                        </div>
                                        <div class="row pl-3 pt-2 slide-from-down" *ngIf="'deliveryFinishDate'| tenantOpportunity : formFieldData : 'isActive'">
                                            <div class="col-1">
                                                <span>
                                                    <mat-icon class="opportunity-info-icons">
                                                        event
                                                    </mat-icon>
                                                </span>
                                            </div>
                                            <div class="col-6 opportunity-info-title">
                                                {{ 'deliveryFinishDate' | tenantOpportunity : formFieldData : 'label'}}
                                            </div>
                                            <div class="col-5 opportunity-info-details"  style="cursor: pointer;" interActiveInlineEdit  
                                            [ngStyle]="{'pointer-events' : (isDisableEdit || !oppEditAccess || (this.opportunityProjectDetails?.length > 0 || isActiveQuoteExists) || ('deliveryFinishDate' | tenantOpportunity : formFieldData : 'isDisabled')) ? 'none' : ''}"
                                            (click)="activateInlineEdit('Delivery Finish Date', 'date-picker', [],overviewDetails)"
                                            [matTooltip]="overviewDetails?.deliveryFinishDate ? (overviewDetails?.deliveryFinishDate | dynamicDatePipe) : '-'">
                                                {{this.tempOverviewDetails?.deliveryFinishDate?((this.tempOverviewDetails?.deliveryFinishDate|dynamicDatePipe) == "Invalid date" ? "-" : (this.tempOverviewDetails?.deliveryFinishDate|dynamicDatePipe)):'-'}}
                                            </div>
                                            <!-- <div class="col-5 opportunity-only-date-info"
                                            [ngStyle]="{
                                                'pointer-events' : (isDisableEdit || !oppEditAccess || (this.opportunityProjectDetails?.length > 0) || ('deliveryFinishDate' | tenantOpportunity : formFieldData : 'isDisabled')) ? 'none' : '',
                                                'cursor' : (isDisableEdit || !oppEditAccess || (this.opportunityProjectDetails?.length > 0) || ('deliveryFinishDate' | tenantOpportunity : formFieldData : 'isDisabled')) ? 'default' : 'pointer'
                                            }"
                                            [matTooltip]="tempOverviewDetails?.deliveryFinishDate ? (tempOverviewDetails?.deliveryFinishDate | dynamicDatePipe) : '-'">
                                            <mat-form-field class="date">
                                                <mat-label *ngIf="!tempOverviewDetails?.deliveryFinishDate">-</mat-label>
                                                <input class="date-picker-readonly"
                                                  [ngStyle]="{
                                                    'pointer-events': isDisableEdit || !oppEditAccess ? 'none' : ('startDate' | tenantOpportunity : formFieldData : 'isDisabled') ? 'none' : '',
                                                    'cursor': isDisableEdit || !oppEditAccess ? 'default' : ('startDate' | tenantOpportunity : formFieldData : 'isDisabled') ? 'default' : 'pointer'
                                                  }"
                                                  matInput [matDatepicker]="picker1"
                                                  [min]="tempOverviewDetails?.deliveryStartDate"
                                                  [ngModel]="deliveryEndTempDate || tempOverviewDetails?.deliveryFinishDate"
                                                  (dateChange)="onDateChange('Delivery Finish Date', tempOverviewDetails, $event.value)"
                                                  (click)="picker1.open()" readonly>
                                                <mat-datepicker #picker1></mat-datepicker>
                                              </mat-form-field>
                                        </div> -->
                                        </div>
                                        <div class="row pl-3 pt-2 slide-from-down" *ngIf="'poNumber'| tenantOpportunity : formFieldData : 'isActive'">
                                            <div class="col-1">
                                                <span>
                                                    <mat-icon class="opportunity-info-icons">
                                                        confirmation_number
                                                    </mat-icon>
                                                </span>
                                            </div>
                                            <div class="col-6 opportunity-info-title">
                                                {{ 'poNumber' | tenantOpportunity : formFieldData : 'label'}}
                                            </div>
                                            <div class="col-5 opportunity-info-details"
                                                [matTooltip]="overviewDetails?.po_no ? overviewDetails?.po_no : '-'">
                                                {{this.overviewDetails?.po_no ? this.overviewDetails?.po_no :'-'}}
                                            </div>
                                        </div>
                                        <div class="row pl-3 pt-2 slide-from-down" *ngIf="'poDate'| tenantOpportunity : formFieldData : 'isActive'">
                                            <div class="col-1">
                                                <span>
                                                    <mat-icon class="opportunity-info-icons">
                                                        event
                                                    </mat-icon>
                                                </span>
                                            </div>
                                            <div class="col-6 opportunity-info-title">
                                             {{ 'poDate' | tenantOpportunity : formFieldData : 'label'}}
                                            </div>
                                            <div class="col-5 opportunity-info-details"
                                                [matTooltip]="overviewDetails?.po_date ? (overviewDetails?.po_date | dynamicDatePipe) : '-'">
                                                {{overviewDetails?.po_date ? (overviewDetails?.po_date | dynamicDatePipe) : '-'}}
                                            </div>
                                        </div>
                                        <div class="row pl-3 pt-2 slide-from-down" *ngIf="('poValue'| tenantOpportunity : formFieldData : 'isActive') && checkFieldShouldbeRestricted('totalRevenue')">
                                            <div class="col-1">
                                                <span>
                                                    <mat-icon class="opportunity-info-icons">
                                                        confirmation_number
                                                    </mat-icon>
                                                </span>
                                            </div>
                                            <div class="col-6 opportunity-info-title">
                                                {{ 'poValue' | tenantOpportunity : formFieldData : 'label'}}
                                            </div>
                                            <div class="col-5 opportunity-info-details"
                                                [matTooltip]="overviewDetails?.po_value ? overviewDetails?.po_value : '-'">
                                                {{this.overviewDetails?.po_value ? this.overviewDetails?.po_value :'-'}}
                                            </div>
                                        </div>
                                        <div class="row pl-3 pt-2 slide-from-down" *ngIf="'actualClosureDate'| tenantOpportunity : formFieldData : 'isActive'">
                                            <div class="col-1">
                                                <span>
                                                    <mat-icon class="opportunity-info-icons">
                                                        event
                                                    </mat-icon>
                                                </span>
                                            </div>
                                            <div class="col-6 opportunity-info-title">
                                                {{ 'actualClosureDate' | tenantOpportunity : formFieldData : 'label'}}
                                            </div>
                                            <div class="col-5 opportunity-info-details" style="cursor: pointer;"  interActiveInlineEdit  
                                            [ngStyle]="{'pointer-events' : isDisableEdit || !oppEditAccess ? 'none' :( 'actualClosureDate' | tenantOpportunity : formFieldData : 'isDisabled'  ) ? 'none' : ''}"
                                            (click)="activateInlineEdit('Actual Closure Date', 'date-picker', [],overviewDetails)"
                                                [matTooltip]="overviewDetails?.actual_closure_date ? (overviewDetails?.actual_closure_date | dynamicDatePipe) : '-'">
                                                {{this.tempOverviewDetails?.actual_closure_date?((this.tempOverviewDetails?.actual_closure_date|dynamicDatePipe) == "Invalid date" ? "-" : (this.tempOverviewDetails?.actual_closure_date|dynamicDatePipe)):'-'}}
                                            </div>

                                          
                                        <!-- <div class="col-5 opportunity-info-details"
                                            [ngStyle]="{'pointer-events' : isDisableEdit || !oppEditAccess ? 'none' :( 'actualClosureDate' | tenantOpportunity : formFieldData : 'isDisabled'  ) ? 'none' : '',
                                            'cursor': isDisableEdit || !oppEditAccess ? 'default' : ('actualClosureDate' | tenantOpportunity : formFieldData : 'isDisabled') ? 'default' : 'pointer'
                                            }"
                                            [matTooltip]="tempOverviewDetails?.actual_closure_date ? (tempOverviewDetails?.actual_closure_date | dynamicDatePipe) : '-'">
                                            <mat-form-field class="date">
                                                <mat-label *ngIf="!tempOverviewDetails?.actual_closure_date">-</mat-label>
                                                <input class="date-picker-readonly"
                                                    matInput [matDatepicker]="picker1"
                                                    [ngStyle]="{'pointer-events' : isDisableEdit || !oppEditAccess ? 'none' :( 'actualClosureDate' | tenantOpportunity : formFieldData : 'isDisabled'  ) ? 'none' : '',
                                                    'cursor': isDisableEdit || !oppEditAccess ? 'default' : ('actualClosureDate' | tenantOpportunity : formFieldData : 'isDisabled') ? 'default' : 'pointer'
                                                    }"
                                                    [ngModel]="tempSelectedDate || tempOverviewDetails?.actual_closure_date"
                                                    (dateChange)="onDateChange('Actual Closure Date', tempOverviewDetails, $event.value)"
                                                    (click)="picker1.open()" readonly>
                                                <mat-datepicker #picker1></mat-datepicker>
                                            </mat-form-field>
                                        </div> -->

                                        </div>
                                        <div class="row pl-3 pt-2 slide-from-down" *ngIf="'legalentitiy'| tenantOpportunity : formFieldData : 'isActive'">
                                            <div class="col-1">
                                                <span>
                                                    <mat-icon class="opportunity-info-icons">
                                                        domain
                                                    </mat-icon>
                                                </span>
                                            </div>
                                            <div class="col-6 opportunity-info-title">
                                                {{ 'legalentitiy' | tenantOpportunity : formFieldData : 'label'}}
                                            </div>
                                            <div  style="cursor:pointer;" class="col-5 opportunity-info-details">
                                                {{this.overviewDetails?.legalentitiy?this.overviewDetails?.legalentitiy:'-'}}
                                            </div>
                                        </div>
                                        <div class="row pl-3 pt-2 slide-from-down" *ngIf="'rfpDate'| tenantOpportunity : formFieldData : 'isActive'">
                                            <div class="col-1">
                                                <span>
                                                    <mat-icon class="opportunity-info-icons">
                                                        event
                                                    </mat-icon>
                                                </span>
                                            </div>
                                            <div class="col-6 opportunity-info-title">
                                                {{ 'rfpDate' | tenantOpportunity : formFieldData : 'label'}}
                                            </div>
                                            <div class="col-5 opportunity-info-details" style="cursor: pointer;" interActiveInlineEdit  
                                                [ngStyle]="{'pointer-events' : isDisableEdit || !oppEditAccess ? 'none' :( 'rfpDate' | tenantOpportunity : formFieldData : 'isDisabled'  ) ? 'none': ''}"
                                                (click)="activateInlineEdit('RFP release date', 'date-picker', [],overviewDetails)">
                                                {{this.tempOverviewDetails?.rfp_date?(this.tempOverviewDetails?.rfp_date=="0000-00-00 00:00:00"?'-':this.tempOverviewDetails?.rfp_date|dynamicDatePipe):'-'}}
                                            </div>
                                            <!-- <div class="col-5 opportunity-only-date-info"
                                            [ngStyle]="{
                                                'pointer-events': isDisableEdit || !oppEditAccess ? 'none' : ('rfpDate' | tenantOpportunity : formFieldData : 'isDisabled') ? 'none' : '',
                                                'cursor': isDisableEdit || !oppEditAccess ? 'default' : ('rfpDate' | tenantOpportunity : formFieldData : 'isDisabled') ? 'default' : 'pointer'
                                            }"
                                            [matTooltip]="tempOverviewDetails?.rfp_date ? (tempOverviewDetails?.rfp_date | dynamicDatePipe) : '-'">
                                            <mat-form-field class="date">
                                                <mat-label *ngIf="!tempOverviewDetails?.rfp_date" 
                                                    >-</mat-label>
                                                <input class="date-picker-readonly"
                                                [ngStyle]="{
                                                    'pointer-events': isDisableEdit || !oppEditAccess ? 'none' : ('rfpDate' | tenantOpportunity : formFieldData : 'isDisabled') ? 'none' : '',
                                                    'cursor': isDisableEdit || !oppEditAccess ? 'default' : ('rfpDate' | tenantOpportunity : formFieldData : 'isDisabled') ? 'default' : 'pointer'
                                                }"   
                                                    matInput [matDatepicker]="picker1"
                                                    [value]="tempOverviewDetails?.rfp_date"
                                                    (dateChange)="onDateChange('RFP release date', tempOverviewDetails, $event.value)"
                                                    (click)="picker1.open()" readonly>
                                                <mat-datepicker #picker1></mat-datepicker>
                                            </mat-form-field>
                                        </div> -->
                                        </div>
                                        <div class="row pl-3 pt-2 slide-from-down" *ngIf="'proposalSubmissionDate'| tenantOpportunity : formFieldData : 'isActive'">
                                            <div class="col-1">
                                                <span>
                                                    <mat-icon class="opportunity-info-icons">
                                                        event
                                                    </mat-icon>
                                                </span>
                                            </div>
                                            <div class="col-6 opportunity-info-title">
                                                {{ 'proposalSubmissionDate' | tenantOpportunity : formFieldData : 'label'}}
                                            </div>
                                            <div class="col-5 opportunity-info-details" style="cursor: pointer;" interActiveInlineEdit  
                                                [ngStyle]="{'pointer-events' : isDisableEdit || !oppEditAccess ? 'none' :( 'proposalSubmissionDate' | tenantOpportunity : formFieldData : 'isDisabled'  ) ? 'none' : ''}"
                                                (click)="activateInlineEdit('Proposal date', 'date-picker', [],overviewDetails)">
                                                {{this.tempOverviewDetails?.proposal_submission_date?(this.tempOverviewDetails?.proposal_submission_date=="0000-00-00 00:00:00"?'-':this.tempOverviewDetails?.proposal_submission_date|dynamicDatePipe):'-'}}
                                            </div>
                                            <!-- <div class="col-5 opportunity-only-date-info"
                                            [ngStyle]="{
                                                'pointer-events': isDisableEdit || !oppEditAccess ? 'none' : ('proposalSubmissionDate' | tenantOpportunity : formFieldData : 'isDisabled') ? 'none' : '',
                                                'cursor': isDisableEdit || !oppEditAccess ? 'default' : ('proposalSubmissionDate' | tenantOpportunity : formFieldData : 'isDisabled') ? 'default' : 'pointer'
                                            }"
                                            [matTooltip]="tempOverviewDetails?.proposal_submission_date ? (tempOverviewDetails?.proposal_submission_date | dynamicDatePipe) : '-'">
                                            <mat-form-field class="date">
                                                <mat-label *ngIf="!tempOverviewDetails?.proposal_submission_date" 
                                                    >-</mat-label>
                                                <input class="date-picker-readonly"
                                                [ngStyle]="{
                                                    'pointer-events': isDisableEdit || !oppEditAccess ? 'none' : ('proposalSubmissionDate' | tenantOpportunity : formFieldData : 'isDisabled') ? 'none' : '',
                                                    'cursor': isDisableEdit || !oppEditAccess ? 'default' : ('proposalSubmissionDate' | tenantOpportunity : formFieldData : 'isDisabled') ? 'default' : 'pointer'
                                                }"   
                                                    matInput [matDatepicker]="picker1"
                                                    [value]="tempOverviewDetails?.proposal_submission_date"
                                                    (dateChange)="onDateChange('Proposal date', tempOverviewDetails, $event.value)"
                                                    (click)="picker1.open()" readonly>
                                                <mat-datepicker #picker1></mat-datepicker>
                                            </mat-form-field>
                                        </div> -->
                                        </div>
                                        <div class="row pl-3 pt-2 slide-from-down" *ngIf="'proposalDate'| tenantOpportunity : formFieldData : 'isActive'">
                                            <div class="col-1">
                                                <span>
                                                    <mat-icon class="opportunity-info-icons">
                                                        event
                                                    </mat-icon>
                                                </span>
                                            </div>
                                            <div class="col-6 opportunity-info-title">
                                                {{ 'proposalDate' | tenantOpportunity : formFieldData : 'label'}}
                                            </div>
                                            <!-- <div class="col-5 opportunity-info-details" style="cursor: pointer;" interActiveInlineEdit  
                                                [ngStyle]="{'pointer-events' : isDisableEdit || !oppEditAccess ? 'none':( 'proposalDate' | tenantOpportunity : formFieldData : 'isDisabled'  ) ? 'none' : ''}"
                                                (click)="activateInlineEdit('Proposal Sales Date', 'date-picker', [],overviewDetails)">
                                                {{this.tempOverviewDetails?.sales_proposal_date?(this.tempOverviewDetails?.sales_proposal_date=="0000-00-00 00:00:00"?'-':this.tempOverviewDetails?.sales_proposal_date|dynamicDatePipe):'-'}}
                                            </div> -->
                                            <div class="col-5 opportunity-only-date-info"
                                            [ngStyle]="{
                                                'pointer-events': isDisableEdit || !oppEditAccess ? 'none' : ('proposalDate' | tenantOpportunity : formFieldData : 'isDisabled') ? 'none' : '',
                                                'cursor': isDisableEdit || !oppEditAccess ? 'default' : ('proposalDate' | tenantOpportunity : formFieldData : 'isDisabled') ? 'default' : 'pointer'
                                            }"
                                            [matTooltip]="tempOverviewDetails?.sales_proposal_date ? (tempOverviewDetails?.sales_proposal_date | dynamicDatePipe) : '-'">
                                            <mat-form-field class="date">
                                                <mat-label *ngIf="!tempOverviewDetails?.sales_proposal_date" 
                                                    >-</mat-label>
                                                <input class="date-picker-readonly"
                                                [ngStyle]="{
                                                    'pointer-events': isDisableEdit || !oppEditAccess ? 'none' : ('proposalDate' | tenantOpportunity : formFieldData : 'isDisabled') ? 'none' : '',
                                                    'cursor': isDisableEdit || !oppEditAccess ? 'default' : ('proposalDate' | tenantOpportunity : formFieldData : 'isDisabled') ? 'default' : 'pointer'
                                                }"   
                                                    matInput [matDatepicker]="picker1"
                                                    [value]="tempOverviewDetails?.sales_proposal_date"
                                                    (dateChange)="onDateChange('Proposal Sales Date' ,tempOverviewDetails, $event.value)"
                                                    (click)="picker1.open()" readonly>
                                                <mat-datepicker #picker1></mat-datepicker>
                                            </mat-form-field>
                                        </div>
                                        </div>
                                        <div class="row pl-3 pt-2 slide-from-down" *ngIf="'firstBillingDate'| tenantOpportunity : formFieldData : 'isActive'">
                                            <div class="col-1">
                                                <span>
                                                    <mat-icon class="opportunity-info-icons">
                                                        event
                                                    </mat-icon>
                                                </span>
                                            </div>
                                            <div class="col-6 opportunity-info-title">
                                                {{ 'firstBillingDate' | tenantOpportunity : formFieldData : 'label'}}
                                            </div>
                                            <div class="col-5 opportunity-info-details" 
                                            [ngStyle]="{'pointer-events' : isDisableEdit || !oppEditAccess ? 'none' :( 'firstBillingDate' | tenantOpportunity : formFieldData : 'isDisabled'  ) ? 'none' : ''}"
                                                [matTooltip]="overviewDetails?.firstBillingDate ? (overviewDetails?.firstBillingDate | dynamicDatePipe) : '-'">
                                                {{this.tempOverviewDetails?.firstBillingDate?((this.tempOverviewDetails?.firstBillingDate|dynamicDatePipe) == "Invalid date" ? "-" : (this.tempOverviewDetails?.firstBillingDate|dynamicDatePipe)):'-'}}
                                            </div>
                                        </div>
                                        <div class="row pl-3 pt-2 slide-from-down" *ngIf="'parentOpportunity'| tenantOpportunity : formFieldData : 'isActive'">
                                            <div class="col-1">
                                                <span>
                                                    <mat-icon class="opportunity-info-icons">
                                                        format_align_left 
                                                    </mat-icon>
                                                </span>
                                            </div>
                                        <div class="col-6 opportunity-info-title">
                                            {{ 'parentOpportunity' | tenantOpportunity : formFieldData : 'label'}}
                                        </div>
                                        <div class="col-5 opportunity-info-details" 
                                            
                                            [matTooltip]="this.tempOverviewDetails?.parent_opportunity_name ? this.tempOverviewDetails?.parent_opportunity_name : '-'">
                                            {{this.tempOverviewDetails?.parent_opportunity_name?this.tempOverviewDetails?.parent_opportunity_name:'-'}}
                                        </div>
                                        </div>
                                        <div class="row pl-3 pt-2 slide-from-down" *ngIf="'blanketPO'| tenantOpportunity : formFieldData : 'isActive'">
                                            <div class="col-1">
                                                <span>
                                                    <mat-icon class="opportunity-info-icons">
                                                        format_align_left 
                                                    </mat-icon>
                                                </span>
                                            </div>
                                            <div class="col-6 opportunity-info-title">
                                                {{ 'blanketPO' | tenantOpportunity : formFieldData : 'label'}}
                                            </div>
                                            <div class="col-5 opportunity-info-details" 
                                                
                                                [matTooltip]="this.tempOverviewDetails?.blanket_po ? this.tempOverviewDetails?.blanket_po : '-'">
                                                {{this.tempOverviewDetails?.blanket_po?this.tempOverviewDetails?.blanket_po:'-'}}
                                            </div>
                                        </div>
                                        <div class="row pl-3 pt-2 slide-from-down" *ngIf="'priorityForDeployment'| tenantOpportunity : formFieldData : 'isActive'">
                                            <div class="col-1">
                                                <span>
                                                    <mat-icon class="opportunity-info-icons">
                                                        format_align_left 
                                                    </mat-icon>
                                                </span>
                                            </div>
                                            <div class="col-6 opportunity-info-title">
                                                {{ 'priorityForDeployment' | tenantOpportunity : formFieldData : 'label'}}
                                            </div>
                                            <div class="col-5 opportunity-info-details" 
                                                
                                                [matTooltip]="this.tempOverviewDetails?.priority_for_deployment ? this.tempOverviewDetails?.priority_for_deployment : '-'">
                                                {{this.tempOverviewDetails?.priority_for_deployment?this.tempOverviewDetails?.priority_for_deployment:'-'}}
                                            </div>
                                        </div>
                                        <!-- <div class="row pl-3 pt-2 slide-from-down">
                                            <div class="col-1">
                                                <span>
                                                    <mat-icon class="opportunity-info-icons">
                                                        people
                                                    </mat-icon>
                                                </span>
                                            </div>
                                            <div class="col-6 opportunity-info-title">
                                                Channel id
                                            </div>
                                            <div class="col-5 opportunity-info-details" style="cursor: pointer;" interActiveInlineEdit  (click)="activateInlineEdit('channel id','simple-text',[],overviewDetails)">
                                                {{this.overviewDetails?.channel_id?this.overviewDetails?.channel_id:'-'}}

                                            </div>
                                        </div> -->
                                        <!-- <div class="row pl-3 pt-2 slide-from-down">
                      <div class="col-2">
                        <span>
                          <mat-icon class="opportunity-info-icons">
                            event
                          </mat-icon>
                        </span>
                      </div>
                      <div class="col-5 opportunity-info-title">
                        CCF date
                      </div>
                      <div class="col-5 opportunity-info-details">
                        {{this.overviewDetails?.ccf_date?(this.overviewDetails?.ccf_date|dynamicDatePipe):'-'}}
                      </div>
                    </div>  -->
                                        <!-- <div class="row pl-3 pt-2 slide-from-down">
                      <div class="col-2">
                        <span>
                          <mat-icon class="opportunity-info-icons">
                            event
                          </mat-icon>
                        </span>
                      </div>
                      <div class="col-5 opportunity-info-title">
                        Submission to Customer Date
                      </div>
                      <div class="col-5 opportunity-info-details">
                        <span>
                          <inline-form-field label='Proposal' type='textDate' formControlName='proposalSubmissionDate'>
                          </inline-form-field>
                        </span>
                      </div>
                    </div> -->
                                    </div>
                                </div>
                                <div class="row pt-2" style="border-bottom: #d6d6d6 1px solid;">


                                </div>
                                <div class="row pt-2">
                                    <div class="col-12 opportunity-sub-headings">
                                        {{ 'owners' | tenantOpportunity : formFieldData : 'label'}}
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-12">
                                        <div class="row pl-3 pt-2" *ngIf="'salesOwner'| tenantOpportunity : formFieldData : 'isActive'">
                                            <div class="col-1">
                                                <span>
                                                    <mat-icon class="opportunity-info-icons">
                                                        account_circle
                                                    </mat-icon>
                                                </span>
                                            </div>
                                            <div class="col-6 opportunity-info-title">
                                                {{ 'salesOwner' | tenantOpportunity : formFieldData : 'label'}}
                                            </div>
                                            <div class="col-5 opportunity-info-details" style="cursor:pointer;" 
                                                [ngStyle]="{'pointer-events' : isDisableEdit || !oppEditAccess ? 'none' : ''}">
                                                <app-user-profile type="name"
                                                    oid="{{this.tempOverviewDetails?.sales_owner?this.tempOverviewDetails?.sales_owner:''}}">
                                                </app-user-profile>
                                            </div>
                                        </div> 

                                        <div class="row pl-3 pt-2" *ngIf="'sdm'| tenantOpportunity : formFieldData : 'isActive'">
                                            <div class="col-1">
                                                <span>
                                                    <mat-icon class="opportunity-info-icons">
                                                        account_circle
                                                    </mat-icon>
                                                </span>
                                            </div>
                                            <div class="col-6 opportunity-info-title">
                                                {{ 'sdm' | tenantOpportunity : formFieldData : 'label'}}
                                            </div>
                                            <div class="col-5 opportunity-info-details" style="cursor:pointer;">
                                                <app-user-profile type="name"
                                                    oid="{{this.tempOverviewDetails?.sdm_oid?this.tempOverviewDetails?.sdm_oid:''}}">
                                                </app-user-profile>
                                            </div>
                                        </div> 


                                        <div class="row pl-3 pt-2" *ngIf="'spoc'| tenantOpportunity : formFieldData : 'isActive'">
                                            <div class="col-1">
                                                <span>
                                                    <mat-icon class="opportunity-info-icons">
                                                        account_circle
                                                    </mat-icon>
                                                </span>
                                            </div>
                                            <div class="col-6 opportunity-info-title">
                                                {{ 'spoc' | tenantOpportunity : formFieldData : 'label'}}
                                            </div>
                                            <div class="col-5 opportunity-info-details" style="cursor:pointer;" 
                                                [ngStyle]="{'pointer-events' : isDisableEdit || !oppEditAccess ? 'none' : ''}">
                                                <app-user-profile type="name"
                                                    oid="{{this.tempOverviewDetails?.opportunity_spoc_oid?this.tempOverviewDetails?.opportunity_spoc_oid:''}}">
                                                </app-user-profile>
                                            </div>
                                        </div>
                                        <div class="row pl-3 pt-2" *ngIf="'marketingSPOC'| tenantOpportunity : formFieldData : 'isActive'">
                                            <div class="col-1">
                                                <span>
                                                    <mat-icon class="opportunity-info-icons">
                                                        account_circle
                                                    </mat-icon>
                                                </span>
                                            </div>
                                            <div class="col-6 opportunity-info-title">
                                                {{ 'marketingSPOC' | tenantOpportunity : formFieldData : 'label'}}
                                            </div>
                                            <div class="col-5 opportunity-info-details" style="cursor:pointer;" 
                                                [ngStyle]="{'pointer-events' : isDisableEdit || !oppEditAccess ? 'none' : ''}">
                                                <app-user-profile type="name"
                                                    oid="{{this.tempOverviewDetails?.marketing_spoc?this.tempOverviewDetails?.marketing_spoc:''}}">
                                                </app-user-profile>
                                            </div>
                                        </div>
                                        <div class="row pl-3 pt-2" *ngIf="'insideSalesSPOC'| tenantOpportunity : formFieldData : 'isActive'">
                                            <div class="col-1">
                                                <span>
                                                    <mat-icon class="opportunity-info-icons">
                                                        account_circle
                                                    </mat-icon>
                                                </span>
                                            </div>
                                            <div class="col-6 opportunity-info-title">
                                                {{ 'insideSalesSPOC' | tenantOpportunity : formFieldData : 'label'}}
                                            </div>
                                            <div class="col-5 opportunity-info-details" style="cursor:pointer;" 
                                                [ngStyle]="{'pointer-events' : isDisableEdit || !oppEditAccess ? 'none' : ''}">
                                                <app-user-profile type="name"
                                                    oid="{{this.tempOverviewDetails?.inside_sales_spoc?this.tempOverviewDetails?.inside_sales_spoc:''}}">
                                                </app-user-profile>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row pt-2" style="border-bottom: #d6d6d6 1px solid;">

                                </div>
                                <div class="row pt-2">
                                    <div class="col-12 opportunity-sub-headings">
                                        {{ 'status' | tenantOpportunity : formFieldData : 'label'}}
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-12">
                                        <div class="row pl-3 pt-2" *ngIf="'salesStatus'| tenantOpportunity : formFieldData : 'isActive'">
                                            <ng-container *ngIf="overviewDetails">
                                                <div class="col-1">
                                                    <div class="status-circular" [ngClass]="['salesStatus_' + this.tempOverviewDetails.sales_status_id]">

                                                    </div>
                                                </div>
                                            </ng-container>
                                            <div class="col-6 opportunity-info-title">
                                                {{ 'salesStatus' | tenantOpportunity : formFieldData : 'label'}}
                                            </div>
                                            <!-- interActiveInlineEdit  (click)="activateInlineEdit('Salesstatus', 'minimal-dropdown',salesStatusMaster,'')" -->
                                            <div class="col-5 opportunity-info-details" style="cursor: pointer;" interActiveInlineEdit  
                                                [ngStyle]="{'pointer-events' : isDisableEdit || !oppEditAccess ? 'none' : ''}"
                                                (click)="activateInlineEdit('Salesstatus', 'minimal-dropdown',salesStatusMaster,'')" 
                                                [matTooltip]="overviewDetails?.sales_status_name ? overviewDetails?.sales_status_name : '-'"
                                            >
                                                {{this.tempOverviewDetails?.sales_status_name?this.tempOverviewDetails?.sales_status_name:'-'}}
                                            </div>
                                        </div>
                                        <div class="row pl-3 pt-2" *ngIf="'proposalStatus'| tenantOpportunity : formFieldData : 'isActive'"> 
                                            <div class="col-1">
                                                <div class="status-circular-proposal" [ngClass]="[
                      'proposalStatus_' + this.tempOverviewDetails.proposal_status_id
                    ]">

                                                </div>
                                            </div>
                                            <div class="col-6 opportunity-info-title">
                                                {{ 'proposalStatus' | tenantOpportunity : formFieldData : 'label'}}
                                            </div>
                                            <!-- interActiveInlineEdit  (click)="activateInlineEdit('Presalesstatus', 'minimal-dropdown',preSalesStatusMaster,'')" -->
                                            <div class="col-5 opportunity-info-details" interActiveInlineEdit  
                                                [ngStyle]="{'pointer-events' : isDisableEdit || !oppEditAccess ? 'none':( 'proposalStatus' | tenantOpportunity : formFieldData : 'isDisabled'  ) ? 'none' : ''}"
                                                (click)="activateInlineEdit('Presalesstatus', 'minimal-dropdown',preSalesStatusMaster,'')"
                                            style="cursor: pointer;">
                                                {{this.tempOverviewDetails?.proposal_status_name?this.tempOverviewDetails?.proposal_status_name:'-'}}
                                            </div>
                                        </div>
                                        <div class="row pl-3 pt-2">
                                            <div class="col-1">
                                                <span>
                                                    <mat-icon class="opportunity-info-icons">
                                                        event
                                                    </mat-icon>
                                                </span>
                                            </div>
                                            <div class="col-6 opportunity-info-title">
                                                Opportunity created on
                                            </div>
                                            <div class="col-5 opportunity-info-details" [matTooltip]="overviewDetails?.created_on ? (overviewDetails?.created_on | dynamicDatePipe) : '-'">
                                                {{this.overviewDetails?.created_on?(this.overviewDetails?.created_on|dynamicDatePipe):'-'}}
                                            </div>
                                        </div>
                                        <div class="row pl-3 pt-2">
                                            <div class="col-1">
                                                <span>
                                                    <mat-icon class="opportunity-info-icons">
                                                        event
                                                    </mat-icon>
                                                </span>
                                            </div>
                                            <div class="col-6 opportunity-info-title">
                                                Opportunity updated on
                                            </div>
                                            <div class="col-5 opportunity-info-details" [matTooltip]="overviewDetails?.updated_on ? (overviewDetails?.updated_on | dynamicDatePipe) : '-'">
                                                {{this.overviewDetails?.updated_on?(this.overviewDetails?.updated_on|dynamicDatePipe):'-'}}
                                            </div>
                                        </div>
                                        <div class="row pl-3 pt-2" *ngIf="reasonPresent && reason">
                                            <div class="col-1">
                                                <span>
                                                    <mat-icon class="opportunity-info-icons">
                                                        insert_comment
                                                    </mat-icon>
                                                </span>
                                            </div>
                                            <div class="col-6 opportunity-info-title">
                                                Reason
                                            </div>
                                            <div class="col-5 opportunity-info-details" [matTooltip]="this.reason.reason_name">
                                                {{this.reason.reason_name}}
                                            </div>
                                        </div>
                                        <div class="row pl-3 pt-2" *ngIf="reasonPresent && reason">
                                            <div class="col-1">
                                                <span>
                                                    <mat-icon class="opportunity-info-icons">
                                                        remove_circle_outline
                                                    </mat-icon>
                                                </span>
                                            </div>
                                            <div class="col-6 opportunity-info-title">
                                                Comments
                                            </div>
                                            <div class="col-5 opportunity-info-details" [matTooltip]="this.reason.comments">
                                                {{this.reason.comments}}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </ng-container>
                </div>
            </div>
        </div>
        <div class="col-7 pl-0">
            <div class="row">
                <div class="col-12">
                    <ng-container *ngIf="overviewDetails">
                        <div  *ngIf="projectViewAccess" class="projectView">
                            <div  class="row tileName pt-1 pb-1" style="display: flex; justify-content: space-between; align-items: center;" #projectList>
                              <div >Projects</div>
                              <!-- <div *ngIf="opportunityProjectDetails.length == 0 && projectCreationAccess" matTooltip="Create Project" (click)="createProject()" class="create-btn-data-screen">+ Create Project</div> -->
                            </div>
                            <ng-container *ngIf="opportunityProjectDetails">
                                <div
                                  class="row pt-2 pb-2 slide-from-down"
                                  *ngIf="opportunityProjectDetails.length != 0"
                                >
                                  <div class="col-12 pl-0">
                                    <div class="row">
                                      <div class="col-3">Project Code </div>
                                      <div class="col-4">Project Name </div>
                                      <div class="col-4">Project Status </div>
                                      <div class="col-1"></div>
                                    </div>
                                  </div>
                                </div>
                              </ng-container>
                              <ng-container *ngIf="opportunityProjectDetails">
                                <div class="row slide-from-down" >
                                  <div class="col-12 pl-0 pr-0">
                                    <div
                                      class="card listcard"
                                      *ngFor="let op of opportunityProjectDetails"
                                      style="border-left: 4px solid #833471"
                                      (click)="projectClicked(op)"
                                    >
                                      <div class="card-body p-2">
                                        <div
                                          class="row"
                                          *ngIf="this.opportunityProjectDetails.length != 0"
                                        >
                                          <div class="col-3 account-sub-headings pl-1 overflow" [matTooltip]="op.project_code">
                                            {{ op.project_code }}
                                          </div>
                                          <div class="col-4 account-sub-headings pl-1 overflow "  [matTooltip]="op.item_name">
                                            {{ op.item_name }}
                                          </div>
                                          <div class="col-4 account-info-details">
                                            <div class="row">
                                              <div class="col-1 pl-0 pr-0">
                                                <div
                                                  class="status-circular" [ngStyle]="{ 'background-color': op.status_color }"
                                                ></div>
                                              </div>
                                              <div class="col-8">{{ op.status}}</div>
                                            </div>
                                          </div>
                                          <div *ngIf="op?.at_risk === 1" class="col-1 if-at-risk">
                                              At Risk!
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                  <div
                                    class="col-12" style="display: flex; flex-direction: column; align-items: center;"
                                    *ngIf="this.opportunityProjectDetails.length == 0  && !projectLoad"
                                  >
                                    <div *ngIf="!projectView">
                                      <p
                                        class="empty-styles d-flex justify-content-center align-items-center m-0"
                                      >
                                        No Project found !
                                      </p>
                                    </div> 
                                    <!-- <div class="d-flex justify-content-center align-items-center">
                                      <img
                                        src="https://assets.kebs.app/images/void.png"
                                        height="190"
                                        width="190"
                                      />
                                    </div> -->
                                    <!-- <div  *ngIf="projectCreationAccess"  (click)="createProject()" class="create-btn-empty-screen">
                                      + Create Project
                                    </div> -->
                        
                                    <div *ngIf="projectView"
                                    (click)="projectCreationAccess ? createProject() :''"
                                    [ngClass]="projectCreationAccess ? 'create-btn-empty-screen' : 'dis-create-btn-empty-screen' "
                                    [disabled]="!projectCreationAccess"
                                    [matTooltip]="
                                    !projectCreationAllowedBasedOnAccount
                                      ? projectCreationText
                                      : (
                                          !projectCreationAccess
                                            ? (
                                                (overviewDetails?.parent_opportunity != null 
                                                  && overviewDetails?.parent_opportunity !== '' 
                                                  && overviewDetails?.parent_opportunity !== undefined) 
                                                  && childRestriction
                                                )
                                              ? projectCreationText
                                              : (!isActiveQuoteExists 
                                                  ? 'Active Quote is Mandatory For Project Creation' 
                                                  : 'Project cannot be created in this opportunity stage.'
                                                )
                                            : 'Create Project'
                                        )
                                  "
                                   
                                    >
                                    + Create Project
                                    </div>
                                  </div>
                                </div>
                              </ng-container>
                          </div>
                          <div  *ngIf="overviewDetails && !revenueLoad && checkFieldShouldbeRestricted('totalRevenue')" class="revenueView">
                            <div  class="row tileName pt-3 pb-1" style="display: flex; justify-content: space-between; align-items: center;" >
                              <div >Revenue details</div>
                              <!-- <div *ngIf="opportunityProjectDetails.length == 0 && projectCreationAccess" matTooltip="Create Project" (click)="createProject()" class="create-btn-data-screen">+ Create Project</div> -->
                            </div>
                            <ng-container *ngIf="overviewDetails">
                              <div
                                class="row pt-2 pb-2 slide-from-down"
                              >
                                <div class="col-12 pl-0">
                                    <div class="row">
                                        <div *ngIf="revenueConfig?.fy?.show !== false" class="{{ revenueConfig?.fy?.class || 'col-5' }}">
                                          {{ revenueConfig?.fy?.value || 'FY' }}
                                        </div>
                                      
                                        <div *ngIf="revenueConfig?.value?.show !== false" class="{{ revenueConfig?.value?.class || 'col-4' }}">
                                          {{ revenueConfig?.value?.value || 'Value' }}
                                        </div>
                                      
                                        <div *ngIf="revenueConfig?.cost?.show === true" class="{{ revenueConfig?.cost?.class || 'col-3' }}">
                                          {{ revenueConfig?.cost?.value || 'Cost' }}
                                        </div>
                                      
                                        <div *ngIf="revenueConfig?.gm?.show === true" class="{{ revenueConfig?.gm?.class || 'col-2' }}">
                                          {{ revenueConfig?.gm?.value || 'GM' }}
                                        </div>
                                      
                                        <div *ngIf="revenueConfig?.currencyCode?.show !== false" class="{{ revenueConfig?.currencyCode?.class || 'col-3' }}">
                                          {{ revenueConfig?.currencyCode?.value || 'Currency' }}
                                        </div>
                                    </div>
                            
                                </div>
                              </div>
                            </ng-container>
                            <ng-container >
                              <div  *ngIf="isActiveQuoteExists && oppFYdata.length>0  && !revenueLoad" class="revenueScrollView row slide-from-down" >
                                <div class="col-12 pl-0 pr-0">
                                  <div
                                    class="card listcard"
                                    *ngFor="let op of oppFYdata"
                                  >
                                    <div class="card-body p-2">
                                        <div class="row" *ngIf="oppFYdata.length !== 0">
  
                                            <div *ngIf="revenueConfig?.fy?.show !== false" class="{{ revenueConfig?.fy?.class || 'col-5' }} account-sub-headings  overflow" [matTooltip]="op.fy_label">
                                              {{ op.fy_label }}
                                            </div>
                                          
                                            <div *ngIf="revenueConfig?.value?.show !== false" class="{{ revenueConfig?.value?.class || 'col-4' }} account-item-headings overflow" [matTooltip]="op.revenue">
                                              {{ op.revenue }}
                                            </div>
                                          
                                            <ng-container
                                            *ngIf=" !checkFieldShouldbeMasked('totalCost');">
                                                <div *ngIf="revenueConfig?.cost?.show === true"
                                                    class="{{ revenueConfig?.cost?.class || 'col-4' }} account-item-headings overflow" [matTooltip]="op.cost">
                                                    {{ op.cost }}
                                                </div>
                                            
                                        </ng-container>
                                        <ng-container
                                            *ngIf="checkFieldShouldbeMasked('totalCost');">
                                            <div class="{{ revenueConfig?.cost?.class || 'col-4' }} account-item-headings overflow" *ngIf="revenueConfig?.cost?.show === true">
                                                {{ masking_configuration.maskDisplay || '****' }}
                                            </div>
                                        </ng-container>

                                            <ng-container
                                                *ngIf=" !checkFieldShouldbeMasked('totalGM');">
                                                    <div *ngIf="revenueConfig?.gm?.show === true"
                                                        class="{{ revenueConfig?.gm?.class || 'col-4' }} account-item-headings overflow" [matTooltip]="op.gm">
                                                        {{ op.gm }}
                                                    </div>
                                                
                                            </ng-container>
                                            <ng-container
                                                *ngIf="checkFieldShouldbeMasked('totalGM');">
                                                <div class="{{ revenueConfig?.gm?.class || 'col-4' }} account-item-headings overflow" *ngIf="revenueConfig?.gm?.show === true">
                                                    {{ masking_configuration.maskDisplay || '****' }}
                                                </div>
                                            </ng-container>

                                          
                                            <div *ngIf="revenueConfig?.currencyCode?.show !== false" class="{{ revenueConfig?.currencyCode?.class || 'col-3' }} account-item-headings overflow" [matTooltip]="op.currency_code">
                                              {{ op.currency_code }}
                                            </div>
                                          
                                          </div>
                                          
                                    </div>
                                  </div>
                                </div>
                              </div>
                              <div  *ngIf="oppFYdata.length==0 && overviewDetails && formFieldDataExist && !revenueLoad" class="revenueScrollView row  slide-from-down" >
                                <div class="col-12 pl-0 pr-0 ">
                                  <div
                                  class="card revenuelistcard"
                                >
                                  <div class="card-body p-2">
                                    <div
                                      class="row"
                                    >
                                      <div class="col-5 account-sub-headings pl-1 overflow" >
                                        {{ 'uobCurrentFYValue1' | tenantOpportunity : formFieldData : 'label'}}
                                      </div>
                                      <div class="col-4 account-item-headings  pl-1 overflow "  [matTooltip]="tempOverviewDetails.uobCurrentFYValue1">
                                        {{  this.tempOverviewDetails?.uobCurrentFYValue1 ?  this.tempOverviewDetails.uobCurrentFYValue1 : "-" }}
                                      </div>
                                      <div class="col-3 account-item-headings pl-3 overflow "  [matTooltip]="tempOverviewDetails.currency_code">
                                         {{  this.tempOverviewDetails?.currency_code}}
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div
                                class="card revenuelistcard"
                              >
                                <div class="card-body p-2">
                                  <div
                                    class="row"
                                  >
                                    <div class="col-5 account-sub-headings pl-1 overflow" >
                                        {{ 'uobCurrentFYValue2' | tenantOpportunity : formFieldData : 'label'}}
                                    </div>
                                    <div class="col-4 account-item-headings  pl-1 overflow "  [matTooltip]="tempOverviewDetails.uobCurrentFYValue2">
                                      {{ this.tempOverviewDetails?.uobCurrentFYValue2  ? this.tempOverviewDetails.uobCurrentFYValue2 : "-" }}
                                    </div>
                                    <div class="col-3 account-item-headings pl-3 overflow "  [matTooltip]="tempOverviewDetails.currency_code">
                                       {{ this.tempOverviewDetails?.currency_code}}
                                    </div>
                                  </div>
                                </div>
                              </div>
                              <div
                              class="card revenuelistcard"
                            >
                              <div class="card-body p-2">
                                <div
                                  class="row"
                                >
                                  <div class="col-5 account-sub-headings pl-1 overflow" >
                                    {{ 'uobCurrentFYValue3' | tenantOpportunity : formFieldData : 'label'}}
                                  </div>
                                  <div class="col-4 account-item-headings  pl-1 overflow "  [matTooltip]="tempOverviewDetails.uobCurrentFYValue3">
                                    {{ this.tempOverviewDetails?.uobCurrentFYValue3  ? this.tempOverviewDetails.uobCurrentFYValue3 : "-" }}
                                  </div>
                                  <div class="col-3 account-item-headings pl-3 overflow "  [matTooltip]="tempOverviewDetails.currency_code">
                                     {{ this.tempOverviewDetails?.currency_code}}
                                  </div>
                                </div>
                              </div>
                            </div>
                            <div
                            class="card revenuelistcard"
                          >
                            <div class="card-body p-2">
                              <div
                                class="row"
                              >
                                <div class="col-5 account-sub-headings pl-1 overflow" >
                                    {{ 'uobCurrentFYValue4' | tenantOpportunity : formFieldData : 'label'}}
                                </div>
                                <div class="col-4 account-item-headings  pl-1 overflow "  [matTooltip]="tempOverviewDetails.uobCurrentFYValue4">
                                  {{ this.tempOverviewDetails?.uobCurrentFYValue4   ? this.tempOverviewDetails.uobCurrentFYValue4 : "-"}}
                                </div>
                                <div class="col-3 account-item-headings pl-3 overflow "  [matTooltip]="tempOverviewDetails.currency_code">
                                   {{ this.tempOverviewDetails?.currency_code}}
                                </div>
                              </div>
                            </div>
                          </div>
                          <div
                          class="card revenuelistcard"
                        >
                          <div class="card-body p-2">
                            <div
                              class="row"
                            >
                              <div class="col-5 account-sub-headings pl-1 overflow" >
                                {{ 'uobCurrentFYValue5' | tenantOpportunity : formFieldData : 'label'}}
                              </div>
                              <div class="col-4 account-item-headings  pl-1 overflow "  [matTooltip]="tempOverviewDetails.uobCurrentFYValue5">
                                {{ this.tempOverviewDetails?.uobCurrentFYValue5 ? this.tempOverviewDetails.uobCurrentFYValue5 : "-"}}
                              </div>
                              <div class="col-3 account-item-headings pl-3 overflow "  [matTooltip]="tempOverviewDetails.currency_code">
                                 {{ this.tempOverviewDetails?.currency_code}}
                              </div>
                            </div>
                          </div>
                        </div>
                                </div>
                              </div>
                            </ng-container>
                          </div>
                        <div *ngIf="('winLossSection'| tenantOpportunity : formFieldData : 'isActive') && overviewDetails && overviewDetails?.win_loss_details && (tempOverviewDetails?.sales_status_id == 13 || tempOverviewDetails?.sales_status_id == 17)" class="revenueView">
                            <div class="row tileName pt-3 pb-1" style="display: flex; justify-content: space-between; align-items: center">
                                <div>{{ 'winLossSection' | tenantOpportunity : formFieldData : 'label' || "Win Loss Details"}}</div>
                            </div>
                            <ng-container *ngIf="overviewDetails.win_loss_details">
                                <div class="row pt-2 pb-2 slide-from-down" *ngIf="overviewDetails.win_loss_details?.length != 0">
                                    <div class="col-12 pl-0">
                                        <div class="row">
                                            <div class="col-3">Win Loss Status</div>
                                            <div class="col-6">Remarks</div>
                                            <div class="col-3">Created By</div>
                                        </div>
                                    </div>
                                </div>
                            </ng-container>
                            <ng-container *ngIf="overviewDetails.win_loss_details">
                                <div class="row slide-from-down">
                                    <div class="col-12 pl-0 pr-0">
                                        <div class="card listcard"
                                            [ngStyle]="{'border-left': overviewDetails.win_loss_details?.win_loss_id === 1 ? '4px solid #009432' : '4px solid #FA541C'}">
                                            <div class="card-body p-2">
                                                <div class="row" *ngIf="overviewDetails.win_loss_details">
                                                    <div class="col-3 account-sub-headings pl-1 overflow"
                                                        [matTooltip]="overviewDetails.win_loss_details.win_or_loss">
                                                        {{ overviewDetails.win_loss_details.win_or_loss }}
                                                    </div>
                                                    <div class="col-6 account-item-headings pl-1 overflow"
                                                        [matTooltip]="overviewDetails.win_loss_details.win_loss_remark">
                                                        {{ overviewDetails.win_loss_details.win_loss_remark || '-'}}
                                                    </div>
                                                    <div class="col-3 account-item-headings pl-1 overflow">
                                                        {{ overviewDetails.win_loss_details.created_by_name || '-'}}
                                                        <!-- <app-user-profile type="name"
                                                        oid="{{this.tempOverviewDetails?.win_loss_details?.created_by ?this.tempOverviewDetails?.win_loss_details?.created_by:''}}">
                                                    </app-user-profile> -->
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-12" style="display: flex; flex-direction: column; align-items: center"
                                        *ngIf="this.overviewDetails.win_loss_details.length == 0  && !projectLoad">
                                        <div *ngIf="!projectView">
                                            <p class="empty-styles d-flex justify-content-center align-items-center m-0">
                                                No Win Loss Details Found !
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </ng-container>
                        </div>
                        <div *ngIf="('plannedHoursSection'| tenantOpportunity : formFieldData : 'isActive')">
                            <span class="tileName pl-1 pt-2" >
                           {{ 'plannedHoursSection' | tenantOpportunity : formFieldData : 'label' ||  "Planned Presales Hours"}}
                       </span>
                       <div class="card d-flex slide-in-top">
                           <div class="card-body p-1" style="font-size: 14px;" >
                               <div class="row pt-2" >
                                   <div class="col-5 opportunity-info-details">
                                       Planned hours for this opportunity
                                   </div>
                                   <div class="col-3" *ngIf="!is_workflow">
                                       <div class="row">
                                           <div class="col-12 planned-hours-sub-headings  mr-5" style="cursor: pointer;" matTooltip="Changing planned hours will trigger workflow" interActiveInlineEdit  
                                               [ngStyle]="{'pointer-events' : isDisableEdit || !oppEditAccess ? 'none' : ''}"
                                               (click)="activateInlineEdit('Planned Presales Hours', 'simple-text', [],overviewDetails)">
                                               {{this.tempOverviewDetails?.planned_presales_hours?this.tempOverviewDetails?.planned_presales_hours:'0'}}  Hrs
                                           </div>
                                       </div>
                                   </div>
                                   <div class="col-3" *ngIf="is_workflow">
                                       <div class="row">
                                           <div class="col-12 planned-hours-sub-headings  mr-5" style="cursor: pointer;" matTooltip="Approval intiated! Cannot change Planned hours" >
                                               {{this.tempOverviewDetails?.planned_presales_hours?this.tempOverviewDetails?.planned_presales_hours:'0'}}  Hrs
                                           </div>   
                                       </div>
                                   </div>
                                   <div class="col-1" *ngIf="!is_workflow">

                                       <span style="cursor: pointer;" class="mt-3"  matTooltip="Changing planned hours will trigger workflow" interActiveInlineEdit  
                                           [ngStyle]="{'pointer-events' : isDisableEdit || !oppEditAccess ? 'none' : ''}"
                                           (click)="activateInlineEdit('Planned Presales Hours', 'simple-text', [],overviewDetails)" >
                                           <mat-icon class="smallCardIcon">edit</mat-icon>
                                       </span>
                                   </div>
                                   <div class="col-1" *ngIf="is_workflow">
                                       <span style="cursor: pointer;" class="mt-3"  matTooltip="Approval intiated! Cannot change Planned hours"  >
                                           <mat-icon class="smallCardIcon">edit</mat-icon>
                                       </span>
                                   </div>
                                   <div class="col-1">
                                       <span matTooltip="View approvers" style="cursor: pointer;" class="mt-3" (click)="showPresalesApprovers()" >
                                           <mat-icon [ngClass]="{'smallCardIcon':!openApprover, 'invertedsmallCardIcon':openApprover}">supervised_user_circle</mat-icon>
                                       </span>
                                   </div>
                                   <div class="col-1">
                                       <span matTooltip="View approval history" style="cursor: pointer;" class="mt-3" (click)="showHistory()" >
                                           <mat-icon [ngClass]="{'smallCardIcon':!openHistory, 'invertedsmallCardIcon':openHistory}">history</mat-icon>
                                       </span>
                                   </div>
                                   <div class="col-1">
                                       <span matTooltip="View comments" style="cursor: pointer;" class="mt-3" (click)="showComments()" >
                                           <mat-icon [ngClass]="{'smallCardIcon':!openComment, 'invertedsmallCardIcon':openComment}"> comment</mat-icon>
                                       </span>
                                   </div>
                               </div>
                               <div *ngIf="!openHistory && !openApprover &&!openComment">
                                   <div *ngIf="is_workflow" style=" padding-top: 10px; padding-bottom: 10px;  ">
                                       <div class="card slide-in-top m-3" style="background-color: #FFE8C7 !important;">
                                           <div class="card-body" style="font-size: 14px; padding-left:14px;" >
                                               <div class="row" >
                                                   <div class="col-1 pt-1 pl-0 info-icon" >
                                                       <mat-icon class="my-auto" style="font-size: 15px;color :#b94141; padding-top: 5px">
                                                           info
                                                       </mat-icon>
                                                   
                                                   </div>
                                                   <div class="col-11 pl-0">
                                                       <div class="row pt-2">
                                                           <div class="opportunity-info-details">Planned hours changed in this opportunity. <span *ngIf="!is_approver">Yet to be approved!</span><span *ngIf="is_approver">Awaiting your approval!</span></div>
                                                       </div>
                                                       <div class="row pt-2 pr-0">
                                                           <div class="col-5 pl-0 opportunity-info-details">
                                                               Changed planned hours
                                                           </div>
                                                           <div class="col-6 planned-hours-sub-headings">
                                                               {{presales_changed_hours}} Hrs
                                                           </div>
                                                       </div>
                                                       <div class="row pt-2 pr-0">
                                                           <div class="col-5 pl-0  opportunity-info-details">
                                                               Changed by
                                                           </div>
                                                           <div class="col-6 planned-hours-sub-headings">
                                                               {{presales_hours_changed_by}}
                                                           </div>
                                                       </div>
                                                       <div class="row pt-2 pr-0">
                                                           <div class="col-5 pl-0  opportunity-info-details">
                                                               Changed on
                                                           </div>
                                                           <div class="col-6 planned-hours-sub-headings">
                                                               {{presales_hours_changed_on}}
                                                           </div>
                                                       </div>
                                                       <div class="row pt-2 pr-0" *ngIf="is_approver">
                                                       
                                           
                                                               <div class="col-5 pl-0  opportunity-info-details">
                                                                   Your Comments
                                                               </div>
                                                               <div class="col-6" [matTooltip]="comments_approvers?comments_approvers:'-'" [ngClass]="{'planned-hours-sub-headings':is_comments, 'opportunity-info-title': !is_comments}" style="cursor: pointer; " interActiveInlineEdit  
                                                                   [ngStyle]="{'pointer-events' : isDisableEdit || !oppEditAccess ? 'none' : ''}"
                                                                   (click)="activateInlineEdit('Planned hours comments','simple-text',[],comments_approvers)">
                                                                   {{comments_approvers?(comments_approvers | lengthfix:30):'Enter your comments'  }}
                                                               </div>
                                                       
                                                       </div>
                                                       <div class="d-flex pt-3 pr-0" *ngIf="!is_approver">
                                                           <div class="col-5 pl-0  opportunity-info-details">
                                                               Approver
                                                           </div>
                                                           <div class="col-6 planned-hours-sub-headings" >
                                                               <div class="row d-flex">
                                                                   <div class="col-1 mr-2" *ngFor="let app of presales_hours_approver">
                                                                       <!-- <span >{{app.name}}  </span> -->
                                                                       <app-user-image [tooltip]="approverTooltip" content-type="template" style="margin: px;"
                                                                               [id]="app.oid" imgHeight="32px" imgWidth="32px" borderStyle="solid"
                                                                               borderWidth="2px" [borderColor]="borderStatusColor(this.app.status)">
                                                                       </app-user-image>
                                                                       <ng-template #approverTooltip>
                                                                       {{app.name}}<br>
                                                                       {{app.level}}<br>
                                                                       {{app.designation}}
                                                                   
                                                                       </ng-template>
                                                                   </div>
                                                               </div>
                                                           </div>
                                                       </div>
                                                   </div>
                                               </div>
                                               
                                           </div>
                                       </div>
                                   
                                       <div *ngIf="is_approver">
                                           
                                           <div class="row pt-2 pr-0" style="padding-left: 20px">
                                           
                                               <div class="col-6 pl-4  opportunity-info-details">
                                               Your desicion
                                               </div>
                                               <div class="col-6 planned-hours-sub-headings pl-0">
                                                   <div class="row">
                                                       <div class="col-3 pl-0">
                                                           <button mat-icon-button class="approve-btn mr-4" matTooltip="Approve" (click)="approveChange()">
                                                               <mat-icon style="color: white !important; font-size: 18px !important">done</mat-icon>
                                                           </button>
                                                       </div>
                                                       <div class="col-3 pl-0">
                                                           <button mat-icon-button class="reject-btn" matTooltip="Reject" (click)="rejectChange()">
                                                               <mat-icon style="color:white !important; font-size: 21px !important; margin-bottom: 1px;">
                                                               highlight_off
                                                               </mat-icon>
                                                           </button>
                                                       </div>
                                                       
                                                   </div>
                                               </div>
                                           </div>
                                       </div>
                                   </div>
                               </div>
                               <div *ngIf="openHistory"  style=" padding-top: 10px; padding-bottom: 10px;">
                                   <div class="row pt-2 border-bottom solid">
                                       <div class="col-12">
                                           <div class="row pt-2">
                                               <div class="col-1 pl-0 info-icon" (click)="showHistory()">
                                                   <mat-icon class="smallCardIcon" style="cursor: pointer;">arrow_back</mat-icon>
                                               </div>
                                               <div class="col-11 pl-0">
                                                   <div class="planned-hours-sub-headings">History</div>
                                               </div>
                                           </div>   
                                       </div>
                                   </div>
                                   <cdk-virtual-scroll-viewport
                                   itemSize="20"
                                   minBufferPx="75"
                                   maxBufferPx="200"
                                   class="example-viewport"
                               >
                                   <div *ngIf="planned_presale_logs!=undefined">
                                       <div *cdkVirtualFor="let item of planned_presale_logs?.slice()?.reverse();
                                                           templateCacheSize: 0;
                                                           let i = index" 
                                           class="container-fluid" >
                                           <div class="row pt-2 pb-2 border-bottom solid d-flex flex-wrap" style="font-size: 12px">
                                               
                                           
                                               
                                                       {{item.history}}
                                                   
                                               
                                           </div>
                                       </div>
                                   </div>

                                   <div *ngIf="planned_presale_logs==undefined || planned_presale_logs==null ">
                                       <div style="position: absolute;top: 42%;left: 39%;font-size: 18px;">
                                           <p>No History Found !</p>
                                       </div>
                                   </div>
                               </cdk-virtual-scroll-viewport>
                               </div>
                               <div *ngIf="openApprover"  style=" padding-top: 10px; padding-bottom: 10px;">
                                   <div class="row pt-2 border-bottom solid">
                                       <div class="col-12">
                                           <div class="row pt-2">
                                               <div class="col-1 pl-0 info-icon" (click)="showPresalesApprovers()">
                                                   <mat-icon class="smallCardIcon" style="cursor: pointer;">arrow_back</mat-icon>
                                               </div>
                                               <div class="col-11 pl-0">
                                                   <div class="planned-hours-sub-headings">Approvers</div>
                                               </div>
                                           </div>   
                                       </div>
                                   </div>
                                   <cdk-virtual-scroll-viewport
                                   itemSize="20"
                                   minBufferPx="75"
                                   maxBufferPx="200"
                                   class="example-viewport"
                               >
                    >         <div *ngIf="presales_approvers?.length!=0">
                                       <div *cdkVirtualFor="let item of presales_approvers;
                                                           templateCacheSize: 0;
                                                           let i = index" 
                                           class="container-fluid" >
                                           <div class="row pt-2 pb-2 border-bottom solid d-flex flex-wrap" style="font-size: 12px">
                                               <div class="col-1 pt-3">
                                                   <mat-icon class="smallCardIcon">person_pin</mat-icon>
                                               </div>
                                               <div class="col-10">
                                                   <div class="row">
                                                       <div class="col-3 opportunity-info-title">
                                                           Name
                                                       </div>
                                                       <div class="col-7 opportunity-info-details">
                                                           {{item.name}}
                                                       </div>
                                                   </div>
                                       
                                                   <div class="row">
                                                       <div class="col-3 opportunity-info-title">
                                                           Level
                                                       </div>
                                                       <div class="col-7 opportunity-info-details">
                                                           {{item.level}}
                                                       </div>
                                                   </div>

                                                   
                                       
                                                   <div class="row">
                                                       <div class="col-3 opportunity-info-title">
                                                           Designation
                                                       </div>
                                                       <div class="col-7 opportunity-info-details">
                                                           {{item.designation}}
                                                       </div>
                                                   </div>
                                               </div>
                                                   
                                               
                                           </div>
                                       </div>
                                   </div>
                                   <div *ngIf="presales_approvers?.length==0">
                                       <div style="position: absolute;top: 40%;left: 35%;font-size: 18px;">
                                           <p>Oops ! No approvers found !</p>
                                       </div>
                                   </div>
                               </cdk-virtual-scroll-viewport>
                               </div>
                               <div *ngIf="openComment" style="padding-top: 10px; padding-bottom: 10px;">
                                   <app-chat-comment [commentBoxHeight]='commentBoxHeight' [commentBoxScrollHeight]='commentBoxScrollHeight' [comments] = 'planned_presales_hours_comment' (sendComments) = 'getComments($event)'></app-chat-comment>
                               </div>
                           </div>
                       </div>
                         </div>
                    </ng-container>
                </div>
            </div>
            <div class="row" style="padding-top: 20px;" *ngIf="this.overviewDetails?.bid_perc!=null && this.overviewDetails?.is_bid!=1">
                <div class="col-11">
                    <span class="tileName">
                        Bid Qualification Approval
                    </span>
                </div>
                <!-- <div class="col-1 pl-0">
                    <span class="tileName" *ngIf="overviewDetails">
                        <mat-icon class="smallCardIcon">info</mat-icon>
                    </span>
                </div> -->
                <div class="col-12">
                    <ng-container *ngIf="overviewDetails">
                        <div class="card d-flex slide-in-top">
                            <div class="card-body p-1" style="font-size: 14px;" >
                                <div class="row pt-2" >

                                    <div class="col-5 opportunity-info-details">
                                        Approval for No-Bid Opportunity
                                    </div>
                                    <div class="col-3">
                                        <div class="row">
                                            <div class="col-12 planned-hours-sub-headings  mr-5" >
                                                {{this.overviewDetails?.bid_perc?(this.overviewDetails?.bid_perc | number : '1.2-6'):'-'}} % 
                                                {{this.bidStatus}}
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-1" *ngIf="!is_bid_workflow && this.overviewDetails.bid_qualification_approval_status==1">
                                        <span matTooltip="Trigger Approval" style="cursor: pointer;" class="mt-3" (click)="triggerBidQualificationApproval()" >
                                            <mat-icon class="smallCardIcon">gavel</mat-icon>
                                        </span>
                                    </div>
                                    <div class="col-1" *ngIf="!is_bid_workflow && this.overviewDetails.bid_qualification_approval_status==3">
                                        <span matTooltip="No Bid Opportunity approval approved successfully! Now status can be changed" style="cursor: pointer;" class="mt-3">
                                            <mat-icon class="smallCardIcon">gavel</mat-icon>
                                        </span>
                                    </div>

                                    <div class="col-1" *ngIf="is_bid_workflow && this.overviewDetails.bid_qualification_approval_status==2">
                                       
                                    </div>
                                   
                                    <div class="col-1">
                                        <span matTooltip="View approvers" style="cursor: pointer;" class="mt-3" (click)="showBidQualificationApprovers()" >
                                            <mat-icon [ngClass]="{'smallCardIcon':!openBidApprover, 'invertedsmallCardIcon':openBidApprover}">supervised_user_circle</mat-icon>
                                        </span>
                                    </div>
                                    <div class="col-1">
                                        <span matTooltip="View approval history" style="cursor: pointer;" class="mt-3" (click)="showBidQualificationHistory()" >
                                            <mat-icon [ngClass]="{'smallCardIcon':!openBidHistory, 'invertedsmallCardIcon':openBidHistory}">history</mat-icon>
                                        </span>
                                    </div>
                                    <div class="col-1">
                                        <span matTooltip="View comments" style="cursor: pointer;" class="mt-3" (click)="showBidQualificationComments()" >
                                            <mat-icon [ngClass]="{'smallCardIcon':!openBidComment, 'invertedsmallCardIcon':openBidComment}"> comment</mat-icon>
                                        </span>
                                    </div>
                                </div>
                                <div *ngIf="!openBidHistory && !openBidApprover &&!openBidComment">
                                    <div *ngIf="is_bid_workflow" style=" padding-top: 10px; padding-bottom: 10px;  ">
                                        <div class="card slide-in-top m-3" style="background-color: #FFE8C7 !important;">
                                            <div class="card-body" style="font-size: 14px; padding-left:14px;" >
                                                <div class="row" >
                                                    <div class="col-1 pt-1 pl-0 info-icon" >
                                                        <mat-icon class="my-auto" style="font-size: 15px;color :#b94141; padding-top: 5px">
                                                            info
                                                        </mat-icon>
                                                    
                                                    </div>
                                                    <div class="col-11 pl-0">
                                                        <div class="row pt-2">
                                                            <div class="opportunity-info-details">No Bid Approval in this opportunity. <span *ngIf="!is_bid_approver">Yet to be approved!</span><span *ngIf="is_bid_approver">Awaiting your approval!</span></div>
                                                        </div>
                                                        <div class="row pt-2 pr-0">
                                                            <div class="col-5 pl-0 opportunity-info-details">
                                                                Bid Percentage
                                                            </div>
                                                            <div class="col-6 planned-hours-sub-headings">
                                                                {{this.overviewDetails?.bid_perc?(this.overviewDetails?.bid_perc | number : '1.2-6'):'-'}} % 
                                                                {{this.bidStatus}}
                                                            </div>
                                                        </div>
                                                        <div class="row pt-2 pr-0">
                                                            <div class="col-5 pl-0  opportunity-info-details">
                                                                Changed by
                                                            </div>
                                                            <div class="col-6 planned-hours-sub-headings">
                                                                {{bid_qualification_changed_by}}
                                                            </div>
                                                        </div>
                                                        <div class="row pt-2 pr-0">
                                                            <div class="col-5 pl-0  opportunity-info-details">
                                                                Changed on
                                                            </div>
                                                            <div class="col-6 planned-hours-sub-headings">
                                                                {{bid_qualification_changed_on}}
                                                            </div>
                                                        </div>

                                                        <div class="d-flex pt-3 pr-0" *ngIf="!is_bid_approver">
                                                            <div class="col-5 pl-0  opportunity-info-details">
                                                                Approver
                                                            </div>
                                                            <div class="col-6 planned-hours-sub-headings" >
                                                                <div class="row d-flex">
                                                                    <div class="col-1 mr-2" *ngFor="let app of bid_qualification_approver">
                                                                        <!-- <span >{{app.name}}  </span> -->
                                                                        <app-user-image [tooltip]="approverTooltip" content-type="template" style="margin: px;"
                                                                                [id]="app.oid" imgHeight="32px" imgWidth="32px" borderStyle="solid"
                                                                                borderWidth="2px" [borderColor]="borderStatusColor(this.app.status)">
                                                                        </app-user-image>
                                                                        <ng-template #approverTooltip>
                                                                        {{app.name}}<br>
                                                                        {{app.level}}<br>
                                                                        {{app.designation}}
                                                                    
                                                                        </ng-template>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                
                                            </div>
                                        </div>
                                    
                                        <div *ngIf="is_bid_approver">
                                            
                                            <div class="row pt-2 pr-0" style="padding-left: 20px">
                                            
                                                <div class="col-6 pl-4  opportunity-info-details">
                                                Your desicion
                                                </div>
                                                <div class="col-6 planned-hours-sub-headings pl-0">
                                                    <div class="row">
                                                        <div class="col-3 pl-0">
                                                            <button mat-icon-button class="approve-btn mr-4" matTooltip="Approve" (click)="approveNoBidApproval()">
                                                                <mat-icon style="color: white !important; font-size: 18px !important">done</mat-icon>
                                                            </button>
                                                        </div>
                                                        <div class="col-3 pl-0">
                                                            <button mat-icon-button class="reject-btn" matTooltip="Reject" (click)="rejectNoBidApproval()">
                                                                <mat-icon style="color:white !important; font-size: 21px !important; margin-bottom: 1px;">
                                                                highlight_off
                                                                </mat-icon>
                                                            </button>
                                                        </div>
                                                        
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div *ngIf="openBidHistory"  style=" padding-top: 10px; padding-bottom: 10px;">
                                    <div class="row pt-2 border-bottom solid">
                                        <div class="col-12">
                                            <div class="row pt-2">
                                                <div class="col-1 pl-0 info-icon" (click)="showBidQualificationHistory()">
                                                    <mat-icon class="smallCardIcon" style="cursor: pointer;">arrow_back</mat-icon>
                                                </div>
                                                <div class="col-11 pl-0">
                                                    <div class="planned-hours-sub-headings">History</div>
                                                </div>
                                            </div>   
                                        </div>
                                    </div>
                                    <cdk-virtual-scroll-viewport
                                    itemSize="20"
                                    minBufferPx="75"
                                    maxBufferPx="200"
                                    class="example-viewport"
                                >
                                    <div *ngIf="bid_qualification_logs!=undefined">
                                        <div *cdkVirtualFor="let item of bid_qualification_logs?.slice()?.reverse();
                                                            templateCacheSize: 0;
                                                            let i = index" 
                                            class="container-fluid" >
                                            <div class="row pt-2 pb-2 border-bottom solid d-flex flex-wrap" style="font-size: 12px">
                                                
                                            
                                                
                                                        {{item.history}}
                                                    
                                                
                                            </div>
                                        </div>
                                    </div>

                                    <div *ngIf="bid_qualification_logs==undefined || bid_qualification_logs==null ">
                                        <div style="position: absolute;top: 42%;left: 39%;font-size: 18px;">
                                            <p>No History Found !</p>
                                        </div>
                                    </div>
                                </cdk-virtual-scroll-viewport>
                                </div>
                                <div *ngIf="openBidApprover"  style=" padding-top: 10px; padding-bottom: 10px;">
                                    <div class="row pt-2 border-bottom solid">
                                        <div class="col-12">
                                            <div class="row pt-2">
                                                <div class="col-1 pl-0 info-icon" (click)="showBidQualificationApprovers()">
                                                    <mat-icon class="smallCardIcon" style="cursor: pointer;">arrow_back</mat-icon>
                                                </div>
                                                <div class="col-11 pl-0">
                                                    <div class="planned-hours-sub-headings">Approvers</div>
                                                </div>
                                            </div>   
                                        </div>
                                    </div>
                                    <cdk-virtual-scroll-viewport
                                    itemSize="20"
                                    minBufferPx="75"
                                    maxBufferPx="200"
                                    class="example-viewport"
                                >
                                    <div *ngIf="bid_qualification_approvers?.length!=0">
                                        <div *cdkVirtualFor="let item of bid_qualification_approvers;
                                                            templateCacheSize: 0;
                                                            let i = index" 
                                            class="container-fluid" >
                                            <div class="row pt-2 pb-2 border-bottom solid d-flex flex-wrap" style="font-size: 12px">
                                                <div class="col-1 pt-3">
                                                    <mat-icon class="smallCardIcon">person_pin</mat-icon>
                                                </div>
                                                <div class="col-10">
                                                    <div class="row">
                                                        <div class="col-3 opportunity-info-title">
                                                            Name
                                                        </div>
                                                        <div class="col-7 opportunity-info-details">
                                                            {{item.name}}
                                                        </div>
                                                    </div>
                                        
                                                    <div class="row">
                                                        <div class="col-3 opportunity-info-title">
                                                            Level
                                                        </div>
                                                        <div class="col-7 opportunity-info-details">
                                                            {{item.level}}
                                                        </div>
                                                    </div>

                                                    
                                        
                                                    <div class="row">
                                                        <div class="col-3 opportunity-info-title">
                                                            Designation
                                                        </div>
                                                        <div class="col-7 opportunity-info-details">
                                                            {{item.designation}}
                                                        </div>
                                                    </div>
                                                </div>
                                                    
                                                
                                            </div>
                                        </div>
                                    </div>
                                    <div *ngIf="bid_qualification_approvers?.length==0">
                                        <div style="position: absolute;top: 40%;left: 35%;font-size: 18px;">
                                            <p>Oops ! No approvers found !</p>
                                        </div>
                                    </div>
                                </cdk-virtual-scroll-viewport>
                                </div>
                                <div *ngIf="openBidComment" style="padding-top: 10px; padding-bottom: 10px;">
                                    <app-chat-comment [commentBoxHeight]='commentBoxHeight' [commentBoxScrollHeight]='commentBoxScrollHeight' [comments] = 'bid_qualification_comments' (sendComments) = 'getBidQualificationComments($event)'></app-chat-comment>
                                </div>
                            </div>
                        </div>
                    </ng-container>
                </div>
            </div>
            <div class="row" style="padding-top: 20px;" *ngIf="('teamsNotificationSection'| tenantOpportunity : formFieldData : 'isActive')">
                <div class="col-11">
                    <span class="tileName">
                        {{ 'teamsNotificationSection' | tenantOpportunity : formFieldData : 'label' ||  "Teams notification "}}
                    </span>
                </div>
                <!-- <div class="col-1 pl-0">
                    <span class="tileName" *ngIf="overviewDetails">
                        <mat-icon class="smallCardIcon">info</mat-icon>
                    </span>
                </div> -->
                <div class="col-12">
                    <ng-container *ngIf="overviewDetails">
                        
                        <div class="card d-flex slide-in-top" >
                            <div class="card-body p-1" style="font-size: 14px;" >
                                <div class="row pt-2 pb-2 border-bottom solid" *ngFor="let channel of dup_entered_Channel; let i=index">
                                    
                                    <div class="col-5 opportunity-info-details" style="cursor: pointer;"  interActiveInlineEdit  
                                        [ngStyle]="{'pointer-events' : isDisableEdit || !oppEditAccess ? 'none' : ''}"
                                        (click)="activateInlineEdit('channel type', 'minimal-dropdown',channel_type,'',i)">
                                       <span>
                                           {{channel.roles}}
                                       </span>
                                    </div>
                      
                                    <div class="col-5 planned-hours-sub-headings" style="cursor: pointer;" interActiveInlineEdit  
                                        [ngStyle]="{'pointer-events' : isDisableEdit || !oppEditAccess ? 'none' : ''}"
                                        (click)="activateInlineEdit('channel id','simple-text',[],dup_entered_Channel,i)">
                                        <span style="text-overflow: ellipsis;">
                                            {{channel.channel_id | lengthfix: 20}}
                                        </span>
                                    </div>
                                    
                                    <div class="col-1">
                                        
                                        <span matTooltip="Delete channel" style="cursor: pointer;" class="mt-3"  (click)="deleteChannel(i)">
                                            <mat-icon class="smallCardIcon">delete</mat-icon>
                                        </span>
                                       
                                    </div>
                                 </div>
                                <div class="row pt-2" >
                                    
                                    <div class="col-5 " [ngClass]="{'opportunity-info-details':channel_type_ex, 'opportunity-info-title': !channel_type_ex}" style="cursor: pointer;"  interActiveInlineEdit  
                                        [ngStyle]="{'pointer-events' : isDisableEdit || !oppEditAccess ? 'none' : ''}"
                                        (click)="activateInlineEdit('New channel type', 'minimal-dropdown',channel_type,'')">
                                       <span>
                                            {{channel_type_value?channel_type_value:'Choose channel type'}}
                                       </span>
                                    </div>
                                   
                                    <div class="col-5" [ngClass]="{'planned-hours-sub-headings':channel_id_type, 'opportunity-info-title': !channel_id_ex}" style="cursor: pointer; " interActiveInlineEdit  
                                        [ngStyle]="{'pointer-events' : isDisableEdit || !oppEditAccess ? 'none' : ''}"
                                        (click)="activateInlineEdit('New channel id','simple-text',[],channel_id_value)">
                                        <span >
                                            {{channel_id_value?(channel_id_value | lengthfix:20):'Enter Teams Channel Id'  }}
                                        </span>
                                    </div>
                                    <div class="col-1">
                                        <span matTooltip="Save channel" *ngIf="!is_save_channel_loading" style="cursor: pointer;" class="mt-3"  (click)="addSaveChannel()">
                                            <mat-icon class="smallCardIcon">save</mat-icon>
                                        </span>
                                        <span  *ngIf="is_save_channel_loading"  class="mt-3"  >
                                            <mat-spinner [diameter]="20"></mat-spinner>
                                        </span>
                                    </div>
                                    
                                 </div>
                            </div>
                                   
                        </div>
                    </ng-container>
                </div>
            </div>
            <div class="row" style="padding-top: 20px;" *ngIf="('scopeSection'| tenantOpportunity : formFieldData : 'isActive')">
                
                    <span class="tileName pl-3">
                        {{ 'scopeSection' | tenantOpportunity : formFieldData : 'label' ||  "Scope"}}
                    </span>
              
                <div class="col-12">
                    <div class="card d-flex slide-in-top">
                        <div class="card-body p-1" style="font-size: 14px;">
                            <div class="row pt-2">
                                <div class="col-12 opportunity-sub-headings">
                                    Services
                                </div>
                            </div>
                            <div class="row pt-2 pb-2 border-bottom solid slide-from-down">
                                <div class="col-12">
                                    <span
                                        *ngIf='overviewDetails?.services_json!=null && overviewDetails?.services_json!=""'
                                        [innerHTML]="overviewDetails?.services_json?(overviewDetails?.services_json[0].value):'' | safeHtml"></span>
                                    <span
                                        *ngIf='overviewDetails?.services_json==null || overviewDetails?.services_json==""'>
                                        Services Not Defined
                                    </span>
                                </div>
                            </div>
                            <div class="row pt-2">
                                <div class="col-12 opportunity-sub-headings">
                                    Solutions
                                </div>
                            </div>
                            <div class="row pt-2 pb-2 border-bottom solid slide-from-down">
                                <div class="col-12">
                                    <span
                                        *ngIf='overviewDetails?.solutions_json!=null && overviewDetails?.solutions_json!=""'
                                        [innerHTML]="overviewDetails?.solutions_json?(overviewDetails?.solutions_json[0].value):'' | safeHtml"></span>
                                    <span
                                        *ngIf='overviewDetails?.solutions_json==null || overviewDetails?.solutions_json==""'>
                                        Solutions Not Defined
                                    </span>
                                </div>

                            </div>
                            <div class="row pt-2">
                                <div class="col-12 opportunity-sub-headings">
                                    Modules
                                </div>
                            </div>
                            <div class="row pt-2 pb-2 slide-from-down">
                                <div class="col-12">
                                    <span
                                        *ngIf='overviewDetails?.modules_json!=null && overviewDetails?.modules_json!=""'
                                        [innerHTML]="overviewDetails?.modules_json?(overviewDetails?.modules_json[0].value):'' | safeHtml"></span>
                                    <span
                                        *ngIf='overviewDetails?.modules_json==null || overviewDetails?.modules_json==""'>
                                        Modules Not Defined
                                    </span>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>

            <div class="row pb-2" style="padding-top: 20px;" *ngIf="('serviceLine'| tenantOpportunity : formFieldData : 'isActive') && service_line.length != 0">
               
                <span class="tileName pl-3">
                    {{ 'serviceLine'| tenantOpportunity : formFieldData : 'label' }}
                </span>

                <div class="col-12">
                    <div class="card slide-in-top">
                    <div class="card-body p-3">
                    
                        <div class="row" *ngFor="let service of service_line">
                            <div class="col-1 pr-0">
                                <div class="status-circular-2"></div>
                            </div>
                            <div class="col-11 pl-2  opportunity-info-details">
                                {{serviceLineMaster[service]?.name}}
                            </div>
                        </div>

                    </div>
                    </div>
                </div>
            </div>
           
            <div class="row pb-2" style="padding-top: 20px;" *ngIf="module_list.length!=0">
               
                    <span class="tileName pl-3">
                        Modules
                    </span>
                
    
    
                <div class="col-12">
                    <div class="card slide-in-top">
                    <div class="card-body p-3">
                        
                        <div class="row" *ngFor="let module of module_list">
                            <div class="col-1 pr-0">
                                <div class="status-circular-2"></div>
                            </div>
                            <div class="col-11 pl-2  opportunity-info-details">
                            {{module}}
                            </div>
                        </div>
                        
                    </div>
                    </div>
                </div>
            </div>
           

            <div class="row pb-2" style="padding-top: 20px;" *ngIf="productCategory.length!=0 && line_of_business_access">
               
                <div class="col-12 d-flex">
                    <span class="tileName pl-1">
                        Line of Business
            
                    </span>
                
                    <button mat-icon-button matTooltip="View more" class="more-button ml-auto" [matMenuTriggerFor]="options">
                        <mat-icon class="smallCardIcon">more_vert</mat-icon>
                    </button>
                    <mat-menu #options="matMenu">
                        <button mat-menu-item class="drop-btn" (click)="viewLineofBusiness()">
                            <mat-icon class="smallCardIcon">business_center</mat-icon> <span>View heirarchy</span>
                        </button>
                    </mat-menu>
                </div>
               
                
               
    
    
                <div class="col-12">
                    <div class="card slide-in-top">
                    <div class="card-body p-3">
                      <div class="row">
                        <div class="col-1 pr-0">
                            
                        </div>
                        <div class="col-3 pl-2 planned-hours-sub-headings">
                            Product Name
                        </div>
                        <!-- <div class="col-2 pl-2 planned-hours-sub-headings">
                            Percent
                        </div> -->
                        <div class="col-2 pl-2 planned-hours-sub-headings">
                          Value
                        </div>
                        <div class="col-2 pl-2 planned-hours-sub-headings">
                            FTE Count
                        </div>
                        <div class="col-2 pl-2 planned-hours-sub-headings">
                            Shift
                        </div>
                        
                      </div>
                        <div class="row" *ngFor="let product of productCategory">
                            <div class="col-1 pr-0">
                                <div class="status-circular-1"></div>
                            </div>
                            <div class="col-3 pl-2 opportunity-info-details"
                                style="font-weight: 500; color:#cf0001; " [matTooltip]="product.name">
                                {{product.name}}
                            </div>
                            <!-- <div class="col-2 pl-2 opportunity-info-details" [matTooltip]="product.percent">
                                {{product.percent}}
                            </div> -->
                            <div class="col-2 pl-2 opportunity-info-details">
                              <app-currency
                              [currencyList]="product.currency_value"
                              class="flex-1" type="small"></app-currency>
                            </div>
                            <div class="col-2 pl-2 opportunity-info-details" [matTooltip]="product.FTE_Count">
                              {{product.FTE_Count}}
                            </div>
                            <div class="col-2 pl-2 opportunity-info-details" [matTooltip]="product.shiftName">
                                {{product.shiftName}}
                            </div>
                        </div>
                       
                    </div>
                    </div>
                </div>
            </div>
            <!---BANT REMOVED AS PER REQUIREMENT-->
            <!-- <div class="row pt-2">
        <div class="col-12">
          <span class="tileName pl-3">BANT</span>
        </div>
      </div>
      <div class="row pt-2" *ngIf='overviewDetails'>
        <div class="col-12">
          <div class="card d-flex slide-in-top">
            <div class="card-body p-2">
              <div class="row pt-2 pb-2 border-bottom solid">
                <div class="col-6 border-right solid counter">
                  <div class="row budget-row opportunity-sub-headings">
                    <span> Budget</span>
                  </div>
                  <div class="row">
                    <div class="col-12">
                      <app-animated-counter [digit]="overviewDetails.budget" [duration]="1000" class="counter-style">
                      </app-animated-counter>
                      <span class="tileName pl-4"> USD</span>
                    </div>
                  </div>
                </div>
                <div class="col-6  counter">
                  <div class="row budget-row opportunity-sub-headings">
                    <span> Time</span>
                  </div>
                  <div class="row">
                    <div class="col-12">
                      <app-animated-counter [digit]="overviewDetails.time" [duration]="2000" class="counter-style">
                      </app-animated-counter>
                      <span class="tileName pl-1" style="padding-left: 19px;">Days</span>
                    </div>
                  </div>
                </div>
              </div>
              <div class="row">
                <div class="col-12 border-bottom solid pb-3">
                  <div>
                    <div class="p-2">
                      <div class="row">
                        <div class="col-12 opportunity-sub-headings pb-2">
                          Authority
                        </div>
                      </div>
                      <div class="row">
                        <div class="col-4" *ngFor="let person of authority">
                          <app-user-image-chip
                            [name]="person.first_name?person.first_name:''+' '+person.last_name?person.last_name:''">
                          </app-user-image-chip>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div> -->   
    
    </div>
    </div>
    <!-- <crm-notes appId="36" [elementId]="elementId"></crm-notes>  -->
</div>


  
