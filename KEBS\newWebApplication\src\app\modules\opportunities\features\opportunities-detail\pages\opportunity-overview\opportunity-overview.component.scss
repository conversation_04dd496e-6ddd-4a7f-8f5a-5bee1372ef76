@import "../../scss/colors.scss";
@import "../../scss/font-size.scss";

@mixin subtleNormal {
  font-size: $normal;
  color: $subtleColor;
}
@mixin subtleSmall {
  font-size: $small;
  color: $subtleColor;
}
.value-div{
  width: 142px;;
}

.opportunity-overview-styles {
  .tileName {
    color: #9a9a9a;
    font-size: 14px !important;
  }

  .smallCardIcon {
    font-size: 21px !important;
    color: #868683 !important;
  }

  .invertedsmallCardIcon {
    font-size: 18px !important;
    color: #e9581f !important;
  }

  .info-icon{
    max-width: 5%;
  }

  .opportunity-overview-card-bg {
    background-color: #fbc0c01f !important;
    box-shadow: 0 3px 6px rgba(105, 105, 105, 0.19),
      0 1px 4px rgba(105, 105, 105, 0.19);
  }
  .more-button {
    width: 27px !important;
    height: 27px !important;
    line-height: 25px !important;
  }
  .budget-row {
    padding-left: 20px;
  }
  // .counter{
  //   padding-left: 6rem;
  // }
  .chip {
    display: inline-block;
    padding: 0 25px;
    height: 50px;
    font-size: 16px;
    line-height: 50px;
    border-radius: 25px;
    background-color: #f1f1f1;
  }

  .chip img {
    float: left;
    margin: 0 10px 0 -25px;
    height: 50px;
    width: 50px;
    border-radius: 50%;
  }
  .smallCardIcon {
    font-size: 18px !important;
    color: #868683 !important;
  }

  .drop-btn {
    font-size: 13px !important;
    height: 38px !important;
    line-height: 2 !important;
    padding: 0 13px !important;
  }

  .view-more {
    line-height: 1;
    height: 30px;
    width: 100px;
    color: #5a5957;
  }

  .opportunity-name {
    color: #cf0001;
    font-size: 16px;
    font-weight: 500;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding: 2px 5px 0px 0%;
  }

  .opportunity-sub-headings {
    color: #cf0001;
    font-size: 14px;
    font-weight: 500;
  }

  .planned-hours-sub-headings {
    color: #1a1a1a;
    font-size: 14px;
    font-weight: 500;
  }

  .opportunity-info-title {
    color: #7b7b7a;
    font-size: 14px;
  }

  .counter-style {
    ::ng-deep .animated-digit {
      font-size: 32px !important;
    }
  }

  .notes-data {
    color: #1a1a1a;
    font-weight: normal;
    font-size: 14px;
  }

  .approve-btn {
    background-color: #64d464;
    font-size: 18px;
    color: white;
    width: 27px;
    height: 27px;
    line-height: 25px;
    box-shadow: 0 4px 4px -1px rgba(0, 0, 0, 0.2),
      0 1px 3px 0 rgba(0, 0, 0, 0.14), 0 2px 14px 0 rgba(0, 0, 0, 0.12);
  }

  .reject-btn {
    background-color: #fc3939;
    font-size: 18px;
    color: white;
    width: 28px;
    height: 28px;
    margin-right: 20px;
    line-height: 27px;
    box-shadow: 0 4px 4px -1px rgba(0, 0, 0, 0.2),
      0 1px 3px 0 rgba(0, 0, 0, 0.14), 0 2px 14px 0 rgba(0, 0, 0, 0.12);
  }

  .opportunity-info-details {
    color: #1a1a1a;
    font-weight: normal;
    font-size: 14px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  
    ::ng-deep .mat-form-field-underline {
      display: none;
    }
  
    .mat-form-field {
      ::ng-deep .mat-form-field-infix {
        padding-bottom: 0px !important;
        padding-top: 0px !important;
        border-top: 0 solid transparent !important;
      }
  
      ::ng-deep .mat-form-field-wrapper {
        padding-bottom: 0px !important;
      }
    }
  
    .date-picker-readonly {
      color: var(--Black-Grey-100, #1a1a1a);
      font-family: Roboto;
      font-size: 13px;
      font-style: normal;
      font-weight: 400;
      line-height: 16px;
      text-transform: capitalize;
    }
  }

  .opportunity-only-date-info {
    color: #1a1a1a;
    font-weight: normal;
    font-size: 14px;
    // overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  
    ::ng-deep .mat-form-field-underline {
      display: none;
    }
  
    .mat-form-field {
      ::ng-deep .mat-form-field-infix {
        padding-bottom: 0px !important;
        padding-top: 0px !important;
        border-top: 0 solid transparent !important;
      }
  
      ::ng-deep .mat-form-field-wrapper {
        padding-bottom: 0px !important;
      }
    }
  
    .date-picker-readonly {
      color: var(--Black-Grey-100, #1a1a1a);
      font-family: Roboto;
      font-size: 13px;
      font-style: normal;
      font-weight: 400;
      line-height: 16px;
      text-transform: capitalize;
    }
  }

  .history-details {
    color: #1a1a1a;
    font-weight: normal;
    font-size: 14px;
    white-space: nowrap;
  }

  .create-account-field {
    font-size: 13px !important;
    width: 100% !important;
   
  }   

  .icon-tray-button {
    width: 27px !important;
    height: 27px !important;
    line-height: 25px !important;
    visibility: hidden;
  }

  .icon-tray-button {
    visibility: visible !important;
    -webkit-animation: slide-top 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)
      both;
    animation: slide-top 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
  }


  .opportunity-info-icons {
    color: #5a5957 !important;
    font-size: 21px !important;
  }

  .title {
    color: #252422 !important;
    font-size: 20px !important;
    text-align: center !important;
    font-weight: 500 !important;
  }

  .sub-heading {
    color: #7b7b7a !important;
    font-size: 14px !important;
    text-align: center !important;
    font-weight: normal !important;
  }

  .listcard {
    transition: all 0.25s linear;
    height: 39px;
    max-height: 39px !important;
  }

  .listcard:hover {
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
    cursor: pointer;
  }

  .icon-tray-button {
    width: 27px !important;
    height: 27px !important;
    line-height: 25px !important;
    visibility: hidden;
  }

  .status-circular {
    height: 13px;
    width: 13px;
    margin-top: 4px;
    border-radius: 50%;
    // background-color: #484198;
    box-shadow: 0 2px 3px -1px rgba(0, 0, 0, 0.2),
      0 1px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 14px 0 rgba(0, 0, 0, 0.12);
  }

  .status-circular-proposal {
    height: 13px;
    width: 13px;
    margin-top: 4px;
    border-radius: 50%;
    // background-color: #009432;
    box-shadow: 0 2px 3px -1px rgba(0, 0, 0, 0.2),
      0 1px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 14px 0 rgba(0, 0, 0, 0.12);
  }

  .img {
    border-radius: 100%;
    display: flex;
    flex: 0 0 150px;
    height: 70px;
    justify-content: center;
    overflow: hidden;
    position: relative;
    width: 70px;
  }
  .img img {
    height: 100%;
  }
  .img__overlay {
    align-items: center;
    bottom: 0;
    display: flex;
    justify-content: center;
    left: 0;
    opacity: 0;
    position: absolute;
    right: 0;
    top: 0;
    transition: opacity 0.25s;
    z-index: 1;
  }
  // .img__overlay:hover {
  //   opacity: 1;
  // }
  .img__overlay {
    background-color: #9a9a9a;
    background: linear-gradient(
      65deg,
      rgba(161, 161, 161, 0.4),
      rgba(253, 246, 236, 0.4)
    );
    color: #fafafa;
    font-size: 24px;
  }

  .image-upload > input {
    display: none;
  }

  //notes
  .colors {
    cursor: pointer;
    height: 16px;
    width: 16px;
    margin-top: 4px;
    border-radius: 50%;
  }
  .dev-extreme-styles {
    ::ng-deep .dx-htmleditor-toolbar-wrapper {
      .dx-toolbar-items-container {
        height: 48px !important;
      }
      .dx-button-content {
        .dx-icon {
          font-size: 16px !important;
          color: #66615b !important;
        }
      }
    }
  }

  .smallSubtleText {
    @include subtleSmall;
    color: #9a9a9a !important;
  }

  .empty-styles {
    color: #3c3c3c;
    font-weight: 500;
    font-size: 14px;
  }

  .status-circular-1 {
    height: 13px;
    width: 13px;
    margin-top: 4px;
    border-radius: 50%;
    background-color: #9de6f0;
    box-shadow: 0 2px 3px -1px rgba(0, 0, 0, 0.2), 0 1px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 14px 0 rgba(0, 0, 0, 0.12);
  }

  .status-circular-2 {
    height: 13px;
    width: 13px;
    margin-top: 4px;
    border-radius: 50%;
    background-color: #f32fc2;
    box-shadow: 0 2px 3px -1px rgba(0, 0, 0, 0.2), 0 1px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 14px 0 rgba(0, 0, 0, 0.12);
  }

  .complete {
    color: white;
    font-weight: normal;
    font-size: 12px !important;
    background-color: #cf0001;
    min-width: 62px;
    line-height: 26px;
    padding: 0 10px;
  }

  .slide-in-top {
    -webkit-animation: slide-in-top 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94)
      both;
    animation: slide-in-top 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
  }

  /**
   * ----------------------------------------
   * animation slide-in-top
   * ----------------------------------------
   */
  @-webkit-keyframes slide-in-top {
    0% {
      -webkit-transform: translateY(-30px);
      transform: translateY(-30px);
      opacity: 0;
    }
    100% {
      -webkit-transform: translateY(0);
      transform: translateY(0);
      opacity: 1;
    }
  }
  @keyframes slide-in-top {
    0% {
      -webkit-transform: translateY(-30px);
      transform: translateY(-30px);
      opacity: 0;
    }
    100% {
      -webkit-transform: translateY(0);
      transform: translateY(0);
      opacity: 1;
    }
  }

  .slide-from-down {
    -webkit-animation: slide-from-down 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94)
      both;
    animation: slide-from-down 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
  }

  /**
   * ----------------------------------------
   * animation slide-from-bottom
   * ----------------------------------------
   */
  @-webkit-keyframes slide-from-down {
    0% {
      -webkit-transform: translateY(30px);
      transform: translateY(30px);
      opacity: 0;
    }
    100% {
      -webkit-transform: translateY(0);
      transform: translateY(0);
      opacity: 1;
    }
  }
  @keyframes slide-from-down {
    0% {
      -webkit-transform: translateY(30px);
      transform: translateY(30px);
      opacity: 0;
    }
    100% {
      -webkit-transform: translateY(0);
      transform: translateY(0);
      opacity: 1;
    }
  }

  .iconbtn {
    height: 26px;
    width: 26px;
    line-height: 26px;
    background-color: #c92020;
    color: white;
    padding: 0 0;
    box-shadow: 0 3px 5px -1px rgba(0, 0, 0, 0.2),
      0 6px 10px 0 rgba(0, 0, 0, 0.14), 0 1px 18px 0 rgba(0, 0, 0, 0.12);
  }
  .bid{
    font-weight: 500;
    cursor: pointer; 
    color:#009432;
  }
  .noBid{
    font-weight: 500;
    cursor: pointer; 
    color:#cf0001;
  }
  

  
  .example-viewport {
    min-height: 200px;
  }

  .fab-btn {
    background-color: #cf0001;
    color: white !important;
    width: 35px;
    height: 35px;
  }

  .iconButton {
    color: $iconButtonColor;
    font-size: 18px;
  }
  .deleteFormButton:hover {
   
    cursor: pointer;

    .icon-tray-button {
      visibility: visible !important;
      -webkit-animation: slide-top 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)
        both;
      animation: slide-top 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
    }
  }

  .icon-tray-button {
    width: 27px !important;
    height: 27px !important;
    line-height: 25px !important;
    visibility: hidden !important;
  }

  .icon-tray-visible {
    width: 27px !important;
    height: 27px !important;
    line-height: 25px !important;
    visibility: visible !important;
  }

  .trend-button-inactive {
    margin-top: 5px !important;
    line-height: 8px;
    width: 26px;
    height: 26px;
    border-radius: 50%;
    box-shadow: 0 4px 4px -1px rgba(0, 0, 0, 0.2),
      0 1px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 14px 0 rgba(0, 0, 0, 0.12);
    .iconButton {
      color: #66615b;
      font-size: 18px;
    }
    .canManView {
      color: #66615b;
      font-size: 21px;
    }
  }

  ::ng-deep .cdk-virtual-scroll-content-wrapper {
    min-height: inherit;
  }
}

.timeline ::-webkit-scrollbar{
  display: none;
}

.loading-border{
	font-size: 3vw;
	// margin: max(1rem, 3vw);
	border-bottom: 3px solid;
	// padding: 3vw;
	border-image: conic-gradient(from var(--angle), #ef4a61 0.1turn, #f27a6c 0.15turn,#FFF 1turn) 10;
	animation: borderRotate 2500ms linear infinite forwards;
}


@keyframes borderRotate {
	100% {
		--angle: 420deg;
	}
}

@property --angle {
  syntax: '<angle>';
  initial-value: 90deg;
  inherits: true;
}


.create-btn-empty-screen {
  padding: 8px 12px;
  background-color: #EE4961;
  width: fit-content;
  border-radius: 4px;
  color: white;
  cursor: pointer;
}

.create-btn-data-screen {
  padding: 8px 12px;
  background-color: #EE4961;
  width: fit-content;
  border-radius: 4px;
  color: white;
  cursor: pointer;
  margin-right: 3vh;
}
.dis-create-btn-empty-screen {
  padding: 8px 12px;
  background-color: #a5a2a2;
  width: fit-content;
  border-radius: 4px;
  color: white;
  cursor: pointer;
  margin-right: 3vh;
}
.account-info-details {
  color: #1a1a1a;
  font-weight: normal;
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.if-at-risk{
  height: 24px;
  font-size: 12px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  padding: 4px 8px;
  background: #EC5F6E !important;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #fff;
  white-space: nowrap;
}

.if-not-at-risk{
  height: 24px;
  font-size: 12px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  padding: 4px 8px;
  background: #52C41A !important;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #fff;
  white-space: nowrap;
}

.account-sub-headings {
  color: #cf0001;
  font-size: 14px;
  font-weight: 500;
  overflow: hidden;
  white-space: nowrap;
}
.account-item-headings {
  font-size: 14px;
  font-weight: 500;
  overflow: hidden;
  white-space: nowrap;
}
.revenueView{
  height:50vh;

}
.revenueScrollView{
  height:40vh;
  overflow-y: scroll;

}
.revenuelistcard {
  transition: all 0.25s linear;
  height: 39px;
  max-height: 39px !important;
}

::ng-deep .multi-line-tooltip-opp-overview {
  white-space: pre-line;
  font-size: 10px !important
}