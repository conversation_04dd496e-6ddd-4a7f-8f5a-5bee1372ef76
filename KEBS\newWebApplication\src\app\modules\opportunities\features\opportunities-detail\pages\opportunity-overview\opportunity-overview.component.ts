import { After<PERSON><PERSON>w<PERSON>nit, Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { OpportunityService } from "../../services/OpportunityService";
import { ActivatedRoute, Router } from '@angular/router';
import * as moment from 'moment';
import { MatSnackBar } from '@angular/material/snack-bar';
import { InlineEditPopupService } from 'src/app/modules/main/services/inline-edit-popup/inline-edit-popup.service';
import { SharedLazyLoadedComponentsService } from "src/app/modules/shared-lazy-loaded-components/services/shared-lazy-loaded-components.service";
import { UtilityService } from '../../../../../../services/utility/utility.service'
import { MatDialog } from '@angular/material/dialog';
import * as _ from 'underscore';
import { MatDialogRef} from '@angular/material/dialog';
import { MeetingInviteComponent } from '../../../../../shared-lazy-loaded-components/meeting-invite/meeting-invite.component';
import { CreateCrmNoteComponent } from 'src/app/modules/account-sales/features/crm-notes/components/edit-card/edit-card.component';
import sweetAlert from 'sweetalert2';
import { TicketService } from 'src/app/modules/ams/services/ticket.service';
import {
  FormBuilder,
  FormGroup,
  Validators, 
  FormControl,
  FormArray
} from "@angular/forms";
import { TabbedComponentComponent } from 'src/app/modules/account-sales/features/activities-form/pages/tabbed-component/tabbed-component.component';
import { Subject } from 'rxjs';
import { UdrfActivityService } from 'src/app/modules/account-sales/features/display-activities/services/udrf-activity.service';
import { ToasterService } from 'src/app/services/toaster-service/toaster.service';
import { takeUntil } from 'rxjs/operators';
import { QuoteMainService } from 'src/app/modules/quote-builder/services/quote-main.service';
import { QBMasterDataService } from 'src/app/modules/quote-builder/services/qb-master-data.service';
import { tenantPipe } from '../../../../shared/pipes/tenant-form.pipe';
import { RolesService } from 'src/app/services/acl/roles.service';

import { DateAdapter } from '@angular/material/core';
import { MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core';
import { MAT_MOMENT_DATE_ADAPTER_OPTIONS, MomentDateAdapter } from '@angular/material-moment-adapter';

import { APP_DATE_FORMATS, AppDateAdapter } from 'src/app/modules/account-sales/features/activities-form/activities-date-format-config';
import { DatePipe } from '@angular/common';

// import { DynamicDatePipePipe } from 'src/app/modules/opportunities/features/opportunities-detail/pipes/dynamic-date-pipe.pipe'

@Component({
  selector: 'app-opportunity-overview',
  templateUrl: './opportunity-overview.component.html',
  styleUrls: ['./opportunity-overview.component.scss'],
  providers: [tenantPipe,DatePipe,
  //   {
  //   provide: DateAdapter,
  //   useClass: MomentDateAdapter,
  //   deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS]
  // },
  // {
  //   provide: MAT_DATE_FORMATS,
  //   useValue: {
  //     parse: {
  //       dateInput: "DD MMM YYYY"
  //     },
  //     display: {
  //       dateInput: "DD MMM YYYY",
  //       monthYearLabel: "MMM YYYY"
  //     }
  //   }
  // }
  { provide: DateAdapter, useClass: AppDateAdapter },
  { provide: MAT_DATE_FORMATS, useValue: APP_DATE_FORMATS },
]
})
export class OpportunityOverviewComponent implements OnInit,AfterViewInit {
  channelid: any;
  savedImage: any = "assets/images/upload.png";
  overviewDetails: any;
  tempOverviewDetails: any;
  opportunityId: any;
  elementId: number;
  authority: any;
  statusLogObservable: Subject<any[]> = new Subject();
  index: any;

  dataArray : any;
  inlineEditActiveRow: any;
  inlineEditField = "";
  editType = "";
  dropdownConfig: any;
  
  current_user: any;

  probabilityMaster : any;
  bidTypeMaster : any;
  sourceMaster : any;
  salesStatusMaster : any;
  preSalesStatusMaster : any;
  serviceTypeMaster : any;

  bidStatus: string;
  planned_presale_history: any;
  planned_presale_logs: any;
  is_workflow: boolean=false;
  openHistory: boolean=false;
  openApprover: boolean=false;
  openComment: boolean=false;
  presales_hours_changed_by: any;
  presales_hours_changed_on: any;
  presales_hours_approver: any=[];
  presales_changed_hours: any;
  is_approver: boolean=false;
  approver_status=0;
  presales_approvers: any;
  is_comments: boolean=false;
  planned_presales_hours_comment=[]
  opportunity_form=[]
  column_config = []
  view_heirarchy_details: any ={}

  is_bid_approver: boolean=false
  bid_qualification_changed_by: any;
  bid_qualification_changed_on: any;
  bid_qualification_approver: any=[];
  bid_qualification_history: any;
  bid_qualification_logs: any;
  is_bid_workflow: boolean=false;
  openBidComment: boolean=false;
  openBidHistory: boolean=false;
  openBidApprover: boolean=false;
  bid_qualification_approvers: any;
  is_bid_comment: boolean=false;
  bid_qualification_comments = [];
  bid_approval_status=0;
  
  comments_approvers=''
  entered_Channel: any=[];
  dup_entered_Channel: any=[];
  channel_type: any;
  
  channel_id_value: any=null;
  channel_type_value: any=null;
  channel_type_id: any;
  channel_saved: boolean=false;
  channel_id_ex: boolean=false;
  channel_type_ex: boolean=false;

  product_category_list: any;
  module_category_list: any;
  productCategory=[]
  module_list=[]
  serviceLineMaster = {};
  service_line=[]
  shift_list=[]
  commentBoxHeight: any="40vh"
  commentBoxScrollHeight: any="80%"

  userDeleteForm: boolean=false;
  userAddForm: boolean=false;
  is_save_channel_loading: boolean=false;
  editAccess: boolean = false;
  reasonPresent: boolean = false;
  reason: any;
  region_id:any;
  costcode:any;
  attendee:any;
  activityId: any;
  data: any;
  statusColor=[]
  salesUnit: any;
  parentOpportunityMaster=[];

  refresh: any;
  formFieldData: any;
  formFieldDataExist : boolean = false;
  isDemoConfig: boolean = false;

  line_of_business_access: boolean=this.opportunityService.displayLineOfBusinessAndModulesInCreateOpportunity();
  mailAccessInOpportunity: boolean= false;
  isAdditionalField : boolean = this.opportunityService.isAdditionalField()
  deactivateAccess : boolean = this.opportunityService.deactivateAccess()
  stagewiseFieldConfig : boolean=false
  tempstagewiseFormFieldData:any;
  stagewiseFormFieldData:any;
  stagewiseFormFieldDataDisabled:any;

  user_form_name = new FormControl("", [Validators.required]);
  user_form_id = new FormControl("",[Validators.required]);

  isDisableEdit : boolean = false;
  reOpenAccess : boolean = false;

  valueSyncButtonVisible: boolean = false;
  valueSyncQuoteId = null;

  isQuoteEnabled: boolean = false;
  isActiveQuoteExists: boolean = false;
  oppEditAccess: boolean = false;
  notesEditAccess: boolean = false;
  opportunityProjectDetails:any =[]
  projectCreationAccess:boolean = false;
  projectViewAccess:boolean = false;
  projectView:boolean = false;
  projectLoad:boolean = true;
  revenueLoad:boolean = true;
  childRestriction:any=false;
  projectCreationAllowedBasedOnAccount:any=true;
  projectCreationText:any="Project cannot be created in this opportunity stage."
  oppFYdata:any =[]
  isFyChange : boolean = this.opportunityService.isFyChange();
  tempSelectedDate: Date | null = null;
  deliveryStartTempDate: Date | null = null;
  deliveryEndTempDate: Date | null = null;
  revenueConfig: object | any = {};
  isStatusStageMappingAllowed: boolean = false ;
  statusStageMapping: any;
  dateFormat: string = 'DD - MMM - YYYY';
  quoteHasChangeRequest: boolean = true;
  dynoTooltipContent: string;
  child_opportunity_creation_access: boolean = false;
  flagChangeWizardAccess: boolean = false;
  projectFormConfig: any;
  masking_configuration: any;
  quote_field_config: any;
  decimalPartsCache: { [key: string]: number } = {};
  maxDigitsAllowedCache: { [key: string]: number } = {};
  
  getCostCenter(){
    this.opportunityService.getCostCode(this.overviewDetails.region_id).then(async(res)=>{
    this.costcode=res;
    // console.log("CostCode",this.costcode)
  },(err)=>{
  console.log(err)
  this.snackBar.open("Cannot integrate with timesheet! Contact KEBS team", "Dismiss", {
    duration: this.opportunityService.mediumInterval,
  });
  })
  }

  // @ViewChild(DynamicDatePipePipe) dynamicDatePipe!: DynamicDatePipePipe;
  
  
  

  // proposalSubmissionDate = new FormControl();
  constructor(private route: ActivatedRoute,
    private opportunityService: OpportunityService,
    private snackBar: MatSnackBar,private inlineEditPopupService: InlineEditPopupService, 
    private sharedLazyLoadedComponentsService:
    SharedLazyLoadedComponentsService,
    private UtilityService: UtilityService,
    public dialog: MatDialog,
    private _router : Router,
    private udrfServices: UdrfActivityService,
    private _toaster: ToasterService,
    private _quoteService: QuoteMainService,
    private roleService: RolesService,
    private _qbMasterService: QBMasterDataService,
    private labelForm: tenantPipe,
    private dateAdapter: DateAdapter<Date>,
    private _ticket: TicketService
  ) { }

    

  async ngOnInit() {
    // this.opportunityProjectDetails=[{project_code:"12PJ",project_name:"ELM Prj",status_id:21,status:"Active"}]
    await this.getGlobalCRMDateFormat();
    this.oppEditAccess= await this.checkEditAcess()
    this.notesEditAccess= await this.checkNotesEditAccess()
    this.flagChangeWizardAccess = await this.opportunityService.checkFlagChangeAccess()
    this._qbMasterService.quoteEnabled
      .subscribe(res => {

        this.isQuoteEnabled = res['quote_enabled'];
        
      });

    this.module_list=[]
    this.route.parent.params.subscribe(async (res) => {
      this.opportunityId = res['opportunityId'];
      this.elementId=parseInt(this.opportunityId)
    });
    await this.getQuoteConfiguration();
    this.revenueConfig=await this.opportunityService.getConfigForOpportunityDetails('opportunityOverviewRevenue')
    await this.opportunityService.stagewiseFieldConfig(29378).then((res:any)=>{
      this.stagewiseFieldConfig = res.messType == "S" && res.messData.length > 0 ? true : false
    },(err)=>{
      console.log(err)
    })
    this.statusLogObservable.subscribe(res=>{
      this.statusLog= res
    })
    this.opportunityService.getStatusLogForOpp(parseInt(this.opportunityId)).subscribe((res:any)=>{
      this.statusLogObservable.next(res.data);
    })
    
    this.userDeleteForm = this.opportunityService.getAccesstoDeleteForm();
    this.userAddForm = this.opportunityService.getAccesstoAddForm();
    await this.udrfServices.getActivitiesType().then(res => {
      let data = res as any[];
      let isDemoPresent = data.filter(item => item.id === 9);
      console.log("isdemo", isDemoPresent);
      if(isDemoPresent.length > 0) {
        this.isDemoConfig = true;
      }
    })
    await this.getChannel();
    await this.getApproverforPresalesHours(this.opportunityId)
    await this.getApproverforBidQualification(this.opportunityId)
    await this.opportunityService.opportunityOverview(this.opportunityId).subscribe(async (res: any) => {
      
      this.overviewDetails = res.opportunityOverview[0];
      this.overviewDetails.win_loss_details = JSON.parse(this.overviewDetails.win_loss_details);
      this.tempOverviewDetails = this.overviewDetails;
      this.prepareTooltipContent()
      this.editAccess = this.opportunityService.checkUserforEditAccess(this.overviewDetails.sales_unit);
      ////console.log("DELL",this.productCategory)
      this.current_user=res.oid;
      this.planned_presale_history=JSON.parse(this.overviewDetails.planned_presales_hours_history)
      this.planned_presale_logs=JSON.parse(this.overviewDetails.planned_presales_hours_logs)
      this.planned_presales_hours_comment=(this.overviewDetails.planned_presales_hours_comment==null)?[]:JSON.parse(this.overviewDetails.planned_presales_hours_comment)

      this.bid_qualification_history=JSON.parse(this.overviewDetails.bid_qualification_history)
      this.bid_qualification_logs=JSON.parse(this.overviewDetails.bid_qualification_logs)
      this.bid_qualification_comments=(this.overviewDetails.bid_qualification_comments==null)?[]:JSON.parse(this.overviewDetails.bid_qualification_comments)

      this.reasonPresent = (this.overviewDetails.reason==null || this.overviewDetails.reason=="")?false:true
      this.reason = this.reasonPresent? JSON.parse(this.overviewDetails.reason):undefined
      console.log(this.reason)
      this.presalesHours();
      this.service_line = this.overviewDetails?.service_line != null && this.overviewDetails.service_line != '' ? JSON.parse(this.overviewDetails.service_line) : []
      
      // this.authority = JSON.parse(JSON.parse(this.overviewDetails.authority));
      // //console.log(this.authority);
      console.log(this.overviewDetails)
      this.bidStatus = this.overviewDetails.bid_perc!=null
      ? this.overviewDetails.is_bid ==1
        ? '/ Bid' : '/ No Bid'
      : ''

      if(this.overviewDetails.bid_perc!=null && this.overviewDetails.is_bid !=1)
      {
        this.noBidQualification()
      }
      let isDisableEdit = await this.opportunityService.isDisableEdit(this.overviewDetails);
      let reOpenAccess = await this.opportunityService.reOpenAccess(this.overviewDetails);
      this.isDisableEdit = isDisableEdit;
      this.reOpenAccess = reOpenAccess;
      this.opportunityService.getStagewiseFormFieldConfig(this.overviewDetails.sales_status_id).then((res:any) => {
        this.tempstagewiseFormFieldData = JSON.parse(res.messData[0].stagewise_field_config)
        this.stagewiseFormFieldDataDisabled=_.filter( this.tempstagewiseFormFieldData,function(item:any){
         return (item.changeIndicator==true && item.disabled)
        })
        console.log(this.stagewiseFormFieldDataDisabled)
      },
        err => {
          console.log(err)
        })
    })

    await this.opportunityService.getOpportunityForm(this.opportunityId).then((res: any)=>{
      this.opportunity_form = res
    },(err)=>{
      console.log(err)
    })

    let precomputeDecimalParts = () => {
      this.formFieldData.forEach(field => {
        this.decimalPartsCache[field.field_name] = Number(field?.decimal_part) || 13;
        this.maxDigitsAllowedCache[field.field_name] = Number(field?.max_digits) || 13;
      });
    }

    this.opportunityService.getFormFieldCollection().subscribe(res => {
      if(res['messType'] == "S")
      this.formFieldData = res['result']
      precomputeDecimalParts();
      if(this.isFyChange){
        let created_on_month = moment(this.overviewDetails.created_on).month();
        if(created_on_month < 3) {
          const currentyear = moment(this.overviewDetails.created_on)
           this.fyYearChange(currentyear.year() - 1)
        } else {
          const currentyear = moment(this.overviewDetails.created_on)
           this.fyYearChange(currentyear.year())
        }
      }
      this.formFieldDataExist = true
      console.log("res is : ",res)
    },
      err => {
        console.log(err)
    }) 

    this.opportunityService.getPMFormCustomizeConfigV().subscribe(res => {
      if (res['messType'] == "S") this.projectFormConfig = res['data']
    },
      err => {
        console.log(err)
      }) 
    

  
    
    // await this.opportunityService.getAttendee(this.opportunityId).then((res: any)=>{
    //   this.attendee = res
    // },(err)=>{
    //   console.log(err)
    // })

    //Inline edit master datas
    await this.opportunityService.getProbabilityMaster().subscribe(res => {
      this.probabilityMaster = res;
    },err =>{
      console.log(err);
    });

    await this.opportunityService.getbidType().subscribe(res => {
      this.bidTypeMaster = res;
    },err =>{
      console.log(err);
    });

    await this.opportunityService.getLeadSource().subscribe(res => {
      this.sourceMaster = res;
    },err =>{
      console.log(err);
    });

    await this.opportunityService.getOpportunityStatusSales().then(res => {
 
     _.each(res, (sec) => {

       let valh = sec.udrf_summary_card
       if (valh != null) {
         let val = JSON.parse(sec.udrf_summary_card)

         this.statusColor.push({
           color: val.color,
           status: val.dataType,
           statusColor: val.color,
           statusName: val.dataType
         })
       }
     })

     this.salesStatusMaster = res;



   })

   await this.loadStatusStageMapping();
   await this.checkChildOpportunityCreationAccess();

   if (this.isQuoteEnabled) {
      
    this.getOppQuoteValueStatus();

    await this._quoteService.getOpportunityFYData(this.tempOverviewDetails.opportunity_id, this.tempOverviewDetails.currency_code,'OVERVIEW')
      .pipe(takeUntil(this._onDestroy))
      .subscribe(res => {

        if (res["messType"] == "S" && res["data"]) {}

          this.isActiveQuoteExists = res["data"]["activeQuoteExists"] || false; 
          this.oppFYdata = res["data"]["fyData"]
          
          for (const fyItem of this.oppFYdata){
          fyItem.fy_label="Est.Rev "+fyItem['fy_label']
          }
          this.revenueLoad = false
          console.log(this. isActiveQuoteExists)
          console.log(this. oppFYdata)
      },
      err => {
        console.log(err);
        
      });

    }
  else{
    
    this.revenueLoad = false
  }
  
   await this.opportunityService.getProposalType().then(res => {
    _.each(res, (sec) => {

        this.statusColor.push({
          color: sec.proposal_status_color,
          status: sec.proposal_status,
          statusColor: sec.proposal_status_color,
          statusName: sec.proposal_status
        })
    })
  })

   this.opportunityService.getSalesUnit().subscribe(res=>{
    this.salesUnit = res;
   })

   this.opportunityService.getParentOpportunityMaster(this.overviewDetails.customer_id).subscribe(res=>{
    this.parentOpportunityMaster = res[0]['data'];
   })

   this.opportunityService.getServiceLineMaster().subscribe(res=>{
      let serviceLineMaster: any = res;
      if(serviceLineMaster && serviceLineMaster.length > 0) {
        serviceLineMaster.forEach(val => {
          this.serviceLineMaster[val.id] = val;
        });
      }
   })

    await this.opportunityService.getProposalType().then(res => {
      this.preSalesStatusMaster = res;
    },err =>{
      console.log(err);
    });

    await this.opportunityService.getChannelType().subscribe(res => {
      this.channel_type = res;

    },err => {
      console.log(err);
    })

    await this.opportunityService.getProductViewHierarchyLabel(36,"Opportunity").then((res: any)=>{
      if(res['messType']=="S")
      {
          this.column_config=res['result']
          this.view_heirarchy_details = res['result_details']
      }
    },(err)=>{
        console.log(err)
    })

    
    await this.opportunityService.getServiceType().subscribe(res => {
      this.serviceTypeMaster = res;
    },err =>{
      //console.log(err);
    });

    // await this.opportunityService.getProductCategory().then((res: any)=>{
    //   this.product_category_list = res;
    // },(err)=>{
    //   //console.log(err)
    // })

    await this.opportunityService.getModule().then((res: any)=>{
        this.module_category_list = res;
    },(err)=>{
      //console.log(err)
    })

    await this.opportunityService.getShiftList().then((res: any)=>{
      this.shift_list = res;
    },(err)=>{
      console.log(err)
    })


    await this.productCategoryList();
    await this.moduleList();

     this.opportunityService.getOpportunityProjectDetails(this.tempOverviewDetails.opportunity_id).subscribe(res => {
        if(res['messType'] == "S")
        this.opportunityProjectDetails = res['messData']
        this.projectLoad = false;
        console.log("res is : ",res)
      },
        err => {
          console.log(err)
      })

    this.childRestriction= await this.checkChildRestriction()
    this.projectCreationAllowedBasedOnAccount=await this.checkProjectRestrictionAccount();
    this.projectCreationAccess = await this.projectAccessCheck() 
    console.log(this.projectCreationAllowedBasedOnAccount)
    this.projectViewAccess = await this.projectViewAccessCheck() 
    this.projectView = await  this.projectBtnviewaccess()
     console.log( this.projectCreationAccess)
    console.log( this.projectView)
  }

  
  checkFieldShouldbeMasked(fieldItem: any): boolean {
    return this.masking_configuration?.maskingEnabled && 
    this.masking_configuration?.masking_field_config.some(item => item.key.includes(fieldItem) && item.role_id.includes(this._ticket.getCurrentUserRole()));
  }

  async loadStatusStageMapping(): Promise<void> {
    try {
      const response = await this.opportunityService.getStatusStageMappingConfig().toPromise();
      if (response?.CODE === 'ALLOWED') {
        this.isStatusStageMappingAllowed = true;
        this.statusStageMapping = response.mapping.reduce((acc, { status_id, proposal_status_id, proposal_status_name }) => {
          acc[status_id] = { proposal_status_id, proposal_status_name };
          return acc;
        }, {});
      } else {
        this.isStatusStageMappingAllowed = false;
        this.statusStageMapping = {};
      }
    } catch (error) {
      console.error('Error loading status stage mapping:', error);
    }
  }

  getProposalStatus(status_id: number): { proposal_status_id: number, proposal_status_name: string } {
    if (this.isStatusStageMappingAllowed) {
      return this.statusStageMapping[status_id] || { proposal_status_id: 1, proposal_status_name: 'Open' };
    }
    return { proposal_status_id: 0, proposal_status_name: null};
  }

  navigateToAccounts(){
    if(this.overviewDetails.customer_name != '-'){
      this.opportunityService.checkIfAccountExist(this.overviewDetails.customer_id).subscribe(
        async (res) => {
          if(res['exists']){
            let navigationUrl =
              window.location.origin +
              '/main/accounts/' +
              this.overviewDetails.customer_id +
              '/' +
              this.UtilityService.encodeURIComponent(this.overviewDetails.customer_name) +
              '/overview';
            window.open(navigationUrl);
          } else {
            this.UtilityService.showMessage('Account Does not Exist!', 'Dismiss');
          }
        },
        (err) => {
          console.log(err);
          this.UtilityService.showMessage('Account Does not Exist!', 'Dismiss');
        }
      );
    }
    else{
      this.UtilityService.showErrorMessage("No Account is linked!","Dismiss")
    }
  }

  navigateToContacts() {
    let navigationUrl =
      window.location.origin +
      '/main/contacts/' +
      this.overviewDetails.customer_contact_id +
      '/overview';
    window.open(navigationUrl);
    this._toaster.showWarning('Redirecting...', '', 500);
    window.open(navigationUrl, '_blank');
  }
  
  parseJson(text) {
    return JSON.parse(text);
  }

  

  activateInlineEdit(inlineEditField, editType, dataArray, dataItem, index) {

  
    if(this.isDisableEdit){
      this.UtilityService.showMessage("Closed Won Opportunity Cannot be Edited!", 'Dismiss');
      return;
    }
    if(!this.oppEditAccess){
      this.UtilityService.showMessage("Edit is restricted", 'Dismiss');
      return;
    }
 
    this.dataArray = dataArray
    this.inlineEditActiveRow = dataItem;
    this.index= index

    if (!this.inlineEditPopupService.inlineEditCallbackSubscription || (this.inlineEditPopupService.inlineEditCallbackSubscription &&
      this.inlineEditPopupService.inlineEditCallbackSubscription.closed))
      this.inlineEditPopupService.inlineEditCallbackSubscription = this.inlineEditPopupService.inlineEditCallback.subscribe(res => {

        if (res && !(Object.keys(res).length === 0 && res.constructor === Object))
          this.inlineEditResponseFunction(res);

      });

    this.inlineEditField = inlineEditField;
    this.editType = editType;
    



    this.editType = editType;

    if (editType == "minimal-dropdown") {

      if (this.inlineEditField == 'Probability') {

        let statusColorMapping = this.UtilityService.lmhv_status_colors;

        this.dropdownConfig = {
          apiDataUpdateKeyName: "id",
          apiDataSelectedKeyName: "name",
        };

      }

      else if (this.inlineEditField == 'Bidtype') {
        this.dropdownConfig = {
          apiDataUpdateKeyName: "id",
          apiDataSelectedKeyName: "name",
          statusColorMapping: null
        };

      }

      else if (this.inlineEditField == 'Source') {
        this.dropdownConfig = {
          apiDataUpdateKeyName: "id",
          apiDataSelectedKeyName: "name",
          statusColorMapping: null
        };

      }

      else if (this.inlineEditField == 'Salesstatus') {
        this.dropdownConfig = {
          apiDataUpdateKeyName: "id",
          apiDataSelectedKeyName: "name",
          statusColorMapping: this.statusColor
        };

      }

      else if (this.inlineEditField == 'SalesOrg') {
        this.dropdownConfig = {
          apiDataUpdateKeyName: "id",
          apiDataSelectedKeyName: "name",
          statusColorMapping: null
        };

      }

      else if (this.inlineEditField == 'Presalesstatus') {
        this.dropdownConfig = {
          apiDataUpdateKeyName: "id",
          apiDataSelectedKeyName: "name",
          statusColorMapping: this.statusColor
        };

      }
      else if (this.inlineEditField == 'Servicetype') {
        this.dropdownConfig = {
          apiDataUpdateKeyName: "id",
          apiDataSelectedKeyName: "name",
          statusColorMapping: null
        };

      }
      else if (this.inlineEditField == 'New channel type') {
        this.dropdownConfig = {
          apiDataUpdateKeyName: "id",
          apiDataSelectedKeyName: "roles",
          statusColorMapping: null
        }
      }
      else if (this.inlineEditField == 'channel type') {
        this.dropdownConfig = {
          apiDataUpdateKeyName: "id",
          apiDataSelectedKeyName: "roles",
          statusColorMapping: null
        }
      }

      else if(this.inlineEditField == 'opportunityType'){
        this.dropdownConfig = {
          apiDataUpdateKeyName: "id",
          apiDataSelectedKeyName: "name",
          statusColorMapping: null
        }
      }
      
    }

    else if (editType == "search-dropdown")
        this.dropdownConfig = {
          inlineEditField: inlineEditField,
          dropdownSelectedValue: null, // field name in the active row
          apiServiceVariable: this.sharedLazyLoadedComponentsService, // service variable of the API to be used
          apiFunctionName: "searchConsultants", // function name of the API to be used
          apiDataUpdateKeyName: "oid", // key of value of the active row to be updated in the backend
          apiDataSelectedKeyName: "name", // key of value to be finally applied in the active row
          hasImageView: true, // does search have an image to be shown? - consultant image etc.
          apiDataImageKeyName: "oid" // key of value to be shown as image in the active row
        };
      

    else if (this.editType == "date-picker") {
      if (this.inlineEditField == 'Start date') 
        this.dropdownConfig = {
          inlineEditField: this.inlineEditField,
          dropdownSelectedValue: this.inlineEditActiveRow.processing_start_date, // field name in the active row
          apiDataSelectedKeyName: this.inlineEditField, // key of value to be finally applied in the active row
        };
      else if(this.inlineEditField == 'End date')
      this.dropdownConfig = {
        inlineEditField: this.inlineEditField,
        dropdownSelectedValue: this.inlineEditActiveRow.processing_end_date, // field name in the active row
        apiDataSelectedKeyName: this.inlineEditField, // key of value to be finally applied in the active row
      };
      else if(this.inlineEditField == 'Delivery Start Date'){
        this.dropdownConfig = {
          inlineEditField: this.inlineEditField,
          maxValue: moment(this.inlineEditActiveRow.deliveryFinishDate).format("YYYY-MM-DD"),
          dropdownSelectedValue: this.inlineEditActiveRow.deliveryStartDate, // field name in the active row
          apiDataSelectedKeyName: this.inlineEditField, 
        }
      }
      else if(this.inlineEditField == 'Actual Closure Date'){
        this.dropdownConfig = {
          inlineEditField: this.inlineEditField,
          dropdownSelectedValue: this.inlineEditActiveRow.actualClosureDate, // field name in the active row
          apiDataSelectedKeyName: this.inlineEditField, 
        }
      } 
          else if(this.inlineEditField == 'First Billing Date'){
        this.dropdownConfig = {
          inlineEditField: this.inlineEditField,
          // maxValue: moment(this.inlineEditActiveRow.deliveryFinishDate).format("YYYY-MM-DD"),
          dropdownSelectedValue: this.inlineEditActiveRow.firstBillingDate, // field name in the active row
          apiDataSelectedKeyName: this.inlineEditField, 
        }
      }
      else if(this.inlineEditField == 'Delivery Finish Date'){
        this.dropdownConfig = {
          inlineEditField: this.inlineEditField,
          minValue:  moment(this.inlineEditActiveRow.deliveryStartDate).format("YYYY-MM-DD"),
          dropdownSelectedValue: this.inlineEditActiveRow.deliveryFinishDate, // field name in the active row
          apiDataSelectedKeyName: this.inlineEditField,      }
      }
      else if(this.inlineEditField == 'RFP release date')
      this.dropdownConfig = {
        inlineEditField: this.inlineEditField,
        dropdownSelectedValue: this.inlineEditActiveRow.rfp_date, // field name in the active row
        apiDataSelectedKeyName: this.inlineEditField, // key of value to be finally applied in the active row
      };
      else if(this.inlineEditField == 'Proposal date')
      this.dropdownConfig = {
        inlineEditField: this.inlineEditField,
        dropdownSelectedValue: this.inlineEditActiveRow.proposal_submission_date, // field name in the active row
        apiDataSelectedKeyName: this.inlineEditField, // key of value to be finally applied in the active row
      };
      else if(this.inlineEditField == 'Proposal Sales Date')
      this.dropdownConfig = {
        inlineEditField: this.inlineEditField,
        dropdownSelectedValue: this.inlineEditActiveRow.sales_proposal_date, // field name in the active row
        apiDataSelectedKeyName: this.inlineEditField, // key of value to be finally applied in the active row
      };
    }

    else if (this.editType == "simple-text") {
      if (this.inlineEditField == 'Opportunity name') 
        this.dropdownConfig = {
          inlineEditField: this.inlineEditField,
          dropdownSelectedValue: this.inlineEditActiveRow.opportunity_name, // field name in the active row
          apiDataSelectedKeyName: this.inlineEditField, // key of value to be finally applied in the active row
          inputType: "text", // type of text to be updated - text or number
        };
        else if (this.inlineEditField == 'Planned Presales Hours')
        this.dropdownConfig = {
          inlineEditField: this.inlineEditField,
          dropdownSelectedValue: this.inlineEditActiveRow.planned_presales_hours, // field name in the active row
          apiDataSelectedKeyName: this.inlineEditField, // key of value to be finally applied in the active row
          inputType: "number", // type of text to be updated - text or number
        };
        else if (this.inlineEditField == 'channel id')
        {
          this.dropdownConfig = {
            inlineEditField: this.inlineEditField,
            dropdownSelectedValue: this.inlineEditActiveRow[this.index].channel_id, // field name in the active row
            apiDataSelectedKeyName: this.inlineEditField, // key of value to be finally applied in the active row
            inputType: "text", // type of text to be updated - text or number
          };
        }
        else if(this.inlineEditField =='New channel id')
        {
          this.dropdownConfig = {
            inlineEditField: this.inlineEditField,
            dropdownSelectedValue: this.inlineEditActiveRow, // field name in the active row
            apiDataSelectedKeyName: this.inlineEditField, // key of value to be finally applied in the active row
            inputType: "text", // type of text to be updated - text or number
          };
        } 
        else if(this.inlineEditField =='Planned hours comments'){
          this.dropdownConfig = {
            inlineEditField: this.inlineEditField,
            dropdownSelectedValue: this.inlineEditActiveRow, // field name in the active row
            apiDataSelectedKeyName: this.inlineEditField, // key of value to be finally applied in the active row
            inputType: "text", // type of text to be updated - text or number
          };
        }
        else if (this.inlineEditField == 'Opportunity Value') 
        this.dropdownConfig = {
          inlineEditField: this.inlineEditField,
          dropdownSelectedValue: this.inlineEditActiveRow.opportunity_currency_value, // field name in the active row
          apiDataSelectedKeyName: this.inlineEditField, // key of value to be finally applied in the active row
          inputType: "deci-number", // type of text to be updated - text or number
          dropdownShowValue:(this.inlineEditActiveRow['currency_code'] !='' && this.inlineEditActiveRow['currency_code'] !=null) ?this.inlineEditActiveRow['currency_code'] :"USD",
          decimalPart: this.decimalPartsCache['opportunityValueMillion'] || 13,
          maxDigits: this.maxDigitsAllowedCache['opportunityValueMillion'] || 13
        };

    }


    this.inlineEditPopupService.setInlineEditActiveDataSubject({
      editType: this.editType,
      dataArray: this.dataArray,
      dropdownConfig: this.dropdownConfig
    });

  }

  async inlineEditResponseFunction(inlineEditResponse){

    if(this.inlineEditField == 'New channel type'){
      if(this.channel_type_value != inlineEditResponse[this.dropdownConfig.apiDataSelectedKeyName]){
        this.channel_type_value = inlineEditResponse[this.dropdownConfig.apiDataSelectedKeyName]
        this.channel_type_id =  inlineEditResponse[this.dropdownConfig.apiDataUpdateKeyName]
        this.channel_type_ex=true;
      }
    }
    else if(this.inlineEditField == 'channel type'){
      if(this.dup_entered_Channel[this.index].notif_type_id != inlineEditResponse[this.dropdownConfig.apiDataUpdateKeyName]){
        this.opportunityService.updateChannel(this.opportunityId,this.dup_entered_Channel[this.index].notif_id,inlineEditResponse[this.dropdownConfig.apiDataUpdateKeyName],'type').then(async(res)=>{
            this.dup_entered_Channel[this.index].roles= inlineEditResponse[this.dropdownConfig.apiDataSelectedKeyName];
            this.dup_entered_Channel[this.index].notif_type_id = inlineEditResponse[this.dropdownConfig.apiDataUpdateKeyName];
            this.snackBar.open("Channel Type updated successfully!","Dismiss",{duration: this.opportunityService.mediumInterval,})
        },err=>{
          //console.log(err)
        })
      }
    }
    else if(this.inlineEditField =='New channel id'){
      if(this.channel_id_value!=inlineEditResponse[this.inlineEditField])
      {
        this.channel_id_value=inlineEditResponse[this.inlineEditField]
        this.channel_id_ex=true;
      
      }
    }
    else if(this.inlineEditField == 'channel id'){
      if(this.dup_entered_Channel[this.index].channel_id != inlineEditResponse[this.inlineEditField]){
        
        this.opportunityService.updateChannel(this.opportunityId,this.dup_entered_Channel[this.index].notif_id,inlineEditResponse[this.inlineEditField],'id').then(async (res) => {   
          this.dup_entered_Channel[this.index].channel_id = inlineEditResponse[this.inlineEditField];
          this.snackBar.open("Channel Id updated successfully!", "Dismiss", {
            duration: this.opportunityService.mediumInterval,
          });
        // this.activityDetail = await this.getDetail();
      }, err => {
        console.error(err);
        
      })
      }
    }
    else if(this.inlineEditField =='Planned hours comments'){
      if(this.comments_approvers!=inlineEditResponse[this.inlineEditField])
      {
        this.comments_approvers=inlineEditResponse[this.inlineEditField]
        this.is_comments=true
      }
    }
    else if (this.inlineEditField == 'Probability') {

      if (inlineEditResponse[this.dropdownConfig.apiDataSelectedKeyName] && this.overviewDetails.probability_perc_name != inlineEditResponse[this.dropdownConfig.apiDataSelectedKeyName])
       this.opportunityService.updateOpportunityProbability(this.overviewDetails.opportunity_id,inlineEditResponse[this.dropdownConfig.apiDataUpdateKeyName])
          .subscribe(async res => {
            this.tempOverviewDetails.probability_perc_name = inlineEditResponse[this.dropdownConfig.apiDataSelectedKeyName];
            this.tempOverviewDetails.probability_percentage = inlineEditResponse[this.dropdownConfig.apiDataUpdateKeyName];
              this.UtilityService.showMessage("Probability Updated Successfully",
              "Dismiss",
              this.opportunityService.mediumInterval)
          }, err => {
              this.UtilityService.showMessage(err,'dismiss');
          });

    }
    else if(this.inlineEditField == 'Bidtype') {
      if (inlineEditResponse[this.dropdownConfig.apiDataSelectedKeyName] && this.tempOverviewDetails.bid_type != inlineEditResponse[this.dropdownConfig.apiDataSelectedKeyName])
       this.opportunityService.updateOpportunityBidtype(this.tempOverviewDetails.opportunity_id,inlineEditResponse[this.dropdownConfig.apiDataUpdateKeyName])
          .subscribe(async res => {
            this.tempOverviewDetails.bid_type = inlineEditResponse[this.dropdownConfig.apiDataSelectedKeyName];
              this.UtilityService.showMessage("Bid Type Updated Successfully",
              "Dismiss",
              this.opportunityService.mediumInterval)
          }, err => {
              this.UtilityService.showMessage(err,'dismiss');
          });

    }
    else if(this.inlineEditField == 'Source') {
      if (inlineEditResponse[this.dropdownConfig.apiDataSelectedKeyName] && this.tempOverviewDetails.source_name != inlineEditResponse[this.dropdownConfig.apiDataSelectedKeyName])
       this.opportunityService.updateOpportunitySource(this.tempOverviewDetails.opportunity_id,inlineEditResponse[this.dropdownConfig.apiDataUpdateKeyName])
          .subscribe(async res => {
            this.tempOverviewDetails.source_name = inlineEditResponse[this.dropdownConfig.apiDataSelectedKeyName];
              this.UtilityService.showMessage("Source type Updated Successfully",
              "Dismiss",
              this.opportunityService.mediumInterval)
          }, err => {
              this.UtilityService.showMessage(err,'dismiss');
          });

    }
    else if(this.inlineEditField == 'Salesstatus') {
      let seqStatusCheck= await this.opportunityService.checkOpportunitySalesStatus(this.opportunityId,inlineEditResponse[this.dropdownConfig.apiDataUpdateKeyName])
      if(seqStatusCheck['messType'] == 'E') {
       this.UtilityService.showMessage(seqStatusCheck['mesText'], 'Dismiss');
       return ;
      } 
      
      
    let opportunity_value_to_be_copied=await this.opportunityService.checkIssuePoAndPoValue(this.opportunityId,inlineEditResponse[this.dropdownConfig.apiDataUpdateKeyName],this.overviewDetails?.issue_po_id);
    if(opportunity_value_to_be_copied["isApplicable"]){
      this.overviewDetails['po_no']=opportunity_value_to_be_copied['poNumber'] || '';
      this.overviewDetails['po_date']=opportunity_value_to_be_copied['poDate'] || '';
      this.overviewDetails['po_value']=opportunity_value_to_be_copied['poValue'] || '';
      // return ;
    }
    let poValueCheck=await this.opportunityService.checkPoValueMatch(this.opportunityId,inlineEditResponse[this.dropdownConfig.apiDataUpdateKeyName],null,this.overviewDetails['po_value'] || null)
      if (inlineEditResponse[this.dropdownConfig.apiDataSelectedKeyName] && this.tempOverviewDetails.sales_status_name != inlineEditResponse[this.dropdownConfig.apiDataSelectedKeyName])
      if(this.stagewiseFieldConfig){
        await this.opportunityService.getStagewiseFormFieldConfig( inlineEditResponse[this.dropdownConfig.apiDataUpdateKeyName]).then((res:any) => {
          this.tempstagewiseFormFieldData = JSON.parse(res.messData[0].stagewise_field_config)
          this.stagewiseFormFieldData=_.filter( this.tempstagewiseFormFieldData,function(item:any){
           return (item.changeIndicator==true && item.is_mandant)
          })
          console.log(this.stagewiseFormFieldData)
        },
          err => {
            console.log(err)
          })
          let mand_fields=false
          for (let item of this.stagewiseFormFieldData) {
            console.log(this.overviewDetails)
            if(this.overviewDetails[item.data_field]=="" || this.overviewDetails[item.data_field]==null || this.overviewDetails[item.data_field]==0 ||  this.overviewDetails['dataSelected']=="00:00:00 00:00:00")
          {
            mand_fields=true;
            break;
          }      else if(item.data_field=="opportunity_value"){
                
            let opp_value=this.overviewDetails[item.data_field]
            console.log(opp_value[0].value)
            if(opp_value[0].value==0)
            {
              mand_fields=true;
              break;
            }

          }

          }
          console.log(mand_fields)
        this.opportunityService.checkNdUpdateOpportunityStatus( this.opportunityId, inlineEditResponse[this.dropdownConfig.apiDataUpdateKeyName], this.overviewDetails,this.isQuoteEnabled ).then(async res => {
      
          if(res['messType']!="E")
          { 
            const updateAllowed = this.isQuoteEnabled ? await this.resolveQuoteOpportunityIntegration(inlineEditResponse[this.dropdownConfig.apiDataUpdateKeyName]) : true;
              
            if (updateAllowed) {
              if(mand_fields==true){
                this.UtilityService.showMessage("Please fill Value to update status!","Dismiss")
                let changedStatus= inlineEditResponse[this.dropdownConfig.apiDataUpdateKeyName]
                this.openEditScreenOnStatusChange(changedStatus)
              } else {
                if(!poValueCheck["is_allowed"]){
                  if(poValueCheck['type']=="WARN"){
                  let copyConfirm = await this.UtilityService .openConfirmationSweetAlertWithCustom(
                    poValueCheck['textMsg'],
                    "Do you wish to close the opportunity with Opportunity Value and PO Value difference?"
                  );
            
                  if (!copyConfirm) {
                    return true;
                  }
                }
                else{
                  this.UtilityService.showMessage(poValueCheck['textMsg'], 'Dismiss',3000);
                  return ;
                }
                }
                let changed_status  = _.where(this.salesStatusMaster,{id:  inlineEditResponse[this.dropdownConfig.apiDataUpdateKeyName]})
                if(changed_status[0]['is_end_form_needed']==1){
                  if (inlineEditResponse[this.dropdownConfig.apiDataUpdateKeyName] == 13 && this.opportunityProjectDetails && this.opportunityProjectDetails.length) return this._toaster.showWarning('Cannot Change Status to Lost if Project Integrated', '', 3000)
                await this.opportunityService.openWinLossForm(this.overviewDetails , changed_status[0]['name']).then((res)=>{
                  if(res['messType'])
                  {
                    if(res!="" && res["result"]!="")
                    this.opportunityService.updateOpportunitySalesStatus(this.opportunityId,inlineEditResponse[this.dropdownConfig.apiDataUpdateKeyName]).subscribe(async (res)=>{
                      if(res['messType']=="S"){
                      if(inlineEditResponse[this.dropdownConfig.apiDataUpdateKeyName] == 17){
                        this.overviewDetails['isOpen'] = 0;
                        this.editAccess = this.opportunityService.checkUserforEditAccess(this.overviewDetails.sales_unit);
                        let isDisableEdit = await this.opportunityService.isDisableEdit(this.overviewDetails);
                        let reOpenAccess = await this.opportunityService.reOpenAccess(this.overviewDetails);
                        this.isDisableEdit = isDisableEdit;
                        this.reOpenAccess = reOpenAccess;
                      }

                      this.UtilityService.showMessage("Status Changed successfully","Dismiss")         
                      this.tempOverviewDetails.sales_status_id = inlineEditResponse[this.dropdownConfig.apiDataUpdateKeyName]
                      this.tempOverviewDetails.sales_status_name= inlineEditResponse[this.dropdownConfig.apiDataSelectedKeyName]

                      if(this.isStatusStageMappingAllowed){
                        const proposalStatus = this.getProposalStatus(inlineEditResponse[this.dropdownConfig.apiDataUpdateKeyName]);
                        this.tempOverviewDetails.proposal_status_id = proposalStatus.proposal_status_id;
                        this.tempOverviewDetails.proposal_status_name = proposalStatus.proposal_status_name;
                      }
                      this.projectCreationAccess = await this.projectAccessCheck() 
                      await this.fetchOpportunityOverview();
                      if(this.childRestriction){
                        if(this.overviewDetails['parent_opportunity'] !=null || this.overviewDetails['parent_opportunity'] !=undefined || this.overviewDetails['parent_opportunity'] !='' ){
                          this.projectCreationAccess=false
                        }
                      }
                      this.projectView = await  this.projectBtnviewaccess()
                      this.opportunityService.checkAdaptTemplate(this.opportunityId,inlineEditResponse[this.dropdownConfig.apiDataUpdateKeyName]).subscribe((res)=>{
                        if(res['messType'] == 'A') {
                          this.UtilityService.openConfirmationSweetAlertWithCustom("Create Duplicate Activities?", "Do you wish to create Activities based on status template which is already available ?")
                          .then((copyConfirm) => {
          
                            if (copyConfirm)
                             {
                              this.opportunityService.adaptStatusTemplate(this.opportunityId,inlineEditResponse[this.dropdownConfig.apiDataUpdateKeyName]).subscribe((res)=>{
                                if(res['messType'] == 'S') {
                                  this.UtilityService.showMessage('Activities created based on Opportunity status ', 'Dismiss');
                                } else {
                                  this.UtilityService.showMessage('Error while creating activities based on Opportunity status ', 'Dismiss');
                               
                                }
                              })
                             }
                             
          
                            else{
                              return true
                            }
          
                        })
                        } else if(res['messType'] == 'S'){
                          this.opportunityService.adaptStatusTemplate(this.opportunityId,inlineEditResponse[this.dropdownConfig.apiDataUpdateKeyName]).subscribe((res)=>{
                            if(res['messType'] == 'S') {
                              this.UtilityService.showMessage('Activities created based on Opportunity status ', 'Dismiss');
                            } else {
                              this.UtilityService.showMessage('Error while creating activities based on Opportunity status ', 'Dismiss');
                           
                            }
                          })
                        }
                      })
                      this.fetchQuoteFydata()
                      this.opportunityService.getStatusLogForOpp(parseInt(this.opportunityId)).subscribe((res:any)=>{
                        this.statusLogObservable.next(res.data);
                      })}
                      else{
                        this.UtilityService.showMessage(res['mesText'],"Dismiss")
                      }
                    })
                  }
                  else
                  {
                    if(res!="" && res["result"]){

                      this.UtilityService.showMessage(res['result'],"Dismiss")
                     
                    }
               
                  }
                })}
                else{
                  this.opportunityService.updateOpportunitySalesStatus(this.opportunityId,inlineEditResponse[this.dropdownConfig.apiDataUpdateKeyName]).subscribe(async (res)=>{
                    if(res['messType']=="S"){
                    if(inlineEditResponse[this.dropdownConfig.apiDataUpdateKeyName] == 17){
                      this.overviewDetails['isOpen'] = 0;
                      this.editAccess = this.opportunityService.checkUserforEditAccess(this.overviewDetails.sales_unit);
                      let isDisableEdit = await this.opportunityService.isDisableEdit(this.overviewDetails);
                      let reOpenAccess = await this.opportunityService.reOpenAccess(this.overviewDetails);
                      this.isDisableEdit = isDisableEdit;
                      this.reOpenAccess = reOpenAccess;
                    }
                    this.UtilityService.showMessage("Status Changed successfully","Dismiss")
                    this.tempOverviewDetails.sales_status_id = inlineEditResponse[this.dropdownConfig.apiDataUpdateKeyName]
                    this.tempOverviewDetails.sales_status_name= inlineEditResponse[this.dropdownConfig.apiDataSelectedKeyName]

                    if(this.isStatusStageMappingAllowed){
                      const proposalStatus = this.getProposalStatus(inlineEditResponse[this.dropdownConfig.apiDataUpdateKeyName]);
                      this.tempOverviewDetails.proposal_status_id = proposalStatus.proposal_status_id;
                      this.tempOverviewDetails.proposal_status_name = proposalStatus.proposal_status_name;
                    }

                    this.projectCreationAccess = await this.projectAccessCheck() 
                    if(this.childRestriction){
                      if(this.overviewDetails['parent_opportunity'] !=null || this.overviewDetails['parent_opportunity'] !=undefined || this.overviewDetails['parent_opportunity'] !='' ){
                        this.projectCreationAccess=false
                      }
                    }
                    this.projectView = await  this.projectBtnviewaccess()
                    await this.fetchOpportunityOverview();
                    this.opportunityService.checkAdaptTemplate(this.opportunityId,inlineEditResponse[this.dropdownConfig.apiDataUpdateKeyName]).subscribe((res)=>{
                      if(res['messType'] == 'A') {
                        this.UtilityService.openConfirmationSweetAlertWithCustom("Create Duplicate Activities?", "Do you wish to create Activities based on status template which is already available ?")
                        .then((copyConfirm) => {
        
                          if (copyConfirm)
                           {
                            this.opportunityService.adaptStatusTemplate(this.opportunityId,inlineEditResponse[this.dropdownConfig.apiDataUpdateKeyName]).subscribe((res)=>{
                              if(res['messType'] == 'S') {
                                this.UtilityService.showMessage('Activities created based on Opportunity status ', 'Dismiss');
                              } else {
                                this.UtilityService.showMessage('Error while creating activities based on Opportunity status ', 'Dismiss');
                             
                              }
                            })
                           }
        
                          else{
                            return true
                          }
        
                      })
                      } else if(res['messType'] == 'S'){
                        this.opportunityService.adaptStatusTemplate(this.opportunityId,inlineEditResponse[this.dropdownConfig.apiDataUpdateKeyName]).subscribe((res)=>{
                          if(res['messType'] == 'S') {
                            this.UtilityService.showMessage('Activities created based on Opportunity status ', 'Dismiss');
                          } else {
                            this.UtilityService.showMessage('Error while creating activities based on Opportunity status ', 'Dismiss');
                         
                          }
                        })
                      }
                    })
                    this.fetchQuoteFydata()
                    this.opportunityService.getStatusLogForOpp(parseInt(this.opportunityId)).subscribe((res:any)=>{
                      this.statusLogObservable.next(res.data);
                    })}
                    else{
                      this.UtilityService.showMessage(res['mesText'],"Dismiss")
                    }
                  })
                  
                }

              
              }
            }
          }



      })
        }
          else {
            if(!poValueCheck["is_allowed"]){
              if(poValueCheck['type']=="WARN"){
              let copyConfirm = await this.UtilityService .openConfirmationSweetAlertWithCustom(
                poValueCheck['textMsg'],
                "Do you wish to close the opportunity with Opportunity Value and PO Value difference?"
              );
        
              if (!copyConfirm) {
                return true;
              }
            }
            else{
              this.UtilityService.showMessage(poValueCheck['textMsg'], 'Dismiss',3000);
              return ;
            }
            }
    this.opportunityService.checkNdUpdateOpportunityStatus( this.opportunityId, inlineEditResponse[this.dropdownConfig.apiDataUpdateKeyName], this.overviewDetails,this.isQuoteEnabled ).then(async res => {
  
      if(res['messType']!="E")
      { 
          console.log("VAl Changes")

          const updateAllowed = this.isQuoteEnabled ? await this.resolveQuoteOpportunityIntegration(inlineEditResponse[this.dropdownConfig.apiDataUpdateKeyName]) : true;

          if (updateAllowed) {

            let changed_status  = _.where(this.salesStatusMaster,{id:  inlineEditResponse[this.dropdownConfig.apiDataUpdateKeyName]})
            if(changed_status[0]['is_end_form_needed']==1){
              if (inlineEditResponse[this.dropdownConfig.apiDataUpdateKeyName] == 13 && this.opportunityProjectDetails && this.opportunityProjectDetails.length) return this._toaster.showWarning('Cannot Change Status to Lost if Project Integrated', '', 3000)
            await this.opportunityService.openWinLossForm(this.overviewDetails , changed_status[0]['name']).then((res)=>{
              if(res['messType'])
              {
                if(res!="" && res["result"]!="")
                this.opportunityService.updateOpportunitySalesStatus(this.opportunityId,inlineEditResponse[this.dropdownConfig.apiDataUpdateKeyName]).subscribe(async (res)=>{
                  if(res['messType']=="S"){
                  if(inlineEditResponse[this.dropdownConfig.apiDataUpdateKeyName] == 17){
                    this.overviewDetails['isOpen'] = 0;
                    this.editAccess = this.opportunityService.checkUserforEditAccess(this.overviewDetails.sales_unit);
                    let isDisableEdit = await this.opportunityService.isDisableEdit(this.overviewDetails);
                    let reOpenAccess = await this.opportunityService.reOpenAccess(this.overviewDetails);
                    this.isDisableEdit = isDisableEdit;
                    this.reOpenAccess = reOpenAccess;
                  }
                  this.UtilityService.showMessage("Status Changed successfully","Dismiss")
                  this.tempOverviewDetails.sales_status_id = inlineEditResponse[this.dropdownConfig.apiDataUpdateKeyName]
                  this.tempOverviewDetails.sales_status_name= inlineEditResponse[this.dropdownConfig.apiDataSelectedKeyName]
                  if(this.isStatusStageMappingAllowed){
                    const proposalStatus = this.getProposalStatus(inlineEditResponse[this.dropdownConfig.apiDataUpdateKeyName]);
                    this.tempOverviewDetails.proposal_status_id = proposalStatus.proposal_status_id;
                    this.tempOverviewDetails.proposal_status_name = proposalStatus.proposal_status_name;
                  }
                  this.projectCreationAccess = await this.projectAccessCheck() 
                  await this.fetchOpportunityOverview();
                  if(this.childRestriction){
                    if(this.overviewDetails['parent_opportunity'] !=null || this.overviewDetails['parent_opportunity'] !=undefined || this.overviewDetails['parent_opportunity'] !='' ){
                      this.projectCreationAccess=false
                    }
                  }
                  this.projectView = await  this.projectBtnviewaccess()
                  this.opportunityService.checkAdaptTemplate(this.opportunityId,inlineEditResponse[this.dropdownConfig.apiDataUpdateKeyName]).subscribe((res)=>{
                    if(res['messType'] == 'A') {
                      this.UtilityService.openConfirmationSweetAlertWithCustom("Create Duplicate Activities?", "Do you wish to create Activities based on status template which is already available ?")
                      .then((copyConfirm) => {
      
                        if (copyConfirm)
                         {
                          this.opportunityService.adaptStatusTemplate(this.opportunityId,inlineEditResponse[this.dropdownConfig.apiDataUpdateKeyName]).subscribe((res)=>{
                            if(res['messType'] == 'S') {
                              this.UtilityService.showMessage('Activities created based on Opportunity status ', 'Dismiss');
                            } else {
                              this.UtilityService.showMessage('Error while creating activities based on Opportunity status ', 'Dismiss');
                           
                            }
                          })
                         }
      
                        else{
                          return true
                        }
      
                    })
                    } else if(res['messType'] == 'S'){
                      this.opportunityService.adaptStatusTemplate(this.opportunityId,inlineEditResponse[this.dropdownConfig.apiDataUpdateKeyName]).subscribe((res)=>{
                        if(res['messType'] == 'S') {
                          this.UtilityService.showMessage('Activities created based on Opportunity status ', 'Dismiss');
                        } else {
                          this.UtilityService.showMessage('Error while creating activities based on Opportunity status ', 'Dismiss');
                       
                        }
                      })
                    }
                  })
                  this.fetchQuoteFydata()
                  this.opportunityService.getStatusLogForOpp(parseInt(this.opportunityId)).subscribe((res:any)=>{
                    this.statusLogObservable.next(res.data);
                  })}
                  else{
                    this.UtilityService.showMessage(res['mesText'],"Dismiss")
                  }
                })
              }
              else
              {
                if(res!="" && res["result"]){

                  this.UtilityService.showMessage(res['result'],"Dismiss")
                 
                }
           
              }
            })}
            else{
              this.opportunityService.updateOpportunitySalesStatus(this.opportunityId,inlineEditResponse[this.dropdownConfig.apiDataUpdateKeyName]).subscribe(async (res)=>{
                if(res['messType']=="S"){
                if(inlineEditResponse[this.dropdownConfig.apiDataUpdateKeyName] == 17){
                  this.overviewDetails['isOpen'] = 0;
                  this.editAccess = this.opportunityService.checkUserforEditAccess(this.overviewDetails.sales_unit);
                  let isDisableEdit = await this.opportunityService.isDisableEdit(this.overviewDetails);
                  let reOpenAccess = await this.opportunityService.reOpenAccess(this.overviewDetails);
                  this.isDisableEdit = isDisableEdit;
                  this.reOpenAccess = reOpenAccess;
                }
                this.UtilityService.showMessage("Status Changed successfully","Dismiss")
                this.tempOverviewDetails.sales_status_id = inlineEditResponse[this.dropdownConfig.apiDataUpdateKeyName]
                this.tempOverviewDetails.sales_status_name= inlineEditResponse[this.dropdownConfig.apiDataSelectedKeyName]
                if(this.isStatusStageMappingAllowed){
                  const proposalStatus = this.getProposalStatus(inlineEditResponse[this.dropdownConfig.apiDataUpdateKeyName]);
                  this.tempOverviewDetails.proposal_status_id = proposalStatus.proposal_status_id;
                  this.tempOverviewDetails.proposal_status_name = proposalStatus.proposal_status_name;
                }
                this.projectCreationAccess = await this.projectAccessCheck() 
                await this.fetchOpportunityOverview();
                if(this.childRestriction){
                  if(this.overviewDetails['parent_opportunity'] !=null || this.overviewDetails['parent_opportunity'] !=undefined || this.overviewDetails['parent_opportunity'] !='' ){
                    this.projectCreationAccess=false
                  }
                }
                this.projectView = await  this.projectBtnviewaccess()
                this.opportunityService.checkAdaptTemplate(this.opportunityId,inlineEditResponse[this.dropdownConfig.apiDataUpdateKeyName]).subscribe((res)=>{
                  if(res['messType'] == 'A') {
                    this.UtilityService.openConfirmationSweetAlertWithCustom("Create Duplicate Activities?", "Do you wish to create Activities based on status template which is already available ?")
                    .then((copyConfirm) => {
    
                      if (copyConfirm)
                       {
                        this.opportunityService.adaptStatusTemplate(this.opportunityId,inlineEditResponse[this.dropdownConfig.apiDataUpdateKeyName]).subscribe((res)=>{
                          if(res['messType'] == 'S') {
                            this.UtilityService.showMessage('Activities created based on Opportunity status ', 'Dismiss');
                          } else {
                            this.UtilityService.showMessage('Error while creating activities based on Opportunity status ', 'Dismiss');
                         
                          }
                        })
                       }
    
                      else{
                        return true
                      }
    
                  })
                  } else if(res['messType'] == 'S'){
                    this.opportunityService.adaptStatusTemplate(this.opportunityId,inlineEditResponse[this.dropdownConfig.apiDataUpdateKeyName]).subscribe((res)=>{
                      if(res['messType'] == 'S') {
                        this.UtilityService.showMessage('Activities created based on Opportunity status ', 'Dismiss');
                      } else {
                        this.UtilityService.showMessage('Error while creating activities based on Opportunity status ', 'Dismiss');
                     
                      }
                    })
                  }
                })
                this.fetchQuoteFydata()
                this.opportunityService.getStatusLogForOpp(parseInt(this.opportunityId)).subscribe((res:any)=>{
                  this.statusLogObservable.next(res.data);
                })}
                else{
                  this.UtilityService.showMessage(res['mesText'],"Dismiss")
                }
              })
            }
            
            
          }
      }



  })
        }
    }
    else if(this.inlineEditField == 'Presalesstatus') {
      let label = this.labelForm.transform('proposalStatus', this.formFieldData, 'label');
      if (inlineEditResponse[this.dropdownConfig.apiDataSelectedKeyName] && this.tempOverviewDetails.proposal_status_name != inlineEditResponse[this.dropdownConfig.apiDataSelectedKeyName])
       this.opportunityService.updateOpportunityPreSalesStatus(this.tempOverviewDetails.opportunity_id,inlineEditResponse[this.dropdownConfig.apiDataUpdateKeyName])
          .subscribe(async res => {
            this.tempOverviewDetails.proposal_status_name = inlineEditResponse[this.dropdownConfig.apiDataSelectedKeyName];
            this.tempOverviewDetails.proposal_status_id = inlineEditResponse[this.dropdownConfig.apiDataUpdateKeyName];
              this.UtilityService.showMessage(`${label} Updated Successfully`,
              "Dismiss",
              this.opportunityService.mediumInterval)
          }, err => {
              this.UtilityService.showMessage(err,'dismiss');
          });

    }
    else if(this.inlineEditField == 'Servicetype') {
      if (inlineEditResponse[this.dropdownConfig.apiDataSelectedKeyName] && this.tempOverviewDetails.service_type_name != inlineEditResponse[this.dropdownConfig.apiDataSelectedKeyName])
       this.opportunityService.updateServiceType(this.tempOverviewDetails.opportunity_id,inlineEditResponse[this.dropdownConfig.apiDataUpdateKeyName])
          .subscribe(async res => {
            
            if(!res['err']){
              this.tempOverviewDetails.service_type_name = inlineEditResponse[this.dropdownConfig.apiDataSelectedKeyName];
              this.tempOverviewDetails.service_type_id = inlineEditResponse[this.dropdownConfig.apiDataUpdateKeyName];
              this.UtilityService.showMessage("Service Type Updated Successfully",
              "Dismiss",
              this.opportunityService.mediumInterval)
            }
            else{
              this.UtilityService.showMessage(res['msg'],
                "Dismiss",
                this.opportunityService.mediumInterval)
            }

          }, err => {
              this.UtilityService.showMessage(err,'dismiss');
          });

    }
    else if(this.inlineEditField == 'Salesowner'){
      if (inlineEditResponse[this.dropdownConfig.apiDataSelectedKeyName] && this.tempOverviewDetails.sales_owner != inlineEditResponse[this.dropdownConfig.apiDataUpdateKeyName])
       this.opportunityService.updateOpportunitySalesOwner(this.tempOverviewDetails.opportunity_id,inlineEditResponse[this.dropdownConfig.apiDataUpdateKeyName])
          .then(async res => {
            if(res!="F")
            {
              this.tempOverviewDetails.sales_owner = inlineEditResponse[this.dropdownConfig.apiDataUpdateKeyName];
                this.UtilityService.showMessage("Sales Owner Updated Successfully",
                "Dismiss",
                this.opportunityService.mediumInterval)
            }
          }, err => {
              this.UtilityService.showMessage(err,'dismiss');
          });
    }
    else if(this.inlineEditField == 'PresalesSPOC'){
      if (inlineEditResponse[this.dropdownConfig.apiDataSelectedKeyName] && this.tempOverviewDetails.opportunity_spoc_oid != inlineEditResponse[this.dropdownConfig.apiDataUpdateKeyName])
       this.opportunityService.updateOpportunityPresalesSPOC(this.tempOverviewDetails.opportunity_id,inlineEditResponse[this.dropdownConfig.apiDataUpdateKeyName])
          .subscribe(async res => {
            this.tempOverviewDetails.opportunity_spoc_oid = inlineEditResponse[this.dropdownConfig.apiDataUpdateKeyName];
              this.UtilityService.showMessage("PreSales SPOC Updated Successfully",
              "Dismiss",
              this.opportunityService.mediumInterval)
          }, err => {
              this.UtilityService.showMessage(err,'dismiss');
          });
    }
    else if(this.inlineEditField == 'MarketingSPOC'){
      if (inlineEditResponse[this.dropdownConfig.apiDataSelectedKeyName] && this.tempOverviewDetails.marketing_spoc != inlineEditResponse[this.dropdownConfig.apiDataUpdateKeyName])
       this.opportunityService.updateOpportunitymarketingSPOC(this.tempOverviewDetails.opportunity_id,inlineEditResponse[this.dropdownConfig.apiDataUpdateKeyName])
          .subscribe(async res => {
            this.tempOverviewDetails.marketing_spoc = inlineEditResponse[this.dropdownConfig.apiDataUpdateKeyName];
              this.UtilityService.showMessage("Marketing SPOC Updated Successfully",
              "Dismiss",
              this.opportunityService.mediumInterval)
          }, err => {
              this.UtilityService.showMessage(err,'dismiss');
          });
    }
    else if(this.inlineEditField == 'InsideSalesSPOC'){
      if (inlineEditResponse[this.dropdownConfig.apiDataSelectedKeyName] && this.tempOverviewDetails.inside_sales_spoc != inlineEditResponse[this.dropdownConfig.apiDataUpdateKeyName])
       this.opportunityService.updateOpportunityinsidesalesSPOC(this.tempOverviewDetails.opportunity_id,inlineEditResponse[this.dropdownConfig.apiDataUpdateKeyName])
          .subscribe(async res => {
            this.tempOverviewDetails.inside_sales_spoc = inlineEditResponse[this.dropdownConfig.apiDataUpdateKeyName];
              this.UtilityService.showMessage("Inside Sales SPOC Updated Successfully",
              "Dismiss",
              this.opportunityService.mediumInterval)
          }, err => {
              this.UtilityService.showMessage(err,'dismiss');
          });
    }
    else if(this.inlineEditField == 'Start date'){
      let UpdatedStartDate = moment(inlineEditResponse[this.dropdownConfig.apiDataSelectedKeyName]).format('YYYY-MM-DD');
      if (inlineEditResponse[this.dropdownConfig.apiDataSelectedKeyName] != "Invalid Date" && this.tempOverviewDetails.processing_start_date != UpdatedStartDate )
       this.opportunityService.updateOpportunityStartDate(this.tempOverviewDetails.opportunity_id,UpdatedStartDate)
          .subscribe(async res => {
            this.tempOverviewDetails.processing_start_date = moment(UpdatedStartDate).format("YYYY-MM-DD[T]HH:mm:ss.SSS[Z]");;
              this.UtilityService.showMessage("Start Date Updated Successfully",
              "Dismiss",
              this.opportunityService.mediumInterval)
          }, err => {
              this.UtilityService.showMessage(err,'dismiss');
          });

    }
    else if(this.inlineEditField == 'End date'){
      let UpdatedEndDate = moment(inlineEditResponse[this.dropdownConfig.apiDataSelectedKeyName]).format('YYYY-MM-DD');

      if (inlineEditResponse[this.dropdownConfig.apiDataSelectedKeyName] != "Invalid Date" && this.tempOverviewDetails.processing_start_date != UpdatedEndDate ){
        //determine pipe
        let pipeline;
        let pipeline_name;
        let determinePipe = (moment(UpdatedEndDate).month() + 1) - (moment().month() + 1);
        if (determinePipe == 0) { pipeline = 1; pipeline_name = "P0"; }
        else if (determinePipe == 1) { pipeline = 2; pipeline_name = "P1"; }
        else if (determinePipe == 2) { pipeline = 3; pipeline_name = "P2"; }
        else if (determinePipe == 3) { pipeline = 4; pipeline_name = "P3"; }
        else if (determinePipe == 4) { pipeline = 5; pipeline_name = "P4"; }
        else if (determinePipe == 5) { pipeline = 6; pipeline_name = "P5"; }
        else if (determinePipe > 5) { pipeline = 9; pipeline_name = "Future"; }

        

        this.opportunityService.updateOpportunityEndDate(this.tempOverviewDetails.opportunity_id,UpdatedEndDate)
          .subscribe(async res => {
            await this.opportunityService.updatePipeline(this.tempOverviewDetails.opportunity_id,pipeline,pipeline_name);
            this.tempOverviewDetails.funnel_pipe_status = pipeline_name
            this.tempOverviewDetails.processing_end_date = moment(UpdatedEndDate).format("YYYY-MM-DD[T]HH:mm:ss.SSS[Z]");;
              this.UtilityService.showMessage("End Date Updated Successfully",
              "Dismiss",
              this.opportunityService.mediumInterval)
          }, err => {
              this.UtilityService.showMessage(err,'dismiss');
          });
      }
       

    }
    else if(this.inlineEditField == 'Delivery Start Date'){
      let UpdatedStartDate = moment(inlineEditResponse[this.dropdownConfig.apiDataSelectedKeyName]).format('YYYY-MM-DD');
      if (inlineEditResponse[this.dropdownConfig.apiDataSelectedKeyName] != "Invalid Date" && this.tempOverviewDetails.deliveryStartDate != UpdatedStartDate )
         {
      if(this.isActiveQuoteExists){
        this.confirmSweetAlert("Changing Delivery Date will make the Quote InActive").then((confirm) => {
          if (confirm.value) {
            console.log(confirm.value)
            this._quoteService.updateInActiveQuoteInOpportunity(this.tempOverviewDetails.opportunity_id)
            .pipe(takeUntil(this._onDestroy))
            .subscribe(res => {
              if (res["messType"] == "S") {

                this.isActiveQuoteExists = false;

                this.opportunityService.updateOpportunityDeliveryStartDate(this.tempOverviewDetails.opportunity_id, UpdatedStartDate)
                  .subscribe(async res => {
                    this.tempOverviewDetails.deliveryStartDate = moment(UpdatedStartDate).format("YYYY-MM-DD[T]HH:mm:ss.SSS[Z]");;
                    let label = this.labelForm.transform('deliveryStartDate', this.formFieldData, 'label');
                    this.UtilityService.showMessage(label + " Updated Successfully",
                      "Dismiss",
                      this.opportunityService.mediumInterval)
                  }, err => {
                    this.UtilityService.showMessage(err, 'dismiss', this.opportunityService.longInterval);
                  });

              }

              if (res["messType"] == "E")
                this._toaster.showError("Error", "Error in Updating In Active Quote", this.opportunityService.longInterval);

            },
              err => {
              console.log(err);
              this._toaster.showError("Error", "Error in Updating In Active Quote", this.opportunityService.longInterval);
              });
              
     
          }
        })
      }
      else{
        this.opportunityService.updateOpportunityDeliveryStartDate(this.tempOverviewDetails.opportunity_id,UpdatedStartDate)
        .subscribe(async res => {
          this.tempOverviewDetails.deliveryStartDate = moment(UpdatedStartDate).format("YYYY-MM-DD[T]HH:mm:ss.SSS[Z]");;
          let label = this.labelForm.transform('deliveryStartDate', this.formFieldData, 'label');
          this.UtilityService.showMessage(label +" Updated Successfully",
            "Dismiss",
            this.opportunityService.mediumInterval)
        }, err => {
            this.UtilityService.showMessage(err,'dismiss');
        });
      }
      }

    }
    else if(this.inlineEditField == 'Delivery Finish Date'){
      let UpdatedEndDate = moment(inlineEditResponse[this.dropdownConfig.apiDataSelectedKeyName]).format('YYYY-MM-DD');

      if (inlineEditResponse[this.dropdownConfig.apiDataSelectedKeyName] != "Invalid Date" && this.tempOverviewDetails.deliveryFinishDate != UpdatedEndDate ){
        if(this.isActiveQuoteExists){
          this.confirmSweetAlert("Changing Delivery Date will make the Quote InActive").then((confirm) => {
            if (confirm.value) {
              console.log(confirm.value)
              this._quoteService.updateInActiveQuoteInOpportunity(this.tempOverviewDetails.opportunity_id)
              .pipe(takeUntil(this._onDestroy))
              .subscribe(res => {
                if (res["messType"] == "S") {

                  this.isActiveQuoteExists = false;

                  this.opportunityService.updateOpportunityDeliveryEndDate(this.tempOverviewDetails.opportunity_id, UpdatedEndDate)
                    .subscribe(async res => {
                      this.tempOverviewDetails.deliveryFinishDate = moment(UpdatedEndDate).format("YYYY-MM-DD[T]HH:mm:ss.SSS[Z]");;
                      let label = this.labelForm.transform('deliveryFinishDate', this.formFieldData, 'label');
                      this.UtilityService.showMessage(label + " Updated Successfully",
                        "Dismiss",
                        this.opportunityService.mediumInterval)
                    }, err => {
                      this.UtilityService.showMessage(err, 'dismiss', this.opportunityService.longInterval);
                    });

                }
                if (res["messType"] == "E")
                  this._toaster.showError("Error", "Error in Updating In Active Quote", this.opportunityService.longInterval);
  
              },
                err => {
                console.log(err);
                this._toaster.showError("Error", "Error in Updating In Active Quote", this.opportunityService.longInterval);
                });
                
       
            }
          })
        }
        else{
        this.opportunityService.updateOpportunityDeliveryEndDate(this.tempOverviewDetails.opportunity_id,UpdatedEndDate)
          .subscribe(async res => {
              this.tempOverviewDetails.deliveryFinishDate = moment(UpdatedEndDate).format("YYYY-MM-DD[T]HH:mm:ss.SSS[Z]");;
              let label = this.labelForm.transform('deliveryFinishDate', this.formFieldData, 'label');
              this.UtilityService.showMessage(label + " Updated Successfully",
              "Dismiss",
              this.opportunityService.mediumInterval)
          }, err => {
              this.UtilityService.showMessage(err,'dismiss');
          });
      }
    }  

    }
    else if(this.inlineEditField == 'RFP release date'){
      let UpdatedRFPDate = moment(inlineEditResponse[this.dropdownConfig.apiDataSelectedKeyName]).format('YYYY-MM-DD');
      if (inlineEditResponse[this.dropdownConfig.apiDataSelectedKeyName] && this.tempOverviewDetails.rfp_date != UpdatedRFPDate)
       this.opportunityService.updateOpportunityRFPReleaseDate(this.tempOverviewDetails.opportunity_id,UpdatedRFPDate)
          .subscribe(async res => {
            this.tempOverviewDetails.rfp_date = moment(UpdatedRFPDate).format("YYYY-MM-DD[T]HH:mm:ss.SSS[Z]");
              this.UtilityService.showMessage("RFP release date Updated Successfully",
              "Dismiss",
              this.opportunityService.mediumInterval)
          }, err => {
              this.UtilityService.showMessage(err,'dismiss');
          });

    }
    else if(this.inlineEditField == 'Proposal date'){
      let UpdatedProposalDate = moment(inlineEditResponse[this.dropdownConfig.apiDataSelectedKeyName]).format('YYYY-MM-DD');
      if (inlineEditResponse[this.dropdownConfig.apiDataSelectedKeyName] && this.tempOverviewDetails.rfp_date != UpdatedProposalDate)
       this.opportunityService.updateOpportunityProposalSubDate(this.tempOverviewDetails.opportunity_id,UpdatedProposalDate)
          .subscribe(async res => {
            this.tempOverviewDetails.proposal_submission_date = moment(UpdatedProposalDate).format("YYYY-MM-DD[T]HH:mm:ss.SSS[Z]");
              this.UtilityService.showMessage("Submission to Customer date Updated Successfully",
              "Dismiss",
              this.opportunityService.mediumInterval)
          }, err => {
              this.UtilityService.showMessage(err,'dismiss');
          });

    }
    else if(this.inlineEditField == 'Proposal Sales Date'){
      let UpdatedProposalDate = moment(inlineEditResponse[this.dropdownConfig.apiDataSelectedKeyName]).format('YYYY-MM-DD');
      if (inlineEditResponse[this.dropdownConfig.apiDataSelectedKeyName] && this.tempOverviewDetails.rfp_date != UpdatedProposalDate)
       this.opportunityService.updateOpportunityProposalSalesDate(this.tempOverviewDetails.opportunity_id,UpdatedProposalDate)
          .subscribe(async res => {
            this.tempOverviewDetails.sales_proposal_date = moment(UpdatedProposalDate).format("YYYY-MM-DD[T]HH:mm:ss.SSS[Z]");
              this.UtilityService.showMessage("Submission to Sales date Updated Successfully",
              "Dismiss",
              this.opportunityService.mediumInterval)
          }, err => {
              this.UtilityService.showMessage(err,'dismiss');
          });

    }
    else if(this.inlineEditField == 'Opportunity name'){
      if (this.inlineEditActiveRow.opportunity_name != inlineEditResponse[this.dropdownConfig.apiDataSelectedKeyName])
       this.opportunityService.updateOpportunityName(this.tempOverviewDetails.opportunity_id,inlineEditResponse[this.dropdownConfig.apiDataSelectedKeyName])
          .subscribe(async res => {
            this.tempOverviewDetails.opportunity_name = inlineEditResponse[this.dropdownConfig.apiDataSelectedKeyName];
              this.UtilityService.showMessage("Opportunity Name Updated Successfully",
              "Dismiss",
              this.opportunityService.mediumInterval)
          }, err => {
              this.UtilityService.showMessage(err,'dismiss');
          });
    }
    else if(this.inlineEditField == 'Planned Presales Hours'){
      if (this.inlineEditActiveRow.opportunity_name != inlineEditResponse[this.dropdownConfig.apiDataSelectedKeyName])
       this.opportunityService.updatePlannedPresalesHours(this.tempOverviewDetails.opportunity_id,inlineEditResponse[this.dropdownConfig.apiDataSelectedKeyName])
          .subscribe(async res => {
            if(res=="F")
            {
              this.UtilityService.showMessage("Sorry! we cannot intiate approval for changing PL Hours",
              "Dismiss",
              this.opportunityService.mediumInterval)
            }
            else if(res=="NAF")
            {
              ////console.log("No approvers found")
              this.UtilityService.showMessage("Sorry! we cannot intiate approval for changing PL Hours",
              "Dismiss",
              this.opportunityService.mediumInterval)
            }
            else
            {
              ////console.log(res)
              ////console.log(this.planned_presale_logs)
              
              if(this.planned_presale_logs==undefined || this.planned_presale_logs==null)
              {
                ////console.log("1")
                
                this.planned_presale_logs=[res]
                if(this.planned_presale_history==null)
                    this.planned_presale_history=[res]
                else
                  this.planned_presale_history.push(res)
                
              
              }
              else
              {
                ////console.log("2")
                this.planned_presale_logs.push(res)
                if(this.planned_presale_history==null)
                    this.planned_presale_history=[res]
                else
                  this.planned_presale_history.push(res)
              }
              this.overviewDetails.planned_presales_hours_status=2;
              this.UtilityService.showMessage("Approval started for changing PL Hours",
                "Dismiss",
                this.opportunityService.mediumInterval)
              this.presalesHours()
             
              //this.tempOverviewDetails.planned_presales_hours = inlineEditResponse[this.dropdownConfig.apiDataSelectedKeyName];
                
            }
            
            
          }, err => {
              this.UtilityService.showMessage(err,'dismiss');
          });
    }
    else if(this.inlineEditField == 'SalesOrg'){
      if (this.inlineEditActiveRow.sales_unit_name != inlineEditResponse[this.dropdownConfig.apiDataSelectedKeyName])
      this.opportunityService.updateSalesOrgForOpportunity(parseInt(this.opportunityId), parseInt(inlineEditResponse[this.dropdownConfig.apiDataUpdateKeyName]), inlineEditResponse[this.dropdownConfig.apiDataSelectedKeyName]).then(res=>{

        if(res['messType']=="S")
        {
          this.overviewDetails.sales_unit_name =  inlineEditResponse[this.dropdownConfig.apiDataSelectedKeyName]
          this.overviewDetails.sales_unit_id = inlineEditResponse[this.dropdownConfig.apiDataUpdateKeyName]
          this.UtilityService.showMessage("Sales Org updated successfully!","Dismiss")
        }
        else
          this.UtilityService.showMessage("Error while updating sales org!","Dismiss")
      })
    }
    else if(this.inlineEditField == 'Opportunity Value'){
    
      if (this.inlineEditActiveRow.opportunity_currency_value != inlineEditResponse[this.dropdownConfig.apiDataSelectedKeyName])
       this.opportunityService.updateOpportunityValue(this.tempOverviewDetails.opportunity_id,inlineEditResponse[this.dropdownConfig.apiDataSelectedKeyName])
          .subscribe(async res => {  
            if(res['messType']=="S"){
            this.tempOverviewDetails.opportunity_currency_value = inlineEditResponse[this.dropdownConfig.apiDataSelectedKeyName];
            this.tempOverviewDetails.opportunity_value = res["messData"]
              this.UtilityService.showMessage("Opportunity Value Updated Successfully",
              "Dismiss",
              this.opportunityService.mediumInterval)
            }
              else{
                this.UtilityService.showMessage(res['messText'],"Dismiss")   
              }
          }, err => {
              this.UtilityService.showMessage(err,'dismiss');
          });
    }
    else if(this.inlineEditField == 'Actual Closure Date'){
      let UpdatedACDDate = moment(inlineEditResponse[this.dropdownConfig.apiDataSelectedKeyName]).format('YYYY-MM-DD');
      if (inlineEditResponse[this.dropdownConfig.apiDataSelectedKeyName] && this.tempOverviewDetails.actual_closure_date != UpdatedACDDate)
       this.opportunityService.updateOpportunityActualClosureDate(this.tempOverviewDetails.opportunity_id,UpdatedACDDate,this.tempOverviewDetails.sales_status_id)
          .subscribe(async res => {
            
            if(res['messType']=="S"){
            this.tempOverviewDetails.actual_closure_date = moment(UpdatedACDDate).format("YYYY-MM-DD[T]HH:mm:ss.SSS[Z]");
              this.UtilityService.showMessage("Actual closure date Updated Successfully",
              "Dismiss",
              this.opportunityService.mediumInterval)
            }
            else if(res['messType']=="E"){
              this.UtilityService.showMessage(res['messText'],
              "Dismiss",
              this.opportunityService.mediumInterval)
            }
          }, err => {
              this.UtilityService.showMessage(err,'dismiss');
          });

    }
  }

  async openForm(){
    ////console.log("after click",this.overviewDetails);
    let form_details
    let isFormAval : boolean

    if(this.overviewDetails.qualifier_form_id!=null && this.overviewDetails.qualifier_form_id!=''){
      form_details = this.overviewDetails.qualifier_form_id
      isFormAval = true
    }
    else{
      await this.opportunityService.getQualifierForm(this.overviewDetails.opportunity_id).then((res:any)=>{
        if(res.messType=="E"){
          this.UtilityService.showMessage("Form Id not found!",
                "Dismiss",
                this.opportunityService.mediumInterval)
          isFormAval = false
        }
        else if(res.messType=="S"){
          form_details = res.data
          isFormAval = true
          this.opportunityService.updateBidForm(form_details,this.overviewDetails.opportunity_id)
        }
      })
    }


    if(!isFormAval){
      return;
    }

    ////console.log(form_details)
  
      const { CustomFormModalComponent } = 
      await import ( 'src/app/modules/shared-lazy-loaded-components/custom-form-modal/custom-form-modal.component' ) 
      const openCustomFormModalComponent = this.dialog.open ( CustomFormModalComponent , 
        { height : '100%' ,
         width : '75%' , 
         position : {
            right : '0px' 
          }, 
         data : 
            { 
              formId : form_details['form_id'], 
              isEdit : true, 
              entryForId : (this.overviewDetails.opportunity_id).toString()
            } 
          }
      )
      
      openCustomFormModalComponent.afterClosed()
          .subscribe(async (res:any)=>{
            if(res.messType =='S'){
              let bid_res = this.calculateBid(res,form_details['bid_criteria'])

              this.opportunityService.updateBidInfo(bid_res,this.overviewDetails.opportunity_id).then((res)=>{
                this.overviewDetails.bid_perc = bid_res.bid_perc
                this.overviewDetails.is_bid = bid_res.is_bid
                this.bidStatus = bid_res.is_bid?'/ Bid':'/ No bid'

              })
            }
          })
    }

    calculateBid(formInfo,bid_criteria){
      let obtainedPerc = 0
      let qualificationCriteria = 0
      let bid = true;
      ////console.log(formInfo)
      if(formInfo.mData.layout == 'multiStepLayout'){

        for(let stepper = 0; stepper<formInfo.mData.msData.multiStepCount; stepper++){
          let stepperPerc = 0,totalMark = 0;
            
            if(formInfo[stepper].length>0){

              for(let value of formInfo[stepper]){

                let m_field = _.where(formInfo.mData.fields[stepper],{_id:value['id']})[0]
                ////console.log('m',m_field)

                //For calculating selected value marks
                if(m_field.fieldType=='Dropdown' && value['value']!='' && value['value']!=null){
                  let selectedFieldWeightage = _.where(m_field.options,{name:value['value']}).length == 0
                                                ? 0
                                                : _.where(m_field.options,{name:value['value']})[0]['weightage'] == null 
                                                  ? 0 : _.where(m_field.options,{name:value['value']})[0]['weightage']

                  selectedFieldWeightage = selectedFieldWeightage!=''
                                           ? selectedFieldWeightage
                                           : 0
                  
                  if(selectedFieldWeightage<0){
                    bid = false
                  }

                  ////console.log("selected",selectedFieldWeightage,typeof selectedFieldWeightage)
                  
                  obtainedPerc +=selectedFieldWeightage
                }
              }
            }
            

        }
      }
      else if(formInfo.mData.layout == 'generalLayout'){
          
          for(let field=0 ; field<formInfo.length ; field++){
            let m_field = _.where(formInfo.mData.fields,{_id:formInfo[field].id})[0]
            ////console.log('m',m_field)

            if(m_field.fieldType=='Dropdown' && formInfo[field].value!='' && formInfo[field].value!=null){

              let selectedFieldWeightage = _.where(m_field.options,{name:formInfo[field]['value']}).length == 0
                                                ? 0
                                                : _.where(m_field.options,{name:formInfo[field]['value']})[0]['weightage'] == null 
                                                  ? 0 :_.where(m_field.options,{name:formInfo[field]['value']})[0]['weightage']

              selectedFieldWeightage = selectedFieldWeightage!=''
                                        ? selectedFieldWeightage
                                        : 0

              //check for negative val
              if(selectedFieldWeightage<0){ bid = false }

              //console.log("selected",selectedFieldWeightage,typeof selectedFieldWeightage)

              obtainedPerc +=selectedFieldWeightage
            }


          }
      }
      //console.log(obtainedPerc);
      //console.log(bid)

      //compare criteria when there is no negative val
      if(bid){
        bid = obtainedPerc >= bid_criteria
              ? true : false
      }

      return {bid_perc: obtainedPerc,is_bid:bid}

    }

    async openReviewForm(){
      let isFormAval : boolean
      let form_details
      if(this.overviewDetails.presales_review_form!=null && this.overviewDetails.presales_review_form!=''){
        form_details = this.overviewDetails.presales_review_form
        isFormAval = true

      }
      else{
        await this.opportunityService.getInternalPresalesReviewForm(this.overviewDetails.opportunity_id).then((res:any)=>{
          if(res.messType=="E"){
            this.snackBar.open("Form Id not found", 'dismiss', {
              duration: 4000,
            });
            isFormAval = false
          }
          else if(res.messType=="S"){
            form_details = res.data.form_id
            isFormAval = true
            this.opportunityService.updateReviewForm(form_details,this.overviewDetails.opportunity_id)
            
          }
        })
      }
      if(!isFormAval){
        return
      }

      else{
        const { CustomFormModalComponent } = 
        await import ( 'src/app/modules/shared-lazy-loaded-components/custom-form-modal/custom-form-modal.component' ) 
        const openCustomFormModalComponent = this.dialog.open ( CustomFormModalComponent , 
          { height : '100%' ,
           width : '75%' , 
           position : {
              right : '0px' 
            }, 
           data : 
              { 
                formId : form_details, 
                isEdit :true, 
                entryForId : (this.overviewDetails.opportunity_id).toString()
              } 
            }
        )
        
        openCustomFormModalComponent.afterClosed()
            .subscribe(async (res:any)=>{
              if(res.messType=="S"){
                //console.log(res)
                let score = this.calculateInternalFeedback(res)
                await this.opportunityService.updateInternalFeedbackScore(this.overviewDetails.opportunity_id,score)
                //console.log(score)
              }
             
            })
      }
    }

    calculateInternalFeedback(result){
      
      let totalScore = 0
      if(result.mData.layout == "generalLayout"){

        for(let field=0 ; field < result.mData.fields.length ; field++){

          let m_id,m_score

          if(result.mData.fields[field].fieldType=="Rating"){

            if(result.mData.fields[field].setWeightage == true){

              m_id = result.mData.fields[field]._id;
              m_score = result.mData.fields[field].weightage;

            }
            else if(result.mData.fields[field].setWeightagePercentage == true){

              m_id = result.mData.fields[field]._id;
              m_score = result.mData.fields[field].weightagePercentage;

            }
            let t_score = (_.where(result,{id:m_id}))[0].value!=null ? (_.where(result,{id:m_id}))[0].value : 0;

            totalScore += (t_score/5)*m_score;

          }
          
        }
      }

      return totalScore
    }

  convertToLocalTime = (time) => {
    let localTime = new Date(time);
    let timezoneOffset = localTime.getTimezoneOffset() * 60000;
    localTime.setTime(localTime.getTime() - timezoneOffset);
    return localTime;
  };

  approveChange(){
      //console.log(this.comments_approvers)
      this.opportunityService.approvePresalesPlannedhours(this.opportunityId,this.comments_approvers).then((res: any)=>{
          //console.log(res)
          if(res=="F")
          {
            this.UtilityService.showMessage("You are not an approver for changing PL Hours",
            "Dismiss",
            this.opportunityService.mediumInterval)
          }
          else if(res=="F1")
          {
            this.UtilityService.showMessage("Cannot approve for changing PL Hours",
            "Dismiss",
            this.opportunityService.mediumInterval)
          }
          else
          {
            //console.log(res)
            this.approver_status=3
            this.tempOverviewDetails.planned_presales_hours=res.hours
            
            if(res.status==1)
            {
              this.overviewDetails.planned_presales_hours_status=1;
            }
              if(this.planned_presale_logs==undefined || this.planned_presale_logs==null)
              {
                //console.log("1")
                
                this.planned_presale_logs=[res.planned_presale_logs]
                //console.log(this.planned_presale_logs)
              
              }
              else
              {
                //console.log("2")
                this.planned_presale_logs.push(res.planned_presale_logs)
              }
              this.UtilityService.showMessage("Approved Successfully",
              "Dismiss",
              this.opportunityService.mediumInterval)
              this.is_approver=false;
              this.presalesHours()
             
          }
      })
  }

  rejectChange(){
    //console.log(this.comments_approvers)
      this.opportunityService.rejectPresalesPlannedhours(this.opportunityId,this.comments_approvers).then((res: any)=>{
          //console.log(res)
          if(res=="F")
          {
            this.UtilityService.showMessage("You are not an approver for changing PL Hours",
            "Dismiss",
            this.opportunityService.mediumInterval)
          }
          else if(res=="F1")
          {
            this.UtilityService.showMessage("Cannot reject for changing PL Hours",
            "Dismiss",
            this.opportunityService.mediumInterval)
          }
          else
          {

            
            //console.log(res)
            this.approver_status=5
            if(res.status==1)
            {
                this.overviewDetails.planned_presales_hours_status=1;
            }
              if(this.planned_presale_logs==undefined || this.planned_presale_logs==null)
              {
                //console.log("1")
                
                this.planned_presale_logs=[res.planned_presale_logs]
                
              
              }
              else
              {
                //console.log("2")
                this.planned_presale_logs.push(res.planned_presale_logs)
              }
              this.UtilityService.showMessage("Rejected Successfully",
              "Dismiss",
              this.opportunityService.mediumInterval)
              this.is_approver=false;
              this.presalesHours()
          }
      })
  }

  showHistory()
  {
    if(this.openHistory==false)
    {
      this.openHistory=true;
      this.openApprover=false;
      this.openComment=false;
    }
    else
      this.openHistory=false;
  }

  showPresalesApprovers()
  {
    
    if(this.openApprover==false)
    {
      this.openApprover=true;
      this.openHistory=false;
      this.openComment=false;
    }
    else
    {
      this.openApprover=false;
    }
  }

  showComments()
  {
    if(this.openComment==false)
    {
      this.openHistory=false;
      this.openApprover=false;
      this.openComment=true;
    }
    else
      this.openComment=false;
  }

  presalesHours()
  {
      
      
      
      //console.log(this.planned_presale_logs, this.planned_presale_history)
      
      if(this.overviewDetails.planned_presales_hours_status==1)
      {
          this.is_workflow=false;
          
      }
      else 
      {
          let getApprover=[]
          this.is_workflow=true;
          //console.log(this.planned_presale_history)
          this.presales_changed_hours=this.planned_presale_history[this.planned_presale_history.length-1].hours
          this.presales_hours_changed_by=this.planned_presale_history[this.planned_presale_history.length-1].changed_by_name
          this.presales_hours_changed_on=moment(this.planned_presale_history[this.planned_presale_history.length-1].changed_on).format('DD-MMM-YY, h:mm:ss a')
          let approver=this.planned_presale_history[this.planned_presale_history.length-1].approver
          //console.log(approver)
          for(let app of approver)
          {
      
              if(this.current_user==app.oid && app.status==2 && this.approver_status==0)
              {
                    this.is_approver=true;
            
              }
              if(this.approver_status==3 && this.current_user==app.oid)
              {
                  app.status=3
              }
              else if(this.approver_status==5 && this.current_user==app.oid)
              {
                  app.status=5
              }
              
              getApprover.push({name: app.name, oid: app.oid, status: app.status, level: app.level, designation: app.designation})
          }
          //console.log(getApprover)
          this.presales_hours_approver=getApprover
        
      }

      


  }

  borderStatusColor(status) {
   
    if (status == 2) {
      return "#b7b7b7"
    }
    else if (status == 3) {
      return "#009432"
    }
    else if (status == 5) {
      return "#af0505"
    }

    else {
      return "white";
    }
  }

  getApproverforPresalesHours(oppId)
  {
    this.opportunityService.getApproversforPresalesHrs(oppId).subscribe(res=>{
        if(res!=undefined && res!=null)
        {
          //console.log(this.presales_approvers)
          this.presales_approvers=res;
        }
        else
        {
           this.presales_approvers=[]
        }

    },(err)=>{
      //console.log(err)
    })
  }

  

  async addSaveChannel()
  {
    if(this.channel_type_id==undefined || this.channel_type_value==undefined)
    {
      this.snackBar.open("Channel Type not entered! Cannot Save Channel!","Dismiss",{duration:this.opportunityService.mediumInterval})
    }
    else if(this.channel_id_value==undefined)
    {
      this.snackBar.open("Channel id not entered! Cannot Save channel!","Dismiss",{duration:this.opportunityService.mediumInterval})
    }
    else
    {
        this.is_save_channel_loading=true;
        let notif_id: any
        await this.opportunityService.saveChannel(this.opportunityId,this.channel_type_id,this.channel_id_value).then(res=>{
          //console.log(res)
            notif_id=res[0].notif_id
        },(err)=>{
          //console.log(err)
        })
          
          let channel_val={
                  "notif_id": notif_id,
                  "roles":this.channel_type_value,
                  "channel_id":this.channel_id_value,
                  "notif_type_id":this.channel_type_id
          }
          this.channel_type_value=undefined;
          this.channel_type_id=undefined;
          this.channel_id_value=undefined  
          this.channel_id_ex=false;
          this.channel_type_ex=false;
          this.is_save_channel_loading=false;
          this.dup_entered_Channel.push(channel_val)
          //console.log("Safe",this.dup_entered_Channel)
    }
      
      
  }

  
  deleteChannel(index)
  {
    
      this.opportunityService.deleteChannel(this.opportunityId,this.entered_Channel[index].notif_id).subscribe(res=>{
          this.entered_Channel=res
          this.dup_entered_Channel=this.entered_Channel
          
          this.snackBar.open("Channel deleted Successfully","Dismiss",{duration: 2000})
      },err=>{
          //console.log(err)
      })
  }
 
  async getChannel()
  {
    await this.opportunityService.getChannels(this.opportunityId).subscribe(res=>{
      this.entered_Channel=res
      this.dup_entered_Channel=this.entered_Channel
      //console.log(this.entered_Channel)
    },err=>{
      //console.log(err);
    })
  }

  getComments(event) {
      //console.log(event)
      this.opportunityService.insertCommentForPlannedpresalesHrs(event, this.opportunityId).subscribe(res => {
        //console.log(res);
      }, err => {
        console.error(err);
      })
  }

  moduleList()
  {
    //console.log(this.overviewDetails,this.overviewDetails.module_list)
    if(this.overviewDetails!=undefined && this.overviewDetails!=null)
    {
      if(this.overviewDetails.module_list!=null)
      {
        //console.log(this.module_category_list,this.overviewDetails.module_list)
        for(let sec of JSON.parse(this.overviewDetails.module_list))
        {
            //console.log(sec,"Hello")
            let list=_.findWhere(this.module_category_list,{id:sec})
      
            this.module_list.push(list.name)

        }
      }
    }
  }

  openDynamicForm=async(form_id: any)=>{

    const { CustomFormModalComponent } = 
        await import ( 'src/app/modules/shared-lazy-loaded-components/custom-form-modal/custom-form-modal.component' ) 
        const openCustomFormModalComponent = this.dialog.open ( CustomFormModalComponent , 
          { height : '100%' ,
           width : '75%' , 
           position : {
              right : '0px' 
            }, 
           data : 
              { 
                formId : form_id, 
                isEdit :true, 
                entryForId : (this.overviewDetails.opportunity_id).toString()
              } 
            }
        )
        
        openCustomFormModalComponent.afterClosed()
            .subscribe(async (res:any)=>{
              if(res.messType=="S"){
                //console.log(res)
               
                
                //console.log(score)
              }
             
            })
      
  }

  deleteDynamicForm=async(form_name: any,form_id: any)=>{
    this.confirmSweetAlert("Do you want to delete "+form_name+"?").then((deleteConfirm) => {
      if (deleteConfirm.value) {
        console.log(deleteConfirm.value)
        this.opportunityService.deleteDynamicForm(form_id,this.opportunityId).then((res: any)=>{
          if(res.messType=="S")
          {
            this.snackBar.open(form_name+" deleted successfully!","Dismiss",{duration:this.opportunityService.mediumInterval})
            this.opportunityService.getOpportunityForm(this.opportunityId).then((res: any)=>{
              this.opportunity_form = res
            
            },(err)=>{
              console.log(err)
            })
          }
          else
          {
            this.snackBar.open("Error while deleting "+form_name+"!","Dismiss",{duration:this.opportunityService.mediumInterval})
          }
        },(err)=>{
          console.log(err)
        })
      }
    })
    
  }

  confirmSweetAlert(title) {
    return sweetAlert.fire({
      customClass: {
        title: "title-class",
        confirmButton: "confirm-button-class",
        cancelButton: "confirm-button-class"
      },
      title: title,
      // text: text,
      icon: "warning",
      showConfirmButton: true,
      showCancelButton: true
    })
  }

  insertUserForm(){
    if(!this.oppEditAccess){
      this.UtilityService.showMessage("Creation is restricted", 'Dismiss');
      return;
    }
    if(this.user_form_id.valid && this.user_form_name.valid){
      this.opportunityService.addUserForm(this.user_form_name.value,this.user_form_id.value,this.opportunityId).then((res:any)=>{
        if(res["messType"]=="S")
        {
            this.opportunityService.getOpportunityForm(this.opportunityId).then((res: any)=>{
              this.opportunity_form = res
            
            },(err)=>{
              console.log(err)
            })
            this.snackBar.open(res["data"],"Dismiss",{duration: 5000})
           
        }
        else if(res["messType"]=="E")
        {
            this.snackBar.open(res["data"],"Dismiss",{duration: 5000})
        }
        else
        {
            this.snackBar.open("Cannot add Planned OBV Freeze! Contact KEBS team!")
        }
        
        this.user_form_id.reset()
        this.user_form_name.reset()
      },err => {
        console.log(err)
      })
    }
    else{
      this.snackBar.open("Invalid Entries!", "Dismiss", {
        duration: this.opportunityService.mediumInterval,
      });
      this.user_form_id.reset()
      this.user_form_name.reset()
    }
  }
  productCategoryList()
  {
    
    if(this.overviewDetails!=undefined && this.overviewDetails!=null)
    {
      let _productCategory=(this.overviewDetails.product_category_value!=null)?JSON.parse(this.overviewDetails.product_category_value):[]
      if(this.shift_list!=null)
      {
        
        for(let sec of _productCategory)
        { 
          
                //console.log(sec)
                let shift_name = _.findWhere(this.shift_list,{id:sec['shift']})
           
                sec['shiftName']=(shift_name!=undefined)?shift_name.name:""
           
        }
      }
      this.productCategory=_productCategory
      //console.log(this.productCategory)
    }
  }

  editOpportunity(type: string) {
    if (this.editAccess) {
      this.opportunityService.getOpportunityAllField(this.opportunityId).subscribe(async res => {
        console.log(res); 
        let sendObject = {};
       
        let editData = res[0];

        if (type === 'CHILD_CREATION') {
          let creationBasicdata: any = [];
          creationBasicdata.parentOpportunity = this.overviewDetails?.opportunity_id;
          sendObject = {
            height: '100%',
            width: '80%',
            position: { right: '0px' },
            data: {
              editDataForPatching: creationBasicdata,
              mode: 'Create'
            },
            disableClose: true
          }
        }
        if (type === 'GENERAL_EDIT') {
          sendObject = {
            height: '100%',
            width: '80%',
            position: { right: '0px' },
            data: {
              editDataForPatching: editData,
              mode: 'Edit'
            },
            disableClose: true
          }
        }

        const { CreateOpportunityComponent } = await import('src/app/modules/create-components/create-opportunity/create-opportunity.component');

        const createopportunityComponent = this.dialog.open(CreateOpportunityComponent, sendObject);
        createopportunityComponent.afterClosed().subscribe(result => {
        if(result=="updated"){
          this.opportunityService.checkAdaptTemplate(this.opportunityId, res[0].status_id).subscribe((res)=>{
            if(res['messType'] == 'A') {
              this.UtilityService.openConfirmationSweetAlertWithCustom("Create Duplicate Activities?", "Do you wish to create Activities based on status template which is already available ?")
              .then((copyConfirm) => {

                if (copyConfirm)
                 {
                  this.opportunityService.adaptStatusTemplate(this.opportunityId, res[0].status_id).subscribe((res)=>{
                    if(res['messType'] == 'S') {
                      this.UtilityService.showMessage('Activities created based on Opportunity status ', 'Dismiss');
                    } else {
                      this.UtilityService.showMessage('Error while creating activities based on Opportunity status ', 'Dismiss');
                   
                    }
                  })
                 }

                else{
                  return true
                }

            })
            } else if(res['messType'] == 'S'){
              this.opportunityService.adaptStatusTemplate(this.opportunityId, res[0].status_id).subscribe((res)=>{
                if(res['messType'] == 'S') {
                  this.UtilityService.showMessage('Activities created based on Opportunity status ', 'Dismiss');
                } else {
                  this.UtilityService.showMessage('Error while creating activities based on Opportunity status ', 'Dismiss');
               
                }
              })
            }
          })
          this.ngOnInit()
        }
        });
      })
    }   
    else {
      this.snackBar.open("Sorry, You don't have access to Edit!", 'dismiss', {
        duration: 4000,
      });
      // this.snackBar.open( editData == "CHILD_CREATION" ? "Sorry, You don't have access to create child opportunity" :"Sorry, You don't have access to Edit!", 'dismiss', {
      //   duration: 4000,
      // });
    }


  }


  async viewLineofBusiness(){
      let product_heirarchy = []

      let shift = []
      await this.opportunityService.getProductHeirarchy().then((res:any)=>{
        product_heirarchy = res
      },(err)=>{
        console.log(err)
      })

      const { HeirarchyViewComponent } = 
      await import ( 'src/app/modules/shared-lazy-loaded-components/heirarchy-view/heirarchy-view.component' ) 
      const openCustomFormModalComponent = this.dialog.open ( HeirarchyViewComponent , 
        { height : '100%' ,
         width : '85%' , 
         position : {
            right : '0px' 
          }, 
         data : 
            { 
              heading : "Line of Business" + " - " + this.overviewDetails.opportunity_name + " ("+this.opportunityId+")",
              heirarchy_data: product_heirarchy,
              column_config: this.column_config,
              application_object: this.view_heirarchy_details['application_object'],
              application_id: this.view_heirarchy_details['application_id'],
              patchData: (this.overviewDetails.product_category_value!=null && this.overviewDetails.product_category_value!="")?JSON.parse(this.overviewDetails.product_category_value):[],
              returnAsArray: false,
              access:"edit"

            } 
          }
      )
      
      openCustomFormModalComponent.afterClosed()
          .subscribe(async (res:any)=>{
            if(res!=undefined)
            {
              console.log(res)
              if(res['messType']=="S")
              {
                  this.opportunityService.updateProductCategory(res['data'],this.opportunityId).then((res)=>{
                      if(res['messType']=="S")
                      {
                        this.snackBar.open("Updated Successfully!","Dismiss",{duration: this.opportunityService.mediumInterval})
                        this.ngOnInit()
                      }
                  },(err)=>{
                    this.snackBar.open("Unable to update Product Category!","Dismiss",{duration: this.opportunityService.mediumInterval})
                  })
              }
            }
          })
  }

  showBidQualificationApprovers()
  {
    
    if(this.openBidApprover==false)
    {
      this.openBidApprover=true;
      this.openBidHistory=false;
      this.openBidComment=false;
    }
    else
    {
      this.openBidApprover=false;
    }
  }

  showBidQualificationComments()
  {
    if(this.openBidComment==false)
    {
      this.openBidHistory=false;
      this.openBidApprover=false;
      this.openBidComment=true;
    }
    else
      this.openBidComment=false;
  }

  showBidQualificationHistory()
  {
    if(this.openBidHistory==false)
    {
      this.openBidHistory=true;
      this.openBidApprover=false;
      this.openBidComment=false;
    }
    else
      this.openBidHistory=false;
  }


  triggerBidQualificationApproval()
  {
      this.UtilityService.openConfirmationSweetAlertWithCustom("In No Bid Opportunity, For changing the status will trigger approval?","Once you confirm approval will be triggered")
      .then(res=>{
        if(res)
        {

          if(this.bid_qualification_approvers.length>0)
          {
              this.opportunityService.updateBidQualificationApproval(this.tempOverviewDetails.opportunity_id)
              .then(async res => {
                if(res=="F")
                {
                  this.UtilityService.showMessage("Sorry! we cannot intiate approval for changing No Bid Opportunity",
                  "Dismiss",
                  this.opportunityService.mediumInterval)
                }
                else if(res=="NAF")
                {
                  ////console.log("No approvers found")
                  this.UtilityService.showMessage("Sorry! we cannot intiate approval for changing No Bid Opportunity",
                  "Dismiss",
                  this.opportunityService.mediumInterval)
                }
                else
                {
                  ////console.log(res)
                  ////console.log(this.bid_qualification_logs)
                  
                  if(this.bid_qualification_logs==undefined || this.bid_qualification_logs==null)
                  {
                    ////console.log("1")
                    
                    this.bid_qualification_logs=[res]
                    if(this.bid_qualification_history==null)
                        this.bid_qualification_history=[res]
                    else
                      this.bid_qualification_history.push(res)
                    
                  
                  }
                  else
                  {
                    ////console.log("2")
                    this.bid_qualification_logs.push(res)
                    if(this.bid_qualification_history==null)
                        this.bid_qualification_history=[res]
                    else
                      this.bid_qualification_history.push(res)
                  }
                  this.overviewDetails.bid_qualification_approval_status=2;
                  this.UtilityService.showMessage("Approval started for No Bid Opportunity",
                    "Dismiss",
                    this.opportunityService.mediumInterval)
                  this.noBidQualification()
                
                  //this.tempOverviewDetails.planned_presales_hours = inlineEditResponse[this.dropdownConfig.apiDataSelectedKeyName];
                    
                }
                
                
              }, err => {
                  this.UtilityService.showMessage(err,'dismiss');
              });
          }
          else
          {
            this.UtilityService.showMessage("No approvers found! Kindly contact KEBS team!","Dismiss")
          }
        }
        else
        {
          console.log(res)
        }
      })
  }

  noBidQualification()
  {  
      if(this.overviewDetails.bid_qualification_approval_status==1 || this.overviewDetails.bid_qualification_approval_status==3)
      {
          this.is_bid_workflow=false;
          
      }
      else 
      {
          let getApprover=[]
          this.is_bid_workflow=true;
   
      
          this.bid_qualification_changed_by=this.bid_qualification_history[this.bid_qualification_history.length-1].changed_by_name
          this.bid_qualification_changed_on=moment(this.bid_qualification_history[this.bid_qualification_history.length-1].changed_on).format('DD-MMM-YY, h:mm:ss a')
          let approver=this.bid_qualification_history[this.bid_qualification_history.length-1].approver
    
          for(let app of approver)
          {
      
              if(this.current_user==app.oid && app.status==2 && this.bid_approval_status==0)
              {
                    this.is_bid_approver=true;
            
              }
              if(this.bid_approval_status==3 && this.current_user==app.oid)
              {
                  app.status=3
              }
              else if(this.bid_approval_status==5 && this.current_user==app.oid)
              {
                  app.status=5
              }
              
              getApprover.push({name: app.name, oid: app.oid, status: app.status, level: app.level, designation: app.designation})
          }
       
          this.bid_qualification_approver=getApprover
        
      }
    }
    /**
     * @description Approve Bid Qualification approval
     */
    approveNoBidApproval(){
     
        this.opportunityService.approveBidQualification(this.opportunityId,this.comments_approvers).then((res: any)=>{
        
            if(res=="F")
            {
              this.UtilityService.showMessage("You are not an approver for No Bid Opportunity Approval",
              "Dismiss",
              this.opportunityService.mediumInterval)
            }
            else if(res=="F1")
            {
              this.UtilityService.showMessage("Cannot approve for No Bid Opportunity Approval",
              "Dismiss",
              this.opportunityService.mediumInterval)
            }
            else
            {
         
              this.bid_approval_status=3
          
              
              if(res.status==1)
              {
                this.overviewDetails.bid_qualification_approval_status=3;
              }
                if(this.bid_qualification_logs==undefined || this.bid_qualification_logs==null)
                {
                  //console.log("1")
                  
                  this.bid_qualification_logs=[res.bid_qualification_logs]
                  //console.log(this.planned_presale_logs)
                
                }
                else
                {
                  //console.log("2")
                  this.bid_qualification_logs.push(res.bid_qualification_logs)
                }
                this.UtilityService.showMessage("Approved Successfully",
                "Dismiss",
                this.opportunityService.mediumInterval)
                this.is_bid_approver=false;
                this.noBidQualification()
               
            }
        })
    }
    /**
     * @description Reject Bid Qualification approval
     */
    rejectNoBidApproval(){
      //console.log(this.comments_approvers)
        this.opportunityService.rejectBidQualification(this.opportunityId,this.comments_approvers).then((res: any)=>{
            //console.log(res)
            if(res=="F")
            {
              this.UtilityService.showMessage("You are not an approver for No Bid Opportunity Approval",
              "Dismiss",
              this.opportunityService.mediumInterval)
            }
            else if(res=="F1")
            {
              this.UtilityService.showMessage("Cannot approve for No Bid Opportunity Approval",
              "Dismiss",
              this.opportunityService.mediumInterval)
            }
            else
            {
  
              
              //console.log(res)
              this.bid_approval_status=5
              if(res.status==1)
              {
                  this.overviewDetails.bid_qualification_approval_status=1;
              }
                if(this.bid_qualification_logs==undefined || this.bid_qualification_logs==null)
                {
                  //console.log("1")
                  
                  this.bid_qualification_logs=[res.bid_qualification_logs]
                  
                
                }
                else
                {
                  //console.log("2")
                  this.bid_qualification_logs.push(res.bid_qualification_logs)
                }
                this.UtilityService.showMessage("Rejected Successfully",
                "Dismiss",
                this.opportunityService.mediumInterval)
                this.is_bid_approver=false;
                this.noBidQualification()
            }
        })
    }
    /**
     * 
     * @param opportunityId 
     * @description Get approver for Bid qualification
     */
    getApproverforBidQualification(opportunityId)
    {
      this.opportunityService.getBidQualificationApprovers(opportunityId).subscribe(res=>{
        if(res!=undefined && res!=null)
        {
          //console.log(this.presales_approvers)
          this.bid_qualification_approvers=res;
        }
        else
        {
           this.bid_qualification_approvers=[]
        }

      },(err)=>{
        //console.log(err)
      })
    }
    /**
     * @description Insert Comment for Bid Qualification from HTMl
     * @param event 
     */
    getBidQualificationComments(event) {
      //console.log(event)
      this.opportunityService.insertCommentForBidQualification(event, this.opportunityId).subscribe(res => {
        //console.log(res);
      }, err => {
        console.error(err);
      })
  }

  openWinLossForm=async(form_id: any)=>{

    const { CustomFormModalComponent } = 
        await import ( 'src/app/modules/shared-lazy-loaded-components/custom-form-modal/custom-form-modal.component' ) 
        const openCustomFormModalComponent = this.dialog.open ( CustomFormModalComponent , 
          { height : '100%' ,
           width : '75%' , 
           position : {
              right : '0px' 
            }, 
           data : 
              { 
                formId : form_id, 
                isEdit :true, 
                entryForId : (this.overviewDetails.opportunity_id).toString(),
                formName: "Win-Loss Form"
              } 
            }
        )
        
        openCustomFormModalComponent.afterClosed()
            .subscribe(async (res:any)=>{
              if(res!=undefined)
              {
                if(res.messType=='S'){
                  console.log("Success")
                  this.opportunityService.updateWinLossForm(form_id,this.opportunityId);
                }
              }
             
            })
      
  }

  
  openMeetingModal() {
    let modalParams = {
      application_id: 36,
      primary_unique_id: this.opportunityId,
      secondary_unique_id: null,
      attendees: [this.attendee],
      cost_centre_object:[],
      is_from_header_creation: false,
      meeting_meta_data: {
        'opportunity_id':this.opportunityId
      }
    };
    this.dialog.open(MeetingInviteComponent, {
      width: '6000px',
      maxHeight: '90vh',
      autoFocus: false,
      data: { modalParams: modalParams },
    });
  }

  
  async createCallLog() {
    if(!this.oppEditAccess){
      this.UtilityService.showMessage("Creation is restricted", 'Dismiss');
      return;
    }
    const dialogRef = this.dialog.open(TabbedComponentComponent, {
      height: '80%',
      width: '80%',
  
      data:{
        tabbed_content_type: 2,
        customer_id: this.overviewDetails['customer_id'],
        mode:"Create",
        application_reference_id: this.opportunityId,
        application_id: 36
      }
  
    });
  
    dialogRef.afterClosed().subscribe(result => {
  
      console.log(result);
  
    })
  
  // dialogRef.afterClosed().subscribe(result => {
  //   console.log(result);
  //   if (result == "update required") {
  //     this.refreshList();
  //   }
  // })
  }
  
  async createMeeting() {
    if(!this.oppEditAccess){
      this.UtilityService.showMessage("Creation is restricted", 'Dismiss');
      return;
    }
    const dialogRef = this.dialog.open(TabbedComponentComponent, {
      height: '80%',
      width: '80%',
  
      data:{
        tabbed_content_type: 1,
        customer_id: this.overviewDetails['customer_id'],
        mode:"Create",
        application_reference_id: this.opportunityId,
        application_id: 36
      }
  
    });
  
    dialogRef.afterClosed().subscribe(result => {
  
      console.log(result);
  
    })
  }
  async createMail() {
    if(!this.oppEditAccess){
      this.UtilityService.showMessage("Creation is restricted", 'Dismiss');
      return;
    }
    const dialogRef = this.dialog.open(TabbedComponentComponent, {
      height: '80%',
      width: '80%',
  
      data:{
        tabbed_content_type: 3,
        customer_id: this.overviewDetails['customer_id'],
        mode:"Create",
        application_reference_id: this.opportunityId,
        application_id: 36
      }
  
    });
  
    dialogRef.afterClosed().subscribe(result => {
  
      console.log(result);
  
    })
  }
  
  async createNewTask() {
    if(!this.oppEditAccess){
      this.UtilityService.showMessage("Creation is restricted", 'Dismiss');
      return;
    }
    const dialogRef = this.dialog.open(TabbedComponentComponent, {
      height: '80%',
      width: '80%',
  
      data:{
        tabbed_content_type: 4,
        customer_id: this.overviewDetails['customer_id'],
        mode:"Create",
        application_reference_id: this.opportunityId,
        application_id: 36
      }
  
    });
  
    dialogRef.afterClosed().subscribe(result => {
  
      console.log(result);
  
    })
  }


  //Add notes in opportunity 
  addNotes(){
    if(!this.notesEditAccess){
      this.UtilityService.showMessage("Creation is restricted", 'Dismiss');
      return;
    }
    const dialogRef = this.dialog.open(TabbedComponentComponent, {
      height: '80%',
      width: '80%',

      data:{
        tabbed_content_type: 8,
        customer_id: this.overviewDetails['customer_id'],
        mode:"Create",
        application_reference_id: this.opportunityId,
        application_id: 36
      }

    });

    dialogRef.afterClosed().subscribe(async (result) => {
      console.log("note res",result);
      //this.ngOnInit();
      this.refresh = "refresh"
    });
  
    
  }
      
  protected _onDestroy = new Subject<void>();

  deactivateOpportunity = () => {
    if(!this.oppEditAccess){
      this.UtilityService.showMessage("Delete is restricted", 'Dismiss');
      return;
    }

    this.UtilityService.openConfirmationSweetAlertWithCustom("Are you sure you want to delete ?", "Once you confirm, this Opportunity will be deleted !").
    then(async (res) => {
      if (res) {
        this.opportunityService.deactivateOpportunity(this.opportunityId).then(
          (res) => {
            if(res['messType'] == "S"){
              this.UtilityService.showMessage("Deleted Successfully!","Dismiss")
              this._router.navigateByUrl('/main/opportunities/opportunitiesHome');
            }
          }
         ).catch(
          (err) => {
            console.log(err)
          }
         )
      }
    });




  }
  ngAfterViewInit(): void {
    // this.currentItemIndex = this.statusLog.length
  }
      

  currentItemIndex = 0;
  @ViewChild('timelineContainer') timelineContainer : ElementRef;

  // scrollLeft(){
  //   this.currentItemIndex = (this.currentItemIndex + 1) % (this.statusLog.length/7);
  //   this.timelineContainer.nativeElement.scrollLeft = this.timelineContainer.nativeElement.offsetWidth*this.currentItemIndex; 
  // }

  // scrollRight(){
  //   if(this.currentItemIndex == 0 ) return 
  //   this.currentItemIndex = (this.currentItemIndex - 1) % this.statusLog.length;
  //   this.timelineContainer.nativeElement.scrollRight = this.timelineContainer.nativeElement.offsetWidth*this.currentItemIndex; 

  // }


  reOpenOpportunity = () => {
     this.opportunityService.reOpenOpportunity(this.opportunityId).subscribe(
      (res)=>{
        if(res["messType"] == "S"){
        this.UtilityService.showMessage("Opportunity Reopened Successfully!","Dismiss")
        this.ngOnInit()
        }
        else
        this.UtilityService.showMessage("Failed to Reopen opportunity!","Dismiss")
      }
     )
  }
    
  async createDemo() {
    if(!this.oppEditAccess){
      this.UtilityService.showMessage("Creation is restricted", 'Dismiss');
      return;
    }
    const dialogRef = this.dialog.open(TabbedComponentComponent, {
      height: '80%',
      width: '80%',
  
  // scrollRight() {
  //   if (this.currentItemIndex === this.statusLog.length - 1) {
  //     return; // Stop sliding when reaching the last item
  //   }
      data:{
        tabbed_content_type: 9,
        customer_id: this.overviewDetails['customer_id'],
        mode:"Create",
        application_reference_id: this.opportunityId,
        application_id: 36
      }
  
    });
  
    dialogRef.afterClosed().subscribe(result => {
  
      console.log(result);
  
    })
  }  

  //   this.currentItemIndex++;
  //   const itemWidth = this.timelineContainer.nativeElement.offsetWidth;
  //   this.timelineContainer.nativeElement.scrollLeft = this.currentItemIndex * itemWidth;
  // }

  // scrollLeft() {
  //   if (this.currentItemIndex === 0) {
  //     return; // Stop sliding when reaching the first item
  //   }

  //   this.currentItemIndex--;
  //   const itemWidth = this.timelineContainer.nativeElement.offsetWidth;
  //   this.timelineContainer.nativeElement.scrollLeft = this.currentItemIndex * itemWidth;
  // }

  // onScroll(event: Event) {
  //   const container = event.target as HTMLElement;
  //   const itemWidth = container.offsetWidth;
  //   const scrollLeft = container.scrollLeft;
  //   this.currentItemIndex = Math.round(scrollLeft / itemWidth);
  // }

  statusLog: any[] = [];

  @ViewChild('timelineView', { read: ElementRef, static:false }) timelineView : ElementRef;

  scrollRight() {
    if (this.currentItemIndex === this.statusLog.length - 1) {
      return; // Stop sliding when reaching the last item
    }

    this.currentItemIndex++;
    const itemWidth = this.getCarouselItemWidth();
    const scrollLeft = this.currentItemIndex * itemWidth;
    this.scrollToPosition(scrollLeft);
  }

  scrollLeft() {
    if (this.currentItemIndex === 0) {
      return; // Stop sliding when reaching the first item
    }

    this.currentItemIndex--;
    const itemWidth = this.getCarouselItemWidth();
    const scrollLeft = this.currentItemIndex * itemWidth;
    this.scrollToPosition(scrollLeft);
  }

  onScroll(event: Event) {
    const container = event.target as HTMLElement;
    const scrollLeft = container.scrollLeft;
    const itemWidth = this.getCarouselItemWidth();
    this.currentItemIndex = Math.round(scrollLeft / itemWidth);
  }

  getCarouselItemWidth(): number {
    const container = this.timelineContainer.nativeElement;
    const carouselItems = container.getElementsByClassName('timeline-item');
    if (carouselItems.length > 0) {
      const firstItem = carouselItems[0] as HTMLElement;
      return firstItem.getBoundingClientRect().width;
    }
    return 0;
  }

  scrollToPosition(scrollLeft: number) {
    this.timelineContainer.nativeElement.scrollTo({
      left: scrollLeft,
      behavior: 'smooth'
    });
  }
  
 async editStatusFromTimeline(status){
    let isEditDisable =await this.opportunityService.isDisableEdit(this.overviewDetails);
    if(isEditDisable){
      this.UtilityService.showMessage("Closed Won Opportunity Cannot be Edited!", 'Dismiss')
      return;
    }
    if(!this.oppEditAccess){
      this.UtilityService.showMessage("Edit is restricted", 'Dismiss');
      return;
    }
    
   
    let opportunity_value_to_be_copied=await this.opportunityService.checkIssuePoAndPoValue(this.tempOverviewDetails.opportunity_id,status.status_id,this.overviewDetails['issue_po_id']);
    if(opportunity_value_to_be_copied["isApplicable"]){
      this.overviewDetails['po_no']=opportunity_value_to_be_copied['poNumber'] || '';
      this.overviewDetails['po_date']=opportunity_value_to_be_copied['poDate'] || '';
      this.overviewDetails['po_value']=opportunity_value_to_be_copied['poValue'] || '';
      // return ;
    }
    let poValueCheck=await this.opportunityService.checkPoValueMatch(this.opportunityId,status.status_id,null,this.overviewDetails['po_value'])
    let seqStatusCheck=await this.opportunityService.checkOpportunitySalesStatus(this.opportunityId,status.status_id)
    console.log("SeqStatusTL")
    console.log(seqStatusCheck)
    if(seqStatusCheck['messType'] == 'E') {
      this.UtilityService.showMessage(seqStatusCheck['mesText'], 'Dismiss');
      return ;
    } 
    if(this.stagewiseFieldConfig){
      await this.opportunityService.getStagewiseFormFieldConfig( status.status_id).then((res:any) => {
        this.tempstagewiseFormFieldData = JSON.parse(res.messData[0].stagewise_field_config)
        this.stagewiseFormFieldData=_.filter( this.tempstagewiseFormFieldData,function(item:any){
         return (item.changeIndicator==true && item.is_mandant)
        })
        console.log(this.stagewiseFormFieldData)
      },
        err => {
          console.log(err)
        })
        let mand_fields=false
        for (let item of this.stagewiseFormFieldData) {
          console.log(this.overviewDetails)
          // await
          if(this.overviewDetails[item.data_field]=="" || this.overviewDetails[item.data_field]==null || this.overviewDetails[item.data_field]==0 ||  this.overviewDetails['dataSelected']=="00:00:00 00:00:00")
        {
          mand_fields=true;
          break;
        }
        else if(item.data_field=="opportunity_value"){
                
          let opp_value=this.overviewDetails[item.data_field]
          console.log(opp_value[0].value)
          if(opp_value[0].value==0)
          {
            mand_fields=true;
            break;
          }

        }

        }
        console.log(mand_fields)

          this.opportunityService.checkNdUpdateOpportunityStatus( this.opportunityId, status.status_id, this.overviewDetails,this.isQuoteEnabled ).then(async res => {
      
            if(res['messType']!="E")
            { 

              const updateAllowed = this.isQuoteEnabled ? await this.resolveQuoteOpportunityIntegration(status.status_id) : true;

              if (updateAllowed) {
              
                if(mand_fields==true){
                
                    this.UtilityService.showMessage("Please fill Value to update status!","Dismiss")
                    let changedStatus= status.status_id
                    this.openEditScreenOnStatusChange(changedStatus)
                    
                
                } else {
                  if(!poValueCheck["is_allowed"]){
                    if(poValueCheck['type']=="WARN"){
                    let copyConfirm = await this.UtilityService .openConfirmationSweetAlertWithCustom(
                      poValueCheck['textMsg'],
                      "Do you wish to close the opportunity with Opportunity Value and PO Value difference?"
                    );
              
                    if (!copyConfirm) {
                      return true;
                    }
                  }
                  else{
                    this.UtilityService.showMessage(poValueCheck['textMsg'], 'Dismiss',3000);
                    return ;
                  }
                  }
                  this.timelineView.nativeElement.classList.add('loading-border')
                  console.log("VAl Changes")

                  let changed_status  = _.where(this.salesStatusMaster,{id:  status.status_id})
                  if (changed_status[0]['is_end_form_needed'] == 1) {
                    await this.opportunityService.openWinLossForm(this.overviewDetails, changed_status[0]['name']).then((res) => {
                      if (res['messType']) {
                        if (res != "" && res["result"] != "")
                          this.opportunityService.updateOpportunitySalesStatus(this.opportunityId, status.status_id).subscribe(async (res) => {
                            if (res['messType'] == "S") {
                              if (status.status_id == 17) {
                                this.overviewDetails['isOpen'] = 0;
                                this.editAccess = this.opportunityService.checkUserforEditAccess(this.overviewDetails.sales_unit);
                                let isDisableEdit = await this.opportunityService.isDisableEdit(this.overviewDetails);
                                let reOpenAccess = await this.opportunityService.reOpenAccess(this.overviewDetails);
                                this.isDisableEdit = isDisableEdit;
                                this.reOpenAccess = reOpenAccess;
                              }
                              this.UtilityService.showMessage("Status Changed successfully", "Dismiss")
                              this.tempOverviewDetails.sales_status_id = status.status_id
                              this.tempOverviewDetails.sales_status_name = status.status_name
                              if (this.isStatusStageMappingAllowed) {
                                const proposalStatus = this.getProposalStatus(status.status_id);
                                this.tempOverviewDetails.proposal_status_id = proposalStatus.proposal_status_id;
                                this.tempOverviewDetails.proposal_status_name = proposalStatus.proposal_status_name;
                              }
                              this.timelineView.nativeElement.classList.remove('loading-border')
                              this.projectCreationAccess = await this.projectAccessCheck()
                              this.projectView = await this.projectBtnviewaccess()
                              await this.fetchOpportunityOverview();
                              this.opportunityService.checkAdaptTemplate(this.opportunityId, status.status_id).subscribe((res) => {
                                if (res['messType'] == 'A') {
                                  this.UtilityService.openConfirmationSweetAlertWithCustom("Create Duplicate Activities?", "Do you wish to create Activities based on status template which is already available ?")
                                    .then((copyConfirm) => {

                                      if (copyConfirm) {
                                        this.opportunityService.adaptStatusTemplate(this.opportunityId, status.status_id).subscribe((res) => {
                                          if (res['messType'] == 'S') {
                                            this.UtilityService.showMessage('Activities created based on Opportunity status ', 'Dismiss');
                                          } else {
                                            this.UtilityService.showMessage('Error while creating activities based on Opportunity status ', 'Dismiss');

                                          }
                                        })
                                      }

                                      else {
                                        return true
                                      }

                                    })
                                } else if (res['messType'] == 'S') {
                                  this.opportunityService.adaptStatusTemplate(this.opportunityId, status.status_id).subscribe((res) => {
                                    if (res['messType'] == 'S') {
                                      this.UtilityService.showMessage('Activities created based on Opportunity status ', 'Dismiss');
                                    } else {
                                      this.UtilityService.showMessage('Error while creating activities based on Opportunity status ', 'Dismiss');

                                    }
                                  })
                                }
                              })
                              this.fetchQuoteFydata()
                              this.opportunityService.getStatusLogForOpp(parseInt(this.opportunityId)).subscribe((res: any) => {
                                this.statusLogObservable.next(res.data);
                              })
                            }
                            else {
                              this.UtilityService.showMessage(res['mesText'], "Dismiss")
                              this.timelineView.nativeElement.classList.remove('loading-border')
                            }
                          })
                      }
                      else {
                        if (res != "" && res["result"]) {

                          this.timelineView.nativeElement.classList.remove('loading-border')
                          this.UtilityService.showMessage(res['result'], "Dismiss", 3000)

                        }

                      }
                    })
                  }
                  else{
                    this.opportunityService.updateOpportunitySalesStatus(this.opportunityId,status.status_id).subscribe(async (res)=>{
                      if(res['messType']=="S"){
                      if(status.status_id == 17){
                        this.overviewDetails['isOpen'] = 0;
                        this.editAccess = this.opportunityService.checkUserforEditAccess(this.overviewDetails.sales_unit);
                        let isDisableEdit = await this.opportunityService.isDisableEdit(this.overviewDetails);
                        let reOpenAccess = await this.opportunityService.reOpenAccess(this.overviewDetails);
                        this.isDisableEdit = isDisableEdit;
                        this.reOpenAccess = reOpenAccess;
                      }
                      this.UtilityService.showMessage("Status Changed successfully","Dismiss")
                      this.tempOverviewDetails.sales_status_id = status.status_id
                      this.tempOverviewDetails.sales_status_name= status.status_name
                      if(this.isStatusStageMappingAllowed){
                        const proposalStatus = this.getProposalStatus(status.status_id);
                        this.tempOverviewDetails.proposal_status_id = proposalStatus.proposal_status_id;
                        this.tempOverviewDetails.proposal_status_name = proposalStatus.proposal_status_name;
                      }
                      this.timelineView.nativeElement.classList.remove('loading-border')
                      this.projectCreationAccess = await this.projectAccessCheck() 
                      this.projectView = await  this.projectBtnviewaccess()
                      await this.fetchOpportunityOverview();
                      this.opportunityService.checkAdaptTemplate(this.opportunityId,status.status_id).subscribe((res)=>{
                        if(res['messType'] == 'A') {
                          this.UtilityService.openConfirmationSweetAlertWithCustom("Create Duplicate Activities?", "Do you wish to create Activities based on status template which is already available ?")
                          .then((copyConfirm) => {
          
                            if (copyConfirm)
                             {
                              this.opportunityService.adaptStatusTemplate(this.opportunityId,status.status_id).subscribe((res)=>{
                                if(res['messType'] == 'S') {
                                  this.UtilityService.showMessage('Activities created based on Opportunity status ', 'Dismiss');
                                } else {
                                  this.UtilityService.showMessage('Error while creating activities based on Opportunity status ', 'Dismiss');
                               
                                }
                              })
                             }
          
                            else{
                              return true
                            }
          
                        })
                        } else if(res['messType'] == 'S'){
                          this.opportunityService.adaptStatusTemplate(this.opportunityId,status.status_id).subscribe((res)=>{
                            if(res['messType'] == 'S') {
                              this.UtilityService.showMessage('Activities created based on Opportunity status ', 'Dismiss');
                            } else {
                              this.UtilityService.showMessage('Error while creating activities based on Opportunity status ', 'Dismiss');
                           
                            }
                          })
                        }
                      })
                      this.fetchQuoteFydata()
                      this.opportunityService.getStatusLogForOpp(parseInt(this.opportunityId)).subscribe((res:any)=>{
                        this.statusLogObservable.next(res.data);
                      })}
                      else{
                        this.UtilityService.showMessage(res['mesText'],"Dismiss")
                        this.timelineView.nativeElement.classList.remove('loading-border')
                      }
                    })
                  }
                 
                }
              }
            }
        })
      }
    else{  
      if(!poValueCheck["is_allowed"]){
        if(poValueCheck['type']=="WARN"){
        let copyConfirm = await this.UtilityService .openConfirmationSweetAlertWithCustom(
          poValueCheck['textMsg'],
          "Do you wish to close the opportunity with Opportunity Value and PO Value difference?"
        );
  
        if (!copyConfirm) {
          return true;
        }
      }
      else{
        this.UtilityService.showMessage(poValueCheck['textMsg'], 'Dismiss',3000);
        return ;
      }
      }
    const updateAllowed = this.isQuoteEnabled ? await this.resolveQuoteOpportunityIntegration(status.status_id) : true;

    if (!updateAllowed)
      return true;
    
    this.opportunityService.checkNdUpdateOpportunityStatus( this.opportunityId, status.status_id, this.overviewDetails,this.isQuoteEnabled ).then(async res => {
      
      if(res['messType']!="E")
      { 
        this.timelineView.nativeElement.classList.add('loading-border')
          console.log("VAl Changes")
          let changed_status  = _.where(this.salesStatusMaster,{id: status.status_id})
          if(changed_status[0]['is_end_form_needed']==1)      
          await this.opportunityService.openWinLossForm(this.overviewDetails , changed_status[0]['name']).then((res)=>{
            if(res['messType'])
            {
              if(res!="" && res["result"]!="")
              this.opportunityService.updateOpportunitySalesStatus(this.opportunityId,status.status_id).subscribe(async (res)=>{
                if(res['messType']=="S"){
                if(status.status_id == 17){
                  this.overviewDetails['isOpen'] = 0;
                  this.editAccess = this.opportunityService.checkUserforEditAccess(this.overviewDetails.sales_unit);
                  let isDisableEdit = await this.opportunityService.isDisableEdit(this.overviewDetails);
                  let reOpenAccess = await this.opportunityService.reOpenAccess(this.overviewDetails);
                  this.isDisableEdit = isDisableEdit;
                  this.reOpenAccess = reOpenAccess;
                }
                this.UtilityService.showMessage("Status Changed successfully","Dismiss")
                this.tempOverviewDetails.sales_status_id = status.status_id
                this.tempOverviewDetails.sales_status_name= status.status_name
                if(this.isStatusStageMappingAllowed){
                  const proposalStatus = this.getProposalStatus(status.status_id);
                  this.tempOverviewDetails.proposal_status_id = proposalStatus.proposal_status_id;
                  this.tempOverviewDetails.proposal_status_name = proposalStatus.proposal_status_name;
                }
                this.timelineView.nativeElement.classList.remove('loading-border')
                this.projectCreationAccess = await this.projectAccessCheck() 
                this.projectView = await  this.projectBtnviewaccess()
                await this.fetchOpportunityOverview();
                this.opportunityService.checkAdaptTemplate(this.opportunityId,status.status_id).subscribe((res)=>{
                  if(res['messType'] == 'A') {
                    this.UtilityService.openConfirmationSweetAlertWithCustom("Create Duplicate Activities?", "Do you wish to create Activities based on status template which is already available ?")
                    .then((copyConfirm) => {
    
                      if (copyConfirm)
                       {
                        this.opportunityService.adaptStatusTemplate(this.opportunityId,status.status_id).subscribe((res)=>{
                          if(res['messType'] == 'S') {
                            this.UtilityService.showMessage('Activities created based on Opportunity status ', 'Dismiss');
                          } else {
                            this.UtilityService.showMessage('Error while creating activities based on Opportunity status ', 'Dismiss');
                         
                          }
                        })
                       }
    
                      else{
                        return true
                      }
    
                  })
                  } else if(res['messType'] == 'S'){
                    this.opportunityService.adaptStatusTemplate(this.opportunityId,status.status_id).subscribe((res)=>{
                      if(res['messType'] == 'S') {
                        this.UtilityService.showMessage('Activities created based on Opportunity status ', 'Dismiss');
                      } else {
                        this.UtilityService.showMessage('Error while creating activities based on Opportunity status ', 'Dismiss');
                     
                      }
                    })
                  }
                })
                this.fetchQuoteFydata()
                this.opportunityService.getStatusLogForOpp(parseInt(this.opportunityId)).subscribe((res:any)=>{
                  this.statusLogObservable.next(res.data);
                })}
                else{
                  this.UtilityService.showMessage(res['mesText'],"Dismiss")
                  this.timelineView.nativeElement.classList.remove('loading-border')
                }
              })
            }
            else
            {
              if(res!="" && res["result"]){

                this.timelineView.nativeElement.classList.remove('loading-border')
                this.UtilityService.showMessage(res['result'],"Dismiss")
               
              }
         
            }
          })
          else{
            this.opportunityService.updateOpportunitySalesStatus(this.opportunityId,status.status_id).subscribe(async (res)=>{
              if(res['messType']=="S"){
              if(status.status_id == 17){
                this.overviewDetails['isOpen'] = 0;
                this.editAccess = this.opportunityService.checkUserforEditAccess(this.overviewDetails.sales_unit);
                let isDisableEdit = await this.opportunityService.isDisableEdit(this.overviewDetails);
                let reOpenAccess = await this.opportunityService.reOpenAccess(this.overviewDetails);
                this.isDisableEdit = isDisableEdit;
                this.reOpenAccess = reOpenAccess;
              }
              this.UtilityService.showMessage("Status Changed successfully","Dismiss")
              this.tempOverviewDetails.sales_status_id = status.status_id
              this.tempOverviewDetails.sales_status_name= status.status_name
              if(this.isStatusStageMappingAllowed){
                const proposalStatus = this.getProposalStatus(status.status_id);
                this.tempOverviewDetails.proposal_status_id = proposalStatus.proposal_status_id;
                this.tempOverviewDetails.proposal_status_name = proposalStatus.proposal_status_name;
              }
              this.timelineView.nativeElement.classList.remove('loading-border')
              this.projectCreationAccess = await this.projectAccessCheck() 
              this.projectView = await  this.projectBtnviewaccess()
              await this.fetchOpportunityOverview();
              this.opportunityService.checkAdaptTemplate(this.opportunityId,status.status_id).subscribe((res)=>{
                if(res['messType'] == 'A') {
                  this.UtilityService.openConfirmationSweetAlertWithCustom("Create Duplicate Activities?", "Do you wish to create Activities based on status template which is already available ?")
                  .then((copyConfirm) => {
  
                    if (copyConfirm)
                     {
                      this.opportunityService.adaptStatusTemplate(this.opportunityId,status.status_id).subscribe((res)=>{
                        if(res['messType'] == 'S') {
                          this.UtilityService.showMessage('Activities created based on Opportunity status ', 'Dismiss');
                        } else {
                          this.UtilityService.showMessage('Error while creating activities based on Opportunity status ', 'Dismiss');
                       
                        }
                      })
                     }
  
                    else{
                      return true
                    }
  
                })
                } else if(res['messType'] == 'S'){
                  this.opportunityService.adaptStatusTemplate(this.opportunityId,status.status_id).subscribe((res)=>{
                    if(res['messType'] == 'S') {
                      this.UtilityService.showMessage('Activities created based on Opportunity status ', 'Dismiss');
                    } else {
                      this.UtilityService.showMessage('Error while creating activities based on Opportunity status ', 'Dismiss');
                   
                    }
                  })
                }
              })
              this.fetchQuoteFydata()
              this.opportunityService.getStatusLogForOpp(parseInt(this.opportunityId)).subscribe((res:any)=>{
                this.statusLogObservable.next(res.data);
              })}
              else{
                this.UtilityService.showMessage(res['mesText'],"Dismiss")
                this.timelineView.nativeElement.classList.remove('loading-border')
              }
            })
          }

          
      }
  })
}

 this.projectCreationAccess = await this.projectAccessCheck() 
 this.projectView = await  this.projectBtnviewaccess()
 this.fetchQuoteFydata()
  }

  openEditScreenOnStatusChange(changedStatus) {
    if (this.editAccess) {
      this.opportunityService.getOpportunityAllField(this.opportunityId).subscribe(async res => {
        console.log(res); 
       

        res[0].status_id=changedStatus;
        const { CreateOpportunityComponent } = await import('src/app/modules/create-components/create-opportunity/create-opportunity.component');

        const createopportunityComponent = this.dialog.open(CreateOpportunityComponent, {
          height: '100%',
          width: '80%',
          position: { right: '0px' },
          data: { editDataForPatching: res[0],
            mode:'Edit' },
          disableClose: true
        });
        createopportunityComponent.afterClosed().subscribe(result => {
        if(result=="updated"){
        
          this.ngOnInit()
        }
        });
      })
    }   
    else {
      this.snackBar.open("Sorry, You don't have access to Edit!", 'dismiss', {
        duration: 4000,
      });
    }


  }

  resolveQuoteOpportunityIntegration = (currentStatus) => {

    return new Promise((resolve, reject) => {
      
      this._quoteService.resolveQuoteOppIntegration(this.opportunityId, currentStatus, 1)
      .pipe(takeUntil(this._onDestroy))
      .subscribe(async res => {

        if (res['messType'] == "S") {

            if (res['quoteMessageType']) {

              if (res['quoteMessageType'] == 'info')
                this._toaster.showInfo("Quote Info", res['messText'], 5000);

              else if (res['quoteMessageType'] == 'warning')
                this._toaster.showWarning("Quote Warning", res['messText'], 5000);

              else if (res['quoteMessageType'] == 'error')
                this._toaster.showError("Quote Error", res['messText'], 5000);

            }

            if (res.hasOwnProperty('allowStatusChange') && res['allowStatusChange'] == false)
              resolve(false);

            if (res.hasOwnProperty('showConfirmationPopup') && res['showConfirmationPopup'] == 1 && res['quoteId'])
              this.UtilityService.openConfirmationSweetAlertWithCustom("Copy Quote Value?", "Do you wish to copy Quote value to Opportunity ?")
                .then(async (copyConfirm) => {

                  if (copyConfirm)
                    await this.updateValueInOpportunity(res['quoteId']);
                  
                  resolve(true);

              });

            else {
              await this.fetchOpportunityOverview();

              resolve(true);
            }
            
        }

        else {

          this._toaster.showError("Error", res["messText"], this.opportunityService.longInterval);

          resolve(true);

        }

      },
        err => {
          console.log(err);
          this._toaster.showError("Error", "Error in Checking Quote Configuration", this.opportunityService.longInterval);
          reject(false);
        });

    });

  }

  openOpportunityValuelog = async () => {
    if (this.opportunityId) {

      const { QuoteActivityLogComponent } = await import('src/app/modules/quote-builder/screens/quote/components/quote-activity-log/quote-activity-log.component');

      this.dialog.open(QuoteActivityLogComponent, {
        height: '100vh',
        width: '35vw',
        data: {
          opportunityId: this.opportunityId
        },
        position: {
          right: '0'
        },
        disableClose: false
      });

    }

    else
      this._toaster.showError("Error", "Opportunity ID not found !", this.opportunityService.longInterval);

  }
  openOpportunityAuditlog = async (fieldName) => {
    if (this.opportunityId) {

      const { QuoteActivityLogComponent } = await import('src/app/modules/quote-builder/screens/quote/components/quote-activity-log/quote-activity-log.component');

      this.dialog.open(QuoteActivityLogComponent, {
        height: '100vh',
        width: '35vw',
        data: {
          opportunityId: this.opportunityId,
          fieldName:fieldName
        },
        position: {
          right: '0'
        },
        disableClose: false
      });

    }

    else
      this._toaster.showError("Error", "Opportunity ID not found !", this.opportunityService.longInterval);

  }
  openOpportunityClosureDatelog = async () => {
console.log('CAsd')

  }

  getOppQuoteValueStatus = async () => {

    this._quoteService.getOppQuoteValueStatus(this.opportunityId)
      .pipe(takeUntil(this._onDestroy))
      .subscribe(res => {

        if (res["messType"] == "S") {

          this.valueSyncButtonVisible = res["data"]["displaySyncButton"] || false;

          if (res["data"]["quoteId"])
            this.valueSyncQuoteId = res["data"]["quoteId"];

        }

      },
       err => {
        console.log(err);
        this._toaster.showError("Error", "Error in Checking Quote Opportunity Values", this.opportunityService.mediumInterval);
       });

  }

  updateOppValue = () => {

    if (this.isQuoteEnabled && this.valueSyncQuoteId)
      this.updateValueInOpportunity(this.valueSyncQuoteId);

  }

  updateValueInOpportunity = (quoteId: number) => {

    return new Promise((resolve, reject) => {

      this._quoteService.updateValueInOpportunity(this.opportunityId, quoteId)
      .pipe(takeUntil(this._onDestroy))
      .subscribe(async res => {

        if (res['messType'] == "S") {

          await this.fetchOpportunityOverview();

          this._toaster.showInfo("Value Updated", res["messText"], this.opportunityService.longInterval);

          this.valueSyncButtonVisible = false;

        }

        else
          this._toaster.showError("Error in Updating", res["messText"], this.opportunityService.longInterval);

        resolve(true);

      },
        err => {
          console.log(err);
          this._toaster.showError("Error", "Error in Updating Quote value in Opportunity", this.opportunityService.mediumInterval);
          reject(false);
        });

    });

  }

  async checkEditAcess()
  {
    let adminAccess = _.where(this.roleService.roles, { application_id: 36, role_id: 1}); 
    let editAccess = _.where(this.roleService.roles, { application_id: 36, object_id:6 ,operation: "*"});  
    console.log("accessList",editAccess)
    if (editAccess.length > 0){
      return true;
    }     
    else{
      return false;  
    }
    
  }

  async checkNotesEditAccess() {
    let editAccess = _.where(this.roleService.roles, { application_id: 36, object_id: 29419, operation: "*" });
    if (editAccess.length > 0) return true;
    else return false;
  }

  callInlineEditApiRestricted(){
    this.UtilityService.showMessage("Edit is restricted","Dismiss")
     
  }
  
  // async createProject(){
  //   console.log("Create Project")
  //   const { OpportunityProjectCreationComponent } = 
  //    await import ( 'src/app/modules/project-management/features/pm-creation/features/project-creation/opportunity-project-creation/opportunity-project-creation.component' ) 
  //    const { AfterSaveDialogComponent } = 
  //    await import ( 'src/app/modules/project-management/features/pm-creation/features/project-creation/after-save-dialog/after-save-dialog.component');
  //    const dialogRef = this.dialog.open ( OpportunityProjectCreationComponent , 
  //       { 
  //         disableClose: false, 
  //        data : 
  //           { 
  //             opportunityId:this.tempOverviewDetails.opportunity_id
  //           } 
  //         }
  //     )
      

  //         dialogRef.afterClosed().subscribe(result => {
  //           if (result.messType == 'S') {

            
  //             const dialogRef1 = this.dialog.open(AfterSaveDialogComponent, {
  //               disableClose: false,
  //               data: {
  //                 data: result,
  //                 mode:"Create"
  //               },
  //             });
  //             dialogRef1.afterClosed().subscribe(result => {
  //               console.log(result)
  //               if (!result) {
  //                 this.opportunityProjectDetails =[]
  //                 this.opportunityService.getOpportunityProjectDetails(this.tempOverviewDetails.opportunity_id).subscribe(res => {
  //                   if(res['messType'] == "S")
  //                   this.opportunityProjectDetails = res['messData']
  //                   console.log("res is : ",res)
  //                 },
  //                   err => {
  //                     console.log(err)
  //                 });
  //               } 
  //               else if (result) {
  //                 if(result['messType'] == "S"){
  //                 this.opportunityProjectDetails =[]
  //                 this.opportunityService.getOpportunityProjectDetails(this.tempOverviewDetails.opportunity_id).subscribe(res => {
  //                   if(res['messType'] == "S")
  //                   this.opportunityProjectDetails = res['messData']
  //                   console.log("res is : ",res)
  //                 },
  //                   err => {
  //                     console.log(err)
  //                 });
  //               }
  //               }
  //             })
  //           }
  //         });
  //       }

  async createProject() {
    console.log("Create Project")

    // let projectCreationVersion = this.formFieldData.find(x => x.field_name == "projectCreation")
    let projectCreationVersion = _.where(this.projectFormConfig, { type: "landing-header", field_name: "version", is_active: true });

    if (projectCreationVersion[0]?.is_active) 
      this._router.navigateByUrl(`main/project-management/create/%20project?opportunityId=${this.tempOverviewDetails.opportunity_id}`);
    
     else {

      const { OpportunityProjectCreationComponent } =
        await import('src/app/modules/project-management/features/pm-creation/features/project-creation/opportunity-project-creation/opportunity-project-creation.component')
      const { AfterSaveDialogComponent } =
        await import('src/app/modules/project-management/features/pm-creation/features/project-creation/after-save-dialog/after-save-dialog.component');
      const dialogRef = this.dialog.open(OpportunityProjectCreationComponent,
        {
          disableClose: false,
          data:
          {
            opportunityId: this.tempOverviewDetails.opportunity_id
          }
        }
      )


      dialogRef.afterClosed().subscribe(result => {
        if (result.messType == 'S') {


          const dialogRef1 = this.dialog.open(AfterSaveDialogComponent, {
            disableClose: false,
            data: {
              data: result,
              mode: "Create"
            },
          });
          dialogRef1.afterClosed().subscribe(result => {
            console.log(result)
            if (!result) {
              this.opportunityProjectDetails = []
              this.opportunityService.getOpportunityProjectDetails(this.tempOverviewDetails.opportunity_id).subscribe(res => {
                if (res['messType'] == "S")
                  this.opportunityProjectDetails = res['messData']
                console.log("res is : ", res)
              },
                err => {
                  console.log(err)
                });
            }
            else if (result) {
              if (result['messType'] == "S") {
                this.opportunityProjectDetails = []
                this.opportunityService.getOpportunityProjectDetails(this.tempOverviewDetails.opportunity_id).subscribe(res => {
                  if (res['messType'] == "S")
                    this.opportunityProjectDetails = res['messData']
                  console.log("res is : ", res)
                },
                  err => {
                    console.log(err)
                  });
              }
            }
          })
        }
      });
    }
  }
  
  projectClicked(data){
  console.log(data)
  //http://localhost:4401/main/project-management/6541/testin81534/Project%2067920%20Opportunity%20testing
            let navigationUrl =
              window.location.origin +
              '/main/project-management/' +
              data.project_id +
              '/' +
              this.UtilityService.encodeURIComponent(data.project_name) +
              '/' +
              data.id +
              '/' +
              this.UtilityService.encodeURIComponent(data.item_name)
            window.open(navigationUrl);
       
       
  }

  async  projectAccessCheck() {
    if(!this.projectCreationAllowedBasedOnAccount){
      return false;
    }
    else  if(!this.isActiveQuoteExists){
      return false;
    }
    else if(this.childRestriction){
      if (this.overviewDetails['parent_opportunity'] !== null && 
        this.overviewDetails['parent_opportunity'] !== undefined && 
        this.overviewDetails['parent_opportunity'] !== '') {
        return false;
    }
      else{
        return  _.where(this.salesStatusMaster, { status_id: this.tempOverviewDetails.sales_status_id, allow_project_creation:1}).length > 0 ? true : false;  
      }
    }
    else{
     return  _.where(this.salesStatusMaster, { status_id: this.tempOverviewDetails.sales_status_id, allow_project_creation:1}).length > 0 ? true : false;  
    }
  }
  async  projectBtnviewaccess() {
 
      return _.where(this.roleService.roles, { application_id: 36, object_id:29383 }).length > 0 ? true : false;
  
}
async  projectViewAccessCheck() {

    return _.where(this.roleService.roles, { application_id: 36, object_id:29384 }).length > 0 ? true : false;

}


fyYearChange = (currentYear) => {
  this.formFieldData = this.formFieldData.map((obj: any) => {
    if (obj.field_name === 'uobCurrentFYValue1')
      obj.label = "Est.Rev "+currentYear+"-"+(currentYear+1).toString().slice(-2)
    if (obj.field_name === 'uobCurrentFYValue2')
      obj.label = "Est.Rev "+(currentYear+1)+"-"+(currentYear+2).toString().slice(-2)
    if (obj.field_name === 'uobCurrentFYValue3')
      obj.label = "Est.Rev "+(currentYear+2)+"-"+(currentYear+3).toString().slice(-2)
    if (obj.field_name === 'uobCurrentFYValue4')
      obj.label = "Est.Rev "+(currentYear+3)+"-"+(currentYear+4).toString().slice(-2)
    if (obj.field_name === 'uobCurrentFYValue5')
      obj.label = "Est.Rev "+(currentYear+4)+"-"+(currentYear+5).toString().slice(-2)
    if (obj.field_name === 'uobCurrentFY1GM')
      obj.label = "Est.Rev GM FY"+(currentYear)+"-"+(currentYear+1).toString().slice(-2)
    if (obj.field_name === 'uobCurrentFY2GM')
      obj.label = "Est.Rev GM FY"+(currentYear+1)+"-"+(currentYear+2).toString().slice(-2)
    if (obj.field_name === 'uobCurrentFY3GM')
      obj.label = "Est.Rev GM FY"+(currentYear+2)+"-"+(currentYear+3).toString().slice(-2)
    if (obj.field_name === 'uobCurrentFY4GM')
      obj.label = "Est.Rev GM FY"+(currentYear+3)+"-"+(currentYear+4).toString().slice(-2)
    if (obj.field_name === 'uobCurrentFY5GM')
      obj.label = "Est.Rev GM FY"+(currentYear+4)+"-"+(currentYear+5).toString().slice(-2)
    return obj;
  });
}


async fetchQuoteFydata(){
  if (this.isQuoteEnabled) {

    await this._quoteService.getOpportunityFYData(this.tempOverviewDetails.opportunity_id, this.tempOverviewDetails.currency_code)
      .pipe(takeUntil(this._onDestroy))
      .subscribe(res => {

        if (res["messType"] == "S" && res["data"]) {}

          this.isActiveQuoteExists = res["data"]["activeQuoteExists"] || false; 
          this.oppFYdata = res["data"]["fyData"]
          
          for (const fyItem of this.oppFYdata){
          fyItem.fy_label="Est.Rev "+fyItem['fy_label']
          }
          this.revenueLoad = false
          console.log(this. isActiveQuoteExists)
          console.log(this. oppFYdata)
      },
      err => {
        console.log(err);
        
      });

    }
  else{
    
    this.revenueLoad = false
  }
}

async checkChildRestriction() {

  return new Promise((resolve, reject) => {

    this.opportunityService.checkChildRestriction()
    .pipe(takeUntil(this._onDestroy))
    .subscribe(res => {

      if (res['messType'] == "S") {

        // this._toaster.showInfo("Value Updated", res["messText"], this.opportunityService.longInterval);
        this.projectCreationText=res['text']
        resolve(res["data"]);
        

      }

      else
        // this._toaster.showError("Error in Updating", res["messText"], this.opportunityService.longInterval);
        this.projectCreationText=res['text']
        resolve(res["data"]);

    },
      err => {
        console.log(err);
        reject(false);
      });

  });

}
async checkProjectRestrictionAccount() {

  return new Promise((resolve, reject) => {

    this.opportunityService.checkProjectRestrictionAccount(this.opportunityId)
    .pipe(takeUntil(this._onDestroy))
    .subscribe(res => {

      if (res['messType'] == "S") {

        // this._toaster.showInfo("Value Updated", res["messText"], this.opportunityService.longInterval);
        if(!res["data"]){
        this.projectCreationText=res['text']
        }
        resolve(res["data"]);

      }

      else{
        // this._toaster.showError("Error in Updating", res["messText"], this.opportunityService.longInterval);
        if(!res["data"]){
          this.projectCreationText=res['text']
          }
        resolve(res["data"]);
      }

    },
      err => {
        console.log(err);
        reject(false);
      });

  });

}
  async onDateChange(type: string, data: any, selectedDate: Date): Promise<void> {
    console.log('Selected Type' + type);
    console.log(data);
    console.log('Selected selectedDate' + selectedDate);

    if (!type || !selectedDate) {
      this._toaster.showWarning('Cannot make changes!', 'Incorrect type or date..', this.opportunityService.longInterval);
      return;
    }

    if (type == 'Start Date') {
      this.opportunityService.updateOpportunityStartDate(this.opportunityId, moment(selectedDate).format('YYYY-MM-DD'))
        .subscribe(async res => {
          data.processing_start_date = selectedDate;
          this._toaster.showSuccess("Start date Updated Successfully", "", this.opportunityService.mediumInterval);
        }, err => {
          this._toaster.showError('Error while updating Opportunity Closure date', '', this.opportunityService.mediumInterval);
        });
    } else if (type == 'End Date') {
      this.opportunityService.updateOpportunityEndDate(this.opportunityId, moment(selectedDate).format('YYYY-MM-DD'))
        .subscribe(async res => {
          data.processing_end_date = selectedDate;
          this._toaster.showSuccess("End date Updated Successfully", "", this.opportunityService.mediumInterval);
        }, err => {
          this._toaster.showError('Error while updating Opportunity Closure date', '', this.opportunityService.mediumInterval);
        });
    } else if (type == 'Delivery Start Date') {
      this.deliveryStartTempDate = selectedDate;
      const label = this.labelForm.transform('deliveryStartDate', this.formFieldData, 'label');
      const updateDeliveryStartDate = () => {
        // this.deliveryStartTempDate = selectedDate;
        this.opportunityService.updateOpportunityDeliveryStartDate(this.opportunityId, moment(selectedDate).format('YYYY-MM-DD'))
          .subscribe(
            async res => {
              data.deliveryStartDate = selectedDate;
              this._toaster.showSuccess(`${label} Updated Successfully`, "", this.opportunityService.mediumInterval);
            },
            err => {
              this._toaster.showError(`Error while updating ${label}`, '', this.opportunityService.mediumInterval);
              console.error(err);
            }
          );
      };

      if (this.isActiveQuoteExists) {
        this.confirmSweetAlert("Changing Delivery Date will make the Quote InActive").then(confirm => {
          if (confirm.value) {
            console.log(confirm.value);
            this._quoteService.updateInActiveQuoteInOpportunity(this.tempOverviewDetails.opportunity_id)
              .pipe(takeUntil(this._onDestroy))
              .subscribe(
                res => {
                  if (res['messType'] == "S") {
                    this.isActiveQuoteExists = false;
                    this.deliveryStartTempDate = selectedDate;
                    updateDeliveryStartDate();
                  } else if (res['messType'] == "E") {
                    this.deliveryStartTempDate = null;
                    this._toaster.showError("Error", "Error in Updating Inactive Quote", 2500);
                  }
                },
                err => {
                  this.deliveryStartTempDate = null;
                  console.log(err);
                  this._toaster.showError("Error", "Error in Updating Inactive Quote", this.opportunityService.mediumInterval);
                }
              );
          } else {
            this.deliveryStartTempDate = null;
          }
        });
      } else {
        this.deliveryStartTempDate = selectedDate;
        updateDeliveryStartDate();
      }
    } else if (type == 'Delivery Finish Date') {
      this.deliveryEndTempDate = selectedDate;
      const label = this.labelForm.transform('deliveryFinishDate', this.formFieldData, 'label');
      const updateDeliveryEndDate = () => {
        this.opportunityService.updateOpportunityDeliveryEndDate(this.opportunityId, moment(selectedDate).format('YYYY-MM-DD'))
          .subscribe(
            async res => {
              data.deliveryFinishDate = selectedDate;
              this._toaster.showSuccess(`${label} Updated Successfully`, "", this.opportunityService.mediumInterval);
            },
            err => {
              this._toaster.showError(`Error while updating ${label}`, '', this.opportunityService.mediumInterval);
              console.error(err);
            }
          );
      };
        
      if (this.isActiveQuoteExists) {
        this.confirmSweetAlert("Changing Delivery Date will make the Quote InActive").then(confirm => {
          if (confirm.value) {
            console.log(confirm.value);
            this._quoteService.updateInActiveQuoteInOpportunity(this.tempOverviewDetails.opportunity_id)
              .pipe(takeUntil(this._onDestroy))
              .subscribe(
                res => {
                  if (res['messType'] == "S") {
                    this.isActiveQuoteExists = false;
                    this.deliveryEndTempDate = selectedDate;
                    updateDeliveryEndDate();
                  } else if (res['messType'] == "E") {
                    this.deliveryEndTempDate = null;
                    this._toaster.showError("Error", "Error in Updating Inactive Quote", 2500);
                  }
                },
                err => {
                  this.deliveryEndTempDate = null;
                  console.log(err);
                  this._toaster.showError("Error", "Error in Updating Inactive Quote", this.opportunityService.mediumInterval);
                }
              );
          } else {
            this.deliveryEndTempDate = null;
          }
        });
      } else {
        this.deliveryEndTempDate = selectedDate;
        updateDeliveryEndDate();
      }
    } else if (type == 'Actual Closure Date') {
      this.tempSelectedDate = selectedDate;
      this.opportunityService.updateOpportunityActualClosureDate(this.opportunityId, moment(selectedDate).format('YYYY-MM-DD'), this.tempOverviewDetails.sales_status_id)
        .subscribe(async res => {
          if (res['messType'] === "E") {
            this.UtilityService.showMessage(res['messText'], "Dismiss", this.opportunityService.longInterval);
            this.tempSelectedDate = null;
          } else if (res['messType'] === "S") {
            this._toaster.showSuccess("Actual closure date Updated Successfully", "", this.opportunityService.mediumInterval);
            data.actual_closure_date = selectedDate;
          }
        }, err => {
          this.tempSelectedDate = null;
          this._toaster.showError('Error while updating Actual closure date', '', this.opportunityService.mediumInterval);
        });
    }
    else if (type == 'RFP release date') {
      this.opportunityService.updateOpportunityRFPReleaseDate(this.tempOverviewDetails.opportunity_id, moment(selectedDate).format('YYYY-MM-DD'))
        .subscribe(async res => {
          data.rfp_date = selectedDate;
          this._toaster.showSuccess("RFP release date Updated Successfully", "", this.opportunityService.mediumInterval);
        }, err => {
          this._toaster.showError('Error while updating RFP release date', '', this.opportunityService.mediumInterval);
        });
    } else if (type == 'Proposal date') {
      this.opportunityService.updateOpportunityProposalSubDate(this.tempOverviewDetails.opportunity_id, moment(selectedDate).format('YYYY-MM-DD'))
        .subscribe(async res => {
          data.proposal_submission_date = selectedDate;
          this._toaster.showSuccess("Submission to Customer date Updated Successfully", "", this.opportunityService.mediumInterval);
        }, err => {
          this._toaster.showSuccess('Error updating Proposal date', '', this.opportunityService.mediumInterval);
        });
    } else if (type == 'Proposal Sales Date') {
      this.opportunityService.updateOpportunityProposalSalesDate(this.tempOverviewDetails.opportunity_id, moment(selectedDate).format('YYYY-MM-DD'))
        .subscribe(async res => {
          this.tempOverviewDetails.sales_proposal_date = selectedDate;
          this._toaster.showSuccess("Submission to Sales date Updated Successfully", "", this.opportunityService.mediumInterval);
        }, err => {
          this._toaster.showError('Error while updating proposal sales date', '', this.opportunityService.mediumInterval);
        });
    }
  }

  async fetchOpportunityOverview(): Promise<void> {
    try {
      const res: any = await this.opportunityService.opportunityOverview(this.opportunityId).toPromise();
  
      this.overviewDetails = res.opportunityOverview[0];
      this.overviewDetails.win_loss_details = JSON.parse(this.overviewDetails.win_loss_details);
      this.tempOverviewDetails = this.overviewDetails;
      this.editAccess = this.opportunityService.checkUserforEditAccess(this.overviewDetails.sales_unit);
      this.current_user = res.oid;
      
      this.planned_presale_history = JSON.parse(this.overviewDetails.planned_presales_hours_history);
      this.planned_presale_logs = JSON.parse(this.overviewDetails.planned_presales_hours_logs);
      this.planned_presales_hours_comment = (this.overviewDetails.planned_presales_hours_comment == null) ? [] : JSON.parse(this.overviewDetails.planned_presales_hours_comment);
  
      this.bid_qualification_history = JSON.parse(this.overviewDetails.bid_qualification_history);
      this.bid_qualification_logs = JSON.parse(this.overviewDetails.bid_qualification_logs);
      this.bid_qualification_comments = (this.overviewDetails.bid_qualification_comments == null) ? [] : JSON.parse(this.overviewDetails.bid_qualification_comments);
  
      this.reasonPresent = (this.overviewDetails.reason == null || this.overviewDetails.reason == "") ? false : true;
      this.reason = this.reasonPresent ? JSON.parse(this.overviewDetails.reason) : undefined;
      console.log(this.reason);
  
      this.presalesHours();
      this.service_line = this.overviewDetails?.service_line != null && this.overviewDetails.service_line != '' ? JSON.parse(this.overviewDetails.service_line) : [];
  
      console.log(this.overviewDetails);
      this.bidStatus = this.overviewDetails.bid_perc != null
        ? this.overviewDetails.is_bid == 1
          ? '/ Bid' : '/ No Bid'
        : '';
  
      if (this.overviewDetails.bid_perc != null && this.overviewDetails.is_bid != 1) {
        this.noBidQualification();
      }
  
      this.isDisableEdit = await this.opportunityService.isDisableEdit(this.overviewDetails);
      this.reOpenAccess = await this.opportunityService.reOpenAccess(this.overviewDetails);
  
      const stagewiseConfig: any = await this.opportunityService.getStagewiseFormFieldConfig(this.overviewDetails.sales_status_id);
      this.tempstagewiseFormFieldData = JSON.parse(stagewiseConfig.messData[0].stagewise_field_config);
      this.stagewiseFormFieldDataDisabled = _.filter(this.tempstagewiseFormFieldData, function (item: any) {
        return (item.changeIndicator == true && item.disabled);
      });
  
      console.log(this.stagewiseFormFieldDataDisabled);
    } catch (err) {
      console.error(err);
    }
  }
  
  async getGlobalCRMDateFormat(): Promise<void>  {
    this.udrfServices.getCRMGlobalDateFormat().subscribe(
      (response: any) => {
        if (response.messType === 'S' && response.data && response.data.length > 0) {
          const config = JSON.parse(response.data[0].config);
          this.dateFormat = config.format || this.dateFormat;
          this.dateAdapter.setLocale('en-US');
          (this.dateAdapter as any).format = (date: Date) => moment(date).format(this.dateFormat);
        }
      },
      (error) => {
        console.error('Error retrieving date format', error);
      }
    );
  }

  prepareTooltipContent() {
    const opportunityLabel = this.labelForm.transform('opportunityValueMillion', this.formFieldData, 'label') || 'Opportunity Value';
    const opportunityValues = this.overviewDetails?.opportunity_value || [];
    const formattedOpportunityValues = opportunityValues
      .filter((val: { currency_code: string; }) => val.currency_code === 'USD')
      .map((val: { value: number; currency_code: string; }) => `${val.value} ${val.currency_code}`)
      .join(', ');

    const changeRequestLabel = this.labelForm.transform('changeRequestValue', this.formFieldData, 'label') || 'Change Request';
    const changeRequestValues = this.overviewDetails?.change_request_value || [];
    const formattedChangeRequestValues = changeRequestValues
      .filter((val: { currency_code: string; }) => val?.currency_code === 'USD')
      .map((val: { value: number; currency_code: string; }) => `${changeRequestLabel}: ${val?.value} ${val?.currency_code}`)
      .join('\n');

    const showChangeRequestSection = formattedChangeRequestValues.trim().length > 0;

    const separator = '------------------------'; 

    this.dynoTooltipContent = `${opportunityLabel}: ${formattedOpportunityValues}`;
    if (showChangeRequestSection) {
      this.dynoTooltipContent += `\n${formattedChangeRequestValues}`;
    }
  }

  async checkChildOpportunityCreationAccess(): Promise<void> {
    try {
      const response: any = await this.opportunityService.checkChildOpportunityCreationAccess(this.overviewDetails.opportunity_id).toPromise();
      this.child_opportunity_creation_access = response?.canCreateChildOpportunity || false;
    } catch (error) {
      console.error('Error checking child opportunity creation access:', error);
    }
  }

  navigateToPartyAccounts(){
    if(this.overviewDetails.party_account_name != '-'){
      this.opportunityService.checkIfAccountExist(this.overviewDetails.party_account_id).subscribe(
        async (res) => {
          if(res['exists']){
            let navigationUrl =
              window.location.origin +
              '/main/accounts/' +
              this.overviewDetails.party_account_id +
              '/' +
              this.UtilityService.encodeURIComponent(this.overviewDetails.party_account_name) +
              '/overview';
            window.open(navigationUrl);
          } else {
            this.UtilityService.showMessage('Account Does not Exist!', 'Dismiss');
          }
        },
        (err) => {
          console.log(err);
          this.UtilityService.showMessage('Account Does not Exist!', 'Dismiss');
        }
      );
    }
    else{
      this.UtilityService.showErrorMessage("No Account is linked!","Dismiss")
    }
  }

  changeFlag = async () => {
    if (this.opportunityId) {

      const { OpportunityFlagChangeWizardComponent } = await import('src/app/modules/opportunities/features/opportunities-detail/components/opportunity-flag-change-wizard/opportunity-flag-change-wizard.component');
      let label = this.labelForm.transform('riskFlagChangeWizard', this.formFieldData, 'label');
      let riskLable = this.labelForm.transform('atRisk', this.formFieldData, 'label');

      const dialogRef = this.dialog.open(OpportunityFlagChangeWizardComponent, {
        data: {
          opportunity_id: Number(this.opportunityId),
          at_risk: this.tempOverviewDetails?.at_risk,
          project_id: this.opportunityProjectDetails.project_id || '',
          label: label,
          riskLable:riskLable
        },
        disableClose: false
      });

      dialogRef.afterClosed().subscribe(async(result) => {
        if (result.messType == 'S') {
          this.tempOverviewDetails.at_risk == result?.at_risk
          this._toaster.showSuccess("Risk Status Updated successfully", '', this.opportunityService.mediumInterval)    
          await this.fetchOpportunityOverview();
        } 
        else if (result.messType == 'E')
          this._toaster.showError("Error Changing the Flag!", result?.error || '', this.opportunityService.mediumInterval)    
      });
    }

    else
      this._toaster.showError("Error", "Opportunity ID not found !", this.opportunityService.longInterval);

  }

  getQuoteConfiguration = () => {

    return new Promise((resolve, reject) => {

      this._qbMasterService.quoteConfiguration
        .pipe(takeUntil(this._onDestroy))
        .subscribe(res => {

          let quoteConfiguration = res;

          for (const configItem of quoteConfiguration)
            if (configItem && configItem['quote_config_name'] && configItem.hasOwnProperty('quote_config_value')) {

              switch (configItem['quote_config_name']) {

                case 'quote_masking_configuration':

                  this.masking_configuration = configItem['quote_config_value'] || null;

                  break;

                case 'quote_field_config':

                  this.quote_field_config = configItem['quote_config_value'] || null;

                  break;

                default:

                  break;

              }

            }

          resolve(true);

        });

    });

  }

  /**
* @description Determines if a field should be restricted based on key and resource_type.
* @param key - The field key to check.
* @returns True if the field should be restricted, otherwise false.
*/
  checkFieldShouldbeRestricted(fieldKey: string): boolean {
    const currentUserRole = this._ticket.getCurrentUserRole();
    const field = this.quote_field_config.find(item => item.key === fieldKey);
    return !(field?.role_access_restriction && field.role_access_restriction.includes(currentUserRole));
  }

  ngOnDestroy() {
    this._onDestroy.next();
    this._onDestroy.complete();
  }
}


