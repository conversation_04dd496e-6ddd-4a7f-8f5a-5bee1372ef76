import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { catchError } from 'rxjs/operators';
import { ErrorService } from 'src/app/services/error/error.service';
import { EMPTY, Observable } from 'rxjs';
import { MatSnackBar } from '@angular/material/snack-bar';
import { RolesService } from 'src/app/services/acl/roles.service';
import * as _ from 'underscore';
import { UtilityService } from 'src/app/services/utility/utility.service';
import { MatDialog} from '@angular/material/dialog';
import { LoginService } from 'src/app/services/login/login.service';
import moment from 'moment';
import { DocumentUploadComponent } from 'src/app/modules/shared-lazy-loaded-components/document-manager/components/document-upload/document-upload.component';
import { ToasterService } from 'src/app/services/toaster-service/toaster.service';
 
@Injectable({
  providedIn: 'root'
})
export class OpportunityService {
 
  constructor(
    private http: HttpClient,
    private errorService: ErrorService,
    private snackBar: MatSnackBar,
    private _roleService: RolesService,
    private _utilService: UtilityService,
    public dialog: MatDialog,
    private loginService: LoginService,
    private _toaster: ToasterService,
  ) { }

  
  currentUser = this.loginService.getProfile().profile;

  saveReportState(state, name, field_conf, application_id) {
    return this.http.post("api/userExperience/saveReportState", { state: state, name: name, field_conf: field_conf, application_id: application_id }).pipe(
      catchError(err => {
        console.log(err);
        this.errorService.userErrorAlert((err && err.code) ? err.code : (err && err.error) ? err.error.code : 'NIL', "Error while retrieving Column info list", (err && err.params) ? err.params : (err && err.error) ? err.error.params : {})
        return EMPTY;
      })
    );
  }
  updateReportState(state, customization_id, field_conf, application_id) {
    return this.http.post("api/userExperience/updateReportState", { application_id, state: state, customization_id: customization_id, field_conf: field_conf }).pipe(
      catchError(err => {
        console.log(err);
        this.errorService.userErrorAlert((err && err.code) ? err.code : (err && err.error) ? err.error.code : 'NIL', "Error while retrieving Column info list", (err && err.params) ? err.params : (err && err.error) ? err.error.params : {})
        return EMPTY;
      })
    );
  }

  updateApprovalDetailInline = (activityId, details) => {
    return this.http.post("/api/activity/editApprovalInline", {
      approvalFormDetails: details,
      activityId: activityId
    });
  };

  addToMyOpportunities = (id) => {
    return this.http.post("/api/opportunity/addToMyOpportunities", {
      opportunityId: id
    });
  };

  insertComment = (comment, activityId) => {
    return this.http.post("/api/activity/insertComment", {
      comment: comment,
      activityId: activityId
    });
  };

  removeFromMyOpportunities = (oppId) => {
    return this.http.post('/api/opportunity/removeFromMyOpportunitiesIds', {
      opportunityId: oppId
    });
  };

  taskFormDetails(activity_id) {
    return new Promise((resolve, reject) => {
      this.http.post("/api/activity/taskFormDetails", { activity_id }).subscribe(res => {
        return resolve(res)
      },
        err => {
          return reject(err)
        })
    })
  }

  meetingFormDetails(activity_id) {
    return new Promise((resolve, reject) => {
      this.http.post("/api/activity/meetingFormDetails", { activity_id }).subscribe(res => {
        return resolve(res)
      },
        err => {
          return reject(err)
        })
    })
  }

  mailFormDetails(activity_id) {
    return new Promise((resolve, reject) => {
      this.http.post("/api/activity/mailFormDetails", { activity_id }).subscribe(res => {
        return resolve(res)
      },
        err => {
          return reject(err)
        })
    })
  }

  callLogFormDetails(activity_id) {
    return new Promise((resolve, reject) => {
      this.http.post("/api/activity/callLogFormDetails", { activity_id }).subscribe(res => {
        return resolve(res)
      },
        err => {
          return reject(err)
        })
    })
  }

  getMyOpportunityIds = () => {
    return this.http.post("/api/opportunity/getMyOpportunitiesIds", {});
  };

  updateCheckListInline = (activityId, details) => {
    return this.http.post("/api/activity/editCheckListInline", {
      checklistFormDetails: details,
      activityId: activityId
    });
  };

  getActivityFilterMaster = (id) => {
    return this.http.post("/api/opportunity/opportunityActivityFilterMasterData", {
      applicationReferenceId: id
    });
  };

  getActivitySearchResults = (id, text) => {
    return this.http.post("/api/opportunity/getActivityListForOpportunitySearch", {
      applicationReferenceId: id,
      searchParameter: text
    });
  };

  getActivityFilteredData = (id, data) => {
    return this.http.post("/api/opportunity/opportunityActivityFilter", {
      applicationReferenceId: id,
      filterData: data
    });
  };

  getActivityNotes = (activityId) => {
    return this.http.post("/api/activity/viewNotes", {
      activityId: activityId
    });
  };

  saveActivityNote = (activityId, title, noteColor, content) => {
    return this.http.post("/api/activity/saveActivityNote", {
      activityId: activityId,
      title: title,
      color: noteColor,
      content: content
    });
  };

  deleteActivityNote = (activityId, noteId) => {
    return this.http.post("/api/activity/deleteOppActivityNote", {
      activityId: activityId,
      noteId: noteId
    });
  };

  updateActivityNote = (
    activityId, noteId, noteTitle, noteContent, noteColor
  ) => {
    return this.http.post("/api/activity/updateOppActivityNote", {
      activityId: activityId,
      noteId: noteId,
      noteTitle: noteTitle,
      noteContent: noteContent,
      noteColor: noteColor
    });
  };

  updateTaskDetailInline = (activityId, details) => {
    return this.http.post("/api/activity/editTaskInline", {
      taskFormDetails: details,
      activityId: activityId
    });
  };
  getActivityComments = (activityId) => {
    return this.http.post("/api/activity/retrieveActivityComments", {
      activityId: activityId
    });
  };

  getActivityAttachments = (activityId) => {
    return this.http.post("/api/activity/retrieveActivityAttachments", {
      activityId: activityId
    });
  };

  
  getCRMAttachmentsConfigs()
{
  return new Promise((resolve, reject) => {
    this.http.get("/api/salesMaster/getCRMAttachmentsConfigs").subscribe(res => {
      return resolve(res)
    },
      err => {
        return reject(err)
      })
  })
}

  updateActivityAttachment(activityId, file) {
    return this.http.post('/api/activity/updateActivityAttachment', {
      activity_id: activityId,
      file: file
    });
  }
  deleteActivityAttachment(activityId, file) {
    return this.http.post('/api/activity/deleteActivityAttachment', {
      activity_id: activityId,
      file: file
    });
  }

  getAllAccounts(orgCodes) {
    return this.http.post("/api/contacts/getAllAccounts", { orgCodes });
  }
  getActivityDetail(activityId) {
    return this.http.post("/api/activity/getActivityDetails", {
      activity_id: activityId
    });
  }
  //green tick
  completeChecklist = (subActivityId) => {
    return this.http.post("/api/activity/completeSalesSubActivity", {
      subActivityId: subActivityId
    });
  };
  getTemplates(serviceId) {
    return this.http.post("/api/activity/getActivityMasterTemplate", {
      applicationId: 36,
      serviceId: serviceId
    });
  }
  //master
  getActivityPhase = (oppId) => {

    return new Promise((resolve, reject)=>{
      this.http.post("/api/opportunity/getPhaseMasterForCreation", {opportunityId: oppId}).subscribe(res=>{
        resolve(res)
      },(err)=>{
        reject(err)
      })
    })

  };
  //edit Opp Task
  editOppTask = (activityId, formData, updateAll) => {
    return this.http.post("/api/activity/editTask", {
      activity_id: activityId,
      taskFormDetails: formData,
      updateAll: updateAll

    })
  };
  //edit checklist
  editCheckList = (activityId, formData) => {
    return this.http.post("/api/activity/editCheckList", {
      activity_id: activityId,
      checklistFormDetails: formData
    });
  };

  //editApproval
  updateApproval = (activityId, formData) => {
    return this.http.post('/api/activity/editApproval', {
      activityId: activityId,
      approvalFormDetails: formData
    });
  };

  //get opportunity activity details by id
  getActivityDetailsById = (activityId) => {
    return this.http.post("/api/activity/getActivityDetailsOpportunity ", {
      activityId: activityId
    });
  };
  //create checklist 
  createCheckList = (formDetail, appId, oppId) => {
    return this.http.post("/api/activity/createChecklist", {
      checklistFormDetails: formDetail,
      applicationId: appId,
      opportunityId: oppId
    });
  };

  getOpportunityActivityIds(opportunityId) {
    return this.http.post("/api/opportunity/getActivityListForOpportunity", {
      opportunityId: opportunityId
    });
  }
  getMyOpportunityActivities(opportunityId) {
    return this.http.post('/api/opportunity/getMyOpportunityActivities', {
      opportunityId: opportunityId
    });
  }

  getLeadSource() {
    return this.http.post("/api/salesMaster/leadSource",{});
  }
  getProbabilityMaster() {
    return this.http.post("/api/salesMaster/getProbabilityMaster", {
      applicationId: 36
    });
  }

  getOpportunityAllField(opportunityId) {
    return this.http.post('/api/opportunity/getAllOpportunityFields', {
      opportunityId: opportunityId
    });
  }

  updatePlForOpportunities() {
    return this.http.post('/api/opportunity/updatePandL', {});
  }

  getExistingContacts = (orgCodes) => {
    return this.http.post('/api/contacts/allActiveContactsDropdown', { orgCodes: orgCodes });
  };

  getSalesUnit() {
    return this.http.get('/api/salesMaster/salesUnit')
  }
  
  getServiceLineMaster() {
    return this.http.post('/api/salesMaster/getServiceLineMaster', {})
  }

  getServiceType() {
    return this.http.post('/api/salesMaster/getServiceTypeMaster',{});
  }

  getSalesType() {
    return this.http.post('/api/salesMaster/getSalesStatusMasterForEdit',{
      statusValues: this.getStatusObjectEntries()
      
    });
  }

  getProposalType() {
    return new Promise((resolve, reject) => {
      this.http.post('/api/salesMaster/getProposalStatusMaster',{}).subscribe(res => {
        return resolve(res)
      }, err => {
        return reject(err)
      })
    })
    // return this.http.post('/api/salesMaster/getProposalStatusMaster',{});
  }

  getActivityStatusMasterDataPromise() {
    return new Promise((resolve, reject) => {
      this.http.post('/api/salesMaster/getActivityStatusMaster',{}).subscribe(res => {
        return resolve(res)
      }, err => {
        return reject(err)
      })
    })
  }

  getProposalTypeBid() {

    return new Promise((resolve, reject) => {
      this.http.post('/api/salesMaster/getProposalStatusMaster',{}).subscribe(res => {
        return resolve(res);
      }, err => {
        return reject(err);
      });
    });
  }
  getOpportunityStatusSalesGov(existing_status) {
    let statusValues= this.getStatusObjectEntries()
    return new Promise((resolve, reject) => {
      this.http.post('/api/salesMaster/getSalesStatusMasterForEdit',{statusValues,existing_status}).subscribe(res => {
        return resolve(res);
      }, err => {
        return reject(err);
      });
    });
  }
  getbidType() {
    return this.http.post('/api/salesMaster/getBidTypeMaster',{});
  }
  getPipelineMaster() {
    return this.http.post("/api/salesMaster/pipelineMaster",{});
  }
  // Fetch all the opportunities Id in opportunities home screen
  getAllOpportunitiesId(orgCodes) {
    console.log(orgCodes);
    return new Promise((resolve, reject) => {
      this.http.post('/api/opportunity/getActiveOpportunityIds', { orgCodes: orgCodes }).subscribe(res => {
        return resolve(res);
      }, err => {
        return reject(err);
      });
    });
  }

  //change ceo attention status
  changeCeoAttentionStatus(id, status) {
    return this.http.post('/api/opportunity/ceoAttention', {
      opportunityId: id,
      flag: status
    });
  }
  //Fetch all the opportunities data in home screen
  getAllOpportunities(opportunity_id) {
    return this.http.post('/api/opportunity/getOpportunityDetails', {
      opportunityId: opportunity_id
    });
  }

  getContactsByAccounts = (accountId) => {
    return this.http.post('api/accounts/getContactsByAccounts', {
      account_id: accountId
    });
  };
  createOpportunity(opportunityForm) {
    return new Promise((resolve, reject) => {
      this.http.post("/api/opportunity/createOpportunity", { opportunityForm }).subscribe(res => {
        return resolve(res);
      }, err => {
        return reject(err);
      });
    });
  }

  getOpportunityAttachments(oppId) {
    return this.http.post('/api/opportunity/getOpportunityAttachments', {
      opportunity_id: oppId
    });
  }
  updateOpportunityAttachment(oppId, file) {
    return this.http.post('/api/opportunity/updateOpportunityAttachment', {
      opportunity_id: oppId,
      file: file
    });
  }
  deleteOpportunityAttachment(oppId, file) {
    return this.http.post('/api/opportunity/deleteOpportunityAttachment', {
      opportunity_id: oppId,
      file: file
    });
  }
  editOpportunity(data, id) {
    return this.http.post('/api/opportunity/editOpportunity', {
      opportunityForm: data,
      opportunityId: id
    });
  }

  serachOpportunities(text, orgCodes) {
    return this.http.post('/api/opportunity/opportunitySearch', {
      search_parameter: text,
      orgCodes: orgCodes
    });
  }
  opportunityDetails(opportunityId) {
    return this.http.post('/api/opportunity/getOpportunityDetails', {
      opportunityId: opportunityId
    });
  }

  opportunityOverview(opportunityId) {
    return this.http.post('/api/opportunity/opportunityOverview', {
      opportunity_id: opportunityId
    });
  }

  getPipelineReportOpportunities(orgCodes) {
    return this.http.post('/api/opportunity/getPipelineReport', {
      orgCodes: orgCodes
    });
  }
  changeProposalStatus(id, status) {
    if (status == 'in process') {
      return this.http.post("/api/opportunity/moveToInprocess", {
        opportunity_id: id
      });
    }
    else if (status == 'in approval') {
      return this.http.post("/api/opportunity/moveToInApproval", {
        opportunity_id: id
      });
    }
    else if (status == 'approved') {
      return this.http.post("/api/opportunity/moveToApproved", {
        opportunity_id: id
      });
    }
    else if (status == 'completed') {
      return this.http.post("/api/opportunity/moveToCompleted", {
        opportunity_id: id
      });
    }
  }
  getFilterMaster() {
    return this.http.post('/api/opportunity/opportunityFilterMasterData', {});
  }
  getFilterData(data, orgCodes) {
    return this.http.post('/api/opportunity/opportunityFilter', {
      filterData: data,
      orgCodes: orgCodes
    });
  }

  createApprovalActivity(approvalForm, opportunityId) {
    return this.http.post("/api/activity/createApproval", {
      applicationId: 36,
      approvalFormDetails: approvalForm,
      opportunityId: opportunityId
    });
  }
  createTask(taskFormDetails) {
    return this.http.post("/api/activity/createTask", {
      taskFormDetails: taskFormDetails
    });
  }
  deleteActivity(activity_id) {
    return this.http.post("/api/activity/deleteActivity", {
      activity_id: activity_id
    });
  }
  moveToSHSCompleted(opportunityId) {
    return this.http.post("/api/opportunity/moveToSHSCompleted", {
      opportunityId: opportunityId
    });
  }
  completeActivity(activity_id, is_completed) {
    return new Promise((resolve, reject) => {
      this.http.post("/api/activity/completeActivity", { activity_id, is_completed }).subscribe(res => {
        return resolve(res);
      },
        err => {
          return reject(err);
        });
    });
  }
  startActivity(activity_id) {
    return new Promise((resolve, reject) => {
      this.http.post("/api/activity/startActivity", { activity_id }).subscribe(res => {
        return resolve(res);
      },
        err => {
          return reject(err);
        });
    });
  }
  openActivity(activity_id) {
    return new Promise((resolve, reject) => {
      this.http.post("/api/activity/openActivity", { activity_id }).subscribe(res => {
        return resolve(res);
      },
        err => {
          return reject(err);
        });
    });
  }
  getGovernanceTypes(applicationId) {
    return new Promise((resolve, reject) => {
      this.http.post("/api/activity/getPresalesGovernanceActivityType", { applicationId: applicationId }).subscribe(res => {
        return resolve(res);
      },
        err => {
          return reject(err);
        });
    });
  }
  getSalesGovernanceTypes(applicationId) {
    return new Promise((resolve, reject) => {
      this.http.post("/api/activity/getSalesGovernanceActivityType", { applicationId: applicationId }).subscribe(res => {
        return resolve(res);
      },
        err => {
          return reject(err);
        });
    });
  }
  getOpportunityIDsDataBidManagerView = (filtersCummulation, application_id) => {
    return this.http.post('/api/opportunity/bidManagerFilterSP', {
      filtersCummulation: filtersCummulation,
      application_id: application_id
    });
  };
  getOpportunityDataBidManagerView = (opportunityId, limits) => {
    return this.http.post('/api/opportunity/getOpportunityDataBidManagerView', {
      opportunityId: opportunityId,
      applicationId: 66,
      limits: limits
    });
  };
  getOpportunityIDsTypeFilterBidManagerView = (limits, govTypes) => {
    return this.http.post('/api/opportunity/getOpportunityIDsTypeFilterBidManagerView', {
      limits: limits,
      govTypes: govTypes
    });
  };
  getBidManagerGlobalSearchIDs = (filtersCummulation, application_id, searchText) => {
    return this.http.post('/api/opportunity/getBidManagerGlobalSearchIDs', {
      filtersCummulation: filtersCummulation,
      application_id: application_id,
      searchText: searchText
    });
  };

  getOpportunityIDsDataSalesGovernanceReport = (filtersCummulation, application_id) => {
    return this.http.post('/api/opportunity/salesGovernanceFilterSP', {
      filtersCummulation: filtersCummulation,
      application_id: application_id
    });
  };
  getOpportunityDataSalesGovernanceReport = (opportunityId, limits) => {
    return this.http.post('/api/opportunity/getOpportunityDataSalesGovernanceReport', {
      opportunityId: opportunityId,
      limits: limits
    });
  };
  getOpportunityIdListSalesGovernanceSearch = (filtersCummulation, application_id, searchText) => {
    return this.http.post('/api/opportunity/getSalesGovernanceSearchIDs', {
      filtersCummulation: filtersCummulation,
      application_id: application_id,
      searchText: searchText
    });
  };
  getOpportunityIDsDataMarketingGovernanceReport = (filtersCummulation, application_id) => {
    return this.http.post('/api/opportunity/getMarketingGovernanceFilterSP', {
      filtersCummulation: filtersCummulation,
      application_id: application_id
    });
  };
  getOpportunityDataMarketingGovernanceReport = (opportunityId, limits) => {
    return this.http.post('/api/opportunity/getOpportunityDataMarketingGovernanceReport', {
      opportunityId: opportunityId,
      limits: limits
    });
  };
  getOpportunityIdListMarketingGovernanceSearch = (filtersCummulation, application_id, searchText) => {
    return this.http.post('/api/opportunity/getMarketingGovernanceSearchIDs', {
      filtersCummulation: filtersCummulation,
      application_id: application_id,
      searchText: searchText
    });
  };
  // getOpportunityIDsTypeFilterBidManagerView=(limits,govTypes)=>{
  //   return this.http.post('/api/opportunity/getOpportunityIDsTypeFilterBidManagerView', {
  //     limits:limits,
  //     govTypes:govTypes
  //   })
  // }
  getReportViewsList = (application_id) => {
    return this.http.post('/api/userExperience/getReportUserViews', {
      application_id: application_id
    }).pipe(
      catchError(err => {
        console.log(err);
        this.errorService.userErrorAlert((err && err.code) ? err.code : (err && err.error) ? err.error.code : 'NIL', "Error while retrieving Column info list", (err && err.params) ? err.params : (err && err.error) ? err.error.params : {})
        return EMPTY;
      })
    );
  };

  convertToLocalTime = async (time) => {
    let localTime = new Date(time);
    let timezoneOffset = localTime.getTimezoneOffset() * 60000;
    localTime.setTime(localTime.getTime() - timezoneOffset);
    return localTime;
  };

  updateOpportunityProbability(oppId, probId) {
    return this.http.post('/api/opportunity/editProbability', {
      opportunityId: oppId,
      probabilityId: probId
    });
  }

  updateOpportunityBidtype(oppId, bidId){
    return this.http.post('/api/opportunity/editBidtype', {
      opportunityId : oppId,
      bidtypeId :bidId
    });
  }

  updateOpportunitySource(oppId, sourceId){
    return this.http.post('/api/opportunity/editSourcetype',{
      opportunityId : oppId,
      sourcetypeId : sourceId
    });
  }

  updateOpportunitySalesStatus(oppId,salesStatusId){
    return this.http.post('/api/opportunity/editSalesStatustype',{
      opportunityId : oppId,
      status_id : salesStatusId,
      currentDate: moment().format('YYYY-MM-DD')
    });
  }

  updateOpportunityPreSalesStatus(oppId, preSalesStatusId){
    return this.http.post('/api/opportunity/editPreSalesStatustype',{
      opportunityId : oppId,
      status_id : preSalesStatusId
    });
  }

  updateOpportunitySalesOwner=async(opportunityId,oid)=>{
      var is_change=await this.isActivityApproval(opportunityId)
      if(is_change=="A")
      {
          this.snackBar.open("Activity approval In-Progress! Cannot change Sales Owner","Dismiss",{duration: this.mediumInterval})
          return Promise.resolve("F")
      }
      else
      {

        return new Promise((resolve, reject) => {
          this.http.post("/api/opportunity/editSalesOwner", { opportunityId,oid }).subscribe(res => {
            return resolve(res)
          },
            err => {
              return reject(err)
            })
        })
        
      }
  }

  updateOpportunityPresalesSPOC(oppId, salesOwnerOid){
    return this.http.post('/api/opportunity/editPreSalesSPOC',{
      opportunityId : oppId,
       oid : salesOwnerOid
    });
  }

  updateOpportunityName(oppId, oppName){
    return this.http.post('/api/opportunity/editOpportunityName',{
      opportunityId : oppId,
       oppName : oppName
    });
  }
  updateOpportunityValue(oppId, oppVal){
    return this.http.post('/api/opportunity/editOpportunityValue',{
      opportunityId : oppId,
      oppVal : oppVal
    });
  }
  updateOpportunityStartDate(oppId, startDate){
    return this.http.post('/api/opportunity/editOpportunityStartDate',{
      opportunityId : oppId,
      startDate : startDate
    });
  }
  updateOpportunityEndDate(oppId, endDate){
    return this.http.post('/api/opportunity/editOpportunityEndDate',{
      opportunityId : oppId,
       endDate : endDate
    });
  }

  updateSalesOwner(oppId,oid){
    console.log(oid);
    return this.http.post('/api/opportunity/editSalesOwnerNameInOpp', {
      oppId : oppId,
      oid: oid
    })
  }

  updatePipeline(oppId,pipeline_id,pipeline_name){
    return new Promise((resolve, reject) => {
      this.http.post("/api/opportunity/updatePipeline", {oppId:oppId,pipeline_id:pipeline_id,pipeline_name:pipeline_name }).subscribe(res => {
        return resolve(res);
      },
        err => {
          return reject(err);
        });
    });
  }
  updateOpportunityRFPReleaseDate(oppId, RFPReleaseDate){
    return this.http.post('/api/opportunity/editOpportunityRFPReleaseDate',{
      opportunityId : oppId,
      RFPReleaseDate : RFPReleaseDate
    });
  }
  updateOpportunityProposalSubDate(oppId, proposalSubDate){
    return this.http.post('/api/opportunity/proposalSubDate',{
      opportunityId : oppId,
      proposalSubDate : proposalSubDate
    });
  }
  getSalesGovernanceMasterData(){
    return this.http.post("/api/salesMaster/getSalesGovernanceTypes",{
    })
  }
  updateSalesGovernanceType(activityId,governanceTypeId){
    return this.http.post("/api/opportunity/editGovernanceType",{
      activityId : activityId,
      governanceId : governanceTypeId
    })
  }
  updateActivityAssignedTo(activityId,assignedToOid){
    return this.http.post("/api/opportunity/editActivityAssignedTo",{
      activityId : activityId,
      oid : assignedToOid
    })
  }
  updateServiceType(opportunityId,serviceTypeId){
    return this.http.post("/api/opportunity/editServiceType",{
      opportunityId : opportunityId,
      serviceTypeId : serviceTypeId
    })
    
  }
  updateActivityDueDate(activityId,date){
    return this.http.post("/api/opportunity/editActivityDueDate",{
      activityId : activityId,
      date : date
    })
  }
  updateActivityStartDate(activityId,date){
    return this.http.post("/api/opportunity/editActivityStartDate",{
      activityId : activityId,
      date : date
    })
  }
  updateActivityStatus(activityId,statusId){
    return this.http.post("/api/opportunity/editActivityStatus",{
      activityId : activityId,
      statusId : statusId
    })
  }

  updatePhaseDate(phaseId,startDate,endDate){
    return this.http.post("/api/opportunity/updatePhase",{
      id : phaseId,
      fields : {
        "start_date" : startDate,
        "end_date" : endDate
      }
    })
  }

  activityDateChangeHandler = (activityParams) => {

    return new Promise((resolve, reject) => {
      this.http
        .post('api/opportunity/activityDateChangeHandler', { activityParams: activityParams })
        .subscribe(
          (res) => {
            // this.utilityService.showMessage("CostCode unblocked successfully", "Dismiss");
            return resolve(res);
          },
          (err) => {
            console.log(err);
            // this.utilityService.showMessage("CostCode cannot be unblocked. Please try again", "Dismiss");
            return reject(err);
          }
        );
    });    
  }

  getSalesGovernanceMasterDataPromise(){
    return new Promise((resolve, reject) => {
      this.http.post("/api/salesMaster/getSalesGovernanceTypes", {}).subscribe(res => {
        return resolve(res);
      },
        err => {
          return reject(err);
        });
    });
  }

  updateActivityEffortPercent(phase_id, activityId,effortPercent, startDate){
    return this.http.post("/api/activity/updateActivityEffortPercent",{
      phaseId: phase_id,
      activityId : activityId,
      effortPercent : effortPercent,
      activityStartDate: startDate
    })
  }

  insertnotification(approver, activityid) {
    return new Promise((resolve, reject) => {
      this.http.post("/api/opportunity/insertTNotification", { approver, activityid }).subscribe(res => {
        return resolve(res);
      },
        err => {
          return reject(err);
        });
    });
  }

  updateChannelId(oppid, channelid) {
    return new Promise((resolve, reject) => {
      this.http.post("/api/opportunity/updateChannelId", { oppid, channelid }).subscribe(res => {
        return resolve(res);
      },
        err => {
          return reject(err);
        });
    });
  }

  // updatePlannedPresalesHours(opp_id,planned_presales_hours){
  //   return this.http.post("api/opportunity/updatePlannedPresalesHrsForOpp",{
  //     opp_id: opp_id,
  //     planned_presales_hours: planned_presales_hours
  // })
  // }

  updatePlannedHours(activity_id,planned_hours){
    return this.http.post("api/activity/updatePlannedHrsForOppAct",{
        activity_id: activity_id,
        planned_hours: planned_hours
    })
  }

  // updateActualHours(activity_id,actual_hours){
  //   return this.http.post("api/activity/updateActualHrsForOppAct",{
  //       activity_id: activity_id,
  //       actual_hours: actual_hours
  //   })
  // }

  getCostCode(region_id){
    return new Promise((resolve, reject) => {
      this.http.post("/api/opportunity/getCostCodefromPandL", { region_id }).subscribe(res => {
        return resolve(res);
      },
        err => {
          return reject(err);
        });
    });
  }

  getLocationList(){
    return new Promise((resolve,reject)=>{
      this.http.post('/api/tsPrimary/getLocationList', {}).subscribe(res=>{
        console.log("Location",res)
        return resolve(res);
        
      },(err)=>{
        return reject(err);
      });
    })
  }

  updatePhasePlannedHours(t_phase_id,planned_hours){
    return this.http.post("api/activity/updatephasePlannedHrs",{
      t_phase_id: t_phase_id,
      planned_hours: planned_hours
  })
  }
  IsPlannedPresalesHrsEditable() {
    return new Promise((resolve, reject) => {
      this.http.post("/api/opportunity/IsPlannedPresalesHrsEditable", {}).subscribe(res => {
        return resolve(res)
      },
        err => {
          return reject(err)
        })
    })
  }

  updateLocationforAct(office_id,location_id,act_id){
    return this.http.post("api/activity/updatelocation",{
       office_id: office_id,
       location_id: location_id ,
       act_id: act_id
    })
  }

  getAssociateId(employee_oid){
    return new Promise((resolve, reject) => {
      this.http.post("/api/activity/getAssociateId", {employee_oid}).subscribe(res => {
        return resolve(res);
      },
        err => {
          return reject(err);
        });
    });
  }

  rejectActivity(activity_id) {
    return new Promise((resolve, reject) => {
      this.http.post("/api/activity/rejectActivity", { activity_id }).subscribe(res => {
        return resolve(res);
      },
        err => {
          return reject(err);
        });
    });
  }

  checkCEOapproval(oppId) {
    return this.http.post('/api/opportunity/checkCEOapproval', {
      oppid: oppId,
    })
  };

  makeActivityStart(activityId,oid, workflow_id){
    return this.http.post('/api/activity/makeActivityStart', {
      activityId: activityId,
      oid: oid,
      workflow_id: workflow_id
    })
     
  }

  checkPandLQualification(oppId) {
    return this.http.post('/api/opportunity/checkPandLQualification', {
      oppid: oppId,
    })
  };

  retriggerActivity(activity_id) {
    return new Promise((resolve, reject) => {
      this.http.post("/api/activity/retriggerActivity", { activity_id }).subscribe(res => {
        return resolve(res);
      },
        err => {
          return reject(err);
        });
    });
  }

  updateOpportunityStatus(oppId, oppStatus) {
    console.log(oppId, oppStatus);
    return this.http.post('/api/opportunity/updateOpportunityStatus', {
      opportunity_id: oppId,
      status_id: oppStatus
    });
  };

  concurredActivity(activity_id) {
    return new Promise((resolve, reject) => {
      this.http.post("/api/activity/concurredActivity", { activity_id }).subscribe(res => {
        return resolve(res);
      },
        err => {
          return reject(err);
        });
    });
  }

  get_PandL_CEO(oppId){
    return new Promise((resolve, reject) => {
      this.http.post("/api/activity/get_PandL_CEO", {oppId}).subscribe(res => {
        return resolve(res);
      },
        err => {
          return reject(err);
        });
    });
  }

  getVHandPSactivityId(oppId){
    return new Promise((resolve, reject) => {
      this.http.post("/api/activity/getVHandPSactivityId", {oppId}).subscribe(res => {
        return resolve(res);
      },
        err => {
          return reject(err);
        });
    });
  }

  getPandLQualificationId(oppId){
    return new Promise((resolve, reject) => {
      this.http.post("/api/activity/getPandLQualificationId", {oppId}).subscribe(res => {
        return resolve(res);
      },
        err => {
          return reject(err);
        });
    });
  }

  updatePlannedPresalesHours(opportunityId,hours){
    return this.http.post("/api/opportunity/editPlannedPresalesHours",{
      opportunityId : opportunityId,
      hours : hours
    })
  }

  getPSactivityId(oppId){
    return new Promise((resolve, reject) => {
      this.http.post("/api/activity/getPSactivityId", {oppId}).subscribe(res => {
        return resolve(res);
      },
        err => {
          return reject(err);
        });
    });
  }

  updateBidInfo(bidInfo,oppId){
    return new Promise((resolve, reject) => {
      this.http.post("/api/opportunity/updateBidInfo", {bid_info:bidInfo,opp_id:oppId}).subscribe(res => {
        return resolve(res)
      },
        err => {
          return reject(err)
        })
    })
  }

  updateBidForm(formId,oppId){
    return new Promise((resolve, reject) => {
      this.http.post("/api/opportunity/updateBidForm", {form_id:formId,opp_id:oppId}).subscribe(res => {
        return resolve(res)
      },
        err => {
          return reject(err)
        })
    })
  }

  getQualifierForm(oppId){
    return new Promise((resolve, reject) => {
      this.http.post("/api/opportunity/getQualifierForm", {opp_id:oppId}).subscribe(res => {
        return resolve(res)
      },
        err => {
          return reject(err)
        })
    })
  }

  getInternalPresalesReviewForm(oppId){
    return new Promise((resolve, reject) => {
      this.http.post("/api/opportunity/getInternalPresalesReviewForm", {opp_id:oppId}).subscribe(res => {
        return resolve(res)
      },
        err => {
          return reject(err)
        })
    })
  }

  approvePresalesPlannedhours(oppId,comments){
    return new Promise((resolve, reject) => {
      this.http.post("/api/opportunity/approvePlannedPresalesHours", {oppId,comments}).subscribe(res => {
        return resolve(res);
      },
        err => {
          return reject(err);
        });
    });
  }

  rejectPresalesPlannedhours(oppId,comments){
    return new Promise((resolve, reject) => {
      this.http.post("/api/opportunity/rejectPlannedPresalesHours", {oppId,comments}).subscribe(res => {
        return resolve(res);
      },
        err => {
          return reject(err);
        });
    });
  }

  getApproversforPresalesHrs(oppId){
    return this.http.post("/api/opportunity/getApproverforPresalesHrs",{
        oppId: oppId
    })
  }

  getChannelType(){
    return this.http.get("/api/opportunity/getChannelType")
  }

  getChannels(opportunityId){
    return this.http.post("/api/opportunity/getChannelList",{
      opportunityId: opportunityId
    })
  }

  deleteChannel(opportunityId, notif_id)
  {
    return this.http.post("/api/opportunity/deleteChannelnotif",{
      opportunityId: opportunityId,
      notif_id: notif_id
    })
  }

  updateChannel(opportunityId,notif_id,common_val,val)
  { 
    return new Promise((resolve, reject) => {
      this.http.post("/api/opportunity/updateChannel", {opportunityId,notif_id,common_val,val}).subscribe(res => {
        return resolve(res);
      },
        err => {
          return reject(err);
        });
    });
  } 

  saveChannel(oppId, notif_type_id, channel_id){
    
    return new Promise((resolve, reject) => {
      this.http.post("/api/opportunity/saveChannel", {oppId, notif_type_id, channel_id}).subscribe(res => {
        return resolve(res);
      },
        err => {
          return reject(err);
        });
    });
  }

  /**
   * @description Gets opportunity Status based on roles
   * @returns {JSON}
   */
  getStatusObjectEntries(){
    let accessList = _.where(this._roleService.roles, { application_id: 36, object_id: 71 });
   
    if (accessList.length > 0)
      return JSON.parse(accessList[0].object_entries);

    else
      return null;
  }
  updateReviewForm(formId,oppId){
    return new Promise((resolve, reject) => {
      this.http.post("/api/opportunity/updateReviewForm", {form_id:formId,opp_id:oppId}).subscribe(res => {
        return resolve(res)
        
      },
        err => {
          return reject(err)
        })
    })
  }

  updateInternalFeedbackScore(oppId,score){
    return new Promise((resolve,reject) => {
      this.http.post("/api/opportunity/updateInternalFeedbackScore",{oppId:oppId,score:score}).subscribe(res =>{
        return resolve(res)
      },
      err =>{
        return reject(err)
      })
    })
  }
  /**
   * @description Gets sales Governance Types for win loss report
   * @returns 
   */
  getwinLossReportMasterDataPromise(){
    return new Promise((resolve, reject) => {
      this.http.post("/api/salesMaster/getSalesGovernanceTypes", {}).subscribe(res => {
        return resolve(res);
      },
        err => {
          return reject(err);
        });
    });
  }
  /**
   * 
   * @param tPhaseId 
   * @description Deletes opportunity phases (Only for admin access)
   * @returns 
   */
  deleteOpportunityPhase(tPhaseId){
    return new Promise((resolve, reject) =>{
      this.http.post("/api/opportunity/deleteOpportunityPhase", {t_phase_id : tPhaseId}).subscribe(res => {
        return resolve(res);
      },
        err => {
          return reject(err);
        });
    });
  }
  /**
   * 
   * @param comment 
   * @param oppId
   * @description Insert comment for planned presales hours in overview screen 
   * @returns 
   */
  insertCommentForPlannedpresalesHrs(comment,oppId)
  {
    return this.http.post("/api/opportunity/insertCommentForPresalesHours", {
      comment: comment,
      oppId: oppId
    });
  }

 /**
  * 
  * @param api 
  * @param oid 
  * @param actid 
  * @description Gets api from table and approves the activities
  * @returns 
  */
  activityApproval=(api,oid,actid)=>{
    return new Promise((resolve,reject)=>{
      this.http.post(api,{actid: actid,oid: oid,comments:""}).subscribe(res=>{
        return resolve(res);
      },(err)=>{
        return reject(err);
      })
    })
  }
  /**
   * 
   * @param actId 
   * @param oid 
   * @description starts the activity
   * @returns 
   */
  triggerWorkFlow=(actId,oid)=>{
    return new Promise((resolve,reject)=>{
      this.http.post("/api/activity/makeActivityStart",{activityId: actId,oid: oid,workflow_id:null}).subscribe(res=>{
        return resolve(res);
      },(err)=>{
        return reject(err);
      })
    })
  }
 /**
  * 
  * @param oppId 
  * @description Check whether opportunity is in approval for Tender
  * @returns 
  */
  isActivityApproval=(oppId)=>{
    return new Promise((resolve, reject) => {
      this.http.post("/api/opportunity/isActivityAppStarted", { oppId }).subscribe(res => {
        return resolve(res)
      },
        err => {
          return reject(err)
        })
    })
    
  }
  /**
   * 
   * @param oppId 
   * @param salesOwnerOid 
   * @description Inline edit of Marketing sales spoc in overview screen
   * @returns 
   */
  updateOpportunitymarketingSPOC(oppId, salesOwnerOid){
    return this.http.post('/api/opportunity/editMarketingSPOC',{
      opportunityId : oppId,
       oid : salesOwnerOid
    });
  }
  /**
   * 
   * @param oppId 
   * @param salesOwnerOid 
   * @description Inline edit of Inside sales spoc in overview screen
   * @returns 
   */
  updateOpportunityinsidesalesSPOC(oppId, salesOwnerOid){
    return this.http.post('/api/opportunity/editInsideSalesSPOC',{
      opportunityId : oppId,
       oid : salesOwnerOid
    });
  }

  /**
   * 
   * @param formId 
   * @param oppId 
   * @description Updates Win Loss form
   * @returns 
   */
  updateWinLossForm(formId,oppId){
    return new Promise((resolve, reject) => {
      this.http.post("/api/opportunity/updateWinLossForm", {form_id:formId,opp_id:oppId}).subscribe(res => {
        return resolve(res)
      },
        err => {
          return reject(err)
        })
    })
  }


  getProductCategory=()=>{
    return new Promise((resolve,reject)=>{
        this.http.post("/api/salesMaster/getProductCategory",{}).subscribe((res)=>{
          return resolve(res)
        },(err)=>{
          return reject(err)
        })
    })
  }

  getModule=()=>{
    return new Promise((resolve,reject)=>{
      this.http.post("/api/salesMaster/getModules",{}).subscribe((res)=>{
        return resolve(res)
      },(err)=>{
        return reject(err)
      })
  })
  }

   /**
   * 
   * @param userData 
   * @description Internal stakeholders opportunity
   * @returns 
   */
    getDetailedConsultantList(userData) {
    
        return this.http.post("/api/opportunity/getDetailedConsultantList", {
          userData : userData
        }) 
          
  }

  getOpportunityStakeholderMaster(stakeholderType){
    return new Promise((resolve, reject) => {
      this.http.post("api/opportunity/getOpportunityStakeholderMaster",{stakeholderType:stakeholderType}).subscribe(res => {
        return resolve(res);
      },
        err => {
          return reject(err)
        })
    })
  }

  removeMember(stakeHolderId,oppId,appId){
    return new Promise((resolve, reject) => {
      this.http.post("/api/opportunity/removeMember",{
        stakeholderId:stakeHolderId,
        appRefId:oppId,
        appId:appId
      }).subscribe(res => {
        return resolve(res)
      },
      err => {
        return reject(err)
      })
    })
  }

  
  getOpportunityTeamDetails(oppId){
    return new Promise((resolve, reject) => {
      this.http.post("/api/opportunity/getOpportunityTeamDetails",{opportunityId: oppId}).subscribe(res => {
        return resolve(res)
      },
      err => {
        return reject(err)
      })
    })
  }

  addTeamMember(oppId,member){
    return new Promise((resolve, reject) => {
      this.http.post("/api/opportunity/addOpportunityTeamMember",{
        opportunityId: oppId,
        member:member
      }).subscribe(res => {
        return resolve(res)
      },
      err => {
        return reject(err)
      })
    })
  }

  getOpportunityName(oppId){
    return this.http.post("/api/opportunity/getOpportunityName",{oppId})
  }

  async getUserSuggestionsFromDB(text, vcoe) {
    try {

      return new Promise((resolve, reject) => {
        this.http
          .post("/api/opportunity/getUserSuggestionsFromDB", { searchText: text, vcoe_flag: vcoe })
          .subscribe(
            res => {
              return resolve(res);
            },
            err => {
              console.log(err);
              return reject(err);
            }
          );
      });
    } catch (err) {
      return Promise.reject();
    }
  }
  /**
   * 
   * @param oppId 
   * @param proposalsalesDate 
   * @description Update Proposal Date inline edit
   * @returns 
   */
  updateOpportunityProposalSalesDate(oppId, proposalsalesDate){
    return this.http.post('/api/opportunity/proposalSalesDate',{
      opportunityId : oppId,
      proposalSalesDate : proposalsalesDate
    });
  }

  getOpportunityForm=(opportunity_id)=>{
    return new Promise((resolve,reject)=>{
      this.http.post("/api/opportunity/getDynamicForm",{opportunity_id: opportunity_id}).subscribe((res)=>
      {
        return resolve(res);
      },(err)=>{
        return reject(err);
      })
    })
  }

  deleteDynamicForm=(form_id,oppId)=>{
    return new Promise((resolve,reject)=>{
      this.http.post("/api/opportunity/deleteDynamicForm",{form_id, oppId}).subscribe((res)=>{
        return resolve(res)
      },(err)=>{
        return reject(err)
      })
    })
  }

  /**
   * @description Gets access to add forms
   * @returns {JSON}
   */
   getAccesstoAddForm(){
    let accessList = _.where(this._roleService.roles, { application_id: 36, object_id: 75 });
   
    if (accessList.length > 0)
      return true;

    else
      return false;
  }

  /**
   * @description Gets access to delete forms
   * @returns {JSON}
   */
   getAccesstoDeleteForm(){
    let accessList = _.where(this._roleService.roles, { application_id: 36, object_id: 76 });
   
    if (accessList.length > 0)
      return true;

    else
      return false;
  }

  addUserForm=(form_name,form_id,opportunity_id)=>{
    return new Promise((resolve,reject)=>{
      this.http.post("/api/opportunity/addUserForm",{form_name,form_id,opportunity_id}).subscribe((res)=>{
        return resolve(res)
      },(err)=>{
        return reject(err)
      })
    })
  }

  /**
     * @description Gets shift List for opportunity
     * @returns 
     */
   getShiftList(){
    return new Promise((resolve,reject)=>{
      this.http.post("/api/salesMaster/getShiftList",{}).subscribe((res)=>{
        return resolve(res)
      },(err)=>{
        return reject(err)
      })
    })
  }
 /**
  * 
  * @param sales_unit 
  * @description Edit Opportunity Access
  * @returns 
  */
  checkUserforEditAccess(sales_unit) {

    let accessList = this._roleService.getUserRoleOrgCodes("Opportunities");
    if (accessList == 'ALL') return true;
    else
      return _.contains(accessList, sales_unit)

  }

  getProductHeirarchy = () =>{
    return new Promise((resolve,reject)=>{
      this.http.post("/api/salesMaster/getProductCategoryHierarchy",{}).subscribe((res)=>{
        return resolve(res)
      },(err)=>{
        return reject(err)
      })
    })
  }

    /**
   * @description Update Product Category
   */
     updateProductCategory(product_category, opportunity_id){
      return new Promise((resolve,reject)=>{
        this.http.post("/api/opportunity/updateProductCategoryInOpp",{product_category, opportunity_id}).subscribe((res)=>{
          return resolve(res)
        },(err)=>{
          return reject(err)
        })
      })
     }

    /**
    * @description get opportunity teams ID
    */
     getOpportunityTeamsId(opportunity_id){
      return new Promise((resolve,reject)=>{
        this.http.post("/api/opportunity/getOpportunitySharePointId",{opportunity_id}).subscribe((res)=>{
          return resolve(res)
        },(err)=>{
          return reject(err)
        })
      })
    }

    /**
     * 
     * @param opportunity_id 
     * @description Trigger Bid Qualification during status change
     * @returns 
     */
    updateBidQualificationApproval(opportunity_id){
      return new Promise((resolve,reject)=>{
        this.http.post("/api/opportunity/triggerBidQualificationApproval",{opportunity_id}).subscribe((res)=>{
          return resolve(res)
        },(err)=>{
          return reject(err)
        })
      })
    }

    /**
     * 
     * @param oppId 
     * @description Get Approvers for Bid Qualification
     * @returns 
     */
    getBidQualificationApprovers(oppId){
      return this.http.post("/api/opportunity/getBidQualificationApprovers",{
          opportunity_id: oppId
      })
    }

    /**
     * 
     * @param comment 
     * @param oppId 
     * @description Insert Comment for Bid Qualification
     * @returns 
     */
    insertCommentForBidQualification(comment,oppId)
    {
      return this.http.post("/api/opportunity/insertCommentForBidQualification", {
        comment: comment,
        oppId: oppId
      });
    }

    /**
     * 
     * @param oppId 
     * @param comments 
     * @description Approve Bid Qualification
     * @returns 
     */
    approveBidQualification(oppId,comments){
      return new Promise((resolve, reject) => {
        this.http.post("/api/opportunity/approveBidQualification", {oppId,comments}).subscribe(res => {
          return resolve(res);
        },
          err => {
            return reject(err);
          });
      });
    }

    /**
     * 
     * @param oppId 
     * @param comments 
     * @description Reject Bid Qualification
     * @returns 
     */
      rejectBidQualification(oppId,comments){
      return new Promise((resolve, reject) => {
        this.http.post("/api/opportunity/rejectBidQualification", {oppId,comments}).subscribe(res => {
          return resolve(res);
        },
          err => {
            return reject(err);
          });
      });
    }

    //Get View Heirarchy Component Label
    getProductViewHierarchyLabel(application_id, label_name)
    {
      return new Promise((resolve,reject)=>{
        this.http.post("/api/salesMaster/getProductViewHeirarchy",{application_id, label_name}).subscribe((res)=>{
          return resolve(res)
        },(err)=>{
          return reject(err)
        })
      })
    }


    displayLineOfBusinessAndModulesInCreateOpportunity(){
      let accessList = _.where(this._roleService.roles, { application_id: 36, object_id: 119 });
     
      if (accessList.length > 0)
        return true;
  
      else
        return false;
     }

  checkChildOpportunityCreationAccess(opp_id) {
    return this.http.post("/api/opportunity/checkChildOpportunityCreationAccess", {
      opportunityId: opp_id
    });
  }


  /**
   * 
   * @param filterConfig 
   * @description Gets Opportunity List
   * @returns 
   */
    getUDRFOpportunityList=(filterConfig, myOppFilter, orgCodes)=>{
      return this.http.post("/api/opportunity/getUDRFOpportunityList", {
        filterConfig: filterConfig,
        orgCodes: orgCodes,
        myOppFilter: myOppFilter
      });
    }

    getUDRFOpportunityCard =(filterConfig, myOppFilter,orgCodes)=>{
      return new Promise((resolve, reject) => {
        this.http.post("/api/opportunity/getUDRFOpportunityCard", {
          filterConfig: filterConfig,
          orgCodes: orgCodes ,
          myOppFilter: myOppFilter
        }).subscribe(res => {
          return resolve(res);
        }, err => {
          return reject(err);
        });
      });
    }
    

    getOpportunityStatusSales() {
      let statusValues= this.getStatusObjectEntries()
      return new Promise((resolve, reject) => {
        this.http.post('/api/salesMaster/getSalesStatusMaster',{statusValues}).subscribe(res => {
          return resolve(res);
        }, err => {
          return reject(err);
        });
      });
    }

    getProposalTypeMaster() {

      return new Promise((resolve, reject) => {
        this.http.post('/api/salesMaster/getProposalTypeMaster',{}).subscribe(res => {
          return resolve(res);
        }, err => {
          return reject(err);
        });
      });
    }

    updateProposalStatus=(opportunity_id, status_id)=>{
      return this.http.post("/api/opportunity/updateProposalStatus", {
        opportunity_id: opportunity_id,
        status_id: status_id
      })
    }

    /**
     * 
     * @param oppId 
     * @description Gets Win loss form id when form gets opened
     * @returns 
     */

    getWinLossForm(oppId){
      return new Promise((resolve, reject) => {
        this.http.post("/api/opportunity/getWinLossForm", {opp_id:oppId}).subscribe(res => {
          return resolve(res)
        },
          err => {
            return reject(err)
          })
      })
    }

    getReasonList() {
      return this.http.post("/api/salesMaster/getReasonList",{});
    }


    checkNdUpdateOpportunityStatus = async(salesStatusBeforeModified, status_id, opportunityData,isQuoteEnabled)=>{
      console.log(salesStatusBeforeModified, status_id, opportunityData)
      return new Promise(async(resolve, reject) => {
          let total_status =[]
          await this.getOpportunityStatusSalesGov(status_id).then((res:any)=>{
              total_status=res
          },(err)=>{
            console.log(err)
          })
          console.log(salesStatusBeforeModified, status_id,opportunityData)
          let changed_status  = _.where(total_status,{id: status_id})
          console.log(changed_status)
          let prev_changed_status = _.where(total_status,{id: salesStatusBeforeModified})
          console.log(prev_changed_status)
          let bid_qualification_approval_status =opportunityData['bid_qualification_approval_status']
          let documentConfig = JSON.parse(changed_status[0]['document_type_upload_required_config']);

          if(changed_status.length>0)
          {
            if(prev_changed_status.length>0)
            {
              if(prev_changed_status[0]['no_bid_status']==1)
              {
                  await this.salesStatusControl(opportunityData , prev_changed_status, changed_status).then((res)=>{
                      if(res['messType'])
                      {
                          //console.log("Status can be changed")
                      }
                      else
                      {
                        this._utilService.showMessage(res['mess'],"Dismiss")
                        bid_qualification_approval_status = res['bid_qualification_approval_status']
                        resolve({messType:"E",bid_qualification_approval_status: bid_qualification_approval_status})
                      }
                  })
              }
            }
            if (documentConfig['value']['is_doc_upload_mandatory']) {
              console.log(documentConfig);
                          
              let fileTypeDropDown = [];
              await this.getOpportunityDocumentTypes()
                .then((res) => {
                  if (res && res['data']) fileTypeDropDown = res['data'];
                  else this._utilService.showMessage("Document Types Not Found!", "Dismiss", 2000);
                })
                .catch((error) => {
                  console.error("Failed to retrieve document types:", error);
                });
            
              // const docUploadConfig = documentConfig['value']['doc_upload_config'];
              // Making PO Doc Non Mandatory if PO is not Issued 
              const docUploadConfig = documentConfig['value']['doc_upload_config'].filter(config => {
                if (opportunityData['issue_po'] == 'No' && config.document_type === 'po_value')
                  return false;
                return config.is_active;
              });

              const pendingDocs = [];
            
              const checkDocsPromises = docUploadConfig
              .filter(config => config.is_active) 
              .map(async (config) => {
                try {
                  const docThere = await this.checkIfDocThere('36' + opportunityData.opportunity_id, config.document_type);
                  if (docThere['data'].length === 0) {
                    pendingDocs.push(config.document_type);
                  }
                } catch (error) {
                  console.error(`Error checking document status for ${config.document_type}:`, error);
                }
              });
            
            await Promise.all(checkDocsPromises);
              for (const docType of pendingDocs) {
                const matchingDocument = fileTypeDropDown.find((type) => type.document_type === docType);
            
                if (!matchingDocument) {
                  this._toaster.showWarning("Mandatory!", `Document type not found in config!`, 3000);
                  continue;
                }
            
                this._toaster.showWarning(
                  "Mandatory!",
                  `${matchingDocument.document_label} Document Upload is Mandatory at this stage!`,
                  3000
                );
            
                const result = await this.openDocumentManager(
                  opportunityData.opportunity_id,
                  '36' + opportunityData.opportunity_id,
                  fileTypeDropDown,
                  docType
                );
            
                if (result?.messType === 'S') {
                  this._toaster.showSuccess(
                    `${matchingDocument.document_label} Document has been uploaded for this stage`,
                    "",
                    3000
                  );
                } else {
                  this._toaster.showWarning(
                    "Status Cannot be changed!",
                    `${matchingDocument.document_label} Document is Mandatory.`,
                    3000
                  );

                  resolve({messType:"E"}) 
                  break;
                }
              }
            }
            // if (changed_status[0]['document_type_upload_required'] != null) {
            //   let docThere = await this.checkIfDocThere('36' + opportunityData.opportunity_id, changed_status[0].document_type_upload_required)
            //   if (docThere['data'].length == 0) {
            //     let fileTypeDropDown = [];

            //     await this.getOpportunityDocumentTypes().then((res) => {
            //       if (res && res['data']) fileTypeDropDown = res['data'];
            //       else this._utilService.showMessage("Document Types Not Found!", "Dismiss", 2000)
            //     }).catch((error) => {
            //       console.error("Failed to retrieve document types:", error);
            //     });

            //     const matchingDocument = fileTypeDropDown.find(
            //       (type) => type.document_type === changed_status[0].document_type_upload_required
            //     );

            //     this._toaster.showWarning("Mandatory!", `${matchingDocument.document_label} Document Upload is Mandatory at this stage!`, 3000)
            //     await this.openDocumentManager(opportunityData.opportunity_id, '36' + opportunityData.opportunity_id, fileTypeDropDown, changed_status[0].document_type_upload_required).then(async (res) => {
            //       if (res['messType'] && res['messType'] == 'S')
            //       this._toaster.showSuccess(`${matchingDocument.document_label} Document has been uploaded for this stage`, "", 3000)
            //       else {
            //         this._toaster.showWarning("Document has not been uploaded for this stage", "", 3000)
            //         resolve({ messType: "E", bid_qualification_approval_status: bid_qualification_approval_status })
            //       }
            //     })
            //   }
            // }
            if(changed_status[0]['is_finance_check']==1)
            {
                console.log("Finance Check")
                await this.checkAccountFinancial(opportunityData['customer_id']).then((res :any)=>{
                  console.log(res)
                  if(res.messType=="S" && res.messData!=null){
                    let accFinFields = JSON.parse(changed_status[0].accountfinance_field_config)
                     accFinFields=_.filter( accFinFields,function(item:any){
                     return item.is_active==true 
                    })
                    let accFinData=res.messData[0]
                    console.log(accFinFields)
                    console.log(accFinData)
                    let mandFields=""
                    let editRestrict =false
                    if(res.messData.length>0){
                    for(let item of accFinFields){
                      console.log(accFinData[item.data_field])
                      if(accFinData[item.data_field]=="" || accFinData[item.data_field]==null || accFinData[item.data_field]==0 ||  accFinData[item.data_field]=="00:00:00 00:00:00")
                      {
                        mandFields+=" " +item.label+ " " 
                        editRestrict=true
                      }
                    }
                    if(editRestrict==true){
                      this._utilService.showMessage("To Update Status Kindly Fill Account Financial Fields " + mandFields,"Dismiss")
                      resolve({messType:"E"})  }
                      else{
                        resolve({messType:"S"})  
                      }
                  }
                    else{
                      console.log("Finance Check2")
                      //this._utilService.showMessage("","Dismiss")
                      resolve({messType:"S"})  

                    }
                  }
                  else{
                    console.log("Finance Check3")
                  this._utilService.showMessage("Financial details of Account not found","Dismiss")
                  resolve({messType:"E"})    
                  }
                   
                })
                
            }
            if(changed_status[0]['proposal_sub_date_flag']==1)
            {
                if(opportunityData['proposal_submission_date']=="0000-00-00 00:00:00" || opportunityData['proposal_submission_date']==null ||opportunityData['proposal_submission_date']==undefined || opportunityData['proposal_submission_date']=="-")
                {
                    this._utilService.showMessage("Kindly enter Submission to Customer Date! Before changing the status","Dismiss")
              
                    resolve({messType:"E",bid_qualification_approval_status:bid_qualification_approval_status})
                }
            }
            if(changed_status[0]['is_end_form_needed']==1 && (!isQuoteEnabled && isQuoteEnabled != undefined ))
            {  console.log("openwlf1") 
            console.log(changed_status[0]['is_end_form_needed'])
            console.log(isQuoteEnabled)
                await this.openWinLossForm(opportunityData, changed_status[0]['name']).then((res)=>{
                  if(res['messType'])
                  {
                    if(res!="" && res["result"]!="")
                      this._utilService.showMessage(res['result'],"Dismiss")
            
                  }
                  else
                  {
                    if(res!="" && res["result"]){

                      this._utilService.showMessage(res['result'],"Dismiss")
                      resolve({messType:"E",bid_qualification_approval_status:bid_qualification_approval_status})          
      
                    }
               
                  }
                })
            }
            if(changed_status[0]['shs_completed']==1)
            { console.log("openwlf13") 
                console.log("SHS completed")
                await this.checkNdmoveToSHSCompleted(opportunityData).then((res)=>{
                  //console.log(res)
                  if(res!="")
                    this._utilService.showMessage(res,"Dismiss")
                  resolve({messType:"E",bid_qualification_approval_status:bid_qualification_approval_status}) 
                })
                
            }

            resolve({messType:"S",bid_qualification_approval_status:bid_qualification_approval_status})          
          }
          else
          {
            this._utilService.showMessage("Status Change is restricted!","Dismiss")
            resolve({messType:"E",bid_qualification_approval_status:bid_qualification_approval_status})
          }
      })

    }

    async openDocumentManager(opportunityId: any, contextId: string, fileTypeDropDown: any[], type?: any): Promise<any> {


      const s3_config = await this.getCRMAttachmentsConfigs();
      let crmAttachmentConfig = s3_config['data']['aws']['attachment_plugin_config']; 
  
      const data = {
        destinationBucket: crmAttachmentConfig['destination_bucket'],
        routingKey: crmAttachmentConfig['routing_key'],
        contextId: contextId,
        allowEdit: true,
        myFilesDefaultFolder: [],
        documentType: type,
        fileTypeDropDown: fileTypeDropDown,
        mode: 'create',
        color: "#79BA44",
        applicationReferenceId: opportunityId,
        appId: '36',
      };
  
      const dialogConfig = {
        data: { data },
        disableClose: true,
        panelClass: 'custom-mat-dialog-panel',
      };
  
      const uploadBtnDialogRef = this.dialog.open(DocumentUploadComponent, dialogConfig);
    
      let res
      let updateDocRes;
  
      try {
        res = await uploadBtnDialogRef.afterClosed().toPromise();
  
        if (res?.refreshLanding) 
          updateDocRes = await this.addAttachmentType(res);
        
      } catch (error) {
        console.error('Error handling dialog result:', error);
      }
        
      return updateDocRes
    }

  async addAttachmentType(res: any): Promise<void> {
    const documentInfo = res.filesUploaded.map(file => ({
      _id: file._id,
      document_type_id: file.document_type_id,
      document_type: file.document_type,
      reviewersList: res.reviewersList,
      isApprovalRequired: res?.isApprovalRequired || false,
      applicationReferenceId: res.appReferenceID,
      appId: res.appID || '36',
      documentId: file?.document_id
    }));

    console.log(documentInfo);

    let updateStatus;
    try {
      updateStatus = await this.updateDocumentTypes(documentInfo).toPromise();
    } catch (error) {
      console.error('API Error:', error);
    }

    return updateStatus
  }

  async checkIfDocThere(a,b): Promise<void> {

    let updateStatus : any;
    try {
      updateStatus = await this.checkIfDocUploaded(a,b).toPromise();
    } catch (error) {
      console.error('API Error:', error);
    }

    return updateStatus
  }

    

    async salesStatusControl(opportunityData , previousStatus, changed_status){


      return new Promise(async (resolve, reject) => {

          await this.getQualifierForm(opportunityData['opportunity_id']).then(async(qualifierRes : any) => {

            let result = {
              messType:false,
              mess:'',
              bid_qualification_approval_status:opportunityData['bid_qualification_approval_status']
            };
          
            switch(true){

              // No form found, sales status can be changed
              case (qualifierRes.messType == "E"):
              {
                result.messType = true;
                break
              }
            
              case(changed_status[0]['no_bid_status']==1):
              {
                result.messType = true;
                break
              }
              
              //Form found but not filled
              case (qualifierRes.messType == "S" && previousStatus[0]['no_bid_status'] == 1 && opportunityData['is_bid']==null):
              {
                result.messType = false;
                result.mess = "Sales status cannot be changed until qualifier form is filled!"
                break
              }
              
              
              case (qualifierRes.messType == "S" && opportunityData['is_bid']==null):
              {
                result.messType = false;
                result.mess = "Sales status cannot be changed until qualifier form is filled!";
                break
              }
  
              case (qualifierRes.messType == "S" && opportunityData['is_bid']==0 ):
              {
                  if(opportunityData['bid_qualification_approval_status'] == 1)
                  {
                    await this.displayAddActivityApprovalMessage(opportunityData['opportunity_id']).then((res: any)=>{
                      result.messType = false;
                      result.mess = res.message;
                      result.bid_qualification_approval_status = res.status
                      
                    })
                    
                  }
                  else if(opportunityData['bid_qualification_approval_status'] == 2)
                  {
                    result.messType = false;
                    result.mess = "This opportunity is under no bid approval! Once approval is done you will be able to change status";
                  }
                  else if(opportunityData['bid_qualification_approval_status'] == 3)
                  {
                    result.messType = true;
                  }
                  else
                  {

                    result.messType = false;
                    result.mess = "Sales status should be closed cancelled or created for no bid!";
                  }
              }
                break
              
              default:
                result.messType = true;

            }
  
            resolve(result)

          })
          .catch((err) => {
            reject(err)

          })    
      })


    }
  
    async displayAddActivityApprovalMessage(opportunity_id)
    {
      return new Promise((resolve,reject)=>{
        this._utilService.openConfirmationSweetAlertWithCustom("In No Bid Opportunity, For changing the status will trigger approval?","Once you confirm approval will be triggered!").
  
        then(async (res) => {
  
          if (res) {
  
            this.updateBidQualificationApproval(opportunity_id).then((res: any) => {
  
  
  
              if(res=="F")
              {
                resolve({message:"We cannot intiate approval for changing No Bid Opportunity",status:1})
              }
              else if(res=="NAF")
              {
                ////console.log("No approvers found")
                resolve({message:"We cannot intiate approval for changing No Bid Opportunity",status:1})
              }
  
              else
              {
  
                resolve({message:"Approval triggered successfully!",status:2})
  
              }
            },(err)=>{
              resolve({message:"Failed to trigger approval!",status:1})
            })
  
          }
  
          else
  
          {
  
            resolve({message:"Sales status should be closed cancelled or created for no bid!",status:1});
  
          }
  
        });
      })
  
    }

    async checkNdmoveToSHSCompleted(opportunitiesData) 
    {
      return new Promise((resolve, reject)=>{

          this._utilService.openConfirmationSweetAlertWithCustom("","Completing SHS will reflect in Actual OBV. Do you want to Proceed?").then((deleteConfirm) => {
            if (deleteConfirm) 
            {
        
                this.moveToSHSCompleted(opportunitiesData['opportunity_id']).subscribe(res => {
                  console.log(res);
      
          
                  resolve("SHS Completed Successfully!");
                }, err => {
                  
                  resolve("Failed To Complete SHS!");
                })
            }
            else
            {
                resolve("")
            }
          })
    
      }) 
    }

    async openWinLossForm(opportunityData, updated_status_name)
    {
        return new Promise(async(resolve, reject)=>{
                let isFormAval : boolean
                let form_details 
                if(opportunityData['win_loss_form']!=null && opportunityData['win_loss_form'] !='')
                {
                    form_details = opportunityData['win_loss_form']
                    isFormAval = true
                }
                else
                {
                    await this.getWinLossForm(opportunityData['opportunity_id']).then(async(res:any)=>
                    {
                        console.log(res)
                        if(res.messType=="E")
                        {
                            console.log("Form ID not found")
                            isFormAval = false
                        }
                        else if(res.messType=="S")
                        {
                            form_details = res.data.form_id
                            isFormAval = true
                            this.updateWinLossForm(form_details,opportunityData['opportunity_id']);
                        }
                    })
                }
          
                if(!isFormAval){
                
                    await this.getReasonList().subscribe(async(res) => {
                      let reason_list = res;
                      const { ReasonModalComponent } = await import ( 'src/app/modules/shared-lazy-loaded-components/reason-modal/reason-modal.component' ) 
                      const openReasonDialog = this.dialog.open ( ReasonModalComponent , 
                        { 
                          height : '100%' ,
                          width : '75%' , 
                          position : {
                              right : '0px' 
                            }, 
                          data : 
                              { 
                                title:`Share your opinion`,
                                reason_title:`Reason for changing  ${updated_status_name} status`,
                                button_name: "Submit Reason",
                                reason_list:reason_list
                              } 
                            }
                      )
                      
                      openReasonDialog.afterClosed().subscribe(async (res:any)=>{
                            console.log(res)
                            if(res!=undefined)
                            {
                                if(res.messType=='S')
                                {
                                    resolve({messType:true,result:"Reason added successfully!"})
                                }
                            }
                            else
                            {
                                resolve({messType:false,result:"Without reason status cannot be changed!"})
                            }
            
                            
                      }) 
        
                    }, err => {
                      console.error(err);
                      resolve({messType:true,result:""})
                    })

                    
                                                     
                }
                else
                {
          
                    const { CustomFormModalComponent } = await import ( 'src/app/modules/shared-lazy-loaded-components/custom-form-modal/custom-form-modal.component' ) 
                    const openCustomFormModalComponent = this.dialog.open ( CustomFormModalComponent , 
                      {height : '100%' ,
                      width : '75%' , 
                      position : {
                         right : '0px' 
                       }, 
                      data : 
                          { 
                            formId : form_details, 
                            isEdit :true, 
                            entryForId : (opportunityData['opportunity_id']).toString(),
                            formName: "Win-Loss Form",
                            inputData: {
                              fieldMappedInput: {
                                [form_details]: updated_status_name.toLowerCase().includes('won') ? 1 : 2
                              }
                            }
                          } 
                        }
                    )
                    
                    openCustomFormModalComponent.afterClosed()
                        .subscribe(async (res:any)=>{
                          console.log(res)
                          if(res!=undefined)
                          {
                              if(res.messType=='S')
                              {
                                this.updateWinLossForm(form_details,opportunityData['opportunity_id']);
                                resolve({messType:true,result:"Form Filled Successfully!"})
                                  
                              } 
                              else{
                                
                               resolve({messType:false,result:"Win Loss Form not submitted"})
                              }                         
                          }
                          resolve({messType:false,result:"Error updating form"})

            
                    })
                    
                }
                

        })
            
           
        
  }

  updateOpportunityReason =(opportunity_id, reason)=>{
    return new Promise((resolve, reject) => {
      this.http.post('/api/salesMaster/updateOpportunityReason',{opportunity_id, reason}).subscribe(res => {
        return resolve(res)
      }, err => {
        return reject(err)
      })
    })
  }

  getLabelForOpportunity = (application_id, id) =>{

    let val = _.where(this._roleService.label,{application_id: application_id, id: id}) 
    return val.length>0?val[0]['label_name']:""
  }

  
  checkForAdminAccess(){
    return new Promise((resolve, reject) => {
      this.http.post("/api/opportunity/checkForAdminAccess",{}).subscribe(res => {
        return resolve(res)
      },
        err => {
          return reject(err)
        })
    })
  }

  getUDRFOpportunityDownloadReport=(filterConfig, myOppFilter, orgCodes)=>{
    return this.http.post("/api/opportunity/getUDRFOpportunityDownloadReport", {
      filterConfig: filterConfig,
      orgCodes: orgCodes,
      myOppFilter: myOppFilter
    })
  }

  contactType() {
    return new Promise((resolve, reject) => {
      this.http.post("/api/salesMaster/getContactType", {}).subscribe(res => {
        return resolve(res)
      },
        err => {
          return reject(err)
        })
    })
  }
  responseType() {
    return new Promise((resolve, reject) => {
      this.http.post("/api/salesMaster/getResponseMaster", {}).subscribe(res => {
        return resolve(res)
      },
        err => {
          return reject(err)
        })
    })
  }

  getLeadsGovernanceType=(applicationId)=>{
    return new Promise((resolve, reject)=>{
      this.http.post('/api/activity/getGovernanceActivityType', {applicationId:applicationId}).subscribe(res=>{
        resolve(res)
      },(err)=>{
        reject(err)
      })
    })
  }

  editCallLog(activity_id, callLogFormDetails) {
    return new Promise((resolve, reject) => {
      this.http.post("/api/activity/editCallLog", { activity_id, callLogFormDetails }).subscribe(res => {
        return resolve(res)
      },
        err => {
          return reject(err)
        })
    })
  }

  createCallLog(callLogForm) {
    return this.http.post('/api/activity/createCallLog', {
      callLogFormDetails: callLogForm
    })
  }

  meetingTypeList() {
    return new Promise((resolve, reject) => {
      this.http.post("/api/salesMaster/getSalesActivityLocationType", {}).subscribe(res => {
        return resolve(res)
      },
        err => {
          return reject(err)
        })
    })
  }

  locationList() {
    return new Promise((resolve, reject) => {
      this.http.post("/api/salesMaster/getActivityLocations", {}).subscribe(res => {
        return resolve(res)
      },
        err => {
          return reject(err)
        })
    })
  }

  peopleList() {
    return new Promise((resolve, reject) => {
      this.http.post("/api/salesMaster/peopleList", {}).subscribe(res => {
        return resolve(res)
      },
        err => {
          return reject(err)
        })
    })
  }

  createMeeting(meetingFormDetails) {
    return this.http.post('/api/activity/createMeeting', {
      meetingFormDetails: meetingFormDetails
    })
  }

  createMail(mailForm) {
    return this.http.post('/api/activity/createMail', {
      mailFormDetails: mailForm
    })
  }

  editMeeting(activity_id, meetingFormDetails) {
    return new Promise((resolve, reject) => {
      this.http.post("/api/activity/editMeeting", { activity_id: activity_id, meetingFormDetails }).subscribe(res => {
        return resolve(res)
      },
        err => {
          return reject(err)
        })
    })
  }

  editMail(activity_id, mailFormDetails) {
    return new Promise((resolve, reject) => {
      this.http.post("/api/activity/editMail", { activity_id, mailFormDetails }).subscribe(res => {
        return resolve(res)
      },
        err => {
          return reject(err)
        })
    })
  }
   
  getAttendee=(opportunity_id)=>{
    return new Promise((resolve,reject)=>{
      this.http.post("/api/opportunity/getAttendee",{opportunity_id: opportunity_id}).subscribe((res)=>
      {
        return resolve(res);
      },(err)=>{
        return reject(err);
      })
    })
  }

  updateSalesOrgForOpportunity=(opportunity_id, sales_org_id, sales_org_name)=>{
    return new Promise((resolve, reject)=>{
      this.http.post("/api/opportunity/updateSalesOrg",{opportunity_id: opportunity_id, sales_unit_id: sales_org_id, sales_unit_name: sales_org_name}).subscribe((res)=>
      {
        return resolve(res);
      },(err)=>{
        return reject(err);
      })
    })
  }

  getUDRFBodyColumns = () => {
    return this.http.post("/api/opportunity/getUDRFBodyColumns",{})
  }
  
  viewButton(){
    let accessList = _.where(this._roleService.roles, { application_id: 36, object_id: 29342 });
   
    if (accessList.length > 0)
      return true;

    else
      return false;
  }
  
  deactivateOpportunity = (opportunityId) => {
    return new Promise((resolve, reject) => {
      this.http.post("/api/opportunity/deactivateOpportunity",{opportunityId:opportunityId}).subscribe(res => {
        return resolve(res)
      },
        err => {
          return reject(err)
        })
    })
  }

  deactivateAccess = () => {
    let accessList = _.where(this._roleService.roles, { application_id: 36, object_id: 29344});  
    console.log("accessList",accessList)
    if (accessList.length > 0)
      return true;
    else
      return false;  
  }

  isAdditionalField = () => {
    let accessList = _.where(this._roleService.roles, { application_id: 36, object_id: 29341});  
    if (accessList.length > 0)
      return true;
    else
      return false;
  }

  getFormFieldCollection = () => {
    return this.http.post("/api/opportunity/getFormFieldCollection",{  
    })
  }

  getPMFormCustomizeConfigV = () => {
    return this.http.post("/api/pm/masterData/getPMFormCustomizeConfig",{  
    })
  }

  async isDisableEdit(opportunityDetail) {
    let isOpen = opportunityDetail?.isOpen && opportunityDetail.isOpen == 1 ? true : false;
    let isOpenOppRoleAvailable = true
    if (isOpenOppRoleAvailable) {
      if (opportunityDetail?.sales_status_id && parseInt(opportunityDetail.sales_status_id) == 17) {
        return true && !isOpen;
      } else {
        return false;
      }
    } else {
      return false;
    }
  }

  async reOpenAccess(opportunityDetail) {
    let isOpen = opportunityDetail?.isOpen && opportunityDetail.isOpen == 1 ? true : false;
    let isOpenOppRoleAvailable = await this.isOpenOpportunityAccess();
    if (isOpenOppRoleAvailable) {
      if (!isOpen && opportunityDetail?.sales_status_id && parseInt(opportunityDetail.sales_status_id) == 17) {
        return true;
      } else {
        return false;
      }
    } else {
      return false;
    }
  }

  async isOpenOpportunityAccess() {
    let accessList = _.where(this._roleService.roles, { application_id: 36, object_id: 29355 });
    if (accessList.length > 0)
      return true;
    else
      return false;
  }

  reOpenOpportunity = (opportunityId) => {
    return this.http.post("/api/opportunity/reOpenOpportunity",{opportunityId:opportunityId})
  }

  isFiscalYearCheck  = () => {
    let accessList = _.where(this._roleService.roles, { application_id: 36, object_id: 29347 });
   
    if (accessList.length > 0)
      return true;

    else
      return false;
   }
  
   getBandBData = (opportunityId) => {
    return this.http.post("/api/opportunity/getBandBdata",{opportunityId:opportunityId})
   }
  getStatusLogForOpp(id) {
    return this.http.post('/api/opportunity/getStatusLogForOpp', {
      opportunity_id: id,
    });
  }

  checkIfAccountExist=(accountId) => {
    return this.http.post("/api/accounts/checkIfAccountExist", {
      accountId: accountId
    });
  }

  checkIfMyOppToggleVisible(){
    return new Promise((resolve, reject) => {
      this.http.post("/api/opportunity/checkIfMyOppToggleVisible",{}).subscribe(res => {
        return resolve(res)
      },
        err => {
          return reject(err)
        })
    })
  }

  checkGeneralConfigUserLevel(config){
    return new Promise((resolve, reject) => {
      this.http.post("/api/opportunity/checkGeneralConfigUserLevel",{config: config}).subscribe(res => {
        return resolve(res)
      },
        err => {
          return reject(err)
        })
    })
  }

  updateGeneralConfigUserLevel(config, value){
    return new Promise((resolve, reject) => {
      this.http.post("/api/opportunity/updateGeneralConfigUserLevel",{config: config, value: value}).subscribe(res => {
        return resolve(res)
      },
        err => {
          return reject(err)
        })
    })
  }

  
  stagewiseFieldConfig = (object_id) => {
   return  new Promise((resolve, reject) => {
      this.http.post("/api/salesMaster/getApplicationObject",{object_id:object_id}).subscribe( (res:any) => {
        if(res.messType=='S' && res.messData.length>0){
         return resolve(res)
        }
        else{
          return resolve(res)
        }
       
      },
        err => {
          return reject(err)
        })
    })
  }

  getStagewiseFormFieldConfig = (id) => {
  
    return new Promise((resolve, reject) => {
      this.http.post("/api/opportunity/getStagewiseFormFieldConfig",{status_id:id}).subscribe( (res:any) => {
        if(res.messType=='S' && res.messData.length>0){
          return resolve(res)
         }
      
      },
        err => {
          return reject(err)
        })
    })
  }

  getOpportunityAuditLogBasedId= (opportunityId,filterData,startIndex) => {
    return this.http.post("/api/auditLog/getOpportunityAuditLogBasedId",{ 
       opportunityId:opportunityId, filterData:filterData,startIndex:startIndex
    })
  }

  checkOpportunity(opportunity_id,project_id){
    return new Promise((resolve, reject) => {
      this.http.post("api/opportunity/checkOpportunity",{opportunityId:opportunity_id,projectId:project_id}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }

  
   checkOpportunitySalesStatus(oppId,salesStatusId){
     console.log("CheckOPP")

    return new Promise((resolve, reject) => {
      this.http.post('/api/opportunity/checkSalesStatustype',{
        opportunityId : oppId,
        status_id : salesStatusId,
        currentDate: moment().format('YYYY-MM-DD')
      }).subscribe( (res:any) => {
        if(res['messType'] == 'E'){
          return resolve(res)
         }
         else{
          return resolve(res)
         }
      
      },
        err => {
          return reject(err)
        })
    })
  }

  updateOpportunityDeliveryStartDate(oppId, startDate){
    return this.http.post('/api/opportunity/editOpportunityDeliveryStartDate',{
      opportunityId : oppId,
      startDate : startDate
    });
  }
  updateOpportunityDeliveryEndDate(oppId, endDate){
    return this.http.post('/api/opportunity/editOpportunityDeliveryEndDate',{
      opportunityId : oppId,
       endDate : endDate
    });
  }
  
  async checkAccountFinancial(accountId) 
  {
    console.log("checkAccountFinancial")

    return new Promise((resolve, reject) => {
      this.http.post('/api/accounts/getAccountFinancialAndAgreement',{
        accountId : accountId
      }).subscribe(res => {
        return resolve(res)  })

      })
    }
  
  getCurrencyList = () => {
    return this.http.post("/api/opportunity/getCurrencyList",{   })
  }
  

  getAdminAuditLogBasedId= (opportunityId,filterData,startIndex) => {
    return this.http.post("/api/auditLog/getAdminAuditLogBasedId",{ filterData:filterData,startIndex:startIndex
    })
  }
  getApplicationList= () => {
    return new Promise((resolve, reject) => {
       this.http.post("/api/salesMaster/getApplicationList",{ 
    }).subscribe( (res:any) => {
        if(res['messType'] == 'S'){
          return resolve(res['messData'])
         }
         else{
          return resolve([])
         }
      
      },
        err => {
          return reject(err)
        })
      })
    }
  getOpportunityProjectDetails = (opportunity_id) => {
    return this.http.post("/api/opportunity/getOpportunityProjectDetails",{  
      opportunityId:opportunity_id
    })
  }

  getParentOpportunityMaster(accountId) {
    return this.http.post('/api/salesMaster/getParentOpportunityMaster',{
      account_id:accountId
    })
  }

  checkAdaptTemplate(oppId,salesStatusId){
    return this.http.post('/api/opportunity/checkAdaptTemplate',{
      opportunityId : oppId,
      status_id : salesStatusId,
      currentDate: moment().format('YYYY-MM-DD')
    });
  }
  adaptStatusTemplate(oppId,salesStatusId){
    return this.http.post('/api/opportunity/adaptActivityTemplateForStatus',{
      opportunityId : oppId,
      status_id : salesStatusId,
      currentDate: moment().format('YYYY-MM-DD')
    });
  }
  
  isFyChange = () => {
    let accessList = _.where(this._roleService.roles, { application_id: 36, object_id: 29362});

    if (accessList.length > 0)
      return true;

    else
      return false;
}

  async checkFlagChangeAccess() {
    let editAccess = _.where(this._roleService.roles, { application_id: 36, object_id: 29416, operation: "*" });
    if (editAccess.length > 0) {
      return true;
    }
    else {
      return false;
    }

  }

  updateOpportunityActualClosureDate(oppId, AcDate,statusId){
    return this.http.post('/api/opportunity/editOpportunityActualClosureDate',{
      opportunityId : oppId,
      actualClosureDate : AcDate,
      statusId: statusId
    });
  }

  getOpportunityProjectDetailsForQuote = (opportunity_id) => {
    return new Promise((resolve, reject) => {
      this.http
        .post("/api/opportunity/getOpportunityProjectDetails", {
          opportunityId: opportunity_id,
        })
        .subscribe(
          (res: any) => {
            return resolve(res);
          },
          (err) => {
            return reject(err);
          }
        );
    });
  };

  getStatusStageMappingConfig() {
    return this.http.get<any>('/api/opportunity/getStatusStageMappingConfig');
  }
  
  checkChildRestriction = () => {
    return this.http.post("/api/opportunity/checkChildRestriction",{  
    })
  }
  
  checkProjectRestrictionAccount = (opportunityId) => {
    return this.http.post("/api/opportunity/checkProjectRestrictionAccount",{ 
      opportunityId:opportunityId 
    })
  }
  
  checkIssuePoAndPoValue = (opportunity_id,status,issue_po) => {
    return new Promise((resolve, reject) => {
      this.http
        .post("/api/opportunity/checkIssuePoAndPoValue", {
          opportunityId:opportunity_id,
          status,
          issue_po:issue_po
        })
        .subscribe(
          (res: any) => {
            return resolve(res);
          },
          (err) => {
            return reject(err);
          }
        );
    });
  };
  
  checkPoValueMatch = (opportunity_id,status,opportunity_value=null,po_value=null) => {
    return new Promise((resolve, reject) => {
      this.http
        .post("/api/opportunity/checkPoValueMatch", {
          opportunityId:opportunity_id,
          status,
          opportunity_value:opportunity_value,
          po_value:po_value
        })
        .subscribe(
          (res: any) => {
            return resolve(res);
          },
          (err) => {
            return reject(err);
          }
        );
    });
  };

  
  getOpportunityDocumentTypes = async () => {
    try {
      const response = await this.http.post("/api/opportunity/getOpportunityDocumentTypes", {}).toPromise();
      return response;
    } catch (error) {
      console.error("Error retrieving document types:", error);
      throw error;
    }
  };

  updateDocumentTypes(documentInfo: any[]): Observable<any> {
    return this.http.post('/api/opportunity/updateDocumentTypes', { fileDetails: documentInfo });
  }

  checkIfDocUploaded(contextId, docType): Observable<any> {
    return this.http.post('/api/opportunity/checkIfDocUploaded', { contextId, docType });
  }
  
  getConfigForOpportunityDetails = (keyWord) => {
    return new Promise((resolve, reject) => {
      this.http
        .post("/api/opportunity/getConfigForOpportunityDetails", {
          key:keyWord,
          
        })
        .subscribe(
          (res: any) => {
            return resolve(res);
          },
          (err) => {
            return reject(err);
          }
        );
    });
  };

  updateRiskStatus(opportunity_id: any, at_risk: any) {
    return new Promise((resolve, reject) => {
      this.http
        .post('/api/opportunity/updateRiskStatus', { opportunity_id: opportunity_id, at_risk: at_risk })
        .subscribe((res) => {
          return resolve(res);
        }),
        (err) => {
          return reject(err);
        };
    });
  }

  veryShortInterval = 1500;

  shortInterval = 2500;

  mediumInterval = 3500;

  longInterval = 5000;

  veryLongInterval = 6000;
}



