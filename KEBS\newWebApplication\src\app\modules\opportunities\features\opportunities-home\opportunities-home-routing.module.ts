import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { OpportunitiesComponent } from '../../opportunities.component';
import { OpportunitiesLandingPageComponent } from './pages/opportunities-landing-page/opportunities-landing-page.component';
import { OpportunitySettingsComponent } from './pages/opportunity-settings/opportunity-settings.component';
import { AddQualifierFormComponent } from './components/add-qualifier-form/add-qualifier-form.component';
import { GovernanceComponent } from './components/governance/governance.component';
import { AddPresalesFormComponent } from './components/add-presales-form/add-presales-form.component';
import { AddTemplateTypeComponent } from './components/add-template-type/add-template-type.component';
import { OpportunityFormsComponent } from './components/opportunity-forms/opportunity-forms.component';
import { WinLossFormComponent } from './components/win-loss-form/win-loss-form.component';
import { OpportunitiesNewLandingPageComponent } from './pages/opportunities-new-landing-page/opportunities-new-landing-page.component';
import { HubspotSyncSettingsComponent } from './components/hubspot-sync-settings/hubspot-sync-settings.component';
import { StatusSettingsComponent } from './components/status-settings/status-settings.component';
import { FormConfigComponent } from './components/form-config/form-config.component';
import { OpportunityFieldConfigComponent } from './components/opportunity-field-config/opportunity-field-config.component';
import { AccountFieldConfigComponent } from './components/account-field-config/account-field-config.component';
import { AuditNotificationConfigComponent } from './components/audit-notification-config/audit-notification-config.component';
import { ActivityTemplateComponent } from './components/activity-template/activity-template.component';
import { ExitEmployeeHandleReallocateComponent } from './components/exit-employee-handle-reallocate/exit-employee-handle-reallocate.component';
import { CrmAuthGuard } from '../../guard/crm-global-auth.guard';
import { OpportunityStaticReportComponent } from './pages/opportunity-static-report/opportunity-static-report.component';
const routes: Routes = [
  {
    path: '',
    component: OpportunitiesNewLandingPageComponent,
    data: { application_id: 36 },
    canActivate: [CrmAuthGuard],
  },
  {
    path: 'static-report',
    component: OpportunityStaticReportComponent,
    data: { application_id: 36 },
    canActivate: [CrmAuthGuard],
  },
  {
    path: 'opportunitySettings',
    component: OpportunitySettingsComponent,
    data: { application_id: 36, breadcrumb: 'Settings' },
    canActivate: [CrmAuthGuard],
    children: [
      {
        path: 'addQualifierForm',
        component: AddQualifierFormComponent,
        data: { application_id: 36, object_id: 537 },
        canActivate: [CrmAuthGuard],
      },
      {
        path: 'governance',
        component: GovernanceComponent,
        data: { application_id: 36 },
        canActivate: [CrmAuthGuard],
      },
      {
        path: 'presalesForm',
        component: AddPresalesFormComponent,
        data: { application_id: 36 },
        canActivate: [CrmAuthGuard],
      },
      {
        path: 'templateType',
        component: AddTemplateTypeComponent,
        data: { application_id: 36 },
        canActivate: [CrmAuthGuard],
      },
      {
        path: 'winLossForm',
        component: WinLossFormComponent,
        data: { application_id: 36 },
        canActivate: [CrmAuthGuard],
      },
      {
        path: 'opportunityForm',
        component: OpportunityFormsComponent,
        data: { application_id: 36 },
        canActivate: [CrmAuthGuard],
      },
      {
        path: 'hubspot',
        component: HubspotSyncSettingsComponent,
        data: { application_id: 36 },
        canActivate: [CrmAuthGuard],
      },
      {
        path: 'status',
        component: StatusSettingsComponent,
        data: { application_id: 36 },
        canActivate: [CrmAuthGuard],
      },
      {
        path: 'stagewiseConfig',
        component: FormConfigComponent,
        data: { application_id: 36, object_id:29378 },
        canActivate: [CrmAuthGuard],
      },
      {
        path: 'opportunityFeildConfig',
        component: OpportunityFieldConfigComponent,
        data: { application_id: 36, object_id:29378 },
        canActivate: [CrmAuthGuard],
      },
      {
        path: 'accountFeildConfig',
        component: AccountFieldConfigComponent,
        data: { application_id: 36 },
        canActivate: [CrmAuthGuard],
      },
      {
        path: 'auditNotificationConfig',
        component: AuditNotificationConfigComponent,
        data: { application_id: 36 },
        canActivate: [CrmAuthGuard],
      },
      {
        path: 'activityTemplate',
        component: ActivityTemplateComponent,
        data: { application_id: 36 },
        canActivate: [CrmAuthGuard],
      },
      {
        path: 'exitEmployeeAllocate',
        component: ExitEmployeeHandleReallocateComponent,
        data: { application_id: 36 },
        canActivate: [CrmAuthGuard],
      },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class OpportunitiesHomeRoutingModule { }
