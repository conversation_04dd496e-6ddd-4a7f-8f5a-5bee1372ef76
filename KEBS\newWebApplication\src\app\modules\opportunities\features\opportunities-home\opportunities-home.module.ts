import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { OpportunitiesHomeRoutingModule } from './opportunities-home-routing.module';
import { OpportunitiesLandingPageComponent } from './pages/opportunities-landing-page/opportunities-landing-page.component';
//Angular Material
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatInputModule } from '@angular/material/input';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatCardModule } from '@angular/material/card';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { TooltipModule } from 'ng2-tooltip-directive';
import { InfiniteScrollModule } from 'ngx-infinite-scroll';
import { ScrollingModule } from '@angular/cdk/scrolling';
import { MatTabsModule } from '@angular/material/tabs';
import { MatDialogModule } from '@angular/material/dialog';
import { MatSelectModule } from '@angular/material/select';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatListModule } from '@angular/material/list';
import { DxHtmlEditorModule } from "devextreme-angular";
import { NgxContentLoadingModule } from 'ngx-content-loading';
//components
import { BidManagerCardComponent } from './components/bid-manager-card/bid-manager-card.component';
import { BidManagerViewComponent } from './components/bid-manager-view/bid-manager-view.component';
import { OpportunitiesCardComponent } from './components/opportunities-card/opportunities-card.component';
import { PipelineCardComponent } from './components/pipeline-card/pipeline-card.component';
import { ShimmerComponent } from '../../shared/components/shimmer/shimmer.component';
import { UserProfileComponent } from '../../shared/components/user-profile/user-profile.component';
import { CurrencyComponent } from '../../shared/components/currency/currency.component';
import { ShimmerBireportComponent } from './components/shimmer-bireport/shimmer-bireport.component';
import { UserImageComponent } from './components/user-image/user-image.component';
//pipes
import { DDMMMYYFormat } from '../../shared/pipes/reportDateFormat.pipe';

import { SafeHtmlPipe } from '../../shared/pipes/safeHtml.pipe';
import { multiDate } from '../../shared/pipes/dateFormatMultiple.pipe';
import { DateDDMMYY } from '../../shared/pipes/dateFormat2.pipe';
import { DateDDMMYYYY } from '../../shared/pipes/dateFormat.pipe';

//userDefined modules
// import { DirectiveModule } from '../../directives/directive.module';
import { DirectiveModule } from "src/app/modules/main/directives/directive.module";
import { CommonFilterComponent } from './components/common-filter/common-filter.component';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatDatepickerModule } from '@angular/material/datepicker';

//others
import { MatNativeDateModule } from "@angular/material/core"
import { MatMomentDateModule } from "@angular/material-moment-adapter";
import { NgxMatSelectSearchModule } from 'ngx-mat-select-search';
import {SharedComponentsModule} from "src/app/app-shared/app-shared-components/components.module"
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import {DragDropModule} from '@angular/cdk/drag-drop';
import {​​​​ BrowserTransferStateModule, TransferState }​​​​ from '@angular/platform-browser';
import { OpportunitySettingsComponent } from './pages/opportunity-settings/opportunity-settings.component';
import { AddQualifierFormComponent } from './components/add-qualifier-form/add-qualifier-form.component';
//import { InputSearchComponent } from './components/input-search/input-search.component';
import { GovernanceComponent } from './components/governance/governance.component';
import { SatPopoverModule } from "@ncstate/sat-popover";
import { AddPresalesFormComponent } from './components/add-presales-form/add-presales-form.component';
import { AddTemplateTypeComponent } from './components/add-template-type/add-template-type.component';
import { WinLossFormComponent } from './components/win-loss-form/win-loss-form.component';
import { OpportunityFormsComponent } from './components/opportunity-forms/opportunity-forms.component';
import { OpportunitiesNewLandingPageComponent } from './pages/opportunities-new-landing-page/opportunities-new-landing-page.component';
import { HubspotSyncSettingsComponent } from './components/hubspot-sync-settings/hubspot-sync-settings.component';
import { StatusSettingsComponent } from './components/status-settings/status-settings.component';
import { FormConfigComponent } from './components/form-config/form-config.component';
import { OpportunityFieldConfigComponent } from './components/opportunity-field-config/opportunity-field-config.component';
import { AccountFieldConfigComponent } from './components/account-field-config/account-field-config.component';
import { AuditNotificationConfigComponent } from './components/audit-notification-config/audit-notification-config.component';
import { ActivityTemplateComponent } from './components/activity-template/activity-template.component';
import { ShowMasterDataPipe } from './pipes/show-master-data.pipe';
import { ExitEmployeeHandleReallocateComponent } from './components/exit-employee-handle-reallocate/exit-employee-handle-reallocate.component';
import { OppHomeMultiSelectComponent } from './components/opp-home-multi-select/opp-home-multi-select.component';
import { ReportLandingModule } from 'src/app/modules/product-report/screens/report-landing/report-landing.module';
import { OpportunityStaticReportComponent } from './pages/opportunity-static-report/opportunity-static-report.component';
 
@NgModule({
  declarations: [
    OpportunitiesLandingPageComponent,
    OpportunitiesNewLandingPageComponent,
    SafeHtmlPipe,
    UserImageComponent,
    multiDate,
    DateDDMMYY,
    DateDDMMYYYY,
    ShimmerBireportComponent,
    ShimmerComponent,
    BidManagerViewComponent,
    CommonFilterComponent,
    BidManagerCardComponent,
    OpportunitiesCardComponent,
    PipelineCardComponent,
    UserProfileComponent,
    CurrencyComponent,
    OpportunitySettingsComponent,
    AddQualifierFormComponent,
    GovernanceComponent,
    AddPresalesFormComponent,
    AddTemplateTypeComponent,
    WinLossFormComponent,
    OpportunityFormsComponent,
    HubspotSyncSettingsComponent,
    StatusSettingsComponent,
    FormConfigComponent,
    OpportunityFieldConfigComponent,
    AccountFieldConfigComponent,
    AuditNotificationConfigComponent,
    ActivityTemplateComponent,
    ShowMasterDataPipe,
    ExitEmployeeHandleReallocateComponent,
    OppHomeMultiSelectComponent,
    OpportunityStaticReportComponent
  ],
  imports: [
    SharedComponentsModule,
    BrowserTransferStateModule,
    CommonModule,
    OpportunitiesHomeRoutingModule,
    MatDatepickerModule,
    NgxContentLoadingModule,
    MatFormFieldModule,
    MatIconModule,
    MatMenuModule,
    TooltipModule,
    InfiniteScrollModule,
    MatInputModule,
    FormsModule,
    ReactiveFormsModule,
    MatListModule,
    MatButtonModule,
    MatTooltipModule,
    MatCardModule,
    MatProgressSpinnerModule,
    ScrollingModule,
    MatTabsModule,
    MatDialogModule,
    MatSidenavModule,
    DirectiveModule,
    MatNativeDateModule,
    MatMomentDateModule,
    MatAutocompleteModule,
    ScrollingModule,
    MatButtonModule,
    MatMenuModule,
    MatTooltipModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    MatProgressSpinnerModule,
    MatListModule,
    MatNativeDateModule,
    MatMomentDateModule,
    MatDatepickerModule,
    FormsModule,
    ReactiveFormsModule,
    MatSelectModule,
    DxHtmlEditorModule,
    NgxMatSelectSearchModule,
    SatPopoverModule,
    DragDropModule,
    MatSlideToggleModule,
    ReportLandingModule
    

  ],
})
export class OpportunitiesHomeModule {}
