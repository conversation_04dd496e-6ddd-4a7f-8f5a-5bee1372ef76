<div *ngIf="pipelineViewEnabled == true">
    <app-pipeline-card (outputData)="getPipelineView($event)">
    </app-pipeline-card>
</div>

<div class="container-fluid opportunity-report-styles pl-0 pr-0">

    <div class="toggle-button d-flex justify-content-end" *ngIf="isStaticReportEnabled">
        <button mat-button class="shine-button" (click)="redirectOV2()"
            matTooltip="Try our brand new Opportunity Report!" aria-label="Try new Opportunity Report">
            <div class="btn-content d-flex align-items-center justify-content-center">
                <span class="label">{{ static_report_label }}</span>
                <mat-icon class="shine-icon">rocket_launch</mat-icon>
            </div>
        </button>
    </div>

    <!-- <div class="toggle-button d-flex justify-content-end" *ngIf="isStaticReportEnabled">
        <div class="d-flex align-items-center redirect">

            <button class="redirect-button" mat-button (click)="redirectOV2()">
                <div class="d-flex align-items-center justify-content-center" matTooltip="">
                    <div> {{static_report_label}} </div>
                    <mat-icon> chevron_right </mat-icon>
                </div>
            </button>

        </div>
    </div> -->

    <udrf-header *ngIf="udrf"></udrf-header>

    <udrf-body *ngIf="udrf"></udrf-body>

    <div class="static" *ngIf="!udrf">
        <app-details-page [applicationId]="36" [reportId]="20036"></app-details-page>
    </div>
</div>