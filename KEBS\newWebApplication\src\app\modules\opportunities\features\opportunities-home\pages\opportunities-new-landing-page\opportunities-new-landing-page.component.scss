.opportunity-report-styles {

  .status-circular {
    height: 13px;
    width: 13px;
    margin-top: 4px;
    border-radius: 50%;
    box-shadow: 0 2px 3px -1px rgba(0, 0, 0, 0.2),
      0 1px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 14px 0 rgba(0, 0, 0, 0.12);
  }

  .shine-button {
    display: flex;
    align-items: center;
    background: linear-gradient(270deg, #f27a6c 105.29%) !important;
    color: white;
    border-radius: 8px;
    padding: 6px 16px;
    position: relative;
    overflow: hidden;
    // box-shadow: 0 4px 12px rgba(66, 165, 245, 0.4);
    transition: transform 0.3s ease;
    animation: shinePulse 3s infinite;
    max-height: 2.5rem;

    &:hover {
      // background: linear-gradient(145deg, #f0a299, #f27a6c);
      transform: scale(1.05);
      border-radius: 8px;

      .shine-icon {
        animation: pulse 1s ease-in-out;
      }
    }

    .btn-content {
      display: flex;
      align-items: center;

      .label {
        font-weight: 600;
        font-size: 12px;
        margin-right: 8px;
      }

      .shine-icon {
        transition: transform 0.3s ease;
      }
    }
  }

  @keyframes shinePulse {

    0%,
    100% {
      box-shadow: 0 0 0 rgba(66, 165, 245, 0.0);
      border-radius: 8px;
    }

    50% {
      box-shadow: 0 0 16px #f4b4ad;
      border-radius: 8px;
    }
  }

  @keyframes pulse {
    0% {
      transform: scale(1);
    }

    50% {
      transform: scale(1.4);
    }

    100% {
      transform: scale(1);
    }
  }


  .toggle-button {
    margin-left: auto;
    margin-bottom: -3rem;
    padding-right: 1%;
    margin-top: 5rem;
    width: 10rem;
    z-index: 1000;
    color: #6B7A99;
    font-size: 12px;
    font-weight: 400;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: inline;
    font-family: "Roboto";
    position: sticky;
  }


  .is-submitted {
    background: #ff7200;
    color: white !important;
  }

  .is-draft {
    background: #c7c4c4;
    color: white !important;
  }

  .is-approved {
    background: #009432;
    color: white !important;
  }

  .is-wfh {
    background: #9980fa;
    color: white !important;
  }

  .is-rejected {
    background: #cf0001;
    color: white !important;
  }

  .is-cancelled {
    background: #9f2825;
    color: white !important;
  }

  .search-bar {
    ::ng-deep .mat-form-field {
      width: 40vw;
      padding-top: 25px;

      .mat-form-field-infix {
        font-size: 14px !important;
        padding: 0.5em 0 0.5em 0 !important;
        border-top: 0.54375em solid transparent !important;
      }
    }
  }

  .spinner-align {
    margin: auto;
    display: inline-block;
  }

  .infinite-scroll-fixed {
    height: 425px;
    overflow-y: scroll;
  }

  .infinite-scroll-fixed-shortened {
    height: 435px;
    overflow-y: scroll;
  }

  .ta-r {
    text-align: right !important;
  }

  .ta-l {
    text-align: left;
  }

  .is-normal-probability-text {
    color: #808080 !important;
  }

  .is-low-probability-text {
    color: #cf0001 !important;
  }

  .is-moderate-probability-text {
    color: #ff7200 !important;
  }

  .is-high-probability-text {
    color: #009432 !important;
  }

  .is-normal-probability {
    border-left-color: #808080 !important;
  }

  .is-low-probability {
    border-left-color: #cf0001 !important;
  }

  .is-moderate-probability {
    border-left-color: #ff7200 !important;
  }

  .is-high-probability {
    border-left-color: #009432 !important;
  }

  .value13Bold {
    color: #000;
    font-size: 13px !important;
    font-weight: 500 !important;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: inline;
  }

  .value14Bold {
    color: #000;
    font-size: 14px !important;
    font-weight: 500 !important;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: inline;
  }

  .listcard {
    transition: all 0.25s linear;
    height: auto;
  }

  .smallSubtleText {
    color: #66615b;
    font-size: 11px;
  }

  .data-type-card {
    min-height: 85px;
    transition: all 0.3s linear;
  }

  .data-type-card-is-active {
    border-color: #cf0001;
    background-color: #fff2f2;
  }

  .headingBold {
    color: #1a1a1a;
    font-weight: 600;
    font-size: 22px;
  }

  .valueGrey14 {
    color: #4a4a4a;
    font-size: 14px;
    font-weight: 300;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: inline;
  }

  .valueGrey12Normal {
    color: #4a4a4a;
    font-size: 12px;
    font-weight: normal;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: inline;
  }

  .cp {
    cursor: pointer !important;
  }

  .view-button-inactive {
    margin-top: 5px !important;
    line-height: 8px;
    width: 26px;
    height: 26px;
    margin-right: 10px !important;
    box-shadow: 0 4px 4px -1px rgba(0, 0, 0, 0.2),
      0 1px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 14px 0 rgba(0, 0, 0, 0.12);
    -webkit-animation: slide-top 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
    animation: slide-top 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;

    .iconButton {
      color: #66615b;
      font-size: 18px;
    }
  }

  .iconButton {
    color: #66615b;
    font-size: 18px;
  }

  .ta-c {
    text-align: center;
    padding-left: 0px !important;
    padding-right: 0px !important;
  }

  .divAlignItemCenter {
    display: flex;
    align-items: center;
  }


  .menu-icons {
    font-size: 4px;
  }

  .example-container {
    width: 400px;
    max-width: 100%;
    margin: 0 25px 25px 0;
    display: inline-block;
    vertical-align: top;
  }

  .example-list {
    border: solid 1px #ccc;
    min-height: 60px;
    background: white;
    border-radius: 4px;
    overflow: hidden;
    display: block;
  }

  ::ng-deep .example-box {
    border-bottom: solid 1px #ccc;
    color: rgba(0, 0, 0, 0.87);
    display: flex;
    flex-direction: row;
    align-items: left;
    justify-content: space-between;
    box-sizing: border-box;
    cursor: move;
    background: white;
    font-size: 4px;
  }

  ::ng-deep .menu-list {
    cursor: move;
    font-size: 8px;
  }

  .cdk-drag-preview {
    box-sizing: border-box;
    border-radius: 4px;
    box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2),
      0 8px 10px 1px rgba(0, 0, 0, 0.14),
      0 3px 14px 2px rgba(0, 0, 0, 0.12);
  }

  .cdk-drag-placeholder {
    opacity: 0;
  }

  .cdk-drag-animating {
    transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
  }

  .example-box:last-child {
    border: none;
  }

  .example-list.cdk-drop-list-dragging .example-box:not(.cdk-drag-placeholder) {
    transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
  }

  .static {
    margin-top: 2rem;
    height: calc(100vh - 200px);
  }

  .redirect {
    border: 1px solid #526179;
    border-radius: 4px;
    height: 2rem;
    width: 7rem;
  }

  .redirect-button {
    color: #6B7A99;
    font-size: 12px;
    font-weight: 500 !important;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: inline;
    font-family: "Roboto";


    mat-icon {
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 14px
    }
  }

}