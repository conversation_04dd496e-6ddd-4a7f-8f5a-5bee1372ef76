import { Component, OnInit, Inject, OnDestroy } from '@angular/core';
import * as _ from 'underscore';
import * as moment from 'moment';
import { MatSnackBar } from '@angular/material/snack-bar';
import { OpportunityService } from 'src/app/modules/opportunities/features/opportunities-detail/services/OpportunityService';
import { UdrfService } from 'src/app/services/udrf/udrf.service';
import { takeUntil } from 'rxjs/operators';
import { GraphApiService } from '../../../../../../services/graph/graph-api.service';
import { UtilityService } from 'src/app/services/utility/utility.service';
import { Subject, Subscription } from 'rxjs';
import {
  MatDialog,
  MatDialogModule,
  MatDialogRef,
  MAT_DIALOG_DATA,
} from '@angular/material/dialog';
import { OpportunityReloadService } from 'src/app/modules/opportunities/shared/services/opportunity-reload.service';
import { Router } from '@angular/router';
import { RolesService } from 'src/app/services/acl/roles.service';
import { JsonToExcelService } from 'src/app/services/excel/json-to-excel.service';
import { ToasterService } from 'src/app/services/toaster-service/toaster.service';
import { QuoteMainService } from 'src/app/modules/quote-builder/services/quote-main.service';
import { QBMasterDataService } from 'src/app/modules/quote-builder/services/qb-master-data.service';
import { tenantPipe } from './../../../../shared/pipes/tenant-form.pipe';
import { UdrfActivityService } from 'src/app/modules/account-sales/features/display-activities/services/udrf-activity.service'
import { TicketService } from 'src/app/modules/ams/services/ticket.service';
import { OpportunityStakeholdersService } from '../../../opportunities-detail/pages/internal-stakeholder/services/opportunity-stakeholders.service';
@Component({
  selector: 'app-opportunities-new-landing-page',
  templateUrl: './opportunities-new-landing-page.component.html',
  styleUrls: ['./opportunities-new-landing-page.component.scss'],
  providers: [tenantPipe]
})
export class OpportunitiesNewLandingPageComponent implements OnInit, OnDestroy {

  applicationId = 36;

  selectedCard = [];
  isCardClicked: boolean = false;
  OpportunityItemDataCurrentIndex = 0;
  current_year_start = moment();
  current_year_end = moment();
  requestDataSubscription: Subscription;
  opportunitySource =[]
  serviceType =[]
  bidType =[]
  salesStatus =[]
  salesStatusEdit=[]
  proposalType =[]
  proposalStatus =[]
  phase_or_sales_status = this.opportunityService.getLabelForOpportunity(36,2)==""?"Phases":this.opportunityService.getLabelForOpportunity(36,2)
  product_category_naming = this.opportunityService.getLabelForOpportunity(36,3)==""?"Product Category":this.opportunityService.getLabelForOpportunity(36,3)
  opportunityProbability =[]
  completeSHSAccess = false;
  isAdminAccess: boolean = false;
  pipelineViewEnabled: boolean = false;
  deactivateAccess : boolean = this.opportunityService.deactivateAccess()
  showMyOppToggle: boolean = false;
  isMyOppToggleChecked: boolean = false;

  isConvertValue: any;
  currency_code: any;
  tenant_info: any;

  udrfBodyColumnsData: any;
  isStaticReportEnabled: boolean = false;

  protected _onAppApiCalled = new Subject<void>();
  protected _onDestroy = new Subject<void>();

  dataTypeArray = [

  ];

  udrfBodyColumns : any;
  udrfItemProposalStatusColor: any[] = [];
  udrfItemStatusColor: any[] = [
    {
      status: "Open",
      color: "#e2e2e2"
    },
    {
      status: "In Progress",
      color: "#ffa502"
    },
    {
      status: "Completed",
      color: "#009432"
    }
  ];

  categorisedDataTypeArray = [

  ];



  minNoOfVisibleSummaryCards = 2;

  maxNoOfVisibleSummaryCards = 15;

  isEditDisable : boolean = true;
  isFiscalYearCheck : boolean = this.opportunityService.isFiscalYearCheck();
  BandBArrayList : any;
  
  line_of_business_access: boolean=this.opportunityService.displayLineOfBusinessAndModulesInCreateOpportunity();
  
  tempstagewiseFormFieldData:any;
  stagewiseFormFieldData:any;
  stagewiseFieldConfig:boolean=false;

  isQuoteEnabled: boolean = false;

  editAccess: boolean=false;
  
  formFieldData: any;
  formFieldDataExist : boolean = false;
  currencyList: any;
  isStatusStageMappingAllowed: boolean = false;
  statusStageMapping: any;
  dateFormat: string = 'DD - MMM - YYYY';
  quote_field_config: any;

  decimalPartsCache: { [key: string]: number } = {};
  maxDigitsAllowedCache: { [key: string]: number } = {};
  OPP_CURRENCY: any;
  udrf: boolean = true;
  static_report_label : string = '';
  isStaticReportTurned: boolean;
  generalConfig: {};
  
  constructor(public udrfService: UdrfService,
    private utilityService: UtilityService,
    private graphService: GraphApiService,
    private opportunityService: OpportunityService,
    public dialog: MatDialog,
    private reloadService: OpportunityReloadService,
    private router: Router,
    private roleService: RolesService,
    private exceltoJson: JsonToExcelService,
    private _toaster: ToasterService,
    private _quoteService: QuoteMainService,
    private _qbMasterService: QBMasterDataService,  
    private formField: tenantPipe,
    private udrfServices: UdrfActivityService,
    private _ticket: TicketService,
    private stakeholderService: OpportunityStakeholdersService
  ) {
    this.current_year_start = moment().startOf('year');
    this.current_year_end = moment().endOf('year');
  }

  async ngOnInit() {

    this.udrf = true;

    this.editAccess= await this.checkEditAcess()
    await this.getGlobalCRMDateFormat();
    this.generalConfig = await this.stakeholderService.getTheme();
    this._qbMasterService.quoteEnabled
      .subscribe(res => {

        this.isQuoteEnabled = res['quote_enabled'];

        if (this.isQuoteEnabled) {

          this.udrfService.udrfUiData.showQMPReportDownloadButton = true;

          this.udrfService.udrfUiData.downloadQMPReport = this.downloadQMPReport.bind(this);

          const sbhbRole = this.roleService.roles.find(val => val['application_id'] == 36 && val['object_id'] == 29400);

          if (sbhbRole && sbhbRole['object_value'] == "*") {
            
            this.udrfService.udrfUiData.showSBHBReportDownloadButton = true;

            this.udrfService.udrfUiData.downloadSBHBReport = this.downloadSBHBReport.bind(this);

          }

        }
        
      });

    await this._qbMasterService.quoteConfiguration
      .pipe(takeUntil(this._onDestroy))
      .subscribe(async res => {
        const quoteConfiguration = res;
        for (const configItem of quoteConfiguration)
          if (configItem && configItem['quote_config_name'] && configItem.hasOwnProperty('quote_config_value')) {
            switch (configItem['quote_config_name']) {
              case 'quote_field_config':
                this.quote_field_config = configItem['quote_config_value'] || null;
                break;
              default:
                break;
            }
          }
      });

    await this.getudrfbodycolumns();

    await this.opportunityService.stagewiseFieldConfig(29378).then((res:any)=>{
      this.stagewiseFieldConfig = res.messType == "S" && res.messData.length > 0 ? true : false
    },(err)=>{
      console.log(err)
    })
    console.log(this.stagewiseFieldConfig)

    this.opportunityService.getFormFieldCollection().subscribe(res => {
      if(res['messType'] == "S")
      this.formFieldData = res['result']
      console.log("res is : ",res)
    },
      err => {
        console.log(err)
    })
    if(this.isFiscalYearCheck){
      this.opportunityService.getFormFieldCollection().subscribe(res => {
        if(res['messType'] == "S")
        this.BandBArrayList = res['result'].find(x => x.field_name == "salesStatus")
        this.BandBArrayList = this.BandBArrayList ? this.BandBArrayList['b&b_Mandant_Status'] : []
      },
        err => {
          console.log(err)
      })
    }

    if(this.udrfBodyColumns == undefined){
      await this.getudrfbodycolumns();
    }

    await this.loadStatusStageMapping();
    await this.opportunityService.checkForAdminAccess().then((res:any)=>{
      this.isAdminAccess = res.messType == "S" ? true : false
    },(err)=>{
      console.log(err)
    })

    await this.opportunityService.checkIfMyOppToggleVisible().then((res:any)=>{
      if(res['messType'] == 'S') {
        if(res['showMyOppToggle']) {
          this.showMyOppToggle = true;
        }
        if(res['isMyOppToggleChecked']) {
          this.isMyOppToggleChecked = true;
        }
      }
    },(err)=>{
      console.log(err)
    })

    await this.checkGeneralConfigUserLevel('isMyOppToggleChecked');
    await this.checkGeneralConfigUserLevel('OPP_CURRENCY');

    this.callInlineEditFunctionMasterData()

    this.phase_or_sales_status = this.opportunityService.getLabelForOpportunity(36,2)==""?"Phases":this.opportunityService.getLabelForOpportunity(36,2)
    this.product_category_naming = this.opportunityService.getLabelForOpportunity(36,3)==""?"Product Category":this.opportunityService.getLabelForOpportunity(36,3)

    await this.opportunityService.getOpportunityStatusSales().then(res => {
       let shs_completed =_.where(res,{shs_completed: 1})

       if(shs_completed.length>0)
       {
          this.completeSHSAccess = true;
       }

      let categoryCardCodes = []
      _.each(res, (sec) => {

        let valh = sec.udrf_summary_card
        if (valh != null) {
          let val = JSON.parse(sec.udrf_summary_card)
          val['statusColor']=val['color']
          categoryCardCodes.push(val.dataTypeCode)
          this.dataTypeArray.push(val)
          this.udrfItemStatusColor.push({
            color: val.color,
            status: val.dataType,
            statusColor: val.color,
            statusName: val.dataType
          })
        }
      })
      console.log(categoryCardCodes)
      this.categorisedDataTypeArray.push({
        categoryType: "Status Cards",
        categoryCardCodes: categoryCardCodes,
        categoryCards: []
      })


    })


    await this.opportunityService.getProposalType().then(res => {
      _.each(res, (sec) => {
          this.udrfItemProposalStatusColor.push({
            color: sec.proposal_status_color,
            status: sec.proposal_status,
            statusColor: sec.proposal_status_color,
            statusName: sec.proposal_status
          })

          this.udrfItemStatusColor.push({
            color: sec.proposal_status_color,
            status: sec.proposal_status,
            statusColor: sec.proposal_status_color,
            statusName: sec.proposal_status
          })
      })
    })

    await this.opportunityService.getProductCategory().then(res=>{
      let categoryCardCodes=[]
      _.each(res,(sec)=>{
        let val: any={
          "cardFilter" : this.product_category_naming,
          "cardType" : "status",
          "color" : "#" + Math.floor(Math.random() * 16777215).toString(16),
          "dataType" : sec.name,
          "dataTypeCode" : sec.id,
          "dataTypeValue" : "0",
          "isActive" : false,
          "isVisible" : false,
          "statusColor" : "#" + Math.floor(Math.random() * 16777215).toString(16),
          "statusName" : sec.name
        }
        
        categoryCardCodes.push(sec.id)
        this.dataTypeArray.push(val)
      })
      this.categorisedDataTypeArray.push({
        categoryType: "Product Cards",
        categoryCardCodes: categoryCardCodes,
        categoryCards: []
      })


    })

    this.tenant_info = this.roleService.currency_info

    this.currency_code = this.tenant_info.default_currency ? this.tenant_info.default_currency : "INR";

    this.isConvertValue = this.tenant_info.is_to_convert_currency_value  != null ? this.tenant_info.is_to_convert_currency_value : true;

     this.reloadService.getNotification().subscribe(async (res) => {
      console.log(res);
      if (res == 'reload opps') {
        this.initReport();
      }
    });

    let durationRangesStartDate = [
      {
        checkboxId: 'CDCRD',
        checkboxName: 'Current Year',
        checkboxStartValue: this.current_year_start,
        checkboxEndValue: this.current_year_end,
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxId: 'CDCRD6',
        checkboxName: 'Previous Year',
        checkboxStartValue: this.utilityService.getFormattedDate(
          moment().subtract(1, 'year').startOf('year'),
          moment(moment().subtract(1, 'year').startOf('year')).date,
          0,
          0,
          0,
          0
        ),
        checkboxEndValue: this.utilityService.getFormattedDate(
          moment().subtract(1, 'year').endOf('year'),
          moment(moment().subtract(1, 'year').endOf('year')).date,
          0,
          0,
          0,
          0
        ),
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxId: 'CDCRD2',
        checkboxName: 'This Week',
        checkboxStartValue: this.utilityService.getFormattedDate(
          moment().startOf('week'),
          moment(moment().startOf('week')).date,
          0,
          0,
          0,
          0
        ),
        checkboxEndValue: this.utilityService.getFormattedDate(
          moment().endOf('week'),
          moment(moment().endOf('week')).date,
          0,
          0,
          0,
          0
        ),
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxId: 'CDCRD3',
        checkboxName: 'This Month',
        checkboxStartValue: this.utilityService.getFormattedDate(
          moment().startOf('month'),
          moment(moment().startOf('month')).date,
          0,
          0,
          0,
          0
        ),
        checkboxEndValue: this.utilityService.getFormattedDate(
          moment().endOf('month'),
          moment(moment().endOf('month')).date,
          0,
          0,
          0,
          0
        ),
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxId: 'CDCRD4',
        checkboxName: 'Next Month',
        checkboxStartValue: this.utilityService.getFormattedDate(
          moment().add(1, 'month').startOf('month'),
          moment(moment().add(1, 'month').startOf('month')).date,
          0,
          0,
          0,
          0
        ),
        checkboxEndValue: this.utilityService.getFormattedDate(
          moment().add(1, 'month').endOf('month'),
          moment(moment().add(1, 'month').endOf('month')).date,
          0,
          0,
          0,
          0
        ),
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxId: 'CDCRD5',
        checkboxName: 'Upcoming 3 Months',
        checkboxStartValue: this.utilityService.getFormattedDate(
          moment().startOf('month'),
          moment(moment().startOf('month')).date,
          0,
          0,
          0,
          0
        ),
        checkboxEndValue: this.utilityService.getFormattedDate(
          moment().add(2, 'month').endOf('month'),
          moment(moment().add(2, 'month').endOf('month')).date,
          0,
          0,
          0,
          0
        ),
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxId: 'CDCRD6',
        checkboxName: 'Previous Month',
        checkboxStartValue: this.utilityService.getFormattedDate(
          moment().subtract(1, 'month').startOf('month'),
          moment(moment().subtract(1, 'month').startOf('month')).date,
          0,
          0,
          0,
          0
        ),
        checkboxEndValue: this.utilityService.getFormattedDate(
          moment().subtract(1, 'month').endOf('month'),
          moment(moment().subtract(1, 'month').endOf('month')).date,
          0,
          0,
          0,
          0
        ),
        isCheckboxDefaultSelected: false,
      },
    ];

    let durationRangesEndDate = [
      {
        checkboxId: 'CDCED',
        checkboxName: 'Current Year',
        checkboxStartValue: this.current_year_start,
        checkboxEndValue: this.current_year_end,
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxId: 'CDCED1',
        checkboxName: 'Previous Year',
        checkboxStartValue: this.utilityService.getFormattedDate(
          moment().subtract(1, 'year').startOf('year'),
          moment(moment().subtract(1, 'year').startOf('year')).date,
          0,
          0,
          0,
          0
        ).format('YYYY-MM-DD'),
        checkboxEndValue: this.utilityService.getFormattedDate(
          moment().subtract(1, 'year').endOf('year'),
          moment(moment().subtract(1, 'year').endOf('year')).date,
          0,
          0,
          0,
          0
        ).format('YYYY-MM-DD'),
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxId: 'CDCED2',
        checkboxName: 'This Week',
        checkboxStartValue: this.utilityService.getFormattedDate(
          moment().startOf('week'),
          moment(moment().startOf('week')).date,
          0,
          0,
          0,
          0
        ).format('YYYY-MM-DD'),
        checkboxEndValue: this.utilityService.getFormattedDate(
          moment().endOf('week'),
          moment(moment().endOf('week')).date,
          0,
          0,
          0,
          0
        ).format('YYYY-MM-DD'),
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxId: 'CDCED3',
        checkboxName: 'This Month',
        checkboxStartValue: this.utilityService.getFormattedDate(
          moment().startOf('month'),
          moment(moment().startOf('month')).date,
          0,
          0,
          0,
          0
        ).format('YYYY-MM-DD'),
        checkboxEndValue: this.utilityService.getFormattedDate(
          moment().endOf('month'),
          moment(moment().endOf('month')).date,
          0,
          0,
          0,
          0
        ).format('YYYY-MM-DD'),
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxId: 'CDCED4',
        checkboxName: 'Next Month',
        checkboxStartValue: this.utilityService.getFormattedDate(
          moment().add(1, 'month').startOf('month'),
          moment(moment().add(1, 'month').startOf('month')).date,
          0,
          0,
          0,
          0
        ).format('YYYY-MM-DD'),
        checkboxEndValue: this.utilityService.getFormattedDate(
          moment().add(1, 'month').endOf('month'),
          moment(moment().add(1, 'month').endOf('month')).date,
          0,
          0,
          0,
          0
        ).format('YYYY-MM-DD'),
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxId: 'CDCED5',
        checkboxName: 'Previous Month',
        checkboxStartValue: this.utilityService.getFormattedDate(
          moment().subtract(1, 'month').startOf('month'),
          moment(moment().subtract(1, 'month').startOf('month')).date,
          0,
          0,
          0,
          0
        ).format('YYYY-MM-DD'),
        checkboxEndValue: this.utilityService.getFormattedDate(
          moment().subtract(1, 'month').endOf('month'),
          moment(moment().subtract(1, 'month').endOf('month')).date,
          0,
          0,
          0,
          0
        ).format('YYYY-MM-DD'),
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxId: 'CDCED6',
        checkboxName: 'Upcoming 3 Months',
        checkboxStartValue: this.utilityService.getFormattedDate(
          moment().startOf('month'),
          moment(moment().startOf('month')).date,
          0,
          0,
          0,
          0
        ).format('YYYY-MM-DD'),
        checkboxEndValue: this.utilityService.getFormattedDate(
          moment().add(2, 'month').endOf('month'),
          moment(moment().add(2, 'month').endOf('month')).date,
          0,
          0,
          0,
          0
        ).format('YYYY-MM-DD'),
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxId: 'CDCED7',
        checkboxName: 'Overdue',
        checkboxStartValue: this.utilityService.getFormattedDate(
          moment('2000-01-01'),
          moment('2000-01-01').date,
          0,
          0,
          0,
          0
        ).format('YYYY-MM-DD'),
        checkboxEndValue: this.utilityService.getFormattedDate(
          moment(),
          moment().date,
          0,
          0,
          0,
          0
        ).format('YYYY-MM-DD'),
        isCheckboxDefaultSelected: false,
      }
    ];

    let durationRangesCreatedOn = [
      {
        checkboxId: 'OPPCON',
        checkboxName: 'Current Year',
        checkboxStartValue: this.current_year_start,
        checkboxEndValue: this.current_year_end,
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxId: 'OPPCON1',
        checkboxName: 'This Week',
        checkboxStartValue: this.utilityService.getFormattedDate(
          moment().startOf('week'),
          moment(moment().startOf('week')).date,
          0,
          0,
          0,
          0
        ),
        checkboxEndValue: this.utilityService.getFormattedDate(
          moment().endOf('week'),
          moment(moment().endOf('week')).date,
          0,
          0,
          0,
          0
        ),
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxId: 'OPPCON2',
        checkboxName: 'This Month',
        checkboxStartValue: this.utilityService.getFormattedDate(
          moment().startOf('month'),
          moment(moment().startOf('month')).date,
          0,
          0,
          0,
          0
        ),
        checkboxEndValue: this.utilityService.getFormattedDate(
          moment().endOf('month'),
          moment(moment().endOf('month')).date,
          0,
          0,
          0,
          0
        ),
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxId: 'OPPCON3',
        checkboxName: 'Previous Month',
        checkboxStartValue: this.utilityService.getFormattedDate(
          moment().subtract(1, 'month').startOf('month'),
          moment(moment().subtract(1, 'month').startOf('month')).date,
          0,
          0,
          0,
          0
        ),
        checkboxEndValue: this.utilityService.getFormattedDate(
          moment().subtract(1, 'month').endOf('month'),
          moment(moment().subtract(1, 'month').endOf('month')).date,
          0,
          0,
          0,
          0
        ),
        isCheckboxDefaultSelected: false,
      }
    ];

    let durationRangesRFPDate = [
      {
        checkboxId: 'RFPNCB',
        checkboxName: 'Current Year',
        checkboxStartValue: this.current_year_start,
        checkboxEndValue: this.current_year_end,
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxId: 'RFPNCB1',
        checkboxName: 'This Week',
        checkboxStartValue: this.utilityService.getFormattedDate(
          moment().startOf('week'),
          moment(moment().startOf('week')).date,
          0,
          0,
          0,
          0
        ),
        checkboxEndValue: this.utilityService.getFormattedDate(
          moment().endOf('week'),
          moment(moment().endOf('week')).date,
          0,
          0,
          0,
          0
        ),
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxId: 'RFPNCB2',
        checkboxName: 'This Month',
        checkboxStartValue: this.utilityService.getFormattedDate(
          moment().startOf('month'),
          moment(moment().startOf('month')).date,
          0,
          0,
          0,
          0
        ),
        checkboxEndValue: this.utilityService.getFormattedDate(
          moment().endOf('month'),
          moment(moment().endOf('month')).date,
          0,
          0,
          0,
          0
        ),
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxId: 'RFPNCB3',
        checkboxName: 'Next Month',
        checkboxStartValue: this.utilityService.getFormattedDate(
          moment().add(1, 'month').startOf('month'),
          moment(moment().add(1, 'month').startOf('month')).date,
          0,
          0,
          0,
          0
        ),
        checkboxEndValue: this.utilityService.getFormattedDate(
          moment().add(1, 'month').endOf('month'),
          moment(moment().add(1, 'month').endOf('month')).date,
          0,
          0,
          0,
          0
        ),
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxId: 'RFPNCB4',
        checkboxName: 'Previous Month',
        checkboxStartValue: this.utilityService.getFormattedDate(
          moment().subtract(1, 'month').startOf('month'),
          moment(moment().subtract(1, 'month').startOf('month')).date,
          0,
          0,
          0,
          0
        ),
        checkboxEndValue: this.utilityService.getFormattedDate(
          moment().subtract(1, 'month').endOf('month'),
          moment(moment().subtract(1, 'month').endOf('month')).date,
          0,
          0,
          0,
          0
        ),
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxId: 'RFPNCB5',
        checkboxName: 'Upcoming 3 Months',
        checkboxStartValue: this.utilityService.getFormattedDate(
          moment().startOf('month'),
          moment(moment().startOf('month')).date,
          0,
          0,
          0,
          0
        ),
        checkboxEndValue: this.utilityService.getFormattedDate(
          moment().add(2, 'month').endOf('month'),
          moment(moment().add(2, 'month').endOf('month')).date,
          0,
          0,
          0,
          0
        ),
        isCheckboxDefaultSelected: false,
      }
    ];

    let durationRangesProposalSubmissionDate = [
      {
        checkboxId: 'PSNCB',
        checkboxName: 'Current Year',
        checkboxStartValue: this.current_year_start,
        checkboxEndValue: this.current_year_end,
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxId: 'PSNCB1',
        checkboxName: 'This Week',
        checkboxStartValue: this.utilityService.getFormattedDate(
          moment().startOf('week'),
          moment(moment().startOf('week')).date,
          0,
          0,
          0,
          0
        ),
        checkboxEndValue: this.utilityService.getFormattedDate(
          moment().endOf('week'),
          moment(moment().endOf('week')).date,
          0,
          0,
          0,
          0
        ),
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxId: 'PSNCB2',
        checkboxName: 'This Month',
        checkboxStartValue: this.utilityService.getFormattedDate(
          moment().startOf('month'),
          moment(moment().startOf('month')).date,
          0,
          0,
          0,
          0
        ),
        checkboxEndValue: this.utilityService.getFormattedDate(
          moment().endOf('month'),
          moment(moment().endOf('month')).date,
          0,
          0,
          0,
          0
        ),
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxId: 'PSNCB3',
        checkboxName: 'Next Month',
        checkboxStartValue: this.utilityService.getFormattedDate(
          moment().add(1, 'month').startOf('month'),
          moment(moment().add(1, 'month').startOf('month')).date,
          0,
          0,
          0,
          0
        ),
        checkboxEndValue: this.utilityService.getFormattedDate(
          moment().add(1, 'month').endOf('month'),
          moment(moment().add(1, 'month').endOf('month')).date,
          0,
          0,
          0,
          0
        ),
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxId: 'PSNCB4',
        checkboxName: 'Previous Month',
        checkboxStartValue: this.utilityService.getFormattedDate(
          moment().subtract(1, 'month').startOf('month'),
          moment(moment().subtract(1, 'month').startOf('month')).date,
          0,
          0,
          0,
          0
        ),
        checkboxEndValue: this.utilityService.getFormattedDate(
          moment().subtract(1, 'month').endOf('month'),
          moment(moment().subtract(1, 'month').endOf('month')).date,
          0,
          0,
          0,
          0
        ),
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxId: 'PSNCB5',
        checkboxName: 'Upcoming 3 Months',
        checkboxStartValue: this.utilityService.getFormattedDate(
          moment().startOf('month'),
          moment(moment().startOf('month')).date,
          0,
          0,
          0,
          0
        ),
        checkboxEndValue: this.utilityService.getFormattedDate(
          moment().add(2, 'month').endOf('month'),
          moment(moment().add(2, 'month').endOf('month')).date,
          0,
          0,
          0,
          0
        ),
        isCheckboxDefaultSelected: false,
      }
    ];

    let durationRangesSubmissionToSales = [
      {
        checkboxId: 'SSNCB',
        checkboxName: 'Current Year',
        checkboxStartValue: this.current_year_start,
        checkboxEndValue: this.current_year_end,
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxId: 'SSNCB1',
        checkboxName: 'This Week',
        checkboxStartValue: this.utilityService.getFormattedDate(
          moment().startOf('week'),
          moment(moment().startOf('week')).date,
          0,
          0,
          0,
          0
        ),
        checkboxEndValue: this.utilityService.getFormattedDate(
          moment().endOf('week'),
          moment(moment().endOf('week')).date,
          0,
          0,
          0,
          0
        ),
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxId: 'SSNCB2',
        checkboxName: 'This Month',
        checkboxStartValue: this.utilityService.getFormattedDate(
          moment().startOf('month'),
          moment(moment().startOf('month')).date,
          0,
          0,
          0,
          0
        ),
        checkboxEndValue: this.utilityService.getFormattedDate(
          moment().endOf('month'),
          moment(moment().endOf('month')).date,
          0,
          0,
          0,
          0
        ),
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxId: 'SSNCB3',
        checkboxName: 'Next Month',
        checkboxStartValue: this.utilityService.getFormattedDate(
          moment().add(1, 'month').startOf('month'),
          moment(moment().add(1, 'month').startOf('month')).date,
          0,
          0,
          0,
          0
        ),
        checkboxEndValue: this.utilityService.getFormattedDate(
          moment().add(1, 'month').endOf('month'),
          moment(moment().add(1, 'month').endOf('month')).date,
          0,
          0,
          0,
          0
        ),
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxId: 'SSNCB4',
        checkboxName: 'Previous Month',
        checkboxStartValue: this.utilityService.getFormattedDate(
          moment().subtract(1, 'month').startOf('month'),
          moment(moment().subtract(1, 'month').startOf('month')).date,
          0,
          0,
          0,
          0
        ),
        checkboxEndValue: this.utilityService.getFormattedDate(
          moment().subtract(1, 'month').endOf('month'),
          moment(moment().subtract(1, 'month').endOf('month')).date,
          0,
          0,
          0,
          0
        ),
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxId: 'SSNCB5',
        checkboxName: 'Upcoming 3 Months',
        checkboxStartValue: this.utilityService.getFormattedDate(
          moment().startOf('month'),
          moment(moment().startOf('month')).date,
          0,
          0,
          0,
          0
        ),
        checkboxEndValue: this.utilityService.getFormattedDate(
          moment().add(2, 'month').endOf('month'),
          moment(moment().add(2, 'month').endOf('month')).date,
          0,
          0,
          0,
          0
        ),
        isCheckboxDefaultSelected: false,
      }
    ];

    let obvMargins = [
      {
        checkboxId: "OBVCRD1",
        checkboxName: "No OBV Limit",
        checkboxStartValue: 0,
        checkboxEndValue: 99999,
        isCheckboxDefaultSelected: true
      },
      {
        checkboxId: "OBVCRD2",
        checkboxName: this.currency_code=="INR"?(this.isConvertValue==1?">1 Cr":">1,00,00,000 INR"):((this.isConvertValue==1?">1 M":">1,000,000 "+this.currency_code)),
        checkboxStartValue: 1,
        checkboxEndValue: 99999,
        isCheckboxDefaultSelected: false
      },
      {
        checkboxId: "OBVCRD3",
        checkboxName: this.currency_code=="INR"?(this.isConvertValue==1?">3 Cr":">3,00,00,000 INR"):((this.isConvertValue==1?">3 M":">3,000,000 "+this.currency_code)),
        checkboxStartValue: 3,
        checkboxEndValue: 99999,
        isCheckboxDefaultSelected: false
      },
      {
        checkboxId: "OBVCRD4",
        checkboxName: this.currency_code=="INR"?(this.isConvertValue==1?">6 Cr":">6,00,00,000 INR"):((this.isConvertValue==1?">6 M":">6,000,000 "+this.currency_code)),
        checkboxStartValue: 6,
        checkboxEndValue: 99999,
        isCheckboxDefaultSelected: false
      }
    ];

    this.udrfService.udrfFunctions.constructCustomRangeData(12, "value", obvMargins);

    this.udrfService.udrfFunctions.constructCustomRangeData(
      7,
      'date',
      durationRangesStartDate
    );

    this.udrfService.udrfFunctions.constructCustomRangeData(
      6,
      'date',
      durationRangesEndDate
    );

    this.udrfService.udrfFunctions.constructCustomRangeData(
      8,
      'date',
      durationRangesRFPDate
    );

    this.udrfService.udrfFunctions.constructCustomRangeData(
      9,
      'date',
      durationRangesProposalSubmissionDate
    );

    this.udrfService.udrfFunctions.constructCustomRangeData(
      10,
      'date',
      durationRangesSubmissionToSales
    );

    this.udrfService.udrfFunctions.constructCustomRangeData(
      11,
      'date',
      durationRangesCreatedOn
    );

    this.udrfService.udrfFunctions.constructCustomRangeData(
      36,
      'date',
      durationRangesCreatedOn
    );


    this.udrfService.udrfBodyData = [];
    // this.udrfService.udrfUiData.groupByResponseData=[];


    this.udrfService.udrfData.applicationId = this.applicationId;
    this.udrfService.udrfUiData.loaderConfig = this.generalConfig['loaderConfig'] || false;
    this.udrfService.udrfUiData.showNewReleasesButton = true;
    this.udrfService.udrfUiData.customColumnSelect = true;
    this.udrfService.udrfUiData.showItemDataCount = true;
    this.udrfService.udrfUiData.itemDataType = "";
    this.udrfService.udrfUiData.bookmarkId = "id";
    this.udrfService.udrfUiData.totalItemDataCount = 0;
    this.udrfService.udrfUiData.showSearchBar = true;
    this.udrfService.udrfUiData.showActionButtons = true;
    this.udrfService.udrfUiData.showUdrfModalButton = true;
    this.udrfService.udrfUiData.showSettingsModalButton = true;
    this.udrfService.udrfUiData.isReportDownloading = false;
    this.udrfService.udrfUiData.showReportDownloadButton = true;
    this.udrfService.udrfUiData.showColumnConfigButton = true;
    this.udrfService.udrfUiData.showGroupByButton = true;
    this.udrfService.udrfUiData.itemHasOpenInNewTab = true;
    this.udrfService.udrfUiData.openInNewTab = this.openOpportunityPageinNewTab.bind(this);
    this.udrfService.udrfUiData.itemHasOpenModal =  this.editAccess;
    this.udrfService.udrfUiData.openModal = this.openEditScreen.bind(this);
    this.udrfService.udrfUiData.openModalName = "Edit"
    this.udrfService.udrfUiData.openModalMatIcon = "edit" 
    this.udrfService.udrfUiData.itemHasOpenModal1 = this.line_of_business_access;
    this.udrfService.udrfUiData.openModal1 = this.viewHierarchyProduct.bind(this);
    this.udrfService.udrfUiData.openModal1Name = "View Hierarchy"
    this.udrfService.udrfUiData.openModal1MatIcon = "business_center"
    this.udrfService.udrfUiData.udrfBodyColumns = this.udrfBodyColumns;
    this.udrfService.udrfUiData.horizontalScroll = true
    this.udrfService.udrfUiData.resolveVisibleSummaryCards = this.resolveVisibleDataTypeArray.bind(this)
    this.udrfService.udrfUiData.summaryCardsSelected = this.dataTypeCardSelected.bind(this);
    this.udrfService.udrfUiData.itemcardSelected = this.itemcardSelected.bind(this)
    this.udrfService.udrfUiData.columnClick = this.itemcardSelected.bind(this)
    this.udrfService.udrfUiData.itemDataScrollDown = this.onOpportunityListScrollDown.bind(this);
    this.udrfService.udrfFunctions.resolveVisibleColumnConfigItems();
    this.udrfService.udrfUiData.summaryCardsItem = {};
    this.udrfService.udrfUiData.callInlineEditApi =this.editAccess == true ? this.callInlineEditApi.bind(this) :  this.callInlineEditApiRestricted.bind(this);
    this.udrfService.udrfUiData.inlineEditData = {};
    this.udrfService.udrfUiData.inlineEditDropDownMasterDatas = {};
    this.udrfService.udrfUiData.udrfItemStatusColor = this.udrfItemStatusColor;
    this.udrfService.udrfUiData.udrfItemProposalStatusColor = this.udrfItemProposalStatusColor;
    this.udrfService.udrfUiData.summaryCards = this.dataTypeArray;
    this.udrfService.udrfUiData.udrfVisibleBodyColumns = this.udrfService.udrfUiData.udrfVisibleBodyColumns;
    this.udrfService.udrfUiData.udrfInvisibleBodyColumns = this.udrfService.udrfUiData.udrfInvisibleBodyColumns;
    this.udrfService.udrfUiData.categorisedSummaryCards = this.categorisedDataTypeArray;
    this.udrfService.udrfUiData.minNoOfVisibleSummaryCards = this.minNoOfVisibleSummaryCards;
    this.udrfService.udrfUiData.maxNoOfVisibleSummaryCards = this.maxNoOfVisibleSummaryCards;
    this.udrfService.udrfUiData.selectedCard = this.selectedCard;
    this.udrfService.udrfUiData.variant = 0;
    this.udrfService.udrfUiData.itemHasQuickCta = false;
    this.udrfService.udrfUiData.itemHasComments = false;
    this.udrfService.udrfUiData.itemHasHierarchyView = false;
    this.udrfService.udrfUiData.itemHasBookmark = false;
    this.udrfService.udrfUiData.quickCTAInput = {};
    this.udrfService.udrfUiData.commentsInput = {};
    this.udrfService.udrfUiData.commentsContext = {};
    this.udrfService.udrfUiData.onColumnClickItem = "";
    this.udrfService.udrfUiData.countForOnlyThisReport = false;
    this.udrfService.udrfUiData.isHeaderSort = true;
    this.udrfService.getAppUdrfConfig(this.applicationId, this.initReport.bind(this));
    this.dropdownInlineEditOpportunity()
    this.udrfService.getNotifyReleasesUDRF();
    this.udrfService.udrfUiData.itemHasStarRating = true;
    this.udrfService.udrfUiData.starRating = this.onStarClick.bind(this);
    this.udrfService.udrfUiData.itemHasSubmitButton = this.completeSHSAccess;
    this.udrfService.udrfUiData.submitButtonMatIcon = "check_circle";
    this.udrfService.udrfUiData.submitButtonName = "Complete SHS";
    this.udrfService.udrfUiData.submitButtonClick = this.openWinLossForm.bind(this);

    this.udrfService.udrfUiData.isMoreOptionsNeeded = true;
    this.udrfService.udrfUiData.additionalViewButton = this.opportunityService.viewButton();
    this.udrfService.udrfUiData.adminSettingButton = this.isAdminAccess;
    this.udrfService.udrfUiData.openAdditionalView = this.openAdditionalView.bind(this);
    this.udrfService.udrfUiData.openAdminSettingsReport = this.openAdminSettingsReport.bind(this);
    this.udrfService.udrfUiData.downloadItemDataReport = this.downloadOpportunityReport.bind(this);
    this.udrfService.udrfUiData.showActualCurrencyForCurrencyComponent = false
    this.udrfService.udrfUiData.itemHasDeleteButton =this.deactivateAccess;
    this.udrfService.udrfUiData.openDeleteButton = this.deactivateOpportunity.bind(this)

    this.udrfService.udrfUiData.showMyOppToggle = this.showMyOppToggle;
    this.udrfService.udrfUiData.isMyOppToggleChecked = this.isMyOppToggleChecked;
    this.udrfService.udrfUiData.toggleMyOpp = this.toggleMyOpp.bind(this);
    this.udrfService.udrfUiData.updateGeneralConfigUserLevel = this.updateGeneralConfigUserLevel.bind(this);
    
    for (let i = 0; i < this.dataTypeArray.length; i++) {
      this.dataTypeArray[i].isActive = false;
    }

    let precomputeDecimalParts = () => {
      this.formFieldData.forEach(field => {
        this.decimalPartsCache[field.field_name] = Number(field?.decimal_part) || 13;
        this.maxDigitsAllowedCache[field.field_name] = Number(field?.max_digits) || 13;
      });
    }

    await this.opportunityService.getFormFieldCollection().toPromise().then(res => {
      if (res['messType'] == "S")
        this.formFieldData = res['result']
      precomputeDecimalParts();
      this.udrfService.udrfUiData.decimalPartsCache = this.decimalPartsCache;
      this.udrfService.udrfUiData.maxDigitsAllowedCache = this.maxDigitsAllowedCache;
      this.static_report_label = this.formField.transform('OPP_STATIC_REPORT', this.formFieldData, 'label') || 'Static Report';  
      this.isStaticReportEnabled = this.formField.transform('OPP_STATIC_REPORT', this.formFieldData, 'isActive');  
      this.formFieldDataExist = true;
    }).catch(
      (err) => {
        console.log(err)
      }
    )

    this.tenant_info = this.roleService.currency_info

    console.log(this.tenant_info)

    this.currency_code = this.tenant_info.default_currency ? this.tenant_info.default_currency : "INR";

    this.isConvertValue = this.tenant_info.is_to_convert_currency_value != null ? this.tenant_info.is_to_convert_currency_value : true;

    await this.opportunityService.getCurrencyList().toPromise().then(
      (res) => {
        console.log(res)
        this.currencyList = res;

        this.udrfService.udrfUiData.currencyValues = _.filter(this.currencyList, (item) => {

          if (this.tenant_info.reporting_currencies.includes(item.name))
            return item; //array of field ids
        });
        this.udrfService.udrfUiData.currencyValues.push({ id: 10, name: "Opportunity Currency" })
        console.log(this.udrfService.udrfUiData.currencyValues)
      }
    ).catch(
      (err) => {
        console.error(err);
      })

    this.udrfService.udrfUiData.selectedCurrencyValue = "Opportunity Currency"

    if (this.OPP_CURRENCY.length)
      this.udrfService.udrfUiData.selectedCurrencyValue = this.OPP_CURRENCY[0].name;

  }

  itemcardSelected(){
    console.log('return')
  }

  async loadStatusStageMapping(): Promise<void> {
    try {
      const response = await this.opportunityService.getStatusStageMappingConfig().toPromise();
      if (response?.CODE === 'ALLOWED') {
        this.isStatusStageMappingAllowed = true;
        this.statusStageMapping = response.mapping.reduce((acc, { status_id, proposal_status_id, proposal_status_name }) => {
          acc[status_id] = { proposal_status_id, proposal_status_name };
          return acc;
        }, {});
      } else {
        this.isStatusStageMappingAllowed = false;
        this.statusStageMapping = {};
      }
    } catch (error) {
      console.error('Error loading status stage mapping:', error);
    }
  }

  getProposalStatus(status_id: number): { proposal_status_id: number, proposal_status_name: string } {
    if (this.isStatusStageMappingAllowed) {
      return this.statusStageMapping[status_id] || { proposal_status_id: 1, proposal_status_name: 'Open' };
    }
    return { proposal_status_id: 0, proposal_status_name: null };
  }
  
  getudrfbodycolumns = async () => {
    await this.opportunityService.getUDRFBodyColumns().toPromise().then(
      (res) => {
        if (res['messType'] == 'S') {
          const restrictedFields = ['opportunity_value', 'po_value']; 
  
          for (const itemData of res['result']) {
            if (itemData['onColumnClick'] === "openOpportunity") {
              itemData['onColumnClick'] = this.openOpportunity.bind(this);
            }
            if (itemData['onColumnClick'] === "openProjectDetails") {
              itemData['onColumnClick'] = this.openProjectDetails.bind(this);
            }
  
            if (restrictedFields.includes(itemData['item']) && !this.checkFieldShouldbeRestricted('totalRevenue')) {
              itemData['isActive'] = false;
              itemData['isVisible'] = false;
            }
          }
  
          this.udrfBodyColumns = res['result'];
        }
      }
    ).catch((err) => {
      console.log(err);
    });
  };
  


  /**
   * @description Gets visible summary cards
   */
  resolveVisibleDataTypeArray = () => {
    for (let summaryCardsItem of this.udrfService.udrfUiData.summaryCards) {

      let isVisible;

      if (this.udrfService.udrfData.udrfSummaryCardCodes.length > 0) {

        isVisible = _.contains(this.udrfService.udrfData.udrfSummaryCardCodes, summaryCardsItem.dataTypeCode);
        summaryCardsItem.isVisible = isVisible;
        if (isVisible) {
          this.udrfService.udrfUiData.summaryCardsItem = summaryCardsItem;

        }
      }

    }

  }

  /**
   * @description Select type of summary cards selected
   */

  dataTypeCardSelected = () => {

    this.udrfService.udrfData.isItemDataLoading = true;
    this.udrfService.udrfData.noItemDataFound = false;
    let dataTypeArrayItem = this.udrfService.udrfUiData.summaryCardsItem;

    for (let i = 0; i < this.dataTypeArray.length; i++) {
      if (this.dataTypeArray[i].dataTypeCode != dataTypeArrayItem["dataTypeCode"])
        this.dataTypeArray[i].isActive = false;
    }

    if (!dataTypeArrayItem["isActive"]) {
      dataTypeArrayItem["isActive"] = true;
    }

    else {
      dataTypeArrayItem["isActive"] = false;
    }

    this.isCardClicked = true;

    this.OpportunityItemDataCurrentIndex = 0;
    this.udrfService.udrfBodyData = [];
    if(dataTypeArrayItem["isActive"]==true && dataTypeArrayItem["dataTypeValue"]!= "0")
      this.getOpportunityList();
      else if(dataTypeArrayItem["isActive"]==false)
      this.getOpportunityList();
      else{
        this.udrfService.udrfBodyData=[]
        this.udrfService.udrfData.noItemDataFound = true;
        this.udrfService.udrfData.isItemDataLoading = false;
      }
  
   

  }
  /**
   * @description Opens leads when click on card
   */
  opportunityCardClicked = () => {
    let opportunityId = this.udrfService.udrfUiData.itemCardSelecteditem["opportunity_id"];
    let opportunityName = this.udrfService.udrfUiData.itemCardSelecteditem["opportunity_name"];
    let navigationUrl = window.location.origin + "/main/opportunities/" + opportunityId + "/" + this.utilityService.encodeURIComponent(opportunityName);
    window.open(navigationUrl);
  }


  /**
     * @description Intialize the Leads Report
     */
  async initReport() {
    //console.log("InitReport")
    this._onAppApiCalled.next();
    this.OpportunityItemDataCurrentIndex = 0;
    this.isCardClicked = false;
    this.udrfService.udrfBodyData = [];
    // for (let i = 0; i < this.dataTypeArray.length; i++) {
    //   this.dataTypeArray[i].isActive = false;
    // }

    this.udrfService.udrfUiData.resolveColumnConfig();
    let activeSummaryCard = _.where(this.dataTypeArray, { isActive: true });
    let activeStatusSummaryCard = [];
    if (activeSummaryCard.length > 0) {
           await this.initOpportunityCard()
           let activeSummaryCard = _.where(this.dataTypeArray, { isActive: true });
           activeStatusSummaryCard = _.where(activeSummaryCard, { cardType: "status" });
      if (activeStatusSummaryCard.length > 0)
      if(activeStatusSummaryCard[0]["dataTypeValue"]!= "0")
      {
        this.getOpportunityList();
      }
      else{
        this.udrfService.udrfBodyData=[]
        this.udrfService.udrfData.noItemDataFound = true;
        this.udrfService.udrfData.isItemDataLoading = false;
      }
    }
    else{
      this.getOpportunityList();
    }
  }

   /**
   * @description This method calls Opportunity list
   */
   getOpportunityList =async() => {
    console.log("cll")
    let mainFilterArray = JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray));
    
    let activeSummaryCard = _.where(this.dataTypeArray, { isActive: true });

    let activeStatusSummaryCard = [];

    let statusApplied = false

    for (let custom_date_range of this.udrfService.udrfData.filterTypeArray) {
      console.log('custom_rnage1')
      console.log(this.udrfService.udrfData)
      if (custom_date_range.isCustomButtonActivated && custom_date_range.hasOwnProperty('customButtonValueStart') && custom_date_range.hasOwnProperty('customButtonValueEnd')) {
        console.log('custom_rnage1')
        custom_date_range['multiOptionSelectSearchValues'] = ["Custom"]
        custom_date_range['multiOptionSelectSearchValuesWithId'] = [{
          startLimit: moment(custom_date_range['customButtonValueStart']).startOf('day').format(),
          endLimit: moment(custom_date_range['customButtonValueEnd']).endOf('day').format()
        }]
        custom_date_range['filterStartValue']=moment(custom_date_range['customButtonValueStart']).startOf('day').format()
        custom_date_range['filterEndValue']=moment (custom_date_range['customButtonValueEnd']).endOf('day').format()
        console.log(mainFilterArray)
        mainFilterArray.push(custom_date_range)
        console.log(mainFilterArray)
        // this.initOpportunityCard()
      }
      
    }
    if (activeSummaryCard.length > 0) {

      activeStatusSummaryCard = _.where(activeSummaryCard, { cardType: "status" });

      console.log("summary",activeStatusSummaryCard)

      if (activeStatusSummaryCard.length > 0)

        if (mainFilterArray.length > 0) {

          for (let mainFilterArrayItem of mainFilterArray) {
            if (mainFilterArrayItem.filterName == activeSummaryCard[0]['cardFilter']) {
              mainFilterArrayItem.multiOptionSelectSearchValues = [activeSummaryCard[0].dataTypeCode];   
              mainFilterArrayItem.multiOptionSelectSearchValuesWithId = [activeSummaryCard[0].dataTypeCode];       
              statusApplied = true;
            }
            else if(mainFilterArray.filterName == activeSummaryCard[0]['cardFilter']) {
                mainFilterArrayItem.multiOptionSelectSearchValues = [activeSummaryCard[0].dataTypeCode];
                mainFilterArrayItem.multiOptionSelectSearchValuesWithId = [activeSummaryCard[0].dataTypeCode];
                statusApplied = true;
            }
          }
          if (statusApplied == false) {
            let statusArray = JSON.parse(JSON.stringify(_.where(this.udrfService.udrfData.filterTypeArray, { filterName: activeSummaryCard[0]['cardFilter'] })));
            mainFilterArray.push(statusArray[0])

            for (let mainFilterArrayItem of mainFilterArray) {
              if (mainFilterArrayItem.filterName == activeSummaryCard[0]['cardFilter']) {
                mainFilterArrayItem.multiOptionSelectSearchValues = [activeSummaryCard[0].dataTypeCode];    
                mainFilterArrayItem.multiOptionSelectSearchValuesWithId = [activeSummaryCard[0].dataTypeCode];      
                statusApplied = true;
              }
              else if(mainFilterArray.filterName == activeSummaryCard[0]['cardFilter']) {
                  mainFilterArrayItem.multiOptionSelectSearchValues = [activeSummaryCard[0].dataTypeCode];
                  mainFilterArrayItem.multiOptionSelectSearchValuesWithId = [activeSummaryCard[0].dataTypeCode];
                  statusApplied = true;
              }
            }
          }


          // let filterData = _.filter(res['messData'], function (status) {
        }
        else {
        
          console.log(this.udrfService.udrfData.filterTypeArray)
          mainFilterArray = JSON.parse(JSON.stringify(_.where(this.udrfService.udrfData.filterTypeArray, { filterName: activeSummaryCard[0]['cardFilter'] })));

          mainFilterArray[0].multiOptionSelectSearchValues = [activeSummaryCard[0].dataTypeCode];
          mainFilterArray[0].multiOptionSelectSearchValuesWithId = [activeSummaryCard[0].dataTypeCode];

        }

    }

    console.log("Fileter Array",mainFilterArray)
    let filterConfig = {
      startIndex: this.OpportunityItemDataCurrentIndex,
      startDate: this.udrfService.udrfData.mainApiDateRangeStart,
      endDate: this.udrfService.udrfData.mainApiDateRangeEnd,
      mainFilterArray: mainFilterArray,
      txTableDetails: this.udrfService.udrfData.txTableDetails,
      mainSearchParameter: this.udrfService.udrfData.mainSearchParameter,
      searchTableDetails: this.udrfService.udrfData.searchTableDetails
    };

    this.selectedCard = this.udrfService.udrfData.udrfSummaryCardCodes;
    

    this.opportunityService.getUDRFOpportunityList(filterConfig, this.udrfService.udrfUiData.isMyOppToggleChecked, this.roleService.getUserRoleOrgCodes("Opportunities")).pipe(takeUntil(this._onDestroy)).pipe(takeUntil(this._onAppApiCalled))
      .subscribe(
        async (res) => {

          if (res['messType'] == 'S' && res['messData'] && res['messData'].length > 0) {
            console.log(res['messData'])
            for(let result of res['messData'])
            {
                for(let [key, value] of Object.entries(result))
                {
                    if(typeof value == "object")
                    {
                        if(value!=null && value!=undefined)
                        {
                          try
                          {
                            if(value['type']=="date" && value['date'] != '-')
                            {      
                                result[key] = moment(value['date']).utc().format(this.dateFormat)
                                if(result[key] == 'Invalid date'){
                                  result[key] = '-'
                                }
                            }
                            if(value['type']=="date" && value['date'] == '-')
                            {     
                                  result[key] = '-'
                            }
             
                          }
                          catch(err)
                          {
                            console.log(err)
                          }
                        }
                    }
                } 
            }

              this.udrfService.udrfBodyData = this.udrfService.udrfBodyData.concat(res['messData']);

              if (!this.isCardClicked && this.OpportunityItemDataCurrentIndex ==0)
                this.initOpportunityCard()
            

          } else {

            this.udrfService.udrfBodyData = this.udrfService.udrfBodyData.concat([]);

            if (!this.isCardClicked && this.OpportunityItemDataCurrentIndex ==0)
              this.initOpportunityCard()

            this.udrfService.udrfData.noItemDataFound = true;
          }

       
          this.udrfService.udrfData.isItemDataLoading = false;

        }, (err) => {
          this.showErrorMessage(err);
        });


  }


  /**
     * @description Initialize Leads summary cards
     */
   async initOpportunityCard() {

    // this.wfhList = data;

    let filterConfig = {
      startIndex: this.OpportunityItemDataCurrentIndex,
      startDate: this.udrfService.udrfData.mainApiDateRangeStart,
      endDate: this.udrfService.udrfData.mainApiDateRangeEnd,
      mainFilterArray: this.udrfService.udrfData.mainFilterArray,
      txTableDetails: this.udrfService.udrfData.txTableDetails,
      mainSearchParameter: this.udrfService.udrfData.mainSearchParameter,
      searchTableDetails: this.udrfService.udrfData.searchTableDetails
    };
    for (let custom_date_range of this.udrfService.udrfData.filterTypeArray) {
      console.log('custom_rnage1')
      console.log(this.udrfService.udrfData)
      if (custom_date_range.isCustomButtonActivated && custom_date_range.hasOwnProperty('customButtonValueStart') && custom_date_range.hasOwnProperty('customButtonValueEnd')) {
        console.log('custom_rnage1')
        custom_date_range['multiOptionSelectSearchValues'] = ["Custom"]
        custom_date_range['multiOptionSelectSearchValuesWithId'] = [{
          startLimit: moment(custom_date_range['customButtonValueStart']).startOf('day').format(),
          endLimit: moment(custom_date_range['customButtonValueEnd']).endOf('day').format()
        }]
        custom_date_range['filterStartValue']=moment(custom_date_range['customButtonValueStart']).startOf('day').format()
        custom_date_range['filterEndValue']=moment (custom_date_range['customButtonValueEnd']).endOf('day').format()
        filterConfig['mainFilterArray'].push(custom_date_range);
        // this.initOpportunityCard()
      }
      
    }


    await this.opportunityService.getUDRFOpportunityCard(filterConfig, this.udrfService.udrfUiData.isMyOppToggleChecked,  this.roleService.getUserRoleOrgCodes("Opportunities")).then((res: any)=>{

        if (this.udrfService.udrfData.udrfSummaryCardCodes.length > 0) {
          console.log(this.udrfService.udrfData.udrfSummaryCardCodes)
          this.resolveVisibleDataTypeArray();
        }

        if (
          res['messType'] == 'S' &&
          res['messData'] &&
          res['messData'].length > 0
        ) {

          for (let i = 0; i < this.dataTypeArray.length; i++) {
            // this.dataTypeArray[i].isActive = false;
            this.dataTypeArray[i].dataTypeValue = "0";
          }
          this.dataTypeArray = this.dataTypeArray.map((item, row) => {
            const found = res["messData"].find(
              element => item.dataType == element.dataType
            );
            return { ...item, ...found };
          });



          this.udrfService.udrfUiData.summaryCards = this.dataTypeArray


          this.udrfService.udrfUiData.totalItemDataCount = res['total']


        }

        else {

          for (let i = 0; i < this.dataTypeArray.length; i++) {
            this.dataTypeArray[i].isActive = false;
            this.dataTypeArray[i].dataTypeValue = "0";
          }

          this.udrfService.udrfUiData.summaryCards = this.dataTypeArray

          this.udrfService.udrfUiData.totalItemDataCount = res['total']

        }


      }, err => {
        this.showErrorMessage(err);
      });




  }
    /**
     * 
     * @param err 
     * @description Displays error msg in snack bar
     */
    showErrorMessage(err) {

      let errReportingTeams = "KEBS";

      this.utilityService.showErrorMessage("Error:Unable to fetch Data", errReportingTeams);

    } 

    /**
   * @description Opens opporutnity in new tab when Right click open in new tab is clicked
   */
    openOpportunityPageinNewTab()
    {
        let cardData =  this.udrfService.udrfUiData.openinNewTabData['data']?this.udrfService.udrfUiData.openinNewTabData['data']:this.udrfService.udrfUiData.itemCardSelecteditem
        let navigationUrl = `${window.location.origin}/main/opportunities/${cardData['opportunity_id']}/`+this.utilityService.encodeURIComponent(cardData['opportunity_name'])+`/overview`;
        this.udrfService.udrfUiData.openinNewTabData={}
        window.open(navigationUrl);
    }
      /**
       * @description Opens Form in right Click when open PopUp button is clicked
       */
      async openEditScreen()
      {
        console.log("OpenEditScreen")
        let opportunityCardDetails =this.udrfService.udrfUiData.openModalData?this.udrfService.udrfUiData.openModalData:this.udrfService.udrfUiData.udrfUiOpenModalDataItem

        let userEditAccess = this.opportunityService.checkUserforEditAccess(opportunityCardDetails['sales_unit']);

          if(opportunityCardDetails!=undefined && userEditAccess)
          {
            console.log("OpenEditScreen1")
              this.opportunityService.getOpportunityAllField(opportunityCardDetails['opportunity_id']).subscribe(async res => {
                let isEditDisable = await this.opportunityService.isDisableEdit(opportunityCardDetails);
                this.isEditDisable = isEditDisable;
                if(!this.isEditDisable) {
                  console.log("OpenEditScreen3")
                const { CreateOpportunityComponent } = await import('src/app/modules/create-components/create-opportunity/create-opportunity.component');
        
                const createopportunityComponent = this.dialog.open(CreateOpportunityComponent, {
                  height: '100%',
                  width: '80%',
                  position: { right: '0px' },
                  data: { editDataForPatching: res[0],
                    mode:'Edit' },
                  disableClose: true
                }); 
                createopportunityComponent.afterClosed().subscribe(result => {
                  if (result == "updated") {
                    this.initReport();
                  }
                });
                }
               else
                {
                  this.utilityService.showMessage("Closed Won Opportunity Cannot be Edited!", 'dismiss')
                } })
          }
          else
          {
            this.utilityService.showMessage("Sorry, You don't have access to Edit!", 'dismiss')
          }
        
   
      }
    
      openOpportunity()
      {
          let opportunityId = this.udrfService.udrfUiData.itemCardSelecteditem["opportunity_id"];
          let opportunityName = this.udrfService.udrfUiData.itemCardSelecteditem["opportunity_name"];
          this.router.navigateByUrl('/main/opportunities/' + opportunityId +"/"+this.utilityService.encodeURIComponent(opportunityName));
      }


      /**
   * @description Gets Inline edit dropdown list
   */
  async dropdownInlineEditOpportunity()
  {
    this.udrfService.udrfUiData.inlineEditDropDownMasterDatas = {};
    
    this.udrfService.udrfUiData.inlineEditDropDownMasterDatas = {
      "opportunity-status":this.salesStatus,
      "opportunity-bid-type":this.bidType,
      "opportunity-service-type":this.serviceType,
      "opportunity-proposal-status":this.proposalStatus,
      "opportunity-proposal-type":this.proposalType,
      "opportunity-source":this.opportunitySource,
      "opportunity-probability":this.opportunityProbability
    };

  }

  /**
   * @description Lazy loading of lead list
   */
   onOpportunityListScrollDown(){
    if (!this.udrfService.udrfData.noItemDataFound) {

      this.OpportunityItemDataCurrentIndex += this.udrfService.udrfData.defaultRecordsPerFetch;

      console.log("Next",this.OpportunityItemDataCurrentIndex)

      this.udrfService.udrfData.isItemDataLoading = true;

      this.getOpportunityList();

    }
  }

  /**
   * @description Inline edit function
   */
   async callInlineEditApi(){

    let isEditDisable = await this.opportunityService.isDisableEdit(this.udrfService.udrfUiData.inlineEditData['dataSelected'])
    this.isEditDisable = isEditDisable;
    if(this.isEditDisable){
      this.utilityService.showMessage("Closed Won Opportunity Cannot be Edited!", 'Dismiss')
      return;
    }
    
    console.log("InlineEdit response data", this.udrfService.udrfUiData.inlineEditData)
    if (this.udrfService.udrfUiData.inlineEditData['hierarchyLevel'] == 'l1') {

      if (this.udrfService.udrfUiData.inlineEditData['inlineEditField'] == 'opportunity-probability' && !this.formField.transform('probability', this.formFieldData, 'isDisabled')) {
      
        if (this.udrfService.udrfUiData.inlineEditData['dataSelected']['probability'] != this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['id']) {


          this.opportunityService.updateOpportunityProbability(this.udrfService.udrfUiData.inlineEditData['dataSelected']['opportunity_id'], this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['id']).subscribe(async res => {

              
                    this.utilityService.showMessage("Probability Updated Successfully",
                      "Dismiss",
                      this.opportunityService.mediumInterval)
                  
                      let index = _.indexOf(this.udrfService.udrfBodyData,this.udrfService.udrfUiData.inlineEditData['dataSelected']);
                    this.udrfService.udrfBodyData[index]['probability'] = this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['id'];
                    this.udrfService.udrfBodyData[index]['probability_name'] = this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['name']
       
           

            }, err => {
              this.utilityService.showMessage(err, 'dismiss', this.opportunityService.mediumInterval);
            });
        }

      }
      else if(this.udrfService.udrfUiData.inlineEditData['inlineEditField'] == 'opportunity-source'  && !this.formField.transform('source', this.formFieldData, 'isDisabled')) {
      
        if (this.udrfService.udrfUiData.inlineEditData['dataSelected']['source'] != this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['id']) {


          this.opportunityService.updateOpportunitySource(this.udrfService.udrfUiData.inlineEditData['dataSelected']['opportunity_id'], this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['id']).subscribe(async res => {

              
                    this.utilityService.showMessage("Opportunity Source Updated Successfully",
                      "Dismiss",
                      this.opportunityService.mediumInterval)
                  
                      let index = _.indexOf(this.udrfService.udrfBodyData,this.udrfService.udrfUiData.inlineEditData['dataSelected']);
                    this.udrfService.udrfBodyData[index]['source'] = this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['id'];
                    this.udrfService.udrfBodyData[index]['opportunity_source_name'] = this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['name']
       
           

            }, err => {
              this.utilityService.showMessage(err, 'dismiss', this.opportunityService.mediumInterval);
            });
        }

      }
      else if(this.udrfService.udrfUiData.inlineEditData['inlineEditField'] == 'opportunity-service-type'  && !this.formField.transform('serviceType', this.formFieldData, 'isDisabled')) {
      
        if (this.udrfService.udrfUiData.inlineEditData['dataSelected']['service_type'] != this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['id']) {


          this.opportunityService.updateServiceType(this.udrfService.udrfUiData.inlineEditData['dataSelected']['opportunity_id'], this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['id']).subscribe(async res => {

              if(!res['err']){
                    this.utilityService.showMessage("Service Type Updated Successfully",
                      "Dismiss",
                      this.opportunityService.mediumInterval)
                    }
                    else{
                      this.utilityService.showMessage(res['msg'],
                       "Dismiss",
                        this.opportunityService.mediumInterval)
                        }
                  
                      let index = _.indexOf(this.udrfService.udrfBodyData,this.udrfService.udrfUiData.inlineEditData['dataSelected']);
                    this.udrfService.udrfBodyData[index]['service_type'] = this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['id'];
                    this.udrfService.udrfBodyData[index]['service_type_name'] = this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['name']
       
           

            }, err => {
              this.utilityService.showMessage(err, 'dismiss', this.opportunityService.mediumInterval);
            });
        }

      }
      else if(this.udrfService.udrfUiData.inlineEditData['inlineEditField'] == 'opportunity-proposal-type'  && !this.formField.transform('proposalType', this.formFieldData, 'isDisabled')) {
      
        if (this.udrfService.udrfUiData.inlineEditData['dataSelected']['proposal_type'] != this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['id']) {


          this.opportunityService.updateServiceType(this.udrfService.udrfUiData.inlineEditData['dataSelected']['opportunity_id'], this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['id']).subscribe(async res => {

            
                    this.utilityService.showMessage("Proposal Type Updated Successfully",
                      "Dismiss",
                      this.opportunityService.mediumInterval)
                  
                      let index = _.indexOf(this.udrfService.udrfBodyData,this.udrfService.udrfUiData.inlineEditData['dataSelected']);
                    this.udrfService.udrfBodyData[index]['proposal_type'] = this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['id'];
                    this.udrfService.udrfBodyData[index]['proposal_type_name'] = this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['name']
       
           

            }, err => {
              this.utilityService.showMessage(err, 'dismiss', this.opportunityService.mediumInterval);
            });
        }

      }
      else if(this.udrfService.udrfUiData.inlineEditData['inlineEditField'] == 'opportunity-proposal-status'  && !this.formField.transform('proposalStatus', this.formFieldData, 'isDisabled')) {
      
        if (this.udrfService.udrfUiData.inlineEditData['dataSelected']['proposal_status'] != this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['id']) {


          this.opportunityService.updateProposalStatus(this.udrfService.udrfUiData.inlineEditData['dataSelected']['opportunity_id'], this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['id']).subscribe(async res => {

              
                    // this.utilityService.showMessage("Proposal Status Updated Successfully",
                    //   "Dismiss",
                    //   this.opportunityService.mediumInterval)

                let label = this.formField.transform('proposalStatus', this.formFieldData, 'label');
                this.utilityService.showMessage(label + " Updated Successfully","Dismiss");
                  
                      let index = _.indexOf(this.udrfService.udrfBodyData,this.udrfService.udrfUiData.inlineEditData['dataSelected']);
                    this.udrfService.udrfBodyData[index]['proposal_status_id'] = this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['id'];
                    this.udrfService.udrfBodyData[index]['proposal_status_name'] = this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['name']
       
           

            }, err => {
              this.utilityService.showMessage(err, 'dismiss', this.opportunityService.mediumInterval);
            });
        }

      }
      else if(this.udrfService.udrfUiData.inlineEditData['inlineEditField'] == 'opportunity-bid-type') {
      
        if (this.udrfService.udrfUiData.inlineEditData['dataSelected']['bid_type_id'] != this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['id']) {


          this.opportunityService.updateProposalStatus(this.udrfService.udrfUiData.inlineEditData['dataSelected']['opportunity_id'], this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['id']).subscribe(async res => {

              
                    this.utilityService.showMessage("Bid Type Updated Successfully",
                      "Dismiss",
                      this.opportunityService.mediumInterval)
                  
                      let index = _.indexOf(this.udrfService.udrfBodyData,this.udrfService.udrfUiData.inlineEditData['dataSelected']);
                    this.udrfService.udrfBodyData[index]['bid_type_id'] = this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['id'];
                    this.udrfService.udrfBodyData[index]['bid_type'] = this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['name']
       
           

            }, err => {
              this.utilityService.showMessage(err, 'dismiss', this.opportunityService.mediumInterval);
            });
        }

      }
      else if(this.udrfService.udrfUiData.inlineEditData['inlineEditField'] == 'opportunity-status') {
   
        if (this.udrfService.udrfUiData.inlineEditData['dataSelected']['status_id'] != this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['id']) {
         let seqStatusCheck=await this.opportunityService.checkOpportunitySalesStatus(this.udrfService.udrfUiData.inlineEditData['dataSelected']['opportunity_id'],this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['id'])
            if(seqStatusCheck['messType'] == 'E') {
              return  this.utilityService.showMessage(seqStatusCheck['mesText'], 'Dismiss');
            } 
            let poValueCheck=await this.opportunityService.checkPoValueMatch(this.udrfService.udrfUiData.inlineEditData['dataSelected']['opportunity_id'],this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['id'])
            if(!poValueCheck["is_allowed"]){
              if(poValueCheck['type']=="WARN"){
              let copyConfirm = await this.utilityService.openConfirmationSweetAlertWithCustom(
                poValueCheck['textMsg'],
                "Do you wish to close the opportunity with Opportunity Value and PO Value difference?"
              );
        
              if (!copyConfirm) {
                return true;
              }
            }
            else{
              this.utilityService.showMessage(poValueCheck['textMsg'], 'Dismiss');
              return ;
            }
            }
            let opportunity_value_to_be_copied=await this.opportunityService.checkIssuePoAndPoValue(this.udrfService.udrfUiData.inlineEditData['dataSelected']['opportunity_id'],this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['id'],this.udrfService.udrfUiData.inlineEditData['dataSelected']['issue_po_id']);
            if(opportunity_value_to_be_copied["isApplicable"]){
              this.udrfService.udrfUiData.inlineEditData['dataSelected']["po_no"]=opportunity_value_to_be_copied['poNumber'] || '';
              this.udrfService.udrfUiData.inlineEditData['dataSelected']["po_date"]=opportunity_value_to_be_copied['poDate'] || '';
              this.udrfService.udrfUiData.inlineEditData['dataSelected']["po_value"]=opportunity_value_to_be_copied['poValue'] || '';
              // return ;
            }
          console.log(this.stagewiseFieldConfig)
          if(this.isFiscalYearCheck){
              let isBandBdata =  1
              await this.opportunityService.getBandBData(this.udrfService.udrfUiData.inlineEditData['dataSelected']['opportunity_id']).toPromise().then(
                (res) => {
                  if(res['messType'] == "S"){
                    isBandBdata = res['result'][0]['current_fy_uob_value']
                  }
                }
              ).catch(
                (err) => {
                  console.log(err)
                }
              )
              console.log(this.BandBArrayList.includes(this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['id']))
              console.log(this.BandBArrayList)
              console.log(this.BandBArrayList.includes(this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['id']) && isBandBdata == 0)
              if(this.BandBArrayList.includes(this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['id']) && isBandBdata == 0){
                this.utilityService.showMessage("Please fill Value to update status!","Dismiss")
                this.openEditScreen()
              }
              else{
                this.opportunityService.checkNdUpdateOpportunityStatus(this.udrfService.udrfUiData.inlineEditData['dataSelected']['status_id'], this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['id'], this.udrfService.udrfUiData.inlineEditData['dataSelected'] ,this.isQuoteEnabled).then(async res => {
       
                  if(res['messType']!="E")
                  { 
                      console.log("VAl Changes")
                      this.opportunityService.updateOpportunitySalesStatus(this.udrfService.udrfUiData.inlineEditData['dataSelected']['opportunity_id'],this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['id']).subscribe((res)=>{
                        if(res['messType'] == 'E') {
                          this.utilityService.showMessage(res['mesText'], 'Dismiss');
                        } else {
                          this.utilityService.showMessage("Status changed successfully","Dismiss")
                          let index = _.indexOf(this.udrfService.udrfBodyData,this.udrfService.udrfUiData.inlineEditData['dataSelected']);
                          this.udrfService.udrfBodyData[index]['status_id'] = this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['id'];
                          this.udrfService.udrfBodyData[index]['sales_status_name'] = this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['name'];

                          if (this.isStatusStageMappingAllowed) {
                            const proposalStatus = this.getProposalStatus(this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['id']);
                            this.udrfService.udrfBodyData[index]['proposal_status_id'] = proposalStatus.proposal_status_id;
                            this.udrfService.udrfBodyData[index]['proposal_status_name'] = proposalStatus.proposal_status_name;
                          }

                          this.opportunityService.checkAdaptTemplate(this.udrfService.udrfUiData.inlineEditData['dataSelected']['opportunity_id'],this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['id']).subscribe((res)=>{
                            if(res['messType'] == 'A') {
                              this.utilityService.openConfirmationSweetAlertWithCustom("Create Duplicate Activities?", "Do you wish to create Activities based on status template which is already available ?")
                              .then((copyConfirm) => {
              
                                if (copyConfirm)
                                 {
                                  this.opportunityService.adaptStatusTemplate(this.udrfService.udrfUiData.inlineEditData['dataSelected']['opportunity_id'],this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['id']).subscribe((res)=>{
                                    if(res['messType'] == 'S') {
                                      this.utilityService.showMessage('Activities created based on Opportunity status ', 'Dismiss');
                                    } else {
                                      this.utilityService.showMessage('Error while creating activities based on Opportunity status ', 'Dismiss');
                                   
                                    }
                                  })
                                 }
              
                                else{
                                  return true
                                }
              
                            })
                            } else if(res['messType'] == 'S'){
                              this.opportunityService.adaptStatusTemplate(this.udrfService.udrfUiData.inlineEditData['dataSelected']['opportunity_id'],this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['id']).subscribe((res)=>{
                                if(res['messType'] == 'S') {
                                  this.utilityService.showMessage('Activities created based on Opportunity status ', 'Dismiss');
                                } else {
                                  this.utilityService.showMessage('Error while creating activities based on Opportunity status ', 'Dismiss');
                               
                                }
                              })
                            }
                          })
                        }
                      })
                  }
    
                
                  let index = _.indexOf(this.udrfService.udrfBodyData,this.udrfService.udrfUiData.inlineEditData['dataSelected']);
                  this.udrfService.udrfBodyData[index]['bid_qualification_approval_status'] = res['bid_qualification_approval_status']
    
              })
              }
          }
          else if(this.stagewiseFieldConfig){

            await this.opportunityService.getStagewiseFormFieldConfig(this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['id']).then((res:any) => {
              this.tempstagewiseFormFieldData = JSON.parse(res.messData[0].stagewise_field_config)
              this.stagewiseFormFieldData=_.filter( this.tempstagewiseFormFieldData,function(item:any){
               return (item.changeIndicator==true && item.is_mandant)
              })
              console.log(this.stagewiseFormFieldData)
            },
              err => {
                console.log(err)
              })
              let mand_fields=false
              for (let item of this.stagewiseFormFieldData) {
                console.log(this.udrfService.udrfUiData.inlineEditData['dataSelected'][item.data_field])
                if(this.udrfService.udrfUiData.inlineEditData['dataSelected'][item.data_field]==null || this.udrfService.udrfUiData.inlineEditData['dataSelected'][item.data_field]==0 ||  this.udrfService.udrfUiData.inlineEditData['dataSelected']=="00:00:00 00:00:00")
              {
                mand_fields=true;
                break;
              }
              else if(item.data_field=="opportunity_value"){
                
                let opp_value=this.udrfService.udrfUiData.inlineEditData['dataSelected'][item.data_field]
                console.log(opp_value[0].value)
                if(opp_value[0].value==0)
                {
                  mand_fields=true;
                  break;
                }

              }

              }
              console.log(mand_fields)
                this.opportunityService.checkNdUpdateOpportunityStatus(this.udrfService.udrfUiData.inlineEditData['dataSelected']['status_id'], this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['id'], this.udrfService.udrfUiData.inlineEditData['dataSelected'] ,this.isQuoteEnabled).then(async res => {
           
                  if(res['messType']!="E")
                  { 
                      const updateAllowed = this.isQuoteEnabled ? await this.resolveQuoteOpportunityIntegration(this.udrfService.udrfUiData.inlineEditData['dataSelected']['opportunity_id'], this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['id']) : true;

                      if (updateAllowed) {
                        if(mand_fields==true){  
                          this.utilityService.showMessage("Please fill Value to update status!","Dismiss")
                          let selectedOpportunity=this.udrfService.udrfUiData.inlineEditData['dataSelected']
                          let changedStatus=this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['id']
                          this.openEditScreenOnStatusChange(selectedOpportunity,changedStatus) 
                        } else {
                          console.log("VAl Changes1")
                          let changed_status  = _.where(this.salesStatus,{id: this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['id']})
                          console.log(changed_status)
                          if(changed_status[0]['is_end_form_needed']==1){
                            if (changed_status[0]['id'] == 13 && this.udrfService.udrfUiData.inlineEditData['dataSelected']['project_id']) return this._toaster.showWarning('Cannot Change Status to Lost if Project Integrated', '', this.opportunityService.mediumInterval)
                          await this.opportunityService.openWinLossForm(this.udrfService.udrfUiData.inlineEditData['dataSelected'] , changed_status[0]['name']).then((res)=>{
                            if(res['messType'])
                            {
                              if(res!="" && res["result"]!="")
                              this.opportunityService.updateOpportunitySalesStatus(this.udrfService.udrfUiData.inlineEditData['dataSelected']['opportunity_id'],this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['id']).subscribe((res)=>{
                                if(res['messType'] == 'E') {
                                  this.utilityService.showMessage('Status can be updated only in sequential order', 'Dismiss');
                                } else {
                                  this.utilityService.showMessage("Status Changed successfully","Dismiss")
                                  let index = _.indexOf(this.udrfService.udrfBodyData,this.udrfService.udrfUiData.inlineEditData['dataSelected']);
                                  this.udrfService.udrfBodyData[index]['status_id'] = this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['id'];
                                  this.udrfService.udrfBodyData[index]['sales_status_name'] = this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['name']
                                  if (this.isStatusStageMappingAllowed) {
                                    const proposalStatus = this.getProposalStatus(this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['id']);
                                    this.udrfService.udrfBodyData[index]['proposal_status_id'] = proposalStatus.proposal_status_id;
                                    this.udrfService.udrfBodyData[index]['proposal_status_name'] = proposalStatus.proposal_status_name;
                                  }
                                  this.opportunityService.checkAdaptTemplate(this.udrfService.udrfUiData.inlineEditData['dataSelected']['opportunity_id'],this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['id']).subscribe((res)=>{
                                    if(res['messType'] == 'A') {
                                      this.utilityService.openConfirmationSweetAlertWithCustom("Create Duplicate Activities?", "Do you wish to create Activities based on status template which is already available ?")
                                      .then((copyConfirm) => {
                      
                                        if (copyConfirm)
                                         {
                                          this.opportunityService.adaptStatusTemplate(this.udrfService.udrfUiData.inlineEditData['dataSelected']['opportunity_id'],this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['id']).subscribe((res)=>{
                                            if(res['messType'] == 'S') {
                                              this.utilityService.showMessage('Activities created based on Opportunity status ', 'Dismiss');
                                            } else {
                                              this.utilityService.showMessage('Error while creating activities based on Opportunity status ', 'Dismiss');
                                           
                                            }
                                          })
                                         }
                      
                                        else{
                                          return true
                                        }
                      
                                    })
                                    } else if(res['messType'] == 'S'){
                                      this.opportunityService.adaptStatusTemplate(this.udrfService.udrfUiData.inlineEditData['dataSelected']['opportunity_id'],this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['id']).subscribe((res)=>{
                                        if(res['messType'] == 'S') {
                                          this.utilityService.showMessage('Activities created based on Opportunity status ', 'Dismiss');
                                        } else {
                                          this.utilityService.showMessage('Error while creating activities based on Opportunity status ', 'Dismiss');
                                       
                                        }
                                      })
                                    }
                                  })
                                }
                              })
                            }
                            else
                            {
                              if(res!="" && res["result"]){
          
                                this.utilityService.showMessage(res['result'],"Dismiss")
                               
                              }
                         
                            }
                          })}
                          else{
                            this.opportunityService.updateOpportunitySalesStatus(this.udrfService.udrfUiData.inlineEditData['dataSelected']['opportunity_id'],this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['id']).subscribe((res)=>{
                              if(res['messType'] == 'E') {
                                this.utilityService.showMessage('Status can be updated only in sequential order', 'Dismiss');
                              } else {
                                this.utilityService.showMessage("Status Changed successfully","Dismiss")
                                let index = _.indexOf(this.udrfService.udrfBodyData,this.udrfService.udrfUiData.inlineEditData['dataSelected']);
                                this.udrfService.udrfBodyData[index]['status_id'] = this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['id'];
                                this.udrfService.udrfBodyData[index]['sales_status_name'] = this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['name']
                                if (this.isStatusStageMappingAllowed) {
                                  const proposalStatus = this.getProposalStatus(this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['id']);
                                  this.udrfService.udrfBodyData[index]['proposal_status_id'] = proposalStatus.proposal_status_id;
                                  this.udrfService.udrfBodyData[index]['proposal_status_name'] = proposalStatus.proposal_status_name;
                                }
                                  this.opportunityService.checkAdaptTemplate(this.udrfService.udrfUiData.inlineEditData['dataSelected']['opportunity_id'],this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['id']).subscribe((res)=>{
                                    if(res['messType'] == 'A') {
                                      this.utilityService.openConfirmationSweetAlertWithCustom("Create Duplicate Activities?", "Do you wish to create Activities based on status template which is already available ?")
                                      .then((copyConfirm) => {
                      
                                        if (copyConfirm)
                                         {
                                          this.opportunityService.adaptStatusTemplate(this.udrfService.udrfUiData.inlineEditData['dataSelected']['opportunity_id'],this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['id']).subscribe((res)=>{
                                            if(res['messType'] == 'S') {
                                              this.utilityService.showMessage('Activities created based on Opportunity status ', 'Dismiss');
                                            } else {
                                              this.utilityService.showMessage('Error while creating activities based on Opportunity status ', 'Dismiss');
                                           
                                            }
                                          })
                                         }
                      
                                        else{
                                          return true
                                        }
                      
                                    })
                                    } else if(res['messType'] == 'S'){
                                      this.opportunityService.adaptStatusTemplate(this.udrfService.udrfUiData.inlineEditData['dataSelected']['opportunity_id'],this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['id']).subscribe((res)=>{
                                        if(res['messType'] == 'S') {
                                          this.utilityService.showMessage('Activities created based on Opportunity status ', 'Dismiss');
                                        } else {
                                          this.utilityService.showMessage('Error while creating activities based on Opportunity status ', 'Dismiss');
                                       
                                        }
                                      })
                                    }
                                  })
                              }
                            })
                          }


                        }
                      }
                  }
    
                
                  let index = _.indexOf(this.udrfService.udrfBodyData,this.udrfService.udrfUiData.inlineEditData['dataSelected']);
                  this.udrfService.udrfBodyData[index]['bid_qualification_approval_status'] = res['bid_qualification_approval_status']
    
              })
              
          }

          else {
            this.opportunityService.checkNdUpdateOpportunityStatus(this.udrfService.udrfUiData.inlineEditData['dataSelected']['status_id'], this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['id'], this.udrfService.udrfUiData.inlineEditData['dataSelected'] ,this.isQuoteEnabled).then(async res => {
       
              if(res['messType']!="E")
              { 
  
                const updateAllowed = this.isQuoteEnabled ? await this.resolveQuoteOpportunityIntegration(this.udrfService.udrfUiData.inlineEditData['dataSelected']['opportunity_id'], this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['id']) : true;

                if (updateAllowed){


                  let changed_status  = _.where(this.salesStatus,{id: this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['id']})
                  if (changed_status[0]['is_end_form_needed'] == 1) {
                    if (changed_status[0]['id'] == 13 && this.udrfService.udrfUiData.inlineEditData['dataSelected']['project_id']) return this._toaster.showWarning('Cannot Change Status to Lost if Project Integrated', '', this.opportunityService.mediumInterval)
                    await this.opportunityService.openWinLossForm(this.udrfService.udrfUiData.inlineEditData['dataSelected'], changed_status[0]['name']).then((res) => {
                      if (res['messType']) {
                        if (res != "" && res["result"] != "")
                          this.opportunityService.updateOpportunitySalesStatus(this.udrfService.udrfUiData.inlineEditData['dataSelected']['opportunity_id'], this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['id']).subscribe((res) => {
                            if (res['messType'] == 'E') {
                              this.utilityService.showMessage(res['mesText'], 'Dismiss');
                            } else {
                              this.utilityService.showMessage("Status Changed successfully", "Dismiss")
                              let index = _.indexOf(this.udrfService.udrfBodyData, this.udrfService.udrfUiData.inlineEditData['dataSelected']);
                              this.udrfService.udrfBodyData[index]['status_id'] = this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['id'];
                              this.udrfService.udrfBodyData[index]['sales_status_name'] = this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['name']
                              if (this.isStatusStageMappingAllowed) {
                                const proposalStatus = this.getProposalStatus(this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['id']);
                                this.udrfService.udrfBodyData[index]['proposal_status_id'] = proposalStatus.proposal_status_id;
                                this.udrfService.udrfBodyData[index]['proposal_status_name'] = proposalStatus.proposal_status_name;
                              }
                              this.opportunityService.checkAdaptTemplate(this.udrfService.udrfUiData.inlineEditData['dataSelected']['opportunity_id'], this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['id']).subscribe((res) => {
                                if (res['messType'] == 'A') {
                                  this.utilityService.openConfirmationSweetAlertWithCustom("Create Duplicate Activities?", "Do you wish to create Activities based on status template which is already available ?")
                                    .then((copyConfirm) => {

                                      if (copyConfirm) {
                                        this.opportunityService.adaptStatusTemplate(this.udrfService.udrfUiData.inlineEditData['dataSelected']['opportunity_id'], this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['id']).subscribe((res) => {
                                          if (res['messType'] == 'S') {
                                            this.utilityService.showMessage('Activities created based on Opportunity status ', 'Dismiss');
                                          } else {
                                            this.utilityService.showMessage('Error while creating activities based on Opportunity status ', 'Dismiss');

                                          }
                                        })
                                      }

                                      else {
                                        return true
                                      }

                                    })
                                } else if (res['messType'] == 'S') {
                                  this.opportunityService.adaptStatusTemplate(this.udrfService.udrfUiData.inlineEditData['dataSelected']['opportunity_id'], this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['id']).subscribe((res) => {
                                    if (res['messType'] == 'S') {
                                      this.utilityService.showMessage('Activities created based on Opportunity status ', 'Dismiss');
                                    } else {
                                      this.utilityService.showMessage('Error while creating activities based on Opportunity status ', 'Dismiss');

                                    }
                                  })
                                }
                              })
                            }
                          })
                      }
                      else {
                        if (res != "" && res["result"]) {

                          this.utilityService.showMessage(res['result'], "Dismiss")

                        }

                      }
                    })
                  }
                  else{
                    this.opportunityService.updateOpportunitySalesStatus(this.udrfService.udrfUiData.inlineEditData['dataSelected']['opportunity_id'],this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['id']).subscribe((res)=>{
                      if(res['messType'] == 'E') {
                        this.utilityService.showMessage(res['mesText'], 'Dismiss');
                      } else {
                        this.utilityService.showMessage("Status Changed successfully","Dismiss")
                        let index = _.indexOf(this.udrfService.udrfBodyData,this.udrfService.udrfUiData.inlineEditData['dataSelected']);
                        this.udrfService.udrfBodyData[index]['status_id'] = this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['id'];
                        this.udrfService.udrfBodyData[index]['sales_status_name'] = this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['name']
                        if (this.isStatusStageMappingAllowed) {
                          const proposalStatus = this.getProposalStatus(this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['id']);
                          this.udrfService.udrfBodyData[index]['proposal_status_id'] = proposalStatus.proposal_status_id;
                          this.udrfService.udrfBodyData[index]['proposal_status_name'] = proposalStatus.proposal_status_name;
                        }
                        this.opportunityService.checkAdaptTemplate(this.udrfService.udrfUiData.inlineEditData['dataSelected']['opportunity_id'],this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['id']).subscribe((res)=>{
                          if(res['messType'] == 'A') {
                            this.utilityService.openConfirmationSweetAlertWithCustom("Create Duplicate Activities?", "Do you wish to create Activities based on status template which is already available ?")
                            .then((copyConfirm) => {
            
                              if (copyConfirm)
                               {
                                this.opportunityService.adaptStatusTemplate(this.udrfService.udrfUiData.inlineEditData['dataSelected']['opportunity_id'],this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['id']).subscribe((res)=>{
                                  if(res['messType'] == 'S') {
                                    this.utilityService.showMessage('Activities created based on Opportunity status ', 'Dismiss');
                                  } else {
                                    this.utilityService.showMessage('Error while creating activities based on Opportunity status ', 'Dismiss');
                                 
                                  }
                                })
                               }
            
                              else{
                                return true
                              }
            
                          })
                          } else if(res['messType'] == 'S'){
                            this.opportunityService.adaptStatusTemplate(this.udrfService.udrfUiData.inlineEditData['dataSelected']['opportunity_id'],this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['id']).subscribe((res)=>{
                              if(res['messType'] == 'S') {
                                this.utilityService.showMessage('Activities created based on Opportunity status ', 'Dismiss');
                              } else {
                                this.utilityService.showMessage('Error while creating activities based on Opportunity status ', 'Dismiss');
                             
                              }
                            })
                          }
                        })  }
                    })

                  }

                }
              }

            
              let index = _.indexOf(this.udrfService.udrfBodyData,this.udrfService.udrfUiData.inlineEditData['dataSelected']);
              this.udrfService.udrfBodyData[index]['bid_qualification_approval_status'] = res['bid_qualification_approval_status']

          })
          }
      }
      

      

      
      }
      else if(this.udrfService.udrfUiData.inlineEditData['inlineEditField'] == 'Opportunity Name'  && !this.formField.transform('opportunityName', this.formFieldData, 'isDisabled')) {
        if (this.udrfService.udrfUiData.inlineEditData['dataSelected']['opportunity_name'] != this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['Opportunity Name']) {
          this.opportunityService.updateOpportunityName(this.udrfService.udrfUiData.inlineEditData['dataSelected']['opportunity_id'],this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['Opportunity Name'])
          .subscribe(async res => {
            this.utilityService.showMessage("Opportunity Name Updated Successfully!","Dismiss")
            let index = _.indexOf(this.udrfService.udrfBodyData,this.udrfService.udrfUiData.inlineEditData['dataSelected']);
            this.udrfService.udrfBodyData[index]['opportunity_name'] = this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['Opportunity Name'];
          }, err => {
              this.utilityService.showMessage(err,'dismiss');
          });
        }
      }




      else if (this.udrfService.udrfUiData.inlineEditData['inlineEditField'] == 'Start Date'  && !this.formField.transform('startDate', this.formFieldData, 'isDisabled')) {
        let formatedDate = moment(this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['Start Date']).format('YYYY-MM-DD');
        if (this.udrfService.udrfUiData.inlineEditData['dataSelected']['start_date'] != formatedDate) {

          this.opportunityService.updateOpportunityStartDate(this.udrfService.udrfUiData.inlineEditData['dataSelected']['opportunity_id'],formatedDate)
            .subscribe(async res => {
              let index = _.indexOf(this.udrfService.udrfBodyData,this.udrfService.udrfUiData.inlineEditData['dataSelected']);
              this.udrfService.udrfBodyData[index]['start_date'] = moment(formatedDate).format("DD - MMM - YYYY")
              this.utilityService.showMessage("Opportunity Start date Updated Successfully","Dismiss")
            }, err => {
              this.utilityService.showMessage(err, 'dismiss', this.opportunityService.mediumInterval);
            });
        }
      }

      else if (this.udrfService.udrfUiData.inlineEditData['inlineEditField'] == 'End Date'  && !this.formField.transform('endDate', this.formFieldData, 'isDisabled')) {
        let formatedDate = moment(this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['End Date']).format('YYYY-MM-DD');
        if (this.udrfService.udrfUiData.inlineEditData['dataSelected']['end_date'] != formatedDate) {

          this.opportunityService.updateOpportunityEndDate(this.udrfService.udrfUiData.inlineEditData['dataSelected']['opportunity_id'],formatedDate)
            .subscribe(async res => {
              let index = _.indexOf(this.udrfService.udrfBodyData,this.udrfService.udrfUiData.inlineEditData['dataSelected']);
              this.udrfService.udrfBodyData[index]['end_date'] = moment(formatedDate).format("DD - MMM - YYYY")
              this.utilityService.showMessage("Opportunity Closure date Updated Successfully","Dismiss")
            }, err => {
              this.utilityService.showMessage(err, 'dismiss', this.opportunityService.mediumInterval);
            });
        }
      }

   
      else if (this.udrfService.udrfUiData.inlineEditData['inlineEditField'] == 'Delivery Start Date') {
        let formatedDate = moment(this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['Delivery Start Date']).format('YYYY-MM-DD');
        if (this.udrfService.udrfUiData.inlineEditData['dataSelected']['deliveryStartDate'] != formatedDate) {
          console.log("formatedDate")
          this.opportunityService.updateOpportunityDeliveryStartDate(this.udrfService.udrfUiData.inlineEditData['dataSelected']['opportunity_id'],formatedDate)
            .subscribe(async res => {
              let index = _.indexOf(this.udrfService.udrfBodyData,this.udrfService.udrfUiData.inlineEditData['dataSelected']);
              this.udrfService.udrfBodyData[index]['deliveryStartDate'] = moment(formatedDate).format(this.dateFormat)
              let label = this.formField.transform('deliveryStartDate', this.formFieldData, 'label');
              this.utilityService.showMessage(label + " Updated Successfully","Dismiss")
            }, err => {
              this.utilityService.showMessage(err, 'dismiss', this.opportunityService.mediumInterval);
            });
        }
      }

      else if (this.udrfService.udrfUiData.inlineEditData['inlineEditField'] == 'Delivery Finish Date') {
        let formatedDate = moment(this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['Delivery Finish Date']).format('YYYY-MM-DD');
        if (this.udrfService.udrfUiData.inlineEditData['dataSelected']['deliveryFinishDate'] != formatedDate) {

          this.opportunityService.updateOpportunityDeliveryEndDate(this.udrfService.udrfUiData.inlineEditData['dataSelected']['opportunity_id'],formatedDate)
            .subscribe(async res => {
              let index = _.indexOf(this.udrfService.udrfBodyData,this.udrfService.udrfUiData.inlineEditData['dataSelected']);
              this.udrfService.udrfBodyData[index]['deliveryFinishDate'] = moment(formatedDate).format(this.dateFormat)
              let label = this.formField.transform('deliveryFinishDate', this.formFieldData, 'label');
              this.utilityService.showMessage(label + " Updated Successfully","Dismiss")
            }, err => {
              this.utilityService.showMessage(err, 'dismiss', this.opportunityService.mediumInterval);
            });
        }
      }   

      


      else if(this.udrfService.udrfUiData.inlineEditData['inlineEditField'] == 'RFP Date'  && !this.formField.transform('rfpDate', this.formFieldData, 'isDisabled')){
        let formatedDate = moment(this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['RFP Date']).format('YYYY-MM-DD');
        if (this.udrfService.udrfUiData.inlineEditData['dataSelected']['rfp_date'] != formatedDate) {
  
         this.opportunityService.updateOpportunityRFPReleaseDate(this.udrfService.udrfUiData.inlineEditData['dataSelected']['opportunity_id'],formatedDate)
            .subscribe(async res => {
                let index = _.indexOf(this.udrfService.udrfBodyData,this.udrfService.udrfUiData.inlineEditData['dataSelected']);
                this.udrfService.udrfBodyData[index]['rfp_date'] = moment(formatedDate).format("DD - MMM - YYYY")
                this.utilityService.showMessage("RFP release date Updated Successfully","Dismiss")
            }, err => {
                this.utilityService.showMessage(err,'dismiss');
            });
  
        }
      }
      else if(this.udrfService.udrfUiData.inlineEditData['inlineEditField'] ==  'Proposal Date'  && !this.formField.transform('proposalDate', this.formFieldData, 'isDisabled')){
        let formatedDate = moment(this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['Proposal Date']).format('YYYY-MM-DD');
        if (this.udrfService.udrfUiData.inlineEditData['dataSelected']['proposal_submission_date'] != formatedDate) {
        
         this.opportunityService.updateOpportunityProposalSubDate(this.udrfService.udrfUiData.inlineEditData['dataSelected']['opportunity_id'],formatedDate)
            .subscribe(async res => {
                let index = _.indexOf(this.udrfService.udrfBodyData,this.udrfService.udrfUiData.inlineEditData['dataSelected']);
                this.udrfService.udrfBodyData[index]['proposal_submission_date'] = moment(formatedDate).format("DD - MMM - YYYY")
                this.utilityService.showMessage("Submission to Customer date Updated Successfully","Dismiss")
            }, err => {
                this.utilityService.showMessage(err,'dismiss');
            });
        }
  
      }
      else if(this.udrfService.udrfUiData.inlineEditData['inlineEditField'] ==  'Proposal Sales Date'  && !this.formField.transform('proposalSubmissionDate', this.formFieldData, 'isDisabled')){
        let formatedDate = moment(this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['Proposal Sales Date']).format('YYYY-MM-DD');
        if (this.udrfService.udrfUiData.inlineEditData['dataSelected']['sales_proposal_date'] != formatedDate) {
        
         this.opportunityService.updateOpportunityProposalSalesDate(this.udrfService.udrfUiData.inlineEditData['dataSelected']['opportunity_id'],formatedDate )
            .subscribe(async res => {
                let index = _.indexOf(this.udrfService.udrfBodyData,this.udrfService.udrfUiData.inlineEditData['dataSelected']);
                this.udrfService.udrfBodyData[index]['sales_proposal_date'] = moment(formatedDate).format("DD - MMM - YYYY")
                this.utilityService.showMessage("Submission to Sales date Updated Successfully","Dismiss")
            }, err => {
                this.utilityService.showMessage(err,'dismiss');
            });
        }
  
      }

      else if (this.udrfService.udrfUiData.inlineEditData['inlineEditField'] == 'Sales Owner'  && !this.formField.transform('salesOwner', this.formFieldData, 'isDisabled')) {
      
        if (this.udrfService.udrfUiData.inlineEditData['dataSelected']['sales_owner_oid'] != this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['oid']) {


          this.opportunityService.updateSalesOwner(this.udrfService.udrfUiData.inlineEditData['dataSelected']['opportunity_id'], this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['oid']).subscribe(async res => {

              
                    this.utilityService.showMessage("Sales Owner Updated Successfully",
                      "Dismiss",
                      this.opportunityService.mediumInterval)
                  
                      let index = _.indexOf(this.udrfService.udrfBodyData,this.udrfService.udrfUiData.inlineEditData['dataSelected']);
                    this.udrfService.udrfBodyData[index]['sales_owner_oid'] = this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['oid'];
                    this.udrfService.udrfBodyData[index]['sales_owner_name'] = this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['name']
       
           

            }, err => {
              this.utilityService.showMessage(err, 'dismiss', this.opportunityService.mediumInterval);
            });
        }

      }
      else if(this.udrfService.udrfUiData.inlineEditData['inlineEditField'] == 'Opportunity Value'  && !this.formField.transform('opportunityValueMillion', this.formFieldData, 'isDisabled')) {
 
        console.log("Opportunity Value inlineedit")
        if (this.udrfService.udrfUiData.inlineEditData['dataSelected']['opportunity_currency_value'] != this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['Opportunity Value']) {
          this.opportunityService.updateOpportunityValue(this.udrfService.udrfUiData.inlineEditData['dataSelected']['opportunity_id'],this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['Opportunity Value'])
          .subscribe(async res => {
            if(res['messType']=="S"){
              this.utilityService.showMessage("Opportunity Value Updated Successfully!","Dismiss")
              let index = _.indexOf(this.udrfService.udrfBodyData,this.udrfService.udrfUiData.inlineEditData['dataSelected']);
              this.udrfService.udrfBodyData[index]['opportunity_currency_value'] = this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['Opportunity Value'];  
              this.udrfService.udrfBodyData[index]['opportunity_value']  = res["messData"]
            }
            else{
              this.utilityService.showMessage(res['messText'],"Dismiss")   
            }
           }, err => {
              this.utilityService.showMessage(err,'dismiss');
          });
        }
      }
      else if(this.udrfService.udrfUiData.inlineEditData['inlineEditField'] == 'Actual Closure Date'  && !this.formField.transform('actualClosureDate', this.formFieldData, 'isDisabled')){
        let formatedDate = moment(this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['Actual Closure Date']).format('YYYY-MM-DD');
        if (this.udrfService.udrfUiData.inlineEditData['dataSelected']['actual_closure_date'] != formatedDate) {
         console.log(this.udrfService.udrfUiData.inlineEditData['dataSelected'])
         this.opportunityService.updateOpportunityActualClosureDate(this.udrfService.udrfUiData.inlineEditData['dataSelected']['opportunity_id'],formatedDate,this.udrfService.udrfUiData.inlineEditData['dataSelected']['status_id'])
            .subscribe(async res => {
              if(res['messType']=="S"){
                let index = _.indexOf(this.udrfService.udrfBodyData,this.udrfService.udrfUiData.inlineEditData['dataSelected']);
                this.udrfService.udrfBodyData[index]['actual_closure_date'] = moment(formatedDate).format(this.dateFormat)
                this.utilityService.showMessage("Actual closure date Updated Successfully","Dismiss")
              }
              else if(res['messType']=="E"){
                this.utilityService.showMessage(res['messText'],
                "Dismiss",
                this.opportunityService.mediumInterval)
              }
            }, err => {
                this.utilityService.showMessage(err,'dismiss');
            });
  
        }
      }
      else{
        {
          this.utilityService.showMessage("Edit is disabled!", 'Dismiss')
          return;
        }
      }

    
    }
  }


  /**
   * @description Mark lead as sales qualified once star is clicked
   */
   onStarClick(){
    let data = this.udrfService.udrfUiData.starRatingData['data'];
    
   
    if (data.opportunity_fav == 0) 
    {
        this.opportunityService.addToMyOpportunities(data.opportunity_id).subscribe(res => {
          let index = _.indexOf(this.udrfService.udrfBodyData,data);
          this.udrfService.udrfBodyData[index]['opportunity_fav'] = 1
          this.udrfService.udrfBodyData[index]['to_display_opportunity_fav_result']="Unmark from favourite"
          this.udrfService.udrfBodyData[index]['opportunity_fav_result']=true
          this.utilityService.showMessage(res['message'], 'dismiss', this.opportunityService.mediumInterval);
        }, err => {
          console.error(err);
          this.utilityService.showMessage('Oops something went wrong!', 'dismiss', this.opportunityService.mediumInterval);
        })
    } 
    else if (data.opportunity_fav == 1) 
    {
        this.opportunityService.removeFromMyOpportunities(data.opportunity_id).subscribe(res => {
          let index = _.indexOf(this.udrfService.udrfBodyData,data);
          this.udrfService.udrfBodyData[index]['opportunity_fav'] = 0
          this.udrfService.udrfBodyData[index]['to_display_opportunity_fav_result']="Mark as favourite"
          this.udrfService.udrfBodyData[index]['opportunity_fav_result']=false
          this.utilityService.showMessage(res['message'], 'dismiss', this.opportunityService.mediumInterval);
        }, err => {
          console.error(err);
          this.utilityService.showMessage("Oops something went wrong!", 'dismiss', this.opportunityService.mediumInterval);
        })
    }
    
  }

  async viewHierarchyProduct()
  {

    let cardDetails =this.udrfService.udrfUiData.openModal1Data?this.udrfService.udrfUiData.openModal1Data:this.udrfService.udrfUiData.udrfUiOpenModal1DataItem

    let product_heirarchy = []
    let column_config = []
    let view_heirarchy_details: any={}
    await this.opportunityService.getProductHeirarchy().then((res:any)=>{
      product_heirarchy = res
    },(err)=>{
      console.log(err)
    })

    await this.opportunityService.getProductViewHierarchyLabel(36,"Opportunity").then((res: any)=>{
      if(res['messType']=="S")
      {
          column_config=res['result']
          view_heirarchy_details = res['result_details']
      }
    },(err)=>{
        console.log(err)
    })


    const { HeirarchyViewComponent } = 
    await import ( 'src/app/modules/shared-lazy-loaded-components/heirarchy-view/heirarchy-view.component' ) 
    const openCustomFormModalComponent = this.dialog.open ( HeirarchyViewComponent , 
      { height : '100%' ,
       width : '85%' , 
       position : {
          right : '0px' 
        }, 
       data : 
          { 
            heading : "Line of Business" + " - " + cardDetails['opportunity_name'] + " ("+cardDetails['opportunity_id']+")",
            heirarchy_data: product_heirarchy,
            column_config: column_config,
            application_object: view_heirarchy_details['application_object'],
            application_id: view_heirarchy_details['application_id'],
            patchData: (cardDetails['product_category_value']!=""&&cardDetails['product_category_value']!=null)?((typeof cardDetails['product_category_value']== 'string') ? JSON.parse(cardDetails['product_category_value']) : cardDetails['product_category_value']):[],
            returnAsArray: false,
            access:"edit"
            

          } 
        }
    )
    
    openCustomFormModalComponent.afterClosed()
        .subscribe(async (res:any)=>{
          if(res!=undefined)
          {
            console.log(res)
            if(res['messType']=="S")
            {
                this.opportunityService.updateProductCategory(res['data'],cardDetails['opportunity_id']).then((res)=>{
                    if(res['messType']=="S")
                    {
                      
                      this.utilityService.showMessage("Updated Successfully!","Dismiss")
                      this.initReport()
  
                    }
                },(err)=>{
                  this.utilityService.showMessage("Unable to update Product Category!","Dismiss")
                })
            }
          }
        })
  }

  callInlineEditFunctionMasterData(){
    this.opportunityService.getProposalType().then(
      (res: any) => {
        this.proposalStatus = res;
      },
      (err) => {
        console.log(err);
      }
    );
    this.opportunityService.getProposalTypeMaster().then(
      (res: any) => {
        this.proposalType = res;
      },
      (err) => {
        console.log(err);
      }
    );
    this.opportunityService.getOpportunityStatusSales().then(
      (res: any) => {
        this.salesStatus = res;
      },
      (err) => {
        console.log(err);
      }
    );

    this.opportunityService.getbidType().subscribe(
      (res: any) => {
        this.bidType = res;
      },
      (err) => {
        console.log(err);
      }
    );

    this.opportunityService.getServiceType().subscribe(
      (res: any) => {
        this.serviceType = res;
      },
      (err) => {
        console.log(err);
      }
    );

    this.opportunityService.getLeadSource().subscribe(
      (res: any) => {
        this.opportunitySource = res;
      },
      (err) => {
        console.log(err);
      }
    );

    this.opportunityService.getProbabilityMaster().subscribe(
      (res: any) => {
        this.opportunityProbability = res;
      },
      (err) => {
        console.log(err);
      }
    );
    
  }

  ngOnDestroy() {

    this._onDestroy.next();
    this._onDestroy.complete();


    this.udrfService.resetUdrfData();

  }

  moveToSHSCompleted(){
    let cardDetails =this.udrfService.udrfUiData.submitButtonData?this.udrfService.udrfUiData.submitButtonData:this.udrfService.udrfUiData.udrfUisubmitButtonData;
    if (cardDetails['sales_status_id'] == 17) {
      this.utilityService.showMessage("SHS Completed Already!", "Dismiss");
    } else {
      this.utilityService.openConfirmationSweetAlertWithCustom("Completing SHS will reflect in Actual OBV.","Do you want to Proceed?").then((deleteConfirm) => {
        if (deleteConfirm) {
          
          this.opportunityService.moveToSHSCompleted(cardDetails['opportunity_id']).subscribe(res => {
            console.log(res);

            this.initReport()
            this.utilityService.showMessage("SHS Completed Successfully!", "Dismiss");
          }, err => {
            console.log(err);
            this.utilityService.showMessage("Failed To Complete SHS!", "Dismiss");
          })
        }
      })

    }
  }


  /**
     *  
     * @description Opens winloss form based on form id 
     * @returns 
     */
   async openWinLossForm(){
    let cardDetails =this.udrfService.udrfUiData.submitButtonData?this.udrfService.udrfUiData.submitButtonData:this.udrfService.udrfUiData.udrfUisubmitButtonData;
    let isFormAval : boolean
    let form_details 
    if(cardDetails['win_loss_form']!=null && cardDetails['win_loss_form'] !=''){

      form_details = cardDetails['win_loss_form']
      isFormAval = true
    }
    else{
      await this.opportunityService.getWinLossForm(cardDetails['opportunity_id']).then((res:any)=>{
        console.log(res)
        if(res.messType=="E"){
          this.utilityService.showMessage("Form Id not found", 'dismiss', this.opportunityService.mediumInterval);
          isFormAval = false
        }
        else if(res.messType=="S"){
          form_details = res.data.form_id
          isFormAval = true
          this.opportunityService.updateWinLossForm(form_details,cardDetails['opportunity_id']);
        }
      })
    }

    if(!isFormAval){

      this.moveToSHSCompleted();
      return;
    }

      const { CustomFormModalComponent } = 
      await import ( 'src/app/modules/shared-lazy-loaded-components/custom-form-modal/custom-form-modal.component' ) 
      const openCustomFormModalComponent = this.dialog.open ( CustomFormModalComponent , 
        { height : '100%' ,
         width : '75%' , 
         position : {
            right : '0px' 
          }, 
         data : 
            { 
              formId : form_details, 
              isEdit :true, 
              entryForId : (cardDetails['opportunity_id']).toString(),
              formName: "Win-Loss Form"
            } 
          }
      )
      
      openCustomFormModalComponent.afterClosed()
          .subscribe(async (res:any)=>{
            if(res!=undefined)
            {
              if(res.messType=='S'){
                console.log("Success")
                this.opportunityService.updateWinLossForm(form_details,cardDetails['opportunity_id']);
              }
            }

            this.moveToSHSCompleted();
            
          })


      
    }
  openAdminSettingsReport(){
    this.router.navigateByUrl("/main/opportunities/opportunitiesHome/opportunitySettings")
  }

  async openAdditionalView() {
    console.log('sdsa')
    this.pipelineViewEnabled = !this.pipelineViewEnabled;

    if (this.pipelineViewEnabled == false) 
    { 
      this.initReport();
    }

  }

  getPipelineView=async(event)=>{
    console.log(event)

    let mainFilterArray =[]
    let add_filter = _.where(this.udrfService.udrfData.mainFilterArray,{filterName:"Pipeline"})
    if(add_filter.length>0)
    {
        mainFilterArray = _.reject(this.udrfService.udrfData.mainFilterArray,m_filter => {
          return (m_filter['filterName']=="Pipeline");
        })
    }

    for(let additionalFilter of this.udrfService.udrfData.filterTypeArray)
    {
        

          if(additionalFilter['filterName']=="Pipeline")
          {
    
            additionalFilter['multiOptionSelectSearchValues']=event['Pipeline']
            mainFilterArray.push(additionalFilter)
    
          }
        
    }
    

    this.udrfService.udrfData.mainFilterArray = mainFilterArray
    

    await this.getOpportunityList();
  }

  downloadOpportunityReport()
  { 
    this.udrfService.udrfUiData.isReportDownloading = true;

    let filterConfig = {
      startIndex: this.OpportunityItemDataCurrentIndex,
      startDate: this.udrfService.udrfData.mainApiDateRangeStart,
      endDate: this.udrfService.udrfData.mainApiDateRangeEnd,
      mainFilterArray: this.udrfService.udrfData.mainFilterArray,
      txTableDetails: this.udrfService.udrfData.txTableDetails,
      mainSearchParameter: this.udrfService.udrfData.mainSearchParameter,
      searchTableDetails: this.udrfService.udrfData.searchTableDetails
    };


    this.opportunityService.getUDRFOpportunityDownloadReport(filterConfig, this.udrfService.udrfUiData.isMyOppToggleChecked, this.roleService.getUserRoleOrgCodes("Opportunities")).pipe(takeUntil(this._onDestroy)).pipe(takeUntil(this._onAppApiCalled))
      .subscribe(async res => {

        const reportData=res['messData']
        let dateFieldArray=[]

        if (res['messType'] == 'S') {

          for (let result of reportData) {
            for (let [key, value] of Object.entries(result)) {
              if (typeof value == "object") {
                if (value != null && value != undefined) {
                  try {
                    if (value['type'] == "date") {



                      const fieldExists = dateFieldArray.find(val => val['fieldKey'] == key);

                      if (!fieldExists)
                        dateFieldArray.push({
                          fieldKey: key,
                          fieldFormat: this.dateFormat
                        });

                      result[key] = value['value'] ? moment(value['value']).utc().format("YYYY/MM/DD") : null;

                      // dateFieldArray.push({fieldKey:key,fieldFormat:this.dateFormat})

                      // result[key] = moment(value['date']).utc().format("YYYY-MM-DD")

                    }
                  }
                  catch (err) {
                    console.log(err)
                  }
                }
              }
            }

          }

          this.exceltoJson.exportAsExcelFile(reportData, res['fileName'], [], null,dateFieldArray)


        }

        else {

         this.utilityService.showMessage("Unable to download report","Dismiss")

        }

        this.udrfService.udrfUiData.isReportDownloading = false;


      }, err => {
        this.showErrorMessage(err);
      });
  }
  deactivateOpportunity = () => {
    if(!this.editAccess){
      this.utilityService.showMessage( "Delete is restricted", 'Dismiss');
      return;
    }
    let data = this.udrfService.udrfUiData.openDeleteButtonData['data']
    console.log(data)
    this.utilityService.openConfirmationSweetAlertWithCustom("Are you sure you want to delete ?", "Once you confirm, this Opportunity will be deleted !").
    then(async (res) => {
      if (res) {
        this.opportunityService.deactivateOpportunity(data['opportunity_id']).then(
          (res) => {
            if(res['messType'] == "S"){
              this.utilityService.showMessage("Opportunity deleted Successfully!","Dismiss")
              this.initReport();
            }
          }
         ).catch(
          (err) => {
            console.log(err)
          }
         )
      }
    });
    

   }

  resolveQuoteOpportunityIntegration = (opportunityId, currentStatus) => {

    return new Promise((resolve, reject) => {
      
      this._quoteService.resolveQuoteOppIntegration(opportunityId, currentStatus, 1)
      .pipe(takeUntil(this._onDestroy))
      .subscribe(res => {

        if (res['messType'] == "S") {

            if (res['quoteMessageType']) {

              if (res['quoteMessageType'] == 'info')
                this._toaster.showInfo("Quote Info", res['messText'], this.opportunityService.longInterval);

              else if (res['quoteMessageType'] == 'warning')
                this._toaster.showWarning("Quote Warning", res['messText'], this.opportunityService.longInterval);

              else if (res['quoteMessageType'] == 'error')
                this._toaster.showError("Quote Error", res['messText'], this.opportunityService.mediumInterval);

            }

            if (res.hasOwnProperty('allowStatusChange') && res['allowStatusChange'] == false)
              resolve(false);

            if (res.hasOwnProperty('showConfirmationPopup') && res['showConfirmationPopup'] == 1 && res['quoteId'])
              this.utilityService.openConfirmationSweetAlertWithCustom("Copy Quote Value?", "Do you wish to copy Quote value to Opportunity ?")
                .then((copyConfirm) => {

                  if (copyConfirm)
                    this._quoteService.updateValueInOpportunity(opportunityId, res['quoteId'])
                      .pipe(takeUntil(this._onDestroy))
                      .subscribe(res => {

                        if (res['messType'] == "S")
                          this._toaster.showInfo("Value Updated", res["messText"], this.opportunityService.longInterval);

                        else
                          this._toaster.showError("Error in Updating", res["messText"], this.opportunityService.mediumInterval);

                        resolve(true);

                      },
                        err => {
                          console.log(err);
                          this._toaster.showError("Error", "Error in Updating Quote value in Opportunity", this.opportunityService.mediumInterval);
                          reject(false);
                        });

                  else
                    resolve(true);

              });

            else
              resolve(true);
            
        }

        else {

          this._toaster.showError("Error", res["messText"], this.opportunityService.mediumInterval);

          resolve(true);

        }

      },
        err => {
          console.log(err);
          this._toaster.showError("Error", "Error in Checking Quote Configuration", this.opportunityService.mediumInterval);
          reject(false);
        });

    });
  }

  async toggleMyOpp() {
    this.udrfService.udrfUiData.isMyOppToggleChecked = !this.udrfService.udrfUiData.isMyOppToggleChecked;
    this.udrfService.udrfData.noItemDataFound = false;
    this.initReport();
    await this.updateGeneralConfigUserLevel('isMyOppToggleChecked', this.udrfService.udrfUiData.isMyOppToggleChecked);
  }

  async updateGeneralConfigUserLevel(config: string, value: any): Promise<void> {
    try {
      const res: any = await this.opportunityService.updateGeneralConfigUserLevel(config, value);
      if (res?.messType === 'S' && config === 'OPP_CURRENCY') 
        this._toaster.showInfo('Currency Updated Successfully', '', 1500);
      
    } catch (err) {
      this._toaster.showError('Error updating user config', '', 1500);
      console.error("Error updating user config:", err);
    }
  }

  async checkGeneralConfigUserLevel(config: string): Promise<void> {
    try {
      const res: any = await this.opportunityService.checkGeneralConfigUserLevel(config);
      const value = res?.result?.[0]?.value;

      if (res?.messType === 'S' && value !== undefined) {
        if (config === 'isMyOppToggleChecked') this.isMyOppToggleChecked = value;
        if (config === 'OPP_CURRENCY') {
          this.OPP_CURRENCY = value ?? null;
          return this.OPP_CURRENCY
        }
      }
    } catch (err) {
      this._toaster.showError('Error retrieving user config', '', 1500);
      console.error("ERROR with general user config:", err);
    }
  }  

  async openEditScreenOnStatusChange(selectedOpportunity,changedStatus)
  {
    console.log("OpenEditScreen")
    let opportunityCardDetails =selectedOpportunity
    console.log(opportunityCardDetails)
    console.log(this.udrfService.udrfUiData.openModalData)
    console.log(this.udrfService.udrfUiData.udrfUiOpenModalDataItem)
    console.log(opportunityCardDetails)
    console.log(opportunityCardDetails['opportunity_id'])
    let editAccess = this.opportunityService.checkUserforEditAccess(opportunityCardDetails['sales_unit']);

      if(opportunityCardDetails!=undefined && editAccess)
      {
        console.log("OpenEditScreen1")
          this.opportunityService.getOpportunityAllField(opportunityCardDetails['opportunity_id']).subscribe(async res => {
            let isEditDisable = await this.opportunityService.isDisableEdit(opportunityCardDetails);
            this.isEditDisable = isEditDisable;
            if(!this.isEditDisable) {
              console.log("OpenEditScreen3")
              
              res[0].status_id=changedStatus;
              console.log(res[0])
            const { CreateOpportunityComponent } = await import('src/app/modules/create-components/create-opportunity/create-opportunity.component');
    
            const createopportunityComponent = this.dialog.open(CreateOpportunityComponent, {
              height: '100%',
              width: '75%',
              position: { right: '0px' },
              data: { editDataForPatching: res[0],
                mode:'Edit' },
                disableClose: true
            });
            createopportunityComponent.afterClosed().subscribe(result => {
              if (result == "updated") {
                this.initReport();
              }
            });
            }
           else
            {
              this.utilityService.showMessage("Closed Won Opportunity Cannot be Edited!", 'dismiss')
            } })
      }
      else
      {
        this.utilityService.showMessage("Sorry, You don't have access to Edit!", 'dismiss')
      }
    
    }
  async checkEditAcess()
  {
    let adminAccess = _.where(this.roleService.roles, { application_id: 36, role_id: 1}); 
    let editAccess = _.where(this.roleService.roles, { application_id: 36, object_id:6 ,operation: "*"});  
    console.log("accessList",editAccess)
    if (editAccess.length > 0){
      return true;
    }     
    else{
      return false;  
    }
  }

  callInlineEditApiRestricted(){
    this.utilityService.showMessage("Edit is restricted","Dismiss")
     
  }

  downloadQMPReport = () => {

    if (this.isQuoteEnabled) {

      this._quoteService.getMonthlyRevenueProjection()
        .pipe(takeUntil(this._onDestroy))
        .subscribe(res => {

          if (res['messType'] == "S" && res['data'] && res['data'].length) {

            const revenueData = res['data'];

            let dateFormatArr = [];

            for (const item of revenueData)
              for (const [key, value] of Object.entries(item)) {

                if (value && typeof value == 'object' && value['type'] && value['type'] == 'date') {

                  const fieldExists = dateFormatArr.find(val => val['fieldKey'] == key);

                  if (!fieldExists)
                    dateFormatArr.push({
                      fieldKey: key,
                      fieldFormat: this.dateFormat
                    });

                  item[key] = value['value'] ? moment(value['value']).utc().format("YYYY/MM/DD") : null;

                }

              }

            this.exceltoJson.exportAsExcelFile(revenueData, res['fileName'], [], null, dateFormatArr);

          }

          else
            this._toaster.showError("Error", res["messText"], this.opportunityService.mediumInterval);

        },
        err => {
          console.log(err);
          this._toaster.showError("Error", "Error in Downloading Monthly projection data from Quote", this.opportunityService.mediumInterval);
        });

    }

  }


  downloadSBHBReport = () => {

    if (this.isQuoteEnabled) {

      this._quoteService.getSBHBProjection()
        .pipe(takeUntil(this._onDestroy))
        .subscribe(res => {

          if (res['messType'] == "S" && res['data'] && res['data'].length) {

            const revenueData = res['data'];

            let dateFormatArr = [];

            for (const item of revenueData)
              for (const [key, value] of Object.entries(item)) {

                if (value && typeof value == 'object' && value['type'] && value['type'] == 'date') {

                  const fieldExists = dateFormatArr.find(val => val['fieldKey'] == key);

                  if (!fieldExists)
                    dateFormatArr.push({
                      fieldKey: key,
                      fieldFormat: this.dateFormat
                    });

                  item[key] = value['value'] ? moment(value['value']).utc().format("YYYY/MM/DD") : null;

                }

              }

            this.exceltoJson.exportAsExcelFile(revenueData, res['fileName'], [], null, dateFormatArr);

          }

          else
            this._toaster.showError("Error", res["messText"], this.opportunityService.mediumInterval);

        },
        err => {
          console.log(err);
          this._toaster.showError("Error", "Error in Downloading Revenue projection data", this.opportunityService.mediumInterval);
        });

    }

  }

  async getGlobalCRMDateFormat() {
    this.udrfServices.getCRMGlobalDateFormat().subscribe(
        (response: any) => {
            if (response.messType === 'S' && response?.data && response.data.length > 0) {
                const config = JSON.parse(response.data[0].config);
                this.dateFormat = config.format || this.dateFormat;
            }
        },
        (error) => {
            console.error("Error retrieving date format", error);
        }
    );
}

  openProjectDetails() {

    let project_id = this.udrfService.udrfUiData.itemCardSelecteditem["project_id"];
    let project_name = this.udrfService.udrfUiData.itemCardSelecteditem["project_name"];
    let project_item_id = this.udrfService.udrfUiData.itemCardSelecteditem["project_item_id"];
    let item_name = this.udrfService.udrfUiData.itemCardSelecteditem["item_name"];

    let navigationUrl =
      window.location.origin +
      '/main/project-management/' +
      project_id +
      '/' +
      this.utilityService.encodeURIComponent(project_name) +
      '/' +
      project_item_id +
      '/' +
      this.utilityService.encodeURIComponent(item_name)
    window.open(navigationUrl);

  }
  
  /**
* @description Determines if a field should be restricted based on key and resource_type.
* @param key - The field key to check.
* @returns True if the field should be restricted, otherwise false.
*/
  checkFieldShouldbeRestricted(fieldKey: string): boolean {
    const currentUserRole = this._ticket.getCurrentUserRole();
    const field = this.quote_field_config.find(item => item.key === fieldKey);
    return !(field?.role_access_restriction && field.role_access_restriction.includes(currentUserRole));
  }

  //Used for Toggle
  changeView() {
    if (this.udrf) this.udrf = false
    else this.udrf = true
  }

  redirectOV2() {
    this.router.navigateByUrl('/main/opportunities/opportunitiesHome/static-report');
  }
}

