import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';

@Component({
  selector: 'app-opportunity-settings',
  templateUrl: './opportunity-settings.component.html',
  styleUrls: ['./opportunity-settings.component.scss']
})
export class OpportunitySettingsComponent implements OnInit {
  navList : any 
  selectedItemId : any
  constructor(private router:Router) { }

  ngOnInit(): void {
    this.router.navigateByUrl("/main/opportunities/opportunitiesHome/opportunitySettings/addQualifierForm")
    this.selectedItemId = 1;
    this.navList=[
      {
        id:1,
        label:'Qualifier form',
        icon:'fact_check',
        link:"addQualifierForm",
        display:true
      },
      {
        id:2,
        label:'Governance',
        icon:'gavel',
        link:"governance",
        display:true
      },
      {
        id:3,
        label:'Presales review form',
        icon:'rate_review',
        link:'presalesForm',
        display:true
      },
      { 
        id:4,
        label:'Win-Loss analysis form',
        icon:'thumbs_up_down',
        link:'winLossForm',
        display:true
      },
      // {
      //   id:5,
      //   label:'Add bot',
      //   icon:'add'
      // },
      {
        id:5,
        label:'Opportunity Template Types',
        icon:'tune',
        link:'templateType',
        display:true
      },
      {
        id:6,
        label:'Opportunity Forms',
        icon:'insert_chart',
        link:'opportunityForm',
        display:true
      },
      {
        id:7,
        label:'Hubspot Sync',
        icon:'insert_chart',
        link:'hubspot',
        display:true
      },
      {
        id:8,
        label:'Status Settings',
        icon:'insert_chart',
        link:'status',
        display:true
      },
      {
        id:9,
        label:'Stagewise Config',
        icon:'insert_chart',
        link:'stagewiseConfig',
        display:true
      },
      {
        id:10,
        label:'Opportunity Form Field Config',
        icon:'insert_chart',
        link:'opportunityFeildConfig',
        display:true
      },
      {
        id:11,
        label:'Account Form Field Config',
        icon:'insert_chart',
        link:'accountFeildConfig',
        display:true
      },
      {
        id:12,
        label:'Audit notification Config',
        icon:'insert_chart',
        link:'auditNotificationConfig',
        display:true
      },
      {
        id:13,
        label:'Activity Template Config',
        icon:'insert_chart',
        link:'activityTemplate',
        display:true
      },
      {
        id:14,
        label:'Exit Employee Handling',
        icon:'insert_chart',
        link:'exitEmployeeAllocate',
        display:true
      }
    ]
  }


  clearSearchText(){

  }
  selectedSettings(id){
    console.log(id)
    this.selectedItemId = id
  }
}
