import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { EMPTY, identity } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { ErrorService } from 'src/app/services/error/error.service';
import { RolesService } from 'src/app/services/acl/roles.service';
import * as _ from 'underscore';
import { TenantService } from 'src/app/services/tenant-service/tenant.service';
@Injectable({
  providedIn: 'root',
})
export class OpportunityService {
  constructor(
    private http: HttpClient,
    private errorService: ErrorService,
    private _roleService: RolesService,
    private tenantService: TenantService
  ) {}

  saveReportState(state, name, field_conf, application_id) {
    return this.http
      .post('api/userExperience/saveReportState', {
        state: state,
        name: name,
        field_conf: field_conf,
        application_id: application_id,
      })
      .pipe(
        catchError((err) => {
          console.log(err);
          this.errorService.userErrorAlert(
            err && err.code
              ? err.code
              : err && err.error
              ? err.error.code
              : 'NIL',
            'Error while retrieving Column info list',
            err && err.params
              ? err.params
              : err && err.error
              ? err.error.params
              : {}
          );
          return EMPTY;
        })
      );
  }
  updateReportState(state, customization_id, field_conf, application_id) {
    return this.http
      .post('api/userExperience/updateReportState', {
        application_id,
        state: state,
        customization_id: customization_id,
        field_conf: field_conf,
      })
      .pipe(
        catchError((err) => {
          console.log(err);
          this.errorService.userErrorAlert(
            err && err.code
              ? err.code
              : err && err.error
              ? err.error.code
              : 'NIL',
            'Error while retrieving Column info list',
            err && err.params
              ? err.params
              : err && err.error
              ? err.error.params
              : {}
          );
          return EMPTY;
        })
      );
  }

  updateApprovalDetailInline = (activityId, details) => {
    return this.http.post('/api/activity/editApprovalInline', {
      approvalFormDetails: details,
      activityId: activityId,
    });
  };

  addToMyOpportunities = (id) => {
    return this.http.post('/api/opportunity/addToMyOpportunities', {
      opportunityId: id,
    });
  };

  insertComment = (comment, activityId) => {
    return this.http.post('/api/activity/insertComment', {
      comment: comment,
      activityId: activityId,
    });
  };

  removeFromMyOpportunities = (oppId) => {
    return this.http.post('/api/opportunity/removeFromMyOpportunitiesIds', {
      opportunityId: oppId,
    });
  };

  getMyOpportunityIds = () => {
    return this.http.post('/api/opportunity/getMyOpportunitiesIds', {});
  };

  updateCheckListInline = (activityId, details) => {
    return this.http.post('/api/activity/editCheckListInline', {
      checklistFormDetails: details,
      activityId: activityId,
    });
  };

  getActivityFilterMaster = (id) => {
    return this.http.post(
      '/api/opportunity/opportunityActivityFilterMasterData',
      {
        applicationReferenceId: id,
      }
    );
  };

  getActivitySearchResults = (id, text) => {
    return this.http.post(
      '/api/opportunity/getActivityListForOpportunitySearch',
      {
        applicationReferenceId: id,
        searchParameter: text,
      }
    );
  };

  getActivityFilteredData = (id, data) => {
    return this.http.post('/api/opportunity/opportunityActivityFilter', {
      applicationReferenceId: id,
      filterData: data,
    });
  };

  getActivityNotes = (activityId) => {
    return this.http.post('/api/activity/viewNotes', {
      activityId: activityId,
    });
  };

  saveActivityNote = (activityId, title, noteColor, content) => {
    return this.http.post('/api/activity/saveActivityNote', {
      activityId: activityId,
      title: title,
      color: noteColor,
      content: content,
    });
  };

  deleteActivityNote = (activityId, noteId) => {
    return this.http.post('/api/activity/deleteOppActivityNote', {
      activityId: activityId,
      noteId: noteId,
    });
  };

  updateActivityNote = (
    activityId,
    noteId,
    noteTitle,
    noteContent,
    noteColor
  ) => {
    return this.http.post('/api/activity/updateOppActivityNote', {
      activityId: activityId,
      noteId: noteId,
      noteTitle: noteTitle,
      noteContent: noteContent,
      noteColor: noteColor,
    });
  };

  updateTaskDetailInline = (activityId, details) => {
    return this.http.post('/api/activity/editTaskInline', {
      taskFormDetails: details,
      activityId: activityId,
    });
  };

  getActivityComments = (activityId) => {
    return this.http.post('/api/activity/retrieveActivityComments', {
      activityId: activityId,
    });
  };

  getActivityAttachments = (activityId) => {
    return this.http.post('/api/activity/retrieveActivityAttachments', {
      activityId: activityId,
    });
  };

  updateActivityAttachment(activityId, file) {
    return this.http.post('/api/activity/updateActivityAttachment', {
      activity_id: activityId,
      file: file,
    });
  }
  deleteActivityAttachment(activityId, file) {
    return this.http.post('/api/activity/deleteActivityAttachment', {
      activity_id: activityId,
      file: file,
    });
  }

  getAllAccounts(orgCodes) {
    return this.http.post('/api/contacts/getAllAccounts', { orgCodes });
  }
  getActivityDetail(activityId) {
    return this.http.post('/api/activity/getActivityDetails', {
      activity_id: activityId,
    });
  }
  //green tick
  completeChecklist = (subActivityId) => {
    return this.http.post('/api/activity/completeSalesSubActivity', {
      subActivityId: subActivityId,
    });
  };
  getTemplates(serviceId) {
    return this.http.post('/api/activity/getActivityMasterTemplate', {
      applicationId: 36,
      serviceId: serviceId,
    });
  }
  //master
  getActivityPhase = (oppId) => {
    return this.http.post('/api/opportunity/getPhaseMasterForCreation', {
      opportunityId: oppId,
    });
  };
  //edit Opp Task
  editOppTask = (activityId, formData, updateAll) => {
    return this.http.post('/api/activity/editTask', {
      activity_id: activityId,
      taskFormDetails: formData,
      updateAll: updateAll,
    });
  };
  //edit checklist
  editCheckList = (activityId, formData) => {
    return this.http.post('/api/activity/editCheckList', {
      activity_id: activityId,
      checklistFormDetails: formData,
    });
  };

  //editApproval
  updateApproval = (activityId, formData) => {
    return this.http.post('/api/activity/editApproval', {
      activityId: activityId,
      approvalFormDetails: formData,
    });
  };

  //get opportunity activity details by id
  getActivityDetailsById = (activityId) => {
    return this.http.post('/api/activity/getActivityDetailsOpportunity ', {
      activityId: activityId,
    });
  };
  //create checklist
  createCheckList = (formDetail, appId, oppId) => {
    return this.http.post('/api/activity/createChecklist', {
      checklistFormDetails: formDetail,
      applicationId: appId,
      opportunityId: oppId,
    });
  };

  getOpportunityActivityIds(opportunityId) {
    return this.http.post('/api/opportunity/getActivityListForOpportunity', {
      opportunityId: opportunityId,
    });
  }
  getMyOpportunityActivities(opportunityId) {
    return this.http.post('/api/opportunity/getMyOpportunityActivities', {
      opportunityId: opportunityId,
    });
  }

  getLeadSource() {
    return this.http.post('/api/salesMaster/leadSource', {});
  }
  getProbabilityMaster() {
    return this.http.post('/api/salesMaster/getProbabilityMaster', {
      applicationId: 36,
    });
  }
  getProposalTypeMaster() {
    return this.http.post('/api/salesMaster/getProposalTypeMaster', {});
  }

  getOpportunityAllField(opportunityId) {
    return this.http.post('/api/opportunity/getAllOpportunityFields', {
      opportunityId: opportunityId,
    });
  }

  updatePlForOpportunities() {
    return this.http.post('/api/opportunity/updatePandL', {});
  }

  getExistingContacts = (orgCodes) => {
    return this.http.post('/api/contacts/allActiveContactsDropdown', {
      orgCodes: orgCodes,
    });
  };

  getSalesUnit() {
    return this.http.get('/api/salesMaster/salesUnit');
  }

  getServiceType() {
    return this.http.post('/api/salesMaster/getServiceTypeMaster', {});
  }

  getSalesType() {
    return this.http.post('/api/salesMaster/getSalesStatusMaster', {});
  }

  getProposalType() {
    // return new Promise((resolve, reject) => {
    //   this.http.post('/api/opportunity/getProposalStatusMaster',{}).subscribe(res => {
    //     return resolve(res)
    //   }, err => {
    //     return reject(err)
    //   })
    // })
    return this.http.post('/api/salesMaster/getProposalStatusMaster', {});
  }
  getProposalTypeBid() {
    return new Promise((resolve, reject) => {
      this.http.post('/api/salesMaster/getProposalStatusMaster', {}).subscribe(
        (res) => {
          return resolve(res);
        },
        (err) => {
          return reject(err);
        }
      );
    });
  }
  getOpportunityStatusSalesGov() {
    let statusValues = this.getStatusObjectEntries();
    return new Promise((resolve, reject) => {
      this.http
        .post('/api/salesMaster/getSalesStatusMasterForEdit', { statusValues })
        .subscribe(
          (res) => {
            return resolve(res);
          },
          (err) => {
            return reject(err);
          }
        );
    });
  }
  getbidType() {
    return this.http.post('/api/salesMaster/getBidTypeMaster', {});
  }
  
  getRequirementType() {
    return this.http.post('/api/salesMaster/getRequirementType',{});
  }

  getOpportunityDetailedType() {
    return this.http.post('/api/salesMaster/getOpportunityDetailedType',{});
  }

  getConfidenceLevel() {
    return this.http.post('/api/salesMaster/getConfidenceLevel',{});
  }

  getBusinessType() {
    return this.http.post('/api/salesMaster/getBusinessType',{});
  }

  getPipelineMaster() {
    return this.http.post('/api/salesMaster/pipelineMaster', {});
  }
  // Fetch all the opportunities Id in opportunities home screen

  getAllOpportunitiesId(orgCodes) {
    console.log(orgCodes);
    return new Promise((resolve, reject) => {
      this.http
        .post('/api/opportunity/getActiveOpportunityIds', {
          orgCodes: orgCodes,
        })
        .subscribe(
          (res) => {
            return resolve(res);
          },
          (err) => {
            return reject(err);
          }
        );
    });
  }

  //change ceo attention status

  changeCeoAttentionStatus(id, status) {
    return this.http.post('/api/opportunity/ceoAttention', {
      opportunityId: id,
      flag: status,
    });
  }
  //Fetch all the opportunities data in home screen

  getAllOpportunities(opportunity_id) {
    return this.http.post('/api/opportunity/getOpportunityDetails', {
      opportunityId: opportunity_id,
    });
  }

  getContactsByAccounts = (accountId) => {
    return this.http.post('api/accounts/getContactsByAccounts', {
      account_id: accountId,
    });
  };
  createOpportunity(opportunityForm) {
    return new Promise((resolve, reject) => {
      this.http
        .post('/api/opportunity/createOpportunity', { opportunityForm })
        .subscribe(
          (res) => {
            return resolve(res);
          },
          (err) => {
            return reject(err);
          }
        );
    });
  }

  getOpportunityAttachments(oppId) {
    return this.http.post('/api/opportunity/getOpportunityAttachments', {
      opportunity_id: oppId,
    });
  }
  updateOpportunityAttachment(oppId, file) {
    return this.http.post('/api/opportunity/updateOpportunityAttachment', {
      opportunity_id: oppId,
      file: file,
    });
  }
  deleteOpportunityAttachment(oppId, file) {
    return this.http.post('/api/opportunity/deleteOpportunityAttachment', {
      opportunity_id: oppId,
      file: file,
    });
  }
  editOpportunity(data, id) {
    return this.http.post('/api/opportunity/editOpportunity', {
      opportunityForm: data,
      opportunityId: id,
    });
  }

  serachOpportunities(text, orgCodes) {
    return this.http.post('/api/opportunity/opportunitySearch', {
      search_parameter: text,
      orgCodes: orgCodes,
    });
  }
  opportunityDetails(opportunityId) {
    return this.http.post('/api/opportunity/getOpportunityDetails', {
      opportunityId: opportunityId,
    });
  }

  opportunityOverview(opportunityId) {
    return this.http.post('/api/opportunity/opportunityOverview', {
      opportunity_id: opportunityId,
    });
  }

  getPipelineReportOpportunities(orgCodes) {
    return this.http.post('/api/opportunity/getPipelineReport', {
      orgCodes: orgCodes,
    });
  }
  changeProposalStatus(id, status) {
    if (status == 'in process') {
      return this.http.post('/api/opportunity/moveToInprocess', {
        opportunity_id: id,
      });
    } else if (status == 'in approval') {
      return this.http.post('/api/opportunity/moveToInApproval', {
        opportunity_id: id,
      });
    } else if (status == 'approved') {
      return this.http.post('/api/opportunity/moveToApproved', {
        opportunity_id: id,
      });
    } else if (status == 'completed') {
      return this.http.post('/api/opportunity/moveToCompleted', {
        opportunity_id: id,
      });
    }
  }
  getFilterMaster() {
    return this.http.post('/api/opportunity/opportunityFilterMasterData', {});
  }
  getFilterData(data, orgCodes) {
    return this.http.post('/api/opportunity/opportunityFilter', {
      filterData: data,
      orgCodes: orgCodes,
    });
  }

  createApprovalActivity(approvalForm, opportunityId) {
    return this.http.post('/api/activity/createApproval', {
      applicationId: 36,
      approvalFormDetails: approvalForm,
      opportunityId: opportunityId,
    });
  }
  createTask(taskFormDetails) {
    return this.http.post('/api/activity/createTask', {
      taskFormDetails: taskFormDetails,
    });
  }
  deleteActivity(activity_id) {
    return this.http.post('/api/activity/deleteActivity', {
      activity_id: activity_id,
    });
  }
  moveToSHSCompleted(opportunityId) {
    return this.http.post('/api/opportunity/moveToSHSCompleted', {
      opportunityId: opportunityId,
    });
  }
  completeActivity(activity_id, is_completed) {
    return new Promise((resolve, reject) => {
      this.http
        .post('/api/activity/completeActivity', { activity_id, is_completed })
        .subscribe(
          (res) => {
            return resolve(res);
          },
          (err) => {
            return reject(err);
          }
        );
    });
  }
  startActivity(activity_id) {
    return new Promise((resolve, reject) => {
      this.http.post('/api/activity/startActivity', { activity_id }).subscribe(
        (res) => {
          return resolve(res);
        },
        (err) => {
          return reject(err);
        }
      );
    });
  }
  openActivity(activity_id) {
    return new Promise((resolve, reject) => {
      this.http.post('/api/activity/openActivity', { activity_id }).subscribe(
        (res) => {
          return resolve(res);
        },
        (err) => {
          return reject(err);
        }
      );
    });
  }
  getGovernanceTypes(applicationId) {
    return new Promise((resolve, reject) => {
      this.http
        .post('/api/activity/getPresalesGovernanceActivityType', {
          applicationId: applicationId,
        })
        .subscribe(
          (res) => {
            return resolve(res);
          },
          (err) => {
            return reject(err);
          }
        );
    });
  }
  getSalesGovernanceTypes(applicationId) {
    return new Promise((resolve, reject) => {
      this.http
        .post('/api/activity/getSalesGovernanceActivityType', {
          applicationId: applicationId,
        })
        .subscribe(
          (res) => {
            return resolve(res);
          },
          (err) => {
            return reject(err);
          }
        );
    });
  }
  getOpportunityIDsDataBidManagerView = (
    filtersCummulation,
    application_id
  ) => {
    return this.http.post('/api/opportunity/bidManagerFilterSP', {
      filtersCummulation: filtersCummulation,
      application_id: application_id,
    });
  };
  getOpportunityDataBidManagerView = (opportunityId, limits) => {
    return this.http.post('/api/opportunity/getOpportunityDataBidManagerView', {
      opportunityId: opportunityId,
      applicationId: 66,
      limits: limits,
    });
  };
  getOpportunityIDsTypeFilterBidManagerView = (limits, govTypes) => {
    return this.http.post(
      '/api/opportunity/getOpportunityIDsTypeFilterBidManagerView',
      {
        limits: limits,
        govTypes: govTypes,
      }
    );
  };
  getBidManagerGlobalSearchIDs = (
    filtersCummulation,
    application_id,
    searchText
  ) => {
    return this.http.post('/api/opportunity/getBidManagerGlobalSearchIDs', {
      filtersCummulation: filtersCummulation,
      application_id: application_id,
      searchText: searchText,
    });
  };

  getOpportunityIDsDataSalesGovernanceReport = (
    filtersCummulation,
    application_id
  ) => {
    return this.http.post('/api/opportunity/salesGovernanceFilterSP', {
      filtersCummulation: filtersCummulation,
      application_id: application_id,
    });
  };
  getOpportunityDataSalesGovernanceReport = (opportunityId, limits) => {
    return this.http.post(
      '/api/opportunity/getOpportunityDataSalesGovernanceReport',
      {
        opportunityId: opportunityId,
        limits: limits,
      }
    );
  };
  getOpportunityIdListSalesGovernanceSearch = (
    filtersCummulation,
    application_id,
    searchText
  ) => {
    return this.http.post('/api/opportunity/getSalesGovernanceSearchIDs', {
      filtersCummulation: filtersCummulation,
      application_id: application_id,
      searchText: searchText,
    });
  };
  getOpportunityIDsDataMarketingGovernanceReport = (
    filtersCummulation,
    application_id
  ) => {
    return this.http.post('/api/opportunity/getMarketingGovernanceFilterSP', {
      filtersCummulation: filtersCummulation,
      application_id: application_id,
    });
  };
  getOpportunityDataMarketingGovernanceReport = (opportunityId, limits) => {
    return this.http.post(
      '/api/opportunity/getOpportunityDataMarketingGovernanceReport',
      {
        opportunityId: opportunityId,
        limits: limits,
      }
    );
  };
  getOpportunityIdListMarketingGovernanceSearch = (
    filtersCummulation,
    application_id,
    searchText
  ) => {
    return this.http.post('/api/opportunity/getMarketingGovernanceSearchIDs', {
      filtersCummulation: filtersCummulation,
      application_id: application_id,
      searchText: searchText,
    });
  };
  // getOpportunityIDsTypeFilterBidManagerView=(limits,govTypes)=>{
  //   return this.http.post('/api/opportunity/getOpportunityIDsTypeFilterBidManagerView', {
  //     limits:limits,
  //     govTypes:govTypes
  //   })
  // }
  getReportViewsList = (application_id) => {
    return this.http
      .post('/api/userExperience/getReportUserViews', {
        application_id: application_id,
      })
      .pipe(
        catchError((err) => {
          console.log(err);
          this.errorService.userErrorAlert(
            err && err.code
              ? err.code
              : err && err.error
              ? err.error.code
              : 'NIL',
            'Error while retrieving Column info list',
            err && err.params
              ? err.params
              : err && err.error
              ? err.error.params
              : {}
          );
          return EMPTY;
        })
      );
  };

  updateBidInfo(bidInfo, oppId) {
    return new Promise((resolve, reject) => {
      this.http
        .post('/api/opportunity/updateBidInfo', {
          bid_info: bidInfo,
          opp_id: oppId,
        })
        .subscribe(
          (res) => {
            return resolve(res);
          },
          (err) => {
            return reject(err);
          }
        );
    });
  }

  updateBidForm(formId, oppId) {
    return new Promise((resolve, reject) => {
      this.http
        .post('/api/opportunity/updateBidForm', {
          form_id: formId,
          opp_id: oppId,
        })
        .subscribe(
          (res) => {
            return resolve(res);
          },
          (err) => {
            return reject(err);
          }
        );
    });
  }

  updateReviewForm(formId, oppId) {
    return new Promise((resolve, reject) => {
      this.http
        .post('/api/opportunity/updateReviewForm', {
          form_id: formId,
          opp_id: oppId,
        })
        .subscribe(
          (res) => {
            return resolve(res);
          },
          (err) => {
            return reject(err);
          }
        );
    });
  }

  getQualifierForm(oppId) {
    return new Promise((resolve, reject) => {
      this.http
        .post('/api/opportunity/getQualifierForm', { opp_id: oppId })
        .subscribe(
          (res) => {
            return resolve(res);
          },
          (err) => {
            return reject(err);
          }
        );
    });
  }

  getInternalPresalesReviewForm(oppId) {
    return new Promise((resolve, reject) => {
      this.http
        .post('/api/opportunity/getInternalPresalesReviewForm', {
          opp_id: oppId,
        })
        .subscribe(
          (res) => {
            return resolve(res);
          },
          (err) => {
            return reject(err);
          }
        );
    });
  }

  getQualifierDetails() {
    return new Promise((resolve, reject) => {
      this.http.post('/api/opportunity/getQualifierDetails', {}).subscribe(
        (res) => {
          return resolve(res);
        },
        (err) => {
          return reject(err);
        }
      );
    });
  }

  getPresalesReviewFormDetails() {
    return new Promise((resolve, reject) => {
      this.http
        .post('/api/opportunity/getPresalesReviewFormDetails', {})
        .subscribe(
          (res) => {
            return resolve(res);
          },
          (err) => {
            return reject(err);
          }
        );
    });
  }

  getAllOrgsForAdmin() {
    return new Promise((resolve, reject) => {
      this.http.post('/api/opportunity/getAllOrgsForAdmin', {}).subscribe(
        (res) => {
          return resolve(res);
        },
        (err) => {
          return reject(err);
        }
      );
    });
  }

  addBidQualifierForm(formDetails) {
    return new Promise((resolve, reject) => {
      this.http
        .post('/api/opportunity/addBidQualifierForm', {
          formDetails: formDetails,
        })
        .subscribe(
          (res) => {
            return resolve(res);
          },
          (err) => {
            return reject(err);
          }
        );
    });
  }

  addPresalesReviewForm(formDetails) {
    return new Promise((resolve, reject) => {
      this.http
        .post('/api/opportunity/addPresalesReviewForm', {
          formDetails: formDetails,
        })
        .subscribe(
          (res) => {
            return resolve(res);
          },
          (err) => {
            return reject(err);
          }
        );
    });
  }

  deleteBidQualifierForm(id) {
    return new Promise((resolve, reject) => {
      this.http
        .post('/api/opportunity/deleteBidQualifierForm', { id: id })
        .subscribe(
          (res) => {
            return resolve(res);
          },
          (err) => {
            return reject(err);
          }
        );
    });
  }

  deletePresalesReviewForm(id) {
    return new Promise((resolve, reject) => {
      this.http
        .post('/api/opportunity/deletePresalesReviewForm', { id: id })
        .subscribe(
          (res) => {
            return resolve(res);
          },
          (err) => {
            return reject(err);
          }
        );
    });
  }

  checkForAdminAccess() {
    return new Promise((resolve, reject) => {
      this.http.post('/api/opportunity/checkForAdminAccess', {}).subscribe(
        (res) => {
          return resolve(res);
        },
        (err) => {
          return reject(err);
        }
      );
    });
  }

  plannedOBVMonthlyFreeze(startDate, endDate) {
    return new Promise((resolve, reject) => {
      this.http
        .post('/api/project/plannedOBVMonthlyFreeze', {
          startDate: startDate,
          endDate: endDate,
        })
        .subscribe(
          (res) => {
            return resolve(res);
          },
          (err) => {
            return reject(err);
          }
        );
    });
  }

  removeRedPlannedOBV(startDate, endDate) {
    return new Promise((resolve, reject) => {
      this.http
        .post('/api/project/removeRedPlannedOBV', {
          startDate: startDate,
          endDate: endDate,
        })
        .subscribe(
          (res) => {
            return resolve(res);
          },
          (err) => {
            return reject(err);
          }
        );
    });
  }

  updateInternalFeedbackScore(oppId, score) {
    return new Promise((resolve, reject) => {
      this.http
        .post('/api/opportunity/updateInternalFeedbackScore', {
          oppId: oppId,
          score: score,
        })
        .subscribe(
          (res) => {
            return resolve(res);
          },
          (err) => {
            return reject(err);
          }
        );
    });
  }

  convertToLocalTime = async (time) => {
    let localTime = new Date(time);
    let timezoneOffset = localTime.getTimezoneOffset() * 60000;
    localTime.setTime(localTime.getTime() - timezoneOffset);
    return localTime;
  };

  getPipelineView(pipeline, orgCodes, filterConfig = []) {
    return new Promise((resolve, reject) => {
      this.http
        .post('/api/opportunity/getPipelineView', {
          pipeline,
          orgCodes,
          filter_config: filterConfig,
        })
        .subscribe(
          (res) => {
            return resolve(res);
          },
          (err) => {
            return reject(err);
          }
        );
    });
  }

  getProbabilityView(probability, orgCodes, filterConfig = []) {
    return new Promise((resolve, reject) => {
      this.http
        .post('/api/opportunity/getProbabilityView', {
          probability,
          orgCodes,
          filter_config: filterConfig,
        })
        .subscribe(
          (res) => {
            return resolve(res);
          },
          (err) => {
            return reject(err);
          }
        );
    });
  }

  gettemplateDetails() {
    return new Promise((resolve, reject) => {
      this.http.post('/api/upload/insertTemplateType', {}).subscribe(
        (res) => {},
        (err) => {
          return reject(err);
        }
      );
    });
  }

  getTemplateTypes = () => {
    return new Promise((resolve, reject) => {
      this.http
        .post('/api/upload/getTemplateTypes', { application_id: 36 })
        .subscribe(
          (res) => {
            return resolve(res);
          },
          (err) => {
            return reject(err);
          }
        );
    });
  };

  deleteTemplateTypeForm = (id) => {
    return new Promise((resolve, reject) => {
      this.http.post('/api/upload/deleteTemplateType', { id: id }).subscribe(
        (res) => {
          return resolve(res);
        },
        (err) => {
          return reject(err);
        }
      );
    });
  };

  addTemplateTypeForm = (form) => {
    return new Promise((resolve, reject) => {
      this.http
        .post('/api/upload/inserTemplateType', {
          application_id: 36,
          form: form,
        })
        .subscribe(
          (res) => {
            return resolve(res);
          },
          (err) => {
            return reject(err);
          }
        );
    });
  };
  getStatusObjectEntries() {
    let accessList = _.where(this._roleService.roles, {
      application_id: 36,
      object_id: 71,
    });

    if (accessList.length > 0) return JSON.parse(accessList[0].object_entries);
    else return null;
  }

  getWinLossDetails() {
    return new Promise((resolve, reject) => {
      this.http.post('/api/opportunity/getWinLossDetails', {}).subscribe(
        (res) => {
          return resolve(res);
        },
        (err) => {
          return reject(err);
        }
      );
    });
  }

  addWinLossForm(formDetails) {
    return new Promise((resolve, reject) => {
      this.http
        .post('/api/opportunity/addWinLossForm', { formDetails: formDetails })
        .subscribe(
          (res) => {
            return resolve(res);
          },
          (err) => {
            return reject(err);
          }
        );
    });
  }

  deleteWinLossForm(id) {
    return new Promise((resolve, reject) => {
      this.http
        .post('/api/opportunity/deleteWinLossForm', { id: id })
        .subscribe(
          (res) => {
            return resolve(res);
          },
          (err) => {
            return reject(err);
          }
        );
    });
  }

  getWinLossForm(oppId) {
    return new Promise((resolve, reject) => {
      this.http
        .post('/api/opportunity/getWinLossForm', { opp_id: oppId })
        .subscribe(
          (res) => {
            return resolve(res);
          },
          (err) => {
            return reject(err);
          }
        );
    });
  }

  updateWinLossForm(formId, oppId) {
    return new Promise((resolve, reject) => {
      this.http
        .post('/api/opportunity/updateWinLossForm', {
          form_id: formId,
          opp_id: oppId,
        })
        .subscribe(
          (res) => {
            return resolve(res);
          },
          (err) => {
            return reject(err);
          }
        );
    });
  }

  getProductCategory = () => {
    return new Promise((resolve, reject) => {
      this.http.post('/api/salesMaster/getProductCategory', {}).subscribe(
        (res) => {
          return resolve(res);
        },
        (err) => {
          return reject(err);
        }
      );
    });
  };

  getOpportunityAdminForm = () => {
    return new Promise((resolve, reject) => {
      this.http.post('/api/opportunity/getOpportunityAdminForm', {}).subscribe(
        (res) => {
          return resolve(res);
        },
        (err) => {
          return reject(err);
        }
      );
    });
  };
  addOpportunityAdminForm = (opportunityForm) => {
    return new Promise((resolve, reject) => {
      this.http
        .post('/api/opportunity/addOpportunityAdminForm', { opportunityForm })
        .subscribe(
          (res) => {
            return resolve(res);
          },
          (err) => {
            return reject(err);
          }
        );
    });
  };

  deleteOpportunityAdminForm = (form_id) => {
    return new Promise((resolve, reject) => {
      this.http
        .post('/api/opportunity/deleteOpportunityAdminForm', { form_id })
        .subscribe(
          (res) => {
            return resolve(res);
          },
          (err) => {
            return reject(err);
          }
        );
    });
  };

  getCurrencyValues = () => {
    return new Promise((resolve, reject) => {
      this.http.post('/api/opportunity/getCurrencyCodesValues', {}).subscribe(
        (res) => {
          return resolve(res);
        },
        (err) => {
          return reject(err);
        }
      );
    });
  };

  getProductHeirarchy = () => {
    return new Promise((resolve, reject) => {
      this.http
        .post('/api/salesMaster/getProductCategoryHierarchy', {})
        .subscribe(
          (res) => {
            return resolve(res);
          },
          (err) => {
            return reject(err);
          }
        );
    });
  };

  /**
   * @description Gets shift List for opportunity
   * @returns
   */
  getShiftList() {
    return new Promise((resolve, reject) => {
      this.http.post('/api/salesMaster/getShiftList', {}).subscribe(
        (res) => {
          return resolve(res);
        },
        (err) => {
          return reject(err);
        }
      );
    });
  }

  /**
   * @description Update Product Category
   */
  updateProductCategory(product_category, opportunity_id) {
    return new Promise((resolve, reject) => {
      this.http
        .post('/api/opportunity/updateProductCategoryInOpp', {
          product_category,
          opportunity_id,
        })
        .subscribe(
          (res) => {
            return resolve(res);
          },
          (err) => {
            return reject(err);
          }
        );
    });
  }

  viewLineofBusinessAccess() {
    let accessList = _.where(this._roleService.roles, {
      application_id: 36,
      object_id: 81,
    });

    if (accessList.length > 0) return true;
    else return false;
  }

  getLabelForOpportunity = (application_id, id) => {
    let val = _.where(this._roleService.label, {
      application_id: application_id,
      id: id,
    });
    return val.length > 0 ? val[0]['label_name'] : '';
  };

  //Get View Heirarchy Component Label
  getProductViewHierarchyLabel(application_id, label_name) {
    return new Promise((resolve, reject) => {
      this.http
        .post('/api/salesMaster/getProductViewHeirarchy', {
          application_id,
          label_name,
        })
        .subscribe(
          (res) => {
            return resolve(res);
          },
          (err) => {
            return reject(err);
          }
        );
    });
  }

  displayLineOfBusinessAndModulesInCreateOpportunity() {
    let accessList = _.where(this._roleService.roles, {
      application_id: 36,
      object_id: 119,
    });

    if (accessList.length > 0) return true;
    else return false;
  }

  hubspotSyncIntoKEBSEngagements(minutes) {
    let domainName = this.tenantService.domainName;
    let tenantInfo = this.tenantService.tenantInfo;
    let is_local = tenantInfo['is_local'] ? 'https://' : 'http://';
    let overallDomainName = is_local + domainName;
    return new Promise((resolve, reject) => {
      this.http
        .post('/api/hubspotBgj/hubspotSyncIntoKEBSEngagements', {
          domain: overallDomainName,
          tenant_id: tenantInfo['tenant_id'],
          minutes: minutes,
        })
        .subscribe(
          (res) => {
            resolve(res);
          },
          (err) => {
            reject(err);
          }
        );
    });
  }

  hubspotSyncIntoKEBS() {
    let domainName = this.tenantService.domainName;
    let tenantInfo = this.tenantService.tenantInfo;
    let is_local = tenantInfo['is_local'] ? 'https://' : 'http://';
    let overallDomainName = is_local + domainName;
    return new Promise((resolve, reject) => {
      this.http
        .post('/api/hubspotBgj/hubspotSyncIntoKEBS', {
          domain: overallDomainName,
          tenant_id: tenantInfo['tenant_id'],
        })
        .subscribe(
          (res) => {
            resolve(res);
          },
          (err) => {
            reject(err);
          }
        );
    });
  }

  hubspotRefreshToken() {
    return new Promise((resolve, reject) => {
      this.http.post('/api/hubspotBgj/hubspotRefreshToken', {}).subscribe(
        (res) => {
          resolve(res);
        },
        (err) => {
          reject(err);
        }
      );
    });
  }

  getBookAndBillUDRFBodyColumns = () => {
    return this.http.post("/api/opportunity/getBookAndBillUDRFBodyColumns",{})
  }

  getActivitiesType = () => {

    return new Promise((resolve, reject) => {
      this.http.post('/api/activity/getActivitiesType', {}).subscribe(
        (res) => {
          resolve(res);
        },
        (err) => {
          reject(err);
        }
      );
    });
  }

  getSalesStatusMaster() {
    return new Promise((resolve, reject) => {
      this.http.post('/api/salesMaster/getSalesStatusMaster', {}).subscribe(
        (res) => {
          resolve(res);
        },
        (err) => {
          reject(err);
        }
      );
    });
  }
  
  getCRMApplication() {
    return new Promise((resolve, reject) => {
      this.http.post('/api/salesMaster/getCRMApplication', {}).subscribe(
        (res) => {
          resolve(res);
        },
        (err) => {
          reject(err);
        }
      );
    });
  }

  addActivityTemplate= (form) => {
    return new Promise((resolve, reject) => {
      this.http
        .post('/api/activity/addActivityTemplate', {
          form: form
        })
        .subscribe(
          (res) => {
            return resolve(res);
          },
          (err) => {
            return reject(err);
          }
        );
    });
  };

  getActivityTemplate = () => {
    return new Promise((resolve, reject) => {
      this.http
        .post('/api/activity/getActivityTemplate', {  })
        .subscribe(
          (res) => {
            return resolve(res);
          },
          (err) => {
            return reject(err);
          }
        );
    });
  };

  getAccountStakeholderMaster(stakeholderType) {
    return new Promise((resolve, reject) => {
      this.http
        .post('api/accounts/getAccountStakeholderMaster', {
          stakeholderType: stakeholderType,
        })
        .subscribe(
          (res) => {
            return resolve(res);
          },
          (err) => {
            return reject(err);
          }
        );
    });
  }

  getOpportunityStakeholderMaster(stakeholderType){
    return new Promise((resolve, reject) => {
      this.http.post("api/opportunity/getOpportunityStakeholderMaster",{stakeholderType:stakeholderType}).subscribe(res => {
        return resolve(res);
      },
        err => {
          return reject(err)
        })
    })
  }

  createOppActivityTemplate(template,appDetails) {
    return new Promise((resolve, reject) => {
      this.http
        .post('api/opportunity/adaptActivityTemplate', {
          template: template,
          appDetails:appDetails
        })
        .subscribe(
          (res) => {
            return resolve(res);
          },
          (err) => {
            return reject(err);
          }
        );
    });
  }
  createAccActivityTemplate(template,appDetails) {
    return new Promise((resolve, reject) => {
      this.http
        .post('api/accounts/adaptActivityTemplate', {
          template: template,
          appDetails:appDetails
        })
        .subscribe(
          (res) => {
            return resolve(res);
          },
          (err) => {
            return reject(err);
          }
        );
    });
  }

  
  getActivitiesTemplateType = () => {

    return new Promise((resolve, reject) => {
      this.http.post('/api/activity/getActivitiesTemplateType', {}).subscribe(
        (res) => {
          resolve(res);
        },
        (err) => {
          reject(err);
        }
      );
    });
  }

  getAllExitEmployee() {
    return this.http.post('/api/salesMaster/getAllExitEmployee', {});
  }

  getAllActiveEmployee() {
    return this.http.post('/api/salesMaster/getAllActiveEmployee', {});
  }

  reallocateEmployee(data) {
    return this.http.post('/api/opportunity/reallocateEmployee', {
      data
    });
  }

  
}
