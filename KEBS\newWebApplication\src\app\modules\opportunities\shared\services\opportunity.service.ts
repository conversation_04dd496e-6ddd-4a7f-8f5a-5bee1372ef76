import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { EMPTY, identity, Observable } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { ErrorService } from 'src/app/services/error/error.service';
import { RolesService } from 'src/app/services/acl/roles.service';
import * as _ from 'underscore';
import { LoginService } from 'src/app/services/login/login.service';
import moment from 'moment';
import { MatDialog} from '@angular/material/dialog';
@Injectable({
  providedIn: 'root'
})
export class OpportunityService {

  constructor(
    private http: HttpClient,
    private errorService: ErrorService,
    private _roleService: RolesService,
    private loginService: LoginService,
    public dialog: MatDialog,
    ) { }

  currentUser = this.loginService.getProfile().profile;

  updateApprovalDetailInline = (activityId, details) => {
    return this.http.post("/api/activity/editApprovalInline", {
      approvalFormDetails: details,
      activityId: activityId
    })
  }

  addToMyOpportunities = (id) => {
    return this.http.post("/api/opportunity/addToMyOpportunities", {
      opportunityId: id
    })
  }

  insertComment = (comment, activityId) => {
    return this.http.post("/api/activity/insertComment", {
      comment: comment,
      activityId: activityId
    })
  }

  removeFromMyOpportunities = (oppId) => {
    return this.http.post('/api/opportunity/removeFromMyOpportunitiesIds', {
      opportunityId: oppId
    })
  }

  getMyOpportunityIds = () => {
    return this.http.post("/api/opportunity/getMyOpportunitiesIds", {})
  }

  updateCheckListInline = (activityId, details) => {
    return this.http.post("/api/activity/editCheckListInline", {
      checklistFormDetails: details,
      activityId: activityId
    })
  }

  getActivityFilterMaster = (id) => {
    return this.http.post("/api/opportunity/opportunityActivityFilterMasterData", {
      applicationReferenceId: id
    })
  }

  getActivitySearchResults = (id, text) => {
    return this.http.post("/api/opportunity/getActivityListForOpportunitySearch", {
      applicationReferenceId: id,
      searchParameter: text
    })
  }

  getActivityFilteredData = (id, data) => {
    return this.http.post("/api/opportunity/opportunityActivityFilter", {
      applicationReferenceId: id,
      filterData: data
    })
  }

  getActivityNotes = (activityId) => {
    return this.http.post("/api/activity/viewNotes", {
      activityId: activityId
    })
  }

  saveActivityNote = (activityId, title, noteColor, content) => {
    return this.http.post("/api/activity/saveActivityNote", {
      activityId: activityId,
      title: title,
      color: noteColor,
      content: content
    })
  }

  deleteActivityNote = (activityId, noteId) => {
    return this.http.post("/api/activity/deleteOppActivityNote", {
      activityId: activityId,
      noteId: noteId
    })
  }

  updateActivityNote = (
    activityId, noteId, noteTitle, noteContent, noteColor
  ) => {
    return this.http.post("/api/activity/updateOppActivityNote", {
      activityId: activityId,
      noteId: noteId,
      noteTitle: noteTitle,
      noteContent: noteContent,
      noteColor: noteColor
    })
  }

  updateTaskDetailInline = (activityId, details) => {
    return this.http.post("/api/activity/editTaskInline", {
      taskFormDetails: details,
      activityId: activityId
    });
  };

  getActivityComments = (activityId) => {
    return this.http.post("/api/activity/retrieveActivityComments", {
      activityId: activityId
    })
  }

  getActivityAttachments = (activityId) => {
    return this.http.post("/api/activity/retrieveActivityAttachments", {
      activityId: activityId
    })
  }

  updateActivityAttachment(activityId, file) {
    return this.http.post('/api/activity/updateActivityAttachment', {
      activity_id: activityId,
      file: file
    })
  }
  deleteActivityAttachment(activityId, file) {
    return this.http.post('/api/activity/deleteActivityAttachment', {
      activity_id: activityId,
      file: file
    })
  }

  getAllAccounts(orgCodes,opportunityId=0) {
    return this.http.post("/api/contacts/getAllAccounts", { orgCodes,opportunityId:opportunityId })
  }
  getActivityDetail(activityId) {
    return this.http.post("/api/activity/getActivityDetails", {
      activity_id: activityId
    })
  }
  //green tick
  completeChecklist = (subActivityId) => {
    return this.http.post("/api/activity/completeSalesSubActivity", {
      subActivityId: subActivityId
    })
  }
  getTemplates(serviceId) {
    return this.http.post("/api/activity/getActivityMasterTemplate", {
      applicationId: 36,
      serviceId: serviceId
    })
  }
  //master
  getActivityPhase = (oppId) => {
    return this.http.post("/api/upload/getPhaseMasterForCreation", {
      opportunityId: oppId,
      application_id: 36
    })
  }
  //edit Opp Task
  editOppTask = (activityId, formData,updateAll) => {
    return this.http.post("/api/activity/editTask", {
      activity_id: activityId,
      taskFormDetails: formData,
      updateAll:updateAll
   
    })
  }
  //edit checklist
  editCheckList = (activityId, formData,updateAll) => {
    return this.http.post("/api/activity/editCheckList", {
      activity_id: activityId,
      checklistFormDetails: formData,
      updateAll:updateAll
    })
  }

  //editApproval
  updateApproval = (activityId, formData,updateAll) => {
    return this.http.post('/api/activity/editApproval', {
      activityId: activityId,
      approvalFormDetails: formData,
      updateAll:updateAll
    })
  }

  //get opportunity activity details by id
  getActivityDetailsById = (activityId) => {
    return this.http.post("/api/activity/getActivityDetailsOpportunity ", {
      activityId: activityId
    })
  }
  //create checklist 
  createCheckList = (formDetail, appId, oppId) => {
    return this.http.post("/api/activity/createChecklist", {
      checklistFormDetails: formDetail,
      applicationId: appId,
      opportunityId: oppId
    })
  }

  getOpportunityActivityIds(opportunityId) {
    return this.http.post("/api/opportunity/getActivityListForOpportunity", {
      opportunityId: opportunityId
    })
  }
  getMyOpportunityActivities(opportunityId) {
    return this.http.post('/api/opportunity/getMyOpportunityActivities', {
      opportunityId: opportunityId
    })
  }
  getLeadSource() {
    return this.http.post("/api/salesMaster/leadSource",{})
  }
  getProbabilityMaster(){
    return this.http.post("/api/salesMaster/getProbabilityMaster",{
      applicationId:36
    })
  }
  getProposalTypeMaster(){
    return this.http.post("/api/salesMaster/getProposalTypeMaster",{
    })
  }
  
  
  getOpportunityAllField(opportunityId) {
    return this.http.post('/api/opportunity/getAllOpportunityFields', {
      opportunityId: opportunityId
    })
  }

  updatePlForOpportunities() {
    return this.http.post('/api/opportunity/updatePandL', {
    })
  }

  getExistingContacts = (orgCodes) => {
    return this.http.post('/api/contacts/allActiveContactsDropdown', { orgCodes: orgCodes });
  }

  getSalesUnit() {
    return this.http.get('/api/salesMaster/salesUnit')
  }

  getServiceLineMaster() {
    return this.http.post('/api/salesMaster/getServiceLineMaster', {})
  }

  getServiceType() {
    return this.http.post('/api/salesMaster/getServiceTypeMaster',{})
  }

  getSalesType() {
    return this.http.post('/api/salesMaster/getSalesStatusMasterForEdit',{
      statusValues: this.getStatusObjectEntries(),
    });
  }

  getProposalType() {
    // return new Promise((resolve, reject) => {
    //   this.http.post('/api/opportunity/getProposalStatusMaster',{}).subscribe(res => {
    //     return resolve(res)
    //   }, err => {
    //     return reject(err)
    //   })
    // })
    return this.http.post('/api/salesMaster/getProposalStatusMaster',{})
  }
  getProposalTypeBid(){
  
    return new Promise((resolve, reject) => {
      this.http.post('/api/salesMaster/getProposalStatusMaster',{}).subscribe(res => {
        return resolve(res)
      }, err => {
        return reject(err)
      });
  });}
  getOpportunityStatusSalesGov(){
    let statusValues= this.getStatusObjectEntries()
    return new Promise((resolve, reject) => {
      this.http.post('/api/salesMaster/getSalesStatusMasterForEdit',{statusValues}).subscribe(res => {
        return resolve(res)
      }, err => {
        return reject(err)
      });
  });}

  getbidType() {
    return this.http.post('/api/salesMaster/getBidTypeMaster',{});
  }

  getRequirementType() {
    return this.http.post('/api/salesMaster/getRequirementType',{});
  }

  getOpportunityDetailedType() {
    return this.http.post('/api/salesMaster/getOpportunityDetailedType',{});
  }

  getConfidenceLevel() {
    return this.http.post('/api/salesMaster/getConfidenceLevel',{});
  }

  getBusinessType() {
    return this.http.post('/api/salesMaster/getBusinessType',{});
  }

  getPipelineMaster() {
    return this.http.post("/api/salesMaster/getPipelineMaster",{});
  }
  // Fetch all the opportunities Id in opportunities home screen

  getAllOpportunitiesId(orgCodes) {
    console.log(orgCodes)
    return new Promise((resolve, reject) => {
      this.http.post('/api/opportunity/getActiveOpportunityIds', { orgCodes: orgCodes }).subscribe(res => {
        return resolve(res)
      }, err => {
        return reject(err)
      })
    })
  }

  //change ceo attention status

  changeCeoAttentionStatus(id, status) {
    return this.http.post('/api/opportunity/ceoAttention', {
      opportunityId: id,
      flag: status
    })
  }
  //Fetch all the opportunities data in home screen

  getAllOpportunities(opportunity_id) {
    return this.http.post('/api/opportunity/getOpportunityDetails', {
      opportunityId: opportunity_id
    })
  }

  getContactsByAccounts = (accountId) => {
    return this.http.post('api/accounts/getContactsByAccounts', {
      account_id: accountId
    })
  }
  createOpportunity(opportunityForm) {
    return new Promise((resolve, reject) => {
      this.http.post("/api/opportunity/createOpportunity", { opportunityForm, currentDate: moment().format('YYYY-MM-DD') }).subscribe(res => {
        return resolve(res);
      }, err => {
        return reject(err);
      })
    })
  }

  getOpportunityAttachments(oppId) {
    return this.http.post('/api/opportunity/getOpportunityAttachments', {
      opportunity_id: oppId
    })
  }
  updateOpportunityAttachment(oppId, file) {
    return this.http.post('/api/opportunity/updateOpportunityAttachment', {
      opportunity_id: oppId,
      file: file
    })
  }
  deleteOpportunityAttachment(oppId, file) {
    return this.http.post('/api/opportunity/deleteOpportunityAttachment', {
      opportunity_id: oppId,
      file: file
    })
  }
  editOpportunity(data, id) {
    return this.http.post('/api/opportunity/editOpportunity', {
      opportunityForm: data,
      opportunityId: id,
      currentDate: moment().format('YYYY-MM-DD')
    })
  }

  serachOpportunities(text, orgCodes) {
    return this.http.post('/api/opportunity/opportunitySearch', {
      search_parameter: text,
      orgCodes: orgCodes
    })
  }
  opportunityDetails(opportunityId) {
    return this.http.post('/api/opportunity/getOpportunityDetails', {
      opportunityId: opportunityId
    })
  }

  opportunityOverview(opportunityId) {
    return this.http.post('/api/opportunity/opportunityOverview', {
      opportunity_id: opportunityId
    })
  }

  getPipelineReportOpportunities(orgCodes) {
    return this.http.post('/api/opportunity/getPipelineReport', {
      orgCodes: orgCodes
    })
  }
  changeProposalStatus(id, status) {
    if (status == 'in process') {
      return this.http.post("/api/opportunity/moveToInprocess", {
        opportunity_id: id
      });
    }
    else if (status == 'in approval') {
      return this.http.post("/api/opportunity/moveToInApproval", {
        opportunity_id: id
      });
    }
    else if (status == 'approved') {
      return this.http.post("/api/opportunity/moveToApproved", {
        opportunity_id: id
      })
    }
    else if (status == 'completed') {
      return this.http.post("/api/opportunity/moveToCompleted", {
        opportunity_id: id
      })
    }
  }
  getFilterMaster() {
    return this.http.post('/api/opportunity/opportunityFilterMasterData', {});
  }
  getFilterData(data, orgCodes) {
    return this.http.post('/api/opportunity/opportunityFilter', {
      filterData: data,
      orgCodes: orgCodes
    })
  }

  createApprovalActivity(approvalForm, opportunityId) {
    return this.http.post("/api/activity/createApproval", {
      applicationId: 36,
      approvalFormDetails: approvalForm,
      opportunityId: opportunityId
    })
  }
  /**
   * 
   * @param taskFormDetails 
   * @param is_duplicate 
   * @description Create task and even create duplicate task
   * @returns 
   */
  createTask(taskFormDetails, is_duplicate) {
    return this.http.post("/api/activity/createTask", {
      taskFormDetails: taskFormDetails,
      is_duplicate: is_duplicate
    })
  }
  deleteActivity(activity_id) {
    return this.http.post("/api/activity/deleteActivity", {
      activity_id: activity_id
    })
  }
  moveToSHSCompleted(opportunityId) {
    return this.http.post("/api/opportunity/moveToSHSCompleted", {
      opportunityId: opportunityId
    })
  }
  completeActivity(activity_id, is_completed) {
    return new Promise((resolve, reject) => {
      this.http.post("/api/activity/completeActivity", { activity_id, is_completed }).subscribe(res => {
        return resolve(res)
      },
        err => {
          return reject(err)
        })
    })
  }
  startActivity(activity_id) {
    return new Promise((resolve, reject) => {
      this.http.post("/api/activity/startActivity", { activity_id }).subscribe(res => {
        return resolve(res)
      },
        err => {
          return reject(err)
        })
    })
  }
  openActivity(activity_id) {
    return new Promise((resolve, reject) => {
      this.http.post("/api/activity/openActivity", { activity_id }).subscribe(res => {
        return resolve(res)
      },
        err => {
          return reject(err)
        })
    })
  }

  updateActualHoursInlineEditTs(activity_id,activity_name,actual_hours,timesheet_id,project_id,application_id){
      return this.http.post("/api/opportunity/addActivityActualHours",{
        activity_id: activity_id,
        activity_name: activity_name,
        actual_hours: actual_hours,
        timesheet_id: timesheet_id,
        _id: project_id,
        application_id: application_id
      })
  }

  deleteActualHoursInlineEdit(activity_id,activity_name,actual_hours,timesheet_id,opp_id){
    return this.http.post("/api/opportunity/deleteActivityActualHours",{
        "activity_id": activity_id,
          "activity_name": activity_name,
          "actual_hours": actual_hours,
          "timesheet_id": timesheet_id,
          "project_id": opp_id
    })
  }

  getGovernanceTypes(applicationId){
    return new Promise((resolve, reject) => {
      this.http.post("/api/activity/getPresalesGovernanceActivityType", { applicationId:applicationId }).subscribe(res => {
        return resolve(res)
      },
        err => {
          return reject(err)
        })
    })
  }
  getSalesGovernanceTypes(applicationId){
    return new Promise((resolve, reject) => {
      this.http.post("/api/activity/getSalesGovernanceActivityType", {applicationId:applicationId}).subscribe(res => {
        return resolve(res)
      },
        err => {
          return reject(err)
        })
    })
  }
  getOpportunityIDsDataBidManagerView=(filtersCummulation,application_id)=>{
    return this.http.post('/api/opportunity/bidManagerFilterSP', {
      filtersCummulation:filtersCummulation,
      application_id:application_id
    })
  }
  getOpportunityDataBidManagerView=(opportunityId,limits)=>{
    return this.http.post('/api/opportunity/getOpportunityDataBidManagerView', {
      opportunityId: opportunityId,
      applicationId:66,
      limits:limits
    })
  };
  getOpportunityIDsTypeFilterBidManagerView=(limits,govTypes)=>{
    return this.http.post('/api/opportunity/getOpportunityIDsTypeFilterBidManagerView', {
      limits:limits,
      govTypes:govTypes
    })
  }
  getBidManagerGlobalSearchIDs=(filtersCummulation,application_id,searchText)=>{
    return this.http.post('/api/opportunity/getBidManagerGlobalSearchIDs', {
      filtersCummulation:filtersCummulation,
      application_id:application_id,
      searchText:searchText
    })
  }

  getOpportunityIDsDataSalesGovernanceReport=(filtersCummulation,application_id)=>{
    return this.http.post('/api/opportunity/salesGovernanceFilterSP', {
      filtersCummulation:filtersCummulation,
      application_id:application_id
    })
  }
  getOpportunityDataSalesGovernanceReport=(opportunityId,limits)=>{
    return this.http.post('/api/opportunity/getOpportunityDataSalesGovernanceReport', {
      opportunityId: opportunityId,
      limits:limits
    })
  };
  getOpportunityIdListSalesGovernanceSearch=(filtersCummulation,application_id,searchText)=>{
    return this.http.post('/api/opportunity/getSalesGovernanceSearchIDs', {
      filtersCummulation:filtersCummulation,
      application_id:application_id,
      searchText:searchText
    })
  }
  getOpportunityIDsDataMarketingGovernanceReport=(filtersCummulation,application_id)=>{
    return this.http.post('/api/opportunity/getMarketingGovernanceFilterSP', {
      filtersCummulation:filtersCummulation,
      application_id:application_id
    })
  }
  getOpportunityDataMarketingGovernanceReport=(opportunityId,limits)=>{
    return this.http.post('/api/opportunity/getOpportunityDataMarketingGovernanceReport', {
      opportunityId: opportunityId,
      limits:limits
    })
  };
  getOpportunityIdListMarketingGovernanceSearch=(filtersCummulation,application_id,searchText)=>{
    return this.http.post('/api/opportunity/getMarketingGovernanceSearchIDs', {
      filtersCummulation:filtersCummulation,
      application_id:application_id,
      searchText:searchText
    })
  }
  // getOpportunityIDsTypeFilterBidManagerView=(limits,govTypes)=>{
  //   return this.http.post('/api/opportunity/getOpportunityIDsTypeFilterBidManagerView', {
  //     limits:limits,
  //     govTypes:govTypes
  //   })
  // }

  getAllActivityList=(opportunityId,flag,activityId)=>{
    return this.http.post('/api/opportunity/getAllActivityList', {
      opportunityId:opportunityId,
      flag: flag,
      activityId: activityId
    })
  }
  getReportViewsList=(application_id)=>{
    return this.http.post('/api/userExperience/getReportUserViews', {
      application_id:application_id
    }).pipe(
      catchError(err => {
        console.log(err);
        this.errorService.userErrorAlert((err && err.code) ? err.code : (err && err.error) ? err.error.code : 'NIL', "Error while retrieving Column info list", (err && err.params) ? err.params : (err && err.error) ? err.error.params : {})
        return EMPTY;
      })
    )
  }
  
  convertToLocalTime = async (time) => {
    let localTime = new Date(time);
    let timezoneOffset = localTime.getTimezoneOffset() * 60000;
    localTime.setTime(localTime.getTime() - timezoneOffset);
    return localTime;
  };

  getLeadsmovedtoopp=()=>{
    return this.http.post("/api/salesMaster/getLeadsmovedOpportunity",{})
  }
/**
 * 
 * @param leadid 
 * @description Gets lead details in creation screen of opportunity
 * @returns 
 */
  getLeadDetails(leadid) {
    return new Promise((resolve, reject) => {
      this.http.post("/api/salesMaster/getLeadDetailsOpportunity", { leadid }).subscribe(res => {
        return resolve(res)
      },
        err => {
          return reject(err)
        })
    })
  }
/**
 * 
 * @param oppId 
 * @description Gets api from table and approves the activities
 * @returns 
 */
  isActivityApproval=(oppId)=>{
    return new Promise((resolve, reject) => {
      this.http.post("/api/opportunity/isActivityAppStarted", { oppId }).subscribe(res => {
        return resolve(res)
      },
        err => {
          return reject(err)
        })
    })
  }
  /**
   * @description  Gets opportunity Status based on roles
   * @returns 
   */
  getStatusObjectEntries(){
    let accessList = _.where(this._roleService.roles, { application_id: 36, object_id: 71 });

    if (accessList.length > 0)
      return JSON.parse(accessList[0].object_entries);

    else
      return null;
  }
  /**
   * 
   * @param existing_status 
   * @description Displays Sales Status during Edit of opportunity
   * @returns 
   */
  getSalesTypeForEditOpp=(existing_status)=>{
      let statusValues=this.getStatusObjectEntries()
      
        return new Promise((resolve, reject) => {
          this.http.post('/api/salesMaster/getSalesStatusMasterForEdit',{statusValues,existing_status}).subscribe(res => {
            return resolve(res)
          }, err => {
            return reject(err)
          });
        });
      
  }

  /**
 * 
 * @param existing_status 
 * @param project_integrated 
 * @description Displays Sales Status during Edit of opportunity
 * @returns 
 */
  getSalesStatusForEditOpp = (existing_status, project_integrated) => {
    let statusValues = this.getStatusObjectEntries()

    return new Promise((resolve, reject) => {
      this.http.post('/api/salesMaster/getStatusMasterForEdit', { statusValues, existing_status, project_integrated: project_integrated }).subscribe(res => {
        return resolve(res)
      }, err => {
        return reject(err)
      });
    });

  }
  /**
   * 
   * @param oppId 
   * @description Gets Win loss form id when form gets opened
   * @returns 
   */

  getWinLossForm(oppId){
    return new Promise((resolve, reject) => {
      this.http.post("/api/opportunity/getWinLossForm", {opp_id:oppId}).subscribe(res => {
        return resolve(res)
      },
        err => {
          return reject(err)
        })
    })
  }

  /**
   * 
   * @param oppId 
   * @description Gets Qualifier form id when form gets opened
   * @returns 
   */
  getQualifierForm(oppId){
    return new Promise((resolve, reject) => {
      this.http.post("/api/opportunity/getQualifierForm", {opp_id:oppId}).subscribe(res => {
        return resolve(res)
      },
        err => {
          return reject(err)
        })
    })
  }
/**
 * 
 * @param formId 
 * @param oppId 
 * @description Updates Win Loss form
 * @returns 
 */
  updateWinLossForm(formId,oppId){
    return new Promise((resolve, reject) => {
      this.http.post("/api/opportunity/updateWinLossForm", {form_id:formId,opp_id:oppId}).subscribe(res => {
        return resolve(res)
      },
        err => {
          return reject(err)
        })
    })
    
  }

/**
 * Returns qualifier form if any form is mapped to the opportunity configuration
 * @param {Object} formValue 
 * @returns 
 */

  getQualifierFormWithOppValue(formValue){
    return new Promise((resolve, reject) => {
      this.http.post("/api/opportunity/getQualifierFormWithOppValue", {formValue:formValue}).subscribe(res => {
        return resolve(res)
      },
        err => {
          return reject(err)
        })
    })
  }

  
  getProductCategory=()=>{
    return new Promise((resolve,reject)=>{
        this.http.post("/api/salesMaster/getProductCategory",{}).subscribe((res)=>{
          return resolve(res)
        },(err)=>{
          return reject(err)
        })
    })
  }

  getModule=()=>{
    return new Promise((resolve,reject)=>{
      this.http.post("/api/salesMaster/getModules",{}).subscribe((res)=>{
        return resolve(res)
      },(err)=>{
        return reject(err)
      })
  })
  }

  /**
   * @description Gets Internal Stakeholders tab based on role
   * @returns {JSON}
   */
     getInternalStakeHoldersEntries(){
      let accessList = _.where(this._roleService.roles, { application_id: 36, object_id: 73 });
     
      if (accessList.length > 0)
        return true;
  
      else
        return false;
    }

    /**
     * @description Gets shift List for opportunity
     * @returns 
     */
    getShiftList(){
      return new Promise((resolve,reject)=>{
        this.http.post("/api/salesMaster/getShiftList",{}).subscribe((res)=>{
          return resolve(res)
        },(err)=>{
          return reject(err)
        })
      })
    }

    getReasonList() {
      return this.http.post("/api/salesMaster/getReasonList",{});
    }

  /**
   * @description Gets Sharepoint access based on role
   * @returns {JSON}
   */
  getSharePointAccessForOpportunity(){
    let accessList = _.where(this._roleService.roles, { application_id: 36, object_id: 98 });
    
    if (accessList.length > 0)
      return true;

    else
      return false;
  }

  /**
   * 
   * @param opportunity_id 
   * @description Trigger Bid Qualification approval during status change
   * @returns 
   */
  updateBidQualificationApproval(opportunity_id){
    return new Promise((resolve,reject)=>{
      this.http.post("/api/opportunity/triggerBidQualificationApproval",{opportunity_id}).subscribe((res)=>{
        return resolve(res)
      },(err)=>{
        return reject(err)
      })
    })
  }

  /**
   * 
   * @param opportunityId
   * @returns 
   */
  retrieveOpportunityNotes(opportunityId) {
    return new Promise((resolve, reject)=>{
      this.http.post('/api/opportunity/noteOpportunityOperations', {opportunityId: opportunityId,operation_id: 2}).subscribe(res=>{
        resolve(res)
      },(err)=>{
        reject(err)
      })
    })
  }

  /**
   * 
   * @param noteId 
   * @param opportunityId 
   * @returns 
   */
  deleteOpportunityNote(noteId, opportunityId) {
    return new Promise((resolve, reject)=>{
      this.http.post('/api/opportunity/noteOpportunityOperations', {note_id: noteId,opportunityId: opportunityId,operation_id: 4}).subscribe(res=>{
        resolve(res)
      },(err)=>{
        reject(err)
      })
  })
  }

  createOpportunityNotes(opportunityId, message, title, colorCode) {
    return new Promise((resolve, reject)=>{
      this.http.post('/api/opportunity/noteOpportunityOperations', {opportunityId: opportunityId,operation_id: 1,message: message,title: title,color_code: colorCode}).subscribe(res=>{
        resolve(res)
      },(err)=>{
        reject(err)
      })
    })

  }


  editOpportunityNotes(noteId, opportunityId, message, title, colorCode) {

    return new Promise((resolve, reject)=>{
      this.http.post("/api/opportunity/noteOpportunityOperations",{note_id: noteId,opportunityId: opportunityId,operation_id: 3,message: message,title: title,color_code: colorCode}).subscribe(res=>{
        resolve(res)
      },(err)=>{
        reject(err)
      })
    })

  }
  displayLineOfBusinessAndModulesInCreateOpportunity(){
    let accessList = _.where(this._roleService.roles, { application_id: 36, object_id: 119 });
   
    if (accessList.length > 0)
      return true;

    else
      return false;
   }

   displayScopeInCreateOpportunity = () => {
    let accessList = _.where(this._roleService.roles, { application_id: 36, object_id: 335 });
   
    if (accessList.length > 0)
      return true;

    else
      return false;
   }

   displayValuesAndPartnersInCreateOpportunity = () => {
    let accessList = _.where(this._roleService.roles, { application_id: 36, object_id: 29331 });
   
    if (accessList.length > 0)
      return true;

    else
      return false;
   }
  
   displayStakeHoldersInCreateOpportunity = () => {
    let accessList = _.where(this._roleService.roles, { application_id: 36, object_id: 29332 });
   
    if (accessList.length > 0)
      return true;

    else
      return false;
   }

   displayAdaptTemplateInCreateOpportunity  = () => {
    let accessList = _.where(this._roleService.roles, { application_id: 36, object_id: 29329 });
   
    if (accessList.length > 0)
      return true;

    else
      return false;
   }

   displayGeneralInCreateOpportunity  = () => {
    let accessList = _.where(this._roleService.roles, { application_id: 36, object_id: 29330 });
   
    if (accessList.length > 0)
      return true;

    else
      return false;
   }

   isFiscalYearCheck  = () => {
    let accessList = _.where(this._roleService.roles, { application_id: 36, object_id: 29347 });
   
    if (accessList.length > 0)
      return true;

    else
      return false;
   }

   getFormFieldCollection = () => {
    return this.http.post("/api/opportunity/getFormFieldCollection",{  
    })
  }

  getPaymentTerms = () => {
    return this.http.post("/api/master/getPaymentTerms", {
    })
  }

  //Used in Opportunity - m_payment_terms Master
  paymentTerms = (acc_id?) => {
    return this.http.post('/api/accounts/paymentTerms', { customer_id: acc_id });
  };
  

  getlegalEntityList = () => {
    return this.http.post("/api/opportunity/getlegalEntityList",{  
    })
  }

  getCurrencyList = () => {
    return this.http.post("/api/opportunity/getCurrencyList",{  
    })
  }

  getOpportunityType = () => {
    return this.http.post("/api/opportunity/getOpportunityType",{  
    })
  }

  getSoWReference = () => {
    return this.http.post("/api/opportunity/getSoWReference",{  
    })
  }

  getAllAccountNames() {
    return this.http.post('/api/salesMaster/allAcountNames',{})
  }

  getDepartmentList() {
    return this.http.post('/api/opportunity/getDepartmentList',{})
  }

  getAccountSpecificContact(accountId) {
    return this.http.post('/api/opportunity/getAccountSpecificContact',{accountId:accountId})
  }

  sendEditNotification = (opportunityId) => {
    return this.http.post('/api/opportunity/getOidForFavOpportunity',{opportunityId:opportunityId})
  }

  isEditNotification = () => {
    let accessList = _.where(this._roleService.roles, { application_id: 36, object_id: 29358});

    if (accessList.length > 0)
      return true;

    else
      return false;
  }

  isFyChange = () => {
    let accessList = _.where(this._roleService.roles, { application_id: 36, object_id: 29362});

    if (accessList.length > 0)
      return true;

    else
      return false;
  }

  stagewiseFieldConfig = (object_id) => {
    return new Promise((resolve, reject) => {
       this.http.post("/api/salesMaster/getApplicationObject",{object_id:object_id}).subscribe( (res:any) => {
         if(res.messType=='S' && res.messData.length>0){
          return resolve(res)
         }
         else{
           return resolve(res)
         }
       },
       err => {
         return reject(err)
       })
    })
  }

  getStagewiseFormFieldConfig = (id) => {
    return new Promise((resolve, reject) => { this.http.post("/api/opportunity/getStagewiseFormFieldConfig",{status_id:id}).subscribe(res=>{
      resolve(res)
    },(err)=>{
      reject(err)
    })
  })
       
  }
  getStagewiseFormFieldConfigAdmin = (id) => {
   
    return this.http.post("/api/opportunity/getStagewiseFormFieldConfig",{status_id:id})
       
  }

     updateSWFieldConfig(id,data) {
      return this.http
          .post('/api/opportunity/updateSWFieldConfig', {
            status_id:id,
            data:data
          })
    }

    resetSWFieldConfig() {
      return this.http
          .post('/api/opportunity/resetSWFieldConfig', {
          })
    }

    getAllStagewiseFormFieldConfig = () => {

       return this.http.post("/api/opportunity/getAllStagewiseFormFieldConfig",{})
    }

    checkNdUpdateOpportunityStatus = async( status_id, opportunityData)=>{
      return new Promise(async(resolve, reject) => {
          let total_status =[]
          await this.getOpportunityStatusSalesGov().then((res:any)=>{
              total_status=res
          },(err)=>{
            console.log(err)
          })
          let changed_status  = _.where(total_status,{id: status_id})
          let messText=""
          if(changed_status.length>0)
          {
            console.log(opportunityData)
            if(changed_status[0]['is_finance_check']==1)
            {
                console.log("Finance Check")
                await this.checkAccountFinancial(opportunityData['in_account']).then((res :any)=>{
                  console.log(res)
                  if(res.messType=="S" && res.messData!=null){
                    let accFinFields = JSON.parse(changed_status[0].accountfinance_field_config)
                     accFinFields=_.filter( accFinFields,function(item:any){
                     return item.is_active==true 
                    })
                    let accFinData=res.messData[0]
                    console.log(accFinFields)
                    console.log(accFinData)
                    let mandFields=""
                    let editRestrict =false
                    if(accFinFields.length>0){
                    for(let item of accFinFields){
                      console.log(accFinData[item.data_field])
                      if(accFinData[item.data_field]=="" || accFinData[item.data_field]==null || accFinData[item.data_field]==0 ||  accFinData[item.data_field]=="00:00:00 00:00:00")
                      {
                        mandFields+=" " +item.label+ " " 
                        editRestrict=true
                      }
                    }
                    if(editRestrict==true){
                      messText="To Update Status Kindly Fill Account Financial Fields " + mandFields
                      resolve({messType:"E",messText:messText})  }
                      else{
                        resolve({messType:"S"})  
                      }
                  }
                    else{
                      console.log("Finance Check2")
                      //this._utilService.showMessage("","Dismiss",3000)
                      resolve({messType:"S"})  

                    }
                  }
                  else{
                    console.log("Finance Check3")
                    messText="Financial details of Account not found"
                  resolve({messType:"E",messText:messText})    
                  }
                   
                })
                
            }
           
            resolve({messType:"S",messText:messText})          
          }
          else
          {
            messText="Status Change is restricted!"
            resolve({messType:"E",messText:messText})
          }
      })

    }

    
  async checkAccountFinancial(accountId) 
  {
    console.log("checkAccountFinancial")

    return new Promise((resolve, reject) => {
      this.http.post('/api/accounts/getAccountFinancialAndAgreement',{
        accountId : accountId
      }).subscribe(res => {
        return resolve(res)
      },
        err => {
          return reject(err)
        })
    })
  }
  
    getFormFieldConfigAdmin = () => {
   
      return this.http.post("/api/opportunity/getFormFieldConfig",{})
         
    }
    updateOppFieldConfig = (item) => {
   
      return this.http.post("/api/opportunity/updateOppFormFieldConfig",{data:item})
         
    }
    deleteOppFieldConfig = (item) => {
   
      return this.http.post("/api/opportunity/deleteOppFormFieldConfig",{data:item})
         
    }
    createOppFieldConfig = (item) => {
   
      return this.http.post("/api/opportunity/createOppFormFieldConfig",{data:item})
         
    }

    getAccountFormFieldCollection = () => {
      return this.http.post("/api/accounts/getFormFieldCollection",{  
      })
    }
    updateAccFieldConfig = (item) => {
   
      return this.http.post("/api/accounts/updateAccFormFieldConfig",{data:item})
         
    }
    deleteAccFieldConfig = (item) => {
   
      return this.http.post("/api/accounts/deleteAccFormFieldConfig",{data:item})
         
    }
    createAccFieldConfig = (item) => {
   
      return this.http.post("/api/accounts/createAccFormFieldConfig",{data:item})
         
    }

    getNotifyFormFieldCollection = () => {
      return this.http.post("/api/auditLog/getNotifyFormFieldCollection",{  
      })
    }

    updateNotifyFieldConfig = (item) => {
   
      return this.http.post("/api/auditLog/updateNotifyFormFieldConfig",{data:item})
         
    }
    deleteNotifyFieldConfig = (item) => {
   
      return this.http.post("/api/auditLog/deleteNotifyFormFieldConfig",{data:item})
         
    }
    createNotifyFieldConfig = (item) => {
   
      return this.http.post("/api/auditLog/createNotifyFormFieldConfig",{data:item})
         
    }
    
  getParentOpportunityMaster(accountId,opportunityId) {
    return this.http.post('/api/salesMaster/getParentOpportunityMaster',{
      account_id:accountId,
      opportunity_id:opportunityId
    })
  }

  getPartyAccountNames(account_id) {
    return this.http.post('/api/salesMaster/getPartyAccountNames',{account_id})
  }
  
  checkIfParentOpportunity(opportunityId) {
    return this.http.post('/api/salesMaster/checkIfParentOpportunity',{
      opportunity_id:opportunityId
    })
  }
  
    async openWinLossForm(opportunityData, updated_status_name)
    {
        return new Promise(async(resolve, reject)=>{
                let isFormAval : boolean
                let form_details 
                if(opportunityData['win_loss_form']!=null && opportunityData['win_loss_form'] !='')
                {
                    form_details = opportunityData['win_loss_form']
                    isFormAval = true
                }
                else
                {
                    await this.getWinLossForm(opportunityData['opportunity_id']).then(async(res:any)=>
                    {
                        console.log(res)
                        if(res.messType=="E")
                        {
                            console.log("Form ID not found")
                            isFormAval = false
                        }
                        else if(res.messType=="S")
                        {
                            form_details = res.data.form_id
                            isFormAval = true
                            this.updateWinLossForm(form_details,opportunityData['opportunity_id']);
                        }
                    })
                }
          
                if(!isFormAval){
                
                    await this.getReasonList().subscribe(async(res) => {
                      let reason_list = res;
                      const { ReasonModalComponent } = await import ( 'src/app/modules/shared-lazy-loaded-components/reason-modal/reason-modal.component' ) 
                      const openReasonDialog = this.dialog.open ( ReasonModalComponent , 
                        { 
                          height : '100%' ,
                          width : '75%' , 
                          position : {
                              right : '0px' 
                            }, 
                          data : 
                              { 
                                title:`Share your opinion`,
                                reason_title:`Reason for changing  ${updated_status_name} status`,
                                button_name: "Submit Reason",
                                reason_list:reason_list
                              } 
                            }
                      )
                      
                      openReasonDialog.afterClosed().subscribe(async (res:any)=>{
                            console.log(res)
                            if(res!=undefined)
                            {
                                if(res.messType=='S')
                                {
                                    resolve({messType:true,result:"Reason added successfully!"})
                                }
                            }
                            else
                            {
                                resolve({messType:false,result:"Without reason status cannot be changed!"})
                            }
            
                            
                      }) 
        
                    }, err => {
                      console.error(err);
                      resolve({messType:true,result:""})
                    })

                    
                                                     
                }
                else
                {
          
                    const { CustomFormModalComponent } = await import ( 'src/app/modules/shared-lazy-loaded-components/custom-form-modal/custom-form-modal.component' ) 
                    const openCustomFormModalComponent = this.dialog.open ( CustomFormModalComponent , 
                      {height : '100%' ,
                      width : '75%' , 
                      position : {
                         right : '0px' 
                       }, 
                      data : 
                          { 
                            formId : form_details, 
                            isEdit :true, 
                            entryForId : (opportunityData['opportunity_id']).toString(),
                            formName: "Win-Loss Form",
                            inputData: {
                              fieldMappedInput: {
                                [form_details]: updated_status_name.toLowerCase().includes('won') ? 1 : 2
                              }
                            }
                          } 
                        }
                    )
                    
                    openCustomFormModalComponent.afterClosed()
                        .subscribe(async (res:any)=>{
                          console.log(res)
                          if(res!=undefined)
                          {
                              if(res.messType=='S')
                              {
                                this.updateWinLossForm(form_details,opportunityData['opportunity_id']);
                                resolve({messType:true,result:"Form Filled Successfully!"})
                                  
                              } 
                              else{
                                
                               resolve({messType:false,result:"Win Loss Form not submitted"})
                              }                         
                          }
                          resolve({messType:false,result:"Error updating form"})

            
                    })
                    
                }
                

        })
            
           
        
  }

  //Get Project Details for the Opportunity if Integrated
  getOpportunityProjectDetails = (opportunity_id) => {
    return new Promise((resolve, reject) => {
      this.http
        .post("/api/opportunity/getOpportunityProjectDetails", {
          opportunityId: opportunity_id,
        })
        .subscribe(
          (res: any) => {
            return resolve(res);
          },
          (err) => {
            return reject(err);
          }
        );
    });
  };

  getTabsConfig(): Observable<any> {
    return this.http.get('/api/opportunity/tabs');
  }

  getStatusStageMappingConfig() {
    return this.http.get<any>('/api/opportunity/getStatusStageMappingConfig');
  }
  
  checkIssuePoAndPoValue = (opportunity_id,status,issue_po?) => {
    return new Promise((resolve, reject) => {
      this.http
        .post("/api/opportunity/checkIssuePoAndPoValue", {
          opportunityId:opportunity_id,
          status,
          issue_po
        })
        .subscribe(
          (res: any) => {
            return resolve(res);
          },
          (err) => {
            return reject(err);
          }
        );
    });
  };
  
  checkAccountEditiable(opportunity_id): Observable<any> {
    return this.http.post('/api/opportunity/checkAccountEditiable',{opportunityId:opportunity_id});
  }

  
  getOpportunityDocumentTypes = async () => {
    try {
      const response = await this.http.post("/api/opportunity/getOpportunityDocumentTypes", {}).toPromise();
      return response;
    } catch (error) {
      console.error("Error retrieving document types:", error);
      throw error;
    }
  };
  
  checkPoValueMatchCheck = () => {
    return new Promise((resolve, reject) => {
      this.http
        .post("/api/opportunity/checkPoValueMatchCheck", {
        })
        .subscribe(
          (res: any) => {
            return resolve(res);
          },
          (err) => {
            return reject(err);
          }
        );
    });
  };

  crmAuthGaurd(oid, aid, application_id, object_id = null) {
    return this.http.post('api/opportunity/crmAuthGaurd', {
      oid: oid,
      aid: aid,
      application_id: application_id,
      object_id: object_id
    });
  }
  
  checkPoValueMatch = (opportunity_id,status,opportunity_value=null,po_value=null) => {
    return new Promise((resolve, reject) => {
      this.http
        .post("/api/opportunity/checkPoValueMatch", {
          opportunityId:opportunity_id,
          status,
          opportunity_value:opportunity_value,
          po_value:po_value
        })
        .subscribe(
          (res: any) => {
            return resolve(res);
          },
          (err) => {
            return reject(err);
          }
        );
    });
  };

}






