<div class="container-fluid ledger-entry-modal">
  <div *ngIf="!isLedgerPopUpSpinnerLoading" class="row justify-content-center" style="padding-top: 10vh;">
    <mat-spinner diameter="30"></mat-spinner>
  </div>
  <form [formGroup]="paymentEntry" *ngIf="isLedgerPopUpSpinnerLoading">
    <div class="row mt-2 pb-1" style="border-bottom: solid 1px #cacaca">
      <div class="col-11 d-flex">
        <div class="raised-circle my-auto">
          <mat-icon class="logo-icon"> credit_card </mat-icon>
        </div>
        <span class="txt-bold my-auto ml-3 mr-2"> Payment entry </span>
        <span class="txt-highlighted my-auto">
          {{ this.itemDetails.description }}
        </span>
        <div style="color: gray; font-size: 13px" class="row px-3 my-auto">
          (Total : {{ totalVendorAmount }})
        </div>
      </div>
      <div class="col-1 d-flex" *ngIf="isPopUpMode">
        <button
          mat-icon-button
          class="ml-auto close-button"
          (click)="closeLedgerEntry('close')"
        >
          <mat-icon class="close-icon">close</mat-icon>
        </button>
      </div>
    </div>
    <div class="row mt-2">
      <mat-horizontal-stepper
        #stepper
        (selectionChange)="selectionChange($event)"
      >
        <mat-step *ngIf="blockTreasuryFeatures == false">
          <ng-template matStepLabel>Expense Entries</ng-template>

          <ng-container
            *ngIf="
              statutoryViewMode == 'readOnly' &&
              isTaxDetailMasterDataAvailable &&
              isGlAccMasterDataAvailable &&
              isLegalEntityMasterDataAvailable &&
              (itemDetails.payment_model_id === 2 ||
                itemDetails.payment_model_id === 3)
            "
          >
            <!-- <pre>{{inPrefilledJson?.StatutoryDetails.expenseEntry|json}}</pre> -->

            <div
              *ngFor="
                let item of inPrefilledJson?.StatutoryDetails.expenseEntry;
                let i = index
              "
            >
              <div class="row">
                <div class="col-12">
                  <strong>{{ item.milestone_name }}</strong
                  >&nbsp;&nbsp;({{ item.milestone_amount }})
                </div>
              </div>
              <div class="row">
                <div class="col-12">

                  <div class="table-wrapperReadOnly">
                    <table class="table table-hover">
                      <thead>
                        <tr>
                          <th scope="col">#</th>
                          <th scope="col">Cost center</th>
                          <th scope="col">Subgroup</th>
                          <th scope="col">Source Entity</th>
                          <th scope="col">Entity Name</th>
                          <th scope="col">Description</th>
                          <th scope="col" *ngIf="tdsNatureOnLedgerEntry">Nature of Payment</th>
                          <th scope="col" *ngIf="taxabilityTypeonLedgerEntry">Taxability Type</th>
                          <th scope="col" *ngIf="taxabilityTypeonLedgerEntry">Taxabaility Rate</th>
                          <th scope="col" *ngIf="HSNSAConLedgerEntry">HSN/SAC</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr
                        *ngFor="
                          let subItem of item.milestone_entries;
                          let si = index
                        "
                      >
                        <th scope="row">{{ si + 1 }}</th>
                        <td>{{ subItem.cost_center?.name }}</td>
                        <td>{{ subItem.subGroupDetail?.name }}</td>
                        <td>{{ subItem.sourceEntityDetail?.name }}</td>
                        <td>
                          {{ subItem.sourceEntityDetail?.tally_entity_name }}
                        </td>
                        <td> {{ subItem.description ? subItem.description : '-'}} </td>
                        <td *ngIf="tdsNatureOnLedgerEntry">
                          {{ subItem.tds_nature_gl_name?subItem.tds_nature_gl_name : '-' }}
                        </td>
                        <td *ngIf="taxabilityTypeonLedgerEntry">
                          {{ subItem.taxability_type_name?subItem.taxability_type_name : '-' }}
                        </td>
                        <td *ngIf="taxabilityTypeonLedgerEntry">
                          {{ subItem.taxability_type_rate?subItem.taxability_type_rate : '-' }}
                        </td>
                        <td *ngIf="HSNSAConLedgerEntry">
                          {{ subItem.hsn_sac_value?subItem.hsn_sac_value : '-' }}
                        </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                  
                  <!-- <table class="table table-hover">
                    <thead>
                      <tr>
                        <th scope="col">#</th>
                        <th scope="col">Cost center</th>
                        <th scope="col">Subgroup</th>
                        <th scope="col">Source Entity</th>
                        <th scope="col">Entity Name</th>
                        <th scope="col">Description</th>
                        <th scope="col" *ngIf="tdsNatureOnLedgerEntry">Nature of Payment</th>
                        <th scope="col" *ngIf="taxabilityTypeonLedgerEntry">Taxability Type</th>
                        <th scope="col" *ngIf="taxabilityTypeonLedgerEntry">Taxabaility Rate</th>
                        <th scope="col" *ngIf="HSNSAConLedgerEntry">HSN/SAC</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr
                        *ngFor="
                          let subItem of item.milestone_entries;
                          let si = index
                        "
                      >
                        <th scope="row">{{ si + 1 }}</th>
                        <td>{{ subItem.cost_center?.name }}</td>
                        <td>{{ subItem.subGroupDetail?.name }}</td>
                        <td>{{ subItem.sourceEntityDetail?.name }}</td>
                        <td>
                          {{ subItem.sourceEntityDetail?.tally_entity_name }}
                        </td>
                        <td> {{ subItem.description ? subItem.description : '-'}} </td>
                        <td *ngIf="tdsNatureOnLedgerEntry">
                          {{ subItem.tds_nature_gl_name?subItem.tds_nature_gl_name : '-' }}
                        </td>
                        <td *ngIf="taxabilityTypeonLedgerEntry">
                          {{ subItem.taxability_type_name?subItem.taxability_type_name : '-' }}
                        </td>
                        <td *ngIf="taxabilityTypeonLedgerEntry">
                          {{ subItem.taxability_type_rate?subItem.taxability_type_rate : '-' }}
                        </td>
                        <td *ngIf="HSNSAConLedgerEntry">
                          {{ subItem.hsn_sac_value?subItem.hsn_sac_value : '-' }}
                        </td>
                      </tr>
                    </tbody>
                  </table> -->
                </div>
              </div>
            </div>
          </ng-container>
          <ng-container
            *ngIf="
              statutoryViewMode == 'readOnly' &&
              isTaxDetailMasterDataAvailable &&
              isGlAccMasterDataAvailable &&
              isLegalEntityMasterDataAvailable &&
              itemDetails.payment_model_id === 1
            "
          >
            <!-- <pre>{{inPrefilledJson?.StatutoryDetails.expenseEntry|json}}</pre> -->

            <div
              *ngFor="
                let item of inPrefilledJson?.StatutoryDetails.expenseEntry;
                let i = index
              "
            >
              <div class="row">
                <div class="col-12">
                  <strong>{{ item.sub_group_name }}</strong
                  >&nbsp;&nbsp;({{ item.sub_group_amount
                  }}{{ item.vendor_currency }})
                </div>
              </div>
              <div class="row">
                <div class="col-12">

                  <div class="table-wrapperReadOnly">
                    <table class="table table-hover">
                      <thead>
                        <tr>
                          <th scope="col">#</th>
                          <th scope="col">Cost center</th>
                          <th scope="col">Amount</th>
                          <th scope="col">GL Account</th>
                          <th scope="col">Source Entity</th>
                          <th scope="col">Entity Name</th>
                          <th scope="col">Description</th>
                          <th scope="col" *ngIf="tdsNatureOnLedgerEntry">Nature of Payment</th>
                          <th scope="col" *ngIf="taxabilityTypeonLedgerEntry">Taxability Type</th>
                          <th scope="col" *ngIf="taxabilityTypeonLedgerEntry">Taxabaility Rate</th>
                          <th scope="col" *ngIf="HSNSAConLedgerEntry">HSN/SAC</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr
                          *ngFor="
                            let subItem of item.sub_group_entries;
                            let si = index
                          "
                        >
                          <th scope="row">{{ si + 1 }}</th>
                          <td>{{ subItem.cost_center?.name }}</td>
                          <td>{{ subItem.cc_amount }}</td>
                          <td>
                            {{ subItem.gl_account_name }}
                          </td>
                          <td>
                            {{ subItem.sourceEntityDetail.name }}
                          </td>
                          <td>
                            {{ subItem.sourceEntityDetail.tally_entity_name }}
                          </td>
                          <td>
                            {{ subItem.description?subItem.description : '-' }}
                          </td>
                          <td *ngIf="tdsNatureOnLedgerEntry">
                            {{ subItem.tds_nature_gl_name?subItem.tds_nature_gl_name : '-' }}
                          </td>
                          <td *ngIf="taxabilityTypeonLedgerEntry">
                            {{ subItem.taxability_type_name?subItem.taxability_type_name : '-' }}
                          </td>
                          <td *ngIf="taxabilityTypeonLedgerEntry">
                            {{ subItem.taxability_type_rate?subItem.taxability_type_rate : '-' }}
                          </td>
                          <td *ngIf="HSNSAConLedgerEntry">
                            {{ subItem.hsn_sac_value?subItem.hsn_sac_value : '-' }}
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>

                  <!-- <table class="table table-hover">
                    <thead>
                      <tr>
                        <th scope="col">#</th>
                        <th scope="col">Cost center</th>
                        <th scope="col">Amount</th>
                        <th scope="col">GL Account</th>
                        <th scope="col">Source Entity</th>
                        <th scope="col">Entity Name</th>
                        <th scope="col">Description</th>
                        <th scope="col" *ngIf="tdsNatureOnLedgerEntry">Nature of Payment</th>
                        <th scope="col" *ngIf="taxabilityTypeonLedgerEntry">Taxability Type</th>
                        <th scope="col" *ngIf="taxabilityTypeonLedgerEntry">Taxabaility Rate</th>
                        <th scope="col" *ngIf="HSNSAConLedgerEntry">HSN/SAC</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr
                        *ngFor="
                          let subItem of item.sub_group_entries;
                          let si = index
                        "
                      >
                        <th scope="row">{{ si + 1 }}</th>
                        <td>{{ subItem.cost_center?.name }}</td>
                        <td>{{ subItem.cc_amount }}</td>
                        <td>
                          {{ subItem.gl_account_name }}
                        </td>
                        <td>
                          {{ subItem.sourceEntityDetail.name }}
                        </td>
                        <td>
                          {{ subItem.sourceEntityDetail.tally_entity_name }}
                        </td>
                        <td>
                          {{ subItem.description?subItem.description : '-' }}
                        </td>
                        <td *ngIf="tdsNatureOnLedgerEntry">
                          {{ subItem.tds_nature_gl_name?subItem.tds_nature_gl_name : '-' }}
                        </td>
                        <td *ngIf="taxabilityTypeonLedgerEntry">
                          {{ subItem.taxability_type_name?subItem.taxability_type_name : '-' }}
                        </td>
                        <td *ngIf="taxabilityTypeonLedgerEntry">
                          {{ subItem.taxability_type_rate?subItem.taxability_type_rate : '-' }}
                        </td>
                        <td *ngIf="HSNSAConLedgerEntry">
                          {{ subItem.hsn_sac_value?subItem.hsn_sac_value : '-' }}
                        </td>
                      </tr>
                    </tbody>
                  </table> -->
                </div>
              </div>
            </div>
          </ng-container>

          <ng-container *ngIf="statutoryViewMode == 'edit'">
            <div class="col-12 d-flex justify-content-between" style="margin-bottom: 10px;">
              <!-- <mat-checkbox class="example-margin ml-2" [(ngModel)]="isChecked" (click)="check()">Advance</mat-checkbox> -->

              <div>
                <mat-checkbox class="example-margin ml-2" formControlName="isChecked" (click)="check()">Advance</mat-checkbox>
              </div>

              <div>
                <button mat-icon-button (click)="saveDraft()" mat-mini-fab [ngClass]="isDraftSpinnerLoading ? 'save-draft-btn-loading' : 'save-draft-btn'" class="save-draft-btn">
                  <mat-icon style="font-size: 20px; margin-top:-8px;" matTooltip="Save as Draft" *ngIf="!isDraftSpinnerLoading">save</mat-icon>
                  <mat-spinner  *ngIf="isDraftSpinnerLoading" matTooltip="Please wait..." class="spinner-align"
                                      diameter="20"></mat-spinner>
                </button>
              </div>
           
            </div>
           

            <div
              style="min-height: 40vh"
              class="col-12 expense-entry-style"
              *ngIf="
                expenseEntrySubGroupControl.length > 0 &&
                itemDetails.payment_model_id === 1
              "
              formArrayName="expenseEntry"
            >
              <div
                *ngFor="
                  let subGroup of expenseEntrySubGroupControl;
                  let i = index
                "
                [formGroupName]="i"
                class="my-2"
              >
                <div class="d-flex">
                  <div>
                    {{ subGroup.value.sub_group_name }}
                    <span class="pl-3" style="color: gray; font-size: 13px"
                      >({{
                        subGroup.value.sub_group_amount +
                          "
                                            " +
                          subGroup.value.vendor_currency
                      }})
                    </span>
                  </div>
                  <div
                    class="mx-3"
                    *ngIf="
                      paymentEntry.controls.expenseEntry['controls'][i][
                        'controls'
                      ]['is_sub_group_amt_exceeded'] == true
                    "
                  >
                    <div class="d-flex">
                      <div class="mx-2">
                        <mat-icon style="color: #cf0001; font-size: 18xp">
                          error_outline
                        </mat-icon>
                      </div>
                      <div style="color: #cf0001; font-size: 12px">
                        Amount limit reached
                      </div>
                    </div>
                  </div>
                </div>

                <div
                  *ngIf="
                    subGroup.controls.sub_group_entries.controls.length > 0
                  "
                  formArrayName="sub_group_entries"
                >
                  <div
                    *ngFor="
                      let subGroupEntries of subGroup.controls.sub_group_entries
                        .controls;
                      let j = index
                    "
                    [formGroupName]="j"
                    class="row my-1"
                  >
                    <div class="mr-2">
                      <app-input-search
                        class="drop-down"
                        required="true"
                        placeholder="Cost Center"
                        [list]="ccExpenseEntry"
                        formControlName="cost_center"
                      >
                      </app-input-search>
                    </div>
                    <div class="mr-2">
                      <mat-form-field
                        appearance="outline"
                        class="create-account-field"
                      >
                        <mat-label>Amount *</mat-label>
                        <input
                          matInput
                          placeholder="Eg: 1000"
                          type="number"
                          formControlName="cc_amount"
                        />
                        <span matSuffix>{{
                          itemDetails.vendor_preferred_currency
                        }}</span>
                      </mat-form-field>
                    </div>
                    <!-- <div class="mr-2">
                                            <app-input-search class="drop-down" required="true" placeholder="Currency"
                                            [list]='currencyExpenseEntry' formControlName='cc_currency'>
                                            </app-input-search>
                                        </div> -->
                    <div class="mr-2">
                      <div *ngIf="j!=0">
                        <mat-form-field
                        appearance="outline"
                        class="create-account-field">
                          <mat-label>Target entity</mat-label>
                          <input
                            matInput
                            readonly
                            formControlName="source_entity_name"/>
                        </mat-form-field>
                      </div>
                      <div *ngIf="j==0">
                        <app-input-search
                        class="drop-down"
                        required="true"
                        placeholder="Target entity"
                        [list]="legalEntityMasterData"
                        formControlName="source_entity_id"
                       
                        >
                        </app-input-search>
                      </div>
                    </div>
                    <div class="mr-2">
                      <app-input-search
                        class="drop-down"
                        required="true"
                        placeholder="GL Account"
                        [list]="
                          paymentEntry.controls.expenseEntry['controls'][i][
                            'controls'
                          ]['sub_group_entries']['controls'][j]['controls'][
                            'gl_account_master_data'
                          ]
                        "
                        formControlName="gl_account"
                      >
                      </app-input-search>
                    </div>

                    <div class="mr-2">
                      <mat-form-field
                        appearance="outline"
                        class="create-account-field"
                      >
                        <mat-label>Description</mat-label>
                        <input
                          matInput
                          required="true"
                          formControlName="description" (keydown.enter)="moveToNextStep()"
                        />
                      </mat-form-field>
                    </div>

                     <!-- TDS Nature -->
                    <div class="mr-2" *ngIf="!this.paymentEntry.get('isChecked').value && tdsNatureOnLedgerEntry && isTDSNAtureAllowed(paymentEntry.controls.expenseEntry['controls'][i][
                                         'controls'
                                       ]['sub_group_entries']['controls'][j]['controls'][
                                         'gl_account'
                                       ].value,i,j,itemDetails.payment_model_id)">
                      <app-input-search class="drop-down" [required]="
                                             paymentEntry.controls.expenseEntry['controls'][i][
                                               'controls'
                                             ]['sub_group_entries']['controls'][j]['controls'][
                                               'gl_account'
                                             ].value
                                             " placeholder="Nature of Payment" [list]="tdsNatureList" formControlName="tds_nature_gl_id"
                        (change)="onDeductionClick()">
                      </app-input-search>
                    </div>

                    <!-- Taxability Type -->
                    <div class="mr-2" *ngIf="!this.paymentEntry.get('isChecked').value && taxabilityTypeonLedgerEntry && isGSTRateonLedgerAllowed(paymentEntry.controls.expenseEntry['controls'][i][
                                                                                         'controls'
                                                                                       ]['sub_group_entries']['controls'][j]['controls'][
                                                                                         'gl_account'
                                                                                       ].value,i,j,itemDetails.payment_model_id)">
                      <app-input-search class="drop-down" [required]="false" placeholder="Taxability Type"
                        [list]="taxabilityTypeDetails" formControlName="taxability_type_id" (change)="onTaxabilityChange(paymentEntry.controls.expenseEntry['controls'][i][
                        'controls'
                      ]['sub_group_entries']['controls'][j]['controls'][
                        'taxability_type_id'
                      ].value,i,j,itemDetails.payment_model_id)">
                      </app-input-search>
                    </div>

                    <!-- GST Rate -->
                    <div class="mr-2" *ngIf="!this.paymentEntry.get('isChecked').value && taxabilityTypeonLedgerEntry && isGSTRateonLedgerAllowed(paymentEntry.controls.expenseEntry['controls'][i][
                                        'controls'
                                        ]['sub_group_entries']['controls'][j]['controls'][
                                        'gl_account'
                                        ].value,i,j,itemDetails.payment_model_id)">
                      <mat-form-field appearance="outline" class="create-account-field">
                        <mat-label>{{taxabilityTypeDetailsPlaceHolder}}</mat-label>
                          <input matInput type="number" placeholder="Eg: 18%" [min]="0" [max]="100" required="false" 

                                        [readonly]="isTaxabilityRateReadOnly(paymentEntry.controls.expenseEntry['controls'][i][
                                        'controls'
                                        ]['sub_group_entries']['controls'][j]['controls'][
                                        'taxability_type_rate'
                                        ].value,i,j,itemDetails.payment_model_id)"
                                        formControlName="taxability_type_rate" />                    
                      </mat-form-field>
                    </div>

                    <!-- HSN/SAC Code -->
                    <div class="mr-2" *ngIf="!this.paymentEntry.get('isChecked').value && HSNSAConLedgerEntry && isHSNSAConLedgerAllowed(paymentEntry.controls.expenseEntry['controls'][i][
                    'controls'
                    ]['sub_group_entries']['controls'][j]['controls'][
                    'gl_account'
                    ].value,i,j,itemDetails.payment_model_id)">
                      <mat-form-field appearance="outline" class="create-account-field">
                        <mat-label>HSN/SAC</mat-label>
                        <input matInput type="text" placeholder="HSN/SAC" [maxLength]="8" 
                        required="false" pattern="[0-9]*" 
                          formControlName="hsn_sac_value" (keydown.enter)="moveToNextStep()" />
                      </mat-form-field>
                    </div>
                             

                    <div class="d-flex pt-1">
                      <div class="add_btn" (click)="addSubGroupEntry(i)">
                        <mat-icon>add</mat-icon>
                      </div>
                      <div
                        class="add_btn ml-3"
                        (click)="removeSubGroupEntry(i, j)"
                      >
                        <mat-icon>delete</mat-icon>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- <div class="row">
                                    <div class="col-3 px-0 mr-2">
    
                                    </div>
                                    <div class="col-3 px-0">
    
                                    </div>
                                </div> -->
              </div>
            </div>

            <div
              style="min-height: 40vh"
              class="col-12 expense-entry-style"
              *ngIf="
                expenseEntrySubGroupControl.length > 0 &&
                (itemDetails.payment_model_id === 2 ||
                  itemDetails.payment_model_id === 3)
              "
              formArrayName="expenseEntry"
            >
              <div
                *ngFor="
                  let milestone of expenseEntrySubGroupControl;
                  let i = index
                "
                [formGroupName]="i"
                class="my-2"
              >
                <div class="d-flex">
                  <div>
                    {{ milestone.value.milestone_name }}
                    <span class="pl-3" style="color: gray; font-size: 13px"
                      >({{
                        milestone.value.milestone_amount +
                          "
                                            " +
                          itemDetails.vendor_preferred_currency
                      }})
                    </span>
                  </div>
                  <div
                    class="mx-3"
                    *ngIf="
                      paymentEntry.controls.expenseEntry['controls'][i][
                        'controls'
                      ]['is_milestone_amount_exceeded'] == true
                    "
                  >
                    <div class="d-flex">
                      <div class="mx-2">
                        <mat-icon style="color: #cf0001; font-size: 18xp">
                          error_outline
                        </mat-icon>
                      </div>
                      <div style="color: #cf0001; font-size: 12px">
                        Amount limit reached
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  *ngIf="
                    milestone.controls.milestone_entries.controls.length > 0
                  "
                  formArrayName="milestone_entries"
                >
                  <div
                    *ngFor="
                      let milestoneEntries of milestone.controls
                        .milestone_entries.controls;
                      let j = index
                    "
                    [formGroupName]="j"
                    class="row my-1"
                  >
                    <div class="mr-2">
                      <!-- <mat-form-field appearance="outline" class="create-account-field">
                                            <mat-label>Cost center</mat-label>
                                            <input matInput readonly placeholder="Eg: 1000" formControlName='cost_center_name'>
                                        </mat-form-field> -->
                      <app-input-search
                        class="drop-down"
                        required="true"
                        placeholder="Cost Center"
                        [list]="ccExpenseEntry"
                        formControlName="cost_center"
                      >
                      </app-input-search>
                    </div>
                    <div class="mr-2">
                      <mat-form-field
                        appearance="outline"
                        class="create-account-field"
                      >
                        <mat-label>Amount</mat-label>
                        <input
                          matInput
                          placeholder="Eg: 1000"
                          formControlName="amount"
                        />
                        <span matSuffix>{{
                          itemDetails.vendor_preferred_currency
                        }}</span>
                      </mat-form-field>
                    </div>
                    <div class="mr-2">
                      <app-input-search
                        class="drop-down"
                        required="true"
                        placeholder="Sub group"
                        [list]="subGroupMasterData"
                        formControlName="sub_group_id"
                      >
                      </app-input-search>
                    </div>
                    <div class="mr-2">
                      <div *ngIf="j!=0">
                        <mat-form-field
                        appearance="outline"
                        class="create-account-field">
                          <mat-label>Target entity</mat-label>
                          <input
                            matInput
                            readonly
                            formControlName="source_entity_name"/>
                        </mat-form-field>
                      </div>
                      <div *ngIf = "j==0">
                        <app-input-search
                        class="drop-down"
                        required="true"
                        placeholder="Target entity"
                        [list]="legalEntityMasterData"
                        formControlName="source_entity_id">
                        </app-input-search>
                      </div>
                    </div>
                    <div class="mr-2">
                      <app-input-search
                        class="drop-down"
                        required="true"
                        placeholder="GL Account"
                        [list]="
                          paymentEntry.controls.expenseEntry['controls'][i][
                            'controls'
                          ]['milestone_entries']['controls'][j]['controls'][
                            'gl_account_master_data'
                          ]
                        "
                        formControlName="gl_account"
                      >
                      </app-input-search>
                    </div>
                    <div class="mr-2">
                      <mat-form-field
                        appearance="outline"
                        class="create-account-field"
                      >
                        <mat-label>Description</mat-label>
                        <input
                          matInput
                          required="true"
                          formControlName="description"
                          (keydown.enter)="moveToNextStep()"
                        />
                      </mat-form-field>
                    </div>

                    <!-- TDS Nature -->
                    <div class="mr-2" *ngIf="!this.paymentEntry.get('isChecked').value && tdsNatureOnLedgerEntry && isTDSNAtureAllowed(paymentEntry.controls.expenseEntry['controls'][i][
                                        'controls'
                                      ]['milestone_entries']['controls'][j]['controls'][
                                        'gl_account'
                                      ].value,i,j,itemDetails.payment_model_id)">
                      <app-input-search class="drop-down" [required]="
                                           paymentEntry.controls.expenseEntry['controls'][i][
                                             'controls'
                                           ]['milestone_entries']['controls'][j]['controls'][
                                             'gl_account'
                                           ].value
                                           " placeholder="Nature of Payment" [list]="tdsNatureList" formControlName="tds_nature_gl_id"
                        (change)="onDeductionClick()">
                      </app-input-search>
                    </div>

                    <!-- Taxability Type -->
                    <div class="mr-2" *ngIf="!this.paymentEntry.get('isChecked').value && taxabilityTypeonLedgerEntry && isGSTRateonLedgerAllowed(paymentEntry.controls.expenseEntry['controls'][i][
                                                                  'controls'
                                                                ]['milestone_entries']['controls'][j]['controls'][
                                                                  'gl_account'
                                                                ].value,i,j,itemDetails.payment_model_id)">
                      <app-input-search class="drop-down" [required]="false" placeholder="Taxability Type" [list]="taxabilityTypeDetails"
                        formControlName="taxability_type_id" (change)="onTaxabilityChange(paymentEntry.controls.expenseEntry['controls'][i][
                                        'controls'
                                        ]['milestone_entries']['controls'][j]['controls'][
                                        'taxability_type_id'
                                        ].value,i,j,itemDetails.payment_model_id)">
                      </app-input-search>
                    </div>

                    <!-- GST Rate -->
                    <div class="mr-2" *ngIf="!this.paymentEntry.get('isChecked').value && taxabilityTypeonLedgerEntry && isGSTRateonLedgerAllowed(paymentEntry.controls.expenseEntry['controls'][i][
                                        'controls'
                                        ]['milestone_entries']['controls'][j]['controls'][
                                        'gl_account'
                                        ].value,i,j,itemDetails.payment_model_id)">
                      <mat-form-field appearance="outline" class="create-account-field">
                        <mat-label>{{taxabilityTypeDetailsPlaceHolder}}</mat-label>
                          <input matInput type="number" placeholder="Eg: 18%" [min]="0" [max]="100" required="false" 

                                        [readonly]="isTaxabilityRateReadOnly(paymentEntry.controls.expenseEntry['controls'][i][
                                        'controls'
                                        ]['milestone_entries']['controls'][j]['controls'][
                                        'taxability_type_rate'
                                        ].value,i,j,itemDetails.payment_model_id)"
                                        formControlName="taxability_type_rate" />                    
                      </mat-form-field>
                    </div>

                    <!-- HSN/SAC Code -->
                    <div class="mr-2" *ngIf="!this.paymentEntry.get('isChecked').value && HSNSAConLedgerEntry && isHSNSAConLedgerAllowed(paymentEntry.controls.expenseEntry['controls'][i][
                    'controls'
                    ]['milestone_entries']['controls'][j]['controls'][
                    'gl_account'
                    ].value,i,j,itemDetails.payment_model_id)">
                      <mat-form-field appearance="outline" class="create-account-field">
                        <mat-label>HSN/SAC</mat-label>
                        <input matInput type="text" placeholder="HSN/SAC" [maxLength]="8" required="false" formControlName="hsn_sac_value" pattern="[0-9]*" 
                          (keydown.enter)="moveToNextStep()" />
                      </mat-form-field>
                    </div>
                    
                    <div class="d-flex pt-1">
                      <div class="add_btn" (click)="addSubGroupEntry(i)">
                        <mat-icon>add</mat-icon>
                      </div>
                      <div
                        class="add_btn ml-3"
                        (click)="removeSubGroupEntry(i, j)"
                      >
                        <mat-icon>delete</mat-icon>
                      </div>
                    </div>
                    <!-- <div class="d-flex pt-1">
                                        <div class="add_btn" (click)="addMilestoneEntry(i)">
                                            <mat-icon>add</mat-icon>
                                        </div>
                                        <div class="add_btn ml-3" (click)="removeMilestoneEntry(i,j)">
                                            <mat-icon>delete</mat-icon>
                                        </div>
                                    </div> -->
                  </div>
                </div>
              </div>
            </div>
          </ng-container>

          <div class="row mt-2">
            <div class="col-10"></div>
            <div class="col-2">
              <button
                mat-mini-fab
                matTooltip="Next"
                style="background-color: #cf0001; color: white"
                (click)="stepper.next()"
              >
                <mat-icon>navigate_next</mat-icon>
              </button>
            </div>
          </div>
        </mat-step>
        <mat-step *ngIf="blockTreasuryFeatures == false">
          <ng-template matStepLabel>Statutory details</ng-template>

          <ng-container
            *ngIf="statutoryViewMode == 'readOnly'"
            &&
            isTaxDetailMasterDataAvailable
            &&
            isGlAccMasterDataAvailable
            &&
            isLegalEntityMasterDataAvailable
          >
            <div class="row mb-2">
              <div class="col-12">
                <span style="font-size: large">
                  <strong>Tax Additions</strong>
                </span>
              </div>
            </div>
            <div
              *ngFor="let item of inPrefilledJson?.StatutoryDetails.taxAddition"
            >
              <div class="row">
                <div class="col-12">
                  <strong>{{ item.sub_group_name }}</strong>
                </div>
              </div>

              <div class="row">
                <div class="col-12">
                  <table class="table table-hover">
                    <thead>
                      <tr>
                        <th scope="col">#</th>
                        <th scope="col">Tax Type</th>
                        <th scope="col">Tax percentage</th>
                        <th scope="col">Tax Amount</th>
                        <th scope="col">Gl Name</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr
                        *ngFor="
                          let subItem of item.sub_group_tax_entries;
                          let siIndex = index
                        "
                      >
                        <th scope="row">{{ siIndex + 1 }}</th>
                        <td>{{ subItem.tax_type_name }}</td>
                        <td>{{ subItem.tax_percentage }}</td>
                        <td>{{ subItem.tax_amount }}</td>
                        <td>{{ subItem.tax_addition_gl_name }}</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>

            <!-- <pre>{{
              inPrefilledJson?.StatutoryDetails.taxAddition | json
            }}</pre> -->
          </ng-container>

          <ng-container *ngIf="statutoryViewMode == 'edit'">
            <div class="col-12 d-flex justify-content-end">
              <button mat-icon-button (click)="saveDraft()" mat-mini-fab [ngClass]="isDraftSpinnerLoading ? 'save-draft-btn-loading' : 'save-draft-btn'" class="save-draft-btn">
                <mat-icon style="font-size: 20px; margin-top:-8px;" matTooltip="Save as Draft" *ngIf="!isDraftSpinnerLoading">save</mat-icon>
                <mat-spinner  *ngIf="isDraftSpinnerLoading" matTooltip="Please wait..." class="spinner-align"
                                    diameter="20"></mat-spinner>
              </button>
            </div>
           
            <div
              class="col-12 statutory-entry-style"
              *ngIf="taxAdditionSubGroupControl.length > 0"
              formArrayName="taxAddition"
            >
              <div style="font-weight: 500">Tax addition (+)</div>
              <div
                class="my-2"
                *ngFor="
                  let subGroup of taxAdditionSubGroupControl;
                  let i = index
                "
                [formGroupName]="i"
              >
                <div class="row">
                  <div class="col-3">
                    <app-input-search
                      class="drop-down"
                      required="true"
                      placeholder="Expense type"
                      [list]="selectedGlAccount"
                      formControlName="sub_group_ref_id"
                    >
                    </app-input-search>
                  </div>
                  <div class="col-3">
                    <div class="d-flex pt-1">
                      <div class="add_btn" (click)="addTaxSubGroupAddition()">
                        <mat-icon>add</mat-icon>
                      </div>
                      <div
                        class="add_btn ml-3"
                        (click)="removeTaxSubGroupAddition(i)"
                      >
                        <mat-icon>delete</mat-icon>
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  *ngIf="
                    subGroup.controls.sub_group_tax_entries.controls.length > 0
                  "
                  formArrayName="sub_group_tax_entries"
                >
                  <div
                    *ngFor="
                      let subGroupEntries of subGroup.controls
                        .sub_group_tax_entries.controls;
                      let j = index
                    "
                    [formGroupName]="j"
                    class="row my-1"
                  >
                    <div class="col-12">
                      <div class="row">
                        <div class="col-1 border-left-bottom"></div>
                        <div class="col-2">
                          <app-input-search
                            class="drop-down"
                            required="true"
                            placeholder="Tax type"
                            [list]="taxAdditionType"
                            formControlName="tax_type"
                          >
                          </app-input-search>
                        </div>
                        <div class="col-2">
                          <mat-form-field
                            style="width: 100%"
                            appearance="outline"
                            class="create-account-field"
                          >
                            <mat-label>Tax percentage</mat-label>
                            <input
                              (input)="
                                onTaxAdditionPercentageChange(
                                  i,
                                  j,
                                  $event.target.value
                                )
                              "
                              matInput
                              type="number"
                              placeholder="Eg: 18%"
                              [min]="0" [max]="100"
                              formControlName="tax_percentage"
                              [readOnly]="!(paymentEntry.controls.taxAddition['controls'][i][
                              'controls'
                            ].sub_group_tax_entries['controls'][j]['controls'][
                              'is_tax_addition_gl_field_mandatory'
                            ].value)"
                            />
                          </mat-form-field>
                        </div>
                        <div class="col-2">
                          <mat-form-field
                            style="width: 100%"
                            appearance="outline"
                            class="create-account-field"
                          >
                            <mat-label>Tax amount</mat-label>
                            <input
                              readonly
                              matInput
                              formControlName="tax_amount"
                            />
                            <!-- <span matSuffix>{{subGroup.value.sub_group_currency}}</span> -->
                            <span matSuffix>{{
                              itemDetails.vendor_preferred_currency
                            }}</span>
                          </mat-form-field>
                        </div>
                        <div class="col-2">
                          <app-input-search
                            class="drop-down"
                            [required]="
                            paymentEntry.controls.taxAddition['controls'][i][
                              'controls'
                            ].sub_group_tax_entries['controls'][j]['controls'][
                              'is_tax_addition_gl_field_mandatory'
                            ].value
                            "
                            placeholder="GL account"
                            [list]="glAccountForAddition"
                            formControlName="tax_addition_gl_id"
                            [disabled]="!(paymentEntry.controls.taxAddition['controls'][i][
                            'controls'
                          ].sub_group_tax_entries['controls'][j]['controls'][
                            'is_tax_addition_gl_field_mandatory'
                          ].value)"
                          >
                          </app-input-search>
                        </div>
                        <div class="col-2">
                          <div class="d-flex pt-1">
                            <div
                              class="add_btn"
                              (click)="addTaxAdditionEntry(i)"
                            >
                              <mat-icon>add</mat-icon>
                            </div>
                            <div
                              class="add_btn ml-3"
                              (click)="removeTaxAdditionEntry(i, j)"
                            >
                              <mat-icon>delete</mat-icon>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </ng-container>

          <div style="padding-right: 100px" class="d-flex flex-row-reverse">
            <div class="pr-2 my-auto tax-dynamic-box">
              <div class="tax-title">Total tax addition</div>
              <div class="tax-value">
                {{
                  this.paymentEntry.value.totalTaxAddition +
                    "
                                " +
                    this.itemDetails.vendor_preferred_currency
                }}
              </div>
            </div>
          </div>

          <ng-container
            *ngIf="statutoryViewMode == 'readOnly'"
            &&
            isTaxDetailMasterDataAvailable
            &&
            isGlAccMasterDataAvailable
            &&
            isLegalEntityMasterDataAvailable
          >
            <div class="row mb-2">
              <div class="col-12">
                <span style="font-size: large">
                  <strong>Tax Deductions</strong>
                </span>
              </div>
            </div>
            <div
              *ngFor="
                let item of inPrefilledJson?.StatutoryDetails.taxDeduction
              "
            >
              <div class="row">
                <div class="col-12">
                  <strong>{{ item.sub_group_name }}</strong>
                </div>
              </div>

              <div class="row">
                <div class="col-12">
                  <table class="table table-hover">
                    <thead>
                      <tr>
                        <th scope="col">#</th>
                        <th scope="col">Tax Type</th>
                        <th scope="col">Tax percentage</th>
                        <th scope="col">Tax Amount</th>
                        <th scope="col">Gl Name</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr
                        *ngFor="
                          let subItem of item.sub_group_tax_entries;
                          let siIndex = index
                        "
                      >
                        <th scope="row">{{ siIndex + 1 }}</th>
                        <td>{{ subItem.tax_type_name }}</td>
                        <td>{{ subItem.tax_percentage }}</td>
                        <td>{{ subItem.tax_amount }}</td>
                        <td>{{ subItem.tax_deduction_gl_name }}</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>

            <!-- <pre>{{
              inPrefilledJson?.StatutoryDetails.taxDeduction | json
            }}</pre> -->
          </ng-container>

          <ng-container *ngIf="statutoryViewMode == 'edit'">
            <div
              class="col-12"
              *ngIf="taxDeductionSubGroupControl.length > 0"
              formArrayName="taxDeduction"
            >
              <div style="font-weight: 500">Tax deduction (-)</div>
              <div
                class="my-2"
                *ngFor="
                  let subGroup of taxDeductionSubGroupControl;
                  let i = index
                "
                [formGroupName]="i"
              >
                <div class="row">
                  <div class="col-3">
                    <app-input-search
                      class="drop-down"
                      required="true"
                      placeholder="Expense type"
                      [list]="selectedGlAccount"
                      formControlName="sub_group_ref_id"
                    >
                    </app-input-search>
                  </div>
                  <div class="col-3">
                    <div class="d-flex pt-1">
                      <div class="add_btn" (click)="addTaxSubGroupDeduction()">
                        <mat-icon>add</mat-icon>
                      </div>
                      <div
                        class="add_btn ml-3"
                        (click)="removeTaxSubGroupDeduction(i)"
                      >
                        <mat-icon>delete</mat-icon>
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  *ngIf="
                    subGroup.controls.sub_group_tax_entries.controls.length > 0
                  "
                  formArrayName="sub_group_tax_entries"
                >
                  <div
                    *ngFor="
                      let subGroupEntries of subGroup.controls
                        .sub_group_tax_entries.controls;
                      let j = index
                    "
                    [formGroupName]="j"
                    class="row my-1"
                  >
                    <div class="col-12">
                      <div class="row">
                        <div class="col-1 border-left-bottom"></div>
                        <div class="col-2">
                          <app-input-search
                            class="drop-down"
                            required="true"
                            placeholder="Tax type"
                            [list]="taxDeductionType"
                            formControlName="tax_type"
                          >
                          </app-input-search>
                        </div>
                        <div class="col-2">
                          <mat-form-field
                            style="width: 100%"
                            appearance="outline"
                            class="create-account-field"
                          >
                            <mat-label>Tax percentage</mat-label>
                            <input
                              (input)="
                                onTaxDeductionPercentageChange(
                                  i,
                                  j,
                                  $event.target.value
                                )
                              "
                              matInput
                              type="number"
                              placeholder="Eg: 18%"
                              [min]="0" [max]="100"
                              formControlName="tax_percentage"
                              [readOnly]="!(paymentEntry.controls.taxDeduction['controls'][i][
                              'controls'
                            ].sub_group_tax_entries['controls'][j]['controls'][
                              'is_tax_deduction_gl_field_mandatory'
                            ].value)"
                            />
                          </mat-form-field>
                        </div>
                        <div class="col-2">
                          <mat-form-field
                            style="width: 100%"
                            appearance="outline"
                            class="create-account-field"
                          >
                            <mat-label>Tax amount</mat-label>
                            <input
                              readonly
                              matInput
                              formControlName="tax_amount"
                            />
                            <!-- <span matSuffix>{{subGroup.value.sub_group_currency}}</span> -->
                            <span matSuffix>{{
                              itemDetails.vendor_preferred_currency
                            }}</span>
                          </mat-form-field>
                        </div>
                        <div class="col-2">
                          <app-input-search
                            class="drop-down"
                            [required]="
                            paymentEntry.controls.taxDeduction['controls'][i][
                              'controls'
                            ].sub_group_tax_entries['controls'][j]['controls'][
                              'is_tax_deduction_gl_field_mandatory'
                            ].value
                            "
                            placeholder="GL account"
                            [list]="glAccountForDeduction"
                            formControlName="tax_deduction_gl_id"
                            (change)="onDeductionClick()"
                            [disabled]="!(paymentEntry.controls.taxDeduction['controls'][i][
                            'controls'
                          ].sub_group_tax_entries['controls'][j]['controls'][
                            'is_tax_deduction_gl_field_mandatory'
                          ].value)"
                          >
                          </app-input-search>
                        </div>
                        <div class="col-2">
                          <div class="d-flex pt-1">
                            <div
                              class="add_btn"
                              (click)="addTaxDeductionEntry(i)"
                            >
                              <mat-icon>add</mat-icon>
                            </div>
                            <div
                              class="add_btn ml-3"
                              (click)="removeTaxDeductionEntry(i, j)"
                            >
                              <mat-icon>delete</mat-icon>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </ng-container>

          <div style="padding-right: 100px" class="d-flex flex-row-reverse">
            <div class="pr-2 my-auto tax-dynamic-box">
              <div class="tax-title">Total tax deduction</div>
              <div class="tax-value">
                {{
                  this.paymentEntry.value.totalTaxDeduction +
                    "
                                " +
                    this.itemDetails.vendor_preferred_currency
                }}
              </div>
            </div>
          </div>
          <!-- <div
                    class="d-flex tax-title">
                        Overall Tax
                    </div>
                    <div
                    class="d-flex tax-value">
                        {{this.paymentEntry.value.overallTaxAmount+' '+this.itemDetails.vendor_preferred_currency}}
                    </div> -->

          <div class="row mt-4">
            <div class="col-10"></div>
            <div class="col-2">
              <button
                mat-mini-fab
                matTooltip="Next"
                style="background-color: #cf0001; color: white"
                (click)="stepper.next()"
              >
                <mat-icon>navigate_next</mat-icon>
              </button>
            </div>
          </div>
        </mat-step>
        <mat-step>
          <ng-template matStepLabel>Transaction</ng-template>
          <div class="col-12 d-flex justify-content-end" style="margin-bottom: 10px;" *ngIf="statutoryViewMode == 'edit'">
            <button mat-icon-button (click)="saveDraft()" mat-mini-fab [ngClass]="isDraftSpinnerLoading ? 'save-draft-btn-loading' : 'save-draft-btn'" class="save-draft-btn">
              <mat-icon style="font-size: 20px; margin-top:-8px;" matTooltip="Save as Draft" *ngIf="!isDraftSpinnerLoading">save</mat-icon>
              <mat-spinner  *ngIf="isDraftSpinnerLoading" matTooltip="Please wait..." class="spinner-align"
                                  diameter="20"></mat-spinner>
            </button>
          </div>
         
          <div class="transaction-stepper row">
            <div class="col-7" *ngIf="blockTreasuryFeatures == false">
              <div class="row mb-3" style="font-weight: 100; font-size: 18px">
                Vendor account
              </div>
              <div class="row">
                <div class="col-12 px-0">
                  <!-- <app-input-search class="drop-down" required="true" placeholder="Vendor account"
                                    [list]='ccExpenseEntry' formControlName='vendorAccount'>
                                    </app-input-search> -->
                  <mat-form-field
                    style="width: 100%"
                    appearance="outline"
                    class="create-account-field"
                  >
                    <mat-label>Vendor account</mat-label>
                    <input
                      readonly
                      matInput
                      placeholder=""
                      formControlName="vendorAccount"
                    />
                  </mat-form-field>
                </div>
              </div>
              <div class="row">
                <div class="col-6 px-0">
                  <app-input-search
                  class="drop-down"
                  placeholder="Vendor Reference"
                  [list]="vendorReferenceTypes"
                  formControlName="vendorRefType">
                  </app-input-search>
                </div>
                <div class="col-6">
                  <mat-form-field appearance="outline" style="width: 100%;">
                    <mat-label>Vendor description</mat-label>
                    <textarea required="true" matInput formControlName="vendorDescription"></textarea>
                  </mat-form-field>
                </div>
              </div>
              <div  class="row">
                <div class="col-6 px-0">
                  <app-input-search
                  *ngIf="financeBooksAvailable "
                  class="drop-down"
                  placeholder="Financial Books"
                  [list]="financialBooksMasterData"
                  formControlName="financeBooksfm"
                  (change)="formSelectedFb()"
                  >
                  </app-input-search>
                </div>
              </div>
              <ng-container *ngIf="showTransationSummary" class="row">
                <div class="d-flex flex-row mt-4" >
                  <div style="font-weight: 100; font-size: 18px">
                    Payment
                  </div>
                  <div class="ml-auto">
                    <mat-icon style="color: gray; font-size: 21px;">
                      error_outline
                    </mat-icon>
                    <span class="cheque-status-msg">
                      {{paymentChequeMsg}}
                    </span>
                  </div>
                </div>
                <div class="row my-2 py-3 cheque-card">
                  <!-- <div class="col-1 px-0 pt-2 pb-2">
                                            <button mat-icon-button class="ml-auto close-button">
                                                <mat-icon>
                                                    edit
                                                </mat-icon>
                                            </button>
                                        </div> -->
                  <div class="col-5">
                    <div
                      *ngIf="!this.paymentEntry.value.bank"
                      class="d-flex justify-content-center align-items-center pt-5"
                    >
                      <button
                        mat-raised-button
                        class="btn-not-active"
                        (click)="chooseBank()"
                      >
                        Choose bank
                      </button>
                    </div>
                    <div *ngIf="this.paymentEntry.value.bank">
                      <div class="row pt-2 pb-2">
                        <div class="col-4 pl-0 bank-title">Bank</div>
                        <div class="col-8 p-0 bank-value">
                          {{ this.paymentEntry.value.bank.bank_name }}
                        </div>
                      </div>
                      <div class="row pt-3 pb-2">
                        <div class="col-4 pl-0 bank-title">Bank Acc No</div>
                        <div class="col-8 p-0 bank-value">
                          {{ this.paymentEntry.value.bank.bank_acc_no }}
                        </div>
                      </div>
                      <div class="row pt-3">
                        <div class="col-4 bank-title pl-0">Bank branch</div>
                        <div class="col-8 p-0 bank-value">
                          {{ this.paymentEntry.value.bank.bank_branch }}
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-6">
                    <div class="row">
                      <div class="col-6 p-0 bank-title">Invoice No</div>
                      <div class="col-6 p-0">
                        <mat-form-field
                          appearance="outline"
                          class="create-account-field"
                        >
                          <mat-label>Invoice No</mat-label>
                          <input matInput readonly formControlName="voucherNo" />
                        </mat-form-field>
                      </div>
                    </div>
                    <div class="row">
                      <div class="col-6 p-0 bank-title">Cheque no / Ref no</div>
                      <div class="col-6 p-0">
                        <mat-form-field
                          appearance="outline"
                          class="create-account-field"
                        >
                          <mat-label>Cheque No</mat-label>
                          <input [required] = "isChequeNoMandatory" matInput formControlName="chequeNo" />
                        </mat-form-field>
                      </div>
                    </div>
                    <div class="row">
                      <div class="col-6 p-0 bank-title">Cheque amount</div>
                      <div class="col-6 p-0">
                        <mat-form-field
                          appearance="outline"
                          class="create-account-field"
                        >
                          <mat-label>Cheque amount *</mat-label>
                          <input
                            matInput
                            type="number"
                            formControlName="chequeAmount"
                          />
                        </mat-form-field>
                      </div>
                    </div>
                  </div>
                </div>

                <div *ngIf="this.activeBill != '' && this.activeBill != null && this.activeBill != undefined" class="row mt-3 mb-2 pt-2 pb-2 session-alert">
                  <div class="col-12 p-0">
                      <div class="row pb-2">
                          <div class="col active-bill">
                              <mat-icon class="mr-2" style="color:#cf0001; font-size: 22px;
                              vertical-align: middle;">warning</mat-icon>Warning !
                              <!-- Total Active bills : 3 -->
                          </div>
                      </div>
                      <div class="row">
                          <div class="col processed-session">
                              <!-- Bill <span style="color:red;">{{this.activeBill}}</span> is Processing By <span style="color:red;">{{this.billPaymentProcessingBy}}</span> on {{this.billPaymentMode}} Page ! -->
                              <!-- This <span style="color:red;">PR {{this.activePR}}'s Invoice</span> under <span style="color:red;">{{this.billPaymentProcessingBy}}</span> processing on the {{this.billPaymentMode}} screen -->
                              This Invoice of<span style="color:red;"> PR {{this.activePR}}</span> is being processed by <span style="color:red;">{{this.billPaymentProcessingBy}}</span> on the {{this.billPaymentMode}} screen
                            </div>
                      </div>
                  </div>
              </div>

                <div class="d-flex justify-content-center">
                  <button
                    mat-raised-button
                    class="btn-active justify-content-center"
                    (click)="VerifyStatutoryDetails()"
                    *ngIf="(isMovedToBank == 0 || isMovedToBank == 4) && !this.paymentEntry.get('isChecked').value && !isLoading"
                  >
                    Verify
                  </button>
                  <button
                    mat-raised-button
                    class="btn-active justify-content-center"
                    style="background-color: green"
                    *ngIf="(isMovedToBank == 2 || isMovedToBank == 1)"
                    disabled
                  >
                    ✔ Verified
                  </button>
                  <button
                    mat-raised-button
                    class="btn-active justify-content-center ml-3"
                    (click)="moveToBank()"
                    *ngIf="!isPaymentCompleted  && !this.paymentEntry.get('isChecked').value && !isLoading"
                  >
                    Move to bank
                  </button>
                  <button
                  mat-raised-button
                  class="btn-active justify-content-center ml-3"
                  (click)="moveToBank()"
                  style="background-color: green"
                  *ngIf="isPaymentCompleted"
                  disabled
                  >
                  ✔ Moved to bank
                  </button>
                  <button
                    mat-raised-button
                    class="btn-active justify-content-center ml-3"
                   (click)="takeAdvance()" 
                   *ngIf="this.paymentEntry.get('isChecked').value"
                  >
                    Take Advance
                  </button>
                  <mat-spinner [ngStyle]="{'margin-left':'10px' }" *ngIf="isLoading" matTooltip="Please wait..." class="spinner-align"
                    diameter="30"></mat-spinner>
                </div>
              </ng-container>
            </div>
            <div class="col-5">
              <ng-container *ngIf="showTransationSummary" class="row">
                <ng-template [ngTemplateOutlet]="transationSummary">
                </ng-template>
              </ng-container>
        
              <ng-container *ngIf="showPartialPaymentSummary" class="row">
                <ng-template [ngTemplateOutlet]="partialPaymentSummary">
                </ng-template>
              </ng-container>

              <ng-template #transationSummary>
                <div class="summary-card py-3">
                  <div
                    class="row px-3"
                  >
                    <mat-icon style="color: gray;">
                      summarize
                    </mat-icon>
                     <span class="px-2"  style="font-weight: 100; font-size: 18px">Summary</span>
                  </div>
                  <div class="row my-2">
                    <div class="col-12 px-0">
                      <!-- <div class="row my-2">
                                                <div class="col-6 summary-title">
                                                    Tax addition account :
                                                </div>
                                                <div class="col-6 px-0 value-account">
                                                    {{this.paymentEntry.value.tax_addition_gl_cc_name ? this.paymentEntry.value.tax_addition_gl_cc_name : '-'}}
                                                </div>
                                            </div>
                                            <div class="row my-2">
                                                <div class="col-6 summary-title">
                                                    Tax deduction account :
                                                </div>
                                                <div class="col-6 px-0 value-account">
                                                    {{this.paymentEntry.value.tax_deduction_gl_cc_name ? this.paymentEntry.value.tax_deduction_gl_cc_name : '-'}}
                                                </div>
                                            </div> -->
                      <div class="row my-2">
                        <div class="col-6 summary-title">
                          Total Expense amount :
                        </div>
                        <div class="col-6 px-0 summary-value">
                          {{
                            this.paymentEntry.value.totalExpenseAmount +
                              "
                                                    " +
                              this.itemDetails.vendor_preferred_currency
                          }}
                        </div>
                      </div>
                      <div class="row my-2">
                        <div class="col-6 summary-title">Total Tax added :</div>
                        <div class="col-6 px-0 summary-value">
                          {{
                            this.paymentEntry.value.totalTaxAddition +
                              "
                                                    " +
                              this.itemDetails.vendor_preferred_currency
                          }}
                        </div>
                      </div>
                      <div class="row my-2">
                        <div class="col-6 summary-title">
                          Total Tax deduced :
                        </div>
                        <div class="col-6 px-0 summary-value">
                          {{
                            this.paymentEntry.value.totalTaxDeduction +
                              "
                                                    " +
                              this.itemDetails.vendor_preferred_currency
                          }}
                        </div>
                      </div>
                      <div class="row my-2">
                        <div class="col-6 summary-title">Overall tax :</div>
                        <div class="col-6 px-0 summary-value">
                          {{
                            this.paymentEntry.value.overallTaxAmount +
                              "
                                                    " +
                              this.itemDetails.vendor_preferred_currency
                          }}
                        </div>
                      </div>
                      <div class="row my-2">
                        <div class="col-6 summary-title">Total amount :</div>
                        <div class="col-6 px-0 value-account">
                          {{
                            this.paymentEntry.value.finalAmount +
                              "
                                                    " +
                              this.itemDetails.vendor_preferred_currency
                          }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ng-template>

              <ng-template #partialPaymentSummary>
                <div>
                  <div class="row my-3 pt-3">
                    <mat-icon style="color: gray;">
                      history
                    </mat-icon>
                    <span class="px-2" style="font-weight:100;font-size:18px"> Partial payment history</span>
                  </div>
                  <div class="row">
                    <table class="table table-hover">
                      <thead>
                        <tr>
                          <th scope="col">#</th>
                          <th scope="col">Amount paid</th>
                          <th scope="col">Payment date</th>
                          <th scope="col">Payment Type</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr 
                        *ngFor="
                        let e of partialPaymentDetails;
                        let i = index
                        "
                        >
                          <th scope="row">{{ i+1 }}</th>
                          <td>{{ e.amount_paid ? 
                            e.amount_paid + " " + this.itemDetails.vendor_preferred_currency
                            : '-'  }}</td>
                          <td>{{ e.payment_date ?  (e.payment_date | date:'dd MMM YYYY') : '-' }}</td>
                          <td>{{e.type}}</td>
                      </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </ng-template>
            </div>
          </div>
          <pre>
                        <!-- {{ paymentEntry.value | json }} -->


                    </pre>
        </mat-step>
      </mat-horizontal-stepper>
    </div>
  </form>
</div>

<!-- {{ inPrefilledJson | json }} -->
