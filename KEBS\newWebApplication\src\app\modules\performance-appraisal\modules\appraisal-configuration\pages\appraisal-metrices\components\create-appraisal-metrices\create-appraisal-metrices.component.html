
<div class="container-fluid pt-2 pb-2 create-cycle-styles">
  <div style="background-color: #F9F9F9;">
    <div class="row">
      <div class="col-11 d-flex pt-2 headingBold">
        <div mat-icon-button class="bubble mt-1">
          <mat-icon class="iconButton">trending_up</mat-icon>
        </div>
        <div *ngIf="!inData.edit" class="pl-2" style="padding-top: 7px">
          Create Appraisal Competencies
        </div>
        <div *ngIf="inData.edit" class="pl-2" style="padding-top: 7px">
          Edit Appraisal Competencies
        </div>
      </div>
        <div class="col-1 mt-0 d-flex">
            <button mat-icon-button class="ml-auto close-button mt-1" (click)="closeDialog();">
              <mat-icon matTooltip="Close" class="close-Icon">close</mat-icon>
            </button>
        </div>
    </div>
  </div>
<div class="row pt-3">
<form class="col-12" [formGroup]="appraisalMetricesForm">
  <div class="col-12 mr-2">
    <!-- createAppraisalMetrices -->
    <div class="col-12 p-0">
      <div class="row pt-2">
        <span style="font-weight: 500; font-size: 15px; color: #cf0001">Appraisal Competence Details:</span>
      </div>
      <div class="row pt-3 pl-0 d-flex">
        <div class="col-6 pl-0">
          <mat-form-field class="w-100 form-field-class" appearance="outline">
            <mat-label class="input__label"
              >Enter Appraisal Competence Name</mat-label
            >
            <input
              matInput
              placeholder="Metrices Name"
              formControlName="appraisalMetricName"
            />
          </mat-form-field>
        </div>
        <div class="col-6 pl-0">
          <mat-form-field class="w-100 form-field-class" appearance="outline">
            <mat-label class="input__label"
              >Enter Appraisal Competence Max Score</mat-label
            >
            <input
              type="Number"
              matInput
              placeholder="Max Score"
              formControlName="appraisalMetricMaxScore"
            />
          </mat-form-field>
        </div>
      </div>

      <div class="row pt-2 pl-0 d-flex">
        <div class="col-6 pl-0">
          <mat-form-field class="w-100 form-field-class" appearance="outline">
            <mat-label class="input__label"
              >Select Appraisal Response Type</mat-label
            >
            <mat-select
              formControlName="appraisalMetricResponseType"
              placeholder="Response Type"
            >
              <ng-container
                *ngFor="
                  let appraisalMetricResponseType of appraisalMetricResponseTypes
                "
              >
                <mat-option [value]="appraisalMetricResponseType.value">{{
                  appraisalMetricResponseType.name
                }}</mat-option>
              </ng-container>
            </mat-select>
          </mat-form-field>
        </div>
        <div class="col-6 pl-0">
          <mat-form-field class="w-100 form-field-class" appearance="outline">
            <mat-label class="input__label"
              >Select Appraisal Evaluation Type</mat-label
            >
            <mat-select
              placeholder="Evaluation Type"
              formControlName="appraisalMetricEvaluationType"
            >
              <ng-container
                *ngFor="
                  let appraisalMetricEvaluationType of appraisalMetricEvaluationTypes
                "
              >
                <mat-option [value]="appraisalMetricEvaluationType.value">{{
                  appraisalMetricEvaluationType.name
                }}</mat-option>
              </ng-container>
            </mat-select>
          </mat-form-field>
        </div>
      </div>

      <div class="row pt-2 pl-0 d-flex">
        <div class="col-6 pl-0">
          <mat-form-field class="w-100 form-field-class" appearance="outline">
            <mat-label class="input__label"
              >Enter Appraisal Competence Unit Name</mat-label
            >
            <input
              matInput
              placeholder="Unit Name"
              formControlName="appraisal_metric_unit"
            />
          </mat-form-field>
        </div>        
      </div>

      <div class="row pl-0 pt-3" *ngIf="!inData.edit">
        <div class="col-12 pl-0 d-flex p-0">
        <div class="col-2 pl-0 pr-0" style="padding-top: 2px;">
          <span style="font-weight: 500; font-size: 14px; color: #cf0001">Select Configurations:</span>
        </div>
        <div class="col-10 pl-0 pr-0 d-flex">
          <div class="col-6 d-flex p-0">
          <div class="col-4 pl-0">
            <span class="pl-2 pr-0">
              <mat-checkbox class="isFeedback" formControlName="isFeedback"
                >is_feedback</mat-checkbox
              >
            </span>
          </div>
          <div class="col-3 pl-0 pr-0">
            <span class="pl-2">
              <mat-checkbox
                class="isActive"
                formControlName="appraisalMetricIsActive"
                >is_active</mat-checkbox
              >
            </span>
          </div>
          <div class="col-5 pl-2 pr-0">
            <span class="pl-2">
              <mat-checkbox class="isActive" formControlName="is_self_evaluation"
                >is_self_evaluation</mat-checkbox
              >
            </span>
          </div>
        </div>
        <div class="col-6 pl-3 d-flex pr-0">
          <div class="col-5 pl-0 pr-0">
            <span class="pl-2">
              <mat-checkbox class="isActive" formControlName="is_master_data"
                >is_master_data</mat-checkbox
              >
            </span>
          </div>
          <div class="col-7 pl-0 pr-0">
            <span class="pl-2">
              <mat-checkbox class="isActive" formControlName="is_external_module"
                >is_external_module</mat-checkbox
              >
            </span>
          </div>
        </div>
        </div>
      </div>
        
      </div>

      <!-- Master Data -->
      <div class="row d-flex pl-0 pt-2">
        <div *ngIf="appraisalMetricesForm.value.is_master_data" class="col-6 pl-0">
          <mat-form-field class="w-100 form-field-class" appearance="outline">
            <mat-label class="input__label">Select Master Data</mat-label>
            <mat-select
              placeholder="Master Data"
              formControlName="masterDataURL"
            >
              <ng-container *ngFor="let masterData of masterDataURL">
                <mat-option
                  [value]="masterData.value"
                  (click)="masterDataSelected(masterData.name)"
                  >{{ masterData.name }}</mat-option
                >
              </ng-container>
            </mat-select>
          </mat-form-field>
        </div>

        <div *ngIf="appraisalMetricesForm.value.is_external_module" class="col-6 pl-0">
          <mat-form-field class="w-100 form-field-class" appearance="outline">
            <mat-label class="input__label">Select External Module</mat-label>
            <mat-select
              placeholder="External Module"
              formControlName="externalModuleURL"
            >
              <ng-container *ngFor="let externalModule of externalModuleURL">
                <mat-option
                  [value]="externalModule.value"
                  (click)="externalModuleSelected(externalModule.name)"
                  >{{ externalModule.name }}</mat-option
                >
              </ng-container>
            </mat-select>
          </mat-form-field>
        </div>
      </div>
     
      
    </div>

    <div class="col-12 p-0" *ngIf="!inData.edit">
    <div class="row pt-2 pb-1">
      <span class="pl-0" style="font-weight: 500; font-size: 15px; color: #cf0001"> Approver Details:</span>
    </div>
  
    <div class="row pt-2" >
      <div class="col-12 pt-2 d-flex p-0" *ngIf="!inData.edit">
        <div class="col-3 p-0">
          <span class="pl-0" style="font-weight: 500; font-size: 14px; color: #cf0001">Choose Type Of Approval:</span>
        </div>
        <div class="col-9 p-0 d-flex">
        <div class="col-3 p-0">
          
            <span class="pl-0">
              <mat-checkbox class="isActive" formControlName="is_manager"
                >Manager</mat-checkbox
              >
            </span>
          
         
        </div>

        
        <div class="col-4 p-0">
          <span class="pl-0">
            <mat-checkbox class="isActive" formControlName="is_peers"
              >Peers</mat-checkbox
            >
          </span>
        </div>

        <div class="col-5 pl-0">
          <span class="pl-0">
            <mat-checkbox class="isActive" formControlName="is_cutomers"
              >Customers</mat-checkbox
            >
          </span>
        </div>
      </div>
      </div>

      <div class="col-12 pt-2 d-flex p-0" *ngIf="appraisalMetricesForm.value.is_manager && !inData.edit">
        <div class="col-3 p-0">
          <span class="pl-0" style="font-weight: 500; font-size: 14px; color: #cf0001">Choose Type Of Approvers:</span>
        </div>
        <div class="col-9 d-flex p-0" >
       
          <!-- Direct Managers -->
          <div class="col-3 pl-0">
              <section>
                <mat-checkbox
                  class="isFeedback"
                  formControlName="directMangers"
                  >Direct Managers</mat-checkbox
                >
              </section>
            <!-- <div
              *ngIf="appraisalMetricesForm.value.directMangers"
              class="col-3"
            >
              <mat-form-field class="form-field-class" appearance="outline">
                <mat-label class="input__label"
                  >Direct Manager Weightage</mat-label
                >
                <input
                  type="Number"
                  matInput
                  placeholder="weightage"
                  formControlName="directMangersWeightage"
                />
              </mat-form-field>
            </div> -->
          </div>

          <!-- Cost Center Owners -->
          <div class="col-4 pl-0">
           <section>
                <mat-checkbox
                  class="isFeedback"
                  formControlName="costCenterApprovers"
                  >Cost Center Approvers</mat-checkbox
                >
              </section>
            
            <!-- <div
              *ngIf="appraisalMetricesForm.value.costCenterApprovers"
              class="col-3"
            >
              <mat-form-field class="form-field-class" appearance="outline">
                <mat-label class="input__label"
                  >Cost Center Owner Weightage</mat-label
                >
                <input
                  type="Number"
                  matInput
                  placeholder="weightage"
                  formControlName="costCenterApproversWeightage"
                />
              </mat-form-field>
            </div> -->
          </div>

          <!-- fixed org -->
          <div class="col-2 pl-0">
              <section>
                <mat-checkbox class="isFeedback" formControlName="fixedOrgs"
                  >Fixed Org</mat-checkbox
                >
              </section>
            
            
            <!-- <div
              *ngIf="
                appraisalMetricesForm.value.fixedOrgs &&
                appraisalMetricesForm.value.orgCode
              "
              class="col-3"
            >
              <mat-form-field class="form-field-class" appearance="outline">
                <mat-label class="input__label"
                  >Fixed Org Weightage</mat-label
                >
                <input
                  type="Number"
                  matInput
                  placeholder="weightage"
                  formControlName="fixedOrgsWeightage"
                />
              </mat-form-field>
            </div> -->
          </div>

          <!-- person -->
          <div class="col-3 pl-0">
            
              <section>
                <mat-checkbox class="isFeedback" formControlName="individual"
                  >Individual Approvers</mat-checkbox
                >
              </section>
           
            
            <!-- <div
              *ngIf="
                appraisalMetricesForm.value.individual &&
                appraisalMetricesForm.value.individualId
              "
              class="col-3"
            >
              <mat-form-field class="form-field-class" appearance="outline">
                <mat-label class="input__label"
                  >individual Approver Weightage</mat-label
                >
                <input
                  type="Number"
                  matInput
                  placeholder="weightage"
                  formControlName="individualWeightage"
                />
              </mat-form-field>
            </div> -->
          </div>
        
      </div>
      </div>

      <div *ngIf="appraisalMetricesForm.value.fixedOrgs && !inData.edit" class="col-12 pt-2 p-0 d-flex">
        <div class="col-3 pt-2 p-0">
          <span style="font-weight: 500; font-size: 14px; color: #cf0001">Choose Organization :</span>
        </div>
        <div class="col-5 p-0">
        <app-appraisal-multi-select
          class="form-field-class"
          formControlName="orgCode"
          placeholder="Organization"
          [list]="orgList"
        >
        </app-appraisal-multi-select>
      </div>
      </div>

      <div *ngIf="appraisalMetricesForm.value.individual && !inData.edit" class="col-12 pt-2 p-0 d-flex">
        <div class="col-3 pt-2 p-0">
          <span style="font-weight: 500; font-size: 14px; color: #cf0001">Choose Approver:</span>
        </div>
        <div class="col-5 p-0">
        <app-appraisal-multi-select
          class="form-field-class"
          formControlName="individualId"
          placeholder="Approvers"
          [list]="assocList"
        >
        </app-appraisal-multi-select>
        </div>
      </div>

      <div class="col-12 d-flex pl-0 pt-2" *ngIf="!inData.edit">
        
          <div class="col-3 pt-2 p-0">
            <span style="font-weight: 500; font-size: 14px; color: #cf0001">Choose Approver levels:</span>
          </div>
       
        <div class="col-9 d-flex p-0">
        <div class="col-4 p-0">
          <mat-form-field class="w-100 form-field-class" appearance="outline">
            <mat-label class="input__label">No of Approver Levels</mat-label>
            <mat-select placeholder="Levels" formControlName="approverLevels">
              <ng-container *ngFor="let item of apprLevels">
                <mat-option [value]="item">{{ item }}</mat-option>
              </ng-container>
            </mat-select>
          </mat-form-field>
        </div>
        <div class="col-4 pl-3">
          <mat-form-field class="w-100 form-field-class" appearance="outline">
            <mat-label class="input__label">No of Mandatory Levels</mat-label>
            <mat-select
              placeholder="Mandatory levels"
              formControlName="mandatoryLevels"
            >
              <ng-container *ngFor="let item of mandLevels">
                <mat-option [value]="item">{{ item }}</mat-option>
              </ng-container>
            </mat-select>
          </mat-form-field>
        </div>
        <div class="col-4 p-0">
          <app-input-search
            placeholder="Max designation"
            [list]="desgList"
            formControlName="maxDesg"
          ></app-input-search>
        </div>
      </div>
      </div>

      <!-- Evaluation Operation -->

      <div class="col-12 d-flex pl-0 pt-2" *ngIf="!inData.edit">
        <div class="col-3 pt-2 p-0">
          <span style="font-weight: 500; font-size: 14px; color: #cf0001">Select Evaluator Operation:</span>
        </div>
        <div class="col-9 pt-2 d-flex p-0">
          <mat-radio-group formControlName="evaluationOperation">
            <mat-radio-button
              value="AND"
              >AND</mat-radio-button
            >
            <mat-radio-button style="margin-left: 16px" value="OR"
              >OR</mat-radio-button
            >
          </mat-radio-group>
        </div>
      </div>
    </div>
  </div>

    <!-- Submission -->
    <div class="row pt-2" >
      <div *ngIf="!inData.edit" class="col-12 pt-3 d-flex my-auto justify-content-end">
        <button mat-icon-button class="iconbtn" [matTooltip]="'Create Competence'"
        [ngStyle]="{'background-color': isMetricBeingCreated ? '#f3f3f3' : '#cf0001'}"
        [ngClass]="{'is-disabled': isMetricBeingCreated}" (click)="submit()" [disabled]="isMetricBeingCreated">
        <mat-icon *ngIf="!isMetricBeingCreated">done_all</mat-icon>
            <mat-spinner *ngIf="isMetricBeingCreated" diameter='30' class='spinner-align'></mat-spinner>
        </button>
    </div>
   
      <div *ngIf="inData.edit" class="col-12 pt-3 d-flex my-auto justify-content-end">
        <button mat-icon-button class="iconbtn" [matTooltip]="'Update Competence'"
        [ngStyle]="{'background-color': isMetricBeingUpdated ? '#f3f3f3' : '#cf0001'}"
        [ngClass]="{'is-disabled': isMetricBeingUpdated}" (click)="edit()" [disabled]="isMetricBeingUpdated">
        <mat-icon *ngIf="!isMetricBeingUpdated">done_all</mat-icon>
            <mat-spinner *ngIf="isMetricBeingUpdated" diameter='30' class='spinner-align'></mat-spinner>
        </button>
    </div> 

      <!-- <div class="col-11"></div>
      <div class="col-1">
        <button
          class="ml-auto mini-tick mb-4"
          mat-mini-fab
          (click)="submit()"
          [disabled]="isMetricBeingCreated"
        >
          <mat-spinner
            *ngIf="isMetricBeingCreated; else showTick"
            diameter="30"
            [color]="'white'"
          ></mat-spinner>
          <ng-template #showTick>
            <mat-icon>done_all</mat-icon>
          </ng-template>
        </button>
      </div> -->
    </div>
  </div>
</form>
</div>
</div>

