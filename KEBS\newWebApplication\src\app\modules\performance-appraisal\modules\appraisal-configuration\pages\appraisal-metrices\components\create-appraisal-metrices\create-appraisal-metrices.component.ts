import { Component, Inject, OnInit } from '@angular/core';

import { FormArray, FormGroup, FormBuilder, Validators } from '@angular/forms';

import { AppraisalMetricesService } from '../../services/appraisal-metrices.service';
import { UtilityService } from '../../../../../../../../services/utility/utility.service';
import {
  MatDialog,
  MatDialogRef,
  MAT_DIALOG_DATA,
} from '@angular/material/dialog';
import { takeUntil } from 'rxjs/operators';
import { Subject } from 'rxjs';

@Component({
  selector: 'app-create-appraisal-metrices',
  templateUrl: './create-appraisal-metrices.component.html',
  styleUrls: ['./create-appraisal-metrices.component.scss'],
})
export class CreateAppraisalMetricesComponent implements OnInit {
  protected _onDestroy = new Subject<void>();
  createAppraisalMetricesResponse: any;
  createWorkFlowTableResponse: any;
  isMetricBeingCreated: boolean = false;
  isMetricBeingUpdated: boolean = false;
  dialogStatus: String = 'No Change';
  masterDataName: '';
  constructor(
    private fb: FormBuilder,
    private _AppraisalMetricesService: AppraisalMetricesService,
    public dialogRef: MatDialogRef<CreateAppraisalMetricesComponent>,
    @Inject(MAT_DIALOG_DATA) public inData: any,
    private _util: UtilityService
  ) {}

  appraisalMetricResponseTypes: any = [
    { name: 'Attachments', value: 'attachment' },
    { name: 'Organizational Activities', value: 'organizational_activities' },
    { name: 'Rating ', value: 'rating' },
    { name: 'Feedback ', value: 'feedback' },
    { name: 'Comment ', value: 'comment' },
    { name: 'External Module', value: 'externalModule' },
    { name: 'None', value: 'none' },
  ];

  appraisalMetricEvaluationTypes: any = [
    { name: 'Scoring', value: 'scoring' },
    { name: 'Boolean', value: 'boolean' },
    { name: 'Rating & Feedback', value: 'ratingAndFeedback' },
    { name: 'Rating', value: 'rating' },
    { name: 'External Module ', value: 'externalModule' },
    { name: 'Feedback Only ', value: 'feedback_only' },
  ];

  masterDataURL: any = [
    {
      name: 'Accounts',
      value: '/api/appraisal/configuration/getAccountsByName',
    },
    {
      name: 'Oppournities',
      value: '/api/appraisal/configuration/getOpportunityByName',
    },
  ];

  externalModuleURL: any = [
    {
      name: 'Okr/IKR',
      value: '/api/okr/visibility/getAllOrgOKR',
    },
  ];

  appraisalMetricesForm = this.fb.group({
    appraisalMetricName: ['', Validators.required],
    appraisalMetricMaxScore: [null, Validators.required],
    appraisalMetricResponseType: ['', Validators.required],
    appraisalMetricEvaluationType: ['', Validators.required],
    masterDataURL: [''],
    externalModuleURL: [''],
    isFeedback: false,
    appraisalMetricIsActive: true,
    is_self_evaluation: false,
    appraisal_metric_unit:[''],
    is_manager: true,
    is_peers: false,
    is_cutomers: false,
    directMangers: false,
    directMangersWeightage: [null, Validators.max(100)],
    costCenterApprovers: false,
    costCenterList: Array,
    costCenterApproversWeightage: [null, Validators.max(100)],
    fixedOrgs: false,
    orgCode: [''],
    fixedOrgsWeightage: [null, Validators.max(100)],
    individual: false,
    individualId: [''],
    individualWeightage: [null, Validators.max(100)],
    evaluationOperation: ['', Validators.required],
    approverLevels: [Number, Validators.required],
    mandatoryLevels: [Number, Validators.required],
    maxDesg: ['', Validators.required],
    is_master_data: false,
    master_data_name: [''],
    is_external_module: false,
    external_module_name: [''],
  });

  masterDataSelected(masterDataName) {
    // this.masterDataName = masterDataName;
    this.appraisalMetricesForm
      .get('master_data_name')
      .patchValue(masterDataName);
  }

  externalModuleSelected(externalModuleName) {
    this.appraisalMetricesForm
      .get('external_module_name')
      .patchValue(externalModuleName);
  }

  orgList: any = [];
  assocList: any = [];
  desgList: any = [];
  apprLevels = [1, 2, 3, 4, 5];
  mandLevels: any = [];

  setEvaluatorsWeightage() {
    return {
      M: 100,
      P: 0,
      C: 0,
      // I: this.appraisalMetricesForm.controls['directMangersWeightage'].value,
      // C: this.appraisalMetricesForm.controls['costCenterApproversWeightage']
      //   .value,
      // O: this.appraisalMetricesForm.controls['fixedOrgsWeightage'].value,
      // P: this.appraisalMetricesForm.controls['individualWeightage'].value,
    };
  }

  getWorkflowDetails(metriceId) {
    return {
      appraisal_metric_name:
        this.appraisalMetricesForm.controls['appraisalMetricName'].value,
      sub_application_id: metriceId,
      no_of_levels: this.appraisalMetricesForm.controls['approverLevels'].value,
      no_of_mand_levels:
        this.appraisalMetricesForm.controls['mandatoryLevels'].value,
      max_desgn_level: this.appraisalMetricesForm.controls['maxDesg'].value,
      approval_type: this.getApprovalType(),
      approval_params: this.getApprovalParams(),
      evaluationOperation:
        this.appraisalMetricesForm.controls['evaluationOperation'].value,
    };
  }

  getApprovalType() {
    let approval_type = '';

    if (this.appraisalMetricesForm.controls['directMangers'].value)
      approval_type += 'I';

    if (this.appraisalMetricesForm.controls['costCenterApprovers'].value)
      approval_type += 'C';

    if (this.appraisalMetricesForm.controls['fixedOrgs'].value)
      approval_type += 'F';

    if (this.appraisalMetricesForm.controls['individual'].value)
      approval_type += 'P';

    return approval_type;
  }

  getApprovalParams() {
    let approval_params = null;

    if (this.appraisalMetricesForm.controls['fixedOrgs'].value)
      approval_params = {
        f: this.appraisalMetricesForm.controls['orgCode'].value,
      };

    if (this.appraisalMetricesForm.controls['individual'].value) {
      if (!approval_params)
        approval_params = {
          p: this.appraisalMetricesForm.controls['individualId'].value,
        };
      else
        approval_params['p'] =
          this.appraisalMetricesForm.controls['individualId'].value;
    }

    return approval_params ? JSON.stringify(approval_params) : null;
  }

  submit() {
    console.log(this.appraisalMetricesForm, this.getApprovalType());

    if (
      this.appraisalMetricesForm.status == 'VALID' &&
      this.getApprovalType() != ''
    ) {
      this.isMetricBeingCreated = true;
      this._AppraisalMetricesService
        .createAppraisalMetrices(this.getCreateAppraisalMetricesResponse())
        .subscribe(
          (result: any) => {
            if (result.error == 'N')
              this._AppraisalMetricesService
                .createWorkflowBasedOnConfig(
                  this.getWorkflowDetails(result.data._id)
                )
                .pipe(takeUntil(this._onDestroy))
                .subscribe(
                  (res) => {
                    if (res['messType'] == 'S')
                      this._AppraisalMetricesService
                        .updateAppraisalMetricesWorkFlowId({
                          appraisalMetricesId: result.data._id,
                          workFlowId: res['data'],
                        })
                        .subscribe(
                          (result: any) => {
                            if (result.error == 'N') {
                              this.dialogStatus = 'Update Required';
                              this.isMetricBeingCreated = false;
                              this._util.showMessage(
                                'Evaluation Metrices Created Successfully',
                                'Dismiss'
                              );
                             // this.dialogRef.close(this.dialogStatus);
                            } else
                              this._util.showMessage(
                                'Error Creating Evaluation Metrices',
                                'Dismiss'
                              );
                            this.isMetricBeingCreated = false;
                          },
                          (error) => {
                            console.log('error updating workflow id', error);
                          }
                        );
                    else
                      this._util.showMessage(
                        'Error Creating Workflow Config',
                        'Dismiss'
                      );
                  },
                  (err) => {
                    console.log(err);
                    this._util.showMessage(
                      'Error Creating Workflow Config',
                      'Dismiss'
                    );
                  }
                );
            else
              this._util.showMessage(
                'Error Creating Evaluation Metrices',
                'Dismiss'
              );
          },
          (error) => {
            console.log(error);
            this._util.showMessage(
              'Error Creating Evaluation Metrices',
              'Dismiss'
            );
          }
        );
    } else
      this._util.showMessage(
        'Please fill in all the mandatory fields',
        'Dismiss'
      );
  }

  edit(){
    this.isMetricBeingUpdated=true;
    let req={
      id:this.inData.data._id,
      data: this.getCreateAppraisalMetricesResponse()
    }
    this._AppraisalMetricesService
        .updateAppraisalMetrices(req)
        .subscribe(
          (result: any) => {
            if (result.error == 'N')
             {
              this.isMetricBeingUpdated=false;
              this._util.showMessage(
                'Evaluation Metrices Updated Successfully',
                'Dismiss'
              );
             }
          },
          (error) => {
            console.log(error);
            this.isMetricBeingUpdated=false;
            this._util.showMessage(
              'Error Creating Evaluation Metrices',
              'Dismiss'
            );
          }
        );
  }

  getCreateAppraisalMetricesResponse() {
    return {
      appraisal_metric_name:
        this.appraisalMetricesForm.controls['appraisalMetricName'].value,
      appraisal_metric_max_score:
        this.appraisalMetricesForm.controls['appraisalMetricMaxScore'].value,
      appraisal_metric_response_type:
        this.appraisalMetricesForm.controls['appraisalMetricResponseType']
          .value,
      appraisal_metric_evaluation_type:
        this.appraisalMetricesForm.controls['appraisalMetricEvaluationType']
          .value,
      is_feedback: this.appraisalMetricesForm.controls['isFeedback'].value,
      is_self_evaluation: this.appraisalMetricesForm.controls['is_self_evaluation'].value,
      appraisal_metric_unit: this.appraisalMetricesForm.controls['appraisal_metric_unit'].value,
      appraisal_metric_is_active:
        this.appraisalMetricesForm.controls['appraisalMetricIsActive'].value,
      appraisal_metric_evaluator_weightage: this.setEvaluatorsWeightage(),
      is_master_data:
        this.appraisalMetricesForm.controls['is_master_data'].value,
      master_data_url:
        this.appraisalMetricesForm.controls['masterDataURL'].value,
      master_data_name:
        this.appraisalMetricesForm.controls['master_data_name'].value,
      is_external_module:
        this.appraisalMetricesForm.controls['is_external_module'].value,
      external_module_url:
        this.appraisalMetricesForm.controls['externalModuleURL'].value,
      external_module_name:
        this.appraisalMetricesForm.controls['external_module_name'].value,
      evaluator_operation:
        this.appraisalMetricesForm.controls['evaluationOperation'].value,
      is_customer_review:
        this.appraisalMetricesForm.controls['is_cutomers'].value,
      is_peer_review: this.appraisalMetricesForm.controls['is_peers'].value,
    };
  }

  ngOnInit() {
    if(this.inData.edit == true){
    console.log(this.inData.data)
    this.patchForm(this.inData.data)
    }
    this._AppraisalMetricesService
      .getOrgDesgData()
      .pipe(takeUntil(this._onDestroy))
      .subscribe((res) => {
        this.orgList = res['org_data'];
        this.desgList = res['desg_data'];
      });
    this._AppraisalMetricesService
      .getEmpMasterForWorkFlow()
      .pipe(takeUntil(this._onDestroy))
      .subscribe((res) => {
        this.assocList = res['data'];
      });
    this.resolveSubscriptions();
  }

  patchForm(data){
    this.appraisalMetricesForm.patchValue({
    appraisalMetricName: data.appraisal_metric_name,
    appraisalMetricMaxScore: data.appraisal_metric_max_score,
    appraisalMetricResponseType: data.appraisal_metric_response_type,
    appraisalMetricEvaluationType: data.appraisal_metric_evaluation_type,
    masterDataURL: data.master_data_url ? data.master_data_url : '',
    externalModuleURL: data.external_module_url ? data.external_module_url : '',
    isFeedback: data.is_feedback ? true : false,
    appraisalMetricIsActive: data.appraisal_metric_is_active ? true : false,
    is_self_evaluation: data.is_self_evaluation ? true : false,
    appraisal_metric_unit: data.appraisal_metric_unit ? data.appraisal_metric_unit : '',
    evaluationOperation: data.evaluator_operation,
    is_master_data: data.is_master_data ? true : false,
    master_data_name: data.master_data_name ? data.master_data_name : '',
    is_external_module: data.is_external_module ? true : false,
    is_peers: data.is_peer_review ? true : false,
    is_cutomers: data.is_customer_review ? true : false,
    external_module_name: data.external_module_name ?data.external_module_name :'',
    })
  }
  resolveSubscriptions() {
    this.appraisalMetricesForm
      .get('directMangers')
      .valueChanges.subscribe((res) => {
        if (res)
          this.appraisalMetricesForm
            .get('directMangersWeightage')
            .setValidators([Validators.required, Validators.max(100)]);
      });

    this.appraisalMetricesForm
      .get('costCenterApprovers')
      .valueChanges.subscribe((res) => {
        if (res)
          this.appraisalMetricesForm
            .get('costCenterApproversWeightage')
            .setValidators([Validators.required, Validators.max(100)]);
      });

    this.appraisalMetricesForm
      .get('fixedOrgs')
      .valueChanges.subscribe((res) => {
        if (res) {
          this.appraisalMetricesForm
            .get('fixedOrgsWeightage')
            .setValidators([Validators.required, Validators.max(100)]);
          this.appraisalMetricesForm
            .get('orgCode')
            .setValidators(Validators.required);
        }
      });

    this.appraisalMetricesForm
      .get('individual')
      .valueChanges.subscribe((res) => {
        if (res) {
          this.appraisalMetricesForm
            .get('individualWeightage')
            .setValidators([Validators.required, Validators.max(100)]);
          this.appraisalMetricesForm
            .get('individualId')
            .setValidators(Validators.required);
        }
      });

    this.appraisalMetricesForm
      .get('approverLevels')
      .valueChanges.subscribe((res) => {
        if (res > 0) {
          this.mandLevels = [];
          for (let i = 0; i < res; i++) this.mandLevels.push(i + 1);
        }
      });
  }

  closeDialog = () => {
    this.dialogRef.close('Update Required');
    }

  ngOnDestroy() {
    this._onDestroy.next();
    this._onDestroy.complete();
  }
}
