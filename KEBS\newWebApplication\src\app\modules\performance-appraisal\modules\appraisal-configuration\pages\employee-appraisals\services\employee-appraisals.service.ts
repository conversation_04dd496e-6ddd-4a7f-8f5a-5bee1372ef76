import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';



@Injectable({
  providedIn: 'root',
})
export class EmployeeAppraisalsService {


  createEmployeeAppraisals(createEmployeeAppraisalsData) {
    return this._httP.post(
      '/api/appraisal/employeeAppraisal/createEmployeeAppraisal',
      createEmployeeAppraisalsData
    );
  }

  createEmployeeAppraisalMetrices(createEmployeeAppraisalMetricesData) {
    return this._httP.post(
      '/api/appraisal/employeeMetrices/createEmployeeAppraisalMetrices',
      createEmployeeAppraisalMetricesData
    );
  }

  getEmployeeAppraisalsAll() {
    return this._httP.post(
      '/api/appraisal/employeeAppraisal/getEmployeeAppraisalAll',
      {}
    );
  }

  createEmployeeAppraisalCycle(data) {
    return this._httP.post(
      '/api/appraisal/configuration/createAppraisalCycle',
      data
    );
  }

  getMetricesByMIdCIc(data){
    return this._httP.post(
      '/api/appraisal/employeeMetrices/getMetricesByMIdCIc',
      data
    );
  }

  removeEvalr(data){
    return this._httP.post(
      '/api/appraisal/evaluator/removeEvalr',
      data
    );
  }

  addEval(data){
    return this._httP.post(
      '/api/appraisal/evaluator/addEvalr',
      data
    );
  }

   /** 
  * @description Update OKR Score Manually
  */
  updateOKRScore(){
    return this._httP.post('/api/appraisal/employeeAppraisal/updOKRScoreInAppraisal',{});
  }

   /** 
  * @description Update OKR Score Manually
  */
  updateOKRScoreinAppraisal(data){
    return this._httP.post('/api/appraisal/employeeAppraisal/updOKRScoreInAppraisal',data);
  }

  /** 
  * @description delete metrices for employee manually
  */
   deleteEmployeeMetrices(bodyParams){
    return this._httP.post('/api/appraisal/employeeMetrices/deleteMetrices',bodyParams);
  }

  /** 
  * @description download LDA Deatial Report 
  */
   getLDADetailForReport(bodyParams){
    return this._httP.post('/api/appraisal/employeeAppraisal/getLDADetailForReport',bodyParams);
  }
  
  /** 
  * @description Bulk Update Metrices
  */
  bulkEditEmployeeAppraisalMetricesWeightage(data){  
    return this._httP.post('/api/appraisal/employeeMetrices/bulkEditEmployeeAppraisalMetricesWeightage', data);
    }

  /** 
  * @description Bulk Revert Acknowledge
  */
  bulkRevertAcknowledge(data){
    return this._httP.post('/api/appraisal/employeeAppraisal/bulkRevertAcknowledge', data);
  }
  
  /** 
  * @description Check Approve and Acknowledge button config
  */
  getButtonVisibility(data){
    return this._httP.post('./api/appraisal/configuration/getButtonVisibility', data);
  }

  /** 
  * @description Toggle Approve and Acknowledge button config
  */
  toggleBtnConfig(data){
    return this._httP.post('./api/appraisal/configuration/toggleBtnConfig', data);
  }
  
  /** 
  * @description retriving star rating config
  */
   halfStarRatingConfig(){
    return this._httP.post('./api/appraisal/configuration/halfStarRatingConfig', {});
  }

  /** 
  * @description change star rating config
  */
  changeStarRatingConfig(data){
    return this._httP.post('./api/appraisal/configuration/changeStarRatingConfig', data);
  }

  /** 
  * @description get unique 
  */
  getUniqueEmployeeByMetrices(data){
    return this._httP.post(
      '/api/appraisal/employeeMetrices/getEmployeeOverallEvaluators',
      data
    );
  }
  
  /** 
  * @description delete evaluator in all metrices
  */
  deleteEvaluatorAllMetrices(data){
    return this._httP.post(
      '/api/appraisal/employeeMetrices/removeEvalforAllMetrices',
      data
    )
  }

  /** 
  * @description delete evaluator in all metrices
  */
  addEvaluatorAllMetrices(data){
    return this._httP.post(
      '/api/appraisal/employeeMetrices/addEvalforAllMetrices',
      data
    )
  }

  /** 
  * @description get evaluator in edit eval
  */
  getEvalInEditEval(data){
    return this._httP.post(
      '/api/appraisal/employeeMetrices/GetEvalAppraisalConfig',
      data
    )
  }

  /** 
  * @description add evaluator in edit eval
  */
  addEvalInEditEval(data){
    return this._httP.post(
      '/api/appraisal/employeeMetrices/AddEvalAppraisalConfig',
      data
    )
  }

  /** 
  * @description delete evaluator in edit eval
  */
  deleteEvalInEditEval(data){
    return this._httP.post(
      '/api/appraisal/employeeMetrices/removeEvalAppraisalConfig',
      data
    )
  }

  /** 
  * @description show default appraisal year
  */
  getDefaultAppraisalYear(data){
    return this._httP.post(
      '/api/appraisal/employeeMetrices/getDefaultAppraisalYear',
      data
    )
  }

  /** 
  * @description Update default appraisal year
  */
  updateDefaultAppraisalYear(data){
    return this._httP.post(
      '/api/appraisal/employeeMetrices/updateDefaultAppraisalYear',
      data
    )
  }

  /** 
  * @description add block appraisal cycle
  */
  addBlockAppraisalCycle(data){
    return this._httP.post(
      '/api/appraisal/employeeMetrices/addBlockAppraisalCycle',
      data
    )
  }

  /** 
  * @description get all block appraisal cycle
  */
  showBlockAppraisalCycle(){
    return this._httP.post(
      '/api/appraisal/employeeMetrices/GetBlockAppraisalCycle',
      {}
    )
  }

  /** 
  * @description remove block appraisal cycle
  */
  removeBlockAppraisalCycle(data){
    return this._httP.post(
      '/api/appraisal/employeeMetrices/removeBlockAppraisalCycle',
      data
    )
  }

  
   /** 
  * @description show tool tip value
  */
  getToolTipValue(){
    return this._httP.post(
      '/api/appraisal/employeeMetrices/showToolTipValue',
      {}
    )
  }


  /** 
  * @description show tool tip value
  */
  getLegendData(){
    return this._httP.post(
      '/api/appraisal/employeeMetrices/showLegendData',
      {}
    )
  }

  constructor(private _httP: HttpClient) {}
}
