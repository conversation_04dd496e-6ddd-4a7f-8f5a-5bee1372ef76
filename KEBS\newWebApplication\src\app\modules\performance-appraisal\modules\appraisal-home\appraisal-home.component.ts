import { Component, OnInit } from '@angular/core';

import { Router, ActivatedRoute, ParamMap } from '@angular/router';
import {AppraisalApiUrlService} from "../../core/services/appraisal-api-url.service"
import {LoginService} from "src/app/services/login/login.service"


@Component({
  selector: 'app-appraisal-home',
  templateUrl: './appraisal-home.component.html',
  styleUrls: ['./appraisal-home.component.scss'],
})
export class AppraisalHomeComponent implements OnInit {
  tabLinks: any;
  isAdmin:boolean;
  profile :any;


  constructor(
     private route: ActivatedRoute,
     private _Router: Router,
     private _url:AppraisalApiUrlService,
     private _LoginService:LoginService
     ) {}

  ngOnInit() {
    this.profile = this._LoginService.getProfile().profile;
    this.tabLinks = [
      // { label: 'PMS Dashboard', path: 'overview/1' },
      { label: 'My Review Form', path: 'achivements/1' },
      { label: 'Approvals', path: 'assessments/1' },  
    ];
    this._url.haveAccess(this.profile.oid).subscribe(res =>{
      this.isAdmin = res['data'];
    })
  }
  navigate(){
    this._Router.navigateByUrl("/main/pms/appraisal/configure")
  }
}
