import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { AppraisalHomeRoutingModule } from './appraisal-home-routing.module';
import { AppraisalHomeComponent } from './appraisal-home.component';
import { SharedComponentsModule } from 'src/app/app-shared/app-shared-components/components.module';
// Material
import { MatTabsModule } from '@angular/material/tabs';
import { DxHtmlEditorModule } from 'devextreme-angular';
import { MatDialogModule } from '@angular/material/dialog';
import {MatProgressSpinnerModule} from '@angular/material/progress-spinner';
import { StatusPopupComponent } from './components/module-detail/components/status-popup/status-popup.component';
















@NgModule({
  declarations: [AppraisalHomeComponent, StatusPopupComponent],
  imports: [
    CommonModule,
    AppraisalHomeRoutingModule,
    MatTabsModule,
    MatProgressSpinnerModule,
    DxHtmlEditorModule,
    SharedComponentsModule,
    MatDialogModule

  ],
})
export class AppraisalHomeModule { }
