<!-- <div class="row d-flex pt-2 pl-0">
  <div class="col-3 pt-2 pl-0 pr-0">
      <span *ngIf="metricName" class="default-font"
      [ngClass]="{'pl-2': getTextLength(metricName) < 6}" style="font-weight: 600;">{{metricName}}</span>
      <span *ngIf="!metricName" class="default-font" style="font-weight: 600;">
        {{starsClicked.length}} / {{totalStars}}
      </span>
  </div>
  <div class="col-9">
    <button mat-icon-button [disabled]="readOnly"
      *ngFor="let stars of ratingStars; let starIndex = index;" 
      (click)="onClick(starIndex)" >
      <mat-icon [ngStyle]="{'color' : getStarColor(stars.clicked)}">
        {{stars.clicked ? 'star' : 'star_border'}}
      </mat-icon>
    </button>
  </div>
</div> -->


<div class="row d-flex pt-2 pl-0">
  <div class="col-3 pt-2 pl-0 pr-0">
      <span *ngIf="metricName" class="default-font"
      [ngClass]="{'pl-2': getTextLength(metricName) < 6}" style="font-weight: 600;">{{metricName}}</span>
      <span *ngIf="!metricName" class="default-font" style="font-weight: 600;">
        {{starsClicked.length}} / {{totalStars}}
      </span>
  </div>
  <div class="col-9">
    <div *ngIf="halfstar == true">
      <fieldset [disabled]=true class="rating" *ngFor="let index of ratingArr">
          <input type="radio" id="star5" name="rating" value="index"/><label [ngStyle]="{'color' : getStarColor(index.fullStarclicked)}" (click)="clickStar(index.starValue)" class="full"></label>
          <input type="radio" id="star4.5" name="rating" value="index"/><label [ngStyle]="{'color' : getHalfStarColor(index.HalfstarClicked)}" (click)="clickStar(index.starValue-0.5)" class="half"></label>
      </fieldset>
    </div>
    <div *ngIf="halfstar == false">
      <fieldset class="rating" *ngFor="let index of ratingArr">
          <input type="radio" id="star5" name="rating" value="index"/><label [ngStyle]="{'color' : getStarColor(index.fullStarclicked)}" (click)="clickStar(index.starValue)" class="full"></label>
      </fieldset>
    </div>
  </div>
</div>