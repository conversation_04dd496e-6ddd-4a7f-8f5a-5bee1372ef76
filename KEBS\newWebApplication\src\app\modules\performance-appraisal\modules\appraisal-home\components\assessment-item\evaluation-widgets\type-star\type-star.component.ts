import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { T } from '@angular/cdk/keycodes';
import * as _ from "underscore";
@Component({
  selector: 'app-type-star',
  templateUrl: './type-star.component.html',
  styleUrls: ['./type-star.component.scss']
})
export class TypeStarComponent implements OnInit {

  @Input() metricName: any;
  @Input() totalStars: any;
  @Input() totalScore: any;
  @Input() readOnly: any;
  @Input() halfstar: any
  @Output() starsResponse = new EventEmitter<any>();

  ratingStars = [];
  ratingArr = Array()
  starsClicked = [];
  eachStarArr = []
  finalArr = Array()
  constructor() { }

  ngOnInit(): void {

    this.calculateScore();
    for(let i=0;i<this.totalStars;i++){
      this.eachStarArr.push(100/this.totalStars)
    }
    console.log(this.eachStarArr)

  }

  ngOnChanges() {

    this.calculateScore();

  }
  calculateScore() {

    let starsSelected = 0;

    if (this.totalScore)
      starsSelected  = (this.totalScore / 100) * this.totalStars;

    // starsSelected = Math.round(starsSelected);
    console.log(starsSelected+"star selec hii last")
    console.log(this.totalScore+"total score")
    this.ratingArr = [];
    // if(this.halfstar==true){
    for (let i = 0; i < this.totalStars; i++){
      this.ratingArr.push({
        starValue : i + 1,
        fullStarclicked : starsSelected > 0 && starsSelected >= i + 1 ? true : false,
        HalfstarClicked : starsSelected > 0 && i < starsSelected ? true : false
    })};
    // }else if(this.halfstar==false){
    //   for (let i = 0; i < this.totalStars; i++){
    //     this.ratingArr.push({
    //       starValue : i + 1,
    //       fullStarclicked : starsSelected > 0 && starsSelected >= i + 1 ? true : false,
    //   })};
    // }

    this.starsClicked = _.where(this.ratingArr, { fullStarclicked : true });


  }
  // calculateScore() {

  //   let starsSelected = 0;

  //   if (this.totalScore)
  //     starsSelected  = (this.totalScore / 100) * this.totalStars;

  //   starsSelected = Math.round(starsSelected);

  //   this.ratingStars = [];

  //   for (let i = 0; i < this.totalStars; i++)
  //     this.ratingStars.push({
  //       starValue : i + 1,
  //       clicked : starsSelected > 0 && starsSelected >= i + 1 ? true : false
  //     });

  //   this.starsClicked = _.where(this.ratingStars, { clicked : true });

  // }

  getStarColor(clicked) {
    
    return (clicked ? '#FFD700': '#66615B');

  }

  getHalfStarColor(HalfstarClicked) {
    
    return (HalfstarClicked ? '#FFD700': '#66615B');

  }

  // async getTotalScore() {
  //   this.employeeMetricesData.employee_appraisal_metrices_evaluators_details.forEach(
  //     async (evaluatorTypes) => {
  //       if (evaluatorTypes == 'manager') {
  //         evaluatorTypes.employee_appraisal_metrices_evaluators.forEach(
  //           async (evaluator) => {
  //             if (
  //               evaluator.employee_appraisal_metrices_evaluator_oid ==
  //               (await this._AppraisalEvaluatorsService.getCurrentUserOID())
  //             ) {
  //               this.evaluatorScore =
  //                 evaluator.employee_appraisal_metrices_evaluator_score_awarded;
  //               this.calculateScore();
  //             }
  //           }
  //         );
  //       }
  //     }
  //   );
  // }


  onClick(starIndex) {

    for (let i = 0; i < this.totalStars; i++)
      this.ratingStars[i].clicked = i <= starIndex ? true : false;
    console.log(starIndex)
    let value = (this.ratingStars[starIndex].starValue / this.totalStars) * 100;
    console.log(this.ratingStars)
    console.log("old value emit")

    this.starsResponse.emit(value);

  }
  
  clickStar(index:any) {
  if(this.readOnly == false){
    for (let i = 0; i < this.totalStars; i++){
      this.ratingArr[i].fullStarclicked = i+1 <= index ? true : false;
      this.ratingArr[i].HalfstarClicked = i < index ? true : false;
      console.log(this.ratingArr[i],"ganesh")
    }
    if(this.eachStarArr[0] == undefined){
    let value1 = (index/ this.totalStars) * 100;
    this.starsResponse.emit(value1);
    console.log(value1+"value")
    }else{
      let value = 0
      if((index - Math.floor(index)) !== 0){
        for(let i=0; i<this.eachStarArr.length; i++){
          if(index > i){
            this.finalArr.push(this.eachStarArr[i])
          }
        }this.finalArr.push(this.finalArr.pop()/2)
      }
      else{
          for(let i=0; i<this.eachStarArr.length; i++){
            if(index > i){
              this.finalArr.push(this.eachStarArr[i])
            }
          }
        }
      for (let i = 0; i < this.finalArr.length; i++) {
        value += this.finalArr[i];
      }
      console.log(value+"  new emit value")
      this.finalArr = [],
      this.starsResponse.emit(value);
      console.log(index)
    }
  }
}

  getTextLength(text) {
    return text.length;
  }


}
