import { Component, Input, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { EmployeeAppraisalsService } from '../../../../../../services/employee_appraisal/employee-appraisals.service';
import { SharedLazyLoadedComponentsService } from "src/app/modules/shared-lazy-loaded-components/services/shared-lazy-loaded-components.service"

@Component({
  selector: 'custom-accordian-card',
  templateUrl: './custom-accordian-card.component.html',
  styleUrls: ['./custom-accordian-card.component.scss'],
})
export class CustomAccordianCardComponent implements OnInit {
  icon: boolean = false;
  @Input() cardTitle: string;
  @Input() scored: number;
  @Input() totalScore: number;
  @Input() appraisalCycleData: any;

  evaluatorsOID = [];
  evaluationMetrices: any = [];
  managerEvaluatorOID: any = [];
  uniqueManagerOID: any = [];

  constructor(private _EmployeeAppraisalsService: EmployeeAppraisalsService,
    private _lazyService: SharedLazyLoadedComponentsService,
    public $dialog: MatDialog,
    ) {}

  getEvaluatorsOID(employeeAppraisalMetricesData) {
    employeeAppraisalMetricesData.forEach(
      (employeeAppraisalMetrices, index) => {
        this._EmployeeAppraisalsService
          .getEmployeeAppraisalMetricesById(
            employeeAppraisalMetrices.employee_appraisal_metrices_id
          )
          .subscribe(
            (result: any) => {
              console.log(result);
              if (result.error == 'N') {
                result.data.employee_appraisal_metrices_evaluators_details.forEach(
                  (evaluatorTypes) => {
                    if (
                      evaluatorTypes.employee_appraisal_metrices_evaluator_type ==
                      'manager'
                    ) {
                      evaluatorTypes.employee_appraisal_metrices_evaluators.forEach(
                        (evaluator, index) => {
                          this.managerEvaluatorOID.push(
                            evaluator.employee_appraisal_metrices_evaluator_oid
                          );
                          if (
                            index + 1 ==
                            evaluatorTypes
                              .employee_appraisal_metrices_evaluators.length
                          ) {
                            this.approversList();
                          }
                        }
                      );
                    }
                  }
                );
              }
            },
            (error) => {
              console.log(error);
            }
          );
      }
    );
  }

  openQuickCTA = () => {
    this._lazyService.openQuickCTAModal(null, this.$dialog);

  }

  approversList() {
    this.uniqueManagerOID = [...new Set(this.managerEvaluatorOID)];
    console.log(this.uniqueManagerOID, 'uniqueManagerOID');
  }

  ngOnInit(): void {
    this.evaluationMetrices = this.appraisalCycleData.grouping_details[0].employee_appraisal_metrices;
    if (this.cardTitle == 'Manager Ratings') {
      this.getEvaluatorsOID(this.evaluationMetrices);
    }
  }
  click() {
    this.icon = !this.icon;
  }
}
