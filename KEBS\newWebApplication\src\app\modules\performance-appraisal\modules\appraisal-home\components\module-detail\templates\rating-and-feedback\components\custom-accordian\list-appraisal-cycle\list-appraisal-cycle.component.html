<ng-container *ngIf="isCycleVisibile == true">
  <div class="row pt-3">
    <div class="col-12">
      <div class="row">
        <!-- <div matTooltip="{{getCycleStatus().status}}">
          <div appStatusPopup [appraisalYear]="appraisalYear" [data]=" inputAppraisalCycleData" style="cursor: pointer;" class="status-circular-in-thread" [ngStyle]="{'background-color': getCycleStatus().color,'pointer-events': getCyclePointer().event}" ></div>
        </div> -->
        <div class="status-circular-in-thread" [ngStyle]="{'background-color': getCycleStatus().color}"
          matTooltip="{{getCycleStatus().status}}"></div>
        <span class="material-icons arrow" (click)="isAccordianExpanded = !isAccordianExpanded">
          keyboard_arrow_right
        </span>
        <span class="month-year-text">
          {{ appraisalCycleData?.appraisal_cycle_name }}
        </span>
        <span class="subtle">
          {{ appraisalCycleData?.appraisal_cycle_start_date | date: "MMM/yyyy" }} To {{
          appraisalCycleData?.appraisal_cycle_end_date | date: "MMM/yyyy" }}
        </span>

      </div>
    </div>
  </div>

  <ng-container *ngIf="isAccordianExpanded">
    <div class="row">
      <div class="col-12 pt-2">
        <custom-accordian-card [cardTitle]="'Manager Ratings'" [scored]="80" [totalScore]="100"
          [appraisalCycleData]="inputAppraisalCycleData"></custom-accordian-card>
        <app-customer-view [cycleId]="appraisalCycleData._id" [moduleId]="appraisalCycleData.appraisal_module_id">
        </app-customer-view>
      </div>
    </div>
  </ng-container>

</ng-container>



<!-- {{appraisalCycleData|json}} -->