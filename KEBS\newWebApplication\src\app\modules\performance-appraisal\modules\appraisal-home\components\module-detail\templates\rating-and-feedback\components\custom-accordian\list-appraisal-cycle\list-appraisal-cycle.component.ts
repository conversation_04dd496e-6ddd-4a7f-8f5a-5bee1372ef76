import { Component, Input, OnInit } from '@angular/core';
import { EmployeeAppraisalsService } from '../../../../../../../services/employee_appraisal/employee-appraisals.service';
import * as moment from "moment"
import {AppraisalApiUrlService} from "../../../../../../../../../core/services/appraisal-api-url.service"
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';


@Component({
  selector: 'app-list-appraisal-cycle',
  templateUrl: './list-appraisal-cycle.component.html',
  styleUrls: ['./list-appraisal-cycle.component.scss'],
})
export class ListAppraisalCycleComponent implements OnInit {
  @Input()
  inputAppraisalCycleData;
  @Input()
  employeeAppraisalData;
  @Input() appraisalYear; 
  appraisalCycleData;
  isAccordianExpanded: boolean = true;
  isCycleVisibile:any;

  isAcknowledge : boolean = false;
 isMeetingScheduled : boolean = false;

cycleStatus:any;
  protected _onDestroy = new Subject<void>();
  constructor(
    private _EmployeeAppraisalsService: EmployeeAppraisalsService,
    private _url : AppraisalApiUrlService
    ) {}

  getAppraisalCycle(appraisalCycleId) {
    this._EmployeeAppraisalsService
      .getAppraisalCycleDataById(appraisalCycleId)
      .subscribe(
        async (result: any) => {
          if (result.error == 'N') {
            this.appraisalCycleData = result.data;
            this.isCycleVisibile = await this._url.checkCycleVisible(this.appraisalCycleData)
            console.log(this.appraisalCycleData, 'cycle data');
          }
        },
        (error) => {
          console.log('Error getting appraisal cycle' + error);
        }
      );
  }

  ngOnInit(): void {
    if(this. inputAppraisalCycleData.is_acknowledged){
      this.isAcknowledge = true;
    }
    if(this. inputAppraisalCycleData.is_meeting_scheduled){
        this.isMeetingScheduled = true;
      }
      console.log(this.isAcknowledge)
      console.log(this.isMeetingScheduled)
    this.getAppraisalCycle(this.inputAppraisalCycleData.appraisal_cycle_id);
    this.resolveSubscription()
  }

  /**  
  * @description get appraisal cycle status
  */
  getCycleStatus = () =>{
    if(this.appraisalCycleData){
      let cycleStartDate = this.appraisalCycleData.appraisal_cycle_start_date;
      let cycleEndDate = this.appraisalCycleData.appraisal_cycle_end_date;
      if(moment(new Date()).isBefore(cycleStartDate)) 
      return {
        status:"Not yet started",
        color:"orange"
      };
      else if(moment(new Date()).isSame(cycleStartDate)||moment(new Date()).isSame(cycleEndDate)||moment(new Date()).isBetween(cycleStartDate,cycleEndDate))
      return{
        status:"Cycle started",
        color:"green"
      }
      else if(moment(new Date()).isAfter(cycleEndDate))
      return {
        status:"Cycle completed",
        color:"red"
      }
    }
    else {
      return{
        status:"Loading",
        color:"gray"
      }
    }
  }

  /**  
  * @description get appraisal cycle status
  */
  //  getCycleStatus = () =>{
  //   if(this.isAcknowledge || this.cycleStatus == 'Acknowledged'){
  //     return {
  //             status:"Acknowledged",
  //             color:"green"
  //           };
  //   }
  //   else if(!this.isAcknowledge || this.cycleStatus != 'Acknowledged'){
  //     return {
  //             status:"Not Acknowledged",
  //             color:"red"
  //           };
  //   }
  //   else {
  //     return{
  //             status:"Loading",
  //             color:"gray"
  //           }
  //   }
  // }

   /**  
  * @description check whether the status popup is visible or not
  */
  // getCyclePointer(){
  //   if(this.isMeetingScheduled && this.isAcknowledge){
  //   return {
  //     event:"none"
  //   };
  // }
  // else if (this.isMeetingScheduled && !this.isAcknowledge){
  //   return {
  //     event:"visible"
  //   };
  // }
  // else {
  //   return {
  //     event:"none"
  //   };
  // }
  // }

   /**  
  * @description listens to changes in status
  */
  resolveSubscription(){
    this._EmployeeAppraisalsService.inlineEditCallback.pipe(takeUntil(this._onDestroy)).subscribe((res: any) => {
     console.log('hello')
      if(res && (Object.keys(res).length > 0)){
        console.log("final",res)
        this.cycleStatus = res.name
        this.isAcknowledge = true;
      }
    })
  }

   /** 
  * @description unsubscribe all the subscription used in this file.
  */
    ngOnDestroy(): void {
      this._onDestroy.next();
      this._onDestroy.complete(); 
    }
}
