<div class="row">
  <div class="col-3 p-0">
    <div class="row" *ngIf="evaluatorOID">
      <div class="col-2 p-0">
        <app-user-image [id] = 'evaluatorOID' [imgWidth] = "'28px'" [imgHeight] = "'28px'"></app-user-image>
      </div>
      <div class="col-10 p-0">
       <strong><app-user-profile [type]="'name'" [oid]="evaluatorOID"></app-user-profile></strong> 
      </div>
    </div>
    <div class="row">
      <div class="col-2 p-0"></div>
      <div class="col-10 p-0">
        <app-user-profile [type]="'role'" [oid]="evaluatorOID"></app-user-profile>
      </div>
    </div>
    <div class="row mt-2 rating" style="overflow: scroll;">
      <div class="col-12">
        <app-evaluator-list-rating-feedback
        [columnType] = "'rating'"
        *ngFor="let evaluationMetric of employeeEvaluationMetrices"
          [employeeEvaluationMetrices]="
            evaluationMetric.employee_appraisal_metrices_id
          "
          [evaluatorOID]="evaluatorOID"
        >
        </app-evaluator-list-rating-feedback>
      </div>
    </div>
  </div>
  <div class="col-9">
    <app-evaluator-list-rating-feedback
    [columnType] = "'feedback'"
    *ngFor="let evaluationMetric of employeeEvaluationMetrices"
      [employeeEvaluationMetrices]="
        evaluationMetric.employee_appraisal_metrices_id
      "
      [evaluatorOID]="evaluatorOID"
    >
    </app-evaluator-list-rating-feedback>
  </div>
</div>



