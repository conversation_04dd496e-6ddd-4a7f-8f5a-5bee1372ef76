 
<ng-container *ngIf="isCycleVisible == true;else spinner">
    <div class="row pt-3">   
        <div matTooltip="{{getCycleStatus().status}}">
          <div appStatusPopup [appraisalYear]="appraisalYear" [data]="appraisalCycleDetails" style="cursor: pointer;" class="status-circular-in-thread" [ngStyle]="{'background-color': getCycleStatus().color,'pointer-events': getCyclePointer().event}" ></div>
        </div>
        <!-- <div  class="status-circular-in-thread" [ngStyle]="{'background-color': getCycleStatus().color}" matTooltip="{{getCycleStatus().status}}"></div> -->
        <span class="material-icons pl-2" style="cursor: pointer;" (click) = "isExpanded = !isExpanded">
          {{isExpanded?'keyboard_arrow_down':'keyboard_arrow_right'}}
          </span>
          <span style="font-weight: 500;">
            {{ appraisalCycleDetails?.appraisal_cycle_name }}
          </span>
          <span class="subtle">
            {{ appraisalCycleDetails?.appraisal_cycle_start_date | date: "MMM/yyyy" }} To  {{ appraisalCycleDetails?.appraisal_cycle_end_date | date: "MMM/yyyy" }}
          </span>
          <!-- <span *ngIf="employeeAppraisalData?.employeeAppraisalModuleData?.isSelfEvalRequired"> -->
          <span>
            <span class="subtle pl-2" style="color: red;font-weight:500;">
              Self Rating {{ appraisalCycleDetails?.employee_rating_start_date | date: "dd/MM/yyyy" }} To {{
              appraisalCycleDetails?.employee_rating_end_date | date: "dd/MM/yyyy" }}
            </span>
            <span class="subtle pl-2" style="color: red;font-weight:500;" *ngIf="allowEmployeeRating==false">
              {{cycleBlockMsg}}
            </span>
          </span>

          <span class="ml-auto mr-3">
            <span class="px-1">
              <span *ngFor="let evaluatorOid of evaluatorList;let a = index">
                  <app-user-image *ngIf="a < 1" [id]="evaluatorOid"
                    [tooltip]="appraiserTooltip" placement="right" content-type="template" max-width="300" imgHeight="28px"
                    imgWidth="28px" borderStyle="solid" borderWidth="2px" [borderColor]="'gray'">
                  </app-user-image>
              
                  <ng-template #appraiserTooltip>
                    <ng-container *ngFor="let evaluatorOid of evaluatorList;let a = index">
                      <div class="row tooltip-text" style="text-align: center">
                        {{ a + 1 }})
                        <app-user-profile [type]="'name'" [oid]="evaluatorOid"></app-user-profile>
                      </div>
                      <div class="row tooltip-text">
                        <app-user-profile [type]="'role'" [oid]="evaluatorOid"></app-user-profile>
                      </div>
                      ---------------------------
                    </ng-container>
                  </ng-template>
              </span>
              <span *ngIf="evaluatorList.length > 1">+
                  {{evaluatorList.length - 1}}
              </span>  
            </span>
  
     
            <button
                *ngIf="employeeAppraisalData?.employeeAppraisalModuleData?.isSelfEvalRequired"
                mat-button           
                (click)="submitRating()"
                class="px-1"
                style="float: right;"
                [ngClass]="( allowEmployeeRating && !ratingTotalScoreData?.eval_approved_all_or_not) ? 'reset-btn' : 'reset-btn-disabled'"
                [disabled]="(!allowEmployeeRating || ratingTotalScoreData?.eval_approved_all_or_not)">
                Submit
            </button>

          </span>
     
    </div>
    <div class="row" *ngIf="isExpanded">
      <div class="col-12 pl-5">
        <div
      *ngFor="
        let appraisalCycleModuleGroups of employeeAppraisalCycleDetails?.grouping_details;let groupIndex = index"
    >
      <app-list-rating-only-group
        [parentIndex] = 'index'
        [groupIndex] = "groupIndex"
        [appraisalCycleModuleGroups]="appraisalCycleModuleGroups"
        [employeeAppraisalData] = "employeeAppraisalData"
        [ratingTotalScore] = "ratingTotalScoreData"
        [l1EvalScoreData] = "l1EvalScoreData"
        [groupTotalScore] = "groupTotalScoreData"
        [appraisalMetricesDetails] = "appraisalMetricesDetails"
        [toolTipValueForStarRating] = "toolTipValueForStarRating"
        [attachmentBucket] = "attachmentBucket"   
        [allowEmployeeRating] = "allowEmployeeRating"
        [employeeOid] = "employeeOid"
        [l2EvalScoreData] = "l2EvalScoreData"
        [isLevel2ApprovalNeeded] = "isLevel2ApprovalNeeded"
      ></app-list-rating-only-group>
    </div>
      </div>
    </div>
  </ng-container> 
  
  <ng-template #spinner>
    <mat-spinner style="margin:0 auto;" diameter="25"></mat-spinner>
  </ng-template>