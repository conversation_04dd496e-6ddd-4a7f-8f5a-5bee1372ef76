import { Component, OnInit, Input } from '@angular/core';
import * as moment from "moment"
import { EmployeeAppraisalsService } from '../../../../../../services/employee_appraisal/employee-appraisals.service';
import {AppraisalApiUrlService} from "../../../../../../../../core/services/appraisal-api-url.service";
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { UtilityService } from 'src/app/services/utility/utility.service';
@Component({
  selector: 'app-list-rating-only-cycle',
  templateUrl: './list-rating-only-cycle.component.html',
  styleUrls: ['./list-rating-only-cycle.component.scss']
})
export class ListRatingOnlyCycleComponent implements OnInit {

  @Input('appraisalCycleDetails')
  employeeAppraisalCycleDetails;
  @Input('employeeAppraisalData')
  employeeAppraisalData;
  @Input() index: number;
  @Input() appraisalYear; 
  @Input() employeeOid;
  @Input() appraisalMetricesDetails;

  attachmentBucket:any;
  appraisalCycleDetails: any;
  allowEmployeeRating:boolean = false;
  isCycleVisible:any;
  //boolean
  isExpanded: boolean = true;
  ratingTotalScoreData:any;
  l1EvalScoreData:any;
  groupTotalScoreData:any;

  isAcknowledge : boolean = false;
  isMeetingScheduled : boolean = false;
  todayDate:any = new Date(moment().format("YYYY-MM-DD"));
  toolTipValueForStarRating:any;
  cycleBlockMsg:any = '';
  evaluatorList:any = [];
  cycleStatus:any;

  l2EvalScoreData: any;
  allowSubdivforlvl2appr:any = [];
  emp_region:any = '';
  isLevel2ApprovalNeeded :boolean = false;
  islevel2Approval: any = false;


  protected _onDestroy = new Subject<void>();

  constructor(
    private _EmployeeAppraisalsService: EmployeeAppraisalsService,
    private _url:AppraisalApiUrlService,
    private _UtilityService:UtilityService,
    ) { 
      this.attachmentBucketDetails();
    }

    /**  
  * @description get appraisal cycle data
  */
  getAppraisalCycleDetails() {
    this._EmployeeAppraisalsService
      .getAppraisalCycleDataById(
        this.employeeAppraisalCycleDetails.appraisal_cycle_id
      ).pipe(takeUntil(this._onDestroy))
      .subscribe(
        async (result: any) => {
          if (result.error == 'N') {
            this.appraisalCycleDetails = result.data;
            this.allowEmployeeRating  = result.allowEmployeeRating ? result.allowEmployeeRating : false;
            this.isCycleVisible = await this._url.checkCycleVisible(this.appraisalCycleDetails);
            this.cycleBlockMsg = this.todayDate < new Date(moment(this.appraisalCycleDetails?.employee_rating_start_date).format("YYYY-MM-DD")) ? 'Yet to Open' : new Date(moment(this.appraisalCycleDetails?.employee_rating_end_date).format("YYYY-MM-DD")) < this.todayDate ? 'Expired' : '';
          }
        },
        (error) => { }
      );
  }

  async ngOnInit() {
    if(this.employeeAppraisalCycleDetails.is_acknowledged){
      this.isAcknowledge = true;
    }
    if(this.employeeAppraisalCycleDetails.is_meeting_scheduled){
        this.isMeetingScheduled = true;
    }

  
    this.emp_region = await this.getEmpSubDivision();
    this.allowSubdivforlvl2appr = await this.getEmpSubDivforlvl2Submission();
    this.islevel2Approval = await this.getLevel2ApprovalNeeded(); // For fetching the level 2 approval needed or not flag from configuration
    // this.isLevel2ApprovalNeeded = this.allowSubdivforlvl2appr.includes(this.emp_region) ? true : false;
    this.isLevel2ApprovalNeeded = this.allowSubdivforlvl2appr.includes(this.emp_region) && this.islevel2Approval ? true : false;
    console.log("islevel2 approval config", this.islevel2Approval);
    console.log("islevel2approvalneeded",this.isLevel2ApprovalNeeded);
    // this.getAppraisalCycleDetails();
    this.getTooltipValueForStarRating();
    this.resolveSubscription();
    this.calculateAndTriggerSelfRatingTotalScore();
    this.getAllEvaluatorsByEmpAppraisalMetricesId();
    // this.calculateEmployeeAppraisalGroupTotalScore();
  }

  /**
   * @description get tooltip value for star rating
   */
   getTooltipValueForStarRating(){    
    let toolTipValueForStarRating = {
      configuration_name:"TooltipValue",
      employeeOid: this.employeeOid
    };

    this._EmployeeAppraisalsService
      .getTooltipValueForStarRating(
        toolTipValueForStarRating
      )
      .pipe(takeUntil(this._onDestroy))
      .subscribe(
        (result: any) => {
          if (result.err == 'N') {            
            this.toolTipValueForStarRating = result.data;            
          } else {

            this.toolTipValueForStarRating = [
              "E - Poor - 0","D - Needs Improvement - 25","C - Meets Expectation - 50","B - Good - 75","A - Exceptional - 100"
            ];

            console.log('Error while retrieving tooltip value');
          }   

          this.getAppraisalCycleDetails();
        },
        (error) => {

          this.toolTipValueForStarRating = [
            "E - Poor - 0","D - Needs Improvement - 25","C - Meets Expectation - 50","B - Good - 75","A - Exceptional - 100"
          ];   

          console.log('Error while retrieving tooltip value' + error);
        }
      );
   }

   getLevel2ApprovalNeeded = () =>{
    return new Promise((resolve, reject) =>{
      let is_lvl2_appr = {
        configuration_name : "isLevel2Approval"
      } 

       this._EmployeeAppraisalsService
      .getLevel2ApprovalNeeded(is_lvl2_appr)
      .pipe(takeUntil(this._onDestroy))
      .subscribe(
        async (res) => {
          if(res && res["data"].length > 0){
            this.islevel2Approval = res["data"][0]['configuration_data'];
          }
          else{
            this.islevel2Approval = false;
          }
          resolve(this.islevel2Approval);
        },
        (err) => {
        reject(err);
        }
      );

    })
   }


   getEmpSubDivforlvl2Submission = () =>{
    return new Promise((resolve, reject) =>{
      let emp_subdiv = {
        configuration_name : "empSubdivforlvl2submission"
      } 

       this._EmployeeAppraisalsService
      .getEmpSubDivforlvl2Submission(emp_subdiv)
      .pipe(takeUntil(this._onDestroy))
      .subscribe(
        async (res) => {
          if(res && res["data"].length > 0){
            this.allowSubdivforlvl2appr = res["data"][0]['configuration_data'];
          }
          else{
            this.allowSubdivforlvl2appr = [];
          }
          resolve(this.allowSubdivforlvl2appr);
        },
        (err) => {
        reject(err);
        }
      );

    })
    
   }

   getEmpSubDivision = () => {
    return new Promise((resolve,reject) =>{
      let subDiv = {
        employee_oid : this.employeeOid
      }
      this._EmployeeAppraisalsService
      .getEmpSubDiv(subDiv)
      .pipe(takeUntil(this._onDestroy))
      .subscribe(
        async (res) => {
          if(res && res["data"].length > 0){
            this.emp_region = res["data"];
          }
          else{
            this.emp_region = '';
          }
          resolve(this.emp_region);
        },
        (err) => {
        console.log("Error while retrieving sub division for l2");
        reject(err);
        }
      );

      })
    
   }

    /**
   * @description get tooltip value for star rating
   */
     attachmentBucketDetails(){    
      let attachmentBucketReq = {
        configuration_name:"appraisal_attachments",        
      };
  
      this._EmployeeAppraisalsService
        .getAppraisalAttachmentBucketDetails(
          attachmentBucketReq
        )
        .pipe(takeUntil(this._onDestroy))
        .subscribe(
          (result: any) => {
            if (result.err == 'N') {            
              this.attachmentBucket = result.data;              
            } else {
  
              this.attachmentBucket = 
                {destination_bucket:"kebs-appraisal",routing_key:"appraisal"}
              
  
              console.log('Error while retrieving attachment bucket');
            }   
          },
          (error) => {
  
            this.attachmentBucket = {destination_bucket:"kebs-appraisal",routing_key:"appraisal"};
  
            console.log('Error while retrieving attachment bucket' + error);
          }
        );
     }

  //  /**
  //  * @description get tooltip value for star rating
  //  */
  //   get(type:string){    
  //     let req = {
  //       configuration_name:"TooltipValue",
  //       employeeOid: this.employeeOid
  //     };
  
  //     this._EmployeeAppraisalsService
  //       .getTooltipValueForStarRating(
  //         req
  //       )
  //       .pipe(takeUntil(this._onDestroy))
  //       .subscribe(
  //         (result: any) => {
  //           if (result.err == 'N') {            
  //             this.toolTipValueForStarRating = result.data;            
  //           } else {
  
  //             this.toolTipValueForStarRating = [
  //               "E - Poor - 0","D - Needs Improvement - 25","C - Meets Expectation - 50","B - Good - 75","A - Exceptional - 100"
  //             ];
  
  //             console.log('Error while retrieving tooltip value');
  //           }   
  //         },
  //         (error) => {
  
  //           this.toolTipValueForStarRating = [
  //             "E - Poor - 0","D - Needs Improvement - 25","C - Meets Expectation - 50","B - Good - 75","A - Exceptional - 100"
  //           ];
  
  //           console.log('Error while retrieving tooltip value' + error);
  //         }
  //       );
  //    }

  // /**  
  // * @description get cycle status
  // */
  // getCycleStatus = () =>{
  //   if(this.appraisalCycleDetails){
  //     let cycleStartDate = this.appraisalCycleDetails.appraisal_cycle_start_date;
  //     let cycleEndDate = this.appraisalCycleDetails.appraisal_cycle_end_date;
  //     if(moment(new Date()).isBefore(cycleStartDate)) 
  //     return {
  //       status:"Not yet started",
  //       color:"orange"
  //     };
  //     else if(moment(new Date()).isSame(cycleStartDate)||moment(new Date()).isSame(cycleEndDate)||moment(new Date()).isBetween(cycleStartDate,cycleEndDate))
  //     return{
  //       status:"Cycle started",
  //       color:"green"
  //     }
  //     else if(moment(new Date()).isAfter(cycleEndDate))
  //     return {
  //       status:"Cycle completed",
  //       color:"red"
  //     }
  //   }
  //   else {
  //     return{
  //       status:"Loading",
  //       color:"gray"
  //     }
  //   }
  // }

  /**  
  * @description get appraisal cycle status
  */
  getCycleStatus = () =>{
    if(this.isAcknowledge || this.cycleStatus == 'Acknowledged'){
      return {
              status:"Acknowledged",
              color:"green"
            };
    }
    else if(!this.isAcknowledge || this.cycleStatus != 'Acknowledged'){
      return {
              status:"Not Acknowledged",
              color:"red"
            };
    }
    else {
      return{
              status:"Loading",
              color:"gray"
            }
    }
  }

   /**  
  * @description check whether the status popup is visible or not
  */
  getCyclePointer(){
    if(this.isMeetingScheduled && this.isAcknowledge){
    return {
      event:"none"
    };
  }
  else if (this.isMeetingScheduled && !this.isAcknowledge){
    return {
      event:"visible"
    };
  }
  else {
    return {
      event:"none"
    };
  }
  }

  /**  
  * @description listens to changes in status
  */
  resolveSubscription(){
    this._EmployeeAppraisalsService.inlineEditCallback.pipe(takeUntil(this._onDestroy)).subscribe((res: any) => {
     console.log('hello')
      if(res && (Object.keys(res).length > 0)){
        console.log("final",res)
        this.cycleStatus = res.name
        this.isAcknowledge = true;
      }
    })
  }

   /** 
  * @description unsubscribe all the subscription used in this file.
  */
    ngOnDestroy(): void {
      this._onDestroy.next();
      this._onDestroy.complete(); 
    }

    submitRating(){

        this._UtilityService
            .openConfirmationSweetAlertWithCustom(
              'Are you sure you want to submit ?',
              'Once you confirm, this  will get submitted, can`t revert back !'
            )
            .then(async (res) => {
              if (res) {

                if(this.employeeAppraisalData?.employeeAppraisalModuleData?.isSelfEvalRequired && !this.ratingTotalScoreData?.eval_approved_all_or_not && this.allowEmployeeRating){

                  let bodyParams = {
                    employee_oid:this.employeeOid,
                    appraisal_year:this.appraisalYear,
                    appraisal_module_id:this.employeeAppraisalData.employeeAppraisalModuleData.appraisal_module_id,
                    appraisal_cycle_id:this.employeeAppraisalCycleDetails.appraisal_cycle_id,
                    self_rating:true,
                    appraisal_cycle_name:this.appraisalCycleDetails?.appraisal_cycle_name,
                  }
          
                  this._EmployeeAppraisalsService.submitEmplpyeeSelfEvaluationResponse(
                    bodyParams
                  ).pipe(takeUntil(this._onDestroy))
                  .subscribe(
                    async (result: any) => {
                      if (result.err == 'N') {
                        console.log(result);
            
                        if(result?.updFlag){
                          this.isExpanded = !this.isExpanded;
                          this.ratingTotalScoreData.eval_approved_all_or_not = !this.ratingTotalScoreData?.eval_approved_all_or_not;
                          setTimeout(() => (this.isExpanded = !this.isExpanded));
                        }
                        
            
                        this._UtilityService.showMessage(result.msg,"Dismiss");
                      }
                      else{
                        console.log(result);
                        this._UtilityService.showErrorMessage("Error in Submission","Dismiss");
                      }
                    },
                    (error) => { 
                      this._UtilityService.showErrorMessage("Error in Submission","Dismiss");
                    }
                  );
                }else{
                  if(!this.employeeAppraisalData?.employeeAppraisalModuleData?.isSelfEvalRequired)
                    this._UtilityService.showMessage("There is no self rating for you","Dismiss");
                  else if(this.ratingTotalScoreData?.eval_approved_all_or_not)
                    this._UtilityService.showMessage("Appraisal has been submitted already","Dismiss");
                  else if(!this.allowEmployeeRating)
                    this._UtilityService.showMessage("Appraisal Cycle Self Rating Submission Date Expired","Dismiss");
                  else 
                    this._UtilityService.showMessage("Couldn't submit your request","Dismiss");
                }
              }
            })
    }

    /**
   * @description To Trigger Self Rating total score
   */
  calculateAndTriggerSelfRatingTotalScore(){
    
    let selfRatingTotalScoreRequest = {
      appraisal_year: this.appraisalYear,
      employee_oid: this.employeeOid,
      appraisal_cycle_id: this.employeeAppraisalCycleDetails.appraisal_cycle_id,
      appraisal_module_id:this.employeeAppraisalData.employeeAppraisalModuleData.appraisal_module_id,
      evaluator_oid: this.employeeOid,
      employee_appraisal_metrices_evaluator_type: 'manager',
    };

    this._EmployeeAppraisalsService
      .calculateEvlScoreForAllIndividualMetricesAndTotal(
        selfRatingTotalScoreRequest
      )
      .pipe(takeUntil(this._onDestroy))
      .subscribe(
        (result: any) => {
          if (result.err == 'N') {
            if(Array.isArray(result.data) && result.data.length>0){
              for(let i=0;i<result.data.length;i++){
                if(result.data[i]?.is_self_evaluator){
                  this.ratingTotalScoreData = result.data[i];
                }
                else if(result.data[i]?.evaluator_level==1){
                  this.l1EvalScoreData = result.data[i];
                }
                else if(result.data[i]?.evaluator_level==2){
                  this.l2EvalScoreData = result.data[i];
                }
              }
            }
          } else {
            console.log('Error while calculating employee self rating total score');
          }   
        },
        (error) => {
          console.log('Error while calculating employee self rating total score' + error);
        }
      );
  }

   /**
   * @description To Trigger Self Rating total score
   */
    calculateEmployeeAppraisalGroupTotalScore(){
    
      let selfRatingTotalScoreRequest = {
        appraisal_year: this.appraisalYear,
        employee_oid: this.employeeOid,
        appraisal_cycle_id: this.employeeAppraisalCycleDetails.appraisal_cycle_id,
        appraisal_module_id:this.employeeAppraisalData.employeeAppraisalModuleData.appraisal_module_id,        
      };
  
      this._EmployeeAppraisalsService
        .calculateEmployeeAppraisalGroupTotalScore(
          selfRatingTotalScoreRequest
        )
        .pipe(takeUntil(this._onDestroy))
        .subscribe(
          (result: any) => {
            if (result.err == 'N') {           
              this.groupTotalScoreData = result.data;              
            } else {
              console.log('Error while calculating employee group total score');
            }   
          },
          (error) => {
            console.log('Error while calculating employee group total score' + error);
          }
        );
    }

    getAllEvaluatorsByEmpAppraisalMetricesId(){
      let empAppraisalMetricesId=[];
      this.employeeAppraisalCycleDetails?.grouping_details.map((i:any)=>{i?.employee_appraisal_metrices.map((j:any)=>{empAppraisalMetricesId.push(j?.employee_appraisal_metrices_id)})})

      if(empAppraisalMetricesId.length>0){

        let bodyParams = {
          empAppraisalMetricesId:empAppraisalMetricesId,
          evaluatorType:"manager"
        }

        this._EmployeeAppraisalsService
        .getAllEvaluatorsByEmpAppraisalMetricesId(bodyParams)
        .pipe(takeUntil(this._onDestroy))
        .subscribe(
          (result: any) => {
            if (result.err == 'N') {
              this.evaluatorList = result.data;
            } else {
              console.log('Error while retrieving evaluators');
            }
          },
          (error) => {
            console.log('Error while retrieving evaluators' + error);
          }
        );
      }
    }
}
