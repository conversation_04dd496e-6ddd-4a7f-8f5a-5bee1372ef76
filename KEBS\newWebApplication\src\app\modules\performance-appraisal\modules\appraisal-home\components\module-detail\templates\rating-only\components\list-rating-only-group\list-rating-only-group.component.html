 <!-- <div class="row pt-2" *ngIf="parentIndex == 0">
    <span class="material-icons pl-2" 
     style="cursor: pointer;"
     [ngStyle]="{'visibility': appraisalCycleModuleGroup?.employee_appraisal_module_group_name == ''?'hidden':''}"
    (click) = "isExpanded = !isExpanded">
      {{isExpanded?'keyboard_arrow_down':'keyboard_arrow_right'}}
      </span>
    <div class="col-4">
      <span class="module-group-name" matTooltip="{{appraisalCycleModuleGroup?.employee_appraisal_module_group_name}}">
        {{ appraisalCycleModuleGroup?.employee_appraisal_module_group_name }}
      </span>
    </div>
    <div class="col-3 d-flex p-0 stars">
      <div class="col-4 over-flow pr-2">legend</div>
      <div class="col-4" *ngIf="employeeAppraisalData?.employeeAppraisalModuleData?.isSelfEvalRequired">Self Rating</div>
      <div class="col-4">Manager Rating</div>
    </div>
    <div class="col-2 pr-0">Status</div>
    <div class="col-2 p-0">Approvers</div>
  </div> -->
  <div class="row pt-4 px-0 mx-0" *ngIf="parentIndex == 0">

    

    <div class="over-flow p-0 m-0 h-100 d-flex justify-content-start align-items-center" [ngClass]="employeeAppraisalData?.employeeAppraisalModuleData
    ?.isSelfEvalRequired ?  'col-3' : 'col-4'">

      <span class="material-icons pr-1" style="cursor: pointer;"
      [ngStyle]="{'visibility': appraisalCycleModuleGroup?.employee_appraisal_module_group_name == ''?'hidden':''}"
      (click)="isExpanded = !isExpanded">
      {{isExpanded?'keyboard_arrow_down':'keyboard_arrow_right'}}
      </span>

      <span class="module-group-name" class="over-flow">
        <span class="module-group-name" matTooltip="{{appraisalCycleModuleGroup?.employee_appraisal_module_group_name}}">
          {{ appraisalCycleModuleGroup?.employee_appraisal_module_group_name }}
        </span>
        <span class="module-group-name pl-1" *ngIf="appraisalCycleModuleGroup?.employee_appraisal_module_group_weightage!=0">({{appraisalCycleModuleGroup?.employee_appraisal_module_group_weightage}})</span>
        <!-- Competencies Details -->
      </span>
    </div>
    <div class="over-flow p-0 m-0 h-100 d-flex justify-content-start align-items-center" *ngIf="groupIndex==0" [ngClass]="employeeAppraisalData?.employeeAppraisalModuleData
    ?.isSelfEvalRequired ?  'col-2' : 'col-3'">
      
    </div>
    <!-- <div class="col-1 over-flow p-0 m-0 h-100 d-flex justify-content-center align-items-center module-group-name">Score</div> -->
    <div class="p-0 m-0" *ngIf="groupIndex==0" [ngClass]="employeeAppraisalData?.employeeAppraisalModuleData
    ?.isSelfEvalRequired ?  'col-4' : 'col-2'">
      <div class="row p-0 m-0 h-100 d-flex align-items-center">
        <div class="p-0 m-0  d-flex justify-content-center align-items-center module-group-name" *ngIf="employeeAppraisalData?.employeeAppraisalModuleData
        ?.isSelfEvalRequired" [ngClass]="employeeAppraisalData?.employeeAppraisalModuleData
        ?.isSelfEvalRequired ?  'col-6' : ''">
          Employee Score
          <span class="pl-1">({{selfRatingTotalScore}})</span>
        </div>
        <div class="p-0 m-0  d-flex justify-content-center align-items-center module-group-name" [ngClass]="employeeAppraisalData?.employeeAppraisalModuleData
        ?.isSelfEvalRequired ?  'col-6' : 'col-12'">Reviewer Score
        <span class="pl-1" *ngIf="l1EvalScore!=0 && !isLevel2ApprovalNeeded">({{l1EvalScore}})</span>
        <span class="pl-1" *ngIf="l2EvalScore!=0 && isLevel2ApprovalNeeded">({{l2EvalScore}})</span>
        </div>
      </div>
    </div>
    <div class="col-2 p-0 m-0 h-100 d-flex justify-content-center align-items-center module-group-name" *ngIf="groupIndex==0">Status</div>
    <!-- <div class="col-1 p-0 m-0 approvers h-100 d-flex justify-content-start align-items-center module-group-name" *ngIf="groupIndex==0">Approvers</div> -->
    <div class="col-1 p-0 m-0 approvers h-100 d-flex justify-content-start align-items-center module-group-name" *ngIf="groupIndex==0">Remarks</div>
  </div>
  
  <div class="row px-0 mx-0 pt-2" *ngIf="isExpanded">
    <div class="col-12 px-0 mx-0">
      <div
      *ngFor="
        let employeeAppraisalMetric of appraisalCycleModuleGroup?.employee_appraisal_metrices
      "
    >
    <!-- <div *ngIf="employeeAppraisalMetric?.appraisal_metric_type=='rating';else feedBackOnly"> -->
      <!-- <p>{{employeeAppraisalMetric?.appraisal_metric_type}}</p> -->
      <app-list-rating-only-value
        [employeeAppraisalsData]="employeeAppraisalMetric"
        [employeeAppraisalData] = "employeeAppraisalData"
        (selfRatingTotalScore)="displaySelfRatingTotalScore($event)"
        [toolTipValueForStarRating]="toolTipValueForStarRating"
        [attachmentBucket] = "attachmentBucket"
        [allowEmployeeRating] = "allowEmployeeRating"
        [employeeOid] = "employeeOid"
        [l2EvalApprStatus] = "l2EvalApproverStatus"
        [isLevel2ApprovalNeeded] = "isLevel2ApprovalNeeded"
      ></app-list-rating-only-value>
    <!-- </div> -->

    <!-- <ng-template #feedBackOnly>
      <p>{{employeeAppraisalMetric?.appraisal_metric_type}}</p>
      <app-feedback-only
        [evaluatorId]=""
        [employeeid]=""
        [evalDetail]="employeeAppraisalMetric"
        [readOnly] = "true"
        mode = "view"        
      ></app-feedback-only>
    </ng-template> -->
      
    </div>
    </div>
  </div> 
  
  
   