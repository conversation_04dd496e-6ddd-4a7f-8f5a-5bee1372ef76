import { Component, Input, OnInit } from '@angular/core';

@Component({
  selector: 'app-list-rating-only-group',
  templateUrl: './list-rating-only-group.component.html',
  styleUrls: ['./list-rating-only-group.component.scss'],
})
export class ListRatingOnlyGroupComponent implements OnInit {
  @Input('appraisalCycleModuleGroups')
  appraisalCycleModuleGroup;
  @Input('employeeAppraisalData')
  employeeAppraisalData;
  @Input() parentIndex: number;
  @Input() ratingTotalScore:any;
  @Input() l1EvalScoreData:any;
  @Input() groupTotalScore:any;
  @Input() groupIndex:number = 0;
  @Input() toolTipValueForStarRating:any;
  @Input() attachmentBucket:any;
  @Input() allowEmployeeRating:any;
  @Input() employeeOid:any;
  @Input() l2EvalScoreData:any;
  @Input() isLevel2ApprovalNeeded:any;
  // @Input() appraisalMetricesDetails:any;

  math = Math;
  isExpanded: boolean = true;
  selfRatingTotalScore:Number=0;
  l1EvalScore:Number=0;
  groupTotalScoreForDisplay:Number = 0; 
  l2EvalScore:Number=0;   

  l2EvalApproverStatus:any = '';

  constructor() {}

  ngOnInit() {  
    this.l2EvalApproverStatus = this.l2EvalScoreData?.employee_appraisal_metrices_eval_score[0]?.employee_appraisal_metrices_evaluator_status ?
      this.l2EvalScoreData?.employee_appraisal_metrices_eval_score[0]?.employee_appraisal_metrices_evaluator_status : '';

    this.selfRatingTotalScore = typeof this.ratingTotalScore?.employee_appraisal_metrices_eval_score_total=="number"?Math.round(this.ratingTotalScore?.employee_appraisal_metrices_eval_score_total):0;    
    this.l1EvalScore =  this.l1EvalScoreData?.eval_approved_all_or_not && typeof this.l1EvalScoreData?.employee_appraisal_metrices_eval_score_total=="number"?Math.round(this.l1EvalScoreData?.employee_appraisal_metrices_eval_score_total):0;
    this.l2EvalScore =  (this.l2EvalApproverStatus == 'Approved' || this.l2EvalApproverStatus == 'Submitted') && typeof this.l2EvalScoreData?.employee_appraisal_metrices_eval_score_total=="number"?Math.round(this.l2EvalScoreData?.employee_appraisal_metrices_eval_score_total):0;

    
    // if(Array.isArray(this.groupTotalScore)){     
    //   for(let i=0;i<this.groupTotalScore.length;i++){       
    //     if(this.groupTotalScore[i]?.group_scores?.appraisal_group_id == this.appraisalCycleModuleGroup?._id){
    //       this.groupTotalScoreForDisplay =  typeof this.groupTotalScore[i].group_scores?.appraisal_group_score=="number"?this.groupTotalScore[i].group_scores?.appraisal_group_score.toFixed(2):0;
    //       break;
    //     }
    //   }
    // }

    // await this.AssignMetricType();    
  }

  displaySelfRatingTotalScore(event){
    this.selfRatingTotalScore = typeof event?.employee_appraisal_metrices_eval_score_total == 'number' ? Math.round(event?.employee_appraisal_metrices_eval_score_total):0;
  }

  // async AssignMetricType(){
  //   for(let i=0;i<this.appraisalCycleModuleGroup?.employee_appraisal_metrices.length;i++){
  //     let t= await this.getType(this.appraisalCycleModuleGroup?.employee_appraisal_metrices[i].employee_appraisal_metrices_id);      
  //     this.appraisalCycleModuleGroup.employee_appraisal_metrices[i]['appraisal_metric_type'] = t['appraisal_metric_evaluation_type'];
  //     this.appraisalCycleModuleGroup.employee_appraisal_metrices[i]['appraisal_metric_name'] = t['appraisal_metric_name'];
  //     this.appraisalCycleModuleGroup.employee_appraisal_metrices[i]['eval_comment'] = t['eval_comment'];
  //     this.appraisalCycleModuleGroup.employee_appraisal_metrices[i]['eval_status'] = t['eval_status'];
  //   }    
  // }

  // getType(appraisal_metrices_id){        
  //   for(let i=0;i<this.appraisalMetricesDetails.length;i++){            
  //     if(this.appraisalMetricesDetails[i]._id==appraisal_metrices_id)       
  //       {          
  //         let temp = {
  //           appraisal_metric_evaluation_type:this.appraisalMetricesDetails[i].appraisal_metric_evaluation_type,
  //           appraisal_metric_name:this.appraisalMetricesDetails[i].appraisal_metric_name,
  //           eval_comment:this.appraisalMetricesDetails[i].l1EvalDetails.eval_comment,
  //           eval_status:this.appraisalMetricesDetails[i].l1EvalDetails.eval_status,
  //         }
          
  //         this.appraisalMetricesDetails.splice(i,1);          
  //         return temp;
  //       }
  //   }    
  //   return "";
  // }
}
