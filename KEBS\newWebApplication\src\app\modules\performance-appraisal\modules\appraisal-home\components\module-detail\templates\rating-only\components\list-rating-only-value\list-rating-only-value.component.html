<div class="row pt-1 px-0 mx-0">
  <div class="col-12  px-0 mx-0">
    <mat-accordion class="listcard px-0 mx-0 py-">
      <mat-expansion-panel class="custom-panel pt-1 px-0 mx-0" style="background-color: #ffffff;border-left: 4px solid #b999f9;" disabled>
        <mat-expansion-panel-header class="p-2 m-0 h-100">
          <div
            class="row font-scss pl-2 pr-1 mx-0 mat-cell"
            style="width: 100% !important;"
          >
            <!-- <div class="col-6 col-sm-6 col-md-1 col-lg-1 pr-0 pl-2 mat-icon-tick authorize-click d-none d-md-block">
                <mat-icon>{{
                  icon ? "keyboard_arrow_down" : "keyboard_arrow_right"
                  }}</mat-icon>
              </div> -->
            <div class="p-0 mx-0 my-auto over-flow" [ngClass]="employeeAppraisalData?.employeeAppraisalModuleData
              ?.isSelfEvalRequired ?  'col-3' : 'col-4'">

              <span *ngIf="appraisalMetricesData;else spinner">
                <span style="font-weight: 500;color: #5cb6dc;" [tooltip]="metricNameTooltip" placement="top" content-type="template">
                  {{ appraisalMetricesData?.appraisal_metric_name }}
                </span>
                <ng-template #metricNameTooltip>
                  {{ appraisalMetricesData?.appraisal_metric_name }}
                </ng-template>
                <br>
                <span>
                  <span style="font-weight: 500;" class="pr-1" [matTooltip]="appraisalMetricesData?.appraisal_metric_unit">Unit
                  </span>
                  {{appraisalMetricesData?.appraisal_metric_unit}}
                </span>
              </span>

              <ng-template #spinner>
                <mat-spinner style="margin:0 auto;" diameter="25"></mat-spinner>
              </ng-template>
            </div>
            

            <div class="pr-0 py-0 mx-0 my-auto" [matTooltip]="employeeAppraisalMetricesData?.group_name" [ngClass]="[employeeAppraisalData?.employeeAppraisalModuleData
            ?.isSelfEvalRequired ?  'col-2' : 'col-3', employeeAppraisalMetricesData?.appraisal_metrices_weightage!=0 ? 'over-flow' : 'over-flow-two-line']">
              <span>
                <span style="font-weight: 500;" class="pr-1">Type</span>
                {{employeeAppraisalMetricesData?.group_name}}
              </span>
              <br>
              <span *ngIf="employeeAppraisalMetricesData?.appraisal_metrices_weightage!=0;else emptyContent">
                <span class="pr-1" style="font-weight: 400;">Weightage</span>
                {{employeeAppraisalMetricesData?.appraisal_metrices_weightage}}%
              </span>
              <ng-template #emptyContent>
                <span>&nbsp;</span>
              </ng-template>
            </div>

            <!-- <div class="col-1 over-flow p-0 m-0 h-100 d-flex flex-column justify-content-center align-items-center"
                [matTooltipDisabled]="
                  employeeAppraisalMetricesData?.employee_appraisal_metrices_evaluation_status ==
                  'Approved'
                    ? false
                    : true
                "
                [matTooltip]="
                  employeeAppraisalMetricesData?.legend_details?.legend_name
                "
              >
                 {{
                  employeeAppraisalMetricesData?.legend_details?.legend_name &&
                  employeeAppraisalMetricesData?.employee_appraisal_metrices_evaluation_status ==
                    "Approved"
                    ? employeeAppraisalMetricesData?.legend_details?.legend_name
                    : "-"
                }}

                {{
                  employeeAppraisalMetricesData?.employee_appraisal_metrices_scored_obtained && employeeAppraisalMetricesData?.employee_appraisal_metrices_evaluation_status ==
                  "Approved" ?
                  scoreForDispaly : "0"
                }} /
                {{
                  employeeAppraisalMetricesData?.appraisal_metrices_weightage ?
                  employeeAppraisalMetricesData?.appraisal_metrices_weightage : "-"
                }}
              </div> -->
            <div class="p-0 m-0" [ngClass]="employeeAppraisalData?.employeeAppraisalModuleData
            ?.isSelfEvalRequired ?  'col-4' : 'col-2'">
              <!-- <div
                class="col-2 over-flow pr-2"
                [matTooltipDisabled]="
                  employeeAppraisalMetricesData?.employee_appraisal_metrices_evaluation_status ==
                  'Approved'
                    ? false
                    : true
                "
                [matTooltip]="
                  employeeAppraisalMetricesData?.legend_details?.legend_name
                "
              >
                {{
                  employeeAppraisalMetricesData?.legend_details?.legend_name &&
                  employeeAppraisalMetricesData?.employee_appraisal_metrices_evaluation_status ==
                    "Approved"
                    ? employeeAppraisalMetricesData?.legend_details?.legend_name
                    : "-"
                }}
              </div> -->
              <div class="row p-0 m-0 align-items-center h-100">
                <div class="py-0 px-0 d-flex flex-column justify-content-center align-items-center" 
                *ngIf="
                  employeeAppraisalData?.employeeAppraisalModuleData
                    ?.isSelfEvalRequired 
                " [ngClass]="employeeAppraisalData?.employeeAppraisalModuleData
                ?.isSelfEvalRequired ?  'col-6' : ''"
              >
                <div *ngIf="employeeAppraisalMetricesData?.employee_appraisal_metrices_evaluation_status == 'Open' && allowEmployeeRating ;else scoreTile">
                  <mat-icon
                    style="font-size: 22px"
                    [ngStyle]="{ color: getStarColor(stars.clicked) }" 
                    [matTooltip]="stars.tooltipValue"                                 
                    *ngFor="let stars of ratingStars; let starIndex = index"
                    [ngClass]="{'disable':employeeAppraisalMetricesData?.employee_appraisal_metrices_evaluation_status !== 'Open'}"
                    (click)="onClick(starIndex)"
                  >
                    {{ stars.clicked ? "star" : "star_border" }}
                  </mat-icon>
                </div>

                <ng-template #scoreTile>
                  <div class="card d-flex justify-content-center align-items-center" style="height: 40px;width: 55px; border-radius:3px;background-color: #249fdc;color:white;font-weight: 500;" [ngStyle]="{'opacity': ((selfEvalStatus=='Saved'||selfEvalStatus=='Open') && allowEmployeeRating==false)  ? '0.5' : '1' }">
                    {{ selfEvalStatus!='Open' ? totalScore : '-'}}
                  </div>
                  <span *ngIf="selfEvalStatus!='Open' && selfEvalStatus!='Saved'">{{employeeAppraisalMetricesData?.legend_details?.selfEval_legend_name}}</span>                  
                </ng-template>

                <!-- <rating-stars
                  [starCount]="appraisalMetricesData.appraisal_metric_max_score"
                  [starSize]="19"
                  [spacing]="6"
                  [starColor]="'orange'"
                  [score]="
                    employeeAppraisalMetricesData.employee_appraisal_metrices_evaluation_status ==
                    'Approved'
                      ? employeeAppraisalMetricesData.employee_appraisal_metrices_scored_obtained
                      : 0
                  "
                  [totalScore]="100"
                  [isScoreVisibile]="false"
                  [scoreAndStarSeperation]="35"
                ></rating-stars> -->

              </div>
              <div class="py-0 px-0 d-flex flex-column justify-content-center align-items-center" *ngIf="appraisalMetricesData" [ngClass]="employeeAppraisalData?.employeeAppraisalModuleData
              ?.isSelfEvalRequired ?  'col-6' : 'col-12'">

                <div class="card d-flex justify-content-center align-items-center" *ngIf="employeeAppraisalData?.appraisalModuleData?.display_appraiser_comments" style="height: 40px;width: 55px; border-radius:3px;background-color: #e56635;color:white;font-weight: 500;">
                  <span *ngIf="!isLevel2ApprovalNeeded">
                    {{ employeeAppraisalMetricesData.employee_appraisal_metrices_evaluation_status ==
                      'Approved'
                        ? employeeAppraisalMetricesData.employee_appraisal_metrices_scored_obtained
                        : '-'}}
                  </span>
                  <span *ngIf="isLevel2ApprovalNeeded">
                    {{ l2EvalApprStatus ==
                      'Approved'
                        ? employeeAppraisalMetricesData?.l2EvalScoreAwarded
                        : '-'}}
                  </span>
                </div>

                <span *ngIf="employeeAppraisalData?.appraisalModuleData?.display_appraiser_comments;else onlyLegend">
                  <span *ngIf="!isLevel2ApprovalNeeded">
                    <span *ngIf="employeeAppraisalMetricesData.employee_appraisal_metrices_evaluation_status == 'Approved'"> 
                      {{ employeeAppraisalMetricesData?.legend_details?.overall_legend_name}}
                    </span>
                  </span>
                  <span *ngIf="isLevel2ApprovalNeeded">
                    <span *ngIf="l2EvalApprStatus == 'Approved'"> 
                      {{ employeeAppraisalMetricesData?.legend_details?.l2Eval_legend_name}}
                    </span>
                  </span>
                </span>

                <ng-template #onlyLegend>
                  <span class="card d-flex justify-content-center align-items-center over-flow" *ngIf="employeeAppraisalMetricesData.employee_appraisal_metrices_evaluation_status == 'Approved' && !isLevel2ApprovalNeeded ;else isLvl2" style="height: 40px;width: 150px; border-radius:3px;background-color: #e56635;color:white;font-weight: 500;">
                    {{ employeeAppraisalMetricesData?.legend_details?.overall_legend_name}}
                  </span>
                  <ng-template #isLvl2>
                    <span class="card d-flex justify-content-center align-items-center over-flow" *ngIf="isLevel2ApprovalNeeded && l2EvalApprStatus == 'Approved' && employeeAppraisalMetricesData.noOfEvaluators >= 2 ;else isLvl2andNoL2Eval" style="height: 40px;width: 150px; border-radius:3px;background-color: #e56635;color:white;font-weight: 500;">
                      {{ employeeAppraisalMetricesData?.legend_details?.l2Eval_legend_name}}
                    </span>
                  </ng-template>
                  <ng-template #isLvl2andNoL2Eval>
                    <span class="card d-flex justify-content-center align-items-center over-flow" *ngIf="employeeAppraisalMetricesData.employee_appraisal_metrices_evaluation_status == 'Approved' && isLevel2ApprovalNeeded && employeeAppraisalMetricesData.noOfEvaluators <= 2 ;else noLegend" style="height: 40px;width: 150px; border-radius:3px;background-color: #e56635;color:white;font-weight: 500;">
                      {{ employeeAppraisalMetricesData?.legend_details?.overall_legend_name}}
                    </span>
                  </ng-template>
                  <ng-template #noLegend>
                    <span class="card d-flex justify-content-center align-items-center over-flow" style="height: 40px;width: 150px; border-radius:3px;background-color: #e56635;color:white;font-weight: 500;"> - </span>
                  </ng-template>
                </ng-template>

                <!-- <rating-stars
                  [starCount]="appraisalMetricesData.appraisal_metric_max_score"
                  [starSize]="19"
                  [spacing]="6"
                  [starColor]="'orange'"
                  [score]="
                    employeeAppraisalMetricesData.employee_appraisal_metrices_evaluation_status ==
                    'Approved'
                      ? employeeAppraisalMetricesData.employee_appraisal_metrices_scored_obtained
                      : 0
                  "
                  [totalScore]="100"
                  [isScoreVisibile]="false"
                  [scoreAndStarSeperation]="35"
                ></rating-stars> -->
              </div>
              </div>
              
            </div>
            <div class="col-2 py-1 px-0 m-0 h-100 d-flex justify-content-center align-items-center">
              <div *ngIf="!isRatingSaveLoading;else loader">
                <div class="d-flex flex-row justify-content-center align-items-center m-0 p-0">
                  <div
                    class="status-circular-in-thread"
                    [ngStyle]="{
                      'background-color': getStatusColor(
                        employeeAppraisalMetricesData?.employee_appraisal_metrices_evaluation_status
                      )
                    }" 
                    [matTooltip]="employeeAppraisalMetricesData?.employee_appraisal_metrices_evaluation_status"
                  ></div>
                  <span class="pl-2">
                    {{
                      employeeAppraisalMetricesData?.employee_appraisal_metrices_evaluation_status
                    }}
                  </span>
                </div>
              </div>

              <ng-template #loader>
                <div class="d-flex flex-row">
                  <mat-spinner matTooltip="Please wait..." diameter="20"> </mat-spinner>
                  <span class="pl-2">saving...</span>
                </div>
              </ng-template>
            </div>            

            <!-- <div class="col-1 p-0 m-0 d-flex justify-content-center align-items-center approvers h-100">                 -->
              <!-- <span *ngFor="
                                let managerData of employeeAppraisalMetricesData?.employee_appraisal_metrices_evaluators_details
                              ">
                <span *ngFor="
                                  let evaluator of managerData.employee_appraisal_metrices_evaluators;
                                  let a = index
                                ">
                  <app-user-image *ngIf="a < 1" [id]="evaluator.employee_appraisal_metrices_evaluator_oid"
                    [tooltip]="appraiserTooltip" placement="right" content-type="template" max-width="300" imgHeight="28px"
                    imgWidth="28px" borderStyle="solid" borderWidth="2px" [borderColor]="'gray'">
                  </app-user-image>
              
                  <ng-template #appraiserTooltip>
                    <ng-container *ngFor="
                                      let approver of managerData.employee_appraisal_metrices_evaluators;
                                      let a = index
                                    ">
                      <div class="row tooltip-text" style="text-align: center">
                        {{ a + 1 }})
                        <app-user-profile [type]="'name'" [oid]="
                                          approver.employee_appraisal_metrices_evaluator_oid
                                        "></app-user-profile>
                      </div>
                      <div class="row tooltip-text">
                        <app-user-profile [type]="'role'" [oid]="
                                          approver.employee_appraisal_metrices_evaluator_oid
                                        "></app-user-profile>
                      </div>
                      ---------------------------
                    </ng-container>
                  </ng-template>
                </span>
              </span>
              <span *ngIf="
                                employeeAppraisalMetricesData
                                  ?.employee_appraisal_metrices_evaluators_details[0]
                                  .employee_appraisal_metrices_evaluators.length > 1
                              ">+
                {{
                employeeAppraisalMetricesData
                ?.employee_appraisal_metrices_evaluators_details[0]
                .employee_appraisal_metrices_evaluators.length - 1
                }}</span>               -->
                
              <!-- <button
                style="margin-left: 2rem"
                mat-icon-button
                matTooltip="Quick CTA"
                class="icon-tray-button"
                (click)="openQuickCTA()"
              >
                <mat-icon class="smallCardIcon">assignment_late</mat-icon>
              </button> -->
              
              
            <!-- </div> -->

            <div class="col-1 p-0 m-0 d-flex flex-column justify-content-center align-items-center approvers h-100">
              <button
              style="margin-left: 0.5rem"
              mat-icon-button
              matTooltip="Add comments"              
              (click)="openComments()"
              >
                <mat-icon class="smallCardIcon" [ngStyle] = "{'color': anyCommentExist  ? 'green' : '' }">forum</mat-icon>
              </button>

              <span *ngIf="employeeAppraisalMetricesData?.employee_appraisal_metrices_evaluators_details">
                <attachment-upload-btn
                [destinationBucket]="attachmentBucket?.destination_bucket"
                [routingKey]="attachmentBucket?.routing_key"
                [allowEdit]="allowAttachmentEmpUpload"
                [contextId]="getContextIdForAttachments('Employee')"
                (change)="contextIdChangeInattachments($event)"
                ></attachment-upload-btn>
              </span>
            
              <span *ngIf="employeeAppraisalMetricesData?.employee_appraisal_metrices_evaluators_details && employeeAppraisalMetricesData.employee_appraisal_metrices_evaluation_status == 'Approved'">
                <attachment-upload-btn
                [destinationBucket]="attachmentBucket?.destination_bucket"
                [routingKey]="attachmentBucket?.routing_key"
                [allowEdit]="allowAttachmentL1EvalUpload"
                [contextId]="getContextIdForAttachments('L1Eval')"
                ></attachment-upload-btn>
              </span>
            </div>
          </div>
        </mat-expansion-panel-header>
        <!-- <div class="row">
            <div class="col-12">
              <div *ngIf="
                  appraisalMetricesData?.appraisal_metric_response_type ==
                  'comment'
                ">
                
                <et-rating *ngIf="
                    employeeAppraisalMetricesData
                      .employee_evaluation_metric_response_data.length == 0 &&
                    employeeAppraisalMetricesData.employee_appraisal_metrices_evaluation_status !=
                      'Approved'
                  " [employeeEvaluationMetricesDetails]="
                    employeeAppraisalMetricesData
                  " 
                  [appraisalMetricesData]="appraisalMetricesData"
                  [name]="userProfile.name" [oid]="userProfile.oid" [designation]="userProfile.unique_name"
                  (erRatingValueChange)="getEtRatingData($event)"></et-rating>
  
               
                <div class="row" *ngIf="
                    employeeAppraisalMetricesData
                      .employee_evaluation_metric_response_data.length > 0 &&
                    (employeeAppraisalMetricesData.employee_appraisal_metrices_evaluation_status ==
                      'Approved' ||
                      employeeAppraisalMetricesData.employee_appraisal_metrices_evaluation_status ==
                        'Submitted')
                  ">
                  <div class="col-1  pr-0">
                    <app-user-image [id]="userProfile.oid" [imgWidth]="'28px'" [imgHeight]="'28px'"></app-user-image>
                  </div>
                  <div class="col-7 pl-0 pr-0 pt-2">
                    <span [innerHtml]="
                        employeeAppraisalMetricesData
                          .employee_evaluation_metric_response_data[0].comment
                      "></span>
                  </div>
                  <div class="col-4 pt-2 subtle">
                    {{
                    employeeAppraisalMetricesData
                    .employee_evaluation_metric_response_data[0].updatedAt
                    | date: "MMM d, y, h:mm:ss a"
                    }}
                    by {{ userProfile.name }}
                  </div>
                </div>
               
                <div class="d-flex justify-content-center" style="color: gray" *ngIf="
                    employeeAppraisalMetricesData
                      .employee_evaluation_metric_response_data.length == 0 &&
                    employeeAppraisalMetricesData.employee_appraisal_metrices_evaluation_status ==
                      'Approved'
                  ">
                  You have'nt written anything!
                </div>
                <div class="row" *ngIf="employeeAppraisalMetricesData.approver_comment"
                  style="color: gray; font-size: 12px">
                  Approver Comment
                </div>
                <div class="row pt-2" *ngIf="employeeAppraisalMetricesData.approver_comment">
                  <div class="col-8">
                    <app-user-image [imgWidth]="'28px'" [imgHeight]="'28px'"
                      [id]="employeeAppraisalMetricesData.approved_by" [tooltip]="commentorTooltip" placement="right"
                      content-type="template" max-width="300">
                    </app-user-image>
                    <ng-template #commentorTooltip>
                      <div class="row tooltip-text" style="text-align: center">
                        <app-user-profile [type]="'name'" [oid]="employeeAppraisalMetricesData.approved_by">
                        </app-user-profile>
                      </div>
                      <div class="row tooltip-text">
                        <app-user-profile [type]="'role'" [oid]="employeeAppraisalMetricesData.approved_by">
                        </app-user-profile>
                      </div>
                    </ng-template>
                    <span style="color: gray; padding-left: 19px">
                      {{
                      employeeAppraisalMetricesData.approver_comment
                      ? employeeAppraisalMetricesData.approver_comment
                      : ""
                      }}
                    </span>
                  </div>
                  <div class="col-4 subtle">
                    {{
                    employeeAppraisalMetricesData.approver_action_date
                    ? (employeeAppraisalMetricesData.approver_action_date
                    | date: "MMM d, y, h:mm:ss a")
                    : ""
                    }} By <app-user-profile [type]="'name'" [oid]="employeeAppraisalMetricesData.approved_by">
                    </app-user-profile>
                  </div>
                </div>
              </div>
            </div>
          </div> -->
      </mat-expansion-panel>
    </mat-accordion>
  </div>
</div>
<!-- {{employeeAppraisalsData|json}}  -->
<!-- {{employeeAppraisalMetricesData|json}} -->
