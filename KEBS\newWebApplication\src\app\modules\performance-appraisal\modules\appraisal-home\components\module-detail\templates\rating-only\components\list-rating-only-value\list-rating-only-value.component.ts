import { Component, Input, OnInit,Output,EventEmitter } from '@angular/core';
import { EmployeeAppraisalsService } from '../../../../../../services/employee_appraisal/employee-appraisals.service';
import { LoginService } from 'src/app/services/login/login.service';
import { UtilityService } from 'src/app/services/utility/utility.service';
import { SharedLazyLoadedComponentsService } from 'src/app/modules/shared-lazy-loaded-components/services/shared-lazy-loaded-components.service';
import { MatDialog } from '@angular/material/dialog';
import { ErrorService } from 'src/app/services/error/error.service';
import { takeUntil } from 'rxjs/operators';
import { Subject } from 'rxjs';

import * as _ from 'underscore';
import { tick } from '@angular/core/testing';

@Component({
  selector: 'app-list-rating-only-value',
  templateUrl: './list-rating-only-value.component.html',
  styleUrls: ['./list-rating-only-value.component.scss'],
})
export class ListRatingOnlyValueComponent implements OnInit {
  icon: boolean = false;
  @Input()
  employeeAppraisalsData;
  @Input('employeeAppraisalData')
  employeeAppraisalData;
  @Output() selfRatingTotalScore = new EventEmitter<any>();
  @Input() toolTipValueForStarRating: any;
  @Input() attachmentBucket: any;
  @Input() allowEmployeeRating:any;
  @Input() employeeOid:any;

  @Input() l2EvalApprStatus:any;
  @Input() isLevel2ApprovalNeeded:any;

  totalStars: any = 5;
  totalScore: any = 0;
  readOnly: any = false;
  selfEvalStatus: any;

  employeeAppraisalMetricesData: any;
  appraisalCycleData: any;
  appraisalMetricesData: any;
  userProfile: any;
  scoreForDispaly: number = 0;
  starValue = [0, 25, 50, 75, 100];
  allowAttachmentL1EvalUpload: boolean = false;
  L1EvalAttachmentContextId:any = null;
  allowAttachmentEmpUpload: boolean = false;
  empAttachmentContextId:any = null;
  contextId:any = null;
  anyCommentExist:boolean = false;
  isRatingSaveLoading:boolean = false;
  isChatReadOnly:boolean = false;

  protected _onDestroy = new Subject<void>();

  constructor(
    private _EmployeeAppraisalsService: EmployeeAppraisalsService,
    private _auth: LoginService,
    public $dialog: MatDialog,
    private _util: UtilityService,
    private _lazyService: SharedLazyLoadedComponentsService,
    private _ErrorService: ErrorService
  ) {
    this.userProfile = this._auth.getProfile().profile;
  }

  ngOnInit(): void {
    this.l2EvalApprStatus = this.l2EvalApprStatus ? this.l2EvalApprStatus : "";

    this.getEmployeeAppraisalMetrices();  
    
  }
  /**
   * @description get response given
   */
  getStarRating(value) {
    alert(value);
  }

  /**
   * @description get response given
   */
  responseGiven(event) {
    console.log(event);
  }

  /**
   * @description open quick cta
   */
  openQuickCTA = () => {
    this._lazyService.openQuickCTAModal(null, this.$dialog);
  };

  /**
   * @description open comments
   */
  async openComments() {
    let msg;
    let inputData = {
      application_id: 92,
      unique_id_1: this.employeeAppraisalMetricesData._id,
      unique_id_2: '',
      application_name: 'Performance Appraisal',
      title: this.appraisalMetricesData?.appraisal_metric_name,
    };

    let modalParams = {
      inputData: inputData,
      context: {
        Name: this.appraisalMetricesData?.appraisal_metric_name,
        Status:
          this.employeeAppraisalMetricesData
            ?.employee_appraisal_metrices_evaluation_status,
      },
      commentBoxHeight: '100vh',
      commentBoxScrollHeight: '80%',
      isReadOnly:this.isChatReadOnly,
    };

    const { ChatCommentContextModalComponent } = await import(
      'src/app/modules/shared-lazy-loaded-components/chat-comment-context-modal/chat-comment-context-modal.component'
    );
    const openChatCommentContextModalComponent = this.$dialog.open(
      ChatCommentContextModalComponent,
      {
        height: '100%',
        width: '50%',
        position: { right: '0px' },
        data: { modalParams: modalParams },
      }
    );
  }

  /**
   * @description get et rating data
   */
  getEtRatingData(commentDetails) {
    console.log(commentDetails);
    this._EmployeeAppraisalsService
      .saveEmployeeResponse(commentDetails)
      .pipe(takeUntil(this._onDestroy))
      .subscribe(
        (result) => {
          // console.log(result);
          this._util.showMessage(
            'Comment added successfully!',
            'dismiss',
            2000
          );
          this.employeeAppraisalMetricesData.employee_evaluation_metric_response_data.push(
            {
              id: '',
              created_date: new Date(),
              comment:
                commentDetails['employee_evaluation_metric_response_data']
                  .comment,
              updatedAt: new Date(),
              createdAt: new Date(),
              activity_approvals: [],
            }
          );
          this.employeeAppraisalMetricesData.employee_appraisal_metrices_evaluation_status =
            'Submitted';
        },
        (error) => {
          console.log(error);
          // this._util.sendMsg('etRatingAPIFailed');
          this._ErrorService.userErrorAlert(
            error.error.code,
            'Some Error Happened in completing the Activity'
          );
        }
      );
  }

  /**
   * @description get status color
   */
  getStatusColor = (status) => {
    status = status ? status.toLowerCase() : '';
    if (status == 'open') return 'gray';
    else if (status == 'submitted') return '#ff9f1a';
    else if (status == 'approved') {
      return '#6ab04c';
    } else if (status == 'rejected') return '#cf0001';
    else if (status == 'closed') return 'green;';
  };

  /**
   * @description get appraisal cycle
   */
  getAppraisalCycle(appraisalCycleId) {
    this._EmployeeAppraisalsService
      .getAppraisalCycleDataById(appraisalCycleId)
      .pipe(takeUntil(this._onDestroy))
      .subscribe(
        (result: any) => {
          if (result.error == 'N') {
            this.appraisalCycleData = result.data;
            console.log(this.appraisalCycleData, 'cycle data');
          }
        },
        (error) => {
          console.log('Error getting appraisal cycle' + error);
        }
      );
  }

  /**
   * @description get appraisal metrices data
   */
  getAppraisalMetrices(appraisalMetricesId) {
    this._EmployeeAppraisalsService
      .getAppraisalMetricesById(appraisalMetricesId)
      .pipe(takeUntil(this._onDestroy))
      .subscribe(
        (result: any) => {
          if (result.error == 'N') {
            this.appraisalMetricesData = result.data;
            console.log(this.appraisalMetricesData, 'metrices data');
          }
        },
        (error) => {
          console.log('Error getting appraisal Metrices' + error);
        }
      );
  }

  /**
   * @description get employee appraisal data
   */
  getEmployeeAppraisalMetrices() {

    let bodyParams = {
      employeeAppraisalMetricesId : this.employeeAppraisalsData.employee_appraisal_metrices_id,
      employeeOid:this.employeeOid
    }

    this._EmployeeAppraisalsService
      .getEmployeeAppraisalMetricesById(
        bodyParams
      )
      .pipe(takeUntil(this._onDestroy))
      .subscribe(
        (result: any) => {
          if (result.error == 'N') {
            this.employeeAppraisalMetricesData = result.data;

            this.getAppraisalMetrices(
              this.employeeAppraisalMetricesData.appraisal_metrices_id
            );

            this.scoreForDispaly = (this.employeeAppraisalMetricesData?.employee_appraisal_metrices_scored_obtained /100) * this.employeeAppraisalMetricesData?.appraisal_metrices_weightage;
            this.scoreForDispaly = Math.round((this.scoreForDispaly + Number.EPSILON) * 100) / 100;
            this.empAttachmentContextId =  this.employeeAppraisalMetricesData?.attachmentContextId?.selfEvalAttachmentId;
            this.L1EvalAttachmentContextId = this.employeeAppraisalMetricesData?.attachmentContextId?.l1EvalAttachmentId;            
            this.allowAttachmentEmpUpload = this.employeeAppraisalMetricesData?.employee_appraisal_metrices_evaluation_status == 'Open' && this.allowEmployeeRating ? true : false;
            this.isChatReadOnly = this.employeeAppraisalMetricesData?.employee_appraisal_metrices_evaluation_status == 'Open' && this.allowEmployeeRating ? false : true;

            result?.data?.employee_appraisal_metrices_evaluators_details.forEach(
              (i) => {
                if (i?.employee_appraisal_metrices_evaluator_type=="manager") {
                  i?.employee_appraisal_metrices_evaluators.forEach((j) => {
                    if (j?.is_self_evaluator) {
                      this.totalScore =
                        j?.employee_appraisal_metrices_evaluator_score_awarded;
                      this.selfEvalStatus =
                        j?.employee_appraisal_metrices_evaluator_status;
                    }
                  });
                  return;
                }
              }
            );

            this.calculateScore();
            // this.getAppraisalCycle(
            //   this.employeeAppraisalMetricesData.appraisal_cycle_id
            // );
            this.checkAnyCommentExistOrNot();
          } else {
            console.log('Error getting emplyee evlauation mnteices data');
          }

          console.log(result, 'emplyee evlauation mnteices data');
        },
        (error) => {
          console.log('Error getting evaluation metrice data' + error);
        }
      );
  }
  click() {
    this.icon = !this.icon;
  }

  /**
   * @description value changes in rating
   */
  valueChangeInErRating(text) {
    // use it to save
    console.log(text);
  }

  /**
   * @description change in org activities
   */
  changeInOrgActivities(event) {
    //to do something
  }

  /**
   * @description unsubscribe all the subscription used in this file.
   */
  ngOnDestroy(): void {
    this._onDestroy.next();
    this._onDestroy.complete();
  }

  ratingStars = [];

  starsClicked = [];

  /**
   * @description detect changes
   */
  ngOnChanges() {
    this.calculateScore();
  }

  /**
   * @description calculate score for selected stars
   */
  calculateScore() {
    let starsSelected = 0;

    if (this.totalScore)
      starsSelected = (this.totalScore / 100) * this.totalStars;

    let starToBeFilled = this.starValue.indexOf(this.totalScore);

    starsSelected =
      starToBeFilled != -1
        ? starToBeFilled == 0 && this.selfEvalStatus == 'Open'
          ? 0
          : starToBeFilled + 1
        : Math.round(starsSelected);

    this.ratingStars = [];

    for (let i = 0; i < this.totalStars; i++)
      this.ratingStars.push({
        starValue: i + 1,
        clicked: starsSelected > 0 && starsSelected >= i + 1 ? true : false,
        tooltipValue: this.toolTipValueForStarRating[i],
      });

    this.starsClicked = _.where(this.ratingStars, { clicked: true });
  }

  /**
   * @description get start color
   */
  getStarColor(clicked) {
    return clicked ? '#FFD700' : '#66615B';
  }

  /**
   * @description on star click function
   */
  onClick(starIndex) {

    this.isRatingSaveLoading = true;
    
    for (let i = 0; i < this.totalStars; i++)
      this.ratingStars[i].clicked = i <= starIndex ? true : false;

    let value =
      starIndex <= this.starValue.length &&
      typeof this.starValue[starIndex] == 'number'
        ? this.starValue[starIndex]
        : (this.ratingStars[starIndex].starValue / this.totalStars) * 100;

    let request = {
      appraisal_year: this.employeeAppraisalMetricesData.appraisal_year,
      employee_oid: this.employeeAppraisalMetricesData.employee_oid,
      eval_reponse: [
        {
          appraisal_cycle_id:
            this.employeeAppraisalMetricesData.appraisal_cycle_id,
          appraisal_metrices_id:
            this.employeeAppraisalMetricesData.appraisal_metrices_id,
          appraisal_metrices_name:
            this.appraisalMetricesData.appraisal_metric_name,
          appraisal_module_id:
            this.employeeAppraisalMetricesData.appraisal_module_id,
          approver_action_date: new Date(),
          approver_action_is_active: true,
          employe_oid: this.employeeAppraisalMetricesData.employee_oid,
          employee_appraisal_metrices_evaluator_comment: '',
          employee_appraisal_metrices_evaluator_oid:
            this.employeeAppraisalMetricesData.employee_oid,
          employee_appraisal_metrices_evaluator_score_awarded: value,
          employee_appraisal_metrices_evaluator_status: 'Saved',
          employee_appraisal_metrices_evaluator_type: 'manager',
          employee_appraisal_module_group_name:
            this.employeeAppraisalMetricesData.group_name,
          employee_evaluation_metrices_id:
            this.employeeAppraisalsData.employee_appraisal_metrices_id,
          evaluator_comment: '',
          evaluator_score_awarded: value,
          id: this.employeeAppraisalsData.employee_appraisal_metrices_id,
        },
      ],
      is_draft_mode: true,
    };

    this._EmployeeAppraisalsService
      .updMetricesScore(request)
      .pipe(takeUntil(this._onDestroy))
      .subscribe(
        (result: any) => {
          if (result.err == 'N') {
            // this._util.showMessage('Rating Saved Successfully..!', 'Dismiss');

            this.calculateAndTriggerSelfRatingTotalScore();
            
          } else {
            console.log('Error while saving employee appraisal metrices data');
            this._util.showErrorMessage(
              'Error while saving your rating',
              'Dismiss'
            );
          }

          this.isRatingSaveLoading = false;
        },
        (error) => {
          this.isRatingSaveLoading = false;
          console.log('Error getting evaluation metrice data' + error);
          this._util.showErrorMessage(
            'Error while saving your rating',
            'Dismiss'
          );
        }
      );
  }

  /**
   * @description get text-length
   */
  getTextLength(text) {
    return text.length;
  }

  /**
   * @description To Trigger Self Rating total score
   */
  calculateAndTriggerSelfRatingTotalScore() {
    let selfRatingTotalScoreRequest = {
      appraisal_year: this.employeeAppraisalMetricesData.appraisal_year,
      employee_oid: this.employeeAppraisalMetricesData.employee_oid,
      appraisal_cycle_id: this.employeeAppraisalMetricesData.appraisal_cycle_id,
      appraisal_module_id:
        this.employeeAppraisalMetricesData.appraisal_module_id,
      evaluator_oid: this.employeeAppraisalMetricesData.employee_oid,
      employee_appraisal_metrices_evaluator_type: 'manager',
    };

    this._EmployeeAppraisalsService
      .calculateEvlScoreForAllIndividualMetricesAndTotal(
        selfRatingTotalScoreRequest
      )
      .pipe(takeUntil(this._onDestroy))
      .subscribe(
        (result: any) => {
          if (result.err == 'N') {
            if(Array.isArray(result?.data)){
              for(let i=0;i<result?.data.length;i++)
                if(result?.data[i]?._id==this.employeeAppraisalMetricesData.employee_oid){
                  this.selfRatingTotalScore.emit(result.data[i]);
                  break;
                }
            }
          } else {
            console.log(
              'Error while calculating employee self rating total score'
            );
          }
        },
        (error) => {
          console.log(
            'Error while calculating employee self rating total score' + error
          );
        }
      );
  }

  /**
   * @description get context id for attachment upload
   */
  getContextIdForAttachments(type:string) {   
    
    if(type == 'Employee'){
      return this.empAttachmentContextId && this.empAttachmentContextId != '' ? this.empAttachmentContextId : null;
    }
    else if(type== 'L1Eval'){
      return this.L1EvalAttachmentContextId && this.L1EvalAttachmentContextId != '' ? this.L1EvalAttachmentContextId : null;
    }   
  }

  contextIdChangeInattachments(event: Event) {
    if (this.getContextIdForAttachments('Employee') != event) {
      let req = {
        employee_appraisal_metrices_id:
          this.employeeAppraisalMetricesData?._id,
        employee_appraisal_metrices_evaluator_attachments: event,
        employee_appraisal_metrices_evaluator_oid:
          this.employeeAppraisalMetricesData?.employee_oid,
      };

      if(!this.employeeAppraisalData?.employeeAppraisalModuleData?.isSelfEvalRequired)req['type'] = 'employee';

      this._EmployeeAppraisalsService
        .contextIdChangeInattachments(req)
        .subscribe(
          (res) => {
            this._util.showMessage(
              'Your Document Successfully uploaded',
              'Dismiss'
            );
          },
          (err) => {
            this._util.showMessage(
              'Error while uploading your document',
              'Dismiss'
            );
          }
        );
    }
  }

   /**
   * @description To Trigger Self Rating total score
   */
    checkAnyCommentExistOrNot() {
      let commentExistOrNotCheckRequest = {
        application_id: 92,
        unique_id_1: this.employeeAppraisalMetricesData._id,
        unique_id_2: '',
        application_name: 'Performance Appraisal',
        title: this.appraisalMetricesData?.appraisal_metric_name,
      };
  
      this._EmployeeAppraisalsService
        .checkAnyCommentExistOrNot(
          commentExistOrNotCheckRequest
        )
        .pipe(takeUntil(this._onDestroy))
        .subscribe(
          (result: any) => {
            if (result.err == 'N') {
              this.anyCommentExist = result.data;
            } else {
              console.log(
                'Error while checking any comment exist or not'
              );
            }
          },
          (error) => {
            console.log(
              'Error while checking any comment exist or not' + error
            );
          }
        );
    }
}
