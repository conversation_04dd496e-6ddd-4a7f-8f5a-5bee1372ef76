

<span *ngIf="isScoreVisibile" [ngStyle]="{'padding-right': scoreAndStarSeperation+'px'}">{{starScore}}/{{starCount}}</span>
<span class="fa"
[ngClass]="{
    'fa-star': star == 1,
    'fa-star-half-empty' : star == 0.5,
    'fa-star-o' : star == 0
}" 
[ngStyle]="{
    'font-size': starSize+'px',
    'padding-right':spacing+'px',
     'color':starColor
}"
*ngFor="let star of stars" matTooltip="Total Score - {{starRating}}">
</span>

