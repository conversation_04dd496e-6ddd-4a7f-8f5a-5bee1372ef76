import { Component, Input, OnInit } from '@angular/core';

@Component({
  selector: 'rating-stars',
  templateUrl: './rating-stars.component.html',
  styleUrls: ['./rating-stars.component.scss']
})
export class RatingStarsComponent implements OnInit {

  @Input() starCount: number;
  @Input() starSize: number;
  @Input() spacing: number;
  @Input() totalScore: number;
  @Input() score: number;
  @Input() starColor: string;
  @Input() isScoreVisibile:boolean
  @Input() scoreAndStarSeperation : number
  starScore: any;
  starRating: any;
  stars: any = [];
  constructor() { }

  ngOnInit(): void {
    let starScore = (this.starCount / this.totalScore) * this.score;
    let score = (this.starCount / this.totalScore)
    console.log("score",score)
    this.starRating = Math.round( ( this.score + Number.EPSILON ) * 100 ) / 100
    this.starScore = starScore
    let wholeVal = Math.floor(starScore);
    // console.log(starScore - wholeVal)
    // console.log(wholeVal);
    for (let i = 1; i <= this.starCount; i++) {
      if (i <= wholeVal)
        this.stars.push(1)
      else
        this.stars.push(0);
      if (i == this.starCount) {
        if (starScore - wholeVal > 0) {
          this.stars[wholeVal] = 0.5
          console.log(this.stars)
        }
      }
    }
  }

}
