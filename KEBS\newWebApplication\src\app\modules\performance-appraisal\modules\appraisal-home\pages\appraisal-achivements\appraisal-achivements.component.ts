import {
  Compiler,
  Component,
  ComponentFactoryResolver,
  OnInit,
  ViewChild,
  ViewContainerRef,
} from '@angular/core';

import { EmployeeAppraisalsService } from '../../services/employee_appraisal/employee-appraisals.service';
import { AppraisalEvaluatorsService } from '../../services/appraisal_evaluators/appraisal-evaluators.service';
import { AppraisalCycleService } from 'src/app/modules/performance-appraisal/modules/appraisal-configuration/pages/appraisal-cycles/services/appraisal-cycle.service';
import * as moment from 'moment';

import { Router, ActivatedRoute, ParamMap } from '@angular/router';
import { takeUntil } from 'rxjs/operators';
import { Subject } from 'rxjs';
import { ErrorService } from 'src/app/services/error/error.service';
import { UtilityService } from 'src/app/services/utility/utility.service';
import { LoginService } from 'src/app/services/login/login.service';
@Component({
  selector: 'app-appraisal-achivements',
  templateUrl: './appraisal-achivements.component.html',
  styleUrls: ['./appraisal-achivements.component.scss'],
})
export class AppraisalAchivementsComponent implements OnInit {
  //viewChilds
  @ViewChild('scoreBoardContainer', { read: ViewContainerRef })
  private scoreBoardContainerRef: ViewContainerRef;
  @ViewChild('userWorkSpaceContainer', { read: ViewContainerRef })
  private userWorkSpaceContainerRef: ViewContainerRef;
  @ViewChild('moduleDetailContainer', { read: ViewContainerRef })
  private moduleDetailContainerRef: ViewContainerRef;

  protected _onDestroy = new Subject<void>();

  ngOnDestroy() {
    if (this.scoreBoardContainerRef) this.scoreBoardContainerRef.clear();
    if (this.userWorkSpaceContainerRef) this.userWorkSpaceContainerRef.clear();
    if (this.moduleDetailContainerRef) this.moduleDetailContainerRef.clear();
    this._onDestroy.next();
    this._onDestroy.complete();
  }

  //data for testing
  appraisalCycleData: any;

  appraisalYear:any;


  oid:any;

  constructor(
    private readonly componentFactoryResolver: ComponentFactoryResolver,
    private compiler: Compiler,
    private _EmployeeAppraisalsService: EmployeeAppraisalsService,
    private _AppraisalEvaluatorsService: AppraisalEvaluatorsService,
    private route: ActivatedRoute,
    private _AppraisalCycleService: AppraisalCycleService,
    private _ErrorService: ErrorService,
    private _util: UtilityService,
    private _loginService:LoginService,
  ) {}

  async ngOnInit() {
    let today = moment();
    this.appraisalYear = await this.getDefaultYear();
    if(this.appraisalYear == ''){
    if (today.month() >= 3) {
      this.appraisalYear = today.format('YYYY')
    }
    else {
      this.appraisalYear = today.subtract(1, 'years').format('YYYY') 
    }
  }
    
    this.route.parent.params.subscribe((res) => {
      console.log(res);
      if (res.oid == 1) {
        this.oid = this._loginService.getProfile().profile.oid;
        this.getAppraisalCycleData();
      } else {
        this.oid = res['oid'];
        this.getAppraisalCycleDataForAdmin(res['oid']);
      }
    });
  }

  async getAppraisalCycleData() {
    this._EmployeeAppraisalsService
      .getAppraisalMetricesAll(
        await this._AppraisalEvaluatorsService.getCurrentUserOID(),
        this.appraisalYear
      )
      .subscribe(
        (result: any) => {
          if (result.error == 'N') {
            this.appraisalCycleData = result.data[0];
            this.lazyLoadScoreBoard(result?.defaultDisplayModule).then(() => {
              if(!result?.defaultDisplayModule)
                this.lazyLoadUserWorkSpace(result?.defaultDisplayModule);
            });
          } else {
            alert('error');
          }
        },
        (error) => {
          console.log(error);
        }
      );
  }

  async getAppraisalCycleDataForAdmin(oid) {
    this._EmployeeAppraisalsService.getAppraisalMetricesAll(oid, this.appraisalYear).subscribe(
      (result: any) => {
        if (result.error == 'N') {
          this.appraisalCycleData = result.data[0];
          this.lazyLoadScoreBoard(result?.defaultDisplayModule).then(() => {
            if(!result?.defaultDisplayModule)
              this.lazyLoadUserWorkSpace(result?.defaultDisplayModule);
          });
        } else {
          alert('error');
        }
      },
      (error) => {
        console.log(error);
      }
    );
  }

  private lazyLoadScoreBoard(defaultDisplayModule:any): Promise<any> {
    return new Promise((resolve, reject) => {
      import('../../components/score-board/score-board.component').then(
        (scoreBoard) => {
          const ngModuleFactory = this.compiler.compileModuleSync(
            scoreBoard.ScoreBoardModule
          );
          const ngModule = ngModuleFactory.create(
            this.scoreBoardContainerRef.injector
          );
          const component = ngModule.componentFactoryResolver.resolveComponentFactory(
            scoreBoard.ScoreBoardComponent
          );
          const componentRef = this.scoreBoardContainerRef.createComponent(
            component
          );
          componentRef.instance.scoreBoardIpData = this.appraisalCycleData?.employee_appraisal_structure[0];
          componentRef.instance.scoreBoardIpData.acknowledge_status = this.appraisalCycleData?.acknowledge_status;
          componentRef.instance.scoreBoardIpData.employeeOid = this.oid;
          componentRef.instance.scoreBoardIpData.defaultDisplayModule = defaultDisplayModule;
          componentRef.instance.changeInScoreBoard.subscribe((res) => {
            console.log(res);
            if (res.event == 'click' && res.changeType == 'module') {
              //clearing the workspace view
              if (this.userWorkSpaceContainerRef)
                this.userWorkSpaceContainerRef.clear();
              //enable module detail view
              this.lazyLoadModuleDetailView(res);
            }
            else if(res.event == 'deselect'){
              this.moduleDetailContainerRef.clear();
              this.lazyLoadUserWorkSpace(defaultDisplayModule);
            }
            else if (res.changeType == 'year'){
              if (this.scoreBoardContainerRef) this.scoreBoardContainerRef.clear();
              if (this.userWorkSpaceContainerRef) this.userWorkSpaceContainerRef.clear();
              this.moduleDetailContainerRef.clear();
              this.appraisalYear=res['value']
             // this.getAppraisal();
             this.route.parent.params.subscribe((res) => {
              console.log(res);
              if (res.oid == 1) {
                this.getAppraisalCycleData();
              } else {
                this.getAppraisalCycleDataForAdmin(res['oid']);
              }
            });
            
            }
          });
          resolve(1);
        }
      );
    });
  }

  // async getAppraisal(){
  //   this.oid = await this._AppraisalEvaluatorsService.getCurrentUserOID();
  //   this.getAppraisalCycleDataForAdmin(this.oid);
  // }

  private lazyLoadModuleDetailView(selectedModuleData): void {
    if (this.moduleDetailContainerRef)this.moduleDetailContainerRef.clear();
    import('../../components/module-detail/module-detail.component').then(
      (moduleView) => {
        const ngModuleFactory = this.compiler.compileModuleSync(
          moduleView.AppraisalModuleDetailModule
        );
        const ngModule = ngModuleFactory.create(
          this.moduleDetailContainerRef.injector
        );
        const component = ngModule.componentFactoryResolver.resolveComponentFactory(
          moduleView.ModuleDetailComponent
        );       
        if (this.moduleDetailContainerRef)this.moduleDetailContainerRef.clear();
        const componentRef = this.moduleDetailContainerRef.createComponent(
          component
        );
        componentRef.instance.selectedModuleData = selectedModuleData;
        componentRef.instance.appraisalYear = this.appraisalYear;
        componentRef.instance.employeeOid = this.oid;
        componentRef.instance.empAppraisalAllDetails =this.appraisalCycleData;
      }
    );
  }

  private lazyLoadUserWorkSpace(defaultDisplayModule): void {
    import(
      '../../components/appraisal-workspace/appraisal-workspace.component'
    ).then((workspace) => {
      const ngModuleFactory = this.compiler.compileModuleSync(
        workspace.AppraisalWorkSpaceModule
      );
      const ngModule = ngModuleFactory.create(
        this.userWorkSpaceContainerRef.injector
      );
      const component = ngModule.componentFactoryResolver.resolveComponentFactory(
        workspace.AppraisalWorkspaceComponent
      );
      const componentRef = this.userWorkSpaceContainerRef.createComponent(
        component
      );
      componentRef.instance.appraisalYear = this.appraisalYear
      componentRef.instance.defaultDisplayModule = defaultDisplayModule;
    });
  }

/**
 * @description get appraisal default year
 */
  getDefaultYear(){
    let req = {
      configuration_name:"default_display_apprisal_year"
     }
     return new Promise((resolve, reject) => {
     this._AppraisalCycleService.getDefaultAppraisalYear(req).pipe(takeUntil(this._onDestroy)).subscribe( (res) => {
     if(res['data'].length>0){
      resolve(res['data'][0]['configuration_data']);
     }
     else{
       this._util.showMessage('No data Found','Dismiss');
       resolve('');
     }
    } , (err) => {
      this._ErrorService.userErrorAlert(
        err.error.code,
        'Some Error Happened in completing the Activity',
        err.error.errMessage
      );
      reject(err);
    });
  });
  }
}
