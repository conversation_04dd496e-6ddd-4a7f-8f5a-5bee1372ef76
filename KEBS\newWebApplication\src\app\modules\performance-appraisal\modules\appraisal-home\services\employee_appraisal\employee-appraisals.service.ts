import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { BehaviorSubject, Subject, Subscription } from 'rxjs';
import { RolesService } from 'src/app/services/acl/roles.service';

@Injectable({
  providedIn: 'root',
})
export class EmployeeAppraisalsService {
  constructor(private _httP: HttpClient,private _roles: RolesService) { }

  inlineEditCallbackSubscription: Subscription;
  
  activitySubject = new BehaviorSubject({});;
  public getActivityObservable = this.activitySubject.asObservable();

  private inlineEditCallbackSubject = new BehaviorSubject({});
  inlineEditCallback = this.inlineEditCallbackSubject.asObservable();

  /**  
  * @description set call back response
  */
  setInlineEditCallbackSubject(inlineEditResponse) {
    this.inlineEditCallbackSubject.next(inlineEditResponse);
  }

  /**  
  * @description set activity observable
  */
  setActivityObservable(data) {
    console.log(data)
    this.activitySubject.next(data);
  }

  /** 
  * @description get Appriasl metrices based on Employee OID
  */
  getAppraisalMetricesAll(employeeOID,year) {
    return this._httP.post(
      '/api/appraisal/employeeAppraisal/getEmployeeAppraisalByOID',
      {
        employee_oid: employeeOID,
        appraisal_year:year
      }
    );
  }

  getAppraisalModuleById(appraisalModuleId) {
    return this._httP.post('/api/appraisal/modules/getAppraisalModulesById', {
      appraisalModuleId: appraisalModuleId,
    });
  }

  getEmployeeAppraisalMetricesById(bodyParams) {

    let t = {};

    if(typeof bodyParams === 'object')
      t = bodyParams
    else
      t = {employeeAppraisalMetricesId: bodyParams}
      
    return this._httP.post(
      '/api/appraisal/employeeMetrices/getEmployeeAppraisalMetricesById',
      t
    );
  }

  getAppraisalCycleDataById(appraisalCycleId) {
    return this._httP.post('/api/appraisal/cycles/getAppraisalcycleById', {
      appraisalCycleId: appraisalCycleId,
    });
  }

  getAppraisalMetricesById(appraisalMetricesId) {
    return this._httP.post('/api/appraisal/metrices/getAppraisalMetricesById', {
      appraisalMetricesId: appraisalMetricesId,
    });
  }

  updateEvaluatorScoreAwarded(updateScoreData) {
    return this._httP.post(
      '/api/appraisal/employeeMetrices/updateEvaluatorScoreAwarded',
      updateScoreData
    );
  }
  
  updateEvaluatorScore(bodyParams) {
    return this._httP.post(
      '/api/appraisal/employeeMetrices/evaluateMetrices',
      bodyParams
    );
  }

  updMetricesScore(bodyParams) {
    return this._httP.post(
      '/api/appraisal/employeeMetrices/updMetricesScore',
      bodyParams
    );
  }

  saveEmployeeResponse(response) {
    return this._httP.post(
      '/api/appraisal/employeeMetrices/updateEmployeeResponse',
      response
    );
  }

  saveEmployeeResponseAsDraft(response) {
    return this._httP.post(
      '/api/appraisal/employeeMetrices/updateEmployeeResponseAsDraft',
      response
    );
  }

  deleteEmployeeResponse(response) {
    return this._httP.post(
      '/api/appraisal/employeeMetrices/deleteEmployeeResponse',
      response
    );
  }

  deleteEmployeeActivities(response) {
    return this._httP.post(
      '/api/appraisal/employeeMetrices/deleteEmployeeActivities',
      response
    );
  }

  private msg = new Subject()

  sendMsg = (msg) => {
    this.msg.next(msg)
  }
  getMsg = () => {
    return this.msg.asObservable();
  }


  getEmployeeMetricesForCustomer = (empOid, apCycleId, ApModuleId) => {
    return this._httP.post("/api/appraisal/customerReview/getEmployeeMetricesForCustomers", {
      "employee_oid": empOid,
      "appraisal_cycle_id": apCycleId,
      "appraisal_module_id": ApModuleId
    })
  }

  getEmpDetailsByEvlIdAndYear = (bodyparams) => {
    return this._httP.post("/api/appraisal/employeeMetrices/getEmpDetailsBasedOnEmpOid", bodyparams)
  }

  getEmpDetailsByEmpOid = (bodyparams) => {
    return this._httP.post("/api/appraisal/employeeMetrices/getEmpDetailsBasedOnModule", bodyparams)
  }

  getEmployeemetrics = (bodyparams) => {
    return this._httP.post("/api/appraisal/employeeMetrices/getAppraisalMetricesByEmpidMidCidYear", bodyparams)
  }

  getEmployeeList(filterConfig,oid) {
    return this._httP.post("/api/appraisal/evaluator/getUDRFDataForEval",
    {
      "filterConfig" : filterConfig,
      "eval_oid" : oid
    })
  }

  getAssesmentCard(filterConfig,oid) {
    return this._httP.post("/api/appraisal/evaluator/getUDRFDataCountForEval",{
      "filterConfig" : filterConfig,
      "eval_oid" : oid
    })
  }

  getEvalPopupData = (bodyparams) => {
    return this._httP.post("/api/appraisal/employeeMetrices/getMetricesForEvalPopup", bodyparams)
  }

   /** 
  * @description get Data for PMS admin Report
  */

  getOverallPMSReportDataForDownload=(appraisalYear)=>  {
    
    return  this._httP.post("/api/appraisal/employeeAppraisal/getYearAndModuleScoreForReport",{
      appraisal_year: appraisalYear
    });

  }

   /** 
  * @description update meeting Schedule
  */
  updMeetingScheduledorNot=(bodyParams)=> {
    return  this._httP.post("/api/appraisal/employeeAppraisal/updMeetingScheduledorNot",bodyParams);
  }

  /** 
  * @description update is acknowledeged status
  */
   updAcknowledgedOrNot=(bodyParams)=> {
    return  this._httP.post("/api/appraisal/employeeAppraisal/updAcknowledgedOrNot",bodyParams);
  }

   /** 
  * @description update star Rating
  */
  // updStarRating=(bodyParams)=> {
  //   return  this._httP.post("/api/appraisal/employeeAppraisal/updMeetingScheduledorNot",bodyParams);
  // }

  /** 
  * @description view other managers data
  */
  getCarouselData=(bodyParams)=> {
    return  this._httP.post("/api/appraisal/employeeMetrices/getOtheEvlMetricesdata",bodyParams);
  }

  /** 
  * @description get employee's sub division for enabling level 2 approver submission
  */
  getEmpSubDivforlvl2Submission(data){
    return this._httP.post(
      '/api/appraisal/employeeMetrices/getEmpSubDivforlvl2Submission',
      data
    )
  }

  /** 
  * @description get level 2 approver submission needed or not
  */
  getLevel2ApprovalNeeded(data){
    return this._httP.post(
      '/api/appraisal/employeeMetrices/getLevel2ApprovalNeeded',
      data
    )
  }

  /** 
  * @description get employee's sub division
  */
  getEmpSubDiv(data){
    return this._httP.post(
      '/api/appraisal/employeeMetrices/getEmpSubDiv',
      data
    )
  }

  /** 
  * @description get all appraisal year
  */
  getAllAppraisalYear=()=> {
    try {
      return new Promise((resolve, reject) => {
        this._httP
          .post("/api/appraisal/evaluator/getAllYearFilter", {})
          .subscribe(
            res => {
              return resolve(res);
            },
            err => {
           
              return reject(err);
            }
          );

      });
    } catch (err) {
      return Promise.reject();
    }
  }

  /** 
  * @description submit employee appraisal response
  */
   submitEmplpyeeSelfEvaluationResponse=(bodyParams)=> {    
      return this._httP.post("/api/appraisal/employeeMetrices/submitEmplpyeeSelfEvaluationResponse", bodyParams);   
   }

   /** 
   * @description update acknowledge status
   */
   updAcknowledgeStatus=(bodyParams)=>{
      return this._httP.post("/api/appraisal/employeeAppraisal/updAcknowledgeStatus", bodyParams);   
   }

   /** 
   * @description to display self rating total score
   */
    calculateEvlScoreForAllIndividualMetricesAndTotal=(bodyParams)=>{
      return this._httP.post("/api/appraisal/employeeMetrices/calculateEvlScoreForAllIndividualMetricesAndTotal", bodyParams);   
    }
    
    /** 
    * @description to display self rating total score
    */
     calculateEmployeeAppraisalGroupTotalScore=(bodyParams)=>{
      return this._httP.post("/api/appraisal/employeeAppraisal/calculateEmployeeAppraisalGroupTotalScore", bodyParams);   
    }

    /** 
    * @description update overall feedback on appraisal
    */
    updEvaluatorFeedBackOnAppraisal=(bodyParams)=>{
      return this._httP.post("/api/appraisal/employeeAppraisal/updEvaluatorFeedBackOnAppraisal", bodyParams);   
    }

    /** 
    * @description update overall feedback on appraisal
    */
     getTooltipValueForStarRating=(bodyParams)=>{
      return this._httP.post("/api/appraisal/employeeAppraisal/getTooltipValueForStarRating", bodyParams);   
    }

    /** 
    * @description upload document for employee appraisal metrices 
    */
     contextIdChangeInattachments=(bodyParams)=>{
      return this._httP.post("/api/appraisal/employeeMetrices/contextIdChangeInattachments", bodyParams);   
    }    
    
    /** 
    * @description get upload attachment bucket details 
    */
     getAppraisalAttachmentBucketDetails=(bodyParams)=>{
      return this._httP.post("/api/appraisal/employeeMetrices/getAppraisalAttachmentBucketDetails", bodyParams);   
    }  

    /** 
    * @description get upload attachment bucket details 
    */
     checkAnyCommentExistOrNot=(bodyParams:any)=>{
      return this._httP.post("/api/appraisal/employeeMetrices/checkAnyCommentExistOrNot", bodyParams);   
    }

    /** 
    * @description delete employee appraisal
    */
    deleteEmployeeAppraisal=(bodyParams:any)=>{
      return this._httP.post("/api/appraisal/employeeAppraisal/deleteEmployeeAppraisal", bodyParams);   
    }

     /** 
    * @description delete employee appraisal
    */
     getAllEvaluatorsByEmpAppraisalMetricesId=(bodyParams:any)=>{
      return this._httP.post("/api/appraisal/employeeMetrices/getAllEvaluatorsByEmpAppraisalMetricesId", bodyParams);   
    }
    
}
