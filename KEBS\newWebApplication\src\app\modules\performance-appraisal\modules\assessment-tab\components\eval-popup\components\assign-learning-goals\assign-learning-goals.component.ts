import { Component, Inject, OnInit } from '@angular/core';
import {
  FormArray,
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';

import {
  MatDialog,
  MatDialogRef,
  MAT_DIALOG_DATA,
} from '@angular/material/dialog';

import * as _ from 'underscore';

// Services
import { WorkflowService } from 'src/app/services/workflow/workflow.service';
import { AppraisalModulesService } from 'src/app/modules/performance-appraisal/modules/appraisal-configuration/pages/appraisal-modules/services/appraisal-modules.service';
import { AppraisalMetricesService } from 'src/app/modules/performance-appraisal/modules/appraisal-configuration/pages/appraisal-metrices/services/appraisal-metrices.service';
import { AppraisalCycleService } from 'src/app/modules/performance-appraisal/modules/appraisal-configuration/pages/appraisal-cycles/services/appraisal-cycle.service';
import { EmployeeAppraisalsService } from 'src/app/modules/performance-appraisal/modules/appraisal-configuration/pages/employee-appraisals/services/employee-appraisals.service';
import { AppraisalEvaluatorsService } from 'src/app/modules/performance-appraisal/modules/appraisal-home/services/appraisal_evaluators/appraisal-evaluators.service';
import { UtilityService } from '../../../../../../../../services/utility/utility.service';
import { GraphApiService } from 'src/app/services/graph/graph-api.service';
import { ErrorService } from 'src/app/services/error/error.service';

import {
  DateAdapter,
  MAT_DATE_FORMATS,
  MAT_DATE_LOCALE,
} from '@angular/material/core';
import {
  MomentDateAdapter,
  MAT_MOMENT_DATE_ADAPTER_OPTIONS,
} from '@angular/material-moment-adapter';
import { MatDatepicker } from '@angular/material/datepicker';
import * as moment from 'moment';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { T } from '@angular/cdk/keycodes';



@Component({
  selector: 'app-assign-learning-goals',
  templateUrl: './assign-learning-goals.component.html',
  styleUrls: ['./assign-learning-goals.component.scss'],
})
export class AssignLearningGoalsComponent implements OnInit {

  protected _onDestroy = new Subject<void>();
  
  isLoading: boolean = false;
  appraisalCycleId: String;
  appraisalCycleName = new FormControl();
  appraisalCycleList: any = [];
  appraisalMetricesData: any = [];
  appraisalMetricesArr: any = [];
  appraisalCycleEmployeesMapped = [];
  employeeId = [];
  appraisalYear: any;
  moduleId:any;
  appraisalConfigs: any ;
  // {
  //   max_score: 100,
  //   appraisal_year: 2025,
  //   scoringWeightageCustomType: null,
  //   appraisalCycleId: '',
  //   scoringWeightageType: 'all',
  //   employeeAppraisalModuleId:'5fca0bd7e5e9e026e6d2e78e',
  //   employeeAppraisalModuleGroupName: 'Certificate'
  // };

  
  constructor(
    private fb: FormBuilder,
    private _AppraisalModulesService: AppraisalModulesService,
    private _AppraisalMetricesService: AppraisalMetricesService,
    private _AppraisalCycleService: AppraisalCycleService,
    private _EmployeeAppraisalsService: EmployeeAppraisalsService,
    private _AppraisalEvaluatorsService: AppraisalEvaluatorsService,
    private _WorkflowService: WorkflowService,
    private _util: UtilityService,
    private _GraphApiService: GraphApiService,
    public dialogRef: MatDialogRef<AssignLearningGoalsComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private _ErrorService: ErrorService
  ) {
    
    this.employeeId = this.data.employee_oid;
    this.moduleId = this.data.module_id;
    this.appraisalYear = this.data.appraisal_year;
  
console.log(this.moduleId,this.appraisalYear)

  }

  assignLearningGoalsForm = this.fb.group({
    appraisalCycleName: ['', Validators.required],
    appraisalModules: this.fb.array([this.employeeAppraisalModules]),
  });

  async ngOnInit(){
    await this.getAppraisalConfigs();
    this.getAppraisalCyclesAll();
    this.getAppraisalMetricesAll();  
    
  }

  /**
   * @description employee  form group
   */
  get employeeAppraisalModules(): FormGroup {
    return this.fb.group({
      employeeAppraisalModuleId: (this.appraisalConfigs && this.appraisalConfigs.employeeAppraisalModuleId) ? this.appraisalConfigs.employeeAppraisalModuleId : '',
      employeeAppraisalModuleWeightage: 100,
      employeeAppraisalModuleScoreObtained: 0,
      isPreRequisiteSatisified: false,
      isPreRequisiteRequired:false,
      appraisalCycleId: '',
      appraisalCycleWeightage: 100,
      employeeAppraisalCycleScored: 0,
      employeeAppraisalModuleGroups: this.fb.array([
        this.employeeAppraisalModuleGroups,
      ]),
    });
  }

  /**
   * @description employee module form group
   */
  get employeeAppraisalModuleGroups(): FormGroup {
    return this.fb.group({
      isGrouped: false,
      employeeAppraisalModuleGroupName:  (this.appraisalConfigs && this.appraisalConfigs.employeeAppraisalModuleGroupName) ? this.appraisalConfigs.employeeAppraisalModuleGroupName : '',
      employeeAppraisalModuleGroupId: '',
      employeeAppraisalModuleGroupWeightage: 100,
      employeeAppraisalModuleGroupScoreObtained: 0,
      employeeAppraisalMetrices: this.fb.array([
        this.employeeAppraisalMetrices,
      ]),
    });
  }

  /**
   * @description employee  form metrices group
   */
  get employeeAppraisalMetrices(): FormGroup {
    return this.fb.group({
      employeeAppraisalMetricesId: ['', Validators.required],
      employeeAppraisalMetricesWeightage: 100,
      employeeAppraisalMetricesScoreObtained: 0,
      managerEvaluationWeightage: 100,
      customerEvaluationWeightage: 0,
      peerEvaluationWeightage: 0,
    });
  }

  /**
   * @description add employee metrices form group
   */
  addMetrices(group) {
    (group.get('employeeAppraisalMetrices') as FormArray).push(
      this.employeeAppraisalMetrices
    );
  }

  /**
   * @description remove employee metrices form group
   */
  deleteMetrices(group, index) {
    (group.get('employeeAppraisalMetrices') as FormArray).removeAt(index);
  }

  /**
   * @description close dialog
   */
  closeDialog(msg) {
    if(msg=='Success'){
      let res = {
        preRequisite : true
      }
      this.dialogRef.close(res);
    }
    else{
    this.dialogRef.close(msg);
    }
  }

  /**
   * @description get all appraisal cycle
   */
  getAppraisalCyclesAll() {
    this._AppraisalCycleService.getAppraisalCycleAll().subscribe(
      (result: any) => {
        if (result.error == 'N') {
          console.log(result.data);
          this.appraisalCycleList = result.data.map((x) => {
            return {
              id: x._id,
              name: x.appraisal_cycle_name,
            };
          });
        }
      },
      (error) => {
        console.log(error);
      }
    );
  }

  /**
   * @description get all appraisal metrices
   */
  getAppraisalMetricesAll() {
    this._AppraisalMetricesService.getAppraisalMetricesAll().subscribe(
      (result: any) => {
        if (result.error == 'N') {
          console.log(result);
          this.appraisalMetricesData = result.data;
          this.appraisalMetricesArr = this.appraisalMetricesData.map((x) => {
            return {
              id: x._id,
              name: x.appraisal_metric_name + ' - Credit Points ->' + x.appraisal_metric_max_score,
            };
          });
        }
      },
      (error) => {
        console.log(error);
      }
    );
  }

  /**
   * @description create appraisal cycle
   */
  async createCycle() {
    console.log('final', this.assignLearningGoalsForm.value);
    console.log('final', this.employeeId);

    this.employeeId.forEach(async (oid) => {
      this.appraisalCycleEmployeesMapped.push({
        employee_oid: oid,
        employee_email_id: '',
        customer_details: [
          {
            customer_email_id: '',
            customer_review_url: '',
          },
        ],
      });
    });
    console.log(this.appraisalCycleEmployeesMapped, 'selectedEmployees');
    this.appraisalCycleId = this.appraisalCycleName.value;
    let response = {
      employees_list: this.employeeId,
      appraisal_cycle_id: this.appraisalCycleId,
      appraisal_module_list:
        this.assignLearningGoalsForm.get('appraisalModules').value,
      appraisal_year: Number(this.appraisalConfigs.appraisal_year),
      appraisal_max_score: this.appraisalConfigs.max_score,
      scoringWeightageType: this.appraisalConfigs.scoringWeightageType ,
      scoringWeightageCustomType: this.appraisalConfigs.scoringWeightageCustomType,
    };
    console.log('response', response);

    this._AppraisalCycleService
      .addEmployeeToAppraisalCycle(
        this.appraisalCycleId,
        this.appraisalCycleEmployeesMapped
      ).pipe(takeUntil(this._onDestroy)).subscribe(
        (result: any) => {
          if (result.error == 'N') {
            let response = {
              employees_list: this.employeeId,
              appraisal_cycle_id: this.appraisalCycleId,
              appraisal_module_list:
                this.assignLearningGoalsForm.get('appraisalModules').value,
              appraisal_year: this.appraisalConfigs.appraisal_year,
              appraisal_max_score: this.appraisalConfigs.max_score,
              scoringWeightageType: this.appraisalConfigs.scoringWeightageCustomType,
              scoringWeightageCustomType: this.appraisalConfigs.scoringWeightageType,
            };
    
            this._EmployeeAppraisalsService.createEmployeeAppraisalCycle(response).pipe(takeUntil(this._onDestroy)).subscribe(
                (result) => {
                  this._util.showMessage(
                    'Certificates Assigned Successfully',
                    'Dismiss',
                    5000
                  );
                  this.updisPreRequisiteSatisifiedFlag();
                  this.appraisalCycleEmployeesMapped = [];
                  console.log(result);
                  this.isLoading =false
                },
                (error) => {
                  this.isLoading =false
                  this._util.showMessage(
                    'Error Assigning Certificates',
                    'Dismiss'
                  );
    
                }
              );
          } else {
            this.isLoading =false
            this._util.showMessage(
              'Error Assigning Certificates',
              'Dismiss'
            );
    
          }
        },
        (error) => {
          this.isLoading =false
          this._util.showMessage(
            'Error launching Appraisal Cycle Creation Job',
            'Dismiss'
          );
    
        }
      );
  }

  /**
   * @description ngOnDestroy
   */
   ngOnDestroy() {
    this._onDestroy.next();
    this._onDestroy.complete();
  }

  /**
   * @description get appraisal configs
   */
  getAppraisalConfigs(){
     let req = {
      "configuration_name":"LDA_integration_config"
     }
     return new Promise((resolve, reject) => {
     this._AppraisalCycleService.getAppraisalConfigs(req).pipe(takeUntil(this._onDestroy)).subscribe( (res) => {
     if(res['data'].length>0){
      this.appraisalConfigs = res['data'][0]['configuration_data'];
      this.updateModuleConfigs();
      resolve(this.appraisalConfigs);
     }
     else{
       this._util.showMessage('No data Found','Dismiss');
       resolve('');
     }
    } , (err) => {
      this._ErrorService.userErrorAlert(
        err.error.code,
        'Some Error Happened in completing the Activity',
        err.error.errMessage
      );
      reject(err);
    });
  });
    
  }

  /**
   * @description update pre requisite flag in appraisal cycle
   */
   updisPreRequisiteSatisifiedFlag(){
    let req = {
      "employee_oid":this.employeeId[0],

      "appraisal_year":this.appraisalYear,
  
      "module_id":this.moduleId,
  
      "isPreRequisiteSatisified":true
    }
   this._AppraisalCycleService.updisPreRequisiteSatisifiedFlag(req).pipe(takeUntil(this._onDestroy)).subscribe( (res) => {
    console.log('Updated Successfully');
    this.closeDialog('Success')
   } , (err) => {
     this._ErrorService.userErrorAlert(
       err.error.code,
       'Some Error Happened in completing the Activity',
       err.error.errMessage
     );
     this.closeDialog('')
   });
   
 }

 /**
   * @description update pre requisite flag in appraisal cycle
   */
 updateModuleConfigs(){
 // const controlArray = <FormArray> this.assignLearningGoalsForm.get('appraisalModules') as FormArray;
  (this.assignLearningGoalsForm.get('appraisalModules') as FormArray).at(0).get('employeeAppraisalModuleId').patchValue(this.appraisalConfigs.employeeAppraisalModuleId);
  ((this.assignLearningGoalsForm.get('appraisalModules') as FormArray).at(0).get('employeeAppraisalModuleGroups')as FormArray).at(0).get('employeeAppraisalModuleGroupName').patchValue(this.appraisalConfigs.employeeAppraisalModuleGroupName);
 }

}
