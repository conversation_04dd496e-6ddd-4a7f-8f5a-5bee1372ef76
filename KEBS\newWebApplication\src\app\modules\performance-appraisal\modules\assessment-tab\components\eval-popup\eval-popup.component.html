<div class="row mt-3 pt-2" style="background-color: #eeeeee">
  <div class="col-7 overflow-ctrl">
    <h2 class="overflow-ctrl">
      Evaluate {{ data.cycleName }} For
      <app-user-profile
        type="name"
        [oid]="data.employeename"
      ></app-user-profile>
    </h2>

    <span *ngIf="templateName=='rating_only_full_row' || templateName=='comments_with_response'">
      <span style="color: red;font-weight:500;font-size: 12px;">
        Appraiser Rating {{ evalRatingStartDate | date: "dd/MM/yyyy" }} To {{
          evalRatingEndDate | date: "dd/MM/yyyy" }}
      </span>
      <span class="pl-2" style="color: red;font-weight:500;font-size: 12px;" *ngIf="allowEvalRating==false">
        {{cycleBlockMsg}}
      </span>
    </span>
  </div>
   <!-- <div class="col-4 d-flex" *ngIf="templateName == 'rating_only' || templateName == 'organizational_activities'">
    <div matTooltip = "Save as Draft">
    <button
      mat-raised-button
      (click)="save()"
      disabled="false"
      [ngClass]="'not-checked'"
    >
      Save As Draft
    </button>
  </div>
  <div  matTooltip = "Submit">
    <button
      class="ml-2"
      mat-raised-button
      (click)="submit()"
      disabled="false"
      [ngClass]="'not-checked'"
    >
      submit
    </button>
  </div>
  <div>
    <mat-slide-toggle *ngIf="templateName != 'skill_set'" style="padding-top:6px;padding-left: 12px;" [matTooltip]="checked?'Go to my Evaluation Screen':'View Other\'s Evaluation'" (change)="onChange($event)">
  
      </mat-slide-toggle>
    </div>
  </div> 
  <div class="col-4 d-flex" *ngIf="templateName != 'rating_only' && templateName != 'organizational_activities'">
    <div [matTooltip] = "(templateName == 'skill_set' && LDAchecked)? 'Please Select a Course to Save' : checked ? 'You Cannot Save in View mode' : 'Save as Draft'">
    <button
      mat-raised-button
      (click)="save()"
      [disabled]="checked || (templateName == 'skill_set' && LDAchecked)"
      [ngClass]="checked || (templateName == 'skill_set' && LDAchecked) ? 'checked' : 'not-checked'"
    >
      Save As Draft
    </button>
  </div>
  <div  [matTooltip] = "(templateName == 'skill_set' && LDAchecked)? 'Please Select a Course to Submit' : checked ? 'You Cannot Submit in View mode' : 'Submit'">
    <button
      class="ml-2"
      mat-raised-button
      (click)="submit()"
      [disabled]="checked  || (templateName == 'skill_set' && LDAchecked)"
      [ngClass]="checked  || (templateName == 'skill_set' && LDAchecked) ? 'checked' : 'not-checked'"
    >
      submit
    </button>
  </div>
  <div>
    <mat-slide-toggle *ngIf="templateName != 'skill_set'" style="padding-top:6px;padding-left: 12px;" [matTooltip]="checked?'Go to my Evaluation Screen':'View Other\'s Evaluation'" (change)="onChange($event)">
  
      </mat-slide-toggle>
    </div>
  </div> -->

  <div class="col-4 d-flex justify-content-end">
    <div
      [matTooltip]="(templateName == 'skill_set' && LDAchecked)? 'Please Select a Course to Save' : (checked && (templateName != 'rating_only' && templateName != 'organizational_activities')) ? 'You Cannot Save in View mode' : 'Save as Draft'"
      *ngIf="templateName!='rating_only_full_row'">
      <button mat-raised-button (click)="save()"
        [disabled]="(checked && (templateName != 'rating_only' && templateName != 'organizational_activities')) || (templateName == 'skill_set' && LDAchecked) || acknowledgeStatus || !allowEvalRating"
        [ngClass]="(checked && (templateName != 'rating_only' && templateName != 'organizational_activities')) || (templateName == 'skill_set' && LDAchecked) ? 'checked' : 'not-checked'">
        Save As Draft
      </button>
    </div>
    <div
      [matTooltip]="(templateName == 'skill_set' && LDAchecked)? 'Please Select a Course to Submit' : (checked && (templateName != 'rating_only' && templateName!='rating_only_full_row' && templateName != 'organizational_activities')) ? 'You Cannot Submit in View mode' : 'Submit'">
      <button class="ml-2" mat-raised-button (click)="submit()"
        [disabled]="(checked && (templateName != 'rating_only' && templateName != 'organizational_activities' && templateName != 'rating_only_full_row'))  || (templateName == 'skill_set' && LDAchecked) || acknowledgeStatus || (templateName=='rating_only_full_row' && currentUserOid==l2EvalOid && !isLevel2ApprovalNeeded) || !allowEvalRating"
        [ngClass]="(checked && (templateName != 'rating_only' && templateName != 'organizational_activities' && templateName != 'rating_only_full_row')) || (templateName == 'skill_set' && LDAchecked) ? 'checked' : 'not-checked'">
        Submit
      </button>
    </div>
    <div>
      <mat-slide-toggle
        *ngIf="templateName != 'skill_set' && templateName != 'rating_only' && templateName != 'organizational_activities' && templateName != 'rating_only_full_row' && templateName != 'comments_with_response'"
        style="padding-top:6px;padding-left: 12px;"
        [matTooltip]="checked?'Go to my Evaluation Screen':'View Other\'s Evaluation'" (change)="onChange($event)">
  
      </mat-slide-toggle>
    </div>
  </div>

  <div class="col-1">
    <button mat-icon-button (click)="closeDialog('')">
      <mat-icon>close</mat-icon>
    </button>
  </div>
</div>

<div style="overflow: auto" *ngIf="!checked" class="mt-3">
  <div *ngIf="templateName == 'rating_and_feedback'">
    <!-- <div class = "row">
        <div class="col-12 d-flex">
            <div class="col-6">
              <div class="row">
                  <div *ngFor="let item of rating;let i = index" class="col-12">
                    <app-type-star
    
                    [metricName]="item?.appraisal_metrices_details?.appraisal_metric_name"
                    [readOnly]="
                    item?.employee_appraisal_metrices_evaluation_status.toLowerCase() ==
            'submitted' ||
            item?.employee_appraisal_metrices_evaluation_status.toLowerCase() ==
            'open' ||
            item?.employee_appraisal_metrices_evaluation_status.toLowerCase() ==
            'approved'
            ? false
            : true
        "
        [totalScore]="
        item?.employee_appraisal_metrices_evaluation_operation ==
          'OR'
            ? item?.employee_appraisal_metrices_scored_obtained
            : currentEvaluatorScore
        "
                    [totalStars]="item?.appraisal_metrices_details?.appraisal_metric_max_score"
                    (starsResponse)="getStarRating($event,i,item?.appraisal_metrices_id)"
                  ></app-type-star>
                  </div>
              </div>
            </div>
            <div class="col-6">
                <div class="row">
                    <div *ngFor="let item of feedback;let i = index" class="col-12">
                        <app-type-comment
                        [readOnly]="
                        item?.employee_appraisal_metrices_evaluation_status.toLowerCase() ==
                          'submitted' ||
                          item?.employee_appraisal_metrices_evaluation_status.toLowerCase() ==
                          'open'
                          ? false
                          : true
                      "
                      [label]="'Your Feedback'"
                      [comment]=" currentEvaluatorComment"
                (commentResponse)="getComment($event,i,item?.appraisal_metrices_id)"
              ></app-type-comment>
                    </div>
                </div> 
            </div>
        </div>
    </div> -->
    <!-- <div *ngIf="evalPopupDetails.appraisal_metrices_details.appraisal_metric_response_type"></div> -->
    <div class="row">
      <div class="col-12 d-flex">
        <div class="col-6">
          <div class="row">
            <div *ngFor="let item of rating; let i = index" class="col-12">
              <app-rating-feedback
                [evaluatorId]="evaluatorId"
                [employeeId]="employeeId"
                [evalDetail]="item"
                mode = "evaluate"
                [readOnly] = "!allowEdit"
                (evaluatorResponse)="getChanges($event)"
                [halfstar]="halfstar"
              ></app-rating-feedback>
            </div>
          </div>
        </div>
        <div class="col-6">
          <div class="row">
            <div *ngFor="let item of feedback; let i = index" class="col-12">
              <app-rating-feedback
                [evaluatorId]="evaluatorId"
                [employeeId]="employeeId"
                [evalDetail]="item"
                mode = "evaluate"
                [readOnly] = "!allowEdit"
                (evaluatorResponse)="getChanges($event)"
                [halfstar]="halfstar"
              ></app-rating-feedback>
            </div>
          </div>
          <div class="row pt-3">
            <div class="col-12 d-flex" *ngIf="isPreRequisiteRequired && evalLevel == 1">
              <div class="col-6"></div>
              <div class="col-3" style="text-align:right">
                <button mat-raised-button class="not-checked"
                (click) ="ViewEmployeeProfile()">
                View Profile</button>
              </div>
              <div class="col-3 p-0" style="text-align:right">
                <button mat-raised-button class="not-checked"
                (click) ="createEmployeeLearningGoals()">
                Assign Learning Goals</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div *ngIf="templateName == 'rating_only' ||templateName == 'organizational_activities' ">
    <div class="row" style="padding-top: 1rem">
      <div class="col-12">
        <div class="row">
          <div
            *ngFor="let item of evalPopupDetails; let i = index"
            class="col-12 pb-2"
          >
            <!-- <app-type-star
    
                    [metricName]="item?.appraisal_metrices_details?.appraisal_metric_name"
                    [readOnly]="
                    item?.employee_appraisal_metrices_evaluation_status.toLowerCase() ==
            'submitted' ||
            item?.employee_appraisal_metrices_evaluation_status.toLowerCase() ==
            'open' ||
            item?.employee_appraisal_metrices_evaluation_status.toLowerCase() ==
            'approved'
            ? false
            : true
        "
        [totalScore]="
        item?.employee_appraisal_metrices_evaluation_operation ==
          'OR'
            ? item?.employee_appraisal_metrices_scored_obtained
            : currentEvaluatorScore
        "
                    [totalStars]="item?.appraisal_metrices_details?.appraisal_metric_max_score"
                    (starsResponse)="getStarRating($event,i,item?.appraisal_metrices_id)"
                  ></app-type-star> -->
            <app-rating-only
              [evaluatorId]="evaluatorId"
              [employeeId]="employeeId"
              [evalDetail]="item"
              [groupTotalScore] = "groupTotalScoreData"
              mode = "evaluate"
              [readOnly] = "!allowEdit"
              (evaluatorResponse)="getChanges($event)"
            ></app-rating-only>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div *ngIf="templateName == 'feedback_only'">
    <div class="row">
      <div class="col-12">
        <div class="row">
          <div
            *ngFor="let item of evalPopupDetails; let i = index"
            class="col-12">
            <app-feedback-only
              [evaluatorId]="evaluatorId"
              [employeeId]="employeeId"
              [evalDetail]="item"
              mode = "evaluate"
              [readOnly] = "!allowEdit"
              (evaluatorResponse)="getChanges($event)"
            ></app-feedback-only>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div *ngIf="templateName == 'skill_set'">
    <div class="row">
      <div class="col-12">
        <div class="row">
          <div
            *ngFor="let item of evalPopupDetails; let i = index"
            class="col-12"
          >
            <app-scoring
              [evaluatorId]="evaluatorId"
              [employeeId]="employeeId"
              [evalDetail]="item"
              [index] = "i"
              (evaluatorResponse)="getLDAChanges($event)"
            ></app-scoring>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div *ngIf="templateName == 'comments_with_response'">
    <div class="row">
      <div class="col-12">
        <div class="row">
          <div
            *ngFor="let item of evalPopupDetails; let i = index"
            class="col-12"
          >
            <comment-with-response
              [evaluatorId]="evaluatorId"
              [employeeId]="employeeId"
              [evalDetail]="item"
              [index]="i"
              [attachmentBucket]="attachmentBucket"
              mode = "evaluate"
              [readOnly] = "!allowEdit || !allowEvalRating || acknowledgeStatus"
              (evaluatorResponse)="getChanges($event)"
            ></comment-with-response>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div *ngIf="templateName == 'organizational_values'">
    <div class="row">
      <div class="col-12">
        <div class="row">
          <div
            *ngFor="let item of evalPopupDetails; let i = index"
            class="col-12"
          >
            <comment-and-rating-with-response
              [evaluatorId]="evaluatorId"
              [employeeId]="employeeId"
              [evalDetail]="item"
              [index]="i" 
              mode = "evaluate"
              [readOnly] = "!allowEdit"
              (evaluatorResponse)="getChanges($event)"
            ></comment-and-rating-with-response>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- View Only Mode -->


<div style = "overflow-y:auto;overflow-x: hidden;" *ngIf="checked" class="mt-3">
  <mat-spinner *ngIf="dataLoading && (templateName != 'rating_only' && templateName != 'organizational_activities')" class="spinner-align" diameter="25"></mat-spinner>
  <div *ngIf="templateName == 'rating_and_feedback' && !dataLoading && viewData.length>0">
    <!-- <p>carousel</p> -->
    <mat-carousel
    [autoplay]="false"
    color ="warn"
    maxWidth="auto"
    proportion="40"
    [loop]="false"
    [hideArrows]="false"
    [hideIndicators]="true"
    [useKeyboard]="true"
    [useMouseWheel]="false"
    orientation="ltr"
  >
  <mat-carousel-slide
      #matCarouselSlide
      *ngFor="let slide of viewData; let i = index"
      [hideOverlay]="true"
    >
    <!-- <p>i</p> -->
    <div style="overflow-y: scroll;height: 80vh;">
    <div class="row mt-4">
      <div class="ml-5  col-11 d-flex">
        <span style="font-size: 15px;font-weight: 500;margin-top:2px;">Evaluator Name : </span>
        <span class="ml-3">
          <app-user-image style="margin: 2px" [id]="slide._id"
                  
                      imgHeight="28px" imgWidth="28px" borderStyle="solid" borderWidth="2px" [borderColor]="'gray'">
                    </app-user-image>
        </span>
        <span class="ml-2 mt-1">
          <app-user-profile
                    [type]="'name'"
                    [oid]="slide._id"
                    style="font-size: 14px;"
                  ></app-user-profile>
        </span>
      </div>
    </div>
    <div class="row  mt-2" >
      <div class="ml-5 col-11 d-flex">
        <div class="col-6">
          <div class="row">
            
            <div *ngFor="let item of slide.rating; let i = index" class="col-12">
              <app-rating-feedback
                [evaluatorId]="evaluatorId"
                [employeeId]="employeeId"
                [evalDetail]="item"
                 readOnly = true
                 mode = "view"
                [halfstar]="halfstar"
              ></app-rating-feedback>
            </div>
          
          </div>
        </div>
        <div class="col-6">
          <div class="row">
            
            <div *ngFor="let item of slide.feedback; let i = index" class="col-12">
              <app-rating-feedback
                [evaluatorId]="evaluatorId"
                [employeeId]="employeeId"
                [evalDetail]="item"
                readOnly = true
                mode = "view"
                [halfstar]="halfstar"
              ></app-rating-feedback>
            </div>
          
          </div>
        </div>
       

      </div>
    </div>
    
  </div>
  </mat-carousel-slide>
</mat-carousel>
    
  </div>

<div class="p-0 col-12 d-flex" *ngIf="templateName == 'rating_only' ||templateName == 'organizational_activities'">
  <mat-spinner *ngIf="dataLoading" class="spinner-align" diameter="25"></mat-spinner>
  <div class="p-0 col-6" *ngIf="!dataLoading">
    <div class="row pt-3" style="padding-left: 38px;">
      <span style="font-size: 15px;font-weight: 500;">Your Rating:</span>
    </div>
    <div class="row" style="padding-top: 1rem">
      <div class="col-12" style="height: 77vh;overflow-y: auto;max-width: 99%;">
        <div class="row">
          <div *ngFor="let item of evalPopupDetails; let i = index" class="p-0 col-12 pb-2">
            <app-rating-only [evaluatorId]="evaluatorId" [employeeId]="employeeId" [evalDetail]="item"
              [groupTotalScore]="groupTotalScoreData" mode="evaluate" (evaluatorResponse)="getChanges($event)">
            </app-rating-only>
          </div>
        </div>
      </div>
    </div>
  </div>
  <mat-divider style="border-right-width:2px;border-right-color: red;height: 82vh;" vertical></mat-divider>
  <div class="p-0 col-6" *ngIf="!dataLoading">
    <!-- <mat-spinner *ngIf="dataLoading" class="spinner-align" diameter="25"></mat-spinner> -->
    <!-- <div class="row pl-5 pt-3">
    <span style="font-size: 15px;font-weight: 500;">Other's Rating:</span>
  </div> -->
    <div class="p-0 col-12" *ngIf="!dataLoading && viewData.length>0 ">
      <mat-carousel [autoplay]="false" color="warn" maxWidth="auto" proportion="40" [loop]="false" [hideArrows]="false"
        [hideIndicators]="true" [useKeyboard]="true" [useMouseWheel]="false" orientation="ltr">

        <mat-carousel-slide #matCarouselSlide *ngFor="let slide of viewData; let i = index" [hideOverlay]="true">
          <!-- <p>i</p> -->
          <div style="overflow-y: auto;height: 78vh;max-width: 99%;">
            <div class="row">
              <div class="ml-4 mt-3 col-11 d-flex">
                <!-- <span style="font-size: 15px;font-weight: 500;margin-top:2px;">Evaluator Name : </span> -->
                <span class="ml-1">
                  <app-user-image style="margin: 2px" [id]="slide._id" imgHeight="28px" imgWidth="28px"
                    borderStyle="solid" borderWidth="2px" [borderColor]="'gray'">
                  </app-user-image>
                </span>
                <span class="ml-1 mt-1">
                  <app-user-profile [type]="'name'" [oid]="slide._id" style="font-size: 14px;font-weight: 500;">
                  </app-user-profile>
                </span>
                <span class="ml-3 mt-1">
                  <app-user-profile [type]="'designation'" [oid]="slide._id" style="font-size: 14px;font-weight: 500;">
                  </app-user-profile>
                </span>
                <span class="ml-5 mt-1" style="font-size: 15px;font-weight: 500;">
                  Total Score Awarded :
                </span>
                <span class="ml-2 mt-1">
                  {{slide.displayEvalTotalScore ? slide.individual_evl_score : '0'}}
                </span>
              </div>
            </div>

            <div class="row" style="padding-top: 10px">
              <div class="ml-4 pl-0 col-11">
                <div class="row">
                  <div *ngFor="let item of slide.eval_data; let i = index" class="pl-0 col-12 pb-2">
                    <app-rating-only [evaluatorId]="evaluatorId" [employeeId]="employeeId" [evalDetail]="item"
                      [groupTotalScore]="groupTotalScoreData" readOnly=true mode="view"></app-rating-only>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </mat-carousel-slide>
      </mat-carousel>
    </div>

    <div *ngIf="viewData.length == 0 && !dataLoading" style="text-align: center;">
      <div>
        <h4 class="d-flex justify-content-center align-items-center mt-3 mb-0">
          Oops ! No Data Found !
        </h4>
      </div>
      <div class="d-flex justify-content-center align-items-center">
        <img src="https://assets.kebs.app/images/nomilestone.png" class="mt-2" height="220" width="250" />
      </div>
    </div>

  </div>

</div>

<!-- ***************************************************************** -->

<div class="p-0 col-12 d-flex" *ngIf="templateName == 'rating_only_full_row'"> 
 
<div class="p-0 col-12" *ngIf="!dataLoading">

  <div class="row mt-3" *ngIf="appraisaloduleDetails?.display_appraiser_comments">
    <div class="col-xl-4 col-lg-4 col-md-4 col-sm-4 col-4 d-flex justify-content-center align-items-center">
      <div  class="card shadow rounded d-flex justify-content-center p-2" style="height: 5rem;width: 15rem; border-radius:3px;background-color: ghostwhite;font-weight: 500;">
        <span class="overflow-ctrl">
          Employee Name  : <app-user-profile
          type="name"
          [oid]="data.employeename"
        ></app-user-profile>     
        </span>
        <span class="overflow-ctrl">
          Employee Score : {{ selfTotalScore | number: "1.0-0" }}
        </span>    
      </div>
    </div>

    <div class="col-xl-4 col-lg-4 col-md-4 col-sm-4 col-4 d-flex justify-content-center align-items-center">
      <div class="d-flex flex-column align-items-center justify-content-center p-2 shadow"
        style="height: auto;width: 15rem; border-radius:12px;background-color: ghostwhite;font-weight: 500;border: 1px solid #249fdc;">
    
        <h2 class="overflow-ctrl mb-3">
          Overall Score : {{ l1EvalTotalScore | number: "1.0-0" }}
        </h2>
    
    
        <div *ngIf="groupTotalScoreDataLength>0">
         
    
            <table style="border: none !important;table-layout: fixed;width: 100%;">
              <!-- <tr>
                <th class="overflow-ctrl">GroupName</th>
                <th class="overflow-ctrl">Score/Weightage</th>
              </tr> -->
              <tr *ngFor="let group of groupTotalScoreData" [matTooltip]="group?.group_scores?.appraisal_group_name">
                <td class="overflow-ctrl" [ngStyle]="{'width': group?.group_scores?.appraisal_group_weightage!=0 ? '70%' : '100%'}">{{group?.group_scores?.appraisal_group_name}}</td>
                <td class="overflow-ctrl pl-2" *ngIf="group?.group_scores?.appraisal_group_weightage!=0">{{ group?.group_scores?.appraisal_group_score | number: "1.0-0"
                  }}/{{group?.group_scores?.appraisal_group_weightage}}</td>
              </tr>
            </table>
              
        </div>
    
    
      </div>
    </div>


    <div class="col-xl-4 col-lg-4 col-md-4 col-sm-4 col-4 d-flex justify-content-center align-items-center">
      <div  class="card shadow rounded  d-flex justify-content-center p-2" style="height: 5rem;width: 15rem; border-radius:3px;background-color: ghostwhite;font-weight: 500;">
        <span class="overflow-ctrl">
          Evaluator Name :<app-user-profile
          type="name"
          [oid]="l1EvalOid"
        ></app-user-profile>         
        </span>
        <span  class="overflow-ctrl">
          Evaluator Score: {{ l1EvalTotalScore | number: "1.0-0" }}
        </span>
      </div>
    </div>
  </div>
  
  <div *ngFor="let item of evalPopupDetails; let i = index" class="col-12 mt-4">
    <div *ngIf="i==0">
      <div class="mx-1 my-2">
        <div class="row">
          <div class="d-flex align-items-center" [ngClass]="isLevel2ApprovalNeeded && isLevel2Approver ? 'col-2' : 'col-3'">
            <span class="module-group-name">
              Competencies Details
            </span>
          </div>
          <div class="d-flex align-items-center" [ngClass]="isSelfEvalRequired && isLevel2ApprovalNeeded && isLevel2Approver ? 'col-1 p-0' : !isSelfEvalRequired && isLevel2ApprovalNeeded && isLevel2Approver ?   'col-1 p-0' :  isSelfEvalRequired ? 'col-2 p-0' : 'col-3 px-5 py-0'">
            <span class="module-group-name">
              Measure
            </span>
          </div>
          <div class="d-flex justify-content-center align-items-center" *ngIf="isSelfEvalRequired" [ngClass]="isSelfEvalRequired ?  'col-2' : ''">           
            <span class="module-group-name">
              Employee Score
            </span>
          </div>
          <div class="d-flex justify-content-center align-items-center" *ngIf="isSelfEvalRequired" [ngClass]="isSelfEvalRequired ?  'col-2' : ''">           
            <span class="module-group-name">
              L1 Reviewer Score
            </span>
          </div>
          <div class="d-flex justify-content-center align-items-center" [ngClass]="isSelfEvalRequired ?  'col-2' : ''" *ngIf="isLevel2ApprovalNeeded && isLevel2Approver">            
            <span class="module-group-name">
              L2 Reviewer Score
            </span>
          </div>
          <div class="col-2 d-flex justify-content-center align-items-center">            
            <span class="module-group-name">
              Status
            </span>
          </div>
          <div class="col-1 d-flex justify-content-center align-items-center">            
            <span class="module-group-name">
              Remarks
            </span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- <div *ngIf="item.appraisal_metrices_details.appraisal_metric_evaluation_type == 'rating'" class="mb-3"> -->
      <app-rating-only-full-row [evaluatorId]="evaluatorId" [employeeId]="employeeId" [evalDetail]="item" [toolTipValueForStarRating]="toolTipValueForStarRating"
      [groupTotalScore]="groupTotalScoreData" [allowEdit]="allowEdit" [allowEvalRating]="allowEvalRating" [isSelfEvalRequired]="isSelfEvalRequired" [currMetricesIndex]="i" [acknowledgeStatus]="acknowledgeStatus" [attachmentBucket]="attachmentBucket" mode="evaluate" [selfEvlMetricesData]="selfEvalDetail" [allowedManagerToEditEvaluation]="allowedManagerToEditEvaluation" [l2EvalApprovedAll]="l2EvalApprovedAll" [appraisaloduleDetails]="appraisaloduleDetails" [isSaveLoading]="isSaveLoading && currMetricesIndex==i" (evaluatorResponse)="saveRatingChanges($event)" [isLevel2ApprovalNeeded]="isLevel2ApprovalNeeded" [isLevel2Approver]="isLevel2Approver" [l2EvalPopupDetails]="l2EvalPopupDetails">
      </app-rating-only-full-row>
    <!-- </div> -->
    
    <!-- <div *ngIf="item.appraisal_metrices_details.appraisal_metric_evaluation_type == 'feedback_only'">
        <app-feedback-only
        [evaluatorId]="evaluatorId"
        [employeeid]="employeeId"
        [evalDetail]="item"
        [readOnly] = "!allowEdit || acknowledgeStatus"
        mode = "evaluate"
        (evaluatorResponse)="saveFeedBack($event)"
      ></app-feedback-only> 
    </div>-->
    
  </div>

  
  <!--  -->
  <div *ngIf="appraisaloduleDetails?.display_appraiser_comments">
    <div class="promotion_details p-4" *ngIf="( (templateName=='rating_only_full_row' && currentUserOid==l2EvalOid) || acknowledgeStatus || !allowEvalRating );else content_edit">
      <p class="module-group-name">Appraiser Comments</p>
      <div class="row pt-1 px-0">
        <div class="col-12 px-0">
          <mat-accordion class="listcard">
            <mat-expansion-panel style="background-color: #ffffff;border-left: 4px solid #b999f9;">
              <mat-expansion-panel-header>
                <mat-panel-title style="font-weight: 500;color: #5cb6dc;"> Overall Feedback </mat-panel-title>
              </mat-expansion-panel-header>
              
              <p *ngIf="
                    overallFeedback &&
                    overallFeedback != ''
                    " [innerHTML]="overallFeedback">
              </p>
              <p *ngIf="
                      !overallFeedback ||
                      overallFeedback == ''
                    " class="font-weight: 500">
                No Feedback given by Appraiser
              </p>
            </mat-expansion-panel>
          </mat-accordion>
        </div>
      </div>
      <div class="row pt-1 px-0" *ngIf="isMeetingScheduled">
        <div class="col-12 px-0">
          <mat-accordion class="listcard">
            <mat-expansion-panel style="background-color: #ffffff;border-left: 4px solid #b999f9;">
              <mat-expansion-panel-header>
                <mat-panel-title style="font-weight: 500;color: #5cb6dc;"> Meeting Notes </mat-panel-title>           
              </mat-expansion-panel-header>
              
              <p *ngIf="
                    meetingNotes &&
                    meetingNotes != ''
                    " [innerHTML]="meetingNotes">
                
              </p>
              <p *ngIf="
                      !meetingNotes ||
                      meetingNotes == ''
                    " class="font-weight: 500">
                No Meeting Notes recorded by Appraiser.
              </p>
            </mat-expansion-panel>
          </mat-accordion>
        </div>
      </div>
      <div class="row pt-1 px-0">
        <div class="col-12 px-0">
          <mat-accordion class="listcard">
            <mat-expansion-panel style="background-color: #ffffff;border-left: 4px solid #b999f9;">
              <mat-expansion-panel-header>
                <mat-panel-title style="font-weight: 500;color: #5cb6dc;"> Promotion Recommendation </mat-panel-title>
              </mat-expansion-panel-header>
              
              <p *ngIf="
                    promotionRecommendation &&
                    promotionRecommendation != ''
                    " [innerHTML]="promotionRecommendation">
                
              </p>
              <p *ngIf="
                      !promotionRecommendation ||
                      promotionRecommendation == ''
                    " class="font-weight: 500">
                No comments from the Appraiser
              </p>
            </mat-expansion-panel>
          </mat-accordion>
        </div>
      </div>
    
    </div>

    <ng-template #content_edit>
      <div class="promotion_details p-4" >
        <p class="module-group-name">Appraiser Comments</p>
        <div class="row pt-1">
          <div class="col-12 px-0">
            <mat-accordion class="listcard">
              <mat-expansion-panel style="background-color: #ffffff;border-left: 4px solid #b999f9;" [expanded]="true">
                <mat-expansion-panel-header>
                  <mat-panel-title style="font-weight: 500;color: #5cb6dc;"> Overall Feedback </mat-panel-title>
                </mat-expansion-panel-header>
                
                <app-html-editor [width]="100" [comment]="overallFeedback" (editorValueChange)="getHtmlEditorValue($event,'overallFeedback')"></app-html-editor>
                
              </mat-expansion-panel>
            </mat-accordion>
          </div>
        </div>
  
        <div class="row pt-1" *ngIf="isMeetingScheduled">
          <div class="col-12 px-0">
            <mat-accordion class="listcard">
              <mat-expansion-panel style="background-color: #ffffff;border-left: 4px solid #b999f9;" [expanded]="true">
                <mat-expansion-panel-header>
                  <mat-panel-title style="font-weight: 500;color: #5cb6dc;"> Meeting Notes </mat-panel-title>           
                </mat-expansion-panel-header>
                
                <app-html-editor [width]="100" [comment]="meetingNotes" (editorValueChange)="getHtmlEditorValue($event,'meetingNotes')"></app-html-editor>
  
              </mat-expansion-panel>
            </mat-accordion>
          </div>
        </div>
        <div class="row pt-1">
          <div class="col-12 px-0">
            <mat-accordion class="listcard">
              <mat-expansion-panel style="background-color: #ffffff;border-left: 4px solid #b999f9;" [expanded]="true">
                <mat-expansion-panel-header>
                  <mat-panel-title style="font-weight: 500;color: #5cb6dc;"> Promotion Recommendation </mat-panel-title>
                </mat-expansion-panel-header>
                
                <app-html-editor [width]="100" [comment]="promotionRecommendation" (editorValueChange)="getHtmlEditorValue($event,'promotionRecommendation')"></app-html-editor>
  
              </mat-expansion-panel>
            </mat-accordion>
          </div>
        </div>
      
      </div>
    </ng-template>
  </div>

  <!--  -->

</div>
  
</div>

<!-- ****************************************************** -->

  <div *ngIf="templateName == 'feedback_only' && !dataLoading && viewData.length>0">
    <mat-carousel
    [autoplay]="false"
    color ="warn"
    maxWidth="auto"
    proportion="40"
    [loop]="false"
    [hideArrows]="false"
    [hideIndicators]="true"
    [useKeyboard]="true"
    [useMouseWheel]="false"
    orientation="ltr"
  >

<mat-carousel-slide
      #matCarouselSlide
      *ngFor="let slide of viewData; let i = index"
      [hideOverlay]="true"
    >
    <!-- <p>i</p> -->
    <div style="overflow-y: scroll;height: 80vh;">
 <div class="row">
      <div class="ml-5 mt-4 col-11 d-flex">
        <span style="font-size: 15px;font-weight: 500;margin-top:2px;">Evaluator Name : </span>
        <span class="ml-3">
          <app-user-image style="margin: 2px" [id]="slide._id"
                  
                      imgHeight="28px" imgWidth="28px" borderStyle="solid" borderWidth="2px" [borderColor]="'gray'">
                    </app-user-image>
        </span>
        <span class="ml-2 mt-1">
          <app-user-profile
                    [type]="'name'"
                    [oid]="slide._id"
                    style="font-size: 14px;"
                  ></app-user-profile>
        </span>
      </div>
    </div>

    <div class="row">
      <div class="ml-5 col-11">
        <div class="row">
          <div
            *ngFor="let item of slide.eval_data; let i = index"
            class="col-12">
            <app-feedback-only
              [evaluatorId]="evaluatorId"
              [employeeId]="employeeId"
              [evalDetail]="item"
              readOnly = true
              mode = "view"
            ></app-feedback-only>
          </div>
        </div>
      </div>
    </div>
  </div>
</mat-carousel-slide>
</mat-carousel>
  </div>

  <div *ngIf="templateName == 'comments_with_response' && !dataLoading && viewData.length>0">
    <mat-carousel
    [autoplay]="false"
    color ="warn"
    maxWidth="auto"
    proportion="40"
    [loop]="false"
    [hideArrows]="false"
    [hideIndicators]="true"
    [useKeyboard]="true"
    [useMouseWheel]="false"
    orientation="ltr"
  >

<mat-carousel-slide
      #matCarouselSlide
      *ngFor="let slide of viewData; let i = index"
      [hideOverlay]="true"
    >
    <!-- <p>i</p> -->
    <div style="overflow-y: scroll;height: 80vh;">
 <div class="row">
      <div class="ml-5 mt-4 col-11 d-flex">
        <span style="font-size: 15px;font-weight: 500;margin-top:2px;">Evaluator Name : </span>
        <span class="ml-3">
          <app-user-image style="margin: 2px" [id]="slide._id"
                  
                      imgHeight="28px" imgWidth="28px" borderStyle="solid" borderWidth="2px" [borderColor]="'gray'">
                    </app-user-image>
        </span>
        <span class="ml-2 mt-1">
          <app-user-profile
                    [type]="'name'"
                    [oid]="slide._id"
                    style="font-size: 14px;"
                  ></app-user-profile>
        </span>
      </div>
    </div>

    <div class="row">
      <div class="ml-5 col-11">
        <div class="row">
          <div
            *ngFor="let item of slide.eval_data; let i = index"
            class="col-12"
          >
            <comment-with-response
              [evaluatorId]="evaluatorId"
              [employeeId]="employeeId"
              [evalDetail]="item"
              [attachmentBucket]="attachmentBucket"
              [readOnly] = "!allowEdit || !allowEvalRating || acknowledgeStatus"
              mode = "view"
              [index]="i"
            ></comment-with-response>
          </div>
        </div>
      </div>
    </div>
  </div>
</mat-carousel-slide>
</mat-carousel>
  </div>

  <div *ngIf="templateName == 'organizational_values' && !dataLoading && viewData.length>0">
    <mat-carousel
    [autoplay]="false"
    color ="warn"
    maxWidth="auto"
    proportion="40"
    [loop]="false"
    [hideArrows]="false"
    [hideIndicators]="true"
    [useKeyboard]="true"
    [useMouseWheel]="false"
    orientation="ltr"
  >

<mat-carousel-slide
      #matCarouselSlide
      *ngFor="let slide of viewData; let i = index"
      [hideOverlay]="true"
    >
    <!-- <p>i</p> -->
    <div style="overflow-y: scroll;height: 80vh;">
 <div class="row">
      <div class="ml-5 mt-4 col-11 d-flex">
        <span style="font-size: 15px;font-weight: 500;margin-top:2px;">Evaluator Name : </span>
        <span class="ml-3">
          <app-user-image style="margin: 2px" [id]="slide._id"
                  
                      imgHeight="28px" imgWidth="28px" borderStyle="solid" borderWidth="2px" [borderColor]="'gray'">
                    </app-user-image>
        </span>
        <span class="ml-2 mt-1">
          <app-user-profile
                    [type]="'name'"
                    [oid]="slide._id"
                    style="font-size: 14px;"
                  ></app-user-profile>
        </span>
      </div>
    </div>

    <div class="row">
      <div class="ml-5 col-11">
        <div class="row">
          <div
            *ngFor="let item of slide.eval_data; let i = index"
            class="col-12"
          >
            <comment-and-rating-with-response
              [evaluatorId]="evaluatorId"
              [employeeId]="employeeId"
              [evalDetail]="item"
              readOnly = true
              mode = "view"
              [index]="i"
            ></comment-and-rating-with-response>
          </div>
        </div>
      </div>
    </div>
  </div>
</mat-carousel-slide>
</mat-carousel>
  </div>

  
  <div *ngIf="viewData.length == 0 && !dataLoading && (templateName != 'rating_only' && templateName != 'organizational_activities')" style="text-align: center;">
    <div>
      <h4 class="d-flex justify-content-center align-items-center mt-3 mb-0">
        Oops ! No Data Found !
      </h4>
    </div>
    <div class="d-flex justify-content-center align-items-center">
      <img src="https://assets.kebs.app/images/nomilestone.png" class="mt-2" height="220" width="250" />
    </div>
  </div>
  
</div>

<!-- <div style = "overflow:auto" *ngIf="checked && (templateName != 'rating_only' && templateName != 'organizational_activities')" class="mt-3">
  <mat-spinner *ngIf="dataLoading" class="spinner-align" diameter="25"></mat-spinner>
  <div *ngIf="templateName == 'rating_and_feedback' && !dataLoading && viewData.length>0">
   
    <mat-carousel
    [autoplay]="false"
    color ="warn"
    maxWidth="auto"
    proportion="40"
    [loop]="false"
    [hideArrows]="false"
    [hideIndicators]="true"
    [useKeyboard]="true"
    [useMouseWheel]="false"
    orientation="ltr"
  >
  <mat-carousel-slide
      #matCarouselSlide
      *ngFor="let slide of viewData; let i = index"
      [hideOverlay]="true"
    >
    
    <div style="overflow-y: scroll;height: 25rem;">
    <div class="row mt-4">
      <div class="ml-5  col-11 d-flex">
        <span style="font-size: 15px;font-weight: 500;margin-top:2px;">Evaluator Name : </span>
        <span class="ml-3">
          <app-user-image style="margin: 2px" [id]="slide._id"
                  
                      imgHeight="28px" imgWidth="28px" borderStyle="solid" borderWidth="2px" [borderColor]="'gray'">
                    </app-user-image>
        </span>
        <span class="ml-2 mt-1">
          <app-user-profile
                    [type]="'name'"
                    [oid]="slide._id"
                    style="font-size: 14px;"
                  ></app-user-profile>
        </span>
      </div>
    </div>
    <div class="row  mt-2" >
      <div class="ml-5 col-11 d-flex">
        <div class="col-6">
          <div class="row">
            
            <div *ngFor="let item of slide.rating; let i = index" class="col-12">
              <app-rating-feedback
                [evaluatorId]="evaluatorId"
                [employeeId]="employeeId"
                [evalDetail]="item"
                 readOnly = true
                 mode = "view"
              ></app-rating-feedback>
            </div>
          
          </div>
        </div>
        <div class="col-6">
          <div class="row">
            
            <div *ngFor="let item of slide.feedback; let i = index" class="col-12">
              <app-rating-feedback
                [evaluatorId]="evaluatorId"
                [employeeId]="employeeId"
                [evalDetail]="item"
                readOnly = true
                mode = "view"
              ></app-rating-feedback>
            </div>
          
          </div>
        </div>
       

      </div>
    </div>
    
  </div>
  </mat-carousel-slide>
</mat-carousel>
    
  </div>

  
<div class="p-0 col-12" *ngIf="(templateName == 'rating_only' ||templateName == 'organizational_activities') && !dataLoading && viewData.length>0 ">
  <mat-carousel
  [autoplay]="false"
  color ="warn"
  maxWidth="auto"
  proportion="40"
  [loop]="false"
  [hideArrows]="false"
  [hideIndicators]="true"
  [useKeyboard]="true"
  [useMouseWheel]="false"
  orientation="ltr"
>

<mat-carousel-slide
    #matCarouselSlide
    *ngFor="let slide of viewData; let i = index"
    [hideOverlay]="true"
  >
  
  <div style="overflow-y: scroll;height: 81vh;">
<div class="row">
    <div class="ml-5 mt-4 col-11 d-flex">
      <span style="font-size: 15px;font-weight: 500;margin-top:2px;">Evaluator Name : </span>
      <span class="ml-3">
        <app-user-image style="margin: 2px" [id]="slide._id"
                
                    imgHeight="28px" imgWidth="28px" borderStyle="solid" borderWidth="2px" [borderColor]="'gray'">
                  </app-user-image>
      </span>
      <span class="ml-2 mt-1">
        <app-user-profile
                  [type]="'name'"
                  [oid]="slide._id"
                  style="font-size: 14px;"
                ></app-user-profile>
      </span>
    </div>
  </div>

  <div class="row" style="padding-top: 1rem">
    <div class="ml-5 col-11">
      <div class="row">
        <div
          *ngFor="let item of slide.eval_data; let i = index"
          class="col-12 pb-2"
        >
          <app-rating-only
            [evaluatorId]="evaluatorId"
            [employeeId]="employeeId"
            [evalDetail]="item"
            readOnly = true
            mode = "view"
          ></app-rating-only>
        </div>
      </div>
    </div>
  </div>
</div>
</mat-carousel-slide>
</mat-carousel>
</div>

  <div *ngIf="templateName == 'feedback_only' && !dataLoading && viewData.length>0">
    <mat-carousel
    [autoplay]="false"
    color ="warn"
    maxWidth="auto"
    proportion="40"
    [loop]="false"
    [hideArrows]="false"
    [hideIndicators]="true"
    [useKeyboard]="true"
    [useMouseWheel]="false"
    orientation="ltr"
  >

<mat-carousel-slide
      #matCarouselSlide
      *ngFor="let slide of viewData; let i = index"
      [hideOverlay]="true"
    >
    
    <div style="overflow-y: scroll;height: 25rem;">
 <div class="row">
      <div class="ml-5 mt-4 col-11 d-flex">
        <span style="font-size: 15px;font-weight: 500;margin-top:2px;">Evaluator Name : </span>
        <span class="ml-3">
          <app-user-image style="margin: 2px" [id]="slide._id"
                  
                      imgHeight="28px" imgWidth="28px" borderStyle="solid" borderWidth="2px" [borderColor]="'gray'">
                    </app-user-image>
        </span>
        <span class="ml-2 mt-1">
          <app-user-profile
                    [type]="'name'"
                    [oid]="slide._id"
                    style="font-size: 14px;"
                  ></app-user-profile>
        </span>
      </div>
    </div>

    <div class="row">
      <div class="ml-5 col-11">
        <div class="row">
          <div
            *ngFor="let item of slide.eval_data; let i = index"
            class="col-12">
            <app-feedback-only
              [evaluatorId]="evaluatorId"
              [employeeId]="employeeId"
              [evalDetail]="item"
              readOnly = true
              mode = "view"
            ></app-feedback-only>
          </div>
        </div>
      </div>
    </div>
  </div>
</mat-carousel-slide>
</mat-carousel>
  </div>

  <div *ngIf="templateName == 'comments_with_response' && !dataLoading && viewData.length>0">
    <mat-carousel
    [autoplay]="false"
    color ="warn"
    maxWidth="auto"
    proportion="40"
    [loop]="false"
    [hideArrows]="false"
    [hideIndicators]="true"
    [useKeyboard]="true"
    [useMouseWheel]="false"
    orientation="ltr"
  >

<mat-carousel-slide
      #matCarouselSlide
      *ngFor="let slide of viewData; let i = index"
      [hideOverlay]="true"
    >
   
    <div style="overflow-y: scroll;height: 25rem;">
 <div class="row">
      <div class="ml-5 mt-4 col-11 d-flex">
        <span style="font-size: 15px;font-weight: 500;margin-top:2px;">Evaluator Name : </span>
        <span class="ml-3">
          <app-user-image style="margin: 2px" [id]="slide._id"
                  
                      imgHeight="28px" imgWidth="28px" borderStyle="solid" borderWidth="2px" [borderColor]="'gray'">
                    </app-user-image>
        </span>
        <span class="ml-2 mt-1">
          <app-user-profile
                    [type]="'name'"
                    [oid]="slide._id"
                    style="font-size: 14px;"
                  ></app-user-profile>
        </span>
      </div>
    </div>

    <div class="row">
      <div class="ml-5 col-11">
        <div class="row">
          <div
            *ngFor="let item of slide.eval_data; let i = index"
            class="col-12"
          >
            <comment-with-response
              [evaluatorId]="evaluatorId"
              [employeeId]="employeeId"
              [evalDetail]="item"
              [attachmentBucket]="attachmentBucket"
              readOnly = true
              mode = "view"
              [index]="i"
            ></comment-with-response>
          </div>
        </div>
      </div>
    </div>
  </div>
</mat-carousel-slide>
</mat-carousel>
  </div>

  <div *ngIf="templateName == 'organizational_values' && !dataLoading && viewData.length>0">
    <mat-carousel
    [autoplay]="false"
    color ="warn"
    maxWidth="auto"
    proportion="40"
    [loop]="false"
    [hideArrows]="false"
    [hideIndicators]="true"
    [useKeyboard]="true"
    [useMouseWheel]="false"
    orientation="ltr"
  >

<mat-carousel-slide
      #matCarouselSlide
      *ngFor="let slide of viewData; let i = index"
      [hideOverlay]="true"
    >
    
    <div style="overflow-y: scroll;height: 25rem;">
 <div class="row">
      <div class="ml-5 mt-4 col-11 d-flex">
        <span style="font-size: 15px;font-weight: 500;margin-top:2px;">Evaluator Name : </span>
        <span class="ml-3">
          <app-user-image style="margin: 2px" [id]="slide._id"
                  
                      imgHeight="28px" imgWidth="28px" borderStyle="solid" borderWidth="2px" [borderColor]="'gray'">
                    </app-user-image>
        </span>
        <span class="ml-2 mt-1">
          <app-user-profile
                    [type]="'name'"
                    [oid]="slide._id"
                    style="font-size: 14px;"
                  ></app-user-profile>
        </span>
      </div>
    </div>

    <div class="row">
      <div class="ml-5 col-11">
        <div class="row">
          <div
            *ngFor="let item of slide.eval_data; let i = index"
            class="col-12"
          >
            <comment-and-rating-with-response
              [evaluatorId]="evaluatorId"
              [employeeId]="employeeId"
              [evalDetail]="item"
              readOnly = true
              mode = "view"
              [index]="i"
            ></comment-and-rating-with-response>
          </div>
        </div>
      </div>
    </div>
  </div>
</mat-carousel-slide>
</mat-carousel>
  </div>

  
  <div *ngIf="viewData.length == 0 && !dataLoading" style="text-align: center;">
    <div>
      <h4 class="d-flex justify-content-center align-items-center mt-3 mb-0">
        Oops ! No Data Found !
      </h4>
    </div>
    <div class="d-flex justify-content-center align-items-center">
      <img src="https://assets.kebs.app/images/nomilestone.png" class="mt-2" height="220" width="250" />
    </div>
  </div>
  
</div>

<div style = "overflow:auto" *ngIf="checked && (templateName == 'rating_only' ||templateName == 'organizational_activities') " class="mt-3">
  <div class="p-0 col-12 d-flex" *ngIf="templateName == 'rating_only' ||templateName == 'organizational_activities' ">
  <div class="p-0 col-8" >
    <div class="row" style="padding-top: 1rem">
      <div class="col-12">
        <div class="row">
          <div
            *ngFor="let item of evalPopupDetails; let i = index"
            class="col-12 pb-2"
          >
            <app-rating-only
              [evaluatorId]="evaluatorId"
              [employeeId]="employeeId"
              [evalDetail]="item"
              mode = "evaluate"
              (evaluatorResponse)="getChanges($event)"
            ></app-rating-only>
          </div>
        </div>
      </div>
    </div>
  </div>
  <mat-divider vertical></mat-divider>
  <div class="p-0 col-8" *ngIf="(templateName == 'rating_only' ||templateName == 'organizational_activities') && !dataLoading && viewData.length>0 ">
    <mat-spinner *ngIf="dataLoading" class="spinner-align" diameter="25"></mat-spinner>
    <mat-carousel
    [autoplay]="false"
    color ="warn"
    maxWidth="auto"
    proportion="40"
    [loop]="false"
    [hideArrows]="false"
    [hideIndicators]="true"
    [useKeyboard]="true"
    [useMouseWheel]="false"
    orientation="ltr"
  >

<mat-carousel-slide
      #matCarouselSlide
      *ngFor="let slide of viewData; let i = index"
      [hideOverlay]="true"
    >
    <div style="overflow-y: scroll;height: 81vh;">
 <div class="row">
      <div class="ml-5 mt-4 col-11 d-flex">
        <span style="font-size: 15px;font-weight: 500;margin-top:2px;">Evaluator Name : </span>
        <span class="ml-3">
          <app-user-image style="margin: 2px" [id]="slide._id"
                  
                      imgHeight="28px" imgWidth="28px" borderStyle="solid" borderWidth="2px" [borderColor]="'gray'">
                    </app-user-image>
        </span>
        <span class="ml-2 mt-1">
          <app-user-profile
                    [type]="'name'"
                    [oid]="slide._id"
                    style="font-size: 14px;"
                  ></app-user-profile>
        </span>
      </div>
    </div>

    <div class="row" style="padding-top: 1rem">
      <div class="ml-5 col-11">
        <div class="row">
          <div
            *ngFor="let item of slide.eval_data; let i = index"
            class="col-12 pb-2"
          >
            <app-rating-only
              [evaluatorId]="evaluatorId"
              [employeeId]="employeeId"
              [evalDetail]="item"
              readOnly = true
              mode = "view"
            ></app-rating-only>
          </div>
        </div>
      </div>
    </div>
  </div>
</mat-carousel-slide>
</mat-carousel>

<div *ngIf="viewData.length == 0 && !dataLoading" style="text-align: center;">
  <div>
    <h4 class="d-flex justify-content-center align-items-center mt-3 mb-0">
      Oops ! No Data Found !
    </h4>
  </div>
  <div class="d-flex justify-content-center align-items-center">
    <img src="https://assets.kebs.app/images/nomilestone.png" class="mt-2" height="220" width="250" />
  </div>
</div>
  </div>

</div>

</div> -->
