.text-area-class {
    display: flex;
    flex-direction: column;
    width: 100%;
    font-size: 13px;
  }

  .spinner-align {
    margin: auto;
    display: inline-block;
    text-align: center;
    margin-left: 32rem;
  }
  .overflow-ctrl{
    max-width: 85%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

:host ::ng-deep .mat-slide-toggle.mat-checked:not(.mat-disabled) .mat-slide-toggle-bar {
  background-color: #cf0001 ;
}

:host ::ng-deep  .mat-slide-toggle.mat-checked:not(.mat-disabled) .mat-slide-toggle-thumb {
  background-color: #ed6565;
}

:host ::ng-deep .carousel-slide{
  height: 79vh !important;
  overflow: hidden;
}

:host ::ng-deep .carousel  button:first-of-type{
  left: 6px !important;
}

.checked {
  color: rgba(0, 0, 0, 0.534);
  background-color: rgba(194, 193, 193, 0.24);
}
 

.not-checked{
  background: red;
   color: white;
}

.module-group-name{
  color:#1a1919;
  font-size: 15px;
  font-weight: 500;
}