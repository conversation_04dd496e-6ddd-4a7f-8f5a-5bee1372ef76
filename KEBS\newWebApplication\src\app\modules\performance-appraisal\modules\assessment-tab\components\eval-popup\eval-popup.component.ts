import { Component, Inject, OnInit } from '@angular/core';
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { takeUntil } from 'rxjs/operators';
import { UtilityService } from 'src/app/services/utility/utility.service';
import { EmployeeAppraisalsService } from '../../../appraisal-home/services/employee_appraisal/employee-appraisals.service';
import { AppraisalEvaluatorsService } from '../../../appraisal-home/services/appraisal_evaluators/appraisal-evaluators.service';
import { Subject } from 'rxjs';
import * as moment from 'moment';
import * as _ from 'underscore';
import {
  MatCarouselSlide,
  MatCarouselSlideComponent,
} from '@ngmodule/material-carousel';

import { FormControl } from '@angular/forms';
import { debounceTime } from 'rxjs/operators';
import { toNumber } from '@amcharts/amcharts4/.internal/core/utils/Type';
import { ErrorService } from 'src/app/services/error/error.service';
import { AppraisalCycleService } from 'src/app/modules/performance-appraisal/modules/appraisal-configuration/pages/appraisal-cycles/services/appraisal-cycle.service';
import { LoginService } from 'src/app/services/login/login.service';

@Component({
  selector: 'app-eval-popup',
  templateUrl: './eval-popup.component.html',
  styleUrls: ['./eval-popup.component.scss'],
})
export class EvalPopupComponent implements OnInit {
  evalPopupDetails: any;
  employeeId: any;
  appraisalYear: any;
  evaluatorId: any;
  cycleName: any;
  templateName: any;
  elaResArray: any = [];
  rating: any = [];
  feedback: any = [];

  evalResponse: any = [];
  saveEvalResponse:any = [];
  preEvalStatus:any = "";

  currentEvaluatorScore = 0;
  currentEvaluatorComment = '';
  evalLevel: any;
  halfstar: any
  //comment: any;

  employeeEvaluationMetrices: any;
  appraisersList: any;
  cycleId: any;
  moduleId: any;
  checked = false;
  LDAchecked = true;
  disabled = false;
  viewData: any = [];
  ratingView: any = [];
  feedbackView: any = [];
  dataLoading: boolean = false;
  isPreRequisiteSatisified: boolean = false;
  isPreRequisiteRequired: boolean = false;
  isSelfEvalRequired: boolean = false;
  acknowledgeStatus: boolean = true;
  allowEdit: boolean = true;
  groupTotalScoreData: any;
  groupTotalScoreDataLength:number = 0;
  selfTotalScore: number = 0;  
  l1EvalOid:any;
  l1EvalTotalScore:number = 0;
  l2EvalOid:any;
  l2EvalApprovedAll:boolean = false;
  selfEvalDetail:any;
  isMeetingScheduled:boolean = false;
  meetingNotes:string = '';
  promotionRecommendation:string = '';
  isPromotionRecommended:boolean = false;
  overallFeedback:string = '';
  attachmentBucket:any;
  currentUserOid:any;
  allowEvalRating:any;
  evalRatingStartDate:any;
  evalRatingEndDate:any;
  toolTipValueForStarRating:any;
  todayDate:any = new Date(moment().format("YYYY-MM-DD"));
  cycleBlockMsg:any = '';
  allowedManagerToEditEvaluation:boolean = false;
  appraisaloduleDetails:any;
  isSaveLoading:boolean = false;
  currMetricesIndex:number = -1;
  emp_region:any = '';
  allowSubdivforlvl2appr:any = [];
  isLevel2ApprovalNeeded:boolean = false;
  l2EvaluatorOid: any;
  isLevel2Approver: boolean = false;
  l2EvalPopupDetails: any;
  islevel2Approval: any;

  protected _onDestroy = new Subject<void>();

  // evaluatorComment: FormControl = new FormControl();

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: any,
    private _EmployeeAppraisalsService: EmployeeAppraisalsService,
    private _util: UtilityService,
    private _ErrorService: ErrorService,
    private _evalService: AppraisalEvaluatorsService,
    private _dialogRef: MatDialogRef<EvalPopupComponent>,
    private dialog: MatDialog,
    private _AppraisalCycleService: AppraisalCycleService, 
    private _LoginService: LoginService,   
  ) {
    this.evalPopupDetails = this.data.evalPopupDetails;
    this.l2EvalPopupDetails = this.data?.l2EvalPopupDetails;
    this.employeeId = this.data.employeename;
    this.cycleName = this.data.cycleName;
    this.cycleId = this.data.cycleId;
    this.moduleId = this.data.moduleId;
    this.templateName = this.data.templateName;
    this.evaluatorId = this.data.evaluatorId;
    this.appraisalYear = this.data.appraisalYear;
    this.isPreRequisiteRequired = this.data.isPreRequisiteRequired;
    this.isPreRequisiteSatisified = this.data.isPreRequisiteSatisified;
    this.isSelfEvalRequired = this.data.isSelfEvalRequired;
    this.acknowledgeStatus = this.data.acknowledgeStatus;
    this.allowEdit = this.data.isEditable;
    this.isMeetingScheduled = this.data.isMeetingScheduled;
    this.meetingNotes = this.data.meetingNotes;
    this.promotionRecommendation = this.data.promotionRecommendation;
    this.isPromotionRecommended = this.data.isPromotionRecommended;
    this.overallFeedback = this.data.overallFeedback;
    this.attachmentBucket = this.data.attachmentBucket;
    this.currentUserOid = _LoginService.getProfile().profile.oid;
    this.allowEvalRating = this.data.allowEvalRating;
    this.evalRatingEndDate = this.data.evalRatingEndDate;
    this.evalRatingStartDate =  this.data.evalRatingStartDate;
    this.toolTipValueForStarRating = this.data.toolTipValueForStarRating;
    this.allowedManagerToEditEvaluation = this.data.allowedManagerToEditEvaluation;
    this.l2EvalApprovedAll = this.data.l2EvalApprovedAll;
    this.appraisaloduleDetails = this.data?.appraisaloduleDetails;
    this.l2EvaluatorOid = this.data?.l2EvaluatorOid;

    console.log(
      this.evalPopupDetails,
      this.l2EvalPopupDetails,
      this.appraisalYear,
      this.moduleId,
      this.employeeId,
      this.cycleName,
      this.isPreRequisiteRequired,
      this.isPreRequisiteSatisified,
      this.allowEdit,
      this.acknowledgeStatus,
      this.isSelfEvalRequired,
      this.isMeetingScheduled,
      this.meetingNotes,
      this.promotionRecommendation,
      this.isPromotionRecommended,
      this.overallFeedback,
      this.attachmentBucket,
      this.allowEvalRating,
      this.toolTipValueForStarRating,
      this.allowedManagerToEditEvaluation,
      this.l2EvalApprovedAll,
      this.l2EvaluatorOid
    );
  }

  ngOnInit(): void {
    this.isLevel2Approver = this.l2EvaluatorOid.includes(this.evaluatorId);
    if (this.templateName == 'rating_and_feedback') {
      this.getRatingAndFeedback();
    } else if (
      this.templateName == 'rating_only' ||
      this.templateName == 'organizational_activities' ||
      this.templateName == 'rating_only_full_row'
    ) {
      this.checked = true;      
      this.splitScreen("initialCall");  
    }
    this.getStarRatingConfig()
    this.getEvalLevel();
    this.calculateEmployeeAppraisalGroupTotalScore();
    this.cycleBlockMsg = this.todayDate < new Date(moment(this.evalRatingStartDate).format("YYYY-MM-DD")) ? 'Yet to Open' : new Date(moment(this.evalRatingEndDate).format("YYYY-MM-DD")) < this.todayDate ? 'Expired' : '' ;
  }  

  getRatingAndFeedback() {
    this.rating = [];
    this.feedback = [];
    for (let i = 0; i < this.evalPopupDetails.length; i++) {
      if (
        this.evalPopupDetails[i]['appraisal_metrices_details'][
          'appraisal_metric_response_type'
        ] == 'feedback'
      ) {
        this.feedback.push(this.evalPopupDetails[i]);
      } else if (
        this.evalPopupDetails[i]['appraisal_metrices_details'][
          'appraisal_metric_response_type'
        ] == 'rating'
      ) {
        this.rating.push(this.evalPopupDetails[i]);
      }
    }
    console.log(this.feedback);
    console.log(this.rating);
  }

  closeDialog(status:any) {
    this._dialogRef.close({status:status});
  }

  /**
   * @description toggle change event
   */
  onChange(event) {
    console.log('slider', event.checked);
    if (event.checked) {
      this.dataLoading = true;
      this.checked = true;
      this.viewData = [];
      //   let req ={
      //     "employee_oid": "594b58f4-af57-4621-ac83-87c0f3b90426",

      // "appraisal_module_id": "5fcb1a624770a9ccba219514",

      // "appraisal_cycle_id": "6101a2090e55250011242140",

      // "evl_type": "manager",

      // "eval_oid": "78a41e4c-3616-409c-952d-670a66a17498",

      // "appraisal_year": "2021"

      //   }
      console.log(this.evalPopupDetails);
      console.log(
        this.evalPopupDetails.appraisal_module_id,
        this.evalPopupDetails.appraisal_cycle_id
      );
      let req = {
        employee_oid: this.employeeId,

        appraisal_module_id: this.moduleId,

        appraisal_cycle_id: this.cycleId,

        evl_type: 'manager',

        eval_oid: this.evaluatorId,

        appraisal_year: this.appraisalYear,

        template: this.templateName,
      };
      this._EmployeeAppraisalsService
        .getCarouselData(req)
        .pipe(takeUntil(this._onDestroy))
        .subscribe(
          (res) => {
            this.viewData = res['data'];
            if (this.templateName == 'rating_and_feedback') {
              this.getRatingAndFeedbackForView();
            }
            this.dataLoading = false;
            console.log('view', this.viewData);
          },
          (err) => {
            //this.showErrorMessage(err);
            this.dataLoading = false;
            this._ErrorService.userErrorAlert(
              err.error.code,
              'Some Error Happened in completing the Activity',
              err.error.errMessage
            );
          }
        );
    } else {
      this.checked = false;
    }
  }

  async splitScreen(type:string) {      
    
    this.viewData = [];

    let req = {
      employee_oid: this.employeeId,

      appraisal_module_id: this.moduleId,

      appraisal_cycle_id: this.cycleId,

      evl_type: 'manager',

      eval_oid: this.evaluatorId,

      appraisal_year: this.appraisalYear,

      template: this.templateName,
    };
    let emp_subdiv = {
      configuration_name : "empSubdivforlvl2submission"
    } 

    this._EmployeeAppraisalsService
    .getEmpSubDivforlvl2Submission(emp_subdiv)
    .pipe(takeUntil(this._onDestroy))
    .subscribe(
      async (res) => {
        if(res && res["data"].length > 0){
          this.allowSubdivforlvl2appr = res["data"][0]['configuration_data'];
        }
        else{
          this.allowSubdivforlvl2appr = [];
        }
      },
      (err) => {
        this._ErrorService.userErrorAlert(
          err.error.code,
          'Error Occured while completing the activity',
          err.error.errMessage
        );
      }
    );

    console.log("employee oid value",this.employeeId);
    this.emp_region = await this.getEmpSubDivision();
    this.islevel2Approval = await this.getLevel2ApprovalNeeded(); // For fetching the level 2 approval needed or not flag from configuration
    console.log("isLevel2Approval value",this.islevel2Approval);
    console.log("emp region value",this.emp_region); 
    console.log("allowed sub divisions",this.allowSubdivforlvl2appr); 
    // this.isLevel2ApprovalNeeded = this.allowSubdivforlvl2appr.includes(this.emp_region) ? true : false;
    this.isLevel2ApprovalNeeded = this.allowSubdivforlvl2appr.includes(this.emp_region) && this.islevel2Approval ? true : false;
    console.log("islevel2 approval needed value",this.isLevel2ApprovalNeeded);

    this._EmployeeAppraisalsService
      .getCarouselData(req)
      .pipe(takeUntil(this._onDestroy))
      .subscribe(
        async(res) => {

          this.viewData = res['data'];  

          if (Array.isArray(this.viewData)) {
            let p=0;
            for (let i = 0; i < this.viewData.length; i++) {
              if (this.viewData[i]._id == this.employeeId) {
                this.selfTotalScore = this.viewData[i]?.displayEvalTotalScore ? this.viewData[i]?.individual_evl_score : 0;
                if (type == 'initialCall') {
                  this.selfEvalDetail = this.viewData[i];                  
                  let temp = this.evalPopupDetails;
                  this.evalPopupDetails = [];
                  setTimeout(() => (this.evalPopupDetails = temp));
                }
                p+=1;              
              }
              else if(this.viewData[i].eval_level==1){
                this.l1EvalOid = this.viewData[i]._id;
                this.l1EvalTotalScore = this.viewData[i].individual_evl_score;           
                p+=1;
              }
              else if(this.viewData[i].eval_level==2){
                this.l2EvalOid = this.viewData[i]._id;
                p+=1;
              }

              

              if(p==3)break;
            }
          }

        },
        (err) => {          
          this.dataLoading = false;
          this._ErrorService.userErrorAlert(
            err.error.code,
            'Some Error Happened in completing the Activity',
            err.error.errMessage
          );
        }
      );
  }
  
  getLevel2ApprovalNeeded = () =>{
    return new Promise((resolve, reject) =>{
      let is_lvl2_appr = {
        configuration_name : "isLevel2Approval"
      } 

       this._EmployeeAppraisalsService
      .getLevel2ApprovalNeeded(is_lvl2_appr)
      .pipe(takeUntil(this._onDestroy))
      .subscribe(
        async (res) => {
          if(res && res["data"].length > 0){
            this.islevel2Approval = res["data"][0]['configuration_data'];
          }
          else{
            this.islevel2Approval = false;
          }
          resolve(this.islevel2Approval);
        },
        (err) => {
        reject(err);
        }
      );

    })
   }

  getEmpSubDivision = () => {
    return new Promise((resolve,reject) =>{
      let subDiv = {
        employee_oid : this.employeeId
      }
      this._EmployeeAppraisalsService
      .getEmpSubDiv(subDiv)
      .pipe(takeUntil(this._onDestroy))
      .subscribe(
        async (res) => {
          if(res && res["data"].length > 0){
            this.emp_region = res["data"];
          }
          else{
            this.emp_region = '';
          }
          resolve(this.emp_region);
        },
        (err) => {
        console.log("Error while retrieving sub division for l2 from eval popup");
        reject(err);
        }
      );

      })
    
   }



  /**
   * @description rating and feedback data in view mode
   */
  getRatingAndFeedbackForView() {
    this.ratingView = [];
    this.feedbackView = [];
    let feedback = [];
    let rating = [];
    for (let i = 0; i < this.viewData.length; i++) {
      feedback = [];
      rating = [];
      for (let j = 0; j < this.viewData[i].eval_data.length; j++) {
        if (
          this.viewData[i]['eval_data'][j]['appraisal_metrices_details'][
            'appraisal_metric_response_type'
          ] == 'feedback'
        ) {
          feedback.push(this.viewData[i]['eval_data'][j]);
        } else if (
          this.viewData[i]['eval_data'][j]['appraisal_metrices_details'][
            'appraisal_metric_response_type'
          ] == 'rating'
        ) {
          rating.push(this.viewData[i]['eval_data'][j]);
        }
      }
      this.feedbackView.push(feedback);
      this.ratingView.push(rating);
      this.viewData[i].feedback = feedback;
      this.viewData[i].rating = rating;
    }
    console.log('feedbackview', this.feedbackView);
    console.log('ratingview', this.ratingView);
    console.log('viewdatachange', this.viewData);
  }

  save() {
    console.log('save');
    this.isSaveLoading = true;

    this.appraisalYear = String(this.appraisalYear);
    let req = {
      appraisal_year: this.appraisalYear,
      employee_oid: this.employeeId,
      is_draft_mode: (this.templateName=="rating_only_full_row" && this.preEvalStatus=='Approved') ? false :  true,
      eval_reponse: this.templateName=="rating_only_full_row"?this.saveEvalResponse :this.evalResponse,
      appraisal_cycle_name:this.cycleName,
      // preRequsiteSatisfied:this.isPreRequisiteSatisified
    };
    console.log(req);
    if (this.allowEdit) {
      this._EmployeeAppraisalsService.updMetricesScore(req).subscribe(
        (res) => {
          

          if(this.templateName=="rating_only_full_row")
          {
            this.splitScreen("");
            this.calculateEmployeeAppraisalGroupTotalScore();
          }
          else{
            this._util.showMessage(
              'Employee Evaluation Saved Successfully',
              'Dismiss'
            );

            if(this.evalResponse.length === this.evalPopupDetails.length){
              this._util
                .openConfirmationSweetAlertWithCustom(
                  'Are you sure you want to close this without submitting ?',
                  'Your changes were saved successfully, Once you confirm this  will get close without submitting!'
                )
                .then(async (res) => {
                  if (res) {
                    this.closeDialog("");
                  }
                });
            }else{
              this.closeDialog("");
            }
          }

          this.isSaveLoading = false;
        },
        (err) => {
          this._util.showMessage(
            'Error evaluating employee. KEBS team has been notified',
            'Dismiss'
          );
          this.closeDialog("");
        }
      );
      if(this.templateName!="rating_only_full_row"){
        
      }
      
    } else {
      this._util.showMessage('This Cycle is Blocked', 'Dismiss');
    }
  }

  submit() {
    console.log(
      'submit',
      this.evalResponse.length,
      this.evalPopupDetails.length
    );
    this.appraisalYear = String(this.appraisalYear);
    if (this.allowEdit) {
      if (this.evalLevel == '1') {
        if (
          this.isPreRequisiteRequired ? this.isPreRequisiteSatisified : true
        ) {
          if (
            this.evalResponse.length === this.evalPopupDetails.length ||
            this.templateName == 'skill_set'
          ) {
            // this._util.showMessage("Approved","Dismiss")
            let req = {
              appraisal_year: this.appraisalYear,
              employee_oid: this.employeeId,
              is_draft_mode: false,
              eval_reponse: this.evalResponse,
              appraisal_cycle_name:this.cycleName,
              //preRequsiteSatisfied:this.isPreRequisiteSatisified
            };
            console.log(req);
            this._EmployeeAppraisalsService.updMetricesScore(req).subscribe(
              (res) => {
                this._util.showMessage(
                  'Your response has been submitted successfully!',
                  'Dismiss'
                );
              },
              (err) => {
                this._util.showMessage(
                  'Error evaluating employee. KEBS team has been notified',
                  'Dismiss'
                );
              }
            );
            this.closeDialog("Approved");
          } else {
            this._util.showMessage('Please fill all details', 'Dismiss');
          }
        } else {
          this._util.showMessage(
            'Please Assign Learning Goals for the employee before Submit',
            'Dismiss'
          );
        }
      }
      else if (this.evalLevel == '2') {
        if (
          this.isPreRequisiteRequired ? this.isPreRequisiteSatisified : true
        ) {
          if (
            this.evalResponse.length === this.l2EvalPopupDetails.length ||
            this.templateName == 'skill_set'
          ) {
            // this._util.showMessage("Approved","Dismiss")
            let req = {
              appraisal_year: this.appraisalYear,
              employee_oid: this.employeeId,
              is_draft_mode: false,
              eval_reponse: this.evalResponse,
              appraisal_cycle_name:this.cycleName,
              //preRequsiteSatisfied:this.isPreRequisiteSatisified
            };
            this._EmployeeAppraisalsService.updMetricesScore(req).subscribe(
              (res) => {
                this._util.showMessage(
                  'Your response has been submitted successfully!',
                  'Dismiss'
                );
              },
              (err) => {
                this._util.showMessage(
                  'Error evaluating employee. KEBS team has been notified',
                  'Dismiss'
                );
              }
            );
            this.closeDialog("Approved");
          } else {
            this._util.showMessage('Please fill all details', 'Dismiss');
          }
        } else {
          this._util.showMessage(
            'Please Assign Learning Goals for the employee before Submit',
            'Dismiss'
          );
        }
      }
       else {
        if (
          this.evalResponse.length === this.evalPopupDetails.length ||
          this.templateName == 'skill_set'
        ) {
          // this._util.showMessage("Approved","Dismiss")
          let req = {
            appraisal_year: this.appraisalYear,
            employee_oid: this.employeeId,
            is_draft_mode: false,
            eval_reponse: this.evalResponse,
            appraisal_cycle_name:this.cycleName,
            //preRequsiteSatisfied:this.isPreRequisiteSatisified
          };
          console.log(req);
          this._EmployeeAppraisalsService.updMetricesScore(req).subscribe(
            (res) => {
              this._util.showMessage(
                'Employee Evaluated Successfully',
                'Dismiss'
              );
            },
            (err) => {
              this._util.showMessage(
                'Error evaluating employee. KEBS team has been notified',
                'Dismiss'
              );
            }
          );
          this.closeDialog("Approved");
        } else {
          this._util.showMessage('Please fill all details', 'Dismiss');
        }
      }
    } else {
      this._util.showMessage('This Cycle is Blocked', 'Dismiss');
    }
  }

  getChanges(response) {
    console.log(response, 'response');
    console.log(this.evalResponse);
    if (
      (response['evaluator_score_awarded'] >= 0 &&
        response['evaluator_score_awarded'] != undefined) ||
      (response['evaluator_comment'] != '' &&
        response['evaluator_comment'] != undefined) ||
      (response['employee_appraisal_metrices_evaluator_comment'] &&
        response['employee_appraisal_metrices_evaluator_comment'] != undefined)
    ) {
      let data = _.where(this.evalResponse, {
        id: response['employee_evaluation_metrices_id'],
      });
      console.log(data, 'data');
      if (data == '' || data == null || this.data.length == 0) {
        this.evalResponse.push(response);
        // console.log("hi",response)
        // console.log("hii",this.evalResponse)
      } else {
        let index = this.evalResponse.findIndex(
          (x) => x.id === response['employee_evaluation_metrices_id']
        );
        console.log(index);
        this.evalResponse[index] = response;
      }
    } else {
      let index = this.evalResponse.findIndex(
        (x) => x.id === response['employee_evaluation_metrices_id']
      );
      this.evalResponse.splice(index, 1);
    }
    //this.evalResponse.push(response)
    if (
      this.checked &&
      this.templateName != 'rating_only' &&
      this.templateName != 'organizational_activities' &&
      this.templateName != 'rating_only_full_row'
    ) {
      this.evalResponse = [];
    }
    console.log(this.evalResponse);    
  }

  /**
   * @description LDA changes
   */
  getLDAChanges(event) {
    console.log(event, 'response');
    console.log(this.evalResponse);
    if (
      event.status == true &&
      ((event.response['evaluator_score_awarded'] > 0 &&
        event.response['evaluator_score_awarded'] != undefined) ||
        (event.response['evaluator_comment'] != '' &&
          event.response['evaluator_comment'] != undefined) ||
        (event.response['employee_appraisal_metrices_evaluator_comment'] &&
          event.response['employee_appraisal_metrices_evaluator_comment'] !=
            undefined))
    ) {
      let data = _.where(this.evalResponse, {
        id: event.response['employee_evaluation_metrices_id'],
      });
      console.log(data, 'data');
      if (data == '' || data == null || this.data.length == 0) {
        this.evalResponse.push(event.response);
        // console.log("hi",response)
        // console.log("hii",this.evalResponse)
      } else {
        let index = this.evalResponse.findIndex(
          (x) => x.id === event.response['employee_evaluation_metrices_id']
        );
        console.log(index);
        this.evalResponse[index] = event.response;
      }
    } else {
      console.log('in here');
      let index = this.evalResponse.findIndex(
        (x) => x.id === event.response['employee_evaluation_metrices_id']
      );
      this.evalResponse.splice(index, 1);
    }
    //this.evalResponse.push(response)
    console.log(this.evalResponse);
    if (this.evalResponse.length == 0) {
      this.LDAchecked = true;
    } else {
      this.LDAchecked = false;
    }
  }

  /**
   * @description create Learning Goals
   */
  async createEmployeeLearningGoals() {
    const { AssignLearningGoalsComponent } = await import(
      'src/app/modules/performance-appraisal/modules/assessment-tab/components/eval-popup/components/assign-learning-goals/assign-learning-goals.component'
    );
    const dialog = this.dialog.open(AssignLearningGoalsComponent, {
      width: '60%',
      height: '60%',
      autoFocus: false,
      data: {
        employee_oid: [this.employeeId],
        appraisal_year: this.appraisalYear,
        module_id: this.moduleId,
      },
    });
    dialog.afterClosed().subscribe((res) => {
      if (res != '') {
        console.log(res);
        this.isPreRequisiteSatisified = res.preRequisite;
        //  console.log("isPreRequisiteSatisified",this.isPreRequisiteSatisified)
      }
    });
  }

  /**
   * @description view Employee Profile
   */
  ViewEmployeeProfile() {
    let navigationUrl = `${window.location.origin}/main/appraisal/home/<USER>/${this.employeeId}`;
    window.open(navigationUrl);
  }

  /**
   * @description getEvalLevel
   */
  getEvalLevel() {
    if(this.isLevel2Approver){
      console.log("Current user is a level 2 approver");
      this.evalLevel = this.l2EvalPopupDetails[0][
        'employee_appraisal_metrices_evaluators_details'
      ]['employee_appraisal_metrices_evaluators']['evaluator_level']
      ? this.l2EvalPopupDetails[0][
        'employee_appraisal_metrices_evaluators_details'
      ]['employee_appraisal_metrices_evaluators']['evaluator_level']
      : '';
      
    }
    else{
      this.evalLevel = this.evalPopupDetails[0][
        'employee_appraisal_metrices_evaluators_details'
      ]['employee_appraisal_metrices_evaluators']['evaluator_level']
      ? this.evalPopupDetails[0][
          'employee_appraisal_metrices_evaluators_details'
        ]['employee_appraisal_metrices_evaluators']['evaluator_level']
      : '';
    }
  }

  /**
   * @description To Trigger Self Rating total score
   */
  calculateEmployeeAppraisalGroupTotalScore() {
    let selfRatingTotalScoreRequest = {
      appraisal_year: this.appraisalYear,
      employee_oid: this.employeeId,
      appraisal_cycle_id: this.cycleId,
      appraisal_module_id: this.moduleId,
    };

    this._EmployeeAppraisalsService
      .calculateEmployeeAppraisalGroupTotalScore(selfRatingTotalScoreRequest)
      .pipe(takeUntil(this._onDestroy))
      .subscribe(
        (result: any) => {
          if (result.err == 'N') {
            this.groupTotalScoreData = result.data;
            this.groupTotalScoreDataLength = this.groupTotalScoreData.length;           
          } else {
            console.log('Error while calculating employee group total score');
          }
        },
        (error) => {
          console.log(
            'Error while calculating employee group total score' + error
          );
        }
      );
  }

  /**
   * @description To Trigger Self Rating total score
   */
  saveRatingChanges(response) {  
    if(response.typeOfOperation=="initialGet"){
      this.getChanges(response);
    }
    else{
      this.getChanges(response);
      this.saveEvalResponse = [response];
      this.preEvalStatus = response?.preEvalStatus;
      this.currMetricesIndex = response?.currMetricesIndex;
      this.save();
      // this.splitScreen("");
    }    
  }

  // saveFeedBack(response){
  //   if(response.typeOfOperation=="initialGet"){
  //     this.getChanges(response);
  //     console.log("called response");
  //   }
  //   else{
  //     this.getChanges(response);
  //     this.saveEvalResponse = [response];
  //     this.save();
  //   }
  // }

  getHtmlEditorValue(value:string,type:string):any{

    let req = {
      employee_oid: this.employeeId,
      appraisal_year: this.appraisalYear,
    }

    if(type=='overallFeedback'){
      req['overallFeedback']  = value;
    }
    else if(type=='meetingNotes'){
      req['meetingNotes']  = value;
    }
    else if(type=='promotionRecommendation'){
      req['promotionRecommendation']  = value;
    }
    else{
      return
    }

    this._EmployeeAppraisalsService
      .updEvaluatorFeedBackOnAppraisal(req)
      .pipe(takeUntil(this._onDestroy))
      .subscribe(
        (res) => {
          if(res['err']=='N'){
            this._util.showMessage(
              res['msg'],
              'Dismiss'
            );
          }
          else if(res['err']=='Y'){
            this._util.showMessage(
              "Error while saving your feedback",
              'Dismiss'
            );
          }          
        },
        (err) => {                    
          this._ErrorService.userErrorAlert(
            err.error.code,
            'Some Error Happened in completing the Activity',
            err.error.errMessage
          );
        }
      );
  }

  getStarRatingConfig(){
    this._evalService.getStarRatingConfig()
        .pipe(takeUntil(this._onDestroy))
        .subscribe(
          (res) => {
            this.halfstar = res['data'][0]?.configuration_data;
          },
          (err) => {
            //this.showErrorMessage(err);
            this._ErrorService.userErrorAlert(
              err.error.code,
              'Some Error Happened in rating config',
              err.error.errMessage
            );
          }
        );
  }
}
   