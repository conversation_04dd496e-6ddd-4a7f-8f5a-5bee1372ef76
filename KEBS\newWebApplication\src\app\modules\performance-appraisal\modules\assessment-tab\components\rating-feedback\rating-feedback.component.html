<!-- <div class="row">
    <div class="col-12 d-flex">
        <div class="col-6">
          <div class="row">
              <div *ngIf="evalDetail?.appraisal_metrices_details?.appraisal_metric_response_type=='rating'" class="col-12">
                <app-type-star

                [metricName]="evalDetail?.appraisal_metrices_details?.appraisal_metric_name"
                [readOnly]="
                evalDetail?.employee_appraisal_metrices_evaluation_status.toLowerCase() ==
        'submitted' ||
        evalDetail?.employee_appraisal_metrices_evaluation_status.toLowerCase() ==
        'open' ||
        evalDetail?.employee_appraisal_metrices_evaluation_status.toLowerCase() ==
        'approved'
        ? false
        : true
    "
    [totalScore]="
    evalDetail?.employee_appraisal_metrices_evaluation_operation ==
      'OR'
        ? evalDetail?.employee_appraisal_metrices_scored_obtained
        : currentEvaluatorScore
    "
                [totalStars]="evalDetail?.appraisal_metrices_details?.appraisal_metric_max_score"
                (starsResponse)="getStarRating($event,evalDetail?.appraisal_metrices_id)"
              ></app-type-star>
              </div>
          </div>
        </div>
        <div class="col-6">
            <div class="row">
                <div *ngIf="evalDetail?.appraisal_metrices_details?.appraisal_metric_response_type=='feedback'" class="col-12">
                    <app-type-comment
                    [readOnly]="
                    evalDetail?.employee_appraisal_metrices_evaluation_status.toLowerCase() ==
                      'submitted' ||
                      evalDetail?.employee_appraisal_metrices_evaluation_status.toLowerCase() ==
                      'open'
                      ? false
                      : true
                  "
                  [label]="'Your Feedback'"
                  [comment]="currentEvaluatorComment"
            (commentResponse)="getComment($event,evalDetail?.appraisal_metrices_id)"
          ></app-type-comment>
                </div>
            </div> 
        </div>
    </div>
</div> -->

<!-- <div *ngIf="evalDetail?.appraisal_metrices_details?.appraisal_metric_response_type=='rating'" class="col-12"> -->
<app-type-star
  *ngIf="
    evalDetail?.appraisal_metrices_details?.appraisal_metric_response_type ==
    'rating'
  "
  [metricName]="evalDetail?.appraisal_metrices_details?.appraisal_metric_name"
  [readOnly]="
  (evalDetail?.employee_appraisal_metrices_evaluation_status.toLowerCase() ==
  'submitted' ||
  evalDetail?.employee_appraisal_metrices_evaluation_status.toLowerCase() ==
  'open' ||
  evalDetail?.employee_appraisal_metrices_evaluation_status.toLowerCase() ==
  'approved') && !readOnly
  ? false
  : true
  "
  [totalScore]="
    evalDetail?.employee_appraisal_metrices_evaluation_operation == 'OR'
      ? evalDetail?.employee_appraisal_metrices_scored_obtained
      : currentEvaluatorScore
  "
  [totalStars]="
    evalDetail?.appraisal_metrices_details?.appraisal_metric_max_score
  "
  [halfstar]="halfstar"
  (starsResponse)="getStarRating($event, evalDetail?.appraisal_metrices_id)"
></app-type-star>
<!-- </div> -->

<!-- <div *ngIf="evalDetail?.appraisal_metrices_details?.appraisal_metric_response_type=='feedback'" class="col-12"> -->
<app-type-comment
  *ngIf="
    evalDetail?.appraisal_metrices_details?.appraisal_metric_response_type ==
    'feedback'
  "
  [readOnly]="
  (evalDetail?.employee_appraisal_metrices_evaluation_status.toLowerCase() ==
  'submitted' ||
  evalDetail?.employee_appraisal_metrices_evaluation_status.toLowerCase() ==
  'open' ||
  evalDetail?.employee_appraisal_metrices_evaluation_status.toLowerCase() ==
  'approved') && !readOnly
  ? false
  : true
  "
  type="feedbackOnly"
  [question]="evalDetail?.appraisal_metrices_details?.appraisal_metric_name"
  [label]="'Your Feedback'"
  [comment]="currentEvaluatorComment"
  (commentResponse)="getComment($event, evalDetail?.appraisal_metrices_id)"
></app-type-comment>
<!-- </div> -->
