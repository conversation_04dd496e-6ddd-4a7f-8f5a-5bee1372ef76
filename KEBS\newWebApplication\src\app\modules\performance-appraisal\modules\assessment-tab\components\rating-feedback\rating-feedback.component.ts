import {
  Component,
  EventEmitter,
  Inject,
  Input,
  OnInit,
  Output,
} from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { takeUntil } from 'rxjs/operators';
import { UtilityService } from 'src/app/services/utility/utility.service';
import { EmployeeAppraisalsService } from '../../../appraisal-home/services/employee_appraisal/employee-appraisals.service';
import { AppraisalEvaluatorsService } from '../../../appraisal-home/services/appraisal_evaluators/appraisal-evaluators.service';
import { Subject } from 'rxjs';
import * as moment from 'moment';
import * as _ from 'underscore';

@Component({
  selector: 'app-rating-feedback',
  templateUrl: './rating-feedback.component.html',
  styleUrls: ['./rating-feedback.component.scss'],
})
export class RatingFeedbackComponent implements OnInit {
  @Input() evalDetail: any;
  @Input() readOnly: boolean = false;
  @Input() employeeId: any;
  @Input() evaluatorId: any;
  @Input() mode : any;
  @Input() halfstar : any
  @Output() evaluatorResponse = new EventEmitter<any>();

  currentEvaluatorScore = 0;
  currentEvaluatorComment = '';

  constructor(
    private _EmployeeAppraisalsService: EmployeeAppraisalsService,
    private _util: UtilityService,
    private _evalService: AppraisalEvaluatorsService
  ) {}

  ngOnInit(): void {
    
    this.getTotalScore();
    
  }

  async getTotalScore() {
    console.log(this.evalDetail);
    this.currentEvaluatorScore = this.evalDetail.eval_score;
    this.currentEvaluatorComment = this.evalDetail.eval_comment;
    if(this.mode == "evaluate"){ if (
      this.evalDetail?.appraisal_metrices_details
        ?.appraisal_metric_response_type == 'feedback' &&
      this.currentEvaluatorComment != '' &&
      this.currentEvaluatorComment != undefined
    ) {
      // let response = {
      //   evaluator_comment: this.currentEvaluatorComment,
      //   id:this.evalDetail?.appraisal_metrices_id
      // }
      let response = {
        // employee_evaluation_metrices_id: this.evalDetail._id,
        // evaluator_oid: this.evaluatorId,
        // evaluator_score_awarded: 0,
        // approver_action_date: new Date(),
        // evaluator_comment: this.currentEvaluatorComment,
        // approver_action_is_active: true,
        // employe_oid: this.employeeId,
        // appraisal_metrices_name:
        //   this.evalDetail.appraisal_metrices_details.appraisal_metric_name,
        employee_evaluation_metrices_id: this.evalDetail._id,
        id: this.evalDetail._id,
        evaluator_score_awarded: 0,
        evaluator_comment: '',
        employee_oid: this.employeeId,
        appraisal_module_id: this.evalDetail.appraisal_module_id,
        appraisal_cycle_id: this.evalDetail.appraisal_cycle_id,
        appraisal_metrices_id: this.evalDetail.appraisal_metrices_id,
        employee_appraisal_metrices_evaluator_type: 'manager',
        employee_appraisal_metrices_evaluator_oid: this.evaluatorId,
        employee_appraisal_metrices_evaluator_score_awarded: 0,
        employee_appraisal_metrices_evaluator_comment:
          this.currentEvaluatorComment,
        employee_appraisal_metrices_evaluator_status: 'Approved',
        employee_appraisal_module_group_name: this.evalDetail.group_name,
        appraisal_metrices_name:
          this.evalDetail.appraisal_metrices_details.appraisal_metric_name,
      };
      this.evaluatorResponse.emit(response);
    } else if (
      this.evalDetail?.appraisal_metrices_details
        ?.appraisal_metric_response_type == 'rating' &&
      this.currentEvaluatorScore > 0 &&
      this.currentEvaluatorScore != undefined
    ) {
      // let response = {
      //   evaluator_score_awarded:this.currentEvaluatorScore,
      //   id:this.evalDetail?.appraisal_metrices_id
      // }
      let response = {
        // employee_evaluation_metrices_id: this.evalDetail._id,
        // evaluator_oid: this.evaluatorId,
        // evaluator_score_awarded: this.currentEvaluatorScore,
        // approver_action_date: new Date(),
        // approver_action_is_active: true,
        // evaluator_comment: '',
        // employe_oid: this.employeeId,
        // appraisal_metrices_name:
        //   this.evalDetail.appraisal_metrices_details.appraisal_metric_name,
        employee_evaluation_metrices_id: this.evalDetail._id,
        id: this.evalDetail._id,
        evaluator_score_awarded: this.currentEvaluatorScore,
        evaluator_comment: '',
        employee_oid: this.employeeId,
        appraisal_module_id: this.evalDetail.appraisal_module_id,
        appraisal_cycle_id: this.evalDetail.appraisal_cycle_id,
        appraisal_metrices_id: this.evalDetail.appraisal_metrices_id,
        employee_appraisal_metrices_evaluator_type: 'manager',
        employee_appraisal_metrices_evaluator_oid: this.evaluatorId,
        employee_appraisal_metrices_evaluator_score_awarded:
          this.currentEvaluatorScore,
        employee_appraisal_metrices_evaluator_comment: '',
        employee_appraisal_metrices_evaluator_status: 'Approved',
        employee_appraisal_module_group_name: this.evalDetail.group_name,
        appraisal_metrices_name:
          this.evalDetail.appraisal_metrices_details.appraisal_metric_name,
      };
      this.evaluatorResponse.emit(response);
    }
  }
  }

  async getStarRating(event, id) {
    //console.log(event,id,index);
    // let response = {
    //   evaluator_score_awarded: parseInt(event),
    //   id:id
    // }

    //console.log(this.evalResponse)
    let response = {
      // employee_evaluation_metrices_id: this.evalDetail._id,
      // evaluator_oid: this.evaluatorId,
      // evaluator_score_awarded: parseInt(event),
      // approver_action_date: new Date(),
      // approver_action_is_active: true,
      // evaluator_comment: '',
      // employe_oid: this.employeeId,
      // appraisal_metrices_name:
      //   this.evalDetail.appraisal_metrices_details.appraisal_metric_name,
      employee_evaluation_metrices_id: this.evalDetail._id,
      id: this.evalDetail._id,
      evaluator_score_awarded: parseInt(event),
      evaluator_comment: '',
      employee_oid: this.employeeId,
      appraisal_module_id: this.evalDetail.appraisal_module_id,
      appraisal_cycle_id: this.evalDetail.appraisal_cycle_id,
      appraisal_metrices_id: this.evalDetail.appraisal_metrices_id,
      employee_appraisal_metrices_evaluator_type: 'manager',
      employee_appraisal_metrices_evaluator_oid: this.evaluatorId,
      employee_appraisal_metrices_evaluator_score_awarded: parseInt(event),
      employee_appraisal_metrices_evaluator_comment: '',
      employee_appraisal_metrices_evaluator_status: 'Approved',
      employee_appraisal_module_group_name: this.evalDetail.group_name,
      appraisal_metrices_name:
        this.evalDetail.appraisal_metrices_details.appraisal_metric_name,
    };
    this.evaluatorResponse.emit(response);
  }

  async getComment(event, id) {
    //console.log(event, id);
    // let response = {
    //   evaluator_comment: event,
    //   id:id
    // }

    //  console.log(this.evalResponse)
    let response = {
      // employee_evaluation_metrices_id: this.evalDetail._id,
      // evaluator_oid: this.evaluatorId,
      // evaluator_score_awarded: 0,
      // approver_action_date: new Date(),
      // evaluator_comment: event,
      // approver_action_is_active: true,
      // employe_oid: this.employeeId,
      // appraisal_metrices_name:
      //   this.evalDetail.appraisal_metrices_details.appraisal_metric_name,
      employee_evaluation_metrices_id: this.evalDetail._id,
      id: this.evalDetail._id,
      evaluator_score_awarded: 0,
      evaluator_comment: event,
      employee_oid: this.employeeId,
      appraisal_module_id: this.evalDetail.appraisal_module_id,
      appraisal_cycle_id: this.evalDetail.appraisal_cycle_id,
      appraisal_metrices_id: this.evalDetail.appraisal_metrices_id,
      employee_appraisal_metrices_evaluator_type: 'manager',
      employee_appraisal_metrices_evaluator_oid: this.evaluatorId,
      employee_appraisal_metrices_evaluator_score_awarded: 0,
      employee_appraisal_metrices_evaluator_comment: event,
      employee_appraisal_metrices_evaluator_status: 'Approved',
      employee_appraisal_module_group_name: this.evalDetail.group_name,
      appraisal_metrices_name:
        this.evalDetail.appraisal_metrices_details.appraisal_metric_name,
    };
    this.evaluatorResponse.emit(response);
  }
}
