<div class="mx-1 my-2">
    <div class="card p-2" style="background-color: #ffffff;border-left: 4px solid #b999f9;">      
        <div class="row">    
            <div class="p-0 mx-0 my-auto overflow-ctrl" [ngClass]="isLevel2ApprovalNeeded && isLevel2Approver ? 'col-2' : 'col-3'">                
                <span style="font-weight: 500;color: #5cb6dc;" class="overflow-ctrl"
                [tooltip]="metricNameTooltip" placement="top" content-type="template">
                    {{evalDetail?.appraisal_metrices_details?.appraisal_metric_name}}
                </span>
                <ng-template #metricNameTooltip>
                    {{evalDetail?.appraisal_metrices_details?.appraisal_metric_name}}
                </ng-template>
                <br>
                <span>
                    <span style="font-weight: 500;" class="pr-1"
                        [matTooltip]="evalDetail?.appraisal_metrices_details?.appraisal_metric_unit">Unit
                    </span>
                    {{evalDetail?.appraisal_metrices_details?.appraisal_metric_unit}}
                </span>
            </div>
            <div class="mx-0 my-auto" [matTooltip]="evalDetail?.group_name" [ngClass]="[isSelfEvalRequired && isLevel2ApprovalNeeded && isLevel2Approver ? 'col-1 p-0' : !isSelfEvalRequired && isLevel2ApprovalNeeded && isLevel2Approver ?   'col-1 p-0' :  isSelfEvalRequired ? 'col-2 p-0' : 'col-3 px-5 py-0' , evalDetail?.appraisal_metrices_weightage ? 'overflow-ctrl' : 'over-flow-two-line']">
                <span>
                    <span style="font-weight: 500;" class="pr-1">Type</span>                
                    {{evalDetail?.group_name}}
                </span>
                <br>
                <span *ngIf="evalDetail?.appraisal_metrices_weightage!=0;else emptyContent">
                    <span class="pr-1" style="font-weight: 400;">Weightage</span>                    
                    {{evalDetail?.appraisal_metrices_weightage}}%
                </span>
                <ng-template #emptyContent>
                    <span>&nbsp;</span>
                </ng-template>
            </div>
            <div class="p-0 d-flex flex-column justify-content-center align-items-center overflow-ctrl" *ngIf="isSelfEvalRequired" [ngClass]="isSelfEvalRequired ?  'col-2' : ''">
                <div class="card d-flex justify-content-center align-items-center" style="height: 40px;width: 55px; border-radius:3px;background-color: #249fdc;color:white;font-weight: 500;"
                [matTooltip]="evalDetail?.employee_appraisal_metrices_evaluation_status!='Open'  ? selfScoreForEmpMetrices+'/100' : null">
                    {{evalDetail?.employee_appraisal_metrices_evaluation_status!='Open' ? selfScoreForEmpMetrices:'-'}}
                </div>
                <span *ngIf="evalDetail?.employee_appraisal_metrices_evaluation_status!='Open'">{{selfScoreLegendName}}</span>          
            </div>
            <div class="p-0 d-flex flex-column justify-content-center align-items-center overflow-ctrl" [ngClass]="isSelfEvalRequired ?  'col-2' : 'col-3'">
                <app-star [readOnly]="false" [totalScore]="currentEvaluatorScore?
                         currentEvaluatorScore:0" [totalStars]="evalDetail?.appraisal_metrices_details?.appraisal_metric_max_score" [tooltipValue]="toolTipValueForStarRating" [starValue]="starValue" [currentStatus]="currentEvaluatorEvalStatus"
                    (starsResponse)="getStarRating($event,evalDetail?.appraisal_metrices_id)" *ngIf="!acknowledgeStatus && evalDetail?.eval_oid == evaluatorId && allowEvalRating ;else scoreTile"></app-star>

                <ng-template #scoreTile>
                    <div class="card d-flex justify-content-center align-items-center" *ngIf="appraisaloduleDetails?.display_appraiser_comments" style="height: 40px;width: 55px; border-radius:3px;background-color: #e56635;color:white;font-weight: 500;" 
                    [matTooltip]="currentEvaluatorScore>0  ? currentEvaluatorScore+'/100' : null" [ngStyle]="{'opacity': ((currentEvaluatorEvalStatus=='Saved'||currentEvaluatorEvalStatus=='Open') && allowEvalRating==false)  ? '0.5' : '1' }">
                        {{currentEvaluatorEvalStatus!='Open'  ? currentEvaluatorScore:'-'}}
                    </div>
                    <span *ngIf="appraisaloduleDetails?.display_appraiser_comments;else onlyLegend">
                        <span *ngIf="currentEvaluatorEvalStatus!='Open'">{{evalDetail?.legend_name}}</span>
                    </span>
                    <ng-template #onlyLegend>
                        <span class="card d-flex justify-content-center align-items-center over-flow" *ngIf="currentEvaluatorEvalStatus!='Open';else noLegend" style="height: 40px;width: 150px; border-radius:3px;background-color: #e56635;color:white;font-weight: 500;">
                            {{evalDetail?.legend_name}}
                          </span>
                          <ng-template #noLegend>
                            <span class="card d-flex justify-content-center align-items-center over-flow" style="height: 40px;width: 150px; border-radius:3px;background-color: #e56635;color:white;font-weight: 500;"> - </span>
                          </ng-template>
                    </ng-template>
                </ng-template>
                
            </div>

            <div class="p-0 d-flex flex-column justify-content-center align-items-center overflow-ctrl" [ngClass]="isSelfEvalRequired ?  'col-2' : 'col-3'" *ngIf="isLevel2ApprovalNeeded && isLevel2Approver">
                <app-star [readOnly]="false" [totalScore]="L2EvaluatorScore?
                L2EvaluatorScore:0" [totalStars]="l2EvalDetail?.appraisal_metrices_details?.appraisal_metric_max_score" [tooltipValue]="toolTipValueForStarRating" [starValue]="starValue" [currentStatus]="currentEvaluatorEvalStatus"
                    (starsResponse)="getStarRatingforL2Appr($event,evalDetail?.appraisal_metrices_id)" *ngIf="!acknowledgeStatus && (evalDetail?.eval_oid == evaluatorId && allowEvalRating || isLevel2ApprovalNeeded) ;else scoreTile"></app-star>

                <ng-template #scoreTile>
                    <div class="card d-flex justify-content-center align-items-center" *ngIf="appraisaloduleDetails?.display_appraiser_comments" style="height: 40px;width: 55px; border-radius:3px;background-color: #e56635;color:white;font-weight: 500;" 
                    [matTooltip]="L2EvaluatorScore>0  ? L2EvaluatorScore+'/100' : null" [ngStyle]="{'opacity': ((currentL2EvaluatorEvalStatus=='Saved'||currentL2EvaluatorEvalStatus=='Open') && allowEvalRating==false)  ? '0.5' : '1' }">
                        {{currentL2EvaluatorEvalStatus!='Open'  ? L2EvaluatorScore:'-'}}
                    </div>
                    <span *ngIf="appraisaloduleDetails?.display_appraiser_comments;else onlyLegend">
                        <span *ngIf="currentL2EvaluatorEvalStatus!='Open'">{{l2EvalDetail?.legend_name}}</span>
                    </span>
                    <ng-template #onlyLegend>
                        <span class="card d-flex justify-content-center align-items-center over-flow" *ngIf="currentL2EvaluatorEvalStatus!='Open';else noLegend" style="height: 40px;width: 150px; border-radius:3px;background-color: #e56635;color:white;font-weight: 500;">
                            {{l2EvalDetail?.legend_name}}
                          </span>
                          <ng-template #noLegend>
                            <span class="card d-flex justify-content-center align-items-center over-flow" style="height: 40px;width: 150px; border-radius:3px;background-color: #e56635;color:white;font-weight: 500;"> - </span>
                          </ng-template>
                    </ng-template>
                </ng-template>
                
            </div>

            <div class="col-2 p-0 m-0 d-flex justify-content-center align-items-center">
                <div *ngIf="!isSaveLoading;else loader">
                    <div class="d-flex justify-content-center align-items-center">
                        <span class="status-circular-in-thread" [ngStyle] = "{'background-color' : getStatusColor( evalDetail?.employee_appraisal_metrices_evaluation_status)}"></span>
                        <span class="pl-2">
                        {{
                            evalDetail?.employee_appraisal_metrices_evaluation_status
                        }}
                        </span>
                    </div>
                </div>

                <ng-template #loader>
                    <div class="d-flex flex-row">
                      <mat-spinner matTooltip="Please wait..." diameter="20"> </mat-spinner>
                      <span class="pl-2">saving...</span>
                    </div>
                </ng-template>
            </div>
            <div class="col-1 pt-2 px-0 d-flex flex-column justify-content-center align-items-center">
                <button *ngIf="evalDetail?.eval_oid == evaluatorId" mat-icon-button matTooltip="Add comments" class="icon-tray-button" (click)="openComments()">
                    <mat-icon class="smallCardIcon" [ngStyle] = "{'color': anyCommentExist  ? 'green' : '' }">forum</mat-icon>
                </button>

                <attachment-upload-btn
                [destinationBucket]="attachmentBucket.destination_bucket"
                [routingKey]="attachmentBucket.routing_key"
                [allowEdit]="allowEmpAttachmentUpload"
                [contextId]="getContextIdForAttachments('Employee')"               
                ></attachment-upload-btn>

                <attachment-upload-btn
                [destinationBucket]="attachmentBucket.destination_bucket"
                [routingKey]="attachmentBucket.routing_key"
                [allowEdit]="allowL1EvalAttachmentUpload"
                [contextId]="getContextIdForAttachments('L1Eval')"
                (change)="contextIdChangeInattachments($event)"
                ></attachment-upload-btn>
            </div>      
        </div>
    </div>    
</div>