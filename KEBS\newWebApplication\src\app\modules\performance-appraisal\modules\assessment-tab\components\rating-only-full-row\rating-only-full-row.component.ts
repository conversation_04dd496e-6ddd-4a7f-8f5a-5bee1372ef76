import { Component, OnInit,Input,Output,EventEmitter } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ifError } from 'assert';
import { UtilityService } from 'src/app/services/utility/utility.service';
import { EmployeeAppraisalsService } from '../../../appraisal-home/services/employee_appraisal/employee-appraisals.service';
import { takeUntil } from 'rxjs/operators';
import { Subject } from 'rxjs';
@Component({
  selector: 'app-rating-only-full-row',
  templateUrl: './rating-only-full-row.component.html',
  styleUrls: ['./rating-only-full-row.component.scss'],
})
export class RatingOnlyFullRowComponent implements OnInit {
  @Input() evalDetail: any;
  @Input() employeeId: any;
  @Input() evaluatorId: any;
  @Input() readOnly: boolean = false;
  @Input() mode: any;
  @Input() allowEdit: boolean;
  @Input() allowEvalRating: boolean;
  @Output() evaluatorResponse = new EventEmitter<any>();
  @Input() groupTotalScore: any;
  @Input() isSelfEvalRequired: boolean;
  @Input() acknowledgeStatus: boolean;
  @Input() selfEvlMetricesData: any;
  @Input() attachmentBucket: any;
  @Input() toolTipValueForStarRating:any;
  @Input() allowedManagerToEditEvaluation:any;
  @Input() l2EvalApprovedAll:boolean;
  @Input() appraisaloduleDetails:any;
  @Input() isSaveLoading:boolean = false;
  @Input() currMetricesIndex:number=-1;
  @Input() isLevel2ApprovalNeeded: boolean;
  @Input() isLevel2Approver: any;
  @Input() l2EvalPopupDetails:any;

  currentEvaluatorScore: number = 0;
  scoreForDispaly: number = 0;
  slefTotalScore: number = 0;
  selfScoreForEmpMetrices: number = 0;
  selfScoreLegendName: string = '';  
  starValue = [0, 25, 50, 75, 100];
  currentEvaluatorEvalStatus: any;
  allowL1EvalAttachmentUpload: boolean = true;
  allowEmpAttachmentUpload: boolean = false;
  isChatReadOnly:boolean = false;
  anyCommentExist:boolean = false;
  L2EvaluatorScore: number = 0;
  l2EvalDetail : any;
  currentL2EvaluatorEvalStatus: any;

  protected _onDestroy = new Subject<void>();

  constructor(
    public $dialog: MatDialog,
    private _util: UtilityService,
    private _EmployeeAppraisalsService: EmployeeAppraisalsService
  ) {}

  ngOnInit(): void {    
    this.getTotalScore();
    this.checkAnyCommentExistOrNot();

    this.allowL1EvalAttachmentUpload = (!this.acknowledgeStatus && this.evalDetail?.eval_oid == this.evaluatorId && this.allowEvalRating ) ? true : false;
    this.isChatReadOnly =  (!this.acknowledgeStatus && this.evalDetail?.eval_oid == this.evaluatorId && this.allowEvalRating ) ? false : true;
    this.starValue = this.appraisaloduleDetails?.display_appraiser_comments ? [0, 25, 50, 75, 100] : [25,50,100];

    if (Array.isArray(this.selfEvlMetricesData?.eval_data)) {
      for (let j = 0; j < this.selfEvlMetricesData?.eval_data.length; j++) {
        if (
          this.selfEvlMetricesData.eval_data[j].appraisal_metrices_id ==
          this.evalDetail?.appraisal_metrices_id
        ) {
          this.selfScoreForEmpMetrices =
            this.selfEvlMetricesData.eval_data[j].eval_score;
          this.selfScoreLegendName =
            this.selfEvlMetricesData.eval_data[j].legend_name;
          break;
        }
      }
    }

    this.currentEvaluatorEvalStatus =
      this.evalDetail?.employee_appraisal_metrices_evaluators_details?.employee_appraisal_metrices_evaluators?.employee_appraisal_metrices_evaluator_status;

    if(this.l2EvalPopupDetails && Array.isArray(this.l2EvalPopupDetails) && this.l2EvalPopupDetails.length >0){
      this.currentL2EvaluatorEvalStatus =
        this.l2EvalPopupDetails[this.currMetricesIndex]?.employee_appraisal_metrices_evaluators_details?.employee_appraisal_metrices_evaluators?.employee_appraisal_metrices_evaluator_status;
    }
    else{
      console.log("l2EvalPopup details not found for evalstatus!");
    }
    
  }

  /**
   * @description calculate score
   */
  async getTotalScore() {
    this.l2EvalDetail = this.l2EvalPopupDetails[this.currMetricesIndex];
    if(this.evalDetail?.l2EvalOid.includes(this.evaluatorId)){
      // this.currentEvaluatorScore = this.evalDetail?.l2Eval_score[2];
    }
    this.currentEvaluatorScore = this.evalDetail.eval_score;

    if(this.l2EvalPopupDetails && Array.isArray(this.l2EvalPopupDetails) && this.l2EvalPopupDetails.length >0){
      this.L2EvaluatorScore = this.l2EvalPopupDetails[this.currMetricesIndex]?.eval_score;
    }
    else{
      this.L2EvaluatorScore = 0;
    }
    // this.scoreForDispaly =
    //   (this.currentEvaluatorScore / 100) *
    //   this.evalDetail?.appraisal_metrices_weightage;
    // this.scoreForDispaly =
    //   Math.round((this.scoreForDispaly + Number.EPSILON) * 100) / 100;
    if (this.mode == 'evaluate') {
      if (
        this.currentEvaluatorScore >= 0 &&
        this.currentEvaluatorScore != undefined
      ) {
        let response = {
          employee_evaluation_metrices_id: this.evalDetail._id,
          id: this.evalDetail._id,
          evaluator_score_awarded: this.currentEvaluatorScore,
          approver_action_date: new Date(),
          approver_action_is_active: true,
          evaluator_comment: '',
          employe_oid: this.employeeId,
          appraisal_module_id: this.evalDetail.appraisal_module_id,
          appraisal_cycle_id: this.evalDetail.appraisal_cycle_id,
          appraisal_metrices_id: this.evalDetail.appraisal_metrices_id,
          employee_appraisal_metrices_evaluator_type: 'manager',
          employee_appraisal_metrices_evaluator_oid: this.evaluatorId,
          employee_appraisal_metrices_evaluator_score_awarded:
            this.currentEvaluatorScore,
          employee_appraisal_metrices_evaluator_attachments:
            this.evalDetail.employee_appraisal_metrices_evaluator_attachments,
          employee_appraisal_metrices_evaluator_comment: '',
          employee_appraisal_metrices_evaluator_status: 'Approved',
          employee_appraisal_module_group_name: this.evalDetail.group_name,
          appraisal_metrices_name:
            this.evalDetail.appraisal_metrices_details.appraisal_metric_name,
          typeOfOperation: 'initialGet',
          preEvalStatus:this.currentEvaluatorEvalStatus,
          // updOverAllScore:true
        };
        this.evaluatorResponse.emit(response);
      }
    }
  }

  /**
   * @description get start rating response
   */
  async getStarRating(event, id) {
    if (this.allowEdit && this.allowEvalRating) {
      // this.scoreForDispaly =
      //   (parseInt(event) / 100) * this.evalDetail?.appraisal_metrices_weightage;

      // this.scoreForDispaly =
      //   Math.round((this.scoreForDispaly + Number.EPSILON) * 100) / 100;

      let response = {
        employee_evaluation_metrices_id: this.evalDetail._id,
        id: this.evalDetail._id,
        evaluator_score_awarded: parseInt(event),
        approver_action_date: new Date(),
        approver_action_is_active: true,
        evaluator_comment: '',
        employe_oid: this.employeeId,
        appraisal_module_id: this.evalDetail.appraisal_module_id,
        appraisal_cycle_id: this.evalDetail.appraisal_cycle_id,
        appraisal_metrices_id: this.evalDetail.appraisal_metrices_id,
        employee_appraisal_metrices_evaluator_type: 'manager',
        employee_appraisal_metrices_evaluator_oid: this.evaluatorId,
        employee_appraisal_metrices_evaluator_score_awarded: parseInt(event),
        employee_appraisal_metrices_evaluator_comment: '',
        employee_appraisal_metrices_evaluator_status: 'Approved',
        employee_appraisal_module_group_name: this.evalDetail.group_name,
        appraisal_metrices_name:
          this.evalDetail.appraisal_metrices_details.appraisal_metric_name,
        typeOfOperation: 'edit',
        preEvalStatus:this.currentEvaluatorEvalStatus,
        currMetricesIndex:this.currMetricesIndex,
        // updOverAllScore:true,
      };

      this.evaluatorResponse.emit(response);
    } else {

      if(this.allowEvalRating){
        this._util.showMessage('This Cycle has been expired', 'Dismiss');
      }
      else{
        this._util.showMessage('This Cycle is Blocked', 'Dismiss');
      }
    }
  }


  /**
   * @description get start rating response for level 2 approver
   */
  async getStarRatingforL2Appr(event, id) {
    if (this.allowEdit && this.allowEvalRating) {
      // this.scoreForDispaly =
      //   (parseInt(event) / 100) * this.evalDetail?.appraisal_metrices_weightage;

      // this.scoreForDispaly =
      //   Math.round((this.scoreForDispaly + Number.EPSILON) * 100) / 100;

      let response = {
        employee_evaluation_metrices_id: this.l2EvalPopupDetails[this.currMetricesIndex]._id,
        id: this.l2EvalPopupDetails[this.currMetricesIndex]._id,
        evaluator_score_awarded: parseInt(event),
        // l2Evaluator_score_awarded: parseInt(event),
        approver_action_date: new Date(),
        approver_action_is_active: true,
        evaluator_comment: '',
        employe_oid: this.employeeId,
        appraisal_module_id: this.l2EvalPopupDetails[this.currMetricesIndex].appraisal_module_id,
        appraisal_cycle_id: this.l2EvalPopupDetails[this.currMetricesIndex].appraisal_cycle_id,
        appraisal_metrices_id: this.l2EvalPopupDetails[this.currMetricesIndex].appraisal_metrices_id,
        employee_appraisal_metrices_evaluator_type: 'manager',
        employee_appraisal_metrices_evaluator_oid: this.evaluatorId,
        employee_appraisal_metrices_evaluator_score_awarded: parseInt(event),
        // employee_appraisal_metrices_l2Evaluator_score_awarded: parseInt(event),
        employee_appraisal_metrices_evaluator_comment: '',
        employee_appraisal_metrices_evaluator_status: 'Approved',
        employee_appraisal_module_group_name: this.l2EvalPopupDetails[this.currMetricesIndex].group_name,
        appraisal_metrices_name:
          this.l2EvalPopupDetails[this.currMetricesIndex].appraisal_metrices_details.appraisal_metric_name,
        typeOfOperation: 'edit',
        preEvalStatus:this.currentEvaluatorEvalStatus,
        currMetricesIndex:this.currMetricesIndex,
        // updOverAllScore:true,
      };


      this.evaluatorResponse.emit(response);
    } else {

      if(this.allowEvalRating){
        this._util.showMessage('This Cycle has been expired', 'Dismiss');
      }
      else{
        this._util.showMessage('This Cycle is Blocked', 'Dismiss');
      }
    }
  }

  /**
   * @description open comments
   */
  async openComments() {
    let msg;
    let inputData = {
      application_id: 92,
      unique_id_1: this.evalDetail._id,
      unique_id_2: '',
      application_name: 'Performance Appraisal',
      title: this.evalDetail?.appraisal_metrices_details?.appraisal_metric_name,
    };

    let modalParams = {
      inputData: inputData,
      context: {
        Name: this.evalDetail?.appraisal_metrices_details
          ?.appraisal_metric_name,
        Status: this.evalDetail?.employee_appraisal_metrices_evaluation_status,
      },
      commentBoxHeight: '100vh',
      commentBoxScrollHeight: '80%',
      isReadOnly:this.isChatReadOnly
    };

    const { ChatCommentContextModalComponent } = await import(
      'src/app/modules/shared-lazy-loaded-components/chat-comment-context-modal/chat-comment-context-modal.component'
    );
    const openChatCommentContextModalComponent = this.$dialog.open(
      ChatCommentContextModalComponent,
      {
        height: '100%',
        width: '50%',
        position: { right: '0px' },
        data: { modalParams: modalParams },
      }
    );
  }

  /**
   * @description open comments
   */
  getContextIdForAttachments(type:string) {
    if(type=='Employee'){
      let val = this.evalDetail?.attachmentContextId?.selfEvalAttachmentId;
      return val && val != null && val != '' ? val : null;
    }
    else if(type == 'L1Eval'){
      let val = this.evalDetail?.attachmentContextId?.l1EvalAttachmentId;
      return val && val != null && val != '' ? val : null;
    }
  }

  contextIdChangeInattachments(event: Event) {
    if (this.getContextIdForAttachments('L1Eval') != event) {
      this.evalDetail.employee_appraisal_metrices_evaluator_attachments = event;

      let req = {
        employee_appraisal_metrices_id: this.evalDetail._id,
        employee_appraisal_metrices_evaluator_attachments: event,
        employee_appraisal_metrices_evaluator_oid: this.evaluatorId,
      };

      this._EmployeeAppraisalsService
        .contextIdChangeInattachments(req)
        .subscribe(
          (res) => {
            this._util.showMessage(
              'Your Document successfully uploaded',
              'Dismiss'
            );
          },
          (err) => {
            this._util.showMessage(
              'Error while uploading your document',
              'Dismiss'
            );
          }
        );
    }
  }

   /**
   * @description To Trigger Self Rating total score
   */
    checkAnyCommentExistOrNot() {
      let commentExistOrNotCheckRequest = {
        application_id: 92,
        unique_id_1: this.evalDetail._id,
        unique_id_2: '',
        application_name: 'Performance Appraisal',
        title: this.evalDetail?.appraisal_metrices_details?.appraisal_metric_name,
      };
  
      this._EmployeeAppraisalsService
        .checkAnyCommentExistOrNot(
          commentExistOrNotCheckRequest
        )
        .pipe(takeUntil(this._onDestroy))
        .subscribe(
          (result: any) => {
            if (result.err == 'N') {
              this.anyCommentExist = result.data;
            } else {
              console.log(
                'Error while checking any comment exist or not'
              );
            }
          },
          (error) => {
            console.log(
              'Error while hecking any comment exist or not' + error
            );
          }
        );
    }

    getStatusColor = (status) => {
      status = status ? status.toLowerCase() : '';
      if (status == 'open') return 'gray';
      else if (status == 'submitted') return '#ff9f1a';
      else if (status == 'approved') {
        return '#6ab04c';
      } else if (status == 'rejected') return '#cf0001';
      else if (status == 'closed') return 'green;';
    };
}
