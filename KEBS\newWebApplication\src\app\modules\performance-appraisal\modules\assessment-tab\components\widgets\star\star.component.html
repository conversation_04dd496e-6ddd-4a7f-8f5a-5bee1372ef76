<div class="row d-flex flex-column pt-2 pl-0">
    <!-- <div class="col-3 pt-2 pl-0 pr-0">
        <span *ngIf="metricName" class="default-font"
        [ngClass]="{'pl-2': getTextLength(metricName) < 6}" style="font-weight: 600;">{{metricName}}</span>
        <span *ngIf="!metricName" class="default-font" style="font-weight: 600;">
          {{starsClicked.length}} / {{totalStars}}
        </span>
    </div> -->
    <div class="p-0 col-12">
      <button mat-icon-button [disabled]="readOnly"
        [matTooltip]="stars.tooltipValue"
        *ngFor="let stars of ratingStars; let starIndex = index;" 
        (click)="onClick(starIndex)" >
        <mat-icon [ngStyle]="{'color' : getStarColor(stars.clicked)}">
          {{stars.clicked ? 'star' : 'star_border'}}
        </mat-icon>
      </button>
    </div>
    <div class="d-flex justify-content-center align-items-center"><span>{{selectedTooltipValue}}</span></div>
  </div>