import { Compo<PERSON>, <PERSON><PERSON>ni<PERSON>, On<PERSON><PERSON>roy } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';


//route
import { ActivatedRoute, Router } from '@angular/router';

//service
import { EmployeeAppraisalsService } from '../../../appraisal-home/services/employee_appraisal/employee-appraisals.service';
import { UdrfService } from 'src/app/services/udrf/udrf.service';
import { LoginService } from 'src/app/services/login/login.service'
import { UtilityService } from 'src/app/services/utility/utility.service';
import { ErrorService } from 'src/app/services/error/error.service';
import { SharedLazyLoadedComponentsService } from 'src/app/modules/shared-lazy-loaded-components/services/shared-lazy-loaded-components.service';
import { AppraisalApiUrlService } from "../../../../core/services/appraisal-api-url.service"
import { AppraisalEvaluatorsService } from '../../../appraisal-home/services/appraisal_evaluators/appraisal-evaluators.service';

//other imports
import { SubSink } from 'subsink';
import { Label } from '@amcharts/amcharts4/core';
import { Subject } from 'rxjs';
import * as _ from 'underscore';
import * as moment from 'moment';
import { takeUntil } from 'rxjs/operators';
import { EmployeeSize } from 'src/app/modules/account-sales/features/account-sales-detail/pipes/employeeSize.pipe';
import { ConstantPool } from '@angular/compiler';
import { Logger } from 'msal';

@Component({
  selector: 'app-assessment-tab',
  templateUrl: './assessment-tab.component.html',
  styleUrls: ['./assessment-tab.component.scss']
})
export class AssessmentTabComponent implements OnInit {

  //variables
  udrfData: any;
  oid: any;
  userId: any;

  //infinite scroll
  skip = 0;
  limit = 15;
  defaultDataRetrievalCount = 15;

  user_duration_start = moment();

  mainApiDateRangeEnd = moment();

  current_year_start = moment();

  current_year_end = moment();

  appraisalYear: any;
  appraisalYearFromFilter: any;


  dateFilters = [
    { name: "Overdue", date: { startLimit: moment('2018-01-01').format(), endLimit: moment().format(), clicked: false } },
    { name: "This Week", date: { startLimit: moment().startOf('week').format(), endLimit: moment().endOf('week').format(), clicked: false } },
    { name: "This Month", date: { startLimit: moment().startOf('month').format(), endLimit: moment().endOf('month').format(), clicked: false } },
    { name: "Next Month", date: { startLimit: moment().add(1, 'month').startOf('month').format(), endLimit: moment().add(1, 'month').endOf('month').format() }, clicked: false },
    { name: "Last Month", date: { startLimit: moment().subtract(1, 'month').startOf('month').format(), endLimit: moment().subtract(1, 'month').endOf('month').format() }, clicked: false },
    { name: "Upcoming 3 Months", date: { startLimit: moment().startOf('month').format(), endLimit: moment().add(2, 'month').endOf('month').format() }, clicked: false },
    { name: "This Year", date: { startLimit: moment().startOf('year').format(), endLimit: moment().endOf('year').format() }, clicked: false }
  ]


  //udrf specific data
  applicationId = 217;
  ItemDataCurrentIndex = 0;
  isLoading: boolean = false;
  cardIterator = [];
  selectedCard = [];
  inputData: any;
  CardClickedData: any;
  finalData: any;
  minNoOfVisibleSummaryCards = 3;
  maxNoOfVisibleSummaryCards = 6;
  isCardClicked: boolean = false;
  cardClicked = "";
  currentFinYear;

  totalScore = 0;

  durationRanges = [];

  protected _onDestroy = new Subject<void>();

  protected _onAppApiCalled = new Subject<void>();

  //subsink
  private subs = new SubSink();

  //summary cards
  dataTypeArray = [
    {
      dataType: "Reportee",
      dataTypeValue: "0",
      isActive: true,
      dataTypeCode: "EMP",
      isVisible: true,
      cardType: 'status',
      statusColor: '#0000FF',

    },
    {
      dataType: "Approved",
      dataTypeValue: "0",
      isActive: false,
      dataTypeCode: "A",
      isVisible: true,
      cardType: 'status',
      statusColor: '#009432',

    },
    {
      dataType: "Approval Pending",
      dataTypeValue: "0",
      isActive: false,
      dataTypeCode: "AP",
      isVisible: true,
      cardType: 'status',
      statusColor: '#ff7200',
    }
  ];

  responsedata: any;

  categorisedDataTypeArray = [
    {
      categoryType: "Appraisal Cards",
      categoryCardCodes: ["EMP", "A", "AP"],
      categoryCards: []
    }];

  //udrf body data
  udrfBodyColumns = [
    {
      item: 'employee_oid',
      header: 'Employee Name',
      isActive: true,
      isVisible: 'true',
      type: 'profileWithRoleAndDesignation',
      position: 1,
      colSize: 2,
      sortOrder: 'N',
      width: 200,
    },
    {
      item: 'appraisal_year_score',
      header: 'Score',
      isActive: true,
      isVisible: 'true',
      type: 'appraisal-score',
      textClass: 'text-center',
      position: 2,
      colSize: 2,
      sortOrder: 'N',
      width: 80,
    },
    {
      item: 'module_name',
      header: 'Module Name',
      isActive: true,
      isVisible: 'true',
      type: 'dialog',
      position: 9,
      colSize: 1,
      sortOrder: 'N',
      width: 200,
      hasColumnClick: true
    },
    {
      item: 'cycle_name',
      header: 'Cycle Name',
      isActive: true,
      isVisible: 'true',
      type: 'dialog',
      position: 3,
      colSize: 1,
      sortOrder: 'N',
      width: 200,
      hasColumnClick: true
    },
    {
      item: 'appraisal_year',
      header: 'Appraisal Year',
      isActive: true,
      isVisible: 'false',
      type: 'text',
      position: 5,
      colSize: 2,
      sortOrder: 'N',
      width: 200,
    },
    // {
    //   item: '',
    //   header: 'Approval',
    //   isActive: true,
    //   isVisible: 'true',
    //   type: 'star-rating',
    //   position: 4,
    //   colSize: 4,
    //   sortOrder: 'N',
    //   width: 150,
    // },
    {
      item: 'evaluator_status',
      header: 'Status',
      isActive: true,
      isVisible: 'true',
      type: 'status',
      position: 6,
      colSize: 1,
      sortOrder: 'N',
      width: 200,
    },
    {
      item: 'employee_oid',
      header: 'Employee Designation',
      isActive: true,
      isVisible: 'false',
      type: 'designation',
      position: 7,
      colSize: 1,
      sortOrder: 'N',
      width: 200,
    },
    {
      item: 'employee_oid',
      header: 'Employee Role',
      isActive: true,
      isVisible: 'false',
      type: 'role',
      position: 8,
      colSize: 1,
      sortOrder: 'N',
      width: 200,
    },
    
    {
      item: 'cycle_start_date',
      header: 'Start Date',
      isActive: true,
      isVisible: 'false',
      type: 'date',
      position: 10,
      colSize: 1,
      sortOrder: 'N',
      width: 200,
    },
    {
      item: 'cycle_end_date',
      header: 'End Date',
      isActive: true,
      isVisible: 'false',
      type: 'date',
      position: 11,
      colSize: 1,
      sortOrder: 'N',
      width: 200,
    },
    {
      item: '',
      header: 'Actions',
      isVisible: 'true',
      isActive: true,
      type: 'action',
      colSize: '2',
      textClass: 'value13light',
      position: 12,
      hasColumnClick: false,
      onColumnClick: '',
      width: 100,
    }
  ]

  udrfItemStatusColor = [
    {
      status: 'Approval Pending',
      color: '#ff7200',
    },
    {
      status: 'Approved',
      color: '#009432',
    }
  ];

  l2Manager: boolean = false;

  sampleData: any = [
    {
      employee_oid: "0054d5f4-60ac-4e63-8609-bb530287414e",
      children: [{
        appraisal_cycle_id: "60fe93aa0e5525001123e965",
        appraisal_module_id: "5fca0bd7e5e9e026e6d2e78e",
        cycle_end_date: "2022-03-30T18:30:00.000Z",
        cycle_name: "LDA for FY22",
        cycle_start_date: "2021-03-31T18:30:00.000Z",
        employee_oid: "0054d5f4-60ac-4e63-8609-bb530287414e",
        evaluator_status: "Approval Pending",
        module_name: "Key Performance Indicator",
        module_template: "skill_set"
      }
      ]
    }
  ]



  ratingStars: any = []

  totalStars: any = 5;

  ApproveBtnStatus:boolean = false;

  constructor(
    private _lazyService: SharedLazyLoadedComponentsService,
    public udrfService: UdrfService,
    private dialog: MatDialog,
    private utilityService: UtilityService,
    private _ErrorService: ErrorService,
    private empService: EmployeeAppraisalsService,
    private authService: LoginService,
    private router: Router,
    private activeroute: ActivatedRoute,
    private _url: AppraisalApiUrlService,
    private _AppraisalEvaluatorsService:AppraisalEvaluatorsService
  ) {
    this.current_year_start = moment().startOf('year');
    this.current_year_end = moment().endOf('year');
  }

  async ngOnInit() {

    this.ItemDataCurrentIndex = 0;

    this.udrfService.udrfBodyData = [];

    this._AppraisalEvaluatorsService.getCheckButtonConfig('Approve_button_config')
    .pipe(takeUntil(this._onDestroy))
    .subscribe(async(result:any)=>{

      this.ApproveBtnStatus = result?.data?.configuration_data;
    }, (err) =>{
      console.log(err);
      this._ErrorService.userErrorAlert(
        err.error.code,
        'Error occured while Checking button config',
        err.error.errMessage
      )

    })

    let today = moment();
    
    if (today.month() >= 3) {
      this.appraisalYear = today.format('YYYY')
      this.currentFinYear = today.format('YYYY')
    }
    else {
      this.appraisalYear = moment().subtract(1, 'years').format('YYYY')
      this.currentFinYear = moment().subtract(1, 'years').format('YYYY')
    }

    this.activeroute.parent.params.subscribe(
      (params) => {
        this.oid = params['oid'];
      });
    console.log(this.oid);
    if (this.oid == '1') {
      this.userId = this.authService.getProfile().profile.oid;
      // this.userId="4cb822d8-cec1-45b1-9d05-90bca00cb94d"
      console.log(this.userId);
    }
    else {
      let isAccessible;
      this._url.haveAccess(this.authService.getProfile().profile.oid).subscribe((res: Boolean) => {
        console.log(res);
        isAccessible = res['data'];
        if (isAccessible == false) {
          this.utilityService.showMessage("You Dont Have Access! Contact KEBS Support.", "dismiss", 2000)
          this.router.navigateByUrl("/main/pms/appraisal/home");
          //this.userId = this.oid;

        }
        else if (isAccessible == true) {
          this.userId = this.oid;
        }
        // resolve(res['data']);
      }, err => {
        //reject(err)
        console.log(err);
      })
      // let isAccessible = <boolean>await this.checkAccess();
      // if(isAccessible == false)
      // {
      //   this.utilityService.showMessage("You Dont Have Access! Contact KEBS Support.","dismiss",2000)
      //   this.router.navigateByUrl("/main/pms/appraisal/home");
      //   //this.userId = this.oid;

      // }
      // else if (isAccessible == true){
      //      this.userId = this.oid;
      // }
      // this.userId = this.oid;
    }

    this.udrfService.udrfData.applicationId = this.applicationId;
    //this.userId = this.authService.getProfile().profile.oid;
    //this.userId="4ca8b32c-55f8-40de-8252-1057f599f2f8"
    //this.userId="ceddd627-0f2c-4f70-bc72-321f59393cf3"
    //this.userId="d5aa6990-2861-4d57-b6fa-32e717c361dd"
    //this.userId = "4cb822d8-cec1-45b1-9d05-90bca00cb94d"
    //this.userId ="cbaa4bec-f0c5-431f-b186-88ff4bb822d7"
    //this.userId=this.oid
    //this.userId = "c7a3f10f-07dd-42fa-8652-1388197e4073"
    this.udrfService.udrfUiData.showNewReleasesButton = false;
    this.udrfService.udrfUiData.showItemDataCount = false
    this.udrfService.udrfUiData.itemDataType = ""
    this.udrfService.udrfUiData.totalItemDataCount = 0
    this.udrfService.udrfUiData.showSearchBar = true;
    this.udrfService.udrfUiData.showActionButtons = true;
    this.udrfService.udrfUiData.showUdrfModalButton = true;
    this.udrfService.udrfUiData.showColumnConfigButton = true;
    this.udrfService.udrfUiData.isReportDownloading = false;
    this.udrfService.udrfUiData.showReportDownloadButton = false;
    this.udrfService.udrfUiData.showColumnConfigButton = true;
    this.udrfService.udrfUiData.showSettingsModalButton = false;
    this.udrfService.udrfUiData.itemHasStarRating = true;
    
    this.udrfService.udrfUiData.resolveVisibleSummaryCards = this.resolveVisibleDataTypeArray.bind(this)
    this.udrfService.udrfUiData.summaryCardsSelected = this.dataTypeCardSelected.bind(this)
    this.udrfService.udrfUiData.openComments = this.openComments.bind(this);
    this.udrfService.udrfUiData.summaryCardsItem = {}
    this.udrfService.udrfUiData.inlineEditData = {}
    this.udrfService.udrfUiData.udrfItemStatusColor = this.udrfItemStatusColor
    //this.udrfService.udrfUiData.downloadItemDataReport = () => { }
    this.udrfService.udrfUiData.itemDataScrollDown = this.onItemDataScrollDown.bind(this)
    // this.udrfService.udrfUiData.itemcardSelected = this.okrCardClicked.bind(this)
    this.udrfService.udrfUiData.summaryCards = this.dataTypeArray
    // this.udrfService.udrfUiData.udrfBodyColumns = this.udrfBodyColumns
    this.udrfService.udrfUiData.udrfVisibleBodyColumns = this.udrfService.udrfUiData.udrfVisibleBodyColumns
    this.udrfService.udrfUiData.udrfInvisibleBodyColumns = this.udrfService.udrfUiData.udrfInvisibleBodyColumns
    this.udrfService.udrfUiData.categorisedSummaryCards = this.categorisedDataTypeArray
    this.udrfService.udrfUiData.minNoOfVisibleSummaryCards = this.minNoOfVisibleSummaryCards
    this.udrfService.udrfUiData.maxNoOfVisibleSummaryCards = this.maxNoOfVisibleSummaryCards
    this.udrfService.udrfUiData.selectedCard = this.selectedCard
    this.udrfService.udrfUiData.variant = 4
    this.udrfService.udrfUiData.collapseAll = false;
    this.udrfService.udrfUiData.showCollapseButton = true;
    this.udrfService.udrfUiData.horizontalScroll = true;
    this.udrfService.udrfUiData.dialogFunction = this.openDialog.bind(this);
    this.udrfService.udrfUiData.itemHasDialogDataBtn = true;
    this.udrfService.udrfUiData.starRating = this.onStarClick.bind(this);
    this.udrfService.udrfUiData.itemHasMeetingInviteBtn = true;
    this.udrfService.udrfUiData.itemHasComments = true;
    this.udrfService.udrfUiData.openMeetingInviteClicked = this.openMeetingInviteModal.bind(this);

   
    let durationRanges = [];
    let response: any = await this.empService.getAllAppraisalYear();
   
    for (let items of response) {
      durationRanges.push({
        checkboxId: items.id,
        checkboxName: items.name,
        checkboxStartValue: '',
        checkboxEndValue: '',
        isCheckboxDefaultSelected: false,
      });
    }
    
    this.udrfService.udrfFunctions.constructCustomRangeData(
      1,
      'date',
      durationRanges
    );
    this.udrfService.udrfUiData.udrfBodyColumns = this.udrfBodyColumns
    this.udrfService.getAppUdrfConfig(this.applicationId, this.initReport.bind(this));
  }

  checkAccess = () => {
    return new Promise((resolve, reject) => {
      this._url.haveAccess(this.authService.getProfile().profile.oid).subscribe((res: Boolean) => {
        console.log(res);
        resolve(res['data']);
      }, err => {
        reject(err)
      })
    })

  }

  async initReport() {

    this._onAppApiCalled.next();
    this.ItemDataCurrentIndex = 0;
    this.udrfService.udrfBodyData = [];
    // for (let i = 0; i < this.dataTypeArray.length; i++) {
    //   this.dataTypeArray[i].isActive = false;
    // }
    this.isCardClicked = false;
    this.cardClicked = "";
    this.udrfService.udrfUiData.resolveColumnConfig();

    this.skip = 0;
    this.limit = this.defaultDataRetrievalCount;
    //this.cardClicked = "Employee"
    await this.getEmployeeList(false);


  }


  async getEmployeeList(infiniScrollFlag) {

    let durationStartDate, durationEndDate, endDateStartDate, endDateEndDate;

    let hasIsActive = _.where(this.dataTypeArray, { isActive: true });

    

    let mainFilterArray = JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray));

    // let dateLogic = this.getDateLogic(mainFilterArray);

    // let appr = this.getYear(mainFilterArray);
    // // this.setYear(mainFilterArray)

    // console.log(appr)

    // this.appraisalYear = appr == undefined ? this.appraisalYear : appr;
   

    // console.log(this.appraisalYear)



    let activeSummaryCard = _.where(this.dataTypeArray, { isActive: true });

   
    let activeStatusSummaryCard = [];

    let statusApplied = false;

    // durationStartDate = dateLogic.durationStartDate;

    //durationEndDate = dateLogic.durationEndDate;

    // endDateStartDate = dateLogic.endDateStartDate;

    //endDateEndDate = dateLogic.endDateEndDate

    if (activeSummaryCard.length > 0 && activeSummaryCard[0].dataType != 'Reportee') {

      activeStatusSummaryCard = _.where(activeSummaryCard, { cardType: "status" });
      console.log("active", activeSummaryCard)
      if (activeStatusSummaryCard.length > 0)

        if (mainFilterArray.length > 0) {

          for (let mainFilterArrayItem of mainFilterArray) {
            if (mainFilterArrayItem.filterName == "EvlStatus") {

              mainFilterArrayItem.multiOptionSelectSearchValues = [activeSummaryCard[0].dataType];

              statusApplied = true;

            }
          }
          if (statusApplied == false) {

            let statusArray = JSON.parse(JSON.stringify(_.where(this.udrfService.udrfData.filterTypeArray, { filterName: "EvlStatus" })));

            mainFilterArray.push(statusArray[0])
            for (let mainFilterArrayItem of mainFilterArray) {
              if (mainFilterArrayItem.filterName == "EvlStatus") {

                mainFilterArrayItem.multiOptionSelectSearchValues = [activeSummaryCard[0].dataType];

                statusApplied = true;
              }
            }
            console.log(mainFilterArray);
          }
        }

        else {

          mainFilterArray = JSON.parse(JSON.stringify(_.where(this.udrfService.udrfData.filterTypeArray, { filterName: "EvlStatus" })));

          console.log(this.udrfService.udrfData.filterTypeArray, mainFilterArray)
          mainFilterArray[0].multiOptionSelectSearchValues = [activeSummaryCard[0].dataType];

          console.log(mainFilterArray);

        }

    }

    let filters = this.getFilters(mainFilterArray);

    


    // if (this.appraisalYear == this.currentFinYear) {
    //   let defaultYear = this.setYear(this.udrfService.udrfData.filterTypeArray)
    //   let appraisalYearFilter = _.where(filters, { filterName: "Appraisal Year" })
    //   console.log(appraisalYearFilter)
    //   if (appraisalYearFilter == undefined || appraisalYearFilter.length == 0) {
    //     filters.push({
    //       filterName: "Appraisal Year",
    //       valueId: [Number(defaultYear)]
    //     });
    //   }
    //   //console.log(appraisalYearFilter)
    // }
    //  else{

    //  }
   
  
    //this.appraisalYearFromFilter = appr || moment().format("YYYY");
    // this.appraisalYearFromFilter = appr == undefined ? this.appraisalYear : appr;
   

    let filterConfig = {
      skip: this.skip,
      limit: this.limit,
      filter: filters,
      mainSearchParameter: this.udrfService.udrfData.mainSearchParameter,
      cardData: null,
      startDate: this.udrfService.udrfData.mainApiDateRangeStart,
      endDate: this.udrfService.udrfData.mainApiDateRangeEnd,
      //durationStartDate : durationStartDate,
      //durationEndDate: durationEndDate,
      //endDateStartDate: endDateStartDate,
      //endDateEndDate: endDateEndDate
    }
    filterConfig.cardData = this.CardClickedData;
    this.selectedCard = this.udrfService.udrfData.udrfSummaryCardCodes;
   
    if( !_.contains(_.pluck(_.where(filterConfig.filter,{'filterName':'Appraisal Year'}),'filterName'),'Appraisal Year')){
     
      let defaultYear = this.setYear(this.udrfService.udrfData.filterTypeArray)
       filterConfig.filter.push({
          filterName: "Appraisal Year",
          valueId: [Number(defaultYear)]
        });
      
       
    
        this.appraisalYear = Number(defaultYear);
       
    
        this.appraisalYearFromFilter = String(defaultYear);

      
    }

    else{
    let filterArray = JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray));

   

    let appr = this.getYear(filterArray);
    // this.setYear(mainFilterArray)

  

    this.appraisalYear = appr == undefined ? this.appraisalYear : appr;
   
   

    this.appraisalYearFromFilter = appr == undefined ? String(this.appraisalYear) : String(appr);
    }

    this.empService.getEmployeeList(filterConfig, this.userId).pipe(takeUntil(this._onDestroy)).pipe(takeUntil(this._onAppApiCalled))
      .subscribe(
        async (res) => {

          if (
            res['err'] == 'N' &&
            res['data'] &&
            res['data'].length > 0
          ) {

            if (!this.isCardClicked) {
              console.log("if check", this.isCardClicked)

              _.each(res['data'], i => {
                i.expanded = this.udrfService.udrfUiData.collapseAll
              })
              console.log(this.udrfService.udrfBodyData);
              this.udrfService.udrfBodyData = this.udrfService.udrfBodyData.concat(res['data']);
              console.log(this.udrfService.udrfBodyData);
              this.formatList(this.udrfService.udrfBodyData, true);
              //this.udrfBodyColumns = [];
              //   if(this.l2Manager == true){
              //     console.log('hi')
              //     for(let i=0;i<this.udrfBodyColumns.length;i++){
              //       if(this.udrfBodyColumns[i]['header']=='Ratings')
              //         this.udrfBodyColumns[i]['isVisible']='false'
              //     }
              // }
              // else{
              //   console.log('hi')
              //   for(let i=0;i<this.udrfBodyColumns.length;i++){
              //     if(this.udrfBodyColumns[i]['header']=='Ratings')
              //       this.udrfBodyColumns[i]['isVisible']='true'
              //   }
              // }
              // this.udrfService.udrfUiData.udrfBodyColumns = this.udrfBodyColumns
              if (!infiniScrollFlag)
                await this.initCard()

              // !this.udrfService.udrfData.mainSearchParameter ? this.initOKRCard() : "";

            }

            else {
              console.log("else check", this.isCardClicked);
              _.each(res['data'], i => {
                i.expanded = this.udrfService.udrfUiData.collapseAll
              })

              this.udrfService.udrfBodyData = this.udrfService.udrfBodyData.concat(res['data']);
              console.log(this.udrfService.udrfBodyData);
              this.formatList(this.udrfService.udrfBodyData, true);
              //   if(this.l2Manager == true){
              //     console.log('hi')
              //     for(let i=0;i<this.udrfBodyColumns.length;i++){
              //       if(this.udrfBodyColumns[i]['header']=='Ratings')
              //         this.udrfBodyColumns[i]['isVisible']='false'
              //     }
              // }
              // else{
              //   console.log('hi')
              //   for(let i=0;i<this.udrfBodyColumns.length;i++){
              //     if(this.udrfBodyColumns[i]['header']=='Ratings')
              //       this.udrfBodyColumns[i]['isVisible']='true'
              //   }
              // }
              // this.udrfService.udrfUiData.udrfBodyColumns = this.udrfBodyColumns
            }

          } else {
            if (!infiniScrollFlag)
              await this.initCard();
            this.udrfService.udrfBodyData = this.udrfService.udrfBodyData.concat([]);

            this.udrfService.udrfData.noItemDataFound = true;
          }

          this.udrfService.udrfData.isItemDataLoading = false;

        }, (err) => {
          //this.showErrorMessage(err);
          this._ErrorService.userErrorAlert(
            err.error.code,
            'Some Error Happened in completing the Activity',
            err.error.errMessage
          );
        });

    // this.udrfService.udrfBodyData=this.sampleData
    // this.formatList(this.udrfService.udrfBodyData, true);
    // this.udrfService.udrfData.isItemDataLoading = false;

  }

  async initCard() {
    let EmployeeCount = await this.getEmployeeCount();
    let ApprovedCount = await this.getApprovedCount();
    let ApprovalPendingCount = await this.getPendingForApprovalCount();


  }

  async getEmployeeCount() {
    let durationStartDate: any, durationEndDate: any, endDateStartDate: any, endDateEndDate: any;
    let mainFilterArray = JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray));
    // let dateLogic = this.getDateLogic(mainFilterArray);

    // durationStartDate = dateLogic.durationStartDate;

    // durationEndDate = dateLogic.durationEndDate;

    // endDateStartDate = dateLogic.endDateStartDate;

    // endDateEndDate = dateLogic.endDateEndDate

    // let appr = this.getYear(mainFilterArray);
    // // this.setYear(mainFilterArray)

    // console.log(appr)

    // this.appraisalYear = appr == undefined ? this.appraisalYear : appr;

    // console.log(this.appraisalYear)




    let filters = this.getFilters(mainFilterArray);

    // if (this.appraisalYear == this.currentFinYear) {
    //   let defaultYear = this.setYear(this.udrfService.udrfData.filterTypeArray)
    //   let appraisalYearFilter = _.where(filters, { filterName: "Appraisal Year" })
    //   console.log(appraisalYearFilter)
    //   if (appraisalYearFilter == undefined || appraisalYearFilter.length == 0) {
    //     filters.push({
    //       filterName: "Appraisal Year",
    //       valueId: [Number(defaultYear)]
    //     });
    //   }
    //   //console.log(appraisalYearFilter)
    // }

    // filters.push({
    //   filterName: "Card Status",
    //   valueId: ["Rejected"]
    // });
    let filterConfig = {
      skip: this.skip,
      limit: this.limit,
      filter: filters,
      startDate: this.udrfService.udrfData.mainApiDateRangeStart,
      endDate: this.udrfService.udrfData.mainApiDateRangeEnd,
      mainSearchParameter: this.udrfService.udrfData.mainSearchParameter,
      // durationStartDate: durationStartDate,
      // durationEndDate: durationEndDate,
      // endDateStartDate: endDateStartDate,
      // endDateEndDate: endDateEndDate
    }

    if( !_.contains(_.pluck(_.where(filterConfig.filter,{'filterName':'Appraisal Year'}),'filterName'),'Appraisal Year')){
     
      let defaultYear = this.getYear(this.udrfService.udrfData.filterTypeArray)
       filterConfig.filter.push({
          filterName: "Appraisal Year",
          valueId: [Number(defaultYear)]
        });
      
    }


    this.empService.getAssesmentCard(filterConfig, this.userId).pipe(takeUntil(this._onDestroy)).pipe(takeUntil(this._onAppApiCalled))
      .subscribe(async res => {

        if (this.udrfService.udrfData.udrfSummaryCardCodes.length > 0) {
          console.log(this.udrfService.udrfData.udrfSummaryCardCodes)
          this.resolveVisibleDataTypeArray();
        }

        if (
          res['err'] == 'N' &&
          res['data'] &&
          res['data'].length > 0
        ) {

          for (let i = 0; i < this.dataTypeArray.length; i++) {
            // this.dataTypeArray[i].isActive = false;
            if (this.dataTypeArray[i].dataType == "Reportee") {
              this.dataTypeArray[i].dataTypeValue = res["data"][0].l1
            }
          }

          this.udrfService.udrfUiData.summaryCards = this.dataTypeArray
          console.log(this.udrfService.udrfUiData.summaryCards, "summarycard Data");
        }

        else {

          for (let i = 0; i < this.dataTypeArray.length; i++) {
            // this.dataTypeArray[i].isActive = false;
            if (this.dataTypeArray[i].dataType == "Reportee") {
              this.dataTypeArray[i].dataTypeValue = "0"
            }
          }

          this.udrfService.udrfUiData.summaryCards = this.dataTypeArray
        }
      }, err => {
        //this.showErrorMessage(err);
        this._ErrorService.userErrorAlert(
          err.error.code,
          'Some Error Happened in completing the Activity',
          err.error.errMessage
        );
      });
  }

  async getApprovedCount() {
    let durationStartDate: any, durationEndDate: any, endDateStartDate: any, endDateEndDate: any;
    let mainFilterArray = JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray));
    //let dateLogic = this.getDateLogic(mainFilterArray);

    // durationStartDate = dateLogic.durationStartDate;

    // durationEndDate = dateLogic.durationEndDate;

    // endDateStartDate = dateLogic.endDateStartDate;

    // endDateEndDate = dateLogic.endDateEndDate


    // let appr = this.getYear(mainFilterArray);
    // // this.setYear(mainFilterArray)

    // console.log(appr)

    // this.appraisalYear = appr == undefined ? this.appraisalYear : appr;

    // console.log(this.appraisalYear)

    let filters = this.getFilters(mainFilterArray);

    // if (this.appraisalYear == this.currentFinYear) {
    //   let defaultYear = this.setYear(this.udrfService.udrfData.filterTypeArray)
    //   let appraisalYearFilter = _.where(filters, { filterName: "Appraisal Year" })
    //   console.log(appraisalYearFilter)
    //   if (appraisalYearFilter == undefined || appraisalYearFilter.length == 0) {
    //     filters.push({
    //       filterName: "Appraisal Year",
    //       valueId: [Number(defaultYear)]
    //     });
    //   }
    //   //console.log(appraisalYearFilter)
    // }

    filters.push({
      filterName: "EvlStatus",
      valueId: ["Approved"]
    });
    let filterConfig = {
      skip: this.skip,
      limit: this.limit,
      filter: filters,
      startDate: this.udrfService.udrfData.mainApiDateRangeStart,
      endDate: this.udrfService.udrfData.mainApiDateRangeEnd,
      mainSearchParameter: this.udrfService.udrfData.mainSearchParameter,
      // durationStartDate: durationStartDate,
      // durationEndDate: durationEndDate,
      // endDateStartDate: endDateStartDate,
      // endDateEndDate: endDateEndDate
    }

    if( !_.contains(_.pluck(_.where(filterConfig.filter,{'filterName':'Appraisal Year'}),'filterName'),'Appraisal Year')){
     
      let defaultYear = this.getYear(this.udrfService.udrfData.filterTypeArray)
       filterConfig.filter.push({
          filterName: "Appraisal Year",
          valueId: [Number(defaultYear)]
        });
      
    }
    this.empService.getAssesmentCard(filterConfig, this.userId).pipe(takeUntil(this._onDestroy)).pipe(takeUntil(this._onAppApiCalled))
      .subscribe(async res => {

        if (this.udrfService.udrfData.udrfSummaryCardCodes.length > 0) {
          console.log(this.udrfService.udrfData.udrfSummaryCardCodes)
          this.resolveVisibleDataTypeArray();
        }

        if (
          res['err'] == 'N' &&
          res['data'] &&
          res['data'].length > 0
        ) {

          for (let i = 0; i < this.dataTypeArray.length; i++) {
            // this.dataTypeArray[i].isActive = false;
            if (this.dataTypeArray[i].dataType == "Approved") {
              this.dataTypeArray[i].dataTypeValue = res["data"][0].l2
            }
          }

          this.udrfService.udrfUiData.summaryCards = this.dataTypeArray
          console.log(this.udrfService.udrfUiData.summaryCards, "summarycard Data");
        }

        else {

          for (let i = 0; i < this.dataTypeArray.length; i++) {
            // this.dataTypeArray[i].isActive = false;
            if (this.dataTypeArray[i].dataType == "Approved") {
              this.dataTypeArray[i].dataTypeValue = "0"
            }
          }

          this.udrfService.udrfUiData.summaryCards = this.dataTypeArray
        }
      }, err => {
        //this.showErrorMessage(err);
        this._ErrorService.userErrorAlert(
          err.error.code,
          'Some Error Happened in completing the Activity',
          err.error.errMessage
        );
      });
  }

  async getPendingForApprovalCount() {
    let durationStartDate: any, durationEndDate: any, endDateStartDate: any, endDateEndDate: any;
    let mainFilterArray = JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray));
    // let dateLogic = this.getDateLogic(mainFilterArray);

    // durationStartDate = dateLogic.durationStartDate;

    // durationEndDate = dateLogic.durationEndDate;

    // endDateStartDate = dateLogic.endDateStartDate;

    // endDateEndDate = dateLogic.endDateEndDate


    // let appr = this.getYear(mainFilterArray);
    // // this.setYear(mainFilterArray)

    // console.log(appr)

    // this.appraisalYear = appr == undefined ? this.appraisalYear : appr;

    // console.log(this.appraisalYear)


    let filters = this.getFilters(mainFilterArray);

    // if (this.appraisalYear == this.currentFinYear) {
    //   let defaultYear = this.setYear(this.udrfService.udrfData.filterTypeArray)
    //   let appraisalYearFilter = _.where(filters, { filterName: "Appraisal Year" })
    //   console.log(appraisalYearFilter)
    //   if (appraisalYearFilter == undefined || appraisalYearFilter.length == 0) {
    //     filters.push({
    //       filterName: "Appraisal Year",
    //       valueId: [Number(defaultYear)]
    //     });
    //   }
    //   //console.log(appraisalYearFilter)
    // }

    filters.push({
      filterName: "EvlStatus",
      valueId: ["Approval Pending"]
    });

    let filterConfig = {
      skip: this.skip,
      limit: this.limit,
      filter: filters,
      startDate: this.udrfService.udrfData.mainApiDateRangeStart,
      endDate: this.udrfService.udrfData.mainApiDateRangeEnd,
      mainSearchParameter: this.udrfService.udrfData.mainSearchParameter,
      // durationStartDate: durationStartDate,
      // durationEndDate: durationEndDate,
      // endDateStartDate: endDateStartDate,
      // endDateEndDate: endDateEndDate
    }

    if( !_.contains(_.pluck(_.where(filterConfig.filter,{'filterName':'Appraisal Year'}),'filterName'),'Appraisal Year')){
     
      let defaultYear = this.getYear(this.udrfService.udrfData.filterTypeArray)
       filterConfig.filter.push({
          filterName: "Appraisal Year",
          valueId: [Number(defaultYear)]
        });
      
    }

    this.empService.getAssesmentCard(filterConfig, this.userId).pipe(takeUntil(this._onDestroy)).pipe(takeUntil(this._onAppApiCalled))
      .subscribe(async res => {

        if (this.udrfService.udrfData.udrfSummaryCardCodes.length > 0) {
          console.log(this.udrfService.udrfData.udrfSummaryCardCodes)
          this.resolveVisibleDataTypeArray();
        }

        if (
          res['err'] == 'N' &&
          res['data'] &&
          res['data'].length > 0
        ) {

          for (let i = 0; i < this.dataTypeArray.length; i++) {
            // this.dataTypeArray[i].isActive = false;
            if (this.dataTypeArray[i].dataType == "Approval Pending") {
              this.dataTypeArray[i].dataTypeValue = res["data"][0].l2
            }
          }

          this.udrfService.udrfUiData.summaryCards = this.dataTypeArray
          console.log(this.udrfService.udrfUiData.summaryCards, "summarycard Data");
        }

        else {

          for (let i = 0; i < this.dataTypeArray.length; i++) {
            // this.dataTypeArray[i].isActive = false;
            if (this.dataTypeArray[i].dataType == "Approval Pending") {
              this.dataTypeArray[i].dataTypeValue = "0"
            }
          }

          this.udrfService.udrfUiData.summaryCards = this.dataTypeArray
        }
      }, err => {
        //this.showErrorMessage(err);
        this._ErrorService.userErrorAlert(
          err.error.code,
          'Some Error Happened in completing the Activity',
          err.error.errMessage
        );
      });
  }


  formatList(taskList, showTask) {

    for (let item of taskList) {

      item['loadChildTask'] = false;

      this.l2Manager = true

      item['showTask'] = showTask;
      if (showTask == false) {
        item['parent'] = false;
        if (item['evaluator_level'] != 1 && item['evaluator_level'] != null) {
          let starsSelected = 0;
          this.l2Manager = false;

          if (item['rating_awarded'])
            this.totalScore = item['rating_awarded']
          else
            this.totalScore = 0

          if (this.totalScore)
            starsSelected = (this.totalScore / 100) * this.totalStars;

          starsSelected = Math.round(starsSelected);

          this.ratingStars = [];

          for (let i = 0; i < this.totalStars; i++) {
            this.ratingStars.push({
              starValue: i + 1,
              clicked: starsSelected > 0 && starsSelected >= i + 1 ? true : false
            });
          }

          item['ratingStars'] = this.ratingStars
        }
      }
      else
        item['parent'] = true;

      if (item['children']?.length > 0)
        this.formatList(item['children'], false);

    }


    console.log(this.udrfService.udrfBodyData)
  }

  async onItemDataScrollDown() {
    if (!this.udrfService.udrfData.noItemDataFound) {
      if (!this.udrfService.udrfData.isItemDataLoading) {
        // this.OKRItemDataCurrentIndex += this.defaultDataRetrievalCount;

        this.skip += this.defaultDataRetrievalCount;

        this.udrfService.udrfData.isItemDataLoading = true;

        await this.getEmployeeList(true);

      }
    }

  }

  async resolveVisibleDataTypeArray() {
    for (let summaryCardsItem of this.udrfService.udrfUiData.summaryCards) {

      let isVisible;

      if (this.udrfService.udrfData.udrfSummaryCardCodes.length > 0) {

        isVisible = _.contains(this.udrfService.udrfData.udrfSummaryCardCodes, summaryCardsItem.dataTypeCode);
        summaryCardsItem.isVisible = isVisible;
        if (isVisible) {
          this.udrfService.udrfUiData.summaryCardsItem = summaryCardsItem;

        }
      }

    }

  }



  dataTypeCardSelected = () => {

    this.udrfService.udrfData.isItemDataLoading = true;
    this.udrfService.udrfData.noItemDataFound = false;

    let dataTypeArrayItem = this.udrfService.udrfUiData.summaryCardsItem;

    for (let i = 0; i < this.dataTypeArray.length; i++)
      if (dataTypeArrayItem["dataTypeCode"] == this.dataTypeArray[i].dataTypeCode) {

        this.dataTypeArray[i].isActive = true;
        dataTypeArrayItem["isActive"] = true;

      }
      else {

        let summaryCard = _.where(this.udrfService.udrfUiData.summaryCards, { dataTypeCode: this.dataTypeArray[i].dataTypeCode });

        if (summaryCard.length > 0)
          summaryCard[0].isActive = false;

        this.dataTypeArray[i].isActive = false;

      }

    this.isCardClicked = true;

    this.cardClicked = dataTypeArrayItem["dataType"];

    dataTypeArrayItem["isActive"] == true ? this.CardClickedData = dataTypeArrayItem : this.CardClickedData = null;

    this.ItemDataCurrentIndex = 0;
    this.udrfService.udrfBodyData = [];
    this.skip = 0;
    this.limit = this.defaultDataRetrievalCount;

    this.getEmployeeList(false);

  }

  showErrorMessage(err) {

    let errReportingTeams = "KEBS";

    this.utilityService.showErrorMessage("Error:Unable to fetch Data", errReportingTeams);

  }


  openDialog() {
    let data = this.udrfService.udrfUiData.dialogData;
    console.log(data,"hello user123");
    // if (data['is_popup'] == true){

    //let data1=this.getBodyData(this.udrfService.udrfBodyData,data,'',data['parent_id'],'')
    // console.log(data1)
    // let req={
    //   emp_oid: data['employee_oid'],
    //   appraisal_year: this.appraisalYear,
    //   module_id: data['appraisal_module_id'],
    //   cycle_id: data['appraisal_cycle_id']
    // }

    let req = {
      "appraisal_year": this.appraisalYearFromFilter,
      "employee_oid": data['employee_oid'],
      "appraisal_cycle_id": data['appraisal_cycle_id'],
      "appraisal_module_id": data['appraisal_module_id'],
      "template": data['module_template'],
      "eval_oid": this.userId
    }
    console.log(req);
   
      this.empService.getEvalPopupData(req).subscribe(res => {
        //console.log(res)
        this.responsedata = res['data']
        console.log(this.responsedata);
        if (this.responsedata.length > 0) {
          this.openListCycleDialog(this.responsedata, data['employee_oid'], data['cycle_name'], data['module_template'], this.userId, this.appraisalYearFromFilter, data['appraisal_cycle_id'], data['appraisal_module_id'],res)
        }
        else {
          this.utilityService.showMessage("No Data Found", "Dismiss");
        }
      }, (err) => {
        //this.showErrorMessage(err);
        this._ErrorService.userErrorAlert(
          err.error.code,
          'Some Error Happened in completing the Activity',
          err.error.errMessage
        );
      })
    

  }

  getBodyData(bodyData, inlineData, childId, parentId, mappedId) {

    for (let item of bodyData) {
      if (item['parent_id'] == parentId)
        return item
    }


  }



  openListCycleDialog = async (evalDetail, employeename, cyclename, templateName, evaloid, appraisalYear, cycleId, moduleId,response) => {    
    const { EvalPopupComponent } = await import("src/app/modules/performance-appraisal/modules/assessment-tab/components/eval-popup/eval-popup.component")
    const dialog = this.dialog.open(EvalPopupComponent, {
      width: '100%',
      height: '100%',
      maxWidth: '100vw',
      maxHeight: '100vh',
      autoFocus: false,
      panelClass: 'trend-dialog',
      data: {
        evalPopupDetails: evalDetail,
        l2EvalPopupDetails: response?.l2EvalResultdata ? response?.l2EvalResultdata : [],
        evaluationData: [],
        elaResArray: [],
        cycleName: cyclename,
        employeename: employeename,
        templateName: templateName,
        evaluatorId: evaloid,
        appraisalYear: appraisalYear,
        cycleId: cycleId,
        moduleId: moduleId,
        isPreRequisiteRequired : response.isPreRequisiteRequired ? response.isPreRequisiteRequired : false ,
        isPreRequisiteSatisified : response.isPreRequisiteSatisified ? response.isPreRequisiteSatisified : false,
        isSelfEvalRequired : response.isSelfEvalRequired ? response.isSelfEvalRequired : false,
        acknowledgeStatus: response.acknowledgeStatus ? response.acknowledgeStatus : false,
        isEditable : response.is_editable ? response.is_editable : false,
        isMeetingScheduled:response.isMeetingScheduled ? response.isMeetingScheduled : false,
        meetingNotes:response.meetingNotes ? response.meetingNotes : '',
        promotionRecommendation:response.promotionRecommendation ? response.promotionRecommendation : '',
        isPromotionRecommended:response.isPromotionRecommended ? response.isPromotionRecommended : false,
        overallFeedback:response.overallFeedback ? response.overallFeedback : '',
        attachmentBucket:response.attachmentBucket ? response.attachmentBucket : {destination_bucket:"kebs-appraisal",routing_key:"appraisal"},
        allowEvalRating:response.allowEvalRating ? response.allowEvalRating : false,
        evalRatingStartDate:response.evalRatingStartDate ? response.evalRatingStartDate: null,
        evalRatingEndDate:response.evalRatingEndDate ? response.evalRatingEndDate: null,
        toolTipValueForStarRating: response?.appraisaloduleDetails?.display_appraiser_comments ? response.toolTipValueForStarRating ? response.toolTipValueForStarRating : [
          "E - Poor - 0","D - Needs Improvement - 25","C - Meets Expectation - 50","B - Good - 70","A - Exceptional - 100"
        ] : ["Needs Improvement","Good","Exceptional"],
        allowedManagerToEditEvaluation:response.allowedManagerToEditEvaluation ? response.allowedManagerToEditEvaluation : false,
        l2EvalApprovedAll:response.l2EvalApprovedAll ? response.l2EvalApprovedAll : false,
        appraisaloduleDetails:response?.appraisaloduleDetails,
        l2EvaluatorOid : response?.l2EvalOid ? response?.l2EvalOid : [],
      }
    })
    dialog.afterClosed().subscribe(res => {
      
      //this.getEmployeeList(false)
      this.initCard()

      if(res.status=="Approved")
        this.udrfService.udrfUiData.dialogData["evaluator_status"] = res.status;

    });

  }

  getFilters(mainFilterArray) {
    let filters = [];

    for (let items of mainFilterArray) {
      if (items.isIdBased) {
        filters.push({
          filterName: items.filterName,
          valueId: items.multiOptionSelectSearchValuesWithId
        });
      }
      else if (items.filterId == 1) {
        for (let checkboxData of items.checkboxValues) {
          if (checkboxData.isCheckboxSelected) {
            filters.push({
              filterName: items.filterName,
              valueId: [Number(checkboxData.checkboxName)]
            });
          }
        }
      }
      else {
        filters.push({
          filterName: items.filterName,
          valueId: items.multiOptionSelectSearchValues
        });
      }
    }
    return filters;
  }

  getYear(mainFilterArray) {
    console.log(mainFilterArray);

    let year;

    for (let items of mainFilterArray) {
      if (items.filterId == 1) {
        let checkbox = _.where(items.checkboxValues, { isCheckboxSelected: true });
        console.log(checkbox);
        year = checkbox[0].checkboxName;

      }
    }
    console.log(year);

    return year
  }

  setYear(mainFilterArray) {
    console.log(mainFilterArray);

    let year;

    for (let items of mainFilterArray) {
      if (items.filterId == 1) {
        let checkbox = _.where(items.checkboxValues, { checkboxName: this.currentFinYear });
        console.log(checkbox);
        year = checkbox[0].checkboxName;
        checkbox[0].isCheckboxSelected = true;
      }
    }
    console.log(year);

    return year
  }


  // getDateLogic(mainFilterArray)
  // {
  //   let filterDate = {
  //     durationStartDate: "",
  //     durationEndDate: "",
  //     endDateStartDate: "",
  //     endDateEndDate: ""
  //   }

  //   console.log(mainFilterArray);

  //   for(let items of mainFilterArray)
  //   {
  //     if(items.filterId == 11)
  //     {
  //       let checkbox = _.where(items.checkboxValues, { isCheckboxSelected: true });
  //       filterDate.endDateStartDate = checkbox[0].checkboxStartValue;
  //       filterDate.endDateEndDate = checkbox[0].checkboxEndValue;

  //     }
  //     else if(items.filterId == 12)
  //     {
  //       let checkbox = _.where(items.checkboxValues, { isCheckboxSelected: true });
  //       filterDate.durationStartDate = checkbox[0].checkboxStartValue;
  //       filterDate.durationEndDate = checkbox[0].checkboxEndValue;
  //     }
  //   }

  //   return filterDate


  // }

  convertToLocalTime = (time) => {
    let localTime = new Date(time);
    let timezoneOffset = localTime.getTimezoneOffset() * 60000;
    localTime.setTime(localTime.getTime() - timezoneOffset);
    return localTime;
  };

  openCTA() {
    this._lazyService.openQuickCTAModal(null, this.dialog);
  }

  async openComments() {
    let selectedRowData = this.udrfService.udrfUiData.openCommentsData;
    console.log(selectedRowData);
    let msg;
    let inputData = {
      application_id: 217,
      unique_id_1: selectedRowData['data'].unique_id ? selectedRowData['data'].unique_id : null,
      unique_id_2: '',
      application_name: 'PMS',
      title: selectedRowData['data'].cycle_name ? selectedRowData['data'].cycle_name : 'cycle'
    }

    let modalParams = {
      inputData: inputData,
      context:
      {
        'Name': selectedRowData['data'].cycle_name ? selectedRowData['data'].cycle_name : 'cycle'
      },
      commentBoxHeight: '100vh',
      commentBoxScrollHeight: '80%'
    };


    const { ChatCommentContextModalComponent } = await import(
      'src/app/modules/shared-lazy-loaded-components/chat-comment-context-modal/chat-comment-context-modal.component'
    );
    const openChatCommentContextModalComponent = this.dialog.open(
      ChatCommentContextModalComponent,
      {
        height: '100%',
        width: '50%',
        position: { right: '0px' },
        data: { modalParams: modalParams },
      }
    );

  }


  onStarClick() {

    let item = this.udrfService.udrfUiData.starRatingData['data'];
    //let starIndex = this.udrfService.udrfUiData.starRatingData['starIndex']

    // for (let i = 0; i < this.totalStars; i++)
    //   item['ratingStars'][i].clicked = i <= starIndex ? true : false;

    let value = 0;

    //this.starsResponse.emit(value);
    console.log(value)

    // let req = {
    //   rating_awarded : value,
    //   employee_id : item['employee_oid'],
    //   appraisal_cycle_id : item['appraisal_cycle_id'],
    //   appraisal_module_id : item['appraisal_module_id'],
    //   module_name : item['module_name'],
    //   cycle_name : item['cycle_name'],
    //   cycle_start_date : item['cycle_start_date'],
    //   cycle_end_date : item['cycle_end_date'],
    //   module_template : item['module_template'],
    //   evalutor_id : this.authService.getProfile().profile.oid
    // }

    let req = {
      "appraisal_year": this.appraisalYearFromFilter,
      "employee_oid": item['employee_oid'],
      "appraisal_cycle_id": item['appraisal_cycle_id'],
      "appraisal_module_id": item['appraisal_module_id'],
      "template": item['module_template'],
      "eval_oid": this.userId
    }

    console.log(req);

    this.empService.getEvalPopupData(req).subscribe(res => {
      //console.log(res)
      this.responsedata = res['data']
      console.log(this.responsedata);
      if (this.responsedata.length > 0) {
        let evalRes: any = []
        // this.openListCycleDialog(this.responsedata, data['employee_oid'], data['cycle_name'],data['module_template'],this.userId,this.appraisalYearFromFilter,data['appraisal_cycle_id'],data['appraisal_module_id'],res)
        for (let i = 0; i < this.responsedata.length; i++) {
          let response = {
            employee_evaluation_metrices_id: this.responsedata[i]._id,
            id: this.responsedata[i]._id,
            evaluator_score_awarded: value,
            approver_action_date: new Date(),
            approver_action_is_active: true,
            evaluator_comment: '',
            employe_oid: item['employee_oid'],
            appraisal_module_id: this.responsedata[i].appraisal_module_id,
            appraisal_cycle_id: this.responsedata[i].appraisal_cycle_id,
            appraisal_metrices_id: this.responsedata[i].appraisal_metrices_id,
            employee_appraisal_metrices_evaluator_type: 'manager',
            employee_appraisal_metrices_evaluator_oid: this.userId,
            employee_appraisal_metrices_evaluator_score_awarded: value,
            employee_appraisal_metrices_evaluator_comment:
              '',
            employee_appraisal_metrices_evaluator_status: 'Approved',
            employee_appraisal_module_group_name: this.responsedata[i].group_name,
            appraisal_metrices_name: this.responsedata[i].appraisal_metrices_details.appraisal_metric_name,
          };
          evalRes.push(response)
        }
        console.log("rateee", evalRes)
        let req1 = {
          appraisal_year: this.appraisalYear,
          employee_oid: item['employee_oid'],
          is_draft_mode: false,
          eval_reponse: evalRes,
        };
        console.log("final", req1);
        this.empService.updMetricesScore(req1).subscribe(
          (res) => {
            this.utilityService.showMessage('Employee Evaluated Successfully', 'Dismiss');
            item['evaluator_status'] = "Approved";
          },
          (err) => {
            this.utilityService.showMessage(
              'Error evaluating employee. KEBS team has been notified',
              'Dismiss'
            );
          }
        );


      }
      else {
        this.utilityService.showMessage("No Data Found", "Dismiss");
      }
    }, (err) => {
      //this.showErrorMessage(err);
      this._ErrorService.userErrorAlert(
        err.error.code,
        'Some Error Happened in completing the Activity',
        err.error.errMessage
      );
    })

    //  this.empService.updStarRating(req).pipe(takeUntil(this._onDestroy))
    //    .subscribe(async res => {
    //        console.log(res);
    //this.utilityService.showMessage("Rating Update Successfully",'Dismiss')
    //    },
    // (err) => {
    //   //this.showErrorMessage(err);
    //   this._ErrorService.userErrorAlert(
    //     err.error.code,
    //     'Some Error Happened in completing the Activity',
    //     err.error.errMessage
    //   );
    // })

    //this.utilityService.showMessage("Rating Update Successfully",'Dismiss')

  }



  /** 
* @description open meeting invite modal
*/
  async openMeetingInviteModal() {

    let requestData = this.udrfService.udrfUiData.openMeetingInviteData['data']

    console.log(requestData, 'meeting data')

    let attendees = [];

    // attendees.push(this.requestItem['createdBy']);


    //let req_type = this.requestItem.objectType == 'P' ? 'Project name' : this.requestItem.objectType == 'O' ? 'Opportunity name' : this.requestItem.objectType + "Hiring";

    let modalParams = {
      application_id: 217,
      primary_unique_id: requestData['unique_id'],
      secondary_unique_id: null,
      attendees: attendees,
      cost_centre_object: {},
      is_from_header_creation: false,
      meeting_meta_data: {
        'Cycle name': requestData['cycle_name'] ? requestData['cycle_name'] : 'No Cycle Found',
        'Module name': requestData['module_name'] ? requestData['module_name'] : 'No Module Found',
        'Status': requestData['evaluator_status'],
        'Cycle Duration': `${moment(requestData['cycle_start_date']).format("DD-MMM-YY")} to ${moment(requestData['cycle_end_date']).format("DD-MMM-YY")}`
      }
    };

    console.log(modalParams)

    const { MeetingInviteComponent } = await import('src/app/modules/shared-lazy-loaded-components/meeting-invite/meeting-invite.component');

    const openMeetingInviteComponent = this.dialog.open(MeetingInviteComponent,
      {
        height: '100%',
        width: '75%',
        position: { right: '0px' },
        data: { modalParams: modalParams },
      }
    );

    openMeetingInviteComponent.afterClosed().subscribe(res => {

      if (res && res['event'] == 'submit' && requestData?.evaluator_level==1) {

        let req = {
          "appraisal_year": requestData['appraisal_year'],

          "employee_id": requestData['employee_oid'],

          "model_id": requestData['appraisal_module_id'],

          "cycle_id": requestData['appraisal_cycle_id'],

          "is_meeting_scheduled": true
        }

        console.log(req)

        this.empService.updMeetingScheduledorNot(req).pipe(takeUntil(this._onDestroy))
          .subscribe(async res => {
            console.log(res);
          },
            (err) => {
              //this.showErrorMessage(err);
              this._ErrorService.userErrorAlert(
                err.error.code,
                'Some Error Happened in completing the Activity',
                err.error.errMessage
              );
            })

      }

    });

  }




  ngOnDestroy() {
    this._onDestroy.next();
    this._onDestroy.complete();
    this.udrfService.resetUdrfData();
  }



}
