import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import {PmsComponent} from './pms.component';
import { AppAuthGuard } from './app-auth.guard';

const routes: Routes = [
  {
    path: '',
    component: PmsComponent,
  },
  {
    path: 'appraisal',
    canActivate:[AppAuthGuard],
    loadChildren: () =>
      import('../performance-appraisal/performance-appraisal.module').then(
        (m) => m.PerformanceAppraisalModule
      ),
    data: {
      breadcrumb: 'Appraisal',
      objectId:323,
    },
  },
  {
    path: 'awards',
    canActivate:[AppAuthGuard],
    loadChildren: () => import("../performance-awards/performance-awards.module").then(
      (m) => m.PerformanceAwardsModule
    ),
    data: {
      breadcrumb: 'Rewards',
      objectId:324,
    }
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class PmsRoutingModule { }
