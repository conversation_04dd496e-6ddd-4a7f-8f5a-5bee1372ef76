<div class="list-with-scroll">
  <div
    class="list"
    [ngStyle]="{ gap: gap }"
    appNextPreviousScrollbar
    #list="appNextPreviousScrollbar"
    [scrollUnit]="scrollUnit"
    (scroll)="autoScrollChangeDetection()"
  >
    <!-- Inject list of items which display -->
    <ng-content></ng-content>
  </div>

  <div
    class="scroll-button scroll-left"
    *ngIf="list.isOverflow"
    [class.disable]="!list.canScrollStart"
    (click)="list.scroll(-1)"
  >
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
      <mask
        id="mask0_1807_2703"
        style="mask-type: alpha"
        maskUnits="userSpaceOnUse"
        x="0"
        y="0"
        width="16"
        height="16"
      >
        <rect
          x="16"
          width="16"
          height="16"
          transform="rotate(90 16 0)"
          fill="#D9D9D9"
        />
      </mask>
      <g mask="url(#mask0_1807_2703)">
        <path
          d="M5.96583 8.00159L9.735 4.23242L10.4375 4.93492L7.37083 8.00159L10.4375 11.0683L9.735 11.7708L5.96583 8.00159Z"
          fill="#45546E"
        />
      </g>
    </svg>
  </div>

  <div
    class="scroll-button scroll-right"
    *ngIf="list.isOverflow"
    [class.disable]="!list.canScrollEnd"
    (click)="list.scroll(1)"
  >
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
      <mask
        id="mask0_1807_2708"
        style="mask-type: alpha"
        maskUnits="userSpaceOnUse"
        x="0"
        y="0"
        width="16"
        height="16"
      >
        <rect
          y="16"
          width="16"
          height="16"
          transform="rotate(-90 0 16)"
          fill="#D9D9D9"
        />
      </mask>
      <g mask="url(#mask0_1807_2708)">
        <path
          d="M10.0342 7.99841L6.265 11.7676L5.5625 11.0651L8.62917 7.99841L5.5625 4.93174L6.265 4.22924L10.0342 7.99841Z"
          fill="#45546E"
        />
      </g>
    </svg>
  </div>
</div>
