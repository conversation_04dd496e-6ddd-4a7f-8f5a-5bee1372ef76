import { Component, Input, OnInit } from '@angular/core';

@Component({
  selector: 'app-prev-next-scroll-carousel-dashboard',
  templateUrl: './prev-next-scroll-carousel-dashboard.component.html',
  styleUrls: ['./prev-next-scroll-carousel-dashboard.component.scss'],
})
export class PrevNextScrollCarouselDashboardComponent implements OnInit {
  @Input() scrollUnit: number = 150;
  @Input() gap: string = '4px';

  constructor() {}

  ngOnInit(): void {}

  autoScrollChangeDetection() {}
}
