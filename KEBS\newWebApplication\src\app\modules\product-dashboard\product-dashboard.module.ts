import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { ProductDashboardRoutingModule } from './product-dashboard-routing.module';
import { SharedComponentsModule } from 'src/app/app-shared/app-shared-components/components.module';
import { ProjectStructureModule } from '../project-management/features/pm-details/features/pm-project-team/features/project-structure/project-structure.module';
import { ApplicantTrackingSystemModule } from '../applicant-tracking-system/applicant-tracking-system.module';

import { ProductStaticDashboardComponent } from './screens/product-static-dashboard/product-static-dashboard.component';

import { CalendarComponent } from './components/calendar/calendar.component';
import { HeaderComponent } from './components/header/header.component';
import { HeaderExpansionComponent } from './components/header-expansion/header-expansion.component';
import { ListViewComponent } from './components/list-view/list-view.component';
import { PeopleIconDisplayComponent } from './components/people-icon-display/people-icon-display.component';
import { PrevNextScrollCarouselDashboardComponent } from './components/prev-next-scroll-carousel-dashboard/prev-next-scroll-carousel-dashboard.component';

import { TagsDialogComponent } from './components/tags-dialog/tags-dialog.component';
import { CommentsDialogComponent } from './components/comments-dialog/comments-dialog.component';
import { ReadMoreComponent } from './components/read-more/read-more.component';
import { OrgChartDialogComponent } from './components/org-chart-dialog/org-chart-dialog.component';

import { ScheduleViewerWidgetComponent } from './widgets/schedule-viewer-widget/schedule-viewer-widget.component';
import { HeaderWidgetComponent } from './widgets/header-widget/header-widget.component';
import { DonutPieChartWidgetComponent } from './widgets/donut-pie-chart-widget/donut-pie-chart-widget.component';
import { LineChartWidgetComponent } from './widgets/line-chart-widget/line-chart-widget.component';
import { ProportionalHorizontalBarChartWidgetComponent } from './widgets/proportional-horizontal-bar-chart-widget/proportional-horizontal-bar-chart-widget.component';
import { NotificationWidgetComponent } from './widgets/notification-widget/notification-widget.component';
import { ApprovalWidgetComponent } from './widgets/approval-widget/approval-widget.component';
import { SmartRemindersWidgetComponent } from './widgets/smart-reminders-widget/smart-reminders-widget.component';
import { QuickCommentsWidgetComponent } from './widgets/quick-comments-widget/quick-comments-widget.component';
import { SlaWidgetComponent } from './widgets/sla-widget/sla-widget.component';
import { FavoriteAppWidgetComponent } from './kebs-home-page-widgets/favorite-app-widget/favorite-app-widget.component';
import { MyProfileWidgetComponent } from './kebs-home-page-widgets/my-profile-widget/my-profile-widget.component';
import { HomeNotificationWidgetComponent } from './kebs-home-page-widgets/home-notification-widget/home-notification-widget.component';
import { HomeApprovalsWidgetComponent } from './kebs-home-page-widgets/home-approvals-widget/home-approvals-widget.component';
import { HomeSmartRemindersWidgetComponent } from './kebs-home-page-widgets/home-smart-reminders-widget/home-smart-reminders-widget.component';
import { HomeTimesheetWidgetComponent } from './kebs-home-page-widgets/home-timesheet-widget/home-timesheet-widget.component';
import { HomeRequiredActionsWidgetComponent } from './kebs-home-page-widgets/home-required-actions-widget/home-required-actions-widget.component';
import { HomeTeamMembersWidgetComponent } from './kebs-home-page-widgets/home-team-members-widget/home-team-members-widget.component';
import { BarChartWidgetComponent } from './widgets/bar-chart-widget/bar-chart-widget.component';
import { HomeBdayWidgetComponent } from './kebs-home-page-widgets/home-bday-widget/home-bday-widget.component';

import { DateFormatPipe } from './pipes/date-format/date-format.pipe';
import { SvgSecurityBypassPipe } from './pipes/svg-security-bypass/svg-security-bypass.pipe';
import { IsDarkColorPipe } from './pipes/is-dark-color/is-dark-color.pipe';
import { CurrencyPipe } from './pipes/currency-format/currency-format.pipe';
import { SelectedInlineFilterPipe } from './pipes/selected-inline-filter/selected-inline-filter.pipe';
import { LastUpdatedTimestampPipe } from './pipes/last-updated-timestamp/last-updated-timestamp.pipe';
import { TsWidgetPipe } from './pipes/ts-widget/ts-widget.pipe';

import { CustomTooltipDirective } from './directives/custom-tooltip/custom-tooltip.directive';

import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';
import { InfiniteScrollModule } from 'ngx-infinite-scroll';
import { MatMenuModule } from '@angular/material/menu';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TooltipModule } from 'ng2-tooltip-directive';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { Ng2SearchPipeModule } from 'ng2-search-filter';
import { OverlayModule } from '@angular/cdk/overlay';

import {
  DxChartModule,
  DxDataGridModule,
  DxPieChartModule,
  DxTooltipModule,
} from 'devextreme-angular';

@NgModule({
  declarations: [
    ProductStaticDashboardComponent,
    CalendarComponent,
    HeaderComponent,
    HeaderExpansionComponent,
    ListViewComponent,
    TagsDialogComponent,
    PeopleIconDisplayComponent,
    CommentsDialogComponent,
    ReadMoreComponent,
    OrgChartDialogComponent,
    PrevNextScrollCarouselDashboardComponent,
    ScheduleViewerWidgetComponent,
    HeaderWidgetComponent,
    DonutPieChartWidgetComponent,
    LineChartWidgetComponent,
    ProportionalHorizontalBarChartWidgetComponent,
    NotificationWidgetComponent,
    ApprovalWidgetComponent,
    SmartRemindersWidgetComponent,
    QuickCommentsWidgetComponent,
    SlaWidgetComponent,
    FavoriteAppWidgetComponent,
    MyProfileWidgetComponent,
    HomeNotificationWidgetComponent,
    HomeApprovalsWidgetComponent,
    HomeSmartRemindersWidgetComponent,
    HomeTimesheetWidgetComponent,
    HomeRequiredActionsWidgetComponent,
    HomeTeamMembersWidgetComponent,
    BarChartWidgetComponent,
    HomeBdayWidgetComponent,
    CustomTooltipDirective,
    DateFormatPipe,
    SvgSecurityBypassPipe,
    IsDarkColorPipe,
    CurrencyPipe,
    SelectedInlineFilterPipe,
    LastUpdatedTimestampPipe,
    TsWidgetPipe,
  ],
  imports: [
    CommonModule,
    ProductDashboardRoutingModule,
    SharedComponentsModule,
    ProjectStructureModule,
    ApplicantTrackingSystemModule,
    MatIconModule,
    MatDividerModule,
    MatMenuModule,
    MatTooltipModule,
    InfiniteScrollModule,
    TooltipModule,
    MatFormFieldModule,
    MatInputModule,
    FormsModule,
    ReactiveFormsModule,
    DxChartModule,
    DxDataGridModule,
    DxPieChartModule,
    DxTooltipModule,
    Ng2SearchPipeModule,
    OverlayModule,
  ],
  exports: [
    ProductStaticDashboardComponent,
    PeopleIconDisplayComponent,
    PrevNextScrollCarouselDashboardComponent,
  ],
})
export class ProductDashboardModule {}
