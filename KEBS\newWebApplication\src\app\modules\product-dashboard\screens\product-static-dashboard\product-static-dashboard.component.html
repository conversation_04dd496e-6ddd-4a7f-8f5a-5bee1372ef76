<ng-container *ngIf="isLoading">
  <div class="loading-container">
    <div>
      <img [src]="loadingImage" />
    </div>
    <div class="loading-wrapper">
      <div class="loading">Loading...</div>
    </div>
  </div>
</ng-container>

<ng-container *ngIf="!isLoading && widgetsList.length == 0">
  <div class="loading-container">
    <img src="https://assets.kebs.app/ATS-Empty-State.png" />
    <div class="no-data-msg">No Widgets Found!</div>
  </div>
</ng-container>

<ng-container *ngIf="!isLoading && widgetsList.length > 0">
  <div class="d-flex flex-wrap dashboard-container">
    <ng-container *ngFor="let widget of widgetsList; let i = index">
      <div
        class="single-widget p-0"
        [class]="widget.width"
        [ngStyle]="{ height: widget.height }"
      >
        <!-- Header Widget -->
        <app-header-widget
          *ngIf="widget.widget_type == 'header'"
          [aid]="aid"
          [oid]="oid"
          [customPayload]="customPayload"
          [widgetConfig]="widget"
        ></app-header-widget>
        <!-- Schedule Viewer Widget -->
        <app-schedule-viewer-widget
          *ngIf="widget.widget_type == 'schedule-viewer'"
          [aid]="aid"
          [oid]="oid"
          [customPayload]="customPayload"
          [widgetConfig]="widget"
        ></app-schedule-viewer-widget>
        <!-- Donut or Pie Chart Widget -->
        <app-donut-pie-chart-widget
          *ngIf="widget.widget_type == 'donut' || widget.widget_type == 'pie'"
          [aid]="aid"
          [oid]="oid"
          [customPayload]="customPayload"
          [widgetConfig]="widget"
          [widgetType]="widget.widget_type"
        >
        </app-donut-pie-chart-widget>
        <!-- Line Chart Widget -->
        <app-single-multi-line-chart-widget
          *ngIf="widget.widget_type == 'line'"
          [aid]="aid"
          [oid]="oid"
          [customPayload]="customPayload"
          [widgetConfig]="widget"
        >
        </app-single-multi-line-chart-widget>
        <!-- Proportional Horizontal Bar Chart Widget -->
        <app-proportional-horizontal-bar-chart-widget
          *ngIf="widget.widget_type == 'proportional-horizontal-bar'"
          [aid]="aid"
          [oid]="oid"
          [customPayload]="customPayload"
          [widgetConfig]="widget"
        >
        </app-proportional-horizontal-bar-chart-widget>
        <!-- Notification Widget -->
        <app-notif-dashboard-widget
          *ngIf="widget.widget_type == 'notification'"
          [aid]="aid"
          [oid]="oid"
          [customPayload]="customPayload"
          [widgetConfig]="widget"
        >
        </app-notif-dashboard-widget>
        <!-- Approval Widget -->
        <app-approval-widget
          *ngIf="widget.widget_type == 'approval'"
          [aid]="aid"
          [oid]="oid"
          [customPayload]="customPayload"
          [widgetConfig]="widget"
        >
        </app-approval-widget>
        <!-- Smart Reminders Widget -->
        <app-smart-reminders-widget
          *ngIf="widget.widget_type == 'smart-reminders'"
          [aid]="aid"
          [oid]="oid"
          [customPayload]="customPayload"
          [widgetConfig]="widget"
          [name]="name"
        >
        </app-smart-reminders-widget>
        <!-- Quick Comments Widget -->
        <app-quick-comments-widget
          *ngIf="widget.widget_type == 'quick-comments'"
          [aid]="aid"
          [oid]="oid"
          [customPayload]="customPayload"
          [widgetConfig]="widget"
          [applicationId]="applicationId"
          [subApplicationId]="subApplicationId"
        >
        </app-quick-comments-widget>
        <!-- SLA Widget -->
        <app-sla-widget
          *ngIf="widget.widget_type == 'sla'"
          [aid]="aid"
          [oid]="oid"
          [customPayload]="customPayload"
          [widgetConfig]="widget"
        >
        </app-sla-widget>
        <!-- Home Page Favorite App Widget -->
        <app-favorite-app-widget
          *ngIf="widget.widget_type == 'kebs-home-favorite-apps'"
          [aid]="aid"
          [oid]="oid"
          [customPayload]="customPayload"
          [widgetConfig]="widget"
          [widgetEmptyState]="widgetEmptyState"
        >
        </app-favorite-app-widget>
        <!-- Home Page Notification Widget -->
        <app-home-notification-widget
          *ngIf="widget.widget_type == 'kebs-home-notification'"
          [aid]="aid"
          [oid]="oid"
          [customPayload]="customPayload"
          [widgetConfig]="widget"
          [widgetEmptyState]="widgetEmptyState"
        >
        </app-home-notification-widget>
        <!-- Home Page Approvals Widget -->
        <app-home-approvals-widget
          *ngIf="widget.widget_type == 'kebs-home-approvals'"
          [aid]="aid"
          [oid]="oid"
          [customPayload]="customPayload"
          [widgetConfig]="widget"
          [widgetEmptyState]="widgetEmptyState"
        >
        </app-home-approvals-widget>
        <!-- Home Page Smart Reminders Widget -->
        <app-home-smart-reminders-widget
          *ngIf="widget.widget_type == 'kebs-home-smart-reminders'"
          [aid]="aid"
          [oid]="oid"
          [customPayload]="customPayload"
          [widgetConfig]="widget"
          [name]="name"
          [widgetEmptyState]="widgetEmptyState"
        >
        </app-home-smart-reminders-widget>
        <!-- Home Page My Profile Widget -->
        <app-my-profile-widget
          *ngIf="widget.widget_type == 'kebs-home-profile'"
          [aid]="aid"
          [oid]="oid"
          [customPayload]="customPayload"
          [widgetConfig]="widget"
          [widgetEmptyState]="widgetEmptyState"
        >
        </app-my-profile-widget>
        <!-- Home Page Timesheet Widget -->
        <app-home-timesheet-widget
          *ngIf="widget.widget_type == 'kebs-home-timesheet'"
          [aid]="aid"
          [oid]="oid"
          [customPayload]="customPayload"
          [widgetConfig]="widget"
          [widgetEmptyState]="widgetEmptyState"
        >
        </app-home-timesheet-widget>
        <!-- Home Page Required Actions Widget -->
        <app-home-required-actions-widget
          *ngIf="widget.widget_type == 'kebs-home-required-actions'"
          [aid]="aid"
          [oid]="oid"
          [customPayload]="customPayload"
          [widgetConfig]="widget"
          [widgetEmptyState]="widgetEmptyState"
        >
        </app-home-required-actions-widget>
        <!-- Home Page Team Members Widget -->
        <app-home-team-members-widget
          *ngIf="widget.widget_type == 'kebs-home-team-members'"
          [aid]="aid"
          [oid]="oid"
          [customPayload]="customPayload"
          [widgetConfig]="widget"
          [widgetEmptyState]="widgetEmptyState"
        >
        </app-home-team-members-widget>
        <!--Bar Chart Widget -->
        <app-bar-chart-widget
          *ngIf="widget.widget_type == 'bar-chart'"
          [aid]="aid"
          [oid]="oid"
          [customPayload]="customPayload"
          [widgetConfig]="widget"
        >
        </app-bar-chart-widget>
        <!-- Home Birthday Widget -->
        <app-home-bday-widget
          *ngIf="widget.widget_type == 'kebs-home-bday'"
          [aid]="aid"
          [oid]="oid"
          [customPayload]="customPayload"
          [widgetConfig]="widget"
          [widgetEmptyState]="widgetEmptyState"
        >
        </app-home-bday-widget>
      </div>
    </ng-container>
  </div>
</ng-container>
