import { Component, Input, OnInit } from '@angular/core';

import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

import { ArchitectureService } from './../../services/architecture/architecture.service';
import { ToasterService } from 'src/app/modules/applicant-tracking-system/shared-components/ats-custom-toast/toaster.service';
import { LoginService } from 'src/app/services/login/login.service';

@Component({
  selector: 'app-product-static-dashboard',
  templateUrl: './product-static-dashboard.component.html',
  styleUrls: ['./product-static-dashboard.component.scss'],
})
export class ProductStaticDashboardComponent implements OnInit {
  @Input() applicationId: number = null;
  @Input() subApplicationId: number = null;
  @Input() customPayload: any = {};
  @Input() widgetEmptyState: string = '';

  protected _onDestroy = new Subject<void>();

  aid: number = null;
  oid: string = null;
  name: string = '';

  loadingImage: string = '';

  widgetsList = [];

  isLoading: boolean = true;

  constructor(
    private _loginService: LoginService,
    private _architectureService: ArchitectureService,
    private _toaster: ToasterService
  ) {}

  async ngOnInit() {
    this.aid = this._loginService.getProfile().profile.aid;
    this.oid = this._loginService.getProfile().profile.oid;
    this.name = this._loginService.getProfile().profile.name;

    await Promise.all([
      this.getDashboardUiConfiguration('theme'),
      this.getDashboardWidgets(),
    ])
      .then(async (res) => {
        this.isLoading = false;
      })
      .catch((err) => {
        this.isLoading = false;
      });
  }

  /**
   * @description Gets All Dashboard Master UI Config
   * @param {string} key
   */
  async getDashboardUiConfiguration(key) {
    return new Promise((resolve, reject) =>
      this._architectureService
        .getDashboardUiConfiguration(key)
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res) => {
            if (res['err'] == false) {
              if (key == 'theme') {
                document.documentElement.style.setProperty(
                  '--dashboardFontFamily',
                  res['data']['fontFamily']
                );
                document.documentElement.style.setProperty(
                  '--dashboardPrimaryColor',
                  res['data']['primaryColor']
                );
                this.loadingImage = res['data']['loadingImage'];
              }
            } else {
              this._toaster.showError('Error', res['msg'], 7000);
            }
            resolve(true);
          },
          error: (err) => {
            this._toaster.showError(
              'Error',
              err.msg ? err.msg : 'Master Data Retrieval Failed!',
              7000
            );
            reject();
          },
        })
    );
  }

  /**
   * @description Gets All Dashboard Widgets
   */
  async getDashboardWidgets() {
    let payload = {
      aid: this.aid,
      oid: this.oid,
      applicationId: this.applicationId,
      subApplicationId: this.subApplicationId,
      ...this.customPayload,
    };
    return new Promise((resolve, reject) =>
      this._architectureService
        .getDashboardWidgets(payload)
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res) => {
            if (res['err'] == false) {
              this.widgetsList = res['data'];
            } else {
              this._toaster.showError('Error', res['msg'], 7000);
            }
            resolve(true);
          },
          error: (err) => {
            this._toaster.showError(
              'Error',
              err.msg ? err.msg : 'Widgets Data Retrieval Failed!',
              7000
            );
            reject();
          },
        })
    );
  }
}
