import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import * as moment from 'moment';

@Injectable({
  providedIn: 'root',
})
export class WidgetsService {
  constructor(private _http: HttpClient) {}

  getDataDynamically(url, payload) {
    payload = payload
      ? { ...payload, currentDate: moment().format('YYYY-MM-DD') }
      : {};
    return this._http.post(url, payload);
  }

  onClickActionsIcon(data, apiURL) {
    data = data ? { ...data, currentDate: moment().format('YYYY-MM-DD') } : {};
    return this._http.post(apiURL, data);
  }

  getATSInterviewCandidateDetails(data) {
    return this._http.post(
      'api/ats/interview/getATSInterviewCandidateDetails',
      {
        data: data,
      }
    );
  }
}
