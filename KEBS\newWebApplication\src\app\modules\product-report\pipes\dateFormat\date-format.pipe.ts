import { Pipe, PipeTransform } from '@angular/core';
import * as moment from 'moment';

@Pipe({
  name: 'dateFormatStaticReport',
})
export class DateFormatPipe implements PipeTransform {
  transform(value: any, format: string, isLocalValue: boolean = true): string {
    if (!value || !moment(value, moment.ISO_8601, true).isValid()) {
      return '-'; // Handle invalid or null values
    }

    // Handle date-only strings without time component
    if (moment(value, 'YYYY-MM-DD', true).isValid()) {
      return moment(value, 'YYYY-MM-DD').format(format); // No UTC conversion
    }

    // Handle ISO 8601 with UTC and local conversion
    const momentObj = moment(value); // Parse the value
    return momentObj.format(format);
  }
}
