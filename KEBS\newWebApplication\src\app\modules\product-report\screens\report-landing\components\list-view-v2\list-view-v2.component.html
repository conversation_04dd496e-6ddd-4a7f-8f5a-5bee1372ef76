<div
  class="list-view"
  infinite-scroll
  [infiniteScrollDistance]="3"
  [infiniteScrollThrottle]="100"
  (scrolled)="onDataScroll()"
  [scrollWindow]="false"
>
  <div class="header-sticky">
    <div class="row header-row-main">
      <ng-container *ngFor="let item of fieldConfig; let i = index">
        <div
          *ngIf="item.is_visible"
          class="header-row"
          [class]="
            item?.is_pinned_left
              ? 'header-row-left-pin'
              : item?.is_pinned_right
              ? 'header-row-right-pin'
              : ''
          "
          [ngStyle]="{
            'min-width': item.width,
            'max-width': item.width,
            left: item?.is_pinned_left
              ? (fieldConfig | calculatePositionOfPinnedColumns : i : 'left')
              : '',
            right: item?.is_pinned_right
              ? (fieldConfig | calculatePositionOfPinnedColumns : i : 'right')
              : ''
          }"
          [appResizable]="100"
          [fieldConfig]="fieldConfig"
          [currentFieldConfigIndex]="i"
          (emitWidthChangeData)="emitWidthChangeData($event)"
        >
          <div class="header-row-content">
            <span
              *ngIf="!(item?.is_search_active && item?.is_search_icon_clicked)"
              class="list-title"
              [matTooltip]="item.label"
            >
              {{ item.label }}
            </span>
            <div
              *ngIf="item?.is_search_active && item?.is_search_icon_clicked"
              class="input-field"
            >
              <input
                [id]="'#inputElement' + i"
                type="text"
                [(ngModel)]="item.search_param"
                placeholder="Search..."
                (keydown.enter)="
                  onEnterKeyPressedForSearch(i, item.search_param)
                "
              />
            </div>
            <div class="d-flex align-items-center">
              <div
                *ngIf="item.is_sort_active && item.sort_order == 0"
                class="svg"
                (click)="onClickSort(1, i)"
              >
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                  <mask
                    id="mask0_18314_8299"
                    style="mask-type: alpha"
                    maskUnits="userSpaceOnUse"
                    x="0"
                    y="0"
                    width="16"
                    height="16"
                  >
                    <rect width="16" height="16" fill="#D9D9D9" />
                  </mask>
                  <g mask="url(#mask0_18314_8299)">
                    <path
                      d="M6.10889 8.42947C5.96711 8.42947 5.84833 8.38152 5.75256 8.28564C5.65689 8.18986 5.60906 8.07113 5.60906 7.92947V3.57563L4.06422 5.12047C3.974 5.2128 3.86128 5.25897 3.72606 5.25897C3.59083 5.25897 3.4715 5.21113 3.36806 5.11547C3.26461 5.01113 3.21289 4.89186 3.21289 4.75764C3.21289 4.62352 3.26461 4.50475 3.36806 4.4013L5.68089 2.08847C5.74322 2.02602 5.80928 1.98202 5.87906 1.95647C5.94895 1.9308 6.02378 1.91797 6.10356 1.91797C6.18333 1.91797 6.25911 1.93086 6.33089 1.95664C6.40267 1.98252 6.46934 2.02647 6.53089 2.08847L8.86039 4.41797C8.9595 4.51708 9.00839 4.63202 9.00706 4.7628C9.00583 4.89358 8.9535 5.01113 8.85006 5.11547C8.74661 5.21113 8.6295 5.26069 8.49872 5.26413C8.36795 5.26747 8.25089 5.21747 8.14756 5.11413L6.60906 3.57563V7.9358C6.60906 8.07569 6.56111 8.19291 6.46522 8.28747C6.36933 8.38213 6.25056 8.42947 6.10889 8.42947ZM9.89656 14.0756C9.81678 14.0756 9.741 14.0627 9.66922 14.0368C9.59745 14.011 9.53078 13.9671 9.46922 13.9051L7.13972 11.5756C7.04061 11.4764 6.99172 11.3615 6.99306 11.2308C6.99428 11.1 7.04661 10.9825 7.15006 10.8781C7.2535 10.7825 7.37061 10.7329 7.50139 10.7295C7.63217 10.726 7.74922 10.776 7.85256 10.8795L9.39106 12.418V8.05763C9.39106 7.91786 9.439 7.80063 9.53489 7.70597C9.63078 7.61141 9.74956 7.56414 9.89122 7.56414C10.033 7.56414 10.1518 7.61202 10.2476 7.7078C10.3432 7.80369 10.3911 7.92247 10.3911 8.06414V12.418L11.9359 10.873C12.0261 10.7807 12.1388 10.7346 12.2741 10.7346C12.4093 10.7346 12.5286 10.7825 12.6321 10.8781C12.7355 10.9825 12.7872 11.1017 12.7872 11.2358C12.7872 11.37 12.7355 11.4889 12.6321 11.5923L10.3192 13.9051C10.2569 13.9675 10.1908 14.0115 10.1211 14.0371C10.0512 14.0628 9.97633 14.0756 9.89656 14.0756Z"
                      fill="#8B95A5"
                    />
                  </g>
                </svg>
              </div>
              <div
                *ngIf="item.is_sort_active && item.sort_order == 1"
                class="svg"
                (click)="onClickSort(2, i)"
              >
                <svg
                  height="10px"
                  width="8px"
                  fill="#8B95A5"
                  version="1.1"
                  id="Layer_1"
                  viewBox="0 0 512 512"
                  enable-background="new 0 0 512 512"
                  xml:space="preserve"
                >
                  <polygon
                    points="245,0 74.3,213.3 202.3,213.3 202.3,512 287.7,512 287.7,213.3 415.7,213.3 "
                  />
                </svg>
              </div>
              <div
                *ngIf="item.is_sort_active && item.sort_order == 2"
                class="svg"
                (click)="onClickSort(0, i)"
              >
                <svg
                  height="10px"
                  width="8px"
                  fill="#8B95A5"
                  version="1.1"
                  id="Layer_1"
                  viewBox="0 0 512 512"
                  enable-background="new 0 0 512 512"
                  xml:space="preserve"
                >
                  <polygon
                    points="283.7,298.7 283.7,0 198.3,0 198.3,298.7 70.3,298.7 241,512 411.7,298.7 "
                  />
                </svg>
              </div>
            </div>
            <div
              *ngIf="item?.is_search_active && !item?.is_search_icon_clicked"
              class="svg"
              (click)="onClickSearch(i)"
            >
              <svg width="12" height="12" viewBox="0 0 18 18" fill="none">
                <path
                  d="M7.01916 12.6153C5.31148 12.6153 3.86533 12.023 2.68073 10.8384C1.49612 9.65383 0.903809 8.20768 0.903809 6.49999C0.903809 4.79231 1.49612 3.34616 2.68073 2.16154C3.86533 0.976943 5.31148 0.384644 7.01916 0.384644C8.72684 0.384644 10.173 0.976943 11.3576 2.16154C12.5422 3.34616 13.1345 4.79231 13.1345 6.49999C13.1345 7.21409 13.0147 7.89614 12.7749 8.54614C12.5352 9.19614 12.2153 9.76153 11.8153 10.2423L17.5692 15.9961C17.7076 16.1346 17.7784 16.3087 17.7816 16.5183C17.7849 16.7279 17.714 16.9051 17.5692 17.05C17.4243 17.1948 17.2486 17.2673 17.0422 17.2673C16.8358 17.2673 16.6602 17.1948 16.5153 17.05L10.7615 11.2961C10.2615 11.7089 9.68646 12.032 9.03646 12.2653C8.38646 12.4987 7.71403 12.6153 7.01916 12.6153ZM7.01916 11.1154C8.30763 11.1154 9.39897 10.6683 10.2932 9.77404C11.1874 8.87981 11.6346 7.78846 11.6346 6.49999C11.6346 5.21153 11.1874 4.12018 10.2932 3.22594C9.39897 2.33171 8.30763 1.88459 7.01916 1.88459C5.73069 1.88459 4.63934 2.33171 3.74511 3.22594C2.85089 4.12018 2.40378 5.21153 2.40378 6.49999C2.40378 7.78846 2.85089 8.87981 3.74511 9.77404C4.63934 10.6683 5.73069 11.1154 7.01916 11.1154Z"
                  fill="#8B95A5"
                />
              </svg>
            </div>
            <div
              *ngIf="item?.is_search_active && item?.is_search_icon_clicked"
              class="svg"
              (click)="onClickCloseSearch(i)"
            >
              <svg width="12" height="12" viewBox="0 0 18 18" fill="none">
                <mask
                  id="mask0_15728_213781"
                  style="mask-type: alpha"
                  maskUnits="userSpaceOnUse"
                  x="0"
                  y="0"
                  width="18"
                  height="18"
                >
                  <rect width="18" height="18" fill="#D9D9D9" />
                </mask>
                <g mask="url(#mask0_15728_213781)">
                  <path
                    d="M4.80008 13.9904L4.00977 13.2001L8.20977 9.00008L4.00977 4.80008L4.80008 4.00977L9.00008 8.20977L13.2001 4.00977L13.9904 4.80008L9.79039 9.00008L13.9904 13.2001L13.2001 13.9904L9.00008 9.79039L4.80008 13.9904Z"
                    fill="#8B95A5"
                  />
                </g>
              </svg>
            </div>
            <div
              class="svg"
              [matMenuTriggerFor]="mainColumnMenu"
              (click)="setIndexOfSelectedMenu(i)"
            >
              <svg
                height="20px"
                viewBox="0 -960 960 960"
                width="20px"
                fill="#8B95A5"
              >
                <path
                  d="M479.79-192Q450-192 429-213.21t-21-51Q408-294 429.21-315t51-21Q510-336 531-314.79t21 51Q552-234 530.79-213t-51 21Zm0-216Q450-408 429-429.21t-21-51Q408-510 429.21-531t51-21Q510-552 531-530.79t21 51Q552-450 530.79-429t-51 21Zm0-216Q450-624 429-645.21t-21-51Q408-726 429.21-747t51-21Q510-768 531-746.79t21 51Q552-666 530.79-645t-51 21Z"
                />
              </svg>
            </div>
          </div>
          <div
            class="d-flex align-items-center justify-content-end vertical-divider-wrapper"
          >
            <mat-divider
              class="vertical-divider"
              [vertical]="true"
            ></mat-divider>
          </div>
        </div>
      </ng-container>
    </div>
  </div>
  <ng-container *ngFor="let item of list; let itemIndex = index">
    <div class="content-sticky">
      <div class="row content-row-main">
        <ng-container *ngFor="let field of fieldConfig; let fieldIndex = index">
          <div
            *ngIf="field.is_visible"
            class="content-row"
            [class]="
              field?.is_pinned_left
                ? 'content-row-left-pin'
                : field?.is_pinned_right
                ? 'content-row-right-pin'
                : ''
            "
            [ngStyle]="{
              'min-width': field.width,
              'max-width': field.width,
              left: field?.is_pinned_left
                ? (fieldConfig
                  | calculatePositionOfPinnedColumns : fieldIndex : 'left')
                : '',
              right: field?.is_pinned_right
                ? (fieldConfig
                  | calculatePositionOfPinnedColumns : fieldIndex : 'right')
                : '',
              'border-left':
                field?.is_pinned_right &&
                !fieldConfig[fieldIndex - 1]?.is_pinned_right
                  ? '1px solid #DADCE2'
                  : '',
              'border-right':
                field?.is_pinned_left &&
                !fieldConfig[fieldIndex + 1]?.is_pinned_left
                  ? '1px solid #DADCE2'
                  : '',
              'justify-content':
                field?.column_type == 'currency' ? 'end' : 'start',
              'padding-right': field?.column_type == 'currency' ? '10px' : '',
              cursor: field.click_actions ? 'pointer' : ''
            }"
            (click)="
              columnClickAction(
                field.click_actions,
                item,
                item[field.click_actions.column_key]
              )
            "
          >
            <ng-container
              *ngIf="
                fieldIndex == 0 &&
                reportDetails?.sub_field_config_field &&
                item[reportDetails.sub_field_config_field] &&
                subFieldConfig &&
                subFieldConfig.length > 0
              "
            >
              <mat-icon
                *ngIf="!item?.isExpanded"
                class="navigation-icon"
                (click)="item.isExpanded = true"
              >
                chevron_right
              </mat-icon>
              <mat-icon
                *ngIf="item?.isExpanded"
                class="navigation-icon"
                (click)="item.isExpanded = false"
              >
                keyboard_arrow_down
              </mat-icon>
            </ng-container>
            <ng-container *ngIf="field.column_type == 'text'">
              <span
                class="normal-text"
                [matTooltip]="item[field.column_key['0']] || '-'"
              >
                {{ item[field.column_key["0"]] || "-" }}
              </span>
            </ng-container>
            <ng-container *ngIf="field.column_type == 'number'">
              <span
                class="normal-text"
                [matTooltip]="
                  [null, undefined, false].includes(item[field.column_key['0']])
                    ? '-'
                    : item[field.column_key['0']]
                "
              >
                {{
                  [null, undefined, false].includes(item[field.column_key["0"]])
                    ? "-"
                    : item[field.column_key["0"]]
                }}
              </span>
            </ng-container>
            <ng-container *ngIf="field.column_type == 'date'">
              <span
                class="normal-text"
                [matTooltip]="
                  item[field.column_key['0']]
                    | dateFormatStaticReport
                      : field.column_key['1'] || 'DD MMM YYYY'
                "
              >
                {{
                  item[field.column_key["0"]]
                    | dateFormatStaticReport
                      : field.column_key["1"] || "DD MMM YYYY"
                }}
              </span>
            </ng-container>
            <ng-container *ngIf="field.column_type == 'chip'">
              <div
                class="chip"
                [ngStyle]="{
                  'background-color': item[field.column_key['1']],
                  color: item[field.column_key['2']]
                }"
                [matTooltip]="item[field.column_key['0']] || '-'"
              >
                {{ item[field.column_key["0"]] || "-" }}
              </div>
            </ng-container>
            <ng-container *ngIf="field.column_type == 'hyperlink'">
              <span
                *ngIf="item[field.column_key['0']]"
                class="hyperlink-text"
                [matTooltip]="item[field.column_key['0']]"
                (click)="openHyperLink(item[field.column_key['0']])"
              >
                {{ item[field.column_key["0"]] }}
              </span>
              <span *ngIf="!item[field.column_key['0']]" class="normal-text">
                -
              </span>
            </ng-container>
            <ng-container *ngIf="field.column_type == 'date-range'">
              <span
                class="normal-text"
                [matTooltip]="
                  item[field.column_key['0']]
                    | dateFormatStaticReport
                      : field.column_key['2'] ||
                          'DD MMM YYYY' + ' - ' + item[field.column_key['1']]
                    | dateFormatStaticReport
                      : field.column_key['2'] || 'DD MMM YYYY'
                "
              >
                {{
                  item[field.column_key["0"]]
                    | dateFormatStaticReport
                      : field.column_key["2"] || "DD MMM YYYY"
                }}
                -
                {{
                  item[field.column_key["1"]]
                    | dateFormatStaticReport
                      : field.column_key["2"] || "DD MMM YYYY"
                }}
              </span>
            </ng-container>
            <ng-container *ngIf="field.column_type == 'employee-display'">
              <app-user-image
                style="padding-right: 8px"
                [oid]="item[field.column_key['1']]"
                imgWidth="24px"
                imgHeight="24px"
              ></app-user-image>
              <span
                class="normal-text"
                [matTooltip]="item[field.column_key['0']] || '-'"
              >
                {{ item[field.column_key["0"]] || "-" }}
              </span>
            </ng-container>
            <ng-container *ngIf="field.column_type == 'actions'">
              <div class="actions">
                <ng-container
                  *ngFor="
                    let icon of field.click_actions;
                    let iconIndex = index
                  "
                >
                  <div
                    *ngIf="
                      !icon?.showLoader ||
                      icon?.showLoader == false ||
                      (icon?.showLoader == true &&
                        !item[field?.id + '_' + iconIndex + '_loader'])
                    "
                  >
                    <div
                      style="cursor: pointer"
                      *ngIf="
                        (icon.type == 'svg' &&
                          icon.action_type == 'hyperlink' &&
                          item[icon.click_action]) ||
                        (icon.type == 'svg' && icon.action_type != 'hyperlink')
                      "
                      [innerHTML]="icon.icon | svgSecurityBypass"
                      [matTooltip]="icon.label"
                      (click)="
                        onClickActionsIcon(
                          item,
                          icon.action_type,
                          icon.click_action,
                          icon?.file_name,
                          itemIndex,
                          fieldIndex,
                          iconIndex,
                          icon?.function_param
                        )
                      "
                    ></div>
                  </div>
                  <div
                    *ngIf="
                      icon?.showLoader == true &&
                      item[field?.id + '_' + iconIndex + '_loader']
                    "
                    class="ml-2"
                  >
                    <div
                      class="spinner-border spinner-border-sm spinner"
                      role="status"
                    >
                      <span class="sr-only">Loading...</span>
                    </div>
                  </div>
                  <div
                    *ngIf="
                      !icon?.showLoader ||
                      icon?.showLoader == false ||
                      (icon?.showLoader == true &&
                        !item[field?.id + '_' + iconIndex + '_loader'])
                    "
                  >
                    <div
                      *ngIf="
                        (icon.type == 'mat-icon' &&
                          icon.action_type == 'hyperlink' &&
                          item[icon.click_action]) ||
                        (icon.type == 'mat-icon' &&
                          icon.action_type != 'hyperlink')
                      "
                    >
                      <mat-icon
                        style="cursor: pointer"
                        [ngStyle]="{
                          'font-size': icon.font_size,
                          width: icon.font_size,
                          height: icon.font_size,
                          color: icon.color
                        }"
                        [matTooltip]="icon.label"
                        (click)="
                          onClickActionsIcon(
                            item,
                            icon.action_type,
                            icon.click_action,
                            icon?.file_name,
                            itemIndex,
                            fieldIndex,
                            iconIndex,
                            icon?.function_param
                          )
                        "
                      >
                        {{ icon.icon }}
                      </mat-icon>
                    </div>
                  </div>
                </ng-container>
              </div>
            </ng-container>
            <ng-container *ngIf="field.column_type == 'currency'">
              <span
                class="normal-text"
                [matTooltip]="
                  item[field.column_key['1']] && item[field.column_key['0']]
                    ? item[field.column_key['1']] +
                      ' ' +
                      item[field.column_key['0']]
                    : '-'
                "
              >
                {{ item[field.column_key["1"]] }}
                {{
                  item[field.column_key["0"]]
                    | currencyFormat
                      : item[field.column_key["1"]]
                      : field.column_key["2"]
                }}
              </span>
            </ng-container>
          </div>
        </ng-container>
      </div>
    </div>
    <ng-container *ngIf="item?.isExpanded">
      <div class="row sub-header-row-main">
        <ng-container
          *ngFor="let subField of subFieldConfig; let subFieldIndex = index"
        >
          <div
            *ngIf="subField.is_visible"
            class="header-row"
            [ngStyle]="{
              'min-width':
                subField.width || fieldConfig[subFieldIndex]['width'],
              'max-width': subField.width || fieldConfig[subFieldIndex]['width']
            }"
          >
            <div class="header-row-content">
              <span class="list-title" [matTooltip]="subField.label">
                {{ subField.label }}
              </span>
            </div>
          </div>
        </ng-container>
      </div>
      <ng-container
        *ngFor="
          let subItem of item[reportDetails.sub_field_config_field];
          let subItemIndex = index
        "
      >
        <div class="row sub-content-row-main">
          <ng-container
            *ngFor="let subField of subFieldConfig; let subFieldIndex = index"
          >
            <div
              *ngIf="subField.is_visible"
              class="content-row"
              [ngStyle]="{
                'min-width': subField.width || fieldConfig[subFieldIndex]['width'],
                'max-width': subField.width || fieldConfig[subFieldIndex]['width'],
                'justify-content':
                  subField?.column_type == 'currency' ? 'end' : 'start',
                'padding-right':
                  subField?.column_type == 'currency' ? '10px' : '',
                cursor: subField.click_actions ? 'pointer' : ''
              }"
              (click)="
                columnClickAction(
                  subField.click_actions,
                  subItem,
                  subItem[subField.click_actions.column_key]
                )
              "
            >
              <ng-container *ngIf="subField.column_type == 'text'">
                <span
                  class="normal-text"
                  [matTooltip]="subItem[subField.column_key['0']] || '-'"
                >
                  {{ subItem[subField.column_key["0"]] || "-" }}
                </span>
              </ng-container>
              <ng-container *ngIf="subField.column_type == 'number'">
                <span
                  class="normal-text"
                  [matTooltip]="
                    [null, undefined, false].includes(
                      subItem[subField.column_key['0']]
                    )
                      ? '-'
                      : subItem[subField.column_key['0']]
                  "
                >
                  {{
                    [null, undefined, false].includes(
                      subItem[subField.column_key["0"]]
                    )
                      ? "-"
                      : subItem[subField.column_key["0"]]
                  }}
                </span>
              </ng-container>
              <ng-container *ngIf="subField.column_type == 'date'">
                <span
                  class="normal-text"
                  [matTooltip]="
                    subItem[subField.column_key['0']]
                      | dateFormatStaticReport
                        : subField.column_key['1'] || 'DD MMM YYYY'
                  "
                >
                  {{
                    subItem[subField.column_key["0"]]
                      | dateFormatStaticReport
                        : subField.column_key["1"] || "DD MMM YYYY"
                  }}
                </span>
              </ng-container>
              <ng-container *ngIf="subField.column_type == 'chip'">
                <div
                  class="chip"
                  [ngStyle]="{
                    'background-color': subItem[subField.column_key['1']],
                    color: subItem[subField.column_key['2']]
                  }"
                  [matTooltip]="subItem[subField.column_key['0']] || '-'"
                >
                  {{ subItem[subField.column_key["0"]] || "-" }}
                </div>
              </ng-container>
              <ng-container *ngIf="subField.column_type == 'hyperlink'">
                <span
                  *ngIf="subItem[subField.column_key['0']]"
                  class="hyperlink-text"
                  [matTooltip]="subItem[subField.column_key['0']]"
                  (click)="openHyperLink(subItem[subField.column_key['0']])"
                >
                  {{ subItem[subField.column_key["0"]] }}
                </span>
                <span
                  *ngIf="!subItem[subField.column_key['0']]"
                  class="normal-text"
                >
                  -
                </span>
              </ng-container>
              <ng-container *ngIf="subField.column_type == 'date-range'">
                <span
                  class="normal-text"
                  [matTooltip]="
                    subItem[subField.column_key['0']]
                      | dateFormatStaticReport
                        : subField.column_key['2'] ||
                            'DD MMM YYYY' +
                              ' - ' +
                              subItem[subField.column_key['1']]
                      | dateFormatStaticReport
                        : subField.column_key['2'] || 'DD MMM YYYY'
                  "
                >
                  {{
                    subItem[subField.column_key["0"]]
                      | dateFormatStaticReport
                        : subField.column_key["2"] || "DD MMM YYYY"
                  }}
                  -
                  {{
                    subItem[subField.column_key["1"]]
                      | dateFormatStaticReport
                        : subField.column_key["2"] || "DD MMM YYYY"
                  }}
                </span>
              </ng-container>
              <ng-container *ngIf="subField.column_type == 'employee-display'">
                <app-user-image
                  style="padding-right: 8px"
                  [oid]="subItem[subField.column_key['1']]"
                  imgWidth="24px"
                  imgHeight="24px"
                ></app-user-image>
                <span
                  class="normal-text"
                  [matTooltip]="subItem[subField.column_key['0']] || '-'"
                >
                  {{ subItem[subField.column_key["0"]] || "-" }}
                </span>
              </ng-container>
              <ng-container *ngIf="subField.column_type == 'actions'">
                <div class="actions">
                  <ng-container
                    *ngFor="
                      let icon of subField.click_actions;
                      let iconIndex = index
                    "
                  >
                    <div
                      *ngIf="
                        !icon?.showLoader ||
                        icon?.showLoader == false ||
                        (icon?.showLoader == true &&
                          !item[field?.id + '_' + iconIndex + '_loader'])
                      "
                    >
                      <div
                        style="cursor: pointer"
                        *ngIf="
                          (icon.type == 'svg' &&
                            icon.action_type == 'hyperlink' &&
                            item[icon.click_action]) ||
                          (icon.type == 'svg' &&
                            icon.action_type != 'hyperlink')
                        "
                        [innerHTML]="icon.icon | svgSecurityBypass"
                        [matTooltip]="icon.label"
                        (click)="
                          onClickActionsIcon(
                            item,
                            icon.action_type,
                            icon.click_action,
                            icon?.file_name,
                            itemIndex,
                            fieldIndex,
                            iconIndex,
                            icon?.function_param
                          )
                        "
                      ></div>
                    </div>
                    <div
                      *ngIf="
                        icon?.showLoader == true &&
                        item[field?.id + '_' + iconIndex + '_loader']
                      "
                      class="ml-2"
                    >
                      <div
                        class="spinner-border spinner-border-sm spinner"
                        role="status"
                      >
                        <span class="sr-only">Loading...</span>
                      </div>
                    </div>
                    <div
                      *ngIf="
                        !icon?.showLoader ||
                        icon?.showLoader == false ||
                        (icon?.showLoader == true &&
                          !item[field?.id + '_' + iconIndex + '_loader'])
                      "
                    >
                      <div
                        *ngIf="
                          (icon.type == 'mat-icon' &&
                            icon.action_type == 'hyperlink' &&
                            item[icon.click_action]) ||
                          (icon.type == 'mat-icon' &&
                            icon.action_type != 'hyperlink')
                        "
                      >
                        <mat-icon
                          style="cursor: pointer"
                          [ngStyle]="{
                            'font-size': icon.font_size,
                            width: icon.font_size,
                            height: icon.font_size,
                            color: icon.color
                          }"
                          [matTooltip]="icon.label"
                          (click)="
                            onClickActionsIcon(
                              item,
                              icon.action_type,
                              icon.click_action,
                              icon?.file_name,
                              itemIndex,
                              fieldIndex,
                              iconIndex,
                              icon?.function_param
                            )
                          "
                        >
                          {{ icon.icon }}
                        </mat-icon>
                      </div>
                    </div>
                  </ng-container>
                </div>
              </ng-container>
              <ng-container *ngIf="subField.column_type == 'currency'">
                <span
                  class="normal-text"
                  [matTooltip]="
                    subItem[subField.column_key['1']] &&
                    subItem[subField.column_key['0']]
                      ? subItem[subField.column_key['1']] +
                        ' ' +
                        subItem[subField.column_key['0']]
                      : '-'
                  "
                >
                  {{ subItem[subField.column_key["1"]] }}
                  {{
                    subItem[subField.column_key["0"]]
                      | currencyFormat
                        : subItem[subField.column_key["1"]]
                        : subField.column_key["2"]
                  }}
                </span>
              </ng-container>
            </div>
          </ng-container>
        </div>
      </ng-container>
    </ng-container>
  </ng-container>
</div>

<mat-menu #mainColumnMenu="matMenu">
  <!-- <button
    class="menu-alignment menu-item"
    mat-menu-item
    [matMenuTriggerFor]="pinColumns"
  >
    <svg height="16px" viewBox="0 -960 960 960" width="16px" fill="#45546E">
      <path
        d="m624-480 96 96v72H516v228l-36 36-36-36v-228H240v-72l96-96v-264h-48v-72h384v72h-48v264Zm-282 96h276l-66-66v-294H408v294l-66 66Zm138 0Z"
      />
    </svg>
    Pin
  </button> -->
  <button
    class="menu-item"
    [ngStyle]="{
      'border-bottom': 'none'
    }"
    mat-menu-item
    (click)="onClickResetColumns()"
  >
    Reset Columns
  </button>
</mat-menu>

<mat-menu #pinColumns="matMenu">
  <button
    class="menu-item"
    mat-menu-item
    (click)="onClickPinColumnsMenu('left')"
  >
    Pin Left
  </button>
  <button
    class="menu-item"
    [ngStyle]="{
      'border-bottom': 'none'
    }"
    mat-menu-item
    (click)="onClickPinColumnsMenu('right')"
  >
    Pin Right
  </button>
</mat-menu>
