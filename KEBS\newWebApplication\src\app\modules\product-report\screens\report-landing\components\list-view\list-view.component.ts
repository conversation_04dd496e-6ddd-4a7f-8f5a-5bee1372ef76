import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';

import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import * as moment from 'moment';

import { ToasterService } from 'src/app/modules/product-report/screens/report-landing/components/custom-toaster/toaster.service';
import { ReportsService } from 'src/app/modules/product-report/services/reports.service';

@Component({
  selector: 'app-list-view',
  templateUrl: './list-view.component.html',
  styleUrls: ['./list-view.component.scss'],
})
export class ListViewComponent implements OnInit {
  protected _onDestroy = new Subject<void>();

  @Input() list: any = [];
  @Input() fieldConfig: any = [];

  @Output() onScroll: EventEmitter<any> = new EventEmitter<any>();
  @Output() onSort: EventEmitter<any> = new EventEmitter<any>();
  @Output() onClickSearchIcon: EventEmitter<any> = new EventEmitter<any>();
  @Output() onClickCloseSearchIcon: EventEmitter<any> = new EventEmitter<any>();
  @Output() onSearchData: EventEmitter<any> = new EventEmitter<any>();
  @Output() onClickPinMenu: EventEmitter<any> = new EventEmitter<any>();
  @Output() onResetColumns: EventEmitter<any> = new EventEmitter<any>();
  @Output() emitResizeWidthData: EventEmitter<any> = new EventEmitter<any>();
  @Output() refreshPageData: EventEmitter<any> = new EventEmitter<any>();

  selectedMenuIndex: number = null;

  constructor(
    private _toaster: ToasterService,
    private _reportsService: ReportsService
  ) {}

  ngOnInit() {}

  /**
   * @description On Scroll
   */
  onDataScroll() {
    this.onScroll.emit();
  }

  /**
   * @description Open Hyperlink text in New Tab
   * @param link
   */
  openHyperLink(link) {
    if (link && link != '' && typeof link == 'string') {
      window.open(link);
    } else {
      this._toaster.showWarning(
        'Warning ⚠️',
        'Invalid URL or No URL Found!',
        7000
      );
    }
  }

  /**
   * @description Sort action
   * @param {number} sortOrder
   * @param {number} index
   */
  onClickSort(sortOrder: number, index: number) {
    let data = { sortOrder: sortOrder, fieldConfigIndex: index };
    this.onSort.emit(data);
  }

  /**
   * @description Search column
   * @param {number} index
   */
  onClickSearch(index: number) {
    this.onClickSearchIcon.emit(index);
    setTimeout(() => {
      const inputElement = document.getElementById(
        '#inputElement' + index
      ) as HTMLInputElement;
      inputElement.focus();
    }, 100);
  }

  /**
   * @description Search column
   * @param {number} index
   */
  onClickCloseSearch(index: number) {
    this.onClickCloseSearchIcon.emit(index);
  }

  /**
   * @description Search column
   * @param {number} index
   * @param {string} searchParam
   */
  onEnterKeyPressedForSearch(index: number, searchParam: string) {
    this.onSearchData.emit({ index: index, searchParam: searchParam });
  }

  /**
   * @description Set index of selected menu
   * @param {number} index;
   */
  setIndexOfSelectedMenu(index: number) {
    this.selectedMenuIndex = index;
  }

  /**
   * @description On click pin column left or right
   * @param {string} pinType
   */
  onClickPinColumnsMenu(pinType: string) {
    this.onClickPinMenu.emit({
      pinType: pinType,
      index: this.selectedMenuIndex,
    });
  }

  /**
   * @description On reset columns to default
   */
  onClickResetColumns() {
    this.onResetColumns.emit();
  }

  /**
   * @description Emit width change data
   * @param {array} event
   */
  emitWidthChangeData(event) {
    this.emitResizeWidthData.emit(event);
  }

  /**
   * @description Date format function
   * @param value
   * @param format
   * @param isLocalValue
   */
  dateFormatFunction(value, format = 'DD MMM YYYY', isLocalValue = true) {
    if (!value || !moment(value, moment.ISO_8601, true).isValid()) {
      return '-'; // Handle invalid or null values
    }

    // Handle date-only strings without time component
    if (moment(value, 'YYYY-MM-DD', true).isValid()) {
      return moment(value, 'YYYY-MM-DD').format(format); // No UTC conversion
    }

    // Handle ISO 8601 with UTC and local conversion
    const momentObj = moment(value); // Parse the value
    return momentObj.format(format); // Keep in UTC if isLocalValue is false
  }

  /**
   * @description On click action icon
   * @param {object} data
   * @param {string} type
   * @param {string} apiURL
   */
  onClickActionsIcon(
    data,
    type,
    apiURL,
    fileName,
    listIndex,
    fieldIndex,
    iconIndex,
    functionParam?
  ) {
    if (type == 'api') {
      if (!data && !apiURL) {
        this._toaster.showWarning(
          'Warning ⚠️',
          'API Configurations Missing!',
          7000
        );
        return;
      }
      this.list[listIndex][
        `${this.fieldConfig[fieldIndex].id}_${iconIndex}_loader`
      ] = true;
      return new Promise((resolve, reject) =>
        this._reportsService
          .onClickActionsIcon(data, apiURL)
          .pipe(takeUntil(this._onDestroy))
          .subscribe({
            next: (res) => {
              if (res['err'] == false) {
                  if (res['messType'] == 'W') {
                  this._toaster.showWarning('Warning ⚠️', res['msg'], 7000);
                } else {
                  this._toaster.showSuccess('Success ✅', res['msg'], 7000);
                   if (
                  this.fieldConfig[fieldIndex].click_actions[iconIndex]
                    .refreshPage
                ) {
                  this.refreshPageData.emit();
                } else if (
                  this.fieldConfig[fieldIndex].click_actions[iconIndex]
                    .updateLineItem
                ) {
                  this.list[listIndex] = {
                    ...this.list[listIndex],
                    ...res['data'],
                  };
                }
                }
               
              } else {
                this._toaster.showError('Error', res['msg'], 7000);
              }
              this.list[listIndex][
                `${this.fieldConfig[fieldIndex].id}_${iconIndex}_loader`
              ] = false;
              resolve(true);
            },
            error: (err) => {
              this.list[listIndex][
                `${this.fieldConfig[fieldIndex].id}_${iconIndex}_loader`
              ] = false;
              this._toaster.showError(
                'Error',
                err.msg ? err.msg : 'API Failed!',
                7000
              );
              reject();
            },
          })
      );
    } else if (type == 'hyperlink') {
      let link = data[apiURL];
      this.openHyperLink(link);
    } else if (type == 'attachment_download') {
      if (!apiURL) {
        this._toaster.showWarning(
          'Warning ⚠️',
          'API Configurations Missing!',
          7000
        );
        return;
      }
      this.list[listIndex][
        `${this.fieldConfig[fieldIndex].id}_${iconIndex}_loader`
      ] = true;
      fileName = eval(fileName);
      this._reportsService
        .onClickDownloadAttachments(data, apiURL)
        .pipe(takeUntil(this._onDestroy))
        .subscribe(
          (blob) => {
            const url = window.URL.createObjectURL(blob);
            const downloadLink = document.createElement('a');
            downloadLink.href = url;
            downloadLink.download = fileName;
            document.body.appendChild(downloadLink);
            downloadLink.click();
            document.body.removeChild(downloadLink);
            window.URL.revokeObjectURL(url);
            this.list[listIndex][
              `${this.fieldConfig[fieldIndex].id}_${iconIndex}_loader`
            ] = false;
          },
          (error) => {
            this.list[listIndex][
              `${this.fieldConfig[fieldIndex].id}_${iconIndex}_loader`
            ] = false;
            this._toaster.showWarning(
              'Warning ⚠️',
              'No Attachments Found!',
              7000
            );
          }
        );
    } else if (type == 'function') {
      if (apiURL && typeof apiURL === 'string') {
        try {
          this._reportsService.callFunction(apiURL, { key: functionParam, lineItem: this.list[listIndex] });
        } catch (error) {
          console.error('Error executing function:', error);
        }
      } else {
        console.warn(`Invalid Function Call: ${apiURL}`);
      }
    }
  }

  /**
   * @description Click action to redirect
   * @param {object} action
   * @param {object} data
   * @param {string} url
   */
  columnClickAction(action, data, url) {
    if (action) {
      if (action?.action_type == 'hyperlink') {
        this.openHyperLink(url);
      } else if (action?.action_type == 'function') {
        this[action?.function](action, data);
      }
    }
  }
}
