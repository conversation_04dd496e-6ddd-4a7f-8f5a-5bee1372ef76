import {
  Component,
  Input,
  Output,
  OnInit,
  EventEmitter,
  ElementRef,
  ViewChild,
  AfterViewInit,
  SimpleChanges,
  TemplateRef,
  ViewContainerRef,
} from '@angular/core';
import { Overlay, OverlayRef } from '@angular/cdk/overlay';
import { TemplatePortal } from '@angular/cdk/portal';

@Component({
  selector: 'app-search-overlay',
  templateUrl: './search-overlay.component.html',
  styleUrls: ['./search-overlay.component.scss'],
})
export class SearchOverlayComponent implements OnInit, AfterViewInit {
  @Input() recentSearch: any;
  @Input() currentSearchText: any;
  @Output() onEnter: EventEmitter<any> = new EventEmitter<any>();
  @ViewChild('inputField') inputField: ElementRef;

  overlayRef: OverlayRef;
  @ViewChild('triggerSearchBarTemplateRef', { static: false })
  private triggerSearchBarTemplateRef!: TemplateRef<HTMLElement>;
  triggerSearchBar: ElementRef;

  searchText: string = '';

  constructor(
    private _overlay: Overlay,
    private _viewContainerRef: ViewContainerRef
  ) {}

  ngOnInit() {
    if (this.currentSearchText && this.currentSearchText != '') {
      this.searchText = this.currentSearchText;
    } else {
      this.searchText = '';
    }
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.currentSearchText) {
      this.searchText = this.currentSearchText ? this.currentSearchText : '';
    }
  }

  ngAfterViewInit() {
    if (this.inputField && this.inputField.nativeElement) {
      this.inputField.nativeElement.focus();
    }
  }

  /**
   * @description Highlight search text
   * @param {string} recentSearch
   */
  highlightSearch(recentSearch: string) {
    if (!this.searchText) {
      return recentSearch;
    }

    let escapedSearch = this.searchText.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');

    const regex = new RegExp(escapedSearch, 'gi');
    return recentSearch.replace(regex, (match) => `<b>${match}</b>`);
  }

  /**
   * @description Enter for search
   */
  onEnterKeyPressed() {
    this.onEnter.emit(this.searchText.trim());
    this.closeOverlay();
  }

  /**
   * @description Select recent search
   * @param {number} index
   */
  onSelectRecentSearch(index: number) {
    this.searchText = this.recentSearch[index];
    this.onEnterKeyPressed();
  }

  /**
   * To open Search Bar Overlay
   * @param {id} triggerField
   * @param {number} yOffset
   * @param {number} xOffset
   */
  openSearchBarOverlay(triggerField, yOffset, xOffset) {
    if (!this.overlayRef?.hasAttached()) {
      const positionStrategyBuilder = this._overlay.position();

      const positionStrategy = positionStrategyBuilder
        .flexibleConnectedTo(triggerField)
        .withFlexibleDimensions(true)
        .withPush(true)
        .withViewportMargin(25)
        .withGrowAfterOpen(true)
        .withPositions([
          {
            originX: 'start',
            originY: 'bottom',
            overlayX: 'start',
            overlayY: 'top',
          },
        ]);
      positionStrategy.withDefaultOffsetY(yOffset);
      if (xOffset) {
        positionStrategy.withDefaultOffsetX(xOffset);
      }
      const scrollStrategy = this._overlay.scrollStrategies.close();

      this.overlayRef = this._overlay.create({
        positionStrategy,
        scrollStrategy,
        hasBackdrop: true,
        backdropClass: '',
        panelClass: ['pop-up'],
      });

      const templatePortal = new TemplatePortal(
        this.triggerSearchBarTemplateRef,
        this._viewContainerRef
      );

      this.overlayRef.attach(templatePortal);

      this.overlayRef.backdropClick().subscribe(() => {
        this.closeOverlay();
      });
    }
  }

  /**
   * To close the Overlay
   */
  closeOverlay() {
    this.overlayRef?.dispose();
  }
}
