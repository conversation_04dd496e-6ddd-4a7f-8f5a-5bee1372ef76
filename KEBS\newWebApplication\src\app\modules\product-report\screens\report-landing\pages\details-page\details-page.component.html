<div *ngIf="isLoading" class="loading-container">
  <div>
    <img [src]="loadingImage" />
  </div>
  <div class="loading-wrapper">
    <div class="loading">Loading...</div>
  </div>
</div>

<div *ngIf="!isLoading" class="main-container">
  <div class="header">
    <div class="header-1">
      <div
        *ngIf="reportDetails?.ui_configurations?.is_back_icon_visible"
        class="back-button"
        (click)="navigateToPreviousRoute()"
      >
        <mat-icon class="back-icon">navigate_before</mat-icon>
      </div>
      <div
        *ngIf="reportDetails?.ui_configurations?.is_report_name_visible"
        class="report-name"
      >
        {{ reportDetails?.report_name || "-" }}
      </div>
    </div>
    <div
      class="header-2"
      [ngStyle]="{
        'pointer-events': isLoading || isReportLoading ? 'none' : ''
      }"
    >
      <app-search-overlay
        *ngIf="reportDetails?.ui_configurations?.is_search_icon_visible"
        [currentSearchText]="searchParams"
        [recentSearch]="recentSearch"
        (onEnter)="onEnterSearch($event)"
      ></app-search-overlay>
      <div
        *ngIf="reportDetails?.ui_configurations?.is_filter_icon_visible"
        class="svg"
        style="position: relative"
        (click)="openFilterDialog()"
      >
        <svg width="15" height="16" viewBox="0 0 15 16" fill="none">
          <path
            d="M6.88476 15.5C6.63347 15.5 6.42322 15.4154 6.25398 15.2461C6.08475 15.0769 6.00013 14.8666 6.00013 14.6154V8.82691L0.402082 1.71541C0.209782 1.45899 0.181899 1.19232 0.318433 0.915407C0.454966 0.63849 0.685408 0.500031 1.00976 0.500031H13.9905C14.3148 0.500031 14.5452 0.63849 14.6818 0.915407C14.8183 1.19232 14.7904 1.45899 14.5981 1.71541L9.00008 8.82691V14.6154C9.00008 14.8666 8.91547 15.0769 8.74623 15.2461C8.577 15.4154 8.36674 15.5 8.11546 15.5H6.88476ZM7.50011 8.30001L12.4501 2.00001H2.55011L7.50011 8.30001Z"
            fill="#8B95A5"
          />
        </svg>
        <div
          *ngIf="appliedFilter && appliedFilter.length > 0"
          class="filter-applied-circle"
        ></div>
      </div>
      <div
        *ngIf="reportDetails?.ui_configurations?.is_customization_icon_visible"
        class="svg"
        (click)="openCustomizationOverlay(triggerColumnCustomizationField)"
        cdkOverlayOrigin
        #triggerColumnCustomization="cdkOverlayOrigin"
        #triggerColumnCustomizationField
      >
        <svg width="18" height="18" viewBox="0 0 18 18" fill="none">
          <path
            d="M8.99975 17.75C8.78708 17.75 8.609 17.6781 8.4655 17.5343C8.32183 17.3906 8.25 17.2125 8.25 17V13C8.25 12.7875 8.32192 12.6094 8.46575 12.4658C8.60958 12.3219 8.78775 12.25 9.00025 12.25C9.21292 12.25 9.391 12.3219 9.5345 12.4658C9.67817 12.6094 9.75 12.7875 9.75 13V14.25H17C17.2125 14.25 17.3906 14.3219 17.5343 14.4658C17.6781 14.6096 17.75 14.7878 17.75 15.0003C17.75 15.2129 17.6781 15.391 17.5343 15.5345C17.3906 15.6782 17.2125 15.75 17 15.75H9.75V17C9.75 17.2125 9.67808 17.3906 9.53425 17.5343C9.39042 17.6781 9.21225 17.75 8.99975 17.75ZM1 15.75C0.7875 15.75 0.609417 15.6781 0.46575 15.5343C0.321917 15.3904 0.25 15.2122 0.25 14.9998C0.25 14.7871 0.321917 14.609 0.46575 14.4655C0.609417 14.3218 0.7875 14.25 1 14.25H5C5.2125 14.25 5.39058 14.3219 5.53425 14.4658C5.67808 14.6096 5.75 14.7878 5.75 15.0003C5.75 15.2129 5.67808 15.391 5.53425 15.5345C5.39058 15.6782 5.2125 15.75 5 15.75H1ZM4.99975 11.75C4.78708 11.75 4.609 11.6781 4.4655 11.5343C4.32183 11.3906 4.25 11.2125 4.25 11V9.75H1C0.7875 9.75 0.609417 9.67808 0.46575 9.53425C0.321917 9.39042 0.25 9.21225 0.25 8.99975C0.25 8.78708 0.321917 8.609 0.46575 8.4655C0.609417 8.32183 0.7875 8.25 1 8.25H4.25V7C4.25 6.7875 4.32192 6.60942 4.46575 6.46575C4.60958 6.32192 4.78775 6.25 5.00025 6.25C5.21292 6.25 5.391 6.32192 5.5345 6.46575C5.67817 6.60942 5.75 6.7875 5.75 7V11C5.75 11.2125 5.67808 11.3906 5.53425 11.5343C5.39042 11.6781 5.21225 11.75 4.99975 11.75ZM9 9.75C8.7875 9.75 8.60942 9.67808 8.46575 9.53425C8.32192 9.39042 8.25 9.21225 8.25 8.99975C8.25 8.78708 8.32192 8.609 8.46575 8.4655C8.60942 8.32183 8.7875 8.25 9 8.25H17C17.2125 8.25 17.3906 8.32192 17.5343 8.46575C17.6781 8.60958 17.75 8.78775 17.75 9.00025C17.75 9.21292 17.6781 9.391 17.5343 9.5345C17.3906 9.67817 17.2125 9.75 17 9.75H9ZM12.9998 5.75C12.7871 5.75 12.609 5.67808 12.4655 5.53425C12.3218 5.39058 12.25 5.2125 12.25 5V1C12.25 0.7875 12.3219 0.609417 12.4658 0.46575C12.6096 0.321917 12.7878 0.25 13.0003 0.25C13.2129 0.25 13.391 0.321917 13.5345 0.46575C13.6782 0.609417 13.75 0.7875 13.75 1V2.25H17C17.2125 2.25 17.3906 2.32192 17.5343 2.46575C17.6781 2.60958 17.75 2.78775 17.75 3.00025C17.75 3.21292 17.6781 3.391 17.5343 3.5345C17.3906 3.67817 17.2125 3.75 17 3.75H13.75V5C13.75 5.2125 13.6781 5.39058 13.5343 5.53425C13.3904 5.67808 13.2122 5.75 12.9998 5.75ZM1 3.75C0.7875 3.75 0.609417 3.67808 0.46575 3.53425C0.321917 3.39042 0.25 3.21225 0.25 2.99975C0.25 2.78708 0.321917 2.609 0.46575 2.4655C0.609417 2.32183 0.7875 2.25 1 2.25H9C9.2125 2.25 9.39058 2.32192 9.53425 2.46575C9.67808 2.60958 9.75 2.78775 9.75 3.00025C9.75 3.21292 9.67808 3.391 9.53425 3.5345C9.39058 3.67817 9.2125 3.75 9 3.75H1Z"
            fill="#8B95A5"
          />
        </svg>
      </div>
      <!-- <div class="svg">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
          <mask
            id="mask0_15748_178490"
            style="mask-type: alpha"
            maskUnits="userSpaceOnUse"
            x="0"
            y="0"
            width="24"
            height="24"
          >
            <rect width="24" height="24" fill="#D9D9D9" />
          </mask>
          <g mask="url(#mask0_15748_178490)">
            <path
              d="M5 8.80777H19V6.30777C19 6.23077 18.9679 6.16026 18.9038 6.09626C18.8398 6.0321 18.7693 6.00002 18.6923 6.00002H5.30775C5.23075 6.00002 5.16025 6.0321 5.09625 6.09626C5.03208 6.16026 5 6.23077 5 6.30777V8.80777ZM5.30775 21.5C4.80258 21.5 4.375 21.325 4.025 20.975C3.675 20.625 3.5 20.1974 3.5 19.6923V6.30777C3.5 5.8026 3.675 5.37502 4.025 5.02502C4.375 4.67502 4.80258 4.50002 5.30775 4.50002H6.69225V3.15377C6.69225 2.9346 6.76567 2.7516 6.9125 2.60477C7.05933 2.4581 7.24233 2.38477 7.4615 2.38477C7.68083 2.38477 7.86383 2.4581 8.0105 2.60477C8.15733 2.7516 8.23075 2.9346 8.23075 3.15377V4.50002H15.8078V3.13477C15.8078 2.92193 15.8795 2.74368 16.023 2.60002C16.1667 2.45652 16.3449 2.38477 16.5578 2.38477C16.7706 2.38477 16.9488 2.45652 17.0923 2.60002C17.2359 2.74368 17.3078 2.92193 17.3078 3.13477V4.50002H18.6923C19.1974 4.50002 19.625 4.67502 19.975 5.02502C20.325 5.37502 20.5 5.8026 20.5 6.30777V11.0213C20.5 11.2341 20.4282 11.4123 20.2845 11.5558C20.141 11.6994 19.9628 11.7713 19.75 11.7713C19.5372 11.7713 19.359 11.6994 19.2155 11.5558C19.0718 11.4123 19 11.2341 19 11.0213V10.3078H5V19.6923C5 19.7693 5.03208 19.8398 5.09625 19.9038C5.16025 19.9679 5.23075 20 5.30775 20H11.425C11.6378 20 11.8161 20.0718 11.9598 20.2155C12.1033 20.359 12.175 20.5372 12.175 20.75C12.175 20.9628 12.1033 21.141 11.9598 21.2845C11.8161 21.4282 11.6378 21.5 11.425 21.5H5.30775ZM18.1923 22.5C16.9436 22.5 15.8814 22.0622 15.0058 21.1865C14.1301 20.3108 13.6923 19.2487 13.6923 18C13.6923 16.7513 14.1301 15.6892 15.0058 14.8135C15.8814 13.9378 16.9436 13.5 18.1923 13.5C19.4411 13.5 20.5033 13.9378 21.3788 14.8135C22.2544 15.6892 22.6923 16.7513 22.6923 18C22.6923 19.2487 22.2544 20.3108 21.3788 21.1865C20.5033 22.0622 19.4411 22.5 18.1923 22.5ZM18.6348 17.8193V15.5C18.6348 15.3795 18.5911 15.2757 18.5038 15.1885C18.4166 15.1013 18.3128 15.0578 18.1923 15.0578C18.0718 15.0578 17.9679 15.1013 17.8808 15.1885C17.7936 15.2757 17.75 15.3795 17.75 15.5V17.8038C17.75 17.9243 17.7718 18.039 17.8155 18.148C17.859 18.257 17.9276 18.3583 18.0213 18.452L19.5463 19.977C19.6334 20.0642 19.7357 20.1094 19.853 20.1125C19.9702 20.1157 20.0756 20.0705 20.1693 19.977C20.2629 19.8834 20.3098 19.7795 20.3098 19.6655C20.3098 19.5513 20.2629 19.4474 20.1693 19.3538L18.6348 17.8193Z"
              fill="#8B95A5"
            />
          </g>
        </svg>
      </div> -->
      <div
        *ngIf="
          !isReportDownloading &&
          reportDetails?.ui_configurations?.is_download_icon_visible
        "
        class="svg"
        (click)="onReportDownload()"
      >
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
          <mask
            id="mask0_15748_178493"
            style="mask-type: alpha"
            maskUnits="userSpaceOnUse"
            x="0"
            y="0"
            width="24"
            height="24"
          >
            <rect width="24" height="24" fill="#D9D9D9" />
          </mask>
          <g mask="url(#mask0_15748_178493)">
            <path
              d="M12 15.4115C11.8795 15.4115 11.7673 15.3923 11.6635 15.3538C11.5597 15.3154 11.4609 15.2494 11.3672 15.1558L8.25775 12.0463C8.10908 11.8974 8.03567 11.7233 8.0375 11.524C8.0395 11.3247 8.11292 11.1474 8.25775 10.9922C8.41292 10.8372 8.59108 10.7572 8.79225 10.752C8.99358 10.7468 9.17183 10.8218 9.327 10.977L11.25 12.9V5.25C11.25 5.03717 11.3218 4.859 11.4655 4.7155C11.609 4.57183 11.7872 4.5 12 4.5C12.2128 4.5 12.391 4.57183 12.5345 4.7155C12.6782 4.859 12.75 5.03717 12.75 5.25V12.9L14.673 10.977C14.8218 10.8283 14.9984 10.7549 15.2028 10.7568C15.4073 10.7588 15.5871 10.8372 15.7423 10.9922C15.8871 11.1474 15.9621 11.3231 15.9672 11.5192C15.9724 11.7154 15.8974 11.8911 15.7423 12.0463L12.6328 15.1558C12.5391 15.2494 12.4403 15.3154 12.3365 15.3538C12.2327 15.3923 12.1205 15.4115 12 15.4115ZM6.30775 19.5C5.80258 19.5 5.375 19.325 5.025 18.975C4.675 18.625 4.5 18.1974 4.5 17.6923V15.7308C4.5 15.5179 4.57183 15.3398 4.7155 15.1962C4.859 15.0526 5.03717 14.9808 5.25 14.9808C5.46283 14.9808 5.641 15.0526 5.7845 15.1962C5.92817 15.3398 6 15.5179 6 15.7308V17.6923C6 17.7692 6.03208 17.8398 6.09625 17.9038C6.16025 17.9679 6.23075 18 6.30775 18H17.6923C17.7692 18 17.8398 17.9679 17.9038 17.9038C17.9679 17.8398 18 17.7692 18 17.6923V15.7308C18 15.5179 18.0718 15.3398 18.2155 15.1962C18.359 15.0526 18.5372 14.9808 18.75 14.9808C18.9628 14.9808 19.141 15.0526 19.2845 15.1962C19.4282 15.3398 19.5 15.5179 19.5 15.7308V17.6923C19.5 18.1974 19.325 18.625 18.975 18.975C18.625 19.325 18.1974 19.5 17.6923 19.5H6.30775Z"
              fill="#8B95A5"
            />
          </g>
        </svg>
      </div>
      <mat-spinner
        *ngIf="isReportDownloading"
        class="green-spinner"
        diameter="24"
      ></mat-spinner>
      <!-- <div class="svg">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
          <g clip-path="url(#clip0_15748_178496)">
            <path
              d="M13.12 17.0232L8.92096 14.7332C8.37276 15.3193 7.66095 15.7269 6.87803 15.9031C6.09512 16.0793 5.27731 16.0159 4.53088 15.7212C3.78445 15.4266 3.14392 14.9142 2.69253 14.2506C2.24114 13.5871 1.99976 12.8032 1.99976 12.0007C1.99976 11.1982 2.24114 10.4143 2.69253 9.75076C3.14392 9.08725 3.78445 8.57486 4.53088 8.28016C5.27731 7.98547 6.09512 7.92211 6.87803 8.09832C7.66095 8.27452 8.37276 8.68214 8.92096 9.2682L13.121 6.9782C12.8825 6.03428 12.9965 5.03579 13.4416 4.16991C13.8867 3.30402 14.6323 2.63018 15.5386 2.2747C16.445 1.91923 17.4499 1.90651 18.3649 2.23894C19.28 2.57137 20.0424 3.22612 20.5093 4.08046C20.9761 4.93481 21.1153 5.93009 20.9009 6.87975C20.6864 7.82941 20.1329 8.66824 19.3442 9.23901C18.5555 9.80979 17.5857 10.0733 16.6166 9.9802C15.6475 9.88708 14.7456 9.44371 14.08 8.7332L9.87996 11.0232C10.0412 11.6646 10.0412 12.3358 9.87996 12.9772L14.079 15.2672C14.7446 14.5567 15.6464 14.1133 16.6156 14.0202C17.5847 13.9271 18.5545 14.1906 19.3432 14.7614C20.1319 15.3322 20.6854 16.171 20.8999 17.1207C21.1143 18.0703 20.9751 19.0656 20.5083 19.9199C20.0414 20.7743 19.279 21.429 18.3639 21.7615C17.4489 22.0939 16.444 22.0812 15.5376 21.7257C14.6313 21.3702 13.8857 20.6964 13.4406 19.8305C12.9955 18.9646 12.8815 17.9661 13.12 17.0222V17.0232ZM5.99996 14.0002C6.5304 14.0002 7.0391 13.7895 7.41418 13.4144C7.78925 13.0393 7.99996 12.5306 7.99996 12.0002C7.99996 11.4698 7.78925 10.9611 7.41418 10.586C7.0391 10.2109 6.5304 10.0002 5.99996 10.0002C5.46953 10.0002 4.96082 10.2109 4.58575 10.586C4.21068 10.9611 3.99996 11.4698 3.99996 12.0002C3.99996 12.5306 4.21068 13.0393 4.58575 13.4144C4.96082 13.7895 5.46953 14.0002 5.99996 14.0002ZM17 8.0002C17.5304 8.0002 18.0391 7.78949 18.4142 7.41442C18.7892 7.03934 19 6.53064 19 6.0002C19 5.46977 18.7892 4.96106 18.4142 4.58599C18.0391 4.21092 17.5304 4.0002 17 4.0002C16.4695 4.0002 15.9608 4.21092 15.5857 4.58599C15.2107 4.96106 15 5.46977 15 6.0002C15 6.53064 15.2107 7.03934 15.5857 7.41442C15.9608 7.78949 16.4695 8.0002 17 8.0002ZM17 20.0002C17.5304 20.0002 18.0391 19.7895 18.4142 19.4144C18.7892 19.0393 19 18.5306 19 18.0002C19 17.4698 18.7892 16.9611 18.4142 16.586C18.0391 16.2109 17.5304 16.0002 17 16.0002C16.4695 16.0002 15.9608 16.2109 15.5857 16.586C15.2107 16.9611 15 17.4698 15 18.0002C15 18.5306 15.2107 19.0393 15.5857 19.4144C15.9608 19.7895 16.4695 20.0002 17 20.0002Z"
              fill="#8B95A5"
            />
          </g>
          <defs>
            <clipPath id="clip0_15748_178496">
              <rect width="24" height="24" fill="white" />
            </clipPath>
          </defs>
        </svg>
      </div> -->
      <div
        *ngIf="
          !isExpenseZipFileDownloading &&
          reportDetails?.ui_configurations?.is_expense_zip_download_icon_visible
        "
        (click)="
          retrieveAndDownloadFile(
            reportDetails?.api_configurations?.expense_zip_download_api
          )
        "
        class="svg"
      >
        <svg width="20" height="16" viewBox="0 0 20 16" fill="none">
          <path
            d="M2 16C1.45 16 0.979333 15.8043 0.588 15.413C0.196667 15.0217 0.000666667 14.5507 0 14V2C0 1.45 0.196 0.979333 0.588 0.588C0.98 0.196666 1.45067 0.000666667 2 0H8L10 2H18C18.55 2 19.021 2.196 19.413 2.588C19.805 2.98 20.0007 3.45067 20 4V14C20 14.55 19.8043 15.021 19.413 15.413C19.0217 15.805 18.5507 16.0007 18 16H2ZM12 14H14V12H16V10H14V8H16V6H14V4H12V6H14V8H12V10H14V12H12V14Z"
            fill="#FFBD3D"
          />
        </svg>
      </div>
      <mat-spinner
        *ngIf="isExpenseZipFileDownloading"
        class="green-spinner"
        diameter="24"
      ></mat-spinner>
      <div 
        *ngIf="reportDetails?.ui_configurations?.is_help_icon_visible"
        class="svg"
        (click)="openHelpDialog()"
        >
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
          <mask id="mask0_1041_5399" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="24">
            <rect width="24" height="24" fill="#D9D9D9"/>
          </mask>
          <g mask="url(#mask0_1041_5399)">
            <path d="M11.25 16.75H12.75V11H11.25V16.75ZM12 9.2885C12.2288 9.2885 12.4207 9.21108 12.5755 9.05625C12.7303 8.90142 12.8077 8.70958 12.8077 8.48075C12.8077 8.25192 12.7303 8.06008 12.5755 7.90525C12.4207 7.75058 12.2288 7.67325 12 7.67325C11.7712 7.67325 11.5793 7.75058 11.4245 7.90525C11.2697 8.06008 11.1923 8.25192 11.1923 8.48075C11.1923 8.70958 11.2697 8.90142 11.4245 9.05625C11.5793 9.21108 11.7712 9.2885 12 9.2885ZM12.0017 21.5C10.6877 21.5 9.45267 21.2507 8.2965 20.752C7.14033 20.2533 6.13467 19.5766 5.2795 18.7218C4.42433 17.8669 3.74725 16.8617 3.24825 15.706C2.74942 14.5503 2.5 13.3156 2.5 12.0017C2.5 10.6877 2.74933 9.45267 3.248 8.2965C3.74667 7.14033 4.42342 6.13467 5.27825 5.2795C6.13308 4.42433 7.13833 3.74725 8.294 3.24825C9.44967 2.74942 10.6844 2.5 11.9983 2.5C13.3123 2.5 14.5473 2.74933 15.7035 3.248C16.8597 3.74667 17.8653 4.42342 18.7205 5.27825C19.5757 6.13308 20.2528 7.13833 20.7518 8.294C21.2506 9.44967 21.5 10.6844 21.5 11.9983C21.5 13.3123 21.2507 14.5473 20.752 15.7035C20.2533 16.8597 19.5766 17.8653 18.7218 18.7205C17.8669 19.5757 16.8617 20.2528 15.706 20.7518C14.5503 21.2506 13.3156 21.5 12.0017 21.5ZM12 20C14.2333 20 16.125 19.225 17.675 17.675C19.225 16.125 20 14.2333 20 12C20 9.76667 19.225 7.875 17.675 6.325C16.125 4.775 14.2333 4 12 4C9.76667 4 7.875 4.775 6.325 6.325C4.775 7.875 4 9.76667 4 12C4 14.2333 4.775 16.125 6.325 17.675C7.875 19.225 9.76667 20 12 20Z" fill="#5F6C81"/>
          </g>
        </svg>
      </div>
    </div>
  </div>
  <div class="report-container">
    <div class="report-header">
      <div
        *ngIf="isReportDateConfigAvailable"
        class="date-picker"
        [ngStyle]="{
          'pointer-events': isLoading || isReportLoading ? 'none' : ''
        }"
      >
        <div class="svg">
          <svg width="18" height="18" viewBox="0 0 18 18" fill="none">
            <mask
              id="mask0_15748_178526"
              style="mask-type: alpha"
              maskUnits="userSpaceOnUse"
              x="0"
              y="0"
              width="18"
              height="18"
            >
              <rect width="18" height="18" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_15748_178526)">
              <path
                d="M11.0188 13.5C10.5371 13.5 10.1282 13.332 9.7922 12.996C9.4562 12.6599 9.2882 12.2509 9.2882 11.7692C9.2882 11.2874 9.4562 10.8786 9.7922 10.5426C10.1282 10.2066 10.5371 10.0386 11.0188 10.0386C11.5006 10.0386 11.9095 10.2066 12.2456 10.5426C12.5816 10.8786 12.7496 11.2874 12.7496 11.7692C12.7496 12.2509 12.5816 12.6599 12.2456 12.996C11.9095 13.332 11.5006 13.5 11.0188 13.5ZM3.98045 16.125C3.60157 16.125 3.28088 15.9938 3.01838 15.7313C2.75588 15.4688 2.62463 15.1481 2.62463 14.7692V4.73082C2.62463 4.35195 2.75588 4.03126 3.01838 3.76876C3.28088 3.50626 3.60157 3.37501 3.98045 3.37501H5.01882V1.78857H6.1727V3.37501H11.8554V1.78857H12.9804V3.37501H14.0188C14.3977 3.37501 14.7184 3.50626 14.9809 3.76876C15.2434 4.03126 15.3746 4.35195 15.3746 4.73082V14.7692C15.3746 15.1481 15.2434 15.4688 14.9809 15.7313C14.7184 15.9938 14.3977 16.125 14.0188 16.125H3.98045ZM3.98045 15H14.0188C14.0766 15 14.1294 14.976 14.1774 14.9278C14.2256 14.8798 14.2496 14.827 14.2496 14.7692V7.73082H3.74963V14.7692C3.74963 14.827 3.7737 14.8798 3.82182 14.9278C3.86982 14.976 3.9227 15 3.98045 15ZM3.74963 6.60582H14.2496V4.73082C14.2496 4.67307 14.2256 4.6202 14.1774 4.5722C14.1294 4.52407 14.0766 4.50001 14.0188 4.50001H3.98045C3.9227 4.50001 3.86982 4.52407 3.82182 4.5722C3.7737 4.6202 3.74963 4.67307 3.74963 4.73082V6.60582Z"
                fill="#6E7B8F"
              />
            </g>
          </svg>
        </div>
        <input
          class="date-picker-input"
          type="text"
          matInput
          ngxDaterangepickerMd
          [locale]="{ applyLabel: 'Set Date', format: 'DD MMM YYYY' }"
          [drops]="'down'"
          [opens]="'right'"
          [ranges]="dateRanges"
          [showCustomRangeLabel]="false"
          [alwaysShowCalendars]="true"
          [keepCalendarOpeningWithRange]="true"
          [showCancel]="true"
          [dateLimit]="reportDateConfig?.max_allowed_days"
          [linkedCalendars]="true"
          autocomplete="off"
          [(ngModel)]="selectedRange"
          name="daterange"
          (datesUpdated)="datesUpdated($event)"
        />
        <div class="date">
          {{ startDate }} -
          {{ endDate }}
        </div>
      </div>
      <div
        *ngIf="
          !isReportDateConfigAvailable &&
          reportDetails?.ui_configurations?.is_ts_custom_date_filter_visible
        "
        class="custom-ts-date-picker"
        [ngStyle]="{
          'pointer-events': isLoading || isReportLoading ? 'none' : ''
        }"
      >
        <input
          matInput
          [matDatepicker]="picker"
          [formControl]="month"
          placeholder="MMM YYYY"
          style="visibility: hidden; width: 220px; position: absolute"
        />
        <mat-datepicker
          style="display: none"
          #picker
          startView="year"
          [startAt]="startAtDate"
          [selected]="selectedDate"
          (monthSelected)="onSelectMonth($event, picker)"
        ></mat-datepicker>
        <div class="svg" (click)="picker.open()">
          <svg width="18" height="18" viewBox="0 0 18 18" fill="none">
            <mask
              id="mask0_15748_178526"
              style="mask-type: alpha"
              maskUnits="userSpaceOnUse"
              x="0"
              y="0"
              width="18"
              height="18"
            >
              <rect width="18" height="18" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_15748_178526)">
              <path
                d="M11.0188 13.5C10.5371 13.5 10.1282 13.332 9.7922 12.996C9.4562 12.6599 9.2882 12.2509 9.2882 11.7692C9.2882 11.2874 9.4562 10.8786 9.7922 10.5426C10.1282 10.2066 10.5371 10.0386 11.0188 10.0386C11.5006 10.0386 11.9095 10.2066 12.2456 10.5426C12.5816 10.8786 12.7496 11.2874 12.7496 11.7692C12.7496 12.2509 12.5816 12.6599 12.2456 12.996C11.9095 13.332 11.5006 13.5 11.0188 13.5ZM3.98045 16.125C3.60157 16.125 3.28088 15.9938 3.01838 15.7313C2.75588 15.4688 2.62463 15.1481 2.62463 14.7692V4.73082C2.62463 4.35195 2.75588 4.03126 3.01838 3.76876C3.28088 3.50626 3.60157 3.37501 3.98045 3.37501H5.01882V1.78857H6.1727V3.37501H11.8554V1.78857H12.9804V3.37501H14.0188C14.3977 3.37501 14.7184 3.50626 14.9809 3.76876C15.2434 4.03126 15.3746 4.35195 15.3746 4.73082V14.7692C15.3746 15.1481 15.2434 15.4688 14.9809 15.7313C14.7184 15.9938 14.3977 16.125 14.0188 16.125H3.98045ZM3.98045 15H14.0188C14.0766 15 14.1294 14.976 14.1774 14.9278C14.2256 14.8798 14.2496 14.827 14.2496 14.7692V7.73082H3.74963V14.7692C3.74963 14.827 3.7737 14.8798 3.82182 14.9278C3.86982 14.976 3.9227 15 3.98045 15ZM3.74963 6.60582H14.2496V4.73082C14.2496 4.67307 14.2256 4.6202 14.1774 4.5722C14.1294 4.52407 14.0766 4.50001 14.0188 4.50001H3.98045C3.9227 4.50001 3.86982 4.52407 3.82182 4.5722C3.7737 4.6202 3.74963 4.67307 3.74963 4.73082V6.60582Z"
                fill="#6E7B8F"
              />
            </g>
          </svg>
        </div>
        <div class="date" (click)="picker.open()">
          {{ startDate }} -
          {{ endDate }}
        </div>
        <mat-form-field appearance="outline" style="margin-top: 10px">
          <mat-select
            [formControl]="weekNumber"
            placeholder="Week Number"
            (selectionChange)="onValueChanged()"
          >
            <mat-option *ngFor="let week of weeksOfMonth" [value]="week.value">
              {{ week.viewValue }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
      <div
        class="filter-display"
        *ngIf="reportDetails?.filter_sub_application_id && !isReportLoading"
      >
        <app-filter-display
          [applicationId]="reportId"
          [internalApplicationId]="reportDetails?.filter_sub_application_id"
          [chipMaxLength]="reportDetails?.filter_max_chip_count || 6"
        ></app-filter-display>
      </div>
    </div>
    <div *ngIf="isReportLoading" class="report-loading-container">
      <div>
        <img [src]="loadingImage" />
      </div>
      <div class="loading-wrapper">
        <div class="loading">Loading...</div>
      </div>
    </div>
    <div
      *ngIf="!isReportLoading && reportData.length == 0"
      class="report-loading-container"
    >
      <svg
        style="margin-right: 100px"
        width="300"
        height="250"
        viewBox="0 0 250 300"
        fill="none"
      >
        <path
          opacity="0.5"
          d="M151.561 242.824C151.561 242.824 191.123 261.489 206.818 268.652C219.25 274.326 283.265 292.476 290.891 287.317C298.517 282.158 315.355 267.998 309.796 258.296C304.237 248.595 275.003 231.176 261.022 228.631C247.04 226.086 207.063 237.655 207.063 237.655L172.963 243.531L151.561 242.824Z"
          fill="url(#paint0_linear_8536_91278)"
        />
        <path
          d="M180.884 159.369L136.797 191.894L148.552 164.223L192.703 136.1L180.884 159.369Z"
          fill="#E2E7EC"
        />
        <path
          d="M147.821 164.794L192.706 136.603V213.489L151.562 242.432L147.821 164.794Z"
          fill="#B9C0CA"
        />
        <path
          d="M192.703 136.603L261.526 152.757V229.672L192.703 213.517V136.603Z"
          fill="#E9EFF1"
        />
        <path
          d="M147.821 164.79L218.141 181.278V258.193L151.562 242.854L147.821 164.79Z"
          fill="#E8E9EE"
        />
        <path
          d="M216.641 181.307L261.526 153.115V230.002L216.641 258.194V181.307Z"
          fill="#B9C0CA"
        />
        <mask
          id="mask0_8536_91278"
          style="mask-type: luminance"
          maskUnits="userSpaceOnUse"
          x="216"
          y="153"
          width="46"
          height="106"
        >
          <path
            d="M216.641 181.307L261.526 153.115V230.002L216.641 258.194V181.307Z"
            fill="white"
          />
        </mask>
        <g mask="url(#mask0_8536_91278)">
          <rect
            x="233.313"
            y="146.708"
            width="14.1066"
            height="117.893"
            fill="#C7D0D9"
          />
        </g>
        <path
          d="M236.825 204.153L280.976 176.031L260.793 153.115L216.642 181.238L236.825 204.153Z"
          fill="#E2E7EC"
        />
        <path
          opacity="0.5"
          d="M113.39 244.761L182.831 280.36L189.314 261.496L121.182 238.757L113.39 244.761Z"
          fill="url(#paint1_linear_8536_91278)"
        />
        <path
          d="M111.494 186.24L135.438 188.134L130.121 240.391C130.121 240.391 129.655 240.913 128.525 240.71C127.396 240.508 126.211 239.877 126.211 239.877L122.649 198.334C122.649 198.334 120.59 213.023 120.734 217.156C120.842 220.27 121.903 224.068 122.471 227.317C122.96 230.114 123.988 236.796 123.988 236.796C123.988 236.796 122.773 237.353 122.127 237.479C121.481 237.604 120.652 236.913 120.652 236.913C120.652 236.913 112.257 220.562 112.149 217.457C112.041 214.352 111.494 186.24 111.494 186.24Z"
          fill="url(#paint2_linear_8536_91278)"
        />
        <path
          d="M120.281 236.836C120.281 236.836 119.215 238.763 117.89 239.938C116.565 241.112 113.951 242.515 113.355 243.152C112.759 243.789 113.41 244.729 113.41 244.729L119.817 244.504L124.937 240.158L124.222 236.017L120.281 236.836Z"
          fill="#515965"
        />
        <path
          d="M130.723 239.067L132.219 243.23C132.219 243.23 129.763 245.139 126.369 245.67C122.975 246.201 120.636 245.385 120.636 245.385C120.636 245.385 120.023 244.146 121.178 243.617C121.709 243.374 122.898 242.363 123.941 241.068C124.413 240.484 126.188 239.226 126.188 239.226L130.723 239.067Z"
          fill="#515965"
        />
        <path
          d="M141.449 158.996C141.449 158.996 143.412 161.991 144.615 161.19C145.818 160.388 153.331 152.663 153.331 152.663C153.331 152.663 155.566 150.937 155.979 151.347C156.392 151.757 155.234 152.663 155.234 152.663C155.234 152.663 157.672 152.903 158.434 152.903C159.196 152.903 160.799 154.124 159.751 154.534C158.703 154.945 154.638 155.047 154.638 155.047C154.638 155.047 148.466 166.757 145.25 167.587C142.034 168.418 137.602 163.722 137.602 163.722L141.449 158.996Z"
          fill="#FACAC4"
        />
        <path
          d="M110.092 159.21C110.092 159.21 108.805 161.854 107.639 161.219C106.473 160.585 99.9302 153.662 99.9302 153.662C99.9302 153.662 97.7513 152.323 97.2185 152.564C96.6857 152.805 97.9992 153.662 97.9992 153.662C97.9992 153.662 94.7936 153.923 94.1906 154.166C93.5875 154.41 92.8851 155.155 93.7969 155.494C94.7087 155.833 98.1989 156.075 98.1989 156.075C98.1989 156.075 103.051 165.694 106.727 166.819C110.403 167.944 113.535 163.512 113.535 163.512L110.092 159.21Z"
          fill="#FACAC4"
        />
        <path
          d="M121.994 151.115V146.388C121.994 146.388 119.552 146.512 118.559 144.871C117.566 143.231 117.298 138.574 117.298 138.574L118.046 136.415L125.402 136.103L129.267 139.408L127.468 142.596L127.125 143.209L128.84 150.456L121.994 151.115Z"
          fill="#FACAC4"
        />
        <mask
          id="mask1_8536_91278"
          style="mask-type: luminance"
          maskUnits="userSpaceOnUse"
          x="117"
          y="136"
          width="13"
          height="16"
        >
          <path
            d="M121.994 151.115V146.388C121.994 146.388 119.552 146.512 118.559 144.871C117.566 143.231 117.298 138.574 117.298 138.574L118.046 136.415L125.402 136.103L129.267 139.408L127.468 142.596L127.125 143.209L128.84 150.456L121.994 151.115Z"
            fill="white"
          />
        </mask>
        <g mask="url(#mask1_8536_91278)">
          <path
            d="M126.78 141.897C124.606 144.61 120.846 146.506 120.356 146.506C119.866 146.506 123.09 149.909 123.09 149.909L125.713 151.115L129.436 150.323L130.764 146.506L128.418 142.176C128.418 142.176 128.954 139.185 126.78 141.897Z"
            fill="url(#paint3_linear_8536_91278)"
          />
        </g>
        <path
          d="M108.53 161.108L112.145 165.994L114.451 163.551C114.451 163.551 112.698 173.197 112.145 177.824C111.593 182.45 111.491 187.058 111.491 187.058C111.491 187.058 117.279 189.214 121.594 189.577C125.91 189.94 135.955 189.006 135.955 189.006L134.659 161.852L138.421 165.357L142.839 160.239C142.839 160.239 135.234 148.513 133.796 147.83C132.358 147.147 128.36 147.83 128.36 147.83C128.36 147.83 125.523 151.199 124.373 151.246C123.222 151.293 120.67 147.83 120.67 147.83C120.67 147.83 116.594 147.177 115.408 148.513C114.223 149.85 108.53 161.108 108.53 161.108Z"
          fill="url(#paint4_linear_8536_91278)"
        />
        <path
          d="M117.298 138.626C117.298 138.626 118.165 136.432 119.296 136.814C120.367 137.176 121.295 138.037 122.627 138.441C123.96 138.846 124.107 137.03 125.012 137.336C125.918 137.643 125.32 140.314 125.958 140.812C126.597 141.309 126.597 137.336 127.957 138.406C129.318 139.476 126.538 142.486 126.538 142.486C126.538 142.486 126.894 144.337 127.008 144.463C127.123 144.588 127.11 143.116 127.11 143.116C127.11 143.116 128.409 142.616 128.963 140.359C129.516 138.102 129.756 137.203 128.963 135.563C128.169 133.923 127.943 132.639 125.958 131.866C123.974 131.092 122.468 131.395 121.08 131.866C119.693 132.337 118.688 133.272 118.046 134.204C116.55 136.414 117.298 138.626 117.298 138.626Z"
          fill="#515965"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M251.058 80.7381C249.481 81.2217 216.843 100.815 216.843 100.815L226.367 101.527L233.136 109.032C233.136 109.032 252.634 80.2546 251.058 80.7381Z"
          fill="#E8E9EE"
        />
        <mask
          id="mask2_8536_91278"
          style="mask-type: luminance"
          maskUnits="userSpaceOnUse"
          x="216"
          y="80"
          width="36"
          height="30"
        >
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M251.058 80.7381C249.481 81.2217 216.843 100.815 216.843 100.815L226.367 101.527L233.136 109.032C233.136 109.032 252.634 80.2546 251.058 80.7381Z"
            fill="white"
          />
        </mask>
        <g mask="url(#mask2_8536_91278)">
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M226.366 101.528L251.057 80.7388L233.135 109.033L226.366 101.528Z"
            fill="#B9C0CA"
          />
        </g>
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M246.154 103.442L234.708 102.242L251.067 80.9403L246.154 103.442Z"
          fill="#E8E9EE"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M225.078 117.634C225.793 116.116 226.513 114.544 227.238 112.919L228.422 113.451C227.693 115.084 226.969 116.663 226.251 118.19L225.078 117.634ZM220.423 126.897C221.213 125.434 222.01 123.902 222.813 122.3L223.971 122.885C223.162 124.498 222.359 126.042 221.563 127.517L220.423 126.897ZM217.881 131.403C216.96 132.961 216.048 134.422 215.145 135.784L216.225 136.505C217.14 135.123 218.064 133.644 218.997 132.067L217.881 131.403ZM208.821 143.89C209.914 142.752 211.024 141.453 212.15 139.993L213.176 140.79C212.022 142.285 210.882 143.619 209.754 144.793L208.821 143.89ZM205.012 147.251C203.496 148.341 202.02 149.095 200.585 149.516L200.949 150.765C202.532 150.3 204.138 149.48 205.767 148.309L205.012 147.251ZM191.44 147.389C192.866 148.543 194.315 149.298 195.791 149.663L195.481 150.926C193.812 150.514 192.194 149.67 190.626 148.402L191.44 147.389ZM187.945 143.693C187.865 143.588 187.784 143.481 187.704 143.373C186.661 141.963 185.902 140.625 185.419 139.362L184.208 139.828C184.736 141.208 185.553 142.648 186.663 144.149C186.747 144.261 186.831 144.373 186.915 144.483L187.945 143.693ZM187.634 130.606C186.234 131.767 185.333 133.099 184.966 134.585L183.707 134.272C184.145 132.498 185.205 130.932 186.809 129.603L187.634 130.606ZM191.967 128.162C193.426 127.59 195.067 127.124 196.846 126.772L196.595 125.496C194.743 125.862 193.029 126.349 191.495 126.95L191.967 128.162ZM206.997 126.028C205.272 125.967 203.553 125.995 201.879 126.113L201.788 124.816C203.508 124.694 205.273 124.665 207.043 124.728L206.997 126.028ZM212.157 126.484C213.895 126.732 215.576 127.073 217.164 127.499L217.499 126.242C215.86 125.802 214.128 125.452 212.339 125.196L212.157 126.484ZM226.285 131.785C225.099 130.817 223.652 129.948 221.982 129.193L222.515 128.007C224.283 128.806 225.825 129.733 227.103 130.776L226.285 131.785ZM229.377 135.694C229.69 136.423 229.89 137.183 229.974 137.973C230.063 138.809 230.094 139.649 230.067 140.494L231.363 140.536C231.392 139.631 231.359 138.73 231.263 137.834C231.165 136.914 230.932 136.028 230.568 135.179L229.377 135.694ZM227.297 150.187C228.132 148.607 228.778 147.045 229.236 145.498L230.479 145.869C229.996 147.5 229.317 149.142 228.443 150.796L227.297 150.187ZM224.553 154.537C223.594 155.848 222.509 157.169 221.299 158.501L222.258 159.377C223.497 158.012 224.611 156.656 225.598 155.307L224.553 154.537ZM213.856 165.539C215.232 164.395 216.521 163.259 217.722 162.129L218.609 163.079C217.388 164.227 216.079 165.381 214.683 166.541L213.856 165.539ZM209.771 168.76C208.438 169.761 207.039 170.767 205.576 171.777L206.311 172.849C207.788 171.829 209.201 170.813 210.547 169.802L209.771 168.76ZM198.526 176.369C199.447 175.8 200.349 175.233 201.229 174.667L201.929 175.763C201.041 176.333 200.134 176.904 199.206 177.477L198.526 176.369Z"
          fill="#6E7B8F"
        />
        <defs>
          <linearGradient
            id="paint0_linear_8536_91278"
            x1="145.747"
            y1="249.296"
            x2="157.329"
            y2="301.061"
            gradientUnits="userSpaceOnUse"
          >
            <stop stop-color="#C4CDD1" />
            <stop offset="1" stop-color="#C6CFD3" stop-opacity="0.01" />
          </linearGradient>
          <linearGradient
            id="paint1_linear_8536_91278"
            x1="110.981"
            y1="254.507"
            x2="124.408"
            y2="289.435"
            gradientUnits="userSpaceOnUse"
          >
            <stop stop-color="#C4CDD1" />
            <stop offset="1" stop-color="#C6CFD3" stop-opacity="0.01" />
          </linearGradient>
          <linearGradient
            id="paint2_linear_8536_91278"
            x1="137.169"
            y1="240.486"
            x2="135.254"
            y2="185.821"
            gradientUnits="userSpaceOnUse"
          >
            <stop stop-color="#A1A7B8" />
            <stop offset="1" stop-color="#7B8195" />
          </linearGradient>
          <linearGradient
            id="paint3_linear_8536_91278"
            x1="127.81"
            y1="147.942"
            x2="127.81"
            y2="143.362"
            gradientUnits="userSpaceOnUse"
          >
            <stop stop-color="#FBD8D6" />
            <stop offset="1" stop-color="#EAC0BE" stop-opacity="0.7" />
          </linearGradient>
          <linearGradient
            id="paint4_linear_8536_91278"
            x1="91.1745"
            y1="166.752"
            x2="127.371"
            y2="204.81"
            gradientUnits="userSpaceOnUse"
          >
            <stop stop-color="#E5E9F0" />
            <stop offset="1" stop-color="#D2DAE8" />
          </linearGradient>
        </defs>
      </svg>
      <div class="no-data-msg">No Data Found!</div>
      <div class="clear-filter" (click)="clearFilters()">Clear Filters</div>
    </div>
    <div
      *ngIf="!isReportLoading && reportData.length > 0"
      class="report-list-view-container"
    >
      <ng-container
        *ngIf="
          reportDetails?.summary_card_config &&
          reportDetails?.ui_configurations?.is_summary_card_visible
        "
      >
        <app-prev-next-scroll-carousel-dashboard
          style="padding: 16px 16px 0px 16px"
          [gap]="'12px'"
        >
          <div
            *ngFor="let item of reportDetails?.summary_card_config"
            class="summary-card"
            [ngClass]="{ 'selected-summary-card': selectedCard == item.id }"
            (click)="onSelectSummaryCard(item.id)"
          >
            <div class="section">
              <div [innerHTML]="item.icon | svgSecurityBypass"></div>
              <div class="text">{{ item.label }}</div>
            </div>
            <div class="count">
              {{ reportSummaryCardData[item.value_key] }}
            </div>
          </div>
        </app-prev-next-scroll-carousel-dashboard>
      </ng-container>
      <app-list-view
        *ngIf="
          !reportDetails?.report_version || reportDetails?.report_version == 1
        "
        [list]="reportData"
        [fieldConfig]="reportColumnConfig"
        (onSort)="onClickSort($event)"
        (onScroll)="onScrollReport()"
        (onClickSearchIcon)="onClickSearchIcon($event)"
        (onClickCloseSearchIcon)="onClickCloseSearchIcon($event)"
        (onSearchData)="onSearchData($event)"
        (onResetColumns)="onResetColumns()"
        (onClickPinMenu)="onClickPinMenu($event)"
        (emitResizeWidthData)="emitResizeWidthData($event)"
        (refreshPageData)="refreshPageData()"
      ></app-list-view>
      <app-list-view-v2
        *ngIf="
          reportDetails?.report_version && reportDetails?.report_version == 2
        "
        [list]="reportData"
        [reportDetails]="reportDetails"
        [fieldConfig]="reportColumnConfig"
        [subFieldConfig]="subReportColumnConfig"
        (onSort)="onClickSort($event)"
        (onScroll)="onScrollReport()"
        (onClickSearchIcon)="onClickSearchIcon($event)"
        (onClickCloseSearchIcon)="onClickCloseSearchIcon($event)"
        (onSearchData)="onSearchData($event)"
        (onResetColumns)="onResetColumns()"
        (onClickPinMenu)="onClickPinMenu($event)"
        (emitResizeWidthData)="emitResizeWidthData($event)"
        (refreshPageData)="refreshPageData()"
      ></app-list-view-v2>
    </div>
  </div>
</div>

<!-- Column Customization Overlay -->
<ng-template
  #triggerColumnCustomizationTemplateRef
  cdkConnectedOverlay
  [cdkConnectedOverlayOrigin]="triggerColumnCustomization"
>
  <app-column-customization-overlay
    [customization]="reportColumnConfig"
    (onApply)="onApplyColumnCustomization($event)"
  ></app-column-customization-overlay>
</ng-template>
