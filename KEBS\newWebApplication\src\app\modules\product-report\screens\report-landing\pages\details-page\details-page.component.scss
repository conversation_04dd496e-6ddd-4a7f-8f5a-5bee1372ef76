.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: var(--reportDynamicHeight);

  ::ng-deep .green-spinner circle {
    stroke: var(--reportPrimaryColor) !important;
  }

  .loading-wrapper {
    display: flex;
    vertical-align: center;
    justify-content: center;
    align-items: center;
  }

  .loading {
    color: rgba(0, 0, 0, 0.3);
    font-size: 16px;
  }

  .loading::before {
    content: "Loading...";
    position: absolute;
    overflow: hidden;
    max-width: 7em;
    white-space: nowrap;
    background: linear-gradient(
      270deg,
      var(--reportPrimaryColor) 0%,
      var(--reportPrimaryColor) 105.29%
    );
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: loading 6s linear infinite;
  }

  @keyframes loading {
    0% {
      max-width: 0;
    }
  }
}

.main-container {
  height: var(--reportDynamicHeight);
  background-color: #f1f3f8;

  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 24px;
    background-color: white;
    height: 60px;

    .header-1 {
      display: flex;
      align-items: center;

      .back-button {
        width: 16px;
        height: 16px;
        margin-right: 10px;
        border: 1px solid #526179;
        border-radius: 4px;
        cursor: pointer;

        .back-icon {
          font-size: 14px;
          color: #526179;
        }
      }

      .report-name {
        font-family: var(--reportFontFamily);
        font-size: 16px;
        font-weight: 700;
        color: #111434;
      }
    }

    .header-2 {
      display: flex;
      align-items: center;
      gap: 16px;

      .svg {
        cursor: pointer;

        .filter-applied-circle {
          position: absolute;
          bottom: 12px;
          left: 10px;
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background-color: var(--reportPrimaryColor);
        }
      }

      .search-ui {
        width: 350px;
        height: 36px;
        padding: 0px 8px;
        border: 2px solid #dadce2;
        border-radius: 8px;
        cursor: text;

        .search-text {
          font-family: var(--reportFontFamily);
          font-size: 11px;
          font-weight: 400;
          color: #5f6c81;
          width: 280px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      ::ng-deep .green-spinner circle {
        stroke: var(--reportPrimaryColor) !important;
      }
    }
  }

  .report-container {
    display: flex;
    flex-direction: column;
    height: var(--reportDynamicSubHeight);
    background-color: #fff;
    margin: 10px 24px;

    .report-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 30px;
      padding: 0px 16px;
      height: 50px;

      .date-picker {
        display: flex;
        align-items: center;
        gap: 4px;

        ::ng-deep .mat-input-element {
          caret-color: transparent !important;
        }

        ::ng-deep .md-drppicker {
          margin-top: 15px !important;
          font-family: var(--reportFontFamily) !important;

          ::ng-deep .btn {
            background-color: var(--reportPrimaryColor) !important;
            color: white !important;
            border-radius: 4px;
            padding: 0px 13px;
          }

          ::ng-deep .btn-default {
            background-color: white !important;
            border: 1px solid #dadce2 !important;
            color: #45546e !important;
            border-radius: 4px;
            padding: 0px 12px;
          }

          button.active {
            background-color: var(--reportPrimaryColor) !important;
            color: white !important;
          }

          button {
            color: #45546e !important;
          }

          td.active:hover {
            background-color: var(--reportPrimaryColor) !important;
            color: white !important;
          }

          td.today {
            background-color: var(--reportPrimaryColor) !important;
            color: white !important;
          }

          td.start-date {
            background-color: var(--reportPrimaryColor) !important;
            color: white !important;
          }

          td.end-date {
            background-color: var(--reportPrimaryColor) !important;
            color: white !important;
          }
        }

        input {
          outline: none;
          border: none;
          color: transparent;
          text-indent: -9999px;
        }

        .date-picker-input {
          width: 200px;
          position: absolute;
          z-index: 9999999;
          cursor: pointer;
        }

        .svg {
          cursor: pointer;
        }

        .date {
          font-family: var(--reportFontFamily);
          font-size: 14px;
          font-weight: 600;
          color: #45546e;
          cursor: pointer;
          width: max-content;
        }
      }

      .custom-ts-date-picker {
        display: flex;
        align-items: center;
        gap: 12px;

        .svg {
          cursor: pointer;
        }

        .date {
          font-family: var(--reportFontFamily);
          font-size: 14px;
          font-weight: 600;
          color: #45546e;
          cursor: pointer;
          width: max-content;
        }
      }

      .filter-display {
        width: 70%;
      }
    }
  }

  .report-loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: var(--reportDynamicListViewHeight);

    ::ng-deep .green-spinner circle {
      stroke: var(--reportPrimaryColor) !important;
    }

    .loading-wrapper {
      display: flex;
      vertical-align: center;
      justify-content: center;
      align-items: center;
    }

    .loading {
      color: rgba(0, 0, 0, 0.3);
      font-size: 16px;
    }

    .loading::before {
      content: "Loading...";
      position: absolute;
      overflow: hidden;
      max-width: 7em;
      white-space: nowrap;
      background: linear-gradient(
        270deg,
        var(--reportPrimaryColor) 0%,
        var(--reportPrimaryColor) 105.29%
      );
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      animation: loading 6s linear infinite;
    }

    @keyframes loading {
      0% {
        max-width: 0;
      }
    }

    .no-data-msg {
      font-family: var(--reportFontFamily);
      font-size: 14px;
      font-weight: 500;
      color: #111434;
    }

    .clear-filter {
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: var(--reportFontFamily);
      font-weight: 500;
      color: white;
      cursor: pointer;
      font-size: 12px;
      padding: 4px 8px;
      border-radius: 8px;
      background: linear-gradient(
        270deg,
        var(--reportPrimaryColor) 0%,
        var(--reportPrimaryColor) 105.29%
      );
      margin-top: 12px;
    }
  }

  .report-list-view-container {
    display: flex;
    flex-direction: column;
    gap: 12px;

    .summary-card {
      display: flex;
      align-items: center;
      justify-content: space-between;
      border: 1px solid #e8e9ee;
      border-radius: 4px;
      cursor: pointer;
      height: 48px;
      padding: 0px 8px;
      min-width: 160px;
      max-width: 160px;
      gap: 12px;

      .section {
        display: flex;
        align-items: center;
        gap: 8px;

        .text {
          font-family: var(--reportFontFamily);
          font-size: 12px;
          font-weight: 500;
          color: #7d838b;
        }
      }

      .count {
        font-family: var(--reportFontFamily);
        font-size: 16px;
        font-weight: 500;
        color: #5f6c81;
      }
    }

    .selected-summary-card {
      background-color: var(--reportSecondaryColor1);
      border: 1px solid var(--reportPrimaryColor);
      box-shadow: 0px 4px 4px 0px var(--reportSecondaryColor1);
    }
  }
}
