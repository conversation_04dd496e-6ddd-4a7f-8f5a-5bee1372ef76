import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { ReportLandingRoutingModule } from './report-landing-routing.module';

import { ProductReportModule } from '../../product-report.module';
import { ProductDashboardModule } from 'src/app/modules/product-dashboard/product-dashboard.module';
import { SharedLazyLoadedModule } from '../../../project-management/shared-lazy-loaded/shared-lazy-loaded.module';
import { SharedComponentsModule } from '../../../../app-shared/app-shared-components/components.module';

import { LandingPageComponent } from './pages/landing-page/landing-page.component';
import { DetailsPageComponent } from './pages/details-page/details-page.component';
import { ListViewComponent } from './components/list-view/list-view.component';
import { SearchOverlayComponent } from './components/search-overlay/search-overlay.component';
import { ColumnCustomizationOverlayComponent } from './components/column-customization-overlay/column-customization-overlay.component';
import { CustomToasterComponent } from './components/custom-toaster/custom-toaster.component';
import { ListViewV2Component } from './components/list-view-v2/list-view-v2.component';

import { MatDividerModule } from '@angular/material/divider';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TooltipModule } from 'ng2-tooltip-directive';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { OverlayModule } from '@angular/cdk/overlay';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { NgxDaterangepickerMd } from 'ngx-daterangepicker-material';
import { InfiniteScrollModule } from 'ngx-infinite-scroll';
import { MatMenuModule } from '@angular/material/menu';
import { MatDialogModule } from '@angular/material/dialog';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatSelectModule } from '@angular/material/select';
import { ReportHelpComponent } from './components/report-help/report-help.component';

@NgModule({
  declarations: [
    LandingPageComponent,
    DetailsPageComponent,
    ListViewComponent,
    SearchOverlayComponent,
    ColumnCustomizationOverlayComponent,
    CustomToasterComponent,
    ListViewV2Component,
    ReportHelpComponent,
  ],
  imports: [
    CommonModule,
    ReportLandingRoutingModule,
    ProductReportModule,
    ProductDashboardModule,
    SharedLazyLoadedModule,
    SharedComponentsModule,
    MatDividerModule,
    MatTooltipModule,
    TooltipModule,
    MatIconModule,
    MatButtonToggleModule,
    FormsModule,
    ReactiveFormsModule,
    MatInputModule,
    DragDropModule,
    OverlayModule,
    MatCheckboxModule,
    MatProgressSpinnerModule,
    NgxDaterangepickerMd.forRoot(),
    InfiniteScrollModule,
    MatMenuModule,
    MatDialogModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatSelectModule,
  ],
  exports: [DetailsPageComponent],
})
export class ReportLandingModule {}
