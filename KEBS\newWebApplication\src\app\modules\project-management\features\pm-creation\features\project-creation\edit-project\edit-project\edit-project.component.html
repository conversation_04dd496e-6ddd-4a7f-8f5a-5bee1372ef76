<div class="editproject-creation-style">
    <div class="loader-container" *ngIf="loading">
        <div class="loader"></div>
    </div>
    <span *ngIf=!loading>
        <div class="row header">
            <div class="col-4"></div>
            <div class="header-title col-4">
                Edit Project
            </div>
            <div class="col-4">
                <!-- <mat-icon class="minimize-button" (click)="onMinimizeClick()">minimize</mat-icon> -->
                <mat-icon class="close-button" (click)="onCloseClick()">clear</mat-icon>
            </div>
        </div>

        <div class="row">
            <div class="mat-body">
                <form [formGroup]="stepperFormGroup">
                    <img [src]="projectImage ? projectImage : 'https://assets.kebs.app/MicrosoftTeams-image%20(16).png'"
                        class="watermark" />
                    <!-- <mat-horizontal-stepper [linear]="true" labelPosition="bottom"> -->
                    <app-stepper [stepperData]="stepperData" [stepper_color]="stepper_color" [stepper_font]="stepper_font" [mode]="edit"
                        (stepperChange)="onStepperChange($event)"></app-stepper>
                    <!-- Project Details step -->
                    <!-- <mat-step label="{{('project_details' | checkLabel : this.formConfig: 'edit-project': 'Project Details')}}">
                       <ng-template matStepperIcon="edit">
                           <mat-icon>{{ edit ? 'home' : 'check' }}</mat-icon>
                       </ng-template> -->
                    <!-- First row -->
                    <div *ngIf="detailStepper" class="col-12 content" style="overflow:hidden;padding-bottom: 30px;">
                        <div style="display:flex;margin-top: 3px;">
                            <!-- Id field-->
                            <div class="col-3" *ngIf="('project_code' | checkActive : this.formConfig: 'edit-project')">
                                <div class="content-title">
                                    {{('project_code' | checkLabel : this.formConfig: 'edit-project': 'Project
                                    Code')}}
                                    <span class="required-star"
                                        *ngIf="('project_code' | checkMandatedField : this.formConfig: 'edit-project')">
                                        &nbsp;*</span>
                                    <span *ngIf="('project_code' | checkInfoIcon : this.formConfig: 'edit-project')">
                                        <mat-icon class="info-icon"
                                            tooltip="{{('project_code' | checkTooltip : this.formConfig: 'edit-project': 'Project code')}}"
                                            matTooltipPosition="above">
                                            info_outline
                                        </mat-icon>
                                    </span>
                                </div>
                                <!-- <div class="row"> -->
                                <mat-form-field [ngStyle]="{'background':project_code_color}" class="input-field project_code"
                                    appearance="outline">
                                    <input class="font-family" style="color: #45546E;" matInput placeholder="Enter here"
                                        [maxlength]="(code_length ? code_length : 15)"
                                        [required]="('project_code' | checkMandatedField : this.formConfig: 'edit-project')"
                                        (input)="validateProjectCode($event)" formControlName="project_code"
                                        [readonly]="project_code_disable">
                                    <mat-icon *ngIf="refresh" class="refresh-icon"
                                        tooltip="{{('renew' | checkLabel : this.formConfig: 'edit-project': 'Refreshing')}}">autorenew</mat-icon>
                                    <mat-icon *ngIf="(valid && !refresh && !empty)" class="valid-icon"
                                        tooltip="{{('valid_msg' | checkLabel : this.formConfig: 'edit-project': 'Valid')}}">check_circle</mat-icon>
                                    <!-- <mat-icon *ngIf="empty"  class="invalid-icon" tooltip="{{('empty_msg' | checkLabel : this.formConfig: 'edit-project': 'Kindly enter project code')}}">cancel</mat-icon>  -->
                                    <mat-icon *ngIf="(!valid && !refresh && !empty)" class="invalid-icon"
                                        tooltip="{{('invalid_msg' | checkLabel : this.formConfig: 'edit-project': 'code has already been taken')}}">error</mat-icon>
                                </mat-form-field>
                                <!-- </div>  -->
                            </div>
                            <!-- Name field -->
                            <div class="col-3" style="margin-left: -43px;"
                                *ngIf="('project_name' | checkActive : this.formConfig: 'edit-project')">
                                <div class="content-title">
                                    {{('project_name' | checkLabel : this.formConfig: 'edit-project': 'Project
                                    Name')}}
                                    <span class="required-star"
                                        *ngIf="('project_name' | checkMandatedField : this.formConfig: 'edit-project')">
                                        &nbsp;*</span>
                                    <span *ngIf="('project_name' | checkInfoIcon : this.formConfig: 'edit-project')">
                                        <mat-icon class="info-icon"
                                            tooltip="{{('project_name' | checkTooltip : this.formConfig: 'edit-project': 'Project Name')}}"
                                            >
                                            info_outline
                                        </mat-icon>
                                    </span>
                                </div>
                                <!-- <div class="row"> -->
                                <mat-form-field [ngStyle]="{'background':('project_name' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id)?'#E8E9EE':'#FFFFFF'}" class="input-field project_name"
                                    appearance="outline">
                                    <input class="font-family" style="color: #45546E;"
                                        [required]="('project_name' | checkMandatedField : this.formConfig: 'edit-project')"
                                        [maxlength]="(name_length ? name_length : 300)" matInput
                                        placeholder="Enter here" formControlName="project_name"
                                        [readonly]="('project_name' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id)">
                                </mat-form-field>
                                <!-- </div> -->
                            </div>
                            <div class="col-3" style="margin-left: 325px"
                                *ngIf="('project_type' | checkActive : this.formConfig: 'edit-project')">
                                <div class="content-title">
                                    {{('project_type' | checkLabel : this.formConfig: 'edit-project':
                                    'Project Type')}}
                                    <span class="required-star"
                                        *ngIf="('project_type' | checkMandatedField : this.formConfig: 'edit-project')">
                                        &nbsp;*</span>
                                    <span *ngIf="('project_type' | checkInfoIcon : this.formConfig: 'edit-project')">
                                        <mat-icon class="info-icon"
                                            tooltip="{{('project_type' | checkTooltip : this.formConfig: 'edit-project': 'Project Type')}}"
                                            matTooltipPosition="above">
                                            info_outline
                                        </mat-icon>
                                    </span>
                                </div>
                                <app-input-search-name [disabled]="project_type_disable"
                                    [required]="('project_type' | checkMandatedField : this.formConfig: 'edit-project')"
                                    class="project_type" [ngStyle]="{'background':('project_type' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id) ? '#E8E9EE':'#FFFFFF'}"
                                    [list]="project_type_list" placeholder="Select One" formControlName="project_type">
                                </app-input-search-name>
                            </div>

                        </div>
                        <!-- Second row -->

                        <div style="display:flex;margin-top: 10px;">
                            <div class="col-3" *ngIf="('start_date' | checkActive : this.formConfig: 'edit-project')">
                                <div class="content-title">
                                    {{('start_date' | checkLabel : this.formConfig: 'edit-project':
                                    'Start Date')}}
                                    <span class="required-star"
                                        *ngIf="('start_date' | checkMandatedField : this.formConfig: 'edit-project')">
                                        &nbsp;*</span>
                                    <span *ngIf="('start_date' | checkInfoIcon : this.formConfig: 'edit-project')">
                                        <mat-icon class="info-icon"
                                            tooltip="{{('start_date' | checkTooltip : this.formConfig: 'edit-project': 'Project Date')}}"
                                            >
                                            {{('info_icon' | checkLabel : this.formConfig: 'edit-project':
                                            'info_outline')}}
                                        </mat-icon>
                                    </span>
                                </div>

                                <mat-form-field class="input-field start_date" [ngStyle]="{'background':('start_date' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id) ? '#E8E9EE':'#FFFFFF'}"
                                    appearance="outline">

                                    <input class="font-family" style="color: #45546E;"
                                        [required]="('start_date' | checkMandatedField : this.formConfig: 'edit-project')"
                                        matInput formControlName="startDate" [matDatepicker]="psdDp"
                                        [max]="calculateMaxStartDate()"
                                        [disabled]="('start_date' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id)" name="startDate"
                                        placeholder="DD-MMM-YYYY" (click)="psdDp.open()" />
                                    <mat-datepicker-toggle matSuffix [for]="psdDp"></mat-datepicker-toggle>
                                    <mat-datepicker [panelClass]="'custom-datepicker-popup'" #psdDp></mat-datepicker>
                                </mat-form-field>
                            </div>
                            <div class="col-3" style="margin-left: -43px;"
                                *ngIf="('end_date' | checkActive : this.formConfig: 'edit-project')">
                                <div class="content-title">
                                    {{('end_date' | checkLabel : this.formConfig: 'edit-project':
                                    'End Date')}}
                                    <span class="required-star"
                                        *ngIf="('end_date' | checkMandatedField : this.formConfig: 'edit-project')">
                                        &nbsp;*</span>
                                    <span *ngIf="('end_date' | checkInfoIcon : this.formConfig: 'edit-project')">
                                        <mat-icon class="info-icon"
                                            tooltip="{{('end_date' | checkTooltip : this.formConfig: 'edit-project': 'Project Date')}}"
                                            >
                                            {{('info_icon' | checkLabel : this.formConfig: 'edit-project':
                                            'info_outline')}}
                                        </mat-icon>
                                    </span>
                                </div>
                                <mat-form-field [ngStyle]="{'background':('end_date' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id) ? '#E8E9EE':'#FFFFFF'}" class="input-field end_date"
                                    appearance="outline">

                                    <input matInput [matDatepicker]="picker4" name="endDate"
                                        [required]="('end_date' | checkMandatedField : this.formConfig: 'edit-project')"
                                        class="font-family"
                                        style="color: #45546E;"
                                        [min]="calculateMinEndDate()"
                                        [disabled]="('end_date' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id)" formControlName="endDate"
                                        placeholder="DD-MMM-YYYY" (click)="picker4.open()" />
                                    <mat-datepicker-toggle matSuffix [for]="picker4"></mat-datepicker-toggle>
                                    <mat-datepicker #picker4></mat-datepicker>
                                </mat-form-field>
                            </div>

                            <div class="col-2" style="margin-left: -44px;"
                                *ngIf="('currency' | checkActive : this.formConfig: 'edit-project')">
                                <div class="content-title">
                                    {{('currency' | checkLabel : this.formConfig: 'edit-project': 'Currency')}}
                                    <span class="required-star"
                                        *ngIf="('currency' | checkMandatedField : this.formConfig: 'edit-project')">
                                        &nbsp;*</span>
                                    <span *ngIf="('currency' | checkInfoIcon : this.formConfig: 'edit-project')">
                                        <mat-icon class="info-icon"
                                            tooltip="{{('currency' | checkTooltip : this.formConfig: 'edit-project': 'currency')}}"
                                            >
                                            info_outline
                                        </mat-icon>
                                    </span>
                                </div>
                                <app-input-search-name class="currency" [list]="currency_list"
                                    [required]="('currency' | checkMandatedField : this.formConfig: 'edit-project')"
                                    [ngStyle]="{'background':('currency' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id)? '#E8E9EE':this.milestoneList.length>0 ? '#E8E9EE': '#FFFFFF'}" [disabled]="('currency' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id) || this.milestoneList.length>0"
                                    placeholder="Select One" formControlName="currency">
                                </app-input-search-name>
                            </div>

                            <div class="col-2" style="margin-left: -24px;"
                                *ngIf="('portfolio' | checkActive : this.formConfig: 'edit-project')  && !('portfolio_mapping' | checkActive : this.formConfig: 'automatic')">
                                <div class="content-title">
                                    {{('portfolio' | checkLabel : this.formConfig: 'edit-project':
                                    'Portfolio')}}
                                    <span class="required-star"
                                        *ngIf="('portfolio' | checkMandatedField : this.formConfig: 'edit-project')">
                                        &nbsp;*</span>
                                    <span *ngIf="('portfolio' | checkInfoIcon : this.formConfig: 'edit-project')">
                                        <mat-icon class="info-icon"
                                            tooltip="{{('portfolio' | checkTooltip : this.formConfig: 'edit-project': 'Portfolio')}}"
                                           >
                                            info_outline
                                        </mat-icon>
                                    </span>
                                </div>
                                <app-input-search-name class="portfolio"
                                    [required]="('portfolio' | checkMandatedField : this.formConfig: 'edit-project')"
                                    [list]="portfolio_list" [ngStyle]="{'background':('portfolio' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id)? '#E8E9EE':'#FFFFFF'}"
                                    [disabled]="('portfolio' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id)" placeholder="Select One" formControlName="portfolio">
                                </app-input-search-name>
                            </div>
                            <div class="col-2" style="margin-left: 62px;"
                                *ngIf="('customer_details' | checkActive : this.formConfig: 'edit-project')  && !('portfolio_mapping' | checkActive : this.formConfig: 'automatic')">
                                <div class="content-title">
                                    {{('customer_details' | checkLabel : this.formConfig: 'edit-project': 'Customer')}}
                                    <span class="required-star"
                                        *ngIf="('customer_details' | checkMandatedField : this.formConfig: 'edit-project')">
                                        &nbsp;*</span>
                                    <mat-icon class="info-icon"
                                        tooltip="{{('customer_details' | checkTooltip : this.formConfig: 'edit-project': 'Customer')}}"
                                        >
                                        info_outline
                                    </mat-icon>
                                </div>
                                <!-- <div class="row"> -->
                                <!-- <mat-form-field class="customer_details" appearance="outline">
                                   <input  readonly="true" style="color: #45546E;font-family: Roboto;" matInput disabled="true" matInput placeholder="Customer" formControlName="customer_details">
                               </mat-form-field> -->
                                <div class="customer-name font-family"
                                    style="font-size: 13px;font-weight: bold;line-height: 16px;letter-spacing: 0em;text-align: left;color: #45546E;margin-top: 10px;text-overflow: ellipsis;overflow: hidden;white-space: nowrap;width: 179px;">
                                    <div tooltip="{{customer}}" placement="right" style="width: max-content">
                                        {{this.customer}}</div>
                                </div>
                                <!-- </div> -->
                            </div>
                            <div class="col-2" style="margin-left: -24px;"
                                *ngIf="('customer_id' | checkActive : this.formConfig: 'edit-project') && ('portfolio_mapping' | checkActive : this.formConfig: 'automatic')">
                                <div class="content-title">
                                    {{('customer_id' | checkLabel : this.formConfig: 'edit-project': 'Customer')}}
                                    <span class="required-star"
                                        *ngIf="('customer_id' | checkMandatedField : this.formConfig: 'edit-project')">
                                        *</span>
                                    <span *ngIf="('customer_id' | checkInfoIcon : this.formConfig: 'edit-project')">
                                        <mat-icon class="info-icon"
                                            tooltip="{{('customer_id' | checkTooltip : this.formConfig: 'edit-project': 'Customer')}}">
                                            info_outline
                                        </mat-icon>
                                    </span>
                                </div>
                                <app-input-search-name class="customer"
                                    [required]="('customer_id' | checkMandatedField : this.formConfig: 'edit-project')"
                                    [list]="customer_list" placeholder="Select One" formControlName="customer_id"
                                    [ngStyle]="{'background':('customer_id' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id)? '#E8E9EE':'#FFFFFF'}"
                                    [disabled]="('customer_id' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id)">
                                </app-input-search-name>
                            </div>


                        </div>

                        <!-- Third row -->

                           <div class="row" style="margin-top: 10px;">
                               <div class="col-12"   *ngIf="('service_type' | checkActive : this.formConfig: 'edit-project')">
                                   <div class="content-title">
                                       {{('service_type' | checkLabel : this.formConfig: 'edit-project':
                                       'Service Type')}} <span class="required-star"
                                       *ngIf="('service_type' | checkMandatedField : this.formConfig: 'edit-project')">
                                       &nbsp;*</span>
                                       <span *ngIf="('service_type' | checkInfoIcon : this.formConfig: 'edit-project')">
                                       <mat-icon class="info-icon" tooltip="{{('service_type' | checkTooltip : this.formConfig: 'edit-project': 'Service type')}}"
                                           >
                                           info_outline
                                       </mat-icon>
                                       </span>
                                   </div>
                                   <div *ngIf="selectedOption==1 && ('blanket' | checkMandatedField : this.formConfig: 'edit-project') " style="display: flex;
                                   margin-top: -25px;
                                   position: absolute;
                                   margin-left: 72px;">
                                       <mat-checkbox class="checkbox-input" style="margin-left: 35px;margin-top: 9px;" [ngModelOptions]="{standalone: true}"  [(ngModel)]="TandM_selected" (change)="selectCheckBox($event)" [disabled]="('blanket' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id)">
                                       </mat-checkbox>
                                       <div class="font-family" style="color: #8B95A5;
                                       font-size: 13px;
                                       margin-top: 8px;margin-left: 4px;">{{('blanket' | checkLabel : this.formConfig: 'edit-project':
                                       'Blanket')}}</div>
                                       </div>
                                   <div style="display:flex">
                                       <div class="button-group">
                                           <button class="cus-button" mat-button color="primary"
                                               *ngFor="let card of cards"
                                               [class.active]="card.id === selectedOption"
                                               (click)="selectOption(card.id)"
                                               [disabled]="('service_type' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id) || this.milestoneList.length>0 || (this.ServiceId==this.inertnal_service_type_id)"
                                               [tooltip]="card.description" matTooltipPosition="above"
                                               matTooltipShowDelay="500">
                                               <!-- <div class="mat-radio-container">
                                       <div class="mat-radio-outer-circle">
                                           <div class="mat-radio-inner-circle">
                                    <input class="button-radio" type="radio" [class.active]="card.service_type_id === selectedOption" [checked]="radioChecked" [value]="card.service_type_id" />
                                   </div>
                                   </div>
                                   </div> -->
                                            <div class="button-content">
                                                <h3 *ngIf="card.id != selectedOption">{{card.name}}</h3>
                                                <h3 *ngIf="card.id === selectedOption" [style.color]="button">
                                                    {{card.name}}</h3>
                                                <!-- <p [ngClass]="{'two-lines': option === selectedOption}" class="para">{{ card.description }}</p> -->
                                                <div class="para">{{card.description}}</div>
                                            </div>
                                            <div *ngIf="card.id != selectedOption"
                                                style="width: 13px;height: 13px;/* left: 0px; *//* top: 0px; */position: absolute;border-radius: 9999px;border: 1px #DADCE2 solid;left: 145px;top: 14px;">
                                            </div>
                                            <div *ngIf="card.id === selectedOption"
                                                style="width: 8px;height: 8px;left: 148.4px;top: 17px;position: absolute;border-radius: 9999px;"
                                                class="card-background"></div>
                                            <div *ngIf="card.id === selectedOption"
                                                style="width: 14px;height: 14px;/* left: 0px; *//* top: 0px; */position: absolute;border-radius: 9999px;left: 145px;top: 14px;"
                                                class="card-selection"></div>
                                        </button>
                                    </div>

                                </div>
                            </div>
                        </div>
                        <!-- <div style="margin-top: -6px;" *ngIf = "('project_description' | checkActive : this.formConfig: 'edit-project')">
                               <div class="row col-9 content-title">
                                   {{('project_description' | checkLabel : this.formConfig: 'edit-project': 'Project Description')}}
                                   <span  class="required-star" *ngIf="('project_description' | checkMandatedField : this.formConfig: 'edit-project')"> &nbsp;*</span>
                                   <span *ngIf="('project_description' | checkInfoIcon : this.formConfig: 'edit-project')">
                                       <mat-icon class="info-icon" tooltip = "{{('project_description' | checkTooltip : this.formConfig: 'edit-project': 'Project description Information')}}" matTooltipPosition ="above">
                                           info_outline
                                       </mat-icon>
                                   </span>
                               </div>
                               <div class="row">
                                   <textarea matInput placeholder="Enter here" formControlName="description" [required]="('project_description' | checkMandatedField : this.formConfig: 'edit-project')" class="description col-9" rows="4"></textarea>
                               </div>
                           </div> -->

                        <!-- Fourth row -->
                    </div>
                    <div *ngIf="detailStepper" class="footer-buttons">
                        <div class="col-1">
                            <button mat-button class="button-skip"
                                *ngIf="('skip_button' | checkActive : this.formConfig: 'edit-project') && detailsSkip && !detailsLast"
                                (click)="getNextStepper()">{{('skip_button' | checkLabel : this.formConfig:
                                'edit-project':
                                'Skip')}}</button>
                        </div>
                        <div class="col-10"></div>
                        <div class="col-1">
                            <button mat-button class="button-next"
                                *ngIf="('next_button' | checkActive : this.formConfig: 'edit-project') && !detailsLast"
                                (click)="getNextStepper()">{{('next_button' | checkLabel : this.formConfig:
                                'edit-project':
                                'Next')}}</button>
                            <button mat-button class="button-next" style="margin-left:-75px" [disabled]="save_disabled"
                                *ngIf="('create_project' | checkActive : this.formConfig: 'edit-project') && detailsLast"
                                (click)="updateProjectDetails()">{{('create_project' | checkLabel : this.formConfig:
                                'edit-project':
                                'Create Project')}}</button>
                            <div class="button-next" *ngIf="save_disabled">
                                <mat-spinner class="green-spinner" diameter="20"></mat-spinner>
                            </div>
                        </div>
                    </div>

                    <!-- </mat-step> -->
                    <!--Enterprise structure-->
                    <!-- <mat-step label="{{('enterprise_structure' | checkLabel : this.formConfig: 'edit-project': 'Enterprise Structure')}}"> -->
                    <!-- <div class="container business-division-style"> -->

                    <!-- <div class="row pt-4 pb-2">
                           <div class="col-12 field-title">
                               Add Business Division
                           </div>
                       </div> -->

                    <div *ngIf="enterpriseStepper" class="col-12 content"  style="overflow-y: auto; height: 345px !important;">
                        <!-- <div class="row pt-2 pb-2"> -->
                        <div class="row" *ngIf="('portfolio_mapping' | checkActive : this.formConfig: 'automatic')">
                            <div class="col-6" *ngIf="('portfolio' | checkActive : this.formConfig: 'edit-project')">

                                <div class="row content-title">
                                    {{('portfolio' | checkLabel : this.formConfig: 'edit-project':
                                    'Portfolio')}}<span> &nbsp;</span>
                                </div>
                                <!-- <div class="font-family"
                                    style="font-size: 13px;font-weight: bold;line-height: 16px;letter-spacing: 0em;text-align: left;color: #45546E;margin-top: 5px;text-overflow: ellipsis;white-space: nowrap;overflow: hidden;">
                                    {{this.portfolio_name}}
                                </div> -->
                                <div class="customer-name font-family"
                                    style="font-size: 13px;font-weight: bold;line-height: 16px;letter-spacing: 0em;text-align: left;color: #45546E;margin-top: 10px;text-overflow: ellipsis;overflow: hidden;white-space: nowrap;width: 397px;">
                                    <div tooltip="{{portfolio_name}}" placement="right" style="width: max-content">
                                        {{this.portfolio_name}}</div>
                                </div>

                            </div>
                            <div class="col-2" style="margin-left: -51px;" *ngIf="('customer' | checkActive : this.formConfig: 'edit-project')">
                                <div class="row content-title">
                                    {{('customer' | checkLabel : this.formConfig: 'edit-project':
                                    'Customer')}}<span> &nbsp;</span>
                                </div>
                                <!-- <div class="font-family"
                                    style="font-size: 13px;font-weight: bold;line-height: 16px;letter-spacing: 0em;text-align: left;color: #45546E;margin-top: 5px;text-overflow: ellipsis;white-space: nowrap;overflow: hidden;">
                                    {{this.customer}}
                                </div> -->
                                <div class="customer-name font-family"
                                    style="font-size: 13px;font-weight: bold;line-height: 16px;letter-spacing: 0em;text-align: left;color: #45546E;margin-top: 10px;text-overflow: ellipsis;overflow: hidden;white-space: nowrap;width: 229px;">
                                    <div tooltip="{{customer}}" placement="right" style="width: max-content">
                                        {{this.customer}}</div>
                                </div>
                            </div>
                            <div class="col-3" style="margin-left: 112px;"
                            *ngIf="('child_customer' | checkActive : this.formConfig: 'edit-project')">
                            <div class="content-title">
                                {{('child_customer' | checkLabel : this.formConfig: 'edit-project':
                                'Child Customer')}}
                                <span class="required-star"
                                    *ngIf="('child_customer' | checkMandatedField : this.formConfig: 'edit-project')">
                                    &nbsp;*</span>
                                <span *ngIf="('child_customer' | checkInfoIcon : this.formConfig: 'edit-project')">
                                    <mat-icon class="info-icon"
                                        tooltip="{{('child_customer' | checkTooltip : this.formConfig: 'edit-project': 'Child Customer')}}"
                                       >
                                        info_outline
                                    </mat-icon>
                                </span>
                            </div>
                            <app-input-search-name class="child_customer"
                                [required]="('child_customer' | checkMandatedField : this.formConfig: 'edit-project')"
                                [list]="child_customer_list" [ngStyle]="{'background':('child_customer' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id)? '#E8E9EE':'#FFFFFF'}"
                                [disabled]="('child_customer' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id)" placeholder="Select One" formControlName="child_customer">
                            </app-input-search-name>
                        </div>
                        </div>
                        <div class="row" *ngIf="('es_filter' | checkActive : this.formConfig: 'edit-project')">
                            <div class="col-4" *ngIf="('entity' | checkActive : this.formConfig: 'edit-project')">


                                <div class="content-title">
                                    {{('entity' | checkLabel : this.formConfig: 'edit-project': 'Entity')}}
                                    <span class="required-star"
                                        *ngIf="('entity' | checkMandatedField : this.formConfig: 'edit-project')">
                                        &nbsp;*</span>
                                    <span *ngIf="('entity' | checkInfoIcon : this.formConfig: 'edit-project')">
                                        <mat-icon class="info-icon"
                                            tooltip="{{('entity' | checkTooltip : this.formConfig: 'edit-project': 'entity')}}"
                                            matTooltipPosition="above">
                                            info_outline
                                        </mat-icon>
                                    </span>
                                </div>
                                <!-- <div class="row"> -->
                                <app-input-search-name class="entity col-12create-account-field" [list]="entityList"
                                    formControlName="entity"
                                    [required]="('entity' | checkMandatedField : this.formConfig: 'edit-project')"
                                    [ngStyle]="{'background':entity_color}" [disabled]="entity_disable"
                                    placeholder="Select One">
                                </app-input-search-name>
                                <!-- </div> -->

                            </div>



                            <div class="col-4"
                                *ngIf="('division' | checkActive : this.formConfig: 'edit-project') && displayDivision">

                                <div class="content-title">
                                    {{('division' | checkLabel : this.formConfig: 'edit-project': 'division')}}
                                    <span class="required-star"
                                        *ngIf="('division' | checkMandatedField : this.formConfig: 'edit-project')">
                                        &nbsp;*</span>
                                    <span *ngIf="('division' | checkInfoIcon : this.formConfig: 'edit-project')">
                                        <mat-icon class="info-icon"
                                            tooltip="{{('division' | checkTooltip : this.formConfig: 'edit-project': 'division')}}"
                                            matTooltipPosition="above">
                                            info_outline
                                        </mat-icon>
                                    </span>
                                </div>
                                <!-- <div class="row"> -->
                                <app-input-search-name class="division col-12 create-account-field "
                                    [list]="divisionList" formControlName="division"
                                    [required]="('division' | checkMandatedField : this.formConfig: 'edit-project')"
                                    [ngStyle]="{'background':division_color}" [disabled]="division_disable"
                                    placeholder="Select One">
                                </app-input-search-name>
                                <!-- </div> -->

                            </div>

                            <div class="col-4"
                                *ngIf="('sub_division' | checkActive : this.formConfig: 'edit-project') && displaySubDivision">

                                <div class="content-title">
                                    {{('sub_division' | checkLabel : this.formConfig: 'edit-project': 'Sub Diviison')}}
                                    <span class="required-star"
                                        *ngIf="('sub_division' | checkMandatedField : this.formConfig: 'edit-project')">
                                        &nbsp;*</span>
                                    <span *ngIf="('sub_division' | checkInfoIcon : this.formConfig: 'edit-project')">
                                        <mat-icon class="info-icon"
                                            tooltip="{{('sub_division' | checkTooltip : this.formConfig: 'edit-project': 'sub_division')}}"
                                            matTooltipPosition="above">
                                            info_outline
                                        </mat-icon>
                                    </span>
                                </div>
                                <!-- <div class="row"> -->
                                <app-input-search-name class="subDivision col-12 create-account-field "
                                    [list]="subDivisionList" formControlName="sub_division"
                                    [required]="('sub_division' | checkMandatedField : this.formConfig: 'edit-project')"
                                    [ngStyle]="{'background':sub_division_color}" [disabled]="sub_division_disable"
                                    placeholder="Select One">
                                </app-input-search-name>
                                <!-- </div> -->

                            </div>

                        </div>
                        <!-- </div> -->

                        <div class="row"
                            *ngIf="displayList.length!=0 && displayEsList && ('es_filter' | checkActive : this.formConfig: 'edit-project')">

                            <div class="col-12"
                                style="height: 195px;overflow-y: scroll;margin-top: 5px;padding-bottom: 20px;padding-right: 12px;">
                                <div *ngFor="let display of displayList; let i= index" class="slide-in-top">

                                    <div class="row pt-1  card-body card-details outlineCheck"
                                        [ngClass]="{ highlightCard : i == this.selectedIndex} "
                                        (click)="selectData(display, i)"
                                        style="cursor: pointer; height:30px;margin-bottom:10px;">
                                        <div class="orgename" style="width: 253px !important;text-overflow: ellipsis;">

                                            <span [tooltip] = "display['entity_name']">{{display['entity_name']}}</span>

                                        </div>

                                        <div class="orgdname" style="width: 253px !important;text-overflow: ellipsis;">

                                            <span [tooltip] = "display['division_name']">{{display['division_name']}}</span>

                                        </div>

                                        <div class="orgsdname" style="width: 253px !important;text-overflow: ellipsis;">

                                            <span [tooltip] = "display['sub_division_name']">{{display['sub_division_name']}}</span>

                                        </div>
                                    </div>

                                </div>
                            </div>


                        </div>

                        <div class="row "
                            *ngIf="displayList.length==0 && ('es_filter' | checkActive : this.formConfig: 'edit-project')">

                            <div class="col-12 justify-content-center">
                                <div class="row">
                                    <div class="col-12  d-flex justify-content-center">
                                        <span class="font-family"
                                            style="margin-top: 26px;font-size: 14px;font-weight: 400;line-height: 16px;letter-spacing: 0em;text-align: left;color: #6E7B8F;">No
                                            matches found!</span>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-12  d-flex justify-content-center">
                                        <button mat-raised-button class="iconbtnSave " tooltip="Clear Filter"
                                            type="submit" (click)="clearFilter();">
                                            <span>Clear All</span>
                                        </button>
                                    </div>
                                </div>
                            </div>



                        </div>
                        <div class="row">
                            <div class="col-2"
                                *ngIf="('profit_center' | checkActive : this.formConfig: 'edit-project')">
                                <div class="content-title">
                                    {{('profit_center' | checkLabel : this.formConfig: 'edit-project': 'Charge Code')}}
                                    <span class="required-star"
                                        *ngIf="('profit_center' | checkMandatedField : this.formConfig: 'edit-project')">
                                        &nbsp;*</span>
                                    <span *ngIf="('profit_center' | checkInfoIcon : this.formConfig: 'edit-project')">
                                        <mat-icon class="info-icon"
                                            tooltip="{{('profit_center' | checkTooltip : this.formConfig: 'edit-project': 'Project code')}}"
                                            matTooltipPosition="above">
                                            info_outline
                                        </mat-icon>
                                    </span>
                                </div>
                                <!-- <div class="row"> -->
                                <mat-form-field class="input-field project_code" [ngStyle]="{'background':profit_center_color}"
                                    appearance="outline">
                                    <input style="color: #45546E;"
                                        [readonly]="profit_center_disable" matInput 
                                        [required]="('profit_center' | checkMandatedField : this.formConfig: 'edit-project')"
                                        formControlName="profit_center" class="project-code font-family"></mat-form-field>
                            </div>
                            <div class="col-2" style="margin-left: 42px;"
                                *ngIf="('p_and_l' | checkActive : this.formConfig: 'edit-project')">
                                <div class="content-title">
                                    {{('p_and_l' | checkLabel : this.formConfig: 'edit-project':
                                    'Profit Center')}}
                                    <span class="required-star"
                                        *ngIf="('p_and_l' | checkMandatedField : this.formConfig: 'edit-project')">
                                        &nbsp;*</span>
                                    <span *ngIf="('p_and_l' | checkInfoIcon : this.formConfig: 'edit-project')">
                                        <mat-icon class="info-icon"
                                            tooltip="{{('p_and_l' | checkTooltip : this.formConfig: 'edit-project': 'P and L')}}"
                                            >
                                            info_outline
                                        </mat-icon>
                                    </span>
                                </div>
                                <app-input-search-name class="portfolio"
                                [ngStyle]="{'background':('p_and_l' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id) ? '#E8E9EE':'#FFFFFF'}"
                                    [required]="('p_and_l' | checkMandatedField : this.formConfig: 'edit-project')"
                                    [disabled]="('p_and_l' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id)"
                                    [list]="p_and_l_list" placeholder="Select One" formControlName="p_and_l">
                                </app-input-search-name>
                            </div>
                            <div class="col-2" style="margin-left: 72px;"
                                *ngIf="('legal_entity' | checkActive : this.formConfig: 'edit-project')">
                                <div class="content-title">
                                    {{('legal_entity' | checkLabel : this.formConfig: 'edit-project':
                                    'Legal Entity')}}
                                    <span class="required-star"
                                        *ngIf="('legal_entity' | checkMandatedField : this.formConfig: 'edit-project')">
                                        *</span>
                                    <span *ngIf="('legal_entity' | checkInfoIcon : this.formConfig: 'edit-project')">
                                        <mat-icon class="info-icon"
                                            tooltip="{{('legal_entity' | checkTooltip : this.formConfig: 'edit-project': 'Legal Entity')}}"
                                            >
                                            info_outline
                                        </mat-icon>
                                    </span>
                                </div>
                                <app-input-search-name class="legalEntity"
                                    [required]="('legal_entity' | checkMandatedField : this.formConfig: 'edit-project')"
                                    [disabled]="('legal_entity' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id)"
                                    [list]="legal_entity_list" [ngStyle]="{'background':getDisabledStatusColor('legal_entity', this.data.item_status_id)}" placeholder="Select One"
                                    formControlName="legal_entity">
                                </app-input-search-name>
                            </div>
                            <div class="col-3" style="margin-left: 112px;"
                                *ngIf="('sow_reference_number' | checkActive : this.formConfig: 'edit-project')">
                                <div class="content-title">
                                    {{('sow_reference_number' | checkLabel : this.formConfig: 'edit-project':
                                    'SOW Reference Number')}}
                                    <span class="required-star"
                                        *ngIf="('sow_reference_number' | checkMandatedField : this.formConfig: 'edit-project')">
                                        *</span>
                                    <span
                                        *ngIf="('sow_reference_number' | checkInfoIcon : this.formConfig: 'edit-project')">
                                        <mat-icon class="info-icon"
                                            tooltip="{{('sow_reference_number' | checkTooltip : this.formConfig: 'edit-project': 'SOW Reference Number')}}">
                                            info_outline
                                        </mat-icon>
                                    </span>
                                </div>
                                <mat-form-field class="input-field sow_reference_number" appearance="outline" [ngStyle]="{'background':getDisabledStatusColor('sow_reference_number', this.data.item_status_id)}">
                                    <input class="font-family" style="color: #45546E; width: fit-content"
                                        [required]="('sow_reference_number' | checkMandatedField : this.formConfig: 'edit-project')"
                                        [readonly]="('sow_reference_number' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id)"
                                        [maxlength]="(name_length ? name_length : 300)" matInput
                                        placeholder="Enter here" formControlName="sow_reference_number">
                                    <mat-icon *ngIf="isSowReferenceNumberRefresh" class="refresh-icon"
                                        tooltip="{{('renew' | checkLabel : this.formConfig: 'project-creation': 'Refreshing')}}">autorenew</mat-icon>
                                    <mat-icon *ngIf="(sowReferenceNumberValid && !isSowReferenceNumberRefresh && !isSowReferenceNumberEmpty)" class="valid-icon"
                                        tooltip="{{('valid_msg' | checkLabel : this.formConfig: 'project-creation': 'Valid')}}">check_circle</mat-icon>
                                    <mat-icon *ngIf="(!sowReferenceNumberValid && !isSowReferenceNumberRefresh && !isSowReferenceNumberEmpty && SowReferenceNumberDuplicated)" class="invalid-icon"
                                        tooltip="{{sowRefDuplicatedMsg ? sowRefDuplicatedMsg : 'Sow Reference Number already exist'}}">error</mat-icon>
                                    <mat-icon *ngIf="(!sowReferenceNumberValid && !isSowReferenceNumberRefresh && !isSowReferenceNumberEmpty && invalidSowReferenceNumberLength)" class="invalid-icon"
                                        tooltip="{{sowInvalidLengthMsg ? sowInvalidLengthMsg : 'Sow Reference Number length invalid'}}">error</mat-icon>
                                </mat-form-field>
                            </div>
                            <div class="col-3" style="margin-left: 112px;"
                                *ngIf="('product_category' | checkActive : this.formConfig: 'edit-project')">
                                <div class="content-title">
                                    {{('product_category' | checkLabel : this.formConfig: 'edit-project':
                                    'Product Category')}}
                                    <span class="required-star"
                                        *ngIf="('product_category' | checkMandatedField : this.formConfig: 'edit-project')">
                                        *</span>
                                    <span
                                        *ngIf="('product_category' | checkInfoIcon : this.formConfig: 'edit-project')">
                                        <mat-icon class="info-icon"
                                            tooltip="{{('product_category' | checkTooltip : this.formConfig: 'edit-project': 'Product Category')}}">
                                            info_outline
                                        </mat-icon>
                                    </span>
                                </div>
                                <app-input-search-name class="product_category"
                                    [disabled]="('product_category' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id)"
                                    [required]="('product_category' | checkMandatedField : this.formConfig: 'edit-project')"
                                    [ngStyle]="{'background':getDisabledStatusColor('product_category', this.data.item_status_id)}"
                                    [list]="product_category_list" placeholder="Select One" formControlName="product_category">
                                </app-input-search-name>
                            </div>
                        </div>
                        <div class="row">

                            <div class="col-2"
                                *ngIf="('revenue_type' | checkActive : this.formConfig: 'edit-project')">
                                <div class="content-title">
                                    {{('revenue_type' | checkLabel : this.formConfig: 'edit-project':
                                    'Profit Center')}}
                                    <span class="required-star"
                                        *ngIf="('revenue_type' | checkMandatedField : this.formConfig: 'edit-project')">
                                        *</span>
                                    <span *ngIf="('revenue_type' | checkInfoIcon : this.formConfig: 'edit-project')">
                                        <mat-icon class="info-icon"
                                            tooltip="{{('revenue_type' | checkTooltip : this.formConfig: 'edit-project': 'Revenue Type')}}">
                                            info_outline
                                        </mat-icon>
                                    </span>
                                </div>
                                <app-input-search-name class="revenue_type"
                                    [ngStyle]="{'background':getDisabledStatusColor('revenue_type', this.data.item_status_id)}"
                                    [required]="('revenue_type' | checkMandatedField : this.formConfig: 'edit-project')"
                                    [list]="revenue_type_list" placeholder="Select One" formControlName="revenue_type">
                                </app-input-search-name>
                            </div>
                            <div class="col-2" 
                                *ngIf="('delivery_type' | checkActive : this.formConfig: 'edit-project')">
                                <div class="content-title">
                                    {{('delivery_type' | checkLabel : this.formConfig: 'edit-project':
                                    'Profit Center')}}
                                    <span class="required-star"
                                        *ngIf="('delivery_type' | checkMandatedField : this.formConfig: 'edit-project')">
                                        *</span>
                                    <span *ngIf="('delivery_type' | checkInfoIcon : this.formConfig: 'edit-project')">
                                        <mat-icon class="info-icon"
                                            tooltip="{{('delivery_type' | checkTooltip : this.formConfig: 'edit-project': 'Delivery Type')}}">
                                            info_outline
                                        </mat-icon>
                                    </span>
                                </div>
                                <app-input-search-name class="delivery_type"
                                    [ngStyle]="{'background':getDisabledStatusColor('delivery_type', this.data.item_status_id)}"
                                    [required]="('delivery_type' | checkMandatedField : this.formConfig: 'edit-project')"
                                    [list]="delivery_type_list" placeholder="Select One" formControlName="delivery_type">
                                </app-input-search-name>
                            </div>
    
                        </div>
                    </div>
                    
               
               
                    <div *ngIf="enterpriseStepper" class="footer-buttons">
                        <div class="col-1">
                            <button mat-button class="button-skip"
                                *ngIf="('skip_button' | checkActive : this.formConfig: 'edit-project') && esSkip && !esLast"
                                (click)="getNextStepper()">{{('skip_button' | checkLabel : this.formConfig:
                                'edit-project':
                                'Skip')}}</button>
                        </div>
                        <div class="col-8"></div>
                        <div class="col-2">
                            <button mat-button class="button-back"
                                *ngIf="('back_button' | checkActive : this.formConfig: 'edit-project') && !esLast"
                                (click)="getPreviousStepper()">{{('back_button' | checkLabel : this.formConfig:
                                'edit-project': 'Back')}}</button>
                        </div>
                        <div></div>
                        <div class="col-1">
                            <button mat-button class="button-next"
                                *ngIf="('next_button' | checkActive : this.formConfig: 'edit-project') && !esLast"
                                (click)="getNextStepper()">Next</button>
                            <button mat-button class="button-next" style="margin-left:-75px" [disabled]="save_disabled"
                                *ngIf="('create_project' | checkActive : this.formConfig: 'edit-project') && esLast"
                                (click)="updateProjectDetails()">{{('create_project' | checkLabel : this.formConfig:
                                'edit-project':
                                'Create Project')}}</button>
                            <div class="button-next" *ngIf="save_disabled">
                                <mat-spinner class="green-spinner" diameter="20"></mat-spinner>
                            </div>
                        </div>
                    </div>
                    <!-- </div> -->
                    <!-- </mat-step> -->
                    <!-- Financial step -->
                    <!-- <mat-step label="{{('financial' | checkLabel : this.formConfig: 'edit-project': 'Financial')}}"> -->
                    <!-- First row -->
                    <div [hidden]="!financialStepper" class="center-body col-12 content"
                        style="padding-bottom:15px;overflow:auto">
                        <div style="display: flex;">
                            <div
                                *ngIf="false && ('opportunity_toggle' | checkActive : this.formConfig: 'edit-project')">
                                <div *ngIf="status_flag"
                                    style="cursor: pointer;width: 619px; height: 48px; justify-content: flex-start; align-items: flex-end; gap: 24px; display: inline-flex;margin-top: 10px;margin-left: 15px;">
                                    <div
                                        style="height: 48px; justify-content: flex-start; align-items: flex-start; gap: 2px; display: flex">
                                        <div *ngIf="withOpportunity" (click)="checkWithOpportunity()"
                                            style="flex: 1 1 0px;height: 0px;padding: 16px;background: rgb(238, 249, 232);;box-shadow: rgba(0, 0, 0, 0.12) 0px 2px 1px;border-top-left-radius: 15px;border-top-right-radius: 0px;border: 1px solid rgb(82, 196, 26);justify-content: center;align-items: center;display: flex;border-bottom-left-radius: 15px;">
                                            <div class="font-family"
                                                style="color: rgb(82, 196, 26); font-size: 13px;font-weight: 600; text-transform: capitalize; line-height: 16px; text-wrap: nowrap">
                                                With Opportunity</div>
                                        </div>
                                        <div *ngIf="!withOpportunity" (click)="checkWithOpportunity()"
                                            style="flex: 1 1 0; height: 0px; padding: 16px; background: white; justify-content: center; align-items: center; display: flex;border-top-left-radius: 15px;border-bottom-left-radius: 15px;">
                                            <div class="font-family"
                                                style="color: #7D838B; font-size: 13px; font-weight: 600; text-transform: capitalize; line-height: 16px; text-wrap: nowrap">
                                                With Opportunity</div>
                                        </div>
                                        <div *ngIf="withoutOpportunity" (click)="checkWithoutOpportunity()"
                                            style="flex: 1 1 0; height: 0px; padding: 16px; background: rgb(238, 249, 232); box-shadow: 0px 2px 1px rgba(0, 0, 0, 0.12); border: 1px solid rgb(82, 196, 26); justify-content: center; align-items: center; display: flex;border-top-right-radius: 15px;border-bottom-right-radius: 15px;">
                                            <div class="font-family"
                                                style="color: rgb(82, 196, 26); font-size: 13px; font-weight: 600; text-transform: capitalize; line-height: 16px; text-wrap: nowrap">
                                                Without Opportunity</div>
                                        </div>
                                        <div *ngIf="!withoutOpportunity" (click)="checkWithoutOpportunity()"
                                            style="flex: 1 1 0; height: 0px; padding: 16px; background: white; justify-content: center; align-items: center; display: flex;border-top-right-radius: 15px;border-bottom-right-radius: 15px;">
                                            <div class="font-family"
                                                style="color: #7D838B; font-size: 13px;font-weight: 600; text-transform: capitalize; line-height: 16px; text-wrap: nowrap">
                                                Without Opportunity</div>
                                        </div>
                                    </div>
                                </div>
                                <!-- <div style="width: 94px; height: 34px; padding-left: 16px; padding-right: 16px; padding-top: 12px; padding-bottom: 12px; background: #FFEBEC; border-radius: 4px; border: 1.50px #FF3A46 solid; justify-content: flex-start; align-items: center; gap: 8px; display: inline-flex">
                                     <div style="width: 12px; height: 12px; position: relative">
                                       <div style="width: 12px; height: 12px; left: 0px; top: 0px; position: absolute"></div>
                                       <div style="width: 10px; height: 10px; left: 1px; top: 1px; position: absolute; color: #FF3A46"><mat-icon style="font-size: 18px;margin-top: -5px;margin-left: -6px;">info_outline</mat-icon></div>
                                     </div>
                                     <div style="color: #45546E; font-size: 12px; font-family: DM Sans; font-weight: 400; line-height: 16px; letter-spacing: 0.24px; word-wrap: break-word">At Risk</div>
                                   </div> -->
                                <div *ngIf="!status_flag"
                                    style="cursor: pointer;width: 619px; height: 48px; justify-content: flex-start; align-items: flex-end; gap: 24px; display: inline-flex;margin-top: 10px;margin-left: 15px;">
                                </div>
                            </div>
                            <div class="row"
                                *ngIf="('opportunity_toggle' | checkActive : this.formConfig: 'edit-project')"
                                style="margin-top: 15px; white-space: nowrap; width: 180px;">
                                <div class="toggle-content" [ngClass]="{'toggle-content-glow': withOpportunity}">
                                    {{('q2c' | checkLabel : this.formConfig: 'edit-project':
                                    'Q2C')}}</div>
                                <!-- <mat-slide-toggle [checked]="toggleOpportunityChecked" (change)="checkOpportunity()"></mat-slide-toggle> -->
                                <label class="toggle-switch">
                                    <input type="checkbox" [checked]="toggleOpportunityChecked"
                                        (click)="checkOpportunity()" [disabled]="('opportunity_toggle' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id) ">
                                    <span class="slider"></span>
                                </label>
                                <div class="toggle-content" [ngClass]="{'toggle-content-glow': withoutOpportunity}">
                                    {{('o2c' | checkLabel : this.formConfig: 'edit-project':
                                    'O2C')}}</div>
                            </div>
                            <div style="margin-left: 180px;"
                                *ngIf="false && ('at_risk_toggle' | checkActive : this.formConfig: 'edit-project')">
                                <div
                                    style="cursor: pointer; height: 48px; justify-content: flex-start; align-items: flex-end; gap: 24px; display: inline-flex;margin-top: 10px;">
                                    <div
                                        style="height: 48px; justify-content: flex-start; align-items: flex-start; gap: 2px; display: flex">
                                        <div *ngIf="standard" (click)="checkStandard()"
                                            style="flex: 1 1 0px;height: 0px;padding: 16px;background: rgb(238, 249, 232);;box-shadow: rgba(0, 0, 0, 0.12) 0px 2px 1px;border-top-left-radius: 15px;border-top-right-radius: 0px;border: 1px solid rgb(82, 196, 26);justify-content: center;align-items: center;display: flex;border-bottom-left-radius: 15px;">
                                            <div class="font-family"
                                                style="color: rgb(82, 196, 26); font-size: 13px; font-weight: 600; text-transform: capitalize; line-height: 16px; word-wrap: break-word">
                                                Secured</div>
                                        </div>
                                        <div *ngIf="!standard" (click)="checkStandard()"
                                            style="flex: 1 1 0; height: 0px; padding: 16px; background: white; justify-content: center; align-items: center; display: flex;border-top-left-radius: 15px;border-bottom-left-radius: 15px;">
                                            <div class="font-family"
                                                style="color: #7D838B; font-size: 13px;font-weight: 600; text-transform: capitalize; line-height: 16px; word-wrap: break-word">
                                                Secured</div>
                                        </div>
                                        <div *ngIf="risk" (click)="checkAtRisk()"
                                            style="flex: 1 1 0; height: 0px; padding: 16px; background: rgb(238, 249, 232); box-shadow: 0px 2px 1px rgba(0, 0, 0, 0.12); border: 1px solid rgb(82, 196, 26); justify-content: center; align-items: center; display: flex;border-top-right-radius: 15px;border-bottom-right-radius: 15px;">
                                            <div class="font-family"
                                                style="color: rgb(82, 196, 26); font-size: 13px; font-weight: 600; text-transform: capitalize; line-height: 16px; word-wrap: break-word">
                                                At Risk</div>
                                        </div>
                                        <div *ngIf="!risk" (click)="checkAtRisk()"
                                            style="flex: 1 1 0; height: 0px; padding: 16px; background: white; justify-content: center; align-items: center; display: flex;border-top-right-radius: 15px;border-bottom-right-radius: 15px;">
                                            <div class="font-family"
                                                style="color: #7D838B; font-size: 13px; font-weight: 600; text-transform: capitalize; line-height: 16px; word-wrap: break-word">
                                                At Risk</div>
                                        </div>
                                    </div>
                                </div>
                                <!-- <div style="width: 94px; height: 34px; padding-left: 16px; padding-right: 16px; padding-top: 12px; padding-bottom: 12px; background: #FFEBEC; border-radius: 4px; border: 1.50px #FF3A46 solid; justify-content: flex-start; align-items: center; gap: 8px; display: inline-flex">
                                     <div style="width: 12px; height: 12px; position: relative">
                                       <div style="width: 12px; height: 12px; left: 0px; top: 0px; position: absolute"></div>
                                       <div style="width: 10px; height: 10px; left: 1px; top: 1px; position: absolute; color: #FF3A46"><mat-icon style="font-size: 18px;margin-top: -5px;margin-left: -6px;">info_outline</mat-icon></div>
                                     </div>
                                     <div style="color: #45546E; font-size: 12px; font-family: DM Sans; font-weight: 400; line-height: 16px; letter-spacing: 0.24px; word-wrap: break-word">At Risk</div>
                                   </div> -->
                            </div>
                            <div class="row" style="margin-left: 600px !important;margin-top: 15px;width: 200px; white-space: nowrap;"
                                *ngIf="('at_risk_toggle' | checkActive : this.formConfig: 'edit-project')">
                                <div class="toggle-content" [ngClass]="{'toggle-content-glow': standard}">{{('secured' |
                                    checkLabel : this.formConfig: 'edit-project':
                                    'Secured')}}</div>
                                <!-- <mat-slide-toggle [checked]="toggleSecuredChecked" (change)="checkSecuredOrRisk()"></mat-slide-toggle> -->
                                <label class="toggle-switch">
                                    <input type="checkbox" [checked]="toggleSecuredChecked"
                                        (click)="checkSecuredOrRisk()" [disabled]="('at_risk_toggle' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id)">
                                    <span class="slider"></span>
                                </label>
                                <div class="toggle-content" [ngClass]="{'toggle-content-glow': risk}">{{('risk' |
                                    checkLabel : this.formConfig: 'edit-project':
                                    'Risk')}}</div>
                            </div>
                        </div>
                        <!-- <div class="row" *ngIf="!multipleQuote">
                               <div class="col-6"
                                   *ngIf="('opportunity' | checkActive : this.formConfig: 'edit-project')">
                                   <div class="content-title">
                                       {{('opportunity' | checkLabel : this.formConfig: 'edit-project': 'Opportunity')}}
                                       <span class="required-star"
                                           *ngIf="('opportunity' | checkMandatedField : this.formConfig: 'edit-project')">
                                           &nbsp;*</span>
                                           <span *ngIf="('opportunity' | checkInfoIcon : this.formConfig: 'edit-project')"> 
                                           <mat-icon class="info-icon" tooltip="{{('oppurtunity' | checkTooltip : this.formConfig: 'edit-project': 'Oppurtunity')}}"
                                           matTooltipClass="primary-tooltip" matTooltipPosition="above">
                                           info_outline
                                       </mat-icon>
                                       </span>
                                   </div>
                                   
                                       <app-input-search-name class="opportunity" [list]="opportunity_list" [required]="('opportunity' | checkMandatedField : this.formConfig: 'edit-project')"  [ngStyle]="{'background':opportunity_color}" [disabled]="opportunity_disable"
                                   placeholder="Select" formControlName="opportunity">
                               </app-input-search-name>
                               </div>
                               <div class="col-4" style="margin-left: -119px;" *ngIf="('quote' | checkActive : this.formConfig: 'edit-project')">
                                   <div class="content-title">
                                       {{('quote' | checkLabel : this.formConfig: 'edit-project':
                                       'Quote')}}
                                       <span class="required-star"
                                           *ngIf="('quote' | checkMandatedField : this.formConfig: 'edit-project')">
                                           &nbsp;*</span>
                                           <span *ngIf="('quote' | checkInfoIcon : this.formConfig: 'edit-project')">
                                       <mat-icon class="info-icon" tooltip="{{('quote' | checkTooltip : this.formConfig: 'edit-project': 'Quote')}}"
                                           matTooltipClass="primary-tooltip" matTooltipPosition="above">
                                           info_outline
                                       </mat-icon>
                                           </span>
                                   </div>
                                  
                                   <mat-form-field [ngStyle]="{'background':quote_color}" class="quote" appearance="outline">
                                       <input style="color: #45546E;font-family: Roboto;" type="number" matInput [readonly]="this.financeFieldDisable || quote_disable" [required]="('quote' | checkMandatedField : this.formConfig: 'edit-project')" placeholder="Quote" formControlName="quote" (keyup)="quoteKeyUp($event)" >
                                   </mat-form-field>
                                   
                               </div>
                               <div class="col-2"></div>
                              
                           <div class="col-2"></div>
                           </div> -->




                        <!-- Second row -->
                        <!-- <div class="row">
                              
                             <div class="col-6"></div>
                           </div> -->
                        <!-- <div class="row" style="margin-top: -20px;" *ngIf="withoutOpportunity"> -->
                        <!-- <div class="col-3"
                               *ngIf="('currency' | checkActive : this.formConfig: 'edit-project')">
                               <div class="content-title">
                                   {{('currency' | checkLabel : this.formConfig: 'edit-project': 'Currency')}}
                                   <span class="required-star"
                                       *ngIf="('currency' | checkMandatedField : this.formConfig: 'edit-project')">
                                       &nbsp;*</span>
                                       <span *ngIf="('currency' | checkInfoIcon : this.formConfig: 'edit-project')">
                                       <mat-icon class="info-icon" tooltip="{{('currency' | checkTooltip : this.formConfig: 'edit-project': 'currency')}}"
                                       matTooltipClass="primary-tooltip" matTooltipPosition="above">
                                       info_outline
                                   </mat-icon>
                                   </span>
                               </div>
                               <app-input-search-name class="currency" [list]="currency_list" [required]="('currency' | checkMandatedField : this.formConfig: 'edit-project')"  [ngStyle]="{'background':currency_color}" [disabled]="currency_disable"
                                   placeholder="Select" formControlName="currency">
                               </app-input-search-name>
                           </div> -->
                        <!-- <div class="col-3"
                           *ngIf="('invoice_template' | checkActive : this.formConfig: 'edit-project')">
                           <div class="content-title">
                               {{('invoice_template' | checkLabel : this.formConfig: 'edit-project':
                               'Invoice Template')}}
                               <span class="required-star"
                                   *ngIf="('invoice_template' | checkMandatedField : this.formConfig: 'edit-project')">
                                   &nbsp;*</span>
                                   <span *ngIf="('invoice_template' | checkInfoIcon : this.formConfig: 'edit-project')">
                                   <mat-icon class="info-icon" tooltip="{{('invoice_template' | checkTooltip : this.formConfig: 'edit-project': 'Invoice Template')}}"
                                   matTooltipClass="primary-tooltip" matTooltipPosition="above">
                                   info_outline
                               </mat-icon>
                               </span>
                           </div>
                           <app-input-search-name class="template" [list]="invoice_template_list" [required]="('invoice_template' | checkMandatedField : this.formConfig: 'edit-project')"  [ngStyle]="{'background':invoice_template_color}" [disabled]="invoice_template_disable"
                               placeholder="Select" formControlName="invoice_template">
                           </app-input-search-name>
                       </div>
                               </div> -->
                               <div *ngIf="withoutOpportunity"  style="height: 44vh;width: 930px;margin-top:20px">
                                <div formArrayName="fieldsArray">
                                  <div *ngFor="let group of stepperFormGroup.get('fieldsArray').controls; let i = index" [formGroupName]="i">
                                    <div class="form-row">
                                        <div class="col-2" style="max-width: 145px;" *ngIf="('po_start_date' | checkActive : this.formConfig: 'edit-project')">
                                            <div class="content-title">
                                              {{ ('po_start_date' | checkLabel : this.formConfig: 'edit-project': 'Start Date') }}
                                              <span class="required-star" *ngIf="('po_start_date' | checkMandatedField : this.formConfig: 'edit-project')">*</span>
                                            </div>
                                            <mat-form-field  appearance="outline" class="disable">
                                              <input matInput placeholder="DD-MMM-YYYY" formControlName="po_start_date" [matDatepicker]="psdDp7"  [disabled]="true"
                                                [required]="('po_start_date' | checkMandatedField : this.formConfig: 'edit-project')">
                                              <mat-datepicker-toggle matSuffix [for]="psdDp7"></mat-datepicker-toggle>
                                              <mat-datepicker #psdDp7></mat-datepicker>
                                            </mat-form-field>
                                          </div>
                                          <div class="col-2" style="max-width: 145px;"  *ngIf="('po_end_date' | checkActive : this.formConfig: 'edit-project')">
                                            <div class="content-title">
                                              {{ ('po_end_date' | checkLabel : this.formConfig: 'edit-project': 'End Date') }}
                                              <span class="required-star" *ngIf="('po_end_date' | checkMandatedField : this.formConfig: 'edit-project')">*</span>
                                            </div>
                                            <mat-form-field class="disable"  appearance="outline">
                                              <input matInput placeholder="DD-MMM-YYYY" formControlName="po_end_date" [matDatepicker]="psdDp8" [disabled]="true"
                                                [required]="('po_end_date' | checkMandatedField : this.formConfig: 'edit-project')">
                                              <mat-datepicker-toggle matSuffix [for]="psdDp8"></mat-datepicker-toggle>
                                              <mat-datepicker #psdDp8></mat-datepicker>
                                            </mat-form-field>
                                          </div>
                                        <div class="col-2" style="max-width: 145px;" *ngIf="('po_date' | checkActive : this.formConfig: 'edit-project')">
                                            <div class="content-title">
                                              {{ ('po_date' | checkLabel : this.formConfig: 'edit-project': 'PO Date') }}
                                              <span class="required-star" *ngIf="('po_date' | checkMandatedField : this.formConfig: 'edit-project')">*</span>
                                            </div>
                                            <mat-form-field class="disable" appearance="outline">
                                              <input matInput placeholder="DD-MMM-YYYY" formControlName="po_date" [matDatepicker]="psdDp" [disabled]="true"
                                                [required]="('po_date' | checkMandatedField : this.formConfig: 'edit-project')">
                                              <mat-datepicker-toggle matSuffix [for]="psdDp"></mat-datepicker-toggle>
                                              <mat-datepicker #psdDp></mat-datepicker>
                                            </mat-form-field>
                                          </div>
                                        <div class="col-2" *ngIf="('po_number' | checkActive : this.formConfig: 'edit-project')">
                                          <div class="content-title">
                                            {{ ('po_number' | checkLabel : this.formConfig: 'edit-project': 'PO Number') }}
                                            <span class="required-star" *ngIf="(!poNonMandate) && MakeFieldsNonMandatory">&nbsp;*</span>
                                          </div>
                                          <mat-form-field class="disable"  appearance="outline">
                                            <input matInput placeholder="Enter here" [required]="(!poNonMandate) && MakeFieldsNonMandatory"
                                              formControlName="po_number" [readonly]="true">
                                          </mat-form-field>
                                        </div>
                                      
                                        <div class="col-2"  *ngIf="('po_value' | checkActive : this.formConfig: 'edit-project')">
                                          <div class="content-title">
                                            {{ ('po_value' | checkLabel : this.formConfig: 'edit-project': 'Order Value') }}
                                            <span class="required-star" *ngIf="('po_value' | checkMandatedField : this.formConfig: 'edit-project') && MakeFieldsNonMandatory">&nbsp;*</span>
                                          </div>
                                          <mat-form-field class="disable"  appearance="outline">
                                            <input matInput placeholder="Enter here" formControlName="po_value" type="text" digitOnly [readonly]="true" [allowDecimal]="true" [digitsAllowed]="po_value_digit"
                                              [required]="('po_value' | checkMandatedField : this.formConfig: 'edit-project') && MakeFieldsNonMandatory" />
                                           
                                          </mat-form-field>
                                         
                                        </div>
                                      
                                        <div class="col-2" *ngIf="('po_reference' | checkActive : this.formConfig: 'edit-project')">
                                          <div class="content-title">
                                            {{ ('po_reference' | checkLabel : this.formConfig: 'edit-project': 'PO Ref#') }}
                                            <span class="required-star" *ngIf="('po_reference' | checkMandatedField : this.formConfig: 'edit-project')">*</span>
                                          </div>
                                          <mat-form-field class="disable" appearance="outline">
                                            <input matInput placeholder="Enter here" formControlName="po_reference" [readonly]="true"
                                              [required]="('po_reference' | checkMandatedField : this.formConfig: 'edit-project')" />
                                          </mat-form-field>
                                        </div>              
                                        <div class="col-2" *ngIf="('payment_terms' | checkActive : this.formConfig: 'edit-project')">
                                          <div class="content-title">
                                            {{ ('payment_terms' | checkLabel : this.formConfig: 'edit-project': 'Payment Terms') }}
                                            <span class="required-star" *ngIf="('payment_terms' | checkMandatedField : this.formConfig: 'edit-project')">*</span>
                                          </div>
                                          <app-input-search-name class="portfolioPay" [list]="payment_list" placeholder="Select One" formControlName="payment_terms" disabled="true"
                                            [required]="('payment_terms' | checkMandatedField : this.formConfig: 'edit-project')"></app-input-search-name>
                                        </div>
                                      
                                        <div class="col-2" *ngIf="('partner' | checkActive : this.formConfig: 'edit-project')">
                                          <div class="content-title">
                                            {{ ('partner' | checkLabel : this.formConfig: 'edit-project': 'Partner') }}
                                            <span class="required-star" *ngIf="('partner' | checkMandatedField : this.formConfig: 'edit-project')">*</span>
                                          </div>
                                          <app-input-search-name [list]="customer_list" placeholder="Select One" formControlName="partner"
                                            [required]="('partner' | checkMandatedField : this.formConfig: 'edit-project')"></app-input-search-name>
                                        </div>
                                      
                                        <div class="col-2" *ngIf="('invoice_template' | checkActive : this.formConfig: 'edit-project')">
                                          <div class="content-title">
                                            {{ ('invoice_template' | checkLabel : this.formConfig: 'edit-project': 'Invoice Template') }}
                                            <span class="required-star" *ngIf="('invoice_template' | checkMandatedField : this.formConfig: 'edit-project')">*</span>
                                          </div>
                                          <app-input-search-name [list]="invoice_template_list" placeholder="Select One" formControlName="invoice_template"
                                            [required]="('invoice_template' | checkMandatedField : this.formConfig: 'edit-project')"></app-input-search-name>
                                        </div>
                                      </div>                                         
                                  </div>
                                </div>
                               
                              </div>

                        <span *ngIf="withOpportunity">
                            <div formArrayName="financial" *ngFor="
                                  let item of stepperFormGroup.get('financial')['controls'];
                                  let i = index
                                " style="margin-top: -10px;margin-left: 15px;">
                                <div [formGroupName]="i">
                                    <div *ngIf="withOpportunity && i==0 && this.formarray.at(i).get('is_active').value==1 "
                                        class="row" style="display :flex;">

                                        <div *ngIf="('opportunity' | checkActive : this.formConfig: 'edit-project')">
                                            <div class="content-title1" *ngIf="i==0">
                                                {{('opportunity' | checkLabel : this.formConfig: 'edit-project':
                                                'Opportunity')}}
                                                <span class="required-star"
                                                    *ngIf="('opportunity' | checkMandatedField : this.formConfig: 'edit-project') && MakeFieldsNonMandatory">
                                                    *</span>
                                                <span
                                                    *ngIf="('opportunity' | checkInfoIcon : this.formConfig: 'edit-project')">
                                                    <mat-icon class="info-icon"
                                                        tooltip="{{('oppurtunity' | checkTooltip : this.formConfig: 'edit-project': 'Oppurtunity')}}"
                                                        >
                                                        info_outline
                                                    </mat-icon>
                                                </span>
                                            </div>
                                            <!-- <app-search-opportunity-master class="opportunity" [isAutocomplete]="true" label="Search"
                                            formControlName="opportunity" [required]="('opportunity' | checkMandatedField : this.formConfig: 'edit-project')"
                                            ></app-search-opportunity-master> -->
                                            <app-input-search-name2 class="opportunity" *ngIf="!childOpportunityFlag"
                                                (change)="getOpportunityData($event,i,0)" [list]="opportunity_list"
                                                [required]="('opportunity-first' | checkMandatedField : this.formConfig: 'edit-project') && MakeFieldsNonMandatory"
                                                placeholder="Select One" formControlName="opportunity_id"
                                                [ngStyle]="{'background':('opportunity-first' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id)  && this.formarray.at(i).get('enable').value==0 ? '#E8E9EE':'#FFFFFF'}"
                                                [disabled]="('opportunity-first' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id) && this.formarray.at(i).get('enable').value==0">
                                            </app-input-search-name2>

                                            <app-input-search-name2 class="opportunity" *ngIf="childOpportunityFlag"
                                                (change)="getParentOpportunityData($event,i,0)" [list]="opportunity_list"
                                                [required]="('opportunity-first' | checkMandatedField : this.formConfig: 'edit-project') && MakeFieldsNonMandatory"
                                                placeholder="Select One" formControlName="opportunity_id"
                                                [ngStyle]="{'background':('opportunity-first' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id)  && this.formarray.at(i).get('enable').value==0 ? '#E8E9EE':'#FFFFFF'}"
                                                [disabled]="('opportunity-first' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id) && this.formarray.at(i).get('enable').value==0">
                                            </app-input-search-name2>

                                         
                                        </div>
                                        <div style="margin-left: 5px;margin-top: 35px;"
                                            *ngIf="('opportunity_status' | checkActive : this.formConfig: 'edit-project')">
                                            <div class="content-title" *ngIf="i==0">
                                                <!-- <span class="required-star"
                                            *ngIf="('opportunity_status' | checkMandatedField : this.formConfig: 'edit-project')">
                                            *</span> -->
                                                <!-- <mat-icon class="info-icon" tooltip="{{('customer_details' | checkTooltip : this.formConfig: 'edit-project': 'Customer')}}"
                                            matTooltipClass="primary-tooltip" matTooltipPosition="above">
                                            info_outline
                                        </mat-icon> -->
                                            </div>
                                            <!-- <div class="row"> -->
                                            <!-- <mat-form-field class="customer_details" appearance="outline">
                                        <input  readonly="true" style="color: #45546E;font-family: Roboto;" matInput disabled="true" matInput placeholder="Customer" formControlName="customer_details">
                                    </mat-form-field> -->

                                            <div tooltip=" Opportunity status -  {{stepperFormGroup.get('financial.' + i + '.opportunity_status').value }}"
                                                placement="right" style="width: max-content"><mat-icon
                                                    style="color: #124559;font-size: 20px;">move_up</mat-icon></div>
                                            <!-- </div> -->
                                        </div>
                                        <div style="margin-left: 2px;"
                                            *ngIf="('quote' | checkActive : this.formConfig: 'edit-project')">
                                            <div class="content-title1" *ngIf="i==0">
                                                {{('quote' | checkLabel : this.formConfig: 'edit-project':
                                                'Quote')}}
                                                <span class="required-star"
                                                    *ngIf="('quote' | checkMandatedField : this.formConfig: 'edit-project') && MakeFieldsNonMandatory">
                                                    *</span>
                                                <span
                                                    *ngIf="('quote' | checkInfoIcon : this.formConfig: 'edit-project')">
                                                    <mat-icon class="info-icon"
                                                        tooltip="{{('quote' | checkTooltip : this.formConfig: 'edit-project': 'Quote')}}"
                                                        >
                                                        info_outline
                                                    </mat-icon>
                                                </span>
                                            </div>
                                            <!-- <div class="row"> -->
                                            <mat-form-field [ngStyle]="{'background':('quote' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id)? '#E8E9EE':'#FFFFFF'}" class="input-field quote"
                                                appearance="outline">
                                                <input class="font-family" style="color: #45546E;" digitOnly
                                                    [isPercentage]="false" [allowDecimal]="true" matInput
                                                    [readonly]="('quote-first' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id)"
                                                    [required]="('quote-first' | checkMandatedField : this.formConfig: 'edit-project') && MakeFieldsNonMandatory"
                                                    placeholder="Enter here" formControlName="quote_id" inputmode="numeric">
                                            </mat-form-field>
                                            <!-- </div> -->
                                        </div>


                                        <div></div>


                                        <!-- <div  *ngIf="!multipleQuote" class="row"> -->
                                        <div style="margin-left: 15px;"
                                            *ngIf="('reason' | checkActive : this.formConfig: 'edit-project')">
                                            <div class="content-title1" *ngIf="i==0">
                                                {{('reason' | checkLabel : this.formConfig: 'edit-project': 'Reason')}}
                                                <span class="required-star"
                                                    *ngIf="('reason' | checkMandatedField : this.formConfig: 'edit-project')">
                                                    *</span>
                                                <span
                                                    *ngIf="('reason' | checkInfoIcon : this.formConfig: 'edit-project')">
                                                    <mat-icon class="info-icon"
                                                        tooltip="{{('reason' | checkTooltip : this.formConfig: 'edit-project': 'reason')}}"
                                                        >
                                                        info_outline
                                                    </mat-icon>
                                                </span>
                                            </div>
                                            <app-input-search-name2 class="reason" [list]="reason_list" [ngStyle]="{'background':('reason-first' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id) && this.formarray.at(i).get('enable').value==0 ? '#E8E9EE':'#FFFFFF'}"
                                                [required]="('reason-first' | checkMandatedField : this.formConfig: 'edit-project')"
                                                [disabled]="('reason-first' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id) && this.formarray.at(i).get('enable').value==0"
                                                placeholder="Select One" formControlName="reason">
                                            </app-input-search-name2>
                                        </div>
                                        <div style="margin-left: 15px;"
                                            *ngIf="('invoice_template' | checkActive : this.formConfig: 'edit-project')">
                                            <div class="content-title1" *ngIf="i==0">
                                                {{('invoice_template' | checkLabel : this.formConfig: 'edit-project':
                                                'Invoice Template')}}
                                                <span class="required-star"
                                                    *ngIf="('invoice_template' | checkMandatedField : this.formConfig: 'edit-project')">
                                                    *</span>
                                                <span
                                                    *ngIf="('invoice_template' | checkInfoIcon : this.formConfig: 'edit-project')">
                                                    <mat-icon class="info-icon"
                                                        tooltip="{{('invoice_template' | checkTooltip : this.formConfig: 'edit-project': 'Invoice Template')}}"
                                                        >
                                                        info_outline
                                                    </mat-icon>
                                                </span>
                                            </div>
                                            <app-input-search-name class="template" [list]="invoice_template_list"
                                                [ngStyle]="{'background':('invoice_template_first' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id)? '#E8E9EE':'#FFFFFF'}"
                                                [required]="('invoice_template_first' | checkMandatedField : this.formConfig: 'edit-project')"
                                                [disabled]="('invoice_template_first' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id)"
                                                placeholder="Select One" formControlName="invoice_template">
                                            </app-input-search-name>
                                        </div>
                                        <div style="margin-left: 15px;"
                                            *ngIf="('po_number_with_opportunity' | checkActive : this.formConfig: 'edit-project')">
                                            <div class="content-title1" *ngIf="i==0">
                                                {{('po_number_with_opportunity' | checkLabel : this.formConfig:
                                                'edit-project': 'PO Number')}}
                                                <span class="required-star" *ngIf="(!poNonMandate) && MakeFieldsNonMandatory">
                                                    *</span>
                                                <span
                                                    *ngIf="('po_number_with_opportunity' | checkInfoIcon : this.formConfig: 'edit-project')">
                                                    <mat-icon class="info-icon"
                                                        tooltip="{{('po_number_with_opportunity' | checkTooltip : this.formConfig: 'edit-project': 'po number')}}"
                                                        >
                                                        info_outline
                                                    </mat-icon>
                                                </span>
                                            </div>
                                            <!-- <div class="row"> -->
                                            <mat-form-field [ngStyle]="{'background':(('po_number_with_opportunity' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id) && this.formarray.at(i).get('enable').value==0) || risk || (('po_data_disable' | checkActive : this.formConfig: 'milestone-creation') && withOpportunity) ? '#E8E9EE':'#FFFFFF'}" class="input-field poNumber"
                                                appearance="outline">
                                                <input class="font-family"
                                                    style="color: #45546E;" matInput
                                                    placeholder="Enter here" [required]="(!poNonMandate) && MakeFieldsNonMandatory"
                                                    formControlName="po_number" [readonly]="(('po_number_with_opportunity' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id) && this.formarray.at(i).get('enable').value==0) || risk || (('po_data_disable' | checkActive : this.formConfig: 'milestone-creation') && withOpportunity)">
                                            </mat-form-field>
                                            <!-- </div> -->
                                        </div>
                                        <div style="margin-left: 15px;"
                                            *ngIf="('po_value_with_opportunity' | checkActive : this.formConfig: 'edit-project')">
                                            <div class="content-title1" *ngIf="i==0">
                                                {{('po_value_with_opportunity' | checkLabel : this.formConfig:
                                                'edit-project': 'Order Value')}}<span
                                                    class="currency-code1">({{this.code}})</span>
                                                <span class="required-star"
                                                    *ngIf="('po_value_with_opportunity' | checkMandatedField : this.formConfig: 'edit-project') && MakeFieldsNonMandatory">
                                                    *</span>
                                                <span
                                                    *ngIf="('po_value_with_opportunity' | checkInfoIcon : this.formConfig: 'edit-project')">
                                                    <mat-icon class="info-icon"
                                                        tooltip="{{('po_value_with_opportunity' | checkTooltip : this.formConfig: 'edit-project': 'Order Value')}}"
                                                        >
                                                        info_outline
                                                    </mat-icon>
                                                </span>
                                            </div>
                                            <!-- <div class="row"> -->
                                            <mat-form-field [ngStyle]="{'background':(('po_value_with_opportunity_first' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id) && this.formarray.at(i).get('enable').value==0) || (('po_data_disable' | checkActive : this.formConfig: 'milestone-creation') && withOpportunity) ? '#E8E9EE':'#FFFFFF'}" class="input-field poValue"
                                                appearance="outline">
                                                <input class="font-family" style="color: #45546E;" type="text" digitOnly
                                                    [isPercentage]="false" [allowDecimal]="true" [digitsAllowed]="po_value_digit"
                                                    [required]="('po_value_with_opportunity_first' | checkMandatedField : this.formConfig: 'edit-project') && MakeFieldsNonMandatory"
                                                    matInput placeholder="Enter here" formControlName="purchase_order"
                                                    [readonly]="(('po_value_with_opportunity_first' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id) && this.formarray.at(i).get('enable').value==0) || (('po_data_disable' | checkActive : this.formConfig: 'milestone-creation') && withOpportunity)" />
                                                <!-- <span class="currency-code">{{this.code}}</span> -->
                                            </mat-form-field>
                                            <!-- </div> -->
                                        </div>
                                        <!-- PO Ref -->
                                        <div style="margin-left: 15px;"
                                            *ngIf="('po_reference' | checkActive : this.formConfig: 'edit-project')">
                                            <div class="content-title1" *ngIf="i==0">
                                                {{('po_reference' | checkLabel : this.formConfig: 'edit-project': 'PO
                                                Ref#')}}
                                                <span class="required-star"
                                                    *ngIf="('po_reference' | checkMandatedField : this.formConfig: 'edit-project')">
                                                    *</span>
                                                <span
                                                    *ngIf="('po_reference' | checkInfoIcon : this.formConfig: 'edit-project')">
                                                    <mat-icon class="info-icon"
                                                        tooltip="{{('po_reference' | checkTooltip : this.formConfig: 'edit-project': 'PO Reference')}}"
                                                        >
                                                        info_outline
                                                    </mat-icon>
                                                </span>
                                            </div>
                                            <!-- <div class="row"> -->
                                            <mat-form-field class="input-field poReference" appearance="outline" [ngStyle]="{'background':('po_reference_first' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id) && this.formarray.at(i).get('enable').value==0 ? '#E8E9EE':'#FFFFFF'}">
                                                <input style="color: #45546E;" matInput
                                                    class="font-family"
                                                    placeholder="Enter here" formControlName="po_reference"
                                                    [disabled]="('po_reference_first' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id) && this.formarray.at(i).get('enable').value==0"
                                                    [required]="('po_reference_first' | checkMandatedField : this.formConfig: 'edit-project')">
                                            </mat-form-field>
                                            <!-- </div> -->
                                        </div>

                                        <!-- PO Date -->
                                        <div style="margin-left: 15px;"
                                            *ngIf="('po_date' | checkActive : this.formConfig: 'edit-project')">
                                            <div class="content-title1" *ngIf="i==0">
                                                {{('po_date' | checkLabel : this.formConfig: 'edit-project': 'Po
                                                Date')}}
                                                <span class="required-star"
                                                    *ngIf="('po_date' | checkMandatedField : this.formConfig: 'edit-project')">
                                                    *</span>
                                                <span
                                                    *ngIf="('po_date' | checkInfoIcon : this.formConfig: 'edit-project')">
                                                    <mat-icon class="info-icon"
                                                        tooltip="{{('po_date' | checkTooltip : this.formConfig: 'edit-project': 'po number')}}"
                                                        >
                                                        info_outline
                                                    </mat-icon>
                                                </span>
                                            </div>
                                            <!-- <div class="row"> -->
                                            <mat-form-field class="input-field po_date" appearance="outline" [ngStyle]="{'background':(('po_date_first' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id) && this.formarray.at(i).get('enable').value==0) || (('po_data_disable' | checkActive : this.formConfig: 'milestone-creation') && withOpportunity) ? '#E8E9EE':'#FFFFFF'}">

                                                <input class="font-family" style="color: #45546E;"
                                                    [required]="('po_date_first' | checkMandatedField : this.formConfig: 'edit-project')"
                                                    [disabled]="(('po_date_first' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id) && this.formarray.at(i).get('enable').value==0) || (('po_data_disable' | checkActive : this.formConfig: 'milestone-creation') && withOpportunity)"
                                                    matInput formControlName="po_date" [matDatepicker]="psdDp"
                                                    name="poDate" placeholder="DD-MMM-YYYY" />
                                                <mat-datepicker-toggle matSuffix [for]="psdDp"></mat-datepicker-toggle>
                                                <mat-datepicker #psdDp></mat-datepicker>
                                            </mat-form-field>
                                            <!-- </div> -->
                                        </div>

                                        <!-- PO Partner -->
                                        <div style="margin-left: 15px;"
                                            *ngIf="('partner' | checkActive : this.formConfig: 'edit-project')">
                                            <div class="content-title1" *ngIf="i==0">
                                                {{('partner' | checkLabel : this.formConfig: 'edit-project':
                                                'Partner')}}
                                                <span class="required-star"
                                                    *ngIf="('partner' | checkMandatedField : this.formConfig: 'edit-project')">
                                                    *</span>
                                                <span
                                                    *ngIf="('partner' | checkInfoIcon : this.formConfig: 'edit-project')">
                                                    <mat-icon class="info-icon"
                                                        tooltip="{{('partner' | checkTooltip : this.formConfig: 'edit-project': 'Partner')}}"
                                                       >
                                                        info_outline
                                                    </mat-icon>
                                                </span>
                                            </div>
                                            <!-- <div class="row"> -->
                                            <app-input-search-name class="partner" [list]="customer_list"
                                                [ngStyle]="{'background':('partner_first' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id) && this.formarray.at(i).get('enable').value==0 ? '#E8E9EE':'#FFFFFF'}"
                                                [required]="('partner_first' | checkMandatedField : this.formConfig: 'edit-project')"
                                                [disabled]="('partner_first' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id) && this.formarray.at(i).get('enable').value==0"
                                                placeholder="Select One" formControlName="partner">
                                            </app-input-search-name>
                                            <!-- </div> -->
                                        </div>

                                        <!-- Payment Terms -->
                                        <div style="margin-left: 15px;"
                                            *ngIf="('payment_terms' | checkActive : this.formConfig: 'edit-project')">
                                            <div class="content-title1" *ngIf="i==0">
                                                {{('payment_terms' | checkLabel : this.formConfig: 'edit-project':
                                                'Payment Terms')}}
                                                <span class="required-star"
                                                    *ngIf="('payment_terms' | checkMandatedField : this.formConfig: 'edit-project')">
                                                    *</span>
                                                <span
                                                    *ngIf="('payment_terms' | checkInfoIcon : this.formConfig: 'edit-project')">
                                                    <mat-icon class="info-icon"
                                                        tooltip="{{('payment_terms' | checkTooltip : this.formConfig: 'edit-project': 'Payment Terms')}}"
                                                        >
                                                        info_outline
                                                    </mat-icon>
                                                </span>
                                            </div>
                                            <!-- <div class="row"> -->
                                            <app-input-search-name2 class="paymentTerms" [list]="payment_list"
                                                [ngStyle]="{'background':('payment_terms_first' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id) && this.formarray.at(i).get('enable').value==0 ? '#E8E9EE':'#FFFFFF'}"
                                                [required]="('payment_terms_first' | checkMandatedField : this.formConfig: 'edit-project')"
                                                [disabled]="('payment_terms_first' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id) && this.formarray.at(i).get('enable').value==0"
                                                placeholder="Select One" formControlName="payment_terms">
                                            </app-input-search-name2>
                                            <!-- </div> -->
                                        </div>
                                        <div (click)="addFinancial()"
                                            style="width: 20px;height: 20px;background: white;border-radius: 22px;border: 1px #8B95A5 solid;align-items: center;gap: 8px;cursor: pointer;
                                    display: flex;margin-top: 53px;margin-left: 10px;justify-content: center;align-items: center;padding-left: 2px;">
                                            <div
                                                style="width: 16px; height: 16px; justify-content: center; align-items: center; display: flex">
                                                <div style="width: 16px; height: 16px; position: relative">
                                                    <div
                                                        style="width: 16px; height: 16px; left: 0px; top: 0px; position: absolute">
                                                    </div>
                                                    <div
                                                        style="width: 9.33px; height: 9.33px; left: 3.33px; top: 3.33px; position: absolute;">
                                                        <mat-icon style="width: 2.33px;height: 3.33px;left: -5.67px;top: -4.67px;position: absolute;color: #45546E;font-size: 19px;
                                            ">add</mat-icon></div>
                                                </div>
                                            </div>
                                        </div>
                                        <div *ngIf="this.stepperFormGroup.get('financial').value.length>1 && this.formarray.at(i).get('enable').value==1"
                                            (click)="removeFinancial(i)"
                                            style="width: 20px;height: 20px;background: white;border-radius: 22px;border: 1px #8B95A5 solid;align-items: center;gap: 8px;cursor: pointer;
                                        display: flex;margin-top: 53px;margin-left: 10px;justify-content: center;align-items: center;padding-left: 2px;">
                                            <div
                                                style="width: 16px; height: 16px; justify-content: center; align-items: center; display: flex">
                                                <div style="width: 16px; height: 16px; position: relative">
                                                    <div
                                                        style="width: 16px; height: 16px; left: 0px; top: 0px; position: absolute">
                                                    </div>
                                                    <div
                                                        style="width: 9.33px; height: 9.33px; left: 3.33px; top: 3.33px; position: absolute;">
                                                        <mat-icon style="width: 2.33px;height: 3.33px;left: -5.67px;top: -4.67px;position: absolute;color: #45546E;font-size: 19px;
                                            ">remove</mat-icon></div>
                                                </div>
                                            </div>
                                        </div>
                                        <!--Multiple Quote-->
                                    </div>
                                    <div *ngIf="withOpportunity && i>0 && this.formarray.at(i).get('is_active').value==1"
                                        class="row" style="display :flex;margin-top: 25px;">

                                        <div *ngIf="('opportunity' | checkActive : this.formConfig: 'edit-project')">
                                            <div class="content-title1" *ngIf="i==0">
                                                {{('opportunity' | checkLabel : this.formConfig: 'edit-project':
                                                'Opportunity')}}
                                                <span class="required-star"
                                                    *ngIf="('opportunity' | checkMandatedField : this.formConfig: 'edit-project') && MakeFieldsNonMandatory" >
                                                    *</span>
                                                <span
                                                    *ngIf="('opportunity' | checkInfoIcon : this.formConfig: 'edit-project')">
                                                    <mat-icon class="info-icon"
                                                        tooltip="{{('oppurtunity' | checkTooltip : this.formConfig: 'edit-project': 'Oppurtunity')}}"
                                                        >
                                                        info_outline
                                                    </mat-icon>
                                                </span>
                                            </div>
                                            <!-- <app-search-opportunity-master class="opportunity" [isAutocomplete]="true" label="Search"
                        formControlName="opportunity" [required]="('opportunity' | checkMandatedField : this.formConfig: 'edit-project')"
                        ></app-search-opportunity-master> -->
                                            <app-input-search-name class="opportunity" *ngIf="!childOpportunityFlag"
                                                (change)="getOpportunityData($event,i,0)" [list]="opportunity_list"
                                                [required]="('opportunity' | checkMandatedField : this.formConfig: 'edit-project') && MakeFieldsNonMandatory"
                                                placeholder="Select One" formControlName="opportunity_id"
                                                [ngStyle]="{'background':('opportunity' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id) && this.formarray.at(i).get('enable').value==0 ? '#E8E9EE':'#FFFFFF'}"
                                                [disabled]="('opportunity' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id) && this.formarray.at(i).get('enable').value==0">
                                            </app-input-search-name>

                                            <app-input-search-name2 class="opportunity" *ngIf="childOpportunityFlag"
                                                (change)="getOpportunityData($event,i,0)" [list]="child_opportunity_list"
                                                [required]="('opportunity' | checkMandatedField : this.formConfig: 'edit-project') && MakeFieldsNonMandatory"
                                                placeholder="Select One" formControlName="opportunity_id"
                                                [ngStyle]="{'background':('opportunity' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id)  && this.formarray.at(i).get('enable').value==0 ? '#E8E9EE':'#FFFFFF'}"
                                                [disabled]="('opportunity' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id) && this.formarray.at(i).get('enable').value==0">
                                            </app-input-search-name2>
                                        </div>
                                        <div style="margin-left: 5px;margin-top: 20px;"
                                            *ngIf="('opportunity_status' | checkActive : this.formConfig: 'edit-project')">
                                            <div class="content-title" *ngIf="i==0">
                                                <!-- <span class="required-star"
                        *ngIf="('opportunity_status' | checkMandatedField : this.formConfig: 'edit-project')">
                        *</span> -->
                                                <!-- <mat-icon class="info-icon" tooltip="{{('customer_details' | checkTooltip : this.formConfig: 'edit-project': 'Customer')}}"
                        matTooltipClass="primary-tooltip" matTooltipPosition="above">
                        info_outline
                    </mat-icon> -->
                                            </div>
                                            <!-- <div class="row"> -->
                                            <!-- <mat-form-field class="customer_details" appearance="outline">
                    <input  readonly="true" style="color: #45546E;font-family: Roboto;" matInput disabled="true" matInput placeholder="Customer" formControlName="customer_details">
                </mat-form-field> -->

                                            <div tooltip="Opportunity status -  {{stepperFormGroup.get('financial.' + i + '.opportunity_status').value }}"
                                                placement="right" style="width: max-content"><mat-icon
                                                    style="color: #124559;font-size: 20px;">move_up</mat-icon></div>
                                            <!-- </div> -->
                                        </div>
                                        <div style="margin-left: 2px;"
                                            *ngIf="('quote' | checkActive : this.formConfig: 'edit-project')">
                                            <div class="content-title1" *ngIf="i==0">
                                                {{('quote' | checkLabel : this.formConfig: 'edit-project':
                                                'Quote')}}
                                                <span class="required-star"
                                                    *ngIf="('quote' | checkMandatedField : this.formConfig: 'edit-project') && MakeFieldsNonMandatory">
                                                    *</span>
                                                <span
                                                    *ngIf="('quote' | checkInfoIcon : this.formConfig: 'edit-project')">
                                                    <mat-icon class="info-icon"
                                                        tooltip="{{('quote' | checkTooltip : this.formConfig: 'edit-project': 'Quote')}}"
                                                        >
                                                        info_outline
                                                    </mat-icon>
                                                </span>
                                            </div>
                                            <!-- <div class="row"> -->
                                            <mat-form-field [ngStyle]="{'background':('quote' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id)? '#E8E9EE':'#FFFFFF'}" class="input-field quote"
                                                appearance="outline">
                                                <input style="color: #45546E;" digitOnly
                                                    class="font-family"
                                                    [isPercentage]="false" [allowDecimal]="true" matInput
                                                    [readonly]="('quote' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id)"
                                                    [required]="('quote' | checkMandatedField : this.formConfig: 'edit-project') && MakeFieldsNonMandatory"
                                                    placeholder="Enter here" formControlName="quote_id" inputmode="numeric">
                                            </mat-form-field>
                                            <!-- </div> -->
                                        </div>


                                        <div></div>


                                        <!-- <div  *ngIf="!multipleQuote" class="row"> -->
                                        <div style="margin-left: 15px;"
                                            *ngIf="('reason' | checkActive : this.formConfig: 'edit-project')">
                                            <div class="content-title1" *ngIf="i==0">
                                                {{('reason' | checkLabel : this.formConfig: 'edit-project': 'Reason')}}
                                                <span class="required-star"
                                                    *ngIf="('reason' | checkMandatedField : this.formConfig: 'edit-project')">
                                                    *</span>
                                                <span
                                                    *ngIf="('reason' | checkInfoIcon : this.formConfig: 'edit-project')">
                                                    <mat-icon class="info-icon"
                                                        tooltip="{{('reason' | checkTooltip : this.formConfig: 'edit-project': 'reason')}}"
                                                        >
                                                        info_outline
                                                    </mat-icon>
                                                </span>
                                            </div>
                                            <app-input-search-name class="reason" [list]="reason_list"
                                                [ngStyle]="{'background':('reason' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id) && this.formarray.at(i).get('enable').value==0 ? '#E8E9EE':'#FFFFFF'}"
                                                [required]="('reason' | checkMandatedField : this.formConfig: 'edit-project') "
                                                [disabled]="('reason' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id) && this.formarray.at(i).get('enable').value==0"
                                                placeholder="Select One" formControlName="reason">
                                            </app-input-search-name>
                                        </div>
                                        <div style="margin-left: 15px;"
                                            *ngIf="('invoice_template' | checkActive : this.formConfig: 'edit-project')">
                                            <div class="content-title1" *ngIf="i==0">
                                                {{('invoice_template' | checkLabel : this.formConfig: 'edit-project':
                                                'Invoice Template')}}
                                                <span class="required-star"
                                                    *ngIf="('invoice_template' | checkMandatedField : this.formConfig: 'edit-project')">
                                                    *</span>
                                                <span
                                                    *ngIf="('invoice_template' | checkInfoIcon : this.formConfig: 'edit-project')">
                                                    <mat-icon class="info-icon"
                                                        tooltip="{{('invoice_template' | checkTooltip : this.formConfig: 'edit-project': 'Invoice Template')}}"
                                                        >
                                                        info_outline
                                                    </mat-icon>
                                                </span>
                                            </div>
                                            <app-input-search-name class="template" [list]="invoice_template_list"
                                                [ngStyle]="{'background':('invoice_template' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id)? '#E8E9EE':'#FFFFFF'}"
                                                [required]="('invoice_template' | checkMandatedField : this.formConfig: 'edit-project')"
                                                [disabled]="('invoice_template' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id)"
                                                placeholder="Select One" formControlName="invoice_template">
                                            </app-input-search-name>
                                        </div>
                                        <div style="margin-left: 15px;"
                                            *ngIf="('po_number_with_opportunity' | checkActive : this.formConfig: 'edit-project') && MakeFieldsNonMandatory">
                                            <div class="content-title1" *ngIf="i==0">
                                                {{('po_number_with_opportunity' | checkLabel : this.formConfig:
                                                'edit-project': 'PO Number')}}
                                                <span class="required-star"
                                                    *ngIf="('po_number_with_opportunity' | checkMandatedField : this.formConfig: 'edit-project')">
                                                    *</span>
                                                <span
                                                    *ngIf="('po_number_with_opportunity' | checkInfoIcon : this.formConfig: 'edit-project')">
                                                    <mat-icon class="info-icon"
                                                        tooltip="{{('po_number_with_opportunity' | checkTooltip : this.formConfig: 'edit-project': 'po number')}}"
                                                        >
                                                        info_outline
                                                    </mat-icon>
                                                </span>
                                            </div>
                                            <!-- <div class="row"> -->
                                            <mat-form-field [ngStyle]="{'background':(('po_number_with_opportunity' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id) && this.formarray.at(i).get('enable').value==0) || risk || (('po_data_disable' | checkActive : this.formConfig: 'milestone-creation') && withOpportunity) ? '#E8E9EE':'#FFFFFF'}" class="input-field poNumber"
                                                appearance="outline">
                                                <input [readonly]="(('po_number_with_opportunity' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id) && this.formarray.at(i).get('enable').value==0) || risk  || (('po_data_disable' | checkActive : this.formConfig: 'milestone-creation') && withOpportunity)"
                                                    class="font-family"
                                                    style="color: #45546E;" matInput
                                                    placeholder="Enter here"
                                                    [required]="('po_number_with_opportunity' | checkMandatedField : this.formConfig: 'edit-project') && MakeFieldsNonMandatory"
                                                    formControlName="po_number">
                                            </mat-form-field>
                                            <!-- </div> -->
                                        </div>
                                        <div style="margin-left: 15px;"
                                            *ngIf="('po_value_with_opportunity' | checkActive : this.formConfig: 'edit-project')">
                                            <div class="content-title1" *ngIf="i==0">
                                                {{('po_value_with_opportunity' | checkLabel : this.formConfig:
                                                'edit-project': 'Order Value')}}<span
                                                    class="currency-code1">({{this.currency_code}})</span>
                                                <span class="required-star"
                                                    *ngIf="('po_value_with_opportunity' | checkMandatedField : this.formConfig: 'edit-project') && MakeFieldsNonMandatory">
                                                    *</span>
                                                <span
                                                    *ngIf="('po_value_with_opportunity' | checkInfoIcon : this.formConfig: 'edit-project')">
                                                    <mat-icon class="info-icon"
                                                        tooltip="{{('po_value_with_opportunity' | checkTooltip : this.formConfig: 'edit-project': 'Order Value')}}"
                                                        >
                                                        info_outline
                                                    </mat-icon>
                                                </span>
                                            </div>
                                            <!-- <div class="row"> -->
                                            <mat-form-field [ngStyle]="{'background':(('po_value_with_opportunity' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id)) || (('po_data_disable' | checkActive : this.formConfig: 'milestone-creation') && withOpportunity)  ? '#E8E9EE':'#FFFFFF'}" class="input-field poValue"
                                                appearance="outline">
                                                <input class="font-family" style="color: #45546E;" type="text" digitOnly
                                                    [isPercentage]="false" [allowDecimal]="true" [digitsAllowed]="po_value_digit"
                                                    [required]="('po_value_with_opportunity' | checkMandatedField : this.formConfig: 'edit-project') && MakeFieldsNonMandatory"
                                                    matInput placeholder="Enter here" formControlName="purchase_order"
                                                    [readonly]="('po_value_with_opportunity' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id) || (('po_data_disable' | checkActive : this.formConfig: 'milestone-creation') && withOpportunity)" />
                                                <!-- <span class="currency-code">{{this.code}}</span> -->
                                            </mat-form-field>
                                            <!-- </div> -->
                                        </div>
                                        <!-- PO Ref -->
                                        <div style="margin-left: 15px;"
                                            *ngIf="('po_reference' | checkActive : this.formConfig: 'edit-project')">
                                            <div class="content-title1" *ngIf="i==0">
                                                {{('po_reference' | checkLabel : this.formConfig: 'edit-project': 'PO
                                                Ref#')}}
                                                <span class="required-star">
                                                    *</span>
                                                <span
                                                    *ngIf="('po_reference' | checkInfoIcon : this.formConfig: 'edit-project')">
                                                    <mat-icon class="info-icon"
                                                        tooltip="{{('po_reference' | checkTooltip : this.formConfig: 'edit-project': 'PO Reference')}}"
                                                        >
                                                        info_outline
                                                    </mat-icon>
                                                </span>
                                            </div>
                                            <!-- <div class="row"> -->
                                            <mat-form-field class="input-field poReference" appearance="outline" [ngStyle]="{'background':('po_reference' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id) && this.formarray.at(i).get('enable').value==0 ? '#E8E9EE':'#FFFFFF'}">
                                                <input class="font-family" style="color: #45546E;" matInput
                                                    placeholder="Enter here" formControlName="po_reference"
                                                    [disabled]="('po_reference' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id) && this.formarray.at(i).get('enable').value==0"
                                                    [required]="('po_reference' | checkMandatedField : this.formConfig: 'edit-project')">
                                            </mat-form-field>
                                            <!-- </div> -->
                                        </div>

                                        <!-- PO Date -->
                                        <div style="margin-left: 15px;"
                                            *ngIf="('po_date' | checkActive : this.formConfig: 'edit-project')">
                                            <div class="content-title1" *ngIf="i==0">
                                                {{('po_date' | checkLabel : this.formConfig: 'edit-project': 'Po
                                                Date')}}
                                                <span class="required-star">
                                                    *</span>
                                                <span
                                                    *ngIf="('po_date' | checkInfoIcon : this.formConfig: 'edit-project')">
                                                    <mat-icon class="info-icon"
                                                        tooltip="{{('po_date' | checkTooltip : this.formConfig: 'edit-project': 'po number')}}"
                                                        >
                                                        info_outline
                                                    </mat-icon>
                                                </span>
                                            </div>
                                            <!-- <div class="row"> -->
                                            <mat-form-field class="input-field po_date" appearance="outline" [ngStyle]="{'background':(('po_date' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id) && this.formarray.at(i).get('enable').value==0)  || (('po_data_disable' | checkActive : this.formConfig: 'milestone-creation') && withOpportunity) ? '#E8E9EE':'#FFFFFF'}">

                                                <input class="font-family" style="color: #45546E;"
                                                    [required]="('po_date' | checkMandatedField : this.formConfig: 'edit-project')"
                                                    matInput formControlName="po_date" [matDatepicker]="psdDp"
                                                    [disabled]="(('po_date' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id) && this.formarray.at(i).get('enable').value==0)  || (('po_data_disable' | checkActive : this.formConfig: 'milestone-creation') && withOpportunity)"
                                                    name="poDate" placeholder="DD-MMM-YYYY" />
                                                <mat-datepicker-toggle matSuffix [for]="psdDp"></mat-datepicker-toggle>
                                                <mat-datepicker #psdDp></mat-datepicker>
                                            </mat-form-field>
                                            <!-- </div> -->
                                        </div>

                                        <!-- PO Partner -->
                                        <div style="margin-left: 15px;"
                                            *ngIf="('partner' | checkActive : this.formConfig: 'edit-project')">
                                            <div class="content-title1" *ngIf="i==0">
                                                {{('partner' | checkLabel : this.formConfig: 'edit-project':
                                                'Partner')}}
                                                <span class="required-star">
                                                    *</span>
                                                <span
                                                    *ngIf="('partner' | checkInfoIcon : this.formConfig: 'edit-project')">
                                                    <mat-icon class="info-icon"
                                                        tooltip="{{('partner' | checkTooltip : this.formConfig: 'edit-project': 'Partner')}}"
                                                        >
                                                        info_outline
                                                    </mat-icon>
                                                </span>
                                            </div>
                                            <!-- <div class="row"> -->
                                            <app-input-search-name class="partner" [list]="customer_list"
                                                [ngStyle]="{'background':('partner' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id) && this.formarray.at(i).get('enable').value==0 ? '#E8E9EE':'#FFFFFF'}"
                                                [required]="('partner' | checkMandatedField : this.formConfig: 'edit-project')"
                                                [disabled]="('partner' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id) && this.formarray.at(i).get('enable').value==0"
                                                placeholder="Select One" formControlName="partner">
                                            </app-input-search-name>
                                            <!-- </div> -->
                                        </div>

                                        <!-- Payment Terms -->
                                        <div style="margin-left: 15px;"
                                            *ngIf="('payment_terms' | checkActive : this.formConfig: 'edit-project')">
                                            <div class="content-title1" *ngIf="i==0">
                                                {{('payment_terms' | checkLabel : this.formConfig: 'edit-project':
                                                'Payment Terms')}}
                                                <span class="required-star">
                                                    *</span>
                                                <span
                                                    *ngIf="('payment_terms' | checkInfoIcon : this.formConfig: 'edit-project')">
                                                    <mat-icon class="info-icon"
                                                        tooltip="{{('payment_terms' | checkTooltip : this.formConfig: 'edit-project': 'Payment Terms')}}"
                                                        >
                                                        info_outline
                                                    </mat-icon>
                                                </span>
                                            </div>
                                            <!-- <div class="row"> -->
                                            <app-input-search-name2 class="paymentTerms" [list]="payment_list"
                                                [ngStyle]="{'background':(('payment_terms' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id) && this.formarray.at(i).get('enable').value==0) || (('po_data_disable' | checkActive : this.formConfig: 'milestone-creation') && withOpportunity) ? '#E8E9EE':'#FFFFFF'}"
                                                [required]="('payment_terms' | checkMandatedField : this.formConfig: 'edit-project')"
                                                [disabled]="(('payment_terms' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id) && this.formarray.at(i).get('enable').value==0) || (('po_data_disable' | checkActive : this.formConfig: 'milestone-creation') && withOpportunity)"
                                                placeholder="Select One" formControlName="payment_terms">
                                            </app-input-search-name2>
                                            <!-- </div> -->
                                        </div>
                                        <div (click)="addFinancial()"
                                            style="width: 20px;height: 20px;background: white;border-radius: 22px;border: 1px #8B95A5 solid;align-items: center;gap: 8px;cursor: pointer;
                display: flex;margin-top: 17px;margin-left: 10px;justify-content: center;align-items: center;padding-left: 2px;">
                                            <div
                                                style="width: 16px; height: 16px; justify-content: center; align-items: center; display: flex">
                                                <div style="width: 16px; height: 16px; position: relative">
                                                    <div
                                                        style="width: 16px; height: 16px; left: 0px; top: 0px; position: absolute">
                                                    </div>
                                                    <div
                                                        style="width: 9.33px; height: 9.33px; left: 3.33px; top: 3.33px; position: absolute;">
                                                        <mat-icon style="width: 2.33px;height: 3.33px;left: -5.67px;top: -4.67px;position: absolute;color: #45546E;font-size: 19px;
                        ">add</mat-icon></div>
                                                </div>
                                            </div>
                                        </div>
                                        <div *ngIf="this.stepperFormGroup.get('financial').value.length>1 && this.formarray.at(i).get('enable').value==1"
                                            (click)="removeFinancial(i)"
                                            style="width: 20px;height: 20px;background: white;border-radius: 22px;border: 1px #8B95A5 solid;align-items: center;gap: 8px;cursor: pointer;
                    display: flex;margin-top: 17px;margin-left: 10px;justify-content: center;align-items: center;padding-left: 2px;">
                                            <div
                                                style="width: 16px; height: 16px; justify-content: center; align-items: center; display: flex">
                                                <div style="width: 16px; height: 16px; position: relative">
                                                    <div
                                                        style="width: 16px; height: 16px; left: 0px; top: 0px; position: absolute">
                                                    </div>
                                                    <div
                                                        style="width: 9.33px; height: 9.33px; left: 3.33px; top: 3.33px; position: absolute;">
                                                        <mat-icon style="width: 2.33px;height: 3.33px;left: -5.67px;top: -4.67px;position: absolute;color: #45546E;font-size: 19px;
                        ">remove</mat-icon></div>
                                                </div>
                                            </div>
                                        </div>
                                        <!--Multiple Quote-->
                                    </div>
                                </div>
                            </div>
                        </span>

                    </div>
                    <!--  Fourth Row -->
                    <div *ngIf="financialStepper" class="footer-buttons">
                        <div class="col-1">
                            <button mat-button class="button-skip"
                                   *ngIf="('skip_button' | checkActive : this.formConfig: 'edit-project') && !MakeFieldsNonMandatory"
                                   (click)="getNextStepper()">{{('skip_button' | checkLabel : this.formConfig:
                                   'portfolio-creation': 'Skip')}}</button>
                        </div>
                        <div class="col-8"></div>
                        <div class="col-2">
                            <button mat-button class="button-back"
                                *ngIf="('back_button' | checkActive : this.formConfig: 'edit-project')"
                                (click)="getPreviousStepper()">{{('back_button' | checkLabel : this.formConfig:
                                'portfolio-creation': 'Back')}}</button>
                        </div>
                        <div></div>
                        <div class="col-1">
                            <button mat-button class="button-next"
                                *ngIf="('next_button' | checkActive : this.formConfig: 'edit-project') && !financialLast"
                                (click)="getNextStepper()">{{('next_button' | checkLabel : this.formConfig:
                                'portfolio-creation': 'Next')}}</button>
                            <button mat-button class="button-next" [disabled]="save_disabled"
                                *ngIf="('create_project' | checkActive : this.formConfig: 'edit-project') && financialLast"
                                (click)="updateProjectDetails()">{{('create_project' | checkLabel : this.formConfig:
                                'edit-project':
                                'Create Project')}}</button>
                            <div class="button-next" *ngIf="save_disabled">
                                <mat-spinner class="green-spinner" diameter="20"></mat-spinner>
                            </div>
                        </div>
                    </div>

                    <!-- </mat-step> -->
                    <!-- People -->
                    <!-- <mat-step label="{{('people' | checkLabel : this.formConfig: 'edit-project': 'People')}}"> -->
                    <!-- </mat-step> -->
                    <!-- Schedule step -->
                    <!-- <mat-step label="{{('schedule' | checkLabel : this.formConfig: 'edit-project': 'Schedule')}}"> -->
                    <div *ngIf="scheduleStepper" class="col-12 content">
                        <div class="row" *ngIf="('week' | checkActive : this.formConfig: 'project-creation')">
                            <div class="col-10">
                                <div class="content-title" style="margin-top:9px">
                                    Week
                                    <span class="required-star"
                                        *ngIf="('week' | checkMandatedField : this.formConfig: 'edit-project')">
                                        &nbsp;*</span>
                                    <span *ngIf="('week' | checkInfoIcon : this.formConfig: 'edit-project')">
                                        <mat-icon class="info-icon"
                                            tooltip="{{('week' | checkTooltip : this.formConfig: 'edit-project': 'Week')}}"
                                            >
                                            info_outline
                                        </mat-icon>
                                    </span>
                                </div>
                                <!-- <mat-button-toggle-group multiple #weekGroup="matButtonToggleGroup">
                                       <mat-button-toggle value="Mon" class="col-1.5">Monday</mat-button-toggle>
                                       <mat-button-toggle value="Tue" class="col-1.5">Tuesday</mat-button-toggle>
                                       <mat-button-toggle value="Wed" class="col-1.5">Wednesday</mat-button-toggle>
                                       <mat-button-toggle value="Thu" class="col-1.5">Thursday</mat-button-toggle>
                                       <mat-button-toggle value="Fri" class="col-1.5">Friday</mat-button-toggle>
                                       <mat-button-toggle value="Sat" class="col-1.5">Saturday</mat-button-toggle>
                                       <mat-button-toggle value="Sun" class="col-1.5">Sunday</mat-button-toggle>
                                   </mat-button-toggle-group> -->
                                <div
                                    style="cursor: pointer;height: 48px; justify-content: flex-end; align-items: flex-end; gap: 24px; display: inline-flex;margin-top: 10px;">
                                    <div
                                        style="height: 48px; justify-content: flex-start; align-items: flex-start; gap: 2px; display: flex">
                                        <div *ngIf="monday" class="week-button" (click)="checkMonday()"
                                            style="flex: 1 1 0; height: 48px; padding: 16px;box-shadow: 0px 2px 1px rgba(0, 0, 0, 0.12); border-top-left-radius: 4px; border-top-right-radius: 4px;justify-content: center; align-items: center; display: flex">
                                            <div class="week-days font-family"
                                                style="font-size: 14px;font-weight: 600; text-transform: capitalize; line-height: 16px; word-wrap: break-word">
                                                Monday</div>
                                        </div>
                                        <div *ngIf="!monday" (click)="checkMonday()"
                                            style="flex: 1 1 0; height: 48px; padding: 16px; background: white; justify-content: center; align-items: center; display: flex">
                                            <div class="font-family"
                                                style="color: black; font-size: 14px;font-weight: 600; text-transform: capitalize; line-height: 16px; word-wrap: break-word">
                                                Monday</div>
                                        </div>
                                        <div *ngIf="tuesday" class="week-button" (click)="checkTuesday()"
                                            style="flex: 1 1 0; height: 48px; padding: 16px;box-shadow: 0px 2px 1px rgba(0, 0, 0, 0.12); justify-content: center; align-items: center; display: flex">
                                            <div class="week-days font-family"
                                                style="font-size: 14px;font-weight: 600; text-transform: capitalize; line-height: 16px; word-wrap: break-word">
                                                Tuesday</div>
                                        </div>
                                        <div *ngIf="!tuesday" (click)="checkTuesday()"
                                            style="flex: 1 1 0; height: 48px; padding: 16px; background: white; justify-content: center; align-items: center; display: flex">
                                            <div class="font-family"
                                                style="color: black; font-size: 14px;font-weight: 600; text-transform: capitalize; line-height: 16px; word-wrap: break-word">
                                                Tuesday</div>
                                        </div>
                                        <div *ngIf="wednesday" class="week-button" (click)="checkWednesday()"
                                            style="flex: 1 1 0; height: 48px; padding: 16px; box-shadow: 0px 2px 1px rgba(0, 0, 0, 0.12); justify-content: center; align-items: center; display: flex">
                                            <div class="week-days font-family"
                                                style="font-size: 14px; font-weight: 600; text-transform: capitalize; line-height: 16px; word-wrap: break-word">
                                                Wednesday</div>
                                        </div>
                                        <div *ngIf="!wednesday" (click)="checkWednesday()"
                                            style="flex: 1 1 0; height: 48px; padding: 16px; background: white; justify-content: center; align-items: center; display: flex">
                                            <div class="font-family"
                                                style="color: black; font-size: 14px;font-weight: 600; text-transform: capitalize; line-height: 16px; word-wrap: break-word">
                                                Wednesday</div>
                                        </div>
                                        <div *ngIf="thursday" class="week-button" (click)="checkThursday()"
                                            style="flex: 1 1 0; height: 48px; padding: 16px;box-shadow: 0px 2px 1px rgba(0, 0, 0, 0.12); justify-content: center; align-items: center; display: flex">
                                            <div class="week-days font-family"
                                                style="font-size: 14px;font-weight: 600; text-transform: capitalize; line-height: 16px; word-wrap: break-word">
                                                Thursday</div>
                                        </div>
                                        <div *ngIf="!thursday" (click)="checkThursday()"
                                            style="flex: 1 1 0; height: 48px; padding: 16px; background: white; justify-content: center; align-items: center; display: flex">
                                            <div class="font-family"
                                                style="color: black; font-size: 14px; font-weight: 600; text-transform: capitalize; line-height: 16px; word-wrap: break-word">
                                                Thursday</div>
                                        </div>
                                        <div *ngIf="friday" class="week-button" (click)="checkFriday()"
                                            style="flex: 1 1 0; height: 48px; padding: 16px; background: #FDF4F3; box-shadow: 0px 2px 1px rgba(0, 0, 0, 0.12); border: 1px #F27A6C solid; justify-content: center; align-items: center; display: flex">
                                            <div class="week-days font-family"
                                                style="font-size: 14px; font-weight: 600; text-transform: capitalize; line-height: 16px; word-wrap: break-word">
                                                Friday</div>
                                        </div>
                                        <div *ngIf="!friday" (click)="checkFriday()"
                                            style="flex: 1 1 0; height: 48px; padding: 16px; background: white; justify-content: center; align-items: center; display: flex">
                                            <div class="font-family"
                                                style="color: black; font-size: 14px;font-weight: 600; text-transform: capitalize; line-height: 16px; word-wrap: break-word">
                                                Friday</div>
                                        </div>
                                        <div *ngIf="saturday" class="week-button" (click)="checkSaturday()"
                                            style="flex: 1 1 0; height: 48px; padding: 16px; box-shadow: 0px 2px 1px rgba(0, 0, 0, 0.12);justify-content: center; align-items: center; display: flex">
                                            <div class="week-days font-family"
                                                style="font-size: 14px;font-weight: 600; text-transform: capitalize; line-height: 16px; word-wrap: break-word">
                                                Saturday</div>
                                        </div>
                                        <div *ngIf="!saturday" (click)="checkSaturday()"
                                            style="flex: 1 1 0; height: 48px; padding: 16px; background: white; justify-content: center; align-items: center; display: flex">
                                            <div class="font-family"
                                                style="color: black; font-size: 14px;font-weight: 600; text-transform: capitalize; line-height: 16px; word-wrap: break-word">
                                                Saturday</div>
                                        </div>
                                        <div *ngIf="sunday" class="week-button" (click)="checkSunday()"
                                            style="flex: 1 1 0; height: 48px; padding: 16px; box-shadow: 0px 2px 1px rgba(0, 0, 0, 0.12); justify-content: center; align-items: center; display: flex">
                                            <div class="week-days font-family"
                                                style="font-size: 14px;font-weight: 600; text-transform: capitalize; line-height: 16px; word-wrap: break-word">
                                                Sunday</div>
                                        </div>
                                        <div *ngIf="!sunday" (click)="checkSunday()"
                                            style="flex: 1 1 0; height: 48px; padding: 16px; background: white; border-top-left-radius: 4px; border-top-right-radius: 4px; justify-content: center; align-items: center; display: flex">
                                            <div class="font-family"
                                                style="color: black; font-size: 14px;font-weight: 600; text-transform: capitalize; line-height: 16px; word-wrap: break-word">
                                                Sunday</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                        <div class="row">
                            <div class="content-title" style="margin-left:15px;font-weight: bold; color: black;">
                                {{('project_working_hours' | checkLabel : this.formConfig: 'project-creation': 'Project
                                Working Hours')}}
                            </div>
                        </div>
                        <div class="row" style="margin-top:-15px">
                            <div class="col-2" *ngIf="('from' | checkActive : this.formConfig: 'project-creation')">
                                <div class="content-title" style="margin-top: 10px;color: #6E7B8F;">
                                    {{('from' | checkLabel : this.formConfig: 'project-creation': 'From')}}
                                    <span class="required-star"
                                        *ngIf="('from' | checkMandatedField : this.formConfig: 'project-creation')">
                                        *</span>
                                    <span *ngIf="('from' | checkInfoIcon : this.formConfig: 'project-creation')">
                                        <mat-icon class="info-icon"
                                            tooltip="{{('from' | checkTooltip : this.formConfig: 'project-creation': 'From')}}">
                                            info_outline
                                        </mat-icon>
                                    </span>
                                </div>
                                <!-- <app-input-search-name [list]="from_list" placeholder="Select" formControlName="from">
                                        </app-input-search-name> -->
                                <!-- <label for="timePicker">From:</label> -->
                                <input formControlName="from" type="time" id="timePicker"
                                    style="width: fit-content;margin-top: 10px;" class="form-control input-time"
                                    bsTimepicker [max]="to">
                                <!-- <div>
                                            <input type="text" [(ngModel)]="Fromhours" (input)="onInputChange()" placeholder="HH" maxlength="2">
                                            <span>:</span>
                                            <input type="text" [(ngModel)]="Fromminutes" (input)="onInputChange()" placeholder="MM" maxlength="2">
                                          </div> -->
                            </div>
                            <div class="col-2" style="margin-left:-50px"
                                *ngIf="('to' | checkActive : this.formConfig: 'project-creation')">
                                <div class="content-title" style="margin-top: 10px;color: #6E7B8F;">
                                    {{('to' | checkLabel : this.formConfig: 'project-creation': 'To')}}
                                    <span class="required-star"
                                        *ngIf="('to' | checkMandatedField : this.formConfig: 'project-creation')">
                                        *</span>
                                    <span *ngIf="('to' | checkInfoIcon : this.formConfig: 'project-creation')">
                                        <mat-icon class="info-icon"
                                            tooltip="{{('to' | checkTooltip : this.formConfig: 'project-creation': 'To')}}">
                                            info_outline
                                        </mat-icon>
                                    </span>
                                </div>

                                <input formControlName="to" type="time" id="timePicker"
                                    style="width: fit-content;margin-top: 10px;" [min]="from"
                                    class="form-control input-time" bsTimepicker>
                                <!-- <div>
                                            <input type="text" [(ngModel)]="Tohours" (input)="onInputChangeTo()" placeholder="HH" maxlength="2">
                                            <span>:</span>
                                            <input type="text" [(ngModel)]="Tominutes" (input)="onInputChangeTo()" placeholder="MM" maxlength="2">
                                          </div> -->
                            </div>
                            
                            <div class="col-2" style="margin-top: 10px;" [ngStyle]="{'margin-left': ('from' | checkActive : this.formConfig: 'project-creation') ? '-25px' : '0px'}"
                            *ngIf="('daily_working_hours' | checkActive : this.formConfig: 'project-creation')">
                            <div class="content-title" style="color: #6E7B8F;">
                                {{('daily_working_hours' | checkLabel : this.formConfig: 'project-creation': 'Daily Hours')}}
                                <span class="required-star"
                                    *ngIf="('daily_working_hours' | checkMandatedField : this.formConfig: 'project-creation')">
                                    *</span>
                                <span
                                    *ngIf="('daily_working_hours' | checkInfoIcon : this.formConfig: 'project-creation')">
                                    <mat-icon class="info-icon"
                                        tooltip="{{('daily_working_hours' | checkTooltip : this.formConfig: 'project-creation':'Daily Working Hours')}}">
                                        info_outline
                                    </mat-icon>
                                </span>
                            </div>
                            <!-- <div class="row"> -->
                            <mat-form-field class="input-field daily_working_hours" appearance="outline">
                                <input class="font-family" style="color: #45546E;" type="text" digitOnly
                                    [isPercentage]="false" [allowDecimal]="true" [maxValue]="24" matInput placeholder="Enter here"
                                    formControlName="daily_working_hours">
                            </mat-form-field>
                            <!-- </div> -->
                            </div>

                            <div class="col-2" style="margin-top: 10px;margin-left: -48px"
                            *ngIf="('monthly_hours' | checkActive : this.formConfig: 'project-creation')">
                            <div class="content-title" style="color: #6E7B8F;">
                                {{('monthly_hours' | checkLabel : this.formConfig: 'project-creation': 'Monthly Working Hours')}}
                                <span class="required-star"
                                    *ngIf="('monthly_hours' | checkMandatedField : this.formConfig: 'project-creation')">
                                    *</span>
                                <span
                                    *ngIf="('monthly_hours' | checkInfoIcon : this.formConfig: 'project-creation')">
                                    <mat-icon class="info-icon"
                                        tooltip="{{('monthly_hours' | checkTooltip : this.formConfig: 'project-creation':'monthly hours')}}">
                                        info_outline
                                    </mat-icon>
                                </span>
                            </div>
                            <!-- <div class="row"> -->
                            <mat-form-field class="input-field monthly_hours" appearance="outline">
                                <input class="font-family" style="color: #45546E;" type="text" digitOnly
                                    [isPercentage]="false" [allowDecimal]="false" matInput placeholder="Enter here"
                                    formControlName="monthly_hours">
                            </mat-form-field>
                            <!-- </div> -->
                            </div>

                     

                            <div class="col-2" style="margin-top: 10px;margin-left: -43px"
                            *ngIf="('leave_paid' | checkActive : this.formConfig: 'project-creation')">
                            <div class="content-title" style="color: #6E7B8F;">
                                {{('leave_paid' | checkLabel : this.formConfig: 'project-creation': 'Leave Paid')}}
                                <span class="required-star"
                                    *ngIf="('leave_paid' | checkMandatedField : this.formConfig: 'project-creation')">
                                    *</span>
                                <span
                                    *ngIf="('leave_paid' | checkInfoIcon : this.formConfig: 'project-creation')">
                                    <mat-icon class="info-icon"
                                        tooltip="{{('leave_paid' | checkTooltip : this.formConfig: 'project-creation':'monthly hours')}}">
                                        info_outline
                                    </mat-icon>
                                </span>
                            </div>
                            <!-- <div class="row"> -->
                            <app-input-search-name class="leave_paid"
                                formControlName="leave_paid" [list]="yes_or_no_list"
                                placeholder="Select One" [ngModelOptions]="{standalone: true}">
                            </app-input-search-name>
                            <!-- </div> -->
                            </div>
                            
                            <div class="col-2" style="margin-top: 10px;margin-left: -56px"
                            *ngIf="('holiday_paid' | checkActive : this.formConfig: 'project-creation')">
                            <div class="content-title" style="color: #6E7B8F;">
                                {{('holiday_paid' | checkLabel : this.formConfig: 'project-creation': 'Holiday Paid')}}
                                <span class="required-star"
                                    *ngIf="('holiday_paid' | checkMandatedField : this.formConfig: 'project-creation')">
                                    *</span>
                                <span
                                    *ngIf="('holiday_paid' | checkInfoIcon : this.formConfig: 'project-creation')">
                                    <mat-icon class="info-icon"
                                        tooltip="{{('holiday_paid' | checkTooltip : this.formConfig: 'project-creation':'monthly hours')}}">
                                        info_outline
                                    </mat-icon>
                                </span>
                            </div>
                            <!-- <div class="row"> -->
                            <app-input-search-name class="holiday_paid"
                                formControlName="holiday_paid" [list]="yes_or_no_list"
                                placeholder="Select One" [ngModelOptions]="{standalone: true}">
                            </app-input-search-name>
                            <!-- </div> -->
                            </div>
                         

                        </div>
                        <div class="row">
                            <div class="content-title"
                                style="margin-left:15px;font-weight: bold; color: black;margin-bottom: -13px;" *ngIf="('location_overall' | checkActive : this.formConfig: 'project-creation')">
                                {{('location_overall' | checkLabel : this.formConfig: 'project-creation': 'Location')}}
                            </div>
                        </div>
                        <div class="col-12"
                            style="height: 185px;overflow-y: scroll;overflow-x:hidden;margin-left:5px;padding-bottom:40px;margin-top: 18px;">
                            <div class="field-row" style="margin-top:-10px" *ngFor="let field of fields; let i = index">


                                <div class="col-3" style="margin-left:-20px"
                                    *ngIf="('work_location' | checkActive : this.formConfig: 'project-creation')">
                                    <div class="content-title" style="color: #6E7B8F;">
                                        {{('work_location' | checkLabel : this.formConfig: 'project-creation': 'Work
                                        Location')}}
                                        <span class="required-star"
                                            *ngIf="('work_location' | checkMandatedField : this.formConfig: 'project-creation')">
                                            *</span>
                                        <span
                                            *ngIf="('work_location' | checkInfoIcon : this.formConfig: 'project-creation')">
                                            <mat-icon class="info-icon"
                                                tooltip="{{('work_location' | checkTooltip : this.formConfig: 'project-creation': 'work location')}}">
                                                info_outline
                                            </mat-icon>
                                        </span>
                                    </div>
                                    <app-multi-select-search2 class="workLocation" [list]="work_location_list"
                                    [ngStyle]="{'background':('work_location' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id)? '#E8E9EE':'#FFFFFF'}"
                                        (input)="storeWLocation(i,$event)"
                                        [disabled]="('work_location' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id)" placeholder="Select"
                                        [ngModelOptions]="{standalone: true}" [(ngModel)]="field.workLocation">
                                    </app-multi-select-search2>
                                </div>


                                <div class="col-3" style="margin-left:15px"
                                    *ngIf="('location' | checkActive : this.formConfig: 'project-creation')">
                                    <div class="content-title" style="color: #6E7B8F;">
                                        {{('location' | checkLabel : this.formConfig: 'project-creation':
                                        'Location')}}
                                        <span class="required-star"
                                            *ngIf="('location' | checkMandatedField : this.formConfig: 'project-creation')">
                                            *</span>
                                        <span
                                            *ngIf="('location' | checkInfoIcon : this.formConfig: 'project-creation')">
                                            <mat-icon class="info-icon"
                                                tooltip="{{('location' | checkTooltip : this.formConfig: 'project-creation': 'Location')}}">
                                                info_outline
                                            </mat-icon>
                                        </span>
                                    </div>
                                    <!-- <mat-form-field class="location" appearance="outline">
                                            <input style="color: #45546E;font-family: Roboto;" matInput placeholder="Location"  (input)="storeLocation(i,$event)" [ngModelOptions]="{standalone: true}" [(ngModel)]="field.location">
                                        </mat-form-field> -->
                                    <app-input-search-name class="location" (input)="storeLocation(i,field.country_id)"
                                    [ngStyle]="{'background':('location' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id)? '#E8E9EE':'#FFFFFF'}"
                                        [list]="country_list" placeholder="Select One" [ngModelOptions]="{standalone: true}"
                                        [disabled]="('location' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id)"
                                        [(ngModel)]="field.country_id">
                                    </app-input-search-name>
                                </div>

                                

                                <div class="col-3"
                                    *ngIf="('holiday_calendar' | checkActive : this.formConfig: 'project-creation')">
                                    <div class="content-title" style="color: #6E7B8F;">
                                        {{('holiday_calendar' | checkLabel : this.formConfig: 'project-creation':
                                        'Choose Holiday Calendar')}}
                                        <span class="required-star"
                                            *ngIf="('holiday_calendar' | checkMandatedField : this.formConfig: 'project-creation')">
                                            *</span>
                                        <span
                                            *ngIf="('holiday_calendar' | checkInfoIcon : this.formConfig: 'project-creation')">
                                            <mat-icon class="info-icon"
                                                tooltip="{{('holiday_calendar' | checkTooltip : this.formConfig: 'project-creation': 'holiday calendar')}}">
                                                info_outline
                                            </mat-icon>
                                        </span>
                                    </div>
                                    <app-input-search-name class="holidayCalendar"
                                        (change)="storeHolidayCalendar(i,$event)" [list]="holiday_calendar_list"
                                        [ngStyle]="{'background':('holiday_calendar' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id)? '#E8E9EE':'#FFFFFF'}"
                                        [disabled]="('holiday_calendar' | checkDisableStatus : this.formConfig: 'edit-project': this.data.item_status_id)" placeholder="Select One"
                                        [ngModelOptions]="{standalone: true}" [(ngModel)]="field.calendar">
                                    </app-input-search-name>

                                </div>
                                <div class="col-2" style="margin-left: 88px;margin-top: 44px;">
                                    <mat-icon style="font-size: 17px;color: #5F6C81;" *ngIf="i!=0"
                                        (click)="removeLocationField(i)">remove_circle_outline</mat-icon>
                                </div>


                                <!-- </div> -->
                            </div>

                            <div class="row">
                                <div class="col-5"></div>
                                <div class="col-5"></div>
                                <div class="col-2"
                                    *ngIf="('location_add' | checkActive : this.formConfig: 'edit-project')">
                                    <div class="add_new_color" (click)="addField()">
                                        {{('location_add' | checkLabel : this.formConfig: 'edit-project':
                                        '+ Add')}}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- </div> -->
                    </div>
                    <div *ngIf="scheduleStepper" class="footer-buttons">
                        <div class="col-1">
                            <button mat-button class="button-skip"
                                *ngIf="('skip_button' | checkActive : this.formConfig: 'edit-project') && scheduleSkip && !scheduleLast"
                                (click)="getNextStepper()">{{('skip_button' | checkLabel : this.formConfig:
                                'edit-project':
                                'Skip')}}</button>
                        </div>
                        <div class="col-8"></div>
                        <div class="col-2">
                            <button mat-button class="button-back"
                                *ngIf="('back_button' | checkActive : this.formConfig: 'edit-project') && !scheduleLast"
                                (click)="getPreviousStepper()">{{('back_button' | checkLabel : this.formConfig:
                                'edit-project': 'Back')}}</button>
                        </div>
                        <div></div>
                        <div class="col-1">
                            <button mat-button class="button-next"
                                *ngIf="('next_button' | checkActive : this.formConfig: 'edit-project') && !scheduleLast"
                                (click)="getNextStepper()">Next</button>
                            <button mat-button class="button-next" style="margin-left:-75px" [disabled]="save_disabled"
                                *ngIf="('create_project' | checkActive : this.formConfig: 'edit-project') && scheduleLast"
                                (click)="updateProjectDetails()">{{('create_project' | checkLabel : this.formConfig:
                                'edit-project':
                                'Create Project')}}</button>
                            <div class="button-next" *ngIf="save_disabled">
                                <mat-spinner class="green-spinner" diameter="20"></mat-spinner>
                            </div>
                        </div>
                    </div>
                    <!-- </mat-step>-->
                    <!-- Template -->
                    <!-- <mat-step label="{{('template' | checkLabel : this.formConfig: 'portfolio-creation': 'Template')}}"> -->
                    <!-- </mat-step>                    -->
                    <!--Advance option-->
                    <!-- <mat-step label="{{('advance_option' | checkLabel : this.formConfig: 'edit-project': 'Advance Option')}}"> -->
                    <div [hidden]="!advanceStepper" class="col-12 content ">
                        <div class="row">

                            <div class="col-12" *ngIf="('tag' | checkActive : this.formConfig: 'edit-project')">
                                <div class="content-title row">
                                    Tag This Project
                                    <span> &nbsp;</span>
                                </div>
                                <!-- <div class="row">

                                       <mat-icon>add_circle</mat-icon>
                                       Add tag
                                   </div> -->
                                <app-tag [add]="add" [existingTag]="existingTag"
                                    (valueEmitted)="handleValueEmitted($event)"></app-tag>
                                <!-- <app-common-tag [add]="add" [mode]="modeForTag" [application_id]="application_id" [unique_id_2]="unique_id_2" (valueEmitted)="handleValueEmitted($event)" ></app-common-tag> -->
                            </div>
                        </div>
                    </div>
                    <div *ngIf="advanceStepper" class="footer-buttons">
                        <!-- <div class = "col-1">
                           <button mat-button class="button-skip" *ngIf = "('skip_button' | checkActive : this.formConfig: 'edit-project')" matStepperNext>{{('skip_button' | checkLabel : this.formConfig: 'edit-project': 'Skip')}}</button>
                       </div> -->
                        <div class="col-1"></div>
                        <div class="col-8"></div>
                        <div class="col-2">
                            <button mat-button class="button-back" style="margin-left:74px"
                                *ngIf="('back_button' | checkActive : this.formConfig: 'edit-project')"
                                (click)="getPreviousStepper()">{{('back_button' | checkLabel : this.formConfig:
                                'edit-project': 'Back')}}</button>
                        </div>
                        <div></div>
                        <div *ngIf="!save_status" class="col-1">
                            <button mat-button class="button-next" style="margin-left:-18px" [disabled]="save_disabled"
                                *ngIf="('create_project' | checkActive : this.formConfig: 'edit-project') && tagLast"
                                (click)="updateProjectDetails()">{{('create_project' | checkLabel : this.formConfig:
                                'edit-project':
                                'Create Project')}}</button>
                            <div class="button-next" *ngIf="save_disabled">
                                <mat-spinner class="green-spinner" diameter="20"></mat-spinner>
                            </div>
                        </div>
                    </div>
                    <!-- </mat-step> -->
                    <!-- </mat-horizontal-stepper> -->
                </form>
            </div>
        </div>
    </span>
</div>