import {
  Component,
  OnInit,
  Renderer2,
  ViewChild,
  Output,
  Inject,
  HostListener,
  InjectionToken,
} from '@angular/core';
import {
  FormArray,
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { PmMasterService } from 'src/app/modules/project-management/services/pm-master.service';
import {
  startWith,
  debounceTime,
  distinctUntilChanged,
  map,
} from 'rxjs/operators';
import {
  MatDialog,
  MatDialogRef,
  MAT_DIALOG_DATA,
} from '@angular/material/dialog';
import {
  MomentDateAdapter,
  MAT_MOMENT_DATE_ADAPTER_OPTIONS,
} from '@angular/material-moment-adapter';
import {
  DateAdapter,
  MAT_DATE_LOCALE,
  MAT_DATE_FORMATS,
} from '@angular/material/core';
import { Observable } from 'rxjs';
import { EventEmitter } from '@angular/core';
import * as _ from 'underscore';
import { MatButtonToggleGroup } from '@angular/material/button-toggle';
import { TagComponent } from 'src/app/modules/project-management/shared-lazy-loaded/components/tag/tag.component';
import { v4 as uuidv4 } from 'uuid';
import { LoginService } from 'src/app/services/login/login.service';
import { UtilityService } from 'src/app/services/utility/utility.service';
import { MatStepper } from '@angular/material/stepper';
import { ThrowStmt } from '@angular/compiler';
import { ProjectCreationService } from '../../services/project-creation.service';
import { ToastrService } from 'ngx-toastr';
import moment from 'moment';
import { MAT_AUTOCOMPLETE_SCROLL_STRATEGY } from '@angular/material/autocomplete';
import { ScrollStrategy } from '@angular/cdk/overlay';
import { Overlay } from '@angular/cdk/overlay';
import { tickStep } from 'd3';
import { ToasterMessageService } from 'src/app/modules/project-management/shared-lazy-loaded/components/toaster-message/toaster-message.service';
import { GetNameByIdPipe } from 'src/app/modules/project-management/shared-lazy-loaded/pipes/get-name-by-id.pipe';

export const TOASTER_EDIT_MESSAGE_SERVICE_TOKEN =
  new InjectionToken<ToasterMessageService>(
    'TOASTER_EDIT_MESSAGE_SERVICE_TOKEN'
  );
import Swal from 'sweetalert2';

@Component({
  selector: 'app-edit-project',
  templateUrl: './edit-project.component.html',
  styleUrls: ['./edit-project.component.scss'],
  providers: [
    {
      provide: DateAdapter,
      useClass: MomentDateAdapter,
      deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS],
    },
    {
      provide: MAT_DATE_FORMATS,
      useValue: {
        parse: {
          dateInput: 'DD-MMM-YYYY',
        },
        display: {
          dateInput: 'DD-MMM-YYYY',
          monthYearLabel: 'MMM YYYY',
        },
      },
    },
    {
      provide: TOASTER_EDIT_MESSAGE_SERVICE_TOKEN,
      useClass: ToasterMessageService,
    },
    GetNameByIdPipe
  ],
})
export class EditProjectComponent implements OnInit {
  @HostListener('window:keyup.esc') onKeyUp() {
    this.onCloseClick();
  }
  @HostListener('document:keypress', ['$event'])
  handleKeyboardEvent(event: KeyboardEvent) {
    if (event.key === 'Enter') {
      event.preventDefault();
    }
  }
  button: string = '#EE4961';
  primary: string = '#FFFFFF';
  secondary: string = '#f5f5f5';
  loading: boolean = false;
  stepperData: any;
  retrieveMessages: any;
  customer_list: any[] = [];
  payment_list: any[] = [];
  product_category_list: any = [];
  child_opportunity_list: any = [];
  revenue_type_list: any = [];
  delivery_type_list: any = [];
  toggleOpportunityChecked: boolean = false;
  toggleSecuredChecked: boolean = false;
  yes_or_no_list: any = [
    {
      id: '1',
      name: 'Yes',
    },
    {
      id: '0',
      name: 'No',
    },
  ];
  stepperFormGroup = this.formBuilder.group({
    project_code: [''],
    project_name: [''],
    portfolio: [''],
    startDate: [''],
    endDate: [''],
    quote: [''],
    project_type: [''],
    from: [''],
    to: [''],
    people: [''],
    ad_group: [''],
    person_date: [''],
    person_role: [''],
    person_split: [''],
    resourceStartDate: [''],
    resourceEndDate: [''],
    invoice_template: [''],
    po_number: [''],
    po_value: [''],
    currency: [''],
    opportunity: [''],
    employee_name: [''],
    customer_details: [''],
    entity: [''],
    division: [''],
    sub_division: [''],
    description: [''],
    textInputControl: [''],
    monthly_hours: [''],
    daily_working_hours: [''],
    leave_paid: [''],
    holiday_paid: [''],
    profit_center: [''],
    p_and_l: [''],
    legal_entity: [''],
    financial: new FormArray([]),
    fieldsArray:new FormArray([]),
    payment_terms: [''],
    partner: [''],
    po_date: [''],
    po_reference: [''],
    sow_reference_number: [''],
    child_customer: [''],
    customer_id: [''],
    product_category: [''],
    delivery_type: [''],
    revenue_type: [''],
  });
  formConfig: any = [];
  opportunity_status_id: any;
  allStepperData: any = [
    {
      id: 1,
      data: '1',
      type: 'details',
      label: 'Project Details',
      is_selected: true,
      is_crossed: false,
      is_completed: false,
      is_active: true,
    },
    {
      id: 2,
      data: '2',
      type: 'es',
      label: 'Enterprise Structure',
      is_selected: false,
      is_crossed: false,
      is_completed: false,
      is_active: true,
    },
    {
      id: 3,
      data: '3',
      type: 'financial',
      label: 'Financial',
      is_selected: false,
      is_crossed: false,
      is_completed: false,
      is_active: true,
    },
    {
      id: 4,
      data: '4',
      type: 'schedule',
      label: 'Schedule',
      is_selected: false,
      is_crossed: false,
      is_completed: false,
      is_active: true,
    },
    {
      id: 5,
      data: '5',
      type: 'advance',
      label: 'Advance Options',
      is_selected: false,
      is_crossed: false,
      is_completed: false,
      is_active: true,
    },
  ];
  ServiceId: any;
  detailStepper = true;
  enterpriseStepper = false;
  financialStepper = false;
  peopleStepper = false;
  templateStepper = false;
  scheduleStepper = false;
  advanceStepper = false;
  week_array = [];
  monday: boolean;
  tuesday: boolean;
  wednesday: boolean;
  thursday: boolean;
  friday: boolean;
  saturday: boolean;
   item_status:any;
  save_status: boolean = false;
  sunday: boolean;
  stepper_color: any;
  stepper_font: any;
  fields: any[] = [
    { workLocation: '', location: '', calendar: '', country_id: '' },
  ];
  refresh: boolean;
  inertnal_service_type_id: any;
  valid: boolean;
  codeDuplicated: boolean;
  empty: boolean = true;
  code_length: any;
  name_length: any;
  selectedOption: any | null = null;
  changeInServiceType: boolean = false;
  project_code_list: any;
  defaultValue: boolean = true;
  radioChecked: boolean = false;
  portfolio_list: any[] = [];
  portfolio_start: any;
  portfolio_end: any;
  customer: any = '-';
  disableInternalOption: boolean = false;
  portfolio_name: any = '-';
  customer_id: any;
  risk: boolean = false;
  opportunity_list: any[] = [];
  project_type_color: any;
  project_type_disable = true;
  project_type_list: any[] = [];
  cards: any = [];
  detailsSkip: boolean = false;
  esSkip: boolean = false;
  financialSkip: boolean = false;
  scheduleSkip: boolean = false;
  templateSkip: boolean = false;
  peopleSkip: boolean = false;
  detailsLast: boolean = false;
  esLast: boolean = false;
  financialLast: boolean = false;
  scheduleLast: boolean = false;
  templateLast: boolean = false;
  peopleLast: boolean = false;
  tagLast: boolean = false;
  entityList: any = [];
  MakeFieldsNonMandatory: boolean = true;
  childOpportunityFlag: boolean = false;
  subDivisionList: any = [];
  divisionList: any = [];
  displayList: any = [];
  ESdata: any;
  mode: any;
  selectedEntity: any;
  selectedData: any = [];

  selectedIndex: any;
  displaySubDivision: boolean = false;
  displayDivision: boolean = false;
  displayEsList: boolean = false;
  check: boolean = false;
  subDivision: any;
  division: any;
  entity: any;
  Filterdata: any;
  currency_list: any[] = [];
  invoice_template_list: any[] = [];
  work_location_list: any[] = [];
  holiday_calendar_list: any[] = [];
  add: boolean = true;
  tags: any = [];
  existingTag: any = [];
  data: any;
  mode1: any;
  disableOptions: boolean = false;
  project_code_color: any;
  project_code_disable = false;
  project_name_color: any;
  project_name_disable = true;
  project_start_date_color: any;
  project_start_date_disable = true;
  project_end_date_color: any;
  project_end_date_disable = true;
  portfolio_color: any;
  portfolio_disable = true;
  legalEntity_disable = true;
  entity_color: any;
  entity_disable = false;
  division_color: any;
  division_disable = false;
  sub_division_color: any;
  sub_division_disable = false;
  opportunity_color: any;
  opportunity_disable = false;
  quote_color: any;
  quote_disable = false;
  po_value_color: any;
  po_value_disable = false;
  po_number_color: any;
  po_number_disable = false;
  currency_color: any;
  currency_disable = false;
  invoice_template_color: any;
  invoice_template_disable = false;
  project_type_default: number;
  dateLogic: boolean;
  save_disabled: boolean = false;
  work_location_color: any;
  work_location_disable = false;
  result: any;
  location_color: any;
  location_disable = false;
  holiday_calendar_color: any;
  holiday_calendar_disable = false;
  monthly_hours_color: any;
  monthly_hours_disable = false;
  modeForTag: any = 'Edit';
  unique_id_2: any;
  application_id: any = 2;
  profit_center_disable = true;
  profit_center_color: any;
  p_and_l_list: any;
  country_list: any;
  standard: boolean;
  multipleQuote: boolean;
  opportunity_status: any;
  financeFieldDisable: boolean;
  quote_id: any;
  financialValue: any;
  projectFinnacialData: any = [];
  code: any;
  formarray: any;
  withoutOpportunity: boolean = false;
  withOpportunity: boolean = true;
  reason_list: any = [];
  opportunity_status_list: any;
  currency_code: any;
  poNonMandate: boolean = false;
  legal_entity_list: any[] = [];
  status_flag: boolean;
  shades: any;
  projectImage: any;
  legal_entity: any;
  intergeration: number;
  external_id: any;
  fontStyle: any;
  TandM_selected: boolean;
  min_start_date: any;
  max_end_date: any;
  fieldOutline: any;
  scrollColor: any;
  child_customer:boolean=false
  child_customer_list:any=[]
  totalMilestoneValue:number=0
  milestoneList:any=[]
  po_value_digit:number=15
  protfolioChanged:boolean=false
  isSowReferenceNumberEmpty: boolean = false;
  isSowReferenceNumberRefresh: boolean = false;
  SowReferenceNumberDuplicated: boolean = false;
  sowReferenceNumberValid:boolean = false;
  invalidSowReferenceNumberLength = false;
  sowReferenceNumberConfig: any;
  sowInvalidLengthMsg: any;
  sowRefDuplicatedMsg: any;
  milestone_grouped_data:any=[];
  oldData: any=[];
  oldFormValue: any;
  newData: any;
  oldFinancialData: any;
  newFinancialData: any;
  constructor(public renderer: Renderer2,
    private formBuilder: FormBuilder,
    private pmMasterService: PmMasterService,
    @Inject(TOASTER_EDIT_MESSAGE_SERVICE_TOKEN)
    private toasterService: ToasterMessageService,
    public dialog: MatDialog,
    private loginService: LoginService,
    private utilityService: UtilityService,
    private ProjectCreationService: ProjectCreationService,
    public dialogRef: MatDialogRef<EditProjectComponent>,
    private getNameByIdPipe: GetNameByIdPipe,
    @Inject(MAT_DIALOG_DATA) public dialogData: DialogData
  ) {}

  async ngOnInit() {
    this.mode1 = this.dialogData.mode;
    this.data = this.dialogData.data;
    this.unique_id_2 = this.data.item_id;
    document.documentElement.style.setProperty('--primary', this.primary);
    document.documentElement.style.setProperty('--secondary', this.secondary);
    // document.documentElement.style.setProperty('--button', this.button)
    this.loading = true;
    this.country_list = this.pmMasterService.country_list;
    await this.pmMasterService.getPMFormCustomizeConfigV().then((res) => {
      this.formConfig = res;
    });
    const retrieveStyles = _.where(this.formConfig, {
      type: 'project-theme',
      field_name: 'styles',
      is_active: true,
    });
    this.button =
      retrieveStyles.length > 0
        ? retrieveStyles[0].data.button_color
          ? retrieveStyles[0].data.button_color
          : '#90ee90'
        : '#90ee90';
    this.shades =
      retrieveStyles.length > 0
        ? retrieveStyles[0].data.shades_color
          ? retrieveStyles[0].data.shades_color
          : '#C9E3B4'
        : '#C9E3B4';
    this.fieldOutline =
      retrieveStyles.length > 0
        ? retrieveStyles[0].data.field_outline_color
          ? retrieveStyles[0].data.field_outline_color
          : '#808080'
        : '#808080';
    this.projectImage =
      retrieveStyles.length > 0
        ? retrieveStyles[0].data.project_image
          ? retrieveStyles[0].data.project_image
          : 'https://assets.kebs.app/MicrosoftTeams-image%20(16).png'
        : 'https://assets.kebs.app/MicrosoftTeams-image%20(16).png';
    document.documentElement.style.setProperty('--editshades', this.shades);
    document.documentElement.style.setProperty('--editbutton', this.button);
    this.fontStyle =
      retrieveStyles.length > 0
        ? retrieveStyles[0].data.font_style
          ? retrieveStyles[0].data.font_style
          : 'Roboto'
        : 'Roboto';
    document.documentElement.style.setProperty(
      '--editProjectFont',
      this.fontStyle
    );
    document.documentElement.style.setProperty(
      '--editFieldOutline',
      this.fieldOutline
    );
    console.log(this.fieldOutline);
    this.scrollColor =
      retrieveStyles.length > 0
        ? retrieveStyles[0].data.scroll_color
          ? retrieveStyles[0].data.scroll_color
          : '#90ee90'
        : '#90ee90';
    document.documentElement.style.setProperty(
      '--project2editScroll',
      this.scrollColor
    );
    const po_value_digit = _.where(this.formConfig, {
      type: 'project-creation',
      field_name: 'value_digit',
    });
    if (po_value_digit.length > 0) {
      this.po_value_digit = po_value_digit[0].default_value
        ? po_value_digit[0].default_value
        : 15;
    }
    await this.pmMasterService.getCustomerList().then((res: any) => {
      this.customer_list = this.pmMasterService.customer_list;
    });

    await this.pmMasterService.getPaymentTermsList().then((res: any) => {
      if (res['messType'] == 'S') {
        this.payment_list = res['data'];
      }
    });

    await this.pmMasterService.getProductCategory().then((res: any) => {
      if (res['messType'] == 'S') {
        this.product_category_list = res['data'];
      }
    });

    await this.pmMasterService.getDeliveryTypeList().then((res: any) => {
      if (res['messType'] == 'S') {
        this.delivery_type_list = res['data'];
      }
    });

    await this.pmMasterService.getRevenueTypeList().then((res: any) => {
      if (res['messType'] == 'S') {
        this.revenue_type_list = res['data'];
      }
    });
    this.retrieveMessages = _.where(this.formConfig, {
      type: 'edit-project',
      field_name: 'messages',
      is_active: true,
    });

    //console.log('Status', this.data.item_status_id)
    this.status_flag = true;
    await this.ProjectCreationService.getMaxMinProjectDates(
      this.data.project_id,
      this.data.item_id
    ).then((res) => {
      if (res['messType'] == 'S') {
        if (res['data'].length > 0) {
          this.min_start_date = res['data'][0]['min_date'];
          this.max_end_date = res['data'][0]['max_date'];
        }
      }
    });

    this.p_and_l_list = this.pmMasterService.p_and_l_list;
    const customerConfig = _.where(this.formConfig, {
      type: 'edit-project',
      field_name: 'child_customer',
      is_active: 1,
    });
    if (customerConfig.length > 0) {
      this.child_customer = true;
    } else {
      this.child_customer = true;
    }
    const status = _.where(this.formConfig, {
      type: 'project-creation',
      field_name: 'opportunity',
      is_active: true,
    });
    this.opportunity_status_list =
      status.length > 0 ? status[0].status_list : [26, 27, 28];
    // this.formConfig=this.pmMasterService.form_config
    this.opportunity_status_id = status.length > 0 ? status[0].status_id : 0;
    this.opportunity_status_list =
      status.length > 0 ? status[0].status_list : [26, 27, 28];
    const details = _.where(this.formConfig, {
      type: 'edit-project',
      field_name: 'default_stepper',
      is_active: true,
    });
    this.stepperData =
      details.length > 0
        ? _.where(details[0].values, { is_active: true })
        : this.allStepperData;
    //console.log(this.stepperData)
    this.stepper_color =
      details.length > 0
        ? details[0].color
          ? details[0].color
          : '#90ee90'
        : '#90ee90';
    this.stepper_font =
      details.length > 0
        ? details[0].font
          ? details[0].font
          : 'Roboto'
        : 'Roboto';
    const retrieveCodeLength = _.where(this.formConfig, {
      type: 'edit-project',
      field_name: 'project_code',
      is_active: true,
    });
    this.code_length =
      retrieveCodeLength.length > 0 ? retrieveCodeLength[0].length : 20;
    const retrieveNameLength = _.where(this.formConfig, {
      type: 'edit-project',
      field_name: 'project_name',
      is_active: true,
    });
    this.name_length =
      retrieveNameLength.length > 0 ? retrieveNameLength[0].maxLength : 300;
    
    // this.addFinancial()
    const intg = _.where(this.formConfig, {
      type: 'project-creation',
      field_name: 'outbound_intg',
      is_active: true,
    });
    this.intergeration = intg.length > 0 ? intg[0].intergerate : 0;
    const checkChildOpportunity = _.where(this.formConfig, {
      type: 'project-creation',
      field_name: 'child_opportunity',
      is_active: true,
    });
    if (checkChildOpportunity.length > 0) {
      this.childOpportunityFlag = true;
    } else {
      this.childOpportunityFlag = false;
    }
    await this.ProjectCreationService.getPortfolioList().then(
      async (res: any) => {
        //console.log(res)
        this.portfolio_list = res['data'];
      }
    );

    await this.pmMasterService.getEntityList().then((res: any) => {
      if (res) {
        this.legal_entity_list = this.pmMasterService.entity_list;
      }
    });

    this.stepperFormGroup
      .get('portfolio')
      .valueChanges.subscribe(async (res) => {
        //console.log(res)
        if (res) {
          if (res != this.data.project_id) {
            this.protfolioChanged = true;
            let portfolio_result = _.where(this.portfolio_list, { id: res });

            if (portfolio_result.length > 0) {
              this.portfolio_name = portfolio_result[0]['p_name'];
              this.customer = portfolio_result[0]['customer_name'];
              this.customer_id = portfolio_result[0]['end_customer_id'];
              this.portfolio_start = portfolio_result[0]['planned_start_date'];
              this.portfolio_end = portfolio_result[0]['planned_end_date'];
              this.stepperFormGroup.patchValue({
                [`customer_details`]:
                  portfolio_result[0]['customer_name'] || '',
              });
            }

            console.log('Customer' + this.customer_id);

            this.ProjectCreationService.getCustomerProjectCode(
              this.customer_id
            ).then((res) => {
              if (res['messType'] == 'S') {
                this.stepperFormGroup.patchValue({ project_code: res['data'] });
              }
            });
            if (this.child_customer) {
              this.child_customer_list = _.filter(
                this.customer_list,
                (customer) =>
                  parseInt(customer.parent_account) == this.customer_id
              );
              console.log(this.child_customer_list);
              if (this.child_customer_list.length == 0) {
                this.child_customer_list.push({
                  id: this.customer_id,
                  name: this.customer,
                });
                this.stepperFormGroup.patchValue({
                  child_customer: this.customer_id,
                });
              }
            }

            await this.ProjectCreationService.getOpportunity(
              this.customer_id,
              this.opportunity_status_list,
              'create'
            ).then((res) => {
              if (res['messType'] == 'S') {
                this.opportunity_list = res['data'];
              }
            });
          } else {
            this.protfolioChanged = false;
            for (let item of this.portfolio_list) {
              if (item['id'] == res) {
                this.portfolio_name = item['p_name'];
                this.customer = item['customer_name'];
                this.customer_id = item['end_customer_id'];
                this.portfolio_start = item['planned_start_date'];
                this.portfolio_end = item['planned_end_date'];
                this.stepperFormGroup.patchValue({
                  [`customer_details`]: item['customer_name'] || '',
                });
              }
            }
            if (this.child_customer) {
              this.child_customer_list = _.filter(
                this.customer_list,
                (customer) =>
                  parseInt(customer.parent_account) == this.customer_id
              );
              console.log(this.child_customer_list);
              if (this.child_customer_list.length == 0) {
                this.child_customer_list.push({
                  id: this.customer_id,
                  name: this.customer,
                });
                this.stepperFormGroup.patchValue({
                  child_customer: this.customer_id,
                });
              }
            }
            // await this.ProjectCreationService.getBillingAddress(res).then((res)=>{
            //   //console.log(res)
            //       if(res['messType']=='S'){
            //         this.risk=false
            //       }
            //       else{
            //         this.risk=true
            //       }
            // })

            if (this.childOpportunityFlag) {
              await this.ProjectCreationService.getParentEditedOpportunity(
                this.data.project_id,
                this.data.item_id,
                this.customer_id,
                this.opportunity_status_list
              ).then((res) => {
                if (res['messType'] == 'S') {
                  this.opportunity_list = res['data'];
                }
              });
            } else {
              await this.ProjectCreationService.getEditedOpportunity(
                this.data.project_id,
                this.data.item_id,
                this.customer_id,
                this.opportunity_status_list
              ).then((res) => {
                if (res['messType'] == 'S') {
                  this.opportunity_list = res['data'];
                }
              });
            }
          }
        } else {
          this.customer = '-';
          this.portfolio_name = '-';
        }
      });
    this.sowReferenceNumberConfig = _.where(this.formConfig, { field_name: "sow_reference_number", type: "project-creation", is_active: true });
    this.sowRefDuplicatedMsg = this.retrieveMessages[0]?.errors?.sow_ref_duplicated ? this.retrieveMessages[0].errors.sow_ref_duplicated : 'SOW Reference Number already exists';
    this.sowInvalidLengthMsg = this.retrieveMessages[0]?.errors?.sow_invalid_length ? this.retrieveMessages[0].errors.sow_invalid_length : 'SOW Reference Number too short';
    this.stepperFormGroup.get('sow_reference_number').valueChanges.subscribe(async (res) => {
      if (this.stepperFormGroup.get('sow_reference_number').value.length > 0) {
        let sowReferenceNumberMinLength = this.sowReferenceNumberConfig[0]?.min_length ? this.sowReferenceNumberConfig[0]?.min_length : 0;
        this.isSowReferenceNumberRefresh = true;
        this.isSowReferenceNumberEmpty = false;
        if (res && this.stepperFormGroup.get('sow_reference_number').value.length >= sowReferenceNumberMinLength) {
          this.invalidSowReferenceNumberLength = false;
          await this.ProjectCreationService.checkSowReferenceNumberDuplication(res,2,this.data.item_id).then((res: any) => {
            if (res) {
              this.SowReferenceNumberDuplicated = res['data'];
              console.log(this.SowReferenceNumberDuplicated);
              if (this.SowReferenceNumberDuplicated == true) {
                this.sowReferenceNumberValid = false;
                console.log(this.sowReferenceNumberValid);
              }
              else {
                this.sowReferenceNumberValid = true;
              }
              this.isSowReferenceNumberRefresh = false;
              if (res == '' && res == null) {
                this.isSowReferenceNumberRefresh = true;
              }
            }
          })
        }
        if (res && this.stepperFormGroup.get('sow_reference_number').value.length < sowReferenceNumberMinLength) {
          this.sowReferenceNumberValid = false;
          this.invalidSowReferenceNumberLength = true;
        }
        this.isSowReferenceNumberRefresh = false;
      }
      else {
        this.isSowReferenceNumberEmpty = true
        this.sowReferenceNumberValid = false
      }
    })


    const type = _.where(this.formConfig, {
      type: 'edit-project',
      field_name: 'project_type',
      is_active: true,
    });
    this.project_type_disable = type.length > 0 ? type[0].disable : true;
    this.project_type_color = type.length > 0 ? type[0].color : '#E8E9EE';
    const project_code = _.where(this.formConfig, {
      type: 'edit-project',
      field_name: 'project_code',
      is_active: true,
    });
    this.project_code_disable =
      project_code.length > 0 ? project_code[0].disable : true;
    this.project_code_color =
      project_code.length > 0 ? project_code[0].color : '#E8E9EE';
    const project_name = _.where(this.formConfig, {
      type: 'edit-project',
      field_name: 'project_name',
      is_active: true,
    });
    this.project_name_disable =
      project_name.length > 0 ? project_name[0].disable : true;
    this.project_name_color =
      project_name.length > 0 ? project_name[0].color : '#E8E9EE';
    const portfolio = _.where(this.formConfig, {
      type: 'edit-project',
      field_name: 'portfolio',
      is_active: true,
    });
    this.portfolio_disable = portfolio.length > 0 ? portfolio[0].disable : true;
    this.portfolio_color =
      portfolio.length > 0 ? portfolio[0].color : '#E8E9EE';
    const project_start_date = _.where(this.formConfig, {
      type: 'edit-project',
      field_name: 'start_date',
      is_active: true,
    });
    this.project_start_date_disable =
      project_start_date.length > 0 ? project_start_date[0].disable : true;
    this.project_start_date_color =
      project_start_date.length > 0 ? project_start_date[0].color : '#E8E9EE';
    const project_end_date = _.where(this.formConfig, {
      type: 'edit-project',
      field_name: 'end_date',
      is_active: true,
    });
    this.project_end_date_disable =
      project_end_date.length > 0 ? project_end_date[0].disable : true;
    this.project_end_date_color =
      project_end_date.length > 0 ? project_end_date[0].color : '#E8E9EE';
    const entity = _.where(this.formConfig, {
      type: 'edit-project',
      field_name: 'entity',
      is_active: true,
    });
    this.entity_disable = entity.length > 0 ? entity[0].disable : false;
    this.entity_color = entity.length > 0 ? entity[0].color : 'white';
    const division = _.where(this.formConfig, {
      type: 'edit-project',
      field_name: 'division',
      is_active: true,
    });
    this.division_disable = division.length > 0 ? division[0].disable : false;
    this.division_color = division.length > 0 ? division[0].color : 'white';
    const sub_division = _.where(this.formConfig, {
      type: 'edit-project',
      field_name: 'sub_division',
      is_active: true,
    });
    this.sub_division_disable =
      sub_division.length > 0 ? sub_division[0].disable : true;
    this.sub_division_color =
      sub_division.length > 0 ? sub_division[0].color : '#E8E9EE';
    const opportunity = _.where(this.formConfig, {
      type: 'edit-project',
      field_name: 'opportunity',
      is_active: true,
    });
    this.opportunity_disable =
      opportunity.length > 0 ? opportunity[0].disable : true;
    this.opportunity_color =
      opportunity.length > 0 ? opportunity[0].color : '#E8E9EE';
    const po_value = _.where(this.formConfig, {
      type: 'edit-project',
      field_name: 'po_value',
      is_active: true,
    });
    this.po_value_disable = po_value.length > 0 ? po_value[0].disable : true;
    this.po_value_color = po_value.length > 0 ? po_value[0].color : '#E8E9EE';
    const quote = _.where(this.formConfig, {
      type: 'edit-project',
      field_name: 'quote',
      is_active: true,
    });
    this.quote_disable = quote.length > 0 ? quote[0].disable : true;
    this.quote_color = quote.length > 0 ? quote[0].color : '#E8E9EE';
    const po_number = _.where(this.formConfig, {
      type: 'edit-project',
      field_name: 'po_number',
      is_active: true,
    });
    this.po_number_disable = po_number.length > 0 ? po_number[0].disable : true;
    this.po_number_color =
      po_number.length > 0 ? po_number[0].color : '#E8E9EE';
    const invoice_template = _.where(this.formConfig, {
      type: 'edit-project',
      field_name: 'invoice_template',
      is_active: true,
    });
    this.invoice_template_disable =
      invoice_template.length > 0 ? invoice_template[0].disable : true;
    this.invoice_template_color =
      invoice_template.length > 0 ? invoice_template[0].color : '#E8E9EE';
    const currency = _.where(this.formConfig, {
      type: 'edit-project',
      field_name: 'currency',
      is_active: true,
    });
    this.currency_disable = currency.length > 0 ? currency[0].disable : true;
    this.currency_color = currency.length > 0 ? currency[0].color : '#E8E9EE';
    const workLocation = _.where(this.formConfig, {
      type: 'edit-project',
      field_name: 'work_location',
      is_active: true,
    });
    this.work_location_disable =
      workLocation.length > 0 ? workLocation[0].disable : true;
    this.work_location_color =
      workLocation.length > 0 ? workLocation[0].color : '#E8E9EE';
    const location = _.where(this.formConfig, {
      type: 'edit-project',
      field_name: 'location',
      is_active: true,
    });
    this.location_disable = location.length > 0 ? location[0].disable : true;
    this.location_color = location.length > 0 ? location[0].color : '#E8E9EE';
    const holiday_calendar = _.where(this.formConfig, {
      type: 'edit-project',
      field_name: 'holiday_calendar',
      is_active: true,
    });
    this.holiday_calendar_disable =
      holiday_calendar.length > 0 ? holiday_calendar[0].disable : true;
    this.holiday_calendar_color =
      holiday_calendar.length > 0 ? holiday_calendar[0].color : '#E8E9EE';
    const monthly_hours = _.where(this.formConfig, {
      type: 'edit-project',
      field_name: 'monthly_hours',
      is_active: true,
    });
    this.monthly_hours_disable =
      monthly_hours.length > 0 ? monthly_hours[0].disable : true;
    this.monthly_hours_color =
      monthly_hours.length > 0 ? monthly_hours[0].color : '#E8E9EE';
    const profit_center = _.where(this.formConfig, {
      type: 'project-creation',
      field_name: 'profit_center',
      is_active: true,
    });
    this.profit_center_disable =
      profit_center.length > 0 ? profit_center[0].disable : true;
    this.profit_center_color =
      profit_center.length > 0 ? profit_center[0].color : '#E8E9EE';

    this.project_type_list = this.pmMasterService.project_type_list;
    this.cards = this.pmMasterService.service_type_list;
    this.selectedOption = this.data.service_type_id;
    let internal = _.where(this.cards, {
      is_internal: 1,
      id: this.selectedOption,
    });
    if (internal.length > 0) {
      console.log(internal[0]);
      this.inertnal_service_type_id = internal[0].id;
      this.MakeFieldsNonMandatory = false;
    } else {
      this.MakeFieldsNonMandatory = true;
    }
    this.legal_entity = this.data.legal_entity;
    this.stepperFormGroup.patchValue({
      project_code: this.data.profit_center,
      project_name: this.data.item_name,
      portfolio: this.data.project_id,
      startDate: this.data.planned_start_date,
      endDate: this.data.planned_end_date,
      profit_center: this.data.profit_center,
      legal_entity: this.data.legal_entity ? this.data.legal_entity : '',
      sow_reference_number: this.data.sow_reference_number
        ? this.data.sow_reference_number
        : '',
      product_category: this.data.product_category
        ? this.data.product_category
        : '',
      delivery_type: this.data.delivery_type ? this.data.delivery_type : '',
      revenue_type: this.data.revenue_type ? this.data.revenue_type : '',
      customer_id: this.data.customer_id,
    });
    this.currency_list = this.pmMasterService.currency_list;
    this.invoice_template_list = this.pmMasterService.invoice_template_list;
    this.reason_list = this.pmMasterService.reason_list;
    // this.stepperFormGroup.patchValue({['monthly_hours']:168})
    this.work_location_list = this.pmMasterService.work_location_list;
    this.holiday_calendar_list = this.pmMasterService.holiday_calendar_list;
    await this.ProjectCreationService.getTagsProject(this.data.item_id).then(
      (res) => {
        if (res['messType'] == 'S') {
          this.existingTag = res['data'];
        }
      }
    );

    let parentOpportunity = '';

    await this.ProjectCreationService.getProjectFinnacialData(
      this.data.item_id
    ).then((res) => {
      if (res['messType'] == 'S') {
        if (res['data'].length > 0) {
          this.multipleQuote = true;
          for (let i = 0; i < res['data'].length; i++) {
            if (this.checkDisabledBasedOnStatus('opportunity_addition')) {
              res['data'][i]['enable'] = 0;
            } else {
              res['data'][i]['enable'] = 1;
            }

            if (i == 0) {
              parentOpportunity = res['data'][i]['opportunity_id'];
            }
            this.addFinancial();
          }
          const financialArray = this.stepperFormGroup.get(
            'financial'
          ) as FormArray;
          financialArray.patchValue(res['data']);
          this.projectFinnacialData = res['data']
        }
      } else {
        this.multipleQuote = false;
        this.addFinancial();
      } 
    });

    if (this.childOpportunityFlag) {
      await this.ProjectCreationService.getAllChildOpportunity(
        parentOpportunity
      ).then((res) => {
        if (!res['err']) {
          this.child_opportunity_list = res['data'];
        }
      });
    }

    //opportunity and risk toggle config handling
    const opportunityToggle = _.where(this.formConfig, {
      type: 'edit-project',
      field_name: 'opportunity_toggle',
    });
    const riskToggle = _.where(this.formConfig, {
      type: 'edit-project',
      field_name: 'at_risk_toggle',
    });
    //console.log('toggle', opportunityToggle, riskToggle);
    if (opportunityToggle.length > 0) {
      if (opportunityToggle[0].default == 'withoutOpportunity') {
        this.checkWithoutOpportunity();
      } else if (opportunityToggle[0].default == 'withOpportunity') {
        this.checkWithOpportunity();
      }
    }
    if (riskToggle.length > 0) {
      if (riskToggle[0].default == 'standard') {
        this.checkStandard();
      } else if (riskToggle[0].default == 'risk') {
        this.checkAtRisk();
      }
    }
    this.formarray = this.stepperFormGroup.get('financial') as FormArray;

    let esFilter = _.where(this.formConfig, {
      type: 'edit-project',
      field_name: 'es_filter',
      is_active: true,
    });

    if (esFilter.length > 0) {
      await this.ProjectCreationService.getOrgMapping().then(
        async (res: any) => {
          //console.log(res)
          if (res['messType'] == 'S') {
            this.ESdata = res['data'];
            this.entityList = await this.getListValue(
              'entity_name',
              'entity_id'
            );
            this.divisionList = await this.getListValue(
              'division_name',
              'division_id'
            );
            this.subDivisionList = await this.getListValue(
              'sub_division_name',
              'sub_division_id'
            );
          }
        }
      );
      this.displayList = this.ESdata;
      if (this.mode == 'edit' && this.selectedEntity) {
        let rejected_array = _.filter(this.displayList, (res) => {
          //console.log(res, res['entity_id'] != this.selectedEntity['entity_id'] || res['division_id'] != this.selectedEntity['division_id'] || res['sub_division_id'] != this.selectedEntity['sub_division_id'])
          return (
            res['entity_id'] != this.selectedEntity['entity_id'] ||
            res['division_id'] != this.selectedEntity['division_id'] ||
            res['sub_division_id'] != this.selectedEntity['sub_division_id']
          );
        });

        let selected_array = _.filter(this.displayList, (res) => {
          return (
            res['entity_id'] == this.selectedEntity['entity_id'] &&
            res['division_id'] == this.selectedEntity['division_id'] &&
            res['sub_division_id'] == this.selectedEntity['sub_division_id']
          );
        });

        //console.log(rejected_array)
        //console.log(selected_array)
        this.displayList = [...selected_array, ...rejected_array];

        let total_result_array =
          selected_array.length > 0
            ? _.where(this.displayList, {
                entity_id: selected_array[0]['entity_id'],
                division_id: selected_array[0]['division_id'],
                sub_division_id: selected_array[0]['sub_division_id'],
              })
            : [];

        this.selectedData =
          total_result_array.length > 0 ? total_result_array[0] : undefined;
        this.selectedIndex =
          selected_array.length > 0
            ? _.indexOf(this.displayList, this.selectedData)
            : undefined;
        //console.log(this.selectedIndex)
      }

      this.entityList = await this.getListValue('entity_name', 'entity_id');
      this.divisionList = await this.getListValue(
        'division_name',
        'division_id'
      );
      this.subDivisionList = await this.getListValue(
        'sub_division_name',
        'sub_division_id'
      );
      this.stepperFormGroup.patchValue({
        entity: this.data.entity_id,
        division: this.data.division_id,
        sub_division: this.data.sub_division_id,
      });

      this.Filterdata = _.where(this.ESdata, {
        entity_id: this.stepperFormGroup.get('entity').value,
      });

      this.divisionList = await this.getListValueFilter(
        'division_name',
        'division_id'
      );
      this.subDivisionList = await this.getListValueFilter(
        'sub_division_name',
        'sub_division_id'
      );
      this.displaySubDivision = true;
      this.displayDivision = true;
      this.displayEsList = true;
    }
    this.project_type_default = type.length > 0 ? type[0].default : 1;
    if (this.project_type_default > 0) {
      this.stepperFormGroup.patchValue({
        [`project_type`]: this.project_type_default,
      });
    }
    this.detailsSkip = this.mandateAvailable('details');
    this.esSkip = this.mandateAvailable('es');
    this.templateSkip = this.mandateAvailable('template');
    this.peopleSkip = this.mandateAvailable('people');
    this.scheduleSkip = this.mandateAvailable('schedule');
    this.financialSkip = this.mandateAvailable('financial');
    this.detailsLast = this.lastStepper('details');
    this.esLast = this.lastStepper('es');
    this.financialLast = this.lastStepper('financial');
    this.peopleLast = this.lastStepper('people');
    this.scheduleLast = this.lastStepper('schedule');
    this.templateLast = this.lastStepper('template');
    this.tagLast = this.lastStepper('advance');
    if (this.projectFinnacialData.length > 0) {
      for (let i = 0; i < this.projectFinnacialData.length; i++) {
        this.getOpportunityData(
          { id: this.projectFinnacialData[i].opportunity_id },
          i,
          1
        );
      }
    }
    await this.ProjectCreationService.getLocations(
      this.data.project_id,
      this.data.item_id
    ).then((res) => {
      if (res['messType'] == 'S') {
        if (res['data'].length > 0) {
          this.fields = [];
          for (let item of res['data']) {
            this.fields.push({
              workLocation: JSON.parse(item['location_type_id']),
              location: item['location_name'],
              country_id: item['country_id'],
              calendar: item['holiday_calendar_id'],
            });
          }
        }
      }
    });
    await this.ProjectCreationService.getGeneralSettings(
      this.data.project_id,
      this.data.item_id
    ).then((res) => {
      if (res['messType'] == 'S') {
        if (res['data'] && res['data'].length > 0) {
          if (
            res['data'][0].work_schedule != null &&
            res['data'][0].work_schedule != '' &&
            res['data'][0].work_schedule != ' '
          )
            if (res['data'][0].work_schedule.length > 0)
              for (let i = 0; i < res['data'][0].work_schedule.length; i++) {
                if (res['data'][0].work_schedule[i] == 1) {
                  this.monday = true;
                }
                if (res['data'][0].work_schedule[i] == 2) {
                  this.tuesday = true;
                }
                if (res['data'][0].work_schedule[i] == 3) {
                  this.wednesday = true;
                }
                if (res['data'][0].work_schedule[i] == 4) {
                  this.thursday = true;
                }
                if (res['data'][0].work_schedule[i] == 5) {
                  this.friday = true;
                }
                if (res['data'][0].work_schedule[i] == 6) {
                  this.saturday = true;
                }
                if (res['data'][0].work_schedule[i] == 7) {
                  this.sunday = true;
                }
              }
          this.stepperFormGroup.patchValue({
            from: res['data'][0].work_start_time_hours,
            to: res['data'][0].work_end_time_hours,
            monthly_hours: res['data'][0].monthly_hours,
            daily_working_hours: res['data'][0].daily_working_hours,
            leave_paid:
              res['data'][0].leave_paid == null ||
              res['data'][0].leave_paid == 0
                ? '0'
                : '1',
            holiday_paid:
              res['data'][0].holiday_paid == null ||
              res['data'][0].holiday_paid == 0
                ? '0'
                : '1',
          });
        }
      }
    });

    await this.ProjectCreationService.getProjectQuote(
      this.data.project_id,
      this.data.item_id
    ).then((res) => {
      if (res['messType'] == 'S') {
        if (res['data'].length > 0) {
          this.ServiceId = res['data'][0].service_type_id;
          this.item_status=res['data'][0].item_status_id;
          if (res['data'][0].at_risk == 1) {
            this.checkAtRisk();
          }
          if (res['data'][0].opportunity_log != null) {
            const opportunity_edit = JSON.parse(res['data'][0].opportunity_log);
            if (opportunity_edit.length > 0) {
              this.stepperFormGroup.patchValue({
                opportunity:
                  opportunity_edit[opportunity_edit.length - 1].opportunity_id,
              });
            } else {
              this.stepperFormGroup.patchValue({
                opportunity: '',
              });
            }
          }
          this.stepperFormGroup.patchValue({
            currency: res['data'][0].billing_currency,
            quote: res['data'][0].quote_id,
            po_number: res['data'][0].po_number,
            po_value: res['data'][0].po_value,
            p_and_l: res['data'][0].p_and_l_id,
            po_reference: res['data'][0].po_reference,
            po_date:moment(res['data'][0].po_date).format('YYYY-MM-DD'),
            payment_terms: res['data'][0].payment_terms,
            partner: res['data'][0].partner,
            child_customer: res['data'][0].child_customer_id,
          });

          for (let items of this.currency_list) {
            if (items['id'] == res['data'][0].billing_currency) {
              this.code = items['name'];
            }
          }
          if (res['data'][0].at_risk == 0) {
            this.standard = true;
            this.risk = false;
          } else {
            this.risk = true;
            this.standard = false;
          }
          if (res['data'][0].with_opportunity == 0) {
            this.withoutOpportunity = true;
            this.withOpportunity = false;
            this.toggleOpportunityChecked = true;
          } else {
            this.withOpportunity = true;
            this.withoutOpportunity = false;
            this.toggleOpportunityChecked = false;
          }
          if (res['data'][0].is_blanket == 0) {
            this.TandM_selected = false;
          } else {
            this.TandM_selected = true;
          }
          this.external_id = res['data'][0].external_reference_id;
        }
      }
    });
    await this.ProjectCreationService.getProjectFinancialwithoutopp(this.data.project_id, this.data.item_id).then((res) => {
      if (res['messType'] === 'S') {
        if (res['data'].length > 0) {
        const data = res['data'];
        const fieldsArray = this.stepperFormGroup.get('fieldsArray') as FormArray;
        fieldsArray.clear();
        data.forEach((item: any) => {
          const group = this.formBuilder.group({
            po_start_date: [moment(item?.po_startDate, 'DD-MMM-YYYY').format('YYYY-MM-DD')],
            po_end_date: [moment(item?.po_endDate, 'DD-MMM-YYYY').format('YYYY-MM-DD')],
            po_date: [moment(item?.po_Date, 'DD-MMM-YYYY').format('YYYY-MM-DD')],
            po_number: [item?.po_number],
            po_value: [item?.po_value],
            po_reference: [item?.po_ref_no],
            payment_terms: [item?.payment_terms],
            partner: [item?.partner || null],
            invoice_template: [item?.invoice_template || null],
          });
    
          fieldsArray.push(group);
        });
      } }
    });
    if(this.item_status==14 ||this.item_status==5 || this.item_status==12){
      this.save_status=true
    }
    await this.ProjectCreationService.retrieveMilestoneId(
      this.data.item_id
    ).then(async (res: any) => {
      if (res['data'].length > 0) {
        this.milestoneList = res['data'];
      }
    });
    this.loading = false;
    await this.ProjectCreationService.getGroupedMilestone(this.data.item_id).then((res)=>{
      if(res['messType']=='S'){
       this.milestone_grouped_data=res['data']
      }
 })
    await this.ProjectCreationService.getSumOFMilestoneValue(this.data.item_id).then((res)=>{
      if(res['messType']=='S' && res['data'].length>0){
        for(let items of res['data']){
          items['milestone_value']=JSON.parse(items['milestone_value'])
          for(let item of items['milestone_value'] ){
            let check=[]
            check=_.where(this.milestone_grouped_data,{childId:items['id']})
            console.log(check)
            if (item['currency_code'] == this.code && check.length==0) {
              this.totalMilestoneValue = this.totalMilestoneValue + item['value'];
            }
          }
        }
      }
    });
    this.stepperFormGroup
      .get('currency')
      .valueChanges.subscribe(async (res) => {
        if (res) {
          for (let items of this.currency_list) {
            if (items['id'] == res) {
              this.code = items['name'];
              break;
            }
          }
        }
      });
    // await this.ProjectCreationService.getProjectCode().then((res: any) => {
    //   if (res['messType'] == 'S') {
    //     this.project_code_list = res['data']
    //   }
    // })
    for (let i = 0; i < this.displayList.length; i++) {
      if (
        this.displayList[i].entity_id ==
          this.stepperFormGroup.get('entity').value &&
        this.displayList[i].division_id ==
          this.stepperFormGroup.get('division').value &&
        this.displayList[i].sub_division_id ==
          this.stepperFormGroup.get('sub_division').value
      ) {
        this.displayList = _.where(this.ESdata, {
          entity_id: this.displayList[i].entity_id,
          division_id: this.displayList[i].division_id,
          sub_division_id: this.displayList[i].sub_division_id,
        });
        this.selectedIndex = i;
        break;
      }
    }
    this.stepperFormGroup
      .get('entity')
      .valueChanges.subscribe(async (res: any) => {
        if (this.stepperFormGroup.get('entity').value != null) {
          this.displaySubDivision = true;
          this.displayDivision = true;
          this.displayEsList = true;
          if (this.check == false) {
            this.entity =
              this.stepperFormGroup.get('entity').value == null
                ? undefined
                : this.stepperFormGroup.get('entity').value;
            this.division =
              this.stepperFormGroup.get('division').value == null
                ? undefined
                : this.stepperFormGroup.get('division').value;
            this.subDivision =
              this.stepperFormGroup.get('sub_division').value == null
                ? undefined
                : this.stepperFormGroup.get('sub_division').value;
            let val = {};
            //console.log(this.subDivision, this.division, this.entity)
            if (this.subDivision) {
              val['sub_division_id'] = this.subDivision;
            }
            if (this.division) {
              val['division_id'] = this.division;
            }
            if (this.entity) {
              val['entity_id'] = this.entity;
            }
            //console.log(val)
            this.displayList = _.where(this.ESdata, val);
            if (this.displayList.length == 0) {
              //console.log('testing')
              this.stepperFormGroup.patchValue({ ['division']: null });
              this.stepperFormGroup.patchValue({ ['sub_division']: null });
              //console.log(this.stepperFormGroup.get('division').value)
              //console.log(this.stepperFormGroup.get('sub_division').value)
              this.entity =
                this.stepperFormGroup.get('entity').value == null
                  ? undefined
                  : this.stepperFormGroup.get('entity').value;
              this.division =
                this.stepperFormGroup.get('division').value == null
                  ? undefined
                  : this.stepperFormGroup.get('division').value;
              this.subDivision =
                this.stepperFormGroup.get('sub_division').value == null
                  ? undefined
                  : this.stepperFormGroup.get('sub_division').value;
              val = {};
              //console.log(this.subDivision, this.division, this.entity)
              if (this.subDivision) {
                val['sub_division_id'] = this.subDivision;
              }
              if (this.division) {
                val['division_id'] = this.division;
              }
              if (this.entity) {
                val['entity_id'] = this.entity;
              }
              this.displayList = _.where(this.ESdata, val);
            }
            this.Filterdata = _.where(this.ESdata, { entity_id: this.entity });

            this.divisionList = await this.getListValueFilter(
              'division_name',
              'division_id'
            );
            this.subDivisionList = await this.getListValueFilter(
              'sub_division_name',
              'sub_division_id'
            );
            //console.log('testentity')

            //console.log(this.displayList)
            this.selectedData = undefined;
            this.selectedIndex = undefined;

            //console.log(this.displayList)
          }
        } else {
          if (
            this.stepperFormGroup.get('division').value == null &&
            this.stepperFormGroup.get('sub_division').value == null
          ) {
            this.displaySubDivision = false;
            this.displayDivision = false;
            this.displayEsList = false;
          }
        }
      });
    this.stepperFormGroup
      .get('division')
      .valueChanges.subscribe(async (res: any) => {
        if (
          this.check == false &&
          this.stepperFormGroup.get('division').value != null
        ) {
          this.displaySubDivision = true;
          this.displayDivision = true;
          this.displayEsList = true;
          this.entity =
            this.stepperFormGroup.get('entity').value == null
              ? undefined
              : this.stepperFormGroup.get('entity').value;
          this.division =
            this.stepperFormGroup.get('division').value == null
              ? undefined
              : this.stepperFormGroup.get('division').value;
          this.subDivision =
            this.stepperFormGroup.get('sub_division').value == null
              ? undefined
              : this.stepperFormGroup.get('sub_division').value;
          let val = {};
          //console.log(this.subDivision, this.division, this.entity)
          if (this.subDivision) {
            val['sub_division_id'] = this.subDivision;
          }
          if (this.division) {
            val['division_id'] = this.division;
          }
          if (this.entity) {
            val['entity_id'] = this.entity;
          }
          //console.log(val)
          this.displayList = _.where(this.ESdata, val);
          this.Filterdata = _.where(this.ESdata, { entity_id: this.entity });

          this.subDivisionList = await this.getListValueFilter(
            'sub_division_name',
            'sub_division_id'
          );
          this.selectedData = undefined;
          this.selectedIndex = undefined;

          //console.log(this.displayList)
        }
      });
    this.stepperFormGroup
      .get('sub_division')
      .valueChanges.subscribe(async (res: any) => {
        if (
          this.check == false &&
          this.stepperFormGroup.get('sub_division').value != null
        ) {
          this.displaySubDivision = true;
          this.displayDivision = true;
          this.displayEsList = true;
          this.entity =
            this.stepperFormGroup.get('entity').value == null
              ? undefined
              : this.stepperFormGroup.get('entity').value;
          this.division =
            this.stepperFormGroup.get('division').value == null
              ? undefined
              : this.stepperFormGroup.get('division').value;
          this.subDivision =
            this.stepperFormGroup.get('sub_division').value == null
              ? undefined
              : this.stepperFormGroup.get('sub_division').value;
          let val = {};
          //console.log(this.subDivision, this.division, this.entity)
          if (this.subDivision) {
            val['sub_division_id'] = this.subDivision;
          }
          if (this.division) {
            val['division_id'] = this.division;
          }
          if (this.entity) {
            val['entity_id'] = this.entity;
          }
          //console.log(val)
          this.displayList = _.where(this.ESdata, val);
          // if(this.displayList.length==0){
          //   this.stepperFormGroup.patchValue({['division']:null})
          //   this.stepperFormGroup.patchValue({['entity']:null})
          //   this.entity=this.stepperFormGroup.get('entity').value==null ? undefined :this.stepperFormGroup.get('entity').value
          //   this.division=this.stepperFormGroup.get('division').value==null ? undefined :this.stepperFormGroup.get('division').value
          //   this.subDivision=this.stepperFormGroup.get('sub_division').value==null ? undefined :this.stepperFormGroup.get('sub_division').value
          //    val={}
          //   //console.log(this.subDivision, this.division, this.entity)
          //   if(this.subDivision)
          //   {
          //     val['sub_division_id'] = this.subDivision
          //   }
          //   if(this.division)
          //   {
          //     val['division_id'] = this.division
          //   }
          //   if(this.entity)
          //   {
          //     val['entity_id'] = this.entity
          //   }
          //   this.displayList = _.where(this.ESdata, val)
          // }

          this.selectedData = undefined;
          this.selectedIndex = undefined;

          //console.log(this.displayList)
        }
      });

      this.oldFormValue = this.stepperFormGroup.value;
      this.oldData = {
        project_name: this.oldFormValue.project_name ? this.oldFormValue.project_name : null,
        project_code: this.oldFormValue.project_code ? this.oldFormValue.project_code : null,
        portfolio: this.oldFormValue.portfolio ?  this.getNameByIdPipe.transform(this.oldFormValue.portfolio, this.portfolio_list) : null,
        start_date: this.oldFormValue.startDate ? moment(this.oldFormValue.startDate).format('DD-MMM-YYYY') : null,
        end_date: this.oldFormValue.endDate ? moment(this.oldFormValue.endDate).format('DD-MMM-YYYY') : null,
        currency: this.oldFormValue.currency ? this.getNameByIdPipe.transform(this.oldFormValue.currency, this.currency_list) : null,
        project_type: this.oldFormValue.project_type ? this.getNameByIdPipe.transform(this.oldFormValue.project_type, this.project_type_list) : null,
        entity: this.oldFormValue.entity ? this.getNameByIdPipe.transform(this.oldFormValue.entity, this.entityList) : null,
        division: this.oldFormValue.division ? this.getNameByIdPipe.transform(this.oldFormValue.division, this.divisionList) : null,
        sub_division: this.oldFormValue.sub_division ? this.getNameByIdPipe.transform(this.oldFormValue.sub_division, this.subDivisionList) : null,
        profit_center: this.oldFormValue.profit_center ? this.oldFormValue.profit_center : null,
        p_and_l: this.oldFormValue.p_and_l ? this.getNameByIdPipe.transform(this.oldFormValue.p_and_l, this.p_and_l_list) : null,
        legal_entity: this.oldFormValue.legal_entity ? this.getNameByIdPipe.transform(this.oldFormValue.legal_entity, this.legal_entity_list) : null,
        sow_reference_number: this.oldFormValue.sow_reference_number ? this.oldFormValue.sow_reference_number : null,
        product_category: this.oldFormValue.product_category ? this.getNameByIdPipe.transform(this.oldFormValue.product_category, this.product_category_list) : null,
        revenue_type: this.oldFormValue.revenue_type ? this.getNameByIdPipe.transform(this.oldFormValue.revenue_type, this.revenue_type_list) : null,
        delivery_type: this.oldFormValue.delivery_type ? this.getNameByIdPipe.transform(this.oldFormValue.delivery_type, this.delivery_type_list) : null,
        opportunity_toggle : this.withOpportunity ? 'Q2C' : 'O2C',
        at_risk_toggle : this.risk ? 'At Risk' : 'Secured',
        po_number: this.oldFormValue.po_number ? this.oldFormValue.po_number : null,
        po_value: this.oldFormValue.po_value ? this.oldFormValue.po_value : null,
        po_reference: this.oldFormValue.po_reference ? this.oldFormValue.po_reference : null,
        po_date: this.oldFormValue.po_date ? moment(this.oldFormValue.po_date).format('DD-MMM-YYYY') : null, 
        partner: this.oldFormValue.partner ? this.getNameByIdPipe.transform(this.oldFormValue.partner, this.customer_list) : null,
        payment_terms: this.oldFormValue.payment_terms ? this.getNameByIdPipe.transform(this.oldFormValue.payment_terms, this.payment_list) : null,
        work_day_monday: this.monday ? 'Yes' : 'No',
        work_day_tuesday: this.tuesday ? 'Yes' : 'No',
        work_day_wednesday: this.wednesday ? 'Yes': 'No',
        work_day_thursday: this.thursday ? 'Yes': 'No',
        work_day_friday: this.friday? 'Yes' : 'No',
        work_day_saturday : this.saturday ? 'Yes' : 'No',
        work_day_sunday: this.sunday ? 'Yes' : 'No',
        from: this.oldFormValue.from ?  this.oldFormValue.from : null,
        to: this.oldFormValue.to ?  this.oldFormValue.to : null,
        daily_working_hours: this.oldFormValue.daily_working_hours ? this.oldFormValue.daily_working_hours : null,
        monthly_hours: this.oldFormValue.monthly_hours ? this.oldFormValue.monthly_hours : null,
        leave_paid: this.oldFormValue.leave_paid ? this.getNameByIdPipe.transform(this.oldFormValue.leave_paid, this.yes_or_no_list) : null,
        holiday_paid: this.oldFormValue.leave_paid ? this.getNameByIdPipe.transform(this.oldFormValue.holiday_paid, this.yes_or_no_list) : null,
        work_location: this.fields[0].workLocation ? this.getNameByIdPipe.transform(this.fields[0].workLocation, this.work_location_list) : null,
        location: this.fields[0].country_id ? this.getNameByIdPipe.transform(this.fields[0].country_id, this.country_list) : null,
        holiday_calendar:this.fields[0].calendar ? this.getNameByIdPipe.transform(this.fields[0].calendar, this.holiday_calendar_list) : null,
        service_type: this.selectedOption ? this.getNameByIdPipe.transform(this.selectedOption, this.cards) : null,
        tags: this.tags && this.tags.length > 0 ? _.map(this.tags, 'name') : null
    }
    if(this.withOpportunity){
      this.oldFinancialData = this.withOpportunity ? this.oldFormValue.financial : null;
      for(let item of this.oldFinancialData){
        item.reason = item.reason ? this.getNameByIdPipe.transform(item.reason, this.reason_list) : null;
        item.payment_terms =  item.payment_terms ? this.getNameByIdPipe.transform(item.payment_terms, this.payment_list) : null;
        item.po_date = item.po_date ?  moment(item.po_date).format('DD-MMM-YYYY') : null;
      }
    }
  }
  patchForm(data: any) {
    const fieldsArray = this.stepperFormGroup.get('fieldsArray') as FormArray;
    fieldsArray.clear(); // Clear existing fields

    // Create a new FormGroup for the data and add it to the FormArray
    const group = this.formBuilder.group({
      po_start_date: [data.po_startDate],
      po_end_date: [data.po_endDate],
      po_date: [data.po_Date],
      po_number: [data.po_number],
      po_value: [data.currencyListJSON],
      po_reference: [data.po_ref_no],
      payment_terms: [data.payment_terms],
      partner: [data.partner || null], // assuming you may have a partner field
      invoice_template: [data.invoice_template || null], // assuming you may have an invoice template field
    });

    fieldsArray.push(group);
  }
  checkInternal() {
    throw new Error('Method not implemented.');
  }
  // checkInternalServiceType() {
  //   let internal = _.where(this.cards, { is_internal: 1,id:this.selectedOption });

  //   // let external = _.where(this.cards, { is_internal: 0, id: this.selectedOption });
  //   // console.log(external)

  //   if (internal.length > 0) {

  //     this.MakeFieldsNonMandatory = false;
  //     // this.disableOptions = false;  // Disable all options
  //   } else {
  //     this.MakeFieldsNonMandatory = true;
  //     // this.disableOptions = false;  // Enable all options
  //   }
  //   // if (external.length > 0) {
  //   //   console.log(external)
  //   //   this.disableInternalOption = true;
  //   // } else {
  //   //   this.disableInternalOption = false;
  //   // }
  // }

  onStepperChange(stepperData: any) {
    this.stepperData = stepperData;
    //console.log('changed stepper data', this.stepperData);
    this.stepperControl();
  }
  getNextStepper() {
    //console.log("next called", this.stepperData)
    const index = this.stepperData.findIndex(
      (item) => item.is_selected === true
    );
    //console.log(index, this.stepperData.length, this.stepperData.length - 2);
    const man = this.checkDetailsMandatory(this.stepperData[index].type);
    if (man) {
      if (index !== -1 && index <= this.stepperData.length - 2) {
        this.stepperData[index].is_selected = false;
        this.stepperData[index].is_crossed = true;
        this.stepperData[index + 1].is_selected = true;
        this.stepperData[index + 1].is_completed = false;

        //console.log(this.stepperData);
      }

      this.stepperControl();
    }
  }
  getPreviousStepper() {
    //console.log("previous called", this.stepperData)
    const index = this.stepperData.findIndex(
      (item) => item.is_selected === true
    );
    //console.log(index, this.stepperData.length, this.stepperData.length - 1);
    if (index !== -1 && index > 0) {
      // this.stepperData[index].is_selected = false;
      this.stepperData[index - 1].is_selected = true;
      this.stepperData[index - 1].is_completed = false;
      this.stepperData[index].is_completed = true;
      // this.stepperData[index-1].is_crossed = false;
      //console.log(this.stepperData);
    }
    this.stepperControl();
  }
  stepperControl() {
    const index = this.stepperData.findIndex(
      (item) => item.is_selected === true
    );
    if (index !== -1) {
      const selectedStepper = this.stepperData[index].type;
      switch (selectedStepper) {
        case 'details':
          this.detailStepper = true;
          this.enterpriseStepper = false;
          this.financialStepper = false;
          this.peopleStepper = false;
          this.templateStepper = false;
          this.scheduleStepper = false;
          this.advanceStepper = false;
          break;

        case 'es':
          this.detailStepper = false;
          this.enterpriseStepper = true;
          this.financialStepper = false;
          this.peopleStepper = false;
          this.templateStepper = false;
          this.scheduleStepper = false;
          this.advanceStepper = false;
          break;

        case 'financial':
          this.detailStepper = false;
          this.enterpriseStepper = false;
          this.financialStepper = true;
          this.peopleStepper = false;
          this.templateStepper = false;
          this.scheduleStepper = false;
          this.advanceStepper = false;
          break;

        case 'schedule':
          this.detailStepper = false;
          this.enterpriseStepper = false;
          this.financialStepper = false;
          this.peopleStepper = false;
          this.templateStepper = false;
          this.scheduleStepper = true;
          this.advanceStepper = false;
          break;

        case 'advance':
          this.detailStepper = false;
          this.enterpriseStepper = false;
          this.financialStepper = false;
          this.peopleStepper = false;
          this.templateStepper = false;
          this.scheduleStepper = false;
          this.advanceStepper = true;
          break;

        default:
          this.detailStepper = true;
          this.enterpriseStepper = false;
          this.financialStepper = false;
          this.peopleStepper = false;
          this.templateStepper = false;
          this.scheduleStepper = false;
          this.advanceStepper = false;
      }
    }
  }
  checkDetailsMandatory(type: any) {
    console.log("Type in Check", type)
    const data = this.stepperFormGroup.value
    const financial_data_without_opportunity=this.stepperFormGroup.get('fieldsArray').value
    const financial_data = this.stepperFormGroup.get('financial').value
    //const details = _.where(this.stepperData, { type: "details", is_active: true });
    let errorOccurred = false;
    let validateError = false
    if (type == 'details') {
      if ((!data.project_code || data.project_code.trim() === '') && this.isMandate('project_code')) {
        // const codeEmptymsg = 'Kindly enter Milestone name';
        const codeEmptymsg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.code_empty_msg ? this.retrieveMessages[0].errors.code_empty_msg : 'Project Code is Mandatory' : 'Project Code is Mandatory';
        this.toasterService.showWarning(codeEmptymsg, 10000)
        // this.toastr.error(codeEmptymsg, 'Error');
        errorOccurred = true;
      }
      else if ((!data.project_name || data.project_name.trim() === '') && this.isMandate('project_name')) {
        // const codeEmptymsg = 'Kindly enter Milestone name';
        const name_empty_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.name_empty_msg ? this.retrieveMessages[0].errors.name_empty_msg : 'Project Name is Mandatory' : 'Project Name is Mandatory';
        this.toasterService.showWarning(name_empty_msg, 10000)
        // this.toastr.error(codeEmptymsg, 'Error');
        errorOccurred = true;
      }
      else if ((data.portfolio === null || data.portfolio === undefined || data.portfolio === '') && this.isMandate('portfolio')) {
        // const customerEmptymsg = 'Responsible Is Mandatory';
        const portfolio_empty_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.portfolio_empty_msg ? this.retrieveMessages[0].errors.portfolio_empty_msg : 'Portfolio is Mandatory' : 'Portfolio is Mandatory';
        this.toasterService.showWarning(portfolio_empty_msg, 10000)
        // this.toastr.error(customerEmptymsg, 'Error');
        errorOccurred = true;
      }
      
      else if(!(this.stepperFormGroup.get('startDate').valid))
      {
        const startDate_empty_msg ='Kindly enter valid start date';
        this.toasterService.showWarning(startDate_empty_msg, 10000)
        this.dateLogic = true;
        errorOccurred = true;
      }
      else if ((data.startDate === null || data.startDate === undefined || data.startDate === '') && this.isMandate('start_date')) {
        // const nameEmptymsg = 'Kindly choose When Is It due?';
        if (data.startDate === '') {
          const startDate_empty_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.startDate_empty_msg ? this.retrieveMessages[0].errors.startDate_empty_msg : 'Start date is Mandatory' : 'Start date is Mandatory';
          this.toasterService.showWarning(startDate_empty_msg, 10000)
        }
        else if (data.startDate === null) {
          const startDate_invalid_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.startDate_invalid_msg ? this.retrieveMessages[0].errors.startDate_invalid_msg : 'Kindly Choose/Enter start date in DD-MM-YYYY format' : 'Kindly Choose/Enter start date in DD-MM-YYYY format';
          this.toasterService.showWarning(startDate_invalid_msg, 10000)
        }
        // this.toastr.error(nameEmptymsg, 'Error');
        this.dateLogic = true;
        errorOccurred = true;
      }
      else if(!(this.stepperFormGroup.get('endDate').valid))
      {
        const endDate_empty_msg ='Kindly enter valid end date';
        this.toasterService.showWarning(endDate_empty_msg, 10000)
        this.dateLogic = true;
        errorOccurred = true;
      }
      else if ((data.endDate === null || data.endDate === undefined || data.endDate === '') && this.isMandate('end_date')) {
        // const nameEmptymsg = 'Kindly choose When Is It due?';
        // this.toastr.error(nameEmptymsg, 'Error');
        if (data.endDate === '') {
          const endDate_empty_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.startDate_empty_msg ? this.retrieveMessages[0].errors.startDate_empty_msg : 'End date is Mandatory' : 'End date is Mandatory';
          this.toasterService.showWarning(endDate_empty_msg, 10000)
        }
        else if (data.endDate === null) {
          const endDate_invalid_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.endDate_invalid_msg ? this.retrieveMessages[0].errors.endDate_invalid_msg : 'Kindly Choose/Enter end date in DD-MM-YYYY format' : 'Kindly Choose/Enter end date in DD-MM-YYYY format';
          this.toasterService.showWarning(endDate_invalid_msg, 10000)
        }
        this.dateLogic = true;
        errorOccurred = true;

      }
      this.checkDates(data.startDate, data.endDate);
      if (!this.dateLogic && !errorOccurred) {
        errorOccurred = true;
        const valid_date_err = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.valid_date_err ? this.retrieveMessages[0].errors.valid_date_err : 'Kindly enter valid date' : 'Kindly enter valid date';
        this.toasterService.showWarning(valid_date_err, 10000)
      }
      else if ((data.project_type === null || data.project_type === undefined || data.project_type === '') && this.isMandate('project_type') && !errorOccurred) {
        // const nameEmptymsg = 'Kindly choose When Is It due?';
        const projectType_empty_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.projectType_empty_msg ? this.retrieveMessages[0].errors.projectType_empty_msg : 'Project Type is Mandatory' : 'Project Type is Mandatory';
        this.toasterService.showWarning(projectType_empty_msg, 10000)
        // this.toastr.error(nameEmptymsg, 'Error');
        errorOccurred = true;
      }
      else if ((data.currency === null || data.currency === undefined || data.currency === '') && this.isMandate('currency')) {
        // const customerEmptymsg = 'Responsible Is Mandatory';
        // this.toastr.error(customerEmptymsg, 'Error');
        const currency_mandate_err = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.currency_mandate_err ? this.retrieveMessages[0].errors.currency_mandate_err : 'Currency is mandatory' : 'Currency is mandatory';
        errorOccurred = true;
        this.toasterService.showWarning(currency_mandate_err, 10000)
      }
      else if ((this.selectedOption === null || this.selectedOption === undefined || this.selectedOption === '') && this.isMandate('service_type') && !errorOccurred) {
        // const nameEmptymsg = 'Kindly choose When Is It due?';
        const serviceType_empty_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.serviceType_empty_msg ? this.retrieveMessages[0].errors.serviceType_empty_msg : 'Service Type is Mandatory' : 'Service Type is Mandatory';
        this.toasterService.showWarning(serviceType_empty_msg, 10000)
        // this.toastr.error(nameEmptymsg, 'Error');
        errorOccurred = true;
      }
      else if ((!data.description || data.description.trim() === '') && this.isMandate('project_description') && !errorOccurred) {
        // const codeEmptymsg = 'Kindly enter Milestone name';
        const description_empty_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.description_empty_msg ? this.retrieveMessages[0].errors.description_empty_msg : 'Description is Mandatory' : 'Description is Mandatory';
        this.toasterService.showWarning(description_empty_msg, 10000)
        // this.toastr.error(codeEmptymsg, 'Error');
        errorOccurred = true;
      }
      else if (this.valid == false) {
        validateError = true
      }
      else if (!errorOccurred && !validateError) {
        for (let items of this.stepperData) {
          if (items['type'] == 'details') {
            items['is_completed'] = true
          }
          return true
        }
      }
      else if (validateError && !errorOccurred) {
        const code_invalid_err = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.code_invalid_err ? this.retrieveMessages[0].errors.code_invalid_err : 'Invalid Project Code! Kindly check and try again' : 'Invalid Project Code! Kindly check and try again';
        this.toasterService.showWarning(code_invalid_err, 10000)
      }
      else if (!this.dateLogic) {
        const isa_dateLogic = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.isa_dateLogic ? this.retrieveMessages[0].errors.isa_dateLogic : 'Kindly Enter Valid Date!' : 'Kindly Enter Valid Date!';
        this.toasterService.showWarning(isa_dateLogic, 10000)
      }
      else if (errorOccurred) {
        //this.toastr.warning("Kindly Enter All Mandatory fields", 'Warning');
        return false
      }


    }
    if (type == 'es') {
      if ((data.entity === null || data.entity === undefined || data.entity === '') && this.isMandate('entity')) {
        // const customerEmptymsg = 'Responsible Is Mandatory';
        const entity_empty = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.entity_empty ? this.retrieveMessages[0].errors.entity_empty : 'Entity is mandatory' : 'Entity is mandatory';

        this.toasterService.showWarning(entity_empty, 10000)
        // this.toastr.error(customerEmptymsg, 'Error');
        errorOccurred = true;
      }
      else if ((data.division === null || data.division === undefined || data.division === '') && this.isMandate('division')) {
        // const customerEmptymsg = 'Responsible Is Mandatory';
        const division_empty = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.division_empty ? this.retrieveMessages[0].errors.division_empty : 'Division is mandatory' : 'Division is mandatory';

        this.toasterService.showWarning(division_empty, 10000)
        // this.toastr.error(customerEmptymsg, 'Error');
        errorOccurred = true;
      }
      else if ((data.sub_division === null || data.sub_division === undefined || data.sub_division === '') && this.isMandate('sub_division')) {
        // const customerEmptymsg = 'Responsible Is Mandatory';
        const subdivision_empty = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.subdivision_empty ? this.retrieveMessages[0].errors.subdivision_empty : 'Sub Division is mandatory' : 'Sub Division is mandatory';

        this.toasterService.showWarning(subdivision_empty, 10000)
        // this.toastr.error(customerEmptymsg, 'Error');
        errorOccurred = true;
      }
      else if ((data.child_customer === null || data.child_customer === undefined || data.child_customer === '') && this.isMandate('child_customer')) {
        // const customerEmptymsg = 'Responsible Is Mandatory';
        const subdivision_empty ='Child Customer is mandatory';

        this.toasterService.showWarning(subdivision_empty, 10000)
        // this.toastr.error(customerEmptymsg, 'Error');
        errorOccurred = true;
      }
      else if ((data.p_and_l === null || data.p_and_l === undefined || data.p_and_l === '') && this.isMandate('p_and_l')) {
        // const customerEmptymsg = 'Responsible Is Mandatory';
        const profitCenter_mandate_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.profitCenter_mandate_msg ? this.retrieveMessages[0].errors.profitCenter_mandate_msg : 'Profit Center is Mandatory' : 'Profit Center is Mandatory';
        this.toasterService.showWarning(profitCenter_mandate_msg, 10000)
        // this.toastr.error(customerEmptymsg, 'Error');
        errorOccurred = true;
      }
      else if ((data.legal_entity === null || data.legal_entity === undefined || data.legal_entity === '') && this.isMandate('legal_entity')) {
        // const customerEmptymsg = 'Responsible Is Mandatory';
        const legalEntity_mandate_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.legalEntity_mandate_msg ? this.retrieveMessages[0].errors.legalEntity_mandate_msg : 'SOW Owner is Mandatory' : 'SOW Owner is Mandatory';
        this.toasterService.showWarning(legalEntity_mandate_msg, 10000)
        // this.toastr.error(customerEmptymsg, 'Error');
        errorOccurred = true;
      }
      else if ((data.sow_reference_number === null || data.sow_reference_number === undefined || data.sow_reference_number === '') ) {
        if(this.isMandate('sow_reference_number')){
          // const customerEmptymsg = 'Responsible Is Mandatory';
          const sowRef_mandate_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.sowRef_mandate_msg ? this.retrieveMessages[0].errors.sowRef_mandate_msg : 'SOW Reference Number is Mandatory' : 'SOW Reference Number is Mandatory';
          this.toasterService.showWarning(sowRef_mandate_msg, 10000);
          errorOccurred = true;
        }
        // this.toastr.error(customerEmptymsg, 'Error');
      }
      else if (data.sow_reference_number){
        if(this.isSowReferenceNumberRefresh){
          this.toasterService.showWarning("Please wait while SOW reference number is being verified.", 10000);
          errorOccurred = true;
        }
        if(!this.sowReferenceNumberValid && this.invalidSowReferenceNumberLength){
          const sow_invalid_length_msg = this.retrieveMessages[0]?.errors?.sow_invalid_length ? this.retrieveMessages[0].errors.sow_invalid_length : 'SOW Reference Number too short';
          this.toasterService.showWarning(sow_invalid_length_msg, 10000);
          errorOccurred = true;
        }
        if(!this.sowReferenceNumberValid && this.SowReferenceNumberDuplicated){
          const sow_ref_duplicated_msg = this.retrieveMessages[0]?.errors?.sow_ref_duplicated ? this.retrieveMessages[0].errors.sow_ref_duplicated : 'SOW Reference Number already exists';
          this.toasterService.showWarning(sow_ref_duplicated_msg, 10000);
          errorOccurred = true;
        }
      }
      else if ((data.product_category === null || data.product_category === undefined || data.product_category === '') && this.isMandate('product_category')) {
        // const customerEmptymsg = 'Responsible Is Mandatory';
        const product_category_mandate_msg = this.retrieveMessages.length ? this.retrieveMessages[0].errors.product_category_mandate_msg ? this.retrieveMessages[0].errors.product_category_mandate_msg : 'Product Category is Mandatory' : 'Product Category is Mandatory';
        this.toasterService.showWarning(product_category_mandate_msg, 10000)
        // this.toastr.error(customerEmptymsg, 'Error');
        errorOccurred = true;
      }
      else if ((data.delivery_type === null || data.delivery_type === undefined || data.delivery_type === '') && this.isMandate('delivery_type')) {
        // const customerEmptymsg = 'Responsible Is Mandatory';
        const delivery_type_mandate_msg = this.retrieveMessages.length ? this.retrieveMessages[0].errors.delivery_type_mandate_msg ? this.retrieveMessages[0].errors.delivery_type_mandate_msg : 'Delivery Type is Mandatory' : 'Delivery Type is Mandatory';
        this.toasterService.showWarning(delivery_type_mandate_msg, 10000)
        // this.toastr.error(customerEmptymsg, 'Error');
        errorOccurred = true;
      }
      else if ((data.revenue_type === null || data.revenue_type === undefined || data.revenue_type === '') && this.isMandate('revenue_type')) {
        // const customerEmptymsg = 'Responsible Is Mandatory';
        const revenue_type_mandate_msg = this.retrieveMessages.length ? this.retrieveMessages[0].errors.revenue_type_mandate_msg ? this.retrieveMessages[0].errors.revenue_type_mandate_msg : 'Revenue Type is Mandatory' : 'Revenue Type is Mandatory';
        this.toasterService.showWarning(revenue_type_mandate_msg, 10000)
        // this.toastr.error(customerEmptymsg, 'Error');
        errorOccurred = true;
      }
      if (!errorOccurred) {
        for (let items of this.stepperData) {
          if (items['type'] == 'es') {
            items['is_completed'] = true
          }
        }
        return true
      }
      else if (errorOccurred) {
        //this.toastr.warning("Kindly Enter All Mandatory fields", 'Warning');
        return false
      }
    }
    if (type == 'financial') {

      if (this.withOpportunity) {
        let total_po_value = 0;
        let i=0;
        for (let item of financial_data) {
          if (item['is_active'] == 1) {
            if ((item['opportunity_id'] === null || item['opportunity_id'] === undefined || item['opportunity_id'] === '') && this.isMandate('opportunity') && this.MakeFieldsNonMandatory) {
              const opportunity_empty = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.opportunity_empty ? this.retrieveMessages[0].errors.opportunity_empty : 'Opportunity is mandatory' : 'Opportunity is mandatory';

              this.toasterService.showWarning(opportunity_empty, 10000)
              // const customerEmptymsg = 'Responsible Is Mandatory';
              // this.toastr.error(customerEmptymsg, 'Error');
              errorOccurred = true;
              break;
            }
            else if ((item['reason'] === null || item['reason'] === undefined || item['reason'] === '') && this.isMandate('reason')) {
              // const customerEmptymsg = 'Responsible Is Mandatory';
              // this.toastr.error(customerEmptymsg, 'Error');
              const reason_mandate_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.reason_mandate_msg ? this.retrieveMessages[0].errors.reason_mandate_msg : 'Reason is Mandatory' : 'Reason is Mandatory';
              errorOccurred = true;
              this.toasterService.showWarning(reason_mandate_msg, 10000)
              break;
            }
            else if ((item['quote_id'] === null || item['quote_id'] === undefined || item['quote_id'] === '') && this.isMandate('quote')) {
              // const codeEmptymsg = 'Kindly enter Milestone name';
              const quote_empty =
                this.retrieveMessages.length > 0
                  ? this.retrieveMessages[0].errors.quote_empty
                    ? this.retrieveMessages[0].errors.quote_empty
                    : 'Quote is mandatory'
                  : 'Quote is mandatory';
              this.toasterService.showWarning(quote_empty, 10000);
              // this.toastr.error(codeEmptymsg, 'Error');
              errorOccurred = true;
              break;
            } else if (
              this.MakeFieldsNonMandatory && i==0 &&
              (!item['po_number'] || item['po_number'].trim() === '') &&
              this.isMandate('po_number_with_opportunity') &&
              !this.poNonMandate
            ) {
              // const codeEmptymsg = 'Kindly enter Milestone name';
              // this.toastr.error(codeEmptymsg, 'Error');
              const quote_empty =
              this.retrieveMessages.length > 0
                ? this.retrieveMessages[0].errors.quote_empty
                  ? this.retrieveMessages[0].errors.quote_empty
                  : 'Quote is mandatory'
                : 'Quote is mandatory';
              this.toasterService.showWarning(quote_empty, 10000)
              // this.toastr.error(codeEmptymsg, 'Error');
              errorOccurred = true;
              break;
            }
            else if ((!item['po_number'] || item['po_number'].trim() === '') && this.isMandate('po_number_with_opportunity') && !this.poNonMandate && this.MakeFieldsNonMandatory) {
              // const codeEmptymsg = 'Kindly enter Milestone name';
              // this.toastr.error(codeEmptymsg, 'Error');
              const poNumber_mandate_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.poNumber_mandate_msg ? this.retrieveMessages[0].errors.poNumber_mandate_msg : 'PO Number is mandatory' : 'PO Number is mandatory';
              this.toasterService.showWarning(poNumber_mandate_msg, 10000)
              errorOccurred = true;
              break;
            }
            else if ((item['purchase_order'] === null || item['purchase_order'] === undefined || item['purchase_order'] === '') && this.isMandate('po_value_with_opportunity') && this.MakeFieldsNonMandatory) {
              // const codeEmptymsg = 'Kindly enter Milestone name';
              // this.toastr.error(codeEmptymsg, 'Error');
              const orderValue_mandate_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.orderValue_mandate_msg ? this.retrieveMessages[0].errors.orderValue_mandate_msg : 'Order value is mandatory' : 'Order value is mandatory';
              this.toasterService.showWarning(orderValue_mandate_msg, 10000)
              errorOccurred = true;
              break;
            }
            else if ((item['purchase_order'] == 0) && this.isMandate('po_value_with_opportunity') && this.MakeFieldsNonMandatory) {
              // const codeEmptymsg = 'Kindly enter Milestone name';
              // this.toastr.rror(codeEmptymsg, 'Error');
              const orderValue_mandate_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.orderValue_mandate_msg ? this.retrieveMessages[0].errors.orderValue_mandate_msg : 'Order value is mandatory' : 'Order value is mandatory';
              this.toasterService.showWarning(orderValue_mandate_msg, 10000)
              errorOccurred = true;
              break;
            }
            else if ((item['payment_terms'] === null || item['payment_terms'] === undefined || item['payment_terms'] === '') && this.isMandate('payment_terms')) {
              // const codeEmptymsg = 'Kindly enter Milestone name';
              const paymentTerms_empty = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.paymentTerms_empty ? this.retrieveMessages[0].errors.paymentTerms_empty : 'Payment Term is mandatory' : 'Payment Term is mandatory';

              this.toasterService.showWarning(paymentTerms_empty, 10000)
              // this.toastr.error(codeEmptymsg, 'Error');
              errorOccurred = true;
              break;
            }
            else if ((item['partner'] === null || item['partner'] === undefined || item['partner'] === '') && this.isMandate('partner')) {
              // const codeEmptymsg = 'Kindly enter Milestone name';
              // this.toastr.error(codeEmptymsg, 'Error');
              const partner_empty = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.partner_empty ? this.retrieveMessages[0].errors.partner_empty : 'Partner is mandatory' : 'Partner is mandatory';
              this.toasterService.showWarning(partner_empty, 10000)
              errorOccurred = true;
              break;
            }
            else if ((item['po_date'] === null || item['po_date'] === undefined || item['po_date'] === '') && this.isMandate('po_date')) {
              // const codeEmptymsg = 'Kindly enter Milestone name';
              // this.toastr.error(codeEmptymsg, 'Error');
              const poDate_empty = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.poDate_empty ? this.retrieveMessages[0].errors.poDate_empty : 'PO Date value is mandatory' : 'PO Date is mandatory';
              this.toasterService.showWarning(poDate_empty, 10000)

              errorOccurred = true;
              break;
            }
            else if ((item['po_reference'] == 0) && this.isMandate('po_reference')) {
              // const codeEmptymsg = 'Kindly enter Milestone name';
              // this.toastr.rror(codeEmptymsg, 'Error');
              const poReference_empty = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.poReference_empty ? this.retrieveMessages[0].errors.poReference_empty : 'PO Reference Cannot be zero' : 'PO Reference value Cannot be zero';
              this.toasterService.showWarning(poReference_empty, 10000)

              errorOccurred = true;
              break;
            }
          }
          i++;
        }
        if (!errorOccurred) {
          let isa_active_oppotunity = _.where(financial_data, { is_active: 1 })
          if (isa_active_oppotunity.length > 1) {
            for (let item of financial_data) {
              let check_duplicate
              check_duplicate = _.where(financial_data, { opportunity_id: item['opportunity_id'] })
              if (check_duplicate.length > 1) {
                const opportunityDuplicate_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.opportunityDuplicate_msg ? this.retrieveMessages[0].errors.opportunityDuplicate_msg : 'Duplicate Opportunity will not be allowed' : 'Duplicate Opportunity will not be allowed';

                this.toasterService.showWarning(opportunityDuplicate_msg, 10000)
                errorOccurred = true;
                break;
              }
            }
          }
        }
        if (!errorOccurred) {
          for (let items of this.stepperData) {
            if (items['type'] == 'financial') {
              items['is_completed'] = true
              return true
            }
          }
        }
      } 
      else {
        for (let item of financial_data_without_opportunity ){
          if ((item['invoice_template'] === null ||item['invoice_template']  === undefined || item['invoice_template']  === '') && this.isMandate('invoice_template')) {
            // const customerEmptymsg = 'Responsible Is Mandatory';
            // this.toastr.error(customerEmptymsg, 'Error');
            errorOccurred = true;
            console.log("IV")
          }
          if ((!item['po_number'] || item['po_number'].trim() === '') && !this.poNonMandate && this.MakeFieldsNonMandatory) {
            // const codeEmptymsg = 'Kindly enter Milestone name';
            // this.toastr.error(codeEmptymsg, 'Error');
            errorOccurred = true;
            console.log("pon")
          }
          if ((item['po_value'] === null || item['po_value'] === undefined || item['po_value'] === '') && this.isMandate('po_value') && this.MakeFieldsNonMandatory) {
            // const codeEmptymsg = 'Kindly enter Milestone name';
            // this.toastr.error(codeEmptymsg, 'Error');
            errorOccurred = true;
            console.log("pov")
          }

        }
        if (!errorOccurred) {
          for (let items of this.stepperData) {
            if (items['type'] == 'financial') {
              items['is_completed'] = true
              return true
            }
          }
        }
        else if (errorOccurred) {
          //this.toastr.warning("Kindly Enter All Mandatory fields", 'Warning');
          return false
        }
      }
    }
    if (type == 'schedule') {
      if (this.week_array.length == 0 && this.isMandate('week')) {
        errorOccurred = true;
      }
      for (let i = 0; i < this.fields.length; i++) {
        if ((this.fields[i].workLocation === null || this.fields[i].workLocation === undefined || this.fields[i].workLocation === '') && this.isMandate('work_location')) {
          // const customerEmptymsg = 'Responsible Is Mandatory';
          // this.toastr.error(customerEmptymsg, 'Error');
          errorOccurred = true;
        }
        if ((this.fields[i].calendar === null || this.fields[i].calendar === undefined || this.fields[i].calendar === '') && this.isMandate('holiday_calendar')) {
          // const customerEmptymsg = 'Responsible Is Mandatory';
          // this.toastr.error(customerEmptymsg, 'Error');
          errorOccurred = true;
        }
        if ((!this.fields[i].location || this.fields[i].location.trim() === '') && this.isMandate('location')) {
          // const codeEmptymsg = 'Kindly enter Milestone name';
          // this.toastr.error(codeEmptymsg, 'Error');
          errorOccurred = true;
        }

      }
      if ((!data.from || data.from.trim() === '') && this.isMandate('from')) {
        // const codeEmptymsg = 'Kindly enter Milestone name';
        // this.toastr.error(codeEmptymsg, 'Error');
        errorOccurred = true;
      }
      if ((!data.to || data.to.trim() === '') && this.isMandate('to')) {
        // const codeEmptymsg = 'Kindly enter Milestone name';
        // this.toastr.error(codeEmptymsg, 'Error');
        errorOccurred = true;
      }
      if (!errorOccurred) {
        for (let items of this.stepperData) {
          if (items['type'] == 'schedule') {
            items['is_completed'] = true
            return true
          }
        }
      }
      else if (errorOccurred) {
        const enterAll_mandate_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.enterAll_mandate_msg ? this.retrieveMessages[0].errors.enterAll_mandate_msg : 'Kindly Enter All Mandatory fields' : 'Kindly Enter All Mandatory fields';
        this.toasterService.showWarning(enterAll_mandate_msg, 10000)

        return false
      }
    }
    
    if (type == 'advance') {
      return true
    }
    if (type == 'financial') {
      return true
    }
  }
  isMandate(field: any) {
    const mandate = _.where(this.formConfig, {
      type: 'edit-project',
      field_name: field,
      is_active: true,
    });
    if (mandate.length > 0) {
      const isMandate = mandate[0].is_mandant;
      return isMandate;
    }
  }
  async selectOption(option: string): Promise<void> {
    //console.log(option)
    //console.log(this.selectedOption)
    if (this.selectedOption != option) {
      this.changeInServiceType = true;
    } else {
      this.changeInServiceType = false;
    }
    this.selectedOption = option;

    let internal = _.where(this.cards, {
      is_internal: 1,
      id: this.selectedOption,
    });
    if (internal.length > 0) {
      console.log(internal[0]);
      this.MakeFieldsNonMandatory = false;
    } else {
      this.MakeFieldsNonMandatory = true;
    }
    // if(this.stepperFormGroup.get('project_code').value.length==0){
    //   this.refresh=true
    //   //console.log(this.selectedOption)
    //         if(this.selectedOption){

    //            const res=_.where(this.project_code_list,{service_type_id:this.selectedOption})
    //            //console.log(res)
    //               this.stepperFormGroup.patchValue({[`project_code`]: res[0].projectCode || ''})
    //               this.defaultValue=true
    //               this.refresh=false

    //         }
    //       }
    //       else if(this.defaultValue){
    //         this.refresh=true
    //   //console.log(this.selectedOption)
    //         if(this.selectedOption){
    //           const res=_.where(this.project_code_list,{service_type_id:this.selectedOption})
    //            //console.log(res)
    //               this.stepperFormGroup.patchValue({[`project_code`]: res[0].projectCode || ''})

    //               this.defaultValue=true
    //               this.refresh=false

    //         }

    //       }
    this.radioChecked = true;
  }
  validateProjectCode(event: Event) {
    this.defaultValue = false;
    const inputValue = (event.target as HTMLInputElement).value;
    const patternStrings = _.where(this.formConfig, {
      type: 'portfolio-creation',
      field_name: 'portfolio_code',
      is_active: true,
    });
    //console.log(patternStrings);
    const patternRegex = patternStrings[0].pattern;
    const patternParts = patternRegex.split('/');
    const pattern1 = new RegExp(patternParts[1], patternParts[2]);
    //console.log(patternRegex, pattern1);
    //const pattern = /[^0-9A-Za-z]/gi;
    const sanitizedValue = inputValue.replace(pattern1, '').toUpperCase();

    // Check if portfolio code is duplicated
    if (this.codeDuplicated) {
      // Mark the portfolio code as invalid
      this.stepperFormGroup.get('project_code').setErrors({ duplicated: true });
      this.valid = false;
    } else {
      this.stepperFormGroup.get('project_code').setValue(sanitizedValue);
      this.valid = this.stepperFormGroup.get('project_code').valid;
    }

    // this.showPatternError = false;
    // this.showMaxLengthError = false;

    // this.showPatternError = this.portfolioCodeControl.hasError('pattern');
    // this.showMaxLengthError = this.portfolioCodeControl.hasError('maxlength');
  }
  async updateProjectDetails() {
    this.save_disabled = true;
    console.log(this.stepperFormGroup);
    this.week_array = [];
    if (this.monday) {
      this.week_array.push(1);
    }
    if (this.tuesday) {
      this.week_array.push(2);
    }
    if (this.wednesday) {
      this.week_array.push(3);
    }
    if (this.thursday) {
      this.week_array.push(4);
    }
    if (this.friday) {
      this.week_array.push(5);
    }
    if (this.saturday) {
      this.week_array.push(6);
    }
    if (this.sunday) {
      this.week_array.push(7);
    }
    //console.log(this.week_array)
    this.stepperFormGroup.patchValue({
      ['endDate']:
        moment(this.stepperFormGroup.get('endDate').value)
          .utc()
          .format('YYYY-MM-DD') || this.stepperFormGroup.get('endDate').value,
    });
    this.stepperFormGroup.patchValue({
      ['startDate']:
        moment(this.stepperFormGroup.get('startDate').value)
          .utc()
          .format('YYYY-MM-DD') || this.stepperFormGroup.get('startDate').value,
    });
    this.result = this.stepperFormGroup.value;

    this.newData = {
      project_name: this.result.project_name ? this.result.project_name : null,
      project_code: this.result.project_code ? this.result.project_code : null,
      portfolio: this.result.portfolio ?  this.getNameByIdPipe.transform(this.result.portfolio, this.portfolio_list) : null,
      start_date: this.result.startDate ? moment(this.result.startDate).format('DD-MMM-YYYY') : null,
      end_date: this.result.endDate ? moment(this.result.endDate).format('DD-MMM-YYYY') : null,
      currency: this.result.currency ? this.getNameByIdPipe.transform(this.result.currency, this.currency_list) : null,
      project_type: this.result.project_type ? this.getNameByIdPipe.transform(this.result.project_type, this.project_type_list) : null,
      entity: this.result.entity ? this.getNameByIdPipe.transform(this.result.entity, this.entityList) : null,
      division: this.result.division ? this.getNameByIdPipe.transform(this.result.division, this.divisionList) : null,
      sub_division: this.result.sub_division ? this.getNameByIdPipe.transform(this.result.sub_division, this.subDivisionList) : null,
      profit_center: this.result.profit_center ? this.result.profit_center : null,
      p_and_l: this.result.p_and_l ? this.getNameByIdPipe.transform(this.result.p_and_l, this.p_and_l_list) : null,
      legal_entity: this.result.legal_entity ? this.getNameByIdPipe.transform(this.result.legal_entity, this.legal_entity_list) : null,
      sow_reference_number: this.result.sow_reference_number ? this.result.sow_reference_number : null,
      product_category: this.result.product_category ? this.getNameByIdPipe.transform(this.result.product_category, this.product_category_list) : null,
      revenue_type: this.result.revenue_type ? this.getNameByIdPipe.transform(this.result.revenue_type, this.revenue_type_list) : null,
      delivery_type: this.result.delivery_type ? this.getNameByIdPipe.transform(this.result.delivery_type, this.delivery_type_list) : null,
      opportunity_toggle : this.withOpportunity ? 'Q2C' : 'O2C',
      at_risk_toggle : this.risk ? 'At Risk' : 'Secured',
      po_number: this.result.po_number ? this.result.po_number : null,
      po_value: this.result.po_value ? this.result.po_value : null,
      po_reference: this.result.po_reference ? this.result.po_reference : null,
      po_date: this.result.po_date ? moment(this.result.po_date).format('DD-MMM-YYYY') : null, 
      partner: this.result.partner ? this.getNameByIdPipe.transform(this.result.partner, this.customer_list) : null,
      payment_terms: this.result.payment_terms ? this.getNameByIdPipe.transform(this.result.payment_terms, this.payment_list) : null,
      work_day_monday: this.monday ? 'Yes' : 'No',
      work_day_tuesday: this.tuesday ? 'Yes' : 'No',
      work_day_wednesday: this.wednesday ? 'Yes': 'No',
      work_day_thursday: this.thursday ? 'Yes': 'No',
      work_day_friday: this.friday? 'Yes' : 'No',
      work_day_saturday : this.saturday ? 'Yes' : 'No',
      work_day_sunday: this.sunday ? 'Yes' : 'No',
      from: this.result.from ?  this.result.from : null,
      to: this.result.to ?  this.result.to : null,
      daily_working_hours: this.result.daily_working_hours ? this.result.daily_working_hours : null,
      monthly_hours: this.result.monthly_hours ? this.result.monthly_hours : null,
      leave_paid: this.result.leave_paid ? this.getNameByIdPipe.transform(this.result.leave_paid, this.yes_or_no_list) : null,
      holiday_paid: this.result.leave_paid ? this.getNameByIdPipe.transform(this.result.holiday_paid, this.yes_or_no_list) : null,
      work_location: this.fields[0].workLocation ? this.getNameByIdPipe.transform(this.fields[0].workLocation, this.work_location_list) : null,
      location: this.fields[0].country_id ? this.getNameByIdPipe.transform(this.fields[0].country_id, this.country_list) : null,
      holiday_calendar:this.fields[0].calendar ? this.getNameByIdPipe.transform(this.fields[0].calendar, this.holiday_calendar_list) : null,
      service_type: this.selectedOption ? this.getNameByIdPipe.transform(this.selectedOption, this.cards) : null, 
      tags: this.tags && this.tags.length > 0 ? _.map(this.tags, 'name') : null
    }
    if(this.withOpportunity){
      this.newFinancialData = this.withOpportunity ? this.result.financial : null;
      for(let item of this.newFinancialData){
        item.reason = item.reason ? this.getNameByIdPipe.transform(item.reason, this.reason_list) : null;
        item.payment_terms =  item.payment_terms ? this.getNameByIdPipe.transform(item.payment_terms, this.payment_list) : null;
        item.po_date = item.po_date ?  moment(item.po_date).format('DD-MMM-YYYY') : null;
      }
    }
    const mandate = this.checkMandateNotEmpty(
      this.result,
      this.fields,
      this.week_array,
      this.selectedOption
    );
    if (mandate) {
      //console.log(this.result);
      //console.log(this.selectedOption)
      //console.log(this.fields)
      //console.log(this.result.portfolio)

      // if(this.selectedValues.length<1){
      //   this.selectedValues=this.weekGroup.value
      // }
      if (this.protfolioChanged) {
        Swal.fire({
          text: 'Due You Want To Change Portfoio Then,Application Will Be Refreshed !',
          showCancelButton: true,
          confirmButtonText: 'OK',
          cancelButtonText: 'Cancel',
          allowOutsideClick: false,
          allowEscapeKey: false,
          icon: 'warning',
        }).then(async (result) => {
          if (result.isConfirmed) {
            for (let item of this.p_and_l_list) {
              if (item['id'] == this.result.p_and_l) {
                this.result['p_and_l_name'] = item['name'];
                break;
              } else {
                this.result['p_and_l_name'] = null;
              }
            }
            this.result['at_risk'] = this.risk ? 1 : 0;
            this.result['with_opportunity'] = this.withOpportunity ? 1 : 0;
            if (this.withOpportunity) {
              const financialArray = this.stepperFormGroup.get(
                'financial'
              ) as FormArray;
              for (
                let i = 0;
                i < this.stepperFormGroup.get('financial').value.length;
                i++
              ) {
                const quote = financialArray.at(i).get('quote_id');
              }
              this.result['financial_data'] =
                this.stepperFormGroup.get('financial').value;
            } else {
              this.result['financial_data_without_opportunity'] = [
                {
                  invoice_template:
                    this.stepperFormGroup.get('invoice_template').value,
                  po_number: this.stepperFormGroup.get('po_number').value,
                  purchase_order: this.stepperFormGroup.get('po_value').value,
                },
              ];
              this.result['financial_data'] = [];
            }
            for (let items of this.currency_list) {
              if (items['id'] == this.data.currency) {
                this.code = items['name'];
              }
            }
            this.result['external_id'] = this.external_id;
            this.result['item_status_id'] = this.data.item_status_id;
            this.result['tandmFlag'] = this.TandM_selected;
            
            await this.ProjectCreationService.updateProjectDetails(
              this.oldData,
              this.newData,
              this.result,
              this.selectedOption,
              this.week_array,
              this.fields,
              this.tags,
              this.data.item_id,
              this.code,
              this.intergeration,
              this.protfolioChanged,
              this.oldFinancialData,
              this.newFinancialData
            ).then((res: any) => {
              //console.log(res)
              if (res['messType'] == 'S') {
                // this.toastr.success('Project edited successfully', 'Success');
                this.initializeStepper();
                this.dialogRef.close({
                  messType: 'P',
                  project_id: this.data.item_id,
                  data: this.result,
                  portfolio_name: this.portfolio_name,
                });
              } else {
                const projectEdit_unsuccess_msg =
                  this.retrieveMessages.length > 0
                    ? this.retrieveMessages[0].errors.projectEdit_unsuccess_msg
                      ? this.retrieveMessages[0].errors
                          .projectEdit_unsuccess_msg
                      : 'Project editing Unsuccessfull'
                    : 'Project editing Unsuccessfull';
                this.toasterService.showError(projectEdit_unsuccess_msg);
                this.save_disabled = false;
              }
            });
          } else if (result.dismiss === Swal.DismissReason.cancel) {
            this.save_disabled = false;
          }
        });
      } else {
        for (let item of this.p_and_l_list) {
          if (item['id'] == this.result.p_and_l) {
            this.result['p_and_l_name'] = item['name'];
            break;
          } else {
            this.result['p_and_l_name'] = null;
          }
        }
        this.result['at_risk'] = this.risk ? 1 : 0;
        this.result['with_opportunity'] = this.withOpportunity ? 1 : 0;
        if (this.withOpportunity) {
          const financialArray = this.stepperFormGroup.get(
            'financial'
          ) as FormArray;
          for (
            let i = 0;
            i < this.stepperFormGroup.get('financial').value.length;
            i++
          ) {
            const quote = financialArray.at(i).get('quote_id');
          }
          this.result['financial_data'] =
            this.stepperFormGroup.get('financial').value;
        } else {
          this.result['financial_data_without_opportunity'] = [
            {
              invoice_template:
                this.stepperFormGroup.get('invoice_template').value,
              po_number: this.stepperFormGroup.get('po_number').value,
              purchase_order: this.stepperFormGroup.get('po_value').value,
            },
          ];
          this.result['financial_data'] = [];
        }
        for (let items of this.currency_list) {
          if (items['id'] == this.data.currency) {
            this.code = items['name'];
          }
        }
        this.result['external_id'] = this.external_id;
        this.result['item_status_id'] = this.data.item_status_id;
        this.result['tandmFlag'] = this.TandM_selected;

        await this.ProjectCreationService.updateProjectDetails(
          this.oldData,
          this.newData,
          this.result,
          this.selectedOption,
          this.week_array,
          this.fields,
          this.tags,
          this.data.item_id,
          this.code,
          this.intergeration,
          this.protfolioChanged,
          this.oldFinancialData,
          this.newFinancialData
        ).then((res: any) => {
          //console.log(res)
          if (res['messType'] == 'S') {
            // this.toastr.success('Project edited successfully', 'Success');
            this.initializeStepper();
            this.dialogRef.close({
              messType: 'S',
              project_id: this.data.item_id,
              data: this.result,
              portfolio_name: this.portfolio_name,
            });
          } else {
            const projectEdit_unsuccess_msg =
              this.retrieveMessages.length > 0
                ? this.retrieveMessages[0].errors.projectEdit_unsuccess_msg
                  ? this.retrieveMessages[0].errors.projectEdit_unsuccess_msg
                  : 'Project editing Unsuccessfull'
                : 'Project editing Unsuccessfull';
            this.toasterService.showError(projectEdit_unsuccess_msg);
            this.save_disabled = false;
          }
        });
      }
    } else {
      this.save_disabled = false;
    }
  }
  mandateAvailable(type) {
    let errorOccurred = false;
    if (type == 'details') {
      const details = _.where(this.stepperData, {
        type: type,
        is_active: true,
      });

      if (details.length > 0) {
        if (this.isMandate('project_code')) {
          // const codeEmptymsg = 'Kindly enter Milestone name';
          // this.toastr.error(codeEmptymsg, 'Error');
          errorOccurred = true;
        }
        if (this.isMandate('project_name')) {
          // const codeEmptymsg = 'Kindly enter Milestone name';
          // this.toastr.error(codeEmptymsg, 'Error');
          errorOccurred = true;
        }
        if (this.isMandate('portfolio')) {
          // const customerEmptymsg = 'Responsible Is Mandatory';
          // this.toastr.error(customerEmptymsg, 'Error');
          errorOccurred = true;
        }
        if (this.isMandate('start_date')) {
          // const nameEmptymsg = 'Kindly choose When Is It due?';
          // this.toastr.error(nameEmptymsg, 'Error');
          errorOccurred = true;
        }
        if (this.isMandate('end_date')) {
          // const nameEmptymsg = 'Kindly choose When Is It due?';
          // this.toastr.error(nameEmptymsg, 'Error');
          errorOccurred = true;
        }
        if (this.isMandate('project_type')) {
          // const nameEmptymsg = 'Kindly choose When Is It due?';
          // this.toastr.error(nameEmptymsg, 'Error');
          errorOccurred = true;
        }
        if (this.isMandate('service_type')) {
          // const nameEmptymsg = 'Kindly choose When Is It due?';
          // this.toastr.error(nameEmptymsg, 'Error');
          errorOccurred = true;
        }
        if (this.isMandate('project_description')) {
          // const codeEmptymsg = 'Kindly enter Milestone name';
          // this.toastr.error(codeEmptymsg, 'Error');
          errorOccurred = true;
        }
      }
    }

    if (type == 'es') {
      const es = _.where(this.stepperData, { type: type, is_active: true });
      if (es.length > 0) {
        if (this.isMandate('entity')) {
          // const customerEmptymsg = 'Responsible Is Mandatory';
          // this.toastr.error(customerEmptymsg, 'Error');
          errorOccurred = true;
        }
        if (this.isMandate('division')) {
          // const customerEmptymsg = 'Responsible Is Mandatory';
          // this.toastr.error(customerEmptymsg, 'Error');
          errorOccurred = true;
        }
        if (this.isMandate('sub_division')) {
          // const customerEmptymsg = 'Responsible Is Mandatory';
          // this.toastr.error(customerEmptymsg, 'Error');
          errorOccurred = true;
        }
      }
    }

    if (type == 'financial') {
      const financial = _.where(this.stepperData, {
        type: type,
        is_active: true,
      });
      if (financial.length > 0) {
        if (this.isMandate('opportunity') && this.MakeFieldsNonMandatory) {
          // const customerEmptymsg = 'Responsible Is Mandatory';
          // this.toastr.error(customerEmptymsg, 'Error');
          errorOccurred = true;
        }
        if (this.isMandate('invoice_template') && this.MakeFieldsNonMandatory) {
          // const customerEmptymsg = 'Responsible Is Mandatory';
          // this.toastr.error(customerEmptymsg, 'Error');
          errorOccurred = true;
        }
        if (this.isMandate('currency') && this.MakeFieldsNonMandatory) {
          // const customerEmptymsg = 'Responsible Is Mandatory';
          // this.toastr.error(customerEmptymsg, 'Error');
          errorOccurred = true;
        }
        if (this.isMandate('quote') && this.MakeFieldsNonMandatory) {
          // const codeEmptymsg = 'Kindly enter Milestone name';
          // this.toastr.error(codeEmptymsg, 'Error');
          errorOccurred = true;
        }
        if (this.isMandate('po_number') && this.MakeFieldsNonMandatory) {
          // const codeEmptymsg = 'Kindly enter Milestone name';
          // this.toastr.error(codeEmptymsg, 'Error');
          errorOccurred = true;
        }
        if (this.isMandate('po_value') && this.MakeFieldsNonMandatory) {
          // const codeEmptymsg = 'Kindly enter Milestone name';
          // this.toastr.error(codeEmptymsg, 'Error');
          errorOccurred = true;
        }
      }
    }

    if (type == 'schedule') {
      const schedule = _.where(this.stepperData, {
        type: type,
        is_active: true,
      });
      if (schedule.length > 0) {
        if (this.isMandate('week')) {
          errorOccurred = true;
        }
        if (this.isMandate('work_location')) {
          // const customerEmptymsg = 'Responsible Is Mandatory';
          // this.toastr.error(customerEmptymsg, 'Error');
          errorOccurred = true;
        }
        if (this.isMandate('holiday_calendar')) {
          // const customerEmptymsg = 'Responsible Is Mandatory';
          // this.toastr.error(customerEmptymsg, 'Error');
          errorOccurred = true;
        }
        if (this.isMandate('location')) {
          // const codeEmptymsg = 'Kindly enter Milestone name';
          // this.toastr.error(codeEmptymsg, 'Error');
          errorOccurred = true;
        }
        if (this.isMandate('from')) {
          // const codeEmptymsg = 'Kindly enter Milestone name';
          // this.toastr.error(codeEmptymsg, 'Error');
          errorOccurred = true;
        }
        if (this.isMandate('to')) {
          // const codeEmptymsg = 'Kindly enter Milestone name';
          // this.toastr.error(codeEmptymsg, 'Error');
          errorOccurred = true;
        }
      }
    }
    if (errorOccurred) {
      return false; // Return false if any error occurred
    } else {
      return true; // Return true if no errors occurred
    }
  }
  lastStepper(type) {
    const details = _.where(this.stepperData, {
      type: type,
      is_active: true,
      is_last_step: true,
    });
    if (details.length > 0) {
      return true;
    } else {
      return false;
    }
  }
  clearFilter() {
    this.displayList = this.ESdata;
    this.subDivision = undefined;
    this.division = undefined;
    this.entity = undefined;
    this.stepperFormGroup.patchValue({ [`entity`]: null });
    this.stepperFormGroup.patchValue({ [`division`]: null });
    this.stepperFormGroup.patchValue({ [`sub_division`]: null });
  }
  async getListValue(name, id) {
    let entityChoosen = _.uniq(_.pluck(this.ESdata, id));
    let val_result = [];
    for (let entit of entityChoosen) {
      let result = _.where(this.ESdata, { [id]: entit });
      if (result.length > 0) {
        val_result.push({
          id: result[0][id],
          name: result[0][name],
        });
      }
    }
    //console.log(name, entityChoosen, val_result)
    return val_result;
  }
  selectData(display, i) {
    this.check = true;
    //console.log(display, i)
    if (this.selectedData) {
      if (this.selectedData == display) {
        this.selectedData = undefined;
        this.selectedIndex = undefined;
      } else {
        this.selectedData = display;
        this.selectedIndex = i;
      }
    } else {
      this.selectedData = display;
      this.selectedIndex = i;
    }
    //console.log(this.selectedData, this.selectedIndex)
    this.entity = this.selectedData.entity_id;
    this.division = this.selectedData.division_id;
    this.subDivision = this.selectedData.sub_division_id;
    this.stepperFormGroup.patchValue({ [`entity`]: this.entity });
    this.stepperFormGroup.patchValue({ [`division`]: this.division });
    this.stepperFormGroup.patchValue({ [`sub_division`]: this.subDivision });
    this.check = false;
    //console.log(this.selectedData, this.selectedIndex)
  }

  submitData() {
    if (this.selectedData) {
      // this.dialogRef.close({messType:"S", data: this.selectedData})
    } else {
      this.utilityService.showMessage(
        'Kindly select option to proceed!',
        'Dismiss',
        3000
      );
    }
  }
  async filterList() {
    this.entity = this.stepperFormGroup.get('entity').value;
    this.division = this.stepperFormGroup.get('division').value;
    this.subDivision = this.stepperFormGroup.get('sub_division').value;
    let val = {};
    //console.log(this.subDivision, this.division, this.entity)
    if (this.subDivision) {
      val['sub_division_id'] = this.subDivision;
    }
    if (this.division) {
      val['division_id'] = this.division;
    }
    if (this.entity) {
      val['entity_id'] = this.entity;
    }
    //console.log(val)
    this.displayList = _.where(this.ESdata, val);
    this.selectedData = undefined;
    this.selectedIndex = undefined;

    //console.log(this.displayList)
  }
  async getListValueFilter(name, id) {
    let entityChoosen = _.uniq(_.pluck(this.Filterdata, id));
    let val_result = [];
    for (let entit of entityChoosen) {
      let result = _.where(this.Filterdata, { [id]: entit });
      if (result.length > 0) {
        val_result.push({
          id: result[0][id],
          name: result[0][name],
        });
      }
    }
    //console.log(name, entityChoosen, val_result)
    return val_result;
  }
  quoteKeyUp(event) {
    //console.log(event.target.value.toString().length)
    //console.log(event.target.value.toString())
    if (this.stepperFormGroup.get('quote').value < 1) {
      const opportunityNegative_msg =
        this.retrieveMessages.length > 0
          ? this.retrieveMessages[0].errors.opportunityNegative_msg
            ? this.retrieveMessages[0].errors.opportunityNegative_msg
            : 'Quote value cannot be negative'
          : 'Quote value cannot be negative';

      this.toasterService.showWarning(opportunityNegative_msg, 10000);
      this.stepperFormGroup.patchValue({ ['quote']: '' });
    }
  }
  poValueKeyUp(event) {
    //console.log(event.target.value.toString().length)
    //console.log(event.target.value.toString())
    if (this.stepperFormGroup.get('po_value').value < 1) {
      //this.toastr.warning('po value cannot be negative','Warning')
      this.stepperFormGroup.patchValue({ ['po_value']: '' });
    }
  }
  checkSaturday() {
    this.saturday = !this.saturday;
  }
  checkSunday() {
    this.sunday = !this.sunday;
  }
  checkMonday() {
    this.monday = !this.monday;
  }
  checkTuesday() {
    this.tuesday = !this.tuesday;
  }
  checkWednesday() {
    this.wednesday = !this.wednesday;
  }
  checkThursday() {
    this.thursday = !this.thursday;
  }
  checkFriday() {
    this.friday = !this.friday;
  }
  storeWLocation(i: any, event: Event) {
    const wlValue = (event.target as HTMLInputElement).value;
    this.fields[i].workLocation = wlValue;
  }
  storeLocation(i: any, lValue: any) {
    this.fields[i].country_id = lValue;
    const index = this.country_list.findIndex((item) => item['id'] === lValue);
    this.fields[i].location = this.country_list[index].name;
    //console.log(this.fields[i].location);
    //console.log(this.country_list[index].name)
  }
  storeHolidayCalendar(i: any, event: Event) {
    const cValue = (event.target as HTMLInputElement).value;
    //console.log(cValue)
    for (let item of this.holiday_calendar_list) {
      if (item['id'] == cValue) {
        this.fields[i].calendar = item['id'];
      }
    }
  }
  removeLocationField(i: any) {
    //console.log(i)
    const indexToDelete = i; // The index of the object you want to delete

    if (indexToDelete >= 0 && indexToDelete < this.fields.length) {
      this.fields.splice(indexToDelete, 1); // This will remove one element at the specified index.
    } else {
      //console.log('Invalid index provided');
    }
  }
  addField() {
    this.fields.push({ workLocation: '', location: '', calendar: '' });
  }
  handleValueEmitted(emittedValue: string) {
    this.tags = emittedValue;
    //console.log(emittedValue)
    //console.log(this.tags)
  }
  onCloseClick(): void {
    //console.log(this.stepperFormGroup)
    if (!this.stepperFormGroup.untouched) {
      this.utilityService
        .openConfirmationSweetAlertWithCustom(
          'Are you sure',
          'You want to Close without saving'
        )
        .then((result) => {
          if (result) {
            this.initializeStepper();
            this.dialogRef.close({ messType: 'E' });
          }
        });
    } else {
      this.initializeStepper();
      this.dialogRef.close({ messType: 'E' });
    }
  }
  checkMandateNotEmpty(data: any, ws: any, week: any, selectedOption: any) {
    let errorOccurred = false;
    const financial_data = this.stepperFormGroup.get('financial').value;
    const financial_data_without_opportunity=this.stepperFormGroup.get('fieldsArray').value
    const details = _.where(this.stepperData, {
      type: 'details',
      is_active: true,
    });

    if (details.length > 0) {
      if (
        (!data.project_code || data.project_code.trim() === '') &&
        this.isMandate('project_code')
      ) {
        // const codeEmptymsg = 'Kindly enter Milestone name';
        // this.toastr.error(codeEmptymsg, 'Error');
        //console.log('test')
        errorOccurred = true;
      }
      if (
        (!data.project_name || data.project_name.trim() === '') &&
        this.isMandate('project_name')
      ) {
        // const codeEmptymsg = 'Kindly enter Milestone name';
        // this.toastr.error(codeEmptymsg, 'Error');
        //console.log('test')
        errorOccurred = true;
      }
      if (
        (data.portfolio === null ||
          data.portfolio === undefined ||
          data.portfolio === '') &&
        this.isMandate('portfolio')
      ) {
        // const customerEmptymsg = 'Responsible Is Mandatory';
        // this.toastr.error(customerEmptymsg, 'Error');
        errorOccurred = true;
      }
      this.checkDates(data.startDate, data.endDate);
      if (!this.dateLogic) {
        const valid_date_err = 'Kindly enter valid date';
        this.toasterService.showWarning(valid_date_err, 10000);
        // errorOccurred = true;
      }
      if (!this.stepperFormGroup.get('startDate').valid) {
        errorOccurred = true;
      }
      if (
        (data.startDate === null ||
          data.startDate === undefined ||
          data.startDate === '') &&
        this.isMandate('start_date')
      ) {
        // const nameEmptymsg = 'Kindly choose When Is It due?';
        // this.toastr.error(nameEmptymsg, 'Error');
        errorOccurred = true;
      }
      if (!this.stepperFormGroup.get('endDate').valid) {
        errorOccurred = true;
      }
      if (
        (data.endDate === null ||
          data.endDate === undefined ||
          data.endDate === '') &&
        this.isMandate('end_date')
      ) {
        // const nameEmptymsg = 'Kindly choose When Is It due?';
        // this.toastr.error(nameEmptymsg, 'Error');
        errorOccurred = true;
      }
      if (
        (data.project_type === null ||
          data.project_type === undefined ||
          data.project_type === '') &&
        this.isMandate('project_type')
      ) {
        // const nameEmptymsg = 'Kindly choose When Is It due?';
        // this.toastr.error(nameEmptymsg, 'Error');
        errorOccurred = true;
      }
      if (
        (data.currency === null ||
          data.currency === undefined ||
          data.currency === '') &&
        this.isMandate('currency')
      ) {
        // const customerEmptymsg = 'Responsible Is Mandatory';
        // this.toastr.error(customerEmptymsg, 'Error');
        errorOccurred = true;
      }
      if (
        (selectedOption === null ||
          selectedOption === undefined ||
          selectedOption === '') &&
        this.isMandate('service_type')
      ) {
        // const nameEmptymsg = 'Kindly choose When Is It due?';
        // this.toastr.error(nameEmptymsg, 'Error');
        errorOccurred = true;
      }
      if (
        (!data.description || data.description.trim() === '') &&
        this.isMandate('project_description')
      ) {
        // const codeEmptymsg = 'Kindly enter Milestone name';
        // this.toastr.error(codeEmptymsg, 'Error');
        errorOccurred = true;
      }
    }

    const es = _.where(this.stepperData, { type: 'es', is_active: true });
    if (es.length > 0) {
      if (
        (data.entity === null ||
          data.entity === undefined ||
          data.entity === '') &&
        this.isMandate('entity')
      ) {
        // const customerEmptymsg = 'Responsible Is Mandatory';
        // this.toastr.error(customerEmptymsg, 'Error');
        errorOccurred = true;
      }
      if (
        (data.division === null ||
          data.division === undefined ||
          data.division === '') &&
        this.isMandate('division')
      ) {
        // const customerEmptymsg = 'Responsible Is Mandatory';
        // this.toastr.error(customerEmptymsg, 'Error');
        errorOccurred = true;
      }
      if (
        (data.sub_division === null ||
          data.sub_division === undefined ||
          data.sub_division === '') &&
        this.isMandate('sub_division')
      ) {
        // const customerEmptymsg = 'Responsible Is Mandatory';
        // this.toastr.error(customerEmptymsg, 'Error');
        errorOccurred = true;
      }
    }
    const financial = _.where(this.stepperData, {
      type: 'financial',
      is_active: true,
    });
    if (financial.length > 0) {
      if (this.withOpportunity) {
        for (let item of financial_data) {
          if (item['is_active'] == 1) {
            if (
              this.MakeFieldsNonMandatory &&
              (item['opportunity_id'] === null ||
                item['opportunity_id'] === undefined ||
                item['opportunity_id'] === '') &&
              this.isMandate('opportunity')
            ) {
              // const customerEmptymsg = 'Responsible Is Mandatory';
              // this.toastr.error(customerEmptymsg, 'Error');
              errorOccurred = true;
              break;
            } else if (
              (item['reason'] === null ||
                item['reason'] === undefined ||
                item['reason'] === '') &&
              this.isMandate('reason')
            ) {
              // const customerEmptymsg = 'Responsible Is Mandatory';
              // this.toastr.error(customerEmptymsg, 'Error');
              errorOccurred = true;
              break;
            } else if (
              (item['quote_id'] === null ||
                item['quote_id'] === undefined ||
                item['quote_id'] === '') &&
              this.isMandate('quote')
            ) {
              // const codeEmptymsg = 'Kindly enter Milestone name';
              // this.toastr.error(codeEmptymsg, 'Error');
              errorOccurred = true;
              break;
            } else if (
              !this.MakeFieldsNonMandatory &&
              (!item['po_number'] || item['po_number'].trim() === '')
            ) {
              // const codeEmptymsg = 'Kindly enter Milestone name';
              // this.toastr.error(codeEmptymsg, 'Error');
              errorOccurred = true;
              break;
            } else if (
              this.MakeFieldsNonMandatory &&
              (!item['po_number'] || item['po_number'].trim() === '') &&
              this.isMandate('po_number_with_opportunity')
            ) {
              // const codeEmptymsg = 'Kindly enter Milestone name';
              // this.toastr.error(codeEmptymsg, 'Error');
              errorOccurred = true;
              break;
            } else if (
              !this.MakeFieldsNonMandatory &&
              (item['purchase_order'] === null ||
                item['purchase_order'] === undefined ||
                item['purchase_order'] === '')
            ) {
              errorOccurred = true;
              break;
            } else if (
              this.MakeFieldsNonMandatory &&
              (item['purchase_order'] === null ||
                item['purchase_order'] === undefined ||
                item['purchase_order'] === '') &&
              this.isMandate('po_value_with_opportunity')
            ) {
              errorOccurred = true;
              break;
            } else if (
              (item['payment_terms'] === null ||
                item['payment_terms'] === undefined ||
                item['payment_terms'] === '') &&
              this.isMandate('payment_terms')
            ) {
              errorOccurred = true;
              break;
            } else if (
              (item['partner'] === null ||
                item['partner'] === undefined ||
                item['partner'] === '') &&
              this.isMandate('partner')
            ) {
              errorOccurred = true;
              break;
            } else if (
              (item['po_date'] === null ||
                item['po_date'] === undefined ||
                item['po_date'] === '') &&
              this.isMandate('po_date')
            ) {
              errorOccurred = true;
              break;
            } else if (
              (item['po_reference'] === null ||
                item['po_reference'] === undefined ||
                item['po_reference'] === '') &&
              this.isMandate('po_reference')
            ) {
              errorOccurred = true;
              break;
            }
          }
        }
      } else {
        if (
          (data.invoice_template === null ||
            data.invoice_template === undefined ||
            data.invoice_template === '') &&
          this.isMandate('invoice_template')
        ) {
          // const customerEmptymsg = 'Responsible Is Mandatory';
          // this.toastr.error(customerEmptymsg, 'Error');
          errorOccurred = true;
          //console.log("IV")
        }
        // if(this.MakeFieldsNonMandatory && ((!data.po_number || data.po_number.trim() === '') && this.isMandate('po_number'))){
        //   this.toastr.error(codeEmptymsg, 'Error');
        //   errorOccurred = true;
        // }
        // if (!this.MakeFieldsNonMandatory && (!data.po_number || data.po_number.trim() === '')) {
        //   // const codeEmptymsg = 'Kindly enter Milestone name';
        //   // this.toastr.error(codeEmptymsg, 'Error');
        //   errorOccurred = true;
        //   //console.log("pon")
        // } else if (this.MakeFieldsNonMandatory && (!data.po_number || data.po_number.trim() === '') && this.isMandate('po_number')) {
        //   // const codeEmptymsg = 'Kindly enter Milestone name';
        //   // this.toastr.error(codeEmptymsg, 'Error');
        //   errorOccurred = true;
        //   //console.log("pon")
        // }
        if (
          !this.MakeFieldsNonMandatory &&
          (data.po_value === null ||
            data.po_value === undefined ||
            data.po_value === '')
        ) {
          // const codeEmptymsg = 'Kindly enter Milestone name';
          // this.toastr.error(codeEmptymsg, 'Error');
          errorOccurred = true;
          //console.log("pov")
        } else if (
          this.MakeFieldsNonMandatory &&
          (data.po_value === null ||
            data.po_value === undefined ||
            data.po_value === '') &&
          this.isMandate('po_value')
        ) {
          // const codeEmptymsg = 'Kindly enter Milestone name';
          // this.toastr.error(codeEmptymsg, 'Error');
          errorOccurred = true;
          //console.log("pov")
        }
      }
    }
    const schedule = _.where(this.stepperData, {
      type: 'schedule',
      is_active: true,
    });
    if (schedule.length > 0) {
      if (week.length == 0 && this.isMandate('week')) {
        errorOccurred = true;
      }
      for (let i = 0; i < ws.length; i++) {
        if (
          (ws[i].workLocation === null ||
            ws[i].workLocation === undefined ||
            ws[i].workLocation === '') &&
          this.isMandate('work_location')
        ) {
          // const customerEmptymsg = 'Responsible Is Mandatory';
          // this.toastr.error(customerEmptymsg, 'Error');
          errorOccurred = true;
        }
        if (
          (ws[i].calendar === null ||
            ws[i].calendar === undefined ||
            ws[i].calendar === '') &&
          this.isMandate('holiday_calendar')
        ) {
          // const customerEmptymsg = 'Responsible Is Mandatory';
          // this.toastr.error(customerEmptymsg, 'Error');
          errorOccurred = true;
        }
        if (
          (!ws[i].location || ws[i].location.trim() === '') &&
          this.isMandate('location')
        ) {
          // const codeEmptymsg = 'Kindly enter Milestone name';
          // this.toastr.error(codeEmptymsg, 'Error');
          errorOccurred = true;
        }
      }
      if ((!data.from || data.from.trim() === '') && this.isMandate('from')) {
        // const codeEmptymsg = 'Kindly enter Milestone name';
        // this.toastr.error(codeEmptymsg, 'Error');
        errorOccurred = true;
      }
      if ((!data.to || data.to.trim() === '') && this.isMandate('to')) {
        // const codeEmptymsg = 'Kindly enter Milestone name';
        // this.toastr.error(codeEmptymsg, 'Error');
        errorOccurred = true;
      }
    }

    if (errorOccurred && !this.dateLogic) {
      const enterAll_mandate_msg =
        this.retrieveMessages.length > 0
          ? this.retrieveMessages[0].errors.enterAll_mandate_msg
            ? this.retrieveMessages[0].errors.enterAll_mandate_msg
            : 'Kindly Enter All Mandatory fields'
          : 'Kindly Enter All Mandatory fields';
      this.toasterService.showWarning(enterAll_mandate_msg, 10000);
      return false; // Return false if any error occurred
    } else {
      return true; // Return true if no errors occurred
    }
  }
  checkDates(start: any, end: any) {
    //console.log("date validation entry")
    const startDate = moment(start).utc();
    const endDate = moment(end).utc();
    //console.log(start, end)
    if (startDate.isValid() && endDate.isValid()) {
      if (startDate.isBefore(endDate) || startDate.isSame(endDate)) {
        this.dateLogic = true;
      } else {
        this.dateLogic = false;
      }
      //console.log(this.dateLogic, 'valid')
    } else {
      this.dateLogic = false;
      //this.toastr.warning("Invalid Date Format", 'Warning');
      console.error('Invalid date format', this.dateLogic);
    }
  }
  initializeStepper() {
    this.stepperData[0].is_selected = true;
    this.stepperData[0].is_crossed = false;
    this.stepperData[0].is_completed = false;
    for (let i = 1; i < this.stepperData.length; i++) {
      this.stepperData[i].is_selected = false;
      this.stepperData[i].is_crossed = true;
      this.stepperData[i].is_completed = true;
    }
    this.stepperData = [];
    this.stepperData = _.filter(this.stepperData, { is_active: true });
  }
  checkStandard() {
    // if(this.standard==false){
    //   this.standard=true
    //   this.risk=false
    //   this.poNonMandate = false;
    // }
    this.toggleSecuredChecked = false;
    this.standard = true;
    this.risk = false;
    this.poNonMandate = false;
  }
  checkAtRisk() {
    // if(this.risk==false){
    //   this.standard=false
    //   this.risk=true
    //   this.poNonMandate = true;
    // }
    this.toggleSecuredChecked = true;
    this.standard = false;
    this.risk = true;
    this.poNonMandate = true;
  }
  async getOpportunityData(id, i, check) {
    //console.log(id)
    //console.log(i)
    const financialArray = this.stepperFormGroup.get('financial') as FormArray;
    if (id) {
      for (let items of this.opportunity_list) {
        if (id.id == items['id']) {
          this.opportunity_status = items['status_name'];
          const status = financialArray.at(i).get('opportunity_status');
          status.patchValue(items['status_name']);
          break;
        }
      }
      await this.ProjectCreationService.getFinancialValues(id.id).then(
        (res) => {
          //console.log(res)
          if (res['messType'] == 'S' && res['data'].length > 0) {
            this.financialValue = res['data'];

            this.quote_id = this.financialValue[0].quote_header_id;
            //console.log(this.quote_id)
            const quote = financialArray.at(i).get('quote_id');
            quote.patchValue(this.quote_id);
            this.financeFieldDisable = true;
            if (this.financialValue.length > 0) {
              if (
                this.financialValue[0].quote_amount != null ||
                this.financialValue[0].quote_amount > 0
              ) {
                const po_value = financialArray.at(i).get('purchase_order');
                po_value.patchValue(this.financialValue[0].quote_amount);
                // this.stepperFormGroup.patchValue({ [`po_value`]: this.financialValue[0].quote_amount || '' })

                // this.stepperFormGroup.get('quote').disable()
              } else {
                const po_value = financialArray.at(i).get('purchase_order');
                if (
                  financialArray.at(i).get('purchase_order').value == null ||
                  financialArray.at(i).get('purchase_order').value == 0
                ) {
                  po_value.patchValue('');
                }

                // this.stepperFormGroup.get('quote').enable()
              }
              const po_number = financialArray.at(i).get('po_number');
              po_number.patchValue(this.financialValue[0].po_number)

              const po_date = financialArray.at(i).get('po_date');
              po_date.patchValue(this.financialValue[0].po_date)

              const payment_terms = financialArray.at(i).get('payment_terms');
              payment_terms.patchValue(this.financialValue[0].payment_terms)
            } else {
              const po_value = financialArray.at(i).get('purchase_order');
              if (
                financialArray.at(i).get('purchase_order').value == null ||
                financialArray.at(i).get('purchase_order').value == 0
              ) {
                po_value.patchValue('');
              }
              // this.stepperFormGroup.patchValue({ ['po_value']: '' })
              // this.stepperFormGroup.get('po_value').enable()
            }
          } else {
            // this.stepperFormGroup.patchValue({ ['purchase_order']: '' })
            // this.stepperFormGroup.get('purchase_order').enable()
            //  this.stepperFormGroup.patchValue({ [`quote_id`]:'' })
            const po_value = financialArray.at(i).get('purchase_order');
            if (check != 1) {
              po_value.patchValue('');
            }
            const quote = financialArray.at(i).get('quote_id');
            quote.patchValue('');
            this.financeFieldDisable = false;
          }
        }
      );

      // this.stepperFormGroup.patchValue({[`quote`]:  items['quote_id']|| ''})
      // this.stepperFormGroup.patchValue({[`po_value`]:  items['po_value'] || ''})
      // this.stepperFormGroup.patchValue({[`po_number`]: items['po_number'] || ''})
    } else {
      //   this.stepperFormGroup.patchValue({ ['purchase_order']: '' })
      //   this.stepperFormGroup.get('purchase_order').enable()
      //  this.stepperFormGroup.patchValue({ [`quote_id`]:'' })
      const po_value = financialArray.at(i).get('purchase_order');
      po_value.patchValue('');
      const quote = financialArray.at(i).get('quote_id');
      quote.patchValue('');
      this.financeFieldDisable = false;
    }
  }

  async getParentOpportunityData(id, i, check) {
    //console.log(id)
    //console.log(i)
    const financialArray = this.stepperFormGroup.get('financial') as FormArray;
    if (id) {
      for (let items of this.opportunity_list) {
        if (id.id == items['id']) {
          this.opportunity_status = items['status_name'];
          const status = financialArray.at(i).get('opportunity_status');
          status.patchValue(items['status_name']);
          break;
        }
      }
      await this.ProjectCreationService.getFinancialValues(id.id).then(
        (res) => {
          //console.log(res)
          if (res['messType'] == 'S' && res['data'].length > 0) {
            this.financialValue = res['data'];

            this.quote_id = this.financialValue[0].quote_header_id;
            //console.log(this.quote_id)
            const quote = financialArray.at(i).get('quote_id');
            quote.patchValue(this.quote_id);
            this.financeFieldDisable = true;
            if (this.financialValue.length > 0) {
              if (
                this.financialValue[0].quote_amount != null ||
                this.financialValue[0].quote_amount > 0
              ) {
                const po_value = financialArray.at(i).get('purchase_order');
                po_value.patchValue(this.financialValue[0].quote_amount);
                // this.stepperFormGroup.patchValue({ [`po_value`]: this.financialValue[0].quote_amount || '' })

                // this.stepperFormGroup.get('quote').disable()
              } else {
                const po_value = financialArray.at(i).get('purchase_order');
                if (
                  financialArray.at(i).get('purchase_order').value == null ||
                  financialArray.at(i).get('purchase_order').value == 0
                ) {
                  po_value.patchValue('');
                }

                // this.stepperFormGroup.get('quote').enable()
              }
            } else {
              const po_value = financialArray.at(i).get('purchase_order');
              if (
                financialArray.at(i).get('purchase_order').value == null ||
                financialArray.at(i).get('purchase_order').value == 0
              ) {
                po_value.patchValue('');
              }
              // this.stepperFormGroup.patchValue({ ['po_value']: '' })
              // this.stepperFormGroup.get('po_value').enable()
            }
          } else {
            // this.stepperFormGroup.patchValue({ ['purchase_order']: '' })
            // this.stepperFormGroup.get('purchase_order').enable()
            //  this.stepperFormGroup.patchValue({ [`quote_id`]:'' })
            const po_value = financialArray.at(i).get('purchase_order');
            if (check != 1) {
              po_value.patchValue('');
            }
            const quote = financialArray.at(i).get('quote_id');
            quote.patchValue('');
            this.financeFieldDisable = false;
          }
        }
      );

      for (let i = 0; i <= financialArray.length; i++) {
        if (
          financialArray.controls[i].get('opportunity_id').value != id.id ||
          financialArray.controls[i].get('opportunity_id').value == null ||
          financialArray.controls[i].get('opportunity_id').value == ''
        ) {
          (this.stepperFormGroup.get('financial') as FormArray).removeAt(i);
        }
      }

      await this.ProjectCreationService.getAllChildOpportunity(id.id).then(
        (res) => {
          if (!res['err']) {
            this.child_opportunity_list = res['data'];
          }
        }
      );

      // this.stepperFormGroup.patchValue({[`quote`]:  items['quote_id']|| ''})
      // this.stepperFormGroup.patchValue({[`po_value`]:  items['po_value'] || ''})
      // this.stepperFormGroup.patchValue({[`po_number`]: items['po_number'] || ''})
    } else {
      //   this.stepperFormGroup.patchValue({ ['purchase_order']: '' })
      //   this.stepperFormGroup.get('purchase_order').enable()
      //  this.stepperFormGroup.patchValue({ [`quote_id`]:'' })
      const po_value = financialArray.at(i).get('purchase_order');
      po_value.patchValue('');
      const quote = financialArray.at(i).get('quote_id');
      quote.patchValue('');
      this.financeFieldDisable = false;
    }
  }
  addFinancial(): void {
    this.stepperFormGroup.value.financial = this.stepperFormGroup.get(
      'financial'
    ) as FormArray;
    this.stepperFormGroup.value.financial.push(this.createAcceptanceCriteria());
    //console.log('new', this.stepperFormGroup.value);
  }
  createAcceptanceCriteria(): FormGroup {
    return this.formBuilder.group({
      opportunity_id: '',
      quote_id: '',
      currency: '',
      invoice_template: '',
      po_number: '',
      purchase_order: '',
      opportunity_status: '',
      reason: '',
      po_reference: '',
      partner: '',
      po_date: '',
      payment_terms: '',
      is_active: 1,
      enable: 1,
    });
  }
  removeFinancial(i: any) {
    //console.log(i)
    if ((this.stepperFormGroup.get('financial') as FormArray).length > 1) {
      // this.removedCriteriaRecord.push(this.contactForm.value.acceptance_criteria_arr[i].id);
      // //console.log("removed criteria", this.removedCriteriaRecord);
      const financialArray = this.stepperFormGroup.get(
        'financial'
      ) as FormArray;
      const is_active = financialArray.at(i).get('is_active');
      is_active.patchValue(0);
      (this.stepperFormGroup.get('financial') as FormArray).removeAt(i);
      // (this.stepperFormGroup.get('financial') as FormArray).removeAt(i);
    }
  }

  checkSecuredOrRisk() {
    //console.log('toggle', this.toggleSecuredChecked);
    if (this.toggleSecuredChecked) {
      this.checkStandard();
    } else if (!this.toggleSecuredChecked) {
      this.checkAtRisk();
    }
  }
  checkOpportunity() {
    //console.log('toggle', this.toggleOpportunityChecked);
    if (this.toggleOpportunityChecked) {
      this.checkWithOpportunity();
    } else if (!this.toggleOpportunityChecked) {
      this.checkWithoutOpportunity();
    }
  }

  checkWithoutOpportunity() {
    // if(this.withoutOpportunity==false){
    //   this.withoutOpportunity=true
    //   this.withOpportunity=false
    // }
    this.toggleOpportunityChecked = true;
    this.withoutOpportunity = true;
    this.withOpportunity = false;
  }
  checkWithOpportunity() {
    // if(this.withOpportunity==false){
    //   this.withoutOpportunity=false
    //   this.withOpportunity=true
    // }
    this.toggleOpportunityChecked = false;
    this.withOpportunity = true;
    this.withoutOpportunity = false;
  }
  selectCheckBox(event: Event) {
    //console.log(this.TandM_selected)
    // const BValue = (event.target as HTMLInputElement).value;
    // this.TandM_selected=BValue
  }

  getPONumberDisabledCheck() {
    if (this.risk) {
      this.stepperFormGroup.patchValue({ po_number: '' });

      let financialArray: any = this.stepperFormGroup.get(
        'financial'
      ) as FormArray;

      //console.log(financialArray)

      for (let i = 0; i < financialArray.length; i++) {
        financialArray.controls[i].get('po_number').setValue('');
      }
    }
    return this.risk ? true : false;
  }

  getDisabledColor(field_name, status_id) {
    if (this.risk) {
      return '#E8E9EE';
    } else {
      return this.getDisabledStatusColor(field_name, status_id);
    }
  }

  checkDisabledBasedOnStatus(field_name) {
    let disabledConfig = _.where(this.formConfig, {
      type: 'edit-project',
      field_name: field_name,
      is_active: true,
    });

    if (disabledConfig.length > 0) {
      if (
        _.contains(
          disabledConfig[0]['disabled_status'],
          this.data.item_status_id
        )
      ) {
        return true;
      } else {
        return false;
      }
    } else {
      return false;
    }
  }

  getDisabledStatusColor(field_name, status_id) {
    let disabledConfig = _.where(this.formConfig, {
      type: 'edit-project',
      field_name: field_name,
      is_active: true,
    });

    if (disabledConfig.length > 0) {
      if (_.contains(disabledConfig[0]['disabled_status'], status_id)) {
        return '#E8E9EE';
      } else {
        // if(field_name==='currency'){
        //      if(this.milestoneList.length>0){
        //       return "#E8E9EE";
        //      }
        // }
        return '#FFFFFF';
      }
    } else {
      // if(field_name==='currency'){
      //   if(this.milestoneList.length>0){
      //    return "#E8E9EE";
      //   }
      return '#FFFFFF';
    }
  }

  calculateMinEndDate() {
    if (
      this.stepperFormGroup.get('startDate').value != '' &&
      this.max_end_date
    ) {
      return moment(this.stepperFormGroup.get('startDate').value)
        .utc()
        .isAfter(this.max_end_date)
        ? this.stepperFormGroup.get('startDate').value
        : this.max_end_date;
    } else if (this.stepperFormGroup.get('startDate').value != '') {
      return this.stepperFormGroup.get('startDate').value;
    } else if (this.max_end_date) {
      return this.max_end_date;
    }
  }

  calculateMaxStartDate() {
    if (
      this.stepperFormGroup.get('endDate').value != '' &&
      this.min_start_date
    ) {
      return moment(this.min_start_date)
        .utc()
        .isAfter(this.stepperFormGroup.get('endDate').value)
        ? this.stepperFormGroup.get('endDate').value
        : this.min_start_date;
    } else if (this.stepperFormGroup.get('endDate').value != '') {
      return this.stepperFormGroup.get('endDate').value;
    } else if (this.min_start_date) {
      return this.min_start_date;
    }
  }

  getPONumberDisabledColor(field_name, status_id) {
    let checkDisabled = this.getDisabledStatusColor(field_name, status_id);

    if (checkDisabled == '#E8E9EE') {
      return checkDisabled;
    } else {
      if (this.risk) {
        return '#E8E9EE';
      } else {
        return '';
      }
    }
  }

  checkRisk(){
    let checkRecords = _.where(this.formConfig,{type:"edit-project", field_name:"checkRiskStatus", is_active: true});

    if(checkRecords.length>0)
    {
        if(this.risk)
        {
            return true;
        }
        else
        {
            return false;
        }
    }
    else
    {
        return true;
    }
  }
}
export interface DialogData {
  mode: any;
  data: any;
}
