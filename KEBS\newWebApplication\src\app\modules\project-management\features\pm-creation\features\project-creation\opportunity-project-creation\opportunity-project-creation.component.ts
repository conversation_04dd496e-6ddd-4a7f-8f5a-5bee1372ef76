import { Component, Inject, OnInit } from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog';
import { PmMasterService } from 'src/app/modules/project-management/services/pm-master.service';
import { UtilityService } from 'src/app/services/utility/utility.service';
import * as _ from 'underscore';
import { ProjectCreationService } from '../services/project-creation.service'
import { SubSink } from 'subsink';
import { ToasterMessageService } from 'src/app/modules/project-management/shared-lazy-loaded/components/toaster-message/toaster-message.service';
import moment from 'moment';
import { MomentDateAdapter, MAT_MOMENT_DATE_ADAPTER_OPTIONS } from '@angular/material-moment-adapter';
import { DateAdapter, MAT_DATE_LOCALE, MAT_DATE_FORMATS } from '@angular/material/core';
import { PmAuthorizationService } from 'src/app/modules/project-management/services/pm-authorization.service';

@Component({
  selector: 'app-opportunity-project-creation',
  templateUrl: './opportunity-project-creation.component.html',
  styleUrls: ['./opportunity-project-creation.component.scss'],
  providers: [
    {
      provide: DateAdapter,
      useClass: MomentDateAdapter,
      deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS]
    },
    {
      provide: MAT_DATE_FORMATS,
      useValue: {
        parse: {
          dateInput: "DD-MMM-YYYY"
        },
        display: {
          dateInput: "DD-MMM-YYYY",
          monthYearLabel: "MMM YYYY"
        }
      }
    }]

})
export class OpportunityProjectCreationComponent implements OnInit {

  formConfig: any;
  sectionList: any = [
    {
      id: 1,
      key_name: 'quote',
      label: 'Quote Details',
      is_active: true
    },
    {
      id: 2,
      key_name: 'enterprise',
      label: 'Enterprise Structure',
      is_active: true
    },
    {
      id: 3,
      key_name: 'finance',
      label: 'Financial',
      is_active: true
    },
    {
      id: 4,
      key_name: 'project',
      label: 'Project Details',
      is_active: true
    }

  ];
  projectCreationFormGroup = this.formBuilder.group({
    opportunity_id: [''],
    opportunity_name: [''],
    customer_id: [''],
    customer_name: [''],
    quote_id: [''],
    quote_value: [''],
    quote_currency: [''],

    project_code: [''],
    project_name: [''],
    project_start_date: [''],
    project_end_date: [''],
    service_type: [''],

    portfolio: [''],
    child_customer: [''],
    financial_region: [''],
    sow_reference_number: [''],
    sow_owner: [''],
    risk: [''],
    po_number: [''],
    order_value: [''],
    po_reference: [''],
    po_date: [''],
    payment_terms: ['']
  });
  isComponentLoading: boolean = false;
  projectImage: string;
  project_name_length: number;
  cards: any = [];
  selectedOption: string | null = null;
  radioChecked: boolean = false;
  toggleOpportunityChecked: boolean = false;
  list: any = [
    {
      id: 1,
      name: 'Portfolio 1'
    },
    {
      id: 2,
      name: 'Portfolio 2'
    }
  ];
  withOpportunity: boolean = true;
  risk: boolean = true;
  standard: boolean = false;
  toggleSecuredChecked: boolean = false;
  disableCode: boolean;
  code_length: number;
  defaultValue: boolean = true;
  formValid: boolean;
  codeDuplicated: boolean;
  refresh: boolean;
  empty: boolean = true
  disableColor: any;
  TandM_selected: boolean = false;
  changeInServiceType: boolean = false;
  save_disabled: boolean = false;
  payment_list: any[] = [];
  portfolio_list: any[] = [];
  customer_list: any[] = [];
  legal_entity_list: any[] = [];
  p_and_l_list: any[] = [];
  poNonMandate: boolean = true;
  currencyList: any = [];
  opportunityDetails: any;
  retrieveMessages: any = [];
  dateLogic: boolean;
  font_size_currency: any = '11px'
  code: any = 'USD';
  private subs = new SubSink();
  disableOpportunitySearch:boolean = false;
  stakeholders:any=[]
  customer_stakeholders:any=[]
  external_stakeholders:any=[]
  crm_internal_allocation:boolean=false
  currency_list:any=[]
  crm_external_allocation:boolean=false
  isSowReferenceNumberEmpty: boolean = true;
  isSowReferenceNumberRefresh: boolean = false;
  SowReferenceNumberDuplicated: boolean = false;
  sowReferenceNumberValid:boolean = false;
  invalidSowReferenceNumberLength = false;
  sowReferenceNumberConfig: any;
  sowInvalidLengthMsg: any;
  sowRefDuplicatedMsg: any;
  projectRoleAccess: any;
  
  constructor(private formBuilder: FormBuilder, private pmMasterService: PmMasterService, public dialog: MatDialog, public dialogRef: MatDialogRef<OpportunityProjectCreationComponent>,
    @Inject(MAT_DIALOG_DATA) public dialogData: any,
    private utilityService: UtilityService,
    private ProjectCreationService: ProjectCreationService,
    private toasterService: ToasterMessageService,
    private pmAuthService: PmAuthorizationService
  ) { }

  async ngOnInit() {
    document.documentElement.style.setProperty('--primary', '#FFFFFF');
    document.documentElement.style.setProperty('--secondary', '#FFFFFF');
    document.documentElement.style.setProperty('--button', '#f5f5f5')
    this.isComponentLoading = true;
    await this.pmMasterService.getPMFormCustomizeConfigV().then((res) => {
      this.formConfig = res
    })
    await this.pmMasterService.currencyList().then((res) => {
      this.currency_list = res
    })

    await this.pmAuthService.getProjectRoleAccess().then((res)=>{
      this.projectRoleAccess= res
    })
    console.log(this.dialogData)
    await this.customizeThemes();
    await this.customizeFields();
    await this.fetchingListMasterData();
    const crm_resource = _.where(this.formConfig, { type: "opportunity-project-creation", field_name: "CRM_resource_add", is_active: true })
    if(crm_resource.length>0){
      this.crm_internal_allocation=true
    }
    else{
      this.crm_external_allocation=true
    }
    if(this.dialogData && this.dialogData.opportunityId){
      this.projectCreationFormGroup.get('opportunity_id').patchValue(this.dialogData.opportunityId);
      console.log('Opportunity Search Disable:',this.disableOpportunitySearch);
      this.disableOpportunitySearch = true;
      this.opportunityDetails=await this.getProjectOpportunityData()
    }
    await this.prePatchProjectFormValues();
    this.isComponentLoading = false;
    // await this.handleChanges();
    this.projectCreationFormGroup.get('project_code').valueChanges.subscribe(async (res) => {
      console.log(res)
      if (this.projectCreationFormGroup.get('project_code').value.length > 0) {
        console.log(res)
        if (res) {
          // this.projectCreationFormGroup.patchValue({ ['profit_center']: res })
          this.empty = false
          this.refresh = true
          await this.ProjectCreationService.checkProjectCodeDuplication(res).then((res: any) => {
            if (res) {
              this.codeDuplicated = res['data'];
              if (this.codeDuplicated == true) {
                this.formValid = false;
              }
              else {
                this.formValid = true;
              }
              this.refresh = false;
              if (res == '' && res == null) {
                this.refresh = true;
              }
            }
          })
        }
      }
      else {
        this.empty = true
        this.formValid = false
      }
    })

    this.sowReferenceNumberConfig = _.where(this.formConfig, { field_name: "sow_reference_number", type: "project-creation", is_active: true });
    this.sowRefDuplicatedMsg = this.retrieveMessages[0]?.errors?.sow_ref_duplicated ? this.retrieveMessages[0].errors.sow_ref_duplicated : 'SOW Reference Number already exists';
    this.sowInvalidLengthMsg = this.retrieveMessages[0]?.errors?.sow_invalid_length ? this.retrieveMessages[0].errors.sow_invalid_length : 'SOW Reference Number too short';

    this.projectCreationFormGroup.get('sow_reference_number').valueChanges.subscribe(async (res) => {
      if (this.projectCreationFormGroup.get('sow_reference_number').value.length > 0) {
        let sowReferenceNumberMinLength = this.sowReferenceNumberConfig[0]?.min_length ? this.sowReferenceNumberConfig[0]?.min_length : 8;
        this.isSowReferenceNumberRefresh = true;
        this.isSowReferenceNumberEmpty = false;
        if (res && this.projectCreationFormGroup.get('sow_reference_number').value.length >= sowReferenceNumberMinLength) {
          this.invalidSowReferenceNumberLength = false;
          await this.ProjectCreationService.checkSowReferenceNumberDuplication(res,1,null).then((res: any) => {
            if (res) {
              this.SowReferenceNumberDuplicated = res['data'];
              console.log(this.SowReferenceNumberDuplicated);
              if (this.SowReferenceNumberDuplicated == true) {
                this.sowReferenceNumberValid = false;
                console.log(this.sowReferenceNumberValid);
              }
              else {
                this.sowReferenceNumberValid = true;
              }
              this.isSowReferenceNumberRefresh = false;
              if (res == '' && res == null) {
                this.isSowReferenceNumberRefresh = true;
              }
            }
          })
        }
        if (res && this.projectCreationFormGroup.get('sow_reference_number').value.length < sowReferenceNumberMinLength) {
          this.sowReferenceNumberValid = false;
          this.invalidSowReferenceNumberLength = true;
        }
        this.isSowReferenceNumberRefresh = false;
      }
      else {
        this.isSowReferenceNumberEmpty = true
        this.sowReferenceNumberValid = false
      }
    })


if(!this.disableOpportunitySearch){
    this.projectCreationFormGroup.get('opportunity_id').valueChanges.subscribe(async (res) => {
      if (this.projectCreationFormGroup.get('opportunity_id').value) {

        if (res) {
          this.opportunityDetails = await this.getProjectOpportunityData();

          await this.prePatchProjectFormValues();
        }
      }
      else {
        this.projectCreationFormGroup.get('quote_id').patchValue("");
        this.projectCreationFormGroup.get('opportunity_name').patchValue("");
        this.projectCreationFormGroup.get('quote_value').patchValue("");
        this.projectCreationFormGroup.get('quote_currency').patchValue("");
        this.projectCreationFormGroup.get('customer_id').patchValue("");
        this.projectCreationFormGroup.get('customer_name').patchValue("");
        this.projectCreationFormGroup.get('project_code').patchValue("");
        this.projectCreationFormGroup.get('order_value').patchValue("");
        this.projectCreationFormGroup.get('project_code').patchValue("");
        this.projectCreationFormGroup.get('sow_owner').patchValue("");
        this.projectCreationFormGroup.get('po_number').patchValue("");
        this.projectCreationFormGroup.get('po_date').patchValue("");
        this.portfolio_list = []
        this.customer_list = []
        this.currencyList = []
      }
    })
  }

  }


  /**
   * @description Close Dialog
   */
  closeDialog() {
    if (this.projectCreationFormGroup.dirty) {
      this.utilityService.openConfirmationSweetAlertWithCustom("Are you sure", "You want to Close without saving").then((result) => {
        if (result) {
          this.dialogRef.close({ messType: "E" })
        }
      });
    }
    else {
      this.dialogRef.close({ messType: "E" })
    }
  }

  /**
   * @description Service Type Check Box 
   */
  async selectOption(option: string): Promise<void> {
    // console.log(option);
    if (this.selectedOption != option) {
      this.changeInServiceType = true
    }
    else {
      this.changeInServiceType = false
    }
    if (this.selectedOption != '1') {
      this.TandM_selected = false
    }
    this.selectedOption = option;
    console.log(this.selectedOption)
    this.projectCreationFormGroup.get('service_type').patchValue(this.selectedOption)
    this.radioChecked = true
  }

  /**
   * @description Opportunity Toggle
   */
  checkOpportunity() {
    console.log('toggle', this.toggleOpportunityChecked);
    // if (this.toggleOpportunityChecked) {
    //   this.checkWithOpportunity();
    // }
    // else if (!this.toggleOpportunityChecked) {
    //   this.checkWithoutOpportunity();
    // }

  }

  /**
   * @description Check Secured or Risk 
   */
  checkSecuredOrRisk() {

    this.toggleSecuredChecked = !this.toggleSecuredChecked;

    if (this.toggleSecuredChecked) {
      this.standard = true
      this.risk = false
      this.poNonMandate = false;
      this.projectCreationFormGroup.get('risk').patchValue(0)
      this.projectCreationFormGroup.get('po_number').patchValue(this.opportunityDetails.po_number);
    }
    else if (!this.toggleSecuredChecked) {
      this.standard = false
      this.risk = true
      this.poNonMandate = true;
      this.projectCreationFormGroup.get('risk').patchValue(1);
      
      let patchPONumber = _.findWhere(this.formConfig, { type: "opportunity-project-creation", field_name: "patch_po_number_risk", is_active: true })

      if(patchPONumber)
        this.projectCreationFormGroup.get('po_number').patchValue(this.opportunityDetails.po_number);
      
    }
  }

  /**
   * @description validating Project Code
   */
  validateProjectCode(event: Event) {
    this.defaultValue = false
    const inputValue = (event.target as HTMLInputElement).value;
    const patternStrings = _.where(this.formConfig, { type: "opportunity-project-creation", field_name: "project_code", is_active: true });

    const patternRegex = patternStrings[0].pattern;
    const patternParts = patternRegex.split('/');
    const pattern1 = new RegExp(patternParts[1], patternParts[2]);

    //const pattern = /[^0-9A-Za-z]/gi;
    //const sanitizedValue = inputValue.replace(pattern1, '').toUpperCase();
    const sanitizedValue = inputValue.replace(pattern1, '')

    // Check if portfolio code is duplicated
    if (this.codeDuplicated) {
      // Mark the portfolio code as invalid
      this.projectCreationFormGroup.get('project_code').setErrors({ duplicated: true });
      this.formValid = false;
    } else {
      this.projectCreationFormGroup.get('project_code').setValue(sanitizedValue);
      this.formValid = this.projectCreationFormGroup.get('project_code').valid;
    }
  }

  /**
   * @description Check Box for T&M Project
   */
  selectCheckBox(event: Event) {
    console.log(this.TandM_selected)
  }

  /**
   * @description Create Project 
   */
  async saveRequest() {
    this.save_disabled = true;
    let mandateNotEmpty = this.checkMandateNotEmpty(this.projectCreationFormGroup.value);
    // console.log(this.projectCreationFormGroup.value);
    if (mandateNotEmpty && this.formValid) {
      let value = this.projectCreationFormGroup.value;
      let portfolio_result = _.where(this.portfolio_list, { id: value.portfolio })
      let portfolio_name
      if (portfolio_result.length > 0) {
        portfolio_name = portfolio_result[0]['p_name']
      }
      await this.ProjectCreationService.getResourceFromAccounts(this.opportunityDetails.customer_id).then((res: any) => {
        console.log(res)
        this.customer_stakeholders = res['data']
      if(this.crm_internal_allocation){
        if (this.customer_stakeholders && this.customer_stakeholders.length > 0) {
          this.stakeholders = _.filter(this.stakeholders, (res) => {
            if (!res['isAccount']) {
              return res;
            }
          });


          for (let data of this.customer_stakeholders) {
            this.stakeholders.push({ employee_name: data['aid'], start_date: this.projectCreationFormGroup.get('project_start_date').value, end_date: this.projectCreationFormGroup.get('project_end_date').value, role: data['project_role_id'], split: 0, isChecked: false, isHead: false, isAccount: true, mail_sent: false })
          }      
        }
      }
      if(this.crm_external_allocation){
        if (this.customer_stakeholders && this.customer_stakeholders.length > 0) {
          for (let data of this.customer_stakeholders) {
            this.external_stakeholders.push({ project_id: '',
              project_item_id:'',
              start_date:'',
              end_date:'',
              associate_id:data['aid'],
              email_id:'',
              external_name:'',
              oid:data['aid'],
              project_role_id:data['project_role_id'] })
          }
        }

      }
      })

      let opportunityISA = _.where(this.projectRoleAccess, {flow_opportunity_isa: 1})

      if(opportunityISA.length>0)
      {

        await this.ProjectCreationService.getResourceFromOpportunity(value.opportunity_id).then((res: any)=>{
            if(!res['err'])
            {
              console.log("opportunity stakeholders", res)
               
                for(let opportunity of opportunityISA)
                {
                    let opportunity_column_name = opportunity['opportunity_column_name']
                    let project_role_id = opportunity['project_role_id']

                    let opportunityStakeholders = _.where(res['data'],{owner_type: opportunity_column_name})

                    
                    let checkInternalExternal = _.where(this.pmMasterService.projectRoleMasterList,{id: project_role_id})

                    if(checkInternalExternal.length>0)
                    {
                        for(let data of opportunityStakeholders)
                        {
                          if(checkInternalExternal[0]['role_type']=="Internal")
                          {
                            this.stakeholders.push({ employee_name: data['id'], start_date: this.projectCreationFormGroup.get('project_start_date').value, end_date: this.projectCreationFormGroup.get('project_end_date').value, role: project_role_id, split: 0, isChecked: false, isHead: false, isAccount: true, mail_sent: false })
                          }
                          else
                          {
                            this.external_stakeholders.push({ project_id: '',
                              project_item_id:'',
                              start_date:'',
                              end_date:'',
                              associate_id:data['id'],
                              email_id:'',
                              external_name:'',
                              oid:data['id'],
                              project_role_id:project_role_id })
                          }
                          
                        }
                    }

                }
            }
        })
      }
      let currency=this.currency_list.find(item => item.name === this.opportunityDetails.currency)
      console.log(currency)
      let opc_payload = {
        opportunity_id: value.opportunity_id,
        opportunity_name: value.opportunity_name,
        quote_value: value.order_value,
        quote_currency: value.quote_currency,
        customer_id: value.customer_id,
        customer_name: value.customer_name,
        project_code: value.project_code,
        project_name: value.project_name,
        project_start_date: value.project_start_date,
        project_end_date: value.project_end_date,
        service_type: value.service_type,
        is_blanket_po: this.TandM_selected ? 1 : 0,
        portfolio: value.portfolio,
        child_customer: value.child_customer,
        financial_region: value.financial_region,
        sow_owner: value.sow_owner,
        sow_reference_number: value.sow_reference_number,
        at_risk: value.risk,
        with_opportunity: this.withOpportunity ? 1 : 0,
        po_number: value.po_number,
        order_value: value.order_value,
        po_reference: value.po_reference,
        payment_terms: value.payment_terms,
        po_date: value.po_date,
        currency: currency.id,
        quote_id: value.quote_id,
        code: this.code,
      }
      let save = await this.createProject(opc_payload,this.stakeholders,this.external_stakeholders);
      if (save && save.messType == 'S') {
        const saveSuccessMsg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].success.save_success ? this.retrieveMessages[0].success.save_success : 'Project Created successfully' : 'Project Created successfully';
        this.toasterService.showSuccess(saveSuccessMsg, 10000);

        opc_payload['portfolio'] = save['portfolio_id']
        this.dialogRef.close({ messType: "S", "project_id": save['data'], "data": opc_payload, "portfolio_name": portfolio_name })
      }
      else {
        const saveUnsuccessMsg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.save_unsuccess ? this.retrieveMessages[0].errors.save_unsuccess : 'Error while creating Project' : 'Error while creating Project';
        this.toasterService.showError(saveUnsuccessMsg);
      }
    }
    this.save_disabled = false;
  }

  /**
   * @description Themes based on tenant
   */
  customizeThemes() {
    let primary: string = '#FFFFFF';
    let secondary: string = '#f5f5f5'
    document.documentElement.style.setProperty('--primary', primary);
    document.documentElement.style.setProperty('--secondary', secondary);
    let retrieveStyles = _.where(this.formConfig, { type: "project-theme", field_name: "styles", is_active: true });
    this.projectImage = retrieveStyles.length > 0 ? retrieveStyles[0].data.project_image ? retrieveStyles[0].data.project_image : "https://assets.kebs.app/MicrosoftTeams-image%20(16).png" : "https://assets.kebs.app/MicrosoftTeams-image%20(16).png";
    let fontStyle = retrieveStyles.length > 0 ? retrieveStyles[0].data.font_style ? retrieveStyles[0].data.font_style : "Roboto" : "Roboto";
    document.documentElement.style.setProperty('--projectFont', fontStyle);
    this.disableColor = retrieveStyles.length > 0 ? retrieveStyles[0].data.disabled_color ? retrieveStyles[0].data.disabled_color : "#E8E9EE !important" : "#E8E9EE !important";
    let button = retrieveStyles.length > 0 ? retrieveStyles[0].data.button_color ? retrieveStyles[0].data.button_color : "#90ee90" : "#90ee90";
    let shades = retrieveStyles.length > 0 ? retrieveStyles[0].data.shades_color ? retrieveStyles[0].data.shades_color : "#C9E3B4" : "#C9E3B4";
    let fieldOutline = retrieveStyles.length > 0 ? retrieveStyles[0].data.field_outline_color ? retrieveStyles[0].data.field_outline_color : "#808080" : "#808080";
    document.documentElement.style.setProperty('--shades', shades)
    document.documentElement.style.setProperty('--button', button)
    document.documentElement.style.setProperty('--projectFieldOutline', fieldOutline)
    let scrollColor = retrieveStyles.length > 0 ? retrieveStyles[0].data.scroll_color ? retrieveStyles[0].data.scroll_color : "#90ee90" : "#90ee90";
    document.documentElement.style.setProperty('--project2projectScroll', scrollColor)
  }

  /**
   * @description Customizing form Field configurations
   */
  customizeFields() {
    let projectCode_config = _.where(this.formConfig, { type: "opportunity-project-creation", field_name: "project_code", is_active: true });
    if (projectCode_config.length > 0) {
      this.disableCode = projectCode_config[0].disable;
      this.code_length = projectCode_config.length > 0 ? projectCode_config[0].length ? projectCode_config[0].length : 20 : 20;
    }
    let retrieveNameLength = _.where(this.formConfig, { type: "opportunity-project-creation", field_name: "project_name", is_active: true });
    this.project_name_length = retrieveNameLength.length > 0 ? retrieveNameLength[0].maxLength ? retrieveNameLength[0].maxLength : 300 : 300;
    this.retrieveMessages = _.where(this.formConfig, { type: "opportunity-project-creation", field_name: "messages", is_active: true });
  }

  /**
   * @description  Value Changes
   */
  handleChanges() {
    this.projectCreationFormGroup.get('project_code').valueChanges.subscribe(async (res) => {
      if (this.projectCreationFormGroup.get('project_code').value.length > 0) {
        // console.log(res)
        if (res) {
          // this.projectCreationFormGroup.patchValue({ ['profit_center']: res })
          this.empty = false
          this.refresh = true
          await this.ProjectCreationService.checkProjectCodeDuplication(res).then((res: any) => {
            if (res) {
              this.codeDuplicated = res['data'];
              if (this.codeDuplicated == true) {
                this.formValid = false;
              }
              else {
                this.formValid = true;
              }
              this.refresh = false;
              if (res == '' && res == null) {
                this.refresh = true;
              }
            }
          })
        }
      }
      else {
        this.empty = true
        this.formValid = false
      }
    })

    this.projectCreationFormGroup.get('opportunity_id').valueChanges.subscribe(async (res) => {
      if (this.projectCreationFormGroup.get('opportunity_id').value) {

        if (res) {
          this.opportunityDetails = await this.getProjectOpportunityData();

          await this.prePatchProjectFormValues();
        }
      }
      else {
        this.projectCreationFormGroup.get('quote_id').patchValue("");
        this.projectCreationFormGroup.get('opportunity_name').patchValue("");
        this.projectCreationFormGroup.get('quote_value').patchValue("");
        this.projectCreationFormGroup.get('quote_currency').patchValue("");
        this.projectCreationFormGroup.get('customer_id').patchValue("");
        this.projectCreationFormGroup.get('customer_name').patchValue("");
        this.projectCreationFormGroup.get('project_code').patchValue("");
        this.projectCreationFormGroup.get('order_value').patchValue("");
        this.projectCreationFormGroup.get('project_code').patchValue("");
        this.projectCreationFormGroup.get('sow_owner').patchValue("");
        this.projectCreationFormGroup.get('po_number').patchValue("");
        this.projectCreationFormGroup.get('po_date').patchValue("");
        this.portfolio_list = []
        this.customer_list = []
        this.currencyList = []
      }
    })
  }

  /**
   * @description Pre Patching ProjectForm Values
   */
  async prePatchProjectFormValues() {
    let risk = this.toggleSecuredChecked ? 0 : 1
    this.projectCreationFormGroup.get('risk').patchValue(risk)
    console.log("Opportunity Details", this.opportunityDetails)
    if (this.opportunityDetails) {

      let currency_value
      if (this.opportunityDetails.quote_value.length > 0) {
        this.currencyList = this.opportunityDetails.quote_value;

        currency_value = this.currencyList.find(item => item.currency_code === this.opportunityDetails.currency);

      }
      if (currency_value !== undefined) {
        this.projectCreationFormGroup.get('quote_value').patchValue(currency_value.value);
        this.projectCreationFormGroup.get('quote_currency').patchValue(this.opportunityDetails.currency);
        this.projectCreationFormGroup.get('order_value').patchValue(currency_value.value);
      } else {
        this.projectCreationFormGroup.get('quote_value').patchValue('');
        this.projectCreationFormGroup.get('quote_currency').patchValue('');
        this.projectCreationFormGroup.get('order_value').patchValue('');
      }

      this.projectCreationFormGroup.get('quote_id').patchValue(this.opportunityDetails.quote_id);
      this.projectCreationFormGroup.get('opportunity_name').patchValue(this.opportunityDetails.opportunity_name);
      this.projectCreationFormGroup.get('customer_name').patchValue(this.opportunityDetails.customer_name);
      this.projectCreationFormGroup.get('customer_id').patchValue(this.opportunityDetails.customer_id);
      let projectCode = await this.getProjectCode(this.opportunityDetails.customer_id);
      this.projectCreationFormGroup.get('project_code').patchValue(projectCode);
      this.portfolio_list = await this.getPortfolioList(this.opportunityDetails.customer_id);
      if(this.portfolio_list.length==1){
        this.projectCreationFormGroup.get('portfolio').patchValue(this.portfolio_list[0].id)
      }
      let risk_marking = _.findWhere(this.formConfig,{type:"opportunity-project-creation", field_name:"risk_marking", is_active: true})

      if(risk_marking)
      {
          if(this.opportunityDetails.po_number!=null && this.opportunityDetails.po_number!="" && this.opportunityDetails.po_number!=" " && this.opportunityDetails.po_number!="-" && this.opportunityDetails.po_number!="null")
          {
              this.checkSecuredOrRisk()
          }
      }
      else
      {
        this.projectCreationFormGroup.get('po_number').patchValue(this.opportunityDetails.po_number);
      }

      
      this.customer_list = await this.getChildCustomerList(this.opportunityDetails.customer_id);
      this.projectCreationFormGroup.get('sow_owner').patchValue(this.opportunityDetails.legal_entity);  
      this.projectCreationFormGroup.get('po_date').patchValue(this.opportunityDetails.po_date);
      this.projectCreationFormGroup.get('service_type').patchValue(this.opportunityDetails.service_type);
      this.projectCreationFormGroup.get('payment_terms').patchValue(this.opportunityDetails.payment_terms);
      this.projectCreationFormGroup.get('project_start_date').patchValue(this.opportunityDetails.deliveryStartDate);
      this.projectCreationFormGroup.get('project_end_date').patchValue(this.opportunityDetails.deliveryFinishDate);
      this.projectCreationFormGroup.get('project_name').patchValue(this.opportunityDetails.opportunity_name);
      this.projectCreationFormGroup.get('financial_region').patchValue(this.opportunityDetails.sales_region);
      this.selectedOption=this.opportunityDetails.service_type
     if(this.customer_list.length==0){
      this.customer_list.push({id:this.opportunityDetails.customer_id,name:this.opportunityDetails.customer_name})
      this.projectCreationFormGroup.get('child_customer').patchValue(this.customer_list[0].id);
     }
     if (this.projectCreationFormGroup.get('project_code').value.length > 0) {
      console.log(projectCode)
      if (projectCode) {
        // this.projectCreationFormGroup.patchValue({ ['profit_center']: res })
        this.empty = false
        this.refresh = true
        await this.ProjectCreationService.checkProjectCodeDuplication(projectCode).then((res: any) => {
          if (res) {
            this.codeDuplicated = res['data'];
            if (this.codeDuplicated == true) {
              this.formValid = false;
            }
            else {
              this.formValid = true;
            }
            this.refresh = false;
            if (res == '' && res == null) {
              this.refresh = true;
            }
          }
        })
      }
    }
    else {
      this.empty = true
      this.formValid = false
    }
    }
    else {
      this.projectCreationFormGroup.get('quote_id').patchValue("");
      this.projectCreationFormGroup.get('opportunity_name').patchValue("");
      this.projectCreationFormGroup.get('quote_value').patchValue("");
      this.projectCreationFormGroup.get('quote_currency').patchValue("");
      this.projectCreationFormGroup.get('customer_id').patchValue("");
      this.projectCreationFormGroup.get('customer_name').patchValue("");
      this.projectCreationFormGroup.get('project_code').patchValue("");
      this.projectCreationFormGroup.get('order_value').patchValue("");
      this.projectCreationFormGroup.get('sow_owner').patchValue("");
      this.projectCreationFormGroup.get('po_number').patchValue("");
      this.projectCreationFormGroup.get('po_date').patchValue("");
    }

  }

  /**
   * @description Checking PO number mandate
   */
  getPONumberDisabledCheck() {
    if (this.risk) {
      let patchPONumber = _.findWhere(this.formConfig, { type: "opportunity-project-creation", field_name: "patch_po_number_risk", is_active: true })

      if(patchPONumber)
        this.projectCreationFormGroup.get('po_number').patchValue(this.opportunityDetails.po_number);
      else
        this.projectCreationFormGroup.patchValue({ 'po_number': '' })

    }
    return this.risk ? true : false
  }

  /**
   * @description getting disabled field color
   */
  getDisabledColor() {
    return this.risk || this.withOpportunity ? '#E8E9EE' : '#FFFFFF'
  }

  /**
   * @description fetching Master data
   */
  async fetchingListMasterData() {
    this.cards = this.pmMasterService.service_type_list;
    await this.pmMasterService.getPaymentTermsList().then((res: any) => {
      if (res['messType'] == "S") {
        this.payment_list = res['data'];
      }

    });
    await this.pmMasterService.getEntityList().then((res: any) => {
      if (res) {
        this.legal_entity_list = this.pmMasterService.entity_list;
      }
    });
    this.p_and_l_list = this.pmMasterService.p_and_l_list
  }


  /**
   * @description get project opportunity data
   */
  getProjectOpportunityData(): Promise<any> {
    let opportunity_id = this.projectCreationFormGroup.get('opportunity_id').value;
    return new Promise((resolve) => {
      this.subs.sink = this.ProjectCreationService.getProjectOpportunityDetails(opportunity_id).subscribe(
        (res: any) => {
          if (res.messType == 'S') {
            resolve(res.data);
          } else {
            resolve(null)
          }
        },
        (err) => {
          console.log(err);
          resolve(null)
        }
      );
    });
  }

  /**
   * @description get project Code
   */
  getProjectCode(customer_id): Promise<any> {

    return new Promise((resolve) => {
      this.subs.sink = this.ProjectCreationService.getProjectCodeForCustomer(customer_id).subscribe(
        (res: any) => {
          if (res.messType == 'S') {
            resolve(res.data);
          } else {
            resolve(null)
          }
        },
        (err) => {
          console.log(err);
          resolve(null)
        }
      );
    });
  }

  /**
   * @description get portfolio List
   */
  getPortfolioList(customer_id): Promise<any> {

    return new Promise((resolve) => {
      this.subs.sink = this.ProjectCreationService.getCustomerPortfolioList(customer_id).subscribe(
        (res: any) => {
          if (res.messType == 'S') {
            resolve(res.data);
          } else {
            resolve(null)
          }
        },
        (err) => {
          console.log(err);
          resolve(null)
        }
      );
    });
  }

  /**
   * @description get Child Customer List
   */
  getChildCustomerList(customer_id): Promise<any> {

    return new Promise((resolve) => {
      this.subs.sink = this.ProjectCreationService.getProjectChildCustomerList(customer_id).subscribe(
        (res: any) => {
          if (res.messType == 'S') {
            resolve(res.data);
          } else {
            resolve(null)
          }
        },
        (err) => {
          console.log(err);
          resolve(null)
        }
      );
    });
  }

  /**
   * @description Check Mandateor not 
   */
  isMandate(field: any) {
    const mandate = _.where(this.formConfig, { type: "opportunity-project-creation", field_name: field, is_active: true });
    if (mandate.length > 0) {
      const isMandate = mandate[0].is_mandant;
      return isMandate;
    }
  }

  /**
   * @description Checking Mandatory Fields
   */
  checkMandateNotEmpty(data: any) {

    let errorOccurred = false;
    if ((data.opportunity_id === null || data.opportunity_id === undefined || data.opportunity_id === '') && this.isMandate('opportunity_id')) {
      let emptyMsg = this.retrieveMessages[0].errors ? this.retrieveMessages[0].errors.opportunityId_empty ? this.retrieveMessages[0].errors.opportunityId_empty : 'Opportunity Id is Mandatory.' : 'Opportunity is Mandatory.';
      this.toasterService.showWarning(emptyMsg, 10000)
      errorOccurred = true;
    }
    else if ((!data.opportunity_name || data.opportunity_name.trim() === '') && this.isMandate('opportunity_name')) {
      let emptyMsg = this.retrieveMessages[0].errors ? this.retrieveMessages[0].errors.opportunityName_empty ? this.retrieveMessages[0].errors.opportunityName_empty : 'Opportunity Name is Mandatory.' : 'Opportunity Name Mandatory.';
      this.toasterService.showWarning(emptyMsg, 10000)
      errorOccurred = true;
    }

    else if ((!data.customer_name || data.customer_name.trim() === '') && this.isMandate('customer_name')) {
      let emptyMsg = this.retrieveMessages[0].errors ? this.retrieveMessages[0].errors.customerName_empty ? this.retrieveMessages[0].errors.customerName_empty : 'Customer Name is Mandatory.' : 'Customer Name Mandatory.';
      this.toasterService.showWarning(emptyMsg, 10000)
      errorOccurred = true;
    }

    else if ((data.quote_id === null || data.quote_id === undefined || data.quote_id === '') && this.isMandate('quote_id')) {
      let emptyMsg = this.retrieveMessages[0].errors ? this.retrieveMessages[0].errors.quoteId_empty ? this.retrieveMessages[0].errors.quoteId_empty : 'Quote Id is Mandatory.' : 'Quote Id is Mandatory.';
      this.toasterService.showWarning(emptyMsg, 10000)
      errorOccurred = true;
    }

    else if ((data.quote_value === null || data.quote_value === undefined || data.quote_value === '') && this.isMandate('quote_value')) {
      let emptyMsg = this.retrieveMessages[0].errors ? this.retrieveMessages[0].errors.quoteValue_empty ? this.retrieveMessages[0].errors.quoteValue_empty : 'Quote Value is Mandatory.' : 'Quote Value is Mandatory.';
      this.toasterService.showWarning(emptyMsg, 10000)
      errorOccurred = true;
    }

    else if ((data.portfolio === null || data.portfolio === undefined || data.portfolio === '') && this.isMandate('portfolio')) {
      let emptyMsg = this.retrieveMessages[0].errors ? this.retrieveMessages[0].errors.portfolioEmpty ? this.retrieveMessages[0].errors.portfolioEmpty : 'Portfolio is Mandatory.' : 'Portfolio is Mandatory.';
      this.toasterService.showWarning(emptyMsg, 10000)
      errorOccurred = true;
    }

    else if ((data.child_customer === null || data.child_customer === undefined || data.child_customer === '') && this.isMandate('child_customer')) {
      let emptyMsg = this.retrieveMessages[0].errors ? this.retrieveMessages[0].errors.childCustomerEmpty ? this.retrieveMessages[0].errors.childCustomerEmpty : 'Customer is Mandatory.' : 'Customer is Mandatory.';
      this.toasterService.showWarning(emptyMsg, 10000)
      errorOccurred = true;
    }

    else if ((data.financial_region === null || data.financial_region === undefined || data.financial_region === '') && this.isMandate('financial_region')) {
      let emptyMsg = this.retrieveMessages[0].errors ? this.retrieveMessages[0].errors.financialRegionEmpty ? this.retrieveMessages[0].errors.financialRegionEmpty : 'Financial Region is Mandatory.' : 'Financial Region is Mandatory.';
      this.toasterService.showWarning(emptyMsg, 10000)
      errorOccurred = true;
    }

    else if ((data.sow_reference_number === null || data.sow_reference_number === undefined || data.sow_reference_number === '') && this.isMandate('sow_reference_number')) {
      let emptyMsg = this.retrieveMessages[0].errors ? this.retrieveMessages[0].errors.sowReferenceEmpty ? this.retrieveMessages[0].errors.sowReferenceEmpty : 'SOW Reference Number is Mandatory.' : 'SOW Reference Number is Mandatory.';
      this.toasterService.showWarning(emptyMsg, 10000)
      errorOccurred = true;
    }

    else if (data.sow_reference_number){
      if(this.isSowReferenceNumberRefresh){
        this.toasterService.showWarning("Please wait while SOW reference number is being verified.", 10000);
        errorOccurred = true;
      }
      if(!this.sowReferenceNumberValid && this.invalidSowReferenceNumberLength){
        const sow_invalid_length_msg = this.retrieveMessages[0]?.errors?.sow_invalid_length ? this.retrieveMessages[0].errors.sow_invalid_length : 'SOW Reference Number too short';
        this.toasterService.showWarning(sow_invalid_length_msg, 10000);
        errorOccurred = true;
      }
      if(!this.sowReferenceNumberValid && this.SowReferenceNumberDuplicated){
        const sow_ref_duplicated_msg = this.retrieveMessages[0]?.errors?.sow_ref_duplicated ? this.retrieveMessages[0].errors.sow_ref_duplicated : 'SOW Reference Number already exists';
        this.toasterService.showWarning(sow_ref_duplicated_msg, 10000);
        errorOccurred = true;
      }
    }

    else if ((data.sow_owner === null || data.sow_owner === undefined || data.sow_owner === '') && this.isMandate('sow_owner')) {
      let emptyMsg = this.retrieveMessages[0].errors ? this.retrieveMessages[0].errors.sowOwnerEmpty ? this.retrieveMessages[0].errors.sowOwnerEmpty : 'SOW Owner is Mandatory.' : 'SOW Owner is Mandatory.';
      this.toasterService.showWarning(emptyMsg, 10000)
      errorOccurred = true;
    }

    else if ((data.po_number === null || data.po_number === undefined || data.po_number === '') && !this.poNonMandate) {
      let emptyMsg = this.retrieveMessages[0].errors ? this.retrieveMessages[0].errors.poNumberEmpty ? this.retrieveMessages[0].errors.poNumberEmpty : 'PO Number is Mandatory.' : 'PO Number is Mandatory.';
      this.toasterService.showWarning(emptyMsg, 10000)
      errorOccurred = true;
    }

    else if ((data.order_value === null || data.order_value === undefined || data.order_value === '') && this.isMandate('order_value')) {
      let emptyMsg = this.retrieveMessages[0].errors ? this.retrieveMessages[0].errors.orderValueEmpty ? this.retrieveMessages[0].errors.orderValueEmpty : 'Order Value is Mandatory.' : 'Order Value is Mandatory.';
      this.toasterService.showWarning(emptyMsg, 10000)
      errorOccurred = true;
    }

    else if ((data.po_reference === null || data.po_reference === undefined || data.po_reference === '') && this.isMandate('po_reference')) {
      let emptyMsg = this.retrieveMessages[0].errors ? this.retrieveMessages[0].errors.poReferenceEmpty ? this.retrieveMessages[0].errors.poReferenceEmpty : 'PO Reference is Mandatory.' : 'PO Reference is Mandatory.';
      this.toasterService.showWarning(emptyMsg, 10000)
      errorOccurred = true;
    }

    else if ((data.po_date === null || data.po_date === undefined || data.po_date === '' || data.po_date == 'Invalid date') && this.isMandate('po_date')) {
      let emptyMsg = this.retrieveMessages[0].errors ? this.retrieveMessages[0].errors.poDateEmpty ? this.retrieveMessages[0].errors.poDateEmpty : 'PO Date is Mandatory.' : 'PO Date is Mandatory.';
      this.toasterService.showWarning(emptyMsg, 10000)
      errorOccurred = true;
    }

    else if ((data.payment_terms === null || data.payment_terms === undefined || data.payment_terms === '') && this.isMandate('payment_terms')) {
      let emptyMsg = this.retrieveMessages[0].errors ? this.retrieveMessages[0].errors.paymentTermsEmpty ? this.retrieveMessages[0].errors.paymentTermsEmpty : 'Payment Terms is Mandatory.' : 'Payment Terms is Mandatory.';
      this.toasterService.showWarning(emptyMsg, 10000)
      errorOccurred = true;
    }

    else if ((!data.project_code || data.project_code.trim() === '') && this.isMandate('project_code')) {
      let emptyMsg = this.retrieveMessages[0].errors ? this.retrieveMessages[0].errors.projectCodeEmpty ? this.retrieveMessages[0].errors.projectCodeEmpty : 'Project Code is Mandatory.' : 'Project Code is Mandatory.';
      this.toasterService.showWarning(emptyMsg, 10000)
      errorOccurred = true;
    }

    else if ((!data.project_name || data.project_name.trim() === '') && this.isMandate('project_name')) {
      let emptyMsg = this.retrieveMessages[0].errors ? this.retrieveMessages[0].errors.projectNameEmpty ? this.retrieveMessages[0].errors.projectNameEmpty : 'Project Name is Mandatory.' : 'Project Name is Mandatory.';
      this.toasterService.showWarning(emptyMsg, 10000)
      errorOccurred = true;
    }

    else if ((data.project_start_date === null || data.project_start_date === undefined || data.project_start_date === '' || data.project_start_date == 'Invalid date') && this.isMandate('project_start_date')) {
      let emptyMsg = this.retrieveMessages[0].errors ? this.retrieveMessages[0].errors.projectStartDateEmpty ? this.retrieveMessages[0].errors.projectStartDateEmpty : 'Project Start Date is Mandatory.' : 'Project Start Date is Mandatory.';
      this.toasterService.showWarning(emptyMsg, 10000)
      errorOccurred = true;

    }

    else if ((data.project_end_date === null || data.project_end_date === undefined || data.project_end_date === '' || data.project_end_date == 'Invalid date') && this.isMandate('project_end_date')) {
      let emptyMsg = this.retrieveMessages[0].errors ? this.retrieveMessages[0].errors.projectEndDateEmpty ? this.retrieveMessages[0].errors.projectEndDateEmpty : 'Project End Date is Mandatory.' : 'Project End Date is Mandatory.';
      this.toasterService.showWarning(emptyMsg, 10000)
      errorOccurred = true;
    }

    this.checkDates(data.project_start_date, data.project_end_date);
    if (!this.dateLogic && !errorOccurred) {
  
      errorOccurred = true;
      const valid_date_err = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.valid_date_err ? this.retrieveMessages[0].errors.valid_date_err : 'Kindly enter valid date' : 'Kindly enter valid date';
      this.toasterService.showWarning(valid_date_err, 10000)
    }
   
    else if ((data.service_type === null || data.service_type === undefined || data.service_type === '') && this.isMandate('service_type')) {
      let emptyMsg = this.retrieveMessages[0].errors ? this.retrieveMessages[0].errors.serviceTypeEmpty ? this.retrieveMessages[0].errors.serviceTypeEmpty : 'Service Type is Mandatory.' : 'Service Type is Mandatory.';
      this.toasterService.showWarning(emptyMsg, 10000)
      errorOccurred = true;
    }

    if (errorOccurred) {

      return false;
    } else {

      return true;
    }
  }
  /**
   * @description Checking Valid Date
   */
  checkDates(start: any, end: any) {

    const startDate = moment(start);
    const endDate = moment(end);

    if (startDate.isValid() && endDate.isValid()) {
      if (startDate.isBefore(endDate) || startDate.isSame(endDate)) {
        this.dateLogic = true;
      }
      else {
        this.dateLogic = false;
      }

    } else {
      this.dateLogic = false;
      //this.toastr.warning("Invalid Date Format", 'Warning');
    }
  }


  /**
   * @description Save the Project details
   */
  createProject(params,stakeholders,external_stakeholders): Promise<any> {

    return new Promise((resolve) => {
      this.subs.sink = this.ProjectCreationService.saveOpportunityProjectCreation(params,stakeholders,external_stakeholders).subscribe(
        (res: any) => {
          if (res.messType == 'S') {
            resolve(res);
          } else {
            resolve(null)
          }
        },
        (err) => {
          console.log(err);
          resolve(null)
        }
      );
    });
  }
}
export interface DialogData {
  opportunityId: number
}