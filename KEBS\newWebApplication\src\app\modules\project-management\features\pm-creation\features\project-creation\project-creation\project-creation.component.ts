import { Component, OnInit, Renderer2, <PERSON>Child, Output, HostListener, ElementRef, Inject } from '@angular/core';
import { FormArray, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { PmMasterService } from 'src/app/modules/project-management/services/pm-master.service';
import { startWith, debounceTime, distinctUntilChanged, map } from 'rxjs/operators';
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog';
import { MomentDateAdapter, MAT_MOMENT_DATE_ADAPTER_OPTIONS } from '@angular/material-moment-adapter';
import { DateAdapter, MAT_DATE_LOCALE, MAT_DATE_FORMATS } from '@angular/material/core';
import { Observable } from 'rxjs';
import { EventEmitter } from '@angular/core';
import * as _ from 'underscore';
import { MatButtonToggleGroup } from '@angular/material/button-toggle';
import { TagComponent } from 'src/app/modules/project-management/shared-lazy-loaded/components/tag/tag.component';
import { v4 as uuidv4 } from 'uuid';
import { LoginService } from "src/app/services/login/login.service";
import { UtilityService } from 'src/app/services/utility/utility.service';
import { ProjectService } from 'src/app/modules/projects/services/project.service';
import { MatStepper } from '@angular/material/stepper';
import { ThrowStmt } from '@angular/compiler';
import { ProjectCreationService } from '../services/project-creation.service'
import { ToastrService } from 'ngx-toastr';
import moment from 'moment';
import { MAT_AUTOCOMPLETE_SCROLL_STRATEGY } from '@angular/material/autocomplete';
import { ScrollStrategy } from '@angular/cdk/overlay';
import { Overlay } from '@angular/cdk/overlay';
import { ToasterService } from 'src/app/services/toaster-service/toaster.service';
import { ToasterMessageService } from 'src/app/modules/project-management/shared-lazy-loaded/components/toaster-message/toaster-message.service';
import { active } from 'd3';
@Component({
  selector: 'app-project-creation',
  templateUrl: './project-creation.component.html',
  styleUrls: ['./project-creation.component.scss'],
  providers: [
    {
      provide: DateAdapter,
      useClass: MomentDateAdapter,
      deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS]
    },
    {
      provide: MAT_DATE_FORMATS,
      useValue: {
        parse: {
          dateInput: "DD-MMM-YYYY"
        },
        display: {
          dateInput: "DD-MMM-YYYY",
          monthYearLabel: "MMM YYYY"
        }
      }
    },
    {
      provide: MAT_AUTOCOMPLETE_SCROLL_STRATEGY,
      useFactory: customAutocompleteScrollStrategyFactory,
      deps: [Overlay],
    },
  ]
})
export class ProjectCreationComponent implements OnInit {
  @ViewChild('weekGroup', { static: true }) weekGroup: MatButtonToggleGroup;
  @Output() closeSideNav = new EventEmitter();
  selectedValues: string[] = [];
  @Output() closeDialog: EventEmitter<void> = new EventEmitter<void>();
  @ViewChild('templateDiv') templateDiv: ElementRef;
  projectService: any;
  @HostListener('window:keyup.esc') onKeyUp() {
    this.onCloseClick()
  }
  toggleOpportunityChecked: boolean = false;
  toggleSecuredChecked: boolean = false;
  public poForm: FormGroup;
  project_code: any;
  data: any;
  formConfig: any;
  portfolio_list: any[] = []
  invoice_template_list: any[] = []
  work_location_list: any[] = []
  from_list: any[] = []
  to_list: any[] = []
  opportunity_list: any[] = []
  child_opportunity_list: any[]=[];
  holiday_calendar_list: any[] = []
  project_type_list: any[] = []
  selectedTimeFrom: Date = new Date();
  selectedTimeTo: Date = new Date();
  selectedType: any
  selectedOption: string | null = null;
  currentUser: any
  aid: any
  radioChecked: boolean = false
  InternalServiceType:boolean=true;
  peopleWidth = '710px'
  invite_project_manager: any = []
  mode: any;
  ESdata: any;
  refresh: boolean;
  valid: boolean;
  codeDuplicated: boolean;
  empty: boolean = true
  changeInServiceType: boolean = false
  customer: any = '-'
  portfolio_name: any = '-'
  customer_id: any
  selectedTemplate: any = '';
  templateChosen: boolean = false;

  entityList: any = [];
  subDivisionList: any = [];
  divisionList: any = [];
  displayList: any = [];
  people_list: any = []
  ad_group_list: any[] = []
  person_date_list: any[] = []
  person_role_list: any[] = []
  person_split_list: any[] = []
  currency_list: any[] = []
  revenue_type_list: any=[];
  delivery_type_list: any=[];
  product_category_list : any=[];
  split_percentage_lower_percentage: number = 1;
  restriction:boolean=false
  subDivision: any;
  division: any;
  entity: any;

  selectedData: any = []
  selectedIndex: any;
  selectedEntity: any;
  taskControl = [];
  headerDesc = "";
  searchText;
  isChecked: boolean = false;
  isHead: boolean = false;
  //billableColor: string = 'warn'
  // isSelected: boolean = false;
  poepleCount: any = 1
  financeFieldDisable: boolean = false
  risk: boolean = false
  code_length: any
  name_length: any;
  mailEnable: boolean = false;
  defaultValue: boolean = true
  cards: any = []
  isMinimized = false; // Track whether the dialog is minimized or not
  originalWidth = '1016px'; // Set the original width of the dialog
  originalHeight = '574px';
  loading: boolean = false;
  button: string = '#EE4961';
  primary: string = '#FFFFFF';
  secondary: string = '#f5f5f5'
  financialValue: any
  customer_stakeholders: any
  scheduleFormGroup: FormGroup;
  peopleValue: boolean;
  dateLogic: boolean;
  retrieveMessages: any;
  people_name: any;
  start_date_notEmpty: any;
  end_date_notEmpty: any;
  p_and_l_list: any;
  country_list: any;
  poNonMandate: boolean = false;
  shades: any;
  fontStyle: any;
  payment_list: any[] = [];
  childOpportunityFlag: boolean=false;
  yes_or_no_list: any=[
    {
      "id":"1",
      "name":"Yes"
    },
    {
      "id":"0",
      "name":"No"
    }
  ]
  stepperFormGroup = this.formBuilder.group({
    project_code: [''],
    project_name: [''],
    portfolio: [''],
    startDate: [''],
    endDate: [''],
    quote: [''],
    project_type: [''],
    from: [''],
    to: [''],
    people: [''],
    ad_group: [''],
    person_date: [''],
    person_role: [''],
    person_split: [''],
    resourceStartDate: [''],
    resourceEndDate: [''],
    currency: [''],
    opportunity: [''],
    employee_name: [''],
    customer_details: [''],
    entity: [''],
    division: [''],
    sub_division: [''],
    description: [''],
    textInputControl: [''],
    monthly_hours: [''],
    daily_working_hours:[''],
    leave_paid:[''],
    holiday_paid:[''],
    profit_center: [''],
    p_and_l: [''],
    legal_entity: [''],
    // location_name:[''],
    // work_location:[''],
    // calendar:[''],
    financial: new FormArray([]),
    fieldsArray: this.formBuilder.array([this.createFieldGroup()]),
    reason: [''],
    adaptTemplate: '',
    sow_reference_number: [''],
    child_customer:[''],
    customer_id:[''],
    delivery_type:[''],
    revenue_type:[''],
    product_category:['']
  });
  isPopupVisible: boolean = false
  add: boolean = true
  tags: any
  allStepperData: any = [{
    id: 1,
    data: "1",
    type: "details",
    label: "Project Details",
    is_selected: true,
    is_crossed: false,
    is_completed: false,
    is_active: true
  },
  {
    id: 2,
    data: "2",
    type: "es",
    label: "Enterprise Structure",
    is_selected: false,
    is_crossed: false,
    is_completed: false,
    is_active: true
  },
  {
    id: 3,
    data: "3",
    type: "financial",
    label: "Financial",
    is_selected: false,
    is_crossed: false,
    is_completed: false,
    is_active: true
  },
  {
    id: 4,
    data: "4",
    type: "people",
    label: "People",
    is_selected: false,
    is_crossed: false,
    is_completed: false,
    is_active: true
  },
  {
    id: 5,
    data: "5",
    type: "schedule",
    label: "Schedule",
    is_selected: false,
    is_crossed: false,
    is_completed: false,
    is_active: true
  },
  {
    id: 6,
    data: "6",
    type: "template",
    label: "Template",
    is_selected: false,
    is_crossed: false,
    is_completed: false,
    is_active: true
  },
  {
    id: 7,
    data: "7",
    type: "advance",
    label: "Advance Options",
    is_selected: false,
    is_crossed: false,
    is_completed: false,
    is_active: true
  }
  ];
  stepperData: any = []
  stepper_color: any;
  stepper_font: any;
  detailStepper: boolean = true;
  public orderValue: number
  MakeFieldsNonMandatory:boolean=true;
  enterpriseStepper: boolean = false;
  financialStepper: boolean = false;
  peopleStepper: boolean = false;
  templateStepper: boolean = false;
  scheduleStepper: boolean = false;
  advanceStepper: boolean = false;
  check: boolean = false
  displayDivision: boolean = false
  displaySubDivision: boolean = false
  displayEsList: boolean = false
  Fromminutes: any
  Fromhours: any
  Tominutes: any
  Tohours: any
  monday: boolean = true
  tuesday: boolean = true
  wednesday: boolean = true
  thursday: boolean = true
  friday: boolean = true
  saturday: boolean = false
  sunday: boolean = false
  portfolio_start: any
  portfolio_end: any
  week_array = []
  detailsSkip: boolean = false
  esSkip: boolean = false
  financialSkip: boolean = false
  scheduleSkip: boolean = false
  templateSkip: boolean = false
  peopleSkip: boolean = false
  detailsLast: boolean = false
  esLast: boolean = false
  financialLast: boolean = false
  scheduleLast: boolean = false
  templateLast: boolean = false
  peopleLast: boolean = false
  tagLast: boolean = false
  project_code_list: any
  buildFromScratch: boolean = true
  useATemplate: boolean = false
  opentemplate: boolean = false
  notMatch: boolean = true
  template_master_list: any
  template_master_list_search: any
  legal_entity_list: any;
  Filterdata: any
  displayTemplate: boolean = true
  opportunity_status_id: any
  project_type_disable = true;
  project_type_color: any
  default_currency: number
  project_type_default: number
  save_disabled: boolean = false
  DOJ: any
  role_disable: boolean = false
  existingTag: any = [];
  fields: any[] = [
    { workLocation: '', location: '', calendar: '', country_id: '' }
  ];
  stakeholders: any = []
  code: any
  unique_id_2: any
  modeForTag: any = 'Create'
  application_id: any = 2
  quote_id: any
  profit_center_disable = true;
  profit_center_color: any
  standard: boolean = true
  multipleQuote: boolean = true
  opportunity_status: any
  withoutOpportunity: boolean = false
  withOpportunity: boolean = true
  reason_list: any = []
  formarray: any
  opportunity_status_list: any
  currency_code: any
  added_opportunity_list: any = []
  gantt_type_id: any = 2;
  customer_list: any[] = [];
  selectedTemplateName: any;
  projectImage: any;
  intergeration: number
  TandM_selected:boolean=false
  creation_mode: any;
  duplicate_data: any;
  disableColor: any;
  disableCode: any;
  fieldOutline: any;
  scrollColor: any;
  default_week: any = [];
  from_default: any;
  to_default: any;
  daily_working_hours_default: any;
  monthly_hours_default: any;
  leave_paid_default: any;
  holiday_paid_default: any;
  location_default: any;
  country_default: any;
  holiday_calendar_default: any;
  child_customer:boolean=false
  child_customer_list:any=[]
  crm_external_allocation:boolean=false
  crm_internal_allocation:boolean=false
  external_stakeholders:any=[]
  po_value_digit:number=15
  account_details_autopatch:boolean = false
  isSowReferenceNumberEmpty: boolean = true;
  isSowReferenceNumberRefresh: boolean = false;
  SowReferenceNumberDuplicated: boolean = false;
  sowReferenceNumberValid:boolean = false;
  invalidSowReferenceNumberLength = false;
  sowReferenceNumberConfig: any;
  sowInvalidLengthMsg: any;
  sowRefDuplicatedMsg: any;
  constructor(public renderer: Renderer2,private fb: FormBuilder,
    private formBuilder: FormBuilder,
    private pmMasterService: PmMasterService,
    private toastr: ToastrService,
    private toasterService: ToasterMessageService,
    public dialog: MatDialog, private loginService: LoginService, private utilityService: UtilityService,
    private ProjectCreationService: ProjectCreationService,
    public dialogRef: MatDialogRef<ProjectCreationComponent>,
    @Inject(MAT_DIALOG_DATA) public dialogData: DialogData) { 
      
    }

  async ngOnInit() {

    this.creation_mode = this.dialogData ? (this.dialogData.mode ? this.dialogData.mode : "Creation") : "Creation" ;
    this.duplicate_data = this.dialogData ? (this.dialogData.data ? this.dialogData.data : undefined ) : undefined;


    this.selectedTemplate = '';
    document.documentElement.style.setProperty('--primary', this.primary);
    document.documentElement.style.setProperty('--secondary', this.secondary);
    this.loading = true;

    
    this.country_list = this.pmMasterService.country_list;
    await this.pmMasterService.getPMFormCustomizeConfigV().then((res) => {
      this.formConfig = res
    })
    const projectCode_config = _.where(this.formConfig, { type: "project-creation", field_name: "project_code"});
    if(projectCode_config.length > 0){
        this.disableCode = projectCode_config[0].disable;
    }
    const retrieveStyles = _.where(this.formConfig, { type: "project-theme", field_name: "styles", is_active: true });
    this.button = retrieveStyles.length > 0 ? retrieveStyles[0].data.button_color ? retrieveStyles[0].data.button_color : "#90ee90" : "#90ee90";
    this.shades = retrieveStyles.length > 0 ? retrieveStyles[0].data.shades_color ? retrieveStyles[0].data.shades_color : "#C9E3B4" : "#C9E3B4";
    this.projectImage = retrieveStyles.length > 0 ? retrieveStyles[0].data.project_image ? retrieveStyles[0].data.project_image : "https://assets.kebs.app/MicrosoftTeams-image%20(16).png" : "https://assets.kebs.app/MicrosoftTeams-image%20(16).png";
    this.disableColor = retrieveStyles.length > 0 ? retrieveStyles[0].data.disabled_color ? retrieveStyles[0].data.disabled_color : "#E8E9EE !important" : "#E8E9EE !important";
    this.fieldOutline = retrieveStyles.length > 0 ? retrieveStyles[0].data.field_outline_color ? retrieveStyles[0].data.field_outline_color : "#808080" : "#808080";
    document.documentElement.style.setProperty('--shades', this.shades)
    document.documentElement.style.setProperty('--button', this.button)
    document.documentElement.style.setProperty('--projectFieldOutline', this.fieldOutline)
    this.fontStyle = retrieveStyles.length > 0 ? retrieveStyles[0].data.font_style ? retrieveStyles[0].data.font_style : "Roboto" : "Roboto";
    document.documentElement.style.setProperty('--projectFont', this.fontStyle);
    this.scrollColor = retrieveStyles.length > 0 ? retrieveStyles[0].data.scroll_color ? retrieveStyles[0].data.scroll_color : "#90ee90" : "#90ee90";
    document.documentElement.style.setProperty('--project2projectScroll', this.scrollColor)

    let split_percentage_lower_percentage = _.findWhere(this.formConfig, { type: "add-member", field_name: 'split_percentage_lower_percentage', is_active: true })
    this.split_percentage_lower_percentage = split_percentage_lower_percentage ? split_percentage_lower_percentage['label'] : 1

    const weekConfig = _.where(this.formConfig, { type: "project-creation", field_name: "week"});
    if(weekConfig.length > 0){
      this.default_week = weekConfig[0].default_value ? weekConfig[0].default_value : [1, 2, 3, 4, 5];
    }

    const fromTime = _.where(this.formConfig, { type: "project-creation", field_name: "from"});
    if(fromTime.length > 0){
      this.from_default = fromTime[0].default_value ? fromTime[0].default_value : '9.00';
    }

    const toTime = _.where(this.formConfig, { type: "project-creation", field_name: "to"});
    if(toTime.length > 0){
      this.to_default = toTime[0].default_value ? toTime[0].default_value : '18.00';
    }

    const dailyWorkingHours = _.where(this.formConfig, { type: "project-creation", field_name: "daily_working_hours"});
    if(dailyWorkingHours.length > 0){
      this.daily_working_hours_default = dailyWorkingHours[0].default_value ? dailyWorkingHours[0].default_value : 8;
    }

    const monthlyHours = _.where(this.formConfig, { type: "project-creation", field_name: "monthly_hours"});
    if(monthlyHours.length > 0){
      this.monthly_hours_default = monthlyHours[0].default_value ? monthlyHours[0].default_value : 168;
    }

    const leavePaid = _.where(this.formConfig, { type: "project-creation", field_name: "leave_paid"});
    if(leavePaid.length > 0){
      this.leave_paid_default = leavePaid[0].default_value ? leavePaid[0].default_value : 0;
    }

    const holidayPaid = _.where(this.formConfig, { type: "project-creation", field_name: "holiday_paid"});
    if(holidayPaid.length > 0){
      this.holiday_paid_default = holidayPaid[0].default_value ? holidayPaid[0].default_value : 0;
    }

    const locationConfig = _.where(this.formConfig, { type: "project-creation", field_name: "work_location"});
    if(locationConfig.length > 0){
      this.location_default = locationConfig[0].default_value ? locationConfig[0].default_value : 0;
    }

    const countryConfig = _.where(this.formConfig, { type: "project-creation", field_name: "location"});
    if(countryConfig.length > 0){
      this.country_default = countryConfig[0].default_value ? countryConfig[0].default_value : 0;
    }
    const holidayCalendar = _.where(this.formConfig, { type: "project-creation", field_name: "holiday_calendar"});
    if(holidayCalendar.length > 0){
      this.holiday_calendar_default = holidayCalendar[0].default_value ? holidayCalendar[0].default_value : 0;}
   const restriction=_.where(this.formConfig, { type: "po_date", field_name: "restriction",is_active:true});
    if(restriction.length > 0){
      this.restriction = true
    }
    const po_value_digit = _.where(this.formConfig, { type: "project-creation", field_name: "value_digit"});
    if(po_value_digit.length > 0){
      this.po_value_digit = po_value_digit[0].default_value ? po_value_digit[0].default_value : 15;
    }
    for(let field of this.fields){
      field.workLocation = this.location_default;
      field.country_id = this.country_default; 
      field.calendar = this.holiday_calendar_default;
    }
    const customerConfig = _.where(this.formConfig, { type: "project-creation", field_name: "child_customer",is_active:1});
    if(customerConfig.length > 0){
      this.child_customer = true;
    }
    else{
      this.child_customer = true;
    }
    const account_details_autopatch = _.where(this.formConfig, { type: "project-creation", field_name: "account_details_autopatch",is_active:true});
    if(account_details_autopatch.length > 0){
      this.account_details_autopatch = true;
    }
    else{
      this.account_details_autopatch = false;
    }

    const checkChildOpportunity = _.where(this.formConfig, { type: "project-creation", field_name: "child_opportunity",is_active:true});
    if(checkChildOpportunity.length > 0){
      this.childOpportunityFlag  = true
    }
    else{
      this.childOpportunityFlag = false;
    }
    await this.pmMasterService.getCustomerList().then((res: any) => {
      this.customer_list = this.pmMasterService.customer_list;
    });

    await this.pmMasterService.getPaymentTermsList().then((res: any) => {
      if (res['messType'] == "S") {
        this.payment_list = res['data'];
      }

    });

    
    await this.pmMasterService.getProductCategory().then((res: any)=>{
     
      if (res['messType'] == "S") {
        this.product_category_list = res['data'];
      }
      
    })

    await this.pmMasterService.getDeliveryTypeList().then((res: any)=>{
      if(res['messType']=="S"){
        this.delivery_type_list = res['data']
      }
    })

    await this.pmMasterService.getRevenueTypeList().then((res: any)=>{
      if(res['messType']=="S"){
        this.revenue_type_list = res['data']
      }
    })
    //opportunity and risk toggle config handling
    const opportunityToggle = _.where(this.formConfig, { type: "project-creation", field_name: "opportunity_toggle" });
    const riskToggle = _.where(this.formConfig, { type: "project-creation", field_name: "at_risk_toggle" });
    console.log('toggle', opportunityToggle, riskToggle);
    if (opportunityToggle.length > 0) {
      if (opportunityToggle[0].default == 'withoutOpportunity') {
        this.checkWithoutOpportunity();
      }
      else if (opportunityToggle[0].default == 'withOpportunity') {
        this.checkWithOpportunity();
      }
    }

    if (riskToggle.length > 0) {
      if (riskToggle[0].default == 'standard') {
        this.checkStandard();
      }
      else if (riskToggle[0].default == 'risk') {
        this.checkAtRisk();
      }
    }

    this.p_and_l_list = this.pmMasterService.p_and_l_list
    this.reason_list = this.pmMasterService.reason_list
    if (this.reason_list.length > 0) {
      console.log('REASON', this.reason_list, this.reason_list[0].id)
      this.stepperFormGroup.patchValue({
        reason: this.reason_list[0].id || ''
      });
    }
    const status = _.where(this.formConfig, { type: "project-creation", field_name: "opportunity", is_active: true });
    this.opportunity_status_list = status.length > 0 ? status[0].status_list : [26, 27, 28]
    const details = _.where(this.formConfig, { type: "project-creation", field_name: "default_stepper", is_active: true });
    this.stepperData = details.length > 0 ? _.where(details[0].values, { is_active: true }) : this.allStepperData;
    this.stepper_color = details.length > 0 ? details[0].color ? details[0].color : "#90ee90" : "#90ee90";
    this.stepper_font = details.length > 0 ? details[0].font ? details[0].font : "#90ee90" : "#90ee90";
    document.documentElement.style.setProperty('--stepperColor', this.stepper_color)
    console.log(this.stepperData)
    this.retrieveMessages = _.where(this.formConfig, { type: "project-creation", field_name: "messages", is_active: true });
    // const ES = _.where(this.formConfig, { type: "project-creation", field_name: "enterprise_structure", is_active: true });
    // this.allStepperData[1].label = ES.length > 0 ? ES[0].label : "Enterprise Structure";
    // const finance = _.where(this.formConfig, { type: "project-creation", field_name: "financial", is_active: true });
    // this.allStepperData[2].label = finance.length > 0 ? finance[0].label : "Financial";
    // const people = _.where(this.formConfig, { type: "project-creation", field_name: "people", is_active: true });
    // this.allStepperData[2].label = people.length > 0 ? people[0].label : "People";
    // const schedule = _.where(this.formConfig, { type: "project-creation", field_name: "schedule", is_active: true });
    // this.allStepperData[2].label = schedule.length > 0 ? schedule[0].label : "Schedule";
    // const template = _.where(this.formConfig, { type: "project-creation", field_name: "template", is_active: true });
    // this.allStepperData[2].label = template.length > 0 ? template[0].label : "Template";
    // const advance = _.where(this.formConfig, { type: "project-creation", field_name: "advance_options", is_active: true });
    // this.allStepperData[2].label = advance.length > 0 ? advance[0].label : "Advance Options";
    // this.stepperData = _.filter(this.allStepperData,{'is_active': true});
    //this.stepper_color = "#EE4961";
    // this.findLastStep();
    this.currentUser = this.loginService.getProfile().profile
    this.aid = this.currentUser['aid']
    console.log(this.currentUser)
    console.log(this.stepperFormGroup.get('project_code').value.length)

    console.log("inside")
    const retrieveCodeLength = _.where(this.formConfig, { type: "project-creation", field_name: "project_code", is_active: true });
    this.code_length = retrieveCodeLength.length > 0 ? retrieveCodeLength[0].length : 20;
    const retrieveNameLength = _.where(this.formConfig, { type: "project-creation", field_name: "project_name", is_active: true });
    this.name_length = retrieveNameLength.length > 0 ? retrieveNameLength[0].maxLength : 300;
    const retreiveMailNotifi = _.where(this.formConfig, { type: "project-creation", field_name: "people_notification", is_active: true });
    this.mailEnable = retreiveMailNotifi.length > 0 ? retreiveMailNotifi[0].mailEnable : false;
    this.stepperFormGroup.get('project_code').valueChanges.subscribe(async (res) => {
      if (this.stepperFormGroup.get('project_code').value.length > 0) {
        console.log(res)
        if (res) {
          this.stepperFormGroup.patchValue({ ['profit_center']: res })
          this.empty = false
          this.refresh = true
          await this.ProjectCreationService.checkProjectCodeDuplication(res).then((res: any) => {
            if (res) {
              this.codeDuplicated = res['data'];
              console.log(this.codeDuplicated);
              if (this.codeDuplicated == true) {
                this.valid = false;
                console.log(this.valid);
              }
              else {
                this.valid = true;
              }
              this.refresh = false;
              if (res == '' && res == null) {
                this.refresh = true;
              }
            }
          })
        }
      }
      else {
        this.empty = true
        this.valid = false
      }
    })
    await this.ProjectCreationService.getPortfolioList().then(async (res: any) => {
      console.log(res)
      this.portfolio_list = res['data']
    })
    const type = _.where(this.formConfig, { type: "project-creation", field_name: "project_type", is_active: true })
    this.project_type_disable = type.length > 0 ? type[0].disable : true
    this.project_type_color = type.length > 0 ? type[0].color : '#E8E9EE'
    this.project_type_default = type.length > 0 ? type[0].default : 1
    const profit_center = _.where(this.formConfig, { type: "project-creation", field_name: "profit_center", is_active: true })
    this.profit_center_disable = profit_center.length > 0 ? profit_center[0].disable : true
    this.profit_center_color = profit_center.length > 0 ? profit_center[0].color : '#E8E9EE'
    this.invoice_template_list = this.pmMasterService.invoice_template_list
    this.cards = this.pmMasterService.service_type_list
    console.log(this.cards)
    this.holiday_calendar_list = this.pmMasterService.holiday_calendar_list;
    console.log(this.holiday_calendar_list)
    this.project_type_list = this.pmMasterService.project_type_list
    this.person_role_list = this.pmMasterService.person_role_list
    this.currency_list = this.pmMasterService.currency_list
    this.work_location_list = this.pmMasterService.work_location_list
    this.template_master_list = this.pmMasterService.template_master_list
    this.template_master_list_search = this.pmMasterService.template_master_list
    console.log(this.template_master_list)
    const currency = _.where(this.formConfig, { type: "project-creation", field_name: "currency", is_active: true })
    this.default_currency = currency.length > 0 ? currency[0].default : 2
    this.currency_code = currency.length > 0 ? currency[0].default_name : 'USD'
    this.ad_group_list = [{ id: 1, name: "Group 1" }, { id: 2, name: "Group 2" }, { id: 3, name: "Group 3" }]
    const intg = _.where(this.formConfig, { type: "project-creation", field_name: "outbound_intg", is_active: true })
    this.intergeration = intg.length > 0 ? intg[0].intergerate : 0
    if (this.project_type_default > 0) {
      this.stepperFormGroup.patchValue({ [`project_type`]: this.project_type_default })
    }
    else {
      this.stepperFormGroup.patchValue({ [`project_type`]: '' })
    }
    this.stepperFormGroup.patchValue({ ['currency']: this.default_currency })
    this.invite_project_manager = _.where(this.formConfig, { type: "project-creation", field_name: "invite_project_manager", is_active: true });
    const crm_resource = _.where(this.formConfig, { type: "project-creation", field_name: "CRM_resource_add", is_active: true })
    if(crm_resource.length>0){
      this.crm_internal_allocation=true
    }
    else{
      this.crm_external_allocation=true
    }
    this.stepperFormGroup.get('portfolio').valueChanges.subscribe(async (res) => {
      console.log(res)
      if (res) {

        let portfolio_result = _.where(this.portfolio_list,{id: res})

        if(portfolio_result.length>0)
        {
          this.portfolio_name = portfolio_result[0]['p_name']
          this.customer = portfolio_result[0]['customer_name']
          this.customer_id = portfolio_result[0]['end_customer_id']
          this.portfolio_start =portfolio_result[0]['planned_start_date']
          this.portfolio_end = portfolio_result[0]['planned_end_date']
          this.stepperFormGroup.patchValue({ [`customer_details`]: portfolio_result[0]['customer_name'] || '' })
        }
        
        console.log("Customer"+this.customer_id)
        if(this.account_details_autopatch){
          let account_data=_.where(this.customer_list,{id:this.customer_id})
          this.stepperFormGroup.patchValue({"legal_entity": account_data[0].legal_entity})
          this.stepperFormGroup.patchValue({"payment_terms": account_data[0].paymentTerms})
        }
        if(this.disableCode){
        this.ProjectCreationService.getCustomerProjectCode(this.customer_id).then((res)=>{
          if(res['messType']=="S")
          {
              this.stepperFormGroup.patchValue({"project_code": res['data']})
          }
        })
      }
      if(this.child_customer){
            this.child_customer_list = _.filter(this.customer_list, (customer) => parseInt(customer.parent_account) == this.customer_id);
            console.log(this.child_customer_list)
            if(this.child_customer_list.length==0){
               this.child_customer_list.push({id:this.customer_id,name:this.customer})
               this.stepperFormGroup.patchValue({child_customer:this.customer_id})
            }
       }
       

        await this.ProjectCreationService.getResourceFromAccounts(this.customer_id).then((res: any) => {
          console.log(res)
          this.customer_stakeholders = res['data']
          // if (this.customer_stakeholders.length == 1) {
          //   console.log(this.customer_stakeholders)
          //   this.stakeholders[0].employee_name = this.customer_stakeholders[0].aid
          //   this.stakeholders[0].role = this.customer_stakeholders[0].project_role_id
          //   this.stakeholders[0].isChecked = this.customer_stakeholders[0].isBillable
          //   console.log(this.stakeholders)
          // }
          // else if (this.customer_stakeholders.length > 0) {
          //   for (let i = 1; i < this.customer_stakeholders.length; i++) {
          //     this.stakeholders.push({ employee_name: '', start_date: '', end_date: '', role: '', split: '', isChecked: false, isHead: false })
          //   }
          //   for (let j = 1; j < this.customer_stakeholders.length; j++) {
          //     this.stakeholders[j].employee_name = this.customer_stakeholders[j].aid
          //     this.stakeholders[j].role = this.customer_stakeholders[j].project_role_id
          //     this.stakeholders[j].isChecked = this.customer_stakeholders[j].isBillable
          //   }
          // }
          // else {
          //   if (this.stakeholders.length == 1) {
          //     this.stakeholders[0].employee_name = ''
          //     if (invite_project_manager.length > 0) {
          //       // this.stakeholders[0].role=1
          //       this.stakeholders[0].role = 1
          //       this.role_disable = true
          //     }
          //     else {
          //       this.stakeholders[0].role = ''
          //       this.role_disable = false
          //     }
          //     this.stakeholders[0].isChecked = false
          //   }
          //   else if (this.stakeholders.length > 0) {
          //     for (let j = 1; j < this.stakeholders.length.length; j++) {
          //       this.stakeholders[j].employee_name = ''
          //       this.stakeholders[j].role = ''
          //       this.stakeholders[j].isChecked = false
          //     }
          //   }
          // }
        if(this.crm_internal_allocation){
          if (this.customer_stakeholders && this.customer_stakeholders.length > 0) {
            this.stakeholders = _.filter(this.stakeholders, (res) => {
              if (!res['isAccount']) {
                return res;
              }
            });


            for (let data of this.customer_stakeholders) {
              this.stakeholders.push({ employee_name: data['aid'], start_date: this.stepperFormGroup.get('startDate').value, end_date: this.stepperFormGroup.get('endDate').value, role: data['project_role_id'], split: 0, isChecked: false, isHead: false, isAccount: true, mail_sent: false })
            }

            if (this.stakeholders.length == 0) {

              if (this.invite_project_manager.length > 0) {
                this.stakeholders.push({ employee_name: '', start_date: '', end_date: '', role: 1, split: '', isChecked: false, isHead: false, isAccount: false, mail_sent: false })
                this.role_disable = true
              }
              else {
                this.stakeholders.push({ employee_name: '', start_date: '', end_date: '', role: '', split: '', isChecked: false, isHead: false, isAccount: false, mail_sent: false })
                this.role_disable = false
              }
            }
          }
          else {
            this.stakeholders = _.filter(this.stakeholders, (res) => {
              if (!res['isAccount']) {
                return res;
              }
            });

            if (this.stakeholders.length == 0) {
              if (this.invite_project_manager.length > 0) {
                this.stakeholders.push({ employee_name: '', start_date: '', end_date: '', role: 1, split: '', isChecked: false, isHead: false, isAccount: false, mail_sent: false })
                this.role_disable = true
              }
              else {
                this.stakeholders.push({ employee_name: '', start_date: '', end_date: '', role: '', split: '', isChecked: false, isHead: false, isAccount: false, mail_sent: false })
                this.role_disable = false
              }
            }
          }
        }
        if(this.crm_external_allocation){
          if (this.customer_stakeholders && this.customer_stakeholders.length > 0) {
            for (let data of this.customer_stakeholders) {
              this.external_stakeholders.push({ project_id: '',
                project_item_id:'',
                start_date:'',
                end_date:'',
                associate_id:data['aid'],
                email_id:'',
                external_name:'',
                oid:data['aid'],
                project_role_id:data['project_role_id'] })
            }
          }

        }
        })
        // await this.ProjectCreationService.getBillingAddress(res).then((res) => {
        //   console.log(res)
        //   if (res['messType'] == 'S') {
        //     this.risk = false
        //   }
        //   else {
        //     this.risk = true
        //   }
        // })

        await this.ProjectCreationService.getOpportunity(this.customer_id, this.opportunity_status_list, "create").then((res) => {
          if (res['messType'] == 'S') {
            this.opportunity_list = res['data']
          }
        })
      }
      else {
        this.customer = '-'
        this.portfolio_name = '-'
      }
      this.poepleCount = this.stakeholders.length
    })

    this.stepperFormGroup.get('customer_id').valueChanges.subscribe(async(res)=>{
      if(res){
        this.customer_id =res;
        if(this.account_details_autopatch){
          let account_data=_.where(this.customer_list,{id:res})
          this.stepperFormGroup.patchValue({"legal_entity": account_data[0].legal_entity})
          this.stepperFormGroup.patchValue({"payment_terms": account_data[0].paymentTerms})
        }
        if(this.disableCode){
        this.ProjectCreationService.getCustomerProjectCode(res).then((res)=>{
          if(res['messType']=="S")
          {
              this.stepperFormGroup.patchValue({"project_code": res['data']})
          }
        })
      }
        await this.ProjectCreationService.getResourceFromAccounts(res).then((res: any) => {
          console.log(res)
          this.customer_stakeholders = res['data']
          
        if(this.crm_internal_allocation){
          if (this.customer_stakeholders && this.customer_stakeholders.length > 0) {
            this.stakeholders = _.filter(this.stakeholders, (res) => {
              if (!res['isAccount']) {
                return res;
              }
            });


            for (let data of this.customer_stakeholders) {
              this.stakeholders.push({ employee_name: data['aid'], start_date: this.stepperFormGroup.get('startDate').value, end_date: this.stepperFormGroup.get('endDate').value, role: data['project_role_id'], split: 0, isChecked: false, isHead: false, isAccount: true, mail_sent: false })
            }

            if (this.stakeholders.length == 0) {

              if (this.invite_project_manager.length > 0) {
                this.stakeholders.push({ employee_name: '', start_date: '', end_date: '', role: 1, split: '', isChecked: false, isHead: false, isAccount: false, mail_sent: false })
                this.role_disable = true
              }
              else {
                this.stakeholders.push({ employee_name: '', start_date: '', end_date: '', role: '', split: '', isChecked: false, isHead: false, isAccount: false, mail_sent: false })
                this.role_disable = false
              }
            }
          }
          else {
            this.stakeholders = _.filter(this.stakeholders, (res) => {
              if (!res['isAccount']) {
                return res;
              }
            });

            if (this.stakeholders.length == 0) {
              if (this.invite_project_manager.length > 0) {
                this.stakeholders.push({ employee_name: '', start_date: '', end_date: '', role: 1, split: '', isChecked: false, isHead: false, isAccount: false, mail_sent: false })
                this.role_disable = true
              }
              else {
                this.stakeholders.push({ employee_name: '', start_date: '', end_date: '', role: '', split: '', isChecked: false, isHead: false, isAccount: false, mail_sent: false })
                this.role_disable = false
              }
            }
          }
        }
        if(this.crm_external_allocation){
          if (this.customer_stakeholders && this.customer_stakeholders.length > 0) {
            for (let data of this.customer_stakeholders) {
              this.external_stakeholders.push({ project_id: '',
                project_item_id:'',
                start_date:'',
                end_date:'',
                associate_id:data['aid'],
                email_id:'',
                external_name:'',
                oid:data['aid'],
                project_role_id:data['project_role_id'] })
            }
          }

        }
        })

        await this.ProjectCreationService.getAllParentOpportunity(this.customer_id).then((res)=>{
          if(!res['err'])
          {
              this.opportunity_list = res['data']
          }
        })
        // await this.ProjectCreationService.getOpportunity(this.customer_id, this.opportunity_status_list, "create").then((res) => {
        //   if (res['messType'] == 'S') {
        //     this.opportunity_list = res['data']
        //   }
        // })
        
        // await this.ProjectCreationService.getOpportunity(res, this.opportunity_status_list, "create").then((res) => {
        //   if (res['messType'] == 'S') {
        //     this.opportunity_list = res['data']
        //   }
        // })
        
      }
    })

    this.sowReferenceNumberConfig = _.where(this.formConfig, { field_name: "sow_reference_number", type: "project-creation", is_active: true });
    this.sowRefDuplicatedMsg = this.retrieveMessages[0]?.errors?.sow_ref_duplicated ? this.retrieveMessages[0].errors.sow_ref_duplicated : 'SOW Reference Number already exists';
    this.sowInvalidLengthMsg = this.retrieveMessages[0]?.errors?.sow_invalid_length ? this.retrieveMessages[0].errors.sow_invalid_length : 'SOW Reference Number too short';

    this.stepperFormGroup.get('sow_reference_number').valueChanges.subscribe(async (res) => {
      if (this.stepperFormGroup.get('sow_reference_number').value.length > 0) {
        let sowReferenceNumberMinLength = this.sowReferenceNumberConfig[0]?.min_length ? this.sowReferenceNumberConfig[0]?.min_length : 0;
        this.isSowReferenceNumberRefresh = true;
        this.isSowReferenceNumberEmpty = false;
        if (res && this.stepperFormGroup.get('sow_reference_number').value.length >= sowReferenceNumberMinLength) {
          this.invalidSowReferenceNumberLength = false;
          await this.ProjectCreationService.checkSowReferenceNumberDuplication(res,1,null).then((res: any) => {
            if (res) {
              this.SowReferenceNumberDuplicated = res['data'];
              console.log(this.SowReferenceNumberDuplicated);
              if (this.SowReferenceNumberDuplicated == true) {
                this.sowReferenceNumberValid = false;
                console.log(this.sowReferenceNumberValid);
              }
              else {
                this.sowReferenceNumberValid = true;
              }
              this.isSowReferenceNumberRefresh = false;
              if (res == '' && res == null) {
                this.isSowReferenceNumberRefresh = true;
              }
            }
          })
        }
        if (res && this.stepperFormGroup.get('sow_reference_number').value.length < sowReferenceNumberMinLength) {
          this.sowReferenceNumberValid = false;
          this.invalidSowReferenceNumberLength = true;
        }
        this.isSowReferenceNumberRefresh = false;
      }
      else {
        this.isSowReferenceNumberEmpty = true
        this.sowReferenceNumberValid = false
      }
    })
    
    await this.pmMasterService.getEntityList().then((res: any) => {
      if (res) {
        this.legal_entity_list = this.pmMasterService.entity_list;
      }
    });

    await this.ProjectCreationService.getOrgMapping().then(async (res: any) => {
      console.log(res)
      if (res['messType'] == "S") {

        this.ESdata = res['data']
        this.entityList = await this.getListValue("entity_name", "entity_id")
        this.divisionList = await this.getListValue("division_name", "division_id")
        this.subDivisionList = await this.getListValue("sub_division_name", "sub_division_id")
      }

    })
    this.displayList = this.ESdata
    if (this.mode == "edit" && this.selectedEntity) {
      let rejected_array = _.filter(this.displayList, (res) => {
        console.log(res, res['entity_id'] != this.selectedEntity['entity_id'] || res['division_id'] != this.selectedEntity['division_id'] || res['sub_division_id'] != this.selectedEntity['sub_division_id'])
        return (res['entity_id'] != this.selectedEntity['entity_id'] || res['division_id'] != this.selectedEntity['division_id'] || res['sub_division_id'] != this.selectedEntity['sub_division_id'])
      })

      let selected_array = _.filter(this.displayList, (res) => {
        return (res['entity_id'] == this.selectedEntity['entity_id'] && res['division_id'] == this.selectedEntity['division_id'] && res['sub_division_id'] == this.selectedEntity['sub_division_id'])
      })

      console.log(rejected_array)
      console.log(selected_array)
      this.displayList = [...selected_array, ...rejected_array]

      let total_result_array = selected_array.length > 0 ? _.where(this.displayList, { entity_id: selected_array[0]['entity_id'], division_id: selected_array[0]['division_id'], sub_division_id: selected_array[0]['sub_division_id'] }) : []

      this.selectedData = total_result_array.length > 0 ? total_result_array[0] : undefined;
      this.selectedIndex = selected_array.length > 0 ? _.indexOf(this.displayList, this.selectedData) : undefined;
      console.log(this.selectedIndex)


    }

    if(this.creation_mode =="Duplicate")
    {
        this.updateDuplicateProjectCreation()
    }


    this.entityList = await this.getListValue("entity_name", "entity_id")
    this.divisionList = await this.getListValue("division_name", "division_id")
    this.subDivisionList = await this.getListValue("sub_division_name", "sub_division_id")
    this.stepperFormGroup.get('opportunity').valueChanges.subscribe(async (res: any) => {
      console.log(res)
      if (res) {
        for (let items of this.opportunity_list) {
          if (res == items['id']) {
            this.opportunity_status = items['status_name']
          }
        }
        await this.ProjectCreationService.getFinancialValues(res).then((res) => {
          console.log(res)
          if (res['messType'] == 'S') {
            this.financialValue = res['data']

            this.quote_id = this.financialValue[0].quote_header_id
            console.log(this.quote_id)
            this.stepperFormGroup.patchValue({ [`quote`]: this.quote_id })
            this.financeFieldDisable = true
            if (this.financialValue.length > 0) {

              if (this.financialValue[0].quote_amount != null || this.financialValue[0].quote_amount > 0) {
                this.stepperFormGroup.patchValue({ [`po_value`]: this.financialValue[0].quote_amount || '' })

                // this.stepperFormGroup.get('quote').disable()

              }
              else {
                this.stepperFormGroup.patchValue({ ['po_value']: '' })

                // this.stepperFormGroup.get('quote').enable()

              }
            }
            else {
              this.stepperFormGroup.patchValue({ ['po_value']: '' })
              this.stepperFormGroup.get('po_value').enable()
            }
          }

        })

        // this.stepperFormGroup.patchValue({[`quote`]:  items['quote_id']|| ''})
        // this.stepperFormGroup.patchValue({[`po_value`]:  items['po_value'] || ''})
        // this.stepperFormGroup.patchValue({[`po_number`]: items['po_number'] || ''})



      }
      else {
        this.stepperFormGroup.patchValue({ ['po_value']: '' })
        this.stepperFormGroup.get('po_value').enable()
        this.stepperFormGroup.patchValue({ [`quote`]: '' })
        this.financeFieldDisable = false
      }
    })
    this.stepperFormGroup.get('quote').valueChanges.subscribe((res: any) => {
      console.log(res)
      if (res == null || res == '') {
        //  this.risk=true
      }
      else {
        // this.risk=false
      }
    })
   

    if(!this.whetherCustomerGeneratedCode())
    {
      await this.ProjectCreationService.getProjectCode().then((res: any) => {
        if (res['messType'] == 'S') {
          this.project_code_list = res['data']
        }
      })
    }
    this.addFinancial()
    this.formarray = this.stepperFormGroup.get('financial') as FormArray;
    this.loading = false;
    this.stepperFormGroup.patchValue({ ['from']: this.from_default ? this.from_default : '09:00' })
    this.stepperFormGroup.patchValue({ ['to']: this.to_default ? this.to_default : '17:00' })
    this.detailsSkip = this.mandateAvailable('details')
    this.esSkip = this.mandateAvailable('es')
    this.templateSkip = this.mandateAvailable('template')
    this.peopleSkip = this.mandateAvailable('people')
    this.scheduleSkip = this.mandateAvailable('schedule')
    this.financialSkip = this.mandateAvailable('financial')
    this.detailsLast = this.lastStepper('details')
    this.esLast = this.lastStepper('es')
    this.financialLast = this.lastStepper('financial')
    this.peopleLast = this.lastStepper('people')
    this.scheduleLast = this.lastStepper('schedule')
    this.templateLast = this.lastStepper('template')
    this.tagLast = this.lastStepper('advance')
    this.stepperFormGroup.patchValue({ ['monthly_hours']: this.monthly_hours_default ? this.monthly_hours_default : 168 })
    this.stepperFormGroup.patchValue({ ['daily_working_hours']: this.daily_working_hours_default ? this.daily_working_hours_default : 8 })
    this.stepperFormGroup.patchValue({ ['leave_paid']: this.leave_paid_default ? this.leave_paid_default : 0 })
    this.stepperFormGroup.patchValue({ ['holiday_paid']: this.holiday_paid_default ? this.holiday_paid_default : 0 })

    this.intializeProjectCode()

    this.stepperFormGroup.get('entity').valueChanges.subscribe(async (res: any) => {
      if (this.stepperFormGroup.get('entity').value != null) {
        this.displaySubDivision = true
        this.displayDivision = true
        this.displayEsList = true
        if (this.check == false) {
          this.entity = this.stepperFormGroup.get('entity').value == null ? undefined : this.stepperFormGroup.get('entity').value
          this.division = this.stepperFormGroup.get('division').value == null ? undefined : this.stepperFormGroup.get('division').value
          this.subDivision = this.stepperFormGroup.get('sub_division').value == null ? undefined : this.stepperFormGroup.get('sub_division').value
          let val = {}
          console.log(this.subDivision, this.division, this.entity)
          if (this.subDivision) {
            val['sub_division_id'] = this.subDivision
          }
          if (this.division) {
            val['division_id'] = this.division
          }
          if (this.entity) {
            val['entity_id'] = this.entity
          }
          console.log(val)
          this.displayList = _.where(this.ESdata, val)
          if (this.displayList.length == 0) {
            console.log('testing')
            this.stepperFormGroup.patchValue({ ['division']: null })
            this.stepperFormGroup.patchValue({ ['sub_division']: null })
            console.log(this.stepperFormGroup.get('division').value)
            console.log(this.stepperFormGroup.get('sub_division').value)
            this.entity = this.stepperFormGroup.get('entity').value == null ? undefined : this.stepperFormGroup.get('entity').value
            this.division = this.stepperFormGroup.get('division').value == null ? undefined : this.stepperFormGroup.get('division').value
            this.subDivision = this.stepperFormGroup.get('sub_division').value == null ? undefined : this.stepperFormGroup.get('sub_division').value
            val = {}
            console.log(this.subDivision, this.division, this.entity)
            if (this.subDivision) {
              val['sub_division_id'] = this.subDivision
            }
            if (this.division) {
              val['division_id'] = this.division
            }
            if (this.entity) {
              val['entity_id'] = this.entity
            }
            this.displayList = _.where(this.ESdata, val)
          }
          this.Filterdata = _.where(this.ESdata, { entity_id: this.entity })

          this.divisionList = await this.getListValueFilter("division_name", "division_id")
          this.subDivisionList = await this.getListValueFilter("sub_division_name", "sub_division_id")
          console.log('testentity')

          console.log(this.displayList)
          this.selectedData = undefined;
          this.selectedIndex = undefined;

          console.log(this.displayList)
        }
      }
      else {
        if (this.stepperFormGroup.get('division').value == null && this.stepperFormGroup.get('sub_division').value == null) {
          this.displaySubDivision = false
          this.displayDivision = false
          this.displayEsList = false
        }
      }
    })
    this.stepperFormGroup.get('division').valueChanges.subscribe(async (res: any) => {
      if (this.check == false && this.stepperFormGroup.get('division').value != null) {
        this.displaySubDivision = true
        this.displayDivision = true
        this.displayEsList = true
        this.entity = this.stepperFormGroup.get('entity').value == null ? undefined : this.stepperFormGroup.get('entity').value
        this.division = this.stepperFormGroup.get('division').value == null ? undefined : this.stepperFormGroup.get('division').value
        this.subDivision = this.stepperFormGroup.get('sub_division').value == null ? undefined : this.stepperFormGroup.get('sub_division').value
        let val = {}
        console.log(this.subDivision, this.division, this.entity)
        if (this.subDivision) {
          val['sub_division_id'] = this.subDivision
        }
        if (this.division) {
          val['division_id'] = this.division
        }
        if (this.entity) {
          val['entity_id'] = this.entity
        }
        console.log(val)
        this.displayList = _.where(this.ESdata, val)
        // if(this.displayList.length==0){
        //   this.stepperFormGroup.patchValue({['entity']:null})
        //   this.stepperFormGroup.patchValue({['sub_division']:null})
        //   this.entity=this.stepperFormGroup.get('entity').value==null ? undefined :this.stepperFormGroup.get('entity').value
        //   this.division=this.stepperFormGroup.get('division').value==null ? undefined :this.stepperFormGroup.get('division').value
        //   this.subDivision=this.stepperFormGroup.get('sub_division').value==null ? undefined :this.stepperFormGroup.get('sub_division').value
        //    val={}
        //   console.log(this.subDivision, this.division, this.entity)
        //   if(this.subDivision)
        //   {
        //     val['sub_division_id'] = this.subDivision
        //   }
        //   if(this.division)
        //   {
        //     val['division_id'] = this.division
        //   }
        //   if(this.entity)
        //   {
        //     val['entity_id'] = this.entity
        //   }
        //   this.displayList = _.where(this.ESdata, val)
        // }

        this.Filterdata = _.where(this.ESdata, { entity_id: this.entity })

        this.subDivisionList = await this.getListValueFilter("sub_division_name", "sub_division_id")
        this.selectedData = undefined;
        this.selectedIndex = undefined;
        if (this.displayList.length == 0) {
          this.stepperFormGroup.patchValue({ ['sub_division']: '' })
        }
        console.log(this.displayList)
      }
    })
    this.stepperFormGroup.get('sub_division').valueChanges.subscribe(async (res: any) => {
      if (this.check == false && this.stepperFormGroup.get('sub_division').value != null) {
        this.displaySubDivision = true
        this.displayDivision = true
        this.displayEsList = true
        this.entity = this.stepperFormGroup.get('entity').value == null ? undefined : this.stepperFormGroup.get('entity').value
        this.division = this.stepperFormGroup.get('division').value == null ? undefined : this.stepperFormGroup.get('division').value
        this.subDivision = this.stepperFormGroup.get('sub_division').value == null ? undefined : this.stepperFormGroup.get('sub_division').value
        let val = {}
        console.log(this.subDivision, this.division, this.entity)
        if (this.subDivision) {
          val['sub_division_id'] = this.subDivision
        }
        if (this.division) {
          val['division_id'] = this.division
        }
        if (this.entity) {
          val['entity_id'] = this.entity
        }
        console.log(val)
        this.displayList = _.where(this.ESdata, val)
        // if(this.displayList.length==0){
        //   this.stepperFormGroup.patchValue({['division']:null})
        //   this.stepperFormGroup.patchValue({['entity']:null})
        //   this.entity=this.stepperFormGroup.get('entity').value==null ? undefined :this.stepperFormGroup.get('entity').value
        //   this.division=this.stepperFormGroup.get('division').value==null ? undefined :this.stepperFormGroup.get('division').value
        //   this.subDivision=this.stepperFormGroup.get('sub_division').value==null ? undefined :this.stepperFormGroup.get('sub_division').value
        //    val={}
        //   console.log(this.subDivision, this.division, this.entity)
        //   if(this.subDivision)
        //   {
        //     val['sub_division_id'] = this.subDivision
        //   }
        //   if(this.division)
        //   {
        //     val['division_id'] = this.division
        //   }
        //   if(this.entity)
        //   {
        //     val['entity_id'] = this.entity
        //   }
        //   this.displayList = _.where(this.ESdata, val)
        // }

        this.selectedData = undefined;
        this.selectedIndex = undefined;

        console.log(this.displayList)
      }
    })
    this.stepperFormGroup.get('textInputControl').valueChanges.subscribe(async (res) => {
      if (this.stepperFormGroup.get('textInputControl').value.length > 0) {
        if (res) {
          this.template_master_list = this.template_master_list_search.filter(item =>
            item.template_name.toLowerCase().includes(res.toLowerCase())
          );
        }
        else {
          this.template_master_list = this.template_master_list_search
        }

      }
      else {
        this.template_master_list = this.template_master_list_search
      }
    })
    this.stepperFormGroup.get('currency').valueChanges.subscribe(async (res) => {
      if (res) {
        for (let items of this.currency_list) {
          if (items['id'] == res) {
            this.currency_code = items['name']
            break;
          }
        }
      }
    })
    this.stepperFormGroup.get('from').valueChanges.subscribe(async(res)=>{
      if(res)
      {
        console.log("FROM CALLED", res)
        this.calculateTimeDifference(res, this.stepperFormGroup.get('to').value)
      }
    })
    this.stepperFormGroup.get('to').valueChanges.subscribe(async(res)=>{
      if(res)
      {
        console.log("TO CALLED", res)
        this.calculateTimeDifference( this.stepperFormGroup.get('from').value, res)
      }
    })
  }

  async saveProjectDetails() {
    console.log(this.stepperFormGroup.get('financial').value)
    this.save_disabled = true
    console.log(this.stepperFormGroup);
    this.week_array = []
    if (this.monday) {
      this.week_array.push(1)
    }
    if (this.tuesday) {
      this.week_array.push(2)
    }
    if (this.wednesday) {
      this.week_array.push(3)
    }
    if (this.thursday) {
      this.week_array.push(4)
    }
    if (this.friday) {
      this.week_array.push(5)
    }
    if (this.saturday) {
      this.week_array.push(6)
    }
    if (this.sunday) {
      this.week_array.push(7)
    }
    else{
      this.week_array = this.default_week.length > 0 ? this.default_week : [1, 2, 3, 4, 5];
    }
    console.log(this.week_array)
    this.stepperFormGroup.patchValue({ ['endDate']: moment(this.stepperFormGroup.get('endDate').value).format('YYYY-MM-DD') || this.stepperFormGroup.get('endDate').value })
    this.stepperFormGroup.patchValue({ ['startDate']: moment(this.stepperFormGroup.get('startDate').value).format('YYYY-MM-DD') || this.stepperFormGroup.get('startDate').value })
    this.data = this.stepperFormGroup.value;
    this.data['gantt_id'] = uuidv4()

    const mandate = this.checkMandateNotEmpty(this.data, this.fields, this.week_array, this.stakeholders, this.selectedOption)
    console.log(mandate,'test')
    if (mandate) {
      console.log(this.data);
      console.log(this.selectedValues.length)
      console.log(this.selectedValues)
      console.log(this.selectedOption)
      console.log(this.fields)
      console.log(this.stakeholders)
      console.log(this.data.portfolio)
      console.log(this.selectedValues[0])
      // if(this.selectedValues.length<1){
      //   this.selectedValues=this.weekGroup.value
      // } 
      console.log(this.selectedValues)
      for (let items of this.currency_list) {
        if (items['id'] == this.data.currency) {
          this.code = items['name']
        }
      }
      for (let item of this.p_and_l_list) {
        if (item['id'] == this.data.p_and_l) {
          this.data['p_and_l_name'] = item['name']
          break
        }
        else {
          this.data['p_and_l_name'] = null
        }
      }
      // this.data['quote_id']=this.quote_id
      this.data['at_risk'] = this.risk ? 1 : 0
      this.data['with_opportunity'] = this.withOpportunity ? 1 : 0
      if (this.withOpportunity) {
        const financialArray = this.stepperFormGroup.get('financial') as FormArray;
        for (let i = 0; i < this.stepperFormGroup.get('financial').value.length; i++) {
          const quote = financialArray.at(i).get('quote_id');
          // quote.patchValue('')
        }
        this.data['financial_data'] = this.stepperFormGroup.get('financial').value
      }
      else {
        const financialArrayWithOutOp=this.stepperFormGroup.get('fieldsArray').value
        this.data['financial_data_without_opportunity'] =this.stepperFormGroup.get('fieldsArray').value
        this.data['financial_data'] = []
      }
      this.data['tandmFlag']=this.TandM_selected
      await this.ProjectCreationService.checkProjectCodeDuplication(this.data.project_code).then((res: any) => {
        if (res) {
          this.codeDuplicated = res['data'];
          console.log(this.codeDuplicated);
          if (this.codeDuplicated == true) {
            this.valid = false;
            const enterAll_mandate_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.duplicate_project_code_msg ? this.retrieveMessages[0].errors.duplicate_project_code_msg : 'Project Code is not Valid! Generating new Project Code...' : 'Project Code is not Valid! Generating new Project Code...';
            this.toasterService.showWarning(enterAll_mandate_msg, 10000)
            this.generateNewProjectCode()
            this.save_disabled = false
            
          }
          else {
            this.valid = true;
          }
          this.refresh = false;
          if (res == '' && res == null) {
            this.refresh = true;
          }
        }
      })
    console.log(this.valid,'check')
      if(this.valid)
      {
        await this.ProjectCreationService.saveProjectDetails(this.data, this.selectedOption, this.week_array, this.fields, this.stakeholders, this.tags, this.code, this.intergeration,this.external_stakeholders).then((res: any) => {
          console.log(res)
          if (res['messType'] == 'S') {
            this.initializeStepper()
            this.selectedTemplate = ''
            this.data.portfolio = res['portfolio_id']
            this.dialogRef.close({ messType: "S", "project_id": res['data'], "portfolio_id": res['portfolio_id'], "data": this.data, "portfolio_name": this.portfolio_name })
          }
          else {
            this.save_disabled = false
          }
        });
      }
      else
      {
        this.save_disabled=false;
      }
    }
    else {
      this.save_disabled = false
    }
  }

  async selectOption(option: string): Promise<void> {
    console.log(option)
    if (this.selectedOption != option) {
      this.changeInServiceType = true
      console.log("Service type clicked",option)
      console.log(this.changeInServiceType)
    }
    else {
      this.changeInServiceType = false
    }
    console.log(this.changeInServiceType)
    this.selectedOption = option;
    console.log('select service type')
    console.log(this.selectedOption)

    let internal = _.where(this.cards , {is_internal:1,id:this.selectedOption})

    if (internal.length>0){
      console.log(internal[0])
      this.MakeFieldsNonMandatory = false
      this.checkWithoutOpportunity()

    }
    // else if (internal.length<1){
      
    //   this.changeInServiceType = false

    // }
    else{
      this.MakeFieldsNonMandatory=true
      // this.checkWithOpportunity()
    }
    console.log(this.selectOption)
    console.log(internal)
    console.log(internal[0])

    

    // if(this.selectedOption == '1'){
    //   this.MakeFieldsNonMandatory = false
    // }
    // else{
    //   this.MakeFieldsNonMandatory=true
    // }
    // console.log(this.MakeFieldsNonMandatory)
    if (this.stepperFormGroup.get('project_code').value.length == 0) {
      if (this.selectedOption) {

        let generated_code = _.where(this.formConfig, { field_name: "project_generation_code", type: "project-creation", is_active: true })

        if (generated_code.length > 0) {
          if (generated_code[0]['label'] == "service_type") {
            this.refresh = true

            const res = _.where(this.project_code_list, { service_type_id: this.selectedOption })

            this.stepperFormGroup.patchValue({ [`project_code`]: res.length > 0 ? res[0].projectCode : '' || '' })
            this.defaultValue = true
            this.refresh = false
          }

        }

      }
    }
    else if (this.defaultValue) {


      if (this.selectedOption) {

        let generated_code = _.where(this.formConfig, { field_name: "project_generation_code", type: "project-creation", is_active: true })

        if (generated_code.length > 0) {
          if (generated_code[0]['label'] == "service_type") {
            this.refresh = true

            const res = _.where(this.project_code_list, { service_type_id: this.selectedOption })

            this.stepperFormGroup.patchValue({ [`project_code`]: res.length > 0 ? res[0].projectCode : '' || '' })
            this.defaultValue = true
            this.refresh = false
          }
        }


      }

    }
    this.radioChecked = true
    console.log(this.selectOption)

  }
  createFieldGroup(): FormGroup {
    return this.formBuilder.group({
      po_number: [''],
      po_value: [0],
      po_reference: [''],
      po_date: [''],
      po_start_date:[''],
      po_end_date:[''],
      partner: [''],
      payment_terms: [''],
      invoice_template: ['']
    });
  }

  get fieldsArray(): FormArray {
    return this.stepperFormGroup.get('fieldsArray') as FormArray;
  }

  addFields() {
    this.fieldsArray.push(this.createFieldGroup());
  }

  removeFields(index: number) {
    if (this.fieldsArray.length > 1) {
      this.fieldsArray.removeAt(index);
    }
  }

  

  onCloseClick(): void {

    if (this.stepperFormGroup.dirty) {
      this.utilityService.openConfirmationSweetAlertWithCustom("Are you sure", "You want to Close without saving").then((result) => {
        if (result) {
          this.initializeStepper()
          this.dialogRef.close({ messType: "E" })
        }
      });
    }
    else {
      this.dialogRef.close({ messType: "E" })
    }
  }
  async filterList() {
    this.entity = this.stepperFormGroup.get('entity').value
    this.division = this.stepperFormGroup.get('division').value
    this.subDivision = this.stepperFormGroup.get('sub_division').value
    let val = {}
    console.log(this.subDivision, this.division, this.entity)
    if (this.subDivision) {
      val['sub_division_id'] = this.subDivision
    }
    if (this.division) {
      val['division_id'] = this.division
    }
    if (this.entity) {
      val['entity_id'] = this.entity
    }
    console.log(val)
    this.displayList = _.where(this.ESdata, val)
    this.selectedData = undefined;
    this.selectedIndex = undefined;

    console.log(this.displayList)
  }

  async getListValue(name, id) {
    let entityChoosen = _.uniq(_.pluck(this.ESdata, id))
    let val_result = []
    for (let entit of entityChoosen) {
      let result = _.where(this.ESdata, { [id]: entit })
      if (result.length > 0) {
        val_result.push({
          "id": result[0][id],
          "name": result[0][name]
        })
      }
    }
    console.log(name, entityChoosen, val_result)
    return val_result
  }

  // closeDialog(){
  //   this.dialogRef.close({messType:"E"})
  // }

  submitData() {
    if (this.selectedData) {
      // this.dialogRef.close({messType:"S", data: this.selectedData})

    }
    else {
      this.utilityService.showMessage("Kindly select option to proceed!", "Dismiss", 3000)
    }
  }

  selectData(display, i) {
    this.check = true
    console.log(display, i)
    if (this.selectedData) {
      if (this.selectedData == display) {
        this.selectedData = undefined;
        this.selectedIndex = undefined;
      }
      else {
        this.selectedData = display;
        this.selectedIndex = i;
      }
    }
    else {
      this.selectedData = display;
      this.selectedIndex = i;
    }
    console.log(this.selectedData, this.selectedIndex)
    this.entity = this.selectedData.entity_id
    this.division = this.selectedData.division_id
    this.subDivision = this.selectedData.sub_division_id
    this.stepperFormGroup.patchValue({ [`entity`]: this.entity })
    this.stepperFormGroup.patchValue({ [`division`]: this.division })
    this.stepperFormGroup.patchValue({ [`sub_division`]: this.subDivision })
    this.check = false
    console.log(this.selectedData, this.selectedIndex)
  }

  clearFilter() {
    this.displayList = this.ESdata;
    this.subDivision = undefined
    this.division = undefined
    this.entity = undefined
    this.stepperFormGroup.patchValue({ [`entity`]: null })
    this.stepperFormGroup.patchValue({ [`division`]: null })
    this.stepperFormGroup.patchValue({ [`sub_division`]: null })

  }
  addField() {
    this.fields.push({ workLocation: '', location: '', calendar: '' })
  }
  onRadioChange(event: any) {
    this.radioChecked = false
  }
  checkAdGroup() {
    console.log(this.stepperFormGroup.value.project_type)
    if (this.stepperFormGroup.value.project_type == 1) {
      this.peopleWidth = '470px'
    }
    else {
      this.peopleWidth = '710px'
    }
    console.log(this.peopleWidth)
  }
  validateProjectCode(event: Event) {
    this.defaultValue = false
    const inputValue = (event.target as HTMLInputElement).value;
    const patternStrings = _.where(this.formConfig, { type: "portfolio-creation", field_name: "portfolio_code", is_active: true });
    console.log(patternStrings);
    const patternRegex = patternStrings[0].pattern;
    const patternParts = patternRegex.split('/');
    const pattern1 = new RegExp(patternParts[1], patternParts[2]);
    console.log(patternRegex, pattern1);
    //const pattern = /[^0-9A-Za-z]/gi;
    //const sanitizedValue = inputValue.replace(pattern1, '').toUpperCase();
    const sanitizedValue = inputValue.replace(pattern1, '')

    // Check if portfolio code is duplicated
    if (this.codeDuplicated) {
      // Mark the portfolio code as invalid
      this.stepperFormGroup.get('project_code').setErrors({ duplicated: true });
      this.valid = false;
    } else {
      this.stepperFormGroup.get('project_code').setValue(sanitizedValue);
      this.valid = this.stepperFormGroup.get('project_code').valid;
    }

    // this.showPatternError = false;
    // this.showMaxLengthError = false;

    // this.showPatternError = this.portfolioCodeControl.hasError('pattern');
    // this.showMaxLengthError = this.portfolioCodeControl.hasError('maxlength');
  }
  onMinimizeClick() {
    console.log("minimize")
    this.isMinimized = !this.isMinimized;
    const newWidth = this.isMinimized ? '1016px' : this.originalWidth;
    const newHeight = this.isMinimized ? '100px' : this.originalHeight;
    // const newPosition = this.isMinimized ? { right: '0', bottom: '0' } : {left: '205px',top: '40px',right:'auto',bottom:'auto'}

    console.log(newWidth)
    this.dialogRef.updateSize(newWidth, newHeight);
    // this.dialogRef.updatePosition(newPosition);
    // this.dialogRef.updatePosition({ right: offsetX, bottom: offsetY });
    console.log('done')
  }
  storeWLocation(i: any, event: Event) {
    const wlValue = (event.target as HTMLInputElement).value;
    this.fields[i].workLocation = wlValue
  }
  storeLocation(i: any, lValue: any) {
    this.fields[i].country_id = lValue;
    const index = this.country_list.findIndex(item => item['id'] === lValue);
    this.fields[i].location = this.country_list[index].name;
    console.log(this.fields[i].location);
    console.log(this.country_list[index].name)
  }
  storeHolidayCalendar(i: any, event: Event) {
    const cValue = (event.target as HTMLInputElement).value;
    console.log(cValue)
    for (let item of this.holiday_calendar_list) {
      if (item['id'] == cValue) {
        this.fields[i].calendar = item['id']
      }
    }
  }
  addMemberField() {
    // if (this.stakeholders.length < 5) {
    this.stakeholders.push({ employee_name: '', start_date: '', end_date: '', role: '', split: '', isChecked: false, isHead: false })
    this.poepleCount = this.stakeholders.length
    // }
  }
  projectDetailsMandatory() {
    console.log(this.stepperFormGroup.valid)
    if (this.stepperFormGroup.valid) {
      this.saveProjectDetails()
    }
    else {
      const enterAll_mandate_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.enterAll_mandate_msg ? this.retrieveMessages[0].errors.enterAll_mandate_msg : 'Kindly Enter All Mandatory fields' : 'Kindly Enter All Mandatory fields';
      this.toasterService.showWarning(enterAll_mandate_msg, 10000)

    }
  }

  storeBillable(i: any, event: Event) {
    const BValue = (event.target as HTMLInputElement).value;
    this.stakeholders[i].isChecked = BValue
  }
  storeHead(i: any) {
    // const HValue = (event.target as HTMLInputElement).value;
    // this.stakeholders[i].isHead=HValue
    console.log("Hvale")
    for (let j = 0; j < this.stakeholders.length; i++) {
      if (i != j) {
        this.stakeholders[j].isHead = false
      }
    }
  }

  addTag() {
    this.isPopupVisible = true
  }
  handleValueEmitted(emittedValue: string) {
    this.tags = emittedValue
    console.log(emittedValue)
    console.log(this.tags)
  }
  onStepperChange(stepperData: any) {
    this.stepperData = stepperData;
    console.log('changed stepper data', this.stepperData);
    this.stepperControl();
  }
  getNextStepper() {
    console.log("next called", this.stepperData)
    const index = this.stepperData.findIndex(item => item.is_selected === true);
    console.log(index, this.stepperData.length, this.stepperData.length - 2);
    const man = this.checkDetailsMandatory(this.stepperData[index].type)
    if (man) {
      if (index !== -1 && index <= this.stepperData.length - 2) {
        this.stepperData[index].is_selected = false;
        this.stepperData[index].is_crossed = true;
        this.stepperData[index + 1].is_selected = true;
        //this.stepperData[index].mandate_completed = true;
        console.log(this.stepperData);
      }



      this.stepperControl();
    }
    // else{
    //   const index = this.stepperData.findIndex(item => item.is_selected === true);

    //   if (index !== -1 && index <= this.stepperData.length - 2) {
    //     this.stepperData[index + 1].mandate_completed = false;
    //   }
    // }            

  }
  getPreviousStepper() {
    console.log("previous called", this.stepperData)
    const index = this.stepperData.findIndex(item => item.is_selected === true);
    console.log(index, this.stepperData.length, this.stepperData.length - 1);
    if (index !== -1 && index > 0) {
      this.stepperData[index].is_selected = false;
      this.stepperData[index - 1].is_selected = true;
      this.stepperData[index - 1].is_crossed = false;
      console.log(this.stepperData);
    }
    this.stepperControl();
  }
  stepperControl() {
    const index = this.stepperData.findIndex(item => item.is_selected === true);
    if (index !== -1) {
      const selectedStepper = this.stepperData[index].type;
      //const mandate = this.checkDetailsMandatory(selectedStepper);
      let man;
      switch (selectedStepper) {
        case 'details':
          this.detailStepper = true
          this.enterpriseStepper = false
          this.financialStepper = false
          this.peopleStepper = false
          this.templateStepper = false
          this.scheduleStepper = false
          this.advanceStepper = false
          break;

        case 'es':
          this.detailStepper = false
          this.enterpriseStepper = true
          this.financialStepper = false
          this.peopleStepper = false
          this.templateStepper = false
          this.scheduleStepper = false
          this.advanceStepper = false
          // man = this.checkDetailsMandatory(selectedStepper)
          // if (man) {
          //   this.allStepperData[index-1].is_completed = true;
          //   this.allStepperData[index-1].mandate_completed = true;
          // }
          // else {
          //   this.allStepperData[index-1].is_completed = false;
          //   this.allStepperData[index-1].mandate_completed = false;
          // }
          break;

        case 'financial':
          this.detailStepper = false
          this.enterpriseStepper = false
          this.financialStepper = true
          this.peopleStepper = false
          this.templateStepper = false
          this.scheduleStepper = false
          this.advanceStepper = false
          // man = this.checkDetailsMandatory(selectedStepper)
          // if (man) {
          //   this.allStepperData[index-1].is_completed = true;
          //   this.allStepperData[index-1].mandate_completed = true;
          // }
          // else {
          //   this.allStepperData[index-1].is_completed = false;
          //   this.allStepperData[index-1].mandate_completed = false;
          // }
          break;

        case 'people':
          this.detailStepper = false
          this.enterpriseStepper = false
          this.financialStepper = false
          this.peopleStepper = true
          this.templateStepper = false
          this.scheduleStepper = false
          this.advanceStepper = false
          // man = this.checkDetailsMandatory(selectedStepper)
          // if (man) {
          //   this.allStepperData[index-1].is_completed = true;
          //   this.allStepperData[index-1].mandate_completed = true;
          // }
          // else {
          //   this.allStepperData[index-1].is_completed = false;
          //   this.allStepperData[index-1].mandate_completed = false;
          // }
          break;

        case 'schedule':
          this.detailStepper = false
          this.enterpriseStepper = false
          this.financialStepper = false
          this.peopleStepper = false
          this.templateStepper = false
          this.scheduleStepper = true
          this.advanceStepper = false
          // man = this.checkDetailsMandatory(selectedStepper)
          // if (man) {
          //   this.allStepperData[index-1].is_completed = true;
          //   this.allStepperData[index-1].mandate_completed = true;
          // }
          // else {
          //   this.allStepperData[index-1].is_completed = false;
          //   this.allStepperData[index-1].mandate_completed = false;
          // }
          break;

        case 'template':
          this.detailStepper = false
          this.enterpriseStepper = false
          this.financialStepper = false
          this.peopleStepper = false
          this.templateStepper = true
          this.scheduleStepper = false
          this.advanceStepper = false
          // man = this.checkDetailsMandatory(selectedStepper)
          // if (man) {
          //   this.allStepperData[index-1].is_completed = true;
          //   this.allStepperData[index-1].mandate_completed = true;
          // }
          // else {
          //   this.allStepperData[index-1].is_completed = false;
          //   this.allStepperData[index-1].mandate_completed = false;
          // }
          break;

        case 'advance':
          this.detailStepper = false
          this.enterpriseStepper = false
          this.financialStepper = false
          this.peopleStepper = false
          this.templateStepper = false
          this.scheduleStepper = false
          this.advanceStepper = true
          // man = this.checkDetailsMandatory(selectedStepper)
          // if (man) {
          //   this.allStepperData[index-1].is_completed = true;
          //   this.allStepperData[index-1].mandate_completed = true;
          // }
          // else {
          //   this.allStepperData[index-1].is_completed = false;
          //   this.allStepperData[index-1].mandate_completed = false;
          // }
          break;

        default:
          this.detailStepper = true
          this.enterpriseStepper = false
          this.financialStepper = false
          this.peopleStepper = false
          this.templateStepper = false
          this.scheduleStepper = false
          this.advanceStepper = false
      }
    }
  }
  onInputChange() {
    // Validate and restrict input to valid time values
    this.Fromhours = this.Fromhours.replace(/[^0-9]/g, '');
    this.Fromminutes = this.Fromminutes.replace(/[^0-9]/g, '');

    // Ensure hours and minutes are within valid ranges
    if (parseInt(this.Fromhours) > 23) {
      this.Fromhours = '23';
    }
    if (parseInt(this.Fromminutes) > 59) {
      this.Fromminutes = '59';
    }
    console.log(this.Fromminutes, this.Fromhours)
  }
  onInputChangeTo() {
    // Validate and restrict input to valid time values
    this.Tohours = this.Tohours.replace(/[^0-9]/g, '');
    this.Tominutes = this.Tominutes.replace(/[^0-9]/g, '');

    // Ensure hours and minutes are within valid ranges
    if (parseInt(this.Tohours) > 23) {
      this.Tohours = '23';
    }
    if (parseInt(this.Tominutes) > 59) {
      this.Tominutes = '59';
    }
    console.log(this.Tohours, this.Tominutes)
  }
  checkSaturday() {
    this.saturday = !this.saturday
  }
  checkSunday() {
    this.sunday = !this.sunday
  }
  checkMonday() {
    this.monday = !this.monday
  }
  checkTuesday() {
    this.tuesday = !this.tuesday
  }
  checkWednesday() {
    this.wednesday = !this.wednesday
  }
  checkThursday() {
    this.thursday = !this.thursday
  }
  checkFriday() {
    this.friday = !this.friday
  }
  removePeopleField(i: any) {
    console.log(i)
    const indexToDelete = i; // The index of the object you want to delete

    if (indexToDelete >= 0 && indexToDelete < this.stakeholders.length) {
      this.stakeholders.splice(indexToDelete, 1);
      this.poepleCount = this.poepleCount - 1 // This will remove one element at the specified index.
    } else {
      console.log('Invalid index provided');
    }
  }
  removeLocationField(i: any) {
    console.log(i)
    const indexToDelete = i; // The index of the object you want to delete

    if (indexToDelete >= 0 && indexToDelete < this.fields.length) {
      this.fields.splice(indexToDelete, 1); // This will remove one element at the specified index.
    } else {
      console.log('Invalid index provided');
    }
  }
  isMandate(field: any) {
    const mandate = _.where(this.formConfig, { type: "project-creation", field_name: field, is_active: true });
    if (mandate.length > 0) {
      const isMandate = mandate[0].is_mandant;
      return isMandate;
    }
  }

  checkMandateNotEmpty(data: any, ws: any, week: any, isa: any, selectedOption: any) {
    const financial_data = this.stepperFormGroup.get('financial').value
    const financial_data_without_opportunity=this.stepperFormGroup.get('fieldsArray').value
    let errorOccurred = false;
    const details = _.where(this.stepperData, { type: "details", is_active: true });

    if (details.length > 0) {
      if ((!data.project_code || data.project_code.trim() === '') && this.isMandate('project_code')) {
        // const codeEmptymsg = 'Kindly enter Milestone name';
        // this.toastr.error(codeEmptymsg, 'Error');
        console.log('test')
        errorOccurred = true;
      }
      if ((!data.project_name || data.project_name.trim() === '') && this.isMandate('project_name')) {
        // const codeEmptymsg = 'Kindly enter Milestone name';
        // this.toastr.error(codeEmptymsg, 'Error');
        console.log('test')
        errorOccurred = true;
      }
      if ((data.portfolio === null || data.portfolio === undefined || data.portfolio === '') && this.isMandate('portfolio')) {
        // const customerEmptymsg = 'Responsible Is Mandatory';
        // this.toastr.error(customerEmptymsg, 'Error');
        errorOccurred = true;
      }
      this.checkDates(data.startDate, data.endDate);
      if (!this.dateLogic) {
        errorOccurred = true;
      }
      // if(this.stepperFormGroup.get('startDate').valid)
      // {
      //   console.log('hi1')
      //   errorOccurred =true;
      // }
      if ((data.startDate === null || data.startDate === undefined || data.startDate === '') && this.isMandate('start_date')) {
        // const nameEmptymsg = 'Kindly choose When Is It due?';
        // this.toastr.error(nameEmptymsg, 'Error');
        errorOccurred = true;
      }
      // if(this.stepperFormGroup.get('endDate').valid)
      // {
      //   console.log('hi3')
      //   errorOccurred =true;
      // }
      if ((data.endDate === null || data.endDate === undefined || data.endDate === '') && this.isMandate('end_date')) {
        // const nameEmptymsg = 'Kindly choose When Is It due?';
        // this.toastr.error(nameEmptymsg, 'Error');
        errorOccurred = true;
      }
      if ((data.project_type === null || data.project_type === undefined || data.project_type === '') && this.isMandate('project_type')) {
        // const nameEmptymsg = 'Kindly choose When Is It due?';
        // this.toastr.error(nameEmptymsg, 'Error');
        errorOccurred = true;
      }
      if ((selectedOption === null || selectedOption === undefined || selectedOption === '') && this.isMandate('service_type')) {
        // const nameEmptymsg = 'Kindly choose When Is It due?';
        // this.toastr.error(nameEmptymsg, 'Error');
        errorOccurred = true;
      }
      if ((!data.description || data.description.trim() === '') && this.isMandate('project_description')) {
        // const codeEmptymsg = 'Kindly enter Milestone name';
        // this.toastr.error(codeEmptymsg, 'Error');
        errorOccurred = true;
      }
      if ((data.currency === null || data.currency === undefined || data.currency === '') && this.isMandate('currency')) {
        // const customerEmptymsg = 'Responsible Is Mandatory';
        // this.toastr.error(customerEmptymsg, 'Error');
        errorOccurred = true;
        const currency_mandate_err = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.currency_mandate_err ? this.retrieveMessages[0].errors.currency_mandate_err : 'Currency is mandatory' : 'Currency is mandatory';
        this.toasterService.showWarning(currency_mandate_err, 10000)
      }



    }

    const es = _.where(this.stepperData, { type: "es", is_active: true });
    if (es.length > 0) {
      if ((data.entity === null || data.entity === undefined || data.entity === '') && this.isMandate('entity')) {
        // const customerEmptymsg = 'Responsible Is Mandatory';
        // this.toastr.error(customerEmptymsg, 'Error');
        errorOccurred = true;
      }
      if ((data.division === null || data.division === undefined || data.division === '') && this.isMandate('division')) {
        // const customerEmptymsg = 'Responsible Is Mandatory';
        // this.toastr.error(customerEmptymsg, 'Error');
        errorOccurred = true;
      }
      if ((data.sub_division === null || data.sub_division === undefined || data.sub_division === '') && this.isMandate('sub_division')) {
        // const customerEmptymsg = 'Responsible Is Mandatory';
        // this.toastr.error(customerEmptymsg, 'Error');
        errorOccurred = true;
      }
    }

    const financial = _.where(this.stepperData, { type: "financial", is_active: true });
    if (financial.length > 0) {
      if (this.withOpportunity) {
        for (let item of financial_data) {
          if (item['is_active'] == 1) {
            if ((item['opportunity_id'] === null || item['opportunity_id'] === undefined || item['opportunity_id'] === '') && this.isMandate('opportunity') && this.MakeFieldsNonMandatory) {
              // const customerEmptymsg = 'Responsible Is Mandatory';
              // this.toastr.error(customerEmptymsg, 'Error');
              errorOccurred = true;
              break;
            }
            else if ((item['reason'] === null || item['reason'] === undefined || item['reason'] === '') && this.isMandate('reason')) {
              // const customerEmptymsg = 'Responsible Is Mandatory';
              // this.toastr.error(customerEmptymsg, 'Error');
              errorOccurred = true;
              break;
            }
            else if ((item['quote_id'] === null || item['quote_id'] === undefined || item['quote_id'] === '') && this.isMandate('quote')) {
              // const codeEmptymsg = 'Kindly enter Milestone name';
              // this.toastr.error(codeEmptymsg, 'Error');
              errorOccurred = true;
              break;
            }
            else if ((!item['po_number'] || item['po_number'].trim() === '') && this.isMandate('po_number_with_opportunity') && !this.poNonMandate && this.MakeFieldsNonMandatory) {
              // const codeEmptymsg = 'Kindly enter Milestone name';
              // this.toastr.error(codeEmptymsg, 'Error');
              errorOccurred = true;
              break;
            }
            else if ((item['purchase_order'] === null || item['purchase_order'] === undefined || item['purchase_order'] === '') && this.isMandate('po_value_with_opportunity') && this.MakeFieldsNonMandatory) {
              errorOccurred = true;
              break;
            }
          }
        }
      }
      else {
        for (let item of financial_data_without_opportunity ){
          if ((item['invoice_template'] === null ||item['invoice_template']  === undefined || item['invoice_template']  === '') && this.isMandate('invoice_template')) {
            // const customerEmptymsg = 'Responsible Is Mandatory';
            // this.toastr.error(customerEmptymsg, 'Error');
            errorOccurred = true;
            console.log("IV")
          }
          if ((!item['po_number'] || item['po_number'].trim() === '') && !this.poNonMandate && this.MakeFieldsNonMandatory) {
            // const codeEmptymsg = 'Kindly enter Milestone name';
            // this.toastr.error(codeEmptymsg, 'Error');
            errorOccurred = true;
            console.log("pon")
          }
          if ((item['po_value'] === null || item['po_value'] === undefined || item['po_value'] === '') && this.isMandate('po_value') && this.MakeFieldsNonMandatory) {
            // const codeEmptymsg = 'Kindly enter Milestone name';
            // this.toastr.error(codeEmptymsg, 'Error');
            errorOccurred = true;
            console.log("pov")
          }

        }
       
      }

    }

    const schedule = _.where(this.stepperData, { type: "schedule", is_active: true });
    if (schedule.length > 0) {
      if (week.length == 0 && this.isMandate('week')) {
        errorOccurred = true;
      }
      for (let i = 0; i < ws.length; i++) {
        if ((ws[i].workLocation === null || ws[i].workLocation === undefined || ws[i].workLocation === '') && this.isMandate('work_location')) {
          // const customerEmptymsg = 'Responsible Is Mandatory';
          // this.toastr.error(customerEmptymsg, 'Error');
          errorOccurred = true;
        }
        if ((ws[i].calendar === null || ws[i].calendar === undefined || ws[i].calendar === '') && this.isMandate('holiday_calendar')) {
          // const customerEmptymsg = 'Responsible Is Mandatory';
          // this.toastr.error(customerEmptymsg, 'Error');
          errorOccurred = true;
        }
        if ((!ws[i].location || ws[i].location.trim() === '') && this.isMandate('location')) {
          // const codeEmptymsg = 'Kindly enter Milestone name';
          // this.toastr.error(codeEmptymsg, 'Error');
          errorOccurred = true;
        }

      }
      if ((!data.from || data.from.trim() === '') && this.isMandate('from')) {
        // const codeEmptymsg = 'Kindly enter Milestone name';
        // this.toastr.error(codeEmptymsg, 'Error');
        errorOccurred = true;
      }
      if ((!data.to || data.to.trim() === '') && this.isMandate('to')) {
        // const codeEmptymsg = 'Kindly enter Milestone name';
        // this.toastr.error(codeEmptymsg, 'Error');
        errorOccurred = true;
      }
    }

    const people = _.where(this.stepperData, { type: "people", is_active: true });
    if (people.length > 0) {

      for (let i = 0; i < isa.length; i++) {
       
        
        if(isa[i].employee_name != null && isa[i].employee_name != undefined && isa[i].employee_name != '')
        {

          if (!this.checkInternalStakeholderDates(isa[i].start_date, isa[i].end_date, data.startDate, data.endDate)) {
            errorOccurred = true;
            this.dateLogic=false;
          }
          if ((isa[i].employee_name === null || isa[i].employee_name === undefined || isa[i].employee_name === '') && this.isMandate('name')) {
            // const customerEmptymsg = 'Responsible Is Mandatory';
            // this.toastr.error(customerEmptymsg, 'Error');
            this.people_name = false;
            errorOccurred = true;
          }
          if ((isa[i].start_date === null || isa[i].start_date === undefined || isa[i].start_date === '') && (this.isMandate('isa_start_date'))) {
            // const customerEmptymsg = 'Responsible Is Mandatory';
            // this.toastr.error(customerEmptymsg, 'Error');
            errorOccurred = true;
          }
          if ((isa[i].end_date === null || isa[i].end_date === undefined || isa[i].end_date === '') && (this.isMandate('isa_end_date'))) {
            // const customerEmptymsg = 'Responsible Is Mandatory';
            // this.toastr.error(customerEmptymsg, 'Error');
            errorOccurred = true;
          }

          if ((isa[i].role === null || isa[i].role === undefined || isa[i].role === '') && this.isMandate('role')) {
            // const customerEmptymsg = 'Responsible Is Mandatory';
            // this.toastr.error(customerEmptymsg, 'Error');
            errorOccurred = true;
          }
          if ((isa[i].split === null || isa[i].split === undefined || isa[i].split === '') && this.isMandate('capacity')) {
            // const customerEmptymsg = 'Responsible Is Mandatory';
            // this.toastr.error(customerEmptymsg, 'Error');
            errorOccurred = true;
          }
        }

      }
    }

    let check = _.where(this.formConfig,{type:"project-creation", field_name:"one_member", is_active:true})

    if(check.length>0)
    {
      if(isa.length==0)
      {
          const people_not_empty = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.people_not_empty ? this.retrieveMessages[0].errors.people_not_empty : 'Atleast 1 People needs to be allocated!' : 'Atleast 1 People needs to be allocated!';
          
          this.toasterService.showWarning(people_not_empty, 10000)

          return false
      }

      if (errorOccurred && !this.dateLogic && !this.people_name) {
        const enterAll_mandate_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.enterAll_mandate_msg ? this.retrieveMessages[0].errors.enterAll_mandate_msg : 'Kindly Enter All Mandatory fields' : 'Kindly Enter All Mandatory fields';
        this.toasterService.showWarning(enterAll_mandate_msg, 10000)
        return false; // Return false if any error occurred
      } else {
        return true; // Return true if no errors occurred
      }
    }
    else
    {
      if(isa.length>0)
      {
        if(!this.people_name)
        {
            return true;
        }
        else
        {
          if (errorOccurred && !this.dateLogic) {
            const enterAll_mandate_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.enterAll_mandate_msg ? this.retrieveMessages[0].errors.enterAll_mandate_msg : 'Kindly Enter All Mandatory fields' : 'Kindly Enter All Mandatory fields';
            this.toasterService.showWarning(enterAll_mandate_msg, 10000)

            return false; // Return false if any error occurred
          } else {
            return true; // Return true if no errors occurred
          }
        }
      }
    }

    if(errorOccurred){
      const enterAll_mandate_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.enterAll_mandate_msg ? this.retrieveMessages[0].errors.enterAll_mandate_msg : 'Kindly Enter All Mandatory fields' : 'Kindly Enter All Mandatory fields';
      this.toasterService.showWarning(enterAll_mandate_msg, 10000)
      return false
    }
    else{
      return true
    }
  }

  checkDetailsMandatory(type: any) {
    const data = this.stepperFormGroup.value
    const financial_data = this.stepperFormGroup.get('financial').value
    const financial_data_without_opportunity=this.stepperFormGroup.get('fieldsArray').value
    //const details = _.where(this.stepperData, { type: "details", is_active: true });
    let errorOccurred = false;
    let validateError = false
    if (type == 'details') {
      if ((!data.project_code || data.project_code.trim() === '') && this.isMandate('project_code')) {
        // const codeEmptymsg = 'Kindly enter Milestone name';
        const codeEmptymsg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.code_empty_msg ? this.retrieveMessages[0].errors.code_empty_msg : 'Project Code is Mandatory' : 'Project Code is Mandatory';
        this.toasterService.showWarning(codeEmptymsg, 10000)
        // this.toastr.error(codeEmptymsg, 'Error');
        errorOccurred = true;
      }
      else if ((!data.project_name || data.project_name.trim() === '') && this.isMandate('project_name')) {
        // const codeEmptymsg = 'Kindly enter Milestone name';
        const name_empty_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.name_empty_msg ? this.retrieveMessages[0].errors.name_empty_msg : 'Project Name is Mandatory' : 'Project Name is Mandatory';
        this.toasterService.showWarning(name_empty_msg, 10000)
        // this.toastr.error(codeEmptymsg, 'Error');
        errorOccurred = true;
      }
      else if ((data.portfolio === null || data.portfolio === undefined || data.portfolio === '') && this.isMandate('portfolio')) {
        // const customerEmptymsg = 'Responsible Is Mandatory';
        const portfolio_empty_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.portfolio_empty_msg ? this.retrieveMessages[0].errors.portfolio_empty_msg : 'Portfolio is Mandatory' : 'Portfolio is Mandatory';
        this.toasterService.showWarning(portfolio_empty_msg, 10000)
        // this.toastr.error(customerEmptymsg, 'Error');
        errorOccurred = true;
      }
      
      else if(!(this.stepperFormGroup.get('startDate').valid))
      {
        const startDate_empty_msg ='Invalid Start Date';
        this.toasterService.showWarning(startDate_empty_msg, 10000)
        this.dateLogic = true;
        errorOccurred = true;
      }
      else if ((data.startDate === null || data.startDate === undefined || data.startDate === '') && this.isMandate('start_date')) {
        // const nameEmptymsg = 'Kindly choose When Is It due?';
        if (data.startDate === '') {
          const startDate_empty_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.startDate_empty_msg ? this.retrieveMessages[0].errors.startDate_empty_msg : 'Start date is Mandatory' : 'Start date is Mandatory';
          this.toasterService.showWarning(startDate_empty_msg, 10000)
        }
        else if (data.startDate === null) {
          const startDate_invalid_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.startDate_invalid_msg ? this.retrieveMessages[0].errors.startDate_invalid_msg : 'Kindly Choose/Enter start date in DD-MM-YYYY format' : 'Kindly Choose/Enter start date in DD-MM-YYYY format';
          this.toasterService.showWarning(startDate_invalid_msg, 10000)
        }
        // this.toastr.error(nameEmptymsg, 'Error');
        this.dateLogic = true;
        errorOccurred = true;
      }
      else if(!(this.stepperFormGroup.get('endDate').valid))
      {
        const endDate_empty_msg ='Invalid End Date';
        this.toasterService.showWarning(endDate_empty_msg, 10000)
        this.dateLogic = true;
        errorOccurred = true;
      }
      else if ((data.endDate === null || data.endDate === undefined || data.endDate === '') && this.isMandate('end_date')) {
        // const nameEmptymsg = 'Kindly choose When Is It due?';
        // this.toastr.error(nameEmptymsg, 'Error');
        if (data.endDate === '') {
          const endDate_empty_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.startDate_empty_msg ? this.retrieveMessages[0].errors.startDate_empty_msg : 'End date is Mandatory' : 'End date is Mandatory';
          this.toasterService.showWarning(endDate_empty_msg, 10000)
        }
        else if (data.endDate === null) {
          const endDate_invalid_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.endDate_invalid_msg ? this.retrieveMessages[0].errors.endDate_invalid_msg : 'Kindly Choose/Enter end date in DD-MM-YYYY format' : 'Kindly Choose/Enter end date in DD-MM-YYYY format';
          this.toasterService.showWarning(endDate_invalid_msg, 10000)
        }
        this.dateLogic = true;
        errorOccurred = true;

      }
      this.checkDates(data.startDate, data.endDate);
      if (!this.dateLogic && !errorOccurred) {
        errorOccurred = true;
        const valid_date_err = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.valid_date_err ? this.retrieveMessages[0].errors.valid_date_err : 'Kindly enter valid date' : 'Kindly enter valid date';
        this.toasterService.showWarning(valid_date_err, 10000)
      }
      else if ((data.project_type === null || data.project_type === undefined || data.project_type === '') && this.isMandate('project_type') && !errorOccurred) {
        // const nameEmptymsg = 'Kindly choose When Is It due?';
        const projectType_empty_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.projectType_empty_msg ? this.retrieveMessages[0].errors.projectType_empty_msg : 'Project Type is Mandatory' : 'Project Type is Mandatory';
        this.toasterService.showWarning(projectType_empty_msg, 10000)
        // this.toastr.error(nameEmptymsg, 'Error');
        errorOccurred = true;
      }
      else if ((data.currency === null || data.currency === undefined || data.currency === '') && this.isMandate('currency')) {
        // const customerEmptymsg = 'Responsible Is Mandatory';
        // this.toastr.error(customerEmptymsg, 'Error');
        const currency_mandate_err = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.currency_mandate_err ? this.retrieveMessages[0].errors.currency_mandate_err : 'Currency is mandatory' : 'Currency is mandatory';
        errorOccurred = true;
        this.toasterService.showWarning(currency_mandate_err, 10000)
      }
      else if ((this.selectedOption === null || this.selectedOption === undefined || this.selectedOption === '') && this.isMandate('service_type') && !errorOccurred) {
        // const nameEmptymsg = 'Kindly choose When Is It due?';
        const serviceType_empty_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.serviceType_empty_msg ? this.retrieveMessages[0].errors.serviceType_empty_msg : 'Service Type is Mandatory' : 'Service Type is Mandatory';
        this.toasterService.showWarning(serviceType_empty_msg, 10000)
        // this.toastr.error(nameEmptymsg, 'Error');
        errorOccurred = true;
      }
      else if ((!data.description || data.description.trim() === '') && this.isMandate('project_description') && !errorOccurred) {
        // const codeEmptymsg = 'Kindly enter Milestone name';
        const description_empty_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.description_empty_msg ? this.retrieveMessages[0].errors.description_empty_msg : 'Description is Mandatory' : 'Description is Mandatory';
        this.toasterService.showWarning(description_empty_msg, 10000)
        // this.toastr.error(codeEmptymsg, 'Error');
        errorOccurred = true;
      }
      else if (this.valid == false) {
        validateError = true
      }
      else if (!errorOccurred && !validateError) {
        for (let items of this.stepperData) {
          if (items['type'] == 'details') {
            items['is_completed'] = true
          }
          return true
        }
      }
      else if (validateError && !errorOccurred) {
        const code_invalid_err = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.code_invalid_err ? this.retrieveMessages[0].errors.code_invalid_err : 'Invalid Project Code! Kindly check and try again' : 'Invalid Project Code! Kindly check and try again';
        this.toasterService.showWarning(code_invalid_err, 10000)
      }
      else if (!this.dateLogic) {
        const isa_dateLogic = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.isa_dateLogic ? this.retrieveMessages[0].errors.isa_dateLogic : 'Kindly Enter Valid Date!' : 'Kindly Enter Valid Date!';
        this.toasterService.showWarning(isa_dateLogic, 10000)
      }
      else if (errorOccurred) {
        //this.toastr.warning("Kindly Enter All Mandatory fields", 'Warning');
        return false
      }


    }
    if (type == 'es') {
      if ((data.entity === null || data.entity === undefined || data.entity === '') && this.isMandate('entity')) {
        // const customerEmptymsg = 'Responsible Is Mandatory';
        const entity_empty = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.entity_empty ? this.retrieveMessages[0].errors.entity_empty : 'Entity is mandatory' : 'Entity is mandatory';

        this.toasterService.showWarning(entity_empty, 10000)
        // this.toastr.error(customerEmptymsg, 'Error');
        errorOccurred = true;
      }
      else if ((data.division === null || data.division === undefined || data.division === '') && this.isMandate('division')) {
        // const customerEmptymsg = 'Responsible Is Mandatory';
        const division_empty = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.division_empty ? this.retrieveMessages[0].errors.division_empty : 'Division is mandatory' : 'Division is mandatory';

        this.toasterService.showWarning(division_empty, 10000)
        // this.toastr.error(customerEmptymsg, 'Error');
        errorOccurred = true;
      }
      else if ((data.sub_division === null || data.sub_division === undefined || data.sub_division === '') && this.isMandate('sub_division')) {
        // const customerEmptymsg = 'Responsible Is Mandatory';
        const subdivision_empty = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.subdivision_empty ? this.retrieveMessages[0].errors.subdivision_empty : 'Sub Division is mandatory' : 'Sub Division is mandatory';

        this.toasterService.showWarning(subdivision_empty, 10000)
        // this.toastr.error(customerEmptymsg, 'Error');
        errorOccurred = true;
      }
      else if ((data.child_customer === null || data.child_customer === undefined || data.child_customer === '') && this.isMandate('child_customer')) {
        // const customerEmptymsg = 'Responsible Is Mandatory';
        const subdivision_empty ='Child Customer is mandatory';

        this.toasterService.showWarning(subdivision_empty, 10000)
        // this.toastr.error(customerEmptymsg, 'Error');
        errorOccurred = true;
      }
      else if ((data.p_and_l === null || data.p_and_l === undefined || data.p_and_l === '') && this.isMandate('p_and_l')) {
        // const customerEmptymsg = 'Responsible Is Mandatory';
        const profitCenter_mandate_msg = this.retrieveMessages[0].errors.profitCenter_mandate_msg ? this.retrieveMessages[0].errors.profitCenter_mandate_msg ? this.retrieveMessages[0].errors.profitCenter_mandate_msg : 'Profit Center is Mandatory' : 'Profit Center is Mandatory';
        this.toasterService.showWarning(profitCenter_mandate_msg, 10000)
        // this.toastr.error(customerEmptymsg, 'Error');
        errorOccurred = true;
      }
      else if ((data.legal_entity === null || data.legal_entity === undefined || data.legal_entity === '') && this.isMandate('legal_entity')) {
        // const customerEmptymsg = 'Responsible Is Mandatory';
        const legalEntity_mandate_msg = this.retrieveMessages[0].errors.legalEntity_mandate_msg ? this.retrieveMessages[0].errors.legalEntity_mandate_msg ? this.retrieveMessages[0].errors.legalEntity_mandate_msg : 'SOW Owner is Mandatory' : 'SOW Owner is Mandatory';
        this.toasterService.showWarning(legalEntity_mandate_msg, 10000)
        // this.toastr.error(customerEmptymsg, 'Error');
        errorOccurred = true;
      }
      else if ((data.sow_reference_number === null || data.sow_reference_number === undefined || data.sow_reference_number === '') ) {
        if(this.isMandate('sow_reference_number')){
          // const customerEmptymsg = 'Responsible Is Mandatory';
          const sowRef_mandate_msg = this.retrieveMessages[0].errors.sowRef_mandate_msg ? this.retrieveMessages[0].errors.sowRef_mandate_msg ? this.retrieveMessages[0].errors.sowRef_mandate_msg : 'SOW Reference Number is Mandatory' : 'SOW Reference Number is Mandatory';
          this.toasterService.showWarning(sowRef_mandate_msg, 10000);
          errorOccurred = true;
        }
        // this.toastr.error(customerEmptymsg, 'Error');
      }
      else if (data.sow_reference_number){
        if(this.isSowReferenceNumberRefresh){
          this.toasterService.showWarning("Please wait while SOW reference number is being verified.", 10000);
          errorOccurred = true;
        }
        if(!this.sowReferenceNumberValid && this.invalidSowReferenceNumberLength){
          const sow_invalid_length_msg = this.retrieveMessages[0]?.errors?.sow_invalid_length ? this.retrieveMessages[0].errors.sow_invalid_length : 'SOW Reference Number too short';
          this.toasterService.showWarning(sow_invalid_length_msg, 10000);
          errorOccurred = true;
        }
        if(!this.sowReferenceNumberValid && this.SowReferenceNumberDuplicated){
          const sow_ref_duplicated_msg = this.retrieveMessages[0]?.errors?.sow_ref_duplicated ? this.retrieveMessages[0].errors.sow_ref_duplicated : 'SOW Reference Number already exists';
          this.toasterService.showWarning(sow_ref_duplicated_msg, 10000);
          errorOccurred = true;
        }
      }
      else if ((data.product_category === null || data.product_category === undefined || data.product_category === '') && this.isMandate('product_category')) {
        // const customerEmptymsg = 'Responsible Is Mandatory';
        const product_category_mandate_msg = this.retrieveMessages[0].errors.product_category_mandate_msg ? this.retrieveMessages[0].errors.product_category_mandate_msg ? this.retrieveMessages[0].errors.product_category_mandate_msg : 'Product Category is Mandatory' : 'Product Category is Mandatory';
        this.toasterService.showWarning(product_category_mandate_msg, 10000)
        // this.toastr.error(customerEmptymsg, 'Error');
        errorOccurred = true;
      }
      else if ((data.delivery_type === null || data.delivery_type === undefined || data.delivery_type === '') && this.isMandate('delivery_type')) {
        // const customerEmptymsg = 'Responsible Is Mandatory';
        const delivery_type_mandate_msg = this.retrieveMessages[0].errors.delivery_type_mandate_msg ? this.retrieveMessages[0].errors.delivery_type_mandate_msg ? this.retrieveMessages[0].errors.delivery_type_mandate_msg : 'Delivery Type is Mandatory' : 'Delivery Type is Mandatory';
        this.toasterService.showWarning(delivery_type_mandate_msg, 10000)
        // this.toastr.error(customerEmptymsg, 'Error');
        errorOccurred = true;
      }
      else if ((data.revenue_type === null || data.revenue_type === undefined || data.revenue_type === '') && this.isMandate('revenue_type')) {
        // const customerEmptymsg = 'Responsible Is Mandatory';
        const revenue_type_mandate_msg = this.retrieveMessages[0].errors.revenue_type_mandate_msg ? this.retrieveMessages[0].errors.revenue_type_mandate_msg ? this.retrieveMessages[0].errors.revenue_type_mandate_msg : 'Revenue Type is Mandatory' : 'Revenue Type is Mandatory';
        this.toasterService.showWarning(revenue_type_mandate_msg, 10000)
        // this.toastr.error(customerEmptymsg, 'Error');
        errorOccurred = true;
      }
      if (!errorOccurred) {
        for (let items of this.stepperData) {
          if (items['type'] == 'es') {
            items['is_completed'] = true
          }
        }
        return true
      }
      else if (errorOccurred) {
        //this.toastr.warning("Kindly Enter All Mandatory fields", 'Warning');
        return false
      }
    }
    if (type == 'financial') {

      if (this.withOpportunity) {
        for (let item of financial_data) {
          if (item['is_active'] == 1) {
            if ((item['opportunity_id'] === null || item['opportunity_id'] === undefined || item['opportunity_id'] === '') && this.isMandate('opportunity') && this.MakeFieldsNonMandatory) {
              const opportunity_empty = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.opportunity_empty ? this.retrieveMessages[0].errors.opportunity_empty : 'Opportunity is mandatory' : 'Opportunity is mandatory';

              this.toasterService.showWarning(opportunity_empty, 10000)
              // const customerEmptymsg = 'Responsible Is Mandatory';
              // this.toastr.error(customerEmptymsg, 'Error');
              errorOccurred = true;
              break;
            }
            else if ((item['reason'] === null || item['reason'] === undefined || item['reason'] === '') && this.isMandate('reason')) {
              // const customerEmptymsg = 'Responsible Is Mandatory';
              // this.toastr.error(customerEmptymsg, 'Error');
              const reason_mandate_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.reason_mandate_msg ? this.retrieveMessages[0].errors.reason_mandate_msg : 'Reason is Mandatory' : 'Reason is Mandatory';
              errorOccurred = true;
              this.toasterService.showWarning(reason_mandate_msg, 10000)
              break;
            }
            else if ((item['quote_id'] === null || item['quote_id'] === undefined || item['quote_id'] === '') && this.isMandate('quote')) {
              // const codeEmptymsg = 'Kindly enter Milestone name';
              const quote_empty = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.quote_empty ? this.retrieveMessages[0].errors.quote_empty : 'Quote is mandatory' : 'Quote is mandatory';

              this.toasterService.showWarning(quote_empty, 10000)
              // this.toastr.error(codeEmptymsg, 'Error');
              errorOccurred = true;
              break;
            }
            else if ((!item['po_number'] || item['po_number'].trim() === '') && this.isMandate('po_number_with_opportunity') && !this.poNonMandate && this.MakeFieldsNonMandatory) {
              // const codeEmptymsg = 'Kindly enter Milestone name';
              // this.toastr.error(codeEmptymsg, 'Error');
              const poNumber_mandate_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.poNumber_mandate_msg ? this.retrieveMessages[0].errors.poNumber_mandate_msg : 'PO Number is mandatory' : 'PO Number is mandatory';
              this.toasterService.showWarning(poNumber_mandate_msg, 10000)
              errorOccurred = true;
              break;
            }
            else if ((item['purchase_order'] === null || item['purchase_order'] === undefined || item['purchase_order'] === '') && this.isMandate('po_value_with_opportunity') && this.MakeFieldsNonMandatory) {
              // const codeEmptymsg = 'Kindly enter Milestone name';
              // this.toastr.error(codeEmptymsg, 'Error');
              const orderValue_mandate_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.orderValue_mandate_msg ? this.retrieveMessages[0].errors.orderValue_mandate_msg : 'Order value is mandatory' : 'Order value is mandatory';
              this.toasterService.showWarning(orderValue_mandate_msg, 10000)
              errorOccurred = true;
              break;
            }
            else if ((item['purchase_order'] == 0) && this.isMandate('po_value_with_opportunity') && this.MakeFieldsNonMandatory) {
              // const codeEmptymsg = 'Kindly enter Milestone name';
              // this.toastr.rror(codeEmptymsg, 'Error');
              const orderValue_mandate_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.orderValue_mandate_msg ? this.retrieveMessages[0].errors.orderValue_mandate_msg : 'Order value is mandatory' : 'Order value is mandatory';
              this.toasterService.showWarning(orderValue_mandate_msg, 10000)
              errorOccurred = true;
              break;
            }
            else if ((item['payment_terms'] === null || item['payment_terms'] === undefined || item['payment_terms'] === '') && this.isMandate('payment_terms')) {
              // const codeEmptymsg = 'Kindly enter Milestone name';
              const paymentTerms_empty = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.paymentTerms_empty ? this.retrieveMessages[0].errors.paymentTerms_empty : 'Payment Term is mandatory' : 'Payment Term is mandatory';

              this.toasterService.showWarning(paymentTerms_empty, 10000)
              // this.toastr.error(codeEmptymsg, 'Error');
              errorOccurred = true;
              break;
            }
            else if ((item['partner'] === null || item['partner'] === undefined || item['partner'] === '') && this.isMandate('partner')) {
              // const codeEmptymsg = 'Kindly enter Milestone name';
              // this.toastr.error(codeEmptymsg, 'Error');
              const partner_empty = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.partner_empty ? this.retrieveMessages[0].errors.partner_empty : 'Partner is mandatory' : 'Partner is mandatory';
              this.toasterService.showWarning(partner_empty, 10000)
              errorOccurred = true;
              break;
            }
            else if ((item['po_date'] === null || item['po_date'] === undefined || item['po_date'] === '') && this.isMandate('po_date')) {
              // const codeEmptymsg = 'Kindly enter Milestone name';
              // this.toastr.error(codeEmptymsg, 'Error');
              const poDate_empty = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.poDate_empty ? this.retrieveMessages[0].errors.poDate_empty : 'PO Date value is mandatory' : 'PO Date is mandatory';
              this.toasterService.showWarning(poDate_empty, 10000)

              errorOccurred = true;
              break;
            }
            else if ((item['po_reference'] == 0) && this.isMandate('po_reference')) {
              // const codeEmptymsg = 'Kindly enter Milestone name';
              // this.toastr.rror(codeEmptymsg, 'Error');
              const poReference_empty = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.poReference_empty ? this.retrieveMessages[0].errors.poReference_empty : 'PO Reference Cannot be zero' : 'PO Reference value Cannot be zero';
              this.toasterService.showWarning(poReference_empty, 10000)

              errorOccurred = true;
              break;
            }
          }
        }
        if (!errorOccurred) {
          let isa_active_oppotunity = _.where(financial_data, { is_active: 1 })
          if (isa_active_oppotunity.length > 1) {
            for (let item of financial_data) {
              let check_duplicate
              check_duplicate = _.where(financial_data, { opportunity_id: item['opportunity_id'] })
              if (check_duplicate.length > 1) {
                const opportunityDuplicate_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.opportunityDuplicate_msg ? this.retrieveMessages[0].errors.opportunityDuplicate_msg : 'Duplicate Opportunity will not be allowed' : 'Duplicate Opportunity will not be allowed';

                this.toasterService.showWarning(opportunityDuplicate_msg, 10000)
                errorOccurred = true;
                break;
              }
            }
          }
        }
        if (!errorOccurred) {
          for (let items of this.stepperData) {
            if (items['type'] == 'financial') {
              items['is_completed'] = true
              return true
            }
          }
        }
        else if (errorOccurred) {
          //this.toastr.warning("Kindly Enter All Mandatory fields", 'Warning');
          return false
        }
      }
      else {
        for (let data of financial_data_without_opportunity){
        if ((data.invoice_template === null || data.invoice_template === undefined || data.invoice_template === '') && this.isMandate('invoice_template')) {
          // const customerEmptymsg = 'Responsible Is Mandatory';
          const invoiceTemplate_empty = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.invoiceTemplate_empty ? this.retrieveMessages[0].errors.invoiceTemplate_empty : 'Invoice Template is mandatory' : 'Invoice Template is mandatory';

          this.toasterService.showWarning(invoiceTemplate_empty, 10000)
          // this.toastr.error(customerEmptymsg, 'Error');
          errorOccurred = true;
        }

        else if ((!data.po_number || data.po_number.trim() === '') && this.isMandate('po_number') && !this.poNonMandate && this.MakeFieldsNonMandatory) {
          // const codeEmptymsg = 'Kindly enter Milestone name';
          // this.toastr.error(codeEmptymsg, 'Error');
          const poNumber_mandate_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.poNumber_mandate_msg ? this.retrieveMessages[0].errors.poNumber_mandate_msg : 'PO Number is mandatory' : 'PO Number is mandatory';

          this.toasterService.showWarning(poNumber_mandate_msg, 10000)
          errorOccurred = true;
        }
        else if ((data.po_value === null || data.po_value === undefined || data.po_value === '') && this.isMandate('po_value') && this.MakeFieldsNonMandatory) {
          // const codeEmptymsg = 'Kindly enter Milestone name';
          // this.toastr.error(codeEmptymsg, 'Error');
          const orderValue_mandate_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.orderValue_mandate_msg ? this.retrieveMessages[0].errors.orderValue_mandate_msg : 'Order value is mandatory' : 'Order value is mandatory';

          this.toasterService.showWarning(orderValue_mandate_msg, 10000)
          errorOccurred = true;
        }
        else if ((data.po_reference === null || data.po_reference === undefined || data.po_reference === '') && this.isMandate('po_reference')) {
          // const codeEmptymsg = 'Kindly enter Milestone name';
          // this.toastr.error(codeEmptymsg, 'Error');
          const poReference_empty = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.poReference_empty ? this.retrieveMessages[0].errors.poReference_empty : 'PO Reference Cannot be zero' : 'PO Reference value Cannot be zero';

          this.toasterService.showWarning(poReference_empty, 10000)
          errorOccurred = true;
        }
        else if ((data.po_date === null || data.po_date === undefined || data.po_date === '') && this.isMandate('po_date')) {
          // const codeEmptymsg = 'Kindly enter Milestone name';
          // this.toastr.error(codeEmptymsg, 'Error');
          const poDate_empty = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.poDate_empty ? this.retrieveMessages[0].errors.poDate_empty : 'PO Date value is mandatory' : 'PO Date is mandatory';
          this.toasterService.showWarning(poDate_empty, 10000)
          errorOccurred = true;
        }
        else if ((data.partner === null || data.partner === undefined || data.partner === '') && this.isMandate('partner')) {
          // const customerEmptymsg = 'Responsible Is Mandatory';
          const partner_empty = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.partner_empty ? this.retrieveMessages[0].errors.partner_empty : 'Partner is mandatory' : 'Partner is mandatory';

          this.toasterService.showWarning(partner_empty, 10000)
          // this.toastr.error(customerEmptymsg, 'Error');
          errorOccurred = true;
        }
        else if ((data.payment_terms === null || data.payment_terms === undefined || data.payment_terms === '') && this.isMandate('payment_terms')) {
          // const customerEmptymsg = 'Responsible Is Mandatory';
          const paymentTerms_empty = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.paymentTerms_empty ? this.retrieveMessages[0].errors.paymentTerms_empty : 'Payment Term is mandatory' : 'Payment Term is mandatory';
          
          this.toasterService.showWarning(paymentTerms_empty, 10000)
          // this.toastr.error(customerEmptymsg, 'Error');
          errorOccurred = true;
        }
        else if ((data.po_value == 0) && this.isMandate('po_value') && this.MakeFieldsNonMandatory) {
          // const codeEmptymsg = 'Kindly enter Milestone name';
          // this.toastr.error(codeEmptymsg, 'Error');
          const poValue_zero_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.poValue_zero_msg ? this.retrieveMessages[0].errors.poValue_zero_msg : 'Order value cannot be zero' : 'Order value cannot be zero';

          this.toasterService.showWarning(poValue_zero_msg, 10000)
          errorOccurred = true;
        }
      }

        if (!errorOccurred) {
          for (let items of this.stepperData) {
            if (items['type'] == 'financial') {
              items['is_completed'] = true
              return true
            }
          }
        }
        else if (errorOccurred) {
          //this.toastr.warning("Kindly Enter All Mandatory fields", 'Warning');
          return false
        }
      }
    }
    if (type == 'schedule') {
      if (this.week_array.length == 0 && this.isMandate('week')) {
        errorOccurred = true;
      }
      for (let i = 0; i < this.fields.length; i++) {
        if ((this.fields[i].workLocation === null || this.fields[i].workLocation === undefined || this.fields[i].workLocation === '') && this.isMandate('work_location')) {
          // const customerEmptymsg = 'Responsible Is Mandatory';
          // this.toastr.error(customerEmptymsg, 'Error');
          errorOccurred = true;
        }
        if ((this.fields[i].calendar === null || this.fields[i].calendar === undefined || this.fields[i].calendar === '') && this.isMandate('holiday_calendar')) {
          // const customerEmptymsg = 'Responsible Is Mandatory';
          // this.toastr.error(customerEmptymsg, 'Error');
          errorOccurred = true;
        }
        if ((!this.fields[i].location || this.fields[i].location.trim() === '') && this.isMandate('location')) {
          // const codeEmptymsg = 'Kindly enter Milestone name';
          // this.toastr.error(codeEmptymsg, 'Error');
          errorOccurred = true;
        }

      }
      if ((!data.from || data.from.trim() === '') && this.isMandate('from')) {
        // const codeEmptymsg = 'Kindly enter Milestone name';
        // this.toastr.error(codeEmptymsg, 'Error');
        errorOccurred = true;
      }
      if ((!data.to || data.to.trim() === '') && this.isMandate('to')) {
        // const codeEmptymsg = 'Kindly enter Milestone name';
        // this.toastr.error(codeEmptymsg, 'Error');
        errorOccurred = true;
      }
      if (!errorOccurred) {
        for (let items of this.stepperData) {
          if (items['type'] == 'schedule') {
            items['is_completed'] = true
            return true
          }
        }
      }
      else if (errorOccurred) {
        const enterAll_mandate_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.enterAll_mandate_msg ? this.retrieveMessages[0].errors.enterAll_mandate_msg : 'Kindly Enter All Mandatory fields' : 'Kindly Enter All Mandatory fields';
        this.toasterService.showWarning(enterAll_mandate_msg, 10000)

        return false
      }
    }
    if (type == 'people') {
      for (let i = 0; i < this.stakeholders.length; i++) {

        if ((this.stakeholders[i].employee_name === null || this.stakeholders[i].employee_name === undefined || this.stakeholders[i].employee_name === '') && this.isMandate('name')) {
          // const customerEmptymsg = 'Responsible Is Mandatory';
          if (this.stakeholders[i].employee_name === '') {
            const peopleName_empty = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.peopleName_empty ? this.retrieveMessages[0].errors.peopleName_empty : 'People is mandatory' : 'People is mandatory';

            this.toasterService.showWarning(peopleName_empty, 10000)

          }
          else if (this.stakeholders[i].employee_name === null || this.stakeholders[i].employee_name === undefined) {
            const people_notMatch = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.people_notMatch ? this.retrieveMessages[0].errors.people_notMatch : 'Kindly Choose a Valid Employee name' : 'Kindly Choose a Valid Employee name';
            this.toasterService.showWarning(people_notMatch, 10000)


          }

          // this.toastr.error(customerEmptymsg, 'Error');
          this.people_name = false;
          errorOccurred = true;
          break;
        }
        else if ((this.stakeholders[i].start_date === null || this.stakeholders[i].start_date === undefined || this.stakeholders[i].start_date === '' || !this.checkInternalStakeholderDates(this.stakeholders[i].start_date, this.stakeholders[i].end_date, data.startDate, data.endDate)) && this.isMandate('isa_start_date')) {
          if (this.stakeholders[i].start_date === '') {
            const startDate_empty_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.startDate_empty_msg ? this.retrieveMessages[0].errors.startDate_empty_msg : 'Start date is Mandatory' : 'Start date is Mandatory';
            this.toasterService.showWarning(startDate_empty_msg, 10000)

          }
          else if (this.stakeholders[i].start_date === null) {
            const startDate_invalid_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.startDate_invalid_msg ? this.retrieveMessages[0].errors.startDate_invalid_msg : 'Kindly Choose/Enter start date in DD-MM-YYYY format' : 'Kindly Choose/Enter start date in DD-MM-YYYY format';
            this.toasterService.showWarning(startDate_invalid_msg, 10000)

          }
          else if(!this.checkInternalStakeholderDates(this.stakeholders[i].start_date, this.stakeholders[i].end_date, data.startDate, data.endDate))
          {
            const valid_date_err = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.valid_date_err ? this.retrieveMessages[0].errors.valid_date_err : 'Kindly enter valid date' : 'Kindly enter valid date';
            this.toasterService.showWarning(valid_date_err, 10000)
          }
          this.dateLogic = true;
          errorOccurred = true;
          break;
          // const customerEmptymsg = 'Responsible Is Mandatory';
          // this.toastr.error(customerEmptymsg, 'Error');
        }
        else if ((this.stakeholders[i].end_date === null || this.stakeholders[i].end_date === undefined || this.stakeholders[i].end_date === '' ||  !this.checkInternalStakeholderDates(this.stakeholders[i].start_date, this.stakeholders[i].end_date, data.startDate, data.endDate)) && (this.isMandate('isa_end_date'))) {
          // const customerEmptymsg = 'Responsible Is Mandatory';
          // this.toastr.error(customerEmptymsg, 'Error');
          if (this.stakeholders[i].end_date === '') {
            const endDate_empty_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.startDate_empty_msg ? this.retrieveMessages[0].errors.startDate_empty_msg : 'End date is Mandatory' : 'End date is Mandatory';
            this.toasterService.showWarning(endDate_empty_msg, 10000)

          }
          else if (this.stakeholders[i].end_date === null) {
            const endDate_invalid_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.endDate_invalid_msg ? this.retrieveMessages[0].errors.endDate_invalid_msg : 'Kindly Choose/Enter end date in DD-MM-YYYY format' : 'Kindly Choose/Enter end date in DD-MM-YYYY format';
            this.toasterService.showWarning(endDate_invalid_msg, 10000)

          }
          else if(!this.checkInternalStakeholderDates(this.stakeholders[i].start_date, this.stakeholders[i].end_date, data.startDate, data.endDate))
          {
            const dates_invalid_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.endDate_invalid_msg ? this.retrieveMessages[0].errors.endDate_invalid_msg : 'Kindly Enter Valid Start Date and End Date' : 'Kindly Enter Valid Start Date and End Date';
            this.toasterService.showWarning(dates_invalid_msg, 10000)
          }
          this.dateLogic = true;
          errorOccurred = true;
         break;


        }

        else if ((this.stakeholders[i].role === null || this.stakeholders[i].role === undefined || this.stakeholders[i].role === '') && this.isMandate('role')) {
          // const customerEmptymsg = 'Responsible Is Mandatory';
          // this.toastr.error(customerEmptymsg, 'Error');
          const role_empty = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.role_empty ? this.retrieveMessages[0].errors.role_empty : 'Role is mandatory' : 'Role is mandatory';

          this.toasterService.showWarning(role_empty, 10000)
          errorOccurred = true;
          break;
        }
        else if ((this.stakeholders[i].split === null || this.stakeholders[i].split === undefined || this.stakeholders[i].split === '') && this.isMandate('capacity')) {
          // const customerEmptymsg = 'Responsible Is Mandatory';
          // this.toastr.error(customerEmptymsg, 'Error');
          const splitPercent_empty = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.splitPercent_empty ? this.retrieveMessages[0].errors.splitPercent_empty : 'Allocation Percentage is mandatory' : 'Allocation Percentage is mandatory';

          this.toasterService.showWarning(splitPercent_empty, 10000)
          errorOccurred = true;
          break;
        }
        else if (this.stakeholders[i].split > 100 || this.stakeholders[i].split < this.split_percentage_lower_percentage) {
          if (this.stakeholders[i].split != '') {
            validateError = true
            break;
          }
        }

       
        if (!this.checkInternalStakeholderDates(this.stakeholders[i].start_date, this.stakeholders[i].end_date, data.startDate, data.endDate)) {
          this.dateLogic=false;
          errorOccurred = true;
          const enter_all_mandate = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.enter_all_mandate ? this.retrieveMessages[0].errors.enter_all_mandate : 'Enter all mandatory fields!' : 'Enter all mandatory fields!';
          
          this.toasterService.showWarning(enter_all_mandate, 10000)
          return;
        }

      }
      if (!errorOccurred && !validateError) {
        for (let items of this.stepperData) {
          if (items['type'] == 'people') {
            items['is_completed'] = true
            return true
          }
        }
      }
      // else if (!this.people_name) {
      //   const people_notMatch = this.retrieveMessages[0].errors.people_notMatch ? this.retrieveMessages[0].errors.people_notMatch ? this.retrieveMessages[0].errors.people_notMatch : 'Kindly Choose a Valid Employee name' : 'Kindly Choose a Valid Employee name';
      //   this.toastr.warning(people_notMatch, 'Warning');
      // }
      else if (!this.dateLogic) {
        const isa_dateLogic = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.isa_dateLogic ? this.retrieveMessages[0].errors.isa_dateLogic : 'Start Date cannot be greater than End date' : 'Start Date cannot be greater than End date';

        this.toasterService.showWarning(isa_dateLogic, 10000);
      }

      else if (validateError && !errorOccurred) {
        this.toasterService.showWarning("Allocation Percentage should be between " + this.split_percentage_lower_percentage + "-100", 10000);
      }
      else if (errorOccurred) {
        //this.toastr.warning("Kindly Enter All Mandatory fields", 'Warning');
        return false
      }
    }
    if (type == 'template') {
      console.log(this.selectedTemplate, this.buildFromScratch);
      if ((this.selectedTemplate !== '' && this.selectedTemplate !== null && this.selectedTemplate !== undefined) || this.buildFromScratch) {
        //errorOccurred = true;
        this.templateChosen = true
      }
      else {
        this.templateChosen = false;
      }
      console.log(this.templateChosen);
      if (this.templateChosen) {
        for (let items of this.stepperData) {
          if (items['type'] == 'template') {
            items['is_completed'] = true
            return true
          }
        }
        return true
      }
      else {
        this.toasterService.showWarning("Kindly choose any one to proceed further", 10000)
        return false
      }
    }
    if (type == 'advance') {
      return true
    }
    if (type == 'financial') {
      return true
    }
  }
  mandateAvailable(type) {
    let errorOccurred = false;
    if (type == 'details') {
      const details = _.where(this.stepperData, { type: type, is_active: true });

      if (details.length > 0) {
        if (this.isMandate('project_code')) {
          // const codeEmptymsg = 'Kindly enter Milestone name';
          // this.toastr.error(codeEmptymsg, 'Error');
          errorOccurred = true;
        }
        if (this.isMandate('project_name')) {
          // const codeEmptymsg = 'Kindly enter Milestone name';
          // this.toastr.error(codeEmptymsg, 'Error');
          errorOccurred = true;
        }
        if (this.isMandate('portfolio')) {
          // const customerEmptymsg = 'Responsible Is Mandatory';
          // this.toastr.error(customerEmptymsg, 'Error');
          errorOccurred = true;
        }
        if (this.isMandate('start_date')) {
          // const nameEmptymsg = 'Kindly choose When Is It due?';
          // this.toastr.error(nameEmptymsg, 'Error');
          errorOccurred = true;
        }
        if (this.isMandate('end_date')) {
          // const nameEmptymsg = 'Kindly choose When Is It due?';
          // this.toastr.error(nameEmptymsg, 'Error');
          errorOccurred = true;
        }
        if (this.isMandate('project_type')) {
          // const nameEmptymsg = 'Kindly choose When Is It due?';
          // this.toastr.error(nameEmptymsg, 'Error');
          errorOccurred = true;
        }
        if (this.isMandate('service_type')) {
          // const nameEmptymsg = 'Kindly choose When Is It due?';
          // this.toastr.error(nameEmptymsg, 'Error');
          errorOccurred = true;
        }
        if (this.isMandate('project_description')) {
          // const codeEmptymsg = 'Kindly enter Milestone name';
          // this.toastr.error(codeEmptymsg, 'Error');
          errorOccurred = true;
        }



      }
    }

    if (type == 'es') {
      const es = _.where(this.stepperData, { type: type, is_active: true });
      if (es.length > 0) {
        if (this.isMandate('entity')) {
          // const customerEmptymsg = 'Responsible Is Mandatory';
          // this.toastr.error(customerEmptymsg, 'Error');
          errorOccurred = true;
        }
        if (this.isMandate('division')) {
          // const customerEmptymsg = 'Responsible Is Mandatory';
          // this.toastr.error(customerEmptymsg, 'Error');
          errorOccurred = true;
        }
        if (this.isMandate('sub_division')) {
          // const customerEmptymsg = 'Responsible Is Mandatory';
          // this.toastr.error(customerEmptymsg, 'Error');
          errorOccurred = true;
        }
        if (this.isMandate('p_and_l')) {
          // const customerEmptymsg = 'Responsible Is Mandatory';
          // this.toastr.error(customerEmptymsg, 'Error');
          errorOccurred = true;
        }

      }
    }

    if (type == 'financial') {
      const financial = _.where(this.stepperData, { type: type, is_active: true });
      if (financial.length > 0) {
        if (this.isMandate('opportunity') && this.MakeFieldsNonMandatory) {
          // const customerEmptymsg = 'Responsible Is Mandatory';
          // this.toastr.error(customerEmptymsg, 'Error');
          errorOccurred = true;
        }
        if (this.isMandate('invoice_template')&& this.MakeFieldsNonMandatory) {
          // const customerEmptymsg = 'Responsible Is Mandatory';
          // this.toastr.error(customerEmptymsg, 'Error');
          errorOccurred = true;
        }
        if (this.isMandate('currency')&& this.MakeFieldsNonMandatory) {
          // const customerEmptymsg = 'Responsible Is Mandatory';
          // this.toastr.error(customerEmptymsg, 'Error');
          errorOccurred = true;
        }
        if (this.isMandate('quote')&& this.MakeFieldsNonMandatory) {
          // const codeEmptymsg = 'Kindly enter Milestone name';
          // this.toastr.error(codeEmptymsg, 'Error');
          errorOccurred = true;
        }
        if (this.isMandate('po_number')&& this.MakeFieldsNonMandatory) {
          // const codeEmptymsg = 'Kindly enter Milestone name';
          // this.toastr.error(codeEmptymsg, 'Error');
          errorOccurred = true;
        }
        if (this.isMandate('po_value')&& this.MakeFieldsNonMandatory) {
          // const codeEmptymsg = 'Kindly enter Milestone name';
          // this.toastr.error(codeEmptymsg, 'Error');
          errorOccurred = true;
        }

      }
    }

    if (type == 'schedule') {
      const schedule = _.where(this.stepperData, { type: type, is_active: true });
      if (schedule.length > 0) {
        if (this.isMandate('week')) {
          errorOccurred = true;
        }
        if (this.isMandate('work_location')) {
          // const customerEmptymsg = 'Responsible Is Mandatory';
          // this.toastr.error(customerEmptymsg, 'Error');
          errorOccurred = true;
        }
        if (this.isMandate('holiday_calendar')) {
          // const customerEmptymsg = 'Responsible Is Mandatory';
          // this.toastr.error(customerEmptymsg, 'Error');
          errorOccurred = true;
        }
        if (this.isMandate('location')) {
          // const codeEmptymsg = 'Kindly enter Milestone name';
          // this.toastr.error(codeEmptymsg, 'Error');
          errorOccurred = true;
        }
        if (this.isMandate('from')) {
          // const codeEmptymsg = 'Kindly enter Milestone name';
          // this.toastr.error(codeEmptymsg, 'Error');
          errorOccurred = true;
        }
        if (this.isMandate('to')) {
          // const codeEmptymsg = 'Kindly enter Milestone name';
          // this.toastr.error(codeEmptymsg, 'Error');
          errorOccurred = true;
        }
      }
    }

    if (type == 'people') {
      const people = _.where(this.stepperData, { type: type, is_active: true });
      if (people.length > 0) {

        if (this.isMandate('name')) {
          // const customerEmptymsg = 'Responsible Is Mandatory';
          // this.toastr.error(customerEmptymsg, 'Error');
          errorOccurred = true;
        }
        if (this.isMandate('isa_start_date')) {
          // const customerEmptymsg = 'Responsible Is Mandatory';
          // this.toastr.error(customerEmptymsg, 'Error');
          errorOccurred = true;
        }
        if (this.isMandate('isa_end_date')) {
          // const customerEmptymsg = 'Responsible Is Mandatory';
          // this.toastr.error(customerEmptymsg, 'Error');
          errorOccurred = true;
        }
        if (this.isMandate('role')) {
          // const customerEmptymsg = 'Responsible Is Mandatory';
          // this.toastr.error(customerEmptymsg, 'Error');
          errorOccurred = true;
        }
        if (this.isMandate('capacity')) {
          // const customerEmptymsg = 'Responsible Is Mandatory';
          // this.toastr.error(customerEmptymsg, 'Error');
          errorOccurred = true;
        }
      }
    }
    if (errorOccurred) {
      return false; // Return false if any error occurred
    } else {
      return true; // Return true if no errors occurred
    }
  }
  lastStepper(type) {

    const details = _.where(this.stepperData, { type: type, is_active: true, is_last_step: true })
    if (details.length > 0) {
      return true
    }
    else {
      return false
    }
  }
  onInputSplit(event: Event, i) {

    const inputValue = (event.target as HTMLInputElement).valueAsNumber;

    if (inputValue > 100) {
      this.stakeholders[i].split = 100;
    }
    else if (inputValue < 1) {
      this.stakeholders[i].split = 1
    }
    else {
      this.stakeholders[i].split = inputValue;
    }

  }
  getTemplateFromScratch() {
    this.buildFromScratch = true;
    //this.useATemplate=!this.useATemplate
    this.selectedTemplate = '';
    for (let i = 0; i < this.template_master_list.length; i++) {
      this.template_master_list[i].border_color = '1px solid white'
      this.template_master_list[i].color = 'white'
    }
    this.useATemplate = false;
    this.opentemplate = false;
    this.template_master_list = _.where(this.pmMasterService.template_master_list, { service_type: this.selectedOption, gantt_type: this.gantt_type_id });
    this.template_master_list_search = _.where(this.pmMasterService.template_master_list, { service_type: this.selectedOption, gantt_type: this.gantt_type_id });
    this.stepperFormGroup.get('textInputControl').patchValue('');
    this.stepperFormGroup.patchValue({
      'adaptTemplate':''
    })

  }
  getTemplate() {
    this.useATemplate = true;
    this.template_master_list = _.where(this.pmMasterService.template_master_list, { service_type: this.selectedOption, gantt_type: this.gantt_type_id });
    this.template_master_list_search = _.where(this.pmMasterService.template_master_list, { service_type: this.selectedOption, gantt_type: this.gantt_type_id });
    //this.buildFromScratch=!this.buildFromScratch;
    this.buildFromScratch = false;
    if (this.useATemplate == true) {
      this.opentemplate = true;
    }
    // if (this.opentemplate == false) {
    //   this.opentemplate = false;
    // }
    this.stepperFormGroup.get('textInputControl').patchValue('');
  }
  // clearTemplateSelection(){
  //   this.useATemplate = false;
  //   this.buildFromScratch = false;
  //   this.opentemplate = false;
  // }
  async getListValueFilter(name, id) {
    let entityChoosen = _.uniq(_.pluck(this.Filterdata, id))
    let val_result = []
    for (let entit of entityChoosen) {
      let result = _.where(this.Filterdata, { [id]: entit })
      if (result.length > 0) {
        val_result.push({
          "id": result[0][id],
          "name": result[0][name]
        })
      }
    }
    console.log(name, entityChoosen, val_result)
    return val_result
  }
  getTemplateSelected(id: any) {
    this.opentemplate = false;
    console.log('templateid', id)
    this.selectedTemplate = id ? id : '';
    const rowIndex = this.template_master_list.findIndex(row => row.id === this.selectedTemplate);
    this.selectedTemplateName = this.template_master_list[rowIndex].template_name;
    console.log(this.selectedTemplateName);
    this.stepperFormGroup.patchValue({ ['adaptTemplate']: this.selectedTemplate })
    this.displayTemplate = false
    for (let i = 0; i < this.template_master_list.length; i++) {
      for (let i = 0; i < this.template_master_list.length; i++) {
        if (this.template_master_list[i].id == id) {
          this.template_master_list[i].border_color = '1px solid #E8E9EE'
          this.template_master_list[i].color = '#E8E9EE'
        }
        else {
          this.template_master_list[i].border_color = '1px solid white'
          this.template_master_list[i].color = 'white'
        }
      }
    }
    console.log(this.template_master_list)
    this.displayTemplate = true
    for (let i = 0; i < this.template_master_list_search.length; i++) {
      if (this.template_master_list_search[i].id == id) {
        this.template_master_list_search[i].border_color = '1px solid #E8E9EE'
        this.template_master_list_search[i].color = '#E8E9EE'
      }
      else {
        this.template_master_list_search[i].border_color = '1px solid white'
        this.template_master_list_search[i].color = 'white'

      }
    }
  }

  unSelectTemplate(){
    this.selectedTemplate=''
    this.stepperFormGroup.patchValue({ ['adaptTemplate']: '' })
  }
  compareDate(date1: any, date2: any) {

    if ((date1 == "" || date1 == null || date1 == undefined) && (date2 == "" || date2 == null || date2 == undefined)) {
      return ''
    }
    else if (date1 == "" || date1 == null || date1 == undefined) {
      return date2
    }
    else if (date2 == "" || date2 == null || date2 == undefined) {
      return date1
    }
    else {
      date1 = moment(date1).format('YYYY-MM-DD')
      date2 = moment(date2).format('YYYY-MM-DD')
      if (date1 < date2)
        return date1
      else
        return date2
    }
  }
  compareDateMinimum(date1: any, date2: any) {

    if ((date1 == "" || date1 == null || date1 == undefined) && (date2 == "" || date2 == null || date2 == undefined)) {
      return ''
    }
    else if (date1 == "" || date1 == null || date1 == undefined) {
      return date2
    }
    else if (date2 == "" || date2 == null || date2 == undefined) {
      return date1
    }
    else {
      date1 = moment(date1).format('YYYY-MM-DD')
      date2 = moment(date2).format('YYYY-MM-DD')
      if (date1 > date2)
        return date1
      else
        return date2
    }
  }
  onInputQuote(event: Event) {

    const inputValue = (event.target as HTMLInputElement).valueAsNumber;

    if (inputValue < 1) {
      this.stepperFormGroup.patchValue({ ['quote']: 1 })
    }
    else {
      this.stepperFormGroup.patchValue({ ['quote']: inputValue })
    }

  }
  onInputPovalue(event: Event) {

    const inputValue = (event.target as HTMLInputElement).valueAsNumber;

    if (inputValue < 1) {
      this.stepperFormGroup.patchValue({ ['po_value']: 1 })
    }
   
    else {
      this.stepperFormGroup.patchValue({ ['po_value']: inputValue })
    }

  }
  async getAid(id: any, i: any) {
    // const inputValue = (event.target as HTMLInputElement).valueAsNumber;
    // console.log(inputValue)
    console.log(id)
    console.log(i)
    // console.log(this.stakeholders.length)
    // console.log(this.stakeholders[id])
    // const name = this.stakeholders[id];
    // console.log(name.employee_name);
    // console.log(this.stakeholders)
    // console.log(this.stakeholders[id].employee_name)
    // for(let i=0;i<this.stakeholders.length;i++){
    //   if(i==id){
    //     console.log(this.stakeholders[i].employee_name)
    //   }
    // }
    await this.ProjectCreationService.getDOJ(id).then((res) => {
      if (res['messType'] == 'S') {
        this.DOJ = res['data'][0].date_of_joining
        this.people_name = true;
        console.log(this.DOJ, res);
      }
      else {
        this.people_name = false;
        console.log(res)
      }
    })
  }
  quoteKeyUp(event) {
    console.log(event.target.value.toString().length)
    console.log(event.target.value.toString())
    if (this.stepperFormGroup.get('quote').value < 0) {
      const quote_negative = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.quote_negative ? this.retrieveMessages[0].errors.quote_negative : 'Quote value cannot be negative' : 'Quote value cannot be negative';
          
      this.toasterService.showWarning(quote_negative, 10000)
      this.stepperFormGroup.patchValue({ ['quote']: '' })
    }
  }
  poValueKeyUp(event) {
    console.log(event.target.value.toString().length)
    console.log(event.target.value.toString())
    if (this.stepperFormGroup.get('po_value').value < 0 && this.stepperFormGroup.get('po_value').value != '' && this.stepperFormGroup.get('po_value').value != null) {
      //this.toastr.warning('po value cannot be negative', 'Warning')
      this.stepperFormGroup.patchValue({ ['po_value']: '' })
    }
  }
  capacityKeyUp(event, i: number) {
    const inputValue = event.target.value.toString();

    // Check if the input is a valid number
    if (!/^\d+$/.test(inputValue)) {
      // If not a valid number, you can handle it accordingly (e.g., ignore or display a message)
      console.log('Invalid input. Please enter a number.');
      return;
    }

    // Convert the input value to a number
    const inputNumber = parseInt(inputValue, 10);

    // Check the range and handle accordingly
    if (inputNumber > 100) {
      const splitPercent_exceed = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.splitPercent_exceed ? this.retrieveMessages[0].errors.splitPercent_exceed : 'Utilization Percentage should be between 1 to 100!' : 'Utilization Percentage should be between 1 to 100!';
          
      this.toasterService.showWarning(splitPercent_exceed, 10000)
      this.stakeholders[i].split = 100;
    } else if (inputNumber < 1) {
      const splitPercent_exceed = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.splitPercent_exceed ? this.retrieveMessages[0].errors.splitPercent_exceed : 'Utilization Percentage should be between 1 to 100!' : 'Utilization Percentage should be between 1 to 100!';
          
      this.toasterService.showWarning(splitPercent_exceed, 10000)
      this.stakeholders[i].split = '';
    }
  }
  checkDates(start: any, end: any) {
    console.log("date validation entry")
    const startDate = moment(start);
    const endDate = moment(end);
    console.log(start, end)
    if (startDate.isValid() && endDate.isValid()) {
      if (startDate.isBefore(endDate) || startDate.isSame(endDate)) {
        this.dateLogic = true;
      }
      else {
        this.dateLogic = false;
      }
      console.log(this.dateLogic, 'valid')
    } else {
      this.dateLogic = false;
      //this.toastr.warning("Invalid Date Format", 'Warning');
      console.error('Invalid date format', this.dateLogic);
    }
  }
  initializeStepper() {
    this.stepperData[0].is_selected = true;
    this.stepperData[0].is_crossed = false;
    this.stepperData[0].is_completed = false;
    this.stepperData[0].mandate_completed = false;
    for (let i = 1; i < this.stepperData.length; i++) {
      this.stepperData[i].is_selected = false;
      this.stepperData[i].is_crossed = false;
      this.stepperData[i].mandate_completed = false;
    }
    this.stepperData = [];
    this.stepperData = _.filter(this.stepperData, { 'is_active': true });
  }
  checkStandard() {
    // if(this.standard==false){
    //   this.standard=true
    //   this.risk=false
    //   this.poNonMandate = false;
    // }
    this.toggleSecuredChecked = false
    this.standard = true
    this.risk = false
    this.poNonMandate = false;
  }
  checkAtRisk() {
    // if(this.risk==false){
    //   this.standard=false
    //   this.risk=true
    // }
    this.toggleSecuredChecked = true;
    this.standard = false
    this.risk = true
    this.poNonMandate = true;
  }
  addFinancial(): void {
    this.stepperFormGroup.value.financial = this.stepperFormGroup.get(
      'financial'
    ) as FormArray;
    this.stepperFormGroup.value.financial.push(
      this.createAcceptanceCriteria()
    );
    console.log('new', this.stepperFormGroup.value);
  }
  createAcceptanceCriteria(): FormGroup {
    return this.formBuilder.group({
      opportunity_id: '',
      quote_id: '',
      currency: '',
      invoice_template: '',
      po_number: '',
      purchase_order: '',
      opportunity_status: '',
      reason: this.reason_list.length > 0 ? this.reason_list[0].id : '',
      po_reference: '',
      partner: '',
      po_date: '',
      payment_terms: '',
      is_active: 1
    });
  }
  removeFinancial(i: any) {
    console.log(i)
    if ((this.stepperFormGroup.get('financial') as FormArray).length > 1) {
      // this.removedCriteriaRecord.push(this.contactForm.value.acceptance_criteria_arr[i].id);
      // console.log("removed criteria", this.removedCriteriaRecord);
      (this.stepperFormGroup.get('financial') as FormArray).removeAt(i);
    }
  }
  async getOpportunityData(id, i) {
    console.log(id)
    console.log(i)
    const financialArray = this.stepperFormGroup.get('financial') as FormArray;
    if (id) {
      for (let items of this.opportunity_list) {
        if (id.id == items['id']) {
          this.opportunity_status = items['status_name']
          const status = financialArray.at(i).get('opportunity_status');
          status.patchValue(items['status_name'])
          break;
        }
      }
      await this.ProjectCreationService.getFinancialValues(id.id).then((res) => {
        console.log(res)
        if (res['messType'] == 'S' && res['data'].length > 0) {
          this.financialValue = res['data']

          this.quote_id = this.financialValue[0].quote_header_id
          console.log(this.quote_id)
          const quote = financialArray.at(i).get('quote_id');
          quote.patchValue(this.quote_id)
          this.financeFieldDisable = true
          if (this.financialValue.length > 0) {

            if (this.financialValue[0].quote_amount != null || this.financialValue[0].quote_amount > 0) {
              const po_value = financialArray.at(i).get('purchase_order');
              po_value.patchValue(this.financialValue[0].quote_amount)
              // this.stepperFormGroup.patchValue({ [`po_value`]: this.financialValue[0].quote_amount || '' })

              // this.stepperFormGroup.get('quote').disable()

            }
            else {
              const po_value = financialArray.at(i).get('purchase_order');
              if (financialArray.at(i).get('purchase_order').value == null || financialArray.at(i).get('purchase_order').value == 0) {
                po_value.patchValue('')
              }
              // this.stepperFormGroup.get('quote').enable()

            }
          }
          else {
            const po_value = financialArray.at(i).get('purchase_order');
            po_value.patchValue('')
            // this.stepperFormGroup.patchValue({ ['po_value']: '' })
            // this.stepperFormGroup.get('po_value').enable()
          }
        }
        else {
          // this.stepperFormGroup.patchValue({ ['purchase_order']: '' })
          // this.stepperFormGroup.get('purchase_order').enable()
          this.stepperFormGroup.patchValue({ [`quote_id`]: '' })
          const po_value = financialArray.at(i).get('purchase_order');
          po_value.patchValue('')
          const quote = financialArray.at(i).get('quote_id');
          quote.patchValue('')
          this.financeFieldDisable = false
        }

      })

      // this.stepperFormGroup.patchValue({[`quote`]:  items['quote_id']|| ''})
      // this.stepperFormGroup.patchValue({[`po_value`]:  items['po_value'] || ''})
      // this.stepperFormGroup.patchValue({[`po_number`]: items['po_number'] || ''})



    }
    else {
      // this.stepperFormGroup.patchValue({ ['purchase_order']: '' })
      // this.stepperFormGroup.get('purchase_order').enable()
      this.stepperFormGroup.patchValue({ [`quote_id`]: '' })
      const po_value = financialArray.at(i).get('purchase_order')
      po_value.patchValue('')
      const quote = financialArray.at(i).get('quote_id');
      quote.patchValue('')
      this.financeFieldDisable = false
    }
  }

  async getParentOpportunityData(id, i) {
    console.log(id)
    console.log(i)
    const financialArray = this.stepperFormGroup.get('financial') as FormArray;
    if (id) {
      for (let items of this.opportunity_list) {
        if (id.id == items['id']) {
          this.opportunity_status = items['status_name']
          const status = financialArray.at(i).get('opportunity_status');
          status.patchValue(items['status_name'])
          break;
        }
      }
      await this.ProjectCreationService.getFinancialValues(id.id).then((res) => {
        console.log(res)
        if (res['messType'] == 'S' && res['data'].length > 0) {
          this.financialValue = res['data']

          this.quote_id = this.financialValue[0].quote_header_id
          console.log(this.quote_id)
          const quote = financialArray.at(i).get('quote_id');
          quote.patchValue(this.quote_id)
          this.financeFieldDisable = true
          if (this.financialValue.length > 0) {

            if (this.financialValue[0].quote_amount != null || this.financialValue[0].quote_amount > 0) {
              const po_value = financialArray.at(i).get('purchase_order');
              po_value.patchValue(this.financialValue[0].quote_amount)
              // this.stepperFormGroup.patchValue({ [`po_value`]: this.financialValue[0].quote_amount || '' })

              // this.stepperFormGroup.get('quote').disable()

            }
            else {
              const po_value = financialArray.at(i).get('purchase_order');
              if (financialArray.at(i).get('purchase_order').value == null || financialArray.at(i).get('purchase_order').value == 0) {
                po_value.patchValue('')
              }
              // this.stepperFormGroup.get('quote').enable()

            }
          }
          else {
            const po_value = financialArray.at(i).get('purchase_order');
            po_value.patchValue('')
            // this.stepperFormGroup.patchValue({ ['po_value']: '' })
            // this.stepperFormGroup.get('po_value').enable()
          }
        }
        else {
          // this.stepperFormGroup.patchValue({ ['purchase_order']: '' })
          // this.stepperFormGroup.get('purchase_order').enable()
          this.stepperFormGroup.patchValue({ [`quote_id`]: '' })
          const po_value = financialArray.at(i).get('purchase_order');
          po_value.patchValue('')
          const quote = financialArray.at(i).get('quote_id');
          quote.patchValue('')
          this.financeFieldDisable = false
        }

      })

      
      for (let i = 0; i < financialArray.length; i++) 
      {
        if(financialArray.controls[i].get("opportunity_id").value !=id.id)
        {
          (this.stepperFormGroup.get('financial') as FormArray).removeAt(i); 
        }
      }

  

    

      await this.ProjectCreationService.getAllChildOpportunity(id.id).then((res)=>{
        if(!res['err'])
          {
             this.child_opportunity_list = res['data']
          }
      })

      

      // this.stepperFormGroup.patchValue({[`quote`]:  items['quote_id']|| ''})
      // this.stepperFormGroup.patchValue({[`po_value`]:  items['po_value'] || ''})
      // this.stepperFormGroup.patchValue({[`po_number`]: items['po_number'] || ''})



    }
    else {
      // this.stepperFormGroup.patchValue({ ['purchase_order']: '' })
      // this.stepperFormGroup.get('purchase_order').enable()
      this.stepperFormGroup.patchValue({ [`quote_id`]: '' })
      const po_value = financialArray.at(i).get('purchase_order')
      po_value.patchValue('')
      const quote = financialArray.at(i).get('quote_id');
      quote.patchValue('')
      this.financeFieldDisable = false
    }
  }
  checkSecuredOrRisk() {
    console.log('toggle', this.toggleSecuredChecked);
    if (this.toggleSecuredChecked) {
      this.checkStandard();
    }
    else if (!this.toggleSecuredChecked) {
      this.checkAtRisk();
    }
  }
  checkOpportunity() {
    console.log('toggle', this.toggleOpportunityChecked);
    if (this.toggleOpportunityChecked) {
      this.checkWithOpportunity();
    }
    else if (!this.toggleOpportunityChecked) {
      this.checkWithoutOpportunity();
    }

  }
  checkWithoutOpportunity() {
    // if(this.withoutOpportunity==false){
    //   this.withoutOpportunity=true
    //   this.withOpportunity=false
    // }
    this.toggleOpportunityChecked = true;
    this.withoutOpportunity = true;
    this.withOpportunity = false;
    console.log('without', this.withoutOpportunity, this.withOpportunity);
  }
  checkWithOpportunity() {
    // if(this.withOpportunity==false){
    //   this.withoutOpportunity=false
    //   this.withOpportunity=true
    // }
    this.toggleOpportunityChecked = false;
    this.withOpportunity = true;
    this.withoutOpportunity = false;
    console.log('with', this.withOpportunity, this.withoutOpportunity);
  }
  sendMailNotification(index: any) {
    console.log(index);
    if(this.stakeholders[index].mail_sent == true){
      this.stakeholders[index].mail_sent = false;
    }
    else if(this.stakeholders[index].mail_sent == false){
      this.stakeholders[index].mail_sent = true;
    }
    else{
      this.stakeholders[index].mail_sent = false;
    } 
    console.log(this.stakeholders[index])
  }

  intializeProjectCode() {


    let generated_code = _.where(this.formConfig, { field_name: "project_generation_code", type: "project-creation", is_active: true })

    if (generated_code.length > 0) {
      if (generated_code[0]['label'] == "general") {
        const res = _.where(this.project_code_list, { service_type_id: 0 })

        this.stepperFormGroup.patchValue({ [`project_code`]: res.length > 0 ? res[0].projectCode : '' || '' })
      }
    }
  }
selectCheckBox(event:Event){
  console.log(this.TandM_selected)
  // const BValue = (event.target as HTMLInputElement).value;
  // this.TandM_selected=BValue
}

  getPONumberDisabledCheck() {
    if (this.risk) {
      this.stepperFormGroup.patchValue({ 'po_number': '' })

      let financialArray: any = this.stepperFormGroup.get("financial") as FormArray;

      console.log(financialArray)

      for (let i = 0; i < financialArray.length; i++) {
        financialArray.controls[i].get('po_number').setValue('');
      }

    }
    return this.risk ? true : false
  }

  getDisabledColor() {
    return (this.risk || this.withOpportunity) ? '#E8E9EE' : '#FFFFFF'
  }

  checkInternalStakeholderDates(isa_start_date, isa_end_date, start_date, end_date) {
      isa_start_date = moment(isa_start_date).format("YYYY-MM-DD")
      isa_end_date = moment(isa_end_date).format("YYYY-MM-DD")
      start_date = moment(start_date).format("YYYY-MM-DD")
      end_date = moment(end_date).format("YYYY-MM-DD")
      if(!(moment(isa_start_date).isSameOrAfter(start_date) && moment(end_date).isSameOrAfter(isa_start_date)))
      {
          console.log("Start_Date 1")
          this.dateLogic =false;
          return false;
      }
      if(!(moment(isa_end_date).isSameOrAfter(start_date) && moment(end_date).isSameOrAfter(isa_start_date)))
      { 
          console.log("Start Date 2")
          this.dateLogic =false;
          return false;
      }
      if(moment(isa_start_date).isAfter(isa_end_date))
      {
          this.dateLogic =false;
          return false;
      }

      return true;

  }

  updateDuplicateProjectCreation(){
    this.stepperFormGroup.patchValue({
      service_type_id: this.duplicate_data.service_type_id,
      legal_entity: this.duplicate_data.legal_entity,
      endDate: this.duplicate_data.planned_end_date,
      startDate: this.duplicate_data.planned_start_date,
      portfolio: this.duplicate_data.project_id,
      project_name: this.duplicate_data.item_name,
      p_and_l: this.duplicate_data.p_and_l_id,
      

    })

    this.selectOption(this.duplicate_data.service_type_id)
  }

  calculateTimeDifference(start_time, end_time)
  {
      console.log(start_time, end_time)
      if(start_time && end_time)
      {
        start_time = start_time.split(":");
        end_time = end_time.split(":");
        let startDate = new Date(0, 0, 0, start_time[0], start_time[1], 0);
        let endDate = new Date(0, 0, 0, end_time[0], end_time[1], 0);
        let diff = endDate.getTime() - startDate.getTime();
        let hours = Math.floor(diff / 1000 / 60 / 60);
        console.log(start_time, end_time, hours)
        this.stepperFormGroup.patchValue({['daily_working_horus']: hours})
      }
  }

  whetherCustomerGeneratedCode(){
    let customer_generated_code = _.where(this.formConfig, { field_name: "project_generation_code", type: "project-creation", label:"customer", is_active: true })

    if(customer_generated_code.length>0)
    {
        return true;
    }
    else
    {
        return false;
    }
  }

  async generateNewProjectCode(){
    this.refresh = true;

    let code = _.where(this.formConfig, { field_name: "project_generation_code", type: "project-creation",  is_active: true })

    if(code.length>0)
    {
        if(code[0]['label']=="customer")
        {
          await this.ProjectCreationService.getCustomerProjectCode(this.customer_id).then((res)=>{
            if(res['messType']=="S")
            {
                this.stepperFormGroup.patchValue({"project_code": res['data']})
            }
          })
        }
        else
        {
          await this.ProjectCreationService.getProjectCode().then((res: any) => {
            if (res['messType'] == 'S') {
              this.project_code_list = res['data']

            }
          })


          if(code[0]['label']=="general")
          {
              this.intializeProjectCode()
          }
          else if(code[0]['label']=="service_type")
          {
              const res = _.where(this.project_code_list, { service_type_id: this.selectedOption })

              this.stepperFormGroup.patchValue({ [`project_code`]: res.length > 0 ? res[0].projectCode : '' || '' })
              
              
          }
        }
    }

    this.refresh = false
  }
  getDisabledStatusColor(field_name, status_id) {
    let disabledConfig = _.where(this.formConfig, { type: 'project-creation', field_name: field_name, is_active: true })

    if (disabledConfig.length > 0) {
      if (_.contains(disabledConfig[0]['disabled_status'], status_id)) {
        return "#E8E9EE";
      }
      else {
        return "#FFFFFF";
      }
    }
    else {
      return "#FFFFFF";
    }
  }


  compareWOMaxDate(projectEndDate, index){

    let date1= projectEndDate;
    let date2=""
    const financial_data_without_opportunity=this.stepperFormGroup.get('fieldsArray').value

    for(let i=0;i<financial_data_without_opportunity.length;i++)
    {
        if(i == index)
        {
          date2 = financial_data_without_opportunity[i]['po_end_date']
        }
    }

    if ((date1 == "" || date1 == null || date1 == undefined) && (date2 == "" || date2 == null || date2 == undefined)) {
      return ''
    }
    else if (date1 == "" || date1 == null || date1 == undefined) {
      return date2
    }
    else if (date2 == "" || date2 == null || date2 == undefined) {
      return date1
    }
    else {
      date1 = moment(date1).format('YYYY-MM-DD')
      date2 = moment(date2).format('YYYY-MM-DD')
      if (date1 < date2)
        return date1
      else
        return date2
    }
  }

  compareWOMinDate(projectStartDate, index){
    let date1= "";
    let date2=projectStartDate
    const financial_data_without_opportunity=this.stepperFormGroup.get('fieldsArray').value

    for(let i=0;i<financial_data_without_opportunity.length;i++)
    {
        if(i == index)
        {
          date2 = financial_data_without_opportunity[i]['po_start_date']
        }
    }

    if ((date1 == "" || date1 == null || date1 == undefined) && (date2 == "" || date2 == null || date2 == undefined)) {
      return ''
    }
    else if (date1 == "" || date1 == null || date1 == undefined) {
      return date2
    }
    else if (date2 == "" || date2 == null || date2 == undefined) {
      return date1
    }
    else {
      date1 = moment(date1).format('YYYY-MM-DD')
      date2 = moment(date2).format('YYYY-MM-DD')
      if (date1 < date2)
        return date1
      else
        return date2
    }

  }
}
export function customAutocompleteScrollStrategyFactory(overlay: Overlay): () => ScrollStrategy {
  return () => overlay.scrollStrategies.reposition();
}

export interface DialogData {
  mode: any;
  data: any;
}
