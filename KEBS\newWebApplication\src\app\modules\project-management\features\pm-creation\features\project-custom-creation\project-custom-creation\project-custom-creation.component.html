<ng-container *ngIf="loading">
    <div class="empty-state">
      <img class="image" [src]="loadingGif || ''" />
      <div class="loading-wrapper">
        <div class="loading">Loading...</div>
      </div>
    </div>
  </ng-container>

  <ng-container *ngIf="!loading">
<div class="bg-container">
    <div class="sub-header">
        <div class="align-center">
            <div class="back-button" (click)="goBack()">
                <mat-icon class="back-icon">navigate_before</mat-icon>
            </div>
            <div class="align-items-column">
                <div class="title-text">
                    {{ type === 'edit-project' ? 'Edit Project' : 'Create Project' }}
                </div>
                <div class="sub-text">
                    {{ type === 'edit-project' ? 'Edit a Project' : 'Create a New Project' }}
                </div>
                
            </div>
        </div>
    </div>

    <div class="main-container">
        <div class="form-section" >
            <ng-container>
                <ng-container *ngFor="let section of activeSections; let i = index">
                    <div class="align-items-column">
                        <div
                            class="align-section-center"
                            [ngClass]="{ 'section-highlight': section?.isSelected }"
                        >
                            <div
                                class="circle"
                                [ngClass]="{ 'circle-color': section?.isVisited }"
                            ></div>
                            <div
                                class="section-text"
                                [matTooltip]="section.label"
                                [ngStyle]="{ color: section?.isSelected ? '#111434' : '' }"
                            >
                                {{ section.label }}
                            </div>
                            <div
                                *ngIf="section?.isVisited && allValid"
                            >
                                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                                    <mask id="mask0_15214_340992" style="mask-type: alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="16" height="16">
                                        <rect width="16" height="16" fill="#D9D9D9" />
                                    </mask>
                                    <g mask="url(#mask0_15214_340992)">
                                        <path d="M7.99935 11.1551C8.1519 11.1551 8.27979 11.1035 8.38302 11.0003C8.48624 10.8971 8.53785 10.7692 8.53785 10.6166C8.53785 10.4641 8.48624 10.3362 8.38302 10.233C8.27979 10.1299 8.1519 10.0783 7.99935 10.0783C7.84679 10.0783 7.7189 10.1299 7.61568 10.233C7.51246 10.3362 7.46085 10.4641 7.46085 10.6166C7.46085 10.7692 7.51246 10.8971 7.61568 11.0003C7.7189 11.1035 7.84679 11.1551 7.99935 11.1551ZM7.99952 8.7193C8.14129 8.7193 8.26002 8.67136 8.35568 8.57547C8.45146 8.47969 8.49935 8.36097 8.49935 8.2193V5.2193C8.49935 5.07764 8.45141 4.95886 8.35552 4.86297C8.25963 4.76719 8.14085 4.7193 7.99918 4.7193C7.8574 4.7193 7.73868 4.76719 7.64302 4.86297C7.54724 4.95886 7.49935 5.07764 7.49935 5.2193V8.2193C7.49935 8.36097 7.54729 8.47969 7.64318 8.57547C7.73907 8.67136 7.85785 8.7193 7.99952 8.7193ZM8.00052 14.3346C7.12452 14.3346 6.30113 14.1684 5.53035 13.836C4.75957 13.5035 4.08913 13.0524 3.51902 12.4825C2.9489 11.9126 2.49752 11.2424 2.16485 10.472C1.83229 9.70152 1.66602 8.87836 1.66602 8.00247C1.66602 7.12647 1.83224 6.30308 2.16468 5.5323C2.49713 4.76152 2.94829 4.09108 3.51818 3.52097C4.08807 2.95086 4.75824 2.49947 5.52868 2.1668C6.29913 1.83425 7.12229 1.66797 7.99818 1.66797C8.87418 1.66797 9.69757 1.83419 10.4683 2.16664C11.2391 2.49908 11.9096 2.95025 12.4797 3.52014C13.0498 4.09002 13.5012 4.76019 13.8338 5.53064C14.1664 6.30108 14.3327 7.12425 14.3327 8.00014C14.3327 8.87614 14.1665 9.69952 13.834 10.4703C13.5016 11.2411 13.0504 11.9115 12.4805 12.4816C11.9106 13.0517 11.2405 13.5031 10.47 13.8358C9.69957 14.1684 8.8764 14.3346 8.00052 14.3346ZM7.99935 13.3346C9.48824 13.3346 10.7493 12.818 11.7827 11.7846C12.816 10.7513 13.3327 9.49019 13.3327 8.0013C13.3327 6.51241 12.816 5.2513 11.7827 4.21797C10.7493 3.18464 9.48824 2.66797 7.99935 2.66797C6.51046 2.66797 5.24935 3.18464 4.21602 4.21797C3.18268 5.2513 2.66602 6.51241 2.66602 8.0013C2.66602 9.49019 3.18268 10.7513 4.21602 11.7846C5.24935 12.818 6.51046 13.3346 7.99935 13.3346Z" fill="#FF3A46" />
                                    </g>
                                </svg>
                            </div>
                        </div>
                        <div style="padding-left: 11.5%">
                            <mat-divider
                                [vertical]="true"
                                class="divider"
                                *ngIf="i !== activeSections.length - 1"
                            ></mat-divider>
                        </div>
                    </div>
                </ng-container>
            </ng-container>
        </div>
        
        <div class="form-section-fields">
            <div class="fixed-header">
                <div *ngIf="activeSections.length > 0" class="d-flex align-items-center justify-content-between">
                    <p class="form-title">
                        {{ getCurrentLabel() }}
                    </p>
                </div>
                
            
                <!-- Conditionally render the additional section when currentIndex is 2 -->
                <div class="col-12" *ngIf="currentKey === 'timelinePlanning'" style="margin-top: -15px; overflow-x: hidden;">
                    <div  class="col-12" style="display: flex; justify-content: space-between; align-items: center;">
                
                        <!-- Opportunity Toggle Section -->
                        <div class="row" *ngIf="('opportunity_toggle' | checkActive : this.formConfig: 'project-creation')" style="margin-top: 15px; white-space: nowrap; flex: 1; display: flex; align-items: center;">
                            <div class="toggle-content font-family" [ngClass]="{'toggle-content-glow': withOpportunity}">
                                {{('q2c' | checkLabel : this.formConfig: 'project-creation': 'Q2C')}}
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" [checked]="toggleOpportunityChecked" (click)="checkOpportunity()" [disabled]="!this.MakeFieldsNonMandatory || disabletoggle || globaltoggleDisable">  
                                <span class="slider"></span>
                            </label>
                            <div class="toggle-content font-family" [ngClass]="{'toggle-content-glow': withoutOpportunity}">
                                {{('o2c' | checkLabel : this.formConfig: 'project-creation': 'O2C')}}
                            </div>
                        </div>
                
                        <!-- At Risk Toggle Section -->
                        <div class="row" *ngIf="('at_risk_toggle' | checkActive : this.formConfig: 'project-creation')" style="margin-top: 15px; white-space: nowrap; flex: 1; display: flex; justify-content: flex-end; align-items: center;">
                            <div class="toggle-content font-family" [ngClass]="{'toggle-content-glow': standard}">
                                {{('secured' | checkLabel : this.formConfig: 'project-creation': 'Secured')}}
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" [checked]="toggleSecuredChecked" (click)="checkSecuredOrRisk()" [disabled]="!this.MakeFieldsNonMandatory || disabletoggle">  
                                <span class="slider"></span>
                            </label>
                            <div class="toggle-content font-family" [ngClass]="{'toggle-content-glow': risk}">
                                {{('atrisk' | checkLabel : this.formConfig: 'project-creation': 'At Risk')}}
                            </div>
                        </div>
                
                    </div>
                </div>
                <div  *ngIf="currentKey === 'resourcesAllocation'" class="col-12 people-body">
                    <div class="col-4 people-name"> {{('people' | checkLabel : this.formConfig:
                        'project-creation':
                        'Invite External Stakeholders')}}</div>
                    <div class="people-count">
                        <div class="people-count-result">{{ externalStakeholderCount }}</div>

                    </div>
                    <div class="col-12"
                        *ngIf="('invite_member' | checkActive : this.formConfig: 'project-creation')">
                        <div style="cursor: pointer !important;margin-left: 60%;" class="add_member" (click)="addStakeholder()">
                            {{('invite_member' | checkLabel : this.formConfig: 'project-creation':
                            '+ Invite Members')}}
                        </div>
                    </div>
                </div>
                
            </div>
            
            <div class="scrollable-content">
                <form [formGroup]="createJobForm" class="form">
                  <div  style="width:100%" *ngIf="currentKey === 'projectDetails'">
                    <div class="row fixed-row">
                        <div class="col-3" 
                             *ngIf="(type === 'project-creation' ? ('project_code' | checkActive : formConfig : 'project-creation') : 
                                     (type === 'opportunity-project-creation' ? ('project_code' | checkActive : formConfig : 'opportunity-project-creation') : 
                                     ('project_code' | checkActive : formConfig : 'edit-project')))">
                            <div class="content-title">
                                {{ (type === 'project-creation' ? ('project_code' | checkLabel : formConfig : 'project-creation') : 
                                   (type === 'opportunity-project-creation' ? ('project_code' | checkLabel : formConfig : 'opportunity-project-creation') : 
                                   ('project_code' | checkLabel : formConfig : 'edit-project'))) }}
                                <span class="required-star" *ngIf="('project_code' | checkMandatedField : formConfig : (type === 'project-creation' ? 'project-creation' : 
                                      (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))"> *  
                                    <div *ngIf="isFieldInvalid('project_code')" class="error-message">Required.</div>
                                </span>
                                <span *ngIf="('project_code' | checkInfoIcon : formConfig : (type === 'project-creation' ? 'project-creation' : 
                                      (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                                    <mat-icon class="info-icon" 
                                              tooltip="{{ ('project_code' | checkTooltip : formConfig : (type === 'project-creation' ? 'project-creation' : 
                                              (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project'))) }}">info_outline</mat-icon>
                                </span>
                            </div>
                            <mat-form-field class="input-field project_code" appearance="outline" 
                                            [ngStyle]="disableCode ? {'background': disableColor} : null">
                                <input class="font-family" style="color: #45546E;" matInput [readonly]="disableCode" 
                                       placeholder="Enter here" [maxlength]="(code_length ? code_length : 15)" 
                                       [required]="('project_code' | checkMandatedField : formConfig : 
                                       (type === 'project-creation' ? 'project-creation' : 
                                       (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))" 
                                       (input)="validateProjectCode($event)" formControlName="project_code" class="project-code">
                                <mat-icon *ngIf="refresh" class="refresh-icon" 
                                          tooltip="{{('renew' | checkLabel : formConfig : 
                                          (type === 'project-creation' ? 'project-creation' : 
                                          (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))}}">autorenew</mat-icon>
                                <mat-icon *ngIf="(valid && !refresh && !empty)" class="valid-icon" 
                                          tooltip="{{('valid_msg' | checkLabel : formConfig : 
                                          (type === 'project-creation' ? 'project-creation' : 
                                          (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))}}">check_circle</mat-icon>
                                <mat-icon *ngIf="(!valid && !refresh && !empty)" class="invalid-icon" 
                                          tooltip="{{('invalid_msg' | checkLabel : formConfig : 
                                          (type === 'project-creation' ? 'project-creation' : 
                                          (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))}}">error</mat-icon>
                            </mat-form-field>
                        </div>
                    
                        <div class="col-6" 
                             *ngIf="(type === 'project-creation' ? ('project_name' | checkActive : formConfig : 'project-creation') : 
                                     (type === 'opportunity-project-creation' ? ('project_name' | checkActive : formConfig : 'opportunity-project-creation') : 
                                     ('project_name' | checkActive : formConfig : 'edit-project')))">
                            <div class="content-title">
                                {{ (type === 'project-creation' ? ('project_name' | checkLabel : formConfig : 'project-creation') : 
                                   (type === 'opportunity-project-creation' ? ('project_name' | checkLabel : formConfig : 'opportunity-project-creation') : 
                                   ('project_name' | checkLabel : formConfig : 'edit-project'))) }}
                                <span class="required-star" *ngIf="('project_name' | checkMandatedField : formConfig : 
                                     (type === 'project-creation' ? 'project-creation' : 
                                     (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))"> *
                                    <div *ngIf="isFieldInvalid('project_name')" class="error-message">Required.</div>
                                </span>
                                <span *ngIf="('project_name' | checkInfoIcon : formConfig : 
                                     (type === 'project-creation' ? 'project-creation' : 
                                     (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                                    <mat-icon class="info-icon" 
                                              tooltip="{{ ('project_name' | checkTooltip : formConfig : 
                                              (type === 'project-creation' ? 'project-creation' : 
                                              (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project'))) }}">info_outline</mat-icon>
                                </span>
                            </div>
                            <mat-form-field [ngStyle]="opportunityCreation ? {'background': disableColor} : null" class="input-field project_name" appearance="outline">
                                <input class="font-family" style="color: #45546E;" 
                                       [required]="('project_name' | checkMandatedField : formConfig : 
                                       (type === 'project-creation' ? 'project-creation' : 
                                       (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))" 
                                       [maxlength]="(name_length ? name_length : 300)" [readonly]="opportunityCreation"
                                       matInput placeholder="Enter here" formControlName="project_name">
                            </mat-form-field>
                        </div>
                    
                        <div class="col-3" 
                             *ngIf="(type === 'project-creation' ? ('project_type' | checkActive : formConfig : 'project-creation') : 
                                     (type === 'opportunity-project-creation' ? ('project_type' | checkActive : formConfig : 'opportunity-project-creation') : 
                                     ('project_type' | checkActive : formConfig : 'edit-project')))">
                            <div class="content-title">
                                {{ (type === 'project-creation' ? ('project_type' | checkLabel : formConfig : 'project-creation') : 
                                   (type === 'opportunity-project-creation' ? ('project_type' | checkLabel : formConfig : 'opportunity-project-creation') : 
                                   ('project_type' | checkLabel : formConfig : 'edit-project'))) }}
                                <span class="required-star" *ngIf="('project_type' | checkMandatedField : formConfig : 
                                     (type === 'project-creation' ? 'project-creation' : 
                                     (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">*</span>
                                <div *ngIf="isFieldInvalid('project_type')" class="error-message">Required.</div>
                                <span *ngIf="('project_type' | checkInfoIcon : formConfig : 
                                     (type === 'project-creation' ? 'project-creation' : 
                                     (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                                    <mat-icon class="info-icon" 
                                              tooltip="{{ ('project_type' | checkTooltip : formConfig : 
                                              (type === 'project-creation' ? 'project-creation' : 
                                              (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project'))) }}">info_outline</mat-icon>
                                </span>
                            </div>
                            <app-input-search-name [showSelect]="false" 
                                                   [disabled]="project_type_disable" 
                                                   [required]="('project_type' | checkMandatedField : formConfig : 
                                                   (type === 'project-creation' ? 'project-creation' : 
                                                   (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))" 
                                                   class="project_type" 
                                                   [ngStyle]="{'background':project_type_color}" 
                                                   [list]="project_type_list" placeholder="Select One" 
                                                   formControlName="project_type"></app-input-search-name>
                        </div>
                    </div>         
                    <div class="row fixed-row">
                        <div class="col-3" *ngIf="(type === 'project-creation' ? ('start_date' | checkActive : formConfig : 'project-creation') : (type === 'opportunity-project-creation' ? ('start_date' | checkActive : formConfig : 'opportunity-project-creation') : ('start_date' | checkActive : formConfig : 'edit-project')))">
                            <div class="content-title">
                                {{ (type === 'project-creation' ? ('start_date' | checkLabel : formConfig : 'project-creation') : (type === 'opportunity-project-creation' ? ('start_date' | checkLabel : formConfig : 'opportunity-project-creation') : ('start_date' | checkLabel : formConfig : 'edit-project'))) }}
                                <span class="required-star" *ngIf="('start_date' | checkMandatedField : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))"> *
                                    <div *ngIf="isFieldInvalid('startDate')" class="error-message">Required.</div>
                                </span>
                                <span *ngIf="('start_date' | checkInfoIcon : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                                    <mat-icon class="info-icon" tooltip="{{('start_date' | checkTooltip : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project'))) }}">{{('info_icon' | checkLabel : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))}}</mat-icon>
                                </span>
                            </div>
                            <mat-form-field [ngStyle]="opportunityCreation ? {'background': disableColor} : null" class="input-field start_date" appearance="outline">
                                <input class="font-family" [readonly]="opportunityCreation" style="color: #45546E;" [required]="('start_date' | checkMandatedField : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))" matInput formControlName="startDate" [matDatepicker]="psdDp" [max]="createJobForm.get('endDate').value" name="startDate" placeholder="DD-MMM-YYYY" />
                                <mat-datepicker-toggle matSuffix [for]="psdDp"></mat-datepicker-toggle>
                                <mat-datepicker [panelClass]="'custom-datepicker-popup'" #psdDp></mat-datepicker>
                            </mat-form-field>
                        </div>
                    
                        <div class="col-3"  *ngIf="(type === 'project-creation' ? ('end_date' | checkActive : formConfig : 'project-creation') : (type === 'opportunity-project-creation' ? ('end_date' | checkActive : formConfig : 'opportunity-project-creation') : ('end_date' | checkActive : formConfig : 'edit-project')))">
                            <div class="content-title">
                                {{ (type === 'project-creation' ? ('end_date' | checkLabel : formConfig : 'project-creation') : (type === 'opportunity-project-creation' ? ('end_date' | checkLabel : formConfig : 'opportunity-project-creation') : ('end_date' | checkLabel : formConfig : 'edit-project'))) }}
                                <span class="required-star" *ngIf="('end_date' | checkMandatedField : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))"> *
                                    <div *ngIf="isFieldInvalid('endDate')" class="error-message">Required.</div>
                                </span>
                                <span *ngIf="('end_date' | checkInfoIcon : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                                    <mat-icon class="info-icon" tooltip="{{('end_date' | checkTooltip : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project'))) }}">{{('info_icon' | checkLabel : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))}}</mat-icon>
                                </span>
                            </div>
                            <mat-form-field class="input-field end_date" [ngStyle]="opportunityCreation ? {'background': disableColor} : null" appearance="outline">
                                <input matInput [readonly]="opportunityCreation" [matDatepicker]="picker4" name="endDate" [required]="('end_date' | checkMandatedField : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))" class="font-family" style="color: #45546E;" [min]="createJobForm.get('startDate').value" formControlName="endDate" placeholder="DD-MMM-YYYY" />
                                <mat-datepicker-toggle matSuffix [for]="picker4"></mat-datepicker-toggle>
                                <mat-datepicker #picker4></mat-datepicker>
                            </mat-form-field>
                        </div>
                        <div class="col-3"  *ngIf="(type === 'project-creation' ? ('customer_id' | checkActive : formConfig : 'project-creation') : (type === 'opportunity-project-creation' ? ('customer_id' | checkActive : formConfig : 'opportunity-project-creation') : ('customer_id' | checkActive : formConfig : 'edit-project')))">
                            <div class="content-title">
                                {{ (type === 'project-creation' ? ('customer_id' | checkLabel : formConfig : 'project-creation') : (type === 'opportunity-project-creation' ? ('customer_id' | checkLabel : formConfig : 'opportunity-project-creation') : ('customer_id' | checkLabel : formConfig : 'edit-project'))) }}
                                <span class="required-star" *ngIf="('customer_id' | checkMandatedField : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))"> *
                                    <div *ngIf="isFieldInvalid('customer_id')" class="error-message">Required.</div>
                                </span>
                                <span *ngIf="('customer_id' | checkInfoIcon : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                                    <mat-icon class="info-icon" tooltip="{{('customer_id' | checkTooltip : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))}}">info_outline</mat-icon>
                                </span>
                            </div>
                            <app-input-search-name  [ngStyle]="{
                                'background': (opportunityCreation || edit_customer) ? edit_customer_color : '#FFFFFF'
                              }" [disabled]="opportunityCreation || edit_customer" class="customer" [showSelect]="false" [required]="('customer_id' | checkMandatedField : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project'))) " [list]="customer_list" placeholder="Select One" formControlName="customer_id"></app-input-search-name>
                        </div>
                    
                        <div class="col-3"  *ngIf="(type === 'project-creation' ? ('portfolio' | checkActive : formConfig : 'project-creation') : (type === 'opportunity-project-creation' ? ('portfolio' | checkActive : formConfig : 'opportunity-project-creation') : ('portfolio' | checkActive : formConfig : 'edit-project')))">
                            <div class="content-title">
                                {{ (type === 'project-creation' ? ('portfolio' | checkLabel : formConfig : 'project-creation') : (type === 'opportunity-project-creation' ? ('portfolio' | checkLabel : formConfig : 'opportunity-project-creation') : ('portfolio' | checkLabel : formConfig : 'edit-project'))) }}
                                <span class="required-star" *ngIf="('portfolio' | checkMandatedField : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))"> *
                                    <div *ngIf="isFieldInvalid('portfolio')" class="error-message">Required.</div>
                                </span>
                                <span *ngIf="('portfolio' | checkInfoIcon : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                                    <mat-icon class="info-icon" tooltip="{{('portfolio' | checkTooltip : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))}}">info_outline</mat-icon>
                                </span>
                            </div>
                            <app-input-search-name style="margin-top: 6px;"  [disabled]="opportunityCreation || edit_customer" class="portfolio" [showSelect]="false" [required]="('portfolio' | checkMandatedField : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project'))) " [list]="project_type_list" placeholder="Select One" formControlName="portfolio"></app-input-search-name>
                        </div>                      
                    </div>                      
                        <div class="row" style="margin-top: 6px;">
                            <div class="col-12" *ngIf="(type === 'project-creation' ? ('service_type' | checkActive : formConfig : 'project-creation') : (type === 'opportunity-project-creation' ? ('service_type' | checkActive : formConfig : 'opportunity-project-creation') : ('service_type' | checkActive : formConfig : 'edit-project')))">
                                <div class="content-title">
                                    {{ (type === 'project-creation' ? ('service_type' | checkLabel : formConfig : 'project-creation') : (type === 'opportunity-project-creation' ? ('service_type' | checkLabel : formConfig : 'opportunity-project-creation') : ('service_type' | checkLabel : formConfig : 'edit-project'))) }}
                                    <span class="required-star" *ngIf="('service_type' | checkMandatedField : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))"> *</span>
                                    <div *ngIf="isFieldInvalid('portfolio')" class="error-message">Required.</div>
                                    <span *ngIf="('service_type' | checkInfoIcon : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                                        <mat-icon class="info-icon" tooltip="{{ ('service_type' | checkTooltip : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project'))) }}">
                                            info_outline
                                        </mat-icon>
                                    </span>
                                </div>
                        
                                <div *ngIf="selectedOption == 1 && (type === 'project-creation' ? ('is_blanket' | checkActive : formConfig : 'project-creation') : (type === 'opportunity-project-creation' ? ('is_blanket' | checkActive : formConfig : 'opportunity-project-creation') : ('is_blanket' | checkActive : formConfig : 'edit-project')))" style="display: flex; margin-top: -25px; position: absolute; margin-left: 72px;">
                                    <mat-checkbox class="checkbox-input" [ngModelOptions]="{standalone: true}" [(ngModel)]="TandM_selected" (change)="selectCheckBox($event)" style="margin-right: 5px;"></mat-checkbox>
                                    <div class="font-family" style="color: #8B95A5; font-size: 13px;">
                                        {{ (type === 'project-creation' ? ('is_blanket' | checkLabel : formConfig : 'project-creation') : (type === 'opportunity-project-creation' ? ('is_blanket' | checkLabel : formConfig : 'opportunity-project-creation') : ('is_blanket' | checkLabel : formConfig : 'edit-project'))) }}
                                    </div>
                                </div>
                        
                                <div style="display: flex;">
                                    <div class="button-group">
                                        <button class="cus-button" mat-button color="primary" *ngFor="let card of cards"
                                            [class.active]="card.id === selectedOption" [disabled]="disableSelectionOption" (click)="selectOption(card.id)"
                                            matTooltipPosition="above" matTooltipShowDelay="500">
                                            
                                            <div class="button-content">
                                                <h3 tooltip="{{card.name}}" *ngIf="card.id != selectedOption">{{card.name}}</h3>
                                                <h3 tooltip="{{card.name}}" *ngIf="card.id === selectedOption" [style.color]="button">{{card.name}}</h3>
                                                <div tooltip="{{card.description}}" class="para">{{card.description}}</div>
                                            </div>
                                            
                                            <div class="service-type-radio">
                                                <mat-icon *ngIf="card.id != selectedOption" class="radio-button-unchecked">radio_button_unchecked</mat-icon>
                                                <mat-icon *ngIf="card.id == selectedOption" class="radio-button-checked">radio_button_checked</mat-icon>
                                            </div>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row" style="display: flex; align-items: center;">
                            <div class="col-12"  style="margin-left: 4px;">
                                <div class="content-title">  
                                    Project Description
                                    <span class="required-star" *ngIf="('project_description' | checkMandatedField : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))"> *  <div *ngIf="isFieldInvalid('project_description')" class="error-message">Required.</div>
                                        
                                    </span>
                                    
                                    <span *ngIf="('project_description' | checkInfoIcon : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                                        <mat-icon class="info-icon" tooltip="{{ ('project_description' | checkTooltip : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project'))) }}"> 
                                            info_outline
                                        </mat-icon>
                                    </span>
                                </div>
                                <div class="row">
                                    <textarea matInput placeholder="Enter here" formControlName="project_description" [required]="('project_description' | checkMandatedField : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))" class="description col-12" [maxlength]="(description_length ? description_length : 512)" rows="4"></textarea>
                                </div>
                            </div>
                        </div>
                                   
                  </div>                      
                  <div style="width:100%;margin-top:0.5%;" *ngIf="currentKey === 'teamMembers'">
                    <div class="row fixed-row">
                        <div class="col-3"  *ngIf="(type === 'project-creation' ? ('region' | checkActive : formConfig : 'project-creation') : (type === 'opportunity-project-creation' ? ('region' | checkActive : formConfig : 'opportunity-project-creation') : ('region' | checkActive : formConfig : 'edit-project')))">
                            <div class="content-title">
                                {{ ('region' | checkLabel : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project'))) }}
                                <span class="required-star" *ngIf="('region' | checkMandatedField : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                                    * <div *ngIf="isFieldInvalid('region')" class="error-message">
                                        Required.
                                    </div>
                                </span>
                               
                                <span *ngIf="('region' | checkInfoIcon : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                                    <mat-icon class="info-icon" tooltip="{{ ('region' | checkTooltip : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project'))) }}">
                                        info_outline
                                    </mat-icon>
                                </span>
                            </div>
                            <app-input-search-name  [disabled]="opportunityCreation" class="portfolio" [showSelect]="false"
                                [required]="('region' | checkMandatedField : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))"
                                [list]="region_list" placeholder="Select One" formControlName="p_and_l">
                            </app-input-search-name>
                        </div>
                        <div class="col-3" *ngIf="(type === 'project-creation' ? ('billing_entity' | checkActive : formConfig : 'project-creation') : (type === 'opportunity-project-creation' ? ('billing_entity' | checkActive : formConfig : 'opportunity-project-creation') : ('billing_entity' | checkActive : formConfig : 'edit-project')))">
                            <div class="content-title">
                                {{ ('billing_entity' | checkLabel : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project'))) }}
                                <span class="required-star" *ngIf="('billing_entity' | checkMandatedField : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                                    *<div *ngIf="isFieldInvalid('billing_entity')" class="error-message">Required.</div>
                                </span>
                                <span *ngIf="('billing_entity' | checkInfoIcon : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                                    <mat-icon class="info-icon" tooltip="{{ ('billing_entity' | checkTooltip : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project'))) }}">
                                        info_outline
                                    </mat-icon>
                                </span>
                            </div>
                            <app-input-search-name  [disabled]="opportunityCreation" class="portfolio" [showSelect]="false"
                                [required]="('billing_entity' | checkMandatedField : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))"
                                [list]="legal_entity_list" placeholder="Select One" formControlName="legal_entity">
                            </app-input-search-name>
                        </div>
                        <div class="col-3" *ngIf="(type === 'project-creation' ? ('currency' | checkActive : formConfig : 'project-creation') : (type === 'opportunity-project-creation' ? ('currency' | checkActive : formConfig : 'opportunity-project-creation') : ('currency' | checkActive : formConfig : 'edit-project')))">
                            <div class="content-title">
                                {{ ('currency' | checkLabel : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project'))) }}
                                <span class="required-star" *ngIf="('currency' | checkMandatedField : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                                    * <div *ngIf="isFieldInvalid('currency')" class="error-message">
                                        Required.
                                    </div>
                                </span>
                               
                                <span *ngIf="('currency' | checkInfoIcon : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                                    <mat-icon class="info-icon" tooltip="{{ ('currency' | checkTooltip : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project'))) }}">
                                        info_outline
                                    </mat-icon>
                                </span>
                            </div>
                            <app-input-search-name class="portfolio"  [showSelect]="false"
                                [list]="currency_list" [ngStyle]="{
                                    'background': (opportunityCreation || edit_currency || disableCurrency) ? '#E8E9EE' : '#FFFFFF'
                                  }"
                                 [disabled]="opportunityCreation || edit_currency || disableCurrency"
                                [required]="('currency' | checkMandatedField : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))"
                                placeholder="Select One" formControlName="currency">
                            </app-input-search-name>
                        </div>
                        <div class="col-3">
                        </div>
                        
                       
                    </div>
                    <div class="row" *ngIf="('maveric_scepicif_info' | checkActive : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                        <div class="col-12 content-title2">
                            Maveric Specific Information
                             </div>
                    </div>
                    <div class="row fixed-row">
                        <div class="col-3" *ngIf="(type === 'project-creation' ? ('sow_reference_number' | checkActive : formConfig : 'project-creation') : (type === 'opportunity-project-creation' ? ('sow_reference_number' | checkActive : formConfig : 'opportunity-project-creation') : ('sow_reference_number' | checkActive : formConfig : 'edit-project')))">
                            <div class="content-title">
                                {{ ('sow_reference_number' | checkLabel : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project'))) }}
                                <span class="required-star" *ngIf="('sow_reference_number' | checkMandatedField : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                                    *<div *ngIf="isFieldInvalid('sow_reference_number')" class="error-message">
                                        Required.
                                    </div>
                                </span>
                                <span *ngIf="('sow_reference_number' | checkInfoIcon : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                                    <mat-icon class="info-icon" tooltip="{{ ('sow_reference_number' | checkTooltip : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project'))) }}">
                                        info_outline
                                    </mat-icon>
                                </span>
                            </div>
                            <mat-form-field class="input-field sow_reference_number" appearance="outline">
                                <input class="font-family" style="color: #45546E;"
                                    [required]="('sow_reference_number' | checkMandatedField : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))"
                                    [maxlength]="(name_length ? name_length : 300)"
                                    matInput placeholder="Enter here" formControlName="sow_reference_number">
                                <mat-icon *ngIf="isSowReferenceNumberRefresh" class="refresh-icon" tooltip="{{ ('renew' | checkLabel : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project'))) }}">
                                    autorenew
                                </mat-icon>
                                <mat-icon *ngIf="(sowReferenceNumberValid && !isSowReferenceNumberRefresh && !isSowReferenceNumberEmpty)" class="valid-icon" tooltip="{{ ('valid_msg' | checkLabel : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project'))) }}">
                                    check_circle
                                </mat-icon>
                                <mat-icon *ngIf="(!sowReferenceNumberValid && !isSowReferenceNumberRefresh && !isSowReferenceNumberEmpty && SowReferenceNumberDuplicated)" class="invalid-icon" tooltip="{{ sowRefDuplicatedMsg ? sowRefDuplicatedMsg : 'Sow Reference Number already exist' }}">
                                    error
                                </mat-icon>
                                <mat-icon *ngIf="(!sowReferenceNumberValid && !isSowReferenceNumberRefresh && !isSowReferenceNumberEmpty && invalidSowReferenceNumberLength)" class="invalid-icon" tooltip="{{ sowInvalidLengthMsg ? sowInvalidLengthMsg : 'Sow Reference Number length invalid' }}">
                                    error
                                </mat-icon>
                            </mat-form-field>
                        </div>
                        <div class="col-3" *ngIf="(type === 'project-creation' ? ('project_classification' | checkActive : formConfig : 'project-creation') : (type === 'opportunity-project-creation' ? ('project_classification' | checkActive : formConfig : 'opportunity-project-creation') : ('project_classification' | checkActive : formConfig : 'edit-project')))">
                            <div class="content-title">
                                {{ ('project_classification' | checkLabel : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project'))) }}
                                <span class="required-star" *ngIf="('project_classification' | checkMandatedField : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                                    * <div *ngIf="isFieldInvalid('project_classification')" class="error-message">Required.</div>
                                </span>
                                <span *ngIf="('project_classification' | checkInfoIcon : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                                    <mat-icon class="info-icon" tooltip="{{ ('project_classification' | checkTooltip : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project'))) }}">
                                        info_outline
                                    </mat-icon>
                                </span>
                            </div>
                            <app-input-search-name class="portfolio" [showSelect]="false"
                                [required]="('project_classification' | checkMandatedField : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))"
                                [list]="project_classification_list" placeholder="Select One" formControlName="project_classification">
                            </app-input-search-name>
                        </div>
                        <div class="col-3"  *ngIf="(type === 'project-creation' ? ('project_engagement' | checkActive : formConfig : 'project-creation') : (type === 'opportunity-project-creation' ? ('project_engagement' | checkActive : formConfig : 'opportunity-project-creation') : ('project_engagement' | checkActive : formConfig : 'edit-project')))">
                            <div class="content-title">
                                {{ ('project_engagement' | checkLabel : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project'))) }}
                                <span class="required-star" *ngIf="('project_engagement' | checkMandatedField : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                                    *<div *ngIf="isFieldInvalid('project_engagement')" class="error-message">Required.</div>
                                </span>
                                <span *ngIf="('project_engagement' | checkInfoIcon : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                                    <mat-icon class="info-icon" tooltip="{{ ('project_engagement' | checkTooltip : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project'))) }}">
                                        info_outline
                                    </mat-icon>
                                </span>
                            </div>
                            <app-input-search-name  class="portfolio" [showSelect]="false"
                                [required]="('project_engagement' | checkMandatedField : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))"
                                [list]="project_engagement_list" placeholder="Select One" formControlName="project_engagement">
                            </app-input-search-name>
                        </div>
                        <div class="col-3" *ngIf="(type === 'project-creation' ? ('billing_area' | checkActive : formConfig : 'project-creation') : (type === 'opportunity-project-creation' ? ('billing_area' | checkActive : formConfig : 'opportunity-project-creation') : ('billing_area' | checkActive : formConfig : 'edit-project')))">
                            <div class="content-title">
                                {{ ('billing_area' | checkLabel : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project'))) }}
                                <span class="required-star" *ngIf="('billing_area' | checkMandatedField : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                                    * <div *ngIf="isFieldInvalid('billing_area')" class="error-message">Required.</div>
                                </span>
                                <span *ngIf="('billing_area' | checkInfoIcon : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                                    <mat-icon class="info-icon" tooltip="{{ ('billing_area' | checkTooltip : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project'))) }}">
                                        info_outline
                                    </mat-icon>
                                </span>
                            </div>
                            <app-input-search-name class="portfolio" [showSelect]="false"
                                [required]="('billing_area' | checkMandatedField : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))"
                                [list]="billing_area_list" placeholder="Select One" formControlName="billing_area">
                            </app-input-search-name>
                        </div>                                 
                        
                    </div>
                    </div>                     
                <div style="width:100%" *ngIf="currentKey === 'budgetDetails'" style="margin-left: 3%;padding: 0px;" class="col-12 content">
                  <div class="col-10" style="margin-top: 10px;padding: 0px">
                      <div class="content-title" style="margin-top:9px; font-weight: bold; color: #6E7B8F;">
                          {{('schedule_week' | checkLabel : this.formConfig: 'project-creation': 'Week')}}
                          <span class="required-star"
                              *ngIf="('week' | checkMandatedField : this.formConfig: 'project-creation')">
                              *</span>
                          <span *ngIf="('week' | checkInfoIcon : this.formConfig: 'project-creation')">
                              <mat-icon class="info-icon"
                                  tooltip="{{('week' | checkTooltip : this.formConfig: 'project-creation': 'Week')}}">
                                  info_outline
                              </mat-icon>
                          </span>
                      </div>
                      <div style="cursor: pointer; height: 38px; justify-content: flex-start; align-items: flex-end; gap: 24px; display: inline-flex; margin-top: 10px;">
                        <div style="height: 38px; justify-content: flex-start; align-items: flex-start; gap: 2px; display: flex;">
                            <div *ngIf="sunday" class="week-button" (click)="checkSunday()"
                                style="flex: 1 1 0; height: 38px; padding: 16px; margin-right: 4px; box-shadow: 0px 2px 1px rgba(0, 0, 0, 0.12); justify-content: center; align-items: center; display: flex;">
                                <div class="week-days font-family"
                                    style="font-size: 13px; font-weight: 600; text-transform: capitalize; line-height: 16px; word-wrap: break-word;">
                                    S
                                </div>
                            </div>
                            <div *ngIf="!sunday" (click)="checkSunday()" class="non-week-button"
                                style="flex: 1 1 0; height: 38px; padding: 16px; margin-right: 4px; background: white; border-radius: 4px; justify-content: center; align-items: center; display: flex;">
                                <div class="font-family"
                                    style="color: #7D838B; font-size: 13px; font-weight: 600; text-transform: capitalize; line-height: 16px; word-wrap: break-word;">
                                    S
                                </div>
                            </div>
                    
                            <div *ngIf="monday" class="week-button" (click)="checkMonday()"
                                style="flex: 1 1 0; height: 38px; padding: 16px; margin-right: 4px; box-shadow: 0px 2px 1px rgba(0, 0, 0, 0.12); border-radius: 4px; justify-content: center; align-items: center; display: flex;">
                                <div class="week-days font-family"
                                    style="font-size: 13px; font-weight: 600; text-transform: capitalize; line-height: 16px; word-wrap: break-word;">
                                    M
                                </div>
                            </div>
                            <div *ngIf="!monday" class="non-week-button" (click)="checkMonday()"
                                style="flex: 1 1 0; height: 38px; padding: 16px; margin-right: 4px; background: white; border-radius: 4px; justify-content: center; align-items: center; display: flex;">
                                <div class="font-family"
                                    style="color: #7D838B; font-size: 13px; font-weight: 600; text-transform: capitalize; line-height: 16px; word-wrap: break-word;">
                                    M
                                </div>
                            </div>
                    
                            <div *ngIf="tuesday" class="week-button" (click)="checkTuesday()"
                                style="flex: 1 1 0; height: 38px; padding: 16px; margin-right: 4px; box-shadow: 0px 2px 1px rgba(0, 0, 0, 0.12); border-radius: 4px; justify-content: center; align-items: center; display: flex;">
                                <div class="week-days font-family"
                                    style="font-size: 13px; font-weight: 600; text-transform: capitalize; line-height: 16px; word-wrap: break-word;">
                                    T
                                </div>
                            </div>
                            <div *ngIf="!tuesday" class="non-week-button" (click)="checkTuesday()"
                                style="flex: 1 1 0; height: 38px; padding: 16px; margin-right: 4px; background: white; border-radius: 4px; justify-content: center; align-items: center; display: flex;">
                                <div class="font-family"
                                    style="color: #7D838B; font-size: 13px; font-weight: 600; text-transform: capitalize; line-height: 16px; word-wrap: break-word;">
                                    T
                                </div>
                            </div>
                    
                            <div *ngIf="wednesday" class="week-button" (click)="checkWednesday()"
                                style="flex: 1 1 0; height: 38px; padding: 16px; margin-right: 4px; box-shadow: 0px 2px 1px rgba(0, 0, 0, 0.12); border-radius: 4px; justify-content: center; align-items: center; display: flex;">
                                <div class="week-days font-family"
                                    style="font-size: 13px; font-weight: 600; text-transform: capitalize; line-height: 16px; word-wrap: break-word;">
                                    W
                                </div>
                            </div>
                            <div *ngIf="!wednesday" class="non-week-button" (click)="checkWednesday()"
                                style="flex: 1 1 0; height: 38px; padding: 16px; margin-right: 4px; background: white; border-radius: 4px; justify-content: center; align-items: center; display: flex;">
                                <div class="font-family"
                                    style="color: #7D838B; font-size: 13px; font-weight: 600; text-transform: capitalize; line-height: 16px; word-wrap: break-word;">
                                    W
                                </div>
                            </div>
                    
                            <div *ngIf="thursday" class="week-button" (click)="checkThursday()"
                                style="flex: 1 1 0; height: 38px; padding: 16px; margin-right: 4px; box-shadow: 0px 2px 1px rgba(0, 0, 0, 0.12); border-radius: 4px; justify-content: center; align-items: center; display: flex;">
                                <div class="week-days font-family"
                                    style="font-size: 13px; font-weight: 600; text-transform: capitalize; line-height: 16px; word-wrap: break-word;">
                                    T
                                </div>
                            </div>
                            <div *ngIf="!thursday" class="non-week-button" (click)="checkThursday()"
                                style="flex: 1 1 0; height: 38px; padding: 16px; margin-right: 4px; background: white; border-radius: 4px; justify-content: center; align-items: center; display: flex;">
                                <div class="font-family"
                                    style="color: #7D838B; font-size: 13px; font-weight: 600; text-transform: capitalize; line-height: 16px; word-wrap: break-word;">
                                    T
                                </div>
                            </div>
                    
                            <div *ngIf="friday" class="week-button" (click)="checkFriday()"
                                style="flex: 1 1 0; height: 38px; padding: 16px; margin-right: 4px; box-shadow: 0px 2px 1px rgba(0, 0, 0, 0.12); border-radius: 4px; justify-content: center; align-items: center; display: flex;">
                                <div class="week-days font-family"
                                    style="font-size: 13px; font-weight: 600; text-transform: capitalize; line-height: 16px; word-wrap: break-word;">
                                    F
                                </div>
                            </div>
                            <div *ngIf="!friday" class="non-week-button" (click)="checkFriday()"
                                style="flex: 1 1 0; height: 38px; padding: 16px; margin-right: 4px; background: white; border-radius: 4px; justify-content: center; align-items: center; display: flex;">
                                <div class="font-family"
                                    style="color: #7D838B; font-size: 13px; font-weight: 600; text-transform: capitalize; line-height: 16px; word-wrap: break-word;">
                                    F
                                </div>
                            </div>
                    
                            <div *ngIf="saturday" class="week-button" (click)="checkSaturday()"
                                style="flex: 1 1 0; height: 38px; padding: 16px; margin-right: 4px; box-shadow: 0px 2px 1px rgba(0, 0, 0, 0.12); border-radius: 4px; justify-content: center; align-items: center; display: flex;">
                                <div class="week-days font-family"
                                    style="font-size: 13px; font-weight: 600; text-transform: capitalize; line-height: 16px; word-wrap: break-word;">
                                    S
                                </div>
                            </div>
                            <div *ngIf="!saturday" class="non-week-button" (click)="checkSaturday()"
                                style="flex: 1 1 0; height: 38px; padding: 16px; background: white; border-radius: 4px; justify-content: center; align-items: center; display: flex;">
                                <div class="font-family"
                                    style="color: #7D838B; font-size: 13px; font-weight: 600; text-transform: capitalize; line-height: 16px; word-wrap: break-word;">
                                    S
                                </div>
                            </div>
                        </div>
                    </div>
                    
                  </div>
                
                  <div class="row">
                      <div class="content-title" style="font-weight: 600; color: #6E7B8F;margin-bottom:1%">
                          {{('project_working_hours' | checkLabel : this.formConfig: 'project-creation': 'Project
                          Working Hours')}}
                      </div>
                  </div>
                  <div class="row" *ngIf="('shift' | checkActive : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                    <div class="col-2 p-0 m-0 pr-3" *ngIf="('shift' | checkActive : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                        <div class="content-title schedule-header">
                            {{ ('shift' | checkLabel : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project'))) }}
                            <span class="required-star" *ngIf="('shift' | checkMandatedField : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">* <div *ngIf="isFieldInvalid('shift')" class="error-message">Required.</div></span>
                            <span *ngIf="('shift' | checkInfoIcon : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                                <mat-icon class="info-icon" tooltip="{{ ('shift' | checkTooltip : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project'))) }}">
                                    info_outline
                                </mat-icon>
                            </span>
                        </div>
                        <app-input-search-name class="input-class" [showSelect]="false" formControlName="shift"
                            [list]="shiftMasterList" placeholder="Select One" [ngtypelOptions]="{standalone: true}"
                            (change)="prefillTimeData()">
                        </app-input-search-name>
                    </div>
                </div>
                  <div class="row shift-section">
                    <div class="col-2 p-0 m-0 pr-3"  
                         *ngIf="('from' | checkActive : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                        <div class="content-title schedule-header">
                            {{ ('from' | checkLabel : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project'))) }}
                            <span class="required-star" *ngIf="('from' | checkMandatedField : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">* <div *ngIf="isFieldInvalid('from')" class="error-message">Required.</div></span>
                            <span *ngIf="('from' | checkInfoIcon : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                                <mat-icon class="info-icon" tooltip="{{ ('from' | checkTooltip : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project'))) }}">
                                    info_outline
                                </mat-icon>
                            </span>
                        </div>
                        <input formControlName="from" type="time" id="timePickerFrom"
                               class="form-control input-time" bsTimepicker [max]="to" [readonly]="shiftBasedDefault">
                    </div>              
                    <div class="col-2 p-0 m-0 pr-3"
                         *ngIf="('to' | checkActive : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                        <div class="content-title schedule-header">
                            {{ ('to' | checkLabel : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project'))) }}
                            <span class="required-star" *ngIf="('to' | checkMandatedField : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">*<div *ngIf="isFieldInvalid('to')" class="error-message">Required.</div></span>
                            <span *ngIf="('to' | checkInfoIcon : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                                <mat-icon class="info-icon" tooltip="{{ ('to' | checkTooltip : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project'))) }}">
                                    info_outline
                                </mat-icon>
                            </span>
                        </div>
                        <input formControlName="to" type="time" id="timePickerTo"
                               class="form-control input-time" [min]="from" bsTimepicker [readonly]="shiftBasedDefault">
                    </div>  
                    <div class="col-2 p-0 m-0 pr-3" 
                         *ngIf="('timezone' | checkActive : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                        <div class="content-title schedule-header">
                            {{ ('timezone' | checkLabel : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project'))) }}
                            <span class="required-star" *ngIf="('timezone' | checkMandatedField : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">*<div *ngIf="isFieldInvalid('timezone')" class="error-message">Required.</div></span>
                            <span *ngIf="('timezone' | checkInfoIcon : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                                <mat-icon class="info-icon" tooltip="{{ ('timezone' | checkTooltip : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project'))) }}">
                                    info_outline
                                </mat-icon>
                            </span>
                        </div>
                        <app-input-search-name class="timezone" [showSelect]="false"
                                               formControlName="timezone" [list]="timezoneMasterList"
                                               placeholder="Select One" [ngtypelOptions]="{standalone: true}">
                        </app-input-search-name>
                    </div>           
                    <div class="col-2 p-0 m-0 pr-3" 
                         *ngIf="('daily_working_hours' | checkActive : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                        <div class="content-title schedule-header">
                            {{ ('daily_working_hours' | checkLabel : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project'))) }}
                            <span class="required-star" *ngIf="('daily_working_hours' | checkMandatedField : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">*<div *ngIf="isFieldInvalid('daily_working_hours')" class="error-message">Required.</div></span>
                            <span *ngIf="('daily_working_hours' | checkInfoIcon : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                                <mat-icon class="info-icon" tooltip="{{ ('daily_working_hours' | checkTooltip : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project'))) }}">
                                    info_outline
                                </mat-icon>
                            </span>
                        </div>
                        <mat-form-field class="input-field daily_working_hours" appearance="outline" [ngClass]="{ 'readonly-field': shiftBasedDefault }">
                            <input class="font-family" style="color: #45546E;" type="text" digitOnly
                                   [isPercentage]="false" [allowDecimal]="true" [maxValue]="24" matInput placeholder="Enter here"
                                   formControlName="daily_working_hours" [readonly]="shiftBasedDefault">
                        </mat-form-field>
                    </div>              
                    <div class="col-2 p-0 m-0 pr-3" 
                         *ngIf="('monthly_hours' | checkActive : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                        <div class="content-title schedule-header">
                            {{ ('monthly_hours' | checkLabel : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project'))) }}
                            <span class="required-star" *ngIf="('monthly_hours' | checkMandatedField : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">*<div *ngIf="isFieldInvalid('monthly_hours')" class="error-message">Required.</div></span>
                            <span *ngIf="('monthly_hours' | checkInfoIcon : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                                <mat-icon class="info-icon" tooltip="{{ ('monthly_hours' | checkTooltip : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project'))) }}">
                                    info_outline
                                </mat-icon>
                            </span>
                        </div>
                        <mat-form-field class="input-field monthly_hours" appearance="outline" [ngClass]="{ 'readonly-field': shiftBasedDefault }">
                            <input class="font-family" style="color: #45546E;" type="text" digitOnly
                                   [isPercentage]="false" [allowDecimal]="false" matInput placeholder="Enter here"
                                   formControlName="monthly_hours" [readonly]="shiftBasedDefault">
                        </mat-form-field>
                    </div>             
                    <div class="col-2 p-0 m-0 pr-3"
                         *ngIf="('leave_paid' | checkActive : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                        <div class="content-title schedule-header">
                            {{ ('leave_paid' | checkLabel : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project'))) }}
                            <span class="required-star" *ngIf="('leave_paid' | checkMandatedField : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">*<div *ngIf="isFieldInvalid('leave_paid')" class="error-message">Required.</div></span>
                            <span *ngIf="('leave_paid' | checkInfoIcon : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                                <mat-icon class="info-icon" tooltip="{{ ('leave_paid' | checkTooltip : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project'))) }}">
                                    info_outline
                                </mat-icon>
                            </span>
                        </div>
                        <app-input-search-name class="leave_paid" [showSelect]="false"
                                               formControlName="leave_paid" [list]="yes_or_no_list"
                                               placeholder="Select One" [ngtypelOptions]="{standalone: true}">
                        </app-input-search-name>
                    </div> 
                    <div class="col-2 p-0 m-0 pr-3" 
                         *ngIf="('holiday_paid' | checkActive : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                        <div class="content-title schedule-header">
                            {{ ('holiday_paid' | checkLabel : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project'))) }}
                            <span class="required-star" *ngIf="('holiday_paid' | checkMandatedField : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">*<div *ngIf="isFieldInvalid('holiday_paid')" class="error-message">Required.</div></span>
                            <span *ngIf="('holiday_paid' | checkInfoIcon : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                                <mat-icon class="info-icon" tooltip="{{ ('holiday_paid' | checkTooltip : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project'))) }}">
                                    info_outline
                                </mat-icon>
                            </span>
                        </div>
                        <app-input-search-name class="holiday_paid" [showSelect]="false"
                                               formControlName="holiday_paid" [list]="yes_or_no_list"
                                               placeholder="Select One" [ngtypelOptions]="{standalone: true}">
                        </app-input-search-name>
                    </div>
                </div>
                <div class="row">
                    <div class="content-title"
                         style="font-weight: 600; color: #6E7B8F; margin-bottom: -2%;">
                        {{('location_overall' | checkLabel : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')) : 'Location')}}
                    </div>
                </div>
                <div formArrayName="fields">
                    <div *ngFor="let group of createJobForm.get('fields').controls; let i = index" [formGroupName]="i">
                        
                            <div class="row pt-3">
                                <div class="col-12 pl-0 ml-0">
                                    <div class="field-row">
                
                                        <!-- Work Location Field -->
                                        <div class="col-3" style="padding: 0px;" 
                                             *ngIf="('work_location' | checkActive : formConfig : type)">
                                            <div class="content-title" style="color: #6E7B8F;">
                                                {{('work_location' | checkLabel : formConfig : type)}}
                                                <span class="required-star" *ngIf="('work_location' | checkMandatedField : formConfig : type)">*</span>
                                                <span *ngIf="('work_location' | checkInfoIcon : formConfig : type)">
                                                    <mat-icon class="info-icon" tooltip="{{('work_location' | checkTooltip : formConfig : type)}}">
                                                        info_outline
                                                    </mat-icon>
                                                </span>
                                            </div>
                                            <app-input-search-name class="workLocation" [list]="work_location_list" 
                                                [showSelect]="false" 
                                                [required]="('work_location' | checkMandatedField : formConfig : type)"
                                                placeholder="Select One" formControlName="location">
                                            </app-input-search-name>
                                        </div>
                
                                        <!-- Location Field -->
                                        <div class="col-3" 
                                             *ngIf="('location' | checkActive : formConfig : type)">
                                            <div class="content-title" style="color: #6E7B8F;">
                                                {{('location' | checkLabel : formConfig : type)}}
                                                <span class="required-star" *ngIf="('location' | checkMandatedField : formConfig : type)">*</span>
                                                <span *ngIf="('location' | checkInfoIcon : formConfig : type)">
                                                    <mat-icon class="info-icon" tooltip="{{('location' | checkTooltip : formConfig : type)}}">
                                                        info_outline
                                                    </mat-icon>
                                                </span>
                                            </div>
                                            <app-input-search-name class="location" 
                                                [list]="country_list" 
                                                [required]="('location' | checkMandatedField : formConfig : type)"
                                                placeholder="Select One" formControlName="country_id">
                                            </app-input-search-name>
                                        </div>
                
                                        <!-- Holiday Calendar Field -->
                                        <div class="col-3"
                                             *ngIf="('holiday_calendar' | checkActive : formConfig : type)">
                                            <div class="content-title" style="color: #6E7B8F;">
                                                {{('holiday_calendar' | checkLabel : formConfig : type)}}
                                                <span class="required-star" *ngIf="('holiday_calendar' | checkMandatedField : formConfig : type)">*<div *ngIf="isFieldInvalid('holiday_calendar')" class="error-message">Required.</div></span>
                                                <span *ngIf="('holiday_calendar' | checkInfoIcon : formConfig : type)">
                                                    <mat-icon class="info-icon" tooltip="{{('holiday_calendar' | checkTooltip : formConfig : type)}}">
                                                        info_outline
                                                    </mat-icon>
                                                </span>
                                            </div> 
                                            <app-input-search-name class="holidayCalendar" 
                                                [list]="holiday_calendar_list" 
                                                [required]="('holiday_calendar' | checkMandatedField : formConfig : type)"
                                                placeholder="Select One" formControlName="calendar">
                                            </app-input-search-name>
                                        </div>
                                        <div class="col-3"></div>
                
                                        <!-- Remove Schedule Icon -->
                                        <div class="col-1" *ngIf="('remove_schedule' | checkActive : formConfig : type)" style="margin-top: 45px; font-size: 18px">
                                            <mat-icon class="close-button" *ngIf="fields.length > 1"  (click)="removeLocationField(i)">clear</mat-icon>
                                        </div>
                                    </div>
                                </div>
                            </div>
                      
                    </div>
                </div>
                                           
                <div class="row" *ngIf="('add_schedule' | checkActive : formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))" style="margin-left:88%;">
                  <button style="margin-left:88%" class="add-button" (click)="addField()">
                      <span class="plus-symbol">+</span>
                      <span class="button-text">Add</span>
                  </button>        
                  
                </div>
      
              </div>
              <div style="width:100%" *ngIf="currentKey === 'timelinePlanning'" class="center-body col-12 content" style="padding-bottom:15px;overflow:hidden">
                <div *ngIf="withoutOpportunity"  style="width: 840px;margin-top:20px;overflow-y: auto;">
                    <div formArrayName="fieldsArray">
                        <div *ngFor="let group of createJobForm.get('fieldsArray').controls; let i = index" [formGroupName]="i">
                            <div class="form-container">
                                <div class="row pt-3">
                                    <div class="col-12 pl-0 ml-0">             
                                        <div class="row">
                                            <div class="col-12 pl-0">          
                                                <div class="field-row">
                                                    <div class="col-3" *ngIf="('po_start_date' | checkActive : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                                                        <div class="content-title4" style="margin-top: 0px !important;">
                                                            {{ ('po_start_date' | checkLabel : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')) : 'Start Date') }}
                                                            <span class="required-star" *ngIf="('po_start_date' | checkMandatedField : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">*<div *ngIf="isFieldInvalid('po_start_date')" class="error-message">Required.</div></span>
                                                        </div>
                                                        <mat-form-field class="fieldarray" [ngStyle]="{'background': getDisabledColor()}" appearance="outline">
                                                            <input matInput placeholder="DD-MMM-YYYY" formControlName="po_start_date" [matDatepicker]="psdDp7" 
                                                                   [min]="createJobForm.get('startDate').value" [disabled]="getPONumberDisabledCheck()" [max]="createJobForm.get('endDate').value"
                                                                   [required]="('po_start_date' | checkMandatedField : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                                                            <mat-datepicker-toggle matSuffix [for]="psdDp7"></mat-datepicker-toggle>
                                                            <mat-datepicker #psdDp7></mat-datepicker>
                                                        </mat-form-field>
                                                    </div>
                    
                                                    <div class="col-3" *ngIf="('po_end_date' | checkActive : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                                                        <div class="content-title4" style="margin-top: 0px !important;">
                                                            {{ ('po_end_date' | checkLabel : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')) : 'End Date') }}
                                                            <span class="required-star" *ngIf="('po_end_date' | checkMandatedField : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">*<div *ngIf="isFieldInvalid('po_end_date')" class="error-message">Required.</div></span>
                                                        </div>
                                                        <mat-form-field class="fieldarray" [ngStyle]="{'background': getDisabledColor()}" appearance="outline">
                                                            <input matInput placeholder="DD-MMM-YYYY" formControlName="po_end_date" [matDatepicker]="psdDp8" 
                                                            [min]="createJobForm.get('fieldsArray').at(i).get('po_start_date').value" [disabled]="getPONumberDisabledCheck()" 
                                                            [disabled]="getPONumberDisabledCheck()" [max]="createJobForm.get('endDate').value"
                                                                   [required]="('po_end_date' | checkMandatedField : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                                                            <mat-datepicker-toggle matSuffix [for]="psdDp8"></mat-datepicker-toggle>
                                                            <mat-datepicker #psdDp8></mat-datepicker>
                                                        </mat-form-field>
                                                    </div>
                    
                                                    <div class="col-3"  *ngIf="('po_date' | checkActive : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                                                        <div class="content-title4" style="margin-top: 0px !important;">
                                                            {{ ('po_date' | checkLabel : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')) : 'PO Date') }}
                                                            <span class="required-star" *ngIf="('po_date' | checkMandatedField : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">*<div *ngIf="isFieldInvalid('po_date')" class="error-message">Required.</div></span>
                                                        </div>
                                                        <mat-form-field class="fieldarray" [ngStyle]="{'background': getDisabledColor()}" appearance="outline">
                                                            <input matInput placeholder="DD-MMM-YYYY" [disabled]="getPONumberDisabledCheck()" formControlName="po_date" [matDatepicker]="psdDp" 
                                                                   [min]="createJobForm.get('startDate').value" [max]="createJobForm.get('endDate').value"
                                                                   [required]="('po_date' | checkMandatedField : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                                                            <mat-datepicker-toggle matSuffix [for]="psdDp"></mat-datepicker-toggle>
                                                            <mat-datepicker #psdDp></mat-datepicker>
                                                        </mat-form-field>
                                                    </div>
                    
                                                    <div class="col-3"  *ngIf="('po_number' | checkActive : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                                                        <div class="content-title4" style="margin-top: 0px !important;">
                                                            {{ ('po_number' | checkLabel : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')) : 'PO Number') }}
                                                            <span class="required-star" *ngIf="(!poNonMandate) && MakeFieldsNonMandatory">&nbsp;* <div *ngIf="isFieldInvalid('po_number')" class="error-message">Required.</div></span>
                                                        </div>
                                                        <mat-form-field class="fieldarray" [ngStyle]="{'background': getDisabledColor()}" appearance="outline">
                                                            <input matInput placeholder="Enter here" readonly="getDisabledColor()" [required]="(!poNonMandate) && MakeFieldsNonMandatory"
                                                                   formControlName="po_number" [readonly]="getPONumberDisabledCheck()">
                                                        </mat-form-field>
                                                    </div>      
                    
                                                </div>             
                                                <div class="field-row">
                                                    <div class="col-3" *ngIf="('po_value' | checkActive : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                                                        <div class="content-title4" style="margin-top: 0px !important;">
                                                            {{ ('po_value' | checkLabel : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')) : 'Order Value') }} 
                                                            <span class="required-star" *ngIf="('po_value' | checkMandatedField : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project'))) && MakeFieldsNonMandatory">&nbsp;* <div *ngIf="isFieldInvalid('po_value')" class="error-message">Required.</div></span>
                                                        </div>
                                                        <mat-form-field class="fieldarray" [ngStyle]="{'background': getDisabledColor()}" appearance="outline">
                                                            <input matInput placeholder="Enter here" readonly="getDisabledColor()" [readonly]="getPONumberDisabledCheck()" formControlName="po_value" type="text" digitOnly 
                                                                   [allowDecimal]="true" [digitsAllowed]="po_value_digit"
                                                                   [required]="('po_value' | checkMandatedField : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project'))) && MakeFieldsNonMandatory" />
                                                        </mat-form-field>
                                                    </div>
                    
                                                    <div class="col-3"  *ngIf="('po_reference' | checkActive : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                                                        <div class="content-title4" style="margin-top: 0px !important;">
                                                            {{ ('po_reference' | checkLabel : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')) : 'PO Ref#') }}
                                                            <span class="required-star" *ngIf="('po_reference' | checkMandatedField : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">* <div *ngIf="isFieldInvalid('po_reference')" class="error-message">Required.</div></span>
                                                        </div>
                                                        <mat-form-field class="fieldarray" [ngStyle]="{'background': getDisabledColor()}" appearance="outline">
                                                            <input matInput placeholder="Enter here" readonly="getDisabledColor()" [readonly]="getPONumberDisabledCheck()" formControlName="po_reference" 
                                                                   [required]="('po_reference' | checkMandatedField : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))" />
                                                        </mat-form-field>
                                                    </div>                               
                    
                                                    <div class="col-3" *ngIf="('payment_terms' | checkActive : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                                                        <div class="content-title4" style="margin-top: 0px !important;">
                                                            {{ ('payment_terms' | checkLabel : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')) : 'Payment Terms') }}
                                                            <span class="required-star" *ngIf="('payment_terms' | checkMandatedField : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">* <div *ngIf="isFieldInvalid('payment_terms')" class="error-message">Required.</div></span>
                                                        </div>
                                                        <app-input-search-name [ngStyle]="{'background': getDisabledColor()}"  [disabled]="getPONumberDisabledCheck()" class="portfolioPay"  [list]="payment_list" placeholder="Select One" formControlName="payment_terms" [showSelect]="false"
                                                            [required]="('payment_terms' | checkMandatedField : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))"></app-input-search-name>
                                                    </div> 
                                                    <div class="col-3" *ngIf="('po_reference_2' | checkActive : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))"></div>                            
                    
                                                    <div class="col-3"  *ngIf="('invoice_template' | checkActive : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                                                        <div class="content-title4" style="margin-top: 0px !important;">
                                                            {{ ('invoice_template' | checkLabel : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')) : 'Invoice Template') }}
                                                            <span class="required-star" *ngIf="('invoice_template' | checkMandatedField : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">*</span>
                                                        </div>
                                                        <app-input-search-name [showSelect]="false" [list]="invoice_template_list" placeholder="Select One" formControlName="invoice_template"
                                                            [required]="('invoice_template' | checkMandatedField : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))"></app-input-search-name>
                                                    </div>
                    
                                                    <div   class="col-3 button-container">
                                                        <div (click)="addFields()" class="action-button add-button2">
                                                            <mat-icon>add</mat-icon>
                                                        </div>
                    
                                                        <div *ngIf="fieldsArray.length > 1" (click)="removeFields(i)" class="action-button remove-button">
                                                            <mat-icon>remove</mat-icon>
                                                        </div>
                                                    </div>
                                                </div>                   
                                            </div>
                                        </div>           
                                    </div>                                              
                                </div>                                                              
                            </div>                                                              
                        </div>
                    </div>
                                     
                  </div>
                  <div *ngIf="withOpportunity" style="width: 840px;overflow-y: auto;margin-top:20px">
                                  <div formArrayName="financial" *ngFor="
                                        let item of createJobForm.get('financial')['controls'];
                                        let i = index
                                      " >
                                      <div [formGroupName]="i">
                                        <div class="form-container">
                                            <div class="row pt-3">
                                                <div class="col-12 pl-0 ml-0">             
                                                  <div class="row">
                                                    <div class="col-12 pl-0">          
                                                        <div class="field-row" style="margin-top: -3%;">
                                                            <div class="col-4" *ngIf="('opportunity' | checkActive : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                                                                <div class="content-title1" >
                                                                    {{ ('opportunity' | checkLabel : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')) : 'Opportunity') }}
                                                                    <span class="required-star"
                                                                        *ngIf="('opportunity' | checkMandatedField : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project'))) && MakeFieldsNonMandatory">* <div *ngIf="isFieldInvalid('opportunity')" class="error-message">Required.</div></span>
                                                                    <span *ngIf="('opportunity' | checkInfoIcon : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                                                                        <mat-icon class="info-icon"
                                                                            tooltip="{{ ('opportunity' | checkTooltip : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')) : 'Opportunity') }}"
                                                                            style="margin-left: 2px !important;margin-right: -10px !important;position: relative !important">
                                                                            info_outline
                                                                        </mat-icon>
                                                                    </span>
                                                                </div>
                                                                <app-input-search-name  [showSelect]="false" class="opportunity" *ngIf="i==0 && childOpportunityFlag"
                                                                    (change)="getParentOpportunityData($event,i)" [list]="opportunity_list"
                                                                    [required]="('opportunity' | checkMandatedField : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project'))) && MakeFieldsNonMandatory"
                                                                    placeholder="Select One" formControlName="opportunity_id">
                                                                </app-input-search-name>
                                                                <app-input-search-name [showSelect]="false" [ngStyle]="{'background': (opportunityCreation && i == 0) || (type == 'edit-project' && i == 0) ? '#E8E9EE' : '#FFFFFF'}"
                                                                class="opportunity" *ngIf="!childOpportunityFlag"
                                                                    (change)="getOpportunityData($event,i)" [list]="opportunity_list"
                                                                    [disabled]="(opportunityCreation && i == 0) || (type == 'edit-project' && i == 0)"
                                                                    [matTooltip]="tooltipText"
                                                                    [required]="('opportunity' | checkMandatedField : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project'))) && MakeFieldsNonMandatory"
                                                                    placeholder="Select One" formControlName="opportunity_id">
                                                                </app-input-search-name>
                                                            </div>
                                                        
                                                            <div class="col-3" style="min-width:18%">
                                                                <div class="content-title1" style="margin-bottom: 19px !important;">
                                                                  Opportunity Status
                                                                </div>
                                                                <!-- Display the status with dynamic background color -->
                                                                <span style="display: flex;margin-top: -8%;"
                                                                  class="status-label"
                                                                  [ngStyle]="{
                                                                    'background-color': item.get('opportunity_status_color')?.value || '#6E7B8F'
                                                                  }"
                                                                >
                                                                  {{ item.get('opportunity_status_name')?.value || 'Status not found' }}
                                                                </span>
                                                              </div>
                                                     
                                                        
                                                            <div class="col-2"  *ngIf="('quote' | checkActive : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                                                                <div class="content-title1" >
                                                                    {{ ('quote' | checkLabel : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')) : 'Quote') }}
                                                                    <span class="required-star"
                                                                        *ngIf="('quote' | checkMandatedField : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">*<div *ngIf="isFieldInvalid('quote')" class="error-message">Required.</div></span>
                                                                    <span *ngIf="('quote' | checkInfoIcon : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                                                                        <mat-icon class="info-icon"
                                                                            tooltip="{{ ('quote' | checkTooltip : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')) : 'Quote') }}"
                                                                            style="margin-left: 2px !important;margin-right: -10px !important;position: relative !important">
                                                                            info_outline
                                                                        </mat-icon>
                                                                    </span>
                                                                </div>
                                                                <mat-form-field  [ngStyle]="{'background':getDisabledColor()}" class="input-field quote" appearance="outline">
                                                                    <input 
                                                                        class="font-family"
                                                                        style="color: #45546E;" digitOnly
                                                                        [isPercentage]="false" [allowDecimal]="true" matInput
                                                                        readonly="true"
                                                                        [disabled]="(('po_data_disable' | checkActive : this.formConfig : 'milestone-creation') && withOpportunity)"
                                                                        [required]="('quote' | checkMandatedField : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))"
                                                                        placeholder="Enter here" formControlName="quote_id" inputtype="numeric">
                                                                </mat-form-field>
                                                            </div>
                                                        
                                                            <div class="col-3"  *ngIf="('reason' | checkActive : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                                                                <div class="content-title1" >
                                                                    {{ ('reason' | checkLabel : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')) : 'Reason') }}
                                                                    <span class="required-star" 
                                                                        *ngIf="('reason' | checkMandatedField : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">*<div *ngIf="isFieldInvalid('reason')" class="error-message">Required.</div></span>
                                                                    <span *ngIf="('reason' | checkInfoIcon : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                                                                        <mat-icon class="info-icon"
                                                                            tooltip="{{ ('reason' | checkTooltip : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')) : 'Reason') }}"
                                                                            style="margin-left: 2px !important;margin-right: -10px !important;position: relative !important">
                                                                            info_outline
                                                                        </mat-icon>
                                                                    </span>
                                                                </div>
                                                                <app-input-search-name [showSelect]="false" [ngStyle]="{'background':getDisabledColor()}" class="reason" [list]="reason_list" [disabled]="(('po_data_disable' | checkActive : this.formConfig : 'milestone-creation') && withOpportunity)"
                                                                    [required]="('reason' | checkMandatedField : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))"
                                                                    placeholder="Select One" formControlName="reason">
                                                                </app-input-search-name>
                                                            </div>
                                                        
                                                         
                                                        </div>
                                                                   
                                                        <div class="row">
                                                            <div class="col-2"  *ngIf="('po_number' | checkActive : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                                                                <div class="content-title1" >
                                                                    {{ ('po_number_with_opportunity' | checkLabel : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')) : 'PO Number') }}
                                                                    <span class="required-star" *ngIf="('po_number_with_opportunity' | checkMandatedField : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">*<div *ngIf="isFieldInvalid('po_number_with_opportunity')" class="error-message">Required.</div></span>
                                                                    <span *ngIf="('po_number_with_opportunity' | checkInfoIcon : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                                                                        <mat-icon class="info-icon"
                                                                            tooltip="{{ ('po_number_with_opportunity' | checkTooltip : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')) : 'PO Number') }}"
                                                                            style="margin-left: 2px !important;margin-right: -10px !important;position: relative !important">
                                                                            info_outline
                                                                        </mat-icon>
                                                                    </span>
                                                                </div>
                                                                <mat-form-field class="input-field poNumber" appearance="outline" [ngStyle]="{'background':getDisabledColor()}">
                                                                    <input  readonly="true"
                                                                        class="font-family"
                                                                        style="color: #45546E;" matInput
                                                                        placeholder="Enter here" [required]="(!poNonMandate) && MakeFieldsNonMandatory" 
                                                                        [readOnly]="getPONumberDisabledCheck() || (('po_data_disable' | checkActive : this.formConfig : 'milestone-creation') && withOpportunity)"
                                                                        formControlName="po_number">
                                                                </mat-form-field>
                                                            </div>
                                                            <div class="col-2" style="margin-left: -1%;" *ngIf="('po_value_with_opportunity' | checkActive : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                                                                <div class="content-title1" >
                                                                    {{ ('po_value_with_opportunity' | checkLabel : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')) : 'Order Value') }} 
                                                                    <span class="currency-code1">({{ this.currency_code }})</span>
                                                                    <span class="required-star"
                                                                        *ngIf="('po_value_with_opportunity' | checkMandatedField : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project'))) && MakeFieldsNonMandatory">*<div *ngIf="isFieldInvalid('po_value_with_opportunity')" class="error-message">Required.</div></span>
                                                                    <span *ngIf="('po_value_with_opportunity' | checkInfoIcon : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                                                                        <mat-icon class="info-icon"
                                                                            tooltip="{{ ('po_value_with_opportunity' | checkTooltip : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')) : 'Order Value') }}"
                                                                            style="margin-left: 2px !important;margin-right: -10px !important;position: relative !important">
                                                                            info_outline
                                                                        </mat-icon>
                                                                    </span>
                                                                </div>
                                                                <mat-form-field [ngStyle]="{'background':getDisabledColor()}" class="input-field poValue" appearance="outline">
                                                                    <input  readonly="true"
                                                                        class="font-family" style="color: #45546E;" type="text" digitOnly
                                                                        [isPercentage]="false" [allowDecimal]="true" [digitsAllowed]="po_value_digit" [disabled]="(('po_data_disable' | checkActive : this.formConfig : 'milestone-creation') && withOpportunity)"
                                                                        [required]="('po_value_with_opportunity' | checkMandatedField : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project'))) && MakeFieldsNonMandatory"
                                                                        matInput placeholder="Enter here" formControlName="purchase_order" />
                                                                </mat-form-field>
                                                            </div>
                                                            
                                                            <div style="margin-left: 1%;" class="col-3"
                                                                *ngIf="('po_date' | checkActive : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                                                                <div class="content-title1" >
                                                                    {{ ('po_date' | checkLabel : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')) : 'PO Date') }}
                                                                    <span class="required-star"
                                                                        *ngIf="('po_date' | checkMandatedField : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">*<div *ngIf="isFieldInvalid('po_date')" class="error-message">Required.</div></span>
                                                                    <span *ngIf="('po_date' | checkInfoIcon : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                                                                        <mat-icon class="info-icon"
                                                                            tooltip="{{ ('po_date' | checkTooltip : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')) : 'PO Date') }}"
                                                                            style="margin-left: 2px !important;margin-right: -10px !important;position: relative !important">
                                                                            info_outline
                                                                        </mat-icon>
                                                                    </span>
                                                                </div>
                                                                <mat-form-field class="input-field po_date" appearance="outline" [ngStyle]="{'background':getDisabledColor()}">
                                                                    <input 
                                                                        class="font-family"
                                                                        style="color: #45546E;"
                                                                        [required]="('po_date' | checkMandatedField : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))"
                                                                        matInput formControlName="po_date" [matDatepicker]="psdDp"
                                                                        name="poDate" placeholder="DD-MMM-YYYY" [disabled]="(('po_data_disable' | checkActive : this.formConfig : 'milestone-creation') && withOpportunity)" />
                                                                    <mat-datepicker-toggle matSuffix [for]="psdDp"></mat-datepicker-toggle>
                                                                    <mat-datepicker #psdDp></mat-datepicker>
                                                                </mat-form-field>
                                                            </div>
                                                        
                                                            <div class="col-2" 
                                                                *ngIf="('po_reference' | checkActive : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                                                                <div class="content-title1" >
                                                                    {{ ('po_reference' | checkLabel : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')) : 'PO Ref#') }}
                                                                    <span class="required-star"
                                                                        *ngIf="('po_reference' | checkMandatedField : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">*<div *ngIf="isFieldInvalid('po_reference')" class="error-message">Required.</div></span>
                                                                    <span *ngIf="('po_reference' | checkInfoIcon : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                                                                        <mat-icon class="info-icon"
                                                                            tooltip="{{ ('po_reference' | checkTooltip : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')) : 'PO Reference') }}"
                                                                            style="margin-left: 2px !important;margin-right: -10px !important;position: relative !important">
                                                                            info_outline
                                                                        </mat-icon>
                                                                    </span>
                                                                </div>
                                                                <mat-form-field class="input-field poReference" appearance="outline">
                                                                    <input class="font-family" style="color: #45546E;" matInput
                                                                        placeholder="Enter here" formControlName="po_reference"
                                                                        [required]="('po_reference' | checkMandatedField : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                                                                </mat-form-field>
                                                            </div>
                                                        
                                                        
                                                            <div class="col-4" style="padding-left:25px"
                                                                *ngIf="('payment_terms' | checkActive : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                                                                <div class="content-title1" >
                                                                    {{ ('payment_terms' | checkLabel : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')) : 'Payment Terms') }}
                                                                    <span class="required-star"
                                                                        *ngIf="('payment_terms' | checkMandatedField : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">*<div *ngIf="isFieldInvalid('payment_terms')" class="error-message">Required.</div></span>
                                                                    <span *ngIf="('payment_terms' | checkInfoIcon : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                                                                        <mat-icon class="info-icon"
                                                                            tooltip="{{ ('payment_terms' | checkTooltip : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')) : 'Payment Terms') }}"
                                                                            style="margin-left: 2px !important;margin-right: -10px !important;position: relative !important">
                                                                            info_outline
                                                                        </mat-icon>
                                                                    </span>
                                                                </div>
                                                                <app-input-search-name  [showSelect]="false" class="paymentTerms" [list]="payment_list" [ngStyle]="{ 'background-color': (type == 'edit-project' ? '#E8E9EE' : '#FFFFFF') }"
                                                                    [required]="('payment_terms' | checkMandatedField : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))"
                                                                    placeholder="Select One" formControlName="payment_terms" [disabled]="type == 'edit-project'"
                                                                    >
                                                                </app-input-search-name>
                                                            </div>                                                 
                                                            <div class="col-1 button-container" style="margin-top: 2%" 
                                                            *ngIf="!opportunityCreation">
                                                           <div (click)="addFinancial()" class="action-button add-button2">
                                                               <mat-icon>add</mat-icon>
                                                           </div>
                                                           <div *ngIf="createJobForm.get('financial').value.length > 1 && i != 0" 
                                                                (click)="removeFinancial(i)" 
                                                                class="action-button remove-button">
                                                               <mat-icon>remove</mat-icon>
                                                           </div>
                                                       </div>
                                                       
                                                        </div>
                                                                          
                                                    </div>
                                                  </div>
                                                </div>           
                                              </div>                                              
                                        </div>                                  
                                      </div>
                                  </div>
                              </div>                        
                </div>
                <div style="display: flex;
                align-items: center;
                justify-content: center;" *ngIf="currentKey === 'riskAssessment'"  class="center-container col-12 content">
                    <button *ngIf='!useATemplate' (click)="getTemplate()" class="custom-button" mat-button
                    color="primary">
                    <div class="row d-flex justify-content-center align-items-center"
                        style="align-self: center;">
                        <mat-icon class="centered-icon">work_outline</mat-icon>
                    </div>
                    <div class="template-font">{{('use_planned_template' | checkLabel : this.formConfig:
                        'project-creation': 'Use a Template')}}</div>
                </button>
                <button *ngIf='useATemplate' (click)="getTemplate()" class="custom-button template-outLine"
                    mat-button color="primary">
                    <div class="row d-flex justify-content-center align-items-center"
                        style="align-self: center;">
                        <mat-icon class="centered-icon">check_circle</mat-icon>
                    </div>
                    <div class="row template-name">
                        <div class="template-font" [matTooltip]="selectedTemplateName"
                            style="width: max-content;">
                            {{selectedTemplateName | maxellipsis: 20}}
                        </div>
                        <mat-icon class="edit-template">
                            edit
                        </mat-icon>
                    </div>

                </button>

                <div *ngIf='opentemplate && useATemplate'
                    style="width: 235px;
                    height: 170px;
                    padding: 10px;
                    background: white;
                    border-radius: 8px;
                    flex-direction: column;
                    justify-content: flex-start;
                    align-items: flex-start;
                    gap: 15px;
                    display: inline-flex;
                    position: absolute;
                    margin-left: -285px;
                    margin-top: 85px;
                    border-color: black;
                    overflow-x: hidden;
                    overflow-y: auto;">
                    <div
                        style="width: 211px; padding-left: 12px; padding-right: 12px; padding-top: 3px; padding-bottom: 3px; border-radius: 4px; border: 1px #DADCE2 solid; justify-content: space-between; align-items: center; display: inline-flex">
                        <!-- <div style="width: 102px; color: #B9C0CA; font-size: 12px; font-family: Roboto; font-weight: 400; line-height: 16px; word-wrap: break-word">Search </div>
                          <div style="width: 16px; height: 16px; justify-content: center; align-items: center; display: flex">
                            <div style="width: 16px; height: 16px; position: relative">
                              <div style="width: 16px; height: 16px; left: 0px; top: 0px; position: absolute"></div>
                              <div style="width: 13.54px; height: 13.54px; left: 1.33px; top: 1.33px; position: absolute; background: #B9C0CA"></div>
                            </div>
                          </div> -->
                        <input style="border: none;width: 130px;outline: none; font-size: 12px;" maxlength="50"
                            formControlName="textInputControl" type="text" placeholder="Search" />
                    </div>
                    <div
                        style="flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 15px; display: flex">
                        <div class="font-family"
                            style="color: black; font-size: 12px; font-weight: 700; text-transform: capitalize; line-height: 16px; word-wrap: break-word">
                            Recommended</div>
                        <div *ngIf="displayTemplate"
                            style="flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 7px; display: flex;overflow: auto;width: 225px;height: 133px;">
                            <div *ngFor="let items of template_master_list;let i=index">
                                <div (click)="getTemplateSelected(items.id)"
                                    style="cursor: pointer;justify-content: flex-start; align-items: flex-start; gap: 4px; display: inline-flex;width: 210px;border-radius: 5px;padding-top: 2px;padding-bottom: 2px;padding-left: 4px;"
                                    [ngStyle]="{'border':items.border_color}"
                                    [ngStyle]="{'background':items.color}">
                                    <div
                                        style="padding: 4px; background: #FDE4E2; border-radius: 60px; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
                                        <div
                                            style="justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
                                            <!-- <div style="width: 8px; height: 8px; background: #D9D9D9"></div>
                                  <div style="width: 6.67px; height: 5.67px; background: #F27A6C"></div> -->
                                            <mat-icon style="font-size: 14px;
                                  width: 100%;
                                  height: 100%;
                                  color: #F27A6C;">folder_open</mat-icon>
                                        </div>
                                    </div>
                                    <div class="font-family"
                                        style="color: black; font-size: 12px;font-weight: 400; text-transform: capitalize; line-height: 16px; word-wrap: break-word;margin-top: 3px;">
                                        {{items.template_name}}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>






                <button *ngIf='!buildFromScratch' (click)="getTemplateFromScratch(); unSelectTemplate()" class="custom-button"
                    mat-button color="primary">
                    <div class="row d-flex justify-content-center align-items-center"
                        style="align-self: center;">
                        <mat-icon class="centered-icon">build</mat-icon>
                    </div>
                    <div class="template-font">{{('plan_scratch' | checkLabel : this.formConfig:
                        'project-creation': 'Build a Plan')}}</div>
                </button>
                <button *ngIf='buildFromScratch' (click)="getTemplateFromScratch(); unSelectTemplate()"
                    class="custom-button template-outLine" mat-button color="primary">
                    <!-- style="border: 2px solid #EC5F6E" -->
                    <div class="row d-flex justify-content-center align-items-center"
                        style="align-self: center;">
                        <mat-icon class="centered-icon">build</mat-icon>
                    </div>
                    <div class="template-font">{{('plan_scratch' | checkLabel : this.formConfig:
                        'project-creation': 'Build a Plan')}}</div>
                </button>
                    
                </div>
                
             
                <div style="width:100%" *ngIf="currentKey === 'resourcesAllocation'" class="col-12 content" style="overflow-y: auto;overflow-x: hidden;">
                    <div class="internal-stakeholders">
                        <div class="resources">
                            <div class="resources-name"
                                *ngIf="('name' | checkActive : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))"> 
                                {{ ('name' | checkLabel : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')) : 'Name') }} 
                                <span class="required-star" *ngIf="('name' | checkMandatedField : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">*<div *ngIf="isaRequired_associate" class="error-message">Required.</div></span>
                            </div>
                            <div class="resources-start-date"
                                *ngIf="('isa_start_date' | checkActive : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                                {{ ('isa_start_date' | checkLabel : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')) : 'Start Date') }} 
                                <span class="required-star" *ngIf="('isa_start_date' | checkMandatedField : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">*<div *ngIf="isaRequired_start" class="error-message">Required.</div></span>
                            </div>
                            <div class="resources-end-date"
     *ngIf="('isa_end_date' | checkActive : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))"
     [ngStyle]="{'margin-left': (isaRequired_end ? '3%' : '6%')}">
    {{ ('isa_end_date' | checkLabel : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')) : 'End Date') }}
    <span class="required-star" *ngIf="('isa_end_date' | checkMandatedField : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">*<div *ngIf="isaRequired_end" class="error-message">Required.</div></span>
</div>

<div class="resources-role"
     *ngIf="('role' | checkActive : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))"
     [ngStyle]="{'margin-left': (isaRequired_role ? '3.7%' : '6.7%')}">
    {{ ('role' | checkLabel : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')) : 'Role') }}
    <span class="required-star" *ngIf="('role' | checkMandatedField : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">*<div *ngIf="isaRequired_role" class="error-message">Required.</div></span>
</div>

                            <div class="resources-notif"> 
                                {{ ('isa_notification' | checkLabel : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')) : 'Notification') }} 
                        </div>
                        </div>
                        <div class="resources-body">
                            <div formArrayName="external_stakeholders">
                                <div *ngFor="let stakeholder of createJobForm.get('external_stakeholders').controls; let i = index" [formGroupName]="i" class="resources-data">
                                  <div class="resources-body-name">
                                    <div *ngIf="('name' | checkActive : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                                      <app-search-user-e360 class="employeeName" [isAutocomplete]="true"
                                        formControlName="associate_id" [label]="'Search for member'"
                                        style="width: 180px;
                                        margin-left: -12px;
                                        font-size: 9px;
                                        padding-top: 4px;"></app-search-user-e360>
                                    </div>
                              
                                    <div *ngIf="('isa_start_date' | checkActive : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                                      <mat-form-field class="input-field person-start-date" appearance="outline">
                                        <input matInput
                                               [ngModelOptions]="{standalone: true}"
                                               [min]="compareDateMinimum(createJobForm.get('startDate').value, DOJ)"
                                               [max]="compareDate(stakeholder.get('end_date').value, createJobForm.get('endDate').value)"
                                               [(ngModel)]="stakeholder.get('start_date').value"
                                               [matDatepicker]="start_date"
                                               formControlName="start_date"
                                               name="startDatePeople"
                                               placeholder="DD-MMM-YYYY" />
                                        <mat-datepicker-toggle matSuffix [for]="start_date"></mat-datepicker-toggle>
                                        <mat-datepicker #start_date></mat-datepicker>
                                      </mat-form-field>
                                    </div>
                              
                                    <div *ngIf="('isa_end_date' | checkActive : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                                      <mat-form-field class="input-field person-end-date" appearance="outline">
                                        <input matInput
                                               [ngModelOptions]="{standalone: true}"
                                               [max]="createJobForm.get('endDate').value"
                                               [min]="stakeholder.get('start_date').value || compareDateMinimum(createJobForm.get('startDate').value, DOJ)"
                                               [(ngModel)]="stakeholder.get('end_date').value"
                                               [matDatepicker]="end_date"
                                               formControlName="end_date"
                                               name="endDatePeople"
                                               placeholder="DD-MMM-YYYY" />
                                        <mat-datepicker-toggle matSuffix [for]="end_date"></mat-datepicker-toggle>
                                        <mat-datepicker #end_date></mat-datepicker>
                                      </mat-form-field>
                                    </div>
                              
                                    <div *ngIf="('role' | checkActive : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                                      <app-input-search-name [showSelect]="false"
                                        class="person-role"
                                        [ngModelOptions]="{standalone: true}"
                                        [list]="person_role_list"
                                        [disabled]="role_disable"
                                        style="font-size: 13.5px;"
                                        placeholder="Select One"
                                        formControlName="project_role_id"
                                        [(ngModel)]="stakeholder.get('project_role_id').value">
                                      </app-input-search-name>
                                    </div>
                                    <div style="margin-top: 12px; cursor: pointer; margin-left:2%;"
     (click)="sendMailNotification(i)"
     *ngIf="('send_mail' | checkActive : this.formConfig: 'project-creation')">
  <mat-icon 
      [style.color]="stakeholder.get('mail_sent').value ? '#3CB371' : '#5F6C81'"
      style="font-size: 20px;">
      {{ stakeholder.get('mail_sent').value ? 'check_circle' : 'check_circle_outline' }}
  </mat-icon>
</div>

                                   
                                    
                              
                                    <div style="margin-top: 15px; cursor: pointer;" *ngIf="this.createJobForm.get('external_stakeholders').value.length > 1" (click)="removePeopleField(i)">
                                      <mat-icon style="font-size: 20px;margin-left :85%;margin-top: -10%; color: #5F6C81;">remove_circle_outline</mat-icon>
                                    </div>
                                  </div>
                                </div>
                              </div>
                              
                            <!-- Optional: Invite member section -->
                            <!-- <div class="row">
                                <div class="col-4"></div>
                                <div class="col-4"></div>
                                <div class="col-4" *ngIf="('invite_member' | checkActive : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')))">
                                    <div class="add_member" (click)="addMemberField()">
                                        {{ ('invite_member' | checkLabel : this.formConfig : (type === 'project-creation' ? 'project-creation' : (type === 'opportunity-project-creation' ? 'opportunity-project-creation' : 'edit-project')) : '+ Invite Members') }}
                                    </div>
                                </div>
                            </div> -->
                        </div>
                    </div>
                    

                </div>     
            <div style="width:100%" *ngIf="currentKey === 'finalReview'">
              <div class="content-title col-12">
                  Tag This Project
                  <span> &nbsp;</span>
              </div>
              <app-tag style="margin-left: -23px;" [add]="add" [existingTag]="existingTag"
                       (valueEmitted)="handleValueEmitted($event)"
                       ></app-tag>
              
      
              
          </div>
                  </form>
            </div>


        </div>
    </div>
</div>
<div class="button-container" style="    margin-top: -5.5%;
margin-right: 5%;
position: relative;
z-index: 5;">
    <div class="row" style="display: flex; align-items: center; justify-content: flex-end;">
        <!-- Previous Button -->
        <mat-icon *ngIf="currentKey !== activeSections[0]?.key" class="previous-btn" (click)="goPrevious()">
            chevron_left
        </mat-icon>

        <!-- Next Button -->
        <mat-icon *ngIf="currentKey !== activeSections[activeSections.length - 1]?.key" class="next-btn" (click)="goNext()" style="margin-left: 16px;">
            chevron_right
        </mat-icon>

        <!-- Create Button -->
        <button 
        *ngIf="currentKey === activeSections[activeSections.length - 1]?.key" 
        [disabled]="save_disabled" 
        style="margin-left: 1%;" 
        class="create-button" 
        (click)="saveProjectDetails()"
        [ngClass]="{'hover-effect': !save_disabled}">
        <span class="button-text">
            {{ type === 'edit-project' ? 'Save' : 'Create Project' }}
        </span>
        
      </button>
     
      
    </div>
</div>

</ng-container>