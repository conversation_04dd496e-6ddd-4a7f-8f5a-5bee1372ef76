import {
  Component,
  OnInit,
  HostListener,
  TemplateRef,
  ViewChild,
  ViewContainerRef,
} from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { PmMasterService } from 'src/app/modules/project-management/services/pm-master.service';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Renderer2, Output, ElementRef, Inject } from '@angular/core';
import { FormArray,FormControl } from '@angular/forms';
import { startWith, debounceTime, distinctUntilChanged, map } from 'rxjs/operators';
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog';
import { MomentDateAdapter, MAT_MOMENT_DATE_ADAPTER_OPTIONS } from '@angular/material-moment-adapter';
import { DateAdapter, MAT_DATE_LOCALE, MAT_DATE_FORMATS } from '@angular/material/core';
import { Observable } from 'rxjs';
import { EventEmitter } from '@angular/core';
import * as _ from 'underscore';
import { MatButtonToggleGroup } from '@angular/material/button-toggle';
import { TagComponent } from 'src/app/modules/project-management/shared-lazy-loaded/components/tag/tag.component';
import { v4 as uuidv4 } from 'uuid';
import { LoginService } from "src/app/services/login/login.service";
import { UtilityService } from 'src/app/services/utility/utility.service';
import { ProjectService } from 'src/app/modules/projects/services/project.service';
import { MatStepper } from '@angular/material/stepper';
import { ThrowStmt } from '@angular/compiler';
import { ToastrService } from 'ngx-toastr';
import moment from 'moment';
import { MAT_AUTOCOMPLETE_SCROLL_STRATEGY } from '@angular/material/autocomplete';
import { ScrollStrategy } from '@angular/cdk/overlay';
import { Overlay } from '@angular/cdk/overlay';
import { ToasterMessageService } from 'src/app/modules/project-management/shared-lazy-loaded/components/toaster-message/toaster-message.service';
import { MatDividerModule } from '@angular/material/divider';
import {ProjectCustomCreationService} from '../services/project-custom-creation.service'
import { SubSink } from 'subsink';
import { PmLandingPageService } from 'src/app/modules/project-management/features/pm-landing-page/features/pm-project-landing-page/services/pm-landing-page.service';
import { AfterSaveDialogComponent } from 'src/app/modules/project-management/features/pm-creation/features/project-creation/after-save-dialog/after-save-dialog.component';
import { ToasterService } from 'src/app/modules/applicant-tracking-system/shared-components/ats-custom-toast/toaster.service';
import {
  InjectionToken,
} from '@angular/core';
import { GetNameByIdPipe } from 'src/app/modules/project-management/shared-lazy-loaded/pipes/get-name-by-id.pipe';
import { tickStep } from 'd3';
import { Router } from '@angular/router';
import { ComponentCanDeactivate } from 'src/app/modules/project-management/features/pm-creation/features/project-custom-creation/guard/pm-alert.guard';
export const TOASTER_EDIT_MESSAGE_SERVICE_TOKEN =
  new InjectionToken<ToasterMessageService>(
    'TOASTER_EDIT_MESSAGE_SERVICE_TOKEN'
  );
import Swal from 'sweetalert2';
import { Location } from '@angular/common';
@Component({
  selector: 'app-project-custom-creation',
  templateUrl: './project-custom-creation.component.html',
  styleUrls: ['./project-custom-creation.component.scss'],
  providers: [
    {
      provide: DateAdapter,
      useClass: MomentDateAdapter,
      deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS]
    },
    {
      provide: MAT_DATE_FORMATS,
      useValue: {
        parse: {
          dateInput: "DD-MMM-YYYY"
        },
        display: {
          dateInput: "DD-MMM-YYYY",
          monthYearLabel: "MMM YYYY"
        }
      }
    },
    {
      provide: MAT_AUTOCOMPLETE_SCROLL_STRATEGY,
      useFactory: customAutocompleteScrollStrategyFactory,
      deps: [Overlay],
    },
    GetNameByIdPipe
  ]
})
export class ProjectCustomCreationComponent implements OnInit,ComponentCanDeactivate {
  @HostListener('window:keyup.esc') onKeyUp() {
    this.onCloseClick();
  }
  @HostListener('document:keypress', ['$event'])
  handleKeyboardEvent(event: KeyboardEvent) {
    if (event.key === 'Enter') {
      event.preventDefault();
    }
  }
  @HostListener('window:beforeunload', ['$event'])
   onWindowClose(event: any): boolean {

        event.preventDefault();
        event.returnValue = 'Do you want leave without saving!';
        return false;
    
  }
  @ViewChild('weekGroup', { static: true }) weekGroup: MatButtonToggleGroup;
  @Output() closeSideNav = new EventEmitter();
  selectedValues: string[] = [];
  @Output() closeDialog: EventEmitter<void> = new EventEmitter<void>();

  @ViewChild('templateDiv') templateDiv: ElementRef;
  projectService: any;
  toggleOpportunityChecked: boolean = false;
  toggleSecuredChecked: boolean = false;
  activeDays = [false, false, false, false, false, false, false];
    checkDay(index: number) {
        this.activeDays[index] = !this.activeDays[index];
    }

    isActive(index: number): boolean {
        return this.activeDays[index];
    }
    public poForm: FormGroup;
    project_code: any;
    data: any;
    formConfig: any;
    portfolio_list: any[] = []
    invoice_template_list: any[] = []
    work_location_list: any[] = []
    from_list: any[] = []
    to_list: any[] = []
    private outsideClickInitialized = false; 
    opportunity_list: any[] = []
    child_opportunity_list: any[]=[];
    holiday_calendar_list: any[] = []
    project_type_list: any[] = []
    selectedTimeFrom: Date = new Date();
    selectedTimeTo: Date = new Date();
    selectedType: any
    showErrors: boolean = false;
    type: string = 'project-creation';
    selectedOption: string | null = null;
    currentUser: any
    aid: any
    radioChecked: boolean = false
    InternalServiceType:boolean=true;
    peopleWidth = '710px'
    invite_project_manager: any = []
    mode: any;
    opportunity_status_name: string = '';
    opportunity_status_color: string = '#6E7B8F'; // Default color
    ESdata: any;
    refresh: boolean;
    valid: boolean;
    codeDuplicated: boolean;
    empty: boolean = true
    changeInServiceType: boolean = false
    customer: any = '-'
    portfolio_name: any = '-'
    customer_id: any
    selectedTemplate: any = '';
    templateChosen: boolean = false;
    entityList: any = [];
    subDivisionList: any = [];
    divisionList: any = [];
    displayList: any = [];
    people_list: any = []
    ad_group_list: any[] = []
    person_date_list: any[] = []
    person_role_list: any[] = []
    person_split_list: any[] = []
    currency_list: any[] = []
    revenue_type_list: any=[];
    delivery_type_list: any=[];
    currencyList: any = [];
    product_category_list : any=[];
    project_classification_list:any=[];
    project_engagement_list:any=[];
    billing_area_list:any=[];
    region_list:any=[];
    split_percentage_lower_percentage: number = 1;
    subDivision: any;
    division: any;
    entity: any; 
    globaltoggleDisable:boolean=false;
    selectedData: any = []
    selectedIndex: any;
    selectedEntity: any;
    taskControl = [];
    headerDesc = "";
    searchText;
    isChecked: boolean = false;
    isHead: boolean = false;
    poepleCount: any = 1
    financeFieldDisable: boolean = false
    risk: boolean = false
    code_length: any
    description_length:any
    name_length: any;
    mailEnable: boolean = false;
    defaultValue: boolean = true
    disabletoggle:boolean=false;
    disableSelectionOption:boolean=false;
    cards: any = []
    isMinimized = false; 
    originalWidth = '1016px';
    originalHeight = '574px';
    loading: boolean = true;
    button: string = '#EE4961';
    primary: string = '#FFFFFF';
    secondary: string = '#f5f5f5'
    financialValue: any
    customer_stakeholders: any
    scheduleFormGroup: FormGroup;
    peopleValue: boolean;
    dateLogic: boolean;
    default_start_date:any;
    default_end_date:any;
    retrieveMessages: any;
    people_name: any;
    start_date_notEmpty: any;
    end_date_notEmpty: any;
    p_and_l_list: any;
    country_list: any[] = []
    mandatory_role:any=[]
    poNonMandate: boolean = false;
    shades: any;
    fontStyle: any;
    label:any;
    payment_list: any[] = [];
    childOpportunityFlag: boolean=false;
    private subs = new SubSink();
    opportunityDetails: any;
    invalidFields: string[] = []; 
    disableOpportunitySearch:boolean=false;
    opportunityId:any;
    hasAttemptedSubmit = false;
    totalMilestoneValue:number=0
    milestone_grouped_data:any=[]
    milestoneList:any=[]
    external_id: any;
    ServiceId: any;
    projectId:any;
    itemId:any;
    result: any;
    isaRequired_start:boolean=false;
    isaRequired_end:boolean=false;
    isaRequired_associate:boolean=false;
    isaRequired_role:boolean=false;
    projectFinnacialData: any = [];
    yes_or_no_list: any=[
      {
        "id":"1",
        "name":"Yes"
      },
      {
        "id":"0",
        "name":"No"
      }
    ]
  
  existingTag: any = [];
  currentIndex:any; // Assuming this is set appropriately in your code
    showExternalFields: boolean = false;

    addExternalStakeholder() {
        this.showExternalFields = !this.showExternalFields; // Toggle the visibility of the fields
    }
  add: boolean = true
  tags: any

  blankJobSections:any = [];
  
  sectionFields = {
    projectOverview: [
      { label: 'Project Name', type: 'text', required: true },
      { label: 'Description', type: 'textarea', required: false }
    ],
    teamMembers: [
      { label: 'Member Name', type: 'text', required: true },
      { label: 'Role', type: 'text', required: true }
    ],
    budgetDetails: [
      { label: 'Total Budget', type: 'number', required: true },
      { label: 'Expected Costs', type: 'number', required: false }
    ],
    timelinePlanning: [
      { label: 'Start Date', type: 'date', required: true },
      { label: 'End Date', type: 'date', required: true }
    ],
    riskAssessment: [
      { label: 'Risk Description', type: 'text', required: true },
      { label: 'Mitigation Plan', type: 'textarea', required: false }
    ],
    resourcesAllocation: [
      { label: 'Resource Name', type: 'text', required: true },
      { label: 'Quantity', type: 'number', required: true }
    ],
    finalReview: [
      { label: 'Final Comments', type: 'textarea', required: false }
    ]
  };
  dynamicHeight: string;
  dynamicSubHeight: string;
  dynamicminHeight:string;
  dynamicHiringProcessHeight: string;
  stepper_color: any;
  stepper_font: any;
  fieldOutline:any;
  scrollColor:any;
  projectImage:any;
  disableColor:any;
  createJobForm = this.formBuilder.group({
    project_code: [''],
  project_name: [''],
  portfolio: [''],
  startDate: [''],
  endDate: [''],
  quote: [''],
  project_type: [''],
  from: [''],
  to: [''],
  people: [''],
  ad_group: [''],
  person_date: [''],
  name:[''],
  person_role: [''],
  person_split: [''],
  resourceStartDate: [''],
  resourceEndDate: [''],
  invoice_template: [''],
  po_value: [0],
  currency: [''],
  opportunity: [''],
  employee_name: [''],
  customer_details: [''],
  entity: [''],
  division: [''],
  sub_division: [''],
  description: [''],
  textInputControl: [''],
  monthly_hours: [''],
  daily_working_hours:[''],
  leave_paid:[''],
  holiday_paid:[''],
  profit_center: [''],
  p_and_l: [''],
  legal_entity: [''],
  financial: new FormArray([]),
  fieldsArray: this.formBuilder.array([this.createFieldGroup()]),
  fields:this.formBuilder.array([this.createLocation()]),
  external_stakeholders: this.formBuilder.array([this.createFieldsGroup()]),
  reason: [''],
  adaptTemplate: '',
  payment_terms: [''],
  partner: [''],
  po_date: [''],
  po_reference: [''],
  sow_reference_number: [''],
  child_customer:[''],
  customer_id:[''],
  delivery_type:[''],
  revenue_type:[''],
  product_category:[''],
  project_classification:[''],
  project_engagement:[''],
  billing_area:[''],
  region:[''],
  billing_entity:[''],
  project_description:[''],
  shift: [''],
  timezone: ['']
  });
  isPopupVisible: boolean = false
  allStepperData: any = [{
    id: 1,
    data: "1",
    type: "details",
    label: "Project Details",
    is_selected: true,
    is_crossed: false,
    is_completed: false,
    is_active: true
  },
  {
    id: 2,
    data: "2",
    type: "es",
    label: "Enterprise Structure",
    is_selected: false,
    is_crossed: false,
    is_completed: false,
    is_active: true
  },
  {
    id: 3,
    data: "3",
    type: "financial",
    label: "Financial",
    is_selected: false,
    is_crossed: false,
    is_completed: false,
    is_active: true
  },
  {
    id: 4,
    data: "4",
    type: "people",
    label: "People",
    is_selected: false,
    is_crossed: false,
    is_completed: false,
    is_active: true
  },
  {
    id: 5,
    data: "5",
    type: "schedule",
    label: "Schedule",
    is_selected: false,
    is_crossed: false,
    is_completed: false,
    is_active: true
  },
  {
    id: 6,
    data: "6",
    type: "template",
    label: "Template",
    is_selected: false,
    is_crossed: false,
    is_completed: false,
    is_active: true
  },
  {
    id: 7,
    data: "7",
    type: "advance",
    label: "Advance Options",
    is_selected: false,
    is_crossed: false,
    is_completed: false,
    is_active: true
  }
  ];
  stepperData: any = []
  detailStepper: boolean = true;
  public orderValue: number
  MakeFieldsNonMandatory:boolean=true;
  enterpriseStepper: boolean = false;
  financialStepper: boolean = false;
  peopleStepper: boolean = false;
  templateStepper: boolean = false;
  scheduleStepper: boolean = false;
  advanceStepper: boolean = false;
  check: boolean = false
  displayDivision: boolean = false
  displaySubDivision: boolean = false
  displayEsList: boolean = false
  Fromminutes: any
  Fromhours: any
  Tominutes: any
  Tohours: any
  monday: boolean = false
  tuesday: boolean = false
  wednesday: boolean = false
  thursday: boolean = false
  friday: boolean = false
  saturday: boolean = false
  sunday: boolean = false
  portfolio_start: any
  portfolio_end: any
  week_array = []
  detailsSkip: boolean = false
  esSkip: boolean = false
  financialSkip: boolean = false
  scheduleSkip: boolean = false
  templateSkip: boolean = false
  peopleSkip: boolean = false
  detailsLast: boolean = false
  esLast: boolean = false
  financialLast: boolean = false
  scheduleLast: boolean = false
  templateLast: boolean = false
  peopleLast: boolean = false
  tagLast: boolean = false
  project_code_list: any
  buildFromScratch: boolean = true
  useATemplate: boolean = false
  opentemplate: boolean = false
  notMatch: boolean = true
  template_master_list: any
  template_master_list_search: any
  legal_entity_list: any;
  Filterdata: any
  loadingGif:any="https://assets.kebs.app/KEBS_MAIN_GIF.gif";
  displayTemplate: boolean = true
  opportunity_status_id: any
  item_status:any;
  project_type_disable = true;
  project_type_color: any
  default_currency: number
  project_type_default: number
  save_disabled: boolean = false
  DOJ: any
  role_disable: boolean = false
  edit_currency:any;
  edit_customer:any;
  edit_currency_color:any;
  edit_customer_color:any;
  stakeholders: any = []
  code: any
  unique_id_2: any
  modeForTag: any = 'Create'
  application_id: any = 2
  quote_id: any
  profit_center_disable = true;
  profit_center_color: any
  standard: boolean = true
  multipleQuote: boolean = true
  opportunity_status: any
  withoutOpportunity: boolean = false
  withOpportunity: boolean = false
  reason_list: any = []
  formarray: any
  opportunity_status_list: any
  currency_code: any
  added_opportunity_list: any = []
  gantt_type_id: any = 2;
  customer_list: any[] = [];
  selectedTemplateName: any;
  min_start_date: any;
  max_end_date: any;
  intergeration: number
  TandM_selected:boolean=false
  creation_mode: any;
  duplicate_data: any;
  disableCode: any;
  opportunityCreation:any;
  disableCurrency;boolean=false;
 
  default_week: any = [];
  from_default: any;
  to_default: any;
  daily_working_hours_default: any;
  monthly_hours_default: any;
  leave_paid_default: any;
  holiday_paid_default: any;
  location_default: any;
  country_default: any;
  holiday_calendar_default: any;
  child_customer:boolean=false
  child_customer_list:any=[]
  crm_external_allocation:boolean=false
  crm_internal_allocation:boolean=false
  external_stakeholders:any=[]
  po_value_digit:number=15
  account_details_autopatch:boolean = false
  isSowReferenceNumberEmpty: boolean = true;
  isSowReferenceNumberRefresh: boolean = false;
  SowReferenceNumberDuplicated: boolean = false;
  sowReferenceNumberValid:boolean = false;
  invalidSowReferenceNumberLength = false;
  sowReferenceNumberConfig: any;
  sowInvalidLengthMsg: any;
  sowRefDuplicatedMsg: any;
  loadingprimary:any="#EE4961"
  activeSections: any[] = [];
  currentKey = '';
  shiftMasterList: any = [];
  timezoneMasterList: any = [];
  shiftBasedDefault: boolean = false;
  oldData: any=[];
  oldFormValue: any;
  newData: any;
  oldFinancialData: any;
  newFinancialData: any;
  constructor(public renderer: Renderer2,private fb: FormBuilder,
    private formBuilder: FormBuilder,
    private location: Location,
    private pmMasterService: PmMasterService,
    private toastr: ToasterService,
    private toasterService: ToasterMessageService,
    private route: ActivatedRoute,
    private _router: Router,
    private pmLandingService: PmLandingPageService,
    private getNameByIdPipe: GetNameByIdPipe,
    public dialog: MatDialog, private loginService: LoginService, private utilityService: UtilityService,
    private ProjectCustomCreationService: ProjectCustomCreationService, private elementRef: ElementRef ) { 
   
  }
  canDeactivate: () => boolean | Observable<boolean>;

 async ngOnInit() {
  this.calculateDynamicContentHeight()
  
  
  await this.pmMasterService.getloaderConfig().then((res) => {
    this.loadingGif = res
  })
    await this.pmMasterService.getPMFormCustomizeConfigV().then((res) => {
      this.formConfig = res
    })
    this.route.queryParams.subscribe(params => {
      this.opportunityId = params['opportunityId'] || null; // Capture opportunityId
      console.log('Opportunity ID:', this.opportunityId); // For debugging
      this.projectId = params['projectId'] || null; // Capture projectId
      this.itemId = params['itemId'] || null; // Capture itemId
      
      console.log('Opportunity ID:', this.opportunityId); // For debugging
      console.log('Project ID:', this.projectId); // For debugging
      console.log('Item ID:', this.itemId); // For debugging
    });
    if(this.projectId && this.itemId ){
      this.type="edit-project"
      this.disabletoggle=true
      this.disableSelectionOption=true
    }

    const projectCode_config = _.where(this.formConfig, { type: "project-creation", field_name: "project_code"});
    const opportunity_config = _.where(this.formConfig, { type: "opportunity-project-creation", field_name: "disabled",is_active:true});
    const loading_config = _.where(this.formConfig, { type: "loading", field_name: "project-creation"});
    const loading_config_section = _.where(this.formConfig, { field_name: "loading_sections", type: "project-creation"});
    const global_disable = _.where(this.formConfig, { type: "project-creation", field_name: "toggle",is_active:true});
    const weekConfig = _.where(this.formConfig, { type: "project-creation", field_name: "week"});
    if(weekConfig.length > 0){
      this.default_week = weekConfig[0].default_value ? weekConfig[0].default_value : [1, 2, 3, 4, 5];
    }
    console.log(loading_config_section)
    if(loading_config_section && loading_config_section.length > 0){
      this.blankJobSections=loading_config_section[0].config
      if(this.type=="project-creation"){
        this.week_array = this.default_week.length > 0 ? this.default_week : [1, 2, 3, 4, 5];
        this.monday=true;
        this.tuesday=true;
        this.wednesday=true;
        this.thursday=true;
        this.friday=true;
        this.saturday=false;
        this.sunday=false;
        if(global_disable && global_disable.length>0){
          this.globaltoggleDisable=true

        }
      this.activeSections = this.blankJobSections.filter(section => section.is_active);}
      else{
        this.activeSections = this.blankJobSections.filter(section => section.is_active && section.is_edit);

      }
      this.currentKey = this.activeSections[0]?.key;
        this.selectSection(this.currentKey);
  }
    if(loading_config && loading_config.length > 0){
      this.loadingGif=loading_config.loading_gif
      this.loadingprimary=loading_config.primary
      document.documentElement.style.setProperty('--loadingprimary', this.loadingprimary)
  }
    if( projectCode_config.length > 0){
        this.disableCode = projectCode_config[0].disable;
    }
    const retrieveStyles = _.where(this.formConfig, { type: "project-theme", field_name: "styles", is_active: true });
    this.button = retrieveStyles.length > 0 ? retrieveStyles[0].data.button_color ? retrieveStyles[0].data.button_color : "#90ee90" : "#90ee90";
    this.shades = retrieveStyles.length > 0 ? retrieveStyles[0].data.shades_color ? retrieveStyles[0].data.shades_color : "#C9E3B4" : "#C9E3B4";
    this.projectImage = retrieveStyles.length > 0 ? retrieveStyles[0].data.project_image ? retrieveStyles[0].data.project_image : "https://assets.kebs.app/MicrosoftTeams-image%20(16).png" : "https://assets.kebs.app/MicrosoftTeams-image%20(16).png";
    this.disableColor = retrieveStyles.length > 0 ? retrieveStyles[0].data.disabled_color ? retrieveStyles[0].data.disabled_color : "#E8E9EE !important" : "#E8E9EE !important";
    this.fieldOutline = retrieveStyles.length > 0 ? retrieveStyles[0].data.field_outline_color ? retrieveStyles[0].data.field_outline_color : "#808080" : "#808080";
    document.documentElement.style.setProperty('--shades', this.shades)
    document.documentElement.style.setProperty('--button', this.button)
    document.documentElement.style.setProperty('--projectFieldOutline', this.fieldOutline)
    this.fontStyle = retrieveStyles.length > 0 ? retrieveStyles[0].data.font_style ? retrieveStyles[0].data.font_style : "Roboto" : "Roboto";
    document.documentElement.style.setProperty('--projectFont', this.fontStyle);
    this.scrollColor = retrieveStyles.length > 0 ? retrieveStyles[0].data.scroll_color ? retrieveStyles[0].data.scroll_color : "#90ee90" : "#90ee90";
    document.documentElement.style.setProperty('--project2projectScroll', this.scrollColor)

    let split_percentage_lower_percentage = _.findWhere(this.formConfig, { type: "add-member", field_name: 'split_percentage_lower_percentage', is_active: true })
    this.split_percentage_lower_percentage = split_percentage_lower_percentage ? split_percentage_lower_percentage['label'] : 1

    

    const fromTime = _.where(this.formConfig, { type: "project-creation", field_name: "from"});
    if(fromTime.length > 0){
      this.from_default = fromTime[0].default_value ? fromTime[0].default_value : '9.00';
    }

    const shift_disable_data = _.where(this.formConfig, { type: "project-creation", field_name: "shift_based_disable",is_active:true});
    if(shift_disable_data.length > 0){
      this.shiftBasedDefault = shift_disable_data[0].is_disable ? shift_disable_data[0].is_disable : false;
    }

    const toTime = _.where(this.formConfig, { type: "project-creation", field_name: "to"});
    if(toTime.length > 0){
      this.to_default = toTime[0].default_value ? toTime[0].default_value : '18.00';
    }
    if(this.type=="edit-project"){
    const edit_currency_config = _.where(this.formConfig, {
      type: 'edit-project',
      field_name: 'currency',
      is_active: true,
    });
    this.edit_currency = edit_currency_config.length > 0 ? edit_currency_config[0].disable : true;
    this.edit_currency_color = edit_currency_config.length > 0 ? edit_currency_config[0].color : '#E8E9EE';
    const edit_customer_config = _.where(this.formConfig, {
      type: 'edit-project',
      field_name: 'customer_id',
      is_active: true,
    });
    this.edit_customer = edit_customer_config.length > 0 ? edit_customer_config[0].disable : true;
    this.edit_customer_color = edit_customer_config.length > 0 ? edit_customer_config[0].color : '#E8E9EE';
  }
    const dailyWorkingHours = _.where(this.formConfig, { type: "project-creation", field_name: "daily_working_hours"});
    if(dailyWorkingHours.length > 0){
      this.daily_working_hours_default = dailyWorkingHours[0].default_value ? dailyWorkingHours[0].default_value : 8;
    }

    const monthlyHours = _.where(this.formConfig, { type: "project-creation", field_name: "monthly_hours"});
    if(monthlyHours.length > 0){
      this.monthly_hours_default = monthlyHours[0].default_value ? monthlyHours[0].default_value : 168;
    }
    const mandRoles = _.where(this.formConfig, { type: "project-creation", field_name: "role"});
    if(mandRoles.length > 0){
      this.mandatory_role = mandRoles[0].role ? mandRoles[0].role : 7;
    }

    const leavePaid = _.where(this.formConfig, { type: "project-creation", field_name: "leave_paid"});
    if(leavePaid.length > 0){
      this.leave_paid_default = leavePaid[0].default_value ? leavePaid[0].default_value : 0;
    }

    const holidayPaid = _.where(this.formConfig, { type: "project-creation", field_name: "holiday_paid"});
    if(holidayPaid.length > 0){
      this.holiday_paid_default = holidayPaid[0].default_value ? holidayPaid[0].default_value : 0;
    }

    const locationConfig = _.where(this.formConfig, { type: "project-creation", field_name: "work_location"});
    if(locationConfig.length > 0){
      this.location_default = locationConfig[0].default_value ? locationConfig[0].default_value : 0;
    }

    const countryConfig = _.where(this.formConfig, { type: "project-creation", field_name: "location"});
    if(countryConfig.length > 0){
      this.country_default = countryConfig[0].default_value ? countryConfig[0].default_value : 0;
    }

    const holidayCalendar = _.where(this.formConfig, { type: "project-creation", field_name: "holiday_calendar"});
    if(holidayCalendar.length > 0){
      this.holiday_calendar_default = holidayCalendar[0].default_value ? holidayCalendar[0].default_value : 0;
    }
    const po_value_digit = _.where(this.formConfig, { type: "project-creation", field_name: "value_digit"});
    if(po_value_digit.length > 0){
      this.po_value_digit = po_value_digit[0].default_value ? po_value_digit[0].default_value : 15;
    }
    
    
 
    const customerConfig = _.where(this.formConfig, { type: "project-creation", field_name: "child_customer",is_active:1});
    if(customerConfig.length > 0){
      this.child_customer = true;
    }
    else{
      this.child_customer = true;
    }
    const account_details_autopatch = _.where(this.formConfig, { type: "project-creation", field_name: "account_details_autopatch",is_active:true});
    if(account_details_autopatch.length > 0){
      this.account_details_autopatch = true;
    }
    else{
      this.account_details_autopatch = false;
    }

    const checkChildOpportunity = _.where(this.formConfig, { type: "project-creation", field_name: "child_opportunity",is_active:true});
    if(checkChildOpportunity.length > 0){
      this.childOpportunityFlag  = true
    }
    else{
      this.childOpportunityFlag = false;
    }
    await this.pmMasterService.getCustomerList().then((res: any) => {
      this.customer_list = this.pmMasterService.customer_list;
    });

    await this.pmMasterService.getPaymentTermsList().then((res: any) => {
      if (res['messType'] == "S") {
        this.payment_list = res['data'];
      }

    });
    
    
    await this.pmMasterService.getProductCategory().then((res: any)=>{
     
      if (res['messType'] == "S") {
        this.product_category_list = res['data'];
      }
      
    })

    await this.pmMasterService.getDeliveryTypeList().then((res: any)=>{
      if(res['messType']=="S"){
        this.delivery_type_list = res['data']
      }
    })

    await this.pmMasterService.getRevenueTypeList().then((res: any)=>{
      if(res['messType']=="S"){
        this.revenue_type_list = res['data']
      }
    })
    this.retrieveMessages = _.where(this.formConfig, {
      type: 'edit-project',
      field_name: 'messages',
      is_active: true,
    });
    //opportunity and risk toggle config handling
    const opportunityToggle = _.where(this.formConfig, { type: "project-creation", field_name: "opportunity_toggle" });
    const riskToggle = _.where(this.formConfig, { type: "project-creation", field_name: "at_risk_toggle" });
    console.log('toggle', opportunityToggle, riskToggle);
    if (opportunityToggle.length > 0) {
      if (opportunityToggle[0].default == 'withoutOpportunity') {
        this.checkWithoutOpportunity();
      }
      else if (opportunityToggle[0].default == 'withOpportunity') {
        this.checkWithOpportunity();
      }
    }
    if(this.opportunityId){
      this.toggleOpportunityChecked = false;
      this.withOpportunity = true;
      this.edit_customer=true;
      this.disableCurrency=true
      if(opportunity_config && opportunity_config.length>0){
        this.opportunityCreation=true

      }
      this.withoutOpportunity = false;
      this.disabletoggle=true
      console.log('with', this.withOpportunity, this.withoutOpportunity);
    }


    if (riskToggle.length > 0) {
      if (riskToggle[0].default == 'standard') {
        this.checkStandard();
      }
      else if (riskToggle[0].default == 'risk') {
        this.checkAtRisk();
      }
    }

    this.p_and_l_list = this.pmMasterService.p_and_l_list
    this.reason_list = this.pmMasterService.reason_list
    if(this.type=="project-creation"){
    if (this.reason_list.length > 0) {
      console.log('REASON', this.reason_list, this.reason_list[0].id)
      this.createJobForm.patchValue({
        reason: this.reason_list[0].id || ''
      });
    }}
    const status = _.where(this.formConfig, { type: "project-creation", field_name: "opportunity", is_active: true });
    this.opportunity_status_list = status.length > 0 ? status[0].status_list : [26, 27, 28]
    const details = _.where(this.formConfig, { type: "project-creation", field_name: "default_stepper", is_active: true });
    this.stepperData = details.length > 0 ? _.where(details[0].values, { is_active: true }) : this.allStepperData;
    this.stepper_color = details.length > 0 ? details[0].color ? details[0].color : "#90ee90" : "#90ee90";
    this.stepper_font = details.length > 0 ? details[0].font ? details[0].font : "#90ee90" : "#90ee90";
    document.documentElement.style.setProperty('--stepperColor', this.stepper_color)
    console.log(this.stepperData)
    this.retrieveMessages = _.where(this.formConfig, { type: "project-creation", field_name: "messages", is_active: true });
    this.currentUser = this.loginService.getProfile().profile
    this.aid = this.currentUser['aid']
    console.log(this.currentUser)
    console.log(this.createJobForm.get('project_code').value.length)

    console.log("inside")
    const retrieveCodeLength = _.where(this.formConfig, { type: "project-creation", field_name: "project_code", is_active: true });
    this.code_length = retrieveCodeLength.length > 0 ? retrieveCodeLength[0].length : 20;
    const retrieveDescriptionLength = _.where(this.formConfig, { type: "project-creation", field_name: "project_description", is_active: true });
    this.description_length = retrieveDescriptionLength.length > 0 ? retrieveDescriptionLength[0].length : 512;
    const retrieveNameLength = _.where(this.formConfig, { type: "project-creation", field_name: "project_name", is_active: true });
    this.name_length = retrieveNameLength.length > 0 ? retrieveNameLength[0].maxLength : 300;
    const retreiveMailNotifi = _.where(this.formConfig, { type: "project-creation", field_name: "people_notification", is_active: true });
    this.mailEnable = retreiveMailNotifi.length > 0 ? retreiveMailNotifi[0].mailEnable : false;
    if(this.type=="project-creation"){
    this.createJobForm.get('project_code').valueChanges.subscribe(async (res) => {
      if (this.createJobForm.get('project_code').value.length > 0) {
        console.log(res)
        if (res) {
          this.createJobForm.patchValue({ ['profit_center']: res })
          this.empty = false
          this.refresh = true
          await this.ProjectCustomCreationService.checkProjectCodeDuplication(res).then((res: any) => {
            if (res) {
              this.codeDuplicated = res['data'];
              console.log(this.codeDuplicated);
              if (this.codeDuplicated == true) {
                this.valid = false;
                console.log(this.valid);
              }
              else {
                this.valid = true;
              }
              this.refresh = false;
              if (res == '' && res == null) {
                this.refresh = true;
              }
            }
          })
        }
      }
      else {
        this.empty = true
        this.valid = false
      }
    })}
    await this.ProjectCustomCreationService.getPortfolioList().then(async (res: any) => {
      console.log(res)
      this.portfolio_list = res['data']
    })

    await this.pmMasterService.getHolidayCalendar().then(async(res: any)=>{
      this.holiday_calendar_list = this.pmMasterService.holiday_calendar_list;
    })

    await this.pmMasterService.getWorkShiftList().then(async(res: any)=>{
      this.shiftMasterList = this.pmMasterService.projectShiftMasterList;
    })

    await this.pmMasterService.getTimeZoneMasterList().then(async(res: any)=>{
      this.timezoneMasterList = this.pmMasterService.timezoneMasterList;
    })

    await this.pmMasterService.getHolidayCalendar().then(async(res: any)=>{
      this.holiday_calendar_list = this.pmMasterService.holiday_calendar_list;
    })
    const type = _.where(this.formConfig, { type: "project-creation", field_name: "project_type", is_active: true })
    this.project_type_disable = type.length > 0 ? type[0].disable : true
    this.project_type_color = type.length > 0 ? type[0].color : '#E8E9EE'
    this.project_type_default = type.length > 0 ? type[0].default : 1
    const profit_center = _.where(this.formConfig, { type: "project-creation", field_name: "profit_center", is_active: true })
    this.profit_center_disable = profit_center.length > 0 ? profit_center[0].disable : true
    this.profit_center_color = profit_center.length > 0 ? profit_center[0].color : '#E8E9EE'
    this.invoice_template_list = this.pmMasterService.invoice_template_list
    await this.pmMasterService.serviceTypeList().then(async(res: any)=>{
      this.cards = this.pmMasterService.service_type_list;
    })
 
    this.project_type_list = this.pmMasterService.project_type_list
    // Filter person_role_list to include only those with role_type 'External'
    this.person_role_list =await this.pmMasterService.external_role_list
    await this.pmMasterService.currencyList().then(async(res: any)=>{
      this.currency_list = this.pmMasterService.currency_list;
    })
    this.work_location_list =await this.pmMasterService.work_location_list
    this.country_list =await this.pmMasterService.country_list;
    this.template_master_list =await this.pmMasterService.template_master_list
    this.template_master_list_search =await this.pmMasterService.template_master_list
    console.log(this.template_master_list)
    const currency = _.where(this.formConfig, { type: "project-creation", field_name: "currency", is_active: true })
    this.default_currency = currency.length > 0 ? currency[0].default : 2
    this.currency_code = currency.length > 0 ? currency[0].default_name : 'USD'
    this.ad_group_list = [{ id: 1, name: "Group 1" }, { id: 2, name: "Group 2" }, { id: 3, name: "Group 3" }]
    const intg = _.where(this.formConfig, { type: "project-creation", field_name: "outbound_intg", is_active: true })
    this.intergeration = intg.length > 0 ? intg[0].intergerate : 0
    if (this.project_type_default > 0) {
      this.createJobForm.patchValue({ [`project_type`]: this.project_type_default })
    }
    else {
      this.createJobForm.patchValue({ [`project_type`]: '' })
    }
    this.createJobForm.patchValue({ ['currency']: this.default_currency })
    this.invite_project_manager = _.where(this.formConfig, { type: "project-creation", field_name: "invite_project_manager", is_active: true });
    const crm_resource = _.where(this.formConfig, { type: "project-creation", field_name: "CRM_resource_add", is_active: true })
    if(crm_resource.length>0){
      this.crm_internal_allocation=true
    }
    else{
      this.crm_external_allocation=true
    }
    this.createJobForm.get('portfolio').valueChanges.subscribe(async (res) => {
      console.log(res)
      if (res) {

        let portfolio_result = _.where(this.portfolio_list,{id: res})

        if(portfolio_result.length>0)
        {
          this.portfolio_name = portfolio_result[0]['p_name']
          this.customer = portfolio_result[0]['customer_name']
          this.customer_id = portfolio_result[0]['end_customer_id']
          this.portfolio_start =portfolio_result[0]['planned_start_date']
          this.portfolio_end = portfolio_result[0]['planned_end_date']
          this.createJobForm.patchValue({ [`customer_details`]: portfolio_result[0]['customer_name'] || '' })
        }
        
        console.log("Customer"+this.customer_id)
        if(this.account_details_autopatch){
          let account_data=_.where(this.customer_list,{id:this.customer_id})
          this.createJobForm.patchValue({"legal_entity": account_data[0].legal_entity})
          this.createJobForm.patchValue({"payment_terms": account_data[0].paymentTerms})
        }
        if(this.disableCode && this.type === "project-creation"){
        this.ProjectCustomCreationService.getCustomerProjectCode(this.customer_id).then((res)=>{
          if(res['messType']=="S")
          {
              this.createJobForm.patchValue({"project_code": res['data']})
          }
        })
      }
      if(this.child_customer){
            this.child_customer_list = _.filter(this.customer_list, (customer) => parseInt(customer.parent_account) == this.customer_id);
            console.log(this.child_customer_list)
            if(this.child_customer_list.length==0){
               this.child_customer_list.push({id:this.customer_id,name:this.customer})
               this.createJobForm.patchValue({child_customer:this.customer_id})
            }
       }
       

        await this.ProjectCustomCreationService.getResourceFromAccounts(this.customer_id).then((res: any) => {
          console.log(res)
          this.customer_stakeholders = res['data']
        if(this.crm_internal_allocation){
          if (this.customer_stakeholders && this.customer_stakeholders.length > 0) {
            this.stakeholders = _.filter(this.stakeholders, (res) => {
              if (!res['isAccount']) {
                return res;
              }
            });


            for (let data of this.customer_stakeholders) {
              this.stakeholders.push({ employee_name: data['aid'], start_date: this.createJobForm.get('startDate').value, end_date: this.createJobForm.get('endDate').value, role: data['project_role_id'], split: 0, isChecked: false, isHead: false, isAccount: true, mail_sent: false })
            }

            if (this.stakeholders.length == 0) {

              if (this.invite_project_manager.length > 0) {
                this.stakeholders.push({ employee_name: '', start_date: '', end_date: '', role: 1, split: '', isChecked: false, isHead: false, isAccount: false, mail_sent: false })
                this.role_disable = true
              }
              else {
                this.stakeholders.push({ employee_name: '', start_date: '', end_date: '', role: '', split: '', isChecked: false, isHead: false, isAccount: false, mail_sent: false })
                this.role_disable = false
              }
            }
          }
          else {
            this.stakeholders = _.filter(this.stakeholders, (res) => {
              if (!res['isAccount']) {
                return res;
              }
            });

            if (this.stakeholders.length == 0) {
              if (this.invite_project_manager.length > 0) {
                this.stakeholders.push({ employee_name: '', start_date: '', end_date: '', role: 1, split: '', isChecked: false, isHead: false, isAccount: false, mail_sent: false })
                this.role_disable = true
              }
              else {
                this.stakeholders.push({ employee_name: '', start_date: '', end_date: '', role: '', split: '', isChecked: false, isHead: false, isAccount: false, mail_sent: false })
                this.role_disable = false
              }
            }
          }
        }
        if(this.crm_external_allocation){
          if (this.customer_stakeholders && this.customer_stakeholders.length > 0) {
            
          }

        }
        })
        await this.ProjectCustomCreationService.getOpportunity(this.customer_id, this.opportunity_status_list, "create").then((res) => {
          if (res['messType'] == 'S') {
            this.opportunity_list = res['data']
          }
          
          
        })
      }
      else {
        this.customer = '-'
        this.portfolio_name = '-'
      }
      this.poepleCount = this.stakeholders.length
    })

    this.createJobForm.get('customer_id').valueChanges.subscribe(async(res)=>{
      if(res){
        this.customer_id =res;
        if(this.account_details_autopatch){
          let account_data=_.where(this.customer_list,{id:res})
          this.createJobForm.patchValue({"legal_entity": account_data[0].legal_entity})
          this.createJobForm.patchValue({"payment_terms": account_data[0].paymentTerms})
        }
        if(this.disableCode && this.type === "project-creation"){
        this.ProjectCustomCreationService.getCustomerProjectCode(res).then((res)=>{
          if(res['messType']=="S")
          {
              this.createJobForm.patchValue({"project_code": res['data']})
          }
        })
      }
        await this.ProjectCustomCreationService.getResourceFromAccounts(res).then((res: any) => {
          console.log(res)
          this.customer_stakeholders = res['data']
          
        if(this.crm_internal_allocation){
          if (this.customer_stakeholders && this.customer_stakeholders.length > 0) {
            this.stakeholders = _.filter(this.stakeholders, (res) => {
              if (!res['isAccount']) {
                return res;
              }
            });


            for (let data of this.customer_stakeholders) {
              this.stakeholders.push({ employee_name: data['aid'], start_date: this.createJobForm.get('startDate').value, end_date: this.createJobForm.get('endDate').value, role: data['project_role_id'], split: 0, isChecked: false, isHead: false, isAccount: true, mail_sent: false })
            }

            if (this.stakeholders.length == 0) {

              if (this.invite_project_manager.length > 0) {
                this.stakeholders.push({ employee_name: '', start_date: '', end_date: '', role: 1, split: '', isChecked: false, isHead: false, isAccount: false, mail_sent: false })
                this.role_disable = true
              }
              else {
                this.stakeholders.push({ employee_name: '', start_date: '', end_date: '', role: '', split: '', isChecked: false, isHead: false, isAccount: false, mail_sent: false })
                this.role_disable = false
              }
            }
          }
          else {
            this.stakeholders = _.filter(this.stakeholders, (res) => {
              if (!res['isAccount']) {
                return res;
              }
            });

            if (this.stakeholders.length == 0) {
              if (this.invite_project_manager.length > 0) {
                this.stakeholders.push({ employee_name: '', start_date: '', end_date: '', role: 1, split: '', isChecked: false, isHead: false, isAccount: false, mail_sent: false })
                this.role_disable = true
              }
              else {
                this.stakeholders.push({ employee_name: '', start_date: '', end_date: '', role: '', split: '', isChecked: false, isHead: false, isAccount: false, mail_sent: false })
                this.role_disable = false
              }
            }
          }
        }
        if(this.crm_external_allocation){
          if (this.customer_stakeholders && this.customer_stakeholders.length > 0) {
            
          }

        }
        })
        if(this.type=="project-creation"){

        await this.ProjectCustomCreationService.getAllParentOpportunity(this.customer_id).then((res)=>{
          if(!res['err'])
          {
              this.opportunity_list = res['data']
          }
        })       
      }}
    })
    if(this.type=="project-creation"){
    this.sowReferenceNumberConfig = _.where(this.formConfig, { field_name: "sow_reference_number", type: "project-creation", is_active: true });
    this.sowRefDuplicatedMsg = this.retrieveMessages[0]?.errors?.sow_ref_duplicated ? this.retrieveMessages[0].errors.sow_ref_duplicated : 'SOW Reference Number already exists';
    this.sowInvalidLengthMsg = this.retrieveMessages[0]?.errors?.sow_invalid_length ? this.retrieveMessages[0].errors.sow_invalid_length : 'SOW Reference Number too short';

    this.createJobForm.get('sow_reference_number').valueChanges.subscribe(async (res) => {
      if (this.createJobForm.get('sow_reference_number').value.length > 0) {
        let sowReferenceNumberMinLength = this.sowReferenceNumberConfig[0]?.min_length ? this.sowReferenceNumberConfig[0]?.min_length : 8;
        this.isSowReferenceNumberRefresh = true;
        this.isSowReferenceNumberEmpty = false;
        if (res && this.createJobForm.get('sow_reference_number').value.length >= sowReferenceNumberMinLength) {
          this.invalidSowReferenceNumberLength = false;
          await this.ProjectCustomCreationService.checkSowReferenceNumberDuplication(res,1,null).then((res: any) => {
            if (res) {
              this.SowReferenceNumberDuplicated = res['data'];
              console.log(this.SowReferenceNumberDuplicated);
              if (this.SowReferenceNumberDuplicated == true) {
                this.sowReferenceNumberValid = false;
                console.log(this.sowReferenceNumberValid);
              }
              else {
                this.sowReferenceNumberValid = true;
              }
              this.isSowReferenceNumberRefresh = false;
              if (res == '' && res == null) {
                this.isSowReferenceNumberRefresh = true;
              }
            }
          })
        }
        if (res && this.createJobForm.get('sow_reference_number').value.length < sowReferenceNumberMinLength) {
          this.sowReferenceNumberValid = false;
          this.invalidSowReferenceNumberLength = true;
        }
        this.isSowReferenceNumberRefresh = false;
      }
      else {
        this.isSowReferenceNumberEmpty = true
        this.sowReferenceNumberValid = false
      }
    })}
    else{
      this.sowReferenceNumberConfig = _.where(this.formConfig, { field_name: "sow_reference_number", type: "project-creation", is_active: true });
      this.sowRefDuplicatedMsg = this.retrieveMessages[0]?.errors?.sow_ref_duplicated ? this.retrieveMessages[0].errors.sow_ref_duplicated : 'SOW Reference Number already exists';
      this.sowInvalidLengthMsg = this.retrieveMessages[0]?.errors?.sow_invalid_length ? this.retrieveMessages[0].errors.sow_invalid_length : 'SOW Reference Number too short';
      this.createJobForm.get('sow_reference_number').valueChanges.subscribe(async (res) => {
        if (this.createJobForm.get('sow_reference_number').value.length > 0) {
          let sowReferenceNumberMinLength = this.sowReferenceNumberConfig[0]?.min_length ? this.sowReferenceNumberConfig[0]?.min_length : 8;
          this.isSowReferenceNumberRefresh = true;
          this.isSowReferenceNumberEmpty = false;
          if (res && this.createJobForm.get('sow_reference_number').value.length >= sowReferenceNumberMinLength) {
            this.invalidSowReferenceNumberLength = false;
            await this.ProjectCustomCreationService.checkSowReferenceNumberDuplication(res,2,this.itemId).then((res: any) => {
              if (res) {
                this.SowReferenceNumberDuplicated = res['data'];
                console.log(this.SowReferenceNumberDuplicated);
                if (this.SowReferenceNumberDuplicated == true) {
                  this.sowReferenceNumberValid = false;
                  console.log(this.sowReferenceNumberValid);
                }
                else {
                  this.sowReferenceNumberValid = true;
                }
                this.isSowReferenceNumberRefresh = false;
                if (res == '' && res == null) {
                  this.isSowReferenceNumberRefresh = true;
                }
              }
            })
          }
          if (res && this.createJobForm.get('sow_reference_number').value.length < sowReferenceNumberMinLength) {
            this.sowReferenceNumberValid = false;
            this.invalidSowReferenceNumberLength = true;
          }
          this.isSowReferenceNumberRefresh = false;
        }
        else {
          this.isSowReferenceNumberEmpty = true
          this.sowReferenceNumberValid = false
        }
      })
    }
    
    await this.pmMasterService.getEntityList().then((res: any) => {
      if (res) {
        this.legal_entity_list = this.pmMasterService.entity_list;
      }
    });
    await this.pmMasterService.getProjectClassification().then((res: any) => {
      if (res) {
        this.project_classification_list = this.pmMasterService.project_classification_list;
      }
    });
    await this.pmMasterService.getProjectEngagement().then((res: any) => {
      if (res) {
        this.project_engagement_list= this.pmMasterService.project_engagement_list;
      }
    });
    await this.pmMasterService.getProjectRegion().then((res: any) => {
      if (res) {
        this.region_list= this.pmMasterService.region_list;
      }
    });
    await this.pmMasterService.getBillingArea().then((res: any) => {
      if (res) {
        this.billing_area_list= this.pmMasterService.billing_area_list;
      }
    });

    await this.ProjectCustomCreationService.getOrgMapping().then(async (res: any) => {
      console.log(res)
      if (res['messType'] == "S") {

        this.ESdata = res['data']
        this.entityList = await this.getListValue("entity_name", "entity_id")
        this.divisionList = await this.getListValue("division_name", "division_id")
        this.subDivisionList = await this.getListValue("sub_division_name", "sub_division_id")
      }

    })
    this.displayList = this.ESdata
    if (this.mode == "edit" && this.selectedEntity) {
      let rejected_array = _.filter(this.displayList, (res) => {
        console.log(res, res['entity_id'] != this.selectedEntity['entity_id'] || res['division_id'] != this.selectedEntity['division_id'] || res['sub_division_id'] != this.selectedEntity['sub_division_id'])
        return (res['entity_id'] != this.selectedEntity['entity_id'] || res['division_id'] != this.selectedEntity['division_id'] || res['sub_division_id'] != this.selectedEntity['sub_division_id'])
      })

      let selected_array = _.filter(this.displayList, (res) => {
        return (res['entity_id'] == this.selectedEntity['entity_id'] && res['division_id'] == this.selectedEntity['division_id'] && res['sub_division_id'] == this.selectedEntity['sub_division_id'])
      })

      console.log(rejected_array)
      console.log(selected_array)
      this.displayList = [...selected_array, ...rejected_array]

      let total_result_array = selected_array.length > 0 ? _.where(this.displayList, { entity_id: selected_array[0]['entity_id'], division_id: selected_array[0]['division_id'], sub_division_id: selected_array[0]['sub_division_id'] }) : []

      this.selectedData = total_result_array.length > 0 ? total_result_array[0] : undefined;
      this.selectedIndex = selected_array.length > 0 ? _.indexOf(this.displayList, this.selectedData) : undefined;
      console.log(this.selectedIndex)


    }

    if(this.creation_mode =="Duplicate")
    {
        this.updateDuplicateProjectCreation()
    }


    this.entityList = await this.getListValue("entity_name", "entity_id")
    this.divisionList = await this.getListValue("division_name", "division_id")
    this.subDivisionList = await this.getListValue("sub_division_name", "sub_division_id")
    this.createJobForm.get('opportunity').valueChanges.subscribe(async (res: any) => {
      console.log(res)
      if (res) {
        for (let items of this.opportunity_list) {
          if (res == items['id']) {
            this.opportunity_status = items['status_name']
          }
        }
        await this.ProjectCustomCreationService.getFinancialValues(res).then((res) => {
          console.log(res)
          if (res['messType'] == 'S') {
            this.financialValue = res['data']

            this.quote_id = this.financialValue[0].quote_header_id
            console.log(this.quote_id)
            this.createJobForm.patchValue({ [`quote`]: this.quote_id })
            this.financeFieldDisable = true
            if (this.financialValue.length > 0) {

              if (this.financialValue[0].quote_amount != null || this.financialValue[0].quote_amount > 0) {
                this.createJobForm.patchValue({ [`po_value`]: this.financialValue[0].quote_amount || '' })

                // this.createJobForm.get('quote').disable()

              }
              else {
                this.createJobForm.patchValue({ ['po_value']: '' })

                // this.createJobForm.get('quote').enable()

              }
            }
            else {
              this.createJobForm.patchValue({ ['po_value']: '' })
              this.createJobForm.get('po_value').enable()
            }
          }

        })
      }
      else {
        this.createJobForm.patchValue({ ['po_value']: '' })
        this.createJobForm.get('po_value').enable()
        this.createJobForm.patchValue({ [`quote`]: '' })
        this.financeFieldDisable = false
      }
    })
    this.createJobForm.get('quote').valueChanges.subscribe((res: any) => {
      console.log(res)
      if (res == null || res == '') {

      }
      else {
      }
    })
    
   

    if(!this.whetherCustomerGeneratedCode())
    {
      await this.ProjectCustomCreationService.getProjectCode().then((res: any) => {
        if (res['messType'] == 'S') {
          this.project_code_list = res['data']
        }
      })
    }
    if(this.type=="project-creation"){
    this.addFinancial()
   
    this.createJobForm.patchValue({ ['from']: this.from_default ? this.from_default : '09:00' })
    this.createJobForm.patchValue({ ['to']: this.to_default ? this.to_default : '17:00' })
    this.createJobForm.patchValue({ ['monthly_hours']: this.monthly_hours_default ? this.monthly_hours_default : 168 })
    this.createJobForm.patchValue({ ['daily_working_hours']: this.daily_working_hours_default ? this.daily_working_hours_default : 8 })
    this.createJobForm.patchValue({ ['leave_paid']: this.leave_paid_default ? this.leave_paid_default : 0 })
    this.createJobForm.patchValue({ ['holiday_paid']: this.holiday_paid_default ? this.holiday_paid_default : 0 })}
    this.formarray = this.createJobForm.get('financial') as FormArray;
    this.detailsSkip = this.mandateAvailable('details')
    this.esSkip = this.mandateAvailable('es')
    this.templateSkip = this.mandateAvailable('template')
    this.peopleSkip = this.mandateAvailable('people')
    this.scheduleSkip = this.mandateAvailable('schedule')
    this.financialSkip = this.mandateAvailable('financial')
    this.detailsLast = this.lastStepper('details')
    this.esLast = this.lastStepper('es')
    this.financialLast = this.lastStepper('financial')
    this.peopleLast = this.lastStepper('people')
    this.scheduleLast = this.lastStepper('schedule')
    this.templateLast = this.lastStepper('template')
    this.tagLast = this.lastStepper('advance')
  

    this.intializeProjectCode()

    this.createJobForm.get('entity').valueChanges.subscribe(async (res: any) => {
      if (this.createJobForm.get('entity').value != null) {
        this.displaySubDivision = true
        this.displayDivision = true
        this.displayEsList = true
        if (this.check == false) {
          this.entity = this.createJobForm.get('entity').value == null ? undefined : this.createJobForm.get('entity').value
          this.division = this.createJobForm.get('division').value == null ? undefined : this.createJobForm.get('division').value
          this.subDivision = this.createJobForm.get('sub_division').value == null ? undefined : this.createJobForm.get('sub_division').value
          let val = {}
          console.log(this.subDivision, this.division, this.entity)
          if (this.subDivision) {
            val['sub_division_id'] = this.subDivision
          }
          if (this.division) {
            val['division_id'] = this.division
          }
          if (this.entity) {
            val['entity_id'] = this.entity
          }
          console.log(val)
          this.displayList = _.where(this.ESdata, val)
          if (this.displayList.length == 0) {
            console.log('testing')
            this.createJobForm.patchValue({ ['division']: null })
            this.createJobForm.patchValue({ ['sub_division']: null })
            console.log(this.createJobForm.get('division').value)
            console.log(this.createJobForm.get('sub_division').value)
            this.entity = this.createJobForm.get('entity').value == null ? undefined : this.createJobForm.get('entity').value
            this.division = this.createJobForm.get('division').value == null ? undefined : this.createJobForm.get('division').value
            this.subDivision = this.createJobForm.get('sub_division').value == null ? undefined : this.createJobForm.get('sub_division').value
            val = {}
            console.log(this.subDivision, this.division, this.entity)
            if (this.subDivision) {
              val['sub_division_id'] = this.subDivision
            }
            if (this.division) {
              val['division_id'] = this.division
            }
            if (this.entity) {
              val['entity_id'] = this.entity
            }
            this.displayList = _.where(this.ESdata, val)
          }
          this.Filterdata = _.where(this.ESdata, { entity_id: this.entity })

          this.divisionList = await this.getListValueFilter("division_name", "division_id")
          this.subDivisionList = await this.getListValueFilter("sub_division_name", "sub_division_id")
          console.log('testentity')

          console.log(this.displayList)
          this.selectedData = undefined;
          this.selectedIndex = undefined;

          console.log(this.displayList)
        }
      }
      else {
        if (this.createJobForm.get('division').value == null && this.createJobForm.get('sub_division').value == null) {
          this.displaySubDivision = false
          this.displayDivision = false
          this.displayEsList = false
        }
      }
    })
    this.createJobForm.get('division').valueChanges.subscribe(async (res: any) => {
      if (this.check == false && this.createJobForm.get('division').value != null) {
        this.displaySubDivision = true
        this.displayDivision = true
        this.displayEsList = true
        this.entity = this.createJobForm.get('entity').value == null ? undefined : this.createJobForm.get('entity').value
        this.division = this.createJobForm.get('division').value == null ? undefined : this.createJobForm.get('division').value
        this.subDivision = this.createJobForm.get('sub_division').value == null ? undefined : this.createJobForm.get('sub_division').value
        let val = {}
        console.log(this.subDivision, this.division, this.entity)
        if (this.subDivision) {
          val['sub_division_id'] = this.subDivision
        }
        if (this.division) {
          val['division_id'] = this.division
        }
        if (this.entity) {
          val['entity_id'] = this.entity
        }
        console.log(val)
        this.displayList = _.where(this.ESdata, val)

        this.Filterdata = _.where(this.ESdata, { entity_id: this.entity })

        this.subDivisionList = await this.getListValueFilter("sub_division_name", "sub_division_id")
        this.selectedData = undefined;
        this.selectedIndex = undefined;
        if (this.displayList.length == 0) {
          this.createJobForm.patchValue({ ['sub_division']: '' })
        }
        console.log(this.displayList)
      }
    })
    this.createJobForm.get('sub_division').valueChanges.subscribe(async (res: any) => {
      if (this.check == false && this.createJobForm.get('sub_division').value != null) {
        this.displaySubDivision = true
        this.displayDivision = true
        this.displayEsList = true
        this.entity = this.createJobForm.get('entity').value == null ? undefined : this.createJobForm.get('entity').value
        this.division = this.createJobForm.get('division').value == null ? undefined : this.createJobForm.get('division').value
        this.subDivision = this.createJobForm.get('sub_division').value == null ? undefined : this.createJobForm.get('sub_division').value
        let val = {}
        console.log(this.subDivision, this.division, this.entity)
        if (this.subDivision) {
          val['sub_division_id'] = this.subDivision
        }
        if (this.division) {
          val['division_id'] = this.division
        }
        if (this.entity) {
          val['entity_id'] = this.entity
        }
        console.log(val)
        this.displayList = _.where(this.ESdata, val)
        this.selectedData = undefined;
        this.selectedIndex = undefined;

        console.log(this.displayList)
      }
    })
    this.createJobForm.get('textInputControl').valueChanges.subscribe(async (res) => {
      if (this.createJobForm.get('textInputControl').value.length > 0) {
        if (res) {
          this.template_master_list = this.template_master_list_search.filter(item =>
            item.template_name.toLowerCase().includes(res.toLowerCase())
          );
        }
        else {
          this.template_master_list = this.template_master_list_search
        }

      }
      else {
        this.template_master_list = this.template_master_list_search
      }
    })
    this.createJobForm.get('currency').valueChanges.subscribe(async (res) => {
      if (res) {
        for (let items of this.currency_list) {
          if (items['id'] == res) {
            this.currency_code = items['name']
            break;
          }
        }
      }
    })
    this.createJobForm.get('from').valueChanges.subscribe(async(res)=>{
      if(res)
      {
        console.log("FROM CALLED", res)
        this.calculateTimeDifference(res, this.createJobForm.get('to').value)
      }
    })
    this.createJobForm.get('to').valueChanges.subscribe(async(res)=>{
      if(res)
      {
        console.log("TO CALLED", res)
        this.calculateTimeDifference( this.createJobForm.get('from').value, res)
      }
    })
    
   if(this.opportunityId){
    if (this.opportunityId) {
     
      this.cards = _.filter(this.cards, card => card.is_internal != 1);
      console.log('Opportunity Search Disable:', this.disableOpportunitySearch);
      this.disableOpportunitySearch = true;
      this.withOpportunity=true
      this.opportunityDetails = await this.getProjectOpportunityData();

      await this.ProjectCustomCreationService.getAllParentOpportunity(this.opportunityDetails?.customer_id).then((res)=>{
        if(!res['err'])
        {
            this.opportunity_list = res['data']
        }
      }) 
      await this.prePatchProjectFormValues();
     
    }
    }
    

    if(!this.disableOpportunitySearch && this.opportunityId){
      this.createJobForm.get('opportunity_id').valueChanges.subscribe(async (res) => {
        if (this.createJobForm.get('opportunity_id').value) {
  
          if (res) {
            this.opportunityDetails = await this.getProjectOpportunityData();
            await this.ProjectCustomCreationService.getAllParentOpportunity(this.opportunityDetails?.customer_id).then((res)=>{
              if(!res['err'])
              {
                  this.opportunity_list = res['data']
              }
            }) 
  
            await this.prePatchProjectFormValues();
          }
        }
       
      })
    }
    if(this.type=="edit-project"){
    await this.ProjectCustomCreationService.getMaxMinProjectDates(
      this.projectId,
      this.itemId
    ).then((res) => {
      if (res['messType'] == 'S') {
        if (res['data'].length > 0) {
          this.min_start_date = res['data'][0]['min_date'];
          this.max_end_date = res['data'][0]['max_date'];
        }
      }
    });
    await this.ProjectCustomCreationService.getProjectQuote(
      this.projectId,
      this.itemId
    ).then((res) => {
      if (res['messType'] == 'S') {
        if (res['data'].length > 0) {
          this.ServiceId = res['data'][0].service_type_id;
          this.customer_id=res['data'][0].end_customer_id
          this.selectedOption= res['data'][0].service_type_id;
          this.item_status=res['data'][0].item_status_id
          if (res['data'][0].at_risk == 1) {
            this.toggleSecuredChecked = true;
            this.standard = false
            this.risk = true
            this.poNonMandate = true;
          }else{
               // }
    this.toggleSecuredChecked = false
    this.standard = true
    this.risk = false
    this.poNonMandate = false;
          }
          if (res['data'][0].opportunity_log != null) {
            const opportunity_edit = JSON.parse(res['data'][0].opportunity_log);
            if (opportunity_edit.length > 0) {
              this.createJobForm.patchValue({
                opportunity:
                  opportunity_edit[opportunity_edit.length - 1].opportunity_id,
              });
            } else {
              this.createJobForm.patchValue({
                opportunity: '',
              });
            }
          }
          this.createJobForm.patchValue({
            project_code:res['data'][0].profit_center,
            project_name:res['data'][0].item_name,
            currency: res['data'][0].billing_currency,
            startDate: res['data'][0].planned_start_date,
            endDate:res['data'][0].planned_end_date,
            customer_id:res['data'][0].end_customer_id,
            project_description:res['data'][0].project_description,
            quote: res['data'][0].quote_id,
            po_number: res['data'][0].po_number,
            po_value: res['data'][0].po_value,
            p_and_l: res['data'][0].p_and_l_id,
            po_reference: res['data'][0].po_reference,
            po_date: res['data'][0].po_date,
            payment_terms: res['data'][0].payment_terms,
            partner: res['data'][0].partner,
            child_customer: res['data'][0].child_customer_id,
            legal_entity:res['data'][0].legal_entity,
            sow_reference_number:res['data'][0].sow_reference_number,
            project_classification:res['data'][0].project_classification,
            project_engagement:res['data'][0].project_engagement,
            billing_area:res['data'][0].billing_area
          });

          for (let items of this.currency_list) {
            if (items['id'] == res['data'][0].billing_currency) {
              this.code = items['name'];
            }
          }
          if (res['data'][0].at_risk == 0) {
            this.standard = true;
            this.risk = false;
          } else {
            this.risk = true;
            this.standard = false;
          }
          if (res['data'][0].with_opportunity == 0) {
            this.withoutOpportunity = true;
            this.withOpportunity = false;
            this.toggleOpportunityChecked = true;
          } else {
            this.withOpportunity = true;
            this.withoutOpportunity = false;
            this.toggleOpportunityChecked = false;
          }
          if (res['data'][0].is_blanket == 0) {
            this.TandM_selected = false;
          } else {
            this.TandM_selected = true;
          }
          this.external_id = res['data'][0].external_reference_id;
        }
      }
    });
    if (this.childOpportunityFlag) {
      await this.ProjectCustomCreationService.getParentEditedOpportunity(
        this.projectId,
        this.itemId,
        this.customer_id,
        this.opportunity_status_list
      ).then((res) => {
        if (res['messType'] == 'S') {
          this.opportunity_list = res['data'];
        }
      });
    } else {
      await this.ProjectCustomCreationService.getEditedOpportunity(
        this.projectId,
        this.itemId,
        this.customer_id,
        this.opportunity_status_list
      ).then((res) => {
        if (res['messType'] == 'S') {
          this.opportunity_list = res['data'];
        }
      });
    }
    await this.ProjectCustomCreationService.getTagsProject(this.itemId).then(
      (res) => {
        if (res['messType'] == 'S') {
          this.existingTag = res['data'];
        }
      }
    );

    let parentOpportunity = '';
    await this.ProjectCustomCreationService.getProjectFinancialwithoutopp(this.projectId,this.itemId).then((res) => {
      if (res['messType'] === 'S') {
        if (res['data'].length > 0) {
        const data = res['data'];
        const fieldsArray = this.createJobForm.get('fieldsArray') as FormArray;
        fieldsArray.clear();
        data.forEach((item: any) => {
          const group = this.formBuilder.group({
            po_start_date: [moment(item?.po_startDate, 'DD-MMM-YYYY').format('YYYY-MM-DD')],
            po_end_date: [moment(item?.po_endDate, 'DD-MMM-YYYY').format('YYYY-MM-DD')],
            po_date: [moment(item?.po_Date, 'DD-MMM-YYYY').format('YYYY-MM-DD')],
            po_number: [item?.po_number],
            po_value: [item?.po_value],
            po_reference: [item?.po_ref_no],
            payment_terms: [item?.payment_terms],
            partner: [item?.partner || null],
            invoice_template: [item?.invoice_template || null],
          });
    
          fieldsArray.push(group);
        });
      } }
    });

    await this.ProjectCustomCreationService.getProjectFinnacialData(
      this.itemId
    ).then((res) => {
      if (res['messType'] == 'S') {
        if (res['data'].length > 0) {
          this.multipleQuote = true;
          for (let i = 0; i < res['data'].length; i++) {
            if (this.checkDisabledBasedOnStatus('opportunity_addition')) {
              res['data'][i]['enable'] = 0;
            } else {
              res['data'][i]['enable'] = 1;
            }

            if (i == 0) {
              parentOpportunity = res['data'][i]['opportunity_id'];
            }
            this.addFinancial();
          }
          const financialArray = this.createJobForm.get(
            'financial'
          ) as FormArray;
          financialArray.patchValue(res['data']);
          this.projectFinnacialData = res['data']
          console.log(this.projectFinnacialData )
        }
      } else {
        this.multipleQuote = false;
        this.addFinancial();
      }
    });
    if (this.childOpportunityFlag) {
      await this.ProjectCustomCreationService.getAllChildOpportunity(
        parentOpportunity
      ).then((res) => {
        if (!res['err']) {
          this.child_opportunity_list = res['data'];
        }
      });
    }
    if (this.projectFinnacialData.length > 0) {
      for (let i = 0; i < this.projectFinnacialData.length; i++) {
        this.getOpportunityData(
          { id: this.projectFinnacialData[i].opportunity_id },
          i,
          1
        );
      }
    }
   
    await this.ProjectCustomCreationService.getGeneralSettings(
      this.projectId,
      this.itemId
    ).then((res) => {
      if (res['messType'] == 'S') {
        if (res['data'] && res['data'].length > 0) {
          if (
            res['data'][0].work_schedule != null &&
            res['data'][0].work_schedule != '' &&
            res['data'][0].work_schedule != ' '
          )
            if (res['data'][0].work_schedule.length > 0)
              for (let i = 0; i < res['data'][0].work_schedule.length; i++) {
                if (res['data'][0].work_schedule[i] == 1) {
                  this.monday = true;
                }
                if (res['data'][0].work_schedule[i] == 2) {
                  this.tuesday = true;
                }
                if (res['data'][0].work_schedule[i] == 3) {
                  this.wednesday = true;
                }
                if (res['data'][0].work_schedule[i] == 4) {
                  this.thursday = true;
                }
                if (res['data'][0].work_schedule[i] == 5) {
                  this.friday = true;
                }
                if (res['data'][0].work_schedule[i] == 6) {
                  this.saturday = true;
                }
                if (res['data'][0].work_schedule[i] == 7) {
                  this.sunday = true;
                }
              }
          this.createJobForm.patchValue({
            from: res['data'][0].work_start_time_hours,
            to: res['data'][0].work_end_time_hours,
            monthly_hours: res['data'][0].monthly_hours,
            daily_working_hours: res['data'][0].daily_working_hours,
            leave_paid:
              res['data'][0].leave_paid == null ||
              res['data'][0].leave_paid == 0
                ? '0'
                : '1',
            holiday_paid:
              res['data'][0].holiday_paid == null ||
              res['data'][0].holiday_paid == 0
                ? '0'
                : '1',
            shift: res['data'][0].shift ? res['data'][0].shift : null,
            timezone: res['data'][0].timezone ? res['data'][0].timezone : null
          });
        }
      }
    });
    await this.ProjectCustomCreationService.getLocations(
      this.projectId,
      this.itemId
    ).then((res) => {
      if (res['messType'] === 'S') {
        if (res['data'].length > 0) {
        const data = res['data'];
        const fields = this.createJobForm.get('fields') as FormArray;
        fields.clear();
        data.forEach((item: any) => {
          const group = this.formBuilder.group({
            location: [parseInt(item['location_name'])],
            country_id: [item['country_id']],
            calendar: [item['holiday_calendar_id']]
          });
    
          fields.push(group);
        });
      } }
    });

   
    await this.ProjectCustomCreationService.retrieveMilestoneId(
      this.itemId
    ).then(async (res: any) => {
      if (res['data'].length > 0) {
        this.milestoneList = res['data'];
      }
    });
    this.loading = false;
    await this.ProjectCustomCreationService.getGroupedMilestone(this.itemId).then((res)=>{
      if(res['messType']=='S'){
       this.milestone_grouped_data=res['data']
      }
 })
    await this.ProjectCustomCreationService.getSumOFMilestoneValue(this.itemId).then((res)=>{
      if(res['messType']=='S' && res['data'].length>0){
        for(let items of res['data']){
          items['milestone_value']=JSON.parse(items['milestone_value'])
          for(let item of items['milestone_value'] ){
            let check=[]
            check=_.where(this.milestone_grouped_data,{childId:items['id']})
            console.log(check)
            if (item['currency_code'] == this.code && check.length==0) {
              this.totalMilestoneValue = this.totalMilestoneValue + item['value'];
            }
          }
        }
      }
    });
  }
  this.oldFormValue = this.createJobForm.value;
        this.oldData = {
          project_name: this.oldFormValue.project_name ? this.oldFormValue.project_name : null,
          project_code: this.oldFormValue.project_code ? this.oldFormValue.project_code : null,
          project_description:this.oldFormValue.project_description? this.oldFormValue.project_description : null,
          portfolio: this.oldFormValue.portfolio ?  this.getNameByIdPipe.transform(this.oldFormValue.portfolio, this.portfolio_list) : null,
          project_classification: this.oldFormValue.project_classification ?  this.getNameByIdPipe.transform(this.oldFormValue.project_classification, this.project_classification_list) : null,
          project_engagement: this.oldFormValue.project_engagement ?  this.getNameByIdPipe.transform(this.oldFormValue.project_engagement, this.project_engagement_list) : null,
          billing_area: this.oldFormValue.billing_area ?  this.getNameByIdPipe.transform(this.oldFormValue.billing_area, this.billing_area_list) : null,
          shift: this.oldFormValue.shift ?  this.getNameByIdPipe.transform(this.oldFormValue.shift, this.shiftMasterList) : null,
          start_date: this.oldFormValue.startDate ? moment(this.oldFormValue.startDate).format('DD-MMM-YYYY') : null,
          end_date: this.oldFormValue.endDate ? moment(this.oldFormValue.endDate).format('DD-MMM-YYYY') : null,
          currency: this.oldFormValue.currency ? this.getNameByIdPipe.transform(this.oldFormValue.currency, this.currency_list) : null,
          project_type: this.oldFormValue.project_type ? this.getNameByIdPipe.transform(this.oldFormValue.project_type, this.project_type_list) : null,
          entity: this.oldFormValue.entity ? this.getNameByIdPipe.transform(this.oldFormValue.entity, this.entityList) : null,
          division: this.oldFormValue.division ? this.getNameByIdPipe.transform(this.oldFormValue.division, this.divisionList) : null,
          sub_division: this.oldFormValue.sub_division ? this.getNameByIdPipe.transform(this.oldFormValue.sub_division, this.subDivisionList) : null,
          profit_center: this.oldFormValue.profit_center ? this.oldFormValue.profit_center : null,
          p_and_l: this.oldFormValue.p_and_l ? this.getNameByIdPipe.transform(this.oldFormValue.p_and_l, this.p_and_l_list) : null,
          legal_entity: this.oldFormValue.legal_entity ? this.getNameByIdPipe.transform(this.oldFormValue.legal_entity, this.legal_entity_list) : null,
          sow_reference_number: this.oldFormValue.sow_reference_number ? this.oldFormValue.sow_reference_number : null,
          product_category: this.oldFormValue.product_category ? this.getNameByIdPipe.transform(this.oldFormValue.product_category, this.product_category_list) : null,
          revenue_type: this.oldFormValue.revenue_type ? this.getNameByIdPipe.transform(this.oldFormValue.revenue_type, this.revenue_type_list) : null,
          delivery_type: this.oldFormValue.delivery_type ? this.getNameByIdPipe.transform(this.oldFormValue.delivery_type, this.delivery_type_list) : null,
          opportunity_toggle : this.withOpportunity ? 'Q2C' : 'O2C',
          at_risk_toggle : this.risk ? 'At Risk' : 'Secured',
          po_number: this.oldFormValue.po_number ? this.oldFormValue.po_number : null,
          po_value: this.oldFormValue.po_value ? this.oldFormValue.po_value : null,
          po_reference: this.oldFormValue.po_reference ? this.oldFormValue.po_reference : null,
          po_date: this.oldFormValue.po_date ? moment(this.oldFormValue.po_date).format('DD-MMM-YYYY') : null, 
          partner: this.oldFormValue.partner ? this.getNameByIdPipe.transform(this.oldFormValue.partner, this.customer_list) : null,
          payment_terms: this.oldFormValue.payment_terms ? this.getNameByIdPipe.transform(this.oldFormValue.payment_terms, this.payment_list) : null,
          work_day_monday: this.monday ? 'Yes' : 'No',
          work_day_tuesday: this.tuesday ? 'Yes' : 'No',
          work_day_wednesday: this.wednesday ? 'Yes': 'No',
          work_day_thursday: this.thursday ? 'Yes': 'No',
          work_day_friday: this.friday? 'Yes' : 'No',
          work_day_saturday : this.saturday ? 'Yes' : 'No',
          work_day_sunday: this.sunday ? 'Yes' : 'No',
          from: this.oldFormValue.from ?  this.oldFormValue.from : null,
          to: this.oldFormValue.to ?  this.oldFormValue.to : null,
          daily_working_hours: this.oldFormValue.daily_working_hours ? this.oldFormValue.daily_working_hours : null,
          monthly_hours: this.oldFormValue.monthly_hours ? this.oldFormValue.monthly_hours : null,
          leave_paid: this.oldFormValue.leave_paid ? this.getNameByIdPipe.transform(this.oldFormValue.leave_paid, this.yes_or_no_list) : null,
          holiday_paid: this.oldFormValue.leave_paid ? this.getNameByIdPipe.transform(this.oldFormValue.holiday_paid, this.yes_or_no_list) : null,
          work_location: this.oldFormValue.fields[0].workLocation ? this.getNameByIdPipe.transform(this.oldFormValue.fields[0].workLocation, this.work_location_list) : null,
        location: this.oldFormValue.fields[0].country_id ? this.getNameByIdPipe.transform(this.oldFormValue.fields[0].country_id, this.country_list) : null,
        holiday_calendar:this.oldFormValue.fields[0].calendar ? this.getNameByIdPipe.transform(this.oldFormValue.fields[0].calendar, this.holiday_calendar_list) : null,
          service_type: this.selectedOption ? this.getNameByIdPipe.transform(this.selectedOption, this.cards) : null,
          tags: this.tags && this.tags.length > 0 ? _.map(this.tags, 'name') : null
      }
      if(this.withOpportunity){
        this.oldFinancialData = this.withOpportunity ? this.oldFormValue.financial : null;
        for(let item of this.oldFinancialData){
          item.reason = item.reason ? this.getNameByIdPipe.transform(item.reason, this.reason_list) : null;
          item.payment_terms =  item.payment_terms ? this.getNameByIdPipe.transform(item.payment_terms, this.payment_list) : null;
          item.po_date = item.po_date ?  moment(item.po_date).format('DD-MMM-YYYY') : null;
        }
      }
    

    this.loading = false;
  }
 
 
  checkDisabledBasedOnStatus(field_name) {
    let disabledConfig = _.where(this.formConfig, {
      type: 'edit-project',
      field_name: field_name,
      is_active: true,
    });

    if (disabledConfig.length > 0) {
      if (
        _.contains(
          disabledConfig[0]['disabled_status'],
          this.item_status
        )
      ) {
        return true;
      } else {
        return false;
      }
    } else {
      return false;
    }
  }
  urlEncodeURIComponent(str) {
    return encodeURIComponent(str).replace(/[!'()*]/g, function (c) {
        return "%" + c.charCodeAt(0).toString(16);
    });
  }
  
  getProjectUrlLink(project_id, project_name, item_id,item_name) {
    return `/main/project-management/${project_id}/${this.urlEncodeURIComponent(project_name)}/${item_id}/${this.urlEncodeURIComponent(item_name)}`;
  }
  async getOpportunityData(id: any, i: number, check?: any): Promise<void> {
    const financialArray = this.createJobForm.get('financial') as FormArray;
    
    // Ensure the financial array is not empty and the index is valid
    if (financialArray && financialArray.length > 0 && financialArray.at(i)) {
      if (id) {
        // Find the matching opportunity from the opportunity_list
        const selectedOpportunity = this.opportunity_list.find(item => item.id === id.id);
  
        if (selectedOpportunity) {
          const financialGroup = financialArray.at(i);

        // Update the form group with opportunity status and color for the specific index
        financialGroup.patchValue({
          opportunity_status_name: selectedOpportunity.status_name,
          opportunity_status_color: selectedOpportunity.status_color || '#6E7B8F', // Default color
        });

        // Debugging logs to check the assigned values
        console.log(`Status for index ${i}: ${selectedOpportunity.status_name}`);
        console.log(`Color for index ${i}: ${selectedOpportunity.status_color}`);
        }
  
        // Now, call your service to fetch additional financial data
        await this.ProjectCustomCreationService.getFinancialValues(id.id).then(res => {
          if (res['messType'] === 'S' && res['data'].length > 0) {
            this.financialValue = res['data'];
            this.quote_id = this.financialValue[0].quote_header_id;
  
            // Patch other fields if available
            const financialGroup = financialArray.at(i);
  
            financialGroup.patchValue({
              quote_id: this.quote_id,
              purchase_order: this.financialValue[0].quote_amount || '',
              po_number: this.financialValue[0].po_number || '',
              po_date: this.financialValue[0].po_date || '',
              payment_terms: this.financialValue[0].payment_terms || '',
              
            });

            if(this.reason_list.length>0)
            {
              financialGroup.patchValue({
                reason: this.reason_list[0].id
              })
            }
  
            // Disable fields after data is fetched
            this.financeFieldDisable = true;
  
          } else {
            // Handle case when no financial data is available
            const financialGroup = financialArray.at(i);
  
            if (check !== 1) {
              financialGroup.patchValue({
                purchase_order: '',
                quote_id: ''
              });
            }
  
            this.financeFieldDisable = false;
          }
        });
      } else {
        // Handle case when `id` is not passed
        const financialGroup = financialArray.at(i);
  
        financialGroup.patchValue({
          purchase_order: '',
          quote_id: ''
        });
  
        this.financeFieldDisable = false;
      }
    } else {
      console.error('Form array or form group not initialized properly');
    }
  }
  


  async getParentOpportunityData(id, i, check) {

    const financialArray = this.createJobForm.get('financial') as FormArray;
    if (id) {
      const selectedOpportunity = this.opportunity_list.find(item => item.id === id.id);
  
        if (selectedOpportunity) {
          const financialGroup = financialArray.at(i);

        // Update the form group with opportunity status and color for the specific index
        financialGroup.patchValue({
          opportunity_status_name: selectedOpportunity.status_name,
          opportunity_status_color: selectedOpportunity.status_color || '#6E7B8F', // Default color
        });

        // Debugging logs to check the assigned values
        console.log(`Status for index ${i}: ${selectedOpportunity.status_name}`);
        console.log(`Color for index ${i}: ${selectedOpportunity.status_color}`);
        
      }
      await this.ProjectCustomCreationService.getFinancialValues(id.id).then(
        (res) => {
          //console.log(res)
          if (res['messType'] == 'S' && res['data'].length > 0) {
            this.financialValue = res['data'];

            this.quote_id = this.financialValue[0].quote_header_id;
            //console.log(this.quote_id)
            const quote = financialArray.at(i).get('quote_id');
            quote.patchValue(this.quote_id);
            this.financeFieldDisable = true;
            if (this.financialValue.length > 0) {
              if (
                this.financialValue[0].quote_amount != null ||
                this.financialValue[0].quote_amount > 0
              ) {
                const po_value = financialArray.at(i).get('purchase_order');
                po_value.patchValue(this.financialValue[0].quote_amount);
                // this.createJobForm.patchValue({ [`po_value`]: this.financialValue[0].quote_amount || '' })

                // this.createJobForm.get('quote').disable()
              } else {
                const po_value = financialArray.at(i).get('purchase_order');
                if (
                  financialArray.at(i).get('purchase_order').value == null ||
                  financialArray.at(i).get('purchase_order').value == 0
                ) {
                  po_value.patchValue('');
                }

                // this.createJobForm.get('quote').enable()
              }
            } else {
              const po_value = financialArray.at(i).get('purchase_order');
              if (
                financialArray.at(i).get('purchase_order').value == null ||
                financialArray.at(i).get('purchase_order').value == 0
              ) {
                po_value.patchValue('');
              }
              // this.createJobForm.patchValue({ ['po_value']: '' })
              // this.createJobForm.get('po_value').enable()
            }
          } else {
            // this.createJobForm.patchValue({ ['purchase_order']: '' })
            // this.createJobForm.get('purchase_order').enable()
            //  this.createJobForm.patchValue({ [`quote_id`]:'' })
            const po_value = financialArray.at(i).get('purchase_order');
            if (check != 1) {
              po_value.patchValue('');
            }
            const quote = financialArray.at(i).get('quote_id');
            quote.patchValue('');
            this.financeFieldDisable = false;
          }
        }
      );

      for (let i = 0; i <= financialArray.length; i++) {
        if (
          financialArray.controls[i].get('opportunity_id').value != id.id ||
          financialArray.controls[i].get('opportunity_id').value == null ||
          financialArray.controls[i].get('opportunity_id').value == ''
        ) {
          (this.createJobForm.get('financial') as FormArray).removeAt(i);
        }
      }

      await this.ProjectCustomCreationService.getAllChildOpportunity(id.id).then(
        (res) => {
          if (!res['err']) {
            this.child_opportunity_list = res['data'];
          }
        }
      );

      // this.createJobForm.patchValue({[`quote`]:  items['quote_id']|| ''})
      // this.createJobForm.patchValue({[`po_value`]:  items['po_value'] || ''})
      // this.createJobForm.patchValue({[`po_number`]: items['po_number'] || ''})
    } else {
      //   this.createJobForm.patchValue({ ['purchase_order']: '' })
      //   this.createJobForm.get('purchase_order').enable()
      //  this.createJobForm.patchValue({ [`quote_id`]:'' })
      const po_value = financialArray.at(i).get('purchase_order');
      po_value.patchValue('');
      const quote = financialArray.at(i).get('quote_id');
      quote.patchValue('');
      this.financeFieldDisable = false;
    }
  }

 

  calculateMinEndDate() {
    if (
      this.createJobForm.get('startDate').value != '' &&
      this.max_end_date
    ) {
      return moment(this.createJobForm.get('startDate').value)
        .utc()
        .isAfter(this.max_end_date)
        ? this.createJobForm.get('startDate').value
        : this.max_end_date;
    } else if (this.createJobForm.get('startDate').value != '') {
      return this.createJobForm.get('startDate').value;
    } else if (this.max_end_date) {
      return this.max_end_date;
    }
  }

  calculateMaxStartDate() {
    if (
      this.createJobForm.get('endDate').value != '' &&
      this.min_start_date
    ) {
      return moment(this.min_start_date)
        .utc()
        .isAfter(this.createJobForm.get('endDate').value)
        ? this.createJobForm.get('endDate').value
        : this.min_start_date;
    } else if (this.createJobForm.get('endDate').value != '') {
      return this.createJobForm.get('endDate').value;
    } else if (this.min_start_date) {
      return this.min_start_date;
    }
  }

  getPONumberDisabledColor(field_name, status_id) {
    let checkDisabled = this.getDisabledStatusColor(field_name, status_id);

    if (checkDisabled == '#E8E9EE') {
      return checkDisabled;
    } else {
      if (this.risk) {
        return '#E8E9EE';
      } else {
        return '';
      }
    }
  }

  checkRisk(){
    let checkRecords = _.where(this.formConfig,{type:"edit-project", field_name:"checkRiskStatus", is_active: true});

    if(checkRecords.length>0)
    {
        if(this.risk)
        {
            return true;
        }
        else
        {
            return false;
        }
    }
    else
    {
        return true;
    }
  }
  loadStakeholders() {
   
      this.addStakeholder();
  
  }
  get stakesArray(): FormArray {
    return this.createJobForm.get('external_stakeholders') as FormArray;
  }

  addStakeholder() {
    this.stakesArray.push(this.createFieldsGroup());
    this.poepleCount+=1
  }
  get externalStakeholderCount(): number {
    const externalStakeholders = this.createJobForm.get('external_stakeholders');
    return externalStakeholders instanceof FormArray ? externalStakeholders.controls.length : 0;
}
patchForm(data: any) {
  const fieldsArray = this.createJobForm.get('fieldsArray') as FormArray;
  fieldsArray.clear(); // Clear existing fields

  // Create a new FormGroup for the data and add it to the FormArray
  const group = this.formBuilder.group({
    po_start_date: [data.po_startDate],
    po_end_date: [data.po_endDate],
    po_date: [data.po_Date],
    po_number: [data.po_number],
    po_value: [data.currencyListJSON],
    po_reference: [data.po_ref_no],
    payment_terms: [data.payment_terms],
    partner: [data.partner || null], // assuming you may have a partner field
    invoice_template: [data.invoice_template || null], // assuming you may have an invoice template field
  });

  fieldsArray.push(group);
}


  removePeopleField(index: number) {
    if ((this.createJobForm.get('external_stakeholders') as FormArray).length > 1) {
      // this.removedCriteriaRecord.push(this.contactForm.value.acceptance_criteria_arr[i].id);
      // console.log("removed criteria", this.removedCriteriaRecord);
      (this.createJobForm.get('external_stakeholders') as FormArray).removeAt(index);
      this.poepleCount=this.poepleCount-1
    }
  }
  @HostListener('window:resize')
  onResize() {
    this.calculateDynamicContentHeight();
  }

  /**
   * @description Calculates dynamic height based on screen size
   */
  calculateDynamicContentHeight() {
    this.dynamicHeight = window.innerHeight - 57 - 48 + 'px';
    document.documentElement.style.setProperty(
      '--dynamicHeight',
      this.dynamicHeight
    );
    this.dynamicminHeight=(window.innerHeight - 57 - 48+50)-180+ 'px'
    this.dynamicSubHeight = (window.innerHeight - 57 - 88 - 57 - 16+ 45+10)+ 'px';
    document.documentElement.style.setProperty(
      '--dynamicSubHeight',
      this.dynamicSubHeight
    );
    document.documentElement.style.setProperty(
      '--dynamicminHeight',
      this.dynamicminHeight
    );
    this.dynamicHiringProcessHeight =
      (window.innerHeight - 57 - 48 - 32 - 57 - 16 - 110)+10 + 'px';
    document.documentElement.style.setProperty(
      '--dynamicHiringProcessHeight',
      this.dynamicHiringProcessHeight
    );
    document.documentElement.style.setProperty(
      '--dynamicAccessDeniedHeight',
      window.innerHeight - 89 + 'px'
    );
  }
  

 


  goNext(): void {
    let internal = _.where(this.cards , {is_internal:1,id:this.selectedOption})
    if(this.currentKey=="projectDetails" && this.type=='project-creation'){
      this.default_start_date=this.createJobForm.get('startDate').value
      this.default_end_date=this.createJobForm.get('endDate').value
      if(!this.valid){
        this.toastr.showWarning(
          'Warning ⚠️',
          "Project Code Is Invalid",
          7000
      );
      return;

      }
    }
    if (this.currentKey == "resourcesAllocation" && this.type == 'project-creation') {
      const formArray = this.createJobForm.get('external_stakeholders') as FormArray;
      
      // Step 1: Validate mandatory fields dynamically (check for empty or invalid fields)
      let invalidFields = [];
      formArray.controls.forEach((control, index) => {
        // Check for mandatory fields in the current form group (start_date, end_date, associate_id, project_role_id)
        if (!control.get('start_date').value) {
          this.isaRequired_start = true;  // Set flag for start_date
          invalidFields.push(index); // Track invalid field
        }
        if (!control.get('end_date').value) {
          this.isaRequired_end = true;  // Set flag for end_date
          invalidFields.push(index); // Track invalid field
        }
        if (!control.get('associate_id').value) {
          this.isaRequired_associate = true;  // Set flag for associate_id
          invalidFields.push(index); // Track invalid field
        }
        if (!control.get('project_role_id').value) {
          this.isaRequired_role = true;  // Set flag for project_role_id
          invalidFields.push(index); // Track invalid field
        }
      });
      
  
      // If there are any invalid fields, show a warning and return
      if (invalidFields.length > 0) {
        
        this.toastr.showWarning(
          'Warning ⚠️',
          'Kindly Enter all mandatory fields!',
          7000
      );
        return;  // Prevent moving to the next step
      }
  
      // Step 2: Validate if all mandatory roles are selected
      const selectedRoles = formArray.value.map(stakeholder => stakeholder.project_role_id); // Extract all selected roles
      const missingRoles = this.mandatory_role.filter(role => !selectedRoles.includes(role));
      
      if (missingRoles.length > 0) {
        // If there are missing roles, show toaster and update the list
        missingRoles.forEach(role => this.showToasterMessage(role));
        return;  // Prevent form submission if mandatory roles are missing
      }
  
      // Step 3: Check for duplicates in associate_id and project_role_id combinations
      let hasDuplicates = false;
      const duplicates: string[] = [];
  
      formArray.controls.forEach((control, index) => {
        const associate_id = control.get('associate_id').value;
        const project_role_id = control.get('project_role_id').value;
  
        // Check if the combination already exists in the form array
        for (let i = index + 1; i < formArray.controls.length; i++) {
          const otherAssociateId = formArray.at(i).get('associate_id').value;
          const otherRoleId = formArray.at(i).get('project_role_id').value;
  
          if (associate_id === otherAssociateId && project_role_id === otherRoleId) {
            hasDuplicates = true;
            duplicates.push(`Associate ID: ${associate_id} with Role: ${project_role_id}`);
          }
        }
      });
  
      if (hasDuplicates) {
        // If any duplicates found, show toaster message with the duplicate combinations
        this.toastr.showWarning(
          'Warning ⚠️',
          `Duplicate Allocation Found`,
          7000
        );
        return;  // Prevent form submission if there are duplicates
      }
  
      // Step 4: Validate date range (start_date and end_date should be within the default range)
      let isDateValid = true;
  
      formArray.controls.forEach(control => {
        const startDate = control.get('start_date').value;
        const endDate = control.get('end_date').value;
  
        // Check if start_date is within the allowed range
        if (startDate && new Date(startDate) < new Date(this.default_start_date)) {
          isDateValid = false;
          control.get('start_date').setErrors({ invalidDate: true });
        }
  
        // Check if end_date is within the allowed range
        if (endDate && new Date(endDate) > new Date(this.default_end_date)) {
          isDateValid = false;
          control.get('end_date').setErrors({ invalidDate: true });
        }
      });
  
      // If any invalid date found, show toastr warning
      if (!isDateValid) {
        this.toastr.showWarning(
          'Warning ⚠️',
          'Kindly Enter Valid Date',
          7000
        );
        return;  // Prevent moving to the next step
      }

    }
   
    
    const currentIndex = this.activeSections.findIndex(section => section.key === this.currentKey);
    let mandatoryFields;

    if (this.currentKey === "timelinePlanning" && internal.length == 0) {
      if(this.withOpportunity){
        const financialArrayValue = this.createJobForm.get('financial').value;

        // Log the value for debugging purposes
        console.log(financialArrayValue);
      
        // Check for duplicate opportunity_ids directly
        const opportunityIds = financialArrayValue.map(item => item.opportunity_id); // Extract all opportunity_id values
        const hasDuplicates = new Set(opportunityIds).size !== opportunityIds.length;
        
          if (hasDuplicates) {
            console.error("Duplicate opportunity IDs found.");
            this.toasterService.showError("Duplicate opportunity IDs found.");
            return;
          }
      }
        if (this.type === "project-creation") {
            mandatoryFields = this.withOpportunity ? 
                this.activeSections[currentIndex].q2c || [] : 
                this.activeSections[currentIndex].o2c || [];
        } else {
            mandatoryFields = this.withOpportunity ? 
                this.activeSections[currentIndex].q2c_edit || [] : 
                this.activeSections[currentIndex].o2c_edit || [];
        }
        const formArrayName = this.withOpportunity ? 'financial' : 'fieldsArray';
        const formArray = this.createJobForm.get(formArrayName) as FormArray; // Get the appropriate FormArray

if (formArray.length === 0) {
    this.toastr.showWarning(
        'Warning ⚠️',
        `No entries found in ${formArrayName}.`,
        7000
    );
    return; // Exit the function if the array is empty
}

// Loop through the form array to validate each entry
for (let i = 0; i < formArray.length; i++) {
    const currentItem = formArray.at(i); // Get the current FormGroup

    // Determine the value to validate based on withOpportunity
    const valueToValidate = this.withOpportunity
        ? currentItem.get('purchase_order')?.value // If withOpportunity, get purchase_order
        : currentItem.get('po_value')?.value; // Otherwise, get po_value

    // Validate the appropriate value
    if (!valueToValidate || (typeof valueToValidate !== 'string' && typeof valueToValidate !== 'number') || 
    (typeof valueToValidate === 'string' && valueToValidate.trim() === "") || 
    Number(valueToValidate) <= 0) {
    const warningMessage = this.withOpportunity 
        ? 'po value must be greater than 0.' 
        : 'po value must be greater than 0.';
    // handle the warning message (e.g., display it to the user)
    this.toastr.showWarning(
      'Warning ⚠️',
      warningMessage,
      7000
  );
  return; // Exit the function on validation failure
}

}

   


      
    } else {
        // Existing logic for project creation and editing
        if (this.type === "project-creation") {
            mandatoryFields = this.activeSections[currentIndex].mandatory || [];
        } else {
            mandatoryFields = this.activeSections[currentIndex].edit_mandatory || [];
        }
    }

    this.invalidFields = []; // Clear previous invalid fields
    const isValid = this.validateMandatoryFields(mandatoryFields);
    console.log(this.invalidFields);

    if (isValid) {
        this.showErrors = false; // Reset the error flag if valid
        const nextIndex = currentIndex + 1;

        if (nextIndex < this.activeSections.length) {
            this.activeSections[currentIndex].isVisited = true; // Mark current as visited
            this.currentKey = this.activeSections[nextIndex].key; // Update to next key
            this.selectSection(this.currentKey); // Select next section
        }
    } else {
        this.showErrors = true; // Show error messages
        this.toastr.showWarning(
            'Warning ⚠️',
            'Kindly enter all mandatory fields!',
            7000
        );
    }
}



validateMandatoryFields(mandatoryFields: string[]): boolean {
  this.invalidFields = [];  // Clear previous invalid fields array

  // Loop through each mandatory field to validate
  for (const field of mandatoryFields) {
    const control = this.createJobForm.get(field);

    if (control) {
      // Handle normal form controls (non-FormArray)
      const value = control.value;
      const stringValue = value != null ? String(value) : '';  // Convert to string to handle null/undefined

      // If the field is empty (trimmed string is empty), it's invalid
      if (stringValue.trim() === '') {
        this.invalidFields.push(field);  // Add invalid field to the list
      }
    } else {
      // Handle fields inside FormArray
      const formArrayName = this.getFormArrayNameBasedOnCurrentKey();
      const formArray = this.createJobForm.get(formArrayName) as FormArray;

      let isFieldValidInAnyControl = false;  // Flag to track if any control in FormArray has a valid value for this field

      // Loop through all controls inside the FormArray
      for (const arrayControl of formArray.controls) {
        const arrayValue = arrayControl.get(field)?.value;  // Get the value of the current control
        const stringValue = arrayValue != null ? String(arrayValue) : '';  // Convert to string to handle null/undefined

        // If the control has a non-empty value, mark it as valid
        if (stringValue.trim() !== '') {
          isFieldValidInAnyControl = true;  // Field has a valid value in this control
        }
      }

      // If the field has no valid values in any control, mark it as invalid
      if (!isFieldValidInAnyControl) {
        this.invalidFields.push(field);  // Add invalid field to the list
      }
    }
  }

  // If any invalid fields are found, return false to prevent form submission
  if (this.invalidFields.length > 0) {
    return false;  // Some mandatory fields are missing, return false
  }

  return true;  // All mandatory fields are filled, return true
}




showToasterMessage(roleId: number | number[]): void {
  // Check if roleId is an array
  if (Array.isArray(roleId)) {
    // Loop through each roleId if it's an array
    roleId.forEach(id => {
      const role = this.person_role_list.find(role => role.id === id);
      
      if (role) {
        this.toastr.showWarning(
          'Warning ⚠️',  
          `${role.name} is mandatory`,  // Show role name
          7000  // Duration (in milliseconds)
        );
      } else {
        // If the role ID is not found in person_role_list, fallback message
        this.toastr.showWarning(
          'Warning ⚠️',
          `Role with ID ${id} is mandatory`,  // Fallback message with ID
          7000
        );
      }
    });
  } else {
    // Handle the case where roleId is a single number
    const role = this.person_role_list.find(role => role.id === roleId);

    if (role) {
      this.toastr.showWarning(
        'Warning ⚠️',  
        `${role.name} Role is mandatory`,  // Show role name
        7000  // Duration (in milliseconds)
      );
    } else {
      // If the role ID is not found in person_role_list, fallback message
      this.toastr.showWarning(
        'Warning ⚠️',
        `Role with ID ${roleId} is mandatory`,  // Fallback message with ID
        7000
      );
    }
  }
}



getFormArrayNameBasedOnCurrentKey(): string {
  switch (this.currentKey) {
      case 'resourcesAllocation':
          return 'external_stakeholders'; // Adjust this to your logic
      case 'timelinePlanning':
          return this.withOpportunity ? 'financial' : 'fieldsArray'; // Check withOpportunity
      default:
          return 'fields'; // Default case if needed
  }
}


isFieldInvalid(fieldName: string): boolean {
    const control = this.createJobForm.get(fieldName);
    return this.showErrors && this.checkIfMandatory(fieldName) && control && !control.value;
}

checkIfMandatory(fieldName: string): boolean {
    return !!(this.formConfig && this.formConfig.find(
        config => config.field_name === fieldName && config.is_active && config.is_mandant
    ));
}
getCurrentLabel(): string {
  const currentSection = this.activeSections.find(section => section.key === this.currentKey);
  return currentSection ? currentSection.label : 'Default Label';
}
  
  // Example method to get fields for the current section
  getFieldsForCurrentSection(index: number): string[] {
    const sections = [
     // ['project_code', 'project_name','startDate','endDate','customer_id','project_type','portfolio'], // Fields for section 0
     // ['billing_area','currency','project_classification','project_engagement','region','billing_entity','sow_reference_number']
     
      // Add more sections as needed
    ];
    return sections[index] || [];
  }
  
 

  
 
  handleValueEmitted(emittedValue: string) {
    this.tags = emittedValue
    console.log(emittedValue)
    console.log(this.tags)
  }
  validateProjectCode(event: Event) {
    this.defaultValue = false
    const inputValue = (event.target as HTMLInputElement).value;
    const patternStrings = _.where(this.formConfig, { type: "portfolio-creation", field_name: "portfolio_code", is_active: true });
    console.log(patternStrings);
    const patternRegex = patternStrings[0].pattern;
    const patternParts = patternRegex.split('/');
    const pattern1 = new RegExp(patternParts[1], patternParts[2]);
    console.log(patternRegex, pattern1);
    //const pattern = /[^0-9A-Za-z]/gi;
    //const sanitizedValue = inputValue.replace(pattern1, '').toUpperCase();
    const sanitizedValue = inputValue.replace(pattern1, '')

    // Check if portfolio code is duplicated
    if (this.codeDuplicated) {
      // Mark the portfolio code as invalid
      this.createJobForm.get('project_code').setErrors({ duplicated: true });
      this.valid = false;
    } else {
      this.createJobForm.get('project_code').setValue(sanitizedValue);
      this.valid = this.createJobForm.get('project_code').valid;
    }

    // this.showPatternError = false;
    // this.showMaxLengthError = false;

    // this.showPatternError = this.portfolioCodeControl.hasError('pattern');
    // this.showMaxLengthError = this.portfolioCodeControl.hasError('maxlength');
  }
 





selectSection(key: string) {
  this.blankJobSections.forEach(section => {
      section.isSelected = section.key === key;
      section.isVisited = this.activeSections.findIndex(s => s.key === section.key) <= this.activeSections.findIndex(s => s.key === key);
  });
  this.currentKey = key;
  console.log(this.currentKey)
}

goPrevious(): void {
    const currentIndex = this.activeSections.findIndex(section => section.key === this.currentKey);
    const previousIndex = currentIndex - 1;

    if (previousIndex >= 0) {
        this.activeSections[currentIndex].isVisited = true; // Mark current as visited
        this.currentKey = this.activeSections[previousIndex].key; // Update to previous key
        this.selectSection(this.currentKey); // Select previous section
    }
}

  


  handleFieldChange(fieldKey: string) {
    // Custom logic based on fieldKey
  }
  async saveProjectDetails() {
 
    console.log(this.createJobForm.get('financial').value)
    const financialArrayValue = this.createJobForm.get('financial').value;

  // Log the value for debugging purposes
  console.log(financialArrayValue);

  // Check for duplicate opportunity_ids directly
  const opportunityIds = financialArrayValue.map(item => item.opportunity_id); // Extract all opportunity_id values
  const hasDuplicates = new Set(opportunityIds).size !== opportunityIds.length;
  
    if (hasDuplicates) {
      console.error("Duplicate opportunity IDs found.");
      this.toasterService.showError("Duplicate opportunity IDs found.");
      return;
    }
    this.save_disabled = true
    console.log(this.createJobForm);
    this.week_array = []
    if (this.monday) {
      this.week_array.push(1)
    }
    if (this.tuesday) {
      this.week_array.push(2)
    }
    if (this.wednesday) {
      this.week_array.push(3)
    }
    if (this.thursday) {
      this.week_array.push(4)
    }
    if (this.friday) {
      this.week_array.push(5)
    }
    if (this.saturday) {
      this.week_array.push(6)
    }
    if (this.sunday) {
      this.week_array.push(7)
    }
   
    console.log(this.week_array)
    this.createJobForm.patchValue({ ['endDate']: moment(this.createJobForm.get('endDate').value).format('YYYY-MM-DD') || this.createJobForm.get('endDate').value })
    this.createJobForm.patchValue({ ['startDate']: moment(this.createJobForm.get('startDate').value).format('YYYY-MM-DD') || this.createJobForm.get('startDate').value })
    this.data = this.createJobForm.value;
    this.result=this.createJobForm.value;
    this.data['gantt_id'] = uuidv4()
    if(this.type=="project-creation"){
      

      for (let items of this.currency_list) {
        if (items['id'] == this.data.currency) {
          this.code = items['name']
        }
      }
      for (let item of this.p_and_l_list) {
        if (item['id'] == this.data.p_and_l) {
          this.data['p_and_l_name'] = item['name']
          break
        }
        else {
          this.data['p_and_l_name'] = null
        }
      }
      // this.data['quote_id']=this.quote_id
      this.data['at_risk'] = this.risk ? 1 : 0
      this.data['with_opportunity'] = this.withOpportunity ? 1 : 0
      if (this.withOpportunity) {
        const financialArray = this.createJobForm.get('financial') as FormArray;
        for (let i = 0; i < this.createJobForm.get('financial').value.length; i++) {
          const quote = financialArray.at(i).get('quote_id');
          // quote.patchValue('')
        }
        this.data['financial_data'] = this.createJobForm.get('financial').value
      }
      else {
        const financialArrayWithOutOp=this.createJobForm.get('fieldsArray').value
        this.data['financial_data_without_opportunity'] =this.createJobForm.get('fieldsArray').value
        this.data['financial_data'] = []
      }
      this.data['tandmFlag']=this.TandM_selected
      if(this.type === "project-creation"){
      await this.ProjectCustomCreationService.checkProjectCodeDuplication(this.data.project_code).then((res: any) => {
        if (res) {
          this.codeDuplicated = res['data'];
          console.log(this.codeDuplicated);
          if (this.codeDuplicated == true) {
            this.valid = false;
            const enterAll_mandate_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.duplicate_project_code_msg ? this.retrieveMessages[0].errors.duplicate_project_code_msg : 'Project Code is not Valid! Generating new Project Code...' : 'Project Code is not Valid! Generating new Project Code...';
            this.toasterService.showWarning(enterAll_mandate_msg, 10000)
            this.generateNewProjectCode()
            this.save_disabled = false
            
          }
          else {
            this.valid = true;
          }
          this.refresh = false;
          if (res == '' && res == null) {
            this.refresh = true;
          }
        }
      })}
    console.log(this.valid,'check')
      if(this.valid)
      {
        await this.ProjectCustomCreationService.saveProjectDetails(this.data, this.selectedOption, this.week_array,this.createJobForm.get('fields').value, [], this.tags, this.code, this.intergeration,this.createJobForm.get('external_stakeholders').value).then((res: any) => {
          console.log(res)
          if (res['messType'] == 'S') {
        
              const dialogRef1 = this.dialog.open(AfterSaveDialogComponent, {
                disableClose: true,
                data: {
                  data:{ messType: "S", "project_id": res['data'], "portfolio_id": res['portfolio_id'], "data": this.data, "portfolio_name":res['portfolio_name'],"project_name":res['project_name'] }
                },
              });
              

          }
          else {
            this.save_disabled = false
          }
        });
      }
      else
      {
        this.save_disabled=false;
      }}
      else{
        this.result = this.createJobForm.value;
        
            this.newData = {
              project_name: this.result.project_name ? this.result.project_name : null,
              project_code: this.result.project_code ? this.result.project_code : null,
              project_description:this.result.project_description? this.result.project_description : null,
              portfolio: this.result.portfolio ?  this.getNameByIdPipe.transform(this.result.portfolio, this.portfolio_list) : null,
              project_classification: this.result.project_classification ?  this.getNameByIdPipe.transform(this.result.project_classification, this.project_classification_list) : null,
              project_engagement: this.result.project_engagement ?  this.getNameByIdPipe.transform(this.result.project_engagement, this.project_engagement_list) : null,
              billing_area: this.result.billing_area ?  this.getNameByIdPipe.transform(this.result.billing_area, this.billing_area_list) : null,
              shift: this.result.shift ?  this.getNameByIdPipe.transform(this.result.shift, this.shiftMasterList) : null,
              start_date: this.result.startDate ? moment(this.result.startDate).format('DD-MMM-YYYY') : null,
              end_date: this.result.endDate ? moment(this.result.endDate).format('DD-MMM-YYYY') : null,
              currency: this.result.currency ? this.getNameByIdPipe.transform(this.result.currency, this.currency_list) : null,
              project_type: this.result.project_type ? this.getNameByIdPipe.transform(this.result.project_type, this.project_type_list) : null,
              entity: this.result.entity ? this.getNameByIdPipe.transform(this.result.entity, this.entityList) : null,
              division: this.result.division ? this.getNameByIdPipe.transform(this.result.division, this.divisionList) : null,
              sub_division: this.result.sub_division ? this.getNameByIdPipe.transform(this.result.sub_division, this.subDivisionList) : null,
              profit_center: this.result.profit_center ? this.result.profit_center : null,
              p_and_l: this.result.p_and_l ? this.getNameByIdPipe.transform(this.result.p_and_l, this.p_and_l_list) : null,
              legal_entity: this.result.legal_entity ? this.getNameByIdPipe.transform(this.result.legal_entity, this.legal_entity_list) : null,
              sow_reference_number: this.result.sow_reference_number ? this.result.sow_reference_number : null,
              product_category: this.result.product_category ? this.getNameByIdPipe.transform(this.result.product_category, this.product_category_list) : null,
              revenue_type: this.result.revenue_type ? this.getNameByIdPipe.transform(this.result.revenue_type, this.revenue_type_list) : null,
              delivery_type: this.result.delivery_type ? this.getNameByIdPipe.transform(this.result.delivery_type, this.delivery_type_list) : null,
              opportunity_toggle : this.withOpportunity ? 'Q2C' : 'O2C',
              at_risk_toggle : this.risk ? 'At Risk' : 'Secured',
              po_number: this.result.po_number ? this.result.po_number : null,
              po_value: this.result.po_value ? this.result.po_value : null,
              po_reference: this.result.po_reference ? this.result.po_reference : null,
              po_date: this.result.po_date ? moment(this.result.po_date).format('DD-MMM-YYYY') : null, 
              partner: this.result.partner ? this.getNameByIdPipe.transform(this.result.partner, this.customer_list) : null,
              payment_terms: this.result.payment_terms ? this.getNameByIdPipe.transform(this.result.payment_terms, this.payment_list) : null,
              work_day_monday: this.monday ? 'Yes' : 'No',
              work_day_tuesday: this.tuesday ? 'Yes' : 'No',
              work_day_wednesday: this.wednesday ? 'Yes': 'No',
              work_day_thursday: this.thursday ? 'Yes': 'No',
              work_day_friday: this.friday? 'Yes' : 'No',
              work_day_saturday : this.saturday ? 'Yes' : 'No',
              work_day_sunday: this.sunday ? 'Yes' : 'No',
              from: this.result.from ?  this.result.from : null,
              to: this.result.to ?  this.result.to : null,
              daily_working_hours: this.result.daily_working_hours ? this.result.daily_working_hours : null,
              monthly_hours: this.result.monthly_hours ? this.result.monthly_hours : null,
              leave_paid: this.result.leave_paid ? this.getNameByIdPipe.transform(this.result.leave_paid, this.yes_or_no_list) : null,
              holiday_paid: this.result.leave_paid ? this.getNameByIdPipe.transform(this.result.holiday_paid, this.yes_or_no_list) : null,
              work_location: this.result.fields[0].workLocation ? this.getNameByIdPipe.transform(this.result.fields[0].workLocation, this.work_location_list) : null,
              location: this.result.fields[0].country_id ? this.getNameByIdPipe.transform(this.result.fields[0].country_id, this.country_list) : null,
              holiday_calendar:this.result.fields[0].calendar ? this.getNameByIdPipe.transform(this.result.fields[0].calendar, this.holiday_calendar_list) : null,
              service_type: this.selectedOption ? this.getNameByIdPipe.transform(this.selectedOption, this.cards) : null, 
              tags: this.tags && this.tags.length > 0 ? _.map(this.tags, 'name') : null
            }
            if(this.withOpportunity){
              this.newFinancialData = this.withOpportunity ? this.result.financial : null;
              for(let item of this.newFinancialData){
                item.reason = item.reason ? this.getNameByIdPipe.transform(item.reason, this.reason_list) : null;
                item.payment_terms =  item.payment_terms ? this.getNameByIdPipe.transform(item.payment_terms, this.payment_list) : null;
                item.po_date = item.po_date ?  moment(item.po_date).format('DD-MMM-YYYY') : null;
              }
            }
        for (let item of this.p_and_l_list) {
          if (item['id'] == this.result.p_and_l) {
            this.result['p_and_l_name'] = item['name'];
            break;
          } else {
            this.result['p_and_l_name'] = null;
          }
        }
        this.result['at_risk'] = this.risk ? 1 : 0;
        this.result['with_opportunity'] = this.withOpportunity ? 1 : 0;
        if (this.withOpportunity) {
          const financialArray = this.createJobForm.get(
            'financial'
          ) as FormArray;
          for (
            let i = 0;
            i < this.createJobForm.get('financial').value.length;
            i++
          ) {
            const quote = financialArray.at(i).get('quote_id');
          }
          this.result['financial_data'] =
            this.createJobForm.get('financial').value;
        } else {
          this.result['financial_data_without_opportunity'] =this.createJobForm.get('fieldsArray').value
          this.result['financial_data'] = [];
        }
        for (let items of this.currency_list) {
          if (items['id'] == this.data.currency) {
            this.code = items['name'];
          }
        }
        this.result['external_id'] = this.external_id;
        this.result['item_status_id'] = this.item_status;
        this.result['tandmFlag'] = this.TandM_selected;
        this.result['portfolio']=parseInt(this.projectId)
        await this.ProjectCustomCreationService.updateProjectDetails(
          this.oldData,
          this.newData,
          this.result,
          this.selectedOption,
          this.week_array,
          this.createJobForm.get('fields').value,
          this.tags,
          parseInt(this.itemId),
          this.code,
          this.intergeration,
          false,
          this.oldFinancialData,
          this.newFinancialData
        ).then((res: any) => {
          //console.log(res)
          if (res['messType'] == 'S') {
            this.toastr.showSuccess(
              'Success',
              'Project edited Successfully!',
              7000
          );
     
          const projectUrl = this.getProjectUrlLink(this.result.portfolio,res['name'],parseInt(this.itemId),this.result.project_name);
          this._router.navigateByUrl(projectUrl)
            
          } else {
            const projectEdit_unsuccess_msg =
              this.retrieveMessages.length > 0
                ? this.retrieveMessages[0].errors.projectEdit_unsuccess_msg
                  ? this.retrieveMessages[0].errors.projectEdit_unsuccess_msg
                  : 'Project editing Unsuccessfull'
                : 'Project editing Unsuccessfull';
            this.toasterService.showError(projectEdit_unsuccess_msg);
            this.save_disabled = false;
          }
        });
      }
    
    
  }

  async selectOption(option: string): Promise<void> {
    console.log(option)
    if (this.selectedOption != option) {
      this.changeInServiceType = true
      console.log("Service type clicked",option)
      console.log(this.changeInServiceType)
    }
    else {
      this.changeInServiceType = false
    }
    console.log(this.changeInServiceType)
    this.selectedOption = option;
    console.log('select service type')
    console.log(this.selectedOption)

    let internal = _.where(this.cards , {is_internal:1,id:this.selectedOption})

    if (internal.length>0){
      console.log(internal[0])
      this.MakeFieldsNonMandatory = false
      this.checkWithoutOpportunity()

    }
    // else if (internal.length<1){
      
    //   this.changeInServiceType = false

    // }
    else{
      this.MakeFieldsNonMandatory=true
      // this.checkWithOpportunity()
    }
    console.log(this.selectOption)
    console.log(internal)
    console.log(internal[0])

    

    // if(this.selectedOption == '1'){
    //   this.MakeFieldsNonMandatory = false
    // }
    // else{
    //   this.MakeFieldsNonMandatory=true
    // }
    // console.log(this.MakeFieldsNonMandatory)
    if (this.createJobForm.get('project_code').value.length == 0) {
      if (this.selectedOption) {

        let generated_code = _.where(this.formConfig, { field_name: "project_generation_code", type: "project-creation", is_active: true })

        if (generated_code.length > 0) {
          if (generated_code[0]['label'] == "service_type") {
            this.refresh = true

            const res = _.where(this.project_code_list, { service_type_id: this.selectedOption })

            this.createJobForm.patchValue({ [`project_code`]: res.length > 0 ? res[0].projectCode : '' || '' })
            this.defaultValue = true
            this.refresh = false
          }

        }

      }
    }
    else if (this.defaultValue) {


      if (this.selectedOption) {

        let generated_code = _.where(this.formConfig, { field_name: "project_generation_code", type: "project-creation", is_active: true })

        if (generated_code.length > 0) {
          if (generated_code[0]['label'] == "service_type") {
            this.refresh = true

            const res = _.where(this.project_code_list, { service_type_id: this.selectedOption })

            this.createJobForm.patchValue({ [`project_code`]: res.length > 0 ? res[0].projectCode : '' || '' })
            this.defaultValue = true
            this.refresh = false
          }
        }


      }

    }
    this.radioChecked = true
    console.log(this.selectOption)

  }
  
  
  onCloseClick(): void {
    let message;
      message = { messType: "S" };
      this.utilityService
        .openConfirmationSweetAlertWithCustom(
          'Are you sure',
          'You want to Close without saving ?'
        )
        .then((result) => {
          if (result) {
            this.location.back(); // Navigate to the previous route
            this.closeDialog.emit(message);
          }
        });
    
  }
  async filterList() {
    this.entity = this.createJobForm.get('entity').value
    this.division = this.createJobForm.get('division').value
    this.subDivision = this.createJobForm.get('sub_division').value
    let val = {}
    console.log(this.subDivision, this.division, this.entity)
    if (this.subDivision) {
      val['sub_division_id'] = this.subDivision
    }
    if (this.division) {
      val['division_id'] = this.division
    }
    if (this.entity) {
      val['entity_id'] = this.entity
    }
    console.log(val)
    this.displayList = _.where(this.ESdata, val)
    this.selectedData = undefined;
    this.selectedIndex = undefined;

    console.log(this.displayList)
  }

  async getListValue(name, id) {
    let entityChoosen = _.uniq(_.pluck(this.ESdata, id))
    let val_result = []
    for (let entit of entityChoosen) {
      let result = _.where(this.ESdata, { [id]: entit })
      if (result.length > 0) {
        val_result.push({
          "id": result[0][id],
          "name": result[0][name]
        })
      }
    }
    console.log(name, entityChoosen, val_result)
    return val_result
  }

  // closeDialog(){
  //   this.dialogRef.close({messType:"E"})
  // }

  submitData() {
    if (this.selectedData) {
      // this.dialogRef.close({messType:"S", data: this.selectedData})

    }
    else {
      this.utilityService.showMessage("Kindly select option to proceed!", "Dismiss", 3000)
    }
  }
  createFieldsGroup(): FormGroup {
    return this.formBuilder.group({
      project_id: [''],
      project_item_id: [''],
      start_date: ['',Validators.required], // Start with empty value
      end_date: ['',Validators.required],   // Start with empty value
      associate_id: ['',Validators.required],
      email_id: '',
      external_name: '',
      oid: [''],
      project_role_id: ['',Validators.required],
      mail_sent: [false]
    });
  }
  
  createFieldGroup(): FormGroup {
    return this.formBuilder.group({
      po_number: [''],
      po_value: [0],
      po_reference: [''],
      po_date: [''],
      po_start_date:[''],
      po_end_date:[''],
      partner: [''],
      payment_terms: [''],
      invoice_template: ['']
    });
  }

  get fieldsArray(): FormArray {
    return this.createJobForm.get('fieldsArray') as FormArray;
  }

  addFields() {
    this.fieldsArray.push(this.createFieldGroup());
  }

  removeFields(index: number) {
    if (this.fieldsArray.length > 1) {
      this.fieldsArray.removeAt(index);
    }
  }



  get fieldArray(): FormArray {
    return this.createJobForm.get('fields') as FormArray;
  }

  addField() {
    this.fieldArray.push(this.createLocation());
  }

  removeLocationField(index: number) {
    if (this.fieldArray.length > 1) {
      this.fieldArray.removeAt(index);
    }
  }

  selectData(display, i) {
    this.check = true
    console.log(display, i)
    if (this.selectedData) {
      if (this.selectedData == display) {
        this.selectedData = undefined;
        this.selectedIndex = undefined;
      }
      else {
        this.selectedData = display;
        this.selectedIndex = i;
      }
    }
    else {
      this.selectedData = display;
      this.selectedIndex = i;
    }
    console.log(this.selectedData, this.selectedIndex)
    this.entity = this.selectedData.entity_id
    this.division = this.selectedData.division_id
    this.subDivision = this.selectedData.sub_division_id
    this.createJobForm.patchValue({ [`entity`]: this.entity })
    this.createJobForm.patchValue({ [`division`]: this.division })
    this.createJobForm.patchValue({ [`sub_division`]: this.subDivision })
    this.check = false
    console.log(this.selectedData, this.selectedIndex)
  }

  clearFilter() {
    this.displayList = this.ESdata;
    this.subDivision = undefined
    this.division = undefined
    this.entity = undefined
    this.createJobForm.patchValue({ [`entity`]: null })
    this.createJobForm.patchValue({ [`division`]: null })
    this.createJobForm.patchValue({ [`sub_division`]: null })

  }
 
  onRadioChange(event: any) {
    this.radioChecked = false
  }
  checkAdGroup() {
    console.log(this.createJobForm.value.project_type)
    if (this.createJobForm.value.project_type == 1) {
      this.peopleWidth = '470px'
    }
    else {
      this.peopleWidth = '710px'
    }
    console.log(this.peopleWidth)
  }
 
  onMinimizeClick() {
    console.log("minimize")
    this.isMinimized = !this.isMinimized;
    const newWidth = this.isMinimized ? '1016px' : this.originalWidth;
    const newHeight = this.isMinimized ? '100px' : this.originalHeight;
    // const newPosition = this.isMinimized ? { right: '0', bottom: '0' } : {left: '205px',top: '40px',right:'auto',bottom:'auto'}

    console.log(newWidth)

    // this.dialogRef.updatePosition(newPosition);
    // this.dialogRef.updatePosition({ right: offsetX, bottom: offsetY });
    console.log('done')
  }
  
  addMemberField() {
    // if (this.stakeholders.length < 5) {
    this.stakeholders.push({ employee_name: '', start_date: '', end_date: '', role: '', split: '', isChecked: false, isHead: false })
    this.poepleCount = this.stakeholders.length
    // }
  }
  projectDetailsMandatory() {
    console.log(this.createJobForm.valid)
    if (this.createJobForm.valid) {
      this.saveProjectDetails()
    }
    else {
      const enterAll_mandate_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.enterAll_mandate_msg ? this.retrieveMessages[0].errors.enterAll_mandate_msg : 'Kindly Enter All Mandatory fields' : 'Kindly Enter All Mandatory fields';
      this.toasterService.showWarning(enterAll_mandate_msg, 10000)

    }
  }

  storeBillable(i: any, event: Event) {
    const BValue = (event.target as HTMLInputElement).value;
    this.stakeholders[i].isChecked = BValue
  }
  storeHead(i: any) {
    // const HValue = (event.target as HTMLInputElement).value;
    // this.stakeholders[i].isHead=HValue
    console.log("Hvale")
    for (let j = 0; j < this.stakeholders.length; i++) {
      if (i != j) {
        this.stakeholders[j].isHead = false
      }
    }
  }

  addTag() {
    this.isPopupVisible = true
  }

  onStepperChange(stepperData: any) {
    this.stepperData = stepperData;
    console.log('changed stepper data', this.stepperData);
    this.stepperControl();
  }
  getNextStepper() {
    console.log("next called", this.stepperData)
    const index = this.stepperData.findIndex(item => item.is_selected === true);
    console.log(index, this.stepperData.length, this.stepperData.length - 2);
    const man = this.checkDetailsMandatory(this.stepperData[index].type)
    if (man) {
      if (index !== -1 && index <= this.stepperData.length - 2) {
        this.stepperData[index].is_selected = false;
        this.stepperData[index].is_crossed = true;
        this.stepperData[index + 1].is_selected = true;
        //this.stepperData[index].mandate_completed = true;
        console.log(this.stepperData);
      }



      this.stepperControl();
    }
    // else{
    //   const index = this.stepperData.findIndex(item => item.is_selected === true);

    //   if (index !== -1 && index <= this.stepperData.length - 2) {
    //     this.stepperData[index + 1].mandate_completed = false;
    //   }
    // }            

  }
  getPreviousStepper() {
    console.log("previous called", this.stepperData)
    const index = this.stepperData.findIndex(item => item.is_selected === true);
    console.log(index, this.stepperData.length, this.stepperData.length - 1);
    if (index !== -1 && index > 0) {
      this.stepperData[index].is_selected = false;
      this.stepperData[index - 1].is_selected = true;
      this.stepperData[index - 1].is_crossed = false;
      console.log(this.stepperData);
    }
    this.stepperControl();
  }
  stepperControl() {
    const index = this.stepperData.findIndex(item => item.is_selected === true);
    if (index !== -1) {
      const selectedStepper = this.stepperData[index].type;
      //const mandate = this.checkDetailsMandatory(selectedStepper);
      let man;
      switch (selectedStepper) {
        case 'details':
          this.detailStepper = true
          this.enterpriseStepper = false
          this.financialStepper = false
          this.peopleStepper = false
          this.templateStepper = false
          this.scheduleStepper = false
          this.advanceStepper = false
          break;

        case 'es':
          this.detailStepper = false
          this.enterpriseStepper = true
          this.financialStepper = false
          this.peopleStepper = false
          this.templateStepper = false
          this.scheduleStepper = false
          this.advanceStepper = false
          // man = this.checkDetailsMandatory(selectedStepper)
          // if (man) {
          //   this.allStepperData[index-1].is_completed = true;
          //   this.allStepperData[index-1].mandate_completed = true;
          // }
          // else {
          //   this.allStepperData[index-1].is_completed = false;
          //   this.allStepperData[index-1].mandate_completed = false;
          // }
          break;

        case 'financial':
          this.detailStepper = false
          this.enterpriseStepper = false
          this.financialStepper = true
          this.peopleStepper = false
          this.templateStepper = false
          this.scheduleStepper = false
          this.advanceStepper = false
          // man = this.checkDetailsMandatory(selectedStepper)
          // if (man) {
          //   this.allStepperData[index-1].is_completed = true;
          //   this.allStepperData[index-1].mandate_completed = true;
          // }
          // else {
          //   this.allStepperData[index-1].is_completed = false;
          //   this.allStepperData[index-1].mandate_completed = false;
          // }
          break;

        case 'people':
          this.detailStepper = false
          this.enterpriseStepper = false
          this.financialStepper = false
          this.peopleStepper = true
          this.templateStepper = false
          this.scheduleStepper = false
          this.advanceStepper = false
          // man = this.checkDetailsMandatory(selectedStepper)
          // if (man) {
          //   this.allStepperData[index-1].is_completed = true;
          //   this.allStepperData[index-1].mandate_completed = true;
          // }
          // else {
          //   this.allStepperData[index-1].is_completed = false;
          //   this.allStepperData[index-1].mandate_completed = false;
          // }
          break;

        case 'schedule':
          this.detailStepper = false
          this.enterpriseStepper = false
          this.financialStepper = false
          this.peopleStepper = false
          this.templateStepper = false
          this.scheduleStepper = true
          this.advanceStepper = false
          // man = this.checkDetailsMandatory(selectedStepper)
          // if (man) {
          //   this.allStepperData[index-1].is_completed = true;
          //   this.allStepperData[index-1].mandate_completed = true;
          // }
          // else {
          //   this.allStepperData[index-1].is_completed = false;
          //   this.allStepperData[index-1].mandate_completed = false;
          // }
          break;

        case 'template':
          this.detailStepper = false
          this.enterpriseStepper = false
          this.financialStepper = false
          this.peopleStepper = false
          this.templateStepper = true
          this.scheduleStepper = false
          this.advanceStepper = false
          // man = this.checkDetailsMandatory(selectedStepper)
          // if (man) {
          //   this.allStepperData[index-1].is_completed = true;
          //   this.allStepperData[index-1].mandate_completed = true;
          // }
          // else {
          //   this.allStepperData[index-1].is_completed = false;
          //   this.allStepperData[index-1].mandate_completed = false;
          // }
          break;

        case 'advance':
          this.detailStepper = false
          this.enterpriseStepper = false
          this.financialStepper = false
          this.peopleStepper = false
          this.templateStepper = false
          this.scheduleStepper = false
          this.advanceStepper = true
          // man = this.checkDetailsMandatory(selectedStepper)
          // if (man) {
          //   this.allStepperData[index-1].is_completed = true;
          //   this.allStepperData[index-1].mandate_completed = true;
          // }
          // else {
          //   this.allStepperData[index-1].is_completed = false;
          //   this.allStepperData[index-1].mandate_completed = false;
          // }
          break;

        default:
          this.detailStepper = true
          this.enterpriseStepper = false
          this.financialStepper = false
          this.peopleStepper = false
          this.templateStepper = false
          this.scheduleStepper = false
          this.advanceStepper = false
      }
    }
  }
  onInputChange() {
    // Validate and restrict input to valid time values
    this.Fromhours = this.Fromhours.replace(/[^0-9]/g, '');
    this.Fromminutes = this.Fromminutes.replace(/[^0-9]/g, '');

    // Ensure hours and minutes are within valid ranges
    if (parseInt(this.Fromhours) > 23) {
      this.Fromhours = '23';
    }
    if (parseInt(this.Fromminutes) > 59) {
      this.Fromminutes = '59';
    }
    console.log(this.Fromminutes, this.Fromhours)
  }
  onInputChangeTo() {
    // Validate and restrict input to valid time values
    this.Tohours = this.Tohours.replace(/[^0-9]/g, '');
    this.Tominutes = this.Tominutes.replace(/[^0-9]/g, '');

    // Ensure hours and minutes are within valid ranges
    if (parseInt(this.Tohours) > 23) {
      this.Tohours = '23';
    }
    if (parseInt(this.Tominutes) > 59) {
      this.Tominutes = '59';
    }
    console.log(this.Tohours, this.Tominutes)
  }
  checkSaturday() {
    this.saturday = !this.saturday
  }
  checkSunday() {
    this.sunday = !this.sunday
  }
  checkMonday() {
    this.monday = !this.monday
  }
  checkTuesday() {
    this.tuesday = !this.tuesday
  }
  checkWednesday() {
    this.wednesday = !this.wednesday
  }
  checkThursday() {
    this.thursday = !this.thursday
  }
  checkFriday() {
    this.friday = !this.friday
  }
  
 
  isMandate(field: any) {
    const mandate = _.where(this.formConfig, { type: "project-creation", field_name: field, is_active: true });
    if (mandate.length > 0) {
      const isMandate = mandate[0].is_mandant;
      return isMandate;
    }
  }

  checkMandateNotEmpty(data: any, ws: any, week: any, isa: any, selectedOption: any) {
    const financial_data = this.createJobForm.get('financial').value
    const financial_data_without_opportunity=this.createJobForm.get('fieldsArray').value
    let errorOccurred = false;
    const details = _.where(this.stepperData, { type: "details", is_active: true });

    if (details.length > 0) {
      if ((!data.project_code || data.project_code.trim() === '') && this.isMandate('project_code')) {
        // const codeEmptymsg = 'Kindly enter Milestone name';
        // this.toastr.error(codeEmptymsg, 'Error');
        console.log('test')
        errorOccurred = true;
      }
      if ((!data.project_name || data.project_name.trim() === '') && this.isMandate('project_name')) {
        // const codeEmptymsg = 'Kindly enter Milestone name';
        // this.toastr.error(codeEmptymsg, 'Error');
        console.log('test')
        errorOccurred = true;
      }
      if ((data.portfolio === null || data.portfolio === undefined || data.portfolio === '') && this.isMandate('portfolio')) {
        // const customerEmptymsg = 'Responsible Is Mandatory';
        // this.toastr.error(customerEmptymsg, 'Error');
        errorOccurred = true;
      }
      this.checkDates(data.startDate, data.endDate);
      if (!this.dateLogic) {
        errorOccurred = true;
      }
      // if(this.createJobForm.get('startDate').valid)
      // {
      //   console.log('hi1')
      //   errorOccurred =true;
      // }
      if ((data.startDate === null || data.startDate === undefined || data.startDate === '') && this.isMandate('start_date')) {
        // const nameEmptymsg = 'Kindly choose When Is It due?';
        // this.toastr.error(nameEmptymsg, 'Error');
        errorOccurred = true;
      }
      // if(this.createJobForm.get('endDate').valid)
      // {
      //   console.log('hi3')
      //   errorOccurred =true;
      // }
      if ((data.endDate === null || data.endDate === undefined || data.endDate === '') && this.isMandate('end_date')) {
        // const nameEmptymsg = 'Kindly choose When Is It due?';
        // this.toastr.error(nameEmptymsg, 'Error');
        errorOccurred = true;
      }
      if ((data.project_type === null || data.project_type === undefined || data.project_type === '') && this.isMandate('project_type')) {
        // const nameEmptymsg = 'Kindly choose When Is It due?';
        // this.toastr.error(nameEmptymsg, 'Error');
        errorOccurred = true;
      }
      if ((selectedOption === null || selectedOption === undefined || selectedOption === '') && this.isMandate('service_type')) {
        // const nameEmptymsg = 'Kindly choose When Is It due?';
        // this.toastr.error(nameEmptymsg, 'Error');
        errorOccurred = true;
      }
      if ((!data.description || data.description.trim() === '') && this.isMandate('project_description')) {
        // const codeEmptymsg = 'Kindly enter Milestone name';
        // this.toastr.error(codeEmptymsg, 'Error');
        errorOccurred = true;
      }
      if ((data.currency === null || data.currency === undefined || data.currency === '') && this.isMandate('currency')) {
        // const customerEmptymsg = 'Responsible Is Mandatory';
        // this.toastr.error(customerEmptymsg, 'Error');
        errorOccurred = true;
        const currency_mandate_err = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.currency_mandate_err ? this.retrieveMessages[0].errors.currency_mandate_err : 'Currency is mandatory' : 'Currency is mandatory';
        this.toasterService.showWarning(currency_mandate_err, 10000)
      }



    }

    const es = _.where(this.stepperData, { type: "es", is_active: true });
    if (es.length > 0) {
      if ((data.entity === null || data.entity === undefined || data.entity === '') && this.isMandate('entity')) {
        // const customerEmptymsg = 'Responsible Is Mandatory';
        // this.toastr.error(customerEmptymsg, 'Error');
        errorOccurred = true;
      }
      if ((data.division === null || data.division === undefined || data.division === '') && this.isMandate('division')) {
        // const customerEmptymsg = 'Responsible Is Mandatory';
        // this.toastr.error(customerEmptymsg, 'Error');
        errorOccurred = true;
      }
      if ((data.sub_division === null || data.sub_division === undefined || data.sub_division === '') && this.isMandate('sub_division')) {
        // const customerEmptymsg = 'Responsible Is Mandatory';
        // this.toastr.error(customerEmptymsg, 'Error');
        errorOccurred = true;
      }
    }

    const financial = _.where(this.stepperData, { type: "financial", is_active: true });
    if (financial.length > 0) {
      if (this.withOpportunity) {
        for (let item of financial_data) {
          if (item['is_active'] == 1) {
            if ((item['opportunity_id'] === null || item['opportunity_id'] === undefined || item['opportunity_id'] === '') && this.isMandate('opportunity') && this.MakeFieldsNonMandatory) {
              // const customerEmptymsg = 'Responsible Is Mandatory';
              // this.toastr.error(customerEmptymsg, 'Error');
              errorOccurred = true;
              break;
            }
            else if ((item['reason'] === null || item['reason'] === undefined || item['reason'] === '') && this.isMandate('reason')) {
              // const customerEmptymsg = 'Responsible Is Mandatory';
              // this.toastr.error(customerEmptymsg, 'Error');
              errorOccurred = true;
              break;
            }
            else if ((item['quote_id'] === null || item['quote_id'] === undefined || item['quote_id'] === '') && this.isMandate('quote')) {
              // const codeEmptymsg = 'Kindly enter Milestone name';
              // this.toastr.error(codeEmptymsg, 'Error');
              errorOccurred = true;
              break;
            }
            else if ((!item['po_number'] || item['po_number'].trim() === '') && this.isMandate('po_number_with_opportunity') && !this.poNonMandate && this.MakeFieldsNonMandatory) {
              // const codeEmptymsg = 'Kindly enter Milestone name';
              // this.toastr.error(codeEmptymsg, 'Error');
              errorOccurred = true;
              break;
            }
            else if ((item['purchase_order'] === null || item['purchase_order'] === undefined || item['purchase_order'] === '') && this.isMandate('po_value_with_opportunity') && this.MakeFieldsNonMandatory) {
              errorOccurred = true;
              break;
            }
          }
        }
      }
      else {
        if ((data.invoice_template === null || data.invoice_template === undefined || data.invoice_template === '') && this.isMandate('invoice_template')) {
          // const customerEmptymsg = 'Responsible Is Mandatory';
          // this.toastr.error(customerEmptymsg, 'Error');
          errorOccurred = true;
          console.log("IV")
        }
        if ((!data.po_number || data.po_number.trim() === '') && this.isMandate('po_number') && !this.poNonMandate && this.MakeFieldsNonMandatory) {
          // const codeEmptymsg = 'Kindly enter Milestone name';
          // this.toastr.error(codeEmptymsg, 'Error');
          errorOccurred = true;
          console.log("pon")
        }
        if ((data.po_value === null || data.po_value === undefined || data.po_value === '') && this.isMandate('po_value') && this.MakeFieldsNonMandatory) {
          // const codeEmptymsg = 'Kindly enter Milestone name';
          // this.toastr.error(codeEmptymsg, 'Error');
          errorOccurred = true;
          console.log("pov")
        }
      }

    }

    const schedule = _.where(this.stepperData, { type: "schedule", is_active: true });
    if (schedule.length > 0) {
      if (week.length == 0 && this.isMandate('week')) {
        errorOccurred = true;
      }
      for (let i = 0; i < ws.length; i++) {
        if ((ws[i].workLocation === null || ws[i].workLocation === undefined || ws[i].workLocation === '') && this.isMandate('work_location')) {
          // const customerEmptymsg = 'Responsible Is Mandatory';
          // this.toastr.error(customerEmptymsg, 'Error');
          errorOccurred = true;
        }
        if ((ws[i].calendar === null || ws[i].calendar === undefined || ws[i].calendar === '') && this.isMandate('holiday_calendar')) {
          // const customerEmptymsg = 'Responsible Is Mandatory';
          // this.toastr.error(customerEmptymsg, 'Error');
          errorOccurred = true;
        }
        if ((!ws[i].location || ws[i].location.trim() === '') && this.isMandate('location')) {
          // const codeEmptymsg = 'Kindly enter Milestone name';
          // this.toastr.error(codeEmptymsg, 'Error');
          errorOccurred = true;
        }

      }
      if ((!data.from || data.from.trim() === '') && this.isMandate('from')) {
        // const codeEmptymsg = 'Kindly enter Milestone name';
        // this.toastr.error(codeEmptymsg, 'Error');
        errorOccurred = true;
      }
      if ((!data.to || data.to.trim() === '') && this.isMandate('to')) {
        // const codeEmptymsg = 'Kindly enter Milestone name';
        // this.toastr.error(codeEmptymsg, 'Error');
        errorOccurred = true;
      }
    }

    const people = _.where(this.stepperData, { type: "people", is_active: true });
    if (people.length > 0) {

      for (let i = 0; i < isa.length; i++) {
       
        
        if(isa[i].employee_name != null && isa[i].employee_name != undefined && isa[i].employee_name != '')
        {

          if (!this.checkInternalStakeholderDates(isa[i].start_date, isa[i].end_date, data.startDate, data.endDate)) {
            errorOccurred = true;
            this.dateLogic=false;
          }
          if ((isa[i].employee_name === null || isa[i].employee_name === undefined || isa[i].employee_name === '') && this.isMandate('name')) {
            // const customerEmptymsg = 'Responsible Is Mandatory';
            // this.toastr.error(customerEmptymsg, 'Error');
            this.people_name = false;
            errorOccurred = true;
          }
          if ((isa[i].start_date === null || isa[i].start_date === undefined || isa[i].start_date === '') && (this.isMandate('isa_start_date'))) {
            // const customerEmptymsg = 'Responsible Is Mandatory';
            // this.toastr.error(customerEmptymsg, 'Error');
            errorOccurred = true;
          }
          if ((isa[i].end_date === null || isa[i].end_date === undefined || isa[i].end_date === '') && (this.isMandate('isa_end_date'))) {
            // const customerEmptymsg = 'Responsible Is Mandatory';
            // this.toastr.error(customerEmptymsg, 'Error');
            errorOccurred = true;
          }

          if ((isa[i].role === null || isa[i].role === undefined || isa[i].role === '') && this.isMandate('role')) {
            // const customerEmptymsg = 'Responsible Is Mandatory';
            // this.toastr.error(customerEmptymsg, 'Error');
            errorOccurred = true;
          }
          if ((isa[i].split === null || isa[i].split === undefined || isa[i].split === '') && this.isMandate('capacity')) {
            // const customerEmptymsg = 'Responsible Is Mandatory';
            // this.toastr.error(customerEmptymsg, 'Error');
            errorOccurred = true;
          }
        }

      }
    }

    let check = _.where(this.formConfig,{type:"project-creation", field_name:"one_member", is_active:true})

    if(check.length>0)
    {
      if(isa.length==0)
      {
          const people_not_empty = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.people_not_empty ? this.retrieveMessages[0].errors.people_not_empty : 'Atleast 1 People needs to be allocated!' : 'Atleast 1 People needs to be allocated!';
          
          this.toasterService.showWarning(people_not_empty, 10000)

          return false
      }

      if (errorOccurred && !this.dateLogic && !this.people_name) {
        const enterAll_mandate_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.enterAll_mandate_msg ? this.retrieveMessages[0].errors.enterAll_mandate_msg : 'Kindly Enter All Mandatory fields' : 'Kindly Enter All Mandatory fields';
        this.toasterService.showWarning(enterAll_mandate_msg, 10000)
        return false; // Return false if any error occurred
      } else {
        return true; // Return true if no errors occurred
      }
    }
    else
    {
      if(isa.length>0)
      {
        if(!this.people_name)
        {
            return true;
        }
        else
        {
          if (errorOccurred && !this.dateLogic) {
            const enterAll_mandate_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.enterAll_mandate_msg ? this.retrieveMessages[0].errors.enterAll_mandate_msg : 'Kindly Enter All Mandatory fields' : 'Kindly Enter All Mandatory fields';
            this.toasterService.showWarning(enterAll_mandate_msg, 10000)

            return false; // Return false if any error occurred
          } else {
            return true; // Return true if no errors occurred
          }
        }
      }
    }

    if(errorOccurred){
      const enterAll_mandate_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.enterAll_mandate_msg ? this.retrieveMessages[0].errors.enterAll_mandate_msg : 'Kindly Enter All Mandatory fields' : 'Kindly Enter All Mandatory fields';
      this.toasterService.showWarning(enterAll_mandate_msg, 10000)
      return false
    }
    else{
      return true
    }
  }

  checkDetailsMandatory(type: any) {
    const data = this.createJobForm.value
    const financial_data = this.createJobForm.get('financial').value
    const financial_data_without_opportunity=this.createJobForm.get('fieldsArray').value
    //const details = _.where(this.stepperData, { type: "details", is_active: true });
    let errorOccurred = false;
    let validateError = false
    if (type == 'details') {
      if ((!data.project_code || data.project_code.trim() === '') && this.isMandate('project_code')) {
        // const codeEmptymsg = 'Kindly enter Milestone name';
        const codeEmptymsg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.code_empty_msg ? this.retrieveMessages[0].errors.code_empty_msg : 'Project Code is Mandatory' : 'Project Code is Mandatory';
        this.toasterService.showWarning(codeEmptymsg, 10000)
        // this.toastr.error(codeEmptymsg, 'Error');
        errorOccurred = true;
      }
      else if ((!data.project_name || data.project_name.trim() === '') && this.isMandate('project_name')) {
        // const codeEmptymsg = 'Kindly enter Milestone name';
        const name_empty_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.name_empty_msg ? this.retrieveMessages[0].errors.name_empty_msg : 'Project Name is Mandatory' : 'Project Name is Mandatory';
        this.toasterService.showWarning(name_empty_msg, 10000)
        // this.toastr.error(codeEmptymsg, 'Error');
        errorOccurred = true;
      }
      else if ((data.portfolio === null || data.portfolio === undefined || data.portfolio === '') && this.isMandate('portfolio')) {
        // const customerEmptymsg = 'Responsible Is Mandatory';
        const portfolio_empty_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.portfolio_empty_msg ? this.retrieveMessages[0].errors.portfolio_empty_msg : 'Portfolio is Mandatory' : 'Portfolio is Mandatory';
        this.toasterService.showWarning(portfolio_empty_msg, 10000)
        // this.toastr.error(customerEmptymsg, 'Error');
        errorOccurred = true;
      }
      
      else if(!(this.createJobForm.get('startDate').valid))
      {
        const startDate_empty_msg ='Invalid Start Date';
        this.toasterService.showWarning(startDate_empty_msg, 10000)
        this.dateLogic = true;
        errorOccurred = true;
      }
      else if ((data.startDate === null || data.startDate === undefined || data.startDate === '') && this.isMandate('start_date')) {
        // const nameEmptymsg = 'Kindly choose When Is It due?';
        if (data.startDate === '') {
          const startDate_empty_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.startDate_empty_msg ? this.retrieveMessages[0].errors.startDate_empty_msg : 'Start date is Mandatory' : 'Start date is Mandatory';
          this.toasterService.showWarning(startDate_empty_msg, 10000)
        }
        else if (data.startDate === null) {
          const startDate_invalid_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.startDate_invalid_msg ? this.retrieveMessages[0].errors.startDate_invalid_msg : 'Kindly Choose/Enter start date in DD-MM-YYYY format' : 'Kindly Choose/Enter start date in DD-MM-YYYY format';
          this.toasterService.showWarning(startDate_invalid_msg, 10000)
        }
        // this.toastr.error(nameEmptymsg, 'Error');
        this.dateLogic = true;
        errorOccurred = true;
      }
      else if(!(this.createJobForm.get('endDate').valid))
      {
        const endDate_empty_msg ='Invalid End Date';
        this.toasterService.showWarning(endDate_empty_msg, 10000)
        this.dateLogic = true;
        errorOccurred = true;
      }
      else if ((data.endDate === null || data.endDate === undefined || data.endDate === '') && this.isMandate('end_date')) {
        // const nameEmptymsg = 'Kindly choose When Is It due?';
        // this.toastr.error(nameEmptymsg, 'Error');
        if (data.endDate === '') {
          const endDate_empty_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.startDate_empty_msg ? this.retrieveMessages[0].errors.startDate_empty_msg : 'End date is Mandatory' : 'End date is Mandatory';
          this.toasterService.showWarning(endDate_empty_msg, 10000)
        }
        else if (data.endDate === null) {
          const endDate_invalid_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.endDate_invalid_msg ? this.retrieveMessages[0].errors.endDate_invalid_msg : 'Kindly Choose/Enter end date in DD-MM-YYYY format' : 'Kindly Choose/Enter end date in DD-MM-YYYY format';
          this.toasterService.showWarning(endDate_invalid_msg, 10000)
        }
        this.dateLogic = true;
        errorOccurred = true;

      }
      this.checkDates(data.startDate, data.endDate);
      if (!this.dateLogic && !errorOccurred) {
        errorOccurred = true;
        const valid_date_err = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.valid_date_err ? this.retrieveMessages[0].errors.valid_date_err : 'Kindly enter valid date' : 'Kindly enter valid date';
        this.toasterService.showWarning(valid_date_err, 10000)
      }
      else if ((data.project_type === null || data.project_type === undefined || data.project_type === '') && this.isMandate('project_type') && !errorOccurred) {
        // const nameEmptymsg = 'Kindly choose When Is It due?';
        const projectType_empty_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.projectType_empty_msg ? this.retrieveMessages[0].errors.projectType_empty_msg : 'Project Type is Mandatory' : 'Project Type is Mandatory';
        this.toasterService.showWarning(projectType_empty_msg, 10000)
        // this.toastr.error(nameEmptymsg, 'Error');
        errorOccurred = true;
      }
      else if ((data.currency === null || data.currency === undefined || data.currency === '') && this.isMandate('currency')) {
        // const customerEmptymsg = 'Responsible Is Mandatory';
        // this.toastr.error(customerEmptymsg, 'Error');
        const currency_mandate_err = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.currency_mandate_err ? this.retrieveMessages[0].errors.currency_mandate_err : 'Currency is mandatory' : 'Currency is mandatory';
        errorOccurred = true;
        this.toasterService.showWarning(currency_mandate_err, 10000)
      }
      else if ((this.selectedOption === null || this.selectedOption === undefined || this.selectedOption === '') && this.isMandate('service_type') && !errorOccurred) {
        // const nameEmptymsg = 'Kindly choose When Is It due?';
        const serviceType_empty_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.serviceType_empty_msg ? this.retrieveMessages[0].errors.serviceType_empty_msg : 'Service Type is Mandatory' : 'Service Type is Mandatory';
        this.toasterService.showWarning(serviceType_empty_msg, 10000)
        // this.toastr.error(nameEmptymsg, 'Error');
        errorOccurred = true;
      }
      else if ((!data.description || data.description.trim() === '') && this.isMandate('project_description') && !errorOccurred) {
        // const codeEmptymsg = 'Kindly enter Milestone name';
        const description_empty_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.description_empty_msg ? this.retrieveMessages[0].errors.description_empty_msg : 'Description is Mandatory' : 'Description is Mandatory';
        this.toasterService.showWarning(description_empty_msg, 10000)
        // this.toastr.error(codeEmptymsg, 'Error');
        errorOccurred = true;
      }
      else if (this.valid == false) {
        validateError = true
      }
      else if (!errorOccurred && !validateError) {
        for (let items of this.stepperData) {
          if (items['type'] == 'details') {
            items['is_completed'] = true
          }
          return true
        }
      }
      else if (validateError && !errorOccurred) {
        const code_invalid_err = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.code_invalid_err ? this.retrieveMessages[0].errors.code_invalid_err : 'Invalid Project Code! Kindly check and try again' : 'Invalid Project Code! Kindly check and try again';
        this.toasterService.showWarning(code_invalid_err, 10000)
      }
      else if (!this.dateLogic) {
        const isa_dateLogic = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.isa_dateLogic ? this.retrieveMessages[0].errors.isa_dateLogic : 'Kindly Enter Valid Date!' : 'Kindly Enter Valid Date!';
        this.toasterService.showWarning(isa_dateLogic, 10000)
      }
      else if (errorOccurred) {
        //this.toastr.warning("Kindly Enter All Mandatory fields", 'Warning');
        return false
      }


    }
    if (type == 'es') {
      if ((data.entity === null || data.entity === undefined || data.entity === '') && this.isMandate('entity')) {
        // const customerEmptymsg = 'Responsible Is Mandatory';
        const entity_empty = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.entity_empty ? this.retrieveMessages[0].errors.entity_empty : 'Entity is mandatory' : 'Entity is mandatory';

        this.toasterService.showWarning(entity_empty, 10000)
        // this.toastr.error(customerEmptymsg, 'Error');
        errorOccurred = true;
      }
      else if ((data.division === null || data.division === undefined || data.division === '') && this.isMandate('division')) {
        // const customerEmptymsg = 'Responsible Is Mandatory';
        const division_empty = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.division_empty ? this.retrieveMessages[0].errors.division_empty : 'Division is mandatory' : 'Division is mandatory';

        this.toasterService.showWarning(division_empty, 10000)
        // this.toastr.error(customerEmptymsg, 'Error');
        errorOccurred = true;
      }
      else if ((data.sub_division === null || data.sub_division === undefined || data.sub_division === '') && this.isMandate('sub_division')) {
        // const customerEmptymsg = 'Responsible Is Mandatory';
        const subdivision_empty = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.subdivision_empty ? this.retrieveMessages[0].errors.subdivision_empty : 'Sub Division is mandatory' : 'Sub Division is mandatory';

        this.toasterService.showWarning(subdivision_empty, 10000)
        // this.toastr.error(customerEmptymsg, 'Error');
        errorOccurred = true;
      }
      else if ((data.child_customer === null || data.child_customer === undefined || data.child_customer === '') && this.isMandate('child_customer')) {
        // const customerEmptymsg = 'Responsible Is Mandatory';
        const subdivision_empty ='Child Customer is mandatory';

        this.toasterService.showWarning(subdivision_empty, 10000)
        // this.toastr.error(customerEmptymsg, 'Error');
        errorOccurred = true;
      }
      else if ((data.p_and_l === null || data.p_and_l === undefined || data.p_and_l === '') && this.isMandate('p_and_l')) {
        // const customerEmptymsg = 'Responsible Is Mandatory';
        const profitCenter_mandate_msg = this.retrieveMessages[0].errors.profitCenter_mandate_msg ? this.retrieveMessages[0].errors.profitCenter_mandate_msg ? this.retrieveMessages[0].errors.profitCenter_mandate_msg : 'Profit Center is Mandatory' : 'Profit Center is Mandatory';
        this.toasterService.showWarning(profitCenter_mandate_msg, 10000)
        // this.toastr.error(customerEmptymsg, 'Error');
        errorOccurred = true;
      }
      else if ((data.legal_entity === null || data.legal_entity === undefined || data.legal_entity === '') && this.isMandate('legal_entity')) {
        // const customerEmptymsg = 'Responsible Is Mandatory';
        const legalEntity_mandate_msg = this.retrieveMessages[0].errors.legalEntity_mandate_msg ? this.retrieveMessages[0].errors.legalEntity_mandate_msg ? this.retrieveMessages[0].errors.legalEntity_mandate_msg : 'SOW Owner is Mandatory' : 'SOW Owner is Mandatory';
        this.toasterService.showWarning(legalEntity_mandate_msg, 10000)
        // this.toastr.error(customerEmptymsg, 'Error');
        errorOccurred = true;
      }
      else if ((data.sow_reference_number === null || data.sow_reference_number === undefined || data.sow_reference_number === '') ) {
        if(this.isMandate('sow_reference_number')){
          // const customerEmptymsg = 'Responsible Is Mandatory';
          const sowRef_mandate_msg = this.retrieveMessages[0].errors.sowRef_mandate_msg ? this.retrieveMessages[0].errors.sowRef_mandate_msg ? this.retrieveMessages[0].errors.sowRef_mandate_msg : 'SOW Reference Number is Mandatory' : 'SOW Reference Number is Mandatory';
          this.toasterService.showWarning(sowRef_mandate_msg, 10000);
          errorOccurred = true;
        }
        // this.toastr.error(customerEmptymsg, 'Error');
      }
      else if (data.sow_reference_number){
        if(this.isSowReferenceNumberRefresh){
          this.toasterService.showWarning("Please wait while SOW reference number is being verified.", 10000);
          errorOccurred = true;
        }
        if(!this.sowReferenceNumberValid && this.invalidSowReferenceNumberLength){
          const sow_invalid_length_msg = this.retrieveMessages[0]?.errors?.sow_invalid_length ? this.retrieveMessages[0].errors.sow_invalid_length : 'SOW Reference Number too short';
          this.toasterService.showWarning(sow_invalid_length_msg, 10000);
          errorOccurred = true;
        }
        if(!this.sowReferenceNumberValid && this.SowReferenceNumberDuplicated){
          const sow_ref_duplicated_msg = this.retrieveMessages[0]?.errors?.sow_ref_duplicated ? this.retrieveMessages[0].errors.sow_ref_duplicated : 'SOW Reference Number already exists';
          this.toasterService.showWarning(sow_ref_duplicated_msg, 10000);
          errorOccurred = true;
        }
      }
      else if ((data.product_category === null || data.product_category === undefined || data.product_category === '') && this.isMandate('product_category')) {
        // const customerEmptymsg = 'Responsible Is Mandatory';
        const product_category_mandate_msg = this.retrieveMessages[0].errors.product_category_mandate_msg ? this.retrieveMessages[0].errors.product_category_mandate_msg ? this.retrieveMessages[0].errors.product_category_mandate_msg : 'Product Category is Mandatory' : 'Product Category is Mandatory';
        this.toasterService.showWarning(product_category_mandate_msg, 10000)
        // this.toastr.error(customerEmptymsg, 'Error');
        errorOccurred = true;
      }
      else if ((data.delivery_type === null || data.delivery_type === undefined || data.delivery_type === '') && this.isMandate('delivery_type')) {
        // const customerEmptymsg = 'Responsible Is Mandatory';
        const delivery_type_mandate_msg = this.retrieveMessages[0].errors.delivery_type_mandate_msg ? this.retrieveMessages[0].errors.delivery_type_mandate_msg ? this.retrieveMessages[0].errors.delivery_type_mandate_msg : 'Delivery Type is Mandatory' : 'Delivery Type is Mandatory';
        this.toasterService.showWarning(delivery_type_mandate_msg, 10000)
        // this.toastr.error(customerEmptymsg, 'Error');
        errorOccurred = true;
      }
      else if ((data.revenue_type === null || data.revenue_type === undefined || data.revenue_type === '') && this.isMandate('revenue_type')) {
        // const customerEmptymsg = 'Responsible Is Mandatory';
        const revenue_type_mandate_msg = this.retrieveMessages[0].errors.revenue_type_mandate_msg ? this.retrieveMessages[0].errors.revenue_type_mandate_msg ? this.retrieveMessages[0].errors.revenue_type_mandate_msg : 'Revenue Type is Mandatory' : 'Revenue Type is Mandatory';
        this.toasterService.showWarning(revenue_type_mandate_msg, 10000)
        // this.toastr.error(customerEmptymsg, 'Error');
        errorOccurred = true;
      }
      if (!errorOccurred) {
        for (let items of this.stepperData) {
          if (items['type'] == 'es') {
            items['is_completed'] = true
          }
        }
        return true
      }
      else if (errorOccurred) {
        //this.toastr.warning("Kindly Enter All Mandatory fields", 'Warning');
        return false
      }
    }
    if (type == 'financial') {

      if (this.withOpportunity) {
        for (let item of financial_data) {
          if (item['is_active'] == 1) {
            if ((item['opportunity_id'] === null || item['opportunity_id'] === undefined || item['opportunity_id'] === '') && this.isMandate('opportunity') && this.MakeFieldsNonMandatory) {
              const opportunity_empty = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.opportunity_empty ? this.retrieveMessages[0].errors.opportunity_empty : 'Opportunity is mandatory' : 'Opportunity is mandatory';

              this.toasterService.showWarning(opportunity_empty, 10000)
              // const customerEmptymsg = 'Responsible Is Mandatory';
              // this.toastr.error(customerEmptymsg, 'Error');
              errorOccurred = true;
              break;
            }
            else if ((item['reason'] === null || item['reason'] === undefined || item['reason'] === '') && this.isMandate('reason')) {
              // const customerEmptymsg = 'Responsible Is Mandatory';
              // this.toastr.error(customerEmptymsg, 'Error');
              const reason_mandate_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.reason_mandate_msg ? this.retrieveMessages[0].errors.reason_mandate_msg : 'Reason is Mandatory' : 'Reason is Mandatory';
              errorOccurred = true;
              this.toasterService.showWarning(reason_mandate_msg, 10000)
              break;
            }
            else if ((item['quote_id'] === null || item['quote_id'] === undefined || item['quote_id'] === '') && this.isMandate('quote')) {
              // const codeEmptymsg = 'Kindly enter Milestone name';
              const quote_empty = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.quote_empty ? this.retrieveMessages[0].errors.quote_empty : 'Quote is mandatory' : 'Quote is mandatory';

              this.toasterService.showWarning(quote_empty, 10000)
              // this.toastr.error(codeEmptymsg, 'Error');
              errorOccurred = true;
              break;
            }
            else if ((!item['po_number'] || item['po_number'].trim() === '') && this.isMandate('po_number_with_opportunity') && !this.poNonMandate && this.MakeFieldsNonMandatory) {
              // const codeEmptymsg = 'Kindly enter Milestone name';
              // this.toastr.error(codeEmptymsg, 'Error');
              const poNumber_mandate_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.poNumber_mandate_msg ? this.retrieveMessages[0].errors.poNumber_mandate_msg : 'PO Number is mandatory' : 'PO Number is mandatory';
              this.toasterService.showWarning(poNumber_mandate_msg, 10000)
              errorOccurred = true;
              break;
            }
            else if ((item['purchase_order'] === null || item['purchase_order'] === undefined || item['purchase_order'] === '') && this.isMandate('po_value_with_opportunity') && this.MakeFieldsNonMandatory) {
              // const codeEmptymsg = 'Kindly enter Milestone name';
              // this.toastr.error(codeEmptymsg, 'Error');
              const orderValue_mandate_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.orderValue_mandate_msg ? this.retrieveMessages[0].errors.orderValue_mandate_msg : 'Order value is mandatory' : 'Order value is mandatory';
              this.toasterService.showWarning(orderValue_mandate_msg, 10000)
              errorOccurred = true;
              break;
            }
            else if ((item['purchase_order'] == 0) && this.isMandate('po_value_with_opportunity') && this.MakeFieldsNonMandatory) {
              // const codeEmptymsg = 'Kindly enter Milestone name';
              // this.toastr.rror(codeEmptymsg, 'Error');
              const orderValue_mandate_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.orderValue_mandate_msg ? this.retrieveMessages[0].errors.orderValue_mandate_msg : 'Order value is mandatory' : 'Order value is mandatory';
              this.toasterService.showWarning(orderValue_mandate_msg, 10000)
              errorOccurred = true;
              break;
            }
            else if ((item['payment_terms'] === null || item['payment_terms'] === undefined || item['payment_terms'] === '') && this.isMandate('payment_terms')) {
              // const codeEmptymsg = 'Kindly enter Milestone name';
              const paymentTerms_empty = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.paymentTerms_empty ? this.retrieveMessages[0].errors.paymentTerms_empty : 'Payment Term is mandatory' : 'Payment Term is mandatory';

              this.toasterService.showWarning(paymentTerms_empty, 10000)
              // this.toastr.error(codeEmptymsg, 'Error');
              errorOccurred = true;
              break;
            }
            else if ((item['partner'] === null || item['partner'] === undefined || item['partner'] === '') && this.isMandate('partner')) {
              // const codeEmptymsg = 'Kindly enter Milestone name';
              // this.toastr.error(codeEmptymsg, 'Error');
              const partner_empty = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.partner_empty ? this.retrieveMessages[0].errors.partner_empty : 'Partner is mandatory' : 'Partner is mandatory';
              this.toasterService.showWarning(partner_empty, 10000)
              errorOccurred = true;
              break;
            }
            else if ((item['po_date'] === null || item['po_date'] === undefined || item['po_date'] === '') && this.isMandate('po_date')) {
              // const codeEmptymsg = 'Kindly enter Milestone name';
              // this.toastr.error(codeEmptymsg, 'Error');
              const poDate_empty = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.poDate_empty ? this.retrieveMessages[0].errors.poDate_empty : 'PO Date value is mandatory' : 'PO Date is mandatory';
              this.toasterService.showWarning(poDate_empty, 10000)

              errorOccurred = true;
              break;
            }
            else if ((item['po_reference'] == 0) && this.isMandate('po_reference')) {
              // const codeEmptymsg = 'Kindly enter Milestone name';
              // this.toastr.rror(codeEmptymsg, 'Error');
              const poReference_empty = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.poReference_empty ? this.retrieveMessages[0].errors.poReference_empty : 'PO Reference Cannot be zero' : 'PO Reference value Cannot be zero';
              this.toasterService.showWarning(poReference_empty, 10000)

              errorOccurred = true;
              break;
            }
          }
        }
        if (!errorOccurred) {
          let isa_active_oppotunity = _.where(financial_data, { is_active: 1 })
          if (isa_active_oppotunity.length > 1) {
            for (let item of financial_data) {
              let check_duplicate
              check_duplicate = _.where(financial_data, { opportunity_id: item['opportunity_id'] })
              if (check_duplicate.length > 1) {
                const opportunityDuplicate_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.opportunityDuplicate_msg ? this.retrieveMessages[0].errors.opportunityDuplicate_msg : 'Duplicate Opportunity will not be allowed' : 'Duplicate Opportunity will not be allowed';

                this.toasterService.showWarning(opportunityDuplicate_msg, 10000)
                errorOccurred = true;
                break;
              }
            }
          }
        }
        if (!errorOccurred) {
          for (let items of this.stepperData) {
            if (items['type'] == 'financial') {
              items['is_completed'] = true
              return true
            }
          }
        }
        else if (errorOccurred) {
          //this.toastr.warning("Kindly Enter All Mandatory fields", 'Warning');
          return false
        }
      }
      else {
        for (let data of financial_data_without_opportunity){
        if ((data.invoice_template === null || data.invoice_template === undefined || data.invoice_template === '') && this.isMandate('invoice_template')) {
          // const customerEmptymsg = 'Responsible Is Mandatory';
          const invoiceTemplate_empty = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.invoiceTemplate_empty ? this.retrieveMessages[0].errors.invoiceTemplate_empty : 'Invoice Template is mandatory' : 'Invoice Template is mandatory';

          this.toasterService.showWarning(invoiceTemplate_empty, 10000)
          // this.toastr.error(customerEmptymsg, 'Error');
          errorOccurred = true;
        }

        else if ((!data.po_number || data.po_number.trim() === '') && this.isMandate('po_number') && !this.poNonMandate && this.MakeFieldsNonMandatory) {
          // const codeEmptymsg = 'Kindly enter Milestone name';
          // this.toastr.error(codeEmptymsg, 'Error');
          const poNumber_mandate_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.poNumber_mandate_msg ? this.retrieveMessages[0].errors.poNumber_mandate_msg : 'PO Number is mandatory' : 'PO Number is mandatory';

          this.toasterService.showWarning(poNumber_mandate_msg, 10000)
          errorOccurred = true;
        }
        else if ((data.po_value === null || data.po_value === undefined || data.po_value === '') && this.isMandate('po_value') && this.MakeFieldsNonMandatory) {
          // const codeEmptymsg = 'Kindly enter Milestone name';
          // this.toastr.error(codeEmptymsg, 'Error');
          const orderValue_mandate_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.orderValue_mandate_msg ? this.retrieveMessages[0].errors.orderValue_mandate_msg : 'Order value is mandatory' : 'Order value is mandatory';

          this.toasterService.showWarning(orderValue_mandate_msg, 10000)
          errorOccurred = true;
        }
        else if ((data.po_reference === null || data.po_reference === undefined || data.po_reference === '') && this.isMandate('po_reference')) {
          // const codeEmptymsg = 'Kindly enter Milestone name';
          // this.toastr.error(codeEmptymsg, 'Error');
          const poReference_empty = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.poReference_empty ? this.retrieveMessages[0].errors.poReference_empty : 'PO Reference Cannot be zero' : 'PO Reference value Cannot be zero';

          this.toasterService.showWarning(poReference_empty, 10000)
          errorOccurred = true;
        }
        else if ((data.po_date === null || data.po_date === undefined || data.po_date === '') && this.isMandate('po_date')) {
          // const codeEmptymsg = 'Kindly enter Milestone name';
          // this.toastr.error(codeEmptymsg, 'Error');
          const poDate_empty = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.poDate_empty ? this.retrieveMessages[0].errors.poDate_empty : 'PO Date value is mandatory' : 'PO Date is mandatory';
          this.toasterService.showWarning(poDate_empty, 10000)
          errorOccurred = true;
        }
        else if ((data.partner === null || data.partner === undefined || data.partner === '') && this.isMandate('partner')) {
          // const customerEmptymsg = 'Responsible Is Mandatory';
          const partner_empty = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.partner_empty ? this.retrieveMessages[0].errors.partner_empty : 'Partner is mandatory' : 'Partner is mandatory';

          this.toasterService.showWarning(partner_empty, 10000)
          // this.toastr.error(customerEmptymsg, 'Error');
          errorOccurred = true;
        }
        else if ((data.payment_terms === null || data.payment_terms === undefined || data.payment_terms === '') && this.isMandate('payment_terms')) {
          // const customerEmptymsg = 'Responsible Is Mandatory';
          const paymentTerms_empty = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.paymentTerms_empty ? this.retrieveMessages[0].errors.paymentTerms_empty : 'Payment Term is mandatory' : 'Payment Term is mandatory';
          
          this.toasterService.showWarning(paymentTerms_empty, 10000)
          // this.toastr.error(customerEmptymsg, 'Error');
          errorOccurred = true;
        }
        else if ((data.po_value == 0) && this.isMandate('po_value') && this.MakeFieldsNonMandatory) {
          // const codeEmptymsg = 'Kindly enter Milestone name';
          // this.toastr.error(codeEmptymsg, 'Error');
          const poValue_zero_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.poValue_zero_msg ? this.retrieveMessages[0].errors.poValue_zero_msg : 'Order value cannot be zero' : 'Order value cannot be zero';

          this.toasterService.showWarning(poValue_zero_msg, 10000)
          errorOccurred = true;
        }
      }
        if (!errorOccurred) {
          for (let items of this.stepperData) {
            if (items['type'] == 'financial') {
              items['is_completed'] = true
              return true
            }
          }
        }
        else if (errorOccurred) {
          //this.toastr.warning("Kindly Enter All Mandatory fields", 'Warning');
          return false
        }
      }
    }
   
    if (type == 'people') {
      for (let i = 0; i < this.stakeholders.length; i++) {

        if ((this.stakeholders[i].employee_name === null || this.stakeholders[i].employee_name === undefined || this.stakeholders[i].employee_name === '') && this.isMandate('name')) {
          // const customerEmptymsg = 'Responsible Is Mandatory';
          if (this.stakeholders[i].employee_name === '') {
            const peopleName_empty = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.peopleName_empty ? this.retrieveMessages[0].errors.peopleName_empty : 'People is mandatory' : 'People is mandatory';

            this.toasterService.showWarning(peopleName_empty, 10000)

          }
          else if (this.stakeholders[i].employee_name === null || this.stakeholders[i].employee_name === undefined) {
            const people_notMatch = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.people_notMatch ? this.retrieveMessages[0].errors.people_notMatch : 'Kindly Choose a Valid Employee name' : 'Kindly Choose a Valid Employee name';
            this.toasterService.showWarning(people_notMatch, 10000)


          }

          // this.toastr.error(customerEmptymsg, 'Error');
          this.people_name = false;
          errorOccurred = true;
          break;
        }
        else if ((this.stakeholders[i].start_date === null || this.stakeholders[i].start_date === undefined || this.stakeholders[i].start_date === '' || !this.checkInternalStakeholderDates(this.stakeholders[i].start_date, this.stakeholders[i].end_date, data.startDate, data.endDate)) && this.isMandate('isa_start_date')) {
          if (this.stakeholders[i].start_date === '') {
            const startDate_empty_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.startDate_empty_msg ? this.retrieveMessages[0].errors.startDate_empty_msg : 'Start date is Mandatory' : 'Start date is Mandatory';
            this.toasterService.showWarning(startDate_empty_msg, 10000)

          }
          else if (this.stakeholders[i].start_date === null) {
            const startDate_invalid_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.startDate_invalid_msg ? this.retrieveMessages[0].errors.startDate_invalid_msg : 'Kindly Choose/Enter start date in DD-MM-YYYY format' : 'Kindly Choose/Enter start date in DD-MM-YYYY format';
            this.toasterService.showWarning(startDate_invalid_msg, 10000)

          }
          else if(!this.checkInternalStakeholderDates(this.stakeholders[i].start_date, this.stakeholders[i].end_date, data.startDate, data.endDate))
          {
            const valid_date_err = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.valid_date_err ? this.retrieveMessages[0].errors.valid_date_err : 'Kindly enter valid date' : 'Kindly enter valid date';
            this.toasterService.showWarning(valid_date_err, 10000)
          }
          this.dateLogic = true;
          errorOccurred = true;
          break;
          // const customerEmptymsg = 'Responsible Is Mandatory';
          // this.toastr.error(customerEmptymsg, 'Error');
        }
        else if ((this.stakeholders[i].end_date === null || this.stakeholders[i].end_date === undefined || this.stakeholders[i].end_date === '' ||  !this.checkInternalStakeholderDates(this.stakeholders[i].start_date, this.stakeholders[i].end_date, data.startDate, data.endDate)) && (this.isMandate('isa_end_date'))) {
          // const customerEmptymsg = 'Responsible Is Mandatory';
          // this.toastr.error(customerEmptymsg, 'Error');
          if (this.stakeholders[i].end_date === '') {
            const endDate_empty_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.startDate_empty_msg ? this.retrieveMessages[0].errors.startDate_empty_msg : 'End date is Mandatory' : 'End date is Mandatory';
            this.toasterService.showWarning(endDate_empty_msg, 10000)

          }
          else if (this.stakeholders[i].end_date === null) {
            const endDate_invalid_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.endDate_invalid_msg ? this.retrieveMessages[0].errors.endDate_invalid_msg : 'Kindly Choose/Enter end date in DD-MM-YYYY format' : 'Kindly Choose/Enter end date in DD-MM-YYYY format';
            this.toasterService.showWarning(endDate_invalid_msg, 10000)

          }
          else if(!this.checkInternalStakeholderDates(this.stakeholders[i].start_date, this.stakeholders[i].end_date, data.startDate, data.endDate))
          {
            const dates_invalid_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.endDate_invalid_msg ? this.retrieveMessages[0].errors.endDate_invalid_msg : 'Kindly Enter Valid Start Date and End Date' : 'Kindly Enter Valid Start Date and End Date';
            this.toasterService.showWarning(dates_invalid_msg, 10000)
          }
          this.dateLogic = true;
          errorOccurred = true;
         break;


        }

        else if ((this.stakeholders[i].role === null || this.stakeholders[i].role === undefined || this.stakeholders[i].role === '') && this.isMandate('role')) {
          // const customerEmptymsg = 'Responsible Is Mandatory';
          // this.toastr.error(customerEmptymsg, 'Error');
          const role_empty = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.role_empty ? this.retrieveMessages[0].errors.role_empty : 'Role is mandatory' : 'Role is mandatory';

          this.toasterService.showWarning(role_empty, 10000)
          errorOccurred = true;
          break;
        }
        else if ((this.stakeholders[i].split === null || this.stakeholders[i].split === undefined || this.stakeholders[i].split === '') && this.isMandate('capacity')) {
          // const customerEmptymsg = 'Responsible Is Mandatory';
          // this.toastr.error(customerEmptymsg, 'Error');
          const splitPercent_empty = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.splitPercent_empty ? this.retrieveMessages[0].errors.splitPercent_empty : 'Allocation Percentage is mandatory' : 'Allocation Percentage is mandatory';

          this.toasterService.showWarning(splitPercent_empty, 10000)
          errorOccurred = true;
          break;
        }
        else if (this.stakeholders[i].split > 100 || this.stakeholders[i].split < this.split_percentage_lower_percentage) {
          if (this.stakeholders[i].split != '') {
            validateError = true
            break;
          }
        }

       
        if (!this.checkInternalStakeholderDates(this.stakeholders[i].start_date, this.stakeholders[i].end_date, data.startDate, data.endDate)) {
          this.dateLogic=false;
          errorOccurred = true;
          const enter_all_mandate = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.enter_all_mandate ? this.retrieveMessages[0].errors.enter_all_mandate : 'Enter all mandatory fields!' : 'Enter all mandatory fields!';
          
          this.toasterService.showWarning(enter_all_mandate, 10000)
          return;
        }

      }
      if (!errorOccurred && !validateError) {
        for (let items of this.stepperData) {
          if (items['type'] == 'people') {
            items['is_completed'] = true
            return true
          }
        }
      }
      // else if (!this.people_name) {
      //   const people_notMatch = this.retrieveMessages[0].errors.people_notMatch ? this.retrieveMessages[0].errors.people_notMatch ? this.retrieveMessages[0].errors.people_notMatch : 'Kindly Choose a Valid Employee name' : 'Kindly Choose a Valid Employee name';
      //   this.toastr.warning(people_notMatch, 'Warning');
      // }
      else if (!this.dateLogic) {
        const isa_dateLogic = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.isa_dateLogic ? this.retrieveMessages[0].errors.isa_dateLogic : 'Start Date cannot be greater than End date' : 'Start Date cannot be greater than End date';

        this.toasterService.showWarning(isa_dateLogic, 10000);
      }

      else if (validateError && !errorOccurred) {
        this.toasterService.showWarning("Allocation Percentage should be between " + this.split_percentage_lower_percentage + "-100", 10000);
      }
      else if (errorOccurred) {
        //this.toastr.warning("Kindly Enter All Mandatory fields", 'Warning');
        return false
      }
    }
    if (type == 'template') {
      console.log(this.selectedTemplate, this.buildFromScratch);
      if ((this.selectedTemplate !== '' && this.selectedTemplate !== null && this.selectedTemplate !== undefined) || this.buildFromScratch) {
        //errorOccurred = true;
        this.templateChosen = true
      }
      else {
        this.templateChosen = false;
      }
      console.log(this.templateChosen);
      if (this.templateChosen) {
        for (let items of this.stepperData) {
          if (items['type'] == 'template') {
            items['is_completed'] = true
            return true
          }
        }
        return true
      }
      else {
        this.toasterService.showWarning("Kindly choose any one to proceed further", 10000)
        return false
      }
    }
    if (type == 'advance') {
      return true
    }
    if (type == 'financial') {
      return true
    }
  }
  createLocation(): FormGroup {
    return this.formBuilder.group({
      location: [''],
      country_id: [''],
      calendar: ['']
    });
  }
  mandateAvailable(type) {
    let errorOccurred = false;
    if (type == 'details') {
      const details = _.where(this.stepperData, { type: type, is_active: true });

      if (details.length > 0) {
        if (this.isMandate('project_code')) {
          // const codeEmptymsg = 'Kindly enter Milestone name';
          // this.toastr.error(codeEmptymsg, 'Error');
          errorOccurred = true;
        }
        if (this.isMandate('project_name')) {
          // const codeEmptymsg = 'Kindly enter Milestone name';
          // this.toastr.error(codeEmptymsg, 'Error');
          errorOccurred = true;
        }
        if (this.isMandate('portfolio')) {
          // const customerEmptymsg = 'Responsible Is Mandatory';
          // this.toastr.error(customerEmptymsg, 'Error');
          errorOccurred = true;
        }
        if (this.isMandate('start_date')) {
          // const nameEmptymsg = 'Kindly choose When Is It due?';
          // this.toastr.error(nameEmptymsg, 'Error');
          errorOccurred = true;
        }
        if (this.isMandate('end_date')) {
          // const nameEmptymsg = 'Kindly choose When Is It due?';
          // this.toastr.error(nameEmptymsg, 'Error');
          errorOccurred = true;
        }
        if (this.isMandate('project_type')) {
          // const nameEmptymsg = 'Kindly choose When Is It due?';
          // this.toastr.error(nameEmptymsg, 'Error');
          errorOccurred = true;
        }
        if (this.isMandate('service_type')) {
          // const nameEmptymsg = 'Kindly choose When Is It due?';
          // this.toastr.error(nameEmptymsg, 'Error');
          errorOccurred = true;
        }
        if (this.isMandate('project_description')) {
          // const codeEmptymsg = 'Kindly enter Milestone name';
          // this.toastr.error(codeEmptymsg, 'Error');
          errorOccurred = true;
        }



      }
    }

    if (type == 'es') {
      const es = _.where(this.stepperData, { type: type, is_active: true });
      if (es.length > 0) {
        if (this.isMandate('entity')) {
          // const customerEmptymsg = 'Responsible Is Mandatory';
          // this.toastr.error(customerEmptymsg, 'Error');
          errorOccurred = true;
        }
        if (this.isMandate('division')) {
          // const customerEmptymsg = 'Responsible Is Mandatory';
          // this.toastr.error(customerEmptymsg, 'Error');
          errorOccurred = true;
        }
        if (this.isMandate('sub_division')) {
          // const customerEmptymsg = 'Responsible Is Mandatory';
          // this.toastr.error(customerEmptymsg, 'Error');
          errorOccurred = true;
        }
        if (this.isMandate('p_and_l')) {
          // const customerEmptymsg = 'Responsible Is Mandatory';
          // this.toastr.error(customerEmptymsg, 'Error');
          errorOccurred = true;
        }

      }
    }

    if (type == 'financial') {
      const financial = _.where(this.stepperData, { type: type, is_active: true });
      if (financial.length > 0) {
        if (this.isMandate('opportunity') && this.MakeFieldsNonMandatory) {
          // const customerEmptymsg = 'Responsible Is Mandatory';
          // this.toastr.error(customerEmptymsg, 'Error');
          errorOccurred = true;
        }
        if (this.isMandate('invoice_template')&& this.MakeFieldsNonMandatory) {
          // const customerEmptymsg = 'Responsible Is Mandatory';
          // this.toastr.error(customerEmptymsg, 'Error');
          errorOccurred = true;
        }
        if (this.isMandate('currency')&& this.MakeFieldsNonMandatory) {
          // const customerEmptymsg = 'Responsible Is Mandatory';
          // this.toastr.error(customerEmptymsg, 'Error');
          errorOccurred = true;
        }
        if (this.isMandate('quote')&& this.MakeFieldsNonMandatory) {
          // const codeEmptymsg = 'Kindly enter Milestone name';
          // this.toastr.error(codeEmptymsg, 'Error');
          errorOccurred = true;
        }
        if (this.isMandate('po_number')&& this.MakeFieldsNonMandatory) {
          // const codeEmptymsg = 'Kindly enter Milestone name';
          // this.toastr.error(codeEmptymsg, 'Error');
          errorOccurred = true;
        }
        if (this.isMandate('po_value')&& this.MakeFieldsNonMandatory) {
          // const codeEmptymsg = 'Kindly enter Milestone name';
          // this.toastr.error(codeEmptymsg, 'Error');
          errorOccurred = true;
        }

      }
    }

    if (type == 'schedule') {
      const schedule = _.where(this.stepperData, { type: type, is_active: true });
      if (schedule.length > 0) {
        if (this.isMandate('week')) {
          errorOccurred = true;
        }
        if (this.isMandate('work_location')) {
          // const customerEmptymsg = 'Responsible Is Mandatory';
          // this.toastr.error(customerEmptymsg, 'Error');
          errorOccurred = true;
        }
        if (this.isMandate('holiday_calendar')) {
          // const customerEmptymsg = 'Responsible Is Mandatory';
          // this.toastr.error(customerEmptymsg, 'Error');
          errorOccurred = true;
        }
        if (this.isMandate('location')) {
          // const codeEmptymsg = 'Kindly enter Milestone name';
          // this.toastr.error(codeEmptymsg, 'Error');
          errorOccurred = true;
        }
        if (this.isMandate('from')) {
          // const codeEmptymsg = 'Kindly enter Milestone name';
          // this.toastr.error(codeEmptymsg, 'Error');
          errorOccurred = true;
        }
        if (this.isMandate('to')) {
          // const codeEmptymsg = 'Kindly enter Milestone name';
          // this.toastr.error(codeEmptymsg, 'Error');
          errorOccurred = true;
        }
      }
    }

    if (type == 'people') {
      const people = _.where(this.stepperData, { type: type, is_active: true });
      if (people.length > 0) {

        if (this.isMandate('name')) {
          // const customerEmptymsg = 'Responsible Is Mandatory';
          // this.toastr.error(customerEmptymsg, 'Error');
          errorOccurred = true;
        }
        if (this.isMandate('isa_start_date')) {
          // const customerEmptymsg = 'Responsible Is Mandatory';
          // this.toastr.error(customerEmptymsg, 'Error');
          errorOccurred = true;
        }
        if (this.isMandate('isa_end_date')) {
          // const customerEmptymsg = 'Responsible Is Mandatory';
          // this.toastr.error(customerEmptymsg, 'Error');
          errorOccurred = true;
        }
        if (this.isMandate('role')) {
          // const customerEmptymsg = 'Responsible Is Mandatory';
          // this.toastr.error(customerEmptymsg, 'Error');
          errorOccurred = true;
        }
        if (this.isMandate('capacity')) {
          // const customerEmptymsg = 'Responsible Is Mandatory';
          // this.toastr.error(customerEmptymsg, 'Error');
          errorOccurred = true;
        }
      }
    }
    if (errorOccurred) {
      return false; // Return false if any error occurred
    } else {
      return true; // Return true if no errors occurred
    }
  }
  lastStepper(type) {

    const details = _.where(this.stepperData, { type: type, is_active: true, is_last_step: true })
    if (details.length > 0) {
      return true
    }
    else {
      return false
    }
  }
  onInputSplit(event: Event, i) {

    const inputValue = (event.target as HTMLInputElement).valueAsNumber;

    if (inputValue > 100) {
      this.stakeholders[i].split = 100;
    }
    else if (inputValue < 1) {
      this.stakeholders[i].split = 1
    }
    else {
      this.stakeholders[i].split = inputValue;
    }

  }
  getTemplateFromScratch() {
    this.buildFromScratch = true;
    //this.useATemplate=!this.useATemplate
    this.selectedTemplate = '';
    for (let i = 0; i < this.template_master_list.length; i++) {
      this.template_master_list[i].border_color = '1px solid white'
      this.template_master_list[i].color = 'white'
    }
    this.useATemplate = false;
    this.opentemplate = false;
    this.template_master_list = _.where(this.pmMasterService.template_master_list, { service_type: this.selectedOption, gantt_type: this.gantt_type_id });
    this.template_master_list_search = _.where(this.pmMasterService.template_master_list, { service_type: this.selectedOption, gantt_type: this.gantt_type_id });
    this.createJobForm.get('textInputControl').patchValue('');
    this.createJobForm.patchValue({
      'adaptTemplate':''
    })

  }
  getTemplate() {
    this.useATemplate = true;
    this.template_master_list = _.where(this.pmMasterService.template_master_list, { service_type: this.selectedOption, gantt_type: this.gantt_type_id });
    this.template_master_list_search = _.where(this.pmMasterService.template_master_list, { service_type: this.selectedOption, gantt_type: this.gantt_type_id });
    //this.buildFromScratch=!this.buildFromScratch;
    this.buildFromScratch = false;
    if (this.useATemplate == true) {
      this.opentemplate = true;
    }
    // if (this.opentemplate == false) {
    //   this.opentemplate = false;
    // }
    this.createJobForm.get('textInputControl').patchValue('');
  }
  // clearTemplateSelection(){
  //   this.useATemplate = false;
  //   this.buildFromScratch = false;
  //   this.opentemplate = false;
  // }
  async getListValueFilter(name, id) {
    let entityChoosen = _.uniq(_.pluck(this.Filterdata, id))
    let val_result = []
    for (let entit of entityChoosen) {
      let result = _.where(this.Filterdata, { [id]: entit })
      if (result.length > 0) {
        val_result.push({
          "id": result[0][id],
          "name": result[0][name]
        })
      }
    }
    console.log(name, entityChoosen, val_result)
    return val_result
  }
  getTemplateSelected(id: any) {
    this.opentemplate = false;
    console.log('templateid', id)
    this.selectedTemplate = id ? id : '';
    const rowIndex = this.template_master_list.findIndex(row => row.id === this.selectedTemplate);
    this.selectedTemplateName = this.template_master_list[rowIndex].template_name;
    console.log(this.selectedTemplateName);
    this.createJobForm.patchValue({ ['adaptTemplate']: this.selectedTemplate })
    this.displayTemplate = false
    for (let i = 0; i < this.template_master_list.length; i++) {
      for (let i = 0; i < this.template_master_list.length; i++) {
        if (this.template_master_list[i].id == id) {
          this.template_master_list[i].border_color = '1px solid #E8E9EE'
          this.template_master_list[i].color = '#E8E9EE'
        }
        else {
          this.template_master_list[i].border_color = '1px solid white'
          this.template_master_list[i].color = 'white'
        }
      }
    }
    console.log(this.template_master_list)
    this.displayTemplate = true
    for (let i = 0; i < this.template_master_list_search.length; i++) {
      if (this.template_master_list_search[i].id == id) {
        this.template_master_list_search[i].border_color = '1px solid #E8E9EE'
        this.template_master_list_search[i].color = '#E8E9EE'
      }
      else {
        this.template_master_list_search[i].border_color = '1px solid white'
        this.template_master_list_search[i].color = 'white'

      }
    }
  }

  unSelectTemplate(){
    this.selectedTemplate=''
    this.createJobForm.patchValue({ ['adaptTemplate']: '' })
  }
  compareDate(date1: any, date2: any) {

    if ((date1 == "" || date1 == null || date1 == undefined) && (date2 == "" || date2 == null || date2 == undefined)) {
      return ''
    }
    else if (date1 == "" || date1 == null || date1 == undefined) {
      return date2
    }
    else if (date2 == "" || date2 == null || date2 == undefined) {
      return date1
    }
    else {
      date1 = moment(date1).format('YYYY-MM-DD')
      date2 = moment(date2).format('YYYY-MM-DD')
      if (date1 < date2)
        return date1
      else
        return date2
    }
  }
  compareDateMinimum(date1: any, date2: any) {

    if ((date1 == "" || date1 == null || date1 == undefined) && (date2 == "" || date2 == null || date2 == undefined)) {
      return ''
    }
    else if (date1 == "" || date1 == null || date1 == undefined) {
      return date2
    }
    else if (date2 == "" || date2 == null || date2 == undefined) {
      return date1
    }
    else {
      date1 = moment(date1).format('YYYY-MM-DD')
      date2 = moment(date2).format('YYYY-MM-DD')
      if (date1 > date2)
        return date1
      else
        return date2
    }
  }
  onInputQuote(event: Event) {

    const inputValue = (event.target as HTMLInputElement).valueAsNumber;

    if (inputValue < 1) {
      this.createJobForm.patchValue({ ['quote']: 1 })
    }
    else {
      this.createJobForm.patchValue({ ['quote']: inputValue })
    }

  }
 
  
  onInputPovalue(event: Event) {

    const inputValue = (event.target as HTMLInputElement).valueAsNumber;

    if (inputValue < 1) {
      this.createJobForm.patchValue({ ['po_value']: 1 })
    }
   
    else {
      this.createJobForm.patchValue({ ['po_value']: inputValue })
    }

  }
  async getAid(id: any, i: any) {
    // const inputValue = (event.target as HTMLInputElement).valueAsNumber;
    // console.log(inputValue)
    console.log(id)
    console.log(i)
    // console.log(this.stakeholders.length)
    // console.log(this.stakeholders[id])
    // const name = this.stakeholders[id];
    // console.log(name.employee_name);
    // console.log(this.stakeholders)
    // console.log(this.stakeholders[id].employee_name)
    // for(let i=0;i<this.stakeholders.length;i++){
    //   if(i==id){
    //     console.log(this.stakeholders[i].employee_name)
    //   }
    // }
    await this.ProjectCustomCreationService.getDOJ(id).then((res) => {
      if (res['messType'] == 'S') {
        this.DOJ = res['data'][0].date_of_joining
        this.people_name = true;
        console.log(this.DOJ, res);
      }
      else {
        this.people_name = false;
        console.log(res)
      }
    })
  }
  quoteKeyUp(event) {
    console.log(event.target.value.toString().length)
    console.log(event.target.value.toString())
    if (this.createJobForm.get('quote').value < 0) {
      const quote_negative = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.quote_negative ? this.retrieveMessages[0].errors.quote_negative : 'Quote value cannot be negative' : 'Quote value cannot be negative';
          
      this.toasterService.showWarning(quote_negative, 10000)
      this.createJobForm.patchValue({ ['quote']: '' })
    }
  }
  poValueKeyUp(event) {
    console.log(event.target.value.toString().length)
    console.log(event.target.value.toString())
    if (this.createJobForm.get('po_value').value < 0 && this.createJobForm.get('po_value').value != '' && this.createJobForm.get('po_value').value != null) {
      //this.toastr.warning('po value cannot be negative', 'Warning')
      this.createJobForm.patchValue({ ['po_value']: '' })
    }
  }
  capacityKeyUp(event, i: number) {
    const inputValue = event.target.value.toString();

    // Check if the input is a valid number
    if (!/^\d+$/.test(inputValue)) {
      // If not a valid number, you can handle it accordingly (e.g., ignore or display a message)
      console.log('Invalid input. Please enter a number.');
      return;
    }

    // Convert the input value to a number
    const inputNumber = parseInt(inputValue, 10);

    // Check the range and handle accordingly
    if (inputNumber > 100) {
      const splitPercent_exceed = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.splitPercent_exceed ? this.retrieveMessages[0].errors.splitPercent_exceed : 'Utilization Percentage should be between 1 to 100!' : 'Utilization Percentage should be between 1 to 100!';
          
      this.toasterService.showWarning(splitPercent_exceed, 10000)
      this.stakeholders[i].split = 100;
    } else if (inputNumber < 1) {
      const splitPercent_exceed = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.splitPercent_exceed ? this.retrieveMessages[0].errors.splitPercent_exceed : 'Utilization Percentage should be between 1 to 100!' : 'Utilization Percentage should be between 1 to 100!';
          
      this.toasterService.showWarning(splitPercent_exceed, 10000)
      this.stakeholders[i].split = '';
    }
  }
  checkDates(start: any, end: any) {
    console.log("date validation entry")
    const startDate = moment(start);
    const endDate = moment(end);
    console.log(start, end)
    if (startDate.isValid() && endDate.isValid()) {
      if (startDate.isBefore(endDate) || startDate.isSame(endDate)) {
        this.dateLogic = true;
      }
      else {
        this.dateLogic = false;
      }
      console.log(this.dateLogic, 'valid')
    } else {
      this.dateLogic = false;
      //this.toastr.warning("Invalid Date Format", 'Warning');
      console.error('Invalid date format', this.dateLogic);
    }
  }
  initializeStepper() {
    this.stepperData[0].is_selected = true;
    this.stepperData[0].is_crossed = false;
    this.stepperData[0].is_completed = false;
    this.stepperData[0].mandate_completed = false;
    for (let i = 1; i < this.stepperData.length; i++) {
      this.stepperData[i].is_selected = false;
      this.stepperData[i].is_crossed = false;
      this.stepperData[i].mandate_completed = false;
    }
    this.stepperData = [];
    this.stepperData = _.filter(this.stepperData, { 'is_active': true });
  }
  checkStandard() {
    // if(this.standard==false){
    //   this.standard=true
    //   this.risk=false
    //   this.poNonMandate = false;
    // }
    this.toggleSecuredChecked = false
    this.standard = true
    this.risk = false
    this.poNonMandate = false;
  }
  checkAtRisk() {
    // if(this.risk==false){
    //   this.standard=false
    //   this.risk=true
    // }
    this.toggleSecuredChecked = true;
    this.standard = false
    this.risk = true
    this.poNonMandate = true;
  }
  addFinancial(): void {
    this.createJobForm.value.financial = this.createJobForm.get(
      'financial'
    ) as FormArray;
    this.createJobForm.value.financial.push(
      this.createAcceptanceCriteria()
    );
    console.log('new', this.createJobForm.value);
  }
  createAcceptanceCriteria(): FormGroup {
    return this.formBuilder.group({
      opportunity_id: '',
      quote_id: '',
      currency: '',
      invoice_template: '',
      po_number: '',
      purchase_order: '',
      opportunity_status_name:'',
      opportunity_status_color:'',
      reason: 1,
      po_reference: '',
      partner: '',
      po_date: '',
      payment_terms: '',
      is_active: 1
    });
  }
  removeFinancial(i: any) {
    console.log(i)
    if ((this.createJobForm.get('financial') as FormArray).length > 1) {
      // this.removedCriteriaRecord.push(this.contactForm.value.acceptance_criteria_arr[i].id);
      // console.log("removed criteria", this.removedCriteriaRecord);
      (this.createJobForm.get('financial') as FormArray).removeAt(i);
    }
  }
 

  checkSecuredOrRisk() {
    console.log('toggle', this.toggleSecuredChecked);
    if (this.toggleSecuredChecked) {
      this.checkStandard();
    }
    else if (!this.toggleSecuredChecked) {
      this.checkAtRisk();
    }
  }
  checkOpportunity() {
    console.log('toggle', this.toggleOpportunityChecked);
    if (this.toggleOpportunityChecked) {
      this.checkWithOpportunity();
    }
    else if (!this.toggleOpportunityChecked) {
      this.checkWithoutOpportunity();
    }

  }
  checkWithoutOpportunity() {
    // if(this.withoutOpportunity==false){
    //   this.withoutOpportunity=true
    //   this.withOpportunity=false
    // }
    this.toggleOpportunityChecked = true;
    this.withoutOpportunity = true;
    this.withOpportunity = false;
    console.log('without', this.withoutOpportunity, this.withOpportunity);
  }
  checkWithOpportunity() {
    // if(this.withOpportunity==false){
    //   this.withoutOpportunity=false
    //   this.withOpportunity=true
    // }
    this.toggleOpportunityChecked = false;
    this.withOpportunity = true;
    this.withoutOpportunity = false;
    console.log('with', this.withOpportunity, this.withoutOpportunity);
  }
  sendMailNotification(index: number) {
    const stakeholdersArray = this.createJobForm.get('external_stakeholders') as FormArray;
    const mailSentControl = stakeholdersArray.at(index).get('mail_sent');
    
    // Toggle mail_sent value
    mailSentControl.setValue(!mailSentControl.value);
  }
  

  intializeProjectCode() {


    let generated_code = _.where(this.formConfig, { field_name: "project_generation_code", type: "project-creation", is_active: true })

    if (generated_code.length > 0) {
      if (generated_code[0]['label'] == "general") {
        const res = _.where(this.project_code_list, { service_type_id: 0 })

        this.createJobForm.patchValue({ [`project_code`]: res.length > 0 ? res[0].projectCode : '' || '' })
      }
    }
  }
selectCheckBox(event:Event){
  console.log(this.TandM_selected)
  // const BValue = (event.target as HTMLInputElement).value;
  // this.TandM_selected=BValue
}

  getPONumberDisabledCheck() {
    if (this.risk) {
      this.createJobForm.patchValue({ 'po_number': '' })

      let financialArray: any = this.createJobForm.get("financial") as FormArray;

      console.log(financialArray)

      for (let i = 0; i < financialArray.length; i++) {
        financialArray.controls[i].get('po_number').setValue('');
      }

    }
    return this.risk ? true : false
  }

  getDisabledColor() {
    return (this.risk || this.withOpportunity) ? '#E8E9EE' : '#FFFFFF'
  }
  getOppCreation() {
    return (this.opportunityCreation) ? '#E8E9EE' : '#FFFFFF'
  }

  checkInternalStakeholderDates(isa_start_date, isa_end_date, start_date, end_date) {
      isa_start_date = moment(isa_start_date).format("YYYY-MM-DD")
      isa_end_date = moment(isa_end_date).format("YYYY-MM-DD")
      start_date = moment(start_date).format("YYYY-MM-DD")
      end_date = moment(end_date).format("YYYY-MM-DD")
      if(!(moment(isa_start_date).isSameOrAfter(start_date) && moment(end_date).isSameOrAfter(isa_start_date)))
      {
          console.log("Start_Date 1")
          this.dateLogic =false;
          return false;
      }
      if(!(moment(isa_end_date).isSameOrAfter(start_date) && moment(end_date).isSameOrAfter(isa_start_date)))
      { 
          console.log("Start Date 2")
          this.dateLogic =false;
          return false;
      }
      if(moment(isa_start_date).isAfter(isa_end_date))
      {
          this.dateLogic =false;
          return false;
      }

      return true;

  }

  updateDuplicateProjectCreation(){
    this.createJobForm.patchValue({
      service_type_id: this.duplicate_data.service_type_id,
      legal_entity: this.duplicate_data.legal_entity,
      endDate: this.duplicate_data.planned_end_date,
      startDate: this.duplicate_data.planned_start_date,
      portfolio: this.duplicate_data.project_id,
      project_name: this.duplicate_data.item_name,
      p_and_l: this.duplicate_data.p_and_l_id,
      

    })

    this.selectOption(this.duplicate_data.service_type_id)
  }

  calculateTimeDifference(start_time, end_time)
  {
      console.log(start_time, end_time)
      if(start_time && end_time)
      {
        start_time = start_time.split(":");
        end_time = end_time.split(":");
        let startDate = new Date(0, 0, 0, start_time[0], start_time[1], 0);
        let endDate = new Date(0, 0, 0, end_time[0], end_time[1], 0);
        let diff = endDate.getTime() - startDate.getTime();
        let hours = Math.floor(diff / 1000 / 60 / 60);
        console.log(start_time, end_time, hours)
        this.createJobForm.patchValue({['daily_working_horus']: hours})
      }
  }

  whetherCustomerGeneratedCode(){
    let customer_generated_code = _.where(this.formConfig, { field_name: "project_generation_code", type: "project-creation", label:"customer", is_active: true })

    if(customer_generated_code.length>0)
    {
        return true;
    }
    else
    {
        return false;
    }
  }

  async generateNewProjectCode(){
    this.refresh = true;

    let code = _.where(this.formConfig, { field_name: "project_generation_code", type: "project-creation",  is_active: true })

    if(code.length>0)
    {
        if(code[0]['label']=="customer")
        {
          await this.ProjectCustomCreationService.getCustomerProjectCode(this.customer_id).then((res)=>{
            if(res['messType']=="S")
            {
                this.createJobForm.patchValue({"project_code": res['data']})
            }
          })
        }
        else
        {
          await this.ProjectCustomCreationService.getProjectCode().then((res: any) => {
            if (res['messType'] == 'S') {
              this.project_code_list = res['data']

            }
          })


          if(code[0]['label']=="general")
          {
              this.intializeProjectCode()
          }
          else if(code[0]['label']=="service_type")
          {
              const res = _.where(this.project_code_list, { service_type_id: this.selectedOption })

              this.createJobForm.patchValue({ [`project_code`]: res.length > 0 ? res[0].projectCode : '' || '' })
              
              
          }
        }
    }

    this.refresh = false
  }

   /**
   * @description Pre Patching ProjectForm Values
   */
   async prePatchProjectFormValues() {
 
    if (this.opportunityDetails) {

      console.log(this.currency_list)
      let currency_value
      if (this.opportunityDetails.quote_value.length > 0) {
        this.currencyList = this.opportunityDetails.quote_value;

        currency_value = this.currencyList.find(item => item.currency_code === this.opportunityDetails.currency);

      }
      const currencyName = this.opportunityDetails.currency; // This should hold the name

      const matchedCurrency = this.currency_list.find(currency => currency.name == currencyName);
      console.log(currency_value)
      if (currency_value !== undefined) {
        this.patchOpportunityIdInFinancial(this.opportunityDetails,currency_value);
          this.createJobForm.get('currency').patchValue(matchedCurrency.id);
        
      } else {
        this.createJobForm.get('currency').patchValue('');
      }
      this.createJobForm.get('project_name').patchValue(this.opportunityDetails.opportunity_name);
      this.createJobForm.get('customer_id').patchValue(this.opportunityDetails.customer_id);
      let projectCode = await this.getProjectCode(this.opportunityDetails.customer_id);
      this.createJobForm.get('project_code').patchValue(projectCode);
      this.portfolio_list = await this.getPortfolioList(this.opportunityDetails.customer_id);
      if(this.portfolio_list.length==1){
        this.createJobForm.get('portfolio').patchValue(this.portfolio_list[0].id)
      }
      
      this.createJobForm.get('legal_entity').patchValue(this.opportunityDetails.legal_entity);  
      this.createJobForm.get('startDate').patchValue(this.opportunityDetails.deliveryStartDate);
      this.createJobForm.get('endDate').patchValue(this.opportunityDetails.deliveryFinishDate);
      this.createJobForm.get('project_name').patchValue(this.opportunityDetails.opportunity_name);
      this.createJobForm.get('p_and_l').patchValue(this.opportunityDetails.sales_region);
      this.selectedOption=this.opportunityDetails.service_type
  
     if (this.createJobForm.get('project_code').value.length > 0) {
      console.log(projectCode)
      if (projectCode) {
        // this.createJobForm.patchValue({ ['profit_center']: res })
        this.empty = false
        this.refresh = true
        if(this.type === "project-creation"){
        await this.ProjectCustomCreationService.checkProjectCodeDuplication(projectCode).then((res: any) => {
          if (res) {
            this.codeDuplicated = res['data'];
            if (this.codeDuplicated == true) {
         
            }
            else {
         
            }
            this.refresh = false;
            if (res == '' && res == null) {
              this.refresh = true;
            }
          }
        })}
      }
    }
    else {
      this.empty = true
 
    }
    }
    else {
      this.createJobForm.get('quote_id').patchValue("");
      this.createJobForm.get('project_name').patchValue("");
      this.createJobForm.get('purchase').patchValue("");
      this.createJobForm.get('currency').patchValue("");
      this.createJobForm.get('customer_id').patchValue("");
      this.createJobForm.get('project_code').patchValue("");
      this.createJobForm.get('legal_entity').patchValue("");
      this.createJobForm.get('po_number').patchValue("");
      this.createJobForm.get('po_date').patchValue("");
    }

  }
  
  /**
   * @description get project opportunity data
   */
  getProjectOpportunityData(): Promise<any> {
    
    return new Promise((resolve) => {
      this.subs.sink = this.ProjectCustomCreationService.getProjectOpportunityDetails(parseInt(this.opportunityId)).subscribe(
        (res: any) => {
          if (res.messType == 'S') {
            resolve(res.data);
          } else {
            resolve(null)
          }
        },
        (err) => {
          console.log(err);
          resolve(null)
        }
      );
    });
  }

  /**
   * @description get project Code
   */
  getProjectCode(customer_id): Promise<any> {

    return new Promise((resolve) => {
      this.subs.sink = this.ProjectCustomCreationService.getProjectCodeForCustomer(customer_id).subscribe(
        (res: any) => {
          if (res.messType == 'S') {
            resolve(res.data);
          } else {
            resolve(null)
          }
        },
        (err) => {
          console.log(err);
          resolve(null)
        }
      );
    });
  }

  /**
   * @description get portfolio List
   */
  getPortfolioList(customer_id): Promise<any> {

    return new Promise((resolve) => {
      this.subs.sink = this.ProjectCustomCreationService.getCustomerPortfolioList(customer_id).subscribe(
        (res: any) => {
          if (res.messType == 'S') {
            resolve(res.data);
          } else {
            resolve(null)
          }
        },
        (err) => {
          console.log(err);
          resolve(null)
        }
      );
    });
  }

  /**
   * @description get Child Customer List
   */
  getChildCustomerList(customer_id): Promise<any> {

    return new Promise((resolve) => {
      this.subs.sink = this.ProjectCustomCreationService.getProjectChildCustomerList(customer_id).subscribe(
        (res: any) => {
          if (res.messType == 'S') {
            resolve(res.data);
          } else {
            resolve(null)
          }
        },
        (err) => {
          console.log(err);
          resolve(null)
        }
      );
    });
  }
  patchOpportunityIdInFinancial(opportunityDetails: any, currency_value: any): void {
    const financialArray = this.createJobForm.get('financial') as FormArray;
  
    // Loop through each FormGroup inside the FormArray
    for (let i = 0; i < financialArray.length; i++) {
      const financialGroup = financialArray.at(i);  // Get the FormGroup for the current index
  
      // Patch opportunity_id if available
      const opportunityControl = financialGroup.get('opportunity_id');
      if (opportunityControl) {
        opportunityControl.patchValue(opportunityDetails.opportunity_id);
      }
  
      // Patch the status_name and status_color if available
      const statusNameControl = financialGroup.get('opportunity_status_name');
      if (statusNameControl) {
        statusNameControl.patchValue(opportunityDetails.status_name);
      }
  
      const statusColorControl = financialGroup.get('opportunity_status_color');
      if (statusColorControl) {
        statusColorControl.patchValue(opportunityDetails.status_color);
      }
  
      // Patch other fields if available
      const purchaseOrderControl = financialGroup.get('purchase_order');
      if (purchaseOrderControl) {
        purchaseOrderControl.patchValue(currency_value.value);  // Assuming 'currency_value.value' holds purchase order amount
      }
  
      const poDateControl = financialGroup.get('po_date');
      if (poDateControl) {
        poDateControl.patchValue(opportunityDetails.po_date);
      }
  
      const poNumberControl = financialGroup.get('po_number');
      if (poNumberControl) {
        poNumberControl.patchValue(opportunityDetails.po_number);
      }
  
      const quoteIdControl = financialGroup.get('quote_id');
      if (quoteIdControl) {
        quoteIdControl.patchValue(opportunityDetails.quote_id);
      }
  
      const paymentTermsControl = financialGroup.get('payment_terms');
      if (paymentTermsControl) {
        paymentTermsControl.patchValue(opportunityDetails.payment_terms);
      }
    }
  }
  
  
 
  getDisabledStatusColor(field_name, status_id) {
    let disabledConfig = _.where(this.formConfig, { type: 'project-creation', field_name: field_name, is_active: true })

    if (disabledConfig.length > 0) {
      if (_.contains(disabledConfig[0]['disabled_status'], status_id)) {
        return "#E8E9EE";
      }
      else {
        return "#FFFFFF";
      }
    }
    else {
      return "#FFFFFF";
    }
  }
  goBack(){

    this.onCloseClick()
  }
  
  prefillTimeData(){
    let shiftValue = this.createJobForm.get('shift').value;
    let shiftData = this.shiftMasterList.find(shift => shift.id == shiftValue);

    if(shiftData && this.shiftBasedDefault){
      this.createJobForm.patchValue({ ['from']: shiftData.from_time ? shiftData.from_time : '' })
      this.createJobForm.patchValue({ ['to']: shiftData.to_time ? shiftData.to_time : '' })
      this.createJobForm.patchValue({ ['monthly_hours']: shiftData.monthly_working_hours ? shiftData.monthly_working_hours : 168 })
      this.createJobForm.patchValue({ ['daily_working_hours']: shiftData.daily_working_hours ? shiftData.daily_working_hours : 8 })
    }
  }


}
export function customAutocompleteScrollStrategyFactory(overlay: Overlay): () => ScrollStrategy {
  return () => overlay.scrollStrategies.reposition();
}
export interface DialogData {
  mode: any;
  data: any;
}
