<div class="pm-details">
  <div class="row title pt-2 pl-3 pb-2" >
    <div class="icon" (click) = "navigateToLandingPage()">
      <svg class="svg-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
        <g opacity="0.4" clip-path="url(#clip0_6114_39061)">
          <path d="M6.81795 8L10 11.1113L9.09103 12L5 8L9.09103 4L10 4.88875L6.81795 8Z" fill="#111434"/>
        </g>
        <defs>
          <clipPath id="clip0_6114_39061">
            <rect width="16" height="16" fill="white"/>
          </clipPath>
        </defs>
      </svg>
    </div>
    <span class="pl-2 pr-2 my-auto item-name" *ngIf="this.pmNameService.projectName"  tooltip="{{this.pmNameService.projectName}}" placement="top" showDelay="500">{{ pmNameService.projectName | maxellipsis: 100 }}</span>
  </div>
  <mat-card class="row tab-card">
    <nav mat-tab-nav-bar>
      <div *ngFor="let link of tabLinks">
        <a
          mat-tab-link
          [routerLink]="link.path"
          routerLinkActive
          #rla="routerLinkActive"
          [active]="rla.isActive"
          *ngIf="link.toDisplay"
          [disabled]="rla.isActive"
          (click)="onTabClick($event,link)"
        >
          {{ link.label }}
        </a>
      </div>
    </nav>
    <div class="project-tab-content">
      <router-outlet></router-outlet>
    </div>
  </mat-card>
</div>  