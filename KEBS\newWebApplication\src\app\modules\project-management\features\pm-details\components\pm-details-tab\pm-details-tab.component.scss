.pm-details{

  background-color: #f0f2f7;
    .title{
        display:flex;
        color: rgba(0, 0, 0, 0.87);
    }
    .back{
        border: 1px solid lightgray;
        border-radius: 5px;
        cursor: pointer;
        margin-left: 15px;
        margin-bottom: 10px;
        color: lightgray;
    }
    .item-name{
      width: max-content;
      cursor: pointer;
      color: #1b2140;
      background: white;
      padding: 4px;
      border-top: 1px solid #D4D6D8;
      border-bottom: 1px solid #D4D6D8;
      border-right: 1px solid #D4D6D8;
      border-radius: 0px 4px 4px 0px;
    }
    a:hover {
        color: unset !important;
        text-decoration: none !important;
    }
    a.mat-tab-link.mat-focus-indicator.ng-star-inserted.mat-tab-label-active {
      color: var(--detailsTabColor) !important;
      font-family: var(--detailsTabFont) !important;
    }
    
    .tab-card{
        margin-left: 15px !important;
        margin-right: 15px !important;
        padding: 0%;
    } 
    .mat-tab-links{
      display: flex;
      justify-content: flex-start;
      background-color: #F7F9FB;
      border-bottom: 1px solid #D4D6D8;
    }

    nav {
        width: 100%;
        z-index: 14;
        background: #f8f9fa;
        margin-left: 0px !important;
    }
    .project-tab-content {
        padding-bottom: 5px;
        padding-left: 5px;
        height: var(--innerDetailsTabHeight) !important;
        font-family: var(--detailsTabFont) !important;
        overflow: hidden;
    }

    a.mat-tab-link {
        min-width: 0px !important;
        color: black;
        font-weight: 500;
        font-family: var(--detailsTabFont) !important;
        font-size: 12px !important;
        padding: 0 10px !important;
        opacity: unset;
    }
    .mat-drawer-content{
        overflow: hidden;
    }

    .title{
      font-family: var(--detailsTabFont) !important;
      font-size: 14px;
      font-style: normal;
      font-weight: 500;
      line-height: 20px;
      letter-spacing: .32px;
      text-transform: capitalize;
    }


    .icon{
      display: flex;
      padding: 4px;
      align-items: center;
      gap: 4px;
      cursor: pointer;
      background-color: white;
      border-top: 1px solid #D4D6D8;
      border-bottom: 1px solid #D4D6D8;
      border-left: 1px solid #D4D6D8;
      border-radius: 4px 0px 0px 4px;
      .svg-icon {
        border: 1px solid #D4D6D8;
        border-radius: 5px;
      }
      .svg-icon:hover {
        background-color: var(--blue-grey-60, #f1f4f7);
        transform: scale(1.05);
      }
    }

    .icon:hover{
        background-color: var(--blue-grey-60, #f1f4f7);
        transform: scale(1.05);
        

    }

    @keyframes pulse {
        0% {
          box-shadow: 0 0 0 0 rgba(0, 0, 0, 0.1);
        }
        70% {
          box-shadow: 0 0 0 15px rgba(0, 0, 0, 0);
        }
        100% {
          box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
        }
      }

    .icon:active{
        animation: pulse 0.5s;
    }
      .tab-card {
        flex-grow: 1; /* Allow the mat-card to grow and fill the available height */
      }

      ::ng-deep .mat-tab-nav-bar.mat-primary .mat-ink-bar {
        background-color: var(--detailsTabColor) !important;
      }
      
    ::ng-deep .project-tab-content::-webkit-scrollbar-thumb{
      background-color: var(--project2Scroll) !important;
    }
        
      
}
::ng-deep .pm-details {
    // height: var(--detailsHeight) !important; /* Set a fixed height for the container */
    // overflow-y: hidden; /* Add vertical scrollbar if content overflows */
    ::-webkit-scrollbar-thumb{
      background-color: var(--project2Scroll) !important;
    }
  }
  

  
