import { Component, HostListener, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from "@angular/router";
import { takeUntil } from 'rxjs/operators';
import { Subject } from 'rxjs';
import { PmAuthorizationService } from 'src/app/modules/project-management/services/pm-authorization.service';
import { PmMasterService } from 'src/app/modules/project-management/services/pm-master.service';
import * as _ from 'underscore';
import { ProjectNameDisplayService} from '../../services/project-name-display.service'
@Component({
  selector: 'app-pm-details-tab',
  templateUrl: './pm-details-tab.component.html',
  styleUrls: ['./pm-details-tab.component.scss']
})
export class PmDetailsTabComponent implements OnInit {
  
  $onDestroy = new Subject<any>();
  item_name: any="Project Name"
  billingTabActive: boolean = false;
  itemName: any;
  formConfig: any;
  button: any;
  fontStyle: any;
  projectId:any;
  itemId:any;
  tabColor:any;
  scrollColor: any;
  service_type_id:any
  serviceTypeList: any=[];
  with_opportunity: any;
  tabLinks = [
    {id:0, label: "Overview", path: "overview", toDisplay : false, isActive:true, object_id: 25 },
    {id:1, label: "Billing", path: "billing", toDisplay: false,isActive : true , object_id: 26},
    {id:2, label: "Planning Board", path: "planningboard", toDisplay : false,isActive : true, object_id: 27},
    {id:3, label: "Timesheet", path: "timesheet", toDisplay : false,isActive : true, object_id: 28 },
    {id:4, label: "Finance", path: "finance", toDisplay : false,isActive : true, object_id: 29 },
    {id:5, label: "Documents", path: "documents", toDisplay : false,isActive : true, object_id:31},
    {id:6, label: "Allocation & Planning", path: "project-team", toDisplay : false,isActive : true, object_id: 30 },
    {id:7, label: "Project Settings", path: "project-settings", toDisplay : false,isActive : true, object_id: 34},
    {id:8, label: "Risk Register", path:"risk-register", toDisplay:false, isActive: true, object_id: 32},
    {id:9, label: "Edit logs", path:"activity", toDisplay: false, isActive: true, object_id: 33},
    {id:10,label:"My Task" , path:"my-task",toDisplay:false,isActive:true,object_id:300}
  ];
  
  constructor(
    private _router: Router,
    private authService: PmAuthorizationService,
    private pmMasterService: PmMasterService,
    public pmNameService: ProjectNameDisplayService
  ) { }

  async ngOnInit(){

    this.itemId = parseInt(this._router.url.split("/")[5])
    this.projectId = parseInt(this._router.url.split("/")[3])
    await this.pmMasterService.serviceTypeList().then((res) => {
      this.serviceTypeList = res
    })
    
    let internal=_.where(this.serviceTypeList,{is_internal:1})
    await this.pmMasterService.getProjectQuote(this.projectId,this.itemId).then((res)=>{
      if(res && res['data'] && res['data'].length>0)
      {
            this.service_type_id=res['data'][0].service_type_id
            this.with_opportunity = res['data'][0]['with_opportunity']
      }
    })
    for(let tab of this.tabLinks)
    {
      tab['toDisplay'] = await this.authService.getProjectWiseObjectAccess(this.projectId, this.itemId, tab['object_id'])
      console.log('toDisplay set to:', tab['toDisplay']);
      //tab['toDisplay'] = true;
      if(internal.length>0)
      {
        if(this.service_type_id==internal[0].id && (tab['object_id']==26 || tab['object_id']==29))
        {
          tab['toDisplay']=false
        }
        if(this.with_opportunity == 0 && tab['object_id']==29)
        {
          tab['toDisplay']=false
        }
      }
 
    }
    await this.pmMasterService.getPMFormCustomizeConfigV().then((res: any) => {
      this.formConfig = res;
      if(this.formConfig.length > 0){
        const retrieveStyles = _.where(this.formConfig, { type: "project-theme", field_name: "styles", is_active: true });
        this.button = retrieveStyles.length > 0 ? retrieveStyles[0].data.button_color ? retrieveStyles[0].data.button_color : "#90ee90" : "#90ee90";
        document.documentElement.style.setProperty('--detailsTab', this.button)
        this.tabColor = retrieveStyles.length > 0 ? retrieveStyles[0].data.tab_color ? retrieveStyles[0].data.tab_color : "" : "";
        document.documentElement.style.setProperty('--detailsTabColor', this.tabColor)
        this.fontStyle = retrieveStyles.length > 0 ? retrieveStyles[0].data.font_style ? retrieveStyles[0].data.font_style : "Roboto" : "Roboto";
        document.documentElement.style.setProperty('--detailsTabFont', this.fontStyle);
        this.scrollColor = retrieveStyles.length > 0 ? retrieveStyles[0].data.scroll_color ? retrieveStyles[0].data.scroll_color : "#90ee90" : "#90ee90";
        document.documentElement.style.setProperty('--project2Scroll', this.scrollColor)
      }
    })

    this.calculateDynamicContentHeight();
    this.item_name = decodeURIComponent(this._router.url.split("/")[6])
    await this.pmNameService.getProjectName(this.projectId, this.itemId).then((res)=>{
      this.pmNameService.projectName = res
    })
    
    this.itemName = this.item_name;
  }
  cards(cards: any, arg1: { is_internal: number; }) {
    throw new Error('Method not implemented.');
  }
  navigateToLandingPage(){
    this._router.navigateByUrl('/main/project-management', { replaceUrl: true });
  }



  onTabClick(event: Event, link: any): void {
    // Your existing logic for disabling tabs
    this.disableTab(event, link.id);

    // Check if the clicked tab is "billing"
    this.billingTabActive = link.label.toLowerCase() === 'billing';
    console.log(this.billingTabActive);
  }
  
  disableTab(event: Event, tabId: number): void {
    event.preventDefault();
  }

  ngOnDestroy(): void {
    this.$onDestroy.next();
    this.$onDestroy.complete();
  }

  calculateDynamicContentHeight() {
    let dynamicinnerHeight = window.innerHeight - 148 + 'px';
    document.documentElement.style.setProperty(
      '--innerDetailsTabHeight',
      dynamicinnerHeight
    );
  }

  @HostListener('window:resize')
  onResize() {
    this.calculateDynamicContentHeight();
  }
}
