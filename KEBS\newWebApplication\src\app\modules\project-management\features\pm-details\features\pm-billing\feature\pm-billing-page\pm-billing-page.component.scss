.pm-billing-page{
  // max-width: 100%;
  /* width: 100%; */
  padding-left: 23px;
  /* overflow-y: auto; */
  /* height: 100%; */
  /* overflow-x: hidden; */
  margin-top: 10px;
  overflow-x: auto;
  height: var(--milestoneListHeight);

  .header{
    .project-finacial-details{
      .font-family{
        font-family: var(--milestoneFont) !important;
      }
      
      ::ng-deep app-currency {
        font-size: 9px !important;
      }
    
      ::ng-deep .data-label {
        font-size: 9px !important;
        font-weight: 500;
        line-height: 19px;
        letter-spacing: 0.02em;
        text-align: left;
        color: #111434;
      }
    
      ::ng-deep .code-header {
        font-size: 9px !important;
        font-weight: 500;
        line-height: 19px;
        letter-spacing: 0.02em;
        text-align: left;
        color: #111434;
      }

      ::ng-deep .change-icon{
        font-size: 9px !important;
      }
    
    }
  }
    .icons{
      white-space: nowrap;
      display: flex;
      /* margin-left: 70%; */
      margin-top: -47px;
      position: absolute;
      right: 37.5px;
      /* z-index: 100;*/
    }
   
    .tab-name{
        width: 71px;
        height: 24px;
        //styleName: General heading;
        font-family: var(--milestoneFont) !important;
        font-size: 16px;
        font-weight: 600;
        line-height: 24px;
        letter-spacing: 0em;
        text-align: left;
        text-emphasis-color: #111434;
        margin-left: -39px;
        margin-top: 10px;
        margin-bottom: 10px;
    }
    .card-m{
    // width: 1130px;
    //  height: 208px;
    //  border: 0px 0px 1px 0px;
    //  border-color:#D4D6D8;
    //  background-color:#FFFFFF
    width: 100%;
    // height: 220px;
    // border: 0px 0px 1px 0px solid #D4D6D8;
    /* border-color: #D4D6D8; */
    background-color: #FFFFFF;
    left: -32px;
    position: relative;
    /* border: 1px solid; */
    border-bottom: 1px solid #D4D6D8;
    padding-bottom: 5px;
    }
    .card-m .details{
        // width:292px;
        // height: 172px;
        // top: 14px;
        // left: 176px;
        // gap: 8px
       width: calc(100% - 250px);
        // height: 172px;
        height: 100%;
        margin-top: -100px;
        margin-left: 250px;
        gap: 8px;    

    }
    .card-m .details .check{
        width: 16px;
        height: 16px;
        margin-left: -30px;
        margin-top: 11px;


    }
    .card-m .details .inside-details{
        width: 100%;
        height: 100%;
        gap: 16px;
        padding-bottom: 20px;

    }
    .card-m .details .inside-details .milestone-details{
        width: 100%;
        height: 100%;
        gap: 12px

    }
    .card-m .details .inside-details .progress{
        // width: 260px;
        width: 300px;
        height: 14px;
        border-radius: 4px;

    }
    .card-m .details .inside-details .progress .progress-bar{
        // width: 172px;
        width: 300px;
        height: 8px;
        top: 121px;
        border-radius: 4px;

    }
    .card-m .details .inside-details .progress .progress-name{
        width: 80px;
        height: 14px;
        top: 118px;
        left: 180px;
        font-family: var(--milestoneFont) !important;
        font-size: 12px;
        font-weight: 400;
        line-height: 14px;
        letter-spacing: 0.02em;
        text-align: center;

    }
    .inside-details-footer{
        // width: 87px;
        // height: 24px;
        height:100%;
        gap: 16px;
        display:flex;
        // padding-top: 10px;
        // margin-top: -30px;


    }
    .card-m .details .inside-details .inside-details-footer .pending_actions{
        width: 24px;
        height: 24px;
        cursor: pointer;
        font-size: 23px;

    }
    
    .card-m .details .inside-details .milestone-details .milestone-inside-details{
        width: 100%;
        // height: 70px;
        height: 100%;
        gap: 8px;
        margin-top: -28px;
        cursor: pointer;
    }
    .card-m .details .inside-details .milestone-details .milestone-inside-details .milestone-name{
        // width: 100%;
        height: 19px;
        font-family: var(--milestoneFont) !important;
        font-size: 12px;
        font-weight: 600;
        line-height: 19px;
        letter-spacing: 0.02em;
        text-align: left;
        padding-bottom: 25px;
        color: #111434;
        padding-right: 10px;
        margin-top: 1px;
    }
    .card-m .details .inside-details .milestone-details .milestone-inside-details .milestone-id{  
      font-family: var(--milestoneFont) !important;
      font-size: 11px;
      font-weight: 400;
      line-height: 16px;
      letter-spacing: 0.02em;
      text-align: left;
      color: #7D838B;
      background: whitesmoke;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 3px;
      margin-top: 0px;
      height: 20px;
      border-radius: 4px;
    }
    .card-m .details .inside-details .milestone-details .milestone-inside-details .milestone-value{
        // width: 61px;
        height: 19px;
        font-family: var(--milestoneFont) !important;
        font-size: 16px;
        font-weight: 500;
        line-height: 19px;
        letter-spacing: 0.02em;
        text-align: left;
    }
    .card-m .status-delete{    
    // // width: 105px;
    // // height: 24px;
    // top: 14px;
    // left: 1082px;
    // gap: 4px;
    // // margin-left: auto;
    // // margin-top: -160px;
    // padding: 12px;
    // margin-left: 752px;
    // margin-top: -95px;
    top: 0;
    /* left: 1082px; */
    gap: 4px;
    padding: 12px;
    /* margin-left: 752px; */
    /* margin-top: -95px; */
    right: 0px;
    z-index: 100;
    position: absolute;
    }
    .card-m .status-delete .status{
        width: auto;
        height: 22px;
        padding: 4px 8px 4px 8px;
        border-radius: 10px;
        background: #FFBD3D;
        width: max-content;
    }
    .card-m .status-delete .status-empty{
        width: auto;
        height: 22px;
        padding: 4px 8px 4px 8px;
        border-radius: 10px;
      
        width: max-content;
    }
    .card-m .status-delete .status .status-name{
        // width: 55px;
       height: 14px;
       font-family: var(--milestoneFont) !important;
       font-size: 12px;
       font-weight: 500;
       line-height: 14px;
       letter-spacing: 0.02em;
       text-align: center;
       color: #FFFFFF
    }
    .card-m .status-delete .delete{
        width: 24px;
        height: 24px;
        opacity: 0.5px;
        margin-top: -22px;
    }
    .card-m .status-delete .delete .delete-icon{
        width: 14.9999513626px;
    height: 16.8845500946px;
    top: 3.615234375px;
    left: 79.5px;
    /* background: #1C1B1F; */
    margin-left: 80px;
    cursor:pointer
    }
    .card-m .details .inside-details .milestone-details .tags{
        width: 779px;
        gap: 8px;
        overflow-y: hidden;
    //     margin-top: -32px;
    // margin-left: 95px;
    

    }
    .chips-container {
        display: flex;
        flex-wrap: nowrap;
        // gap: 10px; /* Adjust the gap between chips */
        // padding-top: 10px;
        // margin-left: -10px
        // gap: 10px;
        gap: 10px;
      }
      
      .custom-chip {
        width: auto;
        min-width: 0;
        height: 20px;
        padding: 2px 8px 2px 8px;
        border-radius: 16px;
        border: 1px;
        gap: 4px;
        white-space: nowrap;
        background: #F6F6F6;
        position: absolute;

      }
      .dot {
        width: 7px;
        height: 7px;
        border-radius: 50%;
        margin-top: 5px;
      }
      .tag-text{
        // width: 37px;
        height: 16px;
        font-family: var(--milestoneFont) !important;
        font-size: 11px;
        font-weight: 400;
        line-height: 16px;
        letter-spacing: 0em;
        text-align: left;
        -webkit-text-emphasis-color: #7D838B;
        text-emphasis-color: #7D838B;
        margin-left: 12px;
        margin-top: -12px;
        color: #7D838B;
      }
      .status-chip{
        width: max-content;
        height: 20px;
        padding: 2px 8px 2px 8px;
        border-radius: 16px;
        border: 1px solid #FF3A46;
        /* gap: 4px; */
        margin-top: 2px;
        background: linear-gradient(0deg, #FFEBEC, #FFEBEC), linear-gradient(0deg, #FF3A46, #FF3A46);
        cursor: pointer;
        display: flex;

      }
      .status-text{
        // width: 55px;
        height: 14px;
        font-family: var(--milestoneFont) !important;
        font-size: 12px;
        font-weight: 400;
        line-height: 14px;
        letter-spacing: 0.02em;
        text-align: center;
        color:#FF3A46;
        width: max-content;
      }
      .popup {
        position: absolute;
        top: 100%;
        left: 0;
        background-color: white;
        border: 1px solid #ccc;
        padding: 10px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        z-index: 1;
      }
      
      .popup select {
        width: 100%;
      }
      .content{
        left: 43%;
        position: fixed;
        top: 46%;

      }
      // .img{
      //   width: 184.4px;
      //   height: 180px;
      //   margin-left: 30px;

      // }
      .save-button{
        width: 120px;
        height: 40px;
        padding: 12px 16px 12px 16px;
        border-radius: 4px;
        gap: 8px;
        font-family: var(--milestoneFont) !important;
        font-size: 14px;
        font-weight: 700;
        line-height: 16px;
        letter-spacing: -0.02em;
        text-align: left;
         color: #FFFFFF;
        background: var(--milestoneButton) !important;
        // margin-left: 750px;
      /* position: fixed; */
      margin-left: 77.5px;
      margin-top: 10px;
    }
    .description{
        width: 272px;
        height: 32px;
        font-family: var(--milestoneFont) !important;
        font-size: 14px;
        font-weight: 400;
        line-height: 16px;
        letter-spacing: 0.02em;
        text-align: center;
        color: #45546E;
        white-space: nowrap;
        margin-top: 5px;
    }
    .tittle{
        width: 272px;
height: 24px;
font-family: var(--milestoneFont) !important;
font-size: 14px;
font-weight: 700;
line-height: 24px;
letter-spacing: 0.02em;
text-align: center;
color: #45546E;

    }
    .img-data{
      width: 130.4px;
      height: 125px;
      margin-left: 67px;
    }
    
.column-list {
    display: flex;
    flex-direction: column;
    // gap: 10px;
    // margin-top: 10px;
    // margin-left: 10px;
}

.column-item {
    display: flex;
    align-items: center;
}
.pop-up{
    height: 90px;
    width: 150px;
    font-size: 13px;
    padding: 18px;
    border-radius: 10px !important;
    position: absolute !important;
    background-color: white !important;
    z-index: 1 !important;
    transition: box-shadow 200ms cubic-bezier(0, 0, 0.2, 1) !important;
    box-shadow: 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12) !important;
    margin-top: -91px;
    margin-left: 40px;
  }
//   ::ng-deep .mat-progress-bar {
//     display: block;
//     height: 4px;
//     overflow: hidden;
//     position: relative;
//     transition: opacity 250ms linear;
//     width: 300px;
// }
.actions{
    // width: 685px;
    height: 16px;
    gap: 32px;
    opacity: 0.6px;
    display: flex;
    // margin-top: 49px;
    // margin-left: 253px;
    padding-bottom: 25px;
    margin-left:2px;
}
.add-notes{
    width: 85px;
    height: 16px;
    gap: 8px;
    display:flex;
    cursor:pointer;
    
}
.notes-icon{
    width: 13.87px;
    height: 13.87px;
    top: 1.4px;
    left: 0.94px;
    color: #7D838B;
    font-size: 16px;
    
}
.notes-name{
    width: 61px;
    height: 15px;
    font-family: var(--milestoneFont) !important;
font-size: 11.4px;
font-weight: 400;
line-height: 15px;
letter-spacing: 0em;
text-align: left;
color: #8B95A5;
white-space: nowrap;
    
}
.tags-name{
    width: 46px;
    height: 15px;
    font-family: var(--milestoneFont) !important;
font-size: 11.4px;
font-weight: 400;
line-height: 15px;
letter-spacing: 0em;
text-align: left;
color: #8B95A5;
white-space: nowrap;
    
}
.tags-icon{
    width: 13.87px;
    height: 13.87px;
    top: 1.4px;
    left: 0.94px;
    color: #7D838B;
    font-size: 16px;
    
}
.add-tags{
    width: 69px;
height: 16px;
gap: 8px;
display:flex;
cursor:pointer;

}
.attach-task{
    width: 146px;
height: 16px;
gap: 8px;
display:flex;
cursor:pointer;

}
.task-icon{
    width: 12px;
height: 12.67px;
top: 2.67px;
left: 2px;
color: #7D838B;
font-size: 16px;

}
.task-name{
    width: 122px;
height: 15px;
font-family: var(--milestoneFont) !important;
font-size: 11.4px;
font-weight: 400;
line-height: 15px;
letter-spacing: 0em;
text-align: left;
color: #8B95A5;
white-space: nowrap;
}
.add-attachment{
    width: 122px;
height: 16px;
gap: 8px;
display:flex;
cursor:pointer;

}
.attachment-icon{
    width: 11.74px;
height: 12.67px;
top: 1.41px;
left: 1.92px;
color:#7D838B;
font-size: 16px;
}
.attachment-name{
    width: 98px;
height: 15px;
font-family: var(--milestoneFont) !important;
font-size: 11.4px;
font-weight: 400;
line-height: 15px;
letter-spacing: 0em;
text-align: left;
color:#8B95A5;
white-space: nowrap;
}
.add-dependencies{
    width: 135px;
height: 16px;
gap: 8px;
display:flex;
cursor:pointer;

}
.dependencies-icon{
    width: 14.43px;
height: 7.99px;
top: 4.54px;
left: 0.78px;
color:#7D838B;
font-size: 16px;
}
.dependencies-name{
    width: 111px;
height: 15px;
font-family: var(--milestoneFont) !important;
font-size: 11.4px;
font-weight: 400;
line-height: 15px;
letter-spacing: 0em;
text-align: left;
color:#8B95A5;
white-space: nowrap;
}

.custom-progress-bar {
    width: 172px;
    border-radius: 10px;
    background-color: white !important;
    height: 7px;
/* Set the desired color here */
  }
  
//   .custom-progress-bar .mat-progress-bar-buffer,
//   .custom-progress-bar .mat-progress-bar-primary {
//     background-color: #007bff; /* Set the color for the filled part of the progress bar */
//   }
  ::ng-deep .mat-progress-bar-fill::after {
    background-color: var(--milestoneButton) !important;
}
::ng-deep .mat-progress-bar-buffer {
    background-color: #E8E9EE;
}
::ng-deep .dx-tooltip-wrapper .dx-overlay-content .dx-popup-content {
    background-color: white;
    border: 1px solid #111434;
  }
  
  ::ng-deep .dx-popover-wrapper .dx-popover-arrow {
    display: block !important;
  }
  
  ::ng-deep .dx-tooltip-wrapper.dx-popover-wrapper .dx-popover-arrow {
    display: block !important;
  }
  
  ::ng-deep .dx-popover-wrapper .dx-popover-arrow::after,
  .dx-popover-wrapper.dx-popover-without-title .dx-popover-arrow::after {
    background: #fff;
    box-shadow: 0px 0px 0px 1px rgba(0, 0, 0, 0.1),
      0 5px 7px 3px rgba(0, 0, 0, 0.1), 0 9px 3px -6px rgba(0, 0, 0, 0.1),
      1px 0px 3px 1px rgba(0, 0, 0, 0.2);
  }
  
  ::ng-deep .dx-popup-content {
    padding: 8px !important;
  }
  
  ::ng-deep .dx-popup-wrapper > .dx-overlay-content {
    box-shadow: 0px 0px 0px 1px rgba(0, 0, 0, 0.1),
      0 5px 7px 3px rgba(0, 0, 0, 0.1), 0 9px 3px -6px rgba(0, 0, 0, 0.1),
      1px 0px 3px 1px rgba(0, 0, 0, 0.2);
  }

  ::ng-deep .mat-checkbox-checked.mat-accent .mat-checkbox-background {
    background-color: var(--milestoneButton) !important;
  }
  ::ng-deep .mat-checkbox-ripple .mat-ripple-element {
    background-color: var(--milestoneButton) !important;
  }
  ::ng-deep .mat-checkbox-indeterminate.mat-accent .mat-checkbox-background {
    background-color: var(--milestoneButton) !important;
  }
  ::ng-deep .button-toggle-class .mat-button-toggle-checked .mat-button-toggle-button {
    background-color: transparent;
  }
  // .loader-container {
  //   display: flex;
  //   justify-content: center;
  //   align-items: center;
  //   height: 100vh; /* Adjust the height as needed */
  //   background-color: whitesmoke !important; ; /* Add a semi-transparent background */
  // }

  .loader-container {
    display: flex;
    justify-content: center;
    align-items: center;
    // height: 100vh; /* Adjust the height as needed */
    background-color: white !important; /* Add a semi-transparent background */
  }
  
  // .loader {
  //   border: 4px solid #f5f5f5 !important;
  //   border-top: 4px solid var(--milestoneButton) !important; /* Change the border color to lightgreen */
  //   border-radius: 50%;
  //   width: 40px;
  //   height: 40px;
  //   margin-bottom: 25% !important;
  //   animation: spin 1s linear infinite;
  // }
  .loader-container ::ng-deep .green-spinner circle {
    stroke: var(--milestoneButton) !important;
  }
  .loader{
    top: 45%;
    position: absolute;
    left: 50%;
    bottom: 50%;
}

  .create{
    width: 20px;
    height: 32px;
    /* padding: 6px; */
    color: #7D838B;
    margin-top: 9px;
    margin-left: 15px;
    cursor: pointer;
    font-size: 22px !important;
    }
    .date-range{
         /* width: 20px; */
  /* height: 20px; */
  top: 1px;
  left: 2px;
  color: #7D838B;
  cursor: pointer;
  margin-left: 10px;
  margin-top: 8px;

    }
    .all{
          /* width: 37px; */
  /* height: 16px; */
  gap: 8px;
  // margin-left: -67px;
  cursor: pointer;
  color: #7D838B;
  font-size: 13px;
  font-weight: bold;
  display: flex;
  justify-content: center;
  align-items: center;

    }
    .all-icon{
      width: 16px;
      height: 16px;
      font-size: 15px;
      display: flex;
      white-space: nowrap;
      align-items: center;
      justify-content: center;
      margin-top: -2px !important;
    }
    
    .multiple{
      width: 20px;
      height: 18px;
      top: 3px;
      left: 2px;
      color: #7D838B;
      margin-top: 9px;
      margin-left: 13px;
      cursor: pointer;
      font-size: 22px !important;

    }
    .color{
      color: #7D838B;
      margin-top: 8px;
      margin-left: 15px;
      cursor: pointer;
      font-size: 22px !important;
    }
    .sort{
      width: 24px;
      height: 24px;
      margin-top: 8px;
      margin-left: 10px;
      color: #7D838B;
      cursor: pointer;
      font-size: 22px !important;

    }
    .filter{
      width: 24px;
      height: 24px;
      margin-top: 8px;
      margin-left: 0px;
      color: #7D838B;
      cursor: pointer;
      font-size: 22px !important;
    }
    .edit{
      width: 24px;
      height: 24px;
      margin-top: 8px;
      margin-left: 10px;
      color: #7D838B;
      cursor: pointer;
      font-size: 22px !important;
    }
    .delete{
      width: 24px;
      height: 24px;
      margin-top: 8px;
      margin-left: 10px;
      color: #7D838B;
      cursor: pointer;
      font-size: 22px !important;
    }
    .custom-confirm-button {
      background-color: #4CAF50; /* Green color */
      color: #fff; /* White text */
      border: none;
      padding: 10px 20px;
      text-align: center;
      text-decoration: none;
      display: inline-block;
      font-size: 16px;
      margin: 4px 2px;
      cursor: pointer;
    }
    .swal2-styled.swal2-confirm {
      border: 0;
      border-radius: 0.25em;
      background: initial;
      color: #fff;
      font-size: 1.0625em;
      background-color: var(--milestoneButton) !important;
  }
  // .loader-container {
  //   display: flex;
  //   justify-content: center;
  //   align-items: center;
  //   height: 100vh; /* Adjust the height as needed */
  //   background-color:white !important;  /* Add a semi-transparent background */
  // }
  
  // .loader {
  //   border: 4px solid  #f5f5f5 !important;
  //   border-top: 4px solid var(--milestoneButton) !important; /* Change the border color to lightgreen */
  //   border-radius: 50%;
  //   width: 40px;
  //   height: 40px;
  //   margin-bottom: 25% !important;
  //   animation: spin 1s linear infinite;
  // }
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
  
  // .loader {
  //   border: 4px solid  #f5f5f5 !important;
  //   border-top: 4px solid var(--milestoneButton) !important;/* Change the border color to lightgreen */
  //   border-radius: 50%;
  //   width: 40px;
  //   height: 40px;
  //   margin-bottom: 25% !important;
  //   animation: spin 1s linear infinite;
  // }
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
::-webkit-scrollbar {
    width: 8px !important;
    height: 8px !important;
}
.list-card{
    background-color: white;
    border-left: 2px solid lightgrey;
    width: max-content;
    height: 35px;
    display: flex;
    align-items: center;
    //margin-left: -25px !important;
}
.list-view {
  top: 3px;
  color: #7D838B;
  margin-top: 5px;
  cursor: pointer;
  font-size: 18px;
  display: flex;
  justify-content: center;
  /* align-items: center; */
  width: 10px;
}
.card-view {
  width: 10px;
    top: 3px;
    left: 2px;
    color: #7D838B;
    cursor: pointer;
    margin-top: 2px;
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.overall-list {
  margin-top: 10px;
}
.column-row{
  // margin-top: 15px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  height: 38px;
  display: flex;
  align-items: center;
  background: #ffff;
  width: 1110px;
}
.button-toggle-class{
  height: 26px;
    width: 65px;
    margin-top: 7px;
    margin-left: 10px;
    margin-right: 5px;
    align-items: center;
}
.actions-icon{
  font-size: 16px !important;
  padding-top: 6px;
}
.data-row{
  margin-top: 5px !important;
  // height: 60vh;
  height: var(--listHeight);
  overflow: auto;
}
.name-col {
  width: 170px;
  font-size: 13.5px;
  font-family: var(--milestoneFont) !important;
  font-weight: 500;
  line-height: 16px;
  word-wrap: break-word;
}
.status-col{
  width: 150px;
}
.calender-icon-list{
  font-size: 17px !important;
  margin-right: 10px !important;
}
.icon-col {
  // width: 8%;
  // margin-right: 18px;
  padding-top: 8px;
  width: 50px;
    margin-left: 5px;
    display: flex;
}
.select-header{
  width: 73px;
  padding-right: 1%;
  padding-top: 8px;
}
.status-header{
  display: flex;
  align-items: center;
  justify-content: left;
  font-size: 12px;
  color: grey;
  margin-left: 1px;
  width: 145px;
}
.pop-up-container{
    height: 92px;
    width: 110px;
    font-size: 13px;
    padding: 18px;
    border-radius: 10px !important;
    position: absolute !important;
    background-color: white !important;
    z-index: 1 !important;
    transition: box-shadow 200ms cubic-bezier(0, 0, 0.2, 1) !important;
    box-shadow: 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12) !important;
    margin-left: 40%;
    margin-top: 6%;
}
.pop-up-container1{
  height: 92px;
  width: 110px;
  font-size: 13px;
  padding: 18px;
  border-radius: 10px !important;
  position: absolute !important;
  background-color: white !important;
  z-index: 1 !important;
  transition: box-shadow 200ms cubic-bezier(0, 0, 0.2, 1) !important;
  box-shadow: 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12) !important;
  margin-left: 646px;
  margin-top: 83px;
}
.first-part-header {
  display: flex;
  align-items: center;
  justify-content: left;
  max-width: 45%;
}
.second-part-header {
  display: flex;
  justify-content: left;
  align-items: center;
  // margin-left: 5%;
}
.name-header {
  width: 170px;
  display: flex;
  align-items: center;
  justify-content: left;
  font-size: 12px;
  margin-left: -1%;
  color: grey;
}
.milestone_attachments{
  display: flex;
  align-items: center;
  justify-content: left;
  font-size: 12px;
  margin-left: 22px;
  color: grey;
}
.milestone_task{
  display: flex;
  align-items: center;
  justify-content: left;
  font-size: 12px;
  margin-left: 8px;
  color: grey;
}
.actions-header{
  display: flex;
  align-items: center;
  justify-content: left;
  font-size: 12px;
  margin-left: 4px;
  color: grey;
  // width: 24%;
  margin-left: 1%;
}
.name-text{
  width: 150px;
  cursor: pointer;
  color: #1b2140;
  overflow: hidden;
  text-overflow: ellipsis;
 text-wrap: nowrap;
}
.date-col {
  //margin-left: 35px;
  color: #45546E;
  font-size: 12px;
  font-family: var(--milestoneFont) !important;
  font-weight: 500;
  line-height: 16px;
  width: 130px;
  word-wrap: break-word;
}

.percentage-col {
  margin-left: 10px;
  color: #45546E;
  font-size: 12px;
  font-family: var(--milestoneFont) !important;
  font-weight: 500;
  line-height: 16px;
  width: 180px;
  word-wrap: break-word;
}
.startDate-header {
  font-size: 12px;
    color: grey;
    width: 130px;
    display: flex;
    align-items: center;
    justify-content: left;
    //margin-left: -3%;
}
.endDate-header {
  font-size: 12px;
  color: grey;
  width: 130px;
  display: flex;
  align-items: center;
  justify-content: left;
}
.value-header {
    font-size: 12px;
    color: grey;
    width: 140px;
    display: flex;
    align-items: center;
    justify-content: left;
}
.value-col{
  width: 140px;
}
.percentage-header {
  font-size: 12px;
  color: grey;
  width: 180px;
  display: flex;
  align-items: center;
  justify-content: left;
  margin-left: 10px;

}
.more-actions {
  font-size: 14px !important;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}
.more-actions-header{
  width: 50px;
}
.view-hierarchy-col{
  cursor: pointer;
  margin-right: 10px;
}
.first-part{
  display: flex;
  align-items: center;
  max-width: 40% !important;
  justify-content: left;
}
.second-part{
  display: flex;
    align-items: center;
    max-width: 65%;
    justify-content: left;
    margin-left: 7%;
    //padding-left: 35px;
}
.pending_action{
  width: 24px;
  height: 24px;
  cursor: pointer;
  font-size: 23px;
}
::ng-deep .mat-menu-panel {
  min-width: 112px;
  max-width: 280px;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
  max-height: calc(100vh - 48px);
  border-radius: 4px;
  outline: 0;
  min-height: 64px;
  overflow: hidden;
}
.revert{
  width: 24px;
  height: 24px;
  margin-top: 8px;
  margin-left: 10px;
  color: #7D838B;
  cursor: pointer;
  font-size: 22px !important;
}
.id-col {
  //margin-left: 35px;
  color: #45546E;
  font-size: 12px;
  font-family: var(--milestoneFont) !important;
  font-weight: 500;
  line-height: 16px;
  width: 90px;
  word-wrap: break-word;
}
.type-col {
  //margin-left: 35px;
  color: #45546E;
  font-size: 12px;
  font-family: var(--milestoneFont) !important;
  font-weight: 500;
  line-height: 16px;
  width: 150px;
  word-wrap: break-word;
}
.type-chip{
  width: 100px;
  height: 20px;
  background: #FFBD3D;
  border-radius: 9999px;
  justify-content: center;
  align-items: center;
  align-content: center;
  text-align: center;
}

.type-text{  
  color: white;
  font-size: 12px;
  font-family: var(--milestoneFont) !important;
  font-weight: 500;
  line-height: 16px;
}
.expand-icon-1 {
  font-size: 16px;
  padding-top: 0px;
  cursor: pointer;
  margin-left: 10px;
}
.id-header {
  width: 105px;
  display: flex;
  align-items: center;
  justify-content: left;
  font-size: 12px;
  margin-left: -2%;
  color: grey;
}
.type-header{
  display: flex;
  align-items: center;
  justify-content: left;
  font-size: 12px;
  color: grey;
  margin-left: 1px;
  width:150px;
}
.table-data{
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    height: 30px;
    margin-top: 15px;
}
.example{
  width:56px;
}
.type-col-1 {
  //margin-left: 35px;
  color: #45546E;
  font-size: 12px;
  font-family: var(--milestoneFont) !important;
  font-weight: 500;
  line-height: 16px;
  width: 150px;
  word-wrap: break-word;
  margin-top: 3px;
  margin-left: 10px;
}
.type-chip-1{
  width: max-content;
  height: 20px;
  background: #FFBD3D;
  border-radius: 9999px;
  justify-content: center;
  align-items: center;
  align-content: center;
  text-align: center;
  padding-left: 10px;
  padding-right: 10px;
}

.type-text-1{  
  color: white;
  font-size: 10px;
  font-family: var(--milestoneFont) !important;
  font-weight: 500;
  line-height: 16px;
}

  .filter{

    gap: 8px;
    cursor: pointer;
    color: #7D838B;
    font-size: 13px;
    font-weight: bold;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .filter-icon{
    width: 16px;
    height: 16px;
    font-size: 15px;
    display: flex;
    white-space: nowrap;
    align-items: center;
    justify-content: center;
  }

  .card-date-label{
    font-family: var(--milestoneFont) !important;
    font-weight: 400;
    font-size: 11px;
    color: #6E7B8F;
  }

    .card-date-value {
      font-family: var(--milestoneFont) !important;
      color: #111434;
      font-weight: 500;
      font-size: 10px;
    }
  
    .data-header {
      background-color: white;
      position: absolute;
      z-index: 1000;
      width: calc(100% - 50px);
  
      .project-value-header {
        border: 1px solid #E8E9EE;
        border-radius: 4px;
        font-family: var(--milestoneFont) !important;
        padding: 6px 12px 6px 12px;
        width: max-content;
  
        .value-card {
          min-width: 130px;
  
          .value-label {
            color: #6E7B8F;
            font-size: 12px;
            font-weight: 400;
            letter-spacing: 2%;
            line-height: 18.23px;
            text-transform: capitalize;
          }
        }
  
        .divider {
          border-left: 1px solid #E8E9EE;
          height: 39px;
        }
      }
    }
  
    .milestone-data {
      font-family: var(--milestoneFont) !important;
      font-size: 12px;
      letter-spacing: 2%;
      line-height: 18.23px;
      text-transform: capitalize;
      padding-top: 31px;
      margin-top: 26px;
  
      .new-card {
        display: flex;
        flex-direction: column;
        width: 100%;
        padding: unset;
        background-color: white;
        border-bottom: 1px solid #E8E9EE;
        max-height: 170px;
  
        .month-header {
          background-color: #F6F6F6;
          font-weight: 500;
          font-size: 14px;
          max-height: 32px;
          border-radius: 4px;
          padding: 6px 6px 6px 6px;
        }
  
        .milestone-values {
          padding: unset;

          &.selected-card{
            box-shadow: rgb(219, 110, 97) 0px 0px 8px 0px; 
            margin-top: 5px;
          }
  
          .calender-data {
            .selection-check-circle {
              font-size: 30px;
              margin-left: 40%;
              margin-top: 15px;
              cursor: pointer;
            }
          }
  
          .milestone-content {
            display: flex;
            flex-direction: column;
            margin-bottom: 10px;
  
            .first-row-content {
              justify-content: space-between;
              margin-top: 20px;
  
              .milestone-id {
                font-family: var(--milestoneFont) !important;
                width: 100px;
                font-size: 12px;
                font-weight: 400;
                line-height: 16px;
                letter-spacing: 0.02em;
                text-align: left;
                color: #7D838B;
                background: whitesmoke;
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 0px 2px 0px 2px;
                margin-top: 0px;
                height: 20px;
                border-radius: 4px;
              }
  
              .milestone-name {
                height: 20px;
                font-family: var(--milestoneFont) !important;
                font-size: 14px;
                font-weight: 500;
                line-height: 20.83px;
                letter-spacing: 0.02em;
                text-align: left;
                padding-bottom: 25px;
                color: #45546E;
                padding-right: 10px;
                margin-top: -1px;
                cursor: pointer;
              }
  
              .milestone-type {
                width: max-content;
                font-family: var(--milestoneFont) !important;
                height: 24px;
                background: #FFBD3D;
                border-radius: 9999px;
                justify-content: center;
                align-items: center;
                align-content: center;
                text-align: center;
                padding-left: 10px;
                padding-right: 10px;
                color: white;
                padding: 4px 8px 4px 8px;
                font-size: 12px;
                margin-top: -4px;
              }
  
              .milestone-action-icons {
                cursor: pointer;
                font-size: 20px;
                color: #7D838B;
                // opacity: 50%;
              }
            }
  
            .second-row-content {
              .milestone-value {
                width: max-content;
                // padding: 5px 0px 5px 0px;
                margin-top: 5px;
              }
              .billed-value{
                width: max-content;
                // padding: 5px 0px 5px 0px;
                margin-top: 5px;
              }
              .divider {
                border-left: 1px solid #E8E9EE;
                height: 39px;
              }
  
              .milestone-parent-id {
                margin-top: 3px;
  
                .chip {
                  display: flex;
                  flex-direction: row;
                  width: max-content;
                  border: 1px solid #E8E9EE;
                  font-family: var(--milestoneFont) !important;
                  border-radius: 16px;
                  padding: 2px 8px 2px 8px;
                  height: 25px;
                  background-color: #F6F6F6;
                  font-size: 11px;
                  font-weight: 400;
                  color: #7D838B;
                  margin-top: 3px;
  
                  .circle {
                    width: 6px;
                    height: 6px;
                    background-color: #1890FF;
                    /* You can change this to any color */
                    border-radius: 50%;
                    /* This makes the div a circle */
                    display: inline-block;
                    /* Ensures the element is displayed inline */
                    margin: 6px;
                  }
                }
              }
  
              .tag-sections {
                display: flex;
                flex-direction: row;
  
                .tags {
                  margin-top: 3px;
                  margin-left: 7px;
  
                  .chip {
                    display: flex;
                    flex-direction: row;
                    width: max-content;
                    border: 1px solid #E8E9EE;
                    font-family: var(--milestoneFont) !important;
                    border-radius: 16px;
                    padding: 2px 8px 2px 8px;
                    height: 25px;
                    background-color: #F6F6F6;
                    font-size: 11px;
                    font-weight: 400;
                    color: #7D838B;
                    margin-top: 3px;
  
                    .circle {
                      width: 6px;
                      height: 6px;
                      background-color: #1890FF;
                      /* You can change this to any color */
                      border-radius: 50%;
                      /* This makes the div a circle */
                      display: inline-block;
                      /* Ensures the element is displayed inline */
                      margin: 6px;
                    }
                  }
                }
              }
            }
  
            .last-row-content {
              margin-top: 8px;
              justify-content: space-between;
  
              .actions-bar {
                .billing-advice-icon {
                  .pending_actions {
                    cursor: pointer;
                    font-size: 25px;
                    color: #1B2140;
                  }
                }
  
                .milestone-status {
                  margin-top: 1px;
                }
  
                .milestone-progress-bar {
                  display: flex;
                  flex-direction: row;
                  background: white !important;
                  margin: 10px 15px 10px 15px;
  
                  .custom-progress-bar {
                    width: 172px;
                    border-radius: 10px;
                    background-color: white !important;
                    height: 7px;
                    /* Set the desired color here */
                  }
  
                  //   .custom-progress-bar .mat-progress-bar-buffer,
                  //   .custom-progress-bar .mat-progress-bar-primary {
                  //     background-color: #007bff; /* Set the color for the filled part of the progress bar */
                  //   }
                  ::ng-deep .mat-progress-bar-fill::after {
                    background-color: var(--milestoneButton) !important;
                  }
  
                  ::ng-deep .mat-progress-bar-buffer {
                    background-color: #E8E9EE;
                  }
  
                  .progress {
                    width: 300px;
                    height: 14px;
                    border-radius: 4px;
                  }
  
                  .progress-bar {
                    width: 300px;
                    height: 8px;
                    top: 121px;
                    border-radius: 4px;
                  }
  
                  .progress-name {
                    width: 110px;
                    height: 14px;
                    top: 118px;
                    left: 180px;
                    font-family: var(--milestoneFont) !important;
                    font-size: 12px;
                    font-weight: 400;
                    line-height: 14px;
                    letter-spacing: 0.02em;
                    text-align: center;
                  }
                }
  
              }
  
              .date-info-section {
                margin-top: 7px;
  .card-date-label {
      font-family: var(--milestoneFont) !important;
      font-weight: 400;
      font-size: 12px;
      color: #6E7B8F;
    }
  
    .card-date-value {
      font-family: var(--milestoneFont) !important;
      color: #111434;
      font-weight: 500;
      font-size: 11px;
    }
  }
  }
  
  }
  }
      }

      .new-list-view{
        font-family: var(--milestoneFont) !important;
        font-size: 12px;
        letter-spacing: 2%;
        line-height: 18.23px;
        text-transform: capitalize;
        display: flex;
        flex-direction: column;
        width: 100%;
        overflow: auto;
        height: calc(var(--milestoneListHeight) - 60px);

        .list-header{
          display: flex;
          padding: unset;
          align-items: center;
          border-bottom: 1px solid #E8E9EE;
          border-top: 1px solid #E8E9EE;
          padding: 5px 0px 5px 0px;
          color:#B9C0CA;
          max-height: 40px;
          position: sticky;
          top: 0;
          z-index: 1;
          background-color: white;
          .item-currency-text{
            overflow: hidden;
            background: white;
            padding: 10px 0px 10px 0px;
            border-top: 1px solid;
            border-bottom: 1px solid #E8E9EE;
            border-top: 1px solid #E8E9EE;
          }
          .line-item-actions{
            display: flex;
            flex-direction: row;
            align-items: center;
            padding: unset;
            background: white;
            padding: 10px 0px 10px 0px;
            border-top: 1px solid;
            border-bottom: 1px solid #E8E9EE;
            border-top: 1px solid #E8E9EE;
          }
        }

        .list-body{
          display: flex;
          flex-direction: column;
          padding: unset;
          color: #45546E;
          max-height: 40px;
          .line-item{
            display: flex;
            padding: 5px 0px 5px 0px;
            align-items: center;

            .line-item-check{
              display: flex;
              justify-content: end;
              height: 35px;
              border-bottom: 1px solid #E8E9EE;
              padding: 5px 0px 5px 0px;
              .checkbox-input{
                height: 15px;
                padding-right: 10px;
                cursor: pointer;
              }
              .expand-input{
                padding-right: 30px;
                margin-top: -3px;
                cursor: pointer;
              }
            }
            .line-item-text{
              overflow: hidden;
              text-overflow: ellipsis;
              text-wrap: nowrap;
              height: 35px;
              border-bottom: 1px solid #E8E9EE;
              padding: 5px 0px 5px 0px;
            }
            .line-item-type{
                width: max-content;
                font-family: var(--milestoneFont) !important;
                height: 24px;
                background: #FFBD3D;
                border-radius: 9999px;
                justify-content: center;
                align-items: center;
                align-content: center;
                text-align: center;
                padding-left: 10px;
                padding-right: 10px;
                color: white;
                padding: 4px 8px 4px 8px;
                font-size: 12px;
                margin-top: -4px;
            }
            .line-item-status{
                width: max-content;
                padding: 1px 8px 1px 8px;
                border-radius: 16px;
                color: white;
                /* gap: 4px; */
                background: linear-gradient(0deg, #FFEBEC, #FFEBEC), linear-gradient(0deg, #FF3A46, #FF3A46);
                cursor: pointer;
                display: flex; 
                align-items: center;      
                height: 20px;
              .item-status-text{
                // width: 55px;
                height: 14px;
                font-family: var(--milestoneFont) !important;
                font-size: 12px !important;
                font-weight: 400;
                line-height: 14px;
                letter-spacing: 0.02em;
                text-align: center;
                color:#FF3A46;
                width: max-content;
              }
              .column-list {
                display: flex;
                flex-direction: column;
                // gap: 10px;
                // margin-top: 10px;
                // margin-left: 10px;
            }
            
            .column-item {
                display: flex;
                align-items: center;
            }
            .pop-up{
                height: 90px;
                width: 150px;
                font-size: 13px;
                padding: 18px;
                border-radius: 10px !important;
                position: absolute !important;
                background-color: white !important;
                z-index: 1 !important;
                transition: box-shadow 200ms cubic-bezier(0, 0, 0.2, 1) !important;
                box-shadow: 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12) !important;
                margin-top: -91px;
                margin-left: 40px;
              }
            }
            .item-currency-text{
              overflow: hidden;
              height: 35px;
              border-bottom: 1px solid #E8E9EE;
              padding: 5px 0px 5px 0px;
            }
            .line-item-actions{
              display: flex;
              flex-direction: row;
              align-items: center;
              padding: unset;
              height: 35px;
              border-bottom: 1px solid #E8E9EE;
              padding: 5px 0px 9px 0px;
            }
            .line-item-date{   
              height: 35px;
              border-bottom: 1px solid #E8E9EE;
              padding: 5px 0px 5px 0px;
            }
          }
        }

        .custom-col {
          flex: 0 0 5%; 
          max-width: 5%; 
        }

        .currency-col{
          flex: 0 0 10%; 
          max-width: 10%; 
        }
        .milestone-name-col{
          flex: 0 0 16%; 
          max-width: 16%; 
        }
        .item-date-col{
          flex: 0 0 8%; 
          max-width: 8%; 
        }
        .item-id-col{
          flex: 0 0 7%; 
          max-width: 7%; 
        }
        .item-actions-col{
          flex: 0 0 15%; 
          max-width: 15%; 
        }
        .item-status-col{
          flex: 0 0 14%; 
          max-width: 14%; 
        }
      }
    }
  
  .customize-icon {
    color: #7D838B;
    margin-top: 10px;
    margin-left: 15px;
    cursor: pointer;
    font-size: 22px !important;
  }
  }
  
  .filter-header {
    height: 35px;
    padding: 10px;
    border-bottom: 1px solid whitesmoke;
  }
  
  .filter-text {
    font-size: 16px;
    font-weight: 500;
  }
  
  .selected-tags {
    font-size: 13px;
    font-weight: 500;
    display: flex;
    flex-wrap: wrap;
    border-bottom: 1px solid whitesmoke;
    padding-bottom: 15px;
    margin-top: 10px;
  }
  
  .tag-preffix {
    font-size: 18px !important;
    margin-top: 4px;
  }
  
  .clear-button {
    background: var(--intButton) !important;
  }
  
  .tag-clear-icon {
    font-size: 17px;
    padding-top: 3px;
    padding-left: 5px;
    cursor: pointer;
    color: grey;
  }
  
  .tag-text {
    font-size: 13px;
    font-weight: 500;
    color: grey;
    width: 6ch;
    height: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    text-wrap: nowrap;
  }
  
  .clear-button {
    background: var(--milestoneButton) !important;
  }
  
  .button {
    background: var(--milestoneButton) !important;
    color: white;
    font-weight: 500;
    width: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 5px;
    margin-top: 6px;
    height: 30px;
    font-size: 12px;
    cursor: pointer;
  }
  
  .filter-footer {
    height: 20px;
    display: flex;
    justify-content: right;
    padding-right: 15px;
    border-top: 1px solid whitesmoke;
    height: 60px;
  }
  
  .billing-advice-button {
    text-decoration: underline;
    font-size: 12px;
    font-family: var(--milestoneFont) !important;
    font-weight: 500;
  }
  
  /* Base styles for the toggle switch container */
  .toggle-switch {
    display: inline-block;
    position: relative;
    width: 30px;
    height: 17px;
    //background-color: #ccc;
    border-radius: 15px;
    cursor: pointer;
    margin-right: 10px !important;
  }
  
  .grayed-out-toggle {
    display: inline-block;
    position: relative;
    width: 30px;
    height: 17px;
    //background-color: #ccc;
    border-radius: 15px;
    background-color: grey;
    margin-right: 10px !important;
  }
  
  .column-config-popup {
    width: 210px;
    padding: 20px;
    height: 51vh;
    overflow: auto;
  }
  
  .auto-adapt {
    width: max-content;
    padding: 20px;
    height: auto;
  }
  
  /* Hide the default checkbox input */
  .toggle-switch input[type="checkbox"] {
    display: none;
  }
  
  /* Style for the slider (the rounded switch) */
  .slider {
    position: absolute;
    top: 2px;
    left: 2px;
    width: 12px;
    height: 12px;
    background-color: #fff;
    border-radius: 50%;
    transition: transform 0.3s;
  }
  
  /* Apply styles when the checkbox is checked */
  .toggle-switch input[type="checkbox"]:checked+.slider {
    transform: translateX(13px);
  }
  
  .toggle-switch input[type="checkbox"]:not(:checked)+.slider {
    transform: translateX(0);
  }
  
  .column-header {
    color: var(--blue-grey-80, #5F6C81);
    font-family: var(--milestoneFont) !important;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 16px;
    letter-spacing: 0.22px;
    text-transform: capitalize;
  }
  
  .column-list {
    justify-content: space-between;
    height: 28px;
  }

// .loader{
//     top: 45%;
//     position: absolute;
//     left: 50%;
//     bottom: 50%;
// }
