<div class="pm-bills" *ngIf="loading">
  <div class="loader-container">
    <mat-spinner class="green-spinner loader" diameter="40"></mat-spinner>
  </div>
</div>

<div class="pm-bills" *ngIf="!loading">

  <div class="container">

    <div class="header row">

      <div class="main-container">
      </div>

      <div class="icons">
        <div class="flex-container">
          <div class="overall-bills-section">
            <div class="overall-bills-section-header">
              {{('overall_bills_value' | checkLabel : this.formConfig: 'bills-landing-list': 'Bill Gross Value')}}
            </div>
            <app-currency [toDisplay]="projectValueAccess" [currencyList]="overallBillsValue" [code]="code"
              [font_size]="font_size_currency" class="flex-1" type="big"></app-currency>
          </div>
          <mat-divider class="divider" vertical></mat-divider>
          <div class="purchase-order">
            <div class="purchase-order-header">
              {{('po_value' | checkLabel : this.formConfig: 'bills-landing-list': 'Purchase Order Value')}}
            </div>
            <app-currency [toDisplay]="projectValueAccess" [currencyList]="item_value_json" [code]="code"
              [font_size]="font_size_currency" class="flex-1" type="big"></app-currency>
          </div>
        </div>
        <mat-icon [ngStyle]="{'margin-right':lumpsum_left}"
          style="margin-top: 11px;font-size: 18px;color: #7D838B;cursor: pointer;"
          *ngIf="billingAdviceAccess && lumpsumBillingAdviceProject" class="pending_actions" tooltip="Billing Advice"
          (click)="openLumpsumAdvise()">pending_actions</mat-icon>
        <div class="all" [ngStyle]="{'margin-left':left}" [matMenuTriggerFor]="menu">{{status_filter_name}} <mat-icon
            class="all-icon">keyboard_arrow_down</mat-icon>
        </div>
        <mat-menu #menu="matMenu" class="menu-overflow">
          <div *ngFor="let item of milestone_filter_status_list">
            <button *ngIf="item.id != -1" (click)="getStatusFilterData(item.id, item.name)" mat-menu-item
              class="font-family button-common button-active">
              <div class="status-dot" [ngStyle]="{'background': item.color}"></div>
              {{item.name}}
            </button>
            <button *ngIf="item.id == -1" (click)="getStatusFilterData(item.id, item.name)" mat-menu-item
              class="font-family button-common button-inactive">
              <div class="status-dot" [ngStyle]="{'background': item.color}"></div>
              {{item.name}}
            </button>
          </div>
        </mat-menu>
        <div tooltip="{{sort_tooltip}}">
          <mat-icon class="sort" (click)="openFilter()">sort</mat-icon>
          <mat-icon class="sort-icon" (click)="openFilter()" *ngIf="duedate">arrow_upward</mat-icon>
          <mat-icon class="sort-icon" (click)="openFilter()" *ngIf="!duedate">arrow_downward</mat-icon>
        </div>
        <mat-icon *ngIf="('create_bills' | checkActive : this.formConfig: 'bills-landing') && billWriteAccess" class="create" tooltip="{{('create_bills' | checkLabel : this.formConfig: 'bills-landing': 'Create Bills')}}" (click)="create()">add_box</mat-icon>
        <mat-icon class="edit" tooltip="Edit" *ngIf="check" (click)="openEdit()">edit</mat-icon>
        <mat-icon class="delete" tooltip="Delete" (click)="deleteBills()" *ngIf="check">delete_outline</mat-icon>
      </div>
    </div>

    <div class="bills-body">

      <div class="bill-body-total">

        <div class="bill-body-header" *ngIf="data.length>0">
          <div class="select-id-header" *ngIf="('bills_select' | checkActive : this.formConfig: 'bills-landing-list')">
          </div>
          <div class="id-header" *ngIf="('bill_id' | checkActive : this.formConfig: 'bills-landing-list')">
            {{('bill_id' | checkLabel : this.formConfig: 'bills-landing-list': 'Bill id')}}
          </div>
          <div class="name-header" *ngIf="('bill_name' | checkActive : this.formConfig: 'bills-landing-list')">
            {{('bill_name' | checkLabel : this.formConfig: 'bills-landing-list': 'Bill Name')}}
          </div>
          <div class="value-header" *ngIf="('bill_value' | checkActive : this.formConfig: 'bills-landing-list')">
            {{('bill_value' | checkLabel : this.formConfig: 'bills-landing-list': 'Bill Value')}}
          </div>
          <div class="status-header" *ngIf="('milestone_status' | checkActive : this.formConfig: 'bills-landing-list')">
            {{('milestone_status' | checkLabel : this.formConfig: 'bills-landing-list': 'Status')}}
          </div>
          <div class="startDate-header"
            *ngIf="('bill_startDate' | checkActive : this.formConfig: 'bills-landing-list')">
            {{('bill_startDate' | checkLabel : this.formConfig: 'bills-landing-list': 'Start Date')}}
          </div>
          <div class="endDate-header" *ngIf="('bill_endDate' | checkActive : this.formConfig: 'bills-landing-list')">
            {{('bill_endDate' | checkLabel : this.formConfig: 'bills-landing-list': 'Due Date')}}
          </div>
          <div class="actions-header" *ngIf="('actions-header' | checkActive : this.formConfig: 'bills-landing-list')">
            {{('actions-header' | checkLabel : this.formConfig: 'bills-landing-list': 'Actions')}}
          </div>
        </div>

        <div class="bill-body-list-outer"  *ngIf="data.length>0">

          <div class="bill-body-list" *ngFor="let p of data; let i=index">

            <div class="select-id-col" *ngIf="'bills_select' | checkActive : this.formConfig: 'bills-landing-list'">
              <mat-checkbox [checked]="p.selected" (change)="getSelectedRow(i, p.month_check)"> </mat-checkbox>
            </div>
            <div class="id-col" *ngIf="'bill_id' | checkActive : this.formConfig: 'bills-landing-list'">
              {{ p.id }}
            </div>
            <div class="name-col" *ngIf="'bill_name' | checkActive : this.formConfig: 'bills-landing-list'">
              <div tooltip="{{ p.label }}">{{ p.label | maxellipsis: 30 }}</div>
            </div>
            <div class="value-col" *ngIf="'bill_value' | checkActive : this.formConfig: 'bills-landing-list'">
              <app-currency [toDisplay]="projectValueAccess" [currencyList]="p.currencyListJSON" [code]="code"
                [font_size]="font_size_currency_card" type="big">
              </app-currency>
            </div>
            <div  class="status-col">
              <mat-chip *ngIf="'bill_status' | checkActive : this.formConfig: 'bills-landing-list'" class="status-chip"
                [ngStyle]="{'border': p.border, 'background': p.background}"
                (click)="togglePopover(p.financial_status_id, i)">
                <div class="status-text" [ngStyle]="{'color': p.color}">{{ p.financial_status }}</div>
              </mat-chip>
            </div>
            <div *ngIf="p.isPopoverOpen && divVisible" id="myDiv"
              [ngClass]="{'pop-up-container': (i !== 0 && i !== 1 && i !== 2), 'pop-up-container1': (i === 0 || i === 1 || i === 2)}">
              <div class="column-list">
                <label *ngFor="let column of status; let j=index" class="column-item">
                  <mat-chip class="status-chip"
                    [ngStyle]="{'border': column['border'], 'background': column['background']}"
                    (click)="statusChange(i, column['id'])">
                    <div class="status-text" [ngStyle]="{'color': column['color']}">{{ column['name'] }}</div>
                  </mat-chip>
                </label>
              </div>
            </div>
            <div class="startDate-col" *ngIf="'bill_startDate' | checkActive : this.formConfig: 'bills-landing-list'">
              {{ p.display_start_date }}
            </div>
            <div class="endDate-col" *ngIf="'bill_endDate' | checkActive : this.formConfig: 'bills-landing-list'">
              {{ p.display_end_date }}
            </div>
            <div class="actions-col">
              <div class="billing-class" *ngIf="'billing-advice' | checkActive : this.formConfig: 'bills-landing-list'">
                <span tooltip="{{('billing-advice' | checkLabel : this.formConfig: 'bills-landing-list': 'Billing Advice')}}" class="billing-text" (click)="openBillingAdvice(p.id)">{{('billing-advice' | checkLabel : this.formConfig: 'bills-landing-list': 'Billing Advice')}}</span>
              </div>
              <div  class="action-icons">
                <mat-icon *ngIf="'comments' | checkActive : this.formConfig: 'bills-landing-list'" class="actions-icon" [ngStyle]="{'color': p.color}"
                          tooltip="{{ 'comments' | checkTooltip : this.formConfig: 'bills-landing-list' : 'Comments' }}"
                          (click)="addNotes(i)">
                  chat_bubble_outline
                </mat-icon>
                <mat-icon *ngIf="'bills_attachments' | checkActive : this.formConfig: 'bills-landing-list'" class="actions-icon"
                          [ngStyle]="{'color': p.color, 'margin-top': '-3px'}"
                          tooltip="{{ 'bills_attachments' | checkTooltip : this.formConfig: 'bills-landing-list' : 'Attach files' }}"
                          (click)="addAttachment(p.id, p.gantt_id)">
                  attachment
                </mat-icon>
                <div *ngIf="p?.cancelButtonEnabled" class="actions-icon" 
                      tooltip="{{('cancel' | checkLabel : this.formConfig:'bills-landing-list': 'Cancel')}}" (click)="cancelMilestone(p)">
                      <svg width="18" height="18" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <mask id="mask0_20530_80140" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="16" height="16">
                        <rect width="16" height="16" fill="#D9D9D9"/>
                        </mask>
                        <g mask="url(#mask0_20530_80140)">
                        <path d="M6.46667 12.4668L5.53333 11.5335L7.06667 10.0002L5.53333 8.46683L6.46667 7.5335L8 9.06683L9.53333 7.5335L10.4667 8.46683L8.93333 10.0002L10.4667 11.5335L9.53333 12.4668L8 10.9335L6.46667 12.4668ZM3.33333 14.6668C2.96667 14.6668 2.65278 14.5363 2.39167 14.2752C2.13056 14.0141 2 13.7002 2 13.3335V4.00016C2 3.6335 2.13056 3.31961 2.39167 3.0585C2.65278 2.79738 2.96667 2.66683 3.33333 2.66683H4V1.3335H5.33333V2.66683H10.6667V1.3335H12V2.66683H12.6667C13.0333 2.66683 13.3472 2.79738 13.6083 3.0585C13.8694 3.31961 14 3.6335 14 4.00016V13.3335C14 13.7002 13.8694 14.0141 13.6083 14.2752C13.3472 14.5363 13.0333 14.6668 12.6667 14.6668H3.33333ZM3.33333 13.3335H12.6667V6.66683H3.33333V13.3335ZM3.33333 5.3335H12.6667V4.00016H3.33333V5.3335Z" fill="#45546E"/>
                        </g>
                      </svg>        
                </div>
                <div *ngIf="p?.reversalButtonEnabled" class="actions-icon"
                  tooltip="{{('revert' | checkLabel : this.formConfig:'bills-landing-list': 'Revert')}}" (click)="revertMilestone(p)">
                  <svg width="18" height="18" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M2.6407 1.95599C3.8504 0.907764 5.39802 0.331758 6.9987 0.333991C10.6807 0.333991 13.6654 3.31866 13.6654 7.00066C13.6654 8.42466 13.2187 9.74466 12.4587 10.8273L10.332 7.00066H12.332C12.3321 5.95507 12.0249 4.93253 11.4485 4.06016C10.8721 3.1878 10.052 2.5041 9.09015 2.09407C8.12832 1.68405 7.06717 1.56579 6.03867 1.754C5.01016 1.94221 4.05967 2.42859 3.30536 3.15266L2.6407 1.95599ZM11.3567 12.0453C10.147 13.0935 8.59937 13.6696 6.9987 13.6673C3.3167 13.6673 0.332031 10.6827 0.332031 7.00066C0.332031 5.57666 0.778698 4.25666 1.5387 3.17399L3.66536 7.00066H1.66536C1.66528 8.04624 1.97253 9.06879 2.54892 9.94115C3.12531 10.8135 3.94541 11.4972 4.90725 11.9072C5.86908 12.3173 6.93022 12.4355 7.95873 12.2473C8.98723 12.0591 9.93773 11.5727 10.692 10.8487L11.3567 12.0453Z" fill="#45546E"/>
                  </svg>
                </div>
                <div *ngIf="p?.creditNoteButtonEnabled" class="actions-icon"
                  tooltip="{{('credit_note' | checkLabel : this.formConfig:'bills-landing-list': 'Credit Note')}}"  (click)="raiseCreditNote(p)">
                  <svg width="18" height="18" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" *ngIf="!this.creditNoteSelected">
                    <path d="M13.3333 14.6673H4.33333C3.71449 14.6673 3.121 14.4215 2.68342 13.9839C2.24583 13.5463 2 12.9528 2 12.334V3.33398C2 2.80355 2.21071 2.29484 2.58579 1.91977C2.96086 1.5447 3.46957 1.33398 4 1.33398H13.3333C13.5101 1.33398 13.6797 1.40422 13.8047 1.52925C13.9298 1.65427 14 1.82384 14 2.00065V14.0007C14 14.1775 13.9298 14.347 13.8047 14.4721C13.6797 14.5971 13.5101 14.6673 13.3333 14.6673ZM12.6667 13.334V11.334H4.33333C4.06812 11.334 3.81376 11.4393 3.62623 11.6269C3.43869 11.8144 3.33333 12.0688 3.33333 12.334C3.33333 12.5992 3.43869 12.8536 3.62623 13.0411C3.81376 13.2286 4.06812 13.334 4.33333 13.334H12.6667ZM6.66667 2.66732V8.00065L9 6.66732L11.3333 8.00065V2.66732H6.66667Z" fill="#45546E"/>
                  </svg>
                </div>  
              </div>
            </div>
          
          </div>

        </div>

      </div>

    </div>

  </div>

  <div *ngIf="data.length==0">

    <div class="content">

      <div class="img">
        <img [src]="noDataImage ? noDataImage : 'https://assets.kebs.app/No-milestone-image.png'" class="img-data" />
      </div>

      <div class="inner-content pt-1">
        <div class="tittle pt-2">No Bills Here</div>
        <div class="description pt-2" *ngIf="('create_bills' | checkActive : this.formConfig: 'bills-landing') && billWriteAccess" >Start Adding Bills
        </div>
        <button *ngIf="('create_bills' | checkActive : this.formConfig: 'bills-landing') && billWriteAccess" mat-raised-button class="save-button" (click)="createBills()">Add
          Bill</button>
      </div>

    </div>
    
  </div>

</div>