<div class="milestone-creation">
    <div class="row header set">
        <div class="col-4"></div>
        <div class="header-title col-4 name">
            {{this.header_name}}
        </div>
        <div class="close">
            <mat-icon class="close-button" (click)="onCloseClickk()">clear</mat-icon>
        </div>
    </div>
    <div class="loader-container" *ngIf="loading">
        <mat-spinner class="green-spinner1" diameter="30"></mat-spinner>
    </div>
    <span *ngIf=!loading>
        <div class="body">
            <form style="margin-top: -19px;margin-left: 14px;" [formGroup]="stepperFormGroup">
                <div class="details">
                    <div class="col-12"
                        *ngIf="('milestone_name' | checkActive : this.formConfig: 'milestone-creation')">
                        <div class="content-title">
                            {{('milestone_name' | checkLabel : this.formConfig: 'milestone-creation': 'Milestone
                            Name')}}
                            <span class="required-star"
                                *ngIf="('milestone_name' | checkMandatedField : this.formConfig: 'milestone-creation')">*</span>
                            <span *ngIf="('milestone_name' | checkInfoIcon : this.formConfig: 'milestone-creation')">
                                <mat-icon class="info-icon"
                                    tooltip="{{('milestone_name' | checkTooltip : this.formConfig: 'milestone-creation': 'milestone name')}}">
                                    info_outline
                                </mat-icon>
                            </span>
                        </div>

                        <mat-form-field class="input-field" appearance="outline" [ngStyle]="{'background':!milestoneWriteAccess ? '#E8E9EE': '#FFFFFF'}">
                            <input matInput [maxlength]="(name_length ? name_length : 300)"
                                class="font-family"
                                style='font-size: 13px;font-weight: 400;line-height: 16px;letter-spacing: 0em;text-align: left;color: #8B95A5;'
                                placeholder="Enter here" formControlName="milestone_name"
                                [required]="('milestone_name' | checkMandatedField : this.formConfig: 'milestone-creation')"
                                [disabled]="!milestoneWriteAccess"
                                [readonly]="edit_data.financial_status_id==8"/>
                        </mat-form-field>

                    </div>
                </div>
                <div class="details-first-inner">
                    <div class="calender-icon">
                        <app-date-display *ngIf="showDisplay" class="row" [date]="this.date"
                            [color]="color"></app-date-display>
                    </div>
                    <div class="details-second-inner">
                        <div class="details-third-inner">
                            <div class="col-4"
                                *ngIf="('start_date' | checkActive : this.formConfig: 'milestone-creation')">
                                <div class="content-title">
                                    {{('start_date' | checkLabel : this.formConfig: 'milestone-creation': 'When is the start
                                    date?')}}
                                    <span class="required-star"
                                        *ngIf="('start_date' | checkMandatedField : this.formConfig: 'milestone-creation')">
                                        *</span>
                                    <span
                                        *ngIf="('start_date' | checkInfoIcon : this.formConfig: 'milestone-creation')">
                                        <mat-icon class="info-icon"
                                            tooltip="{{('start_date' | checkTooltip : this.formConfig: 'milestone-creation': 'Start Date')}}">
                                            info_outline
                                        </mat-icon>
                                    </span>
                                </div>

                                <mat-form-field class="input-field start_date" appearance="outline" [ngStyle]="{'background':!milestoneWriteAccess || !milestoneDetailsAccess ? '#E8E9EE': '#FFFFFF'}">

                                    <input matInput formControlName="startDate" class="font-family"
                                        style='font-size: 13px;font-weight: 400;line-height: 16px;letter-spacing: 0em;text-align: left;color: #8B95A5;'
                                        [matDatepicker]="psdDpp" name="startDate" [min]="project_start_date"
                                        [max]="compareDate(this.stepperFormGroup.get('endDate').value,project_end_date)"
                                        placeholder="DD-MMM-YYYY"
                                        [required]="('start_date' | checkMandatedField : this.formConfig: 'milestone-creation')"
                                        [disabled]="!milestoneWriteAccess || !milestoneDetailsAccess"/>
                                    <mat-datepicker-toggle matSuffix [for]="psdDpp"></mat-datepicker-toggle>
                                    <mat-datepicker #psdDpp></mat-datepicker>
                                </mat-form-field>
                            </div>
                            <div class="col-4" style="margin-left: -30px;"
                                *ngIf="('end_date' | checkActive : this.formConfig: 'milestone-creation')">
                                <div class="content-title">
                                    {{('end_date' | checkLabel : this.formConfig: 'milestone-creation': 'When Is It
                                    Due?')}}
                                    <span class="required-star"
                                        *ngIf="('end_date' | checkMandatedField : this.formConfig: 'milestone-creation')">
                                        *</span>
                                    <span *ngIf="('end_date' | checkInfoIcon : this.formConfig: 'milestone-creation')">
                                        <mat-icon class="info-icon"
                                            tooltip="{{('end_date' | checkTooltip : this.formConfig: 'milestone-creation': 'End Date')}}">
                                            info_outline
                                        </mat-icon>
                                    </span>
                                </div>

                                <mat-form-field class="input-field end_date" appearance="outline" [ngStyle]="{'background':!milestoneWriteAccess || !milestoneDetailsAccess ? '#E8E9EE': '#FFFFFF'}">

                                    <input matInput formControlName="endDate" class="font-family"
                                        style='font-size: 13px;font-weight: 400;line-height: 16px;letter-spacing: 0em;text-align: left;color: #8B95A5;'
                                        [matDatepicker]="psdDp" name="endDate" placeholder="DD-MMM-YYYY"
                                        (dateChange)="onInputChange($event)"
                                        [min]="compareDateMinimum(this.stepperFormGroup.get('startDate').value,project_start_date)"
                                        [max]="project_end_date"
                                        [required]="('end_date' | checkMandatedField : this.formConfig: 'milestone-creation')" 
                                        [disabled]="!milestoneWriteAccess || !milestoneDetailsAccess"/>
                                    <mat-datepicker-toggle matSuffix [for]="psdDp"></mat-datepicker-toggle>
                                    <mat-datepicker #psdDp></mat-datepicker>
                                </mat-form-field>
                            </div>
                            <div class="col-4" style="margin-left: -31px;"
                                *ngIf="('owner' | checkActive : this.formConfig: 'milestone-creation')">
                                <div class="content-title">
                                    {{('owner' | checkLabel : this.formConfig: 'milestone-creation': 'Who Is
                                    Responsible?')}}
                                    <span class="required-star"
                                        *ngIf="('owner' | checkMandatedField : this.formConfig: 'milestone-creation')">
                                        *</span>
                                    <span *ngIf="('owner' | checkInfoIcon : this.formConfig: 'milestone-creation')">
                                        <mat-icon class="info-icon"
                                            tooltip="{{('owner' | checkTooltip : this.formConfig: 'milestone-creation': 'owner')}}">
                                            info_outline
                                        </mat-icon>
                                    </span>
                                </div>
                                <app-search-user-e360 class="owner font-family" [isAutocomplete]="true"
                                    style='font-size: 13px;font-weight: 400;line-height: 16px;letter-spacing: 0em;text-align: left;color: #8B95A5;'
                                    formControlName="employee_name" [label]="'Search for member'"
                                    [ngStyle]="{'background':!milestoneWriteAccess ? '#E8E9EE': '#FFFFFF'}"
                                    [required]="('owner' | checkMandatedField : this.formConfig: 'milestone-creation')"
                                    [disabled]="!milestoneWriteAccess "></app-search-user-e360>
                            </div>
                            <div class="wp-bottom"
                                *ngIf="('notify' | checkActive : this.formConfig: 'milestone-creation')">
                                <mat-checkbox
                                    *ngIf="('notify_bot' | checkActive : this.formConfig: 'milestone-creation')"
                                    [color]="billableColor" [ngModelOptions]="{standalone: true}" [(ngModel)]="isBot"
                                    [disabled]="!milestoneWriteAccess"
                                    class="check-box-remain">{{('notify_bot' | checkLabel : this.formConfig:
                                    'milestone-creation': 'Send Reminder?')}} </mat-checkbox>
                                <mat-checkbox
                                    *ngIf="('notify_email' | checkActive : this.formConfig: 'milestone-creation')"
                                    [color]="billableColor" [ngModelOptions]="{standalone: true}" [(ngModel)]="isEmail"
                                    [disabled]="!milestoneWriteAccess"
                                    class="check-box-notify">{{('notify_email' | checkLabel : this.formConfig:
                                    'milestone-creation': 'Notify By Email?')}}</mat-checkbox>
                            </div>
                            <div class="col-4" style="margin-left: -31px;"
                                *ngIf="('invoice_date' | checkActive : this.formConfig: 'milestone-creation')">
                                <div class="content-title">
                                    {{('invoice_date' | checkLabel : this.formConfig: 'milestone-creation': 'Invoice Date')}}
                                    <span class="required-star"
                                        *ngIf="('invoice_date' | checkMandatedField : this.formConfig: 'milestone-creation')">
                                        *</span>
                                    <span *ngIf="('invoice_date' | checkInfoIcon : this.formConfig: 'milestone-creation')">
                                        <mat-icon class="info-icon"
                                            tooltip="{{('invoice_date' | checkTooltip : this.formConfig: 'milestone-creation': 'Invoice Date')}}">
                                            info_outline
                                        </mat-icon>
                                    </span>
                                </div>

                                <mat-form-field class="input-field end_date" style="width: 216px;" appearance="outline"  [ngStyle]="{'background':!milestoneWriteAccess ? '#E8E9EE': '#FFFFFF'}">

                                    <input matInput formControlName="invoice_date" class="font-family"
                                        style='font-size: 13px;font-weight: 400;line-height: 16px;letter-spacing: 0em;text-align: left;color: #8B95A5;'
                                        [matDatepicker]="psdDppp" name="invoice_date" placeholder="DD-MMM-YYYY" [min]="minDate" [max]="maxDate"
                                       
                                        [required]="('invoice_date' | checkMandatedField : this.formConfig: 'milestone-creation')"
                                        [disabled]="!milestoneWriteAccess"/>
                                    <mat-datepicker-toggle matSuffix [for]="psdDppp"></mat-datepicker-toggle>
                                    <mat-datepicker #psdDppp></mat-datepicker>
                                </mat-form-field>
                            </div>
                        </div>
                        <div class="details-fourth-inner">
                            <div class="col-5" *ngIf="('value' | checkActive : this.formConfig: 'milestone-creation')">
                                <div class="content-title">
                                    {{('value' | checkLabel : this.formConfig: 'milestone-creation': 'Milestone Gross
                                    Value')}} ({{this.code}})
                                    <span class="required-star"
                                        *ngIf="('value' | checkMandatedField : this.formConfig: 'milestone-creation')">*</span>
                                    <span *ngIf="('value' | checkInfoIcon : this.formConfig: 'milestone-creation')">
                                        <mat-icon class="info-icon"
                                            tooltip="{{('value' | checkTooltip : this.formConfig: 'milestone-creation': 'value')}}">
                                            info_outline
                                        </mat-icon>
                                    </span>
                                </div>

                                <mat-form-field class="input-field value" appearance="outline" [ngStyle]="{'background':!milestoneWriteAccess || !milestoneDetailsAccess ? '#E8E9EE': '#FFFFFF'}">
                                    <input matInput type="text" digitOnly [isPercentage]="false" [allowDecimal]="true" [digitsAllowed]="20" [readonly]="!milestoneWriteAccess || !milestoneDetailsAccess"
                                        style='font-size: 13px;font-weight: 400;line-height: 16px;letter-spacing: 0em;text-align: left;color: #8B95A5;'
                                        placeholder="Enter here" class="no-updown-arrows font-family" formControlName="milestone_value"
                                        [required]="('value' | checkMandatedField : this.formConfig: 'milestone-creation')">
                                 
                                </mat-form-field>

                            </div>
                            <div class="col-5" style="margin-left: 32px;"
                                *ngIf="('weighted_percentage' | checkActive : this.formConfig: 'milestone-creation')">
                                <div class="content-title">
                                    {{('weighted_percentage' | checkLabel : this.formConfig: 'milestone-creation':
                                    'Weighted
                                    Percentage')}}
                                    <span class="required-star"
                                        *ngIf="('weighted_percentage' | checkMandatedField : this.formConfig: 'milestone-creation')">
                                        *</span>
                                    <span
                                        *ngIf="('weighted_percentage' | checkInfoIcon : this.formConfig: 'milestone-creation')">
                                        <mat-icon class="info-icon"
                                            tooltip="{{('weighted_percentage' | checkTooltip : this.formConfig: 'milestone-creation': 'percentage')}}">
                                            info_outline
                                        </mat-icon>
                                    </span>
                                </div>

                                <mat-form-field class="input-field percentage" appearance="outline"
                                    [ngStyle]="{'background-color':percentage_disable_color}">
                                    <input matInput type="text" digitOnly [isPercentage]="true" [allowDecimal]="true"
                                        [readonly]="percentage_disable"
                                        style='font-size: 13px;font-weight: 400;line-height: 16px;letter-spacing: 0em;text-align: left;color: #8B95A5;'
                                        class="no-updown-arrows font-family"
                                        formControlName="weighted_percentage"
                                        [required]="('weighted_percentage' | checkMandatedField : this.formConfig: 'milestone-creation')">
                                </mat-form-field>

                            </div>

                        </div>
                    </div>

                </div>
                <div class="row col-12" style="margin-top: 25px;">
                    <div class="tab-fix">
                        <mat-tab-group>
                            <mat-tab *ngIf="('po_info' | checkActive : this.formConfig: 'milestone-creation')"
                                label=" {{('po_info' | checkLabel : this.formConfig: 'milestone-creation': 'Financials')}}">
                                <div class="col-12 po-details">
                                    <div class="first-row row">
                                        <div class="col-3" *ngIf="('quote_id' | checkActive : this.formConfig: 'milestone-creation') && withOpportunity==1">
                                            <div class="content-title">
                                                {{('quote_id' | checkLabel : this.formConfig: 'milestone-creation': 'Quote ID')}}
                                                <span class="required-star"
                                                *ngIf="('quote_id' | checkMandatedField : this.formConfig: 'milestone-creation')  && withOpportunity==1">
                                                *</span>
                                                <span *ngIf="('quote_id' | checkInfoIcon : this.formConfig: 'milestone-creation')">
                                                    <mat-icon class="info-icon"
                                                    tooltip="{{('quote_id' | checkTooltip : this.formConfig: 'milestone-creation': 'quote_id')}}"
                                                    >
                                                        info_outline
                                                    </mat-icon>
                                                </span>
                                            </div>
                                            <app-input-search-name [showSelect]="false" class="input-field poNumber" [list]="quote_id_list" placeholder="Select"
                                                [disabled]="!milestoneDetailsAccess"
                                                formControlName="quote_id">
                                            </app-input-search-name>
                                        </div> 
                                        <div class="col-3"
                                            *ngIf="('po_value' | checkActive : this.formConfig: 'milestone-creation')">
                                            <div class="content-title">
                                                {{('po_value' | checkLabel : this.formConfig: 'milestone-creation':
                                                'Order Value')}}
                                                <span class="required-star"
                                                    *ngIf="('po_value' | checkMandatedField : this.formConfig: 'milestone-creation')">
                                                    &nbsp;*</span>
                                                <span
                                                    *ngIf="('po_value' | checkInfoIcon : this.formConfig: 'milestone-creation')">
                                                    <mat-icon class="info-icon"
                                                        matTooltip="{{('po_value' | checkTooltip : this.formConfig: 'milestone-creation': 'Order Value')}}"
                                                        matTooltipClass="primary-tooltip" matTooltipPosition="above">
                                                        info_outline
                                                    </mat-icon>
                                                </span>
                                            </div>
                                            <!-- <div class="row"> -->
                                            <mat-form-field class="input-field poValue" appearance="outline"  [ngStyle]="{'background':(!milestoneWriteAccess || (('po_data_disable' | checkActive : this.formConfig: 'milestone-creation') && withOpportunity==1)) ? '#E8E9EE': '#FFFFFF'}">
                                                <input class="font-family" style="color: #45546E;" type="number"
                                                    digitOnly [isPercentage]="false" [allowDecimal]="true"
                                                    [digitsAllowed]="8"
                                                    [required]="('po_value' | checkMandatedField : this.formConfig: 'milestone-creation')"
                                                    matInput placeholder="Enter here" formControlName="po_value" [readonly]="(!milestoneWriteAccess || (('po_data_disable' | checkActive : this.formConfig: 'milestone-creation') && withOpportunity==1))" />
                                            </mat-form-field>
                                        </div>
                                        <div class="col-3"
                                            *ngIf="('milestone_type' | checkActive : this.formConfig: 'milestone-creation')">
                                            <div class="content-title">
                                                {{('milestone_type' | checkLabel : this.formConfig:
                                                'milestone-creation': 'Milestone Type')}}
                                                <span class="required-star"
                                                    *ngIf="('milestone_type' | checkMandatedField : this.formConfig: 'milestone-creation')">
                                                    *</span>
                                                <span
                                                    *ngIf="('milestone_type' | checkInfoIcon : this.formConfig: 'milestone-creation')">
                                                    <mat-icon class="info-icon"
                                                        tooltip="{{('milestone_type' | checkTooltip : this.formConfig: 'milestone-creation': 'milestone_type')}}">
                                                        info_outline
                                                    </mat-icon>
                                                </span>
                                            </div>
                                            <!-- <div class="row"> -->
                                            <app-input-search-name [showSelect]="false" class="milestone_type" [list]="milestoneTypeList"
                                            [ngStyle]="{'background':(!milestoneWriteAccess  || this.mode =='Edit') ? '#E8E9EE': '#FFFFFF'}"
                                                [required]="('milestone_type' | checkMandatedField : this.formConfig: 'milestone-creation')"
                                                 placeholder="Select One" formControlName="milestone_type" [disabled]="(!milestoneWriteAccess || !milestoneDetailsAccess) || (this.mode =='Edit')">
                                            </app-input-search-name>
                                            <!-- </div> -->

                                        </div>
                                        <div class="col-5" *ngIf="('change_milestone_type' | checkActive : this.formConfig: 'milestone-creation') && (mode =='Edit' && (this.stepperFormGroup.get('milestone_type').value == 4 || this.stepperFormGroup.get('milestone_type').value == 2) && this.changeMilestoneTypeMatrix)">
                                            <div class="content-title"> 
                                                {{('change_milestone_type' | checkLabel : this.formConfig: 'milestone-creation': 'Change Milestone Type')}}
                                                <span class="required-star" *ngIf="('change_milestone_type' | checkMandatedField : this.formConfig: 'milestone-creation')"> *</span>
                                                <span
                                                    *ngIf="('change_milestone_type' | checkInfoIcon : this.formConfig: 'milestone-creation')">
                                                    <mat-icon class="info-icon"
                                                        tooltip="{{('change_milestone_type' | checkTooltip : this.formConfig: 'milestone-creation': 'Change Milestone Type')}}">
                                                        info_outline
                                                    </mat-icon>
                                                </span>
                                            </div>

                                            <span class="header-value">
                                                <div class="sync-class pt-3 pl-5">
                                                    <span *ngIf="!isChangeMilestoneType" (click)="changeMilestoneType()">
                                                        <mat-icon class="sync-icn">sync</mat-icon>
                                                    </span>
                                                    <span *ngIf="isChangeMilestoneType" >
                                                        <img src="https://assets.kebs.app/round_off_sync_gif.gif" class="gif-class" />
                                                    </span>
                                                </div>
                                            </span>
                                          
                                            

                                        </div>
                                        <div class="col-5"
                                        *ngIf="('milestone_group' | checkActive : this.formConfig: 'milestone-creation') && this.enableMilestoneGroup">
                                        <div class="content-title">
                                            {{('milestone_group' | checkLabel : this.formConfig:
                                            'milestone-creation': 'Milestone Group')}}
                                            <span class="required-star"
                                                *ngIf="('milestone_group' | checkMandatedField : this.formConfig: 'milestone-creation')">
                                                *</span>
                                            <span
                                                *ngIf="('milestone_group' | checkInfoIcon : this.formConfig: 'milestone-creation')">
                                                <mat-icon class="info-icon"
                                                    tooltip="{{('milestone_group' | checkTooltip : this.formConfig: 'milestone-creation': 'milestone_group')}}">
                                                    info_outline
                                                </mat-icon>
                                            </span>
                                        </div>
                                        <!-- <div class="row"> -->
                                        <app-multi-select-search3
                                        class="milestone_group" 
                                        [list]="milestoneGroupList"
                                        formControlName='milestone_group'
                                        [required]="('milestone_group' | checkMandatedField : this.formConfig: 'milestone_group')"
                                        [disabled]="!milestoneWriteAccess"
                                        placeholder="Select">
                                    </app-multi-select-search3>
                                        <!-- </div> -->
                                    </div>
                                    </div>

                                    <div class="second-row row" style="margin-top: -10px;">
                                        <div class="col-3"
                                            *ngIf="('po_number' | checkActive : this.formConfig: 'milestone-creation')">
                                            <div class="content-title">
                                                {{('po_number' | checkLabel : this.formConfig: 'milestone-creation': 'PO
                                                Number')}}
                                                <span class="required-star"
                                                    *ngIf="('po_number' | checkMandatedField : this.formConfig: 'milestone-creation')">
                                                    *</span>
                                                <span
                                                    *ngIf="('po_number' | checkInfoIcon : this.formConfig: 'milestone-creation')">
                                                    <mat-icon class="info-icon"
                                                        tooltip="{{('po_number' | checkTooltip : this.formConfig: 'milestone-creation': 'po number')}}">
                                                        info_outline
                                                    </mat-icon>
                                                </span>
                                            </div>
                                            <mat-form-field *ngIf="!po_number_dropdown_access" class="input-field poNumber" appearance="outline"  [ngStyle]="{'background':(!milestoneWriteAccess || (('po_data_disable' | checkActive : this.formConfig: 'milestone-creation') && withOpportunity==1))  ? '#E8E9EE': '#FFFFFF'}">
                                                <input style="color: #45546E;" matInput class="font-family"
                                                    placeholder="Enter here" formControlName="po_number"
                                                    [required]="('po_number' | checkMandatedField : this.formConfig: 'milestone-creation')"
                                                    [readonly]="(!milestoneWriteAccess || (('po_data_disable' | checkActive : this.formConfig: 'milestone-creation') && withOpportunity==1))">
                                            </mat-form-field>
                                            <app-input-search-name [showSelect]="false" *ngIf="po_number_dropdown_access" [ngStyle]="{'background-color':(!milestoneWriteAccess || (('po_data_disable' | checkActive : this.formConfig: 'milestone-creation') && withOpportunity==1)) ? '#E8E9EE' : '#FFFFFF'}"  class="milestone_type" [list]="po_number_dropdown_list"
                                                [required]="('po_number' | checkMandatedField : this.formConfig: 'milestone-creation')"
                                                [disabled]="(!milestoneWriteAccess || (('po_data_disable' | checkActive : this.formConfig: 'milestone-creation') && withOpportunity==1))"
                                                placeholder="Select One" formControlName="po_number">
                                            </app-input-search-name>
                                        </div>
                                        <div class="col-3"
                                        *ngIf="('po_number_drop_down' | checkActive : this.formConfig: 'milestone-creation')">
                                        <div class="content-title">
                                            {{('po_number_drop_down' | checkLabel : this.formConfig: 'milestone-creation': 'PO
                                            Number')}}
                                            <span class="required-star"
                                                *ngIf="('po_number_drop_down' | checkMandatedField : this.formConfig: 'milestone-creation')">
                                                *</span>
                                            <span
                                                *ngIf="('po_number_drop_down' | checkInfoIcon : this.formConfig: 'milestone-creation')">
                                                <mat-icon class="info-icon"
                                                    tooltip="{{('po_number_drop_down' | checkTooltip : this.formConfig: 'milestone-creation': 'po number')}}">
                                                    info_outline
                                                </mat-icon>
                                            </span>
                                        </div>
                                        <app-input-search-name [showSelect]="false" [ngStyle]="{'background-color':(!milestoneWriteAccess || (('po_data_disable' | checkActive : this.formConfig: 'milestone-creation') && withOpportunity==1)) ? '#E8E9EE' : '#FFFFFF'}"  class="milestone_type" [list]="po_number_dropdown_list"
                                            [required]="('po_number_drop_down' | checkMandatedField : this.formConfig: 'milestone-creation')"
                                            [disabled]="(!milestoneWriteAccess || (('po_data_disable' | checkActive : this.formConfig: 'milestone-creation') && withOpportunity==1))"
                                            placeholder="Select One" formControlName="po_number">
                                        </app-input-search-name>
                                        </div>
                                        <div class="col-3"
                                            *ngIf="('po_date' | checkActive : this.formConfig: 'milestone-creation') && restriction">
                                            <div class="content-title">
                                                {{('po_date' | checkLabel : this.formConfig: 'milestone-creation': 'Po
                                                Date')}}
                                                <span class="required-star"
                                                    *ngIf="('po_date' | checkMandatedField : this.formConfig: 'milestone-creation')">
                                                    *</span>
                                                <span
                                                    *ngIf="('po_date' | checkInfoIcon : this.formConfig: 'milestone-creation')">
                                                    <mat-icon class="info-icon"
                                                        tooltip="{{('po_date' | checkTooltip : this.formConfig: 'milestone-creation': 'Po Date')}}">
                                                        info_outline
                                                    </mat-icon>
                                                </span>
                                            </div>
                                            <!-- <div class="row"> -->
                                            <mat-form-field class="input-field po_date" appearance="outline"  [ngStyle]="{'background':(!milestoneWriteAccess || (('po_data_disable' | checkActive : this.formConfig: 'milestone-creation') && withOpportunity==1))  ? '#E8E9EE': '#FFFFFF'}">

                                                <input class="font-family" style="color: #45546E;"
                                                    [required]="('po_date' | checkMandatedField : this.formConfig: 'milestone-creation')"
                                                    matInput formControlName="po_date" [matDatepicker]="psdDp" [min]="project_start_date"
                                                    [max]="compareDate(this.stepperFormGroup.get('endDate').value,project_end_date)"
                                                    name="poDate" placeholder="DD-MMM-YYYY" [disabled]="(!milestoneWriteAccess || (('po_data_disable' | checkActive : this.formConfig: 'milestone-creation') && withOpportunity==1))" />
                                                <mat-datepicker-toggle matSuffix [for]="psdDp"></mat-datepicker-toggle>
                                                <mat-datepicker #psdDp></mat-datepicker>
                                            </mat-form-field>
                                        </div>
                                        <div class="col-3"
                                            *ngIf="('po_date' | checkActive : this.formConfig: 'milestone-creation') && !restriction">
                                            <div class="content-title">
                                                {{('po_date' | checkLabel : this.formConfig: 'milestone-creation': 'Po
                                                Date')}}
                                                <span class="required-star"
                                                    *ngIf="('po_date' | checkMandatedField : this.formConfig: 'milestone-creation')">
                                                    *</span>
                                                <span
                                                    *ngIf="('po_date' | checkInfoIcon : this.formConfig: 'milestone-creation')">
                                                    <mat-icon class="info-icon"
                                                        tooltip="{{('po_date' | checkTooltip : this.formConfig: 'milestone-creation': 'Po Date')}}">
                                                        info_outline
                                                    </mat-icon>
                                                </span>
                                            </div>
                                            <!-- <div class="row"> -->
                                            <mat-form-field class="input-field po_date" appearance="outline"  [ngStyle]="{'background':(!milestoneWriteAccess || (('po_data_disable' | checkActive : this.formConfig: 'milestone-creation') && withOpportunity==1))  ? '#E8E9EE': '#FFFFFF'}">

                                                <input class="font-family" style="color: #45546E;"
                                                    [required]="('po_date' | checkMandatedField : this.formConfig: 'milestone-creation')"
                                                    matInput formControlName="po_date" [matDatepicker]="psdDp" 
                                                    name="poDate" placeholder="DD-MMM-YYYY" [disabled]="(!milestoneWriteAccess || (('po_data_disable' | checkActive : this.formConfig: 'milestone-creation') && withOpportunity==1))" />
                                                <mat-datepicker-toggle matSuffix [for]="psdDp"></mat-datepicker-toggle>
                                                <mat-datepicker #psdDp></mat-datepicker>
                                            </mat-form-field>
                                        </div>
                                        <div class="col-3"
                                            *ngIf="('payment_terms' | checkActive : this.formConfig: 'milestone-creation')">
                                            <div class="content-title">
                                                {{('payment_terms' | checkLabel : this.formConfig: 'milestone-creation':
                                                'Payment Terms')}}
                                                <span class="required-star"
                                                    *ngIf="('payment_terms' | checkMandatedField : this.formConfig: 'milestone-creation')">
                                                    *</span>
                                                <span
                                                    *ngIf="('payment_terms' | checkInfoIcon : this.formConfig: 'milestone-creation')">
                                                    <mat-icon class="info-icon"
                                                        tooltip="{{('payment_terms' | checkTooltip : this.formConfig: 'milestone-creation': 'Payment Terms')}}">
                                                        info_outline
                                                    </mat-icon>
                                                </span>
                                            </div>
                                            <!-- <div class="row"> -->
                                            <app-input-search-name [showSelect]="false" class="paymentTerms" [list]="payment_list"
                                            [ngStyle]="{'background':(!milestoneWriteAccess || (('po_data_disable' | checkActive : this.formConfig: 'milestone-creation') && withOpportunity==1))  ? '#E8E9EE': '#FFFFFF'}"
                                                [required]="('payment_terms' | checkMandatedField : this.formConfig: 'milestone-creation')"
                                                placeholder="Select One" formControlName="payment_terms" [disabled]="(!milestoneWriteAccess || (('po_data_disable' | checkActive : this.formConfig: 'milestone-creation') && withOpportunity==1))">
                                            </app-input-search-name>
                                            <!-- </div> -->
                                        </div>
                                        <div class="col-3"
                                            *ngIf="('partner' | checkActive : this.formConfig: 'milestone-creation')">
                                            <div class="content-title">
                                                {{('partner' | checkLabel : this.formConfig: 'milestone-creation':
                                                'Partner')}}
                                                <span class="required-star"
                                                    *ngIf="('partner' | checkMandatedField : this.formConfig: 'milestone-creation')">
                                                    *</span>
                                                <span
                                                    *ngIf="('partner' | checkInfoIcon : this.formConfig: 'milestone-creation')">
                                                    <mat-icon class="info-icon"
                                                        tooltip="{{('partner' | checkTooltip : this.formConfig: 'milestone-creation': 'Partner')}}">
                                                        info_outline
                                                    </mat-icon>
                                                </span>
                                            </div>
                                            <!-- <div class="row"> -->
                                            <app-input-search-name [showSelect]="false" class="partner" [list]="customer_list"
                                            [ngStyle]="{'background':!milestoneWriteAccess  ? '#E8E9EE': '#FFFFFF'}"
                                                [required]="('partner' | checkMandatedField : this.formConfig: 'milestone-creation')"
                                                placeholder="Select One" formControlName="partner" [disabled]="!milestoneWriteAccess ">
                                            </app-input-search-name>
                                            <!-- </div> -->
                                        </div>
                                        <div class="col-3" *ngIf="('po_reference' | checkActive : this.formConfig: 'milestone-creation')">
                                            <div class="content-title">
                                                {{('po_reference' | checkLabel : this.formConfig: 'milestone-creation':
                                                'PO Reference')}}
                                                <span class="required-star"
                                                    *ngIf="('po_reference' | checkMandatedField : this.formConfig: 'milestone-creation')">
                                                    &nbsp;*</span>
                                                <span
                                                    *ngIf="('po_reference' | checkInfoIcon : this.formConfig: 'milestone-creation')">
                                                    <mat-icon class="info-icon"
                                                        tooltip="{{('po_reference' | checkTooltip : this.formConfig: 'milestone-creation': 'po reference')}}">
                                                        info_outline
                                                    </mat-icon>
                                                </span>
                                            </div>
                                            <!-- <div class="row"> -->
                                            <mat-form-field class="input-field poReference" appearance="outline">
                                                <input class="font-family" style="color: #45546E;"
                                                    [required]="('po_reference' | checkMandatedField : this.formConfig: 'milestone-creation')"
                                                    [disabled]="!milestoneWriteAccess"
                                                    matInput placeholder="Enter here" formControlName="po_reference" />
                                            </mat-form-field>
                                        </div>

                                    </div>
                                </div>
                            </mat-tab>
                            <mat-tab *ngIf="('description' | checkActive : this.formConfig: 'milestone-creation')"
                                label=" {{('description' | checkLabel : this.formConfig: 'milestone-creation': 'Description')}}">
                                <div class="row">
                                    <div class="col-12 pr-0" style="margin-top:10px;overflow: auto;height: 125px;">
                                        <dx-html-editor class="dev-extreme-styles" (valueType)='valueType'
                                            formControlName="longDescription" (valueChange)="valueChange($event)"
                                            placeholder="Description about the milestone">
                                            <dxo-toolbar>
                                                <dxi-item name="bold"></dxi-item>
                                                <dxi-item name="italic"></dxi-item>
                                                <dxi-item name="strike"></dxi-item>
                                                <dxi-item name="underline"></dxi-item>
                                                <dxi-item name="separator"></dxi-item>
                                                <dxi-item name="alignLeft"></dxi-item>
                                                <dxi-item name="alignCenter"></dxi-item>
                                                <dxi-item name="alignRight"></dxi-item>
                                                <dxi-item name="alignJustify"></dxi-item>
                                                <dxi-item name="separator"></dxi-item>
                                                <dxi-item name="orderedList"></dxi-item>
                                                <dxi-item name="bulletList"></dxi-item>

                                            </dxo-toolbar>
                                        </dx-html-editor>
                                    </div>
                                </div>
                            </mat-tab>
                            <mat-tab *ngIf="('privacy' | checkActive : this.formConfig: 'milestone-creation')"
                                label=" {{('privacy' | checkLabel : this.formConfig: 'milestone-creation': 'Privacy & Followers')}}">
                                <div class="privacy">
                                    <div class="privacy-text1">
                                        Who can see it?
                                    </div>
                                    <div class="privacy-button1">
                                        <div class="privacy-button1-text"><mat-icon
                                                class="privacy-button1-icon">lock</mat-icon>
                                            Everybody on the project</div>
                                    </div>

                                </div>
                            </mat-tab>
                            <mat-tab *ngIf="('tags' | checkActive : this.formConfig: 'milestone-creation')"
                                label=" {{('tags' | checkLabel : this.formConfig: 'milestone-creation': 'Tags')}}">
                                <div id="mytagDiv" style="width: 10px;">
                                    <div *ngIf="displayTag.length==0"
                                        style="cursor: pointer;margin-left:-35px;margin-top: 10px;width: 118px; height: 28px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex;"
                                        (click)="addTag()">

                                        <div
                                            style="padding: 2px; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
                                            <div
                                                style="justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
                                                <div style="width: 24px; height: 24px;"></div>
                                                <div style="width: 13px; height: 13px;"><mat-icon
                                                        style=" color: #526179;font-size: 24px;">add_circle_outline</mat-icon>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="font-family"
                                            style="color: #A8ACB2; font-size: 12px; font-weight: 500; text-transform: capitalize; line-height: 24px; letter-spacing: 0.28px; word-wrap: break-word">
                                            Add tag</div>

                                    </div>
                                    <div *ngIf="isPopupVisible"
                                        style="width: 166px;margin-top: -228px;height: 200px;padding-bottom: 0px !important;padding: 16px; background: white; box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25); border-radius: 4px; border: 1px #E8E9EE solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 8px; display: inline-flex;position: fixed;">
                                        <div
                                            style="justify-content: flex-start; align-items: center; gap: 2px; display: inline-flex">
                                            <div style="width: 14px; height: 14px; position: relative">
                                                <div
                                                    style="width: 14px; height: 14px; left: 0px; top: 0px; position: absolute;">
                                                </div>
                                                <div
                                                    style="width: 11.07px; height: 11.07px; left: 1.46px; top: 1.46px; position: absolute;">
                                                    <mat-icon
                                                        style="color: #7D838B;font-size: 17px;top: 10px;margin-top: -10px;margin-left: -5px;">
                                                        sell
                                                    </mat-icon></div>
                                            </div>
                                            <!-- <div style="color: #7D838B; font-size: 11px; font-family: Roboto; font-weight: 400; text-transform: capitalize; line-height: 16px; letter-spacing: 0.22px; word-wrap: break-word"> Add Tag..</div> -->
                                            <input style="border: none;width: 130px;outline: none; font-size: 12px;"
                                                minlength='3' maxlength="30" formControlName="textInputControl"
                                                type="text" [placeholder]="placeholder"
                                                (keyup.enter)="performEnter()" />
                                        </div>
                                        <div style="align-self: stretch; height: 0px; border: 1px #E8E9EE solid"></div>
                                        <div
                                            style="align-self: stretch; justify-content: flex-start; align-items: flex-start; gap: 8px; display: inline-flex">
                                            <div class="font-family"
                                                style="color: #B9C0CA; font-size: 11px; font-weight: 400; text-transform: capitalize; line-height: 16px; letter-spacing: 0.22px; word-wrap: break-word">
                                                All projects</div>
                                            <div style="width: 16px; height: 16px; position: relative">
                                                <div
                                                    style="width: 16px; height: 16px; left: 0px; top: 0px; position: absolute;">
                                                </div>
                                                <div
                                                    style="width: 13.33px; height: 13.33px; left: 1.33px; top: 1.33px; position: absolute;">
                                                    <mat-icon style="color: #B9C0CA;font-size: 15px;"
                                                        tooltip="{{tooltip}}">info_outline</mat-icon></div>
                                            </div>
                                        </div>
                                        <div style="height: 700px;
                             overflow-y: auto;
                             overflow-x: hidden;
                             width: 149px;">
                                            <div *ngFor="let p of tags;let i=index" style="padding-bottom: 5px;">
                                                <div (click)="getTag(p.id)"
                                                    style="padding-left: 8px;cursor: pointer; padding-right: 8px; background: #F6F6F6; border-radius: 4px; border: 1px #E8E9EE solid; justify-content: flex-start; align-items: center; gap: 4px; display: inline-flex">
                                                    <div style="width: 4px; height: 4px; border-radius: 9999px"
                                                        [ngStyle]="{'background':p.color}"></div>
                                                    <div *ngIf="18>p.name.length" tooltip="{{p.name}}" class="font-family"
                                                        style="color: #7D838B; font-size: 11px; font-weight: 400; line-height: 16px; word-wrap: break-word">
                                                        {{p.name}}</div>
                                                    <div *ngIf="p.name.length>=18" tooltip="{{p.name}}" class="font-family"
                                                        style="color: #7D838B;font-size: 11px;font-weight: 400;line-height: 16px;/* word-wrap: break-word; */overflow: hidden;text-overflow: ellipsis;width: 105px;white-space: nowrap;">
                                                        {{p.name}}</div>
                                                </div>

                                            </div>
                                        </div>
                                    </div>
                                    <div *ngIf="isNewTagVisible && add"
                                        style="width: 180px;margin-top: 35px; height: 100%; padding: 16px; background: white; box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25); border-radius: 4px; border: 1px #E8E9EE solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 8px; display: inline-flex">
                                        <div
                                            style="justify-content: flex-start; align-items: center; gap: 2px; display: inline-flex">
                                            <mat-icon id="mytagDiv" class="close-button"
                                                (click)="onCloseClick()">clear</mat-icon>
                                            <div style="width: 14px; height: 14px; position: relative">
                                                <div
                                                    style="width: 14px; height: 14px; left: 0px; top: 0px; position: absolute;">
                                                </div>
                                                <div
                                                    style="width: 11.07px; height: 11.07px; left: 1.46px; top: 1.46px; position: absolute;">
                                                    <mat-icon
                                                        style="color: #7D838B;font-size: 18px;top: 10px;margin-top: -3px;margin-left: -9px;">
                                                        sell
                                                    </mat-icon></div>
                                            </div>
                                            <!-- <div style="color: #45546E; font-size: 11px; font-family: Roboto; font-weight: 400; text-transform: capitalize; line-height: 16px; letter-spacing: 0.22px; word-wrap: break-word">Phase</div> -->
                                            <input style="border: none;width: 130px;outline: none;font-size: 12px;"
                                                minlength='3' maxlength="30" formControlName="textInputControl"
                                                type="text" placeholder="Add Tag" />
                                        </div>
                                        <div style="align-self: stretch; height: 0px; border: 1px #E8E9EE solid"></div>

                                        <div
                                            style="justify-content: flex-start; align-items: flex-start; gap: 8px; display: inline-flex;margin-left: 6px;">
                                            <!-- <div style="width: 20px; height: 20px; position: relative">
                                     <div style="width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; border-radius: 9999px; border: 1px #D53E3E solid"></div>
                                     <div style="width: 14px; height: 14px; left: 3px; top: 3px; position: absolute; background: #D53E3E; border-radius: 9999px"></div>
                                 </div> -->
                                            <div *ngFor="let item of tagColor;let k=index">
                                                <!-- <div *ngIf='4>=k' (click)="getColor(item.color)" style="cursor: pointer;width: 20px; height: 20px; border-radius: 9999px" [ngStyle]="{'background':item.color}"></div> -->
                                                <div *ngIf='4>=k' (click)="getColor(item.color,k)"
                                                    style="cursor: pointer;width: 20px; height: 20px; position: relative">
                                                    <div *ngIf="k==colorChoosen"
                                                        style="width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; border-radius: 9999px; border: 1px #D53E3E solid">
                                                    </div>
                                                    <div style="width: 14px; height: 14px; left: 3px; top: 3px; position: absolute;border-radius: 9999px"
                                                        [ngStyle]="{'background':item.color}"></div>
                                                </div>
                                            </div>
                                        </div>
                                        <div
                                            style="justify-content: flex-start; align-items: flex-start; gap: 8px; display: inline-flex ;margin-left:-34px">
                                            <div *ngFor="let item of tagColor;let l=index">
                                                <!-- <div *ngIf="l>4" (click)="getColor(item.color)" style="cursor: pointer;width: 20px; height: 20px; border-radius: 9999px" [ngStyle]="{'background':item.color}"></div> -->
                                                <div *ngIf="l>4" (click)="getColor(item.color,l)"
                                                    style="cursor: pointer;width: 20px; height: 20px; position: relative">
                                                    <div *ngIf="l==colorChoosen"
                                                        style="width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; border-radius: 9999px; border: 1px #D53E3E solid">
                                                    </div>
                                                    <div style="width: 14px; height: 14px; left: 3px; top: 3px; position: absolute;border-radius: 9999px"
                                                        [ngStyle]="{'background':item.color}"></div>
                                                </div>
                                            </div>
                                        </div>

                                        <div (click)="createTag()"
                                            style="cursor: pointer;align-self: stretch; height: 32px; padding-top: 8px; flex-direction: column; justify-content: center; align-items: center; gap: 10px; display: flex">
                                            <div
                                                style="padding-left: 8px; padding-right: 8px; padding-top: 4px; padding-bottom: 4px; background: #EE4961; border-radius: 4px; justify-content: flex-start; align-items: center; gap: 8px; display: inline-flex">
                                                <div class="font-family"
                                                    style="color: white; font-size: 12px; font-weight: 700; text-transform: capitalize; line-height: 16px; word-wrap: break-word">
                                                    create new tag</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div *ngIf="displayTag.length>0"
                                    style="margin-top: 30px;width: 706px; height: 129px; justify-content: flex-start; align-items: flex-start; gap: 8px; display: inline-flex">
                                    <div
                                        style="justify-content: flex-start; align-items: flex-start; gap: 8px; display: flex;flex-wrap: wrap;">
                                        <div *ngFor="let d of displayTag;let j=index">
                                            <div style="padding-left: 10px; padding-right: 10px; padding-top: 1px; padding-bottom: 1px;; border-radius: 16px; justify-content: center; align-items: center; gap: 10px; display: flex"
                                                [ngStyle]="{'background':d.color}">
                                                <div tooltip="{{d.name}}" class="font-family"
                                                    style="color: white; font-size: 11px; font-weight: 500; text-transform: capitalize; line-height: 16px; letter-spacing: 0.22px; word-wrap: break-word;overflow: hidden;text-overflow: ellipsis;width: 61px;white-space: nowrap;">
                                                    {{d.name}}</div>
                                                <div
                                                    style="width: 8px; height: 8px; justify-content: center; align-items: center; display: flex">
                                                    <div (click)="removeTag(j)"
                                                        style="cursor: pointer;width: 8px; height: 8px; position: relative">
                                                        <div
                                                            style="width: 8px; height: 8px; left: 0px; top: 0px; position: absolute">
                                                        </div>
                                                        <div
                                                            style="width: 6.67px; height: 6.67px; left: 0.67px; top: 0.67px; position: absolute;">
                                                            <mat-icon style="    color: #515965;
                                       font-size: 13px;
                                       margin-top: -2px;
                                       position: absolute;">cancel</mat-icon></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                                <div *ngIf="displayTag.length>0" (click)="addDisplayTag()" id="displayTagDiv"
                                    style="cursor: pointer;width: 118px; height: 28px; justify-content: flex-start; align-items: center; gap: 12px; display: inline-flex;margin-left: 587px;margin-top: -157px;position: absolute;">
                                    <div
                                        style="padding: 2px; justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
                                        <div
                                            style="justify-content: flex-start; align-items: flex-start; gap: 10px; display: flex">
                                            <div style="width: 24px; height: 24px;"></div>
                                            <div style="width: 13px; height: 13px;"><mat-icon
                                                    style=" color: #526179;font-size: 24px;">add_circle_outline</mat-icon>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="font-family"
                                        style="color: #A8ACB2; font-size: 12px;font-weight: 500; text-transform: capitalize; line-height: 24px; letter-spacing: 0.28px; word-wrap: break-word">
                                        Add tag</div>
                                </div>
                                <div *ngIf="isDisplayVisible" id="displayTagDiv"
                                    style="width: 166px;margin-left: 527px; height: 200px;padding-bottom: 0px !important;padding: 16px; background: white; box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25); border-radius: 4px; border: 1px #E8E9EE solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 8px; display: inline-flex;margin-top: -356px;position: fixed;">
                                    <div
                                        style="justify-content: flex-start; align-items: center; gap: 2px; display: inline-flex">
                                        <div style="width: 14px; height: 14px; position: relative">
                                            <div
                                                style="width: 14px; height: 14px; left: 0px; top: 0px; position: absolute;">
                                            </div>
                                            <div
                                                style="width: 11.07px; height: 11.07px; left: 1.46px; top: 1.46px; position: absolute;">
                                                <mat-icon
                                                    style="color: #7D838B;font-size: 17px;top: 10px;margin-top: -10px;margin-left: -5px;">
                                                    sell
                                                </mat-icon></div>
                                        </div>
                                        <!-- <div style="color: #7D838B; font-size: 11px; font-family: Roboto; font-weight: 400; text-transform: capitalize; line-height: 16px; letter-spacing: 0.22px; word-wrap: break-word"> Add Tag..</div> -->
                                        <input style="border: none;width: 130px;outline: none;font-size: 12px;"
                                            minlength='3' maxlength="30" formControlName="textInputControlDisplay"
                                            type="text" [placeholder]="placeholder"
                                            (keyup.enter)="performEnterDisplay()" />
                                    </div>
                                    <div style="align-self: stretch; height: 0px; border: 1px #E8E9EE solid"></div>
                                    <div
                                        style="align-self: stretch; justify-content: flex-start; align-items: flex-start; gap: 8px; display: inline-flex">
                                        <div class="font-family"
                                            style="color: #B9C0CA; font-size: 11px; font-weight: 400; text-transform: capitalize; line-height: 16px; letter-spacing: 0.22px; word-wrap: break-word">
                                            All projects</div>
                                        <div style="width: 16px; height: 16px; position: relative">
                                            <div
                                                style="width: 16px; height: 16px; left: 0px; top: 0px; position: absolute;">
                                            </div>
                                            <div
                                                style="width: 13.33px; height: 13.33px; left: 1.33px; top: 1.33px; position: absolute;">
                                                <mat-icon style="color: #B9C0CA;font-size: 15px;"
                                                    tooltip="{{tooltip}}">info_outline</mat-icon></div>
                                        </div>
                                    </div>
                                    <div style="height: 700px;
                             overflow-y: auto;
                             overflow-x: hidden;
                             width: 149px;">
                                        <div *ngFor="let p of tags;let i=index" style="padding-bottom: 5px;">
                                            <div (click)="getDisplayTag(p.id)"
                                                style="padding-left: 8px;cursor: pointer; padding-right: 8px; background: #F6F6F6; border-radius: 4px; border: 1px #E8E9EE solid; justify-content: flex-start; align-items: center; gap: 4px; display: inline-flex">
                                                <div style="width: 4px; height: 4px; border-radius: 9999px"
                                                    [ngStyle]="{'background':p.color}"></div>
                                                <div *ngIf="18>p.name.length" class="font-family"
                                                    style="color: #7D838B; font-size: 11px;font-weight: 400; line-height: 16px; word-wrap: break-word">
                                                    {{p.name}}</div>
                                                <div *ngIf="p.name.length>=18" class="font-family"
                                                    style="color: #7D838B;font-size: 11px;font-weight: 400;line-height: 16px;/* word-wrap: break-word; */overflow: hidden;text-overflow: ellipsis;width: 105px;white-space: nowrap;">
                                                    {{p.name}}</div>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                                <div *ngIf="isNewTagVisibleDisplay && add"
                                    style="width: 180px;position: absolute;margin-left: 50px; height: 165px; padding: 16px; background: white; box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25); border-radius: 4px; border: 1px #E8E9EE solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 8px; display: inline-flex">
                                    <div
                                        style="justify-content: flex-start; align-items: center; gap: 2px; display: inline-flex">
                                        <mat-icon id="displayTagDiv" class="close-button"
                                            (click)="onCloseClickDisplay()">clear</mat-icon>
                                        <div style="width: 14px; height: 14px; position: relative">
                                            <div
                                                style="width: 14px; height: 14px; left: 0px; top: 0px; position: absolute;">
                                            </div>
                                            <div
                                                style="width: 11.07px; height: 11.07px; left: 1.46px; top: 1.46px; position: absolute;">
                                                <mat-icon
                                                    style="color: #7D838B;font-size: 18px;top: 10px;margin-top: -3px;margin-left: -9px;">
                                                    sell
                                                </mat-icon></div>
                                        </div>
                                        <!-- <div style="color: #45546E; font-size: 11px; font-family: Roboto; font-weight: 400; text-transform: capitalize; line-height: 16px; letter-spacing: 0.22px; word-wrap: break-word">Phase</div> -->
                                        <input style="border: none;width: 130px;outline: none;font-size: 12px;"
                                            minlength='3' maxlength="30" formControlName="textInputControlDisplay"
                                            type="text" placeholder="Add Tag" />
                                    </div>
                                    <div style="align-self: stretch; height: 0px; border: 1px #E8E9EE solid"></div>

                                    <div
                                        style="justify-content: flex-start; align-items: flex-start; gap: 8px; display: inline-flex;margin-left: 6px;">
                                        <!-- <div style="width: 20px; height: 20px; position: relative">
                                     <div style="width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; border-radius: 9999px; border: 1px #D53E3E solid"></div>
                                     <div style="width: 14px; height: 14px; left: 3px; top: 3px; position: absolute; background: #D53E3E; border-radius: 9999px"></div>
                                 </div> -->
                                        <div *ngFor="let item of tagColor;let k=index">
                                            <!-- <div *ngIf='4>=k' (click)="getColor(item.color)" style="cursor: pointer;width: 20px; height: 20px; border-radius: 9999px" [ngStyle]="{'background':item.color}"></div> -->
                                            <div *ngIf='4>=k' (click)="getColor(item.color,k)"
                                                style="cursor: pointer;width: 20px; height: 20px; position: relative">
                                                <div *ngIf="k==colorChoosen"
                                                    style="width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; border-radius: 9999px; border: 1px #D53E3E solid">
                                                </div>
                                                <div style="width: 14px; height: 14px; left: 3px; top: 3px; position: absolute;border-radius: 9999px"
                                                    [ngStyle]="{'background':item.color}"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div
                                        style="justify-content: flex-start; align-items: flex-start; gap: 8px; display: inline-flex ;margin-left:-34px">
                                        <div *ngFor="let item of tagColor;let l=index">
                                            <!-- <div *ngIf="l>4" (click)="getColor(item.color)" style="cursor: pointer;width: 20px; height: 20px; border-radius: 9999px" [ngStyle]="{'background':item.color}"></div> -->
                                            <div *ngIf="l>4" (click)="getColor(item.color,l)"
                                                style="cursor: pointer;width: 20px; height: 20px; position: relative">
                                                <div *ngIf="l==colorChoosen"
                                                    style="width: 20px; height: 20px; left: 0px; top: 0px; position: absolute; border-radius: 9999px; border: 1px #D53E3E solid">
                                                </div>
                                                <div style="width: 14px; height: 14px; left: 3px; top: 3px; position: absolute;border-radius: 9999px"
                                                    [ngStyle]="{'background':item.color}"></div>
                                            </div>
                                        </div>
                                    </div>

                                    <div (click)="createTagDisplay()"
                                        style="cursor: pointer;align-self: stretch; height: 32px; padding-top: 8px; flex-direction: column; justify-content: center; align-items: center; gap: 10px; display: flex">
                                        <div
                                            style="padding-left: 8px; padding-right: 8px; padding-top: 4px; padding-bottom: 4px; background: #EE4961; border-radius: 4px; justify-content: flex-start; align-items: center; gap: 8px; display: inline-flex">
                                            <div class="font-family"
                                                style="color: white; font-size: 12px; font-weight: 700; text-transform: capitalize; line-height: 16px; word-wrap: break-word">
                                                create new tag</div>
                                        </div>
                                    </div>
                                </div>
                            </mat-tab>

                        </mat-tab-group>
                    </div>
                </div>

            </form>
            <div class="row footer-buttons">
                <div class="col-10"></div>
                <div style="margin-left: 78px;">
                    <button mat-raised-button *ngIf="!save_disabled && milestoneWriteAccess" class="save-button" (click)="saveMilestone()"
                        [disabled]="save_disabled">{{this.save}}</button>
                    <div class="save-button" *ngIf="save_disabled">
                        <mat-spinner class="green-spinner" diameter="20"></mat-spinner>
                    </div>
                </div>
            </div>
        </div>
    </span>
</div>