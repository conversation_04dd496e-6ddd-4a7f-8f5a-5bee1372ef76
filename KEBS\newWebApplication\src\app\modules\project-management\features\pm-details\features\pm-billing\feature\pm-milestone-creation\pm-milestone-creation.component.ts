import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ni<PERSON>, Inject, HostListener, ViewContainerRef, InjectionToken } from '@angular/core';
import { FormArray, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import * as moment from 'moment';
import { MomentDateAdapter, MAT_MOMENT_DATE_ADAPTER_OPTIONS } from '@angular/material-moment-adapter';
import { DateAdapter, MAT_DATE_LOCALE, MAT_DATE_FORMATS } from '@angular/material/core';
import { PmBillingService } from './../../services/pm-billing.service'
import { PmMasterService } from 'src/app/modules/project-management/services/pm-master.service';
import { ToastrService } from 'ngx-toastr';
import * as _ from 'underscore';
import { Router } from '@angular/router';
import { v4 as uuidv4 } from 'uuid';
import { ToasterService } from 'src/app/services/toaster-service/toaster.service';
import { truncateWithEllipsis } from '@amcharts/amcharts4/.internal/core/utils/Utils';
import { UtilityService } from 'src/app/services/utility/utility.service';
import { ToasterMessageService } from 'src/app/modules/project-management/shared-lazy-loaded/components/toaster-message/toaster-message.service';
import { PmAuthorizationService } from 'src/app/modules/project-management/services/pm-authorization.service';
import { GetNameByIdPipe } from 'src/app/modules/project-management/shared-lazy-loaded/pipes/get-name-by-id.pipe';
export const TOASTER_MSG_SERVICE_TOKEN = new InjectionToken<ToasterMessageService>('TOASTER_MSG_SERVICE_TOKEN');

@Component({
  selector: 'app-pm-milestone-creation',
  templateUrl: './pm-milestone-creation.component.html',
  styleUrls: ['./pm-milestone-creation.component.scss'],
  providers: [
    {
      provide: DateAdapter,
      useClass: MomentDateAdapter,
      deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS]
    },
    {
      provide: MAT_DATE_FORMATS,
      useValue: {
        parse: {
          dateInput: "DD-MMM-YYYY"
        },
        display: {
          dateInput: "DD-MMM-YYYY",
          monthYearLabel: "MMM YYYY"
        }
      }
    },
    { provide: TOASTER_MSG_SERVICE_TOKEN, useClass: ToasterMessageService },
    GetNameByIdPipe
  ]
})
export class PmMilestoneCreationComponent implements OnInit {
  projectMilestoneGrossValue: any;
  @HostListener('window:keyup.esc') onKeyUp() {
    this.onCloseClickk()
  }
  milestone_name_info: any = "Milestone Name"
  employee_list: any
  end_date_info: any = "end date"
  employee_name_info: any = "employee"
  tasks_list: any = ['Task 1', 'Task 2', 'Task 3', 'Task 4', 'Task 5', 'Task 6'];
  is_tagOpen: boolean = false
  data: any
  date: any
  showDisplay: boolean = true
  valueType = 'html';
  portfolio_id: any
  project_id: any
  formConfig: any;
  billableColor: any = 'warn'
  isBot: any = false
  isEmail: any = false
  minDate: Date | null = null;
  maxDate: Date | null = null;
  invoiceDateConfig:any;
  loading: any = true
  reponsible_list: any
  datelogic: any = true;
  customer_list: any = true;
  payment_list: any = true;
  isChangeMilestoneType: boolean=false;
  changeMilestoneTypeMatrix: boolean=false;
  stepperFormGroup = this.formBuilder.group({
    endDate: [''],
    milestone_name: [''],
    employee_name: [''],
    milestone_value: [''],
    weighted_percentage: [''],
    task: [''],
    longDescription: [''],
    startDate: [''],
    textInputControlDisplay: [''],
    textInputControl: [''],
    po_number: [''],
    po_value: [''],
    po_date: [''],
    po_reference: [''],
    payment_terms: [''],
    partner: [''],
    milestone_type: [],
    invoice_date: [''],
    po_number_dropdown: [''],
    milestone_group: [''],
    quote_id: ['']
  });
  add: boolean = false
  code: any
  mode: any
  service_type_id: any;
  edit_data: any
  m_value: number
  header_name: any
  save: any
  save_disabled: boolean = false
  targetButton: any
  isPopupVisible: boolean = false
  tags: any
  displayTag: any = []
  isDisplayVisible: boolean = false
  check: any
  isNewTagVisible: boolean = false
  duplicate: any
  tagColor: any
  searchTag: any
  choosedColor: any = '#ffff'
  isNewTagVisibleDisplay: boolean = false
  placeholder: any
  colorChoosen: any = 0
  tooltip: any
  outerClick: boolean = false
  outerClickDisplay: boolean = false
  project_start_date: any
  project_end_date: any
  dateLogic: any;
  retrieveMessages: any;
  quote: any
  percentage: any
  totalMilestoneValue: any = 0
  percentage_disable: boolean = true
  percentage_disable_color: any = '#E8E9EE'
  name_length: any;
  at_risk: any;
  button: any;
  fontStyle: any;
  milestoneTypeList: any = [];
  tabColor: any;
  fieldOutline: any;
  color: any;
  po_number_dropdown_list: any = []
  withOpportunity: any
  risk: any
  po_number: any
  po_number_dropdown_access: boolean = this.authService.getProjectObjectAccess(109)
  max_milestone_value: number
  milestoneGroupList: any = []
  milestone_data: any = []
  enableMilestoneGroup: boolean = false
  milestone_grouped_data: any
  enableMilestoneGroupEdit: boolean = false
  childAvailable: boolean = false
  quote_id_list: any = []
  disableForCreation: boolean = false
  matrixConfig: any;
  defaultMilestoneTypeForCreation: any
  milestoneWriteAccess: boolean=false;
  milestoneDetailsAccess: boolean=false; 
  action_type: any;
  oldFormValue: any;
  oldData: any;
  restriction:boolean=false
  newData: any;
  blanketPo: boolean = false;
  @HostListener('document:click', ['$event'])
  onClick(event: MouseEvent) {
    // Check if the click target is outside of the div
    if (this.isPopupVisible == true) {
      if (!(event.target as HTMLElement).closest('#mytagDiv')) {
        this.isPopupVisible = false
        this.tags = this.searchTag
        this.outerClick = true
        this.stepperFormGroup.patchValue({ [`textInputControl`]: '' })
      }
    }
    if (this.isDisplayVisible == true) {
      if (!(event.target as HTMLElement).closest('#displayTagDiv')) {
        this.isDisplayVisible = false
        this.tags = this.searchTag
        this.outerClickDisplay = true
        this.stepperFormGroup.patchValue({ [`textInputControlDisplay`]: '' })
      }

    }
  }
  constructor(@Inject(MAT_DIALOG_DATA) public dialogData: DialogData, private formBuilder: FormBuilder, private router: Router,
    @Inject(TOASTER_MSG_SERVICE_TOKEN) private toasterService: ToasterMessageService, private pmMasterService: PmMasterService,
    public dialogRef: MatDialogRef<PmMilestoneCreationComponent>, private PmBillingService: PmBillingService,
    private utilityService: UtilityService, private authService: PmAuthorizationService, private getNameByIdPipe: GetNameByIdPipe) { }

  async ngOnInit() {
    this.project_id = this.router.url.split("/")[5]
    this.portfolio_id = this.router.url.split("/")[3]
    this.mode = this.dialogData.mode
    const risk = this.dialogData.at_risk;
    // this.button = this.dialogData.button ? this.dialogData.button : '';
    // document.documentElement.style.setProperty('--milestoneButton', this.button)
    this.at_risk = (parseInt(risk) == 1) ? true : false;
    //console.log(this.at_risk);
    //console.log(this.dialogData.data)
    this.edit_data = this.dialogData.data
    if(this.edit_data?.financial_status_id==6){
      this.save_disabled=true
    }

    this.action_type = this.edit_data.action_type

    this.milestoneWriteAccess = this.dialogData.milestoneWriteAccess;

    
    if(this.mode=="Edit")
    { 
      if(this.action_type=="User")
        this.milestoneDetailsAccess = true;
    }
    else
    {
      this.milestoneWriteAccess=true;
      this.milestoneDetailsAccess = true;
    }

    console.log("Edit Data", this.edit_data, this.mode, this.milestoneWriteAccess, this.milestoneDetailsAccess, this.action_type)

    
    await this.pmMasterService.getPMFormCustomizeConfigV().then((res: any) => {
      if (res) {
        this.formConfig = res;
      }
    });

    
    const retrieveStyles = _.where(this.formConfig, { type: "project-theme", field_name: "styles", is_active: true });
    const restriction=_.where(this.formConfig, { type: "po_date", field_name: "restriction",is_active:true});
    if(restriction.length > 0){
      this.restriction = true
    }
    this.invoiceDateConfig = _.where(this.formConfig, { type: 'milestone-creation', field_name: 'invoice_date_check', is_active: true });
    this.retrieveMessages = _.where(this.formConfig, { type: "milestone-creation", field_name: "messages", is_active: true });
    const type = _.where(this.formConfig, { type: "milestone-creation", field_name: "weighted_percentage", is_active: true })
    this.percentage_disable = type.length > 0 ? type[0].disable : true
    this.percentage_disable_color = type.length > 0 ? type[0].color : '#E8E9EE'
    this.button = retrieveStyles.length > 0 ? retrieveStyles[0].data.button_color ? retrieveStyles[0].data.button_color : "#90ee90" : "#90ee90";
    this.color = this.edit_data.color ? this.edit_data.color : this.button;
    document.documentElement.style.setProperty('--milestoneCreateButton', this.button)
    this.fontStyle = retrieveStyles.length > 0 ? retrieveStyles[0].data.font_style ? retrieveStyles[0].data.font_style : "Roboto" : "Roboto";
    document.documentElement.style.setProperty('--milestoneCreateFont', this.fontStyle);

    this.tabColor = retrieveStyles.length > 0 ? retrieveStyles[0].data.tab_color ? retrieveStyles[0].data.tab_color : "" : "";
    document.documentElement.style.setProperty('--milestoneTabColor', this.tabColor)

    this.fieldOutline = retrieveStyles.length > 0 ? retrieveStyles[0].data.field_outline_color ? retrieveStyles[0].data.field_outline_color : "#808080" : "#808080";
    document.documentElement.style.setProperty('--milestoneField', this.fieldOutline);
    //console.log(this.edit_data)
    //console.log(this.mode)
    //console.log(this.edit_data.end_date)
    const retrieveNameLength = _.where(this.formConfig, { type: "milestone-creation", field_name: "milestone_name", is_active: true });
    this.name_length = retrieveNameLength.length > 0 ? retrieveNameLength[0].maxLength : 300;
    await this.PmBillingService.getProjectQuote(this.portfolio_id, this.project_id).then((res) => {
      if (res['messType'] == 'S') {
        this.code = res['data'][0].currency_code
        this.project_end_date = moment(res['data'][0].planned_end_date).utc().format('YYYY-MM-DD')
        this.project_start_date = moment(res['data'][0].planned_start_date).utc().format('YYYY-MM-DD')
        this.quote = res['data'][0].planned_quote
        this.withOpportunity = res['data'][0].with_opportunity
        this.risk = res['data'][0].at_risk
        this.po_number = res['data'][0].po_number
        this.service_type_id = res['data'][0].service_type_id
        //console.log(this.code)
      }
    })

    
    await this.PmBillingService.getMilestoneMatrix().then((res) => {
      if (res['messType'] == "S") {
        let matrixConfigData = res['data']
        let matrixCheckData = {service_type_id:this.service_type_id, is_active : 1, milestone_type_creation_available: 1}
        console.log('matrixCheckData',matrixCheckData)
        this.matrixConfig = _.where(matrixConfigData, matrixCheckData)

        if(this.mode =="Create")
        {
          this.changeMilestoneTypeMatrix = _.findWhere(matrixConfigData, {service_type_id:this.service_type_id, is_active : 1, change_milestone_type: 1}) ? true: false
        }
        else
        {
          this.changeMilestoneTypeMatrix = _.findWhere(matrixConfigData, {service_type_id:this.service_type_id, is_active : 1, change_milestone_type: 1, milestone_type_id: this.edit_data.milestone_type, milestone_status_id: this.edit_data?.financial_status_id }) ? true: false
        }
      }
      else{
        this.matrixConfig = [];
      }
    })

    await this.pmMasterService.getMilestoneTypeList().then((res: any) => {
      if (res['messType'] == 'S') {

        let milestone_type_list = _.uniq(_.pluck(this.matrixConfig,"milestone_type_id"))

        console.log("Milestone Type List", milestone_type_list)

        if(this.mode == "Create")
        {
          this.milestoneTypeList = _.filter(res['data'],(data)=>{
            if(_.contains(milestone_type_list, data['id']))
            {
                return true;
            }
          })
        }
        else
        {
          this.milestoneTypeList = res['data']
        }
        
      }
    })


    await this.pmMasterService.getCustomerList().then((res: any) => {
      this.customer_list = this.pmMasterService.customer_list;
      //console.log(this.customer_list);
    });

    await this.pmMasterService.getPaymentTermsList().then((res: any) => {
      if (res['messType'] == "S") {
        this.payment_list = res['data'];
      }

    });

   
    this.PmBillingService.getQuoteIDList(this.portfolio_id, this.project_id).then((res) => {
      if (res['messType'] == 'S' && res['data'].length > 0) {
        this.quote_id_list = res['data']
      }
      else {
        this.quote_id_list = []
      }
    })
    // if (this.po_number_dropdown_access) {
    //   if (this.withOpportunity == 0) {
    //     if (this.risk == 1) {
    //       this.po_number_dropdown_list = []
    //     }
    //     else {
    //       this.po_number_dropdown_list = [{ id: this.po_number, name: this.po_number }]
    //       this.stepperFormGroup.patchValue({ ['po_number']: this.po_number })
    //     }
    //   }
    //   else {
    //     if (this.risk == 1) {
    //       this.PmBillingService.getQuoteIDList(this.portfolio_id, this.project_id).then((res) => {
    //         if (res['messType'] == 'S' && res['data'].length > 0) {
    //           this.quote_id_list = res['data']
    //         }
    //         else {
    //           this.quote_id_list = []
    //         }
    //       })
    //       this.po_number_dropdown_list = []
    //     }
    //     else {
          
    //       this.PmBillingService.getPoNumberList(this.portfolio_id, this.project_id).then((res) => {
    //         if (res['messType'] == 'S' && res['data'].length > 0) {
    //           if (res['data'].length == 1) {
    //             this.stepperFormGroup.patchValue({ ['po_number']: res['data'][0].name })
    //             this.stepperFormGroup.patchValue({ ['quote_id']: res['data'][0].quote_id })
    //             this.po_number_dropdown_list = res['data']
    //           }
    //           else {
    //             this.po_number_dropdown_list = res['data']
    //           }
    //         }
    //         else {
    //           this.po_number_dropdown_list = res
    //         }
    //       })
    //     }
    //   }
    // }

    if (this.withOpportunity == 0) {
      this.PmBillingService.getProjectPoMaster(this.project_id, null).then((res) => {
        if (res['messType'] == 'S' && res['data'].length > 0) {
          this.po_number_dropdown_list = res['data']
          if (res['data'].length == 1) {
              this.stepperFormGroup.patchValue({ ['po_number']: res['data'][0].id ? res['data'][0].id : '' });
              this.stepperFormGroup.patchValue({['po_date']: res['data'][0].po_date ? res['data'][0].po_date : ''});
              this.stepperFormGroup.patchValue({['payment_terms']: res['data'][0].payment_terms ?  res['data'][0].payment_terms : ''});
          }
        }
      })
    }

    if (this.mode == 'Create') {
      this.header_name = 'Create Milestone'
      this.maxDate=null;
      this.minDate=null;
      this.save = 'Add'
      this.disableForCreation =  _.find(this.formConfig, { type: "milestone-creation", field_name: "milestone_type", is_active: true })?.disable_in_creation ?? false;
      //this.defaultMilestoneTypeForCreation = _.find(this.formConfig, { type: "milestone-creation", field_name: "milestone_type", is_active: true })?.default_type_id_in_creation ?? false;
      //this.stepperFormGroup.patchValue({ ['milestone_type']:this.defaultMilestoneTypeForCreation })
      // this.stepperFormGroup.patchValue({'endDate':moment().format('YYYY-MM-DD')})
      // this.date=moment(this.stepperFormGroup.get('endDate').value).format('YYYY-MM-DD')
      await this.PmBillingService.getResponsible(this.portfolio_id, this.project_id).then((res) => {
        if (res['messType'] == 'S' && res['data'].length > 0) {
          this.reponsible_list = res['data'][0].associate_id
          //console.log(res)
          this.stepperFormGroup.patchValue({ ['employee_name']: this.reponsible_list || '' })
        }
      })
    }
    await this.PmBillingService.getSumOFMilestoneValue(this.project_id).then((res) => {
      if (res['messType'] == 'S') {
        for (let items of res['data']) {
          items['milestone_value'] = JSON.parse(items['milestone_value'])
          items['quote_id'] = items['quote_id']!=null && items['quote_id']!="" && items['quote_id']!="null" ? parseInt(items['quote_id']) : null;
          for (let item of items['milestone_value']) {
            let check = []
            check = _.where(this.milestone_grouped_data, { childId: items['id'] })
            console.log(check)
            if (item['currency_code'] == this.code && check.length == 0) {
              this.totalMilestoneValue = this.totalMilestoneValue + item['value'];
            }
          }
        }
        this.milestone_data = res['data']
      }
    })

    await this.PmBillingService.getProjectFinancialValues(this.project_id).then((res) =>{
      if(res['messType'] == 'S' && res['data']){
        this.projectMilestoneGrossValue = res['data']['milestoneGrossValue'] ?  res['data']['milestoneGrossValue'] : null;
        let totalMilestoneValueInProjectCurrency = _.find(this.projectMilestoneGrossValue, item=>item.currency_code ==  this.code) 
         this.totalMilestoneValue =  totalMilestoneValueInProjectCurrency?.value ? totalMilestoneValueInProjectCurrency?.value : 0;
      }else{
        this.toasterService.showError(res['message'])
      }
    })


    await this.PmBillingService.getGroupedMilestone(this.project_id).then((res) => {
      if (res['messType'] == 'S') {
        this.milestone_grouped_data = res['data']
      }
    })
    if (this.mode == 'Edit') {
      this.header_name = 'Edit Milestone'
      this.save = 'Save'
      this.invoiceDateConfig = _.where(this.formConfig, { type: 'milestone-creation', field_name: 'invoice_date_check', is_active: true });
      
      if (this.milestone_grouped_data.length > 0) {
        let check_data = _.where(this.milestone_grouped_data, { childId: this.edit_data.id })
        if (check_data.length > 0) {
          this.enableMilestoneGroupEdit = false
        }
        else {
          this.enableMilestoneGroupEdit = true
        }
      }

      if (this.withOpportunity == 1 && parseInt(this.edit_data.quote_id)) {
        this.PmBillingService.getProjectPoMaster(this.project_id, parseInt(this.edit_data.quote_id)).then((res) => {
          if (res['messType'] == 'S' && res['data'].length > 0) {
            if (res['data'].length == 1) {
              this.stepperFormGroup.patchValue({ ['po_number']: res['data'][0].id ? res['data'][0].id : '' });
              this.stepperFormGroup.patchValue({['po_date']: res['data'][0].po_date ? res['data'][0].po_date : ''});
              this.stepperFormGroup.patchValue({['payment_terms']: res['data'][0].payment_terms ?  res['data'][0].payment_terms : ''});
            }
            this.po_number_dropdown_list = res['data']
          }
        })
      }
      
      this.stepperFormGroup.patchValue({ ['endDate']: this.edit_data.end_date })
      this.date = this.edit_data.end_date
      this.stepperFormGroup.patchValue({ ['milestone_name']: this.edit_data.label })
      this.stepperFormGroup.patchValue({ ['startDate']: this.edit_data.start_date })
      this.stepperFormGroup.patchValue({ ['weighted_percentage']: this.edit_data.milestone_percentage })
      this.stepperFormGroup.patchValue({ ['employee_name']: this.edit_data.responsible })
      this.stepperFormGroup.patchValue({ ['longDescription']: this.edit_data.description })

      this.stepperFormGroup.patchValue({ ['po_number']: this.edit_data.po_number })
      this.stepperFormGroup.patchValue({ ['quote_id']: parseInt(this.edit_data.quote_id) })
      this.stepperFormGroup.patchValue({ ['po_value']: this.edit_data.po_value })
      this.stepperFormGroup.patchValue({ ['po_reference']: this.edit_data.po_reference })
      this.stepperFormGroup.patchValue({ ['po_date']: this.edit_data.po_date })
      this.stepperFormGroup.patchValue({ ['payment_terms']: this.edit_data.payment_terms })
      this.stepperFormGroup.patchValue({ ['partner']: this.edit_data.partner })
      this.stepperFormGroup.patchValue({ ['milestone_type']: this.edit_data.milestone_type })
      let milestone_type_ids = _.uniq(_.pluck(_.where(this.milestoneTypeList, {milestone_grouping:1}),"id"))

      
      if (_.contains(milestone_type_ids,this.edit_data.milestone_type)) {
        if (this.milestone_data.length > 0) {
          await this.getMilestoneGroupList(this.edit_data.milestone_type, this.edit_data?.id)
          this.enableMilestoneGroup = true
          console.log(this.milestoneGroupList)
        } else {
          this.enableMilestoneGroup = true
        }
      }
      else {
        this.enableMilestoneGroup = false
        this.milestoneGroupList = []
      }
      if (this.invoiceDateConfig && this.invoiceDateConfig.length > 0) {
        let config = this.invoiceDateConfig[0];
        let negativeDuration = config.negative_duration - 1 || 0;
        let positiveDuration = config.positive_duration + 1 || 0;
        let endDate
        if (config.current_date) {
          endDate = moment();
      } else {
          endDate = moment(this.edit_data.end_date);
      }
       
        let minDate = endDate.clone().subtract(negativeDuration, 'days').toDate();
        let maxDate = endDate.clone().add(positiveDuration, 'days').toDate();
        if (config.milestone_start_date) {
          const startDate = moment(this.edit_data.start_date);
          if (startDate.isBefore(minDate)) {
            minDate = startDate.add(1, 'days').toDate();
          }
        }
        this.minDate = minDate;
        this.maxDate = maxDate;
      } else {
        this.minDate = null;
        this.maxDate = null;
      }
      console.log("Milestone Group", JSON.stringify(this.milestoneGroupList))
      console.log(this.edit_data.milestone_group)
      if (this.edit_data.milestone_group && this.edit_data.milestone_group.length > 0) {
        console.log(this.edit_data.milestone_group)
        this.childAvailable = true
        if(this.edit_data.milestone_group!=null && this.edit_data.milestone_group!="" && this.edit_data.milestone_group!="null" && this.edit_data.milestone_group!=" " && this.edit_data.milestone_group!="[]")
        {
          this.stepperFormGroup.patchValue({ ['milestone_group']: typeof this.edit_data.milestone_group =="string" ? JSON.parse(this.edit_data.milestone_group) : this.edit_data.milestone_group })
        }
      }
      console.log("Milestone Group", this.stepperFormGroup.get('milestone_group').value)
      if (this.edit_data.invoice_date != '' && this.edit_data.invoice_date != null && this.edit_data.invoice_date != undefined) {
        this.stepperFormGroup.patchValue({ ['invoice_date']: this.edit_data.invoice_date })
      }
      if (this.edit_data.remainder == null || this.edit_data.remainder == 0) {
        this.isBot = false
      }
      else {
        this.isBot = true
      }
      if (this.edit_data.email_notify == null || this.edit_data.email_notify == 0) {
        this.isEmail = false
      }
      else {
        this.isEmail = true
      }
      let currencyList = this.edit_data.currencyList != "" && this.edit_data.currencyList != null ? typeof this.edit_data.currencyList == "string" ? JSON.parse(this.edit_data.currencyList) : this.edit_data.currencyList : []
      for (let items of currencyList) {
        //console.log(items)
        //console.log(items['currency_code'])
        //console.log(this.code)
        if (items['currency_code'] == this.code) {
          this.m_value = items['value']
          //console.log(this.m_value)
        }
      }
      this.stepperFormGroup.patchValue({ ['milestone_value']: this.m_value })
      if(this.dialogData.tags && typeof this.dialogData.tags =="object")
      {
        for (let items of this.dialogData.tags) {
          if (items['unique_id_2'] == this.edit_data.id) {
            this.displayTag.push(items)
          }
        }
      }

      //console.log(this.displayTag)
    }
    if(this.defaultMilestoneTypeForCreation === 1){
        if (this.milestone_data.length > 0) {
          this.getMilestoneGroupList(1, this.edit_data?.id)
          this.enableMilestoneGroup = true
          console.log(this.milestoneGroupList)
        } else {
          this.enableMilestoneGroup = true
        }
      this.enableMilestoneGroup = true
    }
    this.loading = false
    if (this.mode == 'Edit') {
      this.totalMilestoneValue = this.totalMilestoneValue - this.m_value
      //console.log(this.totalMilestoneValue)
    }
    this.max_milestone_value = this.quote - this.totalMilestoneValue
    console.log(this.max_milestone_value)
    // await this.pmMasterService.getPMFormCustomizeConfig().then((res: any)=>{
    //   if(res['messType']=="S")
    //   {
    //     this.formConfig=res['data']
    //     //console.log(this.formConfig);

    //   }
    // })
    // this.stepperFormGroup.get('weighted_percentage').valueChanges.subscribe(async(res)=>{
    //                    if(this.stepperFormGroup.get('weighted_percentage').value<0){
    //                     this.stepperFormGroup.patchValue({['weighted_percentage']:0})
    //                    }
    //                    else if(this.stepperFormGroup.get('weighted_percentage').value>100){
    //                     this.stepperFormGroup.patchValue({['weighted_percentage']:100})
    //                    }
    //                    else{
    //                     this.stepperFormGroup.patchValue({['weighted_percentage']:''})
    //                    }
    //      })
    this.stepperFormGroup.get('milestone_value').valueChanges.subscribe(async (res) => {
      //console.log(res);
      let check_value

      if (this.stepperFormGroup.get('milestone_value').value != '') {

        if (res == '' && res == null && res == undefined) {
          this.stepperFormGroup.patchValue({ ['weighted_percentage']: '' })
        }
        if (res != '' && res != null && res != undefined) {
          check_value = this.stepperFormGroup.get('milestone_value').value
          //   if((parseFloat(this.stepperFormGroup.get('milestone_value').value)+parseFloat(this.totalMilestoneValue))<=parseFloat(this.quote)){
          //   this.percentage=((this.stepperFormGroup.get('milestone_value').value)/this.quote)*100
          //   //console.log(res, this.percentage)
          //   if(!isNaN(this.percentage)){
          //     this.stepperFormGroup.patchValue({['weighted_percentage']:this.percentage.toFixed(2)})
          //   }
          // }
          // else{
          //   // this.stepperFormGroup.patchValue({['milestone_value']:''})
          //   const milestoneValue_exceed_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.milestoneValue_exceed_msg ? this.retrieveMessages[0].errors.milestoneValue_exceed_msg : 'Overall milestone value exceeding Order Value' : 'Overall milestone value exceeding Order Value';
          //   this.toasterService.showWarning(milestoneValue_exceed_msg, 10000);
          // }
          // }
          // else if(res<0){
          //             // this.stepperFormGroup.patchValue({['milestone_value']:''})
          // }
          this.percentage = ((this.stepperFormGroup.get('milestone_value').value) / this.quote) * 100
          //console.log(res, this.percentage)
          if (!isNaN(this.percentage)) {
            this.stepperFormGroup.patchValue({ ['weighted_percentage']: this.percentage.toFixed(2) })
          }
        }
      }
      else {
        this.stepperFormGroup.patchValue({ ['weighted_percentage']: '' })
      }

    })
    this.stepperFormGroup.get('endDate').valueChanges.subscribe(async (res) => {
      console.log(res);
      console.log(this.stepperFormGroup.get('invoice_date').value)
      if (this.stepperFormGroup.get('invoice_date').value == '' || this.stepperFormGroup.get('invoice_date').value == null || this.stepperFormGroup.get('invoice_date').value == undefined) {
        console.log('test')
        if (res != '' && res != null && res != undefined) {
          console.log('test 2')
          this.stepperFormGroup.patchValue({ ['invoice_date']: res })
        }
      }

    })

    this.stepperFormGroup.get('quote_id').valueChanges.subscribe(async(res)=>{
        if(res)
        {
            let quote = _.where(this.quote_id_list,{id: res})

            if(quote.length>0)
            {
              this.PmBillingService.getProjectPoMaster(this.project_id, quote[0]?.id).then((res) => {
                if (res['messType'] == 'S' && res['data'].length > 0) {
                  this.po_number_dropdown_list = res['data']
                  if (res['data'].length == 1) {
                      this.stepperFormGroup.patchValue({
                        "po_number": res['data'][0].id ? res['data'][0].id : null,
                        "po_date": res['data'][0].po_date ? res['data'][0].po_date : null,
                        "payment_terms": res['data'][0].payment_terms ? res['data'][0].payment_terms : null
                      })
                  }
                }
              })
            }
            
            let milestone_type = this.stepperFormGroup.get('milestone_type').value;

            if(this.stepperFormGroup.get('milestone_type').valid)
            {
              this.getMilestoneGroupList(milestone_type, this.edit_data?.id)
            }
        }
    })
    this.stepperFormGroup.get('milestone_type').valueChanges.subscribe(async (res) => {
      console.log(res);

      if (this.stepperFormGroup.get('milestone_type').value != '') {

        if (res == '' && res == null && res == undefined) {
          this.enableMilestoneGroup = false
          this.milestoneGroupList = []
        }
        if (res != '' && res != null && res != undefined) {
          let milestone_type_ids = _.uniq(_.pluck(_.where(this.milestoneTypeList, {milestone_grouping:1}),"id"))
      
          if (_.contains(milestone_type_ids,res)) {


            if (this.milestone_data.length > 0) {
              this.milestoneGroupList = []
              this.getMilestoneGroupList(res, this.edit_data?.id)
              this.enableMilestoneGroup = true
              console.log(this.milestoneGroupList)
            } else {
              this.enableMilestoneGroup = true
            }
          }
          else {
            this.enableMilestoneGroup = false
            this.stepperFormGroup.patchValue({ 'milestone_group': '' })
            this.milestoneGroupList = []
          }
        }
      }
      else {
        this.enableMilestoneGroup = false
        this.milestoneGroupList = []
      }
    })
    this.stepperFormGroup.get('milestone_group').valueChanges.subscribe(async (res) => {
      console.log(res);

      if (this.stepperFormGroup.get('milestone_group').value != '') {

        if (res != '' && res != null && res != undefined) {
          if (this.stepperFormGroup.get('milestone_type').value != null && this.stepperFormGroup.get('milestone_type').value != '') {
            console.log(this.stepperFormGroup.get('milestone_group').value)
            if (this.stepperFormGroup.get('milestone_type').value == 3) {
              let grouped_data = this.stepperFormGroup.get('milestone_group').value
              let grouped_value = 0
              for (let i = 0; i < grouped_data.length; i++) {
                let temp_data = _.where(this.milestone_data, { id: grouped_data[i] })
                if (temp_data.length > 0) {
                  for (let item of temp_data[0].milestone_value) {
                    if (item['currency_code'] == this.code) {
                      grouped_value = grouped_value + item['value']
                    }
                  }
                }
              }
              this.stepperFormGroup.patchValue({ ['milestone_value']: grouped_value })
            }
          }
        }
      }
    })
      this.stepperFormGroup.get('po_number').valueChanges.subscribe(async(res)=>{
        if(res != '' && res!= null && res!=undefined){
          let po_details=_.where(this.po_number_dropdown_list,{id:res})
          this.stepperFormGroup.patchValue({['po_date']: po_details[0].po_date ? po_details[0].po_date : ''});
          this.stepperFormGroup.patchValue({['payment_terms']:po_details[0].payment_terms ?  po_details[0].payment_terms : ''});
        }

      })
      // this.stepperFormGroup.get('quote_id').valueChanges.subscribe(async (res) => {

      //   if (res != '' && res != null && res != undefined) {
      //     let quote_id = _.where(this.po_number_dropdown_list, { quote_id: res })
      //     this.stepperFormGroup.patchValue({ ['po_number']: quote_id[0].id })
      //   }
      // })
      await this.PmBillingService.getExistingTags().then((res) => {
        //console.log(res)
        if (res['messType'] == 'S') {
          this.tags = res['data']
          this.searchTag = res['data']
          //console.log(this.searchTag)
          //console.log(this.tags)
        }

      })
      if (this.add == true) {
        this.placeholder = 'Add Tag'
        this.tooltip = 'Press the Enter key to create a new tag.'
      }
      else {
        this.placeholder = 'Search'
        this.tooltip = 'Type here to  search a tags'
      }
      await this.PmBillingService.getTagsColor().then((res) => {
        //console.log(res)
        if (res['messType'] == 'S') {
          this.tagColor = res['data']
        }
      })
      this.stepperFormGroup.get('textInputControl').valueChanges.subscribe(async (res) => {
        if (this.stepperFormGroup.get('textInputControl').value.length > 0) {
          //console.log(res)
          if (res) {
            // this.duplicate=0
            //      for(let items of this.tags){
            //       if((res.toLowerCase()).match(items['name'].toLowerCase())){
            //            this.duplicate=1
            //       }
            //      }
            //      //console.log(this.duplicate)
            //      if(this.duplicate==0){
            //       this.isNewTagVisible=true
            //       this.isPopupVisible=false
            //      }
            //      else{
            //       this.isNewTagVisible=false
            //       this.isPopupVisible=true
            //      }

            //      _.filter(this.searchTag, (value)=>{
            //       //console.log(value)
            //   if((res.toLowerCase()).match(value.name.toLowerCase()))
            //        {
            //         this.tags.splice(0, this.tags.length);
            //         this.tags.push({"id":value.id,"name":value.name,"color":value.color})
            //         //console.log(this.tags)
            //         //console.log(this.searchTag)
            //   }
            // })
            this.tags = this.searchTag.filter(item =>
              item.name.toLowerCase().includes(res.toLowerCase())
            );
            //console.log(this.tags)
            if (this.tags.length > 0) {
              this.isNewTagVisible = false
              this.isPopupVisible = true
            }
            else {
              if (this.add == true) {
                this.isNewTagVisible = true
                this.isPopupVisible = false
              }
              else {
                this.isPopupVisible = true
              }
            }

          }

        }
        else if (this.outerClick == true) {
          this.outerClick = false
        }
        else {
          // this.tags.splice(0, this.tags.length);
          this.tags = this.searchTag
          //console.log(this.searchTag)
          //console.log(this.tags)
          this.isNewTagVisible = false
          this.isPopupVisible = true
        }
      })
      this.stepperFormGroup.get('textInputControlDisplay').valueChanges.subscribe(async (res) => {
        if (this.stepperFormGroup.get('textInputControlDisplay').value.length > 0) {
          //console.log(res)
          if (res) {
            this.tags = this.searchTag.filter(item =>
              item.name.toLowerCase().includes(res.toLowerCase())
            );
            //console.log(this.tags)
            if (this.tags.length > 0) {
              this.isNewTagVisibleDisplay = false
              this.isDisplayVisible = true
            }
            else {
              if (this.add == true) {
                this.isNewTagVisibleDisplay = true
                this.isDisplayVisible = false
              }
              else {
                this.isDisplayVisible = true
              }
            }


          }

        }
        else if (this.outerClickDisplay == true) {
          this.outerClickDisplay = false
        }
        else {
          //console.log(this.searchTag)
          // this.tags.splice(0, this.tags.length);
          //console.log(this.searchTag)
          this.tags = this.searchTag
          //console.log(this.searchTag)
          //console.log(this.tags)
          this.isNewTagVisibleDisplay = false
          this.isDisplayVisible = true
        }
      })
      this.oldFormValue = this.stepperFormGroup.value;
      this.oldData = {
        milestone_name: this.oldFormValue.milestone_name ? this.oldFormValue.milestone_name : null,
        start_date: this.oldFormValue.startDate ? moment(this.oldFormValue.startDate).format('DD-MMM-YYYY') : null,
        end_date: this.oldFormValue.endDate ? moment(this.oldFormValue.endDate).format('DD-MMM-YYYY') : null,
        milestone_type: this.oldFormValue.milestone_type ? this.getNameByIdPipe.transform(this.oldFormValue.milestone_type, this.milestoneTypeList) : null,
        po_date: this.oldFormValue.po_date ? moment(this.oldFormValue.po_date).format('DD-MMM-YYYY') : null,
        value: this.oldFormValue.milestone_value ? this.oldFormValue.milestone_value : null,
        po_number_drop_down: this.oldFormValue.po_number ? this.oldFormValue.po_number : null,
        weighted_percentage: this.oldFormValue.weighted_percentage ? this.oldFormValue.weighted_percentage : null,
        description: this.oldFormValue.longDescription ? await this.htmlToPlainText(this.oldFormValue.longDescription) : null,
        payment_terms: this.oldFormValue.payment_terms ? this.getNameByIdPipe.transform(this.oldFormValue.payment_terms, this.payment_list) : null,
        invoice_date: this.oldFormValue.invoice_date ? moment(this.oldFormValue.invoice_date).format('DD-MMM-YYYY') : null,
        quote_id: this.oldFormValue.quote_id ? this.oldFormValue.quote_id : null,
        po_value: this.oldFormValue.po_value ? this.oldFormValue.po_value : null,
        po_reference: this.oldFormValue.po_reference ? this.oldFormValue.po_reference : null,
        partner: this.oldFormValue.partner ? this.getNameByIdPipe.transform(this.oldFormValue.partner, this.customer_list) : null,
        owner: this.oldFormValue.employee_name ? this.oldFormValue.employee_name: null
      }
  
    }
 
 
  onCloseClickk(){
      // //console.log('end date',this.stepperFormGroup.get('endDate').value)
      // this.date=moment(this.stepperFormGroup.get('endDate').value).format('YYYY-MM-DD')
      //this.dialogRef.close({messType:"E"})
      if(this.stepperFormGroup.dirty){
      this.utilityService.openConfirmationSweetAlertWithCustom("Are you sure", "You want to Close without saving").then((result) => {
        if (result) {
          this.dialogRef.close({ messType: "E" })
        }
      });
    }
    else {
      this.dialogRef.close({ messType: "E" })
    }
  }
  tag() {
    this.is_tagOpen = true
  }
  valueChange = (value) => {
    if (value)
      this.stepperFormGroup.get('longDescription').patchValue(value);
  }
  onInputChange(value: any) {
    //console.log("va;")
    this.showDisplay = false
    setTimeout(() => {
      this.showDisplay = true
    }, 1);
    this.date = moment(this.stepperFormGroup.get('endDate').value).format('YYYY-MM-DD')

    //console.log(this.date)
  }
  // get getDateDisplay(){
  //   //console.log("va;t" )
  //   //console.log(this.stepperFormGroup.get('endDate').value)
  //  return moment(this.stepperFormGroup.get('endDate').value).format('YYYY-MM-DD')
  // }
  async saveMilestone() {
    //console.log(this.stepperFormGroup.valid)
    //console.log('test')
    this.data = this.stepperFormGroup.value
    let milestone_type_list = _.where(this.milestoneTypeList, {id: this.data.milestone_type})
    
    if (this.mode == 'Create') {
      //this.save='Adding..'
      this.save_disabled = true
      let mandatorycheck = this.checkMandateNotEmpty(this.data)

    
      
      if (mandatorycheck) {
        if (this.withOpportunity) {
          const res = await this.PmBillingService.checkOpportunityBlanketPO(this.portfolio_id, this.project_id, this.data.quote_id);
          if (res['messType'] == 'S') {
              this.blanketPo = res['data'];
          }
        }
        //console.log('pass')
        if (!(this.stepperFormGroup.get('startDate').valid)) {
          const startDate_exceed_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.startDate_exceed_msg ? this.retrieveMessages[0].errors.startDate_exceed_msg : 'Kindly enter valid Start Date!' : 'Kindly enter valid Start Date!';
          this.toasterService.showWarning(startDate_exceed_msg, 10000);
          this.save = 'Add'
          this.save_disabled = false
        }
        else if (!(this.stepperFormGroup.get('endDate').valid)) {
          const endDate_exceed_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.endDate_exceed_msg ? this.retrieveMessages[0].errors.endDate_exceed_msg : 'Kindly enter valid End Date!' : 'Kindly enter valid End Date!';
          this.toasterService.showWarning(endDate_exceed_msg, 10000);
          this.save = 'Add'
          this.save_disabled = false
        }
        else if ((this.data.weighted_percentage > 100 && !this.blanketPo) || this.data.weighted_percentage < 0) {
          const weightedPercent_exceed_msg = this.retrieveMessages.length > 0 && this.retrieveMessages[0].errors.weightedPercent_exceed_msg
            ? this.retrieveMessages[0].errors.weightedPercent_exceed_msg
            : 'Weighted Percentage should be between 0-100';

          this.toasterService.showWarning(weightedPercent_exceed_msg, 10000);
          this.save = 'Add';
          this.save_disabled = false;
        }
        else if (this.data.milestone_value <= 0) {
          const milestoneValue_negative = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.milestoneValue_negative ? this.retrieveMessages[0].errors.milestoneValue_negative : 'Milestone Value should not be Negative or Zero' : 'Milestone Value should not be Negative or Zero';
          this.toasterService.showWarning(milestoneValue_negative, 10000);

          this.save = 'Add'
          this.save_disabled = false
        }
        else if(!await this.checkConditionBeforeSave())
        {
          this.save = 'Add'
          this.save_disabled = false
          return;
        }
        else if(await this.checkDescriptionIsMandatory()){
          const milestoneValue_negative = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.descriptionMandatory ? this.retrieveMessages[0].errors.descriptionMandatory : 'Description is mandatory' : 'Description is mandatory';
          this.toasterService.showWarning(milestoneValue_negative, 10000);

          this.save = 'Add'
          this.save_disabled = false
        }
        else {
          this.data = this.stepperFormGroup.value

          //console.log('success')
          this.stepperFormGroup.patchValue({ ['endDate']: moment(this.stepperFormGroup.get('endDate').value).format('YYYY-MM-DD') || this.stepperFormGroup.get('endDate').value })
          this.stepperFormGroup.patchValue({ ['startDate']: moment(this.stepperFormGroup.get('startDate').value).format('YYYY-MM-DD') || this.stepperFormGroup.get('startDate').value })
          this.stepperFormGroup.patchValue({ ['invoice_date']: moment(this.stepperFormGroup.get('invoice_date').value).format('YYYY-MM-DD') || this.stepperFormGroup.get('invoice_date').value })
          this.data['gantt_id'] = uuidv4()
          //console.log(this.data)
          //console.log(this.data.endDate)
          //console.log(this.isBot)
          //console.log(this.displayTag)
          await this.PmBillingService.saveMilestone(this.portfolio_id, this.project_id, this.data, this.isBot, this.isEmail, this.code, this.displayTag).then((res: any) => {
            if (res['messType'] == 'S') {
              const milestoneCreate_success = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].success.milestoneCreate_success ? this.retrieveMessages[0].success.milestoneCreate_success : 'Milestone Created Successfully' : 'Milestone Created Successfully';
              this.toasterService.showSuccess(milestoneCreate_success, 10000);
              this.dialogRef.close({ messType: "S" })
            }
            else if(res['messType']=="W"){
              this.toasterService.showWarning(res['message'], 10000);
              this.save = 'Add'
              this.save_disabled = false
              // this.dialogRef.close({ messType: "S" })
            }
            else {
              const milestoneCreate_unsuccess = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.milestoneCreate_unsuccess ? this.retrieveMessages[0].errors.milestoneCreate_unsuccess : 'Milestone Creation Unsucessful' : 'Milestone Creation Unsucessful';
              this.toasterService.showError(milestoneCreate_unsuccess);
              this.save_disabled = false
              this.save = 'Add'

            }
          })
        }
      }
      else {
        this.save_disabled = false
        this.save = 'Add'
      }
    }
    if (this.mode == 'Edit') {
      this.save = 'Saving..'
      this.save_disabled = true
      let mandatorycheck = this.checkMandateNotEmpty(this.data)
      let grouped_data = this.stepperFormGroup.get('milestone_group').value
      let grouped_value = 0
      for (let i = 0; i < grouped_data.length; i++) {
        let temp_data = _.where(this.milestone_data, { id: grouped_data[i] })
        if (temp_data.length > 0) {
          for (let item of temp_data[0].milestone_value) {
            if (item['currency_code'] == this.code) {
              grouped_value = grouped_value + item['value']
            }
          }
        }
      }
      if (mandatorycheck) {

        if (this.data.weighted_percentage > 100 || this.data.weighted_percentage < 0) {
          const weightedPercent_exceed_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.weightedPercent_exceed_msg ? this.retrieveMessages[0].errors.weightedPercent_exceed_msg : 'Weighted Percentage should be between 0-100' : 'Weighted Percentage should be between 0-100';
          this.toasterService.showWarning(weightedPercent_exceed_msg, 10000);
          this.save = 'Save'
          this.save_disabled = false
        }
        else if (this.data.milestone_value <= 0) {
          const milestoneValue_negative = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.milestoneValue_negative ? this.retrieveMessages[0].errors.milestoneValue_negative : 'Milestone Value should not be Negative or Zero' : 'Milestone Value should not be Negative or Zero';
          this.toasterService.showWarning(milestoneValue_negative, 10000);
          this.save = 'Save'
          this.save_disabled = false
        }  
        else if(!await this.checkConditionBeforeSave())
        {
          this.save = 'Add'
          this.save_disabled = false
          return;
        }  
        // else if ((parseFloat(this.stepperFormGroup.get('milestone_value').value) + parseFloat(this.totalMilestoneValue) -  grouped_value) > parseFloat(this.quote)) {
        //   const milestoneValue_exceed_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.milestoneValue_exceed_msg ? this.retrieveMessages[0].errors.milestoneValue_exceed_msg : 'Overall milestone value exceeding Order Value' : 'Overall milestone value exceeding Order Value';
        //   this.toasterService.showWarning(milestoneValue_exceed_msg, 10000);
        // }
        // else if (this.stepperFormGroup.get('milestone_value').value < grouped_value) {
        //   this.toasterService.showWarning('Grouping Milestone Value Exceeding Milestone Value', 10000);
        //   this.save = 'Save' 
        //   this.save_disabled = false
        // }
        else {
          this.data = this.stepperFormGroup.value
          this.newData = {
            milestone_name: this.data.milestone_name ? this.data.milestone_name : null,
            start_date: this.data.startDate ? moment(this.data.startDate).format('DD-MMM-YYYY') : null,
            end_date: this.data.endDate ? moment(this.data.endDate).format('DD-MMM-YYYY') : null,
            milestone_type: this.data.milestone_type ? this.getNameByIdPipe.transform(this.data.milestone_type, this.milestoneTypeList) : null,
            po_date: this.data.po_date ? moment(this.data.po_date).format('DD-MMM-YYYY') : null,
            value: this.data.milestone_value ? this.data.milestone_value : null,
            po_number_drop_down: this.data.po_number ? this.data.po_number : null,
            weighted_percentage: this.data.weighted_percentage ? this.data.weighted_percentage : null,
            description: this.data.longDescription ? await this.htmlToPlainText(this.data.longDescription) : null,
            payment_terms: this.data.payment_terms ? this.getNameByIdPipe.transform(this.data.payment_terms, this.payment_list) : null,
            invoice_date: this.data.invoice_date ? moment(this.data.invoice_date).format('DD-MMM-YYYY') : null,
            quote_id: this.data.quote_id ? this.data.quote_id : null,
            po_value: this.data.po_value ? this.data.po_value : null,
            po_reference: this.data.po_reference ? this.data.po_reference : null,
            partner: this.data.partner ? this.getNameByIdPipe.transform(this.data.partner, this.customer_list) : null,
            owner: this.data.employee_name ? this.data.employee_name: null
          }
          if(this.edit_data?.milestone_type == 4 &&  this.stepperFormGroup.get('milestone_type').value == 2 && this.edit_data?.financial_status_id == 15){
        
            await this.PmBillingService.checkStandardAccuralToActualEfforts(this.edit_data?.id).then(async (res)=>{
    
              if(res && res['messType']=="S")
              {
                await this.PmBillingService.updateMilestone(this.oldData, this.newData,this.portfolio_id, this.project_id, this.data, this.isBot, this.isEmail, this.edit_data.id, this.code, this.displayTag).then((res: any) => {
                  if (res['messType'] == 'S') {
                    const milestoneEdit_success = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].success.milestoneEdit_success ? this.retrieveMessages[0].success.milestoneEdit_success : 'Milestone Edited Successfully' : 'Milestone Edited Successfully';
                    this.toasterService.showSuccess(milestoneEdit_success, 10000);
                    this.dialogRef.close({ messType: "S", data: this.edit_data.id })
                  }
                  else if(res['messType']=="W"){
                    this.toasterService.showWarning(res['message'], 10000);
                    this.save_disabled = false
                    this.save = 'Save'
                    //this.dialogRef.close({ messType: "S" })
                  }
                  else {
                    const milestoneEdit_unsuccess = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.milestoneEdit_unsuccess ? this.retrieveMessages[0].errors.milestoneEdit_unsuccess : 'Milestone Edit Unsucessful' : 'Milestone Edit Unsucessful';
                    this.toasterService.showError(milestoneEdit_unsuccess);
                    this.save_disabled = false
                    this.save = 'Save'
      
                  }
                })
              }
              else
              {
                console.log('res',res)
                let message_text = (res && res['messText']) ? res['messText'] : (res && res['message'] ? res['message'] : 'Error in updating Revenue for Changing Standard to Actual Efforts');
                this.toasterService.showWarning((message_text), 10000)
                this.save = 'Save'
                this.save_disabled = false
                return;
              }
            })
          }
          else{
            await this.PmBillingService.updateMilestone(this.oldData, this.newData,this.portfolio_id, this.project_id, this.data, this.isBot, this.isEmail, this.edit_data.id, this.code, this.displayTag).then((res: any) => {
              if (res['messType'] == 'S') {
                const milestoneEdit_success = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].success.milestoneEdit_success ? this.retrieveMessages[0].success.milestoneEdit_success : 'Milestone Edited Successfully' : 'Milestone Edited Successfully';
                this.toasterService.showSuccess(milestoneEdit_success, 10000);
                this.dialogRef.close({ messType: "S", data: this.edit_data.id })
              }
              else if(res['messType']=="W"){
                this.toasterService.showWarning(res['message'], 10000);
                this.save_disabled = false
                this.save = 'Save'
                //this.dialogRef.close({ messType: "S" })
              }
              else {
                const milestoneEdit_unsuccess = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.milestoneEdit_unsuccess ? this.retrieveMessages[0].errors.milestoneEdit_unsuccess : 'Milestone Edit Unsucessful' : 'Milestone Edit Unsucessful';
                this.toasterService.showError(milestoneEdit_unsuccess);
                this.save_disabled = false
                this.save = 'Save'
  
              }
            })
          }
        }
      }
      else {
        this.save_disabled = false
        this.save = 'Save'
      }
      // else {
      //   this.toastr.warning("Kindly Enter all Mandatory fields", 'Warning');
      // }



    }
  }
  isMandate(field: any) {
    const mandate = _.where(this.formConfig, { type: "milestone-creation", field_name: field, is_active: true });
    if (mandate.length > 0) {
      const isMandate = mandate[0].is_mandant;
      return isMandate;
    }
  }
  checkMandateNotEmpty(data: any) {
    let errorOccurred = false;

    if ((!data.milestone_name || data.milestone_name.trim() === '') && this.isMandate('milestone_name')) {
      // const codeEmptymsg = 'Kindly enter Milestone name';
      // this.toastr.error(codeEmptymsg, 'Error');
      const name_empty = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.name_empty ? this.retrieveMessages[0].errors.name_empty : 'Milestone name is Mandatory' : 'Milestone name is mandatory';
      this.toasterService.showWarning(name_empty, 900000000);
      errorOccurred = true;
    }

    else if ((data.startDate === null || data.startDate === undefined || data.startDate === '') && this.isMandate('start_date')) {
      // const nameEmptymsg = 'Kindly choose when is start date?';
      // this.toastr.error(nameEmptymsg, 'Error');

      const startDate_empty = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.startDate_empty ? this.retrieveMessages[0].errors.startDate_empty : 'Milestone Start date is Mandatory' : 'Milestone Start date is mandatory';
      this.toasterService.showWarning(startDate_empty, 10000);
      errorOccurred = true;
    }
    else if (!this.stepperFormGroup.get('startDate').valid) {
      const startDate_empty = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.startDate_empty ? this.retrieveMessages[0].errors.startDate_empty : 'Milestone Start date is Mandatory' : 'Milestone Start date is mandatory';
      this.toasterService.showWarning(startDate_empty, 10000);
      errorOccurred = true;
    }

    else if ((data.endDate === null || data.endDate === undefined || data.endDate === '') && this.isMandate('end_date')) {
      // const nameEmptymsg = 'Kindly choose When Is It due?';
      // this.toastr.error(nameEmptymsg, 'Error');
      const endDate_empty = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.endDate_empty ? this.retrieveMessages[0].errors.endDate_empty : 'Milestone End date is Mandatory' : 'Milestone End date is mandatory';
      this.toasterService.showWarning(endDate_empty, 10000);
      errorOccurred = true;
    }
    else if (!this.stepperFormGroup.get('endDate').valid) {
      const endDate_empty = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.endDate_empty ? this.retrieveMessages[0].errors.endDate_empty : 'Milestone End date is Mandatory' : 'Milestone End date is mandatory';
      this.toasterService.showWarning(endDate_empty, 10000);
      errorOccurred = true;
    }
    else if ((data.invoice_date === null || data.invoice_date === undefined || data.invoice_date === '') && this.isMandate('invoice_date')) {
      // const nameEmptymsg = 'Kindly choose When Is It due?';
      // this.toastr.error(nameEmptymsg, 'Error');
      const endDate_empty = 'Invoice Date is Mandatory';
      this.toasterService.showWarning(endDate_empty, 10000);
      errorOccurred = true;
    }
    else if (!this.stepperFormGroup.get('invoice_date').valid && this.isMandate('invoice_date') ) {
      const endDate_empty = 'Invoice Date is Mandatory';
      this.toasterService.showWarning(endDate_empty, 10000);
      errorOccurred = true;
    }
    else if ((data.employee_name === null || data.employee_name === undefined || data.employee_name === '') && this.isMandate('owner')) {
      // const customerEmptymsg = 'Responsible Is Mandatory';
      // this.toastr.error(customerEmptymsg, 'Error');
      const responsible_empty = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.responsible_empty ? this.retrieveMessages[0].errors.responsible_empty : 'Milestone Responsible is Mandatory' : 'Milestone Responsible is mandatory';
      this.toasterService.showWarning(responsible_empty, 10000);
      errorOccurred = true;
    }

    else if ((data.milestone_value === null || data.milestone_value === undefined || data.milestone_value === '') && this.isMandate('value')) {
      // const industryEmptymsg = 'Milestone Value is Mandatory.';
      // this.toastr.error(industryEmptymsg, 'Error');
      const value_empty = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.value_empty ? this.retrieveMessages[0].errors.value_empty : 'Milestone value is Mandatory' : 'Milestone value is mandatory';
      this.toasterService.showWarning(value_empty, 10000);

      errorOccurred = true;
    }
    else if (data.milestone_value === 0) {
      const value_zero = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.value_empty ? this.retrieveMessages[0].errors.value_empty : 'Milestone value Cannot be 0' : 'Milestone value Cannot be 0';
      this.toasterService.showWarning(value_zero, 10000);
      errorOccurred = true;
    }
    else if ((data.weighted_percentage === null || data.weighted_percentage === undefined || data.weighted_percentage === '') && this.isMandate('weighted_percentage')) {
      // const startDateEmptymsg ='Weighted Percentage is Mandatory.';
      // this.toastr.error(startDateEmptymsg, 'Error');
      errorOccurred = true;
    }
    else if((data.quote_id ===null || data.quote_id ===undefined || data.quote_id === '') && (this.isMandate('quote_id') && this.withOpportunity == 1)){
      const quote_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.value_empty ? this.retrieveMessages[0].errors.value_empty : 'Quote Id is Mandatory' : 'Quote Id is Mandatory';
      this.toasterService.showWarning(quote_msg, 10000);
      errorOccurred = true;
    }
    else if((data.milestone_type === null || data.milestone_type === undefined || data.milestone_type === "") && this.isMandate("milestone_type")){
      const type_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.value_empty ? this.retrieveMessages[0].errors.value_empty : 'Milestone Type is Mandatory' : 'Milestone Type is Mandatory';
      this.toasterService.showWarning(type_msg, 10000);
      errorOccurred = true;
    }

    else if((data.payment_terms === null || data.payment_terms=== undefined || data.payment_terms === "") && this.isMandate("payment_terms")){
      const terms_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.value_empty ? this.retrieveMessages[0].errors.value_empty : 'Payment Terms are Mandatory' : 'Payment Terms are Mandatory';
      this.toasterService.showWarning(terms_msg, 10000);
      errorOccurred = true;
    }
    else if((data.po_number === null || data.po_number=== undefined || data.po_number === "") && this.isMandate("po_number")){
      const terms_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.value_empty ? this.retrieveMessages[0].errors.value_empty : 'Po Number is Mandatory' : 'PO Number is Mandatory';
      this.toasterService.showWarning(terms_msg, 10000);
      errorOccurred = true;
    }
    else if (errorOccurred) {
      //this.toastr.warning('Kindly fill all mandatory!', 'Warning')
      return false; // Return false if any error occurred
    }
    else if (!errorOccurred) {
      this.checkDates(data.startDate, data.endDate);
      //console.log(this.dateLogic)
      if (!this.dateLogic) {
        const validDate_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.validDate_msg ? this.retrieveMessages[0].errors.validDate_msg : 'Kindly enter valid date' : 'Kindly enter valid date';
        this.toasterService.showWarning(validDate_msg, 10000);
        return false
      }
      else {
        return true
      }
    }
    else {
      return true; // Return true if no errors occurred
    }
  }
  // handleValueEmitted(tag:any){
  //       //console.log(tag)
  // }
  addTag() {
    //console.log("test")
    this.isPopupVisible = true
  }
  addDisplayTag() {
    //console.log("test")
    if (this.displayTag.length < 8) {
      this.isDisplayVisible = true
    }
    else {
      const maxTag_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.maxTag_msg ? this.retrieveMessages[0].errors.maxTag_msg : 'Cannot be add more than 8 tags' : 'Cannot be add more than 8 tags';
      this.toasterService.showWarning(maxTag_msg, 10000);
    }
  }
  getTag(id: any) {
    //console.log(id)
    for (let items of this.tags) {
      if (items['id'] == id) {
        //console.log("got")
        this.displayTag.push({ "id": items['id'], "name": items['name'], "color": items['color'] })
        this.isPopupVisible = false
        this.isDisplayVisible = false
        // this.valueEmitted.emit(this.displayTag)
      }
    }
    //console.log(this.displayTag)
  }
  getDisplayTag(id: any) {
    if (this.displayTag.length < 8) {
      //console.log(id)
      this.check = 0
      for (let item of this.displayTag) {
        if (item['id'] == id) {
          this.check = 1
          const duplicateTag_selected_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.duplicateTag_selected_msg ? this.retrieveMessages[0].errors.duplicateTag_selected_msg : 'Tag already selected' : 'Tag already selected';
          this.toasterService.showWarning(duplicateTag_selected_msg, 10000);

        }
      }
      for (let items of this.tags) {

        if (items['id'] == id && this.check == 0) {
          //console.log("got")
          this.displayTag.push({ "id": items['id'], "name": items['name'], "color": items['color'] })
          this.isPopupVisible = false
          this.isDisplayVisible = true
          // this.valueEmitted.emit(this.displayTag)
        }

      }
      //console.log(this.displayTag)
    }
    else {
      const maxTag_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.maxTag_msg ? this.retrieveMessages[0].errors.maxTag_msg : 'Cannot be add more than 8 tags' : 'Cannot be add more than 8 tags';
      this.toasterService.showWarning(maxTag_msg, 10000);
    }
  }
  removeTag(i: any) {
    //console.log(i)
    const indexToDelete = i; // The index of the object you want to delete

    if (indexToDelete >= 0 && indexToDelete < this.displayTag.length) {
      this.displayTag.splice(indexToDelete, 1);
      // this.valueEmitted.emit(this.displayTag)// This will remove one element at the specified index.
    } else {
      //console.log('Invalid index provided');
    }
  }
  performEnter() {
    if (this.add == true && this.stepperFormGroup.get('textInputControl').value.length > 0 && !(this.stepperFormGroup.get('textInputControl').value.trim() === '')) {
      this.isNewTagVisible = true
      this.isPopupVisible = false
    }
    else {
      this.isPopupVisible = true
    }

  }
  getColor(color: any, i: any) {
    this.choosedColor = color
    this.colorChoosen = i


  }
  async createTag() {
    if (this.choosedColor != '#ffff') {
      //console.log(this.choosedColor)
      //console.log(this.stepperFormGroup.get('textInputControl').value)
      this.duplicate = 0
      for (let items of this.searchTag) {
        if (this.stepperFormGroup.get('textInputControl').value.toLowerCase() === items['name'].toLowerCase()) {
          this.duplicate = 1
        }
      }
      if (this.duplicate == 0 && !(this.stepperFormGroup.get('textInputControl').value.trim() === '') && !(this.stepperFormGroup.get('textInputControl').value.length < 3)) {
        await this.PmBillingService.insertTag(this.stepperFormGroup.get('textInputControl').value, this.choosedColor).then((res) => {
          //console.log(res)
          this.displayTag.push({ 'id': res['data'].insertId, 'name': this.stepperFormGroup.get('textInputControl').value, 'color': this.choosedColor })
          this.searchTag.push({ 'id': res['data'].insertId, 'name': this.stepperFormGroup.get('textInputControl').value, 'color': this.choosedColor })
          this.tags = this.searchTag
          this.isNewTagVisible = false
          // this.valueEmitted.emit(this.displayTag)

        })
      }
      else if (this.stepperFormGroup.get('textInputControl').value.trim() === '') {
        const tagName_empty = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.tagName_empty ? this.retrieveMessages[0].errors.tagName_empty : 'Kindly enter tag name' : 'Kindly enter tag name';
        this.toasterService.showWarning(tagName_empty, 10000);
      }
      else if (this.stepperFormGroup.get('textInputControl').value.length < 3) {
        const tagLength_err = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.tagLength_err ? this.retrieveMessages[0].errors.tagLength_err : 'Minimum Tag length should be 3 characters' : 'Minimum Tag length should be 3 characters';
        this.toasterService.showWarning(tagLength_err, 10000);
      }
      else {
        const duplicateTag_present_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.duplicateTag_present_msg ? this.retrieveMessages[0].errors.duplicateTag_present_msg : 'Tag already present' : 'Tag already present';
        this.toasterService.showWarning(duplicateTag_present_msg, 10000);
      }
    }
    else {
      const tagColor_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.tagColor_msg ? this.retrieveMessages[0].errors.tagColor_msg : 'Kindly Choose color to proceed' : 'Kindly Choose color to proceed';
      this.toasterService.showWarning(tagColor_msg, 10000);
    }
  }
  onCloseClick() {
    this.stepperFormGroup.patchValue({ [`textInputControl`]: '' })
  }
  onCloseClickDisplay() {
    this.stepperFormGroup.patchValue({ [`textInputControlDisplay`]: '' })
  }
  async createTagDisplay() {
    if (this.choosedColor != '#ffff') {
      //console.log(this.choosedColor)
      //console.log(this.stepperFormGroup.get('textInputControlDisplay').value)
      this.duplicate = 0
      for (let items of this.searchTag) {
        if (this.stepperFormGroup.get('textInputControlDisplay').value.toLowerCase() === items['name'].toLowerCase()) {
          this.duplicate = 1
          //console.log(this.duplicate)
        }
      }
      if (this.duplicate == 0 && !(this.stepperFormGroup.get('textInputControlDisplay').value.trim() === '') && !(this.stepperFormGroup.get('textInputControlDisplay').value.length < 3)) {
        await this.PmBillingService.insertTag(this.stepperFormGroup.get('textInputControlDisplay').value, this.choosedColor).then((res) => {
          //console.log(res)
          this.displayTag.push({ 'id': res['data'].insertId, 'name': this.stepperFormGroup.get('textInputControlDisplay').value, 'color': this.choosedColor })
          this.searchTag.push({ 'id': res['data'].insertId, 'name': this.stepperFormGroup.get('textInputControlDisplay').value, 'color': this.choosedColor })
          //console.log(this.searchTag)
          this.tags = this.searchTag
          this.isNewTagVisibleDisplay = false
          //  this.valueEmitted.emit(this.displayTag)
        })
      }
      else if (this.stepperFormGroup.get('textInputControlDisplay').value.trim() === '') {
        const tagName_empty = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.tagName_empty ? this.retrieveMessages[0].errors.tagName_empty : 'Kindly enter tag name' : 'Kindly enter tag name';
        this.toasterService.showWarning(tagName_empty, 10000);
      }
      else if (this.stepperFormGroup.get('textInputControlDisplay').value.length < 3) {
        const tagLength_err = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.tagLength_err ? this.retrieveMessages[0].errors.tagLength_err : 'Minimum Tag length should be 3 characters' : 'Minimum Tag length should be 3 characters';
        this.toasterService.showWarning(tagLength_err, 10000);
      }
      else {
        const duplicateTag_present_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.duplicateTag_present_msg ? this.retrieveMessages[0].errors.duplicateTag_present_msg : 'Tag already present' : 'Tag already present';
        this.toasterService.showWarning(duplicateTag_present_msg, 10000);
      }
    }
    else {
      const tagColor_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.tagColor_msg ? this.retrieveMessages[0].errors.tagColor_msg : 'Kindly Choose color to proceed' : 'Kindly Choose color to proceed';
      this.toasterService.showWarning(tagColor_msg, 10000);
    }
  }
  performEnterDisplay() {
    if (this.add == true && this.stepperFormGroup.get('textInputControlDisplay').value.length > 0 && !(this.stepperFormGroup.get('textInputControlDisplay').value.trim() === '')) {
      this.isNewTagVisibleDisplay = true
      this.isDisplayVisible = false
    }
    else {
      this.isDisplayVisible = true
    }

  }
  compareDate(date1: any, date2: any) {

    if ((date1 == "" || date1 == null || date1 == undefined) && (date2 == "" || date2 == null || date2 == undefined)) {
      return ''
    }
    else if (date1 == "" || date1 == null || date1 == undefined) {
      return date2
    }
    else if (date2 == "" || date2 == null || date2 == undefined) {
      return date1
    }
    else {
      date1 = moment(date1).format('YYYY-MM-DD')
      date2 = moment(date2).format('YYYY-MM-DD')
      if (date1 < date2)
        return date1
      else
        return date2
    }
  }
  compareDateMinimum(date1: any, date2: any) {

    if ((date1 == "" || date1 == null || date1 == undefined) && (date2 == "" || date2 == null || date2 == undefined)) {
      return ''
    }
    else if (date1 == "" || date1 == null || date1 == undefined) {
      return date2
    }
    else if (date2 == "" || date2 == null || date2 == undefined) {
      return date1
    }
    else {
      date1 = moment(date1).format('YYYY-MM-DD')
      date2 = moment(date2).format('YYYY-MM-DD')
      if (date1 > date2)
        return date1
      else
        return date2
    }
  }
  splitPercentKeyDown(event) {
    //console.log(event.target.value.toString().length)
    //console.log(event.target.value.toString())
    if (this.stepperFormGroup.get('weighted_percentage').value > 100) {
      const weightedPercent_exceed_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.weightedPercent_exceed_msg ? this.retrieveMessages[0].errors.weightedPercent_exceed_msg : 'Weighted Percentage should be between 0-100' : 'Weighted Percentage should be between 0-100';
      this.toasterService.showWarning(weightedPercent_exceed_msg, 10000);
      this.stepperFormGroup.patchValue({ ['weighted_percentage']: 100 })
    }
    if (this.stepperFormGroup.get('weighted_percentage').value < 1) {
      const weightedPercent_exceed_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.weightedPercent_exceed_msg ? this.retrieveMessages[0].errors.weightedPercent_exceed_msg : 'Weighted Percentage should be between 0-100' : 'Weighted Percentage should be between 0-100';
      this.toasterService.showWarning(weightedPercent_exceed_msg, 10000);
      this.stepperFormGroup.patchValue({ ['weighted_percentage']: '' })
    }
  }
  valueKeyUp(event) {
    //console.log(event.target.value.toString().length)
    //console.log(event.target.value.toString())
    if (this.stepperFormGroup.get('milestone_value').value > 10000000000000000000000000000000000000) {
      this.stepperFormGroup.patchValue({ ['milestone_value']: 10000000000000000000000000000000000000 })
    }
    if (this.stepperFormGroup.get('milestone_value').value < 1) {
      const milestoneValue_number = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.milestoneValue_number ? this.retrieveMessages[0].errors.milestoneValue_number : 'Milestone value should be only numbers' : 'Milestone value should be only numbers';
      this.toasterService.showWarning(milestoneValue_number, 10000);
      this.stepperFormGroup.patchValue({ ['milestone_value']: '' })
    }
  }
  checkDates(start: any, end: any) {
    //console.log("date validation entry")
    const startDate = moment(start);
    const endDate = moment(end);
    //console.log(start, end)
    if (startDate.isValid() && endDate.isValid()) {
      if (startDate.isBefore(endDate) || startDate.isSame(endDate)) {
        this.dateLogic = true;
      }
      else {
        this.dateLogic = false;
      }
    } else {
      this.dateLogic = false;
      console.error('Invalid date format');
    }
  }
  getPoNumber(name, event: Event) {
    this.stepperFormGroup.patchValue({ po_number: name })
  }

  // 1. Milestone Data
  // 2. Milestone Type
  // 3. Milestone Grouping Type List
  // 4. Milestone Grouping Status list
  // 5. Existing Milestone Grouping Data

  async getMilestoneGroupList(milestone_type_id, milestone_id)
  {
    
      let milestone_type_list = _.where(this.milestoneTypeList, {id: milestone_type_id})

      if(milestone_type_list.length>0)
      {
          if(milestone_type_list[0]['milestone_grouping']==1)
          {
              let typeList= milestone_type_list[0]['milestone_grouping_milestone_type']!=null ? typeof milestone_type_list[0]['milestone_grouping_milestone_type'] =="string" ? JSON.parse(milestone_type_list[0]['milestone_grouping_milestone_type']) : []: [];
              let statusList= milestone_type_list[0]['milestone_grouping_status_id']!=null ? typeof milestone_type_list[0]['milestone_grouping_status_id'] =="string" ? JSON.parse(milestone_type_list[0]['milestone_grouping_status_id']) : []: [];

              if(this.mode == "Create")
              {
                  let childIds = _.pluck(this.milestone_grouped_data, "childId")
                  let parentIds = _.pluck(this.milestone_grouped_data,"parentId")

                  let data = _.filter(this.milestone_data,(res)=>{
                    if(_.contains(typeList, res['milestone_type']) && _.contains(statusList, res['milestone_status_id']))
                    {
                        if(!_.contains(childIds, res['id']))
                        {
                            return true;
                        }
                    }
                  })

                  let checkCondition = _.where(this.formConfig, {type:"milestone-creation", field_name:"group_milestone_quote_id", is_active: true})

                  if(checkCondition.length>0 && this.withOpportunity==1)
                  {
                      let advance_quote_id = this.stepperFormGroup.get("quote_id").value;

                      this.milestoneGroupList = _.where(data,{quote_id: advance_quote_id})
                  }
                  else
                  {
                    this.milestoneGroupList = data;
                  }
              }
              else
              {
                 let existingGroupingData = _.pluck(_.where(this.milestone_grouped_data, {parentId: milestone_id}), "childId")

                 console.log("Existing Grouping Data", existingGroupingData)

                 let data = _.filter(this.milestone_data,(res)=>{
                  if((_.contains(typeList, res['milestone_type']) && _.contains(statusList, res['milestone_status_id'])) || _.contains(existingGroupingData, res['id']))
                  {
                      return true;
                  }
                })

                let checkCondition = _.where(this.formConfig, {type:"milestone-creation", field_name:"group_milestone_quote_id", is_active: true})

                if(checkCondition.length>0 && this.withOpportunity==1)
                {
                    let advance_quote_id = this.stepperFormGroup.get("quote_id").value;

                    console.log(advance_quote_id, data)

                    this.milestoneGroupList = _.where(data,{quote_id: advance_quote_id})
                }
                else
                {
                  this.milestoneGroupList = data;
                }
              }
          }
      }
  }


  getSumOfGroupedMilestoneValue()
  {
    let grouped_data = this.stepperFormGroup.get('milestone_group').value
    let grouped_value = 0
    for (let i = 0; i < grouped_data.length; i++) 
    {
      let temp_data = _.where(this.milestone_data, { id: grouped_data[i] })
      if (temp_data.length > 0) 
      {
        for (let item of temp_data[0].milestone_value) 
        {
          if (item['currency_code'] == this.code)
          {
            grouped_value = grouped_value + item['value']
          }
        }
      }
    }

    return grouped_value
    
  }


  validateParentChildMilestoneValue(){
      let milestone_value = this.stepperFormGroup.get('milestone_value').value
      let groupedMilestoneValue = this.getSumOfGroupedMilestoneValue()


      return milestone_value-groupedMilestoneValue
  }


  async checkConditionBeforeSave(){
    let milestone_type = this.stepperFormGroup.get("milestone_type").value

    let milestone_type_list = _.where(this.milestoneTypeList, {id: milestone_type})

    let validateCondition = true;

    if(milestone_type_list.length>0)
    {
        if(milestone_type_list[0]['milestone_grouping']==1)
        {
            let remainingValue = this.validateParentChildMilestoneValue()

            let checkCondition = _.where(this.formConfig, {type:"milestone-creation", field_name:"check_milestone_group", is_active: true})

            if(checkCondition.length>0)
            {
                if(remainingValue != 0)
                {
                  if(checkCondition['is_mandant'])
                  {
                      this.toasterService.showError("Milestone Value is not matching with Tagged Milestone Value")
                      return false;
                  }
                  else
                  { 
                      let title = ""
                      if(remainingValue<0)
                      {
                          title ="lesser"
                      }
                      else if(remainingValue >0)
                      {
                          title ="greater"
                      }
                       
                      let checkCondition = true;
                      await this.utilityService.openConfirmationSweetAlertWithCustom("Milestone Value is "+title+" than Tagged Milestone" , "Are you sure you want to "+this.mode+ " milestone?").then((result) => {
                        if (result) {
                          checkCondition = true;
                        }
                        else{
                          checkCondition = false;
                        }
                      });

                      return checkCondition;
                  }
                }
                else
                {
                  return validateCondition
                }
            }
            else
            {
                return validateCondition
            }
        }
    }
   
    return validateCondition
  }


  async changeMilestoneType(){

    this.isChangeMilestoneType = true;

      if(this.stepperFormGroup.get('milestone_type').value == 4)
      {
        await this.PmBillingService.checkActualEffortsMilestone(this.portfolio_id, this.project_id).then(async (res) => {
    
          // If the first API response indicates success (messType: "S")
          if (res['messType'] == "S") {
        
              // Second API Call to check another condition
              const ActualEffortValue = await this.PmBillingService.checkActualEffortValue(this.portfolio_id,this.project_id,parseInt(this.edit_data.quote_id),parseInt(this.edit_data.id));
        
              // If the second API response is also successful or meets the required condition
              if(ActualEffortValue['messType']=="S")
                {
              
                  this.stepperFormGroup.patchValue({
                    'milestone_type': 2
                  })
                  this.isChangeMilestoneType = false
                  let milestone = this.milestoneTypeList.find(item => item.id === 2);
                  let  milestone_name = (milestone && milestone.name) ? milestone.name : 'Prebilled';
                  
                  this.toasterService.showSuccess(`Milestone Type marked as ${milestone_name} `, 10000);
                }
                else
                {
                  this.toasterService.showWarning(ActualEffortValue['message'], 10000)
        
                  this.isChangeMilestoneType = false
                }
              } else {
                this.toasterService.showWarning(res['message'], 10000);
                this.isChangeMilestoneType = false;
            }
          });     
        
      }
      else
      {
        this.stepperFormGroup.patchValue({
          'milestone_type': 4
        })
        this.isChangeMilestoneType = false

        this.toasterService.showSuccess("Milestone Type marked as Standard!", 10000);
      }

    



    
  }

  async htmlToPlainText(html: string): Promise<string> {
    // Create a temporary element to hold the HTML
    const temporaryElement = document.createElement('div');
    temporaryElement.innerHTML = html;
    // Extract plain text from the element
    return temporaryElement.textContent || temporaryElement.innerText || '';
  }
  
  checkDescriptionIsMandatory(){
    let commentMandatory = _.where(this.formConfig,{type:"description", field_name:"milestone-creation", is_active: true, is_mandant: true})

    if(commentMandatory.length>0 && this.stepperFormGroup.get('milestone_type').value == 6)
    {
      let value =  this.stepperFormGroup.get('longDescription').value;

      if(value)
      {
        if(value.trim().length==0)
          return true;
        else
          return false;
      }
      else
      {
        return true;
      }
    }
    else
    {
      return false;
    }
  }
}
export interface DialogData {
  mode: any;
  data: any;
  tags: any;
  at_risk: any;
  button: any;
  milestoneWriteAccess: any;
}
