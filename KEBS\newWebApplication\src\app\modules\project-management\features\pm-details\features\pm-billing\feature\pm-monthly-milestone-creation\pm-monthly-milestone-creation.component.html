<div class="generate-milestone-creation">
    <div class="row header">
        <div class="header-title name">
           Generate Milestone
        </div>
        <div>
            <mat-icon class="close-button" (click)="onCloseClick()">clear</mat-icon>
        </div> 
    </div>
    <div class="col-12 content">
        <form [formGroup]="stepperFormGroup" style="margin-top: 22px;">
            <div class="row" style="margin-top: 9px;">
                <div class="col-3"  *ngIf="('start_date' | checkActive : this.formConfig: 'generate-milestone-creation')">
                    <div class="content-title">
                        {{('start_date' | checkLabel : this.formConfig: 'generate-milestone-creation': 'Start Date')}}
                        <span class="required-star"
                        *ngIf="('start_date' | checkMandatedField : this.formConfig: 'generate-milestone-creation')">
                        *</span>
                        <span *ngIf="('start_date' | checkInfoIcon : this.formConfig: 'generate-milestone-creation')">
                            <mat-icon class="info-icon"
                            tooltip="{{('start_date' | checkTooltip : this.formConfig: 'generate-milestone-creation': 'Start Date')}}"
                            >
                                info_outline
                            </mat-icon>
                        </span>
                    </div>

                    <mat-form-field class="input-field start_date" appearance="outline">

                        <input matInput formControlName="start_date" [matDatepicker]="psdDp" name="start_date" [min]="project_start_date" [max]="compareDate(this.stepperFormGroup.get('end_date').value,project_end_date)"
                            placeholder="DD-MMM-YYYY" />
                        <mat-datepicker-toggle matSuffix [for]="psdDp"></mat-datepicker-toggle>
                        <mat-datepicker #psdDp></mat-datepicker>
                    </mat-form-field>
                </div>
                <div class="col-3"  *ngIf="('end_date' | checkActive : this.formConfig: 'generate-milestone-creation')">
                    <div class="content-title">
                        {{('end_date' | checkLabel : this.formConfig: 'generate-milestone-creation': 'End Date')}}
                        <span class="required-star"
                        *ngIf="('end_date' | checkMandatedField : this.formConfig: 'generate-milestone-creation')">
                        *</span>
                        <span *ngIf="('end_date' | checkInfoIcon : this.formConfig: 'generate-milestone-creation')">
                            <mat-icon class="info-icon"
                            tooltip="{{('end_date' | checkTooltip : this.formConfig: 'generate-milestone-creation': 'End Date')}}"
                            >
                                info_outline
                            </mat-icon>
                        </span>
                    </div>

                    <mat-form-field class="input-field start_date" appearance="outline" >

                        <input matInput formControlName="end_date" [matDatepicker]="psdDp" name="end_date"  [min]="compareDateMinimum(this.stepperFormGroup.get('start_date').value,project_start_date)" [max]="project_end_date"
                            placeholder="DD-MMM-YYYY"/>
                        <mat-datepicker-toggle matSuffix [for]="psdDp"></mat-datepicker-toggle>
                        <mat-datepicker #psdDp></mat-datepicker>
                    </mat-form-field>
                </div>
                <div class="col-6" *ngIf="('milestone_name' | checkActive : this.formConfig: 'generate-milestone-creation')">
                    <div class="content-title">
                        {{('milestone_name' | checkLabel : this.formConfig: 'generate-milestone-creation': 'Milestone Name')}}
                        <span class="required-star"
                        *ngIf="('milestone_name' | checkMandatedField : this.formConfig: 'generate-milestone-creation')">
                        *</span>
                        <span *ngIf="('milestone_name' | checkInfoIcon : this.formConfig: 'generate-milestone-creation')">
                            <mat-icon class="info-icon"
                            tooltip="{{('milestone_name' | checkTooltip : this.formConfig: 'generate-milestone-creation': 'milestone name')}}"
                            >
                                info_outline
                            </mat-icon>
                        </span>
                    </div>

                    <mat-form-field class="input-field milestone_name" appearance="outline">
                        <input matInput [maxlength]="(name_length ? name_length : 300)" placeholder="Milestone Name" formControlName="milestone_name"/>
                    </mat-form-field>

                </div>
            </div>
        <div class="row">
           
                <div class="col-3" *ngIf="('repeat' | checkActive : this.formConfig: 'generate-milestone-creation')">
                    <div class="content-title">
                        {{('repeat' | checkLabel : this.formConfig: 'generate-milestone-creation': 'Repeat')}}
                        <span class="required-star"
                        *ngIf="('repeat' | checkMandatedField : this.formConfig: 'generate-milestone-creation')">
                        *</span>
                        <span *ngIf="('repeat' | checkInfoIcon : this.formConfig: 'generate-milestone-creation')">
                            <mat-icon class="info-icon"
                            tooltip="{{('repeat' | checkTooltip : this.formConfig: 'generate-milestone-creation': 'repeat')}}"
                            >
                                info_outline
                            </mat-icon>
                        </span>
                    </div>
                    <app-input-search-name class="repeat" [showSelect]="false" [list]="repeat_list" placeholder="Select"
                        formControlName="repeat">
                    </app-input-search-name>
                </div>

                <!-- -------- -->
                <div class="col-3" *ngIf="('value' | checkActive : this.formConfig: 'generate-milestone-creation')">
                    <div class="content-title">
                        {{(this.stepperFormGroup.get('repeat').value == 1) ? ('value_weekly' | checkLabel : this.formConfig: 'generate-milestone-creation': 'Per Milestone Value') : ('value_monthly' | checkLabel : this.formConfig: 'generate-milestone-creation': 'Per Milestone Value')}} ({{this.code}})
                        <span class="required-star"
                            *ngIf="('value' | checkMandatedField : this.formConfig: 'generate-milestone-creation')">
                            *</span>  
                            <span *ngIf="('value' | checkInfoIcon : this.formConfig: 'generate-milestone-creation')">
                                <mat-icon class="info-icon"
                                tooltip="{{('value' | checkTooltip : this.formConfig: 'generate-milestone-creation': 'Value')}}"
                                >
                                    info_outline
                                </mat-icon>
                            </span>
                    </div>
                   
                    <mat-form-field class="input-field value" appearance="outline">
                        <input matInput type="text" digitOnly [isPercentage]="false" [allowDecimal]="true" [digitsAllowed]="20" class="no-updown-arrows" placeholder="Value" formControlName="milestone_value"/>
                       
                    </mat-form-field>
                   
                </div>
                <div class="col-3" *ngIf="('percentage' | checkActive : this.formConfig: 'generate-milestone-creation')">
                    <div class="content-title">
                        {{('percentage' | checkLabel : this.formConfig: 'generate-milestone-creation': 'Percentage')}}
                        <span class="required-star"
                            *ngIf="('percentage' | checkMandatedField : this.formConfig: 'generate-milestone-creation')">
                            *</span>
                            <span *ngIf="('percentage' | checkInfoIcon : this.formConfig: 'generate-milestone-creation')">
                                <mat-icon class="info-icon"
                                tooltip="{{('percentage' | checkTooltip : this.formConfig: 'generate-milestone-creation': 'percentage')}}"
                                >
                                    info_outline
                                </mat-icon>
                            </span>
                    </div>
                  
                    <mat-form-field class="input-field percentage" [ngStyle]="{'background-color':percentage_color}" appearance="outline">
                        <input matInput type="text"  digitOnly [isPercentage]="true" [allowDecimal]="true" class="no-updown-arrows" placeholder="Percentage" formControlName="percentage" [readonly]="percentage_disabled"/>
                    </mat-form-field>
                </div>
                <div class="col-3" *ngIf="('quote' | checkActive : this.formConfig: 'generate-milestone-creation') && withOpportunity==1">
                    <div class="content-title">
                        {{('quote' | checkLabel : this.formConfig: 'generate-milestone-creation': 'PO Value')}}
                        <span class="required-star"
                        *ngIf="('quote' | checkMandatedField : this.formConfig: 'generate-milestone-creation')">
                        *</span>
                        <span *ngIf="('quote' | checkInfoIcon : this.formConfig: 'generate-milestone-creation')">
                            <mat-icon class="info-icon"
                            tooltip="{{('quote' | checkTooltip : this.formConfig: 'generate-milestone-creation': 'quote')}}"
                            >
                                info_outline
                            </mat-icon>
                        </span>
                    </div>
                  
                    <mat-form-field class="input-field quote" appearance="outline">
                        <input matInput type="number" placeholder="Value" class="no-updown-arrows" formControlName="project_value" readonly="true"/>
                        <span class="currency-code">{{this.code}}</span>
                    </mat-form-field>
                  
                </div>
            
        </div>
        <div class="row">    
            <div class="col-3" *ngIf="('quote_id' | checkActive : this.formConfig: 'generate-milestone-creation') && withOpportunity==1">
                <div class="content-title">
                    {{('quote_id' | checkLabel : this.formConfig: 'generate-milestone-creation': 'Quote ID')}}
                    <span class="required-star"
                    *ngIf="('quote_id' | checkMandatedField : this.formConfig: 'generate-milestone-creation')">
                    *</span>
                    <span *ngIf="('quote_id' | checkInfoIcon : this.formConfig: 'generate-milestone-creation')">
                        <mat-icon class="info-icon"
                        tooltip="{{('quote_id' | checkTooltip : this.formConfig: 'generate-milestone-creation': 'quote_id')}}"
                        >
                            info_outline
                        </mat-icon>
                    </span>
                </div>
                <app-input-search-name  class="repeat" [showSelect]="false" [list]="quote_id_list" placeholder="Select"
                    [required]="('quote_id' | checkMandatedField : this.formConfig: 'generate-milestone-creation')"
                    formControlName="quote_id">
                </app-input-search-name>
            </div> 
            <div class="col-3"
            *ngIf="('po_number_drop_down' | checkActive : this.formConfig: 'generate-milestone-creation')">
            <div class="content-title">
                {{('po_number_drop_down' | checkLabel : this.formConfig: 'generate-milestone-creation': 'PO Number')}}
                <span class="required-star"
                    *ngIf="('po_number_drop_down' | checkMandatedField : this.formConfig: 'generate-milestone-creation')">
                    *</span>
                <span
                    *ngIf="('po_number_drop_down' | checkInfoIcon : this.formConfig: 'generate-milestone-creation')">
                    <mat-icon class="info-icon"
                        tooltip="{{('po_number_drop_down' | checkTooltip : this.formConfig: 'generate-milestone-creation': 'po number')}}">
                        info_outline
                    </mat-icon>
                </span>
            </div>
            <app-input-search-name [showSelect]="false" class="repeat" [list]="po_number_dropdown_list"
                [required]="('po_number_drop_down' | checkMandatedField : this.formConfig: 'generate-milestone-creation')"
                placeholder="Select One" formControlName="po_number">
            </app-input-search-name>
            </div>

            <div class="col-3"
            *ngIf="('milestone_type' | checkActive : this.formConfig: 'generate-milestone-creation')">
            <div class="content-title">
                {{('milestone_type' | checkLabel : this.formConfig: 'generate-milestone-creation': 'Milestone Type')}}
                <span class="required-star"
                    *ngIf="('milestone_type' | checkMandatedField : this.formConfig: 'generate-milestone-creation')">
                    *</span>
                <span
                    *ngIf="('milestone_type' | checkInfoIcon : this.formConfig: 'generate-milestone-creation')">
                    <mat-icon class="info-icon"
                        tooltip="{{('milestone_type' | checkTooltip : this.formConfig: 'generate-milestone-creation': 'Milestone Type')}}">
                        info_outline
                    </mat-icon>
                </span>
            </div>
            <app-input-search-name [showSelect]="false" class="repeat" [list]="milestoneTypeList"
                [required]="('milestone_type' | checkMandatedField : this.formConfig: 'generate-milestone-creation')"
                placeholder="Select One" formControlName="milestone_type">
            </app-input-search-name>
            </div>
        </div>
        <!-- <div style="display: flex;"> -->
            
        <!-- </div> -->
        </form>
    </div>
    <div class="row footer-buttons">
        
        <div class="col-11 pt-3">
            <span class="notes-header">Note:</span><span class="notes-body"> {{('notes' | checkLabel : this.formConfig: 'generate-milestone-creation': 'Milestone Gross Value should not exceed Order Value')}}</span>
        </div>
        <div class="col-1">
            <button mat-button *ngIf = "!saveDisabled" class="button-next" (click)="generateMilestone()" [disabled]="saveDisabled">{{this.save_name}}</button>
            <div class="button-next" *ngIf="saveDisabled">
                <mat-spinner class="green-spinner" diameter="20"></mat-spinner>
            </div>
        </div>
    </div>
    </div>
