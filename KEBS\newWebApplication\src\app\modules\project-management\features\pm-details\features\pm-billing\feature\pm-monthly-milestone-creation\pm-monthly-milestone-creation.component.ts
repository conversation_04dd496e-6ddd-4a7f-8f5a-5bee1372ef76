
import { Component, OnInit,Inject, HostListener, InjectionToken } from '@angular/core';
import { MatDialog,MatDialogRef} from '@angular/material/dialog';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
import { FormArray, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { PmMasterService } from 'src/app/modules/project-management/services/pm-master.service';
import { Router } from '@angular/router';
import{PmBillingService} from './../../services/pm-billing.service'
import { MomentDateAdapter, MAT_MOMENT_DATE_ADAPTER_OPTIONS } from '@angular/material-moment-adapter';
import { DateAdapter, MAT_DATE_LOCALE, MAT_DATE_FORMATS } from '@angular/material/core';
import { ToastrService } from 'ngx-toastr';
import * as moment from 'moment';
import * as _ from 'underscore';
import { v4 as uuidv4 } from 'uuid';
import { ToasterService } from 'src/app/services/toaster-service/toaster.service';
import { UtilityService } from 'src/app/services/utility/utility.service';
import { ToasterMessageService } from 'src/app/modules/project-management/shared-lazy-loaded/components/toaster-message/toaster-message.service';
export const TOASTER_MILE_MESSAGE_SERVICE_TOKEN = new InjectionToken<ToasterMessageService>('TOASTER_MILE_MESSAGE_SERVICE_TOKEN');


@Component({
  selector: 'app-pm-monthly-milestone-creation',
  templateUrl: './pm-monthly-milestone-creation.component.html',
  styleUrls: ['./pm-monthly-milestone-creation.component.scss'],
  providers: [
    {
      provide: DateAdapter,
      useClass: MomentDateAdapter,
      deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS]
    },
    {
      provide: MAT_DATE_FORMATS,
      useValue: {
        parse: {
          dateInput: "DD-MMM-YYYY"
        },
        display: {
          dateInput: "DD-MMM-YYYY",
          monthYearLabel: "MMM YYYY"
        }
      }
    },
    { provide: TOASTER_MILE_MESSAGE_SERVICE_TOKEN, useClass: ToasterMessageService }
  ]
})
export class PmMonthlyMilestoneCreationComponent implements OnInit {
  @HostListener('window:keyup.esc') onKeyUp() {
    this.onCloseClick()
  }
  stepperFormGroup = this.formBuilder.group({
    start_date:[''],
    milestone_name:[''],
    repeat:[''],
    milestone_value:[''],
    percentage:[''],
    project_value:[''],
    end_date:[''], 
    quote_id:[],
    po_number:[''],
    milestone_type:['']
  });
  repeat_list:any=[
    {
    id:2,
    name:'Monthly'
  }]
  formConfig:any
  project_id:any
  portfolio_id:any
  percentage:any
  value:any
  mValue:boolean=true
  mPercentage:boolean=true
  project_start_date:Date
  project_end_date:Date
  data:any
  saveDisabled:boolean=false
  save_name:any='Generate'
  code:any
  percentage_disabled:boolean=true
  percentage_color:any='#E8E9EE'
  retrieveMessages: any;
  monthlyDates: { start: string; end: string; name:string;value:number;percentage:number;code:string,gantt_id:string,quote_id:string,po_number:string, milestone_type: any}[] = [];
  weeklyDates: { start: string; end: string;name:string;value:number;percentage:number,code:string,gantt_id:string,quote_id:string,po_number:string, milestone_type: any}[] = [];
  quote:any
  perMilestoneValue:any
  totalMilestoneValue:any=0
  number_of_milestone:any=0
  name_length: any;
  button: any;
  fieldOutline: any;
  noteHeader: any;
  monthConfig:boolean=false
  name_config:any
  milestone_grouped_data:any=[]
  po_number_dropdown_list:any=[]
  milestoneTypeList: any=[];
  matrixConfig: any=[];
  withOpportunity:any
  first_po_number:any
  first_quote_id:any
  quote_id_list:any=[]
  service_type_id: any;
  blanketPo: boolean = false;
  constructor(@Inject(TOASTER_MILE_MESSAGE_SERVICE_TOKEN) private toasterService: ToasterMessageService,private PmBillingService:PmBillingService,public dialogRef: MatDialogRef<PmMonthlyMilestoneCreationComponent>,private formBuilder: FormBuilder,private pmMasterService: PmMasterService,private router:Router, private utilityService: UtilityService, @Inject(MAT_DIALOG_DATA) public dialogData: DialogData) { }
 
  async ngOnInit(){
    // this.button = this.dialogData.button ? this.dialogData.button : '';
    // document.documentElement.style.setProperty('--milestoneButton', this.button)
    
    this.project_id=this.router.url.split("/")[5]
    this.portfolio_id=this.router.url.split("/")[3]
    await this.pmMasterService.getPMFormCustomizeConfigV().then((res: any) => {
      if (res) {
        this.formConfig = res;
      }
    });

    this.stepperFormGroup.patchValue({
      "milestone_type": 4
    })

    let weekly_milestone=_.where(this.formConfig,{type:"generate-milestone-creation", field_name:"weekly_milestone", is_active: true})
    if(weekly_milestone.length>0)
    {
      this.repeat_list.push({
        "id": 1,
        "name":"Weekly"
      })
    }
    this.retrieveMessages = _.where(this.formConfig, { type: "generate-milestone-creation", field_name: "messages", is_active: true });
    const retrieveNameLength = _.where(this.formConfig, { type: "generate-milestone-creation", field_name: "milestone_name", is_active: true });
    const retrieveStyles = _.where(this.formConfig, { type: "project-theme", field_name: "styles", is_active: true });
    this.button = retrieveStyles.length > 0 ? retrieveStyles[0].data.button_color ? retrieveStyles[0].data.button_color : "#90ee90" : "#90ee90";
    document.documentElement.style.setProperty('--milestoneButton', this.button)
    this.noteHeader = retrieveStyles.length > 0 ? retrieveStyles[0].data.note_header ? retrieveStyles[0].data.note_header : "rgb(66, 191, 221)" : "rgb(66, 191, 221)";
    document.documentElement.style.setProperty('--milestoneNoteheader', this.noteHeader)
    this.fieldOutline = retrieveStyles.length > 0 ? retrieveStyles[0].data.field_outline_color ? retrieveStyles[0].data.field_outline_color : "#808080" : "#808080";
    document.documentElement.style.setProperty('--monthlyMilestoneField', this.fieldOutline);
    this.name_length = retrieveNameLength.length > 0 ? retrieveNameLength[0].maxLength : 300;
    const monthConfig = _.where(this.formConfig, { type: "generate-milestone-creation", field_name: "month_config", is_active: true });
    this.monthConfig = monthConfig.length > 0 ? monthConfig[0].is_active : false;
    const name_config = _.where(this.formConfig, { type: "generate-milestone-creation", field_name: "name_config", is_active: true });
    this.name_config = name_config.length > 0 ? name_config[0].label : 'Professional Services rendered for the Month';
    await this.PmBillingService.getProjectQuote(this.portfolio_id,this.project_id).then((res)=>{
      if(res['messType']=='S')
      {
        this.stepperFormGroup.patchValue({['project_value']:''})
        this.project_end_date=new Date(moment(res['data'][0].planned_end_date).utc().format('YYYY-MM-DD'))
        this.project_start_date=new Date(moment(res['data'][0].planned_start_date).utc().format('YYYY-MM-DD'))
        this.code=res['data'][0].currency_code
        this.quote=res['data'][0].planned_quote
        this.withOpportunity=res['data'][0].with_opportunity
        this.service_type_id = res['data'][0].service_type_id
        console.log(this.project_end_date)
        this.stepperFormGroup.patchValue({['start_date']:this.project_start_date})
        this.stepperFormGroup.patchValue({['end_date']:this.project_end_date})
        if(res['data'][0].planned_quote==0){
          this.percentage_disabled=false
         this.percentage_color='white'
        }
      }
      else{
        this.percentage_disabled=false
        this.percentage_color='white'
        this.stepperFormGroup.patchValue({['project_value']:''})
      }
    })

    if (this.withOpportunity == 0) {
      this.PmBillingService.getProjectPoMaster(this.project_id, null).then((res) => {
        if (res['messType'] == 'S' && res['data'].length > 0) {
          this.po_number_dropdown_list = res['data']
          if (res['data'].length == 1) {
            this.stepperFormGroup.patchValue({ ['po_number']: res['data'][0].id })
            const po_details = _.where(this.po_number_dropdown_list,{id: res['data'][0].id})
            if(po_details.length>0)
            {
              this.first_po_number = po_details[0].id;
              const orderValueJson = po_details[0].po_value ? JSON.parse(po_details[0].po_value) : 0;
              const currency_code = po_details[0].currency ? po_details[0].currency : 'USD';
              const orderValue = _.filter(orderValueJson, item => item.currency_code === currency_code);
              
              const projectValue = orderValue.length > 0 ? orderValue[0].value : 0;
              this.stepperFormGroup.patchValue({ 'project_value': projectValue });   
            }
          }
        }
      })
    }
    
    await this.PmBillingService.getGroupedMilestone(this.project_id).then((res)=>{
      if(res['messType']=='S'){
       this.milestone_grouped_data=res['data']
      }
    })
   
    if(this.withOpportunity==1){
      this.PmBillingService.getQuoteIDList(this.portfolio_id,this.project_id).then((res)=>{
        if(res['messType']=='S' && res['data'].length>0)
        {
          this.quote_id_list=res['data']
        }
        else{
          this.quote_id_list=[]
        }
      })
    //  this.PmBillingService.getPoNumberList(this.portfolio_id,this.project_id).then((res)=>{
    //   if(res['messType']=='S' && res['data'].length>0)
    //   {
    //       this.po_number_dropdown_list=res['data']
    //       this.stepperFormGroup.patchValue({['quote_id']:this.po_number_dropdown_list[0].quote_id})
    //       this.first_po_number=this.po_number_dropdown_list[0].id
    //   }
    //   else{
    //     this.po_number_dropdown_list=[]
    //   }
    // })
    }
    await this.PmBillingService.getMilestoneMatrix().then((res) => {
      if (res['messType'] == "S") {
        let matrixConfigData = res['data']
        let matrixCheckData = {service_type_id:this.service_type_id, is_active : 1, milestone_type_creation_available: 1}
        console.log('matrixCheckData',matrixCheckData)
        this.matrixConfig = _.where(matrixConfigData, matrixCheckData)
      }
      else{
        this.matrixConfig = [];
      }
    })

    await this.pmMasterService.getMilestoneTypeList().then((res: any) => {
      if (res['messType'] == 'S') {

        let milestone_type_list = _.uniq(_.pluck(this.matrixConfig,"milestone_type_id"))

        console.log("Milestone Type List", milestone_type_list)

      
          this.milestoneTypeList = _.filter(res['data'],(data)=>{
            if(_.contains(milestone_type_list, data['id']))
            {
                return true;
            }
          })
        
      }
    })


 if(this.monthConfig){
  this.stepperFormGroup.patchValue({repeat:2})
  await this.generateMonthlyDates()
  this.number_of_milestone=this.monthlyDates.length
  console.log(this.number_of_milestone)
  this.perMilestoneValue=this.number_of_milestone!=0?((this.stepperFormGroup.get('project_value').value-this.totalMilestoneValue)/this.number_of_milestone):0
  this.stepperFormGroup.patchValue({['milestone_value']:this.perMilestoneValue})
}
this.stepperFormGroup.patchValue({milestone_name:this.name_config})
    this.stepperFormGroup.get('milestone_value').valueChanges.subscribe(async(res)=>{
      console.log(res);

      if(this.stepperFormGroup.get('milestone_value').value!='' && this.stepperFormGroup.get('project_value').value>0){
      // if (res>this.stepperFormGroup.get('project_value').value){
      //   this.stepperFormGroup.patchValue({['milestone_value']:''})
      // }
      if(res == '' && res == null && res == undefined){
        this.stepperFormGroup.patchValue({['percentage']:'-'})
      }
      if(res != '' && res != null && res != undefined){
      //   if(((parseFloat(this.stepperFormGroup.get('milestone_value').value) * parseFloat(this.number_of_milestone))+parseFloat(this.totalMilestoneValue))<=parseFloat(this.quote)){
      //   this.percentage=((this.stepperFormGroup.get('milestone_value').value)/(this.stepperFormGroup.get('project_value').value))*100
      //   console.log(res, this.percentage)
      //   if(!isNaN(this.percentage)){
      //     this.stepperFormGroup.patchValue({['percentage']:this.percentage.toFixed(2)})
      //   }
      // }
      // else{
      //   this.stepperFormGroup.patchValue({['milestone_value']:''})
      //   //this.toastr.warning("Overall milestone value exceeding Order Value", 'Warning');
      // }
      this.percentage=((this.stepperFormGroup.get('milestone_value').value)/this.stepperFormGroup.get('project_value').value)*100
      //console.log(res, this.percentage)
      if(!isNaN(this.percentage)){
        this.stepperFormGroup.patchValue({['percentage']:this.percentage.toFixed(2)})
      }
      }
      else if(res<0){
                  this.stepperFormGroup.patchValue({['milestone_value']:''})
      }
    }
    else{
      this.stepperFormGroup.patchValue({['percentage']:0})
    }

    })
if(this.monthConfig){
  this.stepperFormGroup.patchValue({repeat:2})
  await this.generateMonthlyDates()
  this.number_of_milestone=this.monthlyDates.length
  console.log(this.number_of_milestone)
  this.perMilestoneValue=this.number_of_milestone!=0?((this.stepperFormGroup.get('project_value').value-this.totalMilestoneValue)/this.number_of_milestone):0
  this.stepperFormGroup.patchValue({['milestone_value']:this.perMilestoneValue})
}
this.stepperFormGroup.patchValue({milestone_name:this.name_config})
  this.stepperFormGroup.get('repeat').valueChanges.subscribe(async(res)=>{
      console.log(res);
      if(this.stepperFormGroup.get('start_date').value === null || this.stepperFormGroup.get('start_date').value === undefined || this.stepperFormGroup.get('start_date').value === ''){
        const startDate_empty = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.startDate_empty ? this.retrieveMessages[0].errors.startDate_empty : 'Kindly enter Start Date' : 'Kindly enter Start Date';
        this.toasterService.showWarning(startDate_empty, 10000);
      }
      else{
      if(this.stepperFormGroup.get('repeat').value!='' && this.stepperFormGroup.get('project_value').value>0){
       if(this.stepperFormGroup.get('repeat').value==2){
        await this.generateMonthlyDates()
        this.number_of_milestone=this.monthlyDates.length
        console.log(this.number_of_milestone)
        this.perMilestoneValue=this.number_of_milestone!=0?((this.quote-this.totalMilestoneValue)/this.number_of_milestone):0
        this.stepperFormGroup.patchValue({['milestone_value']:this.perMilestoneValue})
       }
       if(this.stepperFormGroup.get('repeat').value==1){
        await this.generateWeeklyDates()
        this.number_of_milestone=this.weeklyDates.length
        this.perMilestoneValue=this.number_of_milestone!=0?((this.quote-this.totalMilestoneValue)/this.number_of_milestone):0
        this.stepperFormGroup.patchValue({['milestone_value']:this.perMilestoneValue})
       }
    }
    else{
      this.stepperFormGroup.patchValue({['milestone_value']:0})
    }
  }
    })
    this.stepperFormGroup.get('quote_id').valueChanges.subscribe(async(res)=>{
      console.log(res);
      if(res != '' && res!= null && res!=undefined){
        if (this.withOpportunity == 1 && res) {
          this.PmBillingService.getProjectPoMaster(this.project_id, res).then((res) => {
            if (res['messType'] == 'S' && res['data'].length > 0) {
              this.po_number_dropdown_list = res['data'];
              if (res['data'].length == 1) {
                this.stepperFormGroup.patchValue({ ['po_number']: res['data'][0].id })
              }
            }
          })

          await this.PmBillingService.getTotalMilestoneQuoteId(this.project_id, res, this.code, this.stepperFormGroup.get('milestone_type').value).then(res=>{
            this.totalMilestoneValue = res
          })
        }
      }
    })

    this.stepperFormGroup.get('po_number').valueChanges.subscribe(async(res)=>{
      console.log(res);
      if(res != '' && res!= null && res!=undefined){
        const po_details = _.where(this.po_number_dropdown_list,{id: res})
        if(po_details.length>0)
        {
          this.first_po_number = po_details[0].id;
          const orderValueJson = po_details[0].po_value ? JSON.parse(po_details[0].po_value) : 0;
          const currency_code = po_details[0].currency ? po_details[0].currency : 'USD';
          const orderValue = _.filter(orderValueJson, item => item.currency_code === currency_code);
          const projectValue = orderValue.length > 0 ? orderValue[0].value : 0;
          if(this.withOpportunity==0)
            {
          this.quote=orderValue.length > 0 ? orderValue[0].value : 0;}
          this.stepperFormGroup.patchValue({ 'project_value': projectValue });    

          if(this.withOpportunity==0)
          {
            await this.PmBillingService.getTotalMilestonePONumber(this.project_id, res, this.code, this.stepperFormGroup.get('milestone_type').value).then(res=>{
              this.totalMilestoneValue = res
            })
          }
        }

        if(this.stepperFormGroup.get('start_date').value === null || this.stepperFormGroup.get('start_date').value === undefined || this.stepperFormGroup.get('start_date').value === ''){
          const startDate_empty = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.startDate_empty ? this.retrieveMessages[0].errors.startDate_empty : 'Kindly enter Start Date' : 'Kindly enter Start Date';
          this.toasterService.showWarning(startDate_empty, 10000);
        }
        else{
            if(this.stepperFormGroup.get('repeat').value!='' && this.stepperFormGroup.get('project_value').value>0){
            if(this.stepperFormGroup.get('repeat').value==2){
              await this.generateMonthlyDates()
              this.number_of_milestone=this.monthlyDates.length
              console.log(this.number_of_milestone)
              this.perMilestoneValue=this.number_of_milestone!=0?((this.quote-this.totalMilestoneValue)/this.number_of_milestone):0
              this.stepperFormGroup.patchValue({['milestone_value']:this.perMilestoneValue})
            }
            if(this.stepperFormGroup.get('repeat').value==1){
              await this.generateWeeklyDates()
              this.number_of_milestone=this.weeklyDates.length
              this.perMilestoneValue=this.number_of_milestone!=0?((this.quote-this.totalMilestoneValue)/this.number_of_milestone):0
              this.stepperFormGroup.patchValue({['milestone_value']:this.perMilestoneValue})
            }
          }
          else{
            this.stepperFormGroup.patchValue({['milestone_value']:0})
          }
        }
      }
    })

    this.stepperFormGroup.get('milestone_type').valueChanges.subscribe(async(res)=>{
      console.log(res);
      if(res != '' && res!= null && res!=undefined){
        if (this.withOpportunity == 1 && res) {
          await this.PmBillingService.getTotalMilestoneQuoteId(this.project_id, res, this.code, res).then(res=>{
            this.totalMilestoneValue = res
          })
        }
        else if(this.withOpportunity == 0 && res){
          await this.PmBillingService.getTotalMilestoneQuoteId(this.project_id, res, this.code, res).then(res=>{
            this.totalMilestoneValue = res
          })
        }
      }
    })

  //   this.stepperFormGroup.get('percentage').valueChanges.subscribe(async(res)=> {
  //     if(this.stepperFormGroup.get('percentage').value!=''){
  //     if(this.stepperFormGroup.get('percentage').value>100){
  //                     this.stepperFormGroup.patchValue({['percentage']:''})
  //   }
  //   else if(this.stepperFormGroup.get('percentage').value<0){
  //     this.stepperFormGroup.patchValue({['percentage']:''})
  //   }
  //   else{
  //       this.value=((this.stepperFormGroup.get('percentage').value)*(this.stepperFormGroup.get('project_value').value))/100
  //       console.log(this.value)
  //       this.stepperFormGroup.patchValue({['milestone_value']:this.value})
  //   }
  // }
  // else{
  //   this.stepperFormGroup.patchValue({['milestone_value']:''})
  // }
  //   })

    this.stepperFormGroup.get('start_date').valueChanges.subscribe(async(res)=>{
      if(res){
        if(this.stepperFormGroup.get('start_date').value != null && this.stepperFormGroup.get('start_date').value != undefined && this.stepperFormGroup.get('start_date').value != ''){
          if(this.monthConfig){
            this.stepperFormGroup.patchValue({['repeat']:2})
          }
          else{
         this.stepperFormGroup.patchValue({['milestone_value']:''})
         this.stepperFormGroup.patchValue({['repeat']:''})
          }
       
      }
      else{
        this.stepperFormGroup.patchValue({['milestone_value']:''})
         this.stepperFormGroup.patchValue({['repeat']:''})
      }
    }
      else{
        this.stepperFormGroup.patchValue({['milestone_value']:''})
         this.stepperFormGroup.patchValue({['repeat']:''})
      }
    
      })
      this.stepperFormGroup.get('end_date').valueChanges.subscribe(async(res)=>{
        if(res){
          if(this.stepperFormGroup.get('end_date').value != null && this.stepperFormGroup.get('end_date').value != undefined && this.stepperFormGroup.get('end_date').value != ''){
            if(this.monthConfig){
              this.stepperFormGroup.patchValue({['repeat']:2})
            }
            else{
           this.stepperFormGroup.patchValue({['milestone_value']:''})
           this.stepperFormGroup.patchValue({['repeat']:''})
            }
         
        }
        else{
          this.stepperFormGroup.patchValue({['milestone_value']:''})
           this.stepperFormGroup.patchValue({['repeat']:''})
        }
      }
        else{
          this.stepperFormGroup.patchValue({['milestone_value']:''})
           this.stepperFormGroup.patchValue({['repeat']:''})
        }
      
        })
  }
  onCloseClick(){
    //this.dialogRef.close({messType:"E"})
    if(this.stepperFormGroup.dirty){
      this.utilityService.openConfirmationSweetAlertWithCustom("Are you sure","You want to Close without saving").then((result) => {
        if (result) {
          this.dialogRef.close({messType:"E"})
        }
      });
    }
    else{ 
      this.dialogRef.close({messType:"E"})
    }
  }
  async generateMilestone(){
    this.saveDisabled=true
    this.save_name='Generating....'
    const mandate = await this.checkMandate()

    if(mandate){
    if(this.stepperFormGroup.get('repeat').value==2){
        await this.generateMonthlyDates()
        this.monthlyDates[0].start=moment(this.stepperFormGroup.get('start_date').value).utc().format('YYYY-MM-DD')
      this.monthlyDates[this.monthlyDates.length-1].end=moment(this.stepperFormGroup.get('end_date').value).utc().format('YYYY-MM-DD')
        console.log(this.monthlyDates)
        if(this.monthlyDates.length>0){
          await this.PmBillingService.saveGenerateMilestone(this.portfolio_id,this.project_id,this.monthlyDates).then((res)=>{
                    if(res['messType']=='S'){
                      const milestoneCreate_success = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].success.milestoneCreate_success ? this.retrieveMessages[0].success.milestoneCreate_success : 'Milestone Generated Successfully' : 'Milestone Generated Successfully';
                      this.toasterService.showSuccess(milestoneCreate_success, 10000);
                      this.dialogRef.close({messType:"S"})
                    }
                    else if(res['messType']=="W"){
                      this.toasterService.showWarning(res['message'], 10000);
                      this.dialogRef.close({ messType: "S" })
                    }
                    else{
                      const milestoneCreate_unsuccess = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.milestoneCreate_unsuccess ? this.retrieveMessages[0].errors.milestoneCreate_unsuccess : 'Milestone Generation Unsuccessful' : 'Milestone Generation Unsuccessful';
                      this.toasterService.showError(milestoneCreate_unsuccess);
                      this.saveDisabled=false
                      this.save_name='Generate'
                    }
          })
        }
    } 
    else if(this.stepperFormGroup.get('repeat').value==1){
      await this.generateWeeklyDates()
      this.weeklyDates[this.weeklyDates.length-1].end=moment(this.stepperFormGroup.get('end_date').value).format('YYYY-MM-DD')
      console.log(this.weeklyDates)
      if(this.weeklyDates.length>0){
        await this.PmBillingService.saveGenerateMilestone(this.portfolio_id,this.project_id,this.weeklyDates).then((res)=>{
                  if(res['messType']=='S'){
                    const milestoneCreate_success = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].success.milestoneCreate_success ? this.retrieveMessages[0].success.milestoneCreate_success : 'Milestone Generated Successfully' : 'Milestone Generated Successfully';
                    this.toasterService.showSuccess(milestoneCreate_success, 10000);
                    this.dialogRef.close({messType:"S"})
                  }
                  else if(res['messType']=="W"){
                    this.toasterService.showWarning(res['message'], 10000);
                    this.dialogRef.close({ messType: "S" })
                  }
                  else{
                    const milestoneCreate_unsuccess = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.milestoneCreate_unsuccess ? this.retrieveMessages[0].errors.milestoneCreate_unsuccess : 'Milestone Generation Unsuccessful' : 'Milestone Generation Unsuccessful';
                    this.toasterService.showError(milestoneCreate_unsuccess);
                    this.saveDisabled=false
                    this.save_name='Generate'
                  }
        })
      }
    }
    }
    else{
      this.saveDisabled=false
      this.save_name='Generate'
    }
  }
  
  generateMonthlyDates() {
    this.monthlyDates=[]
    let currentDate = new Date(moment(this.stepperFormGroup.get('start_date').value).utc().format('YYYY-MM-DD'));
    let endDate = new Date(moment(this.stepperFormGroup.get('end_date').value).utc().format('YYYY-MM-DD'));
    while (currentDate <= endDate) {
      const startOfMonth = new Date(currentDate);
      startOfMonth.setDate(1);
      console.log(startOfMonth)
      const endOfMonth = new Date(startOfMonth);
      endOfMonth.setMonth(endOfMonth.getMonth() + 1);
      endOfMonth.setDate(0); // Last day of the previous month
      console.log(endOfMonth)
       let m_name =this.stepperFormGroup.get('milestone_name').value+' '+moment(startOfMonth).utc().format('MMMM YYYY')
      this.monthlyDates.push({ start: moment(startOfMonth).format('YYYY-MM-DD'), end: moment(endOfMonth).format('YYYY-MM-DD'),name:m_name,value:this.stepperFormGroup.get('milestone_value').value,percentage:this.stepperFormGroup.get('percentage').value,code:this.code,gantt_id:uuidv4(),quote_id:this.stepperFormGroup.get('quote_id').value,po_number:this.first_po_number, milestone_type:this.stepperFormGroup.get('milestone_type').value})
     console.log('gghh')
     console.log(this.monthlyDates)
      currentDate = new Date(endOfMonth);
      currentDate.setDate(currentDate.getDate() + 1);
      console.log(currentDate) // Start of the next month
    }
  
  }
  generateWeeklyDates() {
    this.weeklyDates=[]
    let currentDate = new Date(moment(this.stepperFormGroup.get('start_date').value).utc().format('YYYY-MM-DD'));
    let endDate = new Date(moment(this.stepperFormGroup.get('end_date').value).utc().format('YYYY-MM-DD'));
    let i=1
    while (currentDate <= endDate) {
      const startOfWeek = new Date(currentDate);
      const endOfWeek = new Date(currentDate);

      endOfWeek.setDate(endOfWeek.getDate() + 6); // Calculate end of the week (6 days later)
      let m_name=this.stepperFormGroup.get('milestone_name').value+' '+'#Week '+i
      this.weeklyDates.push({ start: moment(startOfWeek).format('YYYY-MM-DD'), end: moment(endOfWeek).format('YYYY-MM-DD') ,name:m_name,value:this.stepperFormGroup.get('milestone_value').value,percentage:this.stepperFormGroup.get('percentage').value,code:this.code,gantt_id:uuidv4(),quote_id:this.first_quote_id,po_number:this.first_po_number, milestone_type: this.stepperFormGroup.get("milestone_type").value});

      currentDate = new Date(endOfWeek);
      currentDate.setDate(currentDate.getDate() + 1); // Start of the next week
      i=i+1
    }
  }
  async checkMandate(){
   this.data=this.stepperFormGroup.value
    let errorOccurred=false
    if (this.withOpportunity) {
      const res = await this.PmBillingService.checkOpportunityBlanketPO(this.portfolio_id, this.project_id, this.data.quote_id);
      if (res['messType'] == 'S') {
          this.blanketPo = res['data'];
      }
    }

    if ((!this.data.milestone_name || this.data.milestone_name.trim() === '') && this.isMandate('milestone_name')) {
      // const customerEmptymsg = 'Responsible Is Mandatory';
      // this.toastr.error(customerEmptymsg, 'Error');
      const name_empty = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.name_empty ? this.retrieveMessages[0].errors.name_empty : 'Milestone name is Mandatory' : 'Milestone name is mandatory';
      this.toasterService.showWarning(name_empty, 10000);
      errorOccurred = true;
    }  
    else if ((this.data.milestone_value=== null || this.data.milestone_value === undefined || this.data.milestone_value === '') && this.isMandate('value')) {
      // const customerEmptymsg = 'Responsible Is Mandatory';
      // this.toastr.error(customerEmptymsg, 'Error');
      const value_empty = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.value_empty ? this.retrieveMessages[0].errors.value_empty : 'Milestone value is Mandatory' : 'Milestone value is mandatory';
      this.toasterService.showWarning(value_empty, 19000);
      errorOccurred = true;
    }
    else if (this.data.milestone_value === 0) {
      const value_zero = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.value_empty ? this.retrieveMessages[0].errors.value_empty : 'Milestone value Cannot be 0' : 'Milestone value Cannot be 0';
      this.toasterService.showWarning(value_zero, 10000);

      errorOccurred = true;
    }
    else if ((!this.data.percentage=== null || this.data.percentage === undefined || this.data.percentage === '') && this.isMandate('percentage')) {
      // const customerEmptymsg = 'Responsible Is Mandatory';
      // this.toastr.error(customerEmptymsg, 'Error');
      const percentage_empty = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.percentage_empty ? this.retrieveMessages[0].errors.percentage_empty : 'Milestone Percentage is Mandatory' : 'Milestone Percentage is mandatory';
      this.toasterService.showWarning(percentage_empty, 10000);

      errorOccurred = true;
    }
    else if ((this.data.start_date === null || this.data.start_date === undefined || this.data.start_date === '') && this.isMandate('start_date')) {
      // const customerEmptymsg = 'Responsible Is Mandatory';
      // this.toastr.error(customerEmptymsg, 'Error');
      const startDate_empty = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.startDate_empty ? this.retrieveMessages[0].errors.startDate_empty : 'Milestone Start date is Mandatory' : 'Milestone Start Date is mandatory';
      this.toasterService.showWarning(startDate_empty, 10000);
      errorOccurred = true;
    }
    else if ((this.data.end_date === null || this.data.end_date === undefined || this.data.end_date === '') && this.isMandate('end_date')) {
      // const customerEmptymsg = 'Responsible Is Mandatory';
      // this.toastr.error(customerEmptymsg, 'Error');
      const startDate_empty = 'Milestone end_date is Mandatory' ;
      this.toasterService.showWarning(startDate_empty, 10000);
      errorOccurred = true;
    }
    else if ((this.data.repeat === null || this.data.repeat === undefined || this.data.repeat === '') && this.isMandate('repeat')) {
      // const customerEmptymsg = 'Responsible Is Mandatory';
      // this.toastr.error(customerEmptymsg, 'Error');
      const repeat_empty = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.repeat_empty  ? this.retrieveMessages[0].errors.repeat_empty  : 'Repeat field is Mandatory' : 'Repeat field is mandatory';
      this.toasterService.showWarning(repeat_empty, 10000);

      errorOccurred = true;
    }
    else if ((this.data.project_value === null || this.data.project_value === undefined || this.data.project_value === '') && this.isMandate('quote')) {
      // const customerEmptymsg = 'Responsible Is Mandatory';
      // this.toastr.error(customerEmptymsg, 'Error');
      const projectValue_empty = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.projectValue_empty ? this.retrieveMessages[0].errors.projectValue_empty : 'Milestone Project value is Mandatory' : 'Milestone Project Value is mandatory';
      this.toasterService.showWarning(projectValue_empty, 10000);

      errorOccurred = true;
    }
    else if(!this.blanketPo && ((parseFloat(this.stepperFormGroup.get('milestone_value').value) * (parseFloat(this.number_of_milestone)))+parseFloat(this.totalMilestoneValue))>parseFloat(this.quote)){
      const milestoneValue_exceed_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.milestoneValue_exceed_msg ? this.retrieveMessages[0].errors.milestoneValue_exceed_msg : 'Overall milestone value exceeding Order Value' : 'Overall milestone value exceeding Order Value';
        this.toasterService.showWarning(milestoneValue_exceed_msg, 10000);
        errorOccurred = true;
    }
   
    
  if(errorOccurred){
    //this.toastr.warning("Kindly enter mandatory fields", 'Warning');
        return false
  }
  else{

    if((this.data.milestone_value<=0) && !errorOccurred){

      const milestoneValue_negative = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.milestoneValue_negative ? this.retrieveMessages[0].errors.milestoneValue_negative : 'Milestone Value should not be Negative or Zero' : 'Milestone Value should not be Negative or Zero';
      this.toasterService.showWarning(milestoneValue_negative, 10000);
      return false
    }
    else if((this.data.percentage>100 || this.data.percentage<0) && !errorOccurred){
      const Percent_exceed_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.Percent_exceed_msg ? this.retrieveMessages[0].errors.Percent_exceed_msg : 'Percentage should be between 0-100' : 'Percentage should be between 0-100';
      this.toasterService.showWarning(Percent_exceed_msg, 10000);
      return false
    }
    else{
          this.project_end_date=this.stepperFormGroup.get('end_date').value
          return true
    }
  }
  }

  isMandate(field: any){
    const mandate = _.where(this.formConfig, { type: "generate-milestone-creation", field_name: field, is_active: true });
    if(mandate.length > 0){
      const isMandate = mandate[0].is_mandant;
      return isMandate;
    }
  }
  valueKeyUp(event){
    console.log(event.target.value.toString().length)
    console.log(event.target.value.toString())
      if(this.stepperFormGroup.get('milestone_value').value>1000000000000000000000000000000){
        this.stepperFormGroup.patchValue({['milestone_value']:1000000000000000000000000000000})
      }
      if(this.stepperFormGroup.get('milestone_value').value<1){
        const milestoneValue_number = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.milestoneValue_number ? this.retrieveMessages[0].errors.milestoneValue_number : 'Milestone value should be only numbers' : 'Milestone value should be only numbers';
        this.toasterService.showWarning(milestoneValue_number, 10000);
        this.stepperFormGroup.patchValue({['milestone_value']:''})   
      }
  }
  percentKeyUp(event){
    console.log(event.target.value.toString().length)
    console.log(event.target.value.toString())
      if(this.stepperFormGroup.get('percentage').value>100){
        const Percent_exceed_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.Percent_exceed_msg ? this.retrieveMessages[0].errors.Percent_exceed_msg : 'Percentage should be between 0-100' : 'Percentage should be between 0-100';
        this.toasterService.showWarning(Percent_exceed_msg, 10000);
        this.stepperFormGroup.patchValue({['percentage']:100})
      }
      if(this.stepperFormGroup.get('percentage').value<1){
        const Percent_exceed_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.Percent_exceed_msg ? this.retrieveMessages[0].errors.Percent_exceed_msg : 'Percentage should be between 0-100' : 'Percentage should be between 0-100';
        this.toasterService.showWarning(Percent_exceed_msg, 10000);
        this.stepperFormGroup.patchValue({['percentage']:''})   
      }
  }


  compareDate(date1:any,date2:any){
  
    if((date1=="" || date1==null || date1==undefined) && (date2=="" || date2==null || date2==undefined))
    {
        return ''
    }
    else if(date1=="" || date1==null || date1==undefined)
    {
        return date2
    }
    else if(date2=="" || date2==null || date2==undefined)
    {
        return date1
    }
    else
    {
      date1=moment(date1).format('YYYY-MM-DD')
    date2=moment(date2).format('YYYY-MM-DD')
        if(date1<date2)
            return date1
        else
            return date2
    }
  }
  compareDateMinimum(date1:any,date2:any){
  
    if((date1=="" || date1==null || date1==undefined) && (date2=="" || date2==null || date2==undefined))
    {
        return ''
    }
    else if(date1=="" || date1==null || date1==undefined)
    {
        return date2
    }
    else if(date2=="" || date2==null || date2==undefined)
    {   
        return date1
    }
    else
    {
      date1=moment(date1).format('YYYY-MM-DD')
      date2=moment(date2).format('YYYY-MM-DD')
        if(date1>date2)
            return date1
        else
            return date2
    }
  }
}
export interface DialogData {
  button: any;
}
