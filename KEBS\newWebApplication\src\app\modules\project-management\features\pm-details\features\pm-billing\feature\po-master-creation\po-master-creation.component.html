<div class="flag-change-style"> 
  <span>
    <div class="row header">
      <div class="col-4"></div>
      <div class="header-title col-4">
        Enter Details
      </div>
      <div class="col-4">
        <!-- <mat-icon class="minimize-button" (click)="onMinimizeClick()">minimize</mat-icon> -->
        <mat-icon class="close-button" (click)="onCloseClick()">clear</mat-icon>
      </div>
    </div>
    <div class="loader-container" *ngIf="loading">
      <mat-spinner class="green-spinner1" diameter="30"></mat-spinner>
  </div>
    <div *ngIf="!loading"  class="col-12 content" style="overflow:hidden;height: 500px;padding-bottom: 30px;">
          <form  *ngIf="q2c_check == 1" [formGroup]="poForm">
        <div class="row"  style="display: flex; white-space: nowrap;">
          <div class="col-3"  *ngIf="('start_date' | checkActive : this.formConfig: 'po-creation')">
            <div class="content-title">
              {{ ('start_date' | checkLabel : formConfig: 'po-creation': 'Start Date') }}
              <span class="required-star" *ngIf="('start_date' | checkMandatedField : formConfig: 'po-creation')">*</span>
              <span *ngIf="('start_date' | checkInfoIcon : formConfig: 'po-creation')">
                <mat-icon class="info-icon" matTooltip="{{ ('start_date' | checkTooltip : formConfig: 'po-creation': 'Project Date') }}">
                  {{ ('info_icon' | checkLabel : formConfig: 'po-creation': 'info_outline') }}
                </mat-icon>
              </span>
            </div>
            <mat-form-field class="input-field po_date" appearance="outline">
              <input matInput formControlName="start_date" [matDatepicker]="startDatePicker1"  [min]="min_start_date" 
              [max]="max_end_date" placeholder="DD-MMM-YYYY">
              <mat-datepicker-toggle matSuffix [for]="startDatePicker1"></mat-datepicker-toggle>
              <mat-datepicker #startDatePicker1></mat-datepicker>
            </mat-form-field>
          </div>
          <div class="col-3"  *ngIf="('end_date' | checkActive : this.formConfig: 'po-creation')">
            <div class="content-title">
              {{ ('end_date' | checkLabel : formConfig: 'po-creation': 'End Date') }}
              <span class="required-star" *ngIf="('end_date' | checkMandatedField : formConfig: 'po-creation')">*</span>
              <span *ngIf="('end_date' | checkInfoIcon : formConfig: 'po-creation')">
                <mat-icon class="info-icon" matTooltip="{{ ('end_date' | checkTooltip : formConfig: 'po-creation': 'Project Date') }}">
                  {{ ('info_icon' | checkLabel : formConfig: 'po-creation': 'info_outline') }}
                </mat-icon>
              </span>
            </div>
            <mat-form-field class="input-field po_date" appearance="outline">
              <input matInput formControlName="end_date" [matDatepicker]="endDatePicker1"   [min]="min_start_date" 
              [max]="max_end_date" placeholder="DD-MMM-YYYY">
              <mat-datepicker-toggle matSuffix [for]="endDatePicker1"></mat-datepicker-toggle>
              <mat-datepicker #endDatePicker1></mat-datepicker>
            </mat-form-field>
          </div>
          <div class="col-3"  *ngIf="('po_date' | checkActive : this.formConfig: 'po-creation') && restriction">
            <div class="content-title">
              {{ ('po_date' | checkLabel : formConfig: 'po-creation': 'PO Date') }}
              <span class="required-star" *ngIf="('po_date' | checkMandatedField : formConfig: 'po-creation')">*</span>
              <span *ngIf="('po_date' | checkInfoIcon : formConfig: 'po-creation')">
                <mat-icon class="info-icon" matTooltip="{{ ('po_date' | checkTooltip : formConfig: 'po-creation': 'PO Date') }}">
                  {{ ('info_icon' | checkLabel : formConfig: 'po-creation': 'info_outline') }}
                </mat-icon>
              </span>
            </div>
            <mat-form-field class="input-field po_date" appearance="outline">
              <input matInput formControlName="po_date" [matDatepicker]="poDatePicker1"  [min]="min_start_date" 
              [max]="max_end_date" placeholder="DD-MMM-YYYY">
              <mat-datepicker-toggle matSuffix [for]="poDatePicker1"></mat-datepicker-toggle>
              <mat-datepicker #poDatePicker1></mat-datepicker>
            </mat-form-field>
          </div>
          <div class="col-3"  *ngIf="('po_date' | checkActive : this.formConfig: 'po-creation') && !restriction">
            <div class="content-title">
              {{ ('po_date' | checkLabel : formConfig: 'po-creation': 'PO Date') }}
              <span class="required-star" *ngIf="('po_date' | checkMandatedField : formConfig: 'po-creation')">*</span>
              <span *ngIf="('po_date' | checkInfoIcon : formConfig: 'po-creation')">
                <mat-icon class="info-icon" matTooltip="{{ ('po_date' | checkTooltip : formConfig: 'po-creation': 'PO Date') }}">
                  {{ ('info_icon' | checkLabel : formConfig: 'po-creation': 'info_outline') }}
                </mat-icon>
              </span>
            </div>
            <mat-form-field class="input-field po_date" appearance="outline">
              <input matInput formControlName="po_date" [matDatepicker]="poDatePicker1"  placeholder="DD-MMM-YYYY">
              <mat-datepicker-toggle matSuffix [for]="poDatePicker1"></mat-datepicker-toggle>
              <mat-datepicker #poDatePicker1></mat-datepicker>
            </mat-form-field>
          </div>
          <div class="col-3" *ngIf="('quote_id' | checkActive : this.formConfig: 'po-creation') && ('payment_terms' | checkActive : this.formConfig: 'po-creation')">
            <div class="content-title">
              {{ ('quote_id' | checkLabel : formConfig: 'po-creation': 'Quote ID') }}
              <span class="required-star" *ngIf="('quote_id' | checkMandatedField : this.formConfig: 'po-creation')"> *</span>
               <span *ngIf="('quote_id' | checkInfoIcon : this.formConfig: 'po-creation')">
                                      <mat-icon class="info-icon"
                                          tooltip="{{('quote_id' | checkTooltip : this.formConfig: 'po-creation': 'Quote ID')}}">
                                          info_outline
                                      </mat-icon>
               </span>
            </div>
            <div class="row">
              <app-input-search-name [showSelect]="false" class="input-field poNumber" [list]="quote_id_list" placeholder="Select"
                                                [disabled]="quote_disable"
                                                [required]="('quote_id' | checkMandatedField : this.formConfig: 'po-creation')"
                                                formControlName="quote_id">
                                            </app-input-search-name>
            </div>
          </div>
        </div>
        <div class="row"  style="display: flex; white-space: nowrap;">
          <div class="col-3" *ngIf="('quote_id' | checkActive : this.formConfig: 'po-creation') && !('payment_terms' | checkActive : this.formConfig: 'po-creation')">
            <div class="content-title">
              {{ ('quote_id' | checkLabel : formConfig: 'po-creation': 'Quote ID') }}
              <span class="required-star" *ngIf="('quote_id' | checkMandatedField : this.formConfig: 'po-creation')"> *</span>
               <span *ngIf="('quote_id' | checkInfoIcon : this.formConfig: 'po-creation')">
                                      <mat-icon class="info-icon"
                                          tooltip="{{('quote_id' | checkTooltip : this.formConfig: 'po-creation': 'Quote ID')}}">
                                          info_outline
                                      </mat-icon>
               </span>
            </div>
            <div class="row">
              <app-input-search-name [showSelect]="false" class="input-field poNumber" [list]="quote_id_list" placeholder="Select"
                                                [disabled]="quote_disable"
                                                [required]="('quote_id' | checkMandatedField : this.formConfig: 'po-creation')"
                                                formControlName="quote_id">
                                            </app-input-search-name>
            </div>
          </div>
          <div class="col-3" *ngIf="('po_number' | checkActive : this.formConfig: 'po-creation')  && !ponumdisable">
            <div class="content-title">
              {{ ('po_number' | checkLabel : formConfig: 'po-creation': 'PO Number') }}
              <span class="required-star" *ngIf="('po_number' | checkMandatedField : this.formConfig: 'po-creation')"> *</span>
               <span *ngIf="('po_number' | checkInfoIcon : this.formConfig: 'po-creation')">
                                      <mat-icon class="info-icon"
                                          tooltip="{{('po_number' | checkTooltip : this.formConfig: 'po-creation': 'PO Number')}}">
                                          info_outline
                                      </mat-icon>
               </span>
            </div>
            <div class="row">
              <mat-form-field class="poValue2" appearance="outline">
                <input style="color: #45546E; font-family:Plus Jakarta Sans;" 
                [required]="('po_number' | checkMandatedField : this.formConfig: 'po-creation')"
                [maxlength]="(name_length ? name_length : 300)" matInput
                placeholder="Enter here" formControlName="po_number" [readonly]="ponumdisable"/>
              </mat-form-field>
            </div>
          </div>
          <div class="col-3" *ngIf="('po_number' | checkActive : this.formConfig: 'po-creation') && ponumdisable">
            <div class="content-title">
              {{ ('po_number' | checkLabel : formConfig: 'po-creation': 'PO Number') }}
              <span class="required-star" *ngIf="('po_number' | checkMandatedField : this.formConfig: 'po-creation')"> *</span>
               <span *ngIf="('po_number' | checkInfoIcon : this.formConfig: 'po-creation')">
                                      <mat-icon class="info-icon"
                                          tooltip="{{('po_number' | checkTooltip : this.formConfig: 'po-creation': 'PO Number')}}">
                                          info_outline
                                      </mat-icon>
               </span>
            </div>
            <div class="row">
              <mat-form-field class="poValue3" appearance="outline">
                <input style="color: #45546E; font-family:Plus Jakarta Sans;" 
                [required]="('po_number' | checkMandatedField : this.formConfig: 'po-creation')"
                [maxlength]="(name_length ? name_length : 300)" matInput
                placeholder="Enter here" formControlName="po_number" [readonly]="ponumdisable"/>
              </mat-form-field>
            </div>
          </div>
          <div class="col-3"  *ngIf="('po_reference' | checkActive : this.formConfig: 'po-creation')">
            <div class="content-title">
              {{ ('po_reference' | checkLabel : formConfig: 'po-creation': 'PO Reference') }}
              <span class="required-star" *ngIf="('po_reference' | checkMandatedField : formConfig: 'po-creation')">*</span>
              <span *ngIf="('po_reference' | checkInfoIcon : formConfig: 'po-creation')">
                <mat-icon class="info-icon"
                                          tooltip="{{('po_refrenece' | checkTooltip : this.formConfig: 'po-creation': 'PO Reference')}}">
                                          info_outline
                                      </mat-icon>
              </span>
            </div>
            <div class="row">
              <mat-form-field class="poNumber" appearance="outline">
                <input style="color: #45546E; font-family:Plus Jakarta Sans;" 
                [required]="('po_reference' | checkMandatedField : this.formConfig: 'po-creation')"
                     [maxlength]="(name_length ? name_length : 300)" matInput 
                     placeholder="PO Reference"
                  formControlName="po_reference" />
              </mat-form-field>
            </div>
          </div>
          <div class="col-3"  *ngIf="('po_value' | checkActive : this.formConfig: 'po-creation')">
            <div class="content-title">
              {{ ('po_value' | checkLabel : formConfig: 'po-creation': 'PO Value') }} ({{this.code}})
              <span class="required-star" *ngIf="('po_value' | checkMandatedField : formConfig: 'po-creation')">*</span>
              <span *ngIf="('po_value' | checkInfoIcon : formConfig: 'po-creation')">
                <mat-icon class="info-icon"
                tooltip="{{('po_value' | checkTooltip : this.formConfig: 'po-creation': 'PO Value')}}">
                info_outline
            </mat-icon>
              </span>
            </div>
            <div class="row">
              <mat-form-field class="poValue" appearance="outline">
                <input style="color: #45546E; font-family:Plus Jakarta Sans;" type="text" digitOnly [isPercentage]="false"
                  [digitsAllowed]="60" [required]="true" matInput placeholder="PO Value"
                  formControlName="po_value" />
              </mat-form-field>
            </div>
          </div> 
          <div class="col-3" *ngIf="('payment_terms' | checkActive : this.formConfig: 'po-creation')">
              <div class="content-title">{{('payment_terms' | checkLabel : this.formConfig: 'po-creation':'Payment Terms')}}
                  <span class="required-star" *ngIf="('payment_terms' | checkMandatedField : this.formConfig: 'po-creation')"> *</span>
                       <span *ngIf="('payment_terms' | checkInfoIcon : this.formConfig: 'po-creation')">
                              <mat-icon class="info-icon" tooltip="{{('payment_terms' | checkTooltip : this.formConfig: 'po-creation': 'Payment Terms')}}">
                                                        info_outline
                              </mat-icon>
                        </span>
                </div>
                 <app-input-search-name [showSelect]="false" class="paymentTerms" [list]="payment_list"
                                                [required]="('payment_terms' | checkMandatedField : this.formConfig: 'po-creation')"
                                                placeholder="Select One" formControlName="payment_terms" [disabled]="false">
                  </app-input-search-name>
          </div>  
        </div> 
      </form>
      <form  *ngIf="q2c_check != 1" [formGroup]="poForm">
        <div class="row"  style="display: flex; white-space: nowrap;">
          <div class="col-3"  *ngIf="('start_date' | checkActive : this.formConfig: 'po-creation')">
            <div class="content-title">
              {{ ('start_date' | checkLabel : formConfig: 'po-creation': 'Start Date') }}
              <span class="required-star" *ngIf="('start_date' | checkMandatedField : formConfig: 'po-creation')">*</span>
              <span *ngIf="('start_date' | checkInfoIcon : formConfig: 'po-creation')">
                <mat-icon class="info-icon" matTooltip="{{ ('start_date' | checkTooltip : formConfig: 'po-creation': 'Project Date') }}">
                  {{ ('info_icon' | checkLabel : formConfig: 'po-creation': 'info_outline') }}
                </mat-icon>
              </span>
            </div>
            <mat-form-field class="input-field po_date" appearance="outline">
              <input matInput formControlName="start_date" [matDatepicker]="startDatePicker" placeholder="DD-MMM-YYYY" [min]="min_start_date" 
              [max]="max_end_date"  [required]="('start_date' | checkMandatedField : formConfig : 'po-creation')">
              <mat-datepicker-toggle matSuffix [for]="startDatePicker"></mat-datepicker-toggle>
              <mat-datepicker #startDatePicker></mat-datepicker>
            </mat-form-field>
          </div>
          <div class="col-3"  *ngIf="('end_date' | checkActive : this.formConfig: 'po-creation')">
            <div class="content-title">
              {{ ('end_date' | checkLabel : formConfig: 'po-creation': 'End Date') }}
              <span class="required-star" *ngIf="('end_date' | checkMandatedField : formConfig: 'po-creation')">*</span>
              <span *ngIf="('end_date' | checkInfoIcon : formConfig: 'po-creation')">
                <mat-icon class="info-icon" matTooltip="{{ ('end_date' | checkTooltip : formConfig: 'po-creation': 'Project Date') }}">
                  {{ ('info_icon' | checkLabel : formConfig: 'po-creation': 'info_outline') }}
                </mat-icon>
              </span>
            </div>
            <mat-form-field class="input-field po_date" appearance="outline">
              <input matInput formControlName="end_date" [matDatepicker]="endDatePicker" placeholder="DD-MMM-YYYY" [min]="min_start_date" 
              [max]="max_end_date"  [required]="('end_date' | checkMandatedField : formConfig : 'po-creation')">
              <mat-datepicker-toggle matSuffix [for]="endDatePicker"></mat-datepicker-toggle>
              <mat-datepicker #endDatePicker></mat-datepicker>
            </mat-form-field>
          </div>
          <div class="col-3"  *ngIf="('po_date' | checkActive : this.formConfig: 'po-creation') && restriction">
            <div class="content-title">
              {{ ('po_date' | checkLabel : formConfig: 'po-creation': 'PO Date') }}
              <span class="required-star" *ngIf="('po_date' | checkMandatedField : formConfig: 'po-creation')">*</span>
              <span *ngIf="('po_date' | checkInfoIcon : formConfig: 'po-creation')">
                <mat-icon class="info-icon" matTooltip="{{ ('po_date' | checkTooltip : formConfig: 'po-creation': 'PO Date') }}">
                  {{ ('info_icon' | checkLabel : formConfig: 'po-creation': 'info_outline') }}
                </mat-icon>
              </span>
            </div>
            <mat-form-field class="input-field po_date" appearance="outline">
              <input matInput formControlName="po_date" [matDatepicker]="poDatePicker" placeholder="DD-MMM-YYYY"  [min]="min_start_date" 
              [max]="max_end_date" [required]="('po_date' | checkMandatedField : formConfig : 'po-creation')">
              <mat-datepicker-toggle matSuffix [for]="poDatePicker"></mat-datepicker-toggle>
              <mat-datepicker #poDatePicker></mat-datepicker>
            </mat-form-field>
          </div>
          <div class="col-3"  *ngIf="('po_date' | checkActive : this.formConfig: 'po-creation') && !restriction">
            <div class="content-title">
              {{ ('po_date' | checkLabel : formConfig: 'po-creation': 'PO Date') }}
              <span class="required-star" *ngIf="('po_date' | checkMandatedField : formConfig: 'po-creation')">*</span>
              <span *ngIf="('po_date' | checkInfoIcon : formConfig: 'po-creation')">
                <mat-icon class="info-icon" matTooltip="{{ ('po_date' | checkTooltip : formConfig: 'po-creation': 'PO Date') }}">
                  {{ ('info_icon' | checkLabel : formConfig: 'po-creation': 'info_outline') }}
                </mat-icon>
              </span>
            </div>
            <mat-form-field class="input-field po_date" appearance="outline">
              <input matInput formControlName="po_date" [matDatepicker]="poDatePicker" placeholder="DD-MMM-YYYY"  [required]="('po_date' | checkMandatedField : formConfig : 'po-creation')">
              <mat-datepicker-toggle matSuffix [for]="poDatePicker"></mat-datepicker-toggle>
              <mat-datepicker #poDatePicker></mat-datepicker>
            </mat-form-field>
          </div>
          <div class="col-3" *ngIf="('po_number' | checkActive : this.formConfig: 'po-creation')">
            <div class="content-title">
              {{ ('po_number' | checkLabel : formConfig: 'po-creation': 'PO Number') }}
              <span class="required-star" *ngIf="('po_number' | checkMandatedField : this.formConfig: 'po-creation')"> *</span>
               <span *ngIf="('po_number' | checkInfoIcon : this.formConfig: 'po-creation')">
                                      <mat-icon class="info-icon"
                                          tooltip="{{('po_number' | checkTooltip : this.formConfig: 'po-creation': 'PO Number')}}">
                                          info_outline
                                      </mat-icon>
               </span>
            </div>
            <div class="row">
              <mat-form-field class="poValue2" appearance="outline">
                <input style="color: #45546E; font-family:Plus Jakarta Sans;" 
                [required]="('po_number' | checkMandatedField : this.formConfig: 'po-creation')"
                [maxlength]="(name_length ? name_length : 300)" matInput
                placeholder="Enter here" formControlName="po_number" [readonly]="ponumdisable"/>
              </mat-form-field>
            </div>
          </div>
        </div>
        <div class="row"  style="display: flex; white-space: nowrap;">
          <div class="col-3"  *ngIf="('po_reference' | checkActive : this.formConfig: 'po-creation')">
            <div class="content-title">
              {{ ('po_reference' | checkLabel : formConfig: 'po-creation': 'PO Reference') }}
              <span class="required-star" *ngIf="('po_reference' | checkMandatedField : formConfig: 'po-creation')">*</span>
              <span *ngIf="('po_reference' | checkInfoIcon : formConfig: 'po-creation')">
                <mat-icon class="info-icon"
                                          tooltip="{{('po_refrenece' | checkTooltip : this.formConfig: 'po-creation': 'PO Reference')}}">
                                          info_outline
                                      </mat-icon>
              </span>
            </div>
            <div class="row">
              <mat-form-field class="poNumber" appearance="outline">
                <input style="color: #45546E; font-family:Plus Jakarta Sans;" 
                [required]="('po_reference' | checkMandatedField : this.formConfig: 'po-creation')"
                     [maxlength]="(name_length ? name_length : 300)" matInput 
                     placeholder="PO Reference"
                  formControlName="po_reference" />
              </mat-form-field>
            </div>
          </div>
          <div class="col-3"  *ngIf="('po_value' | checkActive : this.formConfig: 'po-creation')">
            <div class="content-title">
              {{ ('po_value' | checkLabel : formConfig: 'po-creation': 'PO Value') }} ({{this.code}})
              <span class="required-star" *ngIf="('po_value' | checkMandatedField : formConfig: 'po-creation')">*</span>
              <span *ngIf="('po_value' | checkInfoIcon : formConfig: 'po-creation')">
                <mat-icon class="info-icon"
                tooltip="{{('po_value' | checkTooltip : this.formConfig: 'po-creation': 'PO Value')}}">
                info_outline
            </mat-icon>
              </span>
            </div>
            <div class="row">
              <mat-form-field class="poValue" appearance="outline">
                <input style="color: #45546E; font-family:Plus Jakarta Sans;" type="text" digitOnly [isPercentage]="false"
                  [digitsAllowed]="60" [required]="true" matInput placeholder="PO Value"
                  formControlName="po_value" />
              </mat-form-field>
            </div>
          </div> 
          <div class="col-3" *ngIf="('payment_terms' | checkActive : this.formConfig: 'po-creation')">
              <div class="content-title">{{('payment_terms' | checkLabel : this.formConfig: 'po-creation':'Payment Terms')}}
                  <span class="required-star" *ngIf="('payment_terms' | checkMandatedField : this.formConfig: 'po-creation')"> *</span>
                       <span *ngIf="('payment_terms' | checkInfoIcon : this.formConfig: 'po-creation')">
                              <mat-icon class="info-icon" tooltip="{{('payment_terms' | checkTooltip : this.formConfig: 'po-creation': 'Payment Terms')}}">
                                                        info_outline
                              </mat-icon>
                        </span>
                </div>
                 <app-input-search-name [showSelect]="false" class="paymentTerms" [list]="payment_list"
                                                [required]="('payment_terms' | checkMandatedField : this.formConfig: 'po-creation')"
                                                placeholder="Select One" formControlName="payment_terms" [disabled]="false">
                  </app-input-search-name>
          </div>  
        </div> 
      </form>
          <div class="divider" style="margin:60px"></div>
          <div class="row footer-buttons">
              <div class="col-10"></div>
              <div class="button-container">
                 
                <button 
                type="button" 
                mat-raised-button 
                class="ml-3 create-employee-btn" 
                (click)="onSave()" 
                [disabled]="isSaving">
                {{ 'Save' }}
              </button>
               
                </div>
          </div>
          
    </div>
    
  </span>
</div>
