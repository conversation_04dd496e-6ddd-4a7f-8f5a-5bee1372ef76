import { Component, Inject, OnInit,InjectionToken} from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormGroup, Validators } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { DatePipe } from '@angular/common';
import { PmMasterService } from 'src/app/modules/project-management/services/pm-master.service';
import { SubSink } from 'subsink';
import moment from 'moment';
import { Router } from '@angular/router';
import {Input, HostListener, EventEmitter, Output, ViewContainerRef } from '@angular/core';
import {MatDialogConfig } from '@angular/material/dialog';
import { MomentDateAdapter, MAT_MOMENT_DATE_ADAPTER_OPTIONS } from '@angular/material-moment-adapter';
import { DateAdapter, MAT_DATE_LOCALE, MAT_DATE_FORMATS } from '@angular/material/core';
import { PmBillingService } from '../../services/pm-billing.service'
import { PmMilestoneCreationComponent } from '../pm-milestone-creation/pm-milestone-creation.component';
import * as _ from 'underscore';

import { PmBillingAdviceComponent } from '../pm-billing-advice/pm-billing-advice.component'
import { AttachTaskComponent } from '../attach-task/attach-task.component'
import { ChatCommentContextModalComponent } from 'src/app/modules/shared-lazy-loaded-components/chat-comment-context-modal/chat-comment-context-modal.component'
import { FormArray,FormControl} from '@angular/forms';
import { AttachmentServiceService } from "src/app/modules/project-management/shared-lazy-loaded/services/attachment-service.service"
import { ToasterService } from 'src/app/services/toaster-service/toaster.service';
import { PmMonthlyMilestoneCreationComponent } from '../pm-monthly-milestone-creation/pm-monthly-milestone-creation.component'
import { PmMultipleMilestoneCreationComponent } from '../pm-multiple-milestone-creation/pm-multiple-milestone-creation.component'
import { PmLandingPageService } from 'src/app/modules/project-management/features/pm-landing-page/features/pm-project-landing-page/services/pm-landing-page.service';

import Swal from 'sweetalert2';
import { PmAuthorizationService } from 'src/app/modules/project-management/services/pm-authorization.service';
// import { ToasterMessageService } from 'src/app/modules/project-management/shared-lazy-loaded/components/toaster-message/toaster-message.service';
import { PmLumpsumBillingAdviseComponent } from '../pm-lumpsum-billing-advise/pm-lumpsum-billing-advise/pm-lumpsum-billing-advise.component'
import { UtilityService } from 'src/app/services/utility/utility.service';
import { ToasterMessageService } from 'src/app/modules/project-management/shared-lazy-loaded/components/toaster-message/toaster-message.service';
export const TOASTER_MESSAGE_SERVICE_TOKEN = new InjectionToken<ToasterMessageService>('TOASTER_MESSAGE_SERVICE_TOKEN');
import { json } from 'd3';
import { MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import {ViewChild, TemplateRef } from '@angular/core';
import { GetNameByIdPipe } from 'src/app/modules/project-management/shared-lazy-loaded/pipes/get-name-by-id.pipe';

@Component({
  selector: 'app-po-master-creation',
  templateUrl: './po-master-creation.component.html',
  styleUrls: ['./po-master-creation.component.scss'],
  providers: [
    {
      provide: DateAdapter,
      useClass: MomentDateAdapter,
      deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS]
    },
    {
      provide: MAT_DATE_FORMATS,
      useValue: {
        parse: {
          dateInput: "DD-MMM-YYYY"
        },
        display: {
          dateInput: "DD-MMM-YYYY",
          monthYearLabel: "MMM YYYY"
        }
      }
    },
    { provide: TOASTER_MESSAGE_SERVICE_TOKEN, useClass: ToasterMessageService },
    GetNameByIdPipe
  ]
})
export class PoMasterCreationComponent implements OnInit {
  @Output() poAdded = new EventEmitter<void>(); // Add this line

  poForm: FormGroup;
  currency_list = []; // Populate your currency list
  ItemID: any
  isSaving: boolean = false;
  code: any;
  subs = new SubSink();
  projectID: any;
  milestoneId: any;
  potDatePicker: any;
  mode:any='create'
  recordId:any;
  columns: any = [];
  border: any;
  background: any;
  color: any;
  outsideClick: boolean = false
  check_month: boolean
  project_end_date: any
  project_start_date: any
  quote: any
  totalMilestoneValue: any = 0
  listView:true;
  moreOptionClicked: boolean = false;
  selectedRowIndex: any = null;
  at_risk: any = 0;
  calendarIcon: boolean = false;
  milestone_filter_status_list: any = []
  temp: any
  status_filter_name: any = 'All'
  status_filter_id: number=-1;
  temp_status: any;
  service_type_id: any
  retrieveMessages: any;
  button: any;
  shades: any;
  boxShadow: any;
  overallMilestoneValue: any = [];
  milestoneWriteAccess: boolean=false;
  projectValueAccess: boolean =this.authService.getProjectObjectAccess(58)
  billingAdviceAccess: boolean = this.authService.getProjectObjectAccess(59)
  lumpsumBillingAdviceProject: boolean= false;
  noDataImage: any;
  fontStyle: any;
  font_size_currency:any='10px'
  font_size_currency_card:any='12px'
  font_size_currency_header: any = '9px'
  item_status_id:number
  payment_terms_list:any
  payment_term_id:any
  scrollColor: any;
  ponumdisable:boolean=false;
  milestone_group_array:any=[]
  applicableInvoiceDateStatus: any=[]
  applicableAccrualDateStatus: any=[]
  projectMilestoneGrossValue: any=[]
  projectOrderValue: any =[]
  projectConsumedValue: any=[]
  record:any;
  projectRemainingValue: any=[]
  projectFinancialValueConfig: any=[]
  projectFinanicalValue: any=[]
  isProjectMilestoneGrossValueVisible: boolean = false
  isProjectOrderValueVisible:boolean = false
  isProjectConsumedValueVisible:boolean = false
  isProjectReaminingValueVisible:boolean = false
  oldScreen:boolean = false;
  isDataLoaded: boolean = false; 
  listMilestoneId: any
  currentDate = moment().format()
  restriction:boolean=false
  display = false
  milestone_status_list: any
  divVisible: boolean = false
  idCheck: any = null
  status_color: any
  
  isPopupVisible: boolean = false
  loadedData: any
  retriveddData: any
  profit_center: any
  check: boolean = false
  left: any = '28px'
  lumpsum_left: any = '-15px'
  edit_data: any
  openfilter: boolean = false
  duedate: boolean = false
  projectInfo: any;
  // isPopoverOpen:boolean=false
  status: any = []
  item_value_json: any = [];
  tagCheck: number
  convertCurrencyRecords: any = [];
  // editing:any
  // selectedItem: string;
  // items: string[] = ['open', 'Execution', 'YTB', 'Billed'];

  milestoneList: any = []
  edit: any
  idHigh: any
  isDisplayVisible: boolean = false
  searchTag: any
  tagsPopUp: any
  formConfig: any;
name_length=300;
  billingTabFlag: boolean = false;
  loading: boolean = true
  tags: any
  start_date_config: any[]; 
  end_date_config: any[];
  po_date_config: any[];
  payment_list: any = [];
  quote_id_list: any = []
  sort_tooltip: any = `<p>Sort newest -&gt; oldest</p>`
  quote_disable:boolean=false;
  q2c_check:boolean=false;
  min_start_date: any;
  max_end_date: any;
  oldFormValue: any;
  oldData: any;
  newData: any;

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: any,
    private router: Router,
    private fb: FormBuilder,
    private dialog: MatDialog,
    private PmMasterService: PmMasterService,
    public dialogRef: MatDialogRef<PoMasterCreationComponent>,
    @Inject(TOASTER_MESSAGE_SERVICE_TOKEN)  private toasterService: ToasterMessageService,
    private pmMasterService: PmMasterService,
    private PmBillingService: PmBillingService,
    private attachmentService: AttachmentServiceService,
    private authService: PmAuthorizationService,
    private pmService: PmLandingPageService,
    private snackBar: MatSnackBar,
    private utilityService: UtilityService,
    private _router: Router,
    private getNameByIdPipe: GetNameByIdPipe
  ) { }

  async ngOnInit(){
    this.ItemID = parseInt(this.router.url.split("/")[5])
    this.projectID = parseInt(this.router.url.split("/")[3])
    await this.getPoMasterData()
    this.initializeForm();
    await this.pmMasterService.getPMFormCustomizeConfigV().then((res: any) => {
      this.formConfig = res;
      if (this.formConfig.length > 0) {
        const retrieveStyles = _.where(this.formConfig, { type: "project-theme", field_name: "styles", is_active: true });
        const milestoneLandingConfig = _.where(this.formConfig, { type: "milestone-landing", field_name: "milestone_name", is_active: true });
        this.noDataImage = retrieveStyles[0].data.no_data_image ? retrieveStyles[0].data.no_data_image : "https://assets.kebs.app/No-milestone-image.png";
        this.button = retrieveStyles.length > 0 ? retrieveStyles[0].data.button_color ? retrieveStyles[0].data.button_color : "#90ee90" : "#90ee90";
        this.shades = retrieveStyles.length > 0 ? retrieveStyles[0].data.shades_color ? retrieveStyles[0].data.shades_color : "#C9E3B4" : "#C9E3B4";
        this.scrollColor = retrieveStyles.length > 0 ? retrieveStyles[0].data.scroll_color ? retrieveStyles[0].data.scroll_color : "#90ee90" : "#90ee90";
        document.documentElement.style.setProperty('--milestoneScroll', this.scrollColor)
        document.documentElement.style.setProperty('--milestoneShades', this.shades)
        document.documentElement.style.setProperty('--milestoneButton', this.button)
        this.fontStyle = retrieveStyles.length > 0 ? retrieveStyles[0].data.font_style ? retrieveStyles[0].data.font_style : "Roboto" : "Roboto";
        document.documentElement.style.setProperty('--milestoneFont', this.fontStyle);
        this.boxShadow = '0px 0px 0px 0px ' + this.button;
        const columnsConfig =  _.where(this.formConfig, { type: "po-landing-list", field_name: "style_config", is_active: true });
        const finacialValuesConfig =  _.where(this.formConfig, { type: "milestone-landing-header", field_name: "style_config", is_active: true });
        this.start_date_config = _.where(this.formConfig, { type: 'po-creation', field_name: 'start_date', is_mandant: true });
        this.end_date_config = _.where(this.formConfig, { type: 'po-creation', field_name: 'end_date', is_mandant: true });
        this.po_date_config = _.where(this.formConfig, { type: 'po-creation', field_name: 'po_date', is_mandant: true });
        const restriction=_.where(this.formConfig, { type: "po_date", field_name: "restriction",is_active:true});
    if(restriction.length > 0){
      this.restriction = true
    }
    
        this.setValidators();
        this.columns = columnsConfig[0]?.columnConfig ? columnsConfig[0]?.columnConfig : null;
        this.projectFinancialValueConfig = finacialValuesConfig[0]?.columnConfig ? finacialValuesConfig[0]?.columnConfig : null;
      }
    })
    await this.getProjectCurrency()
    await this.PmBillingService.getMaxMinProjectDatesPO(
      this.projectID,
      this.ItemID
    ).then((res) => {
      if (res['messType'] == 'S') {
        if (res['data'].length > 0) {
          this.min_start_date = moment(res['data'][0]['min_date']).format('YYYY-MM-DD');
      this.max_end_date = moment(res['data'][0]['max_date']).format('YYYY-MM-DD');
          console.log(this.min_start_date)
        }
      }
    });
    this.currency_list = this.PmMasterService.currency_list;
    this.PmBillingService.getQuoteIDList(this.projectID, this.ItemID ).then((res) => {
      if (res['messType'] == 'S' && res['data'].length > 0) {
        this.quote_id_list = res['data']
      }
      else {
        this.quote_id_list = []
      }
    })
    await this.pmMasterService.getPaymentTermsList().then((res: any) => {
      if (res['messType'] == "S") {
        this.payment_list = res['data'];
      }
      else{
        this.payment_list=[]
      }

    });
    this.oldFormValue = this.poForm.value;
    this.oldData ={
      "po_number": this.oldFormValue.po_number ? this.oldFormValue.po_number : null,
      "quote_id": this.oldFormValue.quote_id ? this.oldFormValue.quote_id : null,
      "po_reference":this.oldFormValue.po_reference ? this.oldFormValue.po_reference : null,
      "start_date":this.oldFormValue.start_date ? moment(this.oldFormValue.start_date).format('DD-MMM-YYYY') : null,
      "end_date":this.oldFormValue.end_date ? moment(this.oldFormValue.end_date).format('DD-MMM-YYYY') : null,
      "po_date":this.oldFormValue.po_date ? moment(this.oldFormValue.po_date).format('DD-MMM-YYYY') : null,
      "po_value":this.oldFormValue.po_value ? this.oldFormValue.po_value : null,
      "payment_terms": this.oldFormValue.payment_terms ? this.getNameByIdPipe.transform(this.oldFormValue.payment_terms, this.payment_list) : null,
  }
  await this.checkMilestoneStatus()
    this.loading=false
   
  }
  async getProjectCurrency(){
    this.subs.sink = this.PmBillingService.getProjectCurrency(this.projectID,this.ItemID).subscribe(
      (res: any) => {
        this.code = res.data.currency_code
        console.log(this.data)
       },
       (err: any) => {
         console.log(err);
        }
    );
  
  }
  initializeForm(): void {
    this.poForm = this.fb.group({
      po_date: [''],
      po_reference: [''],
      po_value: [''],
      po_number: [''],
      start_date: [''],
      end_date: [''],
      quote_id:[''],
      payment_terms:['']
    });
    if (this.data && this.data.editData) {
      this.code = this.data.code;
      this.mode = this.data.mode;
      this.recordId=this.data.editData.id;
      this.patchFormValues(this.data.editData);
      this.quote_disable=true
      
    }
  }
  setValidators(): void {
    if (this.start_date_config.length > 0 && this.start_date_config[0].is_mandant) {
      this.poForm.get('start_date').setValidators([Validators.required]);
    } else {
      this.poForm.get('start_date').clearValidators();
    }

    if (this.end_date_config.length > 0 && this.end_date_config[0].is_mandant) {
      this.poForm.get('end_date').setValidators([Validators.required]);
    } else {
      this.poForm.get('end_date').clearValidators();
    }

    if (this.po_date_config.length > 0 && this.po_date_config[0].is_mandant) {
      this.poForm.get('po_date').setValidators([Validators.required]);
    } else {
      this.poForm.get('po_date').clearValidators();
    }

    this.poForm.updateValueAndValidity();
  }
  
  

  patchFormValues(data: any): void {
    const currency = data?.currencyList.find(c => c.currency_code.toLowerCase() === this.code.toLowerCase());
    const poValue = currency ? currency.value : null;
    this.poForm.patchValue({
     
      po_number: data?.po_number,
      po_value: poValue,
      po_reference: data?.po_ref_no,
      start_date: data?.po_startDate ? moment(data.po_startDate).format('YYYY-MM-DD'):'',
      end_date:data?.po_endDate ? moment(data.po_endDate).format('YYYY-MM-DD'):'',
      po_date:data?.po_Date ? moment(data.po_Date, 'DD-MMM-YYYY').format('YYYY-MM-DD') : '',
      payment_terms:data?.payment_id,
      quote_id:data?.quote_id 
    });
  }
  
  

  onCloseClick(): void {
    this.dialog.closeAll();
  }

  onCancel(): void {
    this.dialog.closeAll();
  }
  async getPoMasterData():Promise<void>{
    this.subs.sink = this.PmBillingService.getPoMastercheckData(this.projectID,this.ItemID).subscribe(
      (res: any) => {
        this.q2c_check = res.data[0].with_opportunity
        console.log(this.q2c_check)
        console.log(res.data[0].with_opportunity)
       },
       (err: any) => {
         console.log(err);
        }
    );
 }
 async onSave() {
  // Disable the button to prevent multiple clicks
  this.isSaving = true;
  this.setValidators();
  
  const formData = this.poForm.value;
  
  console.log(formData)

  if (formData.po_value <= 0) {
    this.toasterService.showWarning('PO Value must be greater than 0', 10000);
    this.isSaving = false;
    return;
  }

  if (!formData.po_number || formData.po_number.trim() === '') {
    this.toasterService.showWarning('PO Number cannot be empty', 10000);
    this.isSaving = false;
    return;
  }

  if (this.poForm.valid) {
    console.log(formData, this.ItemID, this.projectID);

    try {
      const res: any = await this.PmBillingService.checkPoNumberExists(formData.po_number, this.projectID, this.ItemID).toPromise();

      if (res['data'].length > 0) {
        this.toasterService.showWarning('PO Number already exists', 10000);
        this.isSaving = false;
        return; // Exit the method if PO number exists
      }

      let result: any;

      if (this.mode == 'create') {
        console.log(this.mode);
        result = await this.insertPoMaster(formData, this.ItemID, this.projectID);
      } else {
        console.log(this.mode);
        this.newData ={
          "po_number": formData.po_number ? formData.po_number : null,
          "quote_id": formData.quote_id ? formData.quote_id : null,
          "po_reference":formData.po_reference ? formData.po_reference : null,
          "start_date":formData.start_date ? moment(formData.start_date).format('DD-MMM-YYYY') : null,
          "end_date":formData.end_date ? moment(formData.end_date).format('DD-MMM-YYYY') : null,
          "po_date":formData.po_date ? moment(formData.po_date).format('DD-MMM-YYYY') : null,
          "po_value":formData.po_value ? formData.po_value : null,
          "payment_terms": formData.payment_terms ? this.getNameByIdPipe.transform(formData.payment_terms, this.payment_list) : null,
      }
        result = await this.updatePoMaster(formData, this.ItemID, this.projectID, this.recordId, this.oldData, this.newData);
      }

      console.log(result);

      if (result.messType === 'S') {
        const successMessage = this.mode === 'create' ? 'Purchase Order Added Successfully' : 'Purchase Order Updated Successfully';
        this.toasterService.showSuccess(result.message , 10000);
        this.poAdded.emit();
        this.dialogRef.close();
      } else if (result.messType === "W") {
        this.toasterService.showWarning(result.message, 10000);
      } else {
        const errorMessage = this.mode === 'create' ? 'Purchase Order Addition Unsuccessful' : 'Purchase Order Updation Unsuccessful';
        this.toasterService.showError(errorMessage);
      }
    } catch (error) {
      const errorMessage = this.mode === 'create' ? 'Purchase Order Addition Unsuccessful' : 'Purchase Order Updation Unsuccessful';
      this.toasterService.showError(errorMessage);
      console.error(error);
    } finally {
      // Re-enable the button after the API call is complete
      this.isSaving = false;
    }
  } else {
    const invalidFields = this.getInvalidFields();
    const firstInvalidField = invalidFields[0];
    this.toasterService.showWarning(`${firstInvalidField} is Mandatory`, 10000);
    this.isSaving = false;
  }
}
  
  getInvalidFields(): string[] {
    const invalidFields = [];
    for (const controlName in this.poForm.controls) {
      if (this.poForm.controls[controlName].invalid) {
        const fieldName = this.getFieldLabel(controlName);
        invalidFields.push(fieldName);
      }
    }
    return invalidFields;
  }
  
  getFieldLabel(controlName: string): string {
    const fieldConfig = this.formConfig.find(config => config.field_name === controlName);
    return fieldConfig ? fieldConfig.label : controlName;
  }
  cleanFormData(formData: any): any {
    for (const key in formData) {
      if (formData.hasOwnProperty(key)) {
        const value = formData[key];
        if (value instanceof Date && isNaN(value.getTime())) {
          formData[key] = '';
        }
      }
    }
    return formData;
  }
  
  
  isMandate(field: any) {
    const mandate = _.where(this.formConfig, { type: "po-creation", field_name: field, is_active: true });
    if (mandate.length > 0) {
      const isMandate = mandate[0].is_mandant;
      return isMandate;
    }
  }
  
  

  async updatePoMaster(formData, ItemID, projectID,recordId, oldData, newData) {
    return new Promise((resolve, reject) => {
      this.subs.sink = this.PmBillingService.updatePoMaster(formData, ItemID, projectID,recordId, oldData, newData).subscribe(
        (res: any) => {
          if (res.err === false) {
            resolve(res);
          } else {
            reject(res);
          }
        },
        (err) => {
          console.error(err);
          reject(err);
        }
      );
    });
  }
  async insertPoMaster(formData, ItemID, projectID) {
    return new Promise((resolve, reject) => {
      this.subs.sink = this.PmBillingService.insertPoMaster(formData, ItemID, projectID).subscribe(
        (res: any) => {
          if (res.err === false) {
            resolve(res);
          } else {
            reject(res);
          }
        },
        (err) => {
          console.error(err);
          reject(err);
        }
      );
    });
  }

  async checkMilestoneStatus() :Promise<ApiResponse>{
    return new Promise((resolve, reject) => {
      this.subs.sink = this.PmBillingService.checkmilestonestatus(this.projectID,this.ItemID,this.data?.editData?.po_number).subscribe(
        (res: any) => {
          if (res.err === false) {
            if(res.disable===true){
              this.ponumdisable=true
            }
            resolve(res);
          } else {
            reject(res);
          }
        },
        (err) => {
          console.error(err);
          reject(err);
        }
      );
    });
  }
}
export interface ApiResponse {
  messType: 'S' | 'W' | 'E';
  msg?: string;
  data?: any;
}