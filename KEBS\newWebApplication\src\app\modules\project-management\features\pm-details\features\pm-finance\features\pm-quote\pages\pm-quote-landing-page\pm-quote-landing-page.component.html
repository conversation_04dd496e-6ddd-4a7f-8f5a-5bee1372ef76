<div class="finance-tab">
  <div class="tab-name font-family">Opportunity Quote</div>

  <ng-container *ngIf="loaderComponent">
    <div class="loader-container">
      <mat-spinner class="green-spinner loader" diameter="40" matTooltip="Loading ..."></mat-spinner>
    </div>
  </ng-container>

  <div class="no-data" *ngIf="enableNoData && !loaderComponent">
    <div class="image-class">
      <img [src]="noDataImage ? noDataImage : 'https://assets.kebs.app/images/no_data_found.png'" />
    </div>
    <span class="no-img-text">No Quote Found!</span>
  </div>

  <div class="all-details" *ngIf="!enableNoData && !loaderComponent">

    <div class="finance-tab-header">
      <div *ngFor="let col of colList" [ngClass]="col.class" class="header-cell" [matTooltip]="col.colName" style="cursor: pointer;">
        {{ col.colName }}
      </div>
    </div>

    <div class="finance-tab-body">

      <ng-container *ngFor="let opportunity of groupedData">

        <div class="opportunity-quote-row">

          <div class="opportunity-quote-header">

            <div class="opportunity-details">
              <div class="header-cell"># {{ opportunity.opportunity_id }}</div>
              <span class="divider"> - </span>
              <div class="header-cell" [matTooltip]="opportunity.opportunity_name">
                {{ opportunity.opportunity_name | slice:0:30 }}{{ opportunity.opportunity_name.length > 15 ? '...' :
                ''}}
                <span class="status-label" [ngStyle]="{'background-color': opportunity.quotes[0].status_color}">
                  {{ opportunity.quotes[0].status_name || '-' }}
                </span>
                <span *ngIf="opportunity.quotes[0].at_risk" class="status-label" [ngStyle]="{'background-color': '#FF3A46', color:'#FFFFFF'}">
                  At Risk
                </span>
                <span *ngIf="opportunity.quotes[0].blanked_po" class="status-label" [ngStyle]="{'background-color': '#8B95A5', color:'#FFFFFF'}">
                  Blanket PO
                </span>
              </div>
              <div class="side-arrow"
                (click)="navigateToOpportunity(opportunity.opportunity_id, opportunity.opportunity_name)">
                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="currentColor"
                  class="bi bi-arrow-up-right" viewBox="0 0 16 16">
                  <path fill-rule="evenodd"
                    d="M14 2.5a.5.5 0 0 0-.5-.5h-6a.5.5 0 0 0 0 1h4.793L2.146 13.146a.5.5 0 0 0 .708.708L13 3.707V8.5a.5.5 0 0 0 1 0z" />
                </svg>
              </div>
              <mat-icon *ngIf="docicon" class="doc-icons"  (click)="navigateToAttachments(opportunity.opportunity_id, opportunity.opportunity_name)">attach_file</mat-icon>
            </div>

            <div class="quote-details">
              <div class="header-cell"># {{ opportunity.quotes[0].quote_id }}</div>
              <span class="divider"> - </span>
              <div class="header-cell" [matTooltip]="opportunity.quotes[0].quote_name">
                {{ opportunity.quotes[0].quote_name | slice:0:30 }}{{ opportunity.quotes[0].quote_name.length > 15 ?
                '...' : '' }}
              </div>
              <div class="arrow-expand-collapse">
                <svg *ngIf="!opportunity.expanded" (click)="toggleOpportunity(opportunity, $event)"
                  xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="currentColor"
                  class="bi bi-chevron-down" viewBox="0 0 16 16">
                  <path fill-rule="evenodd"
                    d="M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z" />
                </svg>
                <svg *ngIf="opportunity.expanded" (click)="toggleOpportunity(opportunity, $event)"
                  xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="currentColor" class="bi bi-chevron-up"
                  viewBox="0 0 16 16">
                  <path fill-rule="evenodd"
                    d="M1.646 11.354a.5.5 0 0 1 .708 0L8 5.707l5.646 5.647a.5.5 0 0 1-.708.708l-6-6a.5.5 0 0 1-.708 0l-6 6a.5.5 0 0 1 0 .708z" />
                </svg>
              </div>
            </div>

          </div>

          <div *ngIf="opportunity.expanded">
            <ng-container *ngFor="let quote of opportunity.quotes">
              <div class="quote-details-row">
                <div class="table-row">
                <div *ngFor="let col of colList" [ngClass]="col.class" class="text-cell">
                  <span [matTooltip]="
                      (col.keyName === 'total_cost' || col.keyName === 'rate_per_unit') && isFinancialValuesHidden
                        ? '****'
                        : (
                            col.keyName === 'total_cost'
                              ? (getCurrencyValue(quote) | number: '1.2-2')
                              : (quote[col.keyName]
                                  ? (col.type === 'decimal' || col.keyName === 'rate_per_unit'
                                      ? (quote[col.keyName] | number: '1.2-2')
                                      : quote[col.keyName])
                                  : '-')
                          )
                    ">
                    {{
                    (col.keyName === 'total_cost' || col.keyName === 'rate_per_unit') && isFinancialValuesHidden
                    ? '****'
                    : (
                    col.keyName === 'total_cost'
                    ? (getCurrencyValue(quote) | number: '1.2-2')
                    : (quote[col.keyName]
                    ? (col.type === 'decimal' || col.keyName === 'rate_per_unit'
                    ? (quote[col.keyName] | number: '1.2-2')
                    : quote[col.keyName])
                    : '-')
                    )
                    }}
                  </span>
                </div>


                </div>
              </div>
            </ng-container>
          </div>

        </div>

      </ng-container>

    </div>

  </div>

</div>