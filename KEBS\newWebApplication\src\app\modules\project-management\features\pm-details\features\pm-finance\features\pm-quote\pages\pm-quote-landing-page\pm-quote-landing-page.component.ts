import { Component, HostListener, OnInit } from '@angular/core';
import { SubSink } from 'subsink';
import { PmQuoteServiceService } from '../../services/pm-quote-service.service';
import { Router } from '@angular/router';
import { PmMasterService } from 'src/app/modules/project-management/services/pm-master.service';
import * as _ from 'underscore';
import { UtilityService } from 'src/app/services/utility/utility.service';
import { PmAuthorizationService } from 'src/app/modules/project-management/services/pm-authorization.service'
 

export interface Column {
  keyName: string;
  colName: string;
  isActive: boolean;
  width: number;
  type: string;
}

export interface Quote {
  position: number;
  task_name: string;
  position_name: string;
  resource_count: number;
  experience_label: string;
  location: string;
  nationality_description: string;
  unit_name: string;
  rate_per_unit: number;
  entity_name: string;
  division_name: string;
  sub_division_name: string;
  quantity: number;
  total_cost: JSON;
  status_name?: string;
  [key: string]: any;
}

export interface Opportunity {
  opportunity_id: string;
  opportunity_name: string;
  expanded: boolean;
  quotes: Quote[];
}

@Component({
  selector: 'app-pm-quote-landing-page',
  templateUrl: './pm-quote-landing-page.component.html',
  styleUrls: ['./pm-quote-landing-page.component.scss']
})
export class PmQuoteLandingPageComponent implements OnInit {

  colList: Array<Column> = [];
  dataList: Array<Quote> = [];
  groupedData: Array<Opportunity> = [];
  projectId: number;
  itemId: number;
  loaderComponent: boolean = false;
  enableNoData: boolean = false;
  formConfig: any = [];
  button: any;
  noDataImage: any;
  fontStyle: any;
  code: string = 'USD';
  status_list: any;
  docicon:boolean=false;
  private subs = new SubSink();
  milestoneConfigDetails: any;
  isMilestoneVisible: boolean = false;
  isFinancialValuesHidden:boolean=false
  constructor(private _quoteService: PmQuoteServiceService, private _router: Router, private pmMasterService: PmMasterService,private _utilityService: UtilityService,private authService: PmAuthorizationService) { }

  async ngOnInit() {
    this.loaderComponent = true;
    this.itemId = parseInt(this._router.url.split('/')[5]);
    this.projectId = parseInt(this._router.url.split('/')[3]);
    this.isFinancialValuesHidden=await this.authService.getProjectWiseObjectAccess( this.projectId,this.itemId, 178)
    this.calculateDynamicStyle()
    this.dataList = await this.getProjectOpportunityData();

    await this.pmMasterService.getPMFormCustomizeConfigV().then((res: any) => {
      if (res) {
        this.formConfig = res;
      }
    });

    const retrieveStyles2 = _.where(this.formConfig, { type: "project-theme", field_name: "styles", is_active: true });

    if (retrieveStyles2.length > 0) {
      this.button = retrieveStyles2[0].data.button_color ? retrieveStyles2[0].data.button_color : "#90ee90";
      document.documentElement.style.setProperty('--quoteButton', this.button);
      this.noDataImage = retrieveStyles2[0].data.no_data_image2 ? retrieveStyles2[0].data.no_data_image2 : "https://assets.kebs.app/images/no_data_found.png";
      this.fontStyle = retrieveStyles2.length > 0 ? retrieveStyles2[0].data.font_style ? retrieveStyles2[0].data.font_style : "Roboto" : "Roboto";
      document.documentElement.style.setProperty('--quoteFont', this.fontStyle);
    }

    const colConfig = _.findWhere(this.formConfig, { type: "opportunity-quote", field_name: "finance_tab", is_active: true });
    // console.log('Column Configuration:', colConfig);

    
    let milestoneConfig = _.where(this.formConfig,{
      type: 'position-card',
      field_name: 'milestone_name',
      is_active: true,
    });
    const docshow=_.where(this.formConfig, { type: "finance", field_name: "document",is_active:true});
    if(docshow.length > 0){
      this.docicon = true
    }


    this.milestoneConfigDetails = (milestoneConfig && milestoneConfig.length > 0 ? (milestoneConfig[0].applicable_service_type_id ? milestoneConfig[0].applicable_service_type_id : []) : []);

    this.isMilestoneVisible = this.milestoneConfigDetails.includes(this.dataList[0].service_type_id ? this.dataList[0].service_type_id : [])

    const columnToUpdate = colConfig.columns_config.find(col => col.keyName === "milestone_name");
    if (columnToUpdate) {
      columnToUpdate.isActive = this.isMilestoneVisible; // Update the isActive property to false (or any value you want)
    }

    if (colConfig && colConfig.columns_config) {
      this.colList = colConfig.columns_config.filter(col => col.isActive);
      // console.log('Filtered Columns:', this.colList);
    }

    this.groupedData = this.groupByOpportunity(this.dataList);
    this.loaderComponent = false;
  }

  /**
  * @description get project opportunity data
  */
  getProjectOpportunityData(): Promise<Quote[]> {
    let params = {
      project_id: this.projectId,
      item_id: this.itemId
    }
    return new Promise((resolve) => {
      this.subs.sink = this._quoteService.getProjectOpportunityQuote(params).subscribe(
        (res: any) => {
          // console.log('Response Data:', res.data);
          if (res.messType == 'S') {
            if (res.data.length == 0) {
              this.enableNoData = true;
              resolve([]);
            } else {
              resolve(res.data);
            }
          } else {
            this.enableNoData = true;
            resolve([]);
          }
        }, () => {
          this.enableNoData = true;
          resolve([]);
        });
    });
  }

  /**
   * @description  Groups data by opportunity_id and opportunity_name, keeping the quotes together
   * @param  data - The data to be grouped.
   * @returns  An array of grouped data.
   */
  groupByOpportunity(data: Quote[]): Opportunity[] {
    let groupedData: { [key: string]: Opportunity } = {};
    data.forEach((item: Quote) => {
      if (!groupedData[item.opportunity_id]) {
        groupedData[item.opportunity_id] = {
          opportunity_id: item.opportunity_id,
          opportunity_name: item.opportunity_name,
          expanded: false,
          quotes: []
        };
      }
      groupedData[item.opportunity_id].quotes.push(item);
    });

    const groupedArray = Object.values(groupedData);
    groupedArray.forEach((opportunity, index) => {
      if (index < 2) {
        opportunity.expanded = true;
      } else {
        opportunity.expanded = false;
      }
    });

    return groupedArray;
  }

  /**
  * @description function to toggle opportunity quote details
  * @param opportunity
  */
  toggleOpportunity(opportunity: Opportunity, event: Event) {
    opportunity.expanded = !opportunity.expanded;
    event.stopPropagation();
  }

  /**
  * @description calculates the dynamic styles
  */
  @HostListener('window:resize')
  onResize() {
    this.calculateDynamicStyle();
  }

  calculateDynamicStyle() {
    let quoteWidth = window.innerWidth - 142 + 'px'
    document.documentElement.style.setProperty('--dynamicWidth', quoteWidth)
    let quoteHeight = window.innerHeight - 301 + 'px'
    document.documentElement.style.setProperty('--dynamicHeight', quoteHeight)
  }


  getCurrencyValue(data: Quote) {
    if (!data || !data.rate_currency_code || !Array.isArray(data.total_cost)) {
      return null;
    }
    let rate_currency_code = data.rate_currency_code;
    let item = _.find(data.total_cost, item => item.currency_code === rate_currency_code);
    if (!item) {
      return null;
    }
    return item ? item.value : null;
  }

  navigateToOpportunity(opportunity_id: string, opportunity_name: string) {
    let navigationUrl = window.location.origin +
      "/main/opportunities/" +
      opportunity_id +
      "/" +
      this._utilityService.encodeURIComponent(opportunity_name);
    window.open(navigationUrl, '_blank');
  }
  navigateToAttachments(opportunity_id: string, opportunity_name: string) {
    let navigationUrl = window.location.origin +
      "/main/opportunities/" +
      opportunity_id +
      "/" +
      this._utilityService.encodeURIComponent(opportunity_name)+"/new-attachments";
    window.open(navigationUrl, '_blank');
  }

  ngOnDestroy(): void {
    this.subs.unsubscribe();
  }
}