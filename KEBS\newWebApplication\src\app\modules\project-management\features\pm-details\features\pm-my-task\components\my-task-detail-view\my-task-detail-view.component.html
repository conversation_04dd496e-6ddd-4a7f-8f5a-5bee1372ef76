<div class="task-detailed-view">
  <div *ngIf="isPageLoading" class="loading-img">
    <div class="load-img">
      <img *ngIf="loadingGifUrl" [src]="loadingGifUrl" />
    </div>
    <div class="loading-wrapper">
      <div class="loading">Loading...</div>
    </div>
  </div>
  <div class="task-details" *ngIf="!isPageLoading">
    <div class="task-header">
      <div class="col-12 header-details">
        <div class="task-info">
          <button (click)="routeBacktoList()" class="back-btn" tooltip="Back">
            <svg xmlns="http://www.w3.org/2000/svg" height="18" viewBox="0 0 24 24" width="18">
              <path d="M0 0h24v24H0z" fill="none" />
              <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z" />
            </svg>
          </button>
          <div *ngIf="isDataVisible('billable_act')"
            tooltip="{{task['billable_act'] === 1 ? 'Billable' : 'Non-Billable'}}">
            <ng-container>
              <ng-container *ngIf="task['billable_act'] === 1 ; else notBillable">
                <svg [ngStyle]="{ fill: '#515965', 'margin-bottom': '2px' }" xmlns="http://www.w3.org/2000/svg"
                  enable-background="new 0 0 24 24" height="18" viewBox="0 0 24 24" width="18">
                  <g>
                    <rect fill="none" height="24" width="24" />
                  </g>
                  <g>
                    <path
                      d="M12,2C6.48,2,2,6.48,2,12s4.48,10,10,10s10-4.48,10-10S17.52,2,12,2z M12.88,17.76V19h-1.75v-1.29 c-0.74-0.18-2.39-0.77-3.02-2.96l1.65-0.67c0.06,0.22,0.58,2.09,2.4,2.09c0.93,0,1.98-0.48,1.98-1.61c0-0.96-0.7-1.46-2.28-2.03 c-1.1-0.39-3.35-1.03-3.35-3.31c0-0.1,0.01-2.4,2.62-2.96V5h1.75v1.24c1.84,0.32,2.51,1.79,2.66,2.23l-1.58,0.67 c-0.11-0.35-0.59-1.34-1.9-1.34c-0.7,0-1.81,0.37-1.81,1.39c0,0.95,0.86,1.31,2.64,1.9c2.4,0.83,3.01,2.05,3.01,3.45 C15.9,17.17,13.4,17.67,12.88,17.76z" />
                  </g>
                </svg>
              </ng-container>

              <ng-template #notBillable>
                <svg [ngStyle]="{ fill: '#B9C0CA', 'margin-bottom': '2px' }" xmlns="http://www.w3.org/2000/svg"
                  enable-background="new 0 0 24 24" height="18" viewBox="0 0 24 24" width="18">
                  <g>
                    <rect fill="none" height="24" width="24" />
                  </g>
                  <g>
                    <path
                      d="M12,2C6.48,2,2,6.48,2,12s4.48,10,10,10s10-4.48,10-10S17.52,2,12,2z M12.88,17.76V19h-1.75v-1.29 c-0.74-0.18-2.39-0.77-3.02-2.96l1.65-0.67c0.06,0.22,0.58,2.09,2.4,2.09c0.93,0,1.98-0.48,1.98-1.61c0-0.96-0.7-1.46-2.28-2.03 c-1.1-0.39-3.35-1.03-3.35-3.31c0-0.1,0.01-2.4,2.62-2.96V5h1.75v1.24c1.84,0.32,2.51,1.79,2.66,2.23l-1.58,0.67 c-0.11-0.35-0.59-1.34-1.9-1.34c-0.7,0-1.81,0.37-1.81,1.39c0,0.95,0.86,1.31,2.64,1.9c2.4,0.83,3.01,2.05,3.01,3.45 C15.9,17.17,13.4,17.67,12.88,17.76z" />
                  </g>
                </svg>
              </ng-template>
            </ng-container>
          </div>
          <div *ngIf="isDataVisible('description')" class="task-name" tooltip="{{task['description']}}">
            {{task['description'] ? task['description'] : '-'}}
          </div>
        </div>
        <div class="task-actions">
          <div class="edit-actions">
            <div *ngIf="isTaskEditAllowed" class="edit-icon pr-2" style="cursor: pointer;" tooltip="Edit" (click)="navigateToEdit(task['id'])">
              <svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="0.5" y="0.5" width="25" height="25" rx="5.5" stroke="#7D838B"/>
                <mask id="mask0_14526_13527" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="4" y="4" width="18" height="18">
                <rect x="4" y="4" width="18" height="18" fill="#D9D9D9"/>
                </mask>
                <g mask="url(#mask0_14526_13527)">
                <path d="M7.75 18.25H8.81875L16.15 10.9187L15.0813 9.85L7.75 17.1812V18.25ZM6.25 19.75V16.5625L16.15 6.68125C16.3 6.54375 16.4656 6.4375 16.6469 6.3625C16.8281 6.2875 17.0188 6.25 17.2188 6.25C17.4187 6.25 17.6125 6.2875 17.8 6.3625C17.9875 6.4375 18.15 6.55 18.2875 6.7L19.3188 7.75C19.4688 7.8875 19.5781 8.05 19.6469 8.2375C19.7156 8.425 19.75 8.6125 19.75 8.8C19.75 9 19.7156 9.19062 19.6469 9.37187C19.5781 9.55312 19.4688 9.71875 19.3188 9.86875L9.4375 19.75H6.25ZM15.6063 10.3938L15.0813 9.85L16.15 10.9187L15.6063 10.3938Z" fill="#7D838B"/>
                </g>
                </svg>                
            </div>
            <div  *ngIf="isTaskDeleteAllowed" class="delete-icon pr-2" style="cursor: pointer;" tooltip="Delete" (click)="deleteTask(task['id'])">
              <svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="0.5" y="0.5" width="25" height="25" rx="5.5" stroke="#7D838B"/>
                <mask id="mask0_14526_13532" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="4" y="4" width="18" height="18">
                <rect x="4" y="4" width="18" height="18" fill="#D9D9D9"/>
                </mask>
                <g mask="url(#mask0_14526_13532)">
                <path d="M9.48081 19.3743C9.10794 19.3743 8.78875 19.2416 8.52325 18.9761C8.25775 18.7106 8.125 18.3914 8.125 18.0185V8.49931H7.9375C7.77812 8.49931 7.64456 8.44538 7.53681 8.3375C7.42894 8.22962 7.375 8.096 7.375 7.93663C7.375 7.77712 7.42894 7.64356 7.53681 7.53594C7.64456 7.42819 7.77812 7.37431 7.9375 7.37431H10.75C10.75 7.19069 10.8147 7.03419 10.9441 6.90481C11.0733 6.77556 11.2297 6.71094 11.4134 6.71094H14.5866C14.7703 6.71094 14.9267 6.77556 15.0559 6.90481C15.1853 7.03419 15.25 7.19069 15.25 7.37431H18.0625C18.2219 7.37431 18.3554 7.42825 18.4632 7.53613C18.5711 7.644 18.625 7.77763 18.625 7.937C18.625 8.0965 18.5711 8.23006 18.4632 8.33769C18.3554 8.44544 18.2219 8.49931 18.0625 8.49931H17.875V18.0185C17.875 18.3914 17.7423 18.7106 17.4768 18.9761C17.2113 19.2416 16.8921 19.3743 16.5192 19.3743H9.48081ZM16.75 8.49931H9.25V18.0185C9.25 18.0859 9.27162 18.1412 9.31487 18.1844C9.35812 18.2277 9.41344 18.2493 9.48081 18.2493H16.5192C16.5866 18.2493 16.6419 18.2277 16.6851 18.1844C16.7284 18.1412 16.75 18.0859 16.75 18.0185V8.49931ZM11.6157 16.7493C11.7751 16.7493 11.9086 16.6954 12.0164 16.5877C12.124 16.4798 12.1778 16.3462 12.1778 16.1868V10.5618C12.1778 10.4024 12.1239 10.2688 12.016 10.1609C11.9082 10.0532 11.7746 9.99931 11.6151 9.99931C11.4558 9.99931 11.3222 10.0532 11.2144 10.1609C11.1068 10.2688 11.053 10.4024 11.053 10.5618V16.1868C11.053 16.3462 11.1069 16.4798 11.2146 16.5877C11.3225 16.6954 11.4562 16.7493 11.6157 16.7493ZM14.3849 16.7493C14.5442 16.7493 14.6778 16.6954 14.7856 16.5877C14.8932 16.4798 14.947 16.3462 14.947 16.1868V10.5618C14.947 10.4024 14.8931 10.2688 14.7854 10.1609C14.6775 10.0532 14.5438 9.99931 14.3843 9.99931C14.2249 9.99931 14.0914 10.0532 13.9836 10.1609C13.876 10.2688 13.8222 10.4024 13.8222 10.5618V16.1868C13.8222 16.3462 13.8761 16.4798 13.984 16.5877C14.0917 16.6954 14.2254 16.7493 14.3849 16.7493Z" fill="#6E7B8F"/>
                </g>
                </svg>                
            </div>
          </div>
          <div class="general-actions">
            <div class="fav-icon pr-2" style="cursor: pointer;" (click)="markAsFavourite(task)">
              <div *ngIf="!task.isFavTask"  tooltip="Mark as Favourite">
                <svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <rect x="0.5" y="0.5" width="25" height="25" rx="5.5" fill="#FFFFFF"/> <!-- Background is white -->
                  <rect x="0.5" y="0.5" width="25" height="25" rx="5.5" stroke="#7D838B"/> <!-- Border is light gray -->
                  <mask id="mask0_14526_13536" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="4" y="4" width="18" height="18">
                    <rect x="4" y="4" width="18" height="18" fill="#D9D9D9"/>
                  </mask>
                  <g mask="url(#mask0_14526_13536)">
                    <path d="M12.9997 16.516L10.082 18.2755C9.97329 18.339 9.86436 18.3657 9.75523 18.3556C9.64611 18.3454 9.54736 18.3087 9.45898 18.2453C9.37048 18.1818 9.30217 18.0989 9.25404 17.9965C9.20604 17.8941 9.19836 17.7809 9.23098 17.6567L10.0055 14.3438L7.42967 12.1141C7.33342 12.0304 7.27211 11.9336 7.24573 11.8234C7.21923 11.7133 7.22617 11.6063 7.26654 11.5024C7.30692 11.3987 7.36511 11.3139 7.44111 11.248C7.51711 11.1821 7.62098 11.1391 7.75273 11.1188L11.1521 10.8218L12.4719 7.69337C12.5199 7.577 12.5927 7.49094 12.6904 7.43519C12.788 7.37944 12.8911 7.35156 12.9997 7.35156C13.1084 7.35156 13.2115 7.37944 13.3091 7.43519C13.4067 7.49094 13.4795 7.577 13.5275 7.69337L14.8474 10.8218L18.2467 11.1188C18.3785 11.1391 18.4824 11.1821 18.5584 11.248C18.6344 11.3139 18.6925 11.3987 18.7329 11.5024C18.7733 11.6063 18.7802 11.7133 18.7537 11.8234C18.7274 11.9336 18.666 12.0304 18.5698 12.1141L15.9939 14.3438L16.7685 17.6567C16.8011 17.7809 16.7934 17.8941 16.7454 17.9965C16.6973 18.0989 16.629 18.1818 16.5405 18.2453C16.4521 18.3087 16.3534 18.3454 16.2442 18.3556C16.1351 18.3657 16.0262 18.339 15.9174 18.2755L12.9997 16.516Z" fill="#7D838B"/> <!-- Star is a light gray -->
                  </g>
                </svg>    
              </div>
              <div *ngIf="task.isFavTask"  tooltip="Remove from favourite">
                <svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <rect x="0.5" y="0.5" width="25" height="25" rx="5.5" fill="#FFF8EC"/>
                  <rect x="0.5" y="0.5" width="25" height="25" rx="5.5" stroke="#FFBD3D"/>
                  <mask id="mask0_14526_13536" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="4" y="4" width="18" height="18">
                  <rect x="4" y="4" width="18" height="18" fill="#D9D9D9"/>
                  </mask>
                  <g mask="url(#mask0_14526_13536)">
                  <path d="M12.9997 16.516L10.082 18.2755C9.97329 18.339 9.86436 18.3657 9.75523 18.3556C9.64611 18.3454 9.54736 18.3087 9.45898 18.2453C9.37048 18.1818 9.30217 18.0989 9.25404 17.9965C9.20604 17.8941 9.19836 17.7809 9.23098 17.6567L10.0055 14.3438L7.42967 12.1141C7.33342 12.0304 7.27211 11.9336 7.24573 11.8234C7.21923 11.7133 7.22617 11.6063 7.26654 11.5024C7.30692 11.3987 7.36511 11.3139 7.44111 11.248C7.51711 11.1821 7.62098 11.1391 7.75273 11.1188L11.1521 10.8218L12.4719 7.69337C12.5199 7.577 12.5927 7.49094 12.6904 7.43519C12.788 7.37944 12.8911 7.35156 12.9997 7.35156C13.1084 7.35156 13.2115 7.37944 13.3091 7.43519C13.4067 7.49094 13.4795 7.577 13.5275 7.69337L14.8474 10.8218L18.2467 11.1188C18.3785 11.1391 18.4824 11.1821 18.5584 11.248C18.6344 11.3139 18.6925 11.3987 18.7329 11.5024C18.7733 11.6063 18.7802 11.7133 18.7537 11.8234C18.7274 11.9336 18.666 12.0304 18.5698 12.1141L15.9939 14.3438L16.7685 17.6567C16.8011 17.7809 16.7934 17.8941 16.7454 17.9965C16.6973 18.0989 16.629 18.1818 16.5405 18.2453C16.4521 18.3087 16.3534 18.3454 16.2442 18.3556C16.1351 18.3657 16.0262 18.339 15.9174 18.2755L12.9997 16.516Z" fill="#FFBD3D"/>
                  </g>
                </svg>
              </div>          
            </div>  
            <div class="mark-complete" style="cursor: pointer;" (click)="markAsComplete(task)">
              <div *ngIf="!task.isTaskCompleted" tooltip="Mark as Complete">
                <svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <rect x="0.5" y="0.5" width="25" height="25" rx="5.5" stroke="#7D838B"/>
                  <mask id="mask0_14526_13544" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="4" y="4" width="18" height="18">
                  <rect x="4" y="4" width="18" height="18" fill="#D9D9D9"/>
                  </mask>
                  <g mask="url(#mask0_14526_13544)">
                  <path d="M11.1623 15.3625L17.5185 9.00625C17.6685 8.85625 17.8435 8.78125 18.0435 8.78125C18.2435 8.78125 18.4185 8.85625 18.5685 9.00625C18.7185 9.15625 18.7935 9.33438 18.7935 9.54063C18.7935 9.74688 18.7185 9.925 18.5685 10.075L11.6873 16.975C11.5373 17.125 11.3623 17.2 11.1623 17.2C10.9623 17.2 10.7873 17.125 10.6373 16.975L7.41229 13.75C7.26229 13.6 7.19041 13.4219 7.19666 13.2156C7.20291 13.0094 7.28104 12.8313 7.43104 12.6813C7.58104 12.5313 7.75916 12.4563 7.96541 12.4563C8.17166 12.4563 8.34979 12.5313 8.49979 12.6813L11.1623 15.3625Z" fill="#7D838B"/>
                  </g>
                </svg>    
              </div>
              <div *ngIf="task.isTaskCompleted" tooltip="Task Completed">
                <svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <rect x="0.5" y="0.5" width="25" height="25" rx="5.5" fill="#4DBD1715"/>
                  <rect x="0.5" y="0.5" width="25" height="25" rx="5.5" stroke="#4DBD17"/>
                  <mask id="mask0_14526_13544" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="4" y="4" width="18" height="18">
                  <rect x="4" y="4" width="18" height="18" fill="#4DBD17"/>
                  </mask>
                  <g mask="url(#mask0_14526_13544)">
                  <path d="M11.1623 15.3625L17.5185 9.00625C17.6685 8.85625 17.8435 8.78125 18.0435 8.78125C18.2435 8.78125 18.4185 8.85625 18.5685 9.00625C18.7185 9.15625 18.7935 9.33438 18.7935 9.54063C18.7935 9.74688 18.7185 9.925 18.5685 10.075L11.6873 16.975C11.5373 17.125 11.3623 17.2 11.1623 17.2C10.9623 17.2 10.7873 17.125 10.6373 16.975L7.41229 13.75C7.26229 13.6 7.19041 13.4219 7.19666 13.2156C7.20291 13.0094 7.28104 12.8313 7.43104 12.6813C7.58104 12.5313 7.75916 12.4563 7.96541 12.4563C8.17166 12.4563 8.34979 12.5313 8.49979 12.6813L11.1623 15.3625Z" fill="#4DBD17"/>
                  </g>
                  </svg>
              </div>
            </div>        
          </div>
          <div class="status-badge" *ngIf="isDataVisible('status_id')"
            [ngStyle]="{'background': (task.status_id | showMasterData : statusDetails : 'id' : 'label_color')}" cdkOverlayOrigin
            #overlayOrigin="cdkOverlayOrigin" (click)="statusDropdown($event,task)">
            <svg calss="status-arrow" width="16" height="16" viewBox="0 0 16 16" fill="none"
              xmlns="http://www.w3.org/2000/svg">
              <g clip-path="url(#clip0_14526_13551)">
                <path
                  d="M8.00001 8.78047L4.70001 5.48047L3.75734 6.42314L8.00001 10.6658L12.2427 6.42314L11.3 5.48047L8.00001 8.78047Z"
                  fill="white" />
              </g>
              <defs>
                <clipPath id="clip0_14526_13551">
                  <rect width="16" height="16" fill="white" transform="matrix(-1 0 0 1 16 0)" />
                </clipPath>
              </defs>
            </svg>
            <span>
              {{ task?.status_id ? (task.status_id | showMasterData : statusDetails : 'id' : 'name') : "-" }}</span>
          </div>
          <ng-template #statusdropdownTemplate let-task>
            <div class="dropdown" *ngIf="statusDetails?.length > 0">
              <ng-container *ngFor="let status of statusDetails">
                <div class="d-flex dropdown-item" 
                     [ngStyle]="{
                       'color': (status.id | showMasterData : statusDetails : 'id' : 'label_color')}"
                >
                  <div class="pr-1 small-circle"
                       [ngStyle]="{
                         'background-color': (status.id | showMasterData : statusDetails : 'id' : 'label_color')}"
                  ></div>
                  <div (click)="updateTaskStatus(status.id, task)">
                    {{ status.name }}
                  </div>
                </div>
              </ng-container>
            </div>
          </ng-template>
        </div>
      </div>
      <div class="col-12 header-task-details">
        <div class="details-info">
          <div *ngIf="isDataVisible('id')" class="detail-data">
            <div class="detail-title">
              {{getColumnName('id')}}
            </div>
            <div class="detail-value">
              {{task['id'] ? task['id'] : '-'}}
            </div>
          </div>
          <div *ngIf="isDataVisible('priority')" class="detail-data">
            <div class="detail-title">
              {{getColumnName('priority')}}
            </div>
            <div class="detail-value">
              <span class="sub-content-priority" [ngStyle]="{
              'background-color': task['priority'] ? 
                (task['priority'] | showMasterData : priorityDetails : 'id' : 'background_color') : '',
              'color': task['priority'] ? 
                (task['priority'] | showMasterData : priorityDetails : 'id' : 'text_color') : ''
            }">
                {{ task['priority'] ?
                (task['priority'] | showMasterData : priorityDetails : 'id' : 'name') : '-' }}
              </span>
            </div>
          </div>
          <div *ngIf="isDataVisible('assigned_to')" class="detail-data">
            <div class="detail-title">
              {{getColumnName('assigned_to')}}
            </div>
            <div class="detail-value" *ngIf="task['assigned_to'].length > 0">
              <app-people-icon-display [peopleList]="task['assigned_to']"
              [count]="task['assigned_to'].length" [bgColor]="button">
              </app-people-icon-display>
            </div>
            <div class="detail-value" *ngIf="task['assigned_to'].length == 0">
              -
            </div>
          </div>
          <div *ngIf="isDataVisible('display_start_date')" class="detail-data">
            <div class="detail-title">
              {{getColumnName('display_start_date')}}
            </div>
            <div class="detail-value">
              {{task['display_start_date'] ? task['display_start_date'] : '-'}}
            </div>
          </div>
          <div *ngIf="isDataVisible('display_end_date')" class="detail-data">
            <div class="detail-title">
              {{getColumnName('display_end_date')}}
            </div>
            <div class="detail-value">
              {{task['display_end_date'] ? task['display_end_date'] : '-'}}
            </div>
          </div>
          <div *ngIf="isDataVisible('display_actual_date')" class="detail-data">
            <div class="detail-title">
              {{getColumnName('display_actual_date')}}
            </div>
            <div class="detail-value">
              {{task['display_actual_date'] ? task['display_actual_date'] : '-'}}
            </div>
          </div>
          <div *ngIf="isDataVisible('display_actual_end_date')" class="detail-data">
            <div class="detail-title">
              {{getColumnName('display_actual_end_date')}}
            </div>
            <div class="detail-value">
              {{task['display_actual_end_date'] ? task['display_actual_end_date'] : '-'}}
            </div>
          </div>
          <div *ngIf="isDataVisible('estimated_hours')" class="detail-data">
            <div class="detail-title">
              {{getColumnName('estimated_hours')}}
            </div>
            <div class="detail-value">
              {{(task['estimated_hours'] | hoursToTime) || '0h 0m'}}
            </div>
          </div>
        </div>
        <div *ngIf="isDataVisible('completion_percentage')" class="d-flex pt-3">
        <div class="progress-container">
          <div class="progress-bar-container">
            <div class="progress-bar"
              [ngStyle]="{'width.%': task['completion_percentage'], 'background-color': getProgressBarColor(task['completion_percentage'])}">
            </div>
          </div>
          <div class="progress-label">{{task['completion_percentage'] ? task['completion_percentage'] +
            '%' : '0%' }}</div>
        </div>
      </div>
      </div>
    </div>
  </div>
</div>