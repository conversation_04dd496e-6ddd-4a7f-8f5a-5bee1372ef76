import {
  Component,
  OnInit,
  HostListener,
  ViewContainerRef,
  TemplateRef,
  ViewChild,
  ChangeDetectorRef
} from '@angular/core';
import * as _ from "underscore";
import * as moment from 'moment';
import { ActivatedRoute, Router } from "@angular/router";
import { Form<PERSON>uilder, FormControl, FormGroup } from "@angular/forms";
import { Overlay, OverlayConfig, OverlayRef } from "@angular/cdk/overlay";
import { TemplatePortal } from "@angular/cdk/portal";
import { LoginService } from "src/app/services/login/login.service";
import { PmMasterService } from "src/app/modules/project-management/services/pm-master.service";
import { PmAuthorizationService } from "src/app/modules/project-management/services/pm-authorization.service";
import { MyTaskService } from "./../../services/my-task.service";
import { ToasterMessageService } from 'src/app/modules/project-management/shared-lazy-loaded/components/toaster-message/toaster-message.service';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-my-task-detail-view',
  templateUrl: './my-task-detail-view.component.html',
  styleUrls: ['./my-task-detail-view.component.scss']
})
export class MyTaskDetailViewComponent implements OnInit {

  projectId: any;
  itemId: any;
  aid: any;
  formconfig: any;
  button: any;
  fontStyle: any;
  tabColor: any;
  fieldOutline: any;
  toggleButton: any;
  toggleButtonBg: any;
  isPageLoading: boolean = false;
  taskId: number;
  loadingGifUrl: any;
  isTaskEditAllowed: boolean;
  columns: any;
  statusDetails: any;
  priorityDetails: any;
  task: any;
  private overlayRef: OverlayRef | null;

  @ViewChild("statusdropdownTemplate") 
  statusdropdownTemplate!: TemplateRef<any>;
  isTaskDeleteAllowed: boolean = false;


  constructor(
    private router: Router,
    private _fb: FormBuilder,
    private overlay: Overlay,
    private viewContainerRef: ViewContainerRef,
    private cdr: ChangeDetectorRef,
    private route: ActivatedRoute,
    private _loginService: LoginService,
    private toasterService: ToasterMessageService,
    private pmMasterService: PmMasterService,
    private authService: PmAuthorizationService,
    private myTaskService: MyTaskService
  ) { }

  async ngOnInit() {
    this.isPageLoading = true;
    this.itemId = parseInt(this.router.url.split("/")[5]);
    this.projectId = parseInt(this.router.url.split("/")[3]);
    this.aid = await this._loginService.getProfile().profile.aid;
    this.calculateDynamicStyle();
    this.route.paramMap.subscribe((params) => {
      this.taskId = +params.get("task_id");
    });
    await this.pmMasterService.getPMFormCustomizeConfigV().then((res: any) => {
      if (res) {
        this.formconfig = res;
      }
    });
    const primaLoading = _.findWhere(this.formconfig, {
      type: "my-task-list-view",
      field_name: "loading-gif",
      is_active: true,
    });
    this.loadingGifUrl = primaLoading ? primaLoading["LOADING-GIF"] : null;
    this.isTaskEditAllowed = await this.authService.getProjectWiseObjectAccess(this.projectId, this.itemId, 304)
    this.isTaskDeleteAllowed = await this.authService.getProjectWiseObjectAccess(this.projectId, this.itemId, 310)
    const retrieveStyles = _.where(this.formconfig, {
      type: "project-theme",
      field_name: "styles",
      is_active: true,
    });
    this.button =
      retrieveStyles.length > 0
        ? retrieveStyles[0].data.button_color
          ? retrieveStyles[0].data.button_color
          : "#90ee90"
        : "#90ee90";
    document.documentElement.style.setProperty("--myTaskButton", this.button);

    this.fontStyle =
      retrieveStyles.length > 0
        ? retrieveStyles[0].data.font_style
          ? retrieveStyles[0].data.font_style
          : "Roboto"
        : "Roboto";
    document.documentElement.style.setProperty("--myTaskFont", this.fontStyle);

    this.tabColor =
      retrieveStyles.length > 0
        ? retrieveStyles[0].data.tab_color
          ? retrieveStyles[0].data.tab_color
          : ""
        : "";
    document.documentElement.style.setProperty(
      "--myTaskTabColor",
      this.tabColor
    );

    this.fieldOutline =
      retrieveStyles.length > 0
        ? retrieveStyles[0].data.field_outline_color
          ? retrieveStyles[0].data.field_outline_color
          : ""
        : "";
    document.documentElement.style.setProperty(
      "--myTaskFieldOutline",
      this.fieldOutline
    );

    this.toggleButtonBg =
      retrieveStyles.length > 0
        ? retrieveStyles[0].data.shades_color
          ? retrieveStyles[0].data.shades_color
          : ""
        : "";
    document.documentElement.style.setProperty(
      "--myTasktoggleButtonBg",
      this.toggleButtonBg
    );

    this.toggleButton =
      retrieveStyles.length > 0
        ? retrieveStyles[0].data.toggle_color
          ? retrieveStyles[0].data.toggle_color
          : ""
        : "";
    document.documentElement.style.setProperty(
      "--myTasktoggleButton",
      this.toggleButton
    );



    const columnsConfig = _.where(this.formconfig, { type: "my-task-landing-list", field_name: "style_config", is_active: true });
    this.columns = columnsConfig[0]?.columnConfig ? columnsConfig[0]?.columnConfig : null;

    await this.pmMasterService.getProjectActivityStatus().then((res: any) => {
      if (res['messType'] === 'S' && res['data'] && res['data'].length > 0) {
        this.statusDetails = res['data'];
      } else if (res['messType'] === 'E') {
        this.toasterService.showError(res['messText'] ? res['messText'] : 'Error while retrieving status details');
      }
    });

    await this.pmMasterService.getProjectActivityPriority().then((res: any) => {
      if (res['messType'] === 'S' && res['data'] && res['data'].length > 0) {
        this.priorityDetails = res['data'];
      } else if (res['messType'] === 'E') {
        this.toasterService.showError(res['messText'] ? res['messText'] : 'Error while retrieving status details');
      }
    });

    await this.getTaskdetails(this.taskId)
    this.task.isTaskCompleted = this.task.status_id && this.task.status_id == 5 ? true : false;
    this.isPageLoading = false;

  }

  calculateDynamicStyle() {
    let myTaskHeight = window.innerHeight - 148 + "px";
    document.documentElement.style.setProperty("--myTaskHeight", myTaskHeight);
    let myTaskInnerHeight = window.innerHeight - 254 + "px";
    document.documentElement.style.setProperty(
      "--myTaskInnerHeight",
      myTaskInnerHeight
    );
    let myTaskTimeEntiresHeight = window.innerHeight - 371 + "px";
    document.documentElement.style.setProperty(
      "--myTaskTimeEntriesHeight",
      myTaskTimeEntiresHeight
    );
  }

  @HostListener("window:resize")
  onResize() {
    this.calculateDynamicStyle();
  }

  async getTaskdetails(taskId: number){
    await this.myTaskService.getMyTaskDetails(this.projectId, this.itemId, this.taskId).then((res: any) => {
      if (res['messType'] == "S" && res['data'].length > 0) {
        this.task = res['data'][0]
        this.task.isFavTask = this.task.isFavTask ? this.task.isFavTask : false;
        this.task.isTaskCompleted = this.task.isTaskCompleted ? this.task.isTaskCompleted : false;
      } else if (res['messType'] === 'E') {
        this.toasterService.showError(res['messText'] ? res['messText'] : 'Error while retrieving task details');
      }
    })
  }

  routeBacktoList() {
    this.router.navigate(["list"], { relativeTo: this.route.parent });
  }

  navigateToEdit(taskId: number) {
    this.router.navigate(["edit", taskId], { relativeTo: this.route.parent });
  }

  isDataVisible(key: any): boolean {
    const group = this.columns.find(column => column.column_value_key === key);
    return group ? group.isActive : false; 
  }

  getColumnName(key: any){
    const group = this.columns.find(column => column.column_value_key === key);
    return group ? group.column_name : '-'; 
  }

  getProgressBarColor(value: number): string {
    if (value === 0) {
      return "#7D838B";
    } else if (value >= 1 && value <= 50) {
      return "#FF3A46";
    } else if (value >= 51 && value <= 99) {
      return "#FFBD3D";
    } else if (value === 100) {
      return "#4DBD17";
    } else {
      return "";
    }
  }

  statusDropdown(event: MouseEvent, task: any) { 
    event.stopPropagation();
    
    // Close the overlay if it is already open
    if (this.overlayRef) {
      this.closeOverlay();
    } else {
      const target = event.currentTarget as HTMLElement;
      
      // Create overlay config
      const config = new OverlayConfig({
        hasBackdrop: true,
        backdropClass: "cdk-overlay-transparent-backdrop",
        positionStrategy: this.overlay
          .position()
          .flexibleConnectedTo(target)
          .withPositions([
            {
              originX: "end",
              originY: "bottom",
              overlayX: "end",
              overlayY: "top",
            },
          ]),
      });
  
      // Create overlay reference
      this.overlayRef = this.overlay.create(config);
      
      // Attach the template to the overlay
      this.overlayRef.attach(
        new TemplatePortal(this.statusdropdownTemplate, this.viewContainerRef, {
          $implicit: task // Pass the task as context to the template
        })
      );
  
      // Close overlay on backdrop click
      this.overlayRef.backdropClick().subscribe(() => this.closeOverlay());
    }
  }

  closeOverlay() {
    if (this.overlayRef) {
      this.overlayRef.dispose();
      this.overlayRef = null;
    }
  }

  async updateTaskStatus(statusId, task){
    await this.myTaskService.updateinlineTaskStatus(this.projectId, this.itemId, task.id, statusId,task.actual_start_date,task.actual_end_date).then((res: any) => {
      if (res['messType'] == "S"){
        task.status_id = statusId;
        task.isTaskCompleted = task.status_id && task.status_id == 5 ? true : false;
        if (task.status_id == 7) {
          task.actual_start_date = null;
          task.actual_end_date = null;
          task.display_actual_end_date = null;
          task.display_actual_date = null
        }
        if (task.status_id == 5) {
          if (task.actual_start_date == null || task.actual_start_date == undefined || task.actual_start_date == 'null' || task.actual_start_date == '' || task.actual_start_date == "Invalid date") {
            task.actual_start_date = moment().format("YYYY-MM-DD")
            task.display_actual_date = moment().format("DD-MMM-YYYY");
          }
          task.actual_end_date = moment().format("YYYY-MM-DD")
           task.display_actual_end_date=moment().format("DD-MMM-YYYY");
        }
        if (task.status_id == 4) {
          task.actual_start_date = moment().format("YYYY-MM-DD");
          task.display_actual_date=moment().format("DD-MMM-YYYY");
          task.actual_end_date = null
          task.display_actual_end_date=null;
        }
        this.toasterService.showSuccess(res['messText'],10000)
      }else{
        this.toasterService.showError(res['messText'] ? res['messText'] : 'Error while updating task status');
      }
    })
    this.closeOverlay();
  }

  async markAsFavourite(task){
    if(task.isFavTask){
      await this.myTaskService.removeTaskFromFavourite(this.projectId, this.itemId, [task.id]).then((res: any) => {
        if (res['messType'] == "S"){
          task.isFavTask = false;
          this.toasterService.showSuccess(res['messText'],10000)
        }else{
          this.toasterService.showError(res['messText'] ? res['messText'] : 'Error while updating task');
        }
      })
    }else{
      await this.myTaskService.markTaskAsFavourite(this.projectId, this.itemId, [task.id]).then((res: any) => {
        if (res['messType'] == "S"){
          task.isFavTask = true;
          this.toasterService.showSuccess(res['messText'],10000)
        }else{
          this.toasterService.showError(res['messText'] ? res['messText'] : 'Error while updating task');
        }
      })
    }
  }

  async markAsComplete(task){
    if(task.isTaskCompleted){
      this.toasterService.showWarning("Task has been already completed", 10000);
    }else{
      await this.myTaskService.updateinlineTaskStatus(this.projectId, this.itemId, task.id, 5,task.actual_start_date,task.actual_end_date).then((res: any) => {
        if (res['messType'] == "S"){
          task.status_id = 5;
          task.isTaskCompleted = task.status_id && task.status_id == 5 ? true : false;
          if (task.status_id == 5) {
            if (task.actual_start_date == null || task.actual_start_date == undefined || task.actual_start_date == 'null' || task.actual_start_date == '' || task.actual_start_date == "Invalid date") {
              task.actual_start_date = moment().format("YYYY-MM-DD")
              task.display_actual_date = moment().format("DD-MMM-YYYY");
            }
            task.actual_end_date = moment().format("YYYY-MM-DD")
             task.display_actual_end_date=moment().format("DD-MMM-YYYY");
          }
          this.toasterService.showSuccess(res['messText'],10000)
        }else{
          this.toasterService.showError(res['messText'] ? res['messText'] : 'Error while updating task status');
        }
      })
    }
  }

  deleteTask(taskId){
    Swal.fire({
      text:"Are you sure you want to delete the task? Click 'OK' to proceed.",
      showCancelButton: true,
      confirmButtonText: 'OK',
      cancelButtonText: 'Cancel',
      allowOutsideClick: false,
      allowEscapeKey: false, 
      icon: 'warning'
    }).then(async (result) => {
      if (result.isConfirmed) {
        await this.myTaskService.deleteTaskDetails(this.projectId, this.itemId, taskId).then(async (res: any) => {
          if (res['messType'] == "S"){
            this.toasterService.showSuccess(res['messText'],10000);
            await new Promise(resolve => setTimeout(resolve, 500));
            this.router.navigate(["list"], { relativeTo: this.route.parent });
          }else{
            this.toasterService.showError(res['messText'] ? res['messText'] : 'Error while updating task status');
          }
        })
      }
      else if (result.dismiss === Swal.DismissReason.cancel) {
        
      }
    })
  }

}
