<div class="task-edit-view">
  <div *ngIf="isPageLoading" class="loading-img">
    <div class="load-img">
      <img *ngIf="loadingGifUrl" [src]="loadingGifUrl" />
    </div>
    <div class="loading-wrapper">
      <div class="loading">Loading...</div>
    </div>
  </div>
  <div *ngIf="!isPageLoading">
    <div class="task-edit-header">
      <button (click)="routeBackToDetail()" class="back-btn">
        <svg xmlns="http://www.w3.org/2000/svg" height="18" viewBox="0 0 24 24" width="18">
          <path d="M0 0h24v24H0z" fill="none" />
          <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z" />
        </svg>
      </button>

      <span class="edit-title">
        {{header_name}}
      </span>
      <!-- <button class="open-button">
        <span class="open-text">Save And Open</span>
      </button> -->

      <button class="save-button" (click)="saveTaskDetails()">
        <span class="save-text">Save</span>
      </button>

    </div>
    <div class="task-edit-body">
      <form [formGroup]="taskForm" class="task-edit-details container">
        <div class="row">
          <div class="pt-2 col-12" style="padding: unset;">
            <div *ngIf="('task-name' | checkActive : this.formconfig: 'my-task-edit')">
              <div class="d-flex content-title">
                {{('task-name' | checkLabel : this.formconfig: 'my-task-edit': 'Task Title')}}
                <span style="color: #EC5F6E;"
                *ngIf="('task-name' | checkMandatedField : this.formconfig: 'my-task-edit')">*</span>
              </div>
              <div class="date-field" style="width: 100% !important; padding: 5px !important;">
                <div class="task-name-field">
                  <input matInput [placeholder]="'Enter here...'" formControlName="taskName"
                    [required]="('task-name' | checkMandatedField : this.formconfig: 'my-task-edit')" />
                  <div (click)="clearTaskName()" style="cursor: pointer;" matTooltip="Clear">
                    <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path
                        d="M6.98636 12.1841L9.46341 9.72099L11.9405 12.1841L12.6655 11.4632L10.1885 9L12.6655 6.53684L11.9405 5.81586L9.46341 8.27901L6.98636 5.81586L6.26131 6.53684L8.73836 9L6.26131 11.4632L6.98636 12.1841ZM9.46462 15.5C8.56049 15.5 7.71067 15.3294 6.91515 14.9882C6.11963 14.647 5.42766 14.184 4.83925 13.5991C4.25083 13.0142 3.78495 12.3264 3.44161 11.5357C3.09837 10.745 2.92676 9.90014 2.92676 9.0012C2.92676 8.10214 3.09832 7.25709 3.44143 6.46603C3.78455 5.67496 4.2502 4.98688 4.83839 4.40176C5.42657 3.81665 6.11825 3.35338 6.91343 3.01196C7.70861 2.67065 8.5582 2.5 9.46221 2.5C10.3663 2.5 11.2162 2.6706 12.0117 3.01179C12.8072 3.35298 13.4992 3.81602 14.0876 4.40091C14.676 4.98579 15.1419 5.6736 15.4852 6.46432C15.8285 7.25504 16.0001 8.09986 16.0001 8.9988C16.0001 9.89786 15.8285 10.7429 15.4854 11.534C15.1423 12.325 14.6766 13.0131 14.0884 13.5982C13.5003 14.1834 12.8086 14.6466 12.0134 14.988C11.2182 15.3293 10.3686 15.5 9.46462 15.5ZM9.46341 14.4737C11.0001 14.4737 12.3017 13.9434 13.3682 12.8829C14.4347 11.8224 14.968 10.5281 14.968 9C14.968 7.47193 14.4347 6.17763 13.3682 5.11711C12.3017 4.05658 11.0001 3.52632 9.46341 3.52632C7.92672 3.52632 6.62513 4.05658 5.55862 5.11711C4.49211 6.17763 3.95886 7.47193 3.95886 9C3.95886 10.5281 4.49211 11.8224 5.55862 12.8829C6.62513 13.9434 7.92672 14.4737 9.46341 14.4737Z"
                        fill="#7D838B" />
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="pt-2 row" *ngIf="('status' | checkActive : this.formconfig: 'my-task-edit') && statusEditAllowed">
          <div class="d-flex" style="cursor: pointer;" cdkOverlayOrigin #overlayOrigin="cdkOverlayOrigin"
            (click)="statusDropdown($event,task)">
            <div class="d-flex" [ngStyle]="{
                      'background': (task.status_id | showMasterData : statusDetails : 'id' : 'label_color'),
                      'color': '#FFFFFF',
                      'padding': '5px 10px 5px 5px',
                      'border-radius': '4px 0px 0px 4px'
                    }">
              <div style="font-size: 10px;">
                {{ task.status_id ?
                (task.status_id | showMasterData : statusDetails : 'id' : 'name') : 'Unassigned' }}
              </div>
              <div style="margin: 0px 0px 0px 4px;">
                <svg calss="status-arrow" width="16" height="16" viewBox="0 0 16 16" fill="none"
                  xmlns="http://www.w3.org/2000/svg">
                  <g clip-path="url(#clip0_14526_13551)">
                    <path
                      d="M8.00001 8.78047L4.70001 5.48047L3.75734 6.42314L8.00001 10.6658L12.2427 6.42314L11.3 5.48047L8.00001 8.78047Z"
                      fill="white" />
                  </g>
                  <defs>
                    <clipPath id="clip0_14526_13551">
                      <rect width="16" height="16" fill="white" transform="matrix(-1 0 0 1 16 0)" />
                    </clipPath>
                  </defs>
                </svg>
              </div>
            </div>
            <div [ngStyle]="{
                      'background': (task.status_id | showMasterData : statusDetails : 'id' : 'label_color'),
                      'border-top': '15px solid transparent',
                      'border-bottom': '15px solid transparent',
                      'border-right': '12px solid white',
                      'border-radius': '0px 4px 4px 0px'
                    }">
            </div>
          </div>
          <ng-template #statusdropdownTemplate let-task>
            <div class="dropdown" *ngIf="statusDetails?.length > 0">
              <ng-container *ngFor="let status of statusDetails">
                <div class="d-flex dropdown-item" [ngStyle]="{
                               'color': (status.id | showMasterData : statusDetails : 'id' : 'label_color')}">
                  <div class="pr-1 small-circle"
                    [ngStyle]="{
                                 'background-color': (status.id | showMasterData : statusDetails : 'id' : 'label_color')}"></div>
                  <div (click)="updateTaskStatus(status.id, task)">
                    {{ status.name }}
                  </div>
                </div>
              </ng-container>
            </div>
          </ng-template>
        </div>
        <div class="row">
          <div class="pt-2 col-3" style="padding: unset;"
            *ngIf="('start-date' | checkActive : formconfig : 'my-task-edit') && taskDurationEditAllowed">
            <div cdkOverlayOrigin #overlayOrigin="cdkOverlayOrigin"
              (click)="inlineDatePicker($event,task,'start_date')">
              <div class="d-flex content-title">
                {{('start-date' | checkLabel : this.formconfig: 'my-task-edit': 'Start Date')}}
                <span style="color: #EC5F6E;"
                *ngIf="('start-date' | checkMandatedField : this.formconfig: 'my-task-edit')">*</span>
              </div>
              <div class="date-field" style="cursor: pointer;">
                <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <mask id="mask0_14526_13884" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="17"
                    height="17">
                    <rect width="17" height="17" fill="#D9D9D9" />
                  </mask>
                  <g mask="url(#mask0_14526_13884)">
                    <path
                      d="M10.6452 15.2294V14.1669H13.2402C13.2947 14.1669 13.3447 14.1442 13.39 14.0987C13.4354 14.0534 13.4582 14.0035 13.4582 13.9489V7.30158H3.5415V10.094H2.479V4.46824C2.479 4.11042 2.60296 3.80755 2.85088 3.55963C3.0988 3.31171 3.40167 3.18775 3.75949 3.18775H4.74018V1.68945H5.82995V3.18775H11.197V1.68945H12.2595V3.18775H13.2402C13.598 3.18775 13.9009 3.31171 14.1488 3.55963C14.3967 3.80755 14.5207 4.11042 14.5207 4.46824V13.9489C14.5207 14.3068 14.3967 14.6096 14.1488 14.8575C13.9009 15.1055 13.598 15.2294 13.2402 15.2294H10.6452ZM5.6665 16.7483L4.92683 16.0086L6.92114 13.9898H0.885254V12.9273H6.92114L4.92683 10.9086L5.6665 10.1689L8.95618 13.4586L5.6665 16.7483ZM3.5415 6.23908H13.4582V4.46824C13.4582 4.4137 13.4354 4.36377 13.39 4.31843C13.3447 4.27298 13.2947 4.25026 13.2402 4.25026H3.75949C3.70495 4.25026 3.65501 4.27298 3.60968 4.31843C3.56423 4.36377 3.5415 4.4137 3.5415 4.46824V6.23908Z"
                      fill="#6E7B8F" />
                  </g>
                </svg>
                <div class="date">
                  {{task.display_start_date}}
                </div>
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <mask id="mask0_14526_13888" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="16"
                    height="16">
                    <rect width="16" height="16" fill="#D9D9D9" />
                  </mask>
                  <g mask="url(#mask0_14526_13888)">
                    <path
                      d="M7.68001 9.44998L5.57917 7.34932C5.55028 7.32032 5.52751 7.28793 5.51084 7.25215C5.49417 7.21648 5.48584 7.17821 5.48584 7.13732C5.48584 7.05565 5.51345 6.98465 5.56867 6.92432C5.6239 6.8641 5.69667 6.83398 5.78701 6.83398H10.2127C10.303 6.83398 10.3758 6.86443 10.431 6.92532C10.4862 6.9861 10.5138 7.05704 10.5138 7.13815C10.5138 7.15848 10.4827 7.22887 10.4203 7.34932L8.31967 9.44998C8.27145 9.49832 8.22156 9.5336 8.17001 9.55582C8.11845 9.57804 8.06173 9.58915 7.99984 9.58915C7.93795 9.58915 7.88123 9.57804 7.82967 9.55582C7.77812 9.5336 7.72823 9.49832 7.68001 9.44998Z"
                      fill="#45546E" />
                  </g>
                </svg>
              </div>
            </div>
          </div>
          <div class="pt-2 col-3" style="padding: unset;"
            *ngIf="('due-date' | checkActive : formconfig : 'my-task-edit') && taskDurationEditAllowed">
            <div cdkOverlayOrigin #overlayOrigin="cdkOverlayOrigin" (click)="inlineDatePicker($event,task,'end_date')">
              <div class="d-flex content-title">
                {{('due-date' | checkLabel : this.formconfig: 'my-task-edit': 'End Date')}}
                <span style="color: #EC5F6E;"
                *ngIf="('due-date' | checkMandatedField : this.formconfig: 'my-task-edit')">*</span>
              </div>
              <div class="date-field" style="cursor: pointer;">
                <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <mask id="mask0_14526_13895" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="17"
                    height="17">
                    <rect width="17" height="17" fill="#D9D9D9" />
                  </mask>
                  <g mask="url(#mask0_14526_13895)">
                    <path
                      d="M3.5415 6.23908H13.4582V4.46824C13.4582 4.4137 13.4354 4.36376 13.39 4.31843C13.3447 4.27298 13.2947 4.25025 13.2402 4.25025H3.75949C3.70495 4.25025 3.65501 4.27298 3.60968 4.31843C3.56423 4.36376 3.5415 4.4137 3.5415 4.46824V6.23908ZM3.75949 15.2294C3.40167 15.2294 3.0988 15.1055 2.85088 14.8575C2.60296 14.6096 2.479 14.3068 2.479 13.9489V4.46824C2.479 4.11042 2.60296 3.80755 2.85088 3.55963C3.0988 3.31171 3.40167 3.18775 3.75949 3.18775H4.74018V1.68945H5.82995V3.18775H11.197V1.68945H12.2595V3.18775H13.2402C13.598 3.18775 13.9009 3.31171 14.1488 3.55963C14.3967 3.80755 14.5207 4.11042 14.5207 4.46824V8.33822C14.3508 8.26373 14.1773 8.20352 14.0002 8.1576C13.8231 8.11179 13.6425 8.07573 13.4582 8.0494V7.30158H3.5415V13.9489C3.5415 14.0035 3.56423 14.0534 3.60968 14.0987C3.65501 14.1442 3.70495 14.1669 3.75949 14.1669H8.36508C8.42493 14.363 8.49736 14.5485 8.58236 14.7233C8.66724 14.8982 8.76369 15.0669 8.87171 15.2294H3.75949ZM12.886 15.9378C12.0015 15.9378 11.2492 15.6276 10.6289 15.0074C10.0086 14.3871 9.69851 13.6347 9.69851 12.7503C9.69851 11.8658 10.0086 11.1134 10.6289 10.4932C11.2492 9.87289 12.0015 9.56275 12.886 9.56275C13.7706 9.56275 14.523 9.87289 15.1431 10.4932C15.7634 11.1134 16.0735 11.8658 16.0735 12.7503C16.0735 13.6347 15.7634 14.3871 15.1431 15.0074C14.523 15.6276 13.7706 15.9378 12.886 15.9378ZM14.0657 14.3713L14.507 13.93L13.1995 12.6222V10.6662H12.5728V12.8783L14.0657 14.3713Z"
                      fill="#6E7B8F" />
                  </g>
                </svg>
                <div class="date">
                  {{task.display_end_date}}
                </div>
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <mask id="mask0_14526_13888" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="16"
                    height="16">
                    <rect width="16" height="16" fill="#D9D9D9" />
                  </mask>
                  <g mask="url(#mask0_14526_13888)">
                    <path
                      d="M7.68001 9.44998L5.57917 7.34932C5.55028 7.32032 5.52751 7.28793 5.51084 7.25215C5.49417 7.21648 5.48584 7.17821 5.48584 7.13732C5.48584 7.05565 5.51345 6.98465 5.56867 6.92432C5.6239 6.8641 5.69667 6.83398 5.78701 6.83398H10.2127C10.303 6.83398 10.3758 6.86443 10.431 6.92532C10.4862 6.9861 10.5138 7.05704 10.5138 7.13815C10.5138 7.15848 10.4827 7.22887 10.4203 7.34932L8.31967 9.44998C8.27145 9.49832 8.22156 9.5336 8.17001 9.55582C8.11845 9.57804 8.06173 9.58915 7.99984 9.58915C7.93795 9.58915 7.88123 9.57804 7.82967 9.55582C7.77812 9.5336 7.72823 9.49832 7.68001 9.44998Z"
                      fill="#45546E" />
                  </g>
                </svg>
              </div>
            </div>
          </div>
          <ng-template #inlineDatePickerTemplate let-task let-column="column">
            <div class="calendar-overlay d-flex flex-column">
              <div class="content-text">please select the date that you wish to modify</div>

              <mat-calendar [(selected)]="task[column]" [minDate]="minDate" [maxDate]="maxDate"></mat-calendar>


              <div class="d-flex justify-content-end" style="gap: 8px; margin-top: 6px">
                <div class="cancel-btn" (click)="closeOverlay()"
                  [ngStyle]="{ 'pointer-events': isApiInProgress ? 'none' : '' }">
                  Cancel
                </div>
                <div class="yes-btn" (click)="updateInlineDate(task,task[column], column)"
                  [ngStyle]="{ 'pointer-events': isApiInProgress ? 'none' : '' }">
                  <ng-container *ngIf="!isApiInProgress; else loading">
                    Update
                  </ng-container>
                  <ng-template #loading>
                    <mat-spinner class="white-spinner" diameter="20"></mat-spinner>
                  </ng-template>
                </div>
              </div>
            </div>
          </ng-template>
          <div class="pt-2 col-3" style="padding: unset;">
            <div *ngIf="('estimated-hours' | checkActive : formconfig : 'my-task-edit')">
              <div class="d-flex content-title">
                {{('estimated-hours' | checkLabel : this.formconfig: 'my-task-edit': 'Estimated Hours')}}
                <span style="color: #EC5F6E;"
                *ngIf="('estimated-hours' | checkMandatedField : this.formconfig: 'my-task-edit')">*</span>
              </div>
              <div class="date-field">
                <input matInput [placeholder]="'Enter hours...'" formControlName="estimatedHours"
                  [required]="('estimated-hours' | checkMandatedField : this.formconfig: 'my-task-edit')" />
              </div>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="pt-3 col-3" style="padding: unset;">
            <div *ngIf="('completed-percentage' | checkActive : formconfig : 'my-task-edit')">
              <div class="d-flex content-title">
                {{('completed-percentage' | checkLabel : this.formconfig: 'my-task-edit': 'Completed Percentage')}}
                <span style="color: #EC5F6E;"
                *ngIf="('completed-percentage' | checkMandatedField : this.formconfig: 'my-task-edit')">*</span>
              </div>
              <div class="date-field">
                <input type="number"
                  matInput [placeholder]="'Enter..'" formControlName="completedPercentage"
                  [required]="('completed-percentage' | checkMandatedField : this.formconfig: 'my-task-edit')" 
                  min="0" max="100"/>
              </div>
            </div>
          </div>
          <div class="pt-3 col-3" style="padding: unset;" *ngIf="('sheet_number' | checkActive : formconfig : 'my-task-edit')">
            <div class="d-flex content-title">
              {{('sheet_number' | checkLabel : this.formconfig: 'my-task-edit': 'Sheet Number')}}
              <span style="color: #EC5F6E;"
              *ngIf="('sheet_number' | checkMandatedField : this.formconfig: 'my-task-edit')">*</span>
            </div>
            <div class="date-field">
              <input matInput 
       [placeholder]="'Enter Sheet Number...'" 
       formControlName="sheetNumber"
       [required]="('sheet_number' | checkMandatedField : formconfig : 'my-task-edit')"
       [attr.maxLength]="sheet_number_length ? sheet_number_length : 16"
       />

            </div>
          </div>
          <div class="pt-3 col-3" style="padding: unset;" *ngIf="('software_type' | checkActive : formconfig : 'my-task-edit')">
            <div class="d-flex content-title">
              {{('software_type' | checkLabel : this.formconfig: 'my-task-edit': 'Sheet Number')}}
              <span style="color: #EC5F6E;"
              *ngIf="('software_type' | checkMandatedField : this.formconfig: 'my-task-edit')">*</span>
            </div>
            <div class="date-field">
              <input matInput [placeholder]="'Enter Software Type...'" formControlName="softwareType"
                [required]="('software_type' | checkMandatedField : this.formconfig: 'my-task-edit')" />
            </div>
          </div>
          <div class="pt-3 col-3" style="padding: unset;">
            <div *ngIf="('weighted-percentage' | checkActive : formconfig : 'my-task-edit')">
              <div class="d-flex content-title">
                {{('weighted-percentage' | checkLabel : this.formconfig: 'my-task-edit': 'weighted Percentage')}}
                <span style="color: #EC5F6E;"
                *ngIf="('weighted-percentage' | checkMandatedField : this.formconfig: 'my-task-edit')">*</span>
              </div>
              <div class="date-field">
                <input type="number"
                 matInput [placeholder]="'Enter..'" formControlName="weightedPercentage"
                  [required]="('weighted-percentage' | checkMandatedField : this.formconfig: 'my-task-edit')" 
                  min="0" max="100" />
              </div>
            </div>
          </div>
          
        </div>
        <div class="row">
          <div class="pt-2 col-3" style="padding: unset;"
            *ngIf="('actual_start_date' | checkActive : formconfig : 'my-task-edit') && taskDurationEditAllowed">
            <div cdkOverlayOrigin #overlayOrigin="cdkOverlayOrigin"
              (click)="inlineDatePicker($event,task,'actual_start_date')">
              <div class="d-flex content-title">
                {{('actual_start_date' | checkLabel : this.formconfig: 'my-task-edit': 'Actual Start Date')}}
                <span style="color: #EC5F6E;"
                *ngIf="('actual_start_date' | checkMandatedField : this.formconfig: 'my-task-edit')">*</span>
              </div>
              <div class="date-field" style="cursor: pointer;">
                <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <mask id="mask0_14526_13884" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="17"
                    height="17">
                    <rect width="17" height="17" fill="#D9D9D9" />
                  </mask>
                  <g mask="url(#mask0_14526_13884)">
                    <path
                      d="M10.6452 15.2294V14.1669H13.2402C13.2947 14.1669 13.3447 14.1442 13.39 14.0987C13.4354 14.0534 13.4582 14.0035 13.4582 13.9489V7.30158H3.5415V10.094H2.479V4.46824C2.479 4.11042 2.60296 3.80755 2.85088 3.55963C3.0988 3.31171 3.40167 3.18775 3.75949 3.18775H4.74018V1.68945H5.82995V3.18775H11.197V1.68945H12.2595V3.18775H13.2402C13.598 3.18775 13.9009 3.31171 14.1488 3.55963C14.3967 3.80755 14.5207 4.11042 14.5207 4.46824V13.9489C14.5207 14.3068 14.3967 14.6096 14.1488 14.8575C13.9009 15.1055 13.598 15.2294 13.2402 15.2294H10.6452ZM5.6665 16.7483L4.92683 16.0086L6.92114 13.9898H0.885254V12.9273H6.92114L4.92683 10.9086L5.6665 10.1689L8.95618 13.4586L5.6665 16.7483ZM3.5415 6.23908H13.4582V4.46824C13.4582 4.4137 13.4354 4.36377 13.39 4.31843C13.3447 4.27298 13.2947 4.25026 13.2402 4.25026H3.75949C3.70495 4.25026 3.65501 4.27298 3.60968 4.31843C3.56423 4.36377 3.5415 4.4137 3.5415 4.46824V6.23908Z"
                      fill="#6E7B8F" />
                  </g>
                </svg>
                <div class="date">
                  {{task.display_actual_date}}
                </div>
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <mask id="mask0_14526_13888" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="16"
                    height="16">
                    <rect width="16" height="16" fill="#D9D9D9" />
                  </mask>
                  <g mask="url(#mask0_14526_13888)">
                    <path
                      d="M7.68001 9.44998L5.57917 7.34932C5.55028 7.32032 5.52751 7.28793 5.51084 7.25215C5.49417 7.21648 5.48584 7.17821 5.48584 7.13732C5.48584 7.05565 5.51345 6.98465 5.56867 6.92432C5.6239 6.8641 5.69667 6.83398 5.78701 6.83398H10.2127C10.303 6.83398 10.3758 6.86443 10.431 6.92532C10.4862 6.9861 10.5138 7.05704 10.5138 7.13815C10.5138 7.15848 10.4827 7.22887 10.4203 7.34932L8.31967 9.44998C8.27145 9.49832 8.22156 9.5336 8.17001 9.55582C8.11845 9.57804 8.06173 9.58915 7.99984 9.58915C7.93795 9.58915 7.88123 9.57804 7.82967 9.55582C7.77812 9.5336 7.72823 9.49832 7.68001 9.44998Z"
                      fill="#45546E" />
                  </g>
                </svg>
              </div>
            </div>
          </div>
          <div class="pt-2 col-3" style="padding: unset;"
            *ngIf="('actual_end_date' | checkActive : formconfig : 'my-task-edit') && taskDurationEditAllowed">
            <div cdkOverlayOrigin #overlayOrigin="cdkOverlayOrigin" (click)="inlineDatePicker($event,task,'actual_end_date')">
              <div class="d-flex content-title">
                {{('actual_end_date' | checkLabel : this.formconfig: 'my-task-edit': 'Actual End Date')}}
                <span style="color: #EC5F6E;"
                *ngIf="('actual_end_date' | checkMandatedField : this.formconfig: 'my-task-edit')">*</span>
              </div>
              <div class="date-field" style="cursor: pointer;">
                <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <mask id="mask0_14526_13895" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="17"
                    height="17">
                    <rect width="17" height="17" fill="#D9D9D9" />
                  </mask>
                  <g mask="url(#mask0_14526_13895)">
                    <path
                      d="M3.5415 6.23908H13.4582V4.46824C13.4582 4.4137 13.4354 4.36376 13.39 4.31843C13.3447 4.27298 13.2947 4.25025 13.2402 4.25025H3.75949C3.70495 4.25025 3.65501 4.27298 3.60968 4.31843C3.56423 4.36376 3.5415 4.4137 3.5415 4.46824V6.23908ZM3.75949 15.2294C3.40167 15.2294 3.0988 15.1055 2.85088 14.8575C2.60296 14.6096 2.479 14.3068 2.479 13.9489V4.46824C2.479 4.11042 2.60296 3.80755 2.85088 3.55963C3.0988 3.31171 3.40167 3.18775 3.75949 3.18775H4.74018V1.68945H5.82995V3.18775H11.197V1.68945H12.2595V3.18775H13.2402C13.598 3.18775 13.9009 3.31171 14.1488 3.55963C14.3967 3.80755 14.5207 4.11042 14.5207 4.46824V8.33822C14.3508 8.26373 14.1773 8.20352 14.0002 8.1576C13.8231 8.11179 13.6425 8.07573 13.4582 8.0494V7.30158H3.5415V13.9489C3.5415 14.0035 3.56423 14.0534 3.60968 14.0987C3.65501 14.1442 3.70495 14.1669 3.75949 14.1669H8.36508C8.42493 14.363 8.49736 14.5485 8.58236 14.7233C8.66724 14.8982 8.76369 15.0669 8.87171 15.2294H3.75949ZM12.886 15.9378C12.0015 15.9378 11.2492 15.6276 10.6289 15.0074C10.0086 14.3871 9.69851 13.6347 9.69851 12.7503C9.69851 11.8658 10.0086 11.1134 10.6289 10.4932C11.2492 9.87289 12.0015 9.56275 12.886 9.56275C13.7706 9.56275 14.523 9.87289 15.1431 10.4932C15.7634 11.1134 16.0735 11.8658 16.0735 12.7503C16.0735 13.6347 15.7634 14.3871 15.1431 15.0074C14.523 15.6276 13.7706 15.9378 12.886 15.9378ZM14.0657 14.3713L14.507 13.93L13.1995 12.6222V10.6662H12.5728V12.8783L14.0657 14.3713Z"
                      fill="#6E7B8F" />
                  </g>
                </svg>
                <div class="date">
                  {{task.display_actual_end_date}}
                </div>
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <mask id="mask0_14526_13888" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="16"
                    height="16">
                    <rect width="16" height="16" fill="#D9D9D9" />
                  </mask>
                  <g mask="url(#mask0_14526_13888)">
                    <path
                      d="M7.68001 9.44998L5.57917 7.34932C5.55028 7.32032 5.52751 7.28793 5.51084 7.25215C5.49417 7.21648 5.48584 7.17821 5.48584 7.13732C5.48584 7.05565 5.51345 6.98465 5.56867 6.92432C5.6239 6.8641 5.69667 6.83398 5.78701 6.83398H10.2127C10.303 6.83398 10.3758 6.86443 10.431 6.92532C10.4862 6.9861 10.5138 7.05704 10.5138 7.13815C10.5138 7.15848 10.4827 7.22887 10.4203 7.34932L8.31967 9.44998C8.27145 9.49832 8.22156 9.5336 8.17001 9.55582C8.11845 9.57804 8.06173 9.58915 7.99984 9.58915C7.93795 9.58915 7.88123 9.57804 7.82967 9.55582C7.77812 9.5336 7.72823 9.49832 7.68001 9.44998Z"
                      fill="#45546E" />
                  </g>
                </svg>
              </div>
            </div>
          </div>
          <ng-template #inlineDatePickerTemplate let-task let-column="column">
            <div class="calendar-overlay d-flex flex-column">
              <div class="content-text">please select the date that you wish to modify</div>

              <mat-calendar [(selected)]="task[column]" [minDate]="minDate" [maxDate]="maxDate"></mat-calendar>


              <div class="d-flex justify-content-end" style="gap: 8px; margin-top: 6px">
                <div class="cancel-btn" (click)="closeOverlay()"
                  [ngStyle]="{ 'pointer-events': isApiInProgress ? 'none' : '' }">
                  Cancel
                </div>
                <div class="yes-btn" (click)="updateInlineDate(task,task[column], column)"
                  [ngStyle]="{ 'pointer-events': isApiInProgress ? 'none' : '' }">
                  <ng-container *ngIf="!isApiInProgress; else loading">
                    Update
                  </ng-container>
                  <ng-template #loading>
                    <mat-spinner class="white-spinner" diameter="20"></mat-spinner>
                  </ng-template>
                </div>
              </div>
            </div>
          </ng-template>
        </div>
        <div class="pt-2 row">
          <div *ngIf="('priority' | checkActive : this.formconfig: 'my-task-edit') && priorityEditAllowed" style="cursor: pointer;" cdkOverlayOrigin #overlayOrigin="cdkOverlayOrigin"
          (click)="priorityDropdown($event,task)">
            <div class="d-flex content-title">
              {{('priority' | checkLabel : this.formconfig: 'my-task-edit': 'Priority')}}
              <span style="color: #EC5F6E;"
              *ngIf="('priority' | checkMandatedField : this.formconfig: 'my-task-edit')">*</span>
            </div>
            <div class="detail-value">
              <span class="sub-content-priority" [ngStyle]="{
              'background-color': task['priority'] ? 
                (task['priority'] | showMasterData : priorityDetails : 'id' : 'background_color') : '',
              'color': task['priority'] ? 
                (task['priority'] | showMasterData : priorityDetails : 'id' : 'text_color') : ''
            }">
                {{ task['priority'] ?
                (task['priority'] | showMasterData : priorityDetails : 'id' : 'name') : '-' }}
              </span>
            </div>
            <ng-template #prioritydropdownTemplate let-task>
              <div class="dropdown" *ngIf="priorityDetails.length > 0">
                <ng-container *ngFor="let priority of priorityDetails">
                  <div class="d-flex dropdown-item" [ngStyle]="{
                         'color': (priority.id | showMasterData : priorityDetails : 'id' : 'text_color')}">
                    <div class="pr-1 small-circle"
                      [ngStyle]="{
                           'background-color': (priority.id | showMasterData : priorityDetails : 'id' : 'background_color')}"></div>
                    <div (click)="updateTaskPriority(priority.id, task)">
                      {{ priority.name }}
                    </div>
                  </div>
                </ng-container>
              </div>
            </ng-template>
          </div>
        </div>
        <div class="pt-2 row">
          <div *ngIf="('billable_act' | checkActive : this.formconfig: 'my-task-edit') && commercialEditAllowed">
            <div class="d-flex">
              <mat-checkbox class="checkbox-input custom-checkbox" formControlName="billableActivity">
              </mat-checkbox>
              <div class="label">
                {{('billable_act' | checkLabel : this.formconfig: 'my-task-edit': 'Billable Activity')}}
              </div>
            </div>
          </div>
        </div>
      </form>
      <div class="task-edit-assigned-to" *ngIf="('assigned_to' | checkActive : this.formconfig: 'my-task-edit') && assignedToEditAllowed">
        <div class="content-title">
          {{('assigned_to' | checkLabel : this.formconfig: 'my-task-edit': 'Assigned To')}}
          <span style="color: #EC5F6E;"
          *ngIf="('assigned_to' | checkMandatedField : this.formconfig: 'my-task-edit')">*</span>
        </div>
        <div>
          <div class="detail-value" *ngIf="task['assigned_to'].length > 0">
            <app-people-icon-display [peopleList]="task['assigned_to']"
            [count]="task['assigned_to'].length" [bgColor]="button">
            </app-people-icon-display>
          </div>
          <div class="detail-value" *ngIf="task['assigned_to'].length == 0">
            -
          </div>
        </div>
        <div class="pt-2">
          <div class="date-field" style="width: 100% !important; padding: 5px !important;">
            <div class="task-name-field">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <mask id="mask0_14526_13953" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="24">
                <rect width="24" height="24" fill="#D9D9D9"/>
                </mask>
                <g mask="url(#mask0_14526_13953)">
                <path d="M19.6 21L13.3 14.7C12.8 15.1 12.225 15.4167 11.575 15.65C10.925 15.8833 10.2333 16 9.5 16C7.68333 16 6.14583 15.3708 4.8875 14.1125C3.62917 12.8542 3 11.3167 3 9.5C3 7.68333 3.62917 6.14583 4.8875 4.8875C6.14583 3.62917 7.68333 3 9.5 3C11.3167 3 12.8542 3.62917 14.1125 4.8875C15.3708 6.14583 16 7.68333 16 9.5C16 10.2333 15.8833 10.925 15.65 11.575C15.4167 12.225 15.1 12.8 14.7 13.3L21 19.6L19.6 21ZM9.5 14C10.75 14 11.8125 13.5625 12.6875 12.6875C13.5625 11.8125 14 10.75 14 9.5C14 8.25 13.5625 7.1875 12.6875 6.3125C11.8125 5.4375 10.75 5 9.5 5C8.25 5 7.1875 5.4375 6.3125 6.3125C5.4375 7.1875 5 8.25 5 9.5C5 10.75 5.4375 11.8125 6.3125 12.6875C7.1875 13.5625 8.25 14 9.5 14Z" fill="#7D838B"/>
                </g>
                </svg>                            
              <input matInput [placeholder]="'Find stakeholders'" [(ngModel)]="userSearchParams" (keydown.enter)="onEnterSearch(userSearchParams)" />
              <div (click)="clearSearch()" style="cursor: pointer;" matTooltip="Clear">
                <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M6.98636 12.1841L9.46341 9.72099L11.9405 12.1841L12.6655 11.4632L10.1885 9L12.6655 6.53684L11.9405 5.81586L9.46341 8.27901L6.98636 5.81586L6.26131 6.53684L8.73836 9L6.26131 11.4632L6.98636 12.1841ZM9.46462 15.5C8.56049 15.5 7.71067 15.3294 6.91515 14.9882C6.11963 14.647 5.42766 14.184 4.83925 13.5991C4.25083 13.0142 3.78495 12.3264 3.44161 11.5357C3.09837 10.745 2.92676 9.90014 2.92676 9.0012C2.92676 8.10214 3.09832 7.25709 3.44143 6.46603C3.78455 5.67496 4.2502 4.98688 4.83839 4.40176C5.42657 3.81665 6.11825 3.35338 6.91343 3.01196C7.70861 2.67065 8.5582 2.5 9.46221 2.5C10.3663 2.5 11.2162 2.6706 12.0117 3.01179C12.8072 3.35298 13.4992 3.81602 14.0876 4.40091C14.676 4.98579 15.1419 5.6736 15.4852 6.46432C15.8285 7.25504 16.0001 8.09986 16.0001 8.9988C16.0001 9.89786 15.8285 10.7429 15.4854 11.534C15.1423 12.325 14.6766 13.0131 14.0884 13.5982C13.5003 14.1834 12.8086 14.6466 12.0134 14.988C11.2182 15.3293 10.3686 15.5 9.46462 15.5ZM9.46341 14.4737C11.0001 14.4737 12.3017 13.9434 13.3682 12.8829C14.4347 11.8224 14.968 10.5281 14.968 9C14.968 7.47193 14.4347 6.17763 13.3682 5.11711C12.3017 4.05658 11.0001 3.52632 9.46341 3.52632C7.92672 3.52632 6.62513 4.05658 5.55862 5.11711C4.49211 6.17763 3.95886 7.47193 3.95886 9C3.95886 10.5281 4.49211 11.8224 5.55862 12.8829C6.62513 13.9434 7.92672 14.4737 9.46341 14.4737Z"
                    fill="#7D838B" />
                </svg>
              </div>
            </div>
          </div>
        </div>
        <div class="pt-2" style="overflow-x: auto;">
          <ng-container *ngFor = "let member of stakeholdersList; let i = index">
            <div class="d-flex line-item">
              <app-people-icon-display [peopleList]="[member['associate_id']]"
              [count]="[member['associate_id']].length" [bgColor]="button">
              </app-people-icon-display>
              <div class="member-name">
                {{member.employee_name}}
              </div>
              <div class="action" style="cursor: pointer;" (click)="modifyStakeholder(member)" [disabled]="stakeholderChanges"> 
                <div *ngIf = "member.isSelected">
                  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M6.80484 9.80737L4.84863 7.85095C4.732 7.73446 4.5854 7.67481 4.40884 7.672C4.23242 7.66933 4.08316 7.72898 3.96105 7.85095C3.83909 7.97305 3.77811 8.12098 3.77811 8.29474C3.77811 8.46849 3.83909 8.61642 3.96105 8.73853L6.272 11.0495C6.42428 11.2016 6.60189 11.2777 6.80484 11.2777C7.00779 11.2777 7.1854 11.2016 7.33768 11.0495L12.0227 6.36442C12.1392 6.24779 12.1989 6.10119 12.2017 5.92463C12.2044 5.74821 12.1447 5.59895 12.0227 5.47684C11.9006 5.35488 11.7527 5.2939 11.5789 5.2939C11.4052 5.2939 11.2573 5.35488 11.1352 5.47684L6.80484 9.80737ZM8.00147 16C6.89495 16 5.85488 15.79 4.88126 15.3701C3.90765 14.9502 3.06077 14.3803 2.34063 13.6604C1.62049 12.9406 1.05032 12.094 0.630105 11.1208C0.210035 10.1476 0 9.10786 0 8.00147C0 6.89495 0.209965 5.85488 0.629895 4.88126C1.04982 3.90765 1.61972 3.06077 2.33958 2.34063C3.05944 1.62049 3.90597 1.05032 4.87916 0.630105C5.85235 0.210035 6.89214 0 7.99853 0C9.10505 0 10.1451 0.209965 11.1187 0.629894C12.0924 1.04982 12.9392 1.61972 13.6594 2.33958C14.3795 3.05944 14.9497 3.90597 15.3699 4.87916C15.79 5.85235 16 6.89214 16 7.99853C16 9.10505 15.79 10.1451 15.3701 11.1187C14.9502 12.0924 14.3803 12.9392 13.6604 13.6594C12.9406 14.3795 12.094 14.9497 11.1208 15.3699C10.1476 15.79 9.10786 16 8.00147 16Z" fill="#52C41A"/>
                    </svg>                    
                </div>
                <div *ngIf = "!member.isSelected">
                  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M6.80484 9.80737L4.84863 7.85095C4.732 7.73446 4.5854 7.67481 4.40884 7.672C4.23242 7.66933 4.08316 7.72898 3.96105 7.85095C3.83909 7.97305 3.77811 8.12098 3.77811 8.29474C3.77811 8.46849 3.83909 8.61642 3.96105 8.73853L6.272 11.0495C6.42428 11.2016 6.60189 11.2777 6.80484 11.2777C7.00779 11.2777 7.1854 11.2016 7.33768 11.0495L12.0227 6.36442C12.1392 6.24779 12.1989 6.10119 12.2017 5.92463C12.2044 5.74821 12.1447 5.59895 12.0227 5.47684C11.9006 5.35488 11.7527 5.2939 11.5789 5.2939C11.4052 5.2939 11.2573 5.35488 11.1352 5.47684L6.80484 9.80737ZM8.00147 16C6.89495 16 5.85488 15.79 4.88126 15.3701C3.90765 14.9502 3.06077 14.3803 2.34063 13.6604C1.62049 12.9406 1.05032 12.094 0.630105 11.1208C0.210035 10.1476 0 9.10786 0 8.00147C0 6.89495 0.209965 5.85488 0.629895 4.88126C1.04982 3.90765 1.61972 3.06077 2.33958 2.34063C3.05944 1.62049 3.90597 1.05032 4.87916 0.630105C5.85235 0.210035 6.89214 0 7.99853 0C9.10505 0 10.1451 0.209965 11.1187 0.629894C12.0924 1.04982 12.9392 1.61972 13.6594 2.33958C14.3795 3.05944 14.9497 3.90597 15.3699 4.87916C15.79 5.85235 16 6.89214 16 7.99853C16 9.10505 15.79 10.1451 15.3701 11.1187C14.9502 12.0924 14.3803 12.9392 13.6604 13.6594C12.9406 14.3795 12.094 14.9497 11.1208 15.3699C10.1476 15.79 9.10786 16 8.00147 16Z" fill="#D4D6D8"/>
                    </svg>                    
                </div>
              </div>
            </div>
          </ng-container>
        </div>
      </div>
    </div>
  </div>
</div>