import {
  Compo<PERSON>,
  OnInit,
  HostListener,
  ViewContainerRef,
  TemplateRef,
  ViewChild,
  ChangeDetectorRef
} from '@angular/core';
import * as _ from "underscore";
import * as moment from 'moment';
import { ActivatedRoute, Router } from "@angular/router";
import { Form<PERSON><PERSON>er, FormControl, FormGroup, Validators } from "@angular/forms";
import { Overlay, OverlayConfig, OverlayRef } from "@angular/cdk/overlay";
import { TemplatePortal } from "@angular/cdk/portal";
import { LoginService } from "src/app/services/login/login.service";
import { PmMasterService } from "src/app/modules/project-management/services/pm-master.service";
import { PmAuthorizationService } from "src/app/modules/project-management/services/pm-authorization.service";
import { MyTaskService } from "./../../services/my-task.service";
import { ToasterMessageService } from 'src/app/modules/project-management/shared-lazy-loaded/components/toaster-message/toaster-message.service';
import { PmBillingService } from 'src/app/modules/project-management/features/pm-details/features/pm-billing/services/pm-billing.service'

@Component({
  selector: 'app-my-task-edit',
  templateUrl: './my-task-edit.component.html',
  styleUrls: ['./my-task-edit.component.scss']
})
export class MyTaskEditComponent implements OnInit {

  projectId: any;
  itemId: any;
  aid: any;
  formconfig: any;
  button: any;
  fontStyle: any;
  tabColor: any;
  fieldOutline: any;
  toggleButton: any;
  toggleButtonBg: any;
  isPageLoading: boolean = false;
  taskId: number;
  loadingGifUrl: any;
  isTaskEditAllowed: boolean;
  columns: any;
  statusDetails: any;
  priorityDetails: any;
  task: any;
  header_name: any = "Modify Task";
  taskForm: FormGroup;
  isApiInProgress: boolean = false;
  private overlayRef: OverlayRef | null;
  userSearchParams: any = '';
  stakeholdersList: any = [];
  stakeholderChanges: boolean = false;
  minDate: any;
  maxDate: any;
  assignedToEditAllowed: boolean = false
  sheet_number_length:any=16

  @ViewChild("statusdropdownTemplate")
  statusdropdownTemplate!: TemplateRef<any>;

  @ViewChild("inlineDatePickerTemplate") 
  inlineDatePickerTemplate!: TemplateRef<any>;

  @ViewChild("prioritydropdownTemplate") 
  prioritydropdownTemplate!: TemplateRef<any>;
  taskDurationEditAllowed: boolean = false;
  statusEditAllowed: boolean = false;
  priorityEditAllowed: boolean = false;
  commercialEditAllowed: boolean = false;

  constructor(
    private router: Router,
    private _fb: FormBuilder,
    private overlay: Overlay,
    private viewContainerRef: ViewContainerRef,
    private cdr: ChangeDetectorRef,
    private route: ActivatedRoute,
    private _loginService: LoginService,
    private toasterService: ToasterMessageService,
    private pmMasterService: PmMasterService,
    private authService: PmAuthorizationService,
    private myTaskService: MyTaskService,
    private PmBillingService: PmBillingService
  ) { }

  async ngOnInit() {

    this.isPageLoading = true;
    this.itemId = parseInt(this.router.url.split("/")[5]);
    this.projectId = parseInt(this.router.url.split("/")[3]);
    this.aid = await this._loginService.getProfile().profile.aid;
    this.calculateDynamicStyle();
    this.route.paramMap.subscribe((params) => {
      this.taskId = +params.get("task_id");
    });

    this.taskForm = this._fb.group({
      taskName: [""],
      startDate: [""],
      dueDate: [""],
      estimatedHours: [null],
      completedPercentage: [null],
      weightedPercentage: [null],
      priority: [""],
      billableActivity: [false],
      assignedTo: [[]],
      status_id:[""],
      sheetNumber:[""],
      actualStartDate:[""],
      actualEndDate:[""],
      softwareType:[""]
    });

    await this.pmMasterService.getPMFormCustomizeConfigV().then((res: any) => {
      if (res) {
        this.formconfig = res;
      }
    });
    const primaLoading = _.findWhere(this.formconfig, {
      type: "my-task-list-view",
      field_name: "loading-gif",
      is_active: true,
    });
    this.loadingGifUrl = primaLoading ? primaLoading["LOADING-GIF"] : null;
    this.isTaskEditAllowed = await this.authService.getProjectWiseObjectAccess(this.projectId, this.itemId, 304)
    if(!this.isTaskEditAllowed){
      this.toasterService.showWarning("No Access to edit task details", 5000)
      await new Promise(resolve => setTimeout(resolve, 500));
      history.back();
    }
    this.assignedToEditAllowed = await this.authService.getProjectWiseObjectAccess(this.projectId, this.itemId, 311);
    this.taskDurationEditAllowed = await this.authService.getProjectWiseObjectAccess(this.projectId, this.itemId, 313);
    this.statusEditAllowed = await this.authService.getProjectWiseObjectAccess(this.projectId, this.itemId, 312);
    this.priorityEditAllowed = await this.authService.getProjectWiseObjectAccess(this.projectId, this.itemId, 314);
    this.commercialEditAllowed = await this.authService.getProjectWiseObjectAccess(this.projectId, this.itemId, 316);
    const retrieveStyles = _.where(this.formconfig, {
      type: "project-theme",
      field_name: "styles",
      is_active: true,
    });
    const retrieveSheetLengthSoftwate = _.where(this.formconfig, { type: "gantt", field_name: "sheet_number", is_active: true });
    this.sheet_number_length=retrieveSheetLengthSoftwate.length > 0 ? retrieveSheetLengthSoftwate[0].maxLength :16;

    this.button =
      retrieveStyles.length > 0
        ? retrieveStyles[0].data.button_color
          ? retrieveStyles[0].data.button_color
          : "#90ee90"
        : "#90ee90";
    document.documentElement.style.setProperty("--myTaskButton", this.button);

    this.fontStyle =
      retrieveStyles.length > 0
        ? retrieveStyles[0].data.font_style
          ? retrieveStyles[0].data.font_style
          : "Roboto"
        : "Roboto";
    document.documentElement.style.setProperty("--myTaskFont", this.fontStyle);

    this.tabColor =
      retrieveStyles.length > 0
        ? retrieveStyles[0].data.tab_color
          ? retrieveStyles[0].data.tab_color
          : ""
        : "";
    document.documentElement.style.setProperty(
      "--myTaskTabColor",
      this.tabColor
    );

    this.fieldOutline =
      retrieveStyles.length > 0
        ? retrieveStyles[0].data.field_outline_color
          ? retrieveStyles[0].data.field_outline_color
          : ""
        : "";
    document.documentElement.style.setProperty(
      "--myTaskFieldOutline",
      this.fieldOutline
    );

    this.toggleButtonBg =
      retrieveStyles.length > 0
        ? retrieveStyles[0].data.shades_color
          ? retrieveStyles[0].data.shades_color
          : ""
        : "";
    document.documentElement.style.setProperty(
      "--myTasktoggleButtonBg",
      this.toggleButtonBg
    );

    this.toggleButton =
      retrieveStyles.length > 0
        ? retrieveStyles[0].data.toggle_color
          ? retrieveStyles[0].data.toggle_color
          : ""
        : "";
    document.documentElement.style.setProperty(
      "--myTasktoggleButton",
      this.toggleButton
    );



    const columnsConfig = _.where(this.formconfig, { type: "my-task-landing-list", field_name: "style_config", is_active: true });
    this.columns = columnsConfig[0]?.columnConfig ? columnsConfig[0]?.columnConfig : null;

    await this.pmMasterService.getProjectActivityStatus().then((res: any) => {
      if (res['messType'] === 'S' && res['data'] && res['data'].length > 0) {
        this.statusDetails = res['data'];
      } else if (res['messType'] === 'E') {
        this.toasterService.showError(res['messText'] ? res['messText'] : 'Error while retrieving status details');
      }
    });

    await this.pmMasterService.getProjectActivityPriority().then((res: any) => {
      if (res['messType'] === 'S' && res['data'] && res['data'].length > 0) {
        this.priorityDetails = res['data'];
      } else if (res['messType'] === 'E') {
        this.toasterService.showError(res['messText'] ? res['messText'] : 'Error while retrieving status details');
      }
    });

    await this.PmBillingService.getMaxMinProjectDatesPO(
      this.projectId,
      this.itemId
    ).then((res) => {
      if (res['messType'] == 'S') {
        if (res['data'].length > 0) {
          this.minDate = moment(res['data'][0]['min_date']).format('YYYY-MM-DD');
          this.maxDate = moment(res['data'][0]['max_date']).format('YYYY-MM-DD');
        }
      }
    });

    await this.getTaskdetails(this.taskId)
    this.taskForm.patchValue({
      taskName: this.task.description,
      startDate: this.task.start_date
        ? moment(this.task.start_date).format("YYYY-MM-DD")
        : null,
      dueDate: this.task.end_date
        ? moment(this.task.end_date).format("YYYY-MM-DD")
        : null,
        actualstartDate: this.task.actual_start_date
        ? moment(this.task.actual_start_date).format("YYYY-MM-DD")
        : null,
      actualEndDate: this.task.actual_end_date
        ? moment(this.task.actual_end_date).format("YYYY-MM-DD")
        : null,
      estimatedHours: this.task.estimated_hours,
      sheetNumber:this.task.sheet_number,
      softwareType:this.task.software_type,
      completedPercentage: this.task.completion_percentage,
      weightedPercentage: this.task.weighted_percentage,
      priority: this.task.priority || "",
      billableActivity: this.task.billable_act,
      status_id: this.task.status_id
    });
    await this.addPeopleToTaskSearch(this.userSearchParams);
    this.isPageLoading = false;    
  }

  calculateDynamicStyle() {
    let myTaskEditWidth = window.innerWidth - 104 + "px";
    document.documentElement.style.setProperty(
      "--myTaskEditWidth",
      myTaskEditWidth
    );
    let myTaskHeight = window.innerHeight - 148 + "px";
    document.documentElement.style.setProperty("--myTaskHeight", myTaskHeight);
    let myTaskInnerHeight = window.innerHeight - 219 + "px";
    document.documentElement.style.setProperty(
      "--myTaskInnerHeight",
      myTaskInnerHeight
    );
    let myTaskSubHeight = window.innerHeight - 241 + "px";
    document.documentElement.style.setProperty(
      "--myTaskSubHeight",
      myTaskSubHeight
    );
  }

  @HostListener("window:resize")
  onResize() {
    this.calculateDynamicStyle();
  }

  async getTaskdetails(taskId: number) {
    await this.myTaskService.getMyTaskDetails(this.projectId, this.itemId, taskId).then((res: any) => {
      if (res['messType'] == "S" && res['data'].length > 0) {
        this.task = res['data'][0]
        this.task.isFavTask = this.task.isFavTask ? this.task.isFavTask : false;
        this.task.isTaskCompleted = this.task.isTaskCompleted ? this.task.isTaskCompleted : false;
      } else if (res['messType'] === 'E') {
        this.toasterService.showError(res['messText'] ? res['messText'] : 'Error while retrieving task details');
      }
    })
  }

  async addPeopleToTaskSearch(userSearchParams) {
    await this.myTaskService.addPeopleToTaskSearch(this.projectId, this.itemId, userSearchParams).then((res: any) => {
      if (res['messType'] == "S") {
        this.stakeholdersList = res['data'];
        this.stakeholdersList.forEach(stakeholder => {
          stakeholder.isSelected = this.task.assigned_to.includes(stakeholder.associate_id);
        });
      } else if (res['messType'] === 'E') {
        this.toasterService.showError(res['messText'] ? res['messText'] : 'Error while retrieving stakeholders details');
      }
    })
  }

  async onEnterSearch(userSearchParams){
    await this.addPeopleToTaskSearch(userSearchParams);
  }

  async clearSearch(){
    this.userSearchParams = ''
    await this.addPeopleToTaskSearch(this.userSearchParams);
  }

  modifyStakeholder(member) {
    this.stakeholderChanges = true;
    if (member.isSelected) {
      this.task.assigned_to = this.task.assigned_to.filter(id => id !== member.associate_id);
      member.isSelected = false;
    } else {
      this.task.assigned_to.push(member.associate_id);
      member.isSelected = true;
    }
    this.stakeholderChanges = false;
  }
  

  routeBackToDetail() {
    history.back();
  }

  clearTaskName() {
    this.taskForm.patchValue({
      taskName: [""]
    });
  }

  statusDropdown(event: MouseEvent, task: any) {
    event.stopPropagation();

    // Close the overlay if it is already open
    if (this.overlayRef) {
      this.closeOverlay();
    } else {
      const target = event.currentTarget as HTMLElement;

      // Create overlay config
      const config = new OverlayConfig({
        hasBackdrop: true,
        backdropClass: "cdk-overlay-transparent-backdrop",
        positionStrategy: this.overlay
          .position()
          .flexibleConnectedTo(target)
          .withPositions([
            {
              originX: "end",
              originY: "bottom",
              overlayX: "end",
              overlayY: "top",
            },
          ]),
      });

      // Create overlay reference
      this.overlayRef = this.overlay.create(config);

      // Attach the template to the overlay
      this.overlayRef.attach(
        new TemplatePortal(this.statusdropdownTemplate, this.viewContainerRef, {
          $implicit: task // Pass the task as context to the template
        })
      );

      // Close overlay on backdrop click
      this.overlayRef.backdropClick().subscribe(() => this.closeOverlay());
    }
  }

  closeOverlay() {
    if (this.overlayRef) {
      this.overlayRef.dispose();
      this.overlayRef = null;
    }
  }

  updateTaskStatus(statusId, task) {
    this.taskForm.patchValue({
      status_id: statusId
    });
    this.task.status_id = statusId;
    this.closeOverlay();
  }

  inlineDatePicker(event: MouseEvent, task: any, column: any) { 
    event.stopPropagation();
    
    // Close the overlay if it is already open
    if (this.overlayRef) {
      this.closeOverlay();
    } else {
      const target = event.currentTarget as HTMLElement;
      
      // Create overlay config
      const config = new OverlayConfig({
        hasBackdrop: true,
        backdropClass: "cdk-overlay-transparent-backdrop",
        positionStrategy: this.overlay
          .position()
          .flexibleConnectedTo(target)
          .withPositions([
            {
              originX: "end",
              originY: "bottom",
              overlayX: "end",
              overlayY: "top",
            },
          ]),
      });
  
      this.overlayRef = this.overlay.create(config);
      
      this.overlayRef.attach(
        new TemplatePortal(this.inlineDatePickerTemplate, this.viewContainerRef, {
          $implicit: task,
          column: column
        })
      );
  
      // Close overlay on backdrop click
      this.overlayRef.backdropClick().subscribe(() => this.closeOverlay());
    }
  }

  async updateInlineDate(task, date, column){
    this.isApiInProgress = true;
    if(column === "start_date"){
      task['display_start_date'] = moment(date).format("DD-MMM-YYYY");
      task[column] = moment(date).format("YYYY-MM-DD");
      this.taskForm.patchValue({
        startDate: date
          ? moment(date).format("YYYY-MM-DD")
          : null})

}else if(column === "end_date"){
      task['display_end_date'] = moment(date).format("DD-MMM-YYYY");
      task[column] = moment(date).format("YYYY-MM-DD");
      this.taskForm.patchValue({
        dueDate: date
        ? moment(date).format("YYYY-MM-DD")
        : null})
}else if(column === "actual_start_date"){
  task['display_actual_date'] = moment(date).format("DD-MMM-YYYY");
  task[column] = moment(date).format("YYYY-MM-DD");
  this.taskForm.patchValue({
    actualStartDate: date
      ? moment(date).format("YYYY-MM-DD")
      : null})

}else if(column === "actual_end_date"){
  task['display_actual_end_date'] = moment(date).format("DD-MMM-YYYY");
  task[column] = moment(date).format("YYYY-MM-DD");
  this.taskForm.patchValue({
    actualEndDate: date
    ? moment(date).format("YYYY-MM-DD")
    : null})
}

    this.isApiInProgress = false;
    this.closeOverlay();
  }

  priorityDropdown(event: MouseEvent, task: any) {
    event.stopPropagation();
    
    // Close the overlay if it is already open
    if (this.overlayRef) {
      this.closeOverlay();
    } else {
      const target = event.currentTarget as HTMLElement;
      
      // Create overlay config
      const config = new OverlayConfig({
        hasBackdrop: true,
        backdropClass: "cdk-overlay-transparent-backdrop",
        positionStrategy: this.overlay
          .position()
          .flexibleConnectedTo(target)
          .withPositions([
            {
              originX: "end",
              originY: "bottom",
              overlayX: "end",
              overlayY: "top",
            },
          ]),
      });
  
      // Create overlay reference
      this.overlayRef = this.overlay.create(config);
      
      // Attach the template to the overlay
      this.overlayRef.attach(
        new TemplatePortal(this.prioritydropdownTemplate, this.viewContainerRef, {
          $implicit: task // Pass the task as context to the template
        })
      );
  
      // Close overlay on backdrop click
      this.overlayRef.backdropClick().subscribe(() => this.closeOverlay());
    }
  }

  updateTaskPriority(priority, task) {
    this.taskForm.patchValue({
      priority: priority
    });
    this.task.priority = priority;
    this.closeOverlay();
  }

  async saveTaskDetails(){
    let data = this.taskForm.value;
    let vaidations = await this.checkValidations(data);
    if(vaidations){
      return;
    }
    data.assignedTo =  this.task.assigned_to ? this.task.assigned_to : [];
    data.gantt_id = this.task.gantt_id ? this.task.gantt_id : null;
    await this.myTaskService.updateTaskDetails(this.projectId, this.itemId, this.taskId, data).then((res: any) => {
      if (res['messType'] == "S") {
        this.toasterService.showSuccess(res['messText'],10000);
        history.back();
      } else {
        this.toasterService.showError(res['messText'] ? res['messText'] : 'Error while updating task details');
      }
    })
  }

  async checkValidations(data) {
    // Trim spaces for fields that might contain them
    if (data.taskName) {
        data.taskName = data.taskName.trim();
    }

    const startDate = moment(data.startDate);
    const dueDate = moment(data.dueDate);

    const fieldMappings = _.where(this.formconfig, { type: "my-task-edit", is_active: true });

    for (const mapping of fieldMappings) {
        const value = this.taskForm.value[mapping.field];
        
        // Trim the value if it's a string
        if (typeof value === 'string') {
            this.taskForm.patchValue({ [mapping.field]: value.trim() }, { emitEvent: false });
        }

        // Check for mandatory fields
        if ((value === null || value === undefined || value === "") && this.isMandate(mapping.field_name)) {
            this.toasterService.showWarning(`${mapping.label} cannot be empty`, 10000);
            return true;
        }
    }

    // Validation for completedPercentage
    if (data.completedPercentage < 0 || data.completedPercentage > 100) {
        this.toasterService.showWarning("Task Completion Percentage should be 0-100", 10000);
        return true;
    } 
    // Validation for weightedPercentage
    else if (data.weightedPercentage < 0 || data.weightedPercentage > 100) {
        this.toasterService.showWarning("Task weighted Percentage should be 0-100", 10000);
        return true;
    } 
    // Validation for estimatedHours
    else if (data.estimatedHours && data.estimatedHours <= 0) {
        this.toasterService.showWarning("Hours cannot be less than or equal to zero", 10000);
        return true;
    }
    else if (data.sheetNumber && data.sheetNumber <= 0) {
      this.toasterService.showWarning("Sheet Number cannot be less than  zero", 10000);
      return true;
  }
    // Validation for dates
    else if (!dueDate.isValid() || !startDate.isValid()) {
        this.toasterService.showWarning('Kindly validate task duration', 10000);
        return true;
    } 
    else if (dueDate.isBefore(startDate)) {
        this.toasterService.showWarning('End date cannot be before the start date', 10000);
        return true;
    } 
    else if (startDate.isAfter(dueDate)) {
        this.toasterService.showWarning('Start date cannot be after the end date', 10000);
        return true;
    }

    return false; // No validations failed
}

isMandate(field: any) {
  const mandate = _.where(this.formconfig, { type: "my-task-edit", field_name: field, is_active: true });
  if (mandate.length > 0) {
    const isMandate = mandate[0].is_mandant;
    return isMandate;
  }
}

}
