<div class="task-list-view">
  <!-- Loading -->
  <div *ngIf="isPageLoading" class="loading-img">
    <div class="load-img">
      <img *ngIf="loadingGifUrl" [src]="loadingGifUrl" />
    </div>
    <div class="loading-wrapper">
      <div class="loading">Loading...</div>
    </div>
  </div>
  <!--After Loading -->
  <div *ngIf="!isPageLoading">
    <div class="task-action-header">
      <div class="my-task-search" *ngIf="getIconStatus('search')" [matTooltip]="getIconLabel('search')">
        <!-- Search Icon -->
        <div class="header-icon" (click)="openSearchBarOverlay(triggerSearchField)" cdkOverlayOrigin
          #triggerSearchBar="cdkOverlayOrigin" #triggerSearchField>
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g clip-path="url(#clip0_18774_5853)">
              <path
                d="M12.0482 11.0737L15 14.0248L14.0248 15L11.0737 12.0482C9.9757 12.9285 8.60993 13.4072 7.20262 13.4052C3.77877 13.4052 1 10.6265 1 7.20262C1 3.77877 3.77877 1 7.20262 1C10.6265 1 13.4052 3.77877 13.4052 7.20262C13.4072 8.60993 12.9285 9.9757 12.0482 11.0737ZM10.6657 10.5624C11.5404 9.66291 12.0289 8.45722 12.0269 7.20262C12.0269 4.53687 9.86768 2.37836 7.20262 2.37836C4.53687 2.37836 2.37836 4.53687 2.37836 7.20262C2.37836 9.86768 4.53687 12.0269 7.20262 12.0269C8.45722 12.0289 9.66291 11.5404 10.5624 10.6657L10.6657 10.5624Z"
                fill="#515965" />
            </g>
            <defs>
              <clipPath id="clip0_18774_5853">
                <rect width="16" height="16" fill="white" />
              </clipPath>
            </defs>
          </svg>
        </div>
      </div>
      <ng-template #triggerSearchBarTemplateRef>
        <div class="bg-container">
          <div class="d-flex align-items-center justify-content-between">
            <svg class="svg-icon" width="16" height="16" viewBox="0 0 16 16" fill="none"
              xmlns="http://www.w3.org/2000/svg">
              <path
                d="M7.33337 1.3335C10.6454 1.3335 13.3334 4.0215 13.3334 7.3335C13.3334 10.6455 10.6454 13.3335 7.33337 13.3335C4.02137 13.3335 1.33337 10.6455 1.33337 7.3335C1.33337 4.0215 4.02137 1.3335 7.33337 1.3335ZM7.33337 12.0002C9.91137 12.0002 12 9.9115 12 7.3335C12 4.75483 9.91137 2.66683 7.33337 2.66683C4.75471 2.66683 2.66671 4.75483 2.66671 7.3335C2.66671 9.9115 4.75471 12.0002 7.33337 12.0002ZM12.99 12.0475L14.876 13.9328L13.9327 14.8762L12.0474 12.9902L12.99 12.0475Z"
                fill="#B9C0CA" />
            </svg>
            <div class="search-bar">
              <input #inputField type="text" [(ngModel)]="filterConfig.search_params"
                placeholder="Search by Task id/name" (keydown.enter)="onEnterSearch(filterConfig.search_params)" />
            </div>
            <div (click)="clearSearchFilter()" style="cursor: pointer;" matTooltip="Clear Search">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M7.99999 7.05732L11.3 3.75732L12.2427 4.69999L8.94266 7.99999L12.2427 11.3L11.3 12.2427L7.99999 8.94266L4.69999 12.2427L3.75732 11.3L7.05732 7.99999L3.75732 4.69999L4.69999 3.75732L7.99999 7.05732Z"
                  fill="#8B95A5" />
              </svg>
            </div>
            <div style="cursor: pointer" (click)="onEnterSearch(filterConfig.search_params)">
              <!-- Your SVG or icon here for the action -->
            </div>
          </div>
          <ng-container *ngIf="recentSearch.length > 0">
            <div class="divider"></div>
            <span class="recent-search-title">Recently Searched</span>
            <div class="d-flex align-items-center search-text-list" *ngFor="let item of recentSearch; let i = index"
              (click)="onSelectRecentSearch(i)">
              <div style="margin-bottom: 1px">
                <!-- Your SVG or icon here -->
              </div>
              <div class="recent-search-text" [innerHTML]="highlightSearch(item)"></div>
            </div>
          </ng-container>
        </div>
      </ng-template>
      <div *ngIf="getIconStatus('smart_remainders')" [matTooltip]="getIconLabel('smart_remainders')">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g style="mix-blend-mode:plus-darker">
            <path
              d="M6.69599 16.4293C5.78235 14.8468 6.32455 12.8233 7.90703 11.9097L21.5973 4.00558C23.4246 7.17055 22.3402 11.2176 19.1752 13.0449L11.2157 17.6403C9.63316 18.554 7.60964 18.0118 6.69599 16.4293Z"
              fill="url(#paint0_linear_13679_72559)" />
          </g>
          <g style="mix-blend-mode:plus-darker">
            <path
              d="M6.79363 12.9083C7.70728 11.3258 9.7308 10.7836 11.3133 11.6973L19.2729 16.2927C22.4378 18.12 23.5222 22.1671 21.6949 25.332L8.00467 17.428C6.42218 16.5143 5.87998 14.4908 6.79363 12.9083Z"
              fill="url(#paint1_linear_13679_72559)" />
          </g>
          <path
            d="M7.33892 0.667969L8.33323 3.80943L11.4747 4.80374L8.33323 5.79804L7.33892 8.93951L6.34462 5.79804L3.20316 4.80374L6.34462 3.80943L7.33892 0.667969Z"
            fill="url(#paint2_linear_13679_72559)" />
          <path
            d="M2.3127 7.60303L2.80985 9.17376L4.38058 9.67091L2.80985 10.1681L2.3127 11.7388L1.81554 10.1681L0.244812 9.67091L1.81554 9.17376L2.3127 7.60303Z"
            fill="#F27A6C" />
          <defs>
            <linearGradient id="paint0_linear_13679_72559" x1="23.2516" y1="6.87092" x2="5.81952" y2="16.9353"
              gradientUnits="userSpaceOnUse">
              <stop stop-color="#EF4A61" />
              <stop offset="1" stop-color="#F6A198" />
            </linearGradient>
            <linearGradient id="paint1_linear_13679_72559" x1="23.3492" y1="22.4667" x2="5.91716" y2="12.4023"
              gradientUnits="userSpaceOnUse">
              <stop offset="0.0616666" stop-color="#FFC42C" />
              <stop offset="0.486667" stop-color="#FD6D81" />
              <stop offset="1" stop-color="#F27A6C" />
            </linearGradient>
            <linearGradient id="paint2_linear_13679_72559" x1="11.4747" y1="4.80374" x2="2.76525" y2="4.80374"
              gradientUnits="userSpaceOnUse">
              <stop stop-color="#EF4A61" />
              <stop offset="1" stop-color="#F27A6C" />
            </linearGradient>
          </defs>
        </svg>

      </div>
      <div *ngIf="getIconStatus('filter')" [matTooltip]="getIconLabel('filter')">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <mask id="mask0_13679_72556" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="24"
            height="24">
            <rect width="24" height="24" fill="#D9D9D9" />
          </mask>
          <g mask="url(#mask0_13679_72556)">
            <path
              d="M11 20C10.7167 20 10.4792 19.9042 10.2875 19.7125C10.0959 19.5208 10 19.2833 10 19V13L4.20002 5.6C3.95002 5.26667 3.91252 4.91667 4.08752 4.55C4.26252 4.18333 4.56669 4 5.00002 4H19C19.4334 4 19.7375 4.18333 19.9125 4.55C20.0875 4.91667 20.05 5.26667 19.8 5.6L14 13V19C14 19.2833 13.9042 19.5208 13.7125 19.7125C13.5209 19.9042 13.2834 20 13 20H11ZM12 12.3L16.95 6H7.05002L12 12.3Z"
              fill="#515965" />
          </g>
        </svg>
      </div>
      <div *ngIf="getIconStatus('group_by')" [matTooltip]="getIconLabel('group_by')" cdkOverlayOrigin
        #overlayOrigin="cdkOverlayOrigin" (click)="toggleDropdown($event)"
        [ngStyle]="{ fill: filterConfig.isGroupBy == 1 ? '#EE4961' : '#515965' }" style="cursor: pointer;">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g clip-path="url(#clip0_13679_67603)">
            <circle cx="7.5" cy="7.5" r="3"
              [attr.fill]="filterConfig.isGroupBy == 1 ? 'var(--myTaskButton)' : '#515965'" />
            <circle cx="7.5" cy="16.5" r="3"
              [attr.fill]="filterConfig.isGroupBy == 1 ? 'var(--myTaskButton)' : '#515965'" />
            <circle cx="16.5" cy="7.5" r="3"
              [attr.fill]="filterConfig.isGroupBy == 1 ? 'var(--myTaskButton)' : '#515965'" />
            <circle cx="16.5" cy="16.5" r="3"
              [attr.fill]="filterConfig.isGroupBy == 1 ? 'var(--myTaskButton)' : '#515965'" />
          </g>
          <defs>
            <clipPath id="clip0_13679_67603">
              <rect width="24" height="24" fill="white" />
            </clipPath>
          </defs>
        </svg>
        <ng-template #dropdownTemplate>
          <div class="dropdown" *ngIf="GroupByDetails?.length > 0">
            <ng-container *ngFor="let list of GroupByDetails[0].icon">
              <div class="dropdown-item" *ngIf="list.mode && list.mode.includes(mode)"
                [ngClass]="{'dropdown-item-selected': list.isSelected}">
                <div (click)="getGroupByData(list.keyName)">
                  {{ list.label }}
                </div>
              </div>
            </ng-container>
          </div>
        </ng-template>
      </div>
      <div class="toggle-group" *ngIf="isAllTaskAllowed && getIconStatus('All_task')">
        <mat-button-toggle-group (change)="toggleMode($event)" [value]="mode" [(ngModel)]="mode">
          <mat-button-toggle *ngFor="let types of viewList" [value]="types.id" [matTooltip]="types.name"
            [disabled]="disableToggle">
            <mat-icon [class]="types.class">{{ types.icon }}</mat-icon>
          </mat-button-toggle>
        </mat-button-toggle-group>
      </div>
      <div *ngIf="getIconStatus('column_customisation')" [matTooltip]="getIconLabel('column_customisation')"
        (click)="columnConfigToggle.toggle()" [satPopoverAnchor]="columnConfigToggle" style="cursor: pointer;">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <mask id="mask0_13679_67616" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="24"
            height="24">
            <rect width="24" height="24" fill="#D9D9D9" />
          </mask>
          <g mask="url(#mask0_13679_67616)">
            <path
              d="M5.17297 19.75V12.6923H3.17297V11.1923H8.67297V12.6923H6.67297V19.75H5.17297ZM5.17297 8.80775V4.25H6.67297V8.80775H5.17297ZM9.24997 8.80775V7.30775H11.25V4.25H12.75V7.30775H14.75V8.80775H9.24997ZM11.25 19.75V11.1923H12.75V19.75H11.25ZM17.327 19.75V16.6923H15.327V15.1923H20.827V16.6923H18.827V19.75H17.327ZM17.327 12.8077V4.25H18.827V12.8077H17.327Z"
              fill="#515965" />
          </g>
        </svg>
        <sat-popover #columnConfigToggle horizontalAlign="center" verticalAlign="below" hasBackdrop
          class="overlay-popover">
          <div class="card-column card">
            <div class="column-config-popup">
              <div class="column-list">
                <span *ngFor="let column of columns;let i=index">
                  <div class="row" *ngIf="column.isActive">
                    <label class="toggle-switch" [ngClass]="{ 'grayed-out-toggle': column.isDisabled }"
                      [style.background-color]="column.isDisabled ? 'grey' : (column.isVisible ? button : 'lightgrey')">
                      <input type="checkbox" [(ngModel)]="column.isVisible" [disabled]="column.isDisabled">
                      <span class="slider"></span>
                    </label>
                    <span class="column-header">{{ column.column_name }}</span>
                  </div>
                </span>
              </div>
            </div>
          </div>
        </sat-popover>
      </div>
      <div *ngIf="getIconStatus('summary_card')" [matTooltip]="getIconLabel('summary_card')" (click)="toggleSummaryCustomization()" style="cursor: pointer;">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <mask id="mask0_13679_67619" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="24"
            height="24">
            <rect width="24" height="24" fill="#D9D9D9" />
          </mask>
          <g mask="url(#mask0_13679_67619)">
            <path
              d="M2 15V9C2 8.45 2.19583 7.97917 2.5875 7.5875C2.97917 7.19583 3.45 7 4 7C4.55 7 5.02083 7.19583 5.4125 7.5875C5.80417 7.97917 6 8.45 6 9V15C6 15.55 5.80417 16.0208 5.4125 16.4125C5.02083 16.8042 4.55 17 4 17C3.45 17 2.97917 16.8042 2.5875 16.4125C2.19583 16.0208 2 15.55 2 15ZM9 19C8.45 19 7.97917 18.8042 7.5875 18.4125C7.19583 18.0208 7 17.55 7 17V7C7 6.45 7.19583 5.97917 7.5875 5.5875C7.97917 5.19583 8.45 5 9 5H15C15.55 5 16.0208 5.19583 16.4125 5.5875C16.8042 5.97917 17 6.45 17 7V17C17 17.55 16.8042 18.0208 16.4125 18.4125C16.0208 18.8042 15.55 19 15 19H9ZM18 15V9C18 8.45 18.1958 7.97917 18.5875 7.5875C18.9792 7.19583 19.45 7 20 7C20.55 7 21.0208 7.19583 21.4125 7.5875C21.8042 7.97917 22 8.45 22 9V15C22 15.55 21.8042 16.0208 21.4125 16.4125C21.0208 16.8042 20.55 17 20 17C19.45 17 18.9792 16.8042 18.5875 16.4125C18.1958 16.0208 18 15.55 18 15ZM9 17H15V7H9V17Z"
              [attr.fill]="isSummaryActive? 'var(--myTaskButton)' : '#515965'" />
          </g>
        </svg>
      </div>

      <ng-template #summaryCustomizationTemplate>
        <div class="customization-overlay">
          <div class="d-flex align-items-center justify-content-between">
            <div class="title-text">Summary</div>
            <div class="apply-btn" (click)="applySummaryCustomization()">Apply</div>
          </div>
          <div class="d-flex flex-column summary-config-list" cdkDropList (cdkDropListDropped)="dropSummaryField($event)">
            <div class="d-flex align-items-center justify-content-between" *ngFor="let item of tempSummaryData" cdkDrag>
              <div class="d-flex align-items-center">
                <div [class]="item.isDefaultVisible ? 'checkbox-disabled' : 'checkbox'">
                  <mat-checkbox [(ngModel)]="item.is_visible" [disabled]="item.isDefaultVisible"></mat-checkbox>
                </div>
                <div [ngClass]="item.isDefaultVisible ? 'text-default-visible' : 'text-not-default-visible'">
                  {{ item.label }}
                </div>
              </div>
              <div cdkDragHandle>
                <mat-icon class="icon">drag_indicator</mat-icon>
              </div>
            </div>
          </div>
        </div>
      </ng-template>


    </div>

    <div class="d-flex align-items-center justify-content-between my-task-bulk-edit" (mouseleave)="onMouseLeave()"
      *ngIf="bulkUpdateTaskCount > 0">
      <div class="d-flex align-items-center" style="gap: 20px">
        <ng-container>
          <div class="selected-text">{{ bulkUpdateTaskCount }} Selected</div>
          <div class="divider"></div>
        </ng-container>
        <ng-container *ngFor="let item of bulkEditData; let i = index">
          <ng-container *ngIf="i <= 9">
            <div *ngIf="!item.isHovered && !item.isProcessing" class="d-flex align-items-center svg"
              (mouseenter)="onMouseEnter(item.key)">
              <div class="default-stroke" [innerHTML]="item.svg | svgSecurityBypass"></div>
            </div>
            <ng-container *ngIf="item.type == 'overlay'">
              <ng-container *ngIf="item.key == 'assignedTo'">
                <div *ngIf="item.isHovered || item.isProcessing"
                  (click)="openOverlay('assignedTo', triggerTaskassignedToField, triggerTaskassignedToChange)"
                  cdkOverlayOrigin #triggerTaskassignedTo="cdkOverlayOrigin" #triggerTaskassignedToField
                  class="d-flex align-items-center layout" [ngStyle]="{ background: item.background }">
                  <div [ngStyle]="{ fill: item.color }" [innerHTML]="item.svg | svgSecurityBypass"></div>
                  <div class="layout-text" [ngStyle]="{ color: item.color }">
                    {{ item.label }}
                  </div>
                </div>
              </ng-container>
              <ng-container *ngIf="item.key == 'status'">
                <div *ngIf="item.isHovered || item.isProcessing"
                  (click)="openOverlay('markAsDone', triggerTaskmarkAsDoneField, triggerTaskmarkAsDoneChange)"
                  cdkOverlayOrigin #triggerTaskmarkAsDone="cdkOverlayOrigin" #triggerTaskmarkAsDoneField
                  class="d-flex align-items-center layout" [ngStyle]="{ background: item.background }">
                  <div [ngStyle]="{ fill: item.color }" [innerHTML]="item.svg | svgSecurityBypass"></div>
                  <div class="layout-text" [ngStyle]="{ color: item.color }">
                    {{ item.label }}
                  </div>
                </div>
              </ng-container>
              <ng-container *ngIf="item.key == 'AssignDueDate'">
                <div *ngIf="item.isHovered || item.isProcessing"
                  (click)="openOverlay('AssignDueDate', triggerTaskAssignDueDateField, triggerTaskAssignDueDateChange)"
                  cdkOverlayOrigin #triggerTaskAssignDueDate="cdkOverlayOrigin" #triggerTaskAssignDueDateField
                  class="d-flex align-items-center layout" [ngStyle]="{ background: item.background }">
                  <div [ngStyle]="{ fill: item.color }" [innerHTML]="item.svg | svgSecurityBypass"></div>
                  <div class="layout-text" [ngStyle]="{ color: item.color }">
                    {{ item.label }}
                  </div>
                </div>
              </ng-container>
              <ng-container *ngIf="item.key == 'priority'">
                <div *ngIf="item.isHovered || item.isProcessing"
                  (click)="openOverlay('priority', triggerTaskPriorityField, triggerTaskPriorityChange)" cdkOverlayOrigin
                  #triggerTaskPriority="cdkOverlayOrigin" #triggerTaskPriorityField
                  class="d-flex align-items-center layout" [ngStyle]="{ background: item.background }">
                  <div [ngStyle]="{ fill: item.color }" [innerHTML]="item.svg | svgSecurityBypass"></div>
                  <div class="layout-text" [ngStyle]="{ color: item.color }">
                    {{ item.label }}
                  </div>
                </div>
              </ng-container>

            </ng-container>
            <div *ngIf="bulkEditData.length - 1 != i" class="divider"></div>
          </ng-container>
        </ng-container>
      </div>
      <div class="d-flex align-items-center" style="gap: 24px">
        <div class="svg" (click)="closeToolbar()">
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
            <path
              d="M9.99984 18.9587C5.05817 18.9587 1.0415 14.942 1.0415 10.0003C1.0415 5.05866 5.05817 1.04199 9.99984 1.04199C14.9415 1.04199 18.9582 5.05866 18.9582 10.0003C18.9582 14.942 14.9415 18.9587 9.99984 18.9587ZM9.99984 2.29199C5.74984 2.29199 2.2915 5.75033 2.2915 10.0003C2.2915 14.2503 5.74984 17.7087 9.99984 17.7087C14.2498 17.7087 17.7082 14.2503 17.7082 10.0003C17.7082 5.75033 14.2498 2.29199 9.99984 2.29199Z"
              fill="#292D32" />
            <path
              d="M7.64147 12.9831C7.48314 12.9831 7.3248 12.9248 7.1998 12.7998C6.95814 12.5581 6.95814 12.1581 7.1998 11.9165L11.9165 7.1998C12.1581 6.95814 12.5581 6.95814 12.7998 7.1998C13.0415 7.44147 13.0415 7.84147 12.7998 8.08314L8.08314 12.7998C7.96647 12.9248 7.7998 12.9831 7.64147 12.9831Z"
              fill="#292D32" />
            <path
              d="M12.3581 12.9831C12.1998 12.9831 12.0415 12.9248 11.9165 12.7998L7.1998 8.08314C6.95814 7.84147 6.95814 7.44147 7.1998 7.1998C7.44147 6.95814 7.84147 6.95814 8.08314 7.1998L12.7998 11.9165C13.0415 12.1581 13.0415 12.5581 12.7998 12.7998C12.6748 12.9248 12.5165 12.9831 12.3581 12.9831Z"
              fill="#292D32" />
          </svg>
        </div>
      </div>
    </div>

    <div class="task-header">
      <div class="task-header-name">
        {{this.modeName}}
      </div>
      <div class="task-header-count">
        {{this.taskHeaderCount}}
      </div>
    </div>

    <div class="d-flex align-items-center justify-content-between my-task-summary-detailed" *ngIf="isSummaryActive">
      <div class="d-flex align-items-center" style="gap: 20px">
        <ng-container *ngFor="let item of summaryData; let i = index">
          <ng-container *ngIf="i <= 9">
            <ng-container *ngIf="item.is_visible || item.isDefaultVisible">
              <div class="d-flex align-items-center layout">
                <div [ngStyle]="{ background: item.background }" [innerHTML]="item.svg | svgSecurityBypass"
                  class="inner-color-circle"></div>
                <div class="summmaryData-text">
                  <div class="summmaryData-count">
                    {{ this.summaryCountData[item.key] ? this.summaryCountData[item.key] : '-' }}
                  </div>
                  <div class="summmaryData-label">{{ item.label }}</div>
                </div>
              </div>
              <div *ngIf="summaryData.length - 1 != i" class="divider"></div>
            </ng-container>
          </ng-container>
        </ng-container>
      </div>
      <div class="d-flex align-items-center" style="gap: 24px">
        <div class="svg" (click)="toggleSummaryCustomization()">
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
            <path
              d="M9.99984 18.9587C5.05817 18.9587 1.0415 14.942 1.0415 10.0003C1.0415 5.05866 5.05817 1.04199 9.99984 1.04199C14.9415 1.04199 18.9582 5.05866 18.9582 10.0003C18.9582 14.942 14.9415 18.9587 9.99984 18.9587ZM9.99984 2.29199C5.74984 2.29199 2.2915 5.75033 2.2915 10.0003C2.2915 14.2503 5.74984 17.7087 9.99984 17.7087C14.2498 17.7087 17.7082 14.2503 17.7082 10.0003C17.7082 5.75033 14.2498 2.29199 9.99984 2.29199Z"
              fill="#292D32" />
            <path
              d="M7.64147 12.9831C7.48314 12.9831 7.3248 12.9248 7.1998 12.7998C6.95814 12.5581 6.95814 12.1581 7.1998 11.9165L11.9165 7.1998C12.1581 6.95814 12.5581 6.95814 12.7998 7.1998C13.0415 7.44147 13.0415 7.84147 12.7998 8.08314L8.08314 12.7998C7.96647 12.9248 7.7998 12.9831 7.64147 12.9831Z"
              fill="#292D32" />
            <path
              d="M12.3581 12.9831C12.1998 12.9831 12.0415 12.9248 11.9165 12.7998L7.1998 8.08314C6.95814 7.84147 6.95814 7.44147 7.1998 7.1998C7.44147 6.95814 7.84147 6.95814 8.08314 7.1998L12.7998 11.9165C13.0415 12.1581 13.0415 12.5581 12.7998 12.7998C12.6748 12.9248 12.5165 12.9831 12.3581 12.9831Z"
              fill="#292D32" />
          </svg>
        </div>
      </div>
    </div>

    <div class="task-list">
      <div *ngIf="dataloading" class="loading-img">
        <div class="load-img">
          <img *ngIf="loadingGifUrl" [src]="loadingGifUrl" />
        </div>
        <div class="loading-wrapper">
          <div class="loading">Loading...</div>
        </div>
      </div>
      <div class="empty-state" *ngIf="taskHeaderCount === 0 && !dataloading && filterConfig.search_params == ''">
        <img src="https://assets.kebs.app/ATS-Empty-State.png">
        <div class="empty-title">No Task Found</div>
      </div>
      <div *ngIf="taskHeaderCount === 0 && !dataloading && filterConfig.search_params != ''">
        <div class="empty-state">
          <svg width="220" height="209" viewBox="0 0 220 209" fill="none" xmlns="http://www.w3.org/2000/svg"
            xmlns:xlink="http://www.w3.org/1999/xlink">
            <circle cx="103.579" cy="111.046" r="65.5077" fill="#F6F6F7" />
            <circle cx="42.1774" cy="66.8134" r="4.10589" fill="#F6F6F7" />
            <circle cx="150.237" cy="40.126" r="7.65189" fill="#F6F6F7" />
            <circle cx="43.4837" cy="153.225" r="7.27863" fill="#F6F6F7" />
            <circle cx="173.753" cy="146.133" r="7.27863" fill="#F6F6F7" />
            <circle cx="159.569" cy="154.344" r="3.54554" fill="#F6F6F7" />
            <path
              d="M177.172 123.673L178.315 129.073L183.638 130.233L178.315 131.393L177.172 136.793L176.029 131.393L170.706 130.233L176.029 129.073L177.172 123.673Z"
              fill="white" />
            <path
              d="M170.335 68.5401C170.418 68.1455 170.982 68.1455 171.065 68.5401L171.793 71.979C171.823 72.1227 171.935 72.2352 172.079 72.2664L175.492 73.01C175.884 73.0953 175.884 73.6541 175.492 73.7394L172.079 74.4829C171.935 74.5142 171.823 74.6267 171.793 74.7704L171.065 78.2092C170.982 78.6039 170.418 78.6039 170.335 78.2092L169.607 74.7704C169.576 74.6267 169.465 74.5142 169.321 74.4829L165.908 73.7394C165.516 73.6541 165.516 73.0953 165.908 73.01L169.321 72.2664C169.465 72.2352 169.576 72.1227 169.607 71.979L170.335 68.5401Z"
              fill="#7D838B" />
            <path
              d="M34.7957 113.704C34.8792 113.31 35.4425 113.31 35.526 113.704L36.7738 119.599C36.8042 119.743 36.916 119.855 37.0595 119.886L42.8934 121.157C43.2851 121.243 43.2851 121.801 42.8934 121.887L37.0595 123.158C36.916 123.189 36.8042 123.301 36.7738 123.445L35.526 129.34C35.4425 129.734 34.8792 129.734 34.7957 129.34L33.5479 123.445C33.5175 123.301 33.4057 123.189 33.2622 123.158L27.4283 121.887C27.0365 121.801 27.0365 121.243 27.4283 121.157L33.2622 119.886C33.4057 119.855 33.5175 119.743 33.5479 119.599L34.7957 113.704Z"
              fill="#7D838B" />
            <path
              d="M58.0515 50.964C57.8351 50.918 57.8351 50.6091 58.0515 50.5631L61.3234 49.8673C61.4023 49.8506 61.464 49.7891 61.481 49.7103L62.1855 46.4615C62.2322 46.2462 62.5394 46.2462 62.5861 46.4615L63.2906 49.7103C63.3077 49.7891 63.3694 49.8506 63.4482 49.8673L66.7201 50.5631C66.9365 50.6091 66.9365 50.918 66.7201 50.964L63.4482 51.6598C63.3694 51.6766 63.3077 51.738 63.2906 51.8168L62.5861 55.0657C62.5394 55.281 62.2322 55.281 62.1855 55.0657L61.481 51.8168C61.464 51.738 61.4023 51.6766 61.3234 51.6598L58.0515 50.964Z"
              fill="#7D838B" />
            <rect x="8" y="32" width="211.765" height="144" fill="url(#pattern0_13679_68717)" />
            <defs>
              <pattern id="pattern0_13679_68717" patternContentUnits="objectBoundingBox" width="1" height="1">
                <use xlink:href="#image0_13679_68717" transform="scale(0.002 0.00294118)" />
              </pattern>
              <image id="image0_13679_68717" width="500" height="340"
                xlink:href="data:image/png;base64,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" />
            </defs>
          </svg>
          <div class="empty-title">No Result Found</div>
          <div class="empty-body">Sorry, We Coudn't Find Any Matches For Your Search</div>
        </div>
      </div>
      <div *ngIf="taskHeaderCount > 0 && !dataloading" class="data-list" [ngStyle]="{
          'height': filterConfig.isGroupBy ? 'var(--myTasklistSubHeight)' : 'auto'
        }">
        <div class="col-12 data-header">
          <div *ngIf="filterConfig.isGroupBy" class="item-expand-col">
          </div>
          <ng-container *ngFor="let column of columns; let columnIndex = index">
            <div *ngIf="column.isActive && column.isVisible && column.col_type === 'check-box'" [class]="column.col">
              <mat-checkbox class="checkbox-input custom-checkbox" (change)="onGobalCheckboxChange($event)"
              [(ngModel)]="isAllTaskSelected">
              </mat-checkbox>
            </div>
            <div *ngIf="column.isActive && column.isVisible && column.col_type !== 'check-box'" [class]="column.col"
              class="d-flex">
              <div class="pr-1">
                {{column.column_name}}
              </div>
              <div *ngIf="column.isSortActive" class="svg" (click)="onClickSort(1, column.column_value_key)"
                [ngStyle]="{ fill: column.sortOrder == 1 ? '#111434' : '#B9C0CA' }" style="cursor: pointer;">
                <svg class="svg-icon" height="10px" width="8px" version="1.1" id="Layer_1" viewBox="0 0 512 512"
                  enable-background="new 0 0 512 512" xml:space="preserve">
                  <polygon points="245,0 74.3,213.3 202.3,213.3 202.3,512 287.7,512 287.7,213.3 415.7,213.3 " />
                </svg>
              </div>
              <div *ngIf="column.isSortActive" class="svg" (click)="onClickSort(2, column.column_value_key)"
                [ngStyle]="{ fill: column.sortOrder == 2 ? '#111434' : '#B9C0CA' }" style="cursor: pointer;">
                <svg class="svg-icon" height="10px" width="8px" version="1.1" id="Layer_1" viewBox="0 0 512 512"
                  enable-background="new 0 0 512 512" xml:space="preserve">
                  <polygon points="283.7,298.7 283.7,0 198.3,0 198.3,298.7 70.3,298.7 241,512 411.7,298.7 " />
                </svg>
              </div>
            </div>
          </ng-container>
        </div>
        <div class="col-12 list-body">
          <div *ngFor="let group of taskDetails | keyvalue">
            <div *ngIf="group.key !== 'noGroupBy'" class="pt-2 d-flex group-header">
              <ng-container>
                <div *ngIf="filterConfig.isGroupBy" class="item-expand-col line-item-expand mr-2"
                  (click)="toggleGroupExpansion(group)">
                  <div *ngIf="!isExpanded(group.key)" style="cursor: pointer;" matTooltip="Expand">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <mask id="mask0_13695_86195" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0"
                        width="24" height="24">
                        <rect y="24" width="24" height="24" transform="rotate(-90 0 24)" fill="#D9D9D9" />
                      </mask>
                      <g mask="url(#mask0_13695_86195)">
                        <path
                          d="M15.6922 11.9905L12.048 8.34625L11.004 9.3905L12.854 11.2405L8.298 11.2405L8.298 12.75L12.854 12.75L11.004 14.6L12.048 15.6442L15.6922 11.9905ZM21.5 11.9982C21.5 13.3122 21.2507 14.5473 20.752 15.7035C20.2533 16.8597 19.5766 17.8653 18.7218 18.7205C17.8669 19.5757 16.8617 20.2527 15.706 20.7517C14.5503 21.2506 13.3156 21.5 12.0017 21.5C10.6877 21.5 9.45267 21.2507 8.2965 20.752C7.14033 20.2533 6.13467 19.5766 5.2795 18.7217C4.42433 17.8669 3.74725 16.8617 3.24825 15.706C2.74942 14.5503 2.5 13.3156 2.5 12.0017C2.5 10.6877 2.74933 9.45267 3.248 8.2965C3.74667 7.14033 4.42342 6.13467 5.27825 5.2795C6.13308 4.42433 7.13833 3.74725 8.294 3.24825C9.44967 2.74942 10.6844 2.5 11.9982 2.5C13.3122 2.5 14.5473 2.74933 15.7035 3.248C16.8597 3.74667 17.8653 4.42342 18.7205 5.27825C19.5757 6.13308 20.2527 7.13833 20.7518 8.294C21.2506 9.44967 21.5 10.6844 21.5 11.9982ZM20 12C20 9.76667 19.225 7.875 17.675 6.325C16.125 4.775 14.2333 4 12 4C9.76667 4 7.875 4.775 6.325 6.325C4.775 7.875 4 9.76667 4 12C4 14.2333 4.775 16.125 6.325 17.675C7.875 19.225 9.76667 20 12 20C14.2333 20 16.125 19.225 17.675 17.675C19.225 16.125 20 14.2333 20 12Z"
                          fill="#7D838B" />
                      </g>
                    </svg>
                  </div>
                  <div *ngIf="isExpanded(group.key)" style="cursor: pointer;" matTooltip="collapse">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <mask id="mask0_13695_86191" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0"
                        width="24" height="24">
                        <rect width="24" height="24" fill="#D9D9D9" />
                      </mask>
                      <g mask="url(#mask0_13695_86191)">
                        <path
                          d="M12.0095 15.6923L15.6538 12.048L14.6095 11.004L12.7595 12.854V8.298H11.25V12.854L9.4 11.004L8.35575 12.048L12.0095 15.6923ZM12.0017 21.5C10.6877 21.5 9.45267 21.2507 8.2965 20.752C7.14033 20.2533 6.13467 19.5766 5.2795 18.7218C4.42433 17.8669 3.74725 16.8617 3.24825 15.706C2.74942 14.5503 2.5 13.3156 2.5 12.0017C2.5 10.6877 2.74933 9.45267 3.248 8.2965C3.74667 7.14033 4.42342 6.13467 5.27825 5.2795C6.13308 4.42433 7.13833 3.74725 8.294 3.24825C9.44967 2.74942 10.6844 2.5 11.9983 2.5C13.3123 2.5 14.5473 2.74933 15.7035 3.248C16.8597 3.74667 17.8653 4.42342 18.7205 5.27825C19.5757 6.13308 20.2528 7.13833 20.7518 8.294C21.2506 9.44967 21.5 10.6844 21.5 11.9983C21.5 13.3123 21.2507 14.5473 20.752 15.7035C20.2533 16.8597 19.5766 17.8653 18.7218 18.7205C17.8669 19.5757 16.8617 20.2528 15.706 20.7518C14.5503 21.2506 13.3156 21.5 12.0017 21.5ZM12 20C14.2333 20 16.125 19.225 17.675 17.675C19.225 16.125 20 14.2333 20 12C20 9.76667 19.225 7.875 17.675 6.325C16.125 4.775 14.2333 4 12 4C9.76667 4 7.875 4.775 6.325 6.325C4.775 7.875 4 9.76667 4 12C4 14.2333 4.775 16.125 6.325 17.675C7.875 19.225 9.76667 20 12 20Z"
                          fill="#7D838B" />
                      </g>
                    </svg>
                  </div>
                </div>
                <!-- <div class="pl-2 pt-2 custom-col line-item-check">
                  <mat-checkbox class="checkbox-input custom-checkbox">
                  </mat-checkbox>
                </div> -->
                <div class="d-flex" *ngIf="filterConfig.groupByKey === 'status_id'" [ngStyle]="{
                'background': (convertAsNumber(group.key) | showMasterData : statusDetails : 'id' : 'label_color'),
                'color': '#FFFFFF',
                'padding': '5px 10px 5px 5px',
                'border-radius': '4px 0px 0px 4px'
              }">
                  <div>
                    {{ group.key ?
                    (group.key | showMasterData : groupedTaskdetails : 'key' : 'label') : 'Unassigned' }}
                  </div>
                  <div class="pl-1">
                    {{ group.key ? '(' + (group.key | showMasterData : groupedTaskdetails : 'key' : 'count') + ')' : '-'
                    }}
                  </div>
                </div>
                <div *ngIf="filterConfig.groupByKey === 'status_id'" [ngStyle]="{
                'background': (convertAsNumber(group.key) | showMasterData : statusDetails : 'id' : 'label_color'),
                'border-top': '15px solid transparent',
                'border-bottom': '15px solid transparent',
                'border-right': '12px solid white',
                'border-radius': '0px 4px 4px 0px'
              }">
                </div>
                <div class="d-flex" *ngIf="filterConfig.groupByKey === 'priority'" [ngStyle]="{
                  'background': (convertAsNumber(group.key) | showMasterData : priorityDetails : 'id' : 'background_color'),
                  'color': (convertAsNumber(group.key) | showMasterData : priorityDetails : 'id' : 'text_color'),
                  'padding': '5px 10px 5px 5px',
                  'border-radius': '4px',
                  'margin-right': '5px'
                }">
                    <div>
                      {{ group.key ?
                      (group.key | showMasterData : groupedTaskdetails : 'key' : 'label') : 'Unassigned' }}
                    </div>
                    <div class="pl-1">
                      {{ group.key ? '(' + (group.key | showMasterData : groupedTaskdetails : 'key' : 'count') + ')' : '-'
                      }}
                    </div>
                  </div>
                <div class="d-flex" *ngIf="filterConfig.groupByKey !== 'status_id' && filterConfig.groupByKey !== 'priority'" style="    
                white-space: nowrap;
                padding-top: 7px;
                padding-right: 10px;
                font-weight: 500;
                font-size: 12px;">
                  <div>
                    {{ group.key ?
                    (group.key | showMasterData : groupedTaskdetails : 'key' : 'label') : 'Unassigned' }}
                  </div>
                  <div class="pl-1">
                    {{ group.key ? '(' + (group.key | showMasterData : groupedTaskdetails : 'key' : 'count') + ')' : '-'
                    }}
                  </div>
                </div>
                <div class="custom-dashed-line"></div>
              </ng-container>
            </div>
            <div *ngIf="group.value.length > 0 && (filterConfig.isGroupBy ? isExpanded(group.key) : true)"
              class="group-list" infinite-scroll [infiniteScrollDistance]="3" [infiniteScrollThrottle]="100"
              (scrolled)="onDataScroll(group.key)" [scrollWindow]="false">
              <ng-container *ngFor="let task of group.value let itemIndex = index">
                <div class="col-12 line-item">
                  <div *ngIf="filterConfig.isGroupBy" class="item-expand-col">
                  </div>
                  <ng-container *ngFor="let column of columns; let columnIndex = index">
                    <div *ngIf="column.isActive && column.isVisible && column.col_type === 'check-box'"
                      [class]="column.col">
                      <mat-checkbox class="checkbox-input custom-checkbox" (change)="onTaskCheckboxChange($event, task)"
                        [(ngModel)]="task.isChecked">
                      </mat-checkbox>
                    </div>
                    <div *ngIf="column.isActive && column.isVisible && column.col_type === 'text'" [class]="column.col"
                      style="cursor: pointer;" (click)="navigateToDetail(task.id)"
                      tooltip="{{task[column.column_value_key]}}">
                      {{task[column.column_value_key] | maxellipsis: 30 }}
                    </div>
                    <div *ngIf="column.isActive && column.isVisible && column.col_type === 'date'" [class]="column.col"
                      style="cursor: pointer;" tooltip="{{task[column.column_value_key]}}" cdkOverlayOrigin
                      #overlayOrigin="cdkOverlayOrigin" (click)="inlineDatePicker($event,task,column)">
                      {{task[column.column_value_key] | maxellipsis: 30 }}
                    </div>
                    <ng-template #inlineDatePickerTemplate let-task let-column="column">
                      <div class="calendar-overlay d-flex flex-column">
                        <div class="title-text">Assign {{column.column_name}}</div>

                        <div class="content-text">please select the date that you wish to modify</div>

                        <mat-calendar [(selected)]="task[column.dataKey]" [minDate]="minDate" [maxDate]="maxDate"></mat-calendar>


                        <div class="d-flex justify-content-end" style="gap: 8px; margin-top: 6px">
                          <div class="cancel-btn" (click)="closeOverlay()"
                            [ngStyle]="{ 'pointer-events': isApiInProgress ? 'none' : '' }">
                            Cancel
                          </div>
                          <div class="yes-btn" (click)="updateInlineDate(task,task[column.dataKey], column)"
                            [ngStyle]="{ 'pointer-events': isApiInProgress ? 'none' : '' }">
                            <ng-container *ngIf="!isApiInProgress; else loading">
                              Update
                            </ng-container>
                            <ng-template #loading>
                              <mat-spinner class="white-spinner" diameter="20"></mat-spinner>
                            </ng-template>
                          </div>
                        </div>
                      </div>
                    </ng-template>
                    <div *ngIf="column.isActive && column.isVisible && column.col_type === 'owner'"
                      [class]="column.col" cdkOverlayOrigin #overlayOrigin="cdkOverlayOrigin"
                      (click)="assignedToDropdown($event,task)" style="cursor: pointer;">
                      <ng-container *ngIf="task[column.column_value_key].length > 0">
                        <app-people-icon-display [peopleList]="task[column.column_value_key]"
                        [count]="task[column.column_value_key].length" [bgColor]="button">
                      </app-people-icon-display>
                      </ng-container>
                      <ng-container *ngIf="task[column.column_value_key].length == 0">-</ng-container>
                      <ng-template #assignedToDropdownTemplate let-task>
                        <div class="owner-dropdown">
                          <div class="owner-header">Assigned To</div>      
                          <div class="pt-1 pb-1">
                              <div class="date-field" style="width: 100% !important; padding: 5px !important;">
                                <div class="task-name-field">
                                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <mask id="mask0_14526_13953" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="24">
                                    <rect width="24" height="24" fill="#D9D9D9"/>
                                    </mask>
                                    <g mask="url(#mask0_14526_13953)">
                                    <path d="M19.6 21L13.3 14.7C12.8 15.1 12.225 15.4167 11.575 15.65C10.925 15.8833 10.2333 16 9.5 16C7.68333 16 6.14583 15.3708 4.8875 14.1125C3.62917 12.8542 3 11.3167 3 9.5C3 7.68333 3.62917 6.14583 4.8875 4.8875C6.14583 3.62917 7.68333 3 9.5 3C11.3167 3 12.8542 3.62917 14.1125 4.8875C15.3708 6.14583 16 7.68333 16 9.5C16 10.2333 15.8833 10.925 15.65 11.575C15.4167 12.225 15.1 12.8 14.7 13.3L21 19.6L19.6 21ZM9.5 14C10.75 14 11.8125 13.5625 12.6875 12.6875C13.5625 11.8125 14 10.75 14 9.5C14 8.25 13.5625 7.1875 12.6875 6.3125C11.8125 5.4375 10.75 5 9.5 5C8.25 5 7.1875 5.4375 6.3125 6.3125C5.4375 7.1875 5 8.25 5 9.5C5 10.75 5.4375 11.8125 6.3125 12.6875C7.1875 13.5625 8.25 14 9.5 14Z" fill="#7D838B"/>
                                    </g>
                                    </svg>                            
                                  <input matInput [placeholder]="'Find stakeholders'" [(ngModel)]="userSearchParams" (keydown.enter)="onEnterOwnerSearch(userSearchParams)" />
                                  <div (click)="clearOwnerSearch()" style="cursor: pointer;" matTooltip="Clear">
                                    <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                      <path
                                        d="M6.98636 12.1841L9.46341 9.72099L11.9405 12.1841L12.6655 11.4632L10.1885 9L12.6655 6.53684L11.9405 5.81586L9.46341 8.27901L6.98636 5.81586L6.26131 6.53684L8.73836 9L6.26131 11.4632L6.98636 12.1841ZM9.46462 15.5C8.56049 15.5 7.71067 15.3294 6.91515 14.9882C6.11963 14.647 5.42766 14.184 4.83925 13.5991C4.25083 13.0142 3.78495 12.3264 3.44161 11.5357C3.09837 10.745 2.92676 9.90014 2.92676 9.0012C2.92676 8.10214 3.09832 7.25709 3.44143 6.46603C3.78455 5.67496 4.2502 4.98688 4.83839 4.40176C5.42657 3.81665 6.11825 3.35338 6.91343 3.01196C7.70861 2.67065 8.5582 2.5 9.46221 2.5C10.3663 2.5 11.2162 2.6706 12.0117 3.01179C12.8072 3.35298 13.4992 3.81602 14.0876 4.40091C14.676 4.98579 15.1419 5.6736 15.4852 6.46432C15.8285 7.25504 16.0001 8.09986 16.0001 8.9988C16.0001 9.89786 15.8285 10.7429 15.4854 11.534C15.1423 12.325 14.6766 13.0131 14.0884 13.5982C13.5003 14.1834 12.8086 14.6466 12.0134 14.988C11.2182 15.3293 10.3686 15.5 9.46462 15.5ZM9.46341 14.4737C11.0001 14.4737 12.3017 13.9434 13.3682 12.8829C14.4347 11.8224 14.968 10.5281 14.968 9C14.968 7.47193 14.4347 6.17763 13.3682 5.11711C12.3017 4.05658 11.0001 3.52632 9.46341 3.52632C7.92672 3.52632 6.62513 4.05658 5.55862 5.11711C4.49211 6.17763 3.95886 7.47193 3.95886 9C3.95886 10.5281 4.49211 11.8224 5.55862 12.8829C6.62513 13.9434 7.92672 14.4737 9.46341 14.4737Z"
                                        fill="#7D838B" />
                                    </svg>
                                  </div>
                                </div>
                              </div>
                          </div>   
                          <div class="pt-2" style="overflow-x: auto; height: 142px;">
                              <ng-container *ngFor = "let member of stakeholdersList; let i = index">
                                  <div class="d-flex line-item">
                                    <app-people-icon-display [peopleList]="[member['associate_id']]"
                                    [count]="[member['associate_id']].length" [bgColor]="button">
                                    </app-people-icon-display>
                                    <div class="member-name" [matTooltip]="member.employee_name">
                                      {{member.employee_name}}
                                    </div>
                                    <div class="action" style="cursor: pointer;" (click)="modifyStakeholder(member)"> 
                                      <div *ngIf = "member.isSelected">
                                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                          <path d="M6.80484 9.80737L4.84863 7.85095C4.732 7.73446 4.5854 7.67481 4.40884 7.672C4.23242 7.66933 4.08316 7.72898 3.96105 7.85095C3.83909 7.97305 3.77811 8.12098 3.77811 8.29474C3.77811 8.46849 3.83909 8.61642 3.96105 8.73853L6.272 11.0495C6.42428 11.2016 6.60189 11.2777 6.80484 11.2777C7.00779 11.2777 7.1854 11.2016 7.33768 11.0495L12.0227 6.36442C12.1392 6.24779 12.1989 6.10119 12.2017 5.92463C12.2044 5.74821 12.1447 5.59895 12.0227 5.47684C11.9006 5.35488 11.7527 5.2939 11.5789 5.2939C11.4052 5.2939 11.2573 5.35488 11.1352 5.47684L6.80484 9.80737ZM8.00147 16C6.89495 16 5.85488 15.79 4.88126 15.3701C3.90765 14.9502 3.06077 14.3803 2.34063 13.6604C1.62049 12.9406 1.05032 12.094 0.630105 11.1208C0.210035 10.1476 0 9.10786 0 8.00147C0 6.89495 0.209965 5.85488 0.629895 4.88126C1.04982 3.90765 1.61972 3.06077 2.33958 2.34063C3.05944 1.62049 3.90597 1.05032 4.87916 0.630105C5.85235 0.210035 6.89214 0 7.99853 0C9.10505 0 10.1451 0.209965 11.1187 0.629894C12.0924 1.04982 12.9392 1.61972 13.6594 2.33958C14.3795 3.05944 14.9497 3.90597 15.3699 4.87916C15.79 5.85235 16 6.89214 16 7.99853C16 9.10505 15.79 10.1451 15.3701 11.1187C14.9502 12.0924 14.3803 12.9392 13.6604 13.6594C12.9406 14.3795 12.094 14.9497 11.1208 15.3699C10.1476 15.79 9.10786 16 8.00147 16Z" fill="#52C41A"/>
                                          </svg>                    
                                      </div>
                                      <div *ngIf = "!member.isSelected">
                                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                          <path d="M6.80484 9.80737L4.84863 7.85095C4.732 7.73446 4.5854 7.67481 4.40884 7.672C4.23242 7.66933 4.08316 7.72898 3.96105 7.85095C3.83909 7.97305 3.77811 8.12098 3.77811 8.29474C3.77811 8.46849 3.83909 8.61642 3.96105 8.73853L6.272 11.0495C6.42428 11.2016 6.60189 11.2777 6.80484 11.2777C7.00779 11.2777 7.1854 11.2016 7.33768 11.0495L12.0227 6.36442C12.1392 6.24779 12.1989 6.10119 12.2017 5.92463C12.2044 5.74821 12.1447 5.59895 12.0227 5.47684C11.9006 5.35488 11.7527 5.2939 11.5789 5.2939C11.4052 5.2939 11.2573 5.35488 11.1352 5.47684L6.80484 9.80737ZM8.00147 16C6.89495 16 5.85488 15.79 4.88126 15.3701C3.90765 14.9502 3.06077 14.3803 2.34063 13.6604C1.62049 12.9406 1.05032 12.094 0.630105 11.1208C0.210035 10.1476 0 9.10786 0 8.00147C0 6.89495 0.209965 5.85488 0.629895 4.88126C1.04982 3.90765 1.61972 3.06077 2.33958 2.34063C3.05944 1.62049 3.90597 1.05032 4.87916 0.630105C5.85235 0.210035 6.89214 0 7.99853 0C9.10505 0 10.1451 0.209965 11.1187 0.629894C12.0924 1.04982 12.9392 1.61972 13.6594 2.33958C14.3795 3.05944 14.9497 3.90597 15.3699 4.87916C15.79 5.85235 16 6.89214 16 7.99853C16 9.10505 15.79 10.1451 15.3701 11.1187C14.9502 12.0924 14.3803 12.9392 13.6604 13.6594C12.9406 14.3795 12.094 14.9497 11.1208 15.3699C10.1476 15.79 9.10786 16 8.00147 16Z" fill="#D4D6D8"/>
                                          </svg>                    
                                      </div>
                                    </div>
                                  </div>
                                </ng-container>
                          </div>    
                          <div class="pt-2 actions">
                              <div class="mr-2 cancel-btn" (click)="closeOwnerOverlay()">Cancel</div>
                              <div class="yes-btn" (click)="updateTaskAssignedTo(task)"
                              [ngStyle]="{ 'pointer-events': isApiInProgress ? 'none' : '' }">
                              <ng-container *ngIf="!isApiInProgress; else loading">
                                Update
                              </ng-container>
                              <ng-template #loading>
                                <mat-spinner class="white-spinner" diameter="20"></mat-spinner>
                              </ng-template>
                            </div>
                          </div>
                        </div>
                      </ng-template>
                    </div>
                    <div *ngIf="column.isActive && column.isVisible && column.col_type === 'status'"
                      [class]="column.col" cdkOverlayOrigin #overlayOrigin="cdkOverlayOrigin"
                      (click)="statusDropdown($event,task)">
                      <span class="sub-content-status" [ngStyle]="{
                      'background': (task[column.column_value_key] | showMasterData : statusDetails : 'id' : 'label_color')
                    }">
                        {{ (task[column.column_value_key] | showMasterData : statusDetails : 'id' : 'name') }}
                      </span>
                      <ng-template #statusdropdownTemplate let-task>
                        <div class="dropdown" *ngIf="statusDetails?.length > 0">
                          <ng-container *ngFor="let status of statusDetails">
                            <div class="d-flex dropdown-item" [ngStyle]="{
                                   'color': (status.id | showMasterData : statusDetails : 'id' : 'label_color')}">
                              <div class="pr-1 small-circle"
                                [ngStyle]="{
                                     'background-color': (status.id | showMasterData : statusDetails : 'id' : 'label_color')}"></div>
                              <div (click)="updateTaskStatus(status.id, task)">
                                {{ status.name }}
                              </div>
                            </div>
                          </ng-container>
                        </div>
                      </ng-template>
                    </div>
                    <div *ngIf="column.isActive && column.isVisible && column.col_type === 'priority'"
                      [class]="column.col" cdkOverlayOrigin #overlayOrigin="cdkOverlayOrigin"
                      (click)="priorityDropdown($event,task)">
                      <span class="sub-content-priority" [ngStyle]="{
                        'cursor':'pointer',
                      'background-color': task[column.column_value_key] ? 
                        (task[column.column_value_key] | showMasterData : priorityDetails : 'id' : 'background_color') : '',
                      'color': task[column.column_value_key] ? 
                        (task[column.column_value_key] | showMasterData : priorityDetails : 'id' : 'text_color') : ''
                    }">
                        {{ task[column.column_value_key] ?
                        (task[column.column_value_key] | showMasterData : priorityDetails : 'id' : 'name') : '-' }}
                      </span>
                      <ng-template #prioritydropdownTemplate let-task>
                        <div class="dropdown" *ngIf="priorityDetails.length > 0">
                          <ng-container *ngFor="let priority of priorityDetails">
                            <div class="d-flex dropdown-item" [ngStyle]="{
                                   'color': (priority.id | showMasterData : priorityDetails : 'id' : 'text_color')}">
                              <div class="pr-1 small-circle"
                                [ngStyle]="{
                                     'background-color': (priority.id | showMasterData : priorityDetails : 'id' : 'background_color')}"></div>
                              <div (click)="updateTaskPriority(priority.id, task)">
                                {{ priority.name }}
                              </div>
                            </div>
                          </ng-container>
                        </div>
                      </ng-template>
                    </div>
                    <div *ngIf="column.isActive && column.isVisible && column.col_type === 'commercial'"
                      [class]="column.col">
                      <ng-container>
                        <ng-container *ngIf="task[column.column_value_key] === 1 ; else notBillable">
                          <svg [ngStyle]="{ fill: '#515965', 'margin-bottom': '2px' }"
                            xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" height="18"
                            viewBox="0 0 24 24" width="18">
                            <g>
                              <rect fill="none" height="24" width="24" />
                            </g>
                            <g>
                              <path
                                d="M12,2C6.48,2,2,6.48,2,12s4.48,10,10,10s10-4.48,10-10S17.52,2,12,2z M12.88,17.76V19h-1.75v-1.29 c-0.74-0.18-2.39-0.77-3.02-2.96l1.65-0.67c0.06,0.22,0.58,2.09,2.4,2.09c0.93,0,1.98-0.48,1.98-1.61c0-0.96-0.7-1.46-2.28-2.03 c-1.1-0.39-3.35-1.03-3.35-3.31c0-0.1,0.01-2.4,2.62-2.96V5h1.75v1.24c1.84,0.32,2.51,1.79,2.66,2.23l-1.58,0.67 c-0.11-0.35-0.59-1.34-1.9-1.34c-0.7,0-1.81,0.37-1.81,1.39c0,0.95,0.86,1.31,2.64,1.9c2.4,0.83,3.01,2.05,3.01,3.45 C15.9,17.17,13.4,17.67,12.88,17.76z" />
                            </g>
                          </svg>
                          <span class="pl-1 icon-class">Billable</span>
                        </ng-container>

                        <ng-template #notBillable>
                          <svg [ngStyle]="{ fill: '#B9C0CA', 'margin-bottom': '2px' }"
                            xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" height="18"
                            viewBox="0 0 24 24" width="18">
                            <g>
                              <rect fill="none" height="24" width="24" />
                            </g>
                            <g>
                              <path
                                d="M12,2C6.48,2,2,6.48,2,12s4.48,10,10,10s10-4.48,10-10S17.52,2,12,2z M12.88,17.76V19h-1.75v-1.29 c-0.74-0.18-2.39-0.77-3.02-2.96l1.65-0.67c0.06,0.22,0.58,2.09,2.4,2.09c0.93,0,1.98-0.48,1.98-1.61c0-0.96-0.7-1.46-2.28-2.03 c-1.1-0.39-3.35-1.03-3.35-3.31c0-0.1,0.01-2.4,2.62-2.96V5h1.75v1.24c1.84,0.32,2.51,1.79,2.66,2.23l-1.58,0.67 c-0.11-0.35-0.59-1.34-1.9-1.34c-0.7,0-1.81,0.37-1.81,1.39c0,0.95,0.86,1.31,2.64,1.9c2.4,0.83,3.01,2.05,3.01,3.45 C15.9,17.17,13.4,17.67,12.88,17.76z" />
                            </g>
                          </svg>
                          <span class="pl-1 icon-class">Non-Billable</span>
                        </ng-template>
                      </ng-container>
                    </div>
                    <div *ngIf="column.isActive && column.isVisible && column.col_type === 'progress'"
                      [class]="column.col">
                      <div class="progress-container">
                        <div class="progress-bar-container">
                          <div class="progress-bar"
                            [ngStyle]="{'width.%': task[column.column_value_key], 'background-color': getProgressBarColor(task[column.column_value_key])}">
                          </div>
                        </div>
                        <div class="progress-label">{{task[column.column_value_key] ? task[column.column_value_key] +
                          '%' : '0%' }}</div>
                      </div>
                    </div>
                    <div *ngIf="column.isActive && column.isVisible && column.col_type === 'actions'"
                      [class]="column.col">
                      <div (click)="markAsComplete(task)" style="cursor: pointer;">
                        <div *ngIf="!task.isTaskCompleted" tooltip="Mark As Completed">
                          <svg width="18" height="18" viewBox="0 0 18 18" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <mask id="mask0_14538_151113" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0"
                              width="18" height="18">
                              <rect width="18" height="18" fill="#D9D9D9" />
                            </mask>
                            <g mask="url(#mask0_14538_151113)">
                              <path
                                d="M7.93556 10.6097L6.19331 8.86725C6.08944 8.7635 5.95888 8.71038 5.80163 8.70788C5.6445 8.7055 5.51156 8.75863 5.40281 8.86725C5.29419 8.976 5.23987 9.10775 5.23987 9.2625C5.23987 9.41725 5.29419 9.549 5.40281 9.65775L7.461 11.7159C7.59663 11.8514 7.75481 11.9192 7.93556 11.9192C8.11631 11.9192 8.2745 11.8514 8.41012 11.7159L12.5828 7.54331C12.6865 7.43944 12.7396 7.30887 12.7421 7.15162C12.7445 6.9945 12.6914 6.86156 12.5828 6.75281C12.474 6.64419 12.3422 6.58988 12.1875 6.58988C12.0328 6.58988 11.901 6.64419 11.7922 6.75281L7.93556 10.6097ZM9.00131 16.125C8.01581 16.125 7.0895 15.938 6.22237 15.564C5.35525 15.19 4.601 14.6824 3.95962 14.0413C3.31825 13.4002 2.81044 12.6463 2.43619 11.7795C2.06206 10.9128 1.875 9.98669 1.875 9.00131C1.875 8.01581 2.062 7.0895 2.436 6.22237C2.81 5.35525 3.31756 4.601 3.95869 3.95962C4.59981 3.31825 5.35375 2.81044 6.2205 2.43619C7.08725 2.06206 8.01331 1.875 8.99869 1.875C9.98419 1.875 10.9105 2.062 11.7776 2.436C12.6448 2.81 13.399 3.31756 14.0404 3.95869C14.6818 4.59981 15.1896 5.35375 15.5638 6.2205C15.9379 7.08725 16.125 8.01331 16.125 8.99869C16.125 9.98419 15.938 10.9105 15.564 11.7776C15.19 12.6448 14.6824 13.399 14.0413 14.0404C13.4002 14.6818 12.6463 15.1896 11.7795 15.5638C10.9128 15.9379 9.98669 16.125 9.00131 16.125ZM9 15C10.675 15 12.0938 14.4187 13.2563 13.2563C14.4187 12.0938 15 10.675 15 9C15 7.325 14.4187 5.90625 13.2563 4.74375C12.0938 3.58125 10.675 3 9 3C7.325 3 5.90625 3.58125 4.74375 4.74375C3.58125 5.90625 3 7.325 3 9C3 10.675 3.58125 12.0938 4.74375 13.2563C5.90625 14.4187 7.325 15 9 15Z"
                                fill="#7D838B" />
                            </g>
                          </svg>
                        </div>
                        <div *ngIf="task.isTaskCompleted" tooltip="Task Completed">
                          <svg width="18" height="18" viewBox="0 0 18 18" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <mask id="mask0_14538_151113" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0"
                              width="18" height="18">
                              <rect width="18" height="18" fill="#4DBD17" />
                            </mask>
                            <g mask="url(#mask0_14538_151113)">
                              <path
                                d="M7.93556 10.6097L6.19331 8.86725C6.08944 8.7635 5.95888 8.71038 5.80163 8.70788C5.6445 8.7055 5.51156 8.75863 5.40281 8.86725C5.29419 8.976 5.23987 9.10775 5.23987 9.2625C5.23987 9.41725 5.29419 9.549 5.40281 9.65775L7.461 11.7159C7.59663 11.8514 7.75481 11.9192 7.93556 11.9192C8.11631 11.9192 8.2745 11.8514 8.41012 11.7159L12.5828 7.54331C12.6865 7.43944 12.7396 7.30887 12.7421 7.15162C12.7445 6.9945 12.6914 6.86156 12.5828 6.75281C12.474 6.64419 12.3422 6.58988 12.1875 6.58988C12.0328 6.58988 11.901 6.64419 11.7922 6.75281L7.93556 10.6097ZM9.00131 16.125C8.01581 16.125 7.0895 15.938 6.22237 15.564C5.35525 15.19 4.601 14.6824 3.95962 14.0413C3.31825 13.4002 2.81044 12.6463 2.43619 11.7795C2.06206 10.9128 1.875 9.98669 1.875 9.00131C1.875 8.01581 2.062 7.0895 2.436 6.22237C2.81 5.35525 3.31756 4.601 3.95869 3.95962C4.59981 3.31825 5.35375 2.81044 6.2205 2.43619C7.08725 2.06206 8.01331 1.875 8.99869 1.875C9.98419 1.875 10.9105 2.062 11.7776 2.436C12.6448 2.81 13.399 3.31756 14.0404 3.95869C14.6818 4.59981 15.1896 5.35375 15.5638 6.2205C15.9379 7.08725 16.125 8.01331 16.125 8.99869C16.125 9.98419 15.938 10.9105 15.564 11.7776C15.19 12.6448 14.6824 13.399 14.0413 14.0404C13.4002 14.6818 12.6463 15.1896 11.7795 15.5638C10.9128 15.9379 9.98669 16.125 9.00131 16.125ZM9 15C10.675 15 12.0938 14.4187 13.2563 13.2563C14.4187 12.0938 15 10.675 15 9C15 7.325 14.4187 5.90625 13.2563 4.74375C12.0938 3.58125 10.675 3 9 3C7.325 3 5.90625 3.58125 4.74375 4.74375C3.58125 5.90625 3 7.325 3 9C3 10.675 3.58125 12.0938 4.74375 13.2563C5.90625 14.4187 7.325 15 9 15Z"
                                fill="#4DBD17" />
                            </g>
                          </svg>
                        </div>
                      </div>
                      <div (click)="toggleFavourite(task,group.value,itemIndex)" style="cursor: pointer;">
                        <div *ngIf="!task.isFavTask" tooltip="Mark As Favourite">
                          <svg width="18" height="18" viewBox="0 0 18 18" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <mask id="mask0_14538_151159" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0"
                              width="18" height="18">
                              <rect width="18" height="18" fill="#D9D9D9" />
                            </mask>
                            <g mask="url(#mask0_14538_151159)">
                              <path
                                d="M5.71875 4.8L7.81875 2.08125C7.96875 1.88125 8.14687 1.73438 8.35312 1.64062C8.55937 1.54688 8.775 1.5 9 1.5C9.225 1.5 9.44063 1.54688 9.64688 1.64062C9.85313 1.73438 10.0312 1.88125 10.1812 2.08125L12.2812 4.8L15.4688 5.86875C15.7937 5.96875 16.05 6.15312 16.2375 6.42188C16.425 6.69063 16.5187 6.9875 16.5187 7.3125C16.5187 7.4625 16.4969 7.6125 16.4531 7.7625C16.4094 7.9125 16.3375 8.05625 16.2375 8.19375L14.175 11.1187L14.25 14.1938C14.2625 14.6313 14.1188 15 13.8188 15.3C13.5188 15.6 13.1688 15.75 12.7688 15.75C12.7438 15.75 12.6063 15.7313 12.3563 15.6938L9 14.7563L5.64375 15.6938C5.58125 15.7188 5.5125 15.7344 5.4375 15.7406C5.3625 15.7469 5.29375 15.75 5.23125 15.75C4.83125 15.75 4.48125 15.6 4.18125 15.3C3.88125 15 3.7375 14.6313 3.75 14.1938L3.825 11.1L1.78125 8.19375C1.68125 8.05625 1.60938 7.9125 1.56562 7.7625C1.52187 7.6125 1.5 7.4625 1.5 7.3125C1.5 7 1.59063 6.70937 1.77188 6.44063C1.95312 6.17188 2.20625 5.98125 2.53125 5.86875L5.71875 4.8ZM6.6375 6.09375L3 7.29375L5.325 10.65L5.25 14.2313L9 13.2L12.75 14.25L12.675 10.65L15 7.33125L11.3625 6.09375L9 3L6.6375 6.09375Z"
                                fill="#8B95A5" />
                            </g>
                          </svg>
                        </div>
                        <div *ngIf="task.isFavTask" tooltip="Remove From Favourite">
                          <svg width="18" height="18" viewBox="0 0 18 18" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <mask id="mask0_13682_78021" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0"
                              width="18" height="18">
                              <rect width="18" height="18" fill="#D9D9D9" />
                            </mask>
                            <g mask="url(#mask0_13682_78021)">
                              <path
                                d="M5.71875 4.8L7.81875 2.08125C7.96875 1.88125 8.14687 1.73438 8.35312 1.64062C8.55937 1.54688 8.775 1.5 9 1.5C9.225 1.5 9.44063 1.54688 9.64688 1.64062C9.85313 1.73438 10.0312 1.88125 10.1812 2.08125L12.2812 4.8L15.4688 5.86875C15.7937 5.96875 16.05 6.15312 16.2375 6.42188C16.425 6.69063 16.5187 6.9875 16.5187 7.3125C16.5187 7.4625 16.4969 7.6125 16.4531 7.7625C16.4094 7.9125 16.3375 8.05625 16.2375 8.19375L14.175 11.1187L14.25 14.1938C14.2625 14.6313 14.1188 15 13.8188 15.3C13.5188 15.6 13.1688 15.75 12.7688 15.75C12.7438 15.75 12.6063 15.7313 12.3563 15.6938L9 14.7563L5.64375 15.6938C5.58125 15.7188 5.5125 15.7344 5.4375 15.7406C5.3625 15.7469 5.29375 15.75 5.23125 15.75C4.83125 15.75 4.48125 15.6 4.18125 15.3C3.88125 15 3.7375 14.6313 3.75 14.1938L3.825 11.1L1.78125 8.19375C1.68125 8.05625 1.60938 7.9125 1.56562 7.7625C1.52187 7.6125 1.5 7.4625 1.5 7.3125C1.5 7 1.59063 6.70937 1.77188 6.44063C1.95312 6.17188 2.20625 5.98125 2.53125 5.86875L5.71875 4.8Z"
                                fill="#FFBD3D" />
                            </g>
                          </svg>

                        </div>
                      </div>
                    </div>
                    <div *ngIf="column.isActive && column.isVisible && column.col_type === 'hours'" [class]="column.col"
                      tooltip="{{(task[column.column_value_key] | hoursToTime) || '0h 0m'}}">
                      {{(task[column.column_value_key] | hoursToTime) || '0h 0m'}}
                    </div>
                  </ng-container>
                </div>
              </ng-container>
            </div>
          </div>

        </div>
      </div>
    </div>
  </div>
</div>

<ng-template #triggerTaskmarkAsDoneChange>
  <div class="dropdown" *ngIf="statusDetails?.length > 0">
    <ng-container *ngFor="let status of statusDetails">
      <div class="d-flex dropdown-item" [ngStyle]="{
             'color': (status.id | showMasterData : statusDetails : 'id' : 'label_color')}">
        <div class="pr-1 small-circle" [ngStyle]="{
               'background-color': (status.id | showMasterData : statusDetails : 'id' : 'label_color')}"></div>
        <div (click)="bulkUpdateTaskStatus(status.id)">
          {{ status.name }}
        </div>
      </div>
    </ng-container>
  </div>
</ng-template>

<ng-template #triggerTaskPriorityChange>
  <div class="dropdown" *ngIf="priorityDetails.length > 0">
    <ng-container *ngFor="let priority of priorityDetails">
      <div class="d-flex dropdown-item" [ngStyle]="{
             'color': (priority.id | showMasterData : priorityDetails : 'id' : 'text_color')}">
        <div class="pr-1 small-circle"
          [ngStyle]="{
               'background-color': (priority.id | showMasterData : priorityDetails : 'id' : 'background_color')}"></div>
        <div (click)="bulkUpdateTaskPriority(priority.id)">
          {{ priority.name }}
        </div>
      </div>
    </ng-container>
  </div>
</ng-template>
<ng-template #triggerTaskassignedToChange>
  <div class="owner-dropdown">
    <div class="owner-header">Assigned To</div>      
    <div class="pt-1 pb-1">
        <div class="date-field" style="width: 100% !important; padding: 5px !important;">
          <div class="task-name-field">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <mask id="mask0_14526_13953" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="24">
              <rect width="24" height="24" fill="#D9D9D9"/>
              </mask>
              <g mask="url(#mask0_14526_13953)">
              <path d="M19.6 21L13.3 14.7C12.8 15.1 12.225 15.4167 11.575 15.65C10.925 15.8833 10.2333 16 9.5 16C7.68333 16 6.14583 15.3708 4.8875 14.1125C3.62917 12.8542 3 11.3167 3 9.5C3 7.68333 3.62917 6.14583 4.8875 4.8875C6.14583 3.62917 7.68333 3 9.5 3C11.3167 3 12.8542 3.62917 14.1125 4.8875C15.3708 6.14583 16 7.68333 16 9.5C16 10.2333 15.8833 10.925 15.65 11.575C15.4167 12.225 15.1 12.8 14.7 13.3L21 19.6L19.6 21ZM9.5 14C10.75 14 11.8125 13.5625 12.6875 12.6875C13.5625 11.8125 14 10.75 14 9.5C14 8.25 13.5625 7.1875 12.6875 6.3125C11.8125 5.4375 10.75 5 9.5 5C8.25 5 7.1875 5.4375 6.3125 6.3125C5.4375 7.1875 5 8.25 5 9.5C5 10.75 5.4375 11.8125 6.3125 12.6875C7.1875 13.5625 8.25 14 9.5 14Z" fill="#7D838B"/>
              </g>
              </svg>                            
            <input matInput [placeholder]="'Find stakeholders'" [(ngModel)]="userSearchParams" (keydown.enter)="onEnterOwnerSearch(userSearchParams)" />
            <div (click)="clearOwnerSearch()" style="cursor: pointer;" matTooltip="Clear">
              <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M6.98636 12.1841L9.46341 9.72099L11.9405 12.1841L12.6655 11.4632L10.1885 9L12.6655 6.53684L11.9405 5.81586L9.46341 8.27901L6.98636 5.81586L6.26131 6.53684L8.73836 9L6.26131 11.4632L6.98636 12.1841ZM9.46462 15.5C8.56049 15.5 7.71067 15.3294 6.91515 14.9882C6.11963 14.647 5.42766 14.184 4.83925 13.5991C4.25083 13.0142 3.78495 12.3264 3.44161 11.5357C3.09837 10.745 2.92676 9.90014 2.92676 9.0012C2.92676 8.10214 3.09832 7.25709 3.44143 6.46603C3.78455 5.67496 4.2502 4.98688 4.83839 4.40176C5.42657 3.81665 6.11825 3.35338 6.91343 3.01196C7.70861 2.67065 8.5582 2.5 9.46221 2.5C10.3663 2.5 11.2162 2.6706 12.0117 3.01179C12.8072 3.35298 13.4992 3.81602 14.0876 4.40091C14.676 4.98579 15.1419 5.6736 15.4852 6.46432C15.8285 7.25504 16.0001 8.09986 16.0001 8.9988C16.0001 9.89786 15.8285 10.7429 15.4854 11.534C15.1423 12.325 14.6766 13.0131 14.0884 13.5982C13.5003 14.1834 12.8086 14.6466 12.0134 14.988C11.2182 15.3293 10.3686 15.5 9.46462 15.5ZM9.46341 14.4737C11.0001 14.4737 12.3017 13.9434 13.3682 12.8829C14.4347 11.8224 14.968 10.5281 14.968 9C14.968 7.47193 14.4347 6.17763 13.3682 5.11711C12.3017 4.05658 11.0001 3.52632 9.46341 3.52632C7.92672 3.52632 6.62513 4.05658 5.55862 5.11711C4.49211 6.17763 3.95886 7.47193 3.95886 9C3.95886 10.5281 4.49211 11.8224 5.55862 12.8829C6.62513 13.9434 7.92672 14.4737 9.46341 14.4737Z"
                  fill="#7D838B" />
              </svg>
            </div>
          </div>
        </div>
    </div>   
    <div class="pt-2" style="overflow-x: auto; height: 142px;">
        <ng-container *ngFor = "let member of stakeholdersList; let i = index">
            <div class="d-flex line-item">
              <app-people-icon-display [peopleList]="[member['associate_id']]"
              [count]="[member['associate_id']].length" [bgColor]="button">
              </app-people-icon-display>
              <div class="member-name" [matTooltip]="member.employee_name">
                {{member.employee_name}}
              </div>
              <div class="action" style="cursor: pointer;" (click)="modifyStakeholder(member)"> 
                <div *ngIf = "member.isSelected">
                  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M6.80484 9.80737L4.84863 7.85095C4.732 7.73446 4.5854 7.67481 4.40884 7.672C4.23242 7.66933 4.08316 7.72898 3.96105 7.85095C3.83909 7.97305 3.77811 8.12098 3.77811 8.29474C3.77811 8.46849 3.83909 8.61642 3.96105 8.73853L6.272 11.0495C6.42428 11.2016 6.60189 11.2777 6.80484 11.2777C7.00779 11.2777 7.1854 11.2016 7.33768 11.0495L12.0227 6.36442C12.1392 6.24779 12.1989 6.10119 12.2017 5.92463C12.2044 5.74821 12.1447 5.59895 12.0227 5.47684C11.9006 5.35488 11.7527 5.2939 11.5789 5.2939C11.4052 5.2939 11.2573 5.35488 11.1352 5.47684L6.80484 9.80737ZM8.00147 16C6.89495 16 5.85488 15.79 4.88126 15.3701C3.90765 14.9502 3.06077 14.3803 2.34063 13.6604C1.62049 12.9406 1.05032 12.094 0.630105 11.1208C0.210035 10.1476 0 9.10786 0 8.00147C0 6.89495 0.209965 5.85488 0.629895 4.88126C1.04982 3.90765 1.61972 3.06077 2.33958 2.34063C3.05944 1.62049 3.90597 1.05032 4.87916 0.630105C5.85235 0.210035 6.89214 0 7.99853 0C9.10505 0 10.1451 0.209965 11.1187 0.629894C12.0924 1.04982 12.9392 1.61972 13.6594 2.33958C14.3795 3.05944 14.9497 3.90597 15.3699 4.87916C15.79 5.85235 16 6.89214 16 7.99853C16 9.10505 15.79 10.1451 15.3701 11.1187C14.9502 12.0924 14.3803 12.9392 13.6604 13.6594C12.9406 14.3795 12.094 14.9497 11.1208 15.3699C10.1476 15.79 9.10786 16 8.00147 16Z" fill="#52C41A"/>
                    </svg>                    
                </div>
                <div *ngIf = "!member.isSelected">
                  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M6.80484 9.80737L4.84863 7.85095C4.732 7.73446 4.5854 7.67481 4.40884 7.672C4.23242 7.66933 4.08316 7.72898 3.96105 7.85095C3.83909 7.97305 3.77811 8.12098 3.77811 8.29474C3.77811 8.46849 3.83909 8.61642 3.96105 8.73853L6.272 11.0495C6.42428 11.2016 6.60189 11.2777 6.80484 11.2777C7.00779 11.2777 7.1854 11.2016 7.33768 11.0495L12.0227 6.36442C12.1392 6.24779 12.1989 6.10119 12.2017 5.92463C12.2044 5.74821 12.1447 5.59895 12.0227 5.47684C11.9006 5.35488 11.7527 5.2939 11.5789 5.2939C11.4052 5.2939 11.2573 5.35488 11.1352 5.47684L6.80484 9.80737ZM8.00147 16C6.89495 16 5.85488 15.79 4.88126 15.3701C3.90765 14.9502 3.06077 14.3803 2.34063 13.6604C1.62049 12.9406 1.05032 12.094 0.630105 11.1208C0.210035 10.1476 0 9.10786 0 8.00147C0 6.89495 0.209965 5.85488 0.629895 4.88126C1.04982 3.90765 1.61972 3.06077 2.33958 2.34063C3.05944 1.62049 3.90597 1.05032 4.87916 0.630105C5.85235 0.210035 6.89214 0 7.99853 0C9.10505 0 10.1451 0.209965 11.1187 0.629894C12.0924 1.04982 12.9392 1.61972 13.6594 2.33958C14.3795 3.05944 14.9497 3.90597 15.3699 4.87916C15.79 5.85235 16 6.89214 16 7.99853C16 9.10505 15.79 10.1451 15.3701 11.1187C14.9502 12.0924 14.3803 12.9392 13.6604 13.6594C12.9406 14.3795 12.094 14.9497 11.1208 15.3699C10.1476 15.79 9.10786 16 8.00147 16Z" fill="#D4D6D8"/>
                    </svg>                    
                </div>
              </div>
            </div>
          </ng-container>
    </div>    
    <div class="pt-2 actions">
        <div class="mr-2 cancel-btn" (click)="closeOwnerOverlay()">Cancel</div>
        <div class="yes-btn" (click)="bulkUpdateTaskAssignedTo()"
        [ngStyle]="{ 'pointer-events': isApiInProgress ? 'none' : '' }">
        <ng-container *ngIf="!isApiInProgress; else loading">
          Update
        </ng-container>
        <ng-template #loading>
          <mat-spinner class="white-spinner" diameter="20"></mat-spinner>
        </ng-template>
      </div>
    </div>
  </div>
</ng-template>
<ng-template #triggerTaskAssignDueDateChange>
  <div class="calendar-overlay d-flex flex-column">
    <div class="title-text">Assign Due Date</div>

    <div class="content-text">please select the date that you wish to modify</div>

    <mat-calendar [(selected)]="selectedDueDate" [minDate]="minDate" [maxDate]="maxDate"></mat-calendar>


    <div class="d-flex justify-content-end" style="gap: 8px; margin-top: 6px">
      <div class="cancel-btn" (click)="closeOverlay()"
        [ngStyle]="{ 'pointer-events': isApiInProgress ? 'none' : '' }">
        Cancel
      </div>
      <div class="yes-btn" (click)="bulkUpdateTaskDueDate()"
        [ngStyle]="{ 'pointer-events': isApiInProgress ? 'none' : '' }">
        <ng-container *ngIf="!isApiInProgress; else loading">
          Update
        </ng-container>
        <ng-template #loading>
          <mat-spinner class="white-spinner" diameter="20"></mat-spinner>
        </ng-template>
      </div>
    </div>
  </div>
</ng-template>