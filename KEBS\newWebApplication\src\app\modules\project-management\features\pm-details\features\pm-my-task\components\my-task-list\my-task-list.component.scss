.task-list-view {
  height: var(--dynamicMyTaskHeight);

  .loading-img {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: white;
    height: var(--myTaskSubHeight);

    .load-img img {
      width: 40px;
      /* Adjust the size as needed */
      height: auto;
      /* Maintain aspect ratio */
      margin-bottom: 10px;
      /* Space between GIF and loading text */
    }

    .loading-wrapper {
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .loading {
      color: rgba(0, 0, 0, 0.3);
      font-size: 14px;
      text-align: center;
      /* Center the text */
    }

    .loading::before {
      content: "Loading...";
      position: absolute;
      overflow: hidden;
      max-width: 7em;
      white-space: nowrap;
      background: linear-gradient(270deg,
          var(--myTasktoggleButton) 0%,
          var(--myTasktoggleButton) 105.29%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      animation: loading 6s linear infinite;
      font-size: 14px;
    }

    @keyframes loading {
      0% {
        max-width: 0;
      }
    }

  }

  .task-action-header {
    display: flex;
    flex-direction: row;
    gap: 15px;
    justify-content: flex-end;
    margin-top: -26px;
    margin-right: 10px;

    .my-task-search {
      .header-icon {
        padding-top: 6px;
        margin-right: -8px;
        cursor: pointer;
      }
    }

    .toggle-group {
      .mat-button-toggle-group {
        margin-top: -5px;
      }

      .toggle-button-icon-1 {
        font-size: 18px;
        width: 12px;
        height: 20px;
        margin-left: -5px;
      }

      .toggle-button-icon-2 {
        font-size: 18px;
        width: 12px;
        height: 20px;
        margin-left: -5px;
      }
    }

    .mat-button-toggle-checked {
      background: var(--myTasktoggleButtonBg) !important;
      color: var(--myTasktoggleButton) !important;
    }

    .mat-button-toggle-appearance-standard {
      background: transparent;
      color: #5f6c81;
    }
  }

  .task-header {
    display: flex;
    gap: 8px;
    padding: 10px 6px 6px 5px;
    font-family: var(--myTaskFont);
    font-weight: 500;
    color: #000000;

    .task-header-count {
      background-color: var(--myTaskButton);
      color: #fff;
      border-radius: 12px;
      padding: 0 10px;
      font-size: 12px;
      font-weight: 500;
    }
  }

  .my-task-summary-detailed {
    border: 1px solid #DADCE2;
    border-radius: 4px;
    box-shadow: 0px 3px 3px 0px #0000001F;
    background: #FFFFFF;
    width: var(--dynamicMyTaskWidth);
    padding: 0px 16px;
    height: 58px;
  
    .selected-text {
      font-family: var(--myTaskFont);
      font-size: 13px;
      font-weight: 700;
      color: #1b2140;
    }
  
    .summmaryData-text {
      display: flex;
      flex-direction: column;
      align-items: center;
    }
  
    .summmaryData-count {
      font-family: var(--myTaskFont);
      color: #111434;
      font-size: 13px;
      font-weight: 500;
    }
  
    .summmaryData-label {
      color: #7D838B;
      font-size: 11px;
      font-weight: 400;
      font-family: var(--myTaskFont);
    }
  
    .divider {
      width: 1px;
      height: 40px;
      background: #DADCE2;
    }
  
    .svg {
      cursor: pointer;
    }
  
    .more-text {
      font-family: var(--myTaskFont);
      font-size: 13px;
      font-weight: 500;
      color: #1b2140;
    }
  
    .layout {
      cursor: pointer;
      gap: 8px;
      border-radius: 4px;
      padding: 4px 8px;
  
      .inner-color-circle {
        padding: 5px;
        height: 40px;
        width: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
      }
  
      .layout-text {
        font-family: var(--myTaskFont);
        font-size: 13px;
        font-weight: 700;
      }
    }
  }
  
  .summary-config-list {
    gap: 8px;
  }

  .task-list {
    display: flex;
    flex-direction: column;
    background-color: white;

    .loading-img {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background-color: white;
      height: var(--myTaskSubHeight);

      .load-img img {
        width: 40px;
        /* Adjust the size as needed */
        height: auto;
        /* Maintain aspect ratio */
        margin-bottom: 10px;
        /* Space between GIF and loading text */
      }

      .loading-wrapper {
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .loading {
        color: rgba(0, 0, 0, 0.3);
        font-size: 14px;
        text-align: center;
        /* Center the text */
      }

      .loading::before {
        content: "Loading...";
        position: absolute;
        overflow: hidden;
        max-width: 7em;
        white-space: nowrap;
        background: linear-gradient(270deg,
            var(--myTasktoggleButton) 0%,
            var(--myTasktoggleButton) 105.29%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        animation: loading 6s linear infinite;
        font-size: 14px;
      }

      @keyframes loading {
        0% {
          max-width: 0;
        }
      }

    }

    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      overflow: hidden;
      height: var(--myTaskSubHeight);

      .empty-title {
        font-family: var(--myTaskFont);
        font-size: 14px;
        font-weight: 500;
        color: #111434;
        margin-bottom: 4px;
      }
      .empty-body{
        font-family: var(--myTaskFont);
        font-size: 14px;
        font-weight: 400;
        color: #8B95A5;
        margin-bottom: 4px;
      }
    }

    .data-list {
      overflow-x: hidden;
      font-size: 12px;
      font-family: var(--myTaskFont);
      letter-spacing: 2%;
      line-height: 18.23px;
      text-transform: capitalize;
      display: flex;
      flex-direction: column;
      width: 100%;

      .data-header {
        display: flex;
        align-items: center;
        padding: 5px 10px 5px 0px;
        color: #b9c0ca;
        max-height: 40px;
        position: sticky;
        top: 0;
        z-index: 1;
        background-color: #fff;
        font-size: 11px;

        .line-item-actions {
          display: flex;
          flex-direction: row;
          align-items: center;
          padding: unset;
          background: white;
          padding: 10px 0px 10px 0px;
        }

        .line-progress-actions {
          display: flex;
          flex-direction: row;
          align-items: center;
          padding: unset;
          background: white;
          padding: 10px 0px 10px 0px;
          border-top: 1px solid;
          border-bottom: 1px solid #E8E9EE;
          border-top: 1px solid #E8E9EE;
        }

        .svg-icon {
          padding-bottom: 2px !important;
        }
      }

      .checkbox-input {
        cursor: pointer;
      }

      ::ng-deep .custom-checkbox .mat-checkbox-frame {
        border: 1px solid #DADCE2 !important;
        /* Default border color */
        border-radius: 4px !important;
        /* Custom border radius */
      }

      ::ng-deep .custom-checkbox.mat-checkbox-checked .mat-checkbox-background {
        background-color: var(--myTaskButton) !important;
        /* Background color when selected */
        border-radius: 4px !important;
        /* Rounded corners when checked */
      }

      ::ng-deep .custom-checkbox.mat-checkbox-checked .mat-checkbox-frame {
        border: 1px solid var(--myTaskButton) !important;
        /* Border color changes to red when selected */
      }


      .custom-col {
        flex: 0 0 3%;
        max-width: 3%;
      }

      .item-expand-col{
        flex: 0 0 2%;
        max-width: 2%;
        padding: 5px 0px 0px 0px;
      }

      .currency-col {
        flex: 0 0 10%;
        max-width: 10%;
      }

      .task-name-col {
        flex: 0 0 12%;
        max-width: 12%;
      }

      .item-date-col {
        flex: 0 0 9%;
        max-width: 9%;
      }

      .item-id-col {
        flex: 0 0 5%;
        max-width: 5%;
      }

      .item-actions-col {
        flex: 0 0 7%;
        max-width: 7%;
      }

      .item-status-col {
        flex: 0 0 10%;
        max-width: 10%;
      }

      .item-progress-col {
        flex: 0 0 8%;
        max-width: 8%;
      }

      .item-assign-col {
        flex: 0 0 7%;
        max-width: 7%;
      }

      .list-body {
        display: flex;
        flex-direction: column;
        padding: unset;
        color: #45546E;
        box-sizing: border-box;

        .group-header{
          .custom-dashed-line {
            height: 0; /* No visible height */
            width: 100%;
            margin: 17px 0;
            position: relative; /* Set position relative for pseudo-elements */
          }
        
          .custom-dashed-line::after {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background-image: linear-gradient(to right, #515965 30%, transparent 0%);
            background-size: 17px 1px;
            background-repeat: repeat-x;
          }           
        }

        .group-list{
          max-height: var(--myTaskListContent);
          overflow-y: scroll;
          box-sizing: content-box;
          overflow-x: hidden;
          .line-item {
            display: flex;
            padding: 5px 0px 5px 0px;
            align-items: center;
            
            .line-item-expand{
              display: flex;
              justify-content: start;
              height: 35px;
              padding: 5px 0px 5px 0px;
            }
  
            .line-item-check {
              display: flex;
              justify-content: start;
              height: 35px;
              border-bottom: 1px solid #E8E9EE;
              padding: 5px 0px 5px 0px;
  
              .checkbox-input {
                cursor: pointer;
              }
            }
  
            .line-item-text {
              overflow: hidden;
              text-overflow: ellipsis;
              text-wrap: nowrap;
              height: 35px;
              border-bottom: 1px solid #E8E9EE;
              padding: 5px 0px 5px 0px;
            }
  
            .line-item-type {
              width: max-content;
              font-family: var(--myTaskFont) !important;
              height: 24px;
              background: #FFBD3D;
              border-radius: 9999px;
              justify-content: center;
              align-items: center;
              align-content: center;
              text-align: center;
              padding-left: 10px;
              padding-right: 10px;
              color: white;
              padding: 4px 8px 4px 8px;
              font-size: 12px;
              margin-top: -4px;
            }
  
            .line-item-status {
              width: max-content;
              padding: 1px 8px 1px 8px;
              color: white;
              cursor: pointer;
              display: flex;
              align-items: center;
              height: 35px;
              border-bottom: 1px solid #E8E9EE;
              padding: 5px 0px 5px 0px;

              .sub-content-status {
                font-family: var(--myTaskFont);
                font-size: 11px;
                font-weight: 500;
                text-overflow: ellipsis;
                white-space: nowrap;
                overflow: hidden;
                text-align: center;
                color: #fff;
                padding: 2px 8px;
                border-radius: 10px;
                width: 80px;
                margin-bottom: 10px;
              }
  
              .item-status-text {
                // width: 55px;
                height: 14px;
                font-family: var(--milestoneFont) !important;
                font-size: 12px !important;
                font-weight: 400;
                line-height: 14px;
                letter-spacing: 0.02em;
                text-align: center;
                color: #FF3A46;
                width: max-content;
              }
  
              .column-list {
                display: flex;
                flex-direction: column;
                // gap: 10px;
                // margin-top: 10px;
                // margin-left: 10px;
              }
  
              .column-item {
                display: flex;
                align-items: center;
              }
  
              .pop-up {
                height: 90px;
                width: 150px;
                font-size: 13px;
                padding: 18px;
                border-radius: 10px !important;
                position: absolute !important;
                background-color: white !important;
                z-index: 1 !important;
                transition: box-shadow 200ms cubic-bezier(0, 0, 0.2, 1) !important;
                box-shadow: 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12) !important;
                margin-top: -91px;
                margin-left: 40px;
              }
            }
  
            .item-currency-text {
              overflow: hidden;
              height: 35px;
              border-bottom: 1px solid #E8E9EE;
              padding: 5px 0px 5px 0px;
            }
  
            .line-item-actions {
              display: flex;
              flex-direction: row;
              align-items: center;
              padding: unset;
              height: 35px;
              border-bottom: 1px solid #E8E9EE;
              padding: 5px 0px 9px 0px;
              gap: 5px;
            }
  
            .line-item-date {
              height: 35px;
              border-bottom: 1px solid #E8E9EE;
              padding: 5px 0px 5px 0px;
              .sub-content-priority{
                font-family: var(--myTaskFont);
                font-size: 12px;
                font-weight: 500;
                text-overflow: ellipsis;
                white-space: nowrap;
                overflow: hidden;
                text-align: center;
                padding: 4px 8px;
                border-radius: 4px;
              }
            }

            .line-item-progress {
              height: 35px;
              border-bottom: 1px solid #E8E9EE;
              padding: 5px 0px 5px 0px;

              .progress-container {
                position: relative;
                display: flex;
                width: 90%;
                gap: 5px;
                align-items: center;
                justify-content: center;
              }
              
              .progress-bar-container {
                border-radius: 50px;
                background-color: #E8E9EE;
                width: 100%;
                height: 6px;
              }
              
              .progress-bar {
                height: 100%;
                border-radius: 50px;
                transition: width 0.3s ease-in-out;
              }
              
              .progress-label {
                white-space: nowrap;
                font-size: 10px;
                color: #1B2140;
                font-weight: 400;
              }
            }
          }
        }
      }
    }
  }

  .my-task-bulk-edit {
    position: absolute;
    border: 2px solid #e8e9ee;
    border-radius: 8px;
    box-shadow: 0px 4px 4px 0px #00000040;
    background: #fafcff;
    width: var(--dynamicMyTaskWidth);
    padding: 0px 16px;
    height: 45px;
    z-index: 100;
    top: 52px;
  
    .selected-text {
      font-family: var(--myTaskFont);
      font-size: 13px;
      font-weight: 700;
      color: #1b2140;
    }
  
    .divider {
      width: 2px;
      height: 25px;
      background: #e8e9ee;
    }
  
    .svg {
      cursor: pointer;
    }
  
    .more-text {
      font-family: var(--myTaskFont);
      font-size: 13px;
      font-weight: 500;
      color: #1b2140;
    }
  
    .layout {
      cursor: pointer;
      gap: 8px;
      border-radius: 8px;
      padding: 4px 8px;
  
      .layout-text {
        font-family: var(--myTaskFont);
        font-size: 13px;
        font-weight: 700;
      }
    }
  }

}


.bg-container {
  width: 328px;
  margin-top: -4px;
  margin-right: 10px;
  border: 1px solid #b9c0ca;
  border-radius: 4px;
  background-color: white;
  padding: 8px 8px 8px 8px;

  .svg-icon {
    margin-right: 5px;
  }

  .search-bar {
    width: 100%;

    input {
      height: 100%;
      width: 95%;
      font-family: var(--logFont);
      font-size: 11px;
      font-weight: 400;
      color: #5F6C81;
      ;
      outline: none;
      border: none;
    }

    input::placeholder {
      font-family: var(--logFont);
      font-size: 11px;
      font-weight: 400;
      color: #B9C0CA;
    }
  }

  .divider {
    height: 1px;
    width: 100%;
    background: #dadce2;
    margin-bottom: 8px;
    margin-top: 8px;
  }

  .search-text-list {
    cursor: pointer;
    gap: 8px;
    width: fit-content;
    margin-bottom: 4px;
  }

  .recent-search-title {
    font-family: var(--logFont);
    font-size: 11px;
    font-style: italic;
    font-weight: 400;
    color: #b9c0ca;
  }

  .recent-search-text {
    font-family: var(--logFont);
    font-size: 11px;
    color: #8b95a5;
  }
}


/* Base styles for the toggle switch container */
.toggle-switch {
  display: inline-block;
  position: relative;
  width: 30px;
  height: 17px;
  //background-color: #ccc;
  border-radius: 15px;
  cursor: pointer;
  margin-right: 10px !important;
}

.grayed-out-toggle {
  display: inline-block;
  position: relative;
  width: 30px;
  height: 17px;
  //background-color: #ccc;
  border-radius: 15px;
  background-color: grey;
  margin-right: 10px !important;
}

.column-config-popup {
  width: 210px;
  padding: 20px;
  height: 51vh;
  overflow: auto;
}

.auto-adapt {
  width: max-content;
  padding: 20px;
  height: auto;
}

/* Hide the default checkbox input */
.toggle-switch input[type="checkbox"] {
  display: none;
}

/* Style for the slider (the rounded switch) */
.slider {
  position: absolute;
  top: 2px;
  left: 2px;
  width: 12px;
  height: 12px;
  background-color: #fff;
  border-radius: 50%;
  transition: transform 0.3s;
}

/* Apply styles when the checkbox is checked */
.toggle-switch input[type="checkbox"]:checked+.slider {
  transform: translateX(13px);
}

.toggle-switch input[type="checkbox"]:not(:checked)+.slider {
  transform: translateX(0);
}

.column-header {
  color: var(--blue-grey-80, #5F6C81);
  font-family: var(--myTaskFont) !important;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 16px;
  letter-spacing: 0.22px;
  text-transform: capitalize;
}

.column-list {
  justify-content: space-between;
  height: 28px;
}

.dropdown {
  background-color: white;
  padding: 5px;
  border: 1.5px solid #B9C0CA;
  border-radius: 8px;
  font-size: 12px;
  font-family: var(--myTaskFont);
}

.dropdown-item {
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
  color: #526179;
  font-size: 12px;
  font-weight: 400;
  padding: 4px 12px 4px 12px;
  width: 120px;
  height: 32px;
  .small-circle {
    width: 6px; 
    height: 6px;
    background-color: #515965;  
    border-radius: 50%; 
    display: inline-block;
    margin: 7px 5px 0px 0px;
  }
}

.dropdown-item:last-child {
  border-bottom: none;
}

.dropdown-item:hover {
  color: var(--myTaskButton);
  background-color: white;
}

.dropdown-item-selected {
  color: var(--myTaskButton);
  background-color: white;
}

.dropdown-cluster {
  gap: 5px;
}

.calendar-overlay {
  width: 270px;
  height: 375px;
  background-color: #FFFFFF;
  border-radius: 8px;
  padding: 15px 10px;
  border: 1px solid #E8E9EE;

  .title-text {
    font-family: var(--myTaskFont);
    font-size: 13px;
    font-weight: 700;
    color: #111434;
    margin-bottom: 4px;
  }

  .sub-title-text {
    font-family: var(--myTaskFont);
    font-size: 11px;
    font-weight: 400;
    color: #C5C5C5;
    text-align: justify;
  }

  .content-text {
    font-family: var(--myTaskFont);
    font-size: 11px;
    font-weight: 400;
    color: #C5C5C5;
    text-align: justify;
  }

  .cancel-btn {
    border: 1px solid #45546e;
    border-radius: 8px;
    padding: 2px 10px;
    font-family: var(--myTaskFont);
    font-size: 11px;
    font-weight: 700;
    color: #45546e;
    cursor: pointer;
  }

  .yes-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(270deg, var(--myTasktoggleButton) 0%, var(--myTasktoggleButton) 105.29%);
    border-radius: 8px;
    padding: 0px 10px;
    font-family: var(--myTaskFont);
    font-size: 11px;
    font-weight: 700;
    color: #ffffff;
    cursor: pointer;
  }

  ::ng-deep .white-spinner circle {
    stroke: #fff !important;
  }

  ::ng-deep .mat-calendar-header {
    padding: 0px !important;
  }

  ::ng-deep .mat-calendar-content {
    padding: 0px 13px 0px 13px !important;
  }

  ::ng-deep .mat-calendar-body-selected{
    background-color: var(--myTaskButton);
  }

  ::ng-deep .mat-button,
  .mat-icon-button,
  .mat-stroked-button,
  .mat-flat-button {
    padding: 0px 20px !important;
    font-size: 13px !important;
  }

  ::ng-deep .mat-calendar-controls {
    margin: 0px !important;
  }

}

.task-list-view .task-list .data-list .list-body::-webkit-scrollbar {
  width: 5px !important;
  height: 8px !important;
}

.task-list-view .task-list .data-list .list-body .group-list::-webkit-scrollbar {
  width: var(--groupListScrollBarWidth) !important;
  height: 8px !important;
}

.task-list-view .task-list .data-list .list-body::-webkit-scrollbar-thumb {
  height: 3px !important;
}

.task-list-view .task-list .data-list .list-body .group-list::-webkit-scrollbar-thumb {
  height: 3px !important;
}

.owner-dropdown ::-webkit-scrollbar{
  width: 4px !important;
}

.owner-dropdown{
  background-color: white;
  padding: 10px;
  border: 1.5px solid #B9C0CA;
  border-radius: 8px;
  font-size: 12px;
  font-family: var(--myTaskFont);
  width: 250px;
  height: 270px;

  .owner-header{
    font-size: 14px;
    font-weight: 700;
    line-height: 24px;
    letter-spacing: 0.02em;
    text-align: left;
    color: #111434;
    }
    
    .task-name-field{
      width: 100%;
      display: flex;
    
      input {
        height: 100%;
        width: 98%;
        font-family: var(--myTaskFont);
        font-size: 10px;
        font-weight: 400;
        color: #45546E;
        ;
        outline: none;
        border: none;
        padding-top: 2px;
      }
      input::placeholder {
        font-family: var(--myTaskFont);
        font-size: 10px;
        font-weight: 400;
        color: #B9C0CA;
      }
    }
  
    .date-field{
      border: 1px solid #B9C0CA;
      max-width: 300px;
      padding: 3px;
      border-radius: 4px;
      display: flex;
      font-size: 10px;
    }  
  
    .line-item{
      width: 100%;
      border-bottom: 1px solid #DADCE2;
      padding: 5px;
  
      .member-name{
        font-size: 12px;
        font-weight: 400;
        font-family: var(--myTaskFont);
        color: #1B2140;
        width: 90%;
        padding: 2px 0px 0px 5px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
    } 
  
    .actions{
      display: flex;
      justify-content: flex-end;
    }
  
    .cancel-btn {
      border: 1px solid #45546e;
      border-radius: 8px;
      padding: 2px 10px;
      font-family: var(--myTaskFont);
      font-size: 11px;
      font-weight: 700;
      color: #45546e;
      cursor: pointer;
    }
  
    .yes-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(270deg, var(--myTasktoggleButton) 0%, var(--myTasktoggleButton) 105.29%);
      border-radius: 8px;
      padding: 0px 10px;
      font-family: var(--myTaskFont);
      font-size: 11px;
      font-weight: 700;
      color: #ffffff;
      cursor: pointer;
    }

    ::ng-deep .white-spinner circle {
      stroke: #fff !important;
    }
}

.customization-overlay {
  display: flex;
  flex-direction: column;
  border: 1px solid #b9c0ca;
  border-radius: 8px;
  padding: 10px;
  width: 230px;
  gap: 8px;
  background-color: white;

  .title-text {
    font-family: var(--myTaskFont);
    font-size: 12px;
    font-weight: 400;
    color: #b9c0ca;
  }

  .apply-btn {
    font-family: var(--myTaskFont);
    font-size: 12px;
    font-weight: 700;
    padding: 4px 8px;
    border-radius: 4px;
    background: linear-gradient(270deg, var(--myTaskButton) 0%, var(--myTaskButton) 105.29%);
    cursor: pointer;
    color: white;
  }

  .checkbox {
    height: 16px;
    width: 16px;
    margin-right: 8px;

    ::ng-deep .mat-checkbox-checked.mat-accent .mat-checkbox-background {
      background-color: var(--myTaskButton) !important;
    }

    ::ng-deep .mat-checkbox-frame {
      border-color: #b9c0ca !important;
    }
  }

  .checkbox-disabled {
    height: 16px;
    width: 16px;
    margin-right: 8px;

    ::ng-deep .mat-checkbox-disabled .mat-checkbox-background {
      background-color: #b9c0ca !important;
    }

    ::ng-deep .mat-checkbox-frame {
      border-color: #b9c0ca !important;
    }
  }

  .text-default-visible {
    font-family: var(--myTaskFont);
    font-size: 12px;
    font-weight: 400;
    color: #b9c0ca;
  }

  .text-not-default-visible {
    font-family: var(--myTaskFont);
    font-size: 12px;
    font-weight: 400;
    color: #45546E;
  }

  .icon {
    width: 16px;
    height: 16px;
    font-size: 16px;
    color: #d4d6d8;
    cursor: move;
  }

  .summary-config-list {
    gap: 8px;
    max-height: 300px;
  }
}