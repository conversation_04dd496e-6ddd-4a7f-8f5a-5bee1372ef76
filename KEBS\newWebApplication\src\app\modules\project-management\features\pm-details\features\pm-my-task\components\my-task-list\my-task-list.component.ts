import {
  <PERSON>mpo<PERSON>,
  OnIni<PERSON>,
  HostListener,
  ViewContainerRef,
  ElementRef,
  TemplateRef,
  ViewChild,
  ChangeDetectorRef
} from '@angular/core';
import * as _ from "underscore";
import * as moment from 'moment';
import { ActivatedRoute, Router } from "@angular/router";
import { FormBuilder, FormControl, FormGroup } from "@angular/forms";
import { Overlay, OverlayConfig, OverlayRef } from "@angular/cdk/overlay";
import { CdkDragDrop, moveItemInArray } from "@angular/cdk/drag-drop";
import { TemplatePortal } from "@angular/cdk/portal";
import { LoginService } from "src/app/services/login/login.service";
import { PmMasterService } from "src/app/modules/project-management/services/pm-master.service";
import { PmBillingService } from 'src/app/modules/project-management/features/pm-details/features/pm-billing/services/pm-billing.service'
import { PmAuthorizationService } from "src/app/modules/project-management/services/pm-authorization.service";
import { MyTaskService } from "./../../services/my-task.service";
import { ToasterMessageService } from 'src/app/modules/project-management/shared-lazy-loaded/components/toaster-message/toaster-message.service';
import { MatCheckboxChange } from '@angular/material/checkbox';
import { utcThursday } from 'd3';

@Component({
  selector: 'app-my-task-list',
  templateUrl: './my-task-list.component.html',
  styleUrls: ['./my-task-list.component.scss']
})
export class MyTaskListComponent implements OnInit {

  projectId: any;
  itemId: any;
  formconfig: any;
  button: any;
  fontStyle: any;
  tabColor: any;
  fieldOutline: any;
  toggleButton: any;
  toggleButtonBg: any;
  columnListData: any = [];
  customizationData: any[] = [];
  columnCustomForms: FormGroup;
  private overlayRef: OverlayRef | null;
  dataloading: boolean = false;
  isAllTaskSelected: boolean = false;
  isSummaryActive: boolean = false;
  summaryData: any;
  filterConfig = {
    skip: 0,
    limit: 15,
    sort: [],
    search_params: "",
    isGroupBy: 0,
    groupByKey: "",
    groupByKeyValue: "",
    isFavList: 0,
    favTaskList: []
  };
  viewList = [
    {
      id: 2,
      icon: "group_outline",
      name: "All Tasks",
      class: "toggle-button-icon-2",
    },
    {
      id: 1,
      icon: "person_outline",
      name: "My Tasks",
      class: "toggle-button-icon-1",
    },
  ];
  isPageLoading: boolean = false;
  aid: any;
  loadingGifUrl: any;
  isAllTaskAllowed: boolean = false;
  mode: any = 1;
  modeName: any;
  taskHeaderCount: any;
  recentSearch = [];
  headerActionIconDetails: any;
  taskDetails: any = {
    noGroupBy: []
  };
  columns: any;
  statusDetails: any;
  priorityDetails: any;
  GroupByDetails: any;
  groupedTaskdetails: any;
  disableToggle: boolean = true;
  isApiInProgress: boolean = false;
  favouriteTaskList: any = [];
  bulkEditData: any;
  bulkUpdateTaskList: any = [];
  bulkUpdateTaskCount: number = 0;
  userSearchParams: any = '';
  stakeholdersList: any = [];
  selectedStakeholdersList: any = [];
  groupDetails: any = [];
  selectedDueDate: any = null;
  summaryCountData: any;
  minDate: any;
  maxDate: any;



  @ViewChild("triggerSearchBarTemplateRef", { static: false })
  triggerSearchBarTemplateRef!: TemplateRef<HTMLElement>;

  @ViewChild("dropdownTemplate")
  dropdownTemplate!: TemplateRef<any>;

  @ViewChild("statusdropdownTemplate")
  statusdropdownTemplate!: TemplateRef<any>;

  @ViewChild("assignedToDropdownTemplate")
  assignedToDropdownTemplate!: TemplateRef<any>;

  @ViewChild("prioritydropdownTemplate")
  prioritydropdownTemplate!: TemplateRef<any>;

  @ViewChild("inlineDatePickerTemplate")
  inlineDatePickerTemplate!: TemplateRef<any>;

  @ViewChild("summaryCustomizationTemplate")
  summaryCustomizationTemplate: TemplateRef<any>;

  @ViewChild("triggerTaskmarkAsDoneChange", { static: false })
  private triggerTaskmarkAsDoneChange!: TemplateRef<HTMLElement>;
  triggerTaskmarkAsDone: ElementRef;
  triggerTaskmarkAsDoneChangeField: ElementRef;
  tempSummaryData: any;
  isSelectAllSummary: any;


  constructor(
    private router: Router,
    private _fb: FormBuilder,
    private overlay: Overlay,
    private viewContainerRef: ViewContainerRef,
    private cdr: ChangeDetectorRef,
    private route: ActivatedRoute,
    private _loginService: LoginService,
    private toasterService: ToasterMessageService,
    private pmMasterService: PmMasterService,
    private authService: PmAuthorizationService,
    private myTaskService: MyTaskService,
    private PmBillingService: PmBillingService,
  ) {
    this.columnCustomForms = this._fb.group({});
  }

  async ngOnInit() {
    this.isPageLoading = true;
    this.itemId = parseInt(this.router.url.split("/")[5]);
    this.projectId = parseInt(this.router.url.split("/")[3]);
    this.aid = await this._loginService.getProfile().profile.aid;
    this.calculateDynamicStyle();
    await this.pmMasterService.getPMFormCustomizeConfigV().then((res: any) => {
      if (res) {
        this.formconfig = res;
      }
    });
    const primaLoading = _.findWhere(this.formconfig, {
      type: "my-task-list-view",
      field_name: "loading-gif",
      is_active: true,
    });
    this.loadingGifUrl = primaLoading ? primaLoading["LOADING-GIF"] : null;
    this.isAllTaskAllowed = await this.authService.getProjectWiseObjectAccess(this.projectId, this.itemId, 303)
    this.mode = this.isAllTaskAllowed ? 2 : 1;
    this.modeName = this.mode == 1 ? 'My Task' : 'All Task';
    this.taskHeaderCount = 4;
    await this.getColumnHeaderDetails();

    const bulkEditConfig = _.findWhere(this.formconfig, {
      type: "my-task-list-view",
      field_name: "bulk-edit",
      is_active: true,
    });

    for (const item of bulkEditConfig.bulkEditConfig) {
      const isActive = await this.authService.getProjectWiseObjectAccess(this.projectId, this.itemId, item.object_id);
      item.isActive = isActive;
    }

    this.bulkEditData = bulkEditConfig.bulkEditConfig.filter(
      (col) => col.isActive
    );

    
    const summaryConfig = _.findWhere(this.formconfig, {
      type: "my-task-list-view",
      field_name: "summary-card",
      is_active: true,
    });

    this.summaryData = summaryConfig.summaryConfig.filter(
      (val) => val.isActive
    );

    const retrieveStyles = _.where(this.formconfig, {
      type: "project-theme",
      field_name: "styles",
      is_active: true,
    });

    this.button =
      retrieveStyles.length > 0
        ? retrieveStyles[0].data.button_color
          ? retrieveStyles[0].data.button_color
          : "#90ee90"
        : "#90ee90";
    document.documentElement.style.setProperty("--myTaskButton", this.button);

    this.fontStyle =
      retrieveStyles.length > 0
        ? retrieveStyles[0].data.font_style
          ? retrieveStyles[0].data.font_style
          : "Roboto"
        : "Roboto";
    document.documentElement.style.setProperty("--myTaskFont", this.fontStyle);

    this.tabColor =
      retrieveStyles.length > 0
        ? retrieveStyles[0].data.tab_color
          ? retrieveStyles[0].data.tab_color
          : ""
        : "";
    document.documentElement.style.setProperty(
      "--myTaskTabColor",
      this.tabColor
    );

    this.fieldOutline =
      retrieveStyles.length > 0
        ? retrieveStyles[0].data.field_outline_color
          ? retrieveStyles[0].data.field_outline_color
          : ""
        : "";
    document.documentElement.style.setProperty(
      "--myTaskFieldOutline",
      this.fieldOutline
    );

    this.toggleButtonBg =
      retrieveStyles.length > 0
        ? retrieveStyles[0].data.shades_color
          ? retrieveStyles[0].data.shades_color
          : ""
        : "";
    document.documentElement.style.setProperty(
      "--myTasktoggleButtonBg",
      this.toggleButtonBg
    );

    this.toggleButton =
      retrieveStyles.length > 0
        ? retrieveStyles[0].data.toggle_color
          ? retrieveStyles[0].data.toggle_color
          : ""
        : "";
    document.documentElement.style.setProperty(
      "--myTasktoggleButton",
      this.toggleButton
    );

    this.headerActionIconDetails = _.where(this.formconfig, {
      type: "my-task-list",
      fieldName: "header-actions",
      is_active: true,
    });

    this.GroupByDetails = _.where(this.formconfig, {
      type: "my-task-list",
      fieldName: "group_by",
      is_active: true,
    });

    const columnsConfig = _.where(this.formconfig, { type: "my-task-landing-list", field_name: "style_config", is_active: true });
    this.columns = columnsConfig[0]?.columnConfig ? columnsConfig[0]?.columnConfig : null;

    await this.pmMasterService.getProjectActivityStatus().then((res: any) => {
      if (res['messType'] === 'S' && res['data'] && res['data'].length > 0) {
        this.statusDetails = res['data'];
      } else if (res['messType'] === 'E') {
        this.toasterService.showError(res['messText'] ? res['messText'] : 'Error while retrieving status details');
      }
    });

    await this.pmMasterService.getProjectActivityPriority().then((res: any) => {
      if (res['messType'] === 'S' && res['data'] && res['data'].length > 0) {
        this.priorityDetails = res['data'];
      } else if (res['messType'] === 'E') {
        this.toasterService.showError(res['messText'] ? res['messText'] : 'Error while retrieving status details');
      }
    });

    await this.PmBillingService.getMaxMinProjectDatesPO(
      this.projectId,
      this.itemId
    ).then((res) => {
      if (res['messType'] == 'S') {
        if (res['data'].length > 0) {
          this.minDate = moment(res['data'][0]['min_date']).format('YYYY-MM-DD');
          this.maxDate = moment(res['data'][0]['max_date']).format('YYYY-MM-DD');
        }
      }
    });

    await this.getTaskSummaryCount();
    await this.getMyFavouriteTask();
    if (this.favouriteTaskList && this.favouriteTaskList.length > 0) {
      this.filterConfig.isFavList = 1;
      this.filterConfig.favTaskList = this.favouriteTaskList;
      await this.getTaskdetails(this.filterConfig)
    }
    this.filterConfig.isFavList = 0;
    await this.getTaskdetails(this.filterConfig)
    this.filterConfig.favTaskList = [];
    await this.addPeopleToTaskSearch(this.userSearchParams);
    this.disableToggle = false
    this.isPageLoading = false;
  }

  calculateDynamicStyle() {
    let myTaskWidth = window.innerWidth - 142 + "px";
    document.documentElement.style.setProperty(
      "--dynamicMyTaskWidth",
      myTaskWidth
    );
    let myTaskHeight = window.innerHeight - 216 + "px";
    document.documentElement.style.setProperty(
      "--dynamicMyTaskHeight",
      myTaskHeight
    );
    let myTaskSubHeight = window.innerHeight - 241 + "px";
    document.documentElement.style.setProperty(
      "--myTaskSubHeight",
      myTaskSubHeight
    );
    let myTasklistSubHeight = window.innerHeight - 235 + "px";
    document.documentElement.style.setProperty(
      "--myTasklistSubHeight",
      myTasklistSubHeight
    );
    let myTaskafterChange = window.innerHeight - 371 + "px";
    document.documentElement.style.setProperty(
      "--myTaskAfterChange",
      myTaskafterChange
    );
    if (!this.filterConfig.isGroupBy) {
      let groupListScrollBarWidth = 8 + "px";
      document.documentElement.style.setProperty(
        "--groupListScrollBarWidth",
        groupListScrollBarWidth
      );
      let content_value = this.isSummaryActive ? 350 : 290;
      let myTaskListContent = window.innerHeight - content_value + "px";
      document.documentElement.style.setProperty(
        "--myTaskListContent",
        myTaskListContent
      );
    } else {
      let content_value = this.isSummaryActive ? 450 : 420;
      let myTaskListContent = window.innerHeight - content_value + "px";
      document.documentElement.style.setProperty(
        "--myTaskListContent",
        myTaskListContent
      );
      let groupListScrollBarWidth = 0 + "px";
      document.documentElement.style.setProperty(
        "--groupListScrollBarWidth",
        groupListScrollBarWidth
      );
    }
  }

  @HostListener("window:resize")
  onResize() {
    this.calculateDynamicStyle();
  }

  async getColumnHeaderDetails() {
    const columnList = _.findWhere(this.formconfig, {
      type: "my-task-list-view",
      field_name: "column-list",
      is_active: true,
    });
    console.log("columnList Config", columnList);

    if (columnList && columnList.columnList) {
      this.columnListData = columnList.columnList.filter((col) => col.isActive);
      this.customizationData = [...this.columnListData];
    }

    this.updateColumnCustomizeDetails()
  }

  updateColumnCustomizeDetails(): void {
    Object.keys(this.columnCustomForms.controls).forEach((key) => {
      this.columnCustomForms.removeControl(key);
    });

    this.customizationData.forEach((item) => {
      this.columnCustomForms.addControl(
        item.key,
        new FormControl(item.isVisible)
      );
    });

    // Add a FormControl to track the selection limit
    this.columnCustomForms.addControl(
      'columnSelectionError',
      new FormControl(false)
    );

    // Initial validation check
    this.validateColumnSelection();
  }

  validateColumnSelection(): void {
    const selectedCount = Object.values(this.columnCustomForms.getRawValue()).filter(Boolean).length;

    if (selectedCount > 8) {
      this.columnCustomForms.get('columnSelectionError')?.setValue(true);
    } else {
      this.columnCustomForms.get('columnSelectionError')?.setValue(false);
    }
  }

  openSearchBarOverlay(triggerField) {
    if (!this.overlayRef?.hasAttached()) {
      const positionStrategyBuilder = this.overlay.position();

      const positionStrategy = positionStrategyBuilder
        .flexibleConnectedTo(triggerField)
        .withFlexibleDimensions(true)
        .withPush(true)
        .withViewportMargin(25)
        .withGrowAfterOpen(true)
        .withPositions([
          {
            originX: "end",
            originY: "bottom",
            overlayX: "end",
            overlayY: "top",
          },
        ]);

      positionStrategy.withDefaultOffsetX(-20);
      positionStrategy.withDefaultOffsetY(-30);
      const scrollStrategy = this.overlay.scrollStrategies.close();

      this.overlayRef = this.overlay.create({
        positionStrategy,
        scrollStrategy,
        hasBackdrop: true,
        backdropClass: "",
        panelClass: ["pop-up"],
      });

      const templatePortal = new TemplatePortal(
        this.triggerSearchBarTemplateRef,
        this.viewContainerRef
      );

      this.overlayRef.attach(templatePortal);

      this.overlayRef.backdropClick().subscribe(() => {
        this.closeOverlay();
      });
    }
  }

  closeOverlay() {
    if (this.overlayRef) {
      this.overlayRef.dispose();
      this.overlayRef = null;
    }
  }

  async onEnterSearch(searchData: string) {
    this.closeOverlay();
    searchData = searchData.trim();
    this.dataloading = true;

    if (searchData && searchData !== "") {
      const aid = this._loginService.getProfile().profile.aid;

      if (!this.recentSearch.includes(searchData)) {
        if (this.recentSearch.length === 5) {
          this.recentSearch.pop();
        }
        this.recentSearch.unshift(searchData);
      } else {
        this.recentSearch = this.recentSearch.filter(
          (item) => item !== searchData
        );
        this.recentSearch.unshift(searchData);
      }

      localStorage.setItem(
        `task-recent-search-${aid}`,
        JSON.stringify(this.recentSearch)
      );
    }
    await this.getTaskSummaryCount();
    this.filterConfig.skip = 0;
    this.filterConfig.limit = 15;
    // this.taskDetails = {
    //   noGroupBy: []
    // }

    // await this.getTaskdetails(this.filterConfig);
    await this.refreshData()
    this.dataloading = false;
  }

  async clearSearchFilter() {
    this.closeOverlay();
    this.dataloading = true;
    this.filterConfig.search_params = "";
    await this.getTaskSummaryCount();
    await this.refreshData();
    this.dataloading = false;
  }

  highlightSearch(recentSearch: string) {
    if (!this.filterConfig.search_params) {
      return recentSearch;
    }

    let escapedSearch = this.filterConfig.search_params.replace(
      /[.*+?^${}()|[\]\\]/g,
      "\\$&"
    );
    const regex = new RegExp(escapedSearch, "gi");
    return recentSearch.replace(regex, (match) => `<b>${match}</b>`);
  }

  onSelectRecentSearch(index: number) {
    this.filterConfig.search_params = this.recentSearch[index];
    this.onEnterSearch(this.filterConfig.search_params);
  }

  // async toggleMode(event: any) {
  //   this.dataloading = true;
  //   this.disableToggle = true;
  //   this.mode = event.value;
  //   this.modeName = this.viewList
  //     .map(type => type.id === this.mode ? type.name : null)
  //     .filter(name => name !== null)[0];
  //   this.taskDetails = {
  //     noGroupBy: []
  //   }
  //   this.GroupByDetails[0].icon.forEach(icon => {
  //     icon.isSelected = false;
  //   })
  //   this.filterConfig = {
  //     skip: 0,
  //     limit: 15,
  //     sort: [],
  //     search_params: "",
  //     isGroupBy: 0,
  //     groupByKey: "",
  //     groupByKeyValue: "",
  //     isFavList: 0,
  //     favTaskList: []
  //   }
  //   await this.getTaskSummaryCount();
  //   await this.getMyFavouriteTask();
  //   if ( this.favouriteTaskList && this.favouriteTaskList.length > 0) {
  //     this.filterConfig.isFavList = 1;
  //     this.filterConfig.favTaskList = this.favouriteTaskList;
  //     await this.getTaskdetails(this.filterConfig)
  //   }
  //   this.filterConfig.isFavList = 0;
  //   await this.getTaskdetails(this.filterConfig);
  //   this.calculateDynamicStyle();
  //   this.dataloading = false;
  //   this.disableToggle = false;
  // }

  async toggleMode(event: any){
    this.dataloading = true;
    this.disableToggle = true;
    this.mode = event.value;
    this.modeName = this.viewList
      .map(type => type.id === this.mode ? type.name : null)
      .filter(name => name !== null)[0];
    const matchinghiddenColumn = this.columns.find(column => column.isActive && column.column_value_key === this.filterConfig.groupByKey)
    if (matchinghiddenColumn) {
        matchinghiddenColumn.isVisible = true;
        matchinghiddenColumn.isDisabled = false;
    }
    this.filterConfig = {
      skip: 0,
      limit: 15,
      sort: [],
      search_params: "",
      isGroupBy: 0,
      groupByKey: "",
      groupByKeyValue: "",
      isFavList: 0,
      favTaskList: []
    }
    this.calculateDynamicStyle();
    await this.getTaskSummaryCount();
    await this.refreshData();
    this.dataloading = false;
    this.disableToggle = false;

  }

  getIconStatus(keyName: string): boolean | null {
    const icon = this.headerActionIconDetails[0].icon.find(item => item.keyName === keyName);
    return icon ? icon.isActive : false;
  }

  getIconLabel(keyName: string): string {
    const icon = this.headerActionIconDetails[0].icon.find(icon => icon.keyName === keyName);
    return icon ? icon.label || '' : '';
  }

  async getTaskSummaryCount() {
    await this.myTaskService.getMyTaskSummaryCount(this.projectId, this.itemId, this.mode, this.filterConfig).then((res: any) => {
      if (res['messType'] == "S") {
        this.summaryCountData = res['data'] ? res['data'] : []
        this.taskHeaderCount = res['data'].total_tasks ? res['data'].total_tasks : 0;
      } else {
        this.toasterService.showError(res['messText'] ? res['messText'] : 'Error while retrieving task count');
      }
    })
  }

  async getMyFavouriteTask() {
    await this.myTaskService.getMyFavouriteTask(this.projectId, this.itemId).then((res: any) => {
      if (res['messType'] == "S") {
        this.favouriteTaskList = res['data'] && res['data'].length > 0 && res['data'][0]['task_ids'] ? res['data'][0]['task_ids'] : [];
      } else {
        this.toasterService.showError(res['messText'] ? res['messText'] : 'Error while retrieving task count');
      }
    })
  }

  async getTaskdetails(filterConfig) {
    filterConfig.sort = this.columns
      .filter(column => column.isSortActive && column.sortOrder !== 0)
      .map(column => ({
        key: column.column_value_key,
        order: column.sortOrder
      }));

    await this.myTaskService.getMyTaskListView(this.projectId, this.itemId, this.mode, filterConfig).then((res: any) => {
      if (res['messType'] == "S" && res['data'].length > 0) {
        res['data'] = res['data'].map(task => {
          task.isTaskCompleted = task.status_id && task.status_id == 5 ? true : false;
          task.isFavTask = this.favouriteTaskList && this.favouriteTaskList.includes(task.id) ? true : false;
          task.isChecked = this.isAllTaskSelected ? true : false;
          return task;
        });
        if (!filterConfig.isGroupBy) {
          this.taskDetails.noGroupBy = [...this.taskDetails.noGroupBy, ...res['data']];
        } else {
          this.taskDetails[filterConfig.groupByKeyValue] = [
            ...(this.taskDetails[filterConfig.groupByKeyValue] || []),
            ...res['data']
          ];
        }
      } else if (res['messType'] === 'E') {
        this.toasterService.showError(res['messText'] ? res['messText'] : 'Error while retrieving task');
      }
    })
  }

  async onClickSort(order, columnKey) {
    this.dataloading = true;
    this.columns.forEach(column => {
      if (column.column_value_key === columnKey) {
        if (column.sortOrder === order) {
          column.sortOrder = 0;
        } else {
          column.sortOrder = order;
        }
      } else {
        column.sortOrder = 0;
      }
    });
    await this.refreshData();
    this.dataloading = false;
  }

  onDataScroll(groupKeyValue) {
    let filterConfig = this.filterConfig;
    if (this.filterConfig.isGroupBy) {
      this.groupedTaskdetails.forEach(item => {
        if (item.key === groupKeyValue) {
          item.skip += 15;
          item.limit += 15;
          filterConfig.skip = item.skip;
          filterConfig.limit = item.limit;
          filterConfig.groupByKeyValue = groupKeyValue;
          this.getTaskdetails(filterConfig)
        }
      })
    } else {
      this.filterConfig.skip = this.filterConfig.skip + 15;
      this.filterConfig.limit = this.filterConfig.limit + 15;
      if (this.favouriteTaskList.length > 0) {
        this.filterConfig.isFavList = 0;
        this.filterConfig.favTaskList = this.favouriteTaskList;
      }
      this.filterConfig.groupByKeyValue = groupKeyValue !== 'noGroupBy' ? groupKeyValue : "";
      this.getTaskdetails(this.filterConfig)
    }
  }

  getProgressBarColor(value: number): string {
    if (value === 0) {
      return "#7D838B";
    } else if (value >= 1 && value <= 50) {
      return "#FF3A46";
    } else if (value >= 51 && value <= 99) {
      return "#FFBD3D";
    } else if (value === 100) {
      return "#4DBD17";
    } else {
      return "";
    }
  }

  isOverdue(item: any): boolean {
    const endDate = new Date(item.end_date);
    endDate.setHours(0, 0, 0, 0);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return endDate < today;
  }

  toggleDropdown(event: MouseEvent) {
    event.stopPropagation();
    if (this.overlayRef) {
      this.closeOverlay();
    } else {
      const target = event.currentTarget as HTMLElement;
      const config = new OverlayConfig({
        hasBackdrop: true,
        backdropClass: "cdk-overlay-transparent-backdrop",
        positionStrategy: this.overlay
          .position()
          .flexibleConnectedTo(target)
          .withPositions([
            {
              originX: "end",
              originY: "bottom",
              overlayX: "end",
              overlayY: "top",
            },
          ]),
      });

      this.overlayRef = this.overlay.create(config);
      this.overlayRef.attach(
        new TemplatePortal(this.dropdownTemplate, this.viewContainerRef)
      );
      this.overlayRef.backdropClick().subscribe(() => this.closeOverlay());
    }
  }

  statusDropdown(event: MouseEvent, task: any) {
    event.stopPropagation();

    // Close the overlay if it is already open
    if (this.overlayRef) {
      this.closeOverlay();
    } else {
      const target = event.currentTarget as HTMLElement;

      // Create overlay config
      const config = new OverlayConfig({
        hasBackdrop: true,
        backdropClass: "cdk-overlay-transparent-backdrop",
        positionStrategy: this.overlay
          .position()
          .flexibleConnectedTo(target)
          .withPositions([
            {
              originX: "end",
              originY: "bottom",
              overlayX: "end",
              overlayY: "top",
            },
          ]),
      });

      // Create overlay reference
      this.overlayRef = this.overlay.create(config);

      // Attach the template to the overlay
      this.overlayRef.attach(
        new TemplatePortal(this.statusdropdownTemplate, this.viewContainerRef, {
          $implicit: task // Pass the task as context to the template
        })
      );

      // Close overlay on backdrop click
      this.overlayRef.backdropClick().subscribe(() => this.closeOverlay());
    }
  }

  assignedToDropdown(event: MouseEvent, task: any) {
    event.stopPropagation();
    
    this.stakeholdersList.forEach(stakeholder => {
      stakeholder.isSelected = task.assigned_to.includes(stakeholder.associate_id);
    });
    this.selectedStakeholdersList = [...task.assigned_to]

    // Close the overlay if it is already open
    if (this.overlayRef) {
      this.closeOverlay();
    } else {
      const target = event.currentTarget as HTMLElement;

      // Create overlay config
      const config = new OverlayConfig({
        hasBackdrop: true,
        backdropClass: "cdk-overlay-transparent-backdrop",
        positionStrategy: this.overlay
          .position()
          .flexibleConnectedTo(target)
          .withPositions([
            {
              originX: "end",
              originY: "bottom",
              overlayX: "end",
              overlayY: "top",
            },
          ]),
      });

      // Create overlay reference
      this.overlayRef = this.overlay.create(config);

      // Attach the template to the overlay
      this.overlayRef.attach(
        new TemplatePortal(this.assignedToDropdownTemplate, this.viewContainerRef, {
          $implicit: task // Pass the task as context to the template
        })
      );

      // Close overlay on backdrop click
      this.overlayRef.backdropClick().subscribe(() => this.closeOverlay());
    }
  }

  priorityDropdown(event: MouseEvent, task: any) {
    event.stopPropagation();

    // Close the overlay if it is already open
    if (this.overlayRef) {
      this.closeOverlay();
    } else {
      const target = event.currentTarget as HTMLElement;

      // Create overlay config
      const config = new OverlayConfig({
        hasBackdrop: true,
        backdropClass: "cdk-overlay-transparent-backdrop",
        positionStrategy: this.overlay
          .position()
          .flexibleConnectedTo(target)
          .withPositions([
            {
              originX: "end",
              originY: "bottom",
              overlayX: "end",
              overlayY: "top",
            },
          ]),
      });

      // Create overlay reference
      this.overlayRef = this.overlay.create(config);

      // Attach the template to the overlay
      this.overlayRef.attach(
        new TemplatePortal(this.prioritydropdownTemplate, this.viewContainerRef, {
          $implicit: task // Pass the task as context to the template
        })
      );

      // Close overlay on backdrop click
      this.overlayRef.backdropClick().subscribe(() => this.closeOverlay());
    }
  }

  inlineDatePicker(event: MouseEvent, task: any, column: any) {
    event.stopPropagation();

    // Close the overlay if it is already open
    if (this.overlayRef) {
      this.closeOverlay();
    } else {
      const target = event.currentTarget as HTMLElement;

      // Create overlay config
      const config = new OverlayConfig({
        hasBackdrop: true,
        backdropClass: "cdk-overlay-transparent-backdrop",
        positionStrategy: this.overlay
          .position()
          .flexibleConnectedTo(target)
          .withPositions([
            {
              originX: "end",
              originY: "bottom",
              overlayX: "end",
              overlayY: "top",
            },
          ]),
      });

      this.overlayRef = this.overlay.create(config);

      this.overlayRef.attach(
        new TemplatePortal(this.inlineDatePickerTemplate, this.viewContainerRef, {
          $implicit: task,
          column: column
        })
      );

      // Close overlay on backdrop click
      this.overlayRef.backdropClick().subscribe(() => this.closeOverlay());
    }
  }


  async getGroupByData(groupByKey) {
    this.closeOverlay();
    this.dataloading = true;
    const matchinghiddenColumn = this.columns.find(column => column.isActive && column.column_value_key === this.filterConfig.groupByKey)
    if (matchinghiddenColumn) {
      matchinghiddenColumn.isVisible = true;
      matchinghiddenColumn.isDisabled = false;
    }

    const matchingColumn = this.columns.find(column => column.isActive && column.column_value_key === groupByKey);
    if (matchingColumn) {
      matchingColumn.isVisible = false;
      matchingColumn.isDisabled = true;
    }

    this.filterConfig.isGroupBy = groupByKey === 'clear' ? 0 : 1;
    if (this.filterConfig.isGroupBy) {
      this.GroupByDetails[0].icon.forEach(icon => {
        if (icon.keyName === groupByKey)
          icon.isSelected = true;
        else
          icon.isSelected = false;
      })
      this.filterConfig.groupByKey = groupByKey;
      this.taskDetails = {
        noGroupBy: []
      };
      await this.myTaskService.getMyTaskCountForgroupBy(this.projectId, this.itemId, this.mode, this.filterConfig.groupByKey).then((res: any) => {
        if (res['messType'] == "S" && res['data'].length > 0) {
          this.groupedTaskdetails = res['data'];
          this.groupedTaskdetails.forEach(item => {
            item.isExpanded = true;
            if (typeof item.key !== 'string') {
              item.key = String(item.key); // Convert to string
            }
          });
        } else if (res['messType'] === 'E') {
          this.toasterService.showError(res['messText'] ? res['messText'] : 'Error while fetching the header details of task');
        }
      })
      for (let item of this.groupedTaskdetails) {
        item.skip = 0;
        item.limit = 15;
        let filterConfig = this.filterConfig
        if (item.count > 0) {
          filterConfig.skip = item.skip;
          filterConfig.limit = item.limit;
          filterConfig.groupByKeyValue = item.key;
          filterConfig.favTaskList = [];
          await this.getTaskdetails(filterConfig);
        }
      }
    } else { 
      this.filterConfig.groupByKey = null;
      this.filterConfig.groupByKeyValue = null;
      await this.refreshData();
    }
    await this.calculateDynamicStyle();
    this.dataloading = false;
  }

  convertAsNumber(value) {
    return value ? Number(value) : null
  }

  toggleGroupExpansion(group: any) {
    let groupDetails = this.groupedTaskdetails.find(item => item.key == group.key)
    groupDetails.isExpanded = !groupDetails.isExpanded;
    this.cdr.detectChanges();
  }

  isExpanded(key: any): boolean {
    const group = this.groupedTaskdetails.find(group => group.key === key);
    return group ? group.isExpanded : false;
  }

  async updateTaskStatus(statusId, task) {
    const isAllowed = await this.authService.getProjectWiseObjectAccess(this.projectId, this.itemId, 312)
    if(!isAllowed){
      this.toasterService.showWarning("Access denied: You are not authorized to update the task status.", 10000)
      this.closeOverlay();
      return
    }
    await this.myTaskService.updateinlineTaskStatus(this.projectId, this.itemId, task.id, statusId,task.actual_start_date,task.actual_end_date).then((res: any) => {
      if (res['messType'] == "S") {
        task.status_id = statusId;
        task.completion_percentage = task.status_id && task.status_id == 5 ? 100 : task.completion_percentage;
        if (task.status_id == 7) {
          task.actual_start_date = null;
          task.actual_end_date = null;
          task.display_actual_end_date = null;
          task.display_actual_date = null
        }
        if (task.status_id == 5) {
          if (task.actual_start_date == null || task.actual_start_date == undefined || task.actual_start_date == 'null' || task.actual_start_date == '' || task.actual_start_date == "Invalid date") {
            task.actual_start_date = moment().format("YYYY-MM-DD")
            task.display_actual_date = moment().format("DD-MMM-YYYY");
          }
          task.actual_end_date = moment().format("YYYY-MM-DD")
           task.display_actual_end_date=moment().format("DD-MMM-YYYY");
        }
        if (task.status_id == 4) {
          task.actual_start_date = moment().format("YYYY-MM-DD");
          task.display_actual_date=moment().format("DD-MMM-YYYY");
          task.actual_end_date = null
          task.display_actual_end_date=null;
        }
        task.isTaskCompleted = task.status_id && task.status_id == 5 ? true : false;
        this.toasterService.showSuccess(res['messText'], 10000)
      } else {
        this.toasterService.showError(res['messText'] ? res['messText'] : 'Error while updating task status');
      }
    })
    this.closeOverlay();
  }

  async updateTaskPriority(priority, task) {
    const isAllowed = await this.authService.getProjectWiseObjectAccess(this.projectId, this.itemId, 314)
    if(!isAllowed){
      this.toasterService.showWarning("Access denied: You are not authorized to update the task priority.", 10000);
      this.closeOverlay();
      return;
    }
    await this.myTaskService.updateinlineTaskPriority(this.projectId, this.itemId, task.id, priority).then((res: any) => {
      if (res['messType'] == "S") {
        task.priority = priority;
        this.toasterService.showSuccess(res['messText'] ? res['messText'] : 'Task Updated Successfully', 10000)
      } else {
        this.toasterService.showError(res['messText'] ? res['messText'] : 'Error while updating task priority');
      }
    })
    this.closeOverlay();
  }

  async updateInlineDate(task, date, column) {
    this.isApiInProgress = true;
    const isAllowed = await this.authService.getProjectWiseObjectAccess(this.projectId, this.itemId, 313)
    if(!isAllowed){
      this.toasterService.showWarning("Access denied: You are not authorized to update the task dates.", 10000);
      this.isApiInProgress = false;
      this.closeOverlay();
      return;
    }
    if (column.dataKey === "start_date") {
      // Update the start date
      const startDate = moment(date, "YYYY-MM-DD");
      const endDate = moment(task.end_date, "YYYY-MM-DD");

      if(startDate.isAfter(endDate)){
        this.toasterService.showWarning('Start date cannot be after the end date', 10000);
      }else{
        await this.myTaskService.updateinlineTaskStartDate(this.projectId, this.itemId, task.id, moment(date).format("YYYY-MM-DD")).then((res: any) => {
          if (res['messType'] === "S") {
              task[column.column_value_key] = moment(date).format("DD-MMM-YYYY");
              task[column.dataKey] = moment(date).format("YYYY-MM-DD");
              this.toasterService.showSuccess(res['message'], 10000);
          } else {
              this.toasterService.showError(res['message'] ? res['message'] : 'Error while updating task start date');
          }
      });
      }
  } else if (column.dataKey === "end_date") {
      // First, validate that the end date is not before the start date
      const startDate = moment(task.start_date, "YYYY-MM-DD");
      const endDate = moment(date, "YYYY-MM-DD");
  
      if (endDate.isBefore(startDate)) {
          // If the end date is before the start date, show an error message
          this.toasterService.showWarning('End date cannot be before the start date', 10000);
      }else {
          // Proceed with the update if the end date is valid
          await this.myTaskService.updateinlineTaskEndDate(this.projectId, this.itemId, task.id, moment(date).format("YYYY-MM-DD")).then((res: any) => {
              if (res['messType'] === "S") {
                  task[column.column_value_key] = moment(date).format("DD-MMM-YYYY");
                  task[column.dataKey] = moment(date).format("YYYY-MM-DD");
                  this.toasterService.showSuccess(res['message'], 10000);
              } else {
                  this.toasterService.showError(res['message'] ? res['message'] : 'Error while updating task end date');
              }
          });
      }
  }
  if (column.dataKey === "actual_start_date") {
    // Update the start date
    const startDate = moment(date, "YYYY-MM-DD");
    const endDate = moment(task.actual_end_date, "YYYY-MM-DD");

    if(startDate.isAfter(endDate)){
      this.toasterService.showWarning('Actual Start date cannot be after the Actual end date', 10000);
    }else if(task.status_id==7){
      this.toasterService.showWarning('Actual Start Date cannot be updated', 10000);
    }
    
    else{
      await this.myTaskService.updateinlineTaskActualStartDate(this.projectId, this.itemId, task.id, moment(date).format("YYYY-MM-DD")).then((res: any) => {
        if (res['messType'] === "S") {
            task[column.column_value_key] = moment(date).format("DD-MMM-YYYY");
            task[column.dataKey] = moment(date).format("YYYY-MM-DD");
            this.toasterService.showSuccess(res['message'], 10000);
        } else {
            this.toasterService.showError(res['message'] ? res['message'] : 'Error while updating task start date');
        }
    });
    }
} else if (column.dataKey === "actual_end_date") {
    // First, validate that the end date is not before the start date
    const startDate = moment(task.actual_start_date, "YYYY-MM-DD");
    const endDate = moment(date, "YYYY-MM-DD");

    if (endDate.isBefore(startDate)) {
        // If the end date is before the start date, show an error message
        this.toasterService.showWarning('Actual End date cannot be before the start date', 10000);
    }
    else if(task.status_id ==7 || task.status_id==4){
      this.toasterService.showWarning('Actual Start date cannot be updated', 10000);
    }
    
    else {
        // Proceed with the update if the end date is valid
        await this.myTaskService.updateinlineTaskActualEndDate(this.projectId, this.itemId, task.id, moment(date).format("YYYY-MM-DD")).then((res: any) => {
            if (res['messType'] === "S") {
                task[column.column_value_key] = moment(date).format("DD-MMM-YYYY");
                task[column.dataKey] = moment(date).format("YYYY-MM-DD");
                this.toasterService.showSuccess(res['message'], 10000);
            } else {
                this.toasterService.showError(res['message'] ? res['message'] : 'Error while updating task end date');
            }
        });
    }
}
  
    this.isApiInProgress = false;
    this.closeOverlay();
  }

  navigateToDetail(task_id: number) {
    this.router.navigate(["details", task_id], {
      relativeTo: this.route.parent,
    });
  }

  async markAsComplete(task) {
    if (task.isTaskCompleted) {
      this.toasterService.showWarning("Task has been already completed", 10000);
    } else {
      await this.myTaskService.updateinlineTaskStatus(this.projectId, this.itemId, task.id, 5,task.actual_start_date,task.actual_end_date).then((res: any) => {
        if (res['messType'] == "S") {
          task.status_id = 5;
          task.isTaskCompleted = task.status_id && task.status_id == 5 ? true : false;
          task.completion_percentage = 100;
          if (task.status_id == 5) {
            if (task.actual_start_date == null || task.actual_start_date == undefined || task.actual_start_date == 'null' || task.actual_start_date == '' || task.actual_start_date == "Invalid date") {
              task.actual_start_date = moment().format("YYYY-MM-DD")
              task.display_actual_date = moment().format("DD-MMM-YYYY");
            }
            task.actual_end_date = moment().format("YYYY-MM-DD")
             task.display_actual_end_date=moment().format("DD-MMM-YYYY");
          }
          this.toasterService.showSuccess(res['messText'], 10000)
        } else {
          this.toasterService.showError(res['messText'] ? res['messText'] : 'Error while updating task status');
        }
      })
    }
  }

  async toggleFavourite(task, groupList, itemIndex) {
    if (task.isFavTask) {
      await this.myTaskService.removeTaskFromFavourite(this.projectId, this.itemId, [task.id]).then((res: any) => {
        if (res['messType'] == "S") {
          task.isFavTask = false;
          groupList.splice(itemIndex, 1);
          groupList.push(task);
          this.favouriteTaskList = this.favouriteTaskList.filter(id => id !== task.id)
          this.toasterService.showSuccess(res['messText'], 10000)
        } else {
          this.toasterService.showError(res['messText'] ? res['messText'] : 'Error while updating task');
        }
      })
    } else {
      await this.myTaskService.markTaskAsFavourite(this.projectId, this.itemId, [task.id]).then((res: any) => {
        if (res['messType'] == "S") {
          task.isFavTask = true;
          groupList.splice(itemIndex, 1);
          groupList.unshift(task);
          this.favouriteTaskList.push(task.id)
          this.toasterService.showSuccess(res['messText'], 10000)
        } else {
          this.toasterService.showError(res['messText'] ? res['messText'] : 'Error while updating task');
        }
      })
    }
  }

  onTaskCheckboxChange(event: MatCheckboxChange, task) {
    if (event.checked) {
      task.isChecked = true;
      this.bulkUpdateTaskList.push(task.id);
      this.bulkUpdateTaskCount += 1;
    } else {
      task.isChecked = false;
      this.bulkUpdateTaskList = this.bulkUpdateTaskList.filter(id => id !== task.id);
      this.bulkUpdateTaskCount -= 1;
    }
  }

  onMouseEnter(key: string) {
    if (!this.bulkEditData) return;
    this.bulkEditData.forEach((obj) => {
      obj.isHovered = false;
    });

    const index = this.bulkEditData.findIndex((obj) => obj.key === key);
    if (index !== -1) {
      this.bulkEditData[index].isHovered = true;
    }
    this.bulkEditData = [...this.bulkEditData];
  }

  onMouseLeave() {
    if (!this.bulkEditData) return;
    this.bulkEditData.forEach((obj) => {
      obj.isHovered = false;
    });
    this.bulkEditData = [...this.bulkEditData];
  }

  openOverlay(
    key: string,
    triggerField: ElementRef,
    template: TemplateRef<any>
  ) {
    const index = this.bulkEditData.findIndex((obj) => obj.key === key);
    if (index !== -1) {
      this.bulkEditData[index].isProcessing = true;
      this.bulkEditData = [...this.bulkEditData];
    }

    if (!this.overlayRef?.hasAttached()) {
      const positionStrategy = this.overlay
        .position()
        .flexibleConnectedTo(triggerField)
        .withFlexibleDimensions(true)
        .withPush(true)
        .withViewportMargin(10)
        .withGrowAfterOpen(true)
        .withPositions([
          {
            originX: "end",
            originY: "bottom",
            overlayX: "center",
            overlayY: "top",
            offsetY: 7,
          },
        ]);

      const overlayConfig = new OverlayConfig({
        positionStrategy,
        hasBackdrop: true,
        // backdropClass: "cdk-overlay-dark-backdrop",
        backdropClass: "",
        panelClass: "my-overlay",
      });

      this.overlayRef = this.overlay.create(overlayConfig);

      this.overlayRef.attach(
        new TemplatePortal(template, this.viewContainerRef)
      );
      this.overlayRef.backdropClick().subscribe(() => {
        this.overlayRef?.dispose();
        this.overlayRef = null;
        this.bulkEditData.forEach((obj) => {
          if (obj.key === key) {
            obj.isProcessing = false;
          }
        });
        this.bulkEditData = [...this.bulkEditData];
        if(key === "assignedTo"){
          this.userSearchParams = "";
          this.selectedStakeholdersList = [];
          this.isApiInProgress = false;
        }
      });
    }
  }

  async bulkUpdateTaskStatus(status) {
    await this.myTaskService.bulkUpdateTaskStatus(this.projectId, this.itemId, this.bulkUpdateTaskList, status, this.isAllTaskSelected, this.groupDetails).then((res: any) => {
      if (res['messType'] == "S") {
        this.toasterService.showSuccess(res['messText'], 10000)
        this.closeOverlay();
        this.closeToolbar();
        this.refreshData();

      } else {
        this.toasterService.showError(res['messText'] ? res['messText'] : 'Error while updating task status');
      }
    })
  }

  async bulkUpdateTaskPriority(priority) {
    await this.myTaskService.bulkUpdateTaskPriority(this.projectId, this.itemId, this.bulkUpdateTaskList, priority, this.isAllTaskSelected, this.groupDetails).then((res: any) => {
      if (res['messType'] == "S") {
        this.toasterService.showSuccess(res['messText'], 10000)
        this.closeOverlay();
        this.closeToolbar();
        this.refreshData();

      } else {
        this.toasterService.showError(res['messText'] ? res['messText'] : 'Error while updating task priority');
      }
    })
  }

  async bulkUpdateTaskAssignedTo(){
    this.isApiInProgress = true;
    await this.myTaskService.bulkUpdateTaskAssignedTo(this.projectId, this.itemId, this.bulkUpdateTaskList, this.selectedStakeholdersList, this.isAllTaskSelected, this.groupDetails).then((res: any) => {
      if (res['messType'] == "S") {
        this.toasterService.showSuccess(res['messText'], 10000)
        this.userSearchParams = "";
        this.selectedStakeholdersList = [];
        this.addPeopleToTaskSearch(this.userSearchParams);
        this.isApiInProgress = false;
        this.closeOverlay();
        this.closeToolbar();
        this.refreshData();

      } else {
        this.toasterService.showError(res['messText'] ? res['messText'] : 'Error while updating task priority');
        this.isApiInProgress = false;
      }
    })
    this.isApiInProgress = false;
  }

  async bulkUpdateTaskDueDate() {
    this.isApiInProgress = true;

    // Check if a due date is selected
    if (!this.selectedDueDate) {
        this.isApiInProgress = false;
        this.toasterService.showWarning("Kindly choose a date for updation", 10000);
        return;
    }

    // Convert selectedDueDate to moment object for comparison
    const selectedDueDateMoment = moment(this.selectedDueDate, "YYYY-MM-DD");

    // Loop through the bulkUpdateTaskList to validate the start_date of each task
    const invalidTask = this.bulkUpdateTaskList.find(task => {
        const taskStartDate = moment(task.start_date, "YYYY-MM-DD");
        return selectedDueDateMoment.isBefore(taskStartDate);  // Check if the selected due date is before the task's start date
    });

    // If any task violates the rule (due date is before start date), show an error and exit
    if (invalidTask) {
        this.isApiInProgress = false;
        this.toasterService.showError('Selected due date cannot be before the start date of any task.');
        return;  // Exit from the function to prevent further execution
    }

    // Proceed with the bulk update if all tasks pass validation
    await this.myTaskService.bulkUpdateTaskDueDate(
        this.projectId,
        this.itemId,
        this.bulkUpdateTaskList,
        moment(this.selectedDueDate).format("YYYY-MM-DD"),
        this.isAllTaskSelected,
        this.groupDetails
    ).then((res: any) => {
        if (res['messType'] == "S") {
            this.toasterService.showSuccess(res['messText'], 10000);
            this.selectedDueDate = null;
            this.isApiInProgress = false;
            this.closeOverlay();
            this.closeToolbar();
            this.refreshData();
        } else {
            this.toasterService.showError(res['messText'] ? res['messText'] : 'Error while updating task due date');
            this.isApiInProgress = false;
        }
    });
    
    this.isApiInProgress = false;
}



  closeToolbar() {
    if (!this.bulkEditData) return;

    this.bulkEditData.forEach((obj) => {
      obj.isHovered = false;
      obj.isProcessing = false;
    });

    this.bulkUpdateTaskCount = 0;
    this.bulkUpdateTaskList = [];
    this.uncheckAllTasks()
    this.isAllTaskSelected = false;
  }

  // Function to uncheck all tasks
  uncheckAllTasks() {
    // Iterate through noGroupBy tasks
    this.taskDetails.noGroupBy = this.taskDetails.noGroupBy.map(task => {
      task.isChecked = false;
      return task;
    });

    // Iterate through other groupBy categories if any
    for (const groupKey in this.taskDetails) {
      if (groupKey !== 'noGroupBy' && Array.isArray(this.taskDetails[groupKey])) {
        this.taskDetails[groupKey] = this.taskDetails[groupKey].map(task => {
          task.isChecked = false;
          return task;
        });
      }
    }
  }

  onGobalCheckboxChange(event: MatCheckboxChange) {
    if (event.checked) {
      this.isAllTaskSelected = true;
      this.bulkUpdateTaskCount = this.taskHeaderCount;
      this.checkAllTasks()
    } else {
      this.isAllTaskSelected = false;
      this.bulkUpdateTaskCount = 0;
      this.uncheckAllTasks()
    }
  }

  checkAllTasks() {
    // Iterate through noGroupBy tasks
    this.taskDetails.noGroupBy = this.taskDetails.noGroupBy.map(task => {
      task.isChecked = true;
      return task;
    });

    // Iterate through other groupBy categories if any
    for (const groupKey in this.taskDetails) {
      if (groupKey !== 'noGroupBy' && Array.isArray(this.taskDetails[groupKey])) {
        this.taskDetails[groupKey] = this.taskDetails[groupKey].map(task => {
          task.isChecked = true;
          return task;
        });
      }
    }
  }

  async refreshData(){
    this.dataloading = true;
    this.filterConfig.skip = 0;
    this.filterConfig.limit = 15;
    this.isAllTaskSelected = false;

    this.taskDetails = {
      noGroupBy: []
    };

    await this.getMyFavouriteTask();

    if(!this.filterConfig.isGroupBy){
      this.GroupByDetails[0].icon.forEach(icon => {
        icon.isSelected = false;
      })
      let sortData = this.columns
      .filter(column => column.isSortActive && column.sortOrder !== 0)
      .map(column => ({
        key: column.column_value_key,
        order: column.sortOrder
      }));
      if (this.favouriteTaskList.length > 0 && sortData.length === 0) {
        this.filterConfig.isFavList = 1;
        this.filterConfig.favTaskList = this.favouriteTaskList;
        await this.getTaskdetails(this.filterConfig)
      }
      this.filterConfig.isFavList = 0;
      await this.getTaskdetails(this.filterConfig)
    }else{
      this.getGroupByData(this.filterConfig.groupByKey)
    }
    this.dataloading = false;
  }

  async addPeopleToTaskSearch(userSearchParams) {
    await this.myTaskService.addPeopleToTaskSearch(this.projectId, this.itemId, userSearchParams).then((res: any) => {
      if (res['messType'] == "S") {
        this.stakeholdersList = res['data'];
        this.stakeholdersList.forEach(stakeholder => {
          stakeholder.isSelected = this.selectedStakeholdersList.includes(stakeholder.associate_id);
        });
      } else if (res['messType'] === 'E') {
        this.toasterService.showError(res['messText'] ? res['messText'] : 'Error while retrieving stakeholders details');
      }
    })
  }

  async onEnterOwnerSearch(userSearchParams){
    await this.addPeopleToTaskSearch(userSearchParams);
  }

  async clearOwnerSearch(){
    this.userSearchParams = ''
    await this.addPeopleToTaskSearch(this.userSearchParams);
  }

  modifyStakeholder(member) {
    if (member.isSelected) {
      this.selectedStakeholdersList = this.selectedStakeholdersList.filter(id => id !== member.associate_id);
      member.isSelected = false;
    } else {
      this.selectedStakeholdersList.push(member.associate_id);
      member.isSelected = true;
    }
  }

  async closeOwnerOverlay(){
    this.userSearchParams = "";
    this.selectedStakeholdersList = [];
    await this.addPeopleToTaskSearch(this.userSearchParams);
    this.isApiInProgress = false;
    this.closeOverlay();
  }

  async updateTaskAssignedTo(task){
    this.isApiInProgress = true;
    const isAllowed = await this.authService.getProjectWiseObjectAccess(this.projectId, this.itemId, 311)
    if(!isAllowed){
      this.toasterService.showWarning("Access denied: You are not authorized to update the task assigned to.", 10000);
      this.isApiInProgress = false;
      this.closeOverlay();
      return;
    }
    await this.myTaskService.updateInlineAssignedTo(this.projectId, this.itemId, task.id, this.selectedStakeholdersList, task.gantt_id).then((res: any) => {
      if (res['messType'] == "S") {
        task.assigned_to = this.selectedStakeholdersList;
        this.userSearchParams = "";
        this.selectedStakeholdersList = [];
        this.addPeopleToTaskSearch(this.userSearchParams);
        this.closeOverlay();
        this.isApiInProgress = false;
        this.toasterService.showSuccess(res['messText'],10000);

      } else {
        this.isApiInProgress = false;
        this.toasterService.showError(res['messText'] ? res['messText'] : 'Error while updating task details');
      }
    })
    this.isApiInProgress = false;
  }

  toggleSummaryCustomization(){
    if(this.isSummaryActive){
      this.isSummaryActive = false;
    }else{
      this.openSummaryCustomization();
    }
    this.calculateDynamicStyle();
  }

  openSummaryCustomization(): void {
    this.tempSummaryData = JSON.parse(JSON.stringify(this.summaryData));

    const overlayConfig = new OverlayConfig({
      hasBackdrop: true,
      backdropClass: "cdk-overlay-dark-backdrop",
      positionStrategy: this.overlay
        .position()
        .global()
        .centerHorizontally()
        .centerVertically(),
    });

    this.overlayRef = this.overlay.create(overlayConfig);

    this.overlayRef
      .backdropClick()
      .subscribe(() => this.closeSummaryCustomization());

    const portal = new TemplatePortal(
      this.summaryCustomizationTemplate,
      this.viewContainerRef
    );
    this.overlayRef.attach(portal);
  }

  closeSummaryCustomization(): void {
    if (this.overlayRef) {
      this.overlayRef.dispose();
      this.overlayRef = null;
      this.isSummaryActive = false;
    }
  }

  applySummaryCustomization(): void {
    this.summaryData = JSON.parse(JSON.stringify(this.tempSummaryData));
    this.closeSummaryCustomization();
    this.isSummaryActive = true;
    this.calculateDynamicStyle();
  }

  dropSummaryField(event: CdkDragDrop<string[]>): void {
    moveItemInArray(
      this.tempSummaryData,
      event.previousIndex,
      event.currentIndex
    );
  }

  toggleSelectAll(event: any): void {
    this.isSelectAllSummary = event.checked;
    this.tempSummaryData.forEach((item) => {
      if (!item.isDefaultVisible) {
        item.is_visible = this.isSelectAllSummary;
      }
    });
  }

  updateSelectAllStatus(): void {
    this.isSelectAllSummary = this.tempSummaryData.every(
      (item) => item.is_visible || item.isDefaultVisible
    );
  }

}
