import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from "@angular/common/http";
import { LoginService } from 'src/app/services/login/login.service';

@Injectable({
  providedIn: 'root'
})
export class MyTaskService {
  currentUser = this.loginService.getProfile().profile;

  constructor(private http: HttpClient, private loginService: LoginService) { }

  getMyTaskSummaryCount(projectId, itemId, mode, filterConfig){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/planning/getMyTaskSummaryCount",{projectId, itemId, mode, filterConfig}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }
  getMyTaskListView(projectId, itemId, mode, filterConfig){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/planning/getMyTaskListView",{projectId, itemId, mode, filterConfig}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }
  getMyTaskCountForgroupBy(projectId, itemId, mode, key){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/planning/getMyTaskCountForgroupBy",{projectId: projectId, itemId: itemId, mode: mode, key:key}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }
  updateinlineTaskStatus(projectId, itemId, taskIds, statusId,actualStartDate,actualEndDate){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/planning/updateMyTaskStatus",{projectId,itemId,taskIds,statusId,actualStartDate,actualEndDate}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }
  updateinlineTaskPriority(projectId, itemId, taskIds, priority){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/planning/updateMyTaskPriority",{projectId,itemId,taskIds,priority}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }
  updateinlineTaskStartDate(projectId, itemId, taskIds, date){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/planning/updateMyTaskStartDate",{projectId,itemId,taskIds,date}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }
  updateinlineTaskEndDate(projectId, itemId, taskIds, date){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/planning/updateMyTaskEndDate",{projectId,itemId,taskIds,date}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }
  getMyTaskDetails(projectId, itemId,taskId){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/planning/getTaskDetailView",{projectId, itemId, taskId}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }
  addPeopleToTaskSearch(projectId, itemId, searchParam){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/planning/addPeopleToTaskSearch",{projectId, itemId, searchParam}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }
  getMyFavouriteTask(projectId, itemId){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/planning/getMyFavourites",{projectId, itemId}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }
  markTaskAsFavourite(projectId, itemId,taskId){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/planning/insertIntoMongoFav",{projectId, itemId, taskId}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }
  removeTaskFromFavourite(projectId, itemId,taskId){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/planning/removeFromMongoFav",{projectId, itemId, taskId}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }
  
  updateTaskDetails(projectId, itemId, taskId, data){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/planning/updateTaskDetails",{projectId, itemId, taskId, data}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }

  updateInlineAssignedTo(projectId, itemId, taskId, assignedTo, ganttId){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/planning/updateAssignedTo",{projectId, itemId, taskId, assignedTo, ganttId}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }
  bulkUpdateTaskStatus(projectId, itemId, taskIds, statusId, bulkUpdateAll, groupDetails){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/planning/bulkUpdateTaskStatus",{projectId,itemId,taskIds,statusId, bulkUpdateAll, groupDetails}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }
  bulkUpdateTaskPriority(projectId, itemId, taskIds, priorityId, bulkUpdateAll, groupDetails){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/planning/bulkUpdateTaskPriority",{projectId,itemId,taskIds,priorityId, bulkUpdateAll, groupDetails}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }
  bulkUpdateTaskAssignedTo(projectId, itemId, taskIds, assignedTo, bulkUpdateAll, groupDetails){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/planning/bulkUpdateTaskAssignedTo",{projectId,itemId,taskIds,assignedTo, bulkUpdateAll, groupDetails}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }
  bulkUpdateTaskDueDate(projectId, itemId, taskIds, dueDate, bulkUpdateAll, groupDetails){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/planning/updateBulkEndDate",{projectId,itemId,taskIds,dueDate, bulkUpdateAll, groupDetails}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }
  deleteTaskDetails(projectId, itemId, taskIds){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/planning/deleteMyTaskBulk",{projectId,itemId,taskIds}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }
  updateinlineTaskActualStartDate(projectId, itemId, taskIds, date){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/planning/updateinlineTaskActualStartDate",{projectId,itemId,taskIds,date}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }
  updateinlineTaskActualEndDate(projectId, itemId, taskIds, date){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/planning/updateinlineTaskActualEndDate",{projectId,itemId,taskIds,date}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }
}
