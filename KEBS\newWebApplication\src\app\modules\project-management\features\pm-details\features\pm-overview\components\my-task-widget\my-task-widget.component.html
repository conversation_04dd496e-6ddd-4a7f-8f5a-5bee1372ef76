<div class="card task-style">
    
        <!---------------------------Loader------------------------------------>
        <ng-container *ngIf="isComponentLoading">
            <div class="row loader d-flex justify-content-center pt-2">
                <mat-spinner class="green-spinner" matTooltip="Please wait..." diameter="30"> </mat-spinner>
            </div>
        </ng-container>
        <!---------------------------List Data------------------------------------>
        <ng-container *ngIf="!isComponentLoading">
            <div style="margin-top: 15px;margin-left: 15px;display: flex;margin-bottom: 10px;">
                <div class="col-10 p-0 m-0">
                    <span class="header-text">My Task</span>
                </div>
                <div class="col-3 p-0 m-0">
                    <div class="menu-col" [matMenuTriggerFor]="viewMenu">
                        <span class="header-col">{{ defaultFiltervalue }}</span>
                        <mat-icon class="icon-button">keyboard_arrow_down</mat-icon>
                    </div>
                    <mat-menu class="menu-list-task" #viewMenu="matMenu" style="min-width: 50px !important">
                        <div class="pl-1 pr-1">
                            <div *ngFor="let list of filterList">
                                <div mat-menu-item (click)="getFilterValue(list.id)" matTooltip="{{ list.name }}">
                                    <span class="valueGrey14ForIcon" style="font-size: 13px; letter-spacing: 0.02em">{{
                                        list.name }}</span>
                                </div>
                            </div>
                        </div>
                    </mat-menu>
                </div>
            </div>
            <div class="row" *ngIf="!isComponentLoading">
                <div class="col-5 m-0 chart-class">
                    <div class="d-flex">
                        <div class="col-12 justify-content-center m-0">
                            <dx-pie-chart id="taskPie" type="doughnut" [dataSource]="temp"
                                [customizePoint]="customizePoint" centerTemplate="centerTemplate"
                                (onPointClick)="pointClickHandler($event)" [innerRadius]="0.75">
                                <dxo-size [height]="heightValue" [width]="widthValue">
                                </dxo-size>
                                <dxo-legend [visible]="false" [margin]="1" horizontalAlignment="right"
                                    verticalAlignment="top"></dxo-legend>
                                <dxi-series argumentField="task_name" valueField="task_count">
                                </dxi-series>
                                <svg *dxTemplate="let pieChart of 'centerTemplate'">
                                    <circle cx="100" cy="100" [attr.r]="pieChart.getInnerRadius() - 6" fill="none">
                                    </circle>
                                    <text text-anchor="middle" class="font-family"
                                        style="color: var(--Blue-Grey-100, #45546E);" x="100"
                                        y="100">
                                        <tspan x="100" style="font-weight: 600; font-size:16px">{{
                                            calculateTotal(pieChart)
                                            }}</tspan>
                                        <!-- display: inline -->
                                        <tspan x="100" dy="20px" style="font-weight: 400; font-size:14px">Total</tspan>
                                    </text>
                                </svg>
                            </dx-pie-chart>
                            <div style="display: flex;justify-content: end;">
                                <span class="progress-header">Completion </span>
                                <span class="progress-header" style="margin-left: 4px;">{{totalPercent}} %</span>
                            </div>
                        </div>
                    </div>
                    <div style="place-content: space-evenly;margin-left: 10px;">
                        <ng-container *ngFor="let data of dataList">
                            <div style="align-items: center;display: flex;gap: 5px;cursor: pointer;" (click)="getTaskBystatus(data.id)">
                                <div class="circle" [ngStyle]="{'background': getColor(data.id) }"></div>
                                <span class="status-text">{{data.task_name}} ({{data.task_count}})</span>
                            </div>
                        </ng-container>
                    </div>
                    <!-- <div class="row mt-4 pl-2" style="display: block;">
                        <mat-progress-bar mode="determinate" [value]="totalPercent"></mat-progress-bar>
                    </div> -->

                </div>
                <div class="col-7 pl-3 m-0 pr-0">
                    <mat-tab-group [selectedIndex]="selectedVal" (selectedTabChange)="onValChange($event)"
                        class="tab-group">
                        <mat-tab class="tab-class" *ngFor="let tab of tabList; let index = index" [label]="tab.label">
                            <ng-container *ngFor="let data of taskList" class="tab-container">
                                <div class="row d-flex line-item">
                                    <div class="col-9 d-flex p-0 m-0">
                                        <mat-icon class="check-icon"
                                            [ngStyle]="{'color': colorIcon}">check_circle_outline</mat-icon>
                                        <span class="line-text">{{data.task_description}}</span>
                                    </div>
                                    <div class="col-3 d-flex pl-0 pr-2 m-0 justify-content-end">
                                        <span class="line-sub">{{ data.task_end_date != '' ? (data.task_end_date | date: "dd-MMM-yyyy") : '-' }}</span>
                                    </div>
                                </div>
                            </ng-container>
                            <ng-container  *ngIf="taskList.length == 0">
                                <div class="row mt-4 d-flex justify-content-center">
                                    <img [src]="noDataImage ? noDataImage : 'https://assets.kebs.app/No-milestone-image.png'" class="img-data" />
                                </div>
                                <div class="row mt-3 d-flex justify-content-center">
                                    <span class="noDataTxt">No Task Found</span>
                                </div>
                            </ng-container>
                        </mat-tab>
                    </mat-tab-group>


                </div>
            </div>
            <!---------------------------NO Data------------------------------------>
            <!-- <ng-container  *ngIf="!isComponentLoading && noDataImg">
                <div class="row mt-4 d-flex justify-content-center">
                    <img src="https://assets.kebs.app/No-milestone-image.png" class="img-data" />
                </div>
                <div class="row mt-3 d-flex justify-content-center">
                    <span class="noDataTxt">No Data Found</span>
                </div>
            </ng-container> -->
        </ng-container>



</div>