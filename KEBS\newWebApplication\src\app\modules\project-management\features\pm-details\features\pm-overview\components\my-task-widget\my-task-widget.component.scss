.task-style {
  width: var(--taskWidgetWidth);
  height: 365px;
  box-shadow: 0 0px 0px 0 rgba(0, 0, 0, 0.2), 0 0px 5px 0 rgba(0, 0, 0, 0.19);
  border-radius: 0px;
  min-width: 596px;
  max-width: 994px;
  margin-left: 0.5rem;
}

// margin-right: 30px;
.task-card {
  border: 1px solid var(--Blue-Grey-30, #e8e9ee);
  box-shadow: none;
  min-height: 365px;
}
.header-text {
  color: #45546e;
  font-family: var(--myTaskFont) !important;
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 16px; /* 114.286% */
  text-transform: capitalize;
}
.header-col {
  font-family: var(--myTaskFont) !important;
  font-size: 11px;
  font-weight: 400;
  line-height: 15px;
  letter-spacing: 0.02em;
  text-align: left;
  color: #5f6c81;
}
.icon-button {
  color: #5f6c81;
  font-size: 15px;
}
.menu-col {
  cursor: pointer;
  display: flex;
  margin-left: 26px;
}
.chart-class {
  border-right: 1px solid #d4d6d8;
}
::ng-deep .mat-tab-label {
  height: 39px;
  padding: 0 20px;
  cursor: pointer;
  box-sizing: border-box;
  opacity: 0.6;
  min-width: 90px !important;
  text-align: center;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  white-space: nowrap;
  position: relative;
}
::ng-deep .mat-tab-group.mat-primary .mat-ink-bar,
.mat-tab-nav-bar.mat-primary .mat-ink-bar {
  background-color: var(--dynamicColor) !important;
}
// ::ng-deep .mat-tab-label, .mat-tab-link {
//     color: var(--dynamicColor) !important;
// }
::ng-deep .mat-tab-label:focus:not(.mat-tab-disabled) {
  opacity: 1;
  color: var(--dynamicColor) !important;
}

::ng-deep .mat-tab-nav-bar,
.mat-tab-header {
  border-bottom: none !important;
}
.line-item {
  border-bottom: 1px solid var(--Blue-Grey-30, #e8e9ee);
  margin-top: 12px;
  padding-bottom: 10px;
}
.line-text {
  color: var(--Blue-Grey-80, #5f6c81);
  font-family: var(--myTaskFont) !important;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 16px; /* 145.455% */
  letter-spacing: 0.22px;
  text-transform: capitalize;
  width: 90%;
  text-overflow: ellipsis;
  overflow: hidden;
  text-wrap: nowrap;
  padding-left: 3px;
}
.line-sub {
  color: var(--Blue-Grey-80, #5f6c81);
  font-family: var(--myTaskFont) !important;
  font-size: 11px;
  font-style: normal;
  font-weight: 400;
  line-height: 16px; /* 145.455% */
  letter-spacing: 0.22px;
  text-transform: capitalize;
}
.tab-container {
  height: 31vh;
  overflow: auto;
}
::ng-deep .mat-tab-body-content {
  height: 32vh !important;
  overflow: auto;
}
::ng-deep .mat-progress-bar {
  display: block;
  height: 11px;
  overflow: hidden;
  position: relative;
  transition: opacity 250ms linear;
  width: 89%;
  border-radius: 4px;
}
.progress-header {
  color: var(--Blue-Grey-60, #8b95a5);
  font-family: var(--myTaskFont) !important;
  font-size: 11px;
  font-style: normal;
  font-weight: 400;
  line-height: 16px; /* 145.455% */
  letter-spacing: 0.22px;
  text-transform: capitalize;
  padding-top: 3px;
  display: block;
  text-wrap: nowrap;
  margin-left: -30%;
}
::ng-deep .mat-progress-bar-buffer {
  background: #d9d9d9 !important;
}
::ng-deep .mat-progress-bar-fill::after {
  background-color: #ee4961 !important;
  border-radius: 12px;
  background: repeating-linear-gradient(
    45deg,
    #ee4961,
    #ee4961 10px,
    #dc1616 10px,
    #b12222 13px
  );
}
::ng-deep .green-spinner circle {
  stroke: var(--myTaskButton) !important;
}
.loader {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60vh;
}
.circle-content {
  // position: absolute;
  left: 61px;
  margin-top: 1px;
  height: 13px;
  width: 13px;
  background: white;
  border-radius: 50%;
  border-width: thick;
  display: inline-block;
  border: 1px solid #5f6c81;
}
.circle-item-content {
  font-size: 10px;
  position: absolute;
  top: 3.9px;
  left: 1.5px;
  color: #5f6c81;
  font-weight: bold;
}
.circle {
  border-radius: 50%;
  height: 9px;
  width: 9px;
  // align-items: center;
  // justify-content: center;
  // display: flex;
  // border: 1px solid #E6E6E6;
  // cursor: pointer;
}
.status-text {
  font-family: var(--myTaskFont) !important;
  font-size: 10px;
  font-weight: 550;
  letter-spacing: 0.02em;
  text-align: left;
  color: #5f6c81;
}
.check-icon {
  font-size: 19px;
  padding-left: 3px;
}
.img-data {
  width: 80.4px;
  height: 80px;
  margin-top: 43px;
}
.noDataTxt {
  font-family: Roboto;
  font-size: 12px;
  font-weight: 550;
  line-height: 16px;
  text-align: center;
  color: #45546e;
  display: block;
}

.font-family {
  font-family: var(--myTaskFont) !important;
}
::ng-deep .menu-list-task .mat-menu-content {
  padding: 0 !important;
  max-height: 23vh !important;
  margin-bottom: 10px;
}

::ng-deep .menu-list-task .mat-menu-item {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  cursor: pointer;
  outline: none;
  border: none;
  -webkit-tap-highlight-color: transparent;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
  line-height: 48px !important;
  height: 33px !important;
  padding: 0 16px !important;
  text-align: left;
  text-decoration: none;
  max-width: 100%;
  position: relative;
}
