import { Component, HostListener, OnInit } from '@angular/core';
import { PmOverviewService } from '../../services/pm-overview.service';
import { Router } from '@angular/router';
import moment from 'moment';
import * as _ from 'underscore';
import { PmMasterService } from 'src/app/modules/project-management/services/pm-master.service';
@Component({
  selector: 'app-my-task-widget',
  templateUrl: './my-task-widget.component.html',
  styleUrls: ['./my-task-widget.component.scss']
})
export class MyTaskWidgetComponent implements OnInit {

  colorIcon: string = '#377DFF';
  
  filterList = [
    {
      "id": "week",
      "name": "This Week"
  },
  {
      "id": "month",
      "name": "This Month"
  },
  {
      "id": "year",
      "name": "This Year"
  },
  ];
  dataList: any = [];

  taskList: any = [];
  tabList: any = [
    {
      id: 1,
      label: 'Open',
      statusId:7,
      color: '#377DFF'
    },
    {
      id: 2,
      label: 'Execution',
      statusId:4,
      color: '#FFBD3D',
    },
    {
      id: 3,
      label: 'Overdue',
      statusId:-1,
      color: '#EE4961'
    },
    {
      id: 4,
      label: 'Completed',
      statusId:5,
      color: '#A0D911'
    }
  ];
  selectedVal: number;
  defaultFilterId:string = 'month';
  defaultFiltervalue: string;
  
  activityDataList:any = [];
  widthValue: number = 200;
  heightValue: number = 210;
  project_id: number;
  item_id: number; 
  isComponentLoading:boolean = true;
  noDataImg:boolean = false;
  totalPercent:number = 0;
  startDate:any;
  endDate:any;
  statusId:number = 7 // Open Status Id is 7;
  totalCount:number = 0;
  formConfig: any = [];
  noDataImage: any;
  button: any;
  fontStyle: any;
  temp:any=[]
  constructor(private _pmOverviewService: PmOverviewService, private _router: Router, private pmMasterService: PmMasterService) { }

  async ngOnInit() {
    this.calculateDynamicStyle();
    this.project_id = parseInt(this._router.url.split('/')[3]);
    this.item_id = parseInt(this._router.url.split('/')[5]);
    await this.pmMasterService.getPMFormCustomizeConfigV().then((res: any) => {
      if (res) {
        this.formConfig = res;
      }
    });
    const retrieveStyles2 = _.where(this.formConfig, { type: "project-theme", field_name: "styles", is_active: true });
    if(retrieveStyles2.length > 0){
      this.noDataImage = retrieveStyles2[0].data.no_data_image ? retrieveStyles2[0].data.no_data_image : "https://assets.kebs.app/No-milestone-image.png";
      this.button = retrieveStyles2[0].data.button_color ? retrieveStyles2[0].data.button_color : "#79BA44";
      this.fontStyle = retrieveStyles2.length > 0 ? retrieveStyles2[0].data.font_style ? retrieveStyles2[0].data.font_style : "Roboto" : "Roboto";
      document.documentElement.style.setProperty('--myTaskFont', this.fontStyle);
    }
    const taskNameChange = _.where(this.formConfig, { type: "automatic", field_name: "taskNameChange", is_active: true });
    if(taskNameChange.length > 0){
      this.tabList=[
        {
          id: 1,
          label: 'Yet to Start',
          statusId:7,
          color: '#377DFF'
        },
        {
          id: 2,
          label: 'In Progress',
          statusId:4,
          color: '#FFBD3D',
        },
        {
          id: 3,
          label: 'Overdue',
          statusId:-1,
          color: '#EE4961'
        },
        {
          id: 4,
          label: 'Completed',
          statusId:5,
          color: '#A0D911'
        }
      ];

    }

    document.documentElement.style.setProperty('--myTaskButton', this.button)
    await this.getFilterValue(this.defaultFilterId);
    this.selectedVal = 0;
    this.setDynamicColor()
  }

  async getFilterValue(id: string) {
    let filterValue = this.filterList.find((item) => item.id === id);
    this.defaultFiltervalue = filterValue['name'];
    this.defaultFilterId = filterValue['id']
    // console.log(this.defaultFiltervalue);
    let val = filterValue['id']
    if (val === 'month') {
      this.startDate = moment().startOf('month').format('YYYY-MM-DD');
      this.endDate = moment().endOf('month').format('YYYY-MM-DD');
    } else if (val === 'week') {
      this.startDate = moment().startOf('week').format('YYYY-MM-DD');
      this.endDate = moment().endOf('week').format('YYYY-MM-DD');
    } else if (val === 'year') {
      this.startDate = moment().startOf('year').format('YYYY-MM-DD');
      this.endDate = moment().endOf('year').format('YYYY-MM-DD');
    }
    await this.getWidgetData()

  }

  pointClickHandler(arg) {
    this.selectedVal = arg.target.data.id - 1;
    let value = this.tabList.find((item) => item.id === arg.target.data.id);
    this.colorIcon = value['color'];
    this.statusId = value['statusId']
    this.getTaskListData();
    this.setDynamicColor()
  }

  onValChange = (event) => {
    console.log('Event & Tab:', event)
    // this.selectedVal = tab.id;
    const selectedItem = this.tabList.find(item => item.id === event.index + 1);

    if (selectedItem) {
      this.colorIcon = selectedItem.color;
      this.statusId = selectedItem.statusId
      this.getTaskListData();
    }
    this.setDynamicColor()
  };

  calculateTotal(pieChart) {
    return this.totalCount
  }

  @HostListener('window:resize', ['$event'])
  onResize(event) {
    if (window.innerWidth > 1528) {
      this.widthValue = 150;
      this.heightValue = 160;
    }
    else {
      this.widthValue = 200;
      this.heightValue = 210;
    }
    this.calculateDynamicStyle();
  }

  calculateDynamicStyle() {
    let dynamicWidth = window.innerWidth - 626 + 'px';
    document.documentElement.style.setProperty(
      '--taskWidgetWidth',
      dynamicWidth
    );
    
  }

  customizePoint = (point) => {
   let fillColor;
   let value = this.temp.find((item) => item.task_name === point.argument);
   let colorValue = this.tabList.find((item) => item.id === value['id']);
  //  console.log('This Total count',this.totalCount);
   fillColor = (this.totalCount != 0 ?  colorValue['color'] : '#D9D9D9');
    return { color: fillColor };
  };

  async getMyTaskCountList() {
    let result = [];
    await this._pmOverviewService
      .getMyTaskList(this.project_id, this.item_id,this.startDate,this.endDate)
      .then((res: any) => {
        if (res) {
          if (res.messType == 'S' && res.data.length > 0) {
            result = res.data;
          }
        }
      });
    return result;
  }

  async getMyTaskListData() {
    let result = [];
    await this._pmOverviewService
      .getMyTaskListWithData(this.project_id, this.item_id,this.startDate,this.endDate)
      .then((res: any) => {
        if (res) {
          if (res.messType == 'S' && res.data.length > 0) {
            result = res.data;
          }
        }
      });
    return result;
  }

  filterNoData(data){
    if (data.length > 0) {
      const { total_count, completed_count } = data.reduce((acc, item) => {
        acc.total_count += item['id'] != 3 ? item['task_count'] : 0;
        acc.completed_count += item['id'] === 4 ? item['task_count'] : 0;
        return acc;
      }, { total_count: 0, completed_count: 0 });
      if(total_count != 0){
        this.totalPercent = parseFloat(((completed_count / total_count ) * 100).toFixed(2));
      } else this.totalPercent = 0;
      
      if (total_count === 0) {
        this.noDataImg = true;
      }
      this.totalCount = total_count;
    }
    else{
      this.noDataImg = true;
    }    
  }

  getTaskListData(){
    this.taskList = this.activityDataList.filter((item) => item.status_id === this.statusId);
  }

  async getWidgetData(){
    this.isComponentLoading = true;
    this.dataList = await this.getMyTaskCountList();
    this.temp=[]
    for(let item of this.dataList){
      if(item['id']!=3){
        this.temp.push(item)
      }
    }
    this.filterNoData(this.dataList);

    this.activityDataList = await this.getMyTaskListData();
    this.getTaskListData(); 

    this.isComponentLoading = false;
  }

  getColor(id){
    let colorValue = this.tabList.find((item) => item.id === id);
    if (colorValue !== undefined && this.totalCount != 0) {
      return colorValue.color;
  } else {
      return '#D9D9D9';
  }
  }

  setDynamicColor(){
    let color = this.colorIcon;
    if(this.totalCount == 0){
      color = '#D9D9D9'
    }
    document.documentElement.style.setProperty(
      '--dynamicColor',
      color
    )
  }
  getTaskBystatus(id:any){
    this.selectedVal = id - 1;
    let value = this.tabList.find((item) => item.id === id);
    this.colorIcon = value['color'];
    this.statusId = value['statusId']
    this.getTaskListData();
    this.setDynamicColor()
  }

}
