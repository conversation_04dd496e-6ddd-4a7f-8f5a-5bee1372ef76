import {
  <PERSON><PERSON><PERSON>w<PERSON><PERSON><PERSON>,
  <PERSON>mponent,
  <PERSON><PERSON><PERSON><PERSON>,
  HostListener,
  Input,
  OnInit,
  ViewChild,
} from "@angular/core";
import { PmMasterService } from "src/app/modules/project-management/services/pm-master.service";
import { PmOverviewService } from "./../../services/pm-overview.service";
import { PmLandingPageService } from "./../../../../../pm-landing-page/features/pm-project-landing-page/services/pm-landing-page.service";
import * as _ from "underscore";
import moment from "moment";
import {
  MatDialog,
  MatDialogConfig,
  MatDialogRef,
} from "@angular/material/dialog";
import { EditProjectComponent } from "src/app/modules/project-management/features/pm-creation/features/project-creation/edit-project/edit-project/edit-project.component";
import { PmAuthorizationService } from "src/app/modules/project-management/services/pm-authorization.service";
import { AfterSaveDialogComponent } from "src/app/modules/project-management/features/pm-creation/features/project-creation/after-save-dialog/after-save-dialog.component";
import { ToasterService } from "src/app/services/toaster-service/toaster.service";
import { StatusChangeWizardComponent } from "../status-change-wizard/status-change-wizard.component";
import { ToasterMessageService } from "src/app/modules/project-management/shared-lazy-loaded/components/toaster-message/toaster-message.service";
import { ProjectNameDisplayService } from "../../../../services/project-name-display.service";
import { FlagChangeWizardComponent } from "../flag-change-wizard/flag-change-wizard.component";
import { DelinkOpportunityComponent } from "../delink-opportunity/delink-opportunity.component";
import { ApprovalSuccessPageComponent } from "../approval-success-page/approval-success-page.component";
import { FlagChangeWizardWithStepperComponent } from "../flag-change-wizard-with-stepper/flag-change-wizard-with-stepper.component";
import { Router } from "@angular/router";
@Component({
  selector: "app-pm-overview-header",
  templateUrl: "./pm-overview-header.component.html",
  styleUrls: ["./pm-overview-header.component.scss"],
})
export class PmOverviewHeaderComponent implements OnInit, AfterViewInit {
  @ViewChild("overviewHeaderCard", { static: false })
  overviewHeaderCard: ElementRef;
  @Input() project_id: any;
  @Input() portfolio_id: any;
  @Input() widget_config: any;
  status_list: any[];
  people_list: any[];
  tags_list: any;
  width: any;
  height: any;
  font_size: any;
  dueMessage: any;
  completion_percentage: any;
  progress_color: any;
  tagPresent: boolean = false;
  pmPresent: boolean = false;
  service_type_list: any[];
  diffInDays: any;
  emp_name: any;
  project_details: any;
  formConfig: any;
  percentageCalc: any;
  start_date: any;
  end_date: any;
  loading: boolean;
  multiPM: boolean = false;
  status_label: any;
  status_font_color: any;
  currentDate = moment().format();
  count: number = 0;
  button: any;
  card: any;
  shade: any;
  secondary: any;
  v3:any;
  secondaryShade: any;
  textColor: any;
  round: any;
  type: any;
  image: any;
  projectData: any;
  headerHeight: any;
  retrieveMessages: any;
  projectUpdationAccess: boolean = false;
  status_list_for_project: any = [];
  fontStyle: any;
  tags: any = [
    {
      id: 1,
      name: "dev",
      color: "red",
    },
    {
      id: 2,
      name: "design",
      color: "blue",
    },
    {
      id: 3,
      name: "planning",
      color: "lightgreen",
    },
  ];
  flagChangeAccess: boolean = false;
  flagChangeObject: boolean = false;
  statusUpdationAccess: boolean = false;
  valueChain: any;
  valueChainLabel: any;
  isMigrated: boolean = false;
  superadminaccess:boolean=false;
  save_status: boolean = false;

  moreOption: any = [
    { id: 1, name: "Edit Project" },
    { id: 2, name: "Change Status" },
    { id: 3, name: "Flag Change" },
    { id: 4, name: "Delink Opportunity" },
  ];
  constructor(
    private pmMasterService: PmMasterService,
    private pmOverviewService: PmOverviewService,
    private pmAuthService: PmAuthorizationService,
    private pmlandingService: PmLandingPageService,
    private dialog: MatDialog,
    private toasterService: ToasterMessageService,
    private nameService: ProjectNameDisplayService,
    private router: Router
  ) {}

  async ngOnInit() {
    this.loading = true;
    this.width = 30;
    this.height = 30;
    this.font_size = 14;
    this.calculateDynamicStyle();
    await this.canAccess()

    this.statusUpdationAccess =
      await this.pmAuthService.getProjectWiseObjectAccess(
        this.portfolio_id,
        this.project_id,
        112
      );
    this.flagChangeAccess =
      await this.pmAuthService.getProjectWiseObjectAccess(
        this.portfolio_id,
        this.project_id,
        97
      );
     this.projectUpdationAccess =
      await this.pmAuthService.getProjectWiseObjectAccess(
        this.portfolio_id,
        this.project_id,
        9
      );
    

    await this.pmAuthService
      .getReadWriteAccess(this.portfolio_id, this.project_id)
      .then(async (res) => {
        if (res) {
          this.flagChangeObject =await this.pmAuthService.getProjectWiseObjectAccess(this.portfolio_id,this.project_id,110);
          
        }
      });

    await this.pmMasterService.getPMFormCustomizeConfigV().then((res: any) => {
      if (res) {
        this.formConfig = res;
        this.v3=_.where(this.formConfig, { type: "landing-header", field_name: "version", is_active: true });

        const progress_form = _.where(this.formConfig, {
          type: "project-overview",
          field_name: "progress_calculation",
          is_active: true,
        });

        this.type =
          progress_form.length > 0
            ? progress_form[0].calculation_type
            : "weighted";
        const image_form = _.where(this.formConfig, {
          type: "project-overview-header",
          field_name: "image",
          is_active: true,
        });

        const retrieveStyles = _.where(this.formConfig, {
          type: "project-theme",
          field_name: "styles",
          is_active: true,
        });
        if (retrieveStyles.length > 0) {
          this.card = retrieveStyles[0].data.overview_card
            ? retrieveStyles[0].data.overview_card
            : "#F7F9FB";
          document.documentElement.style.setProperty("--card", this.card);

          this.shade = retrieveStyles[0].data.overview_shade
            ? retrieveStyles[0].data.overview_shade
            : "#ffe4e1";
          document.documentElement.style.setProperty("--shade", this.shade);

          this.round = retrieveStyles[0].data.overview_round
            ? retrieveStyles[0].data.overview_round
            : "#F8AFA7";
          document.documentElement.style.setProperty("--round", this.round);

          this.secondary = retrieveStyles[0].data.overview_secondary
            ? retrieveStyles[0].data.overview_secondary
            : "#FF3A46";
          document.documentElement.style.setProperty(
            "--secondary1",
            this.secondary
          );

          this.fontStyle =
            retrieveStyles.length > 0
              ? retrieveStyles[0].data.font_style
                ? retrieveStyles[0].data.font_style
                : "Roboto"
              : "Roboto";
          document.documentElement.style.setProperty(
            "--overviewFont",
            this.fontStyle
          );

          this.secondaryShade = retrieveStyles[0].data.overview_shade_2
            ? retrieveStyles[0].data.overview_shade_2
            : "#FFEBEC";
          document.documentElement.style.setProperty(
            "--secondary2",
            this.secondaryShade
          );

          this.textColor = retrieveStyles[0].data.toggle_color
            ? retrieveStyles[0].data.toggle_color
            : "#D3D3D3";
          document.documentElement.style.setProperty(
            "--durationText",
            this.textColor
          );

          this.progress_color = this.secondary;

          this.image = retrieveStyles[0].data.overview_header_image
            ? retrieveStyles[0].data.overview_header_image
            : "-";
        }
        this.retrieveMessages = _.where(this.formConfig, {
          type: "project-creation",
          field_name: "messages",
          is_active: true,
        });
      }
    });

    await this.pmOverviewService
      .getProjectOverviewCardDetails(
        this.project_id,
        this.portfolio_id,
        this.currentDate,
        this.type
      )
      .then((res: any) => {
        if (res) {
          this.project_details = res;
          this.completion_percentage = this.project_details.progress_percent
            ? this.project_details.progress_percent
            : 0;
          this.start_date = moment(
            this.project_details.planned_start_date
          ).format("DD-MMM-YYYY");
          this.end_date = moment(this.project_details.planned_end_date).format(
            "DD-MMM-YYYY"
          );
          this.valueChain = this.project_details?.value_chain;
          this.valueChainLabel = this.project_details?.value_chain
            ? this.project_details?.value_chain_label
            : null;
          this.isMigrated = this.project_details?.is_migrated;

          const startDate = this.project_details.planned_start_date;
          const endDate = this.project_details.planned_end_date;

          if (
            this.project_details.planned_end_date &&
            this.project_details.planned_start_date
          ) {
            const msg_form = _.where(this.formConfig, {
              type: "project-overview-header",
              field_name: "messages",
              is_active: true,
            });
            const days_left =
              msg_form.length > 0 ? msg_form[0].days_left_msg : " days left";
            const days_delayed =
              msg_form.length > 0
                ? msg_form[0].days_delayed_msg
                : " days delayed";
            const days_toStart =
              msg_form.length > 0
                ? msg_form[0].days_toStart_msg
                : " days to start";
            const completed_msg =
              msg_form.length > 0 ? msg_form[0].completed_msg : "Completed";
            const started = moment(startDate).diff(
              moment(this.currentDate).endOf("day"),
              "days"
            );
            const onhold_msg = msg_form.length> 0 ? msg_form[0].onhold_msg :"Onhold";
            const cancelled_msg = msg_form.length> 0 ? msg_form[0].cancelled_msg :"Cancelled";
            const closed_msg = msg_form.length> 0 ? msg_form[0].closed_msg :"Closed";
            if(this.project_details.status_id === 6)
            { 
              this.dueMessage = cancelled_msg;
            }
            else if(this.project_details.status_id === 12) 
            {
              this.dueMessage = onhold_msg;
            } 
            else if(this.project_details.status_id === 14){
              this.dueMessage=closed_msg;
            }
            else if (
              started < 0 &&
              (this.project_details.status_id == 4 ||
                this.project_details.status_id == 7)
            ) {
              this.diffInDays = moment(endDate).diff(
                moment(this.currentDate).endOf("day"),
                "days"
              );

              if (this.diffInDays >= 0) {
                this.dueMessage = this.diffInDays + days_left;
              } else if (this.diffInDays < 0) {
                const absolute = Math.abs(this.diffInDays);
                this.dueMessage = absolute + days_delayed;
              }
            } else if (
              this.project_details.status_id !== 4 &&
              this.project_details.status_id !== 7
            ) {
              this.dueMessage = completed_msg;
            } else if (
              started >= 0 &&
              (this.project_details.status_id == 4 ||
                this.project_details.status_id == 7)
            ) {
              this.dueMessage = started + days_toStart;
            }
          }

          this.tags_list = this.project_details.activityTags;
          if (this.tags_list) {
            this.tagPresent = true;
          }
        }
      });

    await this.pmMasterService.getStatusList().then((res: any) => {
      if (res) {
        this.status_list = this.pmMasterService.status_list;
      }
    });
    for (let item of this.status_list) {
      if (this.project_details.status_id == 7) {
        if (item["id"] == 4) {
          this.status_list_for_project.push(item);
        }
        if (item["id"] == 6) {
          this.status_list_for_project.push(item);
        }
        if (item["id"] == 12) {
          this.status_list_for_project.push(item);
        }
      }
      if (this.project_details.status_id == 4) {
        if (item["id"] == 5) {
          this.status_list_for_project.push(item);
        }
        if (item["id"] == 12) {
          this.status_list_for_project.push(item);
        }
        if (item["id"] == 6) {
          this.status_list_for_project.push(item);
        }
      }
      if (this.project_details.status_id == 5) {
        if (item["id"] == 14) {
          this.status_list_for_project.push(item);
        }
      }
      if (this.project_details.status_id == 12) {
        if (item["id"] == 7) {
          this.status_list_for_project.push(item);
        }
        if (item["id"] == 4) {
          this.status_list_for_project.push(item);
        }
        if (item["id"] == 6) {
          this.status_list_for_project.push(item);
        }
      }
    }
    const status = this.status_list.find(
      (status) => status.id == this.project_details.status_id
    );
    if (status) {
      this.status_font_color = "white";
      this.status_label = status.status_color
        ? status.status_color
        : "lightgrey";
    }
    document.documentElement.style.setProperty(
      "--statusLabel",
      this.status_label
    );
    document.documentElement.style.setProperty(
      "--statusFont",
      this.status_font_color
    );
    document.documentElement.style.setProperty(
      "--spinner",
      this.progress_color
    );

    await this.pmMasterService.getProjectTypeList().then((res: any) => {
      if (res) {
        this.service_type_list = this.pmMasterService.service_type_list;
      }
    });

    await this.pmOverviewService
      .getCurrentProjectManager(
        this.project_id,
        this.portfolio_id,
        this.currentDate
      )
      .then((res: any) => {
        if (res) {
          this.people_list = res["data"];
          if (
            this.people_list &&
            this.people_list.length &&
            this.people_list.length > 0
          ) {
            this.pmPresent = true;
            this.count = this.people_list.length;
            if (this.count > 1) {
              this.multiPM = true;
            } else if (this.count == 1) {
              this.emp_name = this.people_list[0].employee_name;
            }
          }
        }
      });
    this.getProjectData();
    if(this.project_details.status_id==14 || this.project_details.status_id==5 || this.project_details.status_id==12){
      this.save_status=true
    }
    this.loading = false;
  }
  canAccess(){
    return new Promise(async(resolve, reject)=>{
      await this.pmAuthService.getAdminAccess().then((res)=>{
          if(res['messType']=="S")
          {
            this.superadminaccess=true
              resolve(true);
          }
          else
          {
              resolve(false)
          }
      })
    })
   
  }
  ngAfterViewInit() {
    const element = this.overviewHeaderCard.nativeElement;
    const computedStyle = window.getComputedStyle(element);
    const height = computedStyle.height;
  }
  getStatusName(id: any) {
    const status_name = this.status_list.find((status) => status.id === id);
    if (status_name) {
      return status_name.name;
    } else {
      return null;
    }
  }
  getServiceName(id: any) {
    const service_name = this.service_type_list.find(
      (service) => service.d === id
    );
    if (service_name) {
      return service_name.name;
    } else {
      return null;
    }
  }
  getTagName(id: any) {
    const tag_name = this.tags.find((tag) => tag.id === id);
    if (tag_name) {
      return tag_name.name;
    } else {
      return null;
    }
  }

  async getProjectData() {
    this.loading = true;
    this.projectData = undefined;
    await this.pmlandingService
      .getProjectCardData(this.project_id, this.portfolio_id, this.currentDate)
      .then((res) => {
        if (res["messType"] == "S") {
          this.projectData = [];
          this.projectData = res["data"];
          this.loading = false;
        }
      });
  }

  editProjectDetails(projectData: any) {
    if (this.projectUpdationAccess && this.project_details.status_id!==6) {
      if(this.v3 && this.v3.length >0){
        
        this.router.navigateByUrl(`/main/project-management/create/ project?projectId=${this.portfolio_id}&itemId=${this.project_id}`);
        console.log(`/main/project-management/create/project?projectId=${this.portfolio_id}&itemId=${this.project_id}`)

      }else{

      const dialogRef = this.dialog.open(EditProjectComponent, {
        disableClose: true,
        data: {
          mode: "Edit",
          data: projectData,
        },
      });
      dialogRef.afterClosed().subscribe((result) => {
        if (result.messType == "S") {
          this.getRefreshedOverviewHeader();
          this.getProjectData();
          this.nameService.getProjectName(this.portfolio_id, this.project_id);
          const editSuccess =
            this.retrieveMessages.length > 0
              ? this.retrieveMessages[0].success.edit_success
                ? this.retrieveMessages[0].success.edit_success
                : "Project Edited Successfully"
              : "Project Edited Successfully";
          this.toasterService.showSuccess(editSuccess, 10000);
        } else if (result.messType == "P") {
          this.router.navigateByUrl("/main/project-management/landing/home");
        }
      });}
    } else {
      const access_restrict_msg =
        this.retrieveMessages.length > 0
          ? this.retrieveMessages[0].success.access_restrict_msg
            ? this.retrieveMessages[0].success.access_restrict_msg
            : "You are not having access to Edit Project"
          : "You are not having access to Edit Project";
      this.toasterService.showError(access_restrict_msg);
      //this.toastr.showWarning("Access Restricted","You are not having access to Edit Project")
    }
  }
  getMoreOptionValue(id, data, event: Event) {
    if (id == 1) {
      this.editProjectDetails(data);
    }
    if (id == 2) {
      if (this.statusUpdationAccess) {
        if (
          (this.project_details.status_id != 6 &&
          this.project_details.status_id != 14) || this.superadminaccess
        ) {
          const dialogRef = this.dialog.open(StatusChangeWizardComponent, {
            disableClose: true,
            data: {
              data: {
                from: this.project_details.status_id,
                to: id,
                project_id: this.project_id,
                portfolio_id: this.portfolio_id,
              },
            },
          });
          dialogRef.afterClosed().subscribe(async (result) => {
            if (result.messType == "S") {
              const statusChangeSuccess = this.retrieveMessages[0].success
                .statusChangeSuccess
                ? this.retrieveMessages[0].success.statusChangeSuccess
                  ? this.retrieveMessages[0].success.statusChangeSuccess
                  : "Status Changed Successfully"
                : "Status Changed Successfully";
              this.toasterService.showSuccess(statusChangeSuccess, 10000);
              await this.pmAuthService
                .getReadWriteAccess(this.portfolio_id, this.project_id)
                .then(async (res) => {
                  if (res) {
                    this.projectUpdationAccess =
                      await this.pmAuthService.getProjectWiseObjectAccess(
                        this.portfolio_id,
                        this.project_id,
                        9
                      );
                  }
                });
                this.getRefreshedOverviewHeader();
                this.getProjectData();
                setTimeout(() => {
                  this.refreshComponent(); // Call refreshComponent after a delay
                }, 2000);
            }
          });
        } else if (this.project_details.status_id == 14) {
          const statusChange_close_msg =
            this.retrieveMessages.length > 0
              ? this.retrieveMessages[0].errors.statusChange_close_msg
                ? this.retrieveMessages[0].errors.statusChange_close_msg
                : "Status cannot be changed for closed project"
              : "Status cannot be changed for closed project";
          this.toasterService.showWarning(statusChange_close_msg, 10000);
        } else {
          const statusChange_cancel_msg =
            this.retrieveMessages.length > 0
              ? this.retrieveMessages[0].errors.statusChange_cancel_msg
                ? this.retrieveMessages[0].errors.statusChange_cancel_msg
                : "Status cannot be changed for cancelled project"
              : "Status cannot be changed for cancelled project";
          this.toasterService.showWarning(statusChange_cancel_msg, 10000);
        }
      } else {
        const statusChange_access_msg =
          this.retrieveMessages.length > 0
            ? this.retrieveMessages[0].errors.statusChange_access_msg
              ? this.retrieveMessages[0].errors.statusChange_access_msg
              : "Access restricted to change Project Status"
            : "Access restricted to change Project Status";
        this.toasterService.showWarning(statusChange_access_msg, 10000);
      }
    }
    if(id==3){
      if(this.flagChangeObject)
      {
        if(this.project_details.status_id==7){
          if(this.flagChangeObject){
            const dialogRef=this.dialog.open(FlagChangeWizardComponent, {
              disableClose: true,
              data: {
                data: {from:this.project_details.status_id, to:id,project_id:this.project_id,portfolio_id:this.portfolio_id,at_risk:this.project_details.at_risk,with_opportunity:this.project_details.with_opportunity,customer_id:this.project_details.end_customer_id,profit_center:this.project_details.profit_center,project_name:this.project_details.project_name}
              },
          });
            dialogRef.afterClosed().subscribe(async(result) => {
              if (result.messType == 'S') {
                this.toasterService.showSuccess("Risk Status Updated successfully", 10000)    
                this.getRefreshedOverviewHeader()          
              }
              else if(result.messType == 'L'){
                this.getRefreshedOverviewHeader()
              }
            });
          }
          else{
        const dialogRef=this.dialog.open(FlagChangeWizardWithStepperComponent, {
          disableClose: true,
          data: {
            data: {from:this.project_details.status_id, to:id,project_id:this.project_id,portfolio_id:this.portfolio_id,at_risk:this.project_details.at_risk,with_opportunity:this.project_details.with_opportunity,customer_id:this.project_details.end_customer_id,profit_center:this.project_details.profit_center,project_name:this.project_details.project_name}
          },
      });
        dialogRef.afterClosed().subscribe(async(result) => {
          if (result.messType == 'S') {
            this.toasterService.showSuccess("Flag Change Action Success", 10000)
            this.getRefreshedOverviewHeader() 
           
          }
          else if(result.messType == 'L'){
            this.getRefreshedOverviewHeader()
          }
        });
        }
        }
        else{
          this.toasterService.showWarning('This Action not possible for respective project status', 10000)
        }
      }
      else
      {
        const flagChange_access_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.flagChange_access_msg ? this.retrieveMessages[0].errors.flagChange_access_msg : 'Access restricted to change Project Flag' : 'Access restricted to change Project Flag';
            this.toasterService.showWarning(flagChange_access_msg, 10000)
      }
    }
    if (id == 4){
      if(this.superadminaccess)
        {  
              const dialogRef=this.dialog.open(DelinkOpportunityComponent, {
                disableClose: true,
                data: {
                  data: {from:this.project_details.status_id, to:id,project_id:this.project_id,portfolio_id:this.portfolio_id,at_risk:this.project_details.at_risk,with_opportunity:this.project_details.with_opportunity,customer_id:this.project_details.end_customer_id,profit_center:this.project_details.profit_center,project_name:this.project_details.project_name}
                },
            });
              dialogRef.afterClosed().subscribe(async(result) => {
                if (result.messType == 'S') {
                  this.toasterService.showSuccess("Updated successfully", 10000)    
                  this.getRefreshedOverviewHeader()          
                }
                else if(result.messType == 'L'){
                  this.getRefreshedOverviewHeader()
                }
              });
        }
        else
        {
          const flagChange_access_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.delinkChange_access_msg ? this.retrieveMessages[0].errors.flagChange_access_msg : 'Access restricted to Delink Opportunity' : 'Access restricted';
              this.toasterService.showWarning(flagChange_access_msg, 10000)
        }

    }
  }
  refreshComponent() {
    // Get the current URL
    //const currentUrl = this.router.url;
    //console.log("url"+currentUrl)

    // Navigate to the current URL again to "refresh" the component
    //this.router.navigateByUrl(currentUrl)
    window.location.reload();
  }
  changeStatusDisplay(id) {
    if (id == 12) {
      this.pmOverviewService
        .moveProjectToOnHold(
          this.portfolio_id,
          this.project_id,
          this.project_details.status_id,
          null,
          null,
          []
        )
        .then((res) => {
          if (res["messType"] == "S") {
            const statusChangeSuccess = this.retrieveMessages[0].success
              .statusChangeSuccess
              ? this.retrieveMessages[0].success.statusChangeSuccess
                ? this.retrieveMessages[0].success.statusChangeSuccess
                : "Status Changed Successfully"
              : "Status Changed Successfully";
            this.toasterService.showSuccess(statusChangeSuccess, 10000);
            this.getRefreshedOverviewHeader();
          }
        });
    } else if (this.project_details.status_id == 12 && id == 7) {
      this.pmOverviewService
        .moveProjectToOpen(
          this.portfolio_id,
          this.project_id,
          this.project_details.status_id,
          null,
          null
        )
        .then((res) => {
          if (res["messType"] == "S") {
            const statusChangeSuccess = this.retrieveMessages[0].success
              .statusChangeSuccess
              ? this.retrieveMessages[0].success.statusChangeSuccess
                ? this.retrieveMessages[0].success.statusChangeSuccess
                : "Status Changed Successfully"
              : "Status Changed Successfully";
            this.toasterService.showSuccess(statusChangeSuccess, 10000);
            this.getRefreshedOverviewHeader();
          }
        });
    } else {
      const dialogRef = this.dialog.open(StatusChangeWizardComponent, {
        disableClose: true,
        data: {
          data: {
            from: this.project_details.status_id,
            to: id,
            project_id: this.project_id,
            portfolio_id: this.portfolio_id,
          },
        },
      });
      dialogRef.afterClosed().subscribe((result) => {
        if (result.messType == "S") {
          const statusChangeSuccess = this.retrieveMessages[0].success
            .statusChangeSuccess
            ? this.retrieveMessages[0].success.statusChangeSuccess
              ? this.retrieveMessages[0].success.statusChangeSuccess
              : "Status Changed Successfully"
            : "Status Changed Successfully";
          this.toasterService.showSuccess(statusChangeSuccess, 10000);
          this.getRefreshedOverviewHeader();
          this.getProjectData();
          setTimeout(() => {
            this.refreshComponent(); // Call refreshComponent after a delay
          }, 2000);
        }
      });
    }
  }

  @HostListener("window:resize")
  onResize() {
    this.calculateDynamicStyle();
  }
  /**
   * @description For Calculating Dynamic Height
   */
  calculateDynamicStyle() {
    let overviewWidth = window.innerWidth - 135 + "px";
    document.documentElement.style.setProperty(
      "--overviewWidth",
      overviewWidth
    );
  }
  encodeURIComponent(str) {
    return encodeURIComponent(str).replace(/[!'()*]/g, function (c) {
      return "%" + c.charCodeAt(0).toString(16);
    });
  }


/**
   * @description Opens opporutnity in new tab when Right click open in new tab is clicked
   */
navigateToAccounts(endCustomerId,customerName)
    {
      if(endCustomerId){
        let navigationUrl = `${window.location.origin}/main/accounts/${endCustomerId}/`+this.encodeURIComponent(customerName)+`/overview`;
        window.open(navigationUrl);}
        else{
          this.toasterService.showWarning("Navigation Not Allowed", 10000)
        }
      
    }


  async getRefreshedOverviewHeader() {
    this.loading = true;
    this.status_list_for_project = [];
    await this.pmOverviewService
      .getProjectOverviewCardDetails(
        this.project_id,
        this.portfolio_id,
        this.currentDate,
        this.type
      )
      .then((res: any) => {
        if (res) {
          this.project_details = res;
          this.completion_percentage = this.project_details.progress_percent
            ? this.project_details.progress_percent
            : 0;
          this.start_date = moment(
            this.project_details.planned_start_date
          ).format("DD-MMM-YYYY");
          this.end_date = moment(this.project_details.planned_end_date).format(
            "DD-MMM-YYYY"
          );

          const startDate = this.project_details.planned_start_date;
          const endDate = this.project_details.planned_end_date;
          if (
            this.project_details.planned_end_date &&
            this.project_details.planned_start_date
          ) {
            const msg_form = _.where(this.formConfig, {
              type: "project-overview-header",
              field_name: "messages",
              is_active: true,
            });
            const days_left =
              msg_form.length > 0 ? msg_form[0].days_left_msg : " days left";
            const days_delayed =
              msg_form.length > 0
                ? msg_form[0].days_delayed_msg
                : " days delayed";
            const days_toStart =
              msg_form.length > 0
                ? msg_form[0].days_toStart_msg
                : " days to start";
            const completed_msg =
              msg_form.length > 0 ? msg_form[0].completed_msg : "Completed";
            const onhold_msg = msg_form.length> 0 ? msg_form[0].onhold_msg :"Onhold";
            const cancelled_msg = msg_form.length> 0 ? msg_form[0].cancelled_msg :"Cancelled";
            const closed_msg = msg_form.length> 0 ? msg_form[0].closed_msg :"Closed";

            const started = moment(startDate).diff(
              moment(this.currentDate).endOf("day"),
              "days"
            );
            if(this.project_details.status_id === 6)
            { 
              this.dueMessage = cancelled_msg;
            }
            else if(this.project_details.status_id === 12) 
            {
              this.dueMessage = onhold_msg;
            } 
            else if(this.project_details.status_id === 14){
              this.dueMessage=closed_msg;
            }
            else if (
              started < 0 &&
              (this.project_details.status_id == 4 ||
                this.project_details.status_id == 7)
            ) {
              this.diffInDays = moment(endDate).diff(
                moment(this.currentDate).endOf("day"),
                "days"
              );
              if (this.diffInDays >= 0) {
                this.dueMessage = this.diffInDays + days_left;
              } else if (this.diffInDays < 0) {
                const absolute = Math.abs(this.diffInDays);
                this.dueMessage = absolute + days_delayed;
              }
            } else if (
              this.project_details.status_id !== 4 &&
              this.project_details.status_id !== 7
            ) {
              this.dueMessage = completed_msg;
            }
            
            else if (
              started >= 0 &&
              (this.project_details.status_id == 4 ||
                this.project_details.status_id == 7)
            ) {
              this.dueMessage = started + days_toStart;
            }
          }
          this.tags_list = this.project_details.activityTags;
          if (this.tags_list) {
            this.tagPresent = true;
          }
        }
      });

    await this.pmMasterService.getStatusList().then((res: any) => {
      if (res) {
        this.status_list = this.pmMasterService.status_list;
      }
    });
    for (let item of this.status_list) {
      if (this.project_details.status_id == 7) {
        if (item["id"] == 4) {
          this.status_list_for_project.push(item);
        }
        if (item["id"] == 6) {
          this.status_list_for_project.push(item);
        }
        if (item["id"] == 12) {
          this.status_list_for_project.push(item);
        }
      }
      if (this.project_details.status_id == 4) {
        if (item["id"] == 5) {
          this.status_list_for_project.push(item);
        }
        if (item["id"] == 12) {
          this.status_list_for_project.push(item);
        }
        if (item["id"] == 6) {
          this.status_list_for_project.push(item);
        }
      }
      if (this.project_details.status_id == 5) {
        if (item["id"] == 14) {
          this.status_list_for_project.push(item);
        }
      }
      if (this.project_details.status_id == 12) {
        if (item["id"] == 7) {
          this.status_list_for_project.push(item);
        }
        if (item["id"] == 4) {
          this.status_list_for_project.push(item);
        }
        if (item["id"] == 6) {
          this.status_list_for_project.push(item);
        }
      }
    }
    const status = this.status_list.find(
      (status) => status.id == this.project_details.status_id
    );
    if (status) {
      this.status_font_color = status.font_color ? status.font_color : "white";
      this.status_label = status.label_color ? status.label_color : "lightgrey";
    }
    document.documentElement.style.setProperty(
      "--statusLabel",
      this.status_label
    );
    document.documentElement.style.setProperty(
      "--statusFont",
      this.status_font_color
    );
    document.documentElement.style.setProperty(
      "--spinner",
      this.progress_color
    );

    await this.getProjectData();
    this.loading = false;
  }
}
