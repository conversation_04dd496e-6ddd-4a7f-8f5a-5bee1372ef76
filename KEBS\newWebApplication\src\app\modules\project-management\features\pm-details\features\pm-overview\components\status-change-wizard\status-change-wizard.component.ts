import { Component, OnInit, Inject, InjectionToken } from '@angular/core';
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import * as _ from 'underscore';
import { PmMasterService } from 'src/app/modules/project-management/services/pm-master.service';
import { trigger, state, style, transition, animate } from '@angular/animations';
import { PmOverviewService } from './../../services/pm-overview.service';
import { truncateSync } from 'fs';
import { ToasterService } from 'src/app/services/toaster-service/toaster.service';
import { ToasterMessageService } from 'src/app/modules/project-management/shared-lazy-loaded/components/toaster-message/toaster-message.service';
import { PmAuthorizationService } from 'src/app/modules/project-management/services/pm-authorization.service';
import { GetNameByIdPipe } from 'src/app/modules/project-management/shared-lazy-loaded/pipes/get-name-by-id.pipe';

export const TOASTER_STATUS_MESSAGE_SERVICE_TOKEN = new InjectionToken<ToasterMessageService>('TOASTER_STATUS_MESSAGE_SERVICE_TOKEN');


@Component({
  selector: 'app-status-change-wizard',
  templateUrl: './status-change-wizard.component.html',
  styleUrls: ['./status-change-wizard.component.scss'],
  animations: [
    trigger('loadingState', [
      state('loading', style({
        opacity: 1,
      })),
      state('notLoading', style({
        opacity: 0,
      })),
      transition('notLoading => loading', animate('300ms ease-in')),
      transition('loading => notLoading', animate('300ms ease-out')),
    ]),
  ],
  providers:[
    {
      provide: TOASTER_STATUS_MESSAGE_SERVICE_TOKEN, useClass: ToasterMessageService
    },
    GetNameByIdPipe
  ]
})
export class StatusChangeWizardComponent implements OnInit {
  loading: boolean = true
  formConfig: any
  items: any=[]
  data: any
  status_list: any
  isLoading: boolean = false
  save: boolean = false
  saveDisabled: boolean = true
  stepperData: any
  stepper_color: any;
  fromStepper: boolean = true
  toStepper: boolean = false
  enableNext: boolean = true
  projectStarted: any
  button_name: any
  items_actions: any=[]
  enable_action: boolean = false
  enable_error: boolean = false
  currentlyCheckedValue: number = null;
  launchGif: boolean = false
  fontStyle: any;
  selectedStatus:any='Select'
  selectedStatusId:number
  lastStepper:boolean=false
  isInternal:any;
  status_list_for_project:any=[];
  retrieveMessages: any = [];
  oldStatus: any;
  newStatus: any;
  superadminaccess:boolean=false;
  
status_history:any  
constructor(@Inject(TOASTER_STATUS_MESSAGE_SERVICE_TOKEN) private toasterService: ToasterMessageService, 
    private pmOverviewService: PmOverviewService, 
    public dialogRef: MatDialogRef<StatusChangeWizardComponent>, 
    @Inject(MAT_DIALOG_DATA) public dialogData: DialogData, 
    private pmMasterService: PmMasterService,
    private pmAuthService: PmAuthorizationService,
    private getNameByIdPipe: GetNameByIdPipe) { }

 async ngOnInit() {
    console.log(this.dialogData.data)
    this.data = this.dialogData.data
    await this.pmMasterService.getPMFormCustomizeConfigV().then((res) => {
      this.formConfig = res
    })
    await this.pmMasterService.getStatusList().then((res) => {
      if (res) {
        this.status_list = res;
      }
    })
    await this.canAccess()
    await this.pmOverviewService.getStatusHistory(this.dialogData.data.portfolio_id, this.dialogData.data.project_id).then((res)=>{
       if(res['messType']=='S' && res['data'].length>0){
        this.status_history=res['data']
       }
       else{
        this.status_history=[]
       }
    })
    await this.pmOverviewService.getProjectType(this.dialogData.data.portfolio_id, this.dialogData.data.project_id).then((res)=>{
      if(res['messType']=='S'){
       this.isInternal=res['data']
      }
   })
    for (let item of this.status_list) {
      if (item['id'] == this.data.from) {
        this.data['from_name'] = item['name']
      }
      if(this.data.from==7){
        if(item['id']==4){
          this.status_list_for_project.push(item)
        }
        if(item['id']==6){
          this.status_list_for_project.push(item)
        }
        if(item['id']==12){
          this.status_list_for_project.push(item)
        }
        if(this.superadminaccess && item['id']==5){
          this.status_list_for_project.push(item)
        }
        if(this.superadminaccess && item['id']==14){
          this.status_list_for_project.push(item)
        }
      }
      if(this.data.from==4){
        if(item['id']==5){
          this.status_list_for_project.push(item)
        }
        if(item['id']==12){
          this.status_list_for_project.push(item)
        }
        if(item['id']==6){
          this.status_list_for_project.push(item)
        }
        if(this.superadminaccess && item['id']==7){
          this.status_list_for_project.push(item)
        }
        if(this.superadminaccess && item['id']==14){
          this.status_list_for_project.push(item)
        }
      }
      if(this.data.from==5){
        if(item['id']==14){
          this.status_list_for_project.push(item)
        }
        if(this.superadminaccess && item['id']==7){
          this.status_list_for_project.push(item)
        }
        if(this.superadminaccess && item['id']==12){
          this.status_list_for_project.push(item)
        }
        if(this.superadminaccess && item['id']==4){
          this.status_list_for_project.push(item)
        }
      }
      if(this.data.from==12){
        if(item['id']==7 && this.status_history.length==0){
          this.status_list_for_project.push(item)
        }
        if(item['id']==4 && this.status_history.length==0){
          this.status_list_for_project.push(item)
        }
        if(item['id']==7 && this.status_history.length>0 && !_.contains([4],this.status_history[0].previous_status_id)){
          this.status_list_for_project.push(item)
        }
        if(item['id']==4 && this.status_history.length>0 && !_.contains([7],this.status_history[0].previous_status_id)){
          this.status_list_for_project.push(item)
        }
        if(item['id']==6){
          this.status_list_for_project.push(item)
        }
        if (!_.includes(this.status_list_for_project.map(item => item.id), 7) && this.superadminaccess) {
          if (item['id'] == 7) {
              this.status_list_for_project.push(item);
          }
      }
      
      if (!_.includes(this.status_list_for_project.map(item => item.id), 4) && this.superadminaccess) {
          if (item['id'] == 4) {
              this.status_list_for_project.push(item);
          }
      }
      }
      if(this.superadminaccess && this.data.from==6){       
        if(this.superadminaccess && item['id']==7){
          this.status_list_for_project.push(item)
        }  
      }
      if(this.superadminaccess && this.data.from==14){
        if(this.superadminaccess && item['id']==4){
          this.status_list_for_project.push(item)
        }
        if(this.superadminaccess && item['id']==7){
          this.status_list_for_project.push(item)
        }
        if(this.superadminaccess && item['id']==12){
          this.status_list_for_project.push(item)
        }
        if(this.superadminaccess && item['id']==5){
          this.status_list_for_project.push(item)
        }
      }
  
    }
    this.oldStatus ={
      Status: this.data['from_name']
    }
    this.stepperData = [{
      id: 1,
      data: "1",
      type: "from",
      label: this.data.from_name,
      is_selected: true,
      is_crossed: false,
      is_completed: false,
      is_active: true 
    }, {
      id: 2,
      data: "2",
      type: "to",
      label: 'Select',
      is_selected: false,
      is_crossed: false,
      is_completed: false,
      is_active: true
    }]
    const details = _.where(this.formConfig, { type: "project-creation", field_name: "default_stepper", is_active: true });
    this.stepper_color = details.length > 0 ? details[0].color ? details[0].color : "#90ee90" : "#90ee90";
    document.documentElement.style.setProperty('--stepperColor', this.stepper_color)
const retrieveStyles = _.where(this.formConfig, { type: "project-theme", field_name: "styles", is_active: true });
this.retrieveMessages = _.where(this.formConfig, { type: "status-wizard", field_name: "messages", is_active: true });
this.fontStyle = retrieveStyles.length > 0 ? retrieveStyles[0].data.font_style ? retrieveStyles[0].data.font_style : "Roboto" : "Roboto";
document.documentElement.style.setProperty('--statusWizFont', this.fontStyle);   
this.loading=false
  }
 
  canAccess(){
    return new Promise(async(resolve, reject)=>{
      await this.pmAuthService.getAdminAccess().then((res)=>{
          if(res['messType']=="S")
          {
            this.superadminaccess=true
              resolve(true);
          }
          else
          {
              resolve(false)
          }
      })
    })
   
  }
  onCloseClick() {
    this.initiateStepper()
    this.dialogRef.close({ messType: "E" })
  }
  moveToStatus() {
    let id = []
    let mandate = 0
    if (this.items_actions.length > 0) {

      for (let check of this.items_actions) {
        if (check['mandate'] == true && check['checked'] != true) {
          mandate = 1
          this.toasterService.showWarning(check['label'] + 'mandatory to change status', 10000);
        }
        if (check['checked'] == true) {
          id.push(check['id'])
        }
      }
    }
    this.newStatus = {
      Status: this.getNameByIdPipe.transform(this.data.to,this.status_list)
    }
    if (this.data.to == 4 && mandate == 0) {
      this.projectStarted = "start";
      this.launchGif = true
      this.pmOverviewService.moveProjectToExcuetion(this.dialogData.data.portfolio_id, this.dialogData.data.project_id, [], 0,this.data.from, this.oldStatus, this.newStatus).then((res => {
        if (res['messType'] == 'S') {
          this.initiateStepper()
          this.pmAuthService.updateProjectStatus(this.dialogData.data.portfolio_id, this.dialogData.data.project_id, 4)
          this.dialogRef.close({ messType: "S" })
        }
      }))
    }
    if (this.data.to == 6 && mandate == 0) {
      this.projectStarted = "start";
      this.launchGif = true
      this.pmOverviewService.moveProjectToCancel(this.dialogData.data.portfolio_id, this.dialogData.data.project_id, id,this.data.from, this.oldStatus, this.newStatus).then((res => {
        if (res['messType'] == 'S') {
          this.initiateStepper()
          this.pmAuthService.updateProjectStatus(this.dialogData.data.portfolio_id, this.dialogData.data.project_id, 6)
          this.dialogRef.close({ messType: "S" })
        }
      }))
    }
    if (this.data.to == 14 && mandate == 0) {
      this.projectStarted = "start";
      this.launchGif = true
      this.pmOverviewService.moveProjectToClosed(this.dialogData.data.portfolio_id, this.dialogData.data.project_id, id,this.data.from, this.oldStatus, this.newStatus).then((res => {
        if (res['messType'] == 'S') {
          this.initiateStepper()
          this.pmAuthService.updateProjectStatus(this.dialogData.data.portfolio_id, this.dialogData.data.project_id, 14)
          this.dialogRef.close({ messType: "S" })
        }
      }))
    }
    if (this.data.to == 5 && mandate == 0) {
      this.projectStarted = "start";
      this.launchGif = true
      this.pmOverviewService.moveProjectToComplete(this.dialogData.data.portfolio_id, this.dialogData.data.project_id, id,this.data.from,  this.oldStatus, this.newStatus).then((res => {
        if (res['messType'] == 'S') {
          this.initiateStepper()
          this.pmAuthService.updateProjectStatus(this.dialogData.data.portfolio_id, this.dialogData.data.project_id,5)
          this.dialogRef.close({ messType: "S" })
        }
      }))
    }
    if (this.data.to == 7 && mandate == 0) {
      this.projectStarted = "start";
      this.launchGif = true
      this.pmOverviewService.moveProjectToOpen(this.dialogData.data.portfolio_id, this.dialogData.data.project_id,this.data.from,  this.oldStatus, this.newStatus).then((res => {
        if (res['messType'] == 'S') {
          this.initiateStepper()
          this.pmAuthService.updateProjectStatus(this.dialogData.data.portfolio_id, this.dialogData.data.project_id,7)
          this.dialogRef.close({ messType: "S" })
        }
      }))
    }
    if (this.data.to == 12 && mandate == 0) {
      this.projectStarted = "start";
      this.launchGif = true
      this.pmOverviewService.moveProjectToOnHold( this.dialogData.data.portfolio_id, this.dialogData.data.project_id, id, this.data.from,  this.oldStatus, this.newStatus).then((res => {
        if (res['messType'] == 'S') {
          this.initiateStepper()
          this.pmAuthService.updateProjectStatus(this.dialogData.data.portfolio_id, this.dialogData.data.project_id,12)
          this.dialogRef.close({ messType: "S" })
        }
      }))
    }
  }
  onStepperChange(stepperData: any) {
    this.stepperData = stepperData;
    console.log('changed stepper data', this.stepperData);
    this.stepperControl();
  }
  stepperControl() {
    const index = this.stepperData.findIndex(item => item.is_selected === true);
    if (index !== -1) {
      const selectedStepper = this.stepperData[index].type;
      switch (selectedStepper) {
        case 'from':
          this.fromStepper = true
          this.toStepper = false
          break;

        case 'to':
          this.fromStepper = false
          this.toStepper = true
          break;
        default:
          this.fromStepper = true
          this.toStepper = false
      }
    }
  }
  getNextStepper() {
    console.log("next called", this.stepperData)
    const index = this.stepperData.findIndex(item => item.is_selected === true);
    console.log(index, this.stepperData.length, this.stepperData.length - 2);

    if (index !== -1 && index <= this.stepperData.length - 2) {
      this.stepperData[index].is_selected = false;
      this.stepperData[index].is_crossed = true;
      this.stepperData[index].is_completed = true;
      this.stepperData[index + 1].is_selected = true;
      this.lastStepper=true
    }
    this.stepperControl();

  }
  getPreviousStepper() {
    console.log("previous called", this.stepperData)
    const index = this.stepperData.findIndex(item => item.is_selected === true);
    console.log(index, this.stepperData.length, this.stepperData.length - 1);
    if (index !== -1 && index > 0) {
      this.stepperData[index].is_selected = false;
      this.stepperData[index - 1].is_selected = true;
      this.stepperData[index - 1].is_crossed = false;
      console.log(this.stepperData);
      this.lastStepper=false
    }
    this.stepperControl();
  }
  selectCheckBox(data, index) {
    console.log(data)
    // if(this.currentlyCheckedValue === index) {
    //   this.currentlyCheckedValue = null;
    //   this.items_actions[index].checked=false
    //   return;
    // }

    if (this.items_actions[index].checked == true) {
      this.items_actions[index].checked = false
    }
    else {
      this.items_actions[index].checked = true
    }
    this.currentlyCheckedValue = index;
  }
  initiateStepper(){
    this.stepperData = [{
      id: 1,
      data: "1",
      type: "from",
      label: this.data.from_name,
      is_selected: true,
      is_crossed: false,
      is_completed: false,
      is_active: true
    }, {
      id: 2,
      data: "2",
      type: "to",
      label: this.data.to_name,
      is_selected: false,
      is_crossed: false,
      is_completed: false,
      is_active: true
    }]
  }
  async changeStatusDisplay(id,name) {
    console.log(id)
    this.selectedStatus=name
    this.selectedStatusId=id
    this.data.to=id
    this.loading=true
   await this.automateWizardForStatus()
  }
 async automateWizardForStatus(){
  console.log(this.data.to)
  this.items_actions=[]
  this.items=[]
  this.enableNext=true
  this.enable_action=false
  this.enable_error=false
  this.launchGif=false
  console.log(this.status_list)
  for (let item of this.status_list) {
    if (item['id'] == this.data.from) {
      // let status_color = JSON.parse(item['color'])
      this.data['from_name'] = item['name']
      // this.data['from_color_color'] = status_color[0].color
      // this.data['from_color_background'] = status_color[0].background
    }
    if (item['id'] == this.data.to) {
      // let status_color = JSON.parse(item['color'])
      this.data['to_name'] = item['name']
      // this.data['to_color_color'] = status_color[0].color
      // this.data['to_color_background'] = status_color[0].background
    }

  }
  console.log(this.data)
  console.log(this.formConfig)
  this.stepperData = [{
    id: 1,
    data: "1",
    type: "from",
    label: this.data.from_name,
    is_selected: true,
    is_crossed: false,
    is_completed: false,
    is_active: true
  }, {
    id: 2,
    data: "2",
    type: "to",
    label: this.data.to_name,
    is_selected: false,
    is_crossed: false,
    is_completed: false,
    is_active: true
  }]
  // document.documentElement.style.setProperty('--secondary', this.data.to_color_color);
  if(this.data.from === 7 && this.data.to === 5){
    this.items = _.filter(this.formConfig, item => 
      item.type === "status-wizard" &&
      item.field_name === "validation" &&
      item.is_active === true &&
      ((item.from === 7 &&
      item.to === 4) || (item.from === 4 &&
        item.to === 5)) &&
      Array.isArray(item.Service) && // Ensure 'Service' is an array
      item.Service.includes(this.isInternal));

  }
  else if(this.data.from === 7 && this.data.to === 14){
    this.items = _.filter(this.formConfig, item => 
      item.type === "status-wizard" &&
      item.field_name === "validation" &&
      item.is_active === true &&
      ((item.from === 7 &&
      item.to === 4) || (item.from === 4 &&
        item.to === 5) || (item.from === 5 &&
          item.to === 14)) &&
      Array.isArray(item.Service) && // Ensure 'Service' is an array
      item.Service.includes(this.isInternal));

  }
  else if(this.data.from === 4 && this.data.to === 14){
    this.items = _.filter(this.formConfig, item => 
      item.type === "status-wizard" &&
      item.field_name === "validation" &&
      item.is_active === true &&
      ((item.from === 4 &&
        item.to === 5) || (item.from === 5 &&
          item.to === 14)) &&
      Array.isArray(item.Service) && // Ensure 'Service' is an array
      item.Service.includes(this.isInternal));

  }
  else{
  this.items = _.filter(this.formConfig, item => 
    item.type === "status-wizard" &&
    item.field_name === "validation" &&
    item.is_active === true &&
    item.from === this.data.from &&
    item.to === this.data.to &&
    Array.isArray(item.Service) && // Ensure 'Service' is an array
    item.Service.includes(this.isInternal) // Check if 'isinternal' exists in 'Service' array
  );}

  if(this.data.from === 7 && this.data.to === 5){
    this.items_actions = _.filter(this.formConfig, item => 
      item.type === "status-wizard" &&
      item.field_name === "action" &&
      item.is_active === true &&
      ((item.from === 7 &&
      item.to === 4) || (item.from === 4 &&
        item.to === 5)) &&
      Array.isArray(item.Service) && // Ensure 'Service' is an array
      item.Service.includes(this.isInternal));

  }
  else if(this.data.from === 7 && this.data.to === 14){
    this.items_actions = _.filter(this.formConfig, item => 
      item.type === "status-wizard" &&
      item.field_name === "action" &&
      item.is_active === true &&
     ((item.from === 7 &&
      item.to === 4) || (item.from === 4 &&
        item.to === 5) || (item.from === 5 &&
          item.to === 14))&&
      Array.isArray(item.Service) && // Ensure 'Service' is an array
      item.Service.includes(this.isInternal));

  }
  else if(this.data.from === 4 && this.data.to === 14){
    this.items_actions = _.filter(this.formConfig, item => 
      item.type === "status-wizard" &&
      item.field_name === "action" &&
      item.is_active === true &&
     ((item.from === 4 &&
        item.to === 5) || (item.from === 5 &&
          item.to === 14))&&
      Array.isArray(item.Service) && // Ensure 'Service' is an array
      item.Service.includes(this.isInternal));

  }else{
  
  this.items_actions = _.filter(this.formConfig, item => 
    item.type === "status-wizard" &&
    item.field_name === "action" &&
    item.is_active === true &&
    item.from === this.data.from &&
    item.to === this.data.to &&
    Array.isArray(item.Service) && // Ensure 'Service' is an array
    item.Service.includes(this.isInternal) // Check if 'isinternal' exists in 'Service' array
  );}
  

  this.loading = false
  this.isLoading = true
  let successToNext = 0
  if (this.items.length > 0) {
    this.enable_action = false
  }
  else {
    this.enableNext = false
    if (this.items_actions.length > 0) {
      this.enable_action = true
    }
    else {
      this.launchGif = true
    }
  }
  if (this.items_actions.length == 0) {
    this.launchGif = true
  }
  if (this.data.to == 6) {
    this.button_name = 'Cancel Project'
  }
  if (this.data.to == 14) {
    this.button_name = 'Close Project'
  }
  if (this.data.to == 12) {
    this.button_name = 'Block Project'
  }
  if (this.data.to == 7) {
    this.button_name = 'Open Project'
  }
  if (this.data.from == 7 && this.data.to == 4 && this.isInternal==23) {
    
this.button_name = 'Launch Project'
  }
  if (this.data.from == 12 && this.data.to == 4 && this.isInternal==23){
    this.button_name = 'Launch Project'
  }

  
  for (let item of this.items) {
    // Open status
    if (this.data.from == 7 && this.data.to == 4) {
      if (item['id'] == 1) {
        item['loading'] = true
       await  this.pmOverviewService.getProjectSecured(this.dialogData.data.project_id).then((res) => {
          if (res['messType'] == 'S' && res['data'].length > 0) {
            item['color'] = "#52C41A"
            item['loading'] = false
            item['status'] = 'SUCCESS'
          }
          else {
            item['color'] = "#FF3A46"
            item['loading'] = false
            item['status'] = 'FAILED'
            item['is_error'] = true
            successToNext = 1
          }
        })
      }
      if (item['id'] == 2) {
       await this.pmOverviewService.checkCostingSheetReleased(this.dialogData.data.project_id).then((res) => {
          if (res['messType'] == 'S' && res['data'].length > 0) {
            item['color'] = "#52C41A"
            item['status'] = 'SUCCESS'
            item['loading'] = false
          }
          else {
            item['color'] = "#FF3A46"
            item['loading'] = false
            item['status'] = 'FAILED'
            item['is_error'] = true
            successToNext = 1
          }
        })
      }
      if (item['id'] == 3) {
       await this.pmOverviewService.checkQuoteAvailable(this.dialogData.data.project_id).then((res) => {
          if (res['messType'] == 'S') {
            item['color'] = "#52C41A"
            item['status'] = 'SUCCESS'
            item['loading'] = false
          }
          else {
            item['color'] = "#FF3A46"
            item['loading'] = false
            item['status'] = 'FAILED'
            item['is_error'] = true
            successToNext = 1
          }
        })
      }
      if (item['id'] == 4) {
        await this.pmOverviewService.checkResourceLoadingAvailable(this.dialogData.data.project_id).then((res) => {
           if (res['messType'] == 'S') {
             item['color'] = "#52C41A"
             item['status'] = 'SUCCESS'
             item['loading'] = false
           }
           else {
             item['color'] = "#FF3A46"
             item['loading'] = false
             item['status'] = 'FAILED'
             item['is_error'] = true
             successToNext = 1
           }
         })
       }
       if (item['id'] == 5) {
        await this.pmOverviewService.checkProjectPINMandatory(this.dialogData.data.project_id).then((res) => {
          if (res['messType'] == 'S') {
            item['color'] = "#52C41A"
            item['status'] = 'SUCCESS'
            item['loading'] = false
            item['value']=res['data']
          }
          else {
            item['color'] = "#FF3A46"
            item['loading'] = false
            item['status'] = 'FAILED'
            item['is_error'] = true
            successToNext = 1
          }
        })
      }
       if (item['id'] == 6) {
        await this.pmOverviewService.checkProjectBillingEntity(this.dialogData.data.project_id).then((res) => {
          if (res['messType'] == 'S') {
            item['color'] = "#52C41A"
            item['status'] = 'SUCCESS'
            item['loading'] = false
            item['value']=res['data']
          }
          else {
            item['color'] = "#FF3A46"
            item['loading'] = false
            item['status'] = 'FAILED'
            item['is_error'] = true
            successToNext = 1
          }
        })
      }
      if (item['id'] == 7) {
        await this.pmOverviewService.checkProjectBillingCurrency(this.dialogData.data.project_id).then((res) => {
          if (res['messType'] == 'S') {
            item['color'] = "#52C41A"
            item['status'] = 'SUCCESS'
            item['loading'] = false
            item['value']=res['data']
          }
          else {
            item['color'] = "#FF3A46"
            item['loading'] = false
            item['status'] = 'FAILED'
            item['is_error'] = true
            successToNext = 1
          }
        })
      }
      if (item['id'] == 8) {
        await this.pmOverviewService.checkProjectPaymentTerms(this.dialogData.data.project_id).then((res) => {
          if (res['messType'] == 'S') {
            item['color'] = "#52C41A"
            item['status'] = 'SUCCESS'
            item['loading'] = false
            item['value']=res['data']
          }
          else {
            item['color'] = "#FF3A46"
            item['loading'] = false
            item['status'] = 'FAILED'
            item['is_error'] = true
            successToNext = 1
          }
        })
      }
      if (item['id'] == 9) {
        await this.pmOverviewService.checkOpportunityStatus(this.dialogData.data.project_id).then((res) => {
          if (res['messType'] == 'S') {
            item['color'] = "#52C41A"
            item['status'] = 'SUCCESS'
            item['loading'] = false
            item['value']=res['data']
          }
          else {
            item['color'] = "#FF3A46"
            item['loading'] = false
            item['status'] = 'FAILED'
            item['is_error'] = true
            successToNext = 1
          }
        })
      }
      if (item['id'] == 10) {
        await this.pmOverviewService.checkOpportunityStatusAsWon(this.dialogData.data.project_id).then((res) => {
          if (res['messType'] == 'S') {
            item['color'] = "#52C41A"
            item['status'] = 'SUCCESS'
            item['loading'] = false
            item['value']=res['data']
          }
          else {
            item['color'] = "#FF3A46"
            item['loading'] = false
            item['status'] = 'FAILED'
            item['is_error'] = true
            successToNext = 1
          }
        })
      }
      
      this.button_name = 'Launch Project'
    }
    if (this.data.from == 7 && this.data.to == 6) {
      
      if (item['id'] == 8) {
        let status_ids = item['status_ids']
        await this.pmOverviewService.checkForRevenueMilestones(this.dialogData.data.project_id, status_ids).then((res) => {
           if (res['messType'] == 'S' && res['data'].length > 0) {
             item['color'] = "#FF3A46"
             item['loading'] = false
             item['status'] = 'FAILED'
             item['is_error'] = true
             successToNext = 1
           }
           else {
             item['color'] = "#52C41A"
             item['status'] = 'SUCCESS'
             item['loading'] = false
           }
         })
       }
       if (item['id'] == 9) {
       let status_ids = item['status_ids']
       await this.pmOverviewService.checkMilestoneBilledMilestones(this.dialogData.data.project_id, status_ids).then((res) => {
           if (res['messType'] == 'S' && res['data'].length > 0) {
             item['color'] = "#FF3A46"
             item['loading'] = false
             item['status'] = 'FAILED'
             item['is_error'] = true
             successToNext = 1
           }
           else {
             item['color'] = "#52C41A"
             item['status'] = 'SUCCESS'
             item['loading'] = false
           }
         })
       }
       if (item['id'] == 10) {
        let billable_ids = item['billable_ids']
        await this.pmOverviewService.checkWhetherBillableTimesheet(this.dialogData.data.project_id, billable_ids).then((res) => {
            if (res['messType'] == 'S' && res['data'].length > 0) {
              item['color'] = "#FF3A46"
              item['loading'] = false
              item['status'] = 'FAILED'
              item['is_error'] = true
              successToNext = 1
            }
            else {
              item['color'] = "#52C41A"
              item['status'] = 'SUCCESS'
              item['loading'] = false
            }
          })
       }
       if (item['id'] == 11) {
        await this.pmOverviewService.checkWhetherBillableExpense(this.dialogData.data.project_id).then((res) => {
            if (res['messType'] == 'S' && res['data'].length > 0) {
              item['color'] = "#FF3A46"
              item['loading'] = false
              item['status'] = 'FAILED'
              item['is_error'] = true
              successToNext = 1
            }
            else {
              item['color'] = "#52C41A"
              item['status'] = 'SUCCESS'
              item['loading'] = false
            }
          })
       }
       if (item['id'] == 12) {
        await this.pmOverviewService.checkRequestPresent(this.dialogData.data.project_id).then((res) => {
            if (res['messType'] == 'S' && res['data'].length > 0) {
              item['color'] = "#FF3A46"
              item['loading'] = false
              item['status'] = 'FAILED'
              item['is_error'] = true
              successToNext = 1
            }
            else {
              item['color'] = "#52C41A"
              item['status'] = 'SUCCESS'
              item['loading'] = false
            }
          })
       }
       if (item['id'] == 15){
        await this.pmOverviewService.checkRevenueBilledEqual(this.dialogData.data.portfolio_id, this.dialogData.data.project_id).then((res) => {
          if (res['messType'] == 'S') {
            item['color'] = "#52C41A"
            item['status'] = 'SUCCESS'
            item['loading'] = false
            item['value']=res['data']
          }
          else {
            item['color'] = "#FF3A46"
            item['loading'] = false
            item['status'] = 'FAILED'
            item['is_error'] = true
            successToNext = 1
          }
        })
      }
       this.button_name = 'Cancel Project'
    }
    // Open status-admin
    if (this.data.from == 7 && this.data.to == 5) {
      if (item['id'] == 1 && item['to']==4) {
        item['loading'] = true
       await  this.pmOverviewService.getProjectSecured(this.dialogData.data.project_id).then((res) => {
          if (res['messType'] == 'S' && res['data'].length > 0) {
            item['color'] = "#52C41A"
            item['loading'] = false
            item['status'] = 'SUCCESS'
          }
          else {
            item['color'] = "#FF3A46"
            item['loading'] = false
            item['status'] = 'FAILED'
            item['is_error'] = true
            successToNext = 1
          }
        })
      }
      if (item['id'] == 2) {
       await this.pmOverviewService.checkCostingSheetReleased(this.dialogData.data.project_id).then((res) => {
          if (res['messType'] == 'S' && res['data'].length > 0) {
            item['color'] = "#52C41A"
            item['status'] = 'SUCCESS'
            item['loading'] = false
          }
          else {
            item['color'] = "#FF3A46"
            item['loading'] = false
            item['status'] = 'FAILED'
            item['is_error'] = true
            successToNext = 1
          }
        })
      }
      if (item['id'] == 3) {
       await this.pmOverviewService.checkQuoteAvailable(this.dialogData.data.project_id).then((res) => {
          if (res['messType'] == 'S') {
            item['color'] = "#52C41A"
            item['status'] = 'SUCCESS'
            item['loading'] = false
          }
          else {
            item['color'] = "#FF3A46"
            item['loading'] = false
            item['status'] = 'FAILED'
            item['is_error'] = true
            successToNext = 1
          }
        })
      }
      if (item['id'] == 4) {
        await this.pmOverviewService.checkResourceLoadingAvailable(this.dialogData.data.project_id).then((res) => {
           if (res['messType'] == 'S') {
             item['color'] = "#52C41A"
             item['status'] = 'SUCCESS'
             item['loading'] = false
           }
           else {
             item['color'] = "#FF3A46"
             item['loading'] = false
             item['status'] = 'FAILED'
             item['is_error'] = true
             successToNext = 1
           }
         })
       }
       if (item['id'] == 5) {
        await this.pmOverviewService.checkProjectPINMandatory(this.dialogData.data.project_id).then((res) => {
          if (res['messType'] == 'S') {
            item['color'] = "#52C41A"
            item['status'] = 'SUCCESS'
            item['loading'] = false
            item['value']=res['data']
          }
          else {
            item['color'] = "#FF3A46"
            item['loading'] = false
            item['status'] = 'FAILED'
            item['is_error'] = true
            successToNext = 1
          }
        })
      }
       if (item['id'] == 6) {
        await this.pmOverviewService.checkProjectBillingEntity(this.dialogData.data.project_id).then((res) => {
          if (res['messType'] == 'S') {
            item['color'] = "#52C41A"
            item['status'] = 'SUCCESS'
            item['loading'] = false
            item['value']=res['data']
          }
          else {
            item['color'] = "#FF3A46"
            item['loading'] = false
            item['status'] = 'FAILED'
            item['is_error'] = true
            successToNext = 1
          }
        })
      }
      if (item['id'] == 7) {
        await this.pmOverviewService.checkProjectBillingCurrency(this.dialogData.data.project_id).then((res) => {
          if (res['messType'] == 'S') {
            item['color'] = "#52C41A"
            item['status'] = 'SUCCESS'
            item['loading'] = false
            item['value']=res['data']
          }
          else {
            item['color'] = "#FF3A46"
            item['loading'] = false
            item['status'] = 'FAILED'
            item['is_error'] = true
            successToNext = 1
          }
        })
      }
      if (item['id'] == 8) {
        await this.pmOverviewService.checkProjectPaymentTerms(this.dialogData.data.project_id).then((res) => {
          if (res['messType'] == 'S') {
            item['color'] = "#52C41A"
            item['status'] = 'SUCCESS'
            item['loading'] = false
            item['value']=res['data']
          }
          else {
            item['color'] = "#FF3A46"
            item['loading'] = false
            item['status'] = 'FAILED'
            item['is_error'] = true
            successToNext = 1
          }
        })
      }
      if (item['id'] == 9) {
        await this.pmOverviewService.checkOpportunityStatus(this.dialogData.data.project_id).then((res) => {
          if (res['messType'] == 'S') {
            item['color'] = "#52C41A"
            item['status'] = 'SUCCESS'
            item['loading'] = false
            item['value']=res['data']
          }
          else {
            item['color'] = "#FF3A46"
            item['loading'] = false
            item['status'] = 'FAILED'
            item['is_error'] = true
            successToNext = 1
          }
        })
      }
      if (item['id'] == 10) {
        await this.pmOverviewService.checkOpportunityStatusAsWon(this.dialogData.data.project_id).then((res) => {
          if (res['messType'] == 'S') {
            item['color'] = "#52C41A"
            item['status'] = 'SUCCESS'
            item['loading'] = false
            item['value']=res['data']
          }
          else {
            item['color'] = "#FF3A46"
            item['loading'] = false
            item['status'] = 'FAILED'
            item['is_error'] = true
            successToNext = 1
          }
        })
      }
      if (item['id'] == 1 && item['to']==5) {
        await this.pmOverviewService.checkEmployeeTimsheetBeforeComplete(this.dialogData.data.project_id).then((res) => {
           if (res['messType'] == 'S' && res['data'].length > 0) {
             item['color'] = "#FF3A46"
             item['loading'] = false
             item['status'] = 'FAILED'
             item['is_error'] = true
             successToNext = 1
           }
           else {
             item['color'] = "#52C41A"
             item['status'] = 'SUCCESS'
             item['loading'] = false
           }
         })
       }
       if (item['id'] == 2 && item['to']==5) {
       await this.pmOverviewService.checkMilestoneMovedToYTB(this.dialogData.data.project_id).then((res) => {
           if (res['messType'] == 'S' && res['data'].length > 0) {
             item['color'] = "#FF3A46"
             item['loading'] = false
             item['status'] = 'FAILED'
             item['is_error'] = true
             successToNext = 1
           }
           else {
             item['color'] = "#52C41A"
             item['status'] = 'SUCCESS'
             item['loading'] = false
           }
         })
       }
       if (item['id'] == 13) {
         await this.pmOverviewService.checkRequestPresent(this.dialogData.data.project_id).then((res) => {
             if (res['messType'] == 'S' && res['data'].length > 0) {
               item['color'] = "#FF3A46"
               item['loading'] = false
               item['status'] = 'FAILED'
               item['is_error'] = true
               successToNext = 1
             }
             else {
               item['color'] = "#52C41A"
               item['status'] = 'SUCCESS'
               item['loading'] = false
             }
           })
       }
       if (item['id'] == 15 && item['to']==5){
        await this.pmOverviewService.checkRevenueBilledEqual(this.dialogData.data.portfolio_id, this.dialogData.data.project_id).then((res) => {
          if (res['messType'] == 'S') {
            item['color'] = "#52C41A"
            item['status'] = 'SUCCESS'
            item['loading'] = false
            item['value']=res['data']
          }
          else {
            item['color'] = "#FF3A46"
            item['loading'] = false
            item['status'] = 'FAILED'
            item['is_error'] = true
            successToNext = 1
          }
        })
      }
       this.button_name = 'Complete Project'
    }
    if (this.data.from == 7 && this.data.to == 14) {
      if (item['id'] == 1 && item['to']==4) {
        item['loading'] = true
       await  this.pmOverviewService.getProjectSecured(this.dialogData.data.project_id).then((res) => {
          if (res['messType'] == 'S' && res['data'].length > 0) {
            item['color'] = "#52C41A"
            item['loading'] = false
            item['status'] = 'SUCCESS'
          }
          else {
            item['color'] = "#FF3A46"
            item['loading'] = false
            item['status'] = 'FAILED'
            item['is_error'] = true
            successToNext = 1
          }
        })
      }
      if (item['id'] == 2) {
       await this.pmOverviewService.checkCostingSheetReleased(this.dialogData.data.project_id).then((res) => {
          if (res['messType'] == 'S' && res['data'].length > 0) {
            item['color'] = "#52C41A"
            item['status'] = 'SUCCESS'
            item['loading'] = false
          }
          else {
            item['color'] = "#FF3A46"
            item['loading'] = false
            item['status'] = 'FAILED'
            item['is_error'] = true
            successToNext = 1
          }
        })
      }
      if (item['id'] == 3) {
       await this.pmOverviewService.checkQuoteAvailable(this.dialogData.data.project_id).then((res) => {
          if (res['messType'] == 'S') {
            item['color'] = "#52C41A"
            item['status'] = 'SUCCESS'
            item['loading'] = false
          }
          else {
            item['color'] = "#FF3A46"
            item['loading'] = false
            item['status'] = 'FAILED'
            item['is_error'] = true
            successToNext = 1
          }
        })
      }
      if (item['id'] == 4) {
        await this.pmOverviewService.checkResourceLoadingAvailable(this.dialogData.data.project_id).then((res) => {
           if (res['messType'] == 'S') {
             item['color'] = "#52C41A"
             item['status'] = 'SUCCESS'
             item['loading'] = false
           }
           else {
             item['color'] = "#FF3A46"
             item['loading'] = false
             item['status'] = 'FAILED'
             item['is_error'] = true
             successToNext = 1
           }
         })
       }
       if (item['id'] == 5) {
        await this.pmOverviewService.checkProjectPINMandatory(this.dialogData.data.project_id).then((res) => {
          if (res['messType'] == 'S') {
            item['color'] = "#52C41A"
            item['status'] = 'SUCCESS'
            item['loading'] = false
            item['value']=res['data']
          }
          else {
            item['color'] = "#FF3A46"
            item['loading'] = false
            item['status'] = 'FAILED'
            item['is_error'] = true
            successToNext = 1
          }
        })
      }
       if (item['id'] == 6) {
        await this.pmOverviewService.checkProjectBillingEntity(this.dialogData.data.project_id).then((res) => {
          if (res['messType'] == 'S') {
            item['color'] = "#52C41A"
            item['status'] = 'SUCCESS'
            item['loading'] = false
            item['value']=res['data']
          }
          else {
            item['color'] = "#FF3A46"
            item['loading'] = false
            item['status'] = 'FAILED'
            item['is_error'] = true
            successToNext = 1
          }
        })
      }
      if (item['id'] == 7) {
        await this.pmOverviewService.checkProjectBillingCurrency(this.dialogData.data.project_id).then((res) => {
          if (res['messType'] == 'S') {
            item['color'] = "#52C41A"
            item['status'] = 'SUCCESS'
            item['loading'] = false
            item['value']=res['data']
          }
          else {
            item['color'] = "#FF3A46"
            item['loading'] = false
            item['status'] = 'FAILED'
            item['is_error'] = true
            successToNext = 1
          }
        })
      }
      if (item['id'] == 8) {
        await this.pmOverviewService.checkProjectPaymentTerms(this.dialogData.data.project_id).then((res) => {
          if (res['messType'] == 'S') {
            item['color'] = "#52C41A"
            item['status'] = 'SUCCESS'
            item['loading'] = false
            item['value']=res['data']
          }
          else {
            item['color'] = "#FF3A46"
            item['loading'] = false
            item['status'] = 'FAILED'
            item['is_error'] = true
            successToNext = 1
          }
        })
      }
      if (item['id'] == 9) {
        await this.pmOverviewService.checkOpportunityStatus(this.dialogData.data.project_id).then((res) => {
          if (res['messType'] == 'S') {
            item['color'] = "#52C41A"
            item['status'] = 'SUCCESS'
            item['loading'] = false
            item['value']=res['data']
          }
          else {
            item['color'] = "#FF3A46"
            item['loading'] = false
            item['status'] = 'FAILED'
            item['is_error'] = true
            successToNext = 1
          }
        })
      }
      if (item['id'] == 10) {
        await this.pmOverviewService.checkOpportunityStatusAsWon(this.dialogData.data.project_id).then((res) => {
          if (res['messType'] == 'S') {
            item['color'] = "#52C41A"
            item['status'] = 'SUCCESS'
            item['loading'] = false
            item['value']=res['data']
          }
          else {
            item['color'] = "#FF3A46"
            item['loading'] = false
            item['status'] = 'FAILED'
            item['is_error'] = true
            successToNext = 1
          }
        })
      }
      if (item['id'] == 1 && item['to']==5) {
        await this.pmOverviewService.checkEmployeeTimsheetBeforeComplete(this.dialogData.data.project_id).then((res) => {
           if (res['messType'] == 'S' && res['data'].length > 0) {
             item['color'] = "#FF3A46"
             item['loading'] = false
             item['status'] = 'FAILED'
             item['is_error'] = true
             successToNext = 1
           }
           else {
             item['color'] = "#52C41A"
             item['status'] = 'SUCCESS'
             item['loading'] = false
           }
         })
       }
       if (item['id'] == 2 && item['to']==5) {
       await this.pmOverviewService.checkMilestoneMovedToYTB(this.dialogData.data.project_id).then((res) => {
           if (res['messType'] == 'S' && res['data'].length > 0) {
             item['color'] = "#FF3A46"
             item['loading'] = false
             item['status'] = 'FAILED'
             item['is_error'] = true
             successToNext = 1
           }
           else {
             item['color'] = "#52C41A"
             item['status'] = 'SUCCESS'
             item['loading'] = false
           }
         })
       }
       if (item['id'] == 13) {
         await this.pmOverviewService.checkRequestPresent(this.dialogData.data.project_id).then((res) => {
             if (res['messType'] == 'S' && res['data'].length > 0) {
               item['color'] = "#FF3A46"
               item['loading'] = false
               item['status'] = 'FAILED'
               item['is_error'] = true
               successToNext = 1
             }
             else {
               item['color'] = "#52C41A"
               item['status'] = 'SUCCESS'
               item['loading'] = false
             }
           })
       }
       if (item['id'] == 15 && item['to']==5){
        await this.pmOverviewService.checkRevenueBilledEqual(this.dialogData.data.portfolio_id, this.dialogData.data.project_id).then((res) => {
          if (res['messType'] == 'S') {
            item['color'] = "#52C41A"
            item['status'] = 'SUCCESS'
            item['loading'] = false
            item['value']=res['data']
          }
          else {
            item['color'] = "#FF3A46"
            item['loading'] = false
            item['status'] = 'FAILED'
            item['is_error'] = true
            successToNext = 1
          }
        })
      }
       if (item['id'] == 1 && item['to']==14) {
        item['loading'] = true
        this.pmOverviewService.getMilestoneStatusForCompletion(this.dialogData.data.project_id).then((res) => {
          if (res['messType'] == 'S' && res['data'].length > 0) {
            item['color'] = "#FF3A46"
            item['loading'] = false
            item['status'] = 'FAILED'
            item['is_error'] = true
            let error = 'List milestone not yet recieve payment'
            for (let error_check of res['data']) {
              error = error + ' ' + error_check['id'] + '-' + error_check['milestone_name'] + ','
            }
            item['error'] = error
            successToNext = 1

          }
          else {
            item['color'] = "#52C41A"
            item['loading'] = false
            item['status'] = 'SUCCESS'
          }
        })
      }
      this.button_name = 'Close Project'
    }
    // Execution status
    if (this.data.from == 4 && this.data.to == 5) { 
      if (item['id'] == 1) {
       await this.pmOverviewService.checkEmployeeTimsheetBeforeComplete(this.dialogData.data.project_id).then((res) => {
          if (res['messType'] == 'S' && res['data'].length > 0) {
            item['color'] = "#FF3A46"
            item['loading'] = false
            item['status'] = 'FAILED'
            item['is_error'] = true
            successToNext = 1
          }
          else {
            item['color'] = "#52C41A"
            item['status'] = 'SUCCESS'
            item['loading'] = false
          }
        })
      }
      if (item['id'] == 2) {
      await this.pmOverviewService.checkMilestoneMovedToYTB(this.dialogData.data.project_id).then((res) => {
          if (res['messType'] == 'S' && res['data'].length > 0) {
            item['color'] = "#FF3A46"
            item['loading'] = false
            item['status'] = 'FAILED'
            item['is_error'] = true
            successToNext = 1
          }
          else {
            item['color'] = "#52C41A"
            item['status'] = 'SUCCESS'
            item['loading'] = false
          }
        })
      }
      if (item['id'] == 13) {
        await this.pmOverviewService.checkRequestPresent(this.dialogData.data.project_id).then((res) => {
            if (res['messType'] == 'S' && res['data'].length > 0) {
              item['color'] = "#FF3A46"
              item['loading'] = false
              item['status'] = 'FAILED'
              item['is_error'] = true
              successToNext = 1
            }
            else {
              item['color'] = "#52C41A"
              item['status'] = 'SUCCESS'
              item['loading'] = false
            }
          })
      }
      if (item['id'] == 15){
        await this.pmOverviewService.checkRevenueBilledEqual(this.dialogData.data.portfolio_id, this.dialogData.data.project_id).then((res) => {
          if (res['messType'] == 'S') {
            item['color'] = "#52C41A"
            item['status'] = 'SUCCESS'
            item['loading'] = false
            item['value']=res['data']
          }
          else {
            item['color'] = "#FF3A46"
            item['loading'] = false
            item['status'] = 'FAILED'
            item['is_error'] = true
            successToNext = 1
          }
        })
      }
      this.button_name = 'Complete Project'
    }
    if (this.data.from == 4 && this.data.to == 6) {
      
      if (item['id'] == 8) {
        let status_ids = item['status_ids']
        await this.pmOverviewService.checkForRevenueMilestones(this.dialogData.data.project_id, status_ids).then((res) => {
           if (res['messType'] == 'S' && res['data'].length > 0) {
             item['color'] = "#FF3A46"
             item['loading'] = false
             item['status'] = 'FAILED'
             item['is_error'] = true
             successToNext = 1
           }
           else {
             item['color'] = "#52C41A"
             item['status'] = 'SUCCESS'
             item['loading'] = false
           }
         })
       }
       if (item['id'] == 9) {
       let status_ids = item['status_ids']
       await this.pmOverviewService.checkMilestoneBilledMilestones(this.dialogData.data.project_id, status_ids).then((res) => {
           if (res['messType'] == 'S' && res['data'].length > 0) {
             item['color'] = "#FF3A46"
             item['loading'] = false
             item['status'] = 'FAILED'
             item['is_error'] = true
             successToNext = 1
           }
           else {
             item['color'] = "#52C41A"
             item['status'] = 'SUCCESS'
             item['loading'] = false
           }
         })
       }
       if (item['id'] == 10) {
        let billable_ids = item['billable_ids']
        await this.pmOverviewService.checkWhetherBillableTimesheet(this.dialogData.data.project_id, billable_ids).then((res) => {
            if (res['messType'] == 'S' && res['data'].length > 0) {
              item['color'] = "#FF3A46"
              item['loading'] = false
              item['status'] = 'FAILED'
              item['is_error'] = true
              successToNext = 1
            }
            else {
              item['color'] = "#52C41A"
              item['status'] = 'SUCCESS'
              item['loading'] = false
            }
          })
       }
       if (item['id'] == 11) {
        await this.pmOverviewService.checkWhetherBillableExpense(this.dialogData.data.project_id).then((res:any) => {
            if (res['messType'] == 'S' && res['data'].length > 0) {
              item['color'] = "#FF3A46"
              item['loading'] = false
              item['status'] = 'FAILED'
              item['is_error'] = true
              successToNext = 1
            }
            else {
              item['color'] = "#52C41A"
              item['status'] = 'SUCCESS'
              item['loading'] = false
            }
          })
       }
       if (item['id'] == 12) {
        await this.pmOverviewService.checkRequestPresent(this.dialogData.data.project_id).then((res) => {
            if (res['messType'] == 'S' && res['data'].length > 0) {
              item['color'] = "#FF3A46"
              item['loading'] = false
              item['status'] = 'FAILED'
              item['is_error'] = true
              successToNext = 1
            }
            else {
              item['color'] = "#52C41A"
              item['status'] = 'SUCCESS'
              item['loading'] = false
            }
          })
       }
       if (item['id'] == 15){
        await this.pmOverviewService.checkRevenueBilledEqual(this.dialogData.data.portfolio_id, this.dialogData.data.project_id).then((res) => {
          if (res['messType'] == 'S') {
            item['color'] = "#52C41A"
            item['status'] = 'SUCCESS'
            item['loading'] = false
            item['value']=res['data']
          }
          else {
            item['color'] = "#FF3A46"
            item['loading'] = false
            item['status'] = 'FAILED'
            item['is_error'] = true
            successToNext = 1
          }
        })
      }
       this.button_name = 'Cancel Project'

    }
    if (this.data.from == 4 && this.data.to == 14) { 
      if (item['id'] == 1 && item['to']==5) {
       await this.pmOverviewService.checkEmployeeTimsheetBeforeComplete(this.dialogData.data.project_id).then((res) => {
          if (res['messType'] == 'S' && res['data'].length > 0) {
            item['color'] = "#FF3A46"
            item['loading'] = false
            item['status'] = 'FAILED'
            item['is_error'] = true
            successToNext = 1
          }
          else {
            item['color'] = "#52C41A"
            item['status'] = 'SUCCESS'
            item['loading'] = false
          }
        })
      }
      if (item['id'] == 2) {
      await this.pmOverviewService.checkMilestoneMovedToYTB(this.dialogData.data.project_id).then((res) => {
          if (res['messType'] == 'S' && res['data'].length > 0) {
            item['color'] = "#FF3A46"
            item['loading'] = false
            item['status'] = 'FAILED'
            item['is_error'] = true
            successToNext = 1
          }
          else {
            item['color'] = "#52C41A"
            item['status'] = 'SUCCESS'
            item['loading'] = false
          }
        })
      }
      if (item['id'] == 13) {
        await this.pmOverviewService.checkRequestPresent(this.dialogData.data.project_id).then((res) => {
            if (res['messType'] == 'S' && res['data'].length > 0) {
              item['color'] = "#FF3A46"
              item['loading'] = false
              item['status'] = 'FAILED'
              item['is_error'] = true
              successToNext = 1
            }
            else {
              item['color'] = "#52C41A"
              item['status'] = 'SUCCESS'
              item['loading'] = false
            }
          })
      }
      if (item['id'] == 15 && item['to']==5){
        await this.pmOverviewService.checkRevenueBilledEqual(this.dialogData.data.portfolio_id, this.dialogData.data.project_id).then((res) => {
          if (res['messType'] == 'S') {
            item['color'] = "#52C41A"
            item['status'] = 'SUCCESS'
            item['loading'] = false
            item['value']=res['data']
          }
          else {
            item['color'] = "#FF3A46"
            item['loading'] = false
            item['status'] = 'FAILED'
            item['is_error'] = true
            successToNext = 1
          }
        })
      }
      if (item['id'] == 1 && item['to']==14) {
        item['loading'] = true
        this.pmOverviewService.getMilestoneStatusForCompletion(this.dialogData.data.project_id).then((res) => {
          if (res['messType'] == 'S' && res['data'].length > 0) {
            item['color'] = "#FF3A46"
            item['loading'] = false
            item['status'] = 'FAILED'
            item['is_error'] = true
            let error = 'List milestone not yet recieve payment'
            for (let error_check of res['data']) {
              error = error + ' ' + error_check['id'] + '-' + error_check['milestone_name'] + ','
            }
            item['error'] = error
            successToNext = 1

          }
          else {
            item['color'] = "#52C41A"
            item['loading'] = false
            item['status'] = 'SUCCESS'
          }
        })
      }
      this.button_name = 'Close Project'
    }
    // Completed Status
    if (this.data.from == 5 && this.data.to == 14) {
      if (item['id'] == 1) {
        item['loading'] = true
        this.pmOverviewService.getMilestoneStatusForCompletion(this.dialogData.data.project_id).then((res) => {
          if (res['messType'] == 'S' && res['data'].length > 0) {
            item['color'] = "#FF3A46"
            item['loading'] = false
            item['status'] = 'FAILED'
            item['is_error'] = true
            let error = 'List milestone not yet recieve payment'
            for (let error_check of res['data']) {
              error = error + ' ' + error_check['id'] + '-' + error_check['milestone_name'] + ','
            }
            item['error'] = error
            successToNext = 1

          }
          else {
            item['color'] = "#52C41A"
            item['loading'] = false
            item['status'] = 'SUCCESS'
          }
        })
      }
      this.button_name = 'Close Project'
    }
    if (this.data.from == 5 && this.data.to == 6) {
      
      if (item['id'] == 8) {
        let status_ids = item['status_ids']
        await this.pmOverviewService.checkForRevenueMilestones(this.dialogData.data.project_id, status_ids).then((res) => {
           if (res['messType'] == 'S' && res['data'].length > 0) {
             item['color'] = "#FF3A46"
             item['loading'] = false
             item['status'] = 'FAILED'
             item['is_error'] = true
             successToNext = 1
           }
           else {
             item['color'] = "#52C41A"
             item['status'] = 'SUCCESS'
             item['loading'] = false
           }
         })
       }
       if (item['id'] == 9) {
       let status_ids = item['status_ids']
       await this.pmOverviewService.checkMilestoneBilledMilestones(this.dialogData.data.project_id, status_ids).then((res) => {
           if (res['messType'] == 'S' && res['data'].length > 0) {
             item['color'] = "#FF3A46"
             item['loading'] = false
             item['status'] = 'FAILED'
             item['is_error'] = true
             successToNext = 1
           }
           else {
             item['color'] = "#52C41A"
             item['status'] = 'SUCCESS'
             item['loading'] = false
           }
         })
       }
       if (item['id'] == 10) {
        let billable_ids = item['billable_ids']
        await this.pmOverviewService.checkWhetherBillableTimesheet(this.dialogData.data.project_id, billable_ids).then((res) => {
            if (res['messType'] == 'S' && res['data'].length > 0) {
              item['color'] = "#FF3A46"
              item['loading'] = false
              item['status'] = 'FAILED'
              item['is_error'] = true
              successToNext = 1
            }
            else {
              item['color'] = "#52C41A"
              item['status'] = 'SUCCESS'
              item['loading'] = false
            }
          })
       }
       if (item['id'] == 11) {
        await this.pmOverviewService.checkWhetherBillableExpense(this.dialogData.data.project_id).then((res) => {
            if (res['messType'] == 'S' && res['data'].length > 0) {
              item['color'] = "#FF3A46"
              item['loading'] = false
              item['status'] = 'FAILED'
              item['is_error'] = true
              successToNext = 1
            }
            else {
              item['color'] = "#52C41A"
              item['status'] = 'SUCCESS'
              item['loading'] = false
            }
          })
       }
       if (item['id'] == 12) {
        await this.pmOverviewService.checkRequestPresent(this.dialogData.data.project_id).then((res) => {
            if (res['messType'] == 'S' && res['data'].length > 0) {
              item['color'] = "#FF3A46"
              item['loading'] = false
              item['status'] = 'FAILED'
              item['is_error'] = true
              successToNext = 1
            }
            else {
              item['color'] = "#52C41A"
              item['status'] = 'SUCCESS'
              item['loading'] = false
            }
          })
       }
       if (item['id'] == 15){
        await this.pmOverviewService.checkRevenueBilledEqual(this.dialogData.data.portfolio_id, this.dialogData.data.project_id).then((res) => {
          if (res['messType'] == 'S') {
            item['color'] = "#52C41A"
            item['status'] = 'SUCCESS'
            item['loading'] = false
            item['value']=res['data']
          }
          else {
            item['color'] = "#FF3A46"
            item['loading'] = false
            item['status'] = 'FAILED'
            item['is_error'] = true
            successToNext = 1
          }
        })
      }
       this.button_name = 'Cancel Project'

    }
    //closed
    if (this.data.from == 14 && this.data.to == 6) {
      
      if (item['id'] == 8) {
        let status_ids = item['status_ids']
        await this.pmOverviewService.checkForRevenueMilestones(this.dialogData.data.project_id, status_ids).then((res) => {
           if (res['messType'] == 'S' && res['data'].length > 0) {
             item['color'] = "#FF3A46"
             item['loading'] = false
             item['status'] = 'FAILED'
             item['is_error'] = true
             successToNext = 1
           }
           else {
             item['color'] = "#52C41A"
             item['status'] = 'SUCCESS'
             item['loading'] = false
           }
         })
       }
       if (item['id'] == 9) {
       let status_ids = item['status_ids']
       await this.pmOverviewService.checkMilestoneBilledMilestones(this.dialogData.data.project_id, status_ids).then((res) => {
           if (res['messType'] == 'S' && res['data'].length > 0) {
             item['color'] = "#FF3A46"
             item['loading'] = false
             item['status'] = 'FAILED'
             item['is_error'] = true
             successToNext = 1
           }
           else {
             item['color'] = "#52C41A"
             item['status'] = 'SUCCESS'
             item['loading'] = false
           }
         })
       }
       if (item['id'] == 10) {
        let billable_ids = item['billable_ids']
        await this.pmOverviewService.checkWhetherBillableTimesheet(this.dialogData.data.project_id, billable_ids).then((res) => {
            if (res['messType'] == 'S' && res['data'].length > 0) {
              item['color'] = "#FF3A46"
              item['loading'] = false
              item['status'] = 'FAILED'
              item['is_error'] = true
              successToNext = 1
            }
            else {
              item['color'] = "#52C41A"
              item['status'] = 'SUCCESS'
              item['loading'] = false
            }
          })
       }
       if (item['id'] == 11) {
        await this.pmOverviewService.checkWhetherBillableExpense(this.dialogData.data.project_id).then((res) => {
            if (res['messType'] == 'S' && res['data'].length > 0) {
              item['color'] = "#FF3A46"
              item['loading'] = false
              item['status'] = 'FAILED'
              item['is_error'] = true
              successToNext = 1
            }
            else {
              item['color'] = "#52C41A"
              item['status'] = 'SUCCESS'
              item['loading'] = false
            }
          })
       }
       if (item['id'] == 12) {
        await this.pmOverviewService.checkRequestPresent(this.dialogData.data.project_id).then((res) => {
            if (res['messType'] == 'S' && res['data'].length > 0) {
              item['color'] = "#FF3A46"
              item['loading'] = false
              item['status'] = 'FAILED'
              item['is_error'] = true
              successToNext = 1
            }
            else {
              item['color'] = "#52C41A"
              item['status'] = 'SUCCESS'
              item['loading'] = false
            }
          })
       }
       if (item['id'] == 15){
        await this.pmOverviewService.checkRevenueBilledEqual(this.dialogData.data.portfolio_id, this.dialogData.data.project_id).then((res) => {
          if (res['messType'] == 'S') {
            item['color'] = "#52C41A"
            item['status'] = 'SUCCESS'
            item['loading'] = false
            item['value']=res['data']
          }
          else {
            item['color'] = "#FF3A46"
            item['loading'] = false
            item['status'] = 'FAILED'
            item['is_error'] = true
            successToNext = 1
          }
        })
      }
       this.button_name = 'Cancel Project'

    }


    // On Hold Status
    if (this.data.from == 12 && this.data.to == 4) {
      if (item['id'] == 1) {
        item['loading'] = true
       await this.pmOverviewService.getProjectSecured(this.dialogData.data.project_id).then((res) => {
          if (res['messType'] == 'S' && res['data'].length > 0) {
            item['color'] = "#52C41A"
            item['loading'] = false
            item['status'] = 'SUCCESS'
          }
          else {
            item['color'] = "#FF3A46"
            item['loading'] = false
            item['status'] = 'FAILED'
            item['is_error'] = true
            successToNext = 1
          }
        })
      }
      if (item['id'] == 2) {
       await this.pmOverviewService.checkCostingSheetReleased(this.dialogData.data.project_id).then((res) => {
          if (res['messType'] == 'S' && res['data'].length > 0) {
            item['color'] = "#52C41A"
            item['status'] = 'SUCCESS'
            item['loading'] = false
          }
          else {
            item['color'] = "#FF3A46"
            item['loading'] = false
            item['status'] = 'FAILED'
            item['is_error'] = true
            successToNext = 1
          }
        })
      }
      if (item['id'] == 3) {
       await this.pmOverviewService.checkQuoteAvailable(this.dialogData.data.project_id).then((res) => {
          if (res['messType'] == 'S') {
            item['color'] = "#52C41A"
            item['status'] = 'SUCCESS'
            item['loading'] = false
          }
          else {
            item['color'] = "#FF3A46"
            item['loading'] = false
            item['status'] = 'FAILED'
            item['is_error'] = true
            successToNext = 1
          }
        })
      }
      this.button_name = 'Launch project'
    }
    if (this.data.from == 12 && this.data.to == 6) {
    }
  }
  if (successToNext == 0) {
    this.enableNext = false
    if (this.items_actions.length > 0) {
      this.enable_action = true
    }
  }
  else {
    this.enable_error = true
    this.enableNext = true
  }
  this.isLoading = false
  console.log(successToNext)
  console.log(this.enableNext)
 }
}
export interface DialogData {
  data: any;
}