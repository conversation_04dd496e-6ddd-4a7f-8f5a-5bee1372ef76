import { Component, OnInit,Input,OnChanges, HostListener} from '@angular/core';
import {MatTableDataSource} from '@angular/material/table';
import{PmMasterService} from'../../../../../../services/pm-master.service'
import { PmOverviewService } from '../../services/pm-overview.service';
import { OriginConnectionPosition } from '@angular/cdk/overlay';
import * as moment from 'moment';
import * as _ from 'underscore' ;
import {widgetConfigMaster} from '../../pm-overview-access-object.auth';
import { Router } from '@angular/router';
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog';
import { MomentDateAdapter, MAT_MOMENT_DATE_ADAPTER_OPTIONS } from '@angular/material-moment-adapter';
import { DateAdapter, MAT_DATE_LOCALE, MAT_DATE_FORMATS } from '@angular/material/core';
@Component({
  selector: 'app-unassigned-assigned-widget',
  templateUrl: './unassigned-assigned-widget.component.html',
  styleUrls: ['./unassigned-assigned-widget.component.scss'],
  providers: [
    {
      provide: DateAdapter,
      useClass: MomentDateAdapter,
      deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS]
    },
    {
      provide: MAT_DATE_FORMATS,
      useValue: {
        parse: {
          dateInput: "DD-MMM-YYYY"
        },
        display: {
          dateInput: "DD-MMM-YYYY",
          monthYearLabel: "MMM YYYY"
        }
      }
    }
  ]
})
export class UnassignedAssignedWidgetComponent implements OnInit {
 
  constructor(
    private pmMasterService: PmMasterService,
    private pmOverviewService:PmOverviewService,
    private _router: Router
  ) { }
  @Input() project_id: any;
  @Input() item_id: any;
  @Input() widget_config: any;
  items: any[] = [
    { name: 'Schedule a Kick Off Meeting', description: 'Pending',start_date: new Date(),end_date: new Date() },
    { name: 'Schedule a Kick Off Meeting', description: 'Pending',start_date: new Date(),end_date: new Date() },
    { name: 'Schedule a Kick Off Meeting', description: 'Pending',start_date: new Date(),end_date: new Date() },
    { name: 'Schedule a Kick Off Meeting', description: 'Pending',start_date: new Date(),end_date: new Date() },
    { name: 'Schedule a Kick Off Meeting', description: 'Pending',start_date: new Date(),end_date: new Date() },
    { name: 'Schedule a Kick Off Meeting', description: 'Pending',start_date: new Date(),end_date: new Date() },
    { name: 'Schedule a Kick Off Meeting', description: 'Pending',start_date: new Date(),end_date: new Date() },
    { name: 'Schedule a Kick Off Meeting', description: 'Pending',start_date: new Date(),end_date: new Date() },
    
  ];
  
  defaultFilterId = 'month';
  defaultFiltervalue: string;
  filterStartDate:Date;
  filterEndDate:Date;
  assignedDetails:any;
  unAssignedDetails:any;
  coloras:any='black'
  loading:boolean = true;
  filterloading:boolean = true;
  assignedToList:any=[];
  statusList:any;
  noData:boolean=true;
  noDataAssign:boolean=true;
  formConfig: any = [];
  noDataImage: any;
  colorMap:any={};
  yearFilterList:any
  fontStyle: any;
   date:any={
    'month': {startDate:moment().startOf('month'), endDate:moment().endOf('month')},
    'week': {startDate:moment().startOf('week'), endDate:moment().endOf('week')},
    // 'year': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')],
    // 'Next Month': [moment().add(1, 'month').startOf('month'), moment().add(1, 'month').endOf('month')],
    'quatar': {startDate:moment().startOf('month'), endDate:moment().add(2, 'month').endOf('month')},
    // 'Previous 3 Months': [moment().subtract(3, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')],
    'year': {startDate:moment().startOf('year'), endDate:moment().endOf('year')},
    // 'Previous Year': [moment().subtract(1, 'year').startOf('year'), moment().subtract(1, "year").endOf('year')]
  }

 startDate:any
 endDate:any
  async ngOnInit() {
    this.calculateDynamicStyle();
    this.item_id = parseInt(this._router.url.split("/")[5]);
    this.project_id = parseInt(this._router.url.split("/")[3]);
    await this.pmOverviewService.getProjectDashboardConfig();
    this.widget_config = this.pmOverviewService.widgetConfig;
    console.log('Widget Config:', this.widget_config)
    // this.statusList=await this.pmMasterService.status_list
    await this.pmMasterService.getStatusListDirect().then((res)=>{
      this.statusList=res
    })
    this.statusList.forEach(item => {
      let colorConfig=item.color!=null && item.color!="" ? JSON.parse(item.color) :[]
      colorConfig=colorConfig && colorConfig.length>0 ? colorConfig[0] :null
      this.colorMap[String(item.id)] = {"background_color":colorConfig?.background||item?.color,
      "text_color":colorConfig?.color || item.text_color,
        "status_name":item?.name
      }
  });
    console.log('Status Outside')
  if(this.widget_config.length>0){
    console.log('Widget Config:Inside')
      let widgetConfig=_.find(this.widget_config,{widget_name:widgetConfigMaster.assignedUnassignedWidget.name})
      this.yearFilterList=widgetConfig?.filter_data[0]?.filter_list
      console.log('FiltersAPIS calling')
      this.getFilterValue(this.defaultFilterId);
      this.getUnassignedAssignedTask()
      console.log('APIS called')
      }
      console.log('Outside')

      await this.pmMasterService.getPMFormCustomizeConfigV().then((res: any) => {
        if (res) {
          this.formConfig = res;
        }
      });
      const retrieveStyles2 = _.where(this.formConfig, { type: "project-theme", field_name: "styles", is_active: true });
      if(retrieveStyles2.length > 0){
        this.noDataImage = retrieveStyles2[0].data.no_data_image ? retrieveStyles2[0].data.no_data_image : "https://assets.kebs.app/No-milestone-image.png";
        this.fontStyle = retrieveStyles2.length > 0 ? retrieveStyles2[0].data.font_style ? retrieveStyles2[0].data.font_style : "Roboto" : "Roboto";
        document.documentElement.style.setProperty('--assignedFont', this.fontStyle);
      }
  }
  selectedTabChange(event:any)
  {}
  

  getFilterValue(id) {
    if(id==4){
      console.log(this.startDate)
      console.log(this.endDate)
      this.filterStartDate=this.startDate
      this.filterEndDate=this.endDate
      this.filterloading = true;
      this.getUnassignedAssignedTask();
      this.defaultFiltervalue = 'Custom Duration';
    }else{
    let filterValue = this.yearFilterList.find((item) => item.id === id);
    this.filterStartDate=this.date[id]?.startDate;
    this.filterEndDate=this.date[id].endDate;
    this.filterloading = true;
    this.getUnassignedAssignedTask();
    this.defaultFiltervalue = filterValue['name'];
    }
  }
  // ngOnChanges(Changes:any){
  //   if(this.widget_config.length>0){
  //   let widgetConfig=_.find(this.widget_config,{widget_name:widgetConfigMaster.assignedUnassignedWidget.name})
  //   this.yearFilterList=widgetConfig?.filter_data[0]?.filter_list
  //   this.getFilterValue(this.defaultFilterId);
  //   this.getUnassignedAssignedTask()
  //   }
  // }

  getUnassignedAssignedTask(){
    this.loading = true;
    // this.noData=false
    // this.noDataAssign=false
    let widgetConfig=_.find(this.widget_config,{widget_name:widgetConfigMaster.assignedUnassignedWidget.name})
    let parmas={    
      'current_date':moment().format('YYYY-MM-DD'),
      'start_date':moment(this.filterStartDate).format('YYYY-MM-DD'),
      'end_date':moment(this.filterEndDate).format('YYYY-MM-DD'),
      'project_id':this.project_id,
      'item_id':this.item_id,
      'api_params':widgetConfig?.api_params
    }
    this.pmOverviewService.getAssignedUnassignedWidget(parmas).then((result:any)=>{
      this.loading=false
      this.filterloading=false
       console.log(result)
      console.log(this.noData,this.noDataAssign)
      this.assignedDetails=result?.data?.assigned || []
      this.unAssignedDetails=result?.data?.unassigned || []
      if(this.unAssignedDetails && this.unAssignedDetails.length > 0){
        this.noData=false
      }
      else{
        this.noData=true
      }
      if(this.assignedDetails && this.assignedDetails.length >0){
        this.noDataAssign=false
      }
      else{
        this.noDataAssign=true
      }
      console.log(this.noData,this.noDataAssign)
    })
  }

  @HostListener('window:resize')
  onResize() {
    this.calculateDynamicStyle();
  }
  calculateDynamicStyle() {
    let dynamicWidth = window.innerWidth - 766 + 'px';
    document.documentElement.style.setProperty(
      '--assUnassWidgetWidth',
      dynamicWidth
    );
    
  }
  
}


