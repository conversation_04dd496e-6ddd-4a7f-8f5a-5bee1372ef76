import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PmOverviewRoutingModule } from './pm-overview-routing.module';
import { PmOverviewPageComponent } from './components/pm-overview-page/pm-overview-page.component';
import { PmOverviewHeaderComponent } from './components/pm-overview-header/pm-overview-header.component';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { SharedLazyLoadedModule } from '../../../../shared-lazy-loaded/shared-lazy-loaded.module';
import { PlannedVsActualTaskComponent } from './components/planned-vs-actual-task/planned-vs-actual-task.component';
import { DxDataGridModule, DxChartModule,DxListModule, DxDropDownBoxModule,DxSelectBoxModule, DxTagBoxModule, DxTooltipModule, DxButtonModule, DxSparklineModule, DxPieChartModule} from 'devextreme-angular';
import { CheckWidgetActivePipe } from './pipes/check-widget-active.pipe';
import { CheckWidgetLabelPipe } from './pipes/check-widget-label.pipe';
import {MatProgressSpinnerModule} from '@angular/material/progress-spinner';
import { RevenueCostWidgetComponent } from './components/revenue-cost-widget/revenue-cost-widget.component';
import {MatCardModule} from '@angular/material/card';
import { MatMenuModule } from '@angular/material/menu';
import {MatTableModule} from '@angular/material/table';
import {MatTabsModule} from '@angular/material/tabs';
import { MatListModule } from '@angular/material/list';
import { UnassignedAssignedWidgetComponent } from './components/unassigned-assigned-widget/unassigned-assigned-widget.component';
import { ActivityCalendarWidgetComponent } from './components/activity-calendar-widget/activity-calendar-widget.component';
import { CalendarActivityPopUpComponent } from './components/calendar-activity-pop-up/calendar-activity-pop-up.component';
import { TooltipModule } from "ng2-tooltip-directive";
import { TaskWorkloadWidgetComponent } from './components/task-workload-widget/task-workload-widget.component';
import { SharedComponentsModule } from "src/app/app-shared/app-shared-components/components.module";
import { ActivitySummaryWidgetComponent } from './components/activity-summary-widget/activity-summary-widget.component';
import { ActivityDoughnutWidgetComponent } from './components/activity-doughnut-widget/activity-doughnut-widget.component';
import { DxCircularGaugeModule } from 'devextreme-angular';
import { TimesheetWidgetComponent } from './components/timesheet-widget/timesheet-widget.component';
import { MyTaskWidgetComponent } from './components/my-task-widget/my-task-widget.component';
import { RecentActivityWidgetComponent } from './components/recent-activity-widget/recent-activity-widget.component';
import {MatProgressBarModule} from '@angular/material/progress-bar';
import { AmsWidgetComponent } from './components/ams-widget/ams-widget.component';
import { AmsDoughnutWidgetComponent } from './components/ams-doughnut-widget/ams-doughnut-widget.component';
import { PlannedVsActualHoursComponent } from './components/planned-vs-actual-hours/planned-vs-actual-hours.component';
import { MilestoneWidgetComponent } from './components/milestone-widget/milestone-widget.component';
import {OpenDeliverablesComponent} from './components/open-deliverables/open-deliverables.component'
import { HealthStatusWidgetComponent } from './components/health-status-widget/health-status-widget.component';
import {MatSidenavModule} from '@angular/material/sidenav';
import { HealthActivityWidgetComponent } from './components/health-activity-widget/health-activity-widget.component';
import {DragDropModule} from '@angular/cdk/drag-drop';
import {MatSlideToggleModule} from '@angular/material/slide-toggle';
import { StatusChangeWizardComponent } from './components/status-change-wizard/status-change-wizard.component';
import { SatPopoverModule } from '@ncstate/sat-popover';
import { MatButtonModule } from '@angular/material/button';
import {MatCheckboxModule} from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatInputModule } from '@angular/material/input';
import { FlagChangeWizardComponent } from './components/flag-change-wizard/flag-change-wizard.component';
import { ApprovalSuccessPageComponent } from './components/approval-success-page/approval-success-page.component';
import { FlagChangeWizardWithStepperComponent } from './components/flag-change-wizard-with-stepper/flag-change-wizard-with-stepper.component';
import { ApporvalWidgetComponent } from './components/apporval-widget/apporval-widget.component';
import { SvgSecurityBypassPipe } from './pipes/svg-security-bypass.pipe';
import {BaselinePlannedActualWidgetComponent} from '../pm-overview/components/baseline-planned-actual-widget/baseline-planned-actual-widget.component'
import { DelinkOpportunityComponent } from './components/delink-opportunity/delink-opportunity.component';
@NgModule({
  declarations: [PmOverviewPageComponent, PmOverviewHeaderComponent, PlannedVsActualTaskComponent,CheckWidgetActivePipe, CheckWidgetLabelPipe, RevenueCostWidgetComponent, UnassignedAssignedWidgetComponent,ActivityCalendarWidgetComponent, CalendarActivityPopUpComponent, TaskWorkloadWidgetComponent, ActivitySummaryWidgetComponent, ActivityDoughnutWidgetComponent, TimesheetWidgetComponent, MyTaskWidgetComponent, RecentActivityWidgetComponent, AmsWidgetComponent, AmsDoughnutWidgetComponent,PlannedVsActualHoursComponent, MilestoneWidgetComponent,OpenDeliverablesComponent,HealthStatusWidgetComponent, HealthActivityWidgetComponent, StatusChangeWizardComponent, FlagChangeWizardComponent, ApprovalSuccessPageComponent, FlagChangeWizardWithStepperComponent, ApporvalWidgetComponent, SvgSecurityBypassPipe,BaselinePlannedActualWidgetComponent, DelinkOpportunityComponent],
  imports: [
    CommonModule,
    PmOverviewRoutingModule,
    MatIconModule,
    SharedLazyLoadedModule,
    MatTooltipModule,
    DxDataGridModule, 
    DxChartModule,
    DxListModule, 
    DxDropDownBoxModule,
    DxSelectBoxModule, 
    DxTagBoxModule, 
    DxTooltipModule, 
    DxButtonModule,  
    MatProgressSpinnerModule,
    MatCardModule,
    MatMenuModule,
    DxSparklineModule,
    MatTableModule,
    MatTabsModule,
    MatListModule,
    TooltipModule,
    SharedComponentsModule,
    DxCircularGaugeModule,
    DxPieChartModule,
    MatProgressBarModule,
    MatSidenavModule,
    DragDropModule,
    MatSlideToggleModule,
    SatPopoverModule,
    MatButtonModule,MatCheckboxModule,MatDatepickerModule,FormsModule, ReactiveFormsModule,MatFormFieldModule,MatInputModule
  ],
  exports: [
    PmOverviewHeaderComponent,PlannedVsActualTaskComponent,UnassignedAssignedWidgetComponent, ActivityCalendarWidgetComponent, CalendarActivityPopUpComponent, AmsDoughnutWidgetComponent
  ]
})
export class PmOverviewModule { }
