<div class="col-12 planning-light-box-styles">
    <div class="loader-container" *ngIf="loading">
        <!-- <div class="loader"></div> -->
        <mat-spinner class="green-spinner" diameter="40"></mat-spinner>
    </div>
    <div *ngIf = "!loading">
        <form [formGroup]="planningLightBoxForm">
            <div class="row planning-light-box-header" [style.height.px]="mode === 'create' ? 50 : 90">
                <div class="row header" style="cursor: all-scroll; height: 45px;" mat-dialog-title cdkDrag cdkDragRootElement=".cdk-overlay-pane" cdkDragHandle>
                    <div class="header-title col-11">
                        <div *ngIf = "(mode == 'edit')" tooltip="{{description}}" class = "title-content">
                            {{gantt_type_name}} - {{description}}
                        </div>
                        <div *ngIf = "(mode == 'create')" class = "title-content">
                            New {{gantt_type_name}}
                        </div>
                    </div>
                    <div class="close-col">
                        <mat-icon class="close-button" (click)="onCloseClick()">clear</mat-icon>
                    </div>
                </div>    
                <div class="row tab-row" *ngIf="(mode=='edit')">
                    <div  *ngFor = "let tab of tab_list" 
                    class="tab-style" 
                    (click)="getSelectedTab(tab.type)"
                    [ngClass]="{ 'selected-tab': tab.is_selected }">
                        {{tab.name}}
                    </div>
                </div>
            </div>
        
            <div class="planning-light-box-body">
                <div class="detail-tab-body" [hidden]= "selectedTab != 'details'">
                    <app-pm-planning-url [gantt_id]="ganttId" [item_id]="itemId" [project_id]="projectId" (switch)="switchURL($event)" *ngIf="this.mode =='edit'"></app-pm-planning-url>
            
                    <div class="row">
                        <div class="col-12">
                            <div class="row" *ngIf="mode!='edit'">
                                <div class="col-3">
                                    <div class="content-title">
                                        {{('task_type' | checkLabel : this.formConfig: 'planning_board': 'Type')}}
                                        <span class="required-star" *ngIf="('task_type' | checkMandatedField : this.formConfig: 'planning_board')">&nbsp;*</span>
                                        <span
                                            *ngIf="('task_type' | checkInfoIcon : this.formConfig: 'planning_board')">
                                                <mat-icon class="info-icon"
                                                tooltip="{{('task_type' | checkTooltip : this.formConfig: 'planning_board': '')}}"
                                                >
                                                    {{('info_icon' | checkLabel : this.formConfig: 'planning_board':'info_outline')}}
                                                </mat-icon>
                                            </span>
                                    </div>
                                    <app-input-search-name class="input-search" formControlName="gantt_type" [list]="ganttList" placeholder="Choose Type"
                                    [required]="('task_type' | checkMandatedField : this.formConfig: 'planning_board')"
                                    [disabled]="(('task_type' | checkDisable : this.formConfig: 'planning_board'))"
                                    >
                                    </app-input-search-name>
                                </div>
                            </div>
            
                                <div class="row">
                                    
                                    <div class="col-9"  *ngIf = "('name' | checkActive : this.formConfig: this.taskType)">
                                        <div class="content-title">
                                            {{('name' | checkLabel : this.formConfig: this.taskType: 'Name')}}
                                            <span class="required-star" *ngIf="('name' | checkMandatedField : this.formConfig: this.taskType)">&nbsp;*</span>
                                            <span
                                            *ngIf="('name' | checkInfoIcon : this.formConfig: this.taskType)">
                                                <mat-icon class="info-icon"
                                                tooltip="{{('name' | checkTooltip : this.formConfig: this.taskType: '')}}"
                                                >
                                                    {{('info_icon' | checkLabel : this.formConfig: this.taskType:
                                                    'info_outline')}}
                                                </mat-icon>
                                            </span>
                                        </div>
                                        <mat-form-field class="name-field input-field mat-form-field" appearance="outline">
                                            <input matInput placeholder="Enter Name"
                                                formControlName="text"
                                                [maxlength]="name_length ? name_length : 300"
                                                [required]="('name' | checkMandatedField : this.formConfig: this.taskType)"
                                                [disabled]="('name' | checkDisable : this.formConfig: this.taskType)"
                                                >
                                        </mat-form-field>
                                    </div>
            
                                </div>
            
                                <div class="row">
            
                                    <div class="col-3" *ngIf = "('planned_start_date' | checkActive : this.formConfig: this.taskType)">
                                        <div class="content-title">
                                            {{('planned_start_date' | checkLabel : this.formConfig: this.taskType: 'Planned Start Date')}}
                                            <span class="required-star" *ngIf="('planned_start_date' | checkMandatedField : this.formConfig: this.taskType)">&nbsp;*</span>
                                            <span
                                            *ngIf="('planned_start_date' | checkInfoIcon : this.formConfig: this.taskType)">
                                                <mat-icon class="info-icon"
                                                tooltip="{{('planned_start_date' | checkTooltip : this.formConfig: this.taskType: '')}}"
                                                >
                                                    {{('info_icon' | checkLabel : this.formConfig: this.taskType:
                                                    'info_outline')}}
                                                </mat-icon>
                                            </span>
                                        </div>
                                        <mat-form-field class="date-field input-field" appearance="outline">
        
                                            <input matInput
                                                formControlName="start_date"
                                                [matDatepicker]="psdDp7"
                                                [min] ="project_start_date"
                                                [max] = "planningLightBoxForm.get('end_date').value"
                                                [required]="('planned_start_date' | checkMandatedField : this.formConfig: this.taskType)"
                                                [disabled]="('planned_start_date' | checkDisable : this.formConfig: this.taskType) || has_child"
                                                name="plStartDate" placeholder="DD-MMM-YYYY" (click)="psdDp7.open()"/>
                                            <mat-datepicker-toggle matSuffix [for]="psdDp7"></mat-datepicker-toggle>
                                            <mat-datepicker #psdDp7></mat-datepicker>
                                        </mat-form-field>
                                    </div>
            
                                    <div class="col-3" *ngIf = "('planned_end_date' | checkActive : this.formConfig: this.taskType)">
                                        <div class="content-title">
                                            {{('planned_end_date' | checkLabel : this.formConfig: this.taskType: 'Planned End Date')}}
                                            <span class="required-star" *ngIf="('planned_end_date' | checkMandatedField : this.formConfig: this.taskType)">&nbsp;*</span>
                                            <span
                                            *ngIf="('planned_end_date' | checkInfoIcon : this.formConfig: this.taskType)">
                                                <mat-icon class="info-icon"
                                                tooltip="{{('planned_end_date' | checkTooltip : this.formConfig: this.taskType: '')}}"
                                                >
                                                    {{('info_icon' | checkLabel : this.formConfig: this.taskType:
                                                    'info_outline')}}
                                                </mat-icon>
                                            </span>
                                        </div>
                                        <mat-form-field class="date-field input-field" appearance="outline">
        
                                            <input matInput 
                                                formControlName="end_date"
                                                [matDatepicker]="psdDp6"
                                                [min] = "planningLightBoxForm.get('start_date').value"
                                                [max] = "project_end_date"
                                                [required]="('planned_end_date' | checkMandatedField : this.formConfig: this.taskType)"
                                                [disabled]="('planned_end_date' | checkDisable : this.formConfig: this.taskType) || has_child"
                                                name="plEndDate" placeholder="DD-MMM-YYYY" (click)="psdDp6.open()"/>
                                            <mat-datepicker-toggle matSuffix [for]="psdDp6"></mat-datepicker-toggle>
                                            <mat-datepicker #psdDp6></mat-datepicker>
                                        </mat-form-field>
                                    </div>
            
                                    <div class="col-3" *ngIf = "('actual_start_date' | checkActive : this.formConfig: this.taskType)">
                                        <div class="content-title">
                                            {{('actual_start_date' | checkLabel : this.formConfig: this.taskType: 'Actual Start Date')}}
                                            <span class="required-star" *ngIf="('actual_start_date' | checkMandatedField : this.formConfig: this.taskType)">&nbsp;*</span>
                                            <span
                                            *ngIf="('actual_start_date' | checkInfoIcon : this.formConfig: this.taskType)">
                                                <mat-icon class="info-icon"
                                                tooltip="{{('actual_start_date' | checkTooltip : this.formConfig: this.taskType: '')}}"
                                                >
                                                    {{('info_icon' | checkLabel : this.formConfig: this.taskType:
                                                    'info_outline')}}
                                                </mat-icon>
                                            </span>
                                        </div>
                                        <mat-form-field class="date-field input-field" appearance="outline">
        
                                            <input matInput [matDatepicker]="psdDp3"
                                                formControlName="actual_start_date"
                                                [max] = "planningLightBoxForm.get('actual_end_date').value"
                                                [min] = "project_start_date"
                                                name="actStartDate" placeholder="DD-MMM-YYYY" (click)="psdDp3.open()"/>
                                            <mat-datepicker-toggle matSuffix [for]="psdDp3"></mat-datepicker-toggle>
                                            <mat-datepicker #psdDp3></mat-datepicker>
                                        </mat-form-field>
                                    </div>
            
                                    <div class="col-3" *ngIf = "('actual_end_date' | checkActive : this.formConfig: this.taskType)">
                                        <div class="content-title">
                                            {{('actual_end_date' | checkLabel : this.formConfig: this.taskType: 'Actual End Date')}}
                                            <span class="required-star" *ngIf="('actual_end_date' | checkMandatedField : this.formConfig: this.taskType)">&nbsp;*</span>
                                            <span
                                            *ngIf="('actual_end_date' | checkInfoIcon : this.formConfig: this.taskType)">
                                                <mat-icon class="info-icon"
                                                tooltip="{{('actual_end_date' | checkTooltip : this.formConfig: this.taskType: '')}}"
                                                >
                                                    {{('info_icon' | checkLabel : this.formConfig: this.taskType:
                                                    'info_outline')}}
                                                </mat-icon>
                                            </span>
                                        </div>
                                        <mat-form-field class="date-field input-field" appearance="outline">
        
                                            <input matInput [matDatepicker]="psdDp4"
                                                [min] = "planningLightBoxForm.get('actual_start_date').value"
                                                [max] = "project_end_date"
            
                                                formControlName="actual_end_date"
                                                name="actEndDate" placeholder="DD-MMM-YYYY" (click)="psdDp4.open()"/>
                                            <mat-datepicker-toggle matSuffix [for]="psdDp4"></mat-datepicker-toggle>
                                            <mat-datepicker #psdDp4></mat-datepicker>
                                        </mat-form-field>
                                    </div>
            
                                </div>
            
                                <div class="row">
            
                                    <div class="col-3" *ngIf = "('owner' | checkActive : this.formConfig: this.taskType)">
                                        <div class="content-title">
                                            {{('owner' | checkLabel : this.formConfig: this.taskType: 'Responsible Person')}}
                                            <span class="required-star" *ngIf="('owner' | checkMandatedField : this.formConfig: this.taskType)">&nbsp;*</span>
                                            <span
                                            *ngIf="('owner' | checkInfoIcon : this.formConfig: this.taskType)">
                                                <mat-icon class="info-icon"
                                                tooltip="{{('owner' | checkTooltip : this.formConfig: this.taskType: '')}}"
                                                >
                                                    {{('info_icon' | checkLabel : this.formConfig: this.taskType:
                                                    'info_outline')}}
                                                </mat-icon>
                                            </span>
                                        </div>
                                        <app-multi-select-search2
                                            *ngIf = "('multi-owner' | checkActive : this.formConfig: this.taskType)"
                                            class="multi-select"
                                            placeholder="Select Owners" 
                                            formControlName="assigned_to_list"
                                            [max_selectable]="maxSelectable"
                                            [list]="allEmployeeList"
                                            [required]="('owner' | checkMandatedField : this.formConfig: this.taskType)"
                                            [disabled]="('owner' | checkDisable : this.formConfig: this.taskType) || planningLightBoxForm.get('status_id').value == '5'"
                                        >
                                        </app-multi-select-search2>

                                        <app-input-search-name 
                                        *ngIf = "('single-owner' | checkActive : this.formConfig: this.taskType)"
                                        class="input-search"
                                        formControlName="assigned_to_list"
                                        placeholder="Choose Owner"
                                        [list]="allEmployeeList"
                                        [required]="('owner' | checkMandatedField : this.formConfig: this.taskType)"
                                        [disabled]="('owner' | checkDisable : this.formConfig: this.taskType) || planningLightBoxForm.get('status_id').value == '5'"
                                        >
                                        </app-input-search-name>
                                    </div>
                                    
                                    <div class="col-3" *ngIf = "('status' | checkActive : this.formConfig: this.taskType)">
                                        <div class="content-title">
                                            {{('status' | checkLabel : this.formConfig: this.taskType: 'Status')}}
                                            <span class="required-star" *ngIf="('status' | checkMandatedField : this.formConfig: this.taskType)">&nbsp;*</span>
                                            <span
                                            *ngIf="('status' | checkInfoIcon : this.formConfig: this.taskType)">
                                                <mat-icon class="info-icon"
                                                tooltip="{{('status' | checkTooltip : this.formConfig: this.taskType: '')}}"
                                                >
                                                    {{('info_icon' | checkLabel : this.formConfig: this.taskType:
                                                    'info_outline')}}
                                                </mat-icon>
                                            </span>
                                        </div>
                                        <app-input-search-name 
                                        class="input-search"
                                        formControlName="status_id"
                                        [list]="statusList" placeholder="Select One"
                                        [required]="('status' | checkMandatedField : this.formConfig: this.taskType)"
                                        [disabled]="('status' | checkDisable : this.formConfig: this.taskType)"
                                        >
                                        </app-input-search-name>
                                    </div>
            
                                    <div class="col-3" *ngIf = "('completion_percentage' | checkActive : this.formConfig: this.taskType)">
                                        <div class="content-title">
                                            {{('completion_percentage' | checkLabel : this.formConfig: this.taskType: 'Completion Percentage')}}
                                            <span class="required-star" *ngIf="('completion_percentage' | checkMandatedField : this.formConfig: this.taskType)">&nbsp;*</span>
                                            <span
                                            *ngIf="('completion_percentage' | checkInfoIcon : this.formConfig: this.taskType)">
                                                <mat-icon class="info-icon"
                                                tooltip="{{('completion_percentage' | checkTooltip : this.formConfig: this.taskType: '')}}"
                                                >
                                                    {{('info_icon' | checkLabel : this.formConfig: this.taskType:
                                                    'info_outline')}}
                                                </mat-icon>
                                            </span>
                                        </div>
                                        <mat-form-field class="percentage-field input-field mat-form-field" appearance="outline">
                                            <input matInput 
                                            formControlName="completion_percentage"
                                            placeholder="Enter"
                                            type = "text"
                                            digitOnly [isPercentage]="true" [allowDecimal]="false"
                                            min="0" max="100"
                                            [required]="('completion_percentage' | checkMandatedField : this.formConfig: this.taskType)"
                                            [disabled]="('completion_percentage' | checkDisable : this.formConfig: this.taskType)"
                                            >
                                        </mat-form-field>
                                    </div>
                                   
            
                                    <div class="col-3" *ngIf = "('weighted_percentage' | checkActive : this.formConfig: this.taskType)">
                                        <div class="content-title">
                                            {{('weighted_percentage' | checkLabel : this.formConfig: this.taskType: 'Weighted Percentage')}}
                                            <span class="required-star" *ngIf="('weighted_percentage' | checkMandatedField : this.formConfig: this.taskType)">&nbsp;*</span>
                                            <span
                                            *ngIf="('weighted_percentage' | checkInfoIcon : this.formConfig: this.taskType)">
                                                <mat-icon class="info-icon"
                                                tooltip="{{('weighted_percentage' | checkTooltip : this.formConfig: this.taskType: '')}}"
                                                >
                                                    {{('info_icon' | checkLabel : this.formConfig: this.taskType:
                                                    'info_outline')}}
                                                </mat-icon>
                                            </span>
                                        </div>
                                        <mat-form-field class="percentage-field input-field mat-form-field" appearance="outline">
                                            <input matInput 
                                            formControlName="weighted_percentage"
                                            placeholder="Enter"
                                            type = "text"
                                            digitOnly [isPercentage]="true" [allowDecimal]="false"
                                            min="0" max="100"
                                            [required]="('weighted_percentage' | checkMandatedField : this.formConfig: this.taskType)"
                                            [disabled]="('weighted_percentage' | checkDisable : this.formConfig: this.taskType)"
                                            >
                                        </mat-form-field>
                                    </div>
            
                                </div>
                                <div class="row">
                                   
        
                                    <div class="col-3" *ngIf = "('fricew' | checkActive : this.formConfig: this.taskType)">
                                        <div class="content-title">
                                            {{('fricew' | checkLabel : this.formConfig: this.taskType: 'FRICEW')}}
                                            <span class="required-star" *ngIf="('fricew' | checkMandatedField : this.formConfig: this.taskType)">&nbsp;*</span>
                                            <span
                                            *ngIf="('fricew' | checkInfoIcon : this.formConfig: this.taskType)">
                                                <mat-icon class="info-icon"
                                                tooltip="{{('fricew' | checkTooltip : this.formConfig: this.taskType: '')}}"
                                                >
                                                    {{('info_icon' | checkLabel : this.formConfig: this.taskType:
                                                    'info_outline')}}
                                                </mat-icon>
                                            </span>
                                        </div>
                                        <app-input-search-name 
                                        class="input-search"
                                        [list]="fricewList" 
                                        formControlName="fricew"
                                        placeholder="Choose Type"
                                        [required]="('fricew' | checkMandatedField : this.formConfig: this.taskType)"
                                        [disabled]="('fricew' | checkDisable : this.formConfig: this.taskType)"
                                        >
                                        </app-input-search-name>
                                    </div>

                                    <div class="col-3" *ngIf = "('etc_hours' | checkActive : this.formConfig: this.taskType)">
                                        <div class="content-title">
                                            {{('etc_hours' | checkLabel : this.formConfig: this.taskType: 'Estimate Time of Completion')}}
                                            <span class="required-star" *ngIf="('etc_hours' | checkMandatedField : this.formConfig: this.taskType)">&nbsp;*</span>
                                            <span
                                            *ngIf="('etc_hours' | checkInfoIcon : this.formConfig: this.taskType)">
                                                <mat-icon class="info-icon"
                                                tooltip="{{('etc_hours' | checkTooltip : this.formConfig: this.taskType: '')}}"
                                                >
                                                    {{('info_icon' | checkLabel : this.formConfig: this.taskType:
                                                    'info_outline')}}
                                                </mat-icon>
                                            </span>
                                        </div>
                                        <mat-form-field class="percentage-field input-field mat-form-field" appearance="outline">
                                            <input matInput 
                                            formControlName="etc_hours"
                                            placeholder="Enter"
                                            type = "text"
                                            digitOnly [isPercentage]="false" [allowDecimal]="false"
                                            maxlength = "3"
                                            [required]="('etc_hours' | checkMandatedField : this.formConfig: this.taskType)"
                                            [disabled]="('etc_hours' | checkDisable : this.formConfig: this.taskType)"
                                            >
                                        </mat-form-field>
                                    </div>
                                    
                                    <div class="col-3" *ngIf = "('estimated_hours' | checkActive : this.formConfig: this.taskType)">
                                        <div class="content-title">
                                            {{('estimated_hours' | checkLabel : this.formConfig: this.taskType: 'Estimate Time of Completion')}}
                                            <span class="required-star" *ngIf="('estimated_hours' | checkMandatedField : this.formConfig: this.taskType)">&nbsp;*</span>
                                            <span
                                            *ngIf="('estimated_hours' | checkInfoIcon : this.formConfig: this.taskType)">
                                                <mat-icon class="info-icon"
                                                tooltip="{{('estimated_hours' | checkTooltip : this.formConfig: this.taskType: '')}}"
                                                >
                                                    {{('info_icon' | checkLabel : this.formConfig: this.taskType:
                                                    'info_outline')}}
                                                </mat-icon>
                                            </span>
                                        </div>
                                        <mat-form-field class="percentage-field input-field mat-form-field" appearance="outline">
                                            <input matInput 
                                            formControlName="estimated_hours"
                                            placeholder="Enter"
                                            type = "text"
                                            digitOnly [isPercentage]="false" [allowDecimal]="false"
                                            maxlength = "6"
                                            [required]="('estimated_hours' | checkMandatedField : this.formConfig: this.taskType)"
                                            [disabled]="('estimated_hours' | checkDisable : this.formConfig: this.taskType)"
                                            >
                                        </mat-form-field>
                                    </div>
                                    <div class="col-3" *ngIf = "('sheet_number' | checkActive : this.formConfig: this.taskType)">
                                        <div class="content-title">
                                            {{('sheet_number' | checkLabel : this.formConfig: this.taskType: 'Sheet Number')}}
                                            <span class="required-star" *ngIf="('sheet_number' | checkMandatedField : this.formConfig: this.taskType)">&nbsp;*</span>
                                            <span
                                            *ngIf="('sheet_number' | checkInfoIcon : this.formConfig: this.taskType)">
                                                <mat-icon class="info-icon"
                                                tooltip="{{('sheet_number' | checkTooltip : this.formConfig: this.taskType: '')}}"
                                                >
                                                    {{('info_icon' | checkLabel : this.formConfig: this.taskType:
                                                    'info_outline')}}
                                                </mat-icon>
                                            </span>
                                        </div>
                                        <mat-form-field class="percentage-field input-field mat-form-field" appearance="outline">
                                            <input matInput 
                                            formControlName="sheet_number"
                                            placeholder="Enter"
                                            type = "text"
                                            digitOnly [isPercentage]="false" [allowDecimal]="allowsheetDecimal"
                                            min="0" [digitsAllowed]="sheet_number_length ? sheet_number_length : 16" [decimalsAllowed]="sheet_number_decimal ? sheet_number_decimal : 16"
                                            [required]="('sheet_number' | checkMandatedField : this.formConfig: this.taskType)"
                                            [disabled]="('sheet_number' | checkDisable : this.formConfig: this.taskType)"
                                            >
                                        </mat-form-field>
                                    </div>
                                    <div class="col-3" *ngIf = "('software_type' | checkActive : this.formConfig: this.taskType)">
                                        <div class="content-title">
                                            {{('software_type' | checkLabel : this.formConfig: this.taskType: 'Software Type')}}
                                            <span class="required-star" *ngIf="('software_type' | checkMandatedField : this.formConfig: this.taskType)">&nbsp;*</span>
                                            <span
                                            *ngIf="('software_type' | checkInfoIcon : this.formConfig: this.taskType)">
                                                <mat-icon class="info-icon"
                                                tooltip="{{('software_type' | checkTooltip : this.formConfig: this.taskType: '')}}"
                                                >
                                                    {{('info_icon' | checkLabel : this.formConfig: this.taskType:
                                                    'info_outline')}}
                                                </mat-icon>
                                            </span>
                                        </div>
                                         <app-input-search-name 
                                        class="input-search"
                                        formControlName="software_type"
                                        [list]="softwareList" placeholder="Select One"
                                        [required]="('software_type' | checkMandatedField : this.formConfig: this.taskType)"
                                        [disabled]="('software_type' | checkDisable : this.formConfig: this.taskType)"
                                        >
                                        </app-input-search-name>
                                    </div>

                                    <div class="col-3" *ngIf = "('day_type' | checkActive : this.formConfig: this.taskType)">
                                        <div class="content-title">
                                            {{('day_type' | checkLabel : this.formConfig: this.taskType: 'Day Type')}}
                                            <span class="required-star" *ngIf="('day_type' | checkMandatedField : this.formConfig: this.taskType)">&nbsp;*</span>
                                            <span
                                            *ngIf="('day_type' | checkInfoIcon : this.formConfig: this.taskType)">
                                                <mat-icon class="info-icon"
                                                tooltip="{{('day_type' | checkTooltip : this.formConfig: this.taskType: '')}}"
                                                >
                                                    {{('info_icon' | checkLabel : this.formConfig: this.taskType:
                                                    'info_outline')}}
                                                </mat-icon>
                                            </span>
                                        </div>
                                        <app-input-search-name 
                                        class="input-search"
                                        [list]="dayTypeList" 
                                        formControlName="day_type"
                                        placeholder="Choose Type"
                                        [required]="('day_type' | checkMandatedField : this.formConfig: this.taskType)"
                                        [disabled]="('day_type' | checkDisable : this.formConfig: this.taskType)"
                                        >
                                        </app-input-search-name>
                                    </div>
                                </div>
                                <div class="row" *ngIf = "('description' | checkActive : this.formConfig: this.taskType)">
                                    <div class="row col-9 content-title">
                                        {{('description' | checkLabel : this.formConfig: this.taskType: 'Description')}}
                                        <span class="required-star" *ngIf="('description' | checkMandatedField : this.formConfig: this.taskType)">&nbsp;*</span>
                                            <span
                                            *ngIf="('description' | checkInfoIcon : this.formConfig: this.taskType)">
                                                <mat-icon class="info-icon"
                                                tooltip="{{('description' | checkTooltip : this.formConfig: this.taskType: '')}}"
                                                >
                                                    {{('info_icon' | checkLabel : this.formConfig: this.taskType:
                                                    'info_outline')}}
                                                </mat-icon>
                                            </span>
                                    </div>
                                        <textarea matInput 
                                        formControlName="description"
                                        placeholder="Enter here"
                                        class="description col-9" rows="4"
                                        [required]="('description' | checkMandatedField : this.formConfig: this.taskType)"
                                        [disabled]="('description' | checkDisable : this.formConfig: this.taskType)"
                                        ></textarea>
                                </div>
                                <div class="row">
                                    <div class="check-col col-3" *ngIf = "('billable_activity' | checkActive : this.formConfig: this.taskType)">
                                        <mat-checkbox class="check-box" name="billable"
                                        [required]="('billable_activity' | checkMandatedField : this.formConfig: this.taskType) "
                                        [disabled]="('billable_activity' | checkDisable : this.formConfig: this.taskType) || this.billableActivityCheck"
                                        formControlName="billable_act"
                                        >
                                            {{('billable_activity' | checkLabel : this.formConfig: this.taskType: 'Billing')}}
                                            <span class="required-star" *ngIf="('billable_activity' | checkMandatedField : this.formConfig: this.taskType)">&nbsp;*</span>
                                            <span
                                            *ngIf="('billable_activity' | checkInfoIcon : this.formConfig: this.taskType)">
                                                <mat-icon class="info-icon"
                                                tooltip="{{('billable_activity' | checkTooltip : this.formConfig: this.taskType: '')}}"
                                                >
                                                    {{('info_icon' | checkLabel : this.formConfig: this.taskType:
                                                    'info_outline')}}
                                                </mat-icon>
                                            </span>
                                        </mat-checkbox>
                                    </div>
                                    <div class="check-col col-3" *ngIf = "('etc' | checkActive : this.formConfig: this.taskType)"> 
                                    <mat-checkbox class="check-box" formControlName = 'etc' name="etc"
                                    [required]="('etc' | checkMandatedField : this.formConfig: this.taskType)"
                                    [disabled]="('etc' | checkDisable : this.formConfig: this.taskType)"
                                        >
                                            {{('etc' | checkLabel : this.formConfig: this.taskType: 'Estimate Time of Completion')}}
                                            <span class="required-star" *ngIf="('etc' | checkMandatedField : this.formConfig: this.taskType)">&nbsp;*</span>
                                            <span
                                            *ngIf="('etc' | checkInfoIcon : this.formConfig: this.taskType)">
                                                <mat-icon class="info-icon"
                                                tooltip="{{('etc' | checkTooltip : this.formConfig: this.taskType: '')}}"
                                                >
                                                    {{('info_icon' | checkLabel : this.formConfig: this.taskType:
                                                    'info_outline')}}
                                                </mat-icon>
                                            </span>
                                        </mat-checkbox>
                                    </div>
                                </div>
                                <div class="row" *ngIf = "('tag' | checkActive : this.formConfig: this.taskType)">
                                    <div class="col-12">
                                        <div class="row content-title">
                                            {{('tag' | checkLabel : this.formConfig: this.taskType: 'Tags')}}
                                            <span class="required-star" *ngIf="('tag' | checkMandatedField : this.formConfig: this.taskType)">&nbsp;*</span>
                                            <span
                                            *ngIf="('tag' | checkInfoIcon : this.formConfig: this.taskType)">
                                                <mat-icon class="info-icon"
                                                tooltip="{{('tag' | checkTooltip : this.formConfig: this.taskType: '')}}"
                                                >
                                                    {{('info_icon' | checkLabel : this.formConfig: this.taskType:
                                                    'info_outline')}}
                                                </mat-icon>
                                            </span>
                                        </div>
                                        <div class="row">
                                            <!-- <app-tag [add]="add" [existingTag]="existingTag" (valueEmitted)="handleValueEmitted($event)"></app-tag> -->
                                        </div>
                                    </div>
                                </div>
                                <div class="row" *ngIf="('pop_up' | checkActive : formConfig : taskType)">
                                    <div class="col-12">
                                        <!-- Header for employees on leave section -->
                                        <div class="leave-employees-header" *ngIf="leaveEmployeeListList.length > 0">
                                            The following employees are on leave during selected dates:
                                        </div>
                                        
                                        <div class="selected-employees-container">
                                            <!-- Loop through leaveEmployeeListList -->
                                            <div class="selected-employee" *ngFor="let employee of leaveEmployeeListList">
                                                <div class="employee-info">
                                                    <img class="employee-image" 
                                                         [src]="employee.profile_pic || 'https://assets.kebs.app/images/User.png'" 
                                                         [alt]="employee.employee_name || 'Employee'">
                                                    <div class="employee-details">
                                                        <div class="employee-name">
                                                            {{ employee.employee_name }}
                                                        </div>
                                                        <div class="employee-dates">
                                                            {{ employee.start_date }} - {{ employee.end_date }}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                        </div>
                    </div>
                </div> 
                <div class="dependency-body" [hidden] = "selectedTab != 'dependency'" >
                    <app-pm-planning-url [gantt_id]="ganttId" [item_id]="itemId" [project_id]="projectId"></app-pm-planning-url>
                    
                    <div class="dependency-table">
                            <app-pm-gantt-dependency [selectedTypeId]="ganttId" [itemId]="itemId" [projectId]="projectId" (emitData)="addDependency($event)" [projectData]="ganttData"></app-pm-gantt-dependency>
                        
                    </div>
                </div>
            </div>
            <div class="planning-light-box-footer">
                <button *ngIf="!saveDisable && !readOnly" mat-button class="button-save" (click)="saveData()" >Save</button>
                <div class="button-save" *ngIf="saveDisable">
                    <mat-spinner class="white-spinner" diameter="20"></mat-spinner>
                </div>
            </div>
        </form>
    </div> 
    
</div>