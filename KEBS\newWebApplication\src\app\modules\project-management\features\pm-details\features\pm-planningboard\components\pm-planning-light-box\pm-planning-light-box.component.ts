import { Component, OnInit, Inject, HostListener, Output, EventEmitter } from '@angular/core';
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA, } from '@angular/material/dialog';
import * as _ from 'underscore';
import moment from 'moment';
import { FormBuilder, FormGroup, Validators, FormControl, DefaultValueAccessor } from '@angular/forms';
import { PmMasterService } from 'src/app/modules/project-management/services/pm-master.service';
import { PmPlanningboardService } from "../../services/pm-planningboard.service";
import { ToasterService } from 'src/app/services/toaster-service/toaster.service';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core';
import { MAT_MOMENT_DATE_ADAPTER_OPTIONS, MomentDateAdapter } from '@angular/material-moment-adapter';
import { ToasterMessageService } from 'src/app/modules/project-management/shared-lazy-loaded/components/toaster-message/toaster-message.service';
import { PmAuthorizationService } from 'src/app/modules/project-management/services/pm-authorization.service';

@Component({
  selector: 'app-pm-planning-light-box',
  templateUrl: './pm-planning-light-box.component.html',
  styleUrls: ['./pm-planning-light-box.component.scss'],
  providers: [
    {
      provide: DateAdapter,
      useClass: MomentDateAdapter,
      deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS]
    },
    {
      provide: MAT_DATE_FORMATS,
      useValue: {
        parse: {
          dateInput: "DD-MMM-YYYY"
        },
        display: {
          dateInput: "DD-MMM-YYYY",
          monthYearLabel: "MMM YYYY"
        }
      }
    },
  ]
})
export class PmPlanningLightBoxComponent implements OnInit {

  @Output() closeDialog: EventEmitter<any> = new EventEmitter<any>();

  formConfig: any = [];
  statusList: any
  softwareList :any;
  ganttList: any = this.masterService.ganttTypeList;

  fricewList: any = this.masterService.fricew_list;
  dayTypeList: any=[];
  dependency: any;
  ganttData: any;
  has_child: boolean=false;

  mode: any;
  projectId: any;
  itemId: any;
  ganttId: any;

  description: any = "";
  gantt_type_name: any = "";
  taskType: any = "";
  ownersList: any;
  currentDate = moment().format("YYYY-MM-DD")
  tabColor: any;
  selectedTab: any;
  gantt_list: any = [];
  name_length: any = 300;
  software_type_length:any=100;
  sheet_number_length:any=16
  sheet_number_decimal:any=16;
  saved: any;
  taskData: any;
  mainData: any;
  loading: boolean = false;
allowsheetDecimal:boolean=false;
  project_start_date: any;
  project_end_date: any;
  errInMandate: any = false;
  allEmployeeList: any = [];
  currentEmployeeList: any = [];
  leaveEmployeeListList:any=[];
  multipleOwner: boolean = false;
  retrieveMessages: any;
  saveDisable: boolean = false;
  button: any;
  readOnly: boolean=false; 
  billableActivityCheck: boolean=false;
  fontStyle: any;
  fieldOutline: any;
  tab_list = [{
    id: 1,
    type: 'details',
    name: 'Details',
    is_selected: true,
    is_active: true
  }];

  dependency_col = [{
    id: 1,
    type: 'select',
    label: 'Select',
    width: '50px',
    is_active: true
  },
  {
    id: 2,
    type: 'wbs',
    label: 'WBS',
    width: '80px',
    is_active: true
  },
  {
    id: 3,
    type: 'task',
    label: 'Task',
    width: '400px',
    is_active: true
  },
  {
    id: 4,
    type: 'delay',
    label: 'Delay',
    width: '400px',
    is_active: true
  }];


  @HostListener('window:keyup.esc') onKeyUp() {
    this.onCloseClick()
  }

  planningLightBoxForm : FormGroup;
  status_array:any
  billableValue:any = '';
  fixedDuration: boolean=false;
  maxSelectable: number = 5;
  constructor(private masterService: PmMasterService,
    public dialogRef: MatDialogRef<PmPlanningLightBoxComponent>,
    @Inject(MAT_DIALOG_DATA) public dialogData: DialogData,
    private fb: FormBuilder,
    private boardService: PmPlanningboardService,
    private toasterService: ToasterMessageService,
    private authService: PmAuthorizationService
    ) { }

  async ngOnInit() {
    this.loading = true;
    await this.masterService.getPMFormCustomizeConfigV().then((res: any) => {
      if (res) {
        this.formConfig = res;

        let multiOwnerConfig = _.findWhere(this.formConfig,{type:"activity", field_name:"multi-owner", is_active: true}) 

        if(multiOwnerConfig)
        {
            this.maxSelectable = multiOwnerConfig['max_selectable'] ? multiOwnerConfig['max_selectable'] :5
        }
      }
    });

    
    
    if(this.formConfig.length > 0){
      const retrieveStyles = _.where(this.formConfig, { type: "project-theme", field_name: "styles", is_active: true });
      this.button = retrieveStyles.length > 0 ? retrieveStyles[0].data.button_color ? retrieveStyles[0].data.button_color : "#90ee90" : "#90ee90";
      document.documentElement.style.setProperty('--planningbutton', this.button)
      this.tabColor = retrieveStyles.length > 0 ? retrieveStyles[0].data.tab_color ? retrieveStyles[0].data.tab_color : "#90ee90" : "#90ee90";
      document.documentElement.style.setProperty('--planningTabColor', this.tabColor)
      this.fontStyle = retrieveStyles.length > 0 ? retrieveStyles[0].data.font_style ? retrieveStyles[0].data.font_style : "Roboto" : "Roboto";
      document.documentElement.style.setProperty('--planninglightboxFont', this.fontStyle);
      this.fieldOutline = retrieveStyles.length > 0 ? retrieveStyles[0].data.field_outline_color ? retrieveStyles[0].data.field_outline_color : "#808080" : "#808080";
      document.documentElement.style.setProperty('--planninglightboxField', this.fieldOutline);
      const status_array_config=_.where(this.formConfig, { type: "gantt", field_name: "status_array_config"});
      if(status_array_config.length > 0){
       this.status_array = status_array_config[0].default;
      }
      else{
        this.status_array = [4,5,7]
      }

      const retrieveNameLength = _.where(this.formConfig, { type: "gantt", field_name: "text_name", is_active: true });
      this.name_length = retrieveNameLength.length > 0 ? retrieveNameLength[0].maxLength : 300;
      const retrieveNameLengthSoftwate = _.where(this.formConfig, { type: "gantt", field_name: "software_type", is_active: true });
      const retrieveSheetLengthSoftwate = _.where(this.formConfig, { type: "gantt", field_name: "sheet_number", is_active: true });
      const allowSheetDecimal = _.where(this.formConfig, { type: "allow_decimal", field_name: "sheet_number", is_active: true });
      if(allowSheetDecimal && allowSheetDecimal.length >0){
        this.allowsheetDecimal=true
      }
      this.software_type_length=retrieveNameLengthSoftwate.length > 0 ? retrieveNameLengthSoftwate[0].maxLength : 100;
      this.sheet_number_length=retrieveSheetLengthSoftwate.length > 0 ? retrieveSheetLengthSoftwate[0].maxLength :16;
      this.sheet_number_decimal=retrieveSheetLengthSoftwate.length > 0 ? retrieveSheetLengthSoftwate[0].decLength :16;
      let billableDefaulted = _.where(this.formConfig, { type: "activity", field_name: "billable_activity"});
      billableDefaulted = billableDefaulted[0] ? billableDefaulted[0]  : ''
      if(billableDefaulted != ''){
        if(billableDefaulted['default_value_enable']){
          this.billableValue = billableDefaulted['value'] ? billableDefaulted['value'] : ''
        }
      }
      
    }

    await this.initializeForm()
    this.statusList= _.filter(this.masterService.task_status_list, (res) => {
      if (_.contains(this.status_array, res['id'])) {
        return res;
      }
    });
    
    this.selectedTab = this.tab_list.length > 0 ? this.tab_list[0].type : '';

    this.ganttId = this.dialogData.ganttId;
    this.mode = this.dialogData.mode;
    this.projectId = this.dialogData.projectId;
    this.itemId = this.dialogData.itemId;
    this.mainData = this.dialogData.data;
    this.readOnly = this.dialogData.readOnly;
    this.ganttData = this.dialogData.ganttData;
    
    console.log("Main Data", this.mainData)
    
    await this.authService.getProjectWiseObjectAccess(this.projectId, this.itemId, 88).then((res)=>{
      if(res)
      {
          this.tab_list.push({
            id: 2,
            type: 'dependency',
            name: 'Dependency',
            is_selected: false,
            is_active: true
          }) 
          if(this.mode == 'create'){
            
            this.tab_list[1].is_active = false;
          }
      }
    })

  

    if(this.mode == 'create'){
      this.ganttList = this.dialogData.ganttList;
    }

    this.masterService.getDayTypeList().then((res)=>{
      this.dayTypeList = res
    })
    this.masterService.getSoftwareList().then((res)=>{
      this.softwareList = res['data']
    })
 
    const fixedDuration_config = _.where(this.formConfig,{type:"gantt", field_name:"fixed-duration", is_active: true})


    if(fixedDuration_config.length >0){
      this.fixedDuration = fixedDuration_config[0] ?  fixedDuration_config[0].default : false
    }

    await this.boardService.getProjectDuration(this.projectId, this.itemId).then((res: any)=>{
      if(res['messType']=="S")
      {
          if(res['data'])
          {
            this.project_start_date = this.fixedDuration ? res['data']['planned_start_date'] : moment("2000-01-01").format("YYYY-MM-DD");
            this.project_end_date = this.fixedDuration ? res['data']['planned_end_date'] : moment("9999-12-31").format("YYYY-MM-DD");
          }
          else
          {
            this.project_start_date =  moment("2000-01-01").format("YYYY-MM-DD");
            this.project_end_date = moment("9999-12-31").format("YYYY-MM-DD");
          }
      }
      else
      {
          this.project_start_date =  moment("2000-01-01").format("YYYY-MM-DD");
          this.project_end_date = moment("9999-12-31").format("YYYY-MM-DD");
      }
    })

    console.log("Date", this.project_start_date, this.project_end_date)

    // this.project_start_date = this.fixedDuration ? this.mainData.start_date : moment("2000-01-01").format("YYYY-MM-DD");
    // this.project_end_date = this.fixedDuration ?this.mainData.end_date : moment("9999-12-31").format("YYYY-MM-DD");
    
    this.retrieveMessages = _.where(this.formConfig, { type: "planning_board", field_name: "messages", is_active: true });
    const multipleOwnerConfig = _.where(this.formConfig, { type: "activity", field_name: "multi-owner", is_active: true });
    this.multipleOwner = multipleOwnerConfig.length > 0 ? true : false;

    await this.boardService.getProjectOwnerList(this.projectId, this.itemId, this.currentDate).then((res) => {
      if (res['messType'] == "S") {
        this.allEmployeeList = res['all_stakeholder']
        this.currentEmployeeList = res['current_stakeholder']

        for (let employee of this.allEmployeeList) {
          employee['id'] = employee['associate_id']
        }
      }
    })

    if (this.mode == "edit") {
      await this.updateTaskDetails();

      this.updateTaskConfiguration();
    }
    if(this.mode == "create"){
      this.planningLightBoxForm.patchValue({
        status_id: 7
      })
    }

    this.loading = false;



    this.planningLightBoxForm.get('gantt_type').valueChanges.subscribe(async (res) => {
      if (res) {
        let selected_gantt_type = _.findWhere(this.ganttList, { id: res })
        if (selected_gantt_type['id'] == 5) {
          this.statusList = this.statusList
            .filter(status => status.is_for_planning === 1)
            .map(status => ({
              ...status,
              name: status.task_label
            }));

          console.log(this.statusList)
        } else {
          this.statusList = this.statusList.filter(status => status.is_only_for_task != 1);
          console.log(this.statusList)
        }
        console.log("gantt type"+selected_gantt_type)

        if (selected_gantt_type) {
          this.gantt_type_name = selected_gantt_type['name']
          this.taskType = selected_gantt_type['gantt_type']

        }
        else
        {
          this.gantt_type_name=""
          this.taskType=""
        }
      }
      else
      {
        this.gantt_type_name=""
        this.taskType=""
      }
    });

    this.planningLightBoxForm.get('status_id').valueChanges.subscribe(async (res) => {
      if (res == null || res == undefined || res == 'null') {
        this.planningLightBoxForm.patchValue({
          ['status_id']: ''
        })
      }
      if (res === 4) {
        this.planningLightBoxForm.patchValue({
          actual_start_date: moment().format("YYYY-MM-DD")
        });
        this.planningLightBoxForm.patchValue({
          actual_end_date: ""
        });
      }
    
      if (res === 5) {
        this.planningLightBoxForm.patchValue({
          actual_end_date: moment().format("YYYY-MM-DD")
        });
        const actualStartDate = this.planningLightBoxForm.get('actual_start_date')?.value;

        if (actualStartDate== null || actualStartDate == undefined || actualStartDate == 'null' || actualStartDate=='' || actualStartDate=="Invalid date") {
          this.planningLightBoxForm.patchValue({
            actual_start_date: moment().format("YYYY-MM-DD")
          });
        }
      }
    
      if (res === 7) {
        this.planningLightBoxForm.patchValue({
          actual_start_date: '',
          actual_end_date: ''
        });
      }
     
    });
    this.planningLightBoxForm.get('start_date').valueChanges.subscribe(async (res) => {
      if (res) {
        await this.boardService.getEmployeeLeaveData(this.planningLightBoxForm.get('start_date').value,this.planningLightBoxForm.get('end_date').value,this.planningLightBoxForm.get('assigned_to_list').value).then((res: any) => {
          if (res) {
            this.leaveEmployeeListList = res;
          }
        })
      }
    });
    this.planningLightBoxForm.get('end_date').valueChanges.subscribe(async (res) => {
      if (res) {
        await this.boardService.getEmployeeLeaveData(this.planningLightBoxForm.get('start_date').value,this.planningLightBoxForm.get('end_date').value,this.planningLightBoxForm.get('assigned_to_list').value).then((res: any) => {
          if (res) {
            this.leaveEmployeeListList = res;
            console.log(this.leaveEmployeeListList)
          }
        })
      }
    });
    this.planningLightBoxForm.get('assigned_to_list').valueChanges.subscribe(async (res) => {
      if (res) {
        await this.boardService.getEmployeeLeaveData(this.planningLightBoxForm.get('start_date').value,this.planningLightBoxForm.get('end_date').value,this.planningLightBoxForm.get('assigned_to_list').value).then((res: any) => {
          if (res) {
            this.leaveEmployeeListList = res;
          }
        })
      }
    });
    // this.planningLightBoxForm.get('end_date').valueChanges.subscribe(async (res) => {
    //   if (res == null || res == undefined || res == 'null') {
    //     this.planningLightBoxForm.patchValue({
    //       ['end_date']: ''
    //     })
    //   }
    // });
  }
  // Add this method to your component class
getEmployeeDetails(employeeId: number): any {
  if (!this.allEmployeeList || !employeeId) return null;
  
  // Find employee by ID (adjust property name if needed)
  const employee = this.allEmployeeList.find(emp => 
      emp.id === employeeId || 
      emp.employee_id === employeeId
  );
  
  return employee || null;
}

  getSelectedTab(selectedTab: any) {
    const preIndex = this.tab_list.findIndex(tab => tab.is_selected === true);
    if (preIndex !== -1) {
      this.tab_list[preIndex].is_selected = false;
      this.selectedTab = selectedTab;
    }

    const selectedIndex = this.tab_list.findIndex(tab => tab.type === selectedTab);
    if (selectedIndex !== -1) {
      this.tab_list[selectedIndex].is_selected = true;
      this.selectedTab = selectedTab;
    }
  }

  onCloseClick() {
    this.dialogRef.close({ messType: "E" })
  }




  async updateTaskDetails() {
    await this.boardService.getProjectGanttItem(this.ganttId, this.projectId, this.itemId).then((res) => {
      if (res['messType'] == "S") {
        this.taskData = res['data'];
        console.log("Task Data", this.taskData)
        if (this.taskData.gantt_type_id == 5) {
          this.statusList = this.statusList
            .filter(status => status.is_for_planning === 1)
            .map(status => ({
              ...status,
              name: status.task_label
            }));

console.log(this.statusList)
        }else {
          this.statusList = this.statusList.filter(status => status.is_only_for_task != 1);
          console.log(this.statusList)
        }
        this.description = this.taskData.description;
        let updateProgress = _.findWhere(this.formConfig,{type:"gantt", field_name:"update-progress-percentage", is_active: true});

        if(updateProgress)
        {
          this.has_child = this.taskData.has_child == 1? true : false;
          if(this.has_child) 
          {
            this.planningLightBoxForm.get('completion_percentage')?.disable()
          }
        }

        console.log("has_chid", this.has_child)
        let selected_gantt_type = _.findWhere(this.ganttList, { id: this.taskData.gantt_type_id })
        if (selected_gantt_type) {
          this.gantt_type_name = selected_gantt_type['name']
          this.taskType = selected_gantt_type['gantt_type']

        }
        this.patchFieldValuesEdit();
        
      }
      else {
        const editFetchError = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.edit_fetch ? this.retrieveMessages[0].errors.edit_fetch : 'Error while fetching Task Data for Edit!' : 'Error while fetching Task Data for Edit!';
        this.toasterService.showError(editFetchError)
      }
    })
    await this.boardService.getEmployeeLeaveData(this.taskData.planned_start_date,this.taskData.planned_end_date,this.taskData.assigned_to_list).then((res: any) => {
      if (res) {
        this.leaveEmployeeListList = res;
      }
    })





  }


  updateTaskConfiguration() {
    let multi_owner_config = _.where(this.formConfig, { type: "activity", field_name: "multi-owner", is_active: true })

    if (multi_owner_config.length > 0) {
      let owner = this.taskData.assigned_to_list;


      this.planningLightBoxForm.patchValue({
        assigned_to_list: owner
      })

    }
    else
    {
      let owner = this.taskData.assigned_to_list;

      if(owner && owner.length>0)
      {
        this.planningLightBoxForm.patchValue({
          assigned_to_list: owner[0]
        })
      }
    }

    const billableActivity = _.findWhere(this.formConfig,{type:"gantt", field_name:"billable_activity_check", is_active: true})

    if(billableActivity)
    {
      this.billableActivityCheck  = _.contains(this.mainData['timesheet_ids'], this.ganttId)
    }

  

  }



  patchFieldValuesEdit() {
    this.planningLightBoxForm.patchValue(
      {
        id: this.taskData.gantt_id,
        parent: this.taskData.parent_id,
        gantt_type: this.taskData.gantt_type_id || '',
        text: this.taskData.description || '',
        start_date: this.taskData.planned_start_date ? moment(this.taskData.planned_start_date).format("YYYY-MM-DD") || '' : '',
        end_date: this.taskData.planned_end_date ? moment(this.taskData.planned_end_date).format("YYYY-MM-DD") || '' : '',
        actual_start_date: this.taskData.actual_start_date ? moment(this.taskData.actual_start_date).format("YYYY-MM-DD") || '' : '',
        actual_end_date:  this.taskData.actual_end_date ? moment(this.taskData.actual_end_date).format("YYYY-MM-DD") || '' :'',
        description: this.taskData.long_description || '',
        status_id: this.taskData.status_id || '',
        weighted_percentage: this.taskData.weighted_percentage || '',
        estimated_hours: this.taskData.estimated_hours || '',
        billable_act: this.taskData.billable_act || 0,
        assigned_to_list: this.taskData.assigned_to_list || [],
        completion_percentage: this.taskData.completion_percentage || 0,
        sheet_number:this.taskData.sheet_number || '',
        software_type:this.taskData.software_type || '',
        etc_hours: this.taskData.etc_hours || 0,
        etc: this.taskData.etc || '',
        fricew: this.taskData.fricew || '',
        day_type: this.taskData.day_type || ''
      }
    )
  }


  saveData() {
    this.saveDisable = true;
    this.mandateErrorNotifi();
    console.log(this.errInMandate);
    if (!this.errInMandate) {
      if (this.planningLightBoxForm.get('text').value == null || this.planningLightBoxForm.get('text').value == '' || this.planningLightBoxForm.get('text').value.trim() == '') {
        
        const invalidName = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.name_empty ? this.retrieveMessages[0].errors.name_empty : 'Kindly Enter Valid Name' : 'Kindly Enter Valid Name';
        this.toasterService.showWarning(invalidName, 10000)
        this.saveDisable = false;
        return false
      }

      if(this.planningLightBoxForm.get('start_date').invalid){
        const invalidStartDate = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.start_date_valid ? this.retrieveMessages[0].errors.start_date_valid : 'Kindly choose valid Start Date' : 'Kindly choose valid Start Date';
        this.toasterService.showWarning(invalidStartDate, 10000)
        this.saveDisable = false;
        return false
      }
        
        
      if(this.planningLightBoxForm.get('end_date').invalid){
        

        const invalidEndDate = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.end_date_valid ? this.retrieveMessages[0].errors.end_date_valid : 'Kindly choose valid End Date' : 'Kindly choose valid End Date';
        this.toasterService.showWarning(invalidEndDate, 10000)
        this.saveDisable = false;
        return false
      }

      let formOutput = this.planningLightBoxForm.value

      console.log("Formouptut Before", formOutput)

      formOutput.start_date = moment(formOutput.start_date).format("YYYY-MM-DD")
      formOutput.end_date = moment(formOutput.end_date).format("YYYY-MM-DD")

      console.log("Formouptut After", formOutput)
      
      this.saveDisable = false;
      this.dialogRef.close({ messType: "S", data: formOutput, dependency: this.dependency })
    }
    // else { 
    //   this.mandateErrorNotifi();
    //   //this.toasterService.showWarning("Warning!", "Kindly enter all mandatory fields!")
    // }
  }


  async switchURL(event) {

    console.log(event)
    this.loading = true;
    this.planningLightBoxForm.reset();


    this.ganttId = event;
    this.mode = this.dialogData.mode;
    this.projectId = this.dialogData.projectId;
    this.itemId = this.dialogData.itemId;


    if (this.mode == "edit") {
      await this.updateTaskDetails();
    }
    else if (this.mode == "create") {
      this.mode = "edit";
      await this.updateTaskDetails();
    }

    this.loading = false;

  }


  addDependency(event) {
    console.log(event)
    this.dependency = event;
  }

  mandateErrorNotifi(){
    console.log('mandate entered')
    console.log('start date', this.planningLightBoxForm.get('start_date').value)
    console.log('end date', this.planningLightBoxForm.get('end_date').value)
    console.log('status', this.planningLightBoxForm.get('status_id').value)
    if(this.planningLightBoxForm.get('start_date').value == '' || this.planningLightBoxForm.get('start_date').value == null || this.planningLightBoxForm.get('start_date').value == undefined){
      const invalidStartDate = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.start_date_empty ? this.retrieveMessages[0].errors.start_date_empty : 'Kindly Choose Start Date' : 'Kindly Choose Start Date';
      this.toasterService.showWarning(invalidStartDate, 10000)
      this.errInMandate = true;
      this.saveDisable = false;
    }
    else if(this.planningLightBoxForm.get('end_date').value == '' || this.planningLightBoxForm.get('end_date').value == null || this.planningLightBoxForm.get('end_date').value == undefined){
      const invalidEndDate = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.end_date_empty ? this.retrieveMessages[0].errors.end_date_empty : 'Kindly Choose End Date' : 'Kindly Choose End Date';
      this.toasterService.showWarning(invalidEndDate, 10000)
      this.errInMandate = true;
      this.saveDisable = false;
    }
    // else if(this.planningLightBoxForm.get('status_id').value == '' || this.planningLightBoxForm.get('status_id').value == null || this.planningLightBoxForm.get('status_id').value == undefined){
    //   const invalidStatus = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.status? this.retrieveMessages[0].errors.status : 'Kindly Choose Status' : 'Kindly Choose Status';
    //   this.toasterService.showWarning("Warning!", invalidStatus)
    //   this.errInMandate = true;
    //   this.saveDisable = false;
    // }
    else{
      this.errInMandate = false;
      this.saveDisable = true;
    }
  }

  initializeForm(){
    this.planningLightBoxForm = this.fb.group({
      id: [''],
      parent: [''],
      text: [''],
      gantt_type: [''],
      start_date: [''],
      end_date: [''],
      actual_start_date: [''],
      actual_end_date: [''],
      billable_act: [this.billableValue],
      etc: [''],
      fricew: [''],
      estimated_hours: [''],
      completion_percentage: [''],
      sheet_number:[''],
      software_type:[''],
      weighted_percentage: [''],
      status_id: [''],
      activity_location: [''],
      description: [''],
      etc_hours: [''],
      day_type:[''],
      assigned_to_list:['']
    });
  }

  ngOnDestroy() {
    this.planningLightBoxForm.reset();
  }


}




export interface DialogData {
  mode: any;
  projectId: any;
  itemId: any;
  ganttId: any;
  data: any;
  ganttList: any;
  readOnly: any;
  ganttData: any;
}