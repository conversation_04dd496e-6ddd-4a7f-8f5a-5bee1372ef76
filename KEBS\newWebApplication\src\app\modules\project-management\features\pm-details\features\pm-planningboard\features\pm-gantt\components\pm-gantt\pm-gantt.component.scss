@import "dhtmlx-gantt/codebase/dhtmlxgantt.css";
@import "dhtmlx-gantt/codebase/skins/dhtmlxgantt_material.css";

 

.gantt_main{

    position: absolute;
    height: var(--detailsHeight) !important;
    width: var(--mainWidth) !important;
    z-index: 1;
    background: white;
    .gantt_chart{
        position:absolute !important;
        width: 100% !important;
        height: var(--innerTabHeight) !important;
        overflow: hidden !important;
        z-index: 2 !important;
        background: white;
    }


  

   

   

  .gantt_task_content{
    color:  #1c0808;
    font-family: Roboto;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    letter-spacing: 0.24px;
    text-transform: capitalize;
    overflow: hidden;
  }


    .gantt-fullscreen {
        position: absolute;
        bottom: 20px;
        right: 20px;
        width: 30px;
        height: 30px;
        padding: 2px;
        font-size: 32px;
        background: transparent;
        cursor: pointer;
        opacity: 0.5;
        text-align: center;
        -webkit-transition: background-color 0.5s, opacity 0.5s;
        transition: background-color 0.5s, opacity 0.5s;
    }

    .gantt-fullscreen:hover {
        background: rgba(150, 150, 150, 0.5);
        opacity: 1;
    }


    .weekend {
        background: #e0ebf9 !important;
    }

    .gantt_critical_task{
      background-color: #ff5252;
      border-color: #ff5252;
    }

    .task_groups {
        background-color: orangered !important;
    }

    .task_groups .gantt_task_progress {
        background-color: red;
        opacity: 0.6;
    }
    .gantt_task_row.gantt_selected .weekend {
        background-color: #EFF5FD !important;
    }

    .gantt_task_cell.weekend {
			background-color: #EFF5FD !important;
		}

		.gantt_task_row.gantt_selected .gantt_task_cell.weekend {
			background-color: #F8EC9C !important;
		}

    .gantt_grid_scale .gantt_grid_head_cell,
    .gantt_task .gantt_task_scale .gantt_scale_cell {
        color:  #A8ACB2;
        font-family: Roboto;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        letter-spacing: 0.24px;
        text-transform: capitalize;
	}

    .gantt_task_progress {
        text-align: left;
        padding-left: 10px;
        box-sizing: border-box;
        color: white;
        font-weight: bold;
    }


    .gantt_task_line.parent-task .gantt_task_progress_drag {
      display: none; /* Remove the progress drag handle */
      pointer-events: none; /* Ensure it's not interactable */
    }


    .gantt_task_line.parent-task {
      border-color: #ccc; /* Optional: lighter border */
    }


    .deadline {
        position: absolute;
        border-radius: 12px;
        border: 2px solid #585858;
        -moz-box-sizing: border-box;
        box-sizing: border-box;

        width: 22px; 
        height: 22px;
        margin-left: -11px;
        margin-top: 6px;
        z-index: 1;
    }


    .overdue-indicator {
        width: 24px;
        margin-top: 5px;
        height: 24px;
        -moz-box-sizing: border-box;
        box-sizing: border-box;
        border-radius: 17px;
        color: white;
        background: rgb(255, 60, 60);
        line-height: 25px;
        text-align: center;
        font-size: 24px;
    }


    .baseline {
        position: absolute;
        border-radius: 2px;
        opacity: 0.6;
        margin-top: -7px;
        height: 12px;
        background: #ffd180;
        border: 1px solid rgb(255, 153, 0);
    }

    /* move task lines upper */
    .gantt_task_line, .gantt_line_wrapper {
        margin-top: 0px;
    }

    .gantt_task_line.gantt_project .gantt_task_progress{
      background-color: transparent !important;
    }

    .gantt_side_content {
        margin-bottom: 7px;
    }


    .gantt_side_content.gantt_right {
        bottom: 0;
    }

    .gantt_row_project{
        font-weight: bold;
    }

    .gantt-info ul{
        line-height: 150%;
    }


    .gantt_task_link.start_to_start .gantt_line_wrapper div {
        background-color: rgb(248, 242, 65);
    }

    .gantt_task_link.start_to_start:hover .gantt_line_wrapper div {
        box-shadow: 0 0 5px 0px rgb(248, 242, 65);
    }

    .gantt_task_link.start_to_start .gantt_link_arrow_right {
        border-left-color: rgb(248, 242, 65);
    }

    .gantt_task_link.finish_to_start .gantt_line_wrapper div {
        background-color: #f38f24;
    }

    .gantt_task_link.finish_to_start:hover .gantt_line_wrapper div {
        box-shadow: 0 0 5px 0px #f38f24;
    }

    .gantt_task_link.finish_to_start .gantt_link_arrow_right {
        border-left-color: #f38f24;
    }

    .gantt_task_link.finish_to_finish .gantt_line_wrapper div {
        background-color: #55d822;
    }

    .gantt_task_link.finish_to_finish:hover .gantt_line_wrapper div {
        box-shadow: 0 0 5px 0px #55d822;
    }

    .gantt_task_link.finish_to_finish .gantt_link_arrow_left {
        border-right-color: #55d822;
    }

    .gantt_task_link.start_to_finish .gantt_line_wrapper div {
      background-color: #cf22d8;
    }

    .gantt_task_link.start_to_finish:hover .gantt_line_wrapper div {
        box-shadow: 0 0 5px 0px #cf22d8;
    }

    .gantt_task_link.start_to_finish .gantt_link_arrow_left {
        border-right-color: #cf22d8;
    }

    .gantt_task_link.criticalPathLink .gantt_line_wrapper div {
      background-color: #ff5252;
    }

    .gantt_task_link.criticalPathLink:hover .gantt_line_wrapper div {
        box-shadow: 0 0 5px 0px #ff5252;;
    }

    .gantt_task_link.criticalPathLink .gantt_link_arrow_left {
        border-right-color: #ff5252;;
    }

    .gantt_tree_content{
        color:  #1c0808;
        font-family: Roboto;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        letter-spacing: 0.24px;
        text-transform: capitalize;
        overflow: hidden;
    }


    .gantt_grid_data .gantt_row, .gantt_grid_data {
        transition: background .15s;
        border-bottom: 0.5px solid var(--blue-grey-50, #B9C0CA);
    }

    .gantt_grid_data .gantt_cell {
        border-left: none;
        color: rgba(0,0,0,.8);
        font-weight: 400;
        border-right: 0.5px solid var(--blue-grey-50, #B9C0CA);
    }

    .gantt_grid .gantt_grid_scale .gantt_grid_head_cell {
        color: rgba(0,0,0,.54);
        border: 0.5px solid var(--blue-grey-50, #B9C0CA);
        font-weight: 500;
    }

    .gantt_drag_marker.gantt_grid_resize_area, .gantt_drag_marker.gantt_row_grid_resize_area{
        background: red;
        opacity: 0.3;
    }


    .add-button {
        cursor: pointer;
        opacity: 0.4;
        position: relative;
        display: inline-block;
        text-align: center;
        font-size: 21px;
        padding-top: 5px;
        padding-bottom: 5px;
        padding-right: 5px;
        margin: 0px;
      }
      
      .add-button:hover {
        opacity: 1;
      }
      
      .add-button:after {
        color: #1a5199;
        content: "add";
      }

      .edit-button {
        cursor: pointer;
        opacity: 0.4;
        position: relative;
        display: inline-block;
        text-align: center;
        font-size: 21px;
        padding-top: 5px;
        padding-bottom: 5px;
        padding-right: 5px;
        margin: 0px;
      }
      
      .edit-button:hover {
        opacity: 1;
      }
      
      .edit-button:after {
        color: #1a5199;
        content: "edit";
      }

      .header {
        background-color: white;
      }
      

      
      .gantt_task_cell {
        border-right: 1px solid #66615b9c !important;
      }

      .task-execution {
        background-color: #ffa502;
        border-color: #ffa502;
        color: white;
      }
      .task-completed {
        background-color: #009432;
        border-color: #009432;
      }
      .task-open {
        background-color: #b7b7b7;
        border-color: #b7b7b7;
        color: #1a1a1a;
      }
      
      .task-draft {
        background-color: #3c6382;
        border-color: #3c6382;
      }

      .task-project{
        background-color:  var(--task_project);
        border-color:  var(--task_project);
      }

      .task-item{
        background-color: var(--task_item);
        border-color: var(--task_item);
      }

      .task-wave{
        background-color: var(--task_wave);
        border-color: var(--task_wave);
      }

      .task-phase{
        background-color: var(--task_phase);
        border-color: var(--task_phase);
      }
      

      .task-activity{
        background-color: var(--task_activity);
        border-color: var(--task_activity);
      }


      // .highlighted_task{
      //   background: #F7F9FB !important;
      // }


    

      .gantt_task_progress{
        padding-left: 10px;
        box-sizing: border-box;
        color: #0a0000;
        font-weight: bold;
        text-align: right;
      }


      .gantt_grid_data .gantt_row.gantt_selected, .gantt_grid_data .gantt_task_row.gantt_selected 
      {
        background-color: #d4ffdf;
      }


      // .placeholder_task{
      //   color: #c7c7c7 !important;
      // }

      .slack {
        position: absolute;
        border-radius: 0;
        opacity: 0.7;
  
        border: none;
        border-right: 1px solid #b6b6b6;
        margin-left: -2px;
        background: #b6b6b6;
        background: repeating-linear-gradient(
            45deg, #FFFFFF,
            #FFFFFF 5px,
            #b6b6b6 5px,
            #b6b6b6 10px
        );
      }
    
}
// .highlighted_task{
//     background:  #F7F9FB !important;
//   }


.gantt_message_area{
    visibility: hidden;
}

.light-box-panel-class .mat-dialog-container {
  max-width: none;
  z-index: 9999; // Adjust this value as needed
}



