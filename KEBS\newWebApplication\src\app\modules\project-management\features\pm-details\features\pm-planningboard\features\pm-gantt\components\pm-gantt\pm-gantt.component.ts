import { Component, ElementRef, OnInit, ViewChild, ViewEncapsulation, Input, Inject, HostListener, ViewContainerRef } from '@angular/core';
import * as _ from 'underscore';
import * as moment from 'moment';
import { PmGanttService } from '../../services/pm-gantt.service';
import { DOCUMENT } from '@angular/common';
import { v4 as uuidv4 } from 'uuid';
import 'dhtmlx-gantt';
import { Gantt } from 'dhtmlx-gantt';
import { PmMasterService } from 'src/app/modules/project-management/services/pm-master.service';
declare let gantt: any;
import { projectDialog } from 'src/app/modules/project-management/shared-lazy-loaded/animation/projectDialog';
import { PmPlanningLightBoxComponent } from "../../../../components/pm-planning-light-box/pm-planning-light-box.component"
import { ToasterService } from 'src/app/services/toaster-service/toaster.service';
import { UtilityService } from 'src/app/services/utility/utility.service';
import './api.js';
import { Subscription } from 'rxjs';
import { ToasterMessageService } from 'src/app/modules/project-management/shared-lazy-loaded/components/toaster-message/toaster-message.service';
import { ProjectNameDisplayService } from 'src/app/modules/project-management/features/pm-details/services/project-name-display.service';
import { PmAuthorizationService } from 'src/app/modules/project-management/services/pm-authorization.service';
import { NgxSpinnerService } from 'ngx-spinner';


@Component({
  selector: 'pm-gantt',
  templateUrl: './pm-gantt.component.html',
  styleUrls: ['./pm-gantt.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class PmGanttComponent implements OnInit {

  @HostListener('window:resize')
  onResize() {
    this.calculateDynamicContentHeight();
  }

  currentDate: any = moment().format();
  retrieveMessages: any = [];
  dialog_animation = {
    entryAnimation: {
      keyframes: [
        { transform: 'translateX(100%)' },
        { transform: 'translateX(0)' },
      ],
      keyframeAnimationOptions: {
        duration: 250,
        easing: 'ease-in-out',
      },
    },
    exitAnimation: {
      keyframes: [
        { transform: 'translateX(0)' },
        { transform: 'translateX(100%)' },
      ],
      keyframeAnimationOptions: {
        duration: 250,
        easing: 'ease-in-out',
      },
    },
  }

  @ViewChild('gantt_here', { static: true }) ganttContainer: ElementRef
  @HostListener('document:fullscreenchange', ['$event'])
  @HostListener('document:webkitfullscreenchange', ['$event'])
  @HostListener('document:mozfullscreenchange', ['$event'])
  @HostListener('document:MSFullscreenChange', ['$event'])

  @Input() projectId: any;
  @Input() itemId: any;
  @Input() formConfig: any;

  dp: any;

  elem: any;


  dynamicHeight: any;
  dynamicinnerHeight: any;


  saving: boolean=false;
  sliderName = "month"
  highlightTasks = [];
  attachedEvents: any=[];
  highlightSearch = {};
  projectStartDate = new Date(2021, 0, 1);
  projectEndDate = new Date(2029, 11, 31);
  autoSchedule: boolean = false;
  ganttInstance: any;
  item_start_date: any;
  item_end_date: any;
  criticalPathEnabled: boolean=false;
  cachedSettings: any={};
  dateFix: boolean;
  fixedDuration: boolean=false;
  columnConfig: any = []
  statusList: any = []
  filterApplied: boolean = false;
  ganttColumnList: any = [];
  loading: boolean = false;
  inline_actual_edit:boolean=false;
  gridView = {
    rows: [
      {
        view: "grid",
        scrollable: true,
        scrollX: "scrollHor1",
        scrollY: "scrollVer"
      },
      {
        view: "scrollbar",
        id: "scrollHor1",
        scroll: 'x',
        group: 'hor'
      },
    ]
  }

  timelineView: any = {
    rows: [
      {
        view: "timeline",
        scrollX: "scrollHor",
        scrollable: true,
        scrollY: "scrollVer"
      },
      {
        view: "scrollbar",
        id: "scrollHor",
        scroll: "x",
        group: "hor"
      }
    ]
  }

  fullLayout: any = {
    css: "gantt_container",
    cols: [
      this.gridView,
      {
        resizer: true, width: 2
      },
      this.timelineView,
      {
        view: "scrollbar",
        id: "scrollVer"
      }
    ]
  }

  onlyGrid = {
    css: "gantt_container",
    
    cols: [
      {
        rows: [
          {
            view: "grid", 
            scrollable: true,
            scrollY:"scrollVer",
            scrollX:"scrollHor1"
          },
          {
            view: "scrollbar", 
            id:"scrollHor1"
          }
        ]
      },
      {
        view: "scrollbar",
        id: "scrollVer"
      }
    ]
  };

  gridLayout: any = {

    css: "gantt_container",
    cols: [
      this.gridView,
      {
        view: "scrollbar",
        id: "scrollver"
      }
    ]
  }


  totalLayout =  {
    css: "gantt_container",
    rows:[
        {
           cols: [
            this.gridView,
            { resizer: true, width: 1 },
            {
              view: "timeline", 
              scrollY:"scrollVer"
            },
            {
              view: "scrollbar", 
              id:"scrollVer"
            }
        ]}
    ]
}

totalTimeLineLayout =  {
  css: "gantt_container",
  rows:[{
          view: "timeline",
          scrollable: true, 
          scrollY:"scrollVer",
          scrollX: "scrollHor1"
        },
        {
          view: "scrollbar", 
          id:"scrollVer"
        },
        {
          view: "scrollbar",
          id: "scrollHor1",
          scroll: 'x',
          group: 'hor'
        }
  ]
}

  timelineLayout = {
    css: "gantt_container",
    cols: [
      {
        rows: [
          {
            view: "timeline", 
            scrollable: true,
            scrollY:"scrollVer",
            scrollX:"scrollHor1"
          },
          {
            view: "scrollbar", 
            id:"scrollHor1"
          }
        ]
      },
      {
        view: "scrollbar",
        id: "scrollVer"
      }
    ]
  };

  cascadeAction = {
    indentright: true,
    indentleft: true,
    del: true
  };

  singularAction = {
    undo: true,
    redo: true
  };


  zoomConfigs = {
    levels: [
      {
        name: "day",
        configLevel: "Day",
        scale_height: 50,
        min_column_width: 100,
        scales: [
          { unit: "day", step: 1, format: "%D, %d %M" }
        ],
        id: 0
      },
      {
        name: "week",
        configLevel: "Week",
        scale_height: 50,
        min_column_width: 80,
        scales: [
          { unit: "month", format: "%F, %Y" },
          { unit: "week", step: 1, format: "Week %W" },
        ],
        id: 25
      },
      {
        name: "month",
        configLevel: "Month",
        scale_height: 50,
        min_column_width: 120,
        scales: [
          { unit: "month", format: "%F, %Y" },
          // { unit: "week", format: "Week #%W" }
        ],
        id: 50
      },
      {
        name: "quarter",
        configLevel: "Quarter",
        height: 50,
        min_column_width: 90,
        scales: [
          { unit: "month", step: 1, format: "%M %Y" },
          {
            unit: "quarter", step: 1, format: (date) => {
              let dateToStr = gantt.date.date_to_str("%M");
              let endDate = gantt.date.add(gantt.date.add(date, 3, "month"), -1, "day");
              return dateToStr(date) + " - " + dateToStr(endDate);
            }
          }
        ],
        id: 75
      },
      {
        name: "year",
        configLevel: "Year",
        scale_height: 50,
        min_column_width: 30,
        scales: [
          { unit: "year", step: 1, format: "%Y" }
        ],
        id: 100
      }
    ]
  };


  actions: any = {
    undo: () => {
      gantt.ext.undo.undo();
    },
    redo: () => {
      gantt.ext.undo.redo();
    },
    indentright: (task_id) => {
      var prev_id = gantt.getPrevSibling(task_id);
      while (gantt.isSelectedTask(prev_id)) {
        var prev = gantt.getPrevSibling(prev_id);
        if (!prev) break;
        prev_id = prev;
      }
      if (prev_id) {
        var new_parent = gantt.getTask(prev_id);
        if (this.deleteCheck(new_parent.id) && new_parent.gantt_type_id==5) {
          // Retrieve the warning message
          const timesheet_activity_msg = this.retrieveMessages.length > 0 
            ? this.retrieveMessages[0].errors.timesheet_activity_msg 
              ? this.retrieveMessages[0].errors.timesheet_activity_msg 
              : 'Function Not Possible' 
            : 'Function Not Possible';
      
          // Show the warning message
          this.toasterService.showWarning(timesheet_activity_msg, 10000);
      
          // Exit the function without adding a new task
          return;
        }
        gantt.moveTask(task_id, gantt.getChildren(new_parent.id).length, new_parent.id);
        new_parent.type = gantt.config.types.project;
				new_parent.$open = true;
        new_parent.$open = true;
        gantt.updateTask(task_id);
        gantt.updateTask(new_parent.id);
        return task_id;
      }
      return null;
    },
    indentleft: (task_id, initialIndexes, initialSiblings) => {
      var cur_task = gantt.getTask(task_id);
      var old_parent = cur_task.parent;
      var top_parent = gantt.getTaskByWBSCode("1")
      if (gantt.isTaskExists(old_parent) && old_parent != gantt.config.root_id && old_parent !=top_parent.id) {
        var index = gantt.getTaskIndex(old_parent) + 1;
        var prevSibling = initialSiblings[task_id].first;

        if (gantt.isSelectedTask(prevSibling)) {
          index += (initialIndexes[task_id] - initialIndexes[prevSibling]);
        }
        gantt.moveTask(task_id, index, gantt.getParent(cur_task.parent));

        gantt.updateTask(task_id);

        let parentTask = gantt.getTask(old_parent)
        parentTask.type = "task"
        //console.log("Parent Task",parentTask)
        gantt.updateTask(parentTask, old_parent);
        return task_id;
      }
      return null;
    },
    delete: async(task_id) => {
      if (gantt.isTaskExists(task_id))
      {
        let currTask = gantt.getTask(task_id)
        if(currTask.type_name =="project")
        {
          const project_delete_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.project_delete_msg ? this.retrieveMessages[0].errors.project_delete_msg : 'Project cannot be deleted!' : 'Project cannot be deleted!';
          this.toasterService.showWarning(project_delete_msg,2000);
        }
        else if(gantt.hasChild(currTask.id))
        {
          let gantt_name = await this.getGanttName(currTask.type_name)
          await this.utilityService.openConfirmationSweetAlertWithCustom("Do you want to delete the "+gantt_name+"?","Once confirmed! Entire child records will get deleted!").then(async(res)=>{
            if(res){
              let deleteRecords = _.where(this.formConfig,{field_name:"delete_activity_timesheet", type:"gantt", is_active: true})

              if(deleteRecords.length>0)
              {
                if(this.deleteCheck(task_id))
                {
                  let deleteRecords = _.where(this.formConfig,{field_name:"delete_activity_timesheet_ask", type:"gantt", is_active: true})

                  if(deleteRecords.length>0)
                  {
                    await this.utilityService.openConfirmationSweetAlertWithCustom("Timesheet has been booked! Do you want to delete the "+gantt_name+"?","Once confirmed! "+ gantt_name +" will get deleted!").then(res1=>{
                      if(res1){
                        gantt.deleteTask(task_id);
                      }
                    })
                  }
                  else
                  {
                    const timesheet_activity_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.timesheet_activity_msg ? this.retrieveMessages[0].errors.timesheet_activity_msg : 'Function Not Possible' : 'Function Not Possible';
                    this.toasterService.showWarning(timesheet_activity_msg, 10000);
                    return
                    
                  }
                }
                else
                {
                  gantt.deleteTask(task_id);
                }
              }
              else
              {
                gantt.deleteTask(task_id);
              }
            }
          });
        }
        else
        {
          
          let gantt_name = await this.getGanttName(currTask.type_name)
          //console.log(currTask.type_name, gantt_name)
          await this.utilityService.openConfirmationSweetAlertWithCustom("Do you want to delete the "+gantt_name+"?","Once confirmed! "+ gantt_name +" will get deleted!").then(async(res)=>{
            if(res){

              let deleteRecords = _.where(this.formConfig,{field_name:"delete_activity_timesheet", type:"gantt", is_active: true})

              if(deleteRecords.length>0)
              {
                console.log("Delete Recrds", deleteRecords, this.deleteCheck(task_id))
                if(this.deleteCheck(task_id))
                {
                  let deleteRecords = _.where(this.formConfig,{field_name:"delete_activity_timesheet_ask", type:"gantt", is_active: true})

                  if(deleteRecords.length>0)
                  {
                    await this.utilityService.openConfirmationSweetAlertWithCustom("Timesheet has been booked! Do you want to delete the "+gantt_name+"?","Once confirmed! "+ gantt_name +" will get deleted!").then(res1=>{
                      if(res1){
                        gantt.deleteTask(task_id);
                      }
                    })
                  }
                  else
                  {
                    console.log("Timesheet is booked;")
                    const timesheet_activity_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.timesheet_activity_msg ? this.retrieveMessages[0].errors.timesheet_activity_msg : 'Function Not Possible' : 'Function Not Possible';
                    this.toasterService.showWarning(timesheet_activity_msg, 10000);
                    return;
                    
                  }
                }
                else
                {
                  gantt.deleteTask(task_id);
                }
              }
              else
              {
                gantt.deleteTask(task_id);
              }
            }
          });
        }
      } 
      return task_id;
    },
    addDates: (task_id) => {
      var task = gantt.getTask(task_id);
      task.start_date = gantt.date.add(task.start_date, 1, "day");
      task.end_date = gantt.calculateEndDate(task.start_date, task.duration);
      gantt.updateTask(task.id);
    },
    reduceDates: (task_id) => {
      var task = gantt.getTask(task_id);
      task.start_date = gantt.date.add(task.start_date, -1, "day");
      task.end_date = gantt.calculateEndDate(task.start_date, task.duration);
      gantt.updateTask(task.id);
    },
    moveup: (task_id, initialIndexes, initialSiblings) => {
      //console.log("Intial Index Move Up", initialIndexes)
      gantt.moveTask(task_id, initialIndexes[task_id] -1, gantt.getTask(task_id).parent);
    },
    movedown: (task_id, initialIndexes, initialSiblings) => {
      //console.log("Intial Index Move Down", initialIndexes)
      gantt.moveTask(task_id, initialIndexes[task_id] +1, gantt.getTask(task_id).parent);
    },

    insertBelow: (task_id) => {
      let task_data = gantt.getTask(task_id)
      if (this.deleteCheck(task_id) && task_data.gantt_type_id==5) {
        // Retrieve the warning message
        const timesheet_activity_msg = this.retrieveMessages.length > 0 
          ? this.retrieveMessages[0].errors.timesheet_activity_msg 
            ? this.retrieveMessages[0].errors.timesheet_activity_msg 
            : 'Function Not Possible' 
          : 'Function Not Possible';
    
        // Show the warning message
        this.toasterService.showWarning(timesheet_activity_msg, 10000);
    
        // Exit the function without adding a new task
        return;
      }

      gantt.getTask(task_id).$open = true

      var list = gantt.serverList("gantt_list");

      let data = _.where(list, { key: this.ganttService.ganttConfig.ganttTypeId })
      let billableDefaulted = _.where(this.formConfig, { type: "activity", field_name: "billable_activity"});
      billableDefaulted = billableDefaulted[0] ? billableDefaulted[0]  : ''

      let billableValue
      if(billableDefaulted != '' && billableDefaulted['default_value_enable']){
          billableValue = billableDefaulted['value'] ? billableDefaulted['value'] : ''
      }      

      var task = {
        text: data.length > 0 ? "+ Add a new " + data[0]['name'] : "+ Add a new Task",
        start_date: this.dateFix ? this.convertUTCDate(this.ganttService.ganttConfig.item_start_date) : task_data.start_date,
        duration: this.dateFix ?gantt.calculateDuration({
          start_date: this.convertUTCDate(this.ganttService.ganttConfig.item_start_date), 
          end_date: this.convertUTCDate(this.ganttService.ganttConfig.item_end_date)
        }) : 1,
        parent: task_id,
        type: "task",
        type_name: this.ganttService.ganttConfig.ganttTypeId,
        _avoid: true,
        billable_act: billableValue ? billableValue : false
      };

      gantt.addTask(task);
    },

    insertBelowSame: (task_id) => {
      let billableDefaulted = _.where(this.formConfig, { type: "activity", field_name: "billable_activity"});
      billableDefaulted = billableDefaulted[0] ? billableDefaulted[0]  : ''
      
      
      let billableValue
      if(billableDefaulted != '' && billableDefaulted['default_value_enable']){
          billableValue = billableDefaulted['value'] ? billableDefaulted['value'] : ''
      }  
      let task_data = gantt.getTask(task_id)

      gantt.getTask(task_id).open = true

      var list = gantt.serverList("gantt_list");

      let data = _.where(list, { key: this.ganttService.ganttConfig.ganttTypeId })
     
      var task = {
        text: data.length > 0 ? "+ Add a new " + data[0]['name'] : "+ Add a new Task",
        start_date: this.dateFix ? this.convertUTCDate(this.ganttService.ganttConfig.item_start_date) : task_data.start_date,
        duration: this.dateFix ?gantt.calculateDuration({
          start_date: this.convertUTCDate(this.ganttService.ganttConfig.item_start_date), 
          end_date: this.convertUTCDate(this.ganttService.ganttConfig.item_end_date)
        }) : 1,
        parent: task_data.parent,
        type: "task",
        type_name: this.ganttService.ganttConfig.ganttTypeId,
        _avoid: true,
        billable_act: billableValue ? billableValue : ''
      };

      gantt.addTask(task);
    },
    enableFullScreen: () => {

        

        this.document.documentElement.style.setProperty(
          '--mainWidth',
          '100%'
        );
        

        this.ganttService.ganttConfig.fullScreen=true;
        
        this.calculateDynamicContentHeight();
      


    },
    closeFullScreen:()=>{
      this.document.documentElement.style.setProperty(
        '--mainWidth',
        '99%'
      );
      
      this.ganttService.ganttConfig.fullScreen=false;

      this.calculateDynamicContentHeight();
    
    },
    zoom_to_fit: () => {
      gantt.$zoomToFit = !gantt.$zoomToFit;
      if (gantt.$zoomToFit) 
      {
        gantt.ext.zoom.setLevel("year")
      } 
      else 
      {
        gantt.ext.zoom.setLevel("month")
      }
      gantt.render();
    },
    expandmore: () => {
      gantt.eachTask((task) => {
        task.$open = true;
      });
      gantt.render();
    },
    expandless: () => {
      if(!this.ganttService.ganttConfig.filterApplied)
      {
        gantt.eachTask((task) => {
          task.$open = false;
        });
        gantt.render();
      }
    },
    slide: () => {
      this.sliderName = this.ganttService.ganttConfig.sliderValue;
      gantt.render();
      gantt.ext.zoom.setLevel(this.sliderName);
      


    },
    autoSchedule: () => {
      this.autoSchedule = !this.autoSchedule
      gantt.config.auto_scheduling = this.autoSchedule;
    },
    showCriticalPath: () => {
      this.criticalPathEnabled = true;
      gantt.config.highlight_critical_path = true;
      gantt.render();
    },
    hideCriticalPath: () => {
      this.criticalPathEnabled = false;
      gantt.config.highlight_critical_path = false;
      gantt.render();
    },
    gridView: () => {
      gantt.config.layout = this.onlyGrid
      gantt.init(this.ganttContainer.nativeElement);
      gantt.render();
    },
    timeView: () => {
      gantt.config.layout = this.timelineLayout;
      gantt.init(this.ganttContainer.nativeElement);
      gantt.render();
    
    },
    timegridView: () => {
      gantt.config.layout = this.fullLayout
      gantt.config.show_grid =true
      gantt.config.show_chart =true
      
      gantt.init(this.ganttContainer.nativeElement);
      gantt.render();
    },
    exportPdf: () => {
      gantt.config.columns[0]['hide']=true
      gantt.config.columns[2]['hide']=true
      gantt.config.columns[3]['hide']=false

      gantt.exportToPDF({
        name:"project.pdf",
        header:"<h1>"+this.ganttService.ganttConfig.project_name+"</h1>",
        raw:true
      });

      gantt.config.columns[0]['hide']=false
      gantt.config.columns[2]['hide']=false
      gantt.config.columns[3]['hide']=true
    },
    exportPNG: () => {

      gantt.config.columns[0]['hide']=true
      gantt.config.columns[2]['hide']=true
      gantt.config.columns[3]['hide']=false


      gantt.exportToPNG({
        name:"project.png",
        header:"<h1>"+this.ganttService.ganttConfig.project_name+"</h1>",
        raw:true
      });
      gantt.config.columns[0]['hide']=false
      gantt.config.columns[2]['hide']=false
      gantt.config.columns[3]['hide']=true
    },
    exportMSProject: () => {
      gantt.exportToMSProject({
        name:'project.xml',
        auto_scheduling: true,
        skip_circular_links: true,

      });
    },
    exportSpreadSheet: () => {
      let tempTasks = [];
      gantt.eachTask((task) => {
        if(!task.text.includes("+ Add"))
        {
          tempTasks.push(
            JSON.parse(
              JSON.stringify(this.excelFormatter(JSON.parse(JSON.stringify(task))))
            )
          );
        }
      });
      


      //console.log("Export Excel", tempTasks)

      let export_columns = [];
      let columnList = gantt.config.columns;
      for (let i = 0; i < columnList.length - 1; i++) {
        if((!columnList[i]['hide'] || columnList[i]['id']=='type_download') && columnList[i]['is_download'])
        {

          export_columns.push({
            id: gantt.config.columns[i].name,
            header: gantt.config.columns[i].label,
            width: 40,
          });
        }
      }

      const multipleOwnerConfig = _.where(this.formConfig, { type: "activity", field_name: "multi-owner", is_active: true });

      for(let task of tempTasks)
      {
          task.start_date = moment(task.start_date).format("DD-MMM-YYYY")
          task.end_date = moment(task.end_date).format("DD-MMM-YYYY")
          task.actual_start_date = task.actual_start_date!="" ? moment(task.actual_start_date).format("DD-MMM-YYYY") :""
          task.actual_end_date = task.actual_end_date!="" ? moment(task.actual_end_date).format("DD-MMM-YYYY") :""
          task.type_name = this.getGanttName(task.type_name)
          task.type ="task"
          task.owner_list ="";

          if(multipleOwnerConfig && multipleOwnerConfig.length > 0){
           
            let owner = task.assigned_to_list ?  task.assigned_to_list :[];
            
            for(let o of owner)
            {
              let aid = _.where(gantt.serverList("employee_list"),{associate_id: o})
              
              if(aid && aid.length>0)
              {
                  task.owner_list += aid[0]['name']+", "
              }
            }
            
            
          }
          else{
         
            let aid = _.filter(gantt.serverList("employee_list"),(res)=>{
              if(res.associate_id+'' == task.assigned_to_list+'')
              {
                return res;
              }
            })
            if(aid && aid.length>0)
            {
              task.owner_list += aid[0]['name']
            }
          } 
      }

      console.log("Export Columns", export_columns, tempTasks)

      
      gantt.exportToExcel({
        name: this.ganttService.ganttConfig.project_name + "("+moment().format("DD-MMM-YYYY")+").xlsx",
        columns: export_columns,
        data: tempTasks,
        date_format: "dd-mmm-yyyy"
      });
    },
    applyColumnConfig: () => {


      this.applyColumnConfigMain()

      gantt.refreshData();
      gantt.render();


    },
    insertAdaptTemplate:()=>{
      this.insertTemplateData();
    },
    showSlack:()=>{
      gantt.config.show_slack = true;
      gantt.render();
    },
    closeSlack:()=>{
      gantt.config.show_slack = false;
      gantt.render();
    },

    applyFilter:()=>{
      gantt.render();
    }


  };


  
  columnSubscription: Subscription;
  multipleOwner:boolean =  false;
  disableBillable:boolean = false;
  status_array:any=[]

  constructor(private ganttService: PmGanttService,
    private dialog: projectDialog,
    @Inject(DOCUMENT) private document: any,
    private utilityService: UtilityService,
    private viewContainerRef: ViewContainerRef,
    private toasterService: ToasterMessageService,
    private nameService: ProjectNameDisplayService,
    private authService: PmAuthorizationService,
    private masterSerivce: PmMasterService,
    private spinnerService: NgxSpinnerService
    ) {
      this.columnSubscription = this.ganttService.getColumns().subscribe((res: any) =>{
        this.ganttColumnList = res;
        //console.log(this.ganttColumnList)
        this.performGanttAction('applyColumnConfig')
      })
    }

  async ngOnInit() {
    await this.masterSerivce.getPMFormCustomizeConfigV().then((res)=>{
      this.formConfig = res;

      const multipleOwnerConfig = _.where(this.formConfig, { type: "activity", field_name: "multi-owner", is_active: true });
      if(multipleOwnerConfig && multipleOwnerConfig.length > 0){
        this.multipleOwner = true;
      }
      let billableDefaulted = _.where(this.formConfig, { type: "activity", field_name: "billable_activity"});
      billableDefaulted = billableDefaulted[0] ? billableDefaulted[0]  : ''
      if(billableDefaulted != '' && billableDefaulted['is_disabled']){
        this.disableBillable = billableDefaulted['is_disabled'] 
      }
    })
    
    this.ganttService.ganttConfig.projectId = this.projectId;

    this.ganttService.ganttConfig.itemId = this.itemId;

    this.elem = document.documentElement;

    gantt = Gantt.getGanttInstance();

    this.createDataProcessor();

    this.intializeGanttView();

    this.intializeMasterData();

    this.applyTimelineClassColor()

    this.initGantt();

    this.viewGantt();

    this.getProjectGanttData()

    gantt.init(this.ganttContainer.nativeElement);

  }

  ngAfterViewInit() {

    document.documentElement.style.setProperty(
      '--mainWidth',
      '99%'
    );

    this.ganttService.ganttConfig.fullScreen=false;
    this.calculateDynamicContentHeight();
  }



  intializeMasterData() {
    gantt.serverList("status_list", this.ganttService.ganttConfig.masterData['status_list']);
    gantt.serverList("gantt_list", this.ganttService.ganttConfig.masterData['gantt_list']);
    gantt.serverList("billable_list", this.ganttService.ganttConfig.masterData['billable_list']);
    gantt.serverList("fricew_list", this.ganttService.ganttConfig.masterData['fricew_list']);
    gantt.serverList("dayType_list", this.ganttService.ganttConfig.masterData['dayType_list']);
    gantt.serverList("priority", this.ganttService.ganttConfig.masterData['priority_list']);
    const status_array_config=_.where(this.formConfig, { type: "gantt", field_name: "status_array_config"});
    if(status_array_config.length > 0){
     this.status_array = status_array_config[0].default;
    }
    else{
      this.status_array = [4,5,7]
    }
    let status_list = gantt.serverList("status_list")
    let status_action_list = _.filter(status_list,(res)=>{
      if(_.contains(this.status_array, res['key']))
      {
        return res;
      }
    })

    gantt.serverList("status_action_list",status_action_list);
    gantt.serverList("employee_list", this.ganttService.ganttConfig.masterData['employee_list']);
  }






  intializeGanttView() {

    this.ganttService.ganttConfig.performAction = this.performAction.bind(this)

  }


  async getProjectGanttData() {


    await this.ganttService.getGanttProjectData(this.projectId, this.itemId).then((res) => {
      gantt.parse(res)
      // gantt.refreshData();
      // gantt.render();
    })

    var top_parent = gantt.getTaskByWBSCode("1")
    gantt.getTask(top_parent['id']).readonly = true
  }



  createDataProcessor() {

    this.dp = gantt.createDataProcessor({
      task: {
        create: async(data) => {
          if (data && !data._avoid && !data.text.includes("+ Add")) {
            data = gantt.getTask(data.id)
            
            data.wbs_code = gantt.getWBSCode(data)
            data.mother_workstream_id = await this.getMotherWorkstreamGanttId(data.id)
            data.mother_phase_id = await this.getMotherPhaseGanttId(data.id)
            data.has_child = this.validateHasChild(data.id)
            data.type_id = this.getGanttTypeId(data.type_name)
            data.url = await this.generateURL(data.id)
            data.owner = _.findWhere(this.formConfig,{ type: "activity", field_name: "multi-owner", is_active: true }) ? data.assigned_to_list: [data.assigned_to_list]

            this.saving = true;

            let record = JSON.parse(JSON.stringify(data))
            record.start_date = moment(record.start_date).format("YYYY-MM-DD")
            record.end_date = moment(record.end_date).format("YYYY-MM-DD")
            record.updateDuration = _.findWhere(this.formConfig,{type:"gantt", field_name:"updateProjectDuration", is_active: true}) ? true: false

            this.ganttService.createGanttData(record).then((res)=>{
              this.saving = false;
              this.ganttService.ganttConfig.saving=false;
            })

            return
          }
        },
        update: async(data, id) => {
          if (data && !data._avoid && !data.text.includes("+ Add")) {
            data = gantt.getTask(data.id)
            data.wbs_code = gantt.getWBSCode(data)
            data.mother_workstream_id = await this.getMotherWorkstreamGanttId(data.id)
            data.mother_phase_id = await this.getMotherPhaseGanttId(data.id)
            data.has_child = this.validateHasChild(data.id)
            data.type_id = this.getGanttTypeId(data.type_name)
            data.owner = _.findWhere(this.formConfig,{ type: "activity", field_name: "multi-owner", is_active: true }) ? data.assigned_to_list: [data.assigned_to_list]

            data.url = await this.generateURL(data.id)
            this.saving = true;

            let record = JSON.parse(JSON.stringify(data))
            record.start_date = moment(record.start_date).format("YYYY-MM-DD")
            record.end_date = moment(record.end_date).format("YYYY-MM-DD")
            record.updateDuration = _.findWhere(this.formConfig,{type:"gantt", field_name:"updateProjectDuration", is_active: true}) ? true: false
            
            record.actual_start_date = record?.actual_start_date && moment(record?.actual_start_date).isValid()
  ? moment(record.actual_start_date).format("YYYY-MM-DD")
  : null; // or set to NULL if missing

record.actual_end_date = record?.actual_end_date && moment(record?.actual_end_date).isValid()
  ? moment(record.actual_end_date).format("YYYY-MM-DD")
  : null; // or set to NULL if missing
            
            this.ganttService.updateGanttData(record, id).then((res)=>{
              this.saving =false;
              this.ganttService.ganttConfig.saving=false;
              if(data.type_name=="project")
              {
                  if(this.nameService.projectName !=data.text)
                    this.nameService.getProjectName(this.projectId, this.itemId)
              }
            })

            

            return;
          }
        },
        delete: (id) => {
          this.saving =true;
          this.ganttService.deleteGanttData(id, this.currentDate).then((res)=>{
            this.saving =false;
            this.ganttService.ganttConfig.saving=false;
          })
        }
      },
      link: {
        create: (data) => {
          this.saving = true;
          this.ganttService.createGanttLinks(data).then((res)=>{
            this.saving =false
            this.ganttService.ganttConfig.saving=false;
          })
        },
        update: (data, id) => {
          this.saving = true;
          this.ganttService.updateGanttLinks(data, id).then((res)=>{
            this.saving =false;
            this.ganttService.ganttConfig.saving=false;
          })
        },
        delete: (id) => {

          this.saving =true;
          this.ganttService.deleteGanttLinks(id).then((res)=>{
            this.ganttService.ganttConfig.saving=false;
          })
        }
      }
    })

    this.dp.attachEvent("onBeforeDataSending", (id, state, data)=>{
      
      this.ganttService.ganttConfig.saving=true;
      return true;
    });

    this.dp.attachEvent("onAfterUpdate", (id, action, tid, response)=>{
      //console.log("Update",id, action, tid, response)
      this.ganttService.ganttConfig.last_updated_on = "Last updated on: "+moment().format("DD-MMM-YYYY hh:mm A")
      
    })

    
  }


  initGantt() {



    gantt.config.sort = true;

    gantt.config.date_format = '%Y-%M-d';

    gantt.plugins({
      auto_scheduling: true,
      tooltip: true,
      marker: true,
      critical_path: true,
      click_drag: true,
      fullscreen: true,
      keyboard_navigation: true,
      multiselect: true,
      export_api: true,
      quick_info: false,
      drag_timeline: true,
      undo: true,
      grouping: true,

    }) 

    gantt.config.readonly = this.ganttService.ganttConfig.readOnly;
    // gantt.config.readonly =true;


    
    gantt.config.date_grid = "%d-%M-%Y";




   



    

    gantt.config.static_background = true;

    
    gantt.config.inline_editors_date_processing = this.ganttService.ganttConfig.inline_editor_date_processing;

    gantt.locale.labels['section_progress'] = "Progress";

    let formatter = gantt.ext.formatters.durationFormatter({
      enter: "day",
      store: "day",
      format: "auto"
    });

    




    let linksFormatter = gantt.ext.formatters.linkFormatter({ durationFormatter: formatter });


    let editors = {
      text: { type: "text", map_to: "text" },
      start_date: { type: "date", map_to: "start_date" },
      end_date: { type: "date", map_to: "end_date" },
      duration: { type: "number", map_to: "duration",  formatter: formatter,   },
      status: { type: "select", map_to: "status_id", options: gantt.serverList("status_action_list") },
      predecessors: { type: "predecessor", map_to: "auto", formatter: linksFormatter },
      estimated_hours: { type: "number", map_to: "estimated_hours" },
      successor: { type: "predecessor", map_to: "auto", formatter: linksFormatter },
      fricew: { type: "select", map_to: "fricew", options: gantt.serverList("fricew_list") },
    };
    
    //do not change index of this array. If need to change. Please update in exportPDF() and exportPNG()
    this.columnConfig = [
      {
        "id": "edit_button",
        "name": 'edit-buttons',
        "is_download":false,
        "configLabel": '',
        "min_width": 33,
        "label": '',
        "align": 'center',
        "template": (task) => {
          if(!task.text.includes("+ Add")){
            return '<span class="material-icons edit-button" gantt-click-action="edit"></span>'
          }
        },
      },
      
      {
        "id": "wbs",
        "name": "wbs",
        "label": "WBS",
        "is_download":true,
        "width": 60,
        "min-width": 30,
        "resize": true,
        "align": 'left',
        "template": (task) => {
          if(!task.text.includes("+ Add")){
            let wbs = gantt.getWBSCode(task)
            task.wbs = wbs
            return wbs
          }
        },
        "hide": false
      },
      {
        "id": "type",
        "name": "type",
        "label": this.getColumnName("type", "Type"),
        "width": 60,
        "min-width": 30,
        "resize": true,
        "align": 'center',
        "template": (task) => {
          if(!task.text.includes("+ Add")){ 
            var list = gantt.serverList("gantt_list");
            let mat_icon_data = _.where(list, { key: task.type_name })
            mat_icon_data = mat_icon_data.length > 0 ? '<span class="material-icons" style="color:' + mat_icon_data[0]['gantt_mat_icon_color'] + '; font-size: 19px;padding-top: 7px;">' + mat_icon_data[0]['gantt_mat_icon'] + '</span>' : ''
            return '<div>' + mat_icon_data + '</div>'
          }
        },
        "hide": false
      },
      {
        "id": "type_download",
        "name": "type_display_name",
        "label": this.getColumnName("type", "Type"),
        "width": 60,
        "min-width": 30,
        "is_download": true,
        "resize": true,
        "align": 'center',
        "template": (task) => {
          if(!task.text.includes("+ Add")){ 
            var list = gantt.serverList("gantt_list");
            let mat_icon_data = _.where(list, { key: task.type_name })
            console.log("List Name", mat_icon_data)
            mat_icon_data = mat_icon_data.length > 0 ? mat_icon_data[0]['label'] :""
            return  mat_icon_data
          }
        },
        "hide": true
      },
      {
        "id": "description",
        "name": "text",
        "label": this.getColumnName("description", "Description"),
        "is_download":true,
        "tree": true,
        "width": 400,
        "min_width": 250,
        "resize": true,
        "editor": editors.text,
        "hide": false,
        "align": 'left',

      },

      {
        "id": "planned_start_date",
        "name": "start_date",
        "label": this.getColumnName("planned_start_date", "Start"),
        "is_download":true,
        "width": 140,
        "min_width": 140,
        "align": "center",
        "resize": true,
        "editor": editors.start_date,
        "hide": false,

      },
      {
        "id": "planned_end_date",
        "name": "end_date",
        "label": this.getColumnName("planned_end_date", "Finish"),
        "is_download":true,
        "width": 140,
        "min_width": 140,
        "align": "center",
        "resize": true,
        "editor": editors.end_date,
        "hide": true
      },
      {
        "id": "planned_duration",
        "name": "duration",
        "label": this.getColumnName("duration", "Duration"),
        "is_download":true,
        "width": 150,
        "min_width": 100,
        "align": "center",
        "resize": true,
        "editor": editors.duration,
        "template":(task)=>{
        
              return task.duration+1;
          
        },
        "hide": false
      },
      
      {
        "id": "calendar_days",
        "name": "calendar_days",
        "label": this.getColumnName("calendar_days", "Calendar Days"),
        "is_download":true,
        "width": 150,
        "min_width": 100,
        "align": "center",
        "resize": true,
        "template":(task)=>{
          let moment_start_date: any = moment(task.start_date) 
          let moment_end_date: any = moment(task.end_date).add(1, "days")

          

          let days = moment_end_date.diff(moment_start_date, 'days');
          return days

        },
        "hide": true
      },
      {
        "id": "predecessors",
        "name": "predecessors",
        "is_download":false,
        "label": this.getColumnName("predecessors", "Predecessors"),
        "width": 150,
        "min_width": 120,
        "align": "left",
        "resize": true,
        "editor": editors.predecessors,
        "template": (task) => {
          var links = task.$target;
          var labels = [];
          for (var i = 0; i < links.length; i++) {
            var link = gantt.getLink(links[i]);
            labels.push(linksFormatter.format(link));
          }
          return labels.join(", ")
        },
        "hide": true
      },
     
      {
        "id": "status",
        "name": "status_id",
        "label": this.getColumnName("status", "Status"),
        "is_download":true,
        "width": 120,
        "min_width": 120,
        "align": "center",
        "resize": true,
        "editor": editors.status,
        "template": this.statusLabel,
        "hide": false
      },
      {
        "id": "peopleList",
        "name": "owner_list",
        "label": this.getColumnName("owner", "Owner"),
        "is_download":true,
        "width": 120,
        "min_width": 120,
        "align": "center",
        "resize": true,
        "hide": false,
        "template": (task) =>{
          const multipleOwnerConfig = _.where(this.formConfig, { type: "activity", field_name: "multi-owner", is_active: true });
          
          if(this.formConfig && this.formConfig.length > 0){
            this.retrieveMessages = _.where(this.formConfig, { type: "gantt", field_name: "messages", is_active: true });
          }
          if(multipleOwnerConfig && multipleOwnerConfig.length > 0){
           
            let owner = task.assigned_to_list;
            
            if(owner && owner.length == 1){
              let aid = _.where(gantt.serverList("employee_list"),{associate_id: owner[0]})
              
              if(aid && aid.length>0)
              {
                  return aid[0]['name']
              }
            }
            else if(owner && owner.length > 1){
              return (owner.length) + ' Owners'
            }
          }
          else{
         
            let aid = _.filter(gantt.serverList("employee_list"),(res)=>{
              if(res.associate_id+'' == task.assigned_to_list+'')
              {
                return res;
              }
            })
            if(aid && aid.length>0)
            {
                return aid[0]['name']
            }
          } 
        }
      },
      {
        "id": "actual_start_date",
        "name": "actual_start_date",
        "label": this.getColumnName("actual_start_date", "Actual Start"),
        "is_download":true,
        "width": 120,
        "min_width": 120,
        "align": "center",
        "resize": true,
        "hide": false,
        "template":(task)=>{
          return task.actual_start_date!="" && task.actual_start_date!=null && task.actual_start_date!=" " ? moment(task.actual_start_date).format("DD-MMM-YYYY") :""
        }
      },
      {
        "id": "actual_end_date",
        "name": "actual_end_date",
        "label": this.getColumnName("actual_start_date", "Actual Finish"),
        "is_download":true,
        "width": 120,
        "min_width": 120,
        "align": "center",
        "resize": true,
        "hide": false,
        "template":(task)=>{
          return task.actual_end_date!="" && task.actual_end_date!=null && task.actual_end_date!=" " ? moment(task.actual_end_date).format("DD-MMM-YYYY") :""
        }
      },
      {
        "id": "actual_duration",
        "name": "actual_duration",
        "label": this.getColumnName("actual_duration", "Actual Duration"),
        "is_download":true,
        "width": 150,
        "min_width": 100,
        "align": "center",
        "resize": true,
        "hide": true,

      },
      {
        "id": "estimated_hours",
        "name": "estimated_hours",
        "label": this.getColumnName("estimated_hours", "Estimated Hours"),
        "is_download":true,
        "width": 150,
        "min_width": 100,
        "align": "center",
        "resize": true,
        "hide": true,
        "editor": editors.estimated_hours
      },

      {
        "id": "billable_act",
        "name": "billable_act",
        "label": this.getColumnName("billable_act", "Billable Activity"),
        "is_download":true,
        "width": 150,
        "min_width": 100,
        "align": "center",
        "resize": true,
        "template": this.billableLabel,
        "hide": true,

      },
      

      {
        "id": "completion_percentage",
        "name": "completion_percentage",
        "is_download":true,
        "label": this.getColumnName("completion_percentage", "Completion %"),
        "width": 150,
        "min_width": 100,
        "align": "center",
        "resize": true,
        "hide": true,
        template: (task)=> {    
          
          return task.completion_percentage ? Math.round(task.completion_percentage) + "%": "0%";
        }

      },
      {
        "id": "weighted_percentage",
        "name": "weighted_percentage",
        "is_download":true,
        "label": this.getColumnName("weighted_percentage", "Weighted %"),
        "width": 150,
        "min_width": 100,
        "align": "center",
        "resize": true,
        "hide": true,

      },
      {
        "id": "sheet_number",
        "name": "sheet_number",
        "is_download":true,
        "label": this.getColumnName("sheet_number", "Sheet Number"),
        "width": 150,
        "min_width": 100,
        "align": "center",
        "resize": true,
        "hide": true,

      },
      {
        "id": "software_type",
        "name": "software_type",
        "is_download":true,
        "label": this.getColumnName("software_type", "Software Type"),
        "width": 150,
        "min_width": 100,
        "align": "center",
        "resize": true,
        "hide": true,

      },
      {
        "id": "fricew",
        "name": "fricew",
        "label":  this.getColumnName("fricew", "FRICEW"),
        "is_download":true,
        "width": 150,
        "min_width": 100,
        "align": "center",
        "resize": true,
        "editor": editors.fricew,
        "template": this.fricewLabel,
        "hide": true,

      },

      {
        "id": "priority",
        "name": "priority",
        "label": this.getColumnName("priority", "Priority"),
        "is_download":true,
        "width": 150,
        "min_width": 100,
        "align": "center",
        "resize": true,
        "template": this.priorityLabel,
        "hide": true,

      },
      
      {
        "id": "etc_hours",
        "name": "etc_hours",
        "is_download":true,
        "label":  this.getColumnName("etc_hours", "ETC Hours"),
        "width": 150,
        "min_width": 100,
        "align": "center",
        "resize": true,
        "hide": true,

      },

      {
        "id": "add_button",
        "name": 'add-buttons',
        "is_download":false,
        "configLabel": '',
        "min_width": 33,
        "label": '',
        "align": 'center',
        "hide": this.ganttService.ganttConfig.readOnly,
        "template": (task) => {
          if(this.deleteCheck(task.id) && task.gantt_type_id==5){

          }
          else if(!task.text.includes("+ Add")){
            return '<span class="material-icons add-button" gantt-click-action="add"></span>'
          } 
        },
      },

    ]
    if (!this.multipleOwner) {
      editors['owner'] = { type: "select", map_to: "assigned_to_list", options: gantt.serverList("employee_list") };
      
      const peopleListColumn = _.find(this.columnConfig, { id: 'peopleList' });
      if (peopleListColumn) {
          peopleListColumn.editor = editors['owner'];
      }
      
    }
    if(!this.disableBillable){
      editors['billable'] = { type: "select", map_to: "billable_act", options: gantt.serverList("billable_list") }
      const billableColumn = _.find(this.columnConfig, { id: 'billable_act' });
      if (billableColumn) {
        billableColumn.editor = editors['billable'];
      }
    }
    let hideColumn = _.where(this.ganttService.ganttConfig.columnList, { is_applicable_gantt: true, isVisible: true, isActive: true })

    let itemList = _.pluck(hideColumn, "item")

    //console.log(itemList)

    for (let column of this.columnConfig) {
      if (column['id'] != 'add_button' && column['id']!='edit_button') {
        if (_.contains(itemList, column['id'])) {
          column['hide'] = false
        }
        else {
          column['hide'] = true
        }
      }
    }

    //console.log(this.columnConfig)
    gantt.config.columns = this.columnConfig;



    //Performance 
    gantt.config.smart_rendering = true;

    gantt.locale.labels["section_progress"] = "Progress";

    gantt.skin = 'material';

    






    gantt.config.container_resize_method = "timeout";
    gantt.config.container_resize_timeout = 300;
    //autoscheduling
    gantt.config.auto_scheduling = true;
    gantt.config.auto_scheduling_strict = false;


    //Open Tree Initially
    gantt.config.open_tree_initially = true;

    //enables dragging project type
    gantt.config.drag_project = true;

    gantt.config.show_errors = false;

    gantt.config.row_height=30;

    gantt.config.grid_elastic_columns = true;
    gantt.config.grid_elastic_columns = "min_width";
    gantt.config.reorder_grid_columns = true;


    //forced touch mode
    gantt.config.touch = false;


    // reordering tasks within the same nesting level
    gantt.config.order_branch = "marker";

    // reordering tasks within the whole gantt
    gantt.config.order_branch_free = true;

    gantt.config.columns[0].sort = false;
    gantt.config.columns[1].sort = false;
    gantt.config.columns[2].sort = false;
    //cell navigation
    gantt.config.keyboard_navigation_cells = true;

  //console.log(this.ganttService.ganttConfig.item_start_date, this.ganttService.ganttConfig.item_end_date)
    
    const dateFix_config = _.where(this.formConfig, { type: "gantt", field_name: "dateFix", is_active: true});
    
    if(dateFix_config.length > 0){
        this.dateFix = dateFix_config[0].default;
    }


    const fixedDuration_config = _.where(this.formConfig,{type:"gantt", field_name:"fixed-duration", is_active: true})

    if(fixedDuration_config.length >0)
    {
      this.fixedDuration = fixedDuration_config[0] ?  fixedDuration_config[0].default : false
    }


    if(this.fixedDuration)
    {
      console.log("Dates", )
      gantt.config.project_start = this.convertUTCDate(this.ganttService.ganttConfig.item_start_date);
      gantt.config.project_end = this.convertUTCDate(this.ganttService.ganttConfig.item_end_date);
      gantt.config.start_date = this.convertUTCDate(this.ganttService.ganttConfig.item_start_date);
      gantt.config.end_date = this.convertUTCDate(this.ganttService.ganttConfig.item_end_date);
    }


    gantt.config.scales = [
      {unit: "month", step: 1, format: "%F, %Y"},
      {unit: "week", step: 1, format: function (date) {
          return "Week #" + gantt.date.getWeek(date);
      }},
      {unit: "day", step: 1, format: "%D", css:(date)=> {
      if(!gantt.isWorkTime({ date: date, unit: "day"})){
              return "weekend"
          }
      }}
  ];

    gantt.config.scale_height = 70;

    
    gantt.ext.zoom.init(this.zoomConfigs);
    gantt.ext.zoom.setLevel(this.sliderName);
    const inlinedrag_config = _.where(this.formConfig,{type:"gantt", field_name:"inlinedrag_config", is_active: true})

    if(inlinedrag_config.length >0)
    {
      gantt.config.drag_move = false;
      gantt.config.drag_resize = false;
      gantt.config.drag_progress = false;
      
      gantt.attachEvent("onBeforeTaskDrag", function(id, mode, e) {
        return false;
      });
    }



 
    //calculate holiday calendar and workschedule
    gantt.config.work_time = true;
    gantt.config.duration_unit= "day";


      //workschedule configuration

      let week_schedule = [{
        "gantt_schedule":0,
        "gantt_schedule_name":"Sunday",
        "schedule":6
      },
      {
        "gantt_schedule":1,
        "gantt_schedule_name":"Monday",
        "schedule":1
      },
      {
        "gantt_schedule":2,
        "gantt_schedule_name":"Tuesday",
        "schedule":2
      },
      {
        "gantt_schedule":3,
        "gantt_schedule_name":"Wednesday",
        "schedule":3
      },
      {
        "gantt_schedule":4,
        "gantt_schedule_name":"Thursday",
        "schedule":4
      },
      {
        "gantt_schedule":5,
        "gantt_schedule_name":"Friday",
        "schedule":5
      },
      {
        "gantt_schedule":6,
        "gantt_schedule_name":"Saturday",
        "schedule":7
      }]




      for(let week of week_schedule)
      {
          if(_.contains(this.ganttService.ganttConfig.work_schedule, week['schedule']))
          {
            gantt.setWorkTime({ day:week['gantt_schedule'], hours:["8:00-17:00"] });
          }
          else
          {
            gantt.setWorkTime({ day:week['gantt_schedule'], hours:false });
          }
      }

  




      
    gantt.config.drag_timeline = {
      ignore: ".gantt_task_line, .gantt_task_link",
      useKey: false
    };


  
   

    gantt.templates.scale_cell_class = (date) => {
      if(this.sliderName=="day" || this.sliderName == "week")
      {
        if (!gantt.isWorkTime(date)) {
          return "weekend";
        }
      }
      
    };
    gantt.templates.timeline_cell_class = (item, date) => {
      if(this.sliderName=="day" || this.sliderName == "week")
      {
        
        if (!gantt.isWorkTime(date)) {
          //console.log(this.sliderName,date)
          return "weekend";
        }
      
      }
      
     
    };

    gantt.templates.grid_header_class =  (columnName, column)=> {
      return 'header';
    };


    gantt.templates.task_row_class =  (task, date)=> {
      return 'task-row';
    };

    gantt.templates.task_class =(start, end, task)=> {

        let className = ""
        if(this.criticalPathEnabled)
        {
          if(gantt.isCriticalTask(gantt.getTask(task.id)))
          {
            className = 'gantt_critical_task'
          }
        }
        
        if(task.type_name =="project")
        {
          className =  'task-project'
        }
        else if(task.type_name =="activity")
        {
          className = 'task-activity'
        }
        else if(task.type_name =="wave")
        {
          className = 'task-wave'
        }
        else if(task.type_name =="phase")
        {
          className =  'task-phase'
        }
        else 
        {
          if (task.status == 1) {
            className = 'task-draft';
          } 
          else if (task.status == 4) {
            className = 'task-execution';
          } 
          else if (task.status == 5) {
            className = 'task-completed';
          } 
          else if (task.status == 6) {
            className = '';
          } 
          else {
            className = '';
          }
        }

        let updateProgress = _.findWhere(this.formConfig,{type:"gantt", field_name:"update-progress-percentage", is_active: true});

        if(updateProgress)
        {
          const children = gantt.getChildren(task.id);

          let count = 0;
          children.forEach(childId => {
              const child = gantt.getTask(childId);
      
              if(!child.text.includes("+ Add"))
              {
                count++;
              }
          });

          if(count !=0)
            className += " parent-task"
        }

        return className;
        
        
    };


    




    gantt.templates.link_class = (link) => {
      var types = gantt.config.links;
      if(this.criticalPathEnabled)
      {
          if(gantt.isCriticalLink(link))
          {
            return "criticalPathLink"
          }
      }
      else
      {
        if(link.type ==0)
        {
            return "finish_to_start";
        }
        else if(link.type ==1)
        {
            return "start_to_start";
        }
        else if(link.type ==2)
        {
          return "finish_to_finish";
        }
        else if(link.type ==3)
        {
          return "start_to_finish";
        }
            
         
      }

    };




    gantt.templates.progress_text = (start, end, task) => {
      return "<span style='text-align:right;'>" + Math.round(task.progress * 100) + "% </span>";
    }


    gantt.templates.tooltip_text = (start,end,task)=>{
      return "<b>Task:</b> "+task.text+"<br/><b>Start:</b> " + moment(task.start_date).format("DD-MMM-YYYY")+"<br/><b>Finish:</b> " +moment(task.end_date).format("DD-MMM-YYYY");
    };

    

    gantt.config.layout = this.fullLayout;

    gantt.config.show_task_cells = false;

    gantt.validate_task =  (id, task) =>{
      if (task.start_date >= task.end_date) {
          
          return false;
      }
      return true;
    };


    


    // gantt.templates.rightside_text = function (start, end, task) {
    //   return "ID: #" + task.id;
    // };

    // gantt.templates.leftside_text = function (start, end, task) {
    //   return task.duration + " days";
    // };


    gantt.templates.grid_row_class = (start, end, task) => {
      if(task.text && (task.text.includes("+ Add") || task._avoid))
      {
        return "placeholder_task"
      }
      if (task.highlight) 
      {
        return "highlighted_task";
      }
      return "";
    };



    // gantt.templates.date_grid = (date, task)=> {
    //   if (!date) {
    //     return "";
    //   }
    //   return gantt.templates.grid_date_format(date);
    // };





    let onBeforeTaskDisplay = gantt.attachEvent("onBeforeTaskDisplay", (id, task)=>{
      
        //apply Filters here
        if(this.ganttService.ganttConfig.filterApplied){
          //let currTask = gantt.getTask(id);
 
          task.$open = true;
          

          let groupByFilter = _.groupBy(this.ganttService.ganttConfig.filterTags,"type")
          let statusFilter=groupByFilter['status']? false:true;
          let typeFilter=groupByFilter['type']? false: true;
          let ownerFilter=groupByFilter['assigned_to_list']? false: true;
   
          

          for(let items of this.ganttService.ganttConfig.filterTags)
          {
          
              if(items['type']=="status")
              {
                if(task.status_id)
                {
                  if(_.contains(_.pluck(groupByFilter[items['type']],"id"),parseInt(task.status_id)))
                    statusFilter=true;
                  else
                    statusFilter=false;
                }
              }
              if(items['type']=="ganttType")
              {
                if(task.type_name)
                {
                  if(_.contains(_.pluck(groupByFilter[items['type']],"id"),task.type_name))
                    typeFilter=true;
                  else
                    typeFilter=false;
                }
              }
              if(items['type']=="owner")
              {
                let owner_list = _.filter(task.assigned_to_list,(res)=>{
                  if(_.contains(_.pluck(groupByFilter[items['type']],"id"), res))
                  {
                      return true;
                  }
                })

                if(owner_list.length>0)
                  ownerFilter=true; 
                else
                  ownerFilter=false;
              }
          }
          if(statusFilter && typeFilter && ownerFilter)
          {
            
            return true;
          }

          else
            return false;
        }
        else{
          return true;
        }
        
    });

    this.attachedEvents.push(onBeforeTaskDisplay);




    let ganttReadyEvent = gantt.attachEvent("onGanttReady", () => {
      var tooltips = gantt.ext.tooltips;
      tooltips.tooltip.setViewport(gantt.$task_data);

      tooltips.tooltipFor({
        selector: ".gantt_task_link",
        html: function (event, domElement) {
            let linkId = domElement.getAttribute(gantt.config.link_attribute);
            if (linkId) {
                let link = gantt.getLink(linkId);
                let source = gantt.getTask(link.source);
                let target = gantt.getTask(link.target);
                return `From: ${source.text} <br> To: ${target.text}`
            }
        }
      }); 

    });

    this.attachedEvents.push(ganttReadyEvent);



    let onLinkClickEvent = gantt.attachEvent("onLinkClick", (id) => {
      var link = gantt.getLink(id),
        src = gantt.getTask(link.source),
        trg = gantt.getTask(link.target),
        types = gantt.config.links;

      var first = "", second = "";
      switch (link.type) {
        case types.finish_to_start:
          first = "finish";
          second = "start";
          break;
        case types.start_to_start:
          first = "start";
          second = "start";
          break;
        case types.finish_to_finish:
          first = "finish";
          second = "finish";
          break;
      }

      gantt.message("Must " + first + " <b>" + src.text + "</b> to " + second + " <b>" + trg.text + "</b>");
    });

    this.attachedEvents.push(onLinkClickEvent);




    let onBeforeLightBoxEvent = gantt.attachEvent("onBeforeLightbox", function (id) {
      return false;
    });

    this.attachedEvents.push(onBeforeLightBoxEvent);


    let onTaskClickEvent = gantt.attachEvent('onTaskClick', async (id, e) => {

 
    //console.log("inside on task click")
      this.ganttService.ganttConfig.onTaskClickGanttType = gantt.getTask(id).text.includes("+ Add") ? "" :gantt.getTask(id).type_name
      this.ganttService.ganttConfig.onTaskClickGanttData = gantt.getTask(id)
      let button = e.target.closest('[gantt-click-action]');
   
      if (button) {

        let action = button.getAttribute('gantt-click-action');
        //console.log(button)
        if (action == "add") {
          this.openPlanningLightBox(id, "create")
        }
        if (action == "edit") {
          this.openPlanningLightBox(id, "edit")
        }
      }



    })

    this.attachedEvents.push(onTaskClickEvent);

    let inlineEditors = gantt.ext.inlineEditors;
 
    inlineEditors.attachEvent("onBeforeEditStart", (state)=>{
      // -> {id: itemId, columnName: columnName};
      let type = gantt.getTask(state.id).type_name;

      
      if(state.columnName == 'owner_list'){
        if(type == 'activity')
          return true;
          
        else
          return false
      }
      else if(state.columnName =='duration'){
        return false;
      }
      else if(this.billableFlagActivityEdit(state.id)){
        return false;
      }
      return true;
    });

    // this.attachedEvents.push(onEditTaskEvent);

    let oldTask: any;
    // gantt.ext.inlineEditors.attachEvent("onSave", (state) => {
    //   var col = state.columnName;
    //   if (gantt.autoSchedule && (col == "start_date" || col == "end_date" || col == "duration")) {
    //     gantt.autoSchedule();
    //   }
    // });

    let onBeforeTaskAutoScheduleEvent = gantt.attachEvent("onBeforeTaskAutoSchedule", (task, start, link, predecessor) => {

  
      return true;
    });

    this.attachedEvents.push(onBeforeTaskAutoScheduleEvent);



    let onBeforeTaskUpdateEvent = gantt.attachEvent("onBeforeTaskUpdate", (id, item) => {
      //console.log("onBeforeTaskUpdate", id)
      let task = gantt.getTask(id)
      //console.log(item, task, id, item == task)

      if(task.end_date<task.start_date)
      {
        return false;
      }

      this.compareGanttLineItems(task, item)

      if (task._avoid) {

        let child = gantt.getChildren(task.parent)
        //console.log(task.parent, child)

        let record = []
        for (let c of child) {
          let childTask = gantt.getTask(c)
          //console.log(c, id, c == id)
          if (c == id && !task.text.includes("+ Add")) {
            task.wbs_code = gantt.getWBSCode(id)
            task.has_child = gantt.hasChild(id)
            task.status_id = 7
            task._avoid = false;
          }

          record.push(childTask._avoid)
        }

        //console.log(record, _.contains(record, true))

        if (!_.contains(record, true) && task.type_name != "project" && !task.text.includes("+ Add")) {
          gantt.performAction("insertBelowSame");
        }
        return true;
      }
      else
      {
        //completion_percentage
        let currTask = gantt.getTask(task.id)
        let completion_percentage = (task.progress * 100) + ""
        let parts = completion_percentage.split('.');
        currTask.completion_percentage = parts[0]




         //Start Date Validation
        if(this.fixedDuration)
        {
          if (this.isTaskWithinProject(task)) {
            
            return true; 
          }
          else
          {
            this.validateTaskDates(task);
            return true;
          }
        }
      }

    });

    this.attachedEvents.push(onBeforeTaskUpdateEvent);

    let onTaskDrag = gantt.attachEvent("onTaskDrag", (id, mode, task, original, e) =>{

      if(this.fixedDuration)
      {
        if (this.isTaskWithinProject(task)) {
    
          return true; 
        }
        else
        {
          this.validateTaskDates(task);
          return true
        }
         

      }
	  });
    this.attachedEvents.push(onTaskDrag);


    let onTaskAdd = gantt.attachEvent("onTaskAdd", (id, task) =>{
      if(this.fixedDuration)
      {
        if (this.isTaskWithinProject(task)) {
          
          return true; 
        }
        else
        {
          this.validateTaskDates(task);
          return true
        } 
      }
    });

    this.attachedEvents.push(onTaskAdd);

    let onTaskUpdate = gantt.attachEvent("onTaskUpdate", (id, task) =>{

      if(this.fixedDuration)
      {
        if (this.isTaskWithinProject(task)) {
          
          return true; 
        }
        else
        {
          this.validateTaskDates(task);
          return true;
        }
      }
    });

    this.attachedEvents.push(onTaskUpdate);


  




    let onAfterTaskAutoSchedule = gantt.attachEvent("onAfterTaskAutoSchedule", (taskId, task) =>{

      console.log("Auto Scheduling ", task)


      if (!this.isTaskWithinProject(task)) 
      {
        if(gantt.isTaskExists(taskId))
        {   
          this.validateTaskDates(task)
          gantt.updateTask(taskId);  // Revert task changes
        }
      }
    });

    this.attachedEvents.push(onAfterTaskAutoSchedule)


    let onBeforeTaskAutoSchedule = gantt.attachEvent("onBeforeTaskAutoSchedule", (object, start,link,predecessor) => {
        let oldTask = object;
        let newTask = gantt.getTask(oldTask.id)

        this.compareGanttLineItems(oldTask, newTask)
    });

    this.attachedEvents.push(onBeforeTaskAutoSchedule);

    let onAfterTaskUpdateEvent = gantt.attachEvent("onAfterTaskUpdate", async(id, item) => {

        // Update parent's start_date and end_date based on children

        gantt.eachParent(parent=>{
          parent.type = gantt.config.types.project
          gantt.updateTask(parent.id)
        }, id);
      
        let updateProgress = _.findWhere(this.formConfig,{type:"gantt", field_name:"update-progress-percentage", is_active: true});

        if(updateProgress)
        {
          gantt.batchUpdate(() => {
            this.propagateProgressToParents(id); // Trigger roll-up updates
          });
        }

              
        gantt.refreshData();
        gantt.render();
    });


   



    this.attachedEvents.push(onAfterTaskUpdateEvent);

    let onAfterTaskDeleteEvent = gantt.attachEvent("onAfterTaskDelete", (id,item)=>{
      //console.log("On After Task Delete Event")

      this.updateParentTask(id)
      
    });

    this.attachedEvents.push(onAfterTaskDeleteEvent);

    let onBeforeTaskAddEvent = gantt.attachEvent("onBeforeTaskAdd", async(id, item) => {
        item.wbs_code = item.$wbs;
        //console.log('hello', item, id);
    });

    this.attachedEvents.push(onBeforeTaskAddEvent);

    let onBeforeTaskMove = gantt.attachEvent("onBeforeTaskMove", (task_id, parent, t_index)=>{

      var top_parent = gantt.getTaskByWBSCode("1")
      let currTask = gantt.getTask(task_id)

      if(top_parent!=null && top_parent)
      {
        //console.log("drag", parent !=top_parent.id, parent, top_parent.id, currTask)
        
        
        if ( parent !=gantt.config.root_id) {
          // console.log("inside drag")
          if(gantt.getTask(parent).text.includes("+ Add"))
          {
            return false; 
          }
          else if (this.deleteCheck(gantt.getTask(parent).id) && gantt.getTask(parent).gantt_type_id == 5) {
            const timesheet_activity_msg = this.retrieveMessages.length > 0
              ? this.retrieveMessages[0].errors.timesheet_activity_msg
                ? this.retrieveMessages[0].errors.timesheet_activity_msg
                : 'Function Not Possible'
              : 'Function Not Possible';

            // Show the warning message
            this.toasterService.showWarning(timesheet_activity_msg, 10000);

            // Exit the function without adding a new task
            return false;
          }
          else if(this.checkGanttStructure(currTask.type_name, gantt.getTask(parent).type_name))
          {
            return true;
          }
          else 
          {
            return false;
          }
         
          
        }
        
       
        
        return false;
      }
      
        

      return false;
     
      
     
    });

    this.attachedEvents.push(onBeforeTaskMove);


 


    let onLinkAddEvent = gantt.attachEvent("onBeforeLinkAdd", (id, link)=>{
      
      if(gantt.isChildOf(link.source, link.target)){
        if(link.type =="0")
          return false;
        //console.log(link.type)
      }
      else if(gantt.isChildOf(link.target, link.source))
      {
        if(link.type =="0")
          return false;
        //console.log(link.type)
      }
      else
      {
          if(link.type =="0")
          {
            link.lag = link.lag ? link.lag == 0 ? 1 : link.lag : 1
          }
          
          return true;
      }


      
    });

    this.attachedEvents.push(onLinkAddEvent);














    gantt.ext.inlineEditors.attachEvent("onBeforeSave", async(state)=>{
      //console.log("ONBEFORESAVE", state)
      let columnName = state.columnName
      let id = state.id
      let oldValue = state.oldValue
      let newValue = state.newValue
      

      let currTask = gantt.getTask(id);
      let old_task = gantt.getTask(state.id);

      let oldTask = gantt.copy(old_task);

      let oldChange = {}
      let newChange = {}
      if(columnName != "text")
      {
        oldChange['text'] = currTask.text;
        newChange['text'] = currTask.text;
        

      }
      
      oldChange[columnName] = oldValue
      newChange[columnName]=  newValue

      oldChange['id'] = currTask.id;
      newChange['id']=  currTask.id;
      oldChange['parent'] = currTask.parent;
      newChange['parent']=  currTask.parent;
          
      
      oldChange['type']=currTask.type_name;
      newChange['type']=currTask.type_name;
      oldChange['assigned_to_list'] = currTask.assigned_to_list;
      newChange['assigned_to_list'] = currTask.assigned_to_list;
  
      oldValue=oldValue+""
      if(oldValue)
      {
        if((oldValue).includes("+ Add"))
        {
            oldChange = null
        }
      }

      
      

      newValue!=null ? this.compareGanttLineItems(oldChange, newChange) :""
      //Status Validation
      if(columnName == "status_id")
      {
          if(oldValue==7)
          { 
              //Open to Execution
              if(newValue==4)
              {
                currTask.actual_start_date = this.convertUTCDate(moment().format())
                currTask.status_id = 5
              }
              //Open to Completed
              else if(newValue == 5)
              {
                currTask.actual_start_date = this.convertUTCDate(moment().format())
                currTask.actual_end_date = this.convertUTCDate(moment().format())
                currTask.actual_duration = gantt.calculateDuration({
                  start_date: currTask.actual_start_date, 
                  end_date: currTask.actual_end_date
        
                });
                currTask.completed_on = moment().format()
                currTask.status_id = 5
              }
          }
          else if(oldValue==4)
          {
              //Execution to Open - Allow Change
              if(newValue == 7)
              {
                  currTask.actual_start_date =""
                  currTask.actual_end_date =""
                  currTask.actual_duration = ""
                  currTask.completed_on =""
                  currTask.status_id = 7
              }
              //Execution to Completed 
              else if(newValue == 5)
              {
                  currTask.actual_end_date = this.convertUTCDate(moment().format())
                  currTask.actual_duration = gantt.calculateDuration({
                    start_date: currTask.actual_start_date, 
                    end_date: currTask.actual_end_date
          
                  });
                  currTask.completed_on = moment().format()
                  currTask.status_id = 5
              }
          }
          else if(oldValue==5)
          {
              if(newValue == 7)
              {
                  currTask.actual_start_date =""
                  currTask.actual_end_date =""
                  currTask.actual_duration = ""
                  currTask.status_id = 7
              }
              else if(newValue == 4)
              {
                  currTask.actual_end_date =""
                  currTask.actual_duration = ""
                  currTask.status_id = 4
              }
          }
      }

      if(columnName =="start_date"){

        //validate start date of the task
        if(this.ganttService.ganttConfig.project_status_id == 4)
        {
          var taskStart = currTask['start_date'];
          var taskEnd = currTask['end_date'];
          var scaleStart = gantt.config.start_date;
        

          // check if the task is out of the range
          if(scaleStart > taskEnd){
            //console.log("End out range")
                currTask['start_date'] = scaleStart;
            
          }
          if(scaleStart > taskStart){
            //console.log("Start out range")
                currTask['start_date'] = scaleStart;
        
          }
        }

        gantt.refreshData();
        gantt.render();
      }

      if(columnName=="end_date"){

      }

      
      return true;
    });


    
    gantt.addTaskLayer(function addSlack(task) {
			if (!gantt.config.show_slack) {
				return null;
			}

			var slack = gantt.getFreeSlack(task);

			if (!slack) {
				return null;
			}

			var state = gantt.getState().drag_mode;

			if (state == 'resize' || state == 'move') {
				return null;
			}

			var slackStart = this.convertUTCDate(task.end_date);
			var slackEnd = gantt.calculateEndDate(slackStart, slack);
			var sizes = gantt.getTaskPosition(task, slackStart, slackEnd);
			var el = document.createElement('div');

			el.className = 'slack';
			el.style.left = sizes.left + 'px';
			el.style.top = sizes.top + 2 + 'px';
			el.style.width = sizes.width + 'px';
			el.style.height = sizes.height + 'px';

			return el;
		});
   




    gantt.addShortcut("delete", async (e)=> {
      gantt.performAction("delete");
    },"taskRow");


    gantt.performAction = (actionName) => {
      //console.log(actionName)
      var action = this.actions[actionName];
      if (!action)
        return;

      if (this.singularAction[actionName]) {
        action();
        return;
      }

      gantt.batchUpdate(() => {

        // need to preserve order of items on indent/outdent,
        // remember order before changing anything:
        var indexes = {};
        var siblings = {};
        gantt.eachSelectedTask((task_id) => {
          gantt.ext.undo.saveState(task_id, "task");
          indexes[task_id] = gantt.getTaskIndex(task_id);
          siblings[task_id] = {
            first: null
          };

          var currentId = task_id;
          while (gantt.isTaskExists(gantt.getPrevSibling(currentId)) && gantt.isSelectedTask(gantt.getPrevSibling(currentId))) {
            currentId = gantt.getPrevSibling(currentId);
          }
          siblings[task_id].first = currentId;
        });

        var updated = {};
        gantt.eachSelectedTask((task_id) => {

          if (this.cascadeAction[actionName]) {
            if (!updated[gantt.getParent(task_id)]) {
              var updated_id = action(task_id, indexes, siblings);

              updated[updated_id] = true;
            } else {
              updated[task_id] = true;
            }
          } else {
            action(task_id, indexes);
          }
        });
      });
    };










    return;
  }


  shiftTask(task_id, direction) {
    var task = gantt.getTask(task_id);
    task.start_date = gantt.date.add(task.start_date, direction, "day");
    task.end_date = gantt.calculateEndDate(task.start_date, task.duration);
    gantt.updateTask(task.id);
  }









  priorityLabel(task) {
    var value = task.priority;
    var list = gantt.serverList("priority");
    for (var i = 0; i < list.length; i++) {
      if (list[i].key == value) {
        return list[i].label;
      }
    }
    return "";
  }


  statusLabel(task) {
    var value = task.status_id;
    var list = gantt.serverList("status_list");
    for (var i = 0; i < list.length; i++) {
      if (list[i].key == value) {
        return list[i].label;
      }
    }
    return "";
  }



  ganttLabel(task) {
    var value = task.type_name;
    var list = gantt.serverList("gantt_list");
    for (var i = 0; i < list.length; i++) {
      if (list[i].key == value) {
        return list[i].label;
      }
    }
    return "";
  }

  billableLabel(task) {
    var value = task.billable_act;
    var list = gantt.serverList("billable_list");
    for (var i = 0; i < list.length; i++) {
      if (list[i].key == value) {
        return list[i].label;
      }
    }
    return "";
  }

  fricewLabel(task) {
    var value = task.fricew;
    var list = gantt.serverList("fricew_list");
    for (var i = 0; i < list.length; i++) {
      if (list[i].key == value) {
        return list[i].label;
      }
    }
    return "";
  }

  performAction() {

    switch (this.ganttService.ganttConfig.actionName) {
      case "insertBelow":
        gantt.performAction(this.ganttService.ganttConfig.actionName);
        break;

      case "insertBelowSame":
        gantt.performAction(this.ganttService.ganttConfig.actionName);
        break;

      case "movedown":
        gantt.performAction(this.ganttService.ganttConfig.actionName);
        break;

      case "moveup":
        gantt.performAction(this.ganttService.ganttConfig.actionName);
        break;

      case "reduceDates":
        gantt.performAction(this.ganttService.ganttConfig.actionName);
        break;

      case "addDates":
        gantt.performAction(this.ganttService.ganttConfig.actionName);
        break;

      case "delete":
        gantt.performAction(this.ganttService.ganttConfig.actionName);
        break;

      case "indentleft":
        gantt.performAction(this.ganttService.ganttConfig.actionName);
        break;

      case "indentright":
        gantt.performAction(this.ganttService.ganttConfig.actionName);
        break;

      case "redo":
        gantt.performAction(this.ganttService.ganttConfig.actionName);
        break;

      case "undo":
        gantt.performAction(this.ganttService.ganttConfig.actionName);
        break;

      default:
        this.performGanttAction(this.ganttService.ganttConfig.actionName)
    }


  }


  applyConfig(config, dates) {

    gantt.config.scales = config.scales;

    // restore the previous scroll position
    if (config.scroll_position) {
      setTimeout(() => {
        gantt.scrollTo(config.scroll_position.x, config.scroll_position.y)
      }, 4)
    }
  }

  getUnitsBetween(from, to, unit, step) {
    var start = this.convertUTCDate(from),
      end = this.convertUTCDate(to);
    var units = 0;
    while (start.valueOf() < end.valueOf()) {
      units++;
      start = gantt.date.add(start, step, unit);
    }
    return units;
  }


  performGanttAction(actionName) {
    var action = this.actions[actionName];
    if (!action)
      return;

    else
      action()
  }

  excelFormatter(task) {
    let excludeList = [];
    for (let i = 0; i < gantt.config.columns.length - 1; i++) {
      let column = gantt.config.columns[i];
      task[column.name] =
        column.template && !_.contains(excludeList, column.name)
          ? column.template(task)
          : task[column.name];

      if (!task[column.name] || task[column.name] == 'undefined') {
        task[column.name] = '';
      } else {
        task[column.name] += '';
      }
    }
    return task;
  }


  applyColumnConfigMain() {
    //console.log(this.ganttColumnList)
    let itemList = [];
    //let hideColumn = _.where(this.ganttColumnList, { is_applicable_gantt: true, isVisible: true, isActive: true })
    for(let column of this.ganttColumnList){
      //console.log(column.is_applicable_gantt, column.isVisible, column.isActive, (column.is_applicable_gantt && column.isVisible && column.isActive), column.item)
      if(column.is_applicable_gantt && column.isVisible && column.isActive){
        itemList.push(column.item)
        //console.log(itemList, column);
      }
    }
    //let itemList = _.pluck(hideColumn, "item")

    //console.log(itemList)

    for (let column of this.columnConfig) {
      if (column['id'] != 'add_button' && column['id']!='edit_button') {
        if (_.contains(itemList, column['id'])) {
          column['hide'] = false
        }
        else {
          column['hide'] = true
        }
      }
    }

    //console.log(this.columnConfig)


    gantt.config.columns = this.columnConfig;

    return;
  }


  openPlanningLightBox(gantt_id, mode) {
    let allTask = gantt.getTaskByTime()
    //console.log("ALl Task", allTask)
    let dialogConfig = {
      data: {
        mode: mode,
        projectId: this.projectId,
        itemId: this.itemId,
        ganttId: gantt_id,
        data:{
          start_date: moment(this.ganttService.ganttConfig.item_start_date).format("YYYY-MM-DD"),
          end_date : moment(this.ganttService.ganttConfig.item_end_date).format("YYYY-MM-DD"),
          status_id: gantt.getTaskByWBSCode("1").status_id,
          timesheet_ids: this.ganttService.ganttConfig.timesheet_gantt_ids
        },
        ganttList: this.getGanttTypeList(gantt.getTask(gantt_id).type_name),
        readOnly: this.ganttService.ganttConfig.readOnly,
        ganttData: allTask,
        
      },
      panelClass: 'light-box-panel-class',
      animation: this.dialog_animation,
      viewContainerRef:this.viewContainerRef
    }



    const dialogRef = this.dialog.open(PmPlanningLightBoxComponent, dialogConfig);

    dialogRef.afterClosed().subscribe(async (result) => {
        if(result['messType']=="S")
        {
            console.log(result)
            result['data']['start_date'] = this.convertDate(result['data']['start_date'])
            result['data']['end_date'] = this.convertDate(result['data']['end_date'])
            result['data']['actual_start_date'] = result['data']['actual_start_date']!="" ? moment(result['data']['actual_start_date']).format("YYYY-MM-DD") :""
            result['data']['actual_end_date'] = result['data']['actual_end_date']!="" ? moment(result['data']['actual_end_date']).format("YYYY-MM-DD"):""
            result['data']['completion_percentage'] = result['data']['completion_percentage']
            result['data']['progress'] =  result['data']['completion_percentage'] && result['data']['completion_percentage']!=0 ? result['data']['completion_percentage']/100 :0
            result['data']['description'] = result['data']['description']
            result['data']['long_description'] = result['data']['description']
            result['data']['etc'] = result['data']['etc']
            result['data']['etc_hours'] = result['data']['etc_hours'] ? result['data']['etc_hours'] :0
            result['data']['weighted_percentage'] = result['data']['weighted_percentage'] ? result['data']['weighted_percentage']:0
            result['data']['sheet_number'] = result['data']['sheet_number'] ? result['data']['sheet_number']:''
            result['data']['software_type'] = result['data']['software_type'] ? result['data']['software_type']:''
            result['data']['parent'] = (result['data']['gantt_type'] == 2) ? '0' : result['data']['parent']
            result['data']['billable_act'] = result['data']['billable_act'] ? result['data']['billable_act'] : ''
            if(mode == 'create'){
              let id = gantt.uid();
              result['data']['id']=id;
              result['data']['parent'] = gantt_id;
              result['data']['gantt_type_id'] = result['data']['gantt_type'];
              //result['data']['wbs'] = gantt.getWBSCode(result['data']);
              ////console.log(result['data']['wbs']);
              let list = gantt.serverList('gantt_list');
              let gantt_name = _.where(list, {id: result['data']['gantt_type']});
              result['data']['type_name'] = gantt_name.length > 0 ? gantt_name[0]['gantt_type'] : '';
              result['data']['type']="task"
              result['data']['open'] = true;
              result['data']['wbs_code'] = ' ';
              //console.log(result['data']);
              this.compareGanttLineItems(null, result['data'])
              gantt.addTask(result['data']);
              gantt.updateTask(result['data']['id'], result['data']);
              gantt.render();
              gantt.refreshData();
            }
            else{
              
            // var taskStart = result['data']['start_date'];
            // var taskEnd = result['data']['end_date'];
            // var scaleStart = gantt.config.start_date;
            // var scaleEnd = gantt.config.end_date;

            // // check if the task is out of the range
            // if(scaleStart > taskEnd || scaleEnd < taskStart){
            //   if(scaleStart > taskEnd){
            //     result['data']['start_date'] = scaleStart;
            //   }
            //   if(scaleEnd < taskStart)
            //   {
            //     result['data']['end_date'] = scaleEnd;
            //   }
            // }

            console.log(result['data']['id'], result['data'])
              gantt.updateTask(result['data']['id'], result['data'])
              gantt.refreshData()
              
            }

            if(result['dependency']){
              if(result['dependency']['newLinkData']){
                for(let link of result['dependency']['newLinkData']){
                  link['type'] = link['type']!="" && link['type']!=undefined ? link['type']+"": 0
                  link['lag'] = link['lag']!="" && link['lag']!=undefined ? parseInt(link['lag']) : 0
                  link['type'] = (""+link['type'] + "").toString()
                
                  gantt.addLink(link)
                }
              }
              if(result['dependency']['updateLinkData']){
                for(let link of result['dependency']['updateLinkData']){
                  link['type'] = link['type']!="" && link['type']!=undefined ? link['type']+"" : 0
                  link['lag'] = link['lag']!="" && link['lag']!=undefined ? parseInt(link['lag']) : 0

                  gantt.getLink(link['id']).type = link['type']+""
                  gantt.getLink(link['id']).lag = link['lag']
                  gantt.updateLink(link['id'])
                }
              }

              if(result['dependency']['deleteLinkData']){
                for(let link of result['dependency']['deleteLinkData'])
                {
                  gantt.deleteLink(link['id'])
                }
              }
            }
            
        }

    });
  }


  getGanttName(type){
    let list = gantt.serverList('gantt_list');
    let gantt_name = _.where(list,{gantt_type: type});
    if(gantt_name.length>0)
    {
      return gantt_name[0]['name']
    }
    else
    {
      return ""
    }
  }

  ganttTypeIdName(type_id){
    let list = gantt.serverList('gantt_list');
    //console.log(list)
    let gantt_name = _.where(list,{id: type_id});
    //console.log("Gantt Name", gantt_name)
    if(gantt_name.length>0)
    {
      return gantt_name[0]['gantt_type']
    }
    else
    {
      return ""
    }
  }

  getGanttTypeId(type){
    let list = gantt.serverList('gantt_list');
    //console.log(list)
    let gantt_name = _.where(list,{gantt_type: type});

    if(gantt_name.length>0)
    {
      return gantt_name[0]['id']
    }
    else
    {
      return ""
    }
  }

  ngOnDestroy(){

    _.each(_.uniq(this.attachedEvents), (event) => {
      gantt.detachEvent(event);
    });

    // gantt.destructor();
    
    this.viewContainerRef.clear();
    gantt = null;
  
    
  }


  applyTimelineClassColor(){
      let list = gantt.serverList('gantt_list');
  

      let color = _.findWhere(list,{gantt_type:"project"});
      color ? document.documentElement.style.setProperty('--task_project', color['gantt_color']) : "" 
  
      color = _.findWhere(list,{gantt_type:"wave"})
      color ? document.documentElement.style.setProperty('--task_wave', color['gantt_color']) : ""

      color = _.findWhere(list,{gantt_type:"phase"})
      color ? document.documentElement.style.setProperty('--task_phase', color['gantt_color']) : ""

      color = _.findWhere(list,{gantt_type:"activity"})
      color ? document.documentElement.style.setProperty('--task_activity', color['gantt_color']) : ""

  }



	saveConfig() {
		let  config = gantt.config;
		this.cachedSettings = {};
		this.cachedSettings.scales = config.scales;
		this.cachedSettings.start_date = config.start_date;
		this.cachedSettings.end_date = config.end_date;
		this.cachedSettings.scroll_position = gantt.getScrollState();
	}

  // restoreConfig() {
	// 	this.applyConfig(this.cachedSettings);
	// }



  
	zoomToFit() {
		let project = gantt.getSubtaskDates()
		let areaWidth = gantt.$task.offsetWidth
		let scaleConfigs = this.zoomConfigs.levels;

    let i=0;
		for (i = 0; i < scaleConfigs.length; i++) 
    {
			let columnCount = this.getUnitsBetween(project.start_date, project.end_date, scaleConfigs[i].scales[scaleConfigs[i].scales.length-1].unit, scaleConfigs[i].scales[0].step);

			if ((columnCount + 2) * gantt.config.min_column_width <= areaWidth) 
      {
				break;
			}
		}


		if (i == scaleConfigs.length) {
			i--;
		}

    //console.log(scaleConfigs[i].name)
		gantt.ext.zoom.setLevel(scaleConfigs[i].name);
		this.applyConfig(scaleConfigs[i], project);
	}


  updateCompletionPercentage(){
    return;
  }


  insertTemplateData(){

  
      this.loading=true;
      let templateData = this.ganttService.ganttConfig.templateData;
      let templateDependencyData = this.ganttService.ganttConfig.templateDependencyData;
      let task_id = this.ganttService.ganttConfig.onTaskClickGanttData['id'];
      if(!gantt.hasChild(task_id))
      {
        let task_data = gantt.getTask(task_id)
        //console.log('task data', task_data);
        gantt.getTask(task_id).$open = true
        
        
        let template_parent_id = [];
        let parent_ids = _.uniq(_.pluck(templateData, 'parent'))
        for(let parent of parent_ids){
          let check_parent = _.where(templateData, {gantt_id: parent});
          if(check_parent.length == 0){
            template_parent_id.push(parent);
          }
        }
        //console.log('template parent id', template_parent_id)

        let reference_ids = [];

        for(let data of templateData)
        {
          
          let find_reference = _.where(reference_ids, {local: data.gantt_id})

          if(find_reference.length == 0){
            let val = {
              'local': data.gantt_id,
              'foreign': gantt.uid()
            }
            data.gantt_id = val.foreign;
            reference_ids.push(val);
          }
          else{
            let foreign = find_reference[0].foreign
            data.gantt_id = foreign;
          }
        }

        for(let data of templateData){
          if(_.contains(template_parent_id, data.parent)){
            data.parent = task_data.id;
          }
          else{
            let find_reference = _.where(reference_ids, {local: data.parent})
            if(find_reference.length > 0 ){
              data.parent = find_reference[0].foreign;
            }
          }
          
        }
        //console.log('after parent updation',templateData)
        for(let data of templateData)
        {

          var list = gantt.serverList("gantt_list");
    
          let validateGanttType = _.where(list, { id: data['gantt_type'] })
          let duration = (data['duration'] != '' && data['duration'] != null && data['duration'] != "null") ? data.duration : '';
          
          if(validateGanttType.length>0)
          {
            let start_date = (task_data.type_name == 'project') ? this.convertUTCDate(this.ganttService.ganttConfig.item_start_date) : (duration != '') ? this.convertUTCDate(moment(task_data.start_date).format("YYYY-MM-DD")) : task_data.start_date;


            let end_date = (task_data.type_name == 'project') ? this.convertUTCDate(this.ganttService.ganttConfig.item_end_date) : duration != '' ? this.convertUTCDate(moment(task_data.end_date).format("YYYY-MM-DD")) : task_data.end_date;
   

            
            var task = {
              text: data.description,
              start_date: start_date,
              end_date: end_date,
              duration: gantt.calculateDuration({
                start_date: start_date,
                end_date: end_date,
              }),
              parent: data.parent,
              type:"task",
              type_name: validateGanttType[0]['key'],
              status_id:7,
              id: data.gantt_id,
              wbs_code: ' ',
              estimated_hours: data.estimated_hours ? data.estimated_hours : '',
              billable_act: data.billable_act ? data.billable_act : false
            };
      
            gantt.addTask(task);
            gantt.updateTask(task.id, task);
          }
         
        }
       
        //console.log('template data', templateData);
        console.log('template data', templateDependencyData);
        for(let data of templateDependencyData){
          let source = _.where(reference_ids, {local: data.source_gantt_id})
          if(source.length > 0){
            data.source = source[0]['foreign'];
          }
          let target = _.where(reference_ids, {local: data.target_gantt_id})
          if(target.length > 0){
            data.target = target[0]['foreign'];
          }
          let val = {
            source: data.source,
            target: data.target,
            type: data.dependency_type,
            lag: data.dependency_lag
          }
          gantt.addLink(val);
        }
        this.loading=false;
  
      }
      
      else
      {
        this.loading=false;
        
        const dependency_delete_msg = this.retrieveMessages.length > 0 ? this.retrieveMessages[0].errors.dependency_delete_msg ? this.retrieveMessages[0].errors.dependency_delete_msg : 'Please delete child task to proceed with Adapt Template!' : 'Please delete child task to proceed with Adapt Template!';
        this.toasterService.showWarning(dependency_delete_msg, 10000);
      }

      
      
  }


  calculateDynamicContentHeight() {
   
    if(!this.ganttService.ganttConfig.fullScreen)
    {
      const height = window.innerHeight - 70;
      const innerheight = height - 160;
      this.dynamicHeight = (window.innerHeight - 212) + 'px';
      this.dynamicinnerHeight = innerheight + 'px';
      document.documentElement.style.setProperty(
        '--detailsHeight',
        this.dynamicHeight
      );
      document.documentElement.style.setProperty(
        '--innerTabHeight',
        this.dynamicinnerHeight
      );
 

      gantt.render();
      gantt.refreshData();
    }
    else
    {
      document.documentElement.style.setProperty(
        '--detailsHeight',
        '100%'
      );
      document.documentElement.style.setProperty(
        '--innerTabHeight',
        '90%'
      );
    }
  }

  getGanttTypeList(gantt_type_id){

    let selectedGanttList =[]
    
    let list = _.where(this.ganttService.ganttConfig.masterData['ganttTypeListRollUp'],{service_type_id: this.ganttService.ganttConfig.service_type_id, gantt_type: gantt_type_id})

    let fetchedList = _.uniq(_.pluck(list,"next_gantt_id"))

    console.log(fetchedList)

    selectedGanttList = _.filter(this.ganttService.ganttConfig.masterData['gantt_list'],(res)=>{
      if(_.contains(fetchedList,res['id']))
      {
        return res;
      }
    })

    console.log(selectedGanttList)

    return selectedGanttList;
  }


  async compareGanttLineItems(prevState, newState)
  {
      let oldTask = prevState!=null?JSON.parse(JSON.stringify(prevState)):null
      let newTask = newState && newState!=null ?JSON.parse(JSON.stringify(newState)) :null
      //console.log(oldTask, newTask, _.isEqual(oldTask, newTask))
      if(newTask!=null)
      {
        if(!newTask.text.includes("+ Add") && oldTask)
        {
          if(!_.isEqual(oldTask, newTask))
          {
              let changedParams = [];

              for(let [key, value] of Object.entries(oldTask))
              {
                  if(oldTask.hasOwnProperty(key) && newTask.hasOwnProperty(key))
                  {
                    
                      if(oldTask[key] != newTask[key])
                      {
                          if(key == "status_id")
                          {
                              oldTask['status_id']=await this.getStatusName(oldTask['status_id'])
                              newTask['status_id']=await this.getStatusName(newTask['status_id'])
                          }
                          if(key == "type_name")
                          {
                              oldTask['type_name']=await this.getGanttName(oldTask['type'])
                              newTask['type_name']=await this.getGanttName(newTask['type'])
                          }
                          changedParams.push(key)
                      }
                  }
              }


              //console.log("Params",changedParams)

              if(changedParams.length>0)
              {
                  oldTask['type_name']=await this.getGanttName(oldTask['type'])
                  newTask['type_name']=await this.getGanttName(newTask['type'])
                  this.updateRecentActivities(oldTask, newTask);
      
              }





          }
        }
        else if(prevState==null)
        {
              newTask['type_name'] = await this.getGanttName(newTask['type'])
              this.updateRecentActivities(null, newTask);
            
        }
      }
  }


  async updateRecentActivities(oldTask, newTask)
  {
      newTask.url = await this.generateURL(newTask.id);
      if(newTask?.gantt_type){
        newTask.type = await this.ganttTypeIdName(newTask?.gantt_type)
        newTask.type_name = await this.getGanttName(newTask['type'])
      }
      if(oldTask){
        oldTask.owner = Object.entries(oldTask)
        .filter(([key, value]) => key.startsWith('assigned_to_aid') && value && value !== 0 &&  value !== "")
        .map(([, value]) => value);
        oldTask.start_date = oldTask.start_date && oldTask.start_date !== "0000-00-00 00:00:00" ? moment(oldTask?.start_date).format("YYYY-MM-DD") : null
        oldTask.end_date = oldTask.end_date && oldTask.end_date !== "0000-00-00 00:00:00" ? moment(oldTask?.end_date).format("YYYY-MM-DD") : null
        oldTask.description = oldTask.long_description && oldTask.long_description !== "" ? oldTask.long_description : null;
      }
      if(newTask){
        newTask.owner = Object.entries(newTask)
        .filter(([key, value]) => key.startsWith('assigned_to_aid') && value && value !== 0 && value !== "")
        .map(([, value]) => value);
        newTask.start_date = newTask.start_date && newTask.start_date !== "0000-00-00 00:00:00" ? moment(newTask?.start_date).format("YYYY-MM-DD") : null
        newTask.end_date = newTask.end_date && newTask.end_date !== "0000-00-00 00:00:00" ? moment(newTask?.end_date).format("YYYY-MM-DD") : null      }
      this.ganttService.updateRecentActivities(this.projectId, this.itemId, oldTask, newTask)
  }

  getStatusName(id){
    let status_list = gantt.serverList('status_list')
    if(id!="")
    {
      let status = _.where(status_list,{id:parseInt(id)})

      if(status.length>0)
      {
          return status[0]['name']
      }
      else
      {
          return ""
      }
    }
    else
    {
        return ""
    }
    
  }

  async getMotherWorkstreamGanttId(id)
  {
    let parent = gantt.getParent(id)

    //console.log(parent, gantt.getTask(parent))
    if(parent=="0" || parent=="-1" || parent==0 || parent==null || gantt.getTask(parent).type_name=="project")
    {
      return null;
    }
    else if(parent && gantt.getTask(parent).type_name=="wave")
    {
      return parent;
    }
    else
    {
      return await this.getMotherWorkstreamGanttId(parent)
    }
  }

  async getMotherPhaseGanttId(id)
  {
    let parent = gantt.getParent(id)

    if(parent=="0" || parent=="-1" || parent==0 || parent==null || gantt.getTask(parent).type_name=="project")
    {
      return null;
    }
    else if(parent && gantt.getTask(parent).type_name=="phase")
    {
      return parent;
    }
    else
    {
      return await this.getMotherPhaseGanttId(parent)
    }
  }


  validateHasChild(id)
  {
    let children = gantt.getChildren(id)

    let i =0;

    for(let child of children)
    {
        let child_task = gantt.getTask(child)

        if(!child_task.text.includes("+ Add"))
        {
          i++;
        }
    }

    if(i==0)
    {
        return false;
    }
    else
    {
        return true;
    }
  }

  convertUTCDate(myDate)
  {
    
    let year = moment(myDate).utc().year()
    let month = moment(myDate).utc().month()
    let day = moment(myDate).utc().date()

    console.log("Year", year, month, day, myDate)
    let now_utc = new Date(year,month,day);

    return now_utc
  }


  convertDate(myDate)
  {
    
    let year = moment(myDate).year()
    let month = moment(myDate).month()
    let day = moment(myDate).date()
    console.log("Year", year, month, day, myDate)

    let now_utc = new Date(year,month,day);
    return now_utc
  }



  updateParentTask(id)
  {
      let currTask = gantt.getTask(id)

      if(currTask)
      {
        let parent = gantt.getTask(currTask.parent)
        gantt.updateTask(parent, parent.id)
        this.updateParentTask(parent.id)
      }
      

      
  }

  getColumnName(field_name, default_column_name){
    let columnName = _.where(this.formConfig,{type:"gantt_columns", field_name:field_name, is_active: true})

    if(columnName.length>0)
    {
        return columnName[0]['label'] ? columnName[0]['label']:default_column_name
    }
    else
    {
        return default_column_name
    }
  }


  viewGantt(){
    this.authService.getProjectWiseObjectAccess(this.projectId, this.itemId, 86).then((res)=>{
      
      if(!res)
          this.performGanttAction('gridView')
      
    })
  }


  checkGanttStructure(curr_type_name, parent_type_name){

    let parent_gantt_type_id = this.getGanttTypeId(parent_type_name)
    let curr_type_id = this.getGanttTypeId(curr_type_name)

    
    
    let list = _.where(this.ganttService.ganttConfig.masterData['ganttTypeListRollUp'],{service_type_id: this.ganttService.ganttConfig.service_type_id, gantt_id: parent_gantt_type_id, next_gantt_id: curr_type_id})

    //console.log("list", this.ganttService.ganttConfig.masterData['ganttTypeListRollUp'], parent_gantt_type_id, curr_type_id, list)

    if(list.length>0)
    {
        //console.log("List out", true)
        return true;
    }
    else
    {
        return false;
    }
  }


  deleteCheck(id) {
    
    if(_.contains(this.ganttService.ganttConfig.timesheet_gantt_ids,id))
    { 
      return true;
    }
    else
    {

      let children = gantt.getChildren(id)

      for(let child of children)
      {
            
          if(this.deleteCheck(child))
          {
              return true;
          }
            
      }

      return false;
    }
   
    
   
  }

  checkTimesheet(id)
  {
      if(_.contains(this.ganttService.ganttConfig.timesheet_gantt_ids,id))
      {
          return true;
      }
      else
      {
          return false;
      } 
  }


  openBranch(id) {

      let parentId = gantt.getParent(id);
      gantt.open(parentId);
      this.openBranch(parentId);
    
  }



  validateTaskDates(task) {
    if (task.start_date <= gantt.config.start_date) {
        task.start_date = gantt.config.start_date;
    }
    if (task.end_date >= gantt.config.end_date) {
        task.end_date = gantt.config.end_date;
    }
  }


  isTaskWithinProject(task) {
    return task.start_date >= gantt.config.start_date && task.end_date <= gantt.config.end_date;
  }


  async generateURL(id){
    let urlArray = []

    let url = ""

    console.log("URL Array 1", urlArray)

    gantt.eachParent(parent=>{
     
      urlArray.push(parent.text)

    }, id);


    console.log("URL Array 2", urlArray)


    urlArray = urlArray.reverse();

    for(let data of urlArray)
    {
        url += ( data + " / " )
    }

    url += gantt.getTask(id).text


    return url;

  }



  billableFlagActivityEdit(id) {

    const billableActivityCheckNeeded = _.findWhere(this.formConfig,{type:"gantt", field_name:"billable_activity_check", is_active: true})

    if (this.billableActivityCheck(id) && billableActivityCheckNeeded) 
    {
        return true;
    }

    return false;
  }


  billableActivityCheck(id){
   
    if(_.contains(this.ganttService.ganttConfig.timesheet_gantt_ids,id))
    { 
      return true;
    }
    else
    {
      return false;
    }
  }
  

  propagateProgressToParents(taskId) {
    let currentTask = gantt.getTask(taskId);

    while (currentTask.parent && currentTask.parent !== 0) {
        const parent = gantt.getTask(currentTask.parent);
        const children = gantt.getChildren(parent.id);

        // Calculate the parent's progress based on children
        let totalDuration = 0;
        let weightedProgress = 0;

        let count = 0;
        children.forEach(childId => {
            const child = gantt.getTask(childId);
     
            weightedProgress += child.progress;
            if(!child.text.includes("+ Add"))
            {
              count++;
            }
        });

        const newProgress =weightedProgress / count;

        // Update parent only if progress has changed
        if (parent.progress !== newProgress) {
            parent.progress = newProgress;
            gantt.updateTask(parent.id); // Apply changes to parent task
        } else {
            break; // Exit if no update is needed
        }

        currentTask = parent; // Move up to the next parent
    }
}

  
  



}


