import { Component, OnInit, Renderer2 } from '@angular/core';
import * as _ from "underscore";
import { Router } from '@angular/router';
import { PmAuthorizationService } from 'src/app/modules/project-management/services/pm-authorization.service';
import { PmMasterService } from 'src/app/modules/project-management/services/pm-master.service';
import { PmProjectTeamService } from '../../services/pm-project-team.service'
@Component({
  selector: 'app-pm-project-team-page',
  templateUrl: './pm-project-team-page.component.html',
  styleUrls: ['./pm-project-team-page.component.scss']
})
export class PmProjectTeamPageComponent implements OnInit {
  
  isComponentLoading: boolean = false;
  selectedToggle: any;
  data: any
  toggle: any = '#EE4961'
  team_access: any
  projectId: any;
  itemId: any;
  withOpportunity: any;


  projectTeamTabs: any = [
    {
      "field_name": "team",
      "type": "project_team_tab",
      "is_active": this.authService.getProjectObjectAccess(37),
      "is_mandant": false,
      "label": "Team",
      "link": 'team'
    }
    , {
      "field_name": "projectStructure",
      "type": "project_team_tab",
      "is_active": this.authService.getProjectObjectAccess(5),
      "is_mandant": false,
      "label": "Project Structure",
      "link": 'project-structure'
    }
    , {
      "field_name": "skillMatrix",
      "type": "project_team_tab",
      "is_active": this.authService.getProjectObjectAccess(7),
      "is_mandant": false,
      "label": "Skill Matrix",
      "link": 'skill-matrix'
    },
    {
      "field_name": "externalStakeholders",
      "type": "project_team_tab",
      "is_active": this.authService.getProjectObjectAccess(69),
      "is_mandant": false,
      "label": "External Stakeholders",
      "link": 'external-stakeholders'
    }
  ]
  


  formConfig: any = [];
  button: any;
  shades: any;
  fontStyle: any;
  serviceTypeList: any = [];
  constructor(private authService: PmAuthorizationService,
    private _router: Router, 
    private pmMasterService: PmMasterService,
    private teamService: PmProjectTeamService) { }

  async ngOnInit() {
    this.itemId = parseInt(this._router.url.split('/')[5]);
    this.projectId = parseInt(this._router.url.split('/')[3]);
    
    await this.pmMasterService.getPMFormCustomizeConfigV().then((res: any) => {
      this.formConfig = res;
      if(this.formConfig.length > 0){
        const retrieveStyles = _.where(this.formConfig, { type: "project-theme", field_name: "styles", is_active: true });
        this.button = retrieveStyles.length > 0 ? retrieveStyles[0].data.button_color ? retrieveStyles[0].data.button_color : "#90ee90" : "#90ee90";
        this.shades = retrieveStyles.length > 0 ? retrieveStyles[0].data.shades_color ? retrieveStyles[0].data.shades_color : "#C9E3B4" : "#C9E3B4";
        document.documentElement.style.setProperty('--intShades', this.shades)
        document.documentElement.style.setProperty('--intButton', this.button)
        this.fontStyle = retrieveStyles.length > 0 ? retrieveStyles[0].data.font_style ? retrieveStyles[0].data.font_style : "Roboto" : "Roboto";
        document.documentElement.style.setProperty('--intFont', this.fontStyle);

      }
    })
    
    await this.teamService.getProjectQ2CStatus(this.projectId, this.itemId).then(async (res: any)=>{
      if(res['messType']=="S")
      {
          this.withOpportunity = res['data']
          if(this.withOpportunity==0)
          {
            this.projectTeamTabs.push({
              "field_name": "resourceRequest",
              "type": "project_team_tab",
              "is_active": this.authService.getProjectObjectAccess(6),
              "is_mandant": false,
              "label": "Allocation Request",
              "link": 'resource-request'
            })
          }
          else
          {
            this.projectTeamTabs.push({
              "field_name": "requestStatus",
              "type": "project_team_tab",
              "is_active": this.authService.getProjectObjectAccess(38),
              "is_mandant": false,
              "label": "CRM Quote",
              "link": 'request-status'
            })
            await this.pmMasterService.serviceTypeList().then((res)=>{
              this.serviceTypeList = res;
            });
            let serviceData = _.where(this.serviceTypeList, {
              id: res['service_type'],
            });
            let resourceLoadingApplicable = (serviceData && serviceData.length > 0) ? serviceData[0]['is_resource_loading_applicable'] ? serviceData[0]['is_resource_loading_applicable'] : 0 : 0;
            let disable_billing_plan = (serviceData && serviceData.length > 0) ? serviceData[0]['deliverable_based_billing_plan'] ? serviceData[0]['deliverable_based_billing_plan'] : 0 : 0;
            if(resourceLoadingApplicable && disable_billing_plan){
              this.projectTeamTabs.push({
                "field_name": "billingPlanVersion",
                "type": "billing_plan_version",
                "is_active": this.authService.getProjectObjectAccess(308),
                "is_mandant": false,
                "label": "Billing Plan",
                "link": 'billing-plan'
              })
            }

          }
      }
      else
      {
        this.projectTeamTabs.push({
          "field_name": "resourceRequest",
          "type": "project_team_tab",
          "is_active": this.authService.getProjectObjectAccess(6),
          "is_mandant": false,
          "label": "Allocation Request",
          "link": 'resource-request'
        })
        this.projectTeamTabs.push({
          "field_name": "requestStatus",
          "type": "project_team_tab",
          "is_active": this.authService.getProjectObjectAccess(38),
          "is_mandant": false,
          "label": "CRM Quote",
          "link": 'request-status'
        })
      }
    })
  //   console.log(this.projectTeamTabs)
  //   let toggle = this.projectTeamTabs.filter((val) => {
  //     if (val.link == this._router.url.split("/")[8]) {
  //       return val.field_name;
  //     }
  //   })
  //  console.log(toggle)
  //   this.selectedToggle = toggle[0].field_name

    // for(let tab of this.projectTeamTabs)
    // {
    //     if(tab['is_active'])
    //     {
    //         let url_array = this._router.url.split("/")
    //         url_array[8] = tab['link']
    //         this._router.navigate(url_array)
    //         this.selectedToggle = tab['field_name']
    //         break;
    //     }
    // }
    const urlSegments = this._router.url.split("/");

    // Extract the relevant segment (e.g., the 9th segment)
    const currentLink = urlSegments[8];

    // Find the tab with a matching link
    const activeTab = this.projectTeamTabs.find(tab => (tab.link === currentLink && tab.is_active));

    // If a matching tab is found, update selectedToggle
    if (activeTab) {
      this.selectedToggle = activeTab.field_name;
    }
    else{
      for(let tab of this.projectTeamTabs)
        {
            if(tab['is_active'])
            {
                let url_array = this._router.url.split("/")
                url_array[8] = tab['link']
                this._router.navigate(url_array)
                this.selectedToggle = tab['field_name']
                break;
            }
        }
    }
   
    document.documentElement.style.setProperty('--toggle-selected', this.toggle);


  }


  selectToggle(event) {
    console.log(this.selectedToggle)
    console.log(event.value)
    this.selectedToggle = event.value;
  }





}