<div class="billing-plan-version">
  <!--------------------Loader-------------------------->
  <ng-container *ngIf="isComponentLoading">
    <div class="empty-state">
      <img class="image" [src]="loadingGif || ''" />
      <div class="loading-wrapper">
        <div class="loading">Loading...</div>
      </div>
    </div>
  </ng-container>

  <ng-container *ngIf="intializeBillingPlan">
    <div class="empty-state">
      <img class="image" [src]="loadingGif || ''" />
      <div class="loading-wrapper">
        <div class="loading">{{textIntializeBillingPlan}}</div>
      </div>
    </div>
  </ng-container>
  <!-------------------- Billing Version List data -------------------------->
  <div
    class="list-class"
    *ngIf="!isComponentLoading && !intializeBillingPlan && billingVersionList.length > 0"
  >
    <div class="list" *ngFor="let data of billingVersionList; let i = index">
      <div class="list-content">
        <div class="id_class">
          <div class="status-flag " [ngClass]="data.billing_status && data.billing_status == 1 ? 'active' : 'draft'">
            <span class="flag-content">{{ (data.billing_status && data.billing_status == 1) ? 'Active' : "Draft" }}</span>
          </div>
          <div class="quote-header">
            <span>MID:</span>
            <span>{{ data.milestone_id ? data.milestone_id : "-" }}</span>
          </div>
        </div>
        <div class="data-class">
          <div class="svg-class">
            <svg
              width="48"
              height="45"
              viewBox="0 0 48 45"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g clip-path="url(#clip0_19017_68116)">
                <path
                  d="M-0.00195312 42.75V2.25C-0.00195312 0.975 0.973047 0 2.24805 0H45.748C47.023 0 47.998 0.975 47.998 2.25V42.75C47.998 44.025 47.023 45 45.748 45H2.24805C0.973047 45 -0.00195312 44.025 -0.00195312 42.75Z"
                  fill="#CAD1D8"
                />
                <path
                  d="M2.24805 3.75C2.66226 3.75 2.99805 3.41421 2.99805 3C2.99805 2.58579 2.66226 2.25 2.24805 2.25C1.83384 2.25 1.49805 2.58579 1.49805 3C1.49805 3.41421 1.83384 3.75 2.24805 3.75Z"
                  fill="#9BA7AF"
                />
                <path
                  d="M5.24805 3.75C5.66226 3.75 5.99805 3.41421 5.99805 3C5.99805 2.58579 5.66226 2.25 5.24805 2.25C4.83384 2.25 4.49805 2.58579 4.49805 3C4.49805 3.41421 4.83384 3.75 5.24805 3.75Z"
                  fill="#9BA7AF"
                />
                <path
                  d="M8.24805 3.75C8.66227 3.75 8.99805 3.41421 8.99805 3C8.99805 2.58579 8.66227 2.25 8.24805 2.25C7.83382 2.25 7.49805 2.58579 7.49805 3C7.49805 3.41421 7.83382 3.75 8.24805 3.75Z"
                  fill="#9BA7AF"
                />
                <path
                  d="M2.24805 6H45.748C46.198 6 46.498 6.3 46.498 6.75V42.75C46.498 43.2 46.198 43.5 45.748 43.5H2.24805C1.79805 43.5 1.49805 43.2 1.49805 42.75V6.75C1.49805 6.3 1.79805 6 2.24805 6Z"
                  fill="white"
                />
                <path
                  d="M45.748 3.75H11.248C10.798 3.75 10.498 3.45 10.498 3C10.498 2.55 10.798 2.25 11.248 2.25H45.748C46.198 2.25 46.498 2.55 46.498 3C46.498 3.45 46.198 3.75 45.748 3.75Z"
                  fill="#E2E5E7"
                />
                <rect
                  x="5.99805"
                  y="11.25"
                  width="6"
                  height="6"
                  rx="3"
                  fill="#FFF3E8"
                />
                <path
                  d="M10.6628 14.748C10.6628 15.393 10.1411 15.9147 9.49609 15.9147L9.67109 15.623"
                  stroke="url(#paint0_linear_19017_68116)"
                  stroke-width="0.28125"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M7.33008 13.7487C7.33008 13.1037 7.85174 12.582 8.49674 12.582L8.32174 12.8737"
                  stroke="url(#paint1_linear_19017_68116)"
                  stroke-width="0.28125"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M9.28125 12.9902L9.94458 13.3736L10.6012 12.9919"
                  stroke="url(#paint2_linear_19017_68116)"
                  stroke-width="0.28125"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M9.94531 14.0511V13.3711"
                  stroke="url(#paint3_linear_19017_68116)"
                  stroke-width="0.28125"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M9.78766 12.617L9.38765 12.8387C9.29765 12.8887 9.22266 13.0154 9.22266 13.1187V13.542C9.22266 13.6454 9.29599 13.772 9.38765 13.822L9.78766 14.0437C9.87266 14.092 10.0127 14.092 10.0993 14.0437L10.4993 13.822C10.5893 13.772 10.6643 13.6454 10.6643 13.542V13.1187C10.6643 13.0154 10.591 12.8887 10.4993 12.8387L10.0993 12.617C10.0143 12.5704 9.87432 12.5704 9.78766 12.617Z"
                  stroke="url(#paint4_linear_19017_68116)"
                  stroke-width="0.28125"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M7.38867 14.8242L8.05034 15.2076L8.70867 14.8259"
                  stroke="url(#paint5_linear_19017_68116)"
                  stroke-width="0.28125"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M8.05078 15.8851V15.2051"
                  stroke="url(#paint6_linear_19017_68116)"
                  stroke-width="0.28125"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M8.77174 14.9539C8.77174 14.8506 8.69841 14.7239 8.60675 14.6739L8.20674 14.4523C8.12174 14.4039 7.98174 14.4039 7.89508 14.4523L7.49508 14.6739C7.40508 14.7239 7.33008 14.8506 7.33008 14.9539V15.3773C7.33008 15.4806 7.40341 15.6073 7.49508 15.6573L7.89508 15.8789C7.98008 15.9273 8.12008 15.9273 8.20674 15.8789L8.60675 15.6573"
                  stroke="url(#paint7_linear_19017_68116)"
                  stroke-width="0.28125"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M5.99805 20.25C5.99805 19.8 6.29805 19.5 6.74805 19.5H11.998C12.448 19.5 12.748 19.8 12.748 20.25C12.748 20.7 12.448 21 11.998 21H6.74805C6.29805 21 5.99805 20.7 5.99805 20.25Z"
                  fill="#CAD1D8"
                />
                <path
                  d="M5.99805 23.25C5.99805 22.8 6.29805 22.5 6.74805 22.5H10.498C10.948 22.5 11.248 22.8 11.248 23.25C11.248 23.7 10.948 24 10.498 24H6.74805C6.29805 24 5.99805 23.7 5.99805 23.25Z"
                  fill="#CAD1D8"
                />
                <path
                  d="M32.25 26.75H19.5C19.05 26.75 18.75 26.45 18.75 26C18.75 25.55 19.05 25.25 19.5 25.25H32.25C32.7 25.25 33 25.55 33 26C33 26.45 32.7 26.75 32.25 26.75Z"
                  fill="#CAD1D8"
                />
                <path
                  d="M32.25 20.75H19.5C19.05 20.75 18.75 20.45 18.75 20C18.75 19.55 19.05 19.25 19.5 19.25H32.25C32.7 19.25 33 19.55 33 20C33 20.45 32.7 20.75 32.25 20.75Z"
                  fill="#CAD1D8"
                />
                <path
                  d="M30 23.75H19.5C19.05 23.75 18.75 23.45 18.75 23C18.75 22.55 19.05 22.25 19.5 22.25H30C30.45 22.25 30.75 22.55 30.75 23C30.75 23.45 30.45 23.75 30 23.75Z"
                  fill="#CAD1D8"
                />
                <path
                  d="M34.5 29.75H19.5C19.05 29.75 18.75 29.45 18.75 29C18.75 28.55 19.05 28.25 19.5 28.25H34.5C34.95 28.25 35.25 28.55 35.25 29C35.25 29.45 34.95 29.75 34.5 29.75Z"
                  fill="#CAD1D8"
                />
                <path
                  d="M36 17.75H19.5C19.05 17.75 18.75 17.45 18.75 17C18.75 16.55 19.05 16.25 19.5 16.25H36C36.45 16.25 36.75 16.55 36.75 17C36.75 17.45 36.45 17.75 36 17.75Z"
                  fill="#CAD1D8"
                />
                <path
                  d="M27.75 32.75H19.5C19.05 32.75 18.75 32.45 18.75 32C18.75 31.55 19.05 31.25 19.5 31.25H27.75C28.2 31.25 28.5 31.55 28.5 32C28.5 32.45 28.2 32.75 27.75 32.75Z"
                  fill="#CAD1D8"
                />
              </g>
              <defs>
                <linearGradient
                  id="paint0_linear_19017_68116"
                  x1="10.6628"
                  y1="15.3314"
                  x2="9.43433"
                  y2="15.3314"
                  gradientUnits="userSpaceOnUse"
                >
                  <stop stop-color="#EF4A61" />
                  <stop offset="1" stop-color="#F27A6C" />
                </linearGradient>
                <linearGradient
                  id="paint1_linear_19017_68116"
                  x1="8.49674"
                  y1="13.1654"
                  x2="7.26831"
                  y2="13.1654"
                  gradientUnits="userSpaceOnUse"
                >
                  <stop stop-color="#EF4A61" />
                  <stop offset="1" stop-color="#F27A6C" />
                </linearGradient>
                <linearGradient
                  id="paint2_linear_19017_68116"
                  x1="10.6012"
                  y1="13.1819"
                  x2="9.21137"
                  y2="13.1819"
                  gradientUnits="userSpaceOnUse"
                >
                  <stop stop-color="#EF4A61" />
                  <stop offset="1" stop-color="#F27A6C" />
                </linearGradient>
                <linearGradient
                  id="paint3_linear_19017_68116"
                  x1="10.9453"
                  y1="13.7111"
                  x2="9.89237"
                  y2="13.7111"
                  gradientUnits="userSpaceOnUse"
                >
                  <stop stop-color="#EF4A61" />
                  <stop offset="1" stop-color="#F27A6C" />
                </linearGradient>
                <linearGradient
                  id="paint4_linear_19017_68116"
                  x1="10.6643"
                  y1="13.331"
                  x2="9.14633"
                  y2="13.331"
                  gradientUnits="userSpaceOnUse"
                >
                  <stop stop-color="#EF4A61" />
                  <stop offset="1" stop-color="#F27A6C" />
                </linearGradient>
                <linearGradient
                  id="paint5_linear_19017_68116"
                  x1="8.70867"
                  y1="15.0159"
                  x2="7.31879"
                  y2="15.0159"
                  gradientUnits="userSpaceOnUse"
                >
                  <stop stop-color="#EF4A61" />
                  <stop offset="1" stop-color="#F27A6C" />
                </linearGradient>
                <linearGradient
                  id="paint6_linear_19017_68116"
                  x1="9.05078"
                  y1="15.5451"
                  x2="7.99784"
                  y2="15.5451"
                  gradientUnits="userSpaceOnUse"
                >
                  <stop stop-color="#EF4A61" />
                  <stop offset="1" stop-color="#F27A6C" />
                </linearGradient>
                <linearGradient
                  id="paint7_linear_19017_68116"
                  x1="8.77174"
                  y1="15.1656"
                  x2="7.25375"
                  y2="15.1656"
                  gradientUnits="userSpaceOnUse"
                >
                  <stop stop-color="#EF4A61" />
                  <stop offset="1" stop-color="#F27A6C" />
                </linearGradient>
                <clipPath id="clip0_19017_68116">
                  <rect width="48" height="45" fill="white" />
                </clipPath>
              </defs>
            </svg>
          </div>
          <div class="content-class">
            <div class="header">
              
                <span>Milestone -&nbsp;</span>
                <span>{{ data?.deliverable_id ? data?.deliverable_id : "-" }} - &nbsp;</span>
          
             
                <span>{{ data?.deliverable_name ? data?.deliverable_name : "-" }}</span>
                
            </div>
            <div class="sub-text">
              <span>Created On</span>
              <span>{{ getFormattedDate(data?.created_on) }}</span>
              <span>By</span>
              <svg
                width="16"
                height="16"
                viewBox="0 0 16 16"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <rect width="16" height="16" rx="8" fill="#FFF3E8" />
                <path
                  d="M5.49805 10.748C5.49805 10.1292 5.74388 9.53572 6.18146 9.09813C6.61905 8.66055 7.21254 8.41471 7.83138 8.41471C8.45022 8.41471 9.04371 8.66055 9.4813 9.09813C9.91888 9.53572 10.1647 10.1292 10.1647 10.748H9.58138C9.58138 10.2839 9.39701 9.8388 9.06882 9.51061C8.74063 9.18242 8.29551 8.99805 7.83138 8.99805C7.36725 8.99805 6.92213 9.18242 6.59394 9.51061C6.26575 9.8388 6.08138 10.2839 6.08138 10.748H5.49805ZM7.83138 8.12305C6.86451 8.12305 6.08138 7.33992 6.08138 6.37305C6.08138 5.40617 6.86451 4.62305 7.83138 4.62305C8.79825 4.62305 9.58138 5.40617 9.58138 6.37305C9.58138 7.33992 8.79825 8.12305 7.83138 8.12305ZM7.83138 7.53971C8.47596 7.53971 8.99805 7.01763 8.99805 6.37305C8.99805 5.72846 8.47596 5.20638 7.83138 5.20638C7.1868 5.20638 6.66471 5.72846 6.66471 6.37305C6.66471 7.01763 7.1868 7.53971 7.83138 7.53971Z"
                  fill="#DB6E61"
                />
              </svg>
              <span>{{ data?.created_by ? data?.created_by : "-" }}</span>
            </div>
          </div>
        </div>
        <div class="latest-data-class">
          <span> {{data?.billing_title ? data?.billing_title : "-"}}</span>
        
        </div>
        <div class="d-flex justify-content-end" *ngIf="data['is_edit_allowed']" style="margin-left: auto;">
          <div (click)="openBillingPlan(data['quote_id'])" class="edit-class">
            <svg
            width="17"
            height="17"
            viewBox="0 0 17 17"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M1.5 15.5H2.7615L12.998 5.2635L11.7365 4.002L1.5 14.2385V15.5ZM0 17V13.6155L13.1905 0.43075C13.3417 0.293416 13.5086 0.187333 13.6913 0.1125C13.8741 0.0375 14.0658 0 14.2663 0C14.4668 0 14.6609 0.0355838 14.8488 0.10675C15.0367 0.177917 15.2032 0.291083 15.348 0.446249L16.5693 1.68275C16.7244 1.82758 16.835 1.99425 16.901 2.18275C16.967 2.37125 17 2.55975 17 2.74825C17 2.94942 16.9657 3.14133 16.897 3.324C16.8283 3.50683 16.7191 3.67383 16.5693 3.825L3.3845 17H0ZM12.3562 4.64375L11.7365 4.002L12.998 5.2635L12.3562 4.64375Z"
              fill="#45546E"
            />
          </svg>
          </div>
        </div>
      </div>
      <div class="divider-class" *ngIf="i+1 != billingVersionList.length"></div>
    </div>
  </div>
  <!-------------------- No Data -------------------------->
  <div
    class="no-data"
    *ngIf="!isComponentLoading && !intializeBillingPlan && billingVersionList.length == 0"
  >
    <div class="img-class">
      <svg
        width="177"
        height="139"
        viewBox="0 0 177 139"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g clip-path="url(#clip0_19017_68466)">
          <path d="M160.059 0H0.0585938V103H160.059V0Z" fill="#E6E6E6" />
          <path d="M156.059 13H5.05859V97H156.059V13Z" fill="white" />
          <path d="M160.059 0H0.0585938V7H160.059V0Z" fill="#45546E" />
          <path
            d="M5.55859 5C6.38702 5 7.05859 4.32843 7.05859 3.5C7.05859 2.67157 6.38702 2 5.55859 2C4.73017 2 4.05859 2.67157 4.05859 3.5C4.05859 4.32843 4.73017 5 5.55859 5Z"
            fill="white"
          />
          <path
            d="M10.5586 5C11.387 5 12.0586 4.32843 12.0586 3.5C12.0586 2.67157 11.387 2 10.5586 2C9.73017 2 9.05859 2.67157 9.05859 3.5C9.05859 4.32843 9.73017 5 10.5586 5Z"
            fill="white"
          />
          <path
            d="M14.5586 5C15.387 5 16.0586 4.32843 16.0586 3.5C16.0586 2.67157 15.387 2 14.5586 2C13.7302 2 13.0586 2.67157 13.0586 3.5C13.0586 4.32843 13.7302 5 14.5586 5Z"
            fill="white"
          />
          <path d="M57.0586 22H18.0586V88H57.0586V22Z" fill="#E6E6E6" />
          <path d="M96.0586 22H64.0586V40H96.0586V22Z" fill="#EE4961" />
          <path d="M96.0586 45H64.0586V65H96.0586V45Z" fill="#E6E6E6" />
          <path d="M96.0586 70H64.0586V88H96.0586V70Z" fill="#E6E6E6" />
          <path d="M142.059 22H103.059V51H142.059V22Z" fill="#E6E6E6" />
          <path d="M142.059 59H103.059V88H142.059V59Z" fill="#E6E6E6" />
          <path
            d="M127.059 52.8571V46.5079C127.059 44.2515 127.954 42.0875 129.548 40.4919C131.142 38.8964 133.304 38 135.559 38C137.813 38 139.975 38.8964 141.569 40.4919C143.163 42.0875 144.059 44.2515 144.059 46.5079V52.8571C144.058 53.1601 143.938 53.4506 143.724 53.6649C143.51 53.8791 143.22 53.9997 142.917 54H128.2C127.898 53.9997 127.607 53.8791 127.393 53.6649C127.179 53.4506 127.059 53.1601 127.059 52.8571Z"
            fill="#2F2E41"
          />
          <path
            d="M147.059 133.197L144.306 134L140.059 123.186L144.121 122L147.059 133.197Z"
            fill="#FACAC4"
          />
          <path
            d="M141.773 133.935L147.671 132.244L148.767 136.09L139.155 138.847C139.011 138.342 138.964 137.814 139.018 137.294C139.072 136.773 139.224 136.271 139.467 135.815C139.711 135.359 140.039 134.958 140.435 134.635C140.83 134.313 141.285 134.075 141.773 133.935Z"
            fill="#2F2E41"
          />
          <path
            d="M116.058 136H113.348L112.059 124H116.059L116.058 136Z"
            fill="#FACAC4"
          />
          <path
            d="M111.922 135H118.059V139H108.059C108.059 138.475 108.159 137.955 108.353 137.469C108.547 136.984 108.831 136.543 109.19 136.172C109.549 135.8 109.975 135.506 110.444 135.304C110.912 135.103 111.415 135 111.922 135Z"
            fill="#2F2E41"
          />
          <path
            d="M112.545 70.0763C112.755 70.3739 113.024 70.6195 113.334 70.7958C113.645 70.9722 113.988 71.075 114.34 71.0971C114.692 71.1191 115.045 71.0598 115.373 70.9234C115.7 70.7869 115.996 70.5767 116.238 70.3074L124.059 74L123.347 69.382L116.03 66.5834C115.572 66.1863 114.991 65.9793 114.398 66.0016C113.804 66.024 113.239 66.2741 112.809 66.7047C112.38 67.1352 112.116 67.7163 112.067 68.3377C112.018 68.9591 112.188 69.5777 112.545 70.0763H112.545Z"
            fill="#FACAC4"
          />
          <path
            d="M129.556 97C127.054 97 124.463 96.6254 122.286 95.5113C121.13 94.9316 120.106 94.1184 119.277 93.1225C118.448 92.1267 117.833 90.9697 117.469 89.7239C116.335 86.1139 117.765 82.5673 119.149 79.1377C120.006 77.0124 120.815 75.0051 121.026 73.0265L121.099 72.324C121.426 69.1687 121.709 66.4437 123.273 65.4217C124.084 64.8919 125.174 64.8617 126.607 65.3291L140.059 69.7206L139.564 95.3824L139.482 95.4099C139.412 95.4336 134.67 97 129.556 97Z"
            fill="#2F2E41"
          />
          <path
            d="M126.156 57.4192C126.156 57.4192 132.851 55.4179 138.059 56.1684C138.059 56.1684 135.083 72.6788 136.075 78.1822C137.067 83.6857 118.84 80.3085 122.56 75.0552L123.8 68.8013C123.8 68.8013 121.32 66.2997 123.552 63.2979L126.156 57.4192Z"
            fill="#FACAC4"
          />
          <path
            d="M123.102 97L115.059 95.4319L117.867 72.9621C118.056 72.3348 122.406 58.0982 124.381 57.3279C125.816 56.8026 127.283 56.3789 128.772 56.0595L129.059 56L127.446 58.5154L121.025 74.5131L123.102 97Z"
            fill="#2F2E41"
          />
          <path
            d="M118.669 133L108.059 131.212L114.013 106.625L122.873 73L122.96 73.5971C122.967 73.6416 123.795 78.0341 136.077 76.0851L136.185 76.068L136.214 76.1744L151.059 129.214L138.969 131.252L127.942 92.5542L118.669 133Z"
            fill="#2F2E41"
          />
          <path
            d="M134.059 102L134.065 101.81C134.073 101.599 134.809 80.6068 134.563 72.1643C134.316 63.6931 137.032 56.3571 137.059 56.284L137.081 56.2251L137.141 56.2076C140.654 55.194 143.702 58.2194 143.733 58.2501L143.775 58.2933L142.775 66.6306L147.059 96.9483L134.059 102Z"
            fill="#2F2E41"
          />
          <path
            d="M133.059 53C136.372 53 139.059 50.3137 139.059 47C139.059 43.6863 136.372 41 133.059 41C129.745 41 127.059 43.6863 127.059 47C127.059 50.3137 129.745 53 133.059 53Z"
            fill="#FACAC4"
          />
          <path
            d="M125.059 46.8704C125.061 45.0489 125.735 43.3026 126.934 42.0146C128.133 40.7266 129.759 40.0021 131.455 40H132.662C134.358 40.0021 135.984 40.7266 137.183 42.0146C138.382 43.3026 139.057 45.0489 139.059 46.8704V47H136.508L135.638 44.3836L135.464 47H134.146L133.707 45.6799L133.619 47H125.059V46.8704Z"
            fill="#2F2E41"
          />
          <path
            d="M132.28 54.532C132.154 54.3625 132.078 54.1612 132.062 53.9511C132.046 53.7411 132.09 53.5308 132.19 53.3446C133.546 50.8024 135.444 46.1051 132.925 43.2082L132.744 43H140.059V53.8265L133.413 54.9823C133.346 54.994 133.278 54.9999 133.21 55C133.028 55 132.849 54.9576 132.688 54.8762C132.526 54.7948 132.387 54.6769 132.28 54.532Z"
            fill="#2F2E41"
          />
          <path
            d="M104.069 9.91631C99.1143 5.71806 93.0511 3.05643 86.6173 2.25529C80.1835 1.45415 73.6565 2.54804 67.8306 5.40387C62.0047 8.25969 57.131 12.7543 53.8025 18.3408C50.4741 23.9273 48.8345 30.3648 49.0832 36.8699C49.3319 43.375 51.4582 49.6673 55.2034 54.9811C58.9485 60.2949 64.151 64.4011 70.1779 66.8C76.2047 69.1989 82.796 69.7871 89.1497 68.493C95.5034 67.199 101.345 64.0784 105.965 59.5111L146.987 94.2686C147.611 94.7985 148.42 95.0577 149.234 94.9892C150.048 94.9207 150.803 94.5301 151.33 93.9034C151.858 93.2766 152.116 92.465 152.048 91.6472C151.98 90.8293 151.591 90.0722 150.966 89.5423L150.962 89.5384L109.94 54.7809C114.705 47.9131 116.745 39.5058 115.659 31.2067C114.573 22.9075 110.44 15.3148 104.069 9.91631ZM101.425 51.434C98.2287 55.2371 93.9816 58.0032 89.2202 59.3825C84.4589 60.7618 79.3971 60.6924 74.675 59.183C69.9529 57.6736 65.7826 54.7921 62.6914 50.9028C59.6003 47.0134 57.7271 42.291 57.3087 37.3327C56.8904 32.3744 57.9457 27.4028 60.3412 23.0467C62.7367 18.6907 66.3648 15.1457 70.7667 12.8601C75.1686 10.5746 80.1467 9.65106 85.0713 10.2064C89.9959 10.7617 94.6459 12.7709 98.4333 15.98H98.4333C100.948 18.1107 103.02 20.7179 104.532 23.6528C106.043 26.5876 106.964 29.7927 107.241 33.0849C107.519 36.3771 107.148 39.692 106.15 42.8403C105.152 45.9887 103.546 48.9088 101.425 51.434Z"
            fill="#3F3D56"
          />
          <path
            opacity="0.3"
            d="M66.7417 54.8961C61.8785 50.8142 58.7366 45.0625 57.9396 38.7831C57.1427 32.5037 58.7492 26.1559 62.44 21C61.9553 21.4855 61.4867 21.994 61.0343 22.5254C58.8874 25.0561 57.2627 27.9825 56.2529 31.1377C55.2431 34.2929 54.8681 37.615 55.1491 40.9143C55.4301 44.2137 56.3617 47.4257 57.8908 50.3669C59.4199 53.3081 61.5164 55.921 64.0607 58.0564C66.605 60.1917 69.5472 61.8077 72.7194 62.8121C75.8916 63.8164 79.2316 64.1895 82.5488 63.91C85.8659 63.6305 89.0952 62.7038 92.0523 61.183C95.0094 59.6621 97.6364 57.5769 99.7833 55.0462C100.235 54.5141 100.66 53.9691 101.059 53.4114C96.5697 57.8974 90.5489 60.5404 84.1915 60.8154C77.8341 61.0905 71.6053 58.9775 66.7417 54.8961Z"
            fill="black"
          />
          <path
            d="M135.051 80.9262C135.417 81.0158 135.798 81.0239 136.167 80.9501C136.537 80.8762 136.885 80.7222 137.189 80.4988C137.492 80.2754 137.743 79.9881 137.924 79.6571C138.105 79.3261 138.211 78.9595 138.234 78.5831L147.059 76.131L143.472 73L135.642 75.8086C135.008 75.8104 134.396 76.0452 133.923 76.4683C133.45 76.8915 133.149 77.4736 133.076 78.1044C133.003 78.7352 133.164 79.3709 133.528 79.891C133.892 80.411 134.433 80.7794 135.051 80.9262Z"
            fill="#FACAC4"
          />
          <path
            d="M138.582 80L138.059 74.9801L145.245 70.9783L140.56 65.2539L141.324 58.8235L143.228 58L143.287 58.0768C144.185 59.2419 152.059 69.4956 152.059 70.7631C152.059 72.065 150.552 75.8881 148.492 76.9281C146.504 77.9312 139.039 79.8809 138.722 79.9636L138.582 80Z"
            fill="#2F2E41"
          />
        </g>
        <defs>
          <clipPath id="clip0_19017_68466">
            <rect
              width="176"
              height="139"
              fill="white"
              transform="translate(0.0585938)"
            />
          </clipPath>
        </defs>
      </svg>
    </div>
    <span class="no-data-bld-txt">No Billing Plan here</span>
    <span class="data-txt"
      >{{startCreatingBillingPlan}}</span
    >
    <button (click)="createBillingPlan()" class="billing-plan-button">
      {{createBPButton}}
    </button>
  </div>
</div>
