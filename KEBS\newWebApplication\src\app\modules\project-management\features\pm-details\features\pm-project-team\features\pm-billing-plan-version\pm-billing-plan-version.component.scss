.billing-plan-version{
    height: var(--dynamicPageHeight) !important;
    position: relative;
    font-family: var(--teamFont) !important;
    padding: 20px;
    .no-data{
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        row-gap: 8px;
        position: relative;
        height: 100%;
        .no-data-bld-txt{
            font-family: var(--teamFont) !important;
            font-size: 16px;
            font-weight: 700;
            letter-spacing: 0.02em;
            text-align: center;
            color: #45546E;
        }
        .data-txt{
            font-family: var(--teamFont) !important;
            font-size: 14px;
            font-weight: 400;
            line-height: 16px;
            letter-spacing: 0.02em;
            text-align: center;
            color: #45546E;
        }
        .billing-plan-button{
            border: none;
            background: var(--teamButton);
            padding: 8px 12px 8px 12px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            cursor: pointer;
        }
    }

    .status-flag{
        padding: 2px 18px;
        position: relative;
        border-radius: 5px 2px 2px 5px;
        clip-path: polygon(0 0, 100% 0%, 64px calc(100% - 50%), 100% 100%, 0 100%);
        height: 30px;
        justify-content: center;
        align-items: center;
        display: flex;
        width: 78px;

        &.active{
            background-color: #009432  !important;
            color: #fff;
        }

        &.draft{
            background-color: #E8E9EE !important;
            color: #45546E;
        }
    }
    .list-class{
        overflow: auto;
        position: relative;
        height: 98%;
        .list{
            display: block;

            .list-content{
                display: flex;
                flex-direction: row;
                gap: 4rem;
                flex-wrap: nowrap;
                align-items: center;
            }

            .id_class{
                display: flex;
                flex-direction: column;
                row-gap: 8px;

                .flag-content{
                    position: relative;
                    left: -4px;
                }
            }
            .quote-header{
                display: flex;
                align-items: center;
                background-color: #F6F6F6;
                color: #7D838B;
                font-size: 12px;
                border-radius: 2px;
                padding-left: 3px;
            }
            .header{
                font-family: var(--teamFont) !important;
                font-size: 14px;
                font-weight: 600;
                letter-spacing: 0.02em;
                text-align: left;
                color: #1B2140;
            }
            .data-class{
                display: flex;
                gap: 1.5rem;
            }
            .content-class{
                display: flex;
                flex-direction: column;
                row-gap: 6px;
            }
            .sub-text{
                display: flex;
                gap: 8px;
                font-family: var(--teamFont);
                font-size: 12px;
                font-weight: 400;
                text-align: left;
                color: #7D838B;
                align-items: center;
            }
            .latest-data-class{
                display: flex;
                align-items: center;
                gap: 4px;
                font-family: var(--teamFont);
                font-size: 12px;
                font-weight: 400;
                text-align: left;
                color:#7D838B;
            }
        }
        .divider-class{
            margin-top: 0.8rem;
            margin-bottom: 0.8rem;
            border-bottom: 1px solid #E8E9EE;
        }
    }
    .edit-class{
        cursor: pointer;
    }
    ::ng-deep .mat-divider.mat-divider-inset {
        margin-top: 20px !important;
        margin-bottom: 20px !important;
    }

    .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 12px;
        
        .image {
          width: 64px; /* Width is 8% */
          height: 64px; /* Height = 8% * (9/16) */
        }
        
        
        .loading-wrapper {
          display: flex;
          vertical-align: center;
          justify-content: center;
          align-items: center;
        }
        
        .loading {
          color: rgba(0, 0, 0, 0.3);
          font-size: 16px;
        }
        
        .loading::before {
          content: "Loading...";
          position: absolute;
          overflow: hidden;
          max-width: 7em;
          white-space: nowrap;
          background: linear-gradient(270deg,
                    var(--billingAdviceField) 0%,
                    var(--billingAdviceField) 105.29%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          animation: loading 6s linear infinite;
        }
        
        @keyframes loading {
          0% {
            max-width: 0;
          }}
        }
}

