import { Component, HostListener, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import moment from 'moment';
import { PmMasterService } from 'src/app/modules/project-management/services/pm-master.service';
import { ToasterMessageService } from 'src/app/modules/project-management/shared-lazy-loaded/components/toaster-message/toaster-message.service';
import { UtilityService } from 'src/app/services/utility/utility.service';
import * as _ from 'underscore';
import { SubSink } from 'subsink';
import { ResourceLoadingServiceService } from 'src/app/modules/project-management/features/pm-resource-loading/services/resource-loading-service.service';
import { PmInternalStakeholderService } from '../project-internal-stakeholders/services/pm-internal-stakeholder.service';


@Component({
  selector: 'app-pm-billing-plan-version',
  templateUrl: './pm-billing-plan-version.component.html',
  styleUrls: ['./pm-billing-plan-version.component.scss']
})
export class PmBillingPlanVersionComponent implements OnInit {

  isComponentLoading:boolean = false;
  itemId:any;
  projectId:any;
  retrieveMessages: any;
  formConfig: any;
  subs = new SubSink();
  versionList: any=[];
  billingVersionList: any = []
  quote_id_list: any=[]
  projectData: any;
  intializeBillingPlan:boolean=false;
  textIntializeBillingPlan: string="Intializing Billing Plan..."
  startCreatingBillingPlan: string = "Start creating a billing plan, now."
  createBPButton: string="Create Billing Plan"
  deliverableList: any;
  loadingGif: any;
  constructor(private _router: Router, private _masterService: PmMasterService,
    private _utilityService: UtilityService,
    private _toasterService: ToasterMessageService,
    private _resourceService: ResourceLoadingServiceService,
    public PmInternalStakeholderService: PmInternalStakeholderService) { }

  async ngOnInit() {
    this.calculateDynamicStyle();
    this.isComponentLoading = true;
    this.itemId = parseInt(this._router.url.split('/')[5]);
    this.projectId = parseInt(this._router.url.split('/')[3]);
  
    await this.initializeFormConfigData();
    await this.retrieveData();
    await this.getBillingPlanVersionHistory();

   
    this.isComponentLoading = false;
  }

  @HostListener('window:resize')
  onResize() {
    this.calculateDynamicStyle();
  }

  /**
   * @description For Calculating Dynamic Height
   */
  calculateDynamicStyle(){
   
    let pageHeight = window.innerHeight - 197 + 'px'
    document.documentElement.style.setProperty(
      '--dynamicPageHeight',
      pageHeight
    )
  }

  /**
   * @description Creating Billing Plan
   */
  async createBillingPlan()
  {
    this.intializeBillingPlan =true;
    for(let quoteId of this.quote_id_list)
    {
        let data: any = await this.getInitialLoadDeliverableData(quoteId)

        let deliverableList = this.formatPositionDeliverableList(data);

        console.log(deliverableList)

        for(let deliverable of deliverableList)
        {
            for(let data of deliverable['position_list'])
            {
                data['project_id'] = this.projectId;
                data['project_item_id'] = this.itemId;
                data['quote_id'] = quoteId;
                data['version_id']=1;
                data['status_id']=1;
                data['action_id']= 2;
                data['position_id'] = data['position']
            }
        }
    
        let data_payload = {
          data: deliverableList,
          project_id: this.projectId,
          project_item_id: this.itemId,
          quote_id:quoteId
        }
  
        await this.savePositionDeliverable(data_payload)
        
    }
    

    await this.getBillingPlanVersionHistory()

    this.intializeBillingPlan=false
  }


  openBillingPlan(quoteId){
    this._router.navigateByUrl(`main/project-management/${this.projectId}/${this.itemId}/${quoteId}/fixed/billing_plan`);
  }

  /**
   * @description initializing Form Config Master data
   */
  async initializeFormConfigData() {
    await this._masterService.getPMFormCustomizeConfigV().then((res: any) => {
      if (res) {
        this.formConfig = res;
      }
    });

    const primaLoading = _.findWhere(this.formConfig, {
      type: "resource-loading",
      field_name: "loading-gif",
      is_active: true,
    });
    this.loadingGif = primaLoading ? primaLoading["LOADING-GIF"] : null;

    const text = _.findWhere(this.formConfig, {
      type: "resource-loading",
      field_name: "text-content",
      is_active: true,
    });
    this.textIntializeBillingPlan = text ? text["textIntializeBillingPlan"] ? "Intializing Billing Plan..." : "Intializing Billing Plan...": "Intializing Billing Plan..."
    this.startCreatingBillingPlan = text ? text["startCreatingBillingPlan"] ? "Start creating a billing plan, now." : "Start creating a billing plan, now.": "Start creating a billing plan, now."
    this.createBPButton = text ? text['createBPButton'] ? "Create Billing Plan" : "Create Billing Plan" :"Create Billing Plan"
    this.retrieveMessages = _.where(this.formConfig, {
      type: 'resource-loading',
      field_name: 'messages',
      is_active: true,
    });

    const retrieveStyles = _.where(this.formConfig, {
      type: 'project-theme',
      field_name: 'styles',
      is_active: true,
    });
    let button =
      retrieveStyles.length > 0
        ? retrieveStyles[0].data.button_color
          ? retrieveStyles[0].data.button_color
          : '#90ee90'
        : '#90ee90';
    
    document.documentElement.style.setProperty('--teamButton', button);
    let fontStyle =
      retrieveStyles.length > 0
        ? retrieveStyles[0].data.font_style
          ? retrieveStyles[0].data.font_style
          : 'Roboto'
        : 'Roboto';
    document.documentElement.style.setProperty('--teamFont', fontStyle);
    let balanceConfig = _.where(this.formConfig, {
      type: 'resource-loading',
      field_name: 'balance-check'
    });

    let retrieveDecimalPlaces = _.where(this.formConfig, {
      type: 'billing-plan',
      field_name: 'decimal_places',
      is_active: true,
    });
  }

  getFormattedDate(date: any): string {
    return moment(date).format('DD-MMM-YYYY, h:mm A');
  }


  getBillingPlanVersionHistory(){
    return new Promise((resolve,reject) => {
      this.subs.sink = this._resourceService.getFixedResourceLoadingHeaderVersions(this.projectId,this.itemId).subscribe(
         (res:any) => {
          
          if(res && res['messType'] == 'S' && res['data'] && res['data']['data'].length > 0) {
             this.versionList = res['data']['data'] ? res['data']['data'] : []

            for(let version of this.versionList)
            {
              let val = {
                billing_status: version['is_edit_allowed'] ? 1: 0,
                billing_id: version['id'],
                billing_title: version['action_type']+" "+'V'+version['forecast_version_id']+"."+version['save_version_id'],
                created_on:version['created_on'],
                created_by:version['created_by_name'],
                is_edit_allowed: version['is_edit_allowed'],
                deliverable_id: version['deliverable_id'],
                deliverable_name: version['deliverable_name'],
                quote_id: version['quote_id'],
                milestone_id: version['milestone_id']
              }

              this.billingVersionList.push(val)
            }
             resolve(res['data'])
          } 
          else{
            resolve([]);
          }
        },
        (err) => {
          this._toasterService.showError('Error in Fetching Position Details');
          console.log(err);
          resolve([]);
        }
      );
    });
  }


  async retrieveData(){

    await this.getProjectDetails();

    await this.PmInternalStakeholderService.getProjectFinnacialData(this.itemId).then(async (res) => {
      if (res['messType'] == 'S') 
        {
        if (res['data'].length > 0) 
          {
          let project_has_quote = 0;
          for (let i = 0; i < res['data'].length; i++) 
          {
            if (res['data'][i].quote_id != null) 
            {
              project_has_quote = 1;
              this.quote_id_list.push(res['data'][i].quote_id);
            }
          }
          
        }
      } 
    });
  }


  getProjectDetails() {
    return new Promise((resolve,reject) => {
      this.subs.sink = this._resourceService.getProjectQuote(this.projectId,this.itemId).subscribe(
         (res:any) => {
          if(res && res['messType'] == 'S' && res['data'] && res['data'].length > 0) {
             this.projectData = res['data'][0] ? res['data'][0] : null;
          } 
          else{
            this.projectData = null;
          }
          resolve(true)
        },
        (err) => {
          this._toasterService.showError('Error in Fetching Project Details');
          console.log(err);
          resolve(null);
        }
      );
    });
  }

  getInitialLoadDeliverableData(quoteId) {
    let startDate = this.projectData?.planned_start_date;
    let endDate = this.projectData?.planned_end_date
    return new Promise((resolve,reject) => {
      this.subs.sink = this._resourceService.getInitialLoadQuoteData(startDate,endDate,quoteId).subscribe(
         (res:any) => {
          if(res && res['messType'] == 'S' && res['data'] && res['data'].length > 0) {
             resolve(res['data'])
          } 
          else{
            resolve([])
          }
        },
        (err) => {
          this._toasterService.showError('Error in Importing Quote data');
          console.log(err);
          resolve([]);
        }
      );
    });
  }


   /**
   * @description Formatting the initial load data
   */
   formatPositionDeliverableList(data){

      
    let deliverables = [];

  
    let deliverable_ids = _.uniq(_.pluck(data,"milestone_id"))

    for(let deliverable of deliverable_ids)
    {

        let result = _.where(data,{milestone_id: deliverable})

        if(result.length>0)
        {
            let planned_quantity =0;
            for(let d of result)
            {
                planned_quantity += d['total_hrs']
            }
            let val = {
              "milestone_id": result[0]['milestone_id'],
              "name": result[0]['milestone_name'],
              "start_date": result[0]['milestone_start_date'],
              "end_date": result[0]['milestone_end_date'],
              "planned_quantity": planned_quantity,
              "position_list": result,
            }

            deliverables.push(val);
        }


    }


    return deliverables
    
  }

  savePositionDeliverable(data_payload)
  { 
    return new Promise((resolve,reject) => {
      this.subs.sink = this._resourceService.getSavePositionDeliverable(data_payload).subscribe((res:any) => 
      {
          if(res && res['messType'] == 'S' && res['data'] && res['data'].length > 0) 
          {
            resolve(res['data'])
          } 
          else{
            resolve([])
          }
        },
        (err) => {
          this._toasterService.showError('Error in Importing Quote data');
          console.log(err);
          resolve([]);
        }
      );
    });

  }
}
