import { Component, HostListener, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { PmMasterService } from 'src/app/modules/project-management/services/pm-master.service';
import * as _ from 'underscore';
import { PmAddExternalMemberComponent } from '../pm-add-external-member/pm-add-external-member.component';
import { Router } from '@angular/router';
import { SubSink } from 'subsink';
import { PmExternalStakeholderServiceService } from '../../services/pm-external-stakeholder-service.service'
import moment from 'moment';
import Swal from 'sweetalert2';
import { ToastrService } from 'ngx-toastr';
import { PmAuthorizationService } from 'src/app/modules/project-management/services/pm-authorization.service';
import { ToasterMessageService } from 'src/app/modules/project-management/shared-lazy-loaded/components/toaster-message/toaster-message.service';

export interface Column {
  keyName: string;
  colName: string;
  isActive: boolean;
  sortOrder: string;
  width: number;
  type: string;
}

@Component({
  selector: 'app-pm-external-stakeholder-landing',
  templateUrl: './pm-external-stakeholder-landing.component.html',
  styleUrls: ['./pm-external-stakeholder-landing.component.scss'],
})
export class PmExternalStakeholderLandingComponent implements OnInit {
  colList: Array<Column> = [
    {
      keyName: 'associate_id',
      colName: 'Aid',
      isActive: true,
      sortOrder: 'I',
      width: 8,
      type: 'text',
    },
    {
      keyName: 'name',
      colName: 'Name',
      isActive: true,
      sortOrder: 'I',
      width: 12,
      type: 'textImg',
    },
    {
      keyName: 'email_id',
      colName: 'Email',
      isActive: true,
      sortOrder: 'I',
      width: 11,
      type: 'text',
    },
    {
      keyName: 'role',
      colName: 'Role',
      isActive: true,
      sortOrder: 'I',
      width: 10,
      type: 'text',
    },
    {
      keyName: 'show_start_date',
      colName: 'Start Date',
      isActive: true,
      sortOrder: 'I',
      width: 8,
      type: 'text',
    },
    {
      keyName: 'show_end_date',
      colName: 'End Date',
      isActive: true,
      sortOrder: 'I',
      width: 10,
      type: 'text',
    },
    {
      keyName: 'status',
      colName: 'Status',
      isActive: true,
      sortOrder: 'I',
      width: 9,
      type: 'status',
    },
    {
      keyName: 'action',
      colName: 'Action',
      isActive: true,
      sortOrder: 'I',
      width: 6,
      type: 'action',
    }
  ];
  dataList: any = [];
  formConfig: any = [];
  button: any;
  noDataImage:string;
  data:any;
  itemId:number;
  projectId: number;
  enableNoData: boolean = false;
  loaderComponent:boolean = false;
  statusList:any = [];
  externalStakeholderAccess: boolean=false;
  private subs = new SubSink();
  constructor(private _pmMasterService: PmMasterService,
    public dialog: MatDialog, 
    private _router: Router, 
    private _pmExternalService : PmExternalStakeholderServiceService,
    private _toaster: ToasterMessageService,
    private authService: PmAuthorizationService) {}

  async ngOnInit() {
    this.loaderComponent = true;
    this.itemId = parseInt(this._router.url.split('/')[5]);
    this.projectId = parseInt(this._router.url.split('/')[3]);

    await this.authService.getReadWriteAccess(this.projectId, this.itemId).then(async(res) => {
      if (res) {
        this.externalStakeholderAccess = await this.authService.getProjectWiseObjectAccess(this.projectId, this.itemId, 84)
        console.log("External Stakeholder Access", this.externalStakeholderAccess)
      }
    })

    this.calculateDynamicStyle();
    await this.getCustomConfig();
    console.log('Form Config:',this.formConfig);
    const retrieveStyles2 = _.where(this.formConfig, { type: "project-theme", field_name: "styles", is_active: true });
    console.log('Styles:',retrieveStyles2) 
    if(retrieveStyles2.length > 0){
        this.button = retrieveStyles2[0].data.button_color ? retrieveStyles2[0].data.button_color : "#90ee90";
        document.documentElement.style.setProperty('--themeButton', this.button);
        this.noDataImage = retrieveStyles2[0].data.no_data_image2 ? retrieveStyles2[0].data.no_data_image2 : "https://assets.kebs.app/images/no_data_found.png";
      }
    console.log('Status List:',this._pmMasterService.status_list);
    this.dataList = await  this.getExternalStakeholders();
    this.loaderComponent = false;
  }

  @HostListener('window:resize')
  onResize() {
    this.calculateDynamicStyle();
  }

  calculateDynamicStyle(){
    let width = window.innerWidth - 142 + 'px'
    document.documentElement.style.setProperty(
      '--dynamicWidth',
      width
    )
    let tableHeight = window.innerHeight - 276 + 'px'
    document.documentElement.style.setProperty(
      '--dynamicHeight',
      tableHeight
    )
  }

  addExternalMember(){
    if(this.externalStakeholderAccess)
    {
      let dialogRef = this.dialog.open(PmAddExternalMemberComponent, {
        disableClose: true,
        data: {
          mode: 'Add',
          data: 'Data Called !',
          color: this.button
        },
      });

      dialogRef.afterClosed().subscribe(async (result) => {
        if (result['messType'] == "S") {
          this.dataList = await  this.getExternalStakeholders();
        }
      })
    }
    else
    {
      this._toaster.showWarning("Access restricted", 3000)  
    }
  }

  /**
   * @description get project external stakeholders
   */
  getExternalStakeholders() : Promise<any>{
    let params ={
      project_id : this.projectId,
      item_id : this.itemId
    }
    return new Promise((resolve) => {
      this.subs.sink = this._pmExternalService.getExternalStakeholdersList(params).subscribe(
        (res: any) => {
          if (res.messType == 'S') {
            if(res.data.length == 0){
              this.enableNoData = true;
            }
            else{
              this.enableNoData = false;
            }
            resolve(res.data);
          } else {
            this.enableNoData = true;
            resolve(null)
          }
        },
        (err) => {
          this.enableNoData = true;
          resolve(null)
          console.log(err);
        }
      );
    });
  }

  /**
   * @description get Status 
   */
  getStatus(startDate,endDate){
    let status
    let currentDate = moment().format('YYYY-MM-DD');
    startDate = moment(startDate).format('YYYY-MM-DD');
    endDate = moment(endDate).format('YYYY-MM-DD');
    if(currentDate >= startDate && currentDate <= endDate){
      status = 'Assigned'
    }
    else if(currentDate > endDate){
      status ='Allocation Completed'
    }
    else {
      status = 'Allocation Reserved'
    }
    return status
  }

  /**
   * @description Remove Member
  */
  removeMember(data: any) {
    if(this.externalStakeholderAccess)
    {
      // console.log(data)

      // let currentDate = moment().format('YYYY-MM-DD')
      // if (moment(data['end_date']).format('YYYY-MM-DD') < currentDate) {
      //   this._toaster.showWarning("Can't demobilise! Allocation period has already ended!",3000);
      // }
      // else if (moment(data['start_date']).format('YYYY-MM-DD') > currentDate) {
        Swal.fire({
          title: 'Alert Message',
          html: `
            <div style="background-color: #FFFFFF;border: 2px solid white; padding: 10px;">
              <h2>Do you want to continue with Stakeholder off-boarding?
              </h2>
            </div>
          `,
          showCancelButton: true,
          confirmButtonText: 'OK',
          cancelButtonText: 'Cancel',
          allowOutsideClick: false,
          allowEscapeKey: false,
        }).then((result) => {
          if (result.isConfirmed) {
            let params = {
              project_id : this.projectId,
              item_id : this.itemId,
              id: data.id,
            }
            return new Promise((resolve, reject) => {
              this.subs.sink = this._pmExternalService.removeExternalStakeholder(params).subscribe(
                async (res: any) => {
                  if (res.messType == 'S') {
                    
                    this._toaster.showSuccess("Stakeholder deleted!",3000);

                    this.dataList = await  this.getExternalStakeholders();
                    resolve(res.data);
                  }
                  else {
                    this._toaster.showError("Failed to Remove Stakeholder")
                    resolve(null);
                  }
                },
                (err) => {
                  this._toaster.showError("Failed to Remove Stakeholder")
                  resolve(null);
                }
              );
            });
          }
        });
      // }
      // else {
      //   Swal.fire({
      //     icon: 'warning',
      //     html: `
      //       <div">
      //         <h1>Do you want to continue with employee off-boarding?
      //         </h1>
      //       </div>
      //     `,
      //     customClass: { popup: 'custom-popup' },
      //     backdrop: false,

      //     showCancelButton: true,
      //     confirmButtonText: 'OK',
      //     cancelButtonText: 'Cancel',
      //     allowOutsideClick: false,
      //     allowEscapeKey: false,


      //   }).then((result) => {
      //     if (result.isConfirmed) {
      //       let params = {
      //         project_id : this.projectId,
      //         item_id : this.itemId,
      //         id: data.id,
      //       }
      //       return new Promise((resolve, reject) => {
      //         this.subs.sink = this._pmExternalService.removeExternalStakeholder(params).subscribe(
      //           async (res: any) => {
      //             if (res.messType == 'S') {
                    
      //               this._toaster.showSuccess("Stakeholder deleted!",3000);

      //               this.dataList = await  this.getExternalStakeholders();
      //               resolve(res.data);
      //             }
      //             else {
      //               this._toaster.showError("Failed to Remove Stakeholder")
      //               resolve(null);
      //             }
      //           },
      //           (err) => {
      //             this._toaster.showError("Failed to Remove Stakeholder")
      //             resolve(null);
      //           }
      //         );
      //       });
      //     }
      //   });
      // }
    }
    else
    {
      this._toaster.showWarning("Access restricted", 3000)  
    }


  }

  /**
   * @description Getting Form Customization values
   */
  getCustomConfig(){
    this._pmMasterService.getPMFormCustomizeConfigV().then((res: any) => {
      if (res) {
        this.formConfig = res;
      }
    });
  }

  /**
   * @description Edit the Member
   */
  editMember(data: any) {
    if(this.externalStakeholderAccess)
    {
      const dialogRef = this.dialog.open(PmAddExternalMemberComponent, {
        disableClose: false,
        data: {
          mode: 'Edit',
          data: data,
          color: this.button
        },
      });

      dialogRef.afterClosed().subscribe(async (result) => {
        if (result['messType'] == "S") {
          this.dataList = await  this.getExternalStakeholders();
        }
      })
    }
    else
    {
      this._toaster.showWarning("Access restricted", 3000)
    }
  }

  ngOnDestroy () {
    this.subs.unsubscribe();
  }

}
