<div class="allocate-member-style">
  <div class="loader-container" *ngIf="!isComponentLoaded">
    <div class="custom-loader"></div>
  </div>
  <div class="header-class" *ngIf="isComponentLoaded">
    <div class="header-start">
      <div class="back-icon" (click)="routeTeamMember()" [ngStyle]="{ 'pointer-events': saveDisabled ? 'none' : 'all' }">
        <mat-icon class="icn-class">navigate_before</mat-icon>
      </div>
      <span class="header-text" *ngIf="this.mode == 'Create'">{{
        "allocate_resource_name"
          | checkLabel : this.formConfig : "add-member" : "Allocate Resource"
      }}</span>
      <span class="header-text" *ngIf="this.mode == 'Edit'">{{
        "allocate_resource_name_edit"
          | checkLabel : this.formConfig : "add-member" : "Allocate Resource"
      }}</span>
      <span class="header-text" *ngIf="this.mode == 'Duplicate'">{{
        "allocate_resource_name_duplicate"
          | checkLabel : this.formConfig : "add-member" : "Allocate Resource"
      }}</span>
    </div>
    <div class="header-end">
      <button class="btn-class" [ngStyle]="{ 'pointer-events': saveDisabled ? 'none' : 'all' }" *ngIf="!demobilizeFlag">
        <span class="btn-txt" *ngIf="!saveDisabled" (click)="addMember()"
          >Allocate</span
        >
        <div *ngIf="saveDisabled" class="custom-save-loader"></div>
      </button>
    </div>
  </div>
  <div class="d-flex main-body-class" *ngIf="isComponentLoaded">
    <div class="section-header-class">
      <div *ngFor="let section of sectionHeaderList; let sectionIndex = index">
        <!-- <div
          [innerHtml]="element?.question"
          class="pl-3 expanded-question"
        ></div> -->
        <div class="section-class" *ngIf="section.sectionVisible">
          <div
            class="section-sidebar"
            (click)="scrollToSection(section.sectionId)"
            [ngStyle]="{
              background: section.sectionId == sectionId ? 'var(--selected_shade)' : '#fff'
            }"
          >
            <div class="circle"></div>
            <span>{{ section.sectionName }}</span>
          </div>
          <div
            class="divider-class"
            *ngIf="sectionIndex != sectionHeaderList.length - 1"
          >
            <mat-divider class="divider-style" [vertical]="true"></mat-divider>
          </div>
        </div>
      </div>
    </div>
    <div class="field-body-class" #content>
      <form [formGroup]="addMemberFormGroup">
        <div
          *ngFor="let section of sectionHeaderList"
          [id]="section.sectionId"
          class="section"
        >
          <div
            class="form-section"
            (mouseenter)="sectionVisited(section.sectionId)"
          >
            <ng-container *ngIf="section.sectionId == 'allocation'">
              <span class="section-header-text">{{ section.sectionName }}</span>
              <div class="row">
                <div
                  class="col-3"
                  *ngIf="
                    'employee_name'
                      | checkActive : this.formConfig : 'add-member'
                  "
                >
                  <div class="content-title" *ngIf="this.mode != 'Edit'">
                    {{
                      "employee_name"
                        | checkLabel
                          : this.formConfig
                          : "add-member"
                          : " Search Employee"
                    }}
                    <span
                      class="required-star"
                      *ngIf="
                        'employee_name'
                          | checkMandatedField : this.formConfig : 'add-member'
                      "
                    >
                      &nbsp;*</span
                    >
                    <span
                      *ngIf="
                        'employee_name'
                          | checkInfoIcon : this.formConfig : 'add-member'
                      "
                    >
                      <mat-icon
                        class="info-icon"
                        tooltip="{{
                          'employee_name'
                            | checkTooltip
                              : this.formConfig
                              : 'add-member'
                              : 'Info add'
                        }}"
                      >
                        info_outline
                      </mat-icon>
                    </span>
                  </div>
                  <div class="content-title" *ngIf="this.mode == 'Edit'">
                    Employee - Allocation ID
                    <span> &nbsp;</span>
                  </div>
                  <app-search-project-search-user
                    *ngIf="this.mode != 'Edit'"
                    class="employee_name"
                    [isAutocomplete]="true"
                    [required]="
                      'employee_name'
                        | checkMandatedField : this.formConfig : 'add-member'
                    "
                    formControlName="employee_name"
                    [label]="'Search for member'"
                  ></app-search-project-search-user>
                  <div
                    class="row font-family"
                    style="
                      font-size: 13px;
                      font-weight: 400;
                      line-height: 16px;
                      letter-spacing: 0em;
                      text-align: left;
                      color: #8b95a5;
                      margin-top: 5px;
                    "
                    *ngIf="this.mode == 'Edit'"
                  >
                {{ this.data?.name }} - {{ this.data?.isa_id }}
                  </div>
                </div>
                <div
                  class="col-3"
                  *ngIf="
                    'commercial' | checkActive : this.formConfig : 'add-member'
                  "
                >
                  <div class="content-title">
                    {{
                      "commercial"
                        | checkLabel
                          : this.formConfig
                          : "add-member"
                          : "Commercial"
                    }}
                    <span
                      class="required-star"
                      *ngIf="
                        'commercial'
                          | checkMandatedField : this.formConfig : 'add-member'
                      "
                    >
                      &nbsp;*</span
                    >
                    <span
                      *ngIf="
                        'commercial'
                          | checkInfoIcon : this.formConfig : 'add-member'
                      "
                    >
                      <mat-icon
                        class="info-icon"
                        tooltip="{{
                          'commercial'
                            | checkTooltip
                              : this.formConfig
                              : 'add-member'
                              : 'Info add'
                        }}"
                      >
                        info_outline
                      </mat-icon>
                    </span>
                  </div>
                  <app-input-search-name
                  [ngClass]="{'input-field-disabled': (disableEditAccess || isLumpsumBased), 'input-field': (!disableEditAccess && !isLumpsumBased)}" 
                    [list]="display_commercial_list"
                    placeholder="Select One"
                    formControlName="isBillableWithIdentity"
                    [required]="
                      'commercial'
                        | checkMandatedField : this.formConfig : 'add-member'
                    "
                    [disabled]="disableEditAccess || isLumpsumBased"
                  >
                  </app-input-search-name>
                </div>
                <div
                  class="col-3"
                  *ngIf="(showIdentity && displaynonbillable) && 
         ('commercial_catageroies' | checkActive : this.formConfig : 'add-member')"
                >
                  <div class="content-title">
                    {{
                      "commercial_catageroies"
                        | checkLabel
                          : this.formConfig
                          : "add-member"
                          : "Category"
                    }}
                    <span
                      class="required-star"
                      *ngIf="
                        'commercial_catageroies'
                          | checkMandatedField : this.formConfig : 'add-member'
                      "
                    >
                      &nbsp;*</span
                    >
                    <span
                      *ngIf="
                        'commercial_catageroies'
                          | checkInfoIcon : this.formConfig : 'add-member'
                      "
                    >
                      <mat-icon
                        class="info-icon"
                        tooltip="{{
                          'commercial_catageroies'
                            | checkTooltip
                              : this.formConfig
                              : 'add-member'
                              : 'Info add'
                        }}"
                      >
                        info_outline
                      </mat-icon>
                    </span>
                  </div>
                  <app-input-search-name
                  [ngClass]="{'input-field-disabled': (disableEditAccess), 'input-field': (!disableEditAccess)}" 
                    [list]="commercial_catageroies_list"
                    placeholder="Select One"
                    formControlName="isBillableWithIdentityCategories"
                    [required]="
                      'commercial_catageroies'
                        | checkMandatedField : this.formConfig : 'add-member'
                    "
                    [disabled]="disableEditAccess"
                  >
                  </app-input-search-name>
                </div>
              </div>
              <div class="row align-items-center">
                <div
                  class="col-3"
                  *ngIf="
                    'start_date' | checkActive : this.formConfig : 'add-member'
                  "
                >
                  <div class="content-title">
                    {{
                      "start_date"
                        | checkLabel
                          : this.formConfig
                          : "add-member"
                          : "Start Date"
                    }}
                    <span
                      class="required-star"
                      *ngIf="
                        'start_date'
                          | checkMandatedField : this.formConfig : 'add-member'
                      "
                    >
                      &nbsp;*</span
                    >
                    <span
                      *ngIf="
                        'start_date'
                          | checkInfoIcon : this.formConfig : 'add-member'
                      "
                    >
                      <mat-icon
                        class="info-icon"
                        tooltip="{{
                          'start_date'
                            | checkTooltip
                              : this.formConfig
                              : 'add-member'
                              : 'Info add'
                        }}"
                      >
                        info_outline
                      </mat-icon>
                    </span>
                  </div>

                  <mat-form-field [ngClass]="{'input-field-disabled': (disableEditAccess), 'input-field': (!disableEditAccess)}"  appearance="outline">
                    <input
                      matInput
                      formControlName="startDate"
                      [matDatepicker]="asd"
                      [required]="
                        'start_date'
                          | checkMandatedField : this.formConfig : 'add-member'
                      "
                      name="startDate"
                      placeholder="DD-MMM-YYYY"
                      [min]="compareDateMinimum(project_start_date, DOJ)"
                      [max]="
                        compareDate(
                          addMemberFormGroup.get('endDate').value,
                          project_end_date
                        )
                      "
                      (keydown)="onKeyDownDate($event,'S')"
                      (focus)="openDatePicker('S')"
                      [disabled]="disableEditAccess"
                    />
                    <mat-datepicker-toggle
                      matSuffix
                      [for]="asd"
                      #datepickerToggle
                    ></mat-datepicker-toggle>
                    <mat-datepicker #asd></mat-datepicker>
                  </mat-form-field>
                </div>
                <div
                  class="col-3"
                  *ngIf="
                    'end_date' | checkActive : this.formConfig : 'add-member'
                  "
                >
                  <div class="content-title">
                    {{
                      "end_date"
                        | checkLabel
                          : this.formConfig
                          : "add-member"
                          : "End Date"
                    }}
                    <span
                      class="required-star"
                      *ngIf="
                        'end_date'
                          | checkMandatedField : this.formConfig : 'add-member'
                      "
                    >
                      &nbsp;*</span
                    >
                    <span
                      *ngIf="
                        'end_date'
                          | checkInfoIcon : this.formConfig : 'add-member'
                      "
                    >
                      <mat-icon
                        class="info-icon"
                        tooltip="{{
                          'end_date'
                            | checkTooltip
                              : this.formConfig
                              : 'add-member'
                              : 'Info add'
                        }}"
                      >
                        info_outline
                      </mat-icon>
                    </span>
                  </div>
                  <mat-form-field class="input-field" appearance="outline">
                    <input
                      matInput
                      [matDatepicker]="aed"
                      name="endDate"
                      [required]="
                        'end_date'
                          | checkMandatedField : this.formConfig : 'add-member'
                      "
                      formControlName="endDate"
                      placeholder="DD-MMM-YYYY"
                      [max]="getMinDate(project_end_date, e360EndDate)"
                      [min]="
                        addMemberFormGroup.get('startDate').value
                          ? addMemberFormGroup.get('startDate').value
                          : project_start_date
                      "
                      (keydown)="onKeyDownDate($event,'E')"
                      (focus)="openDatePicker('E')"
                    />
                    <mat-datepicker-toggle
                      matSuffix
                      [for]="aed"
                      #datepickerToggle
                    ></mat-datepicker-toggle>
                    <mat-datepicker #aed></mat-datepicker>
                  </mat-form-field>
                </div>
                <div
                  class="col-3"
                  *ngIf="'split' | checkActive : this.formConfig : 'add-member'"
                >
                  <div class="content-title">
                    {{
                      "split"
                        | checkLabel
                          : this.formConfig
                          : "add-member"
                          : "Split Percentage"
                    }}
                    <span
                      class="required-star"
                      *ngIf="
                        'split'
                          | checkMandatedField : this.formConfig : 'add-member'
                      "
                    >
                      &nbsp;*</span
                    >
                    <span
                      *ngIf="
                        'split' | checkInfoIcon : this.formConfig : 'add-member'
                      "
                    >
                      <mat-icon
                        class="info-icon"
                        tooltip="{{
                          'split'
                            | checkTooltip
                              : this.formConfig
                              : 'add-member'
                              : 'Info add'
                        }}"
                      >
                        info_outline
                      </mat-icon>
                    </span>
                  </div>

                  <mat-form-field appearance="outline" [ngClass]="{'input-field-disabled': (checkingProgress || allocationDisable || disableEditAccess || disableEditAccess), 'input-field': (!checkingProgress && !allocationDisable &&  !disableEditAccess)}" >
                    <input
                      matInput
                      type="text"
                      [required]="
                        'split'
                          | checkMandatedField : this.formConfig : 'add-member'
                      "
                      formControlName="split_percentage"
                      digitOnly
                      [isPercentage]="false"
                      [allowDecimal]="this.split_percentage_allow_decimal" 
                      [digitsAllowed]="hrs_value_digit"
                      [maxValue]="split_percentage_max_percentage"
                      [readonly]="checkingProgress || allocationDisable || disableEditAccess"
                      [decimalsAllowed]="hrs_decimal_digit"
                    />
                  </mat-form-field>
                </div>

                <!--------------------Commenting the Check Availability-------------->
                <!-- <div class="col-3 pt-3 mt-4" *ngIf="checkAvailabilityVisible">
                  <div *ngIf="!checkingProgress">
                    <button class="btn-animation" (mouseenter)="onMouseEnter()"
                (mouseleave)="onMouseLeave()" (click)="checkAvailability()">
                  <img [src]="imageUrl" class="img-class">Check Availability</button>
                  </div>
                  <div *ngIf="checkingProgress">
                    <button class="btn-animation-checking">
                    Checking....</button>
                  </div>
                </div>
                <div class="col-3" *ngIf="!checkAvailabilityVisible">
                  <div class="content-title">
                    {{
                      "allocated_hours"
                        | checkLabel
                          : this.formConfig
                          : "add-member"
                          : "Planned Allocated Hours"
                    }}
                    <span
                      class="required-star"
                      *ngIf="
                        'allocated_hours'
                          | checkMandatedField : this.formConfig : 'add-member'
                      "
                    >
                      &nbsp;*</span
                    >
                    <span
                      *ngIf="
                        'allocated_hours'
                          | checkInfoIcon : this.formConfig : 'add-member'
                      "
                    >
                      <mat-icon
                        class="info-icon"
                        tooltip="{{
                          'allocated_hours'
                            | checkTooltip
                              : this.formConfig
                              : 'add-member'
                              : 'Info add'
                        }}"
                      >
                        info_outline
                      </mat-icon>
                    </span>
                  </div>

                  <mat-form-field
                    appearance="outline"
                    class="input-field-disabled"
                  >
                    <input
                      matInput
                      type="text"
                      [required]="
                        'allocated_hours'
                          | checkMandatedField : this.formConfig : 'add-member'
                      "
                      formControlName="allocated_hours"
                      readonly
                    />
                  </mat-form-field>
                </div> -->
                 <!--------------------Commenting Ends------------->
              </div>

              <div class="row" *ngIf="allocatedHrsVisible">
                <div class="col-3">
                  <div class="content-title">
                    {{
                      "total_allocated_hours"
                        | checkLabel
                          : this.formConfig
                          : "add-member"
                          : "Total Allocated Hours"
                    }}
                    <span
                      class="required-star"
                      *ngIf="
                        'total_allocated_hours'
                          | checkMandatedField : this.formConfig : 'add-member'
                      "
                    >
                      &nbsp;*</span
                    >
                    <span
                      *ngIf="
                        'total_allocated_hours'
                          | checkInfoIcon : this.formConfig : 'add-member'
                      "
                    >
                      <mat-icon
                        class="info-icon"
                        tooltip="{{
                          'total_allocated_hours'
                            | checkTooltip
                              : this.formConfig
                              : 'add-member'
                              : 'Info add'
                        }}"
                      >
                        info_outline
                      </mat-icon>
                    </span>
                  </div>

                  <mat-form-field
                    appearance="outline"
                    class="input-field-disabled"
                  >
                    <input
                      matInput
                      type="text"
                      [required]="
                        'total_allocated_hours'
                          | checkMandatedField : this.formConfig : 'add-member'
                      "
                      formControlName="total_allocated_hours"
                      digitOnly
                      [isPercentage]="false"
                      [allowDecimal]="true" 
                      [digitsAllowed]="hrs_value_digit"
                      [decimalsAllowed]="hrs_decimal_digit"
                      readonly
                    />
                    <mat-icon *ngIf="checkingProgress" class="refresh-icon" tooltip="{{('renew' | checkLabel : this.formConfig: 'add-member': 'Refreshing')}}">autorenew</mat-icon>
                  </mat-form-field>
                </div>
                <div class="col-3" *ngIf="'remaining_billable_hours' | checkActive : this.formConfig : 'add-member'">
                  <div class="content-title">
                    {{
                      "remaining_billable_hours"
                        | checkLabel
                          : this.formConfig
                          : "add-member"
                          : "Remaining Billable Hours"
                    }}
                    <span
                      class="required-star"
                      *ngIf="
                        'remaining_billable_hours'
                          | checkMandatedField : this.formConfig : 'add-member'
                      "
                    >
                      &nbsp;*</span
                    >
                    <span
                      *ngIf="
                        'remaining_billable_hours'
                          | checkInfoIcon : this.formConfig : 'add-member'
                      "
                    >
                      <mat-icon
                        class="info-icon"
                        tooltip="{{
                          'remaining_billable_hours'
                            | checkTooltip
                              : this.formConfig
                              : 'add-member'
                              : 'Info add'
                        }}"
                      >
                        info_outline
                      </mat-icon>
                    </span>
                  </div>

                  <mat-form-field
                    appearance="outline"
                    class="input-field-disabled"
                  >
                    <input
                      matInput
                      type="text"
                      [required]="
                        'remaining_billable_hours'
                          | checkMandatedField : this.formConfig : 'add-member'
                      "
                      formControlName="remaining_billable_hours"
                      readonly
                    />
                  </mat-form-field>
                </div>
                <div class="col-3">
                  <div class="content-title">
                    {{
                      "allocated_hours"
                        | checkLabel
                          : this.formConfig
                          : "add-member"
                          : "Planned Allocated Hours"
                    }}
                    <span
                      class="required-star"
                      *ngIf="
                        'allocated_hours'
                          | checkMandatedField : this.formConfig : 'add-member'
                      "
                    >
                      &nbsp;*</span
                    >
                    <span
                      *ngIf="
                        'allocated_hours'
                          | checkInfoIcon : this.formConfig : 'add-member'
                      "
                    >
                      <mat-icon
                        class="info-icon"
                        tooltip="{{
                          'allocated_hours'
                            | checkTooltip
                              : this.formConfig
                              : 'add-member'
                              : 'Info add'
                        }}"
                      >
                        info_outline
                      </mat-icon>
                    </span>
                  </div>

                  <mat-form-field
                    appearance="outline"
                    class="input-field"
                    [ngClass]="{'input-field-disabled': (checkingProgress || !allocationDisable || disableEditAccess), 'input-field': (!checkingProgress && allocationDisable  && !disableEditAccess)}" 
                  >
                    <input
                      matInput
                      type="text"
                      [required]="
                        'allocated_hours'
                          | checkMandatedField : this.formConfig : 'add-member'
                      "
                      formControlName="allocated_hours"
                      digitOnly
                      [isPercentage]="false"
                      [allowDecimal]="true" 
                      [digitsAllowed]="hrs_value_digit"
                      [readonly]="(checkingProgress || !allocationDisable || disableEditAccess)"
                      [decimalsAllowed]="hrs_decimal_digit"
                    />
                  </mat-form-field>
                </div>
                <!-----------------Commenting Remaining Billable Hours Logic------->
                <!-- 
                <div class="col-3" *ngIf="'leave_hours' | checkActive : this.formConfig : 'add-member'">
                  <div class="content-title">
                    {{
                      "leave_hours"
                        | checkLabel
                          : this.formConfig
                          : "add-member"
                          : "Leave Hours"
                    }}
                    <span
                      class="required-star"
                      *ngIf="
                        'leave_hours'
                          | checkMandatedField : this.formConfig : 'add-member'
                      "
                    >
                      &nbsp;*</span
                    >
                    <span
                      *ngIf="
                        'leave_hours'
                          | checkInfoIcon : this.formConfig : 'add-member'
                      "
                    >
                      <mat-icon
                        class="info-icon"
                        tooltip="{{
                          'leave_hours'
                            | checkTooltip
                              : this.formConfig
                              : 'add-member'
                              : 'Info add'
                        }}"
                      >
                        info_outline
                      </mat-icon>
                    </span>
                  </div>

                  <mat-form-field
                    appearance="outline"
                    class="input-field-disabled"
                  >
                    <input
                      matInput
                      type="text"
                      [required]="
                        'leave_hours'
                          | checkMandatedField : this.formConfig : 'add-member'
                      "
                      formControlName="leave_hours"
                      readonly
                    />
                  </mat-form-field>
                </div>
                <div class="col-3" *ngIf="'holiday_hours' | checkActive : this.formConfig : 'add-member'">
                  <div class="content-title">
                    {{
                      "holiday_hours"
                        | checkLabel
                          : this.formConfig
                          : "add-member"
                          : "Holiday Hours"
                    }}
                    <span
                      class="required-star"
                      *ngIf="
                        'holiday_hours'
                          | checkMandatedField : this.formConfig : 'add-member'
                      "
                    >
                      &nbsp;*</span
                    >
                    <span
                      *ngIf="
                        'holiday_hours'
                          | checkInfoIcon : this.formConfig : 'add-member'
                      "
                    >
                      <mat-icon
                        class="info-icon"
                        tooltip="{{
                          'holiday_hours'
                            | checkTooltip
                              : this.formConfig
                              : 'add-member'
                              : 'Info add'
                        }}"
                      >
                        info_outline
                      </mat-icon>
                    </span>
                  </div>

                  <mat-form-field
                    appearance="outline"
                    class="input-field-disabled"
                  >
                    <input
                      matInput
                      type="text"
                      [required]="
                        'holiday_hours'
                          | checkMandatedField : this.formConfig : 'add-member'
                      "
                      formControlName="holiday_hours"
                      readonly
                    />
                  </mat-form-field>
                </div> -->
                <!-----------------Commenting Ends------------------------------------->
              </div>
              <div class="row">
                <div
                  class="col-3"
                  *ngIf="'shift' | checkActive : this.formConfig : 'add-member'"
                >
                  <div class="content-title">
                    {{
                      "shift"
                        | checkLabel : this.formConfig : "add-member" : "Shift"
                    }}
                    <span
                      class="required-star"
                      *ngIf="
                        'shift'
                          | checkMandatedField : this.formConfig : 'add-member'
                      "
                    >
                      &nbsp;*</span
                    >
                    <span
                      *ngIf="
                        'shift' | checkInfoIcon : this.formConfig : 'add-member'
                      "
                    >
                      <mat-icon
                        class="info-icon"
                        tooltip="{{
                          'shift'
                            | checkTooltip
                              : this.formConfig
                              : 'add-member'
                              : 'Info add'
                        }}"
                      >
                        info_outline
                      </mat-icon>
                    </span>
                  </div>
                  <app-input-search-name
                  [ngClass]="{'input-field-disabled': (disableEditAccess), 'shift': (!disableEditAccess)}" 
                    [list]="shift_list"
                    placeholder="Select One"
                    formControlName="shift"
                    [disabled]="disableEditAccess"
                  >
                  </app-input-search-name>
                </div>
                <div
                  class="col-3"
                  *ngIf="
                    'actual_hours'
                      | checkActive : this.formConfig : 'add-member'
                  "
                >
                  <div class="content-title">
                    {{
                      "actual_hours"
                        | checkLabel
                          : this.formConfig
                          : "add-member"
                          : "Actual Hours"
                    }}
                    <span
                      class="required-star"
                      *ngIf="
                        'actual_hours'
                          | checkMandatedField : this.formConfig : 'add-member'
                      "
                    >
                      &nbsp;*</span
                    >
                    <span
                      *ngIf="
                        'actual_hours'
                          | checkInfoIcon : this.formConfig : 'add-member'
                      "
                    >
                      <mat-icon
                        class="info-icon"
                        tooltip="{{
                          'actual_hours'
                            | checkTooltip
                              : this.formConfig
                              : 'add-member'
                              : 'Info add'
                        }}"
                      >
                        info_outline
                      </mat-icon>
                    </span>
                  </div>

                  <mat-form-field appearance="outline" [ngClass]="{'input-field-disabled': (disableEditAccess), 'input-field': (!disableEditAccess)}" >
                    <input
                      matInput
                      type="number"
                      formControlName="actual_hours"
                      placeholder="Enter here"
                      [readonly]="disableEditAccess"
                    />
                  </mat-form-field>
                </div>
              </div>
            </ng-container>
            <ng-container *ngIf="section.sectionId == 'project'">
              <span class="section-header-text">{{ section.sectionName }}</span>
              <div class="row">
                <div
                  class="col-3"
                  *ngIf="
                    'project_role'
                      | checkActive : this.formConfig : 'add-member'
                  "
                >
                  <div class="content-title">
                    {{
                      "project_role"
                        | checkLabel
                          : this.formConfig
                          : "add-member"
                          : "Project Role"
                    }}
                    <span
                      class="required-star"
                      *ngIf="
                        'project_role'
                          | checkMandatedField : this.formConfig : 'add-member'
                      "
                    >
                      &nbsp;*</span
                    >
                    <span
                      *ngIf="
                        'project_role'
                          | checkInfoIcon : this.formConfig : 'add-member'
                      "
                    >
                      <mat-icon
                        class="info-icon"
                        tooltip="{{
                          'project_role'
                            | checkTooltip
                              : this.formConfig
                              : 'add-member'
                              : 'Info add'
                        }}"
                      >
                        info_outline
                      </mat-icon>
                    </span>
                  </div>
                  <app-input-search-name
                    class="project_role"
                    [disabled]="this.mode == 'Edit'"
                    [list]="project_role_list"
                    placeholder="Select One"
                    [required]="
                      'project_role'
                        | checkMandatedField : this.formConfig : 'add-member'
                    "
                    [ngStyle]="{ background: project_role_color }"
                    formControlName="projectRole"
                  >
                  </app-input-search-name>
                </div>
                <div
                  class="col-3"
                  *ngIf="
                    'request_position_id'
                      | checkActive : this.formConfig : 'add-member'
                  "
                >
                  <div class="content-title">
                    {{
                      "request_position_id"
                        | checkLabel
                          : this.formConfig
                          : "add-member"
                          : "Allocation Position"
                    }}
                    <span
                      class="required-star"
                      *ngIf="
                        'request_position_id'
                          | checkMandatedField : this.formConfig : 'add-member'
                      "
                      >&nbsp;*</span
                    >
                    <span
                      *ngIf="
                        'request_position_id'
                          | checkInfoIcon : this.formConfig : 'add-member'
                      "
                    >
                      <mat-icon
                        class="info-icon"
                        tooltip="{{
                          'request_position_id'
                            | checkTooltip
                              : this.formConfig
                              : 'add-member'
                              : 'Allocation Position'
                        }}"
                      >
                        info_outline
                      </mat-icon>
                    </span>
                  </div>

                  <app-input-search-name
                  [ngClass]="{'input-field-disabled': (disableEditAccess), 'input-field': (!disableEditAccess)}" 
                    [list]="positionList"
                    placeholder="Select One"
                    formControlName="request_position_id"
                    [required]="
                      'request_position_id'
                        | checkMandatedField : this.formConfig : 'add-member'
                    "
                    [disabled]="disableEditAccess"
                  >
                  </app-input-search-name>
                </div>
                <div
                  class="col-3"
                  *ngIf="
                    'secondary_project_role'
                      | checkActive : this.formConfig : 'add-member'
                  "
                >
                  <div class="content-title">
                    {{
                      "secondary_project_rolesecondary_project_role"
                        | checkLabel
                          : this.formConfig
                          : "add-member"
                          : "
                                        Secondary Project Role"
                    }}
                    <span
                      class="required-star"
                      *ngIf="
                        'secondary_project_role'
                          | checkMandatedField : this.formConfig : 'add-member'
                      "
                    >
                      &nbsp;*</span
                    >
                    <span
                      *ngIf="
                        'secondary_project_role'
                          | checkInfoIcon : this.formConfig : 'add-member'
                      "
                    >
                      <mat-icon
                        class="info-icon"
                        tooltip="{{
                          'secondary_project_role'
                            | checkTooltip
                              : this.formConfig
                              : 'add-member'
                              : 'Info add'
                        }}"
                      >
                        info_outline
                      </mat-icon>
                    </span>
                  </div>

                  <app-multi-select-search2
                    class="secondary_project_role"
                    [disabled]="this.mode == 'Edit'"
                    [list]="secprojectRoleMasterList"
                    formControlName="secondaryProjectRole"
                    placeholder="Select"
                    [required]="
                      'secondary_project_role'
                        | checkMandatedField : this.formConfig : 'add-member'
                    "
                    [ngStyle]="{ background: project_role_color }"
                  >
                  </app-multi-select-search2>
                </div>
                <div
                  class="col-3"
                  *ngIf="
                    'work_city'
                      | checkActive : this.formConfig : 'add-member'
                  "
                >
                  <div class="content-title">
                    {{
                      "work_city"
                        | checkLabel
                          : this.formConfig
                          : "add-member"
                          : "Work City"
                    }}
                    <span
                      class="required-star"
                      *ngIf="
                        'work_city'
                          | checkMandatedField : this.formConfig : 'add-member'
                      "
                    >
                      &nbsp;*</span
                    >
                    <span
                      *ngIf="
                        'work_city'
                          | checkInfoIcon : this.formConfig : 'add-member'
                      "
                    >
                      <mat-icon
                        class="info-icon"
                        tooltip="{{
                          'work_city'
                            | checkTooltip
                              : this.formConfig
                              : 'add-member'
                              : 'Info add'
                        }}"
                      >
                        info_outline
                      </mat-icon>
                    </span>
                  </div>
                  <!-- <app-input-search-huge-input
                  class="inputSearch"
                  [label]="'Select One '"
                  [optionLabel]="['city_id','city_name']"
                  [apiUri]="
                    '/api/rmg/masterData/workCity'
                  " 
                  formControlName="work_city_value"
                >
                </app-input-search-huge-input> -->
                <app-single-select-chip
                [ngClass]="{'chip-class-disabled': (disableEditAccess), 'chip-class': (!disableEditAccess)}" 
                  [masterData]="workCityList"
                  [selectedValue]="addMemberFormGroup.get('work_city').value"
                  [placeholder]="'Select One'"
                  [displayClose]="false"
                  (onValueChange)="onCustomSelectChanges($event)"
                  [disabled]="disableEditAccess"
                ></app-single-select-chip>
                </div>
                <div
                  class="col-3"
                  *ngIf="
                    'work_premisis'
                      | checkActive : this.formConfig : 'add-member'
                  "
                >
                  <div class="content-title">
                    {{
                      "work_premisis"
                        | checkLabel
                          : this.formConfig
                          : "add-member"
                          : "Work Premisis"
                    }}
                    <span
                      class="required-star"
                      *ngIf="
                        'work_premisis'
                          | checkMandatedField : this.formConfig : 'add-member'
                      "
                    >
                      &nbsp;*</span
                    >
                    <span
                      *ngIf="
                        'work_premisis'
                          | checkInfoIcon : this.formConfig : 'add-member'
                      "
                    >
                      <mat-icon
                        class="info-icon"
                        tooltip="{{
                          'work_premisis'
                            | checkTooltip
                              : this.formConfig
                              : 'add-member'
                              : 'Info add'
                        }}"
                      >
                        info_outline
                      </mat-icon>
                    </span>
                  </div>
                  <app-input-search-name
                    [ngClass]="{'input-field-disabled': (disableEditAccess), 'input-field': (!disableEditAccess)}" 
                    class="work_premisis"
                    [list]="workPremisisMasterList"
                    placeholder="Select One"
                    [required]="
                      'work_premisis'
                        | checkMandatedField : this.formConfig : 'add-member'
                    "
                    formControlName="work_premisis"
                    [disabled]="disableEditAccess"
                  >
                  </app-input-search-name>
                </div>
                <div
                  class="col-3"
                  *ngIf="
                    is_reports_to &&
                    ('reports_to'
                      | checkActive : this.formConfig : 'add-member')
                  "
                >
                  <div class="content-title">
                    {{
                      "reports_to"
                        | checkLabel
                          : this.formConfig
                          : "add-member"
                          : "Reports To"
                    }}
                    <span
                      class="required-star"
                      *ngIf="
                        'reports_to'
                          | checkMandatedField : this.formConfig : 'add-member'
                      "
                    >
                      &nbsp;*</span
                    >
                    <span
                      *ngIf="
                        'reports_to'
                          | checkInfoIcon : this.formConfig : 'add-member'
                      "
                    >
                      <mat-icon
                        class="info-icon"
                        tooltip="{{
                          'reports_to'
                            | checkTooltip
                              : this.formConfig
                              : 'add-member'
                              : 'Info add'
                        }}"
                      >
                        info_outline
                      </mat-icon>
                    </span>
                  </div>
                  <app-input-search-name
                    class="reports_to"
                    
                    [list]="reports_to_list"
                    placeholder="Select One"
                    [required]="
                      'reports_to'
                        | checkMandatedField : this.formConfig : 'add-member'
                    "
                    formControlName="reportsTo"
                  >
                  </app-input-search-name>
                </div>
                <div
                  class="col-3"
                  *ngIf="
                    ('travel_type'
                      | checkActive : this.formConfig : 'add-member')
                  "
                >
                  <div class="content-title">
                    {{
                      "travel_type"
                        | checkLabel
                          : this.formConfig
                          : "add-member"
                          : "Travel Type"
                    }}
                    <span
                      class="required-star"
                      *ngIf="
                        'travel_type'
                          | checkMandatedField : this.formConfig : 'add-member'
                      "
                    >
                      &nbsp;*</span
                    >
                    <span
                      *ngIf="
                        'travel_type'
                          | checkInfoIcon : this.formConfig : 'add-member'
                      "
                    >
                      <mat-icon
                        class="info-icon"
                        tooltip="{{
                          'travel_type'
                            | checkTooltip
                              : this.formConfig
                              : 'add-member'
                              : 'Info add'
                        }}"
                      >
                        info_outline
                      </mat-icon>
                    </span>
                  </div>
                  <app-input-search-name
                    class="travel_type"
                    [ngClass]="{'travel_type-disabled': (disableEditAccess), 'travel_type': (!disableEditAccess)}"
                    [list]="traveltypeMasterList"
                    placeholder="Select One"
                    [required]="
                      'travel_type'
                        | checkMandatedField : this.formConfig : 'add-member'
                    "
                    [disabled]="disableEditAccess"
                    formControlName="travel_type"
                  >
                  </app-input-search-name>
                </div>
                <div
                  class="col-3"
                  *ngIf="
                    ('work_shift'
                      | checkActive : this.formConfig : 'add-member')
                  "
                >
                  <div class="content-title">
                    {{
                      "work_shift"
                        | checkLabel
                          : this.formConfig
                          : "add-member"
                          : "Work Shift"
                    }}
                    <span
                      class="required-star"
                      *ngIf="
                        'work_shift'
                          | checkMandatedField : this.formConfig : 'add-member'
                      "
                    >
                      &nbsp;*</span
                    >
                    <span
                      *ngIf="
                        'work_shift'
                          | checkInfoIcon : this.formConfig : 'add-member'
                      "
                    >
                      <mat-icon
                        class="info-icon"
                        tooltip="{{
                          'work_shift'
                            | checkTooltip
                              : this.formConfig
                              : 'add-member'
                              : 'Info add'
                        }}"
                      >
                        info_outline
                      </mat-icon>
                    </span>
                  </div>
                  <app-input-search-name
                    [list]="workShiftMasterList"
                    placeholder="Select One"
                    [required]="
                      'work_shift'
                        | checkMandatedField : this.formConfig : 'add-member'
                    "
                    class="input-field"
                    formControlName="work_shift"
                  >
                  </app-input-search-name>
                </div>
              </div>
              <!-- Check Boxes -->
              <div class="row mt-3">
                <div
                  class="col-3 billable"
                  *ngIf="
                    'billable' | checkActive : this.formConfig : 'add-member'
                  "
                >
                  <mat-checkbox
                    class="billable-name"
                    formControlName="isBillable"
                    [disabled]="disableEditAccess"
                  >
                    {{
                      "billable"
                        | checkLabel
                          : this.formConfig
                          : "add-member"
                          : "Billable"
                    }}
                  </mat-checkbox>
                </div>
                <div
                  class="col-3 is-head"
                  *ngIf="
                    is_head &&
                    ('head' | checkActive : this.formConfig : 'add-member') &&
                    ('billable' | checkActive : this.formConfig : 'add-member')
                  "
                >
                  <mat-checkbox
                    (change)="isHeadChange($event)"
                    class="is-head-name"
                    formControlName="isHead"
                  >
                    {{
                      "head"
                        | checkLabel : this.formConfig : "add-member" : "Head"
                    }}
                  </mat-checkbox>
                </div>
                <div
                  class="col-3 is-head"
                  *ngIf="
                    is_head &&
                    ('head' | checkActive : this.formConfig : 'add-member') &&
                    !('billable' | checkActive : this.formConfig : 'add-member')
                  "
                >
                  <mat-checkbox
                    (change)="isHeadChange($event)"
                    class="is-head-name"
                    formControlName="isHead"
                  >
                    {{
                      "head"
                        | checkLabel : this.formConfig : "add-member" : "Head"
                    }}
                  </mat-checkbox>
                </div>
              </div>
            </ng-container>
            <ng-container *ngIf="section.sectionId == 'quote'">
              <div
                class="row  d-block"
                *ngIf="
                  'rate-card-details'
                    | checkActive : this.formConfig : 'add-member'
                "
              >
                <div class="row d-flex">
                  <span class="section-header-text" *ngIf="!withOpportunity">{{
                    "rate-card-details"
                      | checkLabel
                        : this.formConfig
                        : "add-member"
                        : "Rate Card Details"
                  }}</span>
                  <span class="section-header-text" *ngIf="withOpportunity">{{
                    "quote-card-details"
                      | checkLabel
                        : this.formConfig
                        : "add-member"
                        : "Quote Details"
                  }}</span>

                  <span
                    *ngIf="
                      !withOpportunity &&
                      ('rate_card'
                        | checkActive : this.formConfig : 'add-member') &&
                      this.addMemberFormGroup.get('rate_position_id').value ==
                        ''
                    "
                    class="pt-1 pl-2"
                    (click)="addRateCardPosition()"
                    style="cursor: pointer"
                  >
                    <mat-icon class="smallCardIcon" toolTip="Add Rate Card"
                      >add_circle_outline</mat-icon
                    >
                  </span>

                  <span
                    *ngIf="
                      !withOpportunity &&
                      ('rate_card'
                        | checkActive : this.formConfig : 'add-member') &&
                      this.addMemberFormGroup.get('rate_position_id').value !=
                        ''
                    "
                    class="pt-1 pl-2"
                    (click)="addRateCardPosition()"
                    style="cursor: pointer"
                  >
                    <mat-icon class="smallCardIcon" toolTip="Edit Rate Card"
                      >edit</mat-icon
                    >
                  </span>
                </div>
                <div class="row">
                  <div class="col-12 pl-0">
                    <div class="row" *ngIf="withOpportunity">
                      <div
                        class="col-3"
                        *ngIf="
                          'rate_position_id'
                            | checkActive : this.formConfig : 'add-member'
                        "
                      >
                        <div class="content-title">
                          {{
                            "quote_position_id"
                              | checkLabel
                                : this.formConfig
                                : "add-member"
                                : "Quote Position"
                          }}
                          <span
                            class="required-star"
                            *ngIf="
                              'quote_position_id'
                                | checkMandatedField
                                  : this.formConfig
                                  : 'add-member'
                            "
                            >&nbsp;*</span
                          >
                          <span
                            *ngIf="
                              'quote_position_id'
                                | checkInfoIcon : this.formConfig : 'add-member'
                            "
                          >
                            <mat-icon
                              class="info-icon"
                              tooltip="{{
                                'quote_position_id'
                                  | checkTooltip
                                    : this.formConfig
                                    : 'add-member'
                                    : 'Position'
                              }}"
                            >
                              info_outline
                            </mat-icon>
                          </span>
                        </div>

                        <app-input-search-name
                          class="input-field-small"
                          [ngStyle]="{background: q2cVisible ? '#e8e9ee' : '#fff'}"
                          [list]="quote_position_list"
                          placeholder="Select One"
                          formControlName="rate_card_id"
                          [required]="
                            'quote_position_id'
                              | checkMandatedField
                                : this.formConfig
                                : 'add-member'
                          "
                          [disabled]="q2cVisible"
                        >
                        </app-input-search-name>
                      </div>
                    </div>

                    <div
                      class="row"
                      *ngIf="
                        !withOpportunity &&
                        !(
                          'rate_card'
                          | checkActive : this.formConfig : 'add-member'
                        )
                      "
                    >
                      <div
                        class="col-3"
                        *ngIf="
                          'rate_position_id'
                            | checkActive : this.formConfig : 'add-member'
                        "
                      >
                        <div class="content-title">
                          {{
                            "rate_position_id"
                              | checkLabel
                                : this.formConfig
                                : "add-member"
                                : "Rate Position"
                          }}
                          <span
                            class="required-star"
                            *ngIf="
                              'rate_position_id'
                                | checkMandatedField
                                  : this.formConfig
                                  : 'add-member'
                            "
                            >&nbsp;*</span
                          >
                          <span
                            *ngIf="
                              'rate_position_id'
                                | checkInfoIcon : this.formConfig : 'add-member'
                            "
                          >
                            <mat-icon
                              class="info-icon"
                              tooltip="{{
                                'rate_position_id'
                                  | checkTooltip
                                    : this.formConfig
                                    : 'add-member'
                                    : 'Position'
                              }}"
                            >
                              info_outline
                            </mat-icon>
                          </span>
                        </div>

                        <app-input-search-name
                          class="input-field-small"
                          [list]="rateCardList"
                          placeholder="Select One"
                          formControlName="rate_card_id"
                          [required]="
                            'rate_position_id'
                              | checkMandatedField
                                : this.formConfig
                                : 'add-member'
                          "
                        >
                        </app-input-search-name>
                      </div>
                    </div>

                    <div
                      class="row"
                      *ngIf="
                        !withOpportunity &&
                        ('rate_card'
                          | checkActive : this.formConfig : 'add-member')
                      "
                    >
                      <div
                        class="col-3"
                        *ngIf="
                          'rate_position_id'
                            | checkActive : this.formConfig : 'add-member'
                        "
                      >
                        <div class="content-title">
                          {{
                            "rate_position_id"
                              | checkLabel
                                : this.formConfig
                                : "add-member"
                                : "Rate Position"
                          }}
                          <span
                            class="required-star"
                            *ngIf="
                              'rate_position_id'
                                | checkMandatedField
                                  : this.formConfig
                                  : 'add-member'
                            "
                            >&nbsp;*</span
                          >
                          <span
                            *ngIf="
                              'rate_position_id'
                                | checkInfoIcon : this.formConfig : 'add-member'
                            "
                          >
                            <mat-icon
                              class="info-icon"
                              tooltip="{{
                                'rate_position_id'
                                  | checkTooltip
                                    : this.formConfig
                                    : 'add-member'
                                    : 'Position'
                              }}"
                            >
                              info_outline
                            </mat-icon>
                          </span>
                        </div>

                        <app-input-search-name
                          class="input-field-disabled"
                          [list]="rateCardList"
                          placeholder="Select One"
                          formControlName="rate_card_id"
                          [required]="
                            'rate_position_id'
                              | checkMandatedField
                                : this.formConfig
                                : 'add-member'
                          "
                          disabled
                        >
                        </app-input-search-name>
                      </div>
                    </div>

                    <div class="row">
                      <div
                        class="col-3"
                        *ngIf="
                          'rate_entity'
                            | checkActive : this.formConfig : 'add-member'
                        "
                      >
                        <div class="content-title">
                          {{
                            withOpportunity
                              ? ("quote_entity"
                                | checkLabel
                                  : this.formConfig
                                  : "add-member"
                                  : "Quote Entity")
                              : ("rate_entity"
                                | checkLabel
                                  : this.formConfig
                                  : "add-member"
                                  : "Rate Card Entity")
                          }}

                          <span
                            class="required-star"
                            *ngIf="
                              'rate_entity'
                                | checkMandatedField
                                  : this.formConfig
                                  : 'add-member'
                            "
                            >&nbsp;*</span
                          >
                          <span
                            *ngIf="
                              'rate_entity'
                                | checkInfoIcon : this.formConfig : 'add-member'
                            "
                          >
                            <mat-icon
                              class="info-icon"
                              tooltip="{{
                                'rate_entity'
                                  | checkTooltip
                                    : this.formConfig
                                    : 'add-member'
                                    : 'Entity'
                              }}"
                            >
                              info_outline
                            </mat-icon>
                          </span>
                        </div>

                        <app-input-search-name
                          class="input-field-disabled"
                          [list]="rateEntityList"
                          placeholder="Select One"
                          formControlName="rate_entity"
                          [required]="
                            'rate_entity'
                              | checkMandatedField
                                : this.formConfig
                                : 'add-member'
                          "
                          disabled
                        >
                        </app-input-search-name>
                      </div>
                      <div
                        class="col-3 pl-4"
                        *ngIf="
                          'rate_division'
                            | checkActive : this.formConfig : 'add-member'
                        "
                      >
                        <div class="content-title">
                          {{
                            withOpportunity
                              ? ("quote_division"
                                | checkLabel
                                  : this.formConfig
                                  : "add-member"
                                  : "Quote Division")
                              : ("rate_division"
                                | checkLabel
                                  : this.formConfig
                                  : "add-member"
                                  : "Rate Card Division")
                          }}
                          <span
                            class="required-star"
                            *ngIf="
                              'rate_division'
                                | checkMandatedField
                                  : this.formConfig
                                  : 'add-member'
                            "
                            >&nbsp;*</span
                          >
                          <span
                            *ngIf="
                              'rate_division'
                                | checkInfoIcon : this.formConfig : 'add-member'
                            "
                          >
                            <mat-icon
                              class="info-icon"
                              tooltip="{{
                                'rate_division'
                                  | checkTooltip
                                    : this.formConfig
                                    : 'add-member'
                                    : 'Division'
                              }}"
                            >
                              info_outline
                            </mat-icon>
                          </span>
                        </div>

                        <app-input-search-name
                          class="input-field-disabled"
                          [list]="rateDivisionList"
                          placeholder="Select One"
                          formControlName="rate_division"
                          disabled
                        >
                        </app-input-search-name>
                      </div>

                      <div
                        class="col-3"
                        
                        *ngIf="
                          'rate_sub_division'
                            | checkActive : this.formConfig : 'add-member'
                        "
                      >
                        <div class="content-title">
                          {{
                            withOpportunity
                              ? ("quote_sub_division"
                                | checkLabel
                                  : this.formConfig
                                  : "add-member"
                                  : "Quote Sub Division")
                              : ("rate_sub_division"
                                | checkLabel
                                  : this.formConfig
                                  : "add-member"
                                  : "Rate Card Sub Division")
                          }}
                          <span
                            class="required-star"
                            *ngIf="
                              'rate_sub_division'
                                | checkMandatedField
                                  : this.formConfig
                                  : 'add-member'
                            "
                            >&nbsp;*</span
                          >
                          <span
                            *ngIf="
                              'rate_sub_division'
                                | checkInfoIcon : this.formConfig : 'add-member'
                            "
                          >
                            <mat-icon
                              class="info-icon"
                              tooltip="{{
                                'rate_sub_division'
                                  | checkTooltip
                                    : this.formConfig
                                    : 'add-member'
                                    : 'Sub Division'
                              }}"
                            >
                              info_outline
                            </mat-icon>
                          </span>
                        </div>

                        <app-input-search-name
                          class="input-field-disabled"
                          [list]="rateSubDivisionList"
                          placeholder="Select One"
                          formControlName="rate_sub_division"
                          disabled
                        >
                        </app-input-search-name>
                      </div>

                      <div
                        class="col-3 pl-4"
                        *ngIf="
                          'rate_location'
                            | checkActive : this.formConfig : 'add-member'
                        "
                      >
                        <div class="content-title">
                          {{
                            withOpportunity
                              ? ("quote_location"
                                | checkLabel
                                  : this.formConfig
                                  : "add-member"
                                  : "Quote Location")
                              : ("rate_location"
                                | checkLabel
                                  : this.formConfig
                                  : "add-member"
                                  : "Rate Card Location")
                          }}
                          <span
                            class="required-star"
                            *ngIf="
                              'rate_location'
                                | checkMandatedField
                                  : this.formConfig
                                  : 'add-member'
                            "
                            >&nbsp;*</span
                          >
                          <span
                            *ngIf="
                              'rate_location'
                                | checkInfoIcon : this.formConfig : 'add-member'
                            "
                          >
                            <mat-icon
                              class="info-icon"
                              tooltip="{{
                                'rate_location'
                                  | checkTooltip
                                    : this.formConfig
                                    : 'add-member'
                                    : 'Location'
                              }}"
                            >
                              info_outline
                            </mat-icon>
                          </span>
                        </div>

                        <app-input-search-name
                          class="input-field-disabled"
                          [list]="rateLocationList"
                          placeholder="Select One"
                          formControlName="rate_location"
                          disabled
                        >
                        </app-input-search-name>
                      </div>
                    </div>

                    <div class="row">
                      <div
                        class="col-3"
                        *ngIf="
                          'rate_currency'
                            | checkActive : this.formConfig : 'add-member'
                        "
                      >
                        <div class="content-title">
                          {{
                            withOpportunity
                              ? ("quote_currency"
                                | checkLabel
                                  : this.formConfig
                                  : "add-member"
                                  : "Quote Currency")
                              : ("rate_currency"
                                | checkLabel
                                  : this.formConfig
                                  : "add-member"
                                  : "Rate Card Currency")
                          }}
                          <span
                            class="required-star"
                            *ngIf="
                              'rate_currency'
                                | checkMandatedField
                                  : this.formConfig
                                  : 'add-member'
                            "
                            >&nbsp;*</span
                          >
                          <span
                            *ngIf="
                              'rate_currency'
                                | checkInfoIcon : this.formConfig : 'add-member'
                            "
                          >
                            <mat-icon
                              class="info-icon"
                              tooltip="{{
                                'rate_currency'
                                  | checkTooltip
                                    : this.formConfig
                                    : 'add-member'
                                    : 'Currency'
                              }}"
                            >
                              info_outline
                            </mat-icon>
                          </span>
                        </div>

                        <app-input-search-name
                          class="input-field-disabled"
                          [list]="currencyList"
                          placeholder="Select One"
                          formControlName="rate_currency"
                          disabled
                        >
                        </app-input-search-name>
                      </div>

                      <div
                        class="col-3 pl-4"
                        *ngIf="
                        ('rate_revenue'
                        | checkActive : this.formConfig : 'add-member') && !isFinancialValuesHidden
                        "
                      >
                        <div class="content-title">
                          {{
                            withOpportunity
                              ? ("quote_revenue"
                                | checkLabel
                                  : this.formConfig
                                  : "add-member"
                                  : "Quote Revenue")
                              : ("rate_revenue"
                                | checkLabel
                                  : this.formConfig
                                  : "add-member"
                                  : "Rate Card Revenue")
                          }}
                          <span
                            class="required-star"
                            *ngIf="
                              'rate_revenue'
                                | checkMandatedField
                                  : this.formConfig
                                  : 'add-member'
                            "
                            >&nbsp;*</span
                          >
                          <span
                            *ngIf="
                              'rate_revenue'
                                | checkInfoIcon : this.formConfig : 'add-member'
                            "
                          >
                            <mat-icon
                              class="info-icon"
                              tooltip="{{
                                'rate_revenue'
                                  | checkTooltip
                                    : this.formConfig
                                    : 'add-member'
                                    : 'Revenue'
                              }}"
                            >
                              info_outline
                            </mat-icon>
                          </span>
                        </div>

                        <mat-form-field
                          appearance="outline"
                          class="input-field-disabled"
                        >
                          <input
                            matInput
                            type="text"
                            formControlName="rate_revenue"
                            placeholder="Enter here"
                            disabled="disabled"
                            readOnly="true"
                          />
                        </mat-form-field>
                      </div>

                      <div
                        class="col-3"
                        
                        *ngIf="
                        ('rate_cost'
                        | checkActive : this.formConfig : 'add-member') && !isFinancialValuesHidden
                        "
                      >
                        <div class="content-title">
                          {{
                            withOpportunity
                              ? ("quote_cost"
                                | checkLabel
                                  : this.formConfig
                                  : "add-member"
                                  : "Quote Cost")
                              : ("rate_cost"
                                | checkLabel
                                  : this.formConfig
                                  : "add-member"
                                  : "Rate Card Cost")
                          }}
                          <span
                            class="required-star"
                            *ngIf="
                              'rate_cost'
                                | checkMandatedField
                                  : this.formConfig
                                  : 'add-member'
                            "
                            >&nbsp;*</span
                          >
                          <span
                            *ngIf="
                              'rate_cost'
                                | checkInfoIcon : this.formConfig : 'add-member'
                            "
                          >
                            <mat-icon
                              class="info-icon"
                              tooltip="{{
                                'rate_cost'
                                  | checkTooltip
                                    : this.formConfig
                                    : 'add-member'
                                    : 'Cost'
                              }}"
                            >
                              info_outline
                            </mat-icon>
                          </span>
                        </div>

                        <mat-form-field
                          appearance="outline"
                          class="input-field-disabled"
                        >
                          <input
                            matInput
                            type="text"
                            formControlName="rate_cost"
                            placeholder="Enter here"
                            disabled="true"
                            readOnly="true"
                          />
                        </mat-form-field>
                      </div>

                      <div
                        class="col-3 pl-4"
                        *ngIf="
                          'rate_unit'
                            | checkActive : this.formConfig : 'add-member'
                        "
                      >
                        <div class="content-title">
                          {{
                            withOpportunity
                              ? ("quote_unit"
                                | checkLabel
                                  : this.formConfig
                                  : "add-member"
                                  : "Quote Unit")
                              : ("rate_unit"
                                | checkLabel
                                  : this.formConfig
                                  : "add-member"
                                  : "Rate Card Unit")
                          }}
                          <span
                            class="required-star"
                            *ngIf="
                              'rate_unit'
                                | checkMandatedField
                                  : this.formConfig
                                  : 'add-member'
                            "
                            >&nbsp;*</span
                          >
                          <span
                            *ngIf="
                              'rate_unit'
                                | checkInfoIcon : this.formConfig : 'add-member'
                            "
                          >
                            <mat-icon
                              class="info-icon"
                              tooltip="{{
                                'rate_unit'
                                  | checkTooltip
                                    : this.formConfig
                                    : 'add-member'
                                    : 'Location'
                              }}"
                            >
                              info_outline
                            </mat-icon>
                          </span>
                        </div>

                        <app-input-search-name
                          class="input-field-disabled"
                          [list]="rateUnitList"
                          placeholder="Select One"
                          formControlName="rate_unit"
                          disabled
                        >
                        </app-input-search-name>
                      </div>
                    </div>

                    <div class="row">
                      <div
                      class="col-3"
                      *ngIf="
                        ('allocated_opportunity'
                          | checkActive : this.formConfig : 'add-member') && 
                          allocationOpportunityAllowedServiceType.includes(service_type_id)
                      "
                    >
                      <div class="content-title">
                        {{
                          "allocated_opportunity"
                            | checkLabel
                              : this.formConfig
                              : "add-member"
                              : "Opportunity"
                        }}
                        <span
                          class="required-star"
                          *ngIf="
                            'allocated_opportunity'
                              | checkMandatedField
                                : this.formConfig
                                : 'add-member'
                          "
                          >&nbsp;*</span
                        >
                        <span
                          *ngIf="
                            'allocated_opportunity'
                              | checkInfoIcon : this.formConfig : 'add-member'
                          "
                        >
                          <mat-icon
                            class="info-icon"
                            tooltip="{{
                              'allocated_opportunity'
                                | checkTooltip
                                  : this.formConfig
                                  : 'add-member'
                                  : 'Opportunity'
                            }}"
                          >
                            info_outline
                          </mat-icon>
                        </span>
                      </div>
                      <app-input-search-name
                      class="allocated_opportunity"
                      [ngClass]="{'travel_type-disabled': (disableEditAccess), 'travel_type': (!disableEditAccess)}"
                      [list]="allocatedOpportunityList"
                      placeholder="Select One"
                      [required]="
                        'allocated_opportunity'
                          | checkMandatedField : this.formConfig : 'add-member'
                      "
                      [disabled]="disableEditAccess"
                      formControlName="allocated_opportunity_id"
                    >
                    </app-input-search-name>
                    </div>
                    </div>

                    <div class="row">
                      <div
                        class="col-3"
                        *ngIf="
                          ('quote_start_date'
                            | checkActive : this.formConfig : 'add-member') &&
                          withOpportunity
                        "
                      >
                        <div class="content-title">
                          {{
                            "quote_start_date"
                              | checkLabel
                                : this.formConfig
                                : "add-member"
                                : "Quote Start Date"
                          }}
                          <span
                            class="required-star"
                            *ngIf="
                              'quote_start_date'
                                | checkMandatedField
                                  : this.formConfig
                                  : 'add-member'
                            "
                            >&nbsp;*</span
                          >
                          <span
                            *ngIf="
                              'quote_start_date'
                                | checkInfoIcon : this.formConfig : 'add-member'
                            "
                          >
                            <mat-icon
                              class="info-icon"
                              tooltip="{{
                                'quote_start_date'
                                  | checkTooltip
                                    : this.formConfig
                                    : 'add-member'
                                    : 'Quote End Date'
                              }}"
                            >
                              info_outline
                            </mat-icon>
                          </span>
                        </div>

                        <mat-form-field
                          class="input-field-disabled"
                          appearance="outline"
                        >
                          <input
                            matInput
                            formControlName="quote_start_date"
                            [matDatepicker]="qsd"
                            [required]="
                              'quote_start_date'
                                | checkMandatedField
                                  : this.formConfig
                                  : 'add-member'
                            "
                            placeholder="DD-MMM-YYYY"
                            [max]="
                              this.addMemberFormGroup.get('quote_end_date')
                                .value
                            "
                            disabled
                          />
                          <mat-datepicker-toggle
                            matSuffix
                            [for]="asd"
                          ></mat-datepicker-toggle>
                          <mat-datepicker #asd></mat-datepicker>
                        </mat-form-field>
                      </div>

                      <div
                        class="col-3 pl-4"
                        *ngIf="
                          ('quote_end_date'
                            | checkActive : this.formConfig : 'add-member') &&
                          withOpportunity
                        "
                      >
                        <div class="content-title">
                          {{
                            "quote_end_date"
                              | checkLabel
                                : this.formConfig
                                : "add-member"
                                : "Quote End Date"
                          }}
                          <span
                            class="required-star"
                            *ngIf="
                              'quote_end_date'
                                | checkMandatedField
                                  : this.formConfig
                                  : 'add-member'
                            "
                            >&nbsp;*</span
                          >
                          <span
                            *ngIf="
                              'quote_end_date'
                                | checkInfoIcon : this.formConfig : 'add-member'
                            "
                          >
                            <mat-icon
                              class="info-icon"
                              tooltip="{{
                                'quote_end_date'
                                  | checkTooltip
                                    : this.formConfig
                                    : 'add-member'
                                    : 'Quote End Date'
                              }}"
                            >
                              info_outline
                            </mat-icon>
                          </span>
                        </div>

                        <mat-form-field
                          class="input-field-disabled"
                          appearance="outline"
                        >
                          <input
                            matInput
                            formControlName="quote_end_date"
                            [matDatepicker]="qed"
                            [required]="
                              'quote_end_date'
                                | checkMandatedField
                                  : this.formConfig
                                  : 'add-member'
                            "
                            placeholder="DD-MMM-YYYY"
                            [min]="
                              this.addMemberFormGroup.get('quote_start_date')
                                .value
                            "
                            disabled
                          />
                          <mat-datepicker-toggle
                            matSuffix
                            [for]="asd"
                          ></mat-datepicker-toggle>
                          <mat-datepicker #asd></mat-datepicker>
                        </mat-form-field>
                      </div>

                      <div
                        class="col-3"
                        *ngIf="
                          'actual_billable_hours'
                            | checkActive : this.formConfig : 'add-member'
                        "
                      >
                        <div class="content-title">
                          {{
                            "actual_billable_hours"
                              | checkLabel
                                : this.formConfig
                                : "add-member"
                                : "Actual Billable Hours"
                          }}
                          <span
                            class="required-star"
                            *ngIf="
                              'actual_billable_hours'
                                | checkMandatedField
                                  : this.formConfig
                                  : 'add-member'
                            "
                          >
                            &nbsp;*</span
                          >
                          <span
                            *ngIf="
                              'actual_billable_hours'
                                | checkInfoIcon : this.formConfig : 'add-member'
                            "
                          >
                            <mat-icon
                              class="info-icon"
                              tooltip="{{
                                'actual_billable_hours'
                                  | checkTooltip
                                    : this.formConfig
                                    : 'add-member'
                                    : 'Info add'
                              }}"
                            >
                              info_outline
                            </mat-icon>
                          </span>
                        </div>

                        <mat-form-field
                          appearance="outline"
                          class="input-field"
                        >
                          <input
                            matInput
                            type="text"
                            [required]="
                              'actual_billable_hours'
                                | checkMandatedField
                                  : this.formConfig
                                  : 'add-member'
                            "
                            formControlName="actual_billable_hours"
                            digitOnly
                          />
                        </mat-form-field>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </ng-container>
            <ng-container *ngIf="section.sectionId == 'business'">
              <div class="row  d-block">
                <div class="row">
                  <div class="d-flex" *ngIf="!businessUnitAdded">
                    <span class="section-header-text"
                      style="cursor: pointer"
                      (click)="addBusinessEntity('create')"

                      >{{
                        "edit-business-division-list"
                          | checkLabel
                            : this.formConfig
                            : "add-member"
                            : "+ Edit Business Division"
                      }}</span
                    >
                    <span
                      class="pt-1 pl-2"
                      (click)="addBusinessEntity('create')"
                      style="cursor: pointer"
                      *ngIf="!disableEditAccess"
                    >
                      <mat-icon class="smallCardIcon"
                        >add_circle_outline</mat-icon
                      >
                    </span>
                  </div>
                  <div class="d-flex" *ngIf="businessUnitAdded">
                    <span class="section-header-text" style="cursor: pointer">{{
                      "business-division-list"
                        | checkLabel
                          : this.formConfig
                          : "add-member"
                          : "Business Division"
                    }}</span>
                    <span
                      *ngIf="
                        businessUnitAdded &&
                        ('edit_region'
                          | checkActive : this.formConfig : 'add-member') && !disableEditAccess
                      "
                      class="pt-1 pl-2"
                      (click)="addBusinessEntity('edit')"
                      style="cursor: pointer"
                    >
                      <mat-icon class="smallCardIcon">edit</mat-icon>
                    </span>
                  </div>
                </div>

                <div class="row" *ngIf="this.businessUnitAdded">
                  <div class="col-3">
                    <div class="row content-title">
                      {{
                        "position-entity"
                          | checkLabel
                            : this.formConfig
                            : "add-member"
                            : "Entity"
                      }}
                      <span> &nbsp;</span>
                    </div>
                    <div
                      class="row font-family"
                      style="
                        margin-top: 5px;
                        font-size: 13px;
                        font-weight: 400;
                        line-height: 16px;
                        letter-spacing: 0em;
                        text-align: left;
                        color: #8b95a5;
                      "
                    >
                      {{ this.entity_name }}
                    </div>
                  </div>

                  <div class="col-3" class="col-5 pl-4">
                    <div class="row content-title">
                      {{
                        "position-division"
                          | checkLabel
                            : this.formConfig
                            : "add-member"
                            : "Division"
                      }}
                      <span> &nbsp;</span>
                    </div>
                    <div
                      class="row font-family"
                      style="
                        margin-top: 5px;
                        font-size: 13px;
                        font-weight: 400;
                        line-height: 16px;
                        letter-spacing: 0em;
                        text-align: left;
                        color: #8b95a5;
                      "
                    >
                      {{ this.division_name }}
                    </div>
                  </div>
                  <div class="col-3" class="col-4 pl-4">
                    <div class="row content-title">
                      {{
                        "position-sub-division"
                          | checkLabel
                            : this.formConfig
                            : "add-member"
                            : "Sub Division"
                      }}<span> &nbsp;</span>
                    </div>
                    <div
                      class="row font-family"
                      style="
                        margin-top: 5px;
                        font-size: 13px;
                        font-weight: 400;
                        line-height: 16px;
                        letter-spacing: 0em;
                        text-align: left;
                        color: #8b95a5;
                      "
                    >
                      {{ this.sub_division_name }}
                    </div>
                  </div>
                </div>
              </div>
            </ng-container>
            <div class="form-divider-class">
              <mat-divider class="form-divider-style"></mat-divider>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>
