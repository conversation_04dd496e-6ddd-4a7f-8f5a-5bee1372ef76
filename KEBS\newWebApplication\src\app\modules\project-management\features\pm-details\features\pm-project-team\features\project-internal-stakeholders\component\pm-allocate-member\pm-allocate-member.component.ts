import {
  Component,
  ElementRef,
  HostListener,
  Inject,
  InjectionToken,
  OnInit,
  Renderer2,
  ViewChild,
} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { PmInternalStakeholderService } from '../../services/pm-internal-stakeholder.service';
import {
  MomentDateAdapter,
  MAT_MOMENT_DATE_ADAPTER_OPTIONS,
} from '@angular/material-moment-adapter';
import {
  DateAdapter,
  MAT_DATE_LOCALE,
  MAT_DATE_FORMATS,
} from '@angular/material/core';
import { ToasterMessageService } from 'src/app/modules/project-management/shared-lazy-loaded/components/toaster-message/toaster-message.service';
import { UtilityService } from 'src/app/services/utility/utility.service';
import { PmMasterService } from 'src/app/modules/project-management/services/pm-master.service';
export const TOASTER_MESSAGE_SERVICE_TOKEN =
  new InjectionToken<ToasterMessageService>('TOASTER_MESSAGE_SERVICE_TOKEN');
import * as _ from 'underscore';
import moment from 'moment';
import { FormBuilder } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { SubSink } from 'subsink';
import { MatDatepicker } from '@angular/material/datepicker';
import{PmBillingService} from 'src/app/modules/project-management/features/pm-details/features/pm-billing/services/pm-billing.service'
import { PmAuthorizationService } from 'src/app/modules/project-management/services/pm-authorization.service';
import { GetNameByIdPipe } from 'src/app/modules/project-management/shared-lazy-loaded/pipes/get-name-by-id.pipe';

@Component({
  selector: 'app-pm-allocate-member',
  templateUrl: './pm-allocate-member.component.html',
  styleUrls: ['./pm-allocate-member.component.scss'],
  providers: [
    {
      provide: DateAdapter,
      useClass: MomentDateAdapter,
      deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS],
    },
    {
      provide: MAT_DATE_FORMATS,
      useValue: {
        parse: {
          dateInput: 'DD-MMM-YYYY',
        },
        display: {
          dateInput: 'DD-MMM-YYYY',
          monthYearLabel: 'MMM YYYY',
        },
        useUtc: true,
      },
    },
    { provide: TOASTER_MESSAGE_SERVICE_TOKEN, useClass: ToasterMessageService },GetNameByIdPipe
  ],
})
export class PmAllocateMemberComponent implements OnInit {
  @ViewChild('asd') datepicker: MatDatepicker<Date>;
  @ViewChild('aed') datepicker1: MatDatepicker<Date>;
  @ViewChild('datepickerToggle', { static: false }) datepickerToggle;
  sectionId: string = 'allocation';
  sectionHeaderList: Array<Object> = [
    {
      sectionId: 'allocation',
      sectionVisible: true,
      sectionName: 'Allocation Details',
      position: 1,
    },
    {
      sectionId: 'project',
      sectionVisible: true,
      sectionName: 'Project Details',
      position: 2,
    },
    {
      sectionId: 'quote',
      sectionVisible: true,
      sectionName: 'Quote Details',
      position: 3,
    },
    {
      sectionId: 'business',
      sectionVisible: true,
      sectionName: 'Business Division',
      position: 4,
    },
  ];
  mode: any;
  data: any;
  billableColor: string = 'warn';
  nonBillableIdentity:any;
  show_identity:any;
  displaynonbillable:boolean=false
  isHead: boolean = false;
  isBillable: boolean = false;
  is_head: boolean = true;
  businessUnitAdded: boolean = false;
  selectedBusinessEntity: any;
  entity_name: any;
  division_name: any;
  sub_division_name: any;
  itemId: any;
  projectId: any;
  reports_to_list: any = [];
  internal:any=[]
  currentDate = moment().format();
  saveDisabled: boolean = false;
  isLumpsumBased: boolean=false;
  save_name: any = 'Save';
  is_reports_to: boolean = true;
  project_role_list = _.where(this.masterService.projectRoleMasterList, {
    role_type: 'Internal',
  });
  secprojectRoleMasterList: any = _.where(
    this.masterService.projectRoleMasterList,
    { role_type: 'Internal' }
  );
  shift_list: any = this.masterService.shift_list;
  location_list: any = this.masterService.isaTimesheetLocations;
  commercial_catageroies_list: any = this.masterService.commercialCatageroiesList;
  formConfig: any;
  showIdentity: boolean=false;
  shades: any;
  isComponentLoaded: boolean = false;
  employeePosition: any;
  employeePositionList: any = [];
  workCityList: any = [];
  workPremisisMasterList: any = [];
  traveltypeMasterList: any = [];
  workShiftMasterList: any = [];
  addMemberFormGroup = this.formBuilder.group({
    employee_name: [''],
    role: [''],
    location: [''],
    split_percentage: [''],
    per_hour_rate: [''],
    endDate: [''],
    startDate: [''],
    role_edit: [''],
    employee_name_edit: [''],
    rmg_spoc: [''],
    isBillable: [''],
    isIncentive: [''],
    entity: [''],
    division: [''],
    subdivision: [''],
    projectRole: [''],
    secondaryProjectRole: [''],
    stakeholder_reference_id: [''],
    shift: [''],
    actual_hours: [''],
    reportsTo: [''],
    isHead: [''],
    identity: [''],
    isBillableWithIdentity: [''],
    isBillableWithIdentityCategories:[''],
    request_position_id: [''],
    rate_card_id: [''],
    rate_position_id: [''],
    rate_currency: [''],
    rate_location: [''],
    rate_entity: [''],
    rate_division: [''],
    rate_sub_division: [''],
    rate_revenue: [''],
    rate_cost: [''],
    rate_unit: [''],
    quote_start_date: [''],
    quote_end_date: [''],
    actual_billable_hours: [''],
    allocated_hours: [''],
    remaining_billable_hours: [''],
    leave_hours:[''],
    holiday_hours:[''],
    total_allocated_hours:[''],
    work_premisis: [''],
    work_city_value: [''],
    work_city:[''],
    travel_type: [''],
    work_shift: [''],
    allocated_opportunity_id:['']
  });
  filterReportsTo: any = [];
  subDivisionList: any;
  divisionList: any;
  entityList: any;
  orgMappingList: any;
  project_start_date: any;
  project_end_date: any;
  DOJ: any;
  project_role_color: any = 'white';
  quote_position_list: any;
  quote_id: any = [];
  retrieveMessages: any;
  enable_position: boolean;
  withOpportunity: any;
  commercial_list: any;
  checkCommercial: number;
  color: any;
  identity_list: any = [];
  display_commercial_list: any = [];
  split_percentage_lower_percentage: number = 1;
  customer_id: any;
  rateCardList: any = [];
  rateEntityList: any = [];
  rateDivisionList: any = [];
  rateSubDivisionList: any = [];
  currencyList: any = [];
  rateLocationList: any = [];
  positionList: any = [];
  rateUnitList: any = [];
  rateCardConfiguration: any = [];
  billableList: any = [];
  q2cVisible: boolean = false;
  memberData: Object = null;
  subs = new SubSink();
  // checkAvailabilityVisible:boolean = false;
  // checkAvailabilityClicked:boolean = false;
  checkingProgress:boolean = false;
  milestoneEndDate:any;
  @ViewChild('content') content: ElementRef;
  imageAniUrl:string;
  imageUrl:string = 'https://assets.kebs.app/system-solid-23-calendar (2).gif'
  allowAllocationCheck:boolean = false;
  allocatedHrsVisible: boolean = false;
  hrs_value_digit : number = 14;
  hrs_decimal_digit: number = 2;
  allocationDisable: boolean = false;
  split_percentage_max_percentage: number = 100;
  e360EndDate: any = null;
  demobilizeFlag: boolean = false;
  internalprojectnonmand:boolean=false;
  service_type_id:any;
  serviceTypeList: any=[];
  ItemID:any;
  projectID:any;
  oldFormValue: any;
  oldData: any;
  newData: any;
  isFinancialValuesHidden:boolean=false;
  blanketPo: boolean = false;
  split_percentage_allow_decimal: any=false;
  disableEditAccess: boolean = false;
  editDisableConfigEnable: boolean = false;
  allocatedOpportunityList: any;
  allocationOpportunityAllowedServiceType: any;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    public PmInternalStakeholderService: PmInternalStakeholderService,
    private utilityService: UtilityService,
    private formBuilder: FormBuilder,
    private masterService: PmMasterService,
    public dialog: MatDialog,
    private authService: PmAuthorizationService,
    @Inject(TOASTER_MESSAGE_SERVICE_TOKEN)
    private toasterService: ToasterMessageService,
    private PmBillingService: PmBillingService,
    private getNameByIdPipe: GetNameByIdPipe,
  ) {}

  async ngOnInit() {
    this.ItemID = parseInt(this.router.url.split("/")[5])
    this.projectID = parseInt(this.router.url.split("/")[3])
    await this.getSubscribedData();
    this.isFinancialValuesHidden=await this.authService.getProjectWiseObjectAccess(this.projectID, this.ItemID, 178)
    if(this.memberData){
      await this.calculateDynamicStyle();
      this.isComponentLoaded = false;
      this.itemId = this.router.url.split('/')[5];
      this.projectId = this.router.url.split('/')[3];
      await this.masterService.serviceTypeList().then((res) => {
        this.serviceTypeList = res
      })
      await this.masterService.getOpportunityList().then((res) => {
        this.allocatedOpportunityList = res
      })
      
      this.internal=_.where(this.serviceTypeList,{is_internal:1})
      await this.masterService.getProjectQuote(this.projectId,this.itemId).then((res)=>{
              this.service_type_id=res['data'][0].service_type_id
      })

      let checkForLumpsum = _.where(this.serviceTypeList,{id: this.service_type_id, billable_allocation_allowed: 1})

      if(checkForLumpsum.length>0)
      {
        this.isLumpsumBased = true;
        this.addMemberFormGroup.patchValue({
          isBillableWithIdentity: "1"
        });

      }
      console.log(this.service_type_id)
      await this.initializeFormConfigData();  
      await this.initializeSubscribedData(this.memberData);
      if(this.editDisableConfigEnable){
        this.disableEditAccess =  await this.checkEditDisableAccess();
      }
      await this.handleFieldChanges();
      await this.handleHeaderChanges();
      await this.getAllocatedHours(true);
      // this.milestoneEndDate = await this.getMilestoneEndDate();
      // await this.checkAvailabilityButtonVisible();
      if (this.internalprojectnonmand) {
        if (this.internal?.length > 0 && this.service_type_id == this.internal[0]?.id) {
            let fieldsToDisable = []; // Initialize the array
    
            if (!this.withOpportunity) {
                fieldsToDisable = [
                    "rate_position_id", "rate_entity", 
                    "rate_unit", "rate_cost", "rate_revenue", "rate_currency", 
                    "rate_location", "rate_sub_division", "rate_division"
                ];
            } else {
                fieldsToDisable = [
                    "quote_position_id", "quote_entity", 
                    "quote_unit", "quote_cost", "quote_revenue", "quote_currency", 
                    "quote_location", "quote_sub_division", "quote_division"
                ];
            }
    
            this.formConfig = this.formConfig.map(config => {
                if (config.type === "add-member" && fieldsToDisable.includes(config.field_name)) {
                    return { ...config, is_mandant: false };
                }
                return config;
            });
        }
    }
    this.addMemberFormGroup.get('isBillableWithIdentity').valueChanges.subscribe((value) => {
      this.displaynonbillable = value ==this.nonBillableIdentity; // Set to true if value is 2
    });
      this.isComponentLoaded = true;
    }
    else{
      await this.routeTeamMember()
    }

    this.oldFormValue = this.addMemberFormGroup.value;
    this.oldData = {
      reports_to: this.oldFormValue.reportsTo,
      start_date: this.oldFormValue.startDate ? moment(this.oldFormValue.startDate).format('DD-MMM-YYYY') : null,
      secondary_project_role: this.oldFormValue.secondaryProjectRole ? this.getNameByIdPipe.transform(this.oldFormValue.secondaryProjectRole, this.secprojectRoleMasterList) : null,
      billable: this.oldFormValue.isBillable ? 'Billable' : 'Non-Billable',
      subdivision: this.oldFormValue.subdivision ? this.getNameByIdPipe.transform(this.oldFormValue.subdivision, this.rateSubDivisionList) : null,
      project_role: this.oldFormValue.projectRole ? this.getNameByIdPipe.transform(this.oldFormValue.projectRole, this.project_role_list) : null,
      head: this.oldFormValue.isHead ? 'yes' : 'No',
      rate_unit: this.oldFormValue.rate_unit ? this.getNameByIdPipe.transform(this.oldFormValue.rate_unit, this.rateUnitList) : null,
      rate_sub_division: this.oldFormValue.rate_sub_division ? this.getNameByIdPipe.transform(this.oldFormValue.rate_sub_division, this.rateSubDivisionList) : null,
      split: this.oldFormValue.split_percentage ? this.oldFormValue.split_percentage : null,
      rate_entity: this.oldFormValue.rate_entity ? this.getNameByIdPipe.transform(this.oldFormValue.rate_entity, this.rateEntityList) : null,
      rate_card_id: this.oldFormValue.rate_card_id ? this.oldFormValue.rate_card_id : null,
      rate_cost: this.oldFormValue.rate_cost ? this.oldFormValue.rate_cost : null,
      end_date: this.oldFormValue.endDate ? moment(this.oldFormValue.endDate).format('DD-MMM-YYYY') : null,
      division: this.oldFormValue.division ? this.getNameByIdPipe.transform(this.oldFormValue.division, this.rateDivisionList) : null,
      employee_name: this.oldFormValue.employee_name ? this.oldFormValue.employee_name : null,
      rate_location: this.oldFormValue.rate_location ? this.getNameByIdPipe.transform(this.oldFormValue.rate_location, this.rateLocationList) : null,
      rate_division: this.oldFormValue.rate_division ? this.getNameByIdPipe.transform(this.oldFormValue.rate_division, this.rateDivisionList) : null,
      allocated_hours: this.oldFormValue.allocated_hours ? this.oldFormValue.allocated_hours : null,
      shift: this.oldFormValue.shift ? this.getNameByIdPipe.transform(this.oldFormValue.shift, this.shift_list) : null,
      commercial: this.oldFormValue.isBillable ? 'Billable' : this.oldFormValue.isBillableWithIdentity ? this.getNameByIdPipe.transform(this.oldFormValue.isBillableWithIdentity, this.display_commercial_list) : null,
      request_position_id:  this.oldFormValue.request_position_id ? this.getNameByIdPipe.transform(this.oldFormValue.request_position_id, this.positionList) : null,
    }
    if(!this.withOpportunity){
      this.oldData.rate_position_id = this.oldFormValue.rate_card_id ? this.getNameByIdPipe.transform(this.oldFormValue.rate_card_id, this.rateCardList) : null;
    }else{
      this.oldData.quote_position_id = this.oldFormValue.rate_card_id ? this.getNameByIdPipe.transform(this.oldFormValue.rate_card_id, this.quote_position_list) : null;
    }

    if(this.withOpportunity){
      await this.PmBillingService.checkOpportunityBlanketPO(this.projectID,this.ItemID,this.quote_id).then((res=>{
        if(res['messType']=='S'){
          this.blanketPo = res['data'] 
        }
      }))
    }
  }

  /**
   * @description Fetching data from Team Component through subscription
   */
  async getSubscribedData() {
    this.subs.sink = this.PmInternalStakeholderService.memberData$.subscribe(async (value) => {
      this.memberData = value;
      
    });
  }

  /**
   * @description Routing Back to Main Page
   */
  routeTeamMember() {
    if (this.addMemberFormGroup.dirty && this.memberData) {
      this.utilityService
        .openConfirmationSweetAlertWithCustom(
          'Are you sure',
          'You want to Close without saving'
        )
        .then((result) => {
          if (result) {
            this.router.navigate(['../'], { relativeTo: this.route });
          }
        });
    } else {
      this.router.navigate(['../'], { relativeTo: this.route });
    }
  }

  /**
   * @description Section Selection
   * @param index
   */
  sectionVisited(index) {
    this.sectionId = index;
  }

  /**
   * @description scrolling to selected Section
   * @param id
   */
  scrollToSection(id: string) {
    this.sectionId = id;
    const element = document.getElementById(id);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  }

  /**
   * @description initializing Form Config Master data
   */
  async initializeFormConfigData() {
    await this.masterService.getPMFormCustomizeConfigV().then((res: any) => {
      if (res) {
        this.formConfig = res;
      }
    });

    let split_percentage_decimal = _.findWhere(this.formConfig, {type:"add-member", field_name:"split_percentage_allow_decimal", is_active: true});
    
    this.split_percentage_allow_decimal = split_percentage_decimal ? true: false;

    this.retrieveMessages = _.where(this.formConfig, {
      type: 'add-member',
      field_name: 'messages',
      is_active: true,
    });

    const retrieveStyles = _.where(this.formConfig, {
      type: 'project-theme',
      field_name: 'styles',
      is_active: true,
    });
    let button =
      retrieveStyles.length > 0
        ? retrieveStyles[0].data.button_color
          ? retrieveStyles[0].data.button_color
          : '#90ee90'
        : '#90ee90';
    let selected_shade =
      retrieveStyles.length > 0
        ? retrieveStyles[0].data.primary_shade
          ? retrieveStyles[0].data.primary_shade
          : '#fac5cd82'
        : '#fac5cd82';
    this.imageAniUrl =  retrieveStyles.length > 0
    ? retrieveStyles[0].data.calendar_ani_img
      ? retrieveStyles[0].data.calendar_ani_img
      : this.imageUrl
    : this.imageUrl;
    document.documentElement.style.setProperty('--teamButton', button);
    let fontStyle =
      retrieveStyles.length > 0
        ? retrieveStyles[0].data.font_style
          ? retrieveStyles[0].data.font_style
          : 'Roboto'
        : 'Roboto';
    document.documentElement.style.setProperty('--teamFont', fontStyle);
    let tabColor =
      retrieveStyles.length > 0
        ? retrieveStyles[0].data.tab_color
          ? retrieveStyles[0].data.tab_color
          : ''
        : '';
    document.documentElement.style.setProperty('--teamTab', tabColor);
    let fieldOutline =
      retrieveStyles.length > 0
        ? retrieveStyles[0].data.field_outline_color
          ? retrieveStyles[0].data.field_outline_color
          : '#808080'
        : '#808080';
    document.documentElement.style.setProperty('--teamField', fieldOutline);
    let scrollColor =
      retrieveStyles.length > 0
        ? retrieveStyles[0].data.scroll_color
          ? retrieveStyles[0].data.scroll_color
          : '#90ee90'
        : '#90ee90';
    document.documentElement.style.setProperty(
      '--project2teamScroll',
      scrollColor
    );
    document.documentElement.style.setProperty(
      '--selected_shade',
      selected_shade
    );
    let split_percentage_lower_percentage = _.findWhere(this.formConfig, {
      type: 'add-member',
      field_name: 'split_percentage_lower_percentage',
      is_active: true,
    });
    this.split_percentage_lower_percentage = split_percentage_lower_percentage
      ? split_percentage_lower_percentage['label']
      : 1;
    let retrieveQ2c = _.where(this.formConfig, {
      type: 'project-team',
      field_name: 'q2c_display',
      is_active: true,
    });
    this.q2cVisible =
      retrieveQ2c.length > 0
        ? retrieveQ2c[0]['is_visible']
          ? retrieveQ2c[0]['is_visible']
          : false
        : false;
    let sectionHeaderData = _.where(this.formConfig, {
      type: 'add-member',
      field_name: 'section_header',
      is_active: true,
    });
    this.sectionHeaderList =
    sectionHeaderData.length > 0
        ? sectionHeaderData[0]['section_list']
          ? sectionHeaderData[0]['section_list']
          : this.sectionHeaderList
        : this.sectionHeaderList;

    let allocationConfig = _.findWhere(this.formConfig, {
        type: 'add-member',
        field_name: 'allocation_config',
        is_active: true,
    });
    console.log('allocationConfig',allocationConfig)
    this.allocationDisable =
    allocationConfig
        ? allocationConfig['allocation_disable']
          ? allocationConfig['allocation_disable']
          : false
        : false;
    console.log(this.allocationDisable)
    let split_percentage_max_percentage = _.findWhere(this.formConfig, {
      type: 'add-member',
      field_name: 'split_percentage_max_percentage',
      is_active: true,
    });
    this.split_percentage_max_percentage = split_percentage_max_percentage
      ? split_percentage_max_percentage['label']
      : 100;
    let internalnonmand = _.where(this.formConfig,{ type:"add-member", field_name: "internal_rate_nonman",is_active:true})
    this.internalprojectnonmand = internalnonmand?.length > 0 ? true: false
    this.show_identity = _.findWhere(this.formConfig, {type:"project-team", field_name:"show-identity", is_active: true})

    this.showIdentity = this.show_identity ? true: false;
    console.log(this.showIdentity)
    this.nonBillableIdentity = this.show_identity && this.show_identity.config && this.show_identity.config.value 
  ? this.show_identity.config.value 
  : 2; 
    console.log(this.nonBillableIdentity)
    
    let disableConfig = _.findWhere(this.formConfig, {
      type: 'add-member',
      field_name: 'disable_edit_acces',
      is_active: true,
    });
    
    this.editDisableConfigEnable =
    disableConfig
        ? disableConfig['allocation_disable']
          ? disableConfig['allocation_disable']
          : false
        : false;

    let allocationOpportunityConfig = _.findWhere(this.formConfig, {
        type: 'add-member',
        field_name: 'allocated_opportunity',
        is_active: true,
    });
    this.allocationOpportunityAllowedServiceType =
    allocationOpportunityConfig
        ? allocationOpportunityConfig['allowed_service_type']
          ? allocationOpportunityConfig['allowed_service_type']
          : []
        : [];
  }

  /**
   * @description Initializing Team Tab Data
   * @param data
   */
  async initializeSubscribedData(data) {
    this.mode = data.mode;
    this.data = data.data;
    this.color = data.color ? data.color : 'EE4961';
    this.entityList = data.masterData['orgEntityList'];
    this.divisionList = data.masterData['orgDivisionList'];
    this.subDivisionList = data.masterData['orgSubDivisionList'];
    this.rateEntityList = data.masterData['entityList'];
    this.rateDivisionList = data.masterData['divisionList'];
    this.rateSubDivisionList = data.masterData['subDivisionList'];
    (this.quote_id = data.masterData['quote_id_list']),
      (this.enable_position = data.masterData['enable_position']);
    this.rateCardList = data.masterData['rateCardList'];
    this.project_end_date = data.masterData['project_end_date'];
    this.project_start_date = data.masterData['project_start_date'];
    this.withOpportunity = data.masterData['withOpportunity'];
    this.customer_id = data.masterData['customer_id'];
    this.currencyList = data.masterData['currencyList'];
    this.rateLocationList = data.masterData['rateLocationList'];
    this.orgMappingList = data.masterData['orgMappingList'];
    this.positionList = data.masterData['positionList'];
    this.rateUnitList = data.masterData['rateUnitList'];
    this.currencyList = data.masterData['currenyList'];
    this.quote_position_list = data.masterData['quote_position_list'];
    this.rateCardConfiguration = data.masterData['rateCardConfiguration'];
    this.workPremisisMasterList = await this.getWorkPremisisList();
    this.traveltypeMasterList = await this.getTravelTypeList();
    this.workShiftMasterList = await this.getWorkShiftMasterList();
    let work_city_data = _.findWhere(this.formConfig, {
      type: 'add-member',
      field_name: 'work_city',
      is_active: true,
    });
    if(work_city_data){
      await this.masterService.getWorkCityList().then((res) => {
        this.workCityList = res
      })
    }

    await this.PmInternalStakeholderService.getreportsToMaster(
      this.itemId,
      this.currentDate
    ).then((res) => {
      
      this.reports_to_list = res['data'];
      this.filterReportsTo = res['data'];
    });

    await this.PmInternalStakeholderService.getHeadForISA(
      parseInt(this.itemId)
    ).then((res) => {
      
      if (res['data'].length > 0) {
        this.is_head = false;
      }
    });

    await this.masterService.getISABillingTypeList().then((res) => {
      this.billableList = res;
    });

    let commercialAvailable = _.findWhere(this.formConfig, {
      type: 'add-member',
      field_name: 'commercial',
      is_active: true,
    });

    this.commercial_list = this.billableList;

    this.display_commercial_list = [];
    

    if (commercialAvailable) {
      await this.masterService
        .getProjectIdentityList()
        .then(async (res: any) => {
          this.identity_list = res;

          for (let commercial of this.billableList) {
           
            if (commercial['id'] == 0) {
              for (let identity of this.identity_list) {
                let val = {
                  id: '2_' + (identity['id'] + ''),
                  name: commercial['name'] + ' ' + identity['name'],
                };

                this.display_commercial_list.push(val);
              }
            } else {
              let val = {
                id: '1',
                name: commercial['name'],
              };

              this.display_commercial_list.push(val);
            }
          }

          
        });
    }

    this.addMemberFormGroup
      .get('projectRole')
      .valueChanges.subscribe(async (res) => {
        if (res) {
          this.secprojectRoleMasterList = _.filter(
            this.project_role_list,
            (res1) => {
              return res1['id'] != res;
            }
          );

        
       
        }
      });

    this.addMemberFormGroup
      .get('rate_card_id')
      .valueChanges.subscribe(async (res) => {
       
        if (res) {
          if (this.withOpportunity) {
            let quote_position = _.where(this.quote_position_list, { id: res });

            if (quote_position.length > 0) {
              this.addMemberFormGroup.patchValue({
                rate_position_id: quote_position[0]['position'],
                rate_currency: quote_position[0]['currency'],
                rate_location: quote_position[0]['work_location'],
                rate_entity: quote_position[0]['entity'],
                rate_division: quote_position[0]['division'],
                rate_sub_division: quote_position[0]['sub_division'],
                rate_revenue: quote_position[0]['revenue'],
                rate_cost: quote_position[0]['cost'],
                rate_unit: quote_position[0]['unit'],
                quote_start_date: quote_position[0]['quote_start_date'],
                quote_end_date: quote_position[0]['quote_end_date'],
              });
            
              if(this.mode !="Edit")
              {
                let quotePositionConfig = _.where(this.formConfig,{type:"add-member", field_name:"enterprise_structure_quote_position", is_active: true})

                if(quotePositionConfig.length>0)
                {
                    let val = {
                      entity_id: quote_position[0]['entity'],
                      entity_name: quote_position[0]['entity_name'],
                      division_id:  quote_position[0]['division'],
                      division_name: quote_position[0]['division_name'],
                      sub_division_id: quote_position[0]['sub_division'],
                      sub_division_name: quote_position[0]['sub_division_name'],
                    };
                    this.selectedBusinessEntity = val;
                    this.businessUnitAdded = quote_position[0]['entity'] ? true : false;
                    this.entity_name = quote_position[0]['entity_name'];
                    this.division_name = quote_position[0]['division_name'];
                    this.sub_division_name = quote_position[0]['sub_division_name'];

                    this.addMemberFormGroup.patchValue({
                      entity: quote_position[0]['entity'],
                      division: quote_position[0]['division'],
                      subdivision: quote_position[0]['sub_division'],
                    });
                }
              }
            }
          } else {
            let rate_position = _.where(this.rateCardList, { id: res });

            if (rate_position.length > 0) {
              this.addMemberFormGroup.patchValue({
                rate_position_id: rate_position[0]['position'],
                rate_currency: rate_position[0]['currency'],
                rate_location: rate_position[0]['work_location'],
                rate_entity: rate_position[0]['entity'],
                rate_division: rate_position[0]['division'],
                rate_sub_division: rate_position[0]['sub_division'],
                rate_revenue: rate_position[0]['revenue'],
                rate_cost: rate_position[0]['cost'],
                rate_unit: rate_position[0]['quote_unit'],
              });

              if(this.mode !="Edit")
              {

                let quotePositionConfig = _.where(this.formConfig,{type:"add-member", field_name:"enterprise_structure_quote_position", is_active: true})

                if(quotePositionConfig.length>0)
                {
                  let val = {
                    entity_id: rate_position[0]['entity'],
                    entity_name: rate_position[0]['entity_name'],
                    division_id: rate_position[0]['division'],
                    division_name: rate_position[0]['division_name'],
                    sub_division_id: rate_position[0]['sub_division'],
                    sub_division_name: rate_position[0]['sub_division_name'],
                  };
                  this.selectedBusinessEntity = val;
                  this.businessUnitAdded =rate_position[0]['entity'] ? true : false;
                  this.entity_name = rate_position[0]['entity_name'];
                  this.division_name = rate_position[0]['division_name'];
                  this.sub_division_name = rate_position[0]['sub_division_name'];

                  this.addMemberFormGroup.patchValue({
                    entity:rate_position[0]['entity'],
                    division: rate_position[0]['division'],
                    subdivision: rate_position[0]['sub_division'],
                  });
                }
              }
             
            }
          }
        } else {
          this.addMemberFormGroup.patchValue({
            rate_position_id: '',
            rate_currency: '',
            rate_location: '',
            rate_entity: '',
            rate_division: '',
            rate_sub_division: '',
            rate_revenue: '',
            rate_cost: '',
            rate_unit: '',
            quote_start_date: '',
            quote_end_date: '',
          });
        }
      });
      

    if (this.mode != 'Edit') {
      this.addMemberFormGroup
        .get('employee_name')
        .valueChanges.subscribe(async (res) => {
         
          this.reports_to_list = this.filterReportsTo;
          if (res) {
            let date = moment().format();
            let quotePositionConfig = _.where(this.formConfig,{type:"add-member", field_name:"enterprise_structure_quote_position", is_active: true})

            if(quotePositionConfig.length == 0)
            {
              this.clearBusinessEntity()

              await this.PmInternalStakeholderService.getOrgMappingForEmployee(
                res,
                date
              ).then((res) => {
              
                if (res['messType'] == 'S') {
                  

                  if(quotePositionConfig.length==0)
                  {
                      let val = {
                        entity_id: res['data']['entity_id'],
                        entity_name: res['data']['entity_name'],
                        division_id: res['data']['division_id'],
                        division_name: res['data']['division_name'],
                        sub_division_id: res['data']['sub_division_id'],
                        sub_division_name: res['data']['sub_division_name'],
                      };
                      this.selectedBusinessEntity = val;
                      this.businessUnitAdded = res['data']['entity_id'] ? true : false;
                      this.entity_name = res['data']['entity_name'];
                      this.division_name = res['data']['division_name'];
                      this.sub_division_name = res['data']['sub_division_name'];
      
                      this.addMemberFormGroup.patchValue({
                        entity: res['data']['entity_id'],
                        division: res['data']['division_id'],
                        subdivision: res['data']['sub_division_id'],
                      });
                  }
                  
                  
                }
                
              });
            }
            await this.PmInternalStakeholderService.getDOJ(res).then((res) => {
              if (res['messType'] == 'S') {
                this.DOJ = res['data'][0].date_of_joining;
              }
            });
            await this.PmInternalStakeholderService.getE360EndDate(res).then((res) => {
              if (res['messType'] == 'S') {
                this.e360EndDate = res['data'] ? res['data'].end_date ? res['data'].end_date : null : null;
              }
            });
            for (let result of this.reports_to_list) {
              if (result['associate_id'] == res) {
                const idToDelete = res;
                this.reports_to_list = this.reports_to_list.filter(
                  (obj) => obj.associate_id !== idToDelete
                );
              }
            }

            let checkEmployeePosition = _.where(this.formConfig, {
              field_name: 'employee_position_id',
              is_active: true,
              type: 'add-member',
            });

            if (checkEmployeePosition.length > 0) {
              await this.PmInternalStakeholderService.getEmployeePosition(
                res,
                date
              ).then((res) => {
                if (res['messType'] == 'S') {
                  this.employeePosition = res['data'][0].position;
                  this.addMemberFormGroup.patchValue({
                    request_position_id: this.employeePosition,
                  });
                }
              });
            }
          } else {
            let quotePositionConfig = _.where(this.formConfig,{type:"add-member", field_name:"enterprise_structure_quote_position", is_active: true})
            
            if(quotePositionConfig.length == 0)
            {
              this.clearBusinessEntity();
            }
          }
        });
    }
    if (this.mode == 'Edit') {
      this.reports_to_list = this.filterReportsTo;
      this.employeePosition = this.data.request_position_id;
      for (let result of this.reports_to_list) {
        if (result['associate_id'] == this.data.associate_id) {
          const idToDelete = this.data.associate_id;
          this.reports_to_list = this.reports_to_list.filter(
            (obj) => obj.associate_id !== idToDelete
          );
        }
      }

      // let date = moment().format();
      // this.clearBusinessEntity();
      // await this.PmInternalStakeholderService.getOrgMappingForEmployee(
      //   this.data.associate_id,
      //   date
      // ).then((res) => {
      // if (res['messType'] == 'S') {
      //     let val = {
      //       entity_id: res['data']['entity_id'],
      //       entity_name: res['data']['entity_name'],
      //       division_id: res['data']['division_id'],
      //       division_name: res['data']['division_name'],
      //       sub_division_id: res['data']['sub_division_id'],
      //       sub_division_name: res['data']['sub_division_name'],
      //     };
      //     this.selectedBusinessEntity = val;
      //     this.businessUnitAdded = res['data']['entity_id'] ? true : false;
      //     this.entity_name = res['data']['entity_name'];
      //     this.division_name = res['data']['division_name'];
      //     this.sub_division_name = res['data']['sub_division_name'];

      //     this.addMemberFormGroup.patchValue({
      //       entity: res['data']['entity_id'],
      //       division: res['data']['division_id'],
      //       subdivision: res['data']['sub_division_id'],
      //     });
      //   }
      // });
      
      await this.PmInternalStakeholderService.getDOJ(this.data.associate_id).then((res) => {
        if (res['messType'] == 'S') {
          this.DOJ = res['data'][0].date_of_joining;
        }
      });
      await this.PmInternalStakeholderService.getE360EndDate(this.data.associate_id).then((res) => {
        if (res['messType'] == 'S') {
          this.e360EndDate = res['data'] ? res['data'].end_date ? res['data'].end_date : null : null;
        }
      });
    }
    let withPosition = data.withPosition ? data.withPosition : false;
    if (this.mode == 'Edit') {
      this.project_role_color = '#E8E9EE';
      this.updateFormWithAddMember();
    } else if (this.mode == 'Duplicate') {
      this.duplicateFormWithAddMember();
    } else if (this.mode == 'Create' && withPosition) {
      this.allocatePositionMember();
    }
    this.addMemberFormGroup.get('rate_revenue').disable();
    this.addMemberFormGroup.get('rate_cost').disable();
  }

  clearBusinessEntity() {
    this.addMemberFormGroup.get('entity').reset();
    this.addMemberFormGroup.get('division').reset();
    this.addMemberFormGroup.get('subdivision').reset();
    this.entity_name = undefined;
    this.division_name = undefined;
    this.sub_division_name = undefined;
    this.businessUnitAdded = false;
  }
  async addMember() {
    this.saveDisabled = true;
    this.save_name = 'Saving';
    
    
    let mandate = this.checkMandateNotEmpty(this.addMemberFormGroup.value);
    if (mandate) {
      let commercialAvailable = _.findWhere(this.formConfig, {
        type: 'add-member',
        field_name: 'commercial',
        is_active: true,
      });

      if (commercialAvailable) {
        let commercialValue = this.addMemberFormGroup.get(
          'isBillableWithIdentity'
        ).value;

        if (commercialValue == '1') {
          this.addMemberFormGroup.patchValue({ isBillable: 1, identity: null });
        } else if (commercialValue.includes('2')) {
          let billable_identity = commercialValue.split('_');

          this.addMemberFormGroup.patchValue({
            isBillable: 0,
            identity: billable_identity[1],
          });
        }
      }

      // let res_split_per = _.findWhere(this.formConfig, {type:"isa", field_name:'split_percentage', is_active:true})
      if (
        this.addMemberFormGroup.get('split_percentage').value == -1 || this.addMemberFormGroup.get('split_percentage').value == 0
      ) {
        const percentage_msg =
          this.retrieveMessages.length > 0
            ? this.retrieveMessages[0].errors.percentage_msg
              ? this.retrieveMessages[0].errors.percentage_msg
              : 'Allocation Percentage should be between 1-100'
            : 'Allocation Percentage should be between 1-100';
        this.toasterService.showWarning(percentage_msg, 10000);
        this.saveDisabled = false;
        this.save_name = 'Save';
      } else if (!this.businessUnitAdded) {
        // let res = _.findWhere(this.formConfig, {type:"isa", field_name:'addBusinessDivisionLabel'})
        const business_msg =
          this.retrieveMessages.length > 0
            ? this.retrieveMessages[0].errors.business_msg
              ? this.retrieveMessages[0].errors.business_msg
              : 'Kindly select Business Division to proceed'
            : 'Kindly select Business Division to proceed';
        this.toasterService.showWarning(business_msg, 10000);
        this.saveDisabled = false;
        this.save_name = 'Save';
      } else {
        this.addMemberFormGroup.patchValue({
          startDate: moment(
            this.addMemberFormGroup.get('startDate').value
          ).format('YYYY-MM-DD'),
          endDate: moment(this.addMemberFormGroup.get('endDate').value).format(
            'YYYY-MM-DD'
          ),
        });
        if (this.addMemberFormGroup.get('isHead').value == true) {
          this.addMemberFormGroup.patchValue({ reportsTo: null });
        }
        let result = this.addMemberFormGroup.value;
        
        result['mode'] = this.mode;
        result['oid'] = result['employee_name'];
        let checkEmployeePosition = _.where(this.formConfig, {
          field_name: 'request_position_id',
          is_active: true,
          type: 'add-member',
        });

        if (checkEmployeePosition.length == 0) {
          result['request_position_id'] = this.employeePosition;
        }

        let addMember = false;
        
       
        await this.getRemainingBillableHoursData();

        let dayWizeCheck = await this.checkDayWizeEmployeeHours(this.mode, this.addMemberFormGroup.get('stakeholder_reference_id').value)

        console.log("Day Wise Check", dayWizeCheck)

        if ((this.allowAllocationCheck && dayWizeCheck)) {
            addMember = true;
        } else {
            addMember = false;
        }
        if (addMember) {
          let data = this.addMemberFormGroup.value
          this.newData = {
            reports_to: data.reportsTo,
            start_date: data.startDate ? moment(data.startDate).format('DD-MMM-YYYY') : null,
            secondary_project_role: data.secondaryProjectRole ? this.getNameByIdPipe.transform(data.secondaryProjectRole, this.secprojectRoleMasterList) : null,
            billable: data.isBillable ? 'Billable' : 'Non-Billable',
            subdivision: data.subdivision ? this.getNameByIdPipe.transform(data.subdivision, this.rateSubDivisionList) : null,
            project_role: data.projectRole ? this.getNameByIdPipe.transform(data.projectRole, this.project_role_list) : null,
            head: data.isHead ? 'yes' : 'No',
            rate_unit: data.rate_unit ? this.getNameByIdPipe.transform(data.rate_unit, this.rateUnitList) : null,
            rate_sub_division: data.rate_sub_division ? this.getNameByIdPipe.transform(data.rate_sub_division, this.rateSubDivisionList) : null,
            split: data.split_percentage ? data.split_percentage : null,
            rate_entity: data.rate_entity ? this.getNameByIdPipe.transform(data.rate_entity, this.rateEntityList) : null,
            rate_cost: data.rate_cost ? data.rate_cost : null,
            end_date: data.endDate ? moment(data.endDate).format('DD-MMM-YYYY') : null,
            division: data.division ? this.getNameByIdPipe.transform(data.division, this.rateDivisionList) : null,
            employee_name: data.employee_name ? data.employee_name : null,
            rate_location: data.rate_location ? this.getNameByIdPipe.transform(data.rate_location, this.rateLocationList) : null,
            rate_division: data.rate_division ? this.getNameByIdPipe.transform(data.rate_division, this.rateDivisionList) : null,
            allocated_hours: data.allocated_hours ? data.allocated_hours : null,
            shift: data.shift ? this.getNameByIdPipe.transform(data.shift, this.shift_list) : null,
            commercial: data.isBillableWithIdentity ? this.getNameByIdPipe.transform(data.isBillableWithIdentity, this.display_commercial_list) : null,
            request_position_id:  data.request_position_id ? this.getNameByIdPipe.transform(data.request_position_id, this.positionList) : null,
          }
          if(!this.withOpportunity){
            this.newData.rate_position_id = data.rate_card_id ? this.getNameByIdPipe.transform(data.rate_card_id, this.rateCardList) : null;
          }else{
            this.newData.quote_position_id = data.rate_card_id ? this.getNameByIdPipe.transform(data.rate_card_id, this.quote_position_list) : null;
          }
          if(this.showIdentity && this.displaynonbillable){
            result['isBillableWithIdentityCategories']=result['isBillableWithIdentityCategories']

          }
          else{
            result['isBillableWithIdentityCategories']=null
          }
          await this.PmInternalStakeholderService.addTeamMember(
            this.projectId,
            this.itemId,
            result,
            this.oldData,
            this.newData
          ).then((res) => {
            
            if (res['messType'] == 'E') {
              
              if (result['mode'] == 'Edit') {
                const edit_unsuccess =
                  this.retrieveMessages.length > 0
                    ? this.retrieveMessages[0].errors.edit_unsuccess
                      ? this.retrieveMessages[0].errors.edit_unsuccess
                      : 'Employee information editing was unsuccessful'
                    : 'Employee information editing was unsuccessful';
                this.toasterService.showWarning(edit_unsuccess, 10000);
                this.saveDisabled = false;
                this.save_name = 'Save';
              } else {
                
                const onboarding_fail =
                  this.retrieveMessages.length > 0
                    ? this.retrieveMessages[0].errors.onboarding_fail
                      ? this.retrieveMessages[0].errors.onboarding_fail
                      : 'Employee onboarding fails'
                    : 'Employee onboarding fails';
                this.toasterService.showWarning(onboarding_fail, 10000);
                this.saveDisabled = false;
                this.save_name = 'Save';
              }
            } else if (res['messType'] == 'R') {
              const duplicate_entry =
                this.retrieveMessages.length > 0
                  ? this.retrieveMessages[0].errors.duplicate_entry
                    ? this.retrieveMessages[0].errors.duplicate_entry
                    : 'Resource already exists!'
                  : 'Resource already exists!';
              this.toasterService.showWarning(duplicate_entry, 10000);
              this.saveDisabled = false;
              this.save_name = 'Save';
            }
            else if (res['messType'] == 'W') {
              this.toasterService.showWarning(res['message'], 10000);
              this.saveDisabled = false;
              this.save_name = 'Save';
            }
             else {


              let message = result['mode'] == 'Edit' ? 'Updated' : 'Allocated';
              if (result['mode'] == 'Edit') {
                
                this.save_name = 'Saved';
                // this.dialogRef.close({ messType: "S" });
                const edit_success =
                  this.retrieveMessages.length > 0
                    ? this.retrieveMessages[0].errors.edit_success
                      ? this.retrieveMessages[0].errors.edit_success
                      : 'Employee information was successfully edited'
                    : 'Employee information was successfully edited';
                this.toasterService.showSuccess(edit_success, 10000);
                this.router.navigate(['../'], { relativeTo: this.route });
              } else {
                
                this.save_name = 'Saved';
                // this.dialogRef.close({ messType: "S" });
                const onboard_success =
                  this.retrieveMessages.length > 0
                    ? this.retrieveMessages[0].success.onboard_success
                      ? this.retrieveMessages[0].success.onboard_success
                      : 'Employee successfully onboarded'
                    : 'Employee successfully onboarded';
                this.toasterService.showSuccess(onboard_success, 10000);
                this.router.navigate(['../'], { relativeTo: this.route });
              }

              //this.utilityService.showMessage("Resource "+message+ " successfully!","Dismiss",3000)
            }
          });
        } 
        else if(!dayWizeCheck){
          this.saveDisabled = false;
          
          const dayWize_unsuccess =
                  this.retrieveMessages.length > 0
                    ? this.retrieveMessages[0].errors.dayWize_unsuccess
                      ? this.retrieveMessages[0].errors.dayWize_unsuccess
                      : 'Allocation limit exceeded: An employee cannot be allocated more than daily working hours per day. Please adjust the hours accordingly'
                    : 'Allocation limit exceeded: An employee cannot be allocated more than daily working hours per day. Please adjust the hours accordingly';
          this.toasterService.showWarning(dayWize_unsuccess, 4000);
        }
        else {
          this.saveDisabled = false;
          const hours_unsuccess =
                  this.retrieveMessages.length > 0
                    ? this.retrieveMessages[0].errors.hours_unsuccess
                      ? this.retrieveMessages[0].errors.hours_unsuccess
                      : 'Quote Position Balance is not available to allocate, Kindly Adjust Allocation Details'
                    : 'Quote Position Balance is not available to allocate, Kindly Adjust Allocation Details';
          this.toasterService.showWarning(hours_unsuccess, 4000);
          // this.router.navigate(['../'], { relativeTo: this.route });
        }

        // this.dialogRef.close({messType:"S", data:this.addMemberFormGroup.value, mode: this.mode, formConfig:this.formConfig})
      }
    } else {
      //this.toastr.warning("Enter all Mandatory Fields!", 'Warning');
      this.saveDisabled = false;
      this.save_name = 'Save';
    }

    this.saveDisabled = false;
  }
  updateFormWithAddMember() {
    if (this.mode == 'Edit') {
      let res = this.data ? this.data : null;
      
      // Assuming this.data.isa_id is available and this.reports_to_list is an array of records

// Log the 'isa_id'
console.log("isa id " + this.data.isa_id);

// Step 1: Filter out the records where 'reports_to' is equal to 'isa_id' or where 'reports_to' points to excluded ids.
this.reports_to_list = this.reports_to_list.filter(record => {
    // Exclude records where 'reports_to' is 'isa_id' or 'reports_to' points to an excluded record
    return record.reports_to !== this.data.isa_id && 
           (record.reports_to === null || !this.reports_to_list.some(r => r.id === record.reports_to && r.reports_to === this.data.isa_id));
});

// Log the updated 'reports_to_list'
console.log(this.reports_to_list);


      if (res) {
        
        this.demobilizeFlag = _.findWhere(this.formConfig, {type:"add-member", field_name:"demobilize-flag-check", is_active: true}) ? this.data.is_transfer_action ? this.data.is_transfer_action : false : false
        if (this.data.head == 1) {
          this.is_head = true;
          this.is_reports_to = false;
        }
        if(this.data.billable==0 && this.data.identity==1){
          this.displaynonbillable=true

        }
        this.secprojectRoleMasterList = _.filter(
          this.project_role_list,
          (res1) => {
            return res1['id'] != this.data.project_role_id;
          }
        );

  

        let val = {
          entity_id: this.data.entity_id,
          entity_name: this.data.entity_name,
          division_id: this.data.division_id,
          division_name: this.data.division_name,
          sub_division_id: this.data.sub_division_id,
          sub_division_name: this.data.sub_division_name,
        };
        this.selectedBusinessEntity = val;
        this.businessUnitAdded = this.data.entity_id ? true : false;
        this.entity_name = this.data.entity_name;
        this.division_name = this.data.division_name;
        this.sub_division_name = this.data.sub_division_name;
        console.log('this.data:',this.data);
        this.addMemberFormGroup.patchValue({
          employee_name: this.data.associate_id,
          role: this.data.practice_id,
          location: this.data.location,
          split_percentage: this.data.split_percentage,
          per_hour_rate: this.data.per_hour_rate,
          endDate: moment(this.data.end_date).format('YYYY-MM-DD'),
          startDate: moment(this.data.start_date).format('YYYY-MM-DD'),
          role_edit: this.data.practice_id,
          employee_name_edit: this.data.associate_id,
          rmg_spoc: this.data.rmg_spoc,
          isBillable: this.data.billable,
          isIncentive: this.data.incentive,
          entity: this.data.entity_id,
          division: this.data.division_id,
          subdivision: this.data.sub_division_id,
          projectRole: this.data.project_role_id,
          secondaryProjectRole:
            this.data.secondary_role != null
              ? typeof this.data.secondary_role == 'string'
                ? JSON.parse(this.data.secondary_role)
                : this.data.secondary_role
              : null,
          stakeholder_reference_id: this.data.stakeholder_reference_id,
          shift: this.data.shift,
          actual_hours: this.data.actual_hours,
          reportsTo: this.data.reports_to,
          isHead: this.data.head,
          identity: this.data.identity,
          request_position_id: this.data.request_position_id,
          rate_card_id: this.data.rate_card_id,
          actual_billable_hours: this.data.actual_billable_hours,
          allocated_hours: this.data.allocated_hours,
          isBillableWithIdentityCategories:this.data.non_billable_category,
          work_premisis: this.data.work_premisis,
          work_city: this.data.work_city,
          travel_type: this.data.travel_type ? this.data.travel_type : null,
          work_shift: this.data.work_shift ? this.data.work_shift : null,
          allocated_opportunity_id: this.data.allocated_opportunity_id ? this.data.allocated_opportunity_id : null
        });

        let commercialAvailable = _.findWhere(this.formConfig, {
          type: 'add-member',
          field_name: 'commercial',
          is_active: true,
        });

        if (commercialAvailable) {
          if (this.data.billable == 1) {
            this.addMemberFormGroup.patchValue({
              isBillableWithIdentity: this.data.billable + '',
            });
          } else if (this.data.billable == 0) {
            if (this.data.identity && this.data.identity != null) {
              this.addMemberFormGroup.patchValue({
                isBillableWithIdentity: '2_' + this.data.identity + '',
              });
            }
          }
        }

        if (this.withOpportunity) {
          let quote_position = _.where(this.quote_position_list, {
            id: this.data.rate_card_id,
          });

          if (quote_position.length > 0) {
            this.addMemberFormGroup.patchValue({
              rate_position_id: quote_position[0]['position'],
              rate_currency: quote_position[0]['currency'],
              rate_location: quote_position[0]['work_location'],
              rate_entity: quote_position[0]['entity'],
              rate_division: quote_position[0]['division'],
              rate_sub_division: quote_position[0]['sub_division'],
              rate_revenue: quote_position[0]['revenue'],
              rate_cost: quote_position[0]['cost'],
              rate_unit: quote_position[0]['unit'],
              quote_start_date: quote_position[0]['quote_start_date'],
              quote_end_date: quote_position[0]['quote_end_date'],
            });
          }
        } else {
          let rate_position = _.where(this.rateCardList, {
            id: this.data.rate_card_id,
          });

          if (rate_position.length > 0) {
            this.addMemberFormGroup.patchValue({
              rate_position_id: rate_position[0]['position'],
              rate_currency: rate_position[0]['currency'],
              rate_location: rate_position[0]['work_location'],
              rate_entity: rate_position[0]['entity'],
              rate_division: rate_position[0]['division'],
              rate_sub_division: rate_position[0]['sub_division'],
              rate_revenue: rate_position[0]['revenue'],
              rate_cost: rate_position[0]['cost'],
              rate_unit: rate_position[0]['unit'],
            });
            
          }
        }

        this.isComponentLoaded = true;
      }
    } else {
      this.addMemberFormGroup.reset();
      this.isComponentLoaded = true;
    }
  }
  isHeadChange(event: Event) {
    if (this.addMemberFormGroup.get('isHead').value == true) {
      this.is_reports_to = false;
      this.addMemberFormGroup.patchValue({ ['reportsTo']: '' });
    } else {
      this.is_reports_to = true;
    }
  }
  isMandate(field: any) {
    const mandate = _.where(this.formConfig, {
      type: 'add-member',
      field_name: field,
      is_active: true,
    });
    if (mandate.length > 0) {
      const isMandate = mandate[0].is_mandant;
      return isMandate;
    }
  }

  isInternalStakeholderRateCardMandate(){
    const mandate = _.where(this.formConfig, {
      type: 'add-member',
      field_name: 'internal_rate_nonman',
      is_active: true,
    });
    if (mandate.length > 0) {
      const isActive = mandate[0].is_active;
      return isActive;
    }
    else{
      return false;
    }
  }
  checkMandateNotEmpty(data: any) {
    let errorOccurred = false;

    console.log("ALL", this.internal.length > 0 && this.service_type_id == this.internal[0]?.id && this.isInternalStakeholderRateCardMandate(), "FORM", this.isInternalStakeholderRateCardMandate(), "INT", this.internal.length > 0 && this.service_type_id == this.internal[0]?.id )
   
    if (
      (data.employee_name === null ||
        data.employee_name === undefined ||
        data.employee_name === '') &&
      this.isMandate('employee_name')
    ) {
      const name_empty =
        this.retrieveMessages.length > 0
          ? this.retrieveMessages[0].errors.name_empty
            ? this.retrieveMessages[0].errors.name_empty
            : 'Employee name is Mandatory'
          : 'Employee name is Mandatory';
      this.toasterService.showWarning(name_empty, 10000);
      errorOccurred = true;
    } else if (
      (data.isBillableWithIdentity === null ||
        data.isBillableWithIdentity === undefined ||
        data.isBillableWithIdentity === '') &&
      this.isMandate('commercial')
    ) {
      // const industryEmptymsg = 'Milestone Value is Mandatory.';
      // this.toastr.error(industryEmptymsg, 'Error');
      const commercial_empty =
        this.retrieveMessages.length > 0
          ? this.retrieveMessages[0].errors.commercial_empty
            ? this.retrieveMessages[0].errors.commercial_empty
            : 'Commercial is Mandatory'
          : 'Commercial is Mandatory';
      this.toasterService.showWarning(commercial_empty, 10000);
      errorOccurred = true;
    } 
    else if (!this.addMemberFormGroup.get('startDate').valid) {
      const startDate_empty =
        this.retrieveMessages.length > 0
          ? this.retrieveMessages[0].errors.startDate_empty
            ? this.retrieveMessages[0].errors.startDate_empty
            : 'Start Date is Mandatory'
          : 'Start Date is Mandatory';
      this.toasterService.showWarning(startDate_empty, 10000);
      errorOccurred = true;
    } else if (
      (data.startDate === null ||
        data.startDate === undefined ||
        data.startDate === '') &&
      this.isMandate('start_date')
    ) {
      // const nameEmptymsg = 'Kindly choose when is start date?';
      // this.toastr.error(nameEmptymsg, 'Error');
      const startDate_empty =
        this.retrieveMessages.length > 0
          ? this.retrieveMessages[0].errors.startDate_empty
            ? this.retrieveMessages[0].errors.startDate_empty
            : 'Start Date is Mandatory'
          : 'Start Date is Mandatory';
      this.toasterService.showWarning(startDate_empty, 10000);
      errorOccurred = true;
    } else if (!this.addMemberFormGroup.get('endDate').valid) {
      const endDate_empty =
        this.retrieveMessages.length > 0
          ? this.retrieveMessages[0].errors.endDate_empty
            ? this.retrieveMessages[0].errors.endDate_empty
            : 'End Date is Mandatory'
          : 'End Date is Mandatory';
      this.toasterService.showWarning(endDate_empty, 10000);
      errorOccurred = true;
    } else if (
      (data.endDate === null ||
        data.endDate === undefined ||
        data.endDate === '') &&
      this.isMandate('end_date')
    ) {
      // const nameEmptymsg = 'Kindly choose When Is It due?';
      // this.toastr.error(nameEmptymsg, 'Error');
      const endDate_empty =
        this.retrieveMessages.length > 0
          ? this.retrieveMessages[0].errors.endDate_empty
            ? this.retrieveMessages[0].errors.endDate_empty
            : 'End Date is Mandatory'
          : 'End Date is Mandatory';
      this.toasterService.showWarning(endDate_empty, 10000);
      errorOccurred = true;
    } else if (
      (data.split_percentage === null ||
        data.split_percentage === undefined ||
        data.split_percentage === '') &&
      this.isMandate('split')
    ) {
      // const startDateEmptymsg ='Weighted Percentage is Mandatory.';
      // this.toastr.error(startDateEmptymsg, 'Error');
      const splitPercentage_empty =
        this.retrieveMessages.length > 0
          ? this.retrieveMessages[0].errors.splitPercentage_empty
            ? this.retrieveMessages[0].errors.splitPercentage_empty
            : 'Split Percentage is Mandatory'
          : 'Split Percentage is Mandatory';
      this.toasterService.showWarning(splitPercentage_empty, 10000);

      errorOccurred = true;
    }
     else if (
      (data.projectRole === null ||
        data.projectRole === undefined ||
        data.projectRole === '') &&
      this.isMandate('project_role')
    ) {
      // const industryEmptymsg = 'Milestone Value is Mandatory.';
      // this.toastr.error(industryEmptymsg, 'Error');
      const projectRole_empty =
        this.retrieveMessages.length > 0
          ? this.retrieveMessages[0].errors.projectRole_empty
            ? this.retrieveMessages[0].errors.projectRole_empty
            : 'Project Role is Mandatory'
          : 'Project Role is Mandatory';
      this.toasterService.showWarning(projectRole_empty, 10000);
      errorOccurred = true;
    } else if (
      (data.request_position_id === null ||
        data.request_position_id === undefined ||
        data.quote_position === '') &&
      this.isMandate('request_position_id')
    ) {
      // const industryEmptymsg = 'Milestone Value is Mandatory.';
      // this.toastr.error(industryEmptymsg, 'Error');
      const requestPosition_empty =
        this.retrieveMessages.length > 0
          ? this.retrieveMessages[0].errors.requestPosition_empty
            ? this.retrieveMessages[0].errors.requestPosition_empty
            : 'Allocation Position is Mandatory'
          : 'Allocation Position is Mandatory';
      this.toasterService.showWarning(requestPosition_empty, 10000);

      errorOccurred = true;
    } 
    else if (
      (data.secondaryProjectRole === null ||
        data.secondaryProjectRole === undefined ||
        data.secondaryProjectRole === '') &&
      this.isMandate('secondary_project_role')
    ) {
      // const industryEmptymsg = 'Milestone Value is Mandatory.';
      // this.toastr.error(industryEmptymsg, 'Error');
      const secondaryRole_empty =
        this.retrieveMessages.length > 0
          ? this.retrieveMessages[0].errors.secondaryRole_empty
            ? this.retrieveMessages[0].errors.secondaryRole_empty
            : 'Secondary Role is Mandatory'
          : 'Secondary Role is Mandatory';
      this.toasterService.showWarning(secondaryRole_empty, 10000);
      errorOccurred = true;
    }
    else if (
      (data.work_city === null ||
        data.work_city === undefined ||
        data.work_city === '') &&
      this.isMandate('work_city')
    ) {
      const work_city =
        this.retrieveMessages.length > 0
          ? this.retrieveMessages[0].errors.work_city
            ? this.retrieveMessages[0].errors.work_city
            : 'Work City is Mandatory'
          : 'Work City is Mandatory';
      this.toasterService.showWarning(work_city, 10000);
      errorOccurred = true;
    }
    else if (
      (data.work_premisis === null ||
        data.work_premisis === undefined ||
        data.work_premisis === '') &&
      this.isMandate('work_premisis')
    ) {
      const work_empty =
        this.retrieveMessages.length > 0
          ? this.retrieveMessages[0].errors.work_empty
            ? this.retrieveMessages[0].errors.work_empty
            : 'Work Premises is Mandatory'
          : 'Work Premises is Mandatory';
      this.toasterService.showWarning(work_empty, 10000);
      errorOccurred = true;
    } 
   
   
    else if (
      (data.reportsTo === null ||
        data.reportsTo === undefined ||
        data.reportsTo === '')  &&
        (data.isHead === null || data.isHead === undefined || data.isHead === '' || data.isHead === 0) &&
      this.isMandate('reports_to')
    ) {
      // const industryEmptymsg = 'Milestone Value is Mandatory.';
      // this.toastr.error(industryEmptymsg, 'Error');
      const empty =
        this.retrieveMessages.length > 0
          ? this.retrieveMessages[0].errors.reportsTo_empty
            ? this.retrieveMessages[0].errors.reportsTo_empty
            : 'Reports To is Mandatory'
          : 'Reports To is Mandatory';
      this.toasterService.showWarning(empty, 10000);
      errorOccurred = true;}
      else if (
        (this.showIdentity && this.displaynonbillable)  &&
          (data.isBillableWithIdentityCategories === null || data.isBillableWithIdentityCategories === undefined || data.isBillableWithIdentityCategories === '') &&
        this.isMandate('commercial_catageroies')
      ) {
        const empty =
          this.retrieveMessages.length > 0
            ? this.retrieveMessages[0].errors.commercial_catageroies_empty
              ? this.retrieveMessages[0].errors.commercial_catageroies_empty
              : 'Non Billable Category  is Mandatory'
            : 'Non Billable Category  is Mandatory';
        this.toasterService.showWarning(empty, 10000);
        errorOccurred = true;
      }

    else if (
        (data.travel_type === null ||
          data.travel_type === undefined ||
          data.travel_type === '') &&
        this.isMandate('travel_type')
      ) {
        const travel_type_empty =
          this.retrieveMessages.length > 0
            ? this.retrieveMessages[0].errors.travel_type_empty
              ? this.retrieveMessages[0].errors.travel_type_empty
              : 'Travel Type is Mandatory'
            : 'Travel Type is Mandatory';
        this.toasterService.showWarning(travel_type_empty, 10000);
        errorOccurred = true;
      }
      else if (
        (data.work_shift === null ||
          data.work_shift === undefined ||
          data.work_shift === '') &&
        this.isMandate('work_shift')
      ) {
        const work_shift_empty =
          this.retrieveMessages.length > 0
            ? this.retrieveMessages[0].errors.work_shift_empty
              ? this.retrieveMessages[0].errors.work_shift_empty
              : 'Shift is Mandatory'
            : 'Travel Type is Mandatory';
        this.toasterService.showWarning(work_shift_empty, 10000);
        errorOccurred = true;
      }
    
    else if (
      (data.location === null ||
        data.location === undefined ||
        data.location === '') &&
      this.isMandate('location')
    ) {
      // const industryEmptymsg = 'Milestone Value is Mandatory.';
      // this.toastr.error(industryEmptymsg, 'Error');
      const location_empty =
        this.retrieveMessages.length > 0
          ? this.retrieveMessages[0].errors.location_empty
            ? this.retrieveMessages[0].errors.location_empty
            : 'Location is Mandatory'
          : 'Location is Mandatory';
      this.toasterService.showWarning(location_empty, 10000);

      errorOccurred = true;
    } 
    else if (
      (data.reportsTo === null ||
        data.reportsTo === undefined ||
        data.reportsTo === '')  &&
        (data.isHead === null || data.isHead === undefined || data.isHead === '' || data.isHead === 0) && 
      this.isMandate('reports_to')
    ) {
      // const industryEmptymsg = 'Milestone Value is Mandatory.';
      // this.toastr.error(industryEmptymsg, 'Error');
      const empty =
        this.retrieveMessages.length > 0
          ? this.retrieveMessages[0].errors.reportsTo_empty
            ? this.retrieveMessages[0].errors.reportsTo_empty
            : 'Reports To is Mandatory'
          : 'Reports To is Mandatory';
      this.toasterService.showWarning(empty, 10000);
      errorOccurred = true;}  else if (
      (data.rate_card_id === null ||
        data.rate_card_id === undefined ||
        data.rate_card_id === '') &&
      this.isMandate('rate_card_id') &&
      !(this.internal.length > 0 && this.service_type_id == this.internal[0]?.id && this.isInternalStakeholderRateCardMandate())
    ) {
      // const industryEmptymsg = 'Milestone Value is Mandatory.';
      // this.toastr.error(industryEmptymsg, 'Error');
      if (this.withOpportunity) {
        const quotePosition_empty =
          this.retrieveMessages.length > 0
            ? this.retrieveMessages[0].errors.quotePosition_empty
              ? this.retrieveMessages[0].errors.quotePosition_empty
              : 'Quote Position is Mandatory'
            : 'Quote Position is Mandatory';
        this.toasterService.showWarning(quotePosition_empty, 10000);
      } else {
        const quotePosition_empty =
          this.retrieveMessages.length > 0
            ? this.retrieveMessages[0].errors.quotePosition_empty
              ? this.retrieveMessages[0].errors.quotePosition_empty
              : 'Rate Card Position is Mandatory'
            : 'Rate Card Position is Mandatory';
        this.toasterService.showWarning(quotePosition_empty, 10000);
      }

      errorOccurred = true;
    }
    else if (
      (data.work_premisis === null ||
        data.work_premisis === undefined ||
        data.work_premisis === '') &&
      this.isMandate('work_premisis')
    ) {
      const work_empty =
        this.retrieveMessages.length > 0
          ? this.retrieveMessages[0].errors.work_empty
            ? this.retrieveMessages[0].errors.work_empty
            : 'Work Premises is Mandatory'
          : 'Work Premises is Mandatory';
      this.toasterService.showWarning(work_empty, 10000);
      errorOccurred = true;
    } 
    else if (
      (data.work_city === null ||
        data.work_city === undefined ||
        data.work_city === '') &&
      this.isMandate('work_city')
    ) {
      const work_city =
        this.retrieveMessages.length > 0
          ? this.retrieveMessages[0].errors.work_city
            ? this.retrieveMessages[0].errors.work_city
            : 'Work City is Mandatory'
          : 'Work City is Mandatory';
      this.toasterService.showWarning(work_city, 10000);
      errorOccurred = true;
    }
    else if (
      (data.allocated_opportunity_id === null ||
        data.allocated_opportunity_id === undefined ||
        data.allocated_opportunity_id === '') &&
      this.isMandate('allocated_opportunity')
    ) {
      const allocated_opportunity_id =
        this.retrieveMessages.length > 0
          ? this.retrieveMessages[0].errors.allocated_opportunity_id
            ? this.retrieveMessages[0].errors.allocated_opportunity_id
            : 'Allocated Opportunity is Mandatory'
          : 'Allocated Opportunity is Mandatory';
      this.toasterService.showWarning(allocated_opportunity_id, 10000);
      errorOccurred = true;
    }
    else if (
      (data.shift === null ||
        data.shift === undefined ||
        data.shift === '') &&
      this.isMandate('shift')
    ) {
      const shift =
        this.retrieveMessages.length > 0
          ? this.retrieveMessages[0].errors.shift
            ? this.retrieveMessages[0].errors.shift
            : 'Work Mode is Mandatory'
          : 'Work Mode is Mandatory';
      this.toasterService.showWarning(shift, 10000);
      errorOccurred = true;
    } 
    if (errorOccurred) {
      // this.toastr.warning('Kindly fill all mandatory!', 'Warning')
      return false; // Return false if any error occurred
    } else {
      return true; // Return true if no errors occurred
    }
  }
  splitPercentKeyDown(event) {

    if (this.addMemberFormGroup.get('split_percentage').value > 100) {
      const splitPercentage_empty =
        this.retrieveMessages.length > 0
          ? this.retrieveMessages[0].errors.splitPercentage_empty
            ? this.retrieveMessages[0].errors.splitPercentage_empty
            : 'Split Percentage should be between 1 to 100!'
          : 'Split Percentage should be between 1 to 100!';
      this.toasterService.showWarning(splitPercentage_empty, 10000);
      this.addMemberFormGroup.patchValue({ ['split_percentage']: 100 });
    }
    if (this.addMemberFormGroup.get('split_percentage').value < 1) {
      const splitPercentage_empty =
        this.retrieveMessages.length > 0
          ? this.retrieveMessages[0].errors.splitPercentage_empty
            ? this.retrieveMessages[0].errors.splitPercentage_empty
            : 'Split Percentage should be between 1 to 100!'
          : 'Split Percentage should be between 1 to 100!';
      this.toasterService.showWarning(splitPercentage_empty, 10000);
      this.addMemberFormGroup.patchValue({ ['split_percentage']: '' });
    }
  }
  duplicateFormWithAddMember() {
    if ((this.mode = 'Duplicate')) {
      let res = this.data ? this.data : null;
      
      if (res) {
        // if(this.data.head==1){
        //   this.is_head=false
        // }
        this.secprojectRoleMasterList = _.filter(
          this.project_role_list,
          (res1) => {
            return res1['id'] != this.data.project_role_id;
          }
        );

        

        this.addMemberFormGroup.patchValue({
          location: this.data.location,
          split_percentage: this.data.split_percentage,
          per_hour_rate: this.data.per_hour_rate,
          endDate: moment(this.data.end_date).format('YYYY-MM-DD'),
          startDate: moment(this.data.start_date).format('YYYY-MM-DD'),
          rmg_spoc: this.data.rmg_spoc,
          isBillable: this.data.billable,
          isIncentive: this.data.incentive,
          projectRole: this.data.project_role_id,
          secondaryProjectRole:
            this.data.secondary_role != null
              ? typeof this.data.secondary_role == 'string'
                ? JSON.parse(this.data.secondary_role)
                : this.data.secondary_role
              : null,
          identity: this.data.identity,
        });

        let commercialAvailable = _.findWhere(this.formConfig, {
          type: 'add-member',
          field_name: 'commercial',
          is_active: true,
        });

        if (commercialAvailable) {
          if (this.data.billable == 1) {
            this.addMemberFormGroup.patchValue({
              isBillableWithIdentity: this.data.billable + '',
            });
          } else if (this.data.billable == 0) {
            if (this.data.identity && this.data.identity != null) {
              this.addMemberFormGroup.patchValue({
                isBillableWithIdentity: '2_' + this.data.identity + '',
              });
            }
          }
        }

        this.isComponentLoaded = true;
      }
    }
  }
  onStartChange(event: any) {
   
  }
  async addBusinessEntity(mode) {
    
    const { PmBusinessEntityListComponent } = await import(
      '../pm-business-entity-list/pm-business-entity-list.component'
    );
    const dialogRef = this.dialog.open(PmBusinessEntityListComponent, {
      // height: '75%',
      // width: '75%',
      data: {
        mode: mode,
        formConfig: this.formConfig,
        data: this.orgMappingList,
        selectedEntity: this.selectedBusinessEntity,
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
    
      if (result && result['messType'] == 'S') {
        this.businessUnitAdded = true;
        this.selectedBusinessEntity = result['data'];
        this.addMemberFormGroup.patchValue({
          entity: result['data']['entity_id'],
          division: result['data']['division_id'],
          subdivision: result['data']['sub_division_id'],
        });

        this.entity_name = result['data']['entity_name'];
        this.division_name = result['data']['division_name'];
        this.sub_division_name = result['data']['sub_division_name'];
      }
    });
  }

  compareDate(date1: any, date2: any) {
    if (
      (date1 == '' || date1 == null || date1 == undefined) &&
      (date2 == '' || date2 == null || date2 == undefined)
    ) {
      return '';
    } else if (date1 == '' || date1 == null || date1 == undefined) {
      return date2;
    } else if (date2 == '' || date2 == null || date2 == undefined) {
      return date1;
    } else {
      date1 = moment(date1).format('YYYY-MM-DD');
      date2 = moment(date2).format('YYYY-MM-DD');
      if (date1 < date2) return date1;
      else return date2;
    }
  }
  compareDateMinimum(date1: any, date2: any): string {
   
    const milestoneDate = this.getEndDate();
    const milestoneDateTimestamp = milestoneDate ? moment(milestoneDate).valueOf() : null;

    const date1Timestamp = date1 ? moment(date1).valueOf() : null;
    const date2Timestamp = date2 ? moment(date2).valueOf() : null;

    const timestamps: number[] = [milestoneDateTimestamp, date1Timestamp, date2Timestamp].filter(ts => ts !== null);

    // If no valid timestamps, return an empty string
    if (timestamps.length === 0) {
        return '';
    }

    // Return the maximum of the valid timestamps
    const maxTimestamp = Math.max(...timestamps);
    return moment(maxTimestamp).format('YYYY-MM-DD');
    
  }

  async addRateCardPosition() {
    const { PmChooseRateCardPositionComponent } = await import(
      '../pm-choose-rate-card-position/pm-choose-rate-card-position.component'
    );
    const dialogRef = this.dialog.open(PmChooseRateCardPositionComponent, {
      // height: '75%',
      // width: '75%',
      data: {
        formConfig: this.formConfig,
        rateCardDetails: this.rateCardList,
        entityList: this.entityList,
        divisionList: this.divisionList,
        subDivisionList: this.subDivisionList,
        positionList: this.positionList,
        currencyList: this.currencyList,
        rateUnitList: this.rateUnitList,
        locationList: this.rateLocationList,
        rateCardConfiguration: this.rateCardConfiguration,
        selectedRateCardId: this.addMemberFormGroup.get('rate_card_id').value,
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
     
      if (result && result['messType'] == 'S') {
        this.addMemberFormGroup.patchValue({
          rate_position_id: result['data']['position'],
          rate_currency: result['data']['currency'],
          rate_location: result['data']['work_location'],
          rate_entity: result['data']['entity'],
          rate_division: result['data']['division'],
          rate_sub_division: result['data']['sub_division'],
          rate_revenue: result['data']['revenue'],
          rate_cost: result['data']['cost'],
          rate_unit: result['data']['unit'],
          rate_card_id: result['data']['id'],
        });
      }
    });
  }

  allocatePositionMember() {
    if ((this.mode = 'Create')) {
      let res = this.data ? (this.data.item ? this.data.item : null) : null;
      
      if (res) {
        this.employeePosition = this.data.item.position_id;
        
        if (this.withOpportunity) {
          
          let quote_position = _.where(this.quote_position_list, {
            id: this.data.item.position_id,
          });
          
          if (quote_position.length > 0) {
            this.addMemberFormGroup.patchValue({
              rate_card_id: this.data.item.position_id,
              rate_position_id: quote_position[0]['position'],
              rate_currency: quote_position[0]['currency'],
              rate_location: quote_position[0]['work_location'],
              rate_entity: quote_position[0]['entity'],
              rate_division: quote_position[0]['division'],
              rate_sub_division: quote_position[0]['sub_division'],
              rate_revenue: quote_position[0]['revenue'],
              rate_cost: quote_position[0]['cost'],
              rate_unit: quote_position[0]['unit'],
              quote_start_date: quote_position[0]['quote_start_date'],
              quote_end_date: quote_position[0]['quote_end_date'],
            });

            let quotePositionConfig = _.where(this.formConfig,{type:"add-member", field_name:"enterprise_structure_quote_position", is_active: true})

            if(quotePositionConfig.length>0)
            {
                let val = {
                  entity_id: quote_position[0]['entity'],
                  entity_name: quote_position[0]['entity_name'],
                  division_id:  quote_position[0]['division'],
                  division_name: quote_position[0]['division_name'],
                  sub_division_id: quote_position[0]['sub_division'],
                  sub_division_name: quote_position[0]['sub_division_name'],
                };
                this.selectedBusinessEntity = val;
                this.businessUnitAdded = quote_position[0]['entity'] ? true : false;
                this.entity_name = quote_position[0]['entity_name'];
                this.division_name = quote_position[0]['division_name'];
                this.sub_division_name = quote_position[0]['sub_division_name'];

                this.addMemberFormGroup.patchValue({
                  entity: quote_position[0]['entity'],
                  division: quote_position[0]['division'],
                  subdivision: quote_position[0]['sub_division'],
                });
            }
          }
        }
        
        this.isComponentLoaded = true;
      }
    }
  }

  /**
   * @description Handling Field Changes for QTC Config
   */
  handleFieldChanges() {
    this.addMemberFormGroup
      .get('employee_name')
      .valueChanges.subscribe(async (res) => {
        if (res) {
          await this.getAllocatedHours(false);
        }
      });
    this.addMemberFormGroup
      .get('startDate')
      .valueChanges.subscribe(async (res) => {
        if (res) {
          await this.getAllocatedHours(false);
        }
      });
    this.addMemberFormGroup
      .get('endDate')
      .valueChanges.subscribe(async (res) => {
        if (res) {
          await this.getAllocatedHours(false);
        }
      });
      this.addMemberFormGroup
      .get('allocated_hours')
      .valueChanges.subscribe(async (res) => {
        console.log('Allocated Hours Change:',this.allocationDisable)
        if(this.allocationDisable){
          const allocatedHours = res ? parseFloat(res) : 0;  // Ensure the value is numeric and default to 0 if invalid
          const formValue = this.addMemberFormGroup.value;
          await this.calculatePercentage(formValue.total_allocated_hours, allocatedHours);
        }
      });
      this.addMemberFormGroup
      .get('split_percentage')
      .valueChanges.subscribe(async (res) => {
        console.log('Split Change:',this.allocationDisable)
        if(!this.allocationDisable){
          const formValue = this.addMemberFormGroup.value;
          await this.calculatePlannedHours(formValue.total_allocated_hours, res);
        }
      });
    // this.addMemberFormGroup
    //   .get('split_percentage')
    //   .valueChanges.subscribe(async (res) => {
    //     if (res) {
    //       if(this.checkAvailabilityVisible){
    //         if(this.checkAvailabilityClicked){
    //           await this.getRemainingBillableHoursData()
    //         }
    //       }
    //       else{
    //         await this.getAllocatedHours();
    //       }
    //     }
    //   });
      // this.addMemberFormGroup
      // .get('rate_position_id')
      // .valueChanges.subscribe(async (res) => {
      //   if (res) {
      //     if(this.checkAvailabilityVisible){
      //       if(this.checkAvailabilityClicked){
      //         await this.getRemainingBillableHoursData()
      //       }
      //     }
      //   }
      // });
      // this.addMemberFormGroup
      // .get('isBillableWithIdentity')
      // .valueChanges.subscribe(async (res) => {
      //   if (res) {
      //     await this.checkAvailabilityButtonVisible();
      //   }
      // });
  }

  /**
   * @description Calculating Total Allocated Hours Of Employee
   */
  async getAllocatedHours(initial) {
    let employee_name = this.addMemberFormGroup.get('employee_name').value ? this.addMemberFormGroup.get('employee_name').value : null
    let start_date = this.addMemberFormGroup.get('startDate').value ? moment(this.addMemberFormGroup.get('startDate').value).format('YYYY-MM-DD') : null
    let end_date = this.addMemberFormGroup.get('endDate').value ? moment(this.addMemberFormGroup.get('endDate').value).format('YYYY-MM-DD') : null

    if(employee_name && start_date && end_date && !this.saveDisabled){
      this.allocatedHrsVisible = true;
      this.checkingProgress = true;
      let item = {
        start_date : start_date,
        end_date: end_date,
        associate_id: employee_name,
        allocation_percentage : 100
      }
      let allocated_hrs_data = await this.getTotalAllocatedHours(item);
      // console.log(allocated_hrs_data)
      // const formValue = this.addMemberFormGroup.value;
      if(this.allocationDisable){
        console.log('Allocated Hours Change:',this.allocationDisable)
        if(allocated_hrs_data && allocated_hrs_data['total_allocated_hours']){
          let total_allocated_hours =  allocated_hrs_data['total_allocated_hours']
          let allocated_hours = (allocated_hrs_data['remaining_planned_allocted_hours'] &&  allocated_hrs_data['remaining_planned_allocted_hours'] > 0) ? allocated_hrs_data['remaining_planned_allocted_hours'] : 0;

          total_allocated_hours = parseFloat(total_allocated_hours.toFixed(2));
          allocated_hours = parseFloat(allocated_hours.toFixed(2));
          console.log('INitial',initial)
          if(initial){
            this.addMemberFormGroup.get('total_allocated_hours').patchValue(total_allocated_hours);
            this.addMemberFormGroup.get('remaining_billable_hours').patchValue(allocated_hours);
            let formValue = this.addMemberFormGroup.value
            this.calculatePercentage(total_allocated_hours,formValue.allocated_hours);
          }
          else{
            this.addMemberFormGroup.get('total_allocated_hours').patchValue(total_allocated_hours);
            this.addMemberFormGroup.get('remaining_billable_hours').patchValue(allocated_hours);
            this.addMemberFormGroup.get('allocated_hours').patchValue(allocated_hours);
            // this.calculatePercentage(total_allocated_hours,total_allocated_hours)
            let formValue = this.addMemberFormGroup.value
            this.calculatePercentage(total_allocated_hours,formValue.allocated_hours);
          }
        }
        else{
          this.addMemberFormGroup.get('split_percentage').patchValue('');
          this.addMemberFormGroup.get('total_allocated_hours').patchValue(0);
          this.addMemberFormGroup.get('remaining_billable_hours').patchValue(0);
          this.addMemberFormGroup.get('allocated_hours').patchValue(0);
        }
      }
      else{
        console.log('Allocated Hours Change:',this.allocationDisable)
        let formValue = this.addMemberFormGroup.value
        if(formValue.split_percentage == '' || formValue.split_percentage == undefined || formValue.split_percentage == null){
          this.addMemberFormGroup.get('split_percentage').patchValue(100);
        }
        if(allocated_hrs_data && allocated_hrs_data['total_allocated_hours']){
          let total_allocated_hours =  allocated_hrs_data['total_allocated_hours']
          let allocated_hours = (allocated_hrs_data['remaining_planned_allocted_hours'] &&  allocated_hrs_data['remaining_planned_allocted_hours'] > 0) ? allocated_hrs_data['remaining_planned_allocted_hours'] : 0;
          // console.log('INitial',initial)
          this.addMemberFormGroup.get('total_allocated_hours').patchValue(total_allocated_hours);
          this.addMemberFormGroup.get('remaining_billable_hours').patchValue(allocated_hours);
          formValue = this.addMemberFormGroup.value
          this.calculatePlannedHours(total_allocated_hours,formValue.split_percentage)
        }
        else{
          this.addMemberFormGroup.get('total_allocated_hours').patchValue(0);
          this.addMemberFormGroup.get('remaining_billable_hours').patchValue(0);
          this.addMemberFormGroup.get('allocated_hours').patchValue(0);

        }
      }
      
      this.checkingProgress = false;
    }
    else if(!this.saveDisabled){
      this.allocatedHrsVisible = false;
      this.addMemberFormGroup.get('total_allocated_hours').patchValue('');
      this.addMemberFormGroup.get('remaining_billable_hours').patchValue('');
      this.addMemberFormGroup.get('allocated_hours').patchValue('');
      if(this.allocationDisable){
        this.addMemberFormGroup.get('split_percentage').patchValue('');
      }
    }
  }

  async getMilestoneEndDate(){
    try {
      const res = await this.PmInternalStakeholderService.getMilestoneEndDate(this.projectId,this.itemId);
      if(res['messType'] == 'S'){
        return res['data'];
      }
      else{
        return null;
      }
    } catch (error) {
        console.error('Error fetching finance hours:', error);
        return null;
    }
  }

  /**
   * @description For Calculating Allocated Hrs For Employee
   */
  getAllocatedHrs(item){
    let startDate = moment(item.start_date).format('YYYY-MM-DD');
    let endDate = moment(item.end_date).format('YYYY-MM-DD')
    return new Promise(async (resolve, reject) => {
      this.subs.sink = (await this.PmInternalStakeholderService
        .getEmployeeAllocatedHrs(startDate, endDate, item?.associate_id))
        .subscribe(
          (res: any) => {
            if (res['messType'] == 'S') resolve(res.data);
            else resolve(null);
          },
          (err) => {
            console.log(err);
            resolve(null);
          }
        );
    });
  }

  /**
   * @description Calculating Balance Hours of Position
   */
  // async checkAvailabilityButtonVisible() {
    
  //   if(this.withOpportunity){
  //     let commercialValue = this.addMemberFormGroup.get('isBillableWithIdentity').value;
  //     if (commercialValue == '1') {
  //       this.checkAvailabilityVisible = true;
  //     } 
  //     // else if (commercialValue.includes('2')) {
  //     //   let billable_identity = commercialValue.split('_');
  //     //   if(billable_identity[1] == '2'){
  //     //     this.checkAvailabilityVisible = true;
  //     //   }
  //     //   else{
  //     //     this.checkAvailabilityVisible = false;
  //     //     await this.getAllocatedHours();
  //     //   }
  //     // }
  //     else{
  //       this.checkAvailabilityVisible = false;
  //       await this.getAllocatedHours();
  //     }
  //   }
  //   else{
  //     this.checkAvailabilityVisible = false;
  //     await this.getAllocatedHours();
  //   }
  // }

  /**
   *@description Clicking on checking availability 
   */
  // async checkAvailability(){
  //   this.checkingProgress = true;
  //   await this.getRemainingBillableHoursData();
  //   this.checkingProgress = false;
  // this.checkAvailabilityClicked = true;
  // }

  /**
   * @description Fetching Remaining Billable Hours
   */
  async getRemainingBillableHoursData(){
    const formValue = this.addMemberFormGroup.value;
    console.log('Form Value:',formValue)
    let employee_name = formValue.employee_name || null;
    let start_date = formValue.startDate ? moment(formValue.startDate).format('YYYY-MM-DD') : null;
    let end_date = formValue.endDate ? moment(formValue.endDate).format('YYYY-MM-DD') : null;
    let allocation_percentage = formValue.split_percentage || null;
    let position = formValue.rate_card_id || null;
    let billable = formValue.isBillable || 0;
    let position_type = this.withOpportunity ? 'QTC' : 'OTC'
    let allocated_hours = formValue.allocated_hours || 0;

    let data = await this.remainingBalanceCheck(employee_name,start_date,end_date,allocation_percentage,position,billable,position_type,allocated_hours)
    this.allowAllocationCheck =  (this.withOpportunity && this.blanketPo) ? true : (data &&  data['allow_allocation']) ? (data['allow_allocation']) : false 
  }

  // /**
  //  * 
  //  * @param data 
  //  * @returns 
  //  */
  // async getFinanceHours(employee_name,start_date,end_date,allocation_percentage,position) {
  //   try {
  //       const res = await this.PmInternalStakeholderService.getPlannedAllocatedAndRemainingBillableHours(this.itemId, position, 'QTC',start_date,end_date,allocation_percentage,employee_name);
  //       return res;
  //   } catch (error) {
  //       console.error('Error fetching finance hours:', error);
  //       return null;
  //   }
  // }

  /**
   * @description To prevent Entering Date 
   * @param event 
   */
  onKeyDownDate(event: KeyboardEvent,type) {
    event.preventDefault();
    if(type == 'S'){
      this.datepicker.open();
    }
    else if(type == 'E'){
      this.datepicker1.open();
    }    
  }

  openDatePicker(type) {
    if(type == 'S'){
      this.datepicker.open();
    }
    else if(type == 'E'){
      this.datepicker1.open();
    }  
  }

  onMouseEnter() {
    this.imageUrl = 'https://assets.kebs.app/system-solid-23-calendar (1) (1).gif';
  }

  onMouseLeave() {
    this.imageUrl = this.imageAniUrl;
  }

  getEndDate(){
    // if(this.checkAvailabilityVisible){
    //   if(this.milestoneEndDate){
    //     return this.milestoneEndDate;
    //   }
    //   else{
    //     return null
    //   }
    // }
    return null
  }

  @HostListener('window:resize')
  onResize() {
    this.calculateDynamicStyle();
  }

  calculateDynamicStyle(){
    let allocationHeight = window.innerHeight - 213 + 'px'
    document.documentElement.style.setProperty(
      '--dynamicCardHeight',
      allocationHeight
    )
  }

  /**
   * @description Fetching Total Allocated Hours for an Employee
   * @returns 
   */
  getTotalAllocatedHours(item) {
    let isa_id = (this.data && this.data.isa_id) ? this.data.isa_id : null
    return new Promise((resolve,reject) => {
      this.subs.sink = this.PmInternalStakeholderService.getPlannedAllocatedHours(this.itemId,item.start_date,item.end_date,item.allocation_percentage,item.associate_id,isa_id).subscribe(
         (res:any) => {
          
          if(res && res['messType'] == 'S') {
             resolve(res)
          } 
          else{
            resolve(null);
          }
        },
        (err) => {
          console.log(err);
          this.toasterService.showError('Failed to fetch Allocated Hours')
          resolve(null);
        }
      );
    });
   }

   /**
    * @description calculation Allocation/Split Percentage
    * @param total_hours 
    * @param allocated_hours 
    */
   calculatePercentage(total_hours:number,allocated_hours:number){
    // console.log('Total Calculated:',total_hours)
    // console.log('Allocated Calculated:',allocated_hours)
    if(total_hours != 0 && allocated_hours){
      let percentcalculation = (allocated_hours / total_hours) * 100
      percentcalculation = parseFloat(percentcalculation.toFixed(2))
      this.addMemberFormGroup.get('split_percentage').patchValue(percentcalculation)
    }
    else{
      this.addMemberFormGroup.get('split_percentage').patchValue('')
    }
   }

   /**
    * @description calculation Planned Hours
    * @param total_hours 
    * @param percentage 
    */
   calculatePlannedHours(total_hours:number,percentage:number){
    if(percentage && total_hours){
      let plannedValue = (percentage * total_hours) / 100;
      plannedValue = parseFloat(plannedValue.toFixed(2))
      this.addMemberFormGroup.get('allocated_hours').patchValue(plannedValue)
    }
    else{
      this.addMemberFormGroup.get('allocated_hours').patchValue(0)
    } 
   }

  /**
   * @description Checking for Balance Available or not
   * @param employee_name 
   * @param start_date 
   * @param end_date 
   * @param allocation_percentage 
   * @param position 
   */
  remainingBalanceCheck(employee_name,start_date,end_date,allocation_percentage,position,billable,position_type,allocated_hours){
    console.log('Data:',this.data)
    let isa_id = (this.data && this.data.isa_id) ? this.data.isa_id : null
    return new Promise((resolve,reject) => {
      this.subs.sink = this.PmInternalStakeholderService.getPlannedAllocatedAndRemainingBillableHours(this.itemId,position,position_type,start_date,end_date,allocation_percentage,employee_name,billable,isa_id,allocated_hours).subscribe(
         (res:any) => {
          
          if(res) {
             resolve(res)
          } 
          else{
            this.toasterService.showError('Failed in Checking Quote Position Remaining Balance')
            resolve(null);
          }
        },
        (err) => {
          console.log(err);
          this.toasterService.showError('Failed in Checking Quote Position Remaining Balance')
          resolve(null);
        }
      );
    });
  }

  /**
   * @description Getting Min Date
   * @param projectEndDate 
   * @param e360EndDate 
   * @returns 
   */
  getMinDate(projectEndDate, e360EndDate) {
    if (!projectEndDate && !e360EndDate) {
      return null;
    }
    if (!projectEndDate) {
      return e360EndDate;
    }
    if (!e360EndDate) {
      return projectEndDate;
    }
    return projectEndDate > e360EndDate ? e360EndDate : projectEndDate ;
  }

  // getWorkCityList(){
   
  //   return new Promise(async (resolve,reject) => {
  //     this.subs.sink = (await this.PmInternalStakeholderService.getWorkCityList()).subscribe(
  //        (res:any) => {
          
  //         if(res.messType == 'S' && res.data && res.data.length > 0) {
  //            resolve(res.data)
  //         } 
  //         else{
  //           resolve([]);
  //         }
  //       },
  //       (err) => {
  //         console.log(err);
  //         this.toasterService.showError('Error in Fetching Work City List')
  //         resolve([]);
  //       }
  //     );
  //   });
  // }

  getWorkPremisisList(){
   
    return new Promise(async (resolve,reject) => {
      this.subs.sink = (await this.PmInternalStakeholderService.getWorkPremisisList()).subscribe(
         (res:any) => {
          
          if(res.messType == 'S' && res.data && res.data.length > 0) {
             resolve(res.data)
          } 
          else{
            resolve([]);
          }
        },
        (err) => {
          console.log(err);
          this.toasterService.showError('Error in Fetching Work Premisis List')
          resolve([]);
        }
      );
    });
  }

  getTravelTypeList(){
   
    return new Promise(async (resolve,reject) => {
      this.subs.sink = (await this.PmInternalStakeholderService.getTravelTypeList()).subscribe(
         (res:any) => {
          
          if(res.messType == 'S' && res.data && res.data.length > 0) {
             resolve(res.data)
          } 
          else{
            resolve([]);
          }
        },
        (err) => {
          console.log(err);
          this.toasterService.showError('Error in Fetching Travel Type List')
          resolve([]);
        }
      );
    });
  }

  getWorkShiftMasterList(){
   
    return new Promise(async (resolve,reject) => {
      this.subs.sink = (await this.PmInternalStakeholderService.getWorkShiftMasterList()).subscribe(
         (res:any) => {
          
          if(res.messType == 'S' && res.data && res.data.length > 0) {
             resolve(res.data)
          } 
          else{
            resolve([]);
          }
        },
        (err) => {
          console.log(err);
          this.toasterService.showError('Error in Fetching Work Shift List')
          resolve([]);
        }
      );
    });
  }

  ngDestroy() {
    this.PmInternalStakeholderService.setAllocationData(null);
  }


  async checkDayWizeEmployeeHours(mode, isa_id){

    return new Promise(async(resolve, reject)=>{
      let day_wize = _.findWhere(this.formConfig,{type:"add-member", field_name:"day-wize-check", is_active: true})

      if(day_wize)
      {
          const formValue = this.addMemberFormGroup.value;
          
          let employee_name = formValue.employee_name || null;
          let start_date = formValue.startDate ? moment(formValue.startDate).format('YYYY-MM-DD') : null;
          let end_date = formValue.endDate ? moment(formValue.endDate).format('YYYY-MM-DD') : null;
          let allocation_percentage = formValue.split_percentage || null;

          await this.PmInternalStakeholderService.checkDayWizeEmployeeHours(start_date, end_date, allocation_percentage, employee_name, mode, isa_id,this.projectID,this.itemId).then((res)=>{
            if(res['messType']=="S")
            {
                if(res['data'])
                {
                    resolve(true);
                }
                else
                {
                    resolve(false);
                }
            }
            else
            {
              resolve(false);
            }
          })
      }
      else
      {
        resolve(true);
      }
    })
  }

  /**
   * @description Handling Section Header Changes
   */
  handleHeaderChanges(){
    this.sectionHeaderList.sort((a, b) => a['position'] - b['position']);
    if(!this.withOpportunity){
      let header_name_rate_card = _.findWhere(this.formConfig, {
        type: 'section-header',
        field_name: 'rate-card',
        is_active: true,
      });
      let header_name = header_name_rate_card && header_name_rate_card['label'] ? header_name_rate_card['label'] : 'Rate Card Details';
      this.sectionHeaderList = this.sectionHeaderList.map(section => {
        if (section && (section['sectionId'] == 'quote')) {
            return {
                ...section,
                sectionName: header_name
            };
        }
        return section;
    });
    }
  }

  onCustomSelectChanges(event){
    // console.log(event,'Coty Evebt')
    this.addMemberFormGroup.get('work_city').patchValue(event.val ? event.val : null)
   }

   async checkEditDisableAccess(){
    if(this.mode == 'Edit'){
      let objectAccess = await this.authService.getProjectObjectAccess(350);

      if(objectAccess){
        return false;
      }

      let superAdminAccess = false;
      await this.authService.getAdminAccess().then(async (res) => {
        if (res) {
          superAdminAccess = (res['messType'] && res['messType'] == 'S') ? true : false;
        }
      });

      if(superAdminAccess){
        return false;
      }

      return true;
    }
    return false;
   }
}
