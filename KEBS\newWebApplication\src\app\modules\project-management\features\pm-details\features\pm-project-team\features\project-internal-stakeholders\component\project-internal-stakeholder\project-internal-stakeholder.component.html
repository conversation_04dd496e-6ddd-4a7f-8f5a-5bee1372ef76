<div class="container-fluid isa-landing">
  <div class="loader-container" *ngIf="loading && qtcLoader">
    <mat-spinner class="green-spinner loader" diameter="40"></mat-spinner>
  </div>
  <span *ngIf="!loading && !qtcLoader">
    <div class="icons">
      <div class="search-container" [ngStyle]="{'margin-right': hideCreation ? '-15px' : '-26px'}" cdkOverlayOrigin
        #triggerSearchBar="cdkOverlayOrigin" #triggerSearchField (click)="openSearchBarOverlay(triggerSearchField)">
        <mat-icon class="search-icon">search</mat-icon>
      </div>
      <div class="customize">
        <mat-icon class="customize-icon" (click)="columnConfigToggle.toggle()" [satPopoverAnchor]="columnConfigToggle"
          tooltip="Column Selection">display_settings</mat-icon>

        <!-- <mat-icon class="customize-icon" (click)="columnConfigToggle.toggle()" [satPopoverAnchor]="columnConfigToggle"
          tooltip="Column Selection">display_settings</mat-icon> -->
      </div>
      <div class="filter">
        <mat-icon class="filter-icon" tooltip="Filter" [satPopoverAnchor]="showFilterPopup"
          (click)="showFilterPopup.toggle()">filter_list</mat-icon>
        <div *ngIf="tags.length>0" style="    position: absolute;
          margin-left: 14px;
          background: #F27A6C;
          border-radius: 50%;
          width: 17px;
          height: 17px;
          display: flex;
          justify-content: center;
          margin-top: -37px;
          font-size: 11px;
          font-weight: bold;
          color: white;">{{this.tags.length}}</div>
      </div>
      <div class="add-people" (click)="addMember()" *ngIf="!hideCreation">
        <mat-icon
          tooltip="{{('allocate_resource_name' | checkLabel : this.formConfig: 'add-member': 'Allocate Resource')}}"
          class="add-icon">person_add</mat-icon>
      </div>
      <!-- <div class="add-people" (click)="deMobilizeBulk()" *ngIf="hideCreation">
        <mat-icon tooltip ="{{('q2c_display' | checkLabel : this.formConfig: 'project-team': 'DeMobilize Resource')}}" class="add-icon" style="color: #EE4961 !important;">person</mat-icon>
      </div> -->
      <sat-popover #showFilterPopup horizontalAlign="after" verticalAlign="below" hasBackdrop>

        <div class="card" style="width: 500px;">
          <div class="row filter-header">
            <div class="filter-text">Filters</div>
          </div>
          <div class="row" *ngIf="tags.length > 0" class="selected-tags">
            <div *ngFor="let chip of tags">
              <div class="tag-chip">
                <mat-icon class="tag-preffix" [style.color]="(chip.color ? chip.color : 'grey')">{{chip.icon ? chip.icon
                  : 'cirlce'}}</mat-icon>
                <div class="tag-text" tooltip="{{chip.name}}">
                  {{ chip.name | maxellipsis: 5 }}
                </div>
                <mat-icon class="tag-clear-icon"
                  (click)="closeTagFilter(chip.name,chip.type,chip.color,chip.i,chip.icon)">clear</mat-icon>
              </div>
            </div>
          </div>
          <div class="row selected-tags" *ngIf="tags.length == 0" style="padding-left: 15px; color:'grey'">
            {{('noFilter_selected' | checkLabel : this.formConfig: 'risk-register-filter': 'No Filter selected')}}
          </div>
          <div class="row filter-body">
            <!-- status -->
            <div>
              <div class="drop-down" [satPopoverAnchor]="showRole"
                (click)="showRole.toggle();expandFilterToggle('role')">
                <div class="drop-down-text">
                  {{('role-filter' | checkLabel : this.formConfig: 'project-team-filter': 'Project Role')}}
                </div>
                <mat-icon class="drop-down-icon">keyboard_arrow_down</mat-icon>
              </div>
            </div>
            <sat-popover #showRole horizontalAlign="start" verticalAlign="below" hasBackdrop>
              <div class="card" style="padding: 11px;">
                <div class="search-filter">
                  <mat-icon class="filter-search-icon">search</mat-icon>
                  <input type="text" [(ngModel)]="searchText" placeholder="Search"
                    style="margin-bottom: 10px;padding: 5px;border: none;outline: none;font-size: 12px;">
                </div>
                <div class="filter-list-body">
                  <div class="row" style="padding-left: 10px;" *ngFor="let role of filteredList('role');let i=index">
                    <mat-checkbox class="checkbox-input" [(ngModel)]="role.is_selected"
                      (change)="applyFilter(role.name,'role',role.is_selected,role.color,i,role.icon)"
                      style="margin: 0px !important">
                    </mat-checkbox>
                    <div style="display: flex; margin-top: -2px;">
                      <mat-icon style="font-size: 20px;margin-left: 10px;"
                        [style.color]="(role.color ? role.color : 'grey')">
                        {{role.icon ? role.icon : 'cirlce'}}
                      </mat-icon>
                      <div style="font-size: 12px;
                            margin-left: 5px;
                            color: #45546E;">
                        {{role.name}}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </sat-popover>
            <div>
              <div class="drop-down" [satPopoverAnchor]="showStatus"
                (click)="showStatus.toggle();expandFilterToggle('status')">
                <div class="drop-down-text">
                  {{('status-filter' | checkLabel : this.formConfig: 'project-team-filter': 'Status')}}
                </div>
                <mat-icon class="drop-down-icon">keyboard_arrow_down</mat-icon>
              </div>
            </div>
            <sat-popover #showStatus horizontalAlign="start" verticalAlign="below" hasBackdrop>
              <div class="card" style="padding: 11px;">
                <div class="search-filter">
                  <mat-icon class="filter-search-icon">search</mat-icon>
                  <input type="text" [(ngModel)]="searchText" placeholder="Search"
                    style="margin-bottom: 10px;padding: 5px;border: none;outline: none;font-size: 12px;">
                </div>
                <div class="filter-list-body">
                  <div class="row" style="padding-left: 10px;"
                    *ngFor="let status of filteredList('status');let i=index">
                    <mat-checkbox class="checkbox-input" [(ngModel)]="status.is_selected"
                      (change)="applyFilter(status.name,'status',status.is_selected,status.color,i,status.icon)"
                      style="margin: 0px !important">
                    </mat-checkbox>
                    <div style="display: flex; margin-top: -2px;">
                      <mat-icon style="font-size: 20px;margin-left: 10px;"
                        [style.color]="(status.color ? status.color : 'grey')">
                        {{status.icon ? status.icon : 'cirlce'}}
                      </mat-icon>
                      <div style="font-size: 12px;
                            margin-left: 5px;
                            color: #45546E;">
                        {{status.name}}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </sat-popover>
            <div>
              <div class="drop-down" [satPopoverAnchor]="showCommercial"
                (click)="showCommercial.toggle();expandFilterToggle('commercial')">
                <div class="drop-down-text">
                  {{('commercial-filter' | checkLabel : this.formConfig: 'project-team-filter': 'Commercial')}}
                </div>
                <mat-icon class="drop-down-icon">keyboard_arrow_down</mat-icon>
              </div>
            </div>
            <sat-popover #showCommercial horizontalAlign="start" verticalAlign="below" hasBackdrop>
              <div class="card" style="padding: 11px;">
                <div class="search-filter">
                  <mat-icon class="filter-search-icon">search</mat-icon>
                  <input type="text" [(ngModel)]="searchText" placeholder="Search"
                    style="margin-bottom: 10px;padding: 5px;border: none;outline: none;font-size: 12px;">
                </div>
                <div class="filter-list-body">
                  <div class="row" style="padding-left: 10px;"
                    *ngFor="let comercial of filteredList('commercial');let i=index">
                    <mat-checkbox class="checkbox-input" [(ngModel)]="comercial.is_selected"
                      (change)="applyFilter(comercial.name,'comercial',comercial.is_selected,comercial.color,i,comercial.icon)"
                      style="margin: 0px !important">
                    </mat-checkbox>
                    <div style="display: flex; margin-top: -2px;">
                      <mat-icon style="font-size: 20px;margin-left: 10px;"
                        [style.color]="(comercial.color ? comercial.color : 'grey')">
                        {{comercial.icon ? comercial.icon : 'cirlce'}}
                      </mat-icon>
                      <div style="font-size: 12px;
                            margin-left: 5px;
                            color: #45546E;">
                        {{comercial.name}}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </sat-popover>
          </div>
          <div class="row filter-footer">
            <div class="button">
              <div class="clear-button" (click)="clearFilter()">
                <div class="clear-button-text">
                  Clear
                </div>
              </div>
            </div>
          </div>

        </div>


      </sat-popover>
      <sat-popover #columnConfigToggle horizontalAlign="center" verticalAlign="below" hasBackdrop
        class="overlay-popover">
        <div class="card-column card">
          <div class="column-config-popup">
            <div class="column-list">
              <span *ngFor="let column of columns;let i=index">
                <div class="row" *ngIf="column.isActive">
                  <label class="toggle-switch" [ngClass]="{ 'grayed-out-toggle': column.isDisabled }"
                    [style.background-color]="column.isDisabled ? 'grey' : (column.isVisible ? button : 'lightgrey')">
                    <input type="checkbox" [(ngModel)]="column.isVisible" [disabled]="column.isDisabled"
                      (click)="saveISALandingPageUserConfig()">
                    <span class="slider"></span>
                  </label>
                  <span class="column-header">{{ column.name }}</span>
                </div>
              </span>
            </div>
          </div>

        </div>
      </sat-popover>
    </div>
    <div *ngIf="!hideCreation">
      <div class="column-table" *ngIf="!hideCreation">
        <div *ngIf="data.length>0" class="row sticky-top column">
          <div class="column-expand">
            <div class="expand">
              <!-- {{ columns[0].name }} -->
              <mat-icon class="expand-icon" *ngIf="allExpand" (click)="collapseAll()">keyboard_arrow_down</mat-icon>
              <mat-icon class="expand-icon" *ngIf="!allExpand" (click)="expandAll()">keyboard_arrow_right</mat-icon>
            </div>
          </div>
          <div class="column-AID" *ngIf="this.columns[1].isVisible">
            <div class="AID">
              {{ columns[1].name }}
            </div>
          </div>
          <div class="column-external" *ngIf="this.columns[2].isVisible && enable_external_employee_id">
            <div class="external_id">
              {{ columns[2].name }}
            </div>
          </div>
          <div class="column-name" *ngIf="this.columns[3].isVisible">
            <div class="name">
              {{ columns[3].name }}
            </div>
          </div>
          <div class="column-cc" *ngIf="this.columns[4].isVisible">
            <div class="cc">
              {{ columns[4].name }}
            </div>
          </div>
          <div class="column-commercial" *ngIf="this.columns[5].isVisible">
            <div class="commercial">
              {{ columns[5].name }}
            </div>
          </div>
          <div class="column-role" *ngIf="this.columns[6].isVisible">
            <div class="role">
              {{ columns[6].name }}
            </div>
          </div>
          <div class="column-allocated" *ngIf="this.columns[7].isVisible">
            <div class="allocated">
              {{ columns[7].name }}
            </div>
          </div>
          <div class="column-start-date" *ngIf="this.columns[8].isVisible">
            <div class="start-date">
              {{ columns[8].name }}
            </div>
          </div>
          <div class="column-end-date" *ngIf="this.columns[9].isVisible">
            <div class="end-date">
              {{ columns[9].name }}
            </div>
          </div>
          <div class="column-status" *ngIf="this.columns[10].isVisible">
            <div class="status">
              {{ columns[10].name }}
            </div>
          </div>
          <div class="column-action" *ngIf="this.columns[11].isVisible">
            <div class="action">
              {{ columns[11].name }}
            </div>
          </div>
        </div>
        <!-- <div *ngIf="data.length>0" class="line"></div> -->
        <div *ngIf="data.length>0" class="body-size">
          <div class="row-body" *ngFor="let item of parentData">
            <div class="row-body-1">
              <div class="row-body-2">
                <div class="parent-body">
                  <div class="parent-body-1">
                    <div class="row-expand">
                      <!-- <div style="width: 16px; height: 16px; left: 0px; top: 0px; position: absolute"></div>
                    <div style="width: 8.49px; height: 5.19px; left: 3.76px; top: 5.48px; position: absolute; background: #526179"></div> -->
                      <mat-icon class="expand-icon" *ngIf="item['expand']"
                        (click)="collapse(item['associate_id'])">keyboard_arrow_down</mat-icon>
                      <mat-icon class="expand-icon" *ngIf="!item['expand']"
                        (click)="expand(item['associate_id'])">keyboard_arrow_right</mat-icon>
                    </div>
                    <div class="parent-row-aid-1" *ngIf="this.columns[1].isVisible">
                      
                      <div class="parent-row-aid">{{item['associate_id']}}</div>
                    </div>
                    <div class="parent-row-external-id"
                      *ngIf="this.columns[2].isVisible && enable_external_employee_id">{{item['external_employee_id'] }}
                    </div>
                    <div class="parent-row-name" *ngIf="this.columns[3].isVisible">
                      <!-- <img style="width: 18px; height: 18px; border-radius: 9999px" src="https://assets.kebs.app/images/User.png" /> -->
                      <app-app-user-image class="profile-img" [id]="item['oid']" imgWidth="18px" imgHeight="18px"
                        style="margin-top: -4px;">
                      </app-app-user-image>
                      <div class="parent-row-name-1" *ngIf="this.columns[3].isVisible">{{item['name']}}</div>
                    </div>
                    <div class="parent-row-cc" *ngIf="this.columns[4].isVisible">{{item['split_percentage']}}%</div>
                    <div *ngIf="this.columns[5].isVisible"
                      style="height: 16px; justify-content: flex-start; align-items: flex-start; gap: 4px; display: flex">
                      <div class="font-family"
                        style="width: 100px; opacity: 0; color: #7D838B; font-size: 11px;font-weight: 400; line-height: 16px; word-wrap: break-word">
                        {{item['commercial']+" "+(this.showIdentity ? item['identity_name'] :"")}}</div>
                    </div>
                    <div class="parent-row-role" *ngIf="this.columns[6].isVisible">{{item['role']}}</div>
                    <div class="parent-row-allocated" *ngIf="this.columns[7].isVisible">{{item['allocated_hours']}}
                    </div>
                    <div class="parent-row-start-date" *ngIf="this.columns[8].isVisible">{{item['start_date']}}</div>
                    <div class="parent-row-end-date" *ngIf="this.columns[9].isVisible">{{item['end_date']}}</div>
                    <div *ngIf="this.columns[10].isVisible"
                      style="margin-left: -176px;width: 179px; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: inline-flex">
                      <div [style.background]="item['status_color'].palate"
                        style="border-radius: 8px; justify-content: center; align-items: center; gap: 4px; display: inline-flex">
                        <div [style.color]="item['status_color'].color" class="font-family"
                          style="font-size: 12px;font-weight: 400; text-transform: capitalize; line-height: 16px; letter-spacing: 0.24px; word-wrap: break-word;padding-right: 6px;padding-left: 6px;">
                          {{item['status']}}</div>
                      </div>
                    </div>
                  </div>
                </div>
                <div *ngFor="let items of data">
                  <div class="child-body" *ngIf="item['associate_id']==items['associate_id'] && items['expand']==1">
                    <div class="child-body-1">
                      <div class="child-body-2">
                        <div class="child-expand">
                          <!-- <div style="width: 16px; height: 16px; left: 0px; top: 0px; position: absolute"></div>
                      <div style="width: 8.49px; height: 5.19px; left: 3.76px; top: 5.48px; position: absolute; background: #526179"></div> -->
                        </div>
                        <div class="parent-row-aid-1" *ngIf="this.columns[1].isVisible">
                          <mat-icon *ngIf="items['head']==1"
                          style="font-size: 120%; fill: currentColor; height: 20px; width: 20px;" 
                          class="person-icon" 
                          tooltip="Project Head">
                  person_outline
                </mat-icon>
                          <div style="margin-left: 5%;" class="child-row-aid">{{items['associate_id']}}</div>
                         
                        </div>
                        <div class="child-row-external-id"
                          *ngIf="this.columns[2].isVisible && enable_external_employee_id">
                          {{items['external_employee_id'] }}</div>
                        <div class="child-row-name" *ngIf="this.columns[3].isVisible" tooltip="{{items['name']}}">
                          <!-- <img style="width: 18px; height: 18px; border-radius: 9999px" src="https://assets.kebs.app/images/User.png" /> -->
                          <app-app-user-image class="profile-img" [id]="items['oid']" imgWidth="18px" imgHeight="18px"
                            style="margin-top: -4px;">
                          </app-app-user-image>
                          <div class="child-row-name-1">{{items['name']}}</div>
                        </div>
                        <div class="child-row-cc" *ngIf="this.columns[4].isVisible">{{items['split_percentage'] |
                          decimal:1 }}%</div>
                        <div
                          style="height: 16px; justify-content: flex-start; align-items: flex-start; gap: 4px; display: flex"
                          *ngIf="this.columns[5].isVisible">
                          <div class="font-family"
                            style="width: 100px; color: #45546E; font-size: 11px; font-weight: 400; line-height: 16px; word-wrap: break-word">
                            {{items['commercial']+" "+(this.showIdentity ? items['identity_name'] :"")}}</div>
                        </div>
                        <div class="child-row-role" *ngIf="this.columns[6].isVisible" tooltip="{{items['role']}}">
                          {{items['role']}}</div>
                        <div class="child-row-allocated" *ngIf="this.columns[7].isVisible">{{items['allocated_hours'] |
                          decimal:1 }}</div>
                        <div class="child-row-start-date" *ngIf="this.columns[8].isVisible">{{items['start_date']}}
                        </div>
                        <div class="child-row-end-date" *ngIf="this.columns[9].isVisible">{{items['end_date']}}</div>
                        <div
                          style="width: 185px; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: inline-flex"
                          *ngIf="this.columns[10].isVisible">
                          <div [style.background]="items['status_color'].palate"
                            style="border-radius: 8px; justify-content: center; align-items: center; gap: 4px; display: inline-flex">
                            <div [style.color]="items['status_color'].color" class="font-family"
                              style="font-size: 12px;font-weight: 400; text-transform: capitalize; line-height: 16px; letter-spacing: 0.24px; white-space: nowrap;padding-right: 6px;padding-left: 6px;">
                              {{items['status']}}</div>
                          </div>
                        </div>
                        <div *ngIf="this.columns[11].isVisible"
                          style="justify-content: flex-start; align-items: flex-start; gap: 16px; display: flex;margin-left: -8px;">
                          <div style="width: 16px; height: 16px; position: relative;cursor: pointer;" (click)="deleteISA(items)">
                            <div *ngIf="!items['is_form_required']">
                              <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <mask id="mask0_19813_15919" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="16" height="16">
                                  <rect width="16" height="16" fill="#D9D9D9" />
                                </mask>
                                <g mask="url(#mask0_19813_15919)">
                                  <path
                                    d="M7.99968 13.3315C8.59202 13.3315 9.16318 13.2379 9.71318 13.0507C10.2632 12.8636 10.7681 12.5897 11.2278 12.229C10.7595 11.8811 10.2525 11.6168 9.70668 11.436C9.1609 11.2552 8.59185 11.1649 7.99952 11.1649C7.40729 11.1649 6.83618 11.2531 6.28618 11.4295C5.73618 11.6061 5.23129 11.8726 4.77152 12.229C5.23129 12.5897 5.73618 12.8636 6.28618 13.0507C6.83618 13.2379 7.40735 13.3315 7.99968 13.3315ZM9.63818 7.75585L8.92535 7.04302C9.00224 6.94135 9.06163 6.82841 9.10352 6.70419C9.1454 6.57997 9.16635 6.45574 9.16635 6.33152C9.16635 6.01097 9.05202 5.73641 8.82335 5.50785C8.59479 5.27919 8.32024 5.16485 7.99968 5.16485C7.87546 5.16485 7.75124 5.1858 7.62702 5.22769C7.50279 5.26958 7.38985 5.32897 7.28818 5.40585L6.57535 4.69302C6.77179 4.51702 6.99157 4.38496 7.23468 4.29685C7.47768 4.20885 7.73363 4.16485 8.00252 4.16485C8.60229 4.16485 9.11291 4.37552 9.53435 4.79685C9.95568 5.2183 10.1663 5.72891 10.1663 6.32869C10.1663 6.59758 10.1223 6.85352 10.0343 7.09652C9.94624 7.33963 9.81418 7.55941 9.63818 7.75585ZM13.3227 11.4405L12.5933 10.711C12.8378 10.2956 13.0222 9.86008 13.1465 9.40452C13.2709 8.94897 13.333 8.48019 13.333 7.99819C13.333 6.5093 12.8163 5.24819 11.783 4.21485C10.7497 3.18152 9.48857 2.66485 7.99968 2.66485C7.51768 2.66485 7.0489 2.72702 6.59335 2.85135C6.13779 2.97569 5.70229 3.16008 5.28685 3.40452L4.55735 2.67519C5.0719 2.34519 5.62068 2.0943 6.20368 1.92252C6.78679 1.75074 7.38546 1.66485 7.99968 1.66485C8.87579 1.66485 9.69913 1.83113 10.4697 2.16369C11.2402 2.49613 11.9105 2.94735 12.4805 3.51735C13.0505 4.08735 13.5017 4.75763 13.8342 5.52819C14.1667 6.29874 14.333 7.12208 14.333 7.99819C14.333 8.61241 14.2471 9.21108 14.0753 9.79419C13.9036 10.3772 13.6527 10.926 13.3227 11.4405ZM7.99718 14.3315C7.12207 14.3315 6.29963 14.1651 5.52985 13.8322C4.76018 13.4993 4.09018 13.0479 3.51985 12.478C2.94952 11.9081 2.49802 11.238 2.16535 10.4677C1.83268 9.69724 1.66635 8.87408 1.66635 7.99819C1.66635 7.32985 1.76657 6.6803 1.96702 6.04952C2.16746 5.41874 2.46468 4.83197 2.85868 4.28919L0.853516 2.26735L1.56635 1.55469L14.574 14.5624L13.8612 15.275L3.59202 5.02252C3.28768 5.46352 3.05735 5.9353 2.90102 6.43785C2.74457 6.94041 2.66635 7.46052 2.66635 7.99819C2.66635 8.65874 2.78152 9.29035 3.01185 9.89302C3.24218 10.4956 3.57657 11.04 4.01502 11.5264C4.60224 11.0965 5.23263 10.7621 5.90618 10.5232C6.57974 10.2843 7.27757 10.1649 7.99968 10.1649C8.50735 10.1649 9.00757 10.2328 9.50035 10.3687C9.99313 10.5046 10.4651 10.6888 10.9163 10.9214L12.4792 12.484C11.8801 13.0875 11.1942 13.5462 10.4215 13.8604C9.64885 14.1745 8.84074 14.3315 7.99718 14.3315Z"
                                    fill="#45546E" />
                                </g>
                              </svg>
                            </div>
                            <div *ngIf="items['is_form_required']">
                              <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <mask id="mask0_21022_73625" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="16" height="16">
                                <rect width="16" height="16" fill="#D9D9D9"/>
                                </mask>
                                <g mask="url(#mask0_21022_73625)">
                                <path d="M7.99968 13.3315C8.59202 13.3315 9.16318 13.2379 9.71318 13.0507C10.2632 12.8636 10.7681 12.5897 11.2278 12.229C10.7595 11.8811 10.2525 11.6168 9.70668 11.436C9.1609 11.2552 8.59185 11.1649 7.99952 11.1649C7.40729 11.1649 6.83618 11.2531 6.28618 11.4295C5.73618 11.6061 5.23129 11.8726 4.77152 12.229C5.23129 12.5897 5.73618 12.8636 6.28618 13.0507C6.83618 13.2379 7.40735 13.3315 7.99968 13.3315ZM9.63818 7.75585L8.92535 7.04302C9.00224 6.94135 9.06163 6.82841 9.10352 6.70419C9.1454 6.57997 9.16635 6.45574 9.16635 6.33152C9.16635 6.01097 9.05202 5.73641 8.82335 5.50785C8.59479 5.27919 8.32024 5.16485 7.99968 5.16485C7.87546 5.16485 7.75124 5.1858 7.62702 5.22769C7.50279 5.26958 7.38985 5.32897 7.28818 5.40585L6.57535 4.69302C6.77179 4.51702 6.99157 4.38496 7.23468 4.29685C7.47768 4.20885 7.73363 4.16485 8.00252 4.16485C8.60229 4.16485 9.11291 4.37552 9.53435 4.79685C9.95568 5.2183 10.1663 5.72891 10.1663 6.32869C10.1663 6.59758 10.1223 6.85352 10.0343 7.09652C9.94624 7.33963 9.81418 7.55941 9.63818 7.75585ZM13.3227 11.4405L12.5933 10.711C12.8378 10.2956 13.0222 9.86008 13.1465 9.40452C13.2709 8.94897 13.333 8.48019 13.333 7.99819C13.333 6.5093 12.8163 5.24819 11.783 4.21485C10.7497 3.18152 9.48857 2.66485 7.99968 2.66485C7.51768 2.66485 7.0489 2.72702 6.59335 2.85135C6.13779 2.97569 5.70229 3.16008 5.28685 3.40452L4.55735 2.67519C5.0719 2.34519 5.62068 2.0943 6.20368 1.92252C6.78679 1.75074 7.38546 1.66485 7.99968 1.66485C8.87579 1.66485 9.69913 1.83113 10.4697 2.16369C11.2402 2.49613 11.9105 2.94735 12.4805 3.51735C13.0505 4.08735 13.5017 4.75763 13.8342 5.52819C14.1667 6.29874 14.333 7.12208 14.333 7.99819C14.333 8.61241 14.2471 9.21108 14.0753 9.79419C13.9036 10.3772 13.6527 10.926 13.3227 11.4405ZM7.99718 14.3315C7.12207 14.3315 6.29963 14.1651 5.52985 13.8322C4.76018 13.4993 4.09018 13.0479 3.51985 12.478C2.94952 11.9081 2.49802 11.238 2.16535 10.4677C1.83268 9.69724 1.66635 8.87408 1.66635 7.99819C1.66635 7.32985 1.76657 6.6803 1.96702 6.04952C2.16746 5.41874 2.46468 4.83197 2.85868 4.28919L0.853516 2.26735L1.56635 1.55469L14.574 14.5624L13.8612 15.275L3.59202 5.02252C3.28768 5.46352 3.05735 5.9353 2.90102 6.43785C2.74457 6.94041 2.66635 7.46052 2.66635 7.99819C2.66635 8.65874 2.78152 9.29035 3.01185 9.89302C3.24218 10.4956 3.57657 11.04 4.01502 11.5264C4.60224 11.0965 5.23263 10.7621 5.90618 10.5232C6.57974 10.2843 7.27757 10.1649 7.99968 10.1649C8.50735 10.1649 9.00757 10.2328 9.50035 10.3687C9.99313 10.5046 10.4651 10.6888 10.9163 10.9214L12.4792 12.484C11.8801 13.0875 11.1942 13.5462 10.4215 13.8604C9.64885 14.1745 8.84074 14.3315 7.99718 14.3315Z" fill="#45546E"/>
                                </g>
                                <circle cx="13" cy="3" r="3" fill="red"/>
                                </svg>
                            </div>
                            <!-- <mat-icon class="delete-icon" (click)="deleteISA(items)">delete_outline</mat-icon> -->
                          </div>
                          <div style="width: 16px; height: 16px; position: relative;cursor: pointer">
                            <mat-icon class="copy-icon" (click)="duplicateMember(items)">library_add</mat-icon>
                          </div>
                          <div style="width: 16px; height: 16px; position: relative;cursor: pointer;" (click)="editMember(items)">
                            <!-- <mat-icon class="edit-icon" (click)="editMember(items)">edit
                            </mat-icon> -->
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <mask id="mask0_19813_15925" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="16" height="16">
                                <rect width="16" height="16" fill="#D9D9D9" />
                              </mask>
                              <g mask="url(#mask0_19813_15925)">
                                <path
                                  d="M3.33333 12.6654H4.26667L10.0167 6.91536L9.08333 5.98203L3.33333 11.732V12.6654ZM12.8667 5.9487L10.0333 3.1487L10.9667 2.21536C11.2222 1.95981 11.5361 1.83203 11.9083 1.83203C12.2806 1.83203 12.5944 1.95981 12.85 2.21536L13.7833 3.1487C14.0389 3.40425 14.1722 3.71259 14.1833 4.0737C14.1944 4.43481 14.0722 4.74314 13.8167 4.9987L12.8667 5.9487ZM11.9 6.93203L4.83333 13.9987H2V11.1654L9.06667 4.0987L11.9 6.93203Z"
                                  fill="#45546E" />
                              </g>
                            </svg>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div *ngIf="data.length==0 && !loading && !hideCreation">
        <div class="content">
          <div class="img">
            <img [src]="noDataImage ? noDataImage : 'https://assets.kebs.app/No-milestone-image.png'"
              class="img-data" />
          </div>
          <div class="inner-content pt-1">
            <div class="tittle pt-2">No People Found</div>
          </div>
        </div>
      </div>
    </div>
    <div class="quote-details" *ngIf="hideCreation">
      <div *ngFor="let quote of quoteCardDetails">
        <app-quote-card [quote]="quote" [resourceLoadingAccess]="resourceLoadingVisible"
          (triggerQuoteEvent)="handleQuoteFunction($event)"></app-quote-card>
      </div>
      <div class="mt-3" *ngIf="unSpecifiedCheck && !quoteNotFound && noQuoteList.length > 0">
        <app-quote-card [quote]="noQuoteData" [noQuote]="true" [noQuoteList]="noQuoteList"
          (triggerQuoteEvent)="handleQuoteFunction($event)"></app-quote-card>
      </div>
      <div *ngIf="quoteNotFound && !unSpecifiedCheck">
        <div class="content">
          <div class="img">
            <img [src]="noDataImage ? noDataImage : 'https://assets.kebs.app/No-milestone-image.png'"
              class="img-data" />
          </div>
          <div class="inner-content pt-1">
            <div class="tittle pt-2">No Quote Data Found</div>
          </div>
        </div>
      </div>

    </div>

  </span>

  <sat-popover #columnConfigToggle horizontalAlign="center" verticalAlign="below" hasBackdrop class="overlay-popover">
    <div class="card" style="width: 180px; height: 265px;">
      <div class="column-config-popup">
        <div class="column-list">
          <span *ngFor="let column of this.columns; let i =index">
            <div class="row" *ngIf="column.isActive">
              <label class="toggle-switch" [ngClass]="{ 'grayed-out-toggle': column.freezePane }"
                [style.background-color]="column.freezePane ? 'grey' : (column.isVisible ? '#F27A6C' : 'lightgrey')">
                <input type="checkbox" [(ngModel)]="column.isVisible" [disabled]="column.isDisabled"
                  (click)="!column.isVisible && saveISALandingPageUserConfig() ">
                <span class="slider"></span>
              </label>
              <span class="column-header">{{ column.name }}</span>
            </div>
          </span>
        </div>
      </div>

    </div>
  </sat-popover>
</div>

<!-- Search Config -->
<sat-popover #searchConfigToggle horizontalAlign="center" verticalAlign="below" hasBackdrop class="overlay-popover">
  <div class="card-column card">
    <div class="searchColumn-config-popup">
      <div class="column-list">
        <span *ngFor="let column of searchColumn;let i=index">
          <div class="row" *ngIf="column.isActive">
            <label class="toggle-switch" [ngClass]="{ 'grayed-out-toggle': column.isDisabled }"
              [style.background-color]="column.isDisabled ? 'grey' : (column.isVisible ? button : 'lightgrey')">
              <input type="checkbox" [(ngModel)]="column.isVisible" [disabled]="column.isDisabled"
                (click)="saveISALandingPageUserConfig()">
              <span class="slider"></span>
            </label>
            <span class="column-header">{{ column.name }}</span>
          </div>
        </span>
      </div>
    </div>
  </div>
</sat-popover>

<!-- Search Overlay -->
<ng-template #triggerSearchBarTemplateRef cdkConnectedOverlay [cdkConnectedOverlayOrigin]="triggerSearchBar">
  <div class="search-overlay">
    <div class="search-bar">
      <input #inputField type="text" [(ngModel)]="searchParam" placeholder="Search"
        (keydown.enter)="onEnterSearch(searchParam)" (input)="onSearchInputChange()" />
      <mat-icon class="more-icon" (click)="onToggleClick($event)" [satPopoverAnchor]="searchConfigToggle"
        tooltip="Toggle Selection">more_vert</mat-icon>
    </div>
    <div class="divider"></div>
    <div class="search-results-container">
      <ng-container *ngIf="isSearchLoading">
        <div class="loading-container">
          <div class="spinner"></div>
        </div>
      </ng-container>
      <ng-container *ngIf="!isSearchLoading && searchParam && searchData && searchData.length">
        <div class="search-text-list" *ngFor="let item of searchData" (click)="onCardClick(item)">
          <div class="top-row d-flex align-items-center">
            <span class="item-id">#{{item.associate_id}}</span>
            <app-app-user-image class="profile-img" [id]="item['oid']" imgWidth="18px"
              imgHeight="18px"></app-app-user-image>
            <span class="item-name" tooltip="{{item.name}}">{{item.name}}</span>
            <span class="item-role" tooltip="{{item.role}}">{{item.role}}</span>
          </div>
          <div class="bottom-row">
            <span class="item-dates">{{ formatDateRange(item.start_date, item.end_date) }}</span>
            <span class="item-status"
              [ngStyle]="{'background-color': item['status_color'].palate, 'color': item['status_color'].color}">
              {{item.status}}
            </span>
          </div>
        </div>
      </ng-container>
      <ng-container
        *ngIf="!isSearchLoading && searchSubmitted && searchParam && (!searchData || searchData.length === 0)">
        <div class="no-results-container">
          No results found
        </div>
      </ng-container>
      <ng-container *ngIf="searchParam == '' || searchParam == null">
        <ng-container *ngIf="recentSearch && recentSearch.length > 0">
          <span class="recent-search-title">Recently Searched</span>
          <div class="d-flex align-items-center recent-search-text-list"
            *ngFor="let search of recentSearch; let i = index" (click)="onSelectRecentSearch(i)">
            <div style="margin-bottom: 1px">
              <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
                <mask id="mask0_9594_64568" style="mask-type: alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="12"
                  height="12">
                  <rect width="12" height="12" fill="#D9D9D9" />
                </mask>
                <g mask="url(#mask0_9594_64568)">
                  <path
                    d="M5.99166 10.25C4.90897 10.25 3.96538 9.89357 3.1609 9.18075C2.35641 8.46793 1.89328 7.57434 1.77148 6.49999H2.53685C2.66313 7.36345 3.05175 8.07931 3.70271 8.64759C4.35368 9.21586 5.11666 9.49999 5.99166 9.49999C6.96666 9.49999 7.79374 9.16041 8.47291 8.48124C9.15208 7.80207 9.49166 6.97499 9.49166 5.99999C9.49166 5.02499 9.15208 4.1979 8.47291 3.51874C7.79374 2.83957 6.96666 2.49999 5.99166 2.49999C5.44551 2.49999 4.93365 2.6213 4.45608 2.86393C3.97853 3.10655 3.56731 3.44036 3.22243 3.86536H4.53012V4.61535H1.9917V2.07691H2.74167V3.26154C3.14744 2.7827 3.63333 2.41106 4.19936 2.14664C4.76538 1.88221 5.36282 1.75 5.99166 1.75C6.5814 1.75 7.13396 1.86154 7.64935 2.08463C8.16472 2.3077 8.61408 2.6109 8.99741 2.99423C9.38074 3.37756 9.68395 3.82692 9.90702 4.3423C10.1301 4.85768 10.2416 5.41025 10.2416 5.99999C10.2416 6.58973 10.1301 7.14229 9.90702 7.65768C9.68395 8.17306 9.38074 8.62242 8.99741 9.00575C8.61408 9.38908 8.16472 9.69228 7.64935 9.91535C7.13396 10.1384 6.5814 10.25 5.99166 10.25ZM7.49262 8.01344L5.63108 6.15191V3.49999H6.38107V5.84806L8.01953 7.48654L7.49262 8.01344Z"
                    fill="#8B95A5" />
                </g>
              </svg>
            </div>
            <div class="recent-search-text">{{ search }}</div>
          </div>
        </ng-container>
      </ng-container>
    </div>
  </div>
</ng-template>