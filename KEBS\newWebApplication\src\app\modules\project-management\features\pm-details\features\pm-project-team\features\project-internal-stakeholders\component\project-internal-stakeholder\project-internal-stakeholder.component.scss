.isa-landing {
  // width: 100%;
  // height: 90%;
  top: 262.52px;
  left: 120px;
  border-radius: 8px;
  gap: 8px;
  background: #FFFFFF;
  overflow-y: hidden;
  margin-top: 22px;
  overflow-x: auto;

  .column-table {
    width: var(--dynamicISAWidth);
    height: var(--dynamicISAHeight);
    overflow: auto;
  }

  .column {
    // width: 1220px;
    // height: 16px;
    gap: 16px;
    display: flex;
    border-bottom: 2px solid #E8E9EE;
    padding-bottom: 14px;
    background: #ffff;
    // width: 1234px;
    min-width: max-content;
  }

  .column-expand {
    width: 16px;
    height: 16px;
    border-radius: 4px;

  }

  .expand {
    width: 8.49px;
    height: 5.19px;
    top: 12.24px;
    left: 5.48px
  }

  .column-AID {
    width: 90px;
    height: 16px;
    gap: 4px
  }

  .AID {
    width: 18px;
    height: 16px;
    font-family: var(--intFont) !important;
    font-size: 11px;
    font-weight: 400;
    line-height: 16px;
    letter-spacing: 0.02em;
    text-align: left;
    color: #B9C0CA;
    white-space: nowrap;
  }

  .font-family {
    font-family: var(--intFont) !important;
  }

  .column-name {
    width: 165px;
    height: 16px;
    gap: 4px;

  }

  .name {
    width: 32px;
    height: 16px;
    font-family: var(--intFont) !important;
    font-size: 11px;
    font-weight: 400;
    line-height: 16px;
    letter-spacing: 0.02em;
    text-align: left;
    color: #B9C0CA;
    white-space: nowrap;
  }

  .column-cc {
    width: 126px;
    height: 16px;
    gap: 4px;

  }

  .cc {
    width: 115px;
    height: 16px;
    font-family: var(--intFont) !important;
    font-size: 11px;
    font-weight: 400;
    line-height: 16px;
    letter-spacing: 0.02em;
    text-align: left;
    color: #B9C0CA;
    white-space: nowrap;
  }

  .column-commercial {
    width: 100px;
    height: 16px;
    gap: 4px;
  }

  .commercial {
    width: 73px;
    height: 16px;
    font-family: var(--intFont) !important;
    font-size: 11px;
    font-weight: 400;
    line-height: 16px;
    letter-spacing: 0.02em;
    text-align: left;
    color: #B9C0CA;
    white-space: nowrap;
  }

  .column-role {
    width: 120px;
    height: 16px;
    gap: 4px
  }

  .role {
    width: 28px;
    height: 16px;
    font-family: var(--intFont) !important;
    font-size: 11px;
    font-weight: 400;
    line-height: 16px;
    letter-spacing: 0.02em;
    text-align: left;
    color: #B9C0CA;
    white-space: nowrap;
  }

  .column-start-date {
    width: 98px;
    height: 16px;
    gap: 4px;

  }

  .start-date {
    width: 64px;
    height: 16px;
    font-family: var(--intFont) !important;
    font-size: 11px;
    font-weight: 400;
    line-height: 16px;
    letter-spacing: 0.02em;
    text-align: left;
    color: #B9C0CA;
    white-space: nowrap;

  }

  .column-end-date {
    width: 98px;
    height: 16px;
    gap: 4px
  }

  .end-date {
    width: 70px;
    height: 16px;
    font-family: var(--intFont) !important;
    font-size: 11px;
    font-weight: 400;
    line-height: 16px;
    letter-spacing: 0.02em;
    text-align: left;
    color: #B9C0CA;
    white-space: nowrap;

  }

  .column-status {
    width: 185px;
    height: 16px;
    gap: 4px;
  }

  .column-allocated {
    width: 185px;
    height: 16px;
    gap: 4px;
  }

  .allocated {
    width: 41px;
    height: 16px;
    font-family: var(--intFont) !important;
    font-size: 11px;
    font-weight: 400;
    line-height: 16px;
    letter-spacing: 0.02em;
    text-align: left;
    color: #B9C0CA;
    white-space: nowrap;

  }

  .column-external {
    width: 185px;
    height: 16px;
    gap: 4px;
  }

  .external_id {
    width: 41px;
    height: 16px;
    font-family: var(--intFont) !important;
    font-size: 11px;
    font-weight: 400;
    line-height: 16px;
    letter-spacing: 0.02em;
    text-align: left;
    color: #B9C0CA;
    white-space: nowrap;

  }

  .status {
    width: 41px;
    height: 16px;
    font-family: var(--intFont) !important;
    font-size: 11px;
    font-weight: 400;
    line-height: 16px;
    letter-spacing: 0.02em;
    text-align: left;
    color: #B9C0CA;
    white-space: nowrap;

  }

  .column-action {
    width: 72px;
    height: 16px;
    gap: 4px;
  }

  .action {
    width: 41px;
    height: 16px;
    font-family: var(--intFont) !important;
    font-size: 11px;
    font-weight: 400;
    line-height: 16px;
    letter-spacing: 0.02em;
    text-align: left;
    color: #B9C0CA;
    white-space: nowrap;
  }

  .line {
    width: 1220px;
    border: 1px;
    background: #E8E9EE;
    border: 1px solid #E8E9EE;
    margin-top: 6px;
  }

  .row-body {
    // width: 1176px;
    // height: 144px;
    // width:100%;
    // width: 1234px;
    padding-bottom: 8px;
    border-bottom: 1px #E8E9EE solid;
    justify-content: flex-start;
    align-items: center;
    gap: 28px;
    display: flex;
    min-width: max-content;
    // display: inline-flex
  }

  .row-body-1 {
    // flex: 1 1 0; */
    // height: 136px;
    /* padding-top: 8px; */
    // padding-bottom: 8px;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 16px;

    /* display: flex; */
    /* overflow: auto;*/
  }

  .row-body-2 {
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    // gap: 16px;
    display: inline-flex;
    margin-top: 10px;
  }

  .parent-body {
    justify-content: flex-start;
    align-items: flex-start;
    gap: 16px;
    display: inline-flex
  }

  .parent-body-1 {
    justify-content: flex-start;
    align-items: flex-start;
    gap: 16px;
    display: flex
  }

  .row-expand {
    width: 16px;
    height: 16px;
    position: relative;
    border-radius: 4px;
    overflow: hidden
  }

  .parent-row-aid {
    // width: 40px;
    color: #45546E;
    font-size: 12px;
    font-family: var(--intFont) !important;
    font-weight: 400;
    text-transform: capitalize;
    line-height: 16px;
    letter-spacing: 0.24px;
    word-wrap: break-word;
    text-overflow: ellipsis;
  }

  .parent-row-name {
    height: 18px;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 4px;
    display: flex;
    width: 371px;
  }

  .parent-row-name-1 {
    // width: 100px;
    color: #45546E;
    font-size: 12px;
    font-family: var(--intFont) !important;
    font-weight: 500;
    line-height: 16px;
    word-wrap: break-word
  }

  .parent-row-cc {
    width: 125px;
    opacity: 0;
    color: #45546E;
    font-size: 12px;
    font-family: var(--intFont) !important;
    font-weight: 400;
    text-transform: capitalize;
    line-height: 16px;
    letter-spacing: 0.24px;
    word-wrap: break-word;
    // margin-left: 25px;
  }

  .parent-row-role {
    // width: 162px;
    opacity: 0;
    color: #45546E;
    font-size: 12px;
    font-family: var(--intFont) !important;
    font-weight: 400;
    text-transform: capitalize;
    line-height: 16px;
    letter-spacing: 0.24px;
    // word-wrap: break-word;
    // margin-left: 15px;
    width: 92px;
    text-overflow: ellipsis;
    overflow: hidden;
    text-wrap: nowrap;
  }

  .parent-row-start-date {
    width: 98px;
    opacity: 0;
    color: #45546E;
    font-size: 12px;
    font-family: var(--intFont) !important;
    font-weight: 400;
    text-transform: capitalize;
    line-height: 16px;
    letter-spacing: 0.24px;
    word-wrap: break-word
  }

  .parent-row-end-date {
    width: 98px;
    opacity: 0;
    color: #45546E;
    font-size: 12px;
    font-family: var(--intFont) !important;
    font-weight: 400;
    text-transform: capitalize;
    line-height: 16px;
    letter-spacing: 0.24px;
    word-wrap: break-word
  }

  .parent-row-allocated {
    width: 185px;
    opacity: 0;
    color: #45546E;
    font-size: 12px;
    font-family: var(--intFont) !important;
    font-weight: 400;
    text-transform: capitalize;
    line-height: 16px;
    letter-spacing: 0.24px;
    word-wrap: break-word
  }

  .parent-row-external-id {
    width: 185px;
    // opacity: 0;
    color: #45546E;
    font-size: 12px;
    font-family: var(--intFont) !important;
    font-weight: 400;
    text-transform: capitalize;
    line-height: 16px;
    letter-spacing: 0.24px;
    word-wrap: break-word
  }

  .child-body {
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 4px;
    display: flex;
    margin-top: 8px;
  }

  .child-body-1 {
    justify-content: flex-start;
    align-items: center;
    gap: 16px;
    display: inline-flex
  }

  .child-body-2 {
    height: 26px;
    padding-top: 4px;
    padding-bottom: 4px;
    background: #F7F9FB;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 16px;
    display: flex;
    width: 100%;
  }

  .child-expand {
    width: 16px;
    height: 16px;
    position: relative;
    opacity: 0;
    border-radius: 4px;
    overflow: hidden
  }

  .child-row-aid {
    // width: 40px;
    color: #45546E;
    font-size: 12px;
    font-family: var(--intFont) !important;
    font-weight: 400;
    text-transform: capitalize;
    line-height: 16px;
    letter-spacing: 0.24px;
    word-wrap: break-word;
    text-overflow: ellipsis;
    width: 90px;
  }

  .child-row-name {
    height: 18px;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 4px;
    display: flex;
    width: 165px;
  }

  .child-row-name-1 {
    width: 144px;
    color: #45546E;
    font-size: 12px;
    font-family: var(--intFont) !important;
    font-weight: 400;
    line-height: 16px;
    word-wrap: break-word;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 200px;
  }

  .child-row-cc {
    width: 126px;
    color: #45546E;
    font-size: 12px;
    font-family: var(--intFont) !important;
    font-weight: 400;
    text-transform: capitalize;
    line-height: 16px;
    letter-spacing: 0.24px;
    word-wrap: break-word;
    // margin-left: 25px;
  }

  .child-row-role {
    width: 120px;
    color: #45546E;
    font-size: 12px;
    font-family: var(--intFont) !important;
    font-weight: 400;
    text-transform: capitalize;
    line-height: 16px;
    letter-spacing: 0.24px;
    // word-wrap: break-word;
    // margin-left: 15px;
    text-overflow: ellipsis;
    overflow: hidden;
    text-wrap: nowrap;
  }

  .child-row-start-date {
    width: 98px;
    color: #45546E;
    font-size: 12px;
    font-family: var(--intFont) !important;
    font-weight: 400;
    text-transform: capitalize;
    line-height: 16px;
    letter-spacing: 0.24px;
    white-space: nowrap;
  }

  .child-row-end-date {
    width: 98px;
    color: #45546E;
    font-size: 12px;
    font-family: var(--intFont) !important;
    font-weight: 400;
    text-transform: capitalize;
    line-height: 16px;
    letter-spacing: 0.24px;
    white-space: nowrap;
  }

  .child-row-allocated {
    width: 185px;
    color: #45546E;
    font-size: 12px;
    font-family: var(--intFont) !important;
    font-weight: 400;
    text-transform: capitalize;
    line-height: 16px;
    letter-spacing: 0.24px;
    white-space: nowrap;
  }

  .delete-icon {
    width: 10px;
    height: 11.26px;
    left: 3px;
    top: -2px;
    position: absolute;
    /* background: #45546E; */
    color: #45546E;
    font-size: 20px;
  }

  .copy-icon {
    width: 11.33px;
    height: 13.33px;
    left: 2px;
    top: px;
    position: absolute;
    /* background: #45546E; */
    font-size: 18px;
    color: #45546E;
  }

  .edit-icon {
    width: 12.18px;
    height: 12.17px;
    left: px;
    top: -px;
    position: absolute;
    /* background: #45546E; */
    font-size: 18px;
    color: #45546E;
  }

  .expand-icon {
    /* width: 8.49px; */
    /* height: 5.19px; */
    /* left: 3.76px; */
    /* top: 5.48px; */
    // position: absolute;
    /* color: #526179; */
    font-size: 18px;
    cursor: pointer;
    margin-top: -1.5px;
    color: #526179;
  }

  .parent-row-aid-1 {
    width: 90px;
    height: 16px;
    display: flex
  }

  // .swal2-container:not(.swal2-top):not(.swal2-top-start):not(.swal2-top-end):not(.swal2-top-left):not(.swal2-top-right):not(.swal2-center-start):not(.swal2-center-end):not(.swal2-center-left):not(.swal2-center-right):not(.swal2-bottom):not(.swal2-bottom-start):not(.swal2-bottom-end):not(.swal2-bottom-left):not(.swal2-bottom-right):not(.swal2-grow-fullscreen)>.swal2-modal {
  //     margin: auto !important;
  //     border: 2px solid #52C41A !important;
  //     border-radius: 16px !important;
  //     width: 437px !important;
  //     height: 284px !important;
  // }
  .custom-popup {
    width: 400px;
    /* Adjust the width as needed */
    height: 200px;
    /* Adjust the height as needed */
  }

  .loader-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    /* Adjust the height as needed */
    background-color: white !important;
    /* Add a semi-transparent background */
  }

  ::ng-deep .green-spinner circle {
    stroke: var(--intButton) !important;
  }

  ::ng-deep .green-spinner circle {
    stroke: var(--intButton) !important;
  }

  .loader {
    top: 45%;
    position: absolute;
    left: 50%;
    bottom: 50%;
  }

  //   .loader {
  //     border: 4px solid  #f5f5f5 !important;
  //     border-top: 4px solid var(--intButton) !important; /* Change the border color to lightgreen */
  //     border-radius: 50%;
  //     width: 40px;
  //     height: 40px;
  //     margin-bottom: 25% !important;
  //     animation: spin 1s linear infinite;
  //   }
  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }

    100% {
      transform: rotate(360deg);
    }
  }

  // .body-size {
  //   //     width:1220px;
  //   //     height: 362px;
  //   // overflow: auto;
  //   // /* padding-bottom: 90px; */
  //   // overflow-x: hidden;
  // }

  ::-webkit-scrollbar {
    width: 5px !important;
    height: 5px !important;
  }

  .filter {
    width: 24px;
    height: 24px;
    gap: 16px;
    margin-top: 7px;
    cursor: pointer;
    margin-top: 10px;
    margin-left: 15px;
  }

  .filter-icon {
    width: 18px;
    height: 12px;
    top: 6px;
    left: 3px;
    color: #5F6C81;
    font-size: 20px;
  }

  .icons {
    white-space: nowrap;
    display: flex;
    margin-top: -57px;
    position: absolute;
    right: 0;
    margin-right: 6%;
  }

  .add-people {
    width: 30px;
    height: 30px;
    padding: 10px;
    border-radius: 30px;
    gap: 10px;
    background: #F7F9FB;
    margin-left: 10px;
    cursor: pointer;
    justify-content: center;
    margin-top: 5px;

  }

  .resource-loading {
    width: 30px;
    height: 30px;
    padding: 10px;
    border-radius: 30px;
    gap: 10px;
    background: #F7F9FB;
    margin-left: 10px;
    cursor: pointer;
    justify-content: center;
    margin-top: 5px;
  }

  .add-icon {
    width: 19px;
    height: 21px;
    top: 1px;
    left: 4px;
    color: #52C41A;
    font-size: 20px;
    margin-top: -5px;
    margin-left: -5px;

  }

  .request-people {
    width: 30px;
    height: 30px;
    padding: 10px;
    border-radius: 30px;
    gap: 10px;
    background: #F7F9FB;
    margin-left: 10px;
    cursor: pointer;
    justify-content: center;
    margin-top: 5px;

  }

  .request-icon {
    width: 19px;
    height: 21px;
    top: 1px;
    left: 4px;
    color: #1890FF;
    font-size: 20px;
    margin-top: -5px;
    margin-left: -5px;
  }

  .customize {
    width: 24px;
    height: 24px;
    gap: 16px;
    margin-left: 65%;
    margin-top: 10px;
    cursor: pointer;
    margin-top: 10px;
  }

  .customize-icon {
    width: 18px;
    height: 12px;
    top: 6px;
    left: 3px;
    color: #5F6C81;
    font-size: 20px;
  }

  .content {
    left: 43%;
    position: fixed;
    top: 46%;

  }

  .img-data {
    width: 130.4px;
    height: 125px;
    margin-left: 67px;
  }

  .tittle {
    width: 272px;
    height: 24px;
    font-family: var(--intFont) !important;
    font-size: 14px;
    font-weight: 700;
    line-height: 24px;
    letter-spacing: 0.02em;
    text-align: center;
    color: #45546E;

  }

  .people-count {
    border-radius: 41px;
    gap: 10px;
    background: #F27A6C;
    margin-top: -41px;
    margin-left: 2px;

  }

  .people-count-result {
    font-family: var(--intFont) !important;
    font-size: 10px;
    font-weight: 400;
    letter-spacing: 0em;
    text-align: center;
    color: #FFFFFF;

  }

  ::ng-deep .checkbox-input {
    border: none;
    border-radius: 4px;
    margin-left: 15px;
    margin-right: 7px;
    margin-bottom: 3px;
    color: var(--blue-grey-70, #212529);
    font-family: var(--intFont) !important;
    font-size: 13px;
    font-style: normal;
    font-weight: 500;
    line-height: 16px;
    letter-spacing: 0.24px;
    text-transform: capitalize;
  }

  /* Add this CSS in your global styles or in the component's style file */

  ::ng-deep .checkbox-input .mat-checkbox-inner-container {
    border-radius: 10px;
    /* Change the value to your desired border-radius */
  }

  ::ng-deep .mat-checkbox-checked.mat-accent .mat-checkbox-background {
    background-color: var(--intButton) !important;
  }

  ::ng-deep .mat-checkbox-ripple .mat-ripple-element {
    background-color: var(--intButton) !important;
  }

  ::ng-deep .mat-checkbox-indeterminate.mat-accent .mat-checkbox-background {
    background-color: var(--intButton) !important;
  }

  .child-row-external-id {
    width: 185px;
    color: #45546E;
    font-size: 12px;
    font-family: var(--intFont) !important;
    font-weight: 400;
    text-transform: capitalize;
    line-height: 16px;
    letter-spacing: 0.24px;
    white-space: nowrap;
  }
}

.filter-header {
  height: 35px;
  padding: 10px;
  border-bottom: 1px solid whitesmoke;
}

.filter-text {
  font-size: 16px;
  font-weight: 500;
}

.filter-body {
  height: 80px;
  width: 700px;
  margin-top: 40px;
  padding-left: 10px;
}

.button {
  background: var(--intButton) !important;
  color: white;
  font-weight: 500;
  width: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 5px;
  margin-top: 6px;
  height: 30px;
  font-size: 12px;
  cursor: pointer;
}

.drop-down-text {
  font-size: 13px !important;
  font-weight: 500;
  color: #6E7B8F;
}

.filter-footer {
  height: 20px;
  display: flex;
  justify-content: right;
  padding-right: 15px;
  border-top: 1px solid whitesmoke;
  height: 60px;
}

.drop-down {
  width: max-content;
  display: flex;
  justify-content: center;
  align-items: center;
  background: whitesmoke;
  padding-right: 7px;
  padding-left: 17px;
  border-radius: 20px;
  padding-top: 5px;
  padding-bottom: 5px;
  margin-right: 18px;
  cursor: pointer;
}

.drop-down-icon {
  font-size: 20px !important;
  margin-top: 4px;
  color: #6E7B8F;
  cursor: pointer;
}

.tag-chip {
  width: max-content;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 25px;
  padding-right: 2px;
  padding-left: 5px;
  margin: 5px;
  border: 1px solid grey;
}

.tag-preffix {
  font-size: 18px !important;
  margin-top: 4px;
}

.clear-button {
  background: var(--intButton) !important;
}

.tag-clear-icon {
  font-size: 17px;
  padding-top: 3px;
  padding-left: 5px;
  cursor: pointer;
  color: grey;
}

.tag-text {
  font-size: 13px;
  font-weight: 500;
  color: grey;
  width: 6ch;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  text-wrap: nowrap;
}

.selected-tags {
  font-size: 13px;
  font-weight: 500;
  display: flex;
  flex-wrap: wrap;
  border-bottom: 1px solid whitesmoke;
  padding-bottom: 15px;
  margin-top: 10px;
}

.filter-search-icon {
  font-size: 20px;
  margin-top: 4px;
}

.search-filter {
  display: flex;
}

/* Base styles for the toggle switch container */
.toggle-switch {
  display: inline-block;
  position: relative;
  width: 30px;
  height: 17px;
  //background-color: #ccc;
  border-radius: 15px;
  cursor: pointer;
  margin-right: 10px !important;
}

.grayed-out-toggle {
  display: inline-block;
  position: relative;
  width: 30px;
  height: 17px;
  //background-color: #ccc;
  border-radius: 15px;
  background-color: grey;
  margin-right: 10px !important;
}

.column-config-popup {
  // width: 230px;
  padding: 20px;
  max-height: 290px;
  min-height: 285px;
  overflow: auto;
  min-width: max-content !important;
}

.auto-adapt {
  width: max-content;
  padding: 20px;
  height: auto;
}

/* Hide the default checkbox input */
.toggle-switch input[type="checkbox"] {
  display: none;
}

/* Style for the slider (the rounded switch) */
.slider {
  position: absolute;
  top: 2px;
  left: 2px;
  width: 12px;
  height: 12px;
  background-color: #fff;
  border-radius: 50%;
  transition: transform 0.3s;
}

/* Apply styles when the checkbox is checked */
.toggle-switch input[type="checkbox"]:checked+.slider {
  transform: translateX(13px);
}

.toggle-switch input[type="checkbox"]:not(:checked)+.slider {
  transform: translateX(0);
}

.column-header {
  color: var(--blue-grey-80, #5F6C81);
  font-family: var(--intFont) !important;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 16px;
  letter-spacing: 0.22px;
  text-transform: capitalize;
}

.column-list {
  justify-content: space-between;
  height: 28px;
}

.data-label {
  white-space: nowrap;
  max-width: 101px;
  overflow: hidden;
  text-overflow: ellipsis;
  color: var(--Blue-Grey-80, #5F6C81);
  font-family: var(--intFont) !important;
  font-size: 13px;
  font-style: normal;
  font-weight: 400;
  line-height: 16px;
  /* 145.455% */
  letter-spacing: 0.22px;
  text-transform: capitalize;
}

.data-label-icon {
  color: var(--headerbutton)
}

.card-column:before {
  content: "";
  position: absolute;
  top: -7px;
  left: 83px;
  border-style: solid;
  border-width: 0px 8px 9px 7px;
  border-color: #ffffff transparent;
  display: block;
  width: 0;
  z-index: 0;
}

.column-config-popup {
  // width: 195px;
  padding: 20px;
  height: 40vh;
  min-width: max-content !important;
}

.toggle-switch {
  display: inline-block;
  position: relative;
  width: 30px;
  height: 17px;
  background-color: #ccc;
  border-radius: 15px;
  cursor: pointer;
  background-color: #F27A6C;
  margin-right: 10px !important;
}

.grayed-out-toggle {
  display: inline-block;
  position: relative;
  width: 30px;
  height: 17px;
  background-color: #ccc;
  border-radius: 15px;
  background-color: grey;
  margin-right: 10px !important;
}

.auto-adapt {
  width: max-content;
  padding: 20px;
  height: auto;
}

/* Hide the default checkbox input */
.toggle-switch input[type="checkbox"] {
  display: none;
}

/* Style for the slider (the rounded switch) */
.slider {
  position: absolute;
  top: 2px;
  left: 2px;
  width: 12px;
  height: 12px;
  background-color: #fff;
  border-radius: 50%;
  transition: transform 0.3s;
}

/* Apply styles when the checkbox is checked */
.toggle-switch input[type="checkbox"]:checked+.slider {
  transform: translateX(13px);
}

.column-header {
  color: var(--blue-grey-80, #5F6C81);
  font-family: var(--intFont) !important;
  font-size: 11.6px;
  font-style: normal;
  font-weight: 400;
  line-height: 16px;
  letter-spacing: 0.22px;
  text-transform: capitalize;
}

.column-list {
  justify-content: space-between;
  height: 28px;
}

/* Base styles for the toggle switch container */
.quote-details {
  height: var(--dynamicISAHeight) !important;
  overflow: auto;
  padding-right: 5px;
}

.role-badge {
  background-color: rgb(0 0 0 / 10%);
  color: black;
  padding: 0 4px;
  border-radius: 15px;
  display: inline-block;
  font-size: 11px;
}


.status-container {
  border-radius: 8px;
  justify-content: center;
  align-items: center;
  gap: 4px;
  display: inline-flex;
  padding: 0 6px;
}

.status-text {
  font-size: 11px;
  font-weight: 400;
  text-transform: capitalize;
  line-height: 16px;
  letter-spacing: 0.24px;
  white-space: nowrap;
}

.more-icon {
  cursor: pointer;
  margin-left: 5px;
  font-size: 20px;
  color: #757575;
}

.item-name,
.item-role {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: 500;

}

.search-text-list {
  display: flex;
  flex-direction: column;
  width: 100%;
  font-size: 12px;
  color: #2a3e52;
  padding: 4px 8px;
  border-bottom: 1px solid #e0e0e0;
  cursor: pointer;
}

.top-row {
  display: flex;
  align-items: center;
  gap: 8px;
}

.bottom-row {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 4px;
}

.item-status {
  padding: 3px 5px;
  border-radius: 4px;
  font-weight: 500;
  font-family: var(--intFont) !important;
  margin-bottom: 3px;
}

.item-role {
  font-family: var(--intFont) !important;
  padding: 0 3px;
}

.divider {
  height: 1px;
  width: 100%;
  background: #dadce2;
  margin-bottom: 8px;
  margin-top: 8px;
}

.recent-search-text-list {
  cursor: pointer;
  gap: 8px;
  width: fit-content;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
}

.recent-search-title {
  font-family: var(--intFont) !important;
  font-size: 12px;
  font-style: italic;
  font-weight: 400;
  color: #b9c0ca;
}

.recent-search-text {
  font-family: var(--intFont) !important;
  font-size: 12px;
  color: #8b95a5;
}

.no-results-container {
  text-align: center;
  padding: 10px;
}

.card-content {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  font-size: 9px;
}

.card-content span {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.card-content-header2 {
  padding: 3px;
  font-size: 11px;
  margin-top: -15px;
}

.item-id {
  width: 30px;
  flex: 0 0 60px;
  font-family: var(--intFont) !important;
  font-weight: 600;
}

.item-name {
  flex-grow: 1;
  font-family: var(--intFont) !important;
}

.item-dates {
  width: 140px;
  flex: 1 1 150px;
  font-family: var(--intFont) !important;
  color: #5d6d88;
  font-weight: 400;
}

.search-overlay {
  min-width: 400px;
  max-height: 300px;
  padding: 8px;
  border: 0.5px solid #b9c0ca;
  border-radius: 8px;
  box-shadow: 0px 4px 8px 0px #00000040;
  background-color: white;
}

.search-bar {
  display: flex;
  align-items: center;
  width: 100%;
}

.search-bar input {
  flex: 1;
  font-family: var(--intFont) !important;
  font-size: 13px;
  font-weight: 400;
  color: #45546e;
  outline: none;
  border: none;
}

.search-bar input::placeholder {
  font-family: var(--intFont) !important;
  font-size: 13px;
  font-weight: 400;
  color: #45546e;
}

.search-icon {
  width: 9px;
  color: #5F6C81;
  cursor: pointer;
  margin-top: 9px;
}

.search-results-container {
  max-height: 240px;
  overflow-y: auto;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border-left-color: #52C41A;
  animation: spin 1s ease infinite;
}

.searchColumn-config-popup {
  width: 140px;
  padding: 10px;
  height: 15vh;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
