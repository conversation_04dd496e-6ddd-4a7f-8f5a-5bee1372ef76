import {
  Component,
  HostListener,
  Input,
  OnInit,
  Renderer2,
  ViewContainerRef,
  ElementRef,
  TemplateRef,
  ViewChild
} from '@angular/core';
import { PmInternalStakeholderService } from '../../services/pm-internal-stakeholder.service';
import {
  MatDialog,
  MatDialogRef,
  MatDialogConfig,
} from '@angular/material/dialog';
import { PmAddMemberComponent } from '../pm-add-member/pm-add-member.component';
import moment from 'moment';
import Swal from 'sweetalert2';
import { ToastrService } from 'ngx-toastr';
import { ActivatedRoute, Router } from '@angular/router';
import { ProjectColumnCustomizationComponent } from '../project-column-customization/project-column-customization.component'
import { PmLandingPageService } from 'src/app/modules/project-management/features/pm-landing-page/features/pm-project-landing-page/services/pm-landing-page.service';
import { SatPopover } from '@ncstate/sat-popover';
import { PmMasterService } from 'src/app/modules/project-management/services/pm-master.service';
import { takeUntil, throwIfEmpty } from 'rxjs/operators';
import { ContactsHomeModule } from 'src/app/modules/contacts/features/contacts-home/contacts-home.module';
import * as _ from 'underscore';
import { AnyNsRecord } from 'dns';
import { PmAuthorizationService } from 'src/app/modules/project-management/services/pm-authorization.service';
import { ToasterService } from 'src/app/services/toaster-service/toaster.service';
import { ToasterMessageService } from 'src/app/modules/project-management/shared-lazy-loaded/components/toaster-message/toaster-message.service';
import { SubSink } from 'subsink';
import { Overlay, OverlayRef } from '@angular/cdk/overlay';
import { TemplatePortal } from '@angular/cdk/portal';

@Component({
  selector: 'app-project-internal-stakeholder',
  templateUrl: './project-internal-stakeholder.component.html',
  styleUrls: ['./project-internal-stakeholder.component.scss'],
})
export class ProjectInternalStakeholderComponent implements OnInit {
  columns: any = [
    { name: '', isVisible: false, isDisabled: true, isActive: false },
    { name: 'AID', isVisible: true, isDisabled: true, isActive: true },
    { name: 'KEKA Employee ID', isVisible: false, isDisabled: true, isActive: false },
    { name: 'NAME', isVisible: true, isDisabled: true, isActive: true },
    {
      name: 'CURRENT CAPACITY',
      isVisible: true,
      isDisabled: false,
      isActive: true,
    },
    { name: 'TYPE', isVisible: true, isDisabled: false, isActive: true },
    { name: 'ROLE', isVisible: true, isDisabled: true, isActive: true },
    { name: 'PLANNED ALLOCATED HOURS', isVisible: true, isDisabled: false, isActive: true },
    { name: 'START DATE', isVisible: true, isDisabled: false, isActive: true },
    { name: 'END DATE', isVisible: true, isDisabled: false, isActive: true },
    { name: 'STATUS', isVisible: true, isDisabled: false, isActive: true },
    { name: 'ACTION', isVisible: true, isDisabled: false, isActive: true },
  ];
  data: any = [];
  parentData: any = [];
  projectID: any;
  itemID: any;
  is_edit_aid: any;
  is_edit_head: boolean = false;
  currentDate: any = moment().format('YYYY-MM-DD');
  loading: boolean = true;
  allExpand: boolean = false;
  screen: any = 'project-internal-stakeholders';
  project_role_list: any = [];
  showIdentity: boolean=false;
  projectStatusMatrix: any=[];
  resourceTypeList: any=[];

  item_status_id: any;
  status_list: any = [
    { name: 'Assigned', is_selected: false, color: '#52C41A', icon: 'circle' },
    {
      name: 'Allocation Completed',
      is_selected: false,
      color: '#FA541C',
      icon: 'circle',
    },
    {
      name: 'Allocation Reserved',
      is_selected: false,
      color: '#1890FF',
      icon: 'circle',
    },
  ];
  comercial_list: any = [
    {
      name: 'Billable',
      is_selected: false,
      color: '#52C41A',
      icon: 'attach_money',
    },
    {
      name: 'Non Billable',
      is_selected: false,
      color: '#FA541C',
      icon: 'money_off',
    },
  ];
  tags: any = [];
  filterData: any = [];
  filterParentData: any = [];
  button: any;
  shades: any;
  searchText: any = '';
  roleFilterListOpened: any;
  statusFilterListOpened: any;
  commercialFilterListOpened: any;
  inactivePositionStatus: any=[];
  formConfig: any = [];
  noDataImage: any;
  item_name: any;
  project_name: any;
  internalStakeholderAccess: boolean = false;
  editStakeholderAccess: boolean = false;
  duplicateStakeholderAccess: boolean = false;
  deleteStakeholderAccess: boolean = false;
  addMemberAccess: boolean=false;
  fontStyle: any;
  retrieveMessages: any = [];
  entityList: any = [];
  divisionList: any = [];
  subDivisionList: any = [];
  orgMappingList: any = [];
  orgEntityList: any = [];
  orgDivisionList: any = [];
  orgSubDivisionList: any = [];
  currencyList: any = [];
  quote_id_list: any = [];
  quote_position_list: any = [];
  enable_position: boolean = false;
  rateCardList: any = [];
  project_end_date: any;
  project_start_date: any;
  withOpportunity: any;
  customer_id: any;
  positionList: any;
  rateUnitList: any;
  rateLocationList: any;
  rateCardConfiguration: any = [];
  quoteCardDetails: any = [];
  q2cVisible: boolean = false; //For Enabling QTC configuration
  hideCreation:boolean = true;
  unSpecifiedCheck:boolean = false;
  noQuoteData:any;
  noQuoteList:any;
  quoteNotFound:boolean = false;
  qtcLoader:boolean = false;
  qtcConfig:any;
  resourceLoadingAccess: boolean = false;
  resourceLoadingVisible:boolean = false;
  private subs = new SubSink();
  enable_external_employee_id:boolean=false
  service_type:any;
  serviceTypeList:any = [];
  searchColumn: any = [
    { 'id': 1, 'name': 'Employee Id', 'isVisible': true, 'isDisabled': false, 'isActive': true },
    { 'id': 2, 'name': 'Name', 'isVisible': true, 'isDisabled': true, 'isActive': true },
    { 'id': 3, 'name': 'Email', 'isVisible': true, 'isDisabled': false, 'isActive': true },
  ]
  searchParam: any = []
  searchSubmitted: boolean = false;
  recentSearch: any = [];
  isNoSearchVisible: boolean = false;
  overlayRef: OverlayRef;
  @ViewChild('searchConfigToggle', { static: false }) searchConfigToggle: SatPopover;
  @ViewChild('triggerSearchBarTemplateRef', { static: false })
  private triggerSearchBarTemplateRef!: TemplateRef<HTMLElement>;
  triggerSearchBar: ElementRef;
  searchData: any = []; 
  isSearchLoading: boolean = false;


  constructor(
    private renderer: Renderer2,
    public dialog: MatDialog,
    private router: Router,
    public PmInternalStakeholderService: PmInternalStakeholderService,
    private pmService: PmLandingPageService,
    private masterService: PmMasterService,
    private authService: PmAuthorizationService,
    private toasterService: ToasterMessageService,
    private memberViewContainerRef: ViewContainerRef,
    private route: ActivatedRoute,
    private _overlay: Overlay,
    private _viewContainerRef: ViewContainerRef 
  ) {}

  async ngOnInit() {
    this.loading = true;
    this.qtcLoader = true;
    this.calculateDynamicStyle();
    this.itemID = parseInt(this.router.url.split('/')[5]);
    this.projectID = parseInt(this.router.url.split('/')[3]);
    this.item_name = this.router.url.split('/')[4];
    this.project_name = this.router.url.split('/')[6];

    await this.authService
      .getReadWriteAccess(this.projectID, this.itemID)
      .then(async (res) => {
        if (res) {
          this.internalStakeholderAccess =
            await this.authService.getProjectWiseObjectAccess(
              this.projectID,
              this.itemID,
              66
            );
          this.resourceLoadingAccess = await this.authService.getProjectWiseObjectAccess(
            this.projectID,
            this.itemID,
            143
          );
          this.editStakeholderAccess =
            await this.authService.getProjectWiseObjectAccess(
              this.projectID,
              this.itemID,
              351
            );
            this.duplicateStakeholderAccess =
            await this.authService.getProjectWiseObjectAccess(
              this.projectID,
              this.itemID,
              352
            );
            this.deleteStakeholderAccess =
            await this.authService.getProjectWiseObjectAccess(
              this.projectID,
              this.itemID,
              353
            );
        }
      });

    await this.initializeMasterData();

    await this.retreiveData();

    await this.retrieveUserConfiguration();
    if(this.withOpportunity && this.q2cVisible){
      await this.handleQTCConfiguration();
    }
    else{
      this.hideCreation = false;
    }
    if(this.enable_external_employee_id){
      this.columns[2].isVisible = true;
      this.columns[2].isActive = true;
    }
    else{
      this.columns[2].isVisible = false;
      this.columns[2].isActive = false;
    }
    this.loading = false;
    this.qtcLoader = false;

    await this.initializePage();
  }

  async retreiveData() {
    this.loading = true;

    await this.PmInternalStakeholderService.retrieveISAData(
      parseInt(this.itemID),
      this.currentDate
    ).then((res: any) => {
      this.data = res['data'];
    });

    const uniqueIds = this.getUniqueIds(this.data);
    // for(let item of this.data){
    //   // let finance_allocated_hrs_data = await this.getPlannedAllocatedHours(item);
    //   let prj_allocated_hrs_data  = await this.getAllocatedHrs(item);
    //   let allocated_hours:number = 0;
    //   if(prj_allocated_hrs_data && prj_allocated_hrs_data['allocated_hours']){
    //     allocated_hours = parseFloat((parseFloat(prj_allocated_hrs_data['allocated_hours']) * (item['split_percentage'] / 100)).toFixed(2));
    //   }
    //   item.allocated_hours = allocated_hours;
    // }
    // await this.processAllocatedHours();
    this.parentData = [];

    for (let i = 0; i < uniqueIds.length; i++) {
      let b = 0;
      let b1 = 0;
      let a = 0;
      for (let item of this.data) {
        if (uniqueIds[i] == item['associate_id']) {
          if (item['status'] == 'Assigned') {
            this.parentData.push({
              associate_id: item['associate_id'],
              name: item['name'],
              split_percentage: item['split_percentage'],
              commercial: item['commercial'] +" " +(this.showIdentity ? item['identity_name'] : "") ,
              role: item['role'],
              start_date: moment(item['start_date'])
                .utc()
                .format('DD-MMM-YYYY'),
              end_date: moment(item['end_date']).utc().format('DD-MMM-YYYY'),
              status: item['status'],
              expand: 0,
              status_color: item['status_color'],
              oid: item['oid'],
              stakeholder_reference_id: item['stakeholder_reference_id'],
              rate_card_id:item['rate_card_id'],
              external_employee_id:item['external_employee_id'],
              head:item['head']
            });
            a = a + 1;
            break;
          } else if (item['status'] == 'Allocation Completed') {
            b = b + 1;
          } else {
            b1 = b1 + 1;
          }
        }
      }
      if (b > 0 && b1 > 0 && a == 0) {
        for (let item of this.data) {
          if (uniqueIds[i] == item['associate_id']) {
            if (item['status'] == 'Allocation Reserved') {
              this.parentData.push({
                associate_id: item['associate_id'],
                name: item['name'],
                split_percentage: item['split_percentage'],
                commercial: item['commercial'] +" " +(this.showIdentity ? item['identity_name'] : "") ,
                role: item['role'],
                start_date: moment(item['start_date'])
                  .utc()
                  .format('DD-MMM-YYYY'),
                end_date: moment(item['end_date']).utc().format('DD-MMM-YYYY'),
                status: item['status'],
                expand: 0,
                status_color: item['status_color'],
                oid: item['oid'],
                stakeholder_reference_id: item['stakeholder_reference_id'],
                rate_card_id:item['rate_card_id'],
                external_employee_id:item['external_employee_id'],
                head:item['head']
              });
              break;
            }
          }
        }
      }
      if (b > 0 && b1 == 0 && a == 0) {
        for (let item of this.data) {
          if (uniqueIds[i] == item['associate_id']) {
            if (item['status'] == 'Allocation Completed') {
              this.parentData.push({
                associate_id: item['associate_id'],
                name: item['name'],
                split_percentage: item['split_percentage'],
                commercial: item['commercial'] +" " +(this.showIdentity ? item['identity_name'] : "") ,
                role: item['role'],
                start_date: moment(item['start_date'])
                  .utc()
                  .format('DD-MMM-YYYY'),
                end_date: moment(item['end_date']).utc().format('DD-MMM-YYYY'),
                status: item['status'],
                expand: 0,
                status_color: item['status_color'],
                oid: item['oid'],
                stakeholder_reference_id: item['stakeholder_reference_id'],
                rate_card_id:item['rate_card_id'],
                external_employee_id:item['external_employee_id'],
                head:item['head']
              });
              break;
            }
          }
        }
      }
      if (b1 > 0 && b == 0 && a == 0) {
        for (let item of this.data) {
          if (uniqueIds[i] == item['associate_id']) {
            if (item['status'] == 'Allocation Reserved') {
              this.parentData.push({
                associate_id: item['associate_id'],
                name: item['name'],
                split_percentage: item['split_percentage'],
                commercial: item['commercial'] +" " +(this.showIdentity ? item['identity_name'] : "") ,
                role: item['role'],
                start_date: moment(item['start_date'])
                  .utc()
                  .format('DD-MMM-YYYY'),
                end_date: moment(item['end_date']).utc().format('DD-MMM-YYYY'),
                status: item['status'],
                expand: 0,
                status_color: item['status_color'],
                oid: item['oid'],
                stakeholder_reference_id: item['stakeholder_reference_id'],
                rate_card_id:item['rate_card_id'],
                external_employee_id:item['external_employee_id'],
                head:item['head']
              });
              break;
            }
          }
        }
      }
    }

    this.loading = false;
    this.tags = [];
  
    this.filterParentData = this.parentData;
    this.filterData = this.data;
    this.PmInternalStakeholderService.parentData = this.parentData;
    this.PmInternalStakeholderService.childData = this.data
    this.PmInternalStakeholderService.setParentData(this.parentData);
  }
  getUniqueIds(data: any[]): number[] {
    const uniqueIds = new Set<number>();
    data.forEach((item) => {
      uniqueIds.add(item.associate_id);
    });
    return Array.from(uniqueIds);
  }
  collapse(associate_id: any) {
    console.log(associate_id);
    for (let items of this.data) {
      if (items['associate_id'] == associate_id) {
        items['expand'] = 0;
      }
    }
    let c = 0;
    for (let item of this.parentData) {
      if (item['associate_id'] == associate_id) {
        item['expand'] = 0;
      }
      if (item['expand'] == 1) {
        c = c + 1;
      }
    }
    if (c == 0) {
      this.allExpand = false;
    }
  }
  expand(associate_id: any) {
    console.log(associate_id);
    for (let items of this.data) {
      if (items['associate_id'] == associate_id) {
        items['expand'] = 1;
      }
    }
    let c = 0;
    for (let item of this.parentData) {
      if (item['associate_id'] == associate_id) {
        item['expand'] = 1;
      }
      if (item['expand'] == 0) {
        c = c + 1;
      }
    }
    if (c == 0) {
      this.allExpand = true;
    }
  }
  editMember(items: any) {
    if (this.editStakeholderAccess) {
      let data = {
        mode: 'Edit',
        data: items,
        color: this.button,
        masterData: {
          entityList: this.entityList,
          divisionList: this.divisionList,
          subDivisionList: this.subDivisionList,
          orgMappingList: this.orgMappingList,
          orgEntityList: this.orgEntityList,
          orgDivisionList: this.orgDivisionList,
          orgSubDivisionList: this.orgSubDivisionList,
          quote_id_list: this.quote_id_list,
          enable_position: this.enable_position,
          rateCardList: this.rateCardList,
          project_end_date: this.project_end_date,
          project_start_date: this.project_start_date,
          withOpportunity: this.withOpportunity,
          customer_id: this.customer_id,
          currenyList: this.currencyList,
          positionList: this.positionList,
          rateUnitList: this.rateUnitList,
          rateLocationList: this.rateLocationList,
          quote_position_list: this.quote_position_list,
          rateCardConfiguration: this.rateCardConfiguration,
        },
      }
      this.PmInternalStakeholderService.setAllocationData(data)
      this.router.navigate(['allocateMember'], { relativeTo: this.route});
    } else {
      const no_access =
        this.retrieveMessages.length > 0
          ? this.retrieveMessages[0].errors.no_access
            ? this.retrieveMessages[0].errors.no_access
            : 'You are not having access to edit member'
          : 'You are not having access to edit member';
      this.toasterService.showWarning(no_access, 10000);
    }
  }
  deleteISA(data: any) {
    let isFeedbackFromApplicable = _.findWhere(this.formConfig, {type:"isa", field_name:'isFeedbackFromApplicable', is_active:true})
    if (this.deleteStakeholderAccess) {
      // console.log(data);
      let currentDate = moment().format('YYYY-MM-DD');
      if (moment(data['end_date']).format('YYYY-MM-DD') < currentDate) {
        // let res_deleteMemberPastUnsuccess = _.findWhere(this.formConfig, {type:"isa", field_name:'deletemember_past_failure_message', is_active:true})
        // if(res_deleteMemberPastUnsuccess){
        //   console.log("Delete past employee Msg from Mongo")
        //   this.utilityService.showMessage(res_deleteMemberPastUnsuccess['label'], "Dismiss",3000)
        // }
        // else{
        //   console.log("Delete past employee Msg from VS")
        //   this.utilityService.showMessage("Can't demobilise! Allocation period has already ended!","Dismiss",3000)
        // }

        if(isFeedbackFromApplicable){
          this.PmInternalStakeholderService.setAllocationData(data)
          this.router.navigate(['demobilizeMemeber'], { relativeTo: this.route});
          return;
        }

        const allocation_completed =
          this.retrieveMessages.length > 0
            ? this.retrieveMessages[0].errors.allocation_completed
              ? this.retrieveMessages[0].errors.allocation_completed
              : 'Cannot demobilise! Allocation period has already ended!'
            : 'Cannot demobilise! Allocation period has already ended!';
        this.toasterService.showWarning(allocation_completed, 10000);
      } else if (
        moment(data['start_date']).format('YYYY-MM-DD') > currentDate
      ) {
        // let res_final
        // let res_deleteMemberFutureAlert = _.findWhere(this.formConfig, {type:"isa", field_name:'deletemember_future_alert_message', is_active:true})
        // if(res_deleteMemberFutureAlert){
        //   console.log("Alert Msg from Mongo")
        //   res_final = res_deleteMemberFutureAlert['label']
        //   //this.utilityService.openConfirmationSweetAlertWithCustom(res_deleteMemberAlert['label'], "")
        // }
        // else{
        //   console.log("Alert Msg from VS")
        //   res_final = "Employee will be deleted!"
        //   //this.utilityService.openConfirmationSweetAlertWithCustom("Employee will be deleted!", "")
        // }
        // this.utilityService.openConfirmationSweetAlertWithCustom(res_final, "").
        // then(async (res) => {
        //   if (res) {
        //     this.projectService.removeMember(data['stakeholder_table_id'], moment().format("YYYY-MM-DD"), "delete").then((res)=>{

        //       let res_deleteMemberFutureSuccess = _.findWhere(this.formConfig, {type:"isa", field_name:'deletemember_future_success_message', is_active:true})
        //       if(res_deleteMemberFutureSuccess){
        //         console.log("Delete success Msg from Mongo")
        //         this.utilityService.showMessage(res_deleteMemberFutureSuccess['label'], "Dismiss",3000)
        //       }
        //       else{
        //         console.log("Delete success Msg from VS")
        //         this.utilityService.showMessage("Employee deleted!","Dismiss",3000)
        //       }

        //       this.initReport()
        //     })
        //   }
        // });
        Swal.fire({
          title: 'Alert Message',
          html: `
            <div style="background-color: #FFFFFF;border: 2px solid white; padding: 10px;">
              <h2>Do you want to continue with employee off-boarding?
              </h2>
            </div>
          `,
          showCancelButton: true,
          confirmButtonText: 'OK',
          cancelButtonText: 'Cancel',
          allowOutsideClick: false,
          allowEscapeKey: false,
        }).then((result) => {
          if (result.isConfirmed) {
            this.PmInternalStakeholderService.removeMember(
              data['stakeholder_reference_id'],
              moment().format('YYYY-MM-DD'),
              'delete'
            ).then(async (res) => {
              // let res_deleteMemberFutureSuccess = _.findWhere(this.formConfig, {type:"isa", field_name:'deletemember_future_success_message', is_active:true})
              // if(res_deleteMemberFutureSuccess){
              //   console.log("Delete success Msg from Mongo")
              //   this.utilityService.showMessage(res_deleteMemberFutureSuccess['label'], "Dismiss",3000)
              // }
              // else{
              //   console.log("Delete success Msg from VS")
              //   this.utilityService.showMessage("Employee deleted!","Dismiss",3000)
              // }
              const employee_deleted =
                this.retrieveMessages.length > 0
                  ? this.retrieveMessages[0].errors.employee_deleted
                    ? this.retrieveMessages[0].errors.employee_deleted
                    : 'Employee deleted!'
                  : 'Employee deleted!';
              this.toasterService.showSuccess(employee_deleted, 10000);
              this.qtcLoader = true;
              await this.retreiveData();
              await this.handleQTCConfiguration();
              this.qtcLoader = false;
              // this.initReport()
              // this.ngOnInit()
            });
          } else if (result.dismiss === Swal.DismissReason.cancel) {
            // this.ngOnInit()
          }
        });
      } else {
        // let res_final
        // let res_deleteMemberPresentAlert = _.findWhere(this.formConfig, {type:"isa", field_name:'deletemember_present_alert_message', is_active:true})
        // if(res_deleteMemberPresentAlert){
        //   console.log("Alert Msg from Mongo")
        //   res_final = res_deleteMemberPresentAlert['label']
        //   //this.utilityService.openConfirmationSweetAlertWithCustom(res_deleteMemberAlert['label'], "")
        // }
        // else{
        //   console.log("Alert Msg from VS")
        //   res_final = "Employee will be deleted!"
        //   //this.utilityService.openConfirmationSweetAlertWithCustom("Employee will be deleted!", "")
        // }
        // this.utilityService.openConfirmationSweetAlertWithCustom(res_final, "").
        // then(async (res) => {
        //   if (res) {
        //     this.projectService.removeMember(data['stakeholder_table_id'], moment().format("YYYY-MM-DD"), "demobilize").then((res)=>{

        //       let res_deleteMemberPresentSuccess = _.findWhere(this.formConfig, {type:"isa", field_name:'deletemember_present_success_message', is_active:true})
        //       if(res_deleteMemberPresentSuccess){
        //         console.log("Delete success Msg from Mongo")
        //         this.utilityService.showMessage(res_deleteMemberPresentSuccess['label'], "Dismiss",3000)
        //       }
        //       else{
        //         console.log("Delete success Msg from VS")
        //         this.utilityService.showMessage("Employee demobilized!","Dismiss",3000)
        //       }

        //       //this.utilityService.showMessage("Employee demobilized!","Dismiss",3000)
        //       this.initReport()
        //     })
        //   }
        // });
        Swal.fire({
          icon: 'warning',
          html: `
            <div">
              <h1>Do you want to continue with employee off-boarding?
              </h1>
            </div>
          `,
          customClass: { popup: 'custom-popup' },
          backdrop: false,

          showCancelButton: true,
          confirmButtonText: 'OK',
          cancelButtonText: 'Cancel',
          allowOutsideClick: false,
          allowEscapeKey: false,
        }).then(async (result) => {
          if (result.isConfirmed) {
            if(isFeedbackFromApplicable){
              this.PmInternalStakeholderService.setAllocationData(data)
              this.router.navigate(['demobilizeMemeber'], { relativeTo: this.route});
              return;
            }
            this.PmInternalStakeholderService.removeMember(
              data['stakeholder_reference_id'],
              moment().format('YYYY-MM-DD'),
              'demobilize'
            ).then(async (res) => {
              const employee_demobilized =
                this.retrieveMessages.length > 0
                  ? this.retrieveMessages[0].errors.employee_demobilized
                    ? this.retrieveMessages[0].errors.employee_demobilized
                    : 'Employee demobilized!'
                  : 'Employee demobilized!';
              this.toasterService.showSuccess(employee_demobilized, 10000);
              this.qtcLoader = true;
              await this.retreiveData();
              await this.handleQTCConfiguration();
              this.qtcLoader = false;
              // this.initReport()
              // this.ngOnInit()
            });
          } else if (result.dismiss === Swal.DismissReason.cancel) {
            // this.ngOnInit()
          }
        });
      }
    } else {
      const no_access =
        this.retrieveMessages.length > 0
          ? this.retrieveMessages[0].errors.no_access
            ? this.retrieveMessages[0].errors.no_access
            : 'You are not having access to delete member'
          : 'You are not having access to delete member';
      this.toasterService.showWarning(no_access, 10000);
    }
  }

  duplicateMember(items: any) {
    if (this.duplicateStakeholderAccess) {

      let data = {
        mode: 'Duplicate',
        data: items,
        color: this.button,
        masterData: {
          entityList: this.entityList,
          divisionList: this.divisionList,
          subDivisionList: this.subDivisionList,
          orgMappingList: this.orgMappingList,
          orgEntityList: this.orgEntityList,
          orgDivisionList: this.orgDivisionList,
          orgSubDivisionList: this.orgSubDivisionList,
          quote_id_list: this.quote_id_list,
          enable_position: this.enable_position,
          rateCardList: this.rateCardList,
          project_end_date: this.project_end_date,
          project_start_date: this.project_start_date,
          withOpportunity: this.withOpportunity,
          customer_id: this.customer_id,
          currenyList: this.currencyList,
          positionList: this.positionList,
          rateUnitList: this.rateUnitList,
          rateLocationList: this.rateLocationList,
          quote_position_list: this.quote_position_list,
          rateCardConfiguration: this.rateCardConfiguration,
        },
      }
      this.PmInternalStakeholderService.setAllocationData(data)
      this.router.navigate(['allocateMember'], { relativeTo: this.route});
    } else {
      const no_access =
        this.retrieveMessages.length > 0
          ? this.retrieveMessages[0].errors.no_access
            ? this.retrieveMessages[0].errors.no_access
            : 'You are not having access to add member'
          : 'You are not having access to add member';
      this.toasterService.showWarning(no_access, 10000);
    }
  }

  collapseAll() {
    for (let items of this.data) {
      items['expand'] = 0;
    }
    for (let item of this.parentData) {
      item['expand'] = 0;
    }
    this.allExpand = false;

    this.saveISALandingPageUserConfig();
  }

  expandAll() {
    for (let items of this.data) {
      items['expand'] = 1;
    }
    for (let item of this.parentData) {
      item['expand'] = 1;
    }
    this.allExpand = true;

    this.saveISALandingPageUserConfig();
  }

  customize(event: MouseEvent): void {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.data = { columns: this.columns, color: this.button };
    dialogConfig.disableClose = false;
    const marginLeft = 280;
    const marginTop = 25;
    dialogConfig.position = {
      top: `190px`,
      left: `930px`,
    };
    const dialogRef = this.dialog.open(
      ProjectColumnCustomizationComponent,
      dialogConfig
    );
    if (dialogRef) {
      const backdrop = document.querySelector('.cdk-overlay-backdrop');
      if (backdrop) {
        this.renderer.setStyle(backdrop, 'background-color', 'transparent');
      }
    }

    dialogRef.componentInstance.columnsChanged.subscribe(
      (updatedColumns: any[]) => {
        this.saveISALandingPageUserConfig();
        // console.log(this.columns);
        this.PmInternalStakeholderService.columns = this.columns;
      }
    );
    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.columns = result;
        this.saveISALandingPageUserConfig();
        this.PmInternalStakeholderService.columns = this.columns;
      }
    });
  }

  addMember() {
    if (this.internalStakeholderAccess && this.addMemberAccess) {
      // const dialogRef = this.dialog.open(PmAddMemberComponent, {
      //   disableClose: true,
      //   data: {
      //     mode: 'Create',
      //     color: this.button,
      //     masterData: {
      //       entityList: this.entityList,
      //       divisionList: this.divisionList,
      //       subDivisionList: this.subDivisionList,
      //       orgMappingList: this.orgMappingList,
      //       orgEntityList: this.orgEntityList,
      //       orgDivisionList: this.orgDivisionList,
      //       orgSubDivisionList: this.orgSubDivisionList,
      //       quote_id_list: this.quote_id_list,
      //       enable_position: this.enable_position,
      //       rateCardList: this.rateCardList,
      //       project_end_date: this.project_end_date,
      //       project_start_date: this.project_start_date,
      //       withOpportunity: this.withOpportunity,
      //       customer_id: this.customer_id,
      //       currenyList: this.currencyList,
      //       positionList: this.positionList,
      //       rateUnitList: this.rateUnitList,
      //       rateLocationList: this.rateLocationList,
      //       quote_position_list: this.quote_position_list,
      //       rateCardConfiguration: this.rateCardConfiguration,
      //     },
      //   },
      //   viewContainerRef: this.memberViewContainerRef,
      // });

      // dialogRef.afterClosed().subscribe(async (result) => {
      //   if (result['messType'] == 'S') {
      //     this.qtcLoader = true;
      //     await this.retreiveData();
      //     this.allExpand ? this.expandAll() : this.collapseAll();
      //     await this.handleQTCConfiguration()
      //     this.qtcLoader = false;
      //   }
      //   else if(result['messType'] == 'A'){
      //     this.qtcLoader = true;
      //     await this.handleQTCConfiguration()
      //     this.qtcLoader = false;
      //   }
      // });
      let data = {
        mode: 'Create',
        color: this.button,
        masterData: {
          entityList: this.entityList,
          divisionList: this.divisionList,
          subDivisionList: this.subDivisionList,
          orgMappingList: this.orgMappingList,
          orgEntityList: this.orgEntityList,
          orgDivisionList: this.orgDivisionList,
          orgSubDivisionList: this.orgSubDivisionList,
          quote_id_list: this.quote_id_list,
          enable_position: this.enable_position,
          rateCardList: this.rateCardList,
          project_end_date: this.project_end_date,
          project_start_date: this.project_start_date,
          withOpportunity: this.withOpportunity,
          customer_id: this.customer_id,
          currenyList: this.currencyList,
          positionList: this.positionList,
          rateUnitList: this.rateUnitList,
          rateLocationList: this.rateLocationList,
          quote_position_list: this.quote_position_list,
          rateCardConfiguration: this.rateCardConfiguration,
        },
      };
      this.PmInternalStakeholderService.setAllocationData(data)
      this.router.navigate(['allocateMember'], { relativeTo: this.route});
    }
    else if(!this.addMemberAccess){
      const no_access =
      this.retrieveMessages.length > 0
        ? this.retrieveMessages[0].errors.no_access_status
          ? this.retrieveMessages[0].errors.no_access_status
          : 'Project is in Open Status! You are not having access to add member'
        : 'Project is in Open Status! You are not having access to add member';
      this.toasterService.showWarning(no_access, 10000);
    } else {
      const no_access =
        this.retrieveMessages.length > 0
          ? this.retrieveMessages[0].errors.no_access
            ? this.retrieveMessages[0].errors.no_access
            : 'You are not having access to add member'
          : 'You are not having access to add member';
      this.toasterService.showWarning(no_access, 10000);
    }
  }

  saveISALandingPageUserConfig() {
    let userConfiguration = {
      columnConfigData: this.columns,
      expandAll: this.allExpand,
    };

    this.pmService.saveUserConfiguration(
      userConfiguration,
      this.screen,
      this.currentDate
    );
  }

  async retrieveUserConfiguration() {
    await this.pmService.retrieveUserConfig(this.screen).then((res) => {
      if (res['messType'] == 'S') {
        const columnConfigData = res['data'] ? res['data']['columnConfigData'] : null;
      if (columnConfigData) {
        this.columns = this.columns.map(column => {
          const config = columnConfigData.find(config => config.name === column.name);
          if (config) {
            return {
              ...column,
              isVisible: config.isVisible,
              isDisabled: config.isDisabled,
              isActive: config.isActive
            };
          } else {
            return column; // Keep the existing values if not found in columnConfigData
          }
        });
      }
        this.allExpand = res['data']
          ? res['data']['expandAll']
          : this.allExpand;
        this.allExpand ? this.expandAll() : this.collapseAll();
      }
    });
  }
  async applyFilter(
    id: any,
    type: any,
    apply: boolean,
    color: any,
    i: number,
    icon: any
  ) {
    if (apply) {
      this.tags.push({
        name: id,
        type: type,
        apply: apply,
        color: color,
        i: i,
        icon: icon,
      });
    } else {
      this.tags = await this.deleteObjectById(this.tags, id);
      // console.log(this.tags);
    }
    if (this.tags.length > 0) {
      this.loading = true;
      this.data = [];
      this.parentData = [];
      let check_data = [];
      let check_parent = [];

      for (let item of this.tags) {
        if (item['type'] === 'role') {
          let check_multiple_condition = 0;
          let dataValue = _.findWhere(this.filterData, { role: item['name'] });

          if (dataValue != undefined) {
            // this.data.push(_.findWhere(this.filterData, { 'role': item['name'] }))
            for (let items of this.filterData) {
              if (items['role'] === item['name']) {
                check_data.push(items);
                check_parent.push(items);
                check_multiple_condition = 1;
              }
            }
          }
          if (check_multiple_condition == 0) {
            check_data = [];
            check_parent = [];
            break;
          }
        } else if (item['type'] === 'comercial') {
          let check_multiple_condition = 0;
          let value = item['name'] === 'Billable' ? 1 : 0;
          // console.log(value);
          let dataValue = _.findWhere(this.filterData, { billable: value });

          if (dataValue != undefined) {
            // this.data.push(_.findWhere(this.filterData, { 'billable': item['name'] }))
            for (let items of this.filterData) {
              if (items['billable'] == value) {
                check_data.push(items);
                check_parent.push(items);
                check_multiple_condition = 1;
              }
            }
          }
          if (check_multiple_condition == 0) {
            check_data = [];
            check_parent = [];
            break;
          }
        } else if (item['type'] === 'status') {
          let check_multiple_condition = 0;
          let dataValue = _.findWhere(this.filterData, {
            status: item['name'],
          });
          if (dataValue != undefined) {
            // this.data.push(_.findWhere(this.filterData, { 'status': item['name'] }))
            for (let items of this.filterData) {
              if (items['status'] === item['name']) {
                check_data.push(items);
                check_parent.push(items);
                check_multiple_condition = 1;
              }
            }
          }
          if (check_multiple_condition == 0) {
            check_data = [];
            check_parent = [];
            break;
          }
        }
      }
    
      this.data = await this.removeDuplicates(
        check_data,
        'stakeholder_reference_id'
      );
      // console.log(this.data);
      let parent_duplicate = await this.removeDuplicates(
        check_parent,
        'stakeholder_reference_id'
      );
      this.parentData = await this.removeDuplicates(
        check_parent,
        'associate_id'
      );
      // console.log(this.parentData);
      this.PmInternalStakeholderService.parentData = this.parentData;
      this.PmInternalStakeholderService.childData = this.data
      this.PmInternalStakeholderService.setParentData(this.parentData);
      this.loading = false;
    } else {
      this.data = this.filterData;
      this.parentData = this.filterParentData;
      this.PmInternalStakeholderService.parentData = this.parentData;
      this.PmInternalStakeholderService.childData = this.data
      this.PmInternalStakeholderService.setParentData(this.parentData);
    }
  }
  clearFilter() {
    this.tags = [];
    this.data = this.filterData;
    this.parentData = this.filterParentData;
    this.searchText = '';
    for (let item of this.project_role_list) {
      item['is_selected'] = false;
    }
    for (let item of this.status_list) {
      item['is_selected'] = false;
    }
    for (let item of this.comercial_list) {
      item['is_selected'] = false;
    }
    this.PmInternalStakeholderService.parentData = this.parentData;
    this.PmInternalStakeholderService.childData = this.data
    this.PmInternalStakeholderService.setParentData(this.parentData);

  }
  deleteObjectById(array: any[], idToDelete: any): any[] {
    return _.filter(array, (obj) => obj.name != idToDelete);
  }
  async closeTagFilter(id: any, type: any, color: any, i: any, icon: any) {

    if (type === 'role') {
      this.project_role_list[i].is_selected = false;
    } else if (type === 'status') {
      this.status_list[i].is_selected = false;
    } else if (type === 'comercial') {
      this.comercial_list[i].is_selected = false;
    }
    this.searchText = '';
    await this.applyFilter(id, type, false, color, i, icon);
  }
  removeDuplicates(array: any[], key: string): any[] {
    const seen = new Set();
    return array.filter((item) => {
      const value = item[key];
      if (seen.has(value)) {
        return false;
      }
      seen.add(value);
      return true;
    });
  }
  expandFilterToggle(type: any) {
    if (type == 'role') {
      this.roleFilterListOpened = true;
      this.searchText = '';
    } else if (type == 'status') {
      this.statusFilterListOpened = true;
      this.searchText = '';
    } else if (type == 'commercial') {
      this.commercialFilterListOpened = true;
      this.searchText = '';
    }
  }
  filteredList(type: any): any[] {
    if (type === 'status') {
      if (this.searchText != '') {
        const filterValue = this.searchText.toLowerCase();
        return this.status_list.filter((status) =>
          status.name.toLowerCase().includes(filterValue)
        );
      } else {
        return this.status_list;
      }
    } else if (type === 'role') {
      if (this.searchText != '') {
        const filterValue = this.searchText.toLowerCase();
        return this.project_role_list.filter((category) =>
          category.name.toLowerCase().includes(filterValue)
        );
      } else {
        return this.project_role_list;
      }
    } else if (type === 'commercial') {
      if (this.searchText != '') {
        const filterValue = this.searchText.toLowerCase();
        return this.comercial_list.filter((impact) =>
          impact.name.toLowerCase().includes(filterValue)
        );
      } else {
        return this.comercial_list;
      }
    }
  }
  resourceRequest() {
    let navigationUrl = `main/project-management/${this.projectID}/${this.project_name}/${this.itemID}/ ${this.item_name}/project-team/resource-request`;
    // console.log(navigationUrl);
    this.router.navigateByUrl(navigationUrl);
  }

  async getListValue(name, id) {
    let entityChoosen = _.uniq(_.pluck(this.orgMappingList, id));
    let val_result = [];
    for (let entit of entityChoosen) {
      let result = _.where(this.orgMappingList, { [id]: entit });
      if (result.length > 0) {
        val_result.push({
          id: result[0][id],
          name: result[0][name],
        });
      }
    }
    // console.log(name, entityChoosen, val_result);
    return val_result;
  }

  async initializeMasterData() {
    await this.masterService.getPMFormCustomizeConfigV().then((res: any) => {
      if (res) {
        this.formConfig = res;
        this.retrieveMessages = _.where(this.formConfig, {
          type: 'project-team',
          field_name: 'messages',
          is_active: true,
        });
        let retrieveQ2c = _.where(this.formConfig, {
          type: 'project-team',
          field_name: 'q2c_display',
          is_active: true,
        });
        this.q2cVisible = retrieveQ2c.length > 0 ? (retrieveQ2c[0]['is_visible'] ? retrieveQ2c[0]['is_visible'] : false) : false

        let show_identity = _.findWhere(this.formConfig, {type:"project-team", field_name:"show-identity", is_active: true})

        this.showIdentity = show_identity ? true: false;


        let inactivePositionStatusConfig = _.where(this.formConfig, {
          type: 'position-card',
          field_name: 'inactive_position',
          is_active: true,
        });
    
        this.inactivePositionStatus = (inactivePositionStatusConfig && inactivePositionStatusConfig.length > 0 ? (inactivePositionStatusConfig[0].inactive_position_status ? inactivePositionStatusConfig[0].inactive_position_status  : []) : [])

        console.log("Show-identity", this.showIdentity)
        const retrieveStyles = _.where(this.formConfig, {
          type: 'project-theme',
          field_name: 'styles',
          is_active: true,
        });
        this.button =
          retrieveStyles.length > 0
            ? retrieveStyles[0].data.button_color
              ? retrieveStyles[0].data.button_color
              : '#90ee90'
            : '#90ee90';
        document.documentElement.style.setProperty('--intButton', this.button);
        this.fontStyle =
          retrieveStyles.length > 0
            ? retrieveStyles[0].data.font_style
              ? retrieveStyles[0].data.font_style
              : 'Roboto'
            : 'Roboto';
        document.documentElement.style.setProperty('--intFont', this.fontStyle);
        if (retrieveStyles.length > 0) {
          this.noDataImage = retrieveStyles[0].data.no_data_image
            ? retrieveStyles[0].data.no_data_image
            : 'https://assets.kebs.app/No-milestone-image.png';
        }
        let enable_external_employee_id_data = _.where(this.formConfig, {
          type: 'project-team',
          field_name: 'enable_external_employee_id',
          is_active: true,
        });
        this.enable_external_employee_id=enable_external_employee_id_data.length>0 ? true :false
        this.columns[2].name=enable_external_employee_id_data.length>0 ?enable_external_employee_id_data[0].name : 'External Employee ID'
      }
    });

    await this.masterService.getManPowerResourceType().then((res)=>{
      this.resourceTypeList = res;
    })
    
    await this.masterService.getProjectRoleMaster().then((res) => {
      this.project_role_list = res;
      
      for (let item of this.project_role_list) {
        item['is_selected'] = false;
        item['color'] =
          item['udrf_summary_card'] != null &&
          item['udrf_summary_card'] != '' &&
          item['udrf_summary_card'] != ' ' &&
          item['udrf_summary_card'] != 'null'
            ? typeof JSON.parse(item['udrf_summary_card']) == 'string'
              ? JSON.parse(item['udrf_summary_card']).color
              : item['udrf_summary_card']['color']
            : '#FFFFF';
        item['icon'] = 'account_circle';
      }
    });

    
    this.masterService.getEntityList().then((res) => {
      this.entityList = res;
    });
   
    this.masterService.getDivisionList().then((res) => {
      this.divisionList = res;
    });

    this.masterService.getSubDivisionList().then((res) => {
      this.subDivisionList = res;
    });

    this.masterService.currencyList().then((res) => {
      this.currencyList = res;
    });

    await this.masterService.getPositionList().then((res) => {
      this.positionList = res;
    });

   


    this.PmInternalStakeholderService.getRateCardConfiguration().then((res) => {
      if (res['messType'] == 'S') {
       
        this.rateCardConfiguration = res['data'];
      }
    });
    this.PmInternalStakeholderService.getOrgMapping().then(async (res: any) => {
      if (res['messType'] == 'S') {
        this.orgMappingList = res['data'];
        this.orgEntityList = await this.getListValue(
          'entity_name',
          'entity_id'
        );
        this.orgDivisionList = await this.getListValue(
          'division_name',
          'division_id'
        );
        this.orgSubDivisionList = await this.getListValue(
          'sub_division_name',
          'sub_division_id'
        );
      }
    });

    await this.PmInternalStakeholderService.getProjectFinnacialData(
      this.itemID
    ).then(async (res) => {
      if (res['messType'] == 'S') {
        if (res['data'].length > 0) {
          let project_has_quote = 0;
          for (let i = 0; i < res['data'].length; i++) {
            if (res['data'][i].quote_id != null) {
              project_has_quote = 1;
              this.quote_id_list.push(res['data'][i].quote_id);
            }
          }
          if (project_has_quote > 0) {
            this.enable_position = true;
            await this.PmInternalStakeholderService.getQuotePosition(
              this.quote_id_list, this.resourceTypeList
            ).then((res) => {
              if (res['messType'] == 'S') {
                this.quote_position_list = res['data'];
              }
            });
          } else {
            this.enable_position = false;
          }
        }
      } else {
        this.enable_position = false;
      }
    });
  
    await this.PmInternalStakeholderService.getProjectQuote(
      this.projectID,
      this.itemID
    ).then((res) => {
      if (res['messType'] == 'S') {
        if (res['data'].length > 0) {
          this.project_end_date = moment(res['data'][0].planned_end_date)
            .utc()
            .format('YYYY-MM-DD');
          this.project_start_date = moment(res['data'][0].planned_start_date)
            .utc()
            .format('YYYY-MM-DD');
          

          this.withOpportunity = res['data'][0].with_opportunity;
          this.customer_id = res['data'][0]['end_customer_id'];
          this.service_type = res['data'][0]['service_type_id'];
          this.item_status_id = res['data'][0]['item_status_id'];
          this.PmInternalStakeholderService.projectServiceType = this.service_type;
        }
      }
    });

    await this.masterService.getProjectStatusMatrix().then((res)=>{
      this.projectStatusMatrix = res;

      console.log("Service Type", this.service_type, this.item_status_id)
      let condition = _.where(this.projectStatusMatrix,{service_type_id: this.service_type ,project_status_id: this.item_status_id, is_direct_allocation_allowed: 1})
      console.log(condition)
      if(condition.length==0)
      {
          this.addMemberAccess = false
      }
      else
      {
          this.addMemberAccess=true;
      }
    })

    console.log("Add Member Access", this.addMemberAccess)


    this.PmInternalStakeholderService.getCustomerRateCard(
      this.customer_id,
      moment().format('YYYY-MM-DD')
    ).then((res) => {
      if (res['messType'] == 'S') {
        this.rateCardList = res['data'];
      }
    });

   
    this.PmInternalStakeholderService.getQuoteUnitList().then((res) => {
      this.rateUnitList = res;
    });


    await this.masterService.getOfficeLocationList().then((res) => {
      this.rateLocationList = res;
    });


    await this.PmInternalStakeholderService.getHeadForISA(this.itemID).then(
      (res) => {
        if (res['data'].length > 0) {
          for (let item of res['data']) {
            this.is_edit_aid = item['associate_id'];
          }
        }
      }
    );

    await this.masterService.serviceTypeList().then((res)=>{
      this.serviceTypeList = res;
    });

  }

  resourceLoading(){
    // this.router.navigate(['/team/resourceLoading']);
    this.router.navigate(['resourceLoading'], { relativeTo: this.route });
  }

  @HostListener('window:resize')
  onResize() {
    this.calculateDynamicStyle();
  }
  calculateDynamicStyle() {
    let width = window.innerWidth - 142 + 'px';
    document.documentElement.style.setProperty('--dynamicISAWidth', width);
    let tableHeight = window.innerHeight - 266 + 'px';
    document.documentElement.style.setProperty(
      '--dynamicISAHeight',
      tableHeight
    );
  }

  async getQuoteCardData() {
    await this.masterService.getManPowerResourceType().then((res)=>{
      this.resourceTypeList = res;
    })

    console.log("Resource Type List", this.resourceTypeList)
    await this.PmInternalStakeholderService.getQuoteListDetails(this.projectID,this.itemID, this.resourceTypeList).then((res) => {
      if (res['messType'] == 'S') {
        this.quoteCardDetails = res['data']
      }
    });
  }


  handleQuoteFunction(event:any){
  
    if(event.type == 'E'){
      this.editMember(event.items);
    }
    else if(event.type == 'D'){
      this.duplicateMember(event.items);
    }
    else if(event.type == 'R'){
      this.deleteISA(event.items)
    }
    else if(event.type == 'C'){
      this.allocatePositionMember(event)
    }
  }

  /**
   * @description For Calculating Allocated Hrs For Employee
   */
  getAllocatedHrs(item){
    let startDate = moment(item.start_date);
    let endDate = moment(item.end_date)
    return new Promise(async (resolve, reject) => {
      this.subs.sink = (await this.PmInternalStakeholderService
        .getEmployeeAllocatedHrs(startDate, endDate, item?.associate_id))
        .subscribe(
          (res: any) => {
            if (res['messType'] == 'S') resolve(res.data);
            else resolve(null);
          },
          (err) => {
            console.log(err);
            resolve(null);
          }
        );
    });
  }

//   /**
//    * @description For Fetching planned Allocated Hours from Finance
//    */
//   async getPlannedAllocatedHours(item) {
//     try {
//         let startDate = moment(item.start_date);
//         let endDate = moment(item.end_date)
//         const res = await this.PmInternalStakeholderService.getPlannedAllocatedHours(this.itemID,'QTC',startDate,endDate,item?.split_percentage,item?.associate_id);
//         if(res && res['messType'] == 'S'){
//           return res
//         }
//         else
//           return null
//     } catch (error) {
//         console.error('Error fetching finance hours:', error);
//         return null;
//     }
// }

  deMobilizeBulk(){
   
    if(this.PmInternalStakeholderService.demobilizedData.length > 0){
      this.toasterService.showInfo('Demobilizing Scheduled ',1000)
      this.PmInternalStakeholderService.removeBulkMember(this.PmInternalStakeholderService.demobilizedData).then((res) => {
        
      });
    }
    
  }

  getUniquePositionIds(quotes) {
    const positionIds = quotes.flatMap(quote => quote.positions.map(position => position.position_id));
    const uniquePositionIds = Array.from(new Set(positionIds));
    return uniquePositionIds;
}

  filterNonMatching(positionList, isaData) {
    return isaData.filter(item1 => 
        !positionList.some(item2 => item1.rate_card_id === item2)
    );
  }

  async formatQuoteDetails() {
    if (this.quoteCardDetails.length > 0) {
        const quotePromises = this.quoteCardDetails.map(async (quote) => {
            // let allocated = 0;
            let remaining = 0;
            let expand = false;
            if(this.qtcConfig && this.qtcConfig.length > 0){
              let expand_data = _.where(this.qtcConfig, {
                type: 'quote',
                id: quote.quote_id,
                is_active: true,
              });
              expand = (expand_data && expand_data.length > 0) ? expand_data[0]['expand'] ? expand_data[0]['expand'] : false : false;
            }
            const positionPromises = quote.positions.map(async (position) => {

              
                const hoursData = await this.getFinanceHours(position.position_id);
                // let consumed = 0;
                let remainingHours = 0;
                let total_billed_hours = 0;
                let month_wise_billed_hours = []
                let expand = false;
                let expandAll = false;
                if(this.qtcConfig && this.qtcConfig.length > 0){
                  let expand_data = _.where(this.qtcConfig, {
                    type: 'position',
                    id: position.position_id,
                    is_active: true,
                  });
                  expand = (expand_data && expand_data.length > 0) ? expand_data[0]['expand'] ? expand_data[0]['expand'] : false : false;
                  expandAll = (expand_data && expand_data.length > 0) ? expand_data[0]['expandAll'] ? expand_data[0]['expandAll'] : false : false;
                }
                if(hoursData){
                  // consumed = hoursData['allocated_hours'] ? hoursData['allocated_hours'] : 0;
                  remainingHours = hoursData['remaining_billable_hours'] ? hoursData['remaining_billable_hours'] : 0;
                  total_billed_hours = hoursData['billed_hours'] ? hoursData['billed_hours'] : 0;
                  month_wise_billed_hours = hoursData['month_wise_billed_hours'] ? hoursData['month_wise_billed_hours'] : []
                }

                // position['consumed'] = consumed;
                position['remaining'] = remainingHours;
                position['expand'] = expand;
                position['expandAll'] = expandAll;
                position['total_billed_hours'] = total_billed_hours;
                position['month_wise_billed_hours'] = month_wise_billed_hours;
                // allocated += consumed;
                if(!(this.inactivePositionStatus.length > 0 && _.includes(this.inactivePositionStatus, position['position_status'])))
                {
                  remaining += remainingHours;
                }
            });

            await Promise.all(positionPromises);
            // quote['consumed'] = allocated;
            quote['remaining'] = remaining;
            quote['expand'] = expand;
        });

        await Promise.all(quotePromises);
    }
}

async getFinanceHours(position_id) {
    try {
        const res = await this.PmInternalStakeholderService.getRemainingHours(this.itemID, position_id, 'QTC');
        return res;
    } catch (error) {
        console.error('Error fetching finance hours:', error);
        return null;
    }
}

async handleQTCConfiguration(){
    if(this.withOpportunity && this.q2cVisible){
      this.loading = true;
      this.hideCreation = true;
      this.PmInternalStakeholderService.columns = this.columns;
      this.PmInternalStakeholderService.parentData = this.parentData;
      this.PmInternalStakeholderService.childData = this.data
      this.PmInternalStakeholderService.setParentData(this.parentData);
      this.qtcConfig = await this.PmInternalStakeholderService.getQTCUserConfig(this.projectID,this.itemID)
      let serviceData = _.where(this.serviceTypeList, {
        id: this.service_type,
      });
    
      let resourceLoadingApplicable = (serviceData && serviceData.length > 0) ? serviceData[0]['is_resource_loading_applicable'] ? serviceData[0]['is_resource_loading_applicable'] : 0 : 0;
      
      let disable_billing_plan = (serviceData && serviceData.length > 0) ? serviceData[0]['deliverable_based_billing_plan'] ? serviceData[0]['deliverable_based_billing_plan'] : 0 : 0;

      console.log("Disable Billing Plan", disable_billing_plan)

      if (this.resourceLoadingAccess && resourceLoadingApplicable && !disable_billing_plan) {
        this.resourceLoadingVisible = true;
      }
      else{
        this.resourceLoadingVisible = false;
      }
      await this.getQuoteCardData();
      await this.formatQuoteDetails();
     
      let positionList = this.getUniquePositionIds(this.quoteCardDetails);
      this.noQuoteList  = this.filterNonMatching(positionList,this.data)
      if(this.noQuoteList && this.noQuoteList.length > 0){
        this.unSpecifiedCheck  = true;
        let expand = false,expandAll = false;
        if(this.qtcConfig && this.qtcConfig.length > 0){
          let expand_data = _.where(this.qtcConfig, {
            type: 'quote',
            id: 0,
            is_active: true,
          });
          let position_expand_data = _.where(this.qtcConfig, {
            type: 'position',
            id: 0,
            is_active: true,
          });
          expand = (expand_data && expand_data.length > 0) ? expand_data[0]['expand'] ? expand_data[0]['expand'] : false : false;
          expandAll = (position_expand_data && position_expand_data.length > 0) ? position_expand_data[0]['expandAll'] ? position_expand_data[0]['expandAll'] : false : false;
        }
        this.noQuoteData = {
          "quote_id": 0,
          "quote_name": "-",
          "expand": expand,
          "positions": [
              {
                  "position_id": 0,
                  "position_name": "-",
                  "quantity": 0,
                  "expand":true,
                  "expandAll":expandAll
              }
          ]
        }
      }      
      this.loading = false;
    }
}

allocatePositionMember(item: any) {
  if (this.internalStakeholderAccess &&  this.addMemberAccess) {

    let data = {
      mode: 'Create',
      withPosition:true,
      data: item,
      color: this.button,
      masterData: {
        entityList: this.entityList,
        divisionList: this.divisionList,
        subDivisionList: this.subDivisionList,
        orgMappingList: this.orgMappingList,
        orgEntityList: this.orgEntityList,
        orgDivisionList: this.orgDivisionList,
        orgSubDivisionList: this.orgSubDivisionList,
        quote_id_list: this.quote_id_list,
        enable_position: this.enable_position,
        rateCardList: this.rateCardList,
        project_end_date: this.project_end_date,
        project_start_date: this.project_start_date,
        withOpportunity: this.withOpportunity,
        customer_id: this.customer_id,
        currenyList: this.currencyList,
        positionList: this.positionList,
        rateUnitList: this.rateUnitList,
        rateLocationList: this.rateLocationList,
        quote_position_list: this.quote_position_list,
        rateCardConfiguration: this.rateCardConfiguration,
      },
    }
    this.PmInternalStakeholderService.setAllocationData(data)
    this.router.navigate(['allocateMember'], { relativeTo: this.route});
  }
  else if(!this.addMemberAccess){
    const no_access =
    this.retrieveMessages.length > 0
      ? this.retrieveMessages[0].errors.no_access_status
        ? this.retrieveMessages[0].errors.no_access_status
        : 'Project is in Open Status! You are not having access to add member'
      : 'Project is in Open Status! You are not having access to add member';
  this.toasterService.showWarning(no_access, 10000);
  } else {
    const no_access =
      this.retrieveMessages.length > 0
        ? this.retrieveMessages[0].errors.no_access
          ? this.retrieveMessages[0].errors.no_access
          : 'You are not having access to add member'
        : 'You are not having access to add member';
    this.toasterService.showWarning(no_access, 10000);
  }
}

async processAllocatedHours() {
  const promises = this.data.map(async (item) => {
    // let finance_allocated_hrs_data = await this.getPlannedAllocatedHours(item);
    let prj_allocated_hrs_data = await this.getAllocatedHrs(item);
    let allocated_hours: number = 0;
    if (prj_allocated_hrs_data && prj_allocated_hrs_data['allocated_hours']) {
      allocated_hours = parseFloat((parseFloat(prj_allocated_hrs_data['allocated_hours']) * (item['split_percentage'] / 100)).toFixed(2));
    }
    item.allocated_hours = allocated_hours;
    return item;
  });

  // Wait for all promises to resolve
  await Promise.all(promises);
}

  ngDestroy(){
      this.PmInternalStakeholderService.setParentData(null);
  }

  async initializePage() {
    this.recentSearch = JSON.parse(localStorage.getItem('Project-team-recent-search-' + this.itemID)) &&
      JSON.parse(localStorage.getItem('Project-team-recent-search-' + this.itemID)).length > 0 ?
      JSON.parse(localStorage.getItem('Project-team-recent-search-' + this.itemID)) : [];
  }

  openSearchBarOverlay(triggerField) {
    if (!this.overlayRef?.hasAttached()) {
      const positionStrategyBuilder = this._overlay.position();
      const positionStrategy = positionStrategyBuilder.flexibleConnectedTo(triggerField).withFlexibleDimensions(true).withPush(true).withViewportMargin(25).withGrowAfterOpen(true).withPositions([
        {
          originX: 'center',
          originY: 'bottom',
          overlayX: 'center',
          overlayY: 'top'
        }
      ]);
      if (this.hideCreation) {
        positionStrategy.withDefaultOffsetX(-246);
      } else {
        positionStrategy.withDefaultOffsetX(-210);
      }
      positionStrategy.withDefaultOffsetY(-34);
      // positionStrategy.withDefaultOffsetX(-210);
      const scrollStrategy = this._overlay.scrollStrategies.close();
      this.overlayRef = this._overlay.create({
        positionStrategy,
        scrollStrategy,
        hasBackdrop: true,
        backdropClass: 'cdk-overlay-dark-backdrop',
        // backdropClass: '',
        panelClass: ['pop-up'],
      });
      const templatePortal = new TemplatePortal(
        this.triggerSearchBarTemplateRef,
        this._viewContainerRef
      );
      this.overlayRef.attach(templatePortal);
      this.overlayRef.backdropClick().subscribe(() => {
        this.closeOverlay();
      });
    }
  }

  closeOverlay() {
    this.overlayRef?.dispose();
    this.searchParam = '';
    this.searchData = [];
  }
  
  async onSearch() {
    if (this.searchParam == '' || !this.searchParam) {
      this.searchSubmitted = false;
      return;
    }
    this.isSearchLoading = true;
    this.searchSubmitted = false;
    try {
      const res = await this.PmInternalStakeholderService.searchISAData(parseInt(this.itemID), this.currentDate, this.searchColumn, this.searchParam);
      this.searchData = res['data'];
      this.isSearchLoading = false;
      this.searchSubmitted = true; 
    } catch (error) {
      console.error("Error fetching search data", error);
      this.isSearchLoading = false;
      this.searchSubmitted = true; 
    }
  }

  onCardClick(item) {
    console.log(this.data)
    const editItem = this.data.find(it => it.isa_id === item.id && item.expand === 1);
    if (editItem) {
      this.editMember(editItem);
      this.closeOverlay();
    }
  }

  async onEnterSearch(searchData) {
    if (searchData && searchData != '') {
      if (!this.recentSearch.includes(searchData)) {
        if (this.recentSearch.length == 5) {
          this.recentSearch.pop();
        }
        this.recentSearch.unshift(searchData);
        localStorage.setItem('Project-team-recent-search-' + this.itemID, JSON.stringify(this.recentSearch));
      } else {
        this.recentSearch = this.recentSearch.filter(
          (obj) => obj != searchData
        );
        this.recentSearch.unshift(searchData);
        localStorage.setItem('Project-team-recent-search-' + this.itemID, JSON.stringify(this.recentSearch));
      }
    }
    this.searchData = searchData;
    this.onSearch();
  }

  onSearchInputChange(){
    this.searchSubmitted = false;
    this.searchData = []; 
  }

  onSelectRecentSearch(index: number) {
    this.searchParam = this.recentSearch[index];
    this.onSearch();
  }

  onToggleClick(event: MouseEvent) {
    event.stopPropagation();
    this.searchConfigToggle.toggle();
  }

  formatDateString(dateString: string): string {
    const [day, month, year] = dateString.split('-');
    return `${day} ${month} ${year}`;
  }

  formatDateRange(startDate: string, endDate: string): string {
    return `${this.formatDateString(startDate)} - ${this.formatDateString(endDate)}`;
  }

}
