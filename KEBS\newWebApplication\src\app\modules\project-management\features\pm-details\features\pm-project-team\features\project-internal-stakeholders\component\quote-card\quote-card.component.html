<div class="card quote-card" *ngIf="data">
    <div class="quote-detail">
        <div class="row card-col-arr">
            <mat-icon class="arrow-icn" *ngIf="!data?.expand" (click)="onExpandClick()">keyboard_arrow_right</mat-icon>
            <mat-icon class="arrow-icn" *ngIf="data?.expand" (click)="onExpandClick()">keyboard_arrow_down</mat-icon>
        </div>
        <div class="row card-col text-class" [ngStyle]="{'max-width': getMaxWidth('quote_id', 'quote-card')}" *ngIf="'quote_id' | checkActive : this.formConfig : 'quote-card' ">
          <span class="card-id-header">{{('quote_id' | checkLabel : this.formConfig: 'quote-card': 'QID')}} : {{data.quote_id ? data.quote_id : '-'}}</span>
          <span class="card-id-body" tooltip ="{{data.quote_name ? data.quote_name : '-'}}">{{data.quote_name ? data.quote_name : '-'}}</span>
        </div>
        <mat-divider [vertical]="true" *ngIf="'quantity' | checkActive : this.formConfig : 'quote-card' "></mat-divider>
        <!-- <div class="row card-col">
          <span class="data-header">Rate</span>
          <span class="data-body">{{data.currency ? data.currency : '-'}} {{data.rate ? data.rate : '-'}}</span>
        </div>
        <mat-divider [vertical]="true"></mat-divider> -->
        <div class="row card-col"  [ngStyle]="{'max-width': getMaxWidth('quantity', 'quote-card')}" *ngIf="'quantity' | checkActive : this.formConfig : 'quote-card' ">
          <span class="data-header">{{('quantity' | checkLabel : this.formConfig: 'quote-card': 'Quantity')}}</span>
          <span class="data-body"><span tooltip ="{{data.quantity ? data.quantity : 0}}">{{data.quantity ? data.quantity : 0}}</span></span>
        </div>
        <mat-divider [vertical]="true"  *ngIf="!noQuote && ('position_quantity' | checkActive : this.formConfig : 'quote-card')"></mat-divider>
        <div class="row card-col"  [ngStyle]="{'max-width': getMaxWidth('position_quantity', 'quote-card')}"  *ngIf="!noQuote && ('position_quantity' | checkActive : this.formConfig : 'quote-card')">
          <span class="data-header">{{('position_quantity' | checkLabel : this.formConfig: 'quote-card': 'Position Quantity')}}</span>
          <span class="data-body"> <span tooltip ="{{data.position_quantity ? (data.position_quantity | decimal:1 ) : 0}}">{{ data.position_quantity ? (data.position_quantity | decimal:1 ) : 0 }}</span></span>
        </div>
        <mat-divider [vertical]="true" *ngIf="!noQuote && ('allocated_hrs' | checkActive : this.formConfig : 'quote-card')"></mat-divider>
        <div class="row card-col"  [ngStyle]="{'max-width': getMaxWidth('allocated_hrs', 'quote-card')}" *ngIf="!noQuote && ('allocated_hrs' | checkActive : this.formConfig : 'quote-card')">
          <span class="data-header">{{('allocated_hrs' | checkLabel : this.formConfig: 'quote-card': 'Allocated')}}</span>
          <span class="data-body"> <span tooltip ="{{data.consumed ? (data.consumed | decimal:1 ) : 0}}">{{data.consumed ? (data.consumed | decimal:1 ) : 0}}</span></span>
        </div>
        <mat-divider [vertical]="true" *ngIf="!noQuote && ('remaining_hrs' | checkActive : this.formConfig : 'quote-card')"></mat-divider>
        <div class="row card-col"  [ngStyle]="{'max-width': getMaxWidth('remaining_hrs', 'quote-card')}" *ngIf="!noQuote && ('remaining_hrs' | checkActive : this.formConfig : 'quote-card')">
          <span class="data-header">{{('remaining_hrs' | checkLabel : this.formConfig: 'quote-card': 'Remaining')}}</span>
          <span class="data-body" ><span tooltip ="{{data.remaining ? (data.remaining | decimal:1 ) : 0}}">{{data.remaining ? (data.remaining | decimal:1 ) : 0}}</span></span>
        </div>
        <mat-divider [vertical]="true" *ngIf="!noQuote && resourceLoadingAccess"></mat-divider>
        <div class="row card-col-action" *ngIf="!noQuote && resourceLoadingAccess">
          <span class="data-header">{{('action' | checkLabel : this.formConfig: 'quote-card': 'Action')}}</span>
          <div class="action-class" (click)="routeToResourceLoading()">
            <img src="https://assets.kebs.app/calculator_animation.gif" class="img-class">
            <span class="action-class-txt">{{'resource_loading_plan_name' | checkLabel : this.formConfig: 'quote-card': 'Billing Plan'}}</span>
          </div>
        </div>
        </div>
        <div class="position-detail" *ngIf="data.expand">
            <div *ngFor="let position of data.positionIdList">
                <app-position-card [position]="position" [noQuote]="noQuote" [noQuoteList]="noQuoteList" (triggerPositionEvent)="handlePositionFunction($event)"></app-position-card>
            </div>
        </div>
      </div>