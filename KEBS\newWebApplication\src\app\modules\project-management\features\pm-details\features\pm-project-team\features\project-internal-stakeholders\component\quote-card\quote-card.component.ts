import { Component, EventEmitter, Input, OnInit, Output  } from '@angular/core';
import { PmInternalStakeholderService } from '../../services/pm-internal-stakeholder.service';
import { PmMasterService } from 'src/app/modules/project-management/services/pm-master.service';
import { takeUntil } from 'rxjs/operators';
import { Subject } from 'rxjs';
import moment from 'moment';
import { Router } from '@angular/router';
import * as _ from 'underscore';
@Component({
  selector: 'app-quote-card',
  templateUrl: './quote-card.component.html',
  styleUrls: ['./quote-card.component.scss']
})
export class QuoteCardComponent implements OnInit {
  @Input() quote:any;
  @Input() noQuote:boolean = false;
  @Input() noQuoteList:any = [];
  @Input() resourceLoadingAccess:boolean = false;
  @Output() triggerQuoteEvent: EventEmitter<any> = new EventEmitter<any>();
  data:any;
  // total:any;
  formConfig:any;
  itemId:any;
  projectId:any;
  inactivePositionStatus: any;
  protected _onDestroy = new Subject<void>();
  constructor( private masterService: PmMasterService,private _isService:PmInternalStakeholderService,private _router: Router) { }

  async ngOnInit() {
    this.itemId = parseInt(this._router.url.split('/')[5]);
    this.projectId = parseInt(this._router.url.split('/')[3]);
    await this.masterService.getPMFormCustomizeConfigV().then((res: any) => {
      if (res) {
        this.formConfig = res;
      }
    });
    const retrieveStyles = _.where(this.formConfig, {
      type: 'project-theme',
      field_name: 'styles',
      is_active: true,
    });
    let fontStyle =
      retrieveStyles.length > 0
        ? retrieveStyles[0].data.font_style
          ? retrieveStyles[0].data.font_style
          : 'Roboto'
        : 'Roboto';
    document.documentElement.style.setProperty('--teamFont', fontStyle);
    let inactivePositionStatusConfig = _.where(this.formConfig, {
      type: 'position-card',
      field_name: 'inactive_position',
      is_active: true,
    });

    this.inactivePositionStatus = (inactivePositionStatusConfig && inactivePositionStatusConfig.length > 0 ? (inactivePositionStatusConfig[0].inactive_position_status ? inactivePositionStatusConfig[0].inactive_position_status  : []) : [])

    this.data = await this.getQuoteData(this.quote);
  }

  async getQuoteData(quote){
    let posQuantity
    if(this.noQuote){
      posQuantity =  this.noQuoteList.length > 0 ? this.noQuoteList.length : 0
    }
    else{
      
      posQuantity = await this.getActivePositionCount(quote.positions ? quote.positions : [])
    }
    let quoteData = {
      quote_id: quote.quote_id ? quote.quote_id : null ,
      quote_name: quote.quote_name ? quote.quote_name : '-',
      rate:15000,
      currency:'INR',
      quantity:posQuantity,
      position_quantity: this.getPositionQuantity(quote.positions ? quote.positions : []),
      // consumed: quote.consumed ? quote.consumed : 0,
      remaining: quote.remaining ? quote.remaining : 0,
      consumed: await this.getQuoteConsumed(quote.positions ? quote.positions : []),
      // remaining: this.getRemaining(quote.positions ? quote.positions : []),
      positionIdList: quote.positions ? quote.positions : [],
      expand:quote.expand ? quote.expand : false
    }
    return quoteData;
  }
  async onExpandClick(){
    this.data.expand = !this.data.expand
    await this.saveQTCConfig()
  }

  handlePositionFunction(positionEvent:any){
    if(positionEvent.type == 'C'){
      let item = {
        position_id: positionEvent.item ? positionEvent.item : null,
        rate_card_id: this.data.quote_id ? this.data.quote_id : null
      }
      this.triggerQuoteEvent.emit({item:item,type:'C'})
    }    
    else{
      this.triggerQuoteEvent.emit(positionEvent)
    }
  }

  async getQuoteConsumed(positionList) {
    const promises = positionList.map(position => this._isService.getPositionAvailableHours(position.position_id));
    const allPositionData = await Promise.all(promises);

    let consumed = 0;
    for(let item of allPositionData){
      // if(this.inactivePositionStatus.length > 0 && _.includes(this.inactivePositionStatus, item.position_status)){
      //   continue;
      // }
      consumed += (typeof item === 'number' ? item : 0);
    }
    
    return consumed;
  }

  getRemaining(positionList): number {
      let consumed : any = this.getQuoteConsumed(positionList);
      let remaining = this.getPositionQuantity(positionList) - consumed;
      if (remaining < 0) {
          return 0;
      }
      return remaining;
  }
  /**
   * @description saving the User QTC Config
   */
  saveQTCConfig(){
    let config = {
      "type":"quote",
      "expand":this.data.expand,
      "id":this.data.quote_id,
      "expandAll":false,
      "project_id":this.projectId,
      "item_id":this.itemId,
      "date": moment()
    }
    return new Promise((resolve,reject) => {
      this._isService.saveMemberQTCConfig(config).pipe(takeUntil(this._onDestroy))
      .subscribe({
        next: (res) => {
          if(res['messType'] == 'S') {
            resolve(true)
          }
          else{
            resolve(false)
          }
        },
        error:(err) => {
          resolve(false);
        }
      });
    });
  }

  getPositionQuantity(positionList){
    let quantity = 0
    for (let item of positionList) {
      if(this.inactivePositionStatus.length > 0 && _.includes(this.inactivePositionStatus, item.position_status)){
        continue;
      }
      quantity += (item['quantity'] ? item['quantity'] : 0)
    }
    return quantity;
  }

  async getActivePositionCount(positionList){
    let quantity = 0
    for (let item of positionList) {
      if(this.inactivePositionStatus.length > 0 && _.includes(this.inactivePositionStatus, item.position_status)){
        continue;
      }
      quantity += 1
    }
    return quantity;
  }

  /**
   * @description Routing to Resource Loading Module
   */
  routeToResourceLoading(){
    this._router.navigateByUrl(`main/project-management/${this.projectId}/${this.itemId}/${this.data.quote_id}/resource_loading`);
  }

  /**
   * @description Getting the Max Width
   * @param fieldName 
   * @param type 
   * @returns 
   */
  getMaxWidth(fieldName: string, type: string): string {
    const item = this.formConfig.find(d => d.field_name === fieldName && d.type === type);
    if (item && item.max_width) {
      return `max-width: ${item.max_width}%`;
    }
    return ''; // No max-width applied if no data
  }

}
