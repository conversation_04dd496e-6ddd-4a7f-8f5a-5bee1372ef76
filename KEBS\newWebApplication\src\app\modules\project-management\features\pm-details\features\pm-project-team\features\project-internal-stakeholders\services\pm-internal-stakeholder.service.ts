import {Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from "@angular/common/http";
import { BehaviorSubject  } from 'rxjs';
import { PmMasterService } from 'src/app/modules/project-management/services/pm-master.service';
import * as _ from 'underscore';
@Injectable({
  providedIn: 'root'
})
export class PmInternalStakeholderService {
  columns:any = [];
  parentData:any = [];
  childData:any = [];
  demobilizedData:any = [];
  projectServiceType: any;
  private allocationData$ = new BehaviorSubject<any>(null);
  memberData$ = this.allocationData$.asObservable();

  private itemData$ = new BehaviorSubject<any>(null);
  parentData$ = this.itemData$.asObservable();
  constructor(private http: HttpClient,private masterService: PmMasterService) { }

  retrieveISAData(projectID,currentDate){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/planning/retrieveISAData",{projectID:projectID,currentDate:currentDate}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }
  getOrgMappingForEmployee(associate_id, date){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/planning/getOrgMappingForEmployee",{associate_id: associate_id, currentDate: date}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }
  async addTeamMember(projectId, itemId, member, oldData, newData) {


    return new Promise((resolve, reject)=>{
      this.http.post("/api/pm/planning/addTeamMember",{projectId:projectId, itemId:itemId, member:member, oldData:oldData, newData: newData }).subscribe((res)=>{
        resolve(res)
      },(err)=>{
        reject(err)
      })
    })
   

}

async removeMember(stakeholder_table_id, stop_date, type) {
  return new Promise((resolve, reject) => {
    this.http
      .post("/api/pm/planning/removeMember", {
        stakeholder_table_id,
        stop_date,
        type
      })
      .subscribe(res => {
        return resolve(res);
      });
  });
}
async getHeadForISA(itemId) {


  return new Promise((resolve, reject)=>{
    this.http.post("/api/pm/planning/getHeadForISA",{itemId:itemId}).subscribe((res)=>{
      resolve(res)
    },(err)=>{
      reject(err)
    })
  })
 

}
getOrgMapping(){
  return new Promise((resolve, reject) => {
    this.http.post("/api/pm/planning/getEmployeeDirectoryOrgMapping",{}).subscribe((res) => {
      return resolve(res)
    }, (err) => {
      return reject(err)
    })
  })
}
getProjectQuote(project_id,item_id){
  return new Promise((resolve, reject) => {
    this.http.post("/api/pm/planning/getProjectQuote",{project_id,item_id}).subscribe((res) => {
      return resolve(res)
    }, (err) => {
      return reject(err)
    })
  })
}
getDOJ(aid){
  return new Promise((resolve, reject) => {
    this.http.post("/api/pm/planning/getDOJ",{aid}).subscribe((res) => {
      return resolve(res)
    }, (err) => {
      return reject(err)
    })
  })
}
getreportsToMaster(projectID,currentDate){
    
  return new Promise((resolve, reject) => {
    this.http.post("/api/pm/planning/getreportsToMaster",{projectID:projectID,currentDate:currentDate}).subscribe((res) => {
      return resolve(res)
    }, (err) => {
      return reject(err)
    })
  })

  }
  getQuotePosition(id, resourceTypeList){
    
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/planning/getQuotePosition",{id, resourceTypeList}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  
    }
    getProjectFinnacialData(item_id){
      return new Promise((resolve, reject) => {
        this.http.post("/api/pm/planning/getProjectFinnacialData",{item_id:item_id}).subscribe((res) => {
          return resolve(res)
        }, (err) => {
          return reject(err)
        })
      })
    }
    getEmployeePosition(associate_id, date){
      return new Promise((resolve, reject) => {
        this.http.post("/api/pm/planning/getEmployeePosition",{associate_id: associate_id, currentDate: date}).subscribe((res) => {
          return resolve(res)
        }, (err) => {
          return reject(err)
        })
      })
    }


    getCustomerRateCard(customerId, date){
      return new Promise((resolve, reject) => {
        this.http.post("/api/pm/integration/getCustomerRateCard",{customer_id: customerId, date: date}).subscribe((res) => {
          return resolve(res)
        }, (err) => {
          return reject(err)
        })
      })
    }


    getQuoteUnitList(){
      return new Promise((resolve, reject) => {
        this.http.post("/api/pm/integration/getQuoteUnit",{}).subscribe((res) => {
          return resolve(res)
        }, (err) => {
          return reject(err)
        })
      })
    }


    getRateCardConfiguration(){
      return new Promise((resolve, reject) => {
        this.http.post("/api/qb/master/getRcFieldConfiguration",{isFromProject: true}).subscribe((res) => {
          return resolve(res)
        }, (err) => {
          return reject(err)
        })
      })
    }

    getQuoteListDetails(project_id,item_id, resourceTypeList){
      return new Promise((resolve, reject) => {
        this.http.post("/api/pm/planning/getProjectQuoteDetails",{project_id: project_id,item_id:item_id, resourceTypeList: resourceTypeList}).subscribe((res) => {
          return resolve(res)
        }, (err) => {
          return reject(err)
        })
      })
    }

    async getPositionAvailableHours(id: number): Promise<number> {
      const formConfig = await this.masterService.getPMFormCustomizeConfigV().catch(() => null);
    
      let billableActive = false;
      let identity = [1];
      let billable = [1];
    
      if (formConfig) {
        const billable_check = _.find(formConfig, { type: "project-team", field_name: 'check_billable', is_active: true });
    
        if (billable_check) {
          billableActive = true;
          identity = billable_check.identity || [1];
          billable = billable_check.billable || [1];
        }
      }
    
      let allocated_hrs = 0;
    
      for (const item of this.childData) {
        if (id && item['rate_card_id'] && item['rate_card_id'] === id) {
          if (billableActive) {
            if (billable.includes(item['billable']) || identity.includes(item['identity'])) {
              allocated_hrs += item['allocated_hours'] || 0;
            }
          } else {
            allocated_hrs += item['allocated_hours'] || 0;
          }
        }
      }
      return allocated_hrs;
    }
    
  
  calculateAllocatedHrs(startDate, endDate): number {
      const differenceMs = endDate.getTime() - startDate.getTime();
      const differenceDays = differenceMs / (1000 * 60 * 60 * 24);
      const totalDays = Math.floor(differenceDays) + 1;
  
      const totalWorkingHrs = totalDays * 8;
  
      return totalWorkingHrs; // Return the total working hours
  }

  async removeBulkMember(isa_list) {
    return new Promise((resolve, reject) => {
      this.http
        .post("/api/pm/planning/removeISABulk", {
          isa_list
        })
        .subscribe(res => {
          return resolve(res);
        });
    });
  }

  getRemainingHours(project_id,position_id,position_type){
    return new Promise((resolve, reject) => {
      this.http.post("/api/misFunctions/getRemainingBillableHours",{project_id: project_id,position_id:position_id,position_type:position_type}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return resolve(null)
      })
    })
  }

  getPlannedAllocatedAndRemainingBillableHours(project_id,position_id,position_type,start_date,end_date,utilization_percentage,associate_id,is_billable,isa_id,allocated_hours){
    return this.http.post("/api/misFunctions/getPlannedAllocatedAndRemainingBillableHours",{project_id,position_id,position_type,start_date,end_date,utilization_percentage,associate_id,is_billable,isa_id,allocated_hours});
  }

  getPlannedAllocatedHours(project_id,start_date,end_date,utilization_percentage,associate_id,isa_id=null){
    return this.http.post("/api/misFunctions/getPlannedAllocatedHours",{project_id,start_date,end_date,associate_id,isa_id});
  }

  async getQTCUserConfig(project_id,item_id) {
    return new Promise((resolve, reject) => {
      this.http
      .post("/api/pm/planning/getQTCUserConfig", {
        project_id: project_id,
        item_id: item_id
      })
      .subscribe(
        (res) => {
          if(res['messType'] == 'S'){
            resolve(res['data']);
          }
          else{
            resolve(null);
          }
        },
        (err) => {
          resolve(null);
        }
      );
    });
  }

  saveMemberQTCConfig(configData) {
    return this.http.post('/api/pm/planning/saveMemberQTCConfig', {
      config:configData
    });
  }

  async getEmployeeAllocatedHrs(start_date,end_date,associate_id) {
    return this.http.post('/api/pm/financial/getAllocatedHoursForEmployeeWithDates', {
      start_date: start_date,
      end_date: end_date,
      associate_id:associate_id
    });
  }

  setAllocationData(data:any){
    this.allocationData$.next(data);
  }

  setParentData(data:any){
    this.itemData$.next(data);
  }

  async getMilestoneEndDate(projectId,itemId) {
    return new Promise((resolve, reject)=>{
      this.http.post("/api/pm/planning/getMilestoneEndDate",{project_id:projectId,item_id:itemId}).subscribe((res)=>{
        resolve(res)
      },(err)=>{
        reject(err)
      })
    })
   
  
  }

  searchISAData(projectID,currentDate, searchColumn, searchParam){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/planning/searchISAData",{projectID:projectID,currentDate:currentDate,searchColumn: searchColumn, searchParam: searchParam}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }
  getE360EndDate(associate_id){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/planning/getEmployeeE360EndDate",{associate_id}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }

  async getPositionRemainingLogData(project_item_id,quote_position_id,start,limit) {
    return this.http.post('/api/pm/financial/getPositionRemainingLogData', {
      project_item_id: project_item_id,
      quote_position_id: quote_position_id,
      start:start,
      limit:limit
    });
  }

  async getISAPlannedHoursLog(isa_id) {
    return this.http.post('/api/misFunctions/getEmployeeMonthlyProjection', {
      isa_id: isa_id
    });
  }  

  async getWorkPremisisList() {
    return this.http.post('/api/pm/masterData/getWorkPremisisList', {});
  }

  async getTravelTypeList() {
    return this.http.post('/api/pm/masterData/getTravelTypeList', {});
  }

  async getWorkCityList() {
    return this.http.post('/api/pm/masterData/getWorkCityList', {});
  }


  async checkDayWizeEmployeeHours(start_date, end_date, percentage, associate_id, mode, isa_id,project_id,item_id){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/planning/checkDayWizeEmployeeHours",{start_date, end_date, percentage, associate_id, mode, isa_id,project_id,item_id}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }

  getSkillsMasterDetails(searchParams){
    return new Promise((resolve, reject) => {
      this.http.post("api/employee360/masterData/getAllSkillName",{searchParams}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }

  insertDemobilizeFeedBackForm(projectId,itemId,isaId,data){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/planning/insertDemobilizeFeedBackForm",{projectId,itemId,isaId,data}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }

  getDemobilizeFeedBackForm(projectId,itemId,isaId){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/planning/getDemobilizeFeedBackForm",{projectId,itemId,isaId}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }

  async getWorkShiftMasterList() {
    return this.http.post('/api/pm/masterData/getWorkShiftList', {});
  }

}
