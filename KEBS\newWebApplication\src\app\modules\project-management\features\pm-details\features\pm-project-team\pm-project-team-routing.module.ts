import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Routes, RouterModule } from '@angular/router';
import { PmProjectTeamPageComponent } from './components/pm-project-team-page/pm-project-team-page.component';
import { ProjectStructureComponentComponent } from './features/project-structure/component/project-structure-component/project-structure-component.component';
import { ProjectInternalStakeholderComponent } from './features/project-internal-stakeholders/component/project-internal-stakeholder/project-internal-stakeholder.component';
import{SkillMatrixComponent} from './features/pm-skill-matrix/component/skill-matrix/skill-matrix.component'
import { PmExternalStakeholderLandingComponent } from './features/pm-external-stakeholders/components/pm-external-stakeholder-landing/pm-external-stakeholder-landing.component'
import { PmBillingPlanVersionComponent } from './features/pm-billing-plan-version/pm-billing-plan-version.component';

const routes: Routes = [
  {
   path: '',
   component: PmProjectTeamPageComponent,
   children: [
    {
      path: "",
      redirectTo: "team",
      pathMatch: "full"
    },
    {
      path: "team",
      component: ProjectInternalStakeholderComponent
    },
    {
      path: "project-structure",
      component: ProjectStructureComponentComponent     
    },
    {
      path: "skill-matrix",
      component:SkillMatrixComponent

    },
    {
      path: "resource-request",
      loadChildren: () => import("src/app/modules/shared-lazy-loaded-components/team-members/team-members.module").then(m => m.TeamMembersModule),
      data: { tabId: 1, toggleHidden: true, width: 142, height: 312}
    },
    {
      path: "request-status",
      loadChildren: () => import("src/app/modules/shared-lazy-loaded-components/team-members/team-members.module").then(m => m.TeamMembersModule),
      data: { tabId: 2, toggleHidden: true, width: 142, height: 312}
    },
    {
      path: "external-stakeholders",
      component:PmExternalStakeholderLandingComponent

    },
    {
      path: "billing-plan",
      component: PmBillingPlanVersionComponent

    },
  ]
 }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class PmProjectTeamRoutingModule { }
