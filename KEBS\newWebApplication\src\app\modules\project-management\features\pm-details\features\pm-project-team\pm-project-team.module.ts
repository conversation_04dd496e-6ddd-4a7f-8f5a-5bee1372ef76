import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PmProjectTeamRoutingModule } from './pm-project-team-routing.module';
import { PmProjectTeamPageComponent } from './components/pm-project-team-page/pm-project-team-page.component';
import { SharedLazyLoadedModule } from '../../../../shared-lazy-loaded/shared-lazy-loaded.module';
import {MatButtonToggleModule} from '@angular/material/button-toggle';
import { MatIconModule } from '@angular/material/icon';
import {MatProgressBarModule} from '@angular/material/progress-bar';
import { MatChipsModule } from '@angular/material/chips'; // Import MatChipsModule
import { MatTooltipModule } from '@angular/material/tooltip';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { MatNativeDateModule } from '@angular/material/core';
// import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatStepperModule } from '@angular/material/stepper';
import { MatSelectModule } from '@angular/material/select';
import { NgxMatSelectSearchModule } from 'ngx-mat-select-search';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatOptionModule } from '@angular/material/core';
import { MatCardModule } from '@angular/material/card';
import {MatCheckboxModule} from '@angular/material/checkbox';
import {MatTabsModule} from '@angular/material/tabs';
import {ProjectInternalStakeholdersModule} from '../../features/pm-project-team/features/project-internal-stakeholders/project-internal-stakeholders.module'
import {ProjectStructureModule} from'../../features/pm-project-team/features/project-structure/project-structure.module'
import {PmSkillMatrixModule} from'../pm-project-team/features/pm-skill-matrix/pm-skill-matrix.module'
import {
  DxButtonModule,
  DxDataGridModule,
  DxHtmlEditorModule,
} from 'devextreme-angular';
import {TeamMembersModule} from '../../../../../shared-lazy-loaded-components/team-members/team-members.module'
import {PmExternalStakeholdersModule} from '../../features/pm-project-team/features/pm-external-stakeholders/pm-external-stakeholders.module';
import { PmBillingPlanVersionComponent } from './features/pm-billing-plan-version/pm-billing-plan-version.component'
import { PmResourceLoadingModule } from '../../../pm-resource-loading/pm-resource-loading.module';
@NgModule({
  declarations: [PmProjectTeamPageComponent, PmBillingPlanVersionComponent],
  imports: [
    CommonModule,
    PmProjectTeamRoutingModule,
    SharedLazyLoadedModule,
    MatButtonToggleModule,
    MatIconModule,
    MatProgressBarModule,
    MatChipsModule, 
    MatTooltipModule,
    MatStepperModule,
    MatInputModule,
    ReactiveFormsModule,
    FormsModule,
    MatDatepickerModule,
    MatSelectModule,
    NgxMatSelectSearchModule,
    MatNativeDateModule,  
    MatButtonModule,
    MatFormFieldModule,
    MatOptionModule,
    MatAutocompleteModule,
    MatCardModule,MatCheckboxModule,MatTabsModule,DxButtonModule,
    DxDataGridModule,
    DxHtmlEditorModule,
    ProjectInternalStakeholdersModule,
    PmProjectTeamRoutingModule,
    SharedLazyLoadedModule,
    ProjectStructureModule,
    TeamMembersModule,
    PmSkillMatrixModule,
    PmExternalStakeholdersModule,
    PmResourceLoadingModule
  ]
})
export class PmProjectTeamModule { }
