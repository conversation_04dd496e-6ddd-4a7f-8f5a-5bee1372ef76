import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class ProjectNameDisplayService {

  public projectName: any=""

  constructor(private http: HttpClient) { }


  getProjectName(projectId, itemId){
    return new Promise((resolve, reject)=>{
      this.http.post("/api/pm/planning/getProjectName",{projectId, itemId}).subscribe((res)=>{
        if(res['messType']=="S")
        {
            this.projectName= res['data']
        }
        return resolve(this.projectName)
      },(err)=>{
        return reject(err)
      })
    })
  }
}
