/**----------------------------------NEW Billing Advice CSS-----------------------------------**/
.billing-advice {
  background-color: #f1f3f8;
  height: 100%;

  ::ng-deep .mat-checkbox-checked.mat-accent .mat-checkbox-background {
    background-color: var(--adviceButton) !important; 
  }

  ::ng-deep .mat-checkbox-frame {
    border-color: #b9c0ca !important;
  }

  ::ng-deep .mat-checkbox-inner-container {
    width: 14px !important;
    height: 14px !important;
  }

  .checkbox {
    width: 14px;
    height: 14px;
    margin-right: 8px;
    margin-left: 8px;
  }

  .right-overflow-class {
    max-width: 99%;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    text-align: end;
    cursor: pointer;
  }
  .div-width-align {
    width: 100%;
    display: flex;
    position: relative;
    justify-content: end;
  }

  .approver-header{
    height: 68px;
    display: flex;
    align-items: center;
    margin-top: 5px;
    border: 1px solid rgba(0, 0, 0, .125);
    background-color: #FFFFFF;

    .back-action {
      display: flex;
      align-items: center;
      justify-content: center;
      color: #8b95a5;
      gap: 4px;
      border-radius: 4px;
      border: 1px solid #8b95a5;
      cursor: pointer;
      height: 24px;
      width: 24px;
      .back-icn-class {
        color: #8b95a5;
        font-size: 18px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    .approver-details{
      display: flex;
      flex-direction: column;
      width: 100%;
      padding-left: 5px;

      .details-header{
        font-family: var(--milestoneFont);
        font-size: 14px;
        font-weight: 400;
        color: #6E7B8F;
        padding: unset;
        display: flex;
        .col-2{
          padding: unset;
        }
        .custom-col {
          flex: 0 0 12%; 
          max-width: 12%; 
        }
        .description-col {
          flex: 0 0 25%; 
          max-width: 25%; 
        }

      }
      .details-value{
        font-family: var(--milestoneFont);
        font-size: 14px;
        font-weight: 500;
        color: #1B2140;
        padding: unset;
        display: flex;
        .col-2{
          padding: unset;
        }
        .custom-col {
          flex: 0 0 12%; 
          max-width: 12%; 
        }
        .text{
          padding-top: 5px;
          white-space: nowrap;
          text-overflow: ellipsis;
          width: 100%;
          overflow: hidden;
          cursor: pointer;
        }
        .actions{
          display: flex;
          margin-left: auto;
          gap: 10px;

          .approve{
            padding: 5px;
            background: #79BA44;
            color: white;
            border-radius: 5px;
            width: 72px;
            text-align: center;
            cursor: pointer;
          }

          .reject{
            padding: 5px;
            background: white;
            border-radius: 5px;
            color: #45546E;
            width: 72px;
            text-align: center;
            border: 1px solid #45546E;
            cursor: pointer;
          }
        }
      }
    }
  }
  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-top: 18px;
    .header-name-section {
      display: flex;
      gap: 0.7rem;
      align-items: center;
      .back-action {
        display: flex;
        align-items: center;
        justify-content: center;
        color: #8b95a5;
        gap: 4px;
        border-radius: 4px;
        border: 1px solid #8b95a5;
        cursor: pointer;
        height: 24px;
        width: 24px;
        .back-icn-class {
          color: #8b95a5;
          font-size: 18px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
      .header-title {
        font-family: var(--milestoneFont) !important;
        font-size: 16px;
        font-weight: 700;
        letter-spacing: 0.02em;
        max-width: 300px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        cursor: pointer;
      }
    }
    .header-action-section {
      display: flex;
      gap: 1.2rem;
      align-items: center;
      .order-value-class {
        display: flex;
        flex-direction: column;
        justify-content: right;
        .order-label {
          color: #6e7b8f;
          font-family: var(--milestoneFont) !important;
          font-size: 12px;
          font-weight: 400;
          line-height: 15.62px;
          letter-spacing: 0.02em;
        }
        .order-text {
          color: #272a47;
          font-family: var(--milestoneFont) !important;
          font-size: 14px;
          font-weight: 500;
          line-height: 18.23px;
          letter-spacing: 0.02em;
        }
      }
      .sync-class {
        .sync-icn {
          cursor: pointer;
          color: #1b2140;
          font-size: 20px;
        }
      }
      
      .save-btn-class {
        border: 1px solid #45546e;
        padding: 4px 8px;
        gap: 8px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        .save-text {
          color: #45546e;
          font-family: var(--milestoneFont) !important;
          font-size: 14px;
          font-weight: 700;
          line-height: 16px;
        }
      }
      .cancel-btn-class {
        border: 1px solid #45546e;
        padding: 4px 8px;
        gap: 8px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        .cancel-text {
          color: #45546e;
          font-family: var(--milestoneFont) !important;
          font-size: 14px;
          font-weight: 700;
          line-height: 16px;
        }
      }
      .partial-invoicing-btn-class {
        border: 1px solid #45546e;
        padding: 4px 8px;
        gap: 8px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        .partialInvoice-text {
          color: #45546e;
          font-family: var(--milestoneFont) !important;
          font-size: 14px;
          font-weight: 700;
          line-height: 16px;
        }
      }
      .reversal-btn-class {
        border: 1px solid #45546e;
        padding: 4px 8px;
        gap: 8px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        .reversal-text {
          color: #45546e;
          font-family: var(--milestoneFont) !important;
          font-size: 14px;
          font-weight: 700;
          line-height: 16px;
        }
      }
      .creditNote-btn-class {
        border: 1px solid #45546e;
        padding: 4px 8px;
        gap: 8px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        .creditNote-btn-class {
          color: #45546e;
          font-family: var(--milestoneFont) !important;
          font-size: 14px;
          font-weight: 700;
          line-height: 16px;
        }
      }
      .creditNote-btn-class.primary {
        background: linear-gradient(270deg, #ef4a61 0%, #f27a6c 105.29%);
        color: white;
        border: transparent;
      }
      .comments-class {
        cursor: pointer;
      }
      .attachment-class {
        cursor: pointer;
      }
      .version-history {
        cursor: pointer;
      }
      .download-billing {
        cursor: pointer;
      }
    }
  }
  .info-icon {
    font-size: 12px;
    margin-left: 5px;
    height: 10px;
    cursor: pointer;
    margin-top: 2px;
    color: grey;
    position: absolute;
  }
  .footer {
    position: absolute;
    bottom: 0;
    right: 0;
    left: 0;
    background: #fff;
    min-height: 4rem;
    max-height: max-content;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 10px;
    box-shadow: 0px 0px 4px 0px #00000080;
    gap: 18px;
  }
  .content-header-class {
    display: flex;
    flex-direction: row;
    height: auto;
    padding: 12px 14px;
    .content-text {
      display: flex !important;
      flex-direction: column;
      gap: 8px;
      min-width: 12%;
      flex-wrap: nowrap;
      align-items: flex-start;
      justify-content: left;
      margin: 0px;
      padding: 0px;
      height: 44px;
      &.end-align {
        margin-left: auto;
        align-items: flex-end;
      }
    }
    .sync-class {
      .sync-icn {
        cursor: pointer;
        color: #1b2140;
        font-size: 20px;
      }
    }
    .header-text {
      color: #6e7b8f;
      font-family: var(--milestoneFont) !important;
      font-size: 12px;
      font-weight: 400;
      line-height: 15.62px;
      letter-spacing: 0.02em;
    }
    .header-value {
      font-family: var(--milestoneFont) !important;
      font-size: 12px;
      font-weight: 500;
      line-height: 18.23px;
      letter-spacing: 0.02em;
      color: #272a47;
    }
  }
  ::ng-deep .mat-divider.mat-divider-vertical {
    margin-left: 8px !important;
    margin-top: 2px !important;
    margin-bottom: 2px !important;
    margin-right: 25px !important;
    border-right-width: 2px !important;
  }
  .body-table {
    height: var(--tableHeight) !important;
    width: 100%;
    position: relative;
    overflow: auto;
    margin-bottom: 2px;
  }
  .position-view {
    height: 100%;
  }
  .employee-view {
    height: 100%;
  }
  .header-table {
    position: sticky;
    top: 0;
    z-index: 100;
    .header-row {
      display: flex;
      flex-direction: row;
      flex-wrap: nowrap;
      width: max-content;
    }
    .header-col-class {
      display: flex;
      justify-content: center;
      align-items: center;
      min-width: 6rem;
      background-color: #45546e;
      color: #fff;
      border-right: 1px solid #ffff;
      border-bottom: 1px solid #ffff;
      flex-direction: column;
      padding: 5px 0px 5px 0px;
      font-family: var(--milestoneFont) !important;
      font-size: 12px;
      font-weight: 500;
      line-height: 15.62px;
      letter-spacing: 0.02em;
      &.expand-class {
        @extend .expand-class;
      }
      &.checkbox-class {
        @extend .checkbox-class;
      }
      &.name-class {
        @extend .name-class;
      }
      &.emp-class {
        @extend .emp-class;
      }
      &.enable-class {
        @extend .enable-class;
      }
      &.quote-position-class {
        @extend .quote-position-class;
      }
      &.billable-class {
        @extend .billable-class;
      }
      &.date-class {
        @extend .date-class;
      }
      &.hour-class {
        @extend .hour-class;
      }
      &.val-class {
        @extend .val-class;
      }
      &.line-comments-class {
        @extend .line-comments-class;
      }
    }

    .header-col-class-span {
      display: flex;
      justify-content: center;
      align-items: center;
      min-width: 4rem;
      background-color: #45546e;
      color: #fff;
      border: none;
      flex-direction: column;
      font-family: var(--milestoneFont) !important;
      font-size: 12px;
      font-weight: 500;
      line-height: 15.62px;
      letter-spacing: 0.02em;
    }
    .sub-header {
      display: flex;
      flex-direction: row;
      align-items: center;
      width: 100%;
      border-bottom: 1px solid #ffff;
      border-top: 2px solid #fff;
    }
    .header-main {
      width: 100%;
      justify-content: center;
      align-items: center;
      display: flex;
      border-right: 1px solid #ffff !important;
      padding: 4px 0px 4px 0px;
    }
    .sub-header-col {
      width: 6rem;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 3px 0px 3px 0px;
      border-right: 1px solid #fff;
      background: #6e7b8f;

      &.bill-class{
        width: 10rem;
      }
    }
  }
  .content-table {
    .value-row {
      display: flex;
      flex-direction: row;
      flex-wrap: nowrap;
      width: max-content;
      min-height: 35px;
      background-color: #f6f6f6;
    }
    .value-row-child {
      display: flex;
      flex-direction: row;
      flex-wrap: nowrap;
      width: max-content;
      background-color: #ecedf2;
      min-height: 35px;
    }
    .value-col-class {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      min-width: 6rem;
      max-width: 6rem;
      color: #526179;
      border-right: 1px solid #fff;
      border-bottom: 1px solid #fff;
      font-family: var(--milestoneFont) !important;
      font-size: 12px;
      font-weight: 400;
      line-height: 15.62px;
      letter-spacing: 0.02em;
      padding: 4px 5px 4px 0px;

      &.bill-class{
        min-width: 10rem;
      }

      &.checkbox-class {
        @extend .checkbox-class;
      }
      &.expand-class {
        @extend .expand-class;
      }
      &.name-class {
        @extend .name-class;
      }
      &.emp-class {
        @extend .emp-class;
      }
      &.enable-class {
        @extend .enable-class;
      }
      &.quote-position-class {
        @extend .quote-position-class;
      }
      &.billable-class {
        @extend .billable-class;
      }
      &.date-class {
        @extend .date-class;
      }
      &.hour-class {
        @extend .hour-class;
      }
      &.val-class {
        @extend .val-class;
      }
      &.line-comments-class {
        @extend .line-comments-class;
      }
    }
    .value-col-class input {
      width: 95%;
      padding: 4px;
      margin: 0px 0px 0px 3px;
      box-sizing: border-box;
      border: 1px solid #80808045 !important;
    }
    .value-col-class .disabled {
      background: #d9d9d9;
      color: #7d838b;
    }
  }
  .expand-icon-class {
    color: #1c1b1f;
    font-size: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }
  .overflow-class {
    // width: 99%;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    // min-width: 5rem;
    // max-width: 6rem;
    cursor: pointer;
    &.center {
      text-align: center;
    }
    &.left {
      padding-left: 10px;
      text-align: left;
    }
    &.right {
      padding-left: 10px;
      text-align: end;
    }
    &.checkbox-class {
      @extend .checkbox-class;
    }
    &.expand-class {
      @extend .expand-class;
    }
    &.name-class {
      @extend .name-class;
    }
    &.emp-class {
      @extend .emp-class;
    }
    &.enable-class {
      @extend .enable-class;
    }
    &.quote-position-class {
      @extend .quote-position-class;
    }
    &.billable-class {
      @extend .billable-class;
    }
    &.date-class {
      @extend .date-class;
    }
    &.hour-class {
      @extend .hour-class;
    }
    &.val-class {
      @extend .val-class;
    }
    &.line-comments-class {
      @extend .line-comments-class;
    }
  }
  .status-class {
    width: max-content;
    height: 20px;
    padding: 2px 8px 2px 8px;
    border-radius: 16px;
    border: 1px solid #ff3a46;
    gap: 4px;
    background: linear-gradient(0deg, #ffebec, #ffebec),
      linear-gradient(0deg, #ff3a46, #ff3a46);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .text-status {
    height: 14px;
    font-family: var(--milestoneFont) !important;
    font-size: 12px;
    font-weight: 400;
    line-height: 14px;
    letter-spacing: 0.02em;
    text-align: center;
    color: #ff3a46;
    width: max-content;
  }
  .gif-class {
    width: 20px;
    height: 20px;
  }
  .footer-btns-class {
    display: flex;
    flex-direction: row;
    justify-content: end;
    gap: 20px;
  }
  .button-class {
    display: flex;
    justify-content: center;
    align-items: center;
    width: max-content;
    padding: 10px 12px;
    gap: 4px;
    border-radius: 8px;
    cursor: pointer;
  }
  .button-class.secondary {
    border: 1px solid #45546e;
  }
  .button-class.primary {
    background: linear-gradient(270deg, #ef4a61 0%, #f27a6c 105.29%);
  }
  .button-text {
    font-family: var(--milestoneFont) !important;
    font-size: 14px;
    font-weight: 700;
    line-height: 16px;
    letter-spacing: -0.02em;
    border: none;
  }
  .button-text.secondary {
    color: #45546e;
  }
  .button-text.primary {
    color: #ffffff;
  }
  .checkbox-class {
    min-width: 2.5rem !important;
    max-width: 2.5rem !important;
  }
  .expand-class {
    min-width: 2rem !important;
    max-width: 2rem !important;
  }

  // Expand/Collapse All Button Styling
  .expand-collapse-all-container {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
  }

  .toggle-all-btn {
    background: transparent;
    border: none;
    color: #fff;
    border-radius: 4px;
    padding: 4px 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 28px;
    height: 24px;
    font-size: 10px;
    transition: all 0.2s ease;

    &:hover:not(:disabled) {
      background: rgba(255, 255, 255, 0.1);
      transform: scale(1.05);
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      color: rgba(255, 255, 255, 0.5);
    }

    ::ng-deep mat-icon {
      font-size: 16px !important;
      width: 16px !important;
      height: 16px !important;
    }
  }
  .name-class {
    min-width: 13rem;
    max-width: 13rem !important;
  }
  .emp-class {
    min-width: 5rem;
    max-width: 5rem !important;
  }
  .enable-class {
    max-width: 5.5rem !important;
    min-width: 5.5rem !important;
    justify-content: center !important;
  }
  .quote-position-class{
    max-width: 6rem !important;
    min-width: 6rem !important;
 
  }
  .billable-class {
    max-width: 3.4rem !important;
    min-width: 3.4rem !important;
    justify-content: center !important;
    
  }
  .date-class {
    max-width: 6rem !important;
    min-width: 6rem !important;
  }
  .hour-class {
    max-width: 6rem !important;
    min-width: 6rem;
  }
  .val-class {
    max-width: 6rem !important;
    min-width: 6rem;
  }
  .line-comments-class {
    max-width: 5rem !important;
    min-width: 5rem !important;
    justify-content: center !important;
    .comment-btn-class {
      border: 1px solid #45546e;
      padding: 4px 8px;
      gap: 8px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      .comment-text {
        color: #45546e;
        font-family: var(--milestoneFont) !important;
        font-size: 11px;
        font-weight: 700;
        line-height: 16px;
      }
    }
  }
  .content-header-class-invoice {
    display: flex;
    flex-direction: row;
    height: auto;
    padding: 12px 14px;
    .content-text-invoice {
      display: flex !important;
      flex-direction: column;
      gap: 8px;
      min-width: 12%;
      flex-wrap: nowrap;
      align-items: flex-start;
      justify-content: left;
      margin: 0px;
      padding: 0px;
      height: 44px;
    }
    .content-text-button-class{
      display: flex !important;
      align-items: center;
      justify-content: center;
      margin-left: auto;
    }
    .save-invoice-btn{
      gap: 4px;
      display: flex;
      border: 1px solid #45546e;
      width: fit-content;
      min-width: auto;
      padding: 2px 6px;
      height: 28px !important;
      border-radius: 6px;
      align-items: center;
      cursor: pointer;
    }
    .header-text-invoice {
      color: #6e7b8f;
      font-family: var(--milestoneFont) !important;
      font-size: 12px;
      font-weight: 400;
      line-height: 15.62px;
      letter-spacing: 0.02em;
    }

    .header-value-invoice {
      font-family: var(--milestoneFont) !important;
      font-size: 12px;
      font-weight: 500;
      line-height: 18.23px;
      letter-spacing: 0.02em;
      color: #272a47;
    }

    .header-value-invoice.date-range-class {
      display: flex;
      cursor: pointer;
      flex-direction: row;
      gap: 4px;
      align-items: center;
    }
  }

  .footer-row {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    width: max-content;
    background-color: #feedf0;
    position: sticky;
    z-index: 10;
    bottom: 0;
  }
  .header-table.scroll-content {
    overflow: auto;
    height: 99%;
  }
  .header-row-invoice {
    position: sticky;
    z-index: 10;
    top: 0;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    width: max-content;
  }
  .stepper-class {
    min-height: 50px;
  }
  .stepper-row {
    display: flex;
    flex-direction: row;
    flex: 1;
    padding: 6px 24px 6px 24px;
    box-shadow: none;
  }
  .step-line {
    display: flex;
    flex-direction: row;
    align-items: center;
  }

  .divider-class {
    width: 72px;
    margin: 0px 10px 0px 10px;
    border-top-width: 2px;
  }
  .divider-class.disabled {
    border-color: #b9c0ca;
  }
  .divider-class.completed {
    border-color: #111434;
  }
  .divider-class.selected {
    border-color: #111434;
  }
  .flex-class-card {
    flex: 1;
    box-shadow: none;
  }
  .invoice-grid {
    height: 100%;
  }
  .section-label-class {
    display: flex;
    flex-direction: row;
    gap: 8px;
    align-items: center;
    cursor: pointer;
  }
  .outer-circle-class {
    border-radius: 50%;
    border: none;
    font-family: var(--milestoneFont);
    font-size: 12px;
    font-weight: 500;
    letter-spacing: 0.02em;
    text-align: center;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .outer-circle-class.disabled {
    background-color: #d9d9d9;
    color: #7d838b;
  }

  .outer-circle-class.completed {
    background-color: #79ba47;
    color: #ffffff;
  }

  .outer-circle-class.selected {
    background-color: var(--adviceButton);
    color: #ffffff;
  }

  .section-text {
    font-family: var(--adviceButton);
    font-size: 14px;
    font-weight: 500;
    letter-spacing: 0.02em;
    text-align: center;
  }

  .section-text.disabled {
    color: #7d838b;
  }

  .section-text.selected {
    color: var(--adviceButton);
  }

  .section-text.completed {
    color: #111434;
    background: none;
  }
  .arrow-btn-class {
    border-radius: 8px;
    border: 1px solid #45546e;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    padding: 2px;

    .arrow-btn-icon {
      font-size: 18px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #45546e;
    }
  }
  .header-col-class-invoice {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #e8e9ee;
    color: #6e7b8f;
    border-right: 1px solid #ffff;
    border-bottom: 1px solid #ffff;
    flex-direction: column;
    padding: 5px 0px 5px 0px;
    font-family: var(--milestoneFont) !important;
    font-size: 12px;
    font-weight: 500;
    min-height: 35px;
    letter-spacing: 0.02em;
    background-color: #45546e;
    color: #fff;
  }
  .value-row-invoice {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    width: max-content;
    min-height: 35px;
  }

  .value-col-class-invoice {
    display: flex;
    justify-content: center;
    align-items: center;
    color: #526179;
    border-right: 1px solid #dadce2;
    border-bottom: 1px solid #dadce2;
    font-family: var(--milestoneFont) !important;
    font-size: 12px;
    font-weight: 400;
    line-height: 15.62px;
    letter-spacing: 0.02em;
    padding: 4px 0px 4px 0px;
  }

  .footer-col-class-invoice {
    display: flex;
    justify-content: center;
    align-items: end;
    color: #7d838b;
    flex-direction: column;
    padding: 5px 4px 5px 0px;
    font-family: var(--milestoneFont) !important;
    font-size: 12px;
    font-weight: 500;
    line-height: 15.62px;
    letter-spacing: 0.02em;
    min-height: 36px;
  }

  .footer-col-class-invoice.visible {
    border-right: 1px solid #fff;
    border-left: 1px solid #fff;
  }

  .total-value {
    display: flex;
    flex-direction: row;
    justify-content: end;
    align-items: left;
    gap: 10px;
    width: 100%;
  }

  .invoice-value-grid {
    display: flex;
    align-items: center;
    justify-content: right;
    margin: 0px 2px 0px 2px;
    height: 100%;

    &.action {
      justify-content: space-evenly !important;
    }
  }
  .text-grid {
    width: 100%;
    padding: 0px 4px 0px 4px;
    border: 1px solid #80808045;
    height: 3rem;
  }

  .text-grid.dropdown {
    border: none !important;
  }
  .text-bold-class-align {
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 70%;
    position: relative;
    white-space: nowrap;
    cursor: pointer;
    color: #272a47 !important;
    font-weight: 600 !important;
  }

  .date_class {
    display: flex;
    flex-direction: row;
    gap: 6px;
    cursor: pointer;
  }
  .complete-icon-class {
    font-size: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .text-grid.dropdown.disabled {
    color: #8b95a5;
    background: #e8e9ee;
  }

  .no-data-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 12px;
    height: var(--tableHeight);
  }
  .nodata-text {
    color: #7d838b;
    font-family: var(--milestoneFont) !important;
    font-size: 14px;
    font-weight: 500;
    line-height: 24px;
    letter-spacing: -0.02em;
    text-align: left;
  }
  .hidden-input {
    position: absolute;
    width: 0;
    height: 0;
    border: none;
    padding: 0;
    margin: 0;
    opacity: 0;
    pointer-events: none;
  }
  .icon-med-class {
    font-size: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--adviceButton);
  }

  .footer-row::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #feedf0;
    z-index: -1;
    // opacity: 0.7;
  }
  .loader-container {
    display: flex;
    justify-content: center;
    align-items: center;
    // height: 100vh;

    &.invoice {
      height: 100% !important;
      min-height: var(--tableHeight);
    }
  }

  .loader-container ::ng-deep .green-spinner circle {
    stroke: var(--adviceButton) !important;
  }
  .number-edit-class {
    width: 100%;
    padding: 0px 4px 0px 4px;
    border: 1px solid #80808045;
    height: 1.6rem;
    border-radius: 3px;
  }
}
::ng-deep .employee-billing-slide-toggle .mat-slide-toggle-bar {
  position: relative;
  width: 28px !important;
  height: 16.8px !important;
  flex-shrink: 0;
  border-radius: 8px;
}
::ng-deep .employee-billing-slide-toggle .mat-slide-toggle-thumb {
  height: 11px !important;
  width: 11px !important;
  border-radius: 50%;
  margin-top: 5.5px !important;
  margin-left: 3px !important;
}
::ng-deep
  .billing-advice
  .mat-slide-toggle.mat-checked
  .mat-slide-toggle-thumb {
  background: #ffff !important;
}
::ng-deep .billing-advice .mat-slide-toggle.mat-checked .mat-slide-toggle-bar {
  background: var(--adviceButton) !important;
}
::ng-deep
  .billing-advice
  .mat-slide-toggle.mat-checked
  .mat-slide-toggle-thumb-container {
  transform: translate3d(10.5px, 0, 0) !important;
}

.loader-class{
  height: var(--detailsHeight) !important;
  .loader-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%; 
    background-color: whitesmoke!important;
  }
  
  .loader-container ::ng-deep .green-spinner circle {
    stroke: var(--adviceButton) !important;
  }
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }  
  .loader-container ::ng-deep .green-spinner circle {
    stroke: var(--adviceButton) !important;
  }
}