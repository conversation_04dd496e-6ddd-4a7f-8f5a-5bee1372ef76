<div class="Walkthrough">
    <div class="row mt-2 pr-2 d-flex">
        <div class="col-2 pl-3 m-0 d-flex" *ngIf="videoScreen">
            <div class="close-icon"><mat-icon (click)="return()" style="font-size: 18px;">keyboard_arrow_left</mat-icon>
            </div>
        </div>
        <div class="col p-0 m-0 d-flex justify-content-end">
            <div class="close-icon"><mat-icon (click)="closeDialog(null)" style="font-size: 18px;">clear</mat-icon>
            </div>
        </div>
    </div>
    <div class="loader-container" *ngIf="isComponentLoading">
        <mat-spinner class="green-spinner" diameter="30"></mat-spinner>
    </div>
    <div class="row mt-0 p-0 d-flex" *ngIf="!isComponentLoading && dataList.length > 0 && !videoScreen;">
        <div class="col-6 p-0 m-0" style="display: flex;place-content: center;">
            <ng-container>
                <img class="img-class" [@slideDown] [src]="getImgLink()" />
            </ng-container>

        </div>
        <div class="col-6 pt-2 m-0">
            <span class="header-class" [matTooltip]="headerLabel ? headerLabel : '-'">{{headerLabel ? headerLabel : '-'}}</span>
            <!-----------------For API Content---------------->
            <div class="row d-block content-class mt-2 mb-3">
                <div *ngFor="let data of dataList">
                    <div class="row d-block mt-2 p-3"
                        [ngClass]="{'card-class':!(data.expand), 'card-class-selected':(data.expand)}"
                        [@expandCollapse]="data.expand ? 'expanded' : 'collapsed'">
                        <div class="row mb-2 d-flex"  (click)="collapseSection(data.id)">
                            <div class="col-11 p-0 m-0">
                                <span class="data-class" [matTooltip]="data.header ? data.header : '-'">{{data.header ? data.header : '-'}}</span>
                            </div>
                            <div class="col-1 d-flex p-0 m-0 justify-content-end">
                                <mat-icon class="icon-button"
                                    *ngIf="!data.expand">keyboard_arrow_down</mat-icon>
                                <mat-icon class="icon-button"
                                    *ngIf="data.expand">keyboard_arrow_up</mat-icon>
                            </div>
                        </div>
                        <div class="row" style="position: relative;" *ngIf="(data.expand)" [@slideInFromBottom]>
                            <span class="sub-header-txt">
                                <p [innerHtml]="data.description | safeHtml"></p>
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <button class="button-layout-selected" style="margin-right: 21px;" (click)="openVideoManual()" *ngIf="false"><mat-icon
                        class="video-icon">smart_display</mat-icon> View Explainer</button>
                <button class="button-layout-not-selected" (click)="openQuickTour()" *ngIf="isQuickTourEnable">Walkthrough</button>
            </div>
            <div class="row d-flex mt-3">
                <div class="col-6 p-0" *ngIf="false">
                    <span class="link-text" style="margin-right: 20px;">KEBS Help</span>
                    <span class="link-text">FAQ's</span>
                </div>
                <div class="col-6 d-flex justify-content-end" *ngIf="false">
                    <span class="link-text"><EMAIL></span>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-0 p-0 d-flex" *ngIf="!isComponentLoading && dataList.length > 0 && videoScreen;">
        <div *ngIf="videoUrl != null" style="margin: 9px;">
            <iframe height="480px" width="982px" [src]="getYouTubeEmbedUrl(videoUrl)" allowfullscreen>
            </iframe>
        </div>
        <div class="row mt-0 p-0 vid-no-data" *ngIf="videoUrl == null">
            <div class="image-class">
                <img style="height: 200px; width: 250px"
                    [src]="noDataImage ? noDataImage : 'https://assets.kebs.app/images/no_data_found.png'" />
            </div>
            <span class="no-img-text">No Video Available !</span>
        </div>
    </div>
    <div class="row mt-0 p-0 no-data" *ngIf="!isComponentLoading && dataList.length == 0">
        <div class="image-class">
            <img style="height: 200px; width: 250px"
                [src]="noDataImage ? noDataImage : 'https://assets.kebs.app/images/no_data_found.png'" />
        </div>
        <span class="no-img-text">No Updates Available !</span>
    </div>
</div>

