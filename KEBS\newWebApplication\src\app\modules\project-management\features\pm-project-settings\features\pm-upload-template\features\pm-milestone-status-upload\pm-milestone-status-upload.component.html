<div class="container-fluid milestone-upload-component">
    <div class="d-flex">
        <div class="title-name mr-auto">
            Upload Milestone Status
        </div>
    </div>
    <div class="row">
        <div class="col-12 pl-0 pr-0">
          <mat-card  class="generalcard mx-2 mb-2 mx-auto" style=" width: 98%;height:1000px;">
              <mat-horizontal-stepper #stepper1>
     <mat-step>
      <div class="d-flex justify-content-center align-items-center ng-star-inserted">
     <ng-template matStepLabel>Download Template</ng-template>
            <mat-card class="d-flex settings-card  p-3" (click)="downloadTemplate()">
              <span class="title">Download Template</span>
            </mat-card>
         </div>
        <div class="d-flex justify-content-center align-items-center ng-star-inserted row mt-5">
            <button mat-raised-button class="iconbtnSave justify-content-center upload-btn" matTooltip="Next" type="submit" (click)="onNextPressed();">
              <span>Next</span>
            </button>
          </div>
    </mat-step>
    <mat-step>
        <ng-template matStepLabel>Upload Template</ng-template>
        <mat-horizontal-stepper #stepper2>
          <mat-step>
            <ng-template matStepLabel>Upload File</ng-template>
              <div>
                <div class="row mt-5">
                  <div class="col-12 txt-bold">Choose File</div>
                </div>
                <div class="row mt-3">
                  <div class="col-12 d-flex">
                    <div
                      class="drop-container py-3 px-2 d-flex flex-column"
                      appDropzone
                      (fileDropped)="uploadTemplate($event)"
                    >
                      <span class="zone-text">Drag and drop file here</span>
                      <span class="zone-text">or</span>
                      <button
                        mat-raised-button
                        class="mt-2 mx-auto upload-btn"
                        (click)="fileInput.click()"
                      >
                        Choose File
                      </button>
                    </div>
              
                    <input hidden type="file" #fileInput (change)="uploadTemplate($event)" />
                  </div>
            
                </div>
              </div>
          </mat-step>
    
          <mat-step>
            <ng-template matStepLabel>Validate File</ng-template>
    
            <div class="d-flex justify-content-center align-items-center" *ngIf="!downloadValidateTemplate">
              <button mat-raised-button class="complete upload-btn" (click)="validateTemplate()" [disabled]="validationLoading">
                Validate Template
              </button>
            </div>
    
            <div class="row pt-2" style = "display: grid;justify-content: center;">
              <div *ngIf="validationLoading" class="col-12 justify-content-center">
                <mat-spinner matTooltip="Please wait..." diameter="30"> </mat-spinner>
              </div>
            </div>
            
            <div class="d-flex justify-content-center align-items-center" *ngIf="downloadValidateTemplate">
              <button mat-raised-button class="complete upload-btn" (click)="downloadValidateTemplateFILE()" >
                Download Template
              </button>
            </div>
    
     
            <div class="d-flex justify-content-center align-items-center pt-3" *ngIf="errorFlag">
              {{errorMessageData}}
            </div>
    
            <div class="d-flex justify-content-center align-items-center pt-3" *ngIf="issuePresent">
                Kindly check for errors in template before proceeding!
            </div>
    
            <div class="row" *ngIf="!mandatoryIssuePresent && !issuePresent">
              <div class="col-3 ml-auto d-flex">
                  <button
                      mat-icon-button
                      class="iconbtn ml-2 mt-1 mb-1 ml-auto upload-btn"
                      matTooltip="Next"
                      type="submit"
                      (click)="clickToMigrateFile()"
                  >
                      <mat-icon> chevron_right</mat-icon>
                  </button>
              </div>
          </div>
          </mat-step>
    
    
          <mat-step *ngIf="!mandatoryIssuePresent !issuePresent">
            <ng-template matStepLabel>Migrate File</ng-template>
    
            <div class="d-flex justify-content-center align-items-center" *ngIf="!migrationLoading && (onceuploadcheck)">
              <button mat-raised-button class="complete upload-btn" (click)="uploadTemplateMain()" >
                Migrate Data
              </button>
    
              <div *ngIf="migrationLoading" class="row justify-content-center">
                <mat-spinner matTooltip="Please wait..." diameter="30"> </mat-spinner>
              </div>
            </div>
            
          </mat-step>
      </mat-horizontal-stepper>
        </mat-step>
    </mat-horizontal-stepper>
    </mat-card>
    </div>
    </div>
    </div>
    
    

