import { Component, ViewChild,Input, OnInit } from '@angular/core';
import { ProjectHeaderDmcService } from 'src/app/modules/projects/features/project-list/components/project-header-dmc/services/project-header-dmc.service';
import * as _ from 'underscore';
import { STEPPER_GLOBAL_OPTIONS } from '@angular/cdk/stepper';
import * as moment from "moment";
import { Router, ActivatedRoute } from '@angular/router';
import { FormBuilder, FormGroup, Validators, FormArray } from '@angular/forms';
import { JsonToExcelService } from 'src/app/services/excel/json-to-excel.service';
import { UtilityService } from 'src/app/services/utility/utility.service';
import * as XLSX from "xlsx";
import {MatDialog} from "@angular/material/dialog";
import { LoginService } from 'src/app/services/login/login.service';
import { MatStepper } from '@angular/material/stepper';
import { takeUntil } from "rxjs/operators";
import { Subject } from "rxjs";
import {PmMasterService} from 'src/app/modules/project-management/services/pm-master.service'
import { v4 as uuidv4 } from 'uuid';
import { tickStep } from 'd3';


@Component({
  selector: 'app-pm-milestone-status-upload',
  templateUrl: './pm-milestone-status-upload.component.html',
  styleUrls: ['./pm-milestone-status-upload.component.scss'],
})
export class PmMilestoneStatusUploadComponent implements OnInit {
  @ViewChild('stepper1') stepper1: MatStepper;
  @ViewChild('stepper2') stepper2: MatStepper;

  showDownloadTemplate: boolean = false;
  showUploadTemplate: boolean = false;
  stepNo: any = 0;
  isLinear = false;
  loading: boolean = true;
  uploaded_data: any;
  validationLoading: boolean = false;
  migrationLoading: boolean = false;
  errorFlag: boolean = false;
  errorMessageData: string = '';
  validationId: string = '';
  result_output_data: any = [];
  downloadedTemplate: any = [];
  issuePresent: boolean = false;
  onceuploadcheck: boolean=true;
  mandatoryIssuePresent: boolean = true;
  downloadValidateTemplate: boolean=false;
  formConfig: any = [];
  button: any;
  fontStyle: any;
  protected _onDestroy = new Subject<void>();

  constructor(
    private jsonToExcel: JsonToExcelService,
    private formBuilder: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private pmMasterService: PmMasterService,
    private loginService: LoginService,
    private utilityService: UtilityService,
    private dialog: MatDialog
  ) {}

  async ngOnInit() {
    this.loading = true;
    await this.pmMasterService.getPMFormCustomizeConfigV().then((res: any) => {
          if (res) {
            this.formConfig = res;
          }
        });
        const retrieveStyles2 = _.where(this.formConfig, { type: "project-theme", field_name: "styles", is_active: true });
        if(retrieveStyles2.length > 0){
          this.button = retrieveStyles2.length > 0 ? retrieveStyles2[0].data.button_color ? retrieveStyles2[0].data.button_color : "#90ee90" : "#90ee90";
          document.documentElement.style.setProperty('--milestoneSettingButton', this.button)
          //this.noDataImage1 = retrieveStyles2[0].data.no_data_image ? retrieveStyles2[0].data.no_data_image : "https://assets.kebs.app/No-milestone-image.png";
          this.fontStyle = retrieveStyles2.length > 0 ? retrieveStyles2[0].data.font_style ? retrieveStyles2[0].data.font_style : "Roboto" : "Roboto";
          document.documentElement.style.setProperty('--milestoneSettingFont', this.fontStyle);
        }
        this.loading=false

  }

  // Download Template with Milestone ID and Status ID
  async downloadTemplate() {
    this.showDownloadTemplate = true;
    this.showUploadTemplate = false;
  
    // Data for the first sheet (Main Data)
    let mainData = [{ 'Milestone ID': 1000, 'Status ID': 1 }];
  
    // Data for the second sheet (Master Data)
    let masterData = [
      { 'Milestone ID': 4, 'Status': 'Execution' },
      { 'Milestone ID': 7, 'Status': 'Open' },
      { 'Milestone ID': 8, 'Status': 'YTB' },
      { 'Milestone ID': 9, 'Status': 'Billed' },
      { 'Milestone ID': 10, 'Status': 'Partial Payment' },
      { 'Milestone ID': 11, 'Status': 'Payment Received' }
    ];
  
    // Create a new workbook and add sheets
    const workbook: XLSX.WorkBook = XLSX.utils.book_new();
    const mainSheet: XLSX.WorkSheet = XLSX.utils.json_to_sheet(mainData);
    const masterSheet: XLSX.WorkSheet = XLSX.utils.json_to_sheet(masterData);
  
    XLSX.utils.book_append_sheet(workbook, mainSheet, 'Milestone Data');
    XLSX.utils.book_append_sheet(workbook, masterSheet, 'Master Data');
  
    // Write and trigger download
    XLSX.writeFile(workbook, `Milestone_Status_Upload_Template_${moment().format('DD-MMM-YYYY')}.xlsx`);
  }
  // Validate Uploaded Template
  async validateTemplate() {
    if (this.uploaded_data) {
      if (this.uploaded_data.length > 0) {
        this.validationLoading = true;

        // Validate Column Names
        const columnNamesValid = this.validateColumnNames(this.uploaded_data[0]);
        if (!columnNamesValid) {
          this.validationLoading = false;
          this.errorFlag = true;
          this.utilityService.showMessage(this.errorMessageData, 'Dismiss', 3000);
          return;
        }
        const dataTypesValid = this.validateDataTypes(this.uploaded_data);
      if (!dataTypesValid) {
        this.validationLoading = false;
        this.errorFlag = true;
        this.utilityService.showMessage(this.errorMessageData, 'Dismiss', 3000);
        return;
      }

        // Validate Milestone IDs
        const milestoneIds = this.uploaded_data.map((data) => data['Milestone ID']);
        this.pmMasterService.validateMilestoneIds(this.uploaded_data).then((res)=>{
          this.validationLoading = false;
            if(res['messType']=="S")
            { 
                this.downloadValidateTemplate = true
                this.downloadedTemplate = res['uploaded_data']
                this.result_output_data = res['result_data']
                this.issuePresent = res['issue_present']
                this.mandatoryIssuePresent = res['mandatory_issue_present']

            }
            else
            {
              this.errorFlag= true;
              this.errorMessageData = res['message']
              this.utilityService.showMessage(res['message'],"Dismiss",3000)
            }
        })
        this.validationLoading = false;
        this.downloadedTemplate = this.uploaded_data;
        this.downloadValidateTemplate = true;
        this.issuePresent = false; // No issues if validation passes
        this.mandatoryIssuePresent = false;
      } else {
        this.utilityService.showMessage('No rows are present in the template!', 'Dismiss', 3000);
      }
    } else {
      this.utilityService.showMessage('Kindly upload a template!', 'Dismiss', 3000);
    }
  }

  // Validate Column Names
  validateColumnNames(data: any): boolean {
    if (data['Milestone ID'] === undefined) {
      this.errorMessageData = 'Milestone ID column not found.';
      return false;
    } else if (data['Status ID'] === undefined) {
      this.errorMessageData = 'Status ID column not found.';
      return false;
    }
    return true;
  }
  validateDataTypes(data: any[]): boolean {
    for (const row of data) {
      if (
        !Number.isInteger(row['Milestone ID']) ||
        !Number.isInteger(row['Status ID'])
      ) {
        this.errorMessageData = 'Milestone ID and Status ID must be integers.';
        return false;
      }
    }
    return true;
  }
  downloadValidateTemplateFILE(){
      let fileName = 'Milestone Status Validated Upload Template - (' + moment().format('DD-MMM-YYYY') + ')'
  
      this.jsonToExcel.exportAsExcelFile(this.downloadedTemplate, fileName)
      // this.stepper2.next()
    }


    async uploadTemplate(event){
      console.log(event)
  
      let upload  = await this.excelToJson(event)
  
      let data = upload['data']
      let column = upload['columns']
      this.uploaded_data = await this.formatUploadedData(column, data)
      console.log(this.uploaded_data)
      this.stepper2.next()
      
    }
    formatUploadedData(column, data)
  {
      for(let upload of data)
      {
          for(let col of column)
          {
              if(upload.hasOwnProperty(col)){
                upload[col] = upload[col] ? upload[col] :""
              }
              else
              {
                upload[col]=""
              }
          }
      }

      return data;
  }

  

  // Convert Excel to JSON
  excelToJson(evt: any): Promise<any> {
    return new Promise((resolve, reject) => {
      const target: DataTransfer = <DataTransfer>evt.target;
      const reader: FileReader = new FileReader();

      reader.onload = (e: any) => {
        const bstr: string = e.target.result;
        const wb: XLSX.WorkBook = XLSX.read(bstr, { type: 'binary' });
        const wsname: string = wb.SheetNames[0];
        const ws: XLSX.WorkSheet = wb.Sheets[wsname];
        const data = XLSX.utils.sheet_to_json(ws, { header: 1 });

        let columns: any = [];
        let jsonData: any = [];

        _.each(data, (row, index) => {
          if (index === 0) {
            columns = row;
          } else {
            let obj: any = {};
            _.each(row, (value, colIndex) => {
              obj[columns[colIndex]] = value;
            });
            jsonData.push(obj);
          }
        });

        resolve({ data: jsonData, columns: columns });
      };

      reader.readAsBinaryString(target.files[0]);
    });
  }

  // Upload Validated Template
  async uploadTemplateMain() {
    this.migrationLoading = true;

    this.pmMasterService.uploadMilestoneStatus(this.downloadedTemplate).then((res)=>{
      this.migrationLoading=false;
      console.log(res)
      if(res['messType']=="S")
      {
        this.onceuploadcheck=false;
        this.utilityService.showMessage(res['message'],"Dismiss",3000)
      }
      else
      {
        this.utilityService.showMessage("Error while uploading template","Dismiss",3000)
      }
      
    })
  }
  clickToMigrateFile(){

    if(this.issuePresent)
    {
      this.utilityService.openConfirmationSweetAlertWithCustom("Do you want to upload template with issues?","").then((upload) => {
        if (upload) {
          this.stepper2.next();
        }
    })
    }
    else
    {
      this.stepper2.next();
    }
    
  }
  // Navigate to Next Step
  onNextPressed() {
    this.stepper1.next();
  }

  // Set Step Count
  setStepCount(event: any) {
    this.stepNo = event.selectedIndex;
  }

  // Destroy Subscription
  ngOnDestroy() {
    this._onDestroy.next();
    this._onDestroy.complete();
  }
}