
<ng-container *ngIf="loading">
    <div class="loader-container">
        <div class="loader"></div>
    </div>
</ng-container>
<ng-container *ngIf="!loading">
<div class="container-fluid dmc-component">
    <div class="d-flex">
        <div class="title-name mr-auto">
            Invoice Date Change
        </div>
        <div class="close">
            <mat-icon class="close-button" (click)="onCloseClickk()">clear</mat-icon>
        </div>
    </div>
    
    <!-- Show form when openInvoiceDateOption is true -->
    <div style="margin-top: 5%;" *ngIf="openInvoiceDateOption">
  

       

        <!-- Milestone Grid -->
        <div *ngIf="milestoneGridData.length > 0" class="mt-4">
            <mat-table [dataSource]="milestoneGridData">
                <ng-container class="milestoneId" matColumnDef="milestoneId">
                    <mat-header-cell *matHeaderCellDef>Milestone ID</mat-header-cell>
                    <mat-cell *matCellDef="let element">{{ element.milestoneId }}</mat-cell>
                </ng-container>
        
                <ng-container class="milestoneName" matColumnDef="milestoneName">
                    <mat-header-cell *matHeaderCellDef>Milestone Name</mat-header-cell>
                    <mat-cell *matCellDef="let element">{{ element.milestoneName }}</mat-cell>
                </ng-container>
        
                <ng-container class="invoiceDate" matColumnDef="invoiceDate">
                    <mat-header-cell *matHeaderCellDef>Invoice Date</mat-header-cell>
                    <mat-cell *matCellDef="let element">{{ element.invoiceDate | date: 'dd-MMM-yyyy' }}</mat-cell>
                </ng-container>
        
                <ng-container class="newInvoiceDate" matColumnDef="newInvoiceDate">
                    <mat-header-cell *matHeaderCellDef>Change Invoice Date</mat-header-cell>
                    <mat-cell *matCellDef="let element">
                        <input matInput [matDatepicker]="datePicker" [(ngModel)]="element.newInvoiceDate" (input)="onDateInput($event)" />
                        <mat-datepicker-toggle matSuffix [for]="datePicker"></mat-datepicker-toggle>
                        <mat-datepicker #datePicker></mat-datepicker>
                    </mat-cell>
                </ng-container>
        
                <ng-container class="save" matColumnDef="save">
                    <mat-header-cell *matHeaderCellDef>Actions</mat-header-cell>
                    <mat-cell *matCellDef="let element">
                        <button mat-raised-button class="upload-btn" (click)="saveMilestoneInvoiceDate(element)">Save</button>
                    </mat-cell>
                </ng-container>
        
                <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
                <mat-row *matRowDef="let row; columns: displayedColumns;"></mat-row>
            </mat-table>
        </div>
        
        
    </div>
</div>
</ng-container>
