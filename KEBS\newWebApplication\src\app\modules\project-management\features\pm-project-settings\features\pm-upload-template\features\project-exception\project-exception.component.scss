
.dmc-component{
  margin-top: 2%;
    .title-name{
        color: var(--intSettingButton) !important;
        font-size: 18px!important;
        font-family: var(--intSettingFont) !important;
        margin-bottom: 10px;
    }
    ::ng-deep .mat-horizontal-stepper-header .mat-step-icon{
      background: var(--intSettingButton) !important;
    }
    ::ng-deep .mat-horizontal-stepper-header .mat-step-icon{
      background: var(--intSettingButton) !important;
    }
    ::ng-deep .mat-step-header .mat-step-label{
      font-family: var(--intSettingFont) !important;
    }
    .settings-card{
        min-height: 85px;
        outline: none;
        background-size: 48px 48px;
        background-repeat: no-repeat;
        background-position: 86% 50%;
    
        .title{
            font-size: 15px;
            color: #1a1a1a;
            font-family: var(--intSettingFont) !important;
        }         
    }
    .milestoneId{
      width:15%
    }
    
    .form-label{
        font-size: 15px;
            color: #1a1a1a;
            font-family: var(--intSettingFont) !important;

    }
    .close{
      width: 16px;
      height: 16px;
      margin-left:  566px;
      cursor: pointer;

  }
    .settings-card:hover{
        cursor: pointer;
        box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.2), 0 2px 10px 0 rgba(0, 0, 0, 0.19);
    
        .title:hover{
            font-size: 15px;
            color: var(--intSettingButton) !important;
        }
    }
    
    .slide-in-top {
        -webkit-animation: slide-in-top 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94)
          both;
        animation: slide-in-top 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
      }
    
      .slide-from-down {
        -webkit-animation: slide-from-down 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94)
          both;
        animation: slide-from-down 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
      }
    
    
          /**
       * ----------------------------------------
       * animation slide-in-top
       * ----------------------------------------
       */
    @-webkit-keyframes slide-in-top {
        0% {
          -webkit-transform: translateY(-30px);
          transform: translateY(-30px);
          opacity: 0;
        }
        100% {
          -webkit-transform: translateY(0);
          transform: translateY(0);
          opacity: 1;
        }
      }
      @keyframes slide-in-top {
        0% {
          -webkit-transform: translateY(-30px);
          transform: translateY(-30px);
          opacity: 0;
        }
        100% {
          -webkit-transform: translateY(0);
          transform: translateY(0);
          opacity: 1;
        }
      }
    
    
        /**
       * ----------------------------------------
       * animation slide-from-bottom
       * ----------------------------------------
       */
    
      @-webkit-keyframes slide-from-down {
        0% {
          -webkit-transform: translateY(30px);
          transform: translateY(30px);
          opacity: 0;
        }
        100% {
          -webkit-transform: translateY(0);
          transform: translateY(0);
          opacity: 1;
        }
      }
    
      @keyframes slide-from-down {
        0% {
          -webkit-transform: translateY(30px);
          transform: translateY(30px);
          opacity: 0;
        }
        100% {
          -webkit-transform: translateY(0);
          transform: translateY(0);
          opacity: 1;
        }
      }
    
    
      
        .icon-error {
          color: #921010;
          font-size: 16px;
        }
      
        .small-txt {
          font-size: 12px;
        }
      
        .drop-container {
          width: 100%;
          text-align: center;
          border: dashed 2px #979797;
          position: relative;
          margin: 0 auto;
      
          input {
            opacity: 0;
            position: absolute;
            z-index: 2;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
          }
      
          label {
            color: white;
            width: 183px;
            height: 44px;
            border-radius: 21.5px;
            background-color: #db202f;
            padding: 8px 16px;
          }
      
          .zone-text {
            font-size: 15px;
            color: #38424c;
          }
        }
      
        .txt-bold {
          font-weight: 500;
        }
      
        .upload-btn {
          background-color: var(--intSettingButton) !important;
          color: #ffffff;
          font-family: var(--intSettingFont) !important;
        }
        
        /* Apply border around the entire mat-table */
        /* Apply border around the entire mat-table */
mat-table {
    border: 1px solid #ccc; /* Light gray border for the table */
    border-radius: 4px;      /* Optional: Rounded corners */
  }
  

  
  /* Remove border from last column */
  mat-header-cell:last-child, mat-cell:last-child {
    border-right: none;
  }
  
  /* Border between rows */
 
  /* Remove border from last row */
  mat-header-row:last-child, mat-row:last-child {
    border-bottom: none;
  }
  .mat-header-row, .mat-row {
    display: flex;
    justify-content: space-between;
    width: 100%;
  }
  
  .mat-column-milestoneId,
  
  .mat-column-invoiceDate{
    flex: 1; /* Distribute the space evenly */
    max-width: 100px; /* Ensure columns don’t shrink too much */
  }
  .mat-column-newInvoiceDate{
    flex: 1; /* Distribute the space evenly */
    max-width: 130px;

  }
  .mat-column-milestoneName{
    flex: 1; /* Distribute the space evenly */
    max-width: 170px; /* Ensure columns don’t shrink too much */

  }
  .mat-column-save {
    flex: 1; /* Distribute the space evenly */
    max-width: 70px; /* Ensure columns don’t shrink too much */

  }

  

  
      
        .upload-btn:disabled {
            background-color: #b8b8b8;
            color: #252525;
            font-family: var(--intSettingFont) !important;

        }
      
        .fileover {
          opacity: 0.5;
          border: solid 2px #c92020;
        }
      
        .upload-field-inputsearch {
          font-size: 13px !important;
            width: 100% !important;
          
        }
      
        
.upload-template{
    .title-name{
        color: green;
        font-size: 18px!important;
    }

    .iconbtn {
      background-color: #c92020;
      color: white;
      padding: 0 0;
      box-shadow: 0 3px 5px -1px rgba(0, 0, 0, 0.2),
        0 6px 10px 0 rgba(0, 0, 0, 0.14), 0 1px 18px 0 rgba(0, 0, 0, 0.12);
    }
    
    .settings-card{
        min-height: 85px;
        outline: none;
        background-size: 48px 48px;
        background-repeat: no-repeat;
        background-position: 86% 50%;
    
        .title{
            font-size: 15px;
            color: #1a1a1a;
            font-family: var(--intSettingFont) !important;

        }         
    }
    
    .settings-card:hover{
        cursor: pointer;
        box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.2), 0 2px 10px 0 rgba(0, 0, 0, 0.19);
    
        .title:hover{
            font-size: 15px;
            color: var(--intSettingButton) !important;
            font-family: var(--intSettingFont) !important;

        }
    }
    
    .slide-in-top {
        -webkit-animation: slide-in-top 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94)
          both;
        animation: slide-in-top 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
      }
    
      .slide-from-down {
        -webkit-animation: slide-from-down 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94)
          both;
        animation: slide-from-down 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
      }
    
    
          /**
       * ----------------------------------------
       * animation slide-in-top
       * ----------------------------------------
       */
    @-webkit-keyframes slide-in-top {
        0% {
          -webkit-transform: translateY(-30px);
          transform: translateY(-30px);
          opacity: 0;
        }
        100% {
          -webkit-transform: translateY(0);
          transform: translateY(0);
          opacity: 1;
        }
      }
      @keyframes slide-in-top {
        0% {
          -webkit-transform: translateY(-30px);
          transform: translateY(-30px);
          opacity: 0;
        }
        100% {
          -webkit-transform: translateY(0);
          transform: translateY(0);
          opacity: 1;
        }
      }
    
    
        /**
       * ----------------------------------------
       * animation slide-from-bottom
       * ----------------------------------------
       */
    
      @-webkit-keyframes slide-from-down {
        0% {
          -webkit-transform: translateY(30px);
          transform: translateY(30px);
          opacity: 0;
        }
        100% {
          -webkit-transform: translateY(0);
          transform: translateY(0);
          opacity: 1;
        }
      }
    
      @keyframes slide-from-down {
        0% {
          -webkit-transform: translateY(30px);
          transform: translateY(30px);
          opacity: 0;
        }
        100% {
          -webkit-transform: translateY(0);
          transform: translateY(0);
          opacity: 1;
        }
      }
    
    
      
        .icon-error {
          color: #921010;
          font-size: 16px;
        }
      
        .small-txt {
          font-size: 12px;
        }
      
        .drop-container {
          width: 100%;
          text-align: center;
          border: dashed 2px #979797;
          position: relative;
          margin: 0 auto;
      
          input {
            opacity: 0;
            position: absolute;
            z-index: 2;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
          }
      
          label {
            color: white;
            width: 183px;
            height: 44px;
            border-radius: 21.5px;
            background-color: #db202f;
            padding: 8px 16px;
          }
      
          .zone-text {
            font-size: 15px;
            color: #38424c;
          }
        }
      
        .txt-bold {
          font-weight: 500;
        }
      
        .upload-btn {
          background-color: #c92020;
          color: #ffffff;
        }
      
        .upload-btn:disabled {
            background-color: #b8b8b8;
            color: #252525;
        }
      
        .fileover {
          opacity: 0.5;
          border: solid 2px #c92020;
        }
      
        .upload-field-inputsearch {
          font-size: 13px !important;
            width: 100% !important;
          
        }

        .complete {
          color: white;
          font-weight: normal;
          font-size: 12px !important;
          background-color: #cf0001;
          min-width: 62px;
          line-height: 26px;
          padding: 0 10px;
          width: 150px;
        }
      
      
    }
      
    }
    .loader-container {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh; /* Adjust the height as needed */
        background-color: '#FFFFFF' !important; /* Add a semi-transparent background */
       
      }
      .loader {
        border: 4px solid #FFFFFF;
        border-top: 4px solid var(--intSettingButton) !important; /* Change the border color to lightgreen */
        border-radius: 50%;
        width: 40px;
        height: 40px;
        margin-bottom: 25% !important;
        animation: spin 1s linear infinite;
      }
      @keyframes spin {
        0% {
            transform: rotate(0deg);
        }
        100% {
            transform: rotate(360deg);
        }
    }