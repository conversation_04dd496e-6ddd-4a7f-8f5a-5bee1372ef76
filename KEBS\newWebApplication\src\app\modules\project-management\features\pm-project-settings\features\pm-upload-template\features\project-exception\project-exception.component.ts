
import { Component, HostListener, OnInit, ViewChild } from '@angular/core';
import { MatStepper } from '@angular/material/stepper';
import * as _ from 'underscore';
import { FormBuilder, FormControl, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { debounceTime } from 'rxjs/operators';
import { JsonToExcelService } from 'src/app/services/excel/json-to-excel.service';
import { NgxSpinnerService } from 'ngx-spinner';
import { ToasterService } from 'src/app/modules/applicant-tracking-system/shared-components/ats-custom-toast/toaster.service';
import { HttpClient, HttpClientModule, HttpHeaders, HttpRequest } from "@angular/common/http";
import moment from 'moment';
import { ProjectService } from 'src/app/modules/projects/services/project.service'; 
import { UtilityService } from 'src/app/services/utility/utility.service';
import { LoginService } from "src/app/services/login/login.service";
import { forceCopyProperties } from '@amcharts/amcharts4/.internal/core/utils/Object';
import { DxDataGridComponent,DxNumberBoxComponent } from 'devextreme-angular';
import * as momentBusiness from 'moment-business-days';
import DataSource from "devextreme/data/data_source";
import { MatTableModule } from '@angular/material/table';
import { count } from 'rxjs/operators';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core';
import { MAT_MOMENT_DATE_ADAPTER_OPTIONS, MomentDateAdapter } from '@angular/material-moment-adapter';
import { PmMasterService } from 'src/app/modules/project-management/services/pm-master.service';
import {OnChanges,Inject, ViewContainerRef, InjectionToken } from '@angular/core';
import { FormArray,FormGroup} from '@angular/forms';
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';

import { ToastrService } from 'ngx-toastr';

import { v4 as uuidv4 } from 'uuid';

import { truncateWithEllipsis } from '@amcharts/amcharts4/.internal/core/utils/Utils';

import { ToasterMessageService } from 'src/app/modules/project-management/shared-lazy-loaded/components/toaster-message/toaster-message.service';
import { PmAuthorizationService } from 'src/app/modules/project-management/services/pm-authorization.service';
import { GetNameByIdPipe } from 'src/app/modules/project-management/shared-lazy-loaded/pipes/get-name-by-id.pipe';
export const TOASTER_MSG_SERVICE_TOKEN = new InjectionToken<ToasterMessageService>('TOASTER_MSG_SERVICE_TOKEN');

@Component({
  selector: 'app-project-exception',
  templateUrl: './project-exception.component.html',
  styleUrls: ['./project-exception.component.scss'],
  providers: [
    {
      provide: DateAdapter,
      useClass: MomentDateAdapter,
      deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS]
    },
    {
      provide: MAT_DATE_FORMATS,
      useValue: {
        parse: {
          dateInput: "DD-MMM-YYYY"
        },
        display: {
          dateInput: "DD-MMM-YYYY",
          monthYearLabel: "MMM YYYY"
        }
      }
    }
  ]
})
export class ProjectExceptionComponent implements OnInit {
  openInvoiceDateOption:boolean=false;
  project_list:any=[]
  selectedProject: any;
  selectedMilestone: any;
  projects = []; // List of projects
  milestones = []; // List of milestones based on selected project
  currentInvoiceDate: Date;
  milestoneGridData = []; // Data for milestone grid
  displayedColumns: string[] = ['milestoneId', 'milestoneName', 'invoiceDate', 'newInvoiceDate', 'save'];
  formConfig: any = [];
  button: any;
  oldInvoiceDate:any;
  newInvoiceDate:any;
  fontStyle: any;
  secondary: string = '#f5f5f5'
  save_disabled:boolean=false;
  loading:boolean=true;

  reportForm = this.fb.group({
    project_name: [],
    milestone_name:[]
  })
  @HostListener('window:keyup.esc') onKeyUp() {
    this.onCloseClickk()
  }

  constructor( @Inject(MAT_DIALOG_DATA)
  public data: {
    item_id: any;
    milestone_id:any;
  },private fb: FormBuilder,private utilityService: UtilityService,private PmMasterService: PmMasterService,private _dialogRef: MatDialogRef<ProjectExceptionComponent>) { }

 async  ngOnInit() {
  this.loading=true
  this.openInvoiceDateOption=true

  console.log(this.data)
 
  this.openInvoiceDate()
  await this.PmMasterService.getPMFormCustomizeConfigV().then((res: any) => {
    if (res) {
      this.formConfig = res;
    }
  });
  document.documentElement.style.setProperty('--secondary', this.secondary);
  const retrieveStyles2 = _.where(this.formConfig, { type: "project-theme", field_name: "styles", is_active: true });
  if(retrieveStyles2.length > 0){
    this.button = retrieveStyles2.length > 0 ? retrieveStyles2[0].data.button_color ? retrieveStyles2[0].data.button_color : "#90ee90" : "#90ee90";
    document.documentElement.style.setProperty('--intSettingButton', this.button)
    //this.noDataImage1 = retrieveStyles2[0].data.no_data_image ? retrieveStyles2[0].data.no_data_image : "https://assets.kebs.app/No-milestone-image.png";
    this.fontStyle = retrieveStyles2.length > 0 ? retrieveStyles2[0].data.font_style ? retrieveStyles2[0].data.font_style : "Roboto" : "Roboto";
    document.documentElement.style.setProperty('--intSettingFont', this.fontStyle);
  }



 

await this.PmMasterService.getMilestoneListDetails(this.data.item_id,this.data.milestone_id).then((res: any) => {
            if (res) {
              this.milestoneGridData=res['data']
              this.oldInvoiceDate = {
                "Invoice Date": moment(res['data']['invoice_date']).format("DD-MMM-YYYY")
              }
 
            }
          })

    this.loading=false;
  }
  openInvoiceDate()
  {

    this.openInvoiceDateOption = true
  }


 


  onMilestoneChange(event: any) {
    // Simulate fetching the current invoice date for the selected milestone
    this.currentInvoiceDate = new Date();
  }

  saveInvoiceDate() {
    const invoiceDate = this.reportForm.controls['invoice_date'].value;
    // Save the invoice date
    console.log('Saving invoice date:', invoiceDate);

  }
  onDateInput(event: KeyboardEvent): void {
    const charCode = event.charCode || event.keyCode;
    
    // Prevent non-numeric and non-date characters (e.g., alphabets, symbols)
    if (charCode < 48 || charCode > 57) {
      event.preventDefault();
    }
  }
  

 async  saveMilestoneInvoiceDate(element: any) {

    if((element.newInvoiceDate === null || element.newInvoiceDate === undefined || element.newInvoiceDate === '')){
      this.utilityService.showMessage("Kindly Fill Invoice Date","Dismiss",3000)

    }else{
    this.save_disabled=true
    this.newInvoiceDate = {
      "Invoice Date": moment(element.newInvoiceDate).format("DD-MMM-YYYY")
    }
    
    await this.PmMasterService.updateInvoiceDetails(
      this.data.item_id,
      element.milestoneId,
      element.newInvoiceDate,
      this.oldInvoiceDate,
      this.newInvoiceDate
    ).then((res: any) => {
      if (res['messType'] === 'S') {
        this.utilityService.showMessage(res['message'], "Dismiss", 3000);
        this.save_disabled = false;
      } else {
        this.utilityService.showMessage("Error while updating Invoice Date", "Dismiss", 3000);
        this.save_disabled = false;
      }
    }).catch((error) => {
      this.utilityService.showMessage("Error while updating Invoice Date", "Dismiss", 3000);
      this.save_disabled = false;
    });
    
  }
  }
  onCloseClickk(){
    this._dialogRef.close({ messType: "E" })
  }

}
export interface DialogData {
  item_id :any;
  milestone_id: any;
}