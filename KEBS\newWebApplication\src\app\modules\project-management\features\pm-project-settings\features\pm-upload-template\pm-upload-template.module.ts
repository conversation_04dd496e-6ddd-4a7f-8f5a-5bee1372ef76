import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { PmUploadTemplateRoutingModule } from './pm-upload-template-routing.module';
import { PmBillingAdviceUploadComponent } from './features/pm-billing-advice-upload/pm-billing-advice-upload.component';
import { PmMilestoneUploadComponent } from './features/pm-milestone-upload/pm-milestone-upload.component';
import { PmSowUploadComponent } from './features/pm-sow-upload/pm-sow-upload.component';
import { PmInternalStakeholderUploadComponent } from './features/pm-internal-stakeholder-upload/pm-internal-stakeholder-upload.component';
import { PmAdaptTemplateUploadComponent } from './features/pm-adapt-template-upload/pm-adapt-template-upload.component';
import { PmUploadTemplateComponent } from './components/pm-upload-template/pm-upload-template.component';
import { MatStepperModule } from '@angular/material/stepper';
import { MatFormFieldModule } from '@angular/material/form-field';
import { SharedLazyLoadedModule } from 'src/app/modules/project-management/shared-lazy-loaded/shared-lazy-loaded.module';
import { MatInputModule } from '@angular/material/input';
import { MatBadgeModule } from '@angular/material/badge';
import { MatCardModule } from '@angular/material/card';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatListModule } from '@angular/material/list';
import { VirtualScrollModule } from 'angular2-virtual-scroll';
import { MatProgressButtonsModule } from 'mat-progress-buttons';
import { NgxSpinnerModule } from 'ngx-spinner';
import { PmPortfolioUploadComponent } from './features/pm-portfolio-upload/pm-portfolio-upload.component';
import { PmProjectUploadComponent } from './features/pm-project-upload/pm-project-upload.component';
import { ProjectExceptionComponent } from './features/project-exception/project-exception.component';
import { MatDatepickerModule } from '@angular/material/datepicker';  // Import MatDatepickerModule
import { MatTableModule } from '@angular/material/table';  // Import MatTableModule
import { PmMilestoneStatusUploadComponent } from './features/pm-milestone-status-upload/pm-milestone-status-upload.component';


@NgModule({
  declarations: [
    PmBillingAdviceUploadComponent, 
    PmMilestoneUploadComponent, 
    PmSowUploadComponent, 
    PmInternalStakeholderUploadComponent, 
    PmAdaptTemplateUploadComponent, 
    PmUploadTemplateComponent, PmPortfolioUploadComponent, PmProjectUploadComponent, PmMilestoneStatusUploadComponent,ProjectExceptionComponent
  ],
  imports: [
    CommonModule,
    PmUploadTemplateRoutingModule,
    MatStepperModule,
    MatTableModule,
    MatDatepickerModule,
    MatFormFieldModule,
    SharedLazyLoadedModule,
    MatInputModule,
    MatBadgeModule,
    MatCardModule,
    MatProgressSpinnerModule,
    ReactiveFormsModule,
    FormsModule,
    MatIconModule,
    MatButtonModule,
    MatTooltipModule,
    MatListModule,
    VirtualScrollModule,
    MatProgressButtonsModule,
    NgxSpinnerModule,
    MatStepperModule,
  ]
})
export class PmUploadTemplateModule { }
