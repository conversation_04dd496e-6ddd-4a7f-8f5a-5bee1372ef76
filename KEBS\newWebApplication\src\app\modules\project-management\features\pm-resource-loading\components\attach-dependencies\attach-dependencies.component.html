<div class="attach-dependencies">
  <div class="header">
    <div>
      Attach Dependencies
    </div>
    <div><mat-icon class="icon" (click)="onDialogClose()" tooltip="Close">clear</mat-icon></div>
  </div>
  <div *ngIf="dataLoading" class="loading-img">
    <div class="load-img">
        <img *ngIf="loadingGif" [src]="loadingGif" />
    </div>
    <div class="loading-wrapper">
        <div class="loading">Loading...</div>
    </div>
</div>
  <div *ngIf="!dataLoading" class="table-content">
    <table>
      <thead>
        <tr>
          <th></th>
          <th>Type</th>
          <th>#</th>
          <th>Item</th>
        </tr>
      </thead>
      <tbody>
        <ng-container *ngFor="let item of groupedData">
          <ng-container *ngTemplateOutlet="renderRow; context: { $implicit: item, level: 0 }"></ng-container>
        </ng-container>
      </tbody>
    </table>

    <ng-template #renderRow let-item let-level="level">
      <tr>
        <!-- First column: checkbox -->
        <td style="text-align: center;">
          <mat-checkbox id="{{ getId(item.text) }}" class="checkbox-input custom-checkbox adjust-checkbox" 
          (change)="toggleCheckboxSelection($event, item)" [(ngModel)]="item.checked" [disabled]="isSaveDisabled">
          </mat-checkbox>
      </td>      

        <!-- Second column: arrow for expand/collapse with padding based on level -->
        <td [style.padding-left.px]="(level * 20)+5">
          <div class="d-flex">
            <div *ngIf="item.children && item.children.length > 0" class="toggle-button"
              (click)="toggleVisibility(item)">
              <div *ngIf="item.active">
                <svg width="18" height="19" viewBox="0 0 18 19" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <mask id="mask0_19566_80628" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="18" height="19">
                  <rect y="0.820312" width="18" height="18" fill="#D9D9D9"/>
                  </mask>
                  <g mask="url(#mask0_19566_80628)">
                  <path d="M9.00012 11.8107L5.69727 8.50781H12.303L9.00012 11.8107Z" fill="#1C1B1F"/>
                  </g>
                  </svg>
              </div>
              <div *ngIf ="!item.active">
                <svg width="18" height="19" viewBox="0 0 18 19" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <mask id="mask0_19566_80628" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="18" height="19">
                    <rect y="0.820312" width="18" height="18" fill="#D9D9D9"/>
                  </mask>
                  <g mask="url(#mask0_19566_80628)" transform="rotate(275 9 9.5)"> <!-- Rotation applied here -->
                    <path d="M9.00012 11.8107L5.69727 8.50781H12.303L9.00012 11.8107Z" fill="#1C1B1F"/>
                  </g>
                </svg>
              </div>
            </div>
            <div class="pl-1">
              <div *ngIf="item.gantt_type_id == 3">
                <svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <mask id="mask0_19566_80647" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="16" height="17">
                  <rect y="0.820312" width="16" height="16" fill="#D9D9D9"/>
                  </mask>
                  <g mask="url(#mask0_19566_80647)">
                  <path d="M13.0014 14.6546C12.7701 14.6546 12.5733 14.5736 12.4109 14.4117C12.2485 14.2498 12.1673 14.0533 12.1673 13.822C12.1673 13.5907 12.2483 13.3939 12.4102 13.2315C12.572 13.0691 12.7686 12.9879 12.9999 12.9879C13.2312 12.9879 13.428 13.0689 13.5904 13.2308C13.7528 13.3927 13.834 13.5892 13.834 13.8205C13.834 14.0518 13.753 14.2486 13.5911 14.411C13.4292 14.5734 13.2327 14.6546 13.0014 14.6546ZM6.66805 15.3212C5.60345 15.3212 4.69829 15.2078 3.95257 14.9808C3.20685 14.7539 2.83398 14.4785 2.83398 14.1546C2.83398 13.919 3.04723 13.7091 3.47373 13.5247C3.90023 13.3404 4.40493 13.2042 4.98783 13.1161V14.193H6.16732V2.42383L10.9622 4.75458L7.16728 6.71868V13.0084C8.12284 13.064 8.91729 13.1961 9.55062 13.4048C10.184 13.6135 10.5006 13.8634 10.5006 14.1546C10.5006 14.4785 10.128 14.7539 9.38279 14.9808C8.63756 15.2078 7.73265 15.3212 6.66805 15.3212Z" fill="#FA541C"/>
                  </g>
                  </svg>                  
              </div>
              <div *ngIf="item.gantt_type_id == 4">
                <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <mask id="mask0_19566_80664" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="17" height="17">
                  <rect x="15.998" y="0.730469" width="16" height="16" transform="rotate(89.3569 15.998 0.730469)" fill="#D9D9D9"/>
                  </mask>
                  <g mask="url(#mask0_19566_80664)">
                  <path d="M2.02911 5.82101L2.72371 5.11062L6.535 8.83729L2.80833 12.6486L2.09796 11.954L5.11974 8.85318L2.02911 5.82101ZM6.02886 5.77611L6.72346 5.06572L10.5347 8.79239L6.80808 12.6037L6.0977 11.9091L9.11949 8.80828L6.02886 5.77611ZM10.0286 5.73122L10.7232 5.02082L14.5345 8.74749L10.8078 12.5588L10.0975 11.8642L13.1192 8.76338L10.0286 5.73122Z" fill="#FFBD3D"/>
                  </g>
                  </svg>                                
              </div>
              <div *ngIf="item.gantt_type_id == 5">
                <svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <mask id="mask0_19566_80682" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="16" height="17">
                  <rect y="0.820312" width="16" height="16" fill="#D9D9D9"/>
                  </mask>
                  <g mask="url(#mask0_19566_80682)">
                  <path d="M3.53912 14.4869C3.20664 14.4869 2.92267 14.3691 2.6872 14.1337C2.45172 13.8982 2.33398 13.6142 2.33398 13.2817V4.35871C2.33398 4.02623 2.45172 3.74226 2.6872 3.50679C2.92267 3.27131 3.20664 3.15357 3.53912 3.15357H6.4763C6.56861 2.8288 6.75728 2.55957 7.04232 2.34591C7.32735 2.13223 7.64679 2.02539 8.00063 2.02539C8.36815 2.02539 8.69271 2.13223 8.97433 2.34591C9.25596 2.55957 9.44292 2.8288 9.53522 3.15357H12.4622C12.7946 3.15357 13.0786 3.27131 13.3141 3.50679C13.5495 3.74226 13.6673 4.02623 13.6673 4.35871V13.2817C13.6673 13.6142 13.5495 13.8982 13.3141 14.1337C13.0786 14.3691 12.7946 14.4869 12.4622 14.4869H3.53912ZM3.53912 13.4869H12.4622C12.5134 13.4869 12.5605 13.4655 12.6032 13.4228C12.6459 13.38 12.6673 13.333 12.6673 13.2817V4.35871C12.6673 4.30742 12.6459 4.2604 12.6032 4.21766C12.5605 4.17492 12.5134 4.15356 12.4622 4.15356H11.0006V5.89712H5.00065V4.15356H3.53912C3.48783 4.15356 3.44081 4.17492 3.39807 4.21766C3.35533 4.2604 3.33397 4.30742 3.33397 4.35871V13.2817C3.33397 13.333 3.35533 13.38 3.39807 13.4228C3.44081 13.4655 3.48783 13.4869 3.53912 13.4869ZM8.00063 4.23049C8.17242 4.23049 8.3158 4.17301 8.43075 4.05806C8.54572 3.9431 8.6032 3.79972 8.6032 3.62792C8.6032 3.45613 8.54572 3.31276 8.43075 3.19781C8.3158 3.08285 8.17242 3.02537 8.00063 3.02537C7.82885 3.02537 7.68547 3.08285 7.57052 3.19781C7.45555 3.31276 7.39807 3.45613 7.39807 3.62792C7.39807 3.79972 7.45555 3.9431 7.57052 4.05806C7.68547 4.17301 7.82885 4.23049 8.00063 4.23049Z" fill="#722ED1"/>
                  </g>
                  </svg>
              </div>
            </div>
          </div>
        </td>

        <!-- Third column: text -->
         <td >
          <label style="margin: unset;
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
          width: 50px;
          " for="{{ getId(item.wbsId) }}">{{ item.wbsId }}</label>
         </td>
        <td>
          <label style="margin: unset;
          text-overflow: ellipsis;
          white-space: nowrap;
          width: 475px;
          overflow: hidden;
          " for="{{ getId(item.text) }}">{{ item.text }}</label>
        </td>
      </tr>

      <!-- Render child rows directly below parent row -->
      <ng-container *ngIf="item.active && item.children && item.children.length > 0">
        <ng-container *ngFor="let child of item.children">
          <ng-container *ngTemplateOutlet="renderRow; context: { $implicit: child, level: level + 1 }"></ng-container>
        </ng-container>
      </ng-container>
    </ng-template>
  </div>
  <div *ngIf="!dataLoading" class="footer" [ngStyle]="{ 'justify-content': isSaveDisabled ? 'flex-start' : 'flex-end' }">
    <div class="d-flex note" *ngIf="isSaveDisabled">
      <div style="color: red;">Note:</div>
      <div class="pl-2">Dependencies cannot be added due to the current status of the milestone.</div>
    </div>
    <div class="action">
      <div *ngIf="!isSaveDisabled"
      class="add" (click)="addDependencies()">
        Save
      </div>
    </div>
  </div>
</div>