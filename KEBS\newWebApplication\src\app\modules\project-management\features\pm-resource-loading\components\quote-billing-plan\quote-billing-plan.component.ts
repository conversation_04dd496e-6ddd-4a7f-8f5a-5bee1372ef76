import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { ResourceLoadingLandingPageComponent } from '../../features/resource-loading-landing-page/resource-loading-landing-page.component';
import { AttachDependenciesComponent } from '../attach-dependencies/attach-dependencies.component';
import { MatDialog } from '@angular/material/dialog';
import { SubSink } from 'subsink';
import { Router } from '@angular/router';
import { ToasterMessageService } from 'src/app/modules/project-management/shared-lazy-loaded/components/toaster-message/toaster-message.service';
import { ResourceLoadingServiceService } from '../../services/resource-loading-service.service'
import * as _ from 'underscore';
import { PmAuthorizationService } from 'src/app/modules/project-management/services/pm-authorization.service'
@Component({
  selector: 'app-quote-billing-plan',
  templateUrl: './quote-billing-plan.component.html',
  styleUrls: ['./quote-billing-plan.component.scss']
})
export class QuoteBillingPlanComponent implements OnInit {
  
  @ViewChild('resourceLoadingComponent') resourceLoadingComponent: ResourceLoadingLandingPageComponent;
  @Input() quoteData: Object = null;
  @Input() formConfig: any = null;
  @Input() is_deliverable:boolean=false;


  showAddDependencies: boolean=false;
  showRemainingQuantity: boolean=false;
  subs = new SubSink();
  tableHeight = '405px';
  projectId: any;
  itemId: any;
  isFinancialValuesHidden:boolean=false
  constructor(public dialog: MatDialog,
    private _router: Router,
    private _toasterService: ToasterMessageService,
    private _resourceService : ResourceLoadingServiceService, private authService: PmAuthorizationService

  ) { }

 async ngOnInit() {
    document.documentElement.style.setProperty(
      '--planHeight',
      this.tableHeight
    )
    this.projectId = parseInt(this._router.url.split('/')[3]);
    this.itemId = parseInt(this._router.url.split('/')[4]);
    this.isFinancialValuesHidden=await this.authService.getProjectWiseObjectAccess(this.projectId, this.itemId, 178)
    this.quoteData['dependencies'] = this.quoteData['line_item_ids'] ?  this.quoteData['line_item_ids'].length :0

    this.showAddDependencies = _.findWhere(this.formConfig,{type:"fixed-billing-plan", field_name:"show_add_dependencies", is_active: true})
    this.showRemainingQuantity = _.findWhere(this.formConfig,{type:"fixed-billing-plan", field_name:"show_remaining_quantity", is_active: true})
  }

  async onExpandClick(){
    this.quoteData['expand'] = !this.quoteData['expand']
  }

  // Function to call on Edit button click
  onEditClick() {
    if (this.resourceLoadingComponent) {
      this.resourceLoadingComponent.editPlanned();  // Call the function in child component
    }
  }

  addDependency(){
    const dialogRef = this.dialog.open(AttachDependenciesComponent, {
      data: {
        projectId: this.projectId,
        itemId: this.itemId,
        formConfig: this.formConfig,
        dependencies: this.quoteData['line_item_ids'] ?  this.quoteData['line_item_ids'] :[]
      },
    });
    dialogRef.afterClosed().subscribe((result:any) => {
      if(result && result['data']){
          this.addDependenciesToDeliverable(result['data'])
          this.quoteData['dependencies'] =result['data'].length
          this.quoteData['line_item_ids'] = result['data']
      }
    });
  }


  addDependenciesToDeliverable(line_item_ids) {
    return new Promise((resolve,reject) => {
      this.subs.sink = this._resourceService.saveAttachDependencies(this.quoteData['deliverable_id'], line_item_ids).subscribe(
         (res:any) => {
          if(res['messType']=="S") {
            this._toasterService.showSuccess('Dependencies saved successfully!', 10000);
            this.quoteData['dependencies'] = this.quoteData['line_item_ids'] ?  this.quoteData['line_item_ids'].length :0
          } 
          else{
            this._toasterService.showError('Error while saving dependencies');
          }
       
        },
        (err) => {
          this._toasterService.showError('Error in Fetching Project Details');
          console.log(err);
          resolve(null);
        }
      );
    });
  }
}
