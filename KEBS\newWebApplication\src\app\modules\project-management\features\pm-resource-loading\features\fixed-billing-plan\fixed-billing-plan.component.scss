.fixed-billing-plan{
    background: #f0f2f7;
    padding: 10px;
    height: var(--dynamicResourcePageHeight) !important;
    font-family: var(--teamFont) !important;

    .loader-container{
        // background: #ffff;
        display: flex;
        justify-content: center;
        align-items: center;
        position: relative;
        height: 100%;
      }
      .custom-loader {
        width:50px;
        height:50px;
        border-radius:50%;
        padding:1px;
        background:conic-gradient(#0000 10%,var(--teamButton)) content-box;
        -webkit-mask:
          repeating-conic-gradient(#0000 0deg,#000 1deg 20deg,#0000 21deg 36deg),
          radial-gradient(farthest-side,#0000 calc(100% - 9px),#000 calc(100% - 8px));
        -webkit-mask-composite: destination-in;
        mask-composite: intersect;
        animation:s4 1s infinite steps(10);
      }
      @keyframes s4 {to{transform: rotate(1turn)}}
      .body-class{
        padding: 5px 10px;
      }
      .header {
        display: flex;
        justify-content: space-between;
        // padding-top: 12px;
        padding-bottom: 14px;
    
        .header-start {
          display: flex;
          align-items: center;
          gap: 0.5rem;
    
          .back-icon {
            width: 18px;
            height: 18px;
            border: 1px solid #8B95A5;
            border-radius: 3px;
            cursor: pointer;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 9px;
          }
          .icn-class {
            font-size: 14px;
            color: #8B95A5;
            display: flex;
            justify-content: center;
            align-items: center;
          }
        }
        .header-end {
          display: flex;
          align-items: center;
          gap: 1.2rem;
        }
      }
      .header-text {
        font-family: var(--teamFont) !important;
        font-size: 14px;
        font-weight: 600;
        line-height: 16px;
        text-align: left;
        color: #1B2140;
      }

      .card-class{
        font-family: var(--teamFont) !important;
        background-color: #FFFFFF;
        position: relative;
        height: var(--dynamicContentHeight) !important;
        // padding: 5px;
        font-size: 12px;
        color: #7D838B;
        overflow: auto;

        .header-card{
          box-shadow: 0px 4px 4px -2px rgba(0, 0, 0, 0.2);
          display: flex;
          flex-direction: row;
          height: auto;
          padding: 14px 18px;
          position: sticky;
          top: 0;
          z-index: 1000000000;
          background: #fff;
        }

        .content-long-text{
          display: flex !important;
          flex-direction: column;
          gap: 3px;
          // flex: 1;
          min-width: 15%;
          flex-wrap: nowrap;
          align-items: flex-start;
        }

        .content-text{
          display: flex !important;
          flex-direction: row;
          gap: 24px;
          min-width: 12%;
          flex-wrap: nowrap;
          align-items: center;
          justify-content: left;
        }

        .content-id{
          display: flex;
          flex-direction: row;
          align-items: center;
          gap: 8px;
          justify-content: space-between;
          width: 100%;
        }
        .content-button{
            display: flex;
            background-color: #EEF9E8;
            border: 1px solid #52C41A;
            justify-content: center;
            align-items: center;
            padding: 0px 6px;
            border-radius: 14px;
            gap: 4px;
            font-size: 10px;
            height: 16px;
    
            .circle{
              width: 6px;
              height: 6px;
              border-radius: 50%;
              background-color: #52C41A;
            }
            .active-text{
                color: #7D838B;
            }
        }
        .content-button-save{
          display: flex;
          background-color: #E8F4FF;
          border: 1px solid #1890FF;
          justify-content: center;
          align-items: center;
          padding: 1px 6px;
          border-radius: 14px;
          gap: 4px;
          font-size: 13px;
    
          .circle-save{
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #1890FF;
          }
          .save-text{
              color: #1890FF;
          }
        }
        .content-long-header{
            font-size: 14px;
            font-weight: 500;
            line-height: 16px;
            text-align: left;
            color: #111434;
        }

        .text-content-class{
          display: flex;
          flex-direction: column;
          gap: 3px;
          align-items: flex-start;
          justify-content: center
        }
        .content-head{
          font-family: var(--teamFont) !important;
            font-size: 12px;
            font-weight: 400;
            line-height: 16px;
            text-align: left;
            color: #7D838B;
        }
        .content-value{
          font-family: var(--teamFont) !important;
            font-size: 14px;
            font-weight: 500;
            line-height: 16px;
            text-align: left;
            color: #111434;
        }

      }

      ::ng-deep .mat-divider.mat-divider-vertical {
        margin-left: 8px !important;
        margin-top: 2px !important;
        margin-bottom: 2px !important;
        margin-right: 12px !important;
      } 
}