import { Component, HostListener, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { PmMasterService } from 'src/app/modules/project-management/services/pm-master.service';
import { ToasterMessageService } from 'src/app/modules/project-management/shared-lazy-loaded/components/toaster-message/toaster-message.service';
import { UtilityService } from 'src/app/services/utility/utility.service';
import { SubSink } from 'subsink';
import * as _ from 'underscore';
import { ResourceLoadingServiceService } from '../../services/resource-loading-service.service'
import { PmAuthorizationService } from 'src/app/modules/project-management/services/pm-authorization.service'
 

@Component({
  selector: 'app-fixed-billing-plan',
  templateUrl: './fixed-billing-plan.component.html',
  styleUrls: ['./fixed-billing-plan.component.scss']
})
export class FixedBillingPlanComponent implements OnInit {
  formConfig:any;
  isComponentLoading:boolean = false;
  projectId:any;
  itemId:any;
  quoteId: any;
  retrieveMessages: any;
  decimalPlaces:number = 2;
  projectData:any = null;
  initialLoading: boolean=false;
  deliverableList: any=[];
  is_deliverable: boolean=true;
  subs = new SubSink();
  data: any;
  isFinancialValuesHidden:boolean=false
  constructor(private _router: Router, private _masterService: PmMasterService,private _utilityService: UtilityService, private _resourceService : ResourceLoadingServiceService, private _toasterService: ToasterMessageService,private authService: PmAuthorizationService) { }

  async ngOnInit() {
    this.calculateDynamicStyle();
    this.isComponentLoading = true;
    this.projectId = parseInt(this._router.url.split('/')[3]);
    this.itemId = parseInt(this._router.url.split('/')[4]);
    this.quoteId = parseInt(this._router.url.split('/')[5])
    this.isFinancialValuesHidden=await this.authService.getProjectWiseObjectAccess( this.projectId,this.itemId, 178)
    await this.initializeFormConfigData();
    await this.getProjectDetails();
    this.data = await this.getFixedBillingPlanQuoteDetails();

   
    this.isComponentLoading = false;
  }

  @HostListener('window:resize')
  onResize() {
    this.calculateDynamicStyle();
  }
  /**
   * @description For Calculating Dynamic Height
   */
  calculateDynamicStyle(){
   
    let pageHeight = window.innerHeight - 50 + 'px'
    let contentHeight = window.innerHeight - 130 + 'px'
    document.documentElement.style.setProperty(
      '--dynamicResourcePageHeight',
      pageHeight
    )
    document.documentElement.style.setProperty(
      '--dynamicContentHeight',
      contentHeight
    )
  }

  /**
   * @description initializing Form Config Master data
   */
  async initializeFormConfigData() {
    await this._masterService.getPMFormCustomizeConfigV().then((res: any) => {
      if (res) {
        this.formConfig = res;
      }
    });
    this.retrieveMessages = _.where(this.formConfig, {
      type: 'resource-loading',
      field_name: 'messages',
      is_active: true,
    });

    const retrieveStyles = _.where(this.formConfig, {
      type: 'project-theme',
      field_name: 'styles',
      is_active: true,
    });
    let button =
      retrieveStyles.length > 0
        ? retrieveStyles[0].data.button_color
          ? retrieveStyles[0].data.button_color
          : '#90ee90'
        : '#90ee90';
    
    document.documentElement.style.setProperty('--teamButton', button);
    let fontStyle =
      retrieveStyles.length > 0
        ? retrieveStyles[0].data.font_style
          ? retrieveStyles[0].data.font_style
          : 'Roboto'
        : 'Roboto';
    document.documentElement.style.setProperty('--teamFont', fontStyle);
    let balanceConfig = _.where(this.formConfig, {
      type: 'resource-loading',
      field_name: 'balance-check'
    });

    let retrieveDecimalPlaces = _.where(this.formConfig, {
      type: 'billing-plan',
      field_name: 'decimal_places',
      is_active: true,
    });
    let decimal_value = (retrieveDecimalPlaces && retrieveDecimalPlaces.length > 0) ? (retrieveDecimalPlaces[0]) : null;
    this.decimalPlaces = decimal_value ? (decimal_value.disable_decimal ? 0 : (decimal_value.decimal_places ? decimal_value.decimal_places : 2)) : 2
  }

  /**
   * @description Routing back
   */
  routeBack(){
    if(this.projectData){
      let project_name = this.projectData?.project_name;
      let item_name = this.projectData?.item_name;
      let navigationUrl = `main/project-management/${this.projectId}/${this._utilityService.encodeURIComponent(project_name)}/${this.itemId}/ ${this._utilityService.encodeURIComponent(item_name)}/project-team/billing-plan`;
      this._router.navigateByUrl(navigationUrl);
    }
  }

  /**
   * @description Fetching Project Details
   * @returns 
   */
  getProjectDetails() {
    return new Promise((resolve,reject) => {
      this.subs.sink = this._resourceService.getProjectQuote(this.projectId,this.itemId).subscribe(
         (res:any) => {
          if(res && res['messType'] == 'S' && res['data'] && res['data'].length > 0) {
             this.projectData = res['data'][0] ? res['data'][0] : null;
          } 
          else{
            this.projectData = null;
          }
          resolve(true)
        },
        (err) => {
          this._toasterService.showError('Error in Fetching Project Details');
          console.log(err);
          resolve(null);
        }
      );
    });
  }


  getFixedBillingPlanQuoteDetails() {
    return new Promise((resolve,reject) => {
      this.subs.sink = this._resourceService.getFixedBillingPlanQuoteDetails(this.projectId,this.itemId, this.quoteId).subscribe(
         (res:any) => {
          if(res && res['messType']=='I')
          {
              this.initialLoading = true;
             
          }

          if(res && res['messType'] == 'S' && res['data']) 
          {
              console.log("Data", res['data'])
              resolve(res['data'])
          } 
          else{
            resolve([]);
          }
          
        },
        (err) => {
          this._toasterService.showError('Error in Fetching Project Details');
          console.log(err);
          resolve(null);
        }
      );
    });
  }

  /**
   * @description Fetching Initial Quote Load Details
   * @returns 
   */
  getInitialLoadDeliverableData() {
    let startDate = this.projectData?.planned_start_date;
    let endDate = this.projectData?.planned_end_date
    return new Promise((resolve,reject) => {
      this.subs.sink = this._resourceService.getInitialLoadQuoteData(startDate,endDate,this.quoteId).subscribe(
         (res:any) => {
          if(res && res['messType'] == 'S' && res['data'] && res['data'].length > 0) {
             resolve(res['data'])
          } 
          else{
            resolve([])
          }
        },
        (err) => {
          this._toasterService.showError('Error in Importing Quote data');
          console.log(err);
          resolve([]);
        }
      );
    });
  }

  /**
   * @description Formatting the initial load data
   */
  formatPositionDeliverableList(data){

      
      let deliverables = [];

    
      let deliverable_ids = _.uniq(_.pluck(data,"milestone_id"))

      for(let deliverable of deliverable_ids)
      {

          let result = _.where(data,{milestone_id: deliverable})

          if(result.length>0)
          {
              let planned_quantity =0;
              for(let d of result)
              {
                  planned_quantity += d['total_hrs']
              }
              let val = {
                "milestone_id": result[0]['milestone_id'],
                "name": result[0]['milestone_name'],
                "start_date": result[0]['milestone_start_date'],
                "end_date": result[0]['milestone_end_date'],
                "planned_quantity": planned_quantity,
                "position_list": result
              }

              deliverables.push(val);
          }


      }


      this.deliverableList = deliverables
      
  }

}
