<div class="resource-loading-styles" [ngStyle]="fixedBillingPlanEnabled ? {'background': 'none', 'padding': '0', height:'0'} : {}">
  <div class="loader-container" *ngIf="isComponentLoading" [ngStyle]="fixedBillingPlanEnabled ? {height: 'var(--dynamicResourceTableHeight)'} : {}">
    <div class="custom-loader"></div>
  </div>
  <div class="header" *ngIf="!isComponentLoading && !fixedBillingPlanEnabled">
    <div class="header-start">
      <div class="header-group">
        <div class="back-icon" (click)="routeBack()" [ngStyle]="{ 'pointer-events': (loaderObject.saveLoader || loaderObject.releaseLoader) ? 'none' : 'all' }">
          <mat-icon class="icn-class">navigate_before</mat-icon>
        </div>
        <span class="header-text">{{'resource_loading_plan_name' | checkLabel : this.formConfig: 'quote-card': 'Billing Plan'}}</span>
      </div>
      <div class="mode-icon">
        <span class="mode-class-txt">{{plannedViewEnabled ? 'Edit' : 'View'}} Mode</span>
      </div>
    </div>
    <div class="header-end">
      <!-- <div class="toggle-class">
                <mat-button-toggle-group #headerBtnToggleGroup="matButtonToggleGroup"
                [value]="toggleViewData"
                (change)="toggleSelectedButton(headerBtnToggleGroup.value)"
                [(ngModel)]="toggleViewData">
                <mat-button-toggle *ngFor="let item of toggleViewList" value="{{item.id}}"
                    aria-label="Text align left" class="project-toggle-button" tooltip="{{item.name}}">
                    <mat-icon *ngIf="item?.mat_icon == 'people'">
                        <svg width="17" height="8" viewBox="0 0 17 8" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M0.761719 7.42467V6.61183C0.761719 6.16828 0.99333 5.80395 1.45655 5.51883C1.91989 5.23383 2.52377 5.09133 3.26822 5.09133C3.39122 5.09133 3.51511 5.09517 3.63989 5.10283C3.76466 5.1105 3.89033 5.12545 4.01689 5.14767C3.887 5.35545 3.79061 5.57106 3.72772 5.7945C3.66494 6.01806 3.63355 6.24733 3.63355 6.48233V7.42467H0.761719ZM4.76172 7.42467V6.508C4.76172 6.19589 4.84933 5.91061 5.02455 5.65217C5.19977 5.39361 5.45233 5.16822 5.78222 4.976C6.11211 4.78367 6.50189 4.63944 6.95155 4.54333C7.40111 4.44711 7.89255 4.399 8.42589 4.399C8.96944 4.399 9.466 4.44711 9.91555 4.54333C10.3651 4.63944 10.7548 4.78367 11.0847 4.976C11.4147 5.16822 11.6656 5.39361 11.8374 5.65217C12.0092 5.91061 12.0951 6.19589 12.0951 6.508V7.42467H4.76172ZM13.2232 7.42467V6.48433C13.2232 6.23356 13.1936 5.99722 13.1342 5.77533C13.0748 5.55356 12.9857 5.34433 12.8669 5.14767C12.9977 5.12545 13.1227 5.1105 13.2419 5.10283C13.3611 5.09517 13.4788 5.09133 13.5951 5.09133C14.3395 5.09133 14.9423 5.23217 15.4034 5.51383C15.8645 5.79539 16.0951 6.16139 16.0951 6.61183V7.42467H13.2232ZM3.26639 4.44383C2.95216 4.44383 2.68372 4.33211 2.46105 4.10867C2.23839 3.88522 2.12705 3.61661 2.12705 3.30283C2.12705 2.98495 2.23883 2.71639 2.46239 2.49717C2.68583 2.27794 2.95444 2.16833 3.26822 2.16833C3.58611 2.16833 3.85572 2.27794 4.07705 2.49717C4.2985 2.71639 4.40922 2.98561 4.40922 3.30483C4.40922 3.61461 4.29966 3.88189 4.08055 4.10667C3.86155 4.33144 3.59016 4.44383 3.26639 4.44383ZM13.5951 4.44383C13.2839 4.44383 13.016 4.33144 12.7912 4.10667C12.5664 3.88189 12.4541 3.61461 12.4541 3.30483C12.4541 2.98561 12.5664 2.71639 12.7912 2.49717C13.016 2.27794 13.2843 2.16833 13.5962 2.16833C13.9177 2.16833 14.188 2.27794 14.4072 2.49717C14.6264 2.71639 14.7361 2.98495 14.7361 3.30283C14.7361 3.61661 14.6267 3.88522 14.4081 4.10867C14.1894 4.33211 13.9184 4.44383 13.5951 4.44383ZM8.43072 3.899C7.9505 3.899 7.54161 3.73072 7.20405 3.39417C6.8665 3.05772 6.69772 2.64911 6.69772 2.16833C6.69772 1.67789 6.86594 1.26683 7.20239 0.935166C7.53894 0.603389 7.94761 0.4375 8.42838 0.4375C8.91872 0.4375 9.32977 0.603167 9.66155 0.9345C9.99322 1.26572 10.1591 1.67622 10.1591 2.166C10.1591 2.64611 9.99344 3.055 9.66222 3.39267C9.33089 3.73022 8.92039 3.899 8.43072 3.899Z" fill="#515965"/>
                            </svg>                            
                    </mat-icon>
                    <mat-icon *ngIf="item?.mat_icon == 'employee'">
                        <svg width="13" height="14" viewBox="0 0 13 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M1.38266 13.487C1.04588 13.487 0.760824 13.3703 0.52749 13.137C0.294157 12.9036 0.17749 12.6186 0.17749 12.2818V5.35881C0.17749 5.02204 0.294157 4.73698 0.52749 4.50365C0.760824 4.27031 1.04588 4.15365 1.38266 4.15365H4.84416V1.82031C4.84416 1.54531 4.9421 1.30992 5.13799 1.11415C5.33377 0.918257 5.56916 0.820312 5.84416 0.820312H7.17749C7.45249 0.820312 7.68788 0.918257 7.88366 1.11415C8.07955 1.30992 8.17749 1.54531 8.17749 1.82031V4.15365H11.639C11.9758 4.15365 12.2608 4.27031 12.4942 4.50365C12.7275 4.73698 12.8442 5.02204 12.8442 5.35881V12.2818C12.8442 12.6186 12.7275 12.9036 12.4942 13.137C12.2608 13.3703 11.9758 13.487 11.639 13.487H1.38266ZM2.61332 10.9613H6.40832V10.7638C6.40832 10.5938 6.36116 10.4363 6.26682 10.2913C6.1726 10.1462 6.0411 10.0336 5.87232 9.95365C5.65432 9.85787 5.4336 9.78609 5.21016 9.73831C4.98671 9.69042 4.7536 9.66648 4.51082 9.66648C4.26805 9.66648 4.03493 9.69042 3.81149 9.73831C3.58805 9.78609 3.36732 9.85787 3.14932 9.95365C2.98055 10.0336 2.84905 10.1462 2.75482 10.2913C2.66049 10.4363 2.61332 10.5938 2.61332 10.7638V10.9613ZM7.84416 9.94848H10.5108V9.15365H7.84416V9.94848ZM4.50932 9.15365C4.75821 9.15365 4.97027 9.06653 5.14549 8.89231C5.32071 8.71809 5.40832 8.50654 5.40832 8.25765C5.40832 8.00887 5.32121 7.79687 5.14699 7.62165C4.97277 7.44642 4.76121 7.35881 4.51232 7.35881C4.26343 7.35881 4.05138 7.44592 3.87616 7.62015C3.70094 7.79437 3.61332 8.00587 3.61332 8.25465C3.61332 8.50353 3.70043 8.71559 3.87466 8.89081C4.04888 9.06604 4.26044 9.15365 4.50932 9.15365ZM7.84416 8.15365H10.5108V7.35881H7.84416V8.15365ZM5.84416 5.41015H7.17749V1.82031H5.84416V5.41015Z" fill="#F27A6C"/>
                            </svg>                            
                    </mat-icon>
                </mat-button-toggle>
                </mat-button-toggle-group>
            </div> -->
      <div class="toggle-class">
        <mat-button-toggle-group [value]="this.resource_type_id">
          <ng-container *ngFor="let types of resourceTypeConfig;let i = index;">
              <mat-button-toggle [value]="types.quote_resource_type" (change)="selectToggle($event,types)"
                  class="toggle-billing-btn" [ngClass]="{ 'btn-toggle-selected': this.resource_type_id == types.quote_resource_type }">
                  {{ types.description }}
              </mat-button-toggle>
          </ng-container>
      </mat-button-toggle-group>
      </div>
      <div class="edit-class" (click)="editPlanned()" *ngIf="serviceData && serviceData?.billing_plan_edit" [ngStyle]="{ 'border': plannedViewEnabled ? 'none' : '','background': plannedViewEnabled ? '#E8E9EE' : '' }">
        <!-- <svg
          width="18"
          height="18"
          viewBox="0 0 18 18"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M2 16H3.2615L13.498 5.7635L12.2365 4.502L2 14.7385V16ZM0.5 17.5V14.1155L13.6905 0.93075C13.8417 0.793416 14.0086 0.687333 14.1913 0.6125C14.3741 0.5375 14.5658 0.5 14.7663 0.5C14.9668 0.5 15.1609 0.535584 15.3488 0.60675C15.5367 0.677917 15.7032 0.791083 15.848 0.946249L17.0693 2.18275C17.2244 2.32758 17.335 2.49425 17.401 2.68275C17.467 2.87125 17.5 3.05975 17.5 3.24825C17.5 3.44942 17.4657 3.64133 17.397 3.824C17.3283 4.00683 17.2191 4.17383 17.0693 4.325L3.8845 17.5H0.5ZM12.8562 5.14375L12.2365 4.502L13.498 5.7635L12.8562 5.14375Z"
            fill="#7D838B"
          />
        </svg> -->
        <svg width="12" height="14" viewBox="0 0 12 14" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M2.276 9.66542L9.03733 2.90409L8.09467 1.96142L1.33333 8.72275V9.66542H2.276ZM2.82867 10.9988H0V8.17009L7.62333 0.546754C7.74835 0.421773 7.91789 0.351562 8.09467 0.351562C8.27144 0.351562 8.44098 0.421773 8.566 0.546754L10.452 2.43275C10.577 2.55777 10.6472 2.72731 10.6472 2.90409C10.6472 3.08086 10.577 3.2504 10.452 3.37542L2.82867 10.9988ZM0 12.3321H12V13.6654H0V12.3321Z" 
          [attr.fill]="plannedViewEnabled ? '#B9C0CA' : '#45546E'"/>
        </svg>          
        <span class="edit-text" [ngStyle]="{ 'color': plannedViewEnabled ? '#B9C0CA' : '#45546E'}">Edit</span>
      </div>
      <div class="history-class" (click)="openHistoryDialog()">
        <svg
          width="18"
          height="18"
          viewBox="0 0 18 18"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M8.98078 17.5C6.81544 17.5 4.92828 16.7872 3.31928 15.3615C1.71028 13.9358 0.784027 12.1487 0.540527 10H2.07128C2.32378 11.727 3.10103 13.1587 4.40303 14.2952C5.70486 15.4318 7.23078 16 8.98078 16C10.9308 16 12.5849 15.3208 13.9433 13.9625C15.3016 12.6042 15.9808 10.95 15.9808 9C15.9808 7.05 15.3016 5.39583 13.9433 4.0375C12.5849 2.67917 10.9308 2 8.98078 2C7.88844 2 6.86478 2.24267 5.90978 2.728C4.95461 3.21317 4.13211 3.88075 3.44228 4.73075H6.05778V6.23075H0.980777V1.15375H2.48078V3.523C3.29228 2.56533 4.26411 1.82208 5.39628 1.29325C6.52828 0.764416 7.72311 0.5 8.98078 0.5C10.1603 0.5 11.2654 0.723084 12.2963 1.16925C13.3269 1.61542 14.2256 2.22183 14.9923 2.9885C15.7589 3.75517 16.3654 4.65392 16.8115 5.68475C17.2577 6.71542 17.4808 7.8205 17.4808 9C17.4808 10.1795 17.2577 11.2846 16.8115 12.3152C16.3654 13.3461 15.7589 14.2448 14.9923 15.0115C14.2256 15.7782 13.3269 16.3846 12.2963 16.8307C11.2654 17.2769 10.1603 17.5 8.98078 17.5ZM11.9828 13.027L8.25978 9.30375V4H9.75953V8.69625L13.0365 11.973L11.9828 13.027Z"
            fill="#7D838B"
          />
        </svg>
      </div>
    </div>
  </div>
  <div class="card data-card" *ngIf="!isComponentLoading">
    <div class="content-header-class" *ngIf="!fixedBillingPlanEnabled">
      <div class="row content-long-text">
        <div class="content-id">
          <span>#{{ quoteData?.quote_id ? quoteData.quote_id : "-" }}</span>
          <div class="content-button" *ngIf="statusId == 2">
                <div class="circle"></div>
                <span class="active-text">Active</span>
          </div>
          <div class="content-button-save" *ngIf="statusId == 1">
            <div class="circle-save"></div>
            <span class="save-text">Saved</span>
      </div>
        </div>
        <span class="content-long-header">{{
          quoteData?.quote_name ? quoteData.quote_name : "-"
        }}</span>
      </div>
      <mat-divider [vertical]="true"></mat-divider>
      <div class="row content-text">
        <div class="icon-class">
          <svg
            width="19"
            height="18"
            viewBox="0 0 19 18"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M2.30775 17.5C1.80908 17.5 1.38308 17.3234 1.02975 16.9703C0.676583 16.6169 0.5 16.1909 0.5 15.6923V2.30775C0.5 1.80908 0.676583 1.38308 1.02975 1.02975C1.38308 0.676583 1.80908 0.5 2.30775 0.5H15.6923C16.1909 0.5 16.6169 0.676583 16.9703 1.02975C17.3234 1.38308 17.5 1.80908 17.5 2.30775V5.029H16V2.30775C16 2.21792 15.9712 2.14417 15.9135 2.0865C15.8558 2.02883 15.7821 2 15.6923 2H2.30775C2.21792 2 2.14417 2.02883 2.0865 2.0865C2.02883 2.14417 2 2.21792 2 2.30775V15.6923C2 15.7821 2.02883 15.8558 2.0865 15.9135C2.14417 15.9712 2.21792 16 2.30775 16H15.6923C15.7821 16 15.8558 15.9712 15.9135 15.9135C15.9712 15.8558 16 15.7821 16 15.6923V12.971H17.5V15.6923C17.5 16.1909 17.3234 16.6169 16.9703 16.9703C16.6169 17.3234 16.1909 17.5 15.6923 17.5H2.30775ZM10.3077 13.5C9.80908 13.5 9.38308 13.3234 9.02975 12.9703C8.67658 12.6169 8.5 12.1909 8.5 11.6923V6.30775C8.5 5.80908 8.67658 5.38308 9.02975 5.02975C9.38308 4.67658 9.80908 4.5 10.3077 4.5H16.6923C17.1909 4.5 17.6169 4.67658 17.9703 5.02975C18.3234 5.38308 18.5 5.80908 18.5 6.30775V11.6923C18.5 12.1909 18.3234 12.6169 17.9703 12.9703C17.6169 13.3234 17.1909 13.5 16.6923 13.5H10.3077ZM16.6923 12C16.7821 12 16.8558 11.9712 16.9135 11.9135C16.9712 11.8558 17 11.7821 17 11.6923V6.30775C17 6.21792 16.9712 6.14417 16.9135 6.0865C16.8558 6.02883 16.7821 6 16.6923 6H10.3077C10.2179 6 10.1442 6.02883 10.0865 6.0865C10.0288 6.14417 10 6.21792 10 6.30775V11.6923C10 11.7821 10.0288 11.8558 10.0865 11.9135C10.1442 11.9712 10.2179 12 10.3077 12H16.6923ZM13 10.5C13.4167 10.5 13.7708 10.3542 14.0625 10.0625C14.3542 9.77083 14.5 9.41667 14.5 9C14.5 8.58333 14.3542 8.22917 14.0625 7.9375C13.7708 7.64583 13.4167 7.5 13 7.5C12.5833 7.5 12.2292 7.64583 11.9375 7.9375C11.6458 8.22917 11.5 8.58333 11.5 9C11.5 9.41667 11.6458 9.77083 11.9375 10.0625C12.2292 10.3542 12.5833 10.5 13 10.5Z"
              fill="#F27A6C"
            />
          </svg>
        </div>
        <div class="text-content-class">
          <span class="content-head">Quote Value</span>
          <span class="content-value">
            {{ isFinancialValuesHidden ? '****' : (quoteData?.currency ? quoteData.currency : '-') }}
            {{ isFinancialValuesHidden ? '****' : (quoteData?.quote_value ? (quoteData.quote_value | decimal :
            quote_decimalPlaces) : 0) }}
          </span>
        </div>
      </div>
      <!-- <mat-divider [vertical]="true"></mat-divider>
      <div class="row content-text">
        <div class="icon-class">
          <svg
            width="20"
            height="20"
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M10 19.5C8.68583 19.5 7.45083 19.2507 6.295 18.752C5.13917 18.2533 4.13375 17.5766 3.27875 16.7218C2.42375 15.8669 1.74692 14.8617 1.24825 13.706C0.749417 12.5503 0.5 11.3156 0.5 10.0017C0.5 8.68775 0.749333 7.45267 1.248 6.2965C1.74667 5.14033 2.42342 4.13467 3.27825 3.2795C4.13308 2.42433 5.13833 1.74725 6.294 1.24825C7.44967 0.749417 8.68442 0.5 9.99825 0.5C11.3123 0.5 12.5473 0.749417 13.7035 1.24825C14.8597 1.74692 15.8653 2.42375 16.7205 3.27875C17.5757 4.13375 18.2528 5.13917 18.7518 6.295C19.2506 7.45083 19.5 8.68583 19.5 10C19.5 10.45 19.4718 10.8917 19.4153 11.325C19.3589 11.7583 19.2692 12.1833 19.146 12.6C18.964 12.4103 18.7589 12.2478 18.5308 12.1125C18.3026 11.9773 18.0533 11.8827 17.7828 11.8287C17.8584 11.5339 17.9135 11.2349 17.948 10.9318C17.9827 10.6286 18 10.318 18 10C18 7.76667 17.225 5.875 15.675 4.325C14.125 2.775 12.2333 2 10 2C7.76667 2 5.875 2.775 4.325 4.325C2.775 5.875 2 7.76667 2 10C2 12.2333 2.775 14.125 4.325 15.675C5.875 17.225 7.76667 18 10 18C10.8757 18 11.7089 17.8682 12.4998 17.6047C13.2906 17.3412 14.0176 16.9723 14.6807 16.498C14.8359 16.7045 15.0177 16.8904 15.226 17.0557C15.4343 17.2211 15.6603 17.3525 15.9038 17.45C15.0923 18.0948 14.188 18.5977 13.191 18.9585C12.194 19.3195 11.1303 19.5 10 19.5ZM17.1063 15.75C16.8264 15.75 16.5898 15.6534 16.3962 15.4603C16.2026 15.2669 16.1058 15.0303 16.1058 14.7505C16.1058 14.4707 16.2023 14.234 16.3955 14.0405C16.5888 13.8468 16.8254 13.75 17.1053 13.75C17.3851 13.75 17.6217 13.8466 17.8153 14.0397C18.0089 14.2331 18.1057 14.4697 18.1057 14.7495C18.1057 15.0293 18.0092 15.266 17.816 15.4595C17.6227 15.6532 17.3861 15.75 17.1063 15.75ZM13.473 14.527L9.25 10.3038V5H10.75V9.69625L14.527 13.473L13.473 14.527Z"
              fill="#F27A6C"
            />
          </svg>
        </div>
        <div class="text-content-class">
          <span class="content-head">Planned Hours</span>
          <span class="content-value">{{quoteData?.planned_quantity ?? '-'}} Hrs</span>
        </div>
      </div> -->
      <!-- <mat-divider [vertical]="true"></mat-divider>
      <div class="row content-text">
        <div class="icon-class">
          <svg
            width="16"
            height="16"
            viewBox="0 0 16 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M8 7.69141C7.0375 7.69141 6.21358 7.34874 5.52825 6.66341C4.84275 5.97791 4.5 5.15391 4.5 4.19141C4.5 3.22891 4.84275 2.40499 5.52825 1.71966C6.21358 1.03416 7.0375 0.691406 8 0.691406C8.9625 0.691406 9.78642 1.03416 10.4718 1.71966C11.1573 2.40499 11.5 3.22891 11.5 4.19141C11.5 5.15391 11.1573 5.97791 10.4718 6.66341C9.78642 7.34874 8.9625 7.69141 8 7.69141ZM0.5 15.3069V13.0837C0.5 12.594 0.633 12.1405 0.899 11.7232C1.165 11.3058 1.5205 10.985 1.9655 10.7607C2.95383 10.2762 3.95092 9.91274 4.95675 9.67041C5.96258 9.42807 6.977 9.30691 8 9.30691C9.023 9.30691 10.0374 9.42807 11.0433 9.67041C12.0491 9.91274 13.0462 10.2762 14.0345 10.7607C14.4795 10.985 14.835 11.3058 15.101 11.7232C15.367 12.1405 15.5 12.594 15.5 13.0837V15.3069H0.5ZM2 13.8069H14V13.0837C14 12.8812 13.9413 12.6937 13.824 12.5212C13.7067 12.3488 13.5474 12.2082 13.3462 12.0992C12.4846 11.6748 11.6061 11.3533 10.7107 11.1347C9.81525 10.9162 8.91167 10.8069 8 10.8069C7.08833 10.8069 6.18475 10.9162 5.28925 11.1347C4.39392 11.3533 3.51542 11.6748 2.65375 12.0992C2.45258 12.2082 2.29333 12.3488 2.176 12.5212C2.05867 12.6937 2 12.8812 2 13.0837V13.8069ZM8 6.19141C8.55 6.19141 9.02083 5.99557 9.4125 5.60391C9.80417 5.21224 10 4.74141 10 4.19141C10 3.64141 9.80417 3.17057 9.4125 2.77891C9.02083 2.38724 8.55 2.19141 8 2.19141C7.45 2.19141 6.97917 2.38724 6.5875 2.77891C6.19583 3.17057 6 3.64141 6 4.19141C6 4.74141 6.19583 5.21224 6.5875 5.60391C6.97917 5.99557 7.45 6.19141 8 6.19141Z"
              fill="#F27A6C"
            />
          </svg>
        </div>
        <div class="text-content-class">
          <span class="content-head">No of Resources</span>
          <span class="content-value">3</span>
        </div>
      </div> -->
      <mat-divider [vertical]="true"></mat-divider>
      <div class="row content-text">
        <div class="icon-class">
          <svg
            width="20"
            height="21"
            viewBox="0 0 20 21"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M2 6.80581H16V4.30581C16 4.22881 15.9679 4.15831 15.9038 4.09431C15.8398 4.03014 15.7693 3.99806 15.6923 3.99806H2.30775C2.23075 3.99806 2.16025 4.03014 2.09625 4.09431C2.03208 4.15831 2 4.22881 2 4.30581V6.80581ZM2.30775 19.4981C1.80258 19.4981 1.375 19.3231 1.025 18.9731C0.675 18.6231 0.5 18.1955 0.5 17.6903V4.30581C0.5 3.80065 0.675 3.37306 1.025 3.02306C1.375 2.67306 1.80258 2.49806 2.30775 2.49806H3.69225V0.382812H5.23075V2.49806H12.8078V0.382812H14.3078V2.49806H15.6923C16.1974 2.49806 16.625 2.67306 16.975 3.02306C17.325 3.37306 17.5 3.80065 17.5 4.30581V9.76931C17.2602 9.66415 17.0153 9.57915 16.7653 9.51431C16.5153 9.44965 16.2602 9.39873 16 9.36156V8.30581H2V17.6903C2 17.7673 2.03208 17.8378 2.09625 17.9018C2.16025 17.966 2.23075 17.9981 2.30775 17.9981H8.80975C8.89425 18.2749 8.9965 18.5367 9.1165 18.7836C9.23633 19.0304 9.3725 19.2686 9.525 19.4981H2.30775ZM15.1923 20.4981C13.9436 20.4981 12.8814 20.0602 12.0058 19.1846C11.1301 18.3089 10.6923 17.2467 10.6923 15.9981C10.6923 14.7494 11.1301 13.6872 12.0058 12.8116C12.8814 11.9359 13.9436 11.4981 15.1923 11.4981C16.4411 11.4981 17.5033 11.9359 18.3788 12.8116C19.2544 13.6872 19.6923 14.7494 19.6923 15.9981C19.6923 17.2467 19.2544 18.3089 18.3788 19.1846C17.5033 20.0602 16.4411 20.4981 15.1923 20.4981ZM16.8578 18.2866L17.4808 17.6636L15.6348 15.8173V13.0558H14.75V16.1788L16.8578 18.2866Z"
              fill="#F27A6C"
            />
          </svg>
        </div>
        <div class="text-content-class">
          <span class="content-head">Planned Quantity</span>
          <span class="content-value">{{
            (quoteData?.planned_quantity ? quoteData.planned_quantity : 0) | decimal : (resource_config?.allow_decimals ? decimalPlaces : '0') 
          }} {{ resource_config?.unit_display ? resource_config?.unit_display : '' }}</span>
        </div>
      </div>
      <mat-divider [vertical]="true"></mat-divider>
      <div class="row content-text">
        <div class="icon-class">
          <svg
            width="18"
            height="20"
            viewBox="0 0 18 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M9 15.6326C7.96417 15.6326 6.96675 15.8105 6.00775 16.1663C5.04875 16.5221 4.16475 17.0622 3.35575 17.7866V17.9403C3.38142 17.9596 3.40708 17.9741 3.43275 17.9836C3.45825 17.9932 3.48708 17.9981 3.51925 17.9981H14.4615C14.4937 17.9981 14.5209 17.9932 14.5433 17.9836C14.5658 17.9741 14.5898 17.9596 14.6155 17.9403V17.7866C13.8193 17.0622 12.9449 16.5221 11.9923 16.1663C11.0398 15.8105 10.0423 15.6326 9 15.6326ZM2 16.9828C2.9 16.0995 3.94583 15.4036 5.1375 14.8953C6.32917 14.387 7.61667 14.1328 9 14.1328C10.3833 14.1328 11.6708 14.387 12.8625 14.8953C14.0542 15.4036 15.1 16.0995 16 16.9828V4.30581C16 4.22881 15.9679 4.15831 15.9038 4.09431C15.8398 4.03014 15.7692 3.99806 15.6923 3.99806H2.30775C2.23075 3.99806 2.16025 4.03014 2.09625 4.09431C2.03208 4.15831 2 4.22881 2 4.30581V16.9828ZM9 11.7481C8.0975 11.7481 7.33017 11.4321 6.698 10.8001C6.066 10.1679 5.75 9.40056 5.75 8.49806C5.75 7.59556 6.066 6.82823 6.698 6.19606C7.33017 5.56406 8.0975 5.24806 9 5.24806C9.9025 5.24806 10.6698 5.56406 11.302 6.19606C11.934 6.82823 12.25 7.59556 12.25 8.49806C12.25 9.40056 11.934 10.1679 11.302 10.8001C10.6698 11.4321 9.9025 11.7481 9 11.7481ZM9 10.2481C9.48083 10.2481 9.89267 10.0766 10.2355 9.73356C10.5785 9.39073 10.75 8.9789 10.75 8.49806C10.75 8.01723 10.5785 7.6054 10.2355 7.26256C9.89267 6.91956 9.48083 6.74806 9 6.74806C8.51917 6.74806 8.10733 6.91956 7.7645 7.26256C7.4215 7.6054 7.25 8.01723 7.25 8.49806C7.25 8.9789 7.4215 9.39073 7.7645 9.73356C8.10733 10.0766 8.51917 10.2481 9 10.2481ZM2.30775 19.4981C1.80258 19.4981 1.375 19.3231 1.025 18.9731C0.675 18.6231 0.5 18.1955 0.5 17.6903V4.30581C0.5 3.80065 0.675 3.37306 1.025 3.02306C1.375 2.67306 1.80258 2.49806 2.30775 2.49806H3.69225V0.382812H5.23075V2.49806H12.8077V0.382812H14.3077V2.49806H15.6923C16.1974 2.49806 16.625 2.67306 16.975 3.02306C17.325 3.37306 17.5 3.80065 17.5 4.30581V17.6903C17.5 18.1955 17.325 18.6231 16.975 18.9731C16.625 19.3231 16.1974 19.4981 15.6923 19.4981H2.30775Z"
              fill="#F27A6C"
            />
          </svg>
        </div>
        <div class="text-content-class">
          <span class="content-head">Remaining Quantity</span>
          <span class="content-value"
            >{{
              getRemainingBalance() | decimal : (resource_config?.allow_decimals ? decimalPlaces : '0')
            }}
            {{ resource_config?.unit_display ? resource_config?.unit_display : '' }}</span
          >
        </div>
      </div>
      <mat-divider [vertical]="true"></mat-divider>
      <div class="row content-text">
        <div class="icon-class">
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M6.95375 9.35485L9.17125 7.12209L7.44425 5.37959L6.31525 6.50859L5.2615 5.45484L6.375 4.32584L4.8365 2.78734L2.60375 5.02009L6.95375 9.35485ZM14.9713 17.3873L17.2038 15.1546L15.6653 13.6161L14.5365 14.7296L13.4828 13.6758L14.5963 12.5471L12.8635 10.8296L10.646 13.0471L14.9713 17.3873ZM5.202 18.5008H1.5V14.7988L5.88475 10.4143L0.5 5.02009L4.8365 0.683594L10.2558 6.09309L14.377 1.95659C14.5322 1.80143 14.7027 1.68668 14.8885 1.61234C15.0743 1.53801 15.2686 1.50084 15.4713 1.50084C15.6738 1.50084 15.8679 1.53801 16.0538 1.61234C16.2398 1.68668 16.4103 1.80143 16.5653 1.95659L18.0443 3.47984C18.1994 3.63484 18.3125 3.80534 18.3835 3.99134C18.4547 4.17718 18.4903 4.37134 18.4903 4.57384C18.4903 4.77651 18.4547 4.96659 18.3835 5.14409C18.3125 5.32159 18.1994 5.48793 18.0443 5.64309L13.9423 9.77009L19.3173 15.1643L14.9808 19.5008L9.5865 14.1161L5.202 18.5008ZM3 17.0008H4.5635L14.373 7.20659L12.7943 5.62784L3 15.4373V17.0008ZM13.5963 6.42009L12.7943 5.62784L14.373 7.20659L13.5963 6.42009Z" fill="#F27A6C"/>
            </svg>            
        </div>
        <div class="text-content-class">
          <span class="content-head">Units</span>
          <span class="content-value"
            >{{ resource_config?.unit ? resource_config?.unit : '-' }}</span
          >
        </div>
      </div>
    </div>

    <!---------------------------Edit Mode Planned------------------------------------>
    <div class="planned-table" *ngIf="plannedViewEnabled">
      <div class="table-container">
        <table>
          <thead>
            <tr>
              <th class="fixed-col-header">{{ resource_config?.header_name ?  resource_config?.header_name  : 'Positions'}}</th>

              <!-- <th *ngFor="let month of monthList" class="scroll-col-header">{{ month.name }}</th> -->
              <th
                *ngFor="let month of monthList; let i = index"
                class="scroll-col-header"
              >
                M{{ month.id }} - {{ month.month }} {{ month.year }}
              </th>
              <th class="scroll-col-header carry-header">Carry Forward</th>
              <th class="fixed-col-right-header">
                <div class="balance-class">
                  <span class="balance-date-class"
                    >M{{ monthList.length }}</span
                  >
                  <span>Balance</span>
                </div>
              </th>
            </tr>
          </thead>
          <tbody>
            <!-- <tr class="currency-row">
              <td class="fixed-col"></td>
              <td *ngFor="let month of monthList" class="scroll-col"></td>
              <td class="scroll-col header">Carry Forward</td>
              <td class="fixed-col-right"></td>
            </tr> -->
            <tr *ngFor="let position of positionList">
              <!-- <td class="fixed-col">{{ position.position_name }}</td> -->
              <td class="fixed-col">
                <div class="position-class">
                  <div class="position-id-class">
                    <span>#{{ position.position_id }}</span>
                    <div class="end-class" *ngIf="resource_config?.display_allocation_count == 1">
                      
                      <span
                        ><span class="active-count">{{
                          position?.active_employees
                        }}</span
                        >/{{ position?.total_employees }}</span
                      >
                      <!--Commenting For Quick Fill Option-->
                      <!-- <span
                      ><svg
                        width="12"
                        height="14"
                        viewBox="0 0 12 14"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M5.33336 8.66536L1.38336 8.16536C1.10559 8.13203 0.92503 7.98203 0.841696 7.71536C0.758363 7.4487 0.816696 7.22092 1.0167 7.03203L7.83336 0.498698C7.88892 0.443142 7.95559 0.401476 8.03336 0.373698C8.11114 0.34592 8.2167 0.332031 8.35003 0.332031C8.57225 0.332031 8.7417 0.426476 8.85836 0.615365C8.97503 0.804253 8.97781 0.998698 8.8667 1.1987L6.6667 5.33203L10.6167 5.83203C10.8945 5.86536 11.075 6.01536 11.1584 6.28203C11.2417 6.5487 11.1834 6.77647 10.9834 6.96536L4.1667 13.4987C4.11114 13.5543 4.04447 13.5959 3.9667 13.6237C3.88892 13.6515 3.78336 13.6654 3.65003 13.6654C3.42781 13.6654 3.25836 13.5709 3.1417 13.382C3.02503 13.1931 3.02225 12.9987 3.13336 12.7987L5.33336 8.66536Z"
                          fill="#FFBD3D"
                        />
                      </svg>
                    </span> -->
                    </div>
                  </div>
                  <div class="position-name-class">
                    <span [innerHTML]="(resource_config?.svg || '') | safeHtml" tooltip ="{{resource_config?.description ? resource_config?.description : '-'}}"></span>
                    <span class="position-name-text" tooltip="{{position.position_name ? position.position_name : '-'}}">{{
                      position.position_name ? position.position_name : '-'
                    }}</span>
                    <!-- <span
                      ><svg
                        width="12"
                        height="14"
                        viewBox="0 0 12 14"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M5.33336 8.66536L1.38336 8.16536C1.10559 8.13203 0.92503 7.98203 0.841696 7.71536C0.758363 7.4487 0.816696 7.22092 1.0167 7.03203L7.83336 0.498698C7.88892 0.443142 7.95559 0.401476 8.03336 0.373698C8.11114 0.34592 8.2167 0.332031 8.35003 0.332031C8.57225 0.332031 8.7417 0.426476 8.85836 0.615365C8.97503 0.804253 8.97781 0.998698 8.8667 1.1987L6.6667 5.33203L10.6167 5.83203C10.8945 5.86536 11.075 6.01536 11.1584 6.28203C11.2417 6.5487 11.1834 6.77647 10.9834 6.96536L4.1667 13.4987C4.11114 13.5543 4.04447 13.5959 3.9667 13.6237C3.88892 13.6515 3.78336 13.6654 3.65003 13.6654C3.42781 13.6654 3.25836 13.5709 3.1417 13.382C3.02503 13.1931 3.02225 12.9987 3.13336 12.7987L5.33336 8.66536Z"
                          fill="#FFBD3D"
                        />
                      </svg>
                    </span> -->
                          <div class="currency-text-class" *ngIf="this.area!='billing-advice'">{{position?.rate_currency_code}} <span class="overflow-class" tooltip="{{isFinancialValuesHidden ? '****' :position.rate_per_unit ? position.rate_per_unit : 0}}">{{isFinancialValuesHidden ? '****' : position?.rate_per_unit }}</span></div>
                  </div>
                </div>
              </td>
              <!-- <td
                class="fixed-col-unit"
              >
                {{ position.unit_name ? position.unit_name : '-' }}
              </td> -->
              <td
                *ngFor="let month of monthList"
                class="scroll-col"
                [ngClass]="{ 'disabled-cell': getCellValue(position.position_id, month, 'disabled') }"
                [attr.contenteditable]="!getCellValue(position.position_id, month, 'disabled')"
                (blur)="
                  onBlur($event, position.position_id, month, 'plannedVsActual')
                "
                (keyup.enter)="
                  onEnter(
                    $event,
                    position.position_id,
                    month,
                    'plannedVsActual'
                  )
                "
                (keypress)="restrictInput($event)"
                (paste)="handlePaste($event)"
              >
                {{
                  getCellValue(position.position_id, month, "plannedVsActual")
                    | decimal : (resource_config?.allow_decimals ? decimalPlaces : '0') : false
                }}
              </td>
              <td class="scroll-col currency" style="cursor: pointer;"
              [matMenuTriggerFor]="viewCarryForwardMenu"
              (click)="selectcarryForward(position.position_id)">
                {{ (position.carry_forward ? position.carry_forward : 0) | decimal : (resource_config?.allow_decimals ? decimalPlaces : '0') }}
              </td>
              <td
                class="fixed-col-right"
                [ngStyle]="{
                  'color': getColor(position.balance)
                }"
                style="font-weight: 500;"
              >
                {{ position.balance | decimal : (resource_config?.allow_decimals ? decimalPlaces : '0') }}
              </td>
            </tr>
            <tr class="bottom-row-fixed bills">
              <td class="fixed-col">
               <span class="total-text">Total in {{resource_config?.unit ? resource_config?.unit : '-'}}</span>
              </td>
              <!-- <td
                class="fixed-col-unit"
              >
                {{ unit ? unit : 'Hour' }}
              </td> -->
              <!-- <td *ngFor="let month of position.monthList" class="scroll-col" contenteditable="true">0</td> -->
              <ng-container *ngFor="let month of monthList">
                <td class="scroll-col">
                  <div class="total-class" *ngIf="!getDisabledStatus(month['index']+1, month['year'])">
                    <span>{{ (getMonthWiseData(month['index']+1, month['year'],'planned') | decimal : (resource_config?.allow_decimals ? decimalPlaces : '0'))}}</span>
                  </div>
                  <div class="total-class" *ngIf="getDisabledStatus(month['index']+1, month['year'])">
                    <span>-</span>
                  </div>
                  
                </td>
              </ng-container>
              <td class="scroll-col currency"></td>
              <td class="fixed-col-right"></td>
            </tr>
            <!-- <tr class="bottom-row-fixed bills">
              <td class="fixed-col">
               <span class="total-text">Milestone Value</span>
              </td>
              <td
                class="fixed-col-unit"
              >
              -
              </td>
              <td *ngFor="let month of position.monthList" class="scroll-col" contenteditable="true">0</td>
              <ng-container *ngFor="let month of monthList">
                <td class="scroll-col">
                  <div class="total-class" *ngIf="!getDisabledStatus(month['index']+1, month['year'])">
                    <span>{{ (getMonthWiseData(month['index']+1, month['year'],'planned') | decimal : decimalPlaces)}} Hrs</span>
                    <span>{{quoteData?.currency}} {{ (getMilestoneValue(month['index']+1, month['year'],'planned') | decimal : decimalPlaces )}}</span>
                  </div>
                  <div class="total-class" *ngIf="getDisabledStatus(month['index']+1, month['year'])">
                    <span>{{ (getMonthWiseData(month['index']+1, month['year'],'planned') | decimal : decimalPlaces)}} Hrs</span>
                    <span>-</span>
                  </div>
                </td>
              </ng-container>
              <td class="scroll-col currency"></td>
              <td class="fixed-col-right"></td>
            </tr> -->
            <tr class="bottom-row-fixed">
              <td class="fixed-col">
               <span class="total-text">{{resource_config?.total_display_value ? resource_config?.total_display_value : 'Total Value'}}</span>
              </td>
              <!-- <td
                class="fixed-col-unit"
              >
              -
              </td> -->
              <!-- <td *ngFor="let month of position.monthList" class="scroll-col" contenteditable="true">0</td> -->
              <ng-container *ngFor="let month of monthList">
                <td class="scroll-col">
                  <div class="total-class" *ngIf="!getDisabledStatus(month['index']+1, month['year'])">
                    <!-- <span>{{ (getMonthWiseData(month['index']+1, month['year'],'planned') | decimal : decimalPlaces)}} Hrs</span> -->
                    <span>{{quoteData?.currency}} {{isFinancialValuesHidden ? '****' : (getMilestoneValue(month['index']+1, month['year'],'planned',true) | decimal : quote_decimalPlaces )}}</span>
                  </div>
                  <div class="total-class" *ngIf="getDisabledStatus(month['index']+1, month['year'])">
                    <!-- <span>{{ (getMonthWiseData(month['index']+1, month['year'],'planned',true) | decimal : decimalPlaces)}} Hrs</span> -->
                    <span>-</span>
                  </div>
                </td>
              </ng-container>
              <td class="scroll-col currency"></td>
              <td class="fixed-col-right"></td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!---------------------------Display Mode Planned Vs Actual------------------------->
    <div class="planned-table" *ngIf="!plannedViewEnabled">
      <div class="table-container">
        <table>
          <thead>
            <tr>
              <th class="fixed-col-header">{{ resource_config?.header_name ?  resource_config?.header_name  : 'Positions'}}</th>
              
              <!-- <th *ngFor="let month of monthList" class="scroll-col-header">{{ month.name }}</th> -->
              <th
                *ngFor="let month of monthList; let i = index"
                [attr.colspan]="i === monthList.length - 1 ? 2 : 2"
                class="scroll-col-header"
              >
                M{{ month.id }} - {{ month.month }} {{ month.year }}
              </th>
              <th class="scroll-col-header carry-header">Carry Forward</th>
              <th class="fixed-col-right-header">
                <div class="balance-class">
                  <span class="balance-date-class"
                    >M{{ monthList.length }}</span
                  >
                  <span>Balance</span>
                </div>
              </th>
            </tr>
          </thead>
          <tbody>
            <tr class="currency-row">
              <td class="fixed-col-currency-left"></td>
              <!-- <td class="fixed-col-currency-unit"></td> -->
              <!-- <td *ngFor="let month of monthList" class="scroll-col"></td> -->
              <ng-container *ngFor="let month of monthList">
                <td class="scroll-col planned">Planned</td>
                <td class="scroll-col actual">Actual</td>
              </ng-container>
              <td class="scroll-col right"></td>
              <td class="fixed-col-currency-right"></td>
            </tr>
            <tr *ngFor="let position of positionList">
              <!-- <td class="fixed-col">{{ position.position_name }}</td> -->
              <td class="fixed-col">
                <div class="position-class">
                  <div class="position-id-class">
                    <span>#{{ position.position_id }}</span>
                    <div class="end-class" *ngIf="resource_config?.display_allocation_count == 1">
                      
                      <span
                        ><span class="active-count">{{
                          position?.active_employees
                        }}</span
                        >/{{ position?.total_employees }}</span
                      >
                      <!--Commenting For Quick Fill Option-->
                      <!-- <span
                      ><svg
                        width="12"
                        height="14"
                        viewBox="0 0 12 14"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M5.33336 8.66536L1.38336 8.16536C1.10559 8.13203 0.92503 7.98203 0.841696 7.71536C0.758363 7.4487 0.816696 7.22092 1.0167 7.03203L7.83336 0.498698C7.88892 0.443142 7.95559 0.401476 8.03336 0.373698C8.11114 0.34592 8.2167 0.332031 8.35003 0.332031C8.57225 0.332031 8.7417 0.426476 8.85836 0.615365C8.97503 0.804253 8.97781 0.998698 8.8667 1.1987L6.6667 5.33203L10.6167 5.83203C10.8945 5.86536 11.075 6.01536 11.1584 6.28203C11.2417 6.5487 11.1834 6.77647 10.9834 6.96536L4.1667 13.4987C4.11114 13.5543 4.04447 13.5959 3.9667 13.6237C3.88892 13.6515 3.78336 13.6654 3.65003 13.6654C3.42781 13.6654 3.25836 13.5709 3.1417 13.382C3.02503 13.1931 3.02225 12.9987 3.13336 12.7987L5.33336 8.66536Z"
                          fill="#FFBD3D"
                        />
                      </svg>
                    </span> -->
                    </div>
                  </div>
                  <div class="position-name-class">
                    <span class="svg-icn-class" [innerHTML]="(resource_config?.svg || '') | safeHtml" tooltip ="{{resource_config?.description ? resource_config?.description : '-'}}"></span>
                    <span class="position-name-text" tooltip="{{position.position_name ? position.position_name : '-'}}">{{
                      position?.position_name
                    }}</span>
                    <!-- <span
                      ><svg
                        width="12"
                        height="14"
                        viewBox="0 0 12 14"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M5.33336 8.66536L1.38336 8.16536C1.10559 8.13203 0.92503 7.98203 0.841696 7.71536C0.758363 7.4487 0.816696 7.22092 1.0167 7.03203L7.83336 0.498698C7.88892 0.443142 7.95559 0.401476 8.03336 0.373698C8.11114 0.34592 8.2167 0.332031 8.35003 0.332031C8.57225 0.332031 8.7417 0.426476 8.85836 0.615365C8.97503 0.804253 8.97781 0.998698 8.8667 1.1987L6.6667 5.33203L10.6167 5.83203C10.8945 5.86536 11.075 6.01536 11.1584 6.28203C11.2417 6.5487 11.1834 6.77647 10.9834 6.96536L4.1667 13.4987C4.11114 13.5543 4.04447 13.5959 3.9667 13.6237C3.88892 13.6515 3.78336 13.6654 3.65003 13.6654C3.42781 13.6654 3.25836 13.5709 3.1417 13.382C3.02503 13.1931 3.02225 12.9987 3.13336 12.7987L5.33336 8.66536Z"
                          fill="#FFBD3D"
                        />
                      </svg>
                    </span> -->
                     <div class="currency-text-class">
                      {{ position.rate_currency_code }}
                      <span class="overflow-class" [tooltip]="isFinancialValuesHidden ? '****' : (position?.rate_per_unit ? position.rate_per_unit : 0)">
                        {{ isFinancialValuesHidden ? '****' : (position?.rate_per_unit ? (position?.rate_per_unit | decimal :
                        quote_decimalPlaces) : 0) }}
                      </span>
                    </div>
                  </div>
                </div>
              </td>
              <!-- <td
                class="fixed-col-unit"
              >
              {{ position.unit_name ? position.unit_name : '-' }}
              </td> -->
              <!-- <td *ngFor="let month of position.monthList" class="scroll-col" contenteditable="true">0</td> -->
              <ng-container *ngFor="let month of monthList">
                <td class="scroll-col">
                  {{
                    getCellValue(position.position_id, month, "planned") | decimal : (resource_config?.allow_decimals ? decimalPlaces : '0')
                  }}
                  
                </td>
                <td class="scroll-col">
                  <div class="actual-class">
                    {{
                      (getCellValue(position.position_id, month, "actual") ? getCellValue(position.position_id, month, "actual") : 0) | decimal : (resource_config?.allow_decimals ? decimalPlaces : '0')
                    }}
                    
                    <span
                      *ngIf="
                        getCellValue(
                          position.position_id,
                          month,
                          'carryForward'
                        )
                      "
                      [ngClass]="getCellClass(position.position_id, month)"
                    >
                      {{
                        (getCellValue(
                          position.position_id,
                          month,
                          "carryForward"
                        )
                          ? getCellValue(
                              position.position_id,
                              month,
                              "carryForward"
                            ) > 0
                            ? getCellValue(
                                position.position_id,
                                month,
                                "carryForward"
                              )
                            : getCellValue(
                                position.position_id,
                                month,
                                "carryForward"
                              )
                          : 0
                        ) | decimal : (resource_config?.allow_decimals ? decimalPlaces : '0')
                      }} 
                    </span>
                  </div>
                </td>
              </ng-container>
              <td class="scroll-col currency" style="cursor: pointer;"
              [matMenuTriggerFor]="viewCarryForwardMenu"
              (click)="selectcarryForward(position.position_id)">
                {{
                  (position.carry_forward ? position.carry_forward : 0) | decimal : (resource_config?.allow_decimals ? decimalPlaces : '0')
                }}
                
              </td>
              <td
                class="fixed-col-right"
                [ngStyle]="{
                  'color': getColor(position.balance)
                }"
                style="font-weight: 500;"
              >
                {{ position.balance | decimal : (resource_config?.allow_decimals ? decimalPlaces : '0') }} 
              </td>
            </tr>
            <!-- <tr class="bottom-row-fixed hrs">
              <td class="fixed-col">
               <span class="total-text">Total Hours</span>
              </td>
              <td
                class="fixed-col-unit"
              >
              {{ unit ? unit : 'Hour' }}
              </td>
              <td *ngFor="let month of position.monthList" class="scroll-col" contenteditable="true">0</td>
              <ng-container *ngFor="let month of monthList">
                <td class="scroll-col">
                  <div class="total-class">
                    <span>{{ (getMonthWiseData(month['index']+1, month['year'],'planned') | decimal : decimalPlaces)}} </span>
                  </div>
                  
                </td>
                <td class="scroll-col">
                  <div class="actual-class">
                    {{ (getMonthWiseData(month['index']+1, month['year'],'actual') | decimal : decimalPlaces)}} 
                  </div>
                  
                </td>
              </ng-container>
              <td class="scroll-col currency"></td>
              <td class="fixed-col-right"></td>
            </tr> -->
            
            <tr class="bottom-row-fixed bills">
              <td class="fixed-col">
               <span class="total-text">Total in {{resource_config?.unit ? resource_config?.unit : '-'}}</span>
              </td>
              <!-- <td
                class="fixed-col-unit"
              >
                -
              </td> -->
              <!-- <td *ngFor="let month of position.monthList" class="scroll-col" contenteditable="true">0</td> -->
              <ng-container *ngFor="let month of monthList">
                <td class="scroll-col">
                  <div class="total-class">
                    <span>{{ (getMonthWiseData(month['index']+1, month['year'],'planned') | decimal : (resource_config?.allow_decimals ? decimalPlaces : '0'))}} {{  resource_config?.unit_display ? resource_config?.unit_display : 'Hrs'}}</span>
                  </div>
                  
                </td>
                <td class="scroll-col">
                  <div class="actual-class">
                    {{ (getMonthWiseData(month['index']+1, month['year'],'actual') | decimal : (resource_config?.allow_decimals ? decimalPlaces : '0'))}} {{  resource_config?.unit_display ? resource_config?.unit_display : 'Hrs'}}
                  </div>
                  
                </td>
              </ng-container>
              <td class="scroll-col currency"></td>
              <td class="fixed-col-right"></td>
            </tr>
            <tr class="bottom-row-fixed">
              <td class="fixed-col">
               <span class="total-text">{{resource_config?.total_display_value ? resource_config?.total_display_value : 'Total Value'}}</span>
              </td>
              <!-- <td
                class="fixed-col-unit"
              >
                -
              </td> -->
              <!-- <td *ngFor="let month of position.monthList" class="scroll-col" contenteditable="true">0</td> -->
              <ng-container *ngFor="let month of monthList">
                <td class="scroll-col">
                  <div class="total-class">
                    <!-- <span>{{ (getMonthWiseData(month['index']+1, month['year'],'planned') | decimal : decimalPlaces)}} Hrs</span> -->
                    <span>{{quoteData?.currency}} {{isFinancialValuesHidden ? '****' : (getMilestoneValue(month['index']+1, month['year'],'planned',true) | decimal : quote_decimalPlaces )}}</span>
                  </div>
                  
                </td>
                <td class="scroll-col">
                  <div class="actual-class">
                    -
                  </div>
                  
                </td>
              </ng-container>
              <td class="scroll-col currency"></td>
              <td class="fixed-col-right"></td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!--------------------------------Balance Pop up-------------------------------------------->
    <mat-menu
      #viewCarryForwardMenu="matMenu"
      style="min-width: 106px !important"
    >
      <div class="balance-card">
        <div class="header">
          <span class="header-carry-card">MONTHS</span>
          <span class="header-carry-card">CARRY FORWARD</span>
        </div>
        <div *ngFor="let month of monthList">
          <div class="data-value">
            <span class="value-carry-card"
              >M{{ month.id }} - {{ month.month }} {{ month.year }}</span
            >
            <span class="value-carry-card">{{(getCellValue(
              carryForwardList?.position_id,
              month,
              'carryForward'
            ) ? getCellValue(
              carryForwardList?.position_id,
              month,
              'carryForward'
            ) : 0) | decimal : (carryForwardList?.resource_type == 1 ? decimalPlaces : '0')}} {{  resource_config?.unit_display ? resource_config?.unit_display : 'Hrs'}}</span>
          </div>
        </div>
      </div>
    </mat-menu>

    <div class="footer-section" *ngIf="plannedViewEnabled || (serviceData && !serviceData?.billing_plan_edit)">
      <div class="footer-btn-class" *ngIf="serviceData && serviceData?.billing_plan_edit">
        <button class="save-button" *ngIf="serviceData && serviceData?.billing_plan_save && this.area!='billing-advice' && !isAlreadyForecasted" (click)="savePlanned()" [ngStyle]="{ 'pointer-events': (loaderObject.saveLoader || loaderObject.releaseLoader) ? 'none' : 'all' }">Save</button>
        <button class="release-btn" *ngIf="serviceData && serviceData?.billing_plan_forecast && this.area!='billing-advice' && !isAlreadyForecasted">
          <span class="btn-text" (click)="releaseForCosting()" [ngStyle]="{ 'pointer-events': (loaderObject.saveLoader || loaderObject.releaseLoader) ? 'none' : 'all' }">
            <div *ngIf="!loaderObject.releaseLoader">Forecast</div>
            <div *ngIf="loaderObject.releaseLoader" class="spinner-class">
              Releasing... 
              <mat-spinner class="secondary-spinner"  diameter="20"></mat-spinner>
            </div>
          </span>
        </button>      
      </div>
      <div class="footer-note-class" *ngIf="serviceData && !serviceData?.billing_plan_edit">
        <div class="note-class">
          <span class="note-bold-class">Note:</span>
          <span class="note-text-class">{{ (retrieveMessages && retrieveMessages.length > 0 && retrieveMessages[0]['note_msg']) ? retrieveMessages[0]['note_msg'] : 'For Monthly Fixed use Generate Milestone to Schedule Milestones'}}</span>
        </div>
      </div>
    </div>
  </div>
</div>
