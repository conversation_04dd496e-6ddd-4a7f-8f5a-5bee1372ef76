.resource-loading-styles {
  background: #f0f2f7;
  padding: 10px;
  height: var(--dynamicResourcePageHeight);
  font-family: var(--teamFont) !important;
  .loader-container{
    // background: #ffff;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    height: 100%;
    min-height: 50px;
  }
  .custom-loader {
    width:50px;
    height:50px;
    border-radius:50%;
    padding:1px;
    background:conic-gradient(#0000 10%,var(--teamButton)) content-box;
    -webkit-mask:
      repeating-conic-gradient(#0000 0deg,#000 1deg 20deg,#0000 21deg 36deg),
      radial-gradient(farthest-side,#0000 calc(100% - 9px),#000 calc(100% - 8px));
    -webkit-mask-composite: destination-in;
    mask-composite: intersect;
    animation:s4 1s infinite steps(10);
  }
  @keyframes s4 {to{transform: rotate(1turn)}}
  .header {
    display: flex;
    justify-content: space-between;
    padding-top: 12px;
    padding-bottom: 14px;

    .header-start {
      display: flex;
      align-items: center;
      gap: 0.33rem;

      .header-group{
        background: #fff;
        padding: 5px 10px;
        display: flex;
        gap: 0.33rem;
        align-items: center;
        justify-content: center;
      }

      .back-icon {
        width: 16px;
        height: 16px;
        // margin-right: 10px;
        border: 1px solid #526179;
        border-radius: 4px;
        cursor: pointer;
        display: flex;
        justify-content: center;
        align-items: center;
      }
      .icn-class {
        font-size: 14px;
        color: #526179;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
    .header-end {
      display: flex;
      align-items: center;
      gap: 1.2rem;

      .mat-button-toggle-group {
        height: 32px !important;
      }

      .mat-button-toggle-appearance-standard .mat-button-toggle-label-content {
        color: #111434  !important;
      }

      .mat-button-toggle-checked {
        border: 1px solid var(--teamButton) !important;
        background: var(--teamShade) !important;
        color: var(--teamButton) !important;
      }
      .mat-button-toggle-button {
        border: 0;
        background: none;
        color: inherit;
        padding: 0;
        margin: 0;
        font: inherit;
        outline: none;
        width: 32px !important;
        cursor: pointer;
      }

      .mat-button-toggle-appearance-standard .mat-button-toggle-label-content {
        margin-left: -4px !important;
      }

      .mat-button-toggle-appearance-standard {
        border: 1px solid #dadce2;
        background: transparent;
        color: #5f6c81;
      }
    }
  }
  .header-text {
    font-family: var(--teamFont) !important;
    font-size: 14px;
    font-weight: 400;
    line-height: 16px;
    text-align: left;
  }
  .mode-icon{
    border: 1px solid #1890FF;
    border-radius: 6px;
    padding: 2px 5px;
    background: #E8F4FF;
  }
  .mode-class-txt{
    color: #1890FF;
    font-family: var(--teamFont);
    font-size: 12px;
    font-weight: 500;
  }
  .content-header-class{
    background-color: #F7F9FB;
    display: flex;
    flex-direction: row;
    height: auto;
    padding: 14px 18px;

    .content-long-text{
        display: flex !important;
        flex-direction: column;
        gap: 3px;
        // flex: 1;
        min-width: 12%;
        flex-wrap: nowrap;
        align-items: flex-start;
    }
    .content-id{
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: 8px;
        justify-content: center
    }
    .content-button{
        display: flex;
        background-color: #EEF9E8;
        border: 1px solid #52C41A;
        justify-content: center;
        align-items: center;
        padding: 1px 6px;
        border-radius: 14px;
        gap: 4px;
        font-size: 13px;

        .circle{
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background-color: #52C41A;
        }
        .active-text{
            color: #7D838B;
        }
    }
    .content-button-save{
      display: flex;
      background-color: #E8F4FF;
      border: 1px solid #1890FF;
      justify-content: center;
      align-items: center;
      padding: 1px 6px;
      border-radius: 14px;
      gap: 4px;
      font-size: 13px;

      .circle-save{
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background-color: #1890FF;
      }
      .save-text{
          color: #1890FF;
      }
  }
    .content-long-header{
        font-size: 14px;
        font-weight: 500;
        line-height: 16px;
        text-align: left;
    }
    .content-text{
        display: flex !important;
        flex-direction: row;
        gap: 24px;
        min-width: 16%;
        flex-wrap: nowrap;
        align-items: center;
        justify-content: left;
    }
    .text-content-class{
        display: flex;
        flex-direction: column;
        gap: 3px;
        align-items: flex-start;
        justify-content: center
    }
    .content-head{
      font-family: var(--teamFont) !important;
        font-size: 12px;
        font-weight: 400;
        line-height: 16px;
        text-align: left;
        color: #7D838B;
    }
    .content-value{
      font-family: var(--teamFont) !important;
        font-size: 14px;
        font-weight: 400;
        line-height: 16px;
        text-align: left;
        color: #111434;
    }

  } 

  .edit-class{
    cursor: pointer;
    display: flex;
    gap: 6px;
    padding: 4px 8px;
    border-radius: 4px;
    border: 1px solid #45546E;
    align-items: center;
    justify-content: center;

    .edit-text{
      font-family: var(--teamFont) !important;
      font-size: 14px;
      font-weight: 700;
      color: #45546E;
    }
  }
  .history-class{
    cursor: pointer;
  }



  ::ng-deep .mat-divider.mat-divider-vertical {
    margin-left: 8px !important;
    margin-top: 2px !important;
    margin-bottom: 2px !important;
    margin-right: 25px !important;
  }

  .footer-section{
    margin: 14px;
    padding: 0px 16px 0px 20px;
    display: flex;
    flex-direction: column;
  }
  .footer-btn-class{
    display: flex;
    justify-content: end;
    align-items: center;
    gap: 24px;
  }
  .footer-note-class{
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2px;

    .note-class{
      display: flex;
      gap: 4px;
    }
    .note-bold-class{
      color: var(--milestoneNoteheader);
      font-weight: 500;
      font-size: var(--teamFont);
    }
    .note-text-class{
      font-size: var(--teamFont);
    }
  }
  .save-button{
    background: none;
    border: none;
    color: #45546E;
    font-family: var(--teamFont) !important;
    font-size: 14px;
    font-weight: 700;
    line-height: 16px;
    letter-spacing: -0.02em;
    text-align: left;
  }
  .release-btn{
    padding: 12px 16px 12px 16px;
    gap: 8px;
    border-radius: 8px;
    border: 1px solid #45546E;
    background: none;
    color: #45546E;
    font-family: var(--teamFont) !important;
    font-size: 14px;
    font-weight: 700;
    line-height: 16px;
    letter-spacing: -0.02em;
    text-align: left;
  }
  .spinner-class{
    display: flex;
    gap: 4px;
   }

  .toggle-class{
    .toggle-billing-btn {
      min-width: 7rem;
      font-family: var(--teamFont) !important;

      .btn-toggle-selected {
        font-size: 12px !important;
        background-color:var(--headerbutton) !important;
        color: #ffffff;
      }
    }
  }
}

.planned-table {
    position: relative;
    height: var(--dynamicResourceTableHeight) !important;
    width: 99%;
    .table-container {
      position: relative;
      width: 101%;
      height: 101%;
      overflow: auto; /* Enable both horizontal and vertical scrolling */
    }

    table {
      width: 100%;
      border-collapse: separate !important;
      border-spacing: 0px !important;
    }

    thead th,
    tbody td {
      padding: 8px;
      border: 1px solid #e0e0e0;
      text-align: center;
    }

    thead th {
      background-color: #515965;
      color: #fff;
      font-weight: bold;
      position: sticky;
      top: 0;
      z-index: 10;
      height: 24px;
      font-family: var(--teamFont) !important;
      font-size: 14px;
      font-weight: 500;
      line-height: 16px;
    }

    tbody td {
      background-color: #fff;
      height:52px;
    }

    // tbody tr:nth-child(even) td {
    //   background-color: #f9f9f9;
    // }

    tbody tr:hover td {
      /* background-color: #f1f1f1; */
      // background-color: #f9f9f9;
    }

    .fixed-col,
    .fixed-col-right {
      position: sticky;
      background-color: white;
      z-index: 5;
    }

    .fixed-col {
        left: 0 !important;
        position: sticky !important;
        z-index: 10000 !important;
        width: 288px !important;
        text-align: left !important;
      }

      .fixed-col-unit {
        left: 315px !important;
        position: sticky !important;
        z-index: 10000 !important;
      }
      
      .fixed-col-right {
        right: 0;
        position: sticky !important;
        z-index: 10000 !important;
        min-width: 110px !important;
      }

      .fixed-col-header{
        background: #515965;
        color: #fff;
        top: 0 !important;
        left:0 !important;
        position: sticky !important;
        z-index: 10000000 !important;
        min-width: 316px;
      }
      .fixed-unit-header{
        background: #515965;
        color: #fff;
        top: 0 !important;
        left:315px !important;
        position: sticky !important;
        z-index: 10000000 !important;
        min-width: 85px;
      }
      .fixed-col-right-header{
        background: #F27A6C !important;
        color: #fff;
        top: 0 !important;
        right:0 !important;
        position: sticky !important;
        z-index: 10000000 !important;
      }

    .scroll-col-header{
      min-width: 8rem;
      position: sticky;
      top: 0;
    }
    .scroll-col {
      min-width: 8rem;
    }

    tbody tr.currency-row:hover td {
        // background-color: #DADCE2;
      }
    .currency-row td {
        background-color: #dadce2;
        // height: 24px !important;
    }
    .currency-row .scroll-col.header {
      background: #e8f4ff;
      width: max-content;
      justify-content: center;
      display: flex;
      align-items: center;
      font-size: 12px;
      color: #1890FF;
      min-height: 36px !important;
    }

    .scroll-col.currency{
      color: #1890ff;
      position: sticky;
      right: 110px;
      z-index: 1000;
      width: 120px !important;
    }

    [contenteditable="true"] {
      outline: none;
      cursor: text;
      vertical-align: baseline;
      padding-top: 23px !important;
    }

    [contenteditable="true"]:hover {
      // background-color: #f1f1f1;
    }

    .position-class{
      display: flex;
      flex-direction: column;
      margin: 5px;
      gap: 2px;
    }
    .position-id-class{
      justify-content: space-between;
      display: flex;
      width: 288px !important;
    }
    .position-name-class{
      justify-content: space-between;
      display: flex;
      width: 288px !important;
      align-items: center;
      gap: 6px;
    }
    .balance-class{
      display: flex;
      justify-content: space-between;
    }
    .balance-date-class{
      background: #FACAC4;
      font-family: var(--teamFont) !important;
      font-size: 12px;
      font-weight: 500;
      line-height: 16px;
      text-align: left;
      padding: 0px 4px 0px 4px;
      gap: 10px;
      border-radius: 16px;
      // opacity: 0px;
      color: #DB6E61;
    }
    .scroll-col.actual{
      // background-color: #CFEFDA !important;
      // color: #009432;
      color: #fff;
      background: #B5BBC5 !important;
      font-family: var(--teamFont) !important;
      font-size: 14px;
      font-weight: 540;
      line-height: 16px;
    }
    // .scroll-col.actual::before{
    //   content: '';
    //   position: absolute;
    //   top: 0;
    //   left: 0;
    //   right: 0;
    //   bottom: 0;
    //   background-color: #45546efa;
    //   z-index: -1;
    //   // opacity: 0.7;
    // }
    .scroll-col.planned{
      color:#272A47 !important;
      font-family: var(--teamFont) !important;
      font-size: 14px;
      font-weight: 540;
      line-height: 16px;
    }

    .position-name-text{
      font-family: var(--teamFont) !important;
      font-size: 14px;
      font-weight: 600;
      line-height: 16px;
      text-align: left;
      color: #45546E;
      // width: 85%;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      width: 300%;
      cursor: pointer;
    }
    .svg-icn-class{
      cursor: pointer;
    }
    .active-count{
      color: #52C41A;
    }

    .scroll-col.disabled-cell {
      background-color: #e8e9ee9e;
      color: #A8ACB2;
    }
  
    .currency-row td {
      /* background-color: #dadce2; */
      position: sticky;
      top: 30px; /* This should be the height of your thead th elements */
      z-index: 9;
    }
  
    .currency-row td .fixed-col{
      position: sticky;
      top: 30px; /* This should be the height of your thead th elements */
      z-index: 10000;
    }
    
    .scroll-col-header {
      &.carry-header {
        background: #e8f4ff;
        color: #1890ff;
        position: sticky;
        right: 110px;
        z-index: 10000000;
        width: 120px !important;
      }
    }

    .scroll-col {
      &.right{
        right: 110px;
        z-index: 1000000 !important;
        position: sticky;
        top: 30px !important;
      }
    }



    .actual-class {
      display: flex;
      gap: 3px;
      align-items: center;
      justify-content: center;
      min-width: max-content;

      .positive-class{
        color: #52C41A;
        background: #EEF9E8;
        padding: 0px 4px 0px 4px;
        border-radius: 8px;
      }
      .warning-class{
        color: #fa8c16;
        background: #fa8c161f;
        padding: 0px 4px 0px 4px;
        border-radius: 8px;
      }
      .negative-class{
        color: #FF3A46;
        background: #FFEBEC;
        padding: 0px 4px 0px 4px;
        border-radius: 8px;
      }
    }


    .fixed-col-currency-left {
      background: #515965;
      color: #fff;
      top: 30px !important;
      left: 0 !important;
      position: sticky !important;
      z-index: 100000 !important;
    }

    .fixed-col-currency-unit {
      background: #515965;
      color: #fff;
      top: 30px !important;
      left: 315px !important;
      position: sticky !important;
      z-index: 100000 !important;
    }
  
    .fixed-col-currency-right {
      background: #515965;
      color: #fff;
      top: 30px !important;
      right: 0 !important;
      position: sticky !important;
      z-index: 100000 !important;
    }

    .bottom-row-fixed{
      position: sticky;
      bottom: 0;
      z-index: 10000;
      &.hrs {
        bottom: 104px;
        z-index: 1000000000;
      }
      &.bills {
        bottom: 52px;
        z-index: 100000000;
      }
    }
    .total-text{
      font-family: var(--teamFont) !important;
      font-size: 15px;
      font-weight: 600;
      line-height: 16px;
      text-align: left;
      color: #45546E;
    }
    .total-class{
      display: flex !important;
      flex-flow: column;
      gap: 7px;
    }
    .end-class{
      display: flex;
      flex-direction: row;
      gap: 14px;
    }
    .currency-text-class{
      display: flex;
      width: 33%;
      justify-content: end;
      color: #272A47 !important;
      font-family: var(--teamFont) !important;
      font-size: 13px;
      font-weight: 550;
      line-height: 16px;
      gap: 3px;

      .overflow-class{
        max-width: 98%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        cursor: pointer;
      }
    }
  }

  .balance-card{
    width: 237px;
    max-height: 350px;
    font-size: 13px;
    border-radius: 24px 4px 24px 24px !important;
    background-color: rgb(255, 255, 255) !important;
    z-index: 1 !important;
    padding: 12px 16px 12px 16px;
    display: flex;
    flex-direction: column;
    // font-family: var(--fontFamily) !important;
    .header{
      display: flex;
      justify-content: space-between;
    }
    .header-carry-card{
      color: #A8ACB2;
      font-family: var(--teamFont) !important;
      font-size: 10px;
      font-weight: 400;
      line-height: 12px;
      letter-spacing: 0.02em;
    }
    .data-value{
      display: flex;
      justify-content: space-between;
      flex-direction: row;
      flex: 1;
      padding-top: 6px;
      padding-bottom: 8px;
      border-bottom: 1px solid #E8E9EE;
    }

    .value-carry-card{
      color: #5F6C81;
      font-family: var(--teamFont) !important;
      font-size: 10px;
      font-weight: 400;
      line-height: 12px;
      letter-spacing: 0.02em;

    }
  
}

::ng-deep .secondary-spinner circle{
  stroke: #45546E !important;
}
