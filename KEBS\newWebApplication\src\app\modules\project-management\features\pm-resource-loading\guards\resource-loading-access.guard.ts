import { Injectable } from '@angular/core';
import {
  CanActivate,
  ActivatedRouteSnapshot,
  RouterStateSnapshot,
  UrlTree,
  Router,
} from '@angular/router';
import { Observable } from 'rxjs';
import { PmMasterService } from '../../../services/pm-master.service';
import { PmAuthorizationService } from '../../../services/pm-authorization.service';
import { ToasterService } from 'src/app/services/toaster-service/toaster.service';
import { PmInternalStakeholderService } from '../../pm-details/features/pm-project-team/features/project-internal-stakeholders/services/pm-internal-stakeholder.service';
import * as _ from 'underscore';
@Injectable({
  providedIn: 'root',
})
export class ResourceLoadingAccessGuard implements CanActivate {
  itemId: any;
  projectId: any;
  resourceLoadingAccess: boolean = false;
  withOpportunity: any;
  service_type: any;
  q2cVisible: any;
  action_btn_config:any;
  constructor(
    private pmMasterService: PmMasterService,
    private authService: PmAuthorizationService,
    private toaster: ToasterService,
    private _router: Router,
    private PmInternalStakeholderService: PmInternalStakeholderService
  ) {}
  async canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) {
    let url = state['url'];
    let hasAccess = <boolean>await this.canAccess(url);

    if (hasAccess == false) {
      this.toaster.showError(
        'You are not having access to Billing Plan!',
        'Access Restricted',
        10000
      );
      this._router.navigateByUrl('/main/project-management');
    }

    return hasAccess;
  }

  canAccess(url) {
    this.itemId = parseInt(url.split('/')[4]);
    this.projectId = parseInt(url.split('/')[3]);
    return new Promise(async (resolve, reject) => {
      try {
        await this.authService
          .getReadWriteAccess(this.projectId, this.itemId)
          .then(async (res) => {
            if (res) {
              this.resourceLoadingAccess =
                await this.authService.getProjectWiseObjectAccess(
                  this.projectId,
                  this.itemId,
                  143
                );
            }
          });

        await this.PmInternalStakeholderService.getProjectQuote(
          this.projectId,
          this.itemId
        ).then((res) => {
          if (res['messType'] == 'S') {
            if (res['data'].length > 0) {
              this.withOpportunity = res['data'][0].with_opportunity;
              this.service_type = res['data'][0]['service_type_id'];
            }
          }
        });

        await this.pmMasterService
          .getPMFormCustomizeConfigV()
          .then((res: any) => {
            if (res) {
              let formConfig = res;
              let retrieveQ2c = _.where(formConfig, {
                type: 'project-team',
                field_name: 'q2c_display',
                is_active: true,
              });
              this.q2cVisible =
                retrieveQ2c.length > 0
                  ? retrieveQ2c[0]['is_visible']
                    ? retrieveQ2c[0]['is_visible']
                    : false
                  : false;
              this.action_btn_config = _.where(formConfig, {
                    type: "resource-loading",
                    field_name: "fixed-billing-plan",
                    is_dependency_based:true,
                    is_active: true
              });
            }
          });
        if (this.withOpportunity && this.q2cVisible) {
          let serviceTypeList: any;
          await this.pmMasterService.serviceTypeList().then((res) => {
            serviceTypeList = res;
          });
          let serviceData = _.where(serviceTypeList, {
            id: this.service_type,
          });
          let resourceLoadingApplicable =
            serviceData && serviceData.length > 0
              ? serviceData[0]['is_resource_loading_applicable']
                ? serviceData[0]['is_resource_loading_applicable']
                : 0
              : 0;
          
          let disable_billing_plan = this.action_btn_config.length > 0
          ? this.action_btn_config[0].is_dependency_based
            ? this.action_btn_config[0].is_dependency_based
            : false
          : false;
          if(disable_billing_plan){
            if(this.action_btn_config.length > 0 && this.action_btn_config[0].service_type_list && this.action_btn_config[0].service_type_list.includes(this.service_type)){
              disable_billing_plan = true
            }
            else{
              disable_billing_plan = false
            }
          }
          if (this.resourceLoadingAccess && resourceLoadingApplicable && !disable_billing_plan) {
            resolve(true);
          } else {
            resolve(false);
          }
        }
        else{
          resolve(false);
        }
      } catch (err) {
        resolve(false);
      }
    });
  }
}
