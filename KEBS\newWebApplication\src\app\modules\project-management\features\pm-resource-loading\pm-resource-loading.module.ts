import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { PmResourceLoadingRoutingModule } from './pm-resource-loading-routing.module';
import { ResourceLoadingLandingPageComponent } from './features/resource-loading-landing-page/resource-loading-landing-page.component';
import {MatIconModule} from '@angular/material/icon';
import {MatSelectModule} from '@angular/material/select';
import {FormsModule, ReactiveFormsModule } from '@angular/forms';
import {MatSlideToggleModule} from '@angular/material/slide-toggle';
import {MatInputModule} from '@angular/material/input';
import {MatFormFieldModule} from '@angular/material/form-field';
import { TooltipModule } from "ng2-tooltip-directive";
import { MatButtonModule } from '@angular/material/button';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import {MatDividerModule} from '@angular/material/divider';
import { PmHistoryDialogComponent } from './components/pm-history-dialog/pm-history-dialog.component';
import { ChangesAlertGuard } from './guards/changes-alert.guard'
import { SatPopoverModule} from '@ncstate/sat-popover';
import {MatMenuModule} from '@angular/material/menu';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { SharedLazyLoadedModule } from 'src/app/modules/project-management/shared-lazy-loaded/shared-lazy-loaded.module';
import {MatProgressSpinnerModule} from '@angular/material/progress-spinner';
import { FixedBillingPlanComponent } from './features/fixed-billing-plan/fixed-billing-plan.component';
import { QuoteBillingPlanComponent } from './components/quote-billing-plan/quote-billing-plan.component';
import { AttachDependenciesComponent } from './components/attach-dependencies/attach-dependencies.component';
@NgModule({
  declarations: [ResourceLoadingLandingPageComponent, PmHistoryDialogComponent, FixedBillingPlanComponent, QuoteBillingPlanComponent,AttachDependenciesComponent],
  imports: [
    CommonModule,
    PmResourceLoadingRoutingModule,
    MatIconModule,
    MatSelectModule,
    FormsModule,
    ReactiveFormsModule,
    MatSlideToggleModule,
    MatInputModule,
    MatFormFieldModule,
    TooltipModule,
    MatButtonModule,
    MatButtonToggleModule,
    MatDividerModule,
    SatPopoverModule,
    MatMenuModule,
    SharedLazyLoadedModule,
    MatProgressSpinnerModule,
    MatCheckboxModule
  ],
  providers: [ChangesAlertGuard],
  exports:[
    ResourceLoadingLandingPageComponent,
    AttachDependenciesComponent
  ]
})
export class PmResourceLoadingModule { }
