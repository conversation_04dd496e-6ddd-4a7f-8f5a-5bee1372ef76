import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from "@angular/common/http";
@Injectable({
  providedIn: 'root'
})
export class ResourceLoadingServiceService {

  constructor(private _http: HttpClient) { }


  getProjectQuote(project_id,item_id){
    return this._http.post('/api/pm/planning/getProjectQuote',{project_id,item_id});
  }

  getQuoteDetailsForResourceLoading(project_id,project_item_id,quote_id, resource_type_id,){
    return this._http.post('/api/pm/financial/getQuoteDetailsForBillingPlan',{project_id,project_item_id,quote_id, resource_type_id});
  }

  getResourceLoadingData(project_id,project_item_id,quote_id, resource_type_id, deliverable_id, monthList,decimalPlaces){
    return this._http.post('/api/pm/financial/getBillingPlanData',{project_id,project_item_id,quote_id, resource_type_id, deliverable_id, monthList,decimal_places:decimalPlaces});
  }

  getSaveResourceLoadingData(data){
    console.log('Data:',data)
    return this._http.post('/api/pm/financial/insertBillingPlanData',data);
  }
  getResourceLoadingHeaderVersions(project_id,project_item_id,quote_id, deliverable_id, resource_type_id){
    return this._http.post('/api/pm/financial/getBillingPlanVersionHistory',{project_id,project_item_id,quote_id, deliverable_id, resource_type_id});
  }
  getInitialLoadQuoteData(start_date,end_date,quote_id){
    return this._http.post('/api/qb/quote/getMonthWisePositionEffort',{start_date,end_date,quote_id});
  }

  createMilestoneFromResourceLoading(project_id,item_id,quote_id, deliverable_id, data,is_all_data){
    return this._http.post('/api/pm/planning/createMilestonesResourceLoading',{project_id,item_id,quote_id, deliverable_id, data,is_all_data});
  }

  createBillsFromResourceLoading(project_id,item_id,quote_id, resource_type_id, data,is_all_data){
    return this._http.post('/api/pm/planning/createBillsFromResourceLoading',{project_id,item_id,quote_id, resource_type_id, data,is_all_data});
  }

  initialMileStoneCheck(project_id,item_id,quote_id){
    return this._http.post('/api/pm/planning/initialMileStoneCheck',{project_id,item_id,quote_id});
  }

  insertPlannedTimeTracker(project_id, item_id, quote_id){ 
    return this._http.post("/api/bgjPrimary/buildEmployeePlannedHours",{project_id, item_id, quote_id})
  }

  getFixedBillingPlanQuoteDetails(project_id, item_id, quote_id){ 
    return this._http.post("/api/pm/financial/getFixedBillingPlanQuoteDetails",{project_id, item_id, quote_id})
  }

  getSavePositionDeliverable(data){
    return this._http.post('/api/pm/financial/insertFixedBillingPlanData',data);
  }

  getFixedResourceLoadingHeaderVersions(project_id,project_item_id){
    return this._http.post('/api/pm/financial/getFixedBillingPlanVersionHistory',{project_id,project_item_id});
  }

  saveAttachDependencies(deliverable_id, line_item_ids){
    return this._http.post('/api/pm/financial/saveAttachDependencies',{deliverable_id, line_item_ids});
  }


  resourceTypeConfigQuote(itemId, quote_id){
    return this._http.post('/api/pm/financial/resourceTypeConfigQuote',{project_item_id: itemId, quote_id: quote_id});
  }
}
