import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import {RouteGuard} from "./guard/route/route.guard"

const routes: Routes = [
  {
    path:"",
    redirectTo:"landing",
    pathMatch:"full"
  },
  {
    path:'landing',
    canActivate:[RouteGuard],
    loadChildren: () => import('./features/pm-landing-page/pm-landing-page.module').then(m => m.PmLandingPageModule),
    data : {
      breadcrumb: 'Project'
    },

  },
  {
    path:'setting',
    canActivate:[RouteGuard],
    loadChildren: () => import('./features/pm-project-settings/pm-project-settings.module').then(m => m.PmProjectSettingsModule),
    data : {
      breadcrumb: 'Project'
    },

  },
  {
    path:'create',
    canActivate:[RouteGuard],
    loadChildren: () => import('./features/pm-creation/pm-creation.module').then(m => m.PmCreationModule),
    data : {
      breadcrumb: 'Create'
    }
  },
  {
    path: ':projectId/:itemId',
    canActivate:[RouteGuard],
    children:[
      {
        path:":costingSheetID/costing_sheet",
        loadChildren: () =>import('./features/pm-costing-sheet/pm-costing-sheet.module').then((m) => m.PmCostingSheetModule)
      },
      {
        path:"baselineVsActual",
        loadChildren: () =>import('./features/pm-baseline/pm-baseline.module').then((m) => m.PmBaselineModule)
      },
      {
        path:":milestoneID/billing_advice",
        loadChildren: () =>import('./features/pm-full-screen-billing-advice/pm-full-screen-billing-advice.module').then((m) => m.PmFullScreenBillingAdviceModule),
      },
      {
        path:":milestoneID/billing_advice/new",
        loadChildren: () =>import('./features/pm-full-screen-billing-advice/pm-full-screen-billing-advice.module').then((m) => m.PmFullScreenBillingAdviceModule),
      },
      {
        path:":quoteID/resource_loading",
        loadChildren: () =>import('./features/pm-resource-loading/pm-resource-loading.module').then((m) => m.PmResourceLoadingModule)
      },
      {
        path:":quoteID/fixed",
        loadChildren: () =>import('./features/pm-resource-loading/pm-resource-loading.module').then((m) => m.PmResourceLoadingModule)
      },
    ]

  },
  {
    path: ':projectId/:projectName',
    canActivate:[RouteGuard],
    loadChildren: () =>import('./features/portfolio-details/portfolio-details.module').then((m) => m.PortfolioDetailsModule),
    children:[
      {
        path: ':itemId/:itemName',
        loadChildren: () =>import('./features/pm-details/pm-details.module').then((m) => m.PmDetailsModule)
      },
    ]
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class ProjectManagementRoutingModule { }
