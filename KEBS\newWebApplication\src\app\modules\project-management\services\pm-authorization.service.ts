import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from "@angular/common/http";
import { RolesService } from 'src/app/services/acl/roles.service';
import * as _ from "underscore";
import { ToasterService } from 'src/app/services/toaster-service/toaster.service';
import { LoginService } from 'src/app/services/login/login.service';

@Injectable({
  providedIn: 'root'
})
export class PmAuthorizationService {

  public projectRoleAccessList=[]
  public roleList = []
  public mainAccess = []
  public projectRoleAccess: any;
  public projectEmployeeRole=[]
  public projectListAccess=[]
  public status_list=[];


  constructor(private http: HttpClient,
    private rolesService: RolesService,
    private toaster: ToasterService,
    private loginService: LoginService) { 
    this.getProjectOverallAccess()
    this.getUserRoleAccessProjectList()
    this.getStatusList()

    
  }


  getProjectRoleAccess(){
    return new Promise((resolve, reject) => {
      if(this.projectRoleAccessList && this.projectRoleAccessList.length>0)
      {
        return resolve(this.projectRoleAccessList)
      }
      else
      {
        this.http.post("/api/pm/auth/getProjectRoleAccessList",{}).subscribe((res: any) => {
          
          this.projectRoleAccessList = res
          return resolve(res)
          
        }, (err) => {
          return reject(err)
        })
      }
    })
  }

  checkProjectApplicationAccess(){
    return new Promise(async(resolve, reject)=>{

      if(this.rolesService.roles && this.rolesService.roles.length>0)
      {
        let accessList = _.where(this.rolesService.roles, { application_id: 915 });
        
        this.projectEmployeeRole = accessList

        let projectAccess = _.where(this.projectEmployeeRole,{object_id: 6})

        if (projectAccess.length > 0)
        {
          return resolve(true)
        }
        else
        {
          return resolve(false)
        }
      }
      else
      {
    
        const user = this.loginService.getProfile().profile;
        await this.getAccessList(user.oid, "project").subscribe((res=>{
    
          let accessList = _.where(res, { application_id: 915 });
          
          this.projectEmployeeRole = accessList

          let projectAccess = _.where(this.projectEmployeeRole,{object_id: 6})

          if (projectAccess.length > 0)
          {
            return resolve(true);
          }
          else
          {
            return resolve(false);
          }
        }))
      }
    })

  
    
  }


  checkProjectRoleAccess(projectRoleAccess){

    let accessList = _.where(this.projectEmployeeRole, { application_id: 915, object_id:  6});
       
    if (accessList.length > 0)
    {
      if(accessList[0]['object_value']=="*")
      {

        return {messType:"S", message:"Admin access enabled!", data:this.getAccessTopPriority([1],projectRoleAccess)}
      }
      else if(accessList[0]['object_value']=="True")
      {
        let project_access = typeof accessList[0].object_entries =="string"?JSON.parse(accessList[0].object_entries):accessList[0].object_entries
       
        if(project_access=="null" || project_access=="*" || project_access==null || project_access=="null")
        {
            return {messType:"S", message:"Team Member Access enabled", data:this.getAccessTopPriority([9],projectRoleAccess)}
        } 
        else
        {
            return {messType:"S", message:"Project Role Access enabled", data:this.getAccessTopPriority(project_access,projectRoleAccess)}
        }
        
      }
      else
      {
        return {messType:"E", message:"Not authorized!", data:this.getAccessTopPriority([],projectRoleAccess)}
      }
    }
    else
    {
      return {messType:"E", message:"Not authorized!", data:this.getAccessTopPriority([],projectRoleAccess)}
    }
  }


  getAccessTopPriority(authAccess,projectRoleAccess){
    let accessResult = _.filter(projectRoleAccess, (res)=>{
      for(let auth of authAccess)
      {
        if(res['id']==auth)
        {
          return res;
        }

      }
    })

    return accessResult
  }


  async getProjectRoleObjectAccess(){
    return new Promise(async(resolve, reject)=>{
      if(this.projectRoleAccess && this.projectRoleAccess['messType']=="S")
      {
          resolve(this.projectRoleAccess)
      }
      else
      {
        await this.getProjectRoleAccess().then((res)=>{

          let projectRoleAccess = this.projectRoleAccessList

          let correctRoleAccess = this.checkProjectRoleAccess(projectRoleAccess)
    
          if(correctRoleAccess['messType']=="S")
          {
            let roleData = correctRoleAccess['data']
    
            if(roleData.length==0)
            {
              this.projectRoleAccess = {messType:"E", message:"No access to Projects", access: false, data:[]}
              resolve(this.projectRoleAccess)
            }
            else
            {
              this.projectRoleAccess={messType:"S", message:"Access to Project", access: true, data: roleData}
              resolve(this.projectRoleAccess)
            }
    
    
          }
          else
          {
            this.projectRoleAccess = {messType:"E", message:"No access to Projects", access: false, data:[]}
            resolve(this.projectRoleAccess)
          }
        },(err)=>{
          this.toaster.showError("This action is not allowed!","Dismiss",3000)
          reject(false)
        })
      }
    })
    
  }


  getProjectOverallAccess(){
    return new Promise(async(resolve, reject) => {
      if(this.roleList && this.roleList.length>0)
      {
        return resolve(this.roleList)
      }
      else
      {
        await this.getProjectRoleObjectAccess().then(res=>{
          if(res['messType']=="S")
          {
            if(res['data'].length>0)
            {

              let role_ids = _.pluck(res['data'],'id')

              if(role_ids.length>0)
              {

                this.http.post("/api/pm/auth/getProjectOverallAccess",{mainAccess: role_ids}).subscribe((res: any) => {
                  console.log(this.roleList)
                  this.roleList = res
                  return resolve(res)
                  
                }, (err) => {
                  return reject(err)
                })
              }
              else
              {
                return resolve([])
              }
            }
            else
            {
              return resolve([])
            }
          }
          else
          {
            return resolve([])
          }
        })
      }
    })
  }


  getProjectObjectAccess(object_id){
    let roleAccess = _.where(this.roleList,{object_id: object_id})

    if(roleAccess.length>0)
    {
      return true;
    }
    else
    {
      return false;
    }
  }


  getEmployeeRoleObjectAccess(object_id){
    let roleAccess = _.where(this.projectEmployeeRole,{object_id: object_id})

    if(roleAccess.length>0)
    {
      return true;
    }
    else
    {
      return false;
    }
  }

  getUserRoleAccessProjectList(){
    return new Promise(async(resolve, reject)=>{
        if(this.projectListAccess && this.projectListAccess.length>0)
        {
          resolve(this.projectListAccess)
        }
        else
        {
          await this.getProjectRoleObjectAccess().then(res=>{
            if(res['messType']=="S")
            {
              if(res['data'].length>0)
              {
                this.http.post("/api/pm/auth/getUserRoleAccessProjectList",{mainAccess: res['data']}).subscribe((res: any) => {
                    this.projectListAccess=res;
                    return resolve(res)
                    
                  }, (err) => {
                    return reject(err)
                  })
                
              }
              else
              {
                return resolve([])
              }
            }
            else
            {
              return resolve([])
            }
          })
        }
      
    })
  }

  getAccessList(userOid, type) {
    return this.http.post('/api/pm/auth/getAccessFor', {oid: userOid,type: type,});
  }

  getReadWriteAccess(projectId, itemId){
    return new Promise(async(resolve, reject)=>{
      await this.getUserRoleAccessProjectList().then(async(res)=>{

        await this.getStatusList().then((status)=>{


          let accessList = _.where(res, {item_id: itemId, project_id: projectId, object_access: "Both"})

          if(accessList.length>0)
          {
           
              let statusAccess = _.where(status,{id: accessList[0]['item_status_id'], object_access: "Both"})
             
              if(statusAccess.length>0)
              {
                  resolve(true)
              }
              else
              {
                  resolve(false);
              }
          }
          else
          {
              resolve(false)
          }

        })
       
        

      },(err)=>{
        resolve(true);
      })
    })
  }


  getAdminAccess(){
    return new Promise(async(resolve, reject)=>{
    
      this.http.post("/api/pm/auth/getAdminAccessList",{}).subscribe((res: any) => {
        return resolve(res)
        
      }, (err) => {
        return reject(err)
      })
          
    })
  }


  getStatusList(){
    return new Promise((resolve,reject)=>{
      if (this.status_list && this.status_list.length > 0) {
        return resolve(this.status_list);
      } else {
        this.http.post("/api/pm/masterData/getStatusList",{}).subscribe((res: any)=>{
          this.status_list = res
          return resolve(this.status_list)
        },(err)=>{
          return reject(err)
        })
      }
    })
  }


  async getProjectWiseObjectAccess(projectId, itemId, object_id)
  {
    let role_ids = _.pluck(_.where(this.projectListAccess,{project_id: projectId, item_id: itemId}),"role_access_id")

    //if any collosion occurs between roles get Superior Role for Project
    let superior_role_id = await this.getSuperiorRole(role_ids)

    let accessCheck = _.where(this.roleList,{role_id: superior_role_id, object_id: object_id})

    if(accessCheck.length>0)
    {
      return true;
    }
    else
    {
      return false;
    }
    
    
  }


  getSuperiorRole(role_ids){
    let role_list = _.filter(this.projectRoleAccessList,(res)=>{
      if(_.contains(role_ids, res['id'])){
        return res;
      }
    })

    let sequence_role_list = _.sortBy(role_list,"sequence_list")

    return sequence_role_list[0]['id']


  }


  updateProjectStatus(projectId, itemId, status_id)
  {
    
    for(let project of this.projectListAccess)
    {
      if(project['project_id']== projectId && project['item_id']==itemId)
      {
          project['item_status_id']= status_id
      }
    }
  }



}
