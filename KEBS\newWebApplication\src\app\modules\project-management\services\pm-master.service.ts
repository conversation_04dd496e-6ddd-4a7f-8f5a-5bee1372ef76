import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from "@angular/common/http";
import * as moment from 'moment';
import { ToasterService } from 'src/app/services/toaster-service/toaster.service';
import { resizeCanvas } from '../../contacts/features/contacts-details/components/image-cropper/utils/resize.utils';
//import { BehaviorSubject, Observable, throwError } from "rxjs";
@Injectable({
  providedIn: 'root'
})
export class PmMasterService {
  public milestone_status_list:any=[];
  public customer_list: any = [];
  public industry_list: any = [];
  public contact_list: any = [];
  public status_list: any = [];
  public holiday_calendar_list:any=[];
  public object_access_list:any=[]
  public projectRoleMasterList: any=[];
  public isaTimesheetLocations:any=[];
  public entity_list: any=[];
  public project_type_list:any=[];
  public person_role_list:any=[];
  public currency_list:any=[];
  public form_config:any=[]
  public service_type_list: any=[];
  public invoice_template_list:any=[];
  public work_location_list:any=[];
  public template_master_list:any=[];
  public customer_name_list: any=[];
  public shift_list:any=[];
  public ganttRollUp: any=[];
  public ganttTypeList: any=[];
  public p_and_l_list:any=[];
  public country_list: any=[];
  public task_status_list:any=[];
  public task_software_type:any=[];
  public reason_list:any=[];
  public position_list:any=[]
  public experiance_list:any[]
  public office_location_list:any=[]
  public commercialCatageroiesList:any=[];
  public billable_list: any = [];
  public fricew_list: any = [];
  public paymentTerms_list: any = [];
  public legal_entity_list: any = [];
  public tags_list:any =[];
  public project_status_matrix: any=[];
  public billing_area_list:any=[];
  public region_list:any=[];
  public project_engagement_list:any=[];
  public project_classification_list:any=[];
  public external_role_list:any=[];
  public workCityList: any = [];
  public projectShiftMasterList: any = [];
  public timezoneMasterList: any = [];

  constructor(private http: HttpClient,
    private toasterService: ToasterService) { 
    this.getWorkCityList();
    this.getCustomerList();
    this.getIndustryList();
    this.getHolidayCalendar();
    //this.getAccessObject();
    this.getProjectRoleMaster();
    //this.getISATimesheetLocationsMaster()
    this.getEntityList();
    //this.getServiceTypeList();
    this.getProjectTypeList();
    this.currencyList();
    this.personRoleList();
    this.milestoneStatusList()
    this.getPMFormCustomizeConfigV()
    this.getStatusList();
    this.getTaskStatusList();

    this.serviceTypeList();
    this.getInvoiceTemplate();
    this.getWorkLocation();
    this.getTemplateMaster();
    this.getCustomerNameList();
    this.getShiftList();
    this.getGanttTypeRollUp();
    this.getGanttTypeList();
    this.getProfitLossList();
    this.getCountryList();
    this.getReasonList();
    this.getPositionList();
    this.getExperianceList();
    this.getOfficeLocationList();
    this.getPaymentTermsList();
    this.getBillingTypeList();
    this.getTags();
    this.getBillingArea();
    this.getProjectRegion();
    this.getProjectEngagement();
    this.getProjectClassification();
    this.externalRoleList();
    this.getCommercialCatageroiesList();
    this.getWorkShiftList();
    this.getTimeZoneMasterList();
    this.getSoftwareList();
  }

  getPMFormCustomizeConfigV(){
    return new Promise((resolve,reject)=>{
      if (this.form_config && this.form_config.length > 0) {
        return resolve(this.form_config);
      } else {
        this.http.post("/api/pm/masterData/getPMFormCustomizeConfig",{}).subscribe((res)=>{
          if(res['messType'] == 'S'){
            this.form_config = res['data']
            return resolve(this.form_config)
          }
          else
          {
            this.toasterService.showError("Something went wrong at our end!","Please try again later!",10000)
          }
        },(err)=>{
          return reject(err)
        })
      }
    })
  }

  getCustomerList(){
    return new Promise((resolve,reject)=>{
      if (this.customer_list && this.customer_list.length > 0) {
        return resolve(this.customer_list);
      } else {
        this.getTotalCustomerData().then((res)=>{
          this.customer_list=res
          return resolve(this.customer_list)
        })
        
      }
    })
  }

  getIndustryList(){
    return new Promise((resolve,reject)=>{
      if (this.industry_list && this.industry_list.length > 0) {
        return resolve(this.industry_list);
      } else {
        this.http.post("/api/pm/masterData/getIndustryList",{}).subscribe((res)=>{
          if(res['messType'] == 'S'){
            this.industry_list = res['data']
            return resolve(this.industry_list)
          }
        },(err)=>{
          return reject(err)
        })
      }
    })
  }

  getStatusList(){
    return new Promise((resolve,reject)=>{
      if (this.status_list && this.status_list.length > 0) {
        return resolve(this.status_list);
      } else {
        this.http.post("/api/pm/masterData/getStatusList",{}).subscribe((res)=>{
          this.status_list = res
          return resolve(this.status_list)
        },(err)=>{
          return reject(err)
        })
      }
    })
  }
  getTaskStatusList(){
    return new Promise((resolve,reject)=>{
      if (this.task_status_list && this.task_status_list.length > 0) {
        return resolve(this.task_status_list);
      } else {
        this.http.post("/api/pm/masterData/getTaskStatusList",{}).subscribe((res)=>{
          this.task_status_list = res
          return resolve(this.status_list)
        },(err)=>{
          return reject(err)
        })
      }
    })
  }

  getPortfolioCode(customer){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/masterData/getPortfolioCode",{customer}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }
  getHolidayCalendar(){
    return new Promise((resolve,reject)=>{
      if (this.holiday_calendar_list && this.holiday_calendar_list.length > 0) {
        return resolve(this.holiday_calendar_list);
      } else {
        this.http.post("/api/pm/masterData/getHolidayCalendar",{}).subscribe((res)=>{
          if(res['messType'] == 'S'){
            this.holiday_calendar_list = res['data']
            return resolve(this.holiday_calendar_list)
          }
        },(err)=>{
          return reject(err)
        })
      }
    })
}


getEntityList(){
  return new Promise((resolve,reject)=>{
    if (this.entity_list && this.entity_list.length > 0) {
      return resolve(this.entity_list);
    } else {
      this.http.post("/api/pm/masterData/getEntityList",{}).subscribe((res)=>{
        if(res['messType'] == 'S'){
          this.entity_list = res['data']
          return resolve(this.entity_list)
        }
      },(err)=>{
        return reject(err)
      })
    }
  })
}

getCustomerSuggestionsFromDB(text)
  {
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/masterData/getCustomerSuggestionsFromDB",{text}).subscribe((res) => {
            return resolve(res);
          },
          err => {
            console.log(err);
            return reject(err);
          }
        );
    });
  }
  getCustomerFromDB(id)
  {
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/masterData/getCustomerFromDB",{id}).subscribe((res) => {
            return resolve(res);
          },
          err => {
            console.log(err);
            return reject(err);
          }
        );
    });
  }

  getProjectAttachmentsConfigs(){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/masterData/getProjectAttachmentsConfigs",{}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  

  
}
getProjectRoleMaster(){
    
  return new Promise((resolve,reject)=>{
    if (this.projectRoleMasterList && this.projectRoleMasterList.length > 0) {
      return resolve(this.projectRoleMasterList);
    } else {
      this.http.post("/api/pm/masterData/getProjectRoleMaster",{}).subscribe((res)=>{
        if(res['messType']=="S")
        {
          this.projectRoleMasterList = res['data']
          return resolve(res)
        }
        else
        {
          return resolve([])
        }
      },(err)=>{
        return reject(err)
      })
    }
  })

}
getISATimesheetLocationsMaster() {
  return new Promise((resolve, reject) => {
    if (this.isaTimesheetLocations && this.isaTimesheetLocations.length > 0) {
      return resolve(this.isaTimesheetLocations);
    } else {
      this.http.post("/api/pm/masterData/getTimesheetLocations", {}).subscribe(
        (res: any) => {
          for(let r of res)
          {
            r['id'] = r['name']
          }
          this.isaTimesheetLocations = res;
          return resolve(res);
        },
        err => {
          return reject(err)
        }
      );
    }
  });
}
getreportsToMaster(projectID,currentDate){
    
  return new Promise((resolve, reject) => {
    this.http.post("/api/pm/masterData/getreportsToMaster",{projectID:projectID,currentDate:currentDate}).subscribe((res) => {
      return resolve(res)
    }, (err) => {
      return reject(err)
    })
  })

  }

  getopportunitySuggestionFromDB(text){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/masterData/getopportunitySuggestionFromDB",{text}).subscribe((res) => {
            return resolve(res);
          },
          err => {
            console.log(err);
            return reject(err);
          }
        );
    });
  }


  getOpportunityFromDB(id){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/masterData/getOpportunityFromDB",{id}).subscribe((res) => {
            return resolve(res);
          },
          err => {
            console.log(err);
            return reject(err);
          }
        );
    });
  }
  getProjectTypeList(){
    return new Promise((resolve,reject)=>{
      if (this.project_type_list && this.project_type_list.length > 0) {
        return resolve(this.project_type_list);
      } else {
        this.http.post("/api/pm/masterData/getProjectTypeList",{}).subscribe((res)=>{
          if(res['messType'] == 'S'){
            this.project_type_list = res['data']
            return resolve(this.project_type_list)
          }
        },(err)=>{
          return reject(err)
        })
      }
    })
  }
  personRoleList(){
    return new Promise((resolve,reject)=>{
      if (this.person_role_list && this.person_role_list.length > 0) {
        return resolve(this.person_role_list);
      } else {
        this.http.post("/api/pm/masterData/personRoleList",{}).subscribe((res)=>{
          if(res['messType'] == 'S'){
            this.person_role_list = res['data']
            return resolve(this.person_role_list)
          }
        },(err)=>{
          return reject(err)
        })
      }
    })
  }
  currencyList(){
    return new Promise((resolve,reject)=>{
      if (this.currency_list && this.currency_list.length > 0) {
        return resolve(this.currency_list);
      } else {
        this.http.post("/api/pm/masterData/currencyList",{}).subscribe((res)=>{
          if(res['messType'] == 'S'){
            this.currency_list = res['data']
            return resolve(this.currency_list)
          }
        },(err)=>{
          return reject(err)
        })
      }
    })
  }
  milestoneStatusList(){
    return new Promise((resolve,reject)=>{
      if (this.milestone_status_list && this.milestone_status_list.length > 0) {
        return resolve(this.milestone_status_list);
      } else {
        this.http.post("/api/pm/masterData/milestoneStatusList",{}).subscribe((res)=>{
          if(res['messType'] == 'S'){
            this.milestone_status_list = res['data']
            return resolve(this.milestone_status_list)
          }
        },(err)=>{
          return reject(err)
        })
      }
    })
  }

  serviceTypeList(){
    return new Promise((resolve,reject)=>{
      this.service_type_list=[];
      if (this.service_type_list && this.service_type_list.length > 0) {
        return resolve(this.service_type_list);
      } else {
        this.http.post("/api/pm/masterData/getProjectServiceType",{}).subscribe((res) => {
          if(res['messType']=="S")
          {
            this.service_type_list= res['data']
            return resolve(res['data'])
          }
          else
          {
            return resolve([])
          }
          
        }, (err) => {
          return reject(err)
        })
      }
    })
  }

  getTotalCustomerData(){
    return new Promise((resolve,reject)=>{
      this.http.post("/api/pm/masterData/getCustomerList",{}).subscribe((res)=>{
        if(res['messType'] == 'S'){
          this.customer_list = res['data']
          return resolve(this.customer_list)
        }
      },(err)=>{
        return reject(err)
      })
    })
  }
  getInvoiceTemplate(){
    return new Promise((resolve,reject)=>{
      if (this.invoice_template_list && this.invoice_template_list.length > 0) {
        return resolve(this.invoice_template_list);
      } else {
        this.http.post("/api/pm/masterData/getInvoiceTemplate",{}).subscribe((res)=>{
          if(res['messType'] == 'S'){
            this.invoice_template_list = res['data']
            return resolve(this.invoice_template_list)
          }
        },(err)=>{
          return reject(err)
        })
      }
    })
  }
  getWorkLocation(){
    return new Promise((resolve,reject)=>{
      if (this.work_location_list && this.work_location_list.length > 0) {
        return resolve(this.work_location_list);
      } else {
        this.http.post("/api/pm/masterData/getWorkLocation",{}).subscribe((res)=>{
          if(res['messType'] == 'S'){
            this.work_location_list = res['data']
            return resolve(this.work_location_list)
          }
        },(err)=>{
          return reject(err)
        })
      }
    })
  }
  getTemplateMaster(){
    return new Promise((resolve,reject)=>{
      if (this.template_master_list && this.template_master_list.length > 0) {
        return resolve(this.template_master_list);
      } else {
        this.http.post("/api/pm/masterData/getTemplateMaster",{}).subscribe((res)=>{
          if(res['messType'] == 'S'){
            this.template_master_list = res['data']
            return resolve(this.template_master_list)
          }
        },(err)=>{
          return reject(err)
        })
      }
    })
  }

  getCustomerNameList(){
    return new Promise((resolve,reject)=>{
      if (this.customer_name_list && this.customer_name_list.length > 0) {
        return resolve(this.customer_name_list);
      } else {
        this.getTotalCustomerNameList().then((res)=>{
          this.customer_name_list=res
        })
      }
    })
  }

  getTotalCustomerNameList(){
    return new Promise((resolve,reject)=>{
      this.http.post("/api/pm/masterData/getCustomerNameList",{}).subscribe((res)=>{
        if(res['messType'] == 'S'){
          this.customer_name_list = res['data']
          return resolve(this.customer_name_list)
        }
      },(err)=>{
        return reject(err)
      })
    })
  }
  
  getShiftList (){
    return new Promise((resolve,reject)=>{
      this.http.post("/api/pm/masterData/getShiftList",{}).subscribe((res)=>{
        if(res['messType'] == 'S'){
          this.shift_list = res['data']
          return resolve(this.shift_list)
        }
      },(err)=>{
        return reject(err)
      })
    })
  }


  getGanttTypeRollUp(){
    return new Promise((resolve, reject) => {
      if (this.ganttRollUp && this.ganttRollUp.length > 0) {
        return resolve(this.ganttRollUp);
      }
      else {
        this.http.post("/api/pm/board/getGanttTypeRollUp", "").subscribe(
          res => {
            this.ganttRollUp = res;
            return resolve(res);
          },
          err => {
            console.log(err)
            return resolve([]);
          }
        );
      }
    });
  }
  getUserProfileFromDBE360(aid)
  {
   
    return new Promise((resolve, reject) => {
      this.http
        .post("/api/employee360/employeeDetails/getUserProfileFromDBE360", { aid: aid })
        .subscribe(
          res => {
            return resolve(res);
          },
          err => {
            console.log(err);
            return reject(err);
          }
        );
    });
  }
  getUserSuggestionsFromDB(text)
  {
   
    return new Promise((resolve, reject) => {
      this.http
        .post("/api/employee360/employeeDetails/getUserSuggestionsFromDB", { searchText: text, date: moment().format("YYYY-MM-DD") })
        .subscribe(
          res => {
            return resolve(res);
          },
          err => {
            console.log(err);
            return reject(err);
          }
        );
    });
  }

  getGanttTypeList(){
    return new Promise((resolve, reject) => {
      if (this.ganttTypeList && this.ganttTypeList.length > 0) {
        return resolve(this.ganttTypeList);
      }
      else {
        this.http.post("/api/pm/masterData/getGanttTypeList", "").subscribe(
          res => {
            this.ganttTypeList = res;
            return resolve(res);
          },
          err => {
            console.log(err)
            return resolve([]);
          }
        );
      }
    });
  }
  getProfitLossList (){
    return new Promise((resolve,reject)=>{
      this.http.post("/api/pm/masterData/getProfitLossList",{}).subscribe((res)=>{
        if(res['messType'] == 'S'){
          this.p_and_l_list = res['data']
          return resolve(this.p_and_l_list)
        }
      },(err)=>{
        return reject(err)
      })
    })
  }

  getCountryList(){
    return new Promise((resolve,reject)=>{
      if (this.country_list && this.country_list.length > 0) {
        return resolve(this.country_list);
      } else {
        this.http.post("/api/pm/masterData/getCountryList",{}).subscribe((res)=>{
          if(res['messType'] == 'S'){
            this.country_list = res['data']
            return resolve(this.country_list)
          }
        },(err)=>{
          return reject(err)
        })
      }
    })
  }
  getReasonList(){
    return new Promise((resolve,reject)=>{
      if (this.reason_list && this.reason_list.length > 0) {
        return resolve(this.reason_list);
      } else {
        this.http.post("/api/pm/masterData/getReasonList",{}).subscribe((res)=>{
          if(res['messType'] == 'S'){
            this.reason_list = res['data']
            return resolve(this.reason_list)
          }
        },(err)=>{
          return reject(err)
        })
      }
    })
  }
  getPositionList(){
    return new Promise((resolve,reject)=>{
      if (this.position_list && this.position_list.length > 0) {
        return resolve(this.position_list);
      } else {
        this.http.post("/api/pm/masterData/getPositionList",{}).subscribe((res)=>{
          if(res['messType'] == 'S'){
            this.position_list = res['data']
            return resolve(this.position_list)
          }
        },(err)=>{
          return reject(err)
        })
      }
    })
  }

  getBillingTypeList(){
    return new Promise((resolve,reject)=>{
      if (this.billable_list && this.billable_list.length > 0) {
        return resolve(this.billable_list);
      } else {
        this.http.post("/api/pm/masterData/getBillingTypeList",{}).subscribe((res)=>{
          this.billable_list = res['data']
          return resolve(this.billable_list)
        },(err)=>{
          return reject(err)
        })
      }
    })
  }
  getExperianceList(){
    return new Promise((resolve,reject)=>{
      if (this.experiance_list && this.experiance_list.length > 0) {
        return resolve(this.experiance_list);
      } else {
        this.http.post("/api/pm/masterData/getExperianceList",{}).subscribe((res)=>{
          if(res['messType'] == 'S'){
            this.experiance_list = res['data']
            return resolve(this.experiance_list)
          }
        },(err)=>{
          return reject(err)
        })
      }
    })
  }
  getOfficeLocationList(){
    return new Promise((resolve,reject)=>{
      if (this.office_location_list && this.office_location_list.length > 0) {
        return resolve(this.office_location_list);
      } else {
        this.http.post("/api/pm/masterData/getOfficeLocationList",{}).subscribe((res)=>{
          if(res['messType'] == 'S'){
            this.office_location_list = res['data']
            return resolve(this.office_location_list)
          }
        },(err)=>{
          return reject(err)
        })
      }
    })
  }

  getFricewList(){
    return new Promise((resolve,reject)=>{
      if (this.fricew_list && this.fricew_list.length > 0) {
        return resolve(this.fricew_list);
      } else {
        this.http.post("/api/pm/masterData/getFricewList",{}).subscribe((res)=>{
          this.fricew_list = res
          return resolve(this.fricew_list)
        },(err)=>{
          return reject(err)
        })
      }
    })
  }

  getPaymentTermsList(){
    return new Promise((resolve,reject)=>{
      if (this.paymentTerms_list && this.paymentTerms_list.length > 0) {
        return resolve(this.paymentTerms_list);
      } else {
        this.http.post("/api/pm/masterData/getPaymentTermsList",{}).subscribe((res)=>{
          this.paymentTerms_list = res
          return resolve(this.paymentTerms_list)
        },(err)=>{
          return reject(err)
        })
      }
    })
  }

  validateUploadedAdaptTemplate(result_data){
    return new Promise((resolve, reject)=>{
      this.http.post("/api/pm/planning/ValidateProjectAdaptTemplate",{result_data: result_data}).subscribe((res)=>{
        return resolve(res)
      },(err)=>{
        return reject(err)
      })
    })
  }
  uploadAdaptTemplate(result_data,templateId){
    return new Promise((resolve, reject)=>{
      this.http.post("/api/pm/planning/uploadProjectAdaptTemplate",{result_data: result_data,templateId:templateId}).subscribe((res)=>{
        return resolve(res)
      },(err)=>{
        return reject(err)
      })
    })
  }
  uploadProjectTemplateName(templateName,aid,duration){
    return new Promise((resolve,reject)=>{
        this.http.post("/api/pm/planning/uploadProjectTemplateName",{templateName:templateName,aid:aid,duration:duration}).subscribe((res)=>{
          return resolve(res)
        },(err)=>{
          return reject(err)
        })
      })
  }
  ValidateSOWAndPortfolioUpload(result_data){
    return new Promise((resolve, reject)=>{
      this.http.post("/api/pm/planning/ValidateSOWAndPortfolioUpload",{result_data: result_data}).subscribe((res)=>{
        return resolve(res)
      },(err)=>{
        return reject(err)
      })
    })
  }
  ValidateMilestoneUpload(result_data){
    return new Promise((resolve, reject)=>{
      this.http.post("/api/pm/planning/ValidateMilestoneUpload",{result_data: result_data}).subscribe((res)=>{
        return resolve(res)
      },(err)=>{
        return reject(err)
      })
    })
  }
  uploadSowPortfolioProgram(result_data){
    return new Promise((resolve, reject)=>{
      this.http.post("/api/pm/planning/uploadSowPortfolioProgram",{result_data: result_data}).subscribe((res)=>{
        return resolve(res)
      },(err)=>{
        return reject(err)
      })
    })
  }
  uploadMilestoneProgram(result_data){
    return new Promise((resolve, reject)=>{
      this.http.post("/api/pm/planning/uploadMilestoneProgram",{result_data: result_data}).subscribe((res)=>{
        return resolve(res)
      },(err)=>{
        return reject(err)
      })
    })
  }

  getDivisionList(){
    return new Promise((resolve, reject)=>{
      this.http.post("/api/pm/masterData/getDivisionList",{}).subscribe((res)=>{
        return resolve(res)
      },(err)=>{
        return reject(err)
      })
    })
  }

  getSubDivisionList(){
    return new Promise((resolve, reject)=>{
      this.http.post("/api/pm/masterData/getSubDivisionList",{}).subscribe((res)=>{
        return resolve(res)
      },(err)=>{
        return reject(err)
      })
    })
  }


  getProjectIdentityList(){
    return new Promise((resolve, reject)=>{
      this.http.post("/api/pm/masterData/getProjectIdentityList",{}).subscribe((res)=>{
        return resolve(res)
      },(err)=>{
        return reject(err)
      })
    })
  }

  getProjectRules(){
    return new Promise((resolve, reject)=>{
      this.http.post("/api/pm/masterData/getProjectRules",{}).subscribe((res)=>{
        return resolve(res)
      },(err)=>{
        return reject(err)
      })
    })
  }

  getDayTypeList(){
    return new Promise((resolve, reject)=>{
      this.http.post("/api/pm/integration/getDayTypeList",{}).subscribe((res)=>{
        return resolve(res)
      },(err)=>{
        return reject(err)
      })
    })
  }

  getMilestoneTypeList(){
    return new Promise((resolve, reject)=>{
      this.http.post("/api/pm/financial/getMilestoneTypeList",{}).subscribe((res)=>{
        return resolve(res)
      },(err)=>{
        return reject(err)
      })
    })
  }
  getTags(){
    return new Promise((resolve, reject)=>{
      this.http.post("/api/pm/masterData/getTags",{}).subscribe((res)=>{
        return resolve(res)
      },(err)=>{
        return reject(err)
      })
    })
  }
  getStatusListDirect(){
    return new Promise((resolve,reject)=>{
      this.http.post("/api/pm/masterData/getWidgetStatusList",{}).subscribe((res)=>{
          return resolve(res)
        },(err)=>{
          return reject(err)
        })
    })
  }


  getProjectUserSuggestionsFromDB(text){
    return new Promise((resolve, reject) => {
      this.http
        .post("/api/pm/masterData/getProjectUserSuggestionsFromDB", { searchText: text, date: moment().format("YYYY-MM-DD") })
        .subscribe(
          res => {
            return resolve(res);
          },
          err => {
            console.log(err);
            return reject(err);
          }
        );
    });
  }

  getRegionList(){
    return new Promise((resolve,reject)=>{
      this.http.post("/api/pm/masterData/getRegionList",{}).subscribe((res)=>{
          return resolve(res)
        },(err)=>{
          return reject(err)
        })
    })
  }

  getISABillingTypeList(){
    return new Promise((resolve,reject)=>{
      
      this.http.post("/api/pm/masterData/getBillingTypeList",{}).subscribe((res)=>{
        return resolve(res['data'])
      },(err)=>{
        return reject(err)
      })
      
    })
  }
  getFinanacialTypeList(){
    return new Promise((resolve, reject)=>{
      this.http.post("/api/pm/masterData/getFinanacialTypeList",{}).subscribe((res)=>{
        return resolve(res)
      },(err)=>{
        return reject(err)
      })
    })
  }
  getProjectQuote(project_id,item_id){
    return new Promise((resolve, reject) => {
      this.http.post("/api/pm/planning/getProjectQuote",{project_id,item_id}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }
  getOrgMapping(){
    return new Promise((resolve, reject) => {
      this.http.post("/api/project/v2/getEmployeeDirectoryOrgMapping",{}).subscribe((res) => {
        return resolve(res)
      }, (err) => {
        return reject(err)
      })
    })
  }

  getProductCategory(){
    return new Promise((resolve,reject)=>{
      
      this.http.post("/api/pm/masterData/getProductCategoryList",{}).subscribe((res)=>{
        return resolve(res)
      },(err)=>{
        return reject(err)
      })
      
    })
  }

  getDeliveryTypeList(){
    return new Promise((resolve,reject)=>{
      
      this.http.post("/api/pm/masterData/getDeliveryTypeList",{}).subscribe((res)=>{
        return resolve(res)
      },(err)=>{
        return reject(err)
      })
      
    })
  }

  getRevenueTypeList(){
    return new Promise((resolve,reject)=>{
      
      this.http.post("/api/pm/masterData/getRevenueTypeList",{}).subscribe((res)=>{
        return resolve(res)
      },(err)=>{
        return reject(err)
      })
      
    })
  }


  getBillingArea(){
    return new Promise((resolve,reject)=>{
      
      this.http.post("/api/pm/masterData/getBillingArea",{}).subscribe((res)=>{
        if(res['messType'] == 'S'){
          this.billing_area_list = res['data']
          return resolve(this.billing_area_list)
        }
      },(err)=>{
        return reject(err)
      })
      
    })
  }
    
  getProjectList(){
    return new Promise((resolve, reject)=>{
      this.http.post("/api/pm/report/getEntireProjectLists",{}).subscribe((res)=>{
        resolve(res)
      },(err)=>{
        reject(err)
      })
    })
  }
  
    
  getProjectRegion(){
    return new Promise((resolve,reject)=>{
      
      this.http.post("/api/pm/masterData/getProjectRegion",{}).subscribe((res)=>{
        if(res['messType'] == 'S'){
          this.region_list = res['data']
          return resolve(this.region_list)
        }
      },(err)=>{
        return reject(err)
      })
      
    })
  }
  getProjectEngagement(){
    return new Promise((resolve,reject)=>{
      
      this.http.post("/api/pm/masterData/getProjectEngagement",{}).subscribe((res)=>{
        if(res['messType'] == 'S'){
          this.project_engagement_list = res['data']
          return resolve(this.project_engagement_list)
        }
      },(err)=>{
        return reject(err)
      })
      
    })
  }
  getProjectClassification(){
    return new Promise((resolve,reject)=>{
      
      this.http.post("/api/pm/masterData/getProjectClassification",{}).subscribe((res)=>{
        if(res['messType'] == 'S'){
          this.project_classification_list = res['data']
          return resolve(this.project_classification_list)
        }
      },(err)=>{
        return reject(err)
      })
      
    })
  }
  getloaderConfig(){
    return new Promise((resolve,reject)=>{
      
      this.http.post("/api/pm/masterData/getloaderConfig",{}).subscribe((res)=>{
        if(res['messType'] == 'S'){
          
          return resolve(res['data']['loading_gif'])
        }
      },(err)=>{
        return reject(err)
      })
      
    })
    
  }
  externalRoleList(){
    return new Promise((resolve,reject)=>{
      if (this.external_role_list && this.external_role_list > 0) {
        return resolve(this.external_role_list);
      } else {
        this.http.post("/api/pm/masterData/externalRoleList",{}).subscribe((res)=>{
          if(res['messType'] == 'S'){
            this.external_role_list = res['data']
            return resolve(this.external_role_list)
          }
        },(err)=>{
          return reject(err)
        })
      }
    })
  }
  getCommercialCatageroiesList(){
    return new Promise((resolve,reject)=>{
      
      this.http.post("/api/pm/masterData/getCommercialCatageroiesList",{}).subscribe((res)=>{
        if(res['messType'] == 'S'){
          this.commercialCatageroiesList = res['data']
          return resolve(this.commercialCatageroiesList)
        }
      },(err)=>{
        return reject(err)
      })
      
    })
  }


  getProjectStatusMatrix(){
    return new Promise((resolve,reject)=>{
      if (this.project_status_matrix && this.project_status_matrix.length > 0) {
        return resolve(this.project_status_matrix);
      } else {
        this.http.post("/api/pm/masterData/getStatusMatrix",{}).subscribe((res)=>{
          
            this.project_status_matrix = res
            return resolve(this.project_status_matrix)
          
        },(err)=>{
          return reject(err)
        })
      }
    })
  }

  getPriorityList(){
    return new Promise((resolve,reject)=>{
      
      this.http.post("/api/pm/masterData/getPriorityList",{}).subscribe((res)=>{
        return resolve(res)
      },(err)=>{
        return reject(err)
      })
      
    })
  }
  getProjectActivityStatus(){
    return new Promise((resolve,reject)=>{
      this.http.post("/api/pm/masterData/getProjectActivityStatus",{}).subscribe((res)=>{
        return resolve(res)
      },(err)=>{
        return reject(err)
      })
      
    })
  }


  getManPowerResourceType(){
    return new Promise((resolve,reject)=>{
      
      this.http.post("/api/pm/masterData/getManPowerResourceType",{}).subscribe((res)=>{
        return resolve(res)
      },(err)=>{
        return reject(err)
      })
      
    })
  }
  getProjectActivityPriority(){
    return new Promise((resolve,reject)=>{
      this.http.post("/api/pm/masterData/getProjectActivityPriority",{}).subscribe((res)=>{
        return resolve(res)
      },(err)=>{
        return reject(err)
      })
      
    })
  }

  getWorkCityList(){
    return new Promise((resolve,reject)=>{
      if (this.workCityList && this.workCityList.length > 0) {
        return resolve(this.workCityList);
      } else {
        this.getTotalWorkCityList().then((res)=>{
          this.workCityList=res
          return resolve(this.workCityList)
        })
        
      }
    })
  }

  getTotalWorkCityList(){
    return new Promise((resolve,reject)=>{
      this.http.post("/api/pm/masterData/getWorkCityList",{}).subscribe((res)=>{
        if(res['messType'] == 'S'){
          this.workCityList = res['data']
          return resolve(this.workCityList)
        }
      },(err)=>{
        return reject(err)
      })
    })
  }

  getWorkShiftList(){
    return new Promise((resolve,reject)=>{
      if (this.projectShiftMasterList && this.projectShiftMasterList.length > 0) {
        return resolve(this.projectShiftMasterList);
      } else {
        this.http.post("/api/pm/masterData/getWorkShiftList",{}).subscribe((res)=>{
          if(res['messType'] == 'S'){
            this.projectShiftMasterList = res['data']
            return resolve(this.projectShiftMasterList)
          }
        },(err)=>{
          return reject(err)
        })
      }
    })
  }

  getTimeZoneMasterList(){
    return new Promise((resolve,reject)=>{
      if (this.timezoneMasterList && this.timezoneMasterList.length > 0) {
        return resolve(this.timezoneMasterList);
      } else {
        this.http.post("/api/pm/masterData/getTimeZoneMasterList",{}).subscribe((res)=>{
          if(res['messType'] == 'S'){
            this.timezoneMasterList = res['data']
            return resolve(this.timezoneMasterList)
          }
        },(err)=>{
          return reject(err)
        })
      }
    })
  }
 

  getMilestoneListDetails(item_id,id){
    return new Promise((resolve,reject)=>{
      
      this.http.post("/api/pm/masterData/getMilestoneListDetails",{item_id:item_id,id:id}).subscribe((res)=>{
        return resolve(res)
      },(err)=>{
        return reject(err)
      })
      
    })
  }

  updateInvoiceDetails(ItemID,milestoneID, invoice_date, oldInvoiceDate, newInvoiceDate){
    return new Promise((resolve, reject)=>{
      this.http.post("/api/pm/financial/updateInvoiceDate",{project_item_id: ItemID,milestone_id: milestoneID, invoice_date: invoice_date,oldInvoiceDate: oldInvoiceDate,newInvoiceDate: newInvoiceDate,source_id:86}).subscribe((res)=>{
        return resolve(res)
      },(err)=>{
        return reject(err)
      })
    })
  }

  getOpportunityList(){
    return new Promise((resolve,reject)=>{
      this.http.post("/api/rmg/masterData/getOpportunityMaster",{}).subscribe((res) => {
        if(!res['err'])
        {
          return resolve(res['data'])
        }
        else
        {
          return resolve([])
        }
        
      },(err)=>{
        return reject(err)
      })
    })
  }
  validateMilestoneIds(result_data){
    return new Promise((resolve, reject)=>{
      this.http.post("/api/pm/planning/validateMilestoneIds",{result_data: result_data}).subscribe((res)=>{
        return resolve(res)
      },(err)=>{
        return reject(err)
      })
    })
  }
  uploadMilestoneStatus(result_data){
    return new Promise((resolve, reject)=>{
      this.http.post("/api/pm/planning/uploadMilestoneStatus",{result_data: result_data}).subscribe((res)=>{
        return resolve(res)
      },(err)=>{
        return reject(err)
      })
    })
  }
  getSoftwareList(){
    return new Promise((resolve,reject)=>{
      if (this.task_software_type && this.task_software_type.length > 0) {
        return resolve(this.task_software_type);
      } else {
        this.http.post("/api/pm/masterData/getSoftwareList",{}).subscribe((res)=>{
          this.task_software_type = res
          return resolve(this.task_software_type)
        },(err)=>{
          return reject(err)
        })
      }
    })
  }
}
