<div class="documents-page">

    <div class="loader-container" *ngIf="loading">
        <mat-spinner class="green-spinner" diameter="40"></mat-spinner>
    </div>

    <div *ngIf="!loading">
        <div class = "search-bar">
            <mat-form-field appearance="outline" width="Formwidth">
                <mat-label *ngIf="label">{{ label }}</mat-label>
                <input
                  matInput
                  placeholder="Search files/folders"
                  (keyup)="searchAttachments($event.target.value)"
                  [(ngModel)]="search_text"
                />
                <mat-icon
                  matSuffix
                  style="color: #66615B !important; font-size: 21px !important; cursor: pointer;"
                  (click)="resetSuggestion()"
                  >close</mat-icon
                >
            </mat-form-field>
        </div>
        <div class="document-list">
            <div *ngFor = "let row of tempDocRows; let index = index">
                <div class="row attach-row slide-from-down" *ngIf = "row.isActive && row.isVisible" [style.padding-left.px]="row.indent ? row.indent * 20 : 0">
                    <div class = "expand-column">
                        <span  *ngIf = "row.hasChild">
                            <mat-icon *ngIf ="!row.expandEnable" class="expand-icon" (click)="showChild(row)">
                                chevron_right
                            </mat-icon>
                            <mat-icon *ngIf ="row.expandEnable" class="expand-icon" (click)="showChild(row)">
                                expand_more
                            </mat-icon>
                        </span>
                    </div>
                    <div class="icon-column">
                            <img class="folder-icon" cv= "row.file_format == 'folder'" [src]="row.file_format | fileIcon" >
                            <img *ngIf = "row.file_format != 'folder'" [src]="row.file_format | fileIcon" style="height:30px; width:20px;">
                    </div>

                    <div class="row name-column">
                        
                        <div class="bold-text-display my-auto" *ngIf = "row.file_format == 'folder'" (click)="showChild(row)" style="cursor:pointer;" tooltip="{{row.name}}" placement="top" showDelay="500">
                            {{row.name | maxellipsis : 30}}
                        </div>
                        <div class="text-display my-auto" *ngIf = "row.file_format != 'folder'" (click)="openDocViewer(row)" style="cursor:pointer;" tooltip="{{row.name}}" placement="top" showDelay="500">
                            <span *ngIf = "row.document_type && (!nameConcatenation)" style="font-weight: 500;">
                                {{row.document_type | maxellipsis : 30}}
                            </span>
                            <span *ngIf = "row.document_type && (!nameConcatenation)">
                                -
                            </span>
                            {{row.name | maxellipsis : 30}} 
                        </div>

                        <div class="size-column my-auto" *ngIf = "row.sizeDisplay">
                            {{row.size}}
                        </div>
                    </div>
                    <div class = "date-column my-auto" *ngIf = "row.dateDisplay">
                        {{row.createdOn}}
                    </div>
                    <div class = "button-column my-auto">
                        <mat-icon class="doc-icons" *ngIf = "row.edit  && documentUpdateAccess" (click) = "editRow(row,'edit')">edit</mat-icon>
                        <mat-icon class="doc-icons" *ngIf = "row.attachFile  && documentUpdateAccess" (click) = "openAttachmentPage(row,'create')">attach_file</mat-icon>
                        <mat-icon class="doc-icons" *ngIf = "row.addFile  && documentUpdateAccess" (click) = "openCreateFolder(row, 'create')">add</mat-icon>
                        <mat-icon class="doc-icons" *ngIf = "row.download" (click)="downloadFile(row)">download</mat-icon>
                        <mat-icon class="doc-icons" *ngIf = "row.delete && (row.id != doc_rows[0].id)  && documentUpdateAccess" (click)="deleteRow(row, index)">delete</mat-icon>
                        <mat-icon class="doc-icons" *ngIf = "row.moreOptions  && documentUpdateAccess" [matMenuTriggerFor]="moreDropdownMenu">more_horiz</mat-icon>
                        <mat-menu class="drop-down" #moreDropdownMenu="matMenu">
                            <mat-option *ngFor="let option of more_option" [value]="option" class="options" >
                            {{ option}}
                            </mat-option>
                        </mat-menu>
                    </div>
                </div>
            </div> 
        </div>
    </div>
</div>