import { Component, HostListener, Input, OnInit } from '@angular/core';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { AttachFilesComponent } from './../attach-files/attach-files.component';
import { CreateFolderComponent } from './../create-folder/create-folder.component';
import { ChangeDetectorRef } from '@angular/core';
import { PmMasterService } from 'src/app/modules/project-management/services/pm-master.service';
import * as _ from 'underscore';
import { ToasterService } from 'src/app/services/toaster-service/toaster.service';
import { SharedLazyLoadedComponentsService } from 'src/app/modules/shared-lazy-loaded-components/services/shared-lazy-loaded-components.service';
const { v4: uuidv4 } = require('uuid');
import { SharedService } from "../../services/shared.service"
import * as moment from "moment";
import { DocViewerComponent } from 'src/app/modules/shared-lazy-loaded-components/attachment-mgmt/components/doc-viewer/doc-viewer.component';
import { UtilityService } from 'src/app/services/utility/utility.service';
import { Subject } from 'rxjs';
import { debounceTime } from 'rxjs/operators';
import { projectDialog } from "../../animation/projectDialog";
import { PmAuthorizationService } from '../../../services/pm-authorization.service';
import { ToasterMessageService } from 'src/app/modules/project-management/shared-lazy-loaded/components/toaster-message/toaster-message.service';

@Component({
  selector: 'app-documents',
  templateUrl: './documents.component.html',
  styleUrls: ['./documents.component.scss']
})
export class DocumentsComponent implements OnInit {
  @Input() projectId: any;
  @Input() itemId: any;
  @Input() source: any;
  customerSearchSubject: Subject<string> = new Subject();
  formConfig: any = [];
  row: any;
  index: any;
  newFolder: any;
  doc_rows: any = [];
  more_option: any = ['List & Search', 'Delete Folder', 'Client Visibility', 'Download An Export', 'View Folder Details', 'Permissions', 'Visible To All'];
  projectAttachmentConfig: any;
  projectFormConfig: any;
  loading: boolean=true;
  isDialogOpened: boolean=false;
  tempDocRows: any=[]
  search_text=""
  fileTypeDropDownDetails: any =[]  
  button: any;
  shades: any;
  nameConcatenation:boolean=false;
  documentUpdateAccess: boolean=false;
  fontStyle: any;
  fieldOutline: any;
  dialog_animation = {
    entryAnimation: {
      keyframes: [
        { transform: 'translateX(100%)' },
        { transform: 'translateX(0)' },
      ],
      keyframeAnimationOptions: {
        duration: 250,
        easing: 'ease-in-out',
      },
    },
    exitAnimation: {
      keyframes: [
        { transform: 'translateX(0)' },
        { transform: 'translateX(100%)' },
      ],
      keyframeAnimationOptions: {
        duration: 250,
        easing: 'ease-in-out',
      },
    },
  }
  constructor(
    private dialog: MatDialog,
    private cdr: ChangeDetectorRef,
    private pmMasterService: PmMasterService,
    private toasterService: ToasterMessageService,
    private sharedService: SharedLazyLoadedComponentsService,
    private pmSharedService: SharedService,
    private utilityService: UtilityService,
    private projectDialog: projectDialog,
    private authService: PmAuthorizationService
    ) { }

  async ngOnInit(): Promise<void> {
    this.calculateDynamicContentHeight();
    await this.authService.getReadWriteAccess(this.projectId, this.itemId).then(async(res)=>{
      if(res)
      {
        this.documentUpdateAccess =await this.authService.getProjectWiseObjectAccess(this.projectId, this.itemId, 68)
        console.log("Document UPdate Access", this.documentUpdateAccess)
      }
    })
  
    await this.pmMasterService.getProjectAttachmentsConfigs().then((res) => {
      if(res['messType']!="E")
      {
          let data = res['data']['aws']['attachment_plugin_config'];
          let tad = res['data']['tad']
          this.projectAttachmentConfig = data;
      }
      else
      {
        console.log("Attachment configuration not yet done!")
        this.toasterService.showError("Attachments not configured!")
      }
    });

    await this.pmMasterService.getPMFormCustomizeConfigV().then((res: any)=>{

        this.formConfig=res;
        const retrieveStyles = _.where(this.formConfig, { type: "project-theme", field_name: "styles", is_active: true });
        this.button = retrieveStyles.length > 0 ? retrieveStyles[0].data.button_color ? retrieveStyles[0].data.button_color : "#90ee90" : "#90ee90";
        this.shades = retrieveStyles.length > 0 ? retrieveStyles[0].data.shades_color ? retrieveStyles[0].data.shades_color : "#C9E3B4" : "#C9E3B4";
        document.documentElement.style.setProperty('--documentShades', this.shades)
        document.documentElement.style.setProperty('--documentButton', this.button)
        this.fontStyle = retrieveStyles.length > 0 ? retrieveStyles[0].data.font_style ? retrieveStyles[0].data.font_style : "Roboto" : "Roboto";
        document.documentElement.style.setProperty('--documentFont', this.fontStyle);
        this.fieldOutline = retrieveStyles.length > 0 ? retrieveStyles[0].data.field_outline_color ? retrieveStyles[0].data.field_outline_color : "#808080" : "#808080";
        document.documentElement.style.setProperty('--documentField', this.fieldOutline);
    })

    
    await this.pmSharedService.getProjectAttachmentList(this.projectId, this.itemId, this.source).then((res: any)=>{
      if(res['messType']=="S")
      {
        let data= res['data']
        this.doc_rows = this.formatAttachmentList(data)
        this.formatAtachmentRecords()
      }
      
    })
    await this.pmSharedService.checkDocumentType().then((res) => {
      if (res['messType'] == 'S'){
        this.nameConcatenation=true

      }})

    this.customerSearchSubject.pipe(debounceTime(600)).subscribe(async searchTextValue => {
      console.log("TExtValue",searchTextValue)
      this.tempDocRows = []
      this.tempDocRows = JSON.parse(JSON.stringify(this.doc_rows))

      for(let res of this.tempDocRows)
      {
        if((res['name'].toLowerCase()).match(searchTextValue.toLowerCase()))
        {
          res['isVisible']=true
        }
        else
        {
          res['isVisible']=false
        }
      }   
    });

    await this.pmSharedService.getDocumentTypeMaster(this.source).then((res: any)=>{
      if(res['messType']=="S")
      {
        this.fileTypeDropDownDetails = res['data']
      }
    })

    this.loading=false;
  
  }

 
  openCreateFolder(row: any, mode: any){
    this.row = row;


    this.index = this.doc_rows.findIndex(item => item['id'] === row.id);

    
    let dialogConfig = {
      data :{ 
        parentRow: this.row,
        formConfig: this.formConfig,
        mode: mode,
        data: row,
        color: this.button
     },
     panelClass:'custom-mat-dialog-panel',
     animation: this.dialog_animation
    }
    

    
    const dialogRef = this.projectDialog.open(CreateFolderComponent, dialogConfig);

    dialogRef.afterClosed().subscribe(async(message) => {

      if(message['messType']=="S")
      {
        this.newFolder = message.folderData;
        if (this.newFolder != '' && this.newFolder != undefined && message.mode == 'create') {
          let insertFolder = await this.insertFolder(this.newFolder['name'], this.row['id'])
        }
        else if(message.mode =='edit'){
          row['name'] = message['folderData']['name']
          row['oldData'] = message['folderData']['oldData']
          row['newData'] = message['folderData']['newData']
          row['source'] = this.source
          this.alterDocRows(row)
          this.pmSharedService.updateFolderName(row)
        }
      }
    });
  }

  openAttachmentPage(row: any, mode: any){
    this.row = row;
    let contextId = uuidv4()
    contextId = "PRJ_"+uuidv4()
   
   
    let dialogConfig={
  
      data:{ 
        parentRow: this.row,
        id: this.doc_rows.length + 1,
        formConfig: this.formConfig,
        projectAttachmentConfig: this.projectAttachmentConfig,
        contextId: contextId,
        mode: mode,
        data: row,
        color: this.button,
        fileTypeDropDown: this.fileTypeDropDownDetails
      },
      animation: this.dialog_animation
    }

    
    const dialogRef = this.projectDialog.open(AttachFilesComponent, dialogConfig);

    dialogRef.afterClosed().subscribe(async(res) => {
      if(res['messType']=="S")
      {

        if(res['mode']=="create")
        {
          let uploadedFiles: any = await this.retrieveUploadedFiles(this.projectAttachmentConfig['destination_bucket'], contextId)
          if(uploadedFiles && uploadedFiles.length>0)
          {
            for(let files of uploadedFiles)
            {
                files['description']= res['description']
                const fileDetails = _.find(res['filesUploaded'], item => item._id == files._id);
                files['fileType'] = fileDetails && fileDetails?.fileType ? fileDetails?.fileType : null;
            }
            let insertAttachment = await this.insertAttachmentFiles(uploadedFiles, this.row['id'], this.source)
            
          }
        }
        else if(res['mode']=="edit")
        {
          row['name'] = res['file_name']
          row['description'] = res['description']
          row['project_id'] = this.projectId
          row['item_id'] = this.itemId
          row['document_type'] = res['documentTypeName']
          row['document_type_id'] = res['documentType']
          row['oldData'] = res['oldData']
          row['newData'] = res['newData']
          row['source'] = this.source
          this.alterDocRows(row)
          let update = await this.pmSharedService.updateUploadedFiles(row)
        }
      }

   
    });
  }

  showChild(row: any){

    row['expandEnable'] = !row['expandEnable'];

    if(!row['expandEnable'])
    {
        this.hideRow(row['id'])
    }
    this.alterDocRows(row)

  }

  hideRow(id)
  {
      let hide = _.where(this.tempDocRows,{parent: id})

      for(let row of hide)
      {
          row['isVisible']=false
          row['expandEnable']=false
          this.alterDocRows(row)
          this.hideRow(row['id'])
      }
  }

  alterDocRows(row: any){
    this.index = this.doc_rows.findIndex(item => item['id'] === row.id);
    if(row.expandEnable == true){
      this.doc_rows[this.index].expandEnable = true;
      this.doc_rows[this.index].hasChild = _.where(this.doc_rows,{parent: this.doc_rows[this.index]['id'], isActive: true}).length>0 ? true : false
      this.doc_rows[this.index].delete = !this.doc_rows[this.index].hasChild
      const childRows = _.filter(this.doc_rows,{'parent': row.id});
      for(let child of childRows){
        child.isVisible = true
        child.indent = row.indent + 1;
        const childIndex = this.doc_rows.findIndex(item => item['id'] === child.id);
        this.doc_rows.splice(childIndex, 1); // Remove child from doc_rows
        this.index++; // Increment the index to insert after the parent
        this.doc_rows.splice(this.index, 0, child); // Push child under the parent
      }
      //console.log(childRows, this.doc_rows);
    }
    else{
      this.doc_rows[this.index].expandEnable = false;
      this.doc_rows[this.index].hasChild = _.where(this.doc_rows,{parent: this.doc_rows[this.index]['id'], isActive: true}).length>0 ? true : false
      this.doc_rows[this.index].delete = !this.doc_rows[this.index].hasChild
      const childRows = _.filter(this.doc_rows,{'parent': row.id});
      for(let child of childRows){
        const childIndex = this.doc_rows.findIndex(item => item['id'] === child.id);
        this.doc_rows[childIndex].isVisible = false;
      }
      //console.log(childRows, this.doc_rows);
    }
  }

  retrieveUploadedFiles(destinationBucket, contextId){
    return new Promise((resolve, reject)=>{
      this.sharedService.retrieveUploadedObjects(destinationBucket, contextId).subscribe((res: any) => {
            if(!res['err'])
              resolve(res['data'])
            else
              resolve([])
        },
        (err) => {
            reject(err)
        }
      );
    })
    
  }

  insertAttachmentFiles(uploadedFiles, parent, source){
    return new Promise((resolve, reject)=>{
      this.pmSharedService.insertAttachmentFiles(this.projectId, this.itemId, uploadedFiles, parent, source).then((res)=>{
          if(res['messType']=="S")
          {
              
              //this.doc_rows= this.formatAttachmentList(res['data'])
              let records = this.findNewlyAddedRecords(this.doc_rows, res['data'])
              records = this.formatAttachmentList(records)
              this.doc_rows = [...this.doc_rows,...records]
              this.formatAtachmentRecords()
              
  
          }
          resolve(res)
      },(err)=>{
        reject(err)
      })
    })
  }

  insertFolder(folderName, parent){
    return new Promise((resolve, reject)=>{
        this.pmSharedService.insertFolder(this.projectId, this.itemId, folderName, parent, this.source).then((res)=>{
          if(res['messType']=="S")
          {
              let records = this.findNewlyAddedRecords(this.doc_rows, res['data'])
              records = this.formatAttachmentList(records)
              console.log("New Added records",records)
              this.doc_rows = [...this.doc_rows,...records]

              console.log(this.doc_rows)
              this.formatAtachmentRecords()
              
          }
          resolve(res)
      },(err)=>{
        reject(err)
      })
    })
  }

  formatAttachmentList(data){
    
    for(let files of data)
    {
      files['hasChild'] = _.where(data,{parent: files['id']}).length>0 ? true : false
      files['expandEnable'] = files['parent'] == 0 ? true : false
      files['attachFile'] = files['is_folder'] == 1 ? true : false
      files['addFile'] = files['is_folder'] == 1 ? true : false
      files['edit'] = true
      files['moreOptions'] = false
      files['download'] = files['is_folder'] == 0 ? true : false
      files['delete'] = !files['hasChild']
      files['createdOn'] = moment(files['created_on']).format("DD-MMM-YYYY")
      files['dateDisplay'] = true
      files['isActive'] = true
      files['isVisible'] = true
      files['indent'] = files['parent'] == 0 ? 0 : 0
      
    }


    return data;
  }

  downloadFile(row){
    this.sharedService.getDownloadUrl(row.cdn_link).subscribe((res: any) => {
      if(!res['err'])
        window.open(res['data']);
      else
      {
        this.toasterService.showError("Unable to download file")
      }
    })
  }

  async deleteRow(row, index){
    let name = row.is_folder==1 ? "Folder" : "File"
    this.utilityService.openConfirmationSweetAlertWithCustom("Are you sure?","You want to delete this "+name+"!").then((result) => {
      if (result) {
        
        this.doc_rows[index]['isActive'] = false;
        this.tempDocRows[index]['isActive'] = false;

        this.pmSharedService.deleteAttachmentRow(this.projectId, this.itemId, row['id'], this.source)
        this.formatAtachmentRecords()
      }
    });

  }

  openDocViewer(row){
    let cdn_link = row.cdn_link;
    let uploadPopUpDialogRef = null;
    if(this.isDialogOpened == false){
      this.isDialogOpened = true;
    
      this.sharedService.getDownloadUrl(cdn_link).subscribe((res: any) => {
        uploadPopUpDialogRef = this.dialog.open(DocViewerComponent, {
          width: '100%',
          height: '100%',
          data: {
            selectedFileUrl: res.data,
            fileFormat: row.file_format,
            expHeaderId: ""
          },
        });

        uploadPopUpDialogRef.afterClosed().subscribe((res: any) => {
          this.isDialogOpened = false;
        });
      });
    }
  }


  formatAtachmentRecords(){
    for(let files of this.doc_rows){
      this.alterDocRows(files)
    }

    this.tempDocRows = this.doc_rows
    console.log("Temp Rows", this.doc_rows,this.tempDocRows)
  }

  findNewlyAddedRecords(doc_rows, new_rows){
    let new_ids = _.difference(_.pluck(new_rows,"id"),_.pluck(doc_rows,"id"))

    let temp=[]

    for(let id of new_ids)
    {
        let row = _.where(new_rows,{id: id})

        if(row.length>0)
        {
          temp.push(row[0])
        }
    }

    

    return temp
    
  }


  searchAttachments(text){
    console.log(text)
    if (text.trim() === "") 
    {
      this.tempDocRows = this.doc_rows;
    }
    else
    {
      this.customerSearchSubject.next(text);
    }
  }

  resetSuggestion(){
    this.tempDocRows = this.doc_rows
    this.search_text=""
  }

  editRow(row, mode){
    if(row['is_folder']==1)
    {
      this.openCreateFolder(row, mode)
    }
    else
    {
      this.openAttachmentPage(row, mode)
    }
  }

  calculateDynamicContentHeight() {
    let dynamicHeight = window.innerHeight - 231 + 'px';
    document.documentElement.style.setProperty(
      '--documentListHeight',
      dynamicHeight
    );
  }

  @HostListener('window:resize')
  onResize() {
    this.calculateDynamicContentHeight();
  }
}
