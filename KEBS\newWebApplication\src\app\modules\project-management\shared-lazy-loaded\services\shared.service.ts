import { Injectable } from '@angular/core';
import { BehaviorSubject, Subject } from 'rxjs';
import { HttpClient, HttpHeaders } from "@angular/common/http";
@Injectable({
  providedIn: 'root'
})
export class SharedService {
  
  //private showToastSubject = new Subject<any>();
  constructor(private http: HttpClient) { }


  // showToast$ = this.showToastSubject.asObservable();

  // showToast(data: any) {
  //   this.showToastSubject.next(data);
  // }

    getProjectAttachmentList(projectId, itemId, source){
      return new Promise((resolve, reject)=>{
        this.http.post("/api/pm/planning/getProjectAttachmentList",{project_id: projectId, item_id: itemId, source: source}).subscribe((res)=>{
          return resolve(res)
        },(err)=>{
          return reject(err)
        })
      })
    }
  
    insertAttachmentFiles(projectId, itemId, uploadedFiles, parent, source){
      return new Promise((resolve, reject)=>{
        this.http.post("/api/pm/planning/insertAttachmentFiles",{project_id: projectId, item_id: itemId, uploadedFiles: uploadedFiles, parent: parent, source: source}).subscribe((res)=>{
          return resolve(res)
        },(err)=>{
          return reject(err)
        })
      })
    }

    insertFolder(projectId, itemId, folderName, parent, source){
      return new Promise((resolve, reject)=>{
        this.http.post("/api/pm/planning/insertFolder",{project_id: projectId, item_id: itemId, folder_name: folderName, parent: parent, source: source}).subscribe((res)=>{
          return resolve(res)
        },(err)=>{
          return reject(err)
        })
      })
    }

    deleteAttachmentRow(project_id, item_id, id, source){
      return new Promise((resolve, reject)=>{
        this.http.post("/api/pm/planning/deleteAttachmentRow",{project_id, item_id, id: id, source: source}).subscribe((res)=>{
          return resolve(res)
        },(err)=>{
          return reject(err)
        })
      })
    }


    updateUploadedFiles(row){
      return new Promise((resolve, reject)=>{
        this.http.post("/api/pm/planning/updateAttachmentFiles",row).subscribe((res)=>{
          return resolve(res)
        },(err)=>{
          return reject(err)
        })
      })
    }


    updateFolderName(row){
      return new Promise((resolve, reject)=>{
        this.http.post("/api/pm/planning/updateFolder",row).subscribe((res)=>{
          return resolve(res)
        },(err)=>{
          return reject(err)
        })
      })
    }
    getExistingTags(){
      return new Promise((resolve, reject)=>{
        this.http.post("/api/pm/planning/getExistingTags",{}).subscribe((res)=>{
          return resolve(res)
        },(err)=>{
          return reject(err)
        })
      })
    }
    getTagsColor(){
      return new Promise((resolve, reject)=>{
        this.http.post("/api/pm/planning/getTagsColor",{}).subscribe((res)=>{
          return resolve(res)
        },(err)=>{
          return reject(err)
        })
      })
    }
    insertTag(name,color){
      return new Promise((resolve, reject)=>{
        this.http.post("/api/pm/planning/insertTag",{name,color}).subscribe((res)=>{
          return resolve(res)
        },(err)=>{
          return reject(err)
        })
      })
    }
    getTagsProject(item_id){
      return new Promise((resolve, reject) => {
        this.http.post("/api/pm/planning/getTagsProject",{item_id}).subscribe((res) => {
          return resolve(res)
        }, (err) => {
          return reject(err)
        })
      })
    }

    getMasterDataUsingApi(apiUrl:any,method:any,params:any){
      return new Promise((resolve,reject)=>{
        this.http[method](apiUrl,params).subscribe((res:any)=>{
          console.log(res)
          resolve(res)
        },(err)=>{
          console.log(err)
          reject(err)
        })
      })
    }

    
    getDocumentTypeMaster(source){
      return new Promise((resolve, reject)=>{
        this.http.post("/api/pm/masterData/getDocumentTypeMaster",{source: source}).subscribe((res)=>{
          return resolve(res)
        },(err)=>{
          return reject(err)
        })
      })
    }
  

    getEmployeeNames(associateIds){
      return new Promise((resolve, reject)=>{
        this.http.post("/api/pm/planning/getEmployeeNames",{associateIds}).subscribe((res)=>{
          return resolve(res)
        },(err)=>{
          return reject(err)
        })
      })
    }
    checkDocumentType(){
      return new Promise((resolve, reject) => {
        this.http.post("/api/pm/report/checkProjectDocument",{}).subscribe((res) => {
          return resolve(res)
        }, (err) => {
          return reject(err)
        })
      })
    }

}
