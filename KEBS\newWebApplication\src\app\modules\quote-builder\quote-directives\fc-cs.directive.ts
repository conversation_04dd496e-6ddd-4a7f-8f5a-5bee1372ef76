import { Directive, Input, <PERSON><PERSON><PERSON>roy, OnInit, SimpleChanges } from '@angular/core';
import { NgControl } from '@angular/forms';
import { Subscription } from 'rxjs';

@Directive({
  selector: '[appFcCs]',
})
export class FcCsDirective implements OnInit, OnDestroy {
  @Input() currency: string;
  @Input() showSuffix: boolean = false;
  @Input() suffix: string;
  @Input() decimalPart: number = 2;
  @Input() disabledD: boolean = false;
  @Input() maxDigits: number;

  valueSubscription: Subscription;
  constructor(private ngControl: NgControl) {}

  ngOnInit(): void {
    this.resolveFcCs(this.ngControl.control.value);

    this.valueSubscription = this.ngControl.control.valueChanges.subscribe(
      (value) => {
        if (value != null) this.resolveFcCs(value);
      }
    );
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.currency) {
      const currentValue = this.ngControl.control.value;
      this.resolveFcCs(currentValue);
    }
  }

  resolveFcCs = (value) => {
    if (value === null || value === undefined) {
      this.ngControl.valueAccessor.writeValue(0);
      this.ngControl.control.setValue(0, {
        emitEvent: false,
        emitModelToViewChange: false,
      });
      return;
    }

    let csValue = this.convertValueFormat(value);

    if (csValue !== undefined) {
      if (this.showSuffix) csValue += ` ${this.suffix ? this.suffix : this.currency}`;

      this.ngControl.valueAccessor.writeValue(csValue);
      this.ngControl.control.setValue(this.parseValue(value), {
        emitEvent: false,
        emitModelToViewChange: false,
      });
    } else {
      this.ngControl.valueAccessor.writeValue(0);
      this.ngControl.control.setValue(0, {
        emitEvent: false,
        emitModelToViewChange: false,
      });
    }
    this.setControlDisabledState(this.disabledD);
  };

  private setControlDisabledState(isDisabled: boolean): void {
    if (isDisabled) {
      this.ngControl.control.disable({ emitEvent: false });
    } else {
      this.ngControl.control.enable({ emitEvent: false });
    }
  }

  convertValueFormat = (inputValue: string | number): string | undefined => {
    if (typeof inputValue === 'number') {
      inputValue = inputValue.toString();
    }

    // Remove commas and unwanted characters
    inputValue = inputValue.replace(/,/g, '').replace(/[^0-9.-]/g, '');

    if (inputValue == '0-') return '-';
    else if (inputValue.endsWith('-')) inputValue = inputValue.slice(0, -1);

    let parts = inputValue.split('.');
    let integerPart = parts[0];
    let decimalPart = parts[1] !== undefined ? '.' + parts[1] : '';

    // Restrict number of digits before the decimal
    if (this.maxDigits && integerPart.length > this.maxDigits) {
      integerPart = integerPart.substring(0, this.maxDigits);
    }

    // Regular expression for decimal validation
    const decimalRegex = this.decimalPart === 0
      ? /^-?\d*$/
      : new RegExp(`^-?\\d*(\\.\\d{0,${this.decimalPart}})?$`);

    if (!decimalRegex.test(inputValue)) {
      if (parts.length > 1) {
        decimalPart = '.' + parts[1].substring(0, this.decimalPart);
      } else {
        decimalPart = '';
      }
    }

    if (isNaN(parseFloat(inputValue))) return undefined;

    // Format integer part based on currency
    if (this.currency === 'INR') {
      integerPart = new Intl.NumberFormat('en-IN').format(Number(integerPart));
    } else {
      integerPart = new Intl.NumberFormat('en-US').format(Number(integerPart));
    }

    return decimalPart && this.decimalPart > 0 ? `${integerPart}${decimalPart}` : integerPart;
  };

  parseValue = (input: string | number): number => {
    if (typeof input === 'string') {
      const sanitizedInput = input.trim();
      return sanitizedInput === '' ? 0.0 : parseFloat(sanitizedInput.replace(/,/g, ''));
    } else if (typeof input === 'number') return input;
    else return 0;
  };

  ngOnDestroy(): void {
    if (this.valueSubscription) this.valueSubscription.unsubscribe();
  }
}
