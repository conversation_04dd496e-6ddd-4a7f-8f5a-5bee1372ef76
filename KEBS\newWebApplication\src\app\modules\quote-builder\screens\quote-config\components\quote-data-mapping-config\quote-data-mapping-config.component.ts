import { Component, OnInit } from '@angular/core';
import { QuoteMainService } from 'src/app/modules/quote-builder/services/quote-main.service';
import { ToasterService } from 'src/app/services/toaster-service/toaster.service';

import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { QBMasterDataService } from 'src/app/modules/quote-builder/services/qb-master-data.service';
import { AccountService } from 'src/app/modules/account-sales/shared/services/account.service';


@Component({
  selector: 'app-quote-data-mapping-config',
  templateUrl: './quote-data-mapping-config.component.html',
  styleUrls: ['./quote-data-mapping-config.component.scss']
})
export class QuoteDataMappingConfigComponent implements OnInit {

  protected _onDestroy = new Subject<void>();

  isConfigLoading: boolean = false;

  workLocationList = [];
  entityList = [];
  serviceList = [];
  divisionList = [];
  revenueRegionList = [];
  workLocationEntityMappingList = [];
  revenueRegionEntitySalesMappingList = [];
  serviceDivisionMappingList = [];
  salesRegionList = [];
  booleanList = [
    {
      id: 1,
      name: 'Yes'
    },
    {
      id: 0,
      name: 'No'
    }
  ];

  columnResizingMode = "widget";
  revenueRegionMappingEnabled: boolean;

  constructor(private _quoteService: QuoteMainService, private _toaster: ToasterService,
    private _masterDataService: QBMasterDataService, private accountService: AccountService
    ) { }

  ngOnInit(): void {

    this.isConfigLoading = true;

    Promise.all([
      this.getWorkLocation(),
      this.getEntity(),
      this.getServices(),
      this.getDivisionList(),
      this.getGeographicalRegion(),
      this.getSalesRegionMaster()
    ]).then(() => {

      this.getWorkLocationEntityMappingList();
      this.getRevenueRegionEntitySalesMappingList();

    });

  }

  /**
   * @description Gets the Work Location list master data
   */
  getWorkLocation = () => {

    return new Promise((resolve, reject) => {

      this._masterDataService.workLocation
        .pipe(takeUntil(this._onDestroy))
        .subscribe(res => {

          this.workLocationList = res;

          resolve(true);

        });

    });

  }

  /**
   * @description Gets the Entity list master data
   */
  getEntity = () => {

    return new Promise((resolve, reject) => {

      this._masterDataService.entity
        .pipe(takeUntil(this._onDestroy))
        .subscribe(res => {

          this.entityList = res;

          resolve(true);

        });

    });

  }

    /**
   * @description Gets the Entity list master data
   */
    getGeographicalRegion = () => {

      return new Promise((resolve, reject) => {
  
        this._masterDataService.geographicalRegion
          .pipe(takeUntil(this._onDestroy))
          .subscribe(res => {
  
            this.revenueRegionList = res;
  
            resolve(true);
  
          });
  
      });
  
    }

  getServices() {

    return new Promise((resolve, reject) => {
      this._quoteService.getServices(true)
      .pipe(takeUntil(this._onDestroy))
      .subscribe(
        (res: any) => {

          this.serviceList = res.data;

          resolve(true);

        },
        (err) => {
          console.log(err);
          reject(err);
        }
      );
    });

  }

  /**
   * @description Gets the Division list master data
   */
  getDivisionList = () => {

    return new Promise((resolve, reject) => {

      this._masterDataService.division
        .pipe(takeUntil(this._onDestroy))
        .subscribe(res => {

          this.divisionList = res;

          resolve(true);

        },
          err => {
            console.log(err);
            reject(err);
          });

    });

  }

  getRevenueRegionEntitySalesMappingList = () => {

    this.isConfigLoading = true;

    this._quoteService.getRevenueRegionEntitySalesMappingList()
      .pipe(takeUntil(this._onDestroy))
      .subscribe(res => {

        if (res['messType'] == "S"){
          this.revenueRegionEntitySalesMappingList = res['data'];
          this.revenueRegionMappingEnabled = res['mappingEnabled'];
        }

        else
          this._toaster.showError("Error", "Error in getting Revenue Region, Sales region, Entity Mapping List", 3000);

      },
        err => {
          this.isConfigLoading = false;
          console.log(err);
          this._toaster.showError("Error", "Error in getting Revenue Region, Sales region, Entity Mapping List", 3000);
        });

  }

  getWorkLocationEntityMappingList = () => {

    this.isConfigLoading = true;

    this._quoteService.getQuoteWorkLocationEntityMapping()
      .pipe(takeUntil(this._onDestroy))
      .subscribe(res => {

        if (res['messType'] == "S")
          this.workLocationEntityMappingList = res['data'];

        else
          this._toaster.showError("Error", "Error in getting Work Location Entity Mapping List", 3000);

        this.getServiceDivisionMappingList();

      },
        err => {
          this.isConfigLoading = false;
          console.log(err);
          this._toaster.showError("Error", "Error in getting Work Location Entity Mapping List", 3000);
        });

  }

  getServiceDivisionMappingList = () => {

    this.isConfigLoading = true;

    this._quoteService.getQuoteServiceDivisionMapping()
      .pipe(takeUntil(this._onDestroy))
      .subscribe(res => {

        if (res['messType'] == "S")
          this.serviceDivisionMappingList = res['data'];

        else
          this._toaster.showError("Error", "Error in getting Service Division Mapping List", 3000);

        this.isConfigLoading = false;

      },
        err => {
          this.isConfigLoading = false;
          console.log(err);
          this._toaster.showError("Error", "Error in getting Service Division List", 3000);
        });

  }

  wERowInserted = (event) => {

    const data = event.data;

    this._quoteService.insertQuoteWorkLocationEntityMapping(data)
      .pipe(takeUntil(this._onDestroy))
      .subscribe(res => {

        if (res['messType'] == "S")
          this._toaster.showSuccess('Success', 'Quote Work Location Entity Mapping data added !', 2000);

        else
          this._toaster.showError("Error", res["messText"] || "Error in inserting Quote Work Location Entity data", 3000);

        this.getWorkLocationEntityMappingList();

      },
        err => {
          console.log(err);
          this._toaster.showError("Error", "Error in inserting Quote Work Location Entity data", 3000);
        });

  }

  wERowUpdated = (event) => {

    const data = event.data;

    this._quoteService.updateQuoteWorkLocationEntityMapping(data)
      .pipe(takeUntil(this._onDestroy))
      .subscribe(res => {

        if (res['messType'] == "S")
          this._toaster.showSuccess('Success', 'Quote Work Location Entity Mapping data updated Successfully !', 2000);

        else
          this._toaster.showError("Error", res["messText"] || "Error in updating Quote Work Location Entity Mapping data", 3000);

        this.getWorkLocationEntityMappingList();

      },
        err => {
          console.log(err);
          this._toaster.showError("Error", "Error in updating Quote Work Location Entity Mapping data", 3000);
        });

  }

  sDRowInserted = (event) => {

    const data = event.data;

    this._quoteService.insertQuoteServiceDivisionMapping(data)
      .pipe(takeUntil(this._onDestroy))
      .subscribe(res => {

        if (res['messType'] == "S")
          this._toaster.showSuccess('Success', 'Quote Service Division Mapping data added !', 2000);

        else
          this._toaster.showError("Error", res["messText"] || "Error in inserting Quote Service Division data", 3000);

        this.getServiceDivisionMappingList();

      },
        err => {
          console.log(err);
          this._toaster.showError("Error", "Error in inserting Quote Service Division data", 3000);
        });

  }

  sDRowUpdated = (event) => {

    const data = event.data;

    this._quoteService.updateQuoteServiceDivisionMapping(data)
      .pipe(takeUntil(this._onDestroy))
      .subscribe(res => {

        if (res['messType'] == "S")
          this._toaster.showSuccess('Success', 'Quote Service Division Mapping data updated Successfully !', 2000);

        else
          this._toaster.showError("Error", res["messText"] || "Error in updating Quote Service Division Mapping data", 3000);

        this.getServiceDivisionMappingList();

      },
        err => {
          console.log(err);
          this._toaster.showError("Error", "Error in updating Quote Service Division Mapping data", 3000);
        });

  }

  rSRowInserted = (event) => {

    const data = event.data;

    this._quoteService.insertQuoteEntitySalesRegionRevenueRegionMapping(data)
      .pipe(takeUntil(this._onDestroy))
      .subscribe(res => {

        if (res['messType'] == "S")
          this._toaster.showSuccess('Success', 'Quote Work Location Entity Mapping data added !', 2000);

        else
          this._toaster.showError("Error", res["messText"] || "Error in inserting Quote Entity, Sales region, Revenue/ Geographical Region Mapping", 3000);

        this.getRevenueRegionEntitySalesMappingList();

      },
        err => {
          console.log(err);
          this._toaster.showError("Error", "Error in inserting Quote Entity, Sales region, Revenue/ Geographical Region Mapping", 3000);
        });

  }

  rSRowUpdated = (event) => {

    const data = event.data;

    this._quoteService.updateQuoteEntitySalesRegionRevenueRegionMapping(data)
      .pipe(takeUntil(this._onDestroy))
      .subscribe(res => {

        if (res['messType'] == "S")
          this._toaster.showSuccess('Success', 'Error in updating Quote Entity, Sales region, Revenue/ Geographical Region Mapping', 2000);

        else
          this._toaster.showError("Error", res["messText"] || "Error in updating Quote Entity, Sales region, Revenue/ Geographical Region Mapping", 3000);

        this.getRevenueRegionEntitySalesMappingList();

      },
        err => {
          console.log(err);
          this._toaster.showError("Error", "Error in updating Quote Work Location Entity Mapping data", 3000);
        });

  }

  onEditorPreparing(e) {

    if (e.parentType === "dataRow" && e.dataField === "is_active")
      if (e.row.isNewRow) 
        e.editorOptions.disabled = true;

    if (e.parentType === "dataRow" && e.dataField === "work_location_id")
      if (!e.row.isNewRow) 
        e.editorOptions.disabled = true;

    if (e.parentType === "dataRow" && e.dataField === "service_id")
      if (!e.row.isNewRow) 
        e.editorOptions.disabled = true;
    
  }

  onInitNewRow(e) {

    e.data.is_active = 1;

  }

  calculateSortValue (data) {

    const column = this as any;

    const value = column.calculateCellValue(data);

    if (value === '' || value === null)
    	return value;

    if (column.hasOwnProperty('lookup'))
      return column.lookup.calculateCellValue(value);
  
    else
      return value;

  }

  validateWERow = (event, isInserting = false) => {

    const data = isInserting ? event.data : { ...event.oldData, ...event.newData };

    if (!data['work_location_id']) {

      event.cancel = true;

      return this._toaster.showError("Error", "Kindly Select Work Location !", 2000);

    }

    if (!data['entity_id']) {

      event.cancel = true;

      return this._toaster.showError("Error", "Kindly Select Entity !", 2000);

    }

    event.cancel = false;

  }

  validateSDRow = (event, isInserting = false) => {

    const data = isInserting ? event.data : { ...event.oldData, ...event.newData };

    if (!data['service_id']) {

      event.cancel = true;

      return this._toaster.showError("Error", "Kindly Select Service !", 2000);

    }

    if (!data['division_id']) {

      event.cancel = true;

      return this._toaster.showError("Error", "Kindly Select Division !", 2000);

    }

    event.cancel = false;

  }

  getSalesRegionMaster = () => {
    this.accountService.getSalesRegionMaster()
      .subscribe(res => {

        if (res['messType'] == "S" && res['data'])
          this.salesRegionList = res['data'];

      },
        err => {
          return this._toaster.showError("Error", "Kindly Select Division !", 2000);
        });


  }

  toggleRevenueRegionMapping(){
    this.revenueRegionMappingEnabled = !this.revenueRegionMappingEnabled;

    let updateConfig = [
      {
        config_name: "quote_geograpical_revenue_region_mapping_enable",
        config_value: this.revenueRegionMappingEnabled
      }
    ]
    
    this._quoteService.updateQuoteConfiguration(updateConfig)
      .pipe(takeUntil(this._onDestroy))
      .subscribe(res => {

        if (res['messType'] == "S")
          this._toaster.showSuccess('Success', `Geographical/Revenue Region Mapping ${this.revenueRegionMappingEnabled ? 'Enabled' : 'Disabled'} Successfully !`, 2000);

        else
          this._toaster.showError("Error", res["messText"] || "Error in updating Revenue Region Mapping", 3000);

      },
        err => {
          console.log(err);
          this._toaster.showError("Error", "Error in updating Revenue Region Mapping", 3000);
        });
  }

  ngOnDestroy() {
    this._onDestroy.next();
    this._onDestroy.complete();
  }

}
