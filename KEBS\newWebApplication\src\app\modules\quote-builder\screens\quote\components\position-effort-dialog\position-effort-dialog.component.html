<div class="container-fluid positions-effort-dialog-styles">
    <form [formGroup]="positionForm">

        <div class="row pt-2 pb-1">
            <div class="col-11 d-flex align-items-center header-class">
                Customize Slots
            </div>
            <div class="col-1">
                <button mat-icon-button [disabled]="isCalendarDataLoading" (click)="closeDialog()">
                    <mat-icon style="font-size: 15px;color: #5F6C81;">close</mat-icon>
                </button>
            </div>
        </div>

        <div class="row pt-1 pb-1">
            <div class="col-12 position-name">
                {{ positionForm.get('positionName').value }}
            </div>
        </div>

        <div class="row pt-1 pb-1">
            <div class="col-2 date-picker-class pr-0">
                <div class="header">Start Date</div>
                <mat-form-field class="date">
                    <mat-label class="item" style="font-weight: 300;" *ngIf="!positionForm.get('startDate').value">DD MMM YYYY</mat-label>
                    <input class="item" (click)="inData?.isEditMode ? picker1.open() : ''" [min]="inData.deliveryStartDate || null" [max]="positionForm.get('endDate').value" matInput formControlName="startDate" [matDatepicker]="picker1" readonly/>
                    <mat-datepicker #picker1></mat-datepicker>
                </mat-form-field>
            </div>
            <div class="col-2 date-picker-class pr-0">
                <div class="header">End Date</div>
                <mat-form-field class="date">
                    <mat-label class="item" style="font-weight: 300;" *ngIf="!positionForm.get('endDate').value">DD MMM YYYY</mat-label>
                    <input class="item" (click)="inData?.isEditMode ? picker2.open() : ''" [min]="positionForm.get('startDate').value" [max]="inData.deliveryEndDate || null" matInput formControlName="endDate" [matDatepicker]="picker2" readonly/>
                    <mat-datepicker #picker2></mat-datepicker>
                </mat-form-field>
            </div>
            <div class="col-4"></div>
            <div class="col-2">
                <div class="header">Unit</div>
                <div class="item">{{ positionForm.get('unit').value | displayValue : inData?.unitList : 'name' : 'id' }}
                </div>
            </div>
            <div class="col-2">
                <div class="header">Quantity</div>
                <div class="item">{{ positionForm.get('quantity').value }} {{ positionForm.get('unit').value |
                    displayValue : inData?.unitList : 'unit_suffix' : 'id' }}</div>
            </div>
        </div>

        <ng-container *ngIf="weekMonthData.length && !isCalendarDataLoading">

            <div class="row pt-2 pb-2" style="border-top: 1px solid #E8E9EE; color: #45546E;">
                <div class="col-12 d-flex justify-content-end align-items-center">
                    <mat-radio-group *ngIf="inData?.isEditMode && !unitConfig['is_value_fixed']" [formControl]="utilizeFormControl" class="radio-btn-class">
                        <mat-radio-button [ngClass]="utilizeFormControl.value == 1 ? 'radio-label-color' : ''" value="1">Utilize 100%</mat-radio-button>
                        <mat-radio-button [ngClass]="utilizeFormControl.value == 2 ? 'radio-label-color' : ''" class="pl-3" value="2">Custom %</mat-radio-button>
                    </mat-radio-group>
                    <mat-form-field *ngIf="utilizeFormControl.value == 2" style="width: 13%;font-size: 12px;" class="pl-2" appearance="outline">
                        <mat-select [placeholder]="'%'" [formControl]="customFormControl">
                            <mat-option *ngFor="let item of customPercentageList" [value]="item.value">{{ item.label }}</mat-option>
                          </mat-select>
                    </mat-form-field>
                </div>
            </div>

            <div class="row d-flex" style="border-bottom: 1px solid #E8E9EE;">
                <ng-container *ngFor="let headerItem of monthHeader; let headerIndex = index">
                    <div class="header" [ngClass]="[headerIndex == 0 ? 'col-3' : 'col-1x p-0', headerIndex == monthHeader.length - 1 ? 'd-flex justify-content-center' : '']">
                        {{ headerItem }}
                    </div>
                </ng-container>
            </div>

            <div style="height: 30vh;overflow-x: hidden;">
                <ng-container *ngFor="let weekMonthItem of weekMonthData; let weekMonthIndex = index">
                    <div class="row pt-1 d-flex align-items-center" style="border-bottom: 1px solid #E8E9EE;">
                        <div class="col-3">
                            <div class="item">{{ weekMonthItem?.monthLabel }}</div>
                            <div class="duration-class">{{ weekMonthItem?.duration }}</div>
                        </div>
                        <ng-container *ngFor="let weekItem of weekMonthItem?.weeks; let weekIndex = index">
                            <div class="item col-1x p-0">
                                <ng-container *ngIf="inData?.isEditMode; else showData">
                                    <mat-form-field *ngIf="weekItem?.maxValue; else showEmpty" class="form-field-class" appearance="outline">
                                        <input appFcCs [decimalPart]="7" matInput [formControl]="weekItem?.weekValue">
                                    </mat-form-field>
                                    <ng-template #showEmpty>-</ng-template>
                                </ng-container>
                                <ng-template #showData>
                                    {{ weekItem?.maxValue ? weekItem.weekValue.value : '-' }}
                                </ng-template>
                            </div>
                        </ng-container>
                        <div class="item col-1x p-0 d-flex justify-content-center">
                            {{ weekMonthItem?.weekTotal }}
                        </div>
                    </div>
                </ng-container>
            </div>

            <div *ngIf="inData?.isEditMode" class="row pt-2">
                <button [disabled]="isCalendarDataLoading" (click)="cancel()" mat-raised-button class="cancel-btn ml-2 mt-2">
                    Cancel
                </button>
                <button (click)="saveEffortDetails()" mat-raised-button class="create-btn ml-2 mt-2">
                    Save
                </button>
            </div>

        </ng-container>

        <div *ngIf="isCalendarDataLoading" class="row w-100 justify-content-center mt-5">
            <mat-spinner [diameter]="20" style="color:#cf0000"></mat-spinner>
        </div>
    </form>

</div>