import { Component, ElementRef, Inject, OnInit, Renderer2, ViewChild } from '@angular/core';

import { Subject, Subscription } from 'rxjs';
import { debounceTime, takeUntil, distinctUntilChanged } from 'rxjs/operators';

import { QuoteMainService } from '../../../../services/quote-main.service';
import { QBMasterDataService } from '../../../../services/qb-master-data.service';
import { ToasterService } from 'src/app/services/toaster-service/toaster.service';

import { OpportunityService } from "src/app/modules/opportunities/features/opportunities-detail/services/OpportunityService";

import * as moment from 'moment';

@Component({
  selector: 'app-position-effort-dialog',
  templateUrl: './position-effort-dialog.component.html',
  styleUrls: ['./position-effort-dialog.component.scss'],
  providers: [
    {
      provide: DateAdapter,
      useClass: MomentDateAdapter,
      deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS]
    },
    {
      provide: MAT_DATE_FORMATS,
      useValue: {
        parse: {
          dateInput: "DD MMM YYYY"
        },
        display: {
          dateInput: "DD MMM YYYY",
          monthYearLabel: "MMM YYYY"
        }
      }
    },
  ]
})
export class PositionEffortDialogComponent implements OnInit {

  positionForm: FormGroup;

  protected _onDestroy = new Subject<void>();

  valueChangeSubscription = new Subscription();
  formChangeSubscription = new Subscription();

  utilizeFormControl = new FormControl();
  customFormControl = new FormControl(0);

  weekMonthData = [];
  monthHeader = [];
  customPercentageList = [];

  isCalendarDataLoading = false;

  unitConfig = {};
  positionDetails: { startDate: any; endDate: any; quantity: any; positionEffortData: any[]; };

  constructor(
    public matDialogRef: MatDialogRef<PositionEffortDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public inData: any,
    private fb: FormBuilder,
    private _quoteMainService: QuoteMainService,
    private _toaster: ToasterService,
    private _masterDataService: QBMasterDataService,
    private renderer: Renderer2,
    private opportunityService: OpportunityService,
  ) {
    this.positionForm = this.fb.group({
      quoteId: [null],
      quotePositionId: [null],
      positionName: [null],
      startDate: [null],
      endDate: [null],
      unit: [null],
      quantity: [null],
      resourceTypeId: [null],
      positionLevel: [null]
    });
  }

  ngOnInit(): void {

    if (this.inData && this.inData.positionData) {

      this.positionForm.patchValue({
        quoteId: this.inData['positionData']['quoteId'] || null,
        quotePositionId: this.inData['positionData']['quotePositionId'] || null,
        positionName: this.inData['positionData']['positionName'],
        startDate: this.inData['positionData']['startDate'],
        endDate: this.inData['positionData']['endDate'],
        unit: this.inData['positionData']['unit'],
        quantity: this.inData['positionData']['quantity'],
        resourceTypeId: this.inData['positionData']['resourceTypeId'],
        positionLevel: this.inData['positionData']['positionLevel']
      });

      if (this.inData.unitList)
        this.unitConfig = this.inData.unitList.find(val => val['id'] === this.positionForm.get('unit').value);

      this.getCalendarDataForTheDuration();

      this.getCustomPercentage();

    }

    this.formChangeSubscription.add(this.positionForm.get('startDate').valueChanges.pipe(debounceTime(100), distinctUntilChanged(), takeUntil(this._onDestroy)).subscribe(res => {

      if (res)
        this.getCalendarDataForTheDuration(true);

    }));

    this.formChangeSubscription.add(this.positionForm.get('endDate').valueChanges.pipe(debounceTime(100), distinctUntilChanged(), takeUntil(this._onDestroy)).subscribe(res => {

      if (res)
        this.getCalendarDataForTheDuration(true);

    }));

    this.formChangeSubscription.add(this.utilizeFormControl.valueChanges.pipe(debounceTime(100), takeUntil(this._onDestroy)).subscribe(res => {

      if (res) {

        if (res == 1)
          this.utilizePercentage(100);

        else
          this.customFormControl.setValue(null, { emitEvent: false });

      }

    }));

    this.formChangeSubscription.add(this.customFormControl.valueChanges.pipe(debounceTime(100), takeUntil(this._onDestroy)).subscribe(res => {

      if (res)
        this.utilizePercentage(res);

    }));

  }

  /**
   * @description Gets the Custom percentage list master data
   */
  getCustomPercentage = () => {

    this._masterDataService.customPercentage
      .pipe(takeUntil(this._onDestroy))
      .subscribe(res => {

        this.customPercentageList = res;

      });

  }

  closeDialog = () => {

    this.matDialogRef.close({ event: 'close', data: this.positionDetails  });

  }

  cancel = () => {

    this.matDialogRef.close({ event: 'cancel', data: this.positionDetails  });

  }

  /**
   * @description Gets the calendar data for the duration
   * @params durationChanged indicates change of dates in dialog
   */
  getCalendarDataForTheDuration = (durationChanged=false) => { 

    let startDate = this.positionForm.get('startDate').value;
    let endDate = this.positionForm.get('endDate').value;

    if (startDate && endDate) {

      if (this.unitConfig && this.unitConfig['unit_value_in_hrs'] > 24 && !this.unitConfig['is_value_fixed']) {

        startDate = moment(startDate).startOf('month').toISOString();

        endDate = moment(endDate).endOf('month').toISOString();

      }

      this.isCalendarDataLoading = true;

      let fetchSavedData = true;

      if (this.inData['positionData']['isUnitChanged'] || this.inData['positionData']['isQuantityChanged'] || durationChanged)
        fetchSavedData = false;

      let positionDetails = {
        quoteId: this.positionForm.get('quoteId').value,
        quotePositionId: this.positionForm.get('quotePositionId').value,
        resourceTypeId: this.positionForm.get('resourceTypeId').value,
        positionLevel: this.positionForm.get('positionLevel').value
      }

      this._quoteMainService.getQuoteCalendarData(startDate, endDate, positionDetails, fetchSavedData, this.inData.calendarId, this.inData.workScheduleId)
        .pipe(takeUntil(this._onDestroy))
        .subscribe(res => {

          if (res['messType'] == "S" && res['data'] && res['data'].length) {

            let durationData = res['data'];

            this.positionDetails = {
              startDate: this.positionForm.get('startDate').value,
              endDate: this.positionForm.get('endDate').value,
              quantity: this.positionForm.get('quantity').value,
              positionEffortData: durationData
            }

            if (this.inData['positionData']['positionEffortData'] && fetchSavedData)
              this.patchExistingData(durationData, this.inData['positionData']['positionEffortData']);

            this.constructWeekMonthData(durationData);

          }

          else
            this._toaster.showError("Error", res["messText"] || "Error in getting Quote Calendar details", this.opportunityService.mediumInterval);

          this.isCalendarDataLoading = false;

        },
          err => {
            console.log(err);
            this.isCalendarDataLoading = false;
            this._toaster.showError("Error", "Error in getting Quote Calendar details", this.opportunityService.mediumInterval);
            this.closeDialog();
          });

    }

  }

  /**
   * @description Constructs the week month calendar array
   * @param durationData 
   */
  constructWeekMonthData = (durationData) => {

    let maxWeekNumber = 1, minWeekNumber = 0;
    this.monthHeader = ['Month'];

    if (this.valueChangeSubscription)
      this.valueChangeSubscription.unsubscribe();

    this.valueChangeSubscription = new Subscription();

    this.weekMonthData = durationData.reduce((prev_value, curr_value, index) => {

      if (minWeekNumber == 0 || curr_value['week'] < minWeekNumber)
        minWeekNumber = curr_value['week'];

      if (curr_value['week'] > maxWeekNumber)
        maxWeekNumber = curr_value['week'];

      let month_year = prev_value.find(val => {
        return curr_value['month'] == val['month'] && curr_value['year'] == val['year'];
      });

      if (!month_year) {

        let dayValue = curr_value['day_value'] || 0;

        // if (this.unitConfig['is_value_fixed'])
        //   dayValue = this.unitConfig['unit_value_in_hrs'] || 0;

        const monthObj = {
          month: curr_value['month'],
          year: curr_value['year'],
          monthLabel: moment(curr_value['month'].toString(), 'M').format("MMM"),
          weekTotal: dayValue,
          weeks: [{
            week: curr_value['week'],
            weekValue: new FormControl(dayValue),
            dates: [
              {
                date: curr_value['date'],
                type: curr_value['type'],
                maxHours: curr_value['max_hours'],
                dayValue: curr_value['day_value'] || 0
              }
            ],
            regularDays: curr_value['type'] == "R" ? 1 : 0,
            maxValue: curr_value['max_hours'] || 0
          }],
          monthHours: curr_value['max_hours']
        }

        if (!this.unitConfig['is_value_fixed'] && this.unitConfig['unit_value_in_hrs'] > 24 && !moment(curr_value['date']).isBetween(this.positionForm.get('startDate').value, this.positionForm.get('endDate').value, undefined, '[]'))
          monthObj['weeks'] = [];

        prev_value.push(monthObj);

        if (monthObj['weeks'].length)
          this.resolveValueChangeSubscription(prev_value[prev_value.length - 1]['weeks'][0]['weekValue'], prev_value[prev_value.length - 1], prev_value[prev_value.length - 1]['weeks'][0]);

      }

      else if (this.unitConfig['unit_value_in_hrs'] > 24) { //month

        if (!this.unitConfig['is_value_fixed']) {

          month_year['monthHours'] += curr_value['max_hours'];

          if (moment(curr_value['date']).isBetween(this.positionForm.get('startDate').value, this.positionForm.get('endDate').value, undefined, '[]')) {

            if (month_year['weeks'].length == 0) {

              month_year['weeks'].push({
                week: curr_value['week'],
                weekValue: new FormControl(curr_value['day_value'] || 0),
                dates: [
                  {
                    date: curr_value['date'],
                    type: curr_value['type'],
                    maxHours: curr_value['max_hours'],
                    dayValue: curr_value['day_value'] || 0
                  }
                ],
                regularDays: curr_value['type'] == "R" ? 1 : 0,
                maxValue: curr_value['max_hours'] || 0
              });

              this.resolveValueChangeSubscription(month_year['weeks'][month_year['weeks'].length - 1]['weekValue'], prev_value[prev_value.length - 1], month_year['weeks'][month_year['weeks'].length - 1]);

            }

            else {

              let monthlyWeek = month_year.weeks[0];

              monthlyWeek.maxValue += curr_value['max_hours'] || 0;
              monthlyWeek.regularDays += curr_value['type'] == "R" ? 1 : 0;

              if (curr_value['day_value']) {

                month_year.weekTotal = this.getFixedNumber(month_year.weekTotal + (curr_value['day_value']), index == durationData.length - 1 ? 1 : 2);

                monthlyWeek.weekValue.patchValue(this.getFixedNumber(monthlyWeek.weekValue.value + (curr_value['day_value']), index == durationData.length - 1 ? 1 : 2), { emitEvent: false });

              }

              monthlyWeek.dates.push({
                date: curr_value['date'],
                type: curr_value['type'],
                maxHours: curr_value['max_hours'],
                dayValue: curr_value['day_value'] || 0
              });

            }

          }

        }

        else {

          let monthlyWeek = month_year.weeks[0];

          monthlyWeek.maxValue += curr_value['max_hours'] || 0;
          monthlyWeek.regularDays += curr_value['type'] == "R" ? 1 : 0;

          if (curr_value['day_value']) {

            month_year.weekTotal = this.getFixedNumber(month_year.weekTotal + (curr_value['day_value'] / this.unitConfig['unit_value_in_hrs']), index == durationData.length - 1 ? 1 : 2);

            monthlyWeek.weekValue.patchValue(this.getFixedNumber(monthlyWeek.weekValue.value + (curr_value['day_value'] / this.unitConfig['unit_value_in_hrs']), index == durationData.length - 1 ? 1 : 2), { emitEvent: false });

          }

          monthlyWeek.dates.push({
            date: curr_value['date'],
            type: curr_value['type'],
            maxHours: curr_value['max_hours'],
            dayValue: curr_value['day_value'] || 0
          });

        }

      }

      else {

        let week_month_year = month_year.weeks.find(val => {
          return curr_value['week'] == val['week'];
        });

        if (!week_month_year) {

          month_year.weeks.push({
            week: curr_value['week'],
            weekValue: new FormControl(curr_value['day_value'] || 0),
            dates: [
              {
                date: curr_value['date'],
                type: curr_value['type'],
                maxHours: curr_value['max_hours'],
                dayValue: curr_value['day_value'] || 0
              }
            ],
            regularDays: curr_value['type'] == "R" ? 1 : 0,
            maxValue: curr_value['max_hours'] || 0
          });

          if (curr_value['day_value'])
            month_year.weekTotal = this.getFixedNumber(month_year.weekTotal + (curr_value['day_value']));

          this.resolveValueChangeSubscription(month_year['weeks'][month_year['weeks'].length - 1]['weekValue'], prev_value[prev_value.length - 1], month_year['weeks'][month_year['weeks'].length - 1]);

        }

        else {

          week_month_year.maxValue += curr_value['max_hours'] || 0;
          week_month_year.regularDays += curr_value['type'] == "R" ? 1 : 0;

          if (curr_value['day_value']) {

            month_year.weekTotal = this.getFixedNumber(month_year.weekTotal + (curr_value['day_value']));

            week_month_year.weekValue.patchValue(this.getFixedNumber(week_month_year.weekValue.value + (curr_value['day_value'])), { emitEvent: false });

          }

          week_month_year.dates.push({
            date: curr_value['date'],
            type: curr_value['type'],
            maxHours: curr_value['max_hours'],
            dayValue: curr_value['day_value'] || 0
          });

        }

      }

      return prev_value;

    }, []);

    let totalValue = 0, totalRegularDays = 0;

    for (const weekMonthItem of this.weekMonthData) {

      const startDate = weekMonthItem['weeks'][0]['dates'][0]['date'];

      const lastWeek = weekMonthItem['weeks'].length - 1;
      const endDate = weekMonthItem['weeks'][lastWeek]['dates'][weekMonthItem['weeks'][lastWeek]['dates'].length - 1]['date'];

      weekMonthItem['duration'] = `${moment(startDate).format("DD MMM")} - ${moment(endDate).format("DD MMM YYYY")}`;

      const currentWeekNumbers = weekMonthItem['weeks'].map(val => val['week']);

      weekMonthItem['weekTotal'] = 0;

      for (const weekItem of weekMonthItem['weeks']) {

        let displayValue = 0;

        if (this.unitConfig['unit_value_in_hrs'] > 24 && !this.unitConfig['is_value_fixed'])
          displayValue = weekItem['weekValue'].value / weekMonthItem['monthHours'];

        else if (!this.unitConfig['is_value_fixed'])
          displayValue = weekItem['weekValue'].value / this.unitConfig['unit_value_in_hrs'];

        displayValue = this.getFixedNumber(displayValue);

        weekItem['weekValue'].patchValue(displayValue, { emitEvent: false });

        weekMonthItem['weekTotal'] = this.getFixedNumber(weekMonthItem['weekTotal'] + displayValue);

      }

      totalRegularDays += weekMonthItem['weeks'].reduce((prev_value, curr_value) => prev_value + curr_value['regularDays'], 0);

      if (this.unitConfig['unit_value_in_hrs'] < 24) {

        for (let i = minWeekNumber; i <= maxWeekNumber; i++)
          if (!currentWeekNumbers.includes(i))
            weekMonthItem['weeks'].splice(i - 1, 0, {
              week: i,
              weekValue: new FormControl(0),
              dates: [],
              maxValue: 0
            });

      }

      totalValue += weekMonthItem['weekTotal'];

    }

    if (totalValue == 0 && totalRegularDays > 0 && this.positionForm.get('quantity').value > 0) {

      if (this.unitConfig['unit_value_in_hrs'] < 24) {

        const dayValue = this.positionForm.get('quantity').value / totalRegularDays * this.unitConfig['unit_value_in_hrs'];

        this.utilizePercentage(0, dayValue);

      }

      else {

        for (const weekMonthItem of this.weekMonthData) {

          let dayValue = 0;

          if (!this.unitConfig['is_value_fixed'])
            dayValue = (this.positionForm.get('quantity').value / this.weekMonthData.length) / weekMonthItem['weeks'][0]['regularDays'] * weekMonthItem['monthHours'];

          else
            dayValue = (this.positionForm.get('quantity').value / this.weekMonthData.length) / weekMonthItem['weeks'][0]['regularDays'];

          this.utilizePercentage(0, dayValue, [weekMonthItem]);

        }   

      }

    }

    if (this.unitConfig['unit_value_in_hrs'] > 24)
      this.monthHeader.push('Monthly', 'Total');

    else if (this.unitConfig['unit_value_in_hrs']) {

      for (let i = minWeekNumber; i <= maxWeekNumber; i++)
        this.monthHeader.push(`W${i}`);

      this.monthHeader.push('Total');

    }

  }

  /**
   * @description Patches the pre filled data
   * @param durationData 
   * @param positionEffortData 
   */
  patchExistingData = (durationData, positionEffortData) => {

    for (const durationItem of durationData) {

      const valueItem = positionEffortData.find(val => moment(val['date']).isSame(durationItem['date']));

      if (valueItem)
        durationItem['day_value'] = valueItem['day_value'];

    }

  }

  /**
   * @description Resolves the week value change subscription
   * @param weekValueControl 
   * @param weekMonthItem 
   * @param weekItem
   */
  resolveValueChangeSubscription = (weekValueControl: FormControl, weekMonthItem, weekItem) => { //unit based validation remaining

    this.valueChangeSubscription.add(weekValueControl.valueChanges.pipe(distinctUntilChanged(), debounceTime(500), takeUntil(this._onDestroy)).subscribe(val => {

      if (val === null || val === "") {
        return weekValueControl.patchValue(0, { emitEvent: false });
      } else if (val < 0) {
        return weekValueControl.patchValue(0, { emitEvent: false });
      } else if (parseFloat(val) >= 0) {
        // weekValueControl.patchValue(parseFloat(val.toString()), { emitEvent: false });
      }

      // if (val < 0 || val === "" || val === null)
      //   return weekValueControl.patchValue(0);

      // if (parseFloat(val) >= 0)
      //   weekValueControl.patchValue(parseFloat(val.toString()), { emitEvent: false });

        // weekValueControl.setValue(val, { emitEvent: false });

      if (this.unitConfig['is_value_fixed']) {

        const unitValue = this.unitConfig['unit_value_in_hrs'];

        if (typeof val == 'number')
          weekValueControl.patchValue(Math.ceil(val / unitValue) * unitValue, { emitEvent: false });

      }

      weekMonthItem['weekTotal'] = 0;

      for (const weekTotalItem of weekMonthItem['weeks'])
        weekMonthItem['weekTotal'] = this.getFixedNumber(weekMonthItem['weekTotal'] + weekTotalItem['weekValue'].value);

      let totalHours = 0;

      for (const weekMonthItem of this.weekMonthData)
        totalHours += weekMonthItem['weekTotal'];

      this.positionForm.get('quantity').patchValue(totalHours);

      let dayValue = weekValueControl.value / weekItem['regularDays'];

      if (this.unitConfig['unit_value_in_hrs'] <= 24)
        dayValue = dayValue * this.unitConfig['unit_value_in_hrs'];

      else if (!this.unitConfig['is_value_fixed'])
        dayValue = weekValueControl.value / weekItem['regularDays'] * weekMonthItem['monthHours'];

      for (const dayItem of weekItem['dates'])
        dayItem['dayValue'] = dayItem['type'] == 'R' ? dayValue : 0;

    }));

  }

  utilizePercentage = (percentage: number, dayValue = null, weekMonthData = []) => {

    let totalHours = 0;

    for (const weekMonthItem of (weekMonthData.length ? weekMonthData : this.weekMonthData)) {

      weekMonthItem['weekTotal'] = 0;

      for (const weekItem of weekMonthItem['weeks']) {

        weekItem['weekValue'].patchValue(0, { emitEvent: false });

        for (const dateItem of weekItem['dates']) {

          const dayHours = dayValue != null ? (dateItem['type'] == "R" ? dayValue : 0) : (percentage / 100) * dateItem['maxHours'];

          dateItem['dayValue'] = dayHours;

          weekMonthItem['weekTotal'] = weekMonthItem['weekTotal'] + dayHours;

          weekItem['weekValue'].patchValue(weekItem['weekValue'].value + dayHours, { emitEvent: false });

        }

        if (!this.unitConfig['is_value_fixed']) {

          if (this.unitConfig['unit_value_in_hrs'] > 24)
            weekItem['weekValue'].patchValue(this.getFixedNumber(weekItem['weekValue'].value / weekMonthItem['monthHours'], this.unitConfig['unit_value_in_hrs'] > 24 ? 1 : 2), { emitEvent: false });

          else
            weekItem['weekValue'].patchValue(this.getFixedNumber(weekItem['weekValue'].value / this.unitConfig['unit_value_in_hrs'], this.unitConfig['unit_value_in_hrs'] > 24 ? 1 : 2), { emitEvent: false });

        }

        else
          weekItem['weekValue'].patchValue(this.getFixedNumber(weekItem['weekValue'].value), { emitEvent: false });

      }

      if (!this.unitConfig['is_value_fixed']) {

        if (this.unitConfig['unit_value_in_hrs'] > 24)
          weekMonthItem['weekTotal'] = this.getFixedNumber(weekMonthItem['weekTotal'] / weekMonthItem['monthHours'], this.unitConfig['unit_value_in_hrs'] > 24 ? 1 : 2);

        else
          weekMonthItem['weekTotal'] = this.getFixedNumber(weekMonthItem['weekTotal'] / this.unitConfig['unit_value_in_hrs'], this.unitConfig['unit_value_in_hrs'] > 24 ? 1 : 2);

      }

      else
        weekMonthItem['weekTotal'] = this.getFixedNumber(weekMonthItem['weekTotal']);

      totalHours += weekMonthItem['weekTotal'];

    }

    if (dayValue == null)
      this.positionForm.get('quantity').patchValue(totalHours);

  }

  /**
   * @description Fixes the decimal places of a number
   * @param number 
   * @returns Fixed decimal
   */
  getFixedNumber = (number, noOfDecimals = 2) => {

    return parseFloat(number.toFixed(noOfDecimals));

  }

  /**
   * @description Saves the effort details
   */
  saveEffortDetails = () => { //day_value to be computed based on unit

    let positionEffortData = [], dayValue = null;

    if (this.unitConfig['is_value_fixed'])
      dayValue = this.getAverageValueForFixedType();

    for (const weekMonthItem of this.weekMonthData)
      for (const weekItem of weekMonthItem['weeks'])
        for (const dayItem of weekItem['dates'])
          positionEffortData.push({
            date: dayItem['date'],
            type: dayItem['type'],
            day_value: dayItem['type'] == "R" && dayValue != null ? dayValue : dayItem['dayValue']
          });

    let positionDetails = {
      startDate: this.positionForm.get('startDate').value,
      endDate: this.positionForm.get('endDate').value,
      quantity: this.positionForm.get('quantity').value,
      positionEffortData: positionEffortData
    }

    this.matDialogRef.close({ event: 'submit', data: positionDetails });

  }

  /**
   * @description Calculates average day value for fixed type month units
   * @returns Number
   */
  getAverageValueForFixedType = () => {

    let totalRegulardays = 0, totalHours = 0;

    for (const weekMonthItem of this.weekMonthData)
      for (const weekItem of weekMonthItem['weeks'])
        if (weekItem.weekValue.value > 0) {

          totalHours += weekItem.weekValue.value;

          totalRegulardays += weekItem['regularDays'];

        }

    return totalHours / totalRegulardays;

  }
  @ViewChild('weekValueInput', { static: false }) weekValueInput: ElementRef;
  setCursorToEnd() {
    const input = this.weekValueInput.nativeElement;
    const len = input.value.length;
    if (input.setSelectionRange) {
      input.setSelectionRange(len, len);
    } else if (input.createTextRange) {
      const range = input.createTextRange();
      range.collapse(true);
      range.moveEnd('character', len);
      range.moveStart('character', len);
      range.select();
    }
    input.focus();
  }

  ngOnDestroy() {

    this._onDestroy.next();
    this._onDestroy.complete();

    if (this.formChangeSubscription)
      this.formChangeSubscription.unsubscribe();

    if (this.valueChangeSubscription)
      this.valueChangeSubscription.unsubscribe();

  }

}

import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { QuotePipesModule } from '../../../../quote-pipes/quote-pipes.module';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core';
import { MAT_MOMENT_DATE_ADAPTER_OPTIONS, MomentDateAdapter } from '@angular/material-moment-adapter';
import { MatRadioModule } from '@angular/material/radio';
import { MatSelectModule } from '@angular/material/select';
import { QuoteDirectivesModule } from '../../../../quote-directives/quote-directives.module';

@NgModule({
  declarations: [
    PositionEffortDialogComponent
  ],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatFormFieldModule,
    MatCheckboxModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatInputModule,
    QuotePipesModule,
    MatDatepickerModule,
    MatRadioModule,
    MatSelectModule,
    QuoteDirectivesModule
  ],
  exports: [PositionEffortDialogComponent]
})

class PositionEffortDialogModule { }