<!-- <div class="card" [ngClass]="quote.flag ? 'highlight-class' : ''"> -->
    <div class="card" [ngClass]="{
        'highlight-class': quote.flag == 1,
        'change-request-highlight-class': quote.quote_type == 'CHANGE_REQUEST' && quote.change_request_flag
      }">
      
    <!-- Quote name and flag the top section -->
    <div class="top-section">
        <span (click)="editQuote()">
            <p class="quote-title" [matTooltip]="quote.quote_name">
                QT/{{quote.quote_header_id}} - {{quote.quote_name}}
            </p>
            <p class="version">
                <span class="status-dot" *ngIf="quote.quote_type == 'CHANGE_REQUEST'"></span>
                <span style="width: 10rem;">
                    {{quote.quote_type == 'CHANGE_REQUEST' ? 'Change Request' : ''}}
                    {{quote.flag ? ' (Active)' : quote.change_request_flag ? ' (Active)' :
                    ''}}
                </span>
            </p>
        </span>
        <mat-icon *ngIf="!showDeleted && isCUDEnabled" [matTooltip]="getTooltipText(quote)"
            (click)="flagClick(quote.quote_type)" [fontSet]="getFontSet(quote)" [ngClass]="{
        'flagged': quote.flag && quote.quote_type !== 'CHANGE_REQUEST',
        'change-request-flagged': quote.quote_type === 'CHANGE_REQUEST'
        && quote.change_request_flag}">flag</mat-icon>
    </div>
      
    <!-- Last modified by - the middle section -->
    <div class="mid-section">
        <p class="quote-title">LAST MODIFIED BY</p>
        <div class="d-flex align-items-center">
            <!-- <app-user-profile-e360 [oid]="quote.changed_by" type="small"></app-user-profile-e360> -->
            <img [src]="quote.last_modified_by_profile_url ? quote.last_modified_by_profile_url : 'https://assets.kebs.app/images/User.png'" height="28px" width="28px"/>
            <span class="person-name pl-2">
                {{ quote?.last_modified_by_name }}
            </span>
        </div>
    </div>
    <!-- Value and action icons - the bottom section -->
    <div class="last-section">
        <div class="quote-value">
            <p *ngIf="showQuoteValue" class="quote-title">Quote Value</p>
            <app-currency *ngIf="showQuoteValue" [currencyList]="quote?.quote_revenue_value" type="small" [showActualAmount]="false"></app-currency>
        </div>
        <div class="action-icons d-flex align-items-center">

            <button
                *ngIf="!showDeleted && isCRApplicable && quote.quote_type !== 'CHANGE_REQUEST'; else normalCopyMenu"
                class="widget-btn" matTooltip="Copy Quote" mat-icon-button [matMenuTriggerFor]="copyMenu">
                <mat-icon class="widget-btn-icon">content_copy</mat-icon>
            </button>
            <mat-menu #copyMenu="matMenu">
                <button *ngIf="isCUDEnabled" mat-menu-item (click)="copyQuote('TRADITIONAL')" class="copy-header-class">
                    <span>Copy as Normal Quote</span>
                </button>
                <button mat-menu-item (click)="copyQuote('CHANGE_REQUEST')" class="copy-header-class">
                    <span>Copy as Change Request</span>
                </button>
            </mat-menu>
        
            <ng-template #normalCopyMenu>
                <button *ngIf="!showDeleted && isCUDEnabled" class="widget-btn"
                    matTooltip="{{ quote.quote_type !== 'CHANGE_REQUEST' ? 'Copy Quote' : 'Copy Change Request' }}"
                    mat-icon-button
                    (click)="copyQuote(quote.quote_type !== 'CHANGE_REQUEST' ? 'TRADITIONAL' : 'CHANGE_REQUEST')">
                    <mat-icon class="widget-btn-icon">content_copy</mat-icon>
                </button>
            </ng-template>

            <button *ngIf="!showDeleted && isCUDEnabled" class="widget-btn" (click)="deleteQuote()" matTooltip="{{ quote.quote_type != 'CHANGE_REQUEST' ? 'Delete Quote' : 'Delete Change Request' }}" mat-icon-button>
                <mat-icon fontSet="material-symbols-outlined" class="widget-btn-icon">delete</mat-icon>
            </button>
            <button class="widget-btn" (click)="openQuoteActivityLog()" matTooltip="Activity Log" mat-icon-button>
                <mat-icon fontSet="material-symbols-outlined" class="widget-btn-icon">history</mat-icon>
            </button>
            <!-- <button *ngIf="showApprovalButtons" class="widget-btn" (click)="triggerWorkflowQuoteApproval('APPROVE')" matTooltip="Approve Quote" mat-icon-button>
                <mat-icon fontSet="material-symbols-outlined" class="widget-btn-icon">check_circle               </mat-icon>
            </button>
            <button *ngIf="showApprovalButtons" class="widget-btn" (click)="triggerWorkflowQuoteApproval('REJECT')" matTooltip="Reject Quote" mat-icon-button>
                <mat-icon fontSet="material-symbols-outlined" class="widget-btn-icon">cancel 
                </mat-icon>
            </button> -->
            <button *ngIf="quote?.approval_workflow_header_id && quote.quote_status !== 2" class="status-btn" [ngStyle]="getStatusCss(quote?.quote_status)" [ngClass]="{'shake': quote.isApprover && quote.quote_status === 1}"
                (click)="triggerWorkflowQuoteApproval()" 
                [matTooltip]="
                    quote.isApprover && quote.quote_status === 1 
                        ? 'Click to Approve/Reject Quote Activation' 
                        : quote.isApprover && quote.quote_status === 2 
                        ? 'Click to check the details of Approval' 
                        : quote.isApprover && quote.quote_status === 3 
                        ? 'Click to check the details of Rejection' 
                        : !quote.isApprover && quote.quote_status === 2 
                        ? 'Click to check Approval Details' 
                        : !quote.isApprover && quote.quote_status === 3 
                        ? 'Click to check Rejection Details' 
                        : 'Click to check the status of the approval'
                    " mat-button>
                {{quote.status_name }}
            </button>
        </div>
    </div>
</div>
