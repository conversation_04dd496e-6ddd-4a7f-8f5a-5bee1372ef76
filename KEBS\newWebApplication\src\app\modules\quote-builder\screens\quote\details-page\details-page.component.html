<div class="container-fluid quote-creation-styles" [@.disabled]="true">
    <form [formGroup]="quoteForm" (keydown.enter)="$event.preventDefault()">

        <div class="card" style="box-shadow: none;">
            <div class="card-body" style="padding: 10px;">
                <div class="row align-items-center">
                    <div class="col-1">
                        <button matTooltip="Back" class="back-button" mat-stroked-button (click)="navigateToLandingPage()">
                            <mat-icon class="back-icon">chevron_left</mat-icon>
                            Back
                        </button>
                    </div>
                    <!-- <div #quoteNameContainer class="quote-text" cdkOverlayOrigin #overlayTrigger="cdkOverlayOrigin"
                        [ngClass]="servicesFormArr?.length ? 'col-3' : 'col-7'" (click)="openQuoteOverlay(quoteNameContainer)">
                        <div class="quote-text">{{ quoteForm.get('quoteName').value }}</div>
                        <div class="d-flex align-items-center">
                            <span class="quote-dot" *ngIf="quoteForm.get('quoteType').value == 'CHANGE_REQUEST'"></span>
                            <span class="quote-type" *ngIf="quoteForm.get('quoteType').value == 'CHANGE_REQUEST'">Change Request</span>
                            <span class="quote-type" *ngIf="quoteForm.get('activeQuote').value || quoteForm.get('activeChangeRequest').value">(Active)</span>
                        </div>
                    </div> -->
                    <div #quoteNameContainer class="quote-text" cdkOverlayOrigin #overlayTrigger="cdkOverlayOrigin"
                        [ngClass]="servicesFormArr?.length ? 'col-3' : 'col-7'" (click)="openQuoteOverlay(quoteNameContainer)">
                        <div class="d-flex align-items-center justify-content-start" style="gap: 2%">
                            <div class="quote-text">{{ quoteForm.get('quoteName').value }}</div>
                                <button 
                                    *ngIf="wholeQuoteData?.approval_workflow_header_id" class="status-btn" 
                                    [ngStyle]="getStatusCss(wholeQuoteData?.quote_status)" 
                                    [ngClass]="{'shakeDetailQuote': wholeQuoteData?.isApprover && wholeQuoteData?.quote_status === 1}"
                                    (click)="openApproverDialog(wholeQuoteData)" [matTooltip]="wholeQuoteData?.isApprover && wholeQuoteData?.quote_status === 1 ? 'Click to Approve/Reject Quote Activation' : 'Click to check the status of the approval'" mat-button>
                                {{wholeQuoteData.status_name }}
                            </button>                        
                    </div>
                        <div class="d-flex align-items-center">
                            <span class="quote-dot" *ngIf="quoteForm?.get('quoteType')?.value == 'CHANGE_REQUEST'"></span>
                            <span class="quote-type" *ngIf="quoteForm?.get('quoteType')?.value == 'CHANGE_REQUEST'">Change Request</span>
                            <span class="quote-type" *ngIf="quoteForm?.get('activeQuote')?.value || quoteForm?.get('activeChangeRequest')?.value">(Active)</span>
                        </div>
                    </div>
                    <div class="col-4 slide-in-right">
                        <div class="row date-picker-class">
                            <div class="col-4">
                                <div class="header">Delivery Start Date</div>
                                <mat-form-field class="date">
                                    <mat-label *ngIf="!quoteForm.get('deliveryStartDate').value">DD MMM YYYY</mat-label>
                                    <input matInput [max]="quoteForm.get('deliveryEndDate').value" [min]="quoteForm?.get('quoteType')?.value == 'CHANGE_REQUEST' ? quoteForm.get('deliveryStartDate').value : null"a formControlName="deliveryStartDate" [matDatepicker]="picker1" readonly/>
                                    <mat-datepicker-toggle matSuffix [for]="picker1" *ngIf="isEditMode && has_date_change_access">
                                        <mat-icon style="font-size: 14px;" matDatepickerToggleIcon>edit_calendar</mat-icon>
                                    </mat-datepicker-toggle>
                                    <mat-datepicker #picker1></mat-datepicker>
                                </mat-form-field>
                            </div>
                            <div class="col-4">
                                <div class="header">Delivery End Date</div>
                                <mat-form-field class="date">
                                    <mat-label *ngIf="!quoteForm.get('deliveryEndDate').value">DD MMM YYYY</mat-label>
                                    <input matInput [min]="quoteForm.get('deliveryStartDate').value" formControlName="deliveryEndDate" [matDatepicker]="picker2" readonly/>
                                    <mat-datepicker-toggle matSuffix [for]="picker2" *ngIf="isEditMode && has_date_change_access">
                                        <mat-icon style="font-size: 14px;" matDatepickerToggleIcon>edit_calendar</mat-icon>
                                    </mat-datepicker-toggle>
                                    <mat-datepicker #picker2></mat-datepicker>
                                </mat-form-field>
                            </div>
                            <div class="col-4">
                                <div class="header">Currency</div>
                                <div class="pt-1 d-flex" style="font-size: 12px;font-weight: 500;">
                                    {{ quoteForm.get('quoteCurrency').value }}
                                    <mat-icon *ngIf="isEditMode && quote_currency_change && !wholeQuoteData?.has_parent_opportunity && quoteForm.get('quoteType').value != 'CHANGE_REQUEST'" class="ml-2 service-icon-btn"  style="cursor: pointer;" [matMenuTriggerFor]="currencyMenu">expand_more</mat-icon>
                                </div>
                                <mat-menu #currencyMenu="matMenu" class="custom-mat-menu">
                                    <div class="menu-scroll">
                                        <button mat-menu-item class="menu-header-class" *ngFor="let currency of currencyList"
                                            (click)="selectCurrency(currency)">
                                            {{ currency.name }}
                                        </button>
                                    </div>
                                </mat-menu>
                                </div>
                        </div>
                    </div>
                    <div *ngIf="servicesFormArr?.length" class="col-4 slide-in-right">
                        <div class="row">
                            <div class="col-4 pr-0" *ngIf="checkFieldShouldbeRestricted('totalRevenue')">
                                <div class="header">Total Order Value</div>
                                <div class="revenue-item">
                                    <input type="text" class="overflow-class" matInput formControlName="totalOrderValue" appFcCs [currency]="quoteForm.get('quoteCurrency').value" [showSuffix]="true" readonly>
                                </div>
                            </div>
                            <!-- <div class="col" *ngIf="wholeQuoteData?.isApprover">
                                <mat-icon [matTooltip]="ifUserApprover ? 'This Quote Activation needs your approval' : ''" [ngClass]="{'shine-icon': ifUserApprover}" fontSet="material-symbols-outlined">info</mat-icon> 
                            </div> -->
                            <div class="col-8 d-flex justify-content-center">
                                <button mat-icon-button class="d-flex align-items-center justify-content-center tag-btn" 
                                [disabled]="isDataLoading"
                                *ngIf="quoteMilestoneTagging?.enable && milestoneApplicable" [matTooltip]="quoteMilestoneTagging?.toolTip || 'Tag Milestone'" (click)="openMilestoneDialog()">
                                    <svg xmlns="http://www.w3.org/2000/svg" height="20px" viewBox="0 -960 960 960" width="20px" fill="#45546E"><path d="M843-399 562-117q-11 11-24 16t-27 5q-14 0-27-5t-24-16L116.7-460.3Q106-471 101-483.89T96-511v-281q0-29.7 21.15-50.85Q138.3-864 168-864h281q13.91 0 26.96 5 13.04 5 23.77 15.7L843-500q11 11 16 23.5t5 26.5q0 14-5.02 27.09Q853.96-409.83 843-399ZM511-168l281-281-343-343H168v281l343 343ZM264-636q25 0 42.5-17.5T324-696q0-25-17.5-42.5T264-756q-25 0-42.5 17.5T204-696q0 25 17.5 42.5T264-636Zm216 156Z"/></svg>
                                </button>
                                <button [disabled]="isDataLoading" *ngIf="!isQuoteCreateMode && isEditMode" matTooltip="Cancel Changes" (click)="cancelChanges()" mat-stroked-button class="preview-btn ml-2 mt-2">
                                    Cancel
                                </button>
                                <button [disabled]="isDataBeingSaved || isDataLoading || !isCUDEnabled || quoteForm.get('activeChangeRequest').value || wholeQuoteData?.quote_status === 1 || editRestricted" 
                                [matTooltip]="wholeQuoteData?.quote_status === 1
                                  ? 'Cannot Edit while under review'
                                  : (quoteForm.get('activeChangeRequest').value
                                      ? 'Cannot edit active change request'
                                      : editRestricted? 
                                      editRestrictedMsg? editRestrictedMsg:'Edit not allowed'
                                      :(isEditMode
                                          ? (isChangesMade ? 'Changes made Kindly Save' : 'Save Quote')
                                          : 'Edit Quote'
                                        )
                                    )"
                                    (click)="saveQuote()"
                                    [ngClass]="isEditMode ? 'save-btn' : 'preview-btn'" mat-stroked-button class="ml-2 mt-2"
                                    matBadge="!" [matBadgeHidden]="isEditMode && !isQuoteCreateMode ? !isChangesMade : true" matBadgeSize="small" matBadgeColor="primary">
                                    <ng-container *ngIf="!isDataBeingSaved; else showSpinner">{{ isEditMode ? 'Save' : 'Edit' }}</ng-container>
                                    <ng-template #showSpinner>
                                        <div matTooltip="Saving Quote" class="d-flex justify-content-center">
                                            <mat-spinner [diameter]="20" style="color:#cf0000"></mat-spinner>
                                        </div>
                                    </ng-template>
                                </button>
                                <!-- <button [matMenuTriggerFor]="downloadActions" #moreOptionMenu="matMenuTrigger" matTooltip="Download Quote" mat-stroked-button class="other-btn ml-2 mt-2">
                                    <mat-icon class="service-icon-btn">download</mat-icon>
                                </button>
                                    <mat-menu class="pt-1 pb-1" #downloadActions="matMenu">
                                        <button class="d-flex align-items-center" class="menu-header-class" matTooltip="Download as Spreadsheet File" mat-menu-item>
                                            <span class="menu-item-class">As Spreadsheet</span>
                                        </button>
                                        <button class="d-flex align-items-center" class="menu-header-class" matTooltip="Download as PDF File" mat-menu-item>
                                            <span class="menu-item-class">As PDF</span>
                                        </button>
                                    </mat-menu> -->
                                <button [matMenuTriggerFor]="moreQuoteActions" #moreOptionMenu="matMenuTrigger" matTooltip="More Options" mat-stroked-button class="other-btn ml-2 mt-2">
                                    <mat-icon class="service-icon-btn">more_vert</mat-icon>
                                </button>
                                    <mat-menu class="pt-1 pb-1" #moreQuoteActions="matMenu">
                                        <button *ngIf="quoteId" class="d-flex align-items-center" (click)="openActivityLog()" class="menu-header-class" matTooltip="Activity Log" mat-menu-item>
                                            <span class="menu-item-class">Activity Log</span>
                                        </button>
                                        <!-- <button class="d-flex align-items-center" class="menu-header-class" matTooltip="Reset Values" mat-menu-item>
                                            <span class="menu-item-class">Reset Values</span>
                                        </button> -->
                                        <button class="d-flex align-items-center" (click)="openCustomizeDialog()" class="menu-header-class" matTooltip="Customize Fields" mat-menu-item>
                                            <span class="menu-item-class">Customize Fields</span>
                                        </button>
                                        <!-- <button class="d-flex align-items-center" class="menu-header-class" matTooltip="Dashboard Insights" mat-menu-item>
                                            <span class="menu-item-class">Insights</span>
                                        </button>
                                        <button class="d-flex align-items-center" class="menu-header-class" matTooltip="Clear All Data" mat-menu-item>
                                            <span class="menu-item-class">Clear</span>
                                        </button> -->
                                        <button *ngIf="quoteId && isCUDEnabled" class="d-flex align-items-center" (click)="deleteQuote()" class="menu-header-class" matTooltip="Delete Quote" mat-menu-item>
                                            <span class="menu-item-class">Delete</span>
                                        </button>
                                    </mat-menu>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card mt-2" style="min-height: 70vh;box-shadow: none;">
            <div class="card-body" style="padding: 10px;">

                <ng-container *ngIf="servicesFormArr?.length">
                    <div style="height: 55vh; overflow: auto;" #scrollFrame [@listAnimation]>
                        <div cdkDropList (cdkDropListDropped)="dropService($event)" formArrayName="services">
                            <ng-container *ngFor="let serviceItem of servicesFormArr.controls; let serviceIndex = index"
                                [formGroupName]="serviceIndex">
                                <div class="row d-flex align-items-center drag-class" cdkDrag [cdkDragDisabled]="!isEditMode">
                                    <button *ngIf="isEditMode" cdkDragHandle mat-icon-button>
                                        <mat-icon class="service-icon-btn">drag_indicator</mat-icon>
                                    </button>
                                    <div class="service-name overflow-class" [ngStyle]="{ 'color': serviceItem.get('isSection').value ? '#45546E' : '#cf0001' }" [matTooltip]="serviceItem.get('serviceName').value">{{ serviceItem.get('serviceName').value }}</div>
                                    <button *ngIf="isEditMode && serviceItem.get('isDeleteServiceEnabled').value" mat-icon-button matTooltip="Delete" (click)="deleteService(serviceIndex)">
                                        <mat-icon fontSet="material-symbols-outlined" class="service-icon-btn">delete</mat-icon>
                                    </button>
                                </div>
                                <div style="min-height: 2vh;" id="positionDropZone{{serviceIndex}}" [cdkDropListConnectedTo]="positionDropZones" cdkDropList (cdkDropListDropped)="dropPosition($event, serviceIndex)">
                                    <ng-container formArrayName="positions">
                                        <div *ngIf="serviceItem.get('positions')['controls'].length" class="row d-flex flex-nowrap" [ngClass]="isEditMode ? '' : 'pt-3'">
                                            <div class="col-{{positionField['col']}} label-class" [ngClass]="isEditMode ? 'd-flex justify-content-center': ''">
                                                 {{ positionField['label'] }} <ng-template [ngTemplateOutlet]="mandatoryTemplate"></ng-template>
                                            </div>
                                            <ng-container *ngFor="let fieldItem of customizeFields">
                                                <div *ngIf="fieldItem.isVisible.value && fieldItem?.fieldType != 'display'" class="col-{{fieldItem['col']}} label-class p-0">
                                                    {{ fieldItem['label'] }} <ng-template *ngIf="fieldItem['isMandatory']" [ngTemplateOutlet]="mandatoryTemplate"></ng-template>
                                                </div>
                                            </ng-container>
                                            <ng-container *ngFor="let fieldItem of customizeFields">
                                                <div *ngIf="fieldItem.isVisible.value && fieldItem?.fieldType == 'display'" class="col-{{fieldItem['col']}} label-class d-flex">
                                                    {{ fieldItem['label'] }}
                                                </div>
                                            </ng-container>
                                        </div>
                                        <ng-container
                                            *ngFor="let positionItem of serviceItem.get('positions')['controls']; let positionIndex = index"
                                            [formGroupName]="positionIndex" >
                                            <div [ngStyle]="{ 'border-bottom': !isEditMode && positionItem.get('nmpData')['controls'].length && positionIndex != serviceItem.get('positions')['controls'].length - 1 ? '1px solid #DADCE2' : 'none', 'border-top': !isEditMode && positionItem.get('nmpData')['controls'].length && positionIndex != 0 ? '1px solid #DADCE2' : 'none'}" 
                                                class="drag-class" cdkDrag [cdkDragDisabled]="isDragDisabled(serviceItem, positionItem)" (cdkDragStarted)="onPositionDragStart(serviceIndex)">
                                                <div class="row d-flex align-items-center flex-nowrap">
                                                    <div class="col-{{positionField['col']}} d-flex pr-0">
                                                        <button *ngIf="isEditMode" cdkDragHandle mat-icon-button>
                                                            <mat-icon class="service-icon-btn">drag_indicator</mat-icon>
                                                        </button>
                                                        <div class="d-flex align-items-center" style="padding: 8px 0px 8px 0px;">
                                                            <mat-icon class="uniq-icons" *ngIf="positionItem.get('isNonManpower')?.value" [matTooltip]="'Non Man Power'">
                                                                explore
                                                            </mat-icon>
                                                            <mat-icon class="uniq-icons" *ngIf="positionItem.get('isLicense')?.value"
                                                            [matTooltip]="'License'">
                                                                workspace_premium
                                                            </mat-icon>
                                                            <mat-icon class="uniq-icons" *ngIf="!positionItem.get('isLicense')?.value && !positionItem.get('isNonManpower').value" [matTooltip]="'Man Power'">
                                                                person
                                                            </mat-icon>
                                                        </div>
                                                        <app-input-search [matTooltip]="positionItem.get('positionName').value" *ngIf="isEditMode; else positionNonEdit;" class="form-field-class" style="width: 80%;"
                                                            [list]="positionItem.get('isNonManpower').value ? nmpList : positionItem.get('isLicense').value ? licenseList : skillList" [hideMatLabel]="true"
                                                            [placeholder]="'Search '+positionField['label']" formControlName="positionId" [required]="true" [hasNoneOption]="false" [disabled]="!positionItem.get('isPositionFieldEnabled').value">
                                                        </app-input-search>
                                                        <ng-template #positionNonEdit>
                                                            <div [matTooltip]="positionItem.get('positionName').value" class="display-class">{{ positionItem.get('positionName').value }}</div>
                                                        </ng-template>
                                                    </div>
                                                    <ng-container *ngFor="let fieldItem of customizeFields">
                                                        <div *ngIf="fieldItem.isVisible.value && fieldItem.fieldType != 'display'" class="col-{{fieldItem['col']}} p-0">
                                                            <ng-container *ngIf="fieldItem.fieldType === 'dropdown'">
                                                                <app-input-search *ngIf="(fieldItem?.isForManpowerOnly ? (!positionItem.get('isNonManpower').value && !positionItem.get('isLicense').value) : true) && isEditMode;" class="form-field-class" [required]="fieldItem.isMandatory" [hasNoneOption]="!fieldItem.isMandatory" 
                                                                    [list]="fieldItem.isMasterDataFromPosition ? positionItem.get(fieldItem.key+'MasterData').value : fieldItem.key == 'unit' ?  (positionItem?.get('isLicense')?.value ? licenseUnit : 
                                                                    (positionItem?.get('isNonManpower')?.value ? nonManPowerUnit : manPowerUnit)) : fieldItem.masterData" [hideMatLabel]="true" [placeholder]="'Search '+fieldItem.label" [formControlName]="fieldItem.key"
                                                                    [disabled]="(fieldItem?.enableVariableName ? !positionItem.get(fieldItem?.enableVariableName)?.value : false)  || 
                                                                            ((!wEMappingEnabled && !sDMappingEnabled) 
                                                                                ? (fieldItem.key == 'division' 
                                                                                ? (!positionItem.get('entity').value) 
                                                                                : fieldItem.key == 'subDivision' 
                                                                                    ? (!positionItem.get('entity').value || !positionItem.get('division').value) 
                                                                                    : false
                                                                                ) 
                                                                                : (fieldItem.key == 'entity' 
                                                                                ? !wEMappingEnabled 
                                                                                : fieldItem.key == 'division' 
                                                                                    ? !sDMappingEnabled 
                                                                                    : false
                                                                                )
                                                                                )"
                                                                            >
                                                                </app-input-search>
                                                                <ng-container *ngIf="(fieldItem?.isForManpowerOnly ? (!positionItem.get('isNonManpower').value && !positionItem.get('isLicense').value) : true) && !isEditMode;">
                                                                    <div class="display-class">
                                                                        {{ positionItem.get(fieldItem?.key)?.value 
                                                                          | displayValue : 
                                                                            (fieldItem?.key == 'unit' ?     
                                                                              (positionItem?.get('isLicense')?.value ? licenseUnit : 
                                                                              (positionItem?.get('isNonManpower')?.value ? nonManPowerUnit : manPowerUnit)) 
                                                                            : fieldItem?.masterData) 
                                                                          : 'name' : 'id' 
                                                                        }}
                                                                      </div>
                                                                </ng-container>
                                                            </ng-container>
                                                            <ng-container *ngIf="fieldItem.fieldType === 'number'">
                                                                <mat-form-field *ngIf="isEditMode;" class="form-field-class" appearance="outline" [style.opacity]="positionItem.get(fieldItem?.enableVariableName).value ? 1 : 0.5" [style.pointer-events]="positionItem.get(fieldItem?.enableVariableName).value ? 'auto' : 'none'" >
                                                                    <input type="text" matInput placeholder="{{fieldItem.label}}" [formControlName]="fieldItem.key" appFcCs [decimalPart]="fieldItem?.decimalPart" [currency]="quoteForm.get('quoteCurrency').value">   
                                                                    <span *ngIf="fieldItem.key == 'quantity' && !positionItem.get('isLicense').value" class="suffix-class" matSuffix> {{ positionItem.get('unit').value | displayValue : unitList : 'unit_suffix' : 'id' }} </span>
                                                                    <span *ngIf="fieldItem.key == 'ratePerUnit' || fieldItem.key == 'costPerUnit'" class="suffix-class" matSuffix><ng-template [ngTemplateOutlet]="currencyTemplate"></ng-template></span>
                                                                </mat-form-field>
                                                                <ng-container *ngIf="!isEditMode && !checkFieldShouldbeMasked(fieldItem.key, positionItem.get('isNonManpower')?.value ? 2 : positionItem.get('isLicense')?.value ? 3 : 1);">
                                                                    <div class="display-class">
                                                                        <input type="text" class="overflow-class" matInput [formControlName]="fieldItem.key" appFcCs [currency]="quoteForm.get('quoteCurrency').value" 
                                                                        [showSuffix]="fieldItem.key == 'noOfResources' ? false : true" [decimalPart]="fieldItem?.decimalPart"
                                                                        [suffix]="(fieldItem.key == 'quantity' && !positionItem.get('isLicense').value) ? (positionItem.get('unit').value | displayValue : unitList : 'unit_suffix' : 'id') : ''" readonly>
                                                                    </div>
                                                                </ng-container>
                                                                <ng-container *ngIf="checkFieldShouldbeMasked(fieldItem.key, positionItem.get('isNonManpower')?.value ? 2 : positionItem.get('isLicense')?.value ? 3 : 1);">
                                                                    <div class="display-class" >
                                                                      {{masking_configuration.maskDisplay || '****'}}
                                                                    </div>
                                                                  </ng-container>
                                                            </ng-container>
                                                        </div>
                                                    </ng-container>
                                                    <ng-container *ngFor="let fieldItem of customizeFields">
                                                        <div *ngIf="fieldItem.isVisible.value && fieldItem.fieldType == 'display'" class="col-{{fieldItem['col']}} pr-0 revenue-value">
                                                            <ng-container *ngIf="fieldItem.fieldType === 'display' && fieldItem.key != 'quotePositionId' && !checkFieldShouldbeMasked(fieldItem.key, positionItem.get('isNonManpower')?.value ? 2 : positionItem.get('isLicense')?.value ? 3 : 1);">
                                                                <input type="text" class="overflow-class" matInput [formControlName]="fieldItem.key" appFcCs [currency]="quoteForm.get('quoteCurrency').value" readonly>
                                                            </ng-container>
                                                            <ng-container *ngIf="fieldItem.fieldType === 'display' && fieldItem.key == 'quotePositionId' && !checkFieldShouldbeMasked(fieldItem.key, positionItem.get('isNonManpower')?.value ? 2 : positionItem.get('isLicense')?.value ? 3 : 1);">
                                                                <input type="text" class="overflow-class" matInput [formControlName]="fieldItem.key" readonly>
                                                            </ng-container>
                                                            <ng-container *ngIf="checkFieldShouldbeMasked(fieldItem.key, positionItem.get('isNonManpower')?.value ? 2 : positionItem.get('isLicense')?.value ? 3 : 1);">
                                                                <div class="display-class" >
                                                                  {{masking_configuration.maskDisplay || '****'}}
                                                                </div>
                                                            </ng-container>
                                                        </div>
                                                    </ng-container>
                                                    <div class="col-1 p-0 pt-2 d-flex align-items-center">
                                                        <button *ngIf="isEditMode && positionItem.get('isAddPositionInlineEnabled').value" matTooltip="Add Position" (click)="addPosition(serviceItem, positionIndex, 'positions')" class="more-btn" mat-icon-button>
                                                            <mat-icon class="more-btn-icon">add</mat-icon>
                                                        </button>
                                                        <button *ngIf="isEditMode && positionItem.get('isDeletePositionEnabled').value" matTooltip="Delete Position" (click)="deletePosition(serviceItem, positionIndex, 'positions')" class="more-btn" mat-icon-button>
                                                            <mat-icon fontSet="material-symbols-outlined" class="more-btn-icon">delete</mat-icon>
                                                        </button>
                                                        <button *ngIf="isEditMode && positionItem.get('isClonePositionEnabled').value"
                                                        matTooltip="{{ positionItem.get('positionDuplicationLoader').value ? 'Duplicating...' : 'Duplicate Position' }}" [disabled]="positionItem.get('positionDuplicationLoader').value"
                                                        (click)="duplicatePosition(serviceItem, positionIndex, 'positions')" class="more-btn" mat-icon-button>
                                                        <mat-icon class="more-btn-icon" *ngIf="!positionItem.get('positionDuplicationLoader').value">content_copy</mat-icon>
                                                        <mat-spinner class="more-btn-icon d-flex align-items-center justify-content-center" *ngIf="positionItem.get('positionDuplicationLoader').value" diameter="15" style="color: linear-gradient(270deg, #ef4a61, #f27a6c 105.29%) !important;margin-left: 0.25rem;
                                                        "></mat-spinner>                                         
                                                        </button>
                                                        <button *ngIf="positionItem.get('isCustomiseSlotEnabled').value" matTooltip="Customize Slots" (click)="openPositionEffort(positionItem, positionIndex, 1)" class="more-btn" mat-icon-button>
                                                            <mat-icon class="more-btn-icon">tune</mat-icon>
                                                        </button>
                                                    </div>
                                                </div>
                                                <!-- Nmp array starts -->
                                                <ng-container formArrayName="nmpData">
                                                    <ng-container
                                                        *ngFor="let nmpItem of positionItem.get('nmpData')['controls']; let nmpIndex = index"
                                                        [formGroupName]="nmpIndex">
                                                        <div class="row d-flex align-items-center flex-nowrap" style="width: 100%;">
                                                            <div class="col-{{positionField['col']}} d-flex pr-0">
                                                                <button *ngIf="isEditMode" style="visibility: hidden;" mat-icon-button>
                                                                    <mat-icon class="service-icon-btn">drag_indicator</mat-icon>
                                                                </button>
                                                                <div class="d-flex align-items-center" style="padding: 8px 0px 8px 0px;">
                                                                    <mat-icon class="uniq-icons" *ngIf="positionItem.get('isNonManpower').value" [matTooltip]="'Non Man Power'">
                                                                        explore
                                                                    </mat-icon>
                                                                    <mat-icon class="uniq-icons" *ngIf="positionItem.get('isLicense').value"
                                                                    [matTooltip]="'License'">
                                                                        workspace_premium
                                                                    </mat-icon>
                                                                    <mat-icon class="uniq-icons" *ngIf="!positionItem.get('isLicense').value && !positionItem.get('isNonManpower').value" [matTooltip]="'Man Power'">
                                                                        person
                                                                    </mat-icon>
                                                                </div>
                                                                <app-input-search [matTooltip]="nmpItem.get('positionName').value" *ngIf="isEditMode; else positionNonEdit1;" class="form-field-class" style="width: 80%;"
                                                                    [list]="nmpList" [hideMatLabel]="true"
                                                                    [placeholder]="'Search '+positionField['label']" formControlName="positionId" [required]="true" [hasNoneOption]="false" [disabled]="!positionItem.get('isPositionFieldEnabled').value">
                                                                </app-input-search>
                                                                <ng-template #positionNonEdit1>
                                                                    <div [matTooltip]="nmpItem.get('positionName').value" class="display-class">{{ nmpItem.get('positionName').value }}</div>
                                                                </ng-template>
                                                            </div>
                                                            <ng-container *ngFor="let fieldItem of customizeFields">
                                                                <div *ngIf="fieldItem.isVisible.value && fieldItem.fieldType != 'display'" class="col-{{fieldItem['col']}} p-0">
                                                                    <ng-container *ngIf="fieldItem.fieldType === 'dropdown'">
                                                                        <app-input-search *ngIf="!fieldItem?.isForManpowerOnly && isEditMode;" class="form-field-class" [required]="fieldItem.isMandatory" [hasNoneOption]="!fieldItem.isMandatory"
                                                                            [list]="fieldItem.isMasterDataFromPosition ? nmpItem.get(fieldItem.key+'MasterData').value : fieldItem.masterData" [hideMatLabel]="true" [placeholder]="'Search '+fieldItem.label" [formControlName]="fieldItem.key"
                                                                            [disabled]="(fieldItem?.enableVariableName ? !positionItem.get(fieldItem?.enableVariableName)?.value : false)  || 
                                                                            ((!wEMappingEnabled && !sDMappingEnabled) 
                                                                                ? (fieldItem.key == 'division' 
                                                                                ? (!positionItem.get('entity').value) 
                                                                                : fieldItem.key == 'subDivision' 
                                                                                    ? (!positionItem.get('entity').value || !positionItem.get('division').value) 
                                                                                    : false
                                                                                ) 
                                                                                : (fieldItem.key == 'entity' 
                                                                                ? !wEMappingEnabled 
                                                                                : fieldItem.key == 'division' 
                                                                                    ? !sDMappingEnabled 
                                                                                    : false
                                                                                )
                                                                                )"
                                                                            >
                                                                        </app-input-search>
                                                                        <ng-container *ngIf="!fieldItem?.isForManpowerOnly && !isEditMode;">
                                                                            <div class="display-class">{{ nmpItem.get(fieldItem.key)?.value | displayValue : fieldItem.masterData : 'name' : 'id' }}</div>
                                                                        </ng-container>
                                                                    </ng-container>
                                                                    <ng-container *ngIf="fieldItem.fieldType === 'number'">
                                                                        <mat-form-field *ngIf="isEditMode && !checkFieldShouldbeMasked(fieldItem.key, positionItem.get('isNonManpower')?.value ? 2 : positionItem.get('isLicense')?.value ? 3 : 1);" class="form-field-class" appearance="outline">
                                                                            <input type="text" matInput placeholder="{{fieldItem.label}}" [formControlName]="fieldItem.key" appFcCs [disabledD]="!positionItem.get(fieldItem?.enableVariableName).value" [currency]="quoteForm.get('quoteCurrency').value" [readonly]="fieldItem.key === 'quotePositionId'">
                                                                            <span *ngIf="fieldItem.key == 'quantity'" class="suffix-class" matSuffix> {{ nmpItem.get('unit').value | displayValue : unitList : 'unit_suffix' : 'id' }} </span>
                                                                            <span *ngIf="fieldItem.key == 'ratePerUnit' || fieldItem.key == 'costPerUnit'" class="suffix-class" matSuffix><ng-template [ngTemplateOutlet]="currencyTemplate"></ng-template></span>
                                                                        </mat-form-field>
                                                                        <ng-container *ngIf="!isEditMode && !checkFieldShouldbeMasked(fieldItem.key, positionItem.get('isNonManpower')?.value ? 2 : positionItem.get('isLicense')?.value ? 3 : 1);">
                                                                            <div class="display-class">
                                                                                <input type="text" class="overflow-class" matInput [formControlName]="fieldItem.key" appFcCs [currency]="quoteForm.get('quoteCurrency').value" 
                                                                                [showSuffix]="fieldItem.key == 'noOfResources' || fieldItem.key == 'quotePositionId' ? false : true"
                                                                                [suffix]="fieldItem.key == 'quantity' ? (nmpItem.get('unit').value | displayValue : unitList : 'unit_suffix' : 'id') : ''" readonly>
                                                                            </div>
                                                                        </ng-container>
                                                                        <ng-container *ngIf="checkFieldShouldbeMasked(fieldItem.key, positionItem.get('isNonManpower')?.value ? 2 : positionItem.get('isLicense')?.value ? 3 : 1)">
                                                                            <div class="display-class" >
                                                                              {{masking_configuration.maskDisplay || '****'}}
                                                                            </div>
                                                                          </ng-container>
                                                                    </ng-container>
                                                                </div>
                                                            </ng-container>
                                                            <ng-container *ngFor="let fieldItem of customizeFields">
                                                                <div *ngIf="fieldItem.isVisible.value && fieldItem.fieldType == 'display'" class="col-{{fieldItem['col']}} pr-0 revenue-value">
                                                                    <ng-container *ngIf="fieldItem.fieldType === 'display' && fieldItem.key != 'quotePositionId' && !checkFieldShouldbeMasked(fieldItem.key, positionItem.get('isNonManpower')?.value ? 2 : positionItem.get('isLicense')?.value ? 3 : 1);">
                                                                        <input type="text" class="overflow-class" matInput [formControlName]="fieldItem.key" appFcCs [currency]="quoteForm.get('quoteCurrency').value" readonly>
                                                                    </ng-container>
                                                                    <ng-container *ngIf="fieldItem.fieldType === 'display' && fieldItem.key == 'quotePositionId' && !checkFieldShouldbeMasked(fieldItem.key, positionItem.get('isNonManpower')?.value ? 2 : positionItem.get('isLicense')?.value ? 3 : 1);">
                                                                        <input type="text" class="overflow-class" matInput [formControlName]="fieldItem.key" readonly>
                                                                    </ng-container>
                                                                    <ng-container *ngIf="checkFieldShouldbeMasked(fieldItem.key, positionItem.get('isNonManpower')?.value ? 2 : positionItem.get('isLicense')?.value ? 3 : 1);">
                                                                        <div class="display-class" >
                                                                          {{masking_configuration.maskDisplay || '****'}}
                                                                        </div>
                                                                    </ng-container>
                                                                </div>
                                                            </ng-container>
                                                            <div class="col-1 p-0 pt-2">
                                                                <button *ngIf="isEditMode && positionItem.get('isAddPositionInlineEnabled').value" matTooltip="Add Position" (click)="addPosition(serviceItem, nmpIndex, 'nmpData', positionItem)" class="more-btn" mat-icon-button>
                                                                    <mat-icon class="more-btn-icon">add</mat-icon>
                                                                </button>
                                                                <button *ngIf="isEditMode && positionItem.get('isDeletePositionEnabled').value" matTooltip="Delete Position" (click)="deletePosition(positionItem, nmpIndex, 'nmpData')" class="more-btn" mat-icon-button>
                                                                    <mat-icon fontSet="material-symbols-outlined" class="more-btn-icon">delete</mat-icon>
                                                                </button>
                                                                <button 
                                                                *ngIf="isEditMode && positionItem.get('isClonePositionEnabled').value"
                                                                matTooltip="{{ positionItem.get('positionDuplicationLoader').value  ? 'Duplicating...' : 'Duplicate Position' }}" 
                                                                [disabled]="positionItem.get('positionDuplicationLoader').value "
                                                                (click)="duplicatePosition(serviceItem, positionIndex, 'positions')" class="more-btn" mat-icon-button>
                                                                <mat-icon class="more-btn-icon" *ngIf="!positionItem.get('positionDuplicationLoader').value ">content_copy</mat-icon>
                                                                <mat-spinner class="more-btn-icon d-flex align-items-center p-0" *ngIf="positionItem.get('positionDuplicationLoader').value" diameter="15" style="color: linear-gradient(270deg, #ef4a61, #f27a6c 105.29%) !important"></mat-spinner>                                         
                                                                </button>
                                                                <button *ngIf="positionItem.get('isCustomiseSlotEnabled').value" matTooltip="Customize Slots" (click)="openPositionEffort(nmpItem, nmpIndex, 2)" class="more-btn" mat-icon-button>
                                                                    <mat-icon class="more-btn-icon">tune</mat-icon>
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </ng-container>
                                                </ng-container>
                                            </div>
                                        </ng-container>
                                    </ng-container>
                                </div>
                            </ng-container>
                        </div>
                        <ng-container *ngIf="quoteForm.get('discounts').controls.length || quoteForm.get('taxes').controls.length">
                            <div class="row pt-2 col-12" style="border-top: 1px solid #DADCE2; color: #45546E;">
                                <!-- Discounts & Taxes -->
                            </div>
                            <ng-container formArrayName="discounts">
                                <ng-container *ngFor="let discountItem of quoteForm.get('discounts').controls; let discountIndex = index"
                                    [formGroupName]="discountIndex">
                                    <div class="row d-flex align-items-center" [ngClass]="discountIndex == 0 ? 'pt-2' : ''">
                                        <div class="col-2">
                                            <span *ngIf="discountIndex == 0" class="dt-class"> Discounts </span>
                                        </div>
                                        <div class="col-2 p-0">
                                            <div *ngIf="discountIndex == 0" class="label-class">Discount Name</div>
                                            <ng-container *ngIf="discountItem.get('isCustom').value">
                                                <mat-form-field *ngIf="isEditMode; else discountNameNonEdit" class="form-field-class" appearance="outline">
                                                    <input matInput placeholder="Enter Discount name" formControlName="discountName" />
                                                </mat-form-field>
                                            </ng-container>
                                            <ng-container *ngIf="!discountItem.get('isCustom').value">
                                                <app-input-search *ngIf="isEditMode; else discountNameNonEdit" class="form-field-class"
                                                    [list]="discountsList" [hideMatLabel]="true" [placeholder]="'Search Discount'" formControlName="discountId">
                                                </app-input-search>
                                            </ng-container>
                                            <ng-template #discountNameNonEdit>
                                                <div class="display-class">
                                                    <ng-container *ngIf="!discountItem.get('isCustom').value">
                                                        {{ discountItem.get('discountName').value }}
                                                    </ng-container>
                                                    <ng-container *ngIf="discountItem.get('isCustom').value">
                                                        {{ discountItem.get('discountName').value }}
                                                    </ng-container>
                                                </div>
                                            </ng-template>
                                        </div>
                                        <div class="col-1 p-0">
                                            <div *ngIf="discountIndex == 0" class="label-class">Discount %</div>
                                            <mat-form-field *ngIf="isEditMode; else discountPerNonEdit" class="form-field-class" appearance="outline">
                                                <input type="number" min="0" matInput placeholder="Enter %" formControlName="discountPercentage">
                                                <span class="suffix-class" matSuffix>%</span>
                                            </mat-form-field>
                                            <ng-template #discountPerNonEdit>
                                                <div class="display-class">{{ discountItem.get('discountPercentage').value }}<span class="pl-1">%</span></div>
                                            </ng-template>
                                        </div>
                                        <div class="col-1 pr-0 d-flex flex-column">
                                            <div *ngIf="discountIndex == 0" class="label-class">Discount Value</div>
                                            <div [ngClass]="isEditMode ? 'dt-class pt-2' : 'display-class'">
                                                <input type="text" class="overflow-class" matInput formControlName="discountValue" appFcCs [currency]="quoteForm.get('quoteCurrency').value" [showSuffix]="true" readonly>
                                            </div>
                                        </div>
                                        <div class="col-2">
                                            <ng-container *ngIf="isEditMode">
                                                <button matTooltip="Add Discount" (click)="addDiscountAndTax(discountIndex, 'discounts')" style="width: 40px;" class="more-btn" mat-icon-button>
                                                    <mat-icon class="more-btn-icon">add</mat-icon>
                                                </button>
                                                <button matTooltip="Delete Discount" (click)="deleteDiscountAndTax(discountIndex, 'discounts')" style="width: 40px;" class="more-btn" mat-icon-button>
                                                    <mat-icon fontSet="material-symbols-outlined" class="more-btn-icon">delete</mat-icon>
                                                </button>
                                                <button matTooltip="Custom Discount" (click)="toggleCustomDiscount(discountIndex)" style="width: 40px;" class="more-btn pl-2" mat-icon-button>
                                                    <mat-icon style="font-size: 25px;" [ngStyle]="{'color': discountItem.get('isCustom').value ? '#ff6d5c': '#6E7B8F'}" class="more-btn-icon">{{ discountItem.get('isCustom').value ? 'toggle_on' : 'toggle_off' }}</mat-icon>
                                                </button>
                                            </ng-container>
                                        </div>
                                        <div class="col-2"></div>
                                        <div class="col-2 d-flex flex-column">
                                            <ng-container *ngIf="discountIndex == 0">
                                                <div class="label-class">Total Discount</div>
                                                <div class="dt-class" style="line-height: 5vh; font-size: 13px;">
                                                    <input type="text" class="overflow-class" matInput [formControl]="quoteForm.get('totalDiscount')" appFcCs [currency]="quoteForm.get('quoteCurrency').value" [showSuffix]="true" readonly>
                                                </div>
                                            </ng-container>
                                        </div>
                                    </div>
                                </ng-container>
                            </ng-container>
                            <ng-container formArrayName="taxes">
                                <ng-container *ngFor="let taxItem of quoteForm.get('taxes').controls; let taxIndex = index"
                                    [formGroupName]="taxIndex">
                                    <div class="row d-flex align-items-center" [ngClass]="taxIndex == 0 ? 'pt-2' : ''">
                                        <div class="col-2">
                                            <span *ngIf="taxIndex == 0" class="dt-class"> Taxes </span>
                                        </div>
                                        <div class="col-2 p-0">
                                            <div *ngIf="taxIndex == 0" class="label-class">Tax Name</div>
                                            <app-input-search *ngIf="isEditMode; else taxNameNonEdit" class="form-field-class"
                                                [list]="taxesList" [hideMatLabel]="true" [placeholder]="'Search Tax'" formControlName="taxId">
                                            </app-input-search>
                                            <ng-template #taxNameNonEdit>
                                                <div class="display-class">{{ taxItem.get('taxName').value }}</div>
                                            </ng-template>
                                        </div>
                                        <div class="col-1 p-0">
                                            <div *ngIf="taxIndex == 0" class="label-class">Tax %</div>
                                            <mat-form-field *ngIf="isEditMode; else taxPerNonEdit" class="form-field-class" appearance="outline">
                                                <input type="number" min="0" matInput placeholder="Enter %" formControlName="taxPercentage">
                                                <span class="suffix-class" matSuffix>%</span>
                                            </mat-form-field>
                                            <ng-template #taxPerNonEdit>
                                                <div class="display-class">{{ taxItem.get('taxPercentage').value }}<span class="pl-1">%</span></div>
                                            </ng-template>
                                        </div>
                                        <div class="col-1 pr-0 d-flex flex-column">
                                            <div *ngIf="taxIndex == 0" class="label-class">Tax Value</div>
                                            <div [ngClass]="isEditMode ? 'dt-class pt-2' : 'display-class'">
                                                <input type="text" class="overflow-class" matInput formControlName="taxValue" appFcCs [currency]="quoteForm.get('quoteCurrency').value" [showSuffix]="true" readonly>
                                            </div>
                                        </div>
                                        <div class="col-2">
                                            <ng-container *ngIf="isEditMode">
                                                <button matTooltip="Add Tax" (click)="addDiscountAndTax(taxIndex, 'taxes')" style="width: 40px;" class="more-btn" mat-icon-button>
                                                    <mat-icon class="more-btn-icon">add</mat-icon>
                                                </button>
                                                <button matTooltip="Delete Tax" (click)="deleteDiscountAndTax(taxIndex, 'taxes')" style="width: 40px;" class="more-btn" mat-icon-button>
                                                    <mat-icon fontSet="material-symbols-outlined" class="more-btn-icon">delete</mat-icon>
                                                </button>
                                            </ng-container>
                                        </div>
                                        <div class="col-2"></div>
                                        <div class="col-2 d-flex flex-column">
                                            <ng-container *ngIf="taxIndex == 0">
                                                <div class="label-class">Total Tax</div>
                                                <div class="dt-class" style="line-height: 5vh; font-size: 13px;">
                                                    <input type="text" class="overflow-class" matInput [formControl]="quoteForm.get('totalTax')" appFcCs [currency]="quoteForm.get('quoteCurrency').value" [showSuffix]="true" readonly>
                                                </div>
                                            </ng-container>
                                        </div>
                                    </div>
                                </ng-container>
                            </ng-container>
                        </ng-container>
                    </div>

                    <div class="row" style="background-color: #FDE4E2; line-height: 5vh;border-bottom: 1px solid #F8AFA7">
                        <div class="col-4"></div>
                        <div *ngIf="!isRevenueActive" class="col-2"></div>
                        <div *ngIf="!isCostActive" class="col-2"></div>
                        <div *ngIf="!isGMActive" class="col-2"></div>
                        <div *ngIf="!isGMPerActive" class="col-2"></div>
                        <div *ngIf="isRevenueActive" class="col-2 p-0 d-flex align-items-center">
                            <span class="header" style="min-width: 5rem;">Total Revenue</span> 
                            <span class="revenue-item ml-2" *ngIf="!checkFieldShouldbeMasked('totalRevenue', 'FOOTER')"> 
                                <input type="text" class="overflow-class" matInput formControlName="totalRevenue" appFcCs [currency]="quoteForm.get('quoteCurrency').value" [showSuffix]="true" readonly>
                            </span>
                            <span class="revenue-item ml-2" *ngIf="checkFieldShouldbeMasked('totalRevenue', 'FOOTER')"> 
                                {{masking_configuration.maskDisplay || '****'}}
                        </span>
                        <div *ngIf="quote_footer_splitup_configuration?.showResourceTypeRevenueSplitUp" class="d-flex mt-1" cdkOverlayOrigin #overlayTrigger="cdkOverlayOrigin" #revSummaryContainer>
                            <mat-icon (click)="openRevSummaryOverlay(revSummaryContainer)" class="ml-2 service-icon-btn" style="cursor: pointer;">expand_less</mat-icon>
                        </div>
                        </div>
                        <div *ngIf="isCostActive" class="col-2 p-0 d-flex align-items-center">
                            <span class="header" style="min-width: 4rem;">Total Cost</span> 
                            <span class="revenue-item ml-2" *ngIf="!checkFieldShouldbeMasked('totalCost', 'FOOTER')"> 
                                <input type="text" class="overflow-class" matInput formControlName="totalCost" appFcCs [currency]="quoteForm.get('quoteCurrency').value" [showSuffix]="true" readonly>
                            </span>
                            <span class="revenue-item ml-2" *ngIf="checkFieldShouldbeMasked('totalCost', 'FOOTER')"> 
                                {{masking_configuration.maskDisplay || '****'}}
                            </span>
                            <div *ngIf="quote_footer_splitup_configuration?.showResourceTypeCostSplitUp"  class="d-flex mt-1" cdkOverlayOrigin #overlayTrigger="cdkOverlayOrigin" #headerCostContainer>
                                <mat-icon (click)="openCostOverlay(headerCostContainer)" class="ml-2 service-icon-btn" style="cursor: pointer;">expand_less</mat-icon>
                            </div>
                        </div>
                        <div *ngIf="isGMActive" class="col-2 p-0 d-flex align-items-center">
                            <span class="header" style="min-width: 4rem;">Total GM</span> 
                            <span class="revenue-item ml-2" *ngIf="!checkFieldShouldbeMasked('totalGM', 'FOOTER')"> 
                                <input type="text" class="overflow-class" matInput formControlName="totalGM" appFcCs [currency]="quoteForm.get('quoteCurrency').value" [showSuffix]="true" readonly>                
                            </span>
                            <span class="revenue-item ml-2" *ngIf="checkFieldShouldbeMasked('totalGM', 'FOOTER')"> 
                                    {{masking_configuration.maskDisplay || '****'}}
                            </span>
                        </div>
                        <div *ngIf="isGMPerActive" class="col-2 p-0 d-flex align-items-center">
                            <span class="header">Total GM %</span> 
                            <span class="revenue-item ml-2"> {{ quoteForm.get('totalGMPercentage').value }}</span>
                        </div>
                    </div>

                    <div *ngIf="isEditMode" class="row mt-3">
                        <ng-container *ngFor="let addBtn of addButtonsList">
                            <div *ngIf="addBtn.has_project_integrated && (addBtn?.id == 6 ? quoteForm.get('discounts').controls.length == 0 : 
                                        addBtn?.id == 7 ? quoteForm.get('taxes').controls.length == 0 : true)" class="d-flex" style="cursor: pointer;" (click)="addButtonClick(addBtn)">
                                <mat-icon class="ml-2 service-icon-btn">add</mat-icon>
                                <span class="ml-1 add-btn">{{ addBtn?.label }}</span>
                            </div>
                        </ng-container>
                    </div>

                    <ng-template cdkConnectedOverlay [cdkConnectedOverlayOrigin]="overlayTrigger" #sectionTemplateRef>
                        <div class="card" style="min-width: 25vw;">
                            <div class="card-body p-2">
                                <div class="row pt-2 pb-1">
                                    <div class="col-10 d-flex align-items-center" style="font-weight: 500; color: #45546E;">
                                        Create Section
                                    </div>
                                    <div class="col-2">
                                        <button mat-icon-button (click)="closeSectionDialog()">
                                            <mat-icon style="font-size: 15px;color: #1C1B1F;">close</mat-icon>
                                        </button>
                                    </div>
                                </div>
                                <div class="row col-12 section-label">
                                    Add Section Name
                                </div>
                                <div class="row col-12">
                                    <mat-form-field appearance="outline" style="width: 100%;font-size: 13px;">
                                        <input matInput placeholder="Enter here" [formControl]="addSectionFormControl">
                                    </mat-form-field>
                                </div>
                                <div class="row col-12 pt-1 pb-2 pl-2">
                                    <button (click)="closeSectionDialog()" mat-raised-button class="cancel-btn ml-2 mt-2">
                                        Cancel
                                    </button>
                                    <button [disabled]="!addSectionFormControl.value" [ngClass]="addSectionFormControl.value ? 'create-btn' : 'create-btn-disabled'"
                                        (click)="addSection()" mat-raised-button class="ml-2 mt-2">
                                        Create
                                    </button>
                                </div>
                            </div>
                        </div>
                    </ng-template>

                    <ng-template cdkConnectedOverlay [cdkConnectedOverlayOrigin]="overlayTrigger" #resourceCostRef>
                        <div class="card" style="min-width: 18vw">
                            <div class="card-body p-2">
                                <div class="row">
                                    <div class="col-7 header-label">Manpower Cost</div>
                                    <span class="col-3 pr-0 pl-0 value-label" *ngIf="!checkFieldShouldbeMasked('totalCost', 1)">
                                        <input type="text" class="overflow-class" matInput formControlName="manpowerCost"
                                            [matTooltip]="quoteForm.get('manpowerCost').value?.toString().length > 9 ? formatNumberByCurrency(quoteForm.get('manpowerCost').value, quoteForm.get('quoteCurrency').value) : ''"
                                            appFcCs [currency]="quoteForm.get('quoteCurrency').value" [showSuffix]="false" readonly />
                                    </span>
                                    <span class="col-3 pr-0 pl-0 value-label" *ngIf="checkFieldShouldbeMasked('totalCost', 1)">
                                        {{masking_configuration.maskDisplay || '****'}}
                                    </span>
                                    <span *ngIf="!checkFieldShouldbeMasked('totalCost', 'FOOTER')" class="col pl-0 value-label">{{
                                        quoteForm.get('quoteCurrency').value }}</span>
                                </div>
                                <div class="row">
                                    <div class="col-7 header-label">Non Manpower Cost</div>
                                    <span class="col-3 pr-0 pl-0 value-label" *ngIf="!checkFieldShouldbeMasked('totalCost', 2)">
                                        <input type="text" class="overflow-class" matInput formControlName="nonManpowerCost"
                                            [matTooltip]="quoteForm.get('nonManpowerCost').value?.toString().length > 9 ? formatNumberByCurrency(quoteForm.get('nonManpowerCost').value, quoteForm.get('quoteCurrency').value) : ''"
                                            appFcCs [currency]="quoteForm.get('quoteCurrency').value" [showSuffix]="false" readonly />
                                    </span>
                                    <span class="col-3 pr-0 pl-0 value-label" *ngIf="checkFieldShouldbeMasked('totalCost', 2)">
                                        {{masking_configuration.maskDisplay || '****'}}
                                    </span>
                                    <span *ngIf="!checkFieldShouldbeMasked('totalCost', 2)" class="col pl-0 value-label">{{
                                        quoteForm.get('quoteCurrency').value }}</span>
                                </div>
                                <div class="row">
                                    <div class="col-7 header-label">License Cost</div>
                                    <span class="col-3 pr-0 pl-0 value-label" *ngIf="!checkFieldShouldbeMasked('totalCost', 3)">
                                        <input type="text" class="overflow-class" matInput formControlName="licenseCost"
                                            [matTooltip]="quoteForm.get('licenseCost').value?.toString().length > 9 ? formatNumberByCurrency(quoteForm.get('licenseCost').value, quoteForm.get('quoteCurrency').value) : ''"
                                            appFcCs [currency]="quoteForm.get('quoteCurrency').value" [showSuffix]="false" readonly />
                                    </span>
                                    <span class="col-3 pr-0 pl-0 value-label" *ngIf="checkFieldShouldbeMasked('totalCost', 3)">
                                        {{masking_configuration.maskDisplay || '****'}}
                                    </span>
                                    <span *ngIf="!checkFieldShouldbeMasked('totalCost', 3)" class="col pl-0 value-label">{{
                                        quoteForm.get('quoteCurrency').value }}</span>
                                </div>
                                <div class="row pt-2" style="border-top: 1px solid #dadce2">
                                    <div class="col-7 header-label">Total Cost</div>
                                    <span class="col-3 pr-0 pl-0 value-label" *ngIf="!checkFieldShouldbeMasked('totalCost', 'FOOTER')">
                                        <input type="text" class="overflow-class" matInput formControlName="totalCost"
                                            [matTooltip]="quoteForm.get('totalCost').value?.toString().length > 9 ? formatNumberByCurrency(quoteForm.get('totalCost').value, quoteForm.get('quoteCurrency').value) : ''"
                                            appFcCs [currency]="quoteForm.get('quoteCurrency').value" [showSuffix]="false" readonly />
                                    </span>
                                    <span class="col-3 pr-0 pl-0 value-label" *ngIf="checkFieldShouldbeMasked('totalCost', 'FOOTER')">
                                        {{masking_configuration.maskDisplay || '****'}}
                                    </span>
                                    <span *ngIf="!checkFieldShouldbeMasked('totalCost', 'FOOTER')"
                                        class="col pl-0 value-label">{{quoteForm.get('quoteCurrency').value }}</span>
                                </div>
                            </div>
                        </div>
                    </ng-template>

                    <ng-template cdkConnectedOverlay [cdkConnectedOverlayOrigin]="overlayTrigger" #revSummaryRef>
                        <div class="card" style="min-width: 18vw">
                            <div class="card-body p-2">
                                <div class="row">
                                    <div class="col-7 header-label">Manpower Revenue</div>
                                    <span class="col-3 pr-0 pl-0 value-label" *ngIf="!checkFieldShouldbeMasked('totalRevenue', 1)">
                                        <input type="text" class="overflow-class" matInput formControlName="manpowerRevenue"
                                            [matTooltip]="quoteForm.get('manpowerRevenue').value?.toString().length > 9 ? formatNumberByCurrency(quoteForm.get('manpowerRevenue').value, quoteForm.get('quoteCurrency').value) : ''"
                                            appFcCs [currency]="quoteForm.get('quoteCurrency').value" [showSuffix]="false" readonly />
                                    </span>
                                    <span class="col-3 pr-0 pl-0 value-label" *ngIf="checkFieldShouldbeMasked('totalRevenue', 1)">
                                        {{masking_configuration.maskDisplay || '****'}}
                                    </span>
                                    <span *ngIf="!checkFieldShouldbeMasked('totalRevenue', 1)" class="col pl-0 value-label">{{
                                        quoteForm.get('quoteCurrency').value }}</span>
                                </div>
                                <div class="row">
                                    <div class="col-7 header-label">Non Manpower Revenue</div>
                                    <span class="col-3 pr-0 pl-0 value-label" *ngIf="!checkFieldShouldbeMasked('totalRevenue', 2)">
                                        <input type="text" class="overflow-class" matInput formControlName="nonManpowerRevenue"
                                            [matTooltip]="quoteForm.get('nonManpowerRevenue').value?.toString().length > 9 ? formatNumberByCurrency(quoteForm.get('nonManpowerRevenue').value, quoteForm.get('quoteCurrency').value) : ''"
                                            appFcCs [currency]="quoteForm.get('quoteCurrency').value" [showSuffix]="false" readonly />
                                    </span>
                                    <span class="col-3 pr-0 pl-0 value-label" *ngIf="checkFieldShouldbeMasked('totalRevenue', 2)">
                                        {{masking_configuration.maskDisplay || '****'}}
                                    </span>
                                    <span *ngIf="!checkFieldShouldbeMasked('totalRevenue', 2)" class="col pl-0 value-label">{{
                                        quoteForm.get('quoteCurrency').value }}</span>
                                </div>
                                <div class="row">
                                    <div class="col-7 header-label">License Revenue</div>
                                    <span class="col-3 pr-0 pl-0 value-label" *ngIf="!checkFieldShouldbeMasked('totalRevenue', 3)">
                                        <input type="text" class="overflow-class" matInput formControlName="licenseRevenue"
                                            [matTooltip]="quoteForm.get('licenseRevenue').value?.toString().length > 9 ? formatNumberByCurrency(quoteForm.get('licenseRevenue').value, quoteForm.get('quoteCurrency').value) : ''"
                                            appFcCs [currency]="quoteForm.get('quoteCurrency').value" [showSuffix]="false" readonly />
                                    </span>
                                    <span class="col-3 pr-0 pl-0 value-label" *ngIf="checkFieldShouldbeMasked('totalRevenue', 3)">
                                        {{masking_configuration.maskDisplay || '****'}}
                                    </span>
                                    <span *ngIf="!checkFieldShouldbeMasked('totalRevenue', 3)" class="col pl-0 value-label">{{
                                        quoteForm.get('quoteCurrency').value }}</span>
                                </div>
                                <div class="row pt-2" style="border-top: 1px solid #dadce2">
                                    <div class="col-7 header-label">Total Revenue</div>
                                    <span class="col-3 pr-0 pl-0 value-label" *ngIf="!checkFieldShouldbeMasked('totalRevenue', 'FOOTER')">
                                        <input type="text" class="overflow-class" matInput formControlName="totalRevenue"
                                            [matTooltip]="quoteForm.get('totalRevenue').value?.toString().length > 9 ? formatNumberByCurrency(quoteForm.get('totalRevenue').value, quoteForm.get('quoteCurrency').value) : ''"
                                            appFcCs [currency]="quoteForm.get('quoteCurrency').value" [showSuffix]="false" readonly />
                                    </span>
                                    <span class="col-3 pr-0 pl-0 value-label" *ngIf="checkFieldShouldbeMasked('totalRevenue', 'FOOTER')">
                                        {{masking_configuration.maskDisplay || '****'}}
                                    </span>
                                    <span *ngIf="!checkFieldShouldbeMasked('totalRevenue', 'FOOTER')"
                                        class="col pl-0 value-label">{{quoteForm.get('quoteCurrency').value }}</span>
                                </div>
                            </div>
                        </div>
                    </ng-template>

                    <ng-template cdkConnectedOverlay [cdkConnectedOverlayOrigin]="overlayTrigger" #quoteNameRef>
                        <div class="card" style="min-width: 22vw;">
                            <div class="card-body p-1">
                                <div class="row pt-2 pb-1">
                                    <div class="col-10 d-flex align-items-center" style="font-weight: 500; color: #45546E;">
                                        {{
                                        quoteForm.get('quoteType').value !== 'CHANGE_REQUEST'
                                        ? 'Change Quote Name'
                                        : 'Change Request Name'
                                        }}
                                    </div>
                                    <div class="col-2">
                                        <button mat-icon-button (click)="closeQuoteOverlay()">
                                            <mat-icon style="font-size: 15px;color: #1C1B1F;">close</mat-icon>
                                        </button>
                                    </div>
                                </div>
                                <div class="row col-12">
                                    <mat-form-field appearance="outline" style="width: 100%;font-size: 13px;">
                                        <input matInput placeholder="Enter here" [formControl]="quoteNameFormControl">
                                    </mat-form-field>
                                </div>
                                <div class="row col-12 pt-1 pb-2 pl-2">
                                    <button (click)="closeQuoteOverlay()" mat-raised-button class="cancel-btn ml-2 mt-2">
                                        Cancel
                                    </button>
                                    <button (click)="changeQuoteName()" mat-raised-button class="ml-2 mt-2 create-btn">
                                        Save
                                    </button>
                                </div>
                            </div>
                        </div>
                    </ng-template>
                </ng-container>

                <ng-template #mandatoryTemplate>
                    <span style="color: #cf0001;">*</span>
                </ng-template>

                <ng-template #currencyTemplate>
                    {{ quoteForm.get('quoteCurrency').value }}
                </ng-template>

                <ng-container *ngIf="servicesFormArr?.length == 0 && !isDataLoading">
                    <div class="d-flex justify-content-center align-items-center slide-from-down">
                        <img src="https://assets.kebs.app/images/no_data_found.png" class="mt-5 mb-3" height="150"
                            width="200" />
                    </div>

                    <div class="d-flex flex-column pb-2 justify-content-center align-items-center slide-in-top">
                        <span style="font-weight: 600;color: #191729;">No Services Here</span>
                        <span style="font-size: 13px;color: #5f5f5f;font-weight: 500;">
                            Create a quote by listing down the <br>
                            services and see the whole picture.
                        </span>
                    </div>
            
                    <div class="d-flex justify-content-center slign-items-center slide-from-down">
                        <button (click)="addService()" mat-raised-button class="create-btn mt-2">
                            Add Services
                        </button>
                    </div>
                </ng-container>

                <ngx-spinner bdColor="rgba(0, 0, 0, 0)" size="medium" color="#cf0001" type="ball-clip-rotate" [fullScreen]="false">
                    <p style="color: #cf0001; margin-top: 10vh !important; font-weight: 400">
                        Loading Data...
                    </p>
                </ngx-spinner>
            </div>
        </div>
    </form>
</div>

<!-- Submit for approval Dialog -->
<ng-template #submitForApproval let-data>
    <div class="submit-for-approval-dialog" style="min-height: 40vh" *ngIf="!miniLoader">
        <div>
            <div class="row header-submit-pop" mat-dialog-title>
                <div class="heading">
                    <mat-icon> send </mat-icon> &nbsp; Send Quote For Approval
                </div>
                <button class="d-flex justify-content-center align-items-center" mat-icon-button
                    (click)="closeSubmitApprovalPopUp(data, 'SUBMIT_CANCEL')">
                    <mat-icon>close</mat-icon>
                </button>
            </div>
            <div class="qb-details">
                <div class="row">
                    <div class="col-5 d-flex align-items-center">
                        <div class="qb-title">Quote Name:</div>
                    </div>
                    <div class="col-5 d-flex align-items-center">
                        <div class="qb-value">
                            QT/{{ data?.quoteDetails.quote_header_id }} - {{ data?.quoteDetails.quote_name }}
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-5 d-flex align-items-center">
                        <div class="qb-title">Quote Value:</div>
                    </div>
                    <div class="qb-value col-5 d-flex align-items-center">
                        <!-- <app-currency [currencyList]="data?.quoteDetails?.quote_revenue_value" type="small"
                            [showActualAmount]="true"></app-currency> -->
                            {{data?.quoteDetails.quote_revenue_amount + " " + data?.quoteDetails?.quote_currency}}
                    </div>
                </div>
            </div>
            <div class="approvers-profiles ml-3 mt-3">
                <div class="top-header mb-2">
                    <span>
                        Approvers &nbsp;
                    </span>
                </div>
                <div class="d-flex align-items-center justify-content-start approvers-container">
                    <div *ngIf="reviewersList.length == 0" [matTooltip]="'-'">
                        -
                    </div>
                    <div *ngFor="let approver of reviewersList" class="approver-item">
                        <app-user-image [id]="approver.oid" [matTooltip]="approver.employee_name" imgWidth="33px"
                            imgHeight="33px">
                        </app-user-image>
                    </div>
                </div>
            </div>
        </div>
        <div class="d-flex justify-content-center align-items-center mt-3 pt-3">
            <button class="dialog-cancel-btn" mat-button
                (click)="closeSubmitApprovalPopUp(data.quoteDetails, 'SUBMIT_CANCEL')">
                Cancel
            </button>
            <button [disabled]="reviewersList.length == 0"
                [matTooltip]="reviewersList.length == 0 ? 'No Approvers Found!' : ''" mat-raised-button
                class="dialog-submit-btn" mat-button (click)="closeSubmitApprovalPopUp(data?.quoteDetails, 'SUBMIT')">
                Send
            </button>
        </div>
    </div>
    <div class="mini-loader" *ngIf="miniLoader">
        <mat-spinner class="spinner d-flex justify-content-center" diameter="26"></mat-spinner>
    </div>
</ng-template>

<ng-template #approveOrReject let-data>
    <div class="submit-for-approval-dialog" style="min-height: 40vh" *ngIf="!miniLoader">
        <div>
            <div class="row header-submit-pop" mat-dialog-title>
                <div class="heading">
                    <mat-icon> send </mat-icon> &nbsp; {{"Quote Approval"}}
                </div>
                <button class="d-flex justify-content-center align-items-center" matTooltip="Close" mat-icon-button
                    (click)="closeApproverPopUp(data, 'CANCEL')">
                    <mat-icon>close</mat-icon>
                </button>
            </div>
            <div class="qb-details">
                <div class="top-header mb-2" *ngIf="data.quoteDetails.isApprover">
                    <span class="top-header mb-2">
                        Submitted By &nbsp;
                    </span>
                </div>
                <div class="d-flex align-items-center mb-3 submitter-details" *ngIf="data.quoteDetails.isApprover">
                    <app-user-image [id]="data.submitterDetails?.oid" imgWidth="28px" imgHeight="28px">
                    </app-user-image>&nbsp;&nbsp;&nbsp;
                    <div>
                        <div style="font-size: 12px;">A.ID {{ data?.submitterDetails?.associate_id ||
                            submitterDetails?.associate_id}}</div>
                        <div class="reviwer-name"><app-user-profile type="name" [oid]="data.submitterDetails?.oid">
                            </app-user-profile></div>
                    </div>
                </div>
                <div class="approvers-profiles mb-3 mt-3">
                    <div class="top-header mb-2" style="font-weight: 600 !important;">
                        {{data.quoteDetails.isApprover ? 'Approvers for Quote Activation Request' : 'Approvers '}}
                    </div>
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-center justify-content-start approvers-container">
                            <div *ngIf="reviewersList.length == 0" [matTooltip]="'-'">
                                -
                            </div>
                            <!-- <div class="d-flex align-items-center justify-content-between"> -->
                            <div *ngFor="let approver of reviewersList" class="approver-item">
                                <app-user-image [id]="approver.oid" [matTooltip]="getApproverTooltip(approver)"
                                    imgWidth="33px" imgHeight="33px">
                                </app-user-image>
                                <!-- </div> -->
                            </div>

                        </div>
                        <div class="uploader-details-desc d-flex align-items-center         justify-content-center"
                            [ngStyle]="getStatusCss(data.quoteDetails?.quote_status)">
                            {{ data.quoteDetails.status_name || "-" }}
                        </div>
                    </div>

                </div>
                <div class="row">
                    <div class="col-5 d-flex align-items-center">
                        <div class="qb-title">Quote Name:</div>
                    </div>
                    <div class="col-5 d-flex align-items-center">
                        <div class="qb-value">
                            QT/{{ data?.quoteDetails.quote_header_id }} - {{ data?.quoteDetails.quote_name }}
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-5 d-flex align-items-center">
                        <div class="qb-title">Quote Value:</div>
                    </div>
                    <div class="qb-value col-5 d-flex align-items-center">
                        <!-- <app-currency [currencyList]="data?.quoteDetails?.quote_revenue_value" type="small"
                            [showActualAmount]="true"></app-currency> -->
                            {{data?.quoteDetails.quote_revenue_amount + " " + data?.quoteDetails?.quote_currency}}
                    </div>
                </div>
            </div>
            <div class="approvers-profiles ml-3 mt-3">
                <div class="d-flex align-items-center justify-content-start">
                    <span class="top-header" style="font-weight: 600 !important;"> Comments </span>
                    <mat-icon *ngIf="data?.quoteDetails?.isApprover && this.allow_approval"
                        [matTooltip]="'Comments are required for rejecting document'"> info </mat-icon>
                </div>
                <textarea [(ngModel)]="commentToSubmitter"
                    [readonly]="!data.quoteDetails?.isApprover || data.quoteDetails.quote_status == 3 || data.quoteDetails.quote_status == 2"
                    rows="5" cols="40"></textarea>
            </div>

        </div>
        <div class="d-flex justify-content-center align-items-center mt-3 pt-3"
            *ngIf="data.quoteDetails.isApprover && data.quoteDetails.quote_status === 1">
            <button class="dialog-cancel-btn" mat-button (click)="closeApproverPopUp(data.quoteDetails, 'REJECT')">
                Reject
            </button>
            <button mat-raised-button class="dialog-submit-btn" mat-button
                (click)="closeApproverPopUp(data?.quoteDetails, 'APPROVE')">
                Approve
            </button>
        </div>
    </div>
    <div class="mini-loader" *ngIf="miniLoader">
        <mat-spinner class="spinner d-flex justify-content-center" diameter="26"></mat-spinner>
    </div>
</ng-template>