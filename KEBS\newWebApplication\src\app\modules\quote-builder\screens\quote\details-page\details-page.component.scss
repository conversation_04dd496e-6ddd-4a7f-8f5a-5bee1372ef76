.quote-creation-styles {

    .back-button {
        padding: 2px;
        height: 4vh;
        font-size: 12px;
        display: flex;
        align-items: center;
        color: #8B95A5;

        .back-icon {
            font-size: 18px;
            padding-top: 2px;
        }
    }

    .quote-text {
        font-weight: 600;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        cursor: pointer;

        .quote-dot {
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 50%;
            border: none;
            height: 0.65rem;
            width: 0.65rem;
            border-radius: 50%;
            background: #ee4961;
        }

        .quote-type {
            color: #5f5f5f;
            font-size: 11px !important;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            padding-left: 1%;
            padding-top: 2px;
        }
    }

    .status-btn {
        padding: 3px;
        height: 25px;
        width: 80px;
        font-size: 11px;
        max-height: 70px;
        font-weight: bold !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        white-space: nowrap !important;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    @keyframes shakeDetailQuote {

        0%,
        100% {
            transform: translateX(0);
        }

        10% {
            transform: translateX(-5px);
        }

        20% {
            transform: translateX(5px);
        }

        30% {
            transform: translateX(-5px);
        }

        40% {
            transform: translateX(5px);
        }

        50% {
            transform: translateX(-5px);
        }

        60% {
            transform: translateX(5px);
        }

        70% {
            transform: translateX(-5px);
        }

        80% {
            transform: translateX(5px);
        }

        90% {
            transform: translateX(-5px);
        }
    }

    .shakeDetailQuote {
        animation: shakeDetailQuote 0.7s ease-in-out;
    }

    @keyframes shine {
        0% {
            opacity: 0.5;
            color: #ef4a61;
        }

        50% {
            opacity: 1;
            color: #ff6f61;
        }

        100% {
            opacity: 0.5;
            color: #ef4a61;
        }
    }

    .shine-icon {
        animation: shine 1s infinite ease-in-out;
    }

    .header {
        color: #5f5f5f;
        font-size: 11px !important;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .date {
        width: 100%;
        max-height: 15% !important;
        font-size: 12px;
    }

    .date-picker-class {

        ::ng-deep .mat-form-field-underline {
            display: none;
        }

        .mat-form-field {

            ::ng-deep .mat-form-field-infix {
                padding-bottom: 0px !important;
                margin-top: -7px;
            }

            ::ng-deep .mat-form-field-wrapper {
                padding-bottom: 0px !important;
            }

        }
    }

    .create-btn {
        background: linear-gradient(270deg, #ef4a61, #f27a6c 105.29%) !important;
        color: #fff;
        font-size: 12px !important;
        line-height: 30px;
        border-radius: 4px;
    }

    .save-btn {
        background: linear-gradient(270deg, #ef4a61, #f27a6c 105.29%) !important;
        color: #fff;
        font-size: 12px !important;
        line-height: 30px;
        border-radius: 4px;
    }

    .preview-btn {
        background: #fff;
        font-size: 12px !important;
        line-height: 30px;
        border-radius: 4px;
        height: 30px !important;
    }

    .tag-btn{
        background: #fff;
        font-size: 12px !important;
        line-height: 30px;
        border-radius: 4px;
        border: 1px solid #45546E;

        height: 30px;
        margin-top: 9px;
    

        mat-icon{
            font-size: 19px;
        }
    }

    .other-btn {
        background: #fff;
        font-size: 12px !important;
        line-height: 30px;
        border-radius: 4px;
        min-width: unset;
        width: 33px;
        padding: 0;
    }

    .service-icon-btn {
        font-size: 16px;
        color: #908b8b;
    }

    .revenue-item {
        font-weight: 600;
        font-size: 13px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        width: -webkit-fill-available;
    }

    .revenue-value {
        font-weight: 500;
        color: #45546E;
        font-size: 13px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .form-field-class {
        width: 90%;
        font-size: 12px;

        ::ng-deep .mat-form-field {
            width: 90%;
            font-size: 12px !important;
        }

    }

    .add-btn {
        color: #5F6C81;
        font-size: 12px;
        font-weight: 500;
    }


    .more-btn {
        width: 23px;

        .more-btn-icon {
            color: #6E7B8F;
            font-size: 15px;
        }
    }

    .dt-class {
        font-size: 12px;
        font-weight: 500;
        color: #45546E;
    }

    .label-class {
        font-size: 11px;
        color: #5F6C81;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        line-height: 10px;
    }

    .cdk-drag-animating {
        transition: transform 250ms cubic-bezier(0, 0, .2, 1);
    }

    .cdk-drop-list-dragging .drag-class:not(.cdk-drag-placeholder) {
        transition: transform 250ms cubic-bezier(0, 0, .2, 1);
    }

    .display-class {
        overflow: hidden;
        color: #5F6C81;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 12px;
        font-weight: 600;
        padding: 8px 0px 8px 0px;
    }

    .suffix-class {
        color: #5F6C81;
        font-size: 11px;
        padding-left: 5px;
    }

    .overflow-class {
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .service-name {
        font-weight: 500;
        font-size: 13px;
        max-width: 75vw;
        white-space: nowrap;
    }

    .slide-in-right {
        -webkit-animation: slide-in-right 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
        animation: slide-in-right 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
    }

    @-webkit-keyframes slide-in-right {
        0% {
            -webkit-transform: translateX(30px);
            transform: translateX(30px);
            opacity: 0;
        }

        100% {
            -webkit-transform: translateX(0);
            transform: translateX(0);
            opacity: 1;
        }
    }

    @keyframes slide-in-right {
        0% {
            -webkit-transform: translateX(30px);
            transform: translateX(30px);
            opacity: 0;
        }

        100% {
            -webkit-transform: translateX(0);
            transform: translateX(0);
            opacity: 1;
        }
    }

    .slide-in-top {
        -webkit-animation: slide-in-top 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
        animation: slide-in-top 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
    }

    /**
       * ----------------------------------------
       * animation slide-in-top
       * ----------------------------------------
       */
    @-webkit-keyframes slide-in-top {
        0% {
            -webkit-transform: translateY(-30px);
            transform: translateY(-30px);
            opacity: 0;
        }

        100% {
            -webkit-transform: translateY(0);
            transform: translateY(0);
            opacity: 1;
        }
    }

    @keyframes slide-in-top {
        0% {
            -webkit-transform: translateY(-30px);
            transform: translateY(-30px);
            opacity: 0;
        }

        100% {
            -webkit-transform: translateY(0);
            transform: translateY(0);
            opacity: 1;
        }
    }

    .slide-from-down {
        -webkit-animation: slide-from-down 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
        animation: slide-from-down 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
    }

    /**
       * ----------------------------------------
       * animation slide-from-bottom
       * ----------------------------------------
       */
    @-webkit-keyframes slide-from-down {
        0% {
            -webkit-transform: translateY(30px);
            transform: translateY(30px);
            opacity: 0;
        }

        100% {
            -webkit-transform: translateY(0);
            transform: translateY(0);
            opacity: 1;
        }
    }

    @keyframes slide-from-down {
        0% {
            -webkit-transform: translateY(30px);
            transform: translateY(30px);
            opacity: 0;
        }

        100% {
            -webkit-transform: translateY(0);
            transform: translateY(0);
            opacity: 1;
        }
    }

    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
    }

    ::ng-deep .mat-badge-content {
        background: linear-gradient(270deg, #ef4a61, #f27a6c 105.29%) !important;
        color: #fff !important;
    }

}

.section-label {
    font-size: 12px;
    color: #5F6C81;
    line-height: 8px;
}

.create-btn {
    background: linear-gradient(270deg, #ef4a61, #f27a6c 105.29%) !important;
    color: #fff;
    font-size: 12px !important;
    line-height: 30px;
    border-radius: 4px;
}

.create-btn-disabled {
    font-size: 12px !important;
    line-height: 30px;
    border-radius: 4px;
}

.cancel-btn {
    background: #fff;
    font-size: 12px !important;
    line-height: 35px;
    border-radius: 4px;
}

.quote-transparent-overlay-backdrop {
    ::ng-deep.cdk-overlay-backdrop.cdk-overlay-backdrop-showing {
        opacity: 0;
    }
}

.header-label {
    color: #45546E;
    font-size: 11px;
    font-weight: 400;
}

.value-label {
    color: #45546E;
    text-align: right;
    font-size: 11px;
    font-weight: 600;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;

    input {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
}

.menu-header-class {
    display: flex;
    max-height: 5vh;
    align-items: center;
}

.menu-item-class {
    font-size: 11px;
    color: #45546E;
    font-weight: 400;
}

.custom-mat-menu {
    height: 15rem !important;
    overflow: hidden !important;
}

.menu-scroll {
    max-height: 15rem;
    overflow-y: auto;
}

.submit-for-approval-dialog {
    overflow-y: auto;
    overflow-x: hidden;
    padding: 1rem;
    min-height: 40vh
}

.dialog-title {
    font-family: "Plus Jakarta Sans";
    font-size: 18px;
    font-weight: 700;
    line-height: 28px;
    text-align: center;
}

.dialog-desc {
    font-family: "Plus Jakarta Sans";
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    text-align: center;
    color: #475467;
}

.dialog-submit-btn {
    background: #ef4a61 !important;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 5rem;
    color: #fff !important;
    font-size: 14px;
    font-weight: 500 !important;
    border: 1px solid #ef4a61 !important;
}

.dialog-cancel-btn {
    border: 1px solid #45546e;
    border-radius: 4px;
    font-family: "Plus Jakarta Sans";
    font-size: 14px;
    font-weight: 500 !important;
    display: flex;
    width: 5rem;
    align-items: center;
    justify-content: center;
    margin-left: auto;
    margin-right: 1.5rem;
}

.heading {
    display: flex;
    align-items: center;
    justify-content: start;
    font-weight: bold;
    font-size: 18px;
    padding-top: 0.2rem;
    // margin-left: 25px;
    color: #272A47;
    font-family: "Plus Jakarta Sans" !important;

    mat-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 22px;
        color: #272A47;
    }
}

.top-header {
    font-size: 13px;
    font-weight: 600;
    line-height: 16px;
    letter-spacing: 0.02em;
    text-align: left;
    color: #45546e;
    font-family: "Plus Jakarta Sans";
}


.approvers-profiles {
    textarea {
        border: 1px solid #dadce2;
        font-family: "Plus Jakarta Sans";
        color: #45546e;
        font-size: 13px;
        font-weight: 700;
        line-height: 16px;
        letter-spacing: 0.02em;
        text-align: left;
        margin-top: 1%;
        height: 5rem;
        resize: none;
        padding: 1rem;
        border-radius: 3px;
        width: 95%;
    }

    textarea:focus {
        border: 2px solid #ef4a61 !important;
        outline: none;
    }


    // display: flex;
    align-items: center;
    justify-content: start;
    gap: 5%;

    mat-icon {
        font-size: 18px;
        color: #45546e;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .top-header {
        font-size: 13px;
        font-weight: 500;
        line-height: 16px;
        letter-spacing: 0.02em;
        text-align: left;
        color: #45546e;
        font-family: "Plus Jakarta Sans";
    }

    .uploader-details-desc {
        margin-top: 3%;
        font-size: 12px;
        font-weight: 600;
        line-height: 16px;
        letter-spacing: 0.02em;
        text-align: left;
        display: flex;
        align-items: center;
        border-radius: 4px;
        height: 2rem;
        padding: 2%;
    }

    .approvers-container {
        position: relative;
    }

    .approver-item {
        margin-left: -10px;
        z-index: auto;
    }

    .approver-item:first-child {
        margin-left: 0;
    }
}

.submitter-details {
    font-size: 13px;
    font-weight: 500;
    color: #45546e;
    text-transform: none;
    overflow: hidden;
    text-overflow: ellipsis;
    font-family: Plus Jakarta Sans;
    white-space: nowrap;
}

.qb-details {
    padding: 0rem 1rem;
}

.qb-title {
    font-size: 13px;
    font-weight: 700;
    color: #45546e;
    text-transform: none;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.reviewer-name {
    font-size: 12px;
    margin-top: -2%;
    font-weight: bold;
}

.qb-value {
    font-size: 14px;
    font-weight: 500;
    color: #45546e;
    text-transform: none;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.mini-loader {
    min-height: 40vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.header-submit-pop {
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #5f5f5f;
    font-size: 11px !important;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.uniq-icons{
    display: flex;
    justify-content: center;
    font-size: 18px;
    color: #5F6C81;
    cursor: default;
}