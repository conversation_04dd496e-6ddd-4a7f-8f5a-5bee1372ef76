import { Component, OnInit, ViewContainerRef, ViewChild, TemplateRef, ElementRef, HostListener } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { FormBuilder, FormGroup, Validators, FormArray, AbstractControl, FormControl, Form } from '@angular/forms';
import { MomentDateAdapter, MAT_MOMENT_DATE_ADAPTER_OPTIONS } from '@angular/material-moment-adapter';
import { DateAdapter, MAT_DATE_LOCALE, MAT_DATE_FORMATS } from '@angular/material/core';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { Overlay, OverlayRef } from '@angular/cdk/overlay';
import { TemplatePortal } from '@angular/cdk/portal';

import { Subject, Subscription, timer } from 'rxjs';
import { debounce, debounceTime, distinctUntilChanged, takeUntil } from 'rxjs/operators';
import { trigger, transition, style, animate, query, stagger } from '@angular/animations';

import { QuoteMainService } from '../../../services/quote-main.service';
import { QBMasterDataService } from '../../../services/qb-master-data.service';
import { ToasterService } from 'src/app/services/toaster-service/toaster.service';
import { NgxSpinnerService } from 'ngx-spinner';
import { UtilityService } from 'src/app/services/utility/utility.service';
import moment from 'moment';
import { OpportunityService } from "src/app/modules/opportunities/features/opportunities-detail/services/OpportunityService";
import { QouteLandingPageService } from '../../../services/qoute-landing-page.service';
import { TicketService } from 'src/app/modules/ams/services/ticket.service';
import { v4 as uuidv4 } from 'uuid';

@Component({
  selector: 'app-details-page',
  templateUrl: './details-page.component.html',
  styleUrls: ['./details-page.component.scss'],
  providers: [
    {
      provide: DateAdapter,
      useClass: MomentDateAdapter,
      deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS]
    },
    {
      provide: MAT_DATE_FORMATS,
      useValue: {
        parse: {
          dateInput: "DD MMM YYYY"
        },
        display: {
          dateInput: "DD MMM YYYY",
          monthYearLabel: "MMM YYYY"
        }
      }
    },
  ],
  animations: [
    trigger('listAnimation', [
      transition('* => *', [
        query(':enter', [
          style({ opacity: 0, transform: 'translateY(-10px)' }),
          stagger(100, [
            animate('0.2s ease-out', style({ opacity: 1, transform: 'translateY(0)' }))
          ])
        ], { optional: true }),
        query(':leave', [
          //for immediate leaving
          // animate('0.3s', style({ opacity: 0, transform: 'translateY(10px)' }))
          style({ opacity: 1, transform: 'translateY(0)' }),
          stagger(100, [
            animate('0.2s ease-in', style({ opacity: 0, transform: 'translateY(-10px)' }))
          ])
        ], { optional: true })
      ])
    ]),
    trigger('listPositionAnimation', [
      transition('* => *', [
        query(':enter', [
          style({ opacity: 0, transform: 'translateY(-10px)' }),
          stagger(100, [
            animate('80ms ease-out', style({ opacity: 1, transform: 'translateY(0)' }))
          ])
        ], { optional: true }),
        query(':leave', [
          //for immediate leaving
          // animate('0.3s', style({ opacity: 0, transform: 'translateY(10px)' }))
          style({ opacity: 1, transform: 'translateY(0)' }),
          stagger(100, [
            animate('80ms ease-in', style({ opacity: 0, transform: 'translateY(-10px)' }))
          ])
        ], { optional: true })
      ])
    ]),
    trigger('editModeTransition', [
      transition('* => *', [
        query(':enter', [
          style({ opacity: 0, transform: 'translateY(-10px)' }),
          stagger(100, [
            animate('0.2s ease-out', style({ opacity: 1, transform: 'translateY(0)' }))
          ])
        ], { optional: true }),
        query(':leave', [
          //for immediate leaving
          // animate('0.3s', style({ opacity: 0, transform: 'translateY(10px)' }))
          style({ opacity: 1, transform: 'translateY(0)' }),
          stagger(100, [
            animate('0.2s ease-in', style({ opacity: 0, transform: 'translateY(-10px)' }))
          ])
        ], { optional: true })
      ])
    ]),
    trigger('buttonFadeInOut', [
      transition(':enter', [
        style({ opacity: 0, transform: 'translateY(-10px)' }),
        animate('200ms ease-out', style({ opacity: 1, transform: 'translateY(0)' }))
      ]),
      transition(':leave', [
        animate('200ms ease-in', style({ opacity: 0, transform: 'translateY(-10px)' }))
      ])
    ]),
    trigger('buttonSlideFade', [
      // transition(':enter', [
      //   style({ opacity: 0, transform: 'translateX(-20px)' }),
      //   animate('200ms ease-out', style({ opacity: 1, transform: 'translateX(0)' }))
      // ]),
      // transition(':leave', [
      //   animate('200ms ease-in', style({ opacity: 0, transform: 'translateX(20px)' }))
      // ])
      transition('* => *', [
        query(':enter', [
          style({ opacity: 0, transform: 'translateX(-20px)'}),
          stagger(100, [
            animate('0.2s ease-out', style({ opacity: 1, transform: 'translateX(0)' }))
          ])
        ], { optional: true }),
        query(':leave', [
          //for immediate leaving
          // animate('0.3s', style({ opacity: 0, transform: 'translateY(10px)' }))
          style({ opacity: 1, transform: 'translateX(0)' }),
          stagger(100, [
            animate('0.2s ease-in', style({ opacity: 0, transform: 'translateX(-20px)' }))
          ])
        ], { optional: true })
      ])
    ])

  ]
})

export class DetailsPageComponent implements OnInit {

  quoteForm: FormGroup;

  protected _onDestroy = new Subject<void>();
  valueChangeSubscription = new Subscription();
  routeDataSubscription = new Subscription();
  quoteCreateDataSubscription = new Subscription();

  opportunityId = null;
  skillList = [];
  positionList = [];
  nmpList = [];
  licenseList = [];
  experienceList = [];
  workLocationList = [];
  nationalityList = [];
  entityList = [];
  divisionList = [];
  subDivisionList = [];
  unitList = [];
  otherUnitList = [];
  manPowerUnit = [];
  nonManPowerUnit = [];
  licenseUnit = [];
  discountsList = [];
  taxesList = [];
  orgMappingList = [];
  wEMappingList = [];
  sDMappingList = [];
  businessTypeList = [];
  geographicalRegionList = [];
  positionStatusList = [];

  addButtonsList = [];

  positionDropZones = [];

  initialServiceDragIndex = null;
  
  allocation_details:any=[]

  sectionOverlayRef: OverlayRef;
  costOverlayRef: OverlayRef;
  revOverlayRef: OverlayRef;
  quoteOverlayRef: OverlayRef;

  approveOrRejectRef: MatDialogRef<any>;
  submitQuoteForApprovalRef: MatDialogRef<any>;

  has_date_change_access: boolean = false;
  editRestricted=false;
  editRestrictedMsg='';
  project_access_field_config = [];
  newly_added_quote_project_integration_config = [];
  quoteDuration:any;
  @ViewChild('sectionTemplateRef', { static: false })
  private templateRef!: TemplateRef<HTMLElement>;

  @ViewChild('resourceCostRef', { static: false })
  private costTemplateRef!: TemplateRef<HTMLElement>;

  @ViewChild('revSummaryRef', { static: false })
  private revTemplateRef!: TemplateRef<HTMLElement>;

  @ViewChild('quoteNameRef', { static: false })
  private quoteTemplateRef!: TemplateRef<HTMLElement>;

  @ViewChild('approveOrReject')
  private approveOrRejectTemplate: TemplateRef<any>;

  @ViewChild('submitForApproval')
  private submitQuoteForApprovalTemplate: TemplateRef<any>;

  addSectionFormControl = new FormControl(null);
  quoteNameFormControl = new FormControl(null);

  @ViewChild('scrollFrame', {}) scrollFrame: ElementRef;
  private scrollContainer: any;

  costSummary = {
    resourceCost: 0,
    nonManpowerCost: 0,
    total: 0
  };

  revenueSummary = {
    revenue: 0,
    cost: 0,
    grossMargin: 0
  };

  isEditMode: boolean = true;
  isDataLoading: boolean = false;
  isDataBeingSaved: boolean = false;
  isQuoteCreateMode: boolean = false;
  isChangesMade: boolean = false;
  isCUDEnabled: boolean = true;
  isRevenueActive: boolean = true;
  isCostActive: boolean = true;
  isGMActive: boolean = true;
  isGMPerActive: boolean = true;
  wEMappingEnabled: boolean = false;
  sDMappingEnabled: boolean = false;

  customizeFields = [];
  mandatoryFields = [];
  fieldConfig = [];
  milestoneFieldConfig : any = [];
  quoteId = null;
  calendarId = null;
  workScheduleId = null;
  defaultCurrency = null;
  conversionTypeId = null;
  positionField = {
    label: 'Position',
    col: 3
  };

  initial_delivery_start_date: any;
  initial_delivery_end_date: any;

  //Project Auth Field Config
  checkProjectCreated: boolean = false;
  isPositionFieldEnabled : boolean = true;
  isUnitEnabled : boolean = true;
  isWorkLocationEnabled : boolean = true;
  isQuantityEnabled : boolean = true;
  isNoOfResourcesEnabled : boolean = true;
  isRatePerUnitEnabled : boolean = true;
  isCostPerUnitEnabled : boolean = true;
  isAddServiceEnabled : boolean = true;
  isDeleteServiceEnabled : boolean = true;
  isAddPositionEnabled : boolean = true;
  isAddPositionInlineEnabled : boolean = true;
  isDeletePositionEnabled : boolean = true;
  isClonePositionEnabled : boolean = true;
  isCustomiseSlotEnabled : boolean = true;
  interServicePositionDragNDrop : boolean = true;
  isNationalityEnabled : boolean = true;
  isWorkExperienceEnabled : boolean = true;
  isAddSectionEnabled : boolean = true;
  isTypeOfBusinessEnabled : boolean = true;
  isRevenueRegionEnabled : boolean = true;
  isPositionStatusEnabled : boolean = true;
  isPositionStatusToLost : boolean = true;
  isMilestoneEnabled: boolean = true;

  isNonManPowerButtonEnabled : boolean = true;
  isLicenseButtonEnabled : boolean = true;
  isDiscountButtonEnabled : boolean = true;
  isTaxButtonEnabled : boolean = true;
  isMilestoneButtonEnabled : boolean = true;
  deliveryStartDateEnabled: boolean = true;
  deliveryEndDateEnabled: boolean = true;
  projectDetailsForOpportunity: any;
  isQuoteActive: boolean = true;
  wholeQuoteData: any;
  durationData: any;
  currencyList: any = [];
  milestoneList: any = [];
  quote_currency_change: boolean = false;
  quoteApproveStatusList: any;
  commentToSubmitter: string = '';
  miniLoader: boolean;
  reviewersList: any;
  submitterDetails: any = [];
  manpowerNonManpowerMapping: boolean = false;
  geographicRevenueRegionMapping: boolean = false;
  calendarCombinationConfig: any = [];
  revenueRegionMapping: any = [];
  oppQuoteDetails: any = [];
  masking_configuration: any;
  quote_footer_splitup_configuration: any = {};
  quoteMilestoneTagging: boolean = false;
  isChangeRequestActive: boolean = false;

  public milestoneFormArray: FormArray;
  milestoneFormGroup: FormGroup;
  private previousMilestoneId: number | null = null; 
  positionMilestoneDropZones: any = [];
  milestoneListCopy: any[];
  milestoneApplicable: boolean = false;
  lumpsum_quote_config: any = [];
  oppServiceType:any;
  service_based_unit=false;
  initial_creation_qb_data: any;

  constructor(
    private route: ActivatedRoute,
    private $router: Router,
    private fb: FormBuilder,
    private dialog: MatDialog,
    private viewContainerRef: ViewContainerRef, private overlay: Overlay,
    private _quoteMainService: QuoteMainService,
    private _toaster: ToasterService,
    private masterDataService: QBMasterDataService,
    private spinner: NgxSpinnerService,
    private _util: UtilityService,
    private opportunityService: OpportunityService,
    public _landingPageService: QouteLandingPageService,
    private _ticket: TicketService
  ) {
    this.initializeForm();
  }

  async ngOnInit() {

    this.routeDataSubscription.add(this.route.data.pipe(takeUntil(this._onDestroy)).subscribe(({ opportunity }) => {
      this.opportunityId = parseInt(opportunity['opportunityId']);
    }));

    await this._quoteMainService.getOpportunityMetaDetailsForQuote(this.opportunityId)
    .pipe(takeUntil(this._onDestroy))
    .subscribe(res => {
  
      if (res && res['messType'] == 'S' && res['data']) {
        this.oppServiceType = res['data']['service_type'];
      }})

    await this.loadProjectDetailsForOpportunity();

    this.routeDataSubscription.add(this.route.params.pipe(takeUntil(this._onDestroy)).subscribe(val => {

      this.quoteId = null;

      if (val && val['quoteId'] && parseInt(val['quoteId']))
        this.quoteId = parseInt(val['quoteId']);

      if (!this.quoteId) { // Create new quote

        this.isQuoteCreateMode = true;
        
        if (this.quoteCreateDataSubscription)
          this.quoteCreateDataSubscription.unsubscribe();

        this.quoteCreateDataSubscription = this._quoteMainService.intialQuoteDetail.pipe(takeUntil(this._onDestroy)).subscribe(val => {

          if (val && val['quoteName'] && val['currency']) {
            this.quoteForm.patchValue({
              quoteName: val['quoteName'],
              quoteCurrency: val['currency'],
              deliveryStartDate: val['deliveryStartDate'] || null,
              deliveryEndDate: val['deliveryEndDate'] || null,
              quoteBusinessType: val['businessType'] || null,
              enableMilestoneTagging: val['quoteType'] == 'TRADITIONAL',
              serviceOppTypeId: val['serviceTypeId'] || null,
              quoteType: val['quoteType']
            }, { emitEvent: false });
            this.initial_creation_qb_data = val
            this.quoteDuration = this.getDurationInDays(this.initial_creation_qb_data.deliveryStartDate, this.initial_creation_qb_data.deliveryEndDate)
          }      

          else
            this.navigateToLandingPage();

        });

      }
      this.checkQuoteEditableBasedOnConfig(this.quoteId)

    }));

    this.getQuoteConfiguration();

  }

   /**
   * @description Loads project data for the opportunity
   */
  async loadProjectDetailsForOpportunity() {
    try {
      this.projectDetailsForOpportunity = await this.opportunityService.getOpportunityProjectDetailsForQuote(this.opportunityId);
    } catch (err) {
      console.error("Error Retrieving Project Details for this Opportunity", err);
    }
  }

  getQuoteConfiguration = () => {

    this.masterDataService.quoteConfiguration
      .pipe(takeUntil(this._onDestroy))
      .subscribe(async res => {

        const quoteConfiguration = res;

        for (const configItem of quoteConfiguration) 
          if (configItem && configItem['quote_config_name'] && configItem.hasOwnProperty('quote_config_value')) {

            switch (configItem['quote_config_name']) {

              case 'quote_mandatory_fields':

                this.mandatoryFields = configItem['quote_config_value'];
                
                break;

              case 'quote_field_config':
                
                this.fieldConfig = configItem['quote_config_value'];
                
                break;

              case 'quote_action_button_config':
                
                if (configItem['quote_config_value']) {
                  const btnList = configItem['quote_config_value'];

                  for (const btn of btnList) {
                    btn["has_project_integrated"] = true;

                    const notAllowedTypes = btn['notApplicableForQuoteType'] || [];
                    const currentQuoteType = this.wholeQuoteData?.quote_type || this.quoteForm.get('quoteType').value;

                    const isAllowedForCurrentQuote = !notAllowedTypes.includes(currentQuoteType);

                    if (btn['isActive'] && isAllowedForCurrentQuote) {
                      this.addButtonsList.push(btn);
                    }
                  }
                }

                break;

              case 'quote_calendar_id':
                
                this.calendarId = configItem['quote_config_value'];
                break;

              case 'quote_work_schedule_id':

                this.workScheduleId = configItem['quote_config_value'];
              
                break;

              case 'quote_currency':

                this.defaultCurrency = configItem['quote_config_value'];
              
                break;

              case 'quote_currency_conversion_type_id':

                this.conversionTypeId = configItem['quote_config_value'];
              
                break;

              case 'quote_work_location_entity_mapping_enabled':

                this.wEMappingEnabled = configItem['quote_config_value'] || false;

                break;

              case 'quote_service_division_mapping_enabled':

                this.sDMappingEnabled = configItem['quote_config_value'] || false;

                break;

              case 'quote_date_field_config':

                this.has_date_change_access = configItem['quote_config_value'] || false;

                if (this.has_date_change_access) {
                  try {
                    if (this.projectDetailsForOpportunity && 
                        this.projectDetailsForOpportunity['messType'] == "S" &&
                        this.projectDetailsForOpportunity['messData']?.length > 0) {
                      // console.log("Project Data: ", this.projectDetailsForOpportunity);
                      const quoteConfig = quoteConfiguration.find(config => config.quote_config_name == 'quote_project_integration_config');
                      if (quoteConfig) {
                        const configValues = quoteConfig.quote_config_value;
                        const startDateConfig = configValues.find(config => config.key == 'delivery_start_date');
                        const endDateConfig = configValues.find(config => config.key == 'delivery_end_date');
                        
                        this.deliveryStartDateEnabled = startDateConfig?.editEnabled ?? false;
                        this.deliveryEndDateEnabled = endDateConfig?.editEnabled ?? false;
                      }
                    }
                  } catch (err) {
                    console.error("Error Retrieving Project Details for this Opportunity", err);
                  }
                }

                if(!this.has_date_change_access){
                  this.deliveryStartDateEnabled = false;
                  this.deliveryEndDateEnabled = false;
                }
                
                break;
              
              
                case 'quote_project_integration_config':

                this.project_access_field_config = configItem['quote_config_value'];

                // this.alloteFieldConfig();

              break;

              case 'newly_added_quote_project_integration_config':

                this.newly_added_quote_project_integration_config = configItem['quote_config_value'];

              break;

              // 2 Layer config - If Project Not Integrated, Common Config will be applied.
              // If Project Integrated, Project Integration Config will be applied.

              case 'quote_currency_change':

                this.quote_currency_change = configItem['quote_config_value'];

                const quoteConfig = quoteConfiguration.find(config => config.quote_config_name === 'quote_project_integration_config');

                const hasProjectIntegrated = this.projectDetailsForOpportunity?.messType === "S" &&
                this.projectDetailsForOpportunity?.messData?.length > 0;

                if (this.quote_currency_change && hasProjectIntegrated && quoteConfig) {

                  const quoteCurrencyConversion = quoteConfig.quote_config_value?.find(config => config.key === 'quote_currency_change');

                  this.quote_currency_change = quoteCurrencyConversion?.editEnabled || false;

                }

                break;

              case 'quote_mp_nmp_mapping':

                this.manpowerNonManpowerMapping = configItem['quote_config_value'] || false;

              break;

              case 'quote_geograpical_revenue_region_mapping_enable':

                this.geographicRevenueRegionMapping = configItem['quote_config_value'] || false;

              break;

              case 'quote_milestone_tagging':

                this.quoteMilestoneTagging = configItem['quote_config_value'] || false;

              break;

              case 'quote_milestone_field_config':
                
                this.milestoneFieldConfig = configItem['quote_config_value'] || [];
              
              break;

              case 'lumpsum_quote_config':

                this.lumpsum_quote_config = configItem['quote_config_value'] || [];

              break;

              case 'quote_masking_configuration':

                this.masking_configuration = configItem['quote_config_value'] || null;

              break;

              case 'quote_footer_splitup':

                this.quote_footer_splitup_configuration = configItem['quote_config_value'] || {};

              break;

              case 'service_based_unit':

                this.service_based_unit = true;

              break;

              default:
                break;
            }

          }

        if (this.sDMappingEnabled && this.addButtonsList.length) // Removing Section Option When sDMapping is Enabled
          this.addButtonsList = this.addButtonsList.filter(val => val['id'] != 1);

        this.initializeMasterData();

      });

  }  

  /**
  * @description Checks and Allotes the Field Config based conditions
  */
  alloteFieldConfig() {
    if (
      this.project_access_field_config &&
      this.projectDetailsForOpportunity &&
      this.projectDetailsForOpportunity["messType"] == "S" &&
      this.projectDetailsForOpportunity["messData"]?.length > 0 &&
      this.isQuoteActive
    ) {
      this.checkAndAlloteProjectFieldConfig();
    }
  }

  /**
* @description Drag N Drop condition check for Position
*/
  isDragDisabled(serviceItem: any, positionItem: any): boolean {
    if (!this.isEditMode) {
      return true;
    }
    if (!positionItem.get('interServicePositionDragNDrop').value) {
      return true;
    }

    if (serviceItem.get('positions')['controls'].length == 1) {
      return true;
    }

    return false;
  }

  /**
  * @description Checks and Allotes the Field Config for new fields added after Project Linked to Quote's Opportunity
  */
  newAddedPostionAfterProjectInterationConfig(config) {
    const booleanMap = {};
    config.forEach(item => {
      booleanMap[item?.variableName] = item.editEnabled;
    });
    return booleanMap;
  }

  /**
  * @description Checks and Allotes the default value
  */
  allocateDefaultValues(config) {
    const defaultValuesMap = {};

    config.forEach(item => {
      defaultValuesMap[item.key] = item?.defaultValue ?? null;
    });

    defaultValuesMap['typeOfBusiness'] = this.quoteForm?.get('quoteBusinessType').value || this.wholeQuoteData?.business_type;

    // Find the milestone with the least ID, prioritizing milestones with is_master === 1
    // let suitableMilestone = this.milestoneList.reduce((prev, curr) =>
    //   (curr.is_master === 1 && curr.id < (prev?.id || Infinity)) ||
    //     (!prev && curr.id < (prev?.id || Infinity)) ? curr : prev, null)

    // Find the milestone with the least ID, prioritizing milestones with falsy is_master
    let suitableMilestone = this.milestoneList
    .sort((a, b) => a.id - b.id)
    .find(m => !m.is_master || m.is_master === 0) || this.milestoneList.find(m => m.is_master === 1);

    defaultValuesMap['milestone'] != null ? defaultValuesMap['milestone'] = (suitableMilestone?.id || null) : defaultValuesMap['milestone'] = null;

    return defaultValuesMap;
  }


  /**
  * @description Checks and Allotes the Field Config based on the Project Linked to Quote's Opportunity
  */

  checkAndAlloteProjectFieldConfig() {
    // Mapping of config keys to corresponding variable names
    const keyToVariableMap = {
      positionId: 'isPositionFieldEnabled',
      unit: 'isUnitEnabled',
      workLocation: 'isWorkLocationEnabled',
      quantity: 'isQuantityEnabled',
      noOfResources: 'isNoOfResourcesEnabled',
      ratePerUnit: 'isRatePerUnitEnabled',
      costPerUnit: 'isCostPerUnitEnabled',
      add_service: 'isAddServiceEnabled',
      delete_service: 'isDeleteServiceEnabled',
      add_position: 'isAddPositionEnabled',
      add_position_inline: 'isAddPositionInlineEnabled',
      delete_position: 'isDeletePositionEnabled',
      clone_position: 'isClonePositionEnabled',
      customise_slot: 'isCustomiseSlotEnabled',
      add_tax: 'isTaxButtonEnabled',
      add_license: 'isLicenseButtonEnabled',
      add_tAndl: 'isNonManPowerButtonEnabled',
      add_discount: 'isDiscountButtonEnabled',
      enable_position_drag: 'interServicePositionDragNDrop',
      typeOfBusiness : 'isTypeOfBusinessEnabled',
      positionStatus : 'isPositionStatusEnabled',
      nationality: 'isNationalityEnabled',
      experience: 'isWorkExperienceEnabled',
      add_section: 'isAddSectionEnabled',
      revenueRegion : 'isRevenueRegionEnabled',
      positionStatusToLost: 'isPositionStatusToLost',
      milestone : 'isMilestoneEnabled',
      add_milestone: 'isMilestoneButtonEnabled',
    };

    // Update configuration based on project access field config
    for (const config of this.project_access_field_config) {
      const propertyName = keyToVariableMap[config.key];
      // this.projectFieldconfig[config.key] = config.editEnabled;
      if (propertyName) {
        this[propertyName] = config.editEnabled;
      }

    }

    // Console Log all configured values 
    // for (const key in keyToVariableMap) {
    //   if (Object.prototype.hasOwnProperty.call(keyToVariableMap, key)) {
    //     console.log(`${key}:`, this[keyToVariableMap[key]]);
    //   }
    // }

    // Update Project Integration Config in the Bottom Action Buttons
    for (const btn of this.addButtonsList) {
      switch (btn['key']) {
        case 'service':
          btn['has_project_integrated'] = this.isAddServiceEnabled;
          break;

        case 'position':
          btn['has_project_integrated'] = this.isAddPositionEnabled;
          break;

        case 'nonManpower':
          btn['has_project_integrated'] = this.isNonManPowerButtonEnabled;
          break;

        case 'license':
          btn['has_project_integrated'] = this.isLicenseButtonEnabled;
          break;

        case 'discount':
          btn['has_project_integrated'] = this.isDiscountButtonEnabled;
          break;

        case 'tax':
          btn['has_project_integrated'] = this.isTaxButtonEnabled;
          break;

        case 'milestone':
          btn['has_project_integrated'] = this.isMilestoneButtonEnabled;
          break;

        default:
          break;
      }
    }
    
    // console.log(this.addButtonsList, "Button List!");

    // console.log(this.customizeFields, 'All field Configuration after project integration');

  }

  /**
  * @description Gets the opportunity meta details for quote
  */
  getQuoteAccessPrivilege = () => {

    let CR_ENABLED_OBJECT = this._landingPageService.checkCREnabled();

    this._quoteMainService.getQuoteAccessPrivilege(this.opportunityId)
      .pipe(takeUntil(this._onDestroy))
      .subscribe(res => {

        if (res && res['messType'] == 'S' && res['data']) {

          if (this.wholeQuoteData?.quote_type !== 'CHANGE_REQUEST' || this.quoteForm.get('quoteType').value != 'CHANGE_REQUEST')
            this.isCUDEnabled = res['data']['cud_access_enabled'] || false;

          if (this.wholeQuoteData?.quote_type === 'CHANGE_REQUEST' || this.quoteForm.get('quoteType').value == 'CHANGE_REQUEST' && CR_ENABLED_OBJECT)
            this.isCUDEnabled = true
        }

        else
          this.isCUDEnabled = false;

      },
        err => {
          this.isCUDEnabled = false;
          console.log(err);
          this._toaster.showError("Error", "Error in getting Quote Edit Config", this.opportunityService.longInterval);
        });

  }

  /**
   * @description Patches the change in delivery date to the position date
   * @param formControlName 
   * @param dateValue 
   */
  patchDateChange = (formControlName: string, dateValue: Date) => {

    for (const serviceItem of this.servicesFormArr.controls)
      for (const positionItem of (serviceItem.get('positions') as FormArray).controls) {

        positionItem.get(formControlName).patchValue(dateValue, { emitEvent: false });

        positionItem.get("isPositionEffortChanged").patchValue(true, { emitEvent: false });

        for (const nmpItem of (positionItem.get('nmpData') as FormArray).controls) {

          nmpItem.get(formControlName).patchValue(dateValue, { emitEvent: false });

          nmpItem.get("isPositionEffortChanged").patchValue(true, { emitEvent: false });

        }
        
      }

  }

  /**
   * @description Initializes all thq quote master data
   */
  initializeMasterData = () => {

    this.toggleSpinner(true);

    Promise.all([
      this.getNationalityList(),
      this.getWorkLocation(),
      this.getCurrencyList(),
      this.getPositionList(),
      this.getWorkExperience(),
      this.getTypeOfBusiness(),
      this.getPositionStatusList(),
      this.quoteApproveStatus(),
      this.getUOM(),
      this.getDiscountDetails(),
      this.getTaxDetails(),
      this.getDivisionList(),
      this.getSubDivisionList(),
      this.getEntity(),
      this.getOrgMappingList(),
      this.getOppMetaDetails(),
      this.getGeographicalRegion(),
      this.checkMilestoneApplicable()
    ]).then(async res => {
      

      this.resolveFieldConfig();

      this.getUserFieldConfig();

      if (this.quoteId) // Edit a existing quote
        await this.getQuoteDetails();
      
      this.getQuoteAccessPrivilege();

      this.getCalendarCombinationConfig(); 

      this.getRevenueRegionCombinationConfig(); 

      this.alloteFieldConfig();

      await this.getNonManpowerList();

      await this.getLicenseList();

      if (this.wholeQuoteData) {
        this.patchQuoteDetails(this.wholeQuoteData);

        // if (this.milestoneApplicable){
        //   this.getMilestoneListQB();
        //   this.patchMilestoneDetailsUsingForm();
        // }

        if (this.wholeQuoteData && typeof this.wholeQuoteData === 'object') {
          const status = this.quoteApproveStatusList.find(status => status.id === this.wholeQuoteData.quote_status);
          this.wholeQuoteData.status_name = status ? status.status_name : null; 
        }
      }

      let updateFromDiscount = this.wholeQuoteData?.change_request_merged?.length &&
        this.wholeQuoteData?.discountAndTax.length &&
        this.quoteForm.get('quoteId').value && this.quoteForm.get('totalOrderValue').value != this.wholeQuoteData?.quote_amount;

      // updateFromDiscount && this.saveQuote();

      if (this.milestoneApplicable){
        this.getMilestoneListQB();
        this.patchMilestoneDetailsUsingForm();
      }

      // await this.getNonManpowerList();

      // await this.getLicenseList();

      // if (this.wholeQuoteData) {
      //   this.patchQuoteDetails(this.wholeQuoteData); 
      //   this.patchMilestoneDetailsUsingForm();
      // }
      

      if (this.wEMappingEnabled)
        this.getWEMappingList();

      if (this.sDMappingEnabled)
        this.getSDMappingList();

      this.valueChangeSubscription.add(this.quoteForm.valueChanges.pipe(debounceTime(100), distinctUntilChanged(), takeUntil(this._onDestroy)).subscribe(val => {

        if (val)
          this.isChangesMade = true;

      }));

      // this.calculateOverallDiscount();

      this.toggleSpinner(false);

    });

    //Onchange End Date will change for all the services and the Positions in it.
    this.quoteForm.get("deliveryEndDate").valueChanges.pipe(distinctUntilChanged(), takeUntil(this._onDestroy)).subscribe((value) => {

      if (!value)
        return;

      const formattedDate = moment(value).toISOString();

      let newDuration = this.getDurationInDays(this.quoteForm.get("deliveryStartDate")?.value, value)

      this.quoteForm.get("quoteDuration").patchValue(newDuration)

      if (this.quoteForm.get("quoteDuration")?.value != this.quoteDuration)

        for (const serviceItem of this.servicesFormArr.controls)

          for (const positionItem of (serviceItem.get("positions") as FormArray).controls) {

            positionItem.get("endDate").patchValue(formattedDate, { emitEvent: false });

            for (const nmpItem of (positionItem.get("nmpData") as FormArray).controls) {

              nmpItem.get("endDate").patchValue(formattedDate, { emitEvent: false });

              this.getPositionQuantity(nmpItem as FormGroup);

            }

            // call the /getQuoteDetails for Unit/Quantity patching
            this.getPositionQuantity(positionItem as FormGroup);

          }

    });

    //Onchange Start Date will change for all the services and the Positions in it.
    this.quoteForm.get("deliveryStartDate").valueChanges.pipe(distinctUntilChanged(), takeUntil(this._onDestroy)).subscribe((value) => {
      if (!value)
        return;

      if (this.isQuoteCreateMode ? this.quoteForm.get("deliveryEndDate").value : this.initial_delivery_end_date)
        this.quoteForm.patchValue({ deliveryEndDate: moment(value).add(this.quoteDuration, 'days') }, { emitEvent: false })
      
      const formattedStartDate = moment(value).toISOString();
      const formattedEndDate = moment(value).add(this.quoteDuration, "days").toISOString();

      // Patch date values for the Service, Position, T&E
      for (const serviceItem of this.servicesFormArr.controls)
        for (const positionItem of (serviceItem.get("positions") as FormArray).controls) {

          positionItem.get("startDate").patchValue(formattedStartDate, { emitEvent: false });
          positionItem.get("endDate").patchValue(formattedEndDate, { emitEvent: false });

          for (const nmpItem of (positionItem.get("nmpData") as FormArray).controls) {

            nmpItem.get("startDate").patchValue(formattedStartDate, { emitEvent: false });
            nmpItem.get("endDate").patchValue(formattedEndDate, { emitEvent: false });

            // this.getPositionQuantity(nmpItem as FormGroup);

          }

          // call the /getQuoteDetails for Unit/Quantity patching
          // this.getPositionQuantity(positionItem as FormGroup);

        }

    });

  }

  /**
 * @description Allocate Calendar ID for the Position based on the config
 */
  checkAndAllocateCalendar(entity?: number, service_type?: number, work_location?: number, resource_type_id?: number) {
    // console.log(this.calendarCombinationConfig, "calendarCombinationConfig");
    // console.log(entity, service_type, work_location, resource_type_id);

    // Find the matching combination
    const match = this.calendarCombinationConfig.find(config => 
        config.entity_id === entity &&
        config.service_type_id === service_type &&
        config.location_id === work_location &&
        config.resource_type_id === resource_type_id
    );

    if (match) {
        return match.calendar_id;
    }

    let newCalendarid = this.calendarId
    return newCalendarid;
}

  /**
 * @description Allocate Revenue Region/ Geographical Region for the Position based on the config
 */
  checkAndAllocateRevenueRegion(sales_region?: number, entity?: number) {
    // console.log(this.revenueRegionMapping, "revenueRegionMapping");
    // console.log(entity, sales_region);

    const match = this.revenueRegionMapping.find(config => 
        config.sales_region_id === sales_region &&
        config.legal_entity_id === entity
    );

    if (match) 
        return match.revenue_region_id;
    
    return null;
}

  /**
   * @description Resolves Field Config
   */
  resolveFieldConfig = () => {

    for (const fieldItem of this.fieldConfig) {

      fieldItem['isActive'] = this.checkFieldShouldbeRestricted(fieldItem['key']);

      if (fieldItem['key'] === 'totalRevenue')
        this.isRevenueActive = fieldItem['isActive'] && this.checkFieldShouldbeRestricted('totalRevenue');

      else if (fieldItem['key'] === 'totalCost')
        this.isCostActive = fieldItem['isActive'] && this.checkFieldShouldbeRestricted('totalCost');

      else if (fieldItem['key'] === 'totalGM')
        this.isGMActive = fieldItem['isActive'] && this.checkFieldShouldbeRestricted('totalGM');

      else if (fieldItem['key'] === 'totalGMPercentage')
        this.isGMPerActive = fieldItem['isActive'] && this.checkFieldShouldbeRestricted('totalGMPercentage');

      if (fieldItem['isActive']) {

        let field = {
          key: fieldItem['key'],
          columnName: fieldItem['columnName'],
          label: fieldItem['label'],
          fieldType: fieldItem['fieldType'],
          isVisible: new FormControl(fieldItem['isVisible'] || false),
          col: fieldItem['col'],
          isMandatory: this.mandatoryFields.includes(fieldItem['columnName']) || fieldItem['isMandatory'] || false,
          isForManpowerOnly: fieldItem['isForManpowerOnly'],
          fieldPosition: fieldItem['position'],
          decimalPart: fieldItem['decimalPart'] || 0,
          enableVariableName: fieldItem['enableVariableName'],
          isMasked: this.checkIfFieldShouldBeMasked(fieldItem) 
        };

        if (field['fieldType'] == 'dropdown'){
          switch (field['key']) {

            case 'experience':
              
              field['masterData'] = this.experienceList;
              field['has_project_integrated'] = this.isWorkExperienceEnabled;

              break;

            case 'workLocation':
              
              field['masterData'] = this.workLocationList;
              field['has_project_integrated'] = this.isWorkLocationEnabled;
              
              break;

            case 'nationality':
              
              field['masterData'] = this.nationalityList;
              field['has_project_integrated'] = this.isNationalityEnabled;
              
              break;

            case 'revenueRegion':
              
              field['masterData'] = this.geographicalRegionList;
              field['has_project_integrated'] = this.isRevenueRegionEnabled;

              
              break;

            case 'entity':
              
              field['masterData'] = this.entityList;
              
              break;

            case 'division':
              
              field['masterData'] = this.divisionList;
              field['isMasterDataFromPosition'] = true;
              
              break;

            case 'subDivision':
              
              field['masterData'] = this.subDivisionList;
              field['isMasterDataFromPosition'] = true;
              
              break;

            case 'unit':
              
              field['masterData'] = this.unitList;
              field['has_project_integrated'] = this.isUnitEnabled;
              
              break;
            
            case 'typeOfBusiness':
              
              field['masterData'] = this.businessTypeList;
              field['has_project_integrated'] = this.isTypeOfBusinessEnabled;

              break;
            
            case 'positionStatus':
              
              field['masterData'] = this.positionStatusList;
              field['has_project_integrated'] = this.isPositionStatusEnabled;
              
              break;
          

            case 'milestone':

              // if (this.milestoneApplicable) {
                field['masterData'] = this.milestoneList;
                field['has_project_integrated'] = this.isMilestoneEnabled;
              // } else{
              //   continue;
              // }

              break;

            default:

              field['masterData'] = [];

              break;

          }
        }

        if (field['fieldType'] == 'number'){
            switch (field['key']) {
              case 'quantity':
                field['has_project_integrated'] = this.isQuantityEnabled;
                break;
          
              case 'noOfResources':
                field['has_project_integrated'] = this.isNoOfResourcesEnabled;
                break;
          
              case 'ratePerUnit':
                field['has_project_integrated'] = this.isRatePerUnitEnabled;
                break;
          
              case 'costPerUnit':
                field['has_project_integrated'] = this.isCostPerUnitEnabled;
                break;
          
              default:
                break;
            }
        }

        
        if (field['key'] != 'positionId')
          this.customizeFields.push(field);

        else
          this.positionField = {
            label: field['label'],
            col: field['col']
          };


      }

      if (!this.milestoneApplicable)
        this.customizeFields = this.customizeFields.filter(field => field.key != 'milestone');
      else
        this.customizeFields = this.customizeFields.filter(field => field.key != 'positionStatus');

      console.log(this.customizeFields, 'All field Configuration');
    }

    this.sortFieldConfig();

  }

  /**
   * @description Masks field if the conditions not met
   * @condition Role ID to be configured, Field should be configured.
   */
  checkIfFieldShouldBeMasked(fieldItem: any): boolean {
    const fieldConfig = this.masking_configuration?.masking_field_config.find(item => item.key === fieldItem.key);
    let currentUserRole = this._ticket.getCurrentUserRole();
    console.log(currentUserRole, 'role')

    if (this.masking_configuration?.maskingEnabled && fieldConfig && !fieldConfig?.role_id.includes(currentUserRole)) {
      return true;
    }
    return false;
  }

  formatNumberByCurrency(value: number, currency: string): string {
    if (currency === 'INR') {
      return new Intl.NumberFormat('en-IN', { maximumFractionDigits: 0 }).format(value);
    } else {
      return new Intl.NumberFormat('en-US', { maximumFractionDigits: 0 }).format(value);
    }
  }


  /**
 * @description Determines if a field should be masked based on key and resource_type.
 * @param key - The field key to check.
 * @param resourceType - The resource type (1, 2, 3, FOOTER - For Sum Up Values).
 * @returns True if the field should be masked, otherwise false.
 */
  checkFieldShouldbeMasked(key: string, resourceType: any): boolean {
    const currentUserRole = this._ticket.getCurrentUserRole();

    const fieldConfig = this.masking_configuration?.masking_field_config.find(item =>
      item.resource_type === resourceType && item.key.includes(key)
    );

    if (this.masking_configuration?.maskingEnabled && fieldConfig && 
        fieldConfig.role_id.includes(currentUserRole)
    ) {
      return true;
    }

    return false;
  }

    /**
  * @description Determines if a field should be restricted based on key and resource_type.
  * @param key - The field key to check.
  * @returns True if the field should be restricted, otherwise false.
  */
    checkFieldShouldbeRestricted(fieldKey: string): boolean {

      const currentUserRole = this._ticket.getCurrentUserRole();
  
      const field = this.fieldConfig.find(item => item.key === fieldKey);
  
      if (!field?.isActive) return false;
  
      return !(field?.role_access_restriction && field.role_access_restriction.includes(currentUserRole));
  
    }

  /**
   * @description Sorts Field Config based on Field Position
   */
  sortFieldConfig = () => {

    this.customizeFields.sort((a, b) => {

      if (a.fieldPosition != null && b.fieldPosition != null) {

        if (a.fieldPosition < b.fieldPosition)
          return -1;

        else if (a.fieldPosition > b.fieldPosition)
          return 1;

      }

      else
        return 0;

    });

  }

  /**
   * @description Toggles the spinner
   * @param {boolean} show 
   */
  toggleSpinner = (show = false) => {

    show ? this.spinner.show() : this.spinner.hide();

    this.isDataLoading = show;

  }

  /**
   * @description Gets the Quote details for a quote
   */
  getQuoteDetails = () => {

    return new Promise((resolve, reject) => {

      this._quoteMainService.getQuoteDetails(this.quoteId)
        .pipe(takeUntil(this._onDestroy))
        .subscribe(res => {
          if(res && res["is_rds_peak"]){
            this._toaster.showError("Error", res['messText']|| "System unavailable Please Try Again Later", 3000);
            resolve(true)
          }
          if (res && res['messType'] == "S" && res['data']){
            this.initial_delivery_start_date = res['data']['delivery_start_date']
            this.initial_delivery_end_date = res['data']['delivery_end_date']
            this.quoteDuration=this.getDurationInDays(this.initial_delivery_start_date,this.initial_delivery_end_date)
            this.isQuoteActive = res['data']['flag'] === 1
            this.isChangeRequestActive = res['data']['change_request_flag'] === 1
            this.wholeQuoteData = res['data'];
            let btnList = [...this.addButtonsList]
            this.addButtonsList = [];
            for (const btn of btnList) {
              const notAllowedTypes = btn['notApplicableForQuoteType'] || [];
              const currentQuoteType = this.wholeQuoteData?.quote_type || this.quoteForm.get('quoteType').value;
              const isAllowedForCurrentQuote = !notAllowedTypes.includes(currentQuoteType);
              if (btn['isActive'] && isAllowedForCurrentQuote)
                this.addButtonsList.push(btn);
            }

            if (this.sDMappingEnabled && this.addButtonsList.length) // Removing Section Option When sDMapping is Enabled
              this.addButtonsList = this.addButtonsList.filter(val => val['id'] != 1);

            // this.patchQuoteDetails(res['data']);
          }

          else {

            this._toaster.showError("Error", "Error in getting Quote details", this.opportunityService.longInterval);

            this.navigateToLandingPage();

          }

          resolve(true);

        },
          err => {
            console.log(err);
            this._toaster.showError("Error", "Error in getting Quote details", this.opportunityService.longInterval);
            this.navigateToLandingPage();
            reject(err);
          });

    });

  }

  /**
   * @description Patches the existing quote details to the quote form
   * @param {Object} quoteDetails 
   */
  patchQuoteDetails = (quoteDetails) => {

    this.isEditMode = false;

    this.quoteForm.patchValue({
      quoteId: quoteDetails['quote_header_id'],
      quoteName: quoteDetails['quote_name'],
      quoteCurrency: quoteDetails['quote_currency'], 
      quoteCurrencyId: quoteDetails['quote_currency_id'], 
      initialCurrencyId: quoteDetails['quote_currency_id'], 
      deliveryStartDate: quoteDetails['delivery_start_date'],
      deliveryEndDate: quoteDetails['delivery_end_date'],
      totalRevenue: quoteDetails['quote_revenue_amount'],
      totalCost: quoteDetails['quote_cost_amount'],
      totalGM: (quoteDetails['quote_revenue_amount'] || 0) - (quoteDetails['quote_cost_amount'] || 0),
      totalOrderValue: quoteDetails['quote_amount'],
      version: quoteDetails['version'],
      activeQuote: quoteDetails['flag'] === 1,
      quoteOpportunityHasParent:  quoteDetails['has_parent_opportunity'] === 1,
      enableMilestoneTagging: quoteDetails['has_milestone_tagging '] === 1,
      serviceOppTypeId: quoteDetails['service_type'],
      quoteType: quoteDetails['quote_type'],
      activeChangeRequest: quoteDetails['change_request_flag'] === 1,
      allowNegativeNumber: quoteDetails['allow_negative_number'] === 1,
      quoteDuration: this.quoteDuration
    });

    this.calculateOverallGMPercentage(this.quoteForm);

    const servicesList = quoteDetails['service'];

    if (servicesList.length) {

      this.servicesFormArr.clear();

      const discountFormArr = this.quoteForm.get('discounts') as FormArray;
      const taxFormArr = this.quoteForm.get('taxes') as FormArray;

      discountFormArr.clear();
      taxFormArr.clear();
      
      servicesList.forEach((serviceItem: any, i) => {

        let servicesFormGroup = this.getServicesFormGroup();

        servicesFormGroup.patchValue(
          {
            quoteServiceId: serviceItem['quote_service_id'],
            serviceId: serviceItem['service_header_id'],
            serviceName: serviceItem['quote_service_name'],
            serviceTypeId: serviceItem['service_type_id'],
            serviceRevenue: serviceItem['service_revenue_amount'],
            serviceCost: serviceItem['service_cost_amount'],
            isFixedRate: serviceItem['is_fixed_rate'],
            isSection: serviceItem['service_header_id'] == null,
            isDeleteServiceEnabled: this.isDeleteServiceEnabled,
          },
          {
            emitEvent: false
          }
        );



        let positionFormArr = servicesFormGroup.get('positions') as FormArray;

        positionFormArr.clear();

        if (serviceItem['position'].length == 0) {

          const positionFormGroup = this.getPositionFormGroup();

          this.resolveSDMapping(servicesFormGroup, positionFormGroup);

          positionFormArr.push(positionFormGroup);

        }

        else
          for (const [positionIndex, positionItem] of serviceItem['position'].entries()) {

            let positionFormGroup = this.getPositionFormGroup(positionItem['resource_type_id'] === 2, positionItem['resource_type_id'] === 3);

            if (positionItem['position_items'] && positionItem['position_items'].length) {

              let nmpFormArr = positionFormGroup.get('nmpData') as FormArray;

              for (const [mnpIndex, nmpItem] of positionItem['position_items'].entries()) {

                let nmpFormGroup = this.getPositionFormGroup(true);

                const nmpUnitConfig = this.unitList.find(val => val['id'] === nmpItem['unit_id']);

                nmpFormGroup.patchValue({
                  quotePositionId: nmpItem['qp_item_id'],
                  positionId: nmpItem['nmp_id'],
                  positionName: nmpItem['description'],
                  noOfResources: nmpItem['resource'] || 0,
                  ratePerUnit: nmpItem['rate_per_unit'] || 0,
                  originalRatePerUnit: nmpItem['original_rate'] || 0,
                  costPerUnit: nmpItem['cost_per_unit'] || 0,
                  unit: nmpItem['unit_id'],
                  revenueRegion: nmpItem['revenue_region'],
                  typeOfBusiness: nmpItem['business_type'] || this.quoteForm?.get('quoteBusinessType').value || this.wholeQuoteData?.business_type,
                  positionStatus: nmpItem['position_status'],
                  milestone: nmpItem['milestone_id'],
                  milestoneDetails: nmpItem['milestone'] || [],
                  lastUnit: nmpItem['unit_id'],
                  entity: nmpItem['entity'],
                  division: nmpItem['division'],
                  subDivision: nmpItem['sub_division'],
                  workLocation: nmpItem['work_location'],
                  nationality: nmpItem['nationality'],
                  quantity: nmpItem['quantity'] || 0,
                  totalCost: nmpItem['qpi_amount'],
                  totalRevenue: nmpItem['qpi_revenue_amount'],
                  totalGM: (nmpItem['qpi_revenue_amount'] || 0) - (nmpItem['qpi_amount'] || 0),
                  startDate: nmpItem['start_date'],
                  endDate: nmpItem['end_date'],
                  positionIndex: mnpIndex,
                  defaultDebounce: nmpUnitConfig && nmpUnitConfig['is_value_fixed'] ? 500 : 100,
                  rcValue: nmpItem['rc_value'] || 0,
                  isPositionFieldEnabled : this.isPositionFieldEnabled,
                  isUnitEnabled : this.isUnitEnabled,
                  isWorkLocationEnabled : this.isWorkLocationEnabled,
                  isQuantityEnabled : this.isQuantityEnabled,
                  isNoOfResourcesEnabled : this.isNoOfResourcesEnabled,
                  isRatePerUnitEnabled : this.isRatePerUnitEnabled,
                  isCostPerUnitEnabled : this.isCostPerUnitEnabled,
                  isAddServiceEnabled : this.isAddServiceEnabled,
                  isDeleteServiceEnabled : this.isDeleteServiceEnabled,
                  isAddPositionEnabled : this.isAddPositionEnabled,
                  isAddPositionInlineEnabled : this.isAddPositionInlineEnabled,
                  isDeletePositionEnabled : this.isDeletePositionEnabled,
                  isClonePositionEnabled : this.isClonePositionEnabled,
                  isCustomiseSlotEnabled : this.isCustomiseSlotEnabled,
                  interServicePositionDragNDrop: this.interServicePositionDragNDrop,
                  isTypeOfBusinessEnabled: this.isTypeOfBusinessEnabled,
                  isRevenueRegionEnabled: this.isRevenueRegionEnabled,
                  isPositionStatusEnabled: this.isPositionStatusEnabled,
                  isMilestoneEnabled: this.isMilestoneEnabled,
                  isNationalityEnabled : this.isNationalityEnabled,
                  isWorkExperienceEnabled : this.isWorkExperienceEnabled,
                  isAddSectionEnabled : this.isAddSectionEnabled,
                  isPositionStatusToLost : this.isPositionStatusToLost,
                }, { emitEvent: false });

                this.getCurrentPositionData(nmpFormGroup, nmpItem['nmp_id']);

                this.calculateOverallGMPercentage(nmpFormGroup);

                this.resolveOrgMapping('entity', nmpFormGroup.value, nmpFormGroup);
                this.resolveOrgMapping('division', nmpFormGroup.value, nmpFormGroup);

                nmpFormArr.push(nmpFormGroup);

              }

            }

            const unitConfig = this.unitList.concat(this.otherUnitList).find(val => val['id'] === positionItem['unit_id']);

            positionFormGroup.patchValue({
              quotePositionId: positionItem['quote_position_id'],
              positionId: positionItem['position'],
              positionName: positionItem['position_name'],
              noOfResources: positionItem['resource_count'] || 0,
              experience: positionItem['work_experience'],
              workLocation: positionItem['work_location'],
              nationality: positionItem['nationality'],
              entity: positionItem['entity'],
              division: positionItem['division'],
              subDivision: positionItem['sub_division'],
              unit: positionItem['unit_id'],
              revenueRegion: positionItem['revenue_region'],
              typeOfBusiness: positionItem['business_type'] || this.quoteForm?.get('quoteBusinessType').value || this.wholeQuoteData?.business_type,
              positionStatus: positionItem['position_status'],
              milestone: positionItem['milestone_id'],
              milestoneDetails: positionItem['milestone'] || [],
              lastUnit: positionItem['unit_id'],
              quantity: positionItem['quantity'] || 0,
              ratePerUnit: positionItem['rate_per_unit'] || 0,
              originalRatePerUnit: positionItem['original_rate'] || 0,
              costPerUnit: positionItem['cost_per_unit'] || 0,
              totalRevenue: positionItem['rate_revenue_amount'] || 0,
              totalCost: positionItem['rate_cost_amount'] || 0,
              totalGM: (positionItem['rate_revenue_amount'] || 0) - (positionItem['rate_cost_amount'] || 0),
              positionIndex: positionIndex,
              startDate: positionItem['start_date'],
              endDate: positionItem['end_date'],
              defaultDebounce: unitConfig && unitConfig['is_value_fixed'] ? 500 : 100,
              rcValue: positionItem['rc_value'] || 0,
              isPositionFieldEnabled : this.isPositionFieldEnabled,
              isUnitEnabled : this.isUnitEnabled,
              isWorkLocationEnabled : this.isWorkLocationEnabled,
              isQuantityEnabled : this.isQuantityEnabled,
              isNoOfResourcesEnabled : this.isNoOfResourcesEnabled,
              isRatePerUnitEnabled : this.isRatePerUnitEnabled,
              isCostPerUnitEnabled : this.isCostPerUnitEnabled,
              isAddServiceEnabled : this.isAddServiceEnabled,
              isDeleteServiceEnabled : this.isDeleteServiceEnabled,
              isAddPositionEnabled : this.isAddPositionEnabled,
              isAddPositionInlineEnabled : this.isAddPositionInlineEnabled,
              isDeletePositionEnabled : this.isDeletePositionEnabled,
              isClonePositionEnabled : this.isClonePositionEnabled,
              isCustomiseSlotEnabled : this.isCustomiseSlotEnabled,
              interServicePositionDragNDrop: this.interServicePositionDragNDrop,
              isTypeOfBusinessEnabled : this.isTypeOfBusinessEnabled,
              isRevenueRegionEnabled : this.isRevenueRegionEnabled,
              isPositionStatusEnabled: this.isPositionStatusEnabled,
              isMilestoneEnabled: this.isMilestoneEnabled,
              isNationalityEnabled : this.isNationalityEnabled,
              isWorkExperienceEnabled : this.isWorkExperienceEnabled,
              isAddSectionEnabled : this.isAddSectionEnabled,
              isPositionStatusToLost : this.isPositionStatusToLost
            }, { emitEvent: false });

            this.getCurrentPositionData(positionFormGroup, positionItem['position']);

            this.resolveOrgMapping('entity', positionFormGroup.value, positionFormGroup);
            this.resolveOrgMapping('division', positionFormGroup.value, positionFormGroup);

            this.calculateOverallGMPercentage(positionFormGroup);

            positionFormArr.push(positionFormGroup);

          }

        this.servicesFormArr.push(servicesFormGroup);

        this.addPositionDropZone();
        
      });

      console.log('Milestone Form', this.milestoneFormArray.value);


    }


    if (quoteDetails['discountAndTax'] && quoteDetails['discountAndTax'].length) {

      const discountFormArr = this.quoteForm.get('discounts') as FormArray;
      const taxFormArr = this.quoteForm.get('taxes') as FormArray;

      discountFormArr.clear();
      taxFormArr.clear();

      for (const dtItem of quoteDetails['discountAndTax']) {

        if (dtItem['type'] == 'D') {

          const discountFormGroup = this.getDiscountsFormGroup();

          discountFormGroup.patchValue({
            dtItemId: dtItem['dt_item_id'],
            discountId: dtItem['tax_discount_id'],
            discountName: dtItem['name'],
            discountValue: dtItem['item_amount'],
            isCustom: dtItem['is_custom'] || false
          }, { emitEvent: false });

          discountFormGroup.patchValue({ discountPercentage: dtItem['percentage'] });

          if (dtItem['is_custom'])
            discountFormGroup.get('discountPercentage').enable({ emitEvent: false });

          else
            discountFormGroup.get('discountPercentage').disable({ emitEvent: false });

          discountFormArr.push(discountFormGroup);

        }

        else if (dtItem['type'] == 'T') {

          const taxFormGroup = this.getTaxesFormGroup();

          taxFormGroup.patchValue({
            dtItemId: dtItem['dt_item_id'],
            taxId: dtItem['tax_discount_id'],
            taxName: dtItem['name'],
            taxValue: dtItem['item_amount']
          }, { emitEvent: false });

          taxFormGroup.patchValue({ taxPercentage: dtItem['percentage'] });

          taxFormArr.push(taxFormGroup);

        }

      }

    }

    this.calculateOverallRevenue();

    this.calculateOverallCost();

    setTimeout(() => {

      this.scrollContainer = this.scrollFrame.nativeElement;

    }, 10);

    setTimeout(() => {
    
      this.isChangesMade = false; // to eliminate the change detected in value changes on data refresh
      
    }, 1000);

  }

  /**
 * @description Patches the existing quote's milestone details to the milestone form
 * @param {Object} quoteDetails
 */
  patchMilestoneDetails = (quoteDetails) => {
    const servicesList = quoteDetails['service'];
    let milestoneMap = new Map();

    if (servicesList.length) {
      servicesList.forEach((serviceItem: any) => {
        serviceItem['position'].forEach(positionItem => {
          const milestoneDetails = positionItem['milestone'];

          if (milestoneDetails && milestoneDetails.length > 0) {
            milestoneDetails.forEach(milestone => {
              if (!milestoneMap.has(milestone.milestone_id)) {
                milestoneMap.set(milestone.milestone_id, {
                  milestone: milestone,
                  positions: []
                });
              }
              milestoneMap.get(milestone.milestone_id).positions.push(positionItem);
            });
          }
        });
      });

      this.milestoneFormGroup = this.fb.group({
        allMilestones: this.fb.array([]), 
      });

      let allMilestonesFormArray = this.milestoneFormGroup.get('allMilestones') as FormArray;

      milestoneMap.forEach((milestoneData, milestoneIndex) => {
        let milestoneFormGroup = this.getMilestoneFromGroup();

        milestoneFormGroup.patchValue({
          milestoneId: milestoneData.milestone.milestone_id,
          milestoneName: milestoneData.milestone.milestone_name,
          milestoneStartDate: milestoneData.milestone.milestone_start_date,
          milestoneEndDate: milestoneData.milestone.milestone_end_date,
          milestoneTotalRevenue: milestoneData.milestone.milestone_revenue || 0,
          milestoneTotalCost: milestoneData.milestone.milestone_cost || 0,
          activeQuote: false,
          milestoneIndex: milestoneIndex
        });

        let positionFormArray = milestoneFormGroup.get('positions') as FormArray;

        milestoneData.positions.forEach((positionItem, positionIndex) => {
          let positionFormGroup = this.getPositionFormGroup();

          positionFormGroup.patchValue({
            quotePositionId: positionItem['quote_position_id'],
            positionId: positionItem['position'],
            positionName: positionItem['position_name'],
            noOfResources: positionItem['resource_count'] || 0,
            experience: positionItem['work_experience'],
            workLocation: positionItem['work_location'],
            nationality: positionItem['nationality'],
            entity: positionItem['entity'],
            division: positionItem['division'],
            subDivision: positionItem['sub_division'],
            unit: positionItem['unit_id'],
            typeOfBusiness: positionItem['business_type'],
            milestone: positionItem['milestone_id'],
            milestoneDetails: positionItem['milestone'] || [],
            lastUnit: positionItem['unit_id'],
            quantity: positionItem['quantity'] || 0,
            ratePerUnit: positionItem['rate_per_unit'] || 0,
            originalRatePerUnit: positionItem['original_rate'] || 0,
            costPerUnit: positionItem['cost_per_unit'] || 0,
            totalRevenue: positionItem['rate_revenue_amount'] || 0,
            totalCost: positionItem['rate_cost_amount'] || 0,
            totalGM: (positionItem['rate_revenue_amount'] || 0) - (positionItem['rate_cost_amount'] || 0),
            startDate: positionItem['start_date'],
            positionIndex: positionIndex,
            endDate: positionItem['end_date'],
            defaultDebounce: 500 ,
            rcValue: positionItem['rc_value'] || 0,
            isPositionFieldEnabled : this.isPositionFieldEnabled,
            isUnitEnabled : this.isUnitEnabled,
            isWorkLocationEnabled : this.isWorkLocationEnabled,
            isQuantityEnabled : this.isQuantityEnabled,
            isNoOfResourcesEnabled : this.isNoOfResourcesEnabled,
            isRatePerUnitEnabled : this.isRatePerUnitEnabled,
            isCostPerUnitEnabled : this.isCostPerUnitEnabled,
            isAddServiceEnabled : this.isAddServiceEnabled,
            isDeleteServiceEnabled : this.isDeleteServiceEnabled,
            isAddPositionEnabled : this.isAddPositionEnabled,
            isAddPositionInlineEnabled : this.isAddPositionInlineEnabled,
            isDeletePositionEnabled : this.isDeletePositionEnabled,
            isClonePositionEnabled : this.isClonePositionEnabled,
            isCustomiseSlotEnabled : this.isCustomiseSlotEnabled,
            interServicePositionDragNDrop: this.interServicePositionDragNDrop,
            isTypeOfBusinessEnabled : this.isTypeOfBusinessEnabled,
            isMilestoneEnabled: this.isMilestoneEnabled,
            isNationalityEnabled : this.isNationalityEnabled,
            isWorkExperienceEnabled : this.isWorkExperienceEnabled,
            isAddSectionEnabled : this.isAddSectionEnabled
          }, { emitEvent: false });

          positionFormArray.push(positionFormGroup);
        });

        allMilestonesFormArray.push(milestoneFormGroup);

        this.positionMilestoneDropZones.push(`milestoneDropZone${allMilestonesFormArray.length - 1}`);

        let quoteMilestoneFormArray = this.quoteForm.get('milestoneArray') as FormArray

        quoteMilestoneFormArray.push(milestoneFormGroup)
      });
    }
  } 

  /**
* @description Patches the existing quote's milestone details to the milestone form Using Quote Form
* @param {Object} quoteDetails
*/
  patchMilestoneDetailsUsingForm = () => {
    let milestoneMap = new Map();

    if (this.servicesFormArr.controls) {
      this.servicesFormArr.controls.forEach((serviceItem, serviceIndex) => {
        serviceItem['controls']['positions'].controls.forEach((positionItem, positionIndex) => {
          const milestoneDetails = positionItem['controls']['milestoneDetails']['value'];

          if (milestoneDetails && milestoneDetails.length > 0) {
            milestoneDetails.forEach((milestone) => {
              if (!milestoneMap.has(milestone.milestone_id)) {
                milestoneMap.set(milestone.milestone_id, {
                  milestone: milestone,
                  positions: []
                });
              }
              milestoneMap.get(milestone.milestone_id).positions.push(positionItem);
            });
          }
        });
      });

      this.milestoneFormGroup = this.fb.group({
        allMilestones: this.fb.array([]),
      });

      let allMilestonesFormArray = this.milestoneFormGroup.get('allMilestones') as FormArray;

      milestoneMap.forEach((milestoneData, milestoneIndex) => {
        let milestoneFormGroup = this.getMilestoneFromGroup();

        milestoneFormGroup.patchValue({
          milestoneId: milestoneData.milestone.milestone_id || uuidv4(),
          milestoneName: milestoneData.milestone.milestone_name,
          milestoneStartDate: milestoneData.milestone?.milestone_start_date || milestoneData.milestone?.start_date,
          milestoneEndDate: milestoneData.milestone?.milestone_end_date || milestoneData.milestone?.end_date,
          milestoneTotalRevenue: milestoneData.milestone.milestone_revenue || 0,
          milestoneTotalCost: milestoneData.milestone.milestone_cost || 0,
          activeQuote: false,
          milestoneIndex: milestoneIndex,
          isMasterMilestone: milestoneData.milestone?.is_master || 0
        });

        let positionFormArray = milestoneFormGroup.get('positions') as FormArray;

        milestoneData.positions.forEach((positionItem, positionIndex) => {
          let positionFormGroup = this.getPositionFormGroup();

          positionFormGroup.patchValue({
            quotePositionId: positionItem.get('quotePositionId')?.value,
            positionId: positionItem.get('positionId')?.value,
            positionName: positionItem.get('positionName')?.value,
            noOfResources: positionItem.get('noOfResources')?.value || 0,
            experience: positionItem.get('experience')?.value,
            workLocation: positionItem.get('workLocation')?.value,
            nationality: positionItem.get('nationality')?.value,
            entity: positionItem.get('entity')?.value,
            division: positionItem.get('division')?.value,
            subDivision: positionItem.get('subDivision')?.value,
            unit: positionItem.get('unit')?.value,
            typeOfBusiness: positionItem.get('typeOfBusiness')?.value,
            milestone: positionItem.get('milestone')?.value,
            milestoneDetails: positionItem.get('milestoneDetails')?.value || [],
            lastUnit: positionItem.get('lastUnit')?.value,
            quantity: positionItem.get('quantity')?.value || 0,
            ratePerUnit: positionItem.get('ratePerUnit')?.value || 0,
            costPerUnit: positionItem.get('costPerUnit')?.value || 0,
            totalRevenue: positionItem.get('totalRevenue')?.value || 0,
            totalCost: positionItem.get('totalCost')?.value || 0,
            totalGM: (positionItem.get('totalRevenue')?.value || 0) - (positionItem.get('totalCost')?.value || 0),
            startDate: positionItem.get('startDate')?.value,
            endDate: positionItem.get('endDate')?.value,
            defaultDebounce: 500,
            rcValue: positionItem.get('rcValue')?.value || 0,
            isPositionFieldEnabled: this.isPositionFieldEnabled,
            isUnitEnabled: this.isUnitEnabled,
            isWorkLocationEnabled: this.isWorkLocationEnabled,
            isQuantityEnabled: this.isQuantityEnabled,
            isNoOfResourcesEnabled: this.isNoOfResourcesEnabled,
            isRatePerUnitEnabled: this.isRatePerUnitEnabled,
            isCostPerUnitEnabled: this.isCostPerUnitEnabled,
            isAddServiceEnabled: this.isAddServiceEnabled,
            isDeleteServiceEnabled: this.isDeleteServiceEnabled,
            isAddPositionEnabled: this.isAddPositionEnabled,
            isAddPositionInlineEnabled: this.isAddPositionInlineEnabled,
            isDeletePositionEnabled: this.isDeletePositionEnabled,
            isClonePositionEnabled: this.isClonePositionEnabled,
            isCustomiseSlotEnabled: this.isCustomiseSlotEnabled,
            interServicePositionDragNDrop: this.interServicePositionDragNDrop,
            isTypeOfBusinessEnabled: this.isTypeOfBusinessEnabled,
            isMilestoneEnabled: this.isMilestoneEnabled,
            isNationalityEnabled: this.isNationalityEnabled,
            isWorkExperienceEnabled: this.isWorkExperienceEnabled,
            isAddSectionEnabled: this.isAddSectionEnabled,
            positionIndex: positionIndex
          }, { emitEvent: false });

          positionFormArray.push(positionFormGroup);
        });

        allMilestonesFormArray.push(milestoneFormGroup);

        this.positionMilestoneDropZones.push(`milestoneDropZone${allMilestonesFormArray.length - 1}`);

        // let quoteMilestoneFormArray = this.quoteForm.get('milestoneArray') as FormArray;

        // quoteMilestoneFormArray.push(milestoneFormGroup);
      });
    }
  }

  /**
   * @description Gets the nationality list master data
   */
  getNationalityList = () => {

    return new Promise((resolve, reject) => {

      this.masterDataService.nationality
        .pipe(takeUntil(this._onDestroy))
        .subscribe(res => {

          this.nationalityList = res;

          resolve(true);

        });

    });

  }

    /**
   * @description Gets the nationality list master data
   */
    getGeographicalRegion = () => {

      return new Promise((resolve, reject) => {
  
        this.masterDataService.geographicalRegion
          .pipe(takeUntil(this._onDestroy))
          .subscribe(res => {
  
            this.geographicalRegionList = res;
  
            resolve(true);
  
          });
  
      });
  
    }

  /**
   * @description Gets the Work Location list master data
   */
  getWorkLocation = () => {

    return new Promise((resolve, reject) => {

      this.masterDataService.workLocation
        .pipe(takeUntil(this._onDestroy))
        .subscribe(res => {

          this.workLocationList = res;

          resolve(true);

        });

    });

  }

  /**
 * @description Gets the Work Location list master data
 */
  getCurrencyList = () => {

    return new Promise((resolve, reject) => {

      this.masterDataService.currency
        .pipe(takeUntil(this._onDestroy))
        .subscribe(res => {

          this.currencyList = res;

          resolve(true);

        });

    });

  }

  /**
   * @description Gets the Position list master data
   */
  getPositionList = () => {

    return new Promise((resolve, reject) => {

      this.masterDataService.position
        .pipe(takeUntil(this._onDestroy))
        .subscribe(res => {

          this.positionList = res;

          this.skillList = res.filter(val => val['is_for_quote'] === 1);

          resolve(true);

        });

    });

  }

  /**
  * @description Gets the Skill experience list master data
  */
  getWorkExperience = () => {

    return new Promise((resolve, reject) => {

      this.masterDataService.workExperience
        .pipe(takeUntil(this._onDestroy))
        .subscribe(res => {

          this.experienceList = res;

          resolve(true);

        });

    });

  }

    /**
  * @description Gets the Skill experience list master data
  */
    getTypeOfBusiness = () => {

      return new Promise((resolve, reject) => {
  
        this.masterDataService.typeOfBusiness
          .pipe(takeUntil(this._onDestroy))
          .subscribe(res => {
  
            this.businessTypeList = res;
  
            resolve(true);
  
          });
  
      });
  
    }

  /**
* @description Gets the Skill experience list master data
*/
  getMilestoneList = () => {

    return new Promise((resolve, reject) => {

      this.masterDataService.milestone
        .pipe(takeUntil(this._onDestroy))
        .subscribe(res => {

          this.milestoneList = res;

          resolve(true);

        });

    });

  }

    /**
  * @description Gets the Skill experience list master data
  */
    quoteApproveStatus = () => {

      return new Promise((resolve, reject) => {

        this.masterDataService.quoteApproveStatus
          .pipe(takeUntil(this._onDestroy))
          .subscribe(res => {

            this.quoteApproveStatusList = res;

            resolve(true);

          });

      });

  }

  /**
* @description Gets the Skill experience list master data
*/
  getPositionStatusList = () => {

    return new Promise((resolve, reject) => {

      this.masterDataService.positionStatus
        .pipe(takeUntil(this._onDestroy))
        .subscribe(res => {

          this.positionStatusList = res;

          resolve(true);

        });

    });

  }

  /**
  * @description Gets the Unit Of Measurement list master data
  */
  getUOM = () => {

    return new Promise((resolve, reject) => {

      this.masterDataService.uom
        .pipe(takeUntil(this._onDestroy))
        .subscribe(res => {

          for (const item of res) {
            if(item['applicable_resource_type']){
              let applicable_resource_type=JSON.parse(item['applicable_resource_type'])
            if(applicable_resource_type.includes(2)){
              this.nonManPowerUnit.push(item)
            }

            if(applicable_resource_type.includes(3)){
              this.licenseUnit.push(item)
            }

            if(applicable_resource_type.includes(1)){
              this.manPowerUnit.push(item)
            }
          }
          if(this.service_based_unit){
            if(item['applicable_service_type']){
              let applicable_service_type=JSON.parse(item['applicable_service_type'])
              
              if(this.oppServiceType && !applicable_service_type.includes(this.oppServiceType)){
                this.nonManPowerUnit = this.nonManPowerUnit.filter(unit => unit !== item);
                this.licenseUnit = this.licenseUnit.filter(unit => unit !== item);
                this.manPowerUnit = this.manPowerUnit.filter(unit => unit !== item);
              }
            }
          }
            if (item['is_for_position']){
              this.unitList.push(item);
          }
            else
              this.otherUnitList.push(item);

          }

          resolve(true);

        });

    });

  }

  /**
   * @description Gets the Tax list master data
   */
  getTaxDetails = () => {

    return new Promise((resolve, reject) => {

      this.masterDataService.tax
        .pipe(takeUntil(this._onDestroy))
        .subscribe(res => {

          this.taxesList = res;

          resolve(true);

        });

    });

  }

  /**
  * @description Gets the Discount list master data
  */
  getDiscountDetails = () => {

    return new Promise((resolve, reject) => {

      this.masterDataService.discount
        .pipe(takeUntil(this._onDestroy))
        .subscribe(res => {

          this.discountsList = res;

          resolve(true);

        });

    });

  }

  /**
  * @description Gets the Non-manpower list master data
  */
  getNonManpowerList = () => {

    return new Promise((resolve, reject) => {

      this._quoteMainService.getNonManPowerList(this.opportunityId, this.defaultCurrency, this.quoteForm.get('quoteCurrency').value || this.wholeQuoteData?.quote_currency, this.conversionTypeId)
        .pipe(takeUntil(this._onDestroy))
        .subscribe(res => {

          if (res["messType"] == "S" && res["data"] && res["data"].length)
            this.nmpList = res["data"];

          resolve(true);

        });

    });

  }

  /**
   * @description Gets the Division list master data
   */
  getDivisionList = () => {

    return new Promise((resolve, reject) => {

      this.masterDataService.division
        .pipe(takeUntil(this._onDestroy))
        .subscribe(res => {

          this.divisionList = res;

          resolve(true);

        },
          err => {
            console.log(err);
            reject(err);
          });

    });

  }

  /**
   * @description Gets the Sub Division list master data
   */
  getSubDivisionList = () => {

    return new Promise((resolve, reject) => {

      this.masterDataService.subDivision
        .pipe(takeUntil(this._onDestroy))
        .subscribe(res => {

          this.subDivisionList = res;

          resolve(true);

        });

    });

  }

  /**
   * @description Gets the Entity list master data
   */
  getEntity = () => {

    return new Promise((resolve, reject) => {

      this.masterDataService.entity
        .pipe(takeUntil(this._onDestroy))
        .subscribe(res => {

          this.entityList = res;

          resolve(true);

        });

    });

  }

   /**
   * @description Gets the License list master data
   */
   getLicenseList = () => {

    return new Promise((resolve, reject) => {

      this._quoteMainService.getLicenseList(this.opportunityId, this.defaultCurrency, this.quoteForm.get('quoteCurrency').value || this.wholeQuoteData?.quote_currency, this.conversionTypeId)
        .pipe(takeUntil(this._onDestroy))
        .subscribe(res => {

          if (res["messType"] == "S" && res["data"] && res["data"].length)
            this.licenseList = res["data"];

          resolve(true);

        });

    });

  }

  /**
   * @description Gets the Org Mapping list master data
   */
  getOrgMappingList = () => {

    return new Promise((resolve, reject) => {

      this.masterDataService.orgMapping
      .pipe(takeUntil(this._onDestroy))
        .subscribe(res => {

          this.orgMappingList = res;

          resolve(true);

        });

    });

  }

  /**
  * @description Gets the Work location Entity Mapping list master data
  */
  getWEMappingList = () => {

    return new Promise((resolve, reject) => {

      this.masterDataService.wEMapping
        .pipe(takeUntil(this._onDestroy))
        .subscribe(res => {

          this.wEMappingList = res;

          resolve(true);

        });

    });

  }

  /**
  * @description Gets the Service Division Mapping list master data
  */
  getSDMappingList = () => {

    return new Promise((resolve, reject) => {

      this.masterDataService.sDMapping
        .pipe(takeUntil(this._onDestroy))
        .subscribe(res => {

          this.sDMappingList = res;

          resolve(true);

        });

    });

  }

  /**
   * @description Initializes the quote Form
   */
  initializeForm = () => {

    this.quoteForm = this.fb.group({
      quoteId: [null],
      quoteName: [null, Validators.required],
      quoteCurrency: [null, Validators.required],
      quoteCurrencyId: [null],
      initialCurrencyId: [null],
      deliveryStartDate: [null],
      deliveryEndDate: [null],
      quoteOpportunityHasParent: [null],
      services: this.fb.array([]),
      totalRevenue: [0],
      totalCost: [0],
      totalGM: [0],
      totalGMPercentage: [0],
      totalOrderValue: [0],
      discounts: this.fb.array([]),
      totalDiscount: [0],
      taxes: this.fb.array([]),
      totalTax: [0],
      version: [1],
      activeQuote: [false],
      quoteBusinessType: [null],
      enableMilestoneTagging: [false],
      serviceOppTypeId: [false],
      milestoneArray:this.fb.array([]),
      activeChangeRequest: [false],
      quoteType: [null],
      allowNegativeNumber: [false],
      manpowerRevenue: [0],
      manpowerCost: [0],
      nonManpowerRevenue: [0],
      nonManpowerCost: [0],
      licenseRevenue: [0],
      licenseCost: [0],
      quoteDuration:[0]
    });

    this.milestoneFormArray = this.fb.array([]);

  }
  
  get servicesFormArr() {
    return this.quoteForm.get("services") as FormArray;
  }

  /**
   * @description Returns the services form group
   * @returns FormGroup
   */
  getServicesFormGroup = (): FormGroup => {

    const servicesFormGroup = this.fb.group({
      quoteServiceId: [null],
      serviceId: [null],
      serviceName: [null, Validators.required],
      serviceCost: [null],
      serviceRevenue: [null],
      serviceTypeId: [null],
      isFixedRate: [null],
      positions: this.fb.array([]),
      isSection: [false],
      isDeleteServiceEnabled: [false]
    });

    return servicesFormGroup;

  }

  /**
 * @description Returns the services form array of milestone
 * @returns FormGroup
 */
  get milestoneFormArrayMain() {
    return this.milestoneFormGroup.get("allMilestones") as FormArray;
  }

  /** 
   * @description Returns the position form group
   * @returns FormGroup
   */
  getPositionFormGroup = (isNonManpower = false, isLicense = false): FormGroup => {

    const validator = (isNonManpower || isLicense) ? Validators.nullValidator : Validators.required;
    let businessType = this.quoteForm?.get('quoteBusinessType').value || this.wholeQuoteData?.business_type
    const positionFormGroup = this.fb.group({
      quotePositionId: [null],
      positionId: [null, Validators.required],
      positionName: [null],
      noOfResources: [1, this.mandatoryFields.includes(this.resolveFieldMapping('noOfResources')) ? validator : Validators.nullValidator],
      experience: [null, this.mandatoryFields.includes(this.resolveFieldMapping('experience')) ? validator : Validators.nullValidator],
      workLocation: [null, this.mandatoryFields.includes(this.resolveFieldMapping('workLocation')) ? validator : Validators.nullValidator],
      nationality: [null, this.mandatoryFields.includes(this.resolveFieldMapping('nationality')) ? validator : Validators.nullValidator],
      division: [null, this.mandatoryFields.includes(this.resolveFieldMapping('division')) ? validator : Validators.nullValidator],
      subDivision: [null, this.mandatoryFields.includes(this.resolveFieldMapping('subDivision')) ? validator : Validators.nullValidator],
      entity: [null, this.mandatoryFields.includes(this.resolveFieldMapping('entity')) ? validator : Validators.nullValidator],
      unit: [null, Validators.required],
      typeOfBusiness: [null, this.mandatoryFields.includes(this.resolveFieldMapping('typeOfBusiness')) ? validator : Validators.nullValidator],
      revenueRegion: [null, this.mandatoryFields.includes(this.resolveFieldMapping('revenueRegion')) ? validator : Validators.nullValidator],
      positionStatus: [null, this.mandatoryFields.includes(this.resolveFieldMapping('positionStatus')) ? validator : Validators.nullValidator],
      milestone: [null, this.mandatoryFields.includes(this.resolveFieldMapping('milestone')) ? validator : Validators.nullValidator],
      milestoneDetails: [null],
      lastUnit: [null],
      ratePerUnit: [0, Validators.required],
      originalRatePerUnit: [0],
      costPerUnit: [0],
      quantity: [0, Validators.required],
      isQuantityChanged: [false],
      totalRevenue: [0],
      totalCost: [0],
      totalGM: [{ value: 0, disabled: true }],
      totalGMPercentage: [{ value: 0, disabled: true }],
      isNonManpower: [isNonManpower],
      isLicense: [isLicense],
      startDate: [this.quoteForm.get('deliveryStartDate').value],
      endDate: [this.quoteForm.get('deliveryEndDate').value],
      nmpData: this.fb.array([]),
      isUnitChanged: [false],
      isPositionEffortChanged: [false],
      isPositionValueChanged: [false],
      defaultDebounce: [100],
      divisionMasterData: [this.divisionList],
      subDivisionMasterData: [this.subDivisionList],
      rcValue: [0],
      isPositionFieldEnabled : [false],
      isUnitEnabled : [false],
      isWorkLocationEnabled : [false],
      isQuantityEnabled : [false],
      isNoOfResourcesEnabled : [false],
      isRatePerUnitEnabled : [false],
      isCostPerUnitEnabled : [false],
      isAddServiceEnabled : [false],
      isDeleteServiceEnabled : [false],
      isAddPositionEnabled : [false],
      isAddPositionInlineEnabled : [false],
      isDeletePositionEnabled : [false],
      isClonePositionEnabled : [false],
      isCustomiseSlotEnabled : [false],
      interServicePositionDragNDrop: [false],
      isTypeOfBusinessEnabled : [false],
      isRevenueRegionEnabled : [false],
      isPositionStatusEnabled : [false],
      isNationalityEnabled : [false],
      isWorkExperienceEnabled : [false],
      isAddSectionEnabled : [false],
      isPositionStatusToLost : [false],
      positionDuplicationLoader: [false],
      calendarId: [null],
      isMilestoneEnabled : [false],
      positionIndex: [null]
    });

    this.valueChangeSubscription.add(positionFormGroup.get('positionId').valueChanges.pipe(debounceTime(10), takeUntil(this._onDestroy)).subscribe(res => {

      if (res != null) {

        const currentPositionData = this.getCurrentPositionData(positionFormGroup, res);

        if (currentPositionData) {

          positionFormGroup.get('positionName').patchValue(currentPositionData['name']);

          if (positionFormGroup.get('isNonManpower').value || positionFormGroup.get('isLicense').value) {

            const patchData = {
              entity: currentPositionData['entity'],
              division: currentPositionData['division'],
              subDivision: currentPositionData['sub_division'],
              experience: currentPositionData['work_experience'],
              workLocation: currentPositionData['work_location'],
              nationality: currentPositionData['nationality']
            };

            if (this.wEMappingEnabled)
              delete patchData['entity'];

            if (this.sDMappingEnabled)
              delete patchData['division'];

            positionFormGroup.patchValue(patchData, { emitEvent: false });
  
            positionFormGroup.patchValue({
              ratePerUnit: currentPositionData['revenue'],
              costPerUnit: currentPositionData['cost'],
              rcValue: currentPositionData['revenue']
            });

          }

          else // Manpower - fetch rate card
            this.getPositionRate(positionFormGroup);

      }

      }

      else{
        positionFormGroup.get('positionName').patchValue(null);
        positionFormGroup.get('typeOfBusiness').patchValue(businessType || null)
      }

    }));

    this.valueChangeSubscription.add(positionFormGroup.get('noOfResources').valueChanges.pipe(distinctUntilChanged(), debounceTime(100), takeUntil(this._onDestroy)).subscribe(res => {

      if (res < 0 || res === "" || res === null)
        return positionFormGroup.get('noOfResources').patchValue(0);

      this.calculatePositionRevenue(positionFormGroup);

      this.calculatePositionCost(positionFormGroup);

    }));

    this.valueChangeSubscription.add(positionFormGroup.get('unit').valueChanges.pipe(debounceTime(100), takeUntil(this._onDestroy)).subscribe(res => {

      if (res && res != positionFormGroup.get('lastUnit').value) {

        if (positionFormGroup.get('lastUnit').value != null && positionFormGroup.get('quantity').value > 0)
          this._util.openConfirmationSweetAlertWithCustom("Do you want to change Unit ?", "Doing this will reset the quantity")
            .then((resetConfirm) => {

              if (resetConfirm) {

                positionFormGroup.patchValue({
                  quantity: 1,
                  lastUnit: res,
                  isUnitChanged: true
                });

                this.getPositionQuantity(positionFormGroup);

              }

              else
                positionFormGroup.patchValue({
                  unit: positionFormGroup.get('lastUnit').value,
                  isUnitChanged: false
                }, { emitEvent: false });

            });

        else {

          positionFormGroup.patchValue({
            lastUnit: res
          });

          this.getPositionQuantity(positionFormGroup);

        }

      }

      const unitConfig = this.unitList.find(val => val['id'] === res);

      if (unitConfig && unitConfig['is_value_fixed'])
        positionFormGroup.get('defaultDebounce').patchValue(500);

      else
        positionFormGroup.get('defaultDebounce').patchValue(100);

    }));

    this.valueChangeSubscription.add(positionFormGroup.get('milestone').valueChanges.pipe(debounceTime(100), takeUntil(this._onDestroy)).subscribe(res => {

      if (res) {
        const matchedMilestone = this.milestoneList.find(milestone => milestone.id === res);

        if (matchedMilestone) {
          positionFormGroup.get('milestoneDetails')?.patchValue([matchedMilestone], { emitEvent: false });
        }
        this.patchMilestoneDetailsUsingForm();
      } 

      else 
        console.log("Milestone is not selected or invalid.");
      
    }

    ));

    this.valueChangeSubscription.add(positionFormGroup.get('ratePerUnit').valueChanges.pipe(distinctUntilChanged(), debounceTime(500), takeUntil(this._onDestroy)).subscribe(res => {

      if (!this.quoteForm.get('allowNegativeNumber').value &&
        (res < 0 || res === "" || res === null))
        return positionFormGroup.get('ratePerUnit').patchValue(0);

      // if (res != positionFormGroup.get('originalRatePerUnit').value) {

        //   if (!positionFormGroup.contains('originalRateShouldbeUpdated'))
        //     positionFormGroup.addControl('originalRateShouldbeUpdated', this.fb.control(false));
        //   else positionFormGroup.get('originalRateShouldbeUpdated').patchValue(false);

        res = res.toString().replace(/,/g, '');
        positionFormGroup.get('originalRatePerUnit').patchValue(Number(res));

        let discountFormArr = this.quoteForm.get('discounts') as FormArray;

        if (discountFormArr.value.length) {
          this._toaster.showWarning('Discount for the quote is indentified!', 'The entered rate will be altered according to the applied discount', 4500)
          setTimeout(() => {
            this._toaster.showWarning(
              'Discount for the quote is identified!',
              'The entered rate will be altered according to the applied discount',
              4500
            );

            this.calculateTotalDiscount();
            this.calculatePositionRevenue(positionFormGroup);
          }, 1500);
          return;
        }

      // }

      this.calculatePositionRevenue(positionFormGroup);

    }));

    this.valueChangeSubscription.add(positionFormGroup.get('costPerUnit').valueChanges.pipe(distinctUntilChanged(), debounceTime(100), takeUntil(this._onDestroy)).subscribe(res => {

      if (res < 0 || res === "" || res === null)
        return positionFormGroup.get('costPerUnit').patchValue(0);

      this.calculatePositionCost(positionFormGroup);

    }));

    this.valueChangeSubscription.add(positionFormGroup.get('quantity').valueChanges.pipe(distinctUntilChanged(), debounce(() => timer(positionFormGroup.get('defaultDebounce').value)), takeUntil(this._onDestroy)).subscribe(res => {

      if (!this.quoteForm.get('allowNegativeNumber').value && 
      (res < 0 || res === "" || res === null))
        return positionFormGroup.get('quantity').patchValue(0);

      positionFormGroup.get('isQuantityChanged').patchValue(true);

      if (positionFormGroup.contains('positionEffortData'))
        positionFormGroup.get('positionEffortData').patchValue(null);

      const unitConfig = this.unitList.concat(this.otherUnitList).find(val => val['id'] === positionFormGroup.get('unit').value);

      if (unitConfig && unitConfig['is_value_fixed']) {

        const unitValue = unitConfig['unit_value_in_hrs'];

        if (typeof res == 'string')
          res = res.replace(/,/g, '');

        if (/^\d*\.?\d*$/.test(res))
          positionFormGroup.get('quantity').patchValue(Math.ceil(parseFloat(res) / unitValue) * unitValue);

      }

      this.calculatePositionRevenue(positionFormGroup);

      this.calculatePositionCost(positionFormGroup);

    }));
    

    this.valueChangeSubscription.add(positionFormGroup.get('totalRevenue').valueChanges.pipe(debounceTime(100), takeUntil(this._onDestroy)).subscribe(res => {

      positionFormGroup.get('isPositionValueChanged').patchValue(true);

      positionFormGroup.get('totalGM').patchValue(this.convertToFixed(res - positionFormGroup.get('totalCost').value));

      this.calculateOverallGMPercentage(positionFormGroup);

    }));

    this.valueChangeSubscription.add(positionFormGroup.get('totalCost').valueChanges.pipe(debounceTime(100), takeUntil(this._onDestroy)).subscribe(res => {

      positionFormGroup.get('isPositionValueChanged').patchValue(true);

      positionFormGroup.get('totalGM').patchValue(this.convertToFixed(positionFormGroup.get('totalRevenue').value - res));

      this.calculateOverallGMPercentage(positionFormGroup);

    }));

    this.valueChangeSubscription.add(positionFormGroup.get('totalGM').valueChanges.pipe(debounceTime(100), takeUntil(this._onDestroy)).subscribe(res => {

      this.calculateOverallGM();

    }));

    this.valueChangeSubscription.add(positionFormGroup.get('entity').valueChanges.pipe(debounceTime(100), takeUntil(this._onDestroy)).subscribe(res => {

      this.resolveOrgMapping('entity', positionFormGroup.value, positionFormGroup);

      if (this.geographicRevenueRegionMapping) {

        const revenueRegion = this.checkAndAllocateRevenueRegion(this.wholeQuoteData?.sales_region || this.oppQuoteDetails.sales_region, res);

        positionFormGroup.get('revenueRegion')?.patchValue(revenueRegion, { emitEvent: false });

        if (revenueRegion)
          positionFormGroup.get('revenueRegion').disable({ emitEvent: false });
        
        else
          positionFormGroup.get('revenueRegion').enable({ emitEvent: false });
      }

    }));

    this.valueChangeSubscription.add(positionFormGroup.get('division').valueChanges.pipe(debounceTime(100), takeUntil(this._onDestroy)).subscribe(res => {

      this.resolveOrgMapping('division', positionFormGroup.value, positionFormGroup);

    }));
    

    this.valueChangeSubscription.add(positionFormGroup.get('positionStatus').valueChanges.pipe(debounceTime(100), takeUntil(this._onDestroy)).subscribe(async res => {
      const isPositionStatusToLost = positionFormGroup.get('isPositionStatusToLost').value;
      const hasProjectIntegrated = this.projectDetailsForOpportunity?.messType === "S" &&
      this.projectDetailsForOpportunity?.messData?.length > 0;

   // if (res === 4 && hasProjectIntegrated && !isPositionStatusToLost)  {
      //   positionFormGroup.get('positionStatus').setValue(null, { emitEvent: false });
      //   this._toaster.showWarning('Project has been Integrated', 'Cannot make position status to lost!');
      // }
      let restrictedStatuses = this.positionStatusList
      .filter((item: any) => item.allocation_restrict === 1)
      .map((item: any) => item.id); // assuming you have a `status` field
    
    if (res === 4 || restrictedStatuses.includes(res)) {
       let quoteId= this.quoteForm.get('quoteId')?.value;
       if(quoteId){
       this.allocation_details =await this._quoteMainService.checkQuoteAllocation(quoteId)
      //  if()
       await this.calculateOverallRevenue(positionFormGroup);
       }

      }
      else{
        this.calculateOverallRevenue();
      }
      
    }));

    if (this.wEMappingEnabled)
      this.valueChangeSubscription.add(positionFormGroup.get('workLocation').valueChanges.pipe(debounceTime(100), takeUntil(this._onDestroy)).subscribe(res => {

        if (res && this.wEMappingList.length) {

          const currMapping = this.wEMappingList.find(val => val['work_location_id'] === res);

          positionFormGroup.get('entity').patchValue(currMapping ? currMapping['entity_id'] : null, { emitEvent: false });
          
        }

        else
          positionFormGroup.get('entity').patchValue(null, { emitEvent: false });

      }));

    // Subscription for mandatory fields to trigger rate card for manpower resource
    if (!isNonManpower && !isLicense)
      for (const mandatoryField of this.mandatoryFields)
        this.valueChangeSubscription.add(positionFormGroup.get(this.resolveFieldMapping(mandatoryField)).valueChanges.pipe(debounceTime(100), takeUntil(this._onDestroy)).subscribe(res => {

          this.getPositionRate(positionFormGroup);

        }));

      //Subscription for Calendar Combination Config
      const handleValueChange = () => {
        // console.log('Calendar Function Called! Value Change');

        const entity = positionFormGroup.get("entity")?.value || 0;
        const serviceTypeId = this.wholeQuoteData?.opp_service_type_id;
        const workLocation = positionFormGroup.get("workLocation")?.value || 0;
        const resourceTypeId = positionFormGroup.get('isNonManpower')?.value ? 2 : positionFormGroup.get('isLicense')?.value ? 3 : 1;

        const newCalendarId = this.checkAndAllocateCalendar(entity, serviceTypeId, workLocation, resourceTypeId);
        positionFormGroup.get('calendarId')?.patchValue(newCalendarId, { emitEvent: false });

        this.getPositionQuantity(positionFormGroup);
      };

      let calendarCombinationFields = ["workLocation", "entity"];

    calendarCombinationFields.forEach(field => {
      const control = positionFormGroup.get(field);

      if (control) {
        this.valueChangeSubscription.add(
          control.valueChanges.pipe(
            debounceTime(100),
            takeUntil(this._onDestroy)
          ).subscribe(handleValueChange)
        );
      }
    });

    return positionFormGroup;

  }

  
  /**
 * @description Returns the position form group
 * @returns FormGroup
 */
  getMilestoneFromGroup = (isNonManpower = false, isLicense = false): FormGroup => {

    const milestoneFormGroup = this.fb.group({
      milestoneId: [null],
      milestoneName: [null, Validators.required],
      milestoneStartDate: [null],
      milestoneEndDate: [null],
      positions: this.fb.array([]),
      milestoneTotalRevenue: [0],
      milestoneTotalCost: [0],
      activeQuote: [false],
      milestoneTotalGMPercentage: [0],
      isMasterMilestone: [0]
    });

    return milestoneFormGroup;

  }

  /**
 * @description Calculates rate per unit for a position if discount is applied
 * @param positionFormGroup 
 */
  calculatePositionRatePerUnit = (positionFormGroup: FormGroup, isFromDiscount = false) => {

    const noOfResources = positionFormGroup.get('noOfResources').value;
    const ratePerUnit = positionFormGroup.get('ratePerUnit').value;
    const quantity = positionFormGroup.get('quantity').value;
    // const totalRevenue = positionFormGroup.get('quantity').value;

    let totalRevenue = noOfResources * ratePerUnit * quantity;

    // if (!positionFormGroup.get('originalRatePerUnit')) {
    //   const initialRatePerUnit = positionFormGroup.get('ratePerUnit').value;
    //   positionFormGroup.addControl('originalRatePerUnit', this.fb.control(initialRatePerUnit));
    // }

    if (!positionFormGroup.get('originalPositionRevenue')) {
      positionFormGroup.addControl('originalPositionRevenue', this.fb.control(totalRevenue));
    }

    let originalRatePerUnit = positionFormGroup.get('originalRatePerUnit').value;
    let initialRevenue = positionFormGroup.get('originalPositionRevenue').value;

    totalRevenue = initialRevenue; 

    for (const discountItem of (this.quoteForm.get('discounts') as FormArray).controls) {

      // if (!discountItem.get('isEdited')) {
      //   (discountItem as FormGroup).addControl('isEdited', this.fb.control(false));
      // }

      // if (!discountItem.get('isEdited').value) continue;

      const discountPercentage = discountItem.get('discountPercentage').value;

      const positionDiscountValue = this.convertToFixed(((discountPercentage || 0) / 100) * totalRevenue);

      totalRevenue = totalRevenue - positionDiscountValue;

      const discountAmount = this.convertToFixed(((discountPercentage || 0) / 100) * originalRatePerUnit);

      originalRatePerUnit = originalRatePerUnit - discountAmount;

      if (positionFormGroup.contains(`discount${discountItem.get('discountId').value}`)) {

        if (!isFromDiscount)
          discountItem.get('discountValue').patchValue(discountItem.get('discountValue').value - positionFormGroup.get(`discount${discountItem.get('discountId').value}`).value, { emitEvent: false });

        positionFormGroup.get(`discount${discountItem.get('discountId').value}`).patchValue(positionDiscountValue);

      }

      else
        positionFormGroup.addControl(`discount${discountItem.get('discountId').value}`, this.fb.control(positionDiscountValue));
      
      discountItem.get('discountValue').patchValue(this.convertToFixed(discountItem.get('discountValue').value + positionDiscountValue), { emitEvent: false });

      // if (positionFormGroup.contains(`discount${discountItem.get('discountId').value}`)) {
      //   if (!isFromDiscount) {
      //     discountItem.get('discountValue').patchValue(
      //       discountItem.get('discountValue').value - positionFormGroup.get(`discount${discountItem.get('discountId').value}`).value
      //     );
      //   }

      //   positionFormGroup.get(`discount${discountItem.get('discountId').value}`).patchValue(discountAmount);
      // } else {
      //   positionFormGroup.addControl(`discount${discountItem.get('discountId').value}`, this.fb.control(discountAmount));
      // }

      // discountItem.get('discountValue').patchValue(
      //   this.convertToFixed(discountItem.get('discountValue').value + discountAmount)
      // );
    }

    positionFormGroup.get('ratePerUnit').patchValue(originalRatePerUnit, {emitEvent: false});

    this.calculatePositionRevenue(positionFormGroup);

    // if (!positionFormGroup.contains('originalRateShouldbeUpdated'))
    //   positionFormGroup.addControl('originalRateShouldbeUpdated', this.fb.control(false));
    // else positionFormGroup.get('originalRateShouldbeUpdated').patchValue(false);

    this.calculateOverallRevenue();

  }


  setDiscountEdited = (discountItem: FormGroup) => {
    if (!discountItem.get('isEdited')) {
      discountItem.addControl('isEdited', this.fb.control(true));
    } else {
      discountItem.get('isEdited').patchValue(true);
    }
  }

  /**
   * @description Calculates total revenue for a position
   * @param positionFormGroup 
   */
  calculatePositionRevenue = (positionFormGroup: FormGroup, isFromDiscount = false) => {

    const noOfResources = positionFormGroup.get('noOfResources').value;
    const ratePerUnit = positionFormGroup.get('ratePerUnit').value;
    const quantity = positionFormGroup.get('quantity').value;

    let totalRevenue = noOfResources * ratePerUnit * quantity;

    for (const discountItem of (this.quoteForm.get('discounts') as FormArray).controls) {

      const discountPercentage = discountItem.get('discountPercentage').value;

      const positionDiscountValue = this.convertToFixed(((discountPercentage || 0) / 100) * totalRevenue);

      // totalRevenue = totalRevenue - positionDiscountValue;

      // if (positionFormGroup.contains(`discount${discountItem.get('discountId').value}`)) {

      //   if (!isFromDiscount)
      //     discountItem.get('discountValue').patchValue(discountItem.get('discountValue').value - positionFormGroup.get(`discount${discountItem.get('discountId').value}`).value);

      //   positionFormGroup.get(`discount${discountItem.get('discountId').value}`).patchValue(positionDiscountValue);

      // }

      // else
      //   positionFormGroup.addControl(`discount${discountItem.get('discountId').value}`, this.fb.control(positionDiscountValue));
      
      // discountItem.get('discountValue').patchValue(this.convertToFixed(discountItem.get('discountValue').value + positionDiscountValue));

    }
    const valueChanged = positionFormGroup.get('valueChangedFromTotalRevenue')?.value;

    if (!valueChanged) 
      positionFormGroup.get('totalRevenue').patchValue(totalRevenue, { emitEvent: false });
    
    this.calculateOverallRevenue();
    positionFormGroup.get('valueChangedFromTotalRevenue')?.patchValue(false, { emitEvent: false });
  }

  /**
   * @description Calculates the overall revenue for the quote
   */
  calculateOverallRevenue = (positionFormGroup?) => {
    let totalRevenue = 0;
    let nonManpowerRevenue = 0;
    let licenseRevenue = 0;
    let manpowerRevenue = 0;

    const isFullyDiscounted = this.isQuoteFullyDiscounted();
    let allocated_position = this.allocation_details['data'] || []
    let res_msg = this.allocation_details['msg'] || null
    let res_detailed_msg = this.allocation_details['detail_msg'] || null

    for (const serviceItem of this.servicesFormArr.value) {
      for (const positionItem of serviceItem['positions']) {

        const positionStatus = this.positionStatusList.find(status => status.id === positionItem?.positionStatus);
        if ((positionStatus?.exclude_for_revenue && allocated_position?.includes(positionItem['quotePositionId'])) ||
          (allocated_position?.includes(positionItem['quotePositionId']) && positionStatus?.allocation_restrict)) {

          if (positionFormGroup?.get('positionStatus')) {
            positionFormGroup.get('positionStatus')?.setValue(null, { emitEvent: false });
          }

          this._toaster.showWarning(res_msg || 'Position Cannot be changed to lost status', res_detailed_msg || 'Position already have allocation');
          return true;
        }

        if (!positionStatus?.exclude_for_revenue) {
          const category = positionItem['isNonManpower']
            ? 2
            : positionItem['isLicense']
              ? 3
              : 1;

          // Use originalRate if 100% discount
          const rate = isFullyDiscounted ? (positionItem['originalRate'] || 0) : (positionItem['ratePerUnit'] || 0);
          const noOfResources = positionItem['noOfResources'] || 0;
          const quantity = positionItem['quantity'] || 0;
          const calculatedRevenue = noOfResources * rate * quantity;

          // Push calculated revenue
          switch (category) {
            case 2: nonManpowerRevenue += calculatedRevenue; break;
            case 3: licenseRevenue += calculatedRevenue; break;
            case 1:
            default: manpowerRevenue += calculatedRevenue; break;
          }

          for (const nmpItem of positionItem['nmpData']) {
            const nmpRate = isFullyDiscounted ? nmpItem['originalRate'] : nmpItem['ratePerUnit'];
            const nmpNoOfResources = nmpItem['noOfResources'] || 1;
            const nmpQuantity = nmpItem['quantity'] || 1;
            const nmpRevenue = nmpNoOfResources * nmpRate * nmpQuantity;
            nonManpowerRevenue += nmpRevenue;
          }
        }
      }
    }

    totalRevenue = manpowerRevenue + nonManpowerRevenue + licenseRevenue;
    const totalOrderValue = totalRevenue + this.quoteForm.get('totalTax').value;

    this.quoteForm.get('totalRevenue').patchValue(this.convertToFixed(totalRevenue));
    this.quoteForm.get('totalOrderValue').patchValue(this.convertToFixed(totalOrderValue));
    this.quoteForm.get('manpowerRevenue').patchValue(this.convertToFixed(manpowerRevenue));
    this.quoteForm.get('nonManpowerRevenue').patchValue(this.convertToFixed(nonManpowerRevenue));
    this.quoteForm.get('licenseRevenue').patchValue(this.convertToFixed(licenseRevenue));
  }

  /**
   * @description Calculates total cost for a position
   * @param positionFormGroup 
   */
  calculatePositionCost = (positionFormGroup: FormGroup) => {

    const noOfResources = positionFormGroup.get('noOfResources').value;
    const costPerUnit = positionFormGroup.get('costPerUnit').value;
    const quantity = positionFormGroup.get('quantity').value;

    const totalCost = noOfResources * costPerUnit * quantity;

    positionFormGroup.get('totalCost').patchValue(this.convertToFixed(totalCost));

    this.calculateOverallCost();

  }

  convertToFixed = (value) => {
    return parseFloat(value.toFixed(2));
  }

   /**
   * @description Calculates the overall cost for the quote
   */
   calculateOverallCost = () => {
    let totalCost = 0;
    let nonManpowerCost = 0;
    let licenseCost = 0;
    let manpowerCost = 0;
  
    for (const serviceItem of this.servicesFormArr.value) {
      for (const positionItem of serviceItem['positions']) {
  
        const category = positionItem['isNonManpower']
          ? 2
          : positionItem['isLicense']
          ? 3
          : 1;
  
        switch (category) {
          case 2: // Non-Manpower
            nonManpowerCost += positionItem['totalCost'];
            break;
          case 3: // License
            licenseCost += positionItem['totalCost'];
            break;
          case 1: // Manpower
          default:
            manpowerCost += positionItem['totalCost'];
            break;
        }
  
        for (const nmpItem of positionItem['nmpData']) {
          nonManpowerCost += nmpItem['totalCost'];
        }
      }
    }
  
    totalCost = this.convertToFixed(manpowerCost + nonManpowerCost + licenseCost);
  
    this.quoteForm.get('totalCost').patchValue(totalCost);
    // this.costSummary = {
    //   resourceCost: manpowerCost,
    //   nonManpowerCost: nonManpowerCost,
    //   licenseCost: licenseCost,
    //   total: totalCost
    // };
    this.quoteForm.get('manpowerCost').patchValue(this.convertToFixed(manpowerCost));
    this.quoteForm.get('nonManpowerCost').patchValue(this.convertToFixed(nonManpowerCost));
    this.quoteForm.get('licenseCost').patchValue(this.convertToFixed(licenseCost));
  }

  /**
  * @description Calculates the overall GM for the quote
  */
  calculateOverallGM = () => {

    let totalGM = 0;

    for (const serviceItem of this.servicesFormArr.getRawValue())
      for (const positionItem of serviceItem['positions']) {

        totalGM += positionItem['totalGM'];

        for (const nmpItem of positionItem['nmpData'])
          totalGM += nmpItem['totalGM'];

      }

    totalGM = this.convertToFixed(totalGM);

    this.quoteForm.get('totalGM').patchValue(totalGM);

    this.calculateOverallGMPercentage(this.quoteForm);

  }

   /**
  * @description Calculates the overall GM percentage for the quote
  */
   calculateOverallGMPercentage = (formGroup: FormGroup) => {

    const gm = formGroup.get('totalGM').value;
    const revenue = formGroup.get('totalRevenue').value;

    let gmPercentage = 0;

    if (revenue != 0)
      gmPercentage = this.convertToFixed((gm / revenue) * 100);

    formGroup.get('totalGMPercentage').patchValue(gmPercentage);

  }
  
  navigateToLandingPage = () => {

    this.$router.navigate(['../../'], { relativeTo: this.route });

  }

  /**
   * @description Adds a new service form group to service form arr
   * @param label
   */
  addService = async (label) => {

    const { AddServicesDialogComponent } = await import('../components/add-services-dialog/add-services-dialog.component');

    const openAddServicesDialogComponent = this.dialog.open(AddServicesDialogComponent, {
      height: '90%',
      width: '70%',
      data: {
        label: label,
        opportunityId: this.opportunityId,
        mandatoryFields: this.mandatoryFields,
        experienceList: this.experienceList,
        workLocationList: this.workLocationList,
        nationalityList: this.nationalityList,
        entityList: this.entityList,
        divisionList: this.divisionList,
        subDivisionList: this.subDivisionList,
        defaultCurrency: this.defaultCurrency,
        conversionTypeId: this.conversionTypeId,
        quoteCurrency: this.quoteForm.get('quoteCurrency').value
      },
      disableClose: true
    });

    openAddServicesDialogComponent.afterClosed().subscribe(async (res: any) => {

      if (res && res.event == 'submit' && res.data) {

        const servicesList = res.data;

        if (servicesList.length)
          servicesList.forEach((serviceItem: any, i) => {

            let servicesFormGroup = this.getServicesFormGroup();

            servicesFormGroup.patchValue(
              {
                serviceId: serviceItem['serviceId'],
                serviceName: serviceItem['serviceName'],
                serviceTypeId: serviceItem['serviceTypeId'],
                isFixedRate: serviceItem['isFixedRate'],
                isDeleteServiceEnabled: true
              },
              {
                emitEvent: false
              }
            );

            let positionFormArr = servicesFormGroup.get('positions') as FormArray;

            positionFormArr.clear();

            const newAddedPositionFieldConfig = this.newAddedPostionAfterProjectInterationConfig(this.newly_added_quote_project_integration_config);

            const defaultValueAllocation = this.allocateDefaultValues(this.newly_added_quote_project_integration_config)
            
            const defaultValues = Object.entries(defaultValueAllocation)
            .filter(([key, value]) => typeof value === 'number')
            .reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {});   

            if (serviceItem['positions'].length == 0) {

              const positionFormGroup = this.getPositionFormGroup();

              this.resolveSDMapping(servicesFormGroup, positionFormGroup);


              positionFormGroup.patchValue({
                ...newAddedPositionFieldConfig,
                ...defaultValues
              })


              positionFormArr.push(positionFormGroup);

              this.patchMilestoneDetailsUsingForm()

            }

            else
              for (let positionItem of serviceItem['positions']) {

                let positionFormGroup = this.getPositionFormGroup(false, positionItem['isLicense']);

                const newAddedPositionFieldConfig = this.newAddedPostionAfterProjectInterationConfig(this.newly_added_quote_project_integration_config);

                const defaultValueAllocation = this.allocateDefaultValues(this.newly_added_quote_project_integration_config)

                const defaultValues = Object.entries(defaultValueAllocation)
                .filter(([key, value]) => typeof value === 'number')
                .reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {});                   

                positionFormGroup.patchValue({
                  ...defaultValues,
                  quotePositionId: positionItem['quotePositionId'] || uuidv4(),
                  positionId: positionItem['positionId'],
                  positionName: positionItem['positionName'],
                  experience: positionItem['experience'],
                  workLocation: positionItem['workLocation'],
                  nationality: positionItem['nationality'],
                  entity: positionItem['entity'],
                  division: positionItem['division'],
                  subDivision: positionItem['subDivision'],
                  unit: positionItem['unit'],
                  quantity: 1,
                  ratePerUnit: positionItem['revenue'] || 0,
                  totalRevenue: positionItem['revenue'] || 0,
                  costPerUnit: positionItem['cost'] || 0,
                  totalCost: positionItem['cost'] || 0,
                  totalGM: (positionItem['revenue'] || 0) - (positionItem['cost'] || 0),
                  rcValue: positionItem['revenue'] || 0,
                  revenueRegion: this.checkAndAllocateRevenueRegion(this.wholeQuoteData?.sales_region || 
                                  this.oppQuoteDetails?.sales_region, positionItem['entity']),
                  ...newAddedPositionFieldConfig,
                 }, { emitEvent: false });

                this.resolveSDMapping(servicesFormGroup, positionFormGroup);

                this.calculatePositionRevenue(positionFormGroup);

                this.calculateOverallGMPercentage(positionFormGroup);

                this.getPositionQuantity(positionFormGroup);

                positionFormArr.push(positionFormGroup);

              }

            this.servicesFormArr.push(servicesFormGroup);

            // console.log(this.servicesFormArr)
            // console.log('Service Form');

            this.addPositionDropZone();

            this.patchMilestoneDetailsUsingForm()

          });

        this.calculateOverallCost();

        this.calculateOverallRevenue();

        setTimeout(() => {

          this.scrollContainer = this.scrollFrame.nativeElement;

        }, 10);

      }

    });

  }

  /**
   * Adds a new position drop zone when a new service is added
   */
  addPositionDropZone = () => {

    this.positionDropZones.push(`positionDropZone${this.servicesFormArr.length - 1}`);

  }

  /**
   * @description Opens the quote details in PDF preview mode
   */
  previewQuote = () => {

    this.isEditMode = false;

  }

    async checkMandatory() {
    const formValue = this.quoteForm.getRawValue();
    const missingFields: string[] = [];

    const isEmpty = (value: any) => {
      return (
        value === null ||
        value === undefined ||
        (typeof value === 'string' && value.trim() === '')
      );
    };

    const checkFields = (dataObject: any, scopeLabel: string = '') => {
      for (const field of this.customizeFields) {
        const skipKey = field.key === 'quotePostitionId';
        const FieldType = field.fieldType === 'dropdown';

        if (field.isMandatory && !skipKey && FieldType) {
          const fieldValue = dataObject[field.key];

          if (isEmpty(fieldValue)) {
            const label = field.label || field.key;
            missingFields.push(scopeLabel ? `${scopeLabel} - ${label}` : label);
          }
        }
      }
    };

    if (Array.isArray(formValue.services)) {
      formValue.services.forEach((service: any) => {
        if (Array.isArray(service.positions)) {
          service.positions.forEach((position: any) => {
            const scope = `${position.positionName}`;
            checkFields(position, scope);
          });
        }
      });
    } 

    if (missingFields.length) {
      const message = `${missingFields.join(', ')}`;
      this._toaster.showError('Mandatory Fields Error', message, this.opportunityService.longInterval);
      return false;
    }

    return true;
  }


  /**
   * @description Saves the quote details
   */
   saveQuote = async() => {

    if (this.isCUDEnabled) {

      let updateFromDiscount = this.wholeQuoteData?.change_request_merged?.length && this.wholeQuoteData?.discountAndTax.length && this.quoteForm.get('quoteId').value && this.quoteForm.get('totalOrderValue').value != this.wholeQuoteData?.quote_amount;

      if (!this.isEditMode)
        return this.isEditMode = true;

      if (this.quoteForm.status == "VALID") {

        const isMandatoryValid = await this.checkMandatory();
        if (!isMandatoryValid) {
          return;
        }

        const formValue = this.quoteForm.getRawValue();

        if (formValue['totalRevenue'] <= 0 && formValue['activeQuote']) {
          this._toaster.showError("Mandatory Field Error", "The quote revenue must be greater than 0.", this.opportunityService.longInterval);
          return true;
        }

        if (this.wEMappingEnabled && this.wEMappingList.length && !this.validateWEMapping(formValue))
          return true;

        if (this.sDMappingEnabled && this.sDMappingList.length && !this.validateSDMapping(formValue))
          return true;

        if (this.geographicRevenueRegionMapping && this.revenueRegionMapping.length && !this.validateSalesRegionGeoMapping(formValue))
          return true;

        this.isEditMode = false;

        let quoteDetails = {};

        let services = [], discountAndTax = { items: [] };
        
        for (const [serviceIndex, serviceItem] of formValue['services'].entries()) {

          let positions = [];

          for (const [positionIndex, positionItem] of serviceItem['positions'].entries()) {

            if (!positionItem['isLicense'] && !positionItem['isNonManpower']) {
            
              const currentPositionData = this.skillList.find(val => val['id'] == positionItem['positionId']);

              if (!currentPositionData) {

                const positions = this.servicesFormArr.at(serviceIndex).get('positions') as FormArray;

                positions.at(positionIndex).patchValue({
                  positionId: null,
                  positionName: null
                }, { emitEvent: false });
                
                this._toaster.showError("Mandatory Field Error", "Kindly Select Valid Position", this.opportunityService.longInterval);
                
                return this.isEditMode = true;

              }

            }

            let positionItems = [];

            if (positionItem['nmpData'] && positionItem['nmpData'].length)
              for (const [nmpIndex, nmpItem] of positionItem['nmpData'].entries()) {

                let isPositionEffortChanged = nmpItem['isPositionEffortChanged'] || false;

                if (nmpItem['isQuantityChanged'])
                  isPositionEffortChanged = true;

                positionItems.push({
                  // qp_item_id: nmpItem['quotePositionId'],
                  qp_item_id: typeof nmpItem['quotePositionId'] === 'number' ? nmpItem['quotePositionId'] : nmpItem['quotePositionId'] && nmpItem['quotePositionId'].length > 8 ? null: nmpItem['quotePositionId'] || null,
                  nmp_id: nmpItem['positionId'],
                  name: nmpItem['positionName'] || null,
                  no_of_resource: nmpItem['noOfResources'],
                  rate_per_unit: nmpItem['ratePerUnit'],
                  original_rate: nmpItem['originalRatePerUnit'],
                  cost_per_unit: nmpItem['costPerUnit'],
                  entity: nmpItem['entity'],
                  division: nmpItem['division'],
                  sub_division: nmpItem['subDivision'],
                  work_location: nmpItem['workLocation'],
                  nationality: nmpItem['nationality'],
                  unit_id: nmpItem['unit'],
                  quantity: nmpItem['quantity'],
                  resource_type_id: 2,
                  qpi_currency: formValue['quoteCurrency'],
                  qpi_amount: nmpItem['totalCost'], //nmp_cost ?
                  qpi_revenue_amount: nmpItem['totalRevenue'], //nmp_revenue ?
                  start_date: nmpItem['startDate'],
                  end_date: nmpItem['endDate'],
                  position_effort: nmpItem['positionEffortData'] || null,
                  is_position_effort_changed: isPositionEffortChanged,
                  is_position_value_changed: nmpItem['isPositionValueChanged'],
                  rc_value: nmpItem['rcValue'],
                  business_type : nmpItem['typeOfBusiness'],
                  revenue_region : nmpItem['revenueRegion'],
                  position_status : nmpItem['positionStatus'],
                  // milestone_id : (nmpItem['milestone']?.length > 8) ? null : positionItem['milestone'],
                  milestone_id: (() => {
                    const foundMilestone = this.milestoneList.find(milestone => milestone.id == nmpItem['milestone']);
                    if (foundMilestone) {
                        // Set milestone_id to foundMilestone.milestone_id based on conditions
                        return (foundMilestone.is_master === 1 || (foundMilestone.milestone_id && foundMilestone.milestone_id.length > 8))
                            ? null
                            : foundMilestone.milestone_id;
                    }
                    return null;
                })(),
                  milestoneDetails: this.milestoneList.find(milestone => milestone.id == nmpItem['milestone']) || [],
                  milestone: (() => {
                    const foundMilestone = this.milestoneList.find(milestone => milestone.id == nmpItem['milestone']);
                    if (foundMilestone) {
                      // foundMilestone.milestone_id = (foundMilestone.milestone_id && foundMilestone.milestone_id.length > 8) ? null : foundMilestone.milestone_id;

                      foundMilestone.milestone_id = (foundMilestone?.is_master == 1) ? null : 
                                      (foundMilestone.milestone_id && foundMilestone.milestone_id.length > 8) ? null : 
                                      foundMilestone.milestone_id;

                      return [foundMilestone]; 
                    }
                    return [];
                  })(),
                });

              }

            let isPositionEffortChanged = positionItem['isPositionEffortChanged'] || false;

            if (positionItem['isQuantityChanged'])
              isPositionEffortChanged = true;

            positions.push({
              quote_position_id: typeof positionItem['quotePositionId'] === 'number' ? positionItem['quotePositionId'] : positionItem['quotePositionId'] && positionItem['quotePositionId'].length > 8 ? null: positionItem['quotePositionId'] || null,
              position: positionItem['positionId'],
              position_name: positionItem['positionName'] || null,
              resource_count: positionItem['noOfResources'],
              rate_per_unit: positionItem['ratePerUnit'],
              original_rate: positionItem['originalRatePerUnit'],
              cost_per_unit: positionItem['costPerUnit'],
              work_experience: positionItem['experience'],
              nationality: positionItem['nationality'],
              work_location: positionItem['workLocation'],
              entity: positionItem['entity'],
              division: positionItem['division'],
              sub_division: positionItem['subDivision'],
              unit_id: positionItem['unit'],
              quantity: positionItem['quantity'],
              resource_type_id: positionItem['isNonManpower'] ? 2 : positionItem['isLicense'] ? 3 : 1,
              item_currency: formValue['quoteCurrency'],
              item_revenue_amount: positionItem['totalRevenue'],
              item_cost_amount: positionItem['totalCost'],
              start_date: positionItem['startDate'],
              end_date: positionItem['endDate'],
              position_order: positionIndex,
              position_items: positionItems,
              position_effort: positionItem['positionEffortData'] || null,
              is_position_effort_changed: isPositionEffortChanged,
              is_position_value_changed: positionItem['isPositionValueChanged'],
              rc_value: positionItem['rcValue'],
              business_type: positionItem['typeOfBusiness'],
              revenue_region: positionItem['revenueRegion'],
              position_status: positionItem['positionStatus'],              
              // milestone_id: positionItem['milestone']?.length > 8 ? null : positionItem['milestone'],
              milestone_id: (() => {
                const foundMilestone = this.milestoneList.find(milestone => milestone.id == positionItem['milestone']);
                if (foundMilestone) {
                    // Set milestone_id to foundMilestone.milestone_id based on conditions
                    return (foundMilestone.is_master === 1 || (foundMilestone.milestone_id && foundMilestone.milestone_id.length > 8))
                        ? null
                        : foundMilestone.milestone_id;
                }
                return null;
            })(),
              milestone: (() => {
                const foundMilestone = this.milestoneList.find(milestone => milestone.id == positionItem['milestone']);
                if (foundMilestone) {
                  // foundMilestone.milestone_id = (foundMilestone.milestone_id && foundMilestone.milestone_id.length > 8) ? null : foundMilestone.milestone_id;
                  
                  foundMilestone.milestone_id = (foundMilestone?.is_master == 1) ? null : 
                  (foundMilestone.milestone_id && foundMilestone.milestone_id.length > 8) ? null : 
                  foundMilestone.milestone_id;
                  return [foundMilestone]; 
                }
                return [];
              })(),
              calendar_id: positionItem['calendarId'] || this.calendarId
            });
            
          }

          services.push({
            quote_service_id: serviceItem['quoteServiceId'],
            service_header_id: serviceItem['serviceId'],
            service_name: serviceItem['serviceName'],
            service_type_id: serviceItem['serviceTypeId'] || null,
            service_revenue_amount: serviceItem['serviceRevenue'],
            service_cost_amount: serviceItem['serviceCost'],
            service_currency: formValue['quoteCurrency'],
            is_fixed_rate: serviceItem['isFixedRate'] || null,
            service_order: serviceIndex,
            position: positions
          });

        }

        for (const discountItem of formValue['discounts'])
          discountAndTax['items'].push({
            dt_item_id: discountItem['dtItemId'],
            id: discountItem['discountId'],
            name: discountItem['discountName'],
            percentage: discountItem['discountPercentage'],
            type: "D",
            tax_type: null,
            item_amount: discountItem['discountValue'],
            item_currency: formValue['quoteCurrency'],
            is_custom: discountItem['isCustom']
          });

        for (const taxItem of formValue['taxes'])
          discountAndTax['items'].push({
            dt_item_id: taxItem['dtItemId'],
            id: taxItem['taxId'],
            name: taxItem['taxName'],
            percentage: taxItem['taxPercentage'],
            type: "T",
            tax_type: null,
            item_amount: taxItem['taxValue'],
            item_currency: formValue['quoteCurrency'],
            is_custom: false
          });

        quoteDetails['quote_name'] = formValue['quoteName'];
        quoteDetails['quote_currency'] = formValue['quoteCurrency'];
        quoteDetails['quote_currency_id'] = formValue['quoteCurrencyId'];
        quoteDetails['initial_currency_id'] = formValue['initialCurrencyId'] || formValue['quoteCurrencyId'];
        quoteDetails['quote_amount'] = formValue['totalOrderValue']; //
        quoteDetails['quote_cost_amount'] = formValue['totalCost'];
        quoteDetails['quote_revenue_amount'] = formValue['totalRevenue'];
        quoteDetails['delivery_start_date'] = formValue['deliveryStartDate'];
        quoteDetails['delivery_end_date'] = formValue['deliveryEndDate'];
        quoteDetails['opportunity_id'] = this.opportunityId;
        quoteDetails['service'] = services;
        quoteDetails['discountAndTax'] = discountAndTax;
        quoteDetails['version'] = formValue['version'];
        // quoteDetails['calendar_id'] = this.calendarId || 1;
        quoteDetails['work_schedule_id'] = this.workScheduleId || 1;
        quoteDetails['default_currency'] = this.defaultCurrency;
        quoteDetails['conversion_type_id'] = this.conversionTypeId;
        quoteDetails['initial_delivery_start_date'] = this.initial_delivery_start_date;
        quoteDetails['initial_delivery_end_date'] = this.initial_delivery_end_date;
        quoteDetails['is_quote_active'] = formValue['activeQuote'];
        quoteDetails['has_parent_opportunity'] = formValue['quoteOpportunityHasParent'];
        quoteDetails['quote_type'] = formValue['quoteType'];

        this.isDataBeingSaved = true;

        if (formValue['quoteId']) {

          quoteDetails['quote_header_id'] = formValue['quoteId'];
          
            // this._quoteMainService.checkForPercentageApproval(quoteDetails)
            //   .pipe(takeUntil(this._onDestroy))
            //   .subscribe(res => {
            //     if(res['data']){
            //       this._toaster.showWarning("Warning", res['msg'] || "More than a certain Percentage value Increase should be Change Request", this.opportunityService.mediumInterval);
            //       return false
            //     }  
            //   })
              
                let res =  await this._quoteMainService.checkForPercentageApproval(quoteDetails,true) || null;
                if (res['data'] && res['applicable_for_quote_edit']) {
                  await this.patchQuoteDetails(this.wholeQuoteData);
                  this.isDataBeingSaved = false;
                  return this._toaster.showWarning("Warning", res['edit_msg'] || "More than a certain Percentage value Increase should be Change Request", this.opportunityService.longInterval); 
                }
                
                // Handle further logic if no warning is triggered
              this._quoteMainService.updateQuote(quoteDetails)
              .pipe(takeUntil(this._onDestroy))
              .subscribe(async res => {
                if(res['rds_peak']){
                  this._toaster.showError("System Unavailable", res['msg'] || "System is Unavailable try after Sometime!", 3000);
                  return true
                }
                 if(res['messType']=='W' && res['data']){
                  this.patchQuoteDetails(this.wholeQuoteData);
                  this.isDataBeingSaved = false;
                  this._toaster.showWarning("Warning",res['msg']||"Quantity of Position Cannot be reduced below allocated hours" , this.opportunityService.longInterval);
                  return true
                 }
                if (res['messType'] == 'S' && res['data']) {
  
                  this._toaster.showSuccess("Success", `Quote Updated Successfully !`, this.opportunityService.longInterval);
  
                  this.toggleSpinner(true);
  
                  await this.getQuoteDetails();
  
                  this.alloteFieldConfig();
  
                  // this.patchQuoteDetails(this.wholeQuoteData);
  
                  if (this.quoteForm.get('activeQuote').value)
                    await this.resolveQuoteOpportunityIntegration(quoteDetails);
  
                  this.patchQuoteDetails(this.wholeQuoteData);

                  if(this.milestoneApplicable)
                    await this.getMilestoneListQB();

                  this.patchMilestoneDetailsUsingForm();
  
                  this.toggleSpinner(false);
  
                  this.isChangesMade = false;
  
                }
  
                else
                  this._toaster.showError("Failed", "Quote Updation Failed !", this.opportunityService.mediumInterval);
  
                this.isDataBeingSaved = false;
  
              },
                err => {
                  console.log(err);
                  this.isDataBeingSaved = false;
                  this._toaster.showError("Error", `Error in Updation Quote Data`, this.opportunityService.mediumInterval);
                });

        }

        else {

          this._quoteMainService.createQuote(quoteDetails)
            .pipe(takeUntil(this._onDestroy))
            .subscribe(res => {
              if(res['rds_peak']){
                this._toaster.showError("System Unavailable", res['msg'] || "System is Unavailable try after Sometime!", 3000);
                this.isDataBeingSaved = false;
              }
              if (res['messType'] == 'S' && res['data']) {

                this._toaster.showSuccess("Success", `Quote Created Successfully !`, this.opportunityService.longInterval);

                this.quoteForm.patchValue({
                  quoteId: res['data']
                }, { emitEvent: false });

                this.$router.navigate([`../../edit/${res['data']}`], { relativeTo: this.route });

              }

              else
                this._toaster.showError("Failed", "Quote Creation Failed !", this.opportunityService.mediumInterval);

              this.isDataBeingSaved = false;

            },
              err => {
                console.log(err);
                this.isDataBeingSaved = false;
                this._toaster.showError("Error", `Error in Quote Creation`, this.opportunityService.mediumInterval);
              });

        }

      }

      else {

        for (const serviceItem of this.servicesFormArr.controls)
          for (const positionItem of (serviceItem.get('positions') as FormArray)['controls'])
            if (positionItem.status == "INVALID") {

              for (const nmpItem of (positionItem.get('nmpData') as FormArray)['controls'])
                if (nmpItem.status == "INVALID")
                  return this.resolveMandatoryMsg(nmpItem);

              return this.resolveMandatoryMsg(positionItem);
              
            }
              
      }

    }

  }

  resolveMandatoryMsg = (positionItem) => {

    for (const [key, value] of Object.entries(positionItem['controls']))
      if (value['status'] == "INVALID") {
        
        let mandatoryMsg = "Kindly Fill";

        const field = this.customizeFields.find(val => val['key'] === key);

        if (key == "positionId")
          mandatoryMsg += " Position Field";

        else {

          if (field)
            mandatoryMsg += ` ${field['label']} Field of ${positionItem.get('positionName').value} position`;

        }

        if (field && field['isVisible'].value == false)
          mandatoryMsg += " (Kindly enable the field in Customize Fields Option)";

        if (this.wEMappingEnabled && key == "entity")
          mandatoryMsg += " (Kindly Update the Mapping)";

        if (this.sDMappingEnabled && key == "division")
          mandatoryMsg += " (Kindly Update the Mapping)";

        return this._toaster.showError("Mandatory Field Error", mandatoryMsg, this.opportunityService.longInterval);
  
      }

  }

  /**
   * @description Adds a new position form group to the form array
   * @param serviceItem 
   * @param positionIndex 
   * @param formArrayName
   */
  addPosition = (serviceItem: FormGroup, positionIndex: number, formArrayName: string, positionItem: FormGroup) => {
    const newAddedPositionFieldConfig = this.newAddedPostionAfterProjectInterationConfig(this.newly_added_quote_project_integration_config);
    const defaultValueAllocation = this.allocateDefaultValues(this.newly_added_quote_project_integration_config)
    const defaultValues = Object.entries(defaultValueAllocation)
    .filter(([key, value]) => typeof value === 'number')
    .reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {});   
    const positionFormGroup = this.getPositionFormGroup(formArrayName === 'nmpData');

    positionFormGroup.patchValue({
      ...defaultValues,
      ...newAddedPositionFieldConfig
    }, { emitEvent: false });

    positionFormGroup.patchValue({
      quotePositionId: uuidv4(),
      // milestone: defaultValueAllocation['milestone'] || 1
    },{ emitEvent: false })


    // const matchedMilestone = this.milestoneList.find(milestone => milestone.id === defaultValueAllocation['milestone'] || 1);

    // if (matchedMilestone) {
    //   positionFormGroup.get('milestoneDetails')?.patchValue([matchedMilestone], { emitEvent: false });
    // }

    this.resolveSDMapping(serviceItem, positionFormGroup);

    ((positionItem ? positionItem : serviceItem).get(formArrayName) as FormArray).insert(positionIndex + 1, positionFormGroup);

    const matchedMilestone = this.milestoneList.find(milestone => milestone.id === defaultValues['milestone']);

    if (matchedMilestone) {
      positionFormGroup.get('milestoneDetails')?.patchValue([matchedMilestone], { emitEvent: false });
    }

    this.patchMilestoneDetailsUsingForm()

  }

  /**
  * @description Removes position form group to the form array
  * @param serviceItem 
  * @param positionIndex 
  * @param formArrayName
  */
  deletePosition = (serviceItem: FormGroup, positionIndex: number, formArrayName: string) => {

    const positionFormArray = serviceItem.get(formArrayName) as FormArray;
    const defaultValueAllocation = this.allocateDefaultValues(this.newly_added_quote_project_integration_config)
    const positionFormGroup = this.getPositionFormGroup(formArrayName === 'nmpData');


    if (formArrayName != 'nmpData' && positionFormArray.length == 1)
      return this._toaster.showError("Mandatory Error", "Minimum of 1 Position has to be present", this.opportunityService.longInterval);

    if (positionFormArray.at(positionIndex).get('quotePositionId').value != null)
      this._util.openConfirmationSweetAlertWithCustom("Are you Sure ?", "Do you want to delete this position")
        .then((deleteConfirm) => {

          if (deleteConfirm) {

            positionFormArray.removeAt(positionIndex);

            this.patchMilestoneDetailsUsingForm();

            this.calculateOverallRevenue();

            this.calculateOverallCost();

            this.calculateOverallGM();

          }
          
        });

    else {

      positionFormArray.removeAt(positionIndex);

      this.patchMilestoneDetailsUsingForm();

      this.calculateOverallRevenue();

      this.calculateOverallCost();

      this.calculateOverallGM();

    }

  }

  /**
  * @description Duplicates positions form group to the form array
  * @param serviceItem 
  * @param positionIndex 
  * @param formArrayName
  */
  duplicatePosition = async(serviceItem: FormGroup, positionIndex: number, formArrayName: string, positionItem: FormGroup) =>  {

    let positionFormArray = ((positionItem ? positionItem : serviceItem).get(formArrayName) as FormArray);

    const formArrayData = positionFormArray.at(positionIndex).value;

    let positionFormGroup = this.getPositionFormGroup(formArrayName === 'nmpData');

    positionFormArray.at(positionIndex).get('positionDuplicationLoader').patchValue(true);

    const newAddedPositionFieldConfig = this.newAddedPostionAfterProjectInterationConfig(this.newly_added_quote_project_integration_config);

    positionFormGroup.patchValue(formArrayData, { emitEvent: false });

    positionFormGroup.patchValue({
      quotePositionId: uuidv4(),
      lastUnit: formArrayData['unit'],
      isQuantityChanged: true,
      isUnitChanged: false,
      ...newAddedPositionFieldConfig
    }, { emitEvent: false });

    if (!formArrayData['positionEffortData'] && formArrayData['quotePositionId'] && typeof formArrayData['quotePositionId'] !== 'string' && !formArrayData['isQuantityChanged']) {
      formArrayData['positionEffortData'] = await this.getCalendarDataForTheDuration(positionFormGroup as FormGroup, formArrayData);
    }

    if (!formArrayData['positionEffortData'] && (!formArrayData['quotePositionId'] || (typeof formArrayData['quotePositionId'] === 'string' &&  formArrayData['quotePositionId'].length > 8))) 
      formArrayData['positionEffortData'] = null;
    

    // if (formArrayData['positionEffortData']) {
      if (!positionFormGroup.contains('positionEffortData')) {
        positionFormGroup.addControl('positionEffortData', this.fb.control([]));
      }

      positionFormGroup.patchValue({
        positionEffortData: formArrayData['positionEffortData'],
        isQuantityChanged: false,
        isPositionEffortChanged: true,
        isUnitChanged: false
      }, { emitEvent: false });

    // } else {
    //   this._toaster.showError('Error Duplicating Position Effort', '', 1500);
    //   positionFormArray.at(positionIndex).get('positionDuplicationLoader').patchValue(false);
    //   return;
    // }

    positionFormGroup.get('ratePerUnit').patchValue(formArrayData['ratePerUnit']);
    positionFormGroup.get('costPerUnit').patchValue(formArrayData['costPerUnit']);

    this.resolveSDMapping(serviceItem, positionFormGroup);

    positionFormArray.insert(positionIndex + 1, positionFormGroup);

    positionFormArray.at(positionIndex).get('positionDuplicationLoader').patchValue(false);

    this.patchMilestoneDetailsUsingForm();

}


  /**
  * @description Clear position form group in the form array
  * @param serviceItem 
  * @param positionIndex 
  * @param formArrayName
  */
  clearPosition = (serviceItem: FormGroup, positionIndex: number, formArrayName: string) => {

    let positionFormArray = (serviceItem.get(formArrayName) as FormArray);

    positionFormArray.at(positionIndex).patchValue({
      positionId: null,
      noOfResources: 1,
      experience: null,
      workLocation: null,
      nationality: null,
      unit: null,
      ratePerUnit: 0,
      quantity: 0,
      totalRevenue: 0,
      rcValue: 0
    });

  }

  /**
   * @description Performs the action based on button click
   * @param buttonItem
   */
  addButtonClick = (buttonItem) => {

    switch (buttonItem.id) {

      case 1:

        this.openSectionOverlay();

        break;

      case 2:

        this.addService(buttonItem.label);

        break;

      case 3:

        this.openPositionDialog(buttonItem.label);

        break;

      case 4:

        this.openNonManpowerDialog(buttonItem.label);

        break;

      case 5:

        this.openLicenseDialog(buttonItem.label);

        break;

      case 6:

        if (this.quoteForm.get('totalOrderValue').value <= 0)
          return this._toaster.showWarning( 'Discount cannot be applied as the Total Order Value is zero or negative.', '', this.opportunityService.mediumInterval)

        const discountFormArr = this.quoteForm.get('discounts') as FormArray;

        discountFormArr.push(this.getDiscountsFormGroup());

        setTimeout(() => {

          this.scrollToBottom();
    
        }, 10);
    
        break;

      case 7:

      if (this.quoteForm.get('totalOrderValue').value <= 0)
        return this._toaster.showWarning( 'Tax cannot be applied as the Total Order Value is zero or negative.', '', this.opportunityService.mediumInterval)
      
        const taxFormArr = this.quoteForm.get('taxes') as FormArray;

        taxFormArr.push(this.getTaxesFormGroup());

        setTimeout(() => {

          this.scrollToBottom();
    
        }, 10);

        break;
      
      case 8:

        this.openMilestoneDialog();

        break;

      default:
        break;

    }

  }

  /**
   * @description Opens the Section Overlay
   */
  openSectionOverlay = () => {

    if (!this.sectionOverlayRef?.hasAttached()) {

      this.addSectionFormControl.reset();

      const position = this.overlay.position().global()
        .centerHorizontally().centerVertically();

      this.sectionOverlayRef = this.overlay.create({
        positionStrategy: position,
        hasBackdrop: true
      });

      const templatePortal = new TemplatePortal(this.templateRef, this.viewContainerRef);

      this.sectionOverlayRef.attach(templatePortal);

      this.sectionOverlayRef.backdropClick().subscribe(() => {
        this.closeSectionDialog();
      });

    }

  }

  /**
   * @description Closes the section dialog
   */
  closeSectionDialog = () => {

    this.addSectionFormControl.reset();

    this.sectionOverlayRef?.dispose();

  }

  /**
   * @description Adds a new section to the quote
   */
  addSection = () => {

    const servicesFormGroup = this.getServicesFormGroup();

    servicesFormGroup.patchValue(
      {
        serviceId: null,
        serviceName: this.addSectionFormControl.value,
        isSection: true
      },
      {
        emitEvent: false
      }
    );

    const positionFormArr = servicesFormGroup.get('positions') as FormArray;

    const newAddedPositionFieldConfig = this.newAddedPostionAfterProjectInterationConfig(this.newly_added_quote_project_integration_config);

    const defaultValueAllocation = this.allocateDefaultValues(this.newly_added_quote_project_integration_config)

    const defaultValues = Object.entries(defaultValueAllocation)
    .filter(([key, value]) => typeof value === 'number')
    .reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {});   
    
    let positionFormGroup =  this.getPositionFormGroup()
    positionFormGroup.patchValue({
      ...newAddedPositionFieldConfig,
      ...defaultValues
    })

    // If Required for Discount - Where the values flow from rate card.
    // positionFormGroup.patchValue({
    //   ratePerUnit: currentNmpItem['revenue'] || 0,
    //   originalRatePerUnit: currentNmpItem['revenue']
    // })


    positionFormArr.push(positionFormGroup);

    this.servicesFormArr.push(servicesFormGroup);

    this.addPositionDropZone();

    this.closeSectionDialog();

  }

  /**
  * @description Opens the position dialog
  * @param label
  */
  openPositionDialog = async (label) => {

    const { AddPositionsDialogComponent } = await import('../components/add-positions-dialog/add-positions-dialog.component');

    const openPositionDialogComponent = this.dialog.open(AddPositionsDialogComponent, {
      height: '70%',
      width: '50%',
      data: {
        label: label,
        positionList: this.skillList
      },
      disableClose: true
    });

    openPositionDialogComponent.afterClosed().subscribe(async (res: any) => {

      if (res && res.event == 'submit' && res.data) {

        const positionsList = res.data;

        let servicesFormGroup = this.servicesFormArr.at(this.servicesFormArr.length - 1);

        let positionFormArr = servicesFormGroup.get('positions') as FormArray;

        for (let positionItem of positionsList) {

          let positionFormGroup = this.getPositionFormGroup();

          const newAddedPositionFieldConfig = this.newAddedPostionAfterProjectInterationConfig(this.newly_added_quote_project_integration_config);
          
          let defaultValueAllocation : any  = this.allocateDefaultValues(this.newly_added_quote_project_integration_config);

          const defaultValues = Object.entries(defaultValueAllocation)
          .filter(([key, value]) => typeof value === 'number')
          .reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {}); 

          positionFormGroup.patchValue({
            quotePositionId: uuidv4(),
            positionId: positionItem['positionId'],
            positionName: positionItem['positionName'],
            // originalRatePerUnit: positionItem['originalRatePerUnit'] || positionItem['ratePerUnit'],
            ...defaultValues,
            ...newAddedPositionFieldConfig,
          }, { emitEvent: false });

          positionFormGroup.patchValue({
            ratePerUnit: positionItem['revenue'] || 0,
            originalRatePerUnit: positionItem['originalRatePerUnit'] || positionItem['ratePerUnit']
          })

          const matchedMilestone = this.milestoneList.find(milestone => milestone.id === defaultValues['milestone']);

          if (matchedMilestone) {
            positionFormGroup.get('milestoneDetails')?.patchValue([matchedMilestone], { emitEvent: false });
          }

          this.getPositionQuantity(positionFormGroup);

          this.resolveSDMapping(servicesFormGroup, positionFormGroup);

          positionFormArr.push(positionFormGroup);

        }

        this.patchMilestoneDetailsUsingForm();

      }

    });

  }

  /**
  * @description Opens the non manpower dialog
  * @param label
  */
  openNonManpowerDialog = async (label) => {

    let positionsList = [], i = 0;

    for (const [serviceIndex, serviceItem] of this.servicesFormArr.value.entries())
      for (const [positionIndex, positionItem] of serviceItem['positions'].entries()) 
        if (!positionItem['isNonManpower'] && !positionItem['isLicense']) {

          i++;

          let nationality = this.nationalityList.find(val => val['id'] === positionItem['nationality']);

          let experience = this.experienceList.find(val => val['id'] === positionItem['experience']);

          positionsList.push({
            uniqueId: i,
            serviceIndex: serviceIndex,
            positionIndex: positionIndex,
            position: positionItem['positionName'],
            nationality: nationality?.name,
            experience: experience?.name
          });

        }

    const { AddNonManpowerDialogComponent } = await import('../components/add-non-manpower-dialog/add-non-manpower-dialog.component');

    const openAddNonManpowerDialogComponent = this.dialog.open(AddNonManpowerDialogComponent, {
      minHeight: '70vh',
      height: 'auto',
      width: '50%',
      data: {
        label: label,
        nmpList: this.nmpList,
        positionsList: positionsList,
        manpowerNonManpowerMapping: this.manpowerNonManpowerMapping
      },
      disableClose: true
    });

    openAddNonManpowerDialogComponent.afterClosed().subscribe(async (res: any) => {

      if (res && res.event == 'submit' && res.data && res['data']['nmpData']) {

        const nmpList = res['data']['nmpData'];

        if (res['data']['taggedPositions'] && res['data']['taggedPositions'].length) {

          for (const taggedPositionItem of res['data']['taggedPositions']) {

            let servicesFormGroup = this.servicesFormArr.at(taggedPositionItem['serviceIndex']);

            let positionFormArr = servicesFormGroup.get('positions') as FormArray;

            let nmpFormArr = positionFormArr.at(taggedPositionItem['positionIndex']).get('nmpData') as FormArray;

            const newAddedPositionFieldConfig = this.newAddedPostionAfterProjectInterationConfig(this.newly_added_quote_project_integration_config);

            let defaultValueAllocation : any  = this.allocateDefaultValues(this.newly_added_quote_project_integration_config);

            const defaultValues = Object.entries(defaultValueAllocation)
            .filter(([key, value]) => typeof value === 'number')
            .reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {}); 

            for (let nmpItem of nmpList) {

              let nmpFormGroup = this.getPositionFormGroup(true);

              const currentNmpItem = this.nmpList.find(val => val['id'] === nmpItem['nmpId']);

              const newAddedPositionFieldConfig = this.newAddedPostionAfterProjectInterationConfig(this.newly_added_quote_project_integration_config);
          
              let defaultValueAllocation : any  = this.allocateDefaultValues(this.newly_added_quote_project_integration_config);

              const defaultValues = Object.entries(defaultValueAllocation)
              .filter(([key, value]) => typeof value === 'number')
              .reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {}); 

              if (currentNmpItem)
                nmpFormGroup.patchValue({
                  ...defaultValues,
                  positionId: nmpItem['nmpId'],
                  positionName: nmpItem['nmpName'],
                  workLocation: currentNmpItem['work_location'],
                  nationality: currentNmpItem['nationality'],
                  entity: currentNmpItem['entity'],
                  division: currentNmpItem['division'],
                  subDivision: currentNmpItem['sub_division'],
                  unit: currentNmpItem['quote_unit'],
                  lastUnit: currentNmpItem['quote_unit'],
                  quantity: 1,
                  ratePerUnit: currentNmpItem['revenue'] || 0,
                  totalRevenue: currentNmpItem['revenue'] || 0,
                  costPerUnit: currentNmpItem['cost'] || 0,
                  totalCost: currentNmpItem['cost'] || 0,
                  totalGM: (currentNmpItem['revenue'] || 0) - (currentNmpItem['cost'] || 0),
                  rcValue: currentNmpItem['revenue'] || 0,
                  originalRatePerUnit: currentNmpItem['revenue'],
                  ...newAddedPositionFieldConfig
                }, { emitEvent: false });

                nmpFormGroup.patchValue({
                  ratePerUnit: currentNmpItem['revenue'] || 0,
                  originalRatePerUnit: currentNmpItem['revenue']
                })

                const matchedMilestone = this.milestoneList.find(milestone => milestone.id === defaultValues['milestone']);

                if (matchedMilestone) {
                  nmpFormGroup.get('milestoneDetails')?.patchValue([matchedMilestone], { emitEvent: false });
                }

              this.calculatePositionRevenue(nmpFormGroup);

              this.calculateOverallGMPercentage(nmpFormGroup);

              this.getPositionQuantity(nmpFormGroup);

              this.resolveSDMapping(servicesFormGroup, nmpFormGroup);

              nmpFormArr.push(nmpFormGroup);

            }

            let positionFormGroup = this.getPositionFormGroup(true)
            const matchedMilestone = this.milestoneList.find(milestone => milestone.id === defaultValues['milestone']);
    
            if (matchedMilestone) {
              positionFormGroup.get('milestoneDetails')?.patchValue([matchedMilestone], { emitEvent: false });
            }

            this.patchMilestoneDetailsUsingForm()

          }

        }

        else {

          let servicesFormGroup = this.servicesFormArr.at(this.servicesFormArr.length - 1);

          let positionFormArr = servicesFormGroup.get('positions') as FormArray;

          const newAddedPositionFieldConfig = this.newAddedPostionAfterProjectInterationConfig(this.newly_added_quote_project_integration_config);

          const defaultValueAllocation = this.allocateDefaultValues(this.newly_added_quote_project_integration_config)

          const defaultValues = Object.entries(defaultValueAllocation)
          .filter(([key, value]) => typeof value === 'number')
          .reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {});

          for (let nmpItem of nmpList) {

            let nmpFormGroup = this.getPositionFormGroup(true);

            const currentNmpItem = this.nmpList.find(val => val['id'] === nmpItem['nmpId']);

            if (currentNmpItem)
              nmpFormGroup.patchValue({
                ...defaultValues,
                positionId: nmpItem['nmpId'],
                positionName: nmpItem['nmpName'],
                workLocation: currentNmpItem['work_location'],
                nationality: currentNmpItem['nationality'],
                entity: currentNmpItem['entity'],
                division: currentNmpItem['division'],
                subDivision: currentNmpItem['sub_division'],
                unit: currentNmpItem['quote_unit'],
                lastUnit: currentNmpItem['quote_unit'],
                quantity: 1,
                ratePerUnit: currentNmpItem['revenue'] || 0,
                totalRevenue: currentNmpItem['revenue'] || 0,
                costPerUnit: currentNmpItem['cost'] || 0,
                totalCost: currentNmpItem['cost'] || 0,
                totalGM: (currentNmpItem['revenue'] || 0) - (currentNmpItem['cost'] || 0),
                rcValue: currentNmpItem['revenue'] || 0,
                ...newAddedPositionFieldConfig,
              }, { emitEvent: false });

            nmpFormGroup.patchValue({
              ratePerUnit: currentNmpItem['revenue'] || 0,
              originalRatePerUnit: currentNmpItem['revenue']
            })

            this.calculatePositionRevenue(nmpFormGroup);

            this.calculateOverallGMPercentage(nmpFormGroup);

            this.getPositionQuantity(nmpFormGroup);

            this.resolveSDMapping(servicesFormGroup, nmpFormGroup);

            positionFormArr.push(nmpFormGroup);

          }

        }

      }

    });

  }

  /**
   * @description Opens the license dialog
   * @param label
   */
  openLicenseDialog = async (label) => {

    const { AddPositionsDialogComponent } = await import('../components/add-positions-dialog/add-positions-dialog.component');

    const openPositionDialogComponent = this.dialog.open(AddPositionsDialogComponent, {
      height: '70%',
      width: '50%',
      data: {
        label: label,
        positionList: this.licenseList,
        isLicense: true
      },
      disableClose: true
    });

    openPositionDialogComponent.afterClosed().subscribe(async (res: any) => {

      if (res && res.event == 'submit' && res.data) {

        const positionsList = res.data;

        let servicesFormGroup = this.servicesFormArr.at(this.servicesFormArr.length - 1);

        let positionFormArr = servicesFormGroup.get('positions') as FormArray;

        const newAddedPositionFieldConfig = this.newAddedPostionAfterProjectInterationConfig(this.newly_added_quote_project_integration_config);

        let defaultValueAllocation : any  = this.allocateDefaultValues(this.newly_added_quote_project_integration_config);

        const defaultValues = Object.entries(defaultValueAllocation)
        .filter(([key, value]) => typeof value === 'number')
        .reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {}); 

        for (const positionItem of positionsList) {

          let positionFormGroup = this.getPositionFormGroup(false, true);

          const currentLicenseItem = this.licenseList.find(val => val['id'] === positionItem['positionId']);

          if (currentLicenseItem)
            positionFormGroup.patchValue({
              ...defaultValues,
              positionId: positionItem['positionId'],
              positionName: positionItem['positionName'],
              workLocation: currentLicenseItem['work_location'],
              nationality: currentLicenseItem['nationality'],
              entity: currentLicenseItem['entity'],
              division: currentLicenseItem['division'],
              subDivision: currentLicenseItem['sub_division'],
              unit: currentLicenseItem['quote_unit'],
              lastUnit: currentLicenseItem['quote_unit'],
              quantity: 1,
              
              totalRevenue: currentLicenseItem['revenue'] || 0,
              costPerUnit: currentLicenseItem['cost'] || 0,
              totalCost: currentLicenseItem['cost'] || 0,
              totalGM: (currentLicenseItem['revenue'] || 0) - (currentLicenseItem['cost'] || 0),
              rcValue: currentLicenseItem['revenue'] || 0,
              ...newAddedPositionFieldConfig,
            }, { emitEvent: false });

            positionFormGroup.patchValue({
              ratePerUnit: currentLicenseItem['revenue'] || 0,
              originalRatePerUnit: currentLicenseItem['revenue']
            })

            const matchedMilestone = this.milestoneList.find(milestone => milestone.id === defaultValues['milestone']);

            if (matchedMilestone) {
              positionFormGroup.get('milestoneDetails')?.patchValue([matchedMilestone], { emitEvent: false });
            }

          this.calculatePositionRevenue(positionFormGroup);

          this.calculateOverallGMPercentage(positionFormGroup);

          this.getPositionQuantity(positionFormGroup);

          this.resolveSDMapping(servicesFormGroup, positionFormGroup);

          positionFormArr.push(positionFormGroup);

        }

        this.calculateOverallRevenue();

        this.calculateOverallCost(); 

        this.patchMilestoneDetailsUsingForm();

      }
 

    });

  }

  /**
   * @description Performs drag and drop between position array items
   * @param event 
   * @param serviceIndex 
   */
  dropPosition = (event, serviceIndex) => {

    if (event.previousContainer == event.container) {

      let formArray = this.servicesFormArr.at(serviceIndex).get('positions') as FormArray;

      this.changeFormArrayOrder(formArray, event.previousIndex, event.currentIndex);

    }

    else {

      if (this.initialServiceDragIndex != null) {

        let sourceFormArray = this.servicesFormArr.at(this.initialServiceDragIndex).get('positions') as FormArray;

        let targetFormArray = this.servicesFormArr.at(serviceIndex).get('positions') as FormArray;

        this.resolveSDMapping(this.servicesFormArr.at(serviceIndex), sourceFormArray.at(event.previousIndex) as FormGroup);

        targetFormArray.insert(event.currentIndex, sourceFormArray.at(event.previousIndex));

        sourceFormArray.removeAt(event.previousIndex);

      }

    }

    this.initialServiceDragIndex = null;

  }

  /**
   * @description Performs drag and drop between service array items
   * @param event 
   */
  dropService = (event) => {

    this.changeFormArrayOrder(this.servicesFormArr, event.previousIndex, event.currentIndex);

  }

  /**
   * @description Changes element position in the formArray
   * @param formArray 
   * @param from 
   * @param to 
   */
  changeFormArrayOrder = (formArray: FormArray, from: number, to: number) => {

    const previous = formArray.at(from);
    const current = formArray.at(to);

    formArray.setControl(to, previous);
    formArray.setControl(from, current);

  }

  /**
   * @description Sets the initial drag position
   * @param serviceIndex 
   */
  onPositionDragStart = (serviceIndex) => {

    this.initialServiceDragIndex = serviceIndex;

  }

  /**
   * @description Initializes discount and tax form array
   */
  initDiscountAndTax = () => {

    let discountFormArr = this.quoteForm.get('discounts') as FormArray;

    discountFormArr.push(this.getDiscountsFormGroup());

    let taxFormArr = this.quoteForm.get('taxes') as FormArray;

    taxFormArr.push(this.getTaxesFormGroup());

    setTimeout(() => {

      this.scrollToBottom();

    }, 10);

  }

  /**
   * @description Returns the discounts form group
   * @returns FormGroup
   */
  getDiscountsFormGroup = (): FormGroup => {

    const discountsFormGroup = this.fb.group({
      dtItemId: [null],
      discountId: [null],
      discountName: [null],
      discountPercentage: [{ value: null, disabled: true }],
      discountValue: [0],
      isCustom: [false]
    });

    this.valueChangeSubscription.add(discountsFormGroup.get('discountId').valueChanges.pipe(debounceTime(10), takeUntil(this._onDestroy)).subscribe(res => {

      if (res) {

        const currentDiscount = this.discountsList.find(val => val['id'] === res);

        discountsFormGroup.get('discountName').patchValue(currentDiscount['name'] || null, { emitEvent: false });

        discountsFormGroup.get('discountPercentage').patchValue(currentDiscount['percentage'] || 0);

      }

      else
        discountsFormGroup.get('discountPercentage').patchValue(0);

    }));

    this.valueChangeSubscription.add(discountsFormGroup.get('isCustom').valueChanges.pipe(debounceTime(10), takeUntil(this._onDestroy)).subscribe(res => {

      discountsFormGroup.get('discountId').reset();
      discountsFormGroup.get('discountName').reset();
      discountsFormGroup.get('discountPercentage').reset();

      if (res)
        discountsFormGroup.get('discountPercentage').enable({ emitEvent: false });

      else
        discountsFormGroup.get('discountPercentage').disable({ emitEvent: false });

    }));

    let isInternalUpdate = false;

    this.valueChangeSubscription.add(
      discountsFormGroup.get('discountPercentage').valueChanges.pipe(
        debounceTime(100),
        takeUntil(this._onDestroy)
      ).subscribe((res) => {
        if (isInternalUpdate) return;

        let parsedValue = parseFloat(res);
        // if (isNaN(parsedValue)) {
        //   parsedValue = 0;
        // }

        const control = discountsFormGroup.get('discountPercentage');
        const formArray = this.quoteForm.get('discounts') as FormArray;

        const totalOtherDiscounts = formArray.controls.reduce((sum, group) => {
          if (group !== discountsFormGroup) {
            const val = parseFloat(group.get('discountPercentage').value);
            return sum + (isNaN(val) ? 0 : val);
          }
          return sum;
        }, 0);

        const maxAllowed = Math.max(0, 100 - totalOtherDiscounts);

        if (parsedValue < 0 || isNaN(parsedValue)) {
          // this._toaster.showWarning("Discount cannot be negative.", "", this.opportunityService.longInterval);
          isInternalUpdate = true;
          setTimeout(() => {
            control.patchValue(0, { emitEvent: false });
            isInternalUpdate = false;
          });
          // return;
        } else {
          control.patchValue(parsedValue, { emitEvent: false });
        }

        if (parsedValue > maxAllowed) {
          this._toaster.showWarning(
            `Only up to ${maxAllowed}% can be applied to this discount to keep the total under 100%.`,
            "",
            this.opportunityService.longInterval
          );
          isInternalUpdate = true;
          setTimeout(() => {
            control.setValue(maxAllowed);
            isInternalUpdate = false;
          });
          return;
        }

        this.setDiscountEdited(discountsFormGroup);
        this.calculateTotalDiscount(discountsFormGroup);
      })
    );


    // this.valueChangeSubscription.add(discountsFormGroup.get('discountPercentage').valueChanges.pipe(
    //   distinctUntilChanged(),
    //   debounceTime(100),
    //   takeUntil(this._onDestroy)
    // ).subscribe(res => {

    //   if (this.isEditMode && (res < 0 || res === "" || res === null)) 
    //     return discountsFormGroup.get('discountPercentage').patchValue(0);
      

    //   if (this.isEditMode && res > 100) {
    //     discountsFormGroup.get('discountPercentage').patchValue(100);
    //     return this._toaster.showWarning("Discount Percentage cannot be greater than 100", "");
    //   }

    //   const formArray = this.quoteForm.get('discounts') as FormArray;

    //   const totalDiscount = formArray.controls.reduce((sum, formGroup) => {
    //     const value = formGroup.get('discountPercentage').value;
    //     return sum + (value ? parseFloat(value) : 0);
    //   }, 0);

    //   if (totalDiscount > 100) {
    //     discountsFormGroup.get('discountPercentage').patchValue(totalDiscount - 100, { emitEvent: false });
    //     return this._toaster.showWarning(`Only up to ${totalDiscount - 100}% can be applied to this discount to keep the total under 100%.`, "");
    //   }

    //   if (this.isEditMode && res >= 0) {
    //     discountsFormGroup.get('discountPercentage').patchValue(parseFloat(res), { emitEvent: false });

    //     this.setDiscountEdited(discountsFormGroup);
    //     this.calculateTotalDiscount(discountsFormGroup);
    //   }

    // }));
    

    this.valueChangeSubscription.add(discountsFormGroup.get('discountValue').valueChanges.pipe(debounceTime(100), takeUntil(this._onDestroy)).subscribe(res => {

      this.calculateOverallDiscount();

    }));

    return discountsFormGroup;

  }

  /**
   * @description Calculates the total discount for a quote
   */
  calculateTotalDiscount = (discountForm?) => {

    // this.setDiscountEdited(discountForm);

    for (const discountItem of (this.quoteForm.get('discounts') as FormArray).controls)
      discountItem.get('discountValue').patchValue(0, { emitEvent: false });

    for (const serviceItem of this.servicesFormArr.controls)
      for (const positionItem of (serviceItem.get('positions') as FormArray).controls) {

        this.calculatePositionRatePerUnit(positionItem as FormGroup, true);
        // this.calculatePositionRevenue(positionItem as FormGroup, true);

        for (const nmpItem of (positionItem.get('nmpData') as FormArray).controls)
          this.calculatePositionRatePerUnit(nmpItem as FormGroup, true);
        // this.calculatePositionRevenue(nmpItem as FormGroup, true);

      }

    this.calculateOverallDiscount();

    // this.calculateTotalTax();

    this.calculateOverallRevenue();

    let updateFromDiscount = this.wholeQuoteData?.change_request_merged?.length &&
      this.wholeQuoteData?.discountAndTax.length &&
      this.quoteForm.get('quoteId').value && this.quoteForm.get('totalOrderValue').value != this.wholeQuoteData?.quote_amount;

    // updateFromDiscount && this.saveQuote();

  }

  /**
 * @description Applies discount to a position's ratePerUnit, and tracks original rate and applied discounts (NOT USED)
 * @param positionFormGroup - The form group representing a position
 * @param discountPercentage - Discount percentage to apply
 */
  applyDiscountToPosition(positionFormGroup: FormGroup, totalDiscountPercentage: number) {

    if (!positionFormGroup.contains('originalRatePerUnit')) {
      positionFormGroup.addControl('originalRatePerUnit', this.fb.control(null));
    }

    let originalRate = positionFormGroup.get('originalRatePerUnit')?.value;
    const ratePerUnit = positionFormGroup.get('ratePerUnit').value;

    if (!originalRate) {
      originalRate = ratePerUnit;
      positionFormGroup.get('originalRatePerUnit')?.setValue(originalRate, { emitEvent: false });
    }

    const discountedRate = originalRate - (originalRate * (totalDiscountPercentage / 100));
    positionFormGroup.get('ratePerUnit').patchValue(discountedRate, { emitEvent: false });
  }

/**
 * @description Removes position form group from the form array and recalculates discount/tax
 * @param itemIndex - The index of the discount/tax to remove
 * @param formArrayName - The form array name
 */
  deleteDiscountAndTax = (itemIndex: number, formArrayName: string) => {

    const formArray = this.quoteForm.get(formArrayName) as FormArray;

    const removedFormGroup = formArray.at(itemIndex) as FormGroup;

    (this.quoteForm.get(formArrayName) as FormArray).removeAt(itemIndex);

    const hasMatchingItemInWholeData = this.wholeQuoteData ? this.wholeQuoteData?.discountAndTax.some(wholeDataItem =>
      wholeDataItem.dt_item_id === removedFormGroup.get('dtItemId').value
    ) : false;

    if (formArrayName === 'discounts') {
      // if (hasMatchingItemInWholeData) {
      //   this._toaster.showWarning("Removing this discount will not have any impact on the rate and revenue!", "");
      // } else {
        this.calculateTotalDiscount();
      // }
    } else if (formArrayName === 'taxes') {
      this.calculateTotalTax();
    }

  };

  /**
 * @description Removes a specific discount from the discount form array and recalculates the ratePerUnit. (NOT USED)
 * @param itemIndex - The index of the discount to be removed
 * @usage If need to delete the summed up value of the discounts - applyAllDiscountsToPositions()
 * @param formArrayName - The name of the form array
 */
  removeDiscount = (itemIndex: number, formArrayName: string) => {
    (this.quoteForm.get(formArrayName) as FormArray).removeAt(itemIndex);

    if (this.wholeQuoteData.discountAndTax.length > 0 &&
      this.wholeQuoteData.discountAndTax.some(item => item.type === "T")) {
        this._toaster.showWarning("Removing this discount will not have any impact on the rate!", "", this.opportunityService.longInterval)
    }

    if (itemIndex > -1) {
      this.applyAllDiscountsToPositions();
    }
  }

  isQuoteFullyDiscounted(): boolean {
  const discountsArray = this.quoteForm.get('discounts') as FormArray;
  if (!discountsArray?.length) return false;

  let cumulativeMultiplier = 1;

  discountsArray.controls.forEach((discountCtrl: AbstractControl) => {
    const discountPercent = +discountCtrl.get('discountPercentage')?.value || 0;
    cumulativeMultiplier *= (1 - discountPercent / 100);
  });

  return cumulativeMultiplier === 0;
}



  calculateOverallDiscount = () => {

    // Case - 1: If need to sum up all the discounts and then apply to the Position rate
    // let totalDiscount = 0;

    // for (const discountItem of (this.quoteForm.get('discounts') as FormArray).value)
    //   totalDiscount += discountItem['discountValue'];

    // this.quoteForm.get('totalDiscount').patchValue(totalDiscount);

    // Case - 2: If need to apply the discount percentage from Total Order Value

    // let totalOrderValue = Number(this.quoteForm.get('totalOrderValue')?.value || 0);

    // const discountsArray = this.quoteForm.get('discounts') as FormArray;

    // if (discountsArray?.length > 0) {
    //   const firstDiscountGroup = discountsArray.at(0) as FormGroup;
    //   const discountPercentage = Number(firstDiscountGroup.get('discountPercentage')?.value || 0);

    //   if (discountPercentage > 0 && discountPercentage < 100) {
    //     const originalAmount = totalOrderValue / (1 - discountPercentage / 100);
    //     const discountAmount = originalAmount - totalOrderValue;

    //     this.quoteForm.get('totalDiscount')?.patchValue(discountAmount);
    //   } 
    //   else this.quoteForm.get('totalDiscount')?.patchValue(0);
    // }

    // Case - 3 : If need to apply the discount percentage from Total Order Value for multiple Discounts.

    // const totalOrderValue = this.quoteForm.get('totalOrderValue')?.value;
    // const discountsArray = this.quoteForm.get('discounts') as FormArray;

    // if (!totalOrderValue || !discountsArray?.length) {
    //   this.quoteForm.get('totalDiscount')?.patchValue(0);
    //   return;
    // }

    // let cumulativeMultiplier = 1;

    // discountsArray.controls.forEach((discountCtrl: AbstractControl) => {
    //   const discountPercent = +discountCtrl.get('discountPercentage')?.value || 0;
    //   cumulativeMultiplier *= (1 - discountPercent / 100);
    // });

    // const originalAmount = totalOrderValue / cumulativeMultiplier;
    // const totalDiscount = originalAmount - totalOrderValue;

    // this.quoteForm.get('totalDiscount')?.patchValue(totalDiscount);

    // Case - 4 : To Calculte the Discounted Value at each discount for their respective discount percentage
    const totalOrderValue = this.quoteForm.get('totalOrderValue')?.value;
    const discountsArray = this.quoteForm.get('discounts') as FormArray;

    if (!discountsArray?.length) {
      this.quoteForm.get('totalDiscount')?.patchValue(0);
      return;
    }

    // Step 1: Compute cumulative multiplier
    let cumulativeMultiplier = 1;

    discountsArray.controls.forEach((discountCtrl: AbstractControl) => {
      const discountPercent = +discountCtrl.get('discountPercentage')?.value || 0;
      cumulativeMultiplier *= (1 - discountPercent / 100);
    });

    // Step 2: Calculate original amount and total discount
    const originalAmount = totalOrderValue / cumulativeMultiplier;
    let remainingAmount = originalAmount;
    let totalDiscount = 0;

    // Step @: If Discount is 100% then Calculate Total Discount with Original Rate instead of Rate per Unit.
    if (cumulativeMultiplier === 0) {
      discountsArray.controls.forEach((discountCtrl: AbstractControl) => {
        const discountPercent = +discountCtrl.get('discountPercentage')?.value || 0;
        const value = totalOrderValue * (discountPercent / 100);
        discountCtrl.get('discountValue')?.patchValue(value, { emitEvent: false });
      });

      // Calculate original totalOrderValue using originalRate
      let originalTotalRevenue = 0;

      for (const serviceItem of this.servicesFormArr.value) {
        for (const positionItem of serviceItem['positions']) {
          const noOfResources = positionItem['noOfResources'] || 1;
          const quantity = positionItem['quantity'] || 1;
          const rate = positionItem['originalRatePerUnit'] || 0;

          originalTotalRevenue += noOfResources * quantity * rate;

          for (const nmpItem of positionItem['nmpData']) {
            const nmpNoOfResources = nmpItem['noOfResources'] || 1;
            const nmpQuantity = nmpItem['quantity'] || 1;
            const nmpRate = nmpItem['originalRatePerUnit'] || 0;

            originalTotalRevenue += nmpNoOfResources * nmpQuantity * nmpRate;
          }
        }
      }

      const totalTax = +this.quoteForm.get('totalTax')?.value || 0;
      const originalTotalOrderValue = originalTotalRevenue + totalTax;

      const totalDiscountPercent = discountsArray.controls.reduce((acc, discountCtrl: AbstractControl) => {
        return acc + (+discountCtrl.get('discountPercentage')?.value || 0);
      }, 0);

      if (totalDiscountPercent > 0) {
        discountsArray.controls.forEach((discountCtrl: AbstractControl) => {
          const discountPercent = +discountCtrl.get('discountPercentage')?.value || 0;
          const value = (discountPercent / totalDiscountPercent) * originalTotalOrderValue;
          discountCtrl.get('discountValue')?.patchValue(value, { emitEvent: false });
        });
      }

      this.quoteForm.get('totalDiscount')?.patchValue(originalTotalOrderValue);
      return;
    }

    const totalDiscountPercent = discountsArray.controls.reduce((acc, discountCtrl: AbstractControl) => {
      return acc + (+discountCtrl.get('discountPercentage')?.value || 0);
    }, 0);
    
    // Step 3: Calculate and patch individual discountValues
    discountsArray.controls.forEach((discountCtrl: AbstractControl) => {
      const discountPercent = +discountCtrl.get('discountPercentage')?.value || 0;
      const discountValue = remainingAmount * (discountPercent / 100);
      discountCtrl.get('discountValue')?.patchValue(discountValue, { emitEvent: false });

      remainingAmount -= discountValue;
      totalDiscount += discountValue;
    });

    this.quoteForm.get('totalDiscount')?.patchValue(totalDiscount);
  }

  /**
   * @description Returns the taxes form group
   * @returns FormGroup
   */
  getTaxesFormGroup = (): FormGroup => {

    const taxesFormGroup = this.fb.group({
      dtItemId: [null],
      taxId: [null],
      taxName: [null],
      taxPercentage: [{ value: null, disabled: true }],
      taxValue: [0]
    });

    this.valueChangeSubscription.add(taxesFormGroup.get('taxId').valueChanges.pipe(debounceTime(10), takeUntil(this._onDestroy)).subscribe(res => {

      if (res) {

        const currentTax = this.taxesList.find(val => val['id'] === res);

        taxesFormGroup.get('taxName').patchValue(currentTax['name'] || null, { emitEvent: false });

        taxesFormGroup.get('taxPercentage').patchValue(currentTax['tax_percentage'] || 0);

      }

      else
        taxesFormGroup.get('taxPercentage').patchValue(0);


    }));

    this.valueChangeSubscription.add(taxesFormGroup.get('taxPercentage').valueChanges.pipe(debounceTime(100), takeUntil(this._onDestroy)).subscribe(res => {

      const quoteRevenue = this.quoteForm.get('totalRevenue').value;

      const taxValue = this.convertToFixed(((res || 0) / 100) * quoteRevenue);

      taxesFormGroup.get('taxValue').patchValue(taxValue);

      this.calculateTotalTax();

    }));

    return taxesFormGroup;

  }

  /**
   * @description Calculates the total tax for a quote
   */
  calculateTotalTax = () => {

    let totalTax = 0;

    for (const taxItem of (this.quoteForm.get('taxes') as FormArray).value)
      totalTax += taxItem['taxValue'];

    this.quoteForm.get('totalTax').patchValue(totalTax);

    this.calculateTotalDiscount();

    this.calculateOverallRevenue();

  }

  /**
   * @description Adds a new position form group to the form array
   * @param itemIndex 
   * @param formArrayName
   */
  addDiscountAndTax(itemIndex: number, formArrayName: string) {
    const formArray = this.quoteForm.get(formArrayName) as FormArray;
    const newFormGroup = formArrayName === 'discounts' ? this.getDiscountsFormGroup() : this.getTaxesFormGroup();
    formArray.insert(itemIndex + 1, newFormGroup);

  }

  /**
 * @description Applies the total discount percentage from all discounts to each position's rate per unit across all services.
 * @usage If Need to Sum up all the discounts and then Apply to the Positon rate
 */
  applyAllDiscountsToPositions() {
    const discountFormArray = this.quoteForm.get('discounts') as FormArray;

    this.servicesFormArr.controls.forEach(serviceItem => {

      const positionsFormArray = serviceItem.get('positions') as FormArray;

      positionsFormArray.controls.forEach(positionItem => {

        const totalDiscountPercentage = discountFormArray.controls
          .map(discount => discount.get('discountPercentage').value || 0)
          .reduce((acc, value) => acc + value, 0);

        this.applyDiscountToPosition(positionItem as FormGroup, totalDiscountPercentage);
        
      });
    });
  }

  /**
   * @description Toggles the custom discount button
   * @param discountIndex 
   */
  toggleCustomDiscount = (discountIndex) => {

    let discountFormGroup = (this.quoteForm.get('discounts') as FormArray).at(discountIndex);

    discountFormGroup.get('isCustom').patchValue(!discountFormGroup.get('isCustom').value);

  }

  /**
   * @description Scrolls the container to the bottom
   */
  scrollToBottom() {
    this.scrollContainer.scroll({
      top: this.scrollContainer.scrollHeight,
      left: 0,
      behavior: 'smooth'
    });
  }

  /**
  * 
  * @param triggerField 
  * @param positionIndex 
  * @description Opens overlay for Hours updation
  */
  openCostOverlay(triggerField: HTMLElement) {

    if (!this.costOverlayRef?.hasAttached()) {

      const positionStrategyBuilder = this.overlay.position();

      const positionStrategy = positionStrategyBuilder
        .flexibleConnectedTo(triggerField)
        .withFlexibleDimensions(true)
        .withPush(true)
        .withViewportMargin(25)
        .withGrowAfterOpen(true)
        .withPositions([
          {          
            originX: 'center',
            originY: 'bottom',
            overlayX: 'center',
            overlayY: 'bottom',
            offsetX: 0,
            offsetY: -triggerField.offsetHeight, 
          }
        ]);

      const scrollStrategy = this.overlay.scrollStrategies.close();

      this.costOverlayRef = this.overlay.create({
        positionStrategy,
        scrollStrategy,
        hasBackdrop: true,
        backdropClass: 'quote-transparent-overlay-backdrop'
      });

      const templatePortal = new TemplatePortal(this.costTemplateRef, this.viewContainerRef);

      this.costOverlayRef.attach(templatePortal);

      this.costOverlayRef.backdropClick().subscribe(() => {
        this.closeCostOverlay();
      });

    }

  }

  closeCostOverlay() {

    this.costOverlayRef?.dispose();

    this.costSummary = {
      resourceCost: 0,
      nonManpowerCost: 0,
      total: 0
    };

  }

  /**
  * 
  * @param triggerField 
  * @description Opens overlay for Hours updation
  */
  openRevSummaryOverlay(triggerField: HTMLElement) {

    if (!this.revOverlayRef?.hasAttached()) {

      const positionStrategyBuilder = this.overlay.position();

      const positionStrategy = positionStrategyBuilder
        .flexibleConnectedTo(triggerField)
        .withFlexibleDimensions(true)
        .withPush(true)
        .withViewportMargin(25)
        .withGrowAfterOpen(true)
        .withPositions([
          {
            originX: 'center',
            originY: 'bottom',
            overlayX: 'center',
            overlayY: 'bottom',
            offsetX: 0,
            offsetY: -triggerField.offsetHeight, 
          }
        ]);

      const scrollStrategy = this.overlay.scrollStrategies.close();

      this.revOverlayRef = this.overlay.create({
        positionStrategy, 
        scrollStrategy,
        hasBackdrop: true,
        backdropClass: 'quote-transparent-overlay-backdrop'
      });

      const templatePortal = new TemplatePortal(this.revTemplateRef, this.viewContainerRef);

      this.revOverlayRef.attach(templatePortal);

      this.revOverlayRef.backdropClick().subscribe(() => {
        this.closeRevOverlay();
      });

    }

  }

  closeRevOverlay() {

    this.revOverlayRef?.dispose();

    this.revenueSummary = {
      revenue: 0,
      cost: 0,
      grossMargin: 0
    };

  }

  /**
  * @description Opens the Customize Fields Overlay
  */
  openCustomizeDialog = async () => {

    if (this.customizeFields.length) {

      const { CustomizeFieldDialogComponent } = await import('../components/customize-field-dialog/customize-field-dialog.component');

      const openCustomizeFieldDialog = this.dialog.open(CustomizeFieldDialogComponent, {
        height: 'auto',
        width: '25vw',
        data: {
          customizeFields: this.customizeFields
        },
        disableClose: true
      });

      openCustomizeFieldDialog.afterClosed().subscribe(val => {

        let fieldConfig = [];

        for (let i = 0; i < this.customizeFields.length; i++)
          fieldConfig.push({
            field_key: this.resolveFieldMapping(this.customizeFields[i]['key']),
            field_position: i,
            is_visible: this.customizeFields[i]['isVisible'].value
          });

        this._quoteMainService.updateUserFieldConfig(fieldConfig)
          .pipe(takeUntil(this._onDestroy))
          .subscribe(res => {

            if (res["messType"] == "E")
              this._toaster.showError("Error in Updating Field Config", res["messText"], this.opportunityService.longInterval);

          },
          err => {
            console.log(err);
            this._toaster.showError("Error", "Error in Updating Field Config", this.opportunityService.longInterval);
          });

      });

    }

  }


  /**
   * @description Opens the position effort dialog
   * @param positionFormGroup 
   * @param positionIndex 
   * @param positionLevel 
   */
  openPositionEffort = async (positionFormGroup: FormGroup, positionIndex: number, positionLevel: number) => {

    if (positionFormGroup.get('unit').value) {

      const positionData = {
        quoteId: this.quoteForm.get('quoteId').value,
        quotePositionId: positionFormGroup.get('quotePositionId').value,
        positionName: positionFormGroup.get('positionName').value,
        startDate: positionFormGroup.get('startDate').value,
        endDate: positionFormGroup.get('endDate').value,
        unit: positionFormGroup.get('unit').value,
        quantity: positionFormGroup.get('quantity').value,
        isQuantityChanged: positionFormGroup.get('isQuantityChanged').value,
        isUnitChanged: positionFormGroup.get('isUnitChanged').value,
        resourceTypeId: positionFormGroup.get('isNonManpower').value ? 2 : positionFormGroup.get('isLicense').value ? 3 : 1,
        positionLevel: positionLevel,
        quoteType: this.quoteForm.get('quoteType').value
      };

      if (positionFormGroup.contains('positionEffortData'))
        positionData['positionEffortData'] = positionFormGroup.get('positionEffortData').value;

      const { PositionEffortDialogComponent } = await import('../components/position-effort-dialog/position-effort-dialog.component');

      const OpenPositionEffortDialogComponent = this.dialog.open(PositionEffortDialogComponent, {
        height: '73%',
        width: '50%',
        data: {
          positionData: positionData,
          unitList: this.unitList.concat(this.otherUnitList),
          isEditMode: this.isEditMode,
          // calendarId: this.calendarId || 1,
          calendarId: positionFormGroup.get('calendarId').value,
          workScheduleId: this.workScheduleId || 1,
          deliveryStartDate: this.quoteForm.get('deliveryStartDate').value,
          deliveryEndDate: this.quoteForm.get('deliveryEndDate').value,
          quoteType: this.quoteForm.get('quoteType').value
        },
        disableClose: true
      });
      console.log(positionFormGroup.get('calendarId').value, 'Effort Calendar ID')
      OpenPositionEffortDialogComponent.afterClosed().subscribe(async (res: any) => {

        if (res && res.data) {

          const positionDetails = res['data'];

          if (positionDetails['startDate'])
            positionFormGroup.get('startDate').patchValue(positionDetails['startDate']);

          if (positionDetails['endDate'])
            positionFormGroup.get('endDate').patchValue(positionDetails['endDate']);

          if (positionDetails.hasOwnProperty('quantity')) {

            positionFormGroup.get('quantity').patchValue(positionDetails['quantity'], { emitEvent: false });

            this.calculatePositionRevenue(positionFormGroup);

            this.calculatePositionCost(positionFormGroup);

          }

          if (positionDetails['positionEffortData']) {

            if (!positionFormGroup.contains('positionEffortData'))
              positionFormGroup.addControl('positionEffortData', this.fb.control(null));

            positionFormGroup.patchValue({
              positionEffortData: positionDetails['positionEffortData'],
              isQuantityChanged: false,
              isPositionEffortChanged: true,
              isUnitChanged: false
            });

          }

        }

      });

    }

    else
      this._toaster.showWarning("Unit not found", "Kindly Select Unit before proceeding", this.opportunityService.longInterval);

  }

  // @HostListener('window:beforeunload',['$event'])
  // showMessage($event) {
  //    $event.returnValue='Your data will be lost!';
  // }

  /**
  * @description Opens the Quote Activity log component
  */
  openActivityLog = async () => {

    const { QuoteActivityLogComponent } = await import('../components/quote-activity-log/quote-activity-log.component');

    this.dialog.open(QuoteActivityLogComponent, {
      height: '100vh',
      width: '35vw',
      data: {
        quoteId: this.quoteId,
        showQuoteValue: this.checkFieldShouldbeRestricted('totalRevenue')
      },
      position: {
        right: '0'
      },
      disableClose: false
    });

  }

  resolveQuoteOpportunityIntegration = (quoteDetails?) => {

    return new Promise((resolve, reject) => {
      
      this._quoteMainService.resolveQuoteOppIntegration(this.opportunityId, null, 1, true, quoteDetails)
      .pipe(takeUntil(this._onDestroy))
      .subscribe(res => {

        if (res['messType'] == "S") {

            if (res['quoteMessageType']) {

              if (res['quoteMessageType'] == 'info')
                this._toaster.showInfo("Quote Info", res['messText'], this.opportunityService.longInterval);

              else if (res['quoteMessageType'] == 'warning')
                this._toaster.showWarning("Quote Warning", res['messText'], this.opportunityService.longInterval);

              else if (res['quoteMessageType'] == 'error')
                this._toaster.showError("Quote Error", res['messText'], this.opportunityService.longInterval);

            }

            if (res.hasOwnProperty('showConfirmationPopup') && res['showConfirmationPopup'] == 1 && res['quoteId'])
              this._util.openConfirmationSweetAlertWithCustom("Copy Quote Value?", "Do you wish to copy Quote value to Opportunity ?")
                .then((copyConfirm) => {

                  if (copyConfirm)
                    this._quoteMainService.updateValueInOpportunity(this.opportunityId, res['quoteId'], quoteDetails)
                      .pipe(takeUntil(this._onDestroy))
                      .subscribe(res => {

                        if (res['messType'] == "S")
                          this._toaster.showInfo("Value Updated", res["messText"], this.opportunityService.longInterval);

                        else
                          this._toaster.showError("Error in Updating", res["messText"], this.opportunityService.longInterval);

                        resolve(true);

                      },
                        err => {
                          console.log(err);
                          this._toaster.showError("Error", "Error in Updating Quote value in Opportunity", this.opportunityService.mediumInterval);
                          reject(false);
                        });

                  else
                    resolve(true);

              });

            else
              resolve(true);
            
        }

        else {

          this._toaster.showError("Error", res["messText"], this.opportunityService.mediumInterval);

          resolve(true);

        }

      },
        err => {
          console.log(err);
          this._toaster.showError("Error", "Error in Checking Quote Configuration", this.opportunityService.mediumInterval);
          reject(false);
        });

    });

  }

  /**
   * @description Resolves and returns mapped field
   * @param fieldName 
   */
  resolveFieldMapping = (fieldName: string) => {

    for (const fieldItem of this.customizeFields) {

      if (fieldItem['key'] === fieldName)
        return fieldItem['columnName'];

      if (fieldItem['columnName'] === fieldName)
        return fieldItem['key'];

    }

    return fieldName;

  }

    /**
   * @description Resolves and returns calendar mapped field
   * @param fieldName 
   */
  resolveCalendarFieldMapping = (fieldName: string) => {

    const fieldMapping = {
      "entity": "entity",
      "work_location": "workLocation",
      "service_type_id": "serviceTypeId"
    };
    // console.log(fieldMapping, "Calendar Combination");

    if (fieldMapping[fieldName]) {
      return fieldMapping[fieldName];
    }

    return fieldName;

  }

  /**
   * @description Gets the position rate from rate card
   * @param {FormGroup} positionFormGroup 
   */
  getPositionRate = (positionFormGroup: FormGroup) => {

    const rateFieldDetails = {
      position: positionFormGroup.get('positionId').value
    };

    for (const mandatoryField of this.mandatoryFields) {

      const positionValue = positionFormGroup.get(this.resolveFieldMapping(mandatoryField)).value;

      if (positionValue == null || positionValue == "")
        return true;

      rateFieldDetails[mandatoryField] = positionValue;

    }

    this._quoteMainService.getRate(rateFieldDetails, this.opportunityId, this.defaultCurrency, this.quoteForm.get('quoteCurrency').value, this.conversionTypeId)
      .pipe(takeUntil(this._onDestroy))
      .subscribe(res => {

        if (res["messType"] == "S" && res["data"] && res['data'].length) {

          const rateCard = res['data'][0];

          const patchObject = {
            entity: rateCard['entity'],
            division: rateCard['division'],
            subDivision: rateCard['sub_division'],
            experience: rateCard['work_experience'],
            workLocation: rateCard['work_location'],
            nationality: rateCard['nationality'],
            quantity: 1
          };

          for (const mandatoryField of this.mandatoryFields)
            delete patchObject[this.resolveFieldMapping(mandatoryField)];

          positionFormGroup.patchValue(patchObject, { emitEvent: false });

          const valuePatch = {
            ratePerUnit: rateCard['revenue'],
            originalRatePerUnit: rateCard['revenue'],
            costPerUnit: rateCard['cost'],
            rcValue: rateCard['revenue']
          };

          if (rateCard['unit'] != null)
            valuePatch['unit'] = rateCard['unit'];

          positionFormGroup.patchValue(valuePatch);

        }

        else {

          positionFormGroup.patchValue({
            ratePerUnit: 0,
            costPerUnit: 0,
            originalRatePerUnit: 0,
            rcValue: 0
          });

          this._toaster.showWarning("Rate card not found !", "Rate, Cost has been reset", this.opportunityService.longInterval);

        }

      },
       err => {
        console.log(err);
        this._toaster.showError("Error", "Error in fetching rate card", this.opportunityService.mediumInterval);
       });

  }
  
  /**
   * @description Gets the 100% position quantity for the unit
   * @param {FormGroup} positionFormGroup 
   */
  getPositionQuantity = (positionFormGroup: FormGroup) => {

    const startDate = positionFormGroup.get('startDate').value;
    const endDate = positionFormGroup.get('endDate').value;
    const calendarId = positionFormGroup.get('calendarId').value;

    const unitConfig = this.unitList.concat(this.otherUnitList).find(val => val['id'] === positionFormGroup.get('unit').value);

    if (!unitConfig)
      return true;

    this._quoteMainService.getQuantityForPosition(startDate, endDate, unitConfig, calendarId, this.workScheduleId)
      .pipe(takeUntil(this._onDestroy))
      .subscribe(res => {

        if (res["messType"] == "S" && res["data"] != null)
          positionFormGroup.patchValue({
            quantity: res['data']
          });

      },
        err => {
          console.log(err);
          this._toaster.showError("Error", "Error in fetching quantity for position", this.opportunityService.mediumInterval);
        });

  }

  /**
  * 
  * @param triggerField 
  * @description Opens overlay for Quote name edit
  */
  openQuoteOverlay(triggerField) {

    if (this.isEditMode && !this.quoteOverlayRef?.hasAttached()) {

      this.quoteNameFormControl.patchValue(this.quoteForm.get('quoteName').value);

      const positionStrategyBuilder = this.overlay.position();

      const positionStrategy = positionStrategyBuilder
        .flexibleConnectedTo(triggerField)
        .withFlexibleDimensions(true)
        .withPush(true)
        .withViewportMargin(25)
        .withGrowAfterOpen(true)
        .withPositions([
          {
            originX: 'start',
            originY: 'bottom',
            overlayX: 'start',
            overlayY: 'top'
          }
        ]);

      const scrollStrategy = this.overlay.scrollStrategies.close();

      this.quoteOverlayRef = this.overlay.create({
        positionStrategy,
        scrollStrategy,
        hasBackdrop: true,
        backdropClass: 'quote-transparent-overlay-backdrop'
      });

      const templatePortal = new TemplatePortal(this.quoteTemplateRef, this.viewContainerRef);

      this.quoteOverlayRef.attach(templatePortal);

      this.quoteOverlayRef.backdropClick().subscribe(() => {
        this.closeQuoteOverlay();
      });

    }

  }

  closeQuoteOverlay() {
    this.quoteOverlayRef?.dispose();
  }

  changeQuoteName() {

    const quoteName = this.quoteNameFormControl.value.trim();

    if (!quoteName)
      return this._toaster.showError("Mandatory Field Error", "Kindly enter Quote Name", this.opportunityService.mediumInterval);
    
    this.quoteForm.get('quoteName').patchValue(quoteName);

    this.closeQuoteOverlay();

  }

  getUserFieldConfig() {

    this._quoteMainService.getUserFieldConfig()
      .pipe(takeUntil(this._onDestroy))
      .subscribe(res => {

        if (res["messType"] == "S") {

          if (res["data"]["field_config"] && res["data"]["field_config"] instanceof Array) {
            
            for (let i = 0; i < this.customizeFields.length; i++) {

              const currentField = res["data"]["field_config"].find(val => val['field_key'] === this.resolveFieldMapping(this.customizeFields[i]['key']));

              if (currentField) {

                this.customizeFields[i]['isVisible'].patchValue(currentField['is_visible'] || false);

                this.customizeFields[i]['fieldPosition'] = currentField['field_position'];

              }

            }

            this.sortFieldConfig();

          }

        }

      },
       err => {
        console.log(err);
        this._toaster.showError("Error", "Error in getting Field config details", this.opportunityService.longInterval);
       });

  }

   /**
   * @description Deletes a Quote
   * @param quote 
   */
  deleteQuote = () => {

    let projectIntegratedCheck = this.projectDetailsForOpportunity &&
      this.projectDetailsForOpportunity['messType'] == "S" &&
      this.projectDetailsForOpportunity['messData']?.length > 0;

    if (this.isQuoteActive || this.isChangeRequestActive) {
      if (projectIntegratedCheck) {
        this._toaster.showWarning("Cannot Delete Active Quote", "Project has already been created for the Opportunity!", this.opportunityService.longInterval);
      } else {
        this._toaster.showWarning("Warning!", "Cannot Delete Active Quote", this.opportunityService.longInterval);
      }
      return;
    }

    // if (projectIntegratedCheck) {
    //   this._toaster.showWarning("Cannot Delete Quote", "Project has already been created for the Opportunity!", this.opportunityService.longInterval);
    //   return;
    // }

    if (this.quoteId && this.isCUDEnabled)
      this._util.openConfirmationSweetAlertWithCustom("Are you sure ?", `Do you want to delete ${this.quoteForm.get('quoteName').value} Quote`)
        .then((deleteConfirm) => {

          if (deleteConfirm)
            this._quoteMainService.deleteQuote(this.quoteId, this.opportunityId)
              .pipe(takeUntil(this._onDestroy))
              .subscribe(res => {

                if (res && res['messType'] == "S") {

                  this._toaster.showSuccess("Success", "Quote deleted Successfully !", this.opportunityService.longInterval);

                  this.navigateToLandingPage()

                }

                else
                  this._toaster.showError("Error", res["messText"], this.opportunityService.longInterval);

              },
                err => {
                  console.log(err);
                  this._toaster.showError("Error", "Error in deleting Quote", this.opportunityService.mediumInterval);
                });

        });

  }

  /**
   * @description Cancels changes made in a quote
   */
  cancelChanges = async () => {

    if (!this.isChangesMade)
      return this.isEditMode = false;

    if (this.quoteId) {

      this.toggleSpinner(true);

      await this.getQuoteDetails();

      this.alloteFieldConfig();

      this.patchQuoteDetails(this.wholeQuoteData);

      this.patchMilestoneDetailsUsingForm();

      this.toggleSpinner(false);

    }

  }

  /**
   * @description Updates division, sub division master data based on Org mapping
   * @param changeType 
   * @param data 
   * @param positionFormGroup 
   */
  resolveOrgMapping(changeType, data, positionFormGroup: FormGroup) {

    if (!this.wEMappingEnabled && !this.sDMappingEnabled) {

      if (changeType == 'entity') {

        if (data['entity'] == null) {

          positionFormGroup.get('division').patchValue(null, { emitEvent: false });
          positionFormGroup.get('subDivision').patchValue(null, { emitEvent: false });

          positionFormGroup.get('divisionMasterData').patchValue(this.divisionList, { emitEvent: false });
          positionFormGroup.get('subDivisionMasterData').patchValue(this.subDivisionList, { emitEvent: false });

        }

        else {

          if (positionFormGroup.get('division').disabled)
            positionFormGroup.get('division').enable({ emitEvent: false });

          const currOrgMapping = this.orgMappingList.filter(val => val['entity_id'] === data['entity']);

          const divisionIds = currOrgMapping.map(val => val['division_id']);
          const subDivisionIds = currOrgMapping.map(val => val['sub_division_id']);

          positionFormGroup.get('divisionMasterData').patchValue(this.divisionList.filter(val => divisionIds.includes(val['id'])), { emitEvent: false });
          positionFormGroup.get('subDivisionMasterData').patchValue(this.subDivisionList.filter(val => subDivisionIds.includes(val['id'])), { emitEvent: false });

          if (data['division'] != null && !divisionIds.includes(data['division']))
            positionFormGroup.get('division').patchValue(null);

          if (data['subDivision'] != null && !subDivisionIds.includes(data['subDivision']))
            positionFormGroup.get('subDivision').patchValue(null);

        }

      }

      else if (changeType == 'division') {

        if (data['division'] == null) {

          positionFormGroup.get('subDivision').patchValue(null, { emitEvent: false });

          positionFormGroup.get('subDivisionMasterData').patchValue(this.subDivisionList, { emitEvent: false });

        }

        else {

          if (positionFormGroup.get('subDivision').disabled)
            positionFormGroup.get('subDivision').enable({ emitEvent: false });

          const currOrgMapping = this.orgMappingList.filter(val => val['division_id'] === data['division'] 
          && (data['entity'] != null ? val['entity_id'] == data['entity'] : true));

          const subDivisionIds = currOrgMapping.map(val => val['sub_division_id']);

          positionFormGroup.get('subDivisionMasterData').patchValue(this.subDivisionList.filter(val => subDivisionIds.includes(val['id'])), { emitEvent: false });

          if (data['subDivision'] != null && !subDivisionIds.includes(data['subDivision']))
            positionFormGroup.get('subDivision').patchValue(null);

        }

      }

    }

  }

  /**
  * @description Removes Service to the form array
  * @param serviceItem 
  */
  deleteService = (serviceIndex: number) => {

    const serviceItem = this.servicesFormArr.at(serviceIndex);

    if (serviceItem.get('quoteServiceId').value != null)
      this._util.openConfirmationSweetAlertWithCustom("Are you Sure ?", `Do you want to delete this ${serviceItem.get('serviceId').value ? 'Service' : 'Section'}`)
        .then((deleteConfirm) => {

          if (deleteConfirm) {

            this.servicesFormArr.removeAt(serviceIndex);

            this.calculateOverallRevenue();

            this.calculateOverallCost();

            this.calculateOverallGM();

          }

        });

    else {

      this.servicesFormArr.removeAt(serviceIndex);

      this.calculateOverallRevenue();

      this.calculateOverallCost();

      this.calculateOverallGM();

    }

  }

  /**
   * @description Gets Current Position Data
   * @param positionFormGroup 
   * @param positionId 
   * @returns currentPositionData
   */
  getCurrentPositionData = (positionFormGroup: FormGroup, positionId) => {

    const masterData = positionFormGroup.get('isNonManpower').value ? this.nmpList : positionFormGroup.get('isLicense').value ? this.licenseList : this.positionList;

    const currentPositionData = masterData.find(val => val['id'] === positionId);

    positionFormGroup.get('positionName').patchValue(currentPositionData ? currentPositionData['name'] : null);

    return currentPositionData;

  }

  /**
   * @description Resolves Service Division Mapping
   * @param serviceItem 
   * @param positionFormGroup 
   */
  resolveSDMapping = (serviceItem: AbstractControl, positionFormGroup: FormGroup) => {

    if (this.sDMappingEnabled && this.sDMappingList.length) {

      const serviceId = serviceItem.get('serviceId').value;

      if (serviceId != null) {

        const currMapping = this.sDMappingList.find(val => val['service_id'] === serviceId);

        positionFormGroup.get('division').patchValue(currMapping ? currMapping['division_id'] : null, { emitEvent: false });
        
      } 

      if (!serviceId || serviceId == null) {
        const newAddedPositionFieldConfig = this.newAddedPostionAfterProjectInterationConfig(this.newly_added_quote_project_integration_config);
        positionFormGroup.patchValue({
          ...newAddedPositionFieldConfig
        })
      }
        

    }

  }

  /**
 * @description Validates Work Location Entity Mapping with quote data
 * @param formValue 
 * @returns Boolean
 */
  validateSalesRegionGeoMapping = (formValue) => {

    const revRegField = this.customizeFields.find(val => val['key'] === 'revenueRegion');
    const eField = this.customizeFields.find(val => val['key'] === 'entity');

    for (const serviceItem of formValue['services'])
      for (const positionItem of serviceItem['positions']) {

        if (positionItem['revenueRegion']) {

          const currMapping = this.revenueRegionMapping.find(config => 
            config.sales_region_id === (this.wholeQuoteData?.sales_region || this.oppQuoteDetails.sales_region) && config.legal_entity_id === positionItem['entity']);

          if (currMapping && currMapping['revenue_region_id'] != positionItem['revenueRegion']) {

            this._toaster.showError(`${revRegField ? revRegField['label'] : 'Geographical Region'} <-> ${eField ? eField['label'] : 'Entity'} <-> Sales Region Mapping is Invalid`, `${revRegField ? revRegField['label'] : 'Geographical Region'} Selected for Position - ${positionItem['positionName']} doesn't match with ${eField ? eField['label'] : 'Entity'} <-> Sales Region Mapped.`, 3000);

            return false;

          }

        }

        if (positionItem['nmpData'] && positionItem['nmpData'].length)
          for (const nmpItem of positionItem['nmpData'])
            if (nmpItem['revenueRegion']) {

              const currMapping = this.revenueRegionMapping.find(config => 
                config.sales_region_id === (this.wholeQuoteData?.sales_region || this.oppQuoteDetails.sales_region) && config.legal_entity_id === nmpItem['entity']);

              if (currMapping && currMapping['revenue_region_id'] != nmpItem['revenueRegion']) {

                this._toaster.showError(`${revRegField ? revRegField['label'] : 'Geographical Region'} <-> ${eField ? eField['label'] : 'Entity'} <-> Sales Region Mapping is Invalid`, `${revRegField ? revRegField['label'] : 'Geographical Region'} Selected for Position - ${positionItem['positionName']} doesn't match with ${eField ? eField['label'] : 'Entity'} <-> Sales Region Mapped.`, 3000);
    
                return false;
    
              }

            }

      }

    return true;

  }

  /**
   * @description Validates Work Location Entity Mapping with quote data
   * @param formValue 
   * @returns Boolean
   */
  validateWEMapping = (formValue) => {

    const wField = this.customizeFields.find(val => val['key'] === 'workLocation');
    const eField = this.customizeFields.find(val => val['key'] === 'entity');

    for (const serviceItem of formValue['services'])
      for (const positionItem of serviceItem['positions']) {

        if (positionItem['work_location']) {

          const currMapping = this.wEMappingList.find(val => val['work_location_id'] === positionItem['work_location']);

          if (currMapping)
            if (currMapping['entity_id'] != positionItem['entity']) {

              this._toaster.showError(`${wField ? wField['label'] : 'Work Location'} <-> ${eField ? eField['label'] : 'Entity'} Mapping is Invalid`, `${eField ? eField['label'] : 'Entity'} Selected for Position - ${positionItem['positionName']} doesn't match with ${wField ? wField['label'] : 'Work Location'} Mapped (Kindly remove and Position)`, this.opportunityService.longInterval);

              return false;

            }

        }

        if (positionItem['nmpData'] && positionItem['nmpData'].length)
          for (const nmpItem of positionItem['nmpData'])
            if (nmpItem['work_location']) {
    
              const currMapping = this.wEMappingList.find(val => val['work_location_id'] === nmpItem['work_location']);
    
              if (currMapping)
                if (currMapping['entity_id'] != nmpItem['entity']) {
    
                  this._toaster.showError(`${wField ? wField['label'] : 'Work Location'} <-> ${eField ? eField['label'] : 'Entity'} Mapping is Invalid`, `${eField ? eField['label'] : 'Entity'} Selected for Position - ${positionItem['positionName']} doesn't match with ${wField ? wField['label'] : 'Work Location'} Mapped (Kindly remove and Position)`, this.opportunityService.longInterval);

                  return false;
    
                }
    
            }

      }

    return true;

  }

  /**
   * @description Validates Service Division Mapping with quote data
   * @param formValue 
   * @returns 
   */
  validateSDMapping = (formValue) => {

    const dField = this.customizeFields.find(val => val['key'] === 'division');

    for (const serviceItem of formValue['services']) {

      if (serviceItem['serviceId'] == null) {

        this._toaster.showError(`Service <-> ${dField ? dField['label'] : 'Division'} Mapping is Invalid`, `Kindly remove Section - ${serviceItem['serviceName']} as Service <-> ${dField ? dField['label'] : 'Division'} Mapping is Enabled !`, this.opportunityService.longInterval);

        return false;

      }

      const currMapping = this.sDMappingList.find(val => val['service_id'] === serviceItem['serviceId']);

      if (currMapping)
        for (const positionItem of serviceItem['positions']) {

          if (currMapping['division_id'] != positionItem['division']) {

            this._toaster.showError(`Service <-> ${dField ? dField['label'] : 'Division'} Mapping is Invalid`, `${dField ? dField['label'] : 'Division'} Selected for Position - ${positionItem['positionName']} doesn't match with Service Mapped (Kindly remove and Position)`, this.opportunityService.longInterval);

            return false;

          }

          if (positionItem['nmpData'] && positionItem['nmpData'].length)
            for (const nmpItem of positionItem['nmpData'])
              if (currMapping['division_id'] != nmpItem['division']) {
      
                this._toaster.showError(`Service <-> ${dField ? dField['label'] : 'Division'} Mapping is Invalid`, `${dField ? dField['label'] : 'Division'} Selected for Position - ${nmpItem['positionName']} doesn't match with Service Mapped (Kindly remove and Position)`, this.opportunityService.longInterval);
  
                return false;
      
              }

        }

    }
   
    return true;
    
  }

  /**
 * @description Gets the calendar data for the duration
 */
  getCalendarDataForTheDuration = async (positionFormGroup: FormGroup, lineItemData: any) => {
    let startDate = positionFormGroup.get('startDate').value;
    let endDate = positionFormGroup.get('endDate').value;
  
    const unitConfig = this.unitList.concat(this.otherUnitList).find(val => val['id'] === positionFormGroup.get('unit').value);
  
    if (startDate && endDate) {
      if (unitConfig && unitConfig['unit_value_in_hrs'] > 24 && !unitConfig['is_value_fixed']) {
        startDate = moment(startDate).startOf('month').toISOString();
        endDate = moment(endDate).endOf('month').toISOString();
      }
  
      const positionDetails = {
        quoteId: this.quoteId,
        quotePositionId: lineItemData.quotePositionId,
        resourceTypeId: positionFormGroup.get('isNonManpower').value ? 2 : positionFormGroup.get('isLicense').value ? 3 : 1,
        positionLevel: 1
      };
  
      let calendarId = this.calendarId;
      if (positionFormGroup.contains('calendarId')) {
        calendarId = positionFormGroup.get('calendarId').value || this.calendarId;
      }
  
      try {
        const res = await this._quoteMainService
          .getQuoteCalendarData(startDate, endDate, positionDetails, true, calendarId, this.workScheduleId)
          .toPromise(); 
  
        // Handle the response
        if (res['messType'] === 'S' && res['data'] && res['data'].length) {
          // this.durationData = res['data'];
          return res['data'] || [];
        } else {
          this._toaster.showError('Error', res['messText'] || 'Error in getting Quote Calendar details', 4500);
          return false;
        }
      } catch (err) {
        console.error(err);
        this._toaster.showError('Error', 'Error in getting Quote Calendar details', 4500);
        return false; 
      }
    }
    return [];
  };

  getDurationInDays = (startDate, endDate) => {

    let days = 0;

    if (moment(startDate).isValid() && moment(endDate).isValid())
      days = moment(endDate).diff(startDate, 'days');

    return days;

  }
  
  selectCurrency(currency: { id: number; name: string, currency_id }) {

    const currentCurrency = this.quoteForm.get('quoteCurrency').value;

    if (currentCurrency === currency.name)
      return

    this.quoteForm.patchValue({ quoteCurrency: currency.name }, { emitEvent: true });

    this.quoteForm.patchValue({ quoteCurrencyId: currency.currency_id });

    for (const serviceItem of this.servicesFormArr.controls)

      for (const positionItem of (serviceItem.get("positions") as FormArray).controls) {

        positionItem.get("isPositionValueChanged").patchValue(true, { emitEvent: false });

        for (const nmpItem of (positionItem.get("nmpData") as FormArray).controls) {

          nmpItem.get("isPositionValueChanged").patchValue(true, { emitEvent: false });

        }

      }

  }

  async openApproversPopUp(quoteDetails, type?) {

    if (type === 'SEND')
      await this.getQuoteApprovers(quoteDetails);

    if (type === 'SEE')
      await this.getQuoteStatus(quoteDetails)

    let dialogConfig = {
      width: '30rem',
      minHeight: '40vh',
      data: { type: 'SUBMIT', quoteDetails: quoteDetails, popUpType: type },
      animation: {
        entryAnimation: {
          keyframes: [
            { transform: 'scale(0.5) translateY(100%)', opacity: 0 },
            { transform: 'scale(1) translateY(0)', opacity: 1 },
          ],
          keyframeAnimationOptions: {
            duration: 250,
            easing: 'ease-in-out',
          },
        },
        exitAnimation: {
          keyframes: [
            { transform: 'scale(1) translateY(0)', opacity: 1 },
            { transform: 'scale(0.5) translateY(100%)', opacity: 0 },
          ],
          keyframeAnimationOptions: {
            duration: 250,
            easing: 'ease-in-out',
          },
        },
      },
      disableClose: true,
    };


    this.submitQuoteForApprovalRef = this.dialog.open(this.submitQuoteForApprovalTemplate, dialogConfig);

  }

  async getQuoteApprovers(quoteDetails) {
    this.miniLoader = true;
    let details = {
      quoteDetails: quoteDetails
    };

    this._landingPageService.getQuoteApprovers(details).subscribe(
      (res: any) => {
        console.log('Approvers:', res);
        this.reviewersList = res?.data;
        // this.commentToSubmitter = this.reviewersList?.comments;
        if (this.reviewersList.length == 0)
          this._toaster.showWarning('No Approvers Found for the selected type!', '', this.opportunityService.longInterval)
        this.miniLoader = false;
      },
      (error) => {
        console.error('Error fetching approvers:', error);
        this.miniLoader = false;
      }
    );
  }

  async getQuoteStatus(quoteDetails: any): Promise<void> {
    this.miniLoader = true;
  
    try {
      let details = {
        quote_id: quoteDetails.quote_header_id
      };
  
      const res: any = await new Promise((resolve, reject) => {
        this._landingPageService.getQuoteStatus(details).subscribe(
          (response) => resolve(response),
          (error) => reject(error)
        );
      });
  
      console.log('Approvers:', res);
      let approverList = res.data.approvers
      this.submitterDetails = res?.data.submitter[0];
      this.reviewersList = res?.data.approvers;
      const approver = approverList.find(reviewer => reviewer.isApprover === true);
      // this.commentToSubmitter = approver?.comments || ' ';
      const comments = this.reviewersList.find(reviewer => reviewer.comments)?.comments || ' ';
      this.commentToSubmitter = comments || ' ';
  
      if (this.submitterDetails.length === 0) {
        this._toaster.showWarning('No Approvers Found for the selected type!', '', this.opportunityService.longInterval);
      }
  
    } catch (error) {
      console.error('Error fetching approvers:', error);
    } finally {
      this.miniLoader = false;
    }
  }

  async openApproverDialog(quoteDetails: any) {

    await this.getQuoteStatus(quoteDetails)

    if (!this.isCUDEnabled && quoteDetails?.quote_status === 2 && quoteDetails?.quote_status === 3) {
      this.openApproversPopUp(quoteDetails, 'SEE')
    }

    let submitterDetails = this.submitterDetails;
    let approverDetails = this.reviewersList;

    let dialogConfig = {
      width: '35rem',
      minHeight: '45vh',
      data: { quoteDetails: quoteDetails, submitterDetails: submitterDetails, approverDetails: approverDetails  },
      disableClose: true,
      panelClass: 'custom-mat-dialog-panel',
    };

    this.approveOrRejectRef = this.dialog.open(this.approveOrRejectTemplate, dialogConfig);

    const result = await this.approveOrRejectRef.afterClosed().toPromise();

    if (result === 'CANCEL')
      return

    if (result === 'APPROVE') {
      let details = {
        workflowId: quoteDetails.approval_workflow_header_id,
        approval_status: 'A',
        comments: this.commentToSubmitter,
        quoteDetails: quoteDetails
      };

      try {
        const res: any = await this._landingPageService.approveOrReject(details).toPromise();
        console.log('Approvers:', res);
        if (res) {
          this.miniLoader = false;
          this._toaster.showSuccess('Quote Approved!', '', this.opportunityService.shortInterval);
          // this.updateActiveQuote(quoteDetails);
        }
      } catch (error) {
        console.error('Error approving quote:', error);
        let err = error?.error?.err;
        if (err?.code === "WORKFLOW_TRIGGERED" && err?.err) {
          this._toaster.showWarning('Cannot Approve Quote!', 'Approval process already completed', this.opportunityService.longInterval);
        } else if (err?.code !== "WORKFLOW_TRIGGERED" && err?.err) {
          this._toaster.showWarning('Error approving quote!', err?.msg || 'Kindly try again after sometime', this.opportunityService.mediumInterval);
        } else {
          this._toaster.showError('Error approving quote!', 'Kindly try again after sometime', this.opportunityService.mediumInterval);
        }
        this.miniLoader = false;
      }
    }

    if (result === 'REJECT') {
      let details = {
        workflowId: quoteDetails.approval_workflow_header_id,
        approval_status: 'R',
        comments: this.commentToSubmitter,
        quoteDetails: quoteDetails
      };

      try {
        const res: any = await this._landingPageService.approveOrReject(details).toPromise();
        console.log('Approvers:', res);
        if (res) {
          this.miniLoader = false;
          this._toaster.showSuccess('Quote Rejected!', '', this.opportunityService.shortInterval);
        }
      } catch (error) {
        console.error('Error rejecting quote:', error);
        let err = error?.error?.err;
        if (err?.code === "WORKFLOW_TRIGGERED" && err?.err) {
          this._toaster.showWarning('Cannot Reject Quote!', 'Approval process already completed', this.opportunityService.longInterval);
        } else if (err?.code !== "WORKFLOW_TRIGGERED" && err?.err) {
          this._toaster.showWarning('Error rejecting quote!', err?.msg || 'Kindly try again after sometime', this.opportunityService.mediumInterval);
        } else {
          this._toaster.showError('Error rejecting quote!', 'Kindly try again after sometime', this.opportunityService.mediumInterval);
        }
        this.miniLoader = false;
      }
    }

    // await this._landingPageService.getQuoteDetails(this.opportunityId);
  }

  getStatusCss(status: number): any {
    switch (status) {
      case 4:
        return { 'background': '#E8E9EE', 'color': '#45546E' };
      case 1:
        return { 'background': '#FFF3E8', 'color': '#FA8C16' };
      case 2:
        return { 'background': '#EEF9E8', 'color': '#52C41A' };
      case 3:
        return { 'background': '#FFEBEC', 'color': '#FF3A46' };
      default:
        return {};
    }
  }

  async closeApproverPopUp(quote, action) {
    console.log(quote)
    if (action === 'APPROVE') {
      this.approveOrRejectRef.close(action);
    }
    else if (action === 'REJECT') {
      if (!this.commentToSubmitter || this.commentToSubmitter.trim() === '') {
        this._toaster.showWarning('Please enter comment before rejecting approval!', '', this.opportunityService.mediumInterval);
        return;
      }
      this.approveOrRejectRef.close(action);
    }
    else
      this.approveOrRejectRef.close('CANCEL');
  }

  async closeSubmitApprovalPopUp(quote, action) {
    console.log(quote)
    // if (action === 'SUBMIT') {
    //   await this.sendForApproval(quote);

    //   await this._landingPageService.getQuoteDetails(this.opportunityId);
    // }

    this.submitQuoteForApprovalRef.close();
  }

  getApproverTooltip(approver: any): string {
    return `${approver?.aid || approver?.associate_id} - ${approver.employee_name || ''}\nStatus: ${approver.status_name}`;
  }

  getCalendarCombinationConfig = () => {

    return new Promise((resolve, reject) => {

      this._quoteMainService.getCalendarCombinationConfig()
        .pipe(takeUntil(this._onDestroy))
        .subscribe(res => {

          if (res && res['messType'] == "S" && res['data']) {
            this.calendarCombinationConfig = res['data'];
            // console.log(this.calendarCombinationConfig, 'calendarCombinationConfig')
          }

          else {
            this._toaster.showError("Error", "Error in getting Quote calendar combination config ", this.opportunityService.longInterval);

          }

          resolve(true);

        },
          err => {
            console.log(err);
            this._toaster.showError("Error", "Error in getting Quote calendar combination config ", this.opportunityService.longInterval);
            this.navigateToLandingPage();
            reject(err);
          });

    });

  }

    /**
   * @description Gets the Revenue Region/Geographical Region/Financial PL Region Mapping Comnination
   */
  getRevenueRegionCombinationConfig = () => {

    return new Promise((resolve, reject) => {

      this._quoteMainService.getRevenueRegionCombinationConfig()
        .pipe(takeUntil(this._onDestroy))
        .subscribe(res => {

          if (res && res['messType'] == "S" && res['data'])
            this.revenueRegionMapping = res['data'];

          else
            this._toaster.showError("Error", "Error in getting Quote Geographical Region config ", 3000);

          resolve(true);

        },
          err => {
            console.log(err);
            this._toaster.showError("Error", "Error in getting Quote Geographical Region config ", 3000);
            this.navigateToLandingPage();
            reject(err);
          });

    });

  }

  /**
   * @description Gets the opportunity meta details for quote
   */
  getOppMetaDetails = () => {

    return new Promise((resolve, reject) => {

      this._quoteMainService.getOpportunityMetaDetailsForQuote(this.opportunityId)
        .pipe(takeUntil(this._onDestroy))
        .subscribe(res => {

          if (res && res['messType'] == 'S' && res['data'])
            this.oppQuoteDetails = res['data'];

          resolve(true);

        },
          err => {
            console.log(err);
            this._toaster.showError("Error", "Error in getting Quote Opportunity Meta details", 3000);
            reject(err);
          });

    });

  }

  getMilestoneListQB = () => {

    return new Promise((resolve, reject) => {

      let quotePositionIds: number[] = [];

      for (const serviceItem of this.servicesFormArr.controls) {
        const positions = serviceItem.get("positions") as FormArray;
      
        for (const positionItem of positions.controls) {
          const quotePositionId = positionItem.get("quotePositionId")?.value;
          if (quotePositionId) {
            quotePositionIds.push(quotePositionId);
          }
        }
      }



      this._quoteMainService.getMilestoneListQB(quotePositionIds)
        .pipe(takeUntil(this._onDestroy))
        .subscribe(res => {

          if (res && res['messType'] == "S" && res['data']) {
            this.milestoneList = res['data'];

            if (this.milestoneApplicable) {
              let x = this.customizeFields.find(field => field.key == 'milestone');
              x['masterData'] = this.milestoneList
            }
          }

          else
            this._toaster.showError("", "Error in getting Milestone Data", this.opportunityService.longInterval);


          resolve(true);

        },
          err => {
            console.log(err);
            this._toaster.showError("", "Error in getting Milestone Data", this.opportunityService.longInterval);
            this.navigateToLandingPage();
            reject(err);
          });

    });

  }

  checkMilestoneApplicable = async () => {

    return new Promise((resolve, reject) => {

       this._quoteMainService.checkMilestoneApplicable(null, this.quoteId)
        .pipe(takeUntil(this._onDestroy))
        .subscribe(async res => {

          if (res && res['messType'] == "S") {
            this.milestoneApplicable = 
            this.isQuoteCreateMode && this.lumpsum_quote_config?.service_type?.includes(this.quoteForm.get('serviceOppTypeId').value) 
            ? true 
            : res['data'];        
            // if(this.isQuoteCreateMode)
            //   await this.getMilestoneListQB();
        }
        
          resolve(true);

        },
          err => {
            console.log(err);
            this._toaster.showError("Error", "Error in getting Quote calendar combination config ", this.opportunityService.longInterval);
            this.navigateToLandingPage();
            reject(err);
          });

    });

  }


  /**
  * @description Get all quote detail in a single object 
  * @param positionFormGroup 
  * @param positionIndex 
  * @param positionLevel
  * @param skillList,
  * @param servicesFormArr,
  * @param calendarId,
  * @param opportunityId,
  * @param workScheduleId,
  * @param defaultCurrency,
  * @param conversionTypeId,
  * @param initial_delivery_start_date,
  * @param initial_delivery_end_date
  */
  getAllQuoteData(formValue, skillList, servicesFormArr, calendarId, opportunityId, workScheduleId, defaultCurrency, conversionTypeId, initial_delivery_start_date, initial_delivery_end_date) {

    let quoteDetails = {};
    let services = [];
    let discountAndTax = { items: [] };

    //Validation

    // if (this.quoteForm.status == "VALID") {

      // Loop over services
      for (const [serviceIndex, serviceItem] of formValue['services'].entries()) {
        let positions = [];

        // Loop over positions
        for (const [positionIndex, positionItem] of serviceItem['positions'].entries()) {
          if (!positionItem['isLicense'] && !positionItem['isNonManpower']) {

            const currentPositionData = skillList.find(val => val['id'] == positionItem['positionId']);

            if (!currentPositionData) {
              const positions = servicesFormArr.at(serviceIndex).get('positions') as FormArray;

              positions.at(positionIndex).patchValue({
                positionId: null,
                positionName: null
              }, { emitEvent: false });

              this._toaster.showError("Mandatory Field Error", "Kindly Select Valid Position", this.opportunityService.longInterval);
              return { isEditMode: true };
            }
          }

          let positionItems = [];

          // If NMP Data is present
          if (positionItem['nmpData'] && positionItem['nmpData'].length) {
            for (const nmpItem of positionItem['nmpData']) {
              let isPositionEffortChanged = nmpItem['isPositionEffortChanged'] || nmpItem['isQuantityChanged'] || false;

              positionItems.push({
                qp_item_id: nmpItem['quotePositionId'],
                nmp_id: nmpItem['positionId'],
                name: nmpItem['positionName'] || null,
                no_of_resource: nmpItem['noOfResources'],
                rate_per_unit: nmpItem['ratePerUnit'],
                original_rate: nmpItem['originalRatePerUnit'],
                cost_per_unit: nmpItem['costPerUnit'],
                entity: nmpItem['entity'],
                division: nmpItem['division'],
                sub_division: nmpItem['subDivision'],
                work_location: nmpItem['workLocation'],
                nationality: nmpItem['nationality'],
                unit_id: nmpItem['unit'],
                quantity: nmpItem['quantity'],
                resource_type_id: 2,
                qpi_currency: formValue['quoteCurrency'],
                qpi_amount: nmpItem['totalCost'],
                qpi_revenue_amount: nmpItem['totalRevenue'],
                start_date: nmpItem['startDate'],
                end_date: nmpItem['endDate'],
                position_effort: nmpItem['positionEffortData'] || null,
                is_position_effort_changed: isPositionEffortChanged,
                is_position_value_changed: nmpItem['isPositionValueChanged'],
                rc_value: nmpItem['rcValue'],
                business_type: nmpItem['typeOfBusiness'],
                milestone_id: nmpItem['milestone']
              });
            }
          }

          let isPositionEffortChanged = positionItem['isPositionEffortChanged'] || positionItem['isQuantityChanged'] || false;

          positions.push({
            quote_position_id: positionItem['quotePositionId'],
            position: positionItem['positionId'],
            position_name: positionItem['positionName'] || null,
            resource_count: positionItem['noOfResources'],
            rate_per_unit: positionItem['ratePerUnit'],
            original_rate: positionItem['originalRatePerUnit'],
            cost_per_unit: positionItem['costPerUnit'],
            work_experience: positionItem['experience'],
            nationality: positionItem['nationality'],
            work_location: positionItem['workLocation'],
            entity: positionItem['entity'],
            division: positionItem['division'],
            sub_division: positionItem['subDivision'],
            unit_id: positionItem['unit'],
            quantity: positionItem['quantity'],
            resource_type_id: positionItem['isNonManpower'] ? 2 : positionItem['isLicense'] ? 3 : 1,
            item_currency: formValue['quoteCurrency'],
            item_revenue_amount: positionItem['totalRevenue'],
            item_cost_amount: positionItem['totalCost'],
            start_date: positionItem['startDate'],
            end_date: positionItem['endDate'],
            position_order: positionIndex,
            position_items: positionItems,
            position_effort: positionItem['positionEffortData'] || null,
            is_position_effort_changed: isPositionEffortChanged,
            is_position_value_changed: positionItem['isPositionValueChanged'],
            rc_value: positionItem['rcValue'],
            business_type: positionItem['typeOfBusiness'],
            calendar_id: positionItem['calendarId'] || calendarId,
            milestone_id: positionItem['milestone'],
            milestone: positionItem['milestoneDetails']
          });
        }

        services.push({
          quote_service_id: serviceItem['quoteServiceId'],
          service_header_id: serviceItem['serviceId'],
          service_name: serviceItem['serviceName'],
          service_type_id: serviceItem['serviceTypeId'] || null,
          service_revenue_amount: serviceItem['serviceRevenue'],
          service_cost_amount: serviceItem['serviceCost'],
          service_currency: formValue['quoteCurrency'],
          is_fixed_rate: serviceItem['isFixedRate'] || null,
          service_order: serviceIndex,
          position: positions
        });
      }

      // Process discounts
      for (const discountItem of formValue['discounts']) {
        discountAndTax['items'].push({
          dt_item_id: discountItem['dtItemId'],
          id: discountItem['discountId'],
          name: discountItem['discountName'],
          percentage: discountItem['discountPercentage'],
          type: "D",
          tax_type: null,
          item_amount: discountItem['discountValue'],
          item_currency: formValue['quoteCurrency'],
          is_custom: discountItem['isCustom']
        });
      }

      // Process taxes
      for (const taxItem of formValue['taxes']) {
        discountAndTax['items'].push({
          dt_item_id: taxItem['dtItemId'],
          id: taxItem['taxId'],
          name: taxItem['taxName'],
          percentage: taxItem['taxPercentage'],
          type: "T",
          tax_type: null,
          item_amount: taxItem['taxValue'],
          item_currency: formValue['quoteCurrency'],
          is_custom: false
        });
      }

      // Set quote details
      quoteDetails = {
        quote_name: formValue['quoteName'],
        quote_currency: formValue['quoteCurrency'],
        quote_currency_id: formValue['quoteCurrencyId'],
        initial_currency_id: formValue['initialCurrencyId'] || formValue['quoteCurrencyId'],
        quote_amount: formValue['totalOrderValue'],
        quote_cost_amount: formValue['totalCost'],
        quote_revenue_amount: formValue['totalRevenue'],
        delivery_start_date: formValue['deliveryStartDate'],
        delivery_end_date: formValue['deliveryEndDate'],
        opportunity_id: opportunityId,
        service: services,
        discountAndTax: discountAndTax,
        version: formValue['version'],
        work_schedule_id: workScheduleId || 1,
        default_currency: defaultCurrency,
        conversion_type_id: conversionTypeId,
        initial_delivery_start_date: initial_delivery_start_date,
        initial_delivery_end_date: initial_delivery_end_date,
        is_quote_active: formValue['activeQuote'],
        has_parent_opportunity: formValue['quoteOpportunityHasParent']
      };

      if (formValue['quoteId']) {
        quoteDetails['quote_header_id'] = formValue['quoteId'];
      }

      return quoteDetails;
    // } 
    
    // else {
    //   for (const serviceItem of this.servicesFormArr.controls)
    //     for (const positionItem of (serviceItem.get('positions') as FormArray)['controls'])
    //       if (positionItem.status == "INVALID") {

    //         for (const nmpItem of (positionItem.get('nmpData') as FormArray)['controls'])
    //           if (nmpItem.status == "INVALID")
    //             return this.resolveMandatoryMsg(nmpItem);

    //         return this.resolveMandatoryMsg(positionItem);

    //       }
    // }

  }


  /**
  * @description Opens the position effort dialog
  * @param positionFormGroup 
  * @param positionIndex 
  * @param positionLevel 
  */
  openMilestoneDialog = async () => {

    if (this.quoteForm.status != "VALID") {
      for (const serviceItem of this.servicesFormArr.controls)
        for (const positionItem of (serviceItem.get('positions') as FormArray)['controls'])
          if (positionItem.status == "INVALID") {
            for (const nmpItem of (positionItem.get('nmpData') as FormArray)['controls'])
              if (nmpItem.status == "INVALID")
                return this.resolveMandatoryMsg(nmpItem);
            return this.resolveMandatoryMsg(positionItem);
          }
    }

    this.patchMilestoneDetailsUsingForm();

    const formValue = this.quoteForm.getRawValue();

    // if (this.quoteForm.status != 'VALID')
    //   return this._toaster.showWarning('Quote is not Valid', '')

    let quoteData = this.getAllQuoteData(
      formValue,
      this.skillList,
      this.servicesFormArr,
      this.calendarId,
      this.opportunityId,
      this.workScheduleId,
      this.defaultCurrency,
      this.conversionTypeId,
      this.initial_delivery_start_date,
      this.initial_delivery_end_date
    );

    console.log('quoteData', quoteData)

    const { MilestoneDialogComponent } = await import('../components/milestone-dialog/milestone-dialog.component');

    const OpenPositionEffortDialogComponent = this.dialog.open(MilestoneDialogComponent, {
      minHeight: '60vh',
      minWidth: '65vw',
      data: {
        quoteData: quoteData,
        milestoneFieldConfig: this.milestoneFieldConfig,
        quoteForm: this.quoteForm,
        milestoneFormArray: this.milestoneFormArray.value,
        milestoneFormGroup: this.milestoneFormGroup,
        customizeFields: this.customizeFields,
        mandatoryFields: this.mandatoryFields,
        licenseList: this.licenseList,
        positionList: this.positionList,
        wEMappingEnabled: this.wEMappingEnabled,
        sDMappingEnabled: this.sDMappingEnabled,
        opportunityId: this.opportunityId,
        defaultCurrency: this.defaultCurrency,
        conversionTypeId: this.conversionTypeId,
        nmpList: this.nmpList,
        skillList: this.skillList,
        milestoneList: this.milestoneList,
        positionDropZones: this.positionMilestoneDropZones,
        quoteCurrency: this.quoteForm.get('quoteCurrency').value,
        isEditMode: this.isMilestoneTaggingEnabled ? false : this.isEditMode 
      },
      disableClose: true
    });

    console.log(this.isMilestoneTaggingEnabled, '--')

    OpenPositionEffortDialogComponent.afterClosed().subscribe(async (res: any) => {

      if (res && res.event == 'submit' && res.data) {

        const quoteData = res['data'];
        const updatedMilestones = res['milestoneList'];

        // updatedMilestones.forEach((newMilestone) => {
        //   const existingIndex = this.milestoneList.findIndex(
        //     (milestone) => milestone.id === newMilestone.id
        //   );

        //   if (existingIndex !== -1) {
        //     this.milestoneList[existingIndex] = newMilestone;
        //   } else {
        //     this.milestoneList.push(newMilestone);
        //   }
        // });

        this.milestoneListCopy = [...this.milestoneList];

        updatedMilestones.forEach((newMilestone) => {
          const existingIndex = this.milestoneListCopy.findIndex(
            (milestone) => milestone.id === newMilestone.id
          );

          if (existingIndex !== -1) {
            this.milestoneListCopy[existingIndex] = newMilestone;  
          } else {
            console.log('About to Push')
            this.milestoneListCopy.push(newMilestone);  
          }
        });

        console.log(this.milestoneList, 'OG')
        console.log(this.milestoneListCopy, 'BOG')
        let x = this.customizeFields.find(field => field.key == 'milestone');
        x['masterData'] = []
        this.milestoneList = [];
        this.milestoneList = [...this.milestoneListCopy];
        x['masterData'] = [...this.milestoneListCopy];


        console.log(this.milestoneList, 'After List Change')


        // for (let service of quoteData.service) {
        //   for (let position of service.position) {
        //     if (position.milestone && position.milestone.length > 0) {
        //       for (let milestone of position.milestone) {
        //         const milestoneToUpdate = this.milestoneList.find(milestoneItem => milestoneItem.id === milestone.milestone_id);
                
        //         if (milestoneToUpdate) {
        //           milestoneToUpdate.name = milestone.milestone_name;
        //         }
        //       }
        //     }
        //   }
        // }

        console.log(this.milestoneList)

        
        for (const serviceItem of this.servicesFormArr.controls) {
          for (const positionItem of (serviceItem.get('positions') as FormArray).controls) {
            const positionQuoteId = positionItem.get('quotePositionId')?.value;


            const matchingPosition = quoteData.service
              .flatMap(service => service.position)
              .find(pos => pos.quote_position_id === positionQuoteId);

            if (matchingPosition) {
              if (positionItem.get('milestone')?.value != matchingPosition.milestone_id) {
                positionItem.get('milestone')?.patchValue(null, { emitEvent: false });

                setTimeout(() => {
                  positionItem.get('milestone')?.patchValue(matchingPosition.milestone_id, { emitEvent: false });;
                }, 1000);
              }

              if (positionItem.get('milestoneDetails')?.value != matchingPosition.milestone) {
                positionItem.get('milestoneDetails')?.patchValue(matchingPosition.milestone, { emitEvent: false });
              }

              if (positionItem.get('startDate')?.value != matchingPosition.start_date) {
                positionItem.get('startDate')?.patchValue(matchingPosition.start_date, { emitEvent: false });
              }

              if (positionItem.get('endDate')?.value != matchingPosition.end_date) {
                positionItem.get('endDate')?.patchValue(matchingPosition.end_date, { emitEvent: false });
              }

              if (positionItem.get('isPositionEffortChanged')?.value != matchingPosition.is_position_effort_changed) {
                positionItem.get('isPositionEffortChanged')?.patchValue(matchingPosition.is_position_effort_changed, { emitEvent: false });
              }

              // const nmpDataArray = positionItem.get('nmpData') as FormArray;
              // if (nmpDataArray) {
              //   for (const nmpItem of nmpDataArray.controls) {
              //     if (nmpItem.get('milestone')?.value != matchingPosition.milestone) {
              //       nmpItem.get('milestone')?.patchValue(matchingPosition.milestone, { emitEvent: false });
              //     }

              //     if (nmpItem.get('milestoneDetails')?.value != matchingPosition.milestoneDetails) {
              //       nmpItem.get('milestoneDetails')?.patchValue(matchingPosition.milestoneDetails, { emitEvent: false });
              //     }

              //     if (nmpItem.get('startDate')?.value != matchingPosition.start_date) {
              //       nmpItem.get('startDate')?.patchValue(matchingPosition.start_date, { emitEvent: false });
              //     }

              //     if (nmpItem.get('endDate')?.value != matchingPosition.end_date) {
              //       nmpItem.get('endDate')?.patchValue(matchingPosition.end_date, { emitEvent: false });
              //     }
              //   }
              // }
            }
          }
        }

        // for (let milestones of quoteData) {
        //   for (let position of milestones.positions) {
        //     if (position.milestoneDetails && position.milestoneDetails.length > 0) {
        //       for (let milestone of position.milestoneDetails) {
        //         const milestoneToUpdate = this.milestoneList.find(milestoneItem => milestoneItem.id === milestone.milestone_id);
                
        //         if (milestoneToUpdate) {
        //           milestoneToUpdate.name = milestones.milestoneName;
        //         }
        //       }
        //     }
        //   }
        // }

        // for (const serviceItem of this.servicesFormArr.controls) {
        //   for (const positionItem of (serviceItem.get('positions') as FormArray).controls) {
        //     const quotePositionId = positionItem.get('quotePositionId')?.value;
        
        //     const matchingPosition = quoteData.flatMap(milestone => milestone.positions)
        //       .find(pos => pos.quotePositionId === quotePositionId);
        
        //     if (matchingPosition) {
        
        //       // Milestone
        //       if (positionItem.get('milestone')?.value !== matchingPosition.milestone) {
        //         positionItem.get('milestone')?.patchValue(matchingPosition.milestone, { emitEvent: false });
        //       }
        
        //       // Milestone Details
        //       if (positionItem.get('milestoneDetails')?.value !== matchingPosition.milestoneDetails) {
        //         positionItem.get('milestoneDetails')?.patchValue(matchingPosition.milestoneDetails, { emitEvent: false });
        //       }
        
        //       // Start Date
        //       if (positionItem.get('startDate')?.value !== matchingPosition.startDate) {
        //         positionItem.get('startDate')?.patchValue(matchingPosition.startDate, { emitEvent: false });
        //       }
        
        //       // End Date
        //       if (positionItem.get('endDate')?.value !== matchingPosition.endDate) {
        //         positionItem.get('endDate')?.patchValue(matchingPosition.endDate, { emitEvent: false });
        //       }
        //     }
        //   }
        // }
        

        this.patchMilestoneDetailsUsingForm();
        
      }

    });

  }

  get isMilestoneTaggingEnabled(): boolean {
    const milestoneField = this.customizeFields.find(field => field.key === 'milestone');
    return milestoneField?.has_project_integrated && this.project_access_field_config &&
    this.projectDetailsForOpportunity &&
    this.projectDetailsForOpportunity["messType"] == "S" &&
    this.projectDetailsForOpportunity["messData"]?.length > 0 &&
    this.isQuoteActive
  }

  checkQuoteEditableBasedOnConfig = async(quote_header_id) => {

    this._landingPageService.checkQuoteEditableBasedOnConfig(quote_header_id)
      .pipe(takeUntil(this._onDestroy))
      .subscribe(async res => {
        if(res && !res['allowed']){
       this.editRestricted=!res['allowed']
       this.editRestrictedMsg=res['msg']
        }

      });

  } 
  

  ngOnDestroy() {

    this._onDestroy.next();
    this._onDestroy.complete();

    if (this.valueChangeSubscription)
      this.valueChangeSubscription.unsubscribe();

    if (this.routeDataSubscription)
      this.routeDataSubscription.unsubscribe();

    if (this.quoteCreateDataSubscription)
      this.quoteCreateDataSubscription.unsubscribe();

    this.toggleSpinner(false);

  }

}
