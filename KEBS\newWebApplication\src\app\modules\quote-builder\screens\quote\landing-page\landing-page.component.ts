import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { QouteLandingPageService } from '../../../services/qoute-landing-page.service';
import { debounceTime, take, takeUntil } from 'rxjs/operators';
import { QBMasterDataService } from '../../../services/qb-master-data.service';
import { InitialQuote, QuoteMainService } from '../../../services/quote-main.service';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { FormControl } from '@angular/forms';
import { Subject, Subscription } from 'rxjs';
import * as moment from 'moment';

import { ToasterService } from 'src/app/services/toaster-service/toaster.service';
import { UtilityService } from 'src/app/services/utility/utility.service';
import { NgxSpinnerService } from 'ngx-spinner';
import { LoginService } from 'src/app/services/login/login.service';
import { KebsHelpService } from "kebs-help-application";
import { OpportunityService } from "src/app/modules/opportunities/features/opportunities-detail/services/OpportunityService";
import { TicketService } from 'src/app/modules/ams/services/ticket.service';

@Component({
  selector: 'app-landing-page',
  templateUrl: './landing-page.component.html',
  styleUrls: ['./landing-page.component.scss'],
})
export class LandingPageComponent implements OnInit, OnDestroy {

  constructor(
    private $router: Router,
    private _route: ActivatedRoute,
    public _landingPageService: QouteLandingPageService,
    public masterDataService: QBMasterDataService,
    public _quoteMainService: QuoteMainService,
    private _dialog: MatDialog,
    private _toaster: ToasterService,
    private _util: UtilityService,
    private spinner: NgxSpinnerService,
    private _login: LoginService,
    private _help: KebsHelpService,
    private opportunityService: OpportunityService,
    private _ticket: TicketService
  ) { }

  opportunityId: number;
  initialQuote: InitialQuote = {
    quoteName: '',
    currency: '',
    deliveryStartDate: null,
    deliveryEndDate: null,
    businessType: null,
    serviceTypeId: null,
    quoteType: ''
  };

  oppQuoteDetails = {};

  quoteConfiguration = [];

  oppQuoteList = [];

  limitLoader: boolean = false;
  limit: number = 25;
  offset: number = 0;
  allDataLoaded: boolean = false;
  searchingFlag: boolean = false;
  searchQuery: string = '';
  dialogOpened: boolean = false;

  quoteCurrency = null;

  helpTopicId = null;

  currentUser = null;

  quoteType: string;

  changeRequestEnabled: boolean = false;

  cr_activation_access: boolean = false;

  protected _onDestroy = new Subject<void>();

  searchFormControl = new FormControl('');
  importSearchFormControl = new FormControl('');
  importFormControl = new FormControl('2');

  quoteSearchSubscription = new Subscription();
  importQuoteSearchSubscription = new Subscription();

  showDeleted: boolean = false;
  copyDialogRef: MatDialogRef<any>;
  importDialogRef: MatDialogRef<any>;
  submitQuoteForApprovalRef: MatDialogRef<any>;
  approveOrRejectRef: MatDialogRef<any>;

  isQuoteBeingImported: boolean = false;
  isCUDEnabled: boolean = true;
  loadingContent = "Loading Data...";

  isProjectIntegrated: boolean = true;

  ifUserHasApprovals: any = {};

  allow_approval: boolean = false;

  miniLoader: boolean = false;

  reviewersList: any = [];

  hasToSendApproval: boolean = false;

  commentToSubmitter: string = '';
  
  submitterDetails: any = [];
  
  enableChangeRequest: boolean = false;

  allowMilestoneOpportunityStatus: any = [];

  CR_ENABLED_OBJECT: boolean = false;

  isValidServiceType: boolean = false;

  has_parent_opportunity: boolean = false;
  cr_activation_in_progress=false;

  quoteList: any;

  fieldConfig: any = [];

  @ViewChild('copyQuoteDialog')
  private copyQuoteTemplate: TemplateRef<any>;

  @ViewChild('importQuoteDialog')
  private importQuoteTemplate: TemplateRef<any>;

  @ViewChild('submitForApproval')
  private submitQuoteForApprovalTemplate: TemplateRef<any>;

  @ViewChild('approveOrReject')
  private approveOrRejectTemplate: TemplateRef<any>;

  ngOnInit(): void {

    this.currentUser = this._login.getProfile().profile;

    this.commentToSubmitter = '';

    this.allow_approval = this._landingPageService.checkQuoteActivateAccess();

    this._landingPageService.quotesList.subscribe(quotes => {
      this.quoteList = quotes;
      this.ifUserHasApprovals = quotes.find(quote => quote.isApprover && quote.quote_status === 1) || false;
  });

  this.limit = 25;
  this.offset = 0;
  
    this._route.data.pipe(take(1)).subscribe(async ({ opportunity }) => {

      this.opportunityId = parseInt(opportunity['opportunityId']);

      await this.getQuoteConfiguration();

      this.cr_activation_access = this._landingPageService.checkCRActivateAccess();

      this.getQuoteAccessPrivilege();

      await this.getOppMetaDetails();

      await this._landingPageService.getQuoteDetails(this.opportunityId);

      this.getOppQuoteList();

    });

    this.quoteSearchSubscription = this.searchFormControl.valueChanges.pipe(debounceTime(500), takeUntil(this._onDestroy)).subscribe(val => {

      this._landingPageService.searchQuoteList(val);

    });

    this.importQuoteSearchSubscription = this.importSearchFormControl.valueChanges.pipe(debounceTime(500), takeUntil(this._onDestroy)).subscribe(val => {
      this.searchingFlag = true;
      this.onSearch();

    });

  }

  getQuoteConfiguration = () => {

    return new Promise((resolve, reject) => {

      this.masterDataService.quoteConfiguration
        .pipe(takeUntil(this._onDestroy))
        .subscribe(res => {

          this.quoteConfiguration = res;

          for (const configItem of this.quoteConfiguration)
            if (configItem && configItem['quote_config_name'] && configItem.hasOwnProperty('quote_config_value')) {

              if (configItem['quote_config_name'] === 'quote_currency')
                this.quoteCurrency = configItem['quote_config_value'];

              else if (configItem['quote_config_name'] === 'quote_help_topic_id')
                this.helpTopicId = configItem['quote_config_value'];
              
              else if (configItem['quote_config_name'] === 'lumpsum_quote_config')
                this.allowMilestoneOpportunityStatus = configItem['quote_config_value'] || [];

              else if (configItem['quote_config_name'] === 'change_request_enabled')
                this.changeRequestEnabled = configItem['quote_config_value'];         

              else if (configItem['quote_config_name'] === 'quote_field_config')
                this.fieldConfig = configItem['quote_config_value'];     

          }

          resolve(true);

        });

    });

  }

  /**
  * @description Gets the opportunity meta details for quote
  */
  getQuoteAccessPrivilege = () => {

    this._quoteMainService.getQuoteAccessPrivilege(this.opportunityId)
      .pipe(takeUntil(this._onDestroy))
      .subscribe(res => {

        if (res && res['messType'] == 'S' && res['data'])
          this.isCUDEnabled = res['data']['cud_access_enabled'] || false;

        else
          this.isCUDEnabled = false;

      },
        err => {
          this.isCUDEnabled = false;
          console.log(err);
          this._toaster.showError("Error", "Error in getting Quote Edit Config", this.opportunityService.mediumInterval);
        });

  }

  /**
   * @description Gets the opportunity meta details for quote
   */
  getOppMetaDetails = async () => {

    this.CR_ENABLED_OBJECT = this._landingPageService.checkCREnabled();

    this._quoteMainService.getOpportunityMetaDetailsForQuote(this.opportunityId)
      .pipe(takeUntil(this._onDestroy))
      .subscribe(async res => {

        if (res && res['messType'] == 'S' && res['data']) {

          await this._landingPageService.getQuoteDetails(this.opportunityId);

          this.oppQuoteDetails['currency'] = res['data']['currency'];
          this.oppQuoteDetails['deliveryStartDate'] = res['data']['delivery_start_date'];
          this.oppQuoteDetails['deliveryEndDate'] = res['data']['delivery_end_date'];
          this.oppQuoteDetails['opportunityStatus'] = res['data']['opportunity_status_code'];
          this.oppQuoteDetails['businessType'] = res['data']['business_type'];
          this.oppQuoteDetails['serviceTypeId'] = res['data']['service_type'];

          this.has_parent_opportunity = res['data']['has_parent_opportunity'] == 1 || false;
          this.isValidServiceType = this.allowMilestoneOpportunityStatus?.service_type?.includes(this.oppQuoteDetails['serviceTypeId'])
          let noActiveMainQuote = this._landingPageService._quotesList.filter(q => q.flag == 1).length === 0

          this.enableChangeRequest = res['data']['opportunity_status_code'] == 'won' && 
          this.changeRequestEnabled &&  //m_qb_config - General Config
          this.CR_ENABLED_OBJECT &&  //Role Restriction
          (this.isValidServiceType ?
          this.allowMilestoneOpportunityStatus?.change_request_applicable : true) &&
          !this.has_parent_opportunity &&  //Not for child opportunity 
          !noActiveMainQuote; //No Main Quote Active;

          this.initialQuote['currency'] = this.oppQuoteDetails['currency'];
          this.initialQuote['deliveryStartDate'] = this.oppQuoteDetails['deliveryStartDate'];
          this.initialQuote['deliveryEndDate'] = this.oppQuoteDetails['deliveryEndDate'];
          this.initialQuote['businessType'] = this.oppQuoteDetails['businessType'];
          this.initialQuote['serviceTypeId'] = this.oppQuoteDetails['serviceTypeId'];

        } 
        
        else 
          await this._landingPageService.getQuoteDetails(this.opportunityId);

      },
        async err => {
          console.log(err);
          await this._landingPageService.getQuoteDetails(this.opportunityId);
          this._toaster.showError("Error", "Error in getting Quote details", this.opportunityService.mediumInterval);
        });

  }

  ngOnDestroy(): void {
    this._onDestroy.next();
    this._onDestroy.complete();
    this._landingPageService.destroy();
    this.spinner.hide();

    if (this.quoteSearchSubscription)
      this.quoteSearchSubscription.unsubscribe();

    if (this.importQuoteSearchSubscription)
      this.importQuoteSearchSubscription.unsubscribe();

  }

  createDialogRef: MatDialogRef<any>;

  openCreateDialog(template: TemplateRef<any>, quoteType: string) {
    this.createDialogRef = this._dialog.open(template, {
      height: '270px',
      width: '440px',
    });
    this.quoteType = quoteType;
    this.createDialogRef.afterClosed().subscribe((res) => {
      this.initialQuote = {
        quoteName: '',
        currency: this.oppQuoteDetails['currency'] || null,
        deliveryStartDate: this.oppQuoteDetails['deliveryStartDate'] || null,
        deliveryEndDate: this.oppQuoteDetails['deliveryEndDate'] || null,
        businessType: this.initialQuote['businessType'] || null,
        serviceTypeId: this.oppQuoteDetails['serviceTypeId'] || null,
        quoteType: quoteType
      };
    });
  }

  routeToCreatePage() {

    this.initialQuote.quoteType = this.quoteType;

    if (this.validateData()) {
      this._quoteMainService.setInitalQuoteDetail(this.initialQuote);
      this.createDialogRef?.close();
      this.$router.navigate([`../create/${0}`], { relativeTo: this._route });

    }

  }

  /**
   * @description Deletes a Quote
   * @param quote 
   */
  deleteQuote = async (quote: object) => {

    const isIntegrated = await this.checkIfProjectIntegrated();

    if(quote['quote_status'] == 1)
      return this._toaster.showWarning("Cannot Delete Quote Under Review", "", this.opportunityService.longInterval); 

    if (quote['flag'] == 1 || quote['change_request_flag'] == 1) {
      const message = quote['change_request_flag'] == 1 ? "Cannot Delete Active Change Request" : "Cannot Delete Active Quote";
      this._toaster.showWarning(message, "", this.opportunityService.longInterval);
      return;
    }

    if(quote['quote_status'] == 1)
      return this._toaster.showWarning("Cannot Delete Quote Under Review", "", 4500);

    if (quote['flag'] == 1) {
      this._toaster.showWarning("Cannot Delete Active Quote", "", 4500);
      return;
    }

    if (isIntegrated && quote['flag'] == 1) {
      this._toaster.showWarning("Cannot Delete Active Quote", "Project has already been created for the Opportunity!", this.opportunityService.longInterval);
      return;
    }

    if (quote && quote['quote_header_id'] && quote['quote_type'] === 'CHANGE_REQUEST' ? this.enableChangeRequest : this.isCUDEnabled)
      this._util.openConfirmationSweetAlertWithCustom("Are you sure?",
        `Are you sure you want to delete ${quote['quote_name']}
          Quote${this.quoteList?.some(q => q.quote_type == 'CHANGE_REQUEST' && q.change_requst_flag == 1)
          ? "? Deleting this quote will also remove all associated active change requests."
          : "?"}`).then((deleteConfirm) => {

            if (deleteConfirm)
              this._quoteMainService.deleteQuote(quote['quote_header_id'], this.opportunityId)
                .pipe(takeUntil(this._onDestroy))
                .subscribe(res => {

                if (res && res['messType'] == "S") {

                  this._landingPageService.deleteQuoteFromList(quote['quote_header_id']);

                  // this._toaster.showSuccess("Success", "Quote deleted Successfully !", this.opportunityService.mediumInterval);
                  this._toaster.showSuccess("Success", quote['quoteType'] !== "CHANGE_REQUEST" ? "Quote deleted Successfully !" : "Change Request deleted Successfully !", this.opportunityService.longInterval);


                }

                else
                  this._toaster.showError("Error", res["messText"], this.opportunityService.mediumInterval);

              },
                err => {
                  console.log(err);
                  this._toaster.showError("Error", quote['quoteType'] !== "CHANGE_REQUEST" ? "Error in deleting Quote" : "Error in deleting Change Request !", this.opportunityService.mediumInterval);
                });

        });

    else
      return this._toaster.showWarning('Access Restricted!', '', this.opportunityService.mediumInterval)

  }

  /**
   * @description Check if Opportunity has integrated with the project if yes then isProjectIntegrated will be true
   * @param opportunityId 
   */
  checkIfProjectIntegrated = () => {
    return new Promise((resolve, reject) => {
      this.opportunityService.getOpportunityProjectDetails(this.opportunityId)
        .pipe(takeUntil(this._onDestroy))
        .subscribe(
          res => {
            this.isProjectIntegrated = res && res['messData'].length > 0;
            resolve(this.isProjectIntegrated);
          },
          err => {
            console.error("Error occured while fetching project integration for the opportunity!" + err);
            reject(err);
          }
        );
    });
  };

  /**
   * @description Updates active flag for a Quote
   * @param quote 
   */
  updateActiveQuote = async (quote: object) => {

    this.allow_approval = this._landingPageService.checkQuoteActivateAccess();

    if (quote['quote_status'] === 1)
      return

    const isIntegrated = await this.checkIfProjectIntegrated();

    if (isIntegrated) 
      return this._toaster.showWarning("Cannot Make Quote Active/InActive!", "Project has already been created for the Opportunity!", this.opportunityService.longInterval);
    
    if (this.isValidServiceType && !quote['hasMilestoneForAllPosition'] && quote['flag'] === 0)
      return this._toaster.showWarning("Cannot make the quote active!", "Milestone for all quotes positions is not selected!", this.opportunityService.longInterval)

    if (quote && quote['quote_header_id'] && this.isCUDEnabled ) {

      const isActive = quote['flag'] == 0 ? 1 : 0;

      if (isActive) {

        const isValidationsMet = this.resolveQuoteOpportunityCheck(quote, "Quote cannot be made Active");

        if (!isValidationsMet)
          return true;

      }

      if (quote['quote_revenue_amount'] <= 0)
        return this._toaster.showWarning("Unable to Activate Quote", "The quote value must be greater than 0.", this.opportunityService.longInterval);

      if (quote['activation_approval_required'] == 1 && !this.allow_approval && quote['flag'] == 0)
        this.openApproversPopUp(quote, 'SEND');

      else
        this._landingPageService.updateQuoteActive(this.opportunityId, quote['quote_header_id'], isActive)
          .pipe(takeUntil(this._onDestroy))
          .subscribe(res => {

            if (res && res['messType'] == "S") {

              this._landingPageService.updateActiveFlagInList(quote['quote_header_id'], isActive);
              
              if (typeof res['data'] !== 'string' && res['data'].length)
                for (let cr of res['data'])
                  this._landingPageService.updateChangeRequestFlagInList(cr['quote_header_id']);

              this._toaster.showSuccess("Success", isActive ? "Active Quote updated Successfully !" : "Quote made inActive !", this.opportunityService.longInterval);

              quote['flag'] = isActive;

              if (typeof res['data'] !== 'string' && this.quoteList.find(q => q.quote_type == 'CHANGE_REQUEST')) 
                this._toaster.showInfo("Info", "All the Active Change Requests have been made inactive", this.opportunityService.mediumInterval);

              if (isActive)
                this.resolveQuoteOpportunityIntegration();

            }

            else
              this._toaster.showError("Error", res["messText"], this.opportunityService.mediumInterval);

          },
            err => {
              console.log(err);
              this._toaster.showError("Error", "Error in Updating Quote Flag", this.opportunityService.mediumInterval);
            });

    }

  }

    /**
   * @description Updates change request active flag for a Quote
   * @param quote 
   */
  updateActiveChangeRequestQuote = async (quote: object) => {
    
    if (quote['quote_status'] === 1)
      return

    if(!this.checkAndEnableChangeRequest())
      return

    if (this.isValidServiceType && !quote['hasMilestoneForAllPosition'] && quote['flag'] === 0)
      return this._toaster.showWarning("Cannot make the quote active!", "Milestone for all quotes positions is not selected!", this.opportunityService.longInterval)
    
    //CUD Access Check
    if (quote && quote['quote_header_id']) {

      if (quote['change_request_flag'] == 1)
        return this._toaster.showWarning("Cannot deactivate Change Request once made active!", "", this.opportunityService.longInterval);

      const isActive = quote['change_request_flag'] == 0 ? 1 : 0;

      if (isActive) {

        const isValidationsMet = this.resolveQuoteOpportunityCheck(quote, "Quote cannot be made Active");

        if (!isValidationsMet)
          return true;

      }

      // Quote Value Restriction for Activation
      // if (quote['quote_revenue_amount'] <= 0) {
      //   this._toaster.showWarning("Unable to Activate Change request!", "The quote value must be greater than 0.", this.opportunityService.longInterval);
      //   return;
      // }

      let checkForPercentageApproval = await this.checkForPercentageApproval(quote)

      if (quote['activation_approval_required'] == 1 && (!this.cr_activation_access || !this.CR_ENABLED_OBJECT) && !quote['change_request_flag'])
        this.openApproversPopUp(quote, 'SEND');

      else if(checkForPercentageApproval && checkForPercentageApproval['data']){
        
        if(checkForPercentageApproval['data'] && checkForPercentageApproval['type']=="approval"){
        this.openApproversPopUp(quote, 'SEND');
        }
        else if(checkForPercentageApproval['data'] && checkForPercentageApproval['type']=="error"){
          this._toaster.showError("Change Request Cannot be made Active",checkForPercentageApproval['msg'] || "Unable to Activate Change request!", 3000);
        }
      }
    
      else{
        if(this.cr_activation_in_progress){
          return
        }
        this.cr_activation_in_progress=true
        this._landingPageService.updateChangeRequestQuoteActive(this.opportunityId, quote['quote_header_id'], isActive)
          .pipe(takeUntil(this._onDestroy))
          .subscribe(async res => {
            
            if (res && res["data"]["err"]) {
              this._toaster.showError("Error", res["data"]['msg'] ? res["data"]['msg'] : "Allocation Of a Position Cannot be reduced", this.opportunityService.longInterval);
              this.cr_activation_in_progress=false
              return true
            }
            if (res && res['messType'] == "S") {

              // this._landingPageService.updateChangeRequestFlagInList(quote['quote_header_id'], isActive);

              this._toaster.showSuccess("Success", isActive ? "Change Request Activated Successfully !" : "Change Request made InActive !", this.opportunityService.mediumInterval);

              quote['change_request_flag'] = isActive;
              this.cr_activation_in_progress=false
              // if (isActive)
              //   this.resolveQuoteOpportunityIntegration();
              await this._landingPageService.getQuoteDetails(this.opportunityId);
            }

            else{
              this.cr_activation_in_progress=false
              this._toaster.showError("Error", res["messText"], this.opportunityService.mediumInterval);
            }

          },
            err => {
              console.log(err);
              this.cr_activation_in_progress=false
              this._toaster.showError("Error", "Error in Updating Change Request Flag", this.opportunityService.mediumInterval);
            });
        }

    }

  }

  checkAndEnableChangeRequest(): boolean {
    const warningMessages = [
      {
        condition: this.oppQuoteDetails['opportunityStatus'] !== 'won',
        is_active: true,
        message: 'Opportunity status must be "Closed As Won" to enable change requests.'
      },
      {
        condition: !this.changeRequestEnabled,
        is_active: true,
        message: 'Change request functionality is not enabled in the general configuration.'
      },
      {
        condition: this.has_parent_opportunity,
        is_active: true,
        message: 'Change requests cannot be made active for child opportunities.'
      },
      {
        condition: !this.CR_ENABLED_OBJECT,
        is_active: false,
        message: 'You do not have the required permissions to enable change requests..'
      },
      {
        condition: !this.isValidServiceType,
        is_active: false,
        message: 'Change requests cannot be made active for the selected opportunity service type'
      },
      {
        condition: this._landingPageService._quotesList.filter(q => q.flag == 1).length === 0,
        is_active: true,
        message: 'Change Request cannot be made active if there is no Main Active Quote.'
      }
    ];

    const warning = warningMessages.find(e => e.is_active && e.condition);

    if (warning) {
      this.showToaster(warning.message);
      return false;
    }

    return true;
  }

  showToaster(message: string) {
    this._toaster.showWarning('Warning!', message, this.opportunityService.longInterval); 
  }

  /**
   * @description Navigates to Quote Edit page
   * @param quote 
   */
  editQuote = (quote: object) => {

    if (this.showDeleted)
      return this._toaster.showSuccess("", quote['quoteType'] !== "CHANGE_REQUEST" ? "Deleted Quote cannot be Edited" : "Deleted Change Request cannot be Edited !", this.opportunityService.longInterval);

    if (quote['quote_header_id']) {

      const isValidationsMet = this.resolveQuoteOpportunityCheck(quote, quote['quoteType'] !== "CHANGE_REQUEST" ? "Quote cannot be Edited" : "Change Request cannot be Edited !"
      );

      if (isValidationsMet)
        this.$router.navigate([`../edit/${quote['quote_header_id']}`], { relativeTo: this._route });

    }

  }

  /**
   * @description Resolves Edit/delete operation on Quote
   * @param quote 
   * @param title 
   * @returns Boolean
   */
  resolveQuoteOpportunityCheck = (quote: object, title: string) => {

    let isValidationsMet = false;

    if (quote['quote_type'] !== "CHANGE_REQUEST") {
      if (!moment(quote['delivery_start_date']).isSame(this.oppQuoteDetails['deliveryStartDate'], 'day'))
        this._toaster.showError(title, "Quote Delivery Start Date & Opportunity Delivery Start Date does not match", this.opportunityService.longInterval);

      else if (!moment(quote['delivery_end_date']).isSame(this.oppQuoteDetails['deliveryEndDate'], 'day'))
        this._toaster.showError(title, "Quote Delivery End Date & Opportunity Delivery End Date does not match", this.opportunityService.longInterval);

      // else if (this.isValidServiceType && !quote['hasMilestoneForAllPosition'] && quote['flag'] === 0 )
      //   return this._toaster.showWarning(title, "Milestone for all quotes positions is not selected!", this.opportunityService.longInterval)

      else 
        isValidationsMet = true;
      
    }

    else if (quote['quote_type'] == "CHANGE_REQUEST") {
      
      if (!moment(quote['delivery_start_date']).isBetween(this.oppQuoteDetails['deliveryStartDate'], this.oppQuoteDetails['deliveryEndDate'], 'day', '[]')) {
        this._toaster.showError(title, "Quote Delivery Start Date must be between Opportunity Delivery Start Date and Delivery End Date", this.opportunityService.longInterval);
      }

      // else if (!moment(quote['delivery_end_date']).isBetween(this.oppQuoteDetails['deliveryStartDate'], this.oppQuoteDetails['deliveryEndDate'], 'day', '[]')) {
      //   this._toaster.showError(title, "Quote Delivery End Date must be between Opportunity Delivery Start Date and Delivery End Date", this.opportunityService.mediumInterval);
      // }

      // else if (!this.isValidServiceType && !quote['hasMilestoneForAllPosition'] && quote['flag'] === 0 )
      //   return this._toaster.showWarning(title, "Milestone for all quotes positions is not selected!", this.opportunityService.longInterval)

      else 
        isValidationsMet = true;
      
    }

    if (quote['quote_currency'] != this.oppQuoteDetails['currency']) {
      this._toaster.showError(title, "Quote Currency & Opportunity Currency does not match", this.opportunityService.longInterval);
      isValidationsMet = false;
    }

    return isValidationsMet;

  }

  resolveQuoteOpportunityIntegration = () => {

    return new Promise((resolve, reject) => {

      this._quoteMainService.resolveQuoteOppIntegration(this.opportunityId, null, 1)
        .pipe(takeUntil(this._onDestroy))
        .subscribe(res => {

          if (res['messType'] == "S") {

            if (res['quoteMessageType']) {

              if (res['quoteMessageType'] == 'info')
                this._toaster.showInfo("Quote Info", res['messText'], this.opportunityService.mediumInterval);

              else if (res['quoteMessageType'] == 'warning')
                this._toaster.showWarning("Quote Warning", res['messText'], this.opportunityService.longInterval);

              else if (res['quoteMessageType'] == 'error')
                this._toaster.showError("Quote Error", res['messText'], this.opportunityService.mediumInterval);

            }

            if (res.hasOwnProperty('showConfirmationPopup') && res['showConfirmationPopup'] == 1 && res['quoteId'])
              this._util.openConfirmationSweetAlertWithCustom("Copy Quote Value?", "Do you wish to copy Quote value to Opportunity ?")
                .then((copyConfirm) => {

                  if (copyConfirm)
                    this._quoteMainService.updateValueInOpportunity(this.opportunityId, res['quoteId'])
                      .pipe(takeUntil(this._onDestroy))
                      .subscribe(res => {

                        if (res['messType'] == "S")
                          this._toaster.showInfo("Value Updated", res["messText"], this.opportunityService.mediumInterval);

                        else
                          this._toaster.showError("Error in Updating", res["messText"], this.opportunityService.mediumInterval);

                        resolve(true);

                      },
                        err => {
                          console.log(err);
                          this._toaster.showError("Error", "Error in Updating Quote value in Opportunity", this.opportunityService.mediumInterval);
                          reject(false);
                        });

                  else
                    resolve(true);

                });

            else
              resolve(true);

          }

          else {

            this._toaster.showError("Error", res["messText"], this.opportunityService.mediumInterval);

            resolve(true);

          }

        },
          err => {
            console.log(err);
            this._toaster.showError("Error", "Error in Checking Quote Configuration", this.opportunityService.mediumInterval);
            reject(false);
          });

    });

  }

  checkForPercentageApproval = async (quote) => {

    try {
      const res = await this._quoteMainService.checkForPercentageApproval(quote);
      if (res) {
        return res;
      }
      else {
        return {}
      }
      // Handle further logic if no warning is triggered
    } catch (error) {
      console.error("Error in percentage approval check:", error);
      return false
      // Handle any errors from the API request here
    }
  }

  showDeletedQuotes = () => {

    this.showDeleted = !this.showDeleted;

    this._landingPageService.clearQuoteList();

    this._landingPageService.getQuoteDetails(this.opportunityId, this.showDeleted);

  }

  /**
   * @description Opens the Quote Activity log component
   */
  openActivityLog = async (quote: object) => {

    if (quote['quote_header_id']) {

      const { QuoteActivityLogComponent } = await import('../components/quote-activity-log/quote-activity-log.component');

      this._dialog.open(QuoteActivityLogComponent, {
        height: '100vh',
        width: '35vw',
        data: {
          quoteId: quote['quote_header_id'],
          showQuoteValue: this.checkFieldShouldbeRestricted('totalRevenue')
        },
        position: {
          right: '0'
        },
        disableClose: false
      });

    }

  }

  copyData = () => {

    if (this.validateData())
      this.copyDialogRef.close({ event: 'submit' });

  }

  validateData = () => {

    this.initialQuote.quoteName = this.initialQuote.quoteName.trim();
    // this.initialQuote.quoteType = this.quoteType;

    if (!this.initialQuote.quoteName) {

      this._toaster.showError("Mandatory Field Error", "Kindly enter Quote Name", this.opportunityService.mediumInterval);

      return false;

    }

    if (!this.initialQuote.currency) {

      this._toaster.showError("Mandatory Field Error", "Kindly update Opportunity Currency before creating Quote !", this.opportunityService.mediumInterval);

      return false;

    }

    if (!this.initialQuote.deliveryStartDate || !moment(this.initialQuote.deliveryStartDate).isValid()) {

      this._toaster.showError("Mandatory Field Error", "Kindly update Delivery Start Date in Opportunity before creating Quote !", this.opportunityService.mediumInterval);

      return false;

    }

    if (!this.initialQuote.deliveryEndDate || !moment(this.initialQuote.deliveryEndDate).isValid()) {

      this._toaster.showError("Mandatory Field Error", "Kindly update Delivery End Date in Opportunity before creating Quote !", this.opportunityService.mediumInterval);

      return false;

    }

    // if (!this.initialQuote.quoteType) {

    //   this._toaster.showError("Error", "Quote type not Found!", this.opportunityService.mediumInterval);

    //   return false;

    // }

    return true;

  }

  /**
   * @description Copies the source quote and creates a new quote
   * @param quote 
   * @param isFromImport
   */
  copyQuote = async (quote: object, isFromImport = false, quoteType) => {

    if (quote['quote_header_id'] && quoteType === 'CHANGE_REQUEST' ? this.enableChangeRequest : this.isCUDEnabled) {

      this.initialQuote.quoteName = quote['quote_name'] + ' Copy';
      this.initialQuote.quoteType = quoteType;

      let showImportOption = false;

      if (isFromImport) {

        showImportOption = true;

        if (quote['quote_currency'] != this.oppQuoteDetails['currency'])
          showImportOption = false;

      }

      this.copyDialogRef = this._dialog.open(this.copyQuoteTemplate, {
        height: showImportOption ? '310px' : '270px',
        width: '440px',
        data: {
          isFromImport: isFromImport,
          showImportOption: showImportOption,
          quoteType: quoteType
        }
      });

      this.copyDialogRef.afterClosed().subscribe((res) => {

        if (res['event'] == 'submit') {

          this.spinner.show();

          if (isFromImport)
            this.isQuoteBeingImported = true;

          this.loadingContent = isFromImport ? "Importing Quote..." : "Copying Quote...";

          const quoteName = this.initialQuote.quoteName;

          const oppDetails = {
            opportunity_id: this.opportunityId,
            opp_currency: this.oppQuoteDetails['currency'],
            delivery_start_date: this.oppQuoteDetails['deliveryStartDate'],
            delivery_end_date: this.oppQuoteDetails['deliveryEndDate']
          };
    
          this._quoteMainService.createQuoteFromSourceQuote(quote['quote_header_id'], quoteName, oppDetails, isFromImport ? this.importFormControl.value : null, quoteType)
            .pipe(takeUntil(this._onDestroy))
            .subscribe(res => {

              if (res["messType"] == "S") {

                this._toaster.showSuccess("Success", quoteType !== "CHANGE_REQUEST" ? "Quote Copied Successfully !" : "Change Request Copied Successfully !", this.opportunityService.mediumInterval);

                this._landingPageService.getQuoteDetails(this.opportunityId);

              }

              else
                this._toaster.showError("Error", quoteType !== "CHANGE_REQUEST" ? "Error in Copying Quote" : "Error in Copying Change Request !", this.opportunityService.mediumInterval);

              this.spinner.hide();

              this.isQuoteBeingImported = false;

            },
             err => {
              console.log(err);
              this.spinner.hide();
              this.isQuoteBeingImported = false;
              this._toaster.showError("Error", quoteType !== "CHANGE_REQUEST" ? "Error in Copying Quote" : "Error in Copying Change Request !", this.opportunityService.mediumInterval);
             });

        }

        this.initialQuote.quoteName = '';

        this.importFormControl.reset('2');

      });

    }

    else
      return this._toaster.showWarning('Access Restricted!', '', this.opportunityService.mediumInterval)

  }

   /**
   * @description Gets the Quote List Grouped by Opportunity
   */
   getOppQuoteList = async () => {
    if (this.limitLoader) return;
  
    this.limitLoader = true;
  
    const offset = this.offset;
    const limit = this.limit;
    
    try {
      const res = await this._quoteMainService.getOpportunityQuoteList(this.opportunityId, limit, offset, this.searchQuery)
        .pipe(takeUntil(this._onDestroy))
        .toPromise()
        .catch((err) => {
          if (err.name === 'UnsubscribedError' || err.message === 'The operation was canceled') {
            // console.log("Request cancelled.");
            return null;
          }
          throw err;
        });

      if (!res) return; 
      if(res && res["is_rds_peak"]){
        this._toaster.showError("Error", res['messText']|| "System unavailable Please Try Again Later", 3000);
      }
      if (res['messType'] === 'S' && res['data'] && res['data'].length) {
        this.oppQuoteList = [...this.oppQuoteList, ...res['data']];
  
        // Increment offset for the next batch
        this.offset += this.limit;
  
        // Check if all data is loaded
        if (res['data'].length < this.limit) {
          this.allDataLoaded = true;
        }
  
        if (this._landingPageService._quotesList.length === 0 && this.isCUDEnabled && !this.dialogOpened) {
          this.dialogOpened = true;
          this.openImportQuoteDialog();
        }
      } else {
        this.allDataLoaded = true;
      }
    } catch (err) {
      console.log(err);
      this._toaster.showError("Error", "Error in getting Opportunity Quote list", this.opportunityService.mediumInterval);
    } finally {
      this.limitLoader = false;
    }
  }
  
  async onSearch() {
    try {
      this.searchingFlag = true; 
      this.oppQuoteList = [];
      this.offset = 0;
      await this.getOppQuoteList();
    } finally {
      this.searchingFlag = false; 
    }
  }
  
  clearSearch() {
    this.searchQuery = '';
    this.onSearch();  
  }

  onScroll() {
    if (!this.limitLoader) {
      this.getOppQuoteList();
    }
  }

  openImportQuoteDialog = async () => {

    this.importDialogRef = this._dialog.open(this.importQuoteTemplate, {
      height: '70vh',
      width: '35vw',
      position: {
        right: '0',
        top: '28vh'
      },
      backdropClass: 'quote-transparent-overlay-backdrop'
    });

  }

  /**
   * @description Imports Quote from a Opportunity
   * @param oppItem 
   * @param quoteItem 
   */
  importQuote = (oppItem, quoteItem) => {

    this.copyQuote(quoteItem, true, 'TRADITIONAL');

    this.importDialogRef.close();

  }

  openHelpDialog() {

    if (this.helpTopicId)
      this._help.openHelpDialog(this._login.getToken(), this.helpTopicId, this.currentUser);

    else
      this._toaster.showError("Help Topic is not Configured", "Kindly update Help Topic in Quote Configuration", this.opportunityService.longInterval);

  }


  async openApproversPopUp(quoteDetails, type?) {

    if (type === 'SEND')
      await this.getQuoteApprovers(quoteDetails);

    if (type === 'SEE')
      await this.getQuoteStatus(quoteDetails)

    let dialogConfig = {
      width: '30rem',
      minHeight: '40vh',
      data: { type: 'SUBMIT', quoteDetails: quoteDetails, popUpType: type },
      animation: {
        entryAnimation: {
          keyframes: [
            { transform: 'scale(0.5) translateY(100%)', opacity: 0 },
            { transform: 'scale(1) translateY(0)', opacity: 1 },
          ],
          keyframeAnimationOptions: {
            duration: 250,
            easing: 'ease-in-out',
          },
        },
        exitAnimation: {
          keyframes: [
            { transform: 'scale(1) translateY(0)', opacity: 1 },
            { transform: 'scale(0.5) translateY(100%)', opacity: 0 },
          ],
          keyframeAnimationOptions: {
            duration: 250,
            easing: 'ease-in-out',
          },
        },
      },
      disableClose: true,
    };


    this.submitQuoteForApprovalRef = this._dialog.open(this.submitQuoteForApprovalTemplate, dialogConfig);

  }

  async closeApproverPopUp(quote, action) {
    console.log(quote)
    if (action === 'APPROVE') {
      this.approveOrRejectRef.close(action);
    }
    else if (action === 'REJECT') {
      if (!this.commentToSubmitter || this.commentToSubmitter.trim() === '') {
        this._toaster.showWarning('Please enter comment before rejecting approval!', '', this.opportunityService.longInterval);
        return;
      }
      this.approveOrRejectRef.close(action);
    }
    else
      this.approveOrRejectRef.close('CANCEL');
  }

  async closeSubmitApprovalPopUp(quote, action) {
    console.log(quote)
    if (action === 'SUBMIT') {
      await this.sendForApproval(quote);

      await this._landingPageService.getQuoteDetails(this.opportunityId);
    }

    this.submitQuoteForApprovalRef.close();
  }

  async sendForApproval(quoteDetails) {
    let details = {
      quoteDetails: quoteDetails,
      approvals: this.reviewersList.map((reviewer: { associate_id: any; }) => reviewer.associate_id)
    };

    try {
      const response = await this._landingPageService.submitForQuoteApproval(details).toPromise();

      console.log('API Response:', response);

      if (response.length > 0 && response.messType === "S") {
        this._toaster.showSuccess("Quote sent for Approval", "", this.opportunityService.mediumInterval);
      }
    } catch (error) {
      console.error('API Error:', error);
    }
  }


  // async sendForApproval(quoteDetails) {

  //   let details = {
  //     quoteDetails: quoteDetails,
  //     approvals: this.reviewersList.map((reviewer: { associate_id: any; }) => reviewer.associate_id)
  //   }

  //   await this._landingPageService.submitForQuoteApproval(details).subscribe(
  //     response => {
  //       console.log('API Response:', response);

  //       if (response.length > 0 && response.messType === "S") {

  //         this._toaster.showSuccess("Quote send for Approval", "", this.opportunityService.mediumInterval);

  //       }

  //     },
  //     error => {
  //       console.error('API Error:', error);
  //     }
  //   );
  // }

  async getQuoteApprovers(quoteDetails) {
    this.miniLoader = true;
    let details = {
      quoteDetails: quoteDetails
    };

    this._landingPageService.getQuoteApprovers(details).subscribe(
      (res: any) => {
        console.log('Approvers:', res);
        this.reviewersList = res?.data;
        // this.commentToSubmitter = this.reviewersList?.comments;
        if (this.reviewersList.length == 0)
          this._toaster.showWarning('No Approvers Found for the selected type!', '', this.opportunityService.longInterval)
        this.miniLoader = false;
      },
      (error) => {
        console.error('Error fetching approvers:', error);
        this.miniLoader = false;
      }
    );
  }

  async getQuoteStatus(quoteDetails: any): Promise<void> {
    this.miniLoader = true;
  
    try {
      let details = {
        quote_id: quoteDetails.quote_header_id
      };
  
      const res: any = await new Promise((resolve, reject) => {
        this._landingPageService.getQuoteStatus(details).subscribe(
          (response) => resolve(response),
          (error) => reject(error)
        );
      });
  
      console.log('Approvers:', res);
      let approverList = res.data.approvers
      this.submitterDetails = res?.data.submitter[0];
      this.reviewersList = res?.data.approvers;
      const approver = approverList.find(reviewer => reviewer.isApprover === true);
      const comments = this.reviewersList.find(reviewer => reviewer.comments)?.comments || ' ';
      this.commentToSubmitter = comments || ' ';
  
      if (this.submitterDetails.length === 0) {
        this._toaster.showWarning('No Approvers Found for the selected type!', '', this.opportunityService.longInterval);
      }
  
    } catch (error) {
      console.error('Error fetching approvers:', error);
    } finally {
      this.miniLoader = false;
    }
  }
  

  // async getQuoteStatus(quoteDetails) {
  //   this.miniLoader = true;
  //   let details = {
  //     quote_id: quoteDetails.quote_header_id
  //   };

  //   this._landingPageService.getQuoteStatus(details).subscribe(
  //     (res: any) => {
  //       console.log('Approvers:', res);
  //       // this.reviewersList = res?.data;
  //       this.submitterDetails = res?.data.submitter[0];
  //       this.commentToSubmitter = this.reviewersList?.comments;
  //       if (this.reviewersList.length == 0)
  //         this._toaster.showWarning('No Approvers Found for the selected type!', '')
  //       this.miniLoader = false;
  //     },
  //     (error) => {
  //       console.error('Error fetching approvers:', error);
  //       this.miniLoader = false;
  //     }
  //   );
  // }

  reject(quoteDetails: any): void {

    let dialogConfig = {
      width: '30rem',
      minHeight: '45vh',
      data: { type: 'REJECT', actionTypeName: 'Reject', quoteDetails: quoteDetails },
      disableClose: true,
      panelClass: 'custom-mat-dialog-panel',
    };
    this.approveOrRejectRef = this._dialog.open(this.approveOrRejectTemplate, dialogConfig);

    this.approveOrRejectRef.afterClosed().subscribe(result => {
      if (result && result == 'REJECT_CANCEL') {
        // quote.quote_triggered_disable = false;
      }
      if (result && result == 'REJECT') {

        let details = {
          workflowId: quoteDetails.approval_workflow_header_id,
          approval_status: 'R',
          comments: this.commentToSubmitter,
          quoteDetails: quoteDetails
        };

        this._landingPageService.approveOrReject(details).subscribe(
          (res: any) => {
            console.log('Approvers:', res);
            if (res) {
              this.miniLoader = false;

              this._toaster.showSuccess('Quote Rejected!', '', 1200);
            }
          },
          (error) => {
            console.error('Error fetching approvers:', error);
            let err = error?.error?.err
            if (err?.code == "WORKFLOW_TRIGGERED" && err?.err) {
              this._toaster.showWarning('Cannot Reject Quote!', 'Approval process already completed', this.opportunityService.longInterval);
            } else if (err?.code != "WORKFLOW_TRIGGERED" && err?.err) {
              this._toaster.showWarning('Error rejecting quote!', err?.msg || 'Kindly try again after sometime', this.opportunityService.mediumInterval);
            } else {
              this._toaster.showError('Error rejecting quote!', 'Kindly try again after sometime', 1200);
            }
            this.miniLoader = false;
          }
        );
      }
    });
  }

  approve(quoteDetails: any): void {
    // quote.quote_triggered_disable = true;

    let dialogConfig = {
      width: '30rem',
      minHeight: '45vh',
      data: { type: 'APPROVE', actionTypeName: 'Approve', quoteDetails: quoteDetails },
      disableClose: true,
      panelClass: 'custom-mat-dialog-panel',
    };
    this.approveOrRejectRef = this._dialog.open(this.approveOrRejectTemplate, dialogConfig);

    this.approveOrRejectRef.afterClosed().subscribe(result => {
      if (result && result == 'CANCEL') {
      }
      if (result && result == 'APPROVE') {

        let details = {
          workflowId: quoteDetails.approval_workflow_header_id,
          approval_status: 'A',
          comments: this.commentToSubmitter,
          quoteDetails: quoteDetails
        };

        this._landingPageService.approveOrReject(details).subscribe(
          (res: any) => {
            console.log('Approvers:', res);
            if (res) {
              this.miniLoader = false;

              this._toaster.showSuccess('Quote Approved!', '', 1200);

              this.updateActiveQuote(quoteDetails);

            }
          },
          (error) => {
            console.error('Error fetching approvers:', error);
            let err = error?.error?.err
            if (err?.code == "WORKFLOW_TRIGGERED" && err?.err) {
              this._toaster.showWarning('Cannot Approve Quote!', 'Approval process already completed', this.opportunityService.longInterval);
            } else if (err?.code != "WORKFLOW_TRIGGERED" && err?.err) {
              this._toaster.showWarning('Error approving quote!', err?.msg || 'Kindly try again after sometime', this.opportunityService.mediumInterval);
            } else {
              this._toaster.showError('Error approving quote!', 'Kindly try again after sometime', 1200);
            }
            this.miniLoader = false;
          }
        );
      }
    });
  }

  async openApproverDialog(quoteDetails: any) {

    await this.getQuoteStatus(quoteDetails)

    if (!this.isCUDEnabled && quoteDetails?.quote_status === 2 && quoteDetails?.quote_status === 3) {
      this.openApproversPopUp(quoteDetails, 'SEE')
    }

    let submitterDetails = this.submitterDetails;
    let approverDetails = this.reviewersList;

    let dialogConfig = {
      width: '35rem',
      minHeight: '45vh',
      data: { quoteDetails: quoteDetails, submitterDetails: submitterDetails, approverDetails: approverDetails  },
      disableClose: true,
      panelClass: 'custom-mat-dialog-panel',
    };

    this.approveOrRejectRef = this._dialog.open(this.approveOrRejectTemplate, dialogConfig);

    const result = await this.approveOrRejectRef.afterClosed().toPromise();

    if (result === 'CANCEL')
      return

    if (result === 'APPROVE') {
      let details = {
        workflowId: quoteDetails.approval_workflow_header_id,
        approval_status: 'A',
        comments: this.commentToSubmitter,
        quoteDetails: quoteDetails
      };

      try {
        const res: any = await this._landingPageService.approveOrReject(details).toPromise();
        console.log('Approvers:', res);
        if (res) {
          this.miniLoader = false;
          this._toaster.showSuccess('Quote Approved!', '', 1200);
          this.updateActiveQuote(quoteDetails);
        }
      } catch (error) {
        console.error('Error approving quote:', error);
        let err = error?.error?.err;
        if (err?.code === "WORKFLOW_TRIGGERED" && err?.err) {
          this._toaster.showWarning('Cannot Approve Quote!', 'Approval process already completed', this.opportunityService.longInterval);
        } else if (err?.code !== "WORKFLOW_TRIGGERED" && err?.err) {
          this._toaster.showWarning('Error approving quote!', err?.msg || 'Kindly try again after sometime', this.opportunityService.mediumInterval);
        } else {
          this._toaster.showError('Error approving quote!', 'Kindly try again after sometime', 1200);
        }
        this.miniLoader = false;
      }
    }

    if (result === 'REJECT') {
      let details = {
        workflowId: quoteDetails.approval_workflow_header_id,
        approval_status: 'R',
        comments: this.commentToSubmitter,
        quoteDetails: quoteDetails
      };

      try {
        const res: any = await this._landingPageService.approveOrReject(details).toPromise();
        console.log('Approvers:', res);
        if (res) {
          this.miniLoader = false;
          this._toaster.showSuccess('Quote Rejected!', '', 1200);
        }
      } catch (error) {
        console.error('Error rejecting quote:', error);
        let err = error?.error?.err;
        if (err?.code === "WORKFLOW_TRIGGERED" && err?.err) {
          this._toaster.showWarning('Cannot Reject Quote!', 'Approval process already completed', this.opportunityService.longInterval);
        } else if (err?.code !== "WORKFLOW_TRIGGERED" && err?.err) {
          this._toaster.showWarning('Error rejecting quote!', err?.msg || 'Kindly try again after sometime', this.opportunityService.mediumInterval);
        } else {
          this._toaster.showError('Error rejecting quote!', 'Kindly try again after sometime', 1200);
        }
        this.miniLoader = false;
      }
    }

    await this._landingPageService.getQuoteDetails(this.opportunityId);
  }

  getApproverTooltip(approver: any): string {
    return `${approver?.aid || approver?.associate_id} - ${approver.employee_name || ''}\nStatus: ${approver.status_name}`;
  }

  getStatusCss(status: number): any {
    switch (status) {
      case 4:
        return { 'background': '#E8E9EE', 'color': '#45546E' };
      case 1:
        return { 'background': '#FFF3E8', 'color': '#FA8C16' };
      case 2:
        return { 'background': '#EEF9E8', 'color': '#52C41A' };
      case 3:
        return { 'background': '#FFEBEC', 'color': '#FF3A46' };
      default:
        return {};
    }
  }

  /**
* @description Determines if a field should be restricted based on key and resource_type.
* @param key - The field key to check.
* @returns True if the field should be restricted, otherwise false.
*/
  checkFieldShouldbeRestricted(fieldKey: string): boolean {
    const currentUserRole = this._ticket.getCurrentUserRole();
    const field = this.fieldConfig.find(item => item.key === fieldKey);
    return !(field?.role_access_restriction && field.role_access_restriction.includes(currentUserRole));
  }


}
