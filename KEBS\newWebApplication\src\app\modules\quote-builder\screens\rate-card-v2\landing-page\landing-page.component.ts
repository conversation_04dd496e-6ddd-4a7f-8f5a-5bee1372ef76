import { ChangeDetector<PERSON>ef, Component, <PERSON>E<PERSON>ter, HostListener, OnInit, SimpleChanges, TemplateRef } from '@angular/core';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { QBMasterDataService } from '../../../services/qb-master-data.service';
import { QuoteMainService } from '../../../services/quote-main.service';
import { RolesService } from 'src/app/services/acl/roles.service';
import { ToasterService } from 'src/app/services/toaster-service/toaster.service';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { ActivatedRoute, Router } from '@angular/router';
import { Location } from '@angular/common';
import { FormControl } from '@angular/forms';
import { ReportsService } from 'src/app/modules/product-report/services/reports.service';
import { SubSink } from 'subsink';

@Component({
  selector: 'app-landing-page',
  templateUrl: './landing-page.component.html',
  styleUrls: ['./landing-page.component.scss']
})
export class LandingPageComponent implements OnInit {

  mandatoryFieldDialogRef: MatDialogRef<any>;
  protected _onDestroy = new Subject<void>();
  subs = new SubSink();
  isQuoteEnabled: boolean = false;
  isRateCardUploadEnabled: boolean = false;
  isRateFieldConfigEnabled: boolean = false;
  isRateDataConfigEnabled: boolean = false;
  wEMappingEnabled: boolean = false;
  sDMappingEnabled: boolean = false;
  openFormV: boolean = false;

  selectedToggle: number;
  manpowerFieldsList: any[];
  nonmanpowerFieldsList: any[];
  licenseFieldsList: any[];
  mandatoryConfigName = 'quote_mandatory_fields';
  quoteMandatoryFieldList: any[];
  reports = [
    { label: 'Man Power', value: 'manPower', is_active: true, is_disabled: false },
    { label: 'Non Man Power', value: 'nonManPower', is_active: true, is_disabled: false },
    { label: 'License', value: 'license', is_active: true, is_disabled: false }
  ];

  rcTypelist = [
    {
      label: "Manpower",
      type: 1,
      is_active: true
    },
    {
      label: "Non-Manpower",
      type: 2,
      is_active: true
    },
    {
      label: "License",
      type: 3,
      is_active: true
    }
  ];

  private _activeReport: string;
  rcMandatoryFieldList: any[];
  nmpMandatoryFieldList: any[];
  licMandatoryFieldList: any[];
  rcLabel: string = this.reports.find(r => r.is_active)?.label || 'manPower';
  isLoadingConfig: boolean = false;

  constructor(
    private _dialog: MatDialog,
    private _quoteService: QuoteMainService,
    private _toaster: ToasterService,
    private _qbMasterService: QBMasterDataService,
    private _roles: RolesService,
    private _router: Router,
    private route: ActivatedRoute,
    private location: Location,
    private cdr: ChangeDetectorRef,
    private _rs: ReportsService
  ) { }

  async ngOnInit() {

    this._qbMasterService.quoteEnabled.pipe(takeUntil(this._onDestroy)).subscribe(res => {
      this.isQuoteEnabled = res['quote_enabled'];
    });

    this._rs.registerFunction('customFunction', this.openForm.bind(this));

    let type = this.handleReloadPersistence();

    type ? (this._activeReport = type) : (this._activeReport = this.reports.find(r => r.is_active)?.value || 'manPower') 

    this.rcLabel = this.reports.find(r => r.value === this.activeReport)?.label;

    this.selectedToggle = this.rcTypelist.find(r => r.label.toLowerCase().replace('-', '') === this._activeReport.toLowerCase())?.type || 1; history.pushState(null, '', location.href);

    const tabColor = await this._qbMasterService.getTheme();

    document.documentElement.style.setProperty('--intButton', tabColor);

    const rcRoles = this._roles.roles.filter(val => val['application_id'] === 908);

    const storedOpenFormV = localStorage.getItem('openFormV');

    this.openFormV = storedOpenFormV ? JSON.parse(storedOpenFormV) : false;

    window.addEventListener('storage', this.handleStorageChange);

    if (rcRoles.length) {

      this.isRateCardUploadEnabled = rcRoles.some(val => val['object_id'] === 90801);

      this.isRateFieldConfigEnabled = rcRoles.some(val => val['object_id'] === 90802);
      
      this.isRateDataConfigEnabled = rcRoles.some(val => val['object_id'] === 90803);
    
    }

    this.calculateDynamicContentHeight();

    const config = [this.mandatoryConfigName, 'quote_work_location_entity_mapping_enabled', 'quote_service_division_mapping_enabled'];

    this._quoteService.getQuoteConfiguration(config)
      .pipe(takeUntil(this._onDestroy))
      .subscribe(res => {

        if (res['messType'] == "S" && res['data'] && res['data'].length) {

          const quoteConfiguration = res['data'];

          for (const configItem of quoteConfiguration)
            if (configItem && configItem['quote_config_name'] && configItem.hasOwnProperty('quote_config_value')) {

              switch (configItem['quote_config_name']) {

                case 'quote_mandatory_fields':

                  this.quoteMandatoryFieldList = configItem['quote_config_value'];

                  break;

                case 'quote_work_location_entity_mapping_enabled':

                  this.wEMappingEnabled = configItem['quote_config_value'] || false;

                  break;

                case 'quote_service_division_mapping_enabled':

                  this.sDMappingEnabled = configItem['quote_config_value'] || false;

                  break;

                default:
                  break;

              }
            }
        }
      },
        err => {
          console.log(err);
          this._toaster.showError("Error", "Error in getting Quote configuration", 3000);
        });

    await this.configMaster()   

  }

  async configMaster() {
    this.isLoadingConfig = true;
    try {
      await Promise.all([
        this.getRcFieldConfig(1),
        this.getRcFieldConfig(2),
        this.getRcFieldConfig(3),
      ]);
    } catch (error) {
      console.error("Error loading field configs:", error);
    } finally {
      this.isLoadingConfig = false;
    }
  }

  @HostListener('window:resize')
  onResize() {
    setTimeout(() => {
      this.calculateDynamicContentHeight();
    }, 10);
  }

  /**
   * @description Calculates dynamic height based on screen size
   */
  calculateDynamicContentHeight() {
    document.documentElement.style.setProperty(
      '--reportDynamicHeight',
      window.innerHeight - 140 + 'px'
    );
    document.documentElement.style.setProperty(
      '--reportDynamicSubHeight',
      window.innerHeight - 190 + 'px'
    );
    document.documentElement.style.setProperty(
      '--reportDynamicListViewHeight',
      window.innerHeight - 230 + 'px'
    );
  }

  private handleReloadPersistence() {
    const storedFormAction = localStorage.getItem('formAction');

    if (storedFormAction) {
      const { rcType } = JSON.parse(storedFormAction);

      return rcType
    }
  }

  openForm(type?: any) {
    let rcType = this.reports.find(r => r.value === this.activeReport)?.value;
    let rcLabel = this.reports.find(r => r.value === this.activeReport)?.label;
    let forUpdate;

    if (typeof type === 'object' && type !== null && 'key' in type && 'lineItem' in type)
      forUpdate = type.lineItem, type = type.key;

    this._quoteService.setFormAction(type, rcType, rcLabel, forUpdate);
    this.openFormV = true;
    localStorage.setItem('openFormV', JSON.stringify(this.openFormV));
  }

  onBackClick() {
    this.handleBackNavigation();
  }

  private handleBackNavigation() {
    if (this.openFormV) {
      this.openFormV = false;
      localStorage.removeItem('formValues');
      localStorage.removeItem('formAction');
      localStorage.removeItem('openFormV');
      history.pushState(null, '', location.href);
      setTimeout(() => {
        this.calculateDynamicContentHeight();
      }, 15);
      
    } else {
      // this.location.back();
      history.pushState(null, '', location.href);
      // this.location.back();
      this._router.navigate(['/main/admin-programs']);
    }
  }

  @HostListener('window:popstate', ['$event'])
  onPopState(event: Event) {
    console.log(event)
    this.handleBackNavigation();
  }

  get activeReport(): string {
    return this._activeReport;
  }

  set activeReport(value: string) {
    if (this._activeReport !== value) {
      this._activeReport = value;
      this.onActiveReportChange();
      setTimeout(() => {
        this.calculateDynamicContentHeight();
      }, 15);
    }
  }

  private onActiveReportChange() {
    localStorage.removeItem('formValues');

    let rcType = this.reports.find(r => r.value === this.activeReport)?.value;
    this.rcLabel = this.reports.find(r => r.value === this.activeReport)?.label;

    this._quoteService.setFormAction('CREATE', rcType, this.rcLabel);
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
    window.removeEventListener('storage', this.handleStorageChange);
  }

  private handleStorageChange = (event: StorageEvent) => {
    if (event.key === 'openFormV' && event.newValue === null) {
      this.openFormV = false;
      this.cdr.detectChanges(); // Force re-render
    }
  };

  // Mandatory Field Functions
  resolveMandatoryFields = () => {

    for (const fieldItem of this.manpowerFieldsList) {

      fieldItem['isMandatory'].patchValue(this.rcMandatoryFieldList.includes(fieldItem['fieldKey']));

      fieldItem['isQuoteMandatory'].patchValue(this.isQuoteEnabled ? this.quoteMandatoryFieldList.includes(fieldItem['fieldKey']) : false);

    }

    for (const fieldItem of this.nonmanpowerFieldsList)
      fieldItem['isMandatory'].patchValue(this.nmpMandatoryFieldList.includes(fieldItem['fieldKey']));

    for (const fieldItem of this.licenseFieldsList)
      fieldItem['isMandatory'].patchValue(this.licMandatoryFieldList.includes(fieldItem['fieldKey']));

  }

  openMandatoryFieldsDialog(template: TemplateRef<any>) {
    this.selectedToggle = this.rcTypelist.find(r => r.label.toLowerCase().replace('-', '') === this._activeReport.toLowerCase())?.type || 1; history.pushState(null, '', location.href);

    this.mandatoryFieldDialogRef = this._dialog.open(template, {
      height: '75%',
      width: this.isQuoteEnabled ? '50vw' : '30vw',
    });

    this.mandatoryFieldDialogRef.afterClosed().subscribe((res) => {

    });

  }

  closeMandatoryFieldDialog = () => {

    this.mandatoryFieldDialogRef?.close();

    this.resolveMandatoryFields();

  }

  saveMandatoryFields = () => {

    let quoteMandatoryFieldList = [], fieldConfig = [];

    const fieldsList = this.selectedToggle == 1 ? this.manpowerFieldsList : this.selectedToggle == 2 ? this.nonmanpowerFieldsList : this.licenseFieldsList;

    for (const fieldItem of fieldsList) {

      fieldConfig.push({
        id: fieldItem['id'],
        fieldKey: fieldItem['fieldKey'],
        isMandatory: fieldItem['isMandatory'].value
      });

      if (this.isQuoteEnabled && fieldItem['isMandatory'].value && fieldItem['rateFlowEnabled'] && fieldItem['isQuoteMandatory'].value)
        quoteMandatoryFieldList.push(fieldItem['fieldKey']);

    }

    if (this.isQuoteEnabled && this.selectedToggle == 1) {

      const config = [
        {
          config_name: this.mandatoryConfigName,
          config_value: quoteMandatoryFieldList
        }
      ];

      this._quoteService.updateQuoteConfiguration(config)
        .pipe(takeUntil(this._onDestroy))
        .subscribe(res => {

          if (res['messType'] == "S") {

            this.quoteMandatoryFieldList = quoteMandatoryFieldList;

            this.updateRcFieldConfiguration(fieldConfig);

          }

          else
            this._toaster.showError("Error", "Error in updating Quote configuration", 3000);

        },
          err => {
            console.log(err);
            this._toaster.showError("Error", "Error in updating Quote configuration", 3000);
          });

    }

    else
      this.updateRcFieldConfiguration(fieldConfig);

  }

  updateRcFieldConfig = (fieldConfig = [], selectedType = 1) => {

    if (fieldConfig.length) {

      let fieldsList = [], mandatoryFieldList = [];

      for (const fieldItem of fieldConfig) {

        const field = {
          id: fieldItem['id'],
          fieldKey: fieldItem['field_key'],
          fieldLabel: fieldItem['field_label'],
          isMandatory: new FormControl(fieldItem['is_mandatory']),
          rateFlowEnabled: fieldItem['rate_flow_to_quote_enabled'],
          isQuoteMandatory: new FormControl(this.quoteMandatoryFieldList.includes(fieldItem['field_key']))
        };

        if (fieldItem['is_mandatory'])
          mandatoryFieldList.push(fieldItem['field_key']);

        fieldsList.push(field);

      }

      if (selectedType == 1) {

        this.manpowerFieldsList = fieldsList;

        this.rcMandatoryFieldList = mandatoryFieldList;

      }

      else if (selectedType == 2) {

        this.nonmanpowerFieldsList = fieldsList;

        this.nmpMandatoryFieldList = mandatoryFieldList;

      }

      else if (selectedType == 3) {

        this.licenseFieldsList = fieldsList;

        this.licMandatoryFieldList = mandatoryFieldList;

      }

    }

  }

  updateRcFieldConfiguration = (fieldConfig = []) => {

    this._quoteService.updateRcFieldConfig(fieldConfig)
      .pipe(takeUntil(this._onDestroy))
      .subscribe(res => {

        if (res['messType'] == "S") {

          const mandatoryFields = fieldConfig.map(val => val['isMandatory'] ? val['fieldKey'] : null).filter(val => val);

          if (this.selectedToggle == 1)
            this.rcMandatoryFieldList = mandatoryFields;

          else if (this.selectedToggle == 2)
            this.nmpMandatoryFieldList = mandatoryFields;

          else if (this.selectedToggle == 3)
            this.licMandatoryFieldList = mandatoryFields;

          this._toaster.showSuccess("Updated", "Field Configuration updated Successfully !", 2000);

          this.mandatoryFieldDialogRef?.close();

          if (this.openFormV) {

            this.openFormV = false

            setTimeout(() => {
            this.openFormV = true
            }, 5);

          }

        }

        else
          this._toaster.showError("Error", "Error in updating Field configuration", 3000);

      },
        err => {
          console.log(err);
          this._toaster.showError("Error", "Error in updating Field configuration", 3000);
        });

  }

  async openDataConfigDialog() {
    const { DataConfigPageComponent } = await import(
      './../data-config-page/data-config-page.component'
    )
    this.mandatoryFieldDialogRef = this._dialog.open(DataConfigPageComponent, {
      height: '100%',
      width: 'auto',
      minWidth: '45%',
      position: {
        right: '0px',
      },
    });

    this.mandatoryFieldDialogRef.afterClosed().subscribe((res) => {

      if (this.openFormV && res && this.activeReport == 'manPower') {

        this.openFormV = false

        setTimeout(() => {
          this.openFormV = true
        }, 5);

      }
    });

  }

  selectToggle(selectedType: number) {
    this.selectedToggle = selectedType;
    setTimeout(() => {
      this.calculateDynamicContentHeight();
    }, 15);
  }

  getRcFieldConfig = async (rcType = 1) => {
    return new Promise((resolve, reject) => {
      this.subs.sink = this._quoteService.getRcFieldConfiguration(rcType).subscribe(
        (res: any) => {
          const fieldConfig = res.data;
  
          let refFieldConfig = [];
  
          fieldConfig.forEach((fieldItem: any) => {
            refFieldConfig.push({
              id: fieldItem['id'],
              fieldKey: fieldItem['field_key'], 
              fieldLabel: fieldItem['field_label'], 
              fieldPosition: fieldItem['field_position'],
              isMandatory: new FormControl(fieldItem['is_mandatory']), 
              isQuoteMandatory: new FormControl(fieldItem['is_mandatory']), 
              rateFlowEnabled: fieldItem['rate_flow_to_quote_enabled']
            });
          });
  
          if (rcType === 1) this.manpowerFieldsList = refFieldConfig;
          else if (rcType === 2) this.nonmanpowerFieldsList = refFieldConfig;
          else if (rcType === 3) this.licenseFieldsList = refFieldConfig;
  
          resolve(true); // Resolve the promise
        },
        (err) => {
          console.error(`Error fetching config for rcType ${rcType}:`, err);
          reject(err);
        }
      );
    });
  };
  
}
