import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, Subject, of } from 'rxjs';
import { catchError, takeUntil } from 'rxjs/operators';
import { RolesService } from 'src/app/services/acl/roles.service';
import * as _ from 'underscore';

@Injectable({
  providedIn: 'root',
})

/**Service to support UI state in quote landing page */
export class QouteLandingPageService {
  constructor(private _http: HttpClient, private roleService: RolesService) {}

  private _onDestroy: Subject<any> = new Subject<any>();
  private quotesList$: Subject<any> = new Subject<any>();
  public _quotesList: any[];
  private _quotesListForSearch: any[];
  public quotesList: Observable<any> = this.quotesList$
    .asObservable()
    .pipe(takeUntil(this._onDestroy));

  currentFlaggedQuote: string | number;
  isQuoteLoading: boolean = false;

  async flagQuote(quoteId) {
    let add, remove;
    if (quoteId == this.currentFlaggedQuote) return;
    for (let quote of this._quotesList) {
      if (quoteId == quote.quote_header_id) {
        quote.flag = true;
        add = quote;
      }
      if (this.currentFlaggedQuote == quote.quote_header_id) {
        quote.flag = false;
        remove = quote;
      }
    }
    this.currentFlaggedQuote = quoteId;
    this._http
      .post('api/qb/quote/setQuoteFlag', {
        add: add.quote_header_id,
        remove: remove.quote_header_id,
      })
      .pipe(
        catchError((err) => {
          return of({ messType: 'E' });
        })
      )
      .subscribe((res) => {
        if (res['messType'] == 'E') {
          add.flag = !add.flag;
          remove.flag != remove.flag;
          this.currentFlaggedQuote = remove.quote_header_id;
        }
      });
    this._emitQuoteList();
  }

  getQuoteDetails(opportunity_id, showDeleted = false) {

    return new Promise((resolve, reject) => {

      this.isQuoteLoading = true;
      this._http
        .post('/api/qb/quote/getQuotesList', { opportunity_id: opportunity_id, showDeleted: showDeleted })
        .pipe(
          catchError((err) => {
            this.isQuoteLoading = false;
            console.log(err);
            reject(err);
            return of({
              messText: "Couldn't get quote, try again later!",
              messType: 'E',
            });
          })
        )
        .subscribe((response) => {
          this._quotesList = response['data'] || [];
          this._quotesList.forEach((q) => {
            q.value = q.quote_value ? JSON.parse(q.quote_value) : [];
            q.flag ? (this.currentFlaggedQuote = q.quote_header_id) : '';
            // q.isApprover = true;
          });
          this._quotesListForSearch = this._quotesList;
          this._emitQuoteList();
          this.isQuoteLoading = false;
          resolve(true);
        });

    });

  }

  private _emitQuoteList() {
    this.quotesList$.next(this._quotesList);
  }

  // searchQuote(searchParam){
  //   this.quotesList$.next()
  // }

  destroy() {
    this._onDestroy.next();
    this._onDestroy.complete();
  }

  deleteQuoteFromList(quoteId) {
    this._quotesList = this._quotesList.filter(val => val['quote_header_id'] != quoteId);
    this._emitQuoteList();
  }

  updateQuoteActive(opportunityId, quoteId, isActive) {
    return this._http.post('/api/qb/quote/updateQuoteActive', {
      opportunityId: opportunityId,
      quoteId: quoteId,
      isActive: isActive
    });
  }

  updateChangeRequestQuoteActive(opportunityId, quoteId, isActive) {
    return this._http.post('/api/qb/quote/updateChangeRequestQuoteActive', {
      opportunityId: opportunityId,
      quoteId: quoteId,
      isActive: isActive
    });
  }

  updateActiveFlagInList(quoteId, isActive = 0) {

    for (const quoteItem of this._quotesList)
      quoteItem['flag'] = quoteItem['quote_header_id'] === quoteId ? isActive : 0;
    
    this._emitQuoteList();
    
  }

  updateChangeRequestFlagInList(quoteId, isActive = 0) {

    for (const quoteItem of this._quotesList)
      quoteItem['change_request_flag'] = quoteItem['quote_header_id'] === quoteId ? isActive : 0;
    
    this._emitQuoteList();
    
  }

  clearQuoteList() {

    this._quotesList = [];

    this._emitQuoteList();
    
  }

  searchQuoteList(searchParameter = null) {

    if (searchParameter) {

      const reg = new RegExp(searchParameter, "i");

      this._quotesList = this._quotesListForSearch.filter(val => reg.test(val['quote_name'])
                        || reg.test(val['last_modified_by_name']) || reg.test(val['quote_header_id']));

    }
    
    else
      this._quotesList = this._quotesListForSearch;

    this._emitQuoteList();

  }

  checkCRActivateAccess() {
    let CR_ACTIVATABLE = _.where(this.roleService.roles, { application_id: 36, object_id: 29404, operation: "*" });
    if (CR_ACTIVATABLE.length > 0) {
      return true;
    }
    else {
      return false;
    }
  }

  checkQuoteActivateAccess() {
    let QUOTE_ACTIVATION = _.where(this.roleService.roles, { application_id: 36, object_id: 29412, operation: "*" });
    if (QUOTE_ACTIVATION.length > 0) {
      return true;
    }
    else {
      return false;
    }
  }

  checkCREnabled() {
    let CR_ENABLED = _.where(this.roleService.roles, { application_id: 36, object_id: 29415, operation: "*" });
    if (CR_ENABLED.length > 0) {
      return true;
    }
    else {
      return false;
    }
  }

  getQuoteApprovers(details) {
    return this._http.post('/api/qb/quote/getQuoteApprovers', { details });
  }

  getQuoteStatus(details) {
    return this._http.post('/api/qb/quote/getReviewerStatus', { details });
  }

  submitForQuoteApproval(details: any): Observable<any> {
    return this._http.post('/api/qb/quote/submitForQuoteApproval', { details });
  }

  approveOrReject(details) {
    return this._http.post('/api/qb/quote/updateApprovalWorkFlow', { details });
  }

  checkQuoteEditableBasedOnConfig(quote_header_id) {
    return this._http.post('/api/qb/quote/checkQuoteEditableBasedOnConfig', { quote_header_id });
  }

}
