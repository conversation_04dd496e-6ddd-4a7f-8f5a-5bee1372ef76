import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable } from 'rxjs';

export type InitialQuote = {
  quoteName: string;
  currency: string;
  deliveryStartDate: string,
  deliveryEndDate: string,
  businessType: any,
  serviceTypeId: number,
  quoteType: string
};

@Injectable({
  providedIn: 'root',
})
export class QuoteMainService {
  constructor(private $http: HttpClient) { }

  // Initial quote value to be ued by the create page.

  private initialQuoteDetail$: BehaviorSubject<InitialQuote> =
    new BehaviorSubject<InitialQuote>({
      quoteName: '',
      currency: '',
      deliveryStartDate: null,
      deliveryEndDate: null,
      businessType: null,
      serviceTypeId: null,
      quoteType: ''
    });

  public intialQuoteDetail: Observable<InitialQuote> =
    this.initialQuoteDetail$.asObservable();

  setInitalQuoteDetail(initialQuote: InitialQuote) {
    this.initialQuoteDetail$.next(initialQuote);
  }

  getNationalityList() {
    return this.$http.post('/api/qb/master/nationalityList', {});
  }

  getWorkLocation() {
    return this.$http.post('/api/qb/master/workLocation', {});
  }

  getPositionList() {
    return this.$http.post('/api/qb/master/positionList', {});
  }

  getSkillList() {
    return this.$http.post('/api/qb/master/skillList', {});
  }

  getSkillExperience() {
    return this.$http.post('/api/qb/master/skillExperience', {});
  }

  getWorkExperience() {
    return this.$http.post('/api/qb/master/workExperience', {});
  }

  getUOM() {
    return this.$http.post('/api/qb/master/getUOM', {});
  }

  getCustomPercentage() {
    return this.$http.post('/api/qb/master/getCustomPercentage', {});
  }

  getTaxDetails() {
    return this.$http.post('/api/qb/master/getTaxDetails', {});
  }

  getDiscountDetails() {
    return this.$http.post('/api/qb/master/getDiscountDetails', {});
  }

  getCurrencyList() {
    return this.$http.post('/api/qb/master/getCurrencyList', {});
  }

  getServiceList(opportunityId, mandatoryFields = [], defaultCurrency, quoteCurrency, conversionTypeId) {
    return this.$http.post('/api/qb/serviceConfig/getServiceList', {
      opportunityId: opportunityId,
      mandatoryFields: mandatoryFields,
      defaultCurrency: defaultCurrency,
      quoteCurrency: quoteCurrency,
      conversionTypeId: conversionTypeId
    });
  }

  getDivision() {
    return this.$http.post('/api/qb/master/getDivision', {});
  }

  getSubDivision() {
    return this.$http.post('/api/qb/master/getSubDivision', {});
  }

  getNonManPowerList(opportunityId, defaultCurrency, quoteCurrency, conversionTypeId) {
    return this.$http.post('/api/qb/master/getNonManPowerList', {
      opportunityId: opportunityId,
      defaultCurrency: defaultCurrency,
      quoteCurrency: quoteCurrency,
      conversionTypeId: conversionTypeId
    });
  }

  createQuote(quoteDetails) {
    return this.$http.post('/api/qb/quote/createQuote', {
      quoteDetails: quoteDetails
    });
  }

  getEntity() {
    return this.$http.post('/api/qb/master/getEntity', {});
  }

  getQuoteDetails(quoteId) {
    return this.$http.post('/api/qb/quote/getQuoteDetails', {
      quoteId: quoteId
    });
  }

  getQuoteCalendarData(startDate, endDate, positionDetails, fetchSavedData = true, calendarId = 1, workScheduleId = 1) {
    return this.$http.post('/api/qb/quote/getQuoteCalendarData', {
      startDate: startDate,
      endDate: endDate,
      positionDetails: positionDetails,
      fetchSavedData: fetchSavedData,
      calendarId: calendarId,
      workScheduleId: workScheduleId
    });
  }

  getOpportunityMetaDetailsForQuote(opportunityId) {
    return this.$http.post('/api/qb/quote/getOpportunityMetaDetailsForQuote', {
      opportunityId: opportunityId
    });
  }

  updateQuote(quoteDetails) {
    return this.$http.post('/api/qb/quote/updateQuote', {
      quoteDetails: quoteDetails
    });
  }

  getQuoteActivity(quoteId, showQuoteValue = false) {
    return this.$http.post('/api/qb/quote/getQuoteActivity', {
      quoteId: quoteId,
      showQuoteValue: showQuoteValue
    });
  }

  resolveQuoteOppIntegration(opportunityId, opportunityStatus, changeType, updateValue = true, quoteDetails?) {
    return this.$http.post("/api/qb/quote/resolveQuoteOppIntegration", {
      opportunityId: opportunityId,
      opportunityStatus: opportunityStatus,
      changeType: changeType,
      updateValue: updateValue,
      quoteDetails: quoteDetails
    });
  }

  updateValueInOpportunity(opportunityId, quoteId, quoteDetails?) {
    return this.$http.post("/api/qb/quote/updateValueInOpportunity", {
      opportunityId: opportunityId,
      quoteId: quoteId,
      quoteDetails: quoteDetails
    });
  }

  getOpportunityFYData(opportunityId, opportunityCurrency,isFrom?) {
    return this.$http.post("/api/qb/quote/getOpportunityFYData", {
      opportunityId: opportunityId,
      opportunityCurrency: opportunityCurrency,
      isFrom:isFrom
    });
  }

  getQuoteConfiguration(config = []) {
    return this.$http.post('/api/qb/master/getQuoteConfiguration', {
      config: config
    });
  }

  getCurrentQuoteConfig = (configList, configName) => {

    for (const configItem of configList)
      if (configItem['quote_config_name'] === configName)
        return configItem;

    return null;

  }

  updateQuoteConfiguration(config = []) {
    return this.$http.post('/api/qb/master/updateQuoteConfiguration', {
      config: config
    });
  }

  getServices(activeService = false) {
    return this.$http.post('/api/qb/serviceConfig/getServices', {
      activeService: activeService
    });
  }

  createService(serviceName) {
    return this.$http.post('/api/qb/serviceConfig/createService', {
      serviceName: serviceName
    });
  }

  editServiceStatus(serviceId, status) {
    return this.$http.post('/api/qb/serviceConfig/editServiceStatus', {
      serviceId: serviceId,
      status: status
    });
  }

  getCustomerMaster() {
    return this.$http.post('/api/invoice/customerMasterData', {});
  }

  getLicenseConfiguration() {
    return this.$http.post('/api/qb/master/getLicenseConfiguration', {});
  }

  insertLicenseConfiguration(licenseData) {
    return this.$http.post('/api/qb/master/insertLicenseConfiguration', {
      licenseData: licenseData
    });
  }

  updateLicenseConfiguration(licenseData) {
    return this.$http.post('/api/qb/master/updateLicenseConfiguration', {
      licenseData: licenseData
    });
  }

  getLicenseList(opportunityId, defaultCurrency, quoteCurrency, conversionTypeId) {
    return this.$http.post('/api/qb/master/getLicenseList', {
      opportunityId: opportunityId,
      defaultCurrency: defaultCurrency,
      quoteCurrency: quoteCurrency,
      conversionTypeId: conversionTypeId
    });
  }

  getRate(rateFieldDetails, opportunityId, defaultCurrency, quoteCurrency, conversionTypeId) {
    return this.$http.post('/api/qb/rateCard/getRate', {
      rateFieldDetails: rateFieldDetails,
      opportunityId: opportunityId,
      defaultCurrency: defaultCurrency,
      quoteCurrency: quoteCurrency,
      conversionTypeId: conversionTypeId
    });
  }

  getQuantityForPosition(startDate, endDate, unitConfig, calendarId = 1, workScheduleId = 1) {
    return this.$http.post('/api/qb/quote/getQuantityForPosition', {
      startDate: startDate,
      endDate: endDate,
      unitConfig: unitConfig,
      calendarId: calendarId,
      workScheduleId: workScheduleId
    });
  }

  getOpportunityValueChangeLog(opportunityId) {
    return this.$http.post('/api/qb/quote/getOpportunityValueChangeLog', {
      opportunityId: opportunityId
    });
  }

  getOpportunityAuditChangeLogforField(opportunityId,fieldName) {
    return this.$http.post('/api/opportunity/getOpportunityAuditChangeLogforField', {
      opportunityId: opportunityId,
      fieldName:fieldName
    });
  }

  getOppQuoteValueStatus(opportunityId) {
    return this.$http.post('/api/qb/quote/getOppQuoteValueStatus', {
      opportunityId: opportunityId
    });
  }

  getUserFieldConfig() {
    return this.$http.post('/api/qb/quote/getUserFieldConfig', {});
  }

  updateUserFieldConfig(fieldConfig) {
    return this.$http.post('/api/qb/quote/updateUserFieldConfig', {
      fieldConfig: fieldConfig
    });
  }

  updateInActiveQuoteInOpportunity(opportunityId) {
    return this.$http.post('/api/qb/quote/updateInActiveQuoteInOpportunity', {
      opportunityId: opportunityId
    });
  }

  updateDateChangeInQuote(opportunityId, quoteId = null) {
    return this.$http.post('/api/qb/quote/updateDateChangeInQuote', {
      opportunityId: opportunityId,
      quoteId: quoteId
    });
  }

  createQuoteFromSourceQuote(sourceQuoteId, quoteName, oppDetails, copyOption = null, quoteType) {
    return this.$http.post('/api/qb/quote/createQuoteFromSourceQuote', {
      sourceQuoteId: sourceQuoteId,
      quoteName: quoteName,
      oppDetails: oppDetails,
      copyOption: copyOption,
      quoteType: quoteType
    });
  }

  updateServiceRollup(serviceId, rollupServiceId) {
    return this.$http.post('/api/qb/serviceConfig/updateServiceRollup', {
      serviceId: serviceId,
      rollupServiceId: rollupServiceId
    });
  }

  insertQuoteUOM(uomData) {
    return this.$http.post('/api/qb/master/insertQuoteUOM', {
      uomData: uomData
    });
  }

  updateQuoteUOM(uomData) {
    return this.$http.post('/api/qb/master/updateQuoteUOM', {
      uomData: uomData
    });
  }

  insertCustomPercentage(cpData) {
    return this.$http.post('/api/qb/master/insertCustomPercentage', {
      cpData: cpData
    });
  }

  updateCustomPercentage(cpData) {
    return this.$http.post('/api/qb/master/updateCustomPercentage', {
      cpData: cpData
    });
  }

  getOpportunityQuoteList(opportunityId, limit, offset, searchQuery) {
    return this.$http.post('/api/qb/quote/getOpportunityQuoteList', {
      opportunityId: opportunityId,
      limit: limit,
      offset: offset,
      searchQuery: searchQuery
    });
  }

  getOppStatusConfig() {
    return this.$http.post('/api/qb/master/getOppStatusConfig', {});
  }

  updateOppStatusConfig(statusConfig) {
    return this.$http.post('/api/qb/master/updateOppStatusConfig', {
      statusConfig: statusConfig
    });
  }

  insertDiscount(discountData) {
    return this.$http.post('/api/qb/master/insertDiscount', {
      discountData: discountData
    });
  }

  updateDiscount(discountData) {
    return this.$http.post('/api/qb/master/updateDiscount', {
      discountData: discountData
    });
  }

  insertTax(taxData) {
    return this.$http.post('/api/qb/master/insertTax', {
      taxData: taxData
    });
  }

  updateTax(taxData) {
    return this.$http.post('/api/qb/master/updateTax', {
      taxData: taxData
    });
  }

  deleteQuote(quoteId, opportunityId) {
    return this.$http.post('/api/qb/quote/deleteQuote', {
      quoteId: quoteId,
      opportunityId: opportunityId
    });
  }

  getQuoteAccessPrivilege(opportunityId) {
    return this.$http.post('/api/qb/quote/getQuoteAccessPrivilege', {
      opportunityId: opportunityId
    });
  }

  getQuotePosition() {
    return this.$http.post('/api/qb/master/getQuotePosition', {});
  }
  
  insertQuotePosition(positionData) {
    return this.$http.post('/api/qb/master/insertQuotePosition', {
      positionData: positionData
    });
  }

  updateQuotePosition(positionData) {
    return this.$http.post('/api/qb/master/updateQuotePosition', {
      positionData: positionData
    });
  }

  getQuoteWorkLocationEntityMapping() {
    return this.$http.post('/api/qb/master/getQuoteWorkLocationEntityMapping', {});
  }

  getRevenueRegionEntitySalesMappingList() {
    return this.$http.post('/api/qb/master/getRevenueRegionEntitySalesMappingList', {});
  }
  
  insertQuoteWorkLocationEntityMapping(mappingData) {
    return this.$http.post('/api/qb/master/insertQuoteWorkLocationEntityMapping', {
      mappingData: mappingData
    });
  }

  updateQuoteWorkLocationEntityMapping(mappingData) {
    return this.$http.post('/api/qb/master/updateQuoteWorkLocationEntityMapping', {
      mappingData: mappingData
    });
  }

  insertQuoteEntitySalesRegionRevenueRegionMapping(mappingData) {
    return this.$http.post('/api/qb/master/insertQuoteEntitySalesRegionRevenueRegionMapping', {
      mappingData: mappingData
    });
  }

  updateQuoteEntitySalesRegionRevenueRegionMapping(mappingData) {
    return this.$http.post('/api/qb/master/updateQuoteEntitySalesRegionRevenueRegionMapping', {
      mappingData: mappingData
    });
  }

  getQuoteServiceDivisionMapping() {
    return this.$http.post('/api/qb/master/getQuoteServiceDivisionMapping', {});
  }
  
  insertQuoteServiceDivisionMapping(mappingData) {
    return this.$http.post('/api/qb/master/insertQuoteServiceDivisionMapping', {
      mappingData: mappingData
    });
  }

  updateQuoteServiceDivisionMapping(mappingData) {
    return this.$http.post('/api/qb/master/updateQuoteServiceDivisionMapping', {
      mappingData: mappingData
    });
  }
  
  getMonthlyRevenueProjection() {
    return this.$http.post('/api/qb/quote/getMonthlyRevenueProjection', {
    });
  }

  getRcFieldConfiguration(rcType) {
    return this.$http.post('/api/qb/master/getRcFieldConfiguration', {
      rcType: rcType
    });
  }

  updateRcFieldConfig(fieldConfig) {
    return this.$http.post('/api/qb/master/updateRcFieldConfig', {
      fieldConfig: fieldConfig
    });
  }
  
  getRateCardConfigurationDataConfig() {
    return this.$http.post('/api/qb/master/getRateCardConfigurationDataConfig', {});
  }
  
  getRCMasterDataList(id) {
    return this.$http.post('/api/qb/master/getRCMasterDataList', {id:id});
  }

  insertRCMasterData(data,tableData) {
    return this.$http.post('/api/qb/master/insertRCMasterData', {data:data,tableData:tableData});
  }

  updateRCMasterData(data,tableData) {
    return this.$http.post('/api/qb/master/updateRCMasterData', {data:data,tableData:tableData});
  }

  getSBHBProjection() {
    return this.$http.post('/api/qb/quote/getSBHBProjection', {
    });
  }

  getNonEditableStatus() {
    return this.$http.post('/api/qb/master/getNonEditableStatus', {});
  }

  getFinancialDocumentConfig() {
    return this.$http.post('/api/opportunity/FD/config', {});
  }

  getFinancialDocumentSalesOrgConfig() {
    return this.$http.post('/api/opportunity/FD/approver/sales-org-config', {});
  }

  getDocUploadStageWiseConfig() {
    return this.$http.post('/api/opportunity/FD/getDocUploadStageWiseConfig', {});
  }
  
  stageWiseDocUploadUpdate(updatedRow: any){
    return this.$http.post('/api/opportunity/FD/stageWiseDocUploadUpdate', { config: updatedRow });
  }
  
  getOpportunityDocumentTypes = async () => {
    try {
      const response = await this.$http.post("/api/opportunity/getOpportunityDocumentTypes", {}).toPromise();
      return response;
    } catch (error) {
      console.error("Error retrieving document types:", error);
      throw error;
    }
  };

  updateFinancialDocApproverConfig(updatedRow: any){
    return this.$http.post('/api/opportunity/FD/config/update', { config: updatedRow });
  }

  updateQuoteActivationConfig(updatedRow: any){
    return this.$http.post('/api/qb/quote/updateQuoteActivationConfig', { config: updatedRow });
  }

  getQuoteActivationApproverConfig(){
    return this.$http.post('/api/qb/quote/getQuoteActivationApproverConfig', {});
  }

  updateFinancialDocSalesOrgApproverConfig(updatedRow: any){
    return this.$http.post('/api/opportunity/FD/approver/sales-org-config/update', { config: updatedRow });
  }

  getAllMembers(){
    return this.$http.post('/api/salesMaster/getAllUserFromDB', { config: {} });
  }
  
  getSalesRegionMaster(){
    return this.$http.post('/api/opportunity/getSalesRegionMaster', { config: {} });
  }

  getCalendarCombinationConfig() {
    return this.$http.post('/api/qb/quote/getCalendarCombinationConfig', {});
  }  

  getRevenueRegionCombinationConfig(){
    return this.$http.post('/api/qb/quote/getRevenueRegionCombinationConfig', {});
  }  
  
  checkMilestoneApplicable(oppId, quoteId) {
    return this.$http.post('/api/qb/quote/checkOpportunityIsApplicableForMileStone', { opportunity_id: oppId, quote_id: quoteId });
  }
  
  getMilestoneListQB(quotePositionIds) {
    return this.$http.post('/api/qb/master/getMilestoneList', {quote_position_ids: quotePositionIds});
  }  
  
  checkForPercentageApproval(quote,is_from_edit=false): Promise<any> {
    return new Promise((resolve, reject) => {
      this.$http.post("/api/qb/quote/checkForPercentageApproval", { quote ,is_from_edit:is_from_edit})
        .subscribe(
          res => {
            if (res['messType'] === 'S') {
              resolve(res);
            } else {
              // Uncomment to show error if needed
              // this._toaster.showError("Mandatory Field Error", "Kindly enter Quote Name", 2000);
              resolve(false);
            }
          },
          error => {
            reject(error); // Reject in case of an HTTP error
          }
        );
    });
  }

  checkQuoteAllocation(quote_header_id): Promise<any> {
    return new Promise((resolve, reject) => {
      this.$http.post('/api/qb/quote/checkAllocation', {quote_header_id: quote_header_id})
        .subscribe(
          res => {
            if (res) {
              resolve(res);
            } else {
              // Uncomment to show error if needed
              // this._toaster.showError("Mandatory Field Error", "Kindly enter Quote Name", 2000);
              resolve(false);
            }
          },
          error => {
            reject(error); // Reject in case of an HTTP error
          }
        );
    });
  }

  private formActionSubject = new BehaviorSubject<{ action: string, rcType: string | null, rcLabel: string | null,forUpdate?: Object | null  }>(
    this.getStoredFormAction()
  );

  formAction$ = this.formActionSubject.asObservable();

  setFormAction(action?: string, rcType?: string | null, rcLabel?: string | null, forUpdate?: Object | null) {
    const updatedValue = { action, rcType, rcLabel, forUpdate };
    this.formActionSubject.next(updatedValue);
    localStorage.setItem('formAction', JSON.stringify(updatedValue)); // Store in local storage
  }

  private getStoredFormAction() {
    const stored = localStorage.getItem('formAction');
    return stored ? JSON.parse(stored) : { action: '', rcType: null, rcLabel: null };
  }

}
