<ng-container>

    <!-- Side Navigation Properties On Open -->
    <mat-sidenav-container [hasBackdrop]="false" class="example-container">
        <mat-sidenav #sidenav mode="over" [(opened)]="isSidenavOpened" class="example-sidenav" position="end">

            <div class="side-nav-header">
                <span class="side-nav-header-name">Access Reports</span>
                <mat-icon (click)="toggleSidenav()" class="side-nav-header-icon" aria-label="Close"
                    matTooltip="Click to close the Report Tab">close</mat-icon>
            </div>

            <!-- Side Navigation Report list -->
            <div style="border-bottom: 1px solid #dadce2; width: 100%;"></div>
            <div class="report-list">
                <div *ngFor="let report of filteredReports" class="report-list-item"
                    [ngClass]="{'highlight': report.id == selectedReportId}"
                    (click)="selectReport(report.id, 1); toggleSidenav()">
                    <div class="variant-list-view">
                        <div class="report-name-backgrnd">
                            <svg width="25" height="24" viewBox="0 0 25 24" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M22.1111 22H2.11108C1.70108 22 1.36108 21.66 1.36108 21.25C1.36108 20.84 1.70108 20.5 2.11108 20.5H22.1111C22.5211 20.5 22.8611 20.84 22.8611 21.25C22.8611 21.66 22.5211 22 22.1111 22Z"
                                    fill="#292D32" />
                                <path
                                    d="M9.86108 4V22H14.3611V4C14.3611 2.9 13.9111 2 12.5611 2H11.6611C10.3111 2 9.86108 2.9 9.86108 4Z"
                                    fill="#292D32" />
                                <path opacity="0.4"
                                    d="M3.11108 10V22H7.11108V10C7.11108 8.9 6.71108 8 5.51108 8H4.71108C3.51108 8 3.11108 8.9 3.11108 10Z"
                                    fill="#292D32" />
                                <path opacity="0.4"
                                    d="M17.1111 15V22H21.1111V15C21.1111 13.9 20.7111 13 19.5111 13H18.7111C17.5111 13 17.1111 13.9 17.1111 15Z"
                                    fill="#292D32" />
                            </svg>
                        </div>
                        <span class="variant-list-view" [ngClass]="{'highlighted-text':report.id == selectedReportId}">
                            {{report.name }}
                        </span>
                    </div>
                    <span class="activity-status" *ngIf="report.id == selectedReportId">
                        <span style="align-items: center;">Active</span>
                    </span>
                    <span *ngIf="report.id != selectedReportId" style="margin-right: 11px;">

                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect x="0.5" y="0.5" width="23" height="23" rx="7.5" stroke="#45546E" />
                            <g clip-path="url(#clip0_1527_468)">
                                <path
                                    d="M12.7814 12L9.48145 8.69999L10.4241 7.75732L14.6668 12L10.4241 16.2427L9.48145 15.3L12.7814 12Z"
                                    fill="#45546E" />
                            </g>
                            <defs>
                                <clipPath id="clip0_1527_468">
                                    <rect width="16" height="16" fill="white" transform="translate(4 4)" />
                                </clipPath>
                            </defs>
                        </svg>

                    </span>
                </div>
            </div>
        </mat-sidenav>

        <mat-sidenav #variantSidenav mode="over" [(opened)]="isVariantSidenavOpened" class="example-sidenav"
            position="end">
            <div class="side-nav-header">
                <span class="side-nav-header-name">Variants</span>
                <mat-icon (click)="toggleVariantSidenav()" class="side-nav-header-icon"
                    matTooltip="Click to close the Variant Tab" aria-label="Close">close</mat-icon>
            </div>

            <!-- Expand/Collapse Buttons -->
            <!-- <div class="expand-collapse-actions">
                <button mat-button (click)="expandAll()">Expand All</button>
                <button mat-button (click)="collapseAll()">Collapse All</button>
              </div> -->

              <!-- <mat-accordion #accordion multi>
                <mat-expansion-panel #defaultVariantsPanel>
                  <mat-expansion-panel-header>
                    <mat-panel-title>Default Variants</mat-panel-title>
                  </mat-expansion-panel-header>
                  <div class="report-list">
                    <div *ngFor="let name of defaultViews?.allViews; let i = index" class="report-item"
                        [ngClass]="{'highlight': i == currentView}" (click)="changeView(i); toggleVariantSidenav()">
                        <div class="report-info">
                            <span class="variant-list-view" [ngClass]="{'highlighted-text': i == currentView}">
                                {{name?.config_name }}</span>
                            <div *ngIf="i == currentView" class="current-view-name">
                                Current View
                            </div>
                        </div>
    
                        <mat-icon *ngIf="i == currentView" class="delete-icon" (click)="deleteVariant(i)"
                            matTooltip="Click to delete this variant">
                            <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px"
                                fill="#5f6368">
                                <path
                                    d="M280-120q-33 0-56.5-23.5T200-200v-520h-40v-80h200v-40h240v40h200v80h-40v520q0 33-23.5 56.5T680-120H280Zm400-600H280v520h400v-520ZM360-280h80v-360h-80v360Zm160 0h80v-360h-80v360ZM280-720v520-520Z" />
                            </svg>
                        </mat-icon>
                    </div>
                    <div *ngIf="defaultViews?.allViews?.length === 0" class="no-data-found">
                        <svg width="248" height="185" viewBox="0 0 248 185" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M247.798 162.281H0V162.405H247.798V162.281Z" fill="#EBEBEB" />
                            <path d="M222.969 170.258H215.584V170.382H222.969V170.258Z" fill="#EBEBEB" />
                            <path d="M164.151 171.603H159.843V171.727H164.151V171.603Z" fill="#EBEBEB" />
                            <path d="M213.491 165.656H196.546V165.78H213.491V165.656Z" fill="#EBEBEB" />
                            <path d="M54.332 168.213H32.926V168.337H54.332V168.213Z" fill="#EBEBEB" />
                            <path d="M66.4651 168.213H58.7451V168.337H66.4651V168.213Z" fill="#EBEBEB" />
                            <path d="M126.255 166.55H79.8301V166.674H126.255V166.55Z" fill="#EBEBEB" />
                            <path
                                d="M117.463 140.178H21.7631C21.013 140.177 20.2939 139.879 19.7636 139.349C19.2332 138.818 18.9349 138.099 18.9341 137.349V2.828C18.9351 2.07812 19.2336 1.35927 19.7639 0.829121C20.2942 0.298968 21.0132 0.000793618 21.7631 0H117.463C118.213 0.00105816 118.931 0.299348 119.462 0.829472C119.992 1.3596 120.29 2.07829 120.291 2.828V137.347C120.291 138.097 119.993 138.817 119.463 139.347C118.932 139.878 118.213 140.177 117.463 140.178ZM21.7641 0.124001C21.0486 0.126111 20.3631 0.411253 19.8572 0.917145C19.3513 1.42304 19.0662 2.10857 19.0641 2.824V137.347C19.0662 138.062 19.3513 138.748 19.8572 139.254C20.3631 139.76 21.0486 140.045 21.7641 140.047H117.464C118.18 140.045 118.865 139.76 119.371 139.254C119.877 138.748 120.162 138.062 120.164 137.347V2.828C120.162 2.11256 119.877 1.42703 119.371 0.921143C118.865 0.415251 118.18 0.130113 117.464 0.128002L21.7641 0.124001Z"
                                fill="#EBEBEB" />
                            <path
                                d="M224.659 140.178H128.959C128.209 140.177 127.49 139.879 126.96 139.348C126.43 138.818 126.132 138.099 126.131 137.349V2.828C126.132 2.07821 126.43 1.35936 126.96 0.829182C127.49 0.299001 128.209 0.000794128 128.959 0H224.659C225.409 0.000793618 226.128 0.298968 226.658 0.829121C227.189 1.35927 227.487 2.07812 227.488 2.828V137.347C227.488 138.097 227.19 138.817 226.659 139.348C226.129 139.879 225.41 140.177 224.659 140.178ZM128.959 0.124001C128.244 0.126111 127.558 0.411253 127.052 0.917145C126.546 1.42304 126.261 2.10857 126.259 2.824V137.347C126.261 138.062 126.546 138.748 127.052 139.254C127.558 139.76 128.244 140.045 128.959 140.047H224.659C225.375 140.046 226.062 139.762 226.569 139.256C227.076 138.75 227.362 138.063 227.364 137.347V2.828C227.362 2.1117 227.076 1.42542 226.569 0.919384C226.062 0.41335 225.375 0.128793 224.659 0.128002L128.959 0.124001Z"
                                fill="#EBEBEB" />
                            <path d="M106.293 162.28H115.522L115.522 98.908H106.293V162.28Z" fill="#E6E6E6" />
                            <path d="M109.542 162.28H106.293V150.24H112.948L109.542 162.28Z" fill="#F0F0F0" />
                            <path d="M203.887 162.28H213.116V98.908H203.887V162.28Z" fill="#E6E6E6" />
                            <path d="M106.293 158.993H206.722V98.908L106.293 98.908L106.293 158.993Z" fill="#F0F0F0" />
                            <path d="M203.477 162.28H206.726V150.24H200.067L203.477 162.28Z" fill="#F0F0F0" />
                            <path d="M173.612 103.883V153.587H203.383V103.883H173.612Z" fill="#E6E6E6" />
                            <path
                                d="M185.553 105.631H191.443C193.936 105.656 196.348 104.75 198.207 103.089H178.79C180.649 104.75 183.061 105.656 185.553 105.631Z"
                                fill="#F0F0F0" />
                            <path d="M141.622 103.883V153.587H171.393V103.883H141.622Z" fill="#E6E6E6" />
                            <path
                                d="M153.262 105.631H159.152C161.644 105.656 164.056 104.75 165.915 103.089H146.498C148.357 104.75 150.769 105.656 153.262 105.631Z"
                                fill="#F0F0F0" />
                            <path d="M109.632 103.883V153.587H139.403V103.883H109.632Z" fill="#E6E6E6" />
                            <path
                                d="M121.272 105.631H127.162C129.655 105.656 132.067 104.75 133.926 103.089H114.508C116.367 104.75 118.779 105.656 121.272 105.631Z"
                                fill="#F0F0F0" />
                            <path d="M34.6819 162.28H97.6519L97.6519 20.728H34.6819L34.6819 162.28Z" fill="#F0F0F0" />
                            <path d="M34.682 162.28H96.323L96.323 20.728H34.682L34.682 162.28Z" fill="#F5F5F5" />
                            <path d="M92.5669 159.068L92.5669 23.94L38.4379 23.94L38.4379 159.068H92.5669Z"
                                fill="#F0F0F0" />
                            <path
                                d="M87.2478 49.071C87.2478 47.1331 86.8661 45.2141 86.1245 43.4237C85.3829 41.6333 84.2959 40.0065 82.9256 38.6362C81.5553 37.2659 79.9285 36.1789 78.1381 35.4373C76.3477 34.6957 74.4287 34.314 72.4908 34.314H58.5148C54.601 34.314 50.8475 35.8687 48.08 38.6362C45.3126 41.4037 43.7578 45.1572 43.7578 49.071H87.2478Z"
                                fill="#FAFAFA" />
                            <path d="M87.2478 150.669V118.387H43.7578V150.669H87.2478Z" fill="#F5F5F5" />
                            <path d="M87.2479 58.173H68.0649V114.222H87.2479V58.173Z" fill="#F5F5F5" />
                            <path d="M62.9408 58.173H43.7578V114.222H62.9408V58.173Z" fill="#F5F5F5" />
                            <path
                                d="M50.7149 85.945H42.8579C42.5349 85.9442 42.2253 85.8155 41.9968 85.5871C41.7684 85.3586 41.6397 85.0491 41.6389 84.726C41.64 84.403 41.7687 84.0936 41.9971 83.8652C42.2255 83.6368 42.5349 83.5081 42.8579 83.507H50.7149C51.0379 83.5081 51.3473 83.6368 51.5757 83.8652C51.8041 84.0936 51.9329 84.403 51.9339 84.726C51.9331 85.0491 51.8044 85.3586 51.576 85.5871C51.3476 85.8155 51.038 85.9442 50.7149 85.945Z"
                                fill="#E0E0E0" />
                            <path d="M39.4128 159.068L39.4128 23.94H38.4388L38.4388 159.068H39.4128Z" fill="#F0F0F0" />
                            <path d="M137.482 83.372L204.261 83.372V20.728L137.482 20.728V83.372Z" fill="#E6E6E6" />
                            <path d="M135.799 83.372L202.578 83.372V20.728L135.799 20.728V83.372Z" fill="#F5F5F5" />
                            <path d="M131.9 86.998L204.261 86.998V83.372L131.9 83.372V86.998Z" fill="#E6E6E6" />
                            <path d="M130.005 86.998L198.639 86.998V83.372L130.005 83.372V86.998Z" fill="#F5F5F5" />
                            <path d="M199.556 80.114V23.987L138.82 23.987V80.114H199.556Z" fill="white" />
                            <path d="M170.871 80.114V23.987H167.504V80.114H170.871Z" fill="#F5F5F5" />
                            <path d="M140.093 80.114V23.987H138.82V80.114H140.093Z" fill="#E6E6E6" />
                            <path d="M172.144 80.114V23.987H170.871V80.114H172.144Z" fill="#E6E6E6" />
                            <path
                                d="M146.459 85.033C145.231 72.308 144.376 51.659 145.04 44.753C145.051 44.6343 145.088 44.5194 145.147 44.416C145.207 44.3126 145.287 44.2229 145.384 44.153C145.512 44.059 145.664 44.003 145.823 43.9914C145.982 43.9797 146.141 44.0129 146.281 44.0871C146.422 44.1614 146.539 44.2736 146.619 44.4112C146.699 44.5487 146.739 44.706 146.734 44.865C146.617 48.287 146.482 56.84 147.211 71.595C147.574 78.94 148.097 83.97 147.738 85.031L146.459 85.033Z"
                                fill="#E6E6E6" />
                            <path
                                d="M141.881 55.704C138.381 51.821 131.242 49.06 123.964 49.074C121.107 49.08 132.657 53.591 137.464 58.574C141.305 62.712 144.467 67.4321 146.833 72.559C145.905 64.954 145.378 59.585 141.881 55.704Z"
                                fill="#E6E6E6" />
                            <path
                                d="M148.813 56.886C151.713 52.842 158.42 49.732 165.673 49.38C168.52 49.242 157.687 54.317 153.644 59.519C150.419 63.8669 147.967 68.7382 146.397 73.919C146.185 66.306 145.909 60.931 148.813 56.886Z"
                                fill="#E6E6E6" />
                            <path
                                d="M141.725 41.522C138.764 38.234 132.716 35.896 126.553 35.908C124.134 35.908 133.914 39.734 137.981 43.949C141.234 47.4528 143.912 51.4496 145.915 55.791C145.133 49.357 144.684 44.81 141.725 41.522Z"
                                fill="#E6E6E6" />
                            <path
                                d="M147.594 46.385C150.053 42.96 155.73 40.327 161.871 40.029C164.282 39.912 155.109 44.209 151.685 48.614C148.953 52.2975 146.877 56.4247 145.548 60.814C145.37 54.362 145.135 49.808 147.594 46.385Z"
                                fill="#E6E6E6" />
                            <path
                                d="M146.953 98.908C145.108 98.908 143.332 98.2022 141.99 96.9353C140.648 95.6684 139.842 93.9363 139.736 92.094L139.098 81.031H154.808L154.17 92.094C154.064 93.9363 153.257 95.6684 151.916 96.9353C150.574 98.2022 148.798 98.908 146.953 98.908Z"
                                fill="#E6E6E6" />
                            <path d="M155.825 83.546H138.084L137.576 78.946H156.337L155.825 83.546Z" fill="#E6E6E6" />
                            <path
                                d="M123.899 184.664C176.969 184.664 219.99 182.151 219.99 179.052C219.99 175.953 176.969 173.44 123.899 173.44C70.8295 173.44 27.8081 175.953 27.8081 179.052C27.8081 182.151 70.8295 184.664 123.899 184.664Z"
                                fill="#F5F5F5" />
                            <path d="M125.838 173.689L121.7 174.155L120.174 164.771L124.312 164.305L125.838 173.689Z"
                                fill="#FFC3BD" />
                            <path d="M144.364 174.593H140.232L139.203 165.024H143.335L144.364 174.593Z" fill="#FFC3BD" />
                            <path
                                d="M139.892 174.112H144.929C145.011 174.112 145.091 174.14 145.155 174.191C145.219 174.243 145.263 174.314 145.281 174.394L146.097 178.067C146.116 178.157 146.115 178.249 146.093 178.338C146.072 178.427 146.03 178.51 145.972 178.58C145.914 178.651 145.84 178.708 145.757 178.746C145.674 178.784 145.584 178.804 145.492 178.802C143.871 178.774 142.692 178.679 140.648 178.679C139.392 178.679 135.604 178.809 133.87 178.809C132.17 178.809 131.909 177.094 132.619 176.939C135.803 176.239 138.202 175.282 139.219 174.366C139.404 174.201 139.644 174.11 139.892 174.112Z"
                                fill="#263238" />
                            <path
                                d="M121.632 173.446L126.116 172.938C126.197 172.928 126.28 172.947 126.349 172.991C126.419 173.034 126.471 173.1 126.498 173.178L127.723 176.735C127.754 176.821 127.765 176.913 127.755 177.004C127.745 177.095 127.714 177.182 127.665 177.259C127.616 177.336 127.55 177.401 127.472 177.449C127.394 177.497 127.306 177.526 127.215 177.535C125.595 177.691 123.247 177.861 121.215 178.092C118.838 178.362 118.442 178.998 115.638 178.933C113.938 178.893 113.512 177.168 114.225 177.033C117.473 176.402 118.101 175.911 120.682 173.885C120.952 173.653 121.281 173.501 121.632 173.446Z"
                                fill="#263238" />
                            <path
                                d="M125.282 53.177C123.894 54.323 122.528 55.319 121.115 56.339C119.702 57.359 118.259 58.306 116.774 59.225C115.289 60.144 113.765 61.025 112.167 61.825C110.533 62.6713 108.833 63.3811 107.082 63.947C106.619 64.095 106.168 64.217 105.648 64.347C105.14 64.4572 104.626 64.5397 104.108 64.594C103.177 64.694 102.301 64.739 101.428 64.779C99.6881 64.859 97.9811 64.857 96.2761 64.845C92.8661 64.804 89.4851 64.64 86.0891 64.34L86.0381 61.24C89.3731 60.811 92.7231 60.44 96.0381 60.067C97.6951 59.887 99.3481 59.685 100.963 59.474C101.763 59.365 102.563 59.237 103.301 59.1C103.618 59.044 103.932 58.9702 104.241 58.879C104.532 58.779 104.889 58.665 105.219 58.532C106.636 57.9566 108.012 57.2837 109.336 56.518C110.707 55.731 112.079 54.886 113.427 53.988C114.775 53.09 116.117 52.16 117.447 51.211C118.777 50.262 120.106 49.257 121.354 48.311L125.282 53.177Z"
                                fill="#FFC3BD" />
                            <path
                                d="M129.321 49.645C128.727 53.551 120.503 59.526 120.503 59.526L114.603 52.203C117.188 49.4447 119.928 46.8368 122.811 44.392C125.466 42.222 129.967 45.395 129.321 49.645Z"
                                fill="#526179" />
                            <path
                                d="M87.176 61.567L84.83 59.897L84.573 65.173C84.9276 65.1739 85.2771 65.088 85.5909 64.9228C85.9046 64.7575 86.1732 64.518 86.373 64.225L87.176 61.567Z"
                                fill="#FFC3BD" />
                            <path d="M82.711 58.361L82.231 63.532L84.575 65.173L84.832 59.897L82.711 58.361Z"
                                fill="#FFC3BD" />
                            <path opacity="0.2"
                                d="M120.176 164.774L120.962 169.611L125.101 169.145L124.315 164.308L120.176 164.774Z"
                                fill="black" />
                            <path opacity="0.2" d="M143.337 165.025H139.203L139.735 169.957H143.869L143.337 165.025Z"
                                fill="black" />
                            <path
                                d="M143.396 44.517C143.704 44.586 143.995 44.7159 144.251 44.899C144.508 45.0821 144.726 45.3147 144.891 45.5831C145.057 45.8516 145.167 46.1505 145.216 46.4621C145.264 46.7738 145.249 47.092 145.173 47.398C144.276 51.1195 143.601 54.8907 143.149 58.692C142.758 61.892 142.524 64.949 142.378 67.72C142.035 74.215 142.178 79.166 141.984 80.844C138.753 80.62 126.577 79.773 119.938 79.309C117.697 61.765 120.323 50.569 121.798 46.02C122.024 45.3112 122.446 44.6811 123.016 44.2031C123.587 43.7251 124.281 43.4188 125.018 43.32C125.864 43.209 126.876 43.102 127.944 43.039C128.318 43.015 128.697 43.002 129.083 42.994C132.217 43.0311 135.347 43.2488 138.456 43.646C139.046 43.716 139.642 43.803 140.222 43.896C141.381 44.09 142.483 44.313 143.396 44.517Z"
                                fill="#6E7B8F" />
                            <path
                                d="M138.265 33.033C137.465 36.007 136.487 41.491 138.457 43.651C133.347 47.088 130.085 51.927 129.144 50.876C128.651 50.326 128.437 43.961 129.085 42.997C132.504 42.429 132.591 39.928 132.169 37.544L138.265 33.033Z"
                                fill="#FFC3BD" />
                            <path
                                d="M138.061 42.418C138.061 42.418 129.734 46.103 128.63 54.343C130.924 51.687 135.991 48.797 135.991 48.797L133.756 48.155C135.177 47.6156 136.649 47.2196 138.149 46.973C137.901 45.835 139.223 43.565 139.223 43.565L138.061 42.418Z"
                                fill="#526179" />
                            <path
                                d="M130.548 42.285C130.548 42.285 131.658 46.991 128.631 54.343C128.034 51.7769 127.216 49.2675 126.185 46.843L128.224 47.398C128.224 47.398 128.124 46.449 126.473 45.187C127.161 44.1877 128.019 43.3168 129.008 42.614L130.548 42.285Z"
                                fill="#526179" />
                            <path opacity="0.2"
                                d="M135.78 34.874L132.172 37.542C132.279 38.0972 132.337 38.6607 132.345 39.226C133.645 39.126 135.505 37.843 135.736 36.508C135.85 35.9705 135.864 35.4169 135.78 34.874Z"
                                fill="black" />
                            <path
                                d="M127.684 24.421C125.669 24.508 124.557 27.982 126.602 30.843C128.647 33.704 130.399 24.304 127.684 24.421Z"
                                fill="#263238" />
                            <path
                                d="M137.621 27.478C137.86 31.572 138.208 33.955 136.406 36.288C133.696 39.797 128.456 38.539 126.999 34.599C125.688 31.053 125.517 24.954 129.299 22.859C130.131 22.3926 131.068 22.1467 132.022 22.1447C132.976 22.1426 133.914 22.3845 134.748 22.8474C135.582 23.3103 136.284 23.9787 136.787 24.7892C137.289 25.5997 137.577 26.5253 137.621 27.478Z"
                                fill="#FFC3BD" />
                            <path
                                d="M138.222 30.624C137.127 29.9823 136.171 29.1291 135.41 28.1143C134.648 27.0995 134.096 25.9433 133.785 24.713C132.585 24.997 126.604 27.749 124.765 24.713C122.926 21.677 125.24 19.896 128.681 21.765C127.159 19.079 128.543 17.765 133.325 17.649C138.107 17.533 137.464 20.305 137.464 20.305C137.464 20.305 141.084 18.729 142.364 21.505C143.847 24.695 141.111 30.142 138.222 30.624Z"
                                fill="#263238" />
                            <path
                                d="M136.646 19.572C136.69 19.572 138.457 19.997 140.493 17.948C140.128 19.696 136.966 20.86 136.966 20.86L136.646 19.572Z"
                                fill="#263238" />
                            <path
                                d="M139.022 25.561C137.115 24.902 134.806 27.727 135.659 31.138C136.512 34.549 141.591 26.449 139.022 25.561Z"
                                fill="#263238" />
                            <path
                                d="M140.101 30.883C139.926 31.8997 139.356 32.8058 138.515 33.403C137.394 34.185 136.384 33.291 136.315 31.997C136.257 30.833 136.769 29.023 138.076 28.756C138.363 28.7024 138.658 28.7213 138.936 28.8111C139.214 28.9008 139.465 29.0585 139.666 29.2698C139.867 29.4812 140.012 29.7394 140.088 30.0212C140.164 30.3029 140.169 30.5992 140.101 30.883Z"
                                fill="#FFC3BD" />
                            <path
                                d="M126.152 125.863C127.145 113.777 134.429 80.32 134.429 80.32L119.935 79.309C119.935 79.309 114.346 112.52 113.918 125.124C113.472 138.229 118.743 167.355 118.743 167.355L125.694 166.564C125.694 166.564 125.155 137.999 126.152 125.863Z"
                                fill="#526179" />
                            <path opacity="0.5"
                                d="M126.152 125.863C127.145 113.777 134.429 80.32 134.429 80.32L119.935 79.309C119.935 79.309 114.346 112.52 113.918 125.124C113.472 138.229 118.743 167.355 118.743 167.355L125.694 166.564C125.694 166.564 125.155 137.999 126.152 125.863Z"
                                fill="white" />
                            <path opacity="0.2"
                                d="M128.969 91.65C126.047 95.386 125.995 108.507 127.125 118.201C128.202 111.208 129.899 102.327 131.386 94.924L128.969 91.65Z"
                                fill="black" />
                            <path
                                d="M127.756 79.854C127.756 79.854 129.32 115.229 131.065 127.209C132.975 140.328 137.826 169.001 137.826 169.001H145.394C145.394 169.001 144.149 141.995 143.15 129.106C142.013 114.425 141.982 80.846 141.982 80.846L127.756 79.854Z"
                                fill="#526179" />
                            <path opacity="0.5"
                                d="M127.756 79.854C127.756 79.854 129.32 115.229 131.065 127.209C132.975 140.328 137.826 169.001 137.826 169.001H145.394C145.394 169.001 144.149 141.995 143.15 129.106C142.013 114.425 141.982 80.846 141.982 80.846L127.756 79.854Z"
                                fill="white" />
                            <path d="M136.916 169.162H145.827V166.5L135.866 166.327L136.916 169.162Z" fill="#526179" />
                            <path d="M118.44 168.138L126.724 167.199L126.424 164.522L117.147 165.431L118.44 168.138Z"
                                fill="#526179" />
                            <path
                                d="M131.593 29.355C131.633 29.686 131.493 29.975 131.273 30.001C131.053 30.027 130.849 29.78 130.81 29.449C130.771 29.118 130.91 28.829 131.13 28.803C131.35 28.777 131.554 29.024 131.593 29.355Z"
                                fill="#263238" />
                            <path
                                d="M127.854 29.804C127.894 30.135 127.754 30.424 127.534 30.45C127.314 30.476 127.11 30.229 127.07 29.898C127.03 29.567 127.17 29.278 127.39 29.252C127.61 29.226 127.814 29.477 127.854 29.804Z"
                                fill="#263238" />
                            <path
                                d="M129.062 29.826C128.789 30.8847 128.372 31.9009 127.822 32.846C128.067 32.9804 128.339 33.0596 128.618 33.0781C128.897 33.0966 129.177 33.0539 129.438 32.953L129.062 29.826Z"
                                fill="#ED847E" />
                            <path
                                d="M132.61 28.006C132.575 28.0165 132.538 28.0173 132.503 28.0083C132.468 27.9993 132.436 27.9809 132.41 27.955C132.245 27.7731 132.038 27.633 131.809 27.5461C131.579 27.4592 131.331 27.428 131.087 27.455C131.036 27.4636 130.984 27.4522 130.941 27.4231C130.898 27.394 130.869 27.3495 130.858 27.299C130.853 27.2737 130.853 27.2476 130.858 27.2222C130.863 27.1969 130.873 27.1728 130.887 27.1515C130.902 27.1301 130.921 27.1119 130.942 27.0979C130.964 27.0839 130.988 27.0744 131.014 27.07C131.322 27.03 131.636 27.0648 131.928 27.1713C132.22 27.2778 132.483 27.4529 132.693 27.682C132.729 27.7192 132.749 27.7689 132.749 27.8205C132.749 27.8721 132.729 27.9218 132.693 27.959C132.67 27.9814 132.641 27.9975 132.61 28.006Z"
                                fill="#263238" />
                            <path
                                d="M131.173 34.273C131.645 34.1737 132.086 33.9623 132.459 33.6567C132.832 33.3511 133.126 32.9602 133.316 32.517C133.325 32.4905 133.324 32.4614 133.311 32.436C133.299 32.4107 133.277 32.3913 133.251 32.382C133.224 32.3727 133.195 32.3744 133.17 32.3865C133.145 32.3987 133.125 32.4205 133.116 32.447C132.934 32.8546 132.659 33.2138 132.312 33.4956C131.966 33.7773 131.558 33.9737 131.122 34.069C131.108 34.0721 131.096 34.0778 131.085 34.0858C131.073 34.0938 131.064 34.1039 131.056 34.1156C131.049 34.1273 131.044 34.1404 131.042 34.154C131.039 34.1676 131.04 34.1815 131.043 34.195C131.046 34.2085 131.052 34.2212 131.06 34.2324C131.068 34.2437 131.078 34.2533 131.09 34.2606C131.101 34.2679 131.114 34.2729 131.128 34.2752C131.142 34.2775 131.155 34.2771 131.169 34.274L131.173 34.273Z"
                                fill="#263238" />
                            <path
                                d="M126.095 28.71C126.056 28.7231 126.013 28.7231 125.974 28.71C125.926 28.6921 125.886 28.6562 125.864 28.6098C125.841 28.5634 125.837 28.5101 125.853 28.461C125.947 28.1644 126.111 27.8948 126.331 27.6751C126.551 27.4554 126.821 27.292 127.118 27.199C127.168 27.1876 127.221 27.1961 127.265 27.2225C127.309 27.249 127.342 27.2914 127.355 27.341C127.367 27.3911 127.358 27.4436 127.332 27.4877C127.306 27.5318 127.264 27.5641 127.214 27.578C126.981 27.6558 126.769 27.788 126.597 27.9636C126.425 28.1392 126.297 28.3532 126.223 28.588C126.213 28.6168 126.196 28.6428 126.173 28.6639C126.151 28.6851 126.124 28.7008 126.095 28.71Z"
                                fill="#263238" />
                            <path
                                d="M138.531 174.834C138.248 174.856 137.966 174.782 137.731 174.623C137.658 174.56 137.601 174.48 137.567 174.389C137.532 174.299 137.519 174.202 137.531 174.106C137.531 174.049 137.547 173.993 137.576 173.944C137.606 173.895 137.648 173.855 137.699 173.829C138.158 173.593 139.489 174.413 139.639 174.507C139.656 174.517 139.669 174.533 139.677 174.55C139.685 174.568 139.687 174.588 139.684 174.607C139.681 174.626 139.672 174.644 139.659 174.658C139.646 174.672 139.629 174.682 139.61 174.687C139.257 174.776 138.895 174.825 138.531 174.834ZM137.917 173.979C137.874 173.977 137.83 173.985 137.79 174.003C137.77 174.014 137.753 174.031 137.742 174.051C137.731 174.072 137.726 174.095 137.728 174.118C137.719 174.184 137.727 174.25 137.75 174.312C137.773 174.374 137.811 174.429 137.861 174.473C138.323 174.684 138.848 174.71 139.328 174.547C138.897 174.268 138.417 174.075 137.912 173.979H137.917Z"
                                fill="#526179" />
                            <path
                                d="M139.584 174.689C139.568 174.689 139.552 174.685 139.537 174.677C139.11 174.445 138.283 173.539 138.37 173.077C138.384 173.005 138.423 172.94 138.48 172.893C138.537 172.846 138.608 172.82 138.681 172.82C138.756 172.811 138.831 172.817 138.903 172.839C138.975 172.861 139.041 172.897 139.098 172.946C139.578 173.339 139.677 174.531 139.68 174.581C139.681 174.599 139.678 174.616 139.67 174.632C139.663 174.648 139.651 174.661 139.636 174.671C139.621 174.682 139.603 174.688 139.584 174.689ZM138.75 173.014H138.705C138.578 173.03 138.567 173.088 138.562 173.114C138.51 173.386 139.037 174.07 139.462 174.393C139.434 173.92 139.264 173.467 138.974 173.093C138.911 173.041 138.832 173.013 138.75 173.014Z"
                                fill="#526179" />
                            <path
                                d="M121.296 174.057L121.28 174.064C120.633 174.271 119.475 174.564 118.98 174.218C118.907 174.167 118.847 174.1 118.806 174.021C118.765 173.942 118.744 173.855 118.744 173.766C118.74 173.712 118.75 173.659 118.773 173.61C118.796 173.562 118.831 173.52 118.875 173.489C119.34 173.162 121.087 173.805 121.285 173.88C121.302 173.887 121.317 173.899 121.327 173.914C121.338 173.929 121.344 173.947 121.345 173.966C121.346 173.984 121.342 174.002 121.334 174.019C121.325 174.035 121.312 174.049 121.296 174.058V174.057ZM119.013 173.633L118.99 173.647C118.972 173.659 118.958 173.676 118.949 173.695C118.94 173.715 118.937 173.737 118.94 173.758C118.939 173.817 118.952 173.875 118.978 173.927C119.005 173.979 119.044 174.024 119.092 174.058C119.7 174.248 120.356 174.212 120.94 173.958C120.335 173.689 119.672 173.578 119.012 173.633H119.013Z"
                                fill="#526179" />
                            <path
                                d="M121.296 174.057C121.273 174.069 121.245 174.071 121.22 174.063C120.7 173.897 119.62 173.12 119.64 172.648C119.645 172.537 119.708 172.392 119.972 172.335C120.067 172.314 120.166 172.312 120.262 172.33C120.358 172.349 120.449 172.386 120.53 172.441C120.954 172.846 121.24 173.374 121.346 173.951C121.35 173.968 121.349 173.985 121.343 174.001C121.338 174.018 121.329 174.032 121.316 174.044C121.31 174.049 121.303 174.053 121.296 174.057ZM119.905 172.567C119.838 172.603 119.836 172.645 119.835 172.661C119.821 172.945 120.567 173.567 121.108 173.813C121.008 173.352 120.769 172.933 120.423 172.613C120.364 172.572 120.297 172.544 120.226 172.53C120.156 172.517 120.083 172.518 120.013 172.534C119.976 172.54 119.939 172.551 119.905 172.567Z"
                                fill="#526179" />
                            <path
                                d="M146.882 50.477C146.777 52.091 146.587 53.594 146.39 55.146C146.193 56.698 145.928 58.219 145.637 59.754C145.061 62.8892 144.211 65.9677 143.096 68.954L142.605 70.111L142.481 70.4L142.45 70.472L142.399 70.582L142.255 70.867C142.065 71.2133 141.836 71.5373 141.574 71.833C141.143 72.3181 140.639 72.7331 140.08 73.063C139.628 73.3324 139.153 73.5629 138.661 73.752C137.023 74.3428 135.311 74.7003 133.573 74.814C130.419 75.0497 127.247 74.9313 124.119 74.461L124.232 71.361L126.418 71.114C127.151 71.027 127.88 70.914 128.605 70.814C130.052 70.601 131.484 70.363 132.849 70.046C134.112 69.789 135.339 69.3821 136.505 68.834C136.82 68.7019 137.095 68.4916 137.305 68.223C137.318 68.152 137.16 68.463 137.346 67.966L137.673 67.01C138.474 64.2693 139.087 61.4772 139.507 58.653C139.742 57.215 139.943 55.764 140.137 54.309C140.331 52.854 140.491 51.369 140.644 49.974L146.882 50.477Z"
                                fill="#FFC3BD" />
                            <path
                                d="M147.242 47.481C149.265 50.874 147.542 61.538 147.542 61.538L136.942 59.579C136.942 59.579 136.368 53.479 138.386 48.785C140.577 43.676 144.791 43.37 147.242 47.481Z"
                                fill="#526179" />
                            <path
                                d="M121.264 80.952L115.639 54.788C115.369 53.6707 114.743 52.6715 113.856 51.941C112.968 51.2105 111.867 50.7882 110.719 50.738H89.166C90.3145 50.788 91.4156 51.2102 92.3033 51.9407C93.1909 52.6712 93.817 53.6706 94.087 54.788L99.712 80.952C99.982 82.0693 100.608 83.0685 101.495 83.799C102.383 84.5295 103.484 84.9517 104.632 85.002H126.184C125.036 84.9517 123.935 84.5295 123.047 83.799C122.16 83.0685 121.534 82.0693 121.264 80.952Z"
                                fill="#263238" />
                            <path opacity="0.7"
                                d="M121.264 80.952L115.639 54.788C115.369 53.6707 114.743 52.6715 113.856 51.941C112.968 51.2105 111.867 50.7882 110.719 50.738H89.166C90.3145 50.788 91.4156 51.2102 92.3033 51.9407C93.1909 52.6712 93.817 53.6706 94.087 54.788L99.712 80.952C99.982 82.0693 100.608 83.0685 101.495 83.799C102.383 84.5295 103.484 84.9517 104.632 85.002H126.184C125.036 84.9517 123.935 84.5295 123.047 83.799C122.16 83.0685 121.534 82.0693 121.264 80.952Z"
                                fill="#526179" />
                            <path
                                d="M89.166 50.738H110.718C109.615 50.7587 108.557 51.1816 107.743 51.9274C106.93 52.6732 106.416 53.6905 106.3 54.788L100.709 115.588C100.592 116.685 100.079 117.703 99.2658 118.448C98.4523 119.194 97.3944 119.617 96.291 119.638H82.839C82.3209 119.648 81.8066 119.546 81.3307 119.341C80.8549 119.136 80.4283 118.831 80.0797 118.448C79.7311 118.064 79.4684 117.611 79.3093 117.118C79.1502 116.624 79.0982 116.103 79.157 115.588L84.749 54.788C84.8653 53.6906 85.3783 52.6735 86.1917 51.9278C87.005 51.182 88.0627 50.7589 89.166 50.738Z"
                                fill="#526179" />
                            <path
                                d="M92.609 115.587C92.5503 116.102 92.6022 116.623 92.7613 117.117C92.9204 117.61 93.1831 118.063 93.5317 118.447C93.8804 118.83 94.3069 119.135 94.7828 119.34C95.2586 119.545 95.7729 119.647 96.2911 119.637H74.7391C74.2208 119.647 73.7065 119.546 73.2306 119.34C72.7546 119.135 72.328 118.831 71.9793 118.447C71.6307 118.064 71.368 117.61 71.209 117.117C71.0499 116.624 70.9981 116.102 71.057 115.587H92.609Z"
                                fill="#263238" />
                            <path opacity="0.7"
                                d="M92.609 115.587C92.5503 116.102 92.6022 116.623 92.7613 117.117C92.9204 117.61 93.1831 118.063 93.5317 118.447C93.8804 118.83 94.3069 119.135 94.7828 119.34C95.2586 119.545 95.7729 119.647 96.2911 119.637H74.7391C74.2208 119.647 73.7065 119.546 73.2306 119.34C72.7546 119.135 72.328 118.831 71.9793 118.447C71.6307 118.064 71.368 117.61 71.209 117.117C71.0499 116.624 70.9981 116.102 71.057 115.587H92.609Z"
                                fill="#526179" />
                            <g opacity="0.5">
                                <path opacity="0.5"
                                    d="M102.565 57.55H87.9809C87.8683 57.5515 87.7567 57.5291 87.6534 57.4842C87.5501 57.4393 87.4576 57.3729 87.3819 57.2895C87.3062 57.2061 87.2492 57.1075 87.2145 57.0004C87.1799 56.8932 87.1684 56.7799 87.1809 56.668C87.2069 56.4292 87.3188 56.2081 87.4958 56.0458C87.6728 55.8835 87.9028 55.7911 88.1429 55.786H102.725C102.838 55.7844 102.949 55.8069 103.052 55.8518C103.156 55.8967 103.248 55.9631 103.324 56.0465C103.4 56.1299 103.457 56.2285 103.491 56.3356C103.526 56.4428 103.537 56.5561 103.525 56.668C103.499 56.9064 103.387 57.1273 103.211 57.2896C103.034 57.4518 102.805 57.5444 102.565 57.55Z"
                                    fill="white" />
                                <path opacity="0.5"
                                    d="M102.131 62.321H87.5471C87.4345 62.3225 87.3228 62.3001 87.2196 62.2552C87.1163 62.2103 87.0237 62.1439 86.9481 62.0605C86.8724 61.9771 86.8154 61.8785 86.7807 61.7714C86.7461 61.6642 86.7346 61.5509 86.7471 61.439C86.773 61.2002 86.8849 60.9791 87.062 60.8168C87.239 60.6545 87.469 60.5622 87.7091 60.557H102.293C102.406 60.5555 102.517 60.5779 102.621 60.6228C102.724 60.6677 102.816 60.7341 102.892 60.8175C102.968 60.9009 103.025 60.9995 103.059 61.1066C103.094 61.2138 103.106 61.3271 103.093 61.439C103.067 61.6778 102.956 61.8991 102.778 62.0615C102.601 62.2238 102.371 62.3161 102.131 62.321Z"
                                    fill="white" />
                                <path opacity="0.5"
                                    d="M98.122 67.092H87.113C87.0004 67.0935 86.8888 67.0711 86.7855 67.0262C86.6822 66.9813 86.5897 66.9149 86.514 66.8315C86.4383 66.7481 86.3813 66.6495 86.3466 66.5424C86.312 66.4352 86.3005 66.3219 86.313 66.21C86.3389 65.9712 86.4509 65.7501 86.6279 65.5878C86.8049 65.4255 87.0349 65.3332 87.275 65.328H98.284C98.3966 65.3265 98.5083 65.3489 98.6115 65.3938C98.7148 65.4387 98.8074 65.5051 98.883 65.5885C98.9587 65.6719 99.0157 65.7705 99.0504 65.8776C99.085 65.9848 99.0965 66.0981 99.084 66.21C99.0581 66.4487 98.9462 66.6699 98.7692 66.8322C98.5921 66.9945 98.3621 67.0868 98.122 67.092Z"
                                    fill="white" />
                                <path opacity="0.5"
                                    d="M90.4129 71.862H86.6799C86.5673 71.8635 86.4557 71.8411 86.3524 71.7962C86.2491 71.7513 86.1566 71.6849 86.0809 71.6015C86.0052 71.5181 85.9482 71.4195 85.9135 71.3124C85.8789 71.2052 85.8674 71.0919 85.8799 70.98C85.9056 70.7413 86.0173 70.5202 86.1942 70.3579C86.371 70.1955 86.6009 70.1032 86.8409 70.098H90.5749C90.6875 70.0964 90.7992 70.1189 90.9024 70.1638C91.0057 70.2087 91.0983 70.2751 91.1739 70.3585C91.2496 70.4419 91.3066 70.5405 91.3413 70.6476C91.3759 70.7548 91.3874 70.8681 91.3749 70.98C91.349 71.2187 91.2371 71.4399 91.06 71.6022C90.883 71.7645 90.653 71.8568 90.4129 71.862Z"
                                    fill="white" />
                                <path opacity="0.5"
                                    d="M100.829 76.633H86.2409C86.1283 76.6345 86.0167 76.6121 85.9134 76.5672C85.8101 76.5223 85.7176 76.4559 85.6419 76.3725C85.5663 76.2891 85.5092 76.1905 85.4746 76.0834C85.4399 75.9762 85.4284 75.8629 85.4409 75.751C85.4669 75.5123 85.5788 75.2911 85.7558 75.1288C85.9328 74.9665 86.1628 74.8742 86.4029 74.869H100.987C101.1 74.8675 101.211 74.8899 101.314 74.9348C101.418 74.9797 101.51 75.0461 101.586 75.1295C101.662 75.2129 101.719 75.3115 101.753 75.4186C101.788 75.5258 101.799 75.6391 101.787 75.751C101.761 75.9891 101.65 76.2097 101.474 76.3719C101.297 76.5341 101.068 76.6269 100.829 76.633Z"
                                    fill="white" />
                                <path opacity="0.5"
                                    d="M94.652 81.404H85.812C85.6994 81.4055 85.5877 81.3831 85.4844 81.3382C85.3812 81.2933 85.2886 81.2269 85.213 81.1435C85.1373 81.0601 85.0802 80.9615 85.0456 80.8544C85.0109 80.7472 84.9995 80.6339 85.012 80.522C85.0377 80.2833 85.1494 80.0622 85.3262 79.8999C85.5031 79.7375 85.733 79.6452 85.973 79.64H94.813C94.9256 79.6384 95.0372 79.6609 95.1405 79.7058C95.2438 79.7507 95.3363 79.8171 95.412 79.9005C95.4877 79.9839 95.5447 80.0825 95.5793 80.1896C95.614 80.2968 95.6255 80.4101 95.613 80.522C95.5871 80.7606 95.4753 80.9816 95.2985 81.1439C95.1217 81.3062 94.8919 81.3986 94.652 81.404Z"
                                    fill="white" />
                                <path opacity="0.5"
                                    d="M99.9619 86.174H85.3779C85.2653 86.1755 85.1536 86.1531 85.0504 86.1082C84.9471 86.0633 84.8545 85.9969 84.7789 85.9135C84.7032 85.8301 84.6462 85.7315 84.6115 85.6244C84.5769 85.5172 84.5654 85.4039 84.5779 85.292C84.6038 85.0533 84.7157 84.8321 84.8928 84.6698C85.0698 84.5075 85.2998 84.4152 85.5399 84.41H100.125C100.238 84.4085 100.349 84.4309 100.452 84.4758C100.556 84.5207 100.648 84.5871 100.724 84.6705C100.8 84.7539 100.857 84.8525 100.891 84.9596C100.926 85.0668 100.937 85.1801 100.925 85.292C100.899 85.5309 100.787 85.7522 100.61 85.9145C100.432 86.0769 100.202 86.1691 99.9619 86.174Z"
                                    fill="white" />
                                <path opacity="0.5"
                                    d="M95.9531 90.945H84.9441C84.8315 90.9465 84.7198 90.9241 84.6165 90.8792C84.5132 90.8343 84.4207 90.7679 84.345 90.6845C84.2694 90.6011 84.2123 90.5025 84.1777 90.3954C84.143 90.2882 84.1316 90.1749 84.1441 90.063C84.17 89.8242 84.2819 89.6031 84.4589 89.4408C84.6359 89.2785 84.866 89.1861 85.1061 89.181H96.1151C96.2277 89.1794 96.3393 89.2019 96.4426 89.2468C96.5459 89.2917 96.6384 89.3581 96.7141 89.4415C96.7897 89.5249 96.8468 89.6235 96.8814 89.7306C96.9161 89.8378 96.9275 89.9511 96.9151 90.063C96.8891 90.3017 96.7772 90.5229 96.6002 90.6852C96.4232 90.8475 96.1932 90.9398 95.9531 90.945Z"
                                    fill="white" />
                                <path opacity="0.5"
                                    d="M88.244 95.715H84.508C84.3954 95.7165 84.2838 95.6941 84.1805 95.6492C84.0772 95.6043 83.9847 95.5379 83.909 95.4545C83.8334 95.3711 83.7763 95.2725 83.7417 95.1654C83.707 95.0582 83.6955 94.9449 83.708 94.833C83.734 94.5943 83.8459 94.3731 84.0229 94.2108C84.1999 94.0485 84.4299 93.9562 84.67 93.951H88.408C88.5206 93.9495 88.6323 93.9719 88.7356 94.0168C88.8388 94.0617 88.9314 94.1281 89.007 94.2115C89.0827 94.2949 89.1398 94.3935 89.1744 94.5006C89.2091 94.6078 89.2205 94.7211 89.208 94.833C89.182 95.0721 89.0699 95.2935 88.8924 95.4559C88.715 95.6182 88.4845 95.7103 88.244 95.715Z"
                                    fill="white" />
                                <path opacity="0.5"
                                    d="M98.6599 100.487H84.0759C83.9633 100.489 83.8516 100.466 83.7484 100.421C83.6451 100.376 83.5525 100.31 83.4769 100.227C83.4012 100.143 83.3442 100.045 83.3095 99.9374C83.2749 99.8302 83.2634 99.7169 83.2759 99.605C83.3018 99.3662 83.4137 99.1451 83.5908 98.9828C83.7678 98.8205 83.9978 98.7282 84.2379 98.723H98.8219C98.9345 98.7215 99.0462 98.7439 99.1494 98.7888C99.2527 98.8337 99.3453 98.9001 99.4209 98.9835C99.4966 99.0669 99.5536 99.1655 99.5883 99.2726C99.6229 99.3798 99.6344 99.4931 99.6219 99.605C99.596 99.8438 99.4841 100.065 99.307 100.227C99.13 100.39 98.9 100.482 98.6599 100.487Z"
                                    fill="white" />
                                <path opacity="0.5"
                                    d="M92.483 105.257H83.643C83.5304 105.259 83.4188 105.236 83.3155 105.191C83.2122 105.146 83.1197 105.08 83.044 104.997C82.9684 104.913 82.9113 104.815 82.8767 104.707C82.842 104.6 82.8305 104.487 82.843 104.375C82.8688 104.136 82.9806 103.915 83.1577 103.753C83.3347 103.59 83.5649 103.498 83.805 103.493H92.644C92.7567 103.491 92.8683 103.514 92.9716 103.559C93.0749 103.604 93.1674 103.67 93.2431 103.754C93.3187 103.837 93.3758 103.935 93.4104 104.043C93.4451 104.15 93.4565 104.263 93.444 104.375C93.4181 104.614 93.3064 104.835 93.1296 104.997C92.9528 105.159 92.723 105.252 92.483 105.257Z"
                                    fill="white" />
                                <path opacity="0.5"
                                    d="M97.792 110.028H83.208C83.0954 110.03 82.9837 110.007 82.8804 109.962C82.7772 109.917 82.6846 109.851 82.609 109.768C82.5333 109.684 82.4762 109.586 82.4416 109.478C82.4069 109.371 82.3955 109.258 82.408 109.146C82.4339 108.907 82.5458 108.686 82.7228 108.524C82.8999 108.362 83.1299 108.269 83.37 108.264H97.952C98.0646 108.262 98.1762 108.285 98.2795 108.33C98.3828 108.375 98.4753 108.441 98.551 108.525C98.6267 108.608 98.6837 108.706 98.7184 108.814C98.753 108.921 98.7645 109.034 98.752 109.146C98.7261 109.384 98.6145 109.605 98.4379 109.768C98.2613 109.93 98.0317 110.022 97.792 110.028Z"
                                    fill="white" />
                            </g>
                            <path
                                d="M125.503 71.699L122.337 68.948L120.853 74.012C120.853 74.012 124.069 75.756 125.183 74.155L125.503 71.699Z"
                                fill="#FFC3BD" />
                            <path d="M118.888 68.75L117.858 72.994L120.85 74.014L122.334 68.949L118.888 68.75Z"
                                fill="#FFC3BD" />
                        </svg>
                        <span style="font-weight: 500; color: #5F6C81;">No Version History Found</span>
                    </div>
                </div>
                </mat-expansion-panel> -->
            
                <!-- User Defined Variants -->
                <!-- <mat-expansion-panel #userDefinedVariantsPanel>
                  <mat-expansion-panel-header>
                    <mat-panel-title>User Defined Variants</mat-panel-title>
                  </mat-expansion-panel-header>
                  <div class="report-list">
                    <div *ngFor="let name of views?.allViews; let i = index" class="report-item"
                        [ngClass]="{'highlight': i == currentView}" (click)="changeView(i); toggleVariantSidenav()">
                        <div class="report-info">
                            <span class="variant-list-view" [ngClass]="{'highlighted-text': i == currentView}">
                                {{name?.config_name }}</span>
                            <div *ngIf="i == currentView" class="current-view-name">
                                Current View
                            </div>
                        </div>
    
                        <mat-icon *ngIf="i == currentView" class="delete-icon" (click)="deleteVariant(i)"
                            matTooltip="Click to delete this variant">
                            <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px"
                                fill="#5f6368">
                                <path
                                    d="M280-120q-33 0-56.5-23.5T200-200v-520h-40v-80h200v-40h240v40h200v80h-40v520q0 33-23.5 56.5T680-120H280Zm400-600H280v520h400v-520ZM360-280h80v-360h-80v360Zm160 0h80v-360h-80v360ZM280-720v520-520Z" />
                            </svg>
                        </mat-icon>
                    </div>
                    <div *ngIf="views?.allViews?.length === 0" class="no-data-found">
                        <svg width="248" height="185" viewBox="0 0 248 185" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M247.798 162.281H0V162.405H247.798V162.281Z" fill="#EBEBEB" />
                            <path d="M222.969 170.258H215.584V170.382H222.969V170.258Z" fill="#EBEBEB" />
                            <path d="M164.151 171.603H159.843V171.727H164.151V171.603Z" fill="#EBEBEB" />
                            <path d="M213.491 165.656H196.546V165.78H213.491V165.656Z" fill="#EBEBEB" />
                            <path d="M54.332 168.213H32.926V168.337H54.332V168.213Z" fill="#EBEBEB" />
                            <path d="M66.4651 168.213H58.7451V168.337H66.4651V168.213Z" fill="#EBEBEB" />
                            <path d="M126.255 166.55H79.8301V166.674H126.255V166.55Z" fill="#EBEBEB" />
                            <path
                                d="M117.463 140.178H21.7631C21.013 140.177 20.2939 139.879 19.7636 139.349C19.2332 138.818 18.9349 138.099 18.9341 137.349V2.828C18.9351 2.07812 19.2336 1.35927 19.7639 0.829121C20.2942 0.298968 21.0132 0.000793618 21.7631 0H117.463C118.213 0.00105816 118.931 0.299348 119.462 0.829472C119.992 1.3596 120.29 2.07829 120.291 2.828V137.347C120.291 138.097 119.993 138.817 119.463 139.347C118.932 139.878 118.213 140.177 117.463 140.178ZM21.7641 0.124001C21.0486 0.126111 20.3631 0.411253 19.8572 0.917145C19.3513 1.42304 19.0662 2.10857 19.0641 2.824V137.347C19.0662 138.062 19.3513 138.748 19.8572 139.254C20.3631 139.76 21.0486 140.045 21.7641 140.047H117.464C118.18 140.045 118.865 139.76 119.371 139.254C119.877 138.748 120.162 138.062 120.164 137.347V2.828C120.162 2.11256 119.877 1.42703 119.371 0.921143C118.865 0.415251 118.18 0.130113 117.464 0.128002L21.7641 0.124001Z"
                                fill="#EBEBEB" />
                            <path
                                d="M224.659 140.178H128.959C128.209 140.177 127.49 139.879 126.96 139.348C126.43 138.818 126.132 138.099 126.131 137.349V2.828C126.132 2.07821 126.43 1.35936 126.96 0.829182C127.49 0.299001 128.209 0.000794128 128.959 0H224.659C225.409 0.000793618 226.128 0.298968 226.658 0.829121C227.189 1.35927 227.487 2.07812 227.488 2.828V137.347C227.488 138.097 227.19 138.817 226.659 139.348C226.129 139.879 225.41 140.177 224.659 140.178ZM128.959 0.124001C128.244 0.126111 127.558 0.411253 127.052 0.917145C126.546 1.42304 126.261 2.10857 126.259 2.824V137.347C126.261 138.062 126.546 138.748 127.052 139.254C127.558 139.76 128.244 140.045 128.959 140.047H224.659C225.375 140.046 226.062 139.762 226.569 139.256C227.076 138.75 227.362 138.063 227.364 137.347V2.828C227.362 2.1117 227.076 1.42542 226.569 0.919384C226.062 0.41335 225.375 0.128793 224.659 0.128002L128.959 0.124001Z"
                                fill="#EBEBEB" />
                            <path d="M106.293 162.28H115.522L115.522 98.908H106.293V162.28Z" fill="#E6E6E6" />
                            <path d="M109.542 162.28H106.293V150.24H112.948L109.542 162.28Z" fill="#F0F0F0" />
                            <path d="M203.887 162.28H213.116V98.908H203.887V162.28Z" fill="#E6E6E6" />
                            <path d="M106.293 158.993H206.722V98.908L106.293 98.908L106.293 158.993Z" fill="#F0F0F0" />
                            <path d="M203.477 162.28H206.726V150.24H200.067L203.477 162.28Z" fill="#F0F0F0" />
                            <path d="M173.612 103.883V153.587H203.383V103.883H173.612Z" fill="#E6E6E6" />
                            <path
                                d="M185.553 105.631H191.443C193.936 105.656 196.348 104.75 198.207 103.089H178.79C180.649 104.75 183.061 105.656 185.553 105.631Z"
                                fill="#F0F0F0" />
                            <path d="M141.622 103.883V153.587H171.393V103.883H141.622Z" fill="#E6E6E6" />
                            <path
                                d="M153.262 105.631H159.152C161.644 105.656 164.056 104.75 165.915 103.089H146.498C148.357 104.75 150.769 105.656 153.262 105.631Z"
                                fill="#F0F0F0" />
                            <path d="M109.632 103.883V153.587H139.403V103.883H109.632Z" fill="#E6E6E6" />
                            <path
                                d="M121.272 105.631H127.162C129.655 105.656 132.067 104.75 133.926 103.089H114.508C116.367 104.75 118.779 105.656 121.272 105.631Z"
                                fill="#F0F0F0" />
                            <path d="M34.6819 162.28H97.6519L97.6519 20.728H34.6819L34.6819 162.28Z" fill="#F0F0F0" />
                            <path d="M34.682 162.28H96.323L96.323 20.728H34.682L34.682 162.28Z" fill="#F5F5F5" />
                            <path d="M92.5669 159.068L92.5669 23.94L38.4379 23.94L38.4379 159.068H92.5669Z"
                                fill="#F0F0F0" />
                            <path
                                d="M87.2478 49.071C87.2478 47.1331 86.8661 45.2141 86.1245 43.4237C85.3829 41.6333 84.2959 40.0065 82.9256 38.6362C81.5553 37.2659 79.9285 36.1789 78.1381 35.4373C76.3477 34.6957 74.4287 34.314 72.4908 34.314H58.5148C54.601 34.314 50.8475 35.8687 48.08 38.6362C45.3126 41.4037 43.7578 45.1572 43.7578 49.071H87.2478Z"
                                fill="#FAFAFA" />
                            <path d="M87.2478 150.669V118.387H43.7578V150.669H87.2478Z" fill="#F5F5F5" />
                            <path d="M87.2479 58.173H68.0649V114.222H87.2479V58.173Z" fill="#F5F5F5" />
                            <path d="M62.9408 58.173H43.7578V114.222H62.9408V58.173Z" fill="#F5F5F5" />
                            <path
                                d="M50.7149 85.945H42.8579C42.5349 85.9442 42.2253 85.8155 41.9968 85.5871C41.7684 85.3586 41.6397 85.0491 41.6389 84.726C41.64 84.403 41.7687 84.0936 41.9971 83.8652C42.2255 83.6368 42.5349 83.5081 42.8579 83.507H50.7149C51.0379 83.5081 51.3473 83.6368 51.5757 83.8652C51.8041 84.0936 51.9329 84.403 51.9339 84.726C51.9331 85.0491 51.8044 85.3586 51.576 85.5871C51.3476 85.8155 51.038 85.9442 50.7149 85.945Z"
                                fill="#E0E0E0" />
                            <path d="M39.4128 159.068L39.4128 23.94H38.4388L38.4388 159.068H39.4128Z" fill="#F0F0F0" />
                            <path d="M137.482 83.372L204.261 83.372V20.728L137.482 20.728V83.372Z" fill="#E6E6E6" />
                            <path d="M135.799 83.372L202.578 83.372V20.728L135.799 20.728V83.372Z" fill="#F5F5F5" />
                            <path d="M131.9 86.998L204.261 86.998V83.372L131.9 83.372V86.998Z" fill="#E6E6E6" />
                            <path d="M130.005 86.998L198.639 86.998V83.372L130.005 83.372V86.998Z" fill="#F5F5F5" />
                            <path d="M199.556 80.114V23.987L138.82 23.987V80.114H199.556Z" fill="white" />
                            <path d="M170.871 80.114V23.987H167.504V80.114H170.871Z" fill="#F5F5F5" />
                            <path d="M140.093 80.114V23.987H138.82V80.114H140.093Z" fill="#E6E6E6" />
                            <path d="M172.144 80.114V23.987H170.871V80.114H172.144Z" fill="#E6E6E6" />
                            <path
                                d="M146.459 85.033C145.231 72.308 144.376 51.659 145.04 44.753C145.051 44.6343 145.088 44.5194 145.147 44.416C145.207 44.3126 145.287 44.2229 145.384 44.153C145.512 44.059 145.664 44.003 145.823 43.9914C145.982 43.9797 146.141 44.0129 146.281 44.0871C146.422 44.1614 146.539 44.2736 146.619 44.4112C146.699 44.5487 146.739 44.706 146.734 44.865C146.617 48.287 146.482 56.84 147.211 71.595C147.574 78.94 148.097 83.97 147.738 85.031L146.459 85.033Z"
                                fill="#E6E6E6" />
                            <path
                                d="M141.881 55.704C138.381 51.821 131.242 49.06 123.964 49.074C121.107 49.08 132.657 53.591 137.464 58.574C141.305 62.712 144.467 67.4321 146.833 72.559C145.905 64.954 145.378 59.585 141.881 55.704Z"
                                fill="#E6E6E6" />
                            <path
                                d="M148.813 56.886C151.713 52.842 158.42 49.732 165.673 49.38C168.52 49.242 157.687 54.317 153.644 59.519C150.419 63.8669 147.967 68.7382 146.397 73.919C146.185 66.306 145.909 60.931 148.813 56.886Z"
                                fill="#E6E6E6" />
                            <path
                                d="M141.725 41.522C138.764 38.234 132.716 35.896 126.553 35.908C124.134 35.908 133.914 39.734 137.981 43.949C141.234 47.4528 143.912 51.4496 145.915 55.791C145.133 49.357 144.684 44.81 141.725 41.522Z"
                                fill="#E6E6E6" />
                            <path
                                d="M147.594 46.385C150.053 42.96 155.73 40.327 161.871 40.029C164.282 39.912 155.109 44.209 151.685 48.614C148.953 52.2975 146.877 56.4247 145.548 60.814C145.37 54.362 145.135 49.808 147.594 46.385Z"
                                fill="#E6E6E6" />
                            <path
                                d="M146.953 98.908C145.108 98.908 143.332 98.2022 141.99 96.9353C140.648 95.6684 139.842 93.9363 139.736 92.094L139.098 81.031H154.808L154.17 92.094C154.064 93.9363 153.257 95.6684 151.916 96.9353C150.574 98.2022 148.798 98.908 146.953 98.908Z"
                                fill="#E6E6E6" />
                            <path d="M155.825 83.546H138.084L137.576 78.946H156.337L155.825 83.546Z" fill="#E6E6E6" />
                            <path
                                d="M123.899 184.664C176.969 184.664 219.99 182.151 219.99 179.052C219.99 175.953 176.969 173.44 123.899 173.44C70.8295 173.44 27.8081 175.953 27.8081 179.052C27.8081 182.151 70.8295 184.664 123.899 184.664Z"
                                fill="#F5F5F5" />
                            <path d="M125.838 173.689L121.7 174.155L120.174 164.771L124.312 164.305L125.838 173.689Z"
                                fill="#FFC3BD" />
                            <path d="M144.364 174.593H140.232L139.203 165.024H143.335L144.364 174.593Z" fill="#FFC3BD" />
                            <path
                                d="M139.892 174.112H144.929C145.011 174.112 145.091 174.14 145.155 174.191C145.219 174.243 145.263 174.314 145.281 174.394L146.097 178.067C146.116 178.157 146.115 178.249 146.093 178.338C146.072 178.427 146.03 178.51 145.972 178.58C145.914 178.651 145.84 178.708 145.757 178.746C145.674 178.784 145.584 178.804 145.492 178.802C143.871 178.774 142.692 178.679 140.648 178.679C139.392 178.679 135.604 178.809 133.87 178.809C132.17 178.809 131.909 177.094 132.619 176.939C135.803 176.239 138.202 175.282 139.219 174.366C139.404 174.201 139.644 174.11 139.892 174.112Z"
                                fill="#263238" />
                            <path
                                d="M121.632 173.446L126.116 172.938C126.197 172.928 126.28 172.947 126.349 172.991C126.419 173.034 126.471 173.1 126.498 173.178L127.723 176.735C127.754 176.821 127.765 176.913 127.755 177.004C127.745 177.095 127.714 177.182 127.665 177.259C127.616 177.336 127.55 177.401 127.472 177.449C127.394 177.497 127.306 177.526 127.215 177.535C125.595 177.691 123.247 177.861 121.215 178.092C118.838 178.362 118.442 178.998 115.638 178.933C113.938 178.893 113.512 177.168 114.225 177.033C117.473 176.402 118.101 175.911 120.682 173.885C120.952 173.653 121.281 173.501 121.632 173.446Z"
                                fill="#263238" />
                            <path
                                d="M125.282 53.177C123.894 54.323 122.528 55.319 121.115 56.339C119.702 57.359 118.259 58.306 116.774 59.225C115.289 60.144 113.765 61.025 112.167 61.825C110.533 62.6713 108.833 63.3811 107.082 63.947C106.619 64.095 106.168 64.217 105.648 64.347C105.14 64.4572 104.626 64.5397 104.108 64.594C103.177 64.694 102.301 64.739 101.428 64.779C99.6881 64.859 97.9811 64.857 96.2761 64.845C92.8661 64.804 89.4851 64.64 86.0891 64.34L86.0381 61.24C89.3731 60.811 92.7231 60.44 96.0381 60.067C97.6951 59.887 99.3481 59.685 100.963 59.474C101.763 59.365 102.563 59.237 103.301 59.1C103.618 59.044 103.932 58.9702 104.241 58.879C104.532 58.779 104.889 58.665 105.219 58.532C106.636 57.9566 108.012 57.2837 109.336 56.518C110.707 55.731 112.079 54.886 113.427 53.988C114.775 53.09 116.117 52.16 117.447 51.211C118.777 50.262 120.106 49.257 121.354 48.311L125.282 53.177Z"
                                fill="#FFC3BD" />
                            <path
                                d="M129.321 49.645C128.727 53.551 120.503 59.526 120.503 59.526L114.603 52.203C117.188 49.4447 119.928 46.8368 122.811 44.392C125.466 42.222 129.967 45.395 129.321 49.645Z"
                                fill="#526179" />
                            <path
                                d="M87.176 61.567L84.83 59.897L84.573 65.173C84.9276 65.1739 85.2771 65.088 85.5909 64.9228C85.9046 64.7575 86.1732 64.518 86.373 64.225L87.176 61.567Z"
                                fill="#FFC3BD" />
                            <path d="M82.711 58.361L82.231 63.532L84.575 65.173L84.832 59.897L82.711 58.361Z"
                                fill="#FFC3BD" />
                            <path opacity="0.2"
                                d="M120.176 164.774L120.962 169.611L125.101 169.145L124.315 164.308L120.176 164.774Z"
                                fill="black" />
                            <path opacity="0.2" d="M143.337 165.025H139.203L139.735 169.957H143.869L143.337 165.025Z"
                                fill="black" />
                            <path
                                d="M143.396 44.517C143.704 44.586 143.995 44.7159 144.251 44.899C144.508 45.0821 144.726 45.3147 144.891 45.5831C145.057 45.8516 145.167 46.1505 145.216 46.4621C145.264 46.7738 145.249 47.092 145.173 47.398C144.276 51.1195 143.601 54.8907 143.149 58.692C142.758 61.892 142.524 64.949 142.378 67.72C142.035 74.215 142.178 79.166 141.984 80.844C138.753 80.62 126.577 79.773 119.938 79.309C117.697 61.765 120.323 50.569 121.798 46.02C122.024 45.3112 122.446 44.6811 123.016 44.2031C123.587 43.7251 124.281 43.4188 125.018 43.32C125.864 43.209 126.876 43.102 127.944 43.039C128.318 43.015 128.697 43.002 129.083 42.994C132.217 43.0311 135.347 43.2488 138.456 43.646C139.046 43.716 139.642 43.803 140.222 43.896C141.381 44.09 142.483 44.313 143.396 44.517Z"
                                fill="#6E7B8F" />
                            <path
                                d="M138.265 33.033C137.465 36.007 136.487 41.491 138.457 43.651C133.347 47.088 130.085 51.927 129.144 50.876C128.651 50.326 128.437 43.961 129.085 42.997C132.504 42.429 132.591 39.928 132.169 37.544L138.265 33.033Z"
                                fill="#FFC3BD" />
                            <path
                                d="M138.061 42.418C138.061 42.418 129.734 46.103 128.63 54.343C130.924 51.687 135.991 48.797 135.991 48.797L133.756 48.155C135.177 47.6156 136.649 47.2196 138.149 46.973C137.901 45.835 139.223 43.565 139.223 43.565L138.061 42.418Z"
                                fill="#526179" />
                            <path
                                d="M130.548 42.285C130.548 42.285 131.658 46.991 128.631 54.343C128.034 51.7769 127.216 49.2675 126.185 46.843L128.224 47.398C128.224 47.398 128.124 46.449 126.473 45.187C127.161 44.1877 128.019 43.3168 129.008 42.614L130.548 42.285Z"
                                fill="#526179" />
                            <path opacity="0.2"
                                d="M135.78 34.874L132.172 37.542C132.279 38.0972 132.337 38.6607 132.345 39.226C133.645 39.126 135.505 37.843 135.736 36.508C135.85 35.9705 135.864 35.4169 135.78 34.874Z"
                                fill="black" />
                            <path
                                d="M127.684 24.421C125.669 24.508 124.557 27.982 126.602 30.843C128.647 33.704 130.399 24.304 127.684 24.421Z"
                                fill="#263238" />
                            <path
                                d="M137.621 27.478C137.86 31.572 138.208 33.955 136.406 36.288C133.696 39.797 128.456 38.539 126.999 34.599C125.688 31.053 125.517 24.954 129.299 22.859C130.131 22.3926 131.068 22.1467 132.022 22.1447C132.976 22.1426 133.914 22.3845 134.748 22.8474C135.582 23.3103 136.284 23.9787 136.787 24.7892C137.289 25.5997 137.577 26.5253 137.621 27.478Z"
                                fill="#FFC3BD" />
                            <path
                                d="M138.222 30.624C137.127 29.9823 136.171 29.1291 135.41 28.1143C134.648 27.0995 134.096 25.9433 133.785 24.713C132.585 24.997 126.604 27.749 124.765 24.713C122.926 21.677 125.24 19.896 128.681 21.765C127.159 19.079 128.543 17.765 133.325 17.649C138.107 17.533 137.464 20.305 137.464 20.305C137.464 20.305 141.084 18.729 142.364 21.505C143.847 24.695 141.111 30.142 138.222 30.624Z"
                                fill="#263238" />
                            <path
                                d="M136.646 19.572C136.69 19.572 138.457 19.997 140.493 17.948C140.128 19.696 136.966 20.86 136.966 20.86L136.646 19.572Z"
                                fill="#263238" />
                            <path
                                d="M139.022 25.561C137.115 24.902 134.806 27.727 135.659 31.138C136.512 34.549 141.591 26.449 139.022 25.561Z"
                                fill="#263238" />
                            <path
                                d="M140.101 30.883C139.926 31.8997 139.356 32.8058 138.515 33.403C137.394 34.185 136.384 33.291 136.315 31.997C136.257 30.833 136.769 29.023 138.076 28.756C138.363 28.7024 138.658 28.7213 138.936 28.8111C139.214 28.9008 139.465 29.0585 139.666 29.2698C139.867 29.4812 140.012 29.7394 140.088 30.0212C140.164 30.3029 140.169 30.5992 140.101 30.883Z"
                                fill="#FFC3BD" />
                            <path
                                d="M126.152 125.863C127.145 113.777 134.429 80.32 134.429 80.32L119.935 79.309C119.935 79.309 114.346 112.52 113.918 125.124C113.472 138.229 118.743 167.355 118.743 167.355L125.694 166.564C125.694 166.564 125.155 137.999 126.152 125.863Z"
                                fill="#526179" />
                            <path opacity="0.5"
                                d="M126.152 125.863C127.145 113.777 134.429 80.32 134.429 80.32L119.935 79.309C119.935 79.309 114.346 112.52 113.918 125.124C113.472 138.229 118.743 167.355 118.743 167.355L125.694 166.564C125.694 166.564 125.155 137.999 126.152 125.863Z"
                                fill="white" />
                            <path opacity="0.2"
                                d="M128.969 91.65C126.047 95.386 125.995 108.507 127.125 118.201C128.202 111.208 129.899 102.327 131.386 94.924L128.969 91.65Z"
                                fill="black" />
                            <path
                                d="M127.756 79.854C127.756 79.854 129.32 115.229 131.065 127.209C132.975 140.328 137.826 169.001 137.826 169.001H145.394C145.394 169.001 144.149 141.995 143.15 129.106C142.013 114.425 141.982 80.846 141.982 80.846L127.756 79.854Z"
                                fill="#526179" />
                            <path opacity="0.5"
                                d="M127.756 79.854C127.756 79.854 129.32 115.229 131.065 127.209C132.975 140.328 137.826 169.001 137.826 169.001H145.394C145.394 169.001 144.149 141.995 143.15 129.106C142.013 114.425 141.982 80.846 141.982 80.846L127.756 79.854Z"
                                fill="white" />
                            <path d="M136.916 169.162H145.827V166.5L135.866 166.327L136.916 169.162Z" fill="#526179" />
                            <path d="M118.44 168.138L126.724 167.199L126.424 164.522L117.147 165.431L118.44 168.138Z"
                                fill="#526179" />
                            <path
                                d="M131.593 29.355C131.633 29.686 131.493 29.975 131.273 30.001C131.053 30.027 130.849 29.78 130.81 29.449C130.771 29.118 130.91 28.829 131.13 28.803C131.35 28.777 131.554 29.024 131.593 29.355Z"
                                fill="#263238" />
                            <path
                                d="M127.854 29.804C127.894 30.135 127.754 30.424 127.534 30.45C127.314 30.476 127.11 30.229 127.07 29.898C127.03 29.567 127.17 29.278 127.39 29.252C127.61 29.226 127.814 29.477 127.854 29.804Z"
                                fill="#263238" />
                            <path
                                d="M129.062 29.826C128.789 30.8847 128.372 31.9009 127.822 32.846C128.067 32.9804 128.339 33.0596 128.618 33.0781C128.897 33.0966 129.177 33.0539 129.438 32.953L129.062 29.826Z"
                                fill="#ED847E" />
                            <path
                                d="M132.61 28.006C132.575 28.0165 132.538 28.0173 132.503 28.0083C132.468 27.9993 132.436 27.9809 132.41 27.955C132.245 27.7731 132.038 27.633 131.809 27.5461C131.579 27.4592 131.331 27.428 131.087 27.455C131.036 27.4636 130.984 27.4522 130.941 27.4231C130.898 27.394 130.869 27.3495 130.858 27.299C130.853 27.2737 130.853 27.2476 130.858 27.2222C130.863 27.1969 130.873 27.1728 130.887 27.1515C130.902 27.1301 130.921 27.1119 130.942 27.0979C130.964 27.0839 130.988 27.0744 131.014 27.07C131.322 27.03 131.636 27.0648 131.928 27.1713C132.22 27.2778 132.483 27.4529 132.693 27.682C132.729 27.7192 132.749 27.7689 132.749 27.8205C132.749 27.8721 132.729 27.9218 132.693 27.959C132.67 27.9814 132.641 27.9975 132.61 28.006Z"
                                fill="#263238" />
                            <path
                                d="M131.173 34.273C131.645 34.1737 132.086 33.9623 132.459 33.6567C132.832 33.3511 133.126 32.9602 133.316 32.517C133.325 32.4905 133.324 32.4614 133.311 32.436C133.299 32.4107 133.277 32.3913 133.251 32.382C133.224 32.3727 133.195 32.3744 133.17 32.3865C133.145 32.3987 133.125 32.4205 133.116 32.447C132.934 32.8546 132.659 33.2138 132.312 33.4956C131.966 33.7773 131.558 33.9737 131.122 34.069C131.108 34.0721 131.096 34.0778 131.085 34.0858C131.073 34.0938 131.064 34.1039 131.056 34.1156C131.049 34.1273 131.044 34.1404 131.042 34.154C131.039 34.1676 131.04 34.1815 131.043 34.195C131.046 34.2085 131.052 34.2212 131.06 34.2324C131.068 34.2437 131.078 34.2533 131.09 34.2606C131.101 34.2679 131.114 34.2729 131.128 34.2752C131.142 34.2775 131.155 34.2771 131.169 34.274L131.173 34.273Z"
                                fill="#263238" />
                            <path
                                d="M126.095 28.71C126.056 28.7231 126.013 28.7231 125.974 28.71C125.926 28.6921 125.886 28.6562 125.864 28.6098C125.841 28.5634 125.837 28.5101 125.853 28.461C125.947 28.1644 126.111 27.8948 126.331 27.6751C126.551 27.4554 126.821 27.292 127.118 27.199C127.168 27.1876 127.221 27.1961 127.265 27.2225C127.309 27.249 127.342 27.2914 127.355 27.341C127.367 27.3911 127.358 27.4436 127.332 27.4877C127.306 27.5318 127.264 27.5641 127.214 27.578C126.981 27.6558 126.769 27.788 126.597 27.9636C126.425 28.1392 126.297 28.3532 126.223 28.588C126.213 28.6168 126.196 28.6428 126.173 28.6639C126.151 28.6851 126.124 28.7008 126.095 28.71Z"
                                fill="#263238" />
                            <path
                                d="M138.531 174.834C138.248 174.856 137.966 174.782 137.731 174.623C137.658 174.56 137.601 174.48 137.567 174.389C137.532 174.299 137.519 174.202 137.531 174.106C137.531 174.049 137.547 173.993 137.576 173.944C137.606 173.895 137.648 173.855 137.699 173.829C138.158 173.593 139.489 174.413 139.639 174.507C139.656 174.517 139.669 174.533 139.677 174.55C139.685 174.568 139.687 174.588 139.684 174.607C139.681 174.626 139.672 174.644 139.659 174.658C139.646 174.672 139.629 174.682 139.61 174.687C139.257 174.776 138.895 174.825 138.531 174.834ZM137.917 173.979C137.874 173.977 137.83 173.985 137.79 174.003C137.77 174.014 137.753 174.031 137.742 174.051C137.731 174.072 137.726 174.095 137.728 174.118C137.719 174.184 137.727 174.25 137.75 174.312C137.773 174.374 137.811 174.429 137.861 174.473C138.323 174.684 138.848 174.71 139.328 174.547C138.897 174.268 138.417 174.075 137.912 173.979H137.917Z"
                                fill="#526179" />
                            <path
                                d="M139.584 174.689C139.568 174.689 139.552 174.685 139.537 174.677C139.11 174.445 138.283 173.539 138.37 173.077C138.384 173.005 138.423 172.94 138.48 172.893C138.537 172.846 138.608 172.82 138.681 172.82C138.756 172.811 138.831 172.817 138.903 172.839C138.975 172.861 139.041 172.897 139.098 172.946C139.578 173.339 139.677 174.531 139.68 174.581C139.681 174.599 139.678 174.616 139.67 174.632C139.663 174.648 139.651 174.661 139.636 174.671C139.621 174.682 139.603 174.688 139.584 174.689ZM138.75 173.014H138.705C138.578 173.03 138.567 173.088 138.562 173.114C138.51 173.386 139.037 174.07 139.462 174.393C139.434 173.92 139.264 173.467 138.974 173.093C138.911 173.041 138.832 173.013 138.75 173.014Z"
                                fill="#526179" />
                            <path
                                d="M121.296 174.057L121.28 174.064C120.633 174.271 119.475 174.564 118.98 174.218C118.907 174.167 118.847 174.1 118.806 174.021C118.765 173.942 118.744 173.855 118.744 173.766C118.74 173.712 118.75 173.659 118.773 173.61C118.796 173.562 118.831 173.52 118.875 173.489C119.34 173.162 121.087 173.805 121.285 173.88C121.302 173.887 121.317 173.899 121.327 173.914C121.338 173.929 121.344 173.947 121.345 173.966C121.346 173.984 121.342 174.002 121.334 174.019C121.325 174.035 121.312 174.049 121.296 174.058V174.057ZM119.013 173.633L118.99 173.647C118.972 173.659 118.958 173.676 118.949 173.695C118.94 173.715 118.937 173.737 118.94 173.758C118.939 173.817 118.952 173.875 118.978 173.927C119.005 173.979 119.044 174.024 119.092 174.058C119.7 174.248 120.356 174.212 120.94 173.958C120.335 173.689 119.672 173.578 119.012 173.633H119.013Z"
                                fill="#526179" />
                            <path
                                d="M121.296 174.057C121.273 174.069 121.245 174.071 121.22 174.063C120.7 173.897 119.62 173.12 119.64 172.648C119.645 172.537 119.708 172.392 119.972 172.335C120.067 172.314 120.166 172.312 120.262 172.33C120.358 172.349 120.449 172.386 120.53 172.441C120.954 172.846 121.24 173.374 121.346 173.951C121.35 173.968 121.349 173.985 121.343 174.001C121.338 174.018 121.329 174.032 121.316 174.044C121.31 174.049 121.303 174.053 121.296 174.057ZM119.905 172.567C119.838 172.603 119.836 172.645 119.835 172.661C119.821 172.945 120.567 173.567 121.108 173.813C121.008 173.352 120.769 172.933 120.423 172.613C120.364 172.572 120.297 172.544 120.226 172.53C120.156 172.517 120.083 172.518 120.013 172.534C119.976 172.54 119.939 172.551 119.905 172.567Z"
                                fill="#526179" />
                            <path
                                d="M146.882 50.477C146.777 52.091 146.587 53.594 146.39 55.146C146.193 56.698 145.928 58.219 145.637 59.754C145.061 62.8892 144.211 65.9677 143.096 68.954L142.605 70.111L142.481 70.4L142.45 70.472L142.399 70.582L142.255 70.867C142.065 71.2133 141.836 71.5373 141.574 71.833C141.143 72.3181 140.639 72.7331 140.08 73.063C139.628 73.3324 139.153 73.5629 138.661 73.752C137.023 74.3428 135.311 74.7003 133.573 74.814C130.419 75.0497 127.247 74.9313 124.119 74.461L124.232 71.361L126.418 71.114C127.151 71.027 127.88 70.914 128.605 70.814C130.052 70.601 131.484 70.363 132.849 70.046C134.112 69.789 135.339 69.3821 136.505 68.834C136.82 68.7019 137.095 68.4916 137.305 68.223C137.318 68.152 137.16 68.463 137.346 67.966L137.673 67.01C138.474 64.2693 139.087 61.4772 139.507 58.653C139.742 57.215 139.943 55.764 140.137 54.309C140.331 52.854 140.491 51.369 140.644 49.974L146.882 50.477Z"
                                fill="#FFC3BD" />
                            <path
                                d="M147.242 47.481C149.265 50.874 147.542 61.538 147.542 61.538L136.942 59.579C136.942 59.579 136.368 53.479 138.386 48.785C140.577 43.676 144.791 43.37 147.242 47.481Z"
                                fill="#526179" />
                            <path
                                d="M121.264 80.952L115.639 54.788C115.369 53.6707 114.743 52.6715 113.856 51.941C112.968 51.2105 111.867 50.7882 110.719 50.738H89.166C90.3145 50.788 91.4156 51.2102 92.3033 51.9407C93.1909 52.6712 93.817 53.6706 94.087 54.788L99.712 80.952C99.982 82.0693 100.608 83.0685 101.495 83.799C102.383 84.5295 103.484 84.9517 104.632 85.002H126.184C125.036 84.9517 123.935 84.5295 123.047 83.799C122.16 83.0685 121.534 82.0693 121.264 80.952Z"
                                fill="#263238" />
                            <path opacity="0.7"
                                d="M121.264 80.952L115.639 54.788C115.369 53.6707 114.743 52.6715 113.856 51.941C112.968 51.2105 111.867 50.7882 110.719 50.738H89.166C90.3145 50.788 91.4156 51.2102 92.3033 51.9407C93.1909 52.6712 93.817 53.6706 94.087 54.788L99.712 80.952C99.982 82.0693 100.608 83.0685 101.495 83.799C102.383 84.5295 103.484 84.9517 104.632 85.002H126.184C125.036 84.9517 123.935 84.5295 123.047 83.799C122.16 83.0685 121.534 82.0693 121.264 80.952Z"
                                fill="#526179" />
                            <path
                                d="M89.166 50.738H110.718C109.615 50.7587 108.557 51.1816 107.743 51.9274C106.93 52.6732 106.416 53.6905 106.3 54.788L100.709 115.588C100.592 116.685 100.079 117.703 99.2658 118.448C98.4523 119.194 97.3944 119.617 96.291 119.638H82.839C82.3209 119.648 81.8066 119.546 81.3307 119.341C80.8549 119.136 80.4283 118.831 80.0797 118.448C79.7311 118.064 79.4684 117.611 79.3093 117.118C79.1502 116.624 79.0982 116.103 79.157 115.588L84.749 54.788C84.8653 53.6906 85.3783 52.6735 86.1917 51.9278C87.005 51.182 88.0627 50.7589 89.166 50.738Z"
                                fill="#526179" />
                            <path
                                d="M92.609 115.587C92.5503 116.102 92.6022 116.623 92.7613 117.117C92.9204 117.61 93.1831 118.063 93.5317 118.447C93.8804 118.83 94.3069 119.135 94.7828 119.34C95.2586 119.545 95.7729 119.647 96.2911 119.637H74.7391C74.2208 119.647 73.7065 119.546 73.2306 119.34C72.7546 119.135 72.328 118.831 71.9793 118.447C71.6307 118.064 71.368 117.61 71.209 117.117C71.0499 116.624 70.9981 116.102 71.057 115.587H92.609Z"
                                fill="#263238" />
                            <path opacity="0.7"
                                d="M92.609 115.587C92.5503 116.102 92.6022 116.623 92.7613 117.117C92.9204 117.61 93.1831 118.063 93.5317 118.447C93.8804 118.83 94.3069 119.135 94.7828 119.34C95.2586 119.545 95.7729 119.647 96.2911 119.637H74.7391C74.2208 119.647 73.7065 119.546 73.2306 119.34C72.7546 119.135 72.328 118.831 71.9793 118.447C71.6307 118.064 71.368 117.61 71.209 117.117C71.0499 116.624 70.9981 116.102 71.057 115.587H92.609Z"
                                fill="#526179" />
                            <g opacity="0.5">
                                <path opacity="0.5"
                                    d="M102.565 57.55H87.9809C87.8683 57.5515 87.7567 57.5291 87.6534 57.4842C87.5501 57.4393 87.4576 57.3729 87.3819 57.2895C87.3062 57.2061 87.2492 57.1075 87.2145 57.0004C87.1799 56.8932 87.1684 56.7799 87.1809 56.668C87.2069 56.4292 87.3188 56.2081 87.4958 56.0458C87.6728 55.8835 87.9028 55.7911 88.1429 55.786H102.725C102.838 55.7844 102.949 55.8069 103.052 55.8518C103.156 55.8967 103.248 55.9631 103.324 56.0465C103.4 56.1299 103.457 56.2285 103.491 56.3356C103.526 56.4428 103.537 56.5561 103.525 56.668C103.499 56.9064 103.387 57.1273 103.211 57.2896C103.034 57.4518 102.805 57.5444 102.565 57.55Z"
                                    fill="white" />
                                <path opacity="0.5"
                                    d="M102.131 62.321H87.5471C87.4345 62.3225 87.3228 62.3001 87.2196 62.2552C87.1163 62.2103 87.0237 62.1439 86.9481 62.0605C86.8724 61.9771 86.8154 61.8785 86.7807 61.7714C86.7461 61.6642 86.7346 61.5509 86.7471 61.439C86.773 61.2002 86.8849 60.9791 87.062 60.8168C87.239 60.6545 87.469 60.5622 87.7091 60.557H102.293C102.406 60.5555 102.517 60.5779 102.621 60.6228C102.724 60.6677 102.816 60.7341 102.892 60.8175C102.968 60.9009 103.025 60.9995 103.059 61.1066C103.094 61.2138 103.106 61.3271 103.093 61.439C103.067 61.6778 102.956 61.8991 102.778 62.0615C102.601 62.2238 102.371 62.3161 102.131 62.321Z"
                                    fill="white" />
                                <path opacity="0.5"
                                    d="M98.122 67.092H87.113C87.0004 67.0935 86.8888 67.0711 86.7855 67.0262C86.6822 66.9813 86.5897 66.9149 86.514 66.8315C86.4383 66.7481 86.3813 66.6495 86.3466 66.5424C86.312 66.4352 86.3005 66.3219 86.313 66.21C86.3389 65.9712 86.4509 65.7501 86.6279 65.5878C86.8049 65.4255 87.0349 65.3332 87.275 65.328H98.284C98.3966 65.3265 98.5083 65.3489 98.6115 65.3938C98.7148 65.4387 98.8074 65.5051 98.883 65.5885C98.9587 65.6719 99.0157 65.7705 99.0504 65.8776C99.085 65.9848 99.0965 66.0981 99.084 66.21C99.0581 66.4487 98.9462 66.6699 98.7692 66.8322C98.5921 66.9945 98.3621 67.0868 98.122 67.092Z"
                                    fill="white" />
                                <path opacity="0.5"
                                    d="M90.4129 71.862H86.6799C86.5673 71.8635 86.4557 71.8411 86.3524 71.7962C86.2491 71.7513 86.1566 71.6849 86.0809 71.6015C86.0052 71.5181 85.9482 71.4195 85.9135 71.3124C85.8789 71.2052 85.8674 71.0919 85.8799 70.98C85.9056 70.7413 86.0173 70.5202 86.1942 70.3579C86.371 70.1955 86.6009 70.1032 86.8409 70.098H90.5749C90.6875 70.0964 90.7992 70.1189 90.9024 70.1638C91.0057 70.2087 91.0983 70.2751 91.1739 70.3585C91.2496 70.4419 91.3066 70.5405 91.3413 70.6476C91.3759 70.7548 91.3874 70.8681 91.3749 70.98C91.349 71.2187 91.2371 71.4399 91.06 71.6022C90.883 71.7645 90.653 71.8568 90.4129 71.862Z"
                                    fill="white" />
                                <path opacity="0.5"
                                    d="M100.829 76.633H86.2409C86.1283 76.6345 86.0167 76.6121 85.9134 76.5672C85.8101 76.5223 85.7176 76.4559 85.6419 76.3725C85.5663 76.2891 85.5092 76.1905 85.4746 76.0834C85.4399 75.9762 85.4284 75.8629 85.4409 75.751C85.4669 75.5123 85.5788 75.2911 85.7558 75.1288C85.9328 74.9665 86.1628 74.8742 86.4029 74.869H100.987C101.1 74.8675 101.211 74.8899 101.314 74.9348C101.418 74.9797 101.51 75.0461 101.586 75.1295C101.662 75.2129 101.719 75.3115 101.753 75.4186C101.788 75.5258 101.799 75.6391 101.787 75.751C101.761 75.9891 101.65 76.2097 101.474 76.3719C101.297 76.5341 101.068 76.6269 100.829 76.633Z"
                                    fill="white" />
                                <path opacity="0.5"
                                    d="M94.652 81.404H85.812C85.6994 81.4055 85.5877 81.3831 85.4844 81.3382C85.3812 81.2933 85.2886 81.2269 85.213 81.1435C85.1373 81.0601 85.0802 80.9615 85.0456 80.8544C85.0109 80.7472 84.9995 80.6339 85.012 80.522C85.0377 80.2833 85.1494 80.0622 85.3262 79.8999C85.5031 79.7375 85.733 79.6452 85.973 79.64H94.813C94.9256 79.6384 95.0372 79.6609 95.1405 79.7058C95.2438 79.7507 95.3363 79.8171 95.412 79.9005C95.4877 79.9839 95.5447 80.0825 95.5793 80.1896C95.614 80.2968 95.6255 80.4101 95.613 80.522C95.5871 80.7606 95.4753 80.9816 95.2985 81.1439C95.1217 81.3062 94.8919 81.3986 94.652 81.404Z"
                                    fill="white" />
                                <path opacity="0.5"
                                    d="M99.9619 86.174H85.3779C85.2653 86.1755 85.1536 86.1531 85.0504 86.1082C84.9471 86.0633 84.8545 85.9969 84.7789 85.9135C84.7032 85.8301 84.6462 85.7315 84.6115 85.6244C84.5769 85.5172 84.5654 85.4039 84.5779 85.292C84.6038 85.0533 84.7157 84.8321 84.8928 84.6698C85.0698 84.5075 85.2998 84.4152 85.5399 84.41H100.125C100.238 84.4085 100.349 84.4309 100.452 84.4758C100.556 84.5207 100.648 84.5871 100.724 84.6705C100.8 84.7539 100.857 84.8525 100.891 84.9596C100.926 85.0668 100.937 85.1801 100.925 85.292C100.899 85.5309 100.787 85.7522 100.61 85.9145C100.432 86.0769 100.202 86.1691 99.9619 86.174Z"
                                    fill="white" />
                                <path opacity="0.5"
                                    d="M95.9531 90.945H84.9441C84.8315 90.9465 84.7198 90.9241 84.6165 90.8792C84.5132 90.8343 84.4207 90.7679 84.345 90.6845C84.2694 90.6011 84.2123 90.5025 84.1777 90.3954C84.143 90.2882 84.1316 90.1749 84.1441 90.063C84.17 89.8242 84.2819 89.6031 84.4589 89.4408C84.6359 89.2785 84.866 89.1861 85.1061 89.181H96.1151C96.2277 89.1794 96.3393 89.2019 96.4426 89.2468C96.5459 89.2917 96.6384 89.3581 96.7141 89.4415C96.7897 89.5249 96.8468 89.6235 96.8814 89.7306C96.9161 89.8378 96.9275 89.9511 96.9151 90.063C96.8891 90.3017 96.7772 90.5229 96.6002 90.6852C96.4232 90.8475 96.1932 90.9398 95.9531 90.945Z"
                                    fill="white" />
                                <path opacity="0.5"
                                    d="M88.244 95.715H84.508C84.3954 95.7165 84.2838 95.6941 84.1805 95.6492C84.0772 95.6043 83.9847 95.5379 83.909 95.4545C83.8334 95.3711 83.7763 95.2725 83.7417 95.1654C83.707 95.0582 83.6955 94.9449 83.708 94.833C83.734 94.5943 83.8459 94.3731 84.0229 94.2108C84.1999 94.0485 84.4299 93.9562 84.67 93.951H88.408C88.5206 93.9495 88.6323 93.9719 88.7356 94.0168C88.8388 94.0617 88.9314 94.1281 89.007 94.2115C89.0827 94.2949 89.1398 94.3935 89.1744 94.5006C89.2091 94.6078 89.2205 94.7211 89.208 94.833C89.182 95.0721 89.0699 95.2935 88.8924 95.4559C88.715 95.6182 88.4845 95.7103 88.244 95.715Z"
                                    fill="white" />
                                <path opacity="0.5"
                                    d="M98.6599 100.487H84.0759C83.9633 100.489 83.8516 100.466 83.7484 100.421C83.6451 100.376 83.5525 100.31 83.4769 100.227C83.4012 100.143 83.3442 100.045 83.3095 99.9374C83.2749 99.8302 83.2634 99.7169 83.2759 99.605C83.3018 99.3662 83.4137 99.1451 83.5908 98.9828C83.7678 98.8205 83.9978 98.7282 84.2379 98.723H98.8219C98.9345 98.7215 99.0462 98.7439 99.1494 98.7888C99.2527 98.8337 99.3453 98.9001 99.4209 98.9835C99.4966 99.0669 99.5536 99.1655 99.5883 99.2726C99.6229 99.3798 99.6344 99.4931 99.6219 99.605C99.596 99.8438 99.4841 100.065 99.307 100.227C99.13 100.39 98.9 100.482 98.6599 100.487Z"
                                    fill="white" />
                                <path opacity="0.5"
                                    d="M92.483 105.257H83.643C83.5304 105.259 83.4188 105.236 83.3155 105.191C83.2122 105.146 83.1197 105.08 83.044 104.997C82.9684 104.913 82.9113 104.815 82.8767 104.707C82.842 104.6 82.8305 104.487 82.843 104.375C82.8688 104.136 82.9806 103.915 83.1577 103.753C83.3347 103.59 83.5649 103.498 83.805 103.493H92.644C92.7567 103.491 92.8683 103.514 92.9716 103.559C93.0749 103.604 93.1674 103.67 93.2431 103.754C93.3187 103.837 93.3758 103.935 93.4104 104.043C93.4451 104.15 93.4565 104.263 93.444 104.375C93.4181 104.614 93.3064 104.835 93.1296 104.997C92.9528 105.159 92.723 105.252 92.483 105.257Z"
                                    fill="white" />
                                <path opacity="0.5"
                                    d="M97.792 110.028H83.208C83.0954 110.03 82.9837 110.007 82.8804 109.962C82.7772 109.917 82.6846 109.851 82.609 109.768C82.5333 109.684 82.4762 109.586 82.4416 109.478C82.4069 109.371 82.3955 109.258 82.408 109.146C82.4339 108.907 82.5458 108.686 82.7228 108.524C82.8999 108.362 83.1299 108.269 83.37 108.264H97.952C98.0646 108.262 98.1762 108.285 98.2795 108.33C98.3828 108.375 98.4753 108.441 98.551 108.525C98.6267 108.608 98.6837 108.706 98.7184 108.814C98.753 108.921 98.7645 109.034 98.752 109.146C98.7261 109.384 98.6145 109.605 98.4379 109.768C98.2613 109.93 98.0317 110.022 97.792 110.028Z"
                                    fill="white" />
                            </g>
                            <path
                                d="M125.503 71.699L122.337 68.948L120.853 74.012C120.853 74.012 124.069 75.756 125.183 74.155L125.503 71.699Z"
                                fill="#FFC3BD" />
                            <path d="M118.888 68.75L117.858 72.994L120.85 74.014L122.334 68.949L118.888 68.75Z"
                                fill="#FFC3BD" />
                        </svg>
                        <span style="font-weight: 500; color: #5F6C81;">No Version History Found</span>
                    </div>
                </div>
                </mat-expansion-panel>
              </mat-accordion> -->

            <!-- Variant List content -->
            <div style="border-bottom: 1px solid #dadce2; width: 100%;"></div>
            <div class="report-list">
                <div *ngFor="let name of views?.allViews; let i = index" class="report-item"
                    [ngClass]="{'highlight': i == currentView}" (click)="changeView(i); toggleVariantSidenav()">
                    <div class="report-info">
                        <span class="variant-list-view" [ngClass]="{'highlighted-text': i == currentView}">
                            {{name?.config_name }}</span>
                        <div *ngIf="i == currentView" class="current-view-name">
                            Current View
                        </div>
                    </div>

                    <mat-icon *ngIf="i == currentView" class="delete-icon" (click)="deleteVariant(i)"
                        matTooltip="Click to delete this variant">
                        <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px"
                            fill="#5f6368">
                            <path
                                d="M280-120q-33 0-56.5-23.5T200-200v-520h-40v-80h200v-40h240v40h200v80h-40v520q0 33-23.5 56.5T680-120H280Zm400-600H280v520h400v-520ZM360-280h80v-360h-80v360Zm160 0h80v-360h-80v360ZM280-720v520-520Z" />
                        </svg>
                    </mat-icon>
                </div>
                <div *ngIf="views?.allViews?.length === 0" class="no-data-found">
                    <svg width="248" height="185" viewBox="0 0 248 185" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M247.798 162.281H0V162.405H247.798V162.281Z" fill="#EBEBEB" />
                        <path d="M222.969 170.258H215.584V170.382H222.969V170.258Z" fill="#EBEBEB" />
                        <path d="M164.151 171.603H159.843V171.727H164.151V171.603Z" fill="#EBEBEB" />
                        <path d="M213.491 165.656H196.546V165.78H213.491V165.656Z" fill="#EBEBEB" />
                        <path d="M54.332 168.213H32.926V168.337H54.332V168.213Z" fill="#EBEBEB" />
                        <path d="M66.4651 168.213H58.7451V168.337H66.4651V168.213Z" fill="#EBEBEB" />
                        <path d="M126.255 166.55H79.8301V166.674H126.255V166.55Z" fill="#EBEBEB" />
                        <path
                            d="M117.463 140.178H21.7631C21.013 140.177 20.2939 139.879 19.7636 139.349C19.2332 138.818 18.9349 138.099 18.9341 137.349V2.828C18.9351 2.07812 19.2336 1.35927 19.7639 0.829121C20.2942 0.298968 21.0132 0.000793618 21.7631 0H117.463C118.213 0.00105816 118.931 0.299348 119.462 0.829472C119.992 1.3596 120.29 2.07829 120.291 2.828V137.347C120.291 138.097 119.993 138.817 119.463 139.347C118.932 139.878 118.213 140.177 117.463 140.178ZM21.7641 0.124001C21.0486 0.126111 20.3631 0.411253 19.8572 0.917145C19.3513 1.42304 19.0662 2.10857 19.0641 2.824V137.347C19.0662 138.062 19.3513 138.748 19.8572 139.254C20.3631 139.76 21.0486 140.045 21.7641 140.047H117.464C118.18 140.045 118.865 139.76 119.371 139.254C119.877 138.748 120.162 138.062 120.164 137.347V2.828C120.162 2.11256 119.877 1.42703 119.371 0.921143C118.865 0.415251 118.18 0.130113 117.464 0.128002L21.7641 0.124001Z"
                            fill="#EBEBEB" />
                        <path
                            d="M224.659 140.178H128.959C128.209 140.177 127.49 139.879 126.96 139.348C126.43 138.818 126.132 138.099 126.131 137.349V2.828C126.132 2.07821 126.43 1.35936 126.96 0.829182C127.49 0.299001 128.209 0.000794128 128.959 0H224.659C225.409 0.000793618 226.128 0.298968 226.658 0.829121C227.189 1.35927 227.487 2.07812 227.488 2.828V137.347C227.488 138.097 227.19 138.817 226.659 139.348C226.129 139.879 225.41 140.177 224.659 140.178ZM128.959 0.124001C128.244 0.126111 127.558 0.411253 127.052 0.917145C126.546 1.42304 126.261 2.10857 126.259 2.824V137.347C126.261 138.062 126.546 138.748 127.052 139.254C127.558 139.76 128.244 140.045 128.959 140.047H224.659C225.375 140.046 226.062 139.762 226.569 139.256C227.076 138.75 227.362 138.063 227.364 137.347V2.828C227.362 2.1117 227.076 1.42542 226.569 0.919384C226.062 0.41335 225.375 0.128793 224.659 0.128002L128.959 0.124001Z"
                            fill="#EBEBEB" />
                        <path d="M106.293 162.28H115.522L115.522 98.908H106.293V162.28Z" fill="#E6E6E6" />
                        <path d="M109.542 162.28H106.293V150.24H112.948L109.542 162.28Z" fill="#F0F0F0" />
                        <path d="M203.887 162.28H213.116V98.908H203.887V162.28Z" fill="#E6E6E6" />
                        <path d="M106.293 158.993H206.722V98.908L106.293 98.908L106.293 158.993Z" fill="#F0F0F0" />
                        <path d="M203.477 162.28H206.726V150.24H200.067L203.477 162.28Z" fill="#F0F0F0" />
                        <path d="M173.612 103.883V153.587H203.383V103.883H173.612Z" fill="#E6E6E6" />
                        <path
                            d="M185.553 105.631H191.443C193.936 105.656 196.348 104.75 198.207 103.089H178.79C180.649 104.75 183.061 105.656 185.553 105.631Z"
                            fill="#F0F0F0" />
                        <path d="M141.622 103.883V153.587H171.393V103.883H141.622Z" fill="#E6E6E6" />
                        <path
                            d="M153.262 105.631H159.152C161.644 105.656 164.056 104.75 165.915 103.089H146.498C148.357 104.75 150.769 105.656 153.262 105.631Z"
                            fill="#F0F0F0" />
                        <path d="M109.632 103.883V153.587H139.403V103.883H109.632Z" fill="#E6E6E6" />
                        <path
                            d="M121.272 105.631H127.162C129.655 105.656 132.067 104.75 133.926 103.089H114.508C116.367 104.75 118.779 105.656 121.272 105.631Z"
                            fill="#F0F0F0" />
                        <path d="M34.6819 162.28H97.6519L97.6519 20.728H34.6819L34.6819 162.28Z" fill="#F0F0F0" />
                        <path d="M34.682 162.28H96.323L96.323 20.728H34.682L34.682 162.28Z" fill="#F5F5F5" />
                        <path d="M92.5669 159.068L92.5669 23.94L38.4379 23.94L38.4379 159.068H92.5669Z"
                            fill="#F0F0F0" />
                        <path
                            d="M87.2478 49.071C87.2478 47.1331 86.8661 45.2141 86.1245 43.4237C85.3829 41.6333 84.2959 40.0065 82.9256 38.6362C81.5553 37.2659 79.9285 36.1789 78.1381 35.4373C76.3477 34.6957 74.4287 34.314 72.4908 34.314H58.5148C54.601 34.314 50.8475 35.8687 48.08 38.6362C45.3126 41.4037 43.7578 45.1572 43.7578 49.071H87.2478Z"
                            fill="#FAFAFA" />
                        <path d="M87.2478 150.669V118.387H43.7578V150.669H87.2478Z" fill="#F5F5F5" />
                        <path d="M87.2479 58.173H68.0649V114.222H87.2479V58.173Z" fill="#F5F5F5" />
                        <path d="M62.9408 58.173H43.7578V114.222H62.9408V58.173Z" fill="#F5F5F5" />
                        <path
                            d="M50.7149 85.945H42.8579C42.5349 85.9442 42.2253 85.8155 41.9968 85.5871C41.7684 85.3586 41.6397 85.0491 41.6389 84.726C41.64 84.403 41.7687 84.0936 41.9971 83.8652C42.2255 83.6368 42.5349 83.5081 42.8579 83.507H50.7149C51.0379 83.5081 51.3473 83.6368 51.5757 83.8652C51.8041 84.0936 51.9329 84.403 51.9339 84.726C51.9331 85.0491 51.8044 85.3586 51.576 85.5871C51.3476 85.8155 51.038 85.9442 50.7149 85.945Z"
                            fill="#E0E0E0" />
                        <path d="M39.4128 159.068L39.4128 23.94H38.4388L38.4388 159.068H39.4128Z" fill="#F0F0F0" />
                        <path d="M137.482 83.372L204.261 83.372V20.728L137.482 20.728V83.372Z" fill="#E6E6E6" />
                        <path d="M135.799 83.372L202.578 83.372V20.728L135.799 20.728V83.372Z" fill="#F5F5F5" />
                        <path d="M131.9 86.998L204.261 86.998V83.372L131.9 83.372V86.998Z" fill="#E6E6E6" />
                        <path d="M130.005 86.998L198.639 86.998V83.372L130.005 83.372V86.998Z" fill="#F5F5F5" />
                        <path d="M199.556 80.114V23.987L138.82 23.987V80.114H199.556Z" fill="white" />
                        <path d="M170.871 80.114V23.987H167.504V80.114H170.871Z" fill="#F5F5F5" />
                        <path d="M140.093 80.114V23.987H138.82V80.114H140.093Z" fill="#E6E6E6" />
                        <path d="M172.144 80.114V23.987H170.871V80.114H172.144Z" fill="#E6E6E6" />
                        <path
                            d="M146.459 85.033C145.231 72.308 144.376 51.659 145.04 44.753C145.051 44.6343 145.088 44.5194 145.147 44.416C145.207 44.3126 145.287 44.2229 145.384 44.153C145.512 44.059 145.664 44.003 145.823 43.9914C145.982 43.9797 146.141 44.0129 146.281 44.0871C146.422 44.1614 146.539 44.2736 146.619 44.4112C146.699 44.5487 146.739 44.706 146.734 44.865C146.617 48.287 146.482 56.84 147.211 71.595C147.574 78.94 148.097 83.97 147.738 85.031L146.459 85.033Z"
                            fill="#E6E6E6" />
                        <path
                            d="M141.881 55.704C138.381 51.821 131.242 49.06 123.964 49.074C121.107 49.08 132.657 53.591 137.464 58.574C141.305 62.712 144.467 67.4321 146.833 72.559C145.905 64.954 145.378 59.585 141.881 55.704Z"
                            fill="#E6E6E6" />
                        <path
                            d="M148.813 56.886C151.713 52.842 158.42 49.732 165.673 49.38C168.52 49.242 157.687 54.317 153.644 59.519C150.419 63.8669 147.967 68.7382 146.397 73.919C146.185 66.306 145.909 60.931 148.813 56.886Z"
                            fill="#E6E6E6" />
                        <path
                            d="M141.725 41.522C138.764 38.234 132.716 35.896 126.553 35.908C124.134 35.908 133.914 39.734 137.981 43.949C141.234 47.4528 143.912 51.4496 145.915 55.791C145.133 49.357 144.684 44.81 141.725 41.522Z"
                            fill="#E6E6E6" />
                        <path
                            d="M147.594 46.385C150.053 42.96 155.73 40.327 161.871 40.029C164.282 39.912 155.109 44.209 151.685 48.614C148.953 52.2975 146.877 56.4247 145.548 60.814C145.37 54.362 145.135 49.808 147.594 46.385Z"
                            fill="#E6E6E6" />
                        <path
                            d="M146.953 98.908C145.108 98.908 143.332 98.2022 141.99 96.9353C140.648 95.6684 139.842 93.9363 139.736 92.094L139.098 81.031H154.808L154.17 92.094C154.064 93.9363 153.257 95.6684 151.916 96.9353C150.574 98.2022 148.798 98.908 146.953 98.908Z"
                            fill="#E6E6E6" />
                        <path d="M155.825 83.546H138.084L137.576 78.946H156.337L155.825 83.546Z" fill="#E6E6E6" />
                        <path
                            d="M123.899 184.664C176.969 184.664 219.99 182.151 219.99 179.052C219.99 175.953 176.969 173.44 123.899 173.44C70.8295 173.44 27.8081 175.953 27.8081 179.052C27.8081 182.151 70.8295 184.664 123.899 184.664Z"
                            fill="#F5F5F5" />
                        <path d="M125.838 173.689L121.7 174.155L120.174 164.771L124.312 164.305L125.838 173.689Z"
                            fill="#FFC3BD" />
                        <path d="M144.364 174.593H140.232L139.203 165.024H143.335L144.364 174.593Z" fill="#FFC3BD" />
                        <path
                            d="M139.892 174.112H144.929C145.011 174.112 145.091 174.14 145.155 174.191C145.219 174.243 145.263 174.314 145.281 174.394L146.097 178.067C146.116 178.157 146.115 178.249 146.093 178.338C146.072 178.427 146.03 178.51 145.972 178.58C145.914 178.651 145.84 178.708 145.757 178.746C145.674 178.784 145.584 178.804 145.492 178.802C143.871 178.774 142.692 178.679 140.648 178.679C139.392 178.679 135.604 178.809 133.87 178.809C132.17 178.809 131.909 177.094 132.619 176.939C135.803 176.239 138.202 175.282 139.219 174.366C139.404 174.201 139.644 174.11 139.892 174.112Z"
                            fill="#263238" />
                        <path
                            d="M121.632 173.446L126.116 172.938C126.197 172.928 126.28 172.947 126.349 172.991C126.419 173.034 126.471 173.1 126.498 173.178L127.723 176.735C127.754 176.821 127.765 176.913 127.755 177.004C127.745 177.095 127.714 177.182 127.665 177.259C127.616 177.336 127.55 177.401 127.472 177.449C127.394 177.497 127.306 177.526 127.215 177.535C125.595 177.691 123.247 177.861 121.215 178.092C118.838 178.362 118.442 178.998 115.638 178.933C113.938 178.893 113.512 177.168 114.225 177.033C117.473 176.402 118.101 175.911 120.682 173.885C120.952 173.653 121.281 173.501 121.632 173.446Z"
                            fill="#263238" />
                        <path
                            d="M125.282 53.177C123.894 54.323 122.528 55.319 121.115 56.339C119.702 57.359 118.259 58.306 116.774 59.225C115.289 60.144 113.765 61.025 112.167 61.825C110.533 62.6713 108.833 63.3811 107.082 63.947C106.619 64.095 106.168 64.217 105.648 64.347C105.14 64.4572 104.626 64.5397 104.108 64.594C103.177 64.694 102.301 64.739 101.428 64.779C99.6881 64.859 97.9811 64.857 96.2761 64.845C92.8661 64.804 89.4851 64.64 86.0891 64.34L86.0381 61.24C89.3731 60.811 92.7231 60.44 96.0381 60.067C97.6951 59.887 99.3481 59.685 100.963 59.474C101.763 59.365 102.563 59.237 103.301 59.1C103.618 59.044 103.932 58.9702 104.241 58.879C104.532 58.779 104.889 58.665 105.219 58.532C106.636 57.9566 108.012 57.2837 109.336 56.518C110.707 55.731 112.079 54.886 113.427 53.988C114.775 53.09 116.117 52.16 117.447 51.211C118.777 50.262 120.106 49.257 121.354 48.311L125.282 53.177Z"
                            fill="#FFC3BD" />
                        <path
                            d="M129.321 49.645C128.727 53.551 120.503 59.526 120.503 59.526L114.603 52.203C117.188 49.4447 119.928 46.8368 122.811 44.392C125.466 42.222 129.967 45.395 129.321 49.645Z"
                            fill="#526179" />
                        <path
                            d="M87.176 61.567L84.83 59.897L84.573 65.173C84.9276 65.1739 85.2771 65.088 85.5909 64.9228C85.9046 64.7575 86.1732 64.518 86.373 64.225L87.176 61.567Z"
                            fill="#FFC3BD" />
                        <path d="M82.711 58.361L82.231 63.532L84.575 65.173L84.832 59.897L82.711 58.361Z"
                            fill="#FFC3BD" />
                        <path opacity="0.2"
                            d="M120.176 164.774L120.962 169.611L125.101 169.145L124.315 164.308L120.176 164.774Z"
                            fill="black" />
                        <path opacity="0.2" d="M143.337 165.025H139.203L139.735 169.957H143.869L143.337 165.025Z"
                            fill="black" />
                        <path
                            d="M143.396 44.517C143.704 44.586 143.995 44.7159 144.251 44.899C144.508 45.0821 144.726 45.3147 144.891 45.5831C145.057 45.8516 145.167 46.1505 145.216 46.4621C145.264 46.7738 145.249 47.092 145.173 47.398C144.276 51.1195 143.601 54.8907 143.149 58.692C142.758 61.892 142.524 64.949 142.378 67.72C142.035 74.215 142.178 79.166 141.984 80.844C138.753 80.62 126.577 79.773 119.938 79.309C117.697 61.765 120.323 50.569 121.798 46.02C122.024 45.3112 122.446 44.6811 123.016 44.2031C123.587 43.7251 124.281 43.4188 125.018 43.32C125.864 43.209 126.876 43.102 127.944 43.039C128.318 43.015 128.697 43.002 129.083 42.994C132.217 43.0311 135.347 43.2488 138.456 43.646C139.046 43.716 139.642 43.803 140.222 43.896C141.381 44.09 142.483 44.313 143.396 44.517Z"
                            fill="#6E7B8F" />
                        <path
                            d="M138.265 33.033C137.465 36.007 136.487 41.491 138.457 43.651C133.347 47.088 130.085 51.927 129.144 50.876C128.651 50.326 128.437 43.961 129.085 42.997C132.504 42.429 132.591 39.928 132.169 37.544L138.265 33.033Z"
                            fill="#FFC3BD" />
                        <path
                            d="M138.061 42.418C138.061 42.418 129.734 46.103 128.63 54.343C130.924 51.687 135.991 48.797 135.991 48.797L133.756 48.155C135.177 47.6156 136.649 47.2196 138.149 46.973C137.901 45.835 139.223 43.565 139.223 43.565L138.061 42.418Z"
                            fill="#526179" />
                        <path
                            d="M130.548 42.285C130.548 42.285 131.658 46.991 128.631 54.343C128.034 51.7769 127.216 49.2675 126.185 46.843L128.224 47.398C128.224 47.398 128.124 46.449 126.473 45.187C127.161 44.1877 128.019 43.3168 129.008 42.614L130.548 42.285Z"
                            fill="#526179" />
                        <path opacity="0.2"
                            d="M135.78 34.874L132.172 37.542C132.279 38.0972 132.337 38.6607 132.345 39.226C133.645 39.126 135.505 37.843 135.736 36.508C135.85 35.9705 135.864 35.4169 135.78 34.874Z"
                            fill="black" />
                        <path
                            d="M127.684 24.421C125.669 24.508 124.557 27.982 126.602 30.843C128.647 33.704 130.399 24.304 127.684 24.421Z"
                            fill="#263238" />
                        <path
                            d="M137.621 27.478C137.86 31.572 138.208 33.955 136.406 36.288C133.696 39.797 128.456 38.539 126.999 34.599C125.688 31.053 125.517 24.954 129.299 22.859C130.131 22.3926 131.068 22.1467 132.022 22.1447C132.976 22.1426 133.914 22.3845 134.748 22.8474C135.582 23.3103 136.284 23.9787 136.787 24.7892C137.289 25.5997 137.577 26.5253 137.621 27.478Z"
                            fill="#FFC3BD" />
                        <path
                            d="M138.222 30.624C137.127 29.9823 136.171 29.1291 135.41 28.1143C134.648 27.0995 134.096 25.9433 133.785 24.713C132.585 24.997 126.604 27.749 124.765 24.713C122.926 21.677 125.24 19.896 128.681 21.765C127.159 19.079 128.543 17.765 133.325 17.649C138.107 17.533 137.464 20.305 137.464 20.305C137.464 20.305 141.084 18.729 142.364 21.505C143.847 24.695 141.111 30.142 138.222 30.624Z"
                            fill="#263238" />
                        <path
                            d="M136.646 19.572C136.69 19.572 138.457 19.997 140.493 17.948C140.128 19.696 136.966 20.86 136.966 20.86L136.646 19.572Z"
                            fill="#263238" />
                        <path
                            d="M139.022 25.561C137.115 24.902 134.806 27.727 135.659 31.138C136.512 34.549 141.591 26.449 139.022 25.561Z"
                            fill="#263238" />
                        <path
                            d="M140.101 30.883C139.926 31.8997 139.356 32.8058 138.515 33.403C137.394 34.185 136.384 33.291 136.315 31.997C136.257 30.833 136.769 29.023 138.076 28.756C138.363 28.7024 138.658 28.7213 138.936 28.8111C139.214 28.9008 139.465 29.0585 139.666 29.2698C139.867 29.4812 140.012 29.7394 140.088 30.0212C140.164 30.3029 140.169 30.5992 140.101 30.883Z"
                            fill="#FFC3BD" />
                        <path
                            d="M126.152 125.863C127.145 113.777 134.429 80.32 134.429 80.32L119.935 79.309C119.935 79.309 114.346 112.52 113.918 125.124C113.472 138.229 118.743 167.355 118.743 167.355L125.694 166.564C125.694 166.564 125.155 137.999 126.152 125.863Z"
                            fill="#526179" />
                        <path opacity="0.5"
                            d="M126.152 125.863C127.145 113.777 134.429 80.32 134.429 80.32L119.935 79.309C119.935 79.309 114.346 112.52 113.918 125.124C113.472 138.229 118.743 167.355 118.743 167.355L125.694 166.564C125.694 166.564 125.155 137.999 126.152 125.863Z"
                            fill="white" />
                        <path opacity="0.2"
                            d="M128.969 91.65C126.047 95.386 125.995 108.507 127.125 118.201C128.202 111.208 129.899 102.327 131.386 94.924L128.969 91.65Z"
                            fill="black" />
                        <path
                            d="M127.756 79.854C127.756 79.854 129.32 115.229 131.065 127.209C132.975 140.328 137.826 169.001 137.826 169.001H145.394C145.394 169.001 144.149 141.995 143.15 129.106C142.013 114.425 141.982 80.846 141.982 80.846L127.756 79.854Z"
                            fill="#526179" />
                        <path opacity="0.5"
                            d="M127.756 79.854C127.756 79.854 129.32 115.229 131.065 127.209C132.975 140.328 137.826 169.001 137.826 169.001H145.394C145.394 169.001 144.149 141.995 143.15 129.106C142.013 114.425 141.982 80.846 141.982 80.846L127.756 79.854Z"
                            fill="white" />
                        <path d="M136.916 169.162H145.827V166.5L135.866 166.327L136.916 169.162Z" fill="#526179" />
                        <path d="M118.44 168.138L126.724 167.199L126.424 164.522L117.147 165.431L118.44 168.138Z"
                            fill="#526179" />
                        <path
                            d="M131.593 29.355C131.633 29.686 131.493 29.975 131.273 30.001C131.053 30.027 130.849 29.78 130.81 29.449C130.771 29.118 130.91 28.829 131.13 28.803C131.35 28.777 131.554 29.024 131.593 29.355Z"
                            fill="#263238" />
                        <path
                            d="M127.854 29.804C127.894 30.135 127.754 30.424 127.534 30.45C127.314 30.476 127.11 30.229 127.07 29.898C127.03 29.567 127.17 29.278 127.39 29.252C127.61 29.226 127.814 29.477 127.854 29.804Z"
                            fill="#263238" />
                        <path
                            d="M129.062 29.826C128.789 30.8847 128.372 31.9009 127.822 32.846C128.067 32.9804 128.339 33.0596 128.618 33.0781C128.897 33.0966 129.177 33.0539 129.438 32.953L129.062 29.826Z"
                            fill="#ED847E" />
                        <path
                            d="M132.61 28.006C132.575 28.0165 132.538 28.0173 132.503 28.0083C132.468 27.9993 132.436 27.9809 132.41 27.955C132.245 27.7731 132.038 27.633 131.809 27.5461C131.579 27.4592 131.331 27.428 131.087 27.455C131.036 27.4636 130.984 27.4522 130.941 27.4231C130.898 27.394 130.869 27.3495 130.858 27.299C130.853 27.2737 130.853 27.2476 130.858 27.2222C130.863 27.1969 130.873 27.1728 130.887 27.1515C130.902 27.1301 130.921 27.1119 130.942 27.0979C130.964 27.0839 130.988 27.0744 131.014 27.07C131.322 27.03 131.636 27.0648 131.928 27.1713C132.22 27.2778 132.483 27.4529 132.693 27.682C132.729 27.7192 132.749 27.7689 132.749 27.8205C132.749 27.8721 132.729 27.9218 132.693 27.959C132.67 27.9814 132.641 27.9975 132.61 28.006Z"
                            fill="#263238" />
                        <path
                            d="M131.173 34.273C131.645 34.1737 132.086 33.9623 132.459 33.6567C132.832 33.3511 133.126 32.9602 133.316 32.517C133.325 32.4905 133.324 32.4614 133.311 32.436C133.299 32.4107 133.277 32.3913 133.251 32.382C133.224 32.3727 133.195 32.3744 133.17 32.3865C133.145 32.3987 133.125 32.4205 133.116 32.447C132.934 32.8546 132.659 33.2138 132.312 33.4956C131.966 33.7773 131.558 33.9737 131.122 34.069C131.108 34.0721 131.096 34.0778 131.085 34.0858C131.073 34.0938 131.064 34.1039 131.056 34.1156C131.049 34.1273 131.044 34.1404 131.042 34.154C131.039 34.1676 131.04 34.1815 131.043 34.195C131.046 34.2085 131.052 34.2212 131.06 34.2324C131.068 34.2437 131.078 34.2533 131.09 34.2606C131.101 34.2679 131.114 34.2729 131.128 34.2752C131.142 34.2775 131.155 34.2771 131.169 34.274L131.173 34.273Z"
                            fill="#263238" />
                        <path
                            d="M126.095 28.71C126.056 28.7231 126.013 28.7231 125.974 28.71C125.926 28.6921 125.886 28.6562 125.864 28.6098C125.841 28.5634 125.837 28.5101 125.853 28.461C125.947 28.1644 126.111 27.8948 126.331 27.6751C126.551 27.4554 126.821 27.292 127.118 27.199C127.168 27.1876 127.221 27.1961 127.265 27.2225C127.309 27.249 127.342 27.2914 127.355 27.341C127.367 27.3911 127.358 27.4436 127.332 27.4877C127.306 27.5318 127.264 27.5641 127.214 27.578C126.981 27.6558 126.769 27.788 126.597 27.9636C126.425 28.1392 126.297 28.3532 126.223 28.588C126.213 28.6168 126.196 28.6428 126.173 28.6639C126.151 28.6851 126.124 28.7008 126.095 28.71Z"
                            fill="#263238" />
                        <path
                            d="M138.531 174.834C138.248 174.856 137.966 174.782 137.731 174.623C137.658 174.56 137.601 174.48 137.567 174.389C137.532 174.299 137.519 174.202 137.531 174.106C137.531 174.049 137.547 173.993 137.576 173.944C137.606 173.895 137.648 173.855 137.699 173.829C138.158 173.593 139.489 174.413 139.639 174.507C139.656 174.517 139.669 174.533 139.677 174.55C139.685 174.568 139.687 174.588 139.684 174.607C139.681 174.626 139.672 174.644 139.659 174.658C139.646 174.672 139.629 174.682 139.61 174.687C139.257 174.776 138.895 174.825 138.531 174.834ZM137.917 173.979C137.874 173.977 137.83 173.985 137.79 174.003C137.77 174.014 137.753 174.031 137.742 174.051C137.731 174.072 137.726 174.095 137.728 174.118C137.719 174.184 137.727 174.25 137.75 174.312C137.773 174.374 137.811 174.429 137.861 174.473C138.323 174.684 138.848 174.71 139.328 174.547C138.897 174.268 138.417 174.075 137.912 173.979H137.917Z"
                            fill="#526179" />
                        <path
                            d="M139.584 174.689C139.568 174.689 139.552 174.685 139.537 174.677C139.11 174.445 138.283 173.539 138.37 173.077C138.384 173.005 138.423 172.94 138.48 172.893C138.537 172.846 138.608 172.82 138.681 172.82C138.756 172.811 138.831 172.817 138.903 172.839C138.975 172.861 139.041 172.897 139.098 172.946C139.578 173.339 139.677 174.531 139.68 174.581C139.681 174.599 139.678 174.616 139.67 174.632C139.663 174.648 139.651 174.661 139.636 174.671C139.621 174.682 139.603 174.688 139.584 174.689ZM138.75 173.014H138.705C138.578 173.03 138.567 173.088 138.562 173.114C138.51 173.386 139.037 174.07 139.462 174.393C139.434 173.92 139.264 173.467 138.974 173.093C138.911 173.041 138.832 173.013 138.75 173.014Z"
                            fill="#526179" />
                        <path
                            d="M121.296 174.057L121.28 174.064C120.633 174.271 119.475 174.564 118.98 174.218C118.907 174.167 118.847 174.1 118.806 174.021C118.765 173.942 118.744 173.855 118.744 173.766C118.74 173.712 118.75 173.659 118.773 173.61C118.796 173.562 118.831 173.52 118.875 173.489C119.34 173.162 121.087 173.805 121.285 173.88C121.302 173.887 121.317 173.899 121.327 173.914C121.338 173.929 121.344 173.947 121.345 173.966C121.346 173.984 121.342 174.002 121.334 174.019C121.325 174.035 121.312 174.049 121.296 174.058V174.057ZM119.013 173.633L118.99 173.647C118.972 173.659 118.958 173.676 118.949 173.695C118.94 173.715 118.937 173.737 118.94 173.758C118.939 173.817 118.952 173.875 118.978 173.927C119.005 173.979 119.044 174.024 119.092 174.058C119.7 174.248 120.356 174.212 120.94 173.958C120.335 173.689 119.672 173.578 119.012 173.633H119.013Z"
                            fill="#526179" />
                        <path
                            d="M121.296 174.057C121.273 174.069 121.245 174.071 121.22 174.063C120.7 173.897 119.62 173.12 119.64 172.648C119.645 172.537 119.708 172.392 119.972 172.335C120.067 172.314 120.166 172.312 120.262 172.33C120.358 172.349 120.449 172.386 120.53 172.441C120.954 172.846 121.24 173.374 121.346 173.951C121.35 173.968 121.349 173.985 121.343 174.001C121.338 174.018 121.329 174.032 121.316 174.044C121.31 174.049 121.303 174.053 121.296 174.057ZM119.905 172.567C119.838 172.603 119.836 172.645 119.835 172.661C119.821 172.945 120.567 173.567 121.108 173.813C121.008 173.352 120.769 172.933 120.423 172.613C120.364 172.572 120.297 172.544 120.226 172.53C120.156 172.517 120.083 172.518 120.013 172.534C119.976 172.54 119.939 172.551 119.905 172.567Z"
                            fill="#526179" />
                        <path
                            d="M146.882 50.477C146.777 52.091 146.587 53.594 146.39 55.146C146.193 56.698 145.928 58.219 145.637 59.754C145.061 62.8892 144.211 65.9677 143.096 68.954L142.605 70.111L142.481 70.4L142.45 70.472L142.399 70.582L142.255 70.867C142.065 71.2133 141.836 71.5373 141.574 71.833C141.143 72.3181 140.639 72.7331 140.08 73.063C139.628 73.3324 139.153 73.5629 138.661 73.752C137.023 74.3428 135.311 74.7003 133.573 74.814C130.419 75.0497 127.247 74.9313 124.119 74.461L124.232 71.361L126.418 71.114C127.151 71.027 127.88 70.914 128.605 70.814C130.052 70.601 131.484 70.363 132.849 70.046C134.112 69.789 135.339 69.3821 136.505 68.834C136.82 68.7019 137.095 68.4916 137.305 68.223C137.318 68.152 137.16 68.463 137.346 67.966L137.673 67.01C138.474 64.2693 139.087 61.4772 139.507 58.653C139.742 57.215 139.943 55.764 140.137 54.309C140.331 52.854 140.491 51.369 140.644 49.974L146.882 50.477Z"
                            fill="#FFC3BD" />
                        <path
                            d="M147.242 47.481C149.265 50.874 147.542 61.538 147.542 61.538L136.942 59.579C136.942 59.579 136.368 53.479 138.386 48.785C140.577 43.676 144.791 43.37 147.242 47.481Z"
                            fill="#526179" />
                        <path
                            d="M121.264 80.952L115.639 54.788C115.369 53.6707 114.743 52.6715 113.856 51.941C112.968 51.2105 111.867 50.7882 110.719 50.738H89.166C90.3145 50.788 91.4156 51.2102 92.3033 51.9407C93.1909 52.6712 93.817 53.6706 94.087 54.788L99.712 80.952C99.982 82.0693 100.608 83.0685 101.495 83.799C102.383 84.5295 103.484 84.9517 104.632 85.002H126.184C125.036 84.9517 123.935 84.5295 123.047 83.799C122.16 83.0685 121.534 82.0693 121.264 80.952Z"
                            fill="#263238" />
                        <path opacity="0.7"
                            d="M121.264 80.952L115.639 54.788C115.369 53.6707 114.743 52.6715 113.856 51.941C112.968 51.2105 111.867 50.7882 110.719 50.738H89.166C90.3145 50.788 91.4156 51.2102 92.3033 51.9407C93.1909 52.6712 93.817 53.6706 94.087 54.788L99.712 80.952C99.982 82.0693 100.608 83.0685 101.495 83.799C102.383 84.5295 103.484 84.9517 104.632 85.002H126.184C125.036 84.9517 123.935 84.5295 123.047 83.799C122.16 83.0685 121.534 82.0693 121.264 80.952Z"
                            fill="#526179" />
                        <path
                            d="M89.166 50.738H110.718C109.615 50.7587 108.557 51.1816 107.743 51.9274C106.93 52.6732 106.416 53.6905 106.3 54.788L100.709 115.588C100.592 116.685 100.079 117.703 99.2658 118.448C98.4523 119.194 97.3944 119.617 96.291 119.638H82.839C82.3209 119.648 81.8066 119.546 81.3307 119.341C80.8549 119.136 80.4283 118.831 80.0797 118.448C79.7311 118.064 79.4684 117.611 79.3093 117.118C79.1502 116.624 79.0982 116.103 79.157 115.588L84.749 54.788C84.8653 53.6906 85.3783 52.6735 86.1917 51.9278C87.005 51.182 88.0627 50.7589 89.166 50.738Z"
                            fill="#526179" />
                        <path
                            d="M92.609 115.587C92.5503 116.102 92.6022 116.623 92.7613 117.117C92.9204 117.61 93.1831 118.063 93.5317 118.447C93.8804 118.83 94.3069 119.135 94.7828 119.34C95.2586 119.545 95.7729 119.647 96.2911 119.637H74.7391C74.2208 119.647 73.7065 119.546 73.2306 119.34C72.7546 119.135 72.328 118.831 71.9793 118.447C71.6307 118.064 71.368 117.61 71.209 117.117C71.0499 116.624 70.9981 116.102 71.057 115.587H92.609Z"
                            fill="#263238" />
                        <path opacity="0.7"
                            d="M92.609 115.587C92.5503 116.102 92.6022 116.623 92.7613 117.117C92.9204 117.61 93.1831 118.063 93.5317 118.447C93.8804 118.83 94.3069 119.135 94.7828 119.34C95.2586 119.545 95.7729 119.647 96.2911 119.637H74.7391C74.2208 119.647 73.7065 119.546 73.2306 119.34C72.7546 119.135 72.328 118.831 71.9793 118.447C71.6307 118.064 71.368 117.61 71.209 117.117C71.0499 116.624 70.9981 116.102 71.057 115.587H92.609Z"
                            fill="#526179" />
                        <g opacity="0.5">
                            <path opacity="0.5"
                                d="M102.565 57.55H87.9809C87.8683 57.5515 87.7567 57.5291 87.6534 57.4842C87.5501 57.4393 87.4576 57.3729 87.3819 57.2895C87.3062 57.2061 87.2492 57.1075 87.2145 57.0004C87.1799 56.8932 87.1684 56.7799 87.1809 56.668C87.2069 56.4292 87.3188 56.2081 87.4958 56.0458C87.6728 55.8835 87.9028 55.7911 88.1429 55.786H102.725C102.838 55.7844 102.949 55.8069 103.052 55.8518C103.156 55.8967 103.248 55.9631 103.324 56.0465C103.4 56.1299 103.457 56.2285 103.491 56.3356C103.526 56.4428 103.537 56.5561 103.525 56.668C103.499 56.9064 103.387 57.1273 103.211 57.2896C103.034 57.4518 102.805 57.5444 102.565 57.55Z"
                                fill="white" />
                            <path opacity="0.5"
                                d="M102.131 62.321H87.5471C87.4345 62.3225 87.3228 62.3001 87.2196 62.2552C87.1163 62.2103 87.0237 62.1439 86.9481 62.0605C86.8724 61.9771 86.8154 61.8785 86.7807 61.7714C86.7461 61.6642 86.7346 61.5509 86.7471 61.439C86.773 61.2002 86.8849 60.9791 87.062 60.8168C87.239 60.6545 87.469 60.5622 87.7091 60.557H102.293C102.406 60.5555 102.517 60.5779 102.621 60.6228C102.724 60.6677 102.816 60.7341 102.892 60.8175C102.968 60.9009 103.025 60.9995 103.059 61.1066C103.094 61.2138 103.106 61.3271 103.093 61.439C103.067 61.6778 102.956 61.8991 102.778 62.0615C102.601 62.2238 102.371 62.3161 102.131 62.321Z"
                                fill="white" />
                            <path opacity="0.5"
                                d="M98.122 67.092H87.113C87.0004 67.0935 86.8888 67.0711 86.7855 67.0262C86.6822 66.9813 86.5897 66.9149 86.514 66.8315C86.4383 66.7481 86.3813 66.6495 86.3466 66.5424C86.312 66.4352 86.3005 66.3219 86.313 66.21C86.3389 65.9712 86.4509 65.7501 86.6279 65.5878C86.8049 65.4255 87.0349 65.3332 87.275 65.328H98.284C98.3966 65.3265 98.5083 65.3489 98.6115 65.3938C98.7148 65.4387 98.8074 65.5051 98.883 65.5885C98.9587 65.6719 99.0157 65.7705 99.0504 65.8776C99.085 65.9848 99.0965 66.0981 99.084 66.21C99.0581 66.4487 98.9462 66.6699 98.7692 66.8322C98.5921 66.9945 98.3621 67.0868 98.122 67.092Z"
                                fill="white" />
                            <path opacity="0.5"
                                d="M90.4129 71.862H86.6799C86.5673 71.8635 86.4557 71.8411 86.3524 71.7962C86.2491 71.7513 86.1566 71.6849 86.0809 71.6015C86.0052 71.5181 85.9482 71.4195 85.9135 71.3124C85.8789 71.2052 85.8674 71.0919 85.8799 70.98C85.9056 70.7413 86.0173 70.5202 86.1942 70.3579C86.371 70.1955 86.6009 70.1032 86.8409 70.098H90.5749C90.6875 70.0964 90.7992 70.1189 90.9024 70.1638C91.0057 70.2087 91.0983 70.2751 91.1739 70.3585C91.2496 70.4419 91.3066 70.5405 91.3413 70.6476C91.3759 70.7548 91.3874 70.8681 91.3749 70.98C91.349 71.2187 91.2371 71.4399 91.06 71.6022C90.883 71.7645 90.653 71.8568 90.4129 71.862Z"
                                fill="white" />
                            <path opacity="0.5"
                                d="M100.829 76.633H86.2409C86.1283 76.6345 86.0167 76.6121 85.9134 76.5672C85.8101 76.5223 85.7176 76.4559 85.6419 76.3725C85.5663 76.2891 85.5092 76.1905 85.4746 76.0834C85.4399 75.9762 85.4284 75.8629 85.4409 75.751C85.4669 75.5123 85.5788 75.2911 85.7558 75.1288C85.9328 74.9665 86.1628 74.8742 86.4029 74.869H100.987C101.1 74.8675 101.211 74.8899 101.314 74.9348C101.418 74.9797 101.51 75.0461 101.586 75.1295C101.662 75.2129 101.719 75.3115 101.753 75.4186C101.788 75.5258 101.799 75.6391 101.787 75.751C101.761 75.9891 101.65 76.2097 101.474 76.3719C101.297 76.5341 101.068 76.6269 100.829 76.633Z"
                                fill="white" />
                            <path opacity="0.5"
                                d="M94.652 81.404H85.812C85.6994 81.4055 85.5877 81.3831 85.4844 81.3382C85.3812 81.2933 85.2886 81.2269 85.213 81.1435C85.1373 81.0601 85.0802 80.9615 85.0456 80.8544C85.0109 80.7472 84.9995 80.6339 85.012 80.522C85.0377 80.2833 85.1494 80.0622 85.3262 79.8999C85.5031 79.7375 85.733 79.6452 85.973 79.64H94.813C94.9256 79.6384 95.0372 79.6609 95.1405 79.7058C95.2438 79.7507 95.3363 79.8171 95.412 79.9005C95.4877 79.9839 95.5447 80.0825 95.5793 80.1896C95.614 80.2968 95.6255 80.4101 95.613 80.522C95.5871 80.7606 95.4753 80.9816 95.2985 81.1439C95.1217 81.3062 94.8919 81.3986 94.652 81.404Z"
                                fill="white" />
                            <path opacity="0.5"
                                d="M99.9619 86.174H85.3779C85.2653 86.1755 85.1536 86.1531 85.0504 86.1082C84.9471 86.0633 84.8545 85.9969 84.7789 85.9135C84.7032 85.8301 84.6462 85.7315 84.6115 85.6244C84.5769 85.5172 84.5654 85.4039 84.5779 85.292C84.6038 85.0533 84.7157 84.8321 84.8928 84.6698C85.0698 84.5075 85.2998 84.4152 85.5399 84.41H100.125C100.238 84.4085 100.349 84.4309 100.452 84.4758C100.556 84.5207 100.648 84.5871 100.724 84.6705C100.8 84.7539 100.857 84.8525 100.891 84.9596C100.926 85.0668 100.937 85.1801 100.925 85.292C100.899 85.5309 100.787 85.7522 100.61 85.9145C100.432 86.0769 100.202 86.1691 99.9619 86.174Z"
                                fill="white" />
                            <path opacity="0.5"
                                d="M95.9531 90.945H84.9441C84.8315 90.9465 84.7198 90.9241 84.6165 90.8792C84.5132 90.8343 84.4207 90.7679 84.345 90.6845C84.2694 90.6011 84.2123 90.5025 84.1777 90.3954C84.143 90.2882 84.1316 90.1749 84.1441 90.063C84.17 89.8242 84.2819 89.6031 84.4589 89.4408C84.6359 89.2785 84.866 89.1861 85.1061 89.181H96.1151C96.2277 89.1794 96.3393 89.2019 96.4426 89.2468C96.5459 89.2917 96.6384 89.3581 96.7141 89.4415C96.7897 89.5249 96.8468 89.6235 96.8814 89.7306C96.9161 89.8378 96.9275 89.9511 96.9151 90.063C96.8891 90.3017 96.7772 90.5229 96.6002 90.6852C96.4232 90.8475 96.1932 90.9398 95.9531 90.945Z"
                                fill="white" />
                            <path opacity="0.5"
                                d="M88.244 95.715H84.508C84.3954 95.7165 84.2838 95.6941 84.1805 95.6492C84.0772 95.6043 83.9847 95.5379 83.909 95.4545C83.8334 95.3711 83.7763 95.2725 83.7417 95.1654C83.707 95.0582 83.6955 94.9449 83.708 94.833C83.734 94.5943 83.8459 94.3731 84.0229 94.2108C84.1999 94.0485 84.4299 93.9562 84.67 93.951H88.408C88.5206 93.9495 88.6323 93.9719 88.7356 94.0168C88.8388 94.0617 88.9314 94.1281 89.007 94.2115C89.0827 94.2949 89.1398 94.3935 89.1744 94.5006C89.2091 94.6078 89.2205 94.7211 89.208 94.833C89.182 95.0721 89.0699 95.2935 88.8924 95.4559C88.715 95.6182 88.4845 95.7103 88.244 95.715Z"
                                fill="white" />
                            <path opacity="0.5"
                                d="M98.6599 100.487H84.0759C83.9633 100.489 83.8516 100.466 83.7484 100.421C83.6451 100.376 83.5525 100.31 83.4769 100.227C83.4012 100.143 83.3442 100.045 83.3095 99.9374C83.2749 99.8302 83.2634 99.7169 83.2759 99.605C83.3018 99.3662 83.4137 99.1451 83.5908 98.9828C83.7678 98.8205 83.9978 98.7282 84.2379 98.723H98.8219C98.9345 98.7215 99.0462 98.7439 99.1494 98.7888C99.2527 98.8337 99.3453 98.9001 99.4209 98.9835C99.4966 99.0669 99.5536 99.1655 99.5883 99.2726C99.6229 99.3798 99.6344 99.4931 99.6219 99.605C99.596 99.8438 99.4841 100.065 99.307 100.227C99.13 100.39 98.9 100.482 98.6599 100.487Z"
                                fill="white" />
                            <path opacity="0.5"
                                d="M92.483 105.257H83.643C83.5304 105.259 83.4188 105.236 83.3155 105.191C83.2122 105.146 83.1197 105.08 83.044 104.997C82.9684 104.913 82.9113 104.815 82.8767 104.707C82.842 104.6 82.8305 104.487 82.843 104.375C82.8688 104.136 82.9806 103.915 83.1577 103.753C83.3347 103.59 83.5649 103.498 83.805 103.493H92.644C92.7567 103.491 92.8683 103.514 92.9716 103.559C93.0749 103.604 93.1674 103.67 93.2431 103.754C93.3187 103.837 93.3758 103.935 93.4104 104.043C93.4451 104.15 93.4565 104.263 93.444 104.375C93.4181 104.614 93.3064 104.835 93.1296 104.997C92.9528 105.159 92.723 105.252 92.483 105.257Z"
                                fill="white" />
                            <path opacity="0.5"
                                d="M97.792 110.028H83.208C83.0954 110.03 82.9837 110.007 82.8804 109.962C82.7772 109.917 82.6846 109.851 82.609 109.768C82.5333 109.684 82.4762 109.586 82.4416 109.478C82.4069 109.371 82.3955 109.258 82.408 109.146C82.4339 108.907 82.5458 108.686 82.7228 108.524C82.8999 108.362 83.1299 108.269 83.37 108.264H97.952C98.0646 108.262 98.1762 108.285 98.2795 108.33C98.3828 108.375 98.4753 108.441 98.551 108.525C98.6267 108.608 98.6837 108.706 98.7184 108.814C98.753 108.921 98.7645 109.034 98.752 109.146C98.7261 109.384 98.6145 109.605 98.4379 109.768C98.2613 109.93 98.0317 110.022 97.792 110.028Z"
                                fill="white" />
                        </g>
                        <path
                            d="M125.503 71.699L122.337 68.948L120.853 74.012C120.853 74.012 124.069 75.756 125.183 74.155L125.503 71.699Z"
                            fill="#FFC3BD" />
                        <path d="M118.888 68.75L117.858 72.994L120.85 74.014L122.334 68.949L118.888 68.75Z"
                            fill="#FFC3BD" />
                    </svg>
                    <span style="font-weight: 500; color: #5F6C81;">No Version History Found</span>
                </div>
            </div>
        </mat-sidenav>
          
        <!-- Main content -->
        <mat-sidenav-content>
            <div>
                <div class="dashboard-styles">
                    <div class="sticky-header-class">


                        <div class="header-content-main">
                            <div class="back-icn-class" (click)="backToReports()">
                                <mat-icon class="icn-style-class"
                                    matTooltip="Click to return to the reports">keyboard_arrow_left</mat-icon>
                            </div>

                            <span class="header-label">{{ report_name }}</span>

                            <div class="duration">
                                <span style="margin-right: 4px;margin-top: 2px;">
                                    <svg width="8" height="14" viewBox="0 0 8 14" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M2.12299 7.00001L7.14483 12.0218C7.27727 12.1543 7.34183 12.3102 7.33849 12.4897C7.33505 12.6692 7.2671 12.8252 7.13466 12.9577C7.0021 13.0901 6.8461 13.1563 6.66666 13.1563C6.48722 13.1563 6.33122 13.0901 6.19866 12.9577L1.08466 7.85384C0.964103 7.73329 0.87477 7.59823 0.816659 7.44868C0.758548 7.29912 0.729492 7.14957 0.729492 7.00001C0.729492 6.85045 0.758548 6.7009 0.816659 6.55134C0.87477 6.40179 0.964103 6.26673 1.08466 6.14618L6.19866 1.03201C6.33122 0.899565 6.48894 0.835065 6.67183 0.83851C6.85472 0.841954 7.01238 0.909898 7.14483 1.04234C7.27727 1.17479 7.34349 1.33079 7.34349 1.51034C7.34349 1.68979 7.27727 1.84573 7.14483 1.97818L2.12299 7.00001Z"
                                            fill="#45546E" />
                                    </svg>
                                </span>
                                <span matTooltip="Click to set a custom duration">
                                    <svg width="14" height="16" viewBox="0 0 14 16" fill="none"
                                        xmlns="http://www.w3.org/2000/svg" class="ngx-daterangepicker-action">
                                        <path
                                            d="M9.01882 12.4995C8.53707 12.4995 8.1282 12.3315 7.7922 11.9955C7.4562 11.6594 7.2882 11.2505 7.2882 10.7687C7.2882 10.287 7.4562 9.87809 7.7922 9.54209C8.1282 9.20609 8.53707 9.03809 9.01882 9.03809C9.50057 9.03809 9.90951 9.20609 10.2456 9.54209C10.5816 9.87809 10.7496 10.287 10.7496 10.7687C10.7496 11.2505 10.5816 11.6594 10.2456 11.9955C9.90951 12.3315 9.50057 12.4995 9.01882 12.4995ZM1.98045 15.1245C1.60157 15.1245 1.28088 14.9933 1.01838 14.7308C0.755884 14.4683 0.624634 14.1476 0.624634 13.7687V3.73034C0.624634 3.35146 0.755884 3.03077 1.01838 2.76827C1.28088 2.50577 1.60157 2.37452 1.98045 2.37452H3.01882V0.788086H4.1727V2.37452H9.85545V0.788086H10.9804V2.37452H12.0188C12.3977 2.37452 12.7184 2.50577 12.9809 2.76827C13.2434 3.03077 13.3746 3.35146 13.3746 3.73034V13.7687C13.3746 14.1476 13.2434 14.4683 12.9809 14.7308C12.7184 14.9933 12.3977 15.1245 12.0188 15.1245H1.98045ZM1.98045 13.9995H12.0188C12.0766 13.9995 12.1294 13.9755 12.1774 13.9273C12.2256 13.8793 12.2496 13.8265 12.2496 13.7687V6.73034H1.74963V13.7687C1.74963 13.8265 1.7737 13.8793 1.82182 13.9273C1.86982 13.9755 1.9227 13.9995 1.98045 13.9995ZM1.74963 5.60534H12.2496V3.73034C12.2496 3.67259 12.2256 3.61971 12.1774 3.57171C12.1294 3.52359 12.0766 3.49952 12.0188 3.49952H1.98045C1.9227 3.49952 1.86982 3.52359 1.82182 3.57171C1.7737 3.61971 1.74963 3.67259 1.74963 3.73034V5.60534Z"
                                            fill="#6E7B8F" />
                                    </svg>
                                </span>
                                <input matInput [(ngModel)]="applied_duration" type="text"
                                    class="filter-date custom-input" [showCustomRangeLabel]="false" [locale]="{
                            applyLabel: 'Apply',
                            displayFormat: 'DD MMM YYYY',
                            customRangeLabel: 'Custom Range',
                            format: 'YYYY-MM-DD',
                            clearLabel: 'clear'
                        }" [alwaysShowCalendars]="true" [drops]="'down'" [opens]="'right'"
                                    [ranges]="dateRangePickerRanges" [linkedCalendars]="true"
                                    [keepCalendarOpeningWithRange]="true" [closeOnAutoApply]="true" autocomplete="off"
                                    [showCancel]="true"
                                    [autoApply]="false" ngxDaterangepickerMd (datesUpdated)="onDateInputChange($event)"
                                    readonly />
                                <!-- <div class="date">
                                    {{ getDateFormat(applied_duration?.startDate) ?
                                    getDateFormat(applied_duration?.startDate) :
                                    '' }} - {{ getDateFormat(applied_duration?.endDate) ?
                                    getDateFormat(applied_duration?.endDate) : '' }}
                                                                         
                                </div> -->
                                <span style="margin-left: -11px;margin-top: 2px;">
                                    <svg width="8" height="14" viewBox="0 0 8 14" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M5.88722 6.99992L0.865384 1.97809C0.73294 1.84564 0.668439 1.68797 0.671884 1.50509C0.675328 1.3222 0.743273 1.16447 0.875717 1.03192C1.00816 0.899474 1.16583 0.833252 1.34872 0.833252C1.53161 0.833252 1.68933 0.899474 1.82188 1.03192L6.92572 6.14609C7.04616 6.26664 7.13544 6.4017 7.19355 6.55125C7.25177 6.70081 7.28088 6.85036 7.28088 6.99992C7.28088 7.14947 7.25177 7.29903 7.19355 7.44859C7.13544 7.59814 7.04616 7.7332 6.92572 7.85375L1.81155 12.9679C1.67911 13.1004 1.52316 13.1649 1.34372 13.1614C1.16416 13.158 1.00816 13.09 0.875717 12.9576C0.743273 12.8251 0.677051 12.6675 0.677051 12.4846C0.677051 12.3017 0.743273 12.144 0.875717 12.0114L5.88722 6.99992Z"
                                            fill="#45546E" />
                                    </svg>
                                </span>
                            </div>

                               <!-- Save a variant as Admin -->
                               <!-- <div *ngIf="isAdmin">
                            <button data-step="9" data-intro="Click to Save as a new version" data-position="right" mat-icon-button
                                disableRipple="true" disableFocus="true" 
                                [satPopoverAnchor]="saveAsAdmin" (click)="saveAsAdmin.toggle()">
                                <span matTooltip="Click to save a variant as Admin, making it visible to all users">
                                    <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#5f6368"><path d="M680-280q25 0 42.5-17.5T740-340q0-25-17.5-42.5T680-400q-25 0-42.5 17.5T620-340q0 25 17.5 42.5T680-280Zm0 120q31 0 57-14.5t42-38.5q-22-13-47-20t-52-7q-27 0-52 7t-47 20q16 24 42 38.5t57 14.5ZM480-80q-139-35-229.5-159.5T160-516v-244l320-120 320 120v227q-19-8-39-14.5t-41-9.5v-147l-240-90-240 90v188q0 47 12.5 94t35 89.5Q310-290 342-254t71 60q11 32 29 61t41 52q-1 0-1.5.5t-1.5.5Zm200 0q-83 0-141.5-58.5T480-280q0-83 58.5-141.5T680-480q83 0 141.5 58.5T880-280q0 83-58.5 141.5T680-80ZM480-494Z"/></svg>
                                </span>
                            </button>
                        </div> -->
                        </div>


                        <div class="header-action-items">
                            <!-- <mat-menu #options="matMenu" style="min-height: none !important;">
                                <div class="card">
                                    <div class="card-body pl-3 pr-3 pt-2 pb-2">
                                        <div class="row">
                                            <div class="col-12">
                                                <button mat-icon-button matTooltip="Hide Data Fields"
                                                    class="iconsSize ml-1" (click)="showDataFieldsFn()">
                                                    <mat-icon class="iconsSize" matBadge="!" matBadgeSize="small"
                                                        [matBadgeHidden]="showDataFields"
                                                        matBadgeColor="warn">menu_open</mat-icon>
                                                </button>
                                                <button mat-icon-button matTooltip="Hide Row Fields"
                                                    class="iconsSize ml-1" (click)="showRowFieldsFn()">
                                                    <mat-icon class="iconsSize" matBadge="!" matBadgeSize="small"
                                                        [matBadgeHidden]="showRowFields"
                                                        matBadgeColor="warn">menu_open</mat-icon>
                                                </button>
                                                <button mat-icon-button matTooltip="Hide Column Fields"
                                                    class="iconsSize ml-1" (click)="showColumnFieldsFn()">
                                                    <mat-icon class="iconsSize" matBadge="!" matBadgeSize="small"
                                                        [matBadgeHidden]="showColumnFields"
                                                        matBadgeColor="warn">view_column</mat-icon>
                                                </button>
                                                <button mat-icon-button matTooltip="Hide Filter Fields"
                                                    class="iconsSize ml-1" (click)="showFilterFieldsFn()">
                                                    <mat-icon class="iconsSize" matBadge="!" matBadgeSize="small"
                                                        [matBadgeHidden]="showFilterFields"
                                                        matBadgeColor="warn">filter_list</mat-icon>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </mat-menu> -->

                            <!-- <sat-popover #saveAs horizontalAlign="after" verticalAlign="below">
                                <div style="
                                  box-shadow: 0 3px 1px -2px rgba(0, 0, 0, 0.2),
                        0 2px 2px 0 rgba(0, 0, 0, 0.14),
                        0 1px 5px 0 rgba(0, 0, 0, 0.12) !important;
                      padding: 16px !important;
                      background-color: white !important;
                      max-width: 300px !important;
                      line-height: 33px !important;
                    ">
                                    <div class="row" style="display: flex; justify-content: flex-end;">
                                        <button mat-icon-button (click)="saveAs.toggle()" matTooltip="Close">
                                            <mat-icon>close</mat-icon>
                                        </button>
                                    </div>
                                    <div class="row">
                                        <mat-form-field>
                                            <input matInput placeholder="Save as - Name" [formControl]="versionName"
                                                style="color: #111434 !important;" required />
                                            <mat-error *ngIf="versionName.hasError('required')"
                                                style="color: #111434 !important;">
                                                Name is required
                                            </mat-error>
                                        </mat-form-field>
                                    </div>
                                    <div class="row">
                                        <button mat-raised-button [disabled]="!versionName.value"
                                            [ngStyle]="{'background-color': versionName.value ? '#79BA44' : 'lightgray', 'color': 'white'}"
                                            (click)="saveState(); saveAs.toggle()">
                                            Save
                                        </button>

                                    </div>
                                </div>
                            </sat-popover> -->

                            <sat-popover #saveAs horizontalAlign="after" verticalAlign="below"
                                style="max-width: 300px;">
                                <div class="save-as-popup" style="padding: 10px;
                                width: 75%;
                                box-shadow: 0 0 0 1px rgba(0, 0, 0, .1), 0 5px 7px 3px rgba(0, 0, 0, .1), 0 9px 3px -6px rgba(0, 0, 0, .1), 1px 0 3px 1px rgba(0, 0, 0, .2); 
                                background-color: white; position: relative;">
                                    <div class="row">
                                        <span>Save As - Variant Name</span>
                                        <mat-form-field appearance="outline">
                                            <input matInput placeholder="Save as - Name" [formControl]="versionName"
                                                style="color: #111434; width: 65%;" required />
                                            <mat-error *ngIf="versionName.hasError('required')"
                                                style="color: #111434; margin-top: 10px;">
                                                Name is required
                                            </mat-error>
                                        </mat-form-field>
                                    </div>
                                    <div class="row"
                                        style="display: flex; gap:10px; justify-content: end; margin-top: 10px;">
                                        <button mat-raised-button style="background-color: transparent; color: #45546e;
                                        border: 1px solid #45546e;" (click)="saveAs.toggle()">
                                            Cancel
                                        </button>
                                        <button mat-raised-button [disabled]="!versionName.value"
                                            [ngStyle]="{'background-color': versionName.value ? '#79BA44' : 'lightgray', 'color': 'white'}"
                                            (click)="saveState(); saveAs.toggle()">
                                            Save
                                        </button>
                                    </div>
                                </div>
                            </sat-popover>

                            <!-- <sat-popover #saveAsAdmin horizontalAlign="after" verticalAlign="below"
                            style="max-width: 300px;">
                            <div class="save-as-popup" style="padding: 10px;
                            width: 75%;
                            box-shadow: 0 0 0 1px rgba(0, 0, 0, .1), 0 5px 7px 3px rgba(0, 0, 0, .1), 0 9px 3px -6px rgba(0, 0, 0, .1), 1px 0 3px 1px rgba(0, 0, 0, .2); 
                            background-color: white; position: relative;">
                                <div class="row">
                                    <span>Save As - Variant Name</span>
                                    <mat-form-field appearance="outline">
                                        <input matInput placeholder="Save as - Name" [formControl]="versionName"
                                            style="color: #111434; width: 65%;" required />
                                        <mat-error *ngIf="versionName.hasError('required')"
                                            style="color: #111434; margin-top: 10px;">
                                            Name is required
                                        </mat-error>
                                    </mat-form-field>
                                </div>
                                <div class="row"
                                    style="display: flex; gap:10px; justify-content: end; margin-top: 10px;">
                                    <button mat-raised-button style="background-color: transparent; color: #45546e;
                                    border: 1px solid #45546e;" (click)="saveAsAdmin.toggle()">
                                        Cancel
                                    </button>
                                    <button mat-raised-button [disabled]="!versionName.value"
                                        [ngStyle]="{'background-color': versionName.value ? '#79BA44' : 'lightgray', 'color': 'white'}"
                                        (click)="saveStateAsAdmin(); saveAsAdmin.toggle()">
                                        Save
                                    </button>
                                </div>
                            </div>
                        </sat-popover> -->

                            <!-- Refresh Icon -->
                            <dx-button style="box-shadow: none !important; background-color: transparent !important;"
                                (onClick)="refresh()">
                                <span matTooltip="Refresh the report view by clicking here.">
                                    <svg width="22" height="16" viewBox="0 0 22 16" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M11.05 15.75C8.88718 15.75 7.04651 14.9974 5.52801 13.4923C4.00934 11.9871 3.25001 10.1563 3.25001 8V7.21925L1.92701 8.54225C1.78851 8.68075 1.61443 8.75158 1.40476 8.75475C1.19526 8.75792 1.01801 8.68708 0.873012 8.54225C0.728178 8.39742 0.655762 8.22183 0.655762 8.0155C0.655762 7.809 0.728178 7.63333 0.873012 7.4885L3.36726 4.99425C3.54809 4.81342 3.75901 4.723 4.00001 4.723C4.24101 4.723 4.45193 4.81342 4.63276 4.99425L7.12701 7.4885C7.26534 7.627 7.33618 7.801 7.33951 8.0105C7.34268 8.22017 7.27184 8.39742 7.12701 8.54225C6.98201 8.68708 6.80634 8.7595 6.60001 8.7595C6.39368 8.7595 6.21801 8.68708 6.07301 8.54225L4.75001 7.21925V8C4.75001 9.73717 5.36151 11.2132 6.58451 12.428C7.80768 13.6427 9.29618 14.25 11.05 14.25C11.3423 14.25 11.6337 14.2302 11.924 14.1905C12.2145 14.1507 12.5008 14.0827 12.7828 13.9865C12.9956 13.9225 13.1965 13.9293 13.3855 14.0068C13.5747 14.0843 13.7167 14.216 13.8115 14.402C13.9063 14.5982 13.9124 14.7933 13.8298 14.9875C13.7471 15.1817 13.5993 15.3108 13.3865 15.375C13.0097 15.5083 12.6244 15.6042 12.2308 15.6625C11.8373 15.7208 11.4437 15.75 11.05 15.75ZM10.95 1.75C10.6577 1.75 10.3663 1.76983 10.076 1.8095C9.78551 1.84933 9.49926 1.91733 9.21726 2.0135C9.00443 2.0775 8.80093 2.07075 8.60676 1.99325C8.41259 1.91575 8.26801 1.784 8.17301 1.598C8.07818 1.41217 8.07051 1.22217 8.15001 1.028C8.22951 0.833667 8.37051 0.7045 8.57301 0.6405C8.96018 0.507167 9.35218 0.40875 9.74901 0.34525C10.1458 0.28175 10.5462 0.25 10.95 0.25C13.1128 0.25 14.9535 1.00258 16.472 2.50775C17.9907 4.01292 18.75 5.84367 18.75 8V8.78075L20.073 7.45775C20.2115 7.31925 20.3856 7.24842 20.5953 7.24525C20.8048 7.24208 20.982 7.31292 21.127 7.45775C21.2718 7.60258 21.3443 7.77817 21.3443 7.9845C21.3443 8.191 21.2718 8.36667 21.127 8.5115L18.6328 11.0058C18.4519 11.1866 18.241 11.277 18 11.277C17.759 11.277 17.5481 11.1866 17.3673 11.0058L14.873 8.5115C14.7347 8.373 14.6638 8.199 14.6605 7.9895C14.6573 7.77983 14.7282 7.60258 14.873 7.45775C15.018 7.31292 15.1937 7.2405 15.4 7.2405C15.6063 7.2405 15.782 7.31292 15.927 7.45775L17.25 8.78075V8C17.25 6.26283 16.6385 4.78683 15.4155 3.572C14.1923 2.35733 12.7038 1.75 10.95 1.75Z"
                                            fill="#8B95A5" />
                                    </svg>
                                </span>
                            </dx-button>

                            <!-- Edit icon
                            <div [matMenuTriggerFor]="options">
                                <span matTooltip="Click to modify the field view">
                                    <svg width="22" height="18" viewBox="0 0 18 18" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M2 16H3.2615L13.498 5.7635L12.2365 4.502L2 14.7385V16ZM1.404 17.5C1.14783 17.5 0.933167 17.4133 0.76 17.24C0.586667 17.0668 0.5 16.8522 0.5 16.596V14.8635C0.5 14.6197 0.546833 14.3873 0.6405 14.1663C0.734 13.9453 0.862833 13.7527 1.027 13.5885L13.6905 0.93075C13.8417 0.793416 14.0086 0.687333 14.1913 0.6125C14.3741 0.5375 14.5658 0.5 14.7663 0.5C14.9668 0.5 15.1609 0.535584 15.3488 0.60675C15.5367 0.677917 15.7032 0.791083 15.848 0.946249L17.0693 2.18275C17.2244 2.32758 17.335 2.49425 17.401 2.68275C17.467 2.87125 17.5 3.05975 17.5 3.24825C17.5 3.44942 17.4657 3.64133 17.397 3.824C17.3283 4.00683 17.2191 4.17383 17.0693 4.325L4.4115 16.973C4.24733 17.1372 4.05475 17.266 3.83375 17.3595C3.61275 17.4532 3.38033 17.5 3.1365 17.5H1.404ZM12.8562 5.14375L12.2365 4.502L13.498 5.7635L12.8562 5.14375Z"
                                            fill="#8B95A5" />
                                    </svg>
                                </span>
                            </div> -->

                            <!--Save icon -->
                            <div toolTip="Save" (click)="views?.allViews?.length !== 0 && stateUpdate()"
                                [ngClass]="{'disabled': views?.allViews?.length == 0}" style="cursor: pointer;">
                                <span matTooltip="Click to Save the view">
                                    <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960"
                                        width="24px" fill="#5f6368">
                                        <path
                                            d="M840-680v480q0 33-23.5 56.5T760-120H200q-33 0-56.5-23.5T120-200v-560q0-33 23.5-56.5T200-840h480l160 160Zm-80 34L646-760H200v560h560v-446ZM480-240q50 0 
             85-35t35-85q0-50-35-85t-85-35q-50 0-85 35t-35 85q0 50 35 85t85 35ZM240-560h360v-160H240v160Zm-40-86v446-560 114Z"
                                            fill="#8B95A5" />
                                    </svg>
                                </span>
                            </div>

                            <!-- column chooser Icon -->
                            <dx-button style="box-shadow: none !important; background-color: transparent !important;"
                                (onClick)="pivotGrid1.getFieldChooserPopup().show()">
                                <span matTooltip="Click to modify the view or hide columns/Rows">
                                    <svg width="18" height="18" viewBox="0 0 18 18" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M8.99975 17.75C8.78708 17.75 8.609 17.6781 8.4655 17.5343C8.32183 17.3906 8.25 17.2125 8.25 17V13C8.25 12.7875 8.32192 12.6094 8.46575 12.4658C8.60958 12.3219 8.78775 12.25 9.00025 12.25C9.21292 12.25 9.391 12.3219 9.5345 12.4658C9.67817 12.6094 9.75 12.7875 9.75 13V14.25H17C17.2125 14.25 17.3906 14.3219 17.5343 14.4658C17.6781 14.6096 17.75 14.7878 17.75 15.0003C17.75 15.2129 17.6781 15.391 17.5343 15.5345C17.3906 15.6782 17.2125 15.75 17 15.75H9.75V17C9.75 17.2125 9.67808 17.3906 9.53425 17.5343C9.39042 17.6781 9.21225 17.75 8.99975 17.75ZM1 15.75C0.7875 15.75 0.609417 15.6781 0.46575 15.5343C0.321917 15.3904 0.25 15.2122 0.25 14.9998C0.25 14.7871 0.321917 14.609 0.46575 14.4655C0.609417 14.3218 0.7875 14.25 1 14.25H5C5.2125 14.25 5.39058 14.3219 5.53425 14.4658C5.67808 14.6096 5.75 14.7878 5.75 15.0003C5.75 15.2129 5.67808 15.391 5.53425 15.5345C5.39058 15.6782 5.2125 15.75 5 15.75H1ZM4.99975 11.75C4.78708 11.75 4.609 11.6781 4.4655 11.5343C4.32183 11.3906 4.25 11.2125 4.25 11V9.75H1C0.7875 9.75 0.609417 9.67808 0.46575 9.53425C0.321917 9.39042 0.25 9.21225 0.25 8.99975C0.25 8.78708 0.321917 8.609 0.46575 8.4655C0.609417 8.32183 0.7875 8.25 1 8.25H4.25V7C4.25 6.7875 4.32192 6.60942 4.46575 6.46575C4.60958 6.32192 4.78775 6.25 5.00025 6.25C5.21292 6.25 5.391 6.32192 5.5345 6.46575C5.67817 6.60942 5.75 6.7875 5.75 7V11C5.75 11.2125 5.67808 11.3906 5.53425 11.5343C5.39042 11.6781 5.21225 11.75 4.99975 11.75ZM9 9.75C8.7875 9.75 8.60942 9.67808 8.46575 9.53425C8.32192 9.39042 8.25 9.21225 8.25 8.99975C8.25 8.78708 8.32192 8.609 8.46575 8.4655C8.60942 8.32183 8.7875 8.25 9 8.25H17C17.2125 8.25 17.3906 8.32192 17.5343 8.46575C17.6781 8.60958 17.75 8.78775 17.75 9.00025C17.75 9.21292 17.6781 9.391 17.5343 9.5345C17.3906 9.67817 17.2125 9.75 17 9.75H9ZM12.9998 5.75C12.7871 5.75 12.609 5.67808 12.4655 5.53425C12.3218 5.39058 12.25 5.2125 12.25 5V1C12.25 0.7875 12.3219 0.609417 12.4658 0.46575C12.6096 0.321917 12.7878 0.25 13.0003 0.25C13.2129 0.25 13.391 0.321917 13.5345 0.46575C13.6782 0.609417 13.75 0.7875 13.75 1V2.25H17C17.2125 2.25 17.3906 2.32192 17.5343 2.46575C17.6781 2.60958 17.75 2.78775 17.75 3.00025C17.75 3.21292 17.6781 3.391 17.5343 3.5345C17.3906 3.67817 17.2125 3.75 17 3.75H13.75V5C13.75 5.2125 13.6781 5.39058 13.5343 5.53425C13.3904 5.67808 13.2122 5.75 12.9998 5.75ZM1 3.75C0.7875 3.75 0.609417 3.67808 0.46575 3.53425C0.321917 3.39042 0.25 3.21225 0.25 2.99975C0.25 2.78708 0.321917 2.609 0.46575 2.4655C0.609417 2.32183 0.7875 2.25 1 2.25H9C9.2125 2.25 9.39058 2.32192 9.53425 2.46575C9.67808 2.60958 9.75 2.78775 9.75 3.00025C9.75 3.21292 9.67808 3.391 9.53425 3.5345C9.39058 3.67817 9.2125 3.75 9 3.75H1Z"
                                            fill="#8B95A5" />
                                    </svg>
                                </span>
                            </dx-button>

                            <!-- Variant name Icon -->
                            <div toolTip="Variant" (click)="toggleVariantSidenav()" style="cursor: pointer;">
                                <span matTooltip="Click to display the variants">
                                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M10.75 9.70574L13.827 12.7827C13.9653 12.9212 14.0329 13.0952 14.0297 13.3047C14.0266 13.5144 13.9558 13.6885 13.8173 13.827C13.6788 13.9653 13.5042 14.0345 13.2935 14.0345C13.0828 14.0345 12.9093 13.9653 12.773 13.827L9.52125 10.5652C9.42758 10.4717 9.359 10.3698 9.3155 10.2595C9.27183 10.1492 9.25 10.0351 9.25 9.91724V5.99999C9.25 5.78749 9.32192 5.60941 9.46575 5.46574C9.60958 5.32191 9.78775 5.24999 10.0003 5.24999C10.2129 5.24999 10.391 5.32191 10.5345 5.46574C10.6782 5.60941 10.75 5.78749 10.75 5.99999V9.70574ZM13.1053 3.04799C12.8198 3.04799 12.5786 2.94941 12.3818 2.75224C12.1849 2.55508 12.0865 2.31374 12.0865 2.02824C12.0865 1.74274 12.1851 1.50157 12.3822 1.30474C12.5794 1.10807 12.8208 1.00974 13.1062 1.00974C13.3917 1.00974 13.6329 1.10832 13.8298 1.30549C14.0266 1.50249 14.125 1.74382 14.125 2.02949C14.125 2.31499 14.0264 2.55616 13.8292 2.75299C13.6321 2.94966 13.3907 3.04799 13.1053 3.04799ZM13.0763 18.9902C12.7908 18.9902 12.5497 18.8917 12.353 18.6945C12.1562 18.4975 12.0577 18.2562 12.0577 17.9705C12.0577 17.685 12.1563 17.4438 12.3535 17.247C12.5507 17.0503 12.792 16.952 13.0775 16.952C13.363 16.952 13.6042 17.0506 13.801 17.2477C13.9977 17.4449 14.096 17.6862 14.096 17.9717C14.096 18.2572 13.9975 18.4984 13.8005 18.6952C13.6033 18.8919 13.3619 18.9902 13.0763 18.9902ZM17.0763 6.43274C16.7908 6.43274 16.5497 6.33416 16.353 6.13699C16.1562 5.93982 16.0577 5.69849 16.0577 5.41299C16.0577 5.12749 16.1563 4.88633 16.3535 4.68949C16.5507 4.49266 16.792 4.39424 17.0775 4.39424C17.363 4.39424 17.6042 4.49282 17.801 4.68999C17.9977 4.88716 18.096 5.12849 18.096 5.41399C18.096 5.69949 17.9975 5.94066 17.8005 6.13749C17.6033 6.33432 17.3619 6.43274 17.0763 6.43274ZM17.0475 15.6345C16.762 15.6345 16.5208 15.5359 16.324 15.3387C16.1272 15.1417 16.0288 14.9004 16.0288 14.6147C16.0288 14.3292 16.1273 14.0881 16.3245 13.8912C16.5217 13.6946 16.763 13.5962 17.0485 13.5962C17.3342 13.5962 17.5753 13.6948 17.772 13.892C17.9688 14.089 18.0673 14.3303 18.0673 14.616C18.0673 14.9015 17.9687 15.1427 17.7715 15.3395C17.5743 15.5362 17.333 15.6345 17.0475 15.6345ZM18.4803 11.0192C18.1948 11.0192 17.9536 10.9207 17.7568 10.7235C17.5599 10.5263 17.4615 10.285 17.4615 9.99949C17.4615 9.71399 17.5601 9.47283 17.7572 9.27599C17.9544 9.07916 18.1958 8.98074 18.4813 8.98074C18.7668 8.98074 19.0079 9.07933 19.2048 9.27649C19.4016 9.47366 19.5 9.71499 19.5 10.0005C19.5 10.286 19.4014 10.5272 19.2043 10.724C19.0071 10.9208 18.7658 11.0192 18.4803 11.0192ZM0.5 9.99849C0.5 7.49183 1.34358 5.33691 3.03075 3.53374C4.71792 1.73057 6.7865 0.729574 9.2365 0.530741C9.4455 0.510241 9.625 0.572157 9.775 0.716491C9.925 0.860657 10 1.03941 10 1.25274C10 1.45341 9.93017 1.62816 9.7905 1.77699C9.65067 1.92566 9.4795 2.01024 9.277 2.03074C7.2155 2.21274 5.48725 3.06182 4.09225 4.57799C2.69742 6.09399 2 7.90124 2 9.99974C2 12.1152 2.69742 13.9268 4.09225 15.4345C5.48725 16.9423 7.2155 17.7872 9.277 17.9692C9.4795 17.9897 9.65067 18.075 9.7905 18.225C9.93017 18.3752 10 18.5502 10 18.7502C10 18.9629 9.925 19.141 9.775 19.2845C9.625 19.4282 9.4455 19.4897 9.2365 19.4692C6.77633 19.2704 4.70517 18.2689 3.023 16.4647C1.341 14.6606 0.5 12.5052 0.5 9.99849Z"
                                            fill="#8B95A5" />
                                    </svg>
                                </span>
                            </div>

                            <!-- Download Icon -->
                            <dx-button (onClick)="pivotGrid1.exportToExcel()"
                                style="box-shadow: none !important; background-color: transparent !important;">
                                <span matTooltip="Click to download the report">
                                    <!-- <svg width="16" height="16" viewBox="0 0 16 16" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M8 11.4115C7.8795 11.4115 7.76733 11.3923 7.6635 11.3538C7.55967 11.3154 7.46092 11.2494 7.36725 11.1558L4.25775 8.04625C4.10908 7.89742 4.03567 7.72333 4.0375 7.524C4.0395 7.32467 4.11292 7.14742 4.25775 6.99225C4.41292 6.83725 4.59108 6.75717 4.79225 6.752C4.99358 6.74683 5.17183 6.82183 5.327 6.977L7.25 8.9V1.25C7.25 1.03717 7.32183 0.859 7.4655 0.7155C7.609 0.571833 7.78717 0.5 8 0.5C8.21283 0.5 8.391 0.571833 8.5345 0.7155C8.67817 0.859 8.75 1.03717 8.75 1.25V8.9L10.673 6.977C10.8218 6.82833 10.9984 6.75492 11.2028 6.75675C11.4073 6.75875 11.5871 6.83725 11.7423 6.99225C11.8871 7.14742 11.9621 7.32308 11.9672 7.51925C11.9724 7.71542 11.8974 7.89108 11.7423 8.04625L8.63275 11.1558C8.53908 11.2494 8.44033 11.3154 8.3365 11.3538C8.23267 11.3923 8.1205 11.4115 8 11.4115ZM2.30775 15.5C1.80258 15.5 1.375 15.325 1.025 14.975C0.675 14.625 0.5 14.1974 0.5 13.6923V11.7308C0.5 11.5179 0.571833 11.3398 0.7155 11.1962C0.859 11.0526 1.03717 10.9808 1.25 10.9808C1.46283 10.9808 1.641 11.0526 1.7845 11.1962C1.92817 11.3398 2 11.5179 2 11.7308V13.6923C2 13.7692 2.03208 13.8398 2.09625 13.9038C2.16025 13.9679 2.23075 14 2.30775 14H13.6923C13.7692 14 13.8398 13.9679 13.9038 13.9038C13.9679 13.8398 14 13.7692 14 13.6923V11.7308C14 11.5179 14.0718 11.3398 14.2155 11.1962C14.359 11.0526 14.5372 10.9808 14.75 10.9808C14.9628 10.9808 15.141 11.0526 15.2845 11.1962C15.4282 
                                    11.3398 15.5 11.5179 15.5 11.7308V13.6923C15.5 14.1974 15.325 14.625 14.975 14.975C14.625 15.325 14.1974 15.5 13.6923 15.5H2.30775Z"
                                            fill="#8B95A5" />
                                    </svg> -->
                                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M12.2383 9.59442L5.32019 8.37567V17.3813C5.32011 17.4792 5.33935 17.5761 5.3768 17.6665C5.41426 17.757 5.4692 17.8391 5.53847 17.9083C5.60774 17.9774 5.68998 18.0322 5.78047 18.0695C5.87097 18.1068 5.96793 18.1259 6.06582 18.1257H18.0033C18.1013 18.1261 18.1984 18.1071 18.289 18.0699C18.3797 18.0327 18.4621 17.9779 18.5315 17.9087C18.6009 17.8395 18.6559 17.7573 18.6935 17.6668C18.731 17.5763 18.7503 17.4793 18.7502 17.3813V14.0632L12.2383 9.59442Z"
                                            fill="#185C37" />
                                        <path
                                            d="M12.2382 1.875H6.06569C5.96781 1.87476 5.87085 1.89383 5.78035 1.93114C5.68986 1.96844 5.60762 2.02324 5.53835 2.0924C5.46908 2.16155 5.41414 2.2437 5.37668 2.33413C5.33922 2.42456 5.31999 2.5215 5.32007 2.61938V5.9375L12.2382 10L15.9013 11.2188L18.7501 10V5.9375L12.2382 1.875Z"
                                            fill="#21A366" />
                                        <path d="M5.32007 5.9375H12.2382V10H5.32007V5.9375Z" fill="#107C41" />
                                        <path opacity="0.1"
                                            d="M10.2714 5.12531H5.32019V15.2816H10.2714C10.4687 15.2806 10.6577 15.2019 10.7974 15.0626C10.9371 14.9232 11.0162 14.7345 11.0177 14.5372V5.86968C11.0162 5.67239 10.9371 5.48363 10.7974 5.3443C10.6577 5.20497 10.4687 5.12629 10.2714 5.12531Z"
                                            fill="black" />
                                        <path opacity="0.2"
                                            d="M9.86456 5.53058H5.32019V15.6868H9.86456C10.0619 15.6858 10.2508 15.6072 10.3905 15.4678C10.5302 15.3285 10.6093 15.1397 10.6108 14.9425V6.27495C10.6093 6.07767 10.5302 5.8889 10.3905 5.74957C10.2508 5.61024 10.0619 5.53156 9.86456 5.53058Z"
                                            fill="black" />
                                        <path opacity="0.2"
                                            d="M9.86456 5.53058H5.32019V14.8743H9.86456C10.0619 14.8733 10.2508 14.7947 10.3905 14.6553C10.5302 14.516 10.6093 14.3272 10.6108 14.13V6.27495C10.6093 6.07767 10.5302 5.8889 10.3905 5.74957C10.2508 5.61024 10.0619 5.53156 9.86456 5.53058Z"
                                            fill="black" />
                                        <path opacity="0.2"
                                            d="M9.45769 5.53058H5.32019V14.8743H9.45769C9.65498 14.8733 9.84394 14.7947 9.98362 14.6553C10.1233 14.516 10.2025 14.3272 10.2039 14.13V6.27495C10.2025 6.07767 10.1233 5.8889 9.98362 5.74957C9.84394 5.61024 9.65498 5.53156 9.45769 5.53058Z"
                                            fill="black" />
                                        <path
                                            d="M1.99625 5.53058H9.4575C9.65514 5.53042 9.84477 5.60873 9.9847 5.74831C10.1246 5.88789 10.2034 6.07731 10.2038 6.27496V13.7237C10.2034 13.9214 10.1246 14.1108 9.9847 14.2504C9.84477 14.3899 9.65514 14.4682 9.4575 14.4681H1.99625C1.89832 14.4684 1.80128 14.4494 1.71072 14.4121C1.62015 14.3749 1.53784 14.3201 1.4685 14.2509C1.39916 14.1817 1.34417 14.0996 1.30667 14.0091C1.26918 13.9186 1.24992 13.8216 1.25 13.7237V6.27496C1.24992 6.17702 1.26918 6.08004 1.30667 5.98957C1.34417 5.89909 1.39916 5.81692 1.4685 5.74776C1.53784 5.67859 1.62015 5.62381 1.71072 5.58654C1.80128 5.54927 1.89832 5.53025 1.99625 5.53058Z"
                                            fill="url(#paint0_linear_1215_70532)" />
                                        <path
                                            d="M3.56287 12.421L5.13224 9.99352L3.69474 7.57977H4.84912L5.63349 9.1254C5.70599 9.27165 5.75849 9.3804 5.78224 9.4529H5.79287C5.84412 9.33581 5.89828 9.22206 5.95537 9.11165L6.79412 7.58227H7.85662L6.38224 9.98227L7.89412 12.4229H6.76349L5.85724 10.7285C5.81528 10.6556 5.77953 10.5793 5.75037 10.5004H5.73537C5.70888 10.5773 5.67365 10.6509 5.63037 10.7198L4.69724 12.421H3.56287Z"
                                            fill="white" />
                                        <path
                                            d="M18.0035 1.875H12.2379V5.9375H18.7498V2.61938C18.7499 2.52145 18.7306 2.42446 18.6931 2.33399C18.6556 2.24352 18.6006 2.16134 18.5313 2.09218C18.462 2.02302 18.3796 1.96823 18.2891 1.93096C18.1985 1.89369 18.1015 1.87467 18.0035 1.875Z"
                                            fill="#33C481" />
                                        <path d="M12.2379 10H18.7498V14.0625H12.2379V10Z" fill="#107C41" />
                                        <defs>
                                            <linearGradient id="paint0_linear_1215_70532" x1="2.80875" y1="4.94558"
                                                x2="8.645" y2="15.0531" gradientUnits="userSpaceOnUse">
                                                <stop stop-color="#18884F" />
                                                <stop offset="0.5" stop-color="#117E43" />
                                                <stop offset="1" stop-color="#0B6631" />
                                            </linearGradient>
                                        </defs>
                                    </svg>
                                </span>
                            </dx-button>

                            <!-- Save a variant Icon -->
                            <button data-step="9" data-intro="Click to Save as a new version" data-position="right"
                                mat-icon-button disableRipple="true" disableFocus="true" matTooltip="Click to Save a New Variant"
                                class="iconbtn-backgrnd" [satPopoverAnchor]="saveAs"  (click)="saveAs.toggle()">
                                <span class="cancel-btn">Save As</span>
                            </button>


                            <!-- Access Report -->
                            <div [class.opened]="isSidenavOpened" class="toggle-button-container">
                                <button mat-icon-button disableRipple="true" (click)="toggleSidenav()"
                                    matTooltip="Click to View the Report List" class="toggle-button">
                                    <mat-icon>chevron_left</mat-icon>
                                    <span class="variant-text">Access Reports</span>
                                </button>
                            </div>
                        </div>

                        <!-- KEBS logo content -->
                        <!-- <div class = "kebs-image">
                            <svg width="324" height="63" viewBox="0 0 324 63" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <g opacity="0.9">
                                <g opacity="0.5">
                                <path d="M138.226 -1.19724L138.226 -1.19719C141.664 -0.57 144.896 -3.02045 144.896 -6.3228V-24.1425L158.562 -36.8367V-4.45866C158.562 -0.199142 156.763 3.9035 153.563 6.98404C149.433 10.9287 143.435 12.6619 137.608 11.5794L137.607 11.5792L111.535 6.84336L123.12 -3.97073L138.226 -1.19724Z" fill="#F6F6F6" fill-opacity="0.5" stroke="#DADCE2" stroke-width="0.5"/>
                                <path d="M212.464 6.84366L186.426 11.5792L186.425 11.5794C180.605 12.6619 174.615 10.9288 170.489 6.98413L170.487 6.98177C167.235 3.95735 165.438 -0.143848 165.438 -4.40383V-36.8363L179.087 -24.1426V-6.59694C179.087 -3.13089 182.492 -0.568977 186.045 -1.25234C186.045 -1.25238 186.046 -1.25242 186.046 -1.25246L200.836 -3.97077L212.464 6.84366Z" stroke="#DADCE2" stroke-width="0.5"/>
                                <path d="M158.562 34.5623V66.9592L144.896 54.279V36.041C144.896 32.9601 141.899 30.6788 138.641 31.2491L138.639 31.2494L123.12 34.0748L111.535 23.273L137.607 18.5424L137.608 18.5423C143.435 17.461 149.433 19.1921 153.563 23.1321C156.763 26.2092 158.562 30.3075 158.562 34.5623Z" stroke="#DADCE2" stroke-width="0.5"/>
                                <path d="M186.392 18.5426L186.393 18.5428L212.464 23.2784L200.88 34.0381L185.066 31.1549C185.066 31.1548 185.066 31.1548 185.066 31.1548C181.982 30.5821 179.104 32.7589 179.104 35.7321V54.2645L165.438 66.9587V34.5807C165.438 30.321 167.237 26.2183 170.437 23.1377C174.567 19.1932 180.565 17.4602 186.392 18.5426Z" fill="#F6F6F6" fill-opacity="0.5" stroke="#DADCE2" stroke-width="0.5"/>
                                </g>
                                <g opacity="0.5">
                                <path d="M27.2257 43.0762L27.226 43.0762C30.6635 43.7034 33.8958 41.253 33.8958 37.9506V20.1309L47.5625 7.4367V39.8148C47.5625 44.0743 45.7632 48.1769 42.5634 51.2575C38.4329 55.2022 32.4352 56.9353 26.6082 55.8528L26.6072 55.8527L0.534576 51.1168L12.1203 40.3027L27.2257 43.0762Z" fill="#F6F6F6" fill-opacity="0.5" stroke="#DADCE2" stroke-width="0.5"/>
                                <path d="M101.464 51.1171L75.4255 55.8527L75.4245 55.8528C69.6049 56.9353 63.6147 55.2022 59.4894 51.2576L59.4868 51.2552C56.2348 48.2308 54.4375 44.1296 54.4375 39.8696V7.43718L68.0867 20.1309V37.6765C68.0867 41.1426 71.4917 43.7045 75.0452 43.0211C75.0454 43.0211 75.0456 43.021 75.0459 43.021L89.8359 40.3027L101.464 51.1171Z" stroke="#DADCE2" stroke-width="0.5"/>
                                <path d="M47.5625 78.8358V111.233L33.8958 98.5524V80.3145C33.8958 77.2336 30.8989 74.9522 27.6409 75.5225L27.6392 75.5228L12.1204 78.3482L0.535001 67.5464L26.6071 62.8159L26.6081 62.8157C32.435 61.7344 38.4325 63.4655 42.563 67.4055C45.7631 70.4827 47.5625 74.5809 47.5625 78.8358Z" fill="#F7F8FA" stroke="#DADCE2" stroke-width="0.5"/>
                                <path d="M75.3918 62.816L75.3928 62.8162L101.464 67.5518L89.88 78.3116L74.0665 75.4283C74.0663 75.4283 74.0662 75.4282 74.0661 75.4282C70.9821 74.8556 68.1042 77.0324 68.1042 80.0055V98.5379L54.4375 111.232V78.8541C54.4375 74.5945 56.2369 70.4917 59.4369 67.4111C63.5674 63.4666 69.565 61.7336 75.3918 62.816Z" stroke="#DADCE2" stroke-width="0.5"/>
                                </g>
                                <g opacity="0.5">
                                <path d="M249.226 43.0762L249.226 43.0762C252.664 43.7034 255.896 41.253 255.896 37.9506V20.1309L269.562 7.4367V39.8148C269.562 44.0743 267.763 48.1769 264.563 51.2575C260.433 55.2022 254.435 56.9353 248.608 55.8528L248.607 55.8527L222.535 51.1168L234.12 40.3027L249.226 43.0762Z" fill="#F6F6F6" fill-opacity="0.5" stroke="#DADCE2" stroke-width="0.5"/>
                                <path d="M323.464 51.1171L297.426 55.8527L297.425 55.8528C291.605 56.9353 285.615 55.2022 281.489 51.2576L281.487 51.2552C278.235 48.2308 276.438 44.1296 276.438 39.8696V7.43718L290.087 20.1309V37.6765C290.087 41.1426 293.492 43.7045 297.045 43.0211C297.045 43.0211 297.046 43.021 297.046 43.021L311.836 40.3027L323.464 51.1171Z" stroke="#DADCE2" stroke-width="0.5"/>
                                <path d="M269.562 78.8358V111.233L255.896 98.5524V80.3145C255.896 77.2336 252.899 74.9522 249.641 75.5225L249.639 75.5228L234.12 78.3482L222.535 67.5464L248.607 62.8159L248.608 62.8157C254.435 61.7344 260.433 63.4655 264.563 67.4055C267.763 70.4827 269.562 74.5809 269.562 78.8358Z" stroke="#DADCE2" stroke-width="0.5"/>
                                <path d="M297.392 62.816L297.393 62.8162L323.464 67.5518L311.88 78.3116L296.066 75.4283C296.066 75.4283 296.066 75.4282 296.066 75.4282C292.982 74.8556 290.104 77.0324 290.104 80.0055V98.5379L276.438 111.232V78.8541C276.438 74.5945 278.237 70.4917 281.437 67.4111C285.567 63.4666 291.565 61.7336 297.392 62.816Z" stroke="#DADCE2" stroke-width="0.5"/>
                                </g>
                                </g>
                                </svg>
                                
                                
                        </div>  -->
                    </div>

                    <div class="content-body-class">
                        <div *ngIf="this.totalCount != 0">
                            <dx-pivot-grid id="costing" [allowSortingBySummary]="true" [allowSorting]="true" [allowFiltering]="true"
                                [allowExpandAll]="true" [showBorders]="true" [dataSource]="dataSource" [wordWrapEnabled]="false"
                                [height]="580" [showColumnTotals]="showSubTotal" [showColumnGrandTotals]="showColumnGrandTotal"
                                [showRowGrandTotals]="showRowGrandTotal" [showRowTotals]="showSubTotal"
                                (onInitialized)="onInitialized($event)">
                    
                                <dxo-export [enabled]="false" [fileName]="downloadFileName"></dxo-export>
                                <dxo-field-chooser [enabled]="false" [allowSearch]="true"></dxo-field-chooser>
                                <dxo-field-panel [showDataFields]="showDataFields" [showRowFields]="showRowFields"
                                    [showColumnFields]="showColumnFields" [showFilterFields]="false" [allowFieldDragging]="true"
                                    [visible]="true"> </dxo-field-panel>
                                <dxo-header-filter [allowSearch]="true" [width]="300" [height]="400"></dxo-header-filter>
                                <dxo-state-storing [enabled]="false" type="localStorage" storageKey="storageKey">
                                </dxo-state-storing>
                            </dx-pivot-grid>
                        </div>
                        <div *ngIf="noDataAvailable">
                            <span class="row justify-content-center">
                                <svg width="389" height="391" viewBox="0 0 389 391" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path opacity="0.5"
                                        d="M151.561 242.823C151.561 242.823 191.124 261.488 206.819 268.651C219.251 274.325 283.265 292.475 290.891 287.316C298.518 282.157 315.355 267.996 309.797 258.295C304.238 248.594 275.004 231.175 261.022 228.629C247.041 226.084 207.063 237.654 207.063 237.654L172.963 243.53L151.561 242.823Z"
                                        fill="url(#paint0_linear_26524_94321)" />
                                    <path d="M180.884 159.368L136.797 191.893L148.552 164.222L192.704 136.099L180.884 159.368Z"
                                        fill="#E2E7EC" />
                                    <path d="M147.822 164.793L192.706 136.602V213.488L151.562 242.431L147.822 164.793Z" fill="#B9C0CA" />
                                    <path d="M192.704 136.602L261.527 152.756V229.671L192.704 213.517V136.602Z" fill="#E9EFF1" />
                                    <path d="M147.822 164.789L218.141 181.277V258.192L151.562 242.853L147.822 164.789Z" fill="#E8E9EE" />
                                    <path d="M216.642 181.307L261.526 153.115V230.002L216.642 258.194V181.307Z" fill="#B9C0CA" />
                                    <mask id="mask0_26524_94321" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="216" y="153"
                                        width="46" height="106">
                                        <path d="M216.642 181.307L261.526 153.115V230.002L216.642 258.194V181.307Z" fill="white" />
                                    </mask>
                                    <g mask="url(#mask0_26524_94321)">
                                        <rect x="233.314" y="146.709" width="14.1066" height="117.893" fill="#C7D0D9" />
                                    </g>
                                    <path d="M236.826 204.153L280.977 176.031L260.793 153.115L216.642 181.238L236.826 204.153Z"
                                        fill="#E2E7EC" />
                                    <path opacity="0.5" d="M113.391 244.761L182.831 280.36L189.314 261.496L121.182 238.757L113.391 244.761Z"
                                        fill="url(#paint1_linear_26524_94321)" />
                                    <path
                                        d="M111.494 186.241L135.438 188.135L130.121 240.392C130.121 240.392 129.655 240.914 128.526 240.711C127.396 240.508 126.211 239.878 126.211 239.878L122.65 198.335C122.65 198.335 120.59 213.024 120.734 217.157C120.842 220.271 121.903 224.069 122.471 227.318C122.96 230.115 123.988 236.797 123.988 236.797C123.988 236.797 122.773 237.354 122.127 237.48C121.481 237.605 120.652 236.914 120.652 236.914C120.652 236.914 112.257 220.563 112.149 217.458C112.041 214.353 111.494 186.241 111.494 186.241Z"
                                        fill="url(#paint2_linear_26524_94321)" />
                                    <path
                                        d="M120.282 236.837C120.282 236.837 119.215 238.764 117.89 239.939C116.565 241.113 113.951 242.516 113.355 243.153C112.759 243.791 113.41 244.73 113.41 244.73L119.817 244.505L124.937 240.16L124.222 236.018L120.282 236.837Z"
                                        fill="#515965" />
                                    <path
                                        d="M130.724 239.068L132.22 243.231C132.22 243.231 129.763 245.14 126.369 245.671C122.975 246.202 120.636 245.386 120.636 245.386C120.636 245.386 120.024 244.147 121.179 243.618C121.709 243.375 122.898 242.364 123.942 241.069C124.413 240.485 126.188 239.227 126.188 239.227L130.724 239.068Z"
                                        fill="#515965" />
                                    <path
                                        d="M141.449 158.997C141.449 158.997 143.412 161.993 144.615 161.191C145.819 160.39 153.331 152.664 153.331 152.664C153.331 152.664 155.566 150.938 155.979 151.348C156.393 151.759 155.234 152.664 155.234 152.664C155.234 152.664 157.673 152.904 158.434 152.904C159.196 152.904 160.799 154.125 159.751 154.536C158.703 154.947 154.638 155.048 154.638 155.048C154.638 155.048 148.466 166.758 145.25 167.589C142.034 168.419 137.603 163.723 137.603 163.723L141.449 158.997Z"
                                        fill="#FACAC4" />
                                    <path
                                        d="M110.092 159.21C110.092 159.21 108.805 161.853 107.639 161.219C106.473 160.585 99.9303 153.662 99.9303 153.662C99.9303 153.662 97.7514 152.323 97.2186 152.564C96.6858 152.805 97.9994 153.662 97.9994 153.662C97.9994 153.662 94.7937 153.923 94.1907 154.166C93.5877 154.409 92.8853 155.155 93.7971 155.494C94.7088 155.833 98.199 156.074 98.199 156.074C98.199 156.074 103.051 165.694 106.727 166.819C110.403 167.944 113.535 163.512 113.535 163.512L110.092 159.21Z"
                                        fill="#FACAC4" />
                                    <path
                                        d="M121.994 151.115V146.389C121.994 146.389 119.552 146.513 118.559 144.872C117.566 143.231 117.298 138.574 117.298 138.574L118.046 136.415L125.402 136.104L129.267 139.408L127.468 142.597L127.125 143.209L128.84 150.456L121.994 151.115Z"
                                        fill="#FACAC4" />
                                    <mask id="mask1_26524_94321" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="117" y="136"
                                        width="13" height="16">
                                        <path
                                            d="M121.994 151.115V146.389C121.994 146.389 119.552 146.513 118.559 144.872C117.566 143.231 117.298 138.574 117.298 138.574L118.046 136.415L125.402 136.104L129.267 139.408L127.468 142.597L127.125 143.209L128.84 150.456L121.994 151.115Z"
                                            fill="white" />
                                    </mask>
                                    <g mask="url(#mask1_26524_94321)">
                                        <path
                                            d="M126.779 141.898C124.605 144.611 120.845 146.506 120.356 146.506C119.866 146.506 123.09 149.909 123.09 149.909L125.712 151.115L129.435 150.323L130.764 146.506L128.418 142.176C128.418 142.176 128.953 139.185 126.779 141.898Z"
                                            fill="url(#paint3_linear_26524_94321)" />
                                    </g>
                                    <path
                                        d="M108.531 161.108L112.146 165.993L114.451 163.551C114.451 163.551 112.698 173.197 112.146 177.823C111.594 182.45 111.491 187.058 111.491 187.058C111.491 187.058 117.28 189.214 121.595 189.577C125.91 189.94 135.956 189.005 135.956 189.005L134.659 161.852L138.421 165.356L142.839 160.238C142.839 160.238 135.234 148.513 133.796 147.83C132.358 147.147 128.36 147.83 128.36 147.83C128.36 147.83 125.523 151.198 124.373 151.245C123.223 151.292 120.671 147.83 120.671 147.83C120.671 147.83 116.594 147.177 115.409 148.513C114.223 149.849 108.531 161.108 108.531 161.108Z"
                                        fill="url(#paint4_linear_26524_94321)" />
                                    <path
                                        d="M117.298 138.627C117.298 138.627 118.165 136.432 119.296 136.814C120.367 137.176 121.295 138.038 122.627 138.442C123.96 138.846 124.107 137.03 125.012 137.337C125.918 137.643 125.32 140.315 125.958 140.812C126.597 141.31 126.597 137.337 127.957 138.406C129.318 139.476 126.538 142.487 126.538 142.487C126.538 142.487 126.894 144.337 127.008 144.463C127.123 144.589 127.11 143.117 127.11 143.117C127.11 143.117 128.409 142.616 128.963 140.359C129.516 138.102 129.756 137.204 128.963 135.564C128.169 133.924 127.943 132.64 125.958 131.866C123.974 131.092 122.468 131.395 121.08 131.866C119.693 132.337 118.688 133.273 118.046 134.204C116.55 136.415 117.298 138.627 117.298 138.627Z"
                                        fill="#515965" />
                                    <path fill-rule="evenodd" clip-rule="evenodd"
                                        d="M251.058 80.7384C249.482 81.2219 216.843 100.815 216.843 100.815L226.368 101.528L233.136 109.033C233.136 109.033 252.634 80.2549 251.058 80.7384Z"
                                        fill="#E8E9EE" />
                                    <mask id="mask2_26524_94321" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="216" y="80"
                                        width="36" height="30">
                                        <path fill-rule="evenodd" clip-rule="evenodd"
                                            d="M251.058 80.7384C249.482 81.2219 216.843 100.815 216.843 100.815L226.368 101.528L233.136 109.033C233.136 109.033 252.634 80.2549 251.058 80.7384Z"
                                            fill="white" />
                                    </mask>
                                    <g mask="url(#mask2_26524_94321)">
                                        <path fill-rule="evenodd" clip-rule="evenodd"
                                            d="M226.367 101.529L251.057 80.74L233.135 109.034L226.367 101.529Z" fill="#B9C0CA" />
                                    </g>
                                    <path fill-rule="evenodd" clip-rule="evenodd"
                                        d="M246.154 103.443L234.709 102.243L251.068 80.9416L246.154 103.443Z" fill="#E8E9EE" />
                                    <path fill-rule="evenodd" clip-rule="evenodd"
                                        d="M225.078 117.635C225.793 116.116 226.513 114.545 227.238 112.92L228.422 113.451C227.693 115.084 226.969 116.664 226.251 118.191L225.078 117.635ZM220.423 126.898C221.213 125.435 222.01 123.903 222.813 122.301L223.971 122.886C223.162 124.499 222.359 126.043 221.563 127.518L220.423 126.898ZM217.881 131.404C216.96 132.962 216.048 134.423 215.145 135.785L216.225 136.505C217.14 135.124 218.064 133.645 218.997 132.067L217.881 131.404ZM208.821 143.891C209.914 142.752 211.024 141.454 212.15 139.994L213.176 140.79C212.022 142.286 210.882 143.62 209.754 144.794L208.821 143.891ZM205.012 147.252C203.496 148.342 202.02 149.096 200.585 149.517L200.949 150.766C202.532 150.301 204.138 149.481 205.767 148.31L205.012 147.252ZM191.44 147.39C192.866 148.543 194.315 149.299 195.791 149.663L195.481 150.927C193.812 150.514 192.194 149.671 190.626 148.402L191.44 147.39ZM187.945 143.694C187.865 143.588 187.784 143.482 187.704 143.373C186.661 141.964 185.902 140.626 185.419 139.362L184.208 139.829C184.736 141.209 185.553 142.649 186.663 144.149C186.747 144.262 186.831 144.374 186.915 144.484L187.945 143.694ZM187.634 130.607C186.234 131.767 185.333 133.1 184.966 134.586L183.707 134.273C184.145 132.499 185.205 130.933 186.809 129.603L187.634 130.607ZM191.967 128.163C193.426 127.591 195.067 127.125 196.846 126.773L196.595 125.497C194.743 125.863 193.029 126.35 191.495 126.951L191.967 128.163ZM206.997 126.029C205.272 125.967 203.553 125.996 201.879 126.114L201.788 124.816C203.508 124.695 205.273 124.666 207.043 124.729L206.997 126.029ZM212.157 126.485C213.895 126.733 215.576 127.073 217.164 127.5L217.499 126.243C215.86 125.803 214.128 125.452 212.339 125.197L212.157 126.485ZM226.285 131.786C225.099 130.818 223.652 129.949 221.982 129.194L222.515 128.008C224.283 128.807 225.825 129.734 227.103 130.777L226.285 131.786ZM229.377 135.694C229.69 136.424 229.89 137.183 229.974 137.974C230.063 138.809 230.094 139.65 230.067 140.494L231.363 140.537C231.392 139.632 231.359 138.731 231.263 137.835C231.165 136.915 230.932 136.028 230.568 135.18L229.377 135.694ZM227.297 150.187C228.132 148.608 228.778 147.045 229.236 145.499L230.479 145.87C229.996 147.501 229.317 149.143 228.443 150.797L227.297 150.187ZM224.553 154.538C223.594 155.848 222.509 157.17 221.299 158.501L222.258 159.378C223.497 158.013 224.611 156.656 225.598 155.308L224.553 154.538ZM213.856 165.539C215.232 164.396 216.521 163.26 217.722 162.13L218.609 163.079C217.388 164.228 216.079 165.381 214.683 166.541L213.856 165.539ZM209.771 168.761C208.438 169.762 207.039 170.767 205.576 171.778L206.311 172.85C207.788 171.829 209.201 170.814 210.547 169.803L209.771 168.761ZM198.526 176.37C199.447 175.801 200.349 175.234 201.229 174.668L201.929 175.763C201.041 176.333 200.134 176.905 199.206 177.478L198.526 176.37Z"
                                        fill="#6E7B8F" />
                                    <defs>
                                        <linearGradient id="paint0_linear_26524_94321" x1="145.748" y1="249.295" x2="157.329" y2="301.06"
                                            gradientUnits="userSpaceOnUse">
                                            <stop stop-color="#C4CDD1" />
                                            <stop offset="1" stop-color="#C6CFD3" stop-opacity="0.01" />
                                        </linearGradient>
                                        <linearGradient id="paint1_linear_26524_94321" x1="110.981" y1="254.507" x2="124.409" y2="289.435"
                                            gradientUnits="userSpaceOnUse">
                                            <stop stop-color="#C4CDD1" />
                                            <stop offset="1" stop-color="#C6CFD3" stop-opacity="0.01" />
                                        </linearGradient>
                                        <linearGradient id="paint2_linear_26524_94321" x1="137.169" y1="240.487" x2="135.254" y2="185.822"
                                            gradientUnits="userSpaceOnUse">
                                            <stop stop-color="#A1A7B8" />
                                            <stop offset="1" stop-color="#7B8195" />
                                        </linearGradient>
                                        <linearGradient id="paint3_linear_26524_94321" x1="127.81" y1="147.942" x2="127.81" y2="143.362"
                                            gradientUnits="userSpaceOnUse">
                                            <stop stop-color="#FBD8D6" />
                                            <stop offset="1" stop-color="#EAC0BE" stop-opacity="0.7" />
                                        </linearGradient>
                                        <linearGradient id="paint4_linear_26524_94321" x1="91.1749" y1="166.752" x2="127.371" y2="204.809"
                                            gradientUnits="userSpaceOnUse">
                                            <stop stop-color="#E5E9F0" />
                                            <stop offset="1" stop-color="#D2DAE8" />
                                        </linearGradient>
                                    </defs>
                                </svg>
                    
                            </span>
                            <div class="message">Data Not Available</div>
                        </div>
                        <div class="dialog dialog-info" *ngIf="isDialogOpen" (click)="closeMessage()">
                            <span class="msg-text">❗ Unable to Fetch Data</span>
                        </div>
                        <div class="contents" *ngIf="!isAuthorized">
                            <svg width="273" height="273" viewBox="0 0 273 273" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="134.549" cy="130.655" r="85.4007" fill="#F6F6F7" />
                                <circle cx="54.5007" cy="72.9914" r="5.35275" fill="#F6F6F7" />
                                <circle cx="192.942" cy="51.8232" r="9.97558" fill="#F6F6F7" />
                                <circle cx="56.2038" cy="185.641" r="9.48897" fill="#F6F6F7" />
                                <circle cx="226.032" cy="176.395" r="9.48897" fill="#F6F6F7" />
                                <circle cx="207.541" cy="187.101" r="4.62283" fill="#F6F6F7" />
                                <path
                                    d="M148.801 58.6538C148.519 58.5938 148.519 58.1912 148.801 58.1312L153.067 57.2241C153.17 57.2022 153.25 57.1221 153.272 57.0194L154.191 52.784C154.252 52.5032 154.652 52.5032 154.713 52.784L155.631 57.0194C155.654 57.1221 155.734 57.2022 155.837 57.2241L160.102 58.1312C160.385 58.1912 160.385 58.5938 160.102 58.6538L155.837 59.5609C155.734 59.5827 155.654 59.6629 155.631 59.7656L154.713 64.001C154.652 64.2817 154.252 64.2817 154.191 64.001L153.272 59.7656C153.25 59.6629 153.17 59.5827 153.067 59.5609L148.801 58.6538Z"
                                    fill="#DADCE2" />
                                <path
                                    d="M44.8768 134.12C44.9857 133.606 45.72 133.606 45.8289 134.12L47.4556 141.805C47.4952 141.992 47.641 142.139 47.8281 142.18L55.4336 143.837C55.9443 143.948 55.9443 144.676 55.4336 144.788L47.8281 146.444C47.641 146.485 47.4952 146.632 47.4556 146.819L45.8289 154.504C45.72 155.018 44.9857 155.018 44.8768 154.504L43.2501 146.819C43.2105 146.632 43.0647 146.485 42.8777 146.444L35.2721 144.788C34.7614 144.676 34.7614 143.948 35.2721 143.837L42.8777 142.18C43.0647 142.139 43.2105 141.992 43.2501 141.805L44.8768 134.12Z"
                                    fill="#DADCE2" />
                                <path
                                    d="M210.871 75.2393C210.98 74.7249 211.715 74.7249 211.823 75.2393L212.772 79.7225C212.812 79.9098 212.958 80.0565 213.145 80.0972L217.594 81.0666C218.105 81.1778 218.105 81.9062 217.594 82.0175L213.145 82.9868C212.958 83.0276 212.812 83.1742 212.772 83.3615L211.823 87.8447C211.715 88.3592 210.98 88.3592 210.871 87.8447L209.922 83.3615C209.883 83.1742 209.737 83.0276 209.55 82.9868L205.1 82.0175C204.59 81.9062 204.59 81.1778 205.1 81.0666L209.55 80.0972C209.737 80.0565 209.883 79.9098 209.922 79.7225L210.871 75.2393Z"
                                    fill="#DADCE2" />
                                <path
                                    d="M230.488 147.117L231.979 154.157L238.918 155.669L231.979 157.181L230.488 164.221L228.998 157.181L222.059 155.669L228.998 154.157L230.488 147.117Z"
                                    fill="white" />
                                <path
                                    d="M96.1222 95.8017L96.1471 95.8143L96.1726 95.8254C99.0722 97.0871 101.159 98.1795 102.597 99.1409C104.059 100.118 104.736 100.887 104.987 101.455C105.191 101.919 105.145 102.319 104.798 102.819C104.407 103.383 103.673 103.997 102.593 104.696C101.558 105.366 100.305 106.053 98.9178 106.814L98.8023 106.877C97.3823 107.656 95.8419 108.504 94.3418 109.453C91.3589 111.339 88.4083 113.7 86.792 116.901C85.1623 120.128 85.1885 123.863 85.6643 127.318C85.9037 129.055 86.263 130.759 86.6064 132.328C86.6505 132.53 86.6942 132.729 86.7373 132.925C87.0338 134.273 87.3028 135.496 87.4706 136.581C87.6659 137.843 87.6992 138.78 87.5457 139.419C87.4729 139.722 87.3673 139.916 87.2524 140.044C87.1431 140.166 86.9838 140.276 86.7178 140.349C86.1358 140.507 85.1111 140.466 83.3942 139.95C81.7212 139.448 79.5193 138.535 76.6499 137.116C68.8505 131.654 63.4635 126.085 60.7375 120.511C58.0294 114.973 57.9251 109.396 60.7705 103.761C66.4331 92.5473 82.1748 88.7588 96.1222 95.8017Z"
                                    fill="url(#paint0_linear_12232_225842)" stroke="#B9C0CA" stroke-width="1.94645" />
                                <path d="M28.577 91.9891L29.1588 91.941C40.7923 90.9784 52.4356 93.6772 62.4594 99.6596V99.6596"
                                    stroke="#B9C0CA" stroke-width="1.94645" />
                                <path d="M25.0381 98.6302L26.0083 98.5467C37.5072 97.5578 49.0299 100.159 58.989 105.991V105.991"
                                    stroke="#B9C0CA" stroke-width="1.94645" />
                                <path
                                    d="M118.073 132.697C114.734 139.308 110.961 144.667 106.048 147.366C101.223 150.017 95.0898 150.205 86.7322 145.985C76.0185 140.575 72.2819 125.824 78.8204 112.875C85.3546 99.9356 99.4287 94.1849 110.14 99.5762C118.786 104.508 122.262 109.564 122.93 114.83C123.611 120.197 121.427 126.054 118.073 132.697Z"
                                    fill="#B9C0CA" stroke="#B9C0CA" stroke-width="1.94645" />
                                <ellipse cx="103.323" cy="125.183" rx="18.6071" ry="25.606" transform="rotate(-155.936 103.323 125.183)"
                                    fill="white" />
                                <path
                                    d="M120.331 133.835C117.059 140.313 112.165 145.16 106.938 147.78C101.711 150.401 96.2162 150.772 91.6222 148.452C87.0283 146.133 84.0657 141.49 83.0714 135.728C82.0771 129.966 83.0715 123.151 86.3429 116.672C89.6143 110.194 94.5088 105.348 99.7358 102.727C104.963 100.107 110.457 99.735 115.051 102.055C119.645 104.375 122.608 109.017 123.602 114.779C124.596 120.541 123.602 127.357 120.331 133.835Z"
                                    fill="white" stroke="#B9C0CA" stroke-width="1.94645" />
                                <path
                                    d="M106.124 117.456C107.127 115.472 109.321 114.733 110.949 115.556C112.578 116.378 113.286 118.582 112.284 120.567C111.282 122.551 109.087 123.29 107.459 122.467C105.831 121.645 105.122 119.441 106.124 117.456Z"
                                    fill="#B9C0CA" stroke="#B9C0CA" stroke-width="1.94645" />
                                <path
                                    d="M98.9081 131.745C99.9103 129.761 102.105 129.022 103.733 129.845C105.361 130.667 106.07 132.871 105.068 134.856C104.066 136.841 101.871 137.579 100.243 136.756C98.6145 135.934 97.9058 133.73 98.9081 131.745Z"
                                    fill="#B9C0CA" stroke="#B9C0CA" stroke-width="1.94645" />
                                <path
                                    d="M149.261 126.805L149.293 126.789L149.324 126.77C167.237 116.101 188.642 119.637 197.289 134.155C205.936 148.672 198.852 169.178 180.939 179.848L180.914 179.862L180.891 179.878C178.344 181.603 176.332 182.696 174.742 183.287C173.143 183.882 172.074 183.931 171.349 183.718C170.664 183.517 170.127 183.034 169.682 182.165C169.225 181.27 168.906 180.036 168.664 178.484C168.423 176.94 168.269 175.165 168.102 173.214L168.094 173.11C167.931 171.199 167.754 169.136 167.471 167.033C166.893 162.755 165.861 158.221 163.51 154.275C161.161 150.331 157.67 147.237 154.187 144.66C152.48 143.396 150.757 142.244 149.16 141.176L149.058 141.108C147.423 140.015 145.936 139.017 144.694 138.057C143.444 137.091 142.51 136.213 141.94 135.378C141.384 134.566 141.218 133.865 141.366 133.18C141.522 132.456 142.068 131.559 143.348 130.465C144.621 129.378 146.536 128.169 149.261 126.805Z"
                                    fill="url(#paint1_linear_12232_225842)" stroke="#B9C0CA" stroke-width="1.94645" />
                                <path d="M233.109 106.638L232.749 107.323C225.549 121.038 214.242 132.158 200.409 139.128V139.128"
                                    stroke="#B9C0CA" stroke-width="1.94645" />
                                <path d="M228.188 97.9461L227.591 99.092C220.516 112.674 209.404 123.729 195.786 130.733V130.733"
                                    stroke="#B9C0CA" stroke-width="1.94645" />
                                <path d="M123.605 138.161L128.888 135.014L133.21 132.439" stroke="#B9C0CA" stroke-width="1.94645" />
                                <path
                                    d="M131.007 133.119L131.049 133.1L131.088 133.076C144.961 124.813 164.322 131.24 174.236 147.884C184.148 164.524 180.583 184.603 166.719 192.87C155.18 199.322 146.937 199.655 140.395 196.538C133.753 193.374 128.613 186.552 123.56 178.069C118.533 169.628 115.042 160.899 115.467 153.058C115.677 149.16 116.855 145.477 119.307 142.13C121.765 138.776 125.537 135.712 131.007 133.119Z"
                                    fill="#B9C0CA" stroke="#B9C0CA" stroke-width="1.94645" />
                                <path
                                    d="M120.679 179.787C115.719 171.46 113.755 162.462 114.547 154.669C115.339 146.876 118.872 140.354 124.845 136.796C130.819 133.238 138.236 133.238 145.466 136.253C152.696 139.268 159.672 145.281 164.631 153.607C169.591 161.933 171.555 170.931 170.763 178.725C169.971 186.518 166.438 193.039 160.465 196.597C154.491 200.155 147.075 200.156 139.844 197.141C132.615 194.126 125.638 188.113 120.679 179.787Z"
                                    fill="white" stroke="#B9C0CA" stroke-width="1.94645" />
                                <path
                                    d="M150.71 172.492C151.932 174.545 151.259 177.2 149.207 178.423L120.227 195.684C118.175 196.906 115.519 196.233 114.297 194.181C113.074 192.128 113.747 189.473 115.8 188.25L144.779 170.989C146.832 169.766 149.487 170.439 150.71 172.492Z"
                                    fill="#F6F6F7" stroke="#B9C0CA" stroke-width="1.94645" />
                                <path
                                    d="M138.545 154C139.767 156.053 139.094 158.708 137.042 159.93L108.062 177.191C106.01 178.414 103.354 177.741 102.132 175.689C100.909 173.636 101.582 170.981 103.635 169.758L132.614 152.497C134.667 151.274 137.322 151.947 138.545 154Z"
                                    fill="#F6F6F7" stroke="#B9C0CA" stroke-width="1.94645" />
                                <defs>
                                    <linearGradient id="paint0_linear_12232_225842" x1="89.5132" y1="91.2412" x2="68.0023" y2="133.84"
                                        gradientUnits="userSpaceOnUse">
                                        <stop stop-color="#F9F9FA" />
                                        <stop offset="1" stop-color="#D2D2D2" />
                                    </linearGradient>
                                    <linearGradient id="paint1_linear_12232_225842" x1="192.895" y1="173.967" x2="160.166" y2="119.019"
                                        gradientUnits="userSpaceOnUse">
                                        <stop stop-color="#D1D1D1" />
                                        <stop offset="1" stop-color="#E1E1E1" />
                                    </linearGradient>
                                </defs>
                            </svg>
                            <div class="message">
                                Oops! You're not authorized to view this content. Contact your
                                administrator for access.
                            </div>
                        </div>
                    </div>


                    <ngx-spinner bdColor="rgba(230, 230, 231,0.7)" size="medium" color="#111434" type="ball-clip-rotate"
                        fullScreen="true">
                        <p style="color: #111434; margin-top: 15px !important; font-weight: 400">
                            Please wait..
                        </p>
                    </ngx-spinner>

                </div>
            </div>
        </mat-sidenav-content>
    </mat-sidenav-container>
</ng-container>