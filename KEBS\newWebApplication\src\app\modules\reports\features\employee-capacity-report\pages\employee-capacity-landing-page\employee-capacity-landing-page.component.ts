import { Component, HostListener, OnInit } from '@angular/core';
import { takeUntil } from 'rxjs/operators';
import { Subject,Subscription } from 'rxjs';
import { ToasterMessageService } from 'src/app/modules/project-management/shared-lazy-loaded/components/toaster-message/toaster-message.service';
import { Router } from '@angular/router';
import moment from 'moment';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core';
import { MAT_MOMENT_DATE_ADAPTER_OPTIONS, MomentDateAdapter } from '@angular/material-moment-adapter';
import { EmployeeReportServiceService } from '../../services/employee-report-service.service'
import { FilterServiceService } from '../../../../../project-management/shared-lazy-loaded/components/filters-common-dialog/services/filter-service.service';

@Component({
  selector: 'app-employee-capacity-landing-page',
  templateUrl: './employee-capacity-landing-page.component.html',
  styleUrls: ['./employee-capacity-landing-page.component.scss'],
  providers: [
    {
      provide: DateAdapter,
      useClass: MomentDateAdapter,
      deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS],
    },
    {
      provide: MAT_DATE_FORMATS,
      useValue: {
        parse: {
          dateInput: 'DD-MM-YYYY',
        },
        display: {
          dateInput: 'DD-MM-YYYY',
          monthYearLabel: 'MMM YYYY',
        },
      },
    },
  ],
})
export class EmployeeCapacityLandingPageComponent implements OnInit {
  reportConfiguration: any = null;
  reportLoading: boolean = false;
  loadingGifUrl: any;
  dateRangePickerRanges = {
    'This Month': [moment().startOf('month'), moment().endOf('month')],
    'Last Month': [
      moment().subtract(1, 'month').startOf('month'),
      moment().subtract(1, 'month').endOf('month'),
    ],
    'Next Month': [
      moment().add(1, 'month').startOf('month'),
      moment().add(1, 'month').endOf('month'),
    ],
    'Upcoming 3 Months': [
      moment().startOf('month'),
      moment().add(2, 'month').endOf('month'),
    ],
    'This Year': [moment().startOf('year'), moment().endOf('year')],
    'Previous Year': [
      moment().subtract(1, 'year').startOf('year'),
      moment().subtract(1, 'year').endOf('year'),
    ],
    'Current FY': [
      moment()
        .subtract(moment().month() < 3 ? 1 : 0, 'year')
        .month(3)
        .date(1)
        .startOf('day'),
      moment()
        .add(moment().month() >= 3 ? 1 : 0, 'year')
        .month(2)
        .date(31)
        .endOf('day'),
    ],
    'Previous FY': [
      moment()
        .subtract(moment().month() < 3 ? 2 : 1, 'years')
        .month(3)
        .date(1)
        .startOf('day'),
      moment()
        .subtract(moment().month() < 3 ? 1 : 0, 'years')
        .month(2)
        .date(31)
        .endOf('day'),
    ],
  };
  max_allowed_days: number;
  applied_duration: { startDate: any; endDate: any };
  gridLoading: boolean = false;
  
  reportData = [];

  columnHeaderKeyList: Array<Object> = [];
  sub_layout = false;
  viewBySelected: string = 'month';
  viewByLabel: string;
  tabsList: Array<Object> = [];
  selectedToggle: string;
  application_id = 8222;
  internal_application_id = 'employee_capacity_report_allocation';
  filterSubscription$: Subscription;
  filterCount: number = 0;
  filterData: any = {};
  searchSelected: boolean = false;
  gridHeader:string = 'Utilization Percentage';
  gridInfo:string = 'Utilization Percentage';
  isTooltipVisible: boolean = false;
  empIndex: number;
  rangeData:any = [];
  weekColumnHeaderList: any = [];
  monthColumnHeaderList: any = [];
  dayColumnHeaderList: any = [];
  subData: string = '';
  totalDataConfig : any = [];
  skip:number = 0;
  limit: number = 15;
  searchText:string = null;
  noData: boolean = false;
  rangeLoader: boolean = false;
  minWidth: number = 100;
  allDataLoaded: boolean = false;
  lazyLoader: boolean = false;
  isDownloading: boolean = false;
  projectList: any = [
    {
      "project_name":"Testing Project",
      "start_date":"2025-01-01",
      "end_date":"2026-01-01",
      "color":"#ee4961",
      "project_code":"GLNGSOW0004",
      "daily_hours":8,
      "total_allocated_hours":240
    },
    {
      "project_name":"Testing Project",
      "start_date":"2025-04-01",
      "end_date":"2026-01-01",
      "color":"#1890FF",
      "project_code":"GLNGSOW0004",
      "daily_hours":8,
      "total_allocated_hours":240
    },
    {
      "project_name":"Testing Project",
      "start_date":"2025-04-01",
      "end_date":"2025-04-01",
      "color":"#FA8C16",
      "project_code":"GLNGSOW0004",
      "daily_hours":8,
      "total_allocated_hours":240
    }
  ];
  cellWidth: number;
  minDate:any;
  totalBarClassWidth: any = '452px';
  filterConfig: Object = null;
  showFilterDisplay : boolean = false;
  downloadHeaderName: string = 'Downloaded Data';
  font_size: number = 12;
  filterDisable: boolean = false;
  protected _onDestroy = new Subject<void>();
  constructor(private _toasterService: ToasterMessageService,private _router:Router,private _employeeReportService: EmployeeReportServiceService,private _filterService: FilterServiceService) { }

  async ngOnInit() {
    this.reportLoading = true;
    this.calculateDynamicStyle();
    this.reportConfiguration =  await this.getEmployeeDashboardConfigurations()
    await this.initiateReportConfigurations();
    await this.setDuration(null,true);
    await this.selectToggle(null,null);
    this._employeeReportService.searchData.searchParameter = ""
    this._employeeReportService.searchData.startProjectSearch = this.startSearch.bind(this);
    this.reportLoading = false;
  }

  @HostListener('window:resize')
  onResize() {
    this.calculateDynamicStyle();
  }

  /**
   * @description For Calculating Dynamic Style
   */
  calculateDynamicStyle() {
    let filterHeight = 0;
    if(this.filterCount > 0){
      filterHeight = 35;
    }
    let dynamicHeight = window.innerHeight - 75 + 'px';
    let dynamicInnerHeight = window.innerHeight - 193 - filterHeight + 'px';
    let dynamicInnerGridWidth = window.innerWidth - 78 + 'px';
    document.documentElement.style.setProperty(
      '--pageContentHeight',
      dynamicHeight
    );
    document.documentElement.style.setProperty(
      '--innerTabHeight',
      dynamicInnerHeight
    );
    document.documentElement.style.setProperty(
      '--innerGridWidth',
      dynamicInnerGridWidth
    );
  }

  /**
   * @description Calculating Cell Width
   */
  calculateCellWidth(){
    let columnCount  = 0;
    if(this.sub_layout){
      for(let column of this.columnHeaderKeyList){
        columnCount += column['sub_columns']?.length || 0
      }
    }
    else{
      columnCount += this.columnHeaderKeyList?.length || 0
    }
    
      
    let min_width = this.minWidth;
    const containerWidth = window.innerWidth - 130 - 352 ; // Assume container width (px). Replace with actual value.
    const gap = 10; // Gap between columns
    const totalGaps = (columnCount - 1) * gap;

    // Calculate column width dynamically
    let width = (containerWidth - totalGaps) / columnCount;
    this.cellWidth = width < min_width ? min_width : width
    let dynamicCellWidth = width < min_width ? `${min_width}px` : `${width}px`;
    
    document.documentElement.style.setProperty(
      '--cellWidth',
      dynamicCellWidth
    );
  }

  /**
   * @description API for fetching the Employee Report Configuration
   * @returns 
   */
  getEmployeeDashboardConfigurations() {
    
    return new Promise((resolve, reject) => {
      this._employeeReportService
        .getEmployeeDashboardConfigurations()
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res) => {
            if (res && res['messType'] == 'S' && res['data']) {
              resolve(res['data']);
            }
            else
            resolve(null);
          },
          error: (err) => {
            this._toasterService.showError('Error in Fetching Report Configuration');
            console.log(err);
            resolve(null);
          },
        });
    });
  }

  /**
   * @description Initializing the required data
   */
  async initiateReportConfigurations(){
    this.loadingGifUrl = (this.reportConfiguration && this.reportConfiguration['ui_themes'] && this.reportConfiguration['ui_themes']['loader_gif']) ? this.reportConfiguration['ui_themes']['loader_gif'] : 'https://assets.kebs.app/kebs%20loading.gif';
    
    let primary_color = (this.reportConfiguration && this.reportConfiguration['ui_themes'] && this.reportConfiguration['ui_themes']['primary_color']) ? this.reportConfiguration['ui_themes']['primary_color'] : '#EE4961';
    document.documentElement.style.setProperty(
      '--primaryButtonColor',
      primary_color
    );

    let font_style = (this.reportConfiguration && this.reportConfiguration['ui_themes'] && this.reportConfiguration['ui_themes']['font-family']) ? this.reportConfiguration['ui_themes']['font-family'] : 'Dm Sans';
    document.documentElement.style.setProperty(
      '--ecReportFontFamily',
      font_style
    );
    let days_range = (this.reportConfiguration &&  this.reportConfiguration['max_allowed_days']) ? this.reportConfiguration['max_allowed_days'] : 365;
    this.max_allowed_days = days_range;

    this.reportConfiguration?.view_by_settings?.types.find((data) => {
      if (data['is_default']) {
        this.viewBySelected = data['key'] ? data['key'] : null
        this.viewByLabel =  data['label'] ? data['label'] : null;
        this.sub_layout = data['sub_layout'] ? data['sub_layout'] : false;
        this.minWidth = data['min-width'] ? data['min-width'] : 100
        this.font_size = data['font-size'] ? data['font-size'] : 12
        let dynamicFontSize = this.font_size + 'px';
        document.documentElement.style.setProperty(
          '--subHeaderFontSize',
          dynamicFontSize
        );
      }
    });
    this.tabsList = this.reportConfiguration?.toggle_settings?.types || [];
    this.selectedToggle =  this.reportConfiguration?.toggle_settings?.default || 'allocation';
    const foundType = this.tabsList.find(type => type['key'] == this.selectedToggle);

    if (foundType) {
        this.gridHeader = foundType['gridHeader'] ? foundType['gridHeader'] : 'Utilization Percentage';
        this.gridInfo = foundType['gridInfo'] ? foundType['gridInfo'] : 'Utilization Percentage';
        this.rangeData = foundType['range_data'] ? foundType['range_data'] : [];
        this.totalDataConfig = foundType['total_data'] ? foundType['total_data'] : [];
        this.internal_application_id = foundType['internal_application_id'] ? foundType['internal_application_id'] : 'employee_capacity_report_allocation'
        this.downloadHeaderName = foundType['download_header_name'] ? foundType['download_header_name'] : 'Downloaded Data';
    }
      
  }
  /**
   * @description For routing back to reports
   */
  backToReports() {
    this._router.navigateByUrl("/main/reports");
  }
  /**
   * @description Getter function for displaying in proper Date format
   * @param date 
   * @returns 
   */
  getDateFormat(date){
    if(date){
      return moment(date).format('DD MMM YYYY')
    }
  }

  /**
   * @description Changing the View BY
   * @param data 
   */
  async changeViewBy(data) {
    if (data && data['key'] && data['label']) {
      this.gridLoading = true;
      this.viewByLabel = data['label'];
      this.viewBySelected = data['key'];
      this.sub_layout = data['sub_layout'] || false;
      this.minWidth = data['min-width'] ? data['min-width'] : 100
      this.font_size = data['font-size'] ? data['font-size'] : 12
      let dynamicFontSize = this.font_size + 'px';
      document.documentElement.style.setProperty(
        '--subHeaderFontSize',
        dynamicFontSize
      );
      await this.employeeExpandReset();
      await this.simulateAsyncOperation();

      this.columnHeaderKeyList = this.viewBySelected === 'day' 
      ? this.dayColumnHeaderList 
      : this.viewBySelected === 'month' 
      ? this.monthColumnHeaderList 
      : this.viewBySelected === 'week' 
      ? this.weekColumnHeaderList 
      : [];
      if(this.viewBySelected == 'day'){
        this.subData = 'day_data'
      }
      else if(this.viewBySelected == 'month'){
        this.subData = 'month_data'
      }
      else if(this.viewBySelected == 'week'){
        this.subData = 'week_data'
      }
      await this.calculateCellWidth()
      this.gridLoading = false;
    }
  }

  async simulateAsyncOperation() {
    await new Promise((resolve) => setTimeout(resolve, 500));
  }
  

  /**
   * @description On Changing the date
   * @param event
   */
  async onDateInputChange(event) {
    let selectedDate = event;
    const newEndDateValue = selectedDate.endDate._d;
    const newStartDateValue = selectedDate.startDate._d;
    const newendDateValue = moment(newEndDateValue).format('YYYY-MM-DD');
    const newstartDateValue = moment(newStartDateValue).format('YYYY-MM-DD');

    this.applied_duration = {
      startDate: newstartDateValue,
      endDate: newendDateValue,
    };
  }

  /**
   * @description For Setting the Default Date Range from the Config
   */
  async setDuration(event,initial = null) {
    let selectedDate = event;
    if (this.reportConfiguration && this.reportConfiguration['date_enable']) {
      if(initial){
        let default_date_config_value =  this.reportConfiguration && this.reportConfiguration['default_enable_config'] ? this.reportConfiguration['default_enable_config'] : 'This Month';
        let default_date_config = this.dateRangePickerRanges[default_date_config_value]
        this.applied_duration = {
          startDate: default_date_config[0],
          endDate: default_date_config[1],
        };
      }
      else{
        const newEndDateValue = selectedDate?.endDate?._d;
        const newStartDateValue = selectedDate?.startDate?._d;
        const newendDateValue = moment(newEndDateValue).format('YYYY-MM-DD');
        const newstartDateValue = moment(newStartDateValue).format('YYYY-MM-DD');

        this.applied_duration = {
          startDate: newstartDateValue,
          endDate: newendDateValue,
        };
        if(!this.gridLoading){
          await this.getReportData();
        }
      }
    }
  }

  /**
   * @description Changing the Toggle
   * @param event 
   */
  async selectToggle(event,type) {
    if(event && type){
      this.rangeLoader = true;
      this.selectedToggle = event.value;
      this.gridHeader = type?.gridHeader;
      this.gridInfo = type?.gridInfo;
      this.rangeData = type['range_data'] ? type['range_data'] : [];
      this.totalDataConfig = type['total_data'] ? type['total_data'] : [];
      this.internal_application_id = type['internal_application_id'] ? type['internal_application_id'] : 'employee_capacity_report_allocation'
      this.downloadHeaderName = type['download_header_name'] ? type['download_header_name'] : 'Downloaded Data';
      this.rangeLoader = false;
    }
    this.showFilterDisplay = false;
    setTimeout(() => {
      this.showFilterDisplay = true;
    }, 1000);
    if (this.filterSubscription$) {
      this.filterSubscription$.unsubscribe();
    }
    this.filterSubscription$ = await this._filterService.getFilterConfig(this.application_id,this.internal_application_id).subscribe(async filterList => {
      await this.getReportData();  
    });
  }

  /**
   * @description Enable Filter Config
   */
  openFilter(){
    this.filterDisable = true
    this._filterService.openFilterLandingPage(this.application_id,this.internal_application_id,this.filterData);
    setTimeout(() => {
      this.filterDisable = false; // Show tooltip after ensuring the target is updated
    }, 1000);
  }

  /**
   * @description Calling the Main Report API
   */
  async getReportData(skip = 0){
    this.gridLoading = skip == 0 ? true : false;
    this.skip = skip;
    this.reportData = this.skip > 0 ? this.reportData :  [];
    if(this.reportConfiguration?.filter_enable){
      await this.applyFilter();
    }
    if(skip == 0){
      await this.calculateSkipLimit();
    }
    let data: any = await this.getReportEmpData();
    if(data && data.length > 0){
      if(this.skip > 0){
        this.reportData = this.reportData.concat(data);
      }
      else {
        this.reportData = data;
      }
      this.allDataLoaded = false;
    }
    else {
      this.allDataLoaded = true;
    }
  
    this.columnHeaderKeyList = this.viewBySelected === 'day' ? this.dayColumnHeaderList : 
                   this.viewBySelected === 'month' ? this.monthColumnHeaderList: 
                   this.viewBySelected === 'week' ? this.weekColumnHeaderList : []; 
    if(this.viewBySelected == 'day'){
      this.subData = 'day_data'
    }
    else if(this.viewBySelected == 'month'){
      this.subData = 'month_data'
    }
    else if(this.viewBySelected == 'week'){
      this.subData = 'week_data'
    }
    await this.calculateCellWidth()
  
    this.gridLoading = false;
  }

  /**
   * @description Applying Filter Config
   */
  async applyFilter() {
    this.filterConfig = null;
    let filter = await this.getUserFilterConfig();
    let emp_filter_ids = this.reportConfiguration['employee_filter_ids'] && this.reportConfiguration['employee_filter_ids'].length > 0 ? this.reportConfiguration['employee_filter_ids'] : [];
    if (filter && filter !== '' && filter != null) {
      let filterVal = filter && filter['filterConfig'] && filter['filterConfig']['filterData'] ? filter['filterConfig']['filterData'] : [];
      if(filterVal && filterVal.length > 0){
        this.filterCount = filterVal.length

        // Separate filters
        let empFilters = filterVal.filter(f => emp_filter_ids.includes(f.id));
        let allocationFilters = filterVal.filter(f => !emp_filter_ids.includes(f.id));

        // Generate queries
        let emp_query = this._filterService.generateConditionBasedQuery(empFilters);
        let allocation_query = this._filterService.generateConditionBasedQuery(allocationFilters);

        if (!this.filterConfig) {
          this.filterConfig = {}; // Initialize as an empty object if it's null
        }
        this.filterConfig['filter_emp_query'] = emp_query;
        this.filterConfig['filter_allocation_query'] = allocation_query;
        this.filterConfig['filter'] = filterVal
      }
      else{
        this.filterCount = 0;
      }
    }
    else{
      this.filterCount = 0;
    }
    await this.calculateDynamicStyle();
  }

  /**
   * @description Fetching the User Filter Config
   */
  getUserFilterConfig(){
    return new Promise((resolve,reject) => {
      this._filterService.getFilterUserConfig(this.application_id,
      this.internal_application_id).pipe(takeUntil(this._onDestroy))
      .subscribe({
        next: (res) => {
          
          if(res['messType'] == 'S') {
            resolve(res['data'])
          }
          else{
            resolve([])
          }
        },
        error:(err) => {
          this._toasterService.showError('Error in Fetching User Filter Config');
          resolve([]);
        }
      });
    });
  }

  /**
   * @description To Enable Search
   */
  async openSearchComponent(action='open'){
    if(action == 'close'){
      this.searchSelected = false;
    }
    else{
      this.searchSelected = true;
    }
  }

  async startSearch() {
    this.searchText = this._employeeReportService.searchData.searchParameter
    this.searchText = this._employeeReportService?.searchData?.searchParameter || null;
    await this.getReportData();
  }

  showTooltip(index: number): void {
    this.empIndex = index; // Update the target index
    this.isTooltipVisible = false; // Force tooltip to hide first
    setTimeout(() => {
      this.isTooltipVisible = true; // Show tooltip after ensuring the target is updated
    }, 0); // Let Angular process the updated target
  }
  
  hideTooltip(): void {
    this.isTooltipVisible = false;
  }

  /**
   * @description Fetching the report data
   */
  getReportEmpData(){
    let params = {
      "start_date":moment(this.applied_duration?.startDate).format("YYYY-MM-DD"),
      "end_date":moment(this.applied_duration?.endDate).format("YYYY-MM-DD"),
      "search_param":this.searchText,
      "skip":this.skip,
      "limit":this.limit,
      "range_config":this.rangeData,
      "total_data_config":this.totalDataConfig,
      "types": this.reportConfiguration?.view_by_settings?.types,
      "current_date":moment().format("YYYY-MM-DD"),
      "filterConfig":this.filterConfig
    }

    if(this.selectedToggle == 'allocation'){
      return new Promise((resolve,reject) => {
        this._employeeReportService.getReportAllocatedData(params).pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res) => {
            
            if(res['messType'] == 'S') {
              this.weekColumnHeaderList = res['weekColumnHeaderList'];
              this.dayColumnHeaderList = res['dayColumnHeaderList'];
              this.monthColumnHeaderList = res['monthColumnHeaderList'];
              resolve(res['data'])
            }
            else{
              resolve([])
            }
          },
          error:(err) => {
            this._toasterService.showError('Error in Fetching Report Data');
            resolve([]);
          }
        });
      });
    }
    else {
      return new Promise((resolve,reject) => {
        this._employeeReportService.getReportAvailableData(params).pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res) => {
            
            if(res['messType'] == 'S') {
              this.weekColumnHeaderList = res['weekColumnHeaderList'];
              this.dayColumnHeaderList = res['dayColumnHeaderList'];
              this.monthColumnHeaderList = res['monthColumnHeaderList'];
              resolve(res['data'])
            }
            else{
              resolve([])
            }
          },
          error:(err) => {
            this._toasterService.showError('Error in Fetching Report Data');
            resolve([]);
          }
        });
      });
    }
    
  }

  /**
   * @description Lazy Loading
   */
  async onScroll(){
    if(this.allDataLoaded || this.lazyLoader) return;
    this.lazyLoader = true;
    this.skip += this.limit;
    this.totalBarClassWidth = (this.columnHeaderKeyList.length * this.cellWidth) + 352 + 10 + 'px';
    await this.getReportData(this.skip);
    this.lazyLoader = false;
  }

  /**
   * @description Downloading the Report
   */
  downloadReport(){
    if(this.reportData && this.reportData.length > 0){
      this.isDownloading = true
      let params = {
        "start_date":moment(this.applied_duration?.startDate).format("YYYY-MM-DD"),
        "end_date":moment(this.applied_duration?.endDate).format("YYYY-MM-DD"),
        "search_param":this.searchText,
        "range_config":this.rangeData,
        "total_data_config":this.totalDataConfig,
        "types": this.reportConfiguration?.view_by_settings?.types,
        "current_date":moment().format("YYYY-MM-DD"),
        "selection_type": this.selectedToggle,
        "all_data":true,
        "view_type": this.viewBySelected,
        "filterConfig":this.filterConfig,
        "report_name": this.downloadHeaderName
      }
      return new Promise((resolve,reject) => {
        this._employeeReportService.downloadReportData(params).pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res) => {
            
            if (res['messType'] == 'S') {
              let bufferData = res['buffer'];
          
              // Convert Base64 string to ArrayBuffer (if needed)
              if (typeof bufferData === 'string') {
                  const byteCharacters = atob(bufferData); // Decode Base64
                  const byteNumbers = new Array(byteCharacters.length);
                  for (let i = 0; i < byteCharacters.length; i++) {
                      byteNumbers[i] = byteCharacters.charCodeAt(i);
                  }
                  bufferData = new Uint8Array(byteNumbers); // Convert to Uint8Array
              }
          
              // Create a Blob with the correct MIME type
              const blob = new Blob([bufferData], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
          
              // Create a URL from the Blob
              const url = window.URL.createObjectURL(blob);
          
              // Create a hidden <a> element and trigger a download
              const a = document.createElement('a');
              a.href = url;
              a.download = `${this.downloadHeaderName}.xlsx`; // Ensure filename has .xlsx extension
              document.body.appendChild(a);
              a.click();
          
              // Clean up
              document.body.removeChild(a);
              window.URL.revokeObjectURL(url);
              this.isDownloading = false
              resolve([])
            }
          
            else{
              this.isDownloading = false;
              if(res['time_out']){
                this._toasterService.showInfo('Report generation is scheduled, Soon You will notified in Email',5000)
              }
              else{
                this._toasterService.showError('Error in Downloading the Report');
              }
              resolve([])
            }
          },
          error:(err) => {
            this.isDownloading = false;
            this._toasterService.showError('Error in Downloading the Report');
            resolve([]);
          }
        });
      });
    }
    else{
      this._toasterService.showWarning('No Data available for download.',2000)
    }
    
  }

  calculateGanttPositions(employee: any) {
    // Find the minimum start date and normalize it
    let minDate = moment.min(employee['projectList'].map(project => moment.utc(project.start_date).startOf('day')));
    minDate = moment.min(minDate, moment.utc(this.applied_duration.startDate).startOf('day'));

    this.totalBarClassWidth = (this.columnHeaderKeyList.length * this.cellWidth) + 352 + 10 + 'px';
    let colorList = this.reportConfiguration?.color_config || [];

    employee['projectList'].forEach((project, index) => {
        // Normalize task start and end dates
        const taskStart = moment.utc(project.start_date).startOf('day');
        const taskEnd = moment.utc(project.end_date).startOf('day');

        if (this.viewBySelected === 'month') {
            const startOffset = taskStart.startOf('month').diff(minDate.startOf('month'), 'months'); 
            let duration = taskEnd.diff(taskStart, 'months') + (taskEnd.date() > 1 ? 1 : 0);

            // Ensure minimum width for single-month tasks
            duration = Math.max(duration, 1);

            project.left = (startOffset * this.cellWidth) + 'px';
            project.width = (duration * this.cellWidth) - 10 + 'px';
        } 
        
        else if (this.viewBySelected === 'week') {
            const startOffset = taskStart.startOf('week').diff(minDate.startOf('week'), 'weeks'); 
            let duration = taskEnd.startOf('week').diff(taskStart.startOf('week'), 'weeks') + 1;  // Always include the last week

            duration = Math.max(duration, 1);

            project.left = (startOffset * this.cellWidth) + 'px';
            project.width = (duration * this.cellWidth) - 10 + 'px';
        }

        // Apply colors
        let color_set = colorList.length > 0 ? colorList[index % colorList.length] : null;
        project.color = color_set?.color;
        project.color_shade = color_set?.color_shade;
    });
}
  
  
  async toggleEmployeeProjects(employee: any) {
    employee['ganttLoader'] = true;
    if(!employee['projectList']){
      employee['projectList'] = await this.getEmployeeGanttData(employee.associate_id)
    }
    employee.expand = !employee?.expand;  // Toggle the expand property
    if (employee.expand) {
      this.calculateGanttPositions(employee); // Calculate Gantt positions only when expanding
    }
    employee['ganttLoader'] = false;
  }

  getEmployeeGanttData(associate_id){
    let params = {
      "associate_id":associate_id,
      "start_date":moment(this.applied_duration?.startDate).format("YYYY-MM-DD"),
      "end_date":moment(this.applied_duration?.endDate).format("YYYY-MM-DD")
    }
    return new Promise((resolve,reject) => {
      this._employeeReportService.getEmployeeGanttData(params).pipe(takeUntil(this._onDestroy))
      .subscribe({
        next: (res) => {
          
          if (res['messType'] == 'S') {
            resolve(res['data'])
          }
        
          else{
            resolve([])
          }
        },
        error:(err) => {
          this._toasterService.showError('Error in Fetching the Project Data');
          resolve([]);
        }
      });
    });    
  }

  employeeExpandReset(){
    for(let item of this.reportData){
      if(item?.expand) {
        item.expand = false;
      }
    }
  }

  calculateSkipLimit(){
    let filterHeight = 0;
    if(this.filterCount > 0){
      filterHeight = 35;
    }
    const tableHeight = window.innerHeight - 193 - filterHeight;
    const itemHeight = 50;

    // Calculate how many items fit into the container
    const itemsToLoad = Math.floor(tableHeight / itemHeight);
    this.limit = itemsToLoad > 15 ? itemsToLoad : 15
  }
  
  /**
   * @description Destroying the Filter Subscription
   */
  ngOnDestroy() {
    if(this.filterSubscription$){
      this.filterSubscription$.unsubscribe();
    }
  }

}
