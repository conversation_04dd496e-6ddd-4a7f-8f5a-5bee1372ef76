<div class="container-fluid">
    <div class="row">
        <div class="col-2 mt-2">
          
        </div>
        <div class="col mt-3 d-flex justify-content-center">
            <span style="font-size:16px;font-weight: 500;color:#cf0001; margin-right: 274px;">
               Expense AP report
            </span>
            <span>
            </span>
        </div>
       
    </div>
    <div class="row mt-2" *ngIf="reportData">
        <div class="col-12">
            <dx-data-grid id="gridContainer" [allowColumnResizing]="true" [dataSource]="reportData" [showBorders]="true"
                [hoverStateEnabled]="true" class="dev-style custom-grid" [columnAutoWidth]="true" (onCellPrepared)="onCellPrepared($event)" >
                <dxo-export [enabled]="true" fileName="Employee level AP report">
                </dxo-export>

                <dxo-column-chooser [enabled]="true" mode="select"> </dxo-column-chooser>

                <dxo-column-fixing [enabled]="true"></dxo-column-fixing>
                <dxo-group-panel [visible]="true"></dxo-group-panel>
                <dxo-state-storing
                  [enabled]="true"
                  type="localStorage"
                  storageKey="storage"
                ></dxo-state-storing>

                <dxo-search-panel [visible]="true" [width]="240" placeholder="Search..."></dxo-search-panel>
                <dxo-selection mode="single"></dxo-selection>
                <dxo-header-filter [visible]="true"></dxo-header-filter>
                <dxo-filter-row [visible]="false"></dxo-filter-row>

                <dxi-column *ngFor="let value of columnConfig" [allowSorting]="true"
                [dataField]="value.dataField" [allowReordering]="true" [caption]="value.caption">
            </dxi-column>
            <dxo-summary>
                <dxi-total-item column="total_balance_due" summaryType="sum" displayFormat="Total : {0}">
                </dxi-total-item>
            </dxo-summary>
            </dx-data-grid>
        </div>
    </div>
</div>
<ngx-spinner size="medium" type="ball-clip-rotate"  bdColor="rgba(236, 233, 233, 0.8)" color="#cf0001"></ngx-spinner>   
