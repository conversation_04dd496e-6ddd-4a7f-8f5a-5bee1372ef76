import { Component, OnInit } from '@angular/core';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatDialog } from '@angular/material/dialog';
import * as _ from "underscore";
import { NgxSpinnerService } from 'ngx-spinner';
import * as moment from 'moment';
import { EmployeeLevelApReportService } from "../services/employee-level-ap-report.service"

@Component({
  selector: 'app-employee-level-ap-report',
  templateUrl: './employee-level-ap-report.component.html',
  styleUrls: ['./employee-level-ap-report.component.scss']
})
export class EmployeeLevelApReportComponent implements OnInit {

  reportData: any;

  constructor(private _employeeAPService: EmployeeLevelApReportService, private snackBar: MatSnackBar,
     private dialog: MatDialog, private _spinnerService: NgxSpinnerService
) { }

columnConfig = [
  {"caption": "Expense Type", "dataField": "expense_type", "allowSorting": true },
  {"caption": "Entity Name", "dataField": "entity_name", "allowSorting": true },
  {"caption": "Entity Currency", "dataField": "entity_currency_code", "allowSorting": true },
  { "caption": "Employee Name", "dataField": "employee_name", "allowSorting": true },
  { "caption": "Associate ID", "dataField": "employee_aid", "allowSorting": true },
  { "caption": "Amount", "dataField": "total_balance_due" },
];

  ngOnInit(): void {
    this.employeeLevelAPReport()
  }

  async employeeLevelAPReport(){
    this._spinnerService.show();
    let expenseData = await this._employeeAPService.employeeLevelAPReport();
    if(expenseData["messType"] == "S"){
      this._spinnerService.hide();
      this.snackBar.open(expenseData["messText"], 'Dismiss', { duration: 2000 });
      this.reportData = expenseData['data'];
    }
    else{
      this._spinnerService.hide();
      this.snackBar.open(expenseData["messText"], 'Dismiss', { duration: 2000 });
    }
    this._spinnerService.hide();
  }
}

