import { Component, OnInit } from '@angular/core';
import { MatDialog } from "@angular/material/dialog";
import * as _ from 'underscore';
import * as moment from "moment";
import { takeUntil } from "rxjs/operators";
import { Subject, Subscription } from "rxjs";
import { LoginService } from "src/app/services/login/login.service";
import { UtilityService } from 'src/app/services/utility/utility.service';
import { MomentDateAdapter, MAT_MOMENT_DATE_ADAPTER_OPTIONS } from '@angular/material-moment-adapter';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core';
import { ExpenseReportService } from '../../services/expense-report.service'
import { NgxSpinnerService } from 'ngx-spinner';
import { SharedLazyLoadedComponentsService } from 'src/app/modules/shared-lazy-loaded-components/services/shared-lazy-loaded-components.service';
import { UdrfService } from 'src/app/services/udrf/udrf.service';
import { Router } from '@angular/router';
import { JsonToExcelService } from 'src/app/services/excel/json-to-excel.service';
import { ErrorService } from 'src/app/services/error/error.service';
import { ReportsService } from "../../../../services/reports.service";
import {AttachmentViewerComponent} from "../../components/attachment-viewer/attachment-viewer.component";
import { MatSnackBar } from "@angular/material/snack-bar";

export const MONTH_YEAR_DATE_FORMAT = {
  parse: {
    dateInput: 'MMMM YYYY',
  },
  display: {
    dateInput: 'DD - MM - YYYY',
    monthYearLabel: 'MMM YYYY',
    dateA11yLabel: 'LL',
    monthYearA11yLabel: 'MMMM YYYY',
  },
};
@Component({
  selector: 'app-expense-report-landing',
  templateUrl: './expense-report-landing.component.html',
  styleUrls: ['./expense-report-landing.component.scss'],
  providers: [
    {
      provide: DateAdapter,
      useClass: MomentDateAdapter,
      deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS]
    },
    { provide: MAT_DATE_FORMATS, useValue: MONTH_YEAR_DATE_FORMAT },
  ]
})
export class ExpenseReportLandingComponent implements OnInit {

  applicationId = 76;
  cardIterator: any;
  currentUserOId = "";
  ExpenseItemDataCurrentIndex = 0;
  ExpenseList: any[];
  protected _onDestroy = new Subject<void>();
  protected _onAppApiCalled = new Subject<void>();
  isMoreLoading: boolean = false;
  noData: boolean = false;
  responseData: any;
  startIndex = 0;
  associateData: any = [];
  slaView: boolean = false;
  defaultDataRetrievalCount = 15;
  isCardClicked: boolean = false;
  totallen = 0;
  cardClicked = "";
  dateFormat: string = 'DD-MMM-YYYY';
  viewType = 0;
  currency = " INR"
  formFieldConfig : any;
  fieldConfig : any = {};
  dataTypeArray = [
    // {
    //   dataType: 'Total amount paid',
    //   dataTypeCode: 'TAP',
    //   dataTypeValue: "0 INR",
    //   isActive: false,
    //   isVisible: true,
    //   isDisabled: false,
    // },
    // {
    //   dataType: 'Total amount pending',
    //   dataTypeCode: 'TAPE',
    //   dataTypeValue: "0 INR",
    //   isActive: false,
    //   isVisible: true,
    //   isDisabled: false,
    // },
    {
      dataType: 'Total Claims Paid ',
      dataTypeCode: 'TCP',
      dataTypeValue: "0 Cr",
      dataTypeValueOriginal: 0,
      isActive: false,
      isVisible: true,
      isDisabled: false,
      cardType: 'status',
      isToolTip: true,
    },
    {
      dataType: 'Total Claims Pending',
      dataTypeCode: 'TCPE',
      dataTypeValue: "0 Cr",
      dataTypeValueOriginal: 0,
      isActive: false,
      isVisible: true,
      isDisabled: false,
      cardType: 'status',
      isToolTip: true,
    },
    {
      dataType: 'Total advance paid',
      dataTypeCode: 'TADP',
      dataTypeValue: "0 Cr",
      dataTypeValueOriginal: 0,
      isActive: false,
      isVisible: true,
      isDisabled: false,
      cardType: 'status',
      isToolTip: true,
    },
    {
      dataType: 'Advance yet to KO',
      dataTypeCode: 'TAKO',
      dataTypeValue: "0 Cr",
      dataTypeValueOriginal: 0,
      isActive: false,
      isVisible: true,
      isDisabled: false,
      cardType: 'status',
      isToolTip: true,
    },
    {
      dataType: 'No Of Request Pending',
      dataTypeCode: 'TNRP',
      dataTypeValue: "0",
      isActive: false,
      isVisible: true,
      isDisabled: false,
      cardType: 'status',
    },
    {
      dataType: 'Claim Amount Pending',
      dataTypeCode: 'CAP',
      dataTypeValue: "0 Cr",
      dataTypeValueOriginal: 0,
      isActive: false,
      isVisible: true,
      isDisabled: false,
      cardType: 'status',
      isToolTip: true,
    }
  ];
  slaDataTypeArray = [
    {
      dataType: 'No Of Request Pending',
      dataTypeCode: 'TNRP',
      dataTypeValue: "0 Cr",
      isActive: false,
      isVisible: true,
      isDisabled: false,
      isToolTip: false,
    },
    {
      dataType: 'Claim Amount Pending > 30 days',
      dataTypeCode: 'CAP',
      dataTypeValue: "0 Cr",
      isActive: false,
      isVisible: true,
      isDisabled: false,
      isToolTip: true,
    }
  ];

  //SR Expense UDRF Format --------------------------------------------------------------------------------------

  udrfBodyColums = [
    {
      item: 'id',
      header: 'Expense Id',
      isVisible: 'true',
      type: 'text',
      position: 1,
      isActive: true,
      colSize: '2',
      textClass: 'value13Bold',
      headerTextClass: 'text-center',
      filterId: 1,
      sortOrder: 'N',
      width: 100,
      hasColumnClick: true,
      alternateFieldName: ['Expense ID']
    },
    {
      item: 'expense_type_name',
      nextItem: 'name',
      header: 'Expense Type - Category',
      isVisible: 'true',
      type: 'textN',
      position: 1,
      isActive: true,
      colSize: '2',
      textClass: 'value13Bold',
      headerTextClass: 'text-center',
      filterId: 1,
      sortOrder: 'N',
      width: 300,
      hasColumnClick: true,
      alternateFieldName: ["Category", "Expense Type"]
    },
    {
      item: 'employee_name',
      header: 'Associate Name',
      isVisible: 'true',
      type: 'profileImg',
      position: 2,
      isActive: true,
      colSize: '2',
      textClass: 'value13Bold',
      headerTextClass: 'text-center',
      filterId: 13,
      sortOrder: 'I',
      width: 240,
      hasColumnClick: true,
      alternateFieldName: ['Associate Name']
    },
    {
      item: 'amount',
      header: 'Amount',
      isVisible: 'true',
      type: 'text',
      position: 3,
      isActive: true,
      colSize: '2',
      textClass: 'value13Bold',
      headerTextClass: 'text-center',
      filterId: 3,
      sortOrder: 'N',
      width: 140,
      hasColumnClick: true,
      alternateFieldName: ['Amount']
    },
    {
      item: 'currency_code',
      header: 'Currency',
      isVisible: 'true',
      type: 'text',
      position: 4,
      isActive: true,
      colSize: '2',
      textClass: 'value13Bold',
      headerTextClass: 'text-center',
      filterId: 3,
      sortOrder: 'N',
      width: 140,
      hasColumnClick: true,
      alternateFieldName: ['Currency']
    },
    {
      item: 'entity_amount',
      header: 'Entity Currency Amount',
      isVisible: 'true',
      type: 'text',
      position: 3,
      isActive: true,
      colSize: '2',
      textClass: 'value13Bold',
      headerTextClass: 'text-center',
      filterId: 3,
      sortOrder: 'N',
      width: 200,
      hasColumnClick: true,
      alternateFieldName: ['Entity Currency Amount']
    },
    {
      item: 'entity_currency_code',
      header: 'Entity Currency',
      isVisible: 'true',
      type: 'text',
      position: 4,
      isActive: true,
      colSize: '2',
      textClass: 'value13Bold',
      headerTextClass: 'text-center',
      filterId: 3,
      sortOrder: 'N',
      width: 140,
      hasColumnClick: true,
      alternateFieldName: ['Entity Currency']
    },
    {
      item: 'cost_centre',
      header: 'Cost Centre',
      subItem: 'cost_centre_description',
      isVisible: 'true',
      type: 'text3',
      position: 5,
      isActive: true,
      colSize: '2',
      textClass: 'value13Bold',
      headerTextClass: 'text-center',
      filterId: 4,
      sortOrder: 'I',
      width: 200,
      hasColumnClick: true,
      alternateFieldName: ["Cost Center", "Cost Center Description", "Charge code", "Charge code Description"]
    },
    {
      item: 'status_name',
      header: 'Status',
      isVisible: 'true',
      type: 'status',
      position: 6,
      isActive: true,
      colSize: '2',
      textClass: 'value13Bold',
      headerTextClass: 'text-center',
      filterId: 6,
      sortOrder: 'I',
      width: 140,
      hasColumnClick: true,
      alternateFieldName: ["Status"]
    },
    {
      item: 'cc_approved_on',
      header: 'Cost Center Action On',
      isVisible: 'true',
      type: 'text',
      position: 7,
      isActive: true,
      colSize: '2',
      textClass: 'value13Bold',
      headerTextClass: 'text-center',
      filterId: 18,
      sortOrder: 'N',
      width: 160,
      hasColumnClick: true,
      alternateFieldName: ['Cost Center Action On', 'Charge Code Action On']
    },
    {
      item: 'status_date',
      header: 'Action On Date',
      isVisible: 'true',
      type: 'text',
      position: 8,
      isActive: true,
      colSize: '2',
      textClass: 'value13Bold',
      headerTextClass: 'text-center',
      filterId: 6,
      sortOrder: 'N',
      width: 160,
      hasColumnClick: true,
      alternateFieldName: ['Action On Date']
    },
    {
      item: 'submission_date',
      header: 'Submission Date',
      isVisible: 'true',
      type: 'text',
      position: 9,
      isActive: true,
      colSize: '2',
      textClass: 'value13Bold',
      headerTextClass: 'text-center',
      filterId: 14,
      sortOrder: 'I',
      width: 160,
      hasColumnClick: true,
      alternateFieldName: ['Submission Date']
    },
    {
      item: 'action',
      isActive: true,
      header: 'Actions',
      isVisible: 'true',
      type: 'action',
      position: 10,
      colSize: 1,
      width: 150,
      sortOrder: 'N',
      hasColumnClick: false
    },
    {
      item: 'description',
      isActive: true,
      header: 'Description',
      textClass: 'value13Bold',
      isVisible: 'true',
      type: 'text',
      position: 11,
      colSize: '2',
      width: 200,
      sortOrder: 'N',
      hasColumnClick: true,
      alternateFieldName: ['Description']
    },
    {
      item: 'expense_end_date',
      isActive: true,
      header: 'Expense Duration Start Date',
      textClass: 'value13Bold',
      isVisible: 'true',
      type: 'text',
      position: 11,
      colSize: '2',
      width: 200,
      sortOrder: 'N',
      hasColumnClick: true,
      alternateFieldName: ['Expense Duration Start Date']
    },
    {
      item: 'expense_start_date',
      isActive: true,
      header: 'Expense Duration End Date',
      textClass: 'value13Bold',
      isVisible: 'true',
      type: 'text',
      position: 12,
      colSize: '2',
      width: 200,
      sortOrder: 'N',
      hasColumnClick: true,
      alternateFieldName: ['Expense Duration End Date']
    },
    {
      item: 'people_involved',
      isActive: true,
      header: 'People Involved',
      textClass: 'value13Bold',
      isVisible: 'true',
      type: 'expensePeopleInvolved',
      position: 13,
      colSize: '2',
      width: 200,
      sortOrder: 'N',
      hasColumnClick: true,
      alternateFieldName: ['People Involved']
    },
    {
      item: 'expense_attachments',
      isActive: true,
      header: 'Download Attachments',
      textClass: 'value13Bold',
      isVisible: 'true',
      type: 'expenseAttachments',
      position: 13,
      colSize: '2',
      width: 200,
      sortOrder: 'N',
      hasColumnClick: true,
    },
    {
      item: 'billing_type',
      isActive: true,
      header: 'Customer Billing',
      textClass: 'value13Bold',
      isVisible: 'true',
      type: 'text',
      position: 14,
      colSize: '2',
      width: 200,
      sortOrder: 'N',
      hasColumnClick: true,
      alternateFieldName: ['Customer Billing']
    },
    {
      item: 'approvers',
      isActive: true,
      header: 'Approvers',
      textClass: 'value13Bold',
      isVisible: 'true',
      type: 'expenseApprover',
      position: 15,
      colSize: '2',
      width: 200,
      sortOrder: 'N',
      hasColumnClick: true,
      alternateFieldName: ['Approvers']
    },
    {
      item: 'cc_ageing_days',
      isActive: true,
      header: 'Cost center Approver Ageing Days',
      textClass: 'value13Bold',
      isVisible: 'true',
      type: 'text',
      position: 16,
      colSize: '2',
      width: 200,
      sortOrder: 'N',
      hasColumnClick: true,
      alternateFieldName: ['Cost center Approver Ageing Days']
    },
    {
      item: 't_ageing_days',
      isActive: true,
      header: 'Treasury Approver Ageing Days',
      textClass: 'value13Bold',
      isVisible: 'true',
      type: 'text',
      position: 17,
      colSize: '2',
      width: 200,
      sortOrder: 'N',
      hasColumnClick: true,
      alternateFieldName: ['Treasury Approver Ageing Days']
    }
  ]
  udrfItemStatusColor = [
    {
      status: 'Submitted',
      color: '#ffb142',
    },
    {
      status: 'Approved',
      color: '#badc58',
    },
    {
      status: 'Closed',
      color: '#009432',
    },
    {
      status: 'Draft',
      color: '#9980fa',
    },
    {
      status: 'Rejected',
      color: '#cf0001 ',
    },
    {
      status: 'Verified',
      color: '#9f2825',
    },
    {
      status: 'Knocked-Off',
      color: '#e2e2e2',
    },
    {
      status: 'KO - Approved',
      color: '#5ce88c',
    },
    {
      status: 'KO - Submitted',
      color: '#e58e26',
    },
    {
      status: 'Payed',
      color: '#079992',
    }
  ];

  durationRanges: any = [];

  selectedCard: any = [];

  categorisedDataTypeArray = [
    {
      categoryType: 'Status Cards',
      categoryCardCodes: [],
      categoryCards: [],
    },
  ];

  expenseItem: any;

  cardFilter: any = "";

  expenseDurationRanges: any = [];

  misFlags: any;

  constructor(private expensereport: ExpenseReportService, private dialog: MatDialog, private spinner: NgxSpinnerService,
    private utilityService: UtilityService, private authService: LoginService,
    public udrfService: UdrfService, private sharedLazyLoadedComponentsService: SharedLazyLoadedComponentsService, private $router: Router,
    private excelService: JsonToExcelService, private errorService: ErrorService, private reportsService: ReportsService, private _snackBar: MatSnackBar) { }

  ngOnInit(): void {

    this.getFormFieldConfig();
    this.reportsService.getMisFlags().subscribe(res => {

      if (res["messType"] == "S") {

        this.misFlags = res["misFlags"];

        //SR UDRF-----------------------------------------------------------------------------
        this.udrfService.udrfBodyData = [];

        this.currentUserOId = this.authService.getProfile().profile.oid;

        console.log(this.authService.getProfile());

        this.ExpenseItemDataCurrentIndex = 0;

        this.durationRanges = [
          {
            checkboxId: 'CDCRD',
            checkboxName: 'Current Year',
            checkboxStartValue: moment().startOf('year').format("YYYY-MM-DD"),
            checkboxEndValue: moment().endOf('year').format("YYYY-MM-DD"),
            isCheckboxDefaultSelected: true,
          },
          {
            checkboxId: 'CDCRD4',
            checkboxName: 'Next Month',
            checkboxStartValue: this.utilityService.getFormattedDate(
              moment().add(1, 'month').startOf('month'),
              moment(moment().add(1, 'month').startOf('month')).date,
              15,
              0,
              0,
              0
            ),
            checkboxEndValue: this.utilityService.getFormattedDate(
              moment().add(1, 'month').endOf('month'),
              moment(moment().add(1, 'month').endOf('month')).date,
              15,
              0,
              0,
              0
            ),
            isCheckboxDefaultSelected: false,
          },
          {
            checkboxId: 'CDCRD3',
            checkboxName: 'This Month',
            checkboxStartValue: this.utilityService.getFormattedDate(
              moment().startOf('month'),
              moment(moment().startOf('month')).date,
              15,
              0,
              0,
              0
            ),
            checkboxEndValue: this.utilityService.getFormattedDate(
              moment().endOf('month'),
              moment(moment().endOf('month')).date,
              15,
              0,
              0,
              0
            ),
            isCheckboxDefaultSelected: false,
          },
          {
            checkboxId: 'CDCRD2',
            checkboxName: 'This Week',
            checkboxStartValue: this.utilityService.getFormattedDate(
              moment().startOf('week'),
              moment(moment().startOf('week')).date,
              15,
              0,
              0,
              0
            ),
            checkboxEndValue: this.utilityService.getFormattedDate(
              moment().endOf('week'),
              moment(moment().endOf('week')).date,
              15,
              0,
              0,
              0
            ),
            isCheckboxDefaultSelected: false,
          },
          {
            checkboxId: 'CDCRD5',
            checkboxName: 'Upcoming 3 Months',
            checkboxStartValue: this.utilityService.getFormattedDate(
              moment().startOf('month'),
              moment(moment().startOf('month')).date,
              15,
              0,
              0,
              0
            ),
            checkboxEndValue: this.utilityService.getFormattedDate(
              moment().add(2, 'month').endOf('month'),
              moment(moment().add(2, 'month').endOf('month')).date,
              15,
              0,
              0,
              0
            ),
            isCheckboxDefaultSelected: false,
          },
          {
            checkboxId: 'CDCRD6',
            checkboxName: 'Full',
            checkboxStartValue: moment("2020-01-01").format("YYYY-MM-DD"),
            checkboxEndValue: moment().format("YYYY-MM-DD"),
            isCheckboxDefaultSelected: false,
          }
        ]

        this.udrfService.udrfFunctions.constructCustomRangeData(
          11,
          'date',
          this.durationRanges
        );

        let obvMargins = [
          {
            checkboxId: "OBVCRD1",
            checkboxName: "No Limit",
            checkboxStartValue: 0,
            checkboxEndValue: 99999,
            isCheckboxDefaultSelected: true
          },
          {
            checkboxId: "OBVCRD2",
            checkboxName: "> 10 K",
            checkboxStartValue: 10,
            checkboxEndValue: 99999,
            isCheckboxDefaultSelected: false
          },
          {
            checkboxId: "OBVCRD3",
            checkboxName: "> 50 K",
            checkboxStartValue: 50,
            checkboxEndValue: 99999,
            isCheckboxDefaultSelected: false
          },
          {
            checkboxId: "OBVCRD4",
            checkboxName: "> 1 L",
            checkboxStartValue: 100,
            checkboxEndValue: 99999,
            isCheckboxDefaultSelected: false
          },
          {
            checkboxId: "OBVCRD5",
            checkboxName: "> 10 L",
            checkboxStartValue: 1000,
            checkboxEndValue: 99999,
            isCheckboxDefaultSelected: false
          }
        ]

        this.expenseDurationRanges = [
          {
            checkboxId: 'CDCRD',
            checkboxName: 'Current Year',
            checkboxStartValue: moment().startOf('year').format("YYYY-MM-DD"),
            checkboxEndValue: moment().endOf('year').format("YYYY-MM-DD"),
            isCheckboxDefaultSelected: false,
          },
          {
            checkboxId: 'CDCRD3',
            checkboxName: 'This Month',
            checkboxStartValue: this.utilityService.getFormattedDate(
              moment().startOf('month'),
              moment(moment().startOf('month')).date,
              15,
              0,
              0,
              0
            ),
            checkboxEndValue: this.utilityService.getFormattedDate(
              moment().endOf('month'),
              moment(moment().endOf('month')).date,
              15,
              0,
              0,
              0
            ),
            isCheckboxDefaultSelected: false,
          },
          {
            checkboxId: 'CDCRD6',
            checkboxName: 'Previous Month',
            checkboxStartValue: this.utilityService.getFormattedDate(
              moment().add(-1, 'month').startOf('month'),
              moment(moment().add(-1, 'month').startOf('month')).date,
              15,
              0,
              0,
              0
            ),
            checkboxEndValue: this.utilityService.getFormattedDate(
              moment().add(-1, 'month').endOf('month'),
              moment(moment().add(-1, 'month').endOf('month')).date,
              15,
              0,
              0,
              0
            ),
            isCheckboxDefaultSelected: false,
          },
          {
            checkboxId: 'CDCRD6',
            checkboxName: 'Last 3 Month',
            checkboxStartValue: this.utilityService.getFormattedDate(
              moment().add(-2, 'month').startOf('month'),
              moment(moment().add(-3, 'month').startOf('month')).date,
              15,
              0,
              0,
              0
            ),
            checkboxEndValue: this.utilityService.getFormattedDate(
              moment().startOf('month'),
              moment(moment().startOf('month')).date,
              15,
              0,
              0,
              0
            ),
            isCheckboxDefaultSelected: false,
          },

        ];

        this.udrfService.udrfFunctions.constructCustomRangeData(
          15,
          'date',
          this.expenseDurationRanges
        );


        this.udrfService.udrfFunctions.constructCustomRangeData(12, "value", obvMargins);

        this.udrfService.udrfData.applicationId = this.applicationId;
        this.udrfService.udrfUiData.showNewReleasesButton = true;
        this.udrfService.udrfUiData.horizontalScroll = true;
        this.udrfService.udrfUiData.showItemDataCount = true;
        this.udrfService.udrfUiData.itemDataType = "";
        this.udrfService.udrfUiData.totalItemDataCount = 0;
        this.udrfService.udrfUiData.showSearchBar = true;
        this.udrfService.udrfUiData.showActionButtons = true;
        this.udrfService.udrfUiData.showUdrfModalButton = true;
        this.udrfService.udrfUiData.showColumnConfigButton = true;
        this.udrfService.udrfUiData.showReportDownloadButton = true;
        this.udrfService.udrfUiData.udrfBodyColumns = this.udrfBodyColums;
        this.udrfService.udrfUiData.showReportDownloadButton = true;
        this.udrfService.udrfUiData.resolveVisibleSummaryCards = this.resolveVisibleSummaryCards.bind(this);
        this.udrfService.udrfUiData.summaryCardsSelected = this.dataTypeCardSelected.bind(this);
        this.udrfService.udrfUiData.summaryCardsItem = {};
        this.udrfService.udrfUiData.showSettingsModalButton = false;
        this.udrfService.udrfUiData.itemDataScrollDown = this.expenseReportScrollDown.bind(this);
        this.udrfService.udrfUiData.callInlineEditApi = this.callInlineEditApi.bind(this);
        this.udrfService.udrfUiData.inlineEditData = {};
        this.udrfService.udrfUiData.inlineEditDropDownMasterDatas = {};
        this.udrfService.udrfUiData.summaryCards = this.dataTypeArray;
        this.udrfService.udrfUiData.udrfVisibleBodyColumns = this.udrfService.udrfUiData.udrfVisibleBodyColumns;
        this.udrfService.udrfUiData.udrfInvisibleBodyColumns = this.udrfService.udrfUiData.udrfInvisibleBodyColumns;
        this.udrfService.udrfUiData.selectedCard = this.selectedCard;
        this.udrfService.udrfUiData.variant = 0;
        this.udrfService.udrfUiData.itemHasQuickCta = false;
        this.udrfService.udrfUiData.itemHasComments = true;
        this.udrfService.udrfUiData.quickCTAInput = {};
        this.udrfService.udrfUiData.commentsInput = {};
        this.udrfService.udrfUiData.commentsContext = {};
        this.udrfService.udrfUiData.categorisedSummaryCards = this.categorisedDataTypeArray;
        this.udrfService.udrfUiData.openComments = this.openComments.bind(this)
        this.udrfService.udrfUiData.udrfItemStatusColor = this.udrfItemStatusColor;
        this.udrfService.udrfUiData.columnClick = this.getExpenseItem.bind(this);
        this.udrfService.udrfUiData.downloadItemDataReport = this.downloadReport.bind(this);
        this.udrfService.udrfUiData.isHeaderSort = true;
        this.udrfService.getAppUdrfConfig(this.applicationId, this.initReport.bind(this));
        this.udrfService.getNotifyReleasesUDRF();
        this.udrfService.udrfUiData.expenseAttachmentsDownload = this.expenseAttachmentsDownload.bind(this);

        this.udrfService.udrfUiData.itemHasAttachments = true;
        this.udrfService.udrfUiData.itemHasAttachmentsDownload = true;
        this.udrfService.udrfUiData.attachmentsInput = {};
        this.udrfService.udrfUiData.openAttachments = this.openExpenseAttachments.bind(this)


        //------------------------------------------------------------------------

      }

      else {

        let errReportingTeams = "KEBS";

        this.utilityService.showErrorMessage(res, errReportingTeams);

      }

    }, err => {

      let errReportingTeams = "KEBS";

      this.utilityService.showErrorMessage(err, errReportingTeams);

    });

  }

  expenseAttachmentsDownload(expenseData){
    this.udrfService.udrfUiData.showAttachmentsLoader = true;
    const objectIndex = this.udrfService.udrfBodyData.findIndex(item => item.id === expenseData?.id);
    console.log("objectIndex")
    console.log(objectIndex)

    // If found, update the showAttachmentsLoader property
    if (objectIndex !== -1) {
      this.udrfService.udrfBodyData[objectIndex].showAttachmentsLoader = true;
    } else {
      console.error('Object with the specified ID not found.');
    }
    this.expensereport.downloadExpenseAttachments(expenseData?.id).subscribe(blob => {
      this.udrfService.udrfBodyData[objectIndex].showAttachmentsLoader = false;
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `Expense ID-${expenseData?.id}.zip`; // Specify the file name
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url); // Clean up the URL object
    }, error => {
      this.udrfService.udrfBodyData[objectIndex].showAttachmentsLoader = false;
      this._snackBar.open(`No Attachments Found for Expense ID - ${expenseData?.id} !`,"Dismiss",{duration:3000})
    });
  }

  openExpenseAttachments(){
    let checkForValueInContextId = false;
    let columnData = this.udrfService.udrfUiData.attachmentsInput;
    let contextIds = columnData["context_id"];
    
    if(contextIds!=null){
      contextIds.every(function (value) {
        if (value != null) {
          checkForValueInContextId = true;
        }
      })
    }

    if(checkForValueInContextId != false){
      const openExpenseAttachmentViewerComponent = this.dialog.open(AttachmentViewerComponent, {
        height: '50%',
        width: '20%',
        data: { modalParams: columnData }
      });
    }
    else{
      this._snackBar.open(`No Attachments Found for Expense ID - ${columnData['id']} !`,"Dismiss",{duration:3000})
    }
  }

  initReport() {
    this._onAppApiCalled.next();
    this.ExpenseItemDataCurrentIndex = 0;
    this.ExpenseList = [];
    this.associateData = [];
    this.isCardClicked = false;
    this.cardClicked = "";
    this.cardIterator = [];
    this.associateData = [];
    this.udrfService.udrfBodyData = [];
    this.cardFilter = "";
    for (let i = 0; i < this.dataTypeArray.length; i++) {
      this.dataTypeArray[i].isActive = false;
    }
    this.udrfService.udrfUiData.resolveColumnConfig();
    this.initExpenseListSR();
  }

  //UDRF Format Conv -------------------------------------------------------------------

  filterConfig: any;
  async initExpenseListSR() {
    this.spinner.show();

    let mainFilterArray = JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray));

    let expenseStartDate, expenseEndDate;

    let tempMainFilterArray = _.filter(this.udrfService.udrfData.filterTypeArray, { filterId: 5 });
    if(tempMainFilterArray.length > 0 && tempMainFilterArray[0].multiOptionSelectSearchValues.length == 0){
      tempMainFilterArray[0].multiOptionSelectSearchValues[0] = this.authService.getProfile().profile.aid;
    }
    else{
      tempMainFilterArray[0].multiOptionSelectSearchValues = tempMainFilterArray[0].multiOptionSelectSearchValues.filter(item => item !== "0");
    }

    if (mainFilterArray.length > 0) {
      for (let items of mainFilterArray) {
        if (items.filterId == 15 && !items.isCustomButtonActivated) {
          let dateValues = this.seperateMultipleDate(items.checkboxValues);
          expenseStartDate = moment(dateValues.startDate).format("YYYY-MM-DD");
          expenseEndDate = moment(dateValues.endDate).format("YYYY-MM-DD");
        }
        else if (items.filterId == 15 && items.isCustomButtonActivated) {
          expenseStartDate = moment(items.customButtonValueStart).format("YYYY-MM-DD");
          expenseEndDate = moment(items.customButtonValueEnd).format("YYYY-MM-DD");
        }
      }
    }
    if (_.filter(mainFilterArray, { filterId: 5 }).length == 0) {
      mainFilterArray.push(tempMainFilterArray[0]);
    }
    else {
      mainFilterArray = mainFilterArray
    }


    let filterConfig = {
      startIndex: this.ExpenseItemDataCurrentIndex,
      startDate: moment(this.udrfService.udrfData.mainApiDateRangeStart).format("YYYY-MM-DD"),
      endDate: moment(this.udrfService.udrfData.mainApiDateRangeEnd).format("YYYY-MM-DD"),
      mainFilterArray: mainFilterArray,
      txTableDetails: this.udrfService.udrfData.txTableDetails,
      mainSearchParameter: this.udrfService.udrfData.mainSearchParameter,
      searchTableDetails: this.udrfService.udrfData.searchTableDetails,
      viewType: this.viewType,
      cardFilter: this.cardFilter,
      expenseDurationStartDate: expenseStartDate,
      expenseDurationEndDate: expenseEndDate
    }

    this.filterConfig = filterConfig;
    this.expensereport.getExpenseReportSR(filterConfig).pipe(takeUntil(this._onDestroy)).pipe(takeUntil(this._onAppApiCalled))
      .subscribe(async res => {

        if (res['data'] && res['messType'] == 'S') {
          await this.getTotalCount(filterConfig);
          this.udrfService.udrfBodyData = this.udrfService.udrfBodyData.concat(res['data']);

          if (!this.cardClicked) {
            this.initExpenseCardSR();
          }

        }
        else {
          this.udrfService.udrfBodyData = this.udrfService.udrfBodyData.concat([]);

          this.udrfService.udrfData.noItemDataFound = true;
        }

        this.udrfService.udrfData.isItemDataLoading = false;

      },
        (err) => {
          this.errorService.userErrorAlert((err && err.code) ? err.code : (err && err.error) ? err.error.code : 'NIL', "Error in Report ", (err && err.params) ? err.params : (err && err.error) ? err.error.params : {});
        });



  }
  initExpenseCardSR() {
    let mainFilterArray = JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray));

    let tempMainFilterArray = _.filter(this.udrfService.udrfData.filterTypeArray, { filterId: 5 });
    if(tempMainFilterArray.length > 0 && tempMainFilterArray[0].multiOptionSelectSearchValues.length == 0){
      tempMainFilterArray[0].multiOptionSelectSearchValues[0] = this.authService.getProfile().profile.aid;
    }
    else{
      tempMainFilterArray[0].multiOptionSelectSearchValues = tempMainFilterArray[0].multiOptionSelectSearchValues.filter(item => item !== "0");
    }

    if (_.filter(mainFilterArray, { filterId: 5 }).length == 0) {
      mainFilterArray.push(tempMainFilterArray[0]);
    }
    else {
      mainFilterArray = mainFilterArray
    }

    let filterConfig = {
      startIndex: this.ExpenseItemDataCurrentIndex,
      startDate: this.udrfService.udrfData.mainApiDateRangeStart,
      endDate: this.udrfService.udrfData.mainApiDateRangeEnd,
      mainFilterArray: mainFilterArray,
      txTableDetails: this.udrfService.udrfData.txTableDetails,
      mainSearchParameter: this.udrfService.udrfData.mainSearchParameter,
      searchTableDetails: this.udrfService.udrfData.searchTableDetails,
      viewType: this.viewType
    }

    this.expensereport.getExpenseCardSR(filterConfig)
      .pipe(takeUntil(this._onDestroy)).pipe(takeUntil(this._onAppApiCalled))
      .subscribe(async res => {
        if (res['data']) {

          let metaData = res['data'][0];
          this.dataTypeArray = [
            // {
            //   dataType: 'Total amount paid',
            //   dataTypeCode: 'TAP',
            //   dataTypeValue: metaData['total_amount_paid'],
            //   isActive: false,
            //   isVisible: true,
            //   isDisabled: false,
            // },
            // {
            //   dataType: 'Total amount pending',
            //   dataTypeCode: 'TAPE',
            //   dataTypeValue: metaData['amount_pending'],
            //   isActive: false,
            //   isVisible: true,
            //   isDisabled: false,
            // },
            {
              dataType: 'Total Claims Paid ',
              dataTypeCode: 'TCP',
              dataTypeValue: metaData['total_amount_paid'] == null ? "0 Cr" : ((metaData['total_amount_paid'] / 10000000).toFixed(2) + " Cr"),
              dataTypeValueOriginal: metaData['total_amount_paid'] == null ? 0 : this.getDisplayValueInInrFormat(metaData['total_amount_paid']),
              isActive: false,
              isVisible: true,
              isDisabled: false,
              cardType: 'status',
              isToolTip: true,
            },
            {
              dataType: 'Total Claims Pending',
              dataTypeCode: 'TCPE',
              dataTypeValue: metaData['amount_pending'] == null ? "0 Cr" : ((metaData['amount_pending'] / 10000000).toFixed(2) + " Cr"),
              dataTypeValueOriginal: metaData['amount_pending'] == null ? 0 : this.getDisplayValueInInrFormat(metaData['amount_pending']),
              isActive: false,
              isVisible: true,
              isDisabled: false,
              cardType: 'status',
              isToolTip: true,
            },
            {
              dataType: 'Total Advance Paid',
              dataTypeCode: 'TADP',
              dataTypeValue: metaData['total_advance_paid'] == null ? "0 Cr" : ((metaData['total_advance_paid'] / 10000000).toFixed(2) + " Cr"),
              dataTypeValueOriginal: metaData['total_advance_paid'] == null ? 0 : this.getDisplayValueInInrFormat(metaData['total_advance_paid']),
              isActive: false,
              isVisible: true,
              isDisabled: false,
              cardType: 'status',
              isToolTip: true,
            },
            {
              dataType: 'Advance yet to KO',
              dataTypeCode: 'TAKO',
              dataTypeValue: "0 INR",
              dataTypeValueOriginal: 0,
              isActive: false,
              isVisible: true,
              isDisabled: false,
              cardType: 'status',
              isToolTip: true,
            },
            {
              dataType: 'No Of Request Pending',
              dataTypeCode: 'TNRP',
              dataTypeValue: metaData['no_of_requests_pending'],
              isActive: false,
              isVisible: true,
              isDisabled: false,
              cardType: 'status',
            },
            {
              dataType: 'Claim Amount Pending > 30 days',
              dataTypeCode: 'CAP',
              dataTypeValue: metaData['claim_amt'] == null ? "0 Cr" : ((metaData['claim_amt'] / 10000000).toFixed(2) + " Cr"),
              dataTypeValueOriginal: metaData['claim_amt'] == null ? 0 : this.getDisplayValueInInrFormat(metaData['claim_amt']),
              isActive: false,
              isVisible: true,
              isDisabled: false,
              cardType: 'status',
              isToolTip: true,
            }
          ]
          // this.initExpenseListSR();
          this.udrfService.udrfUiData.summaryCards = this.dataTypeArray;
        }

        this.spinner.hide();


      },
        (err) => {
          this.errorService.userErrorAlert((err && err.code) ? err.code : (err && err.error) ? err.error.code : 'NIL', "Error in Report", (err && err.params) ? err.params : (err && err.error) ? err.error.params : {});
        });


  }

  async expenseReportScrollDown() {
    if (!this.udrfService.udrfData.noItemDataFound) {
      {
        if (!this.udrfService.udrfData.isItemDataLoading) {

          this.ExpenseItemDataCurrentIndex += this.udrfService.udrfData.defaultRecordsPerFetch;

          this.udrfService.udrfData.isItemDataLoading = true;

          await this.initExpenseListSR();
        }
      }
    }

  }

  callInlineEditApi() {

  }

  openCTA(item) {
    let quickCtaInput = {
      applicationId: 65,
      objectId: item.id,
      objectType: 'E',
      originatorData: null
    }
    // this.salesGovernanceService.createQuickCTA(quickCtaInput);
    this.sharedLazyLoadedComponentsService.openQuickCTAModal(quickCtaInput, this.dialog);
  }
  async openComments() {
    let item = this.udrfService.udrfUiData.openCommentsData["data"]
    let msg = {
      inputData: {
        application_id: 65,
        application_name: "Expenses",
        title: "Expense - #EX" + item.id,
        unique_id_1: item.id,
        unique_id_2: "",
      },

      context: {
        Role: "Claimer",
        "Expense type":
          item.expense_type == "C" ? "Claim" : "Advance",
        // Description: JSON.parse(
        //   this.expenseItem["description_of_items"]
        // ).toString(),
        "Expense Status": item.status_name,
        Department: item.department,
      },
      commentBoxHeight: '100vh',
      commentBoxScrollHeight: '80%'
    }
    const { ChatCommentContextModalComponent } = await import('src/app/modules/shared-lazy-loaded-components/chat-comment-context-modal/chat-comment-context-modal.component')
    const openChatCommentContextModalComponent = this.dialog.open(ChatCommentContextModalComponent, {
      height: '100%',
      width: '50%',
      position: { right: '0px' },
      data: { modalParams: msg }
    });
  }
  getExpenseItem() {
    if(this.udrfService.udrfUiData.currentColumnDetails['item'] != "people_involved"){
    let oid = this.udrfService.udrfUiData.onColumnClickItem['created_by_oid'];
    let id = this.udrfService.udrfUiData.onColumnClickItem['id'];
    this.expensereport.getExpenseItem(oid, 0, 10000).pipe(takeUntil(this._onDestroy)).subscribe(async res => {
      if (res) {
        this.expenseItem = res;
        this.expenseItem = this.expenseItem.filter((item) => item.expense_header_id === id)
        this.saveExpenseDetailToSession();
      }

    },
      (err) => {
        this.showErrorMessage(err);
      });
    }
  }
  saveExpenseDetailToSession = () => {
    sessionStorage.setItem("expenseDetail", JSON.stringify(this.expenseItem[0]));
    this.goToMainExpense();
  };
  goToMainExpense() {
    let role = this.expenseItem[0].role;
    let status = this.expenseItem[0].status;

    let expenseType = this.expenseItem[0].expense_type;
    let eHeaderId = this.expenseItem[0].expense_header_id;

    //Claim
    if (expenseType == 'C') {
      if (status == 'Draft' && role == "Claimer") {
        //this.openClaimForm();

      }
      else {
        if (role == "Claimer") {
          window.open("/main/expenses/expenseDetail/" +
            this.expenseItem[0].expense_header_id +
            "/claims", '_blank');
          return;
        }
        else if (role == "Approver") {
          window.open("/main/expenses/expenseDetail/" +
            this.expenseItem[0].expense_header_id +
            "/approvals", '_blank')
          return;
        } else if (role == "Treasurer") {
          window.open("/main/expenses/expenseDetail/" +
            this.expenseItem[0].expense_header_id +
            "/verifications/payment", '_blank');
          return;
        }
        else if (role == "Admin") {
          window.open("/main/expenses/expenseDetail/" +
            this.expenseItem[0].expense_header_id +
            "/verifications/payment", '_blank');
          return;
        }
      }
    }
    else if (expenseType == 'A') {
      window.open("/main/expenses/expenseDetail/advance/" + this.expenseItem[0].expense_header_id + "/adminView", '_blank');

    }

  }


  getDisplayValue(displayValue, displayValueOriginal) {

    return this.utilityService.getDisplayValue(displayValue, displayValueOriginal);

  }

  getDisplayValueInInrFormat(displayValue) {

    return this.utilityService.getDisplayValueInInrFormat(displayValue);

  }


  async resolveVisibleSummaryCards() {

    for (let summaryCardsItem of this.udrfService.udrfUiData.summaryCards) {

      let isVisible;

      if (this.udrfService.udrfUiData.selectedCard != null) {

        isVisible = _.contains(this.udrfService.udrfUiData.selectedCard, summaryCardsItem.dataTypeCode);
        summaryCardsItem.isVisible = isVisible;

      }

      if (this.udrfService.udrfData.udrfSummaryCardCodes != null) {

        isVisible = _.contains(this.udrfService.udrfData.udrfSummaryCardCodes, summaryCardsItem.dataTypeCode);
        summaryCardsItem.isVisible = isVisible;

      }

    }

  }

  dataTypeCardSelected() {

    this.udrfService.udrfData.isItemDataLoading = true;
    this.udrfService.udrfData.noItemDataFound = false;

    let dataTypeArrayItem = this.udrfService.udrfUiData.summaryCardsItem;

    for (let i = 0; i < this.dataTypeArray.length; i++)
      if (dataTypeArrayItem["dataTypeCode"] == this.dataTypeArray[i].dataTypeCode) {

        this.dataTypeArray[i].isActive = true;
        dataTypeArrayItem["isActive"] = true;
        this.udrfService.udrfUiData.cardClicked = true;
        this.cardFilter = dataTypeArrayItem;

      }
      else {

        let summaryCard = _.where(this.udrfService.udrfUiData.summaryCards, { dataTypeCode: this.dataTypeArray[i].dataTypeCode });

        if (summaryCard.length > 0)
          summaryCard[0].isActive = false;

        this.dataTypeArray[i].isActive = false;

        this.udrfService.udrfUiData.cardClicked = false;
        this.cardFilter = "";

      }

    this.isCardClicked = true;

    this.cardClicked = dataTypeArrayItem["dataType"];

    this.udrfService.udrfBodyData = [];

    this.ExpenseItemDataCurrentIndex = 0;

    this.initExpenseListSR();

  }

  showErrorMessage(err) {
    let errReportingTeams = "KEBS";
    this.utilityService.showErrorMessage(err, errReportingTeams);
  }

  downloadReport() {
  let mainFilterArray = JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray));

    let expenseStartDate, expenseEndDate;

    let tempMainFilterArray = _.filter(this.udrfService.udrfData.filterTypeArray, { filterId: 5 });
    if(tempMainFilterArray.length > 0 && tempMainFilterArray[0].multiOptionSelectSearchValues.length == 0){
      tempMainFilterArray[0].multiOptionSelectSearchValues[0] = this.authService.getProfile().profile.aid;
    }
    else{
      tempMainFilterArray[0].multiOptionSelectSearchValues = tempMainFilterArray[0].multiOptionSelectSearchValues.filter(item => item !== "0");
    }

    if (mainFilterArray.length > 0) {
      for (let items of mainFilterArray) {
        if (items.filterId == 15 && !items.isCustomButtonActivated) {
          let dateValues = this.seperateMultipleDate(items.checkboxValues);
          expenseStartDate = moment(dateValues.startDate).format("YYYY-MM-DD");
          expenseEndDate = moment(dateValues.endDate).format("YYYY-MM-DD");
        }
        else if (items.filterId == 15 && items.isCustomButtonActivated) {
          expenseStartDate = moment(items.customButtonValueStart).format("YYYY-MM-DD");
          expenseEndDate = moment(items.customButtonValueEnd).format("YYYY-MM-DD");
        }
      }
    }
    if (_.filter(mainFilterArray, { filterId: 5 }).length == 0) {
      mainFilterArray.push(tempMainFilterArray[0]);
    }
    else {
      mainFilterArray = mainFilterArray
    }


    let filterConfig = {
      startIndex: this.ExpenseItemDataCurrentIndex,
      startDate: moment(this.udrfService.udrfData.mainApiDateRangeStart).format("YYYY-MM-DD"),
      endDate: moment(this.udrfService.udrfData.mainApiDateRangeEnd).format("YYYY-MM-DD"),
      mainFilterArray: mainFilterArray,
      txTableDetails: this.udrfService.udrfData.txTableDetails,
      mainSearchParameter: this.udrfService.udrfData.mainSearchParameter,
      searchTableDetails: this.udrfService.udrfData.searchTableDetails,
      viewType: this.viewType,
      cardFilter: this.cardFilter,
      expenseDurationStartDate: expenseStartDate,
      expenseDurationEndDate: expenseEndDate
    }

    this.expensereport.getExpenseReportDownloadSR(this.filterConfig).pipe(takeUntil(this._onDestroy)).pipe(takeUntil(this._onAppApiCalled))
    .subscribe(async res => {
      this.udrfService.udrfUiData.isReportDownloading = false;
      let dateFieldArray=[]
      if (res['data'] && res['messType'] == 'S') {
        res['data']=await this.returnVisibleBodyColumnsForDownload(res['data']);
        for (let result of res['data']) {
          for (let [key, value] of Object.entries(result)) {
            if (typeof value == "object") {
              if (value != null && value != undefined) {
                try {
                  if (value['type'] == "date") {

                    const fieldExists = dateFieldArray.find(val => val['fieldKey'] == key);

                    if (!fieldExists)
                      dateFieldArray.push({
                        fieldKey: key,
                        fieldFormat: this.dateFormat
                      });

                    result[key] = value['value'] ? moment(value['value']).utc().format("YYYY/MM/DD") : null;

                    // dateFieldArray.push({fieldKey:key,fieldFormat:this.dateFormat})

                    // result[key] = moment(value['date']).utc().format("YYYY-MM-DD")

                  }
                }
                catch (err) {
                  console.log(err)
                }
              }
            }
          }

        }
        
        this.excelService.exportAsExcelFile(
          res['data'],
          "Expense-Report",  [], null,dateFieldArray
        );
      }
      else {
        this.utilityService.showMessage('Report Download Failed', 'Dismiss', 3000);
      }

      this.udrfService.udrfData.isItemDataLoading = false;

    },
      (err) => {
        this.udrfService.udrfUiData.isReportDownloading = false;
        this.errorService.userErrorAlert((err && err.code) ? err.code : (err && err.error) ? err.error.code : 'NIL', "Error in Downloading Report", (err && err.params) ? err.params : (err && err.error) ? err.error.params : {});
      });

}

    returnVisibleBodyColumnsForDownload(data) {

      let udrfVisibleColumns = [];
  
      for (const udrfBodyColumnsItem of this.udrfService.udrfUiData.udrfVisibleBodyColumns){
        
        if (udrfBodyColumnsItem.isVisible != null && (udrfBodyColumnsItem.isVisible == "true" || udrfBodyColumnsItem.isVisible == true)) {
  
          if (udrfBodyColumnsItem.alternateFieldName && udrfBodyColumnsItem.alternateFieldName.length>0){
            udrfVisibleColumns = udrfVisibleColumns.concat(udrfBodyColumnsItem.alternateFieldName);
          }
        }
      }
  
      udrfVisibleColumns = _.uniq(udrfVisibleColumns);
  
      for (let excelDataItem of data){
        for (const excelDataKey of Object.keys(excelDataItem)){
          if (!_.contains(udrfVisibleColumns, excelDataKey)){
            delete excelDataItem[excelDataKey];
          }
        }
      }
  
      return data;
  
    }

  seperateMultipleDate(DateArray) {
    let dateData;

    for (let dateTypeArrayItem of DateArray) {
      if (dateTypeArrayItem.isCheckboxSelected) {
        dateData =
        {
          startDate: dateTypeArrayItem.checkboxStartValue,
          endDate: dateTypeArrayItem.checkboxEndValue,
        }
      }
    }
    return dateData;
  }

 FormFieldConfig(){
    return new Promise((resolve, reject) => {
   this.expensereport
    .getFormFieldConfig()
    .subscribe(
      (res) => {
      if(res['data'])
      {
        resolve(res['data'])
      }  
      },
      (err) => {
        console.error(err);
        reject(err)
      }
    );
  })
  }

  async getFormFieldConfig(){
    this.formFieldConfig = await this.FormFieldConfig();
    if(this.formFieldConfig){
          this.formFieldConfig.forEach((element) => {
          this.fieldConfig[element.field_key] = element
        });
        _.each(this.udrfBodyColums, (l) =>{
          if(l.item=='cost_centre'){
            l.header=this.fieldConfig?.costCenterCode?.field_label ? this.fieldConfig.costCenterCode.field_label:'Cost Center'
            console.log(l.header)
          }
          if(l.item=='cc_approved_on'){
            l.header=this.fieldConfig?.costCenterCode?.field_label ? `${this.fieldConfig.costCenterCode.field_label} Action On`:'Cost Center Action On'
            console.log(l.header)
          }
        })
      }
}



  ngOnDestroy() {

    this._onDestroy.next();
    this._onDestroy.complete();
    this.udrfService.resetUdrfData();
  }
  /**
   * @param  {} filterConfig
   * @description To Get the Count of the Expense Report
   */
  async getTotalCount(filterConfig) {

    this.expensereport.getExpenseReportDownloadSR(filterConfig).pipe(takeUntil(this._onDestroy)).pipe(takeUntil(this._onAppApiCalled))
      .subscribe(async res => {
        this.udrfService.udrfUiData.isReportDownloading = false;
        if (res['data'] && res['messType'] == 'S') {
          this.udrfService.udrfUiData.totalItemDataCount = res['len'];

        }
      },
        (err) => {
          this.udrfService.udrfUiData.isReportDownloading = false;
          this.errorService.userErrorAlert((err && err.code) ? err.code : (err && err.error) ? err.error.code : 'NIL', "Error in Downloading Report", (err && err.params) ? err.params : (err && err.error) ? err.error.params : {});
        });

  }

}
