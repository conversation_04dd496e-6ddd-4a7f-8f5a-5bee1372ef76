import { animate, state, style, transition, trigger } from '@angular/animations';
import { Component, OnInit, Inject, ViewChild } from '@angular/core';
import { DOCUMENT } from '@angular/common';
import { FormControl } from "@angular/forms";
import { MatDialog } from "@angular/material/dialog";
import * as moment from 'moment';
import * as _ from 'underscore';
import { Subject, Subscription } from "rxjs";
import { takeUntil } from "rxjs/operators";
import { Moment } from 'moment';
import { MatDatepicker } from '@angular/material/datepicker';
import { JsonToExcelService } from 'src/app/services/excel/json-to-excel.service';

import { GovernanceReportService } from "../../services/governance-report.service";
import { GovernanceReportSubService } from "../../services/governance-report-sub.service";
import { ReportsService } from "../../../../services/reports.service";
import { ArUbrService } from "../../../ar-ubr/services/ar-ubr.service";
import { SharedLazyLoadedComponentsService } from "src/app/modules/shared-lazy-loaded-components/services/shared-lazy-loaded-components.service";
import { DetailedViewService } from "src/app/modules/reports/features/pmo-dashboard-version1/features/detailed-view/services/detailed-view.service";

import { RolesService } from "src/app/services/acl/roles.service";
import { LoginService } from "src/app/services/login/login.service";
import { UtilityService } from 'src/app/services/utility/utility.service';
import { ArithmeticEvalService } from 'src/app/services/arithmetic-eval/arithmetic-eval.service';

import { MomentDateAdapter, MAT_MOMENT_DATE_ADAPTER_OPTIONS } from '@angular/material-moment-adapter';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core';
import { UdrfService } from 'src/app/services/udrf/udrf.service';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { CdkOverlayOrigin } from '@angular/cdk/overlay';

export const MONTH_YEAR_DATE_FORMAT = {
  parse: {
    dateInput: 'MMMM YYYY',
  },
  display: {
    dateInput: 'MMMM YYYY',
    monthYearLabel: 'MMMM YYYY',
    dateA11yLabel: 'LL',
    monthYearA11yLabel: 'MMMM YYYY',
  },
};

@Component({
  selector: 'app-governance-report-landing-page',
  templateUrl: './governance-report-landing-page.component.html',
  styleUrls: ['./governance-report-landing-page.component.scss'],
  animations: [
    trigger("slideInOut", [
      state("in", style({ height: "*", overflow: "hidden" })),
      state("out", style({ height: 0, overflow: "hidden" })),
      transition("* => in", [
        style({ height: 0 }),
        animate(250, style({ height: "*" }))
      ]),
      transition("in=> *", [
        style({ height: "*" }),
        animate(250, style({ height: 0 }))
      ])
    ])
  ],
  providers: [
    {
      provide: DateAdapter,
      useClass: MomentDateAdapter,
      deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS]
    },
    { provide: MAT_DATE_FORMATS, useValue: MONTH_YEAR_DATE_FORMAT },
  ]
})

export class GovernanceReportLandingPageComponent implements OnInit {

  isNgrPivotVisible = false;

  colorCoding = null

  isColorCodingOpen = false

  isSnapshotHistoryOpen = false
  @ViewChild(CdkOverlayOrigin) triggerSnapShot: CdkOverlayOrigin;
  issnapshotHistoryDataAvailable = false
  
  ngrPivotApplicationId: any = 374;

  headerSortBy(arrayToSort, sortField) {

    this.governanceReportService.headerSortBy(arrayToSort, sortField, this.reportMode, this.normalModeView,
      this.getSelectedCBOWCardDataType(), this.baseLegalEntityLevel);

  }

  getSortOrder(sortField) {

    return this.governanceReportService.getSortOrder(sortField, this.reportMode, this.normalModeView, this.getSelectedCBOWCardDataType(),
      this.baseLegalEntityLevel);

  }

  headerSortByDm(arrayToSort, sortField, sortFieldTypeTemp) {

    this.governanceReportService.headerSortByDm(arrayToSort, sortField, sortFieldTypeTemp, this.baseLegalEntityLevel);

  }

  getSortOrderDm(sortField, sortFieldTypeTemp) {

    return this.governanceReportService.getSortOrderDm(sortField, sortFieldTypeTemp, this.baseLegalEntityLevel)

  }

  protected _onDestroy = new Subject<void>();

  protected _repeatCall = new Subject<void>();

  isSettingsLoading = false;

  reportMode: any = "Normal Mode";

  normalModeView: any = "CBOW";

  incomplete_cta_count: any = 0;

  completed_cta_count: any = 0;

  total_cta_count: any = 0;

  detailed_mode_incomplete_cta_count: any = 0;

  detailed_mode_completed_cta_count: any = 0;

  detailed_mode_total_ar_days: any = 0;

  detailed_mode_total_ar_amount: any = 0;

  detailed_mode_total_ar_amount_original: any = 0;

  detailed_mode_total_ubr_days: any = 0;

  detailed_mode_total_ubr_amount: any = 0;

  detailed_mode_total_ubr_amount_original: any = 0;

  detailed_mode_total_wc_days: any = 0;

  detailed_mode_total_wc_amount: any = 0;

  detailed_mode_total_wc_amount_original: any = 0;

  detailed_mode_total_retention_amount: any = 0;

  detailed_mode_total_retention_amount_original: any = 0;

  detailed_mode_total_ar_wo_retention: any = 0;

  detailed_mode_total_ar_wo_retention_original: any = 0;

  detailed_mode_total_wc_days_wo_retention: any = 0;

  detailed_mode_total_wc_wo_retention: any = 0;

  detailed_mode_total_wc_wo_retention_original: any = 0;

  detailed_mode_total_cta_count: any = 0;

  reportMonthInterval: any = "This Month";

  normalModeArAgingInterval: any = 0;

  detailedModeArAgingInterval: any = 0;

  normalModeCtaViewInterval: any = "Overall";

  detailedModeCtaViewInterval: any = "Overall";

  detailedModeBrightness: any = "Bright Mode";

  latest_actual_date: Moment;

  monthYearDate = new FormControl(moment());

  totalPoCAvg: any = 0;

  totalPoCPlanned: any = 0;

  totalPoCActual: any = 0;

  totalPoCValue: any = 0;

  misDataYearWise: any = [];

  projectsColClass = "col-2-px";

  projectsColItemClass = "col-2-px-item";

  projectsColSpilloverClass = "col-2-px-spillover";

  cbowViewCards = null

  ctaViewCards = [
    {
      status: 'Total',
      count: 0,
      selected: true
    },
    {
      status: 'Completed',
      count: 0,
      selected: false
    },
    {
      status: 'Open',
      count: 0,
      selected: false
    },
    {
      status: 'Execution',
      count: 0,
      selected: false
    }
  ];

  applicationId: any = 71;

  current_fy_year_1: any;

  current_fy_year_2: any;

  next_fy_year_2: any;

  highestPl = "";

  detailedModeBaseLegalEntity = "";

  detailedModeBaseLegalEntityExpandOverridden = false;

  normalModeCBOWBaseLegalEntity = "";

  normalModeCBOWBaseLegalEntityExpandOverridden = false;

  normalModeCTABaseLegalEntity = "";

  baseLegalEntityLevel = 0;

  isCBOFTMDataRetrieved = false;

  isCTAFTMDataRetrieved = false;

  isWCDDataRetrieved = false;


  isBBDataRetrieved = false;

  isBbDataResolved = false;

  BBData = [];

  originalBbData = [];

  ResolvedBBData = [];

  BBDataCount = [
    {
      obv: 0,
      bbRev: 0,
      bbGM: 0,
      uob1: 0,
      uob2: 0,
      uob3: 0,
      uob4: 0,
      total: 0,
      cgm: 0,
      nygm: 0,
      nyrev: 0,
      bbgmm: 0
    }
  ];

  BBProbability = [
    {
      veryhigh: false,
      high: false,
      low: false,
      medium: false,
      netnew: false,
      eam: false
    }
  ];

  bbProbabilityModal = [
    {
      veryhigh: false,
      high: false,
      low: false,
      medium: false,
      netnew: false,
      eam: false,
      upcoming3months: false
    }
  ];

  BBDurationRange = "Current FY";

  isArAgingDaysDataRetrieved = false;

  isItemWiseUbrValuesRetrieved = false;

  notifyRelease = false;

  notifyReleaseAnimated = false;

  resolvedMISData: any = [];

  resolvedMisDataForOpportunityExcessCards: any = [];

  misOppChipFilterAppliedFirstTime = false;

  resolvedMISOpportunityData: any = [];

  isDownloadingMisReport = false;

  downloadableMisData: any = [];

  sourceCBOData: any = [];

  cboFTMData: any;

  cboActiveFTMData: any;

  resolvedCBOData: any = [];

  resolvedCboLowerLeLevelData = [];

  // resolvedCBOItemData: any = [];

  // resolvedCBOItemPlannedData: any = [];

  ctaFTMData: any = [];

  resolvedNormalModeCTAFTMData: any = [];

  resolvedDetailedModeCTAFTMData: any = [];

  wcdData: any = [];

  resolvedWCDData: any = [];

  arAgingDaysData: any = [];

  arOriginalAgingDaysData: any = [];

  itemWiseUbrValues: any = [];

  itemWiseUbrValuesOriginal: any = [];

  itemWiseUbrItemIds: any = [];

  preResolvedArAgingDaysData: any = [];

  resolvedArAgingDaysData: any = [];

  legalEntitiesData = [];

  legalEntities = [];
  authLegalEntities = [];
  legalEntitiesLoading = true;

  misFirstFilter = "pl_id";
  misSecondFilter = "sub_pl_id";

  cboFirstFilter = "pl";
  cboSecondFilter = "sub_pl";

  dateFormat = "YYYY-MM-DD";

  ftwStartDate: any = "";
  ftwEndDate: any = "";

  ftmDate: any = "";
  ftmStartDate: any = "";
  ftmEndDate: any = "";

  activeFtmDate: any = "";
  activeFtmStartDate: any = "";
  activeFtmEndDate: any = "";

  activeFtmPyDate: any = "";
  ftpyStartDate: any = "";
  ftpyEndDate: any = "";

  ftyStartDate: any = "";
  ftyEndDate: any = "";

  upcoming3MonthsStartDate: any = "";
  upcoming3MonthsEndDate: any = "";

  ftnyStartDate: any = "";
  ftnyEndDate: any = "";

  actualSubType = "Actual";
  plannedMonthSubType = "Planned-Month";
  originalPlannedMonthSubType = "Planned-Month";
  plannedSubType = "Planned";
  elem;

  arAmountLabel = "AR Amount";
  arDaysLabel = "AR Days";
  ubrAmountLabel = "UBR Amount";
  ubrDaysLabel = "UBR Days";
  wcdAmountLabel = "WC Amount";
  wcdDaysLabel = "WC Days";
  retentionAmountLabel = "Retention Amount";
  arWoRetentionLabel = "AR w/o Retention";
  wcDaysWoRetentionLabel = "WC Days w/o Retention";
  wcWoRetentionLabel = "WC w/o Retention";
  actualLabel = "Actual";
  plannedLabel = "Planned";

  currentUser: any = {};

  dfDDMMM = "DD-MMM";

  dfDDMMMYYYY = "DD-MMM-YYYY";

  poCTargetToDate = 0;

  visibleMisCards = [];

  misParams = {
    visibleBackendPostingTypeIdsOtherThanDefault: [],
    visibleFrontendPostingTypeIdsOtherThanDefault: []
  };

  userRoleOrgCodes: any;

  userRolePlIds: any;

  userRolePostingTypeIds: any;

  visibleMisCardsModulo = 3;

  visibleMisOpportunityCardsModulo = 3;

  prevftyStartDate: any = "";
  prevftyEndDate: any = "";
  ytdendDate: any = "";

  fyq1EndDate: any = "";
  fyq2StartDate: any = "";
  fyq2EndDate: any = "";
  fyq3StartDate: any = "";
  fyq3EndDate: any = "";
  fyq4StartDate: any = "";

  prevFyq1EndDate: any = "";
  prevFyq2StartDate: any = "";
  prevFyq2EndDate: any = "";
  prevFyq3StartDate: any = "";
  prevFyq3EndDate: any = "";
  prevFyq4StartDate: any = "";
  prevYTDEndDate: any = "";

  localUdrfData = {
    "misCards": [
      {
        "dataTypeCode": "FTM",
        "dataTypeName": "FTM",
        "dataTypeDescription": "FTM",
        "isSelected": true,
        "isVisible": true,
        "order": 1
      },
      {
        "dataTypeCode": "YTD",
        "dataTypeName": "YTD",
        "dataTypeDescription": "YTD",
        "isSelected": true,
        "isVisible": true,
        "order": 3
      },
      {
        "dataTypeCode": "YTD_PY",
        "dataTypeName": "YTD PY",
        "dataTypeDescription": "YTD PY",
        "isSelected": true,
        "isVisible": true,
        "order": 4
      },
      {
        "dataTypeCode": "UOB",
        "dataTypeName": "UOB",
        "dataTypeDescription": "UOB",
        "isSelected": true,
        "isVisible": true,
        "order": 5
      },
      {
        "dataTypeCode": "LE",
        "dataTypeName": "LE",
        "dataTypeDescription": "LE",
        "isSelected": false,
        "isVisible": true,
        "order": 6
      },
      {
        "dataTypeCode": "LE_BUD",
        "dataTypeName": "LE: Actual + Budget",
        "dataTypeDescription": "LE: Actual + Budget",
        "isSelected": false,
        "isVisible": true,
        "order": 7
      },
      {
        "dataTypeCode": "UOB_NY",
        "dataTypeName": "UOB NY",
        "dataTypeDescription": "UOB NY",
        "isSelected": false,
        "isVisible": true,
        "order": 8
      },
      {
        "dataTypeCode": "FTM_PY",
        "dataTypeName": "FTM PY",
        "dataTypeDescription": "FTM PY",
        "isSelected": false,
        "isVisible": true,
        "order": 2
      },
      {
        "dataTypeCode": "LE_PY",
        "dataTypeName": "LE PY",
        "dataTypeDescription": "LE PY",
        "isSelected": false,
        "isVisible": true,
        "order": 9
      },
      {
        "dataTypeCode": "LE_YTD",
        "dataTypeName": "LE YTD",
        "dataTypeDescription": "LE YTD",
        "isSelected": false,
        "isVisible": true,
        "order": 10
      },
      {
        "dataTypeCode": "YOY_YTD",
        "dataTypeName": "YOY YTD",
        "dataTypeDescription": "YoY YTD",
        "isSelected": false,
        "isVisible": true,
        "order": 11
      },
      {
        "dataTypeCode": "YOY_LE_BUD",
        "dataTypeName": "YOY LE: Actual + Budget",
        "dataTypeDescription": "YOY LE: Actual + Budget",
        "isSelected": false,
        "isVisible": true,
        "order": 12
      }
    ],
    "misOpportunityCards": [
      {
        "dataTypeCode": "FTM_BB",
        "dataTypeName": "FTM + Opportunity",
        "dataTypeDescription": "FTM + Opportunity",
        "isSelected": true,
        "isVisible": true,
        "order": 1
      },
      {
        "dataTypeCode": "UOB_BB",
        "dataTypeName": "UOB + Opportunity",
        "dataTypeDescription": "UOB + Opportunity",
        "isSelected": true,
        "isVisible": true,
        "order": 2
      },
      {
        "dataTypeCode": "LE_BB",
        "dataTypeName": "LE + Opportunity",
        "dataTypeDescription": "LE + Opportunity",
        "isSelected": true,
        "isVisible": true,
        "order": 3
      },
      {
        "dataTypeCode": "UOB_NY_BB",
        "dataTypeName": "UOB NY + Opportunity",
        "dataTypeDescription": "UOB NY + Opportunity",
        "isSelected": true,
        "isVisible": true,
        "order": 4
      }
    ],
    "gbmCards": [],
    "otherCards": [
      {
        "id": "CO",
        "header": "Collection",
        "isChecked": true,
        "isVisible": true
      },
      {
        "id": "BI",
        "header": "Billing",
        "isChecked": true,
        "isVisible": true
      },
      {
        "id": "OBV",
        "header": "OBV",
        "isChecked": true,
        "isVisible": true
      },
      {
        "id": "WCD",
        "header": "WCD and AR",
        "isChecked": true,
        "isVisible": true
      },
      {
        "id": "BBR",
        "header": "B&B - Current FY",
        "isChecked": true,
        "isVisible": true
      },
      {
        "id": "CTA",
        "header": "CTA",
        "isChecked": true,
        "isVisible": true
      },
      {
        "id": "Projects",
        "header": "Weekly Milestone",
        "isChecked": true,
        "isVisible": true
      }
    ],
    "settingsDetails": [],
    "defaultReportView": "Card",
    "overallPoCCalcMethod": "Average of PoCs",
    "isMisBudgetVisible": "No",
    "isMisVarianceVisible": "No",
    "misCardsSize": "M",
    "cboCardsSize": "M",
    "misOpportunityCardsSize": "M",
    "projectsWidgetSize": "F",
    "isFullValue": false,
    "misLiveData": false,
    "defaultCurrency": "INR",
    "ngrReportTypes": [],
    "projectMilestonesLimit": 25,
    "isProjectsOverdueVisible": false
    // ,
    // "projectVisibleColumns": ["Milestone"]
  };

  projectsWidgetColSize = "col-11-5-pmo";

  udrfFilterConfig = [
    {
      filterId: 1,
      filterIds: [],
      filterNames: [],
      misFilterIdBased: "N",
      misFilterField: "serviceType",
      cboFilterIdBased: "Y",
      cboFilterField: "serviceTypeId",
      arFilterIdBased: "Y",
      arFilterField: "service_type_id",
      ctaFilterIdBased: "N",
      ctaFilterField: "serviceType",
    },
    {
      filterId: 2,
      filterIds: [],
      filterNames: [],
      misFilterIdBased: "N",
      misFilterField: "productCategory",
      cboFilterIdBased: "Y",
      cboFilterField: "productCategoryId",
      arFilterIdBased: "Y",
      arFilterField: "product_category_id",
      ctaFilterIdBased: "N",
      ctaFilterField: "productCategory"
    },
    {
      filterId: 3,
      filterIds: [],
      filterNames: [],
      misFilterIdBased: "Y",
      misFilterField: "customerId",
      cboFilterIdBased: "Y",
      cboFilterField: "customerId",
      arFilterIdBased: "Y",
      arFilterField: "customer_id",
      ctaFilterIdBased: "Y",
      ctaFilterField: "customerId"
    },
    {
      filterId: 4,
      filterIds: [],
      filterNames: [],
      misFilterIdBased: "Y",
      misFilterField: "customerId",
      cboFilterIdBased: "Y",
      cboFilterField: "customerId",
      arFilterIdBased: "Y",
      arFilterField: "customer_id",
      ctaFilterIdBased: "Y",
      ctaFilterField: "customerId"
    },
    {
      filterId: 5,
      filterIds: [],
      filterNames: [],
      misFilterIdBased: "N",
      misFilterField: "plId",
      cboFilterIdBased: "Y",
      cboFilterField: "plId",
      arFilterIdBased: "N",
      arFilterField: "plId",
      ctaFilterIdBased: "N",
      ctaFilterField: "plId"
    },
    {
      filterId: 6,
      filterIds: [],
      filterNames: [],
      misFilterIdBased: "N",
      misFilterField: "itemStatus",
      cboFilterIdBased: "Y",
      cboFilterField: "itemStatus",
      arFilterIdBased: "N",
      arFilterField: "item_status_name",
      ctaFilterIdBased: "N",
      ctaFilterField: "itemStatus"
    },
    {
      filterId: 7,
      filterIds: [],
      filterNames: [],
      misFilterIdBased: "Y",
      misFilterField: "ganttTypeId",
      cboFilterIdBased: "Y",
      cboFilterField: "ganttType",
      arFilterIdBased: "Y",
      arFilterField: "gantt_type_id",
      ctaFilterIdBased: "Y",
      ctaFilterField: "ganttType"
    },
    {
      filterId: 8,
      filterIds: [],
      filterNames: [],
      filterKeyValues: [],
      idKey: "oid",
      filterKey: "item_ids",
      misFilterIdBased: "K",
      misFilterField: "item_id",
      cboFilterIdBased: "K",
      cboFilterField: "itemId",
      arFilterIdBased: "K",
      arFilterField: "item_id",
      ctaFilterIdBased: "K",
      ctaFilterField: "item_id"
    },
    {
      filterId: 9,
      filterIds: [],
      filterNames: [],
      misFilterIdBased: "N",
      misFilterField: "opportunityStatusId",
      cboFilterIdBased: "Y",
      cboFilterField: "opportunityStatusId",
      arFilterIdBased: "N",
      arFilterField: "opportunityStatusId",
      ctaFilterIdBased: "N",
      ctaFilterField: "opportunityStatusId"
    },
    {
      filterId: 10,
      filterIds: [],
      filterNames: [],
      misFilterIdBased: "N",
      misFilterField: "opportunity_owner_oid",
      cboFilterIdBased: "Y",
      cboFilterField: "opportunity_owner_oid",
      arFilterIdBased: "N",
      arFilterField: "opportunity_owner_oid",
      ctaFilterIdBased: "N",
      ctaFilterField: "opportunity_owner_oid"
    },
    {
      filterId: 11,
      filterIds: [],
      filterNames: [],
      misFilterIdBased: "N",
      misFilterField: "planned_range_value_id",
      cboFilterIdBased: "Y",
      cboFilterField: "planned_range_value_id",
      arFilterIdBased: "N",
      arFilterField: "planned_range_value_id",
      ctaFilterIdBased: "N",
      ctaFilterField: "planned_range_value_id"
    },
    {
      filterId: 12,
      filterIds: [],
      filterNames: [],
      misFilterIdBased: "N",
      misFilterField: "actual_range_value_id",
      cboFilterIdBased: "Y",
      cboFilterField: "actual_range_value_id",
      arFilterIdBased: "N",
      arFilterField: "actual_range_value_id",
      ctaFilterIdBased: "N",
      ctaFilterField: "actual_range_value_id"
    },
    {
      filterId: 13,
      filterIds: [],
      filterNames: [],
      misFilterIdBased: "N",
      misFilterField: "color_scheme_id_param",
      cboFilterIdBased: "Y",
      cboFilterField: "color_scheme_id_param",
      arFilterIdBased: "N",
      arFilterField: "color_scheme_id_param",
      ctaFilterIdBased: "N",
      ctaFilterField: "color_scheme_id_param"
    },
    {
      filterId: 14,
      filterIds: [],
      filterNames: [],
      misFilterIdBased: "N",
      misFilterField: "projectName",
      cboFilterIdBased: "Y",
      cboFilterField: "projectName",
      arFilterIdBased: "N",
      arFilterField: "projectName",
      ctaFilterIdBased: "N",
      ctaFilterField: "projectName"
    },
    {
      filterId: 15,
      filterIds: [],
      filterNames: [],
      misFilterIdBased: "N",
      misFilterField: "milestoneType",
      cboFilterIdBased: "Y",
      cboFilterField: "milestoneType",
      arFilterIdBased: "N",
      arFilterField: "milestoneType",
      ctaFilterIdBased: "N",
      ctaFilterField: "milestoneType"
    },
   
  ]

  misFlags: any;

  misCardClass = "col-3-5";

  misCardColClass = "col-1-9";

  misDetailedModeCardClass = "col-1-7-5";

  misOpportunityCardClass = "col-3-5";

  misOpportunityDetailedModeCardClass = "col-1-7-5";

  cboCardClass = "col-3-5";

  cboCardFirstColClass = "col-3-3";

  cboCardColClass = "col-1-9";

  misOpportunityColCardClass = "col-1-9"

  otherCardsFlags = {
    isCoVisible: true,
    isBiVisible: true,
    isObvVisible: true,
    isWcdVisible: true,
    isBbrVisible: true,
    isCtaVisible: true,
    isPmoDbVisible: true
  }

  pmoDb = {
    milestonePmoDbData: [],
    resolvedPmoDbData: [],
    totAcMlCount: 0,
    totPlMlCount: 0,
    totOvMlCount: 0,
    totAcAcCount: 0,
    totPlAcCount: 0,
    totOvAcCount: 0,
    totAcSoCount: 0,
    totPlSoCount: 0,
    totOvSoCount: 0,
    totAcPhCount: 0,
    totPlPhCount: 0,
    totOvPhCount: 0,
    totAcWpCount: 0,
    totPlWpCount: 0,
    totOvWpCount: 0,
    totAcMlValueOriginal: 0,
    totPlMlValueOriginal: 0,
    totOvMlValueOriginal: 0,
    totAcMlValue: "0",
    totPlMlValue: "0",
    totOvMlValue: "0",
    totAcMlValueTooltip: "0",
    totPlMlValueTooltip: "0",
    totOvMlValueTooltip: "0",
    isPmoDbDataLoading: false,
    pmoDbDataMonthCount: moment().month(),
    pmoDbDataMonthInterval: 'M0',
    pmoDbDataMonthIntervalCount: 0,
    pmoDbDataWeekIntervals: [],
    monthIntervals: [],
    weekIntervals: [],
    currentWeekIntervals: [],
    currentWeekIntervalLabel: "",
    currentMonthIntervalCount: 0,
    isProjectsDataRetrieved: false,
    pmoDataKeys: [{
      id: 1,
      order: 1,
      description: "Milestone",
      ganttTypeId: 0,
      ganttTypeName: "Milestones",
      visibleText: "Ml. Value",
      visibleTextSpillover: "Ml. Val.",
      visibleTextExtra: "Count",
      actualDataKey: "ml_actual_value",
      actualDataKeyText: "Actual Value",
      plannedDataKey: "ml_planned_value",
      plannedDataKeyText: "Planned Value",
      overdueDataKey: "ml_overdue_value",
      overdueDataKeyText: "Overdue Value",
      actualDataKeyExtra: "ml_actual_count",
      actualDataKeyExtraText: "Actual Count",
      plannedDataKeyExtra: "ml_planned_count",
      plannedDataKeyExtraText: "Planned Count",
      overdueDataKeyExtra: "ml_overdue_count",
      overdueDataKeyExtraText: "Overdue Count",
      ganttTypeKeys: ["Milestone_actual_count", "Milestone_planned_count", "Milestone_overdue_count"],
      isVisibleInWidget: true
    },
    {
      id: 2,
      order: 2,
      description: "Activity",
      ganttTypeId: 0,
      ganttTypeName: "Activities",
      visibleText: "Ac. Count",
      actualDataKey: "ac_actual_count",
      actualDataKeyText: "Actual Count",
      plannedDataKey: "ac_planned_count",
      plannedDataKeyText: "Planned Count",
      overdueDataKey: "ac_overdue_count",
      overdueDataKeyText: "Overdue Count",
      ganttTypeKeys: ["Activity_actual_count", "Activity_planned_count", "Activity_overdue_count"],
      isVisibleInWidget: false
    },
    {
      id: 3,
      order: 3,
      description: "Sign Off",
      ganttTypeId: 0,
      ganttTypeName: "Sign Offs",
      visibleText: "SO. Count",
      actualDataKey: "so_actual_count",
      actualDataKeyText: "Actual Count",
      plannedDataKey: "so_planned_count",
      plannedDataKeyText: "Planned Count",
      overdueDataKey: "so_overdue_count",
      overdueDataKeyText: "Overdue Count",
      ganttTypeKeys: ["Sign Off_actual_count", "Sign Off_planned_count", "Sign Off_overdue_count"],
      isVisibleInWidget: false
    },
    {
      id: 4,
      order: 4,
      description: "Phase",
      ganttTypeId: 0,
      ganttTypeName: "Phases",
      visibleText: "Ph. Count",
      actualDataKey: "ph_actual_count",
      actualDataKeyText: "Actual Count",
      plannedDataKey: "ph_planned_count",
      plannedDataKeyText: "Planned Count",
      overdueDataKey: "ph_overdue_count",
      overdueDataKeyText: "Overdue Count",
      ganttTypeKeys: ["Phase_actual_count", "Phase_planned_count", "Phase_overdue_count"],
      isVisibleInWidget: false
    },
    {
      id: 5,
      order: 5,
      description: "Work Package",
      ganttTypeId: 0,
      ganttTypeName: "Work Packages",
      visibleText: "WP. Count",
      actualDataKey: "wp_actual_count",
      actualDataKeyText: "Actual Count",
      plannedDataKey: "wp_planned_count",
      plannedDataKeyText: "Planned Count",
      overdueDataKey: "wp_overdue_count",
      overdueDataKeyText: "Overdue Count",
      ganttTypeKeys: ["Work Package_actual_count", "Work Package_planned_count", "Work Package_overdue_count"],
      isVisibleInWidget: false
    }],
    visiblePmoDataKeys: []
  };

  allNgrReportTypes: any;

  masterConfigs: any = null

  selectedCard: any = null

  isWidgetViewEnabled: boolean = true
  isFiltersEnabled: boolean = true
  isCTAEnabled: boolean = true
  isHistoryEnabled: boolean = true
  isSettingsEnabled: boolean = true
  isNewReleasesEnabled: boolean = true
  isPivotViewEnabled: boolean = true
  isAdminSettingsEnabled: boolean = false
  isReloadButtonEnabled: boolean = true
  isGovDataLoading: boolean = true;

  isSnapShotHistoryButtonEnabled: boolean = false
  snapshotHistoryData: any

  isGroupViewButtonEnabled : boolean = false;
  groupMenuButtons : any
  selectedViewGroupMode = ''
  selectedViewGroupFilterField : any
  isGroupByButtonLoading = true
  otherGroupBysourceCBOData: any = [];
  otherViewGroupByData : any = {}

  isOtherGroupDataLoading = true

  misPostingPeriod : any
  isMISPostingDateDefault = false

  isDisplayFullValue = false
  isDefaultCurrencyUSD = false

  govUdrfData = {};
  ctaAppConfig = null;

  routeSubscription: Subscription;

  constructor(private governanceReportService: GovernanceReportService, private reportsService: ReportsService,
    private rolesService: RolesService, private utilityService: UtilityService, @Inject(DOCUMENT) private document: any,
    private authService: LoginService, private arUbrService: ArUbrService, public dialog: MatDialog,
    private sharedLazyLoadedComponentsService: SharedLazyLoadedComponentsService, public udrfService: UdrfService,
    private arithmeticEvalService: ArithmeticEvalService, private governanceReportSubService: GovernanceReportSubService,
    private jsonToExcelService: JsonToExcelService, private detailedViewService: DetailedViewService, 
    private router: Router, private activatedRoute: ActivatedRoute) { }

  async ngOnInit() {

    this.masterConfigs = await this.governanceReportSubService.getGovernanceReportConfigs()
    this.colorCoding = await this.governanceReportSubService.getColorCodingLegend()
    this.groupMenuButtons = await this.governanceReportSubService.getGroupViewItemConfigs()

    if (this.groupMenuButtons && this.groupMenuButtons.length > 0) {
      let currentView = this.groupMenuButtons.filter(item => item.default_view == 1)
      this.selectedViewGroupMode = currentView[0].group_name
      this.selectedViewGroupFilterField = currentView[0].filter_field
      console.log("Default View : ", this.selectedViewGroupMode)
      if (this.selectedViewGroupMode != 'Region') {
        console.log("Other Type Data Source on Default section : ", this.otherGroupBysourceCBOData)
        this.callOtherViewTypeCBODataType()
      }
    }
    else {
      this.selectedViewGroupMode = 'Region'
      this.selectedViewGroupFilterField = 'pl'
      console.log("Default View : ", this.selectedViewGroupMode)
    }

    if(this.masterConfigs && this.masterConfigs.length > 0) {

      let cboCards =_.find(this.masterConfigs,  config => {
        return config.type == "cbowViewCards"
      })
      let ctaCards = _.find(this.masterConfigs,  config => {
        return config.type == "ctaViewCards"
      })

      let widgetView = _.find(this.masterConfigs,  config => {
        return config.type == "widgetview"
      })

      let filters = _.find(this.masterConfigs,  config => {
        return config.type == "filters"
      })

      let cta = _.find(this.masterConfigs,  config => {
        return config.type == "cta"
      })

      let history = _.find(this.masterConfigs,  config => {
        return config.type == "history"
      })

      let settings = _.find(this.masterConfigs,  config => {
        return config.type == "settings"
      })

      let newReleases = _.find(this.masterConfigs,  config => {
        return config.type == "newreleases"
      })

      let pivotView = _.find(this.masterConfigs,  config => {
        return config.type == "pivotview"
      })

      let adminSettings = _.find(this.masterConfigs,  config => {
        return config.type == "adminsettings"
      })

      let reloadBtn = _.find(this.masterConfigs,  config => {
        return config.type == "reloadBtn"
      })

      let snapshotHistoryBtn = _.find(this.masterConfigs,  config => {
        return config.type == "snapshotHistory"
      })

      let groupViewBtn = _.find(this.masterConfigs,  config => {
        return config.type == "groupView"
      })

      let isMISDefaultMonth = _.find(this.masterConfigs,  config => {
        return config.type == "isMISPostingPeriodDefaultMonth"
      })

      let displayFullValueConfig = _.find(this.masterConfigs,  config => {
        return config.type == "isDisplayFullValue"
      })

      let defaultCurrencyUSD = _.find(this.masterConfigs, config => {
        return config.type == "isDefaultCurrencyUSD"
      })

      if(cboCards) {
        this.cbowViewCards = (typeof cboCards.config == "string") ? JSON.parse(cboCards.config) : cboCards.config
        let selected = _.find(this.cbowViewCards, card => {
          return card.selected
        })
        this.selectedCard = selected?.dataTypeName
      }
      if(ctaCards) {
        this.ctaViewCards = (typeof ctaCards.config == "string") ? JSON.parse(ctaCards.config) : ctaCards.config 
      }

      if(widgetView) {
        this.isWidgetViewEnabled = (widgetView.enabled == 1) ? true : false
      }

      if(filters) {
        this.isFiltersEnabled = (filters.enabled == 1) ? true : false
      }

      if(cta) {
        this.isCTAEnabled = (cta.enabled == 1) ? true : false
      }

      if(history) {
        this.isHistoryEnabled = (history.enabled == 1) ? true : false
      }

      if(settings) {
        this.isSettingsEnabled = (settings.enabled == 1) ? true : false
      }

      if(newReleases) {
        this.isNewReleasesEnabled = (newReleases.enabled == 1) ? true : false
      }

      if(pivotView) {
        this.isPivotViewEnabled = (pivotView.enabled == 1) ? true : false
      }

      if(adminSettings) {
        this.isAdminSettingsEnabled = (adminSettings.enabled == 1) ? true : false
      }

      if(reloadBtn) {
        this.isReloadButtonEnabled = (reloadBtn.enabled == 1) ? true : false
      }

      if(snapshotHistoryBtn) {
        this.isSnapShotHistoryButtonEnabled = (snapshotHistoryBtn.enabled == 1) ? true : false
      }

      if(groupViewBtn) {
        this.isGroupViewButtonEnabled = (groupViewBtn.enabled == 1) ? true : false
      }

      if(isMISDefaultMonth) {
        this.isMISPostingDateDefault = true
        await this.governanceReportSubService.getMISPostingPeriod()
        .pipe(takeUntil(this._onDestroy)).pipe(takeUntil(this._repeatCall))
        .subscribe(async res => {
          if (res["messType"] == "S") {
            this.misPostingPeriod = res["data"]
            console.log("misPostingPeriod : ",this.misPostingPeriod)
            this.monthYearDate.patchValue(moment(this.misPostingPeriod['start_date']))

          }
        })
      }

      // Setting the Display Full value Report Config
      if(displayFullValueConfig){
        this.isDisplayFullValue = true
        this.localUdrfData.isFullValue = true
      }

      // Setting the Default Currency Report Config
      if(defaultCurrencyUSD){
        this.isDefaultCurrencyUSD = true
        this.localUdrfData.defaultCurrency = "USD"
      }

      this.ctaAppConfig = this.masterConfigs.find(val => val['type'] == 'ctaAppConfig')?.config || null;

      if (this.ctaAppConfig)
        this.ctaAppConfig = JSON.parse(this.ctaAppConfig);

    }

    if (this.rolesService.isApplicationVisibleForUser(this.ngrPivotApplicationId))
      this.isNgrPivotVisible = true;

    this.resetNonDetailedModeData();

    this.isSettingsLoading = true;
    this.isGroupByButtonLoading = false


    this.reportsService.getMisFlags()
      .pipe(takeUntil(this._onDestroy)).pipe(takeUntil(this._repeatCall))
      .subscribe(res => {

        if (res["messType"] == "S") {

          this.misFlags = res["misFlags"];

          for (let bbDefaultChip of this.misFlags.bbDefaultChips)
            this.BBProbability[0][bbDefaultChip.replace(" ", "").toLowerCase()] = true;

          for (let bbDefaultChip of this.misFlags.bbDefaultChips)
            this.bbProbabilityModal[0][bbDefaultChip.replace(" ", "").toLowerCase()] = true;

          this.originalPlannedMonthSubType = this.plannedMonthSubType;

          this.elem = document.documentElement;

          this.currentUser = this.authService.getProfile().profile;

          this.calculatePoCTargetToDate();

          var current_year = moment().year();

          var current_month = moment().month();


          this.ftwStartDate = moment().startOf('week').format(this.dateFormat);

          this.ftwEndDate = moment().endOf('week').format(this.dateFormat);


          this.ftmDate = moment().format("YYYY-MM-27");

          this.ftmStartDate = moment().startOf('month').format(this.dateFormat);

          this.ftmEndDate = moment().endOf('month').format(this.dateFormat);


          this.activeFtmDate = this.ftmDate;

          this.activeFtmPyDate = moment(this.ftmDate).subtract(1, 'year').format("YYYY-MM-27");

          this.activeFtmStartDate = this.ftmStartDate;

          this.activeFtmEndDate = this.ftmEndDate;


          if (this.misFlags.tenantFyStartMonth == 1 && this.misFlags.tenantFyEndMonth == 12) {

            this.current_fy_year_1 = current_year - 1;
            this.current_fy_year_2 = current_year - 1;
            this.next_fy_year_2 = current_year + 1;
            this.ftnyStartDate = moment().year(this.next_fy_year_2).month(this.misFlags.tenantFyStartMonth - 1).startOf('month').format(this.dateFormat);

          }

          else if (current_month > (this.misFlags.tenantFyStartMonth - 2)) {

            this.current_fy_year_1 = current_year - 1;
            this.current_fy_year_2 = current_year;
            this.next_fy_year_2 = current_year + 2;
            this.ftnyStartDate = moment().year(this.current_fy_year_2 + 1).month(this.misFlags.tenantFyStartMonth - 1).startOf('month').format(this.dateFormat);

          } else {

            this.current_fy_year_1 = current_year - 2;
            this.current_fy_year_2 = current_year - 1;
            this.next_fy_year_2 = current_year + 1;
            this.ftnyStartDate = moment().year(this.current_fy_year_2 + 1).month(this.misFlags.tenantFyStartMonth - 1).startOf('month').format(this.dateFormat);

          }

          this.ftpyStartDate = moment().year(this.current_fy_year_1).month(this.misFlags.tenantFyStartMonth - 1).startOf('month').format(this.dateFormat);

          this.ftpyEndDate = moment().year(this.current_fy_year_2).month(this.misFlags.tenantFyEndMonth - 1).endOf('month').format(this.dateFormat);

          this.ftyStartDate = moment().year(this.current_fy_year_1 + 1).month(this.misFlags.tenantFyStartMonth - 1).startOf('month').format(this.dateFormat);

          this.ftyEndDate = moment().year(this.current_fy_year_2 + 1).month(this.misFlags.tenantFyEndMonth - 1).endOf('month').format(this.dateFormat);

          this.ftnyEndDate = moment().year(this.next_fy_year_2).month(this.misFlags.tenantFyEndMonth - 1).endOf('month').format(this.dateFormat);

          this.upcoming3MonthsStartDate = moment().startOf('month').format(this.dateFormat);

          this.upcoming3MonthsEndDate = moment().add(2, 'month').endOf('month').format(this.dateFormat);

          this.prevftyStartDate = moment().year(this.current_fy_year_1).month(this.misFlags.tenantFyStartMonth - 1).startOf('month').format(this.dateFormat);

          this.prevftyEndDate = moment().year(this.current_fy_year_1).month(this.misFlags.tenantFyEndMonth - 1).endOf('month').format(this.dateFormat);

          this.ytdendDate = moment().format(this.dateFormat);

          this.fyq1EndDate = moment().year(this.current_fy_year_1).month(this.misFlags.tenantFyEndMonth - 1).add(3, "month").endOf('month').format(this.dateFormat);

          this.fyq2StartDate = moment().year(this.current_fy_year_1).month(this.misFlags.tenantFyStartMonth - 1).add(3, "month").startOf('month').format(this.dateFormat);

          this.fyq2EndDate = moment().year(this.current_fy_year_1).month(this.misFlags.tenantFyEndMonth - 1).add(6, "month").endOf('month').format(this.dateFormat);

          this.fyq3StartDate = moment().year(this.current_fy_year_1).month(this.misFlags.tenantFyStartMonth - 1).add(6, "month").startOf('month').format(this.dateFormat);

          this.fyq3EndDate = moment().year(this.current_fy_year_1).month(this.misFlags.tenantFyEndMonth - 1).add(9, "month").endOf('month').format(this.dateFormat);

          this.fyq4StartDate = moment().year(this.current_fy_year_1).month(this.misFlags.tenantFyStartMonth - 1).add(9, "month").startOf('month').format(this.dateFormat);
          
          this.prevFyq1EndDate = moment().year(this.current_fy_year_1).month(this.misFlags.tenantFyEndMonth - 1).add(3, "month").endOf('month').format(this.dateFormat);

          this.prevFyq2StartDate = moment().year(this.current_fy_year_1).month(this.misFlags.tenantFyStartMonth - 1).add(3, "month").startOf('month').format(this.dateFormat);

          this.prevFyq2EndDate = moment().year(this.current_fy_year_1).month(this.misFlags.tenantFyEndMonth - 1).add(6, "month").endOf('month').format(this.dateFormat);

          this.prevFyq3StartDate = moment().year(this.current_fy_year_1).month(this.misFlags.tenantFyStartMonth - 1).add(6, "month").startOf('month').format(this.dateFormat);

          this.prevFyq3EndDate = moment().year(this.current_fy_year_1).month(this.misFlags.tenantFyEndMonth - 1).add(9, "month").endOf('month').format(this.dateFormat);

          this.prevFyq4StartDate = moment().year(this.current_fy_year_1).month(this.misFlags.tenantFyStartMonth - 1).add(9, "month").startOf('month').format(this.dateFormat);

          this.prevYTDEndDate = moment().subtract(1, "year").format(this.dateFormat);

          // console.log(`ftpyStartDate - ${this.ftpyStartDate} ,ftpyEndDate - ${this.ftpyEndDate} , 
          // ftyStartDate - ${this.ftyStartDate} ,ftyEndDate -${this.ftyEndDate} , 
          // ftnyStartDate - ${this.ftnyStartDate} , ftnyEndDate - ${this.ftnyEndDate} `)

          this.localUdrfData.gbmCards = [{
            "order": 1,
            "isVisible": true,
            "isSelected": false,
            "dataTypeCode": "CurrentFY",
            "dataTypeName": "CurrentFY",
            "startDate": this.ftyStartDate,
            "endDate": this.ftyEndDate,
            "gbmName": "GBM CurrentFY"
          },
          {
            "order": 2,
            "isVisible": true,
            "isSelected": false,
            "dataTypeCode": "PrevFY",
            "dataTypeName": "PrevFY",
            "startDate": this.prevftyStartDate,
            "endDate": this.prevftyEndDate,
            "gbmName": "GBM PrevFY"
          },
          {
            "order": 3,
            "isVisible": true,
            "isSelected": false,
            "dataTypeCode": "YTD",
            "dataTypeName": "YTD",
            "startDate": this.ftyStartDate,
            "endDate": this.ytdendDate,
            "gbmName": "GBM YTD"
          },

          {
            "dataTypeCode": "FYQ1",
            "dataTypeName": "FYQ1",
            "isSelected": false,
            "isVisible": true,
            "order": 4,
            "startDate": this.ftyStartDate,
            "endDate": this.fyq1EndDate,
            "gbmName": "GBM FYQ1"
          },
          {
            "dataTypeCode": "FYQ2",
            "dataTypeName": "FYQ2",
            "isSelected": false,
            "isVisible": true,
            "order": 5,
            "startDate": this.fyq2StartDate,
            "endDate": this.fyq2EndDate,
            "gbmName": "GBM FYQ2"
          },
          {
            "dataTypeCode": "FYQ3",
            "dataTypeName": "FYQ3",
            "isSelected": false,
            "isVisible": true,
            "order": 6,
            "startDate": this.fyq3StartDate,
            "endDate": this.fyq3EndDate,
            "gbmName": "GBM FYQ3"
          },
          {
            "dataTypeCode": "FYQ4",
            "dataTypeName": "FYQ4",
            "isSelected": false,
            "isVisible": true,
            "order": 7,
            "startDate": this.fyq4StartDate,
            "endDate": this.ftyEndDate,
            "gbmName": "GBM FYQ4"
          },
          {
            "dataTypeCode": "PrevFYQ1",
            "dataTypeName": "PrevFYQ1",
            "isSelected": false,
            "isVisible": true,
            "order": 8,
            "startDate": this.prevftyStartDate,
            "endDate": this.prevFyq1EndDate,
            "gbmName": "GBM PrevFYQ1"
          },
          {
            "dataTypeCode": "PrevFYQ2",
            "dataTypeName": "PrevFYQ2",
            "isSelected": false,
            "isVisible": true,
            "order": 9,
            "startDate": this.prevFyq2StartDate,
            "endDate": this.prevFyq2EndDate,
            "gbmName": "GBM PrevFYQ2"
          },
          {
            "dataTypeCode": "PrevFYQ3",
            "dataTypeName": "PrevFYQ3",
            "isSelected": false,
            "isVisible": true,
            "order": 10,
            "startDate": this.prevFyq3StartDate,
            "endDate": this.prevFyq3EndDate,
            "gbmName": "GBM PrevFYQ3"
          },
          {
            "dataTypeCode": "PrevFYQ4",
            "dataTypeName": "PrevFYQ4",
            "isSelected": false,
            "isVisible": true,
            "order": 11,
            "startDate": this.prevFyq4StartDate,
            "endDate": this.prevftyEndDate,
            "gbmName": "GBM PrevFYQ4"
          },
          {
            "dataTypeCode": "PrevYTD",
            "dataTypeName": "PrevYTD",
            "isSelected": false,
            "isVisible": true,
            "order": 12,
            "startDate": this.prevftyStartDate,
            "endDate": this.prevYTDEndDate,
            "gbmName": "GBM PrevYTD"
          }];

          this.udrfService.udrfData.customRangeData = [];
          this.udrfService.getAppUdrfConfig(this.applicationId, this.retrieveFirstTimeData.bind(this));
          
          this.getNotifyReleases();

        }

        else {

          this.isSettingsLoading = false;

          let errReportingTeams = "KEBS";

          this.utilityService.showErrorMessage(res, errReportingTeams);

        }

      }, err => {

        this.isSettingsLoading = false;

        let errReportingTeams = "KEBS";

        this.utilityService.showErrorMessage(err, errReportingTeams);

      });

    this.routeSubscription = this.router.events.subscribe(event => {

      if (event instanceof NavigationEnd) {

        const currentUrl = event.url;

        if (!currentUrl.includes('lcdp-outlet:lcdp')) {

          this.normalModeView = "CBOW";
          
          this.navigateToGov();

        }

      }

    });

    if (this.router.url.includes('lcdp-outlet'))
      this.router.navigate(['../'], { relativeTo: this.activatedRoute });

  }

  async openUdrfModal() {

    const { UdrfModalComponent } = await import('src/app/modules/shared-lazy-loaded-components/udrf-modal/udrf-modal.component');

    this.dialog.open(UdrfModalComponent, {
      minWidth: "100%",
      height: "84%",
      position: { top: this.reportMode == "Normal Mode" ? '56px' : '0px', left: this.reportMode == "Normal Mode" ? '77px' : '0px' },
      disableClose: true
    });

  }

  firstTime = true;

  async retrieveFirstTimeData() {
  
    this.isSettingsLoading = false;

    for (let udrfFilterConfigItem of this.udrfFilterConfig) {

      let udrfFilterConfigItemData = _.where(this.udrfService.udrfData.mainFilterArray, { filterId: udrfFilterConfigItem.filterId });

      if (udrfFilterConfigItemData.length > 0 && udrfFilterConfigItemData[0].multiOptionSelectSearchValuesWithId.length > 0) {

        udrfFilterConfigItem.filterIds = udrfFilterConfigItemData[0].multiOptionSelectSearchValuesWithId;

        udrfFilterConfigItem.filterNames = udrfFilterConfigItemData[0].multiOptionSelectSearchValues;

        if (udrfFilterConfigItem.filterKeyValues != null) {

          udrfFilterConfigItem.filterKeyValues = [];

          for (let filterId of udrfFilterConfigItem.filterIds) {

            let currentFilterIdValue = _.where(udrfFilterConfigItemData[0].masterData, { [udrfFilterConfigItem.idKey]: filterId });

            if (currentFilterIdValue.length > 0)
              udrfFilterConfigItem.filterKeyValues = udrfFilterConfigItem.filterKeyValues.concat(currentFilterIdValue[0][udrfFilterConfigItem.filterKey]);

          }

        }

      }

      else {

        udrfFilterConfigItem.filterIds = [];

        udrfFilterConfigItem.filterNames = [];

        if (udrfFilterConfigItem.filterKeyValues != null)
          udrfFilterConfigItem.filterKeyValues = [];

      }

    }

    if (this.firstTime) {

      if(this.isMISPostingDateDefault){
        this.activeFtmStartDate = moment(this.misPostingPeriod['start_date']).format(this.dateFormat);
        this.activeFtmEndDate = moment(this.misPostingPeriod['end_date']).format(this.dateFormat);
      }
      if(this.isDisplayFullValue){
        if (!this.udrfService.udrfData.appliedConfig["customFields"]) {
          this.udrfService.udrfData.appliedConfig = {
            customFields: {}
          };
        }
        this.udrfService.udrfData.appliedConfig["customFields"]["isFullValue"] = true
      }
      if(this.isDefaultCurrencyUSD){
        if (!this.udrfService.udrfData.appliedConfig["customFields"]) {
          this.udrfService.udrfData.appliedConfig = {
            customFields: {}
          };
        }
        this.udrfService.udrfData.appliedConfig["customFields"]["defaultCurrency"] = "USD" 
      }

      this.firstTime = false;

      this.userRoleOrgCodes = await this.rolesService.getUserRoleNgrOrgCodesByAppId(this.applicationId, [167, 168, 169, 170, 171, 176, 29365]);

      this.userRolePostingTypeIds = await this.rolesService.getUserRolePostingTypes(this.applicationId);

      let customFields: any;

      if (this.udrfService.udrfData.udrfDefaultSummaryCardCodes)
        customFields = this.udrfService.udrfData.udrfDefaultSummaryCardCodes;

      else
        customFields = this.localUdrfData;

      console.log("retrieveFirstTimeData first time data : ",customFields)

      if (!this.udrfService.udrfData.appliedConfig["customFields"])
        this.udrfService.udrfData.appliedConfig["customFields"] = customFields;

      for (let [localUdrfDataItemKey, localUdrfDataItem] of Object.entries(this.localUdrfData))
        if (!this.udrfService.udrfData.appliedConfig["customFields"][localUdrfDataItemKey])
          if (this.udrfService.udrfData.udrfDefaultSummaryCardCodes[localUdrfDataItemKey])
            this.udrfService.udrfData.appliedConfig["customFields"][localUdrfDataItemKey] = this.udrfService.udrfData.udrfDefaultSummaryCardCodes[localUdrfDataItemKey];
          else
            this.udrfService.udrfData.appliedConfig["customFields"][localUdrfDataItemKey] = this.localUdrfData[localUdrfDataItemKey];

      if (!this.udrfService.udrfData.appliedConfig["customFields"]["defaultCurrency"])
        if (this.misFlags["defaultCurrency"])
          this.udrfService.udrfData.appliedConfig["customFields"]["defaultCurrency"] = this.misFlags["defaultCurrency"];
        else {
          // this.udrfService.udrfData.appliedConfig["customFields"]["defaultCurrency"] = this.localUdrfData["defaultCurrency"];
          this.udrfService.udrfData.appliedConfig["customFields"]["defaultCurrency"] = this.misFlags["defaultCurrency"];

        }

      this.allNgrReportTypes = _.filter(this.misFlags.ngrReportTypes, function (ngrReportTypesItem) {

        if (this.userRolePostingTypeIds == "ALL" || _.contains(this.userRolePostingTypeIds, ngrReportTypesItem.posting_type_id))
          return ngrReportTypesItem;

      }, this);

      if (this.udrfService.udrfData.appliedConfig["customFields"]["ngrReportTypes"] && this.udrfService.udrfData.appliedConfig["customFields"]["ngrReportTypes"].length > 0) {

        for (let allNgrReportType of this.allNgrReportTypes)
          allNgrReportType.to_display_in_ngr = false;

        for (let ngrReportType of this.udrfService.udrfData.appliedConfig["customFields"]["ngrReportTypes"]) {

          let ngrReportTypeTemp = _.where(this.allNgrReportTypes, { posting_type_id: ngrReportType.posting_type_id });

          if (ngrReportTypeTemp.length > 0)
            ngrReportTypeTemp[0].to_display_in_ngr = ngrReportType.to_display_in_ngr;

        }

      }

      if (!this.udrfService.udrfData.appliedConfig["customFields"]["isFullValue"] && this.udrfService.udrfData.appliedConfig["customFields"]["isFullValue"] != false)
        this.determineIsFullValue();

      this.changeCardSizeAndConfigs();

      this.calculateIfDisplayInMisNgrIsZeroCards();

      this.udrfService.udrfData.appliedConfig["customFields"]["misCards"] = this.udrfService.udrfData.appliedConfig["customFields"]["misCards"].sort((a, b) => {
        if (a.order < b.order)
          return -1;
        else if (a.order > b.order)
          return 1;
        else
          return 0;
      });

      this.udrfService.udrfData.appliedConfig["customFields"]["misOpportunityCards"] = this.udrfService.udrfData.appliedConfig["customFields"]["misOpportunityCards"].sort((a, b) => {
        if (a.order < b.order)
          return -1;
        else if (a.order > b.order)
          return 1;
        else
          return 0;
      });

      this.udrfService.udrfData.appliedConfig["customFields"]["gbmCards"] = this.udrfService.udrfData.appliedConfig["customFields"]["gbmCards"].sort((a, b) => {
        if (a.order < b.order)
          return -1;
        else if (a.order > b.order)
          return 1;
        else
          return 0;
      });

      this.updateMisFlags();

      this.retrieveMISFTYData();

    }

    else if (!this.isFromReset)
      this.callApisAgain();

  }

  shouldDisplayMis(postingTypeId) {

    let postingTypeData = _.where(this.allNgrReportTypes, { posting_type_id: postingTypeId });

    return postingTypeData && postingTypeData.length > 0 && postingTypeData[0].to_display_in_ngr ? true : false;

  }

  calculateIfDisplayInMisNgrIsZeroCards() {

    let allNgrReportTypes = _.filter(this.allNgrReportTypes, function (allNgrReportType) {

      if (allNgrReportType.to_display_in_ngr == true || allNgrReportType.to_display_in_ngr == 1)
        return allNgrReportType;

    });

    let hasEnteredOnce = false;

    if (allNgrReportTypes.length == 0) {

      let ngrReportTypeTempRevenue = _.where(this.allNgrReportTypes, { posting_type_id: this.misFlags.reportRevenueTypeId });

      if (ngrReportTypeTempRevenue.length > 0) {

        ngrReportTypeTempRevenue[0].to_display_in_ngr = true;
        hasEnteredOnce = true;

      }

      let ngrReportTypeTempCost = _.where(this.allNgrReportTypes, { posting_type_id: this.misFlags.reportCostTypeId });

      if (ngrReportTypeTempCost.length > 0) {

        ngrReportTypeTempCost[0].to_display_in_ngr = true;
        hasEnteredOnce = true;

      }

      let ngrReportTypeTempGm = _.where(this.allNgrReportTypes, { posting_type_id: this.misFlags.reportGmTypeId });

      if (ngrReportTypeTempGm.length > 0) {

        ngrReportTypeTempGm[0].to_display_in_ngr = true;
        hasEnteredOnce = true;

      }

      let ngrReportTypeTempSga = _.where(this.allNgrReportTypes, { posting_type_id: this.misFlags.reportSgaTypeId });

      if (ngrReportTypeTempSga.length > 0) {

        ngrReportTypeTempSga[0].to_display_in_ngr = true;
        hasEnteredOnce = true;

      }

      let ngrReportTypeTempPbt = _.where(this.allNgrReportTypes, { posting_type_id: this.misFlags.reportPbtTypeId });

      if (ngrReportTypeTempPbt.length > 0) {

        ngrReportTypeTempPbt[0].to_display_in_ngr = true;
        hasEnteredOnce = true;

      }

      if (!hasEnteredOnce && this.allNgrReportTypes.length > 0)
        this.allNgrReportTypes[0].to_display_in_ngr = true;

    }

  }

  determineIsFullValue() {
    if (this.udrfService.udrfData.appliedConfig["customFields"]["defaultCurrency"] == "INR") {

      if (this.misFlags.divByDuringRetrievalForInrNgr == 1)
        this.udrfService.udrfData.appliedConfig["customFields"]["isFullValue"] = false;
      else
        this.udrfService.udrfData.appliedConfig["customFields"]["isFullValue"] = true;

    }

    else if (this.udrfService.udrfData.appliedConfig["customFields"]["defaultCurrency"] == "USD") {

      if (this.misFlags.divByDuringRetrievalForUsdNgr == 1)
        this.udrfService.udrfData.appliedConfig["customFields"]["isFullValue"] = false;
      else
        this.udrfService.udrfData.appliedConfig["customFields"]["isFullValue"] = true;

    }

  }

  updateMisFlags() {

    this.misFlags.divByDuringRetrievalForInrNgr = this.udrfService.udrfData.appliedConfig["customFields"]["isFullValue"] ? 1 : 10000000;
    this.misFlags.divByDuringRetrievalForUsdNgr = this.udrfService.udrfData.appliedConfig["customFields"]["isFullValue"] ? 1 : 1000000;

  }

  calculateNoOfAdditions() {

    let noOfAdditions = 0;

    if (this.udrfService.udrfData.appliedConfig["customFields"]["isMisBudgetVisible"] == 'Yes')
      noOfAdditions++;

    if (this.udrfService.udrfData.appliedConfig["customFields"]["isFullValue"])
      noOfAdditions++;

    if (this.udrfService.udrfData.appliedConfig["customFields"]["isMisVarianceVisible"] == 'Yes')
      noOfAdditions++;

    return noOfAdditions;

  }

  changeCardSizeAndConfigs() {

    let misCards: any, misOpportunityCards: any;

    misCards = this.determineMisCards(misCards, "misCards");
    misOpportunityCards = this.determineMisCards(misOpportunityCards, "misOpportunityCards");

    let gbmCards: any;

    if (this.udrfService.udrfData.udrfDefaultSummaryCardCodes["gbmCards"])
      gbmCards = this.udrfService.udrfData.udrfDefaultSummaryCardCodes["gbmCards"];

    else
      gbmCards = this.localUdrfData.gbmCards;

    for (let gbmCardsItem of _.where(gbmCards, { isVisible: true })) {

      let missingCard = _.where(this.udrfService.udrfData.appliedConfig["customFields"]["gbmCards"], { dataTypeCode: gbmCardsItem.dataTypeCode });

      if (missingCard && missingCard.length == 0)
        this.udrfService.udrfData.appliedConfig["customFields"]["gbmCards"].push(gbmCardsItem);

    }

    for (let gbmCardsItem of gbmCards)
      for (let gbmCardsTempItem of this.udrfService.udrfData.appliedConfig["customFields"]["gbmCards"])
        if (gbmCardsItem.dataTypeCode == gbmCardsTempItem.dataTypeCode) {

          gbmCardsTempItem.isVisible = gbmCardsItem.isVisible;
          gbmCardsTempItem.dataTypeName = gbmCardsItem.dataTypeName ? gbmCardsItem.dataTypeName : gbmCardsItem.dataTypeCode;
          gbmCardsTempItem.gbmName = gbmCardsItem.gbmName ? gbmCardsItem.gbmName : gbmCardsTempItem.dataTypeName;

        }

    let otherCards: any;

    if (this.udrfService.udrfData.udrfDefaultSummaryCardCodes["otherCards"])
      otherCards = this.udrfService.udrfData.udrfDefaultSummaryCardCodes["otherCards"];

    else
      otherCards = this.localUdrfData.otherCards;

    for (let otherCardsItem of otherCards) {

      let userConfigCardPresent = false;

      for (let otherCardsItemVal of this.udrfService.udrfData.appliedConfig["customFields"]["otherCards"])
        if (otherCardsItemVal.id == otherCardsItem.id) {

          userConfigCardPresent = true;
          otherCardsItemVal.isVisible = otherCardsItem.isVisible;
          otherCardsItemVal.header = otherCardsItem.header;

        }

      if (!userConfigCardPresent)
        this.udrfService.udrfData.appliedConfig["customFields"]["otherCards"].push(otherCardsItem);

    }

    this.determineCardLength("misCards", "misCardClass", "misDetailedModeCardClass", "visibleMisCardsModulo");

    this.addMisCards("misCards", this.visibleMisCardsModulo, "MIS");

    this.otherCardsFlags = {
      isCoVisible: _.where(this.udrfService.udrfData.appliedConfig["customFields"]["otherCards"], { id: "CO", isChecked: true }).length > 0,
      isBiVisible: _.where(this.udrfService.udrfData.appliedConfig["customFields"]["otherCards"], { id: "BI", isChecked: true }).length > 0,
      isObvVisible: _.where(this.udrfService.udrfData.appliedConfig["customFields"]["otherCards"], { id: "OBV", isChecked: true }).length > 0,
      isWcdVisible: _.where(this.udrfService.udrfData.appliedConfig["customFields"]["otherCards"], { id: "WCD", isChecked: true }).length > 0,
      isBbrVisible: _.where(this.udrfService.udrfData.appliedConfig["customFields"]["otherCards"], { id: "BBR", isChecked: true }).length > 0,
      isCtaVisible: _.where(this.udrfService.udrfData.appliedConfig["customFields"]["otherCards"], { id: "CTA", isChecked: true }).length > 0,
      isPmoDbVisible: _.where(this.udrfService.udrfData.appliedConfig["customFields"]["otherCards"], { id: "Projects", isChecked: true }).length > 0
    }

  }

  determineMisCards(misCards, cardType) {

    if (this.udrfService.udrfData.udrfDefaultSummaryCardCodes[cardType] != null)
      misCards = this.udrfService.udrfData.udrfDefaultSummaryCardCodes[cardType];

    else
      misCards = this.localUdrfData[cardType];

    for (let misCardsItem of _.where(misCards, { isVisible: true })) {

      let missingCard = _.where(this.udrfService.udrfData.appliedConfig["customFields"][cardType], { dataTypeCode: misCardsItem.dataTypeCode });

      if (missingCard && missingCard.length == 0)
        this.udrfService.udrfData.appliedConfig["customFields"][cardType].push(misCardsItem);

    }

    for (let misCardsItem of misCards)
      for (let misCardsTempItem of this.udrfService.udrfData.appliedConfig["customFields"][cardType])
        if (misCardsItem.dataTypeCode == misCardsTempItem.dataTypeCode) {

          misCardsTempItem.isVisible = misCardsItem.isVisible;
          misCardsTempItem.dataTypeName = misCardsItem.dataTypeName ? misCardsItem.dataTypeName : misCardsItem.dataTypeCode;
          misCardsTempItem.dataTypeDescription = misCardsItem.dataTypeDescription ? misCardsItem.dataTypeDescription : misCardsTempItem.dataTypeName;

        }

    return misCards;

  }

  determineCardLength(cardType, misCardClassName, misDetailedModeCardClassName, visibleMisCardsModuloName) {

    if (this.udrfService.udrfData.appliedConfig["customFields"][cardType + "Size"] == "F") {

      this[misCardClassName] = "col-11-5";
      this[misDetailedModeCardClassName] = "col-1-16-5";
      this[visibleMisCardsModuloName] = 1;

    }

    else if (this.udrfService.udrfData.appliedConfig["customFields"][cardType + "Size"] == "H") {

      this[misCardClassName] = "col-5-5";
      this[misDetailedModeCardClassName] = "col-1-10-5";
      this[visibleMisCardsModuloName] = 2;

    }

    else {

      this[misCardClassName] = "col-3-5";
      this[misDetailedModeCardClassName] = "col-1-7-5";
      this[visibleMisCardsModuloName] = 3;

    }

    if (this.udrfService.udrfData.appliedConfig["customFields"]["cboCardsSize"] == "F") {

      this.cboCardClass = "col-11-5";
      this.cboCardFirstColClass = "col-3-4";

    }

    else {

      this.cboCardClass = "col-3-5";
      this.cboCardFirstColClass = "col-3-3";

    }

    if (!this.udrfService.udrfData.appliedConfig["customFields"]["isFullValue"]) {

      if (this.udrfService.udrfData.appliedConfig["customFields"]["isMisBudgetVisible"] == 'Yes')
        this.misCardColClass = 'col-1-9-2';

      else
        this.misCardColClass = 'col-1-9';

      this.cboCardColClass = "col-1-9-2";

      this.misOpportunityColCardClass = "col-1-9";

    }

    else {

      if (this.udrfService.udrfData.appliedConfig["customFields"]["isMisBudgetVisible"] == 'Yes')
        this.misCardColClass = 'col-1-9-5';

      else
        this.misCardColClass = 'col-1-9-2';

      this.cboCardColClass = "col-1-9-3";

      this.misOpportunityColCardClass = "col-1-9-2";

    }

    let noOfAdditions = this.calculateNoOfAdditions();

    if (noOfAdditions > 1)
      this[misDetailedModeCardClassName] = (misDetailedModeCardClassName == "misOpportunityDetailedModeCardClass") ? "col-1-10-5" : "col-1-16-5";

    else if (noOfAdditions > 0)
      this[misDetailedModeCardClassName] = "col-1-10-5";

    else
      this[misDetailedModeCardClassName] = "col-1-7-5";

    if (cardType != "misOpportunityCards")
      this.determineCardLength("misOpportunityCards", "misOpportunityCardClass", "misOpportunityDetailedModeCardClass", "visibleMisOpportunityCardsModulo");

  }

  addMisCards(cardType, modulo, cardName) {

    let noOfCardsAdded = 0, boolVal = true, iterationCount = 0;

    let isCheckedCount = _.where(this.udrfService.udrfData.appliedConfig["customFields"][cardType], { isVisible: true, isSelected: true }).length;

    while (isCheckedCount != 0 && isCheckedCount % modulo != 0) {

      let hasAddedInThisIteration = false;

      for (let i = 0; i < this.udrfService.udrfData.appliedConfig["customFields"][cardType].length && !hasAddedInThisIteration; i++)
        if ((boolVal && !this.udrfService.udrfData.appliedConfig["customFields"][cardType][i].isSelected) || (!boolVal && this.udrfService.udrfData.appliedConfig["customFields"][cardType][i].isSelected)) {

          this.udrfService.udrfData.appliedConfig["customFields"][cardType][i].isSelected = boolVal;

          if (boolVal) {

            isCheckedCount++;
            noOfCardsAdded++;

          }

          else {

            isCheckedCount--;
            noOfCardsAdded--;

          }

          hasAddedInThisIteration = true;

        }

      iterationCount++;

      if (iterationCount >= this.udrfService.udrfData.appliedConfig["customFields"][cardType].length)
        boolVal = false;

    }

    if (noOfCardsAdded != 0)
      this.utilityService.showToastMessage(Math.abs(noOfCardsAdded) + " " + cardName + " card(s) have been toggled " + (noOfCardsAdded > 0 ? "on" : "off") + " to align to the design. You may customize as needed again.");

    this.otherCardsFlags = {
      isCoVisible: _.where(this.udrfService.udrfData.appliedConfig["customFields"]["otherCards"], { id: "CO", isChecked: true }).length > 0,
      isBiVisible: _.where(this.udrfService.udrfData.appliedConfig["customFields"]["otherCards"], { id: "BI", isChecked: true }).length > 0,
      isObvVisible: _.where(this.udrfService.udrfData.appliedConfig["customFields"]["otherCards"], { id: "OBV", isChecked: true }).length > 0,
      isWcdVisible: _.where(this.udrfService.udrfData.appliedConfig["customFields"]["otherCards"], { id: "WCD", isChecked: true }).length > 0,
      isBbrVisible: _.where(this.udrfService.udrfData.appliedConfig["customFields"]["otherCards"], { id: "BBR", isChecked: true }).length > 0,
      isCtaVisible: _.where(this.udrfService.udrfData.appliedConfig["customFields"]["otherCards"], { id: "CTA", isChecked: true }).length > 0,
      isPmoDbVisible: _.where(this.udrfService.udrfData.appliedConfig["customFields"]["otherCards"], { id: "Projects", isChecked: true }).length > 0
    }

    if (cardType != "misOpportunityCards")
      this.addMisCards("misOpportunityCards", this.visibleMisOpportunityCardsModulo, "MIS + Opportunity");

  }

  toShowCbo(resolvedCBOData) {

    let resolvedCBODataFinal = [];

    for (let resolvedCBODataItem of resolvedCBOData)
      if ((resolvedCBODataItem.type == 'Collection' && this.otherCardsFlags.isCoVisible) || (resolvedCBODataItem.type == 'Billing' &&
        this.otherCardsFlags.isBiVisible) || (resolvedCBODataItem.type == 'OBV' && this.otherCardsFlags.isObvVisible))
        resolvedCBODataFinal.push(resolvedCBODataItem);

    return resolvedCBODataFinal;

  }

  async openSettingsModal() {

    console.log("Settings Config  : ",this.udrfService.udrfData.appliedConfig['customFields'])
    if (!this.udrfService.udrfData.appliedConfig.hasOwnProperty('customFields') && this.govUdrfData.hasOwnProperty('appliedConfig'))
      this.udrfService.udrfData.appliedConfig = this.govUdrfData['appliedConfig'];

    let previousDefaultCurrency = JSON.parse(JSON.stringify(this.udrfService.udrfData.appliedConfig["customFields"]["defaultCurrency"]));

    let previousMisLiveData = JSON.parse(JSON.stringify(this.udrfService.udrfData.appliedConfig["customFields"]["misLiveData"]));

    let udrfSettings = this.constructUdrfSettings();

    let settingsDetails: any;

    if (this.udrfService.udrfData.udrfDefaultSummaryCardCodes["settingsDetails"])
      settingsDetails = this.udrfService.udrfData.udrfDefaultSummaryCardCodes["settingsDetails"];

    else
      settingsDetails = this.localUdrfData.settingsDetails;

    for (let settingsDetail of settingsDetails)
      if (settingsDetail.id) {

        let udrfCurrentSetting = _.where(udrfSettings, { id: settingsDetail.id });

        if (udrfCurrentSetting && udrfCurrentSetting.length > 0) {

          if (settingsDetail.header)
            udrfCurrentSetting[0]["header"] = settingsDetail.header;

          if (settingsDetail.description)
            udrfCurrentSetting[0]["description"] = settingsDetail.description;

          if (settingsDetail.isVisible)
            udrfCurrentSetting[0]["isVisible"] = settingsDetail.isVisible == "Yes";

          if (settingsDetail.order)
            udrfCurrentSetting[0]["order"] = settingsDetail.order;

          if (settingsDetail.minCheckedCards)
            udrfCurrentSetting[0]["minCheckedCards"] = settingsDetail.minCheckedCards;

          if (settingsDetail.maxCheckedCards)
            udrfCurrentSetting[0]["maxCheckedCards"] = settingsDetail.maxCheckedCards;

          if (settingsDetail.items && settingsDetail.items.length > 0) {

            for (let item of settingsDetail.items)
              if (item.id) {

                let udrfCurrentItemSetting = _.where(udrfCurrentSetting[0]["items"], { id: item.id });

                if (udrfCurrentItemSetting && udrfCurrentItemSetting.length > 0) {

                  if (item.header)
                    udrfCurrentItemSetting[0]["header"] = item.header;

                  if (item.description)
                    udrfCurrentItemSetting[0]["description"] = item.description;

                }

              }

          }

        }

      }

    let modalParams = {
      udrfData: this.udrfService.udrfData,
      udrfSettings: udrfSettings
    }

    const { UdrfNewSettingsModalComponent } = await import('src/app/modules/shared-lazy-loaded-components/udrf-new-settings-modal/udrf-new-settings-modal.component');

    const openUdrfNewSettingsModalComponent = this.dialog.open(UdrfNewSettingsModalComponent, {
      height: '100%',
      width: '95%',
      maxWidth: '95%',
      minWidth: '95%',
      position: { right: '0px' },
      panelClass: 'udrf-new-settings-modal',
      data: { modalParams: modalParams }
    });

    openUdrfNewSettingsModalComponent.afterClosed().subscribe(async res => {

      if (res.event == "Save") {

        // Value Transfers

        for (let udrfSetting of res.modalResult.udrfSettings) {

          if (udrfSetting.isValueTransferable)
            this.udrfService.udrfData.appliedConfig["customFields"][udrfSetting.fieldName] = this.returnYesOrTrue(udrfSetting, 'value');

          if (udrfSetting.isArrayOfChecks)
            for (let item of udrfSetting.items)
              this.udrfService.udrfData.appliedConfig["customFields"][item.fieldName] = this.returnYesOrTrue(item, 'isChecked');

          if (udrfSetting.isCheckArray) {

            for (let item of udrfSetting.items) {

              let currentItem = [];

              if (udrfSetting.isRootConfigField)
                currentItem = _.where(this[udrfSetting.arrayName], { [udrfSetting.fieldName]: item.header });

              else
                currentItem = _.where(this.udrfService.udrfData.appliedConfig["customFields"][udrfSetting.arrayName], { [udrfSetting.fieldName]: item.header });

              if (currentItem && currentItem.length > 0) {

                currentItem[0][udrfSetting.checkField] = item.isChecked;

                if (udrfSetting.type == "checkbox-with-sort")
                  currentItem[0][udrfSetting.orderField] = item.order;

              }

            }

            if (udrfSetting.type == "checkbox-with-sort")
              if (udrfSetting.isRootConfigField)
                this[udrfSetting.arrayName] = this[udrfSetting.arrayName].sort((a, b) => {
                  if (a[udrfSetting.orderField] < b[udrfSetting.orderField])
                    return -1;
                  else if (a[udrfSetting.orderField] > b[udrfSetting.orderField])
                    return 1;
                  else
                    return 0;
                });
              else
                this.udrfService.udrfData.appliedConfig["customFields"][udrfSetting.arrayName] = this.udrfService.udrfData.appliedConfig["customFields"][udrfSetting.arrayName].sort((a, b) => {
                  if (a[udrfSetting.orderField] < b[udrfSetting.orderField])
                    return -1;
                  else if (a[udrfSetting.orderField] > b[udrfSetting.orderField])
                    return 1;
                  else
                    return 0;
                });

          }

        }

        if (this.udrfService.udrfData.appliedConfig["customFields"]["isMisBudgetVisible"] == "No")
          this.udrfService.udrfData.appliedConfig["customFields"]["isMisVarianceVisible"] = this.udrfService.udrfData.appliedConfig["customFields"]["isMisBudgetVisible"];

        // Condition Checks - 2

        this.changeCardSizeAndConfigs();

        this.updateMisFlags();

        this.calculateIfDisplayInMisNgrIsZeroCards();

        if (previousDefaultCurrency != this.udrfService.udrfData.appliedConfig["customFields"]["defaultCurrency"] ||
          previousMisLiveData != this.udrfService.udrfData.appliedConfig["customFields"]["misLiveData"])
          this.misDataYearWise = [];

          console.log("Update UDRF Config Data : ",this.udrfService.udrfData.appliedConfig)

          this.udrfService.updateUdrfUserConfig(this.udrfService.udrfData).subscribe(async res1 => {

          this.udrfService.udrfData.userConfigId = res1["userConfigId"];

          this.callApisAgain();

        }, err => {

          let errReportingTeams = "KEBS";

          this.utilityService.showErrorMessage(err, errReportingTeams);

        });

      }

    });

  }

  callApisAgain() {

    this.isBBDataRetrieved = false;
    this.isCBOFTMDataRetrieved = false;
    this.pmoDb.isProjectsDataRetrieved = false;

    this.spacingKeys = {};

    this._repeatCall.next();

    if (this.reportMode == "Detailed Mode")
      this.callAllDetailedModeFns();

    else
      this.callAllNormalModeFns();

  }

  callAllDetailedModeFns() {

    this.resolveRetrievedMisData();

    this.retrieveProjectsData();

    this.callAllNormalModeFns();

  }

  callAllNormalModeFns() {

    this.resetNonDetailedModeData()
    this.retrieveCBOData();

    // this.retrieveWCDData();

    // this.retrieveArAgingData();

    // let endDate = moment(this.upcoming3MonthsEndDate).isAfter(this.ftyEndDate) ? this.upcoming3MonthsEndDate : this.ftyEndDate;

    // this.retrieveBBData(this.ftyStartDate, endDate);

    // this.retrieveCTAFTMData();

  }

  returnYesOrTrue(udrfSetting, field) {

    if (udrfSetting.isBasedOnYes)
      return udrfSetting[field] ? "Yes" : "No";

    else
      return udrfSetting[field];

  }

  constructUdrfSettings() {

    console.log("Applied UDRF SEtting Value : ",this.udrfService.udrfData.appliedConfig["customFields"])
    
    let udrfSettings = [];

    let categoryCards = ["MIS Widgets", "MIS Data Types", "MIS + Opportunity Widgets", "GBM Widgets"];

    for (let categoryCard of categoryCards) {

      let udrfSetting = {};

      if (categoryCard == "MIS Widgets") {

        let settingArray = _.where(this.udrfService.udrfData.appliedConfig["customFields"]["misCards"], { isVisible: true });

        let settingFields = {
          header: "dataTypeName",
          description: "dataTypeDescription",
          isChecked: "isSelected",
          order: "order"
        }

        udrfSetting = {
          id: categoryCard,
          header: categoryCard,
          type: "checkbox-with-sort",
          items: this.assimilateUdrfSettingArray(settingArray, settingFields),
          order: 8,
          isVisible: true,
          isCheckArray: true,
          fieldName: "dataTypeName",
          checkField: "isSelected",
          orderField: "order",
          arrayName: "misCards",
          additionalClass: "pl-3 pr-0"
        };

      }

      else if (categoryCard == "MIS Data Types") {

        let settingArray = this.allNgrReportTypes;

        let settingFields = {
          header: "description",
          isChecked: "to_display_in_ngr"
        }

        udrfSetting = {
          id: categoryCard,
          header: categoryCard,
          type: "checkbox",
          items: this.assimilateUdrfSettingArray(settingArray, settingFields),
          order: 7,
          isVisible: true,
          isCheckArray: true,
          fieldName: "description",
          checkField: "to_display_in_ngr",
          arrayName: "allNgrReportTypes",
          isRootConfigField: true,
          additionalClass: "pl-0 pr-3 bor-righ-grey"
        };

      }

      else if (categoryCard == "MIS + Opportunity Widgets") {

        let settingArray = _.where(this.udrfService.udrfData.appliedConfig["customFields"]["misOpportunityCards"], { isVisible: true });

        let settingFields = {
          header: "dataTypeName",
          description: "dataTypeDescription",
          isChecked: "isSelected",
          order: "order"
        }

        udrfSetting = {
          id: categoryCard,
          header: categoryCard,
          type: "checkbox-with-sort",
          items: this.assimilateUdrfSettingArray(settingArray, settingFields),
          order: 9,
          isVisible: true,
          isCheckArray: true,
          fieldName: "dataTypeName",
          checkField: "isSelected",
          orderField: "order",
          arrayName: "misOpportunityCards",
          additionalClass: "pl-0 pr-3 bor-righ-grey"
        };

      }

      else if (categoryCard == "GBM Widgets") {

        let settingArray = _.where(this.udrfService.udrfData.appliedConfig["customFields"]["gbmCards"], { isVisible: true });

        let settingFields = {
          header: "gbmName",
          isChecked: "isSelected",
          order: "order"
        }

        udrfSetting = {
          id: categoryCard,
          header: categoryCard,
          type: "checkbox-with-sort",
          items: this.assimilateUdrfSettingArray(settingArray, settingFields),
          order: 10,
          isVisible: true,
          isCheckArray: true,
          fieldName: "gbmName",
          checkField: "isSelected",
          orderField: "order",
          arrayName: "gbmCards",
          minCheckedCards: 0,
          maxCheckedCards: 6,
          additionalClass: "pl-3 pr-0"
        };

      }

      udrfSettings.push(udrfSetting);

    }

    let settingArray = _.where(this.udrfService.udrfData.appliedConfig["customFields"]["otherCards"], { isVisible: true });

    let settingFields = {
      id: "id",
      header: "header",
      isChecked: "isChecked"
    }

    udrfSettings.push({
      id: "Other Widgets In Widget View",
      header: "Other Widgets In Widget View",
      type: "checkbox",
      items: this.assimilateUdrfSettingArray(settingArray, settingFields),
      order: 11,
      isVisible: true,
      isCheckArray: true,
      fieldName: "header",
      checkField: "isChecked",
      arrayName: "otherCards",
      additionalClass: "pl-0 pr-3 bor-righ-grey"
    });


    udrfSettings.push({
      id: "CBO Widget Size",
      header: "CBO Widget Size",
      type: "radio",
      value: this.udrfService.udrfData.appliedConfig["customFields"]["cboCardsSize"],
      items: [{
        id: "Minimal",
        header: "Minimal",
        value: "M"
      },
      {
        id: "Full",
        header: "Full Screen",
        value: "F"
      }],
      order: 3,
      isVisible: true,
      fieldName: "cboCardsSize",
      isValueTransferable: true,
      additionalClass: "pl-0 pr-3 bor-righ-grey",
      additionalItemClass: "mr-4"
    });

    udrfSettings.push({
      id: "View On Report Load",
      header: "View On Report Load",
      type: "radio",
      value: this.udrfService.udrfData.appliedConfig["customFields"]["defaultReportView"],
      items: [{
        id: "Card",
        header: "Card",
        value: "Card",
        description: "Collection, Billing, OBV, WCD and CTA in Cards"
      },
      {
        id: "Widget",
        header: "Widget",
        value: "Widget",
        description: "MIS, Collection, Billing, OBV, WCD, BBR and CTA in Widgets"
      }],
      order: 6,
      isVisible: true,
      fieldName: "defaultReportView",
      isValueTransferable: true,
      additionalClass: "pl-3 pr-0",
      additionalItemClass: "mr-4"
    });

    // let projectVisibleColumnsSettingArray = this.pmoDb.pmoDataKeys;

    // let projectVisibleColumnsSettingFields = {
    //   header: "description",
    //   isChecked: "isVisibleInWidget"
    // }

    // let projectVisibleColumnsSettingUdrfSetting = {
    //   id: "Projects Widget Types",
    //   header: "Projects Widget Types",
    //   type: "checkbox-with-sort",
    //   items: this.assimilateUdrfSettingArray(projectVisibleColumnsSettingArray, projectVisibleColumnsSettingFields),
    //   order: 14,
    //   isVisible: true,
    //   isCheckArray: true,
    //   isNonObjectArray: true,
    //   fieldName: "description",
    //   checkField: "isVisibleInWidget",
    //   orderField: "order",
    //   arrayName: "projectVisibleColumns",
    //   isRootConfigField: false,
    //   additionalClass: "pl-3 pr-0",
    //   additionalItemClass: "mr-2"
    // };

    // udrfSettings.push(projectVisibleColumnsSettingUdrfSetting);

    udrfSettings.push({
      id: "MIS Widget Size",
      header: "MIS Widget Size",
      type: "radio",
      value: this.udrfService.udrfData.appliedConfig["customFields"]["misCardsSize"],
      items: [{
        id: "Minimal",
        header: "Minimal",
        value: "M"
      },
      {
        id: "Half",
        header: "Half Screen",
        value: "H"
      },
      {
        id: "Full",
        header: "Full Screen",
        value: "F"
      }],
      order: 1,
      isVisible: true,
      fieldName: "misCardsSize",
      isValueTransferable: true,
      additionalClass: "pl-0 pr-3 bor-righ-grey",
      additionalItemClass: "mr-2"
    });

    udrfSettings.push({
      id: "MIS + Opportunity Widget Size",
      header: "MIS + Opportunity Widget Size",
      type: "radio",
      value: this.udrfService.udrfData.appliedConfig["customFields"]["misOpportunityCardsSize"],
      items: [{
        id: "Minimal",
        header: "Minimal",
        value: "M"
      },
      {
        id: "Half",
        header: "Half Screen",
        value: "H"
      },
      {
        id: "Full",
        header: "Full Screen",
        value: "F"
      }],
      order: 2,
      isVisible: true,
      fieldName: "misOpportunityCardsSize",
      isValueTransferable: true,
      additionalClass: "pl-3 pr-0",
      additionalItemClass: "mr-2"
    });

    udrfSettings.push({
      id: "Overall PoC Calculation Method",
      header: "Overall PoC Calculation Method",
      type: "radio",
      value: this.udrfService.udrfData.appliedConfig["customFields"]["overallPoCCalcMethod"],
      items: [{
        id: "Average of PoCs",
        header: "Average of PoCs",
        value: "Average of PoCs"
      }, {
        id: "Based on Planned-Actual",
        header: "Based on Planned-Actual",
        value: "Based on Planned-Actual"
      }],
      order: 5,
      isVisible: true,
      fieldName: "overallPoCCalcMethod",
      isValueTransferable: true,
      additionalClass: "pl-0 pr-3 bor-righ-grey",
      additionalItemClass: "mr-4"
    });

    let currencyItems = [];

    for (let reportingCurrency of this.misFlags["reportingCurrencies"])
      currencyItems.push({
        header: reportingCurrency,
        value: reportingCurrency
      });

    udrfSettings.push({
      id: "Currency",
      header: "Currency",
      type: "radio",
      value: this.udrfService.udrfData.appliedConfig["customFields"]["defaultCurrency"],
      items: currencyItems,
      order: 4,
      isVisible: true,
      fieldName: "defaultCurrency",
      isValueTransferable: true,
      additionalClass: "pl-3 pr-0",
      additionalItemClass: "mr-4"
    });

    let otherSettingArray = [
      {
        id: "Data In Full Value",
        header: "Data In Full Value",
        isChecked: this.udrfService.udrfData.appliedConfig["customFields"]["isFullValue"],
        fieldName: "isFullValue"
      },
      {
        id: "Projects Overdue Data Visibility",
        header: "Projects Overdue Data Visibility",
        isChecked: this.udrfService.udrfData.appliedConfig["customFields"]["isProjectsOverdueVisible"],
        fieldName: "isProjectsOverdueVisible"
      },
      {
        id: "Budget in MIS",
        header: "Budget in MIS",
        isChecked: this.udrfService.udrfData.appliedConfig["customFields"]["isMisBudgetVisible"] == "Yes",
        fieldName: "isMisBudgetVisible",
        isBasedOnYes: true
      },
      {
        id: "Variance in MIS Budget",
        header: "Variance in MIS Budget",
        isChecked: this.udrfService.udrfData.appliedConfig["customFields"]["isMisVarianceVisible"] == "Yes",
        fieldName: "isMisVarianceVisible",
        isBasedOnYes: true
      },
      {
        id: "MIS Live Data",
        header: "MIS Live Data",
        isChecked: this.udrfService.udrfData.appliedConfig["customFields"]["misLiveData"],
        fieldName: "misLiveData"
      }];

    let otherSettingFields = {
      id: "id",
      header: "header",
      isChecked: "isChecked",
      fieldName: "fieldName",
      isBasedOnYes: "isBasedOnYes"
    }

    let otherUdrfSetting = {
      id: "Other Settings",
      header: "Other Settings",
      type: "checkbox",
      items: this.assimilateUdrfSettingArray(otherSettingArray, otherSettingFields),
      order: 12,
      isVisible: true,
      isArrayOfChecks: true,
      additionalClass: "pl-3 pr-0"
    };

    udrfSettings.push(otherUdrfSetting);

    udrfSettings.push({
      id: "Projects Widget Size",
      header: "Projects Widget Size",
      type: "radio",
      value: this.udrfService.udrfData.appliedConfig["customFields"]["projectsWidgetSize"],
      items: [
        {
          id: "Half",
          header: "Half Screen",
          value: "H"
        },
        {
          id: "Full",
          header: "Full Screen",
          value: "F"
        }],
      order: 13,
      isVisible: true,
      fieldName: "projectsWidgetSize",
      isValueTransferable: true,
      additionalClass: "pl-0 pr-3 bor-righ-grey",
      additionalItemClass: "mr-2"
    });

    return udrfSettings;

  }

  assimilateUdrfSettingArray(settingArray, settingFields) {

    let returnArray = [];

    for (let settingArrayItem of settingArray) {

      let returnArrayItem = {};

      for (let [settingFieldsItemKey, settingFieldsItem] of Object.entries(settingFields))
        returnArrayItem[settingFieldsItemKey] = settingArrayItem[settingFields[settingFieldsItemKey]];

      returnArray.push(returnArrayItem);

    }

    return returnArray;

  }

  calculatePoCTargetToDate() {

    let today = moment();

    let numberOfDaysInMonth = today.daysInMonth();

    let dailyTarget = 100 / numberOfDaysInMonth;

    let currentDate = today.date();

    if (moment(this.monthYearDate.value).isBefore(today, 'month'))
      this.poCTargetToDate = 100;

    else if (moment(this.monthYearDate.value).isAfter(today, 'month'))
      this.poCTargetToDate = 0;

    else
      this.poCTargetToDate = dailyTarget * currentDate;

  }

  plRollupWithDuplicates = [];

  async getPlRollup() {

    this.userRolePlIds = [];

    this.governanceReportSubService.getPlRollup(false)
      .pipe(takeUntil(this._onDestroy)).pipe(takeUntil(this._repeatCall))
      .subscribe(async res2 => {

        this.plRollupWithDuplicates = res2["data"];

        this.reportsService.getPlsOfOrgCodes(this.userRoleOrgCodes)
          .pipe(takeUntil(this._onDestroy)).pipe(takeUntil(this._repeatCall))
          .subscribe(async res => {

            console.log(this.userRoleOrgCodes)
            console.log(res)

            if (res["messType"] == "S")
              this.userRolePlIds = res["userRolePlIds"];

            this.governanceReportSubService.getPlRollup(true)
              .pipe(takeUntil(this._onDestroy)).pipe(takeUntil(this._repeatCall))
              .subscribe(async res1 => {

                this.legalEntitiesData = res1["data"];

                for (let legalEntitiesDataItem of this.legalEntitiesData)
                  if (legalEntitiesDataItem.rollupLevels.length == 0) {

                    this.highestPl = legalEntitiesDataItem.description;

                    this.detailedModeBaseLegalEntity = this.highestPl;
                    this.normalModeCBOWBaseLegalEntity = this.highestPl;
                    this.normalModeCTABaseLegalEntity = this.highestPl;

                  }

                let tempLegalEntities = [];

                this.legalEntities = [];

                this.legalEntitiesData = _.sortBy(this.legalEntitiesData, function (legalEntitiesDataItem) {
                  return legalEntitiesDataItem.rollupLevels.length;
                });

                for (let legalEntitiesDataItem of this.legalEntitiesData)
                  tempLegalEntities.push({
                    plId: legalEntitiesDataItem.plId,
                    name: legalEntitiesDataItem.description,
                    leLevel: legalEntitiesDataItem.rollupLevels.length,
                    rollup: legalEntitiesDataItem.rollupLevels.length > 0 ? (legalEntitiesDataItem.rollupLevels[0].rollUpPlName) : "",
                    rollupId: legalEntitiesDataItem.rollupLevels.length > 0 ? (legalEntitiesDataItem.rollupLevels[0].rollUpPlId) : 0,
                    isExpanded: true,
                    isVisible: true,
                    isExpandedOverridden: false,
                    Collection: {
                      poC: 0,
                      yetTo: 0,
                      yetToOriginal: 0
                    },
                    Billing: {
                      poC: 0,
                      yetTo: 0,
                      yetToOriginal: 0
                    },
                    OBV: {
                      poC: 0,
                      yetTo: 0,
                      yetToOriginal: 0
                    },
                    WCD: {
                      arDays: 0,
                      arAmount: 0,
                      arAmountOriginal: 0,
                      arAmountMain: 0,
                      ubrDays: 0,
                      ubrAmount: 0,
                      ubrAmountOriginal: 0,
                      ubrAmountMain: 0,
                      wcDays: 0,
                      wcAmount: 0,
                      wcAmountOriginal: 0,
                      retentionAmount: 0,
                      retentionAmountOriginal: 0,
                      arWoRetention: 0,
                      arWoRetentionOriginal: 0,
                      wcDaysWoRetention: 0,
                      wcWoRetention: 0,
                      wcWoRetentionOriginal: 0
                    },
                    completedCtaCount: 0,
                    totalCtaCount: 0
                  });

                for (let i = 0; i < tempLegalEntities.length; i++) {

                  let doesLegalEntityExist = _.where(this.legalEntities, { name: tempLegalEntities[i].name });

                  if (doesLegalEntityExist.length == 0) {

                    let rollupLegalEntities = _.where(tempLegalEntities, { rollup: tempLegalEntities[i].name });

                    if (rollupLegalEntities.length == 0)
                      tempLegalEntities[i].isExpandedOverridden = true;

                    this.legalEntities.push(tempLegalEntities[i]);

                    if (i != 0)
                      for (let rollupLegalEntitiesItem of rollupLegalEntities) {

                        let doesLowerLegalEntityExist = _.where(this.legalEntities, { name: rollupLegalEntitiesItem.name });

                        if (doesLowerLegalEntityExist.length == 0) {

                          let rollupLowerLegalEntities = _.where(tempLegalEntities, { rollup: rollupLegalEntitiesItem.name });

                          if (rollupLowerLegalEntities.length == 0)
                            rollupLegalEntitiesItem.isExpandedOverridden = true;

                          this.legalEntities.push(rollupLegalEntitiesItem);

                          for (let rollupLowerLegalEntitiesItem of rollupLowerLegalEntities) {

                            let rollupLowerSubLegalEntities = _.where(tempLegalEntities, { rollup: rollupLowerLegalEntitiesItem.name });

                            if (rollupLowerSubLegalEntities.length == 0)
                              rollupLowerLegalEntitiesItem.isExpandedOverridden = true;

                            this.legalEntities.push(rollupLowerLegalEntitiesItem);

                          }

                        }

                      }

                  }

                }

                this.calculateAuthLe();

                if (this.udrfService.udrfData.appliedConfig["customFields"]["defaultReportView"] == "Widget")
                  this.changeReportMode();

                else
                  this.callAllNormalModeFns();

              }, err => {
                this.showErrorMessage(err);
              });

          }, err => {
            this.showErrorMessage(err);
          });

      }, err => {
        this.showErrorMessage(err);
      });

  }

  collapseLegalEntity(currentlegalEntity) {

    for (let i = 0; i < this.authLegalEntities.length; i++) {

      if (this.authLegalEntities[i].name == currentlegalEntity.name)
        this.authLegalEntities[i].isExpanded = !this.authLegalEntities[i].isExpanded;

      else if (this.authLegalEntities[i].rollup == currentlegalEntity.name) {

        this.authLegalEntities[i].isVisible = !this.authLegalEntities[i].isVisible;

        for (let j = 0; j < this.authLegalEntities.length; j++)
          if (this.authLegalEntities[j].rollup == this.authLegalEntities[i].name && this.authLegalEntities[i].isExpanded) {

            this.authLegalEntities[j].isVisible = this.authLegalEntities[i].isVisible;

            for (let k = 0; k < this.authLegalEntities.length; k++)
              if (this.authLegalEntities[k].rollup == this.authLegalEntities[j].name && this.authLegalEntities[j].isExpanded)
                this.authLegalEntities[k].isVisible = this.authLegalEntities[j].isVisible;

          }

      }

    }

  }

  selectCBOWCard(cbowViewCardsItemIndex) {

    for (let i = 0; i < this.cbowViewCards.length; i++)
      this.cbowViewCards[i].selected = false;

    this.cbowViewCards[cbowViewCardsItemIndex].selected = true;
    this.selectedCard = this.cbowViewCards[cbowViewCardsItemIndex].dataTypeName
    
    if(this.selectedViewGroupMode != 'Region'){
      this.callOtherViewTypeCBODataType()
    }
    this.calculateAuthLe();

  }

  calculateAuthLe() {

    this.legalEntitiesLoading = true;

    this.authLegalEntities = JSON.parse(JSON.stringify(this.legalEntities));

    let objectId = 0;

    if (this.normalModeView == "CTA")
      objectId = 171;

    else {

      let currentCbowViewCard = _.where(this.cbowViewCards.length, { selected: true });

      objectId = currentCbowViewCard.length > 0 && currentCbowViewCard[0].dataType == "WCD" ? 169 : 168;

    }

    let currentCbowUserRolePlIds = _.where(this.userRolePlIds, { object_id: objectId });

    if (currentCbowUserRolePlIds && currentCbowUserRolePlIds.length > 0)
      currentCbowUserRolePlIds = currentCbowUserRolePlIds[0].pl_ids;

    if (currentCbowUserRolePlIds != "ALL") {

      if (currentCbowUserRolePlIds.length > 0)
        this.resolveAuthLegalEntities(currentCbowUserRolePlIds);

      else {

        this.authLegalEntities = [];
        this.legalEntitiesLoading = false;

      }

    }

    else
      this.legalEntitiesLoading = false;

  }

  resolveAuthLegalEntities(currentCbowUserRolePlIds) {

    let tempCurrentCbowUserRolePlIds = JSON.parse(JSON.stringify(currentCbowUserRolePlIds));

    this.authLegalEntities = _.filter(this.legalEntities, function (legalEntity) {

      if (legalEntity.plId != null && _.contains(currentCbowUserRolePlIds, legalEntity.plId))
        return legalEntity;

      else if (legalEntity.rollupId != null && legalEntity.rollupId != 0 && _.contains(currentCbowUserRolePlIds, legalEntity.rollupId))
        return legalEntity;

    }, this);

    let visiblePlIds = _.uniq(_.pluck(this.authLegalEntities, 'plId'));

    currentCbowUserRolePlIds = currentCbowUserRolePlIds.concat(visiblePlIds);

    currentCbowUserRolePlIds = _.uniq(currentCbowUserRolePlIds);

    if (tempCurrentCbowUserRolePlIds.length != currentCbowUserRolePlIds.length)
      this.resolveAuthLegalEntities(currentCbowUserRolePlIds);

    else
      this.legalEntitiesLoading = false;

  }

  selectCTACard(ctaViewCardsItemIndex) {

    for (let i = 0; i < this.ctaViewCards.length; i++)
      this.ctaViewCards[i].selected = false;

    this.ctaViewCards[ctaViewCardsItemIndex].selected = true;

    this.retrieveCTAFTMData();

  }

  async openOldGovernanceReportModal() {

    let modalParams = {
      userRolePlIds: this.userRolePlIds
    };

    if (!this.udrfService.udrfData.appliedConfig.hasOwnProperty('customFields') && this.govUdrfData.hasOwnProperty('appliedConfig'))
      this.udrfService.udrfData.appliedConfig = this.govUdrfData['appliedConfig'];

    const { GovRepOldReportModalComponent } = await import('../../lazy-loaded-components/gov-rep-old-report-modal/gov-rep-old-report-modal.component');

    this.dialog.open(GovRepOldReportModalComponent, {
      height: '100%',
      minWidth: '95%',
      position: { right: '0px' },
      data: { modalParams: modalParams, currDate: this.monthYearDate.value, isFullValue: this.udrfService.udrfData.appliedConfig["customFields"]["isFullValue"], defaultCurrency: this.udrfService.udrfData.appliedConfig["customFields"]["defaultCurrency"] }
    });

  }

  openViewHistoryModal(historyModalInput, historyModalInputType) {

    let sourceCBOData = [];

    if (historyModalInput) {

      if (this.baseLegalEntityLevel == 0 || this.baseLegalEntityLevel == 1 || historyModalInputType == "OBV" || historyModalInputType == "Opportunity")
        sourceCBOData = this.resolvedCBOData;

      else if (this.baseLegalEntityLevel == 2 || this.baseLegalEntityLevel == 3)
        sourceCBOData = this.resolvedCBOData;
      // sourceCBOData = this.resolvedCBOItemData;

    }

    else {

      if (this.baseLegalEntityLevel == 0 || this.baseLegalEntityLevel == 1)
        sourceCBOData = this.resolvedCBOData;

      else if (this.baseLegalEntityLevel == 2 || this.baseLegalEntityLevel == 3)
        sourceCBOData = this.resolvedCBOData;
      // sourceCBOData = sourceCBOData.concat(this.resolvedCBOData, this.resolvedCBOItemData);

      historyModalInputType = this.reportMode == "Normal Mode" ? this.getSelectedCBOWCardDataType() : historyModalInputType;

    }

    this.governanceReportService.openGovernanceReportViewHistoryModal(historyModalInput, historyModalInputType, this.baseLegalEntityLevel, sourceCBOData, this.dialog);

  }

  clickedPmoDbItemData = {
    clickedPmoDbItemDataType: "",
    clickedPmoDbItemDataTypeFormatted: "",
    resolvedPmoDbDataItem: "",
    pmoDbWeekInterval: {},
    visiblePmoDataKey: ""
  };

  setPmoDbClickItemData(clickedPmoDbItemDataType, resolvedPmoDbDataItem, pmoDbWeekInterval, visiblePmoDataKey) {

    this.clickedPmoDbItemData.clickedPmoDbItemDataType = clickedPmoDbItemDataType;
    this.clickedPmoDbItemData.clickedPmoDbItemDataTypeFormatted = clickedPmoDbItemDataType == "COMPLETED" ? "Actual" : (clickedPmoDbItemDataType == "PLANNED" ? "Planned" : "Overdue");
    this.clickedPmoDbItemData.resolvedPmoDbDataItem = resolvedPmoDbDataItem;
    this.clickedPmoDbItemData.pmoDbWeekInterval = pmoDbWeekInterval;
    this.clickedPmoDbItemData.visiblePmoDataKey = visiblePmoDataKey;

  }

  async openDmPopup(popupType, isFromHeader, doesStartWithLoadingScreen) {

    if (isFromHeader) {

      if (popupType == "Projects" && !this.pmoDb.isPmoDbDataLoading) {

        this.governanceReportService.sortOrders = JSON.parse(JSON.stringify(this.governanceReportService.defaultSortOrders));

        let modalParams = {
          misFlags: this.misFlags,
          udrfData: this.udrfService.udrfData,
          applicationId: this.applicationId,
          legalEntities: this.legalEntities,
          baseLegalEntityLevel: this.baseLegalEntityLevel,
          popupType: popupType,
          popupTypeTitle: "Weekly Milestone",
          isFromHeader: isFromHeader,
          doesStartWithLoadingScreen: doesStartWithLoadingScreen,
          dialog: this.dialog
        };

        if (popupType == "Projects") {

          modalParams["pmoDb"] = JSON.parse(JSON.stringify(this.pmoDb));

          let currentMonthInterval = _.where(modalParams["pmoDb"].monthIntervals, { monthInterval: modalParams["pmoDb"].pmoDbDataMonthInterval });

          let finalCurrentMonthInterval = currentMonthInterval && currentMonthInterval.length > 0 ? currentMonthInterval[0] : modalParams["pmoDb"].monthIntervals[0];

          modalParams["pmoDb"].currentWeekIntervals = finalCurrentMonthInterval.weekIntervals;

          modalParams["pmoDb"]["pmoDbDataWeekIntervals"] = [this.pmoDb.currentWeekIntervals[0]];

          modalParams["pmoDb"]["dmBaseLegalEntityRollDowns"] = this.dmBaseLegalEntityRollDowns;

          modalParams["pmoDb"]["cssClasses"] = {
            "projectsColClass": this.projectsColClass,
            "projectsColItemClass": this.projectsColItemClass,
            "projectsColSpilloverClass": this.projectsColSpilloverClass
          }

          modalParams["pmoDb"]["pmoDbDataMonthIntervalCount"] = parseInt(this.pmoDb.pmoDbDataMonthInterval.replace("M", ""));

        }

        const { GovRepDmPopupComponent } = await import('../../lazy-loaded-components/gov-rep-dm-popup/gov-rep-dm-popup.component');

        this.dialog.open(GovRepDmPopupComponent, {
          minWidth: "95%",
          height: "95%",
          data: { modalParams: modalParams }
        });

      }

    }

    else {

      if (popupType == "Projects" && !this.pmoDb.isPmoDbDataLoading) {

        this.governanceReportService.sortOrders = JSON.parse(JSON.stringify(this.governanceReportService.defaultSortOrders));

        let udrfData = JSON.parse(JSON.stringify(this.udrfService.udrfData));

        let modalParams = {
          misFlags: this.misFlags,
          udrfData: udrfData,
          applicationId: this.applicationId,
          legalEntities: this.legalEntities,
          baseLegalEntityLevel: this.baseLegalEntityLevel,
          popupType: popupType,
          popupTypeTitle: "Weekly Milestone",
          isFromHeader: isFromHeader,
          multiOptionSelectSearchValuesForPmoDbPlFilter: this.multiOptionSelectSearchValuesForPmoDbPlFilter,
          doesStartWithLoadingScreen: doesStartWithLoadingScreen,
          dialog: this.dialog
        };

        if (popupType == "Projects") {

          modalParams["pmoDb"] = JSON.parse(JSON.stringify(this.pmoDb));

          modalParams["pmoDb"]["totAcMlCount"] = this.clickedPmoDbItemData.resolvedPmoDbDataItem["ml_actual_count" + this.clickedPmoDbItemData.pmoDbWeekInterval["weekInterval"] + this.clickedPmoDbItemData.pmoDbWeekInterval["monthCount"]];
          modalParams["pmoDb"]["totPlMlCount"] = this.clickedPmoDbItemData.resolvedPmoDbDataItem["ml_planned_count" + this.clickedPmoDbItemData.pmoDbWeekInterval["weekInterval"] + this.clickedPmoDbItemData.pmoDbWeekInterval["monthCount"]];
          modalParams["pmoDb"]["totOvMlCount"] = this.clickedPmoDbItemData.resolvedPmoDbDataItem["ml_overdue_count" + this.clickedPmoDbItemData.pmoDbWeekInterval["weekInterval"] + this.clickedPmoDbItemData.pmoDbWeekInterval["monthCount"]];

          modalParams["pmoDb"]["totAcAcCount"] = this.clickedPmoDbItemData.resolvedPmoDbDataItem["ac_actual_count" + this.clickedPmoDbItemData.pmoDbWeekInterval["weekInterval"] + this.clickedPmoDbItemData.pmoDbWeekInterval["monthCount"]];
          modalParams["pmoDb"]["totPlAcCount"] = this.clickedPmoDbItemData.resolvedPmoDbDataItem["ac_planned_count" + this.clickedPmoDbItemData.pmoDbWeekInterval["weekInterval"] + this.clickedPmoDbItemData.pmoDbWeekInterval["monthCount"]];
          modalParams["pmoDb"]["totOvAcCount"] = this.clickedPmoDbItemData.resolvedPmoDbDataItem["ac_overdue_count" + this.clickedPmoDbItemData.pmoDbWeekInterval["weekInterval"] + this.clickedPmoDbItemData.pmoDbWeekInterval["monthCount"]];

          modalParams["pmoDb"]["totAcSoCount"] = this.clickedPmoDbItemData.resolvedPmoDbDataItem["so_actual_count" + this.clickedPmoDbItemData.pmoDbWeekInterval["weekInterval"] + this.clickedPmoDbItemData.pmoDbWeekInterval["monthCount"]];
          modalParams["pmoDb"]["totPlSoCount"] = this.clickedPmoDbItemData.resolvedPmoDbDataItem["so_planned_count" + this.clickedPmoDbItemData.pmoDbWeekInterval["weekInterval"] + this.clickedPmoDbItemData.pmoDbWeekInterval["monthCount"]];
          modalParams["pmoDb"]["totOvSoCount"] = this.clickedPmoDbItemData.resolvedPmoDbDataItem["so_overdue_count" + this.clickedPmoDbItemData.pmoDbWeekInterval["weekInterval"] + this.clickedPmoDbItemData.pmoDbWeekInterval["monthCount"]];

          modalParams["pmoDb"]["totAcPhCount"] = this.clickedPmoDbItemData.resolvedPmoDbDataItem["ph_actual_count" + this.clickedPmoDbItemData.pmoDbWeekInterval["weekInterval"] + this.clickedPmoDbItemData.pmoDbWeekInterval["monthCount"]];
          modalParams["pmoDb"]["totPlPhCount"] = this.clickedPmoDbItemData.resolvedPmoDbDataItem["ph_planned_count" + this.clickedPmoDbItemData.pmoDbWeekInterval["weekInterval"] + this.clickedPmoDbItemData.pmoDbWeekInterval["monthCount"]];
          modalParams["pmoDb"]["totOvPhCount"] = this.clickedPmoDbItemData.resolvedPmoDbDataItem["ph_overdue_count" + this.clickedPmoDbItemData.pmoDbWeekInterval["weekInterval"] + this.clickedPmoDbItemData.pmoDbWeekInterval["monthCount"]];

          modalParams["pmoDb"]["totAcWpCount"] = this.clickedPmoDbItemData.resolvedPmoDbDataItem["wp_actual_count" + this.clickedPmoDbItemData.pmoDbWeekInterval["weekInterval"] + this.clickedPmoDbItemData.pmoDbWeekInterval["monthCount"]];
          modalParams["pmoDb"]["totPlWpCount"] = this.clickedPmoDbItemData.resolvedPmoDbDataItem["wp_planned_count" + this.clickedPmoDbItemData.pmoDbWeekInterval["weekInterval"] + this.clickedPmoDbItemData.pmoDbWeekInterval["monthCount"]];
          modalParams["pmoDb"]["totOvWpCount"] = this.clickedPmoDbItemData.resolvedPmoDbDataItem["wp_overdue_count" + this.clickedPmoDbItemData.pmoDbWeekInterval["weekInterval"] + this.clickedPmoDbItemData.pmoDbWeekInterval["monthCount"]];

          modalParams["pmoDb"]["totAcMlValueOriginal"] = this.clickedPmoDbItemData.resolvedPmoDbDataItem["ml_actual_value" + this.clickedPmoDbItemData.pmoDbWeekInterval["weekInterval"] + this.clickedPmoDbItemData.pmoDbWeekInterval["monthCount"] + "Original"];
          modalParams["pmoDb"]["totPlMlValueOriginal"] = this.clickedPmoDbItemData.resolvedPmoDbDataItem["ml_planned_value" + this.clickedPmoDbItemData.pmoDbWeekInterval["weekInterval"] + this.clickedPmoDbItemData.pmoDbWeekInterval["monthCount"] + "Original"];
          modalParams["pmoDb"]["totOvMlValueOriginal"] = this.clickedPmoDbItemData.resolvedPmoDbDataItem["ml_overdue_value" + this.clickedPmoDbItemData.pmoDbWeekInterval["weekInterval"] + this.clickedPmoDbItemData.pmoDbWeekInterval["monthCount"] + "Original"];
          modalParams["pmoDb"]["totAcMlValue"] = this.getValueWithComma(modalParams["pmoDb"]["totAcMlValueOriginal"]);
          modalParams["pmoDb"]["totPlMlValue"] = this.getValueWithComma(modalParams["pmoDb"]["totPlMlValueOriginal"]);
          modalParams["pmoDb"]["totOvMlValue"] = this.getValueWithComma(modalParams["pmoDb"]["totOvMlValueOriginal"]);

          modalParams["pmoDb"]["totAcMlValueTooltip"] = this.getValueTooltipWithComma(modalParams["pmoDb"]["totAcMlValueOriginal"]);
          modalParams["pmoDb"]["totPlMlValueTooltip"] = this.getValueTooltipWithComma(modalParams["pmoDb"]["totPlMlValueOriginal"]);
          modalParams["pmoDb"]["totOvMlValueTooltip"] = this.getValueTooltipWithComma(modalParams["pmoDb"]["totOvMlValueOriginal"]);

          modalParams["pmoDb"]["pmoDbDataWeekIntervals"] = [this.clickedPmoDbItemData.pmoDbWeekInterval];

          modalParams["pmoDb"]["pmoDbDataMonthInterval"] = this.pmoDb.pmoDbDataMonthInterval;

          modalParams["pmoDb"]["pmoDbDataMonthIntervalCount"] = parseInt(this.pmoDb.pmoDbDataMonthInterval.replace("M", ""));

          modalParams["pmoDb"]["pmoDbDataMonthCount"] = this.pmoDb.pmoDbDataMonthCount;

          modalParams["pmoDb"]["dmBaseLegalEntityRollDowns"] = this.dmBaseLegalEntityRollDowns;

          modalParams["pmoDbMilestoneData"] = {
            clickedPmoDbItemData: this.clickedPmoDbItemData,
            currentWeekPmoMilestoneDbData: []
          };

        }

        const { GovRepDmPopupComponent } = await import('../../lazy-loaded-components/gov-rep-dm-popup/gov-rep-dm-popup.component');

        this.dialog.open(GovRepDmPopupComponent, {
          minWidth: "95%",
          height: "95%",
          data: { modalParams: modalParams }
        });

      }

    }

  }

  async openDetailedModeModal(detailedModeModalInput, detailedModeModalInputType) {

    this.governanceReportService.sortOrders = JSON.parse(JSON.stringify(this.governanceReportService.defaultSortOrders));

    let isExpandOverridden = false;

    if (detailedModeModalInputType == "WCD") {

      isExpandOverridden = this.reportMode == "Normal Mode" ? this.normalModeCBOWBaseLegalEntityExpandOverridden : this.detailedModeBaseLegalEntityExpandOverridden;

      detailedModeModalInput = isExpandOverridden ? this.resolvedArAgingDaysData : this.resolvedWCDData;

    }

    if ((detailedModeModalInputType != "WCD" && detailedModeModalInputType != "BB") ||
      (!this.isResolvedWCDDataLoading && !this.isResolvedArDataLoading && detailedModeModalInputType == "WCD") ||
      (!this.isBbDataLoading && detailedModeModalInputType == "BB")) {

      let sourceCBOData = [];
      let bbHeader = false;

      if (this.baseLegalEntityLevel == 0 || this.baseLegalEntityLevel == 1 || detailedModeModalInputType == "OBV")
        sourceCBOData = this.resolvedCBOData;

      else if (this.baseLegalEntityLevel == 2 || this.baseLegalEntityLevel == 3)
        sourceCBOData = this.resolvedCBOData;
      // sourceCBOData = this.resolvedCBOItemData;

      if (detailedModeModalInputType == "BB")
        bbHeader = true;

      let BBDataCount = {
        cgm: this.getValueWithComma(this.BBDataCount[0].cgm),
        nygm: this.getValueWithComma(this.BBDataCount[0].nygm),
        obv: this.getValueWithComma(this.BBDataCount[0].obv),
        bbRev: this.getValueWithComma(this.BBDataCount[0].bbRev),
        nyrev: this.getValueWithComma(this.BBDataCount[0].nyrev),
        bbgmm: this.getValueWithComma(this.BBDataCount[0].bbgmm),
      }

      let modalParams = {
        isMisBudgetVisible: JSON.parse(JSON.stringify(this.udrfService.udrfData.appliedConfig["customFields"]["isMisBudgetVisible"])),
        isMisVarianceVisible: JSON.parse(JSON.stringify(this.udrfService.udrfData.appliedConfig["customFields"]["isMisVarianceVisible"])),
        isFullValue: JSON.parse(JSON.stringify(this.udrfService.udrfData.appliedConfig["customFields"]["isFullValue"])),
        misDetailedModeCardClass: JSON.parse(JSON.stringify(detailedModeModalInputType.includes("Opportunity") ? this.misOpportunityDetailedModeCardClass : this.misDetailedModeCardClass)),
        misFlags: this.misFlags,
        defaultCurrency: JSON.parse(JSON.stringify(this.udrfService.udrfData.appliedConfig["customFields"]["defaultCurrency"])),
        udrfData: this.udrfService.udrfData,
        allNgrReportTypes: this.allNgrReportTypes,
        applicationId: this.applicationId,
        actualLabel: this.actualLabel,
        actualSubType: this.actualSubType,
        plannedLabel: this.plannedLabel,
        plannedMonthSubType: this.plannedMonthSubType,
        // plannedSubType: this.plannedSubType,
        yetToText: this.plannedLabel,
        legalEntities: this.legalEntities,
        resolvedArAgingDaysData: this.resolvedArAgingDaysData,
        sourceCBOData: sourceCBOData,
        arAmountLabel: this.arAmountLabel,
        totalArAmount: this.detailed_mode_total_ar_amount,
        totalArAmountOriginal: this.detailed_mode_total_ar_amount_original,
        arDaysLabel: this.arDaysLabel,
        totalArDays: this.detailed_mode_total_ar_days,
        ubrAmountLabel: this.ubrAmountLabel,
        totalUbrAmount: this.detailed_mode_total_ubr_amount,
        totalUbrAmountOriginal: this.detailed_mode_total_ubr_amount_original,
        ubrDaysLabel: this.ubrDaysLabel,
        totalUbrDays: this.detailed_mode_total_ubr_days,
        wcdAmountLabel: this.wcdAmountLabel,
        totalWcAmount: this.detailed_mode_total_wc_amount,
        totalWcAmountOriginal: this.detailed_mode_total_wc_amount_original,
        retentionAmountLabel: this.retentionAmountLabel,
        retentionAmount: this.detailed_mode_total_retention_amount,
        retentionAmountOriginal: this.detailed_mode_total_retention_amount_original,
        arWoRetentionLabel: this.arWoRetentionLabel,
        arWoRetention: this.detailed_mode_total_ar_wo_retention,
        arWoRetentionOriginal: this.detailed_mode_total_ar_wo_retention_original,
        wcDaysWoRetentionLabel: this.wcDaysWoRetentionLabel,
        wcDaysWoRetention: this.detailed_mode_total_wc_days_wo_retention,
        wcWoRetentionLabel: this.wcWoRetentionLabel,
        wcWoRetention: this.detailed_mode_total_wc_wo_retention,
        wcWoRetentionOriginal: this.detailed_mode_total_wc_wo_retention_original,
        wcdDaysLabel: this.wcdDaysLabel,
        totalWcDays: this.detailed_mode_total_wc_days,
        baseLegalEntityLevel: this.baseLegalEntityLevel,
        poCTargetToDate: this.poCTargetToDate,
        modalDataType: detailedModeModalInputType,
        modalData: JSON.parse(JSON.stringify(detailedModeModalInput)),
        preResolvedArAgingDaysData: JSON.parse(JSON.stringify(this.preResolvedArAgingDaysData)),
        dialog: this.dialog,
        visibleMisCards: this.visibleMisCards,
        bbHeader: bbHeader,
        currentMonthYear: moment(this.activeFtmDate).format('MMM YYYY'),
        allComments: this.allComments,
        projectAndOpportunityData: this.projectAndOpportunityData,
        bbProbability: this.bbProbabilityModal,
        detailedModeBaseLegalEntity: this.detailedModeBaseLegalEntity,
        highestPl: this.highestPl,
        itemWiseUbrValuesOriginal: this.itemWiseUbrValuesOriginal,
        isExpandOverridden: isExpandOverridden,
        ftyStartDate: this.ftyStartDate,
        ftyEndDate: this.ftyEndDate,
        upcoming3MonthsStartDate: this.upcoming3MonthsStartDate,
        upcoming3MonthsEndDate: this.upcoming3MonthsEndDate,
        BBDataCount: BBDataCount,
        ftnyStartDate: this.ftnyStartDate,
        ftnyEndDate: this.ftnyEndDate
      };

      if (detailedModeModalInputType == "Projects")
        modalParams["pmoDb"] = this.pmoDb;

      if (detailedModeModalInputType.includes("Opportunity")) {

        modalParams["misOpportunityFilterArray"] = this.misOpportunityFilterArray;
        modalParams["intervalCode"] = detailedModeModalInput.intervalCode;
        modalParams["bbData"] = this.BBData;
        modalParams["legalEntitiesData"] = this.legalEntitiesData;
        modalParams["dateFormat"] = this.dateFormat;
        modalParams["ftyEndDate"] = this.ftyEndDate;
        modalParams["bbDataTypes"] = this.initBbDataTypes;

      }

      const { GovRepDetailedModeModalComponent } = await import('../../lazy-loaded-components/gov-rep-detailed-mode-modal/gov-rep-detailed-mode-modal.component');

      const openGovRepDetailedModeModalComponent = this.dialog.open(GovRepDetailedModeModalComponent, {
        minWidth: this.udrfService.udrfData.appliedConfig["customFields"]["isFullValue"] || (detailedModeModalInputType.includes("MIS") && this.udrfService.udrfData.appliedConfig["customFields"]["isMisBudgetVisible"] == 'Yes') ? "98%" : "75%",
        height: "95%",
        data: { modalParams: modalParams }
      });

      openGovRepDetailedModeModalComponent.afterClosed().subscribe(async res => {

        if (res.event == "Close")
          this.projectAndOpportunityData = res.projectAndOpportunityData;

      });

    }

  }

  async openDetailedModeCTAModal(ctaModalInputs) {

    if (!this.isCtaDataLoading) {

      let viewCtaData = [];

      for (let ctaModalInput of ctaModalInputs)
        viewCtaData.push({
          assigned_to: ctaModalInput.primaryOwner,
          cta_name: ctaModalInput.name,
          cta_type: ctaModalInput.type,
          due_on: ctaModalInput.dueDateOriginal,
          id: ctaModalInput.id,
          originated_by: ctaModalInput.originator,
          status: ctaModalInput.status
        });

      let modalParams = {
        applicationId: this.applicationId,
        viewCtaData: viewCtaData,
        openViewCTAModal: true
      }

      const { QuickCtaModalComponent } = await import('src/app/modules/shared-lazy-loaded-components/quick-cta-modal/quick-cta-modal.component');

      const openQuickCtaModalComponent = this.dialog.open(QuickCtaModalComponent, {
        height: '100%',
        minWidth: '85%',
        position: { right: '0px' },
        data: { modalParams: modalParams }
      });

    }

  }

  async openCreateQuickCTAModal(ctaModalInput) {

    if (this.ctaAppConfig) {

      const { CustomFormModalComponent } = await import('src/app/modules/shared-lazy-loaded-components/custom-form-modal/custom-form-modal.component');

      this.dialog.open(CustomFormModalComponent,
        {
          height: '100%',
          width: '75%',
          position: { right: '0px' },
          data: {
            lcdpApplicationId: this.ctaAppConfig['lcdp_app_id'],
            formId: this.ctaAppConfig['form_id'],
            isEdit: true,
            entryForId: null,
            formSubmissionMessage: this.ctaAppConfig['form_submission_msg'] || '',
            inputData: {
              fieldMappedInput: {
                [this.ctaAppConfig['region_field_id']]: ctaModalInput['vertical_id']
              }
            }
          },
          disableClose: false
        }
      );

    }

    else
      this.utilityService.showToastMessage("CTA App Config not Found !");

    // this.governanceReportService.openGovernanceReportCreateQuickCTAModal(ctaModalInput, this.applicationId, this.dialog);

  }

  async openViewCTAModal(ctaModalInput) {

    let modalParams = {
      applicationId: this.applicationId,
      viewCtaData: [{
        assigned_to: ctaModalInput.primaryOwner,
        cta_name: ctaModalInput.name,
        cta_type: ctaModalInput.type,
        due_on: ctaModalInput.dueDateOriginal,
        id: ctaModalInput.id,
        originated_by: ctaModalInput.originator,
        status: ctaModalInput.status
      }],
      openViewCTAModal: true
    }

    const { QuickCtaModalComponent } = await import('src/app/modules/shared-lazy-loaded-components/quick-cta-modal/quick-cta-modal.component');

    const openQuickCtaModalComponent = this.dialog.open(QuickCtaModalComponent, {
      height: '100%',
      minWidth: '85%',
      position: { right: '0px' },
      data: { modalParams: modalParams }
    });

  }

  changeCTAStatus(ctaInput) {

    if (ctaInput.originatorOId == this.currentUser.oid) {

      if (ctaInput.status == "Closed")
        this.utilityService.showToastMessage("This CTA has been already been closed!");

      else if (ctaInput.status == "Completed") {

        this.utilityService
          .openConfirmationSweetAlertWithCustom("Are you sure ?", "Once closed, you will not be able to open this CTA again !")
          .then(async (res) => {

            if (res) {

              let logs = "\n-> " + this.currentUser.name + " changed the status from " + ctaInput.status + " to Closed!";

              this.sharedLazyLoadedComponentsService.callUpdateStatus(ctaInput.id, logs, "Closed").then((res: any) => {

                ctaInput.status = res[0].status_name;

                this.utilityService.showToastMessage("CTA has been closed successfully!");

              }, err => {
                this.utilityService.showToastMessage(err);
              });

            }

          });

      }

      else {

        let logs = "\n-> " + this.currentUser.name + " changed the status from " + ctaInput.status + " to Completed!";

        this.sharedLazyLoadedComponentsService.callUpdateStatus(ctaInput.id, logs, "Completed").then((res: any) => {

          ctaInput.status = res[0].status_name;

          this.utilityService.showToastMessage("CTA has been completed successfully!");

        }, err => {
          this.utilityService.showToastMessage(err);
        });

      }

    }

    else if (ctaInput.primaryOwnerOId == this.currentUser.oid) {

      if (ctaInput.status == "Closed")
        this.utilityService.showToastMessage("This CTA has been already been closed!");

      else if (ctaInput.status == "Completed")
        this.utilityService.showToastMessage("This CTA has been already been completed!");

      else {

        let logs = "\n-> " + this.currentUser.name + " changed the status from " + ctaInput.status + " to Completed!";

        this.sharedLazyLoadedComponentsService.callUpdateStatus(ctaInput.id, logs, "Completed").then((res: any) => {

          ctaInput.status = res[0].status_name;

          this.utilityService.showToastMessage("CTA has been completed successfully!");

        }, err => {
          this.utilityService.showToastMessage(err);
        });

      }

    }

  }

  openCommentsModal(commentsModalInput, commentsModalInputType) {

    commentsModalInput["isFullValue"] = this.udrfService.udrfData.appliedConfig["customFields"]["isFullValue"];
    commentsModalInput["defaultCurrency"] = this.udrfService.udrfData.appliedConfig["customFields"]["defaultCurrency"];
    commentsModalInput["misFlags"] = this.misFlags;
    commentsModalInput["visibleMisCards"] = this.visibleMisCards;

    this.governanceReportService.openGovernanceReportCommentsModal(commentsModalInput, commentsModalInputType, this.applicationId, this.dialog).then((hasEnteredComment: any) => {

      if (hasEnteredComment)
        commentsModalInput["hasEnteredComment"] = hasEnteredComment;

    });

  }

  projectAndOpportunityData = {};

  checkIfCommentsExist(commentsModalInput, commentsModalInputType) {

    if (this.misFlags.shouldShowIfNgrCommentsAreCounted) {

      let result = this.governanceReportService.checkIfCommentsExist(commentsModalInput, commentsModalInputType, this.allComments, this.projectAndOpportunityData);

      this.projectAndOpportunityData = result["projectAndOpportunityData"];

      return result["hasEnteredComment"];

    }

    else
      return false;

  }

  changeDetailedModeBrightness() {

    this.detailedModeBrightness = this.detailedModeBrightness == "Bright Mode" ? "Dark Mode" : "Bright Mode";

  }

  async openLegalEntityModal() {

    let modalParams = {
      legalEntities: this.authLegalEntities,
      baseLegalEntity: this.detailedModeBaseLegalEntity,
      isExpandOverridden: this.detailedModeBaseLegalEntityExpandOverridden
    }

    const { GovRepLegalEntityModalComponent } = await import('../../lazy-loaded-components/gov-rep-legal-entity-modal/gov-rep-legal-entity-modal.component');

    const openGovRepLegalEntityModalComponent = this.dialog.open(GovRepLegalEntityModalComponent, {
      height: '100%',
      minWidth: '25%',
      width: '25%',
      position: { right: '0px' },
      data: { modalParams: modalParams }
    });

    openGovRepLegalEntityModalComponent.afterClosed().subscribe(res => {

      if (res.event == 'Submit') {

        this.spacingKeys = {};

        this._repeatCall.next();

        this.detailedModeBaseLegalEntity = res.data.govRepLegalEntityModalResponse["baseLegalEntity"];

        this.detailedModeBaseLegalEntityExpandOverridden = res.data.govRepLegalEntityModalResponse["isExpandOverridden"]

        this.callAllDetailedModeFns();

      }

    });

  }

  changeReportMode() {

    this.spacingKeys = {};

    this._repeatCall.next();

    if (this.reportMode == "Normal Mode") {

      this.reportMode = "Detailed Mode";

      this.utilityService.addIsFullScreenAppValue(true);

      this.openFullScreen();

      this.resolvedCBOData = [];

      this.callAllDetailedModeFns();

    }

    else {

      this.reportMode = "Normal Mode";

      this.utilityService.addIsFullScreenAppValue(false);

      this.closeFullScreen();

      this.callAllNormalModeFns();

    }

  }

  retrieveMISFTYData() {

    this.reportsService.getPostingPeriodDate()
      .pipe(takeUntil(this._onDestroy)).pipe(takeUntil(this._repeatCall))
      .subscribe(async res => {

        this.latest_actual_date = moment(res["latest_actual_date"]);

        this.getPlRollup();

      }, err => {
        this.showErrorMessage(err);
      });

  }

  misUserRoleOrgCodes = [];

  isResolvedWCDDataLoading = false; isResolvedArDataLoading = false; isBbDataLoading = false; isCtaDataLoading = false;

  resolveRetrievedMisData() {

    this.misUserRoleOrgCodes = [];

    let misUserRoleOrgCodesData = _.where(this.userRoleOrgCodes, { objectId: 167 });

    if (misUserRoleOrgCodesData && misUserRoleOrgCodesData.length > 0)
      this.misUserRoleOrgCodes = misUserRoleOrgCodesData[0].roles;

    let legalEntitiesItem = _.where(this.legalEntities, { name: this.detailedModeBaseLegalEntity });

    this.baseLegalEntityLevel = legalEntitiesItem.length > 0 ? legalEntitiesItem[0].leLevel : 0;

    this.yearCount = 0;
    this.cardCountPy = 0;
    this.cardCountNy = 0;
    this.cardCountRest = 0;

    this.resolveMisSortOrders();

    this.resolveMisOpportunitySortOrders();

    this.resetNonDetailedModeData();

    let isForDownload = false;

    this.goneToResolveMisFtyData = false;

    this.isMisFtyDataResolved = false;

    this.retrieveMisFtyDataYearWise(isForDownload);

  }

  resolveMisSortOrders() {

    this.resolvedMISData = [];

    this.resolvedMisDataForOpportunityExcessCards = [];

    for (let misCard of this.udrfService.udrfData.appliedConfig["customFields"]["misCards"])
      misCard["isForOpportunityExcess"] = false;

    let selectedMisOpportunityIntervalCards = _.where(this.udrfService.udrfData.appliedConfig["customFields"]["misOpportunityCards"], { isSelected: true });

    for (let misOpportunityIntervalCard of selectedMisOpportunityIntervalCards) {

      let dataTypeCode = misOpportunityIntervalCard.dataTypeCode.replace("_BB", "");

      let misIntervalCard = _.where(this.udrfService.udrfData.appliedConfig["customFields"]["misCards"], { dataTypeCode: dataTypeCode });

      if (misIntervalCard.length > 0 && !misIntervalCard[0].isSelected)
        misIntervalCard[0]["isForOpportunityExcess"] = true;

    }

    for (let misCard of this.udrfService.udrfData.appliedConfig["customFields"]["misCards"])
      if (misCard.isSelected || misCard.isForOpportunityExcess) {

        let intervalName = "";

        let intervalCard = _.where(this.udrfService.udrfData.appliedConfig["customFields"]["misCards"], { dataTypeCode: misCard.dataTypeCode });

        if (intervalCard && intervalCard.length > 0)
          intervalName = intervalCard[0].dataTypeName;

        if (!misCard.isForOpportunityExcess)
          this.resolvedMISData.push({
            interval: intervalName,
            intervalCode: misCard.dataTypeCode,
            items: [],
            isLoading: true,
            order: misCard.order,
            isForOpportunityExcess: misCard.isForOpportunityExcess
          });

        else
          this.resolvedMisDataForOpportunityExcessCards.push({
            interval: intervalName,
            intervalCode: misCard.dataTypeCode,
            items: [],
            isLoading: true,
            order: misCard.order,
            isForOpportunityExcess: misCard.isForOpportunityExcess
          });

        for (let i = 0; i < 4; i++) {

          this.governanceReportService.sortOrders["MIS" + misCard.dataTypeCode + "Pu" + i] = [{
            sortField: "legalEntity",
            sortOrder: "N"
          }];

          this.governanceReportService.sortOrdersDm["MISDm" + misCard.dataTypeCode + i] = {
            legalEntity: "N"
          };

        }

        if (misCard.dataTypeCode.includes("PY"))
          this.cardCountPy++;

        else if (misCard.dataTypeCode.includes("NY"))
          this.cardCountNy++;

        else
          this.cardCountRest++;

      }

    this.governanceReportService.defaultSortOrders = JSON.parse(JSON.stringify(this.governanceReportService.sortOrders));

  }

  resolveMisOpportunitySortOrders() {

    this.resolvedMISOpportunityData = [];

    for (let misCard of this.udrfService.udrfData.appliedConfig["customFields"]["misOpportunityCards"])
      if (misCard.isSelected) {

        let intervalName = "";

        let intervalCard = _.where(this.udrfService.udrfData.appliedConfig["customFields"]["misOpportunityCards"], { dataTypeCode: misCard.dataTypeCode });

        if (intervalCard && intervalCard.length > 0)
          intervalName = intervalCard[0].dataTypeName;

        this.resolvedMISOpportunityData.push({
          interval: intervalName,
          intervalCode: misCard.dataTypeCode,
          items: [],
          isLoading: true,
          order: misCard.order
        });

        for (let i = 0; i < 4; i++) {

          this.governanceReportService.sortOrders[misCard.dataTypeCode + "MISOpportunity" + "Pu" + i] = [{
            sortField: "legalEntity",
            sortOrder: "N"
          }];

          this.governanceReportService.sortOrdersDm["MISOpportunityDm" + misCard.dataTypeCode + i] = {
            legalEntity: "N"
          };

        }

      }

    this.governanceReportService.defaultSortOrders = JSON.parse(JSON.stringify(this.governanceReportService.sortOrders));

  }

  resetNonDetailedModeData() {

    console.log("Resetting resolvedCBOData...")
    this.resolvedCBOData = [{
      type: "Collection",
      items: [],
      isLoading: true
    },
    {
      type: "Billing",
      items: [],
      isLoading: true
    },
    {
      type: "OBV",
      items: [],
      isLoading: true
    }];

    this.resolvedWCDData = [];
    this.resolvedArAgingDaysData = [];
    this.ResolvedBBData = [];
    this.resolvedDetailedModeCTAFTMData = [];

    this.isResolvedWCDDataLoading = true; this.isResolvedArDataLoading = true; this.isBbDataLoading = true; this.isCtaDataLoading = true;

  }

  yearCount = 0; cardCountPy = 0; cardCountNy = 0; cardCountRest = 0;;

  retrieveMisFtyDataYearWise(isForDownload) {

    let dontRetrieveData = false;

    let yearMisData = _.where(this.misDataYearWise, { yearCount: this.yearCount });

    if (yearMisData.length > 0 || (!isForDownload && ((this.yearCount == 0 && this.cardCountPy == 0) || (this.yearCount == 1 &&
      this.cardCountRest == 0) || (this.yearCount == 2 && this.cardCountNy == 0))))
      dontRetrieveData = true;

    if (!dontRetrieveData) {

      //To set default calculation start and end year
      let startYear = this.current_fy_year_1 + this.yearCount;
      let endYear = this.current_fy_year_1 + this.yearCount + 1;

      //To determine start and end year ,if the start month is January and end month is December where difference of 12 months falls under same year
      if (this.misFlags.tenantFyStartMonth == 1 && this.misFlags.tenantFyEndMonth == 12) {
        startYear = this.current_fy_year_1 + this.yearCount;
        endYear = startYear;
      }

      let apiParams = {
        startYear: startYear,
        endYear: endYear ,
        orgCodes: this.misUserRoleOrgCodes,
        postingTypeIds: this.userRolePostingTypeIds,
        isFromMisReport: false,
        defaultCurrency: this.udrfService.udrfData.appliedConfig["customFields"]["defaultCurrency"],
        yearCount: this.yearCount,
        misLiveData: this.udrfService.udrfData.appliedConfig["customFields"]["misLiveData"]
      };

      this.reportsService
        .getMISReportDataFromSQLStaging(apiParams)
        .pipe(takeUntil(this._onDestroy)).pipe(takeUntil(this._repeatCall))
        .subscribe(async data => {

          this.misDataYearWise.push({
            yearCount: data["yearCount"],
            data: data["data"],
            workingData: []
          });

          console.log(`this.current_fy_year_1 - ${this.current_fy_year_1} , data["yearCount"] - ${data["yearCount"]} , next_fy_year_2 - ${this.next_fy_year_2} `)
          if (this.current_fy_year_1 + data["yearCount"] < this.next_fy_year_2){
            console.log("condition passed")
            this.resolveMisFtyDataYearWise(isForDownload);
          }


        }, err => {
          this.showErrorMessage(err);
        });

    }

    else
      if (this.current_fy_year_1 + this.yearCount < this.next_fy_year_2)
        this.resolveMisFtyDataYearWise(isForDownload);


    if (!isForDownload) { //temporary if loop

      this.yearCount++;

      if (this.yearCount < 3)
        this.retrieveMisFtyDataYearWise(isForDownload);

    }

  }

  resolveMisFtyDataYearWise(isForDownload) {

    if (!isForDownload) { //temporary if loop

      let areAllDataRetrieved = 0;

      for (let i = 0; i < 3; i++) {

        let yearMisData = _.where(this.misDataYearWise, { yearCount: i });

        if (yearMisData.length > 0 || (!isForDownload && ((i == 0 && this.cardCountPy == 0) || (i == 1 &&
          this.cardCountRest == 0) || (i == 2 && this.cardCountNy == 0))))
          areAllDataRetrieved++;

      }

      if (areAllDataRetrieved == 3 && !this.goneToResolveMisFtyData)
        this.resolveMISFTYData(isForDownload);

    }

    else
      this.resolveMISFTYData(isForDownload);

  }

  goneToResolveMisFtyData = false;

  isMisFtyDataResolved = false;

  resolveMISFTYData(isForDownload) {

    this.goneToResolveMisFtyData = true;

    this.visibleMisCards = _.filter(this.allNgrReportTypes, function (allNgrReportType) {

      if (allNgrReportType.to_display_in_ngr == true || allNgrReportType.to_display_in_ngr == 1)
        return allNgrReportType;

    }, this);

    let misMainFilter = "";

    if ((!isForDownload && this.baseLegalEntityLevel == 0) || (isForDownload && this.downloadLeLevel == 0)) {

      this.misFirstFilter = "pl_id";
      this.misSecondFilter = "sub_pl_id";

    }

    else {

      if ((!isForDownload && this.baseLegalEntityLevel == 1) || (isForDownload && this.downloadLeLevel == 1)) {

        misMainFilter = "pl";
        this.misFirstFilter = "sub_pl_id";
        this.misSecondFilter = "vertical_id";

      }

      else if ((!isForDownload && this.baseLegalEntityLevel == 2) || (isForDownload && this.downloadLeLevel == 2)) {

        misMainFilter = "sub_pl";
        this.misFirstFilter = "vertical_id";
        this.misSecondFilter = this.downloadLeLevel2Iteration == 0 ? "project" : "item";

      }

      else if ((!isForDownload && this.baseLegalEntityLevel == 3) || (isForDownload && this.downloadLeLevel == 3)) {

        misMainFilter = "vertical";
        this.misFirstFilter = "project";
        this.misSecondFilter = isForDownload ? "item" : "";

      }

    }

    for (let misDataYearWiseItem of this.misDataYearWise) {

      if ((!isForDownload && this.baseLegalEntityLevel != 0) || (isForDownload && this.downloadLeLevel != 0))
        misDataYearWiseItem["workingData"] = JSON.parse(JSON.stringify(_.where(misDataYearWiseItem.data, { [misMainFilter]: !isForDownload ? this.detailedModeBaseLegalEntity : this.downloadLe })));

      else
        misDataYearWiseItem["workingData"] = JSON.parse(JSON.stringify(misDataYearWiseItem["data"]));

      for (let udrfFilterConfigItem of this.udrfFilterConfig)
        if (udrfFilterConfigItem.misFilterIdBased && udrfFilterConfigItem.misFilterIdBased == "Y" && udrfFilterConfigItem.filterIds.length > 0)
          misDataYearWiseItem["workingData"] = _.filter(misDataYearWiseItem["workingData"], function (sourceMISDataItem) {

            if (sourceMISDataItem[udrfFilterConfigItem.misFilterField] && _.contains(udrfFilterConfigItem.filterIds, sourceMISDataItem[udrfFilterConfigItem.misFilterField]))
              return sourceMISDataItem;

          }, this);

        else if (udrfFilterConfigItem.misFilterIdBased && udrfFilterConfigItem.misFilterIdBased == "N" && udrfFilterConfigItem.filterNames.length > 0)
          misDataYearWiseItem["workingData"] = _.filter(misDataYearWiseItem["workingData"], function (sourceMISDataItem) {

            if (sourceMISDataItem[udrfFilterConfigItem.misFilterField] && _.contains(udrfFilterConfigItem.filterNames, sourceMISDataItem[udrfFilterConfigItem.misFilterField]))
              return sourceMISDataItem;

          }, this);

        else if (udrfFilterConfigItem.misFilterIdBased && udrfFilterConfigItem.misFilterIdBased == "K" && udrfFilterConfigItem.filterKeyValues.length > 0)
          misDataYearWiseItem["workingData"] = _.filter(misDataYearWiseItem["workingData"], function (sourceMISDataItem) {

            if (sourceMISDataItem[udrfFilterConfigItem.misFilterField] && _.contains(udrfFilterConfigItem.filterKeyValues, sourceMISDataItem[udrfFilterConfigItem.misFilterField]))
              return sourceMISDataItem;

          }, this);

    }

    let visiblePostingTypeIds = [], visibleBackendPostingTypeIdsOtherThanDefault = [], visibleFrontendPostingTypeIdsOtherThanDefault = [];

    let defaultPostingTypeIds = [this.misFlags.reportRevenueTypeId, this.misFlags.reportCostTypeId, this.misFlags.reportGmTypeId,
    this.misFlags.reportSgaTypeId, this.misFlags.reportPbtTypeId];

    for (let visibleMisCard of this.visibleMisCards) {

      visiblePostingTypeIds.push(visibleMisCard.posting_type_id);

      if (visibleMisCard.calc_in_ui == 0 && !_.contains(defaultPostingTypeIds, visibleMisCard.posting_type_id))
        visibleBackendPostingTypeIdsOtherThanDefault.push(visibleMisCard.posting_type_id);

      if (visibleMisCard.calc_in_ui == 1 && !_.contains(defaultPostingTypeIds, visibleMisCard.posting_type_id))
        visibleFrontendPostingTypeIdsOtherThanDefault.push(visibleMisCard.posting_type_id);

    }

    if (!isForDownload) {

      for (let sortOrderKey of Object.keys(this.governanceReportService.sortOrders))
        for (let visiblePostingTypeId of visiblePostingTypeIds)
          this.governanceReportService.sortOrders[sortOrderKey].push({
            sortField: visiblePostingTypeId + '',
            sortOrder: "N"
          });

      for (let sortOrderKey of Object.keys(this.governanceReportService.sortOrdersDm))
        for (let visiblePostingTypeId of visiblePostingTypeIds)
          this.governanceReportService.sortOrdersDm[sortOrderKey][visiblePostingTypeId + ''] = "N";

    }

    this.governanceReportService.defaultSortOrders = JSON.parse(JSON.stringify(this.governanceReportService.sortOrders));

    this.misParams = {
      visibleBackendPostingTypeIdsOtherThanDefault: visibleBackendPostingTypeIdsOtherThanDefault,
      visibleFrontendPostingTypeIdsOtherThanDefault: visibleFrontendPostingTypeIdsOtherThanDefault
    };

    if (!isForDownload) {

      let selectedCount = 0;

      for (let i = 0; i < this.udrfService.udrfData.appliedConfig["customFields"]["misCards"].length; i++)
        if (this.udrfService.udrfData.appliedConfig["customFields"]["misCards"][i].isSelected || this.udrfService.udrfData.appliedConfig["customFields"]["misCards"][i].isForOpportunityExcess)
          setTimeout(() => {
            this.resolveMISData(this.udrfService.udrfData.appliedConfig["customFields"]["misCards"][i].dataTypeCode, this.udrfService.udrfData.appliedConfig["customFields"]["misCards"][i].order, isForDownload, false);
          }, 50 * selectedCount++);

    }

    else
      this.resolveMISData("FTM", 0, isForDownload, false);

  }

  resolveMISData(interval, order, isForDownload, isForOpportunityCard) {

    if (isForOpportunityCard)
      interval = interval.replace("_BB", "");

    let totalMisData = [];

    if (!isForDownload) {

      if (interval.includes("PY"))
        totalMisData = _.where(this.misDataYearWise, { yearCount: 0 });

      else if (interval.includes("NY"))
        totalMisData = _.where(this.misDataYearWise, { yearCount: 2 });

      else
        totalMisData = _.where(this.misDataYearWise, { yearCount: 1 });

    }

    else
      totalMisData = _.where(this.misDataYearWise, { yearCount: this.yearCount });

    if (totalMisData && totalMisData.length > 0 && totalMisData[0]["workingData"])
      totalMisData = totalMisData[0]["workingData"];

    else
      totalMisData = [];

    let totalRevenue, totalRevenueBudget, totalCost, totalCostBudget, totalGM, totalGMBudget, totalSGA, totalSGABudget, totalPBT, totalPBTBudget;

    let rollupRevenue, rollupRevenueBudget, rollupCost, rollupCostBudget, rollupGM, rollupGMBudget, rollupSGA, rollupSGABudget, rollupPBT, rollupPBTBudget;

    let revenue, revenueBudget, cost, costBudget, gm, gmBudget, sga, sgaBudget, pbt, pbtBudget;

    let items = [], subItems = [];

    let totalKey = {}, rollupKey = {}, itemKey = {}, keyDatabase = {};

    let plId = 0, verticalId = 0, projectItemName = "", projectId = 0;

    let currentMonth = !isForDownload ? this.activeFtmDate : this.currentDownloadMonth;

    // console.log(`interval - ${interval} ,
    // this.latest_actual_date - ${this.latest_actual_date} ,
    //  moment(this.latest_actual_date).subtract(1, 'year') - ${moment(this.latest_actual_date).subtract(1, 'year')}  `)
    let totalData = _.filter(totalMisData, function (misDataItem) {

      if (interval == "FTM")
        return (moment(misDataItem.date).isSame(currentMonth));

      else if (interval == "YTD"){
        // console.log(`${misDataItem.date} , 
        // moment(misDataItem.date).isSameOrBefore(this.latest_actual_date) - ${moment(misDataItem.date).isSameOrBefore(this.latest_actual_date)} ,
        // moment(misDataItem.date).isAfter(this.ftpyEndDate) - ${moment(misDataItem.date).isAfter(this.ftpyEndDate)},
        // moment(misDataItem.date).isBefore(this.ftnyStartDate) - ${moment(misDataItem.date).isBefore(this.ftnyStartDate)} `)
        return (moment(misDataItem.date).isSameOrBefore(this.latest_actual_date) && moment(misDataItem.date).isAfter(this.ftpyEndDate) && moment(misDataItem.date).isBefore(this.ftnyStartDate));
      }

      else if (interval == "YOY_YTD")
        return (moment(misDataItem.date).isSameOrBefore(this.latest_actual_date) && moment(misDataItem.date).isAfter(this.ftpyEndDate) && moment(misDataItem.date).isBefore(this.ftnyStartDate));

      else if (interval == "YTD_PY")
      {
        // console.log(`${misDataItem.date} , 
        // moment(misDataItem.date).isSameOrBefore(moment(this.latest_actual_date).subtract(1, 'year')) - ${moment(misDataItem.date).isSameOrBefore(moment(this.latest_actual_date).subtract(1, 'year'))} ,
        // moment(misDataItem.date).isSameOrAfter(this.ftpyStartDate) - ${moment(misDataItem.date).isSameOrAfter(this.ftpyStartDate)},
        // moment(misDataItem.date).isSameOrBefore(this.ftpyEndDate) - ${moment(misDataItem.date).isSameOrBefore(this.ftpyEndDate)} `)

        return (moment(misDataItem.date).isSameOrBefore(moment(this.latest_actual_date).subtract(1, 'year')) && moment(misDataItem.date).isSameOrAfter(this.ftpyStartDate) && moment(misDataItem.date).isSameOrBefore(this.ftpyEndDate));
      }
        
      else if (interval == "UOB")
      {
        // console.log(`${misDataItem.date} , 
        // moment(misDataItem.date).isAfter(this.latest_actual_date) - ${moment(misDataItem.date).isAfter(this.latest_actual_date)} ,
        // moment(misDataItem.date).isAfter(this.ftpyEndDate) - ${moment(misDataItem.date).isAfter(this.ftpyEndDate)},
        // moment(misDataItem.date).isBefore(this.ftnyStartDate) - ${moment(misDataItem.date).isBefore(this.ftnyStartDate)} `)
        return (moment(misDataItem.date).isAfter(this.latest_actual_date) && moment(misDataItem.date).isAfter(this.ftpyEndDate) && moment(misDataItem.date).isBefore(this.ftnyStartDate));
      }
       
      else if (interval == "LE")
        return (moment(misDataItem.date).isBefore(this.ftnyStartDate) && moment(misDataItem.date).isAfter(this.ftpyEndDate));

      else if (interval == "LE_BUD")
        return (moment(misDataItem.date).isBefore(this.ftnyStartDate) && moment(misDataItem.date).isAfter(this.ftpyEndDate));

      else if (interval == "YOY_LE_BUD")
        return (moment(misDataItem.date).isBefore(this.ftnyStartDate) && moment(misDataItem.date).isAfter(this.ftpyEndDate));

      else if (interval == "LE_YTD")
        return (moment(misDataItem.date).isSameOrBefore(this.latest_actual_date) && moment(misDataItem.date).isAfter(this.ftpyEndDate) && moment(misDataItem.date).isBefore(this.ftnyStartDate));

      else if (interval == "UOB_NY")
        return (moment(misDataItem.date).isSameOrAfter(this.ftnyStartDate) && moment(misDataItem.date).isSameOrBefore(this.ftnyEndDate));

      else if (interval == "FTM_PY")
        return (moment(misDataItem.date).isSame(this.activeFtmPyDate));

      else if (interval == "LE_PY")
        return (moment(misDataItem.date).isBefore(this.ftyStartDate) && moment(misDataItem.date).isSameOrAfter(this.ftpyStartDate));

    }, this);

    console.log(totalData);

    let totalBudgetData = [];

    if (interval == "YOY_YTD" || interval == "YOY_LE_BUD" || interval == "LE_YTD")
      totalBudgetData = _.filter(totalMisData, function (misDataItem) {

        if (interval == "YOY_YTD")
          return (moment(misDataItem.date).isSameOrBefore(moment(this.latest_actual_date).subtract(1, 'year')) && moment(misDataItem.date).isSameOrAfter(this.ftpyStartDate) && moment(misDataItem.date).isSameOrBefore(this.ftpyEndDate));

        else if (interval == "YOY_LE_BUD")
          return (moment(misDataItem.date).isBefore(this.ftyStartDate) && moment(misDataItem.date).isSameOrAfter(this.ftpyStartDate));

        else if (interval == "LE_YTD")
          return (moment(misDataItem.date).isBefore(this.ftnyStartDate) && moment(misDataItem.date).isAfter(this.ftpyEndDate));

      }, this);

    else
      totalBudgetData = totalData;

    let totalRevenueData = _.filter(totalData, function (misDataItem) {

      if (interval == "LE_BUD" || interval == "YOY_LE_BUD")
        return misDataItem.posting_type_id == this.misFlags.reportRevenueTypeId &&
          ((moment(misDataItem.date).isSameOrBefore(this.latest_actual_date) && moment(misDataItem.date).isAfter(this.ftpyEndDate) && moment(misDataItem.date).isBefore(this.ftnyStartDate) && (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned")) ||
            (moment(misDataItem.date).isAfter(this.latest_actual_date) && moment(misDataItem.date).isBefore(this.ftnyStartDate) && misDataItem.subType.trim() == "Budget"));

      else
        return misDataItem.posting_type_id == this.misFlags.reportRevenueTypeId && (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned");

    }, this);

    let totalRevenueBudgetData = _.filter(totalBudgetData, function (misDataItem) {

      if (interval == "YOY_YTD" || interval == "YOY_LE_BUD")
        return misDataItem.posting_type_id == this.misFlags.reportRevenueTypeId && (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned");

      else
        return misDataItem.posting_type_id == this.misFlags.reportRevenueTypeId && (misDataItem.subType.trim() == "Budget");

    }, this);

    for (let visibleBackendPostingTypeIdsOtherThanDefaultItem of this.misParams.visibleBackendPostingTypeIdsOtherThanDefault) {

      let totalKeyData = _.filter(totalData, function (misDataItem) {

        if (interval == "LE_BUD" || interval == "YOY_LE_BUD")
          return misDataItem.posting_type_id == visibleBackendPostingTypeIdsOtherThanDefaultItem &&
            ((moment(misDataItem.date).isSameOrBefore(this.latest_actual_date) && moment(misDataItem.date).isAfter(this.ftpyEndDate) && moment(misDataItem.date).isBefore(this.ftnyStartDate) && (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned")) ||
              (moment(misDataItem.date).isAfter(this.latest_actual_date) && moment(misDataItem.date).isBefore(this.ftnyStartDate) && misDataItem.subType.trim() == "Budget"));

        else
          return misDataItem.posting_type_id == visibleBackendPostingTypeIdsOtherThanDefaultItem && (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned");

      }, this);

      let totalKeyBudgetData = _.filter(totalBudgetData, function (misDataItem) {

        if (interval == "YOY_YTD" || interval == "YOY_LE_BUD")
          return misDataItem.posting_type_id == visibleBackendPostingTypeIdsOtherThanDefaultItem && (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned");

        else
          return misDataItem.posting_type_id == visibleBackendPostingTypeIdsOtherThanDefaultItem && (misDataItem.subType.trim() == "Budget");

      }, this);

      let totalKeyValue = totalKeyData.reduce((s, f) => {
        return s + f.value;
      }, 0.0);

      let totalKeyBudgetValue = totalKeyBudgetData.reduce((s, f) => {
        return s + f.value;
      }, 0.0);

      totalKey["total" + visibleBackendPostingTypeIdsOtherThanDefaultItem] = totalKeyValue;
      totalKey["total" + visibleBackendPostingTypeIdsOtherThanDefaultItem + "Budget"] = totalKeyBudgetValue;
      totalKey["total" + visibleBackendPostingTypeIdsOtherThanDefaultItem + "Variance"] = this.misFlags.varianceCalc == 'bma' ? totalKeyBudgetValue - totalKeyValue : totalKeyValue - totalKeyBudgetValue;
      totalKey["total" + visibleBackendPostingTypeIdsOtherThanDefaultItem + "VarPerc"] = this.misFlags.varianceCalc == 'amb' ? (((totalKeyBudgetValue - totalKeyValue) == 0 || totalKeyValue == 0) ? (this.misFlags.variancePerCalc == 'hmv' ? (100 - 0) : this.misFlags.variancePerCalc == 'hpv' ? (100 + 0) : 0) : (this.misFlags.variancePerCalc == 'hmv' ? (100 - (((totalKeyBudgetValue - totalKeyValue) / totalKeyValue) * 100)) : this.misFlags.variancePerCalc == 'hpv' ? (100 + (((totalKeyBudgetValue - totalKeyValue) / totalKeyValue) * 100)) : (((totalKeyBudgetValue - totalKeyValue) / totalKeyValue) * 100))) : (((totalKeyValue - totalKeyBudgetValue) == 0 || totalKeyBudgetValue == 0) ? (this.misFlags.variancePerCalc == 'hmv' ? (100 - 0) : this.misFlags.variancePerCalc == 'hpv' ? (100 + 0) : 0) : (this.misFlags.variancePerCalc == 'hmv' ? (100 - (((totalKeyValue - totalKeyBudgetValue) / totalKeyBudgetValue) * 100)) : this.misFlags.variancePerCalc == 'hpv' ? (100 + (((totalKeyValue - totalKeyBudgetValue) / totalKeyBudgetValue) * 100)) : (((totalKeyValue - totalKeyBudgetValue) / totalKeyBudgetValue) * 100)));

      let totalKeyDataSep = _.filter(totalData, function (misDataItem) {

        if (interval == "LE_BUD" || interval == "YOY_LE_BUD")
          return ((moment(misDataItem.date).isSameOrBefore(this.latest_actual_date) && moment(misDataItem.date).isAfter(this.ftpyEndDate) && moment(misDataItem.date).isBefore(this.ftnyStartDate) && (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned")) ||
            (moment(misDataItem.date).isAfter(this.latest_actual_date) && moment(misDataItem.date).isBefore(this.ftnyStartDate) && misDataItem.subType.trim() == "Budget"));

        else
          return (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned");

      }, this);

      let totalKeyBudgetDataSep = _.filter(totalBudgetData, function (misDataItem) {

        if (interval == "YOY_YTD" || interval == "YOY_LE_BUD")
          return (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned");

        else
          return (misDataItem.subType.trim() == "Budget");

      }, this);

      keyDatabase["totalKeyDataSep"] = totalKeyDataSep;
      keyDatabase["totalKeyBudgetDataSep"] = totalKeyBudgetDataSep;

    }

    for (let visibleFrontendPostingTypeIdsOtherThanDefaultItem of this.misParams.visibleFrontendPostingTypeIdsOtherThanDefault) {

      let currentPostingType = _.where(this.visibleMisCards, { posting_type_id: visibleFrontendPostingTypeIdsOtherThanDefaultItem });

      let totalKeyValue = 0, totalKeyBudgetValue = 0;

      if (currentPostingType && currentPostingType.length > 0 && currentPostingType[0].calculation) {

        let evalString = JSON.parse(currentPostingType[0].calculation);

        let dataString = "(" + evalString.calculation + ")", budgetDataString = "(" + evalString.calculation + ")";

        for (let posting_type_data_item of evalString.posting_type_data) {

          let totalKeyDataTemp = _.filter(totalData, function (misDataItem) {

            if (interval == "LE_BUD" || interval == "YOY_LE_BUD")
              return misDataItem.posting_type_id == parseInt(posting_type_data_item.posting_type_id) &&
                ((moment(misDataItem.date).isSameOrBefore(this.latest_actual_date) && moment(misDataItem.date).isAfter(this.ftpyEndDate) && moment(misDataItem.date).isBefore(this.ftnyStartDate) && (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned")) ||
                  (moment(misDataItem.date).isAfter(this.latest_actual_date) && moment(misDataItem.date).isBefore(this.ftnyStartDate) && misDataItem.subType.trim() == "Budget"));

            else
              return misDataItem.posting_type_id == parseInt(posting_type_data_item.posting_type_id) && (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned");

          }, this);

          let totalKeyBudgetDataTemp = _.filter(totalBudgetData, function (misDataItem) {

            if (interval == "YOY_YTD" || interval == "YOY_LE_BUD")
              return misDataItem.posting_type_id == parseInt(posting_type_data_item.posting_type_id) && (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned");

            else
              return misDataItem.posting_type_id == parseInt(posting_type_data_item.posting_type_id) && (misDataItem.subType.trim() == "Budget");

          }, this);

          let totalKeyValueTemp = totalKeyDataTemp.reduce((s, f) => {
            return s + f.value;
          }, 0.0);

          let totalKeyBudgetValueTemp = totalKeyBudgetDataTemp.reduce((s, f) => {
            return s + f.value;
          }, 0.0);

          dataString = dataString.replace(posting_type_data_item.replace_variable, totalKeyValueTemp);

          budgetDataString = budgetDataString.replace(posting_type_data_item.replace_variable, totalKeyBudgetValueTemp);

        }

        totalKeyValue = this.arithmeticEvalService.evaluate(dataString), totalKeyBudgetValue = this.arithmeticEvalService.evaluate(budgetDataString);

        if (isNaN(totalKeyValue))
          totalKeyValue = 0;

        if (isNaN(totalKeyBudgetValue))
          totalKeyBudgetValue = 0;

      }

      totalKey["total" + visibleFrontendPostingTypeIdsOtherThanDefaultItem] = totalKeyValue;
      totalKey["total" + visibleFrontendPostingTypeIdsOtherThanDefaultItem + "Budget"] = totalKeyBudgetValue;
      totalKey["total" + visibleFrontendPostingTypeIdsOtherThanDefaultItem + "Variance"] = this.misFlags.varianceCalc == 'bma' ? totalKeyBudgetValue - totalKeyValue : totalKeyValue - totalKeyBudgetValue;
      totalKey["total" + visibleFrontendPostingTypeIdsOtherThanDefaultItem + "VarPerc"] = this.misFlags.varianceCalc == 'amb' ? (((totalKeyBudgetValue - totalKeyValue) == 0 || totalKeyValue == 0) ? (this.misFlags.variancePerCalc == 'hmv' ? (100 - 0) : this.misFlags.variancePerCalc == 'hpv' ? (100 + 0) : 0) : (this.misFlags.variancePerCalc == 'hmv' ? (100 - (((totalKeyBudgetValue - totalKeyValue) / totalKeyValue) * 100)) : this.misFlags.variancePerCalc == 'hpv' ? (100 + (((totalKeyBudgetValue - totalKeyValue) / totalKeyValue) * 100)) : (((totalKeyBudgetValue - totalKeyValue) / totalKeyValue) * 100))) : (((totalKeyValue - totalKeyBudgetValue) == 0 || totalKeyBudgetValue == 0) ? (this.misFlags.variancePerCalc == 'hmv' ? (100 - 0) : this.misFlags.variancePerCalc == 'hpv' ? (100 + 0) : 0) : (this.misFlags.variancePerCalc == 'hmv' ? (100 - (((totalKeyValue - totalKeyBudgetValue) / totalKeyBudgetValue) * 100)) : this.misFlags.variancePerCalc == 'hpv' ? (100 + (((totalKeyValue - totalKeyBudgetValue) / totalKeyBudgetValue) * 100)) : (((totalKeyValue - totalKeyBudgetValue) / totalKeyBudgetValue) * 100)));

    }

    let totalCostData = _.filter(totalData, function (misDataItem) {

      if (interval == "LE_BUD" || interval == "YOY_LE_BUD")
        return misDataItem.posting_type_id == this.misFlags.reportCostTypeId &&
          ((moment(misDataItem.date).isSameOrBefore(this.latest_actual_date) && moment(misDataItem.date).isAfter(this.ftpyEndDate) && moment(misDataItem.date).isBefore(this.ftnyStartDate) && (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned")) ||
            (moment(misDataItem.date).isAfter(this.latest_actual_date) && moment(misDataItem.date).isBefore(this.ftnyStartDate) && misDataItem.subType.trim() == "Budget"));

      else
        return misDataItem.posting_type_id == this.misFlags.reportCostTypeId && (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned");

    }, this);

    let totalCostBudgetData = _.filter(totalBudgetData, function (misDataItem) {

      if (interval == "YOY_YTD" || interval == "YOY_LE_BUD")
        return misDataItem.posting_type_id == this.misFlags.reportCostTypeId && (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned");

      else
        return misDataItem.posting_type_id == this.misFlags.reportCostTypeId && (misDataItem.subType.trim() == "Budget");

    }, this);

    let totalGMData = _.filter(totalData, function (misDataItem) {

      if (interval == "LE_BUD" || interval == "YOY_LE_BUD")
        return misDataItem.posting_type_id == this.misFlags.reportGmTypeId &&
          ((moment(misDataItem.date).isSameOrBefore(this.latest_actual_date) && moment(misDataItem.date).isAfter(this.ftpyEndDate) && moment(misDataItem.date).isBefore(this.ftnyStartDate) && (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned")) ||
            (moment(misDataItem.date).isAfter(this.latest_actual_date) && moment(misDataItem.date).isBefore(this.ftnyStartDate) && misDataItem.subType.trim() == "Budget"));

      else
        return misDataItem.posting_type_id == this.misFlags.reportGmTypeId && (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned");

    }, this);

    let totalGMBudgetData = _.filter(totalBudgetData, function (misDataItem) {

      if (interval == "YOY_YTD" || interval == "YOY_LE_BUD")
        return misDataItem.posting_type_id == this.misFlags.reportGmTypeId && (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned");

      else
        return misDataItem.posting_type_id == this.misFlags.reportGmTypeId && (misDataItem.subType.trim() == "Budget");

    }, this);

    let totalSGAData = _.filter(totalData, function (misDataItem) {

      if (interval == "LE_BUD" || interval == "YOY_LE_BUD")
        return misDataItem.posting_type_id == this.misFlags.reportSgaTypeId &&
          ((moment(misDataItem.date).isSameOrBefore(this.latest_actual_date) && moment(misDataItem.date).isAfter(this.ftpyEndDate) && moment(misDataItem.date).isBefore(this.ftnyStartDate) && (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned")) ||
            (moment(misDataItem.date).isAfter(this.latest_actual_date) && moment(misDataItem.date).isBefore(this.ftnyStartDate) && misDataItem.subType.trim() == "Budget"));

      else
        return misDataItem.posting_type_id == this.misFlags.reportSgaTypeId && (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned");

    }, this);

    let totalSGABudgetData = _.filter(totalBudgetData, function (misDataItem) {

      if (interval == "YOY_YTD" || interval == "YOY_LE_BUD")
        return misDataItem.posting_type_id == this.misFlags.reportSgaTypeId && (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned");

      else
        return misDataItem.posting_type_id == this.misFlags.reportSgaTypeId && (misDataItem.subType.trim() == "Budget");

    }, this);

    let totalPBTData = _.filter(totalData, function (misDataItem) {

      if (interval == "LE_BUD" || interval == "YOY_LE_BUD")
        return misDataItem.posting_type_id == this.misFlags.reportPbtTypeId &&
          ((moment(misDataItem.date).isSameOrBefore(this.latest_actual_date) && moment(misDataItem.date).isAfter(this.ftpyEndDate) && moment(misDataItem.date).isBefore(this.ftnyStartDate) && (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned")) ||
            (moment(misDataItem.date).isAfter(this.latest_actual_date) && moment(misDataItem.date).isBefore(this.ftnyStartDate) && misDataItem.subType.trim() == "Budget"));

      else
        return misDataItem.posting_type_id == this.misFlags.reportPbtTypeId && (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned");

    }, this);

    let totalPBTBudgetData = _.filter(totalBudgetData, function (misDataItem) {

      if (interval == "YOY_YTD" || interval == "YOY_LE_BUD")
        return misDataItem.posting_type_id == this.misFlags.reportPbtTypeId && (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned");

      else
        return misDataItem.posting_type_id == this.misFlags.reportPbtTypeId && (misDataItem.subType.trim() == "Budget");

    }, this);

    totalRevenue = totalRevenueData.reduce((s, f) => {
      return s + f.value;
    }, 0.0);

    totalRevenueBudget = totalRevenueBudgetData.reduce((s, f) => {
      return s + f.value;
    }, 0.0);

    totalCost = totalCostData.reduce((s, f) => {
      return s + f.value;
    }, 0.0);

    totalCostBudget = totalCostBudgetData.reduce((s, f) => {
      return s + f.value;
    }, 0.0);

    totalGM = totalGMData.reduce((s, f) => {
      return s + f.value;
    }, 0.0);

    totalGMBudget = totalGMBudgetData.reduce((s, f) => {
      return s + f.value;
    }, 0.0);

    totalSGA = totalSGAData.reduce((s, f) => {
      return s + f.value;
    }, 0.0);

    totalSGABudget = totalSGABudgetData.reduce((s, f) => {
      return s + f.value;
    }, 0.0);

    totalPBT = totalPBTData.reduce((s, f) => {
      return s + f.value;
    }, 0.0);

    totalPBTBudget = totalPBTBudgetData.reduce((s, f) => {
      return s + f.value;
    }, 0.0);

    let mainLegalEntities = _.uniq(_.pluck(totalData, this.misFirstFilter));

    if (isForOpportunityCard && mainLegalEntities.length == 0) {

      for (let le of this.legalEntitiesData)
        if ((this.detailedModeBaseLegalEntity == this.highestPl && le.rollupLevels.length == 1) || le.rollupLevels.length > 1)
          for (let subLe of le.rollupLevels)
            if (subLe.rollUpPlName == this.detailedModeBaseLegalEntity)
              mainLegalEntities.push(le.plId);

    }

    for (let legalEntity of mainLegalEntities) {

      let rollupKeyDataSep = _.filter(keyDatabase["totalKeyDataSep"], function (misDataItem) {

        if (interval == "LE_BUD" || interval == "YOY_LE_BUD")
          return misDataItem[this.misFirstFilter] == legalEntity &&
            ((moment(misDataItem.date).isSameOrBefore(this.latest_actual_date) && moment(misDataItem.date).isAfter(this.ftpyEndDate) && moment(misDataItem.date).isBefore(this.ftnyStartDate) && (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned")) ||
              (moment(misDataItem.date).isAfter(this.latest_actual_date) && moment(misDataItem.date).isBefore(this.ftnyStartDate) && misDataItem.subType.trim() == "Budget"));

        else
          return misDataItem[this.misFirstFilter] == legalEntity && (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned");

      }, this);

      let rollupKeyBudgetDataSep = _.filter(keyDatabase["totalKeyBudgetDataSep"], function (misDataItem) {

        if (interval == "YOY_YTD" || interval == "YOY_LE_BUD")
          return misDataItem[this.misFirstFilter] == legalEntity && (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned");

        else
          return misDataItem[this.misFirstFilter] == legalEntity && (misDataItem.subType.trim() == "Budget");

      }, this);

      keyDatabase["rollupKeyDataSep"] = rollupKeyDataSep;
      keyDatabase["rollupKeyBudgetDataSep"] = rollupKeyBudgetDataSep;

      let rollupData = _.where(totalData, { [this.misFirstFilter]: legalEntity });

      let legalEntityName = "";

      if (this.misFirstFilter != "project") {

        if (rollupData.length > 0)
          legalEntityName = rollupData[0][this.misFirstFilter.replace('_id', '')];

        else if (isForOpportunityCard) {

          let currentPl = _.where(this.legalEntitiesData, { plId: legalEntity });

          if (currentPl.length > 0)
            legalEntityName = currentPl[0].description;

        }

      }

      else
        legalEntityName = legalEntity;

      if (rollupData.length > 0)
        plId = rollupData[0].pl_id;

      else if (isForOpportunityCard) {

        for (let le of this.legalEntitiesData)
          if (le.plId == legalEntity) {

            if (le.rollupLevels.length == 1)
              plId = le.plId;

            else
              plId = le.rollupLevels[le.rollupLevels.length - 2].rollUpPlId;

          }

      }

      verticalId = 0, projectItemName = "", projectId = 0;

      subItems = [];

      let rollupRevenueData = _.filter(totalRevenueData, function (misDataItem) {

        if (interval == "LE_BUD" || interval == "YOY_LE_BUD")
          return misDataItem[this.misFirstFilter] == legalEntity &&
            ((moment(misDataItem.date).isSameOrBefore(this.latest_actual_date) && moment(misDataItem.date).isAfter(this.ftpyEndDate) && moment(misDataItem.date).isBefore(this.ftnyStartDate) && (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned")) ||
              (moment(misDataItem.date).isAfter(this.latest_actual_date) && moment(misDataItem.date).isBefore(this.ftnyStartDate) && misDataItem.subType.trim() == "Budget"));

        else
          return misDataItem[this.misFirstFilter] == legalEntity && (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned");

      }, this);

      let rollupRevenueBudgetData = _.filter(totalRevenueBudgetData, function (misDataItem) {

        if (interval == "YOY_YTD" || interval == "YOY_LE_BUD")
          return misDataItem[this.misFirstFilter] == legalEntity && (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned");

        else
          return misDataItem[this.misFirstFilter] == legalEntity && (misDataItem.subType.trim() == "Budget");

      }, this);

      let rollupCostData = _.filter(totalCostData, function (misDataItem) {

        if (interval == "LE_BUD" || interval == "YOY_LE_BUD")
          return misDataItem[this.misFirstFilter] == legalEntity &&
            ((moment(misDataItem.date).isSameOrBefore(this.latest_actual_date) && moment(misDataItem.date).isAfter(this.ftpyEndDate) && moment(misDataItem.date).isBefore(this.ftnyStartDate) && (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned")) ||
              (moment(misDataItem.date).isAfter(this.latest_actual_date) && moment(misDataItem.date).isBefore(this.ftnyStartDate) && misDataItem.subType.trim() == "Budget"));

        else
          return misDataItem[this.misFirstFilter] == legalEntity && (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned");

      }, this);

      let rollupCostBudgetData = _.filter(totalCostBudgetData, function (misDataItem) {

        if (interval == "YOY_YTD" || interval == "YOY_LE_BUD")
          return misDataItem[this.misFirstFilter] == legalEntity && (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned");

        else
          return misDataItem[this.misFirstFilter] == legalEntity && (misDataItem.subType.trim() == "Budget");

      }, this);

      let rollupGMData = _.filter(totalGMData, function (misDataItem) {

        if (interval == "LE_BUD" || interval == "YOY_LE_BUD")
          return misDataItem[this.misFirstFilter] == legalEntity &&
            ((moment(misDataItem.date).isSameOrBefore(this.latest_actual_date) && moment(misDataItem.date).isAfter(this.ftpyEndDate) && moment(misDataItem.date).isBefore(this.ftnyStartDate) && (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned")) ||
              (moment(misDataItem.date).isAfter(this.latest_actual_date) && moment(misDataItem.date).isBefore(this.ftnyStartDate) && misDataItem.subType.trim() == "Budget"));

        else
          return misDataItem[this.misFirstFilter] == legalEntity && (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned");

      }, this);

      let rollupGMBudgetData = _.filter(totalGMBudgetData, function (misDataItem) {

        if (interval == "YOY_YTD" || interval == "YOY_LE_BUD")
          return misDataItem[this.misFirstFilter] == legalEntity && (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned");

        else
          return misDataItem[this.misFirstFilter] == legalEntity && (misDataItem.subType.trim() == "Budget");

      }, this);

      let rollupSGAData = _.filter(totalSGAData, function (misDataItem) {

        if (interval == "LE_BUD" || interval == "YOY_LE_BUD")
          return misDataItem[this.misFirstFilter] == legalEntity &&
            ((moment(misDataItem.date).isSameOrBefore(this.latest_actual_date) && moment(misDataItem.date).isAfter(this.ftpyEndDate) && moment(misDataItem.date).isBefore(this.ftnyStartDate) && (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned")) ||
              (moment(misDataItem.date).isAfter(this.latest_actual_date) && moment(misDataItem.date).isBefore(this.ftnyStartDate) && misDataItem.subType.trim() == "Budget"));

        else
          return misDataItem[this.misFirstFilter] == legalEntity && (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned");

      }, this);

      let rollupSGABudgetData = _.filter(totalSGABudgetData, function (misDataItem) {

        if (interval == "YOY_YTD" || interval == "YOY_LE_BUD")
          return misDataItem[this.misFirstFilter] == legalEntity && (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned");

        else
          return misDataItem[this.misFirstFilter] == legalEntity && (misDataItem.subType.trim() == "Budget");

      }, this);

      let rollupPBTData = _.filter(totalPBTData, function (misDataItem) {

        if (interval == "LE_BUD" || interval == "YOY_LE_BUD")
          return misDataItem[this.misFirstFilter] == legalEntity &&
            ((moment(misDataItem.date).isSameOrBefore(this.latest_actual_date) && moment(misDataItem.date).isAfter(this.ftpyEndDate) && moment(misDataItem.date).isBefore(this.ftnyStartDate) && (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned")) ||
              (moment(misDataItem.date).isAfter(this.latest_actual_date) && moment(misDataItem.date).isBefore(this.ftnyStartDate) && misDataItem.subType.trim() == "Budget"));

        else
          return misDataItem[this.misFirstFilter] == legalEntity && (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned");

      }, this);

      let rollupPBTBudgetData = _.filter(totalPBTBudgetData, function (misDataItem) {

        if (interval == "YOY_YTD" || interval == "YOY_LE_BUD")
          return misDataItem[this.misFirstFilter] == legalEntity && (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned");

        else
          return misDataItem[this.misFirstFilter] == legalEntity && (misDataItem.subType.trim() == "Budget");

      }, this);

      rollupRevenue = rollupRevenueData.reduce((s, f) => {
        return s + f.value;
      }, 0.0);

      rollupRevenueBudget = rollupRevenueBudgetData.reduce((s, f) => {
        return s + f.value;
      }, 0.0);

      rollupCost = rollupCostData.reduce((s, f) => {
        return s + f.value;
      }, 0.0);

      rollupCostBudget = rollupCostBudgetData.reduce((s, f) => {
        return s + f.value;
      }, 0.0);

      rollupGM = rollupGMData.reduce((s, f) => {
        return s + f.value;
      }, 0.0);

      rollupGMBudget = rollupGMBudgetData.reduce((s, f) => {
        return s + f.value;
      }, 0.0);

      rollupSGA = rollupSGAData.reduce((s, f) => {
        return s + f.value;
      }, 0.0);

      rollupSGABudget = rollupSGABudgetData.reduce((s, f) => {
        return s + f.value;
      }, 0.0);

      rollupPBT = rollupPBTData.reduce((s, f) => {
        return s + f.value;
      }, 0.0);

      rollupPBTBudget = rollupPBTBudgetData.reduce((s, f) => {
        return s + f.value;
      }, 0.0);


      if (this.misSecondFilter != "") {

        let rollupLegalEntities = _.uniq(_.pluck(rollupData, this.misSecondFilter));

        if (isForOpportunityCard && rollupLegalEntities.length == 0) {

          for (let le of this.legalEntitiesData)
            if ((le.rollupLevels.length - 1) == (this.baseLegalEntityLevel + 1))
              for (let subLe of le.rollupLevels)
                if (subLe.rollUpPlId == legalEntity)
                  rollupLegalEntities.push(le.plId);

        }

        let rollupLegalEntityNameTest = "";

        if (rollupLegalEntities.length == 1 && this.misSecondFilter != "project" && this.misSecondFilter != "item") {

          let rollupLegalEntityNameTestArray = _.where(rollupData, { [this.misSecondFilter]: rollupLegalEntities[0] });

          if (rollupLegalEntityNameTestArray.length > 0)
            rollupLegalEntityNameTest = rollupLegalEntityNameTestArray[0][this.misSecondFilter.replace('_id', '')];

        }

        if (!isForDownload && rollupLegalEntityNameTest == legalEntityName)
          subItems = [];

        else {

          for (let rollupLegalEntity of rollupLegalEntities) {

            let rollupSubData = _.where(rollupData, { [this.misSecondFilter]: rollupLegalEntity });

            let rollupLegalEntityName = "";

            if (this.misSecondFilter != "project" && this.misSecondFilter != "item") {

              if (rollupSubData.length > 0)
                rollupLegalEntityName = rollupSubData[0][this.misSecondFilter.replace('_id', '')];

              else if (isForOpportunityCard) {

                let currentPl = _.where(this.legalEntitiesData, { plId: rollupLegalEntity });

                if (currentPl.length > 0)
                  rollupLegalEntityName = currentPl[0].description;

              }

            }

            else
              rollupLegalEntityName = rollupLegalEntity;

            let revenueData = _.filter(rollupRevenueData, function (misDataItem) {

              if (interval == "LE_BUD" || interval == "YOY_LE_BUD")
                return misDataItem[this.misSecondFilter] == rollupLegalEntity &&
                  ((moment(misDataItem.date).isSameOrBefore(this.latest_actual_date) && moment(misDataItem.date).isAfter(this.ftpyEndDate) && moment(misDataItem.date).isBefore(this.ftnyStartDate) && (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned")) ||
                    (moment(misDataItem.date).isAfter(this.latest_actual_date) && moment(misDataItem.date).isBefore(this.ftnyStartDate) && misDataItem.subType.trim() == "Budget"));

              else
                return misDataItem[this.misSecondFilter] == rollupLegalEntity && (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned");

            }, this);

            let revenueBudgetData = _.filter(rollupRevenueBudgetData, function (misDataItem) {

              if (interval == "YOY_YTD" || interval == "YOY_LE_BUD")
                return misDataItem[this.misSecondFilter] == rollupLegalEntity && (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned");

              else
                return misDataItem[this.misSecondFilter] == rollupLegalEntity && (misDataItem.subType.trim() == "Budget");

            }, this);

            let costData = _.filter(rollupCostData, function (misDataItem) {

              if (interval == "LE_BUD" || interval == "YOY_LE_BUD")
                return misDataItem[this.misSecondFilter] == rollupLegalEntity &&
                  ((moment(misDataItem.date).isSameOrBefore(this.latest_actual_date) && moment(misDataItem.date).isAfter(this.ftpyEndDate) && moment(misDataItem.date).isBefore(this.ftnyStartDate) && (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned")) ||
                    (moment(misDataItem.date).isAfter(this.latest_actual_date) && moment(misDataItem.date).isBefore(this.ftnyStartDate) && misDataItem.subType.trim() == "Budget"));

              else
                return misDataItem[this.misSecondFilter] == rollupLegalEntity && (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned");

            }, this);

            let costBudgetData = _.filter(rollupCostBudgetData, function (misDataItem) {

              if (interval == "YOY_YTD" || interval == "YOY_LE_BUD")
                return misDataItem[this.misSecondFilter] == rollupLegalEntity && (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned");

              else
                return misDataItem[this.misSecondFilter] == rollupLegalEntity && (misDataItem.subType.trim() == "Budget");

            }, this);

            let gmData = _.filter(rollupGMData, function (misDataItem) {

              if (interval == "LE_BUD" || interval == "YOY_LE_BUD")
                return misDataItem[this.misSecondFilter] == rollupLegalEntity &&
                  ((moment(misDataItem.date).isSameOrBefore(this.latest_actual_date) && moment(misDataItem.date).isAfter(this.ftpyEndDate) && moment(misDataItem.date).isBefore(this.ftnyStartDate) && (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned")) ||
                    (moment(misDataItem.date).isAfter(this.latest_actual_date) && moment(misDataItem.date).isBefore(this.ftnyStartDate) && misDataItem.subType.trim() == "Budget"));

              else
                return misDataItem[this.misSecondFilter] == rollupLegalEntity && (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned");

            }, this);

            let gmBudgetData = _.filter(rollupGMBudgetData, function (misDataItem) {

              if (interval == "YOY_YTD" || interval == "YOY_LE_BUD")
                return misDataItem[this.misSecondFilter] == rollupLegalEntity && (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned");

              else
                return misDataItem[this.misSecondFilter] == rollupLegalEntity && (misDataItem.subType.trim() == "Budget");

            }, this);

            let sgaData = _.filter(rollupSGAData, function (misDataItem) {

              if (interval == "LE_BUD" || interval == "YOY_LE_BUD")
                return misDataItem[this.misSecondFilter] == rollupLegalEntity &&
                  ((moment(misDataItem.date).isSameOrBefore(this.latest_actual_date) && moment(misDataItem.date).isAfter(this.ftpyEndDate) && moment(misDataItem.date).isBefore(this.ftnyStartDate) && (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned")) ||
                    (moment(misDataItem.date).isAfter(this.latest_actual_date) && moment(misDataItem.date).isBefore(this.ftnyStartDate) && misDataItem.subType.trim() == "Budget"));

              else
                return misDataItem[this.misSecondFilter] == rollupLegalEntity && (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned");

            }, this);

            let sgaBudgetData = _.filter(rollupSGABudgetData, function (misDataItem) {

              if (interval == "YOY_YTD" || interval == "YOY_LE_BUD")
                return misDataItem[this.misSecondFilter] == rollupLegalEntity && (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned");

              else
                return misDataItem[this.misSecondFilter] == rollupLegalEntity && (misDataItem.subType.trim() == "Budget");

            }, this);

            let pbtData = _.filter(rollupPBTData, function (misDataItem) {

              if (interval == "LE_BUD" || interval == "YOY_LE_BUD")
                return misDataItem[this.misSecondFilter] == rollupLegalEntity &&
                  ((moment(misDataItem.date).isSameOrBefore(this.latest_actual_date) && moment(misDataItem.date).isAfter(this.ftpyEndDate) && moment(misDataItem.date).isBefore(this.ftnyStartDate) && (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned")) ||
                    (moment(misDataItem.date).isAfter(this.latest_actual_date) && moment(misDataItem.date).isBefore(this.ftnyStartDate) && misDataItem.subType.trim() == "Budget"));

              else
                return misDataItem[this.misSecondFilter] == rollupLegalEntity && (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned");

            }, this);

            let pbtBudgetData = _.filter(rollupPBTBudgetData, function (misDataItem) {

              if (interval == "YOY_YTD" || interval == "YOY_LE_BUD")
                return misDataItem[this.misSecondFilter] == rollupLegalEntity && (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned");

              else
                return misDataItem[this.misSecondFilter] == rollupLegalEntity && (misDataItem.subType.trim() == "Budget");

            }, this);

            revenue = revenueData.reduce((s, f) => {
              return s + f.value;
            }, 0.0);

            revenueBudget = revenueBudgetData.reduce((s, f) => {
              return s + f.value;
            }, 0.0);

            cost = costData.reduce((s, f) => {
              return s + f.value;
            }, 0.0);

            costBudget = costBudgetData.reduce((s, f) => {
              return s + f.value;
            }, 0.0);

            gm = gmData.reduce((s, f) => {
              return s + f.value;
            }, 0.0);

            gmBudget = gmBudgetData.reduce((s, f) => {
              return s + f.value;
            }, 0.0);

            sga = sgaData.reduce((s, f) => {
              return s + f.value;
            }, 0.0);

            sgaBudget = sgaBudgetData.reduce((s, f) => {
              return s + f.value;
            }, 0.0);

            pbt = pbtData.reduce((s, f) => {
              return s + f.value;
            }, 0.0);

            pbtBudget = pbtBudgetData.reduce((s, f) => {
              return s + f.value;
            }, 0.0);

            let shouldInsertSubItem = false;

            for (let visibleBackendPostingTypeIdsOtherThanDefaultItem of this.misParams.visibleBackendPostingTypeIdsOtherThanDefault) {

              let keyData = _.filter(keyDatabase["rollupKeyDataSep"], function (misDataItem) {

                if (interval == "LE_BUD" || interval == "YOY_LE_BUD")
                  return misDataItem.posting_type_id == visibleBackendPostingTypeIdsOtherThanDefaultItem && misDataItem[this.misSecondFilter] == rollupLegalEntity &&
                    ((moment(misDataItem.date).isSameOrBefore(this.latest_actual_date) && moment(misDataItem.date).isAfter(this.ftpyEndDate) && moment(misDataItem.date).isBefore(this.ftnyStartDate) && (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned")) ||
                      (moment(misDataItem.date).isAfter(this.latest_actual_date) && moment(misDataItem.date).isBefore(this.ftnyStartDate) && misDataItem.subType.trim() == "Budget"));

                else
                  return misDataItem.posting_type_id == visibleBackendPostingTypeIdsOtherThanDefaultItem && misDataItem[this.misSecondFilter] == rollupLegalEntity && (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned");

              }, this);

              let keyBudgetData = _.filter(keyDatabase["rollupKeyBudgetDataSep"], function (misDataItem) {

                if (interval == "YOY_YTD" || interval == "YOY_LE_BUD")
                  return misDataItem.posting_type_id == visibleBackendPostingTypeIdsOtherThanDefaultItem && misDataItem[this.misSecondFilter] == rollupLegalEntity && (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned");

                else
                  return misDataItem.posting_type_id == visibleBackendPostingTypeIdsOtherThanDefaultItem && misDataItem[this.misSecondFilter] == rollupLegalEntity && (misDataItem.subType.trim() == "Budget");

              }, this);

              let keyValue = keyData.reduce((s, f) => {
                return s + f.value;
              }, 0.0);

              let keyBudgetValue = keyBudgetData.reduce((s, f) => {
                return s + f.value;
              }, 0.0);

              itemKey[visibleBackendPostingTypeIdsOtherThanDefaultItem] = keyValue;
              itemKey[visibleBackendPostingTypeIdsOtherThanDefaultItem + "Budget"] = keyBudgetValue;
              itemKey[visibleBackendPostingTypeIdsOtherThanDefaultItem + "Variance"] = this.misFlags.varianceCalc == 'bma' ? keyBudgetValue - keyValue : keyValue - keyBudgetValue;
              itemKey[visibleBackendPostingTypeIdsOtherThanDefaultItem + "VarPerc"] = this.misFlags.varianceCalc == 'amb' ? (((keyBudgetValue - keyValue) == 0 || keyValue == 0) ? (this.misFlags.variancePerCalc == 'hmv' ? (100 - 0) : this.misFlags.variancePerCalc == 'hpv' ? (100 + 0) : 0) : (this.misFlags.variancePerCalc == 'hmv' ? (100 - (((keyBudgetValue - keyValue) / keyValue) * 100)) : this.misFlags.variancePerCalc == 'hpv' ? (100 + (((keyBudgetValue - keyValue) / keyValue) * 100)) : (((keyBudgetValue - keyValue) / keyValue) * 100))) : (((keyValue - keyBudgetValue) == 0 || keyBudgetValue == 0) ? (this.misFlags.variancePerCalc == 'hmv' ? (100 - 0) : this.misFlags.variancePerCalc == 'hpv' ? (100 + 0) : 0) : (this.misFlags.variancePerCalc == 'hmv' ? (100 - (((keyValue - keyBudgetValue) / keyBudgetValue) * 100)) : this.misFlags.variancePerCalc == 'hpv' ? (100 + (((keyValue - keyBudgetValue) / keyBudgetValue) * 100)) : (((keyValue - keyBudgetValue) / keyBudgetValue) * 100)));

              if (keyValue != 0 || keyBudgetValue != 0)
                shouldInsertSubItem = true;

            }

            for (let visibleFrontendPostingTypeIdsOtherThanDefaultItem of this.misParams.visibleFrontendPostingTypeIdsOtherThanDefault) {

              let currentPostingType = _.where(this.visibleMisCards, { posting_type_id: visibleFrontendPostingTypeIdsOtherThanDefaultItem });

              let keyValue = 0, keyBudgetValue = 0;

              if (currentPostingType && currentPostingType.length > 0 && currentPostingType[0].calculation) {

                let evalString = JSON.parse(currentPostingType[0].calculation);

                let dataString = "(" + evalString.calculation + ")", budgetDataString = "(" + evalString.calculation + ")";

                for (let posting_type_data_item of evalString.posting_type_data) {

                  let keyDataTemp = _.filter(totalData, function (misDataItem) {

                    if (interval == "LE_BUD" || interval == "YOY_LE_BUD")
                      return misDataItem.posting_type_id == parseInt(posting_type_data_item.posting_type_id) && misDataItem[this.misSecondFilter] == rollupLegalEntity &&
                        ((moment(misDataItem.date).isSameOrBefore(this.latest_actual_date) && moment(misDataItem.date).isAfter(this.ftpyEndDate) && moment(misDataItem.date).isBefore(this.ftnyStartDate) && (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned")) ||
                          (moment(misDataItem.date).isAfter(this.latest_actual_date) && moment(misDataItem.date).isBefore(this.ftnyStartDate) && misDataItem.subType.trim() == "Budget"));

                    else
                      return misDataItem.posting_type_id == parseInt(posting_type_data_item.posting_type_id) && misDataItem[this.misSecondFilter] == rollupLegalEntity && (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned");

                  }, this);

                  let keyBudgetDataTemp = _.filter(totalBudgetData, function (misDataItem) {

                    if (interval == "YOY_YTD" || interval == "YOY_LE_BUD")
                      return misDataItem.posting_type_id == parseInt(posting_type_data_item.posting_type_id) && misDataItem[this.misSecondFilter] == rollupLegalEntity && (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned");

                    else
                      return misDataItem.posting_type_id == parseInt(posting_type_data_item.posting_type_id) && misDataItem[this.misSecondFilter] == rollupLegalEntity && (misDataItem.subType.trim() == "Budget");

                  }, this);

                  let keyValueTemp = keyDataTemp.reduce((s, f) => {
                    return s + f.value;
                  }, 0.0);

                  let keyBudgetValueTemp = keyBudgetDataTemp.reduce((s, f) => {
                    return s + f.value;
                  }, 0.0);

                  dataString = dataString.replace(posting_type_data_item.replace_variable, keyValueTemp);

                  budgetDataString = budgetDataString.replace(posting_type_data_item.replace_variable, keyBudgetValueTemp);

                }

                keyValue = this.arithmeticEvalService.evaluate(dataString), keyBudgetValue = this.arithmeticEvalService.evaluate(budgetDataString);

                if (isNaN(keyValue))
                  keyValue = 0;

                if (isNaN(keyBudgetValue))
                  keyBudgetValue = 0;

              }

              itemKey[visibleFrontendPostingTypeIdsOtherThanDefaultItem] = keyValue;
              itemKey[visibleFrontendPostingTypeIdsOtherThanDefaultItem + "Budget"] = keyBudgetValue;
              itemKey[visibleFrontendPostingTypeIdsOtherThanDefaultItem + "Variance"] = this.misFlags.varianceCalc == 'bma' ? keyBudgetValue - keyValue : keyValue - keyBudgetValue;
              itemKey[visibleFrontendPostingTypeIdsOtherThanDefaultItem + "VarPerc"] = this.misFlags.varianceCalc == 'amb' ? (((keyBudgetValue - keyValue) == 0 || keyValue == 0) ? (this.misFlags.variancePerCalc == 'hmv' ? (100 - 0) : this.misFlags.variancePerCalc == 'hpv' ? (100 + 0) : 0) : (this.misFlags.variancePerCalc == 'hmv' ? (100 - (((keyBudgetValue - keyValue) / keyValue) * 100)) : this.misFlags.variancePerCalc == 'hpv' ? (100 + (((keyBudgetValue - keyValue) / keyValue) * 100)) : (((keyBudgetValue - keyValue) / keyValue) * 100))) : (((keyValue - keyBudgetValue) == 0 || keyBudgetValue == 0) ? (this.misFlags.variancePerCalc == 'hmv' ? (100 - 0) : this.misFlags.variancePerCalc == 'hpv' ? (100 + 0) : 0) : (this.misFlags.variancePerCalc == 'hmv' ? (100 - (((keyValue - keyBudgetValue) / keyBudgetValue) * 100)) : this.misFlags.variancePerCalc == 'hpv' ? (100 + (((keyValue - keyBudgetValue) / keyBudgetValue) * 100)) : (((keyValue - keyBudgetValue) / keyBudgetValue) * 100)));

              if (keyValue != 0 || keyBudgetValue != 0)
                shouldInsertSubItem = true;

            }

            if (revenue != 0 || cost != 0 || gm != 0 || sga != 0 || pbt != 0 || revenueBudget != 0 || costBudget != 0 || gmBudget != 0 || sgaBudget != 0 || pbtBudget != 0)
              shouldInsertSubItem = true;

            if (rollupData.length > 0)
              verticalId = rollupData[0].sub_pl_id;

            else if (isForOpportunityCard) {

              for (let le of this.legalEntitiesData)
                if (le.plId == rollupLegalEntity) {

                  if (le.rollupLevels.length == 2)
                    verticalId = le.plId;

                  else
                    verticalId = le.rollupLevels[le.rollupLevels.length - 3].rollUpPlId;

                }

            }

            projectId = rollupData.length > 0 ? (rollupData[0].project_id ? rollupData[0].project_id : 0) : 0;

            if ((!isForDownload && this.baseLegalEntityLevel == 2) || (isForDownload && this.downloadLeLevel == 2))
              projectItemName = rollupLegalEntityName;

            if (shouldInsertSubItem || (isForOpportunityCard && totalMisData.length == 0)) {

              let subItem = {
                legalEntity: rollupLegalEntityName,
                plId: plId,
                verticalId: verticalId,
                projectItemName: projectItemName,
                projectId: projectId,
                [this.misFlags.reportRevenueTypeId]: revenue,
                [this.misFlags.reportRevenueTypeId + 'Budget']: revenueBudget,
                [this.misFlags.reportRevenueTypeId + 'Variance']: this.misFlags.varianceCalc == 'bma' ? revenueBudget - revenue : revenue - revenueBudget,
                [this.misFlags.reportRevenueTypeId + 'VarPerc']: this.misFlags.varianceCalc == 'amb' ? (((revenueBudget - revenue) == 0 || revenue == 0) ? (this.misFlags.variancePerCalc == 'hmv' ? (100 - 0) : this.misFlags.variancePerCalc == 'hpv' ? (100 + 0) : 0) : (this.misFlags.variancePerCalc == 'hmv' ? (100 - (((revenueBudget - revenue) / revenue) * 100)) : this.misFlags.variancePerCalc == 'hpv' ? (100 + (((revenueBudget - revenue) / revenue) * 100)) : (((revenueBudget - revenue) / revenue) * 100))) : (((revenue - revenueBudget) == 0 || revenueBudget == 0) ? (this.misFlags.variancePerCalc == 'hmv' ? (100 - 0) : this.misFlags.variancePerCalc == 'hpv' ? (100 + 0) : 0) : (this.misFlags.variancePerCalc == 'hmv' ? (100 - (((revenue - revenueBudget) / revenueBudget) * 100)) : this.misFlags.variancePerCalc == 'hpv' ? (100 + (((revenue - revenueBudget) / revenueBudget) * 100)) : (((revenue - revenueBudget) / revenueBudget) * 100))),
                [this.misFlags.reportCostTypeId]: cost,
                [this.misFlags.reportCostTypeId + 'Budget']: costBudget,
                [this.misFlags.reportCostTypeId + 'Variance']: this.misFlags.varianceCalc == 'bma' ? costBudget - cost : cost - costBudget,
                [this.misFlags.reportCostTypeId + 'VarPerc']: this.misFlags.varianceCalc == 'amb' ? (((costBudget - cost) == 0 || cost == 0) ? (this.misFlags.variancePerCalc == 'hmv' ? (100 - 0) : this.misFlags.variancePerCalc == 'hpv' ? (100 + 0) : 0) : (this.misFlags.variancePerCalc == 'hmv' ? (100 - (((costBudget - cost) / cost) * 100)) : this.misFlags.variancePerCalc == 'hpv' ? (100 + (((costBudget - cost) / cost) * 100)) : (((costBudget - cost) / cost) * 100))) : (((cost - costBudget) == 0 || costBudget == 0) ? (this.misFlags.variancePerCalc == 'hmv' ? (100 - 0) : this.misFlags.variancePerCalc == 'hpv' ? (100 + 0) : 0) : (this.misFlags.variancePerCalc == 'hmv' ? (100 - (((cost - costBudget) / costBudget) * 100)) : this.misFlags.variancePerCalc == 'hpv' ? (100 + (((cost - costBudget) / costBudget) * 100)) : (((cost - costBudget) / costBudget) * 100))),
                [this.misFlags.reportGmTypeId]: gm,
                [this.misFlags.reportGmTypeId + 'Budget']: gmBudget,
                [this.misFlags.reportGmTypeId + 'Variance']: this.misFlags.varianceCalc == 'bma' ? gmBudget - gm : gm - gmBudget,
                [this.misFlags.reportGmTypeId + 'VarPerc']: this.misFlags.varianceCalc == 'amb' ? (((gmBudget - gm) == 0 || gm == 0) ? (this.misFlags.variancePerCalc == 'hmv' ? (100 - 0) : this.misFlags.variancePerCalc == 'hpv' ? (100 + 0) : 0) : (this.misFlags.variancePerCalc == 'hmv' ? (100 - (((gmBudget - gm) / gm) * 100)) : this.misFlags.variancePerCalc == 'hpv' ? (100 + (((gmBudget - gm) / gm) * 100)) : (((gmBudget - gm) / gm) * 100))) : (((gm - gmBudget) == 0 || gmBudget == 0) ? (this.misFlags.variancePerCalc == 'hmv' ? (100 - 0) : this.misFlags.variancePerCalc == 'hpv' ? (100 + 0) : 0) : (this.misFlags.variancePerCalc == 'hmv' ? (100 - (((gm - gmBudget) / gmBudget) * 100)) : this.misFlags.variancePerCalc == 'hpv' ? (100 + (((gm - gmBudget) / gmBudget) * 100)) : (((gm - gmBudget) / gmBudget) * 100))),
                [this.misFlags.reportSgaTypeId]: sga,
                [this.misFlags.reportSgaTypeId + 'Budget']: sgaBudget,
                [this.misFlags.reportSgaTypeId + 'Variance']: this.misFlags.varianceCalc == 'bma' ? sgaBudget - sga : sga - sgaBudget,
                [this.misFlags.reportSgaTypeId + 'VarPerc']: this.misFlags.varianceCalc == 'amb' ? (((sgaBudget - sga) == 0 || sga == 0) ? (this.misFlags.variancePerCalc == 'hmv' ? (100 - 0) : this.misFlags.variancePerCalc == 'hpv' ? (100 + 0) : 0) : (this.misFlags.variancePerCalc == 'hmv' ? (100 - (((sgaBudget - sga) / sga) * 100)) : this.misFlags.variancePerCalc == 'hpv' ? (100 + (((sgaBudget - sga) / sga) * 100)) : (((sgaBudget - sga) / sga) * 100))) : (((sga - sgaBudget) == 0 || sgaBudget == 0) ? (this.misFlags.variancePerCalc == 'hmv' ? (100 - 0) : this.misFlags.variancePerCalc == 'hpv' ? (100 + 0) : 0) : (this.misFlags.variancePerCalc == 'hmv' ? (100 - (((sga - sgaBudget) / sgaBudget) * 100)) : this.misFlags.variancePerCalc == 'hpv' ? (100 + (((sga - sgaBudget) / sgaBudget) * 100)) : (((sga - sgaBudget) / sgaBudget) * 100))),
                [this.misFlags.reportPbtTypeId]: pbt,
                [this.misFlags.reportPbtTypeId + 'Budget']: pbtBudget,
                [this.misFlags.reportPbtTypeId + 'Variance']: this.misFlags.varianceCalc == 'bma' ? pbtBudget - pbt : pbt - pbtBudget,
                [this.misFlags.reportPbtTypeId + 'VarPerc']: this.misFlags.varianceCalc == 'amb' ? (((pbtBudget - pbt) == 0 || pbt == 0) ? (this.misFlags.variancePerCalc == 'hmv' ? (100 - 0) : this.misFlags.variancePerCalc == 'hpv' ? (100 + 0) : 0) : (this.misFlags.variancePerCalc == 'hmv' ? (100 - (((pbtBudget - pbt) / pbt) * 100)) : this.misFlags.variancePerCalc == 'hpv' ? (100 + (((pbtBudget - pbt) / pbt) * 100)) : (((pbtBudget - pbt) / pbt) * 100))) : (((pbt - pbtBudget) == 0 || pbtBudget == 0) ? (this.misFlags.variancePerCalc == 'hmv' ? (100 - 0) : this.misFlags.variancePerCalc == 'hpv' ? (100 + 0) : 0) : (this.misFlags.variancePerCalc == 'hmv' ? (100 - (((pbt - pbtBudget) / pbtBudget) * 100)) : this.misFlags.variancePerCalc == 'hpv' ? (100 + (((pbt - pbtBudget) / pbtBudget) * 100)) : (((pbt - pbtBudget) / pbtBudget) * 100))),
                isSubItem: true
              };

              subItem = this.jsonConcat(subItem, itemKey);

              subItems.push(subItem);

            }

          }

        }

      }

      if ((!isForDownload && this.baseLegalEntityLevel > 1) || (isForDownload && this.downloadLeLevel > 1)) {

        if (rollupData.length > 0)
          verticalId = rollupData[0].sub_pl_id;

        else if (isForOpportunityCard) {

          for (let le of this.legalEntitiesData)
            if (le.plId == legalEntity) {

              if (le.rollupLevels.length == 2)
                verticalId = le.plId;

              else
                verticalId = le.rollupLevels[le.rollupLevels.length - 3].rollUpPlId;

            }

        }

        projectId = rollupData.length > 0 ? rollupData[0].project_id : 0;

      }

      if ((!isForDownload && this.baseLegalEntityLevel == 3) || (isForDownload && this.downloadLeLevel == 3))
        projectItemName = legalEntityName;

      let shouldInsertItem = false;

      for (let visibleBackendPostingTypeIdsOtherThanDefaultItem of this.misParams.visibleBackendPostingTypeIdsOtherThanDefault) {

        let rollupKeyData = _.filter(keyDatabase["totalKeyDataSep"], function (misDataItem) {

          if (interval == "LE_BUD" || interval == "YOY_LE_BUD")
            return misDataItem.posting_type_id == visibleBackendPostingTypeIdsOtherThanDefaultItem && misDataItem[this.misFirstFilter] == legalEntity &&
              ((moment(misDataItem.date).isSameOrBefore(this.latest_actual_date) && moment(misDataItem.date).isAfter(this.ftpyEndDate) && moment(misDataItem.date).isBefore(this.ftnyStartDate) && (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned")) ||
                (moment(misDataItem.date).isAfter(this.latest_actual_date) && moment(misDataItem.date).isBefore(this.ftnyStartDate) && misDataItem.subType.trim() == "Budget"));

          else
            return misDataItem.posting_type_id == visibleBackendPostingTypeIdsOtherThanDefaultItem && misDataItem[this.misFirstFilter] == legalEntity && (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned");

        }, this);

        let rollupKeyBudgetData = _.filter(keyDatabase["totalKeyBudgetDataSep"], function (misDataItem) {

          if (interval == "YOY_YTD" || interval == "YOY_LE_BUD")
            return misDataItem.posting_type_id == visibleBackendPostingTypeIdsOtherThanDefaultItem && misDataItem[this.misFirstFilter] == legalEntity && (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned");

          else
            return misDataItem.posting_type_id == visibleBackendPostingTypeIdsOtherThanDefaultItem && misDataItem[this.misFirstFilter] == legalEntity && (misDataItem.subType.trim() == "Budget");

        }, this);

        let rollupKeyValue = rollupKeyData.reduce((s, f) => {
          return s + f.value;
        }, 0.0);

        let rollupKeyBudgetValue = rollupKeyBudgetData.reduce((s, f) => {
          return s + f.value;
        }, 0.0);

        rollupKey[visibleBackendPostingTypeIdsOtherThanDefaultItem] = rollupKeyValue;
        rollupKey[visibleBackendPostingTypeIdsOtherThanDefaultItem + "Budget"] = rollupKeyBudgetValue;
        rollupKey[visibleBackendPostingTypeIdsOtherThanDefaultItem + "Variance"] = this.misFlags.varianceCalc == 'bma' ? rollupKeyBudgetValue - rollupKeyValue : rollupKeyValue - rollupKeyBudgetValue;
        rollupKey[visibleBackendPostingTypeIdsOtherThanDefaultItem + "VarPerc"] = this.misFlags.varianceCalc == 'amb' ? (((rollupKeyBudgetValue - rollupKeyValue) == 0 || rollupKeyValue == 0) ? (this.misFlags.variancePerCalc == 'hmv' ? (100 - 0) : this.misFlags.variancePerCalc == 'hpv' ? (100 + 0) : 0) : (this.misFlags.variancePerCalc == 'hmv' ? (100 - (((rollupKeyBudgetValue - rollupKeyValue) / rollupKeyValue) * 100)) : this.misFlags.variancePerCalc == 'hpv' ? (100 + (((rollupKeyBudgetValue - rollupKeyValue) / rollupKeyValue) * 100)) : (((rollupKeyBudgetValue - rollupKeyValue) / rollupKeyValue) * 100))) : (((rollupKeyValue - rollupKeyBudgetValue) == 0 || rollupKeyBudgetValue == 0) ? (this.misFlags.variancePerCalc == 'hmv' ? (100 - 0) : this.misFlags.variancePerCalc == 'hpv' ? (100 + 0) : 0) : (this.misFlags.variancePerCalc == 'hmv' ? (100 - (((rollupKeyValue - rollupKeyBudgetValue) / rollupKeyBudgetValue) * 100)) : this.misFlags.variancePerCalc == 'hpv' ? (100 + (((rollupKeyValue - rollupKeyBudgetValue) / rollupKeyBudgetValue) * 100)) : (((rollupKeyValue - rollupKeyBudgetValue) / rollupKeyBudgetValue) * 100)));

        if (rollupKeyValue != 0 || rollupKeyBudgetValue != 0)
          shouldInsertItem = true;

      }

      for (let visibleFrontendPostingTypeIdsOtherThanDefaultItem of this.misParams.visibleFrontendPostingTypeIdsOtherThanDefault) {

        let currentPostingType = _.where(this.visibleMisCards, { posting_type_id: visibleFrontendPostingTypeIdsOtherThanDefaultItem });

        let rollupKeyValue = 0, rollupKeyBudgetValue = 0;

        if (currentPostingType && currentPostingType.length > 0 && currentPostingType[0].calculation) {

          let evalString = JSON.parse(currentPostingType[0].calculation);

          let dataString = "(" + evalString.calculation + ")", budgetDataString = "(" + evalString.calculation + ")";

          for (let posting_type_data_item of evalString.posting_type_data) {

            let rollupKeyDataTemp = _.filter(totalData, function (misDataItem) {

              if (interval == "LE_BUD" || interval == "YOY_LE_BUD")
                return misDataItem.posting_type_id == parseInt(posting_type_data_item.posting_type_id) && misDataItem[this.misFirstFilter] == legalEntity &&
                  ((moment(misDataItem.date).isSameOrBefore(this.latest_actual_date) && moment(misDataItem.date).isAfter(this.ftpyEndDate) && moment(misDataItem.date).isBefore(this.ftnyStartDate) && (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned")) ||
                    (moment(misDataItem.date).isAfter(this.latest_actual_date) && moment(misDataItem.date).isBefore(this.ftnyStartDate) && misDataItem.subType.trim() == "Budget"));

              else
                return misDataItem.posting_type_id == parseInt(posting_type_data_item.posting_type_id) && misDataItem[this.misFirstFilter] == legalEntity && (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned");

            }, this);

            let rollupKeyBudgetDataTemp = _.filter(totalBudgetData, function (misDataItem) {

              if (interval == "YOY_YTD" || interval == "YOY_LE_BUD")
                return misDataItem.posting_type_id == parseInt(posting_type_data_item.posting_type_id) && misDataItem[this.misFirstFilter] == legalEntity && (misDataItem.subType.trim() == "Actual" || misDataItem.subType.trim() == "Planned");

              else
                return misDataItem.posting_type_id == parseInt(posting_type_data_item.posting_type_id) && misDataItem[this.misFirstFilter] == legalEntity && (misDataItem.subType.trim() == "Budget");

            }, this);

            let rollupKeyValueTemp = rollupKeyDataTemp.reduce((s, f) => {
              return s + f.value;
            }, 0.0);

            let rollupKeyBudgetValueTemp = rollupKeyBudgetDataTemp.reduce((s, f) => {
              return s + f.value;
            }, 0.0);

            dataString = dataString.replace(posting_type_data_item.replace_variable, rollupKeyValueTemp);

            budgetDataString = budgetDataString.replace(posting_type_data_item.replace_variable, rollupKeyBudgetValueTemp);

          }

          rollupKeyValue = this.arithmeticEvalService.evaluate(dataString), rollupKeyBudgetValue = this.arithmeticEvalService.evaluate(budgetDataString);

          if (isNaN(rollupKeyValue))
            rollupKeyValue = 0;

          if (isNaN(rollupKeyBudgetValue))
            rollupKeyBudgetValue = 0;

        }

        rollupKey[visibleFrontendPostingTypeIdsOtherThanDefaultItem] = rollupKeyValue;
        rollupKey[visibleFrontendPostingTypeIdsOtherThanDefaultItem + "Budget"] = rollupKeyBudgetValue;
        rollupKey[visibleFrontendPostingTypeIdsOtherThanDefaultItem + "Variance"] = this.misFlags.varianceCalc == 'bma' ? rollupKeyBudgetValue - rollupKeyValue : rollupKeyValue - rollupKeyBudgetValue;
        rollupKey[visibleFrontendPostingTypeIdsOtherThanDefaultItem + "VarPerc"] = this.misFlags.varianceCalc == 'amb' ? (((rollupKeyBudgetValue - rollupKeyValue) == 0 || rollupKeyValue == 0) ? (this.misFlags.variancePerCalc == 'hmv' ? (100 - 0) : this.misFlags.variancePerCalc == 'hpv' ? (100 + 0) : 0) : (this.misFlags.variancePerCalc == 'hmv' ? (100 - (((rollupKeyBudgetValue - rollupKeyValue) / rollupKeyValue) * 100)) : this.misFlags.variancePerCalc == 'hpv' ? (100 + (((rollupKeyBudgetValue - rollupKeyValue) / rollupKeyValue) * 100)) : (((rollupKeyBudgetValue - rollupKeyValue) / rollupKeyValue) * 100))) : (((rollupKeyValue - rollupKeyBudgetValue) == 0 || rollupKeyBudgetValue == 0) ? (this.misFlags.variancePerCalc == 'hmv' ? (100 - 0) : this.misFlags.variancePerCalc == 'hpv' ? (100 + 0) : 0) : (this.misFlags.variancePerCalc == 'hmv' ? (100 - (((rollupKeyValue - rollupKeyBudgetValue) / rollupKeyBudgetValue) * 100)) : this.misFlags.variancePerCalc == 'hpv' ? (100 + (((rollupKeyValue - rollupKeyBudgetValue) / rollupKeyBudgetValue) * 100)) : (((rollupKeyValue - rollupKeyBudgetValue) / rollupKeyBudgetValue) * 100)));

        if (rollupKeyValue != 0 || rollupKeyBudgetValue != 0)
          shouldInsertItem = true;

      }

      if (rollupRevenue != 0 || rollupCost != 0 || rollupGM != 0 || rollupSGA != 0 || rollupPBT != 0 || rollupRevenueBudget != 0 || rollupCostBudget != 0 || rollupGMBudget != 0 || rollupSGABudget != 0 || rollupPBTBudget != 0)
        shouldInsertItem = true;

      if (shouldInsertItem || (isForOpportunityCard && totalMisData.length == 0)) {

        if (this.misFlags.shouldSortByLegalEntityByDefault)
          subItems = subItems.sort((a, b) => {
            if (a.legalEntity < b.legalEntity)
              return -1;
            else if (a.legalEntity > b.legalEntity)
              return 1;
            else
              return 0;
          });

        let item = {
          isOpportunityItem: false,
          legalEntity: legalEntityName,
          plId: plId,
          verticalId: verticalId,
          projectItemName: projectItemName,
          projectId: projectId,
          [this.misFlags.reportRevenueTypeId]: rollupRevenue,
          [this.misFlags.reportRevenueTypeId + 'Budget']: rollupRevenueBudget,
          [this.misFlags.reportRevenueTypeId + 'Variance']: this.misFlags.varianceCalc == 'bma' ? rollupRevenueBudget - rollupRevenue : rollupRevenue - rollupRevenueBudget,
          [this.misFlags.reportRevenueTypeId + 'VarPerc']: this.misFlags.varianceCalc == 'amb' ? (((rollupRevenueBudget - rollupRevenue) == 0 || rollupRevenue == 0) ? (this.misFlags.variancePerCalc == 'hmv' ? (100 - 0) : this.misFlags.variancePerCalc == 'hpv' ? (100 + 0) : 0) : (this.misFlags.variancePerCalc == 'hmv' ? (100 - (((rollupRevenueBudget - rollupRevenue) / rollupRevenue) * 100)) : this.misFlags.variancePerCalc == 'hpv' ? (100 + (((rollupRevenueBudget - rollupRevenue) / rollupRevenue) * 100)) : (((rollupRevenueBudget - rollupRevenue) / rollupRevenue) * 100))) : (((rollupRevenue - rollupRevenueBudget) == 0 || rollupRevenueBudget == 0) ? (this.misFlags.variancePerCalc == 'hmv' ? (100 - 0) : this.misFlags.variancePerCalc == 'hpv' ? (100 + 0) : 0) : (this.misFlags.variancePerCalc == 'hmv' ? (100 - (((rollupRevenue - rollupRevenueBudget) / rollupRevenueBudget) * 100)) : this.misFlags.variancePerCalc == 'hpv' ? (100 + (((rollupRevenue - rollupRevenueBudget) / rollupRevenueBudget) * 100)) : (((rollupRevenue - rollupRevenueBudget) / rollupRevenueBudget) * 100))),
          [this.misFlags.reportCostTypeId]: rollupCost,
          [this.misFlags.reportCostTypeId + 'Budget']: rollupCostBudget,
          [this.misFlags.reportCostTypeId + 'Variance']: this.misFlags.varianceCalc == 'bma' ? rollupCostBudget - rollupCost : rollupCost - rollupCostBudget,
          [this.misFlags.reportCostTypeId + 'VarPerc']: this.misFlags.varianceCalc == 'amb' ? (((rollupCostBudget - rollupCost) == 0 || rollupCost == 0) ? (this.misFlags.variancePerCalc == 'hmv' ? (100 - 0) : this.misFlags.variancePerCalc == 'hpv' ? (100 + 0) : 0) : (this.misFlags.variancePerCalc == 'hmv' ? (100 - (((rollupCostBudget - rollupCost) / rollupCost) * 100)) : this.misFlags.variancePerCalc == 'hpv' ? (100 + (((rollupCostBudget - rollupCost) / rollupCost) * 100)) : (((rollupCostBudget - rollupCost) / rollupCost) * 100))) : (((rollupCost - rollupCostBudget) == 0 || rollupCostBudget == 0) ? (this.misFlags.variancePerCalc == 'hmv' ? (100 - 0) : this.misFlags.variancePerCalc == 'hpv' ? (100 + 0) : 0) : (this.misFlags.variancePerCalc == 'hmv' ? (100 - (((rollupCost - rollupCostBudget) / rollupCostBudget) * 100)) : this.misFlags.variancePerCalc == 'hpv' ? (100 + (((rollupCost - rollupCostBudget) / rollupCostBudget) * 100)) : (((rollupCost - rollupCostBudget) / rollupCostBudget) * 100))),
          [this.misFlags.reportGmTypeId]: rollupGM,
          [this.misFlags.reportGmTypeId + 'Budget']: rollupGMBudget,
          [this.misFlags.reportGmTypeId + 'Variance']: this.misFlags.varianceCalc == 'bma' ? rollupGMBudget - rollupGM : rollupGM - rollupGMBudget,
          [this.misFlags.reportGmTypeId + 'VarPerc']: this.misFlags.varianceCalc == 'amb' ? (((rollupGMBudget - rollupGM) == 0 || rollupGM == 0) ? (this.misFlags.variancePerCalc == 'hmv' ? (100 - 0) : this.misFlags.variancePerCalc == 'hpv' ? (100 + 0) : 0) : (this.misFlags.variancePerCalc == 'hmv' ? (100 - (((rollupGMBudget - rollupGM) / rollupGM) * 100)) : this.misFlags.variancePerCalc == 'hpv' ? (100 + (((rollupGMBudget - rollupGM) / rollupGM) * 100)) : (((rollupGMBudget - rollupGM) / rollupGM) * 100))) : (((rollupGM - rollupGMBudget) == 0 || rollupGMBudget == 0) ? (this.misFlags.variancePerCalc == 'hmv' ? (100 - 0) : this.misFlags.variancePerCalc == 'hpv' ? (100 + 0) : 0) : (this.misFlags.variancePerCalc == 'hmv' ? (100 - (((rollupGM - rollupGMBudget) / rollupGMBudget) * 100)) : this.misFlags.variancePerCalc == 'hpv' ? (100 + (((rollupGM - rollupGMBudget) / rollupGMBudget) * 100)) : (((rollupGM - rollupGMBudget) / rollupGMBudget) * 100))),
          [this.misFlags.reportSgaTypeId]: rollupSGA,
          [this.misFlags.reportSgaTypeId + 'Budget']: rollupSGABudget,
          [this.misFlags.reportSgaTypeId + 'Variance']: this.misFlags.varianceCalc == 'bma' ? rollupSGABudget - rollupSGA : rollupSGA - rollupSGABudget,
          [this.misFlags.reportSgaTypeId + 'VarPerc']: this.misFlags.varianceCalc == 'amb' ? (((rollupSGABudget - rollupSGA) == 0 || rollupSGA == 0) ? (this.misFlags.variancePerCalc == 'hmv' ? (100 - 0) : this.misFlags.variancePerCalc == 'hpv' ? (100 + 0) : 0) : (this.misFlags.variancePerCalc == 'hmv' ? (100 - (((rollupSGABudget - rollupSGA) / rollupSGA) * 100)) : this.misFlags.variancePerCalc == 'hpv' ? (100 + (((rollupSGABudget - rollupSGA) / rollupSGA) * 100)) : (((rollupSGABudget - rollupSGA) / rollupSGA) * 100))) : (((rollupSGA - rollupSGABudget) == 0 || rollupSGABudget == 0) ? (this.misFlags.variancePerCalc == 'hmv' ? (100 - 0) : this.misFlags.variancePerCalc == 'hpv' ? (100 + 0) : 0) : (this.misFlags.variancePerCalc == 'hmv' ? (100 - (((rollupSGA - rollupSGABudget) / rollupSGABudget) * 100)) : this.misFlags.variancePerCalc == 'hpv' ? (100 + (((rollupSGA - rollupSGABudget) / rollupSGABudget) * 100)) : (((rollupSGA - rollupSGABudget) / rollupSGABudget) * 100))),
          [this.misFlags.reportPbtTypeId]: rollupPBT,
          [this.misFlags.reportPbtTypeId + 'Budget']: rollupPBTBudget,
          [this.misFlags.reportPbtTypeId + 'Variance']: this.misFlags.varianceCalc == 'bma' ? rollupPBTBudget - rollupPBT : rollupPBT - rollupPBTBudget,
          [this.misFlags.reportPbtTypeId + 'VarPerc']: this.misFlags.varianceCalc == 'amb' ? (((rollupPBTBudget - rollupPBT) == 0 || rollupPBT == 0) ? (this.misFlags.variancePerCalc == 'hmv' ? (100 - 0) : this.misFlags.variancePerCalc == 'hpv' ? (100 + 0) : 0) : (this.misFlags.variancePerCalc == 'hmv' ? (100 - (((rollupPBTBudget - rollupPBT) / rollupPBT) * 100)) : this.misFlags.variancePerCalc == 'hpv' ? (100 + (((rollupPBTBudget - rollupPBT) / rollupPBT) * 100)) : (((rollupPBTBudget - rollupPBT) / rollupPBT) * 100))) : (((rollupPBT - rollupPBTBudget) == 0 || rollupPBTBudget == 0) ? (this.misFlags.variancePerCalc == 'hmv' ? (100 - 0) : this.misFlags.variancePerCalc == 'hpv' ? (100 + 0) : 0) : (this.misFlags.variancePerCalc == 'hmv' ? (100 - (((rollupPBT - rollupPBTBudget) / rollupPBTBudget) * 100)) : this.misFlags.variancePerCalc == 'hpv' ? (100 + (((rollupPBT - rollupPBTBudget) / rollupPBTBudget) * 100)) : (((rollupPBT - rollupPBTBudget) / rollupPBTBudget) * 100))),
          subItems: subItems,
          isExpanded: false
        };

        item = this.jsonConcat(item, rollupKey);

        items.push(item);

      }

    }

    if (items.length == 1 && ((!isForDownload && items[0].legalEntity == this.detailedModeBaseLegalEntity) || (isForDownload && items[0].legalEntity == this.downloadLe)) && ((!isForDownload && this.baseLegalEntityLevel > 1) || (isForDownload && this.downloadLeLevel > 1)))
      if (items[0].subItems && items[0].subItems.length > 0) {

        let newItems = [];

        for (let subItem of items[0].subItems) {

          subItem["isExpanded"] = false;
          subItem["subItems"] = [];
          subItem.isSubItem = false;

          newItems.push(subItem);

        }

        items = newItems;

      }

    if (this.misFlags.shouldSortByLegalEntityByDefault)
      items = items.sort((a, b) => {
        if (a.legalEntity < b.legalEntity)
          return -1;
        else if (a.legalEntity > b.legalEntity)
          return 1;
        else
          return 0;
      });

    let intervalName = "", intervalDescription = "";

    let intervalArray = isForOpportunityCard ? this.udrfService.udrfData.appliedConfig["customFields"]["misOpportunityCards"] : this.udrfService.udrfData.appliedConfig["customFields"]["misCards"];

    let codeInterval = interval;

    if (isForOpportunityCard)
      codeInterval += "_BB";

    let intervalCard = _.where(intervalArray, { dataTypeCode: codeInterval });

    if (intervalCard && intervalCard.length > 0) {

      intervalName = intervalCard[0].dataTypeName;
      intervalDescription = intervalCard[0].dataTypeDescription;

    }

    let resolvedMisDataKey = {
      interval: intervalName,
      intervalCode: codeInterval,
      intervalDescription: intervalDescription,
      ["total" + this.misFlags.reportRevenueTypeId]: totalRevenue,
      ["total" + this.misFlags.reportRevenueTypeId + "Budget"]: totalRevenueBudget,
      ["total" + this.misFlags.reportRevenueTypeId + "Variance"]: this.misFlags.varianceCalc == 'bma' ? totalRevenueBudget - totalRevenue : totalRevenue - totalRevenueBudget,
      ["total" + this.misFlags.reportRevenueTypeId + "VarPerc"]: this.misFlags.varianceCalc == 'amb' ? (((totalRevenueBudget - totalRevenue) == 0 || totalRevenue == 0) ? (this.misFlags.variancePerCalc == 'hmv' ? (100 - 0) : this.misFlags.variancePerCalc == 'hpv' ? (100 + 0) : 0) : (this.misFlags.variancePerCalc == 'hmv' ? (100 - (((totalRevenueBudget - totalRevenue) / totalRevenue) * 100)) : this.misFlags.variancePerCalc == 'hpv' ? (100 + (((totalRevenueBudget - totalRevenue) / totalRevenue) * 100)) : (((totalRevenueBudget - totalRevenue) / totalRevenue) * 100))) : (((totalRevenue - totalRevenueBudget) == 0 || totalRevenueBudget == 0) ? (this.misFlags.variancePerCalc == 'hmv' ? (100 - 0) : this.misFlags.variancePerCalc == 'hpv' ? (100 + 0) : 0) : (this.misFlags.variancePerCalc == 'hmv' ? (100 - (((totalRevenue - totalRevenueBudget) / totalRevenueBudget) * 100)) : this.misFlags.variancePerCalc == 'hpv' ? (100 + (((totalRevenue - totalRevenueBudget) / totalRevenueBudget) * 100)) : (((totalRevenue - totalRevenueBudget) / totalRevenueBudget) * 100))),
      ["total" + this.misFlags.reportCostTypeId]: totalCost,
      ["total" + this.misFlags.reportCostTypeId + "Budget"]: totalCostBudget,
      ["total" + this.misFlags.reportCostTypeId + "Variance"]: this.misFlags.varianceCalc == 'bma' ? totalCostBudget - totalCost : totalCost - totalCostBudget,
      ["total" + this.misFlags.reportCostTypeId + "VarPerc"]: this.misFlags.varianceCalc == 'amb' ? (((totalCostBudget - totalCost) == 0 || totalCost == 0) ? (this.misFlags.variancePerCalc == 'hmv' ? (100 - 0) : this.misFlags.variancePerCalc == 'hpv' ? (100 + 0) : 0) : (this.misFlags.variancePerCalc == 'hmv' ? (100 - (((totalCostBudget - totalCost) / totalCost) * 100)) : this.misFlags.variancePerCalc == 'hpv' ? (100 + (((totalCostBudget - totalCost) / totalCost) * 100)) : (((totalCostBudget - totalCost) / totalCost) * 100))) : (((totalCost - totalCostBudget) == 0 || totalCostBudget == 0) ? (this.misFlags.variancePerCalc == 'hmv' ? (100 - 0) : this.misFlags.variancePerCalc == 'hpv' ? (100 + 0) : 0) : (this.misFlags.variancePerCalc == 'hmv' ? (100 - (((totalCost - totalCostBudget) / totalCostBudget) * 100)) : this.misFlags.variancePerCalc == 'hpv' ? (100 + (((totalCost - totalCostBudget) / totalCostBudget) * 100)) : (((totalCost - totalCostBudget) / totalCostBudget) * 100))),
      ["total" + this.misFlags.reportGmTypeId]: totalGM,
      ["total" + this.misFlags.reportGmTypeId + "Budget"]: totalGMBudget,
      ["total" + this.misFlags.reportGmTypeId + "Variance"]: this.misFlags.varianceCalc == 'bma' ? totalGMBudget - totalGM : totalGM - totalGMBudget,
      ["total" + this.misFlags.reportGmTypeId + "VarPerc"]: this.misFlags.varianceCalc == 'amb' ? (((totalGMBudget - totalGM) == 0 || totalGM == 0) ? (this.misFlags.variancePerCalc == 'hmv' ? (100 - 0) : this.misFlags.variancePerCalc == 'hpv' ? (100 + 0) : 0) : (this.misFlags.variancePerCalc == 'hmv' ? (100 - (((totalGMBudget - totalGM) / totalGM) * 100)) : this.misFlags.variancePerCalc == 'hpv' ? (100 + (((totalGMBudget - totalGM) / totalGM) * 100)) : (((totalGMBudget - totalGM) / totalGM) * 100))) : (((totalGM - totalGMBudget) == 0 || totalGMBudget == 0) ? (this.misFlags.variancePerCalc == 'hmv' ? (100 - 0) : this.misFlags.variancePerCalc == 'hpv' ? (100 + 0) : 0) : (this.misFlags.variancePerCalc == 'hmv' ? (100 - (((totalGM - totalGMBudget) / totalGMBudget) * 100)) : this.misFlags.variancePerCalc == 'hpv' ? (100 + (((totalGM - totalGMBudget) / totalGMBudget) * 100)) : (((totalGM - totalGMBudget) / totalGMBudget) * 100))),
      ["total" + this.misFlags.reportSgaTypeId]: totalSGA,
      ["total" + this.misFlags.reportSgaTypeId + "Budget"]: totalSGABudget,
      ["total" + this.misFlags.reportSgaTypeId + "Variance"]: this.misFlags.varianceCalc == 'bma' ? totalSGABudget - totalSGA : totalSGA - totalSGABudget,
      ["total" + this.misFlags.reportSgaTypeId + "VarPerc"]: this.misFlags.varianceCalc == 'amb' ? (((totalSGABudget - totalSGA) == 0 || totalSGA == 0) ? (this.misFlags.variancePerCalc == 'hmv' ? (100 - 0) : this.misFlags.variancePerCalc == 'hpv' ? (100 + 0) : 0) : (this.misFlags.variancePerCalc == 'hmv' ? (100 - (((totalSGABudget - totalSGA) / totalSGA) * 100)) : this.misFlags.variancePerCalc == 'hpv' ? (100 + (((totalSGABudget - totalSGA) / totalSGA) * 100)) : (((totalSGABudget - totalSGA) / totalSGA) * 100))) : (((totalSGA - totalSGABudget) == 0 || totalSGABudget == 0) ? (this.misFlags.variancePerCalc == 'hmv' ? (100 - 0) : this.misFlags.variancePerCalc == 'hpv' ? (100 + 0) : 0) : (this.misFlags.variancePerCalc == 'hmv' ? (100 - (((totalSGA - totalSGABudget) / totalSGABudget) * 100)) : this.misFlags.variancePerCalc == 'hpv' ? (100 + (((totalSGA - totalSGABudget) / totalSGABudget) * 100)) : (((totalSGA - totalSGABudget) / totalSGABudget) * 100))),
      ["total" + this.misFlags.reportPbtTypeId]: totalPBT,
      ["total" + this.misFlags.reportPbtTypeId + "Budget"]: totalPBTBudget,
      ["total" + this.misFlags.reportPbtTypeId + "Variance"]: this.misFlags.varianceCalc == 'bma' ? totalPBTBudget - totalPBT : totalPBT - totalPBTBudget,
      ["total" + this.misFlags.reportPbtTypeId + "VarPerc"]: this.misFlags.varianceCalc == 'amb' ? (((totalPBTBudget - totalPBT) == 0 || totalPBT == 0) ? (this.misFlags.variancePerCalc == 'hmv' ? (100 - 0) : this.misFlags.variancePerCalc == 'hpv' ? (100 + 0) : 0) : (this.misFlags.variancePerCalc == 'hmv' ? (100 - (((totalPBTBudget - totalPBT) / totalPBT) * 100)) : this.misFlags.variancePerCalc == 'hpv' ? (100 + (((totalPBTBudget - totalPBT) / totalPBT) * 100)) : (((totalPBTBudget - totalPBT) / totalPBT) * 100))) : (((totalPBT - totalPBTBudget) == 0 || totalPBTBudget == 0) ? (this.misFlags.variancePerCalc == 'hmv' ? (100 - 0) : this.misFlags.variancePerCalc == 'hpv' ? (100 + 0) : 0) : (this.misFlags.variancePerCalc == 'hmv' ? (100 - (((totalPBT - totalPBTBudget) / totalPBTBudget) * 100)) : this.misFlags.variancePerCalc == 'hpv' ? (100 + (((totalPBT - totalPBTBudget) / totalPBTBudget) * 100)) : (((totalPBT - totalPBTBudget) / totalPBTBudget) * 100))),
      items: items,
      isLoading: false,
      order: order
    };

    if (isForOpportunityCard) {

      this.resolveOpportunityPlData(codeInterval, this.detailedModeBaseLegalEntity, 0, resolvedMisDataKey);

      let areAllItemsLastLevel = true, doesntHaveItems = true;

      for (let item of resolvedMisDataKey.items) {

        doesntHaveItems = false;

        this.resolveOpportunityPlData(codeInterval, item.legalEntity, 1, item);

        if (item.subItems.length > 0)
          areAllItemsLastLevel = false;

        for (let subItem of item.subItems)
          this.resolveOpportunityPlData(codeInterval, subItem.legalEntity, 2, subItem);

        if (this.baseLegalEntityLevel == 2 && (totalMisData.length == 0 || item.subItems.length > 0))
          this.appendOpportunityItems(codeInterval, item.legalEntity, item.subItems, true);

      }

      if (((this.baseLegalEntityLevel == 2 && (totalMisData.length != 0 || doesntHaveItems)) || this.baseLegalEntityLevel == 3) && areAllItemsLastLevel)
        this.appendOpportunityItems(codeInterval, this.detailedModeBaseLegalEntity, resolvedMisDataKey.items, false);

    }

    resolvedMisDataKey = this.jsonConcat(resolvedMisDataKey, totalKey);

    if (isForOpportunityCard) {

      this.resolveOpportunityPlPercData(0, resolvedMisDataKey);

      for (let item of resolvedMisDataKey.items) {

        this.resolveOpportunityPlPercData(1, item);

        for (let subItem of item.subItems)
          this.resolveOpportunityPlPercData(2, subItem);

      }

    }

    if (!isForDownload) {

      if (!isForOpportunityCard) {

        let itsDone = false;

        for (let i = 0; i < this.resolvedMISData.length && !itsDone; i++)
          if (!this.resolvedMISData[i].isForOpportunityExcess && this.resolvedMISData[i].intervalCode == codeInterval) {

            this.resolvedMISData.splice(i, 1);
            this.resolvedMISData.push(resolvedMisDataKey);
            itsDone = !itsDone;

          }

        this.resolvedMISData = this.resolvedMISData.sort((a, b) => {
          if (a.order < b.order)
            return -1;
          else if (a.order > b.order)
            return 1;
          else
            return 0;
        });

        for (let i = 0; i < this.resolvedMisDataForOpportunityExcessCards.length && !itsDone; i++)
          if (this.resolvedMisDataForOpportunityExcessCards[i].isForOpportunityExcess && this.resolvedMisDataForOpportunityExcessCards[i].intervalCode == codeInterval) {

            this.resolvedMisDataForOpportunityExcessCards.splice(i, 1);
            this.resolvedMisDataForOpportunityExcessCards.push(resolvedMisDataKey);
            itsDone = !itsDone;

          }


        let allSelectedMisCards = _.where(this.udrfService.udrfData.appliedConfig["customFields"]["misCards"], { isSelected: true });

        if (allSelectedMisCards.length > 0) {

          let lastSelectedMisCard = allSelectedMisCards[allSelectedMisCards.length - 1];

          if (codeInterval == lastSelectedMisCard.dataTypeCode)
            this.isMisFtyDataResolved = true;

        }

        else
          this.isMisFtyDataResolved = true;

        if (this.isMisFtyDataResolved && this.isBbDataResolved)
          this.resolveMISOpportunityData(null);

      }

      else {

        let itsDone = false;

        for (let i = 0; i < this.resolvedMISOpportunityData.length && !itsDone; i++)
          if (this.resolvedMISOpportunityData[i].intervalCode == codeInterval) {

            this.resolvedMISOpportunityData.splice(i, 1);
            this.resolvedMISOpportunityData.push(resolvedMisDataKey);
            itsDone = !itsDone;

          }

        this.resolvedMISOpportunityData = this.resolvedMISOpportunityData.sort((a, b) => {
          if (a.order < b.order)
            return -1;
          else if (a.order > b.order)
            return 1;
          else
            return 0;
        });

        if (!this.misOppChipFilterAppliedFirstTime) {

          let shouldApplyFilter = _.where(this.resolvedMISOpportunityData, { isLoading: true });

          if (shouldApplyFilter.length == 0) {

            for (let resolvedMISOpportunityDataItem of this.resolvedMISOpportunityData)
              for (let misOpportunityDefaultChip of this.misFlags.misOpportunityDefaultChips)
                if (!this.doesMisOpportunityFilterHave(misOpportunityDefaultChip, resolvedMISOpportunityDataItem.intervalCode))
                  this.changeMisOpportunityFilter(misOpportunityDefaultChip, resolvedMISOpportunityDataItem.intervalCode)

            this.misOppChipFilterAppliedFirstTime = true;

          }

        }

      }

    }

    else {

      resolvedMisDataKey["legalEntity"] = this.downloadLe;
      resolvedMisDataKey["legalEntityLevel"] = this.downloadLeLevel;
      resolvedMisDataKey["legalEntityLevelIteration"] = this.downloadLeLevel == 2 ? this.downloadLeLevel2Iteration : 0;

      this.downloadableMisData.push(resolvedMisDataKey);

      let noOfLesInCurrentLevel = _.where(this.legalEntities, { leLevel: this.downloadLeLevel }).length;

      if (this.currentDownloadLeCountOfLeLevel == noOfLesInCurrentLevel) {

        if (this.downloadLeLevel == 2)
          this.downloadLeLevel2Iteration++;

        if (this.downloadLeLevel != 2 || this.downloadLeLevel2Iteration == 2) {

          this.downloadLeLevel++;
          this.downloadLeLevel2Iteration = 0;

        }

        this.currentDownloadLeCountOfLeLevel = 1;

      }

      else
        this.currentDownloadLeCountOfLeLevel++;

      let maxLeLevel = _.max(this.legalEntities, function (o) { return o.leLevel; }).leLevel;

      if (this.downloadLeLevel <= maxLeLevel) {

        let currentLe = _.where(this.legalEntities, { leLevel: this.downloadLeLevel })[this.currentDownloadLeCountOfLeLevel - 1];

        this.downloadLe = currentLe.name;

        this.resolveMISFTYData(isForDownload);

      }

      else
        // temporary
        // this.callNextMisApi(isForDownload);
        this.downloadMisReportFn();

    }
    console.log(this.resolvedMISData);
  }

  appendOpportunityItems(codeInterval, legalEntity, array, isSubItem) {

    let currentBbDataType = _.where(this.bbDataTypes, { intervalCode: codeInterval, legalEntity: legalEntity });

    if (currentBbDataType.length > 0)
      for (let opportunityItem of currentBbDataType[0].opportunityItems) {

        let revenue = 0, cost = 0, gm = 0, pbt = 0;

        if (codeInterval == "LE_BB" || codeInterval == "UOB_BB") {

          revenue = parseFloat(opportunityItem.current_fy_uob_value ? opportunityItem.current_fy_uob_value : 0);

          gm = parseFloat(opportunityItem.future_fy_gm_inr_1_orig ? opportunityItem.future_fy_gm_inr_1_orig : 0);

        }

        else if (codeInterval == "UOB_NY_BB") {

          revenue = parseFloat(opportunityItem.current_fy_uob_value2 ? opportunityItem.current_fy_uob_value2 : 0);

          gm = parseFloat(opportunityItem.future_fy_gm_inr_2_orig ? opportunityItem.future_fy_gm_inr_2_orig : 0);

        }

        else if (codeInterval == "FTM_BB") {

          if (moment(moment(opportunityItem.closure_date).format(this.dateFormat)).add(opportunityItem.opp_closure_date_buffer_days).isSameOrBefore(moment().endOf('month').format(this.dateFormat))) {

            let noOfDaysRemainingInMonth = Math.abs(moment(moment(opportunityItem.closure_date).format(this.dateFormat)).diff(moment().endOf('month').format(this.dateFormat), 'd')) + 1;

            let noOfDaysFromClosureDateToEndOfFy = Math.abs(moment(moment(opportunityItem.closure_date).format(this.dateFormat)).diff(this.ftyEndDate, 'd')) + 1;

            let revenuePerDay = opportunityItem.current_fy_uob_value / noOfDaysFromClosureDateToEndOfFy;

            let gmPerDay = opportunityItem.future_fy_gm_inr_1_orig / noOfDaysFromClosureDateToEndOfFy;

            revenue += (noOfDaysRemainingInMonth * revenuePerDay);

            gm += (noOfDaysRemainingInMonth * gmPerDay);

          }

        }

        cost = revenue - gm;
        pbt = gm - 0;

        let opportunity_item = {
          isOpportunityItem: true,
          legalEntity: opportunityItem.opportunity_name,
          plId: opportunityItem.rollup_id,
          verticalId: opportunityItem.pl_id,
          projectItemName: "",
          projectId: 0,
          [this.misFlags.reportRevenueTypeId]: revenue,
          [this.misFlags.reportCostTypeId]: cost,
          [this.misFlags.reportGmTypeId]: gm,
          [this.misFlags.reportPbtTypeId]: pbt,
          ["mis" + this.misFlags.reportRevenueTypeId]: 0,
          ["mis" + this.misFlags.reportCostTypeId]: 0,
          ["mis" + this.misFlags.reportGmTypeId]: 0,
          ["mis" + this.misFlags.reportPbtTypeId]: 0,
          ["opportunity" + this.misFlags.reportRevenueTypeId]: revenue,
          ["opportunity" + this.misFlags.reportCostTypeId]: cost,
          ["opportunity" + this.misFlags.reportGmTypeId]: gm,
          ["opportunity" + this.misFlags.reportPbtTypeId]: pbt,
          subItems: [],
          isSubItem: isSubItem,
          isExpanded: false
        };

        for (let visibleMisCard of this.visibleMisCards)
          if (!_.contains([this.misFlags.reportRevenueTypeId, this.misFlags.reportCostTypeId, this.misFlags.reportGmTypeId,
          this.misFlags.reportPbtTypeId], parseInt(visibleMisCard.posting_type_id))) {

            opportunity_item[visibleMisCard.posting_type_id] = 0;
            opportunity_item["mis" + visibleMisCard.posting_type_id] = 0;
            opportunity_item["opportunity" + visibleMisCard.posting_type_id] = 0;

          }

        array.push(opportunity_item);

      }

  }

  resolveOpportunityPlData(codeInterval, legalEntity, legalEntityLevel, resolvedMisDataKey) {

    let currentBbDataType = _.where(this.bbDataTypes, { intervalCode: codeInterval, legalEntity: legalEntity });

    if (currentBbDataType.length > 0)
      for (let key of Object.keys(currentBbDataType[0]))
        if (key != "intervalCode" && key != "legalEntity" && key != "items") {

          if (resolvedMisDataKey[(legalEntityLevel == 0 ? "totalOpportunity" : "opportunity") + key] == null) {

            resolvedMisDataKey[(legalEntityLevel == 0 ? "totalOpportunity" : "opportunity") + key] = 0;
            resolvedMisDataKey[(legalEntityLevel == 0 ? "totalMis" : "mis") + key] = resolvedMisDataKey[(legalEntityLevel == 0 ? "total" : "") + key];

          }

          resolvedMisDataKey[(legalEntityLevel == 0 ? "total" : "") + key] += currentBbDataType[0][key];
          resolvedMisDataKey[(legalEntityLevel == 0 ? "totalOpportunity" : "opportunity") + key] += currentBbDataType[0][key];

        }

  }

  resolveOpportunityPlPercData(legalEntityLevel, resolvedMisDataKey) {

    for (let key of Object.keys(resolvedMisDataKey))
      if (/\d/.test(key) && !key.includes("Budget") && !key.includes("VarPerc") && !key.includes("Variance")) {

        let currentPostingTypeId = parseInt(key.match(/\d+/)[0]);

        let isCurrentPostingTypeFrontEndCalculated = _.contains(this.misParams.visibleFrontendPostingTypeIdsOtherThanDefault, currentPostingTypeId);

        if (isCurrentPostingTypeFrontEndCalculated) {

          let currentPostingType = _.where(this.visibleMisCards, { posting_type_id: currentPostingTypeId });

          if (currentPostingType && currentPostingType.length > 0 && currentPostingType[0].calculation) {

            let evalString = JSON.parse(currentPostingType[0].calculation);

            let dataString = "(" + evalString.calculation + ")";

            let shouldCalculate = false;

            for (let posting_type_data_item of evalString.posting_type_data) {

              dataString = dataString.replace(posting_type_data_item.replace_variable, resolvedMisDataKey[(legalEntityLevel == 0 ? ("total" + (key.includes("Mis") ? "Mis" : key.includes("Opportunity") ? "Opportunity" : "")) : ((key.includes("mis") ? "mis" : key.includes("opportunity") ? "opportunity" : ""))) + posting_type_data_item.posting_type_id]);

              if (_.contains([this.misFlags.reportRevenueTypeId, this.misFlags.reportCostTypeId, this.misFlags.reportGmTypeId,
              this.misFlags.reportPbtTypeId], parseInt(posting_type_data_item.posting_type_id)))
                shouldCalculate = true;

            }

            if (shouldCalculate)
              resolvedMisDataKey[(legalEntityLevel == 0 ? ("total" + (key.includes("Mis") ? "Mis" : key.includes("Opportunity") ? "Opportunity" : "")) : ((key.includes("mis") ? "mis" : key.includes("opportunity") ? "opportunity" : ""))) + currentPostingTypeId] = this.arithmeticEvalService.evaluate(dataString);

          }

        }

      }

  }

  callNextMisApi(isForDownload) {

    this.yearCount++;

    if (isForDownload) {

      this.downloadMisReportCommons();
      this.downloadMisReportFn();

    }

  }

  downloadLe = "";
  downloadLeLevel = 0;
  plIteration = 0;
  downloadLeLevel2Iteration = 0;
  currentDownloadLeCountOfLeLevel = 1;
  currentDownloadMonth = "";

  downloadMisReport() {

    this.isDownloadingMisReport = true;

    this.downloadableMisData = [];

    // temporary
    // this.yearCount = 0;

    this.yearCount = 1;

    this.downloadMisReportCommons();

    let isForDownload = true;

    this.retrieveMisFtyDataYearWise(isForDownload);

  }

  downloadMisReportCommons() {

    let leLevel0 = _.where(this.legalEntities, { leLevel: 0 });

    this.downloadLe = leLevel0 && leLevel0.length > 0 ? leLevel0[0].name : "";
    this.downloadLeLevel = 0;
    this.downloadLeLevel2Iteration = 0;
    this.plIteration = 0;
    this.currentDownloadLeCountOfLeLevel = 1;

    if (this.yearCount == 0)
      this.currentDownloadMonth = moment().subtract(1, 'year').format("YYYY-MM-27");

    else if (this.yearCount == 1)
      this.currentDownloadMonth = moment().format("YYYY-MM-27");

    else
      this.currentDownloadMonth = moment().add(1, 'year').format("YYYY-MM-27");

  }

  downloadMisReportFn() {

    let downloadableMisDataKey: any;

    let data = [];

    let postingTypeKey = {
      "P & L": "",
      "Sub P & L": "",
      "Vertical": "",
      "Project Name": "",
      "Item Name": "",
      "plId": 8888888,
      "subPlId": 8888888,
      "verticalId": 8888888,
      "projectId": 8888888,
      "itemId": 8888888,
      "isPostingTypeKey": true
    };

    let currencyKey = {
      "P & L": "",
      "Sub P & L": "",
      "Vertical": "",
      "Project Name": "",
      "Item Name": "",
      "plId": 9999999,
      "subPlId": 9999999,
      "verticalId": 9999999,
      "projectId": 9999999,
      "itemId": 9999999,
      "isCurrencyKey": true
    };

    let totalKey = {
      "P & L": "",
      "Sub P & L": "",
      "Vertical": "",
      "Project Name": "",
      "Item Name": ""
    };

    let merges = [{
      s: { r: 0, c: 0 },
      e: { r: 2, c: 0 }
    }, {
      s: { r: 0, c: 1 },
      e: { r: 2, c: 1 }
    }, {
      s: { r: 0, c: 2 },
      e: { r: 2, c: 2 }
    }, {
      s: { r: 0, c: 3 },
      e: { r: 2, c: 3 }
    }, {
      s: { r: 0, c: 4 },
      e: { r: 2, c: 4 }
    }];

    let postingTypeFields = [{
      key: "",
      value: "Actual"
    },
    {
      key: "Budget",
      value: "Budget"
    },
    {
      key: "Variance",
      value: "Variance"
    },
    {
      key: "VarPerc",
      value: "Var. Perc."
    }];

    let startColumn = 5, endColumn = 5;

    for (let visibleMisCard of this.visibleMisCards) {

      startColumn = endColumn;

      for (let postingTypeField of postingTypeFields) {

        currencyKey[visibleMisCard.description + (postingTypeField.key == '' ? '' : (" " + postingTypeField.key))] = this.udrfService.udrfData.appliedConfig["customFields"]["defaultCurrency"];
        postingTypeKey[visibleMisCard.description + (postingTypeField.key == '' ? '' : (" " + postingTypeField.key))] = postingTypeField.value;

      }

      endColumn += 3;

      merges.push({
        s: { r: 0, c: startColumn },
        e: { r: 0, c: endColumn }
      });

      merges.push({
        s: { r: 1, c: startColumn },
        e: { r: 1, c: endColumn++ }
      });

    }

    data.push(currencyKey);
    data.push(postingTypeKey);

    for (let downloadableMisDataItem of this.downloadableMisData) {

      if (downloadableMisDataItem.legalEntityLevel == 0)
        totalKey["P & L"] = downloadableMisDataItem.legalEntity + " Total";

      downloadableMisDataKey = downloadableMisDataItem;

      for (let downloadableMisDataKeyItem of Object.keys(downloadableMisDataKey))
        if (downloadableMisDataItem.legalEntityLevel == 0 && downloadableMisDataKeyItem.includes("total")) {

          let postingTypeId = parseInt(downloadableMisDataKeyItem.match(/\d+/)[0]);

          let postingTypeName = _.where(this.visibleMisCards, { posting_type_id: postingTypeId });

          if (postingTypeName && postingTypeName.length > 0) {

            postingTypeName = postingTypeName[0].description;

            let keyName = "";

            for (let postingTypeField of postingTypeFields)
              if (postingTypeField.key != '' && downloadableMisDataKeyItem.includes(postingTypeField.key))
                keyName = postingTypeName + " " + postingTypeField.key;
              else if (postingTypeField.key == '')
                keyName = postingTypeName;

            totalKey[keyName] = downloadableMisDataKey[downloadableMisDataKeyItem];

          }

        }

        else if (downloadableMisDataKeyItem == "items")
          for (let item of downloadableMisDataKey[downloadableMisDataKeyItem]) {

            let itemKey = {
              "P & L": "",
              "Sub P & L": "",
              "Vertical": "",
              "Project Name": "",
              "Item Name": "",
              "plId": "",
              "subPlId": "",
              "verticalId": "",
              "projectId": "",
              "itemId": ""
            };

            if (downloadableMisDataItem.legalEntityLevel == 0)
              itemKey["P & L"] = item.legalEntity;

            else if (downloadableMisDataItem.legalEntityLevel == 1)
              itemKey["Sub P & L"] = item.legalEntity;

            else if (downloadableMisDataItem.legalEntityLevel == 2 && downloadableMisDataItem.legalEntityLevelIteration == 0 && item.subItems && item.subItems.length > 0)
              itemKey["Vertical"] = item.legalEntity;

            else if (downloadableMisDataItem.legalEntityLevel == 2 && downloadableMisDataItem.legalEntityLevelIteration == 0 && item.subItems && item.subItems.length == 0)
              itemKey["Project Name"] = item.legalEntity;

            else if (downloadableMisDataItem.legalEntityLevel == 2 && downloadableMisDataItem.legalEntityLevelIteration == 1)
              itemKey["Item Name"] = item.legalEntity;

            else if (downloadableMisDataItem.legalEntityLevel == 3)
              itemKey["Project Name"] = item.legalEntity;

            let keyName = "";

            for (let itemKeyVal of Object.keys(item))
              if (/\d/.test(itemKeyVal)) {

                let postingTypeId = parseInt(itemKeyVal.match(/\d+/)[0]);

                let postingTypeName = _.where(this.visibleMisCards, { posting_type_id: postingTypeId });

                if (postingTypeName && postingTypeName.length > 0) {

                  postingTypeName = postingTypeName[0].description;

                  for (let postingTypeField of postingTypeFields)
                    if (postingTypeField.key != '' && itemKeyVal.includes(postingTypeField.key))
                      keyName = postingTypeName + " " + postingTypeField.key;
                    else if (postingTypeField.key == '')
                      keyName = postingTypeName;

                  itemKey[keyName] = item[itemKeyVal];

                }

              }

              else if (downloadableMisDataItem.legalEntityLevel > 0 && downloadableMisDataItem.legalEntityLevelIteration == 0 && itemKeyVal == "subItems")
                for (let subItem of item[itemKeyVal]) {

                  let subItemKey = {
                    "P & L": "",
                    "Sub P & L": "",
                    "Vertical": "",
                    "Project Name": "",
                    "Item Name": "",
                    "plId": "",
                    "subPlId": "",
                    "verticalId": "",
                    "projectId": "",
                    "itemId": ""
                  };

                  if (downloadableMisDataItem.legalEntityLevel == 1)
                    subItemKey["Vertical"] = subItem.legalEntity;

                  else
                    subItemKey["Item Name"] = subItem.legalEntity;

                  let keyName = "";

                  for (let subItemVal of Object.keys(subItem))
                    if (/\d/.test(subItemVal)) {

                      let postingTypeId = parseInt(subItemVal.match(/\d+/)[0]);

                      let postingTypeName = _.where(this.visibleMisCards, { posting_type_id: postingTypeId });

                      if (postingTypeName && postingTypeName.length > 0) {

                        postingTypeName = postingTypeName[0].description;

                        for (let postingTypeField of postingTypeFields)
                          if (postingTypeField.key != '' && subItemVal.includes(postingTypeField.key))
                            keyName = postingTypeName + " " + postingTypeField.key;
                          else if (postingTypeField.key == '')
                            keyName = postingTypeName;

                        subItemKey[keyName] = subItem[subItemVal];

                      }

                    }

                  if ((downloadableMisDataItem.legalEntityLevel == 1 && item.subItems.length == 1 && item.legalEntity == item.subItems[0].legalEntity) || downloadableMisDataItem.legalEntityLevel > 1)
                    data.push(subItemKey);

                }

            data.push(itemKey);

          }

    }

    let totalData = [];

    for (let misDataYearWiseItem of this.misDataYearWise)
      totalData = totalData.concat(misDataYearWiseItem.data);

    for (let dataItem of data) {

      if (dataItem["P & L"] != "") {

        let current = _.where(totalData, { pl: dataItem["P & L"] });

        if (current && current.length > 0)
          dataItem["plId"] = current[0].pl_id;

      }

      if (dataItem["Sub P & L"] != "") {

        let current = _.where(totalData, { sub_pl: dataItem["Sub P & L"] });

        if (current && current.length > 0) {

          dataItem["plId"] = current[0].pl_id;
          dataItem["subPlId"] = current[0].sub_pl_id;

        }

      }

      if (dataItem["Vertical"] != "") {

        let current = _.where(totalData, { vertical: dataItem["Vertical"] });

        if (current && current.length > 0) {

          dataItem["plId"] = current[0].pl_id;
          dataItem["subPlId"] = current[0].sub_pl_id;
          dataItem["verticalId"] = current[0].vertical_id;

        }

      }

      if (dataItem["Project Name"] != "") {

        let current = _.where(totalData, { project: dataItem["Project Name"] });

        if (current && current.length > 0) {

          dataItem["plId"] = current[0].pl_id;
          dataItem["subPlId"] = current[0].sub_pl_id;
          dataItem["verticalId"] = current[0].vertical_id;
          dataItem["projectId"] = current[0].project_id;

        }

      }

      if (dataItem["Item Name"] != "") {

        let current = _.where(totalData, { item: dataItem["Item Name"] });

        if (current && current.length > 0) {

          dataItem["plId"] = current[0].pl_id;
          dataItem["subPlId"] = current[0].sub_pl_id;
          dataItem["verticalId"] = current[0].vertical_id;
          dataItem["projectId"] = current[0].project_id;
          dataItem["itemId"] = current[0].item_id;

        }

      }

    }

    data = _.chain(data).sortBy('itemId').sortBy('projectId').sortBy('verticalId').sortBy('subPlId').sortBy('plId').reverse().value();

    let finalData = [];

    let indexArray = [{
      id: 0,
      startRow: 0,
      endRow: 0,
      field: 'plId',
      fieldName: 'P & L',
      columnNo: 0,
      iteration: 3
    }, {
      id: 0,
      startRow: 0,
      endRow: 0,
      field: 'subPlId',
      fieldName: 'Sub P & L',
      columnNo: 1,
      iteration: 3
    }, {
      id: 0,
      startRow: 0,
      endRow: 0,
      field: 'verticalId',
      fieldName: 'Vertical',
      columnNo: 2,
      iteration: 3
    }, {
      id: 0,
      startRow: 0,
      endRow: 0,
      field: 'projectId',
      fieldName: 'Project Name',
      columnNo: 3,
      iteration: 3
    }];

    let itemIdList = [], tempData = [];

    for (let dataItem of data) {

      let shouldEnter = true;

      if (dataItem["itemId"] && dataItem["itemId"] != '') {

        let doesItemIdExist = _.contains(itemIdList, dataItem["itemId"]);

        if (!doesItemIdExist)
          itemIdList.push(dataItem["itemId"]);

        else
          shouldEnter = false;

      }

      if (shouldEnter)
        tempData.push(dataItem);

    }

    data = tempData;

    for (let j = 0; j < data.length; j++)
      if (!(!data[j].plId || data[j].plId == '') || data[j].isPostingTypeKey || data[j].isCurrencyKey) {

        if (!(data[j].isPostingTypeKey || data[j].isCurrencyKey))
          for (let i = 0; i < indexArray.length; i++)
            if (!(!data[j][indexArray[i].field] || data[j][indexArray[i].field] == '')) {

              if (indexArray[i].id != data[j][indexArray[i].field]) {

                if (indexArray[i].id != 0) {

                  indexArray[i].id = 0;

                  data[indexArray[i].startRow][indexArray[i].fieldName] = data[indexArray[i].endRow][indexArray[i].fieldName];

                  merges.push({
                    s: { r: indexArray[i].startRow, c: indexArray[i].columnNo },
                    e: { r: indexArray[i].endRow, c: indexArray[i].columnNo }
                  });

                  indexArray[i].id = data[j][indexArray[i].field];
                  indexArray[i].startRow = indexArray[i].iteration;

                }

                else {

                  indexArray[i].id = data[j][indexArray[i].field];
                  indexArray[i].startRow = indexArray[i].iteration;

                }

              }

              else
                indexArray[i].endRow = indexArray[i].iteration - 1;

              indexArray[i].iteration++;

            }

            else
              indexArray[i].iteration++;

        delete data[j]["itemId"];
        delete data[j]["projectId"];
        delete data[j]["verticalId"];
        delete data[j]["subPlId"];
        delete data[j]["plId"];

        if (data[j].isPostingTypeKey)
          delete data[j]["isPostingTypeKey"];

        if (data[j].isCurrencyKey)
          delete data[j]["isCurrencyKey"];

        finalData.push(data[j]);

      }

    for (let indexArrayItem of indexArray) {

      data[indexArrayItem.startRow][indexArrayItem.fieldName] = data[indexArrayItem.endRow][indexArrayItem.fieldName];

      merges.push({
        s: { r: indexArrayItem.startRow, c: indexArrayItem.columnNo },
        e: { r: indexArrayItem.endRow, c: indexArrayItem.columnNo }
      });

    }

    let idEncounter = [];

    for (let i = 0; i < finalData.length; i++) {

      if (finalData[i]["P & L"] != "") {

        let doesAlreadyExist = _.where(idEncounter, { type: "P & L", value: finalData[i]["P & L"] });

        if (doesAlreadyExist && doesAlreadyExist.length > 0) {

          if (finalData[i + 1])
            finalData[i + 1]["P & L"] = finalData[i]["P & L"] + " Total";

          finalData[i]["P & L"] = finalData[i]["P & L"] + " Total";

          merges.push({
            s: { r: i + 1, c: 0 },
            e: { r: i + 1, c: 4 }
          });

        }

        else {

          finalData[i - 1]["P & L"] = finalData[i]["P & L"];

          idEncounter.push({
            type: "P & L",
            value: finalData[i]["P & L"]
          });

          finalData[i]["P & L"] = "";

        }

      }

      if (finalData[i]["Sub P & L"] != "") {

        let doesAlreadyExist = _.where(idEncounter, { type: "Sub P & L", value: finalData[i]["Sub P & L"] });

        if (doesAlreadyExist && doesAlreadyExist.length > 0) {

          if (finalData[i + 1])
            finalData[i + 1]["Sub P & L"] = finalData[i]["Sub P & L"] + " Total";

          finalData[i]["Sub P & L"] = "";

          merges.push({
            s: { r: i + 1, c: 1 },
            e: { r: i + 1, c: 4 }
          });

        }

        else {

          finalData[i - 1]["Sub P & L"] = finalData[i]["Sub P & L"];

          idEncounter.push({
            type: "Sub P & L",
            value: finalData[i]["Sub P & L"]
          });

          finalData[i]["Sub P & L"] = "";

        }

      }

      if (finalData[i]["Vertical"] != "") {

        let doesAlreadyExist = _.where(idEncounter, { type: "Vertical", value: finalData[i]["Vertical"] });

        if (doesAlreadyExist && doesAlreadyExist.length > 0) {

          if (finalData[i + 1])
            finalData[i + 1]["Vertical"] = finalData[i]["Vertical"] + " Total";

          finalData[i]["Vertical"] = "";

          merges.push({
            s: { r: i + 1, c: 2 },
            e: { r: i + 1, c: 4 }
          });

        }

        else {

          finalData[i - 1]["Vertical"] = finalData[i]["Vertical"];

          idEncounter.push({
            type: "Vertical",
            value: finalData[i]["Vertical"]
          });

          finalData[i]["Vertical"] = "";

        }

      }

      if (finalData[i]["Project Name"] != "" && !finalData[i]["Project Name"].includes(' Total')) {

        let finalDataCount = _.where(finalData, { "Project Name": finalData[i]["Project Name"] });

        let doesAlreadyExist = _.where(idEncounter, { type: "Project Name", value: finalData[i]["Project Name"] });

        if (doesAlreadyExist && doesAlreadyExist.length > 0) {

          if (finalData[i + 1])
            finalData[i + 1]["Project Name"] = finalData[i]["Project Name"] + " Total";

          finalData[i]["Project Name"] = "";

          merges.push({
            s: { r: i + 1, c: 3 },
            e: { r: i + 1, c: 4 }
          });

        }

        else {

          idEncounter.push({
            type: "Project Name",
            value: finalData[i]["Project Name"]
          });

          finalData[i - 1]["Project Name"] = finalData[i]["Project Name"];

          if (finalDataCount.length > 1)
            finalData[i]["Project Name"] = "";

          else {

            finalData[i]["Project Name"] = finalData[i]["Project Name"] + " Total";

            if (finalDataCount.length == 1)
              merges.push({
                s: { r: i + 1, c: 3 },
                e: { r: i + 1, c: 4 }
              });

          }

        }

      }

    }

    finalData.push(totalKey);

    merges.push({
      s: { r: finalData.length, c: 0 },
      e: { r: finalData.length, c: 4 }
    });

    let fileName = "NGR MIS Report";

    this.jsonToExcelService.exportAsExcelFileWithCellMerge(finalData, fileName, merges);

    this.isDownloadingMisReport = false;

  }

  jsonConcat(o1, o2) {

    for (var key in o2)
      o1[key] = o2[key];

    return o1;

  }

  retrieveCBOData() {

    if (this.reportMonthInterval == "This Month") {

      this.isCBOFTMDataRetrieved = false
      if (!this.isCBOFTMDataRetrieved) {

        this.isCBOFTMDataRetrieved = true;

        this.retrieveCBODataAPI(this.activeFtmStartDate, this.activeFtmEndDate).then((data: any) => {

          if (data && data.length && data.length > 0)
            this.cboFTMData = data;

          else
            this.cboFTMData = [];

          this.resolveCBOData();

        });

      }

      else
        this.resolveCBOData();

    }

    else if (this.reportMonthInterval == "Custom Month") {

      this.retrieveCBODataAPI(this.activeFtmStartDate, this.activeFtmEndDate).then((data: any) => {

        if (data && data.length && data.length > 0)
          this.cboActiveFTMData = data;

        else
          this.cboActiveFTMData = [];

        this.resolveCBOData();

      });

    }

  }

  resolveCBOData() {

    this.sourceCBOData = [];

    this.resolvedCBOData = [];

    if (this.reportMonthInterval == "This Month")
      this.sourceCBOData = this.cboFTMData;

    else if (this.reportMonthInterval == "Custom Month")
      this.sourceCBOData = this.cboActiveFTMData;

    let cboUserRolePlIds: any = [];

    let cboUserRolePlIdsData = _.where(this.userRolePlIds, { object_id: 168 });

    if (cboUserRolePlIdsData && cboUserRolePlIdsData.length > 0)
      cboUserRolePlIds = cboUserRolePlIdsData[0].pl_ids;

    if (cboUserRolePlIds != "ALL")
      if (cboUserRolePlIds.length > 0)
        this.sourceCBOData = _.filter(this.sourceCBOData, function (sourceCBODataItem) {

          if ((sourceCBODataItem.pl_id && _.contains(cboUserRolePlIds, sourceCBODataItem.pl_id)) ||
            (sourceCBODataItem.sub_pl_id && _.contains(cboUserRolePlIds, sourceCBODataItem.sub_pl_id)) ||
            (sourceCBODataItem.vertical_id && _.contains(cboUserRolePlIds, sourceCBODataItem.vertical_id)))
            return sourceCBODataItem;

        }, this);
      else
        this.sourceCBOData = [];

    // Ensuring that status is only true, subType is only Actual or Planned-Month

    this.sourceCBOData = _.filter(this.sourceCBOData, function (sourceCBODataItem) {

      if ((sourceCBODataItem.status == true && (sourceCBODataItem.subType == this.actualSubType ||
        sourceCBODataItem.subType == this.plannedMonthSubType)) || ((sourceCBODataItem.status == false) && (sourceCBODataItem.subType == "Planned-Month")))
        return sourceCBODataItem;

    }, this);

    for (let udrfFilterConfigItem of this.udrfFilterConfig)
      if (udrfFilterConfigItem.cboFilterIdBased && udrfFilterConfigItem.cboFilterIdBased == "Y" && udrfFilterConfigItem.filterIds.length > 0)
        this.sourceCBOData = _.filter(this.sourceCBOData, function (sourceMISDataItem) {

          if (sourceMISDataItem[udrfFilterConfigItem.cboFilterField] && _.contains(udrfFilterConfigItem.filterIds, sourceMISDataItem[udrfFilterConfigItem.cboFilterField]))
            return sourceMISDataItem;

        }, this);

      else if (udrfFilterConfigItem.cboFilterIdBased && udrfFilterConfigItem.cboFilterIdBased == "N" && udrfFilterConfigItem.filterNames.length > 0)
        this.sourceCBOData = _.filter(this.sourceCBOData, function (sourceMISDataItem) {

          if (sourceMISDataItem[udrfFilterConfigItem.cboFilterField] && _.contains(udrfFilterConfigItem.filterNames, sourceMISDataItem[udrfFilterConfigItem.cboFilterField]))
            return sourceMISDataItem;

        }, this);

      else if (udrfFilterConfigItem.cboFilterIdBased && udrfFilterConfigItem.cboFilterIdBased == "K" && udrfFilterConfigItem.filterKeyValues.length > 0)
        this.sourceCBOData = _.filter(this.sourceCBOData, function (sourceMISDataItem) {

          if (sourceMISDataItem[udrfFilterConfigItem.cboFilterField] && _.contains(udrfFilterConfigItem.filterKeyValues, sourceMISDataItem[udrfFilterConfigItem.cboFilterField]))
            return sourceMISDataItem;

        }, this);

    this.totalPoCAvg = 0;

    this.totalPoCPlanned = 0;

    this.totalPoCActual = 0;

    this.totalPoCValue = 0;

    this.baseLegalEntityLevel = _.filter(this.legalEntities, function (legalEntitiesValue) {

      if ((this.reportMode == "Normal Mode" && this.normalModeCBOWBaseLegalEntity == legalEntitiesValue.name) ||
        (this.reportMode == "Detailed Mode" && this.detailedModeBaseLegalEntity == legalEntitiesValue.name))
        return legalEntitiesValue;

    }, this)[0].leLevel;

    this.baseLegalEntityLevelCbo = this.baseLegalEntityLevel;
    this.baseLegalEntityLevelCboCount = 1;
    this.normalModeCBOWBaseLegalEntityCbo = this.normalModeCBOWBaseLegalEntity;
    this.detailedModeBaseLegalEntityCbo = this.detailedModeBaseLegalEntity;
    this.resolvedCboLowerLeLevelData = [];

    this.otherGroupBysourceCBOData = this.sourceCBOData

    this.recursivelyCallCboFns();

  }

  baseLegalEntityLevelCbo = 0;
  baseLegalEntityLevelCboCount = 1;
  normalModeCBOWBaseLegalEntityCbo = "";
  detailedModeBaseLegalEntityCbo = "";

  async recursivelyCallCboFns() {

    let isExpandedOverriddenData = _.filter(this.legalEntities, function (legalEntityItem) {

      if (((this.reportMode == "Normal Mode" && this.normalModeCBOWBaseLegalEntityCbo == legalEntityItem.name) || (this.reportMode ==
        "Detailed Mode" && this.detailedModeBaseLegalEntityCbo == legalEntityItem.name)) && legalEntityItem.isExpandedOverridden)
        return legalEntityItem;

    }, this);

    if ((this.reportMode == "Normal Mode" && this.normalModeCBOWBaseLegalEntityCbo == this.normalModeCBOWBaseLegalEntity) ||
      (this.reportMode == "Detailed Mode" && this.detailedModeBaseLegalEntityCbo == this.detailedModeBaseLegalEntity)) {

      this.normalModeCBOWBaseLegalEntityExpandOverridden = isExpandedOverriddenData.length > 0 ? true : false;
      this.detailedModeBaseLegalEntityExpandOverridden = isExpandedOverriddenData.length > 0 ? true : false;

    }

    // let cboTypes = ["Collection", "Billing", "OBV"];
    let cboTypes = _.pluck(this.cbowViewCards, "dataType")

    for (let cboType of cboTypes) {
      if (this.selectedViewGroupMode == 'Region') {
        this.resolveCBODataType(cboType);
      }
      else if (this.selectedViewGroupMode != 'Region') {
        this.otherViewGroupByData[cboType] = await this.retrieveCBODataForOtherViewGroupType(cboType)

        console.log("Other View Type Data  for Type : ", cboType, " => ", this.otherViewGroupByData)
      }
    }

  }

  resolveCBODataType(type) {

    this.identifyCboFilters(type);

    let totalActual, totalPlanned, totalYetTo, totalYetToBracketed, yetToText, overallPoC;

    let rollupActual, rollupPlanned, rollupYetTo, rollupYetToBracketed, rollupPoC;

    let actual, planned, yetTo, yetToBracketed, poC;

    let items = [], subItems = [];

    let plId = 0, verticalId = 0, projectItemName = "", projectId = 0, projectName = "", itemId = 0, itemName = "", status = true, opportunityId = 0, itemType = "", colorCodeScheme = null, history = [];

    // let itemValue, itemDate, itemMovedToDate, subType;

    totalActual = 0, totalPlanned = 0;

    let sourceCBOTypeData = _.where(this.sourceCBOData, { type: type });

    let mainLegalEntities = _.uniq(_.pluck(sourceCBOTypeData, this.cboFirstFilter));

    for (let legalEntity of mainLegalEntities) {

      let rollupData = _.where(sourceCBOTypeData, { [this.cboFirstFilter]: legalEntity });

      let mainDisplayDates = [], mainDisplayMovedToDates = [];

      let shortMainDisplayDate, shortMainDisplayMovedToDate, mainDisplayDate, mainDisplayMovedToDate;

      plId = rollupData[0].pl_id, verticalId = 0, projectItemName = "", projectId = 0, projectName = "", itemId = 0, itemName = "", status=true, opportunityId = 0, itemType = "", colorCodeScheme = null, history = [];

      let shouldLegalEntityBeThere = false;

      for (let currentlegalEntity of this.legalEntities)
        if (currentlegalEntity.name == legalEntity && ((this.reportMode == "Normal Mode" &&
          this.normalModeCBOWBaseLegalEntityCbo == currentlegalEntity.rollup) || (this.reportMode == "Detailed Mode" &&
            this.detailedModeBaseLegalEntityCbo == currentlegalEntity.rollup)))
          shouldLegalEntityBeThere = true;
        else if (this.baseLegalEntityLevelCbo == 2) {

          let legalEntityData = _.where(sourceCBOTypeData, {
            sub_pl: this.reportMode == "Normal Mode" ?
              this.normalModeCBOWBaseLegalEntityCbo : this.detailedModeBaseLegalEntityCbo, [this.cboFirstFilter]: legalEntity
          });

          if (legalEntityData.length > 0)
            shouldLegalEntityBeThere = true;

        }
        else if (this.baseLegalEntityLevelCbo == 3) {

          let legalEntityData = _.where(sourceCBOTypeData, {
            vertical: this.reportMode == "Normal Mode" ?
              this.normalModeCBOWBaseLegalEntityCbo : this.detailedModeBaseLegalEntityCbo, [this.cboFirstFilter]: legalEntity
          });

          if (legalEntityData.length > 0)
            shouldLegalEntityBeThere = true;

        }

      if (shouldLegalEntityBeThere) {

        subItems = [];

        // if (this.baseLegalEntityLevel != 2) {

        rollupActual = 0, rollupPlanned = 0;

        let rollupLegalEntities = _.uniq(_.pluck(rollupData, this.cboSecondFilter));

        for (let rollupLegalEntity of rollupLegalEntities) {

          let rollupSubData = _.where(rollupData, { [this.cboSecondFilter]: rollupLegalEntity });

          if (this.baseLegalEntityLevelCbo == 0) {

            let rollupSubLegalEntities = _.uniq(_.pluck(rollupSubData, "vertical"));

            for (let rollupSubLegalEntity of rollupSubLegalEntities) {

              let rollupSubDataTemp = _.where(rollupSubData, { vertical: rollupSubLegalEntity });

              let rollupFilterDataTemp = _.where(rollupSubDataTemp, { type: type });

              let actualDataTemp = _.where(rollupFilterDataTemp, { subType: this.actualSubType });

              let plannedDataTemp = _.where(rollupFilterDataTemp, { subType: this.plannedMonthSubType });

              let actualTemp = actualDataTemp.reduce((s, f) => {
                return s + f.value;
              }, 0.0);

              let plannedTemp = plannedDataTemp.reduce((s, f) => {
                return s + f.value;
              }, 0.0);

              let yetToData = this.computeYetToData(plannedTemp, actualTemp, rollupFilterDataTemp, type, true);

              let yetToTemp = yetToData.yetTo;

              let poCTemp = plannedTemp == 0 ? 0 : Math.round((actualTemp / plannedTemp) * 100).toFixed(0);

              this.setLegalEntityCBO(rollupSubLegalEntity, type, yetToTemp, poCTemp);

            }

          }

          let displayDates = [], displayMovedToDates = [];

          let shortDisplayDate, shortDisplayMovedToDate, displayDate, displayMovedToDate;

          verticalId = rollupSubData[0].sub_pl_id, projectItemName = "", projectId = rollupSubData[0].projectId, projectName = rollupSubData[0].projectName, itemId = rollupSubData[0].itemId, itemName = rollupSubData[0].itemName, status = rollupSubData[0].status, itemType = rollupSubData[0].type, opportunityId = rollupSubData[0].opportunityId, colorCodeScheme = rollupSubData[0].color_scheme, history = rollupSubData[0].history;

          if (this.baseLegalEntityLevelCbo == 2)
            projectItemName = rollupLegalEntity;

          let rollupFilterData = _.where(rollupSubData, { type: type });

          let actualData = _.where(rollupFilterData, { subType: this.actualSubType });

          let plannedData = _.where(rollupFilterData, { subType: this.plannedMonthSubType });


          actual = actualData.reduce((s, f) => {
            return s + f.value;
          }, 0.0);

          planned = plannedData.reduce((s, f) => {
            return s + f.value;
          }, 0.0);

          let yetToData = this.computeYetToData(planned, actual, rollupFilterData, type, true);

          yetTo = yetToData.yetTo;

          yetToBracketed = yetToData.yetToBracketed;

          poC = planned == 0 ? 0 : Math.round((actual / planned) * 100).toFixed(0);

          let legalEntityName = rollupLegalEntity;

          if (type == 'OBV' && this.cboFirstFilter != 'customerName' && this.cboSecondFilter == 'projectName')
            legalEntityName = `${rollupSubData[0]['customerName']} - ${rollupLegalEntity} ${ rollupSubData[0].opportunityId ? " (ID : " + rollupSubData[0].opportunityId + ")" : ""}`;

          // legalEntityName = `${rollupSubData[0]['customerName']} - ${rollupLegalEntity}`

          let subItemData = {
            legalEntity: legalEntityName,
            plId: plId,
            verticalId: verticalId,
            projectItemName: projectItemName,
            projectId: projectId,
            projectName: projectName,
            itemId: itemId,
            itemName: itemName,
            opportunityId: opportunityId,
            type: itemType,
            actualReal: actual,
            plannedReal: planned,
            status: status,
            yetToReal: yetTo,
            actual: this.getValueWithComma(actual),
            planned: this.getValueWithComma(planned),
            yetTo: this.getValueWithComma(yetTo),
            yetToBracketed: yetToBracketed,
            poC: poC,
            actualOriginal: this.getValueTooltipWithComma(actual),
            plannedOriginal: this.getValueTooltipWithComma(planned),
            yetToOriginal: this.getValueTooltipWithComma(yetTo),
            ar: type == "Collection" ? this.getARAmountForCollection(rollupLegalEntity, "Normal") : 0,
            arOriginal: type == "Collection" ? this.getARAmountForCollection(rollupLegalEntity, "Original") : 0,
            isSubItem: true,
            colorScheme: colorCodeScheme || null,
            history: history || [],
            pl_id: rollupSubData[0]['pl_id'],
            pl_name: rollupSubData[0]['pl'],
            sub_pl_id: rollupSubData[0]['sub_pl_id'],
            sub_pl_name: rollupSubData[0]['sub_pl'],
            vertical_id: rollupSubData[0]['vertical_id'],
            vertical_name: rollupSubData[0]['vertical']
          };

          if (this.baseLegalEntityLevelCbo == 2 || this.baseLegalEntityLevelCbo == 3) {

            subItemData["projectItemNameOriginal"] = "";

            subItemData["projectItemNameOriginalExists"] = true;

          }

          if (this.baseLegalEntityLevelCbo == 3) {

            for (let rollupFilterDataItem of rollupFilterData) {

              displayDates.push(rollupFilterDataItem.date);

              displayMovedToDates.push(this.getMilestoneMovedToDateFromHistory(rollupFilterDataItem, type));

            }

            shortDisplayDate = this.getMaxDate(displayDates, this.dfDDMMM);

            shortDisplayMovedToDate = this.getMaxDate(displayMovedToDates, this.dfDDMMM);

            displayDate = this.getMaxDate(displayDates, this.dfDDMMMYYYY);

            displayMovedToDate = this.getMaxDate(displayMovedToDates, this.dfDDMMMYYYY);

            subItemData["shortDisplayDate"] = shortDisplayDate;

            subItemData["shortDisplayMovedToDate"] = shortDisplayMovedToDate;

            subItemData["displayDate"] = displayDate;

            subItemData["displayMovedToDate"] = displayMovedToDate;

            mainDisplayDates.push(displayDate);

            mainDisplayMovedToDates.push(displayMovedToDate);

          }

          subItems.push(subItemData);

          if (this.baseLegalEntityLevelCbo == 0)
            this.setLegalEntityCBO(rollupLegalEntity, type, yetTo, poC);

          rollupActual += parseFloat(actual);

          rollupPlanned += parseFloat(planned);

        }

        rollupActual = parseFloat(rollupActual);

        rollupPlanned = parseFloat(rollupPlanned);

        // }

        // else {

        if (this.baseLegalEntityLevelCbo == 2 || this.baseLegalEntityLevelCbo == 3) {

          let rollupData = _.where(sourceCBOTypeData, { [this.cboFirstFilter]: legalEntity });

          let rollupActualData = _.where(rollupData, { subType: this.actualSubType });

          let rollupPlannedData = _.where(rollupData, { subType: this.plannedMonthSubType });

          rollupActual = rollupActualData.reduce((s, f) => {
            return s + f.value;
          }, 0.0);

          rollupPlanned = rollupPlannedData.reduce((s, f) => {
            return s + f.value;
          }, 0.0);

        }

        rollupYetTo = (rollupPlanned - rollupActual >= 0 ? rollupPlanned - rollupActual : 0);

        let rollupYetToData = this.computeYetToData(rollupPlanned, rollupActual, rollupData, type, true);

        rollupYetTo = rollupYetToData.yetTo;

        rollupYetToBracketed = rollupYetToData.yetToBracketed;

        rollupPoC = rollupPlanned == 0 ? 0 : Math.round((rollupActual / rollupPlanned) * 100).toFixed(0);

        totalActual += parseFloat(rollupActual);

        totalPlanned += parseFloat(rollupPlanned);

        if (this.baseLegalEntityLevelCbo > 1) {

          verticalId = rollupData[0].sub_pl_id;
          projectId = rollupData[0].projectId;

        }

        if (this.baseLegalEntityLevelCbo == 3)
          projectItemName = legalEntity;

        if (this.misFlags.shouldSortByLegalEntityByDefault)
          subItems = subItems.sort((a, b) => {
            if (a.legalEntity < b.legalEntity)
              return -1;
            else if (a.legalEntity > b.legalEntity)
              return 1;
            else
              return 0;
          });

        let itemData = {
          legalEntity: legalEntity,
          plId: plId,
          verticalId: verticalId,
          projectItemName: projectItemName,
          projectId: projectId,
          projectName: projectName,
          itemId: itemId,
          itemName: itemName,
          opportunityId: opportunityId,
          type: itemType,
          status: status,
          actualReal: rollupActual,
          plannedReal: rollupPlanned,
          yetToReal: rollupYetTo,
          actual: this.getValueWithComma(rollupActual),
          planned: this.getValueWithComma(rollupPlanned),
          yetTo: this.getValueWithComma(rollupYetTo),
          yetToBracketed: rollupYetToBracketed,
          poC: rollupPoC,
          actualOriginal: this.getValueTooltipWithComma(rollupActual),
          plannedOriginal: this.getValueTooltipWithComma(rollupPlanned),
          yetToOriginal: this.getValueTooltipWithComma(rollupYetTo),
          ar: type == "Collection" ? this.getARAmountForCollection(legalEntity, "Normal") : 0,
          arOriginal: type == "Collection" ? this.getARAmountForCollection(legalEntity, "Original") : 0,
          subItems: subItems,
          isExpanded: false,
          colorScheme: colorCodeScheme || null,
          history : history || null,
          pl_id: rollupData[0]['pl_id'],
          pl_name: rollupData[0]['pl'],
          sub_pl_id: rollupData[0]['sub_pl_id'],
          sub_pl_name: rollupData[0]['sub_pl'],
          vertical_id: rollupData[0]['vertical_id'],
          vertical_name: rollupData[0]['vertical']
        };

        // if (type == "OBV" && this.reportMode == "Detailed Mode") {

        if (this.baseLegalEntityLevelCbo == 3) {

          // itemData["itemValue"] = this.roundToTwo(itemValue);

          // itemData["itemValueOriginal"] = this.convertToCrOrMn(itemValue);

          // itemData["itemDate"] = itemDate;

          // itemData["itemMovedToDate"] = itemMovedToDate;

          // itemData["subType"] = subType;

          itemData["projectItemNameOriginal"] = "";

          itemData["projectItemNameOriginalExists"] = true;


          shortMainDisplayDate = this.getMaxDate(mainDisplayDates, this.dfDDMMM);

          shortMainDisplayMovedToDate = this.getMaxDate(mainDisplayMovedToDates, this.dfDDMMM);

          mainDisplayDate = this.getMaxDate(mainDisplayDates, this.dfDDMMMYYYY);

          mainDisplayMovedToDate = this.getMaxDate(mainDisplayMovedToDates, this.dfDDMMMYYYY);

          itemData["shortDisplayDate"] = shortMainDisplayDate;

          itemData["shortDisplayMovedToDate"] = shortMainDisplayMovedToDate;

          itemData["displayDate"] = mainDisplayDate;

          itemData["displayMovedToDate"] = mainDisplayMovedToDate;

        }

        // }

        // if (type == "OBV" && this.baseLegalEntityLevel == 2) {

        //   itemData["projectItemNameOriginal"] = "";

        //   itemData["projectItemNameOriginalExists"] = true;

        // }

        items.push(itemData);

        if (this.baseLegalEntityLevelCbo == 0)
          this.setLegalEntityCBO(legalEntity, type, rollupYetTo, rollupPoC);

      }

    }

    let totalYetToData = this.computeYetToData(totalPlanned, totalActual, [], type, false);

    totalYetTo = totalYetToData.yetTo;

    totalYetToBracketed = totalYetToData.yetToBracketed;

    overallPoC = totalPlanned == 0 ? 0 : Math.round((totalActual / totalPlanned) * 100).toFixed(0);

    if (this.baseLegalEntityLevelCbo == this.baseLegalEntityLevel && ((this.reportMode == "Normal Mode" &&
      this.normalModeCBOWBaseLegalEntityCbo == this.normalModeCBOWBaseLegalEntity) || (this.reportMode == "Detailed Mode" &&
        this.detailedModeBaseLegalEntityCbo == this.detailedModeBaseLegalEntity))) {

      this.totalPoCAvg += parseInt(overallPoC);

      this.totalPoCPlanned += totalPlanned;

      this.totalPoCActual += totalActual;

      if (type == "OBV") {

        this.totalPoCAvg = Math.round(this.totalPoCAvg / 3).toFixed(0);

        this.totalPoCValue = this.totalPoCPlanned == 0 ? 0 : Math.round((this.totalPoCActual / this.totalPoCPlanned) * 100).toFixed(0);

      }

    }

    let cbowViewCardsData = _.where(this.cbowViewCards, { dataType: type });

    yetToText = cbowViewCardsData[0].yetToText;

    if ((this.reportMode == "Normal Mode" && this.normalModeCBOWBaseLegalEntityCbo == this.normalModeCBOWBaseLegalEntity) ||
      (this.reportMode == "Detailed Mode" && this.detailedModeBaseLegalEntityCbo == this.detailedModeBaseLegalEntity)) {

      cbowViewCardsData[0].actualValue = this.getValueWithComma(totalActual);

      cbowViewCardsData[0].actualValueOriginal = this.getValueTooltipWithComma(totalActual);

      cbowViewCardsData[0].plannedValue = this.getValueWithComma(totalPlanned);

      cbowViewCardsData[0].plannedValueOriginal = this.getValueTooltipWithComma(totalPlanned);

      cbowViewCardsData[0].yetToValue = this.getValueWithComma(totalYetTo);

      cbowViewCardsData[0].yetToValueOriginal = this.getValueTooltipWithComma(totalYetTo);

    }

    if (this.baseLegalEntityLevelCbo == 1) {

      for (let i = 0; i < items.length; i++)
        if (items[i].subItems.length == 1 && items[i].legalEntity == items[i].subItems[0].legalEntity) {

          items[i].subItems[0]["isExpanded"] = false;
          items[i].subItems[0]["subItems"] = [];
          items[i].subItems[0].isSubItem = false;

          items[i] = items[i].subItems[0];

        }

    }

    else if (this.baseLegalEntityLevelCbo == 2 && items.length == 1)
      if (items[0].legalEntity == (this.reportMode == "Normal Mode" ? this.normalModeCBOWBaseLegalEntityCbo : this.detailedModeBaseLegalEntityCbo)) {

        let newItems = [];

        for (let subItem of items[0].subItems) {

          subItem["isExpanded"] = false;
          subItem["subItems"] = [];
          subItem.isSubItem = false;

          newItems.push(subItem);

        }

        items = newItems;

      }

    if (this.misFlags.shouldSortByLegalEntityByDefault)
      items = items.sort((a, b) => {
        if (a.legalEntity < b.legalEntity)
          return -1;
        else if (a.legalEntity > b.legalEntity)
          return 1;
        else
          return 0;
      });

    let resolvedCboDataItem = {
      type: type,
      actualReal: totalActual,
      plannedReal: totalPlanned,
      yetToReal: totalYetTo,
      totalActual: this.getValueWithComma(totalActual),
      totalPlanned: this.getValueWithComma(totalPlanned),
      totalYetTo: this.getValueWithComma(totalYetTo),
      totalYetToBracketed: totalYetToBracketed,
      yetToText: yetToText,
      overallPoC: overallPoC,
      totalActualOriginal: this.getValueTooltipWithComma(totalActual),
      totalPlannedOriginal: this.getValueTooltipWithComma(totalPlanned),
      totalYetToOriginal: this.getValueTooltipWithComma(totalYetTo),
      history: this.resolveCBODataHistory(sourceCBOTypeData, type),
      items: items,
      isLoading: false
    };

    if (this.baseLegalEntityLevelCbo == this.baseLegalEntityLevel && ((this.reportMode == "Normal Mode" &&
      this.normalModeCBOWBaseLegalEntityCbo == this.normalModeCBOWBaseLegalEntity) || (this.reportMode == "Detailed Mode" &&
        this.detailedModeBaseLegalEntityCbo == this.detailedModeBaseLegalEntity))) {

      let tempResolvedCboData = JSON.parse(JSON.stringify(this.resolvedCBOData));

      this.resolvedCBOData = [];

      for (let tempResolvedCboDataItem of tempResolvedCboData)
        if (tempResolvedCboDataItem.type != type)
          this.resolvedCBOData.push(tempResolvedCboDataItem);

      this.resolvedCBOData.push(resolvedCboDataItem);

    }

    else {

      resolvedCboDataItem["le"] = this.reportMode == "Normal Mode" ? this.normalModeCBOWBaseLegalEntityCbo : this.detailedModeBaseLegalEntityCbo;
      this.resolvedCboLowerLeLevelData.push(resolvedCboDataItem);

    }

    if (this.baseLegalEntityLevelCbo == 0)
      this.setLegalEntityCBO(this.legalEntities[0].name, type, totalYetTo, overallPoC);

    if (this.baseLegalEntityLevelCbo < 4 && type == "OBV") {

      let currentLevelLe = _.where(this.legalEntities, { leLevel: this.baseLegalEntityLevelCbo });

      if (currentLevelLe.length == this.baseLegalEntityLevelCboCount) {

        this.baseLegalEntityLevelCboCount = 1;
        this.baseLegalEntityLevelCbo++;

      }

      else
        this.baseLegalEntityLevelCboCount++;

      if (this.baseLegalEntityLevelCbo < 4) {

        let nextLevelLe = _.where(this.legalEntities, { leLevel: this.baseLegalEntityLevelCbo });
        this.normalModeCBOWBaseLegalEntityCbo = nextLevelLe[this.baseLegalEntityLevelCboCount - 1].name;
        this.detailedModeBaseLegalEntityCbo = nextLevelLe[this.baseLegalEntityLevelCboCount - 1].name;

        this.recursivelyCallCboFns();

      }

      else
        this.resolveAllCboData();

    }

  }

  resolveAllCboData() {

    for (let resolvedCBODataItem of this.resolvedCBOData) {

      let lowerLeLevelTypeData = _.where(this.resolvedCboLowerLeLevelData, { type: resolvedCBODataItem.type });

      if (resolvedCBODataItem.items.length == 0)
        this.callNonArCalcApis(resolvedCBODataItem);

      else
        for (let item of resolvedCBODataItem.items) {

          if (item.subItems.length > 0)
            for (let subItem of item.subItems) {

              let lowerLeLevelData1 = _.where(lowerLeLevelTypeData, { le: subItem.legalEntity });

              let key1 = "items";

              if (lowerLeLevelData1.length == 0) {

                let lowerLeLevelData1Temp = _.where(lowerLeLevelTypeData, { le: item.legalEntity });

                if (lowerLeLevelData1Temp.length > 0)
                  for (let tempItem of lowerLeLevelData1Temp[0].items)
                    if (tempItem.legalEntity == subItem.legalEntity) {

                      lowerLeLevelData1 = [tempItem];
                      key1 = "subItems";

                    }

              }

              if (lowerLeLevelData1.length > 0) {

                subItem["subItems"] = lowerLeLevelData1[0][key1];

                if (subItem["subItems"] != null)
                  for (let subItem1 of subItem["subItems"])
                    if (subItem1["subItems"] != null)
                      for (let subItem2 of subItem1["subItems"]) {

                        let lowerLeLevelData2 = _.where(lowerLeLevelTypeData, { le: subItem2.legalEntity });

                        let key2 = "items";

                        if (lowerLeLevelData2.length == 0) {

                          let lowerLeLevelData2Temp = _.where(lowerLeLevelTypeData, { le: subItem1.legalEntity });

                          if (lowerLeLevelData2Temp.length > 0)
                            for (let tempItem of lowerLeLevelData2Temp[0].items)
                              if (tempItem.legalEntity == subItem2.legalEntity) {

                                lowerLeLevelData2 = [tempItem];
                                key2 = "subItems";

                              }

                        }

                        if (lowerLeLevelData2.length > 0) {

                          subItem2["subItems"] = lowerLeLevelData2[0][key2];

                          if (subItem2["subItems"] != null)
                            for (let subItem3 of subItem2["subItems"])
                              if (subItem3["subItems"] != null)
                                for (let subItem4 of subItem3["subItems"]) {

                                  let lowerLeLevelData3 = _.where(lowerLeLevelTypeData, { le: subItem4.legalEntity });

                                  if (lowerLeLevelData3.length > 0)
                                    subItem4["subItems"] = lowerLeLevelData3[0]["items"];

                                }

                        }

                      }

              }

            }

          else {

            let lowerLeLevelData1 = _.where(lowerLeLevelTypeData, { le: item.legalEntity });

            if (lowerLeLevelData1.length > 0)
              item.subItems = lowerLeLevelData1[0].items;

          }

          this.callNonArCalcApis(resolvedCBODataItem);

        }

    }

    this.calculateAuthLe();

  }

  callNonArCalcApis(resolvedCBODataItem) {

    if (resolvedCBODataItem.type == "Collection" && this.reportMode == "Normal Mode") {

      resolvedCBODataItem.items = this.addNonArCollectionRecords(resolvedCBODataItem.items, this.normalModeCBOWBaseLegalEntity);

      this.appendNonArData(resolvedCBODataItem.items, true);

      for (let item of resolvedCBODataItem.items) {

        this.appendNonArData([item], false);

        item.subItems = this.addNonArCollectionRecords(item.subItems, item.legalEntity);

        this.appendNonArData(item.subItems, false);

        for (let subItem of item.subItems) {

          subItem.subItems = this.addNonArCollectionRecords(subItem.subItems == null ? [] : subItem.subItems, subItem.legalEntity);

          this.appendNonArData(subItem.subItems, false);

        }

      }

    }

  }

  addNonArCollectionRecords(items, legalEntity) {

    for (let wcdDataItem of this.wcdData)
      if (wcdDataItem.rollup_id_name == legalEntity) {

        let doesCurrentPlExists = _.where(items, { legalEntity: wcdDataItem.p_and_l_description });

        if (doesCurrentPlExists.length == 0 && wcdDataItem.p_and_l_description != legalEntity)
          items.push({
            actual: "-",
            actualOriginal: "0.00",
            actualReal: 0,
            ar: this.getARAmountForCollection(wcdDataItem.p_and_l_description, "Normal"),
            arOriginal: this.getARAmountForCollection(wcdDataItem.p_and_l_description, "Original"),
            isExpanded: false,
            legalEntity: wcdDataItem.p_and_l_description,
            plId: wcdDataItem.p_and_l_id,
            planned: "-",
            plannedOriginal: "0.00",
            plannedReal: 0,
            poC: "0",
            projectId: 0,
            projectItemName: "",
            subItems: [],
            verticalId: 0,
            yetTo: "-",
            yetToBracketed: false,
            yetToOriginal: "0.00",
            yetToReal: 0
          });

      }

    return items;

  }

  appendNonArData(subItems, isL1) {

    if (!isL1)
      for (let subItemL1 of subItems)
        this.appendNonArDataItem(subItemL1.legalEntity, subItemL1.subItems, subItemL1.plId);

    else
      this.appendNonArDataItem(this.normalModeCBOWBaseLegalEntity, subItems, 1);

  }

  appendNonArDataItem(legalEntity, subItems, plId) {

    let currentVerticalArAgingDaysData = _.where(this.arAgingDaysData, { vertical: legalEntity });

    let uniqueProjectNames = _.uniq(_.pluck(currentVerticalArAgingDaysData, 'project_name'));;

    for (let uniqueProjectName of uniqueProjectNames) {

      let doesProjectNameExist = _.where(subItems, { legalEntity: uniqueProjectName });

      let currentProjectArAgingDaysData = _.where(currentVerticalArAgingDaysData, { project_name: uniqueProjectName });

      let subItemsL2 = [], subItemsL2ArValue = 0;

      for (let currentProjectArAgingDaysDataItem of currentProjectArAgingDaysData)
        if (currentProjectArAgingDaysDataItem.ar_value != 0) {

          subItemsL2ArValue += currentProjectArAgingDaysDataItem.ar_value;

          subItemsL2.push({
            actual: "-",
            actualOriginal: "0.00",
            actualReal: 0,
            ar: this.getValueWithComma(currentProjectArAgingDaysDataItem.ar_value),
            arOriginal: this.getValueTooltipWithComma(currentProjectArAgingDaysDataItem.ar_value),
            isExpanded: false,
            legalEntity: currentProjectArAgingDaysDataItem.milestone_name,
            plId: plId,
            planned: "-",
            plannedOriginal: "0.00",
            plannedReal: 0,
            poC: "0",
            projectId: 0,
            projectItemName: "",
            subItems: [],
            verticalId: 0,
            yetTo: "-",
            yetToBracketed: false,
            yetToOriginal: "0.00",
            yetToReal: 0
          });

        }

      if (doesProjectNameExist.length <= 0) {

        if (subItems == null)
          subItems = [];

        subItems.push({
          actual: "-",
          actualOriginal: "0.00",
          actualReal: 0,
          ar: this.getValueWithComma(subItemsL2ArValue),
          arOriginal: this.getValueTooltipWithComma(subItemsL2ArValue),
          isExpanded: false,
          legalEntity: uniqueProjectName,
          plId: plId,
          planned: "-",
          plannedOriginal: "0.00",
          plannedReal: 0,
          poC: "0",
          projectId: 0,
          projectItemName: "",
          subItems: subItemsL2,
          verticalId: 0,
          yetTo: "-",
          yetToBracketed: false,
          yetToOriginal: "0.00",
          yetToReal: 0
        });

      }

      else {

        doesProjectNameExist[0].subItems = subItemsL2;
        doesProjectNameExist[0].ar = this.getValueWithComma(subItemsL2ArValue);
        doesProjectNameExist[0].arOriginal = this.getValueTooltipWithComma(subItemsL2ArValue);

      }

    }

  }

  computeYetToData(planned, actual, legalEntityData, type, isBracketedToBeChecked) {

    let yetToData = {
      yetTo: (planned - actual) > 0 ? (Number(planned) - Number(actual)) : 0,
      yetToBracketed: false
    };

    // if (isBracketedToBeChecked && yetToData.yetTo == 0) {

    //   yetToData.yetTo = this.retrieveBracketedYetToValue(legalEntityData, type);

    //   yetToData.yetTo = yetToData.yetTo > 0 ? yetToData.yetTo : 0;

    //   yetToData.yetToBracketed = yetToData.yetTo > 0 ? true : yetToData.yetToBracketed;

    // }

    return yetToData;

  }

  retrieveBracketedYetToValue(legalEntityData, type) {

    let yetTo = 0;

    let filteredLegalEntityData = _.filter(legalEntityData, function (legalEntityDataItem) {

      if (type == "Collection" && (legalEntityDataItem.milestoneStatusId == 9 || legalEntityDataItem.milestoneStatusId == 10))
        return legalEntityDataItem;

      else if (type == "Billing" && legalEntityDataItem.milestoneStatusId < 9)
        return legalEntityDataItem;

      // else if (type == "OBV")
      //   return legalEntityDataItem;

    }, this);

    yetTo = filteredLegalEntityData.reduce((s, f) => {
      return s + f.value;
    }, 0.0);

    return yetTo;

  }

  identifyCboFilters(type) {

    if (this.baseLegalEntityLevelCbo == 0) {

      this.cboFirstFilter = "pl";
      this.cboSecondFilter = "sub_pl";

    }

    else {

      let isExpandOverridden = this.reportMode == "Normal Mode" ? this.normalModeCBOWBaseLegalEntityExpandOverridden : this.detailedModeBaseLegalEntityExpandOverridden;

      if (this.baseLegalEntityLevelCbo == 1) {

        this.cboFirstFilter = "sub_pl";
        this.cboSecondFilter = "vertical";

      }

      else if (this.baseLegalEntityLevelCbo == 2 && !isExpandOverridden) {

        this.cboFirstFilter = "vertical";
        this.cboSecondFilter = "projectName";

      }

      else if (this.baseLegalEntityLevelCbo == 3 || (this.baseLegalEntityLevelCbo == 2 && isExpandOverridden)) {

        this.cboFirstFilter = type == "OBV" ? "customerName" : "projectName";
        this.cboSecondFilter = type == "OBV" ? "projectName" : "milestoneName";

      }

    }

  }


  getDDMMMYYYDate(dateValue, format) {

    return this.utilityService.getDDMMMYYYDate(dateValue, format);

  }

  getHHMMDate(dateValue) {

    if (dateValue)
      return moment(dateValue).format("HH:mm");

    else
      return "-";

  }

  getDisplayValue(displayValue, displayValueOriginal) {

    return this.utilityService.getDisplayValueRoundOff(displayValue, displayValueOriginal);

  }

  spacingKeys = {};

  getSpacing(value, object) {

    if (value != null) {

      let absValue = value != "-" && isNaN(value) ? value.replaceAll('-', '') : value;

      let valueSize = absValue.toString().length;

      if (!this.spacingKeys[object])
        this.spacingKeys[object] = valueSize;

      else if (valueSize > this.spacingKeys[object])
        this.spacingKeys[object] = valueSize;

      let valueSpaces = '';

      for (let i = valueSize; i < this.spacingKeys[object]; i++)
        valueSpaces = valueSpaces.concat("\xa0");

      value = valueSpaces + value + (value != "-" ? '' : valueSpaces);

      return value;

    }

    else
      return "";

  }

  getValueWithComma(value) {
    return this.utilityService.getValueWithComma(this.udrfService.udrfData.appliedConfig["customFields"]["isFullValue"], value, this.udrfService.udrfData.appliedConfig["customFields"]["defaultCurrency"], "Ngr", this.misFlags);
  }

  getValueWithCommaMis(value, isPercField) {

    if (isPercField)
      return this.roundToTwo(value);

    else
      return this.getValueWithComma(value);

  }

  getValueTooltipWithComma(value) {
    return this.utilityService.getValueTooltipWithComma(value, this.udrfService.udrfData.appliedConfig["customFields"]["defaultCurrency"], "Ngr", this.misFlags);
  }

  getDisplayValueInInrFormat(displayValue) {
    return this.udrfService.udrfData.appliedConfig["customFields"]["defaultCurrency"] == "INR" ? this.utilityService.getDisplayValueInInrFormat(displayValue) : this.utilityService.getDisplayValueInUSDFormat(displayValue);
  }

  getMisTextColor(typeId, value) {

    if (this.misFlags["ngrVarianceMisColorCodes" + typeId])
      if (this.misFlags["ngrVarianceMisColorCodes" + typeId] == 1) {

        if (value >= 0)
          return 1;

        else if (value < 0)
          return 2;

      }

      else {

        if (value <= 0)
          return 1;

        else if (value > 0)
          return 2;

      }

    else
      return 0;

  }

  resolveCBODataHistory(sourceCBOTypeData, type) {

    let history = [];

    let historyItemDateType = type == "Collection" ? "plannedCollectionDate" : "plannedBillingDate";

    for (let sourceCBOTypeDataItem of sourceCBOTypeData)
      if (sourceCBOTypeDataItem.versionHistory && sourceCBOTypeDataItem.versionHistory.length > 0) {

        let currentLastIndex = sourceCBOTypeDataItem.versionHistory.length - 1;

        while (currentLastIndex >= 0) {

          let historyItem = {
            dataType: type,
            plId: sourceCBOTypeDataItem.pl_id,
            verticalId: sourceCBOTypeDataItem.sub_pl_id,
            plName: sourceCBOTypeDataItem.pl,
            verticalName: sourceCBOTypeDataItem.sub_pl,
            projectName: sourceCBOTypeDataItem.projectName,
            itemName: sourceCBOTypeDataItem.itemName,
            milestoneName: sourceCBOTypeDataItem.milestoneName,
            opportunityId: sourceCBOTypeDataItem.opportunityId,
            opportunityName: sourceCBOTypeDataItem.opportunityName ? sourceCBOTypeDataItem.opportunityName : sourceCBOTypeDataItem.projectName,
            changedOn: this.getDDMMMYYYDate(sourceCBOTypeDataItem.versionHistory[currentLastIndex].changedOn, this.dfDDMMMYYYY),
            changedOnHrs: this.getHHMMDate(sourceCBOTypeDataItem.versionHistory[currentLastIndex].changedOn),
            changedBy: sourceCBOTypeDataItem.versionHistory[currentLastIndex].changedBy,
            changedByName: sourceCBOTypeDataItem.versionHistory[currentLastIndex].changedByName,
            changedFrom: this.getDDMMMYYYDate(sourceCBOTypeDataItem.versionHistory[currentLastIndex].changedFrom, this.dfDDMMMYYYY),
            changedTo: this.getDDMMMYYYDate(sourceCBOTypeDataItem.versionHistory[currentLastIndex][historyItemDateType], this.dfDDMMMYYYY)
          };

          currentLastIndex--;

          let existingHistory = _.find(history, obj => {
            return (obj.changedOn == historyItem.changedOn && obj.changedFrom == historyItem.changedFrom && obj.changedTo == historyItem.changedTo && obj.changedBy == historyItem.changedBy && (obj.opportunityId ? (obj.opportunityId == historyItem.opportunityId) : false))
          })
          if(!existingHistory)
          history.push(historyItem);

        }

      }

    return history;

  }

  getMaxDate(dates, format) {

    return this.utilityService.getMaxDate(dates, format);

  }

  getMaxValue(values) {

    return this.utilityService.getMaxValue(values);

  }

  getCBOItemLevelItemColor(milestones) {

    let itemColor = "value13";

    let uniqueMilestoneColors = _.uniq(_.pluck(milestones, "itemColor"));

    let greenItemColorCount = 0, redItemColorCount = 0, orangeItemColorCount = 0, blackItemColorCount = 0;

    for (let uniqueMilestoneColorsItem of uniqueMilestoneColors) {

      let colorCount = _.where(milestones, { itemColor: uniqueMilestoneColorsItem }).length;

      if (uniqueMilestoneColorsItem == "value13Green")
        greenItemColorCount = colorCount;

      else if (uniqueMilestoneColorsItem == "value13Red")
        redItemColorCount = colorCount;

      else if (uniqueMilestoneColorsItem == "value13Orange")
        orangeItemColorCount = colorCount;

      else if (uniqueMilestoneColorsItem == "value13")
        blackItemColorCount = colorCount;

    }

    if (redItemColorCount > 0)
      itemColor = "value13Red";

    else if (milestones.length == greenItemColorCount)
      itemColor = "value13Green";

    else if ((milestones.length == orangeItemColorCount) || (greenItemColorCount > 0 && orangeItemColorCount > 0) || (blackItemColorCount > 0 && orangeItemColorCount > 0))
      itemColor = "value13Orange";

    else
      itemColor = "value13";

    return itemColor;

  }

  getMilestoneMovedToDateFromHistory(sourceCBOTypeDataItem, type) {

    let movedToDate = "-";

    let historyItemDateType = type == "Collection" ? "plannedCollectionDate" : "plannedBillingDate";

    if (sourceCBOTypeDataItem.versionHistory && sourceCBOTypeDataItem.versionHistory.length > 1)
      movedToDate = this.getDDMMMYYYDate(sourceCBOTypeDataItem.versionHistory[sourceCBOTypeDataItem
        .versionHistory.length - 1][historyItemDateType], this.dfDDMMMYYYY);

    return movedToDate;

  }

  retrieveWCDData() {

    this.resetNonDetailedModeData();

    if (!this.isWCDDataRetrieved) {

      this.isWCDDataRetrieved = true;

      this.arUbrService.getARUBRDays().then((wcdData: any) => {

        if (wcdData && wcdData.length && wcdData.length > 0)
          this.wcdData = wcdData;

        else
          this.wcdData = [];

        this.resolveWCDData();

      }, err => {
        this.showErrorMessage(err);
      });

    }

    else
      this.resolveWCDData();

  }

  resolveWCDData() {

    this.resolvedWCDData = [];

    let wcdUserRolePlIds: any = [], wcdUserRoleRollupPlIds: any = [];

    let wcdUserRolePlIdsData = _.where(this.userRolePlIds, { object_id: 169 });

    if (wcdUserRolePlIdsData && wcdUserRolePlIdsData.length > 0) {

      wcdUserRolePlIds = wcdUserRolePlIdsData[0].pl_ids;
      wcdUserRoleRollupPlIds = wcdUserRolePlIdsData[0].rollup_pl_ids;

    }

    if (wcdUserRolePlIds != "ALL")
      if (wcdUserRolePlIds.length > 0) {

        let cumulativeIds = wcdUserRolePlIds.concat(wcdUserRoleRollupPlIds);

        this.wcdData = _.filter(this.wcdData, function (wcdDataItem) {

          if (wcdDataItem.p_and_l_id && _.contains(cumulativeIds, wcdDataItem.p_and_l_id))
            return wcdDataItem;

          else if (wcdDataItem.rollup_id && _.contains(cumulativeIds, wcdDataItem.rollup_id) &&
            wcdDataItem.p_and_l_id && _.contains(cumulativeIds, wcdDataItem.p_and_l_id))
            return wcdDataItem;

        }, this);

      }

      else
        this.wcdData = [];

    for (let wcdDataItem of this.wcdData)
      this.resolvedWCDData.push({
        legalEntity: wcdDataItem.p_and_l_description,
        legalEntityRollup: wcdDataItem.rollup_id_name,
        plId: wcdDataItem.rollup_id_name == this.highestPl ? wcdDataItem.p_and_l_id : wcdDataItem.rollup_id,
        verticalId: wcdDataItem.rollup_id_name == this.highestPl ? "" : wcdDataItem.p_and_l_id,
        projectItemName: "",
        arDays: wcdDataItem.ar_days,
        arAmount: wcdDataItem.ar_amount,
        ubrDays: wcdDataItem.ubr_days,
        ubrAmount: wcdDataItem.ubr_amount,
        wcDays: wcdDataItem.wc_days,
        wcAmount: (wcdDataItem.ar_amount + wcdDataItem.ubr_amount),
        retentionAmount: wcdDataItem.retention_amount,
        arWoRetention: wcdDataItem.ar_wo_retention,
        wcDaysWoRetention: wcdDataItem.wc_days_wo_retention,
        wcWoRetention: wcdDataItem.wc_wo_retention
      });

    let lei_ar_days = 0, lei_ar_amount = 0, lei_ubr_days = 0, lei_ubr_amount = 0, lei_wc_days = 0, lei_wc_amount = 0,
      lei_retention_amount = 0, lei_ar_wo_retention = 0, lei_wc_days_wo_retention = 0, lei_wc_wo_retention = 0;

    let wcdCbowViewCard = _.where(this.cbowViewCards, { dataType: 'WCD' });

    // if (this.baseLegalEntityLevel == 0)
    for (let legalEntitiesItem of this.legalEntities) {
      // if (legalEntitiesItem.name != this.highestPl) {

      let leiData = _.where(this.resolvedWCDData, { legalEntity: legalEntitiesItem.name });

      leiData = leiData.length > 0 ? [leiData[0]] : [];

      lei_ar_days = leiData.reduce((s, f) => {
        return s + f.arDays;
      }, 0);

      lei_ar_amount = leiData.reduce((s, f) => {
        return s + f.arAmount;
      }, 0.0);

      lei_ubr_days = leiData.reduce((s, f) => {
        return s + f.ubrDays;
      }, 0);

      lei_ubr_amount = leiData.reduce((s, f) => {
        return s + f.ubrAmount;
      }, 0.0);

      lei_wc_days = leiData.length > 0 && leiData[0].wcDays ? leiData[0].wcDays : 0;

      lei_wc_amount = leiData.reduce((s, f) => {
        return s + f.wcAmount;
      }, 0.0);

      lei_retention_amount = leiData.reduce((s, f) => {
        return s + f.retentionAmount;
      }, 0.0);

      lei_ar_wo_retention = leiData.reduce((s, f) => {
        return s + f.arWoRetention;
      }, 0.0);

      lei_wc_days_wo_retention = leiData.reduce((s, f) => {
        return s + f.wcDaysWoRetention;
      }, 0.0);

      lei_wc_wo_retention = leiData.reduce((s, f) => {
        return s + f.wcWoRetention;
      }, 0.0);

      this.setLegalEntityWCD(legalEntitiesItem.name, lei_ar_days, lei_ar_amount, lei_ubr_days, lei_ubr_amount, lei_wc_days, lei_wc_amount,
        lei_retention_amount, lei_ar_wo_retention, lei_wc_days_wo_retention, lei_wc_wo_retention);

      if ((this.reportMode == "Normal Mode" && legalEntitiesItem.name == this.normalModeCBOWBaseLegalEntity) || (this.reportMode ==
        "Detailed Mode" && legalEntitiesItem.name == this.detailedModeBaseLegalEntity)) {

        wcdCbowViewCard[0].arAmount = this.getValueWithComma(lei_ar_amount);

        wcdCbowViewCard[0].arAmountOriginal = this.getValueTooltipWithComma(lei_ar_amount);

        wcdCbowViewCard[0].arAmountMain = lei_ar_amount;

        wcdCbowViewCard[0].arDays = lei_ar_days;

        wcdCbowViewCard[0].ubrAmount = this.getValueWithComma(lei_ubr_amount);

        wcdCbowViewCard[0].ubrAmountOriginal = this.getValueTooltipWithComma(lei_ubr_amount);

        wcdCbowViewCard[0].ubrAmountMain = lei_ubr_amount;

        wcdCbowViewCard[0].ubrDays = lei_ubr_days;

        wcdCbowViewCard[0].retentionAmount = this.getValueWithComma(lei_retention_amount);

        wcdCbowViewCard[0].retentionAmountOriginal = this.getValueTooltipWithComma(lei_retention_amount);

        wcdCbowViewCard[0].arWoRetention = this.getValueWithComma(lei_ar_wo_retention);

        wcdCbowViewCard[0].arWoRetentionOriginal = this.getValueTooltipWithComma(lei_ar_wo_retention);

        wcdCbowViewCard[0].wcDaysWoRetention = lei_wc_days_wo_retention;

        wcdCbowViewCard[0].wcWoRetention = this.getValueWithComma(lei_wc_wo_retention);

        wcdCbowViewCard[0].wcWoRetentionOriginal = this.getValueTooltipWithComma(lei_wc_wo_retention);

        wcdCbowViewCard[0].actualValue = lei_wc_days;

        wcdCbowViewCard[0].actualValueOriginal = lei_wc_days;

        wcdCbowViewCard[0].plannedValue = this.getValueWithComma(lei_wc_amount);

        wcdCbowViewCard[0].plannedValueOriginal = this.getValueTooltipWithComma(lei_wc_amount);

        this.detailed_mode_total_ar_days = lei_ar_days;

        this.detailed_mode_total_ar_amount = this.getValueWithComma(lei_ar_amount);

        this.detailed_mode_total_ar_amount_original = this.getValueTooltipWithComma(lei_ar_amount);

        this.detailed_mode_total_ubr_days = lei_ubr_days;

        this.detailed_mode_total_ubr_amount = this.getValueWithComma(lei_ubr_amount);

        this.detailed_mode_total_ubr_amount_original = this.getValueTooltipWithComma(lei_ubr_amount);

        this.detailed_mode_total_wc_days = lei_wc_days;

        this.detailed_mode_total_wc_amount = this.getValueWithComma(lei_wc_amount);

        this.detailed_mode_total_wc_amount_original = this.getValueTooltipWithComma(lei_wc_amount);

        this.detailed_mode_total_retention_amount = this.getValueWithComma(lei_retention_amount);

        this.detailed_mode_total_retention_amount_original = this.getValueTooltipWithComma(lei_retention_amount);

        this.detailed_mode_total_ar_wo_retention = this.getValueWithComma(lei_ar_wo_retention);

        this.detailed_mode_total_ar_wo_retention_original = this.getValueTooltipWithComma(lei_ar_wo_retention);

        this.detailed_mode_total_wc_days_wo_retention = lei_wc_days_wo_retention;

        this.detailed_mode_total_wc_wo_retention = this.getValueWithComma(lei_wc_wo_retention);

        this.detailed_mode_total_wc_wo_retention_original = this.getValueTooltipWithComma(lei_wc_wo_retention);

      }

    }

    let tempResolvedWCDData = [];

    let resolvedWCDPlData = _.filter(this.resolvedWCDData, function (resolvedWCDDataItem) {

      if ((this.reportMode == "Normal Mode" && resolvedWCDDataItem.legalEntityRollup ==
        this.normalModeCBOWBaseLegalEntity) || (this.reportMode == "Detailed Mode" &&
          resolvedWCDDataItem.legalEntityRollup == this.detailedModeBaseLegalEntity))
        return resolvedWCDDataItem;

    }, this);

    let tempData = [];

    if (resolvedWCDPlData.length == 2 && resolvedWCDPlData[0].legalEntity == resolvedWCDPlData[1].legalEntity)
      tempData = [resolvedWCDPlData[0]];

    else
      tempData = resolvedWCDPlData;

    resolvedWCDPlData = tempData;

    for (let resolvedWCDPlDataItem of resolvedWCDPlData) {

      let resolvedWCDRollupData = _.where(this.resolvedWCDData, { legalEntityRollup: resolvedWCDPlDataItem.legalEntity });

      if (!(resolvedWCDRollupData.length == 1 && resolvedWCDPlDataItem.legalEntity == resolvedWCDRollupData[0].legalEntity) &&
        !(resolvedWCDRollupData.length == 2 && resolvedWCDPlDataItem.legalEntity == resolvedWCDRollupData[0].legalEntity &&
          resolvedWCDPlDataItem.legalEntity == resolvedWCDRollupData[1].legalEntity)) {

        let subItems = [];

        for (let resolvedWCDRollupDataItem of resolvedWCDRollupData) {

          resolvedWCDRollupDataItem.arAmountOriginal = this.getValueTooltipWithComma(resolvedWCDRollupDataItem.arAmount);
          resolvedWCDRollupDataItem.arAmountMain = resolvedWCDRollupDataItem.arAmount;
          resolvedWCDRollupDataItem.ubrAmountOriginal = this.getValueTooltipWithComma(resolvedWCDRollupDataItem.ubrAmount);
          resolvedWCDRollupDataItem.ubrAmountMain = resolvedWCDRollupDataItem.ubrAmount;
          resolvedWCDRollupDataItem.wcAmountOriginal = this.getValueTooltipWithComma(resolvedWCDRollupDataItem.wcAmount);

          resolvedWCDRollupDataItem.arAmount = this.getValueWithComma(resolvedWCDRollupDataItem.arAmount);
          resolvedWCDRollupDataItem.ubrAmount = this.getValueWithComma(resolvedWCDRollupDataItem.ubrAmount);
          resolvedWCDRollupDataItem.wcAmount = this.getValueWithComma(resolvedWCDRollupDataItem.wcAmount);

          resolvedWCDRollupDataItem.isSubItem = true;

          subItems.push(resolvedWCDRollupDataItem);

        }

        resolvedWCDPlDataItem.subItems = subItems;

      }

      resolvedWCDPlDataItem.arAmountOriginal = this.getValueTooltipWithComma(resolvedWCDPlDataItem.arAmount);
      resolvedWCDPlDataItem.arAmountMain = resolvedWCDPlDataItem.arAmount;
      resolvedWCDPlDataItem.ubrAmountOriginal = this.getValueTooltipWithComma(resolvedWCDPlDataItem.ubrAmount);
      resolvedWCDPlDataItem.ubrAmountMain = resolvedWCDPlDataItem.ubrAmount;
      resolvedWCDPlDataItem.wcAmountOriginal = this.getValueTooltipWithComma(resolvedWCDPlDataItem.wcAmount);

      resolvedWCDPlDataItem.arAmount = this.getValueWithComma(resolvedWCDPlDataItem.arAmount);
      resolvedWCDPlDataItem.ubrAmount = this.getValueWithComma(resolvedWCDPlDataItem.ubrAmount);
      resolvedWCDPlDataItem.wcAmount = this.getValueWithComma(resolvedWCDPlDataItem.wcAmount);

      resolvedWCDPlDataItem.isExpanded = false;

      tempResolvedWCDData.push(resolvedWCDPlDataItem);

    }

    if (this.misFlags.shouldSortByLegalEntityByDefault)
      tempResolvedWCDData = tempResolvedWCDData.sort((a, b) => {
        if (a.legalEntity < b.legalEntity)
          return -1;
        else if (a.legalEntity > b.legalEntity)
          return 1;
        else
          return 0;
      });

    this.resolvedWCDData = tempResolvedWCDData;

    this.isResolvedWCDDataLoading = false;

  }

  ChangeBBduration(range) {

    let startDate;
    let endDate;

    if (range == "Current FY") {

      startDate = this.ftyStartDate;
      endDate = this.ftyEndDate;
      this.isBBDataRetrieved = false;
      this.retrieveBBData(startDate, endDate);
      this.BBDurationRange = "Current FY";

    }

    else if (range == "This month") {

      startDate = this.utilityService.getFormattedDate(moment().startOf('month'), moment(moment().startOf('month')).date, 15, 0, 0, 0);
      endDate = this.utilityService.getFormattedDate(moment().endOf('month'), moment(moment().endOf('month')).date, 15, 0, 0, 0);
      this.BBDurationRange = "This month";
      this.isBBDataRetrieved = false;
      this.retrieveBBData(startDate, endDate);

    } else if (range == "Next month") {

      startDate = this.utilityService.getFormattedDate(moment().add(1, 'month').startOf('month'), moment(moment().add(1, 'month').startOf('month')).date, 15, 0, 0, 0);
      endDate = this.utilityService.getFormattedDate(moment().add(1, 'month').endOf('month'), moment(moment().add(1, 'month').endOf('month')).date, 15, 0, 0, 0);
      this.BBDurationRange = "Next month";
      this.isBBDataRetrieved = false;
      this.retrieveBBData(startDate, endDate);

    } else if (range == "3 months") {

      startDate = this.utilityService.getFormattedDate(moment().startOf('month'), moment(moment().startOf('month')).date, 15, 0, 0, 0);
      startDate = this.utilityService.getFormattedDate(moment().add(2, 'month').endOf('month'), moment(moment().add(2, 'month').endOf('month')).date, 15, 0, 0, 0);
      this.BBDurationRange = "3 months";
      this.isBBDataRetrieved = false;
      this.retrieveBBData(startDate, endDate);

    } else if (range == "Next FY") {

      startDate = this.ftnyStartDate;
      endDate = this.ftnyEndDate;
      this.isBBDataRetrieved = false;
      this.retrieveBBData(startDate, endDate);
      this.BBDurationRange = "Next FY";

    }

  }

  ChangeBBProbability(prob) {

    this.BBProbability[0][prob] = !this.BBProbability[0][prob];
    this.resolveBBData();

  }

  retrieveBBData(BBstartDate, BBendDate) {

    let apiParams = {
      startDate: BBstartDate,
      endDate: BBendDate,
      defaultCurrency: this.udrfService.udrfData.appliedConfig["customFields"]["defaultCurrency"],
      todaysDate: moment().format(this.dateFormat)
    };

    if (!this.isBBDataRetrieved) {

      this.isBBDataRetrieved = true;

      this.governanceReportSubService.getBBData(apiParams).then((BBDataRes: any) => {

        if (BBDataRes["data"]?.length > 0) {

          for (let bbItemDataValue of BBDataRes["data"]) {

            if (bbItemDataValue["customer_name"] != null && isNaN(bbItemDataValue["customer_name"]))
              bbItemDataValue["customer_name"] = bbItemDataValue["customer_name"].trim();

            if (bbItemDataValue["opportunity_name"] != null && isNaN(bbItemDataValue["opportunity_name"]))
              bbItemDataValue["opportunity_name"] = bbItemDataValue["opportunity_name"].trim();

            if (bbItemDataValue["sales_unit_name"] != null && isNaN(bbItemDataValue["sales_unit_name"]))
              bbItemDataValue["sales_unit_name"] = bbItemDataValue["sales_unit_name"].trim();

            bbItemDataValue["expected_value_show"] = this.getValueWithComma(bbItemDataValue["expected_value"]);
            bbItemDataValue["expected_value_original"] = bbItemDataValue["expected_value"];
            bbItemDataValue["expected_value_view"] = this.getValueTooltipWithComma(bbItemDataValue["expected_value"]);

            bbItemDataValue["expected_future_revenue_view"] = this.utilityService.getDisplayValueInInrFormat(this.utilityService.roundToTwo(bbItemDataValue["expected_future_revenue"]));
            bbItemDataValue["expected_future_revenue_in_cr"] = this.utilityService.convertFromCr(bbItemDataValue["expected_future_revenue"]);

            bbItemDataValue["bb_gm"] = this.getValueWithComma(bbItemDataValue["total_bb_gm"]);
            bbItemDataValue["bb_gm_original"] = bbItemDataValue["total_bb_gm"];
            bbItemDataValue["bb_gm_view"] = this.getValueTooltipWithComma(bbItemDataValue["total_bb_gm"]);

            bbItemDataValue["kt_cost_view"] = this.utilityService.getDisplayValueInInrFormat(this.utilityService.roundToTwo(bbItemDataValue["kt_cost"]));
            bbItemDataValue["kt_cost_in_cr"] = this.utilityService.convertFromCr(bbItemDataValue["kt_cost"]);

            bbItemDataValue["current_fy_uob_value_show"] = this.getValueWithComma(bbItemDataValue["current_fy_uob_value"]);
            bbItemDataValue["current_fy_uob_value_original"] = bbItemDataValue["current_fy_uob_value"];
            bbItemDataValue["current_fy_uob_value_view"] = this.getValueTooltipWithComma(bbItemDataValue["current_fy_uob_value"]);

            bbItemDataValue["current_fy_uob_value2_view"] = this.utilityService.getDisplayValueInInrFormat(this.utilityService.roundToTwo(bbItemDataValue["current_fy_uob_value2"]));
            bbItemDataValue["current_fy_uob_value2_in_cr"] = this.utilityService.convertFromCr(bbItemDataValue["current_fy_uob_value2"]);

            bbItemDataValue["current_fy_uob_value3_view"] = this.utilityService.getDisplayValueInInrFormat(this.utilityService.roundToTwo(bbItemDataValue["current_fy_uob_value3"]));
            bbItemDataValue["current_fy_uob_value3_in_cr"] = this.utilityService.convertFromCr(bbItemDataValue["current_fy_uob_value3"]);

            bbItemDataValue["current_fy_uob_value4_view"] = this.utilityService.getDisplayValueInInrFormat(this.utilityService.roundToTwo(bbItemDataValue["current_fy_uob_value4"]));
            bbItemDataValue["current_fy_uob_value4_in_cr"] = this.utilityService.convertFromCr(bbItemDataValue["current_fy_uob_value4"]);

            bbItemDataValue["current_fy_uob_value5_view"] = this.utilityService.getDisplayValueInInrFormat(this.utilityService.roundToTwo(bbItemDataValue["current_fy_uob_value5"]));
            bbItemDataValue["current_fy_uob_value5_in_cr"] = this.utilityService.convertFromCr(bbItemDataValue["current_fy_uob_value5"]);

            bbItemDataValue["current_fy_gm_inr_value"] = bbItemDataValue["future_fy_gm_inr_1"];
            bbItemDataValue["future_fy_gm_inr_1_orig"] = bbItemDataValue["future_fy_gm_inr_1"];

            bbItemDataValue["future_fy_gm_inr_1_view"] = this.utilityService.getDisplayValueInInrFormat(this.utilityService.roundToTwo(bbItemDataValue["future_fy_gm_inr_1"]));
            bbItemDataValue["future_fy_gm_inr_1"] = this.utilityService.convertFromCr(bbItemDataValue["future_fy_gm_inr_1"]);



            bbItemDataValue["future_fy_gm_inr_value"] = bbItemDataValue["future_fy_gm_inr_2"];
            bbItemDataValue["future_fy_gm_inr_2_orig"] = bbItemDataValue["future_fy_gm_inr_2"];

            bbItemDataValue["future_fy_gm_inr_2_view"] = this.utilityService.getDisplayValueInInrFormat(this.utilityService.roundToTwo(bbItemDataValue["future_fy_gm_inr_2"]));
            bbItemDataValue["future_fy_gm_inr_2"] = this.utilityService.convertFromCr(bbItemDataValue["future_fy_gm_inr_2"]);

            bbItemDataValue["future_fy_gm_inr_3_view"] = this.utilityService.getDisplayValueInInrFormat(this.utilityService.roundToTwo(bbItemDataValue["future_fy_gm_inr_3"]));
            bbItemDataValue["future_fy_gm_inr_3"] = this.utilityService.convertFromCr(bbItemDataValue["future_fy_gm_inr_3"]);

            bbItemDataValue["future_fy_gm_inr_4_view"] = this.utilityService.getDisplayValueInInrFormat(this.utilityService.roundToTwo(bbItemDataValue["future_fy_gm_inr_4"]));
            bbItemDataValue["future_fy_gm_inr_4"] = this.utilityService.convertFromCr(bbItemDataValue["future_fy_gm_inr_4"]);

            bbItemDataValue["future_fy_gm_inr_5_view"] = this.utilityService.getDisplayValueInInrFormat(this.utilityService.roundToTwo(bbItemDataValue["future_fy_gm_inr_5"]));
            bbItemDataValue["future_fy_gm_inr_5"] = this.utilityService.convertFromCr(bbItemDataValue["future_fy_gm_inr_5"]);

            // bbItemDataValue["future_uob_cummulation_inr_view"] = this.utilityService.getDisplayValueInInrFormat(this.utilityService.roundToTwo(bbItemDataValue["future_uob_cummulation_inr"]));
            // bbItemDataValue["future_uob_cummulation_inr"] = this.utilityService.convertFromCr(bbItemDataValue["future_uob_cummulation_inr"]);

            bbItemDataValue["future_uob_cummulation_inr_view"] = this.utilityService.getDisplayValueInInrFormat(this.utilityService.roundToTwo(
              bbItemDataValue["current_fy_uob_value2"] + bbItemDataValue["current_fy_uob_value3"] +
              bbItemDataValue["current_fy_uob_value4"] + bbItemDataValue["current_fy_uob_value5"]));

            bbItemDataValue["future_uob_cummulation_inr"] = this.utilityService.convertFromCr(
              bbItemDataValue["current_fy_uob_value2"] + bbItemDataValue["current_fy_uob_value3"] + bbItemDataValue["current_fy_uob_value4"] +
              bbItemDataValue["current_fy_uob_value5"]);


            if (bbItemDataValue["processing_end_date"] == "0000-00-00 00:00:00")
              bbItemDataValue["processing_end_date"] = "";

            else {

              let tempText = bbItemDataValue["processing_end_date"].split("T");

              if (tempText.length > 0)
                bbItemDataValue["processing_end_date"] = moment(tempText[0]).format("DD-MMM-YY");

              else
                bbItemDataValue["processing_end_date"] = "";

            }

            bbItemDataValue["closure_date"] = bbItemDataValue["processing_end_date"];

          }

          this.originalBbData = BBDataRes["data"];

        }

        else
          this.originalBbData = [];

        this.resolveBBData();

      }, err => {
        this.showErrorMessage(err);
      });

    }

    else
      this.resolveBBData();

  }

  resolveBBData() {

    let bbUserRolePlIds: any = [];

    let bbUserRolePlIdsData = _.where(this.userRolePlIds, { object_id: 170 });

    if (bbUserRolePlIdsData && bbUserRolePlIdsData.length > 0)
      bbUserRolePlIds = bbUserRolePlIdsData[0].pl_ids;

    this.BBData = JSON.parse(JSON.stringify(this.originalBbData));

    if (bbUserRolePlIds != "ALL")
      if (bbUserRolePlIds.length > 0)
        this.BBData = _.filter(this.originalBbData, function (BBDataItem) {

          if ((BBDataItem.pl_id && _.contains(bbUserRolePlIds, BBDataItem.pl_id)) ||
            (BBDataItem.rollup_id && _.contains(bbUserRolePlIds, BBDataItem.rollup_id)))
            return BBDataItem;

        }, this);
      else
        this.BBData = [];

    this.BBData = _.filter(this.BBData, function (BBDataItem) {
      if (moment(BBDataItem.closure_date).isSameOrAfter(this.ftyStartDate) && moment(BBDataItem.closure_date).isSameOrBefore(this.ftyEndDate))
        return BBDataItem;
    }, this);

    let pl = this.detailedModeBaseLegalEntity;
    let probability = this.BBProbability;

    if (this.detailedModeBaseLegalEntity != this.highestPl) {
      this.BBData = _.filter(this.BBData, function (resolvedBBDataItem: any) {

        return resolvedBBDataItem.description == pl || resolvedBBDataItem.rollup_id_name == pl || resolvedBBDataItem.region_rollup_name == pl || resolvedBBDataItem.region_rollup_name1 == pl;

      });
    }

    this.ResolvedBBData = _.filter(this.BBData, function (BBDataItem) {
      if (moment(BBDataItem.closure_date).isSameOrAfter(this.ftyStartDate) && moment(BBDataItem.closure_date).isSameOrBefore(this.ftyEndDate))
        return BBDataItem;
    }, this);

    if (probability[0]['veryhigh'] || probability[0]['high'] || probability[0]['medium'] || probability[0]['low']) {
      this.ResolvedBBData = _.filter(this.ResolvedBBData, function (resolvedBBDataItem: any) {
        let prob;
        prob = resolvedBBDataItem.probability == 'Very High' ? 'veryhigh' : resolvedBBDataItem.probability == 'High' ? 'high' : resolvedBBDataItem.probability == 'Medium' ? 'medium' : 'low';
        if (probability[0][prob] == true)
          return resolvedBBDataItem.probability;

      });
    }
    if (probability[0]['netnew']) {
      this.ResolvedBBData = _.where(this.ResolvedBBData, { 'account_type_id': 4 })
    }
    if (probability[0]['eam']) {
      this.ResolvedBBData = _.where(this.ResolvedBBData, { 'account_type_id': 5 })

    }

    let obv: any, bbRev: any, bbGM: any, uob1: any, uob2: any, uob3: any, uob4: any, cgm = 0, nygm = 0, nyrev = 0, bbgmm = 0;
    obv = this.ResolvedBBData.reduce((s, f) => {
      return s + f.expected_value_original;
    }, 0);

    bbRev = this.ResolvedBBData.reduce((s, f) => {
      return s + f.current_fy_uob_value;
    }, 0);

    bbGM = this.ResolvedBBData.reduce((s, f) => {
      return s + f.total_bb_gm;
    }, 0);
    uob1 = this.ResolvedBBData.reduce((s, f) => {
      return s + f.current_fy_uob_value2;
    }, 0.00);

    uob2 = this.ResolvedBBData.reduce((s, f) => {
      return s + f.current_fy_uob_value3;
    }, 0.00);

    uob3 = this.ResolvedBBData.reduce((s, f) => {
      return s + f.current_fy_uob_value4;
    }, 0.00);

    uob4 = this.ResolvedBBData.reduce((s, f) => {
      return s + f.current_fy_uob_value5;
    }, 0.00);

    for (let item of this.ResolvedBBData) {

      cgm += parseFloat(item['current_fy_gm_inr_value']);
      nygm += parseFloat(item['future_fy_gm_inr_value']);
      nyrev += parseFloat(item['expected_future_revenue']);
      bbgmm += parseFloat(item['bb_gm_original']);
    }

    this.BBDataCount[0].obv = obv
    this.BBDataCount[0].bbRev = bbRev
    this.BBDataCount[0].bbGM = bbGM
    this.BBDataCount[0].uob1 = uob1
    this.BBDataCount[0].uob2 = uob2
    this.BBDataCount[0].uob3 = uob3
    this.BBDataCount[0].uob4 = uob4
    this.BBDataCount[0].cgm = cgm
    this.BBDataCount[0].nygm = nygm
    this.BBDataCount[0].nyrev = nyrev
    this.BBDataCount[0].bbgmm = bbgmm
    this.BBDataCount[0].total = this.ResolvedBBData?.length ? this.ResolvedBBData.length : 0;

    this.isBbDataLoading = false;

    this.isBbDataResolved = true;

    if (this.isMisFtyDataResolved && this.isBbDataResolved)
      this.resolveMISOpportunityData(null);

  }

  resolveBbDataTypeItems(legalEntity, intervalCode) {

    let involvedPls = [];

    for (let legalEntitiesDataItem of this.legalEntitiesData) {

      if (legalEntitiesDataItem.description == legalEntity)
        involvedPls.push(legalEntitiesDataItem);

      for (let rollupLevel of legalEntitiesDataItem.rollupLevels)
        if (rollupLevel.rollUpPlName == legalEntity)
          involvedPls.push(legalEntitiesDataItem);

    }

    let involvedPlIds = _.uniq(_.pluck(involvedPls, 'plId'));

    let currentMisOpportunityFilterArray = _.where(this.misOpportunityFilterArray, { intervalCode: (intervalCode + "_BB") });

    let currentBbData = currentMisOpportunityFilterArray.length > 0 ? _.filter(this.BBData, function (bbDataItem) {

      if (currentMisOpportunityFilterArray[0].filters.length > 0) {

        if (_.contains(currentMisOpportunityFilterArray[0].filters, bbDataItem.probability))
          return bbDataItem;

        else if (_.contains(currentMisOpportunityFilterArray[0].filters, "Net New") && bbDataItem.account_type_id == 4)
          return bbDataItem;

        else if (_.contains(currentMisOpportunityFilterArray[0].filters, "EAM") && bbDataItem.account_type_id == 5)
          return bbDataItem;

      }

      else if (currentMisOpportunityFilterArray[0].filters.length == 0)
        return bbDataItem;

    }) : this.BBData;

    let plBbData = _.filter(currentBbData, function (BbDataItem) {

      if (_.contains(involvedPlIds, BbDataItem.pl_id))
        return BbDataItem;

      else if (BbDataItem.description == BbDataItem.rollup_id_name && _.contains(involvedPlIds, BbDataItem.rollup_id))
        return BbDataItem;

    });


    let revenue = 0, cost = 0, gm = 0, pbt = 0;

    if (intervalCode == "LE" || intervalCode == "UOB") {

      revenue = plBbData.reduce((s, f) => {
        return s + parseFloat(f.current_fy_uob_value ? f.current_fy_uob_value : 0);
      }, 0.0);

      gm = plBbData.reduce((s, f) => {
        return s + parseFloat(f.future_fy_gm_inr_1_orig ? f.future_fy_gm_inr_1_orig : 0);
      }, 0.0);

    }

    else if (intervalCode == "UOB_NY") {

      revenue = plBbData.reduce((s, f) => {
        return s + parseFloat(f.current_fy_uob_value2 ? f.current_fy_uob_value2 : 0);
      }, 0.0);

      gm = plBbData.reduce((s, f) => {
        return s + parseFloat(f.future_fy_gm_inr_2_orig ? f.future_fy_gm_inr_2_orig : 0);
      }, 0.0);

    }

    else if (intervalCode == "FTM") {

      for (let plBbDataItem of plBbData)
        if (moment(moment(plBbDataItem.closure_date).format(this.dateFormat)).add(plBbDataItem.opp_closure_date_buffer_days).isSameOrBefore(moment().endOf('month').format(this.dateFormat))) {

          let noOfDaysRemainingInMonth = Math.abs(moment(moment(plBbDataItem.closure_date).format(this.dateFormat)).diff(moment().endOf('month').format(this.dateFormat), 'd')) + 1;

          let noOfDaysFromClosureDateToEndOfFy = Math.abs(moment(moment(plBbDataItem.closure_date).format(this.dateFormat)).diff(this.ftyEndDate, 'd')) + 1;

          let revenuePerDay = plBbDataItem.current_fy_uob_value / noOfDaysFromClosureDateToEndOfFy;

          let gmPerDay = plBbDataItem.future_fy_gm_inr_1_orig / noOfDaysFromClosureDateToEndOfFy;

          revenue += (noOfDaysRemainingInMonth * revenuePerDay);

          gm += (noOfDaysRemainingInMonth * gmPerDay);

        }

    }

    cost = revenue - gm;
    pbt = gm - 0;

    let bbDataType = {
      intervalCode: intervalCode + "_BB",
      legalEntity: legalEntity,
      1: revenue,
      2: cost,
      9: gm,
      12: pbt,
      opportunityItems: plBbData
    };

    for (let visibleMisCard of this.visibleMisCards)
      if (!_.contains([this.misFlags.reportRevenueTypeId, this.misFlags.reportCostTypeId, this.misFlags.reportGmTypeId,
      this.misFlags.reportPbtTypeId], parseInt(visibleMisCard.posting_type_id))) {

        bbDataType[visibleMisCard.posting_type_id] = 0;
        bbDataType["mis" + visibleMisCard.posting_type_id] = 0;
        bbDataType["opportunity" + visibleMisCard.posting_type_id] = 0;

      }

    this.bbDataTypes.push(bbDataType);

  }

  misOpportunityFilterArray = [];

  changeMisOpportunityFilter(misOpportunityFilter, intervalCode) {

    let currentIntervalFilters = _.where(this.misOpportunityFilterArray, { intervalCode: intervalCode });

    if (this.doesMisOpportunityFilterHave(misOpportunityFilter, intervalCode))
      currentIntervalFilters[0].filters.splice(currentIntervalFilters[0].filters.indexOf(misOpportunityFilter), 1);

    else {

      if (currentIntervalFilters.length == 0)
        this.misOpportunityFilterArray.push({
          intervalCode: intervalCode,
          filters: [misOpportunityFilter]
        });

      else
        currentIntervalFilters[0].filters.push(misOpportunityFilter);

    }

    this.resolveMISOpportunityData(intervalCode);

  }

  doesMisOpportunityFilterHave(misOpportunityFilter, intervalCode) {

    let currentIntervalFilters = _.where(this.misOpportunityFilterArray, { intervalCode: intervalCode });

    return currentIntervalFilters.length > 0 ? _.contains(currentIntervalFilters[0].filters, misOpportunityFilter) : false;

  }

  resolveBbDataTypes() {

    this.bbDataTypes = [];

    let overallMisData = JSON.parse(JSON.stringify(this.resolvedMISData.concat(this.resolvedMisDataForOpportunityExcessCards)));

    for (let resolvedMISDataItem of overallMisData) {

      this.resolveBbDataTypeItems(this.detailedModeBaseLegalEntity, resolvedMISDataItem.intervalCode);

      let items = resolvedMISDataItem.items ? resolvedMISDataItem.items : [];

      if (items.length == 0) {

        for (let le of this.legalEntitiesData)
          if ((this.detailedModeBaseLegalEntity == this.highestPl && le.rollupLevels.length == 1) || le.rollupLevels.length > 1)
            for (let subLe of le.rollupLevels)
              if (subLe.rollUpPlName == this.detailedModeBaseLegalEntity)
                items.push({ legalEntity: le.description });

      }

      for (let item of items) {

        this.resolveBbDataTypeItems(item.legalEntity, resolvedMISDataItem.intervalCode);

        let subItems = item.subItems ? item.subItems : [];

        if (subItems.length == 0) {

          for (let le of this.legalEntitiesData)
            if ((le.rollupLevels.length - 1) == (this.baseLegalEntityLevel + 1))
              for (let subLe of le.rollupLevels)
                if (subLe.rollUpPlName == item.legalEntity)
                  subItems.push({ legalEntity: le.description });

        }

        for (let subItem of subItems)
          this.resolveBbDataTypeItems(subItem.legalEntity, resolvedMISDataItem.intervalCode);

      }

    }

    if (this.initBbDataTypes.length == 0)
      this.initBbDataTypes = this.bbDataTypes;

  }

  bbDataTypes = [];

  initBbDataTypes = [];

  resolveMISOpportunityData(intervalCode) {

    this.resolveBbDataTypes();

    let selectedCount = 0;

    for (let misOpportunityCardsItem of this.udrfService.udrfData.appliedConfig["customFields"]["misOpportunityCards"])
      if (misOpportunityCardsItem.isSelected && (intervalCode == null || (misOpportunityCardsItem.dataTypeCode == intervalCode)))
        setTimeout(() => {
          this.resolveMISData(misOpportunityCardsItem.dataTypeCode, misOpportunityCardsItem.order, false, true);
        }, 50 * selectedCount++);

    this.isMisFtyDataResolved = false;

    this.isBbDataResolved = false;

  }

  retrieveArAgingData() {

    if (!this.isArAgingDaysDataRetrieved) {

      this.isArAgingDaysDataRetrieved = true;

      this.arUbrService.getAgingGovernance()
        .pipe(takeUntil(this._onDestroy)).pipe(takeUntil(this._repeatCall))
        .subscribe(async res => {

          this.arOriginalAgingDaysData = res;

          if (!this.isItemWiseUbrValuesRetrieved) {

            this.arUbrService.getItemWiseUbrValues()
              .pipe(takeUntil(this._onDestroy)).pipe(takeUntil(this._repeatCall))
              .subscribe(async res => {

                if (res["messType"] == "S" && res["data"]) {

                  this.isItemWiseUbrValuesRetrieved = true;

                  this.itemWiseUbrValues = res["data"];

                  this.itemWiseUbrValues = this.itemWiseUbrValues.reduce((previousValue, currentValue) =>
                    (previousValue[currentValue.item_id] = currentValue.ubr_value ? currentValue.ubr_value : 0, previousValue), {});

                  this.itemWiseUbrValuesOriginal = res["data"];

                  this.itemWiseUbrItemIds = _.uniq(_.pluck(this.itemWiseUbrValuesOriginal, 'item_id'));

                  this.resolveArAgingData();

                }

                else
                  this.utilityService.showToastMessage(res["messText"]);

              }, err => {
                this.showErrorMessage(err);
              });

          }

          else
            this.resolveArAgingData();

        }, err => {
          this.showErrorMessage(err);
        });

    }

    else
      this.resolveArAgingData();

  }

  resolveArAgingData() {

    this.arAgingDaysData = JSON.parse(JSON.stringify(this.arOriginalAgingDaysData));

    this.preResolvedArAgingDaysData = [];

    let wcdUserRolePlIds: any = [];

    let wcdUserRolePlIdsData = _.where(this.userRolePlIds, { object_id: 169 });

    if (wcdUserRolePlIdsData && wcdUserRolePlIdsData.length > 0)
      wcdUserRolePlIds = wcdUserRolePlIdsData[0].pl_ids;

    if (wcdUserRolePlIds != "ALL")
      if (wcdUserRolePlIds.length > 0)
        this.arAgingDaysData = _.filter(this.arAgingDaysData, function (arAgingDaysDataItem) {

          if ((arAgingDaysDataItem.pl_id && _.contains(wcdUserRolePlIds, arAgingDaysDataItem.pl_id)) ||
            (arAgingDaysDataItem.sub_pl_id && _.contains(wcdUserRolePlIds, arAgingDaysDataItem.sub_pl_id)) ||
            (arAgingDaysDataItem.vertical_id && _.contains(wcdUserRolePlIds, arAgingDaysDataItem.vertical_id)))
            return arAgingDaysDataItem;

        }, this);
      else
        this.arAgingDaysData = [];

    for (let udrfFilterConfigItem of this.udrfFilterConfig)
      if (udrfFilterConfigItem.arFilterIdBased && udrfFilterConfigItem.arFilterIdBased == "Y" && udrfFilterConfigItem.filterIds.length > 0)
        this.arAgingDaysData = _.filter(this.arAgingDaysData, function (sourceMISDataItem) {

          if (sourceMISDataItem[udrfFilterConfigItem.arFilterField] && _.contains(udrfFilterConfigItem.filterIds, sourceMISDataItem[udrfFilterConfigItem.arFilterField]))
            return sourceMISDataItem;

        }, this);

      else if (udrfFilterConfigItem.arFilterIdBased && udrfFilterConfigItem.arFilterIdBased == "N" && udrfFilterConfigItem.filterNames.length > 0)
        this.arAgingDaysData = _.filter(this.arAgingDaysData, function (sourceMISDataItem) {

          if (sourceMISDataItem[udrfFilterConfigItem.arFilterField] && _.contains(udrfFilterConfigItem.filterNames, sourceMISDataItem[udrfFilterConfigItem.arFilterField]))
            return sourceMISDataItem;

        }, this);

      else if (udrfFilterConfigItem.arFilterIdBased && udrfFilterConfigItem.arFilterIdBased == "K" && udrfFilterConfigItem.filterKeyValues.length > 0)
        this.arAgingDaysData = _.filter(this.arAgingDaysData, function (sourceMISDataItem) {

          if (sourceMISDataItem[udrfFilterConfigItem.arFilterField] && _.contains(udrfFilterConfigItem.filterKeyValues, sourceMISDataItem[udrfFilterConfigItem.arFilterField]))
            return sourceMISDataItem;

        }, this);

    for (let arAgingDaysDataItem of this.arAgingDaysData)
      if (arAgingDaysDataItem.customer_retention_amount && parseFloat(arAgingDaysDataItem.customer_retention_amount) > 0) {

        let balanceAr = arAgingDaysDataItem.ar_value - parseFloat(arAgingDaysDataItem.customer_retention_amount);

        if (balanceAr > 500)
          this.preResolvedArAgingDaysData.push({
            isGreaterThanCP15Days: !arAgingDaysDataItem.legal && arAgingDaysDataItem.aging_days > (arAgingDaysDataItem.CP + 15) ? true : false,
            isGreaterThanCP30Days: !arAgingDaysDataItem.legal && arAgingDaysDataItem.aging_days > (arAgingDaysDataItem.CP + 30) ? true : false,
            isGreaterThan30Days: !arAgingDaysDataItem.legal && arAgingDaysDataItem.aging_days > 30 && arAgingDaysDataItem.aging_days <= 60 ? true : false,
            isGreaterThan60Days: !arAgingDaysDataItem.legal && arAgingDaysDataItem.aging_days > 60 && arAgingDaysDataItem.aging_days <= 90 ? true : false,
            isGreaterThan90Days: !arAgingDaysDataItem.legal && arAgingDaysDataItem.aging_days > 90 && arAgingDaysDataItem.aging_days <= 120 ? true : false,
            isGreaterThan120Days: !arAgingDaysDataItem.legal && arAgingDaysDataItem.aging_days > 120 && arAgingDaysDataItem.aging_days <= 150 ? true : false,
            isGreaterThan150Days: !arAgingDaysDataItem.legal && arAgingDaysDataItem.aging_days > 150 && arAgingDaysDataItem.aging_days <= 180 ? true : false,
            isGreaterThan180Days: !arAgingDaysDataItem.legal && arAgingDaysDataItem.aging_days > 180 && arAgingDaysDataItem.aging_days <= 360 ? true : false,
            isGreaterThan360Days: !arAgingDaysDataItem.legal && arAgingDaysDataItem.aging_days > 360 ? true : false,
            isLegal: arAgingDaysDataItem.legal,
            isRetention: false,
            cp: arAgingDaysDataItem.CP,
            agingDays: arAgingDaysDataItem.aging_days,
            invoiceNumber: arAgingDaysDataItem.invoice_no,
            plannedCollectionDate: moment(arAgingDaysDataItem.planned_collection_date).format(this.dfDDMMMYYYY),
            plannedCollectionDateOriginal: arAgingDaysDataItem.planned_collection_date,
            arValue: balanceAr,
            arValueOriginal: balanceAr,
            arValueInitial: balanceAr,
            arValueMain: parseFloat(Number(balanceAr).toFixed(2)),
            ubrAmount: this.itemWiseUbrValues[arAgingDaysDataItem.item_id],
            ubrAmountOriginal: this.itemWiseUbrValues[arAgingDaysDataItem.item_id],
            ubrAmountMain: parseFloat(Number(this.itemWiseUbrValues[arAgingDaysDataItem.item_id]).toFixed(2)),
            plName: arAgingDaysDataItem.pl_name,
            plId: arAgingDaysDataItem.pl_id,
            customerId: arAgingDaysDataItem.customer_id,
            customerName: arAgingDaysDataItem.customer_name,
            projectName: arAgingDaysDataItem.project_name,
            projectId: arAgingDaysDataItem.project_id,
            legalEntity: arAgingDaysDataItem.sub_pl_name,
            verticalId: arAgingDaysDataItem.sub_pl_id,
            vertical: arAgingDaysDataItem.vertical,
            subPl: arAgingDaysDataItem.sub_pl,
            subPlId: arAgingDaysDataItem.sub_pl_id,
            pl: arAgingDaysDataItem.pl,
            projectItemName: arAgingDaysDataItem.item_name,
            milestoneName: arAgingDaysDataItem.milestone_name,
            itemId: arAgingDaysDataItem.item_id
          });

        this.preResolvedArAgingDaysData.push({
          isGreaterThanCP15Days: false,
          isGreaterThanCP30Days: false,
          isGreaterThan30Days: false,
          isGreaterThan60Days: false,
          isGreaterThan90Days: false,
          isGreaterThan120Days: false,
          isGreaterThan150Days: false,
          isGreaterThan180Days: false,
          isGreaterThan360Days: false,
          isLegal: false,
          isRetention: true,
          cp: arAgingDaysDataItem.CP,
          agingDays: arAgingDaysDataItem.aging_days,
          invoiceNumber: arAgingDaysDataItem.invoice_no,
          plannedCollectionDate: moment(arAgingDaysDataItem.planned_collection_date).format(this.dfDDMMMYYYY),
          plannedCollectionDateOriginal: arAgingDaysDataItem.planned_collection_date,
          arValue: arAgingDaysDataItem.customer_retention_amount,
          arValueOriginal: arAgingDaysDataItem.customer_retention_amount,
          arValueInitial: arAgingDaysDataItem.customer_retention_amount,
          arValueMain: parseFloat(Number(arAgingDaysDataItem.customer_retention_amount).toFixed(2)),
          ubrAmount: this.itemWiseUbrValues[arAgingDaysDataItem.item_id],
          ubrAmountOriginal: this.itemWiseUbrValues[arAgingDaysDataItem.item_id],
          ubrAmountMain: parseFloat(Number(this.itemWiseUbrValues[arAgingDaysDataItem.item_id]).toFixed(2)),
          plName: arAgingDaysDataItem.pl_name,
          plId: arAgingDaysDataItem.pl_id,
          customerId: arAgingDaysDataItem.customer_id,
          customerName: arAgingDaysDataItem.customer_name,
          projectName: arAgingDaysDataItem.project_name,
          projectId: arAgingDaysDataItem.project_id,
          legalEntity: arAgingDaysDataItem.sub_pl_name,
          verticalId: arAgingDaysDataItem.sub_pl_id,
          vertical: arAgingDaysDataItem.vertical,
          subPl: arAgingDaysDataItem.sub_pl,
          subPlId: arAgingDaysDataItem.sub_pl_id,
          pl: arAgingDaysDataItem.pl,
          projectItemName: arAgingDaysDataItem.item_name,
          milestoneName: arAgingDaysDataItem.milestone_name,
          itemId: arAgingDaysDataItem.item_id
        });

      }

      else {

        this.preResolvedArAgingDaysData.push({
          isGreaterThanCP15Days: !arAgingDaysDataItem.legal && arAgingDaysDataItem.aging_days > (arAgingDaysDataItem.CP + 15) ? true : false,
          isGreaterThanCP30Days: !arAgingDaysDataItem.legal && arAgingDaysDataItem.aging_days > (arAgingDaysDataItem.CP + 30) ? true : false,
          isGreaterThan30Days: !arAgingDaysDataItem.legal && arAgingDaysDataItem.aging_days > 30 && arAgingDaysDataItem.aging_days <= 60 ? true : false,
          isGreaterThan60Days: !arAgingDaysDataItem.legal && arAgingDaysDataItem.aging_days > 60 && arAgingDaysDataItem.aging_days <= 90 ? true : false,
          isGreaterThan90Days: !arAgingDaysDataItem.legal && arAgingDaysDataItem.aging_days > 90 && arAgingDaysDataItem.aging_days <= 120 ? true : false,
          isGreaterThan120Days: !arAgingDaysDataItem.legal && arAgingDaysDataItem.aging_days > 120 && arAgingDaysDataItem.aging_days <= 150 ? true : false,
          isGreaterThan150Days: !arAgingDaysDataItem.legal && arAgingDaysDataItem.aging_days > 150 && arAgingDaysDataItem.aging_days <= 180 ? true : false,
          isGreaterThan180Days: !arAgingDaysDataItem.legal && arAgingDaysDataItem.aging_days > 180 && arAgingDaysDataItem.aging_days <= 360 ? true : false,
          isGreaterThan360Days: !arAgingDaysDataItem.legal && arAgingDaysDataItem.aging_days > 360 ? true : false,
          isLegal: arAgingDaysDataItem.legal,
          isRetention: false,
          cp: arAgingDaysDataItem.CP,
          agingDays: arAgingDaysDataItem.aging_days,
          invoiceNumber: arAgingDaysDataItem.invoice_no,
          plannedCollectionDate: moment(arAgingDaysDataItem.planned_collection_date).format(this.dfDDMMMYYYY),
          plannedCollectionDateOriginal: arAgingDaysDataItem.planned_collection_date,
          arValue: arAgingDaysDataItem.ar_value,
          arValueOriginal: arAgingDaysDataItem.ar_value,
          arValueInitial: arAgingDaysDataItem.ar_value,
          arValueMain: parseFloat(Number(arAgingDaysDataItem.ar_value).toFixed(2)),
          ubrAmount: this.itemWiseUbrValues[arAgingDaysDataItem.item_id],
          ubrAmountOriginal: this.itemWiseUbrValues[arAgingDaysDataItem.item_id],
          ubrAmountMain: parseFloat(Number(this.itemWiseUbrValues[arAgingDaysDataItem.item_id]).toFixed(2)),
          plName: arAgingDaysDataItem.pl_name,
          plId: arAgingDaysDataItem.pl_id,
          customerId: arAgingDaysDataItem.customer_id,
          customerName: arAgingDaysDataItem.customer_name,
          projectName: arAgingDaysDataItem.project_name,
          projectId: arAgingDaysDataItem.project_id,
          legalEntity: arAgingDaysDataItem.sub_pl_name,
          verticalId: arAgingDaysDataItem.sub_pl_id,
          vertical: arAgingDaysDataItem.vertical,
          subPl: arAgingDaysDataItem.sub_pl,
          subPlId: arAgingDaysDataItem.sub_pl_id,
          pl: arAgingDaysDataItem.pl,
          projectItemName: arAgingDaysDataItem.item_name,
          milestoneName: arAgingDaysDataItem.milestone_name,
          itemId: arAgingDaysDataItem.item_id
        });

      }

    let arAgingDaysDataItemIds = _.uniq(_.pluck(this.arAgingDaysData, 'item_id'));

    let ubrItemIdsWithoutAr = _.difference(this.itemWiseUbrItemIds, arAgingDaysDataItemIds);

    let ubrValuesWithoutAr = _.filter(this.itemWiseUbrValuesOriginal, function (itemWiseUbrValuesOriginalItem) {

      if (_.contains(ubrItemIdsWithoutAr, itemWiseUbrValuesOriginalItem.item_id))
        return itemWiseUbrValuesOriginalItem;

    });

    if (wcdUserRolePlIds != "ALL")
      if (wcdUserRolePlIds.length > 0)
        ubrValuesWithoutAr = _.filter(ubrValuesWithoutAr, function (ubrValueWithoutAr) {

          if ((ubrValueWithoutAr.pl_id && _.contains(wcdUserRolePlIds, ubrValueWithoutAr.pl_id)) ||
            (ubrValueWithoutAr.sub_pl_id && _.contains(wcdUserRolePlIds, ubrValueWithoutAr.sub_pl_id)) ||
            (ubrValueWithoutAr.vertical_id && _.contains(wcdUserRolePlIds, ubrValueWithoutAr.vertical_id)))
            return ubrValueWithoutAr;

        }, this);
      else
        ubrValuesWithoutAr = [];

    for (let udrfFilterConfigItem of this.udrfFilterConfig)
      if (udrfFilterConfigItem.arFilterIdBased && udrfFilterConfigItem.arFilterIdBased == "Y" && udrfFilterConfigItem.filterIds.length > 0)
        ubrValuesWithoutAr = _.filter(ubrValuesWithoutAr, function (ubrValueWithoutAr) {

          if (ubrValueWithoutAr[udrfFilterConfigItem.arFilterField] && _.contains(udrfFilterConfigItem.filterIds, ubrValueWithoutAr[udrfFilterConfigItem.arFilterField]))
            return ubrValueWithoutAr;

        }, this);

      else if (udrfFilterConfigItem.arFilterIdBased && udrfFilterConfigItem.arFilterIdBased == "N" && udrfFilterConfigItem.filterNames.length > 0)
        ubrValuesWithoutAr = _.filter(ubrValuesWithoutAr, function (ubrValueWithoutAr) {

          if (ubrValueWithoutAr[udrfFilterConfigItem.arFilterField] && _.contains(udrfFilterConfigItem.filterNames, ubrValueWithoutAr[udrfFilterConfigItem.arFilterField]))
            return ubrValueWithoutAr;

        }, this);

      else if (udrfFilterConfigItem.arFilterIdBased && udrfFilterConfigItem.arFilterIdBased == "K" && udrfFilterConfigItem.filterKeyValues.length > 0)
        ubrValuesWithoutAr = _.filter(ubrValuesWithoutAr, function (ubrValueWithoutAr) {

          if (ubrValueWithoutAr[udrfFilterConfigItem.arFilterField] && _.contains(udrfFilterConfigItem.filterKeyValues, ubrValueWithoutAr[udrfFilterConfigItem.arFilterField]))
            return ubrValueWithoutAr;

        }, this);

    for (let ubrValueWithoutAr of ubrValuesWithoutAr)
      this.preResolvedArAgingDaysData.push({
        isGreaterThanCP15Days: false,
        isGreaterThanCP30Days: false,
        isGreaterThan30Days: false,
        isGreaterThan60Days: false,
        isGreaterThan90Days: false,
        isGreaterThan120Days: false,
        isGreaterThan150Days: false,
        isGreaterThan180Days: false,
        isGreaterThan360Days: false,
        isLegal: false,
        isRetention: false,
        cp: 0,
        agingDays: 0,
        invoiceNumber: "",
        plannedCollectionDate: "",
        plannedCollectionDateOriginal: "",
        arValue: 0,
        arValueOriginal: 0,
        arValueInitial: 0,
        arValueMain: 0,
        ubrAmount: ubrValueWithoutAr.ubr_value ? ubrValueWithoutAr.ubr_value : 0,
        ubrAmountOriginal: ubrValueWithoutAr.ubr_value ? ubrValueWithoutAr.ubr_value : 0,
        ubrAmountMain: parseFloat(Number(ubrValueWithoutAr.ubr_value ? ubrValueWithoutAr.ubr_value : 0).toFixed(2)),
        plName: ubrValueWithoutAr.sub_pl,
        plId: ubrValueWithoutAr.pl_id,
        customerId: ubrValueWithoutAr.customer_id,
        customerName: ubrValueWithoutAr.customer_name,
        projectName: ubrValueWithoutAr.project_name,
        projectId: ubrValueWithoutAr.project_id,
        legalEntity: ubrValueWithoutAr.sub_pl_name,
        verticalId: ubrValueWithoutAr.sub_pl_id,
        vertical: ubrValueWithoutAr.vertical,
        subPl: ubrValueWithoutAr.sub_pl,
        subPlId: ubrValueWithoutAr.sub_pl_id,
        pl: ubrValueWithoutAr.pl,
        projectItemName: ubrValueWithoutAr.item_name,
        milestoneName: ubrValueWithoutAr.item_name,
        itemId: ubrValueWithoutAr.item_id
      });

    if (this.baseLegalEntityLevel != 0)
      this.preResolvedArAgingDaysData = _.filter(this.preResolvedArAgingDaysData, function (resolvedArAgingDaysDataItem) {

        if (this.baseLegalEntityLevel == 1 && ((this.reportMode == "Normal Mode" && this.normalModeCBOWBaseLegalEntity ==
          resolvedArAgingDaysDataItem.pl) || (this.reportMode == "Detailed Mode" && this.detailedModeBaseLegalEntity ==
            resolvedArAgingDaysDataItem.pl)))
          return resolvedArAgingDaysDataItem;

        else if (this.baseLegalEntityLevel == 2 && ((this.reportMode == "Normal Mode" && this.normalModeCBOWBaseLegalEntity ==
          resolvedArAgingDaysDataItem.subPl) || (this.reportMode == "Detailed Mode" && this.detailedModeBaseLegalEntity ==
            resolvedArAgingDaysDataItem.subPl)))
          return resolvedArAgingDaysDataItem;

        else if (this.baseLegalEntityLevel == 3 && ((this.reportMode == "Normal Mode" && this.normalModeCBOWBaseLegalEntity ==
          resolvedArAgingDaysDataItem.vertical) || (this.reportMode == "Detailed Mode" && this.detailedModeBaseLegalEntity ==
            resolvedArAgingDaysDataItem.vertical)))
          return resolvedArAgingDaysDataItem;

      }, this);

    let legalItems = _.where(this.preResolvedArAgingDaysData, { isLegal: true });

    let legalItemIds = _.uniq(_.pluck(legalItems, 'itemId'));

    let retentionItems = _.where(this.preResolvedArAgingDaysData, { isRetention: true });

    let retentionItemIds = _.uniq(_.pluck(retentionItems, 'itemId'));

    this.resolvedArAgingDaysData = _.filter(this.preResolvedArAgingDaysData, function (resolvedArAgingDaysDataItem) {

      if ((this.reportMode == "Normal Mode" && this.normalModeArAgingInterval == 30888 &&
        resolvedArAgingDaysDataItem.isGreaterThanCP15Days) || (this.reportMode == "Detailed Mode" &&
          this.detailedModeArAgingInterval == 30888 && resolvedArAgingDaysDataItem.isGreaterThanCP15Days))
        return resolvedArAgingDaysDataItem;

      else if ((this.reportMode == "Normal Mode" && this.normalModeArAgingInterval == 30999 &&
        resolvedArAgingDaysDataItem.isGreaterThanCP30Days) || (this.reportMode == "Detailed Mode" &&
          this.detailedModeArAgingInterval == 30999 && resolvedArAgingDaysDataItem.isGreaterThanCP30Days))
        return resolvedArAgingDaysDataItem;

      else if ((this.reportMode == "Normal Mode" && this.normalModeArAgingInterval == 30 &&
        resolvedArAgingDaysDataItem.isGreaterThan30Days) || (this.reportMode == "Detailed Mode" &&
          this.detailedModeArAgingInterval == 30 && resolvedArAgingDaysDataItem.isGreaterThan30Days))
        return resolvedArAgingDaysDataItem;

      else if ((this.reportMode == "Normal Mode" && this.normalModeArAgingInterval == 60 &&
        resolvedArAgingDaysDataItem.isGreaterThan60Days) || (this.reportMode == "Detailed Mode" &&
          this.detailedModeArAgingInterval == 60 && resolvedArAgingDaysDataItem.isGreaterThan60Days))
        return resolvedArAgingDaysDataItem;

      else if ((this.reportMode == "Normal Mode" && this.normalModeArAgingInterval == 90 &&
        resolvedArAgingDaysDataItem.isGreaterThan90Days) || (this.reportMode == "Detailed Mode" &&
          this.detailedModeArAgingInterval == 90 && resolvedArAgingDaysDataItem.isGreaterThan90Days))
        return resolvedArAgingDaysDataItem;

      else if ((this.reportMode == "Normal Mode" && this.normalModeArAgingInterval == 120 &&
        resolvedArAgingDaysDataItem.isGreaterThan120Days) || (this.reportMode == "Detailed Mode" &&
          this.detailedModeArAgingInterval == 120 && resolvedArAgingDaysDataItem.isGreaterThan120Days))
        return resolvedArAgingDaysDataItem;

      else if ((this.reportMode == "Normal Mode" && this.normalModeArAgingInterval == 150 &&
        resolvedArAgingDaysDataItem.isGreaterThan150Days) || (this.reportMode == "Detailed Mode" &&
          this.detailedModeArAgingInterval == 150 && resolvedArAgingDaysDataItem.isGreaterThan150Days))
        return resolvedArAgingDaysDataItem;

      else if ((this.reportMode == "Normal Mode" && this.normalModeArAgingInterval == 180 &&
        resolvedArAgingDaysDataItem.isGreaterThan180Days) || (this.reportMode == "Detailed Mode" &&
          this.detailedModeArAgingInterval == 180 && resolvedArAgingDaysDataItem.isGreaterThan180Days))
        return resolvedArAgingDaysDataItem;

      else if ((this.reportMode == "Normal Mode" && this.normalModeArAgingInterval == 360 &&
        resolvedArAgingDaysDataItem.isGreaterThan360Days) || (this.reportMode == "Detailed Mode" &&
          this.detailedModeArAgingInterval == 360 && resolvedArAgingDaysDataItem.isGreaterThan360Days))
        return resolvedArAgingDaysDataItem;

      else if ((this.reportMode == "Normal Mode" && this.normalModeArAgingInterval == 50999 &&
        resolvedArAgingDaysDataItem.isRetention) || (this.reportMode == "Detailed Mode" &&
          this.detailedModeArAgingInterval == 50999 && resolvedArAgingDaysDataItem.isRetention))
        return resolvedArAgingDaysDataItem;

      else if ((this.reportMode == "Normal Mode" && this.normalModeArAgingInterval == 40999 &&
        resolvedArAgingDaysDataItem.isLegal) || (this.reportMode == "Detailed Mode" &&
          this.detailedModeArAgingInterval == 40999 && resolvedArAgingDaysDataItem.isLegal))
        return resolvedArAgingDaysDataItem;

      else if ((this.reportMode == "Normal Mode" && this.normalModeArAgingInterval == 0 && !resolvedArAgingDaysDataItem.isLegal &&
        !resolvedArAgingDaysDataItem.isRetention) || (this.reportMode == "Detailed Mode" && this.detailedModeArAgingInterval == 0 &&
          !resolvedArAgingDaysDataItem.isLegal && !resolvedArAgingDaysDataItem.isRetention))
        return resolvedArAgingDaysDataItem;

    }, this);

    let filteredItemWiseUbrValuesOriginal = _.filter(this.itemWiseUbrValuesOriginal, function (itemWiseUbrValuesOriginalItem) {

      if ((this.reportMode == "Normal Mode" && this.normalModeArAgingInterval == 40999 &&
        _.contains(legalItemIds, itemWiseUbrValuesOriginalItem.item_id)) || (this.reportMode == "Detailed Mode" &&
          this.detailedModeArAgingInterval == 40999 && _.contains(legalItemIds, itemWiseUbrValuesOriginalItem.item_id)))
        return itemWiseUbrValuesOriginalItem;

      else if ((this.reportMode == "Normal Mode" && this.normalModeArAgingInterval == 50999 &&
        _.contains(retentionItemIds, itemWiseUbrValuesOriginalItem.item_id)) || (this.reportMode == "Detailed Mode" &&
          this.detailedModeArAgingInterval == 50999 && _.contains(retentionItemIds, itemWiseUbrValuesOriginalItem.item_id)))
        return itemWiseUbrValuesOriginalItem;

      else if (!_.contains(legalItemIds, itemWiseUbrValuesOriginalItem.item_id) && !_.contains(retentionItemIds, itemWiseUbrValuesOriginalItem.item_id))
        return itemWiseUbrValuesOriginalItem;

    }, this);

    this.resolvedArAgingDaysData = this.governanceReportService.resolveArAgingDataFilter(this.udrfService.udrfData, this.resolvedArAgingDaysData, this.misFlags, filteredItemWiseUbrValuesOriginal);

    this.isResolvedArDataLoading = false;

    this.retrieveCBOData();

  }

  isProjectsRetrievedFirstTime = false;

  pmoDbDataStartDate = "";
  pmoDbDataEndDate = "";

  projectsUserRolePlIds: any;

  multiOptionSelectSearchValuesForPmoDbPlFilter = [];

  areNonMilestonePmoDbColumnsVisible = false;

  async retrieveProjectsData() {

    // for (let pmoDataKey of this.pmoDb.pmoDataKeys)
    //   pmoDataKey.isVisibleInWidget = false;

    // for (let pmoDbVisibleColumn of this.udrfService.udrfData.appliedConfig["customFields"]["projectVisibleColumns"]) {

    //   let currentPmoDataKeys = _.where(this.pmoDb.pmoDataKeys, { description: pmoDbVisibleColumn });

    //   if (currentPmoDataKeys && currentPmoDataKeys.length > 0)
    //     currentPmoDataKeys[0].isVisibleInWidget = true;

    // }

    this.areNonMilestonePmoDbColumnsVisible = false;

    this.pmoDb.visiblePmoDataKeys = _.where(this.pmoDb.pmoDataKeys, { isVisibleInWidget: true });


    if (this.udrfService.udrfData.appliedConfig["customFields"]["projectsWidgetSize"] == "F")
      this.projectsWidgetColSize = "col-11-5-pmo";

    else if (this.udrfService.udrfData.appliedConfig["customFields"]["projectsWidgetSize"] == "H")
      this.projectsWidgetColSize = "col-5-5";

    if (this.udrfService.udrfData.appliedConfig["customFields"]["isFullValue"]) {

      this.projectsColClass = "col-5-px";
      this.projectsColItemClass = "col-5-px-item";
      this.projectsColSpilloverClass = "col-5-px-spillover";

    }

    else {

      this.projectsColClass = "col-2-px";
      this.projectsColItemClass = "col-2-px-item";
      this.projectsColSpilloverClass = "col-2-px-spillover";

    }

    this.pmoDb.isPmoDbDataLoading = true;

    let projectsUserRolePlIds = _.where(this.userRolePlIds, { object_id: 29365 });

    if (projectsUserRolePlIds != null && projectsUserRolePlIds.length > 0)
      this.projectsUserRolePlIds = projectsUserRolePlIds[0].pl_ids;

    if (!this.pmoDb.isProjectsDataRetrieved) {

      this.pmoDb.pmoDbDataMonthInterval = "M0";
      this.pmoDb.pmoDbDataMonthIntervalCount = 0;
      this.pmoDb.pmoDbDataMonthCount = moment().month();

      let currentIterationDate = JSON.parse(JSON.stringify(moment().startOf('month').format(this.dateFormat)));

      this.pmoDbDataStartDate = JSON.parse(JSON.stringify(moment().startOf('month').format(this.dateFormat)));

      this.pmoDbDataEndDate = moment().add(5, 'month').endOf('month').format(this.dateFormat);

      this.pmoDb.monthIntervals = [];

      this.pmoDb.weekIntervals = [];

      this.pmoDb.currentWeekIntervals = [];

      let monthCount = 0, currentMonth = null, weekCount = 0;

      let currentWeekItem;

      while (moment(currentIterationDate).isSameOrBefore(this.pmoDbDataEndDate)) {

        let checkMonth = moment(currentIterationDate).month();

        if (currentMonth == null || currentMonth != checkMonth) {

          currentMonth = checkMonth;

          this.pmoDb.monthIntervals.push({
            monthInterval: "M" + monthCount,
            monthCount: currentMonth,
            monthIntervalText: moment().add(monthCount, 'month').startOf('month').format("MMM") + " '" + moment().add(monthCount, 'month').startOf('month').format("YY"),
            weekIntervals: []
          });

          weekCount = 0;
          monthCount++;

        }

        let currentWeekString = "W" + moment(currentIterationDate).week();

        let weekStartDate = moment(currentIterationDate).startOf('week');
        let monthStartDate = moment(currentIterationDate).startOf('month');

        let startDate = moment(weekStartDate).isSameOrBefore(monthStartDate, 'day') ? monthStartDate : weekStartDate;

        let weekEndDate = moment(currentIterationDate).endOf('week');
        let monthEndDate = moment(currentIterationDate).endOf('month');

        let endDate = moment(weekEndDate).isSameOrAfter(monthEndDate, 'day') ? monthEndDate : weekEndDate;

        if (weekCount == 0 && moment(startDate).isAfter(monthStartDate, 'day')) {

          let excessEndDate = moment(startDate).subtract(1, 'day');
          let excessStartDate = monthStartDate;

          let currentWeekTempString = "W" + (moment(currentIterationDate).week() - 1);

          let currentWeekTempItem = {
            weekInterval: currentWeekTempString,
            monthCount: currentMonth,
            startDate: excessStartDate.format("DD-MM-YYYY"),
            endDate: excessEndDate.format("DD-MM-YYYY")
          };

          this.insertWeekIntoMonth(monthCount, currentMonth, currentWeekTempItem);

        }

        currentWeekItem = {
          weekInterval: currentWeekString,
          monthCount: currentMonth,
          startDate: startDate.format("DD-MM-YYYY"),
          endDate: endDate.format("DD-MM-YYYY")
        };

        this.insertWeekIntoMonth(monthCount, currentMonth, currentWeekItem);

        weekCount++;
        currentIterationDate = moment(currentIterationDate).add(7, 'd');

        if (moment(endDate).isBefore(monthEndDate, 'day') && moment(currentIterationDate).isAfter(monthEndDate, 'day')) {

          let excessEndDate = monthEndDate;
          let excessStartDate = moment(endDate).add(1, 'day');

          let currentWeekTempString = "W" + moment(currentIterationDate).week();

          let currentWeekTempItem = {
            weekInterval: currentWeekTempString,
            monthCount: currentMonth,
            startDate: excessStartDate.format("DD-MM-YYYY"),
            endDate: excessEndDate.format("DD-MM-YYYY")
          };

          this.insertWeekIntoMonth(monthCount, currentMonth, currentWeekTempItem);

        }

      }

      this.pmoDbDataEndDate = moment(currentWeekItem.endDate, "DD-MM-YYYY").format(this.dateFormat);

      if (!this.isProjectsRetrievedFirstTime) {

        for (let j = 0; j < 4; j++)
          for (let k = 0; k < this.pmoDb.pmoDataKeys.length; k++) {

            this.governanceReportService.sortOrdersDm["ProjectsDm" + j][this.pmoDb.pmoDataKeys[k].overdueDataKey + "Spillover" + (this.pmoDb.pmoDataKeys[k].overdueDataKey.includes("_value") ? "Original" : "")] = "N";

            this.governanceReportService.sortOrders["ProjectsPu" + j].push({
              sortField: this.pmoDb.pmoDataKeys[k].overdueDataKey + "Spillover" + (this.pmoDb.pmoDataKeys[k].overdueDataKey.includes("_value") ? "Original" : ""),
              sortOrder: "N"
            });

            if (this.pmoDb.pmoDataKeys[k].visibleTextExtra != null) {

              this.governanceReportService.sortOrdersDm["ProjectsDm" + j][this.pmoDb.pmoDataKeys[k].overdueDataKeyExtra + "Spillover" + (this.pmoDb.pmoDataKeys[k].overdueDataKeyExtra.includes("_value") ? "Original" : "")] = "N";

              this.governanceReportService.sortOrders["ProjectsPu" + j].push({
                sortField: this.pmoDb.pmoDataKeys[k].overdueDataKeyExtra + "Spillover" + (this.pmoDb.pmoDataKeys[k].overdueDataKeyExtra.includes("_value") ? "Original" : ""),
                sortOrder: "N"
              });

            }

            for (let i = 0; i < this.pmoDb.weekIntervals.length; i++) {

              this.governanceReportService.sortOrdersDm["ProjectsDm" + j][this.pmoDb.pmoDataKeys[k].actualDataKey + this.pmoDb.weekIntervals[i].weekInterval + this.pmoDb.weekIntervals[i].monthCount + (this.pmoDb.pmoDataKeys[k].actualDataKey.includes("_value") ? "Original" : "")] = "N";
              this.governanceReportService.sortOrdersDm["ProjectsDm" + j][this.pmoDb.pmoDataKeys[k].plannedDataKey + this.pmoDb.weekIntervals[i].weekInterval + this.pmoDb.weekIntervals[i].monthCount + (this.pmoDb.pmoDataKeys[k].plannedDataKey.includes("_value") ? "Original" : "")] = "N";
              this.governanceReportService.sortOrdersDm["ProjectsDm" + j][this.pmoDb.pmoDataKeys[k].overdueDataKey + this.pmoDb.weekIntervals[i].weekInterval + this.pmoDb.weekIntervals[i].monthCount + (this.pmoDb.pmoDataKeys[k].overdueDataKey.includes("_value") ? "Original" : "")] = "N";

              if (this.pmoDb.pmoDataKeys[k].visibleTextExtra != null) {

                this.governanceReportService.sortOrdersDm["ProjectsDm" + j][this.pmoDb.pmoDataKeys[k].actualDataKeyExtra + this.pmoDb.weekIntervals[i].weekInterval + this.pmoDb.weekIntervals[i].monthCount + (this.pmoDb.pmoDataKeys[k].actualDataKeyExtra.includes("_value") ? "Original" : "")] = "N";
                this.governanceReportService.sortOrdersDm["ProjectsDm" + j][this.pmoDb.pmoDataKeys[k].plannedDataKeyExtra + this.pmoDb.weekIntervals[i].weekInterval + this.pmoDb.weekIntervals[i].monthCount + (this.pmoDb.pmoDataKeys[k].plannedDataKeyExtra.includes("_value") ? "Original" : "")] = "N";
                this.governanceReportService.sortOrdersDm["ProjectsDm" + j][this.pmoDb.pmoDataKeys[k].overdueDataKeyExtra + this.pmoDb.weekIntervals[i].weekInterval + this.pmoDb.weekIntervals[i].monthCount + (this.pmoDb.pmoDataKeys[k].overdueDataKeyExtra.includes("_value") ? "Original" : "")] = "N";

              }

              this.governanceReportService.sortOrders["ProjectsPu" + j].push({
                sortField: this.pmoDb.pmoDataKeys[k].actualDataKey + this.pmoDb.weekIntervals[i].weekInterval + this.pmoDb.weekIntervals[i].monthCount + (this.pmoDb.pmoDataKeys[k].actualDataKey.includes("_value") ? "Original" : ""),
                sortOrder: "N"
              });

              this.governanceReportService.sortOrders["ProjectsPu" + j].push({
                sortField: this.pmoDb.pmoDataKeys[k].plannedDataKey + this.pmoDb.weekIntervals[i].weekInterval + this.pmoDb.weekIntervals[i].monthCount + (this.pmoDb.pmoDataKeys[k].plannedDataKey.includes("_value") ? "Original" : ""),
                sortOrder: "N"
              });

              this.governanceReportService.sortOrders["ProjectsPu" + j].push({
                sortField: this.pmoDb.pmoDataKeys[k].overdueDataKey + this.pmoDb.weekIntervals[i].weekInterval + this.pmoDb.weekIntervals[i].monthCount + (this.pmoDb.pmoDataKeys[k].overdueDataKey.includes("_value") ? "Original" : ""),
                sortOrder: "N"
              });

              if (this.pmoDb.pmoDataKeys[k].visibleTextExtra != null) {

                this.governanceReportService.sortOrders["ProjectsPu" + j].push({
                  sortField: this.pmoDb.pmoDataKeys[k].actualDataKeyExtra + this.pmoDb.weekIntervals[i].weekInterval + this.pmoDb.weekIntervals[i].monthCount + (this.pmoDb.pmoDataKeys[k].actualDataKeyExtra.includes("_value") ? "Original" : ""),
                  sortOrder: "N"
                });

                this.governanceReportService.sortOrders["ProjectsPu" + j].push({
                  sortField: this.pmoDb.pmoDataKeys[k].plannedDataKeyExtra + this.pmoDb.weekIntervals[i].weekInterval + this.pmoDb.weekIntervals[i].monthCount + (this.pmoDb.pmoDataKeys[k].plannedDataKeyExtra.includes("_value") ? "Original" : ""),
                  sortOrder: "N"
                });

                this.governanceReportService.sortOrders["ProjectsPu" + j].push({
                  sortField: this.pmoDb.pmoDataKeys[k].overdueDataKeyExtra + this.pmoDb.weekIntervals[i].weekInterval + this.pmoDb.weekIntervals[i].monthCount + (this.pmoDb.pmoDataKeys[k].overdueDataKeyExtra.includes("_value") ? "Original" : ""),
                  sortOrder: "N"
                });

              }

            }

            this.governanceReportService.sortOrdersDm["ProjectsDm" + j][this.pmoDb.pmoDataKeys[k].actualDataKey + "Pu" + (this.pmoDb.pmoDataKeys[k].actualDataKey.includes("_value") ? "Original" : "")] = "N";
            this.governanceReportService.sortOrdersDm["ProjectsDm" + j][this.pmoDb.pmoDataKeys[k].plannedDataKey + "Pu" + (this.pmoDb.pmoDataKeys[k].plannedDataKey.includes("_value") ? "Original" : "")] = "N";
            this.governanceReportService.sortOrdersDm["ProjectsDm" + j][this.pmoDb.pmoDataKeys[k].overdueDataKey + "Pu" + (this.pmoDb.pmoDataKeys[k].overdueDataKey.includes("_value") ? "Original" : "")] = "N";

            if (this.pmoDb.pmoDataKeys[k].visibleTextExtra != null) {

              this.governanceReportService.sortOrdersDm["ProjectsDm" + j][this.pmoDb.pmoDataKeys[k].actualDataKeyExtra + "Pu" + (this.pmoDb.pmoDataKeys[k].actualDataKeyExtra.includes("_value") ? "Original" : "")] = "N";
              this.governanceReportService.sortOrdersDm["ProjectsDm" + j][this.pmoDb.pmoDataKeys[k].plannedDataKeyExtra + "Pu" + (this.pmoDb.pmoDataKeys[k].plannedDataKeyExtra.includes("_value") ? "Original" : "")] = "N";
              this.governanceReportService.sortOrdersDm["ProjectsDm" + j][this.pmoDb.pmoDataKeys[k].overdueDataKeyExtra + "Pu" + (this.pmoDb.pmoDataKeys[k].overdueDataKeyExtra.includes("_value") ? "Original" : "")] = "N";

            }

            this.governanceReportService.sortOrders["ProjectsPu" + j].push({
              sortField: this.pmoDb.pmoDataKeys[k].actualDataKey + "Pu" + (this.pmoDb.pmoDataKeys[k].actualDataKey.includes("_value") ? "Original" : ""),
              sortOrder: "N"
            });

            this.governanceReportService.sortOrders["ProjectsPu" + j].push({
              sortField: this.pmoDb.pmoDataKeys[k].plannedDataKey + "Pu" + (this.pmoDb.pmoDataKeys[k].plannedDataKey.includes("_value") ? "Original" : ""),
              sortOrder: "N"
            });

            this.governanceReportService.sortOrders["ProjectsPu" + j].push({
              sortField: this.pmoDb.pmoDataKeys[k].overdueDataKey + "Pu" + (this.pmoDb.pmoDataKeys[k].overdueDataKey.includes("_value") ? "Original" : ""),
              sortOrder: "N"
            });

            if (this.pmoDb.pmoDataKeys[k].visibleTextExtra != null) {

              this.governanceReportService.sortOrders["ProjectsPu" + j].push({
                sortField: this.pmoDb.pmoDataKeys[k].actualDataKeyExtra + "Pu" + (this.pmoDb.pmoDataKeys[k].actualDataKeyExtra.includes("_value") ? "Original" : ""),
                sortOrder: "N"
              });

              this.governanceReportService.sortOrders["ProjectsPu" + j].push({
                sortField: this.pmoDb.pmoDataKeys[k].plannedDataKeyExtra + "Pu" + (this.pmoDb.pmoDataKeys[k].plannedDataKeyExtra.includes("_value") ? "Original" : ""),
                sortOrder: "N"
              });

              this.governanceReportService.sortOrders["ProjectsPu" + j].push({
                sortField: this.pmoDb.pmoDataKeys[k].overdueDataKeyExtra + "Pu" + (this.pmoDb.pmoDataKeys[k].overdueDataKeyExtra.includes("_value") ? "Original" : ""),
                sortOrder: "N"
              });

            }

          }

        this.isProjectsRetrievedFirstTime = true;

      }

      this.multiOptionSelectSearchValuesForPmoDbPlFilter = [];

      if (this.projectsUserRolePlIds != null && this.projectsUserRolePlIds != "ALL") {

        for (let projectsUserRolePlId of this.projectsUserRolePlIds) {

          let plIdData = _.where(this.legalEntitiesData, { plId: projectsUserRolePlId });

          if (plIdData != null && plIdData.length > 0)
            this.multiOptionSelectSearchValuesForPmoDbPlFilter.push(plIdData[0].description);

          for (let legalEntitiesDataItem of this.legalEntitiesData) {

            let doesContainPl = _.where(legalEntitiesDataItem.rollupLevels, { rollUpPlId: projectsUserRolePlId });

            if (doesContainPl != null && doesContainPl.length > 0)
              this.multiOptionSelectSearchValuesForPmoDbPlFilter.push(legalEntitiesDataItem.description);

          }

        }

      }

      this.pmoDb.milestonePmoDbData = [];

      if (this.projectsUserRolePlIds != null && (this.projectsUserRolePlIds == "ALL" || this.projectsUserRolePlIds.length > 0)) {

        // let udrfData = JSON.parse(JSON.stringify(this.udrfService.udrfData));

        // udrfData["mainApiDateRangeStart"] = this.pmoDbDataStartDate;
        // udrfData["mainApiDateRangeEnd"] = this.pmoDbDataEndDate;

        // if (udrfData["mainFilterArray"] == null)
        //   udrfData["mainFilterArray"] = [];

        // udrfData["mainFilterArray"] = _.filter(udrfData["mainFilterArray"], function (mainFilterArrayItem) {

        //   if (mainFilterArrayItem.filterId != 2 || (mainFilterArrayItem.filterId == 2 && mainFilterArrayItem.filterName == "SBU"))
        //     return mainFilterArrayItem;

        // }, this);

        // let plFilter = _.where(udrfData["mainFilterArray"], { filterId: 2 });

        // if (plFilter != null && plFilter.length > 0)
        //   plFilter[0].multiOptionSelectSearchValues = this.multiOptionSelectSearchValuesForPmoDbPlFilter;

        // else
        //   udrfData["mainFilterArray"].push({
        //     filterColumnAlias: "TPI",
        //     filterColumnName: "p_and_l_description",
        //     filterId: 2,
        //     multiOptionSelectSearchValues: this.multiOptionSelectSearchValuesForPmoDbPlFilter,
        //     isFilterRange: false,
        //     isMainDateRangeFilter: false,
        //     shouldMasterTableBeJoined: false
        //   });

        // let monthWeekList = this.pmoDb.monthIntervals;

        let apiConfig = {
          start_date: this.pmoDbDataStartDate,
          end_date: this.pmoDbDataEndDate,
          defaultCurrency: this.udrfService.udrfData.appliedConfig["customFields"]["defaultCurrency"],
          tab_type: "bu"
        }

        this.detailedViewService.getNGRPMODashboardCount(apiConfig)
          .pipe(takeUntil(this._onDestroy)).pipe(takeUntil(this._repeatCall))
          .subscribe(async res => {

            if (res && res["messType"] == "S") {

              if (res["data"] && res["data"].length > 0)
                this.pmoDb.milestonePmoDbData = res["data"];

              if (res["colList"] && res["colList"].length > 0)
                for (let pmoDataKey of this.pmoDb.pmoDataKeys) {

                  let currentGanttTypeData = _.filter(res["colList"], function (colListItem) {

                    if (colListItem.keyName != null && _.contains(pmoDataKey.ganttTypeKeys, colListItem.keyName))
                      return colListItem;

                  }, this);

                  pmoDataKey.ganttTypeId = 0;

                  if (currentGanttTypeData != null && currentGanttTypeData.length > 0 && currentGanttTypeData[0].gantt_type_id != null)
                    pmoDataKey.ganttTypeId = currentGanttTypeData[0].gantt_type_id;

                }

              let overall_planned_value = res["planned_value"];
              let overall_actual_value = res["actual_value"];
              let overall_overdue_value = res["overdue_value"];

              for (let milestonePmoDbDataItem of this.pmoDb.milestonePmoDbData) {

                if (milestonePmoDbDataItem.planned_count == null)
                  milestonePmoDbDataItem.planned_count = 0;

                if (milestonePmoDbDataItem.actual_count == null)
                  milestonePmoDbDataItem.actual_count = 0;

                if (milestonePmoDbDataItem.overdue_count == null)
                  milestonePmoDbDataItem.overdue_count = 0;

                if (milestonePmoDbDataItem.planned_value == null)
                  milestonePmoDbDataItem.planned_value = 0;

                if (milestonePmoDbDataItem.actual_value == null)
                  milestonePmoDbDataItem.actual_value = 0;

                if (milestonePmoDbDataItem.overdue_value == null)
                  milestonePmoDbDataItem.overdue_value = 0;

                if (milestonePmoDbDataItem.gantt_type_name == "Milestone") {

                  let project_week_planned_value = _.filter(overall_planned_value, function (overall_planned_value_item) {

                    if (overall_planned_value_item.project_id == milestonePmoDbDataItem.project_id && overall_planned_value_item.week == milestonePmoDbDataItem.week && overall_planned_value_item.month == milestonePmoDbDataItem.month && moment(overall_planned_value_item.planned_end_date).format("YYYY-MM-DD") == moment(milestonePmoDbDataItem.planned_end_date).format("YYYY-MM-DD"))
                      return overall_planned_value_item;

                  });

                  for (let project_week_planned_value_item of project_week_planned_value)
                    milestonePmoDbDataItem.planned_value += project_week_planned_value_item.milestone_value;


                  let project_week_actual_value = _.filter(overall_actual_value, function (overall_actual_value_item) {

                    if (overall_actual_value_item.project_id == milestonePmoDbDataItem.project_id && overall_actual_value_item.week == milestonePmoDbDataItem.week && overall_actual_value_item.month == milestonePmoDbDataItem.month && moment(overall_actual_value_item.planned_end_date).format("YYYY-MM-DD") == moment(milestonePmoDbDataItem.planned_end_date).format("YYYY-MM-DD"))
                      return overall_actual_value_item;

                  });

                  for (let project_week_actual_value_item of project_week_actual_value)
                    milestonePmoDbDataItem.actual_value += project_week_actual_value_item.milestone_value;


                  let project_week_overdue_value = _.filter(overall_overdue_value, function (overall_overdue_value_item) {

                    if (overall_overdue_value_item.project_id == milestonePmoDbDataItem.project_id && overall_overdue_value_item.week == milestonePmoDbDataItem.week && overall_overdue_value_item.month == milestonePmoDbDataItem.month && moment(overall_overdue_value_item.planned_end_date).format("YYYY-MM-DD") == moment(milestonePmoDbDataItem.planned_end_date).format("YYYY-MM-DD"))
                      return overall_overdue_value_item;

                  });

                  for (let project_week_overdue_value_item of project_week_overdue_value)
                    milestonePmoDbDataItem.overdue_value += project_week_overdue_value_item.milestone_value;

                }

                if (milestonePmoDbDataItem.is_spill_over == 1) {

                  milestonePmoDbDataItem.planned_count = 0;
                  milestonePmoDbDataItem.actual_count = 0;
                  milestonePmoDbDataItem.planned_value = 0;
                  milestonePmoDbDataItem.actual_value = 0;

                }

              }

            }

            this.resolveProjectsData();

          }, err => {
            this.showErrorMessage(err);
          });

      }

      else
        this.resolveProjectsData();

    }

    else
      this.resolveProjectsData();

  }

  insertWeekIntoMonth(monthCount, currentMonth, currentWeekItem) {

    if (monthCount == 1)
      this.pmoDb.currentWeekIntervals.push(currentWeekItem);

    let currentWeekCount = parseInt(currentWeekItem.weekInterval.replace("W", ""));

    if (moment().month() == currentMonth && moment().week() == currentWeekCount) {

      this.pmoDb.currentWeekIntervalLabel = currentWeekItem.weekInterval;
      this.pmoDb.currentMonthIntervalCount = currentMonth;

    }

    this.pmoDb.weekIntervals.push(currentWeekItem);

    this.pmoDb.monthIntervals[monthCount - 1].weekIntervals.push(currentWeekItem);

  }

  async iterativelyAppendPlsToRollDowns(rollUpPlId) {

    for (let legalEntitiesDataItem of this.plRollupWithDuplicates) {

      let doesContainPl = _.where(legalEntitiesDataItem.rollupLevels, { rollUpPlId: rollUpPlId });

      if (doesContainPl != null && doesContainPl.length > 0) {

        this.dmBaseLegalEntityRollDowns.push(legalEntitiesDataItem.plId);
        await this.iterativelyAppendPlsToRollDowns(legalEntitiesDataItem.plId);

      }

    }

  }

  dmBaseLegalEntityRollDowns = [];

  async resolveProjectsData() {

    let currentLeData = _.where(this.plRollupWithDuplicates, { description: this.detailedModeBaseLegalEntity });

    this.dmBaseLegalEntityRollDowns = [];

    if (currentLeData != null && currentLeData.length > 0) {

      this.dmBaseLegalEntityRollDowns = [currentLeData[0].plId];

      await this.iterativelyAppendPlsToRollDowns(currentLeData[0].plId);

    }

    this.pmoDb.resolvedPmoDbData = [];

    this.calculatePmoDbHeaderData();

    for (let milestonePmoDbDataItem of this.pmoDb.milestonePmoDbData) {

      let dataItem = {
        currentPlId: milestonePmoDbDataItem.pl_id,
        currentSubPlId: milestonePmoDbDataItem.sub_pl_id,
        currentVerticalId: milestonePmoDbDataItem.vertical_id,
        currentProjectId: milestonePmoDbDataItem.project_id,
        currentPlName: milestonePmoDbDataItem.pl_name,
        currentSubPlName: milestonePmoDbDataItem.sub_pl_name,
        currentVerticalName: milestonePmoDbDataItem.vertical_name,
        currentProjectName: milestonePmoDbDataItem.project_name,
        currentDateWeekKey: milestonePmoDbDataItem.is_spill_over == 1 ? "Spillover" : ("W" + milestonePmoDbDataItem.week),
        currentMonthCount: milestonePmoDbDataItem.is_spill_over == 1 ? "" : (milestonePmoDbDataItem.month - 1)
      }

      if (_.contains(this.dmBaseLegalEntityRollDowns, dataItem.currentVerticalId)) {

        let currentPlData = _.where(this.pmoDb.resolvedPmoDbData, { pl_id: dataItem.currentPlId });

        if (currentPlData && currentPlData.length > 0) {

          if (milestonePmoDbDataItem.project_id != null)
            currentPlData[0]["project_ids"].push(milestonePmoDbDataItem.project_id);

          let countPlPrefixType = "";

          if (milestonePmoDbDataItem.gantt_type_name == "Milestone")
            countPlPrefixType = "ml_";

          else if (milestonePmoDbDataItem.gantt_type_name == "Activity")
            countPlPrefixType = "ac_";

          else if (milestonePmoDbDataItem.gantt_type_name == "Sign Off")
            countPlPrefixType = "so_";

          else if (milestonePmoDbDataItem.gantt_type_name == "Phase")
            countPlPrefixType = "ph_";

          else if (milestonePmoDbDataItem.gantt_type_name == "Work Package")
            countPlPrefixType = "wp_";

          if (currentPlData[0][countPlPrefixType + "actual_count" + dataItem.currentDateWeekKey + dataItem.currentMonthCount] == null)
            currentPlData[0][countPlPrefixType + "actual_count" + dataItem.currentDateWeekKey + dataItem.currentMonthCount] = 0;

          if (currentPlData[0][countPlPrefixType + "planned_count" + dataItem.currentDateWeekKey + dataItem.currentMonthCount] == null)
            currentPlData[0][countPlPrefixType + "planned_count" + dataItem.currentDateWeekKey + dataItem.currentMonthCount] = 0;

          if (currentPlData[0][countPlPrefixType + "overdue_count" + dataItem.currentDateWeekKey + dataItem.currentMonthCount] == null)
            currentPlData[0][countPlPrefixType + "overdue_count" + dataItem.currentDateWeekKey + dataItem.currentMonthCount] = 0;

          currentPlData[0][countPlPrefixType + "actual_count" + dataItem.currentDateWeekKey + dataItem.currentMonthCount] += milestonePmoDbDataItem.actual_count;
          currentPlData[0][countPlPrefixType + "planned_count" + dataItem.currentDateWeekKey + dataItem.currentMonthCount] += milestonePmoDbDataItem.planned_count;
          currentPlData[0][countPlPrefixType + "overdue_count" + dataItem.currentDateWeekKey + dataItem.currentMonthCount] += milestonePmoDbDataItem.overdue_count;

          currentPlData[0]["ml_actual_value" + dataItem.currentDateWeekKey + dataItem.currentMonthCount + "Original"] += milestonePmoDbDataItem.actual_value;
          currentPlData[0]["ml_planned_value" + dataItem.currentDateWeekKey + dataItem.currentMonthCount + "Original"] += milestonePmoDbDataItem.planned_value;
          currentPlData[0]["ml_overdue_value" + dataItem.currentDateWeekKey + dataItem.currentMonthCount + "Original"] += milestonePmoDbDataItem.overdue_value;

          let currentSubPlData = _.where(currentPlData[0].subItems, { sub_pl_id: dataItem.currentSubPlId });

          if (currentSubPlData && currentSubPlData.length > 0) {

            if (milestonePmoDbDataItem.project_id != null)
              currentSubPlData[0]["project_ids"].push(milestonePmoDbDataItem.project_id);

            let countSubPlPrefixType = "";

            if (milestonePmoDbDataItem.gantt_type_name == "Milestone")
              countSubPlPrefixType = "ml_";

            else if (milestonePmoDbDataItem.gantt_type_name == "Activity")
              countSubPlPrefixType = "ac_";

            else if (milestonePmoDbDataItem.gantt_type_name == "Sign Off")
              countSubPlPrefixType = "so_";

            else if (milestonePmoDbDataItem.gantt_type_name == "Phase")
              countSubPlPrefixType = "ph_";

            else if (milestonePmoDbDataItem.gantt_type_name == "Work Package")
              countSubPlPrefixType = "wp_";

            if (currentSubPlData[0][countSubPlPrefixType + "actual_count" + dataItem.currentDateWeekKey + dataItem.currentMonthCount] == null)
              currentSubPlData[0][countSubPlPrefixType + "actual_count" + dataItem.currentDateWeekKey + dataItem.currentMonthCount] = 0;

            if (currentSubPlData[0][countSubPlPrefixType + "planned_count" + dataItem.currentDateWeekKey + dataItem.currentMonthCount] == null)
              currentSubPlData[0][countSubPlPrefixType + "planned_count" + dataItem.currentDateWeekKey + dataItem.currentMonthCount] = 0;

            if (currentSubPlData[0][countSubPlPrefixType + "overdue_count" + dataItem.currentDateWeekKey + dataItem.currentMonthCount] == null)
              currentSubPlData[0][countSubPlPrefixType + "overdue_count" + dataItem.currentDateWeekKey + dataItem.currentMonthCount] = 0;

            currentSubPlData[0][countSubPlPrefixType + "actual_count" + dataItem.currentDateWeekKey + dataItem.currentMonthCount] += milestonePmoDbDataItem.actual_count;
            currentSubPlData[0][countSubPlPrefixType + "planned_count" + dataItem.currentDateWeekKey + dataItem.currentMonthCount] += milestonePmoDbDataItem.planned_count;
            currentSubPlData[0][countSubPlPrefixType + "overdue_count" + dataItem.currentDateWeekKey + dataItem.currentMonthCount] += milestonePmoDbDataItem.overdue_count;

            currentSubPlData[0]["ml_actual_value" + dataItem.currentDateWeekKey + dataItem.currentMonthCount + "Original"] += milestonePmoDbDataItem.actual_value;
            currentSubPlData[0]["ml_planned_value" + dataItem.currentDateWeekKey + dataItem.currentMonthCount + "Original"] += milestonePmoDbDataItem.planned_value;
            currentSubPlData[0]["ml_overdue_value" + dataItem.currentDateWeekKey + dataItem.currentMonthCount + "Original"] += milestonePmoDbDataItem.overdue_value;

            let currentVerticalData = _.where(currentSubPlData[0].subItems, { vertical_id: dataItem.currentVerticalId });

            if (currentVerticalData && currentVerticalData.length > 0) {

              if (milestonePmoDbDataItem.project_id != null)
                currentVerticalData[0]["project_ids"].push(milestonePmoDbDataItem.project_id);

              let countVerticalPrefixType = "";

              if (milestonePmoDbDataItem.gantt_type_name == "Milestone")
                countVerticalPrefixType = "ml_";

              else if (milestonePmoDbDataItem.gantt_type_name == "Activity")
                countVerticalPrefixType = "ac_";

              else if (milestonePmoDbDataItem.gantt_type_name == "Sign Off")
                countVerticalPrefixType = "so_";

              else if (milestonePmoDbDataItem.gantt_type_name == "Phase")
                countVerticalPrefixType = "ph_";

              else if (milestonePmoDbDataItem.gantt_type_name == "Work Package")
                countVerticalPrefixType = "wp_";

              if (currentVerticalData[0][countVerticalPrefixType + "actual_count" + dataItem.currentDateWeekKey + dataItem.currentMonthCount] == null)
                currentVerticalData[0][countVerticalPrefixType + "actual_count" + dataItem.currentDateWeekKey + dataItem.currentMonthCount] = 0;

              if (currentVerticalData[0][countVerticalPrefixType + "planned_count" + dataItem.currentDateWeekKey + dataItem.currentMonthCount] == null)
                currentVerticalData[0][countVerticalPrefixType + "planned_count" + dataItem.currentDateWeekKey + dataItem.currentMonthCount] = 0;

              if (currentVerticalData[0][countVerticalPrefixType + "overdue_count" + dataItem.currentDateWeekKey + dataItem.currentMonthCount] == null)
                currentVerticalData[0][countVerticalPrefixType + "overdue_count" + dataItem.currentDateWeekKey + dataItem.currentMonthCount] = 0;

              currentVerticalData[0][countVerticalPrefixType + "actual_count" + dataItem.currentDateWeekKey + dataItem.currentMonthCount] += milestonePmoDbDataItem.actual_count;
              currentVerticalData[0][countVerticalPrefixType + "planned_count" + dataItem.currentDateWeekKey + dataItem.currentMonthCount] += milestonePmoDbDataItem.planned_count;
              currentVerticalData[0][countVerticalPrefixType + "overdue_count" + dataItem.currentDateWeekKey + dataItem.currentMonthCount] += milestonePmoDbDataItem.overdue_count;

              currentVerticalData[0]["ml_actual_value" + dataItem.currentDateWeekKey + dataItem.currentMonthCount + "Original"] += milestonePmoDbDataItem.actual_value;
              currentVerticalData[0]["ml_planned_value" + dataItem.currentDateWeekKey + dataItem.currentMonthCount + "Original"] += milestonePmoDbDataItem.planned_value;
              currentVerticalData[0]["ml_overdue_value" + dataItem.currentDateWeekKey + dataItem.currentMonthCount + "Original"] += milestonePmoDbDataItem.overdue_value;

              let currentProjectData = _.where(currentVerticalData[0].subItems, { project_id: dataItem.currentProjectId });

              if (currentProjectData && currentProjectData.length > 0) {

                if (milestonePmoDbDataItem.project_id != null)
                  currentProjectData[0]["project_ids"].push(milestonePmoDbDataItem.project_id);

                let countProjectPrefixType = "";

                if (milestonePmoDbDataItem.gantt_type_name == "Milestone")
                  countProjectPrefixType = "ml_";

                else if (milestonePmoDbDataItem.gantt_type_name == "Activity")
                  countProjectPrefixType = "ac_";

                else if (milestonePmoDbDataItem.gantt_type_name == "Sign Off")
                  countProjectPrefixType = "so_";

                else if (milestonePmoDbDataItem.gantt_type_name == "Phase")
                  countProjectPrefixType = "ph_";

                else if (milestonePmoDbDataItem.gantt_type_name == "Work Package")
                  countProjectPrefixType = "wp_";

                if (currentProjectData[0][countProjectPrefixType + "actual_count" + dataItem.currentDateWeekKey + dataItem.currentMonthCount] == null)
                  currentProjectData[0][countProjectPrefixType + "actual_count" + dataItem.currentDateWeekKey + dataItem.currentMonthCount] = 0;

                if (currentProjectData[0][countProjectPrefixType + "planned_count" + dataItem.currentDateWeekKey + dataItem.currentMonthCount] == null)
                  currentProjectData[0][countProjectPrefixType + "planned_count" + dataItem.currentDateWeekKey + dataItem.currentMonthCount] = 0;

                if (currentProjectData[0][countProjectPrefixType + "overdue_count" + dataItem.currentDateWeekKey + dataItem.currentMonthCount] == null)
                  currentProjectData[0][countProjectPrefixType + "overdue_count" + dataItem.currentDateWeekKey + dataItem.currentMonthCount] = 0;

                currentProjectData[0][countProjectPrefixType + "actual_count" + dataItem.currentDateWeekKey + dataItem.currentMonthCount] += milestonePmoDbDataItem.actual_count;
                currentProjectData[0][countProjectPrefixType + "planned_count" + dataItem.currentDateWeekKey + dataItem.currentMonthCount] += milestonePmoDbDataItem.planned_count;
                currentProjectData[0][countProjectPrefixType + "overdue_count" + dataItem.currentDateWeekKey + dataItem.currentMonthCount] += milestonePmoDbDataItem.overdue_count;

                currentProjectData[0]["ml_actual_value" + dataItem.currentDateWeekKey + dataItem.currentMonthCount + "Original"] += milestonePmoDbDataItem.actual_value;
                currentProjectData[0]["ml_planned_value" + dataItem.currentDateWeekKey + dataItem.currentMonthCount + "Original"] += milestonePmoDbDataItem.planned_value;
                currentProjectData[0]["ml_overdue_value" + dataItem.currentDateWeekKey + dataItem.currentMonthCount + "Original"] += milestonePmoDbDataItem.overdue_value;

              }

              else
                currentVerticalData[0].subItems.push(await this.returnPmoDbDataItem(dataItem, milestonePmoDbDataItem, 0));

            }

            else
              currentSubPlData[0].subItems.push(await this.returnPmoDbDataItem(dataItem, milestonePmoDbDataItem, 1));

          }

          else
            currentPlData[0].subItems.push(await this.returnPmoDbDataItem(dataItem, milestonePmoDbDataItem, 2));

        }

        else
          this.pmoDb.resolvedPmoDbData.push(await this.returnPmoDbDataItem(dataItem, milestonePmoDbDataItem, 3));

      }

    }

    for (let weekInterval of this.pmoDb.weekIntervals)
      this.calcWeekWiseAllData(weekInterval);

    let weekIntervalSpillover = {
      weekInterval: "Spillover",
      monthCount: ""
    }

    this.calcWeekWiseAllData(weekIntervalSpillover);

    this.determinePmoWidgetItemColorClasses();

    this.pmoDb.isPmoDbDataLoading = false;

    this.pmoDb.isProjectsDataRetrieved = true;

    this.governanceReportService.defaultSortOrders = JSON.parse(JSON.stringify(this.governanceReportService.sortOrders));

  }

  calcWeekWiseAllData(weekInterval) {

    for (let resolvedPmoDbPlDataItem of this.pmoDb.resolvedPmoDbData) {

      resolvedPmoDbPlDataItem = this.calculateValueAndTooltipData(resolvedPmoDbPlDataItem, weekInterval);

      for (let resolvedPmoDbSubPlDataItem of resolvedPmoDbPlDataItem.subItems) {

        resolvedPmoDbSubPlDataItem = this.calculateValueAndTooltipData(resolvedPmoDbSubPlDataItem, weekInterval);

        for (let resolvedPmoDbVerticalDataItem of resolvedPmoDbSubPlDataItem.subItems) {

          resolvedPmoDbVerticalDataItem = this.calculateValueAndTooltipData(resolvedPmoDbVerticalDataItem, weekInterval);

          for (let resolvedPmoDbProjectDataItem of resolvedPmoDbVerticalDataItem.subItems)
            resolvedPmoDbProjectDataItem = this.calculateValueAndTooltipData(resolvedPmoDbProjectDataItem, weekInterval);

        }

      }

    }

  }

  calculateValueAndTooltipData(resolvedPmoDbDataItem, weekInterval) {

    resolvedPmoDbDataItem["ml_actual_value" + weekInterval.weekInterval + weekInterval.monthCount] = this.getValueWithComma(resolvedPmoDbDataItem["ml_actual_value" + weekInterval.weekInterval + weekInterval.monthCount + "Original"]);
    resolvedPmoDbDataItem["ml_planned_value" + weekInterval.weekInterval + weekInterval.monthCount] = this.getValueWithComma(resolvedPmoDbDataItem["ml_planned_value" + weekInterval.weekInterval + weekInterval.monthCount + "Original"]);
    resolvedPmoDbDataItem["ml_overdue_value" + weekInterval.weekInterval + weekInterval.monthCount] = this.getValueWithComma(resolvedPmoDbDataItem["ml_overdue_value" + weekInterval.weekInterval + weekInterval.monthCount + "Original"]);

    resolvedPmoDbDataItem["ml_actual_value" + weekInterval.weekInterval + weekInterval.monthCount + "Tooltip"] = this.getValueTooltipWithComma(resolvedPmoDbDataItem["ml_actual_value" + weekInterval.weekInterval + weekInterval.monthCount + "Original"]);
    resolvedPmoDbDataItem["ml_planned_value" + weekInterval.weekInterval + weekInterval.monthCount + "Tooltip"] = this.getValueTooltipWithComma(resolvedPmoDbDataItem["ml_planned_value" + weekInterval.weekInterval + weekInterval.monthCount + "Original"]);
    resolvedPmoDbDataItem["ml_overdue_value" + weekInterval.weekInterval + weekInterval.monthCount + "Tooltip"] = this.getValueTooltipWithComma(resolvedPmoDbDataItem["ml_overdue_value" + weekInterval.weekInterval + weekInterval.monthCount + "Original"]);


    if (resolvedPmoDbDataItem["ml_actual_count" + weekInterval.weekInterval + weekInterval.monthCount] == null)
      resolvedPmoDbDataItem["ml_actual_count" + weekInterval.weekInterval + weekInterval.monthCount] = 0;

    if (resolvedPmoDbDataItem["ml_planned_count" + weekInterval.weekInterval + weekInterval.monthCount] == null)
      resolvedPmoDbDataItem["ml_planned_count" + weekInterval.weekInterval + weekInterval.monthCount] = 0;

    if (resolvedPmoDbDataItem["ml_overdue_count" + weekInterval.weekInterval + weekInterval.monthCount] == null)
      resolvedPmoDbDataItem["ml_overdue_count" + weekInterval.weekInterval + weekInterval.monthCount] = 0;


    if (resolvedPmoDbDataItem["ac_actual_count" + weekInterval.weekInterval + weekInterval.monthCount] == null)
      resolvedPmoDbDataItem["ac_actual_count" + weekInterval.weekInterval + weekInterval.monthCount] = 0;

    if (resolvedPmoDbDataItem["ac_planned_count" + weekInterval.weekInterval + weekInterval.monthCount] == null)
      resolvedPmoDbDataItem["ac_planned_count" + weekInterval.weekInterval + weekInterval.monthCount] = 0;

    if (resolvedPmoDbDataItem["ac_overdue_count" + weekInterval.weekInterval + weekInterval.monthCount] == null)
      resolvedPmoDbDataItem["ac_overdue_count" + weekInterval.weekInterval + weekInterval.monthCount] = 0;


    if (resolvedPmoDbDataItem["ph_actual_count" + weekInterval.weekInterval + weekInterval.monthCount] == null)
      resolvedPmoDbDataItem["ph_actual_count" + weekInterval.weekInterval + weekInterval.monthCount] = 0;

    if (resolvedPmoDbDataItem["ph_planned_count" + weekInterval.weekInterval + weekInterval.monthCount] == null)
      resolvedPmoDbDataItem["ph_planned_count" + weekInterval.weekInterval + weekInterval.monthCount] = 0;

    if (resolvedPmoDbDataItem["ph_overdue_count" + weekInterval.weekInterval + weekInterval.monthCount] == null)
      resolvedPmoDbDataItem["ph_overdue_count" + weekInterval.weekInterval + weekInterval.monthCount] = 0;


    if (resolvedPmoDbDataItem["so_actual_count" + weekInterval.weekInterval + weekInterval.monthCount] == null)
      resolvedPmoDbDataItem["so_actual_count" + weekInterval.weekInterval + weekInterval.monthCount] = 0;

    if (resolvedPmoDbDataItem["so_planned_count" + weekInterval.weekInterval + weekInterval.monthCount] == null)
      resolvedPmoDbDataItem["so_planned_count" + weekInterval.weekInterval + weekInterval.monthCount] = 0;

    if (resolvedPmoDbDataItem["so_overdue_count" + weekInterval.weekInterval + weekInterval.monthCount] == null)
      resolvedPmoDbDataItem["so_overdue_count" + weekInterval.weekInterval + weekInterval.monthCount] = 0;


    if (resolvedPmoDbDataItem["wp_actual_count" + weekInterval.weekInterval + weekInterval.monthCount] == null)
      resolvedPmoDbDataItem["wp_actual_count" + weekInterval.weekInterval + weekInterval.monthCount] = 0;

    if (resolvedPmoDbDataItem["wp_planned_count" + weekInterval.weekInterval + weekInterval.monthCount] == null)
      resolvedPmoDbDataItem["wp_planned_count" + weekInterval.weekInterval + weekInterval.monthCount] = 0;

    if (resolvedPmoDbDataItem["wp_overdue_count" + weekInterval.weekInterval + weekInterval.monthCount] == null)
      resolvedPmoDbDataItem["wp_overdue_count" + weekInterval.weekInterval + weekInterval.monthCount] = 0;

    return resolvedPmoDbDataItem;

  }

  returnPmoDbDataItem(dataItem, milestonePmoDbDataItem, pl_level) {

    let currentDataItem = {
      pl_id: dataItem.currentPlId,
      sub_pl_id: dataItem.currentSubPlId,
      vertical_id: dataItem.currentVerticalId,
      plId: dataItem.currentPlId,
      subPlId: dataItem.currentSubPlId,
      verticalId: dataItem.currentVerticalId,
      project_id: dataItem.currentProjectId,
      description: pl_level == 0 ? dataItem.currentProjectName : pl_level == 1 ? dataItem.currentVerticalName : pl_level == 2 ? dataItem.currentSubPlName : pl_level == 3 ? dataItem.currentPlName : "",
      subItems: [],
      isSubItem: pl_level == 3 ? false : true,
      plLevel: pl_level,
      project_ids: [],
      isExpanded: false
    };

    for (let weekInterval of this.pmoDb.weekIntervals)
      this.weekWiseCalcIntervalData(milestonePmoDbDataItem, currentDataItem, dataItem, weekInterval);


    let weekIntervalSpillover = {
      weekInterval: "Spillover",
      monthCount: ""
    }

    this.weekWiseCalcIntervalData(milestonePmoDbDataItem, currentDataItem, dataItem, weekIntervalSpillover);

    if (pl_level == 3) {

      let projectItem = this.prepareItemPmoDbJson(currentDataItem, 0, dataItem);
      let verticalItem = this.prepareItemPmoDbJson(currentDataItem, 1, dataItem);
      let subItem = this.prepareItemPmoDbJson(currentDataItem, 2, dataItem);

      verticalItem.subItems.push(projectItem);
      subItem.subItems.push(verticalItem);
      currentDataItem.subItems.push(subItem);

    }

    else if (pl_level == 2) {

      let projectItem = this.prepareItemPmoDbJson(currentDataItem, 0, dataItem);
      let verticalItem = this.prepareItemPmoDbJson(currentDataItem, 1, dataItem);

      verticalItem.subItems.push(projectItem);
      currentDataItem.subItems.push(verticalItem);

    }

    else if (pl_level == 1) {

      let projectItem = this.prepareItemPmoDbJson(currentDataItem, 0, dataItem);
      currentDataItem.subItems.push(projectItem);

    }

    return currentDataItem;

  }

  weekWiseCalcIntervalData(milestonePmoDbDataItem, currentDataItem, dataItem, weekInterval) {

    if (milestonePmoDbDataItem.project_id != null)
      currentDataItem["project_ids"] = [milestonePmoDbDataItem.project_id];

    let countItemPrefixType = "";

    if (milestonePmoDbDataItem.gantt_type_name == "Milestone")
      countItemPrefixType = "ml_";

    else if (milestonePmoDbDataItem.gantt_type_name == "Activity")
      countItemPrefixType = "ac_";

    else if (milestonePmoDbDataItem.gantt_type_name == "Sign Off")
      countItemPrefixType = "so_";

    else if (milestonePmoDbDataItem.gantt_type_name == "Phase")
      countItemPrefixType = "ph_";

    else if (milestonePmoDbDataItem.gantt_type_name == "Work Package")
      countItemPrefixType = "wp_";

    if (currentDataItem[countItemPrefixType + "actual_count" + dataItem.currentDateWeekKey + weekInterval.monthCount] == null)
      currentDataItem[countItemPrefixType + "actual_count" + dataItem.currentDateWeekKey + weekInterval.monthCount] = 0;

    if (currentDataItem[countItemPrefixType + "planned_count" + dataItem.currentDateWeekKey + weekInterval.monthCount] == null)
      currentDataItem[countItemPrefixType + "planned_count" + dataItem.currentDateWeekKey + weekInterval.monthCount] = 0;

    if (currentDataItem[countItemPrefixType + "overdue_count" + dataItem.currentDateWeekKey + weekInterval.monthCount] == null)
      currentDataItem[countItemPrefixType + "overdue_count" + dataItem.currentDateWeekKey + weekInterval.monthCount] = 0;

    currentDataItem[countItemPrefixType + "actual_count" + dataItem.currentDateWeekKey + weekInterval.monthCount] += (dataItem.currentDateWeekKey == weekInterval.weekInterval && dataItem.currentMonthCount == weekInterval.monthCount) ? milestonePmoDbDataItem.actual_count : 0;
    currentDataItem[countItemPrefixType + "planned_count" + dataItem.currentDateWeekKey + weekInterval.monthCount] += (dataItem.currentDateWeekKey == weekInterval.weekInterval && dataItem.currentMonthCount == weekInterval.monthCount) ? milestonePmoDbDataItem.planned_count : 0;
    currentDataItem[countItemPrefixType + "overdue_count" + dataItem.currentDateWeekKey + weekInterval.monthCount] += (dataItem.currentDateWeekKey == weekInterval.weekInterval && dataItem.currentMonthCount == weekInterval.monthCount) ? milestonePmoDbDataItem.overdue_count : 0;

    currentDataItem["ml_actual_value" + weekInterval.weekInterval + weekInterval.monthCount + "Original"] = (dataItem.currentDateWeekKey == weekInterval.weekInterval && dataItem.currentMonthCount == weekInterval.monthCount) ? milestonePmoDbDataItem.actual_value : 0;
    currentDataItem["ml_planned_value" + weekInterval.weekInterval + weekInterval.monthCount + "Original"] = (dataItem.currentDateWeekKey == weekInterval.weekInterval && dataItem.currentMonthCount == weekInterval.monthCount) ? milestonePmoDbDataItem.planned_value : 0;
    currentDataItem["ml_overdue_value" + weekInterval.weekInterval + weekInterval.monthCount + "Original"] = (dataItem.currentDateWeekKey == weekInterval.weekInterval && dataItem.currentMonthCount == weekInterval.monthCount) ? milestonePmoDbDataItem.overdue_value : 0;

  }

  prepareItemPmoDbJson(currentDataItem, currentPlLevel, dataItem) {

    let currentDataItemTemp = JSON.parse(JSON.stringify(currentDataItem));

    currentDataItemTemp.isSubItem = true;

    currentDataItemTemp.plLevel = currentPlLevel;

    currentDataItemTemp.description = currentPlLevel == 0 ? dataItem.currentProjectName : currentPlLevel == 1 ? dataItem.currentVerticalName : currentPlLevel == 2 ? dataItem.currentSubPlName : currentPlLevel == 3 ? dataItem.currentPlName : "";

    return currentDataItemTemp;

  }

  retrieveCTAFTMData() {

    if (!this.isCTAFTMDataRetrieved) {

      let inputDetails = {
        userOId: this.currentUser.oid,
        startDate: this.activeFtmStartDate,
        endDate: this.activeFtmEndDate
      }

      this.isCTAFTMDataRetrieved = true;

      this.sharedLazyLoadedComponentsService.retrieveCTAsBetweenRange(inputDetails).then((myCtaData: any) => {

        if (myCtaData && myCtaData.length && myCtaData.length > 0)
          this.ctaFTMData = myCtaData;

        else
          this.ctaFTMData = [];

        this.resolveCTAFTMData();

      }, err => {
        this.showErrorMessage(err);
      });

    }

    else
      this.resolveCTAFTMData();

  }

  resolveCTAFTMData() {

    let ctaUserRolePlIds: any = [];

    let ctaUserRolePlIdsData = _.where(this.userRolePlIds, { object_id: 171 });

    if (ctaUserRolePlIdsData && ctaUserRolePlIdsData.length > 0)
      ctaUserRolePlIds = ctaUserRolePlIdsData[0].pl_ids;

    if (ctaUserRolePlIds != "ALL")
      if (ctaUserRolePlIds.length > 0)
        this.ctaFTMData = _.filter(this.ctaFTMData, function (ctaFTMDataItem) {

          if ((ctaFTMDataItem.pl_id && _.contains(ctaUserRolePlIds, ctaFTMDataItem.pl_id)) ||
            (ctaFTMDataItem.rollup_pl_id && _.contains(ctaUserRolePlIds, ctaFTMDataItem.rollup_pl_id)))
            return ctaFTMDataItem;

        }, this);
      else
        this.ctaFTMData = [];

    for (let udrfFilterConfigItem of this.udrfFilterConfig)
      if (udrfFilterConfigItem.ctaFilterIdBased && udrfFilterConfigItem.ctaFilterIdBased == "Y" && udrfFilterConfigItem.filterIds.length > 0)
        this.ctaFTMData = _.filter(this.ctaFTMData, function (sourceMISDataItem) {

          if (sourceMISDataItem[udrfFilterConfigItem.ctaFilterField] && _.contains(udrfFilterConfigItem.filterIds, sourceMISDataItem[udrfFilterConfigItem.ctaFilterField]))
            return sourceMISDataItem;

        }, this);

      else if (udrfFilterConfigItem.ctaFilterIdBased && udrfFilterConfigItem.ctaFilterIdBased == "N" && udrfFilterConfigItem.filterNames.length > 0)
        this.ctaFTMData = _.filter(this.ctaFTMData, function (sourceMISDataItem) {

          if (sourceMISDataItem[udrfFilterConfigItem.ctaFilterField] && _.contains(udrfFilterConfigItem.filterNames, sourceMISDataItem[udrfFilterConfigItem.ctaFilterField]))
            return sourceMISDataItem;

        }, this);

      else if (udrfFilterConfigItem.ctaFilterIdBased && udrfFilterConfigItem.ctaFilterIdBased == "K" && udrfFilterConfigItem.filterKeyValues.length > 0)
        this.ctaFTMData = _.filter(this.ctaFTMData, function (sourceMISDataItem) {

          if (sourceMISDataItem[udrfFilterConfigItem.ctaFilterIdBased] && _.contains(udrfFilterConfigItem.filterKeyValues, sourceMISDataItem[udrfFilterConfigItem.ctaFilterIdBased]))
            return sourceMISDataItem;

        }, this);

    if (this.reportMode == "Normal Mode")
      this.resolveNormalModeCTAFTMData();

    else if (this.reportMode == "Detailed Mode")
      this.resolveDetailedModeCTAFTMData();

  }

  resolveNormalModeCTAFTMData() {

    this.resolvedNormalModeCTAFTMData = [];

    for (let ctaFTMDataItem of this.ctaFTMData) {

      let resolvedNormalModeCTAFTMData = {
        id: ctaFTMDataItem.id,
        name: ctaFTMDataItem.description,
        type: ctaFTMDataItem.cta_type,
        originatorOId: ctaFTMDataItem.owner_individual_oid,
        originator: ctaFTMDataItem.owner_individual_display_name,
        primaryOwnerOId: ctaFTMDataItem.primary_assigned_oid,
        primaryOwner: ctaFTMDataItem.primary_assigned_display_name,
        plName: ctaFTMDataItem.pl_name ? ctaFTMDataItem.pl_name : this.highestPl,
        rollupPlName: ctaFTMDataItem.rollup_pl_name ? ctaFTMDataItem.rollup_pl_name : "",
        status: ctaFTMDataItem.status_name,
        dueDate: moment(ctaFTMDataItem.planned_end_date).format(this.dfDDMMMYYYY),
        dueDateOriginal: ctaFTMDataItem.planned_end_date
      };

      resolvedNormalModeCTAFTMData["displayName"] = ((resolvedNormalModeCTAFTMData.rollupPlName == '' ?
        resolvedNormalModeCTAFTMData.plName : resolvedNormalModeCTAFTMData.rollupPlName)) +
        ((resolvedNormalModeCTAFTMData.rollupPlName == '' ? '' : ' | ')) + ((resolvedNormalModeCTAFTMData.rollupPlName == '' ?
          resolvedNormalModeCTAFTMData.rollupPlName : resolvedNormalModeCTAFTMData.plName));

      this.resolvedNormalModeCTAFTMData.push(resolvedNormalModeCTAFTMData);

    }

    this.total_cta_count = this.resolvedNormalModeCTAFTMData.length;

    this.completed_cta_count = _.where(this.resolvedNormalModeCTAFTMData, { status: "Completed" }).length;

    this.incomplete_cta_count = this.total_cta_count - this.completed_cta_count;

    if (this.baseLegalEntityLevel == 0)
      this.setLegalEntityCTA(this.legalEntities[0].name, this.completed_cta_count, this.total_cta_count);

    let lei_complete_count = 0, lei_total_count = 0;

    if (this.baseLegalEntityLevel == 0)
      for (let legalEntitiesItem of this.legalEntities)
        if (legalEntitiesItem.name != this.highestPl) {

          lei_complete_count = _.filter(this.resolvedNormalModeCTAFTMData, function (resolvedCTAFTMDataItem) {

            if ((resolvedCTAFTMDataItem.plName == legalEntitiesItem.name || resolvedCTAFTMDataItem.rollupPlName ==
              legalEntitiesItem.name) && resolvedCTAFTMDataItem.status == "Completed")
              return resolvedCTAFTMDataItem;

          }, this).length;

          lei_total_count = _.filter(this.resolvedNormalModeCTAFTMData, function (resolvedCTAFTMDataItem) {

            if (resolvedCTAFTMDataItem.plName == legalEntitiesItem.name || resolvedCTAFTMDataItem.rollupPlName == legalEntitiesItem.name)
              return resolvedCTAFTMDataItem;

          }, this).length;

          this.setLegalEntityCTA(legalEntitiesItem.name, lei_complete_count, lei_total_count);

        }


    if (this.normalModeCtaViewInterval == "This Month")
      this.resolvedNormalModeCTAFTMData = _.filter(this.resolvedNormalModeCTAFTMData, function (resolvedCTAFTMDataItem) {

        if (moment(resolvedCTAFTMDataItem.dueDateOriginal).isSameOrAfter(this.activeFtmStartDate) &&
          moment(resolvedCTAFTMDataItem.dueDateOriginal).isSameOrBefore(this.activeFtmEndDate))
          return resolvedCTAFTMDataItem;

      }, this);

    else if (this.normalModeCtaViewInterval == "This Week")
      this.resolvedNormalModeCTAFTMData = _.filter(this.resolvedNormalModeCTAFTMData, function (resolvedCTAFTMDataItem) {

        if (moment(resolvedCTAFTMDataItem.dueDateOriginal).isSameOrAfter(this.ftwStartDate) &&
          moment(resolvedCTAFTMDataItem.dueDateOriginal).isSameOrBefore(this.ftwEndDate))
          return resolvedCTAFTMDataItem;

      }, this);


    let ctaViewCardsTotalData = _.where(this.ctaViewCards, { status: "Total" });

    ctaViewCardsTotalData[0].count = _.filter(this.resolvedNormalModeCTAFTMData, function (resolvedCTAFTMDataItem) {

      if (this.normalModeCTABaseLegalEntity == this.highestPl)
        return resolvedCTAFTMDataItem;

      else if (resolvedCTAFTMDataItem.plName == this.normalModeCTABaseLegalEntity || resolvedCTAFTMDataItem.rollupPlName ==
        this.normalModeCTABaseLegalEntity)
        return resolvedCTAFTMDataItem;

    }, this).length;

    let ctaViewCardsCompletedData = _.where(this.ctaViewCards, { status: "Completed" });

    ctaViewCardsCompletedData[0].count = _.filter(this.resolvedNormalModeCTAFTMData, function (resolvedCTAFTMDataItem) {

      if (this.normalModeCTABaseLegalEntity == this.highestPl && resolvedCTAFTMDataItem.status == "Completed")
        return resolvedCTAFTMDataItem;

      else if ((resolvedCTAFTMDataItem.plName == this.normalModeCTABaseLegalEntity || resolvedCTAFTMDataItem.rollupPlName ==
        this.normalModeCTABaseLegalEntity) && resolvedCTAFTMDataItem.status == "Completed")
        return resolvedCTAFTMDataItem;

    }, this).length;

    let ctaViewCardsOpenData = _.where(this.ctaViewCards, { status: "Open" });

    ctaViewCardsOpenData[0].count = _.filter(this.resolvedNormalModeCTAFTMData, function (resolvedCTAFTMDataItem) {

      if (this.normalModeCTABaseLegalEntity == this.highestPl && resolvedCTAFTMDataItem.status == "Open")
        return resolvedCTAFTMDataItem;

      else if ((resolvedCTAFTMDataItem.plName == this.normalModeCTABaseLegalEntity || resolvedCTAFTMDataItem.rollupPlName ==
        this.normalModeCTABaseLegalEntity) && resolvedCTAFTMDataItem.status == "Open")
        return resolvedCTAFTMDataItem;

    }, this).length;

    let ctaViewCardsExecutionData = _.where(this.ctaViewCards, { status: "Execution" });

    ctaViewCardsExecutionData[0].count = _.filter(this.resolvedNormalModeCTAFTMData, function (resolvedCTAFTMDataItem) {

      if (this.normalModeCTABaseLegalEntity == this.highestPl && resolvedCTAFTMDataItem.status == "In Progress")
        return resolvedCTAFTMDataItem;

      else if ((resolvedCTAFTMDataItem.plName == this.normalModeCTABaseLegalEntity || resolvedCTAFTMDataItem.rollupPlName ==
        this.normalModeCTABaseLegalEntity) && resolvedCTAFTMDataItem.status == "In Progress")
        return resolvedCTAFTMDataItem;

    }, this).length;


    this.resolvedNormalModeCTAFTMData = _.filter(this.resolvedNormalModeCTAFTMData, function (resolvedCTAFTMDataItem) {

      if (this.normalModeCTABaseLegalEntity == this.highestPl)
        return resolvedCTAFTMDataItem;

      else if (resolvedCTAFTMDataItem.plName == this.normalModeCTABaseLegalEntity || resolvedCTAFTMDataItem.rollupPlName ==
        this.normalModeCTABaseLegalEntity)
        return resolvedCTAFTMDataItem;

    }, this);

    let selectedCtaViewCardsData = _.where(this.ctaViewCards, { selected: true });

    this.resolvedNormalModeCTAFTMData = _.filter(this.resolvedNormalModeCTAFTMData, function (resolvedCTAFTMDataItem) {

      if (selectedCtaViewCardsData[0].status == "Total")
        return resolvedCTAFTMDataItem;

      else if (selectedCtaViewCardsData[0].status == "Execution" && resolvedCTAFTMDataItem.status == "In Progress")
        return resolvedCTAFTMDataItem;

      else if (resolvedCTAFTMDataItem.status == selectedCtaViewCardsData[0].status)
        return resolvedCTAFTMDataItem;

    }, this);

    this.governanceReportService.headerSortByFunction(this.resolvedNormalModeCTAFTMData, this.reportMode, this.normalModeView,
      this.getSelectedCBOWCardDataType, this.baseLegalEntityLevel);

    this.isCtaDataLoading = false;

    this.retrieveAllComments();

  }

  openProject(dataRecord) {

    console.log("Inside open project")
    console.log(dataRecord)

    if(dataRecord && dataRecord.type == "Billing") {
      if (dataRecord.legalEntity && dataRecord.projectItemName && dataRecord.projectId &&
        (dataRecord.legalEntity == dataRecord.projectItemName && dataRecord.projectId != 0)) {
  
        window.open('/main/project-management/' + dataRecord.projectId + "/" +
          this.utilityService.encodeURIComponent(dataRecord.projectItemName) + "/" + dataRecord.itemId + "/" + this.utilityService.encodeURIComponent(dataRecord.itemName) + "/overview", "_blank");
  
      }
  
      // else if (dataRecord.displayName && dataRecord.projectName && dataRecord.projectId &&
      //   (dataRecord.displayName == dataRecord.projectName && dataRecord.projectId != 0)) {
  
      //   window.open('/main/project/' + dataRecord.projectId + "/" +
      //     this.utilityService.encodeURIComponent(dataRecord.projectName) + "/" + dataRecord.itemId + "/" + this.utilityService.encodeURIComponent(dataRecord.itemName) + "/overview", "_blank");
  
      // }
    }
    else if(dataRecord && dataRecord.type == "OBV") {
      if(dataRecord.opportunityId && dataRecord.projectName) {
        window.open('/main/opportunities/' + dataRecord.opportunityId + "/" + this.utilityService.encodeURIComponent(dataRecord.projectName) + "/overview", "_blank");
      }

    }
    else {

    }



  }

  resolveDetailedModeCTAFTMData() {

    this.resolvedDetailedModeCTAFTMData = [];

    for (let ctaFTMDataItem of this.ctaFTMData) {

      let resolvedDetailedModeCTAFTMDataItem = {
        id: ctaFTMDataItem.id,
        name: ctaFTMDataItem.description,
        type: ctaFTMDataItem.cta_type,
        originator: ctaFTMDataItem.owner_individual_display_name,
        originatorOId: ctaFTMDataItem.owner_individual_oid,
        primaryOwner: ctaFTMDataItem.primary_assigned_display_name,
        primaryOwnerOId: ctaFTMDataItem.primary_assigned_oid,
        plName: ctaFTMDataItem.pl_name ? ctaFTMDataItem.pl_name : this.highestPl,
        rollupPlName: ctaFTMDataItem.rollup_pl_name ? ctaFTMDataItem.rollup_pl_name : "",
        dueDate: moment(ctaFTMDataItem.planned_end_date).format(this.dfDDMMMYYYY),
        dueDateOriginal: ctaFTMDataItem.planned_end_date
      }

      resolvedDetailedModeCTAFTMDataItem["displayName"] = ((resolvedDetailedModeCTAFTMDataItem.rollupPlName == '' ?
        resolvedDetailedModeCTAFTMDataItem.plName : resolvedDetailedModeCTAFTMDataItem.rollupPlName)) +
        ((resolvedDetailedModeCTAFTMDataItem.rollupPlName == '' ? '' : ' | ')) + ((resolvedDetailedModeCTAFTMDataItem.rollupPlName == '' ?
          resolvedDetailedModeCTAFTMDataItem.rollupPlName : resolvedDetailedModeCTAFTMDataItem.plName));

      this.resolvedDetailedModeCTAFTMData.push(resolvedDetailedModeCTAFTMDataItem);

    }

    this.resolvedDetailedModeCTAFTMData = _.filter(this.resolvedDetailedModeCTAFTMData, function (resolvedCTAFTMDataItem) {

      if (this.detailedModeBaseLegalEntity == this.highestPl)
        return resolvedCTAFTMDataItem;

      else if (resolvedCTAFTMDataItem.plName == this.detailedModeBaseLegalEntity || resolvedCTAFTMDataItem.rollupPlName ==
        this.detailedModeBaseLegalEntity)
        return resolvedCTAFTMDataItem;

    }, this);


    if (this.detailedModeCtaViewInterval == "Assigned To Me")
      this.resolvedDetailedModeCTAFTMData = _.filter(this.resolvedDetailedModeCTAFTMData, function (resolvedCTAFTMDataItem) {

        if (resolvedCTAFTMDataItem.primaryOwnerOId == this.currentUser.oid)
          return resolvedCTAFTMDataItem;

      }, this);

    else if (this.detailedModeCtaViewInterval == "Assigned By Me")
      this.resolvedDetailedModeCTAFTMData = _.filter(this.resolvedDetailedModeCTAFTMData, function (resolvedCTAFTMDataItem) {

        if (resolvedCTAFTMDataItem.originatorOId == this.currentUser.oid)
          return resolvedCTAFTMDataItem;

      }, this);

    else if (this.detailedModeCtaViewInterval == "Due This Month")
      this.resolvedDetailedModeCTAFTMData = _.filter(this.resolvedDetailedModeCTAFTMData, function (resolvedCTAFTMDataItem) {

        if (moment(resolvedCTAFTMDataItem.dueDateOriginal).isSameOrAfter(this.activeFtmStartDate) &&
          moment(resolvedCTAFTMDataItem.dueDateOriginal).isSameOrBefore(this.activeFtmEndDate))
          return resolvedCTAFTMDataItem;

      }, this);

    this.detailed_mode_total_cta_count = this.resolvedDetailedModeCTAFTMData.length;

    this.detailed_mode_completed_cta_count = _.where(this.resolvedDetailedModeCTAFTMData, { status: "Completed" }).length;

    this.detailed_mode_incomplete_cta_count = this.total_cta_count - this.completed_cta_count;

    this.governanceReportService.headerSortByFunctionDm(this.resolvedDetailedModeCTAFTMData, "CTADm", this.baseLegalEntityLevel);

    this.isCtaDataLoading = false;

    this.retrieveAllComments();

  }

  allComments = [];

  areAllCommentsRetrieved = false;

  retrieveAllComments() {

    if (!this.areAllCommentsRetrieved && this.misFlags.shouldShowIfNgrCommentsAreCounted) {

      this.areAllCommentsRetrieved = true;

      this.governanceReportSubService.retrieveAllComments(this.applicationId)
        .pipe(takeUntil(this._onDestroy)).pipe(takeUntil(this._repeatCall))
        .subscribe(async res => {

          if (res["messType"] == "S" && res["data"] && res["data"].length > 0)
            this.allComments = res["data"];

          else
            this.allComments = [];

        }, err => {
          this.showErrorMessage(err);
        });

    }

  }

  setLegalEntityCBO(legalEntity, type, yetTo, poC) {

    for (let legalEntitiesItem of this.legalEntities)
      if (legalEntitiesItem.name == legalEntity) {

        legalEntitiesItem[type].yetTo = this.getValueWithComma(yetTo);
        legalEntitiesItem[type].yetToOriginal = this.getValueTooltipWithComma(yetTo);
        legalEntitiesItem[type].poC = poC;

      }

  }

  getARAmountForCollection(legalEntity, context) {

    return this.governanceReportService.getARAmountForCollection(this.legalEntities, this.resolvedArAgingDaysData, legalEntity, context, this.udrfService.udrfData, this.misFlags);

  }

  setLegalEntityCTA(legalEntity, completedCtaCount, totalCtaCount) {

    for (let legalEntitiesItem of this.legalEntities)
      if (legalEntitiesItem.name == legalEntity) {

        legalEntitiesItem.completedCtaCount = completedCtaCount;
        legalEntitiesItem.totalCtaCount = totalCtaCount;

      }

  }

  setLegalEntityWCD(legalEntity, arDays, arAmount, ubrDays, ubrAmount, wcDays, wcAmount, retentionAmount, arWoRetention, wcDaysWoRetention, wcWoRetention) {

    for (let legalEntitiesItem of this.legalEntities)
      if (legalEntitiesItem.name == legalEntity) {

        legalEntitiesItem["WCD"].arDays = arDays;
        legalEntitiesItem["WCD"].arAmount = this.getValueWithComma(arAmount);
        legalEntitiesItem["WCD"].arAmountOriginal = this.getValueTooltipWithComma(arAmount);
        legalEntitiesItem["WCD"].arAmountMain = arAmount;
        legalEntitiesItem["WCD"].ubrDays = ubrDays;
        legalEntitiesItem["WCD"].ubrAmount = this.getValueWithComma(ubrAmount);
        legalEntitiesItem["WCD"].ubrAmountOriginal = this.getValueTooltipWithComma(ubrAmount);
        legalEntitiesItem["WCD"].ubrAmountMain = ubrAmount;
        legalEntitiesItem["WCD"].wcDays = wcDays;
        legalEntitiesItem["WCD"].wcAmount = this.getValueWithComma(wcAmount);
        legalEntitiesItem["WCD"].wcAmountOriginal = this.getValueTooltipWithComma(wcAmount);
        legalEntitiesItem["WCD"].retentionAmount = this.getValueWithComma(retentionAmount);
        legalEntitiesItem["WCD"].retentionAmountOriginal = this.getValueTooltipWithComma(retentionAmount);
        legalEntitiesItem["WCD"].arWoRetention = this.getValueWithComma(arWoRetention);
        legalEntitiesItem["WCD"].arWoRetentionOriginal = this.getValueTooltipWithComma(arWoRetention);
        legalEntitiesItem["WCD"].wcDaysWoRetention = wcDaysWoRetention;
        legalEntitiesItem["WCD"].wcWoRetention = this.getValueWithComma(wcWoRetention);
        legalEntitiesItem["WCD"].wcWoRetentionOriginal = this.getValueTooltipWithComma(wcWoRetention);

      }

  }

  async selectCBOWBaseLegalEntity(selectedLegalEntity) {

    let currentAuthLegalEntities = JSON.parse(JSON.stringify(this.authLegalEntities));

    this.normalModeCBOWBaseLegalEntity = selectedLegalEntity;

    let isExpandedOverriddenData = _.where(this.legalEntities, { name: this.normalModeCBOWBaseLegalEntity, isExpandedOverridden: true });

    this.normalModeCBOWBaseLegalEntityExpandOverridden = isExpandedOverriddenData.length > 0 ? true : false;

    this.baseLegalEntityLevel = _.filter(this.legalEntities, function (legalEntitiesValue) {

      if ((this.reportMode == "Normal Mode" && this.normalModeCBOWBaseLegalEntity == legalEntitiesValue.name) ||
        (this.reportMode == "Detailed Mode" && this.detailedModeBaseLegalEntity == legalEntitiesValue.name))
        return legalEntitiesValue;

    }, this)[0].leLevel;

    this.spacingKeys = {};

    this._repeatCall.next();

    await this.callAllNormalModeFns();

    for (let authLegalEntity of this.authLegalEntities) {

      let tempAuthLegalEntities = _.where(currentAuthLegalEntities, { plId: authLegalEntity.plId });

      if (tempAuthLegalEntities.length > 0)
        if (!tempAuthLegalEntities[0].isExpanded)
          this.collapseLegalEntity(authLegalEntity);

    }

  }

  selectCTABaseLegalEntity(selectedLegalEntity) {

    this.normalModeCTABaseLegalEntity = selectedLegalEntity;

    // for (const mainFilterArrayItem of this.udrfService.udrfData.mainFilterArray)
    //   if (mainFilterArrayItem['filterId'] == this.ctaAppConfig['region_field_id'])
    //     mainFilterArrayItem['multiOptionSelectSearchValuesWithId'] = [selectedLegalEntity];

    // this.udrfService.udrfFunctions.callAppApi();

    this.retrieveCTAFTMData();

  }

  getSelectedCBOWCardDataType() {

    let selectedCBOWViewCard = _.where(this.cbowViewCards, { selected: true });

    return selectedCBOWViewCard[0].dataType;

  }

  getResolvedCBOWData() {

    let selectedCBOWViewCard = _.where(this.cbowViewCards, { selected: true });

    return _.where(this.resolvedCBOData, { type: selectedCBOWViewCard[0].dataType });

  }

  getResolvedLegalEntities(legalEntity) {

    let selectedCBOWViewCard = _.where(this.cbowViewCards, { selected: true });

    legalEntity[selectedCBOWViewCard[0].dataType]["yetToText"] = selectedCBOWViewCard[0].yetToText;

    return legalEntity[selectedCBOWViewCard[0].dataType];

  }

  getCBOWItemCardClass(cboItem) {

    if (cboItem.subType == this.actualSubType)
      return 'greenCard';

    else if (cboItem.subType == this.plannedMonthSubType)
      return 'redCard';

  }

  getCBOWItemDataClass(cboItem) {

    if (cboItem.subType == this.actualSubType)
      return 'valueGreen13Bold';

    else if (cboItem.subType == this.plannedMonthSubType)
      return 'valueRed13Bold';

  }

  async retrieveCBODataAPI(startDate, endDate) {

    let limits = {
      startLimit: {
        date: moment(startDate).startOf('month').format(this.dateFormat + "T00:00:00"),
        year: moment(startDate).format("YYYY"),
        week: moment(startDate).month() != 0 ? moment(startDate).startOf('month').week() : 1
      },
      endLimit: {
        date: moment(endDate).endOf('month').format(this.dateFormat + "T23:59:59"),
        year: moment(endDate).format("YYYY"),
        week: moment(endDate).month() != 11 ? moment(endDate).endOf('month').week() : 52
      },
      defaultCurrency: this.udrfService.udrfData.appliedConfig["customFields"]["defaultCurrency"]
    };

    if(this.isSnapShotHistoryButtonEnabled){
      this.snapshotHistoryData = await this.governanceReportSubService.getGovSnapshotHistory(this.activeFtmStartDate)

      if(Object.keys(this.snapshotHistoryData).length > 0){
        this.issnapshotHistoryDataAvailable = true
        
        this.snapshotHistoryData["types"] = Object.keys(this.snapshotHistoryData)
        this.snapshotHistoryData["itemType"] = []
        _.each(this.snapshotHistoryData['types'], (item) => {
          let tempItem = {
            type: item,
            isExpanded: false,
          }
  
          this.snapshotHistoryData["itemType"].push(tempItem)
        })
  
        _.each(this.snapshotHistoryData['types'], (item) => {
          _.each(this.snapshotHistoryData[item], (typeItem)=>{
            const utcMoment = moment.utc(typeItem.initiated_on);
            const localMoment = utcMoment.local();
            typeItem.initiated_on = localMoment.format('DD-MMM-YYYY HH:mm:ss');
          })    
        })
      }
      else{
        this.issnapshotHistoryDataAvailable = false
      }
    }

    
    return new Promise(resolve => {

      this.isGovDataLoading = true;

      this.isOtherGroupDataLoading = true

      this.governanceReportSubService.getSundayGovPivotData(limits)
        .pipe(takeUntil(this._onDestroy)).pipe(takeUntil(this._repeatCall))
        .subscribe(async res => {

          this.isGovDataLoading = false;
          this.isOtherGroupDataLoading = false;


          resolve(res["data"]);

        }, err => {
          this.isGovDataLoading = false;
          this.isOtherGroupDataLoading = false;

          this.showErrorMessage(err);
        });

    });

  }

  changeNormalModeView() {

    if (this.udrfService.udrfData.areSortAndFiltersLoading || this.isSettingsLoading || this.legalEntitiesLoading || this.isGovDataLoading)
      return this.utilityService.showToastMessage("Kindly Wait, Data is being Loaded !");

    if (this.normalModeView == 'CTA' && (this.udrfService.udrfData.areSortAndFiltersLoading || this.udrfService.udrfData.isItemDataLoading))
      return this.utilityService.showToastMessage("Kindly Wait, Data is being Loaded !");

    this.normalModeView = this.normalModeView == 'CBOW' ? 'CTA' : 'CBOW';

    this.calculateAuthLe();

    if (this.normalModeView == 'CTA') {

      this.govUdrfData = JSON.parse(JSON.stringify(this.udrfService.udrfData));

      if (this.ctaAppConfig)
        this.router.navigate([{ outlets: { 'lcdp-outlet': ['lcdp', this.ctaAppConfig['mysql_app_id'], this.ctaAppConfig['lcdp_app_label'], this.ctaAppConfig['lcdp_app_id'] ]} }], { relativeTo: this.activatedRoute });

      else
        this.utilityService.showToastMessage("CTA App Config not Found !");

    }

    else
      this.navigateToGov();

  }

  navigateToGov = () => {

    this.router.navigate(['../'], { relativeTo: this.activatedRoute });

    if (Object.keys(this.govUdrfData).length)
      setTimeout(async () => {
        
        await this.udrfService.getAppUdrfConfig(this.applicationId, this.retrieveFirstTimeData.bind(this));
        this.udrfService.udrfData = JSON.parse(JSON.stringify(this.govUdrfData));
        
      }, 1);

  }

  changeNormalModeArAgingInterval(normalModeArAgingInterval) {

    this.spacingKeys = {};

    this._repeatCall.next();

    if (this.normalModeArAgingInterval != normalModeArAgingInterval)
      this.normalModeArAgingInterval = normalModeArAgingInterval;

    else
      this.normalModeArAgingInterval = 0;

    this.callAllNormalModeFns();

  }

  changeDetailedModeArAgingInterval(detailedModeArAgingInterval) {

    this.spacingKeys = {};

    this._repeatCall.next();

    if (this.detailedModeArAgingInterval != detailedModeArAgingInterval)
      this.detailedModeArAgingInterval = detailedModeArAgingInterval;

    else
      this.detailedModeArAgingInterval = 0;

    this.callAllNormalModeFns();

  }

  changePmoDbDataMonthInterval(pmoDbDataMonthInterval) {

    this.spacingKeys = {};

    this._repeatCall.next();

    this.pmoDb.pmoDbDataMonthInterval = pmoDbDataMonthInterval;

    this.pmoDb.pmoDbDataMonthIntervalCount = parseInt(pmoDbDataMonthInterval.replace("M", ""));

    let currentMonthInterval = _.where(this.pmoDb.monthIntervals, { monthInterval: this.pmoDb.pmoDbDataMonthInterval });

    this.pmoDb.pmoDbDataMonthCount = currentMonthInterval && currentMonthInterval.length > 0 ? currentMonthInterval[0].monthCount : this.pmoDb.monthIntervals[0].monthCount;

    let finalCurrentMonthInterval = currentMonthInterval && currentMonthInterval.length > 0 ? currentMonthInterval[0] : this.pmoDb.monthIntervals[0];

    this.pmoDb.currentWeekIntervals = finalCurrentMonthInterval.weekIntervals;

    this.calculatePmoDbHeaderData();

    this.determinePmoWidgetItemColorClasses();

  }

  determinePmoWidgetItemColorClasses() {

    let currentMonthCount = this.selectedMonthData.monthInterval;

    for (let resolvedPmoDbPlDataItem of this.pmoDb.resolvedPmoDbData) {

      resolvedPmoDbPlDataItem["color_class_" + currentMonthCount] = "";

      for (let resolvedPmoDbSubPlDataItem of resolvedPmoDbPlDataItem.subItems) {

        resolvedPmoDbSubPlDataItem["color_class_" + currentMonthCount] = "";

        for (let resolvedPmoDbVerticalDataItem of resolvedPmoDbSubPlDataItem.subItems) {

          resolvedPmoDbVerticalDataItem["color_class_" + currentMonthCount] = "";

          for (let resolvedPmoDbProjectDataItem of resolvedPmoDbVerticalDataItem.subItems)
            resolvedPmoDbProjectDataItem["color_class_" + currentMonthCount] = "";

        }

      }

    }

    for (let currentWeekInterval of this.pmoDb.currentWeekIntervals)
      for (let resolvedPmoDbPlDataItem of this.pmoDb.resolvedPmoDbData) {

        if (resolvedPmoDbPlDataItem["color_class_" + currentMonthCount] != 'value13RedBold')
          resolvedPmoDbPlDataItem["color_class_" + currentMonthCount] = resolvedPmoDbPlDataItem["ml_overdue_count" + currentWeekInterval.weekInterval + currentWeekInterval.monthCount] > 0 ? 'value13RedBold' : (resolvedPmoDbPlDataItem["ml_actual_count" + currentWeekInterval.weekInterval + currentWeekInterval.monthCount] > 0 ? 'value13GreenBold' : (resolvedPmoDbPlDataItem["color_class_" + currentMonthCount] != 'value13GreenBold' ? '' : 'value13GreenBold'));

        for (let resolvedPmoDbSubPlDataItem of resolvedPmoDbPlDataItem.subItems) {

          if (resolvedPmoDbSubPlDataItem["color_class_" + currentMonthCount] != 'value13RedBold')
            resolvedPmoDbSubPlDataItem["color_class_" + currentMonthCount] = resolvedPmoDbSubPlDataItem["ml_overdue_count" + currentWeekInterval.weekInterval + currentWeekInterval.monthCount] > 0 ? 'value13RedBold' : (resolvedPmoDbSubPlDataItem["ml_actual_count" + currentWeekInterval.weekInterval + currentWeekInterval.monthCount] > 0 ? 'value13GreenBold' : (resolvedPmoDbSubPlDataItem["color_class_" + currentMonthCount] != 'value13GreenBold' ? '' : 'value13GreenBold'));

          for (let resolvedPmoDbVerticalDataItem of resolvedPmoDbSubPlDataItem.subItems) {

            if (resolvedPmoDbVerticalDataItem["color_class_" + currentMonthCount] != 'value13RedBold')
              resolvedPmoDbVerticalDataItem["color_class_" + currentMonthCount] = resolvedPmoDbVerticalDataItem["ml_overdue_count" + currentWeekInterval.weekInterval + currentWeekInterval.monthCount] > 0 ? 'value13RedBold' : (resolvedPmoDbVerticalDataItem["ml_actual_count" + currentWeekInterval.weekInterval + currentWeekInterval.monthCount] > 0 ? 'value13GreenBold' : (resolvedPmoDbVerticalDataItem["color_class_" + currentMonthCount] != 'value13GreenBold' ? '' : 'value13GreenBold'));

            for (let resolvedPmoDbProjectDataItem of resolvedPmoDbVerticalDataItem.subItems)
              if (resolvedPmoDbProjectDataItem["color_class_" + currentMonthCount] != 'value13RedBold')
                resolvedPmoDbProjectDataItem["color_class_" + currentMonthCount] = resolvedPmoDbProjectDataItem["ml_overdue_count" + currentWeekInterval.weekInterval + currentWeekInterval.monthCount] > 0 ? 'value13RedBold' : (resolvedPmoDbProjectDataItem["ml_actual_count" + currentWeekInterval.weekInterval + currentWeekInterval.monthCount] > 0 ? 'value13GreenBold' : (resolvedPmoDbProjectDataItem["color_class_" + currentMonthCount] != 'value13GreenBold' ? '' : 'value13GreenBold'));

          }

        }

      }

  }

  calculatePmoDbHeaderData() {

    this.resetPmoHeaderData();

    this.calculatePmoHeaderDates();

    for (let milestonePmoDbDataItem of this.pmoDb.milestonePmoDbData)
      if (milestonePmoDbDataItem.is_spill_over == 0) {

        let doesWeekMatch = _.where(this.selectedMonthData.weekIntervals, { weekInterval: "W" + milestonePmoDbDataItem.week, monthCount: milestonePmoDbDataItem.month - 1 });

        if (doesWeekMatch != null && doesWeekMatch.length > 0)
          this.calculatePmoHeaderValues(milestonePmoDbDataItem);

      }

      else if (parseInt(this.selectedMonthData.monthInterval.replace("M", "")) == 0 && milestonePmoDbDataItem.is_spill_over == 1)
        this.calculatePmoHeaderValues(milestonePmoDbDataItem);

    this.pmoDb.totAcMlValue = this.getValueWithComma(this.pmoDb.totAcMlValueOriginal);
    this.pmoDb.totPlMlValue = this.getValueWithComma(this.pmoDb.totPlMlValueOriginal);
    this.pmoDb.totOvMlValue = this.getValueWithComma(this.pmoDb.totOvMlValueOriginal);

    this.pmoDb.totAcMlValueTooltip = this.getValueTooltipWithComma(this.pmoDb.totAcMlValueOriginal);
    this.pmoDb.totPlMlValueTooltip = this.getValueTooltipWithComma(this.pmoDb.totPlMlValueOriginal);
    this.pmoDb.totOvMlValueTooltip = this.getValueTooltipWithComma(this.pmoDb.totOvMlValueOriginal);

  }

  selectedMonthData: any;

  resetPmoHeaderData() {

    this.pmoDb.totAcMlCount = 0;
    this.pmoDb.totPlMlCount = 0;
    this.pmoDb.totOvMlCount = 0;

    this.pmoDb.totAcAcCount = 0;
    this.pmoDb.totPlAcCount = 0;
    this.pmoDb.totOvAcCount = 0;

    this.pmoDb.totAcSoCount = 0;
    this.pmoDb.totPlSoCount = 0;
    this.pmoDb.totOvSoCount = 0;

    this.pmoDb.totAcPhCount = 0;
    this.pmoDb.totPlPhCount = 0;
    this.pmoDb.totOvPhCount = 0;

    this.pmoDb.totAcWpCount = 0;
    this.pmoDb.totPlWpCount = 0;
    this.pmoDb.totOvWpCount = 0;

    this.pmoDb.totAcMlValueOriginal = 0;
    this.pmoDb.totPlMlValueOriginal = 0;
    this.pmoDb.totOvMlValueOriginal = 0;

    this.pmoDb.totAcMlValue = "0";
    this.pmoDb.totPlMlValue = "0";
    this.pmoDb.totOvMlValue = "0";

    this.pmoDb.totAcMlValueTooltip = "0";
    this.pmoDb.totPlMlValueTooltip = "0";
    this.pmoDb.totOvMlValueTooltip = "0";

  }

  calculatePmoHeaderDates() {

    let selectedMonthData = _.where(this.pmoDb.monthIntervals, { monthInterval: this.pmoDb.pmoDbDataMonthInterval });

    this.selectedMonthData = selectedMonthData != null && selectedMonthData.length > 0 ? selectedMonthData[0] : "";

  }

  calculatePmoHeaderValues(milestonePmoDbDataItem) {

    if (_.contains(this.dmBaseLegalEntityRollDowns, milestonePmoDbDataItem.vertical_id)) {

      if (milestonePmoDbDataItem.gantt_type_name == "Milestone") {

        this.pmoDb.totAcMlCount += milestonePmoDbDataItem.actual_count;
        this.pmoDb.totPlMlCount += milestonePmoDbDataItem.planned_count;
        this.pmoDb.totOvMlCount += milestonePmoDbDataItem.overdue_count;

      }

      else if (milestonePmoDbDataItem.gantt_type_name == "Activity") {

        this.pmoDb.totAcAcCount += milestonePmoDbDataItem.actual_count;
        this.pmoDb.totPlAcCount += milestonePmoDbDataItem.planned_count;
        this.pmoDb.totOvAcCount += milestonePmoDbDataItem.overdue_count;

      }

      else if (milestonePmoDbDataItem.gantt_type_name == "Sign Off") {

        this.pmoDb.totAcSoCount += milestonePmoDbDataItem.actual_count;
        this.pmoDb.totPlSoCount += milestonePmoDbDataItem.planned_count;
        this.pmoDb.totOvSoCount += milestonePmoDbDataItem.overdue_count;

      }

      else if (milestonePmoDbDataItem.gantt_type_name == "Phase") {

        this.pmoDb.totAcPhCount += milestonePmoDbDataItem.actual_count;
        this.pmoDb.totPlPhCount += milestonePmoDbDataItem.planned_count;
        this.pmoDb.totOvPhCount += milestonePmoDbDataItem.overdue_count;

      }

      else if (milestonePmoDbDataItem.gantt_type_name == "Work Package") {

        this.pmoDb.totAcWpCount += milestonePmoDbDataItem.actual_count;
        this.pmoDb.totPlWpCount += milestonePmoDbDataItem.planned_count;
        this.pmoDb.totOvWpCount += milestonePmoDbDataItem.overdue_count;

      }

      this.pmoDb.totAcMlValueOriginal += milestonePmoDbDataItem.actual_value;
      this.pmoDb.totPlMlValueOriginal += milestonePmoDbDataItem.planned_value;
      this.pmoDb.totOvMlValueOriginal += milestonePmoDbDataItem.overdue_value;

    }

  }

  changeDetailedModeCTAViewInterval(detailedModeCtaViewInterval) {

    if (this.detailedModeCtaViewInterval != detailedModeCtaViewInterval)
      this.detailedModeCtaViewInterval = detailedModeCtaViewInterval;

    else
      this.detailedModeCtaViewInterval = "Overall";

    this.retrieveCTAFTMData();

  }

  changeNormalModeCTAViewInterval(normalModeCtaViewInterval) {

    if (this.normalModeCtaViewInterval != normalModeCtaViewInterval)
      this.normalModeCtaViewInterval = normalModeCtaViewInterval;

    else
      this.normalModeCtaViewInterval = "Overall";

    this.retrieveCTAFTMData();

  }

  isResetFiltersVisible() {

    if (this.reportMode == "Normal Mode") {

      if (this.normalModeView == 'CBOW') {

        let selectedCbowViewCards = _.where(this.cbowViewCards, { selected: true });

        if (this.normalModeCBOWBaseLegalEntity != this.highestPl || this.reportMonthInterval != "This Month" ||
          selectedCbowViewCards[0].dataType != "Collection")
          return true;

        else
          return false;

      }

      else if (this.normalModeView == 'CTA') {

        let selectedCtaViewCards = _.where(this.ctaViewCards, { selected: true });

        if (this.normalModeCTABaseLegalEntity != this.highestPl || this.reportMonthInterval != "This Month" ||
          selectedCtaViewCards[0].status != "Total" || this.normalModeCtaViewInterval != "Overall")
          return true;

        else
          return false;

      }

    }

    else if (this.reportMode == "Detailed Mode") {

      if (this.detailedModeBaseLegalEntity != this.highestPl || this.reportMonthInterval != "This Month" ||
        this.detailedModeArAgingInterval != 0 || this.detailedModeCtaViewInterval != "Overall")
        return true;

      else
        return false;

    }

  }

  isFromReset = false;

  resetFilters() {

    this.isFromReset = true;

    this.udrfService.udrfFunctions.clearConfig();

    this.spacingKeys = {};

    this._repeatCall.next();

    const monthYearDate = this.isMISPostingDateDefault ?  this.getFormattedDate(moment().month(moment(this.misPostingPeriod['start_date']).month()).year(moment(this.misPostingPeriod['start_date']).year())) : this.getFormattedDate(moment().month(moment(this.ftmDate).month()).year(moment(this.ftmDate).year()));

    this.changeMonthYear(monthYearDate);

    if (this.reportMode == "Normal Mode") {

      if (this.normalModeView == 'CBOW') {

        this.normalModeCBOWBaseLegalEntity = this.highestPl;

        this.normalModeArAgingInterval = 0;

        this.callAllNormalModeFns();

        this.selectCBOWCard(0);

      }

      else if (this.normalModeView == 'CTA') {

        this.normalModeCTABaseLegalEntity = this.highestPl;

        this.normalModeCtaViewInterval = "Overall";

        this.retrieveCTAFTMData();

        this.selectCTACard(0);

      }

    }

    else if (this.reportMode == "Detailed Mode") {

      this.detailedModeBaseLegalEntity = this.highestPl;

      this.detailedModeArAgingInterval = 0;

      this.detailedModeCtaViewInterval = "Overall";

      this.callAllDetailedModeFns();

    }

  }

  getFormattedDate(monthYearDate) {

    return this.utilityService.getFormattedDate(monthYearDate, 1, 15, 0, 0, 0);

  }

  selectMonth(normalizedMonth: Moment, datepicker: MatDatepicker<Moment>) {

    this.resolvedCBOData = []
    this.otherViewGroupByData = {}
    this.isOtherGroupDataLoading = true

    const monthYearDate = this.monthYearDate.value;

    normalizedMonth = moment(normalizedMonth);

    if (monthYearDate.month() != normalizedMonth.month()) {

      this.getFormattedDate(monthYearDate.month(normalizedMonth.month()).year(normalizedMonth.year()));

      this.changeMonthYear(monthYearDate);

    }
    else{
      this.getFormattedDate(monthYearDate.month(normalizedMonth.month()).year(normalizedMonth.year()));
      this.changeMonthYear(monthYearDate);
    }

    datepicker.close();

  }

  selectYear(normalizedYear: Moment) {

    this.resolvedCBOData = []
    this.otherViewGroupByData = {}
    this.isOtherGroupDataLoading = true

    const monthYearDate = this.monthYearDate.value;

    if (monthYearDate.year() != normalizedYear.year()) {

      monthYearDate.year(normalizedYear.year());

      this.changeMonthYear(monthYearDate);

    }

  }

  changeMonthYear(monthYearDate) {

    this.resolvedCBOData = []
    this.otherViewGroupByData = {}
    this.isOtherGroupDataLoading = true

    const currentMonthYearDate = this.getFormattedDate(moment());

    if (moment(monthYearDate).isSameOrBefore(currentMonthYearDate))
      this.plannedMonthSubType = this.originalPlannedMonthSubType;

    else
      this.plannedMonthSubType = this.plannedSubType;

    this.monthYearDate.setValue(monthYearDate);

    this.calculatePoCTargetToDate();

    this.alterActiveDates();

  }

  goToPreviousMonth() {

    this.resolvedCBOData = []
    this.otherViewGroupByData = {}
    this.isOtherGroupDataLoading = true

    const monthYearDate = this.getFormattedDate(this.monthYearDate.value.subtract(1, 'M'));

    this.changeMonthYear(monthYearDate);

  }

  goToNextMonth() {

    this.resolvedCBOData = []
    this.otherViewGroupByData = {}
    this.isOtherGroupDataLoading = true

    const monthYearDate = this.getFormattedDate(this.monthYearDate.value.add(1, 'M'));

    this.changeMonthYear(monthYearDate);

  }

  alterActiveDates() {

    this.spacingKeys = {};

    this._repeatCall.next();

    if (moment(this.getFormattedDate(moment()).format(this.dateFormat)).isSame(this.monthYearDate.value.format(this.dateFormat))) {

      this.reportMonthInterval = "This Month";

      this.activeFtmDate = this.ftmDate;

      this.activeFtmPyDate = moment(this.ftmDate).subtract(1, 'year').format("YYYY-MM-27");

      this.activeFtmStartDate = this.ftmStartDate;

      this.activeFtmEndDate = this.ftmEndDate;

    }

    else {

      this.reportMonthInterval = "Custom Month";

      this.activeFtmDate = moment(this.monthYearDate.value).format("YYYY-MM-27");

      this.activeFtmPyDate = moment(this.monthYearDate.value).subtract(1, 'year').format("YYYY-MM-27");

      this.activeFtmStartDate = moment(this.monthYearDate.value).startOf('month').format(this.dateFormat);

      this.activeFtmEndDate = moment(this.monthYearDate.value).endOf('month').format(this.dateFormat);

    }

    // if (!this.isFromReset) {

    //   if (this.reportMode == "Normal Mode")
    //     this.callAllNormalModeFns();

    //   else
    //     this.callAllDetailedModeFns();

    // }

    if (this.reportMode == "Normal Mode")
      this.callAllNormalModeFns();
    else
      this.callAllDetailedModeFns();

  }

  getCurrentMonthYear() {
    return moment(this.activeFtmDate).format('MMM YYYY');
  }

  roundToZero(value) {
    return this.utilityService.roundToZero(value);
  }

  roundToTwo(value) {
    return this.utilityService.roundToTwo(value);
  }

  convertToCrOrMn(value) {
    return this.udrfService.udrfData.appliedConfig["customFields"]["defaultCurrency"] == "INR" ? this.utilityService.convertToCr(value) : this.utilityService.convertToMillion(value);
  }

  convertFromCr(value) {
    return this.udrfService.udrfData.appliedConfig["customFields"]["defaultCurrency"] == "INR" ? this.utilityService.convertFromCr(value) : this.utilityService.convertFromMillion(value);
  }

  showErrorMessage(err) {

    let errReportingTeams = "KEBS";

    this.utilityService.showErrorMessage(err, errReportingTeams);

  }

  openFullScreen() {

    if (this.elem.requestFullscreen)
      this.elem.requestFullscreen();

    /* Firefox */
    else if (this.elem.mozRequestFullScreen)
      this.elem.mozRequestFullScreen();

    /* Chrome, Safari and Opera */
    else if (this.elem.webkitRequestFullscreen)
      this.elem.webkitRequestFullscreen();

    /* IE/Edge */
    else if (this.elem.msRequestFullscreen)
      this.elem.msRequestFullscreen();

  }

  closeFullScreen() {

    if (this.document.fullscreenElement || this.document.mozFullScreenElement ||
      this.document.webkitFullscreenElement || this.document.msFullscreenElement)
      if (this.document.exitFullscreen)
        this.document.exitFullscreen();

      /* Firefox */
      else if (this.document.mozCancelFullScreen)
        this.document.mozCancelFullScreen();

      /* Chrome, Safari and Opera */
      else if (this.document.webkitExitFullscreen)
        this.document.webkitExitFullscreen();

      /* IE/Edge */
      else if (this.document.msExitFullscreen)
        this.document.msExitFullscreen();

  }

  async openNewReleasesComponent() {

    this.notifyRelease = Boolean(await this.sharedLazyLoadedComponentsService.openNewReleasesModal(this.applicationId, this.dialog));
  }

  async getNotifyReleases() {

    this.sharedLazyLoadedComponentsService.getNotifyReleases(this.applicationId)
      .pipe(takeUntil(this._onDestroy)).pipe(takeUntil(this._repeatCall))
      .subscribe(async res => {

        let existingItem = localStorage.getItem("Application" + this.applicationId + "Version");

        if (existingItem != res['data']['version']) {
          this.notifyReleaseAnimated = true;
          await this.openNewReleasesComponent();
          this.notifyReleaseAnimated = false;
          this.notifyRelease = res['data']['_id'];
          localStorage.setItem("Application" + this.applicationId + "Version", res['data']['version']);

        }
      })
  }

  async getCurrencyInformation() {

    const { DisplayCurrencyComponent } = await import('src/app/modules/reports/features/mis-report/components/display-currency/display-currency.component');

    const openHierarchyViewComponent = this.dialog.open(DisplayCurrencyComponent, {
      height: '80%',
      width: '40%',
    });

    openHierarchyViewComponent.afterClosed().subscribe(async (result: any) => { });

  }


  openAdminSettings = async () => {

    let ngrAccessList = _.pluck(_.filter(this.rolesService.roles, (roleObj: any) => {
      return ((roleObj.application_id == 71) && (roleObj.object_value == "*") && (roleObj.operation == "*"))
    }), "object_id")

    let settingsTemplate = {
      canFreezeBillingPlan: _.contains(ngrAccessList, 71001),
      canFreezeOBVPlan: _.contains(ngrAccessList, 71002),
      canAddMilestone: _.contains(ngrAccessList, 71005),
      canRemoveMilestone: _.contains(ngrAccessList, 71006),
      canAddOpportunity: _.contains(ngrAccessList, 71003),
      canRemoveOpportunity: _.contains(ngrAccessList, 71004),
      canOnDemandFreezeOpportunityTool: _.contains(ngrAccessList, 71008),
      lableConfigs: {}
    }

    let canOnDemandFreezeOpportunityTool =  this.masterConfigs.find(item => item.type == "canOnDemandFreezeOpportunityTool") || [];

    let lableConfigs = {
      canOnDemandFreezeOpportunityTool: JSON.parse(canOnDemandFreezeOpportunityTool?.config || "[]"),
    }

    settingsTemplate.lableConfigs = lableConfigs

    const { NgrAdminToolsComponent } = await import('../../components/ngr-admin-tools/ngr-admin-tools.component');

    const openAdminToolsModalComponent = this.dialog.open(NgrAdminToolsComponent, {
      minHeight: '70%',
      maxWidth: '90%',
      minWidth: '80%',
      data: settingsTemplate
    });

    openAdminToolsModalComponent.afterClosed().subscribe(async res => {
      console.log(res)
      if(res) {
        this.callApisAgain()
      }
    })


  }

  ngOnDestroy() {

    this.utilityService.addIsFullScreenAppValue(false);

    this.closeFullScreen();

    this._onDestroy.next();
    this._onDestroy.complete();

    this._repeatCall.complete();

    this.udrfService.resetUdrfData();

    if (this.routeSubscription)
      this.routeSubscription.unsubscribe();

  }

  expandAllOrCollapseSnapshotHistory(itemType, index){
    this.snapshotHistoryData.itemType[index].isExpanded = !this.snapshotHistoryData.itemType[index].isExpanded
  }


  limitedSnapshotHistoryData(itemType) {
    return this.snapshotHistoryData[itemType].slice(0,5)
  }

  async changeGroupView(viewName) {
    let currentView = this.groupMenuButtons.filter(item => item.group_name == viewName)
    this.selectedViewGroupMode = currentView[0].group_name
    this.selectedViewGroupFilterField = currentView[0].filter_field
    console.log("Selected View Mode : ", this.selectedViewGroupMode)
    if (this.selectedViewGroupMode != 'Region') {
      // console.log("Other Type Data Source : ",this.otherGroupBysourceCBOData)
      this.callOtherViewTypeCBODataType()
    }
    else if (this.selectedViewGroupMode == 'Region') {
      console.log("Other Type Data Source : ", this.otherGroupBysourceCBOData)
      this.callAllNormalModeFns()
    }
  }

  async callOtherViewTypeCBODataType() {
    this.isOtherGroupDataLoading = true
    this.otherViewGroupByData = {}
    this.baseLegalEntityLevelCbo = 0
    this.resetNonDetailedModeData()
    let cboTypes = _.pluck(this.cbowViewCards, "dataType")
    let selectedType = this.getSelectedCBOWCardDataType()

    this.otherViewGroupByData[selectedType] = await this.retrieveCBODataForOtherViewGroupType(selectedType)
    console.log("Other View Type Data  for Type : ", selectedType, " => ", this.otherViewGroupByData)

    console.log("Summary Card Data : ",this.cbowViewCards)

    // if(this.otherViewGroupByData[selectedType]?.arr.length == 0){
    //   let cardIndex = this.cbowViewCards.findIndex(item => item.dataType == selectedType);

    //   if (cardIndex != -1) {
    //     //  Summary Card Data
    //     this.cbowViewCards[cardIndex].actualValue = '-'
    //     this.cbowViewCards[cardIndex].plannedValue = '-'
    //     this.cbowViewCards[cardIndex].yetToValue = '-'

    //     // Summary Card ToolTip Data
    //     this.cbowViewCards[cardIndex].actualValueOriginal = '0'
    //     this.cbowViewCards[cardIndex].plannedValueOriginal = '0'
    //     this.cbowViewCards[cardIndex].yetToValueOriginal = '0'
    //   }
     
    // }
   
    // for (let cboType of cboTypes) {
    //   let OtherTypeViewData = await this.retrieveCBODataForOtherViewGroupType(cboType)
    //   this.otherViewGroupByData[cboType] = OtherTypeViewData
    //   console.log("Other View Type Data  for Type : ", cboType, " => ", this.otherViewGroupByData)
    // }
  }

  getResolvedCBOWDataForOtherGroupView() {
    let selectedCBOWViewCard = _.where(this.cbowViewCards, { selected: true });
    console.log("current View - ", this.selectedViewGroupMode, " data => ", _.where(this.resolvedCBOData, { type: selectedCBOWViewCard[0].dataType }))
    return _.where(this.resolvedCBOData, { type: selectedCBOWViewCard[0].dataType });
  }

  async retrieveCBODataForOtherViewGroupType(type) {
    let data = this.sourceCBOData
    let sequenceFormat = this.selectedViewGroupFilterField.filter(item => item.type == type)
    if(sequenceFormat && sequenceFormat.length > 0){

    sequenceFormat = sequenceFormat[0]?.filter_field
      console.log("Sequence Format for Type : ", type, " => ", sequenceFormat)
      data = data.filter(item => item.type == type)
  
      let groupedData
  
      if(data && data.length > 0){
        groupedData = await this.recurrsionFormat(data, sequenceFormat, sequenceFormat.length);
      }
      else{
        groupedData = {
          arr: [],
          planned_value: 0,
          actual_value: 0,
        };
      }
  
      this.isOtherGroupDataLoading = false
  
      return groupedData
  
    }
    else{
      this.isOtherGroupDataLoading = false

      return  {
        arr: [],
        planned_value: 0,
        actual_value: 0,
      };
    }

  }

  recurrsionFormat = async (data, sequence, seqLength) => {

    if (sequence.length === 0) {
      if (data.length === 0) {
        return {
          arr: [],
          planned_value: 0,
          actual_value: 0,
          colorScheme: {},
        };
      }

      // Calculate values based on the data array
      let planned_value = 0;
      let actual_value = 0;
      let colorScheme = {};

      data.forEach(item => {
        if (item.subType == "Planned" || item.subType == "Planned-Month") {
          planned_value += item.value || 0;
        } else if (item.subType === "Actual") {
          actual_value += item.value || 0;
        }

        if (item.color_scheme) {
          colorScheme[item.color_scheme.id] = item.color_scheme;
        }
      });

      return {
        arr: [],
        planned_value: planned_value,
        actual_value: actual_value,
        colorScheme: colorScheme,
      };
    }

    let grouping_param = sequence[0];
    let subset = _.groupBy(data, grouping_param);
    let groupedData = Object.keys(subset);
    // groupedData = groupedData.filter(item => (item != null && item != undefined))
    let result_arr = [];
    let depth = Number(seqLength) - Number(sequence.length)

    let splicedData = sequence.slice(1)
    for (let i = 0; i < groupedData.length; i++) {
      let key = groupedData[i]

      if (key && (key != null && key != undefined && key != "undefined" && key != "null")) {
        let result = null
        if (subset[key].length > 0) {
          result = await this.recurrsionFormat(subset[key], splicedData, seqLength)
        }

        let template = {
          name: key,
          planned_value: result.planned_value,
          actual_value: result.actual_value,
          has_children: (result.arr?.length > 0) ? true : false,
          children: result.arr,
          depth: depth,
          colorScheme: result.colorScheme,
        }

        result_arr.push(template)
      }
      else {
        console.log("Not valid Key Data : ", groupedData)
      }



      if (i == groupedData.length - 1) {
        // Aggregate planned_value and actual_value for this level
        let totalPlannedValue = _.reduce(result_arr, (sum, item) => sum + (item.planned_value || 0), 0);
        let totalActualValue = _.reduce(result_arr, (sum, item) => sum + (item.actual_value || 0), 0);

        let maxDepth = Math.max(
          ...result_arr.map((item) =>
            item.children.length > 0 ? item.children[0].depth : depth
          )
        );
        // Aggregate color scheme data
      
        // Include colorScheme only at maximum depth
        let aggregatedColorScheme = result_arr.reduce((acc, item) => {
          if (item.depth == maxDepth) {
            return { ...acc, ...item.colorScheme };
          }
          return acc;
        }, {});

  
        return {
          arr: result_arr,
          planned_value: totalPlannedValue,
          actual_value: totalActualValue,
          colorScheme: aggregatedColorScheme,
        };

      }


    }

  }
}