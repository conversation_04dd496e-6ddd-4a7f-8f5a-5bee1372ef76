import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { GreytLeaveIntegrationRoutingModule } from './greyt-leave-integration-routing.module';
import { GreyTIntegrationReportComponent } from './pages/grey-t-integration-report/grey-t-integration-report.component';

import { DxDataGridModule, DxTemplateModule } from 'devextreme-angular';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatSelectModule } from '@angular/material/select';
import { NgxMatSelectSearchModule } from 'ngx-mat-select-search';
import { MatStepperModule } from '@angular/material/stepper';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { NgxSpinnerModule } from "ngx-spinner";

@NgModule({
  declarations: [GreyTIntegrationReportComponent],
  imports: [
    CommonModule,
    GreytLeaveIntegrationRoutingModule,
    DxDataGridModule,
    DxTemplateModule,
    MatProgressSpinnerModule,
    MatFormFieldModule,
    MatInputModule,
    MatDatepickerModule,
    MatNativeDateModule,
    FormsModule,
    ReactiveFormsModule,
    MatIconModule,
    MatTooltipModule,
    MatSelectModule,
    NgxMatSelectSearchModule,
    MatCheckboxModule,
    MatStepperModule,
    NgxSpinnerModule
  ]
})
export class GreytLeaveIntegrationModule { }
