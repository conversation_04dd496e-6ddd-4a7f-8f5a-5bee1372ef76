<div class="bg-container">
  <div class="report-screen">
    <div class="align-items-center" style="margin-bottom: 16px">
      <div class="align-items-center">
        <div class="back-btn" (click)="goToReportsMainScreen()">
          <mat-icon class="back-btn-icon">chevron_left</mat-icon>
          Back
        </div>
        <div class="header-title">GreyT - KEBS Leave Integration Report</div>
        <div style="padding-left: 10px">
          <div class="btn-style" (click)="viewErrorLog()">View Error Log</div>
        </div>
        <div style="padding-left: 10px">
            <div class="btn-style" (click)="viewSyncLog()">View Sync Log</div>
          </div>
        <div style="padding-left: 10px">
          <div
            class="btn-style"
            style="padding-left: 10px"
            (click)="resyncLeave()"
          >
            Resync Leave
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="report-height" *ngIf="showSyncData">
    <dx-data-grid
      #dataGrid
      class="data-grid"
      [height]="dynamicGridReportHeight"
      [dataSource]="syncData"
      [showBorders]="true"
      [columnAutoWidth]="true"
      [showColumnLines]="true"
      [showRowLines]="true"
      (onExporting)="onExporting($event)"
    >
      <dxo-filter-row [visible]="true"></dxo-filter-row>
      <dxo-header-filter [visible]="true"></dxo-header-filter>
      <dxo-column-chooser
        [allowSearch]="true"
        [enabled]="true"
        mode="select"
      ></dxo-column-chooser>
      <dxo-export [enabled]="true"></dxo-export>
      <dxo-scrolling mode="infinite"></dxo-scrolling>
      <dxi-column caption="Date" dataField="date"></dxi-column>
      <dxi-column caption="Associate Id" dataField="associate_id"></dxi-column>
      <dxi-column
        caption="Leave Start Date"
        dataField="leave_start_date"
      ></dxi-column>
      <dxi-column
      caption="Leave End Date"
      dataField="leave_end_date"
    ></dxi-column>
      <dxi-column
        caption="Half Day Leave"
        dataField="is_half_day_leave"
      ></dxi-column>
      <dxi-column caption="External Id" dataField="external_id"></dxi-column>
      <dxi-column caption="Reason" dataField="reason"></dxi-column>
      <dxi-column caption="Sync Status" dataField="is_synced"></dxi-column>
      <dxi-column caption="Error" dataField="err"></dxi-column>
    </dx-data-grid>
  </div>
  <div class="report-height" *ngIf="showErrorLogData">
    <dx-data-grid
      #dataGrid
      class="data-grid"
      [height]="dynamicGridReportHeight"
      [dataSource]="errorLogData"
      [showBorders]="true"
      [columnAutoWidth]="true"
      [showColumnLines]="true"
      [showRowLines]="true"
      (onExporting)="onExporting($event)"
    >
      <dxo-filter-row [visible]="true"></dxo-filter-row>
      <dxo-header-filter [visible]="true"></dxo-header-filter>
      <dxo-column-chooser
        [allowSearch]="true"
        [enabled]="true"
        mode="select"
      ></dxo-column-chooser>
      <dxo-export [enabled]="true"></dxo-export>
      <dxo-scrolling mode="infinite"></dxo-scrolling>

      <dxi-column caption="Date" dataField="date"></dxi-column>
      <dxi-column caption="Error Code" dataField="errorCode"></dxi-column>
      <dxi-column
        caption="Error Description"
        dataField="errorDescription"
      ></dxi-column>
      <dxi-column caption="Created By" dataField="created_by"></dxi-column>
      <dxi-column caption="Source" dataField="source"></dxi-column>
    </dx-data-grid>
  </div>
</div>
