.bg-container {
    background-color: #f1f3f8;
    overflow: hidden;
  
    .report-screen {
      margin-left: 24px;
      margin-right: 24px;
      margin-bottom: 24px;
      margin-top: 24px;
      height: var(--dynamicHeight);
    }
  
    .report-height {
      height: var(--dynamicReportHeight);
    }
  
    .align-items-center {
      display: flex;
      align-items: center;
    }
  
    .align-items-center-justify {
      display: flex;
      align-items: center;
      justify-content: center;
    }
  
    .align-items-space-between {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  
    .flex-direction-column {
      display: flex;
      flex-direction: column;
    }
  
    .back-btn {
      display: flex;
      justify-content: center;
      align-items: center;
      color: #111434;
      font-size: 11px;
      font-weight: 400;
      border: 1px solid #8b95a5;
      border-radius: 4px;
      padding: 4px;
      cursor: pointer;
      width: fit-content;
  
      mat-icon {
        width: 0px !important;
        height: 15px !important;
      }
  
      .back-btn-icon {
        font-size: 18px;
        color: #111434;
        margin-right: 20px;
        margin-bottom: 2px;
      }
    }
  
    .header-title {
      font-size: 16px;
      font-weight: 600;
      color: #111434;
      margin-left: 20px;
    }
  
    .form-field {
      width: 100%;
    }
  
    .btn-line-style {
      display: flex;
      justify-content: flex-end;
      gap: 20px;
    }
  
    .btn-style {
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #EE4961;
      border-radius: 4px;
      padding: 8px;
      cursor: pointer;
      color: white;
    }
  
    .data-grid ::ng-deep .dx-toolbar .dx-toolbar-items-container {
      height: 50px !important;
    }
  
    ::ng-deep .mat-step-header .mat-step-icon-state-number,
    ::ng-deep .mat-step-header .mat-step-icon-selected,
    ::ng-deep .mat-step-header .mat-step-icon-state-done,
    ::ng-deep .mat-step-header .mat-step-icon-state-edit {
      background-color: #EE4961;
      color: #fff;
    }
  
    ::ng-deep .mat-step-header {
      pointer-events: none !important;
    }
  
    .disabled {
      opacity: 0.6; /* Reduce opacity to indicate disabled state */
      pointer-events: none; /* Disable pointer events */
    }
  }
  