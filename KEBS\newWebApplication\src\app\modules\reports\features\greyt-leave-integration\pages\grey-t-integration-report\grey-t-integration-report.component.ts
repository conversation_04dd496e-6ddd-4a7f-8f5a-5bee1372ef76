import { Component, HostListener, OnInit } from '@angular/core';
import { Subject } from 'rxjs';
import { ToasterService } from 'src/app/services/toaster-service/toaster.service';
import { GreyTIntegrationService } from '../../services/grey-t-integration.service';
import { takeUntil } from 'rxjs/operators';
import { Router } from '@angular/router';

@Component({
  selector: 'app-grey-t-integration-report',
  templateUrl: './grey-t-integration-report.component.html',
  styleUrls: ['./grey-t-integration-report.component.scss'],
})
export class GreyTIntegrationReportComponent implements OnInit {
  protected _onDestroy = new Subject<void>();
  syncData = [];
  errorLogData = [];
  showSyncData: boolean = true;
  showErrorLogData: boolean = false;
  dynamicHeight: string;
  dynamicReportHeight: string;
  dynamicGridReportHeight: string;

  constructor(
    private toasterService: ToasterService,
    private greyTIntegrationService: GreyTIntegrationService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.syncData = [];
    this.errorLogData = [];
    this.showSyncData = true;
    this.showErrorLogData = false;
    this.getLeaveSyncedData();
  }

  resyncLeave() {
    return new Promise((resolve, reject) =>
      this.greyTIntegrationService
        .syncleave()
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res) => {
            if (res['messType'] == 'S') {
              this.toasterService.showSuccess(
                'Report Message',
                'Leave Synced Successfully',
                7000
              );
            } else {
              this.toasterService.showSuccess(
                'Report Message',
                'Leave Synced InProgress',
                7000
              );
            }
            resolve(true);
          },
          error: (err) => {
            this.toasterService.showSuccess(
              'Report Message',
              'Leave Synced Failed',
              7000
            );
            console.log(err);
            reject();
          },
        })
    );
  }

  @HostListener('window:resize')
  onResize() {
    this.calculateDynamicContentHeight();
  }
  //Adjust UI size based on window size
  calculateDynamicContentHeight() {
    this.dynamicHeight = window.innerHeight - 104 + 'px';
    document.documentElement.style.setProperty(
      '--dynamicHeight',
      this.dynamicHeight
    );
    this.dynamicGridReportHeight = window.innerHeight - 240 + 'px';
    document.documentElement.style.setProperty(
      '--dynamicGridReportHeight',
      this.dynamicGridReportHeight
    );
    this.dynamicReportHeight = window.innerHeight - 223 + 'px';
    document.documentElement.style.setProperty(
      '--dynamicReportHeight',
      this.dynamicReportHeight
    );
  }

  viewErrorLog() {
    this.showSyncData = false;
    this.showErrorLogData = true;
  }

  viewSyncLog(){
    this.showSyncData = true;
    this.showErrorLogData = false;
  }

  getLeaveSyncedData() {
    return new Promise((resolve, reject) =>
      this.greyTIntegrationService
        .getGreyTLeaveIntegrationLogs()
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res) => {
            if (res['messType'] == 'S') {
              this.syncData = res['syncData'];
              this.errorLogData = res['errorLogData'];
            } else {
              this.toasterService.showError(
                'Report Message',
                res['messText'],
                7000
              );
            }
            resolve(true);
          },
          error: (err) => {
            this.toasterService.showError(
              'Report Message',
              'Error: Report Failed to retrieved, Kindly try after sometime',
              7000
            );
            console.log(err);
            reject();
          },
        })
    );
  }

  onExporting(e) {
    e.fileName = 'GreyT_KEBS_Leave_Integration';
  }

  goToReportsMainScreen() {
    this.router.navigate(['/main/reports']);
  }
}
