import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
@Injectable({
  providedIn: 'root',
})
export class GreyTIntegrationService {
  constructor(private _http: HttpClient) {}

  getGreyTLeaveIntegrationLogs() {
    return this._http.post('/api/tsPrimary/getGreyTLeaveIntegrationLogs', {});
  }

  syncleave(){
    return this._http.post('/api/bgjPrimary/integrateLeaveFromGrety', {});
  }
}
