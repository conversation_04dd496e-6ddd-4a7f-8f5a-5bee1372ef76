<div class="container-fluid landingpageForLogsTrace">
    <div class="titile_card row d-flex justify-content-center">
        <!-- <div class="col-2"></div> -->
        <div class="title row d-flex justify-content-center"
            style="text-align: center;color:rgb(251, 62, 62);margin-top: 1vh;">
            <div> Integration Trace Logs</div>
            <mat-icon class="iconsSize" matTooltip="Refresh" style="margin-left:15px;color:#cf0001;cursor: pointer;"
                (click)="loadTableView()">refresh</mat-icon>
        </div>
    </div>

    <div *ngIf="isLoading" class="row justify-content-center" style="padding-top: 10vh;">
        <mat-spinner diameter="30"></mat-spinner>
    </div>

    <ng-container *ngIf="!isLoading">
        <dx-data-grid id="gridContainer" [allowColumnResizing]="true" [dataSource]="tableData" [showBorders]="true"
            [hoverStateEnabled]="true" class="dev-style" style="padding:5px;" [columnAutoWidth]="true"
            (onExporting)="onExporting($event)" (onCellClick)="onColumnNameCellClick($event)">

            <dxo-column-chooser [enabled]="true" mode="select"> </dxo-column-chooser>

            <!-- <dxo-column-fixing [enabled]="true"></dxo-column-fixing>
            <dxi-column [allowFixing]="false"></dxi-column> -->

            <dxo-export [enabled]="true"></dxo-export>

            <dxo-search-panel [visible]="true" [width]="240" placeholder="Search..."></dxo-search-panel>
            <dxo-header-filter [visible]="true"></dxo-header-filter>
            <dxo-filter-row [visible]="true"></dxo-filter-row>

            <!-- Column Begins ! -->

            <dxi-column dataField="id" caption="ID" [allowSorting]="true" [allowFiltering]="true" alignment='left'
                [minWidth]="auto" [allowReordering]="true">
            </dxi-column>

            <dxi-column dataField="integration_name" caption="Integration Name" [allowSorting]="true"
                [allowFiltering]="true" alignment='left' [minWidth]="auto" [allowReordering]="true">
            </dxi-column>

            <dxi-column dataField="interface_name" caption="Interface Name" [allowSorting]="true"
                [allowFiltering]="true" alignment='left' [minWidth]="auto" [allowReordering]="true">
            </dxi-column>

            <dxi-column dataField="trace_stage" caption="Trace For" [allowSorting]="true"
                [allowFiltering]="true" alignment='left' [minWidth]="auto" [allowReordering]="true">
            </dxi-column>

            
            <dxi-column dataField="status" caption="Status" [allowSorting]="true"
                [allowFiltering]="true" alignment='left' [minWidth]="auto" [allowReordering]="true">
            </dxi-column>

            <dxi-column dataField="error_code" caption="Error Code" [allowSorting]="true"
                [allowFiltering]="true" alignment='left' [minWidth]="auto" [allowReordering]="true">
            </dxi-column>
            
            <dxi-column dataField="error_description" caption="Error Description" [allowSorting]="true"
                [allowFiltering]="true" alignment='left' [minWidth]="auto" [allowReordering]="true">
            </dxi-column>

            <dxi-column dataField="error_solution" caption="Error Solution" [allowSorting]="true"
                [allowFiltering]="true" alignment='left' [minWidth]="auto" [allowReordering]="true">
            </dxi-column>

            <dxi-column dataField="ticket_id" caption="Ticket ID" [allowSorting]="true"
                [allowFiltering]="true" alignment='left' [minWidth]="auto" [allowReordering]="true">
            </dxi-column>

            <dxi-column dataField="timestamp" caption="Timestamp" dataType="date" [format]="'dd-MMM-yyyy HH:mm:ss'"
                [allowSorting]="true" [allowFiltering]="true" alignment='left' [minWidth]="auto"
                [allowReordering]="true">
            </dxi-column>

            <dxi-column caption="Payload Data" dataField="payload_data" [allowSorting]="false" [allowFiltering]="false"
                [cellTemplate]="statusCellTemplate" [showFilterRow]="false" [minWidth]="80" [allowReordering]="true">
                <div style="text-align: right;"></div>
            </dxi-column>

            <!-- <dxi-column *ngFor="let value of tableDataColumns" [allowSorting]="!value.allowSorting" [allowFiltering]="value.allowFiltering"
                alignment='left'  [dataField]="value.dataField" [allowReordering]="true" [caption]="value.caption" [cellTemplate]="value.dataField == this.view_log_field ? statusCellTemplate : null">
            </dxi-column> -->


            <!--Column Ends-->


        </dx-data-grid>
    </ng-container>

</div>



<!-- dev Extreme Table Content Ends-->