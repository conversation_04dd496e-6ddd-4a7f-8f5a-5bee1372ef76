import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import {InternalStakeholdersComponent} from './pages/internal-stakeholders/internal-stakeholders.component';
import { InternalStakeholdersTabComponent} from "./pages/internal-stakeholders-tab/internal-stakeholders-tab.component";
import { ExceptionInternalStakeholderComponent } from './pages/exception-internal-stakeholder/exception-internal-stakeholder.component';
import { NonBillableMemberComponent } from './pages/non-billable-member/non-billable-member.component';
import { PartiallyAssignedComponent } from './pages/partially-assigned/partially-assigned.component'

const routes: Routes = [{
  path:"",
  component:InternalStakeholdersTabComponent,
  children:[
    {
      path:"",
      pathMatch:"full",
      redirectTo:"internal_stakeholders"
    },
    {
      path:"internal_stakeholders",
      component: InternalStakeholdersComponent
    },
    {
      path:"exception_report",
      component: ExceptionInternalStakeholderComponent
    },
    {
      path:"non_billable_member",
      component: NonBillableMemberComponent
    },
    {      
      path:"partially_assigned",
      component: PartiallyAssignedComponent
    }

  ]
}];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class InternalStakeholdersReportRoutingModule { }
