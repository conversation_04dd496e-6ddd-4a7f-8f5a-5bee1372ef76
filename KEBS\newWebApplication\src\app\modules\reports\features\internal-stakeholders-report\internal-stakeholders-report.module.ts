import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import {MatButtonToggleModule} from '@angular/material/button-toggle';

import { InternalStakeholdersReportRoutingModule } from './internal-stakeholders-report-routing.module';
import { InternalStakeholdersComponent } from './pages/internal-stakeholders/internal-stakeholders.component';
import { DxDataGridModule, DxListModule, DxDropDownBoxModule, DxTagBoxModule, DxTooltipModule, DxButtonModule } from 'devextreme-angular';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatMenuModule } from '@angular/material/menu';
import { SatPopoverModule } from "@ncstate/sat-popover";
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatBadgeModule } from '@angular/material/badge';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { NgxSpinnerModule } from "ngx-spinner";
import { CheckLabelPipe } from './pipes/check-label.pipe';
import { CheckColumnPipe } from './pipes/check-column.pipe';
import { InternalStakeholdersTabComponent } from './pages/internal-stakeholders-tab/internal-stakeholders-tab.component';
import { ExceptionInternalStakeholderComponent } from './pages/exception-internal-stakeholder/exception-internal-stakeholder.component';
import {MatTabsModule} from '@angular/material/tabs';
import { ExceptionIsaWiseComponent } from './pages/exception-isa-wise/exception-isa-wise.component';
import { ExceptionCcWiseComponent } from './pages/exception-cc-wise/exception-cc-wise.component';
import { NonBillableMemberComponent } from './pages/non-billable-member/non-billable-member.component';
import { PartiallyAssignedComponent } from './pages/partially-assigned/partially-assigned.component';
@NgModule({
  declarations: [InternalStakeholdersComponent, CheckLabelPipe, CheckColumnPipe, InternalStakeholdersTabComponent, ExceptionInternalStakeholderComponent, ExceptionIsaWiseComponent, ExceptionCcWiseComponent, NonBillableMemberComponent,PartiallyAssignedComponent],
  imports: [
    CommonModule,
    InternalStakeholdersReportRoutingModule,
    DxDataGridModule,
    DxListModule,
    DxDropDownBoxModule,
    DxTagBoxModule,
    DxTooltipModule,
    MatBadgeModule,
    DxButtonModule,
    MatTooltipModule,
    SatPopoverModule,
    MatFormFieldModule,
    MatInputModule,
    FormsModule,
    ReactiveFormsModule,
    DxTooltipModule,
    MatIconModule,
    MatMenuModule,
    MatButtonModule,
    MatDatepickerModule,
    NgxSpinnerModule,
    MatButtonToggleModule,
    MatTabsModule
  ],
  exports:[
    ExceptionInternalStakeholderComponent,
    ExceptionIsaWiseComponent
  ]
})
export class InternalStakeholdersReportModule { }
