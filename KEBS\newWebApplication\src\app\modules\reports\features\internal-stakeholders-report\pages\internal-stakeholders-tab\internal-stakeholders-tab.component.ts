import { Component, OnInit } from '@angular/core';
import { MasterService } from 'src/app/modules/projects/services/master.service';
import { ProjectService } from 'src/app/modules/projects/services/project.service';
import * as _ from 'underscore';
@Component({
  selector: 'app-internal-stakeholders-tab',
  templateUrl: './internal-stakeholders-tab.component.html',
  styleUrls: ['./internal-stakeholders-tab.component.scss']
})
export class InternalStakeholdersTabComponent implements OnInit {


  tabLinks = []
  exceptionTabs: any=[]
  formConfig: any=[]

  constructor(private masterService: MasterService,
    private projectService: ProjectService) { }

  async ngOnInit() {

    let access  = this.projectService.getGlobalCreationAccess(562, 582)
    let access_1=this.projectService.getGlobalCreationAccess(562,602)
    let access_2= this.projectService.getGlobalCreationAccess(562,603)
    this.tabLinks = [
            { label: "Project Assigned", path: "internal_stakeholders", toDisplay : true },
            { label: "Unassigned", path: "exception_report", toDisplay:access  },
            {label:"Partially Assigned", path:"partially_assigned",toDisplay:access_1},
            { label: "Non Billable Members", path: "non_billable_member", toDisplay:access_2}
    ];
    

  

  }

}
