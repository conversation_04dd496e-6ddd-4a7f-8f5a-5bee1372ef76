<div class="container-fluid internal-stakeholders-component">

  
  <form [formGroup]="dateForm">
    <div class="row header-row">
        <div class="col-2">
            Total Associates: {{this.count}}
        </div> 
      <div class="col-2">
        <mat-form-field appearance="outline" style="width: 80%">
            <mat-label>Start Period</mat-label>
            <input matInput [matDatepicker]="sd" formControlName="startDate" [max]="this.dateForm.get('endDate').value">
            <mat-datepicker-toggle matSuffix [for]="sd"></mat-datepicker-toggle>
            <mat-datepicker #sd></mat-datepicker>
        </mat-form-field>
        </div>
        <div class="col-2">
        <mat-form-field appearance="outline" style="width: 80%">
            <mat-label>End Period</mat-label>
            <input matInput [matDatepicker]="ed" formControlName="endDate" [min]="this.dateForm.get('startDate').value" placeholder="MMMM-YYYY">
            <mat-datepicker-toggle matSuffix [for]="ed"></mat-datepicker-toggle>
            <mat-datepicker #ed></mat-datepicker>
        </mat-form-field>
    </div>
    <div class="col-4">
        <button mat-icon-button matTooltip="Submit" class="iconbtn mr-2"
            style="font-weight: normal; margin-left: 1%; margin-bottom: 1%;"
            (click)="getprojectIsaReport()">
            <mat-icon>done_all</mat-icon>
        </button>
        <button mat-icon-button matTooltip="Refresh" class="iconbtn mr-2"
            style="font-weight: normal; margin-left: 1%; margin-bottom: 1%;"
            (click)="getprojectIsaReport()">
            <mat-icon>cached</mat-icon>
        </button>
        <button mat-icon-button matTooltip="Choose Column" class="iconbtn mr-2"
            style="font-weight: normal; margin-left: 1%; margin-bottom: 1%;"
            (click)="showDetailedGridColumnChooser()">
            <mat-icon>view_column</mat-icon>
        </button>
        <button mat-icon-button matTooltip="Save my columns" class="iconbtn mr-2"
        style="font-weight: normal; margin-left: 1%; margin-bottom: 1%;"
        (click)="saveVisibleColumns()">
        <mat-icon>save</mat-icon>
    </button>
        <button mat-icon-button matTooltip="Export" class="iconbtn mr-2"
            style="font-weight: normal; margin-left: 1%; margin-bottom: 1%;"
            (click)="exportEntireReport()">
            <mat-icon>cloud_download</mat-icon>
        </button>
    </div>
    <div>
      <dx-data-grid 
          id="gridContainer"
          [dataSource]="isaDataSource"
          [allowColumnReordering]="false"
          [showBorders]="true"
          [showColumnLines]="true"
          [showRowLines]="true"
          [rowAlternationEnabled]="true"
          [columnHidingEnabled]="false"
          [allowColumnReordering]="false"
          [allowColumnResizing]="true"
          [columnAutoWidth]="true"
          [hoverStateEnabled]="true"
          
          >    
          <dxo-load-panel [enabled]="true"></dxo-load-panel>
          <dxo-column-chooser [enabled]="true" mode="select"></dxo-column-chooser>
          <dxo-column-fixing [enabled]="true"></dxo-column-fixing>
          <dxo-filter-row
          visible="true"
          [applyFilter]="true"></dxo-filter-row>
          <dxo-header-filter
          visible="true"></dxo-header-filter>
          <dxo-scrolling
        mode="virtual" preloadEnabled="true"> <!-- or "virtual" | "infinite" -->
    </dxo-scrolling>
    <dxo-sorting mode="multiple"></dxo-sorting>
<dxo-summary>
    <dxi-total-item column="associate_id" summaryType="count" alignment="left"></dxi-total-item>
</dxo-summary> 
          <dxi-column dataField="customer_name" caption="{{('customer_name' | checkLabel : this.formConfig: 'isa_report': 'Account Name')}}" alignment = "left" [visible]="this.usercolumn[0]" >
              
          </dxi-column>
          <dxi-column dataField="project_id" caption="{{('project_id' | checkLabel : this.formConfig: 'isa_report': 'Project Id')}}" alignment = "left" [visible]="this.usercolumn[1]">
  
              
          </dxi-column>
          <dxi-column dataField="project_name" caption="{{('project_name' | checkLabel : this.formConfig: 'isa_report': 'Project Name')}}" alignment = "left"  [visible]="this.usercolumn[2]">
              
          </dxi-column>
          
          <dxi-column dataField="profit_center" caption="{{('profit_center' | checkLabel : this.formConfig: 'isa_report': 'Profit Center')}}" alignment = "left" [visible]="this.usercolumn[3]">
              
          </dxi-column>
          <dxi-column dataField="item_name" caption="{{('item_name' | checkLabel : this.formConfig: 'isa_report': 'Item Name')}}" alignment = "left" [visible]="this.usercolumn[4]">
              
          </dxi-column>
          <dxi-column dataField="associate_id" caption="{{('associate_id' | checkLabel : this.formConfig: 'isa_report': 'AID')}}" alignment = "left" dataType='number' [visible]="this.usercolumn[5]">
              
          </dxi-column>|
          <dxi-column dataField="display_name" caption="{{('display_name' | checkLabel : this.formConfig: 'isa_report': 'Employee Name')}}" alignment = "left" [visible]="this.usercolumn[6]">
              
          </dxi-column>
          <dxi-column dataField="name" caption="{{('name' | checkLabel : this.formConfig: 'isa_report': 'Project Role')}}" alignment = "left" [visible]="this.usercolumn[7]">
              
          </dxi-column>
          <dxi-column dataField="start_date" caption="{{('start_date' | checkLabel : this.formConfig: 'isa_report': 'Start Date')}}" alignment = "left" dataType="date" [format]="{ type: 'dd-MMM-yyyy' }" [visible]="this.usercolumn[8]">
              
          </dxi-column>
          <dxi-column dataField="end_date" caption="{{('end_date' | checkLabel : this.formConfig: 'isa_report': 'End Date')}}" alignment = "left" dataType="date" [format]="{ type: 'dd-MMM-yyyy' }" [visible]="this.usercolumn[9]">
              
          </dxi-column>
          <dxi-column dataField="billing_name" caption="{{('billing_name' | checkLabel : this.formConfig: 'card': 'Billable')}}" alignment = "left" [visible]="this.usercolumn[10]">
              
          </dxi-column>
          <dxi-column dataField="entity_name" caption="{{('entitiy_name' | checkLabel : this.formConfig: 'isa_report': 'Entitiy')}}" alignment = "left" [visible]="this.usercolumn[11]">
              
          </dxi-column>
          <dxi-column dataField="region" caption="{{('region' | checkLabel : this.formConfig: 'isa_report': 'Division')}}" alignment = "left" [visible]="this.usercolumn[12]">
              
          </dxi-column>
          <dxi-column dataField="business_unit" caption="{{('business_unit' | checkLabel : this.formConfig: 'isa_report': 'Sub Division')}}" alignment = "left" [visible]="this.usercolumn[13]">
              
          </dxi-column>
          <dxi-column dataField="department_name" caption="{{('department_name' | checkLabel : this.formConfig: 'isa_report': 'Department')}}" alignment = "left" [visible]="this.usercolumn[14]">
              
          </dxi-column>

          <dxi-column dataField="split_percentage" caption="{{('split_percentage' | checkLabel : this.formConfig: 'isa_report': 'Allocation Percentage')}}" alignment = "left" [visible]="this.usercolumn[15]">
              
          </dxi-column>
          <dxi-column dataField="org_region" caption="{{('org_region' | checkLabel : this.formConfig: 'isa_report': 'Region')}}" alignment = "left" [visible]="this.usercolumn[16]">
              
          </dxi-column>
          <ng-container *ngIf="enable_rates">
          <dxi-column dataField="position_name" caption="{{('position_name' | checkLabel : this.formConfig: 'isa_report': 'Rate Card Position (O2C)')}}" alignment = "left" [visible]="this.usercolumn[17]">

          </dxi-column>
          <dxi-column dataField="position_q2c_name" caption="{{('position_name' | checkLabel : this.formConfig: 'isa_report': 'Rate Card Position (Q2C)')}}" alignment = "left" [visible]="this.usercolumn[18]">
              
          </dxi-column>
          
          <dxi-column dataField="revenue" caption="{{('revenue' | checkLabel : this.formConfig: 'isa_report': 'Revenue')}}" alignment = "left" [visible]="this.usercolumn[19]">
              
          </dxi-column>
          <dxi-column dataField="rate_currency" caption="{{('rate_currency' | checkLabel : this.formConfig: 'isa_report': 'Revenue Currency')}}" alignment = "left" [visible]="this.usercolumn[20]">
              
          </dxi-column>
          <dxi-column dataField="revenue_in_usd" caption="{{('revenue_in_usd' | checkLabel : this.formConfig: 'isa_report': 'Revenue (In USD)')}}" alignment = "left" [visible]="this.usercolumn[21]">
              
          </dxi-column>
          <dxi-column dataField="unit" caption="{{('unit' | checkLabel : this.formConfig: 'isa_report': 'Revenue Unit')}}" alignment = "left" [visible]="this.usercolumn[22]">
              
          </dxi-column>
        </ng-container>
          <dxi-column dataField="project_status_name" caption="{{('project_status_name' | checkLabel : this.formConfig: 'isa_report': 'Project Status')}}" alignment = "left" [visible]="this.usercolumn[23]">
              
          </dxi-column>
          <dxi-column dataField="sow_reference_number" caption="{{('sow_reference_number' | checkLabel : this.formConfig: 'isa_report': 'SOW Reference Number')}}" alignment = "left" [visible]="this.usercolumn[24]">
              
          </dxi-column>
          <dxi-column dataField="sales_region" caption="{{('sales_region' | checkLabel : this.formConfig: 'isa_report': 'Sales Region')}}" alignment = "left" [visible]="this.usercolumn[25]">
              
          </dxi-column>
          <ng-container *ngIf="considerWithoutHolidays">
          <dxi-column *ngFor="let month of months" [dataField]="month.month_result" [caption]="month.month_result" alignment = "left"  dataType='number' [visible]="true">
            
          </dxi-column>
          </ng-container>
          <ng-container *ngIf="considerHolidays">
          <dxi-column *ngFor="let month of months_holiday" [dataField]="month.month_result" [caption]="month.month_result" alignment = "left"  dataType='number' [visible]="this.check_holiday_enabled">
            
          </dxi-column>
          </ng-container>
    
          <dxo-export [enabled]="true" fileName="ISA"></dxo-export>
      </dx-data-grid>
  </div>
</div>
  </form>
</div>

