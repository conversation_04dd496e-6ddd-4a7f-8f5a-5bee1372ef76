import { Component, OnInit, ViewChild } from '@angular/core';
import { HttpClient, HttpClientModule, HttpHeaders, HttpRequest } from "@angular/common/http";
import{InternalStakeholdersService} from './../../services/internal-stakeholders.service'
import moment from 'moment';
import { FormBuilder, Validators, FormGroup } from "@angular/forms";
import { ProjectService } from 'src/app/modules/projects/services/project.service'; 

import { UtilityService } from 'src/app/services/utility/utility.service';
import { LoginService } from "src/app/services/login/login.service";
import { forceCopyProperties } from '@amcharts/amcharts4/.internal/core/utils/Object';
import { DxDataGridComponent,DxNumberBoxComponent } from 'devextreme-angular';
import * as momentBusiness from 'moment-business-days';
import DataSource from "devextreme/data/data_source";
import { count } from 'rxjs/operators';
import * as _ from 'underscore';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core';
import { MAT_MOMENT_DATE_ADAPTER_OPTIONS, MomentDateAdapter } from '@angular/material-moment-adapter';
@Component({
  selector: 'app-internal-stakeholders',
  templateUrl: './internal-stakeholders.component.html',
  styleUrls: ['./internal-stakeholders.component.scss'],
  providers: [
    {
      provide: DateAdapter,
      useClass: MomentDateAdapter,
      deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS]
    },
    {
      provide: MAT_DATE_FORMATS,
      useValue: {
        parse: {
          dateInput: "DD-MMM-YYYY"
        },
        display: {
          dateInput: "DD-MMM-YYYY",
          monthYearLabel: "MMM YYYY"
        }
      }
    },
  ]
})
export class InternalStakeholdersComponent implements OnInit {

  @ViewChild(DxDataGridComponent, { static: false }) dataGrid: DxDataGridComponent;
   isaDataSource=[{}]
   dateForm: FormGroup = this.fb.group({
    startDate: [Validators.required],
    endDate: [Validators.required],
  });
 @ViewChild('dataGridRef', { static: false }) dataGridd: DxDataGridComponent;
  
startDate: any
endDate :any
formConfig: any=[];
accesslist: any; 
items:any
projectRoleAccess = this.InternalStakeholdersService.getProjectRoleAccess();
month_requested:number
count:number=0
check=[]
months=[]
unicheck=[]
isaColumn:any
usercolumn=[]
j:any
currentUser:any
save:any=0
countColumn:any=0
months_holiday:any=[]
check_holiday_enabled:boolean=false
considerWithoutHolidays:boolean=false
considerHolidays: boolean = false;
enable_rates:boolean=false
  constructor(private InternalStakeholdersService: InternalStakeholdersService,
    private fb:FormBuilder,
    private ProjectService:ProjectService,
    private UtilityService:UtilityService,
    private loginService:LoginService) 
    {}

  async ngOnInit() {
   
    this.currentUser = this.loginService.getProfile().profile
    await this.ProjectService.getProjectFormFieldConfig().then((res: any)=>{
      if(res['messType']=="S")
      {
        this.formConfig=res['data']
        this.considerHolidays=_.findWhere(this.formConfig, {field_name: 'allocation_withHolidays', type: 'isa', is_active: true}) ? _.findWhere(this.formConfig, {field_name: 'allocation_withHolidays', type: 'isa', is_active: true}).consider_holidays : false;
        this.considerWithoutHolidays=_.findWhere(this.formConfig, {field_name: 'allocation_withoutHolidays', type: 'isa', is_active: true}) ? _.findWhere(this.formConfig, {field_name: 'allocation_withoutHolidays', type: 'isa', is_active: true}).consider_holidays : false;
        this.enable_rates=_.findWhere(this.formConfig, {field_name: 'enable_rates', type: 'isa', is_active: true}) ? _.findWhere(this.formConfig, {field_name: 'enable_rates', type: 'isa', is_active: true}).is_active : false;
      }
    })
    this.month_requested=_.findWhere(this.formConfig, {field_name: 'month_requested', type: 'isa', is_active: true}) ? _.findWhere(this.formConfig, {field_name: 'month_requested', type: 'isa', is_active: true}).label : 6
    this.startDate = moment().startOf('month').format('YYYY-MM-DD');
    this.endDate = moment().endOf('month').format('YYYY-MM-DD');

    this.dateForm.patchValue({
      startDate:this.startDate,
      endDate:this.endDate
    })
    this.retrieveDisplayIsaUserConfig(this.currentUser["aid"])
    this.getprojectIsaReport()


    this.dateForm.get('endDate').valueChanges.subscribe(res=>{
      this.dataGrid.instance.beginCustomLoading("Loading...");
      this.getprojectIsaReport()
    })

    this.dateForm.get('startDate').valueChanges.subscribe(res=>{
      this.dataGrid.instance.beginCustomLoading("Loading...");
      this.getprojectIsaReport()
    })
    
   
  }

  getprojectIsaReport =async () => {
    this.startDate=this.dateForm.get('startDate').value
    this.endDate=this.dateForm.get('endDate').value
    this.accesslist=this.InternalStakeholdersService.getObjectValueAccessList()
    this.months =[]
    this.months_holiday=[]
    for(let i=0;i<this.month_requested;i++)
    {  
     
      let result =moment(this.startDate).add(i,"months").format("MM")
     
      
      let month_result = moment(this.startDate).add(i,"months").format("MMM-YYYY")
      //console.log("Month Requested", result, month_result)
      this.months.push({"month": result, "month_result": "Allocated Hours on "+month_result})
      this.months_holiday.push({"month": result,  "month_result": "Allocated Hours On "+month_result})
    }
   
    this.startDate = moment(this.startDate).format("YYYY-MM-DD")
    this.endDate = moment(this.endDate).format("YYYY-MM-DD")
    let enable_holiday= _.where(this.formConfig, { field_name: 'enable_holiday',type:'isa_report',is_active:true})
    this.check_holiday_enabled=enable_holiday.length>0 ? true :false
    await this.InternalStakeholdersService.getIsaData(moment().format('YYYY-MM-DD'),this.startDate,this.endDate,this.accesslist, this.projectRoleAccess, this.month_requested,this.check_holiday_enabled).then((res:any)=>{
      console.log(res)
    //  let flexi_growth_enable= _.where(this.formConfig, { field_name: 'flexi_growth',type:'isa_report',is_active:true})
    //   for(let items of res){
    //     if(flexi_growth_enable.length>0){
    //      let identity= items['identity'] ? items['identity']==1 ? 'Growth' : items['identity']==2 ? 'Flexi' : '' : ''
    //      if(identity!=''){
    //       items['billing_name']=identity
    //      }
    //      } 
    //   for(let i=0;i<=this.month_requested;i++)
    //   {  
    //       let month_start_date = moment(this.startDate).add(i,"months").startOf("month")
    //       let month_end_date = moment(this.startDate).add(i,"months").endOf("month")

    //       let start_month = moment(month_start_date).isAfter(moment(items['start_date'])) ? moment(month_start_date).format("YYYY-MM-DD") : moment(items['start_date']).format("YYYY-MM-DD");
        
    //       let end_month = moment(items['end_date']).isAfter(moment(month_end_date)) ? moment(month_end_date).format("YYYY-MM-DD") : moment(items['end_date']).format("YYYY-MM-DD")
          

    //       let days = this.findWorkingDays(start_month, end_month)
    //       //console.log(items['profit_center'],moment(start_month),moment(end_month), month_start_date, month_end_date, days)

    //       let result = moment(this.startDate).add(i,"months").format("MM")

      

    //       if(moment(start_month).isAfter(end_month))
    //       {
    //         //console.log("First",items['associate_id'], items['profit_center'], i+1, start_month, end_month, days)
    //         items[result]=(0*(items["split_percentage"]/100)).toFixed(2)
    //       }
    //       else
    //       {
    //         console.log("Third", items['associate_id'], items['profit_center'], i+1, start_month, end_month,  days)

    //         items[result]= ((days)*8*(items["split_percentage"]/100)).toFixed(2)
            
    //       }

        
    //       //console.log(items[result])

    //   }
    //   items['start_date']=moment(items['start_date']).format('DD-MMM-YYYY')
    //   items['end_date']=moment(items['end_date']).format('DD-MMM-YYYY')
          
    //  }

     this.isaDataSource=res
     this.check=this.isaDataSource
     this.unicheck=this.removeDuplicates(this.check,"associate_id")
     //console.log(this.unicheck)
     this.count=this.unicheck.length
    
     //console.log(this.isaDataSource)
     //this.count=this.isaDataSource['associate_id'].length
    this.dataGrid.instance.endCustomLoading();
    })
  }

  showDetailedGridColumnChooser(){
    this.dataGrid.instance.showColumnChooser();
  }
  saveVisibleColumns(){
    this.countColumn=this.dataGrid.instance.columnCount()
    console.log(this.countColumn)
    this.isaColumn=this.dataGrid.instance.getVisibleColumns()
    console.log(this.isaColumn)
    for (let i=0;i<=this.countColumn;i++){
             this.usercolumn[i]=false
     }
    for(let items of this.isaColumn){
      this.usercolumn[items["visibleIndex"]]=true
    }
    console.log(this.usercolumn)
    this.save=1
    this.updateDisplayIsaUserConfig(this.currentUser["aid"],this.usercolumn,this.save)
    
    
  }
  updateDisplayIsaUserConfig(aid,column,save){
    this.ProjectService.updateDisplayIsaUserConfig(aid,column,save).then((res)=>{
      //config updated successfully
      this.UtilityService.showMessage("Column updated", "Dismiss");
    })
  }
  retrieveDisplayIsaUserConfig(aid){
    this.ProjectService.retrieveDisplayIsaUserConfig(aid).then((res: any)=>{
        for(let items of res){
          this.usercolumn=items["column"]
          console.log(this.usercolumn)
        }
    })
  }
  removeDuplicates(myArray, Prop) {
    return myArray.filter((obj, pos, arr) => {
      return arr.map(mapObj => mapObj[Prop]).indexOf(obj[Prop]) === pos;
    });
  }

  exportEntireReport(){
    this.dataGrid.export.enabled = true;
    this.dataGrid.export.fileName = 'ISA';
    this.dataGrid.instance.exportToExcel(false); // false to export all row data
  }
  

  findWorkingDays(start_month, end_month)
  {
    let days = 0;

    let start_day_name = moment(start_month).format("dddd");
    let end_day_name = moment(end_month).format("dddd");

    if(start_day_name!="Sunday" && start_day_name!="Saturday")
    {
      days = days + 1
    }

    let day_condition_match = false;

    while(!day_condition_match)
    {
      if(start_day_name == end_day_name)
      {
        day_condition_match = true;
      }
      else
      {

        
        start_month = moment(start_month).add(1,"day")

        start_day_name = moment(start_month).format("dddd")
        
        if(start_day_name!="Sunday" && start_day_name!="Saturday")
        {
          days = days + 1;
        }
      }
      
      
    }

    let date_difference = moment(end_month).diff(start_month,"days")

    let mul_value = date_difference/7

    days = days + (mul_value * 5)


    return days
  }


}

