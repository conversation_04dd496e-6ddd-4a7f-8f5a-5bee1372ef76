import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { RolesService } from 'src/app/services/acl/roles.service';
import * as _ from "underscore";
import { C } from '@angular/cdk/keycodes';

@Injectable({
  providedIn: 'root'
})
export class InternalStakeholdersService {

  constructor(private http: HttpClient,private RolesService:RolesService) { }

  getIsaData = (currentdate,startDate,endDate,accessList, projectRoleAccess, month_requested,check_holiday_enabled) => {
    return new Promise((resolve, reject) => {

      this.http.post("/api/project/v2/projectIsaReport", {currentdate:currentdate,startDate:startDate,endDate:endDate,accessList:accessList, roleAccess: projectRoleAccess, month_requested: month_requested,check_holiday_enabled:check_holiday_enabled}).subscribe(
        res => {
          return resolve(res);
        },
        err => {
          console.log(err)
          return resolve([]);
        }
      );

    });
  } 
  getObjectValueAccessList(){
    let accessList = _.where(this.RolesService.roles, { application_id: 562, object_id: 6 });
   
    if (accessList.length > 0)
      return accessList[0]['object_value']

    else
      return false
  }

  /**
   * @description  Gets Project Role Access
   * @returns 
   */
  getProjectRoleAccess(){
    try
    {
       let accessList = _.where(this.RolesService.roles, { application_id: 562, object_id: 6 });

       if (accessList.length > 0)
       {
         let role_access = typeof accessList[0].object_entries =="string"?JSON.parse(accessList[0].object_entries):accessList[0].object_entries
      
         if(role_access=="null" || role_access=="*" || role_access==null || role_access=="null")
         {
             return "ALL"
         } 
         else
         {
             return role_access
         }
       }

       else
         return "ALL";
    }
    catch(err)
    {
      return "ALL"
    }
  }


  getExeceptionReportUnallocated(date){
    return new Promise((resolve, reject)=>{
      this.http.post("/api/project/v2/unAllocatedEmployee",{date: date}).subscribe((res)=>{
        resolve(res)
      },(err)=>{
        reject(err)
      })
    })
  }

  getExeceptionReportCostCenter(date){
    return new Promise((resolve, reject)=>{
      this.http.post("/api/project/v2/unAllocatedCostCenter",{date: date}).subscribe((res)=>{
        resolve(res)
      },(err)=>{
        reject(err)
      })
    })
  }
  
  getNonBillableMemberForIsaReport(){
    return new Promise((resolve, reject)=>{
      this.http.post("/api/project/v2/getNonBillableMemberForIsaReport",{}).subscribe((res)=>{
        resolve(res)
      },(err)=>{
        reject(err)
      })
    })
  }
  getPartiallyAssignedData(month,year,currentdate){
    return new Promise((resolve, reject)=>{
      this.http.post("/api/project/v2/getPartiallyAssigned",{month: month,year:year,currentdate:currentdate}).subscribe((res)=>{
        resolve(res)
      },(err)=>{
        reject(err)
      })
    })
  }
}
