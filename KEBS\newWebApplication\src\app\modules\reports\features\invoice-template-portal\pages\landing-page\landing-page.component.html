<div class="container-fluid">
    <div class="row pt-4" style="height: 10px;">

    </div>
    <dx-data-grid id="gridContainer" [dataSource]="templateDetails" [allowColumnReordering]="true" [showBorders]="true"
        [columnHidingEnabled]="false" [columnAutoWidth]="true" [allowColumnResizing]="true"
        (onCellClick)="onColumnNameCellClick($event)" (onToolbarPreparing)="customizeToolbar($event)">
        <dxo-filter-row [visible]="true" [applyFilter]="true"></dxo-filter-row>
        <dxo-header-filter [visible]="true"></dxo-header-filter>
        <dxo-search-panel [visible]="true" [width]="240" placeholder="Search..."></dxo-search-panel>
        <dxo-paging [pageSize]="10" [pageIndex]="0"> </dxo-paging>
        <dxo-editing mode="row" [allowUpdating]="false" [allowAdding]="false"></dxo-editing>


        <dxo-column-chooser [enabled]="true" mode="select"></dxo-column-chooser>
        <dxo-export [enabled]="true" fileName="Invoice Template Portal"></dxo-export>


        <dxi-column dataField="id" [allowReordering]="true" caption="ID"  [visibleIndex]="2"
            [allowEditing]="false" [visible]="false">
        </dxi-column>

        <dxi-column dataField="entity_name" [allowReordering]="true" caption="Legal Entity" 
            [visibleIndex]="2" [allowEditing]="false">
        </dxi-column>

        <dxi-column dataField="type" [allowReordering]="true" caption="Type"  [visibleIndex]="2"
            [allowEditing]="false">
        </dxi-column>

        <dxi-column dataField="template_name" [allowReordering]="true" caption="Template Name" 
            [visibleIndex]="2" [allowEditing]="false">
        </dxi-column>

        <dxi-column dataField="file_name" [allowReordering]="true" caption="File Name" 
            [visibleIndex]="2" [allowEditing]="false">
        </dxi-column>

        <dxi-column dataField="is_default" [allowReordering]="true" caption="Default Status" 
            [visibleIndex]="2" [allowEditing]="false">
        </dxi-column>

        <dxi-column dataField="is_active" [allowReordering]="true" caption="Active Status" 
            [visibleIndex]="2" [allowEditing]="false">
        </dxi-column>

        <dxi-column dataField="change_log" caption="Download"  [allowEditing]="false"
            [allowFiltering]="false" [visibleIndex]="12" [allowExporting]="false" [cellTemplate]="statusCellTemplate" [allowSorting]="false">
        </dxi-column>

        <dxi-column dataField="Actions" caption="Edit"  [allowEditing]="false" [allowFiltering]="false"
            [visibleIndex]="12" [allowExporting]="false" [cellTemplate]="statusEditCellTemplate" [allowSorting]="false">
        </dxi-column>

    </dx-data-grid>


    <dx-popup [(visible)]="isPopupVisible" [title]="'Edit Record'" [width]="607" [height]="650" class="popup-container">
        <div class="popup-content">
            <form [formGroup]="editForm" class="popup-form">

                <!-- Entity Name -->
                <div class="form-group">
                    <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Legal Entity</mat-label>
                        <input matInput formControlName="entity_name" placeholder="Legal Entity" readonly />
                    </mat-form-field>
                </div>

                <!-- Type -->
                <div class="form-group">
                    <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Type</mat-label>
                        <input matInput formControlName="type" placeholder="Type" readonly />
                    </mat-form-field>
                </div>

                <!-- Template Name -->
                <div class="form-group">
                    <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Template Name</mat-label>
                        <input matInput formControlName="template_name" placeholder="Enter Template Name" />
                        <mat-error *ngIf="editForm.get('template_name')?.hasError('required')" class="pt-2">
                            Template Name is required.
                        </mat-error>
                    </mat-form-field>
                </div>

                <!-- File Name -->
                <div class="form-group">
                    <mat-form-field appearance="outline" class="full-width">
                        <mat-label>File Name</mat-label>
                        <input matInput formControlName="fileName" placeholder="File Name" readonly />
                    </mat-form-field>
                </div>

                <div class="row">
                    <div class="col-6">
                        <!-- Active Status -->
                        <div class="form-group">
                            <label>Active Status</label>
                            <mat-radio-group formControlName="is_active" class="radio-group">
                                <mat-radio-button value="Yes">Yes</mat-radio-button>
                                <mat-radio-button value="No">No</mat-radio-button>
                            </mat-radio-group>
                            <mat-error *ngIf="editForm.get('is_active')?.hasError('required')" class="pt-2">
                                Please select an active status.
                            </mat-error>
                        </div>

                        <!-- Default Status -->
                        <div class="form-group">
                            <label>Default Status</label>
                            <mat-radio-group formControlName="is_default" class="radio-group">
                                <mat-radio-button value="Yes">Yes</mat-radio-button>
                                <mat-radio-button value="No">No</mat-radio-button>
                            </mat-radio-group>
                            <mat-error *ngIf="editForm.get('is_default')?.hasError('required')" class="pt-2">
                                Please select a default status.
                            </mat-error>
                        </div>
                    </div>
                    <div class="col-6 pt-5">
                        <input type="file" #fileInput style="display: none" single ng2FileSelect [uploader]="uploader"
                            (change)="onFileSelected($event)" />
                        <div class="row" style="justify-content: end !important;">
                            <button mat-raised-button class="mx-auto addDocument" (click)="triggerFileInput()">
                                Reupload Document <mat-icon style="transform: rotate(42deg);
                      font-size: 18px;
                      height: 17px;
                      margin-left: 3px;">
                                    attach_file
                                </mat-icon>
                            </button>
                        </div>
                        <div *ngIf="fileName" class="mt-2">
                            <p>File Ready To Upload: {{ fileName }}</p>
                        </div>
                        <div *ngIf="fileTypeError" style="color: red; display: flex; align-items: center;" class="pt-3">
                            <mat-icon style="color: orange; margin-right: 8px;">warning</mat-icon>
                            <span>{{ fileTypeError }}</span>
                        </div>
                    </div>
                </div>


            </form>

            <!-- Action Buttons -->
            <div class="popup-actions">
                <button mat-raised-button color="warn" (click)="onSaveChanges()" [disabled]="!editForm.valid">
                    Save
                </button>
                <button mat-raised-button color="warn" (click)="onCancelEdit()">Cancel</button>
            </div>
        </div>
    </dx-popup>




    <div *ngIf="createPopupVisible" class="popup-container" style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); background-color: white; border: 1px solid #ccc; padding: 20px; z-index: 2000; width: 607px;
    height: 600px;">

        <!-- Popup Header with Title and Close Button -->
        <div class="popup-header"
            style="display: flex; justify-content: space-between; align-items: center; border-bottom: 1px solid #ccc; padding-bottom: 10px; margin-bottom: 10px;">
            <span style="font-size: 16px; font-weight: bold;">Create Template</span> <!-- Title -->
            <button mat-icon-button (click)="onCancelCreate()">
                <mat-icon>close</mat-icon> <!-- Close Button -->
            </button>
        </div>

        <div class="popup-content">
            <form [formGroup]="createForm" class="popup-form">
                <!-- Entity Dropdown -->
                <div class="form-group" style="margin-top: 5px;">
                    <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Legal Entity</mat-label>
                        <mat-select formControlName="entity_name">
                            <mat-option *ngFor="let entity of entityOptions" [value]="entity.name">
                                {{ entity.name }}
                            </mat-option>
                        </mat-select>
                    </mat-form-field>
                </div>

                <!-- Type Dropdown -->
                <div class="form-group">
                    <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Type</mat-label>
                        <mat-select formControlName="type">
                            <mat-option *ngFor="let type of types" [value]="type.value">
                                {{ type.value }}
                            </mat-option>
                        </mat-select>
                    </mat-form-field>
                </div>

                <!-- Template Name -->
                <div class="form-group">
                    <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Template Name</mat-label>
                        <input matInput formControlName="template_name" placeholder="Enter Template Name" />
                        <mat-error *ngIf="createForm.get('template_name')?.hasError('required')" class="pt-2">
                            Template Name is required.
                        </mat-error>
                    </mat-form-field>
                </div>

                <div class="row">
                    <div class="col-6">
                        <!-- Active Status -->
                        <div class="form-group">
                            <label>Active Status</label>
                            <mat-radio-group formControlName="is_active" class="radio-group" [disabled]="true">
                                <mat-radio-button value="true">Yes</mat-radio-button>
                                <mat-radio-button value="false">No</mat-radio-button>
                            </mat-radio-group>
                            <mat-error *ngIf="createForm.get('is_active')?.hasError('required')" class="pt-2">
                                Please select an active status.
                            </mat-error>
                        </div>

                        <!-- Default Status -->
                        <div class="form-group">
                            <label>Default Status</label>
                            <mat-radio-group formControlName="is_default" class="radio-group" [disabled]="true">
                                <mat-radio-button value="true">Yes</mat-radio-button>
                                <mat-radio-button value="false">No</mat-radio-button>
                            </mat-radio-group>
                            <mat-error *ngIf="createForm.get('is_default')?.hasError('required')" class="pt-2">
                                Please select a default status.
                            </mat-error>
                        </div>
                    </div>

                    <!-- File Uploader -->
                    <div class="col-6 pt-5">
                        <input type="file" #fileInput style="display: none" single ng2FileSelect [uploader]="uploader"
                            (change)="onFileSelected($event)" />
                        <div class="row" style="justify-content: end !important;">
                            <button mat-raised-button class="mx-auto addDocument" (click)="triggerFileInput()">
                                Upload Document
                                <mat-icon
                                    style="transform: rotate(42deg); font-size: 18px; height: 17px; margin-left: 3px;">
                                    attach_file
                                </mat-icon>
                            </button>
                        </div>
                        <div *ngIf="fileName" class="mt-2">
                            <p class="file-name">File Ready To Upload: {{ fileName }}</p>
                        </div>
                        <div *ngIf="fileTypeError" style="color: red; display: flex; align-items: center;" class="pt-3">
                            <mat-icon style="color: orange; margin-right: 8px;">warning</mat-icon>
                            <span>{{ fileTypeError }}</span>
                        </div>
                    </div>
                </div>
            </form>

            <!-- Action Buttons -->
            <div class="popup-actions">
                <button mat-raised-button color="warn" (click)="onCreateChanges()" [disabled]="!createForm.valid">
                    Save
                </button>
                <button mat-raised-button color="warn" (click)="onCancelCreate()">Cancel</button>
            </div>
        </div>
    </div>



</div>

<ngx-spinner bdColor="rgb(245,245,245,0.6)" size="medium" color="#cf0001" type="ball-clip-rotate" [fullScreen]="true">
</ngx-spinner>