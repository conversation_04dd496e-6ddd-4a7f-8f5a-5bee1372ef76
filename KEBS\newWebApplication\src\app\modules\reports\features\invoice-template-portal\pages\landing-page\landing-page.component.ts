import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { MatSnackBar } from "@angular/material/snack-bar";
import { TemplatePortalService } from '../../services/template-portal.service';
import { NgxSpinnerService } from 'ngx-spinner';
import { Router } from '@angular/router';
import { FileUploader } from 'ng2-file-upload';
import { LoginService } from 'src/app/services/login/login.service';
import {  DxDataGridComponent} from 'devextreme-angular';
@Component({
  selector: 'app-landing-page',
  templateUrl: './landing-page.component.html',
  styleUrls: ['./landing-page.component.scss']
})
export class LandingPageComponent implements OnInit {
  
  templateDetails: any;
  isPopupVisible = false;
  selectedRowData: any = {};
  editForm: FormGroup;
  createForm: FormGroup;
  uploader: FileUploader;
  destinationBucket: string = 'kebs-invoices';
  routingKey: string = 'pdf-templates';
  contextId: string;
  @ViewChild('fileInput') fileInput!: ElementRef<HTMLInputElement>;
  URL = '/api/invoice/v2/uploadObjectFromDevice';
  fileName: string | null = null;
  fileTypeError: string | null = null;
  createPopupVisible = false;
  @ViewChild(DxDataGridComponent) dataGrid: DxDataGridComponent;
  entityOptions: any;
  types = [{ value: "PDF" }];
  selectedEntity: any;
  isAuthorized: boolean = true;

  constructor(private fb: FormBuilder, private invoiceTemplateService: TemplatePortalService, private openMatDialog: MatDialog, private snackBar: MatSnackBar,
    private spinnerService: NgxSpinnerService, private _router: Router, private _login: LoginService,) {
    this.editForm = this.fb.group({
      template_name: [this.selectedRowData.template_name, Validators.required],
      entity_name: [this.selectedRowData.entity_name],
      is_active: [this.selectedRowData.is_active, Validators.required],
      is_default: [this.selectedRowData.is_default, Validators.required],
      type: [this.selectedRowData.type, Validators.required],
      fileName: [this.selectedRowData.file_name, Validators.required],
      id: [this.selectedRowData.id],
    });

    this.createForm = this.fb.group({
      template_name: ['', Validators.required],
      entity_name: ['', Validators.required],
      is_active: ["true", Validators.required],
      is_default: ["false", Validators.required],
      type: ['PDF', Validators.required],
      fileName: [''],
      entity_id: [''],
      context_id: ['']
    });
    
  }

  async ngOnInit() {
    await this.reportAuthorizationValidation();
    let entityOptionsResult = await this.getEntityDetails();
    let result = await this.getTemplateDetails();
    this.contextId = `${this.create_UUID()}`
    this.uploader = new FileUploader({
      url: this.URL,
      authToken: 'Bearer ' + this._login.getToken(),
      disableMultipart: false,
      headers: [
        {
          name: 'context-id',
          value: this.contextId,
        },
        {
          name: 'routing-key',
          value: this.routingKey,
        },
        {
          name: 'bucket-name',
          value: this.destinationBucket,
        },
      ],
      maxFileSize: 1024 * 1024 * 10, //10mb
    });

  }

  getTemplateDetails() {
    this.templateDetails = [];
    this.spinnerService.show();
    return new Promise((resolve, reject) => {
      this.invoiceTemplateService.getInvoiceTemplateDetails().subscribe((res: any) => {
        this.templateDetails = res['data']
        resolve(res.data)
        this.spinnerService.hide();
      }, err => {
        reject(err);
        this.spinnerService.hide();
      })
    })

  }

  getEntityDetails() {
    return new Promise((resolve, reject) => {
      this.invoiceTemplateService.getEntityOptions().subscribe((res: any) => {
        this.entityOptions = res['data']
        resolve(res.data)
      }, err => {
        reject(err);
      })
    })

  }


  statusCellTemplate = (container, options) => {
    const iconClass = "dx-icon-download"
    const icon = document.createElement("i");
    icon.className = iconClass;
    container.appendChild(icon);
  }

  statusEditCellTemplate = (container, options) => {
    const iconClass = "dx-icon-edit"
    const icon = document.createElement("i");
    icon.className = iconClass;
    container.appendChild(icon);
  }

  onColumnNameCellClick(event) {
    let context_id = event?.data?.context_id;
    let file_name = event?.data?.file_name;

    if (event?.column?.caption == 'Download' && context_id) {
      return new Promise((resolve, reject) => {
        this.invoiceTemplateService.retrieveUploadedTemplate(context_id, file_name ).subscribe((res: any) => {
          if (res["data"]) {
            window.open(res["data"]);
          }
          resolve(res.data)
        }, err => {
          reject(err);
        })
      })

    }
    else {
      this.selectedRowData = { ...event.row.data };
      this.isPopupVisible = true;
      // Patch values into the form
      this.editForm.patchValue({
        template_name: this.selectedRowData.template_name,
        entity_name: this.selectedRowData.entity_name,
        is_active: this.selectedRowData.is_active,
        is_default: this.selectedRowData.is_default,
        type: this.selectedRowData.type,
        fileName: this.selectedRowData.file_name,
        id: this.selectedRowData.id
      });
    }

  }


  // Save changes and close the popup
  async onSaveChanges() {

    const updatedData = {};

    for (const controlName in this.editForm.controls) {
      if (this.editForm.controls[controlName].dirty) {
        updatedData[controlName] = this.editForm.get(controlName)?.value;
      }
      updatedData["id"] = this.editForm.get("id")?.value;

    }
    this.isPopupVisible = false;
    this.spinnerService.show();

    if (this.uploader.queue.length > 0) {
      this.uploader.onWhenAddingFileFailed = (item) => {
        this.snackBar.open('Failed to upload file', 'dismiss', {
          duration: 1000,
        });
        this.spinnerService.hide();
      };

      this.uploader.onCompleteItem = async (
        item: any,
        response: any,
        status: any,
        headers: any
      ) => {
        this.uploader.removeFromQueue(item);
        let result = typeof response === "string" ? JSON.parse(response) : response;
        if (result['err'] == false || result["messType"] == 'S') {
          await this.updateTemplateDetailsWithoutFile(updatedData, "WF", result['data']);
          this.spinnerService.hide();
        }
        this.spinnerService.hide();
      };

      this.uploader.queue.forEach((fileItem: any) => {
        const fileName = fileItem.file.name;
        // Check if the filename contains special characters
        if (this.containsSpecialChars(fileName)) {
          const cleansedName = fileName.replace(/[`!@#$%^&*()_+\-=\[\]{};':"\\|,<>\/?~]/g, "_");
          fileItem.file.name = cleansedName;
        }
      })

      this.uploader.uploadAll()

    } else {
      // Directly update template details if there are no files to upload
      const updatedData = this.editForm.value;
      await this.updateTemplateDetailsWithoutFile(updatedData, "WOF", "");
      this.spinnerService.hide();
    }
    this.spinnerService.hide();
  }
  

  containsSpecialChars = (str) => {
    const specialChars = /[`!@#$%^&*()_+\-=\[\]{};':"\\|,<>\/?~]/;
    return specialChars.test(str);
  }


  // This method is for the case when no file is being uploaded
  async updateTemplateDetailsWithoutFile(updatedData, type, metaData) {
    const formData = this.editForm.value;
    let updateDetails = await this.updateTemplateDetails(formData, updatedData, type, metaData);
    if (updateDetails["messType"] == 'E') {
      this.snackBar.open(updateDetails["messText"], "Dismiss", { duration: 2000 })
      this.fileName = "";
      await this.getTemplateDetails()
    }
    if (updateDetails["messType"] == 'S') {
      this.snackBar.open("Template details updated successfully!", "Dismiss", { duration: 2000 })
      await this.getTemplateDetails()
      this.fileName = "";
    }

  }

  // Cancel the edit and close the popup
  onCancelEdit() {
    this.isPopupVisible = false;
  }

  onCancelCreate() {
    this.createPopupVisible = false;
  }

  downloadFile(fileUrl: string) {
    window.open(fileUrl);
  }

  triggerFileInput() {
    this.fileInput.nativeElement.click();
  }

  updateTemplateDetails = (formData, updatedData, type, metaData) => {
    return new Promise((resolve, reject) => {
      this.invoiceTemplateService.updateTemplateDetails(formData, updatedData, this.contextId, type, metaData).subscribe(res => {
        resolve(res);
      },
        (err) => {
          console.error(err);
          reject(err);
        }
      );
    });
  };

  create_UUID() {
    var dt = new Date().getTime();
    var uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(
      /[xy]/g,
      function (c) {
        var r = (dt + Math.random() * 16) % 16 | 0;
        dt = Math.floor(dt / 16);
        return (c == 'x' ? r : (r & 0x3) | 0x8).toString(16);
      }
    );
    return uuid;
  } 
  
  onFileSelected(event: Event) {
    this.fileTypeError = "";
    this.contextId = `${this.create_UUID()}`
    this.uploader.options.headers = [
      { name: 'context-id', value: this.contextId }, 
      { name: 'routing-key', value: this.routingKey },
      { name: 'bucket-name', value: this.destinationBucket },
    ];

    const fileInput = event.target as HTMLInputElement;
    if (fileInput.files && fileInput.files[0]) {
      const selectedFile = fileInput.files[0];
      let fileName = selectedFile.name;
        // Check if the file is a .docx file
        if (!this.isValidDocxFile(fileName)) {
          this.fileTypeError = 'Only .docx files are allowed.';
          this.uploader.clearQueue();
          this.fileName = null;  // Reset the file name if invalid
          return;  // Stop further processing if file type is not valid
        }

      if (this.containsSpecialChars(fileName)) {
        fileName = fileName.replace(/[`!@#$%^&*()_+\-=\[\]{};':"\\|,<>\/?~]/g, "_");
      }
      this.fileName = fileName;  
      fileInput.value = "";
    }
  }

    // Check if the file has a .docx extension
    isValidDocxFile(fileName: string): boolean {
      const fileExtension = fileName.split('.').pop()?.toLowerCase();
      return fileExtension === 'docx';
    }

    customizeToolbar(e: any) {
      const toolbarItems = e.toolbarOptions.items;
  
      // Add a custom refresh button
      toolbarItems.push({
        widget: "dxButton",
        options: {
          icon: "add",
          hint: "Create Record",
          onClick: () => {
            this.createPopupVisible = true;
          },
        },
        location: "after",
      });    
  }

  async onCreateChanges() {
    this.spinnerService.show();
    const entityName = this.createForm.get('entity_name')?.value;
    const templateName = this.createForm.get('template_name')?.value;
    
    const duplicateTemplate = this.templateDetails?.find(
      (entity) => entity.entity_name == entityName && entity.template_name == templateName
    );
    
    if (duplicateTemplate) {
      this.snackBar.open('Template name already exists!', 'Dismiss', {
        duration: 3000, // Increased duration for better visibility
      });
      this.spinnerService.hide();
      return; // Stop further execution
    }
    

    const selectedEntity = this.entityOptions.find(entity => entity.name == this.createForm.get("entity_name")?.value);

    this.createForm.patchValue({
      entity_id: selectedEntity.entity_id,
      fileName: this.fileName,
      context_id:this.contextId
  });  

  



    if (this.uploader.queue.length > 0) {
      this.uploader.onWhenAddingFileFailed = (item) => {
        this.snackBar.open('Failed to upload file', 'dismiss', {
          duration: 1000,
        });
        this.spinnerService.hide();
      };

      this.uploader.onCompleteItem = async (
        item: any,
        response: any,
        status: any,
        headers: any
      ) => {
        let result = typeof response === "string" ? JSON.parse(response) : response;
        if (result['err'] == false || result["messType"] == 'S') {
          await this.insertTemplateDetailsFunc("WF", result['data'], item);
          this.spinnerService.hide();
        }
      };

      this.uploader.queue.forEach((fileItem: any) => {
        const fileName = fileItem.file.name;
        // Check if the filename contains special characters
        if (this.containsSpecialChars(fileName)) {
          const cleansedName = fileName.replace(/[`!@#$%^&*()_+\-=\[\]{};':"\\|,<>\/?~]/g, "_");
          fileItem.file.name = cleansedName;
        }
      })

      this.uploader.uploadAll()

    } 
    else{
      this.spinnerService.hide();
      this.snackBar.open("Template is required!", "Dismiss", { duration: 2000 })
    }
  }

  async insertTemplateDetailsFunc(type, metaData, item) {
    const formData = this.createForm.value;
    let updateDetails = await this.insertTemplateDetails(formData, type, metaData);
    if (updateDetails["messType"] == 'E') {
      this.snackBar.open(updateDetails["messText"], "Dismiss", { duration: 2000 })
    }
    if (updateDetails["messType"] == 'S') {
      this.snackBar.open("Template details added successfully!", "Dismiss", { duration: 2000 })
      await this.getTemplateDetails()
      this.createPopupVisible = false;
      this.createForm.reset();
      this.createForm.patchValue({
        template_name: '',
        entity_name: '',
        is_active: 'true',
        is_default: 'false',
        type: 'PDF',
        fileName: '',
        entity_id: '',
        context_id: '',
      });
      this.fileName = ""; 
      this.uploader.removeFromQueue(item);     
    }

  }

  insertTemplateDetails = (formData, type, metaData) => {
    return new Promise((resolve, reject) => {
      this.invoiceTemplateService.insertTemplateDetails(formData, this.contextId, type, metaData).subscribe(res => {
        resolve(res);
      },
        (err) => {
          console.error(err);
          reject(err);
        }
      );
    });
  };

  async reportAuthorizationValidation(){
    let roleAccessFilters = await this.invoiceTemplateService.reportAuthorizationValidation();
     this.isAuthorized = true;
     if (roleAccessFilters['messType'] != "S"){
       this.isAuthorized = false;
       this._router.navigateByUrl("/main/reports")
       return this.snackBar.open(roleAccessFilters['messText'], 'Dismiss', { duration: 2000 })
     }
   }

}
