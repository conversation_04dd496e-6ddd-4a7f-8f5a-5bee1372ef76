<div class="container-fluid">

    <div class="row">
        <!-- <div class="col-1 p-2">
            <button mat-raised-button style="font-weight: normal; margin-left: 1%; margin-bottom: 1%;"
                class='btn-active' [matMenuTriggerFor]="menu" matTooltip="MIS reports">Reports</button>

            <mat-menu #menu="matMenu" class="h-50">
                <ng-container *ngFor="let view of misMenu; index as i">
                <button *ngIf="view.is_report_display == 1" mat-menu-item class="matOptions"
                    >
                    <div class="row p-1">
                        <div class="col-8 p-0" (click)=loadMisView(i,1)>{{view.display_name}}</div>
                        <div class="col p-0 d-flex justify-content-end">
                            <mat-icon (click)="openReportInNewTab(i)" class="newTabIcon m-0 mt-3">open_in_new</mat-icon>
                        </div>
                    </div>
                </button>
                </ng-container>
            </mat-menu>
        </div> -->

        <div class="col pt-1 p-2">
            <button mat-raised-button style="font-weight: normal; margin-left: 1%; margin-bottom: 1%;"
                class='btn-active' [matMenuTriggerFor]="menu" matTooltip="MIS reports">Reports</button>

            <mat-menu #menu="matMenu" class="h-50">
                <ng-container *ngFor="let view of misMenu; index as i">
                <button *ngIf="view.is_report_display == 1" mat-menu-item class="matOptions"
                    >
                    <div class="row p-1">
                        <div class="col-8 p-0" (click)=loadMisView(i,1)>{{view.display_name}}</div>
                        <div class="col p-0 d-flex justify-content-end">
                            <mat-icon (click)="openReportInNewTab(i)" class="newTabIcon m-0 mt-3">open_in_new</mat-icon>
                        </div>
                    </div>
                </button>
                </ng-container>
            </mat-menu>
            <span *ngIf="misDataGridVisibility && !misVersioningVisibility">
                <span style="font-size:14px;font-weight: 500;color:#cf0001">
                    {{selectedMISTableDisplayName}} {{selectedPeriodMonth}} - {{selectedPeriodYear}}
                </span>
                <span>
                    <button mat-icon-button matTooltip="Refresh" style="color:#cf0001" class="iconsSize ml-2"
                        (click)="loadMisView(misCurrentViewIndex,1)">
                        <mat-icon class="iconsSize">refresh</mat-icon>
                    </button>
                </span>
            </span>
        
            <span *ngIf="misVersioningVisibility && currentOpenedTab == 'TEMPORARY'">
                <span style="font-size:14px;font-weight: 500;color:#cf0001">
                    <span>{{versionName.value}}</span>
                </span>
                <span>
                    <button mat-icon-button matTooltip="Save" style="color:#cf0001" class="iconsSize ml-2"
                        (click)="openMisVersionSaveDialog()">
                        <mat-icon class="iconsSize">save</mat-icon>
                    </button>
                </span>
            </span>
        
            <span *ngIf="misVersioningVisibility && currentOpenedTab == 'SAVED'">
                <span style="font-size:14px;font-weight: 500;color:#cf0001">
                    {{selectedVersionName}}
                </span>
                <span>
                    <mat-icon class="current-flag-icon" *ngIf="isCurrentVersion == true"
                        style="vertical-align: top;">flag</mat-icon>
                    <button mat-icon-button *ngIf="isCurrentVersion == false" matTooltip="Save as Current Version" class="ml-2"
                        style="font-size: 26px; color: #807f7e;" (click)="changeToCurrentVersion()">
                        <mat-icon style="font-size: 26px; color: #807f7e;">flag</mat-icon>
                    </button>
                </span>
            </span>
        </div>

        <div class="col-2 center p-3">
            <span style="font-size:medium;font-weight: 500;color:#cf0001">
                MIS Admin Functions
            </span>
        </div>
        <div class="d-flex justify-content-center col-6 p-2 pr-0">
            <div>
            <mat-form-field appearance="outline" style="width: 150px !important">
                <mat-label>MIS Month-Year</mat-label>
                <input matInput [matDatepicker]="dp" [formControl]="date" placeholder="MMMM-YYYY">
                <mat-datepicker-toggle matSuffix [for]="dp"></mat-datepicker-toggle>
                <mat-datepicker #dp startView="multi-year" (monthSelected)="setMonthAndYear($event, dp)">
                </mat-datepicker>
            </mat-form-field>
            <!-- MIS Calculation Section Begins ! -->
            <button mat-icon-button matTooltip="MIS Calculation" class="iconsSize" (click)="misCalculation()">
                <mat-icon class="iconsSize">stacked_line_chart</mat-icon>
            </button>
            <button *ngIf="misCalculationLevel == 'sub_division' && isBlinking == true" mat-icon-button matTooltip="Save Temporary Version" class="icons-size-btn save-temporary-btn" [ngClass]="{'save-btn': isBlinking}" (click)="openSaveTemporarayVersion(1)">
                <mat-icon class="icons-size-btn">table_view</mat-icon>
            </button>
            <button *ngIf="misCalculationLevel == 'sub_division' && isBlinking == false && currentOpenedTab != 'TEMPORARY'" mat-icon-button matTooltip="Save Temporary Version" class="iconsSize disabled-icon" [ngClass]="{'save-btn': currentOpenedTab == 'TEMPORARY'}">
                <mat-icon class="iconsSize" [ngClass]="{'save-btn': currentOpenedTab == 'TEMPORARY'}">table_view</mat-icon>
            </button>
            <button *ngIf="misCalculationLevel == 'sub_division' && isBlinking == false && currentOpenedTab == 'TEMPORARY'" mat-icon-button matTooltip="Save Temporary Version" class="icons-size-btn">
                <mat-icon class="icons-size-btn">table_view</mat-icon>
            </button>
            <span *ngIf="misCalculationLevel == 'sub_division'" class="menu-mis">
            <button mat-icon-button matTooltip="Saved MIS Versions" *ngIf="savedMisValue.length > 0" (click)="getSavedMis()" [ngClass]="{'iconsSize': currentOpenedTab != 'SAVED', 'icons-size-btn': currentOpenedTab == 'SAVED'}">
                <mat-icon class="iconsSize" [ngClass]="{'iconsSize': currentOpenedTab != 'SAVED', 'icons-size-btn': currentOpenedTab == 'SAVED'}" 
                style="font-size: 25px;" [matMenuTriggerFor]="openMenu">menu_open</mat-icon>
            </button>
            <mat-menu #openMenu="matMenu" style="max-width: 500px !important;">
                <button mat-menu-item class="drop-btn" *ngFor="let item of savedMisValue" (click)="onMisVersionMenuClick(item, 1)">
                  <span>{{item.title}}</span>
                  <mat-icon *ngIf="item.is_current_version == 0" style="color:#807f7e; font-size: 26px; vertical-align: text-bottom;" class="pl-1">flag</mat-icon>
                  <mat-icon *ngIf="item.is_current_version == 1" style="color:#cf0001; font-size: 26px; vertical-align: text-bottom;" class="pl-1">flag</mat-icon>
                </button>
            </mat-menu>
            </span>
            <!-- MIS Calculation Section Ends ! -->

            <!-- MIS Post Section Begins ! -->
            <button mat-icon-button matTooltip="MIS Post" class="iconsSize" (click)="misPost()">
                <mat-icon class="iconsSize">local_post_office</mat-icon>
            </button>
            <!-- MIS Post Section  Ends ! -->

            <!-- Tally Month Data Begins ! -->
            <button mat-icon-button matTooltip="Financial Books Sync" class="iconsSize" (click)="openDateDialog()"> 
                <mat-icon class="iconsSize">sync</mat-icon>
            </button>
            <!-- Tally Month Data  Ends ! -->

            <!-- MIS Log Begins ! -->
            <button mat-icon-button matTooltip="MIS Log" class="iconsSize">
                <mat-icon class="iconsSize" (click)="getMisLog()">note</mat-icon>
            </button>
            <!-- MIS Log  Ends ! -->

            <!-- MIS Configuration Begins ! -->
            <button mat-icon-button matTooltip="MIS Configurations" class="iconsSize" 
                [matMenuTriggerFor]="confMenu">
                    <mat-icon class="iconsSize">source</mat-icon>
            </button>

            <mat-menu #confMenu="matMenu">
                <button mat-menu-item *ngFor="let item of configurationLink; " [matTooltip]="item.label"
                    (click)=openMisConfiguration(item.path)>{{item.label}}</button>
            </mat-menu>
            <!-- MIS Configuration Ends ! -->

            <!-- MIS Currency Conversion Rate Starts ! -->
            <button mat-icon-button matTooltip="Currency Conversion Rate" class="iconsSize">
                <mat-icon (click)="getCurrencyInfo()" class="iconsSize">currency_exchange</mat-icon>
            </button>
            <!-- MIS Currency Conversion Rate Ends ! -->

            <!-- Timesheet Missing Employee Data Upload Starts ! -->
            <button mat-icon-button matTooltip="Timesheet Missing Employee Data Upload" class="iconsSize" (click)="updateTimesheetMissingEmployee()">
                <mat-icon  class="iconsSize">cloud_upload</mat-icon>
            </button>
            <!-- Timesheet Missing Employee Data Upload Ends ! -->
            
            <button mat-icon-button matTooltip="T&M UBR Posting" class="iconsSize">
                <mat-icon class="iconsSize" [matMenuTriggerFor]="openMenu">menu_open</mat-icon>
            </button>
    <mat-menu #openMenu="matMenu">
      <button mat-menu-item class="drop-btn" (click)="onMisMenuClick('all')">
        <span>Post T&M UBR</span>
      </button>
      <button mat-menu-item class="drop-btn" (click)="onMisMenuClick('filter_t_and_m_timesheet_data')">
        <span>Refresh T&M Timesheet Freezed Data</span>
      </button>
      <button mat-menu-item class="drop-btn" (click)="onMisMenuClick('update_employee_practice')">
        <span>Update T&M Employee Practice</span>
      </button>
      <button mat-menu-item class="drop-btn" (click)="onMisMenuClick('ubr_posting_t_and_m_cost_centers')">
        <span>Post T&M UBR only</span>
      </button>
      <button mat-menu-item class="drop-btn" (click)="onMisMenuClick('carry_frwd_previous_month_ubr')">
        <span>Post Carry Forward UBR only</span>
      </button>
    </mat-menu>
    </div>
        </div>
    </div>
    <div class="row" *ngIf="misDataGridVisibility">
        <div class="col-12">
            <app-mis-data-grid-devextreme [data]=misDataGridData [columnConfig]=misDataGridColumnConfig
            [summaryTotalConfig]  = misDataGridSummaryTotalConfig
                [fileName]=misMenu[misCurrentViewIndex].file_name>
            </app-mis-data-grid-devextreme>
        </div>
    </div>

    <div class="row" *ngIf="!misDataGridVisibility && misVersioningVisibility">
        <div class="col-12 p-0">
            <div class="row pl-3">
                <div class="col-auto tab-group p-0 pt-2 pb-2 pl-2 pr-2 left-tab-group" style="cursor: pointer;" (click)="getCostCenterDetails()" [ngClass]="{'selected-tab': currentActiveTab == 1}">GL Cost Center</div>
                <div class="col-auto tab-group p-0 pt-2 pb-2 pl-2 pr-2 tab-left-center" style="cursor: pointer;" (click)="getCostCenterEmployeeDetails()" [ngClass]="{'selected-tab': currentActiveTab == 2}">Cost Center Employee</div>
                <div class="col-auto tab-group p-0 pt-2 pb-2 pl-2 pr-2 tab-right-center" style="cursor: pointer;" (click)="getSubDivisionDetails()" [ngClass]="{'selected-tab': currentActiveTab == 3}">Sub-Division</div>
                <div class="col-auto tab-group p-0 pt-2 pb-2 pl-2 pr-2 right-tab-group" style="cursor: pointer;" (click)="getSubDivisionEmployeeDetails()" [ngClass]="{'selected-tab': currentActiveTab == 4}">Sub-Division Employee</div>
                <div class="col"> </div>
            </div>
            <div class="row">
                <div class="col-12">
                <app-mis-data-grid-devextreme [data]=misDataGridData [columnConfig]=misDataGridColumnConfig
                [summaryTotalConfig]  = misDataGridSummaryTotalConfig
                [fileName]=misMenu[misCurrentViewIndex].file_name>
            </app-mis-data-grid-devextreme>
            </div>
            </div>
            <!-- <mat-tab-group>
                <mat-tab label="MIS Report 1">
                    <div class="row mt-2" *ngIf="isTemporaryVersion == true">
                        <div class="col-10">
                            <span (click)="editVersionName()" *ngIf="showEditInput == false">
                                <mat-icon style="vertical-align: bottom;
                                color: #cf0001;
                                font-size: 20px;">edit</mat-icon>
                                <span *ngIf="!versionName.value || versionName.value == ' ' ">Enter Version Name</span>
                                <span *ngIf="versionName.value">{{versionName.value}}</span>
                            </span>
                            <span *ngIf="showEditInput == true">
                                <mat-form-field>
                                    <input [formControl]="versionName" class="pb-2" matInput placeholder="Edit Version Name">
                                </mat-form-field>
                            </span>

                        </div>
                        <div class="col-2 mt-2">
                            <div class="mt-2 saveTemporaryVersionBtn pl-2 pr-2 pt-2 pb-2 d-flex justify-content-center" (click)="saveTemporaryVersion()">
                                <span>Save Temporary Version</span>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-2" *ngIf="isTemporaryVersion == false">
                        <div class="col-10">
                            <span>
                                <span style="color: #cf0001;">{{selectedVersionName}}</span>
                            </span>
                        </div>
                        <div class="col-2">
                            <div *ngIf="isCurrentVersion == true" class="mt-2 currentVersionBtn pl-2 pr-2 pt-2 pb-2 d-flex justify-content-center" style="width: 100%">
                                <span>Current Version</span>
                            </div>
                            <div *ngIf="isCurrentVersion == false" class="mt-2 saveTemporaryVersionBtn pl-2 pr-2 pt-2 pb-2 d-flex justify-content-center" (click)="saveCurrentVersion()" style="width: 100%">
                                <span>Save as Current Version</span>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <app-data-upload-grid [id]="1"></app-data-upload-grid>
                    </div>
                 </mat-tab>
                <mat-tab label="MIS Report 2"> MIS Report 2 </mat-tab>
                <mat-tab label="MIS Report 3"> MIS Report 3 </mat-tab>
            </mat-tab-group> -->
        </div>
    </div>
</div>
<ngx-spinner size="medium" type="ball-clip-rotate"  bdColor="rgba(236, 233, 233, 0.8)" color="#cf0001"></ngx-spinner>