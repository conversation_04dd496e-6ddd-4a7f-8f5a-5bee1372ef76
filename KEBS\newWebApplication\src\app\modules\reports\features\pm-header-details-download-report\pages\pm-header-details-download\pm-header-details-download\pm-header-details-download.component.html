<div class="employee-details-style">
  <div class="bg-container">
    <div class="report-screen">
      <div class="align-items-center" style="margin-bottom: 16px">
        <div class="align-items-center">
          <div class="back-btn" (click)="goToReportsMainScreen()">
            <mat-icon class="back-btn-icon">chevron_left</mat-icon>
            Back
          </div>
          <div class="header-title">
            {{ ('report_name' | checkLabel: this.formConfig: 'header-details-reports-download': 'Project Header Report') }}
          </div>
        </div>
      </div>
      <div *ngIf="!loading">
        <form [formGroup]="reportForm">
          <mat-horizontal-stepper #stepper>
            <mat-step label="Report Input" editable="false">
              <div class="align-items-center-justify">
                <div class="col-12 report-height flex-direction-column">
                  <div class="align-items-center" style="margin-bottom: 16px; gap: 10px;">
                    <div class="col-12 p-2 row">
                      <div class="col-4"></div>
                      <!-- <div class="col-4"><h2 style="color: #79ba44; text-align: center;">Projects</h2></div> -->
                      <div class="col-4"></div>
                      <div class="col-6"
                        *ngIf="('finnacial_region_input' | checkActive : this.formConfig: 'header-details-reports-download')">
                        <app-multi-select-search3 class="create-account-field" [list]="financialRegion"
                          formControlName='region' placeholder="Sales Region">
                        </app-multi-select-search3>
                      </div>
                      <div class="col-6"
                        *ngIf="('parent_account_input' | checkActive : this.formConfig: 'header-details-reports-download')">
                        <app-multi-select-search3 class="create-account-field" [list]="parentCustomerList"
                          formControlName='parent_account' placeholder="Customer">
                        </app-multi-select-search3>
                      </div>
                      <div class="col-6"
                        *ngIf="('portfolio_input' | checkActive : this.formConfig: 'header-details-reports-download')">
                        <app-multi-select-search3 class="create-account-field" [list]="portfolioList"
                          formControlName='project_name' placeholder="Portfolio">
                        </app-multi-select-search3>
                      </div>
                      <div class="col-6"
                        *ngIf="('entity_name' | checkActive : this.formConfig: 'header-details-reports-download')">
                        <app-multi-select-search3 class="create-account-field" [list]="legalentityList"
                          formControlName='entity_name' placeholder="SOW Owner">
                        </app-multi-select-search3>
                      </div>
                      <div class="col-6"
                        *ngIf="('division_input' | checkActive : this.formConfig: 'header-details-reports-download')">
                        <app-multi-select-search3 class="create-account-field" [list]="divisionList"
                          formControlName='division_name' placeholder="Division">
                        </app-multi-select-search3>
                      </div>
                      <div class="col-6"
                        *ngIf="('sub_division_input' | checkActive : this.formConfig: 'header-details-reports-download')">
                        <app-multi-select-search3 class="create-account-field" [list]="subdivisionList"
                          formControlName='sub_division_name' placeholder="Sub Division">
                        </app-multi-select-search3>
                      </div>
                      <!-- <div class="col-6" *ngIf="('at_risk' | checkActive : this.formConfig: 'header-details-reports-download')">
                          <app-multi-select-search3 class="create-account-field" [list]="riskList"
                            formControlName='risk_name' placeholder="Risk Status">
                          </app-multi-select-search3>
                        </div> -->
                      <div class="col-6"
                        *ngIf="('project_status' | checkActive : this.formConfig: 'header-details-reports-download')">
                        <app-multi-select-search3 class="create-account-field" [list]="statusList"
                          formControlName='name' placeholder="Project Status">
                        </app-multi-select-search3>
                      </div>
                      <div class="col-6"
                        *ngIf="('SOW_input' | checkActive : this.formConfig: 'header-details-reports-download')">
                        <app-multi-select-search3 class="create-account-field" [list]="projectList"
                          formControlName='sow' placeholder="Project">
                        </app-multi-select-search3>
                      </div>
                    </div>
                  </div>
                  <div class="btn-line-style">
                    <div class="btn-style" [ngStyle]="{ 'pointer-events': apiInProgress ? 'none' : '' }"
                      (click)="onDeleteFilters()">
                      Clear Inputs
                    </div>
                    <!-- <div class="btn-style" [ngStyle]="{ 'pointer-events': apiInProgress ? 'none' : '' }"
                      (click)="onDownloadReport()">
                      Download Report
                    </div> -->
                    <div class="btn-style" [ngStyle]="{ 'pointer-events': apiInProgress ? 'none' : '' }"
                      (click)="onGenerateReport()">
                      Generate Report
                    </div>
                  </div>
                  <!-- <div *ngIf="showDownloadIcon"> -->
                  <ngx-spinner bdColor="rgba(0, 0, 0, 0.8)" size="medium" color="#fff" type="ball-clip-rotate-pulse"
                    [fullScreen]="true">
                    <p style="color: white"> Downloading... </p>
                  </ngx-spinner>
                  <!-- </div> -->
                </div>
              </div>
            </mat-step>
            <mat-step label="Report Result" editable="false">
              <div class="align-items-space-between">
                <div class="back-btn" (click)="goToPreviousScreen()">
                  <mat-icon class="back-btn-icon">chevron_left</mat-icon>
                  Back to Previous Screen
                </div>
                <div class="btn-style" (click)="clearSortAndFilters()">
                  Clear Sort & Filters
                </div>
              </div>
              <div class="report-height">
                <dx-data-grid #dataGrid class="data-grid" [height]="dynamicGridReportHeight" [dataSource]="reportData"
                  [showBorders]="true" [columnAutoWidth]="true" [showColumnLines]="true" [showRowLines]="true"
                  (onExporting)="onExporting($event)">
                  <dxo-filter-row [visible]="true"></dxo-filter-row>
                  <dxo-header-filter [visible]="true"></dxo-header-filter>
                  <dxo-column-chooser [allowSearch]="true" [enabled]="true" mode="select"></dxo-column-chooser>
                  <dxo-export [enabled]="true"></dxo-export>
                  <dxo-scrolling mode="infinite"></dxo-scrolling>
                  

                <dxo-template name="iconTemplate">
                  <div *dxTemplate="let data of 'iconTemplate'" >
                    <ng-container >
                      <mat-icon  (click)="navigateToDetails(data)" style="cursor: pointer">open_in_new</mat-icon>
                    </ng-container>
                
                  </div>
                </dxo-template>
                  <dxi-column dataField="region"
                    *ngIf="('finnacial_region_input' | checkActive : this.formConfig: 'header-details-report-download-columns-names')"
                    caption="{{ ('finnacial_region_input' | checkLabel: this.formConfig: 'header-details-report-download-columns-names': 'Sales Region') }}"
                    alignment="left" visible="true">
                  </dxi-column>
                  <dxi-column dataField="customer_name"
                    *ngIf="('parent_customer_name' | checkActive : this.formConfig: 'header-details-report-download-columns-names')"
                    caption="{{ ('parent_customer_name' | checkLabel: this.formConfig: 'header-details-report-download-columns-names': 'Customer') }}"
                    alignment="left" visible="true">
                  </dxi-column>
                  <dxi-column dataField="portfolio_id"
                  *ngIf="('portfolio_id' | checkActive : this.formConfig: 'header-details-report-download-columns-names')"
                    caption="{{ ('portfolio_id' | checkLabel: this.formConfig: 'header-details-report-download-columns-names': 'Portfolio ID') }}"
                    alignment="left" visible="true">
                  </dxi-column>
                  <dxi-column dataField="portfolio_name"
                  *ngIf="('portfolio_name' | checkActive : this.formConfig: 'header-details-report-download-columns-names')"
                    caption="{{ ('portfolio_name' | checkLabel: this.formConfig: 'header-details-report-download-columns-names': 'Portfolio Name') }}"
                    alignment="left" visible="true">
                  </dxi-column>
                  <dxi-column dataField="entity_name"
                  *ngIf="('legal_entity' | checkActive : this.formConfig: 'header-details-report-download-columns-names')"
                    caption="{{ ('legal_entity' | checkLabel: this.formConfig: 'header-details-report-download-columns-names': 'SOW Owner') }}"
                    alignment="left" visible="true">
                  </dxi-column>
                  <dxi-column dataField="item_name"
                  *ngIf="('SOW_input' | checkActive : this.formConfig: 'header-details-report-download-columns-names')"
                    caption="{{ ('SOW_input' | checkLabel: this.formConfig: 'header-details-report-download-columns-names': 'Project Name') }}"
                    alignment="left" visible="true">
                  </dxi-column>
                  <dxi-column dataField="profit_center"
                  *ngIf="('profit_center' | checkActive : this.formConfig: 'header-details-report-download-columns-names')"
                    caption="{{ ('profit_center' | checkLabel: this.formConfig: 'header-details-report-download-columns-names': 'Project Code') }}"
                    alignment="left" visible="true">
                  </dxi-column>
                  <dxi-column dataField="sow_reference_number"
                  *ngIf="('sow_reference_number' | checkActive : this.formConfig: 'header-details-report-download-columns-names')"
                    caption="{{ ('sow_reference_number' | checkLabel: this.formConfig: 'header-details-report-download-columns-names': 'SOW Reference Number') }}"
                    alignment="left" visible="true">
                  </dxi-column>
                  <dxi-column dataField="planned_start_date"
                  *ngIf="('project_start_date' | checkActive : this.formConfig: 'header-details-report-download-columns-names')"
                    caption="{{ ('project_start_date' | checkLabel: this.formConfig: 'header-details-report-download-columns-names': 'Project start date') }}"
                    alignment="left" visible="true">
                  </dxi-column>
                  <dxi-column dataField="planned_end_date"
                  *ngIf="('project_end_date' | checkActive : this.formConfig: 'header-details-report-download-columns-names')"
                    caption="{{ ('project_end_date' | checkLabel: this.formConfig: 'header-details-report-download-columns-names': 'Project end date') }}"
                    alignment="left" visible="true">
                  </dxi-column>
                  <dxi-column dataField="project_status"
                  *ngIf="('project_status' | checkActive : this.formConfig: 'header-details-report-download-columns-names')"
                    caption="{{ ('project_status' | checkLabel: this.formConfig: 'header-details-report-download-columns-names': 'Project Status') }}"
                    alignment="left" visible="true">
                  </dxi-column>
                  <dxi-column dataField="at_risk"
                  *ngIf="('risk_name' | checkActive : this.formConfig: 'header-details-report-download-columns-names')"
                    caption="{{ ('risk_name' | checkLabel: this.formConfig: 'header-details-report-download-columns-names': 'Risk Status') }}"
                    alignment="left" visible="true">
                  </dxi-column>
                  <dxi-column dataField="service_type_name"
                  *ngIf="('project_type' | checkActive : this.formConfig: 'header-details-report-download-columns-names')"
                    caption="{{('project_type' | checkLabel : this.formConfig: 'header-details-report-download-columns-names': 'Project Type')}}"
                    alignment="left" visible="false">
                  </dxi-column>
                  <dxi-column dataField="product_category_name"
                  *ngIf="('product_category' | checkActive : this.formConfig: 'header-details-report-download-columns-names')"
                    caption="{{('product_category' | checkLabel : this.formConfig: 'header-details-report-download-columns-names': 'Product Category')}}"
                    alignment="left" visible="false">
                  </dxi-column>
                  <dxi-column dataField="delivery_type_name"
                  *ngIf="('delivery_type' | checkActive : this.formConfig: 'header-details-report-download-columns-names')"
                    caption="{{('delivery_type' | checkLabel : this.formConfig: 'header-details-report-download-columns-names': 'Delivery type')}}"
                    alignment="left" visible="false">
                  </dxi-column>
                  <dxi-column dataField="value_chain"
                  *ngIf="('value_chain' | checkActive : this.formConfig: 'header-details-report-download-columns-names')"
                    caption="{{('value_chain' | checkLabel : this.formConfig: 'header-details-report-download-columns-names': 'Value Chain')}}"
                    alignment="left" visible="false">
                  </dxi-column>
                  <dxi-column dataField="opportunity_id"
                  *ngIf="('opportunity_id' | checkActive : this.formConfig: 'header-details-report-download-columns-names')"
                    caption="{{('opportunity_id' | checkLabel : this.formConfig: 'header-details-report-download-columns-names': 'Opportunity Id')}}"
                    alignment="left" visible="false">
                  </dxi-column>
                  <dxi-column dataField="opportunity_code"
                  *ngIf="('opportunity_code' | checkActive : this.formConfig: 'header-details-report-download-columns-names')"
                    caption="{{('opportunity_code' | checkLabel : this.formConfig: 'header-details-report-download-columns-names': 'Opportunity Code')}}"
                    alignment="left" visible="false">
                  </dxi-column>
                  <dxi-column dataField="opportunity_name"
                  *ngIf="('opportunity_name' | checkActive : this.formConfig: 'header-details-report-download-columns-names')"
                    caption="{{('opportunity_name' | checkLabel : this.formConfig: 'header-details-report-download-columns-names': 'Opportunity Name')}}"
                    alignment="left" visible="false">
                  </dxi-column>
                  <dxi-column dataField="status_name"
                  *ngIf="('opportunity_status' | checkActive : this.formConfig: 'header-details-report-download-columns-names')"
                    caption="{{('opportunity_status' | checkLabel : this.formConfig: 'header-details-report-download-columns-names': 'Opportunity status')}}"
                    alignment="left" visible="false">
                  </dxi-column>
                  <dxi-column dataField="quote_id"
                  *ngIf="('quote_id' | checkActive : this.formConfig: 'header-details-report-download-columns-names')"
                    caption="{{('quote_id' | checkLabel : this.formConfig: 'header-details-report-download-columns-names': 'Quote Id')}}"
                    alignment="left" visible="false">
                  </dxi-column>
                  <dxi-column dataField="quote_name"
                  *ngIf="('quote_name' | checkActive : this.formConfig: 'header-details-report-download-columns-names')"
                    caption="{{('quote_name' | checkLabel : this.formConfig: 'header-details-report-download-columns-names': 'Quote Name')}}"
                    alignment="left" visible="false">
                  </dxi-column>
                  <dxi-column dataField="overall_quote_value"
                  *ngIf="('quote_value' | checkActive : this.formConfig: 'header-details-report-download-columns-names')"
                    caption="{{('quote_value' | checkLabel : this.formConfig: 'header-details-report-download-columns-names': 'Quote value')}}"
                    alignment="left" visible="false">
                  </dxi-column>
                  <dxi-column dataField="overall_item_value"
                  *ngIf="('order_value' | checkActive : this.formConfig: 'header-details-report-download-columns-names')"
                    caption="{{('order_value' | checkLabel : this.formConfig: 'header-details-report-download-columns-names': 'Order value')}}"
                    alignment="left" visible="false">
                  </dxi-column>
                  <dxi-column dataField="overall_milestone_value"
                  *ngIf="('overall_milestone_value' | checkActive : this.formConfig: 'header-details-report-download-columns-names')"
                    caption="{{('overall_milestone_value' | checkLabel : this.formConfig: 'header-details-report-download-columns-names': 'Overall milestone value')}}"
                    alignment="left" visible="false">
                  </dxi-column>
                  <dxi-column dataField="project_currency"
                  *ngIf="('project_currency' | checkActive : this.formConfig: 'header-details-report-download-columns-names')"
                    caption="{{('project_currency' | checkLabel : this.formConfig: 'header-details-report-download-columns-names': 'Project currency')}}"
                    alignment="left" visible="false">
                  </dxi-column>
                  <dxi-column dataField="project_manager_name"
                  *ngIf="('project_manager_name' | checkActive : this.formConfig: 'header-details-report-download-columns-names')"
                    caption="{{('project_manager_name' | checkLabel : this.formConfig: 'header-details-report-download-columns-names': 'Project Manager')}}"
                    alignment="left" visible="false">
                  </dxi-column>
                  <dxi-column dataField="SDM_name"
                  *ngIf="('SDM' | checkActive : this.formConfig: 'header-details-report-download-columns-names')"
                    caption="{{('SDM' | checkLabel : this.formConfig: 'header-details-report-download-columns-names': 'SDM')}}"
                    alignment="left" visible="false">
                  </dxi-column>
                  <dxi-column dataField="CSM_name"
                  *ngIf="('CSM' | checkActive : this.formConfig: 'header-details-report-download-columns-names')"
                    caption="{{('CSM' | checkLabel : this.formConfig: 'header-details-report-download-columns-names': 'CSM')}}"
                    alignment="left" visible="false">
                  </dxi-column>
                  <dxi-column dataField="po_number"
                  *ngIf="('po_number' | checkActive : this.formConfig: 'header-details-report-download-columns-names')"
                    caption="{{('po_number' | checkLabel : this.formConfig: 'header-details-report-download-columns-names': 'PO Number')}}"
                    alignment="left" visible="false">
                  </dxi-column>
                  <dxi-column dataField="po_date"
                  *ngIf="('po_date' | checkActive : this.formConfig: 'header-details-report-download-columns-names')"
                    caption="{{('po_date' | checkLabel : this.formConfig: 'header-details-report-download-columns-names': 'PO Date')}}"
                    alignment="left" visible="false">
                  </dxi-column>
                  <dxi-column dataField="forecasted_date"
                  *ngIf="('forecasted_date' | checkActive : this.formConfig: 'header-details-report-download-columns-names')"
                    caption="{{('forecasted_date' | checkLabel : this.formConfig: 'header-details-report-download-columns-names': 'Forecasted Date')}}"
                    alignment="left" visible="false">
                  </dxi-column>
                  <dxi-column dataField="latest_billing_posting_date"
                  *ngIf="('latest_billing_posting_date' | checkActive : this.formConfig: 'header-details-report-download-columns-names')"
                    caption="{{('latest_billing_posting_date' | checkLabel : this.formConfig: 'header-details-report-download-columns-names': 'Latest Billing Posting Date')}}"
                    alignment="left" visible="false">
                  </dxi-column>
                  <dxi-column dataField="created_by"
                  *ngIf="('created_by' | checkActive : this.formConfig: 'header-details-report-download-columns-names')"
                    caption="{{('created_by' | checkLabel : this.formConfig: 'header-details-report-download-columns-names': 'Created By')}}"
                    alignment="left" visible="false">
                  </dxi-column>
                  <dxi-column dataField="created_on"
                  *ngIf="('created_on' | checkActive : this.formConfig: 'header-details-report-download-columns-names')"
                    caption="{{('created_on' | checkLabel : this.formConfig: 'header-details-report-download-columns-names': 'Created On')}}"
                    alignment="left" visible="false">
                  </dxi-column>
                  <dxi-column cellTemplate="iconTemplate"  width="100" *ngIf="('actions' | checkActive : this.formConfig: 'header-details-report-download-columns-names')"
                  caption="{{ ('actions' | checkLabel: this.formConfig: 'header-details-report-download-columns-names': 'Project Link') }}"
                  alignment="left" visible="true">
                   
                </dxi-column>
                  <dxo-export [enabled]="true" fileName="Project Header Details Report Download"></dxo-export>
                </dx-data-grid>
              </div>
            </mat-step>
          </mat-horizontal-stepper>
        </form>
      </div>
      <div *ngIf="loading">
        <mat-spinner class="green-spinner" diameter="30"></mat-spinner>
      </div>
    </div>
  </div>
</div>