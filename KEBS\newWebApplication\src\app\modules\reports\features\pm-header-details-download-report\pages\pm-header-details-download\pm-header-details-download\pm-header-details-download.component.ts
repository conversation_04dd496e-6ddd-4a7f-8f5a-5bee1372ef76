import { Component, HostListener, OnInit, ViewChild } from '@angular/core';
import { PmHeaderDetailsDownloadService } from 'src/app/modules/reports/features/pm-header-details-download-report/services/pm-header-details-download.service';
import { PmMasterService } from 'src/app/modules/project-management/services/pm-master.service';
import { MatStepper } from '@angular/material/stepper';
import * as _ from 'underscore';
import { FormBuilder, FormControl } from '@angular/forms';
import { Router } from '@angular/router';
import { debounceTime } from 'rxjs/operators';
import { JsonToExcelService } from 'src/app/services/excel/json-to-excel.service';
import { NgxSpinnerService } from 'ngx-spinner';
import { ToasterService } from 'src/app/services/toaster-service/toaster.service';
import moment from 'moment';


@Component({
  selector: 'app-pm-header-details-download',
  templateUrl: './pm-header-details-download.component.html',
  styleUrls: ['./pm-header-details-download.component.scss']
})
export class PmHeaderDetailsDownloadComponent implements OnInit {

  @ViewChild('dataGrid', { static: false }) dataGrid: any;
  @ViewChild('stepper') stepper: MatStepper;

  dynamicHeight: string;
  dynamicReportHeight: string;
  dynamicGridReportHeight: string;
  
  loading: boolean=true;
  reportData: boolean=false;
  formConfig: any=[];
  apiInProgress: boolean=false;
  projectId:any;
  financialRegion: any=[]
  customerList: any=[];
  parentCustomerList: any=[];
  childCustomerList: any=[];
  projectList: any=[];
  legalentityList: any=[];
  portfolioList: any=[];
  divisionList: any=[];
  subdivisionList: any=[];
  // riskList: any=[
  //   { id: 1, name: 'Risk' },
  //   { id: 2, name: 'Secured' }
  // ];
  statusList: any=[];
  retrieveMessages: any;
  button: any;
  shades: any;
  last_updated_on: any;
  column_names:any=[]
  reportForm = this.fb.group({
    region:[],
    sow:[],
    parent_account:[],
    child_account:[],
    project_name: [],
    entity_name: [],
    division_name: [],
    sub_division_name: [],
    //risk_name: [],
    name: []

  })
 allProjects : any;
 allDivision : any;
  allsubDivision: any;
  constructor(private detailService: PmHeaderDetailsDownloadService,
    private masterService: PmMasterService,
    private router: Router,
    private excelService: JsonToExcelService,
    private spinnerService: NgxSpinnerService,
    private toasterService: ToasterService,
    private fb: FormBuilder) { 

      
  }

  async ngOnInit() {

    this.calculateDynamicContentHeight()

    this.loading = true;
    await this.masterService.getPMFormCustomizeConfigV().then((res)=>{
        this.formConfig = res

        this.retrieveMessages = _.where(this.formConfig, { type: "landing-page", field_name: "messages", is_active: true });
        const retrieveStyles = _.where(this.formConfig, { type: "project-theme", field_name: "styles", is_active: true });
        this.button = retrieveStyles.length > 0 ? retrieveStyles[0].data.button_color ? retrieveStyles[0].data.button_color : "#90ee90" : "#90ee90";
        this.shades = retrieveStyles.length > 0 ? retrieveStyles[0].data.shades_color ? retrieveStyles[0].data.shades_color : "#C9E3B4" : "#C9E3B4";
        document.documentElement.style.setProperty('--landingshades', this.shades)
        document.documentElement.style.setProperty('--landingbutton', this.button)
        this.column_names= _.where(this.formConfig, { type: "header-details-report-download-columns-names",is_active: true });
        console.log(this.column_names)
    })

    await this.masterService.getProfitLossList().then((res)=>{
        this.financialRegion= res
    })

    await this.detailService.getProjectList(this.reportForm.value).then((res)=>{
      if(res['messType']=="S")
      {
        this.projectList= res['data']
        this.allProjects = res['data']
      }else{
        this.projectList = [];
      }
    })

    await this.detailService.getPortfolioList().then((res)=>{
      if(res['messType']=="S"){
        this.portfolioList=res['data']
      }
    })

    await this.detailService.getlegalentity().then((res)=>{
      if(res['messType']=="S"){
        this.legalentityList=res['data']
      }
    })

    await this.detailService.getdivision().then((res)=>{
      if(res['messType']=="S"){
        this.divisionList=res['data']
        this.allDivision=res['data']
      }else{
        this.divisionList=[]
      }
    })

    await this.detailService.getsubdivision().then((res)=>{
      if(res['messType']=="S"){
        this.subdivisionList=res['data']
        this.allsubDivision=res['data']
      }else{
        this.subdivisionList=[]
      }
    })

    await this.detailService.getProjectstatus().then((res)=>{
      if(res['messType']=="S"){
        this.statusList=res['data']
      }
    })

    await this.masterService.getCustomerList().then((res)=>{
      this.customerList = res
      this.parentCustomerList = _.filter(this.customerList,(res)=>{
        if(res['parent_account']==0 || res['parent_account']==null || res['parent_account']=="" || res['parent_account']=="null")
        {
          return res;
        }
      })
    })

    this.loading = false;

    this.reportForm.get('parent_account').valueChanges.pipe(debounceTime(1000)).subscribe((res)=>{
      console.log("REs", res)
      if(res)
      {
          this.childCustomerList  = _.filter(this.customerList,(customer)=>{
            if(_.contains(res, customer['parent_account']))
            {
                return res;
            }
          })
      }
      else
      {
        this.childCustomerList=[];
      }
    })

    this.reportForm.get('region').valueChanges.pipe(debounceTime(1000)).subscribe((res)=>{
      console.log("REs", res)
      if(res && res.length>0)
      {
         this.projectList  = _.filter(this.allProjects,(temp)=>{
          if(_.contains(res, temp['p_and_l_id']))
          {
              return res;
          }
        })
      }
      else
      {
          this.projectList=this.allProjects
      }
    })

    this.reportForm.get('entity_name').valueChanges.pipe(debounceTime(1000)).subscribe((res)=>{
      console.log("REs", res)
      if(res)
      {
        this.projectList  = _.filter(this.allProjects,(temp)=>{
            if(_.contains(res, temp['id']))
            {
                return res;
            }
          })
      }
      else
      {
        this.projectList=this.allProjects
      }
    })

    this.reportForm.get('parent_account').valueChanges.pipe(debounceTime(1000)).subscribe((res)=>{
      console.log("REs", res)
      if(res && res.length>0)
      {
         this.projectList  = _.filter(this.allProjects,(temp)=>{
          if(_.contains(res, temp['customer_id']))
          {
              return res;
          }
        })
      }
      else
      {
          this.projectList=this.allProjects
      }
    })

    this.reportForm.get('project_name').valueChanges.pipe(debounceTime(1000)).subscribe((res)=>{
      console.log("REs", res)
      if(res && res.length>0)
      {
         this.projectList  = _.filter(this.allProjects,(temp)=>{
          if(_.contains(res, temp['id']))
          {
              return res;
          }
        })
      }
      else
      {
          this.projectList=this.allProjects
      }
    })

    this.reportForm.get('entity_name').valueChanges.pipe(debounceTime(1000)).subscribe((res)=>{
      console.log("REs", res)
      if(res && res.length>0)
      {
         this.divisionList = _.filter(this.allDivision,(temp)=>{
          if(_.contains(res, temp['id']))
          {
              return res;
          }
        })
      }
      else
      {
          this.divisionList=this.allDivision
      }
    })

    this.reportForm.get('division_name').valueChanges.pipe(debounceTime(1000)).subscribe((res)=>{
      console.log("REs", res)
      if(res && res.length>0)
      {
         this.subdivisionList = _.filter(this.allsubDivision,(temp)=>{
          if(_.contains(res, temp['id']))
          {
              return res;
          }
        })
      }
      else
      {
          this.subdivisionList=this.allsubDivision
      }
    })

    this.reportForm.get('sub_division_name').valueChanges.pipe(debounceTime(1000)).subscribe((res)=>{
      console.log("REs", res)
      if(res && res.length>0)
      {
         this.projectList = _.filter(this.allProjects,(temp)=>{
          if(_.contains(res, temp['id']))
          {
              return res;
          }
        })
      }
      else
      {
        this.projectList=this.allProjects
      }
    })

    this.reportForm.get('name').valueChanges.pipe(debounceTime(1000)).subscribe((res)=>{
      console.log("REs", res)
      if(res && res.length>0)
      {
         this.projectList = _.filter(this.allProjects,(temp)=>{
          if(_.contains(res, temp['status_id']))
          {
              return res;
          }
        })
      }
      else
      {
        this.projectList=this.allProjects
      }
    })
    
    
  }
 

  onDeleteFilters(){
    this.childCustomerList =[];
    this.reportForm.reset();
  }


  async onGenerateReport(){
  
    await this.detailService.getProjectHeaderDetailsReport(this.reportForm.value).then((res)=>{
      if(res['messType']=="S")
      {
          this.reportData = res['data']

          this.apiInProgress=false;
      }
      else
      {
          this.apiInProgress=false;
      }

    })

    this.stepper.next();
  }
  


  async onDownloadReport(){

    
    this.spinnerService.show();
    await this.detailService.getProjectHeaderDetailsReport(this.reportForm.value).then((res)=>{
      if(res['messType']=="S")
      { 
        let columnMappings = this.column_names[0].data
    
        // Modify column names
        let modifiedData = res['data'].map(item => {
          const modifiedItem = {};
          for (const key in item) {
            if (Object.prototype.hasOwnProperty.call(columnMappings, key)) {
              modifiedItem[columnMappings[key]] = item[key];
            }
          }
          return modifiedItem;
        });
        this.spinnerService.hide();
        this.toasterService.showSuccess("Project Header Details Report Message", "Report Downloaded Successfully", 3000);
        this.excelService.exportAsExcelFile(modifiedData, "Project Header Details Report");
      }
      else
      {
          this.apiInProgress=false;
      }

    })

  }
 
async navigateToDetails(data: any) {
  console.log(data)
  
  const projectName = data.data.item_name;
  const itemId = data.data.project_item_id;
  await this.detailService.getProjectId(data.data.project_item_id).then((res)=>{
    if(res['messType']=="S")
    {
        this.projectId = res['data']
    }
  })

  const projectUrl = this.getProjectUrlLink(this.projectId, projectName, itemId);
  window.open(projectUrl, '_blank');

// Open the URL in a new tab

}

urlEncodeURIComponent(str) {
  return encodeURIComponent(str).replace(/[!'()*]/g, function (c) {
      return "%" + c.charCodeAt(0).toString(16);
  });
}

getProjectUrlLink(project_id, project_name, item_id) {
  return `/main/project-management/${project_id}/${this.urlEncodeURIComponent(project_name)}/${item_id}/${this.urlEncodeURIComponent(project_name)}`;
}

  

  goToPreviousScreen(){
    // this.onDeleteFilters();
    this.clearSortAndFilters();
    this.stepper.reset();
  }

  clearSortAndFilters() {
    this.dataGrid.instance.clearSorting();
    this.dataGrid.instance.clearFilter();
  }

  goToReportsMainScreen(){
    this.router.navigate(['/main/reports']);
  }


  onExporting(event){

  }


  @HostListener('window:resize')
  onResize() {
    this.calculateDynamicContentHeight();
  }
  //Adjust UI size based on window size
  calculateDynamicContentHeight() {
    this.dynamicHeight = window.innerHeight - 104 + 'px';
    document.documentElement.style.setProperty(
      '--dynamicHeight',
      this.dynamicHeight
    );
    this.dynamicGridReportHeight = window.innerHeight - 240 + 'px';
    document.documentElement.style.setProperty(
      '--dynamicGridReportHeight',
      this.dynamicGridReportHeight
    );
    this.dynamicReportHeight = window.innerHeight - 223 + 'px';
    document.documentElement.style.setProperty(
      '--dynamicReportHeight',
      this.dynamicReportHeight
    );
  }
}

