import { Component, ElementRef, HostListener, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { takeUntil } from 'rxjs/operators';
import { Subject, Subscription } from 'rxjs';
import * as _ from 'underscore';
import * as moment from 'moment';

import { LoginService } from 'src/app/services/login/login.service';
import { FilterServiceService } from 'src/app/modules/project-management/shared-lazy-loaded/components/filters-common-dialog/services/filter-service.service';
import { MasterService } from './../../services/master/master.service'
import { ToasterService } from './../../components/custom-toaster/toaster.service'
@Component({
  selector: 'app-dashboard-landing-page',
  templateUrl: './dashboard-landing-page.component.html',
  styleUrls: ['./dashboard-landing-page.component.scss']
})
export class DashboardLandingPageComponent implements OnInit {

  protected _onDestroy = new Subject<void>();

  reportLoader: boolean = false;
  widgetsLoader: boolean = false;
  reportId: any;
  aid: number = null;
  oid: string = null;
  reportDetails: any = {};
  widgetsList: any = [];

  selectedRange: { startDate: moment.Moment; endDate: moment.Moment };
  dateRanges: any = {};
  startDate: string = '';
  endDate: string = '';
  reportDateConfig: any;
  loadingImage: any;
  isReportDateConfigAvailable: any;
  isReportLoading: boolean = false;
  isLoading: boolean = false;
  isSubtabsApplicable: boolean = false;
  selectedToggle: any;

  filterSubscription$: Subscription;
  filterData: any = {};
  filterQuery: string = '';
  appliedFilter: any = [];
  isFilterActive: boolean = false;
  activeTabDetails: any;
  showFilterDisplay: boolean = true;


  constructor(
    private _route: ActivatedRoute,
    private _toaster: ToasterService,
    private _loginService: LoginService,
    private _filterService: FilterServiceService,
    private _masterService: MasterService
  ) { }

  async ngOnInit() {
    this.reportLoader = true;
    this.widgetsLoader = true;
    let snapshotData = this._route.snapshot.data;
    if (snapshotData) {
      this.reportId = snapshotData.reportId;
    }

    this.calculateDynamicContentHeight();

    await Promise.all([
      this.getDashboardUiConfiguration('theme'),
      this.getDashboardMainConfiguration(),
      // this.getDashboardWidgetConfiguration(),
      this.getDashboardDateFilterConfiguration(),
    ])
      .then(async (res) => {
        await this.setDateRanges();
        await this.setDefaultDateRange();
      })
      .catch((err) => {
        console.log(err);
        // this.navigateToPreviousRoute();
      });
    const defaultTab = this.reportDetails.sub_tabs.find((tab) => tab.is_active);
    if (defaultTab) {
      const defaultEvent = { value: defaultTab };
      this.selectToggle(defaultEvent);
    }
    else {
      this._toaster.showError(
        'Error',
        'Error in Fetching Dashboard Configuration',
        7000
      );
    }
    this.reportLoader = false;
    this.widgetsLoader = false;
  }

  /**
 * @description Apply filter and generate query
 */
  async applyFilter() {
    let filter = await this.getUserFilterConfig();
    if (filter && filter !== '' && filter != null) {
      let filterVal =
        filter && filter['filterConfig'] && filter['filterConfig']['filterData']
          ? filter['filterConfig']['filterData']
          : [];
      let query = this._filterService.generateConditionBasedQuery(filterVal);
      this.appliedFilter = filterVal;
      this.filterQuery = query ? query : '';
      this.filterData = filterVal ? filterVal : []
    }
    this.widgetsList = [];
    await this.getDashboardWidgets();
  }

  /**
   * @description To open filter dialog
   */
  async openFilterDialog() {
    this._filterService.openFilterLandingPage(
      this.reportId,
      this.activeTabDetails?.filter_sub_application_id,
      this.filterData
    );
  }

  /**
* @description Fetching the User Filter Config
*/
  getUserFilterConfig() {
    if (
      !this.activeTabDetails?.filter_sub_application_id ||
      this.activeTabDetails?.filter_sub_application_id == ''
    ) {
      return;
    }

    return new Promise((resolve, reject) => {
      this._filterService
        .getFilterUserConfig(
          this.reportId,
          this.activeTabDetails?.filter_sub_application_id
        )
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res) => {
            if (res['messType'] == 'S') {
              resolve(res['data']);
            } else {
              resolve([]);
            }
          },
          error: (err) => {
            // this._toaster.showError(
            //   'Error',
            //   'Error in Fetching User Filter Config',
            //   7000
            // );
            console.log("filter error")
            resolve([]);
          },
        });
    });
  }

  /**
* @description Set date ranges for date picker
*/
  async setDateRanges() {
    let dateFilterConfig = this.reportDateConfig?.config;
    if (dateFilterConfig && dateFilterConfig.length > 0) {
      for (let i = 0; i < dateFilterConfig.length; i++) {
        if (dateFilterConfig[i] == 1) {
          this.dateRanges['This Month'] = [
            moment().startOf('month'),
            moment().endOf('month'),
          ];
        } else if (dateFilterConfig[i] == 2) {
          this.dateRanges['Previous Month'] = [
            moment().subtract(1, 'month').startOf('month'),
            moment().subtract(1, 'month').endOf('month'),
          ];
        } else if (dateFilterConfig[i] == 3) {
          this.dateRanges['Next Month'] = [
            moment().add(1, 'month').startOf('month'),
            moment().add(1, 'month').endOf('month'),
          ];
        } else if (dateFilterConfig[i] == 4) {
          this.dateRanges['Upcoming 3 Months'] = [
            moment().startOf('month'),
            moment().add(2, 'month').endOf('month'),
          ];
        } else if (dateFilterConfig[i] == 5) {
          this.dateRanges['Previous 3 Months'] = [
            moment().subtract(2, 'month').startOf('month'),
            moment().endOf('month'),
          ];
        } else if (dateFilterConfig[i] == 6) {
          this.dateRanges['This Year'] = [
            moment().startOf('year'),
            moment().endOf('year'),
          ];
        } else if (dateFilterConfig[i] == 7) {
          this.dateRanges['Previous Year'] = [
            moment().subtract(1, 'year').startOf('year'),
            moment().subtract(1, 'year').endOf('year'),
          ];
        } else if (dateFilterConfig[i] == 8) {
          this.dateRanges['Next Year'] = [
            moment().add(1, 'year').startOf('year'),
            moment().add(1, 'year').endOf('year'),
          ];
        } else if (dateFilterConfig[i] == 9) {
          this.dateRanges['Current FY'] = [
            moment()
              .subtract(moment().month() < 3 ? 1 : 0, 'year')
              .month(3)
              .date(1)
              .startOf('day'),
            moment()
              .add(moment().month() >= 3 ? 1 : 0, 'year')
              .month(2)
              .date(31)
              .endOf('day'),
          ];
        } else if (dateFilterConfig[i] == 10) {
          this.dateRanges['Previous FY'] = [
            moment()
              .subtract(moment().month() < 3 ? 2 : 1, 'years')
              .month(3)
              .date(1)
              .startOf('day'),
            moment()
              .subtract(moment().month() < 3 ? 1 : 0, 'years')
              .month(2)
              .date(31)
              .endOf('day'),
          ];
        } else if (dateFilterConfig[i] == 11) {
          this.dateRanges['Next FY'] = [
            moment()
              .add(moment().month() < 3 ? 0 : 1, 'years')
              .month(3)
              .date(1)
              .startOf('day'),
            moment()
              .add(moment().month() < 3 ? 1 : 2, 'years')
              .month(2)
              .date(31)
              .endOf('day'),
          ];
        } else if (dateFilterConfig[i] == 12) {
          this.dateRanges['This Week'] = [
            moment().startOf('week'),
            moment().endOf('week'),
          ];
        } else if (dateFilterConfig[i] == 13) {
          this.dateRanges['Previous Week'] = [
            moment().subtract(1, 'weeks').startOf('week'),
            moment().subtract(1, 'weeks').endOf('week'),
          ];
        } else if (dateFilterConfig[i] == 14) {
          this.dateRanges['Next Week'] = [
            moment().add(1, 'weeks').startOf('week'),
            moment().add(1, 'weeks').endOf('week'),
          ];
        } else if (dateFilterConfig[i] == 15) {
          this.dateRanges['Today'] = [
            moment(),
            moment(),
          ];
        }
        else if (dateFilterConfig[i] == 15) {
          this.dateRanges['Today'] = [
            moment(),
            moment(),
          ];
        }
      }
    }
  }


  /**
   * @description Set default date range
   */
  async setDefaultDateRange() {
    let defaultConfig = this.reportDateConfig?.default_config;
    if (defaultConfig) {
      if (defaultConfig == 1) {
        this.selectedRange = {
          startDate: moment().startOf('month'),
          endDate: moment().endOf('month'),
        };
      } else if (defaultConfig == 2) {
        this.selectedRange = {
          startDate: moment().subtract(1, 'month').startOf('month'),
          endDate: moment().subtract(1, 'month').endOf('month'),
        };
      } else if (defaultConfig == 3) {
        this.selectedRange = {
          startDate: moment().add(1, 'month').startOf('month'),
          endDate: moment().add(1, 'month').endOf('month'),
        };
      } else if (defaultConfig == 4) {
        this.selectedRange = {
          startDate: moment().startOf('month'),
          endDate: moment().add(2, 'month').endOf('month'),
        };
      } else if (defaultConfig == 5) {
        this.selectedRange = {
          startDate: moment().subtract(2, 'month').startOf('month'),
          endDate: moment().endOf('month'),
        };
      } else if (defaultConfig == 6) {
        this.selectedRange = {
          startDate: moment().startOf('year'),
          endDate: moment().endOf('year'),
        };
      } else if (defaultConfig == 7) {
        this.selectedRange = {
          startDate: moment().subtract(1, 'year').startOf('year'),
          endDate: moment().subtract(1, 'year').endOf('year'),
        };
      } else if (defaultConfig == 8) {
        this.selectedRange = {
          startDate: moment().add(1, 'year').startOf('year'),
          endDate: moment().add(1, 'year').endOf('year'),
        };
      } else if (defaultConfig == 9) {
        this.selectedRange = {
          startDate: moment()
            .subtract(moment().month() < 3 ? 1 : 0, 'year')
            .month(3)
            .date(1)
            .startOf('day'),
          endDate: moment()
            .add(moment().month() >= 3 ? 1 : 0, 'year')
            .month(2)
            .date(31)
            .endOf('day'),
        };
      } else if (defaultConfig == 10) {
        this.selectedRange = {
          startDate: moment()
            .subtract(moment().month() < 3 ? 2 : 1, 'years')
            .month(3)
            .date(1)
            .startOf('day'),
          endDate: moment()
            .subtract(moment().month() < 3 ? 1 : 0, 'years')
            .month(2)
            .date(31)
            .endOf('day'),
        };
      } else if (defaultConfig == 11) {
        this.selectedRange = {
          startDate: moment()
            .add(moment().month() < 3 ? 0 : 1, 'years')
            .month(3)
            .date(1)
            .startOf('day'),
          endDate: moment()
            .add(moment().month() < 3 ? 1 : 2, 'years')
            .month(2)
            .date(31)
            .endOf('day'),
        };
      } else if (defaultConfig == 12) {
        this.selectedRange = {
          startDate: moment().startOf('week'),
          endDate: moment().endOf('week'),
        };
      } else if (defaultConfig == 13) {
        this.selectedRange = {
          startDate: moment().subtract(1, 'weeks').startOf('week'),
          endDate: moment().subtract(1, 'weeks').endOf('week'),
        };
      } else if (defaultConfig == 14) {
        this.selectedRange = {
          startDate: moment().add(1, 'weeks').startOf('week'),
          endDate: moment().add(1, 'weeks').endOf('week'),
        };
      } else if (defaultConfig == 15) {
        this.selectedRange = {
          startDate: moment(),
          endDate: moment(),
        };
      } 
      else {
        this.selectedRange = {
          startDate: moment().startOf('month'),
          endDate: moment().endOf('month'),
        };
      }
    } else {
      this.selectedRange = {
        startDate: moment().startOf('month'),
        endDate: moment().endOf('month'),
      };
    }
  }

  /**
 * @description Go back to main reports page
 */
  navigateToPreviousRoute() {
    history.back();
  }

  /**
 * @description Gets All Reports Master UI Config
 * @param {string} key
 */
  async getDashboardUiConfiguration(key) {
    return new Promise((resolve, reject) =>
      this._masterService
        .getDashboardUiConfiguration(key)
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res) => {
            if (res['err'] == false) {
              if (key == 'theme') {
                document.documentElement.style.setProperty(
                  '--reportFontFamily',
                  res['data']['fontFamily']
                );
                document.documentElement.style.setProperty(
                  '--reportPrimaryColor',
                  res['data']['primaryColor']
                );
                document.documentElement.style.setProperty(
                  '--reportSecondaryColor1',
                  res['data']['secondaryColor1']
                );
                this.loadingImage = res['data']['UI-LOADING'];
              }
            } else {
              this._toaster.showError('Error', res['msg'], 7000);
            }
            resolve(true);
          },
          error: (err) => {
            this._toaster.showError(
              'Error',
              err.msg ? err.msg : 'Master Data Retrieval Failed!',
              7000
            );
            reject();
          },
        })
    );
  }

  /**
* @description Gets Report Date Filter Details
*/
  async getDashboardDateFilterConfiguration() {
    return new Promise((resolve, reject) =>
      this._masterService
        .getDashboardDateFilterConfiguration(this.reportId)
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res) => {
            if (res['err'] == false) {
              this.reportDateConfig =
                res['data'] ? res['data'] : {};
              this.isReportDateConfigAvailable =
                res['data'] ? true : false;
            } else {
              this._toaster.showError('Error', res['msg'], 7000);
            }
            resolve(true);
          },
          error: (err) => {
            this._toaster.showError(
              'Error',
              err.msg
                ? err.msg
                : 'Report Date Filter Configuration Details Retrieval Failed!',
              7000
            );
            reject();
          },
        })
    );
  }

  /**
 * @description Gets Report Details
 */
  async getDashboardMainConfiguration() {
    return new Promise((resolve, reject) =>
      this._masterService
        .getDashboardMainConfiguration(this.reportId)
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res) => {
            if (res['err'] == false) {
              if (res['data']) {
                this.reportDetails = res['data'];
                this.isSubtabsApplicable = this.reportDetails?.sub_tabs && this.reportDetails?.sub_tabs.length > 0 ? true : false;
                if (this.isSubtabsApplicable) {
                  this.activeTabDetails = this.reportDetails?.sub_tabs?.filter(tab => tab.is_selected === true) || [];
                  this.selectedToggle = this.activeTabDetails && this.activeTabDetails.length > 0 ? this.activeTabDetails[0] : [];
                  this.activeTabDetails = this.activeTabDetails && this.activeTabDetails.length > 0 ? this.activeTabDetails[0] : [];
                }
              } else {
                this.navigateToPreviousRoute();
                this.isReportLoading = false;
                this.isLoading = false;
              }
            } else {
              this.navigateToPreviousRoute();
              this.isReportLoading = false;
              this.isLoading = false;
              this._toaster.showError('Error', res['msg'], 7000);
            }
            resolve(true);
          },
          error: (err) => {
            this._toaster.showError(
              'Error',
              err.msg ? err.msg : 'Report Details Retrieval Failed!',
              7000
            );
            reject();
          },
        })
    );
  }

  @HostListener('window:resize')
  onResize() {
    this.calculateDynamicContentHeight();
  }

  /**
   * @description Calculates dynamic height based on screen size
   */
  calculateDynamicContentHeight() {
    document.documentElement.style.setProperty(
      '--reportDynamicHeight',
      window.innerHeight - 77 + 'px'
    );
    document.documentElement.style.setProperty(
      '--reportDynamicWidgetsConatinerHeight',
      window.innerHeight - 80 + 'px'
    );
    document.documentElement.style.setProperty(
      '--reportDynamicSubContainerHeight',
      window.innerHeight - 85 + 'px'
    );
  }

  /**
* @description On date range changes
* @param {object} event
*/
  async datesUpdated(event) {
    this.startDate = moment(this.selectedRange.startDate).format('DD MMM YYYY');
    this.endDate = moment(this.selectedRange.endDate).format('DD MMM YYYY');
    if (!this.isLoading) {
      this.applyFilter();
    }
  }

  async selectToggle(event) {
    this.showFilterDisplay = false;
    setTimeout(() => {
      this.showFilterDisplay = true;
    }, 1000);
    if (this.filterSubscription$) {
      this.filterSubscription$.unsubscribe();
    }
    this.selectedToggle = event.value;
    this.activeTabDetails = event.value;
    this.filterSubscription$ = this._filterService
      .getFilterConfig(
        this.reportId,
        this.activeTabDetails?.filter_sub_application_id
      )
      .subscribe(async (filterList) => {
        this.applyFilter();
      });
    this.widgetsList = []
    await this.getDashboardWidgets();
  }

  /**
   * @description Gets All Dashboard Widgets
   */
  async getDashboardWidgets() {
    this.widgetsLoader = true;
    let payload = {
      aid: this.aid,
      oid: this.oid,
      reportId: this.reportId,
      subTabId: this.activeTabDetails.id,
    };
    return new Promise((resolve, reject) =>
      this._masterService
        .getDashboardWidgets(payload)
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res) => {
            if (res['err'] == false) {
              this.widgetsList = res['data'];
              this.widgetsList.sort((a, b) => a.position - b.position);
              this.widgetsLoader = false;
            } else {
              this._toaster.showError('Error', res['msg'], 7000);
              this.widgetsLoader = false;
            }
            resolve(true);
          },
          error: (err) => {
            this._toaster.showError(
              'Error',
              err.msg ? err.msg : 'Widgets Data Retrieval Failed!',
              7000
            );
            reject();
          },
        })
    );
  }

}
