<div class="widget-container">
    <div class="header">
      <div class="d-flex align-items-center" class="title-header">
        <div class="chart-title">{{ widgetConfig?.label }}</div>
      </div>
    </div>
    <ng-container *ngIf="data && !isLoading">
        <div class="chart" [ngStyle]="{ height: calculatedWidgetHeight }">
            <dx-vector-map id="vector-map" [bounds]="[-180, 85, 180, -60]" [size]="{ height: calculatedChartWidgetHeight }" (onDrawn)="onMapDrawn($event)">
                <dxo-tooltip [enabled]="true" contentTemplate="tooltipContent"></dxo-tooltip>
                <dxo-common-settings
                borderWidth="0"
                ></dxo-common-settings>
                <dxi-layer
                [customize]="customizeLayers"
                [dataSource]="worldMap"
                name="areas"
                >
                    <dxo-label [enabled]="true" dataField="name"></dxo-label>
                </dxi-layer>
                <dxi-legend [customizeText]="customizeText">
                    <dxo-source layer="areas" grouping="color"></dxo-source>
                </dxi-legend>
                <div *dxTemplate="let info of 'tooltipContent'" class="state-tooltip">
                    <div>
                        <div>{{ data[info.attribute("name")]?.region }}</div>
                        <div>{{ info.attribute("total") }}</div>
                    </div>
                </div>
            </dx-vector-map> 

        </div>
    </ng-container>
    <ng-container *ngIf="!data && !isLoading">
        <div class="chart" [ngStyle]="{ height: calculatedWidgetHeight }">
          <span class="empty-data">No Data Found!</span>
        </div>
    </ng-container>
</div>