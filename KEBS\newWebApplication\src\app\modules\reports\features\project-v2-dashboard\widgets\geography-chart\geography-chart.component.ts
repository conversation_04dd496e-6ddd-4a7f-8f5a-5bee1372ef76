import { Component, Input, OnInit, ViewContainerRef, TemplateRef, ViewChild } from '@angular/core';

import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { DecimalPipe } from '@angular/common';
import { Overlay, OverlayConfig, OverlayRef } from "@angular/cdk/overlay";
import { TemplatePortal } from "@angular/cdk/portal";
import { DxVectorMapModule, DxPieChartModule } from 'devextreme-angular';
import * as mapsData from '../../../../../../../../node_modules/devextreme/dist/js/vectormap-data/world.js'
import { WidgetsService } from './../../services/widgets/widgets.service';
import { ToasterService } from 'src/app/modules/applicant-tracking-system/shared-components/ats-custom-toast/toaster.service';

@Component({
  selector: 'app-geography-chart',
  templateUrl: './geography-chart.component.html',
  styleUrls: ['./geography-chart.component.scss'],
  providers: [DecimalPipe]
})
export class GeographyChartComponent implements OnInit {

  @Input() aid: any = null;
  @Input() oid: any = null;
  @Input() widgetConfig: any = {};
  @Input() widgetType: string = '';
  @Input() startDate: any = null;
  @Input() endDate: any = null;
  @Input() filterData: any = null;
  @Input() filterQuery: any = '';
  protected _onDestroy = new Subject<void>();

  calculatedWidgetHeight: string = '';
  calculatedChartWidgetHeight: number = null;
  isLoading: boolean = true;
  data = [];
  worldMap = mapsData.world;


  constructor(
    private _widgetService: WidgetsService,
    private _toaster: ToasterService,
    private _decimalPipe: DecimalPipe,
    private overlay: Overlay,
    private viewContainerRef: ViewContainerRef,
  ) { }

  async ngOnInit() {
    this.calculateHeight();
    await this.getChartData();
    console.log("worldMap", this.worldMap);
    this.isLoading = false;
  }

  /**
* @description To calculate height dynamically
*/
  calculateHeight() {
    if (this.widgetConfig && this.widgetConfig?.height) {
      this.calculatedWidgetHeight = `calc(${this.widgetConfig.height} - 75px)`;
      this.calculatedChartWidgetHeight =
        parseInt(this.widgetConfig.height) - (this.widgetConfig.reduce_chart_height ? this.widgetConfig.reduce_chart_height : 150);
      console.log("calculatedChartWidgetHeight", this.calculatedChartWidgetHeight)
    }
  }

  /**
  * @description Get list view menu data
  */
  async getChartData() {
    if (
      !this.widgetConfig?.widget_config?.api ||
      this.widgetConfig?.widget_config?.api == ''
    ) {
      return;
    }

    let apiUrl = this.widgetConfig?.widget_config?.api;
    let payload = {
      aid: this.aid,
      oid: this.oid,
      startDate: this.startDate,
      endDate: this.endDate,
      inlineFilterData: this.widgetConfig?.widget_config?.inline_filter ? this.widgetConfig?.widget_config?.inline_filter : [],
      filterData: this.filterData,
      filterQuery: this.filterQuery
    };

    this.data = [];
    this.isLoading = true;

    return new Promise((resolve, reject) => {
      this._widgetService
        .getDataDynamically(apiUrl, payload)
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res) => {
            if (res['err'] == false) {
              this.data =
                res['data'] ? res['data'] : {};
            } else {
              this._toaster.showError('Error', res['msg'], 7000);
            }
            this.isLoading = false;
            resolve(true);
          },
          error: (err) => {
            this._toaster.showError(
              'Error',
              'Error in Fetching Widget Data',
              7000
            );
            this.isLoading = false;
            reject();
          },
        });
    });
  }

  customizeLayers = (elements: Record<string, Function>[]) => {
    elements.forEach((element) => {
      const Data = this.data[element.attribute('name')];
      element.attribute('total', Data && Data.total || 0);
      const value = element.attribute('total');
      if (value === 0 || value == null) {
        element.applySettings({ color: '#DADCE2' });
      }else{
        element.applySettings({ color: Data?.color ? Data?.color : '#EE4961' });
      }
    });
  };

  onMapDrawn(e: any) {
    const background = document.querySelector('#vector-map .dxm-background');
    if (background) {
      (background as SVGRectElement).setAttribute('stroke', 'none');
      (background as SVGRectElement).setAttribute('fill', 'transparent');
    }
  }

  customizeText = (info: any) => {
    const countryName = info.point?.name || "Unknown";
    const value = info.point?.total || 0;
    const region = info.point?.region || null;
    return `${region}: $${value.toLocaleString()}`;
  };
  
}
