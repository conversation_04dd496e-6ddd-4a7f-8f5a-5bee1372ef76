<div class="widget-container">
    <div class="header">
      <div class="d-flex align-items-center">
        <div class="chart-title">{{ widgetConfig?.label }}</div>
      </div>
    </div>
    <ng-container *ngIf="data?.total !== undefined && !isLoading">
      <div class="chart" [ngStyle]="{ height: calculatedWidgetHeight + 'px' }">
        <div>
            <div class="total-area">
                <div class="total-header">Total:</div>
                <div class="total-label">{{data?.total}}</div>
            </div>
            <div class="legends-area">
                <ng-container *ngFor="let item of data?.data">
                    <div class="legends">
                        <svg width="6" height="6" viewBox="0 0 6 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="3" cy="3" r="3"  [attr.fill]="item['color']"/>
                        </svg>                        
                        <div class="legend-label">{{item.label}}</div>
                    </div>
                </ng-container>
              </div>
        </div>      
        <div class="bars-container">
          <div class="bar" 
               *ngFor="let item of data?.data; let i = index" 
               [ngStyle]="{
                 height: (item.value / maxValue) * 100 + '%',
                 backgroundColor: item['color']
               }">
            <div class="value">{{ item.value }}</div>
          </div>
        </div>
      </div>
    </ng-container>
    <ng-container *ngIf="!data?.total && !isLoading">
      <div class="chart" [ngStyle]="{ height: calculatedWidgetHeight + 'px' }">
        <span class="empty-data">No Data Found!</span>
      </div>
    </ng-container>
  </div>
  