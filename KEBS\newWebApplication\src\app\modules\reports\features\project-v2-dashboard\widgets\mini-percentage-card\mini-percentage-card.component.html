<div class="widget-container">
    <div class="header">
      <div class="d-flex align-items-center">
        <div class="chart-title">{{ widgetConfig?.label }}</div>
      </div>
    </div>
    <ng-container *ngIf="data?.percentage !== undefined && !isLoading">
      <div class="chart" [ngStyle]="{ height: calculatedWidgetHeight + 'px' }">
        <div
          class="progress-circle-container"
          [ngStyle]="{
            width: spinnerSize + 'px',
            height: spinnerSize + 'px'
          }"
        >
          <svg
            [attr.width]="spinnerSize"
            [attr.height]="spinnerSize"
            [attr.viewBox]="'0 0 ' + spinnerSize + ' ' + spinnerSize"
          >
            <!-- Background circle -->
            <circle
              [attr.cx]="spinnerSize / 2"
              [attr.cy]="spinnerSize / 2"
              [attr.r]="radius"
              stroke="#e6e6e6"
              stroke-width="7"
              fill="none"
            ></circle>
  
            <!-- Foreground circle (progress) -->
            <circle
              [attr.cx]="spinnerSize / 2"
              [attr.cy]="spinnerSize / 2"
              [attr.r]="radius"
              [style.stroke]="data?.color"
              stroke-width="7"
              fill="none"
              [attr.stroke-dasharray]="circumference"
              [attr.stroke-dashoffset]="strokeDashoffset"
              stroke-linecap="round"
              [attr.transform]="'rotate(-90 ' + spinnerSize / 2 + ' ' + spinnerSize / 2 + ')'"
            ></circle>
          </svg>
          <div class="percentage-label">{{ data?.percentage }}%</div>
        </div>
        <div class="total-area">
            <div class="total-header">Total</div>
            <div class="total-label">{{data?.total}}</div>
        </div>
      </div>
    </ng-container>
    <ng-container *ngIf="!data?.percentage && !isLoading">
      <div class="chart" [ngStyle]="{ height: calculatedWidgetHeight + 'px' }">
        <span class="empty-data">No Data Found!</span>
      </div>
    </ng-container>
  </div>
  