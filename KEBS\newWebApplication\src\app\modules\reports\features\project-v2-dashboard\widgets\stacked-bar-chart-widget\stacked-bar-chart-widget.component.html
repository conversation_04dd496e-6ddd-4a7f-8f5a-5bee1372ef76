<div class="widget-container">
    <div class="header">
            <div>
                <div class="chart-title">{{ widgetConfig?.label }}</div>
                <div class="chart-title" *ngIf="this.widgetConfig?.widget_config?.show_header_total">{{countLabel}}</div>
            </div>
            <div class="inline-filter">
                <ng-container *ngFor="let filter of widgetConfig?.widget_config?.inline_filter">
                    <div class="selected-filter" (click)="openFilterOverlay($event,filter)" cdkOverlayOrigin
                    #triggerInlineFilter="cdkOverlayOrigin" #triggerInlineFilter>
                        {{ getDefaultFilterLabel(filter) }}
                        <div class="pl-1">
                            <svg width="8" height="6" viewBox="0 0 8 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M3.91375 5.02644L0.144531 1.25724L0.847081 0.554688L3.91375 3.62135L6.98041 0.554688L7.68296 1.25724L3.91375 5.02644Z" fill="#1C1B1F"/>
                            </svg>                                
                        </div>
                    </div>
                </ng-container> 
            </div>
    </div>
    <div class="custom-legend" *ngIf="data.length > 0 && !isLoading">
        <ng-container *ngFor="let series of seriesData">
            <div class="d-flex legend">
                <div class="pr-1">
                    <svg width="8" height="9" viewBox="0 0 8 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="4" cy="4.5" r="4" [attr.fill]="series['color']"></circle>
                    </svg>
                </div>
                <div class="label">
                    {{series.label}}
                </div>
            </div>
        </ng-container>
    </div>
    <ng-container *ngIf="data.length > 0 && !isLoading">
        <div class="chart" [ngStyle]="{ height: calculatedWidgetHeight }">
            <div class="d-flex chart-header" *ngIf="widgetConfig?.widget_config?.custom_chart_header">
                <div class="argument-label">{{this.widgetConfig?.widget_config?.argument_label}}</div>
                <div class="value-label">{{this.widgetConfig?.widget_config?.value_label}}</div>
            </div>
            <dx-chart id="chartContainer" 
                [dataSource]="data" style="width: 100%"
                [rotated]="this.widgetConfig?.widget_config?.is_roated" 
                [commonSeriesSettings]="{
                argumentField: widgetConfig?.widget_config?.argument_field,
                type: 'stackedBar'
                }"
                [barGroupWidth]="widgetConfig?.widget_config?.bar_group_width || 24"
            >

            <dxo-common-axis-settings>
                <dxo-label>
                  <dxo-font
                    [color]="'#45546E'"     
                    [size]="12"           
                    [weight]="400" >
                  </dxo-font>
                </dxo-label>
            </dxo-common-axis-settings>


                <dxo-size [height]="calculatedChartWidgetHeight"> </dxo-size>
                <!-- Enable zooming and scrolling -->
                <dxo-zoom-and-pan argumentAxis="pan" valueAxis="none"></dxo-zoom-and-pan>
                <dxo-scroll-bar [visible]="widgetConfig?.widget_config?.scroll_bar_enabled"></dxo-scroll-bar>

                <!-- Configure visual range -->
                <dxo-argument-axis [(visualRange)]="chartVisualRange">
                    <dxo-label overlappingBehavior="stagger" rotationAngle="-45"></dxo-label>
                    <dxo-title *ngIf="!widgetConfig?.widget_config?.custom_chart_header" [text]="widgetConfig?.widget_config?.argument_label"
                    [font]="{ color: '#B9C0CA', size: 12, weight: 400 }">
                    </dxo-title>
                </dxo-argument-axis>

                <dxo-value-axis>
                    <dxo-label overlappingBehavior="stagger"></dxo-label>
                </dxo-value-axis>

                <!-- Add optional value axis configuration -->
                <dxi-value-axis *ngIf="widgetConfig?.widget_config?.value_axis_position"
                    [position]="widgetConfig?.widget_config?.value_axis_position">
                    <dxo-title
                    *ngIf="!widgetConfig?.widget_config?.custom_chart_header"
                    [text]="widgetConfig?.widget_config?.value_label"
                    [font]="{ color: '#B9C0CA', size: 12, weight: 400 }">
                    </dxo-title>
                </dxi-value-axis>

                <!-- Add optional argument axis configuration -->
                <dxi-argument-axis *ngIf="widgetConfig?.widget_config?.argument_axis_position"
                    [position]="widgetConfig?.widget_config?.argument_axis_position">
                </dxi-argument-axis>

                <!-- Add series with custom point styles -->
                <dxi-series *ngFor="let series of seriesData" [valueField]="series.key" [name]="series.label"
                    [color]="series.color">
                </dxi-series>

                <dxo-legend [visible]="widgetConfig?.widget_config?.is_legend_enabled" [horizontalAlignment]="
                  widgetConfig?.widget_config?.legend_horizontal_alignment
                " [verticalAlignment]="
                  widgetConfig?.widget_config?.legend_vertical_alignment
                " [customizeText]="customizeText" [rowItemSpacing]="16"></dxo-legend>

            </dx-chart>

        </div>
    </ng-container>
    <ng-container *ngIf="data.length == 0 && !isLoading">
        <div class="chart" [ngStyle]="{ height: calculatedWidgetHeight }">
            <span class="empty-data">No Data Found!</span>
        </div>
    </ng-container>
</div>
<ng-template #triggerInlineFilterTemplateRef let-filter>
    <div class="dropdown" *ngIf="filter.filter_data.length > 0">
        <ng-container *ngFor="let item of filter.filter_data">
            <div class="dropdown-item" [matTooltip]="item.label" (click)="updateFilterData(item.id, filter)">{{item.label}}</div>
        </ng-container>
    </div>
</ng-template>