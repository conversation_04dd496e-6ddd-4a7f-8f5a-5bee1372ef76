import { Component, OnInit, ViewChild, ElementRef, HostListener, TemplateRef } from '@angular/core';
import { MonthyQuoteProjectionService } from '../../services/monthy-quote-projection.service';
import { FormBuilder, FormControl, Validators } from '@angular/forms';
import { ToasterService } from 'src/app/services/toaster-service/toaster.service';
import { DxPivotGridComponent } from 'devextreme-angular';
import { Router } from '@angular/router';
import { NgxSpinnerService } from 'ngx-spinner';
import sweetAlert from 'sweetalert2';
import moment from 'moment';
import CustomStore from 'devextreme/data/custom_store';
import PivotGridDataSource from 'devextreme/ui/pivot_grid/data_source';
import { OpportunityStakeholdersService } from 'src/app/modules/opportunities/features/opportunities-detail/pages/internal-stakeholder/services/opportunity-stakeholders.service';
import { LoginService } from 'src/app/services/login/login.service';
import { <PERSON><PERSON>ialog, MatDialogRef } from '@angular/material/dialog';

@Component({
  selector: 'app-quote-monthly-pivot',
  templateUrl: './quote-monthly-pivot.component.html',
  styleUrls: ['./quote-monthly-pivot.component.scss'],
  providers: [],
})
export class QuoteMonthlyPivotComponent implements OnInit {

  @ViewChild(DxPivotGridComponent) pivotGrid: DxPivotGridComponent;
  @ViewChild('cardScroll', { read: ElementRef })
  public cardScroll: ElementRef<any>;

  showDataFields: boolean = true;
  showRowFields: boolean = true;
  showColumnFields: boolean = true;
  showFilterFields: boolean = false;
  searchEnabled: boolean = true;
  loading: boolean = true;
  displayView: boolean = true;
  hasAccess: boolean = true;
  isDuplicate: boolean = false;
  isSidenavOpened: boolean;
  isVariantSidenavOpened: boolean;
  downloadFileName: string = 'Quote Monthly Projection Report';
  empDetailsForm = this.fb.group({
    employeeControl: ['', Validators.required],
  })
  cannotSave: boolean;
  versionName = new FormControl('', [Validators.required]);

  empListPopUpRef: MatDialogRef<any>;
  @ViewChild('empListPopUp')
  private empListPopUpTemplate: TemplateRef<any>;


  storageKey: string = 'dx-widget-gallery-pivotgrid-storing-quote-monthly-report';
  localState: string;

  daterange: { startDate: moment.Moment; endDate: moment.Moment; };
  date: { startDate: string; endDate: string };
  applied_duration: { startDate: string; endDate: string };

  applicationId: number = 2501;

  mainConfig: any;
  dataSource: any;
  views: any;
  reports: any;
  searchValue: any = '';
  report_name: any;
  data: any;
  selectedReportId: any;
  report_id: any;
  selectedDate: any;
  pivotGrid1: any;
  currentView: any;
  globalCurrentView: any;
  variant_length: any;
  globalVariants: any;
  globalVariantEnabled: any;

  fields = [];
  store = [];
  dateRangePickerRanges = {
    'This Month': [
      moment().startOf('month').format('YYYY-MM-DD'),
      moment().endOf('month').format('YYYY-MM-DD'),
    ],
    'Last Month': [
      moment().subtract(1, 'month').startOf('month').format('YYYY-MM-DD'),
      moment().subtract(1, 'month').endOf('month').format('YYYY-MM-DD'),
    ],
    'Next Month': [
      moment().add(1, 'month').startOf('month').format('YYYY-MM-DD'),
      moment().add(1, 'month').endOf('month').format('YYYY-MM-DD'),
    ],
    'Upcoming 3 Months': [
      moment().startOf('month').format('YYYY-MM-DD'),
      moment().add(2, 'month').endOf('month').format('YYYY-MM-DD'),
    ],
    'Previous 3 Months': [
      moment().subtract(3, 'month').startOf('month').format('YYYY-MM-DD'),
      moment().subtract(1, 'month').endOf('month').format('YYYY-MM-DD'),
    ],
    'This Year': [
      moment().startOf('year'),
      moment().endOf('year'),
    ],
    'Previous Year': [
      moment().subtract(1, 'year').startOf('year').format('YYYY-MM-DD'),
      moment().subtract(1, 'year').endOf('year').format('YYYY-MM-DD'),
    ],
    'Current Financial Year': [
      moment().month() < 3
        ? moment()
          .subtract(1, 'year')
          .startOf('year')
          .month(3)
          .format('YYYY-MM-DD')
        : moment().startOf('year').month(3).format('YYYY-MM-DD'),
      moment().month() < 3
        ? moment().endOf('year').month(2).format('YYYY-MM-DD')
        : moment().add(1, 'year').endOf('year').month(2).format('YYYY-MM-DD'),
    ],
  };
  isReportADefaultReport: boolean = false;
  variant_name: boolean;  
  main_report_name: any;

  employeeControl = new FormControl(null);
  employees:any;
  isFirstDateInputCall: boolean = true;
  private isFirstLoad = true;
  constructor(
    private _qbService: MonthyQuoteProjectionService,
    private _toaster: ToasterService,
    private _router: Router,
    private _spinner: NgxSpinnerService,
    private _stakeholderService: OpportunityStakeholdersService,
    private _loginService: LoginService,
    private _dialog: MatDialog,
    private fb: FormBuilder,
    
  ) { }

  async ngOnInit() {
    // await this.getQuoteProjectionReportConfig();
    // this.hasAccess = await this._qbService.hasCUAccess()
    let domain = window.location.hostname;
    console.log(window.location.hostname)
    this.selectReport(null, 0);
    const generalConfig = await this._stakeholderService.getTheme();
       document.documentElement.style.setProperty('--intButton', generalConfig['tabColor']);

    this.daterange = { startDate: moment().startOf('year'), endDate: moment().endOf('year') };
    this.calculateDynamicStyle();
  }


  async onDateInputChange(event?: { startDate?: moment.Moment; endDate?: moment.Moment }) {
    if (this.isFirstLoad) {
      this.isFirstLoad = false;
      return;
    }
  
    if (event?.startDate && event?.endDate) {
      this.daterange = { startDate: event.startDate, endDate: event.endDate };
    } else {
      this.daterange = { 
        startDate: moment().startOf('year'), 
        endDate: moment().endOf('year') 
      };
    }
  
    this.selectedDate = {
      startDate: this.daterange.startDate.format('YYYY-MM-DD'),
      endDate: this.daterange.endDate.format('YYYY-MM-DD'),
    };
  
    if (this.selectedDate.startDate && this.selectedDate.endDate) {
      this._spinner.show();
  
      const configType = this.isFirstDateInputCall ? 'INIT' : '';
      await this.getConfigData(configType);
  
      this.isFirstDateInputCall = false;
  
      await this.getQuoteDetailsForReport();
      this._spinner.hide();
    }
  }

  async selectReport(id?: number, fromUI?: any) {
    if (fromUI == 1) {
      this.report_id = id;
      this.selectedReportId = id;
      // for (const r of this.reports) {
      //   if (r.id == id) {
      //     this.data = r;
      //     break;
      //   }
      // }
      this.report_name = this.data?.name;
      // this.applicationId = this.data?.application_id;
      // this.storageKey = this.data.storageKey;
      localStorage.setItem('selectedReportId', this.selectedReportId);
      await this.setDuration();
      this.getQuoteDetailsForReport();
    } else {
      this.report_id = localStorage.getItem('selectedReportId')
        ? localStorage.getItem('selectedReportId')
        : 1;
      this.selectedReportId = localStorage.getItem('selectedReportId')
        ? localStorage.getItem('selectedReportId')
        : 1;

      if (this.reports) {
        for (const r of this.reports) {
          if (r.id == this.report_id) {
            this.data = r;
            break;
          }
        }
      }

      this.report_name = this.data?.name;
      // this.applicationId = this.data?.application_id;
      // this.storageKey = this.data.storageKey;
      await this.setDuration();
      // this.getQuoteDetailsForReport();
    }
  }

  /**
   * @description For Setting the Default Date Range from the Config
   */
  setDuration() {
    // console.log('inside set duration');
    // Step 1: Retrieve the stored data for all reports from localStorage
    let storedData =
      JSON.parse(localStorage.getItem('revenue_forecast_date_details')) || {};

    // Step 2: Check if the specific report_id exists in the stored data
    if (storedData[this.report_id]) {
      // Step 3: Get start and end dates for the specific report
      let storedStartDate =
        storedData[this.report_id].startDate;
      let storedEndDate = storedData[this.report_id].endDate;

      // Step 4: Apply the retrieved dates if they exist
      if (storedStartDate && storedEndDate) {
        this.applied_duration = {
          startDate: storedStartDate,
          endDate: storedEndDate,
        };
      }
    } else {
      const startDate = moment().startOf('year').format('YYYY-MM-DD');
      const endDate = moment().endOf('year').format('YYYY-MM-DD');

      this.applied_duration = {
        startDate: startDate,
        endDate: endDate,
      };
    }
  }

  showDataFieldsFn() {
    this.showDataFields = !this.showDataFields;
    if (this.showDataFields == true) {
      this._toaster.showInfo('Displaying Data Fields!', 'Dismiss', 1000);
    } else {
      this._toaster.showInfo('Data Fields Hidden!', 'Dismiss', 1000);
    }
  }

  async getQuoteDetailsForReport(): Promise<void> {
    this.localState = localStorage.getItem(this.storageKey);

    return new Promise((resolve, reject) => {
      this.loading = true;
      this._qbService.getQuoteDetailsForReport(this.selectedDate).subscribe({
        next: (data) => {
          this.loading = false;
          this.dataSource = new PivotGridDataSource({
            fields: this.fields,
            store: new CustomStore({
              load: function () {
                return data['data'];
              },
            }),
          });
          resolve();
        },
        error: (error) => {
          console.error('Error fetching quote details:', error);
          this.loading = false;
          reject(error);
        },
      });
    });
  }

  async getQuoteProjectionReportConfig(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.loading = true;
      this._qbService.getQuoteProjectionReportConfig().subscribe({
        next: (data) => {
          let fields = data?.data;
          this.globalVariants = data?.globalVariants || [];
          this.globalVariantEnabled = data?.globalVariantEnabled;
          this.main_report_name = data?.reportName;
          this.mainConfig = data;
          this.hasAccess = data.hasCUAccess;
          this.fields = [...fields]
          resolve();
        },
        error: (error) => {
          console.error('Error fetching quote details:', error);
          reject(error);
        },
      });
    });
  }

  onInitialized(e) {
    this.pivotGrid1 = e.component;
  }

  async refresh() {
    this._spinner.show();
    await this.getQuoteDetailsForReport()
    this.pivotGrid1.getDataSource().reload();
    this._spinner.hide();

  }

  reset() {
    this.pivotGrid1.getDataSource().state({});
  }

  setFieldChoosers(fieldVisibiltyData) {
    // console.log(fieldVisibiltyData);
    this.showDataFields = fieldVisibiltyData.showDataFields;
    this.showRowFields = fieldVisibiltyData.showRowFields;
    this.showColumnFields = fieldVisibiltyData.showColumnFields;
    this.showFilterFields = fieldVisibiltyData.showFilterFields;
  }

  resetFieldChoosers() {
    this.showDataFields = false;
    this.showRowFields = false;
    this.showColumnFields = false;
    this.showFilterFields = false;
  }

  async getConfigData(type?) {
    await this.getQuoteProjectionReportConfig();
    await this._qbService.getReportUserViews(this.applicationId).subscribe(res => {
      this.views = res;
      this.views.allViews.push(...this.globalVariants)
      const index = this.views.allViews.findIndex(item => item.is_default_selected == true);

      this.variant_length = this.views.allViews.length;
      for (let i = 0; i < this.views.allViews.length; i++) {
        this.views.allViews[i].visibleDeleteView = false;
      }

      if (type === 'G_S') {
        const latestView = this.views.allViews.reduce((latest, item) => {
          return new Date(item?.created_on) > new Date(latest?.created_on) ? item : latest;
        }, this.views.allViews[0] || null);

        this.views.activeView = latestView ? [latestView] : this.views.activeView;
      }

      if (type === 'G_U_S') {
        const latestView = this.views.allViews.reduce((latest, item) => {
          if (item?.updated_on) {
            if (!latest || new Date(item.updated_on) > new Date(latest.updated_on)) {
              return item;
            }
          }
          return latest;
        }, null);

        this.views.activeView = latestView ? [latestView] : this.views.activeView;
      }


      const Gindex = this.views.allViews.findIndex(item => item?._id == this.views.activeView[0]?._id);

      if (this.views.activeView.length > 0 && this.views) {
        this.enableDisplayView();
        this.currentView = 0;
        this.globalCurrentView = 0;
        localStorage.setItem(this.storageKey, this.views.activeView[0].saved_config);
        this.pivotGrid1.getDataSource().state(JSON.parse(this.views.activeView[0].saved_config))
        if (this.views.activeView[0]?.field_config?.length > 0)
          this.setFieldChoosers(JSON.parse(this.views.activeView[0]?.field_config)[0])
        else {
          let field_conf = {
            showDataFields: true,
            showRowFields: true,
            showColumnFields: true,
            showFilterFields: true
          };
          this.setFieldChoosers(field_conf)
        }
      }
      else if (this.views.activeView.length == 0 && this.views.allViews.length && this.views) {
        this.enableDisplayView();
        this.currentView = 0;
        this.globalCurrentView = 0;
        localStorage.setItem(this.storageKey, this.views.allViews[0].saved_config);
        this.pivotGrid1.getDataSource().state(JSON.parse(this.views.allViews[0].saved_config))
        if (this.views.activeView[0]?.field_config.length > 0)
          this.setFieldChoosers(JSON.parse(this.views.allViews[0]?.field_config)[0])
        else {
          let field_conf = {

            showDataFields: true,
            showRowFields: true,
            showColumnFields: true,
            showFilterFields: true
          };
          this.setFieldChoosers(field_conf)
        }
      }
      else {
        localStorage.setItem(this.storageKey, this.localState);
        this.pivotGrid1.getDataSource().state(this.localState)
      }

      if (index !== -1 && (type == 'INIT' || type == 'DEL')) {
        const report = this.views.allViews[index];
        this.changeView(index, report);
      } else if (Gindex !== -1 && type == 'G_S') {
        const report = this.views.allViews[Gindex];
        this.changeView(Gindex, report);
      } else if (Gindex !== -1 && type == 'G_U_S') {
        const report = this.views.allViews[Gindex];
        this.changeView(Gindex, report);
      }

    }, err => {

      this.enableDisplayView();
      this._toaster.showError("Error Retrieving Report Views! Try Refreshing", "Dismiss", { duration: 2000 });
    });
  }


  stateUpdate = async () => {
    // let temp = localStorage.getItem(this.storageKey);
    // if (typeof temp == 'string') temp = JSON.parse(temp);
    let field_conf = [
      {
        showDataFields: this.showDataFields,
        showRowFields: this.showRowFields,
        showColumnFields: this.showColumnFields,
        showFilterFields: this.showFilterFields,
      },
    ];
    // console.log(temp);
    let temp = localStorage.getItem(this.storageKey);
    if (typeof temp == 'string') temp = JSON.parse(temp);
    if (temp && this.views && this.views.allViews.length > 0) {
      if (this.views.allViews[this.currentView]?.reportKeyName == 'global_variant') {
        let globalConfig = {
          "_id": this.views.allViews[this.currentView]?._id,
          "reportName": this.views.allViews[this.currentView]?.reportName,
          "reportKeyName": "global_variant",
          "customization_id": this.views.allViews[this.currentView]?.customization_id,
          "application_id": this.applicationId,
          "user_oid": this._loginService.getProfile().profile.oid || '-',
          "saved_config": JSON.stringify(temp),
          "active_config": 1,
          "creator_name": this.views.allViews[this.currentView]?.creator_name,
          "created_on": this.views.allViews[this.currentView]?.created_on,
          "updated_on": new Date().toISOString(),
          "updated_by": { user_name: this._loginService.getProfile().profile.name || '-', user_oid: this._loginService.getProfile().profile.oid },
          "field_config": "[{\"showRowFields\": true, \"showDataFields\": true, \"showColumnFields\": true, \"showFilterFields\": false}]",
          "is_active": 1
        }
        // console.log(globalConfig, 'globalConfig Save');
        this._qbService
          .updateGlobalReportState(globalConfig)
          .subscribe(
            async (res) => {
              // console.log(res);

              this.enableDisplayView();
              this._toaster.showInfo(
                this.mainConfig.globalVariantEnabledLabel + ' - ' + this.views.allViews[this.currentView].reportName +
                ' was updated Successfully!',
                '',
                2500
              );
              await this.getConfigData('G_U_S');
            },
            (err) => {
              this._toaster.showWarning(
                'Unable to Update the Current Report Version! Try Again',
                ''
              );
              // console.log(err);
            }
          );
      }
      else {
        this._qbService
          .updateReportState(
            temp,
            this.views.allViews[this.currentView].customization_id,
            field_conf,
            this.applicationId
          )
          .subscribe(
            (res) => {
              // console.log(res);

              this.enableDisplayView();
              this._toaster.showInfo(
                'Report Version - ' +
                this.views.allViews[this.currentView].config_name +
                ' was updated Successfully!',
                '',
                2500
              );
              this.getConfigData();
            },
            (err) => {
              this._toaster.showWarning(
                'Unable to Update the Current Report Version! Try Again',
                ''
              );
              // console.log(err);
            }
          );
      }
    } else {
      this._toaster.showError(
        'No Report Versions Found. Kindly Use SaveAs!',
        '',
        2500
      );
    }
  };

  saveState(globalSave?) {
    let isDuplicate = this.views.allViews.some(view => view.config_name == this.versionName.value);
    if (isDuplicate)
      return this._toaster.showWarning("Cannot Save with same variant name", "");
    let temp = localStorage.getItem(this.storageKey);
    if (typeof temp == 'string') temp = JSON.parse(temp);
    let field_conf = [
      {
        showDataFields: this.showDataFields,
        showRowFields: this.showRowFields,
        showColumnFields: this.showColumnFields,
        showFilterFields: this.showFilterFields,
      },
    ];
    // console.log(this.applicationId);

    if (globalSave) {
      let globalConfig = {
        "reportName": this.versionName.value,
        "reportKeyName": "global_variant",
        "customization_id": null,
        "application_id": this.applicationId,
        "user_oid": this._loginService.getProfile().profile.oid || '-',
        "saved_config": JSON.stringify(temp),
        "active_config": 1,
        "creator_name": this._loginService.getProfile().profile.name || '-',
        "created_on": new Date().toISOString(),
        "field_config": "[{\"showRowFields\": true, \"showDataFields\": true, \"showColumnFields\": true, \"showFilterFields\": false}]",
        "is_active": 1
      }

      console.log(globalConfig, 'globalConfig');

      this._qbService
        .saveGlobalReportState(globalConfig)
        .subscribe(
          async (res) => {
            // console.log(res);
            // let prevIndLen = this.views.allViews.length

            this.enableDisplayView();
            this._toaster.showSuccess(
              this.mainConfig.globalVariantEnabledLabel + ' - ' +
              this.versionName.value +
              ' was created Successfully!',
              '',
              2500
            );
            await this.getConfigData('G_S');
            if (globalSave) {
              this.variant_name = this.versionName.value;
              this.isReportADefaultReport = true;
              // this.currentView = prevIndLen + 1
            } else {
              this.variant_name = this.versionName.value;
              this.isReportADefaultReport = false;
              // this.currentView = prevIndLen + 1
            }
            this.versionName.reset();
          },
          (err) => {
            this._toaster.showWarning(
              'Unable to create the Report Version! Try Again',
              ''
            );
            // console.log(err);
          }
        );
    }
    else {
      this._qbService
        .saveReportState(
          temp,
          this.versionName.value,
          field_conf,
          this.applicationId
        )
        .subscribe(
          (res) => {
            // console.log(res);

            this.enableDisplayView();
            this._toaster.showSuccess(
              'Report Version - ' +
              this.versionName.value +
              ' was created Successfully!',
              '',
              2500
            );
            this.getConfigData();
            if (globalSave) {
              this.variant_name = this.versionName.value;
              this.isReportADefaultReport = true;
            } else {
              this.variant_name = this.versionName.value;
              this.isReportADefaultReport = false;
            }
          },
          (err) => {
            this._toaster.showWarning(
              'Unable to create the Report Version! Try Again',
              ''
            );
            // console.log(err);
          }
        );
    }
  }

  changeView(index, report?) {
    this.currentView = index;
    localStorage.setItem(this.storageKey, this.views.allViews[index].saved_config);
    let temp = localStorage.getItem(this.storageKey);
    if (report?.reportKeyName == "global_variant") {
      this.cannotSave = true;
      this.report_name = report?.reportName || 'Quote Monthly Projection';
      this.isReportADefaultReport = true
      this.downloadFileName = `Quote Monthly Projection Report - ${this.report_name}`;

    } else {
      this.isReportADefaultReport = false
      this.variant_name = report?.config_name || ''
      this.cannotSave = false
      this.downloadFileName = `Quote Monthly Projection Report - ${this.variant_name}`;
    }
    this.pivotGrid1.getDataSource().state(JSON.parse(this.views.allViews[index].saved_config));
    this.setFieldChoosers(JSON.parse(this.views.allViews[index]?.field_config)[0])
    this.enableDisplayView();
  }

  deleteVariant(index) {
    let name = this.views.allViews[index].config_name;
    this.confirmSweetAlert('Do you want to Delete ' + name + ' Variant?').then(
      (deleteConfirm) => {
        if (deleteConfirm.value) {
          // console.log(deleteConfirm.value);
          this._qbService
            .deleteVariant(
              this.applicationId,
              this.views.allViews[index].customization_id
            )
            .subscribe(
              async (res) => {
                await this.getConfigData('DEL');
                this._toaster.showSuccess(
                  'Variant ' + name + ' was Deleted Succesfully',
                  '',
                  2500
                );
              },
              (err) => {
                this._toaster.showError(
                  'Failed to delete Variant ' + name + '.',
                  '',
                  2500
                );
              }
            );
        }
      }
    );
  }

  async openEmployeeListPopup(i) {
    let name = this.views.allViews[i].config_name;

    let dialogConfig = {
      width: '35rem',
      // minHeight: '45vh',
      data: {i: i, header: name || 'Share Variant'},
      disableClose: true,
      panelClass: 'custom-mat-dialog-panel',
    };

    this.empListPopUpRef = this._dialog.open(this.empListPopUpTemplate, dialogConfig);

    const result = await this.empListPopUpRef.afterClosed().toPromise();

    if (result)
      this.employeeControl.reset();

  }


  shareVariant(index) {
    let name = this.views.allViews[index].config_name;

    if (!this.employeeControl.value)
      return this._toaster.showWarning('Select an Employee to Share Variant!', '', 2500);

    if (this.employeeControl.value == this._loginService.getProfile().profile.oid)
      return this._toaster.showWarning('Cannot Share Variant to Yourself!', '', 2500);

    let redirectPath = `https://${window.location.hostname}/main/reports/quote-monthly-projection`;

    this._qbService
      .shareVariant(
        this.applicationId,
        this.views.allViews[index],
        this._loginService.getProfile().profile.oid,
        this.employeeControl.value,
        this.mainConfig?.sharingOptionConfig?.shareContent || null,
        window['href'] || redirectPath
      )
      .subscribe(
        async (res) => {
          if (res && res['messType'] == 'S'){
            this._toaster.showSuccess(
              `Variant '${name}' was successfully shared with the selected employee.`,
              '',
              2500
            );
            this.empListPopUpRef.close('SUCCESS');
          }
        },
        (err) => {
          this._toaster.showError(
            'Failed to Share Variant ' + name + '.',
            err,
            2500
          );
        }
      );
  }

  closePopEmpPopUp() {
    this.empListPopUpRef.close('CANCEL');
  }
  
  showRowFieldsFn() {
    this.showRowFields = !this.showRowFields;
    if (this.showRowFields == true) {
      this._toaster.showInfo('Displaying Row Fields!', '', 1000);
    } else {
      this._toaster.showInfo('Row Fields Hidden!', 'Dismiss', 1000);
    }
  }

  showColumnFieldsFn() {
    this.showColumnFields = !this.showColumnFields;
    if (this.showColumnFields == true) {
      this._toaster.showInfo('Displaying Column Fields!', '', 1000);
    } else {
      this._toaster.showInfo('Column Fields Hidden!', 'Dismiss', 1000);
    }
  }

  showFilterFieldsFn() {
    this.showFilterFields = !this.showFilterFields;
    if (this.showFilterFields == true) {
      this._toaster.showInfo('Displaying Filter Fields!', '', 1000);
    } else {
      this._toaster.showInfo('Filter Fields Hidden!', 'Dismiss', 1000);
    }
  }

  toggleEditView() {
    this.displayView = !this.displayView;
  }

  enableDisplayView() {
    this.displayView = true;
  }

  scrollRight() {
    this.cardScroll.nativeElement.scrollTo({
      left: this.cardScroll.nativeElement.scrollLeft + 1060,
      behavior: 'smooth',
    });
  }

  scrollLeft() {
    this.cardScroll.nativeElement.scrollTo({
      left: this.cardScroll.nativeElement.scrollLeft - 1060,
      behavior: 'smooth',
    });
  }

  confirmSweetAlert(title) {
    let template = {
      customClass: {
        title: 'title-class',
        confirmButton: 'confirm-button-class',
        cancelButton: 'confirm-button-class',
      },
      title: title,
      // text: text,
      type: 'warning',
      showConfirmButton: true,
      showCancelButton: true,
    };

    return sweetAlert.fire(template);
  }

  toggleSidenav() {
    this.isSidenavOpened = !this.isSidenavOpened;
  }

  get filteredReports() {
    if (!this.searchValue) {
      return this.reports;
    }
    return this.reports.filter((report) =>
      report.name.toLowerCase().includes(this.searchValue.toLowerCase())
    );
  }

  async getQuoteDefaultConfig() {
    let reportData = await this._qbService.getQuoteDefaultConfig();
    if (reportData['messType'] == 'S') {
      this.reports = reportData['data'];
    }
  }

  toggleVariantSidenav() {
    this.isVariantSidenavOpened = !this.isVariantSidenavOpened;
  }

  backToReports() {
    this._router.navigateByUrl('/main/reports');
  }

  @HostListener('window:resize')
  onResize() {
    this.calculateDynamicStyle();
  }

  calculateDynamicStyle() {
    let dynamicHeight = window.innerHeight - 65 + 'px';
    let dynamicInnerHeight = window.innerHeight - 135 + 'px';
    document.documentElement.style.setProperty(
      '--pageContentHeight',
      dynamicHeight
    );
    document.documentElement.style.setProperty(
      '--innerTabHeight',
      dynamicInnerHeight
    );
  }

}
