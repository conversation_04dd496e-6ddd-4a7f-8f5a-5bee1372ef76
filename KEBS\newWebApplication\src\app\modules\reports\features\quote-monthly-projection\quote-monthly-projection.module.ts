import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';

import { QuoteMonthlyProjectionRoutingModule } from './quote-monthly-projection-routing.module';
import { QuoteMonthlyPivotComponent } from './component/quote-monthly-pivot/quote-monthly-pivot.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatMenuModule } from '@angular/material/menu';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { SatPopoverModule } from "@ncstate/sat-popover";
import { MatTooltipModule } from '@angular/material/tooltip';
import { TooltipModule, TooltipOptions } from 'ng2-tooltip-directive';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { OverlayModule } from '@angular/cdk/overlay';
import {MatSelectModule} from '@angular/material/select';
import {ScrollingModule} from '@angular/cdk/scrolling';

import {
  DxPivotGridModule,
  DxDataGridModule,
  DxButtonModule,
  DxTooltipModule,
} from 'devextreme-angular';

import { NgxSpinnerModule } from 'ngx-spinner';
import { MatSidenavModule } from '@angular/material/sidenav';

export const NgrTooltipOptions: TooltipOptions = {
  'max-width': 300,
  'show-delay': 750
}
//Datetime
import { NgxDaterangepickerMd } from 'ngx-daterangepicker-material';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatDialogModule } from '@angular/material/dialog';
import { SharedComponentsModule } from 'src/app/app-shared/app-shared-components/components.module';


@NgModule({
  declarations: [QuoteMonthlyPivotComponent],
  imports: [
    CommonModule,
    QuoteMonthlyProjectionRoutingModule,
    DxPivotGridModule,
    DxDataGridModule,
    NgxDaterangepickerMd.forRoot(),
    MatNativeDateModule,
    MatDatepickerModule,
    FormsModule,
    ReactiveFormsModule,
    MatMenuModule,
    DxTooltipModule,
    MatIconModule,
    MatMenuModule,
    MatButtonModule,
    DxButtonModule,
    MatProgressSpinnerModule,
    SatPopoverModule,
    MatTooltipModule,
    TooltipModule.forRoot(NgrTooltipOptions as TooltipOptions),
    NgxSpinnerModule,
    MatSidenavModule,
    MatInputModule,
    MatFormFieldModule,
    MatDialogModule,
    OverlayModule,
    DragDropModule,
    MatSelectModule,
    ScrollingModule,
    SharedComponentsModule
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class QuoteMonthlyProjectionModule { }
