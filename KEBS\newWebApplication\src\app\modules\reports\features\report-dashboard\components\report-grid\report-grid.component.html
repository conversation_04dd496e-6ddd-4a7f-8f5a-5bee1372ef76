<div class="grid-component" *ngIf="!isComponentLoading">
  <div [ngClass]="{'header-section': true, 'sticky-detail-header': card}">
    <!------------------------------Link URL Header---------------------------------->
    <div class="link-url-class" *ngIf="card == null">
      <div class="line-item" *ngFor="let link of urlLinkData; let i = index">
        <span
          class="link-text"
          [ngStyle]="{
            color:
              link?.link_url == groupBySelected
                ? 'var(--teamButton)'
                : '#6E7B8F',
            'font-weight': link?.link_url == groupBySelected ? 600 : 400,
            'cursor': link?.disableNavigate ? 'not-allowed' : 'pointer',
            'pointer-events': link?.disableNavigate ? 'none' : 'auto'
          }"
          matToolTip="{{ link?.link_label }}"
          (click)="!link?.disableNavigate && openLinkUrl(link)"
          >{{ link?.link_label }}</span
        >
        <mat-icon class="link-icon" *ngIf="i < urlLinkData.length - 1"
          >keyboard_arrow_right</mat-icon
        >
      </div>
    </div>
    <div *ngIf="card">
      <mat-button-toggle-group [value]="groupBySelected">
        <ng-container *ngFor="let link of urlLinkData;let i = index;">
            <mat-button-toggle [value]="link?.link_url" (change)="!link?.disableNavigate && openLinkUrl(link)"
                class="toggle-btn" [ngClass]="{ 'btn-toggle-selected': groupBySelected == link?.link_url }">
                {{ link?.link_label }}
            </mat-button-toggle>
        </ng-container>
      </mat-button-toggle-group>
    </div>
    <div class="icon-section">
      <!---------- View By------------>
      <div
        class="dropdown-class"
        [matMenuTriggerFor]="groupByPopUp"
        *ngIf="groupTypeSettings.length > 0 && !gridSettings?.disableLevelNavigation"
      >
        <span class="dropdown-icn-header">Level </span>
        <div class="dropdown-text">
          <span>{{
            groupBySelected
              | showMasterData : groupTypeSettings : "link_url" : "link_label"
          }}</span>
          <mat-icon class="dropdown-icon">expand_more</mat-icon>
        </div>

        <mat-menu #groupByPopUp="matMenu">
          <ng-template [matMenuContent]>
            <div
              (click)="$event.stopPropagation()"
              class="view-type-grid-popup"
            >
              <mat-radio-group class="radio-group">
                <mat-radio-button
                  *ngFor="let item of groupTypeSettings; let i = index"
                  [value]="item"
                  [checked]="item.link_url === groupBySelected"
                  (change)="changeGroupBYType(item, $event)"
                  >{{
                    item.link_label ? item.link_label : "-"
                  }}</mat-radio-button
                >
              </mat-radio-group>
            </div>
          </ng-template>
        </mat-menu>
      </div>

      <!---------- View Type------------>
      <div
        class="dropdown-class"
        [matMenuTriggerFor]="viewByPopUp"
        *ngIf="viewTypeSettings.length > 0 && isViewTypeSettingsActive"
      >
        <span class="dropdown-icn-header">Show by </span>
        <div class="dropdown-text">
          <span>{{
            viewTypeSelected | showMasterData : viewTypeSettings : "id" : "name"
          }}</span>
          <mat-icon class="dropdown-icon">expand_more</mat-icon>
        </div>

        <mat-menu #viewByPopUp="matMenu">
          <ng-template [matMenuContent]>
            <div
              (click)="$event.stopPropagation()"
              class="view-type-grid-popup"
            >
              <mat-radio-group class="radio-group">
                <mat-radio-button
                  *ngFor="let item of viewTypeSettings; let i = index"
                  [value]="item"
                  [checked]="item.id === viewTypeSelected"
                  (change)="changeViewType(item, $event)"
                  >{{ item.name ? item.name : "-" }}</mat-radio-button
                >
              </mat-radio-group>
            </div>
          </ng-template>
        </mat-menu>
      </div>

      <!-------------- Filter---------------------->
      <div class="filter-icn" *ngIf="gridSettings?.filter_settings?.filter_enable" (click)="openFilter()">
        <svg
          width="18"
          height="16"
          viewBox="0 0 18 16"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M8.00008 16C7.71675 16 7.47925 15.9042 7.28758 15.7125C7.09591 15.5208 7.00008 15.2833 7.00008 15V9L1.20008 1.6C0.95008 1.26667 0.91258 0.916667 1.08758 0.55C1.26258 0.183333 1.56675 0 2.00008 0H16.0001C16.4334 0 16.7376 0.183333 16.9126 0.55C17.0876 0.916667 17.0501 1.26667 16.8001 1.6L11.0001 9V15C11.0001 15.2833 10.9042 15.5208 10.7126 15.7125C10.5209 15.9042 10.2834 16 10.0001 16H8.00008ZM9.00008 8.3L13.9501 2H4.05008L9.00008 8.3Z"
            fill="#8B95A5"
          />
        </svg>
      </div>

      <!---------- Customize------------>
      <div
        class="customize-class"
        tooltip="Grid Settings"
        [satPopoverAnchor]="showCustomizeCard"
        (click)="showCustomizeCard.toggle()"
        verticalAlign="below"
        horizontalAlign="before"
      >
        <svg
          width="18"
          height="18"
          viewBox="0 0 18 18"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M8.99975 17.75C8.78708 17.75 8.609 17.6781 8.4655 17.5343C8.32183 17.3906 8.25 17.2125 8.25 17V13C8.25 12.7875 8.32192 12.6094 8.46575 12.4658C8.60958 12.3219 8.78775 12.25 9.00025 12.25C9.21292 12.25 9.391 12.3219 9.5345 12.4658C9.67817 12.6094 9.75 12.7875 9.75 13V14.25H17C17.2125 14.25 17.3906 14.3219 17.5343 14.4658C17.6781 14.6096 17.75 14.7878 17.75 15.0003C17.75 15.2129 17.6781 15.391 17.5343 15.5345C17.3906 15.6782 17.2125 15.75 17 15.75H9.75V17C9.75 17.2125 9.67808 17.3906 9.53425 17.5343C9.39042 17.6781 9.21225 17.75 8.99975 17.75ZM1 15.75C0.7875 15.75 0.609417 15.6781 0.46575 15.5343C0.321917 15.3904 0.25 15.2122 0.25 14.9998C0.25 14.7871 0.321917 14.609 0.46575 14.4655C0.609417 14.3218 0.7875 14.25 1 14.25H5C5.2125 14.25 5.39058 14.3219 5.53425 14.4658C5.67808 14.6096 5.75 14.7878 5.75 15.0003C5.75 15.2129 5.67808 15.391 5.53425 15.5345C5.39058 15.6782 5.2125 15.75 5 15.75H1ZM4.99975 11.75C4.78708 11.75 4.609 11.6781 4.4655 11.5343C4.32183 11.3906 4.25 11.2125 4.25 11V9.75H1C0.7875 9.75 0.609417 9.67808 0.46575 9.53425C0.321917 9.39042 0.25 9.21225 0.25 8.99975C0.25 8.78708 0.321917 8.609 0.46575 8.4655C0.609417 8.32183 0.7875 8.25 1 8.25H4.25V7C4.25 6.7875 4.32192 6.60942 4.46575 6.46575C4.60958 6.32192 4.78775 6.25 5.00025 6.25C5.21292 6.25 5.391 6.32192 5.5345 6.46575C5.67817 6.60942 5.75 6.7875 5.75 7V11C5.75 11.2125 5.67808 11.3906 5.53425 11.5343C5.39042 11.6781 5.21225 11.75 4.99975 11.75ZM9 9.75C8.7875 9.75 8.60942 9.67808 8.46575 9.53425C8.32192 9.39042 8.25 9.21225 8.25 8.99975C8.25 8.78708 8.32192 8.609 8.46575 8.4655C8.60942 8.32183 8.7875 8.25 9 8.25H17C17.2125 8.25 17.3906 8.32192 17.5343 8.46575C17.6781 8.60958 17.75 8.78775 17.75 9.00025C17.75 9.21292 17.6781 9.391 17.5343 9.5345C17.3906 9.67817 17.2125 9.75 17 9.75H9ZM12.9998 5.75C12.7871 5.75 12.609 5.67808 12.4655 5.53425C12.3218 5.39058 12.25 5.2125 12.25 5V1C12.25 0.7875 12.3219 0.609417 12.4658 0.46575C12.6096 0.321917 12.7878 0.25 13.0003 0.25C13.2129 0.25 13.391 0.321917 13.5345 0.46575C13.6782 0.609417 13.75 0.7875 13.75 1V2.25H17C17.2125 2.25 17.3906 2.32192 17.5343 2.46575C17.6781 2.60958 17.75 2.78775 17.75 3.00025C17.75 3.21292 17.6781 3.391 17.5343 3.5345C17.3906 3.67817 17.2125 3.75 17 3.75H13.75V5C13.75 5.2125 13.6781 5.39058 13.5343 5.53425C13.3904 5.67808 13.2122 5.75 12.9998 5.75ZM1 3.75C0.7875 3.75 0.609417 3.67808 0.46575 3.53425C0.321917 3.39042 0.25 3.21225 0.25 2.99975C0.25 2.78708 0.321917 2.609 0.46575 2.4655C0.609417 2.32183 0.7875 2.25 1 2.25H9C9.2125 2.25 9.39058 2.32192 9.53425 2.46575C9.67808 2.60958 9.75 2.78775 9.75 3.00025C9.75 3.21292 9.67808 3.391 9.53425 3.5345C9.39058 3.67817 9.2125 3.75 9 3.75H1Z"
            fill="#8B95A5"
          />
        </svg>
      </div>

      <!----------Show Front Icon------------>
      <div
        class="checkbox-class"
        *ngIf="gridSettings['showTotalSettings']['is_active']"
      >
        <mat-checkbox
          width="16px"
          height="16px"
          [(ngModel)]="showTotalFlag"
          style="padding-top: 8px"
        ></mat-checkbox>
        <span class="checkbox-text-class">{{
          gridSettings["showTotalSettings"]["label"]
        }}</span>
      </div>
    </div>
  </div>
  <div *ngIf="card">
    <app-summary-card-detail
      [card]="card"
      [formConfig]="formConfig"
      [detailData]="formattedData"
    ></app-summary-card-detail>
  </div>
  <div class="row body-header-section">
    <span class="body-content-header"
      >{{ getCurrentHeaderValueName() }}
      <span class="count-class">({{ data ? data?.length : 0 }})</span></span
    >
    <div class="col filter-class" *ngIf="gridSettings?.filter_settings?.filter_enable">
      <app-filter-display [applicationId]="gridSettings?.filter_settings?.application_id" [internalApplicationId]="gridSettings?.filter_settings?.internal_application_id"></app-filter-display>
    </div>
  </div>
  <div class="report-grid-class" infinite-scroll [infiniteScrollDistance]="1" [infiniteScrollThrottle]="300" (scrolled)="onScroll()" [scrollWindow]="false">
    <div class="header-row" *ngIf="data?.length > 0">
      <div class="data-grid-header head">
        <span>{{
          groupBySelected
            | showMasterData : groupTypeSettings : "link_url" : "link_label"
        }}</span>
      </div>
      <div class="d-flex" *ngIf="groupType == groupBySelected && groupEnable">
        <div class="data-grid-header head no-sticky" *ngFor="let group of groupList" [ngStyle]="{'display': group.is_active && group.is_visible ? 'flex' : 'none'}">
            <span tooltip="{{group.name ? group.name : '-'}}">{{
              group?.name
            }}</span>
        </div>
      </div>
      <div class="data-split-header total-class" *ngIf="showTotalFlag">
        <div class="sub-header-text">
          <span>Total</span>
        </div>
        <div class="sub-header total-class">
          <div *ngFor="let col of columnList">
            <div
              class="sub-header-split"
              *ngIf="col?.is_active && col?.is_visible"
            >
              <span>{{ col?.column_label }}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="data-split-header" *ngFor="let data of data[0]?.data">
        <div class="sub-header-text">
          <span>{{ data?.title }}</span>
        </div>
        <div class="sub-header">
          <div *ngFor="let col of columnList">
            <div
              class="sub-header-split"
              *ngIf="col?.is_active && col?.is_visible"
            >
              <span>{{ col?.column_label }}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="data-split-header total-class" *ngIf="!showTotalFlag">
        <div class="sub-header-text">
          <span>Total</span>
        </div>
        <div class="sub-header  total-class">
          <div *ngFor="let col of columnList">
            <div
              class="sub-header-split"
              *ngIf="col?.is_active && col?.is_visible"
            >
              <span>{{ col?.column_label }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div *ngFor="let item of dataList">
      <div class="data-row">
        <div class="data-grid-header value-class">
          <span (click)="openNextUrl(groupBySelected, item)" class="name-class" tooltip="{{item.name ? item.name : '-'}}">{{
            item?.name
          }}</span>
        </div>
        <div class="d-flex" *ngIf="groupType == groupBySelected && groupEnable">
          <div class="data-grid-header value-class no-sticky" *ngFor="let group of groupList" [ngStyle]="{'display': group.is_active && group.is_visible ? 'flex' : 'none'}">
            <span class="name-class" tooltip="{{item?.group[group['key']] ? item?.group[group['key']] : '-'}}">{{
              item?.group[group['key']] ? item?.group[group['key']] : '-'
            }}</span>
        </div>
        </div>
        <div class="data-split-header value-class" *ngIf="showTotalFlag">
          <div class="sub-header">
            <div *ngFor="let col of columnList">
              <div class="sub-header-split value-class" *ngIf="col?.is_active && col?.is_visible">
                <span matTooltip="{{
                                  !isFormatted ?
                                              (item['totalList'][col?.tooltip_key ? col?.tooltip_key : col.column_key] | number: '1.2-2') : item['totalList'][col?.tooltip_key ? col?.tooltip_key : col.column_key]
                                            }}">
                  {{
                  !isFormatted
                  ? (((col?.type == 'non-negative') ? (item["totalList"][col.column_key] > 0 ? item["totalList"][col.column_key] : 0) : item["totalList"][col.column_key]) | number : "1.2-2")
                  : item['totalList'][col.column_key]
                  }}
                </span>
              </div>
            </div>
          </div>
        </div>
        <div
          class="data-split-header value-class"
          *ngFor="let data of item?.data"
        >
          <div class="sub-header">
            <div *ngFor="let col of columnList">
              <div class="sub-header-split value-class" *ngIf="col?.is_active && col?.is_visible">
                <span matTooltip="{{
                  !isFormatted ? 
                                (data[col?.tooltip_key ? col?.tooltip_key : col.column_key] | number: '1.2-2') : data[col?.tooltip_key ? col?.tooltip_key : col.column_key]
                              }}">
                  {{
                  !isFormatted
                  ? ( ((col?.type == 'non-negative') ? (data[col.column_key] > 0 ? data[col.column_key] : 0) :  (data[col.column_key])) | number : "1.2-2")
                  : data[col.column_key]
                  }}
                </span>
              </div>
            </div>
          </div>
        </div>
        <div class="data-split-header value-class" *ngIf="!showTotalFlag">
          <div class="sub-header">
            <div *ngFor="let col of columnList">
              <div class="sub-header-split value-class" *ngIf="col?.is_active && col?.is_visible">
                <span matTooltip="{{
                  !isFormatted ?
                                (item['totalList'][col?.tooltip_key ? col?.tooltip_key : col.column_key] | number: '1.2-2') : item['totalList'][col?.tooltip_key ? col?.tooltip_key : col.column_key]
                              }}">
                  {{
                  !isFormatted
                  ? (((col?.type == 'non-negative') ? (item["totalList"][col.column_key] > 0 ? item["totalList"][col.column_key] : 0) :  item["totalList"][col.column_key]) | number : "1.2-2")
                  : item['totalList'][col.column_key]
                  }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="data-row bottom-fixed" *ngIf="data?.length > 0">
      <div class="data-grid-header value-class total-bottom">
        <span>Total&nbsp;({{units}})</span>
      </div>
      <div class="d-flex" *ngIf="groupType == groupBySelected && groupEnable">
        <div class="data-grid-header value-class no-sticky" *ngFor="let group of groupList" [ngStyle]="{'display': group.is_active && group.is_visible ? 'flex' : 'none'}">
          <span class="name-class">-</span>
        </div>
      </div>
      <div class="data-split-header value-class" *ngIf="showTotalFlag">
        <div class="sub-header">
          <div *ngFor="let col of columnList">
            <div class="sub-header-split value-class total-text" *ngIf="col?.is_active && col?.is_visible">
              <span matTooltip="{{
                !isFormatted ?
                            (getTotalData(col?.tooltip_key ? col?.tooltip_key : col.column_key, 'total')  | number: '1.2-2') : getTotalData(col?.tooltip_key ? col?.tooltip_key : col.column_key, 'total')
                          }}">
                {{
                !isFormatted
                ? (getTotalData(col.column_key, 'total',col?.type) | number : "1.2-2")
                : getTotalData(col.column_key, 'total')
                }}
              </span>
            </div>
          </div>
        </div>
      </div>
      <div
        class="data-split-header value-class"
        *ngFor="let data of data[0]?.data"
      >
        <div class="sub-header">
          <div *ngFor="let col of columnList">
            <div class="sub-header-split value-class  total-text" *ngIf="col?.is_active && col?.is_visible">
              <!-- <span>{{ (formattedData[col.column_key] != null ? formattedData[col.column_key] : 0) | number : '1.2-2' }}</span> -->
              <span matTooltip="{{
                !isFormatted ?
                            (getTotalData(col?.tooltip_key ? col?.tooltip_key : col.column_key, data?.title )  | number: '1.2-2') : getTotalData(col?.tooltip_key ? col?.tooltip_key : col.column_key, data?.title )
                          }}">
                {{
                !isFormatted
                ? (getTotalData(col.column_key, data?.title,col?.type) | number : "1.2-2")
                : getTotalData(col.column_key, data?.title)
                }}
              </span>
            </div>
          </div>
        </div>
      </div>
      <div class="data-split-header value-class" *ngIf="!showTotalFlag">
        <div class="sub-header">
          <div *ngFor="let col of columnList">
            <div class="sub-header-split value-class total-text" *ngIf="col?.is_active && col?.is_visible">
              <span matTooltip="{{
                !isFormatted ?
                            (getTotalData(col?.tooltip_key ? col?.tooltip_key : col.column_key, 'total')  | number: '1.2-2') : getTotalData(col?.tooltip_key ? col?.tooltip_key : col.column_key, 'total')
                          }}">
                {{
                !isFormatted
                ? (getTotalData(col.column_key, 'total',col?.type) | number : "1.2-2")
                : getTotalData(col.column_key, 'total')
                }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="no-data-class" *ngIf="data?.length == 0">
      <div class="no-search" *ngIf="searchText && searchText.trim() !== ''; else nodata">
        <img src="https://assets.kebs.app/search_result_animation.gif">
        <span class="no-data-text">No Result Found</span>
        <!-- <span class="no-sub-text">Sorry, we couldn't find any matches for your search.</span> -->
      </div>
      <ng-template #nodata>
        <img src="https://assets.kebs.app/ATS-Empty-State.png">
        <span class="no-data-text">No Data</span>
      </ng-template>
    </div>
  </div>
</div>
<div class="grid-component" *ngIf="isComponentLoading">
  <div class="loader-container">
    <div class="loader"></div>
  </div>
</div>

<sat-popover
        #showCustomizeCard
        horizontalAlign="after"
        verticalAlign="below"
        hasBackdrop
      >
        <div class="column-grid-config-popup-card card">
          <div *ngFor="let column of getColumnList(); let i = index">
            <div class="setting-list-item" *ngIf="column?.is_active">
              <mat-slide-toggle
                class="button-slide-toggle"
                [checked]="column.is_visible"
                (change)="onGridCardSettingsChange($event, column)"
              >
              </mat-slide-toggle>
              <span class="label-class" *ngIf="column.is_group; else notGroup">
                {{ column.name ? column.name : "-" }}
              </span>
              <ng-template #notGroup>
                <span class="label-class">
                  {{ column.column_label ? column.column_label : "-" }}
                </span>
              </ng-template>
            </div>
          </div>
        </div>
      </sat-popover>
