import { Component, EventEmitter, HostListener, Input, OnInit, Output } from '@angular/core';
import { ToasterMessageService } from 'src/app/modules/project-management/shared-lazy-loaded/components/toaster-message/toaster-message.service';

import * as _ from 'underscore';
import { ReportDashboardServiceService } from '../../services/report-dashboard-service.service';
import { Subject, Subscription } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { PmMasterService } from 'src/app/modules/project-management/services/pm-master.service';
import { FilterServiceService } from 'src/app/modules/project-management/shared-lazy-loaded/components/filters-common-dialog/services/filter-service.service';
import { ProjectSearchService } from 'src/app/modules/project-management/shared-lazy-loaded/components/project-search/services/project-search.service';
import moment from 'moment';

@Component({
  selector: 'app-report-grid',
  templateUrl: './report-grid.component.html',
  styleUrls: ['./report-grid.component.scss'],
})
export class ReportGridComponent implements OnInit {
  @Input() columnList: Array<Object> = [];
  @Input() gridSettings:Object = null;
  @Input() applied_duration:Object = null; 
  @Input() formConfig:any = null;
  @Input() card:any = null;
  @Input() gridData:any = null;
  @Input() dataLoaded:boolean = false;
  @Input() reportId:any = null;
  @Input() cardSettings: any = [];
  @Output() gridDataLoaded = new EventEmitter<any>();

  data = [];
  showTotalFlag: boolean = false;
  viewTypeSelected: any = null;
  viewTypeSettings:any = [];
  groupTypeSettings:any = [];
  groupBySelected:any;
  isComponentLoading: boolean = false;
  filterData:any = [];
  urlLinkData:Array<Object> = [];
  formattedData:any = [];
  totalList:any = [];
  isFormatted: boolean = false;
  units: any = "Hrs";
  filterSubscription$: Subscription;
  application_id = null
  internal_application_id = null;
  filterConfig:Object = {};
  searchText:string = null;
  startIndex: number = 0;
  skip:number = 0;
  dataList:any = [];
  isViewTypeSettingsActive: boolean = true;
  groupList: any = [];
  groupEnable:any = [];
  groupType:any = '';
  limit: number = 15;
  protected _onDestroy = new Subject<void>();
  constructor(private _toasterService: ToasterMessageService, private _reportService: ReportDashboardServiceService, private _masterService : PmMasterService,private _filterService: FilterServiceService, private _searchService: ProjectSearchService) {}

  async ngOnInit() {
    this.isComponentLoading = true;
    this.calculateDynamicHeight();
    await this.initializeFormConfig();
    await this.initializeGridSettings();
    await this.updateFilterData(null, 'all',true);
    await this.updateSavedConfig();
    if(this.gridSettings['filter_settings'] && this.gridSettings['filter_settings']['filter_enable'] && this.gridSettings['filter_settings']['application_id'] && this.gridSettings['filter_settings']['internal_application_id']) {
      this.application_id = this.gridSettings['filter_settings']['application_id']
      this.internal_application_id = this.gridSettings['filter_settings']['internal_application_id']
      this.filterSubscription$ = await this._filterService.getFilterConfig(this.gridSettings['filter_settings']['application_id'],this.gridSettings['filter_settings']['internal_application_id']).subscribe(async filterList => {
        this.isComponentLoading = true;
        await this.getGridData();
        this.isComponentLoading = false;
      });
    }
    else{
      if(!this.dataLoaded){
        await this.getGridData();
      }
      else{
        this.data = (this.gridData && this.gridData['data'] && this.gridData['data'].length > 0) ? this.gridData['data'] : []
        this.formattedData = (this.gridData && this.gridData['totalList'] && this.gridData['totalList'].length > 0) ? this.gridData['totalList'] : []
        this.isFormatted = (this.gridData && this.gridData['isFormatted']) ? this.gridData['isFormatted'] : false;
        this.units = (this.gridData && this.gridData['units']) ? this.gridData['units'] : 'Hrs';
        this._reportService.gridData = this.gridData;
        this._reportService.gridLoader = false;
        this._reportService.units = this.units
        this.dataLoaded = false
      }
      
    }
    this._searchService.searchData.searchParameter = ""
    this._searchService.searchData.startProjectSearch = this.startSearch.bind(this);
    // await this.formatMonthwiseData();
    this.startIndex = 0;
    this.dataList = [];
    await this.calculateSkipLimit();
    this.skip = this.limit;
    await this.getLazyLoadedData();
    this.isComponentLoading = false;
  }

  /**
   * @description Filtering the Active and Visible Columns
   * @returns
   */
  filteredColumnList() {
    return this.columnList.filter(
      (column) => column['is_active'] && column['is_visible']
    );
  }


  /**
   * @description For Changing the Toggle for Summary Card Display
   * @param event 
   * @param card 
   */
  async onGridCardSettingsChange(event,card){
    if(card.is_group){
      const cardIndex = this.groupList.findIndex(
        (item) => item['key'] === card.key
      );
      if (cardIndex != -1) {
        if (this.groupList[cardIndex]['is_visible']) {
          this.groupList[cardIndex]['is_visible'] = false;
        }
        else{
          this.groupList[cardIndex]['is_visible'] = true;
        }
      }
    }
    else{
      let visibleColumns = _.where(
        this.columnList,
        { is_visible: true, is_active: true }
      );
      const cardIndex = this.columnList.findIndex(
        (item) => item['column_key'] === card.column_key
      );
      if (cardIndex != -1) {
        if (this.columnList[cardIndex]['is_visible']) {
          if (visibleColumns.length <= 1) {
            this._toasterService.showWarning(
              'At least 1 Sub Column needs to be selected!',
              10000
            );
            event.source.checked = true;
          } else {
            this.columnList[cardIndex]['is_visible'] =
              false;
          }
        } else if (
          !this.columnList[cardIndex]['is_visible']
        ) {
          this.columnList[cardIndex]['is_visible'] = true;
        }
  
      }
    }
    await this.saveReportConfig();
  }

  /**
   * @description Initialize Grid Settings
   */
  initializeGridSettings(){
    this.viewTypeSelected = this.gridSettings['viewTypeSelected'] ? this.gridSettings['viewTypeSelected'] : 'month';
    this.viewTypeSettings = _.where(
      this.gridSettings['view_type_settings'],
      {is_active:true}
    );
    this.isViewTypeSettingsActive = (this.gridSettings['is_view_type_settings_visible'] != undefined ) ? this.gridSettings['is_view_type_settings_visible'] : true;

    let selectedLevelList = _.where(this.gridSettings?.['link_settings'] || []);
    this.groupTypeSettings = selectedLevelList
    selectedLevelList = _.sortBy(selectedLevelList, 'position');
    if (selectedLevelList.length > 0) {
      this.groupBySelected = selectedLevelList[0]['link_url'];
      this._reportService.groupBySelected = this.groupBySelected;
    }

    if(this.urlLinkData.length == 0 && this.groupTypeSettings.length > 0) {
      this.urlLinkData.push(this.groupTypeSettings[0])
    }

    this.groupEnable = (this.gridSettings['group_settings']) ? (this.gridSettings['group_settings'].is_active ? this.gridSettings['group_settings'].is_active : false) : false;
    if(this.groupEnable){
      this.groupList = (this.gridSettings['group_settings']) ? (this.gridSettings['group_settings'].groupList ? this.gridSettings['group_settings'].groupList : []) : [];
      this.groupType = (this.gridSettings['group_settings']) ? (this.gridSettings['group_settings'].type ? this.gridSettings['group_settings'].type : '') : '';
    }
  }

  /**
   * @description For changing the View Type
   * @param value 
   * @param $event 
   */
  async changeViewType(value, $event) {
    this.viewTypeSelected = value.id
    this.isComponentLoading = true;
    await this.getGridData();
    this.startIndex = 0;
    this.dataList = [];
    this.skip = this.limit;
    await this.getLazyLoadedData();
    // await this.formatMonthwiseData();
    await this.saveReportConfig();
    this.isComponentLoading = false
  }

  /**
   * @description Routing for URL
   * @param link 
   */
  async openLinkUrl(link) {
    this.groupBySelected = link.link_url
    this._reportService.groupBySelected = this.groupBySelected;
    let index = this.urlLinkData.findIndex(item => item['link_url'] === link.link_url);
    if (index !== -1) {
      this.urlLinkData = this.urlLinkData.slice(0, index + 1);
    }
    this.filterData =[];
    this.isComponentLoading = true;
    this.updateFilterData(null,link)
    await this.getGridData();
    this.startIndex = 0;
    this.dataList = [];
    this.skip = this.limit;
    await this.getLazyLoadedData();
    // await this.formatMonthwiseData();
    await this.saveReportConfig();
    this.isComponentLoading = false
  }

  /**
   * @description Routing to Next URL
   */
  async openNextUrl(link,linkData){
    let currentLink = this.groupTypeSettings.find(item => item.link_url === link);
    const existingIndex = this.urlLinkData.findIndex(item => item['link_url'] === link);
    this.groupBySelected = currentLink.next_link_url ? currentLink.next_link_url : link
    this._reportService.groupBySelected = this.groupBySelected;
    if (this.groupBySelected != link) {
      this.isComponentLoading = true;
      if (existingIndex !== -1) {
        let updatedLink = {
          ...this.urlLinkData[existingIndex],
          data: linkData 
        };
        this.urlLinkData[existingIndex] = updatedLink;
      }
      let current_link = this.groupTypeSettings.find(item => item.link_url === this.groupBySelected);
      this.urlLinkData.push(current_link);
      await this.updateFilterData(linkData,link)
      await this.getGridData();
      this.startIndex = 0;
      this.dataList = [];
      this.skip = this.limit;
      await this.getLazyLoadedData();
      // await this.formatMonthwiseData();
      await this.saveReportConfig();
      this.isComponentLoading = false;
    }
  }

  /**
   * @description Changing the Group By Data for level
   * @param value 
   * @param $event 
   */
  async changeGroupBYType(value, $event){
    this.groupBySelected = value.link_url;
    this._reportService.groupBySelected = this.groupBySelected;
    this.urlLinkData = [];
    this.groupTypeSettings.some(item => {
        this.urlLinkData.push(item);
        return this.groupBySelected === item.link_url;
    });
    this.filterData =[];
    this.updateFilterData(null, 'all');
    this.isComponentLoading = true;
    await this.getGridData();
    this.startIndex = 0;
    this.dataList = [];
    this.skip = this.limit;
    await this.getLazyLoadedData();
    // await this.formatMonthwiseData();
    await this.saveReportConfig();
    this.isComponentLoading = false;
  }

   /**
   * @description Getting the Grid Data
   */
   async getGridData() {
    if(this.gridSettings['filter_settings'] && this.gridSettings['filter_settings']['filter_enable'] && this.gridSettings['filter_settings']['application_id'] && this.gridSettings['filter_settings']['internal_application_id']){
      await this.applyFilter()
    }
    this._reportService.gridLoader = true;
    let grid_config = null;
    if (
      this.gridSettings['data_config']
    ) {
      grid_config = this.gridSettings['data_config'];
      grid_config.params.startDate = this.applied_duration['startDate'];
      grid_config.params.endDate = this.applied_duration['endDate'];
      grid_config.params.groupedByData = this.groupBySelected;
      grid_config.params.viewTypeData = this.viewTypeSelected;
      grid_config.params.filterData = this.filterData;
      grid_config.params.filter_config = this.filterConfig;
      grid_config.params.search_text = this.searchText;
    }
    const item = this.groupTypeSettings.find(item => item.link_url === this.groupBySelected);
    this._reportService.gridLabel =  item?.link_label ?? '';
    return new Promise((resolve, reject) => {
      this._reportService
        .getReportGridData(grid_config)
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res) => {
            if (res['messType'] == 'S') {
              this.data = (res['data'] && res['data'].length > 0) ? res['data'] : []
              this.formattedData = (res['totalList'] && res['totalList'].length > 0) ? res['totalList'] : []
              this.isFormatted = res['isFormatted'] ? res['isFormatted'] : false;
              this.units = res['units'] ? res['units'] : 'Hrs';
              this._reportService.units = this.units
              this._reportService.gridData = res;
              this._reportService.gridLoader = false;
              // Emit the data to the parent
              this.gridDataLoaded.emit(this._reportService.gridData);
              resolve(res['data']);
            } else {
              this._reportService.gridData = null;
              this._reportService.gridLoader = false;
              // Emit the data to the parent
              this.gridDataLoaded.emit(this._reportService.gridData);
              resolve([]);
            }
          },
          error: (err) => {
            this._toasterService.showError(
              'Error in Fetching Grid Data'
            );
            console.log(err);
            this._reportService.gridData = null;
            this._reportService.gridLoader = false;
            this.gridDataLoaded.emit(this._reportService.gridData);
            resolve([]);
          },
        });
    });
  }

  @HostListener('window:resize')
  onResize() {
    this.calculateDynamicHeight();
  }

  /**
   * @description Calculating the Dymanic Height and Width
   */
  calculateDynamicHeight(){
    let gridHeight = window.innerHeight - 360 + 'px';
    document.documentElement.style.setProperty(
      '--gridTableHeight',
      gridHeight
    );
    let gridWidth = window.innerWidth - 148 + 'px';
    document.documentElement.style.setProperty(
      '--gridTableWidth',
      gridWidth
    );
  }

  /**
   * @description Initialization of Form Config
   */
  async initializeFormConfig(){
    const retrieveStyles = _.where(this.formConfig, {
      type: 'project-theme',
      field_name: 'styles',
      is_active: true,
    });

    let button =
      retrieveStyles.length > 0
        ? retrieveStyles[0].data.button_color
          ? retrieveStyles[0].data.button_color
          : '#ee4967'
        : '#ee4967';
    
    document.documentElement.style.setProperty('--teamButton', button);
    let fontStyle =
      retrieveStyles.length > 0
        ? retrieveStyles[0].data.font_style
          ? retrieveStyles[0].data.font_style
          : 'Roboto'
        : 'Roboto';
    document.documentElement.style.setProperty('--teamFont', fontStyle);
    let primary = '#FFFFFF';
    let secondary = '#f5f5f5'
    document.documentElement.style.setProperty('--primary', primary);
    document.documentElement.style.setProperty('--secondary', secondary);
  }

  /**
   * @description For Updating the Filter Data
   * @param data 
   * @param type 
   */
  async updateFilterData(data, type,disableSave = false) {
    const existingIndex = this.groupTypeSettings.findIndex(item => item.link_url === type);
    if (type === 'all') {
        this.groupTypeSettings.forEach(item => item.data = null);
    } else { 
        if (existingIndex !== -1) {
            if(data){
              this.groupTypeSettings[existingIndex].data = data;
            }
            // Clear data for subsequent items
            for (let i = existingIndex+1; i < this.groupTypeSettings.length; i++) {
                this.groupTypeSettings[i].data = null;
            }
        }
    }
    // console.log('groupTypeSettings',this.groupTypeSettings)
    const existing_group_Index = this.groupTypeSettings.findIndex(item => item.link_url === this.groupBySelected);
    // Rebuild filterData
    this.filterData = this.groupTypeSettings
    .slice(0, existing_group_Index) // Extract the part of the array after the existingIndex
    .filter(item => item.data && Object.keys(item.data).length > 0)
    .map(item => ({
        type: item.link_url,
        data: item.data['id'],
        position: item.position
    }));
    // console.log(this.filterData)
    if(!disableSave){
      await this.saveReportConfig();
    }    
  }

  getCurrentHeaderValueName(){
    const item = this.groupTypeSettings.find(item => item.next_link_url === this.groupBySelected);
    return item?.data?.name ?? 'Global';
  }

  /**
   * @description Formatting Data with Month wise
   */
  formatMonthwiseData() {
    const monthwiseData = {};
    const totalList = {
      available: 0,
      allocated: 0,
      billed: 0,
      unallocated: 0,
      logged: 0,
    };
  
    // Flatten the data structure
    const flatData = [];
    for (let i = 0; i < this.data.length; i++) {
      const region = this.data[i];
      for (let j = 0; j < region.data.length; j++) {
        flatData.push(region.data[j]);
      }
    }
  
    // Process the flat data structure in a single loop
    for (let i = 0; i < flatData.length; i++) {
      const entry = flatData[i];
      const { title, available, allocated, billed, unallocated, logged } = entry;
  
      if (!monthwiseData[title]) {
        monthwiseData[title] = {
          title,
          available: 0,
          allocated: 0,
          billed: 0,
          unallocated: 0,
          logged: 0,
        };
      }
  
      monthwiseData[title].available += available;
      monthwiseData[title].allocated += allocated;
      monthwiseData[title].billed += billed;
      monthwiseData[title].unallocated += unallocated;
      monthwiseData[title].logged += logged;
  
      // Add to totalList
      totalList.available += available;
      totalList.allocated += allocated;
      totalList.billed += billed;
      totalList.unallocated += unallocated;
      totalList.logged += logged;
    }
  
    this.formattedData = Object.values(monthwiseData);
    this.totalList = totalList;  // Store totalList separately
  }
  

  getTotalData(col,data,type=null){
    if(data && col){
      const item = this.formattedData.find(item => item.title === data);
      if(type && type == 'non-negative'){
        return (item[col] && item[col] > 0) ? item[col] : 0
      }
      else
      return item[col] ? item[col] : 0
    }
    
    return 0;
  }


  async applyFilter() {
    let filter = await this.getUserFilterConfig();
    
    if (filter && filter !== '' && filter != null) {
      let filterVal = (filter && filter['filterConfig'] && filter['filterConfig']['filterData']) ? filter['filterConfig']['filterData'] : [];
      
      let query = this._filterService.generateConditionBasedQuery(filterVal);
     
      this.filterConfig['filter'] = filterVal ? filterVal : [];
      this.filterConfig['condition_query'] = query ? query : ''
    }
  }

  /**
   * @description Fetching the User Filter Config
   */
  getUserFilterConfig(){
    return new Promise((resolve,reject) => {
      this._filterService.getFilterUserConfig(this.application_id,
      this.internal_application_id).pipe(takeUntil(this._onDestroy))
      .subscribe({
        next: (res) => {
          
          if(res['messType'] == 'S') {
            resolve(res['data'])
          }
          else{
            resolve([])
          }
        },
        error:(err) => {
          this._toasterService.showError('Error in Fetching User Filter Config');
          resolve([]);
        }
      });
    });
  }

  openFilter(){
    this._filterService.openFilterLandingPage(this.application_id,this.internal_application_id);
  }

  async startSearch() {
    // console.log("Search Parameter", this._searchService.searchData.searchParameter)
    this.searchText = this._searchService.searchData.searchParameter
    this.isComponentLoading = true;
    await this.getGridData();
    this.startIndex = 0;
    this.dataList = [];
    this.skip = this.limit;
    await this.getLazyLoadedData();
    this.isComponentLoading = false;
    // console.log('Searched Parameter:',this.searchText)
  }

  /**
   * Applying Infinite Scroll
   */
  async onScroll(){
    // console.log('Scroll called:')
    this.startIndex = this.skip;
    this.skip += this.limit;
    await this.getLazyLoadedData();
  }

  getLazyLoadedData(){
    let len = this.data ? this.data.length : 0;
    for(let i = this.startIndex; i < this.skip; i++){
      if(i < len)
        this.dataList.push(this.data[i]);
      else break;
    }
  }

  getColumnList() {
    if (this.groupType == this.groupBySelected && this.groupEnable) {
      // Combine groupList and columnList if groupEnable is true
      return [...this.groupList, ...this.columnList];
    } else {
      // Return only columnList if groupEnable is false
      return this.columnList;
    }
  }

  /**
   * @description Save the User Config
   */
  async saveReportConfig(){
    let reportConfig = {
      "card_settings": this.cardSettings ? this.cardSettings : [],
      "viewTypeSelected": this.viewTypeSelected,
      "groupBySelected": this.groupBySelected,
      "urlLinkData": this.urlLinkData,
      "column_settings": this.columnList ? this.columnList : [],
      "group_settings" :  this.groupList ? this.groupList : [],
      "units": this.units,
      "filterData": this.filterData,
      "groupTypeSettings": this.groupTypeSettings
    }
    await this.saveUserConfig(reportConfig);
  }

  /**
   * @description Save User Config
   * @returns
   */
  saveUserConfig(reportConfig) {
    let currentDate = moment().format();
    return new Promise((resolve, reject) => {
      this._reportService
        .saveUserConfig(
          this.reportId,
          reportConfig,
          currentDate
          )
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res) => {
            if (res['messType'] == 'S') {
              resolve(true);
            }
            else
            resolve(false);
          },
          error: (err) => {
            this._toasterService.showError('Error in Saving Configuration');
            console.log(err);
            resolve(false);
          },
        });
    });
  }

  
  /**
   * @description Updating Based on Saved Config
   */
  async updateSavedConfig(){
    let savedConfig = await this.getSavedConfig();
    savedConfig = (savedConfig && savedConfig['reportConfig']) ? savedConfig['reportConfig'] : null;
    if(savedConfig){
      savedConfig = savedConfig

      //Updating the ViewType
      if(savedConfig['viewTypeSelected']){
        this.viewTypeSelected = savedConfig['viewTypeSelected']
      }  
      //Update groupBySelected
      if(savedConfig['groupBySelected']){
        this.groupBySelected = savedConfig['groupBySelected']
      }

      //Updating Unit Settings
      if(savedConfig['units']){
        this.units = savedConfig['units']
      }

      // Updating column_settings
      if (
        savedConfig['column_settings'] &&
        savedConfig['column_settings'].length > 0 &&
        this.columnList &&
        this.columnList.length > 0
      ) {
          const columnVisibilityMap = savedConfig['column_settings'].reduce((map, setting) => {
              map[setting.column_key] = setting.is_visible;
              return map;
          }, {});
      
          this.columnList.forEach((item) => {
              if (columnVisibilityMap.hasOwnProperty(item['column_key'])) {
                  item["is_visible"] = columnVisibilityMap[item['column_key']];
              }
          });
      }

      // Updating group_settings
      if (
          savedConfig['group_settings'] &&
          savedConfig['group_settings'].length > 0 &&
          this.groupList &&
          this.groupList.length > 0
      ) {
          const groupVisibilityMap = savedConfig['group_settings'].reduce((map, setting) => {
              map[setting.key] = setting.is_visible;
              return map;
          }, {});
      
          this.groupList.forEach((item) => {
              if (groupVisibilityMap.hasOwnProperty(item.key)) {
                  item.is_visible = groupVisibilityMap[item.key];
              }
          });
      }

      //Updating filter data, urlLinkData & groupType Settings
      let { filterData, urlLinkData, groupTypeSettings } = this.constructFilterData(
        (this.gridSettings?.['link_settings'] || []), (savedConfig['filterData'] || []), (savedConfig['urlLinkData'] || [])
      );

      this.filterData = filterData;
      this.urlLinkData = urlLinkData;
      this.groupTypeSettings = groupTypeSettings;
  
      const lastUrlLink = this.urlLinkData[this.urlLinkData.length - 1];
  
      // If last item exists, patch viewTypeData with link_url
      if (lastUrlLink && lastUrlLink['link_url']) {
        this.groupBySelected = lastUrlLink['link_url']
      }

    }
  }

  /**
   * @description Getting Saved Config
   * @returns
   */
  getSavedConfig() {
    return new Promise((resolve, reject) => {
      this._reportService
        .getSavedConfig(
          this.reportId
          )
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res) => {
            if (res['messType'] == 'S') {
              resolve(res['data']);
            }
            resolve(null);
          },
          error: (err) => {
            this._toasterService.showError('Error in Fetching Saved Configuration');
            console.log(err);
            resolve(null);
          },
        });
    });
  }

  constructFilterData(link_settings,filterDataSaved,urlLinkDataSaved){
    let filterData = [];
    let urlLinkData = [];
    let groupTypeSettings = [];
    let mismatchFound = false;
    let nullDataAdded = false;

    link_settings.forEach((linkItem) => {
        // Check for a continuous match in filterData
        const savedFilter = filterDataSaved?.find(item => item.type === linkItem.link_url);
        const savedUrlLink = urlLinkDataSaved?.find(item => item.link_url === linkItem.link_url);

        if (!mismatchFound && savedFilter && savedUrlLink) {
            // Construct filterData with a continuous match
            filterData.push({
                type: linkItem.link_url,
                data: savedFilter.data,
                position: linkItem.position
            });

            // Construct urlLinkData with matching link_url
            urlLinkData.push({
                ...linkItem,
                data: savedUrlLink.data // Populate with matched data from savedConfig
            });

            // Push to groupTypeSettings with matching data
            groupTypeSettings.push({
                ...linkItem,
                data: savedUrlLink.data
            });
        } else {
            // Stop pushing to filterData and urlLinkData if mismatch found
            mismatchFound = true;

            // Populate groupTypeSettings with empty data for non-matching entries
            groupTypeSettings.push({
                ...linkItem,
                data: {}
            });

            // Add null data entry to urlLinkData if not already added
            if (!nullDataAdded) {
                urlLinkData.push({
                    ...linkItem,
                    data: null // Add a null entry after first mismatch
                });
                nullDataAdded = true;
            }
        }
    });

    return { filterData, urlLinkData, groupTypeSettings };

  }

  /**
   * @description Calculating Skip limit
   */
  calculateSkipLimit(){
    const tableHeight = window.innerHeight - 360;
    const itemHeight = 35;

    // Calculate how many items fit into the container
    const itemsToLoad = Math.floor(tableHeight / itemHeight);
    this.limit = itemsToLoad > 15 ? itemsToLoad : 15
  }

  ngOnDestroy() {
    if(this.filterSubscription$){
      this.filterSubscription$.unsubscribe();
    }
  }

}
