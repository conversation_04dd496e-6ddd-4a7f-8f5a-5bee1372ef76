<div class="summary-card-class" *ngIf="cardConfig">
  <div class="header">
    <div class="header-txt">
      <span class="overflow-class" tooltip ="{{cardConfig.card_label ? cardConfig.card_label : '-'}}">{{
        cardConfig.card_label ? cardConfig.card_label : "-"
      }}</span>
    </div>
    <mat-icon
      matTooltip="Open"
      class="open-icn"
      (click)="openSummaryDetailCard()"
      *ngIf="cardConfig?.view_detail">open_in_new</mat-icon
    >
  </div>
  <div class="content-section">
    <div
      class="row chart-class"
      *ngIf="cardConfig?.type == 'circular-progress'"
    >
      <div class="col-3 p-0 m-0">
        <div
          class="circular-progress"
          [style.background]="conicGradient(totalCount)"
        >
          <div class="progress-value">
            <span>{{ totalCount ? totalCount : 0 }} %</span>
          </div>
        </div>
      </div>
      <div class="col-9 pl-3 pr-0 m-0">
        <div class="display-data-class">
          <div class="task-data-class" *ngFor="let item of temp">
            <div class="task-title">
              <span class="overflow-class"  tooltip ="{{item.task_label ? item.task_label : '-'}}">{{ item?.task_label }}</span>
            </div>
            <div class="task-data-hyphen">-</div>
            <div class="task-data">
              <span class="overflow-class" tooltip="{{item.task_count ? item.task_count : 0}}">{{ item?.task_count | number : "1.2-2"}} </span>
              <span>{{ item?.unit }} </span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="row bar-class" *ngIf="cardConfig?.type == 'bar-progress'">
      <div class="col-10 p-0 m-0">
        <div *ngFor="let item of temp" class="row pb-2 bar-comparision">
          <div class="col-7 p-0 m-0">
            <div
              class="bar-style"
              [ngStyle]="{ width: item?.percentage + '%' }"
            >
              <span class="bar-label-class">{{ item?.task_label }}</span>
            </div>
          </div>
          <div class="col-5 p-0 m-0 value-class">
            <span class="overflow-class">{{ item?.task_count | number : "1.2-2" }}</span>
            <span>{{ item?.unit }}</span>
          </div>
        </div>
      </div>
      <div class="col-2 p-0 pl-1 m-0 mt-4">
        <div class="progress-value d-flex align-content-end">
          <span class="overflow-class">{{ totalCount ? totalCount : 0 }}</span>
          <span>%</span>
        </div>
      </div>
    </div>
  </div>
</div>
