<div>
    <form [formGroup]="dateForm">
        <div class="row  w-100 pt-2 pb-2">

            <div class="col-2 pr-0">
                <mat-form-field appearance="outline" style="width: 90%">
                    <mat-label>Start Period</mat-label>
                    <input matInput [matDatepicker]="sd" formControlName="startDate" [min]="minStartDate">
                    <mat-datepicker-toggle matSuffix [for]="sd"></mat-datepicker-toggle>
                    <mat-datepicker #sd></mat-datepicker>
                </mat-form-field>
            </div>
            <div class="col-2 pl-0">
                <mat-form-field appearance="outline" style="width: 90%">
                    <mat-label>End Period</mat-label>
                    <input matInput [matDatepicker]="ed" formControlName="endDate" [min]="minEndDate"
                        placeholder="MMMM-YYYY">
                    <mat-datepicker-toggle matSuffix [for]="ed"></mat-datepicker-toggle>
                    <mat-datepicker #ed></mat-datepicker>
                </mat-form-field>

            </div>
            <div class="col-1 pl-0 mt-2">
                <button mat-icon-button matToolTip="Submit" class="iconbtn mr-2"
                    style="font-weight: normal; margin-left: 1%; margin-bottom: 1%;"
                    (click)="revenueActualsDataUpdate()">
                    <mat-icon>done_all</mat-icon>
                </button>
            </div>
            <div class="col-4 pl-0 pr-0 d-flex">
                <button mat-icon-button (click)="scrollLeft()" class="iconsSize btn-fab-left mt-2"
                    *ngIf="this.views?.allViews?.length > 0">
                    <mat-icon style="color: #1e2733 !important; font-size: 21px !important;">chevron_left</mat-icon>
                </button>
                <div class="d-flex scrollable-container" style="max-width: 100%; min-width: 70%;" #cardScroll>
                    <div class="d-flex" style="white-space: nowrap;">
                        <button mat-raised-button style="font-weight: normal; margin-left: 1%;" (click)="changeView(i)"
                            *ngFor="let name of this.views?.allViews; let i = index" (mouseenter)="name.visibleDeleteView=true;"
                            (mouseleave)="name.visibleDeleteView=false;" [ngClass]="
                            i == this.currentView
                              ? 'btn-active my-2 version-button'
                              : 'btn-not-active my-2  version-button'
                          " matTooltip="{{ name?.config_name }}" matTooltipPosition="above">
            
                            <span class="button-text">{{ name?.config_name }}</span>
                            <mat-icon *ngIf="name.visibleDeleteView" class="resource-costing-icon ml-1 mt-1" (click)="deleteVariant(i);">
                                delete</mat-icon>
                        </button>
                    </div>
                </div>
                <button mat-icon-button (click)="scrollRight()" class="iconsSize btn-fab-right mt-2"
                    *ngIf="this.views?.allViews?.length > 0">
                    <mat-icon style="color: #1e2733 !important; font-size: 21px !important;">chevron_right</mat-icon>
                </button>
            </div>
            <div class="col-3 pr-0 pl-0 d-flex align-items-baseline">
                <button mat-icon-button matTooltip="Edit View" class="iconsSize ml-auto mt-1"
                    [matMenuTriggerFor]="options">
                    <mat-icon class="iconsSize" matBadge="!" matBadgeSize="small" [matBadgeHidden]="displayView"
                        matBadgeColor="warn">create</mat-icon>
                </button>

                <mat-menu #options="matMenu" style="min-height: none !important;">
                    <div class="card">
                        <div class="card-body pl-3 pr-3 pt-2 pb-2">
                            <div class="row">
                                <div class="col-12">
                                    <button mat-icon-button matTooltip="Hide Data Fields" class="iconsSize ml-1"
                                        (click)="showDataFieldsFn()">
                                        <mat-icon class="iconsSize" matBadge="!" matBadgeSize="small"
                                            [matBadgeHidden]="showDataFields" matBadgeColor="warn">menu_open</mat-icon>
                                    </button>
                                    <button mat-icon-button matTooltip="Hide Row Fields" class="iconsSize ml-1"
                                        (click)="showRowFieldsFn()">
                                        <mat-icon class="iconsSize" matBadge="!" matBadgeSize="small"
                                            [matBadgeHidden]="showRowFields" matBadgeColor="warn">menu_open</mat-icon>
                                    </button>
                                    <button mat-icon-button matTooltip="Hide Column Fields" class="iconsSize ml-1"
                                        (click)="showColumnFieldsFn()">
                                        <mat-icon class="iconsSize" matBadge="!" matBadgeSize="small"
                                            [matBadgeHidden]="showColumnFields"
                                            matBadgeColor="warn">view_column</mat-icon>
                                    </button>
                                    <button mat-icon-button matTooltip="Hide Filter Fields" class="iconsSize ml-1"
                                        (click)="showFilterFieldsFn()">
                                        <mat-icon class="iconsSize" matBadge="!" matBadgeSize="small"
                                            [matBadgeHidden]="showFilterFields"
                                            matBadgeColor="warn">filter_list</mat-icon>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </mat-menu>

                <button mat-icon-button matTooltip="Save" class="iconsSize ml-1 mt-1" (click)="stateUpdate()"
                    [disabled]="views?.allViews?.length == 0">
                    <mat-icon class="iconsSize">save</mat-icon>
                </button>
                <button data-step="9" data-intro="Click to Save as a new version" data-position="right" mat-icon-button
                    matTooltip="Save As" class="iconsSize ml-1 mt-1" [satPopoverAnchor]="saveAs"
                    (click)="saveAs.toggle()">
                    <mat-icon class="iconsSize">move_to_inbox</mat-icon>
                </button>
                <sat-popover #saveAs horizontalAlign="after" verticalAlign="below">
                    <div style="
                box-shadow: 0 3px 1px -2px rgba(0, 0, 0, 0.2),
                  0 2px 2px 0 rgba(0, 0, 0, 0.14),
                  0 1px 5px 0 rgba(0, 0, 0, 0.12) !important;
                padding: 16px !important;
                background-color: white !important;
                max-width: 300px !important;
                line-height: 33px !important;
              ">
                        <div class="row">
                            <mat-form-field>
                                <input matInput placeholder="Save as - Name" [formControl]="versionName" required />
                                <mat-error *ngIf="versionName.hasError('required')">
                                    Name is required
                                </mat-error>
                            </mat-form-field>
                        </div>
                        <div class="row">
                            <button mat-raised-button color="warn" [disabled]="!versionName.value"
                                (click)="saveState(); saveAs.toggle()">
                                Save
                            </button>
                        </div>
                    </div>
                </sat-popover>
                <!-- <dx-button class="iconsSize ml-1" icon="clear">
                </dx-button> -->
                <dx-button class="ml-1 iconsSize" icon="refresh" (onClick)="refresh()" matTooltip="Refresh"></dx-button>
                <dx-button class="iconsSize ml-1" icon="columnchooser"
                    (onClick)="pivotGrid1.getFieldChooserPopup().show()" matTooltip="Column Chooser">
                </dx-button>
                <dx-button class="iconsSize ml-1" icon="exportxlsx" (onClick)="pivotGrid1.exportToExcel()" matTooltip="Export"></dx-button>
            </div>
        </div>


        <div class="row pb-1">
            <div class="col-12 text-right" style="text-align: center;">

                <mat-slide-toggle color="primary" [checked]="showSubTotal"
                    style="font-weight: normal; color: #1E2733; font-size: 14px; vertical-align: middle; margin-top: 6px; margin-right: 15px;"
                    (change)="showSubTotal = $event.checked">Show Sub Total</mat-slide-toggle>

                <mat-slide-toggle color="primary" [checked]="showRowGrandTotals"
                    style="font-weight: normal; color: #1E2733; font-size: 14px; vertical-align: middle; margin-top: 6px; margin-right: 15px;"
                    (change)="showRowGrandTotals = $event.checked">Show Row Grand Totals</mat-slide-toggle>


                <mat-slide-toggle color="primary" [checked]="showColumnGrandTotals"
                    style="font-weight: normal; color: #1E2733; font-size: 14px; vertical-align: middle; margin-top: 6px;"
                    (change)="showColumnGrandTotals = $event.checked">Show Column Grand Totals</mat-slide-toggle>

            </div>

        </div>
        <dx-pivot-grid id="costing" [allowSortingBySummary]="true" [allowSorting]="true" [allowFiltering]="true"
            [allowExpandAll]="true" [showBorders]="true" [dataSource]="dataSource" [wordWrapEnabled]="false"
            [height]="580" [showColumnTotals]="showSubTotal" [showColumnGrandTotals]="showColumnGrandTotals"
            [showRowGrandTotals]="showRowGrandTotals" [showRowTotals]="showSubTotal"
            (onInitialized)="onInitialized($event)">

            <dxo-export [enabled]="false" [fileName]="fileName"></dxo-export>
            <dxo-field-chooser [enabled]="false" [allowSearch]="true"></dxo-field-chooser>
            <dxo-field-panel [showDataFields]="showDataFields" [showRowFields]="showRowFields"
                [showColumnFields]="showColumnFields" [showFilterFields]="showFilterFields" [allowFieldDragging]="true"
                [visible]="true"> </dxo-field-panel>
            <dxo-header-filter [allowSearch]="true" [width]="300" [height]="400"></dxo-header-filter>
            <dxo-state-storing [enabled]="false" type="localStorage"
                storageKey="dx-widget-gallery-pivotgrid-storing-revenue-actuals-report">
            </dxo-state-storing>
        </dx-pivot-grid>

    </form>

    <ngx-spinner bdColor="rgba(230, 230, 231,0.7)" size="medium" color="#cf0001" type="ball-clip-rotate"
        fullScreen="true">
        <p style="color: #cf0001; margin-top: 15px !important; font-weight: 400">
            Please wait..
        </p>
    </ngx-spinner>

</div>

<!-- {{dateForm.value |json}} -->