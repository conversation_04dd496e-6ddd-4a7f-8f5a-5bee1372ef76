.iconbtn {
    background-color: #c92020;
    color: white;
    padding: 0 0;
    box-shadow: 0 3px 5px -1px rgba(0, 0, 0, 0.2),
      0 6px 10px 0 rgba(0, 0, 0, 0.14), 0 1px 18px 0 rgba(0, 0, 0, 0.12);
  }

  .card {
    padding: 8px;
    background-color: #f5f5f5;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  
  .card-body {
    display: flex;
    justify-content: space-between; /* Space icons evenly */
    align-items: center;
  }

  .iconsSize {
    font-size: 24px; /* Adjust icon size as needed */
    color: #424242; /* Dark grey icon color */
  }

.version-button:hover {
  // box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
  cursor: pointer;
  // transition: box-shadow 0.3s;
  .resource-costing-icon {
    visibility: visible !important;
    font-size: 20px !important;
    -webkit-animation: slide-top 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
    animation: slide-top 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
  }
}
.version-button{
  .resource-costing-icon {
    visibility: hidden ;
    position: absolute;
    right: 5px;
    cursor: pointer;

  }
}

.btn-active {
  background-color: #cf0001;
  color: white;
  font-weight: normal;
  font-size: 13px !important;
  min-width: 60px;
  line-height: 33px;
  padding: 0 8px;
  border-radius: 4px;
  margin-right: 6px !important;
  margin-bottom: 3px;
}

.btn-not-active {
  margin-right: 6px !important;
  margin-bottom: 3px;
  color: rgba(0, 0, 0, 0.534);
  background-color: rgba(194, 193, 193, 0.24);
  font-weight: normal;
  font-size: 13px !important;
  min-width: 60px;
  line-height: 33px;
  padding: 0 8px;
  border-radius: 4px;
}

.card-item {
  margin-right: 10px;
}

.button-text {
  overflow: hidden;
  max-width: 10px;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding-right: 25px;
  max-width: 130px;
  width: 130px;
  display: inline-block;
}

.scrollable-container {
  display: flex;
  flex-direction: row;
  overflow: hidden;
  white-space: nowrap;
  flex-grow: 1;
}

button:focus {
  outline: none;
}
