import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import PivotGridDataSource from "devextreme/ui/pivot_grid/data_source";
import { DxPivotGridComponent } from "devextreme-angular";
import { FormControl, FormBuilder, Validators, FormGroup } from "@angular/forms";
import { MatSnackBar } from "@angular/material/snack-bar";
import * as _ from "underscore";
import sweetAlert from 'sweetalert2';
import { UtilityService } from 'src/app/services/utility/utility.service';
import * as moment from 'moment';
import { default as _rollupMoment, Moment } from 'moment';
import { NgxSpinnerService } from 'ngx-spinner';
import { MomentDateAdapter, MAT_MOMENT_DATE_ADAPTER_OPTIONS } from '@angular/material-moment-adapter';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core';
import {RevenueService} from '../../services/revenue.service';
import { Router } from '@angular/router';
@Component({
  selector: 'app-landing-page',
  templateUrl: './landing-page.component.html',
  styleUrls: ['./landing-page.component.scss'],
  providers: [
    {
      provide: DateAdapter,
      useClass: MomentDateAdapter,
      deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS]
    },
    {
      provide: MAT_DATE_FORMATS,
      useValue: {
        parse: {
          dateInput: "DD-MMM-YYYY"
        },
        display: {
          dateInput: "DD-MMM-YYYY",
          monthYearLabel: "MMM YYYY"
        },
        useUtc: true
      }
    },
  ]
})
export class LandingPageComponent implements OnInit {
  @ViewChild(DxPivotGridComponent) pivotGrid: DxPivotGridComponent;
  @ViewChild("cardScroll", { read: ElementRef })
  public cardScroll: ElementRef<any>;
  
  dataSource: any;
  allowSearch: any;
  filters: string;

  storageKey = "dx-widget-gallery-pivotgrid-storing-revenue-actuals-report";
  applicationId = 3005;
  showDataFields: boolean = true;
  showRowFields: boolean = true;
  showColumnFields: boolean = true;
  showFilterFields: boolean = true;
  displayView: boolean = true;
  visibleDeleteView: any;
  currentView: any;
  views: any;
  showSubTotal = true;
  showColumnGrandTotals = true;
  showRowGrandTotals = true;

  versionName = new FormControl("", [Validators.required]);

  pivotGrid1: any;

  minStartDate = moment("2021-04-01");
  minEndDate: any = moment(this.minStartDate).endOf("month");
  startDate = moment().format('YYYY-MM-01');
  endDate = moment(this.startDate).endOf('month').format('YYYY-MM-01')


  defaultCurrency: any;
  freezePeriod: any = "";
  roleAccessFilters: any = "";

  dateForm: FormGroup = this.fb.group({
    startDate: [this.startDate, Validators.required],
    endDate: [this.endDate, Validators.required],
  });
  fileName: any;
  localState: any;
  isAuthorized: boolean = true;
  fields: any;
  constructor(
    private snackBar: MatSnackBar,
    private _util: UtilityService,
    private spinner: NgxSpinnerService,
    private fb: FormBuilder,
    private _revenueService : RevenueService,
    private _router: Router
  ) { 
    this.setCurrentFyData()
  }

  async ngOnInit(){
    await this.reportAuthorizationValidation();
    this.getReportColumnConfig();
    this.subscribeOnValueChanges()
    this.getConfigData();
  }


  revenueActualsDataUpdate(){
    localStorage.setItem("RevenueStartDate", this.dateForm.controls['startDate'].value)
    localStorage.setItem("RevenueEndDate", this.dateForm.controls['endDate'].value)

    this.getRevenueActualsData(this.startDate, this.endDate)
  }

    // Variables used for fetching country level MIS data 
    revenueActualsData: any = [];
    startIndex: number = 0;
    noOfRecords: number = 100000;
    totalCount: any = 0;
    checkDataAvailability: any = 1;

    // To get revenue actuals data !
    async getRevenueActualsData(startDate, endDate) {
      this.fileName = `Revenue-Report-${moment(startDate).format('MMMMYY')}-${moment(endDate).format('MMMMYY')}-${moment().format("h-mm-A")}`;
      this.revenueActualsData = [];
  
      // Reset variables when called again 
      this.startIndex = 0;
      this.totalCount = 0;
      this.checkDataAvailability = 1;
  
      this.spinner.show();
      this.localState = localStorage.getItem(this.storageKey);
      let getRevenueData = await this._revenueService.getRevenueActualsData(startDate, endDate);
      this.revenueActualsData = getRevenueData["data"]
      // Once data is fetched , passing it to devextreme 
      await this.fetchRevenueActualsReport(this.revenueActualsData);
      this.getConfigData();
      this.spinner.hide();
    }

      // PL & Country Level MIS Report Devextreme data retreival and binding function
    fetchRevenueActualsReport = async (revenueData) => {
    let reporting_currency_1 = revenueData[0]?.reporting_currency_1 ? revenueData[0]?.reporting_currency_1 : 'Reporting Currency 1'
    let reporting_currency_2 = revenueData[0]?.reporting_currency_2 ? revenueData[0]?.reporting_currency_2 : 'Reporting Currency 2'
    this.fields = this.fields?.map(field => {
      if (typeof field.caption === 'string') {
        field.caption = field.caption.replace('${reporting_currency_1}', reporting_currency_1)
                                     .replace('${reporting_currency_2}', reporting_currency_2);
      }
      if (field.format && field.format.currency === 'reporting_currency_1') {
        field.format.currency = reporting_currency_1; // Replace with actual value
      }
      if (field.format && field.format.currency === 'reporting_currency_2') {
        field.format.currency = reporting_currency_2; // Replace with actual value
      }
      return field;
    });
    this.dataSource = new PivotGridDataSource({
      fields: this.fields,
      store: this.revenueActualsData
    });
  };


  // To intialize Pivot Grid 
  onInitialized(e) {
    this.pivotGrid1 = e.component;
  }

  refresh(){
    this.getRevenueActualsData(this.startDate, this.endDate)
  }
  
  // Reset Filter
  reset() {
    this.pivotGrid1.getDataSource().state({});
  }

  async setCurrentFyData() {
    let storedStartDate = localStorage.getItem('RevenueStartDate');
    let storedEndDate = localStorage.getItem('RevenueEndDate');

    if (storedStartDate != null && storedEndDate != null) {
      this.startDate = moment(storedStartDate).format('YYYY-MM-DD');
      this.endDate = moment(storedEndDate).format('YYYY-MM-DD');
    }
    else {

      // this.startDate = moment(moment().subtract(2, 'month')).startOf('month').format("YYYY-MM-DD")
      // this.endDate = moment().endOf('month').format("YYYY-MM-DD")

      this.startDate = moment().subtract(1, 'month').startOf('month').format("YYYY-MM-DD");
      this.endDate = moment().add(1, 'month').endOf('month').format("YYYY-MM-DD");


    }

    this.dateForm.patchValue({
      startDate: this.startDate,
      endDate: this.endDate,
    });


    this.minEndDate = moment(this.startDate).endOf('month').format('DD')

    // After Start & End Date fetched
    this.getRevenueActualsData(this.startDate, this.endDate)
  }

  // Subscribe for Start date and End Date on value changes
  subscribeOnValueChanges() {

    this.dateForm.get('startDate').valueChanges.subscribe(res => {
      if (res) {
        this.startDate = moment(res).format("YYYY-MM-DD");
        this.minEndDate = moment(this.startDate)
        const currentEndDate = this.dateForm.get('endDate').value;
        if (currentEndDate && moment(this.startDate).isAfter(moment(currentEndDate))) {
          this.endDate = '';
          this.dateForm.get('endDate').setValue('');
        }
      }
    })

    this.dateForm.get('endDate').valueChanges.subscribe(res => {
      if (res) {
        this.endDate = res;
        this.endDate = moment(this.endDate).endOf('month').format("YYYY-MM-DD");

      }
    })
  }

    // To get user config corresponding to the report from DB
    async getConfigData() {
      await this._revenueService.getReportUserViews(this.applicationId).subscribe(res => {
        this.views = res;
        for (let i = 0; i < this.views.allViews.length; i++) {
          console.log(i)
          this.views.allViews[i].visibleDeleteView = false;
        }
  
        if (this.views.activeView.length > 0 && this.views) {
  
          this.enableDisplayView();
  
          this.currentView = 0;
          localStorage.setItem(this.storageKey, this.views.activeView[0].saved_config);
          this.pivotGrid1.getDataSource().state(JSON.parse(this.views.activeView[0].saved_config))
          if (this.views.activeView[0].field_config.length > 0)
            this.setFieldChoosers(JSON.parse(this.views.activeView[0].field_config)[0])
          else {
            let field_conf = {
  
              showDataFields: true,
              showRowFields: true,
              showColumnFields: true,
              showFilterFields: true,
              showRowGrandTotals: true,
              showColumnGrandTotals: true,
              showSubTotal: true
            };
            this.setFieldChoosers(field_conf)
          }
        }
        else if (this.views.activeView.length == 0 && this.views.allViews.length && this.views) {
          this.enableDisplayView();
          this.currentView = 0;
          localStorage.setItem(this.storageKey, this.views.allViews[0].saved_config);
          this.pivotGrid1.getDataSource().state(JSON.parse(this.views.allViews[0].saved_config))
          if (this.views.activeView[0].field_config.length > 0)
            this.setFieldChoosers(JSON.parse(this.views.allViews[0].field_config)[0])
          else {
            let field_conf = {
  
              showDataFields: true,
              showRowFields: true,
              showColumnFields: true,
              showFilterFields: true,              
              showRowGrandTotals: true,
              showColumnGrandTotals: true,
              showSubTotal: true
            };
            this.setFieldChoosers(field_conf)
          }
        }
        else{
          localStorage.setItem(this.storageKey,this.localState);
          this.pivotGrid1.getDataSource().state(this.localState)
        }
        console.log(this.views);
      }, err => {
  
        this.enableDisplayView();
        this.snackBar.open("Error Retrieving Report Views! Try Refreshing", "Dismiss", { duration: 2000 });
        console.log("Error retrieving views")
      });
    }
  
    // To update Report variant changes
    stateUpdate = async () => {
      const state = this.pivotGrid1.getDataSource()?.state();
      let temp = state
      localStorage.setItem(this.storageKey, state);
      if (typeof temp == "string") temp = JSON.parse(temp)
      let field_conf = [{
  
        showDataFields: this.showDataFields,
        showRowFields: this.showRowFields,
        showColumnFields: this.showColumnFields,
        showFilterFields: this.showFilterFields,
        showRowGrandTotals: this.showRowGrandTotals,
        showColumnGrandTotals: this.showColumnGrandTotals,
        showSubTotal: this.showSubTotal
        
      }];
      console.log(temp)
      if (temp && this.views && this.views.allViews.length > 0) {
        this._revenueService.updateReportState(temp, this.views.allViews[this.currentView].customization_id, field_conf, this.applicationId).subscribe(res => {
          console.log(res);
  
          this.enableDisplayView();
          this.snackBar.open("Report Version - " + this.views.allViews[this.currentView].config_name + " was updated Successfully!", "Dismiss", { duration: 2000 });
          this.getConfigData();
  
        }, err => {
          this.snackBar.open("Unable to Update the Current Report Version! Try Again", "Dismiss", { duration: 2000 });
          console.log(err)
        })
      }
  
      else {
        this.snackBar.open("No Report Versions Found.Kindly Use SaveAs!", "Dismiss", { duration: 2000 });
      }
    }

  
    // Save report variant 
    saveState() {
      // console.log("Here")
      const state = this.pivotGrid1.getDataSource()?.state();
      let temp = state
      localStorage.setItem(this.storageKey, state);
      // console.log("Here")
      // console.log(temp)
      // console.log(typeof (this.currentState));
      if (typeof temp == "string") temp = JSON.parse(temp)
      // console.log(this.sundayGovService.value)
      let field_conf = [{
        showDataFields: this.showDataFields,
        showRowFields: this.showRowFields,
        showColumnFields: this.showColumnFields,
        showFilterFields: this.showFilterFields,
        showRowGrandTotals: this.showRowGrandTotals,
        showColumnGrandTotals: this.showColumnGrandTotals,
        showSubTotal: this.showSubTotal
      }];
      console.log(this.applicationId)
      this._revenueService.saveReportState(temp, this.versionName.value, field_conf, this.applicationId).subscribe(res => {
        console.log(res);
  
        this.enableDisplayView();
        this.snackBar.open("Report Version - " + this.versionName.value + " was created Successfully!", "Dismiss", { duration: 2000 });
        this.getConfigData();
      }, err => {
  
        this.snackBar.open("Unable to create the Report Version! Try Again", "Dismiss", { duration: 2000 });
        console.log(err)
      })
  
    }
  
    // To change report variant
    changeView(index) {
      this.currentView = index;
      console.log(index)
      localStorage.setItem(this.storageKey, this.views.allViews[index].saved_config);
      let temp = localStorage.getItem(this.storageKey);
      console.log("Here")
      console.log(temp)
      this.pivotGrid1.getDataSource().state(JSON.parse(this.views.allViews[index].saved_config));
      this.setFieldChoosers(JSON.parse(this.views.allViews[index].field_config)[0])
      this.enableDisplayView();
    }
  
    // To delete variant
    deleteVariant(index) {
      let name = this.views.allViews[index].config_name;
      this.confirmSweetAlert("Do you want to Delete " + name + " Variant?").then((deleteConfirm) => {
        if (deleteConfirm.value) {
          console.log(deleteConfirm.value)
          this._revenueService.deleteVariant(this.applicationId, this.views.allViews[index].customization_id).subscribe(res => {
            this.getConfigData();
            this.snackBar.open("Variant " + name + " was Deleted Succesfully", "Dismiss", { duration: 2000 });
  
          }, err => {
            this.snackBar.open("Failed to delete Variant " + name + ".", "Dismiss", { duration: 2000 })
          })
        }
      })
    }
  
    // Show Data fields
    showDataFieldsFn() {
      this.showDataFields = !this.showDataFields;
      if (this.showDataFields == true) {
  
        this.snackBar.open("Displaying Data Fields!", "Dismiss", { duration: 1000 });
      } else {
        this.snackBar.open("Data Fields Hidden!", "Dismiss", { duration: 1000 });
      }
      this.editBadgeInFields();
    };
  
    // Show Row Fields
    showRowFieldsFn() {
      this.showRowFields = !this.showRowFields;
      if (this.showRowFields == true) {
  
        this.snackBar.open("Displaying Row Fields!", "Dismiss", { duration: 1000 });
      } else {
        this.snackBar.open("Row Fields Hidden!", "Dismiss", { duration: 1000 });
      }
      this.editBadgeInFields();
    };
  
    // Show Column Fields
    showColumnFieldsFn() {
      this.showColumnFields = !this.showColumnFields;
      if (this.showColumnFields == true) {
  
        this.snackBar.open("Displaying Column Fields!", "Dismiss", { duration: 1000 });
      } else {
        this.snackBar.open("Column Fields Hidden!", "Dismiss", { duration: 1000 });
      }
      this.editBadgeInFields();
    };
  
    // Show Filter Fields
    showFilterFieldsFn() {
      this.showFilterFields = !this.showFilterFields;
      if (this.showFilterFields == true) {
  
        this.snackBar.open("Displaying Filter Fields!", "Dismiss", { duration: 1000 });
      } else {
        this.snackBar.open("Filter Fields Hidden!", "Dismiss", { duration: 1000 });
      }
      this.editBadgeInFields();
    };

    editBadgeInFields(){
      if(!this.showDataFields || !this.showRowFields || !this.showColumnFields || !this.showFilterFields){
        this.displayView = false;
      }
      else{
        this.displayView = true;
      }
    }
  
    // Toggle Edit 
    toggleEditView() {
      this.displayView = !this.displayView;
    }
  
    // To enable display view
    enableDisplayView() {
      this.displayView = true;
    }
  
    scrollRight() {
      const scrollOffset = this.cardScroll.nativeElement.offsetWidth;
      this.cardScroll.nativeElement.scrollTo({
        left: this.cardScroll.nativeElement.scrollLeft + scrollOffset,
        behavior: "smooth",
      });
    }
    
    scrollLeft() {
      const scrollOffset = this.cardScroll.nativeElement.offsetWidth;
      this.cardScroll.nativeElement.scrollTo({
        left: this.cardScroll.nativeElement.scrollLeft - scrollOffset,
        behavior: "smooth",
      });
    }
    
  
    // Sweet alert
    confirmSweetAlert(title) {
      return sweetAlert.fire({
        customClass: {
          title: "title-class",
          confirmButton: "confirm-button-class",
          cancelButton: "confirm-button-class"
        },
        title: title,
        // text: text,
        icon: "warning",
        showConfirmButton: true,
        showCancelButton: true
      })
    }
  
    // Set Field Choosers
    setFieldChoosers(fieldVisibiltyData) {
      console.log(fieldVisibiltyData)
      this.showDataFields = fieldVisibiltyData.showDataFields;
      this.showRowFields = fieldVisibiltyData.showRowFields;
      this.showColumnFields = fieldVisibiltyData.showColumnFields;
      this.showFilterFields = fieldVisibiltyData.showFilterFields;
      this.showRowGrandTotals = fieldVisibiltyData.showRowGrandTotals;
      this.showColumnGrandTotals = fieldVisibiltyData.showColumnGrandTotals;
      this.showSubTotal = fieldVisibiltyData.showSubTotal;
    }
  
    // Reset Field Choosers
    resetFieldChoosers() {
      this.showDataFields = false;
      this.showRowFields = false;
      this.showColumnFields = false;
      this.showFilterFields = false;
    }
  
    // To format value based on currency code 
    onCellPrepared(e) {
      console.log("e")
      console.log(e)
      if (e.area == "data") {
        if (e.cell.value) {
          if (_.contains(e.cell.columnPath, "INR")) {
            let text = "₹ " + new Intl.NumberFormat('en-IN', {}).format(e.cell.value)
            e.cellElement.setAttribute('title', text);
            e.cellElement.innerHTML = text;
          } else if ((_.contains(e.cell.columnPath, "USD"))) {
            let text = "$ " + new Intl.NumberFormat('en-US', {}).format(e.cell.value)
            e.cellElement.setAttribute('title', text);
            e.cellElement.innerHTML = text;
          } else {
            if (this.defaultCurrency == "INR") {
              let text = "₹ " + new Intl.NumberFormat('en-IN', {}).format(e.cell.value)
              e.cellElement.setAttribute('title', text);
              e.cellElement.innerHTML = text;
            } else if (this.defaultCurrency == "USD") {
              let text = "$ " + new Intl.NumberFormat('en-US', {}).format(e.cell.value)
              e.cellElement.setAttribute('title', text);
              e.cellElement.innerHTML = text;
  
            }
          }
  
        }
      }
  
    }

  async reportAuthorizationValidation() {
    let roleAccessFilters = await this._revenueService.reportAuthorizationValidation();
    this.isAuthorized = true;
    if (roleAccessFilters['messType'] != "S") {
      this.isAuthorized = false;
      this._router.navigateByUrl("/main/reports")
      return this.snackBar.open(roleAccessFilters['messText'], 'Dismiss', { duration: 2000 })
    }
  }

  async getReportColumnConfig() {
    let getRevenueColumn = await this._revenueService.getReportColumnConfig();
    this.fields = getRevenueColumn["data"]
    if(this.fields?.length == 0){
      this.fields = [
        {
          "area" : "filter",
          "caption" : "Legal Entity",
          "dataField" : "entity_name",
          "width" : 120
        },
        {
          "area" : "filter",
          "caption" : "Sales Region",
          "dataField" : "sales_region",
          "width" : 120
        },
        {
          "area" : "row",
          "caption" : "Cluster",
          "dataField" : "cluster",
          "width" : 120
        },
        {
          "area" : "filter",
          "caption" : "Project Name",
          "dataField" : "project_name",
          "width" : 120
        },
        {
          "area" : "filter",
          "caption" : "Subdivision",
          "dataField" : "sub_division_name",
          "width" : 120
        },
        {
          "area" : "filter",
          "caption" : "Division",
          "dataField" : "division_name",
          "width" : 120
        },
        {
          "area" : "filter",
          "caption" : "Employee",
          "dataField" : "associate_name",
          "width" : 120
        },
        {
          "area" : "filter",
          "caption" : "Position",
          "dataField" : "position_name",
          "width" : 120
        },
        {
          "area" : "filter",
          "caption" : "Milestone Description",
          "dataField" : "milestone_name",
          "width" : 120
        },
        {
          "area" : "filter",
          "caption" : "Type",
          "dataField" : "type",
          "width" : 120
        },
        {
          "area" : "column",
          "caption" : "Year",
          "dataField" : "posting_date",
          "dataType" : "date",
          "expanded" : true,
          "groupInterval" : "year"
        },
        {
          "area" : "column",
          "caption" : "Month",
          "dataField" : "posting_date",
          "dataType" : "date",
          "groupInterval" : "month"
        },
        {
          "area" : "filter",
          "caption" : "Revenue In Project Currency",
          "dataField" : "revenue",
          "dataType" : "number",
          "format" : 
          {
            "precision" : 2,
            "type" : "fixedPoint"
          },
          "summaryType" : "sum",
          "width" : 120
        },
        {
          "area" : "filter",
          "caption" : "Project Currency",
          "dataField" : "currency",
          "dataType" : "string",
          "width" : 120
        },
        {
          "area" : "data",
          "caption" : "${reporting_currency_1}",
          "dataField" : "reporting_currency_1_value",
          "dataType" : "number",
          "format" : 
          {
            "currency" : "reporting_currency_1",
            "precision" : 2,
            "type" : "currency"
          },
          "summaryType" : "sum"
        },
        {
          "area" : "filter",
          "caption" : "${reporting_currency_2}",
          "dataField" : "reporting_currency_2_value",
          "dataType" : "number",
          "format" : 
          {
            "currency" : "reporting_currency_2",
            "precision" : 2,
            "type" : "currency"
          },
          "summaryType" : "sum"
        },
        {
          "area" : "filter",
          "caption" : "Position Id",
          "dataField" : "position_id",
          "width" : 120
        },
        {
          "area" : "filter",
          "caption" : "Sow Reference",
          "dataField" : "sow_reference_number",
          "width" : 120
        },
        {
          "area" : "filter",
          "caption" : "Project Code",
          "dataField" : "project_code",
          "expanded" : false,
          "width" : 120
        },
        {
          "area" : "filter",
          "caption" : "Uploaded Revenue",
          "dataField" : "is_from_upload_status",
          "width" : 120
        },
        {
          "area" : "filter",
          "caption" : "Project Type",
          "dataField" : "project_type",
          "width" : 120
        },
        {
          "area" : "filter",
          "caption" : "Hours",
          "dataField" : "hours",
          "dataType" : "number",
          "format" : 
          {
            "precision" : 2,
            "type" : "fixedPoint"
          },
          "summaryType" : "sum",
          "width" : 120
        },
        {
          "area" : "filter",
          "caption" : "Parent Customer",
          "dataField" : "parent_customer",
          "width" : 120
        },
        {
          "area" : "row",
          "caption" : "Customer",
          "dataField" : "customer_name",
          "width" : 120
        },
        {
          "dataField" : "year",
          "visible" : false
        },
        {
          "dataField" : "month",
          "visible" : false
        },
        {
          "area" : "filter",
          "caption" : "Employment Type",
          "dataField" : "employment_type",
          "width" : 120
        },
        {
          "dataField" : "reporting_currency_1",
          "visible" : false
        },
        {
          "dataField" : "reporting_currency_2",
          "visible" : false
        }
      ]
    }
  }
    
}


