import { Component, ViewChild, HostListener, ElementRef, OnInit } from '@angular/core';
import { RevenueReportV2Service } from "../services/revenue-report-v2.service";
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatDialog } from '@angular/material/dialog';
import * as _ from "underscore";
import { NgxSpinnerService } from 'ngx-spinner';
import * as moment from 'moment';
import { FormControl, FormBuilder, Validators, FormGroup } from "@angular/forms";
import { UtilityService } from 'src/app/services/utility/utility.service';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core';
import { MomentDateAdapter, MAT_MOMENT_DATE_ADAPTER_OPTIONS } from '@angular/material-moment-adapter';
import { MatSidenav } from '@angular/material/sidenav';
import PivotGridDataSource from "devextreme/ui/pivot_grid/data_source";
import { DxPivotGridComponent } from "devextreme-angular";
import { ChangeDetectorRef } from '@angular/core';
import sweetAlert from 'sweetalert2';
import { DaterangepickerDirective } from 'ngx-daterangepicker-material';
import { MatStartDate } from '@angular/material/datepicker';
import { DxButtonModule, DxButtonComponent } from 'devextreme-angular';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'app-revenue-report-landing-page',
  templateUrl: './revenue-report-landing-page.component.html',
  styleUrls: ['./revenue-report-landing-page.component.scss'],
  providers: [
    {
      provide: DateAdapter,
      useClass: MomentDateAdapter,
      deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS],
    },
    {
      provide: MAT_DATE_FORMATS,
      useValue: {
        parse: {
          dateInput: 'DD-MM-YYYY',
        },
        display: {
          dateInput: 'DD-MM-YYYY',
          monthYearLabel: 'MMM YYYY',
        },
      },
    },
  ],
})


export class RevenueReportLandingPageComponent implements OnInit {
  @ViewChild(DaterangepickerDirective, { static: false }) pickerDirective: DaterangepickerDirective;
  @ViewChild(DxPivotGridComponent) pivotGrid: DxPivotGridComponent;
  @ViewChild("cardScroll", { read: ElementRef })
  public cardScroll: ElementRef<any>;

  dataSource: any;
  allowSearch: any;
  filters: string;
  fyEndDate: any;
  fyStartDate: any;
  storageKey: any;
  applicationId = 3011;
  showDataFields: boolean = true;
  showRowFields: boolean = true;
  showColumnFields: boolean = true;
  showFilterFields: boolean = true;
  displayView: boolean = true;
  visibleDeleteView: any;
  currentView: any;
  views: any;
  columnConfig: any;
  showSubTotal = true;
  showColumnGrandTotal = true;
  showRowGrandTotal = true;
  downloadFileName: any;
  report_id: any;
  report_name: any;
  versionName = new FormControl("", [Validators.required]);
  isVariantSidenavOpened = false;
  pivotGrid1: any;
  reports: any;
  variant_length: any;
  reportLoader: any;
  applied_duration: { startDate: any; endDate: any };
  dateRangePickerRanges : any;
  defaultCurrency: any;
  freezePeriod: any = "";
  roleAccessFilters: any = "";
  fileName: any;
  localState: any;
  previousFyEndDate : any;
  previousFyStartDate : any;
  nextFyStartDate : any;
  nextFyEndDate : any;
  isAuthorized: boolean = true;
  constructor(private _revenueReports: RevenueReportV2Service, private snackBar: MatSnackBar, private fb: FormBuilder,
    private dialog: MatDialog, private spinner: NgxSpinnerService, private _util: UtilityService,
    private cdr: ChangeDetectorRef, private _router: Router) {
    }

  async ngOnInit() {
    await this.revenueForecastAuthorizationValidation()
    await this.getDefaultFyData();
    await this.initializeDateRanges(); 
    await this.getRevenueReportListData();
    this.getConfigData();
    await this.setDuration();
    this.selectReport(null, 0);
    this.calculateDynamicStyle();
  }

  isSidenavOpened = false;
  searchValue: string = '';

  async getRevenueReportListData() {
    if (this.isAuthorized){
    let reportData = await this._revenueReports.getRevenueReportListData();
    if (reportData["messType"] == "S") {
      this.reports = reportData["data"];
    }
  }
  }

  async revenueForecastAuthorizationValidation(){
    this.roleAccessFilters = await this._revenueReports.revenueForecastAuthorizationValidation();
    this.isAuthorized = true;
    console.log("NOAUTH")
    console.log(this.roleAccessFilters)
    console.log(this.isAuthorized)
    if (this.roleAccessFilters.messType != "S"){
      console.log("inside NOAUTH")
      this.isAuthorized = false;
      this._router.navigateByUrl("/main/reports")
      return this.snackBar.open(this.roleAccessFilters.messText, 'Dismiss', { duration: 2000 })
    }
  }

  async getDefaultFyData() {
    let financialYearData = await this._revenueReports.getDefaultFyData();
    if (financialYearData["messType"] == "S") {
      this.fyStartDate = financialYearData["data"]["fy_start_date"];
      this.fyEndDate = financialYearData["data"]["fy_end_date"];
      this.previousFyStartDate = financialYearData["data"]["previous_fy_start_date"];
      this.previousFyEndDate = financialYearData["data"]["previous_fy_end_date"];
      this.nextFyStartDate = financialYearData["data"]["next_fy_start_date"];
      this.nextFyEndDate = financialYearData["data"]["next_fy_end_date"];
    }
  }

  async initializeDateRanges() {
    this.dateRangePickerRanges = {
      'This Month': [moment().startOf('month'), moment().endOf('month')],
      'Last Month': [
        moment().subtract(1, 'month').startOf('month'),
        moment().subtract(1, 'month').endOf('month'),
      ],
      'Next Month': [
        moment().add(1, 'month').startOf('month'),
        moment().add(1, 'month').endOf('month'),
      ],
      'Upcoming 3 Months': [
        moment().startOf('month'),
        moment().add(2, 'month').endOf('month'),
      ],
      'This Year': [moment().startOf('year'), moment().endOf('year')],
      'Previous Year': [
        moment().subtract(1, 'year').startOf('year'),
        moment().subtract(1, 'year').endOf('year'),
      ],
      'Next Year' : [
        moment().add(1, 'year').startOf('year'),
        moment().add(1, 'year').endOf('year'),
      ],
      'Current FY': [
        this.fyStartDate,
        this.fyEndDate
      ],
      'Previous FY': [
        this.previousFyStartDate,
        this.previousFyEndDate
      ],
      'Next FY': [
        this.nextFyStartDate,
        this.nextFyEndDate
      ],
    };
  }


  get filteredReports() {
    if (!this.searchValue) {
      return this.reports;
    }
    return this.reports.filter(report =>
      report.name.toLowerCase().includes(this.searchValue.toLowerCase())
    );
  }


  selectedReportId: any;
  data: any;
  async selectReport(id?: number, fromUI?: any) {
    if (this.isAuthorized){
    if (fromUI == 1) {
      this.report_id = id;
      this.selectedReportId = id;
      for (const r of this.reports) {
        if (r.id == id) {
          this.data = r;
          break;
        }
      }
      this.report_name = this.data.name;
      this.applicationId = this.data.application_id;
      this.storageKey = this.data.storageKey;
      localStorage.setItem("selectedReportId", this.selectedReportId)
      await this.setDuration();
      this.getRevenueData();
    }
    else {
      this.report_id = localStorage.getItem("selectedReportId") ? localStorage.getItem("selectedReportId") : 1;
      this.selectedReportId = localStorage.getItem("selectedReportId") ? localStorage.getItem("selectedReportId") : 1;

      for (const r of this.reports) {
        if (r.id == this.report_id) {
          this.data = r;
          break;
        }
      }

      this.report_name = this.data.name;
      this.applicationId = this.data.application_id;
      this.storageKey = this.data.storageKey;
      await this.setDuration();
      this.getRevenueData();
    }
  }
  }


  @HostListener('window:resize')
  onResize() {
    this.calculateDynamicStyle();
  }


  toggleSidenav() {
    this.isSidenavOpened = !this.isSidenavOpened;
  }

  toggleVariantSidenav() {
    this.isVariantSidenavOpened = !this.isVariantSidenavOpened;
  }

  backToReports() {
    this._router.navigateByUrl("/main/reports")
  }

  // async onDateInputChange(event) {
  //   let selectedDate = event;
  //   const newEndDateValue = selectedDate.endDate._d;
  //   const newStartDateValue = selectedDate.startDate._d;
  //   const newendDateValue = moment(newEndDateValue).format('YYYY-MM-DD');
  //   const newstartDateValue = moment(newStartDateValue).format('YYYY-MM-DD');
     
  //   this.applied_duration = {
  //     startDate: newstartDateValue,
  //     endDate: newendDateValue,
  //   };

  //   localStorage.setItem("revenue_forecast_start_date", newstartDateValue)
  //   localStorage.setItem("revenue_forecast_end_date", newendDateValue)

  //   this.spinner.show();
  //   await this.getRevenueData();
  //   this.spinner.hide();
  // }
  async onDateInputChange(event) {
    // Assuming the event contains the selected dates
    let selectedDate = event;
    const newEndDateValue = selectedDate.endDate._d;
    const newStartDateValue = selectedDate.startDate._d;

    // Formatting dates to 'YYYY-MM-DD'
    const newendDateValue = moment(newEndDateValue).format('YYYY-MM-DD');
    const newstartDateValue = moment(newStartDateValue).format('YYYY-MM-DD');
     
    // Step 1: Retrieve existing data from localStorage
    let existingData = JSON.parse(localStorage.getItem('revenue_forecast_date_details')) || {};

    // Ensure `this.report_id` is valid
    if (!this.report_id) {
        console.error("Report ID is undefined or invalid.");
        return;
    }

    // Step 2: Check if the reportID exists in the stored data
    console.log("onDateInputChange");
    console.log("existingData:", existingData);
    console.log("existingData[this.report_id]:", existingData[this.report_id]);

    if (!existingData[this.report_id]) {
        // If the reportID is not present, create a new entry for this reportID
        existingData[this.report_id] = {
            revenue_forecast_start_date: newstartDateValue, // Use formatted date values
            revenue_forecast_end_date: newendDateValue
        };
    } else {
        // If the reportID exists, update its values
        existingData[this.report_id].revenue_forecast_start_date = newstartDateValue;
        existingData[this.report_id].revenue_forecast_end_date = newendDateValue;
    }

    // Step 3: Save the updated data back to localStorage
    localStorage.setItem('revenue_forecast_date_details', JSON.stringify(existingData));

    // Update the applied duration in the current component or service
    this.applied_duration = {
        startDate: newstartDateValue,
        endDate: newendDateValue,
    };

    // Show spinner during data retrieval
    this.spinner.show();
    await this.getRevenueData();  // Fetch revenue data asynchronously
    this.spinner.hide();
}


  /**
 * @description For Setting the Default Date Range from the Config
 */
  setDuration() {
    console.log("inside set duration")
    // Step 1: Retrieve the stored data for all reports from localStorage
    let storedData = JSON.parse(localStorage.getItem('revenue_forecast_date_details')) || {};

    // Step 2: Check if the specific report_id exists in the stored data
    if (storedData[this.report_id]) {
        // Step 3: Get start and end dates for the specific report
        let storedStartDate = storedData[this.report_id].revenue_forecast_start_date;
        let storedEndDate = storedData[this.report_id].revenue_forecast_end_date;

        // Step 4: Apply the retrieved dates if they exist
        if (storedStartDate && storedEndDate) {
            this.applied_duration = {
                startDate: storedStartDate,
                endDate: storedEndDate
            };
        } 
    } else {
        // If no data is found, use the fiscal year start and end dates
        const endDate = this.fyEndDate;
        const startDate = this.fyStartDate;
        this.applied_duration = {
            startDate: startDate,
            endDate: endDate
        };
    }
}


  openDatePicker() {
    console.log('Picker called')
    console.log(this.pickerDirective)
    this.pickerDirective.open();
  }

  getDateFormat(date) {
    if (date) {
      return moment(date).format('DD MMM YYYY')
    }
  }

  revenueData: any = [];

  async getRevenueData() {
    this.revenueData = [];
    let start_date = this.applied_duration.startDate;
    let end_date = this.applied_duration.endDate;
    this.spinner.show();
    this.localState = localStorage.getItem(this.storageKey);
    let getRevenueData = await this._revenueReports.getRevenueData(start_date, end_date, this.report_id);
    if (getRevenueData["messType"] == "S") {
      this.spinner.hide();
      this.revenueData = getRevenueData["data"]["revenue_data"];
      if(this.report_id == 3){
        this.columnConfig = getRevenueData["data"]["column_config"] 
        this.columnConfig.push(
          {
              caption: 'Variance',
              dataType: 'number',
              format: 'currency',
              area: 'data',
              calculateSummaryValue: function (summaryCell) {
                  const prevCell = summaryCell.prev('column', true);
                  const prevVal = prevCell ? prevCell.value("USD") || 0 : 0; // Default to 0 if undefined
                  const currentVal = summaryCell.value("USD") || 0; // Default to 0 if undefined
      
                  return currentVal - prevVal; // Calculate variance
              },
          },
          {
              caption: 'Variance %',
              dataType: 'number',
              format: {
                  type: 'percent',
                  precision: 0
              },
              area: 'data',
              calculateSummaryValue: function (summaryCell) {
                  const prevCell = summaryCell.prev('column', true);
                  const prevVal = prevCell ? prevCell.value("USD") || 0 : 0; // Default to 0 if undefined
                  const currentVal = summaryCell.value("USD") || 0; // Default to 0 if undefined
      
                  if (prevVal !== 0) {
                      let variance = currentVal - prevVal;
                      return variance / prevVal; // Calculate variance %
                  }
                  return 0; // Return 0 when prevVal is 0
              },
          }
      )        
      }
      else{
        this.columnConfig = getRevenueData["data"]["column_config"];
      }
      this.showSubTotal = getRevenueData["data"]["show_sub_total"] ? true : false;
      this.showRowGrandTotal = getRevenueData["data"]["show_row_grand_total"] ? true : false;
      this.showColumnGrandTotal = getRevenueData["data"]["show_column_grand_total"] ? true : false;
      this.downloadFileName = getRevenueData["data"]["download_file_name"];
      await this.fetchRevenueActualsReport(this.revenueData);
      this.getConfigData();
    }
  }


  fetchRevenueActualsReport = async (revenueData) => {

    this.dataSource = new PivotGridDataSource({
      fields: this.columnConfig,
      store: this.revenueData
    });
  };

  onInitialized(e) {
    this.pivotGrid1 = e.component;
  }

  refresh() {
    this.getRevenueData();
  }

  reset() {
    this.pivotGrid1.getDataSource().state({});
  }

  async getConfigData() {
    await this._revenueReports.getReportUserViews(this.applicationId).subscribe(res => {
      this.views = res;
      this.variant_length = this.views.allViews.length;
      for (let i = 0; i < this.views.allViews.length; i++) {
        this.views.allViews[i].visibleDeleteView = false;
      }

      if (this.views.activeView.length > 0 && this.views) {

        this.enableDisplayView();

        this.currentView = 0;
        localStorage.setItem(this.storageKey, this.views.activeView[0].saved_config);
        this.pivotGrid1.getDataSource().state(JSON.parse(this.views.activeView[0].saved_config))
        if (this.views.activeView[0].field_config.length > 0)
          this.setFieldChoosers(JSON.parse(this.views.activeView[0].field_config)[0])
        else {
          let field_conf = {

            showDataFields: true,
            showRowFields: true,
            showColumnFields: true,
            showFilterFields: true
          };
          this.setFieldChoosers(field_conf)
        }
      }
      else if (this.views.activeView.length == 0 && this.views.allViews.length && this.views) {
        this.enableDisplayView();
        this.currentView = 0;
        localStorage.setItem(this.storageKey, this.views.allViews[0].saved_config);
        this.pivotGrid1.getDataSource().state(JSON.parse(this.views.allViews[0].saved_config))
        if (this.views.activeView[0].field_config.length > 0)
          this.setFieldChoosers(JSON.parse(this.views.allViews[0].field_config)[0])
        else {
          let field_conf = {

            showDataFields: true,
            showRowFields: true,
            showColumnFields: true,
            showFilterFields: true
          };
          this.setFieldChoosers(field_conf)
        }
      }
      else {
        localStorage.setItem(this.storageKey, this.localState);
        this.pivotGrid1.getDataSource().state(this.localState)
      }
    }, err => {

      this.enableDisplayView();
      this.snackBar.open("Error Retrieving Report Views! Try Refreshing", "Dismiss", { duration: 2000 });
    });
  }



  calculateDynamicStyle() {
    let dynamicHeight = window.innerHeight - 65 + 'px';
    let dynamicInnerHeight = window.innerHeight - 135 + 'px';
    document.documentElement.style.setProperty(
      '--pageContentHeight',
      dynamicHeight
    );
    document.documentElement.style.setProperty(
      '--innerTabHeight',
      dynamicInnerHeight
    );
  }


  stateUpdate = async () => {
    let temp = this.pivotGrid1.getDataSource()?.state();
    if (typeof temp == "string") temp = JSON.parse(temp)
    let field_conf = [{

      showDataFields: this.showDataFields,
      showRowFields: this.showRowFields,
      showColumnFields: this.showColumnFields,
      showFilterFields: this.showFilterFields
    }];
    console.log(temp)
    if (temp && this.views && this.views.allViews.length > 0) {
      this._revenueReports.updateReportState(temp, this.views.allViews[this.currentView].customization_id, field_conf, this.applicationId).subscribe(res => {
        console.log(res);

        this.enableDisplayView();
        this.snackBar.open("Report Version - " + this.views.allViews[this.currentView].config_name + " was updated Successfully!", "Dismiss", { duration: 2000 });
        this.getConfigData();

      }, err => {
        this.snackBar.open("Unable to Update the Current Report Version! Try Again", "Dismiss", { duration: 2000 });
        console.log(err)
      })
    }

    else {
      this.snackBar.open("No Report Versions Found.Kindly Use SaveAs!", "Dismiss", { duration: 2000 });
    }
  }


  // Save report variant 
  saveState() {
    const state = this.pivotGrid1.getDataSource()?.state();
    let temp = state
    localStorage.setItem(this.storageKey, state);

    if (typeof temp == "string") temp = JSON.parse(temp)
    let field_conf = [{

      showDataFields: this.showDataFields,
      showRowFields: this.showRowFields,
      showColumnFields: this.showColumnFields,
      showFilterFields: this.showFilterFields
    }];
    console.log(this.applicationId)
    this._revenueReports.saveReportState(temp, this.versionName.value, field_conf, this.applicationId).subscribe(res => {
      console.log(res);

      this.enableDisplayView();
      this.snackBar.open("Report Version created Successfully!", "Dismiss", { duration: 2000 });
      this.getConfigData();
    }, err => {

      this.snackBar.open("Unable to create the Report Version! Try Again", "Dismiss", { duration: 2000 });
      console.log(err)
    })

    this.versionName.reset();

  }

  // To change report variant
  changeView(index) {
    this.currentView = index;
    console.log(index)
    localStorage.setItem(this.storageKey, this.views.allViews[index].saved_config);
    let temp = localStorage.getItem(this.storageKey);
    console.log("Here")
    console.log(temp)
    this.pivotGrid1.getDataSource().state(JSON.parse(this.views.allViews[index].saved_config));
    this.setFieldChoosers(JSON.parse(this.views.allViews[index].field_config)[0])
    this.enableDisplayView();
  }

  // To delete variant
  deleteVariant(index) {
    let name = this.views.allViews[index].config_name;
    this.confirmSweetAlert("Do you want to Delete " + name + " Variant?").then((deleteConfirm) => {
      if (deleteConfirm.value) {
        console.log(deleteConfirm.value)
        this._revenueReports.deleteVariant(this.applicationId, this.views.allViews[index].customization_id).subscribe(res => {
          this.getConfigData();
          this.snackBar.open("Variant " + name + " was Deleted Succesfully", "Dismiss", { duration: 2000 });

        }, err => {
          this.snackBar.open("Failed to delete Variant " + name + ".", "Dismiss", { duration: 2000 })
        })
      }
    })
  }

  // Show Data fields
  showDataFieldsFn() {
    this.showDataFields = !this.showDataFields;
    if (this.showDataFields == true) {

      this.snackBar.open("Displaying Data Fields!", "Dismiss", { duration: 1000 });
    } else {
      this.snackBar.open("Data Fields Hidden!", "Dismiss", { duration: 1000 });
    }
  };

  // Show Row Fields
  showRowFieldsFn() {
    this.showRowFields = !this.showRowFields;
    if (this.showRowFields == true) {

      this.snackBar.open("Displaying Row Fields!", "Dismiss", { duration: 1000 });
    } else {
      this.snackBar.open("Row Fields Hidden!", "Dismiss", { duration: 1000 });
    }
  };

  // Show Column Fields
  showColumnFieldsFn() {
    this.showColumnFields = !this.showColumnFields;
    if (this.showColumnFields == true) {

      this.snackBar.open("Displaying Column Fields!", "Dismiss", { duration: 1000 });
    } else {
      this.snackBar.open("Column Fields Hidden!", "Dismiss", { duration: 1000 });
    }
  };

  // Show Filter Fields
  showFilterFieldsFn() {
    this.showFilterFields = !this.showFilterFields;
    if (this.showFilterFields == true) {

      this.snackBar.open("Displaying Filter Fields!", "Dismiss", { duration: 1000 });
    } else {
      this.snackBar.open("Filter Fields Hidden!", "Dismiss", { duration: 1000 });
    }
  };

  // Toggle Edit 
  toggleEditView() {
    this.displayView = !this.displayView;
  }

  // To enable display view
  enableDisplayView() {
    this.displayView = true;
  }

  // Sweet alert
  confirmSweetAlert(title) {
    return sweetAlert.fire({
      customClass: {
        title: "title-class",
        confirmButton: "confirm-button-class",
        cancelButton: "confirm-button-class"
      },
      title: title,
      // text: text,
      icon: "warning",
      showConfirmButton: true,
      showCancelButton: true
    })
  }

  // Set Field Choosers
  setFieldChoosers(fieldVisibiltyData) {
    console.log(fieldVisibiltyData)
    this.showDataFields = fieldVisibiltyData.showDataFields;
    this.showRowFields = fieldVisibiltyData.showRowFields;
    this.showColumnFields = fieldVisibiltyData.showColumnFields;
    this.showFilterFields = fieldVisibiltyData.showFilterFields;
  }

  // Reset Field Choosers
  resetFieldChoosers() {
    this.showDataFields = false;
    this.showRowFields = false;
    this.showColumnFields = false;
    this.showFilterFields = false;
  }




  // To format value based on currency code 
  onCellPrepared(e) {
    console.log("e")
    console.log(e)
    if (e.area == "data") {
      if (e.cell.value) {
        if (_.contains(e.cell.columnPath, "INR")) {
          let text = "₹ " + new Intl.NumberFormat('en-IN', {}).format(e.cell.value)
          e.cellElement.setAttribute('title', text);
          e.cellElement.innerHTML = text;
        } else if ((_.contains(e.cell.columnPath, "USD"))) {
          let text = "$ " + new Intl.NumberFormat('en-US', {}).format(e.cell.value)
          e.cellElement.setAttribute('title', text);
          e.cellElement.innerHTML = text;
        } else {
          if (this.defaultCurrency == "INR") {
            let text = "₹ " + new Intl.NumberFormat('en-IN', {}).format(e.cell.value)
            e.cellElement.setAttribute('title', text);
            e.cellElement.innerHTML = text;
          } else if (this.defaultCurrency == "USD") {
            let text = "$ " + new Intl.NumberFormat('en-US', {}).format(e.cell.value)
            e.cellElement.setAttribute('title', text);
            e.cellElement.innerHTML = text;

          }
        }

      }
    }

  }

  onResetButtonClick() {
    this.dataSource.state({});
  }
}
