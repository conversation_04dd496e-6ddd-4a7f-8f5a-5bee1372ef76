<div class="report-data-grid-styles">

    

    <div class="report-table-view" style=" overflow: auto" [hidden]="hideLoading">
      

            <div class="sticky-card fixed-card" style="width: 20%; height: 100%;">
        
              <div class="row row-header outer-border title-card-display" style="font-size:12px;">
                {{ groupBySelectedValue === 'Position' ? 'Position/Activity' : groupBySelectedValue }}
              </div>
        
              <ng-container *ngFor="let project of projectData">
                <div class="row row-header outer-border justify-content-center title-display" (click)="openNextLevel(project.id, project.title)" *ngIf="this.groupBySelectedValue !='Position'">
                  
             
                  <div class="col p-0 sticky-content-wrap title-content-display" >
                    {{project.title}}
                  </div>
                </div>

                <div class="row row-header outer-border justify-content-center title-display"  *ngIf="this.groupBySelectedValue =='Position'">
                  
                  <div class="col p-0 sticky-content-wrap title-content-display-without-Cursor" >
                    {{project.title}}
                  </div>
          
                </div>
              </ng-container>
        
        
              <div style="height: 6px;     background: #F0F2F7">
              </div>
        
              <ng-container >
                <div class="row row-header outer-border justify-content-center total-display">
                  
                  <div class="col p-0 sticky-content-wrap total-content-display" >
                    Total (Hrs)
                  </div>
                </div>
              </ng-container>
             
        
            </div>
        
        
            <div class="data-grid-card-cell data-grid-scroll" style="width: 80%; height: 100%;">
            
              <ng-container>
        
        
                <div class="row data-grid-display">

                  <ng-container *ngIf="showTotalFront">
        
        
                    <div class="col-header-m-top p-0 mr-1 data-grid-total-border"  >
                      <div class="row d-flex mt-1 justify-content-center ">
                        <span style="font-size: 12px;">Total (Hrs)</span> 
                      </div>
        
                      <div class="row row-header" style="width: 100%">
               
                        <div class=" data-grid-header-wise  p-0 m-0">
                          <div class="row row-header data-grid-wise" style="border-top: 2px solid #F0F2F7;">
                            
                            <div class=" data-grid-cell-total-header border-right" *ngIf="('milestone_plan' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue) ">
                              {{getColumnName("milestone_plan", "Planned")}}
                            </div>
                            <div class=" data-grid-cell-total-header border-right" *ngIf="('allocated_billable_hours' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{getColumnName("allocated_billable_hours", "Allocated (B)")}}
                            </div>

                            <div class=" data-grid-cell-total-header border-right" *ngIf="('allocated_sum_billable_non_billable_flexi' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{getColumnName("allocated_sum_billable_non_billable_flexi", "Allocated (B+NBF)")}}
                            </div>

                            

                            <div class=" data-grid-cell-total-header border-right" *ngIf="('allocated_non_billable_flexi' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{getColumnName("allocated_non_billable_flexi", "Allocated (NBF)")}}
                            </div>

                            <div class=" data-grid-cell-total-header border-right" *ngIf="('allocated_non_billable_growth' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{getColumnName("allocated_non_billable_growth", "Allocated (NBG)")}} 
                            </div>
            
                            <div class=" data-grid-cell-total-header border-right" *ngIf="('allocated_non_billable_hours' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{getColumnName("allocated_non_billable_hours", "Allocated (NB)")}}
                            </div>

                            <div class=" data-grid-cell-total-header border-right" *ngIf="('allocated_hours' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{getColumnName("allocated_hours", "Allocated (T)")}}
                            </div>
                            <div class=" data-grid-cell-total-header border-right" *ngIf="('logged_billable_hours' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{getColumnName("logged_billable_hours", "Logged (B)")}}
                            </div>

                            <div class=" data-grid-cell-total-header border-right" *ngIf="('logged_sum_billable_non_billable_flexi' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{getColumnName("logged_sum_billable_non_billable_flexi", "Logged (B+NBF)")}} 
                            </div>
                          
           
                            

                            <div class=" data-grid-cell-total-header border-right" *ngIf="('logged_non_billable_flexi' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{getColumnName("logged_non_billable_flexi", "Logged (NBF)")}}
                            </div>

                            <div class=" data-grid-cell-total-header border-right" *ngIf="('logged_non_billable_growth' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{getColumnName("logged_non_billable_growth", "Logged (NBG)")}}
                            </div>

                            <div class=" data-grid-cell-total-header border-right" *ngIf="('logged_non_billable_hours' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{getColumnName("logged_non_billable_hours", "Logged (NB)")}}
                            </div>

                            <div class=" data-grid-cell-total-header border-right" *ngIf="('logged_hours' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{getColumnName("logged_hours", "Logged (T)")}}
                            </div>
                            <div class=" data-grid-cell-total-header border-right" *ngIf="('billable_hours' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{getColumnName("billed_hours", "Billed")}}
                            </div>
                           
                          
                          </div>
                        </div>
                      </div>
                      
                    
                    </div>
        
                  </ng-container>
        
                  <ng-container >
        
        
                    <div class="col-header-m-top p-0 mr-1 data-grid-outer-border" *ngFor="let month of duration" >
                      <div class="row d-flex mt-1 justify-content-center ">
                        <span style="color: #515965;font-size: 8px; font-weight: 600;">{{ month.title }}</span>
                      </div>
        
                      <div class="row row-header" >
               
                        <div class=" data-grid-header-wise  p-0 m-0">
                          <div class="row row-header data-grid-wise" style="border-top: 2px solid #F0F2F7;">
                            
                            <div class=" data-grid-cell-header border-right" *ngIf="('milestone_plan' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{this.getColumnName("milestone_plan", "Planned")}}
                            </div>
                            <div class=" data-grid-cell-header border-right" *ngIf="('allocated_billable_hours' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{getColumnName("allocated_billable_hours", "Allocated (B)")}}
                            </div>

                            <div class=" data-grid-cell-header border-right" *ngIf="('allocated_sum_billable_non_billable_flexi' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              Allocated (B+NBF)
                            </div>

                           

                            <div class=" data-grid-cell-header border-right" *ngIf="('allocated_non_billable_flexi' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{getColumnName("allocated_non_billable_flexi", "Allocated (NBF)")}}
                            </div>
                            <div class=" data-grid-cell-header border-right" *ngIf="('allocated_non_billable_growth' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{getColumnName("allocated_non_billable_growth", "Allocated (NBG)")}} 
                            </div>

                            <div class=" data-grid-cell-header border-right" *ngIf="('allocated_non_billable_hours' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{getColumnName("allocated_non_billable_hours", "Allocated (NB)")}}
                            </div>
            
                            <div class=" data-grid-cell-header border-right" *ngIf="('allocated_hours' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{getColumnName("allocated_hours", "Allocated (T)")}}
                            </div>
                            <div class=" data-grid-cell-header border-right" *ngIf="('logged_billable_hours' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{getColumnName("logged_billable_hours", "Logged (B)")}}
                            </div>

                            <div class=" data-grid-cell-header border-right" *ngIf="('logged_sum_billable_non_billable_flexi' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{getColumnName("logged_sum_billable_non_billable_flexi", "Logged (B+NBF)")}}
                            </div>
                          
                           

                            <div class=" data-grid-cell-header border-right" *ngIf="('logged_non_billable_flexi' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{getColumnName("logged_non_billable_flexi", "Logged (NBF)")}}
                            </div>
                            <div class=" data-grid-cell-header border-right" *ngIf="('logged_non_billable_growth' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{getColumnName("logged_non_billable_growth", "Logged (NBG)")}}
                            </div>
                            <div class=" data-grid-cell-header border-right" *ngIf="('logged_non_billable_hours' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{getColumnName("logged_non_billable_hours", "Logged (NB)")}}
                            </div>

                            <div class=" data-grid-cell-header border-right" *ngIf="('logged_hours' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{getColumnName("logged_hours", "Logged (T)")}}
                            </div>
                            <div class=" data-grid-cell-header border-right" *ngIf="('billable_hours' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{getColumnName("billed_hours", "Billed")}}
                            </div>
                           
                          
                          </div>
                        </div>
                      </div>
                      
                    
                    </div>
        
                  </ng-container>
        
                  
        
                  <ng-container *ngIf="!showTotalFront">
        
        
                    <div class="col-header-m-top p-0 mr-1 data-grid-total-border"  >
                      <div class="row d-flex mt-1 justify-content-center ">
                        <span style="font-size: 12px;">Total (Hrs)</span>
                      </div>
        
                      <div class="row row-header" style="width: 100%">
               
                        <div class=" data-grid-header-wise  p-0 m-0">
                          <div class="row row-header data-grid-wise" style="border-top: 2px solid #F0F2F7;">
                            
                            <div class=" data-grid-cell-total-header border-right" *ngIf="('milestone_plan' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{this.getColumnName("milestone_plan", "Planned")}}
                            </div>
                            <div class=" data-grid-cell-total-header border-right" *ngIf="('allocated_billable_hours' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{getColumnName("allocated_billable_hours", "Allocated (B)")}}
                            </div>

                            <div class=" data-grid-cell-total-header border-right" *ngIf="('allocated_sum_billable_non_billable_flexi' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              Allocated (B+NBF)
                            </div>

                           

                            <div class=" data-grid-cell-total-header border-right" *ngIf="('allocated_non_billable_flexi' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{getColumnName("allocated_non_billable_flexi", "Allocated (NBF)")}}
                            </div>

                            <div class=" data-grid-cell-total-header border-right" *ngIf="('allocated_non_billable_growth' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{getColumnName("allocated_non_billable_growth", "Allocated (NBG)")}} 
                            </div>

                            <div class=" data-grid-cell-total-header border-right" *ngIf="('allocated_non_billable_hours' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{getColumnName("allocated_non_billable_hours", "Allocated (NB)")}}
                            </div>
            
                            <div class=" data-grid-cell-total-header border-right" *ngIf="('allocated_hours' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{getColumnName("allocated_hours", "Allocated (T)")}}
                            </div>
                            <div class=" data-grid-cell-total-header border-right" *ngIf="('logged_billable_hours' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{getColumnName("logged_billable_hours", "Logged (B)")}}
                            </div>

                            
                            <div class=" data-grid-cell-total-header border-right" *ngIf="('logged_sum_billable_non_billable_flexi' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{getColumnName("logged_sum_billable_non_billable_flexi", "Logged (B+NBF)")}}
                            </div>
                          
                          

                            <div class=" data-grid-cell-total-header border-right" *ngIf="('logged_non_billable_flexi' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{getColumnName("logged_non_billable_flexi", "Logged (NBF)")}}
                            </div>

                            <div class=" data-grid-cell-total-header border-right" *ngIf="('logged_non_billable_growth' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{getColumnName("logged_non_billable_growth", "Logged (NBG)")}}
                            </div>
                            <div class=" data-grid-cell-total-header border-right" *ngIf="('logged_non_billable_hours' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{getColumnName("logged_non_billable_hours", "Logged (NB)")}}
                            </div>
                            <div class=" data-grid-cell-total-header border-right" *ngIf="('logged_hours' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{getColumnName("logged_hours", "Logged (T)")}}
                            </div>
                            <div class=" data-grid-cell-total-header border-right" *ngIf="('billable_hours' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{getColumnName("billed_hours", "Billed")}}
                            </div>
                           
                          
                          </div>
                        </div>
                      </div>
                      
                    
                    </div>
        
                  </ng-container>
                </div>
                  
                  <ng-container *ngFor="let project of projectData">
                    
                    <div class="row row-header">

                      <div class="col-header-m-top p-0 mr-1 " *ngIf="showTotalFront">
                    
        
                    
                
                        <div class=" data-grid-cell-wise  p-0 m-0"> 
                          <div class="row row-header cell-spacing" > 
                            
                            <div class=" data-grid-cell-total-data border-right border-top" *ngIf="('milestone_plan' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{'available_hours' | totaldata : project.id : projectData}}
                            </div>
                            <div class=" data-grid-cell-total-data border-right border-top" *ngIf="('allocated_billable_hours' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{'allocated_billable_hours' | totaldata : project.id : projectData}}
                            </div>

                            <div class=" data-grid-cell-total-data border-right border-top" *ngIf="('allocated_sum_billable_non_billable_flexi' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{'allocated_sum_billable_non_billable_flexi' | totaldata : project.id : projectData}}
                            </div>
                            

                            <div class=" data-grid-cell-total-data  border-right border-top" *ngIf="('allocated_non_billable_flexi' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{'allocated_non_billable_flexi' | totaldata : project.id : projectData}}
                            </div>
                            <div class=" data-grid-cell-total-data  border-right border-top" *ngIf="('allocated_non_billable_growth' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{'allocated_non_billable_growth' | totaldata : project.id : projectData}}
                            </div>
                            <div class=" data-grid-cell-total-data border-right border-top" *ngIf="('allocated_non_billable_hours' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{'allocated_non_billable_hours' | totaldata : project.id : projectData}}
                            </div>

                            <div class=" data-grid-cell-total-data border-right border-top" *ngIf="('allocated_hours' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{'allocated_hours' | totaldata : project.id : projectData}}
                            </div>

                            <div class=" data-grid-cell-total-data border-right border-top" *ngIf="('logged_billable_hours' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{'logged_billable_hours' | totaldata: project.id : projectData}}
                            </div>


                            <div class=" data-grid-cell-total-data border-right border-top" *ngIf="('logged_sum_billable_non_billable_flexi' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{'logged_sum_billable_non_billable_flexi' | totaldata: project.id : projectData}}
                            </div>
                          
                          
                            
                            <div class=" data-grid-cell-total-data  border-right border-top" *ngIf="('logged_non_billable_flexi' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{'logged_non_billable_flexi' | totaldata: project.id : projectData}}
                            </div>
                            <div class=" data-grid-cell-total-data  border-right border-top" *ngIf="('logged_non_billable_growth' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{'logged_non_billable_growth' | totaldata  : project.id : projectData}}
                            </div>

                            <div class=" data-grid-cell-total-data border-right border-top" *ngIf="('logged_non_billable_hours' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{'logged_non_billable_hours' | totaldata : project.id : projectData}}
                            </div>
                            
 

                            <div class=" data-grid-cell-total-data border-right border-top" *ngIf="('logged_hours' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{'logged_hours' | totaldata : project.id : projectData}}
                            </div>
                            <div class=" data-grid-cell-total-data border-right border-top" *ngIf="('billable_hours' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{'billable_hours' | totaldata : project.id : projectData}}
                            </div>
                          
                          
                          </div>
                        </div>
                  
                      
                    
                      </div>

                      <div class="col-header-m-top p-0 mr-1 " *ngFor="let month of duration" >
                    
        
                    
                
                          <div class=" data-grid-cell-wise  p-0 m-0">
                            <div class="row row-header cell-spacing" >
                              
                              <div class=" data-grid-cell-header-value border-right border-top" *ngIf="('milestone_plan' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                                {{'available_hours' | monthdata : month.month : month.year : durationSelectedValue :project.month_data }}
                              </div>
                              <div class=" data-grid-cell-header-value border-right border-top" *ngIf="('allocated_billable_hours' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                                {{'allocated_billable_hours' | monthdata : month.month : month.year : durationSelectedValue :project.month_data}}
                              </div>
                              
                              <div class=" data-grid-cell-header-value border-right border-top" *ngIf="('allocated_sum_billable_non_billable_flexi' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                                {{'allocated_sum_billable_non_billable_flexi' | monthdata : month.month : month.year : durationSelectedValue :project.month_data}}
                              </div>
                              
                              <div class=" data-grid-cell-header-value  border-right border-top" *ngIf="('allocated_non_billable_flexi' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                                {{'allocated_non_billable_flexi' | monthdata : month.month : month.year : durationSelectedValue :project.month_data}}
                              </div>
                              <div class=" data-grid-cell-header-value  border-right border-top" *ngIf="('allocated_non_billable_growth' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                                {{'allocated_non_billable_growth' | monthdata : month.month : month.year : durationSelectedValue :project.month_data}}
                              </div>
                              <div class=" data-grid-cell-header-value border-right border-top" *ngIf="('allocated_non_billable_hours' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                                {{'allocated_non_billable_hours' | monthdata : month.month : month.year : durationSelectedValue :project.month_data}}
                              </div>
                              
                              <div class=" data-grid-cell-header-value border-right border-top" *ngIf="('allocated_hours' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                                {{'allocated_hours' | monthdata : month.month : month.year : durationSelectedValue :project.month_data}}
                              </div>
              
                
                              <div class=" data-grid-cell-header-value border-right border-top" *ngIf="('logged_billable_hours' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                                {{'logged_billable_hours' | monthdata : month.month : month.year : durationSelectedValue :project.month_data}}
                              </div>

                              <div class=" data-grid-cell-header-value border-right border-top" *ngIf="('logged_sum_billable_non_billable_flexi' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                                {{'logged_sum_billable_non_billable_flexi' | monthdata : month.month : month.year : durationSelectedValue :project.month_data}}
                              </div>
                            
                             
                              <div class=" data-grid-cell-header-value  border-right border-top" *ngIf="('logged_non_billable_flexi' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                                {{'logged_non_billable_flexi' | monthdata : month.month : month.year : durationSelectedValue :project.month_data}}
                              </div>
                              <div class=" data-grid-cell-header-value  border-right border-top" *ngIf="('logged_non_billable_growth' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                                {{'logged_non_billable_growth' | monthdata : month.month : month.year : durationSelectedValue :project.month_data}}
                              </div>
                              <div class=" data-grid-cell-header-value border-right border-top" *ngIf="('logged_non_billable_hours' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                                {{'logged_non_billable_hours' | monthdata : month.month : month.year : durationSelectedValue :project.month_data}}
                              </div>

                              <div class=" data-grid-cell-header-value border-right border-top" *ngIf="('logged_hours' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                                {{'logged_hours' | monthdata : month.month : month.year : durationSelectedValue :project.month_data}}
                              </div>
                              <div class=" data-grid-cell-header-value border-right border-top" *ngIf="('billable_hours' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                                {{'billable_hours' | monthdata : month.month : month.year : durationSelectedValue :project.month_data}}
                              </div>
                            
                            
                            </div>
                          </div>
                    
                        
                      
                      </div>
                      
                      <div class="col-header-m-top p-0 mr-1 " *ngIf="!showTotalFront">
                    
        
                    
                
                        <div class=" data-grid-cell-wise  p-0 m-0">
                          <div class="row row-header cell-spacing" >
                            
                            <div class=" data-grid-cell-total-data border-right border-top" *ngIf="('milestone_plan' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{'available_hours' | totaldata : project.id : projectData}}
                            </div>
                            <div class=" data-grid-cell-total-data border-right border-top" *ngIf="('allocated_billable_hours' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{'allocated_billable_hours' | totaldata : project.id : projectData}}
                            </div>

                            <div class=" data-grid-cell-total-data border-right border-top" *ngIf="('allocated_sum_billable_non_billable_flexi' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{'allocated_sum_billable_non_billable_flexi' | totaldata : project.id : projectData}}
                            </div>
                            

                            <div class=" data-grid-cell-total-data  border-right border-top" *ngIf="('allocated_non_billable_flexi' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{'allocated_non_billable_flexi' | totaldata : project.id : projectData}}
                            </div>
                            <div class=" data-grid-cell-total-data  border-right border-top" *ngIf="('allocated_non_billable_growth' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{'allocated_non_billable_growth' | totaldata : project.id : projectData}}
                            </div>
                            <div class=" data-grid-cell-total-data border-right border-top" *ngIf="('allocated_non_billable_hours' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{'allocated_non_billable_hours' | totaldata : project.id : projectData}}
                            </div>

                            <div class=" data-grid-cell-total-data border-right border-top" *ngIf="('allocated_hours' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{'allocated_hours' | totaldata : project.id : projectData}}
                            </div>

                            <div class=" data-grid-cell-total-data border-right border-top" *ngIf="('logged_billable_hours' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{'logged_billable_hours' | totaldata: project.id : projectData}}
                            </div>

                            <div class=" data-grid-cell-total-data border-right border-top" *ngIf="('logged_sum_billable_non_billable_flexi' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{'logged_sum_billable_non_billable_flexi' | totaldata : project.id : projectData}}
                            </div>
                          
                            
                            <div class=" data-grid-cell-total-data  border-right border-top" *ngIf="('logged_non_billable_flexi' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{'logged_non_billable_flexi' | totaldata: project.id : projectData}}
                            </div>
                            <div class=" data-grid-cell-total-data  border-right border-top" *ngIf="('logged_non_billable_growth' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{'logged_non_billable_growth' | totaldata  : project.id : projectData}}
                            </div>
                            <div class=" data-grid-cell-total-data border-right border-top" *ngIf="('logged_non_billable_hours' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{'logged_non_billable_hours' | totaldata : project.id : projectData}}
                            </div>
 

                            <div class=" data-grid-cell-total-data border-right border-top" *ngIf="('logged_hours' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{'logged_hours' | totaldata : project.id : projectData}}
                            </div>
                            <div class=" data-grid-cell-total-data border-right border-top" *ngIf="('billable_hours' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{'billable_hours' | totaldata : project.id : projectData}}
                            </div>
                          
                          
                          </div>
                        </div>
                  
                      
                    
                      </div>
                   

                    </div>
        
                  </ng-container>
                
        
        
                  <div style="height: 6px;     background: #F0F2F7">
                  </div>
        
                  <ng-container>
                    
                    <div class="row row-header">

                      <div class=" col-header-m-top p-0 mr-1 " *ngIf="showTotalFront" >
                    
        
                    
                
                        <div class=" data-grid-cell-wise  p-0 m-0">
                          <div class="row row-header cell-spacing" >
                            
                            <div class=" data-grid-total-header border-right border-top" *ngIf="('milestone_plan' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{'available_hours' | totaldataTotal :  projectData}}
                            </div>
                            <div class=" data-grid-total-header border-right border-top" *ngIf="('allocated_billable_hours' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{'allocated_billable_hours' | totaldataTotal :  projectData}}
                            </div>

                            <div class=" data-grid-total-header border-right border-top" *ngIf="('allocated_sum_billable_non_billable_flexi' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{'allocated_sum_billable_non_billable_flexi' | totaldataTotal :  projectData}}
                            </div>
                            
                            <div class=" data-grid-total-header border-right border-top" *ngIf="('allocated_non_billable_flexi' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{'allocated_non_billable_flexi' | totaldataTotal :  projectData}}
                            </div>
                            <div class=" data-grid-total-header border-right border-top" *ngIf="('allocated_non_billable_growth' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{'allocated_non_billable_growth' | totaldataTotal :  projectData}}
                            </div>
                            <div class=" data-grid-total-header border-right border-top" *ngIf="('allocated_non_billable_hours' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{'allocated_non_billable_hours' | totaldataTotal :  projectData}}
                            </div>
                            <div class=" data-grid-total-header border-right border-top" *ngIf="('allocated_hours' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{'allocated_hours' | totaldataTotal :  projectData}}
                            </div>
                            <div class=" data-grid-total-header border-right border-top" *ngIf="('logged_billable_hours' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{'logged_billable_hours' | totaldataTotal:  projectData}}
                            </div>

                            <div class=" data-grid-total-header border-right border-top" *ngIf="('logged_sum_billable_non_billable_flexi' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{'logged_sum_billable_non_billable_flexi' | totaldataTotal :  projectData}}
                            </div>
                          
                    
                            <div class=" data-grid-total-header border-right border-top" *ngIf="('logged_non_billable_flexi' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{'logged_non_billable_flexi' | totaldataTotal:  projectData}}
                            </div>
                            <div class=" data-grid-total-header border-right border-top" *ngIf="('logged_non_billable_growth' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{'logged_non_billable_growth' | totaldataTotal  :  projectData}}
                            </div>

                            <div class=" data-grid-total-header border-right border-top" *ngIf="('logged_non_billable_hours' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{'logged_non_billable_hours' | totaldataTotal :  projectData}}
                            </div>


                            <div class=" data-grid-total-header border-right border-top" *ngIf="('logged_hours' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{'logged_hours' | totaldataTotal :  projectData}}
                            </div>
                            <div class=" data-grid-total-header border-right border-top" *ngIf="('billable_hours' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{'billable_hours' | totaldataTotal :  projectData}}
                            </div>
                          
                          
                          </div>
                        </div>
                  
                      
                    
                      </div>

                      <div class=" col-header-m-top p-0 mr-1 " *ngFor="let month of duration" >
                    
        
                    
                
                          <div class=" data-grid-cell-wise  p-0 m-0">
                            <div class="row row-header cell-spacing" >
                              
                              <div class=" data-grid-total-header border-right border-top" *ngIf="('milestone_plan' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                                {{'available_hours' | totaldatavalue : month.month : month.year : durationSelectedValue : projectData}} <span style="font-size: 9px;"> Hrs</span>
                              </div>
                              <div class=" data-grid-total-header border-right border-top" *ngIf="('allocated_billable_hours' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                                {{'allocated_billable_hours' | totaldatavalue : month.month : month.year : durationSelectedValue :projectData}}<span style="font-size: 9px;"> Hrs</span>
                              </div>

                              <div class=" data-grid-total-header border-right border-top" *ngIf="('allocated_sum_billable_non_billable_flexi' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                                {{'allocated_sum_billable_non_billable_flexi' | totaldatavalue : month.month : month.year : durationSelectedValue :projectData}}<span style="font-size: 9px;"> Hrs</span>
                              </div>
                              
                              <div class=" data-grid-total-header border-right border-top" *ngIf="('allocated_non_billable_flexi' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                                {{'allocated_non_billable_flexi' | totaldatavalue : month.month : month.year : durationSelectedValue :projectData}}<span style="font-size: 9px;"> Hrs</span>
                              </div>
                              <div class=" data-grid-total-header border-right border-top" *ngIf="('allocated_non_billable_growth' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                                {{'allocated_non_billable_growth' | totaldatavalue : month.month : month.year : durationSelectedValue :projectData}}<span style="font-size: 9px;"> Hrs</span>
                              </div>
                              <div class=" data-grid-total-header border-right border-top" *ngIf="('allocated_non_billable_hours' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                                {{'allocated_non_billable_hours' | totaldatavalue : month.month : month.year : durationSelectedValue :projectData}}<span style="font-size: 9px;"> Hrs</span>
                              </div>
                              <div class=" data-grid-total-header border-right border-top" *ngIf="('allocated_hours' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                                {{'allocated_hours' | totaldatavalue : month.month : month.year : durationSelectedValue :projectData}}<span style="font-size: 9px;"> Hrs</span>
                              </div>
                              <div class=" data-grid-total-header border-right border-top" *ngIf="('logged_billable_hours' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                                {{'logged_billable_hours' | totaldatavalue : month.month : month.year : durationSelectedValue :projectData}}<span style="font-size: 9px;"> Hrs</span>
                              </div>

                              <div class=" data-grid-total-header border-right border-top" *ngIf="('logged_sum_billable_non_billable_flexi' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                                {{'logged_sum_billable_non_billable_flexi' | totaldatavalue : month.month : month.year : durationSelectedValue :projectData}}<span style="font-size: 9px;"> Hrs</span>
                              </div>
                            
                      
                              <div class=" data-grid-total-header border-right border-top" *ngIf="('logged_non_billable_flexi' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                                {{'logged_non_billable_flexi' | totaldatavalue : month.month : month.year : durationSelectedValue :projectData}}<span style="font-size: 9px;"> Hrs</span>
                              </div>
                              <div class=" data-grid-total-header border-right border-top" *ngIf="('logged_non_billable_growth' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                                {{'logged_non_billable_growth' | totaldatavalue : month.month : month.year : durationSelectedValue :projectData}}<span style="font-size: 9px;"> Hrs</span>
                              </div>

                              <div class=" data-grid-total-header border-right border-top" *ngIf="('logged_non_billable_hours' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                                {{'logged_non_billable_hours' | totaldatavalue : month.month : month.year : durationSelectedValue :projectData}}<span style="font-size: 9px;"> Hrs</span>
                              </div>


                              <div class=" data-grid-total-header border-right border-top" *ngIf="('logged_hours' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                                {{'logged_hours' | totaldatavalue : month.month : month.year : durationSelectedValue :projectData}}<span style="font-size: 9px;"> Hrs</span>
                              </div>
                              <div class=" data-grid-total-header border-right border-top" *ngIf="('billable_hours' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                                {{'billable_hours' | totaldatavalue : month.month : month.year : durationSelectedValue :projectData}}<span style="font-size: 9px;"> Hrs</span>
                              </div>
                            
                            
                            </div>
                          </div>
                    
                        
                      
                      </div>

                      <div class=" col-header-m-top p-0 mr-1 " *ngIf="!showTotalFront" >
                    
        
                    
                
                        <div class=" data-grid-cell-wise  p-0 m-0">
                          <div class="row row-header cell-spacing" >
                            
                            <div class=" data-grid-total-header border-right border-top" *ngIf="('milestone_plan' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{'available_hours' | totaldataTotal :  projectData}}
                            </div>
                            <div class=" data-grid-total-header border-right border-top" *ngIf="('allocated_billable_hours' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{'allocated_billable_hours' | totaldataTotal :  projectData}}
                            </div>
                             

                            <div class=" data-grid-total-header border-right border-top" *ngIf="('allocated_sum_billable_non_billable_flexi' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{'allocated_sum_billable_non_billable_flexi' | totaldataTotal :  projectData}}
                            </div>

                            <div class=" data-grid-total-header border-right border-top" *ngIf="('allocated_non_billable_flexi' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{'allocated_non_billable_flexi' | totaldataTotal :  projectData}}
                            </div>
                            <div class=" data-grid-total-header border-right border-top" *ngIf="('allocated_non_billable_growth' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{'allocated_non_billable_growth' | totaldataTotal :  projectData}}
                            </div>
                            <div class=" data-grid-total-header border-right border-top" *ngIf="('allocated_non_billable_hours' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{'allocated_non_billable_hours' | totaldataTotal :  projectData}}
                            </div>
                            <div class=" data-grid-total-header border-right border-top" *ngIf="('allocated_hours' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{'allocated_hours' | totaldataTotal :  projectData}}
                            </div>
                            <div class=" data-grid-total-header border-right border-top" *ngIf="('logged_billable_hours' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{'logged_billable_hours' | totaldataTotal:  projectData}}
                            </div>

                            <div class=" data-grid-total-header border-right border-top" *ngIf="('logged_sum_billable_non_billable_flexi' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{'logged_sum_billable_non_billable_flexi' | totaldataTotal :  projectData}}
                            </div>
                          
                    
                            <div class=" data-grid-total-header border-right border-top" *ngIf="('logged_non_billable_flexi' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{'logged_non_billable_flexi' | totaldataTotal:  projectData}}
                            </div>
                            <div class=" data-grid-total-header border-right border-top" *ngIf="('logged_non_billable_growth' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{'logged_non_billable_growth' | totaldataTotal  :  projectData}}
                            </div>

                            <div class=" data-grid-total-header border-right border-top" *ngIf="('logged_non_billable_hours' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{'logged_non_billable_hours' | totaldataTotal :  projectData}}
                            </div>


                            <div class=" data-grid-total-header border-right border-top" *ngIf="('logged_hours' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{'logged_hours' | totaldataTotal :  projectData}}
                            </div>
                            <div class=" data-grid-total-header border-right border-top" *ngIf="('billable_hours' | checkColumn : this.columns : this.durationSelectedValue: this.groupBySelectedValue)">
                              {{'billable_hours' | totaldataTotal :  projectData}}
                            </div>
                          
                          
                          </div>
                        </div>
                  
                      
                    
                      </div>
                    </div>
        
                  </ng-container>
                
                
              </ng-container>
            </div>
            
          

    </div>





</div> 