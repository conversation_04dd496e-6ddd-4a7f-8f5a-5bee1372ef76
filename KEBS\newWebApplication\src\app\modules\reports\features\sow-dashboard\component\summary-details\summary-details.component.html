<div class="summary-card-details-styles">

    <div class="loader-container" *ngIf="isComponentLoading">
        <mat-spinner class="green-spinner loader" style diameter="40"></mat-spinner>
    </div>
    <div *ngIf="!isComponentLoading">
        <div class="details-fix-style">
            <div class="row title pt-2 pb-1">

                <div class="icon" (click)="onCloseClick()">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                        <g opacity="0.4" clip-path="url(#clip0_6114_39061)">
                            <path d="M6.81795 8L10 11.1113L9.09103 12L5 8L9.09103 4L10 4.88875L6.81795 8Z"
                                fill="#111434" />
                        </g>
                        <defs>
                            <clipPath id="clip0_6114_39061">
                                <rect width="16" height="16" fill="white" />
                            </clipPath>
                        </defs>
                    </svg>
                </div>
                <span class="pl-3 my-auto item-name" style="padding-top:2px;" placement="top"
                    showDelay="500">{{summaryCardHeader}}</span>

            </div>




            <div class="filter-container">


                <ng-container>
                    <div class="filter-title-display">


                        <div>
                            <div style="cursor: pointer" [matMenuTriggerFor]="groupByPopUp">
                                <div class="view-type-box-groupby">
                                    <p class="view-type-text"><span style="font-weight: 700;">View By: </span> 
                                        {{ groupBySelectedValue === 'Position' ? 'Position/Activity' : groupBySelectedValue }}</p>
                                    <mat-icon class="view-type-icon">expand_more</mat-icon>
                                </div>
                            </div>

                        </div>


                        <div class="filter-content" style="margin-left: 23px;">
                            <span class="filter-title">
                                Filter
                            </span>
                        </div>

                        <div style="margin-left: 20px;" *ngIf="groupBySelectedValue =='Region'">
                            <div style="cursor: pointer" [matMenuTriggerFor]="regionTypePopUp">
                                <div class="view-type-box">
                                    <p class="view-type-text"><span style="font-weight: 700;">Region ({{this.region_ids.length}})</span> </p>
                                    <mat-icon class="view-type-icon">expand_more</mat-icon>
                                </div>
                            </div>

                        </div>

                        <div style="margin-left: 20px;" *ngIf="groupBySelectedValue =='Sales Region'">
                            <div style="cursor: pointer" [matMenuTriggerFor]="salesRegionTypePopUp">
                                <div class="view-type-box">
                                    <p class="view-type-text"><span style="font-weight: 700;">Sales Region ({{this.sales_region_ids.length}})</span> </p>
                                    <mat-icon class="view-type-icon">expand_more</mat-icon>
                                </div>
                            </div>

                        </div>

                        <div style="margin-left: 20px;" *ngIf="groupBySelectedValue =='Customer'">
                            <div style="cursor: pointer" [matMenuTriggerFor]="customerTypePopUp">
                                <div class="view-type-box">
                                    <p class="view-type-text"><span style="font-weight: 700;">Customer
                                            ({{this.customer_ids.length}})</span> </p>
                                    <mat-icon class="view-type-icon">expand_more</mat-icon>
                                </div>
                            </div>

                        </div>

                        <div style="margin-left: 20px;" *ngIf="groupBySelectedValue == 'Project'">
                            <div style="cursor: pointer" [matMenuTriggerFor]="sowTypePopUp">
                                <div class="view-type-box">
                                    <p class="view-type-text"><span style="font-weight: 700;">Project
                                            ({{this.sow_ids.length}})</span> </p>
                                    <mat-icon class="view-type-icon">expand_more</mat-icon>
                                </div>
                            </div>

                        </div>

                        <div style="margin-left: 20px;" *ngIf="groupBySelectedValue == 'Position'">
                            <div style="cursor: pointer" [matMenuTriggerFor]="positionTypePopUp">
                                <div class="view-type-box">
                                    <p class="view-type-text"><span style="font-weight: 700;">Position/Activity ({{this.position_ids.length}})</span> </p>
                                    <mat-icon class="view-type-icon">expand_more</mat-icon>
                                </div>
                            </div>

                        </div>



                        <div class="filter-column-customize">

                            <div class="row">
                                <div class="col-12" style="margin-top: 16px; display: flex;">
                                    <div style="margin-left:2px; margin-right: 6px; margin-top: 3px;">
                                        <mat-checkbox width="16px" height="16px"
                                            [(ngModel)]="showTotalFrontVal"></mat-checkbox>
                                    </div>
                                    <div class="text-style">
                                        Show total in front
                                    </div>

                                </div>
                                <!-- <div class="col-4" style="margin-top: 14px;">
                                    <div style="white-space: nowrap;  display: flex;">
                                        <span class="toggle-content"
                                            [ngClass]="{'toggle-content-glow': !durationToggle}">
                                            Month
                                        </span>
                                        <span style="margin-top:4px">
                                            <label class="toggle-switch" [style.background-color]="button">
                                                <input type="checkbox" [checked]="durationToggle"
                                                    (click)="changeDurationType()">
                                                <span class="slider"></span>
                                            </label>
                                        </span>
                                        <span class="toggle-content font-family"
                                            [ngClass]="{'toggle-content-glow': durationToggle}">
                                            Week
                                        </span>
                                    </div>
                                </div> -->


                            </div>


                        </div>

                    </div>



                    <mat-menu #viewTypePopup="matMenu">
                        <ng-template [matMenuContent]>
                            <div (click)="$event.stopPropagation()" class="view-type-popup">
                                <p class="view-type-title">GRID PERIODS</p>
                                <mat-radio-group class="radio-group">
                                    <mat-radio-button *ngFor="let item of viewType; let i = index" [value]="item"
                                        [checked]="item === durationSelectedValue"
                                        (change)="changeViewType(item, $event)">{{ item }}</mat-radio-button>
                                </mat-radio-group>
                            </div>
                        </ng-template>
                    </mat-menu>

                    <mat-menu #groupByPopUp="matMenu">
                        <ng-template [matMenuContent]>
                            <div (click)="$event.stopPropagation()" class="view-type-popup-groupBy">
                                <p class="view-type-title">VIEW BY</p>
                                <mat-radio-group class="radio-group">
                                    <mat-radio-button *ngFor="let item of groupByType; let i = index" [value]="item"
                                        [checked]="item === groupBySelectedValue"
                                        (change)="changeGroupByType(item, $event)" style="width: 180px;">{{ item === 'Position' ? 'Position/Activity' : item 
                                        }}
                                    </mat-radio-button>
                                </mat-radio-group>
                            </div>
                        </ng-template>
                    </mat-menu>






                    <mat-menu #regionTypePopUp="matMenu">
                        <ng-template [matMenuContent]>
                            <div (click)="$event.stopPropagation()" class="column-type-popup">
                                <p class="title-text">Region</p>
                                <div class="align-items-center">
                                    <div>
                                        <mat-checkbox width="16px" height="16px"
                                            (click)="onRegionIdsChange( 'all')"></mat-checkbox>
                                    </div>
                                    <div class="text-style">
                                        Select All
                                    </div>
                                </div>
                                <ng-container *ngFor="let item of region_list; let i = index">
                                    <div class="align-items-center">
                                        <div>
                                            <mat-checkbox width="16px" height="16px" [(ngModel)]="item.checked"
                                                (click)="onRegionIdsChange(i)"></mat-checkbox>
                                        </div>
                                        <div class="text-style" matToolTip="{{item.name}}">
                                            {{ item.name}}
                                        </div>
                                    </div>
                                </ng-container>

                            </div>
                        </ng-template>
                    </mat-menu>
                    
                    <mat-menu #salesRegionTypePopUp="matMenu">
                        <ng-template [matMenuContent]>
                            <div (click)="$event.stopPropagation()" class="column-type-popup">
                                <p class="title-text">Sales Region</p>
                                <div class="align-items-center">
                                    <div>
                                        <mat-checkbox width="16px" height="16px"
                                            (click)="onSalesRegionIdsChange( 'all')"></mat-checkbox>
                                    </div>
                                    <div class="text-style">
                                        Select All
                                    </div>
                                </div>
                                <ng-container *ngFor="let item of sales_region_list; let i = index">
                                    <div class="align-items-center">
                                        <div>
                                            <mat-checkbox width="16px" height="16px" [(ngModel)]="item.checked"
                                                (click)="onSalesRegionIdsChange(i)"></mat-checkbox>
                                        </div>
                                        <div class="text-style" matToolTip="{{item.name}}">
                                            {{ item.name}}
                                        </div>
                                    </div>
                                </ng-container>

                            </div>
                        </ng-template>
                    </mat-menu>



                    <mat-menu #customerTypePopUp="matMenu">
                        <ng-template [matMenuContent]>

                            <div (click)="$event.stopPropagation()" class="column-type-popup">
                                <p class="title-text">Customer</p>
                                <div class="align-items-center">
                                    <div>
                                        <mat-checkbox width="16px" height="16px"
                                            (click)="onCustomerIdsChange('all')"></mat-checkbox>
                                    </div>
                                    <div class="text-style">
                                        Select All
                                    </div>
                                </div>
                                <ng-container *ngFor="let item of customer_list; let i = index">
                                    <div class="align-items-center">
                                        <div>
                                            <mat-checkbox width="16px" height="16px" [(ngModel)]="item.checked"
                                                (click)="onCustomerIdsChange(i)"></mat-checkbox>
                                        </div>
                                        <div class="text-style" matToolTip="{{item.name}}">
                                            {{ item.name}}
                                        </div>
                                    </div>
                                </ng-container>

                            </div>

                        </ng-template>
                    </mat-menu>

                    <mat-menu #sowTypePopUp="matMenu">
                        <ng-template [matMenuContent]>

                            <div (click)="$event.stopPropagation()" class="column-type-popup">
                                <p class="title-text">Project</p>
                                <div class="align-items-center">
                                    <div>
                                        <mat-checkbox width="16px" height="16px"
                                            (click)="onSOWIdsChange('all')"></mat-checkbox>
                                    </div>
                                    <div class="text-style">
                                        Select All
                                    </div>
                                </div>
                                <ng-container *ngFor="let item of project_list; let i = index">
                                    <div class="align-items-center">
                                        <div>
                                            <mat-checkbox width="16px" height="16px" [(ngModel)]="item.checked"
                                                (click)="onSOWIdsChange(i)"></mat-checkbox>
                                        </div>
                                        <div class="text-style" matToolTip="{{item.name}}">
                                            {{ item.name}}
                                        </div>
                                    </div>
                                </ng-container>

                            </div>

                        </ng-template>
                    </mat-menu>

                    <mat-menu #positionTypePopUp="matMenu">
                        <ng-template [matMenuContent]>

                            <div (click)="$event.stopPropagation()" class="column-type-popup">
                                <p class="title-text">Quote Position</p>
                                <div class="align-items-center">
                                    <div>
                                        <mat-checkbox width="16px" height="16px"
                                            (click)="onPositionIdsChange('all')"></mat-checkbox>
                                    </div>
                                    <div class="text-style">
                                        Select All
                                    </div>
                                </div>
                                <ng-container *ngFor="let item of position_list; let i = index">
                                    <div class="align-items-center">
                                        <div>
                                            <mat-checkbox width="16px" height="16px" [(ngModel)]="item.checked"
                                                (click)="onPositionIdsChange(i)"></mat-checkbox>
                                        </div>
                                        <div class="text-style" matToolTip="{{item.name}}">
                                            {{ item.name}}
                                        </div>
                                    </div>
                                </ng-container>

                            </div>

                        </ng-template>
                    </mat-menu>


                </ng-container>



            </div>
        </div>

        <hr style="margin-top: 4px;  margin-bottom: 16px;">


        <div class="body" >





            <div class="row pl-3 justify-content-center" *ngIf="reportLoading">
                <div class="col-12 justify-content-center" style="display: flex;height: 1500px;">
                    <mat-spinner matTooltip="Please wait..." diameter="30"> </mat-spinner>
                </div>

            </div>
            

            <div class="summary-charts row" *ngIf="!reportLoading">
                <div >
                    <app-summary-card-charts [dashboardColumn]="dashboardColumn"
                        [durationSelectedValue]="durationSelectedValue"
                        [reportTableData] = "reportTableData"
                        [duration]="this.duration"></app-summary-card-charts>
                </div>
            </div>



            <div class="row" style="padding-top: 30px;padding-bottom: 20px;" *ngIf="!reportLoading">

                <app-report-table-grid-url [ganttURL]="ganttURL"
                    (urlOutput)="goPreviousScreen($event)"></app-report-table-grid-url>

            </div>

            <div class="summary-report-table-grid row" *ngIf="!reportLoading">



                <div class="col-12 ml-0 pl-0 pr-0">
                    <app-report-table-grid [columns]="this.dashboardColumn" [projectData]="reportTableData"
                        (nextLevel)="openNextLevel($event)" [startDate]="this.startDate" [endDate]="this.endDate"
                        [duration]="this.duration" [durationSelectedValue]="durationSelectedValue"
                        [groupBySelectedValue]="this.groupBySelectedValue"
                        [showTotalFront]="this.showTotalFrontVal"></app-report-table-grid>
                </div>
            </div>

            
        </div>



    </div>

</div>