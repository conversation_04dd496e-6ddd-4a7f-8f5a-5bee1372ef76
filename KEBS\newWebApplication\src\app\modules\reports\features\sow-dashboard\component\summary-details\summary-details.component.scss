.summary-card-details-styles{
    background: #F0F2F7;
    padding-left: 20px;
    padding-right: 20px;
    height:100%;
    

    .header {
        background: #FFFFFF;
        align-items: center;
    }

    .header-title {
        font-family: 'Plus Jakarta Sans';
        font-size: 16px;
        font-weight: 600;
        line-height: 24px;
        letter-spacing: 0em;
        text-align: left;
        color: #111434;
    }

    .close-button {
        color: #7D838B;
        font-size: 20px;
        cursor: pointer;

    }

    .body {
    
        background: #F0F2F7;
    }

    .details-fix-style{
        position:sticky;
    }

    // .addMember-details {
        
    // }

    .first-line-details {
        width: 890px;
        height: 52px;
        gap: 16px;
        display: flex;
        margin-left: 25px;

    }

    .content-title {
        padding-top: 20px;
        padding-bottom: 4px;
        font-family: Roboto;
        font-size: 12px;
        font-weight: 500;
        line-height: 16px;
        letter-spacing: 0.02em;
        text-align: left;
        color: #6E7B8F;
    }

    .input-field {
        border-radius: 5px;
        display: flow;
        background-color: white;
        padding-left: 0px !important;
        padding-right: 0px !important;
        font-size: 12px;
        font-family: Roboto;
        /* font-size: 13px; */
        font-weight: 400;
        line-height: 16px;
        letter-spacing: 0em;
        text-align: left;
        color: #8B95A5;
        width: 267px;
    }

    ::ng-deep .mat-form-field .mat-form-field-wrapper {
        padding-bottom: 0px !important;
    }

    ::ng-deep .mat-form-field {
        /* display: inline-block; */
        position: relative;
        text-align: left;
        display: flow;
    }

    .button-next ::ng-deep .green-spinner circle {
        stroke: white !important;
    }

    .loader-container ::ng-deep .green-spinner circle {
        stroke: var(--extButton) !important;
    }

    .second-line-details {
        width: 223px;
        height: 24px;
        gap: 32px
    }

    .is-head {
        width: 71px;
        height: 24px;
        gap: 12px;
        margin-top: -23.5px;
        margin-left: 150px;

    }

    .third-line-details {
        // width: 157px;
        height: 40px;
        gap: 10px;
        margin-left: 25px;
        margin-top: -5px;
        display: flex;

    }

    .fourth-line-details {
        gap: 16px;
        display: flex;
        margin-left: 25px;
        margin-top: 40px;

    }

    .fifth-line-details {
        width: 223px;
        height: 24px;
        gap: 24px;
        margin-top: -20px;

    }

    .billable {
        width: 70px;
        height: 24px;
        gap: 12px;
        margin-top: 30px;
        margin-left: 40px;

    }

    .footer-buttons {
        background-color: whitesmoke;
        border-top: 1px solid grey;
        height: 66px;
        align-items: center;
    }

    .button-back {
        color: black;
        border: none;
        background: none;
    }

    .button-save {
        color: white;
        font-family: Roboto;
        font-size: 14px;
        font-weight: 700;
        letter-spacing: -0.02em;
        text-align: left;
        border: none;
        background-color: var(--extButton);
    }

    .save-class {
        background-color: var(--extButton) !important;
        // background-color: var(--memberButton);
        border-radius: 5px;
        color: white;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 64px;
    }


    .add_new_color {
        width: 133px;
        height: 16px;
        font-family: Roboto;
        font-size: 14px;
        font-weight: 700;
        line-height: 16px;
        letter-spacing: -0.02em;
        text-align: left;
        color: var(---extButton) !important;


    }

    .business-unit {
        width: 157px;
        height: 40px;
        gap: 10px;
        margin-left: 40px;
        margin-top: 51px;

    }

    .is-head-name {
        width: 43px;
        height: 24px;
        font-family: Roboto;
        font-size: 12px;
        font-weight: 500;
        line-height: 24px;
        letter-spacing: 0.02em;
        text-align: left;
        color: #6E7B8F;


    }

    .billable-name {
        width: 42px;
        height: 24px;
        font-family: Roboto;
        font-size: 12px;
        font-weight: 500;
        line-height: 24px;
        letter-spacing: 0.02em;
        text-align: left;
        color: #6E7B8F;
    }

    .bussiness-division {
        margin-top: -25px;
        display: flex;
        margin-left: 25px;
    }

    .division-name {
        margin-left: 40px
    }

    .sub-division-name {
        margin-left: 264px
    }

    .business-unit-1 {

        width: 157px;
        height: 40px;
        gap: 10px;
        margin-left: 40px;
        margin-top: 40px;
    }

    .info-icon {
        font-size: 12px;
        margin-left: 5px;
        height: 10px;
        cursor: pointer;
        margin-top: 2px;
        color: grey;
        position: absolute;
    }

    .required-star {
        color: #EC5F6E;
    }

    .employee_name {
        border-radius: 5px;
        display: flow;
        background-color: white;
        padding-left: 0px !important;
        padding-right: 0px !important;
        font-size: 19px;
        font-family: Roboto;
        font-weight: 400;
        line-height: 16px;
        letter-spacing: 0em;
        text-align: left;
        color: #8B95A5;
        margin-top: -3px;
        width: 247px;
    }

    .project_role {
        border-radius: 5px;
        display: flow;
        // background-color: white;
        padding-left: 0px !important;
        padding-right: 0px !important;
        font-size: 12px;
        font-family: Roboto;
        /* font-size: 13px; */
        font-weight: 400;
        line-height: 16px;
        letter-spacing: 0em;
        text-align: left;
        color: #8B95A5;
        width: 267px;
        background: #ffff;
    }

    .reports_to {
        border-radius: 5px;
        display: flow;
        background-color: white;
        padding-left: 0px !important;
        padding-right: 0px !important;
        font-size: 12px;
        font-family: Roboto;
        /* font-size: 13px; */
        font-weight: 400;
        line-height: 16px;
        letter-spacing: 0em;
        text-align: left;
        color: #8B95A5;
    }

    .secondary_project_role {
        border-radius: 5px;
        display: flow;
        // background-color: white;
        padding-left: 0px !important;
        padding-right: 0px !important;
        font-size: 12px;
        font-family: Roboto;
        font-weight: 400;
        line-height: 16px;
        letter-spacing: 0em;
        text-align: left;
        color: #8B95A5;
        width: 267px;
    }

    .no-updown-arrows::-webkit-inner-spin-button,
    .no-updown-arrows::-webkit-outer-spin-button {
        -webkit-appearance: none;
        appearance: none;
        margin: 0;
    }

    ::ng-deep .mat-datepicker-toggle,
    .mat-datepicker-content .mat-calendar-next-button,
    .mat-datepicker-content .mat-calendar-previous-button {
        color: var(--extButton) !important;
    }

    .loader-container {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100%;
        /* Adjust the height as needed */
        background-color: white !important;
        /* Add a semi-transparent background */
    }

    .loader {
        color: var(--extButton)
    }

    @keyframes spin {
        0% {
            transform: rotate(0deg);
        }

        100% {
            transform: rotate(360deg);
        }
    }

    ::-webkit-scrollbar {
        width: 5px !important;
        height: 5px !important;
    }

    .shift {
        border-radius: 5px;
        display: flow;
        background-color: white;
        padding-left: 0px !important;
        padding-right: 0px !important;
        font-size: 12px;
        font-family: Roboto;
        /* font-size: 13px; */
        font-weight: 400;
        line-height: 16px;
        letter-spacing: 0em;
        text-align: left;
        color: #8B95A5;
        width: 267px;
    }
    .fieldvalue {
        font-weight: 400;
        font-size: 12px;
        line-height: 16px;
        letter-spacing: 0.02em;
        color: #8B95A5;
    }
    .form-header{
        font-family: Roboto;
        font-size: 12px;
        font-weight: 500;
        line-height: 16px;
        letter-spacing: 0.02em;
        text-align: left;
        color: #6E7B8F;
    }
    .dateFieldBorder {
        border: 1px solid;
        border-color: #b9c0ca;
        width: 267px;
        height: 38px;
        display: flex;
        border-radius: 4px;
        background: #ffff;
      }
    .textDiv{
        align-items: center;
        display: flex;
        position: relative;
        left: 11px;
        justify-content: center;
        padding-top: 17px !important;
    }
    .form-header-val{
        display: block;
        margin-top: 3px;
        color: #6c757dde;
        font-family: Roboto;
        font-size: 12px;
        font-weight: 500;
        line-height: 16px;
        letter-spacing: 0.02em;
        text-align: left;
    }

      .dateFieldBorder:focus {
        border: 2px solid var(--externalStakeField) !important; /* Change the border color on focus */
      }
      .dateFieldBorder:hover {
        border: 2px solid var(--externalStakeField) !important; /* Change the border color on focus */
      }
      ::ng-deep .mat-form-field-appearance-outline .mat-form-field-outline-thick{
        color: var(--externalStakeField) !important;
      }


      .summary-charts{
        justify-content: center;
        height: 300px;
        background: #fff;
        margin-top: 10px;
        margin-bottom: 10px;
        padding-left: 10px;
        padding-right: 10px;
        
      }
  
      .summary-report-table-grid{
        justify-self: center;
        width: 100%;
      }



  
  
    .duration {
      padding-left: 20px;
      margin-top: 3px;
    }
  
  
  
  
  
  
    .dashboard-option-styles {
      position: absolute;
      right: -1px;
      margin-top: -10px !important;
  
      .view-button-inactive {
        margin-top: 5px !important;
        line-height: 8px;
        width: 45px;
        height: 40px;
        margin-right: 10px !important;
        // box-shadow: 0 4px 4px -1px rgba(0, 0, 0, 0.2),
        //   0 1px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 14px 0 rgba(0, 0, 0, 0.12);
        -webkit-animation: slide-top 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
        animation: slide-top 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
        border-radius: 10px;
  
        .iconButton {
          color: #5F6C81;
          font-size: 16px;
          height: 25px;
          transition: all 0.3s linear;
        }
  
  
  
        .iconButtonWarn {
          color: #cf0001;
          font-size: 18px;
        }
  
        .iconButtonWarnAnim {
          color: #cf0001;
          font-size: 18px;
          animation: blink 1.1s cubic-bezier(.5, 0, 1, 1) infinite alternate;
        }
  
        .iconButton:hover {
          transform: scale(1.5);
        }
      }
    }
  
  
    .urlGeneration {
  
      margin-top: 1%;
      display: flex;
      flex-wrap: nowrap;
      overflow: hidden;
      font-size: 16px;
      font-family: 'Plus Jakarta Sans';
  
      .url-styles {
        width: 100% !important;
        padding-top: 5px;
        color: grey;
        font-family: var(--lightboxurlFont) !important;
        font-size: 12px;
        font-style: normal;
        font-weight: 500;
        line-height: 16px;
        letter-spacing: 0.24px;
        text-transform: capitalize;
      }
  
      .expand-more {
        padding-top: 3px;
      }
  
      .slash-seperator {
        padding-right: 20px;
        padding-left: 20px;
        font-size: 19px;
      }
  
      .link-name {
        padding-right: 5px;
        padding-left: 5px;
        cursor: pointer;
        display: flex;
        justify-content: center;
        align-items: center;
      }
  
      .link-name:hover {
        padding-right: 5px;
        padding-left: 5px;
        cursor: pointer;
  
        display: flex;
        justify-content: center;
        align-items: center;
      }
  
      .type {
        font-size: 15px !important;
        padding-top: 3px;
      }
  
  
  
  
      .url-item {
        display: flex;
        white-space: nowrap;
  
      }
  
      .url-item1 {
        display: flex;
        white-space: nowrap;
        margin-left: 23px;
      }
  
  
      .link-text {
        font-weight: 600;
      }
  
      .link-text-last-child {
        font-weight: 300;
      }
    }
  
  
  
    .filter-container {
  
      display: flex;
      flex-wrap: nowrap;
      overflow: hidden;
      margin-top: 6px;
  
      .filter-column-customize {
        right: 0;
        position: absolute;
  
        .text-style {
          font-size: 13px;
          font-weight: 500;
          font-family: 'Plus Jakarta Sans';
        }
  
        ::ng-deep .mat-checkbox-checked.mat-accent .mat-checkbox-background {
          background-color: #f27a6c !important;
        }
      }
  
  
  
      .filter-title {
        font-size: 13px;
        font-weight: 600;
        font-family: 'Plus Jakarta Sans';
  
      }
  
  
    }
  
    .filter-title-display {
      display: flex;
      align-items: center;
      padding-right: 10%;
      height: 33px;
      margin-top: 4px;
      color: #030600;
      width: 100%;
      margin-bottom: 10px;
  
    }
  
    .title-card-display {
      display: flex;
      align-items: center;
      padding-left: 10%;
      padding-right: 10%;
      height: 55px;
      font-size: 12px;
      line-height: 16px;
      font-weight: 500;
      color: #000000;
    }
  
    .title-display {
      display: flex;
      align-items: center;
      padding-right: 10%;
      height: 33px;
      margin-top: 4px;
      color: #030600;
    }
  
  
  
    .title {
      display: flex;
      color: rgba(0, 0, 0, 0.87);
    }
  
    .back {
      border: 1px solid lightgray;
      border-radius: 5px;
      cursor: pointer;
      margin-left: 15px;
      margin-bottom: 10px;
      color: lightgray;
    }
  
    .item-name {
      width: max-content;
      cursor: pointer;
      color: #1b2140;
    }
  
    a:hover {
      color: unset !important;
      text-decoration: none !important;
    }
  
    a.mat-tab-link.mat-focus-indicator.ng-star-inserted.mat-tab-label-active {
      color: var(--detailsTabColor) !important;
      font-family: var(--detailsTabFont) !important;
    }
  
    .tab-card {
      margin-left: 15px !important;
      margin-right: 15px !important;
      padding: 0%;
    }
  
    nav {
      width: 100%;
      z-index: 14;
      background: #f8f9fa;
      margin-left: 0px !important;
    }
  
    .project-tab-content {
      padding-bottom: 5px;
      padding-left: 5px;
      height: var(--innerDetailsTabHeight) !important;
      font-family: var(--detailsTabFont) !important;
      overflow: hidden;
    }
  
    a.mat-tab-link {
      min-width: 0px !important;
      color: black;
      font-weight: bold;
      font-family: var(--detailsTabFont) !important;
    }
  
    .mat-drawer-content {
      overflow: hidden;
    }
  
    .title {
      color: var(--black-100, #111434);
      font-family: var(--detailsTabFont) !important;
      font-size: 14px;
      font-style: normal;
      font-weight: 600;
      line-height: 24px;
      /* 150% */
      letter-spacing: 0.32px;
      text-transform: capitalize;
    }
  
  
  
  
    .icon {
      border-radius: 4px;
      border: 1px solid var(--blue-grey-60, #d1d6dd);
      display: flex;
      padding: 4px;
      align-items: center;
      gap: 4px;
      cursor: pointer;
    }
  
    .icon:hover {
      background-color: var(--blue-grey-60, #f1f4f7);
      transform: scale(1.05);
  
  
    }
  
    @keyframes pulse {
      0% {
        box-shadow: 0 0 0 0 rgba(0, 0, 0, 0.1);
      }
  
      70% {
        box-shadow: 0 0 0 15px rgba(0, 0, 0, 0);
      }
  
      100% {
        box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
      }
    }
  
    .icon:active {
      animation: pulse 0.5s;
    }
  
    .tab-card {
      flex-grow: 1;
      /* Allow the mat-card to grow and fill the available height */
    }
  
    ::ng-deep .mat-tab-nav-bar.mat-primary .mat-ink-bar {
      background-color: var(--detailsTabColor) !important;
    }
  
    ::ng-deep .project-tab-content::-webkit-scrollbar-thumb {
      background-color: var(--project2Scroll) !important;
    }
  
  
  }
  
  /* Base styles for the toggle switch container */
  .toggle-switch {
    display: inline-block;
    position: relative;
    width: 30px;
    height: 17px;
    //background-color: #ccc;
    border-radius: 15px;
    cursor: pointer;
    margin-right: 10px !important;
  }
  
  .grayed-out-toggle {
    display: inline-block;
    position: relative;
    width: 30px;
    height: 17px;
    //background-color: #ccc;
    border-radius: 15px;
    background-color: grey;
    margin-right: 10px !important;
  }
  
  .column-config-popup {
    width: 230px;
    padding: 20px;
    height: 55vh;
  }
  
  .column-config-popup-card {
    width: 245px;
    padding: 20px;
    height: 45vh;
    overflow-y: scroll;
  
  }
  
  .column-config-popup-card-1 {
    width: 200px;
    padding: 0px;
    height: 55vh;
    overflow: hidden;
  }
  
  .auto-adapt {
    width: max-content;
    padding: 20px;
    height: auto;
  }
  
  .toggle-content {
    font-weight: 700;
    margin-left: 0px;
    margin-right: 14px;
    margin-top: 2px;
    font-size: 12px;
    color: grey !important;
    padding-left: 0px;
    padding-right: 0px;
  
  }
  
  .toggle-content-glow {
    width: max-content;
    border-radius: 50px;
    display: flex;
    justify-content: center;
    color: var(--button) !important;
  }
  
  
  
  /* Hide the default checkbox input */
  .toggle-switch input[type="checkbox"] {
    display: none;
  }
  
  /* Style for the slider (the rounded switch) */
  .slider {
    position: absolute;
    top: 2px;
    left: 2px;
    width: 12px;
    height: 12px;
    background-color: #fff;
    border-radius: 50%;
    transition: transform 0.3s;
  }
  
  /* Apply styles when the checkbox is checked */
  .toggle-switch input[type="checkbox"]:checked+.slider {
    transform: translateX(13px);
  }
  
  .toggle-switch input[type="checkbox"]:not(:checked)+.slider {
    transform: translateX(0);
  }
  
  .column-header {
    color: var(--blue-grey-80, #5F6C81);
    font-family: 'Plus Jakarta Sans';
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 16px;
    letter-spacing: 0.22px;
    text-transform: capitalize;
  }
  
  .column-list {
    justify-content: space-between;
    height: 28px;
  }
  
  .data-label {
    white-space: nowrap;
    max-width: 101px;
    overflow: hidden;
    text-overflow: ellipsis;
    color: var(--Blue-Grey-80, #5F6C81);
    font-family: 'Plus Jakarta Sans';
    font-size: 13px;
    font-style: normal;
    font-weight: 400;
    line-height: 16px;
    /* 145.455% */
    letter-spacing: 0.22px;
    text-transform: capitalize;
  }
  
  .data-label-icon {
    color: var(--Blue-Grey-80, #5F6C81);
  }
  
  // .toggle-switch:not(:checked) {
  //   background-color: lightgrey;
  // }
  
  // .toggle-switch:checked {
  //   background-color: #F27A6C; 
  // }
  
  
  ::ng-deep .mat-calendar-arrow.mat-calendar-invert {
    display: none;
  }
  
  ::ng-deep .mat-calendar-period-button {
    pointer-events: none;
  }
  
  .bg-container {
    background-color: #f1f3f8;
    overflow: hidden;
  }
  
  .report-screen {
    margin-left: 24px;
    margin-right: 24px;
    margin-bottom: 24px;
    margin-top: 24px;
    height: var(--dynamicHeight);
  
    ::ng-deep .md-drppicker {
      z-index: 99999999 !important;
    }
  }
  
  .report-height {
    height: var(--dynamicReportHeight);
  }
  
  .spinner-height {
    height: var(--dynamicReportHeight);
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .main-spinner-height {
    margin-left: 24px;
    margin-right: 24px;
    margin-bottom: 24px;
    margin-top: 24px;
    height: var(--dynamicHeight);
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .no-data-img-height {
    height: var(--dynamicReportHeight);
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .align-items-center {
    display: flex;
    align-items: center;
  }
  
  .align-items-center-between {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }
  
  .back-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    color: #111434;
    font-size: 11px;
    font-weight: 400;
    border: 1px solid #8b95a5;
    border-radius: 4px;
    padding: 4px;
    cursor: pointer;
    width: fit-content;
  
    mat-icon {
      width: 0px !important;
      height: 15px !important;
    }
  
    .back-btn-icon {
      font-size: 18px;
      color: #111434;
      margin-right: 20px;
      margin-bottom: 2px;
    }
  }
  
  .header-title {
    font-size: 16px;
    font-weight: 600;
    color: #111434;
    margin-left: 20px;
  }
  
  .hours-tile {
    height: 80px;
    background-color: white;
    border: 1px solid #e8e9ee;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
  
    .hours-tile-text {
      font-size: 12px;
      font-weight: 600;
      display: flex;
      justify-content: center;
      margin-bottom: 5px;
    }
  
    .total-hours-value {
      color: #52c41a;
      font-size: 18px;
      font-weight: 500;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  
    .total-billable-hours-value {
      color: #ee4961;
      font-size: 18px;
      font-weight: 500;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  
    .total-non-billable-hours-value {
      color: #7d838b;
      font-size: 18px;
      font-weight: 500;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  
    .total-overtime-value {
      color: #fa541c;
      font-size: 18px;
      font-weight: 500;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  
    .total-leave-value {
      color: #13c2c2;
      font-size: 18px;
      font-weight: 500;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  
    .icon-size {
      font-size: 24px;
    }
  }
  
  .view-type-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 160px;
    padding: 8px 12px;
    border-radius: 4px;
    border: 1px solid #8b95a5;
    background: #fff;
    height: 25px;
  }
  
  .view-type-box-groupby {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 200px;
    padding: 8px 12px;
    border-radius: 4px;
    border: 1px solid #8b95a5;
    background: #fff;
    height: 25px;
  }
  
  
  .view-type-text {
    color: #5f6c81;
    font-size: 12px;
    font-weight: 400;
    margin-bottom: 0px;
  }
  
  .view-type-icon {
    height: 16px;
    width: 16px;
    font-size: 16px;
    color: #5f6c81;
  }
  
  .export-btn {
    display: flex;
    padding: 8px 12px;
    align-items: center;
    gap: 8px;
    border-radius: 4px;
    background-color: #ee4961;
    cursor: pointer;
  }
  
  .export-text {
    color: white;
    font-size: 14px;
    font-weight: 700;
    margin-bottom: 0px;
  }
  
  .export-icon {
    font-size: 16px;
    height: 16px;
    width: 16px;
    color: white;
  }
  
  .send-btn {
    border-radius: 4px;
    border: 1px solid #45546e;
    padding: 8px;
    cursor: pointer;
    width: 36px;
    height: 36px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .send-icon {
    height: 16px;
    width: 16px;
    font-size: 16px;
    color: #45546e;
  }
  
  .settings-btn {
    border-radius: 4px;
    border: 1px solid #45546e;
    padding: 8px;
    cursor: pointer;
    width: 36px;
    height: 36px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .settings-icon {
    height: 16px;
    width: 16px;
    font-size: 16px;
    color: #45546e;
  }
  
  .view-type-popup-groupBy {
    overflow: hidden;
    padding: 12px;
    width: 200px;
  
    .view-type-title {
      font-size: 12px;
      font-weight: 500;
      color: #45546e;
      margin-bottom: 4px;
    }
  
    .radio-group {
      font-size: 12px;
      font-weight: 400;
      color: #45546e;
    }
  
    ::ng-deep .mat-radio-button.mat-accent .mat-radio-inner-circle {
      background-color: #f27a6c !important;
    }
  
    ::ng-deep .mat-radio-button.mat-accent.mat-radio-checked .mat-radio-outer-circle {
      border-color: #f27a6c !important;
    }
  
    ::ng-deep .mat-radio-outer-circle {
      height: 16px !important;
      width: 16px !important;
      margin-top: 2px;
      border-color: #f27a6c;
    }
  
    ::ng-deep .mat-radio-inner-circle {
      height: 16px !important;
      width: 16px !important;
      margin-top: 2px;
    }
  
    ::ng-deep .mat-radio-label-content {
      padding-left: 0px !important;
      color: #45546e
    }
  }
  
  .view-type-popup {
    overflow: hidden;
    padding: 12px;
    width: 120px;
  
    .view-type-title {
      font-size: 12px;
      font-weight: 500;
      color: #45546e;
      margin-bottom: 4px;
    }
  
    .radio-group {
      font-size: 12px;
      font-weight: 400;
      color: #45546e;
    }
  
    ::ng-deep .mat-radio-button.mat-accent .mat-radio-inner-circle {
      background-color: #f27a6c !important;
    }
  
    ::ng-deep .mat-radio-button.mat-accent.mat-radio-checked .mat-radio-outer-circle {
      border-color: #f27a6c !important;
    }
  
    ::ng-deep .mat-radio-outer-circle {
      height: 16px !important;
      width: 16px !important;
      margin-top: 2px;
      border-color: #f27a6c;
    }
  
    ::ng-deep .mat-radio-inner-circle {
      height: 16px !important;
      width: 16px !important;
      margin-top: 2px;
    }
  
    ::ng-deep .mat-radio-label-content {
      padding-left: 0px !important;
      color: #45546e
    }
  }
  
  .filter-text {
    font-size: 14px;
    font-weight: 400;
    color: #45546e;
  }
  
  .filter-group-box {
    display: flex;
    align-items: center;
    height: 48px;
    border-radius: 4px;
    border: 1px solid #8b95a5;
    background: white;
  }
  
  .header-icon {
    color: #111434;
    font-size: 24px;
    cursor: pointer;
    margin-top: 8px;
  }
  
  .date-text {
    font-size: 14px;
    font-weight: 500;
    color: #111434;
  }
  
  .column-type-popup {
    overflow-y: auto;
    overflow-x: hidden;
    padding: 12px;
    width: 200px;
    height: 300px;
  
    .title-text {
      font-size: 12px;
      font-weight: 500;
      color: #45546e;
      margin-bottom: 6px;
      padding-top: 0px;
    }
  
    .text-style {
      font-size: 12px;
      font-weight: 400;
      color: #45546e;
      margin-bottom: 5px;
      margin-left: 6px;
      white-space: nowrap;
      text-overflow: ellipsis;
      display: block;
      overflow: hidden;
    }
  
    ::ng-deep .mat-checkbox-checked.mat-accent .mat-checkbox-background {
      background-color: #f27a6c !important;
    }
  }
  
  .clearbtn {
    font-weight: 600;
    font-size: 12px;
    line-height: 16px;
    border: 1px solid;
    height: 34px;
    border-radius: 4px;
    border-color: #ee4961;
    background-color: #f2d4cdad;
    color: #ee4961;
  }
  
  .searchField {
    display: inline-block;
    border: solid thin #dadce2;
    border-radius: 4px;
    height: 36px;
    margin-bottom: 8px;
  }
  
  .searchboxes {
    display: flex;
    align-items: center;
  }
  
  .titlemargin {
    margin-left: 0px !important;
    margin-right: 0px !important;
  }
  
  .searchtitle {
    font-weight: 400;
    font-size: 12px;
    line-height: 16px;
    margin: auto 5px auto 5px;
    padding: 2px;
    padding-left: 10px;
    padding-right: 10px;
  }
  
  .clearonefiltericn {
    font-size: 13px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
  }
  
  .dropdownfilter {
    font-size: 13px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    margin-top: 5px;
  }
  
  .boxstyle {
    background-color: #f2d4cdad;
    height: 1.5rem;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-right: 0px;
    color: #526179;
  }
  
  .filterval {
    width: 100px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
  
  .filterfield {
    width: 57px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
  
  .searchFieldtxt {
    outline: none;
    border: none;
    font-weight: 400;
    font-size: 12px;
    line-height: 16px;
  }
  
  .positionFieldlable {
    margin-top: 19px;
    margin-left: 4px;
    color: #a8acb2;
    font-weight: 400;
    font-size: 12px;
    line-height: 16px;
  }
  
  .positionField {
    display: inline-block;
    margin-left: 10px;
    padding-top: 5px;
    width: 147px;
    margin-right: 20px;
  }
  
  .positionFieldtxt {
    border-radius: 4px;
    height: 40px;
    width: 200px;
    border-color: #b9c0ca;
  }
  
  .tooltip {
    position: relative;
    display: inline-block;
  }
  
  .droplistvisible {
    visibility: visible !important;
  }
  
  .droplisthidden {
    visibility: hidden !important;
  }
  
  .dropdata {
    font-size: 12px;
    line-height: 16px;
    letter-spacing: 0.02em;
    text-transform: capitalize;
    color: #111434;
    width: 175px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
  
  .example-margin {
    display: flex;
    right: 10px;
    position: absolute;
  }
  
  .dropdownborder {
    z-index: 1;
    width: 230px;
    border: 1px solid lightgrey;
    border-radius: 4px;
    margin-top: 35px;
    padding: 10px !important;
  }
  
  .tooltip .tooltiptext {
    visibility: hidden;
    background-color: #ffffff;
    border-radius: 6px;
    padding: 5px;
    padding-top: 7px;
    width: 250x;
    height: 200px;
    position: absolute;
    z-index: 99999999;
    border-radius: 4px;
    overflow-y: scroll;
    overflow-x: hidden;
  }
  
  .tooltip:active .tooltiptext {
    visibility: visible;
  }
  
  .search-icon {
    color: #b9c0ca;
  }
  
  .dp-class {
    background: 0 0;
    border: lightgrey 1px solid;
    border-radius: 4px;
    font-size: 12px;
    height: 40px;
    font-weight: 500;
    width: 100%;
    height: 40px;
    margin-top: 4px;
    cursor: pointer;
    text-align: center;
  }
  
  .lazy-load-spinner {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    bottom: 5%;
  }
  
  .loading-text {
    position: relative;
    color: #ee4961;
    font-size: 20px;
    font-weight: bold;
  }
  
  .disabled {
    opacity: 0.6;
    /* Reduce opacity to indicate disabled state */
    pointer-events: none;
    /* Disable pointer events */
  }
  
  .view-button-inactive {
    margin-top: 5px !important;
    line-height: 8px;
    width: 45px;
    height: 40px;
    margin-right: 10px !important;
    // box-shadow: 0 4px 4px -1px rgba(0, 0, 0, 0.2),
    //   0 1px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 14px 0 rgba(0, 0, 0, 0.12);
    -webkit-animation: slide-top 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
    animation: slide-top 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
    border-radius: 10px;
  
    .iconButton {
      color: #5F6C81;
      font-size: 16px;
      height: 25px;
      transition: all 0.3s linear;
    }
  
  
  
    .iconButtonWarn {
      color: #cf0001;
      font-size: 18px;
    }
  
    .iconButtonWarnAnim {
      color: #cf0001;
      font-size: 18px;
      animation: blink 1.1s cubic-bezier(.5, 0, 1, 1) infinite alternate;
    }
  
    .iconButton:hover {
      transform: scale(1.5);
    }
  }
  
  
  .region {
    border-radius: 5px;
    display: flow;
    // background-color: white;
    padding-left: 0px !important;
    padding-right: 0px !important;
    font-size: 10.9px;
    font-family: var(--editProjectFont) !important;
    margin-top: 2.5px;
    color: #45546E;
    margin-top: 10px;
  
  }
  
  .customer {
    border-radius: 5px;
    display: flow;
    // background-color: white;
    padding-left: 0px !important;
    padding-right: 0px !important;
    font-size: 10.9px;
    font-family: var(--editProjectFont) !important;
    margin-top: 2.5px;
    color: #45546E;
    margin-top: 10px;
  
  }
  
  .sow {
    border-radius: 5px;
    display: flow;
    // background-color: white;
    padding-left: 0px !important;
    padding-right: 0px !important;
    font-size: 10.9px;
    font-family: var(--editProjectFont) !important;
    margin-top: 2.5px;
    color: #45546E;
    margin-top: 10px;
  
  }
  
  .quote_position {
    border-radius: 5px;
    display: flow;
    // background-color: white;
    padding-left: 0px !important;
    padding-right: 0px !important;
    font-size: 10.9px;
    font-family: var(--editProjectFont) !important;
    margin-top: 2.5px;
    color: #45546E;
    margin-top: 10px;
  
  }