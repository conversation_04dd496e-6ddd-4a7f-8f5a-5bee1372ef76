<div class="sow-dashboard-styles" *ngIf="!showDetailsPage">
    <ng-container >
        <ng-container>
            <div class="row title pt-2 pb-1">

                <div class="icon" (click)="navigateToLandingPage()">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                        <g opacity="0.4" clip-path="url(#clip0_6114_39061)">
                            <path d="M6.81795 8L10 11.1113L9.09103 12L5 8L9.09103 4L10 4.88875L6.81795 8Z" fill="#111434" />
                        </g>
                        <defs>
                            <clipPath id="clip0_6114_39061">
                                <rect width="16" height="16" fill="white" />
                            </clipPath>
                        </defs>
                    </svg>
                </div>
                <span class="pl-3 my-auto item-name" style="padding-top:2px;" placement="top" showDelay="500">Project
                    Efficiency Dashboard</span>

                <span class="duration">
                    <input [(ngModel)]="applied_duration" type="text" class="filter-date" [showCustomRangeLabel]="true"
                        [locale]="{
                                applyLabel: 'Apply',
                                displayFormat: 'DD MMM YYYY',
                                customRangeLabel: 'Custom Range',
                                format: 'YYYY-MM-DD',
                                clearLabel: 'clear'
                            }" [alwaysShowCalendars]="true" [ranges]="dateRangePickerRanges" [linkedCalendars]="true"
                        ngxDaterangepickerMd placeholder="DD-MMM-YYYY - DD-MMM-YYYY" style=" width: 200px;
                                border: 0px;
                                background: #F0F2F7;
                                font-size: 12px;" (change)="onDateInputChange($event)" />
                </span>

                <div class="dashboard-option-styles">
                    <span class="settings-button">
                        <button mat-icon-button class="customise-area view-button-inactive" tooltip="Dashboard Settings"
                            [satPopoverAnchor]="showSummaryCard" (click)="showSummaryCard.toggle()" verticalAlign="below"
                            
                            horizontalAlign="before">
                            <mat-icon class="iconButton">settings</mat-icon>
                        </button>
                    </span>
                    <span class="download-button">
                        <button mat-icon-button class="customise-area view-button-inactive" tooltip="Download" (click)="downloadSOWDashboard()">
                            <mat-icon class="iconButton">cloud_download</mat-icon>
                        </button>
                    </span>
                </div>

                <sat-popover #showSummaryCard horizontalAlign="after" verticalAlign="below" hasBackdrop>


                    <div class="column-config-popup-card card" style=" width: 252px;height: 335px;overflow: hidden;">
                        <div class="column-config-popup-card-1">
                            <div class="column-list">
                                <span *ngFor="let column of summaryCards; let i =index">
                                    <div class="row" *ngIf="column.isActive && checkSummaryGroupByType(column, i)">
                                        <label class="toggle-switch"
                                            [style.background-color]="(column.isVisible ? button : 'lightgrey')">
                                            <input type="checkbox"
                                                [disabled]="false"
                                                [checked]="column.isVisible"
                                                (click)=" onSummaryCardChangeVisible(i, $event) ">
                                            <span class="slider"></span>
                                        </label>
                                        <span class="column-header">{{ column.header }}</span>
                                    </div>
                                </span>
                            </div>
                        </div>

                    </div>



                </sat-popover>




            </div>

            <hr style="margin-top: 4px;  margin-bottom: 16px;">

            <app-summary-card *ngIf="!loading" [summaryCards]="summaryCards" [dashboardColumn]="dashboardColumn"
                [summaryCardData]="this.summaryCardData" [startDate]="this.applied_duration.startDate"
                [endDate]="this.applied_duration.endDate" [duration]="this.duration"
                [showTotalFront]="this.showTotalFrontVal"
                [durationSelectedValue]="this.durationSelectedValue"
                (openDetailsPageEmit)="openDetailsPageEmit($event)"></app-summary-card>




            <div class="filter-container" *ngIf="!loading">


                <ng-container>
                    <div class="filter-title-display">


                        <div>
                            <div style="cursor: pointer" [matMenuTriggerFor]="groupByPopUp">
                                <div class="view-type-box-groupby">
                                    <p class="view-type-text"><span style="font-weight: 700;">View By: </span>
                                        {{ groupBySelectedValue === 'Position' ? 'Position/Activity' : groupBySelectedValue }}</p>
                                    <mat-icon class="view-type-icon">expand_more</mat-icon>
                                </div>
                            </div>

                        </div>

                        <!-- <div style="margin-left: 20px;">
                            <div style="cursor: pointer" [matMenuTriggerFor]="viewTypePopup">
                                <div class="view-type-box">
                                    <p class="view-type-text"><span style="font-weight: 700;">Views: </span> {{
                                        durationSelectedValue }}</p>
                                    <mat-icon class="view-type-icon">expand_more</mat-icon>
                                </div>
                            </div>

                        </div> -->

                        <div class="filter-content" style="margin-left: 23px;">
                            <span class="filter-title">
                                Filter
                            </span>
                        </div>

                        <div style="margin-left: 20px;" *ngIf="groupBySelectedValue =='Region'">
                            <div style="cursor: pointer" [matMenuTriggerFor]="regionTypePopUp">
                                <div class="view-type-box">
                                    <p class="view-type-text"><span style="font-weight: 700;">Region ({{this.region_ids.length}})</span> </p>
                                    <mat-icon class="view-type-icon">expand_more</mat-icon>
                                </div>
                            </div>

                        </div>

                        <div style="margin-left: 20px;" *ngIf="groupBySelectedValue =='Sales Region'">
                            <div style="cursor: pointer" [matMenuTriggerFor]="salesRegionTypePopUp">
                                <div class="view-type-box">
                                    <p class="view-type-text"><span style="font-weight: 700;">Sales Region ({{this.sales_region_ids.length}})</span> </p>
                                    <mat-icon class="view-type-icon">expand_more</mat-icon>
                                </div>
                            </div>

                        </div>

                        <div style="margin-left: 20px;" *ngIf="groupBySelectedValue =='Customer'">
                            <div style="cursor: pointer" [matMenuTriggerFor]="customerTypePopUp">
                                <div class="view-type-box">
                                    <p class="view-type-text"><span style="font-weight: 700;">Customer ({{this.customer_ids.length}})</span> </p>
                                    <mat-icon class="view-type-icon">expand_more</mat-icon>
                                </div>
                            </div>

                        </div>

                        <div style="margin-left: 20px;" *ngIf="groupBySelectedValue == 'Project'">
                            <div style="cursor: pointer" [matMenuTriggerFor]="sowTypePopUp">
                                <div class="view-type-box">
                                    <p class="view-type-text"><span style="font-weight: 700;">Project ({{this.sow_ids.length}})</span> </p>
                                    <mat-icon class="view-type-icon">expand_more</mat-icon>
                                </div>
                            </div>

                        </div>

                        <div style="margin-left: 20px;" *ngIf="groupBySelectedValue == 'Position'">
                            <div style="cursor: pointer" [matMenuTriggerFor]="positionTypePopUp">
                                <div class="view-type-box">
                                    <p class="view-type-text"><span style="font-weight: 700;">Position/Activity ({{this.position_ids.length}})</span> </p>
                                    <mat-icon class="view-type-icon">expand_more</mat-icon>
                                </div>
                            </div>

                        </div>



                        <div class="filter-column-customize">

                            <div class="row">
                                <div class="col-6" style="margin-top: 16px; display: flex;">
                                    <div style="margin-left:2px; margin-right: 6px; margin-top: 3px;" >
                                        <mat-checkbox width="16px" height="16px"
                                            [(ngModel)]="showTotalFrontVal"
                                            ></mat-checkbox>
                                    </div>
                                    <div class="text-style">
                                        Show total in front
                                    </div>

                                </div>
                                <div class="col-4" style="margin-top: 14px;">
                                    <div style="white-space: nowrap;  display: flex;">
                                        <span class="toggle-content" [ngClass]="{'toggle-content-glow': !durationToggle}">
                                            Month
                                        </span>
                                        <span style="margin-top:4px">
                                            <label class="toggle-switch" [style.background-color]="button">
                                                <input type="checkbox" [checked]="durationToggle"
                                                    (click)="changeDurationType()">
                                                <span class="slider"></span>
                                            </label>
                                        </span>
                                        <span class="toggle-content font-family" [ngClass]="{'toggle-content-glow': durationToggle}">
                                            Week
                                        </span>
                                    </div>
                                </div>

                                <div class="col-1">
                                    <button mat-icon-button class="customise-area view-button-inactive" tooltip="Column Selection"
                                        [satPopoverAnchor]="showColumnConfig" (click)="showColumnConfig.toggle()"
                                        verticalAlign="below" horizontalAlign="before">
                                        <mat-icon class="iconButton">display_settings</mat-icon>
                                    </button>
                                </div>
                            </div>
                    
                    
                        </div>

                    </div>



                    <mat-menu #viewTypePopup="matMenu">
                        <ng-template [matMenuContent]>
                            <div (click)="$event.stopPropagation()" class="view-type-popup">
                                <p class="view-type-title">GRID PERIODS</p>
                                <mat-radio-group class="radio-group">
                                    <mat-radio-button *ngFor="let item of viewType; let i = index" [value]="item"
                                        [checked]="item === durationSelectedValue"
                                        (change)="changeViewType(item, $event)">{{ item }}</mat-radio-button>
                                </mat-radio-group>
                            </div>
                        </ng-template>
                    </mat-menu>

                    <mat-menu #groupByPopUp="matMenu">
                        <ng-template [matMenuContent]>
                            <div (click)="$event.stopPropagation()" class="view-type-popup-groupBy">
                                <p class="view-type-title">VIEW BY</p>
                                <mat-radio-group class="radio-group">
                                    <mat-radio-button *ngFor="let item of groupByType; let i = index" [value]="item"
                                        [checked]="item === groupBySelectedValue" (change)="changeGroupByType(item, $event)"
                                        style="width: 180px;">{{ item === 'Position' ? 'Position/Activity' : item }}</mat-radio-button>
                                </mat-radio-group>
                            </div>
                        </ng-template>
                    </mat-menu>


                    <sat-popover #showColumnConfig horizontalAlign="after" verticalAlign="below" hasBackdrop>


                        <div class="card-column card"
                            style="width: 180px; height: 230px; overflow-y: auto; overflow-x: hidden;">
                            <div class="column-config-popup">
                                <div class="column-list">
                                    <span *ngFor="let column of dashboardColumn; let i =index">
                                        <div class="row" *ngIf="column.isActive && checkGroupByType(column)">
                                            <label class="toggle-switch"
                                                [style.background-color]="(column.isVisible ? button : 'lightgrey')">
                                                <input type="checkbox" [checked]="column.isVisible"
                                                    [disabled]="false"
                                                    (click)=" onDashboardColumnChangeVisible(i, $event)">
                                                <span class="slider"></span>
                                            </label>
                                            <span class="column-header">{{ column.header }}</span>
                                        </div>
                                    </span>
                                </div>
                            </div>

                        </div>



                    </sat-popover>




                    <mat-menu #regionTypePopUp="matMenu">
                        <ng-template [matMenuContent]>
                            <div (click)="$event.stopPropagation()" class="column-type-popup">
                                <p class="title-text">Region</p>
                                <div class="align-items-center">
                                    <div>
                                        <mat-checkbox width="16px" height="16px"
                                            (click)="onRegionIdsChange( 'all')"></mat-checkbox>
                                    </div>
                                    <div class="text-style">
                                        Select All
                                    </div>
                                </div>
                                <ng-container *ngFor="let item of region_list; let i = index">
                                    <div class="align-items-center">
                                        <div>
                                            <mat-checkbox width="16px" height="16px" [(ngModel)]="item.checked"
                                                (click)="onRegionIdsChange(i)"></mat-checkbox>
                                        </div>
                                        <div class="text-style" matToolTip="{{item.name}}">
                                            {{ item.name}}
                                        </div>
                                    </div>
                                </ng-container>

                            </div>
                        </ng-template>
                    </mat-menu>
                    
                    <mat-menu #salesRegionTypePopUp="matMenu">
                        <ng-template [matMenuContent]>
                            <div (click)="$event.stopPropagation()" class="column-type-popup">
                                <p class="title-text">Sales Region</p>
                                <div class="align-items-center">
                                    <div>
                                        <mat-checkbox width="16px" height="16px"
                                            (click)="onSalesRegionIdsChange( 'all')"></mat-checkbox>
                                    </div>
                                    <div class="text-style">
                                        Select All
                                    </div>
                                </div>
                                <ng-container *ngFor="let item of sales_region_list; let i = index">
                                    <div class="align-items-center">
                                        <div>
                                            <mat-checkbox width="16px" height="16px" [(ngModel)]="item.checked"
                                                (click)="onSalesRegionIdsChange(i)"></mat-checkbox>
                                        </div>
                                        <div class="text-style" matToolTip="{{item.name}}">
                                            {{ item.name}}
                                        </div>
                                    </div>
                                </ng-container>

                            </div>
                        </ng-template>
                    </mat-menu>


                    <mat-menu #customerTypePopUp="matMenu">
                        <ng-template [matMenuContent]>

                            <div (click)="$event.stopPropagation()" class="column-type-popup">
                                <p class="title-text">Customer</p>
                                <div class="align-items-center">
                                    <div>
                                        <mat-checkbox width="16px" height="16px"
                                            (click)="onCustomerIdsChange('all')"></mat-checkbox>
                                    </div>
                                    <div class="text-style">
                                        Select All
                                    </div>
                                </div>
                                <ng-container *ngFor="let item of customer_list; let i = index">
                                    <div class="align-items-center">
                                        <div>
                                            <mat-checkbox width="16px" height="16px" [(ngModel)]="item.checked"
                                                (click)="onCustomerIdsChange(i)"></mat-checkbox>
                                        </div>
                                        <div class="text-style" matToolTip="{{item.name}}">
                                            {{ item.name}}
                                        </div>
                                    </div>
                                </ng-container>

                            </div>

                        </ng-template>
                    </mat-menu>

                    <mat-menu #sowTypePopUp="matMenu">
                        <ng-template [matMenuContent]>

                            <div (click)="$event.stopPropagation()" class="column-type-popup">
                                <p class="title-text">Project</p>
                                <div class="align-items-center">
                                    <div>
                                        <mat-checkbox width="16px" height="16px"
                                            (click)="onSOWIdsChange('all')"></mat-checkbox>
                                    </div>
                                    <div class="text-style">
                                        Select All
                                    </div>
                                </div>
                                <ng-container *ngFor="let item of project_list; let i = index">
                                    <div class="align-items-center">
                                        <div>
                                            <mat-checkbox width="16px" height="16px" [(ngModel)]="item.checked"
                                                (click)="onSOWIdsChange(i)"></mat-checkbox>
                                        </div>
                                        <div class="text-style" matToolTip="{{item.name}}">
                                            {{ item.name}}
                                        </div>
                                    </div>
                                </ng-container>

                            </div>

                        </ng-template>
                    </mat-menu>

                    <mat-menu #positionTypePopUp="matMenu">
                        <ng-template [matMenuContent]>

                            <div (click)="$event.stopPropagation()" class="column-type-popup">
                                <p class="title-text">Position/Activity</p>
                                <div class="align-items-center">
                                    <div>
                                        <mat-checkbox width="16px" height="16px"
                                            (click)="onPositionIdsChange('all')"></mat-checkbox>
                                    </div>
                                    <div class="text-style">
                                        Select All
                                    </div>
                                </div>
                                <ng-container *ngFor="let item of position_list; let i = index">
                                    <div class="align-items-center">
                                        <div>
                                            <mat-checkbox width="16px" height="16px" [(ngModel)]="item.checked"
                                                (click)="onPositionIdsChange(i)"></mat-checkbox>
                                        </div>
                                        <div class="text-style" matToolTip="{{item.name}}">
                                            {{ item.name}}
                                        </div>
                                    </div>
                                </ng-container>

                            </div>

                        </ng-template>
                    </mat-menu>


                </ng-container>



            </div>

            <ng-container *ngIf="!reportLoading && !loading">
                <app-report-table-grid-url [ganttURL]="ganttURL"
                    (urlOutput)="goPreviousScreen($event)"></app-report-table-grid-url>
            </ng-container>



            <div *ngIf="!reportLoading && !loading" class="summary-data-report-table-grid">
                <app-report-table-grid [columns]="dcol" [projectData]="reportTableData" (nextLevel)="openNextLevel($event)"
                    [startDate]="this.applied_duration.startDate" [endDate]="this.applied_duration.endDate"
                    [duration]="this.duration" [durationSelectedValue]="this.durationSelectedValue"
                    [showTotalFront]="this.showTotalFrontVal"
                    [groupBySelectedValue]="this.groupBySelectedValue" class="report-table-grid-height">
                </app-report-table-grid>
            </div>


            <div class="row" *ngIf="reportLoading && !loading">
                <div class="col-12 d-flex justify-content-center mt-3">
                    <mat-spinner matTooltip="Please wait..." diameter="30"> </mat-spinner>
                </div>
            </div>
        </ng-container>

        <ng-container *ngIf="loading">
            <div class="row">
                <div class="col-12 d-flex justify-content-center mt-3">
                    <mat-spinner matTooltip="Please wait..." diameter="30"> </mat-spinner>
                </div>
            </div>
        </ng-container>
    </ng-container>

   
</div>

<ng-container *ngIf="showDetailsPage">
    <app-summary-details [dialogData]="this.summaryDetailsData" (closeDetails)="closeDetailsPage()"></app-summary-details>
</ng-container>