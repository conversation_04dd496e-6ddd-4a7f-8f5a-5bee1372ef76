import { Component, HostListener, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import {
  DateAdapter,
  MAT_DATE_FORMATS,
  MAT_DATE_LOCALE,
} from '@angular/material/core';
import { MAT_MOMENT_DATE_ADAPTER_OPTIONS, MomentDateAdapter } from '@angular/material-moment-adapter';
import moment from 'moment';
import * as _ from 'underscore';
import { SowDashboardService } from "../../services/sow-dashboard.service"
import { ToasterMessageService } from 'src/app/modules/project-management/shared-lazy-loaded/components/toaster-message/toaster-message.service';
import { FormBuilder } from '@angular/forms';
import { JsonToExcelService } from 'src/app/services/excel/json-to-excel.service';
import * as XLSX from 'xlsx';

@Component({
  selector: 'app-sow-dashboard',
  templateUrl: './sow-dashboard.component.html',
  styleUrls: ['./sow-dashboard.component.scss'],
  providers: [
    {
      provide: DateAdapter,
      useClass: MomentDateAdapter,
      deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS],
    },
    {
      provide: MAT_DATE_FORMATS,
      useValue: {
        parse: {
          dateInput: 'DD-MM-YYYY',
        },
        display: {
          dateInput: 'DD-MM-YYYY',
          monthYearLabel: 'MMM YYYY',
        },
      },
    },
  ],
})
export class SowDashboardComponent implements OnInit {

  @HostListener('window:resize')
  onResize() {
    this.calculateDynamicContentHeight();
  }

  constructor(private router: Router,
    public dashboardService: SowDashboardService,
    private toasterService: ToasterMessageService,
    private formBuilder: FormBuilder,
    private exceltoJson: JsonToExcelService
  ) {
    this.calculateDynamicContentHeight()
  }


  dcol = false;
  date_selected: any;
  formConfig: any;
  maxSummaryCard: number = 6;
  durationSelectedValue: any = "Month"

  loading: boolean = true;

  dynamicinnerHeight: any;
  dynamicHeight: any;

  showDetailsPage: boolean = false;
  summaryDetailsData: any;


  position_ids: any = [];
  region_ids: any = [];
  sales_region_ids: any=[];
  sow_ids: any = [];
  customer_ids: any = [];
  applied_duration: any;
  showTotalFrontVal: boolean = false;

  duration: any = [];

  position_list: any = []

  region_list: any = []

  project_list: any = []

  customer_list: any = []

  sales_region_list: any=[];

  dateRangePickerRanges = {
    'This Month': [moment().startOf('month'), moment().endOf('month')],
    'Last Month': [
      moment().subtract(1, 'month').startOf('month'),
      moment().subtract(1, 'month').endOf('month'),
    ],
    'Next Month': [
      moment().add(1, 'month').startOf('month'),
      moment().add(1, 'month').endOf('month'),
    ],
    'Upcoming 3 Months': [
      moment().startOf('month'),
      moment().add(2, 'month').endOf('month'),
    ],
    'This Year': [moment().startOf('year'), moment().endOf('year')],
    'Previous Year': [
      moment().subtract(1, 'year').startOf('year'),
      moment().subtract(1, 'year').endOf('year'),
    ],
    'Current FY': [
      moment().subtract(moment().month() < 3 ? 1 : 0, 'year').month(3).date(1).startOf('day'),
      moment().add(moment().month() >= 3 ? 1 : 0, 'year').month(2).date(31).endOf('day'),
    ],
    'Previous FY': [
      moment().subtract(moment().month() < 3 ? 2 : 1, 'years').month(3).date(1).startOf('day'),
      moment().subtract(moment().month() < 3 ? 1 : 0, 'years').month(2).date(31).endOf('day'),
    ]
  };


 


  dashboardColumn: any = [];
  summaryCards: any = [];
  button: any;
  shades: any;
  tabColor: any;
  fontStyle: any;
  scrollColor: any;

  viewType: any = ["Month", "Week"]
  groupByType: any = ["Region", "Sales Region", "Customer", "Project", "Position"]
  
  groupBySelectedValue: any = "Region";
  
  ganttURL: any = [
    {
      title: "Global",
      groupBySelectedValue: this.groupBySelectedValue,
      level: 1
    }
  ]

  durationToggle: boolean = false;

  summaryCardData: any;
  reportTableData: any = [];
  reportLoading: boolean = false;





  async ngOnInit() {

    this.calculateDynamicContentHeight()

    await this.dashboardService.getPMFormCustomizeConfigV().then((res) => {
      this.formConfig = res

      if (this.formConfig.length > 0) {
        const retrieveStyles = _.where(this.formConfig, { type: "project-theme", field_name: "styles", is_active: true });
        this.button = retrieveStyles.length > 0 ? retrieveStyles[0].data.button_color ? retrieveStyles[0].data.button_color : "#90ee90" : "#90ee90";
        document.documentElement.style.setProperty('--detailsTab', this.button)
        this.tabColor = retrieveStyles.length > 0 ? retrieveStyles[0].data.tab_color ? retrieveStyles[0].data.tab_color : "" : "";
        document.documentElement.style.setProperty('--detailsTabColor', this.tabColor)
        this.fontStyle = retrieveStyles.length > 0 ? retrieveStyles[0].data.font_style ? retrieveStyles[0].data.font_style : "Roboto" : "Roboto";
        document.documentElement.style.setProperty('--detailsTabFont', this.fontStyle);
        this.scrollColor = retrieveStyles.length > 0 ? retrieveStyles[0].data.scroll_color ? retrieveStyles[0].data.scroll_color : "#90ee90" : "#90ee90";
        document.documentElement.style.setProperty('--project2Scroll', this.scrollColor)
      }
    })

    await this.dashboardService.getBUList().then((res: any)=>{
      this.region_list = res['data']
      for (let region of this.region_list) {
        region['checked'] = false;
      }
    })

 


    this.dashboardColumn = [

      {
        'type': "milestone_plan",
        'header': this.getColumnName("milestone_plan", "Planned"),
        'isActive': this.getActiveColumn("milestone_plan"),
        'isVisible': true,
        'durationType': ["Month", "Week"],
        'groupByType': this.groupByType
      },
      {
        'type': "allocated_billable_hours",
        'header': this.getColumnName("allocated_billable_hours", "Allocated (B)"),
        'isActive': this.getActiveColumn("allocated_billable_hours"),
        'isVisible': true,
        'durationType': ["Month", "Week"],
        'groupByType': this.groupByType
      },

      {
        'type': "allocated_non_billable_flexi",
        'header': this.getColumnName("allocated_non_billable_flexi", "Allocated (NBF)"),
        'isActive': this.getActiveColumn("allocated_non_billable_flexi"),
        'isVisible': false,
        'durationType': ["Month", "Week"],
        'groupByType': this.groupByType
      },
      {
        'type': "allocated_sum_billable_non_billable_flexi",
        'header': this.getColumnName("allocated_sum_billable_non_billable_flexi", "Allocated (B+NBF)"),
        'isActive': this.getActiveColumn("allocated_sum_billable_non_billable_flexi"),
        'isVisible': false,
        'durationType': ["Month", "Week"],
        'groupByType': this.groupByType
      },
      {
        'type': "allocated_non_billable_growth",
        'header': this.getColumnName("allocated_non_billable_growth", "Allocated (NBG)"),
        'isActive': this.getActiveColumn("allocated_non_billable_growth"),
        'isVisible': false,
        'durationType': ["Month", "Week"],
        'groupByType': this.groupByType
      },
      {
        'type': "allocated_non_billable_hours",
        'header': this.getColumnName("allocated_non_billable_hours", "Allocated (NB)"),
        'isActive': this.getActiveColumn("allocated_non_billable_hours"),
        'isVisible': false,
        'durationType': ["Month", "Week"],
        'groupByType': this.groupByType
      },
      {
        'type': "allocated_hours",
        'header': this.getColumnName("allocated_hours", "Allocated (T)"),
        'isActive': this.getActiveColumn("allocated_hours"),
        'isVisible': false,
        'durationType': ["Month", "Week"],
        'groupByType': this.groupByType
      },
      {
        'type': "logged_billable_hours",
        'header': this.getColumnName("logged_billable_hours", "Logged (B)"),
        'isActive': this.getActiveColumn("logged_billable_hours"),
        'isVisible': true,
        'durationType': ["Month", "Week"],
        'groupByType': _.reject(this.groupByType, (res) => {
          if (res == "Position")
            return true;
        })
      },
      {
        'type': "logged_sum_billable_non_billable_flexi",
        'header': this.getColumnName("logged_sum_billable_non_billable_flexi", "Logged (B+NBF)"),
        'isActive': this.getActiveColumn("logged_sum_billable_non_billable_flexi"),
        'isVisible': false,
        'durationType': ["Month", "Week"],
        'groupByType': _.reject(this.groupByType, (res) => {
          if (res == "Position")
            return true;
        })
      },
      {
        'type': "logged_non_billable_flexi",
        'header': this.getColumnName("logged_non_billable_flexi", "Logged (NBF)"),
        'isActive': this.getActiveColumn("logged_non_billable_flexi"),
        'isVisible': false,
        'durationType': ["Month", "Week"],
        'groupByType': _.reject(this.groupByType, (res) => {
          if (res == "Position")
            return true;
        })
      },
      {
        'type': "logged_non_billable_growth",
        'header': this.getColumnName("logged_non_billable_growth", "Logged (NBG)"),
        'isActive': this.getActiveColumn("logged_non_billable_growth"),
        'isVisible': false,
        'durationType': ["Month", "Week"],
        'groupByType': _.reject(this.groupByType, (res) => {
          if (res == "Position")
            return true;
        })
      },
      {
        'type': "logged_non_billable_hours",
        'header': this.getColumnName("logged_non_billable_hours", "Logged (NB)"),
        'isActive': this.getActiveColumn("logged_non_billable_hours"),
        'isVisible': false,
        'durationType': ["Month", "Week"],
        'groupByType': _.reject(this.groupByType, (res) => {
          if (res == "Position")
            return true;
        })
      },
      {
        'type': "logged_hours",
        'header': this.getColumnName("logged_hours", "Logged (T)"),
        'isActive': this.getActiveColumn("logged_hours"),
        'isVisible': false,
        'durationType': ["Month", "Week"],
        'groupByType': _.reject(this.groupByType, (res) => {
          if (res == "Position")
            return true;
        })
      },

      {
        'type': "billable_hours",
        'header': this.getColumnName("billed_hours", "Billed"),
        'isActive': this.getActiveColumn("billed_hours"),
        'isVisible': true,
        'durationType': ["Month", "Week"],
        'groupByType': this.groupByType
      }
    ]




    this.summaryCards = [
      {
        type: "planned_vs_allocated_billable",
        header: this.getColumnName("planned_vs_allocated_billable", "Planned Vs Allocated (B)"),
        isActive: this.getActiveColumn("planned_vs_allocated_billable"),
        isVisible: true,
        columns: ["allocated_billable_hours", "milestone_plan"],
        durationType: ["Month", "Week"],
        value: 0
      },
      {
        type: "planned_vs_allocated_non_billable",
        header: this.getColumnName("planned_vs_allocated_non_billable", "Planned Vs Allocated (NB)"),
        isActive: this.getActiveColumn("planned_vs_allocated_non_billable"),
        isVisible: false,
        columns: ["allocated_non_billable_hours", "milestone_plan"],
        durationType: ["Month", "Week"],
        value: 0
      },
      {
        type: "planned_vs_allocated_total",
        header: this.getColumnName("planned_vs_allocated_total", "Planned Vs Allocated (T)"),
        isActive: this.getActiveColumn("planned_vs_allocated_total"),
        columns: ["allocated_hours", "milestone_plan"],
        durationType: ["Month", "Week"],
        isVisible: true,
        value: 0
      },
      {
        type: "logged_vs_allocated_billable",
        header: this.getColumnName("logged_vs_allocated_billable", "Allocated Vs Logged (B)"),
        isActive: this.getActiveColumn("logged_vs_allocated_billable"),
        columns: ["logged_billable_hours", "allocated_billable_hours"],
        durationType: ["Month", "Week"],
        isVisible: true,
        value: 0
      },
      {
        type: "logged_vs_allocated_non_billable",
        header: this.getColumnName("logged_vs_allocated_non_billable", "Allocated Vs Logged (NB)"),
        isActive: this.getActiveColumn("logged_vs_allocated_non_billable"),
        columns: ["logged_non_billable_hours", "allocated_non_billable_hours"],
        durationType: ["Month", "Week"],
        isVisible: false,
        value: 0
      },
      {
        type: "logged_vs_allocated_total",
        header: this.getColumnName("logged_vs_allocated_total", "Allocated Vs Logged (T)"),
        isActive: this.getActiveColumn("logged_vs_allocated_total"),
        columns: ["logged_hours", "allocated_hours"],
        durationType: ["Month", "Week"],
        isVisible: true,
        value: 0
      },
      {
        type: "planned_vs_logged_billable",
        header: this.getColumnName("planned_vs_logged_billable", "Planned Vs Logged (B)"),
        isActive: this.getActiveColumn("planned_vs_logged_billable"),
        columns: ["allocated_billable_hours", "milestone_plan"],
        durationType: ["Month", "Week"],
        isVisible: true,
        value: 0
      },
      {
        type: "planned_vs_logged_non_billable",
        header: this.getColumnName("planned_vs_logged_non_billable", "Planned Vs Logged (NB)"),
        isActive: this.getActiveColumn("planned_vs_logged_non_billable"),
        columns: ["allocated_non_billable_hours", "milestone_plan"],
        durationType: ["Month", "Week"],
        isVisible: false,
        value: 0
      },
      {
        type: "planned_vs_logged_total",
        header: this.getColumnName("planned_vs_logged_total", "Planned Vs Logged (T)"),
        isActive: this.getActiveColumn("planned_vs_logged_total"),
        columns: ["logged_hours", "milestone_plan"],
        durationType: ["Month", "Week"],
        isVisible: false,
        value: 0
      },
      {
        type: "planned_vs_billed",
        header: this.getColumnName("planned_vs_billed", "Planned Vs Billed"),
        isActive: this.getActiveColumn("planned_vs_billed"),
        columns: ["billable_hours", "milestone_plan"],
        durationType: ["Month", "Week"],
        isVisible: false,
        value: 0
      },
      {
        type: "allocated_vs_billed",
        header: this.getColumnName("allocated_vs_billed", "Allocated Vs Billed"),
        isActive: this.getActiveColumn("allocated_vs_billed"),
        columns: ["billable_hours", "allocated_hours"],
        durationType: ["Month", "Week"],
        isVisible: true,
        value: 0
      },
      {
        type: "logged_vs_billed",
        header: this.getColumnName("logged_vs_billed", "Logged Vs Billed"),
        isActive: this.getActiveColumn("logged_vs_billed"),
        columns: ["billable_hours", "logged_hours"],
        durationType: ["Month", "Week"],
        isVisible: false,
        value: 0
      },

    ]

    this.applied_duration = {
      startDate: moment().startOf('month').format(),
      endDate: moment().add(2, 'month').endOf('month').format()
    }





    this.dashboardService.dashboardConfig.columnList = this.dashboardColumn;



    await this.getSummaryCardData(this.applied_duration.startDate, this.applied_duration.endDate)

    await this.getSOWDashboardData(this.applied_duration.startDate, this.applied_duration.endDate)

    this.loading = false;



  }


  async getSummaryCardData(startDate, endDate) {
    await this.dashboardService.getSOWDashboardSummaryCardData(startDate, endDate).then((res) => {
      this.summaryCardData = res

      console.log("Summary Card Data:", this.summaryCardData)
    })
  }


  async getSOWDashboardData(startDate, endDate) {
    this.reportLoading = true;
    let filterData = {
      "region": this.region_ids,
      "salesRegion": this.sales_region_ids,
      "customer": this.customer_ids,
      "project": this.sow_ids,
      "position": this.position_ids
    }
    console.log("Filter Data", filterData)
    await this.dashboardService.getSOWDashboardData(startDate, endDate, this.groupBySelectedValue, this.durationSelectedValue, filterData).then((res) => {
      this.reportTableData = res;
      if (this.durationSelectedValue == "Week") {
        this.incrementByWeek(startDate, endDate)
      }
      else {
        this.incrementByMonth(startDate, endDate)
      }

      this.reportLoading = false;

      setTimeout(() => {
        this.dashboardService.sendColumns(this.dashboardColumn);
        console.log('column sent');
      }, 50);
    })
  }


  async openNextLevel(event) {

    this.reportLoading = true;


    let indexOf = _.indexOf(this.groupByType, this.groupBySelectedValue)

    let level = indexOf + 1



    if (level == 1) {
      this.region_ids = [event['id']]

      for (let item of this.region_list) {
        if (_.contains(this.region_ids, item['id'])) {
          item['checked'] = true;
        }
      }
      this.groupBySelectedValue = "Sales Region"

      this.dashboardService.getVerticalListFromBU(this.region_ids).then((res) => {
        this.sales_region_list = res
        for (let sales_region of this.sales_region_list) {
          sales_region['checked'] = false
        }
      })



    }
    if (level == 2) {
      this.sales_region_ids = [event['id']]

      for (let item of this.sales_region_list) {
        if (_.contains(this.sales_region_ids, item['id'])) {
          item['checked'] = true;
        }
      }
      this.groupBySelectedValue = "Customer"

      this.dashboardService.getCustomerList(this.sales_region_ids).then((res) => {
        this.customer_list = res
        for (let customer of this.customer_list) {
          customer['checked'] = false
        }
      })



    }
    else if (level == 3) {
      this.customer_ids = [event['id']]

      for (let item of this.customer_list) {
        if (_.contains(this.customer_ids, item['id'])) {
          item['checked'] = true;
        }
      }
      this.groupBySelectedValue = "Project"

      this.dashboardService.getProjectList(this.customer_ids).then((res) => {
        this.project_list = res
        for (let project of this.project_list) {
          project['checked'] = false
        }
      })

    }
    else if (level == 4) {
      this.sow_ids = [event['id']]

      for (let item of this.project_list) {
        if (_.contains(this.sow_ids, item['id'])) {
          item['checked'] = true;
        }
      }
      this.groupBySelectedValue = "Position"

      this.dashboardService.getPositionList(this.sow_ids).then((res) => {
        this.position_list = res
        for (let position of this.position_list) {
          position['checked'] = false;
        }
      })

    }
    else if (level == 5) {
      this.position_ids = [event['id']]

      for (let item of this.position_list) {
        if (_.contains(this.position_ids, item['id'])) {
          item['checked'] = true;
        }
      }

    }

    if (level < 5) {
      this.ganttURL.push({
        title: event['name'],
        level: level +1 ,
        id: event['id'],
        groupBySelectedValue: this.groupBySelectedValue,
        data: {
          region_ids: this.region_ids,
          sales_region_ids: this.sales_region_ids,
          sow_ids: this.sow_ids,
          customer_ids: this.customer_ids,
          position_ids: this.position_ids
        }
      })
    }



    if (level < 5)
      await this.getSOWDashboardData(this.applied_duration.startDate, this.applied_duration.endDate)

  }





  navigateToLandingPage() {
    this.router.navigateByUrl("/main/reports")
  }






  getColumnName(field_name, default_column_name) {

    let columnName = _.where(this.formConfig, { type: "sow_dashboard_dashboardColumn", field_name: field_name, is_active: true })

    if (columnName.length > 0) {
      return columnName[0]['label'] ? columnName[0]['label'] : default_column_name
    }
    else {
      return default_column_name
    }
  }

  getActiveColumn(field_name) {
    let columnName = _.where(this.formConfig, { type: "sow_dashboard_dashboardColumn", field_name: field_name })

    if (columnName.length > 0) {
      if (columnName[0]['is_active'])
        return true
      else
        return false;
    }
    else {
      return true;
    }
  }


  onSummaryCardChangeVisible(i, $event) {
    setTimeout(() => {

      console.log(this.summaryCards[i]['isVisible'])



      console.log(this.summaryCards, i, $event, this.summaryCards[i],)
      let visibleColumns = _.where(this.summaryCards, { isVisible: true, isActive: true })

      console.log(visibleColumns.length, this.maxSummaryCard, visibleColumns)
      if (this.summaryCards[i]['isVisible']) {
        if (visibleColumns.length <= 1) {
          this.toasterService.showWarning("Atleast 1 column to be selected!", 10000);
          this.summaryCards[i]['isVisible'] = true;
          $event.preventDefault()

        }
        else {
          this.summaryCards[i]['isVisible'] = false;
        }
      }
      else if (!this.summaryCards[i]['isVisible']) {
        if (this.maxSummaryCard <= visibleColumns.length) {
          this.toasterService.showWarning("More than " + (this.maxSummaryCard) + " cards cannot be selected!", 10000);
          this.summaryCards[i]['isVisible'] = false;
          $event.preventDefault()
        }
        else {
          this.summaryCards[i]['isVisible'] = true;

        }
      }
      let cards = []
      for (let summary of this.summaryCards) {
        cards.push(summary)
      }

      this.summaryCards = JSON.parse(JSON.stringify(cards))
    }, 100)


  }

  showErrorMessageSummaryCard(value, $event) {
    if (value == 1) {

      $event.preventDefault();
    }
    else if (value == 2) {

      $event.preventDefault();
    }
  }

  showErrorMessageDashboardCard(value, $event) {
    if (value == 1) {
      this.toasterService.showWarning("Atleast 1 column to be selected!", 10000);
      $event.preventDefault();
      return;
    }

  }

  onDashboardColumnChangeVisible(i, $event) {
    setTimeout(() => {


      let visibleColumns = _.where(this.dashboardColumn, { isVisible: true })


      if (this.dashboardColumn[i]['isVisible']) {
        if (visibleColumns.length <= 1) {
          this.toasterService.showWarning("Atleast 1 column to be selected!", 10000);
          this.dashboardColumn[i]['isVisible'] = true;
          $event.preventDefault()

        }
        else {
          this.dashboardColumn[i]['isVisible'] = false;
        }
      }
      else if (!this.dashboardColumn[i]['isVisible']) {
        this.dashboardColumn[i]['isVisible'] = true;
      }

      let cards = []
      for (let summary of this.dashboardColumn) {
        cards.push(summary)
      }

      this.dashboardColumn = JSON.parse(JSON.stringify(cards))

      setTimeout(() => {
        this.dashboardService.sendColumns(this.dashboardColumn);
        console.log('column sent');
      }, 50);
    }, 100)

  }

  onDashboardColumnChange() {


    this.dashboardService.dashboardConfig.columnList = this.dashboardColumn;

    setTimeout(() => {
      this.dashboardService.sendColumns(this.dashboardColumn);
      console.log('column sent');
    }, 50);
  }


  async changeGroupByType(value, $event) {
    this.groupBySelectedValue = value

    this.region_ids = [];
    this.customer_ids = [];
    this.sow_ids = [];
    this.position_ids = [];

    if (this.groupBySelectedValue == "Sales Region") {

    }
    if (this.groupBySelectedValue == "Project") {
      this.dashboardService.getProjectList([]).then((res) => {
        this.project_list = res
        for (let project of this.project_list) {
          project['checked'] = false
        }
      })

    }
    if (this.groupBySelectedValue == "Customer") {
      this.dashboardService.getCustomerList([]).then((res) => {
        this.customer_list = res
        for (let customer of this.customer_list) {
          customer['checked'] = false
        }
      })

    }
    if (this.groupBySelectedValue == "Position") {
      this.dashboardService.getPositionList([]).then((res) => {
        this.position_list = res
        for (let project of this.position_list) {
          project['checked'] = false
        }
      })
    }

    this.getSOWDashboardData(this.applied_duration.startDate, this.applied_duration.endDate)






  }

  startDateClicked($event) {

    if (this.durationSelectedValue == "Week") {
      this.applied_duration.startDate = moment($event.startDate._d).startOf('week').format();
      this.applied_duration.endDate = moment($event.startDate._d).endOf('week').format();


    }

    else {
      this.applied_duration.startDate = moment($event.startDate._d).startOf('month').format();
      this.applied_duration.endDate = moment($event.startDate._d).endOf('month').format();


    }
  }








  onSummaryCardChange(i) {
    console.log("Summary Card Changes!", this.summaryCards)
  }

  changeViewType(value, $event) {
    this.durationSelectedValue = value

    this.getSOWDashboardData(this.applied_duration.startDate, this.applied_duration.endDate)
  }

  onRegionIdsChange(i) {

    setTimeout(async () => {
      if (i == "all") {

        for (let item of this.region_list) {
          console.log(item)
          if (item.checked)
            item.checked = false;
          else
            item.checked = true;
        }
      }

      this.region_ids = _.pluck(_.where(this.region_list, { checked: true }), "id")

      console.log(this.region_ids)

      await this.dashboardService.getVerticalListFromBU(this.region_ids).then((res) => {
        this.sales_region_list = res
        for (let customer of this.sales_region_list) {
          customer['checked'] = false
        }
      })

      await this.getSOWDashboardData(this.applied_duration.startDate, this.applied_duration.endDate)
    }, 50)

  }

  onSalesRegionIdsChange(i) {

    setTimeout(async () => {
      if (i == "all") {

        for (let item of this.sales_region_list) {
          console.log(item)
          if (item.checked)
            item.checked = false;
          else
            item.checked = true;
        }
      }

      this.sales_region_ids = _.pluck(_.where(this.sales_region_list, { checked: true }), "id")



      await this.dashboardService.getCustomerList(this.sales_region_ids).then((res) => {
        this.customer_list = res
        for (let customer of this.customer_list) {
          customer['checked'] = false
        }
      })

      await this.getSOWDashboardData(this.applied_duration.startDate, this.applied_duration.endDate)
    }, 50)

  }

  onCustomerIdsChange(i) {
    setTimeout(async () => {
      if (i == "all") {

        for (let item of this.customer_list) {
          console.log(item)
          if (item.checked)
            item.checked = false;
          else
            item.checked = true;
        }
      }

      this.customer_ids = _.pluck(_.where(this.customer_list, { checked: true }), "id")
      console.log(this.customer_ids)

      await this.dashboardService.getProjectList(this.customer_ids).then((res) => {
        this.project_list = res
        for (let project of this.project_list) {
          project['checked'] = false
        }
      })

      await this.getSOWDashboardData(this.applied_duration.startDate, this.applied_duration.endDate)
    }, 50)
  }

  onSOWIdsChange(i) {
    setTimeout(async () => {
      if (i == "all") {

        for (let item of this.project_list) {
          console.log(item)
          if (item.checked)
            item.checked = false;
          else
            item.checked = true;
        }
      }

      this.sow_ids = _.pluck(_.where(this.project_list, { checked: true }), "id")
      console.log(this.sow_ids)

      await this.dashboardService.getPositionList(this.sow_ids).then((res) => {
        this.position_list = res
        for (let position of this.position_list) {
          position['checked'] = false;
        }
      })

      await this.getSOWDashboardData(this.applied_duration.startDate, this.applied_duration.endDate)
    }, 50)
  }

  onPositionIdsChange(i) {
    setTimeout(async () => {
      if (i == "all") {

        for (let item of this.position_list) {
          console.log(item)
          if (item.checked)
            item.checked = false;
          else
            item.checked = true;
        }
      }

      this.position_ids = _.pluck(_.where(this.position_list, { checked: true }), "id")
      console.log(this.position_ids)

      await this.getSOWDashboardData(this.applied_duration.startDate, this.applied_duration.endDate)
    }, 50)
  }



  async goPreviousScreen(url) {

    let indexOf = _.indexOf(this.groupByType, this.groupBySelectedValue)

    console.log("Index", indexOf)

    let ganttURLLength = indexOf + 1

    let level = url.level

    if (ganttURLLength == level) {
      return;
    }
    if (ganttURLLength != level) {
      console.log("Gantt URL", ganttURLLength, level)
      this.ganttURL = this.ganttURL.splice(0, level)
    }

    console.log(this.ganttURL, level, ganttURLLength)

    this.region_ids = []
    this.sales_region_ids=[];
    this.sow_ids = []
    this.customer_ids = []
    this.position_ids = []


    if (level == 1) {

      this.region_ids = []
      this.sales_region_ids = [];
      this.sow_ids = [];
      this.customer_ids = [];
      this.position_ids = [];

      this.groupBySelectedValue = "Region"
    }
    else if (level == 2) {

      this.region_ids  = this.ganttURL[this.ganttURL.length - 1]['data']['region_ids'];

      for (let item of this.region_list) {
        if (_.contains(this.region_ids, item['id'])) {
          item['checked'] = true;
        }
      }

      this.groupBySelectedValue = "Sales Region"

      this.dashboardService.getVerticalListFromBU(this.region_ids).then((res)=>{
        this.sales_region_list = res

        for (let sales_region of this.sales_region_list) {
          sales_region['checked'] = false
        }
      })

      console.log("SOW Dashboard Data")
      
    }

    else if (level == 3) {

      this.sales_region_ids = this.ganttURL[this.ganttURL.length - 1]['data']['sales_region_ids'];

      for (let item of this.sales_region_list) {
        if (_.contains(this.sales_region_ids, item['id'])) {
          item['checked'] = true;
        }
      }

      this.groupBySelectedValue = "Customer"

      this.dashboardService.getCustomerList(this.sales_region_ids).then((res) => {
        this.customer_list = res
        for (let customer of this.customer_list) {
          customer['checked'] = false
        }
      })
    }
    else if (level == 4) {
      this.customer_ids = this.ganttURL[this.ganttURL.length - 1]['data']['customer_ids'];

      for (let item of this.customer_list) {
        if (_.contains(this.customer_ids, item['id'])) {
          item['checked'] = true;
        }
      }
      this.groupBySelectedValue = "Project"

      this.dashboardService.getProjectList(this.customer_ids).then((res) => {
        this.project_list = res
        for (let project of this.project_list) {
          project['checked'] = false
        }
      })
    }
    else if (level == 5) {
      this.sow_ids = this.ganttURL[this.ganttURL.length - 1]['data']['sow_ids'];

      for (let item of this.project_list) {
        if (_.contains(this.sow_ids, item['id'])) {
          item['checked'] = true;
        }
      }
      this.groupBySelectedValue = "Position"

      this.dashboardService.getPositionList(this.sow_ids).then((res) => {
        this.position_list = res
        for (let project of this.position_list) {
          project['checked'] = false
        }
      })
    }


    this.getSOWDashboardData(this.applied_duration.startDate, this.applied_duration.endDate)
  }


  onDateInputChange(event) {

    let selectedDate = event;
    const newEndDateValue = selectedDate.endDate._d;
    const newStartDateValue = selectedDate.startDate._d;
    const newendDateValue = moment(newEndDateValue).format("YYYY-MM-DD")
    const newsndDateValue = moment(newStartDateValue).format("YYYY-MM-DD")

    console.log("APplied Duration", this.applied_duration, newendDateValue, newsndDateValue)
    this.getSOWDashboardData(this.applied_duration.startDate, this.applied_duration.endDate)
  }



  async incrementByMonth(startDate, endDate) {

    this.duration = [];
    let start = moment(startDate, "YYYY-MM-DD");
    let end = moment(endDate, "YYYY-MM-DD");

    // Check if the start date is before the end date
    if (start.isAfter(end)) {
      console.log("Start date must be before end date.");
      return;
    }

    // Loop to increment the date by one month at a time
    while (start.isBefore(end)) {

      let val = {
        month: start.month() + 1,
        year: start.year(),
        title: start.format("MMM YY"),
        chart_title: start.format("MMM YY")
      }

      //this.duration.push(val)
      console.log(start.format("YYYY-MM-DD"));
      start.add(1, 'months');

      this.duration.push(val)


    }


  }

  async incrementByWeek(startDate, endDate) {

    this.duration = [];

    let start = moment(startDate, "YYYY-MM-DD");
    let end = moment(endDate, "YYYY-MM-DD");

    // Check if the start date is before the end date
    if (start.isAfter(end)) {
      console.log("Start date must be before end date.");
      return;
    }

    // Loop to increment the date by one month at a time
    while (start.isBefore(end)) {

      let val = {
        month: start.month() + 1,
        year: start.year(),
        week: start.week(),
        title: "Week " + start.week() + " (" + moment(start).startOf('week').format("DD MMM") + " - " + moment(start).endOf('week').format("DD MMM") + ")",
        chart_title: "Week " + start.week()
      }

      //this.duration.push(val)
      console.log(start.format("YYYY-MM-DD"));
      start.add(1, 'week');

      this.duration.push(val)



    }


  }

  calculateDynamicContentHeight() {


    const height = window.innerHeight - 70;
    const innerheight = height - 160;
    this.dynamicHeight = (window.innerHeight - 55) + 'px';
    this.dynamicinnerHeight = innerheight + 'px';
    document.documentElement.style.setProperty(
      '--detailsHeight',
      this.dynamicHeight
    );
    console.log("HEIGHT", height, this.dynamicHeight)
    document.documentElement.style.setProperty(
      '--innerTabHeight',
      this.dynamicinnerHeight
    );



  }

  changeDurationType() {

    this.durationToggle = !this.durationToggle
    if (this.durationToggle) {
      this.durationSelectedValue = "Week"
    }
    else {
      this.durationSelectedValue = "Month"
    }

    this.getSOWDashboardData(this.applied_duration.startDate, this.applied_duration.endDate)


  }


  openDetailsPageEmit(event) {
    this.summaryDetailsData = event
    this.showDetailsPage = true;
  }

  closeDetailsPage() {
    this.showDetailsPage = false;
    this.getSOWDashboardData(this.applied_duration.startDate, this.applied_duration.endDate)
  }


  downloadSOWDashboard() {
    let filterData = {
      "region": this.region_ids,
      "salesRegion": this.sales_region_ids,
      "customer": this.customer_ids,
      "project": this.sow_ids,
      "position": this.position_ids
    }

    let mainHeader =[];
    let subHeader = [];
    this.dashboardService.downloadSOWDashboard(this.applied_duration.startDate, this.applied_duration.endDate, filterData, this.durationSelectedValue, this.groupBySelectedValue).then((res) => {

      let columnCount =0;
      let headerCount =0;
      // Create a new workbook
      const wb = XLSX.utils.book_new();
      const wsData = [];

      // Define the headers with merged cells
      let months = Object.keys(res['data'][0]).filter(key => (key !== "Region")  && (key !== "Sales Region") && (key!=="Customer") && (key!=="Project") && (key!=="Position"));

      mainHeader = []
      let valHeader = []
      for(let type of this.groupByType)
      {
        if(type === 'Position')
        { 
          mainHeader.push('Position/Activity')
          valHeader.push('Position/Activity')
          subHeader.push("")
        }else{
          mainHeader.push(type)
          valHeader.push(type)
          subHeader.push("")
        }
        headerCount++;
        if(this.groupBySelectedValue == type)
        {
           break;
        }
      }
   
   
      console.log("Main rehader", mainHeader)

      for(let column of this.dashboardColumn)
      {
    
        if(column.isVisible && column.isActive)
        {
          columnCount++;
          
        }
      }
      console.log(months)

      for(let month of months) 
      {
        console.log(mainHeader)
        mainHeader.push(month);
   
        for(let column of this.dashboardColumn)
        {
     
          if(column.isVisible && column.isActive)
          {
            subHeader.push(column.header);
            mainHeader.push("")
          }

        }
        mainHeader.pop()

        
        
      }

      console.log(columnCount)

      // Add headers to the worksheet data
      wsData.push(mainHeader);
      wsData.push(subHeader);

      // Add data rows to the worksheet data
      for(let regionData of res['data'])
      {
        console.log(regionData)
        let rowData = [];
        for(let col of valHeader)
        {
            console.log(col, regionData[col])
            rowData.push(regionData[col])
        }

        for(let month of months)
        {

          
          for(let column of this.dashboardColumn)
          {
            if(column.isVisible && column.isActive)
            {
              rowData.push(regionData[month] ? regionData[month][column['header']] : "")
            }
          }

        }
        wsData.push(rowData);
      }

      // Create a worksheet from the data
      const ws = XLSX.utils.aoa_to_sheet(wsData);

      // Define merges for the month headers
      ws['!merges'] = [];
      months.forEach((month, index) => {
        const colStart = headerCount + index * (columnCount);
        const colEnd = colStart + (columnCount- 1);
        ws['!merges'].push({
            s: { r: 0, c: colStart },
            e: { r: 0, c: colEnd }
        });
    });

      // Append the worksheet to the workbook
      XLSX.utils.book_append_sheet(wb, ws,"Report");

      // Write the workbook to a file
      XLSX.writeFile(wb, res['file_name']+' ('+moment().format("DD-MMM-YYYY")+').xlsx');


    })
  }


  checkGroupByType(column) {
    return _.contains(column['durationType'], this.durationSelectedValue)
  }

  checkSummaryGroupByType(column, i) {

    this.summaryCards[i]['isActive'] = _.contains(column['durationType'], this.durationSelectedValue)
    return _.contains(column['durationType'], this.durationSelectedValue)

  }


}
