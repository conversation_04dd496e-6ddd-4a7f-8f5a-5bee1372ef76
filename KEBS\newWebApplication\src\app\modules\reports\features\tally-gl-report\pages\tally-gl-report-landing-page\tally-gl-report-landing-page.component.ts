import { Component, OnInit, ViewChild, Inject, ElementRef } from '@angular/core';
import CustomStore from "devextreme/data/custom_store";
import { TallyGlReportService } from '../../services/tally-gl-report.service';
import PivotGridDataSource from "devextreme/ui/pivot_grid/data_source";
import {
  DxDataGridComponent,
  DxPivotGridComponent,
} from "devextreme-angular";
import {
  FormControl,
  FormBuilder,
  Validators,
  FormArray
} from "@angular/forms";
import { MatSnackBar } from "@angular/material/snack-bar";
import * as _ from "underscore";

import sweetAlert from 'sweetalert2';
import {MatDialog} from '@angular/material/dialog';
import * as moment from 'moment';
import { MomentDateAdapter, MAT_MOMENT_DATE_ADAPTER_OPTIONS } from '@angular/material-moment-adapter';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core';
import { Router } from '@angular/router';

@Component({
  selector: 'app-tally-gl-report-landing-page',
  templateUrl: './tally-gl-report-landing-page.component.html',
  styleUrls: ['./tally-gl-report-landing-page.component.scss'],
  providers: [
    {
      provide: DateAdapter,
      useClass: MomentDateAdapter,
      deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS]
    },
    {
      provide: MAT_DATE_FORMATS,
      useValue: {
        parse: {
          dateInput: "DD-MM-YYYY"
        },
        display: {
          dateInput: "DD-MM-YYYY",
          monthYearLabel: "MMM YYYY"
        },
        useUtc: true
      }
    },
  ]
})
export class TallyGlReportLandingPageComponent implements OnInit {

  @ViewChild(DxPivotGridComponent) pivotGrid: DxPivotGridComponent;
  @ViewChild("cardScroll", { read: ElementRef })
  public cardScroll: ElementRef<any>;

  dataSource: any;
  allowSearch: any;
  filters: string;

  storageKey = "dx-widget-gallery-pivotgrid-storing-tally-gl-report";
  applicationId = 69;
  showDataFields: boolean = true;
  showRowFields: boolean = true;
  showColumnFields: boolean = true;
  showFilterFields: boolean = true;
  displayView: boolean = true;
  visibleDeleteView: any;
  currentView: any;
  views: any;
  isAuthorized = true;
  roleAccessFilters: any = "";
  versionName = new FormControl("", [Validators.required]);
  errorMessage : any;
  pivotGrid1: any;
  
  startDate: any;
  endDate: any;
  financialYear: any;
  fyStartMonth: any;

  dateRange: any;
  minEndDate: any;
  maxEndDate: any;
  onFetchRecord=false;
  postingPeriod: any;
  startDateFromStorage: any;
  endDateFromStorage: any;

  constructor(
    private _tallyGlReportService: TallyGlReportService,
    private snackBar: MatSnackBar,
    private dialog: MatDialog,
    private fb: FormBuilder,
    private _router: Router

  ) {
    this.startDateFromStorage = localStorage.getItem("tallyGlReportStartDate");
    this.endDateFromStorage = localStorage.getItem("tallyGlReportEndDate");
   
    let startDate = this.startDateFromStorage ? moment(this.startDateFromStorage).format('YYYY-MM-DD') : moment().subtract(12, 'months').format('YYYY-MM-DD');
    let endDate = this.endDateFromStorage ? moment(this.endDateFromStorage).format('YYYY-MM-DD') : moment().endOf('month').format('YYYY-MM-DD');

    this.dateRange = this.fb.group({
      startDate : [startDate, Validators.required],
      endDate : [ endDate, Validators.required]
    })
    this.startDate = this.dateRange.controls.startDate.value;
    this.endDate = this.dateRange.controls.endDate.value;
    
    this.minEndDate = new Date(this.dateRange.controls.startDate.value);
    this.maxEndDate = new Date(moment(this.dateRange.controls.startDate.value).add(12, 'months').format('YYYY-MM-DD'));
   }

 async ngOnInit(){
  await this.reportAuthorizationValidation();
  if (this.isAuthorized == true) {
    this.fetchTallyGLReport();
    this.getConfigData();
  }
  else {
    this._router.navigateByUrl("/main/reports");
    return this.snackBar.open(this.errorMessage, 'Dismiss', { duration: 2000 });
  }
      }
  
  onStartDateChange(){
    let startDate = new Date(this.dateRange.controls.startDate.value);
    let endDate = new Date(this.dateRange.controls.endDate.value);
    let diffInMonths = endDate.getMonth() - startDate.getMonth() +
        12 * (endDate.getFullYear() - startDate.getFullYear());

    if (diffInMonths > 12 || diffInMonths < 0) {
      this.dateRange.controls.endDate.patchValue(moment(startDate).add(12, 'months').format('YYYY-MM-DD'));
      this.minEndDate = startDate;
      this.maxEndDate = new Date(this.dateRange.controls.endDate.value);
    }
    else{
      this.minEndDate = startDate;
      this.maxEndDate = moment(startDate).add(12, 'months').format('YYYY-MM-DD');
    
      if(endDate<startDate || moment(endDate).format('YYYY-MM-DD')>this.maxEndDate){
        this.dateRange.controls.endDate.patchValue(moment(startDate).add(12, 'months').format('YYYY-MM-DD'))
      }
    }
  }

  onFySelection(){
    localStorage.setItem("tallyGlReportStartDate", this.dateRange.controls.startDate.value);
    localStorage.setItem("tallyGlReportEndDate", this.dateRange.controls.endDate.value);
    this.onFetchRecord = true;
    this.refresh();
  }

  onInitialized(e) {
    this.pivotGrid1 = e.component;
  }

  refresh() {
    this.pivotGrid1.getDataSource().reload();
  }
  reset() {
    this.pivotGrid1.getDataSource().state({});
  }

  fetchTallyGLReport = async () => {
    if(!this.startDateFromStorage || !this.endDateFromStorage){
      let getPostingPeriodDetails = await this._tallyGlReportService.getPostingPeriodDetails();

      if(getPostingPeriodDetails['messType']=='S'){
        this.postingPeriod = getPostingPeriodDetails['data']['posting_period'][0]['date'];
        let postingPeriodStartDate = moment(this.postingPeriod).startOf('month').format('YYYY-MM-DD');
        this.dateRange.controls.startDate.patchValue(moment(postingPeriodStartDate).format('YYYY-MM-DD'));
        this.dateRange.controls.endDate.patchValue(moment(postingPeriodStartDate).add(1, 'months').endOf('month').format('YYYY-MM-DD'));
        this.minEndDate = new Date(this.dateRange.controls.startDate.value);
        this.maxEndDate = new Date(moment(this.dateRange.controls.startDate.value).add(12, 'months').format('YYYY-MM-DD'));
      }
    }

    this.filters = "IncomeStatementReport";
    this.dataSource = new PivotGridDataSource({
      fields: [
        {
          caption: "Legal Entity",
          width: 120,
          dataField: "entity_name",
          area: "row",
          expanded: true
        },
        {
          caption: "GL Level 1",
          width: 120,
          dataField: "GL Level 1",
          area: "row"

        },
        {
          caption: "GL Level 2",
          width: 120,
          dataField: "GL Level 2",
          area: "row"

        },
        {
          caption: "GL Level 3",
          width: 120,
          dataField: "GL Level 3",
          area: "row"

        },
        {
          caption: "GL code",
          width: 120,
          dataField: "gl_code",
          area: "filter",
        },
        {
          caption: "GL name",
          width: 120,
          dataField: "gl_description",
          area: "row"

        },
        {
          caption: "Cost Center",
          width: 120,
          dataField: "cost_centre",
          area: "filter",
        },
        {
          caption: "Vertical",
          width: 120,
          dataField: "pl_name",
          area: "filter",
        },
        {
          caption: "FY",
          width: 120,
          dataField: "fy",
          area: "filter",
        },
        {
          caption: "Year",
          dataField: "period_year",
          area: "column",
          expanded: true
        },
        {
          caption: "Month",
          dataField: "period_month",
          dataType: "date",
          area: "column",
          groupInterval: "month",
          expanded: true
        },
        {
          caption: "Type",
          width: 120,
          dataField: "type",
          area: "column",
        },
        {
          caption: "Value",
          width: 120,
          dataField: "valueFormatted",
          dataType: "number",
          summaryType: "sum",
          format: {
            formatter: (valueFormatted: any) => {
              return valueFormatted.toFixed(2);
            },
            parser: (valueFormatted) => {
              return (Number(valueFormatted));
            }
          },
          area: "filter"
        },
        {
          caption: "Full Value",
          width: 120,
          dataField: "value",
          dataType: "number",
          summaryType: "sum",
          format: {
            type: "fixedPoint",
            precision: 2
          },
          area: "data"
        },
        {
          caption: "Currency",
          width: 120,
          dataField: "entity_currency_code",
          dataType: "string",
          area: "column",
          expanded: true
        }
      ],
      store: new CustomStore({
        load: function () {
          this.onFetchRecord = true;
          return this._tallyGlReportService.getTallyGlReport(this.dateRange.controls.startDate.value, this.dateRange.controls.endDate.value)
            .then(data => {
              console.log("tally Gl report data");
              console.log(data);
              this.onFetchRecord = false;
              return data;
            });
        }.bind(this)
      })
    });
  };


  async getConfigData() {
    await this._tallyGlReportService.getReportUserViews(this.applicationId).subscribe(res => {
      this.views = res;
      for (let i = 0; i < this.views.allViews.length; i++) {
        console.log(i)
        this.views.allViews[i].visibleDeleteView = false;
      }

      if (this.views.activeView.length > 0 && this.views) {

        this.enableDisplayView();

        this.currentView = 0;
        localStorage.setItem(this.storageKey, this.views.activeView[0].saved_config);
        this.pivotGrid1.getDataSource().state(JSON.parse(this.views.activeView[0].saved_config))
        if (this.views.activeView[0].field_config.length > 0)
          this.setFieldChoosers(JSON.parse(this.views.activeView[0].field_config)[0])
        else {
          let field_conf = {

            showDataFields: true,
            showRowFields: true,
            showColumnFields: true,
            showFilterFields: true
          };
          this.setFieldChoosers(field_conf)
        }
      }
      console.log(this.views);
    }, err => {

      this.enableDisplayView();
      this.snackBar.open("Error Retrieving Report Views! Try Refreshing", "Dismiss");
      console.log("Error retrieving views")
    });
  }


  stateUpdate = async () => {
    let temp = await localStorage.getItem(this.storageKey);
    if (typeof temp == "string") temp = JSON.parse(temp)
    let field_conf = [{

      showDataFields: this.showDataFields,
      showRowFields: this.showRowFields,
      showColumnFields: this.showColumnFields,
      showFilterFields: this.showFilterFields
    }];
    console.log(temp)
    if (temp && this.views && this.views.allViews.length > 0) {
      this._tallyGlReportService.updateReportState(temp, this.views.allViews[this.currentView].customization_id, field_conf, this.applicationId).subscribe(res => {
        console.log(res);

        this.enableDisplayView();
        this.snackBar.open("Report Version - " + this.views.allViews[this.currentView].config_name + " was updated Successfully!", "Dismiss");
        this.getConfigData();

      }, err => {
        this.snackBar.open("Unable to Update the Current Report Version! Try Again", "Dismiss");
        console.log(err)
      })
    }

    else {
      this.snackBar.open("No Report Versions Found.Kindly Use SaveAs!", "Dismiss");
    }
  }
  saveState() {
    // console.log("Here")
    let temp = localStorage.getItem(this.storageKey);
    // console.log("Here")
    // console.log(temp)
    // console.log(typeof (this.currentState));
    if (typeof temp == "string") temp = JSON.parse(temp)
    // console.log(this.sundayGovService.value)
    let field_conf = [{

      showDataFields: this.showDataFields,
      showRowFields: this.showRowFields,
      showColumnFields: this.showColumnFields,
      showFilterFields: this.showFilterFields
    }];
    console.log(this.applicationId)
    this._tallyGlReportService.saveReportState(temp, this.versionName.value, field_conf, this.applicationId).subscribe(res => {
      console.log(res);

      this.enableDisplayView();
      this.snackBar.open("Report Version - " + this.versionName.value + " was created Successfully!", "Dismiss");
      this.getConfigData();
    }, err => {

      this.snackBar.open("Unable to create the Report Version! Try Again", "Dismiss");
      console.log(err)
    })

  }

  changeView(index) {
    this.currentView = index;
    console.log(index)
    localStorage.setItem(this.storageKey, this.views.allViews[index].saved_config);
    let temp = localStorage.getItem(this.storageKey);
    console.log("Here")
    console.log(temp)
    this.pivotGrid1.getDataSource().state(JSON.parse(this.views.allViews[index].saved_config));
    this.setFieldChoosers(JSON.parse(this.views.allViews[index].field_config)[0])
    this.enableDisplayView();
  }
  deleteVariant(index) {
    let name = this.views.allViews[index].config_name;
    this.confirmSweetAlert("Do you want to Delete " + name + " Variant?").then((deleteConfirm) => {
      if (deleteConfirm.value) {
        console.log(deleteConfirm.value)
        this._tallyGlReportService.deleteVariant(this.applicationId, this.views.allViews[index].customization_id).subscribe(res => {
          this.getConfigData();
          this.snackBar.open("Variant " + name + " was Deleted Succesfully", "Dismiss");

        }, err => {
          this.snackBar.open("Failed to delete Variant " + name + ".", "Dismiss")
        })
      }
    })
  }
  showDataFieldsFn() {
    this.showDataFields = !this.showDataFields;
    if (this.showDataFields == true) {

      this.snackBar.open("Displaying Data Fields!", "Dismiss", { duration: 1000 });
    } else {
      this.snackBar.open("Data Fields Hidden!", "Dismiss", { duration: 1000 });
    }
  };
  showRowFieldsFn() {
    this.showRowFields = !this.showRowFields;
    if (this.showRowFields == true) {

      this.snackBar.open("Displaying Row Fields!", "Dismiss", { duration: 1000 });
    } else {
      this.snackBar.open("Row Fields Hidden!", "Dismiss", { duration: 1000 });
    }
  };
  showColumnFieldsFn() {
    this.showColumnFields = !this.showColumnFields;
    if (this.showColumnFields == true) {

      this.snackBar.open("Displaying Column Fields!", "Dismiss", { duration: 1000 });
    } else {
      this.snackBar.open("Column Fields Hidden!", "Dismiss", { duration: 1000 });
    }
  };
  showFilterFieldsFn() {
    this.showFilterFields = !this.showFilterFields;
    if (this.showFilterFields == true) {

      this.snackBar.open("Displaying Filter Fields!", "Dismiss", { duration: 1000 });
    } else {
      this.snackBar.open("Filter Fields Hidden!", "Dismiss", { duration: 1000 });
    }
  };
  toggleEditView() {
    this.displayView = !this.displayView;
  }
  enableDisplayView() {
    this.displayView = true;
  }

  scrollRight() {
    this.cardScroll.nativeElement.scrollTo({
      left: this.cardScroll.nativeElement.scrollLeft + 1060,
      behavior: "smooth"
    });
  }

  scrollLeft() {
    this.cardScroll.nativeElement.scrollTo({
      left: this.cardScroll.nativeElement.scrollLeft - 1060,
      behavior: "smooth"
    });
  }

  confirmSweetAlert(title) {
    let template = {
      customClass: {
        title: "title-class",
        confirmButton: "confirm-button-class",
        cancelButton: "confirm-button-class"
      },
      title: title,
      // text: text,
      type: "warning",
      showConfirmButton: true,
      showCancelButton: true
    }

    return sweetAlert.fire(template)
  }

  setFieldChoosers(fieldVisibiltyData) {
    console.log(fieldVisibiltyData)
    this.showDataFields = fieldVisibiltyData.showDataFields;
    this.showRowFields = fieldVisibiltyData.showRowFields;
    this.showColumnFields = fieldVisibiltyData.showColumnFields;
    this.showFilterFields = fieldVisibiltyData.showFilterFields;
  }
  resetFieldChoosers() {
    this.showDataFields = false;
    this.showRowFields = false;
    this.showColumnFields = false;
    this.showFilterFields = false;
  }

  onCellPrepared(e) {
    if (e.area == "data") {
      if (e.cell.value) {
        if (_.contains(e.cell.columnPath, "INR")) {
          let text = "₹ " + new Intl.NumberFormat('en-IN', {}).format(e.cell.value)
          e.cellElement.setAttribute('title', text);
          e.cellElement.innerHTML = text;
        } else{
          let format_value = new Intl.NumberFormat('en-US', {}).format(e.cell.value)
          if(_.contains(e.cell.columnPath, "USD")){
            format_value = "$ " + format_value;
          }
          e.cellElement.setAttribute('title', format_value);
          e.cellElement.innerHTML = format_value;
        }
      }
    }

  }

  //Gets Current Information
  async getInfo() {
    const { DisplayCurrencyComponent } = await import(
      'src/app/modules/reports/features/mis-report/components/display-currency/display-currency.component'
    );
    const openHierarchyViewComponent = this.dialog.open(
      DisplayCurrencyComponent,
      { height: '80%', width: '40%' }
    );

    openHierarchyViewComponent
      .afterClosed()
      .subscribe(async (result: any) => {});
  }

  async reportAuthorizationValidation(){
    try{
    this.roleAccessFilters = await this._tallyGlReportService.reportAuthorizationValidation();
    if (this.roleAccessFilters.messType == "E"){
      this.isAuthorized = false;
    }
  }
  catch (error) {
    this.isAuthorized = false;
     this.errorMessage = error?. error?.errorMessage || "Error in Retrieving Income statment Report";
     this.snackBar.open(this.errorMessage, 'Dismiss', { duration: 2000 });
  }
  }

}
