import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { ErrorService } from 'src/app/services/error/error.service';
import { catchError } from 'rxjs/operators';
import { EMPTY } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class TallyGlReportService {

  private application_id: number = 256;
  private application_object: number = 6;

  constructor(
    private http:HttpClient , private errorService: ErrorService
  ) { }


  getTallyGlReport(startDate, endDate) {
    return new Promise((resolve, reject) => {
      this.http.post("/api/project/getTallyGlReportData", {startDate, endDate,
        application_id : this.application_id,
         application_object : this.application_object
      }).subscribe(res => {
        return resolve(res)
      }, err => {
        return reject(err)
      })
    })
  }

  saveReportState(state, name, field_conf, application_id) {
    return this.http.post("api/userExperience/saveReportState", { state: state, name: name, field_conf: field_conf, application_id: application_id }).pipe(
      catchError(err => {
        console.log(err);
        this.errorService.userErrorAlert((err && err.code) ? err.code : (err && err.error) ? err.error.code : 'NIL', "Error while retrieving Column info list", (err && err.params) ? err.params : (err && err.error) ? err.error.params : {})
        return EMPTY;
      })
    );
  }
  updateReportState(state, customization_id, field_conf, application_id) {
    return this.http.post("api/userExperience/updateReportState", { application_id, state: state, customization_id: customization_id, field_conf: field_conf }).pipe(
      catchError(err => {
        console.log(err);
        this.errorService.userErrorAlert((err && err.code) ? err.code : (err && err.error) ? err.error.code : 'NIL', "Error while retrieving Column info list", (err && err.params) ? err.params : (err && err.error) ? err.error.params : {})
        return EMPTY;
      })
    );
  }
  getReportUserViews(application_id) {
    return this.http.post("api/userExperience/getReportUserViews", { application_id: application_id }).pipe(
      catchError(err => {
        console.log(err);
        this.errorService.userErrorAlert((err && err.code) ? err.code : (err && err.error) ? err.error.code : 'NIL', "Error while retrieving Column info list", (err && err.params) ? err.params : (err && err.error) ? err.error.params : {})
        return EMPTY;
      })
    )
  }
  deleteVariant(application_id, customization_id) {
    return this.http.post("api/userExperience/deleteVariant", { application_id: application_id, customization_id: customization_id }).pipe(
      catchError(err => {
        console.log(err);
        this.errorService.userErrorAlert((err && err.code) ? err.code : (err && err.error) ? err.error.code : 'NIL', "Error while retrieving Column info list", (err && err.params) ? err.params : (err && err.error) ? err.error.params : {})
        return EMPTY;
      })
    )
  }

  getPostingPeriodDetails(){
    try{
      return new Promise((resolve,reject)=>{
        this.http.post('api/project/getPostingPeriodDetails',{
          application_id : this.application_id,
         application_object : this.application_object
        })
        .subscribe((res)=>{
          return resolve(res);
        },(err)=>{
          return reject(err);
        })
      })
    }
    catch(err){
      return Promise.reject(err);
    }
  }

  reportAuthorizationValidation() {
    return new Promise((resolve, reject) => {
      this.http.post("/api/invoice/checkRoleAccessForUser", {
        application_id : this.application_id,
        application_object : this.application_object
      }).subscribe(res => {
        return resolve(res)
      }, err => {
        return reject(err)
      })
    })
  }
}
