.dashboard-style {
    .expand-icon {
        font-size: 24px !important;
        color: #4d4d4b !important;
    }

    .expand-button {
        width: 38px !important;
        height: 38px !important;
        line-height: 55px !important;
    }
    .filter-header{
        padding-left: 60px;
    }
    .apply-btn{
        border: 1px solid #df514c;
        background-color: #df514c ;
        color: white;
        font-family: 'Roboto';
        width: 45px;
        height: 45px;
        border-radius: 3px;
    }
    .btn-div{
        line-height: 55px;
    }

    ::ng-deep {
        ngx-daterangepicker-material {
        & {
         .md-drppicker {
            width: 750px;
            top: 0px;
         }
        }
       }
    }
    .filter-applied-circle {
        position: absolute;
        bottom: 35px;
        left: 12px;
        width: 10px;
        height: 10px;
        border-radius: 50%;
        background-color: #EE4961;
      }
}