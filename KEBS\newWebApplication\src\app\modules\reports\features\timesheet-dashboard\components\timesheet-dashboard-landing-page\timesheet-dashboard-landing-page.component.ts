import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { Validators } from 'ngx-editor';
import { TimesheetDashboardService } from '../../services/timesheet-dashboard.service';
import { ToasterService } from 'src/app/services/toaster-service/toaster.service';
import { ErrorService } from 'src/app/services/error/error.service';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { LoginService } from 'src/app/services/login/login.service';
import * as moment from 'moment';
import { MatDialog } from '@angular/material/dialog';
@Component({
  selector: 'app-timesheet-dashboard-landing-page',
  templateUrl: './timesheet-dashboard-landing-page.component.html',
  styleUrls: ['./timesheet-dashboard-landing-page.component.scss'],
})
export class TimesheetDashboardLandingPageComponent implements OnInit {
  applicationId: Number = 565;

  isLoading: boolean = false;

  entityData = [];

  divisionData = [];

  subDivisionData = [];

  employmentTypeData = [];

  employmentStatusData = [];

  locationData = [];

  costCenterData = [];

  timesheetStatusData = [];

  leaveTypesData = [];

  holidayCalendarData = [];

  workScheduleData = [];

  billingTypeData = [];

  dashboardData = [];

  defaultRowShow: Number = 1;

  showMoreFilters: boolean = false;

  duration: any;

  filterData = [
    {
      id: 1,
      filterName: 'Entity',
      filterLabel: 'Entity',
      filterData: this.entityData,
      formControlName: 'entity',
      displayFilter: true,
      displayRow: 1,
    },
    {
      id: 2,
      filterName: 'Division',
      filterLabel: 'Division',
      filterData: this.divisionData,
      formControlName: 'division',
      displayFilter: true,
      displayRow: 1,
    },
    {
      id: 3,
      filterName: 'Sub Division',
      filterLabel: 'Sub Division',
      filterData: this.subDivisionData,
      formControlName: 'sub_division',
      displayFilter: true,
      displayRow: 1,
    },
    {
      id: 4,
      filterName: 'Employment Type',
      filterLabel: 'Employment Type',
      filterData: this.employmentTypeData,
      formControlName: 'employment_type',
      displayFilter: true,
      displayRow: 1,
    },
    {
      id: 5,
      filterName: 'Employment Status',
      filterLabel: 'Employment Status',
      filterData: this.employmentStatusData,
      formControlName: 'employment_status',
      displayFilter: false,
      displayRow: 2,
    },
    {
      id: 6,
      filterName: 'Location',
      filterLabel: 'Location',
      filterData: this.locationData,
      formControlName: 'location',
      displayFilter: false,
      displayRow: 2,
    },
    {
      id: 7,
      filterName: 'Project Code - Project Name',
      filterLabel: 'Project Code - Project Name',
      filterData: this.costCenterData,
      formControlName: 'cost_centre',
      displayFilter: false,
      displayRow: 2,
    },
    {
      id: 8,
      filterName: 'Timesheet Status',
      filterLabel: 'Timesheet Status',
      filterData: this.timesheetStatusData,
      formControlName: 'timesheet_status',
      displayFilter: false,
      displayRow: 2,
    },
    {
      id: 9,
      filterName: 'Leave Type',
      filterLabel: 'Leave Type',
      filterData: this.leaveTypesData,
      formControlName: 'leave_type',
      displayFilter: false,
      displayRow: 2,
    },
    {
      id: 10,
      filterName: 'Holiday Calendar',
      filterLabel: 'Holiday Calendar',
      filterData: this.holidayCalendarData,
      formControlName: 'holiday_calendar',
      displayFilter: false,
      displayRow: 2,
    },
    {
      id: 11,
      filterName: 'Work Schedule',
      filterLabel: 'Work Schedule',
      filterData: this.workScheduleData,
      formControlName: 'work_schedule',
      displayFilter: false,
      displayRow: 2,
    },
    {
      id: 12,
      filterName: 'Billing Type',
      filterLabel: 'Billing Type',
      filterData: this.billingTypeData,
      formControlName: 'billing_type',
      displayFilter: false,
      displayRow: 2,
    },
    {
      id: 13,
      filterName: 'Duration',
      filterLabel: 'Duration',
      filterData: [],
      formControlName: 'dateRange',
      displayFilter: true,
      displayRow: 1,
    },
  ];
  selected: any;
  alwaysShowCalendars: boolean;

  invalidDates: moment.Moment[] = [
    moment().add(2, 'days'),
    moment().add(3, 'days'),
    moment().add(5, 'days'),
  ];

  seriesData = [
    {
      alignment: 'left',
      caption: 'Associate Id',
      dataField: 'associate_id',
      hasSummaryType: false,
      visible: true,
    },
    {
      alignment: 'left',
      caption: 'Employee Name',
      dataField: 'employee_name',
      hasSummaryType: false,
      visible: true,
    },
    {
      alignment: 'left',
      caption: 'Status',
      dataField: 'status',
      hasSummaryType: false,
      visible: true,
    },
    {
      alignment: 'left',
      caption: 'Available From',
      dataField: 'available_from',
      hasSummaryType: false,
      visible: true,
    },
    {
      alignment: 'left',
      caption: 'Entity',
      dataField: 'entity',
      hasSummaryType: false,
      visible: true,
    },
    {
      alignment: 'left',
      caption: 'Division',
      dataField: 'division',
      hasSummaryType: false,
      visible: true,
    },
    {
      alignment: 'left',
      caption: 'Sub Division',
      dataField: 'sub_division',
      hasSummaryType: false,
      visible: true,
    },
  ];

  appliedFilter: boolean = false;

  protected onDestroy = new Subject<void>();

  filterForm: FormGroup = this.fb.group({
    entity: ['', Validators.required],
    division: ['', Validators.required],
    sub_division: ['', Validators.required],
    employment_type: ['', Validators.required],
    employment_status: ['', Validators.required],
    location: ['', Validators.required],
    cost_centre: ['', Validators.required],
    timesheet_status: ['', Validators.required],
    leave_type: ['', Validators.required],
    holiday_calendar: ['', Validators.required],
    billing_type: ['', Validators.required],
    work_schedule: ['', Validators.required],
    dateRange: ['', Validators.required],
  });

  currentUser: any;

  applyBtn: boolean = false;

  constructor(
    private fb: FormBuilder,
    private _timesheetDashboardService: TimesheetDashboardService,
    private _toasterService: ToasterService,
    private _errorService: ErrorService,
    private _loginService: LoginService,
    private _dialog: MatDialog
  ) {
    this.alwaysShowCalendars = true;
    this.filterForm.get('dateRange').setValue({
      startDate: moment().startOf('month').format('DD-MMM-YYYY'),
      endDate: moment().endOf('month').format('DD-MMM-YYYY'),
    });
  }

  ngOnInit(): void {
    this.currentUser = this._loginService.getProfile().profile;
    this.getEntityData();
    this.getDivisionData();
    this.getSubDivisionData();
    this.getEmploymentTypeData();
    this.getEmploymentStatusData();
    this.getLocationData();
    this.getCostCenterData();
    this.getTimesheetStatusData();
    this.getLeaveData();
    this.getHolidayCalendarData();
    this.getWorkScheduleData();
    this.getBilingTypeData();
    this.loadTimesheetDashboard();
  }

  onPointClick(e) {
    e.target.select();
  }

  getEntityData() {
    this._timesheetDashboardService
      .getEntityData()
      .pipe(takeUntil(this.onDestroy))
      .subscribe(
        async (res) => {
          if (res['data'] && res['messType'] == 'S') {
            this.entityData = res['data'];
            this.filterData[0].filterData = this.entityData;
          }
        },
        (err) => {
          this._errorService.userErrorAlert(
            err && err.code
              ? err.code
              : err && err.error
              ? err.error.code
              : 'NIL',
            'Kindly Try After SomeTime',
            err && err.params
              ? err.params
              : err && err.error
              ? err.error.params
              : {}
          );
        }
      );
  }

  getDivisionData() {
    this._timesheetDashboardService
      .getDivisionData()
      .pipe(takeUntil(this.onDestroy))
      .subscribe(
        async (res) => {
          if (res['data'] && res['messType'] == 'S') {
            this.divisionData = res['data'];
            this.filterData[1].filterData = this.divisionData;
          }
        },
        (err) => {
          this._errorService.userErrorAlert(
            err && err.code
              ? err.code
              : err && err.error
              ? err.error.code
              : 'NIL',
            'Kindly Try After SomeTime',
            err && err.params
              ? err.params
              : err && err.error
              ? err.error.params
              : {}
          );
        }
      );
  }

  getSubDivisionData() {
    this._timesheetDashboardService
      .getSubDivisionData()
      .pipe(takeUntil(this.onDestroy))
      .subscribe(
        async (res) => {
          if (res['data'] && res['messType'] == 'S') {
            this.subDivisionData = res['data'];
            this.filterData[2].filterData = this.subDivisionData;
          }
        },
        (err) => {
          this._errorService.userErrorAlert(
            err && err.code
              ? err.code
              : err && err.error
              ? err.error.code
              : 'NIL',
            'Kindly Try After SomeTime',
            err && err.params
              ? err.params
              : err && err.error
              ? err.error.params
              : {}
          );
        }
      );
  }

  getEmploymentTypeData() {
    this._timesheetDashboardService
      .getEmploymentTypeData()
      .pipe(takeUntil(this.onDestroy))
      .subscribe(
        async (res) => {
          if (res['data'] && res['messType'] == 'S') {
            this.employmentTypeData = res['data'];
            this.filterData[3].filterData = this.employmentTypeData;
          }
        },
        (err) => {
          this._errorService.userErrorAlert(
            err && err.code
              ? err.code
              : err && err.error
              ? err.error.code
              : 'NIL',
            'Kindly Try After SomeTime',
            err && err.params
              ? err.params
              : err && err.error
              ? err.error.params
              : {}
          );
        }
      );
  }

  getLocationData() {
    this._timesheetDashboardService
      .getLocationData()
      .pipe(takeUntil(this.onDestroy))
      .subscribe(
        async (res) => {
          if (res['data'] && res['messType'] == 'S') {
            this.locationData = res['data'];
            this.filterData[5].filterData = this.locationData;
          }
        },
        (err) => {
          this._errorService.userErrorAlert(
            err && err.code
              ? err.code
              : err && err.error
              ? err.error.code
              : 'NIL',
            'Kindly Try After SomeTime',
            err && err.params
              ? err.params
              : err && err.error
              ? err.error.params
              : {}
          );
        }
      );
  }

  getEmploymentStatusData() {
    this._timesheetDashboardService
      .getEmploymentStatusData()
      .pipe(takeUntil(this.onDestroy))
      .subscribe(
        async (res) => {
          if (res['data'] && res['messType'] == 'S') {
            this.employmentStatusData = res['data'];
            this.filterData[4].filterData = this.employmentStatusData;
          }
        },
        (err) => {
          this._errorService.userErrorAlert(
            err && err.code
              ? err.code
              : err && err.error
              ? err.error.code
              : 'NIL',
            'Kindly Try After SomeTime',
            err && err.params
              ? err.params
              : err && err.error
              ? err.error.params
              : {}
          );
        }
      );
  }

  getCostCenterData() {
    this._timesheetDashboardService
      .getCostCenterData(this.currentUser.aid, this.currentUser.oid)
      .pipe(takeUntil(this.onDestroy))
      .subscribe(
        async (res) => {
          if (res['data'] && res['messType'] == 'S') {
            this.costCenterData = res['data'];
            this.filterData[6].filterData = this.costCenterData;
          }
        },
        (err) => {
          this._errorService.userErrorAlert(
            err && err.code
              ? err.code
              : err && err.error
              ? err.error.code
              : 'NIL',
            'Kindly Try After SomeTime',
            err && err.params
              ? err.params
              : err && err.error
              ? err.error.params
              : {}
          );
        }
      );
  }

  getTimesheetStatusData() {
    this._timesheetDashboardService
      .getTimesheetStatusData()
      .pipe(takeUntil(this.onDestroy))
      .subscribe(
        async (res) => {
          if (res['data'] && res['messType'] == 'S') {
            this.timesheetStatusData = res['data'];
            this.filterData[7].filterData = this.timesheetStatusData;
          }
        },
        (err) => {
          this._errorService.userErrorAlert(
            err && err.code
              ? err.code
              : err && err.error
              ? err.error.code
              : 'NIL',
            'Kindly Try After SomeTime',
            err && err.params
              ? err.params
              : err && err.error
              ? err.error.params
              : {}
          );
        }
      );
  }

  getLeaveData() {
    this._timesheetDashboardService
      .getLeaveData()
      .pipe(takeUntil(this.onDestroy))
      .subscribe(
        async (res) => {
          if (res['data'] && res['messType'] == 'S') {
            this.leaveTypesData = res['data'];
            this.filterData[8].filterData = this.leaveTypesData;
          }
        },
        (err) => {
          this._errorService.userErrorAlert(
            err && err.code
              ? err.code
              : err && err.error
              ? err.error.code
              : 'NIL',
            'Kindly Try After SomeTime',
            err && err.params
              ? err.params
              : err && err.error
              ? err.error.params
              : {}
          );
        }
      );
  }

  getHolidayCalendarData() {
    this._timesheetDashboardService
      .getHolidayCalendarData()
      .pipe(takeUntil(this.onDestroy))
      .subscribe(
        async (res) => {
          if (res['data'] && res['messType'] == 'S') {
            this.holidayCalendarData = res['data'];
            this.filterData[9].filterData = this.holidayCalendarData;
          }
        },
        (err) => {
          this._errorService.userErrorAlert(
            err && err.code
              ? err.code
              : err && err.error
              ? err.error.code
              : 'NIL',
            'Kindly Try After SomeTime',
            err && err.params
              ? err.params
              : err && err.error
              ? err.error.params
              : {}
          );
        }
      );
  }

  getWorkScheduleData() {
    this._timesheetDashboardService
      .getWorkScheduleData()
      .pipe(takeUntil(this.onDestroy))
      .subscribe(
        async (res) => {
          if (res['data'] && res['messType'] == 'S') {
            this.workScheduleData = res['data'];
            this.filterData[10].filterData = this.workScheduleData;
          }
        },
        (err) => {
          this._errorService.userErrorAlert(
            err && err.code
              ? err.code
              : err && err.error
              ? err.error.code
              : 'NIL',
            'Kindly Try After SomeTime',
            err && err.params
              ? err.params
              : err && err.error
              ? err.error.params
              : {}
          );
        }
      );
  }

  getBilingTypeData() {
    this._timesheetDashboardService
      .getBilingTypeData()
      .pipe(takeUntil(this.onDestroy))
      .subscribe(
        async (res) => {
          if (res['data'] && res['messType'] == 'S') {
            this.billingTypeData = res['data'];
            this.filterData[11].filterData = this.billingTypeData;
          }
        },
        (err) => {
          this._errorService.userErrorAlert(
            err && err.code
              ? err.code
              : err && err.error
              ? err.error.code
              : 'NIL',
            'Kindly Try After SomeTime',
            err && err.params
              ? err.params
              : err && err.error
              ? err.error.params
              : {}
          );
        }
      );
  }

  showAllFilters(flag) {
    if (flag == 1) {
      this.showMoreFilters = true;
      for (let items of this.filterData) {
        items.displayFilter = true;
      }
    } else {
      this.showMoreFilters = false;
      for (let items of this.filterData) {
        if (items.displayRow == 2) items.displayFilter = false;
      }
    }
  }

  async loadTimesheetDashboard() {
    let costCenterFilter = await this.getCostCenterId(
      this.filterForm.get('cost_centre').value
        ? this.filterForm.get('cost_centre').value
        : []
    );
    let mainFilterArray = {
      ['MLE.entity_id']: this.filterForm.get('entity').value
        ? this.filterForm.get('entity').value
        : [],
      ['MED.id']: this.filterForm.get('division').value
        ? this.filterForm.get('division').value
        : [],
      ['MESD.id']: this.filterForm.get('sub_division').value
        ? this.filterForm.get('sub_division').value
        : [],
      ['MEET.id']: this.filterForm.get('employment_type').value
        ? this.filterForm.get('employment_type').value
        : [],
      ['MEES.id']: this.filterForm.get('employment_status').value
        ? this.filterForm.get('employment_status').value
        : [],
      ['TETD.cost_centre_id']: costCenterFilter.dept.concat(costCenterFilter.project),
      ['TPI2.id']: [],
      ['MTOL.id']: this.filterForm.get('location').value
        ? this.filterForm.get('location').value
        : [],
      ['MTVS.id']: this.filterForm.get('timesheet_status').value
        ? this.filterForm.get('timesheet_status').value
        : [],
      ['MDT.id']: this.filterForm.get('leave_type').value
        ? this.filterForm.get('leave_type').value
        : [],
      ['MHL.id']: [],
      ['MWS.id']: this.filterForm.get('work_schedule').value
        ? this.filterForm.get('work_schedule').value
        : [],
      ['MEHC.id']: this.filterForm.get('holiday_calendar').value
        ? this.filterForm.get('holiday_calendar').value
        : [],
      ['MTBD.id']: this.filterForm.get('billing_type').value
        ? this.filterForm.get('billing_type').value
        : [],
      ['TEEC.associate_id']: [],
    };
    this.appliedFilter = Object.values(mainFilterArray).some(value => Array.isArray(value) && value.length > 0);
    this.dashboardData = [];

    this.duration = {
      startDate: this.filterForm.get('dateRange').value
        ? moment(this.filterForm.get('dateRange').value.startDate).format(
            'YYYY-MM-DD'
          )
        : moment().startOf('month').format('YYYY-MM-DD'),
      endDate: this.filterForm.get('dateRange').value
        ? moment(this.filterForm.get('dateRange').value.endDate).format(
            'YYYY-MM-DD'
          )
        : moment().endOf('month').format('YYYY-MM-DD'),
    };
    this.applyBtn = true;
    this._timesheetDashboardService
      .getDashboardDataBasedOnRole(
        this.currentUser.aid,
        this.currentUser.oid,
        mainFilterArray,
        this.duration
      )
      .pipe(takeUntil(this.onDestroy))
      .subscribe(
        async (res) => {
          if (res && res['data'] && res['messType'] == 'S') {
            this.dashboardData = res['data'];
            this.applyBtn = false;
          } else {
            this.applyBtn = false;
          }
        },
        (err) => {
          this.applyBtn = false;
          this._errorService.userErrorAlert(
            err && err.code
              ? err.code
              : err && err.error
              ? err.error.code
              : 'NIL',
            'Kindly Try After SomeTime',
            err && err.params
              ? err.params
              : err && err.error
              ? err.error.params
              : {}
          );
        }
      );
  }

  isInvalidDate = (m: moment.Moment) => {
    return this.invalidDates.some((d) => d.isSame(m, 'day'));
  };

  async getCostCenterId(value) {
    let dept_cc_id = [],
      project_cc_id = [];
    if (value.length > 0) {
      for (let items of value) {
        for (let innerItems of this.costCenterData) {
          if (items == innerItems.id && innerItems.cost_center_type == 'I') {
            project_cc_id.push(items);
          } else if (
            items == innerItems.id &&
            innerItems.cost_center_type != 'I'
          ) {
            dept_cc_id.push(items);
          }
        }
      }
    }
    return Promise.resolve({
      dept: dept_cc_id,
      project: project_cc_id,
    });
  }

  async openDashboardFilters() {
    const { TimesheetDashboardFilterComponent } = await import(
      '../../lazy-loaded-components/timesheet-dashboard-filter/timesheet-dashboard-filter.component'
    );
    const dialogRef = this._dialog.open(TimesheetDashboardFilterComponent, {
      height: '57%',
      position: { top: '0px', left: '10%', right: '5%' },
      data: {
        filterData: this.filterData,
        formData: this.filterForm,
      },
    });

    dialogRef.afterClosed().subscribe(async (result) => {
      if (result.ops == 2) {
        this.filterForm.reset();
        this.filterData = result.filterData;
      } else if (result.ops == 1) {
        this.filterData = result.filterData;
        this.filterForm = result.filterForm;
      }
      this.loadTimesheetDashboard();
    });
  }

  ngOnDestroy() {
    this.onDestroy.next();
    this.onDestroy.complete();
  }
}
