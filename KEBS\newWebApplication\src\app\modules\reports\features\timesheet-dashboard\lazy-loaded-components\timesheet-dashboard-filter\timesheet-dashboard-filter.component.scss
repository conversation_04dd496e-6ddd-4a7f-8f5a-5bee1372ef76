.dashboard-filter-style{
   .headerText{
    color: #000000;
    font-family: 'Roboto';
    font-weight: 700;
    font-size: 14px;
    line-height: 16px;
   }
   .view-button-inactive{
    color: #45546E;
    font-size: 13px;
    font-weight: bolder;
    cursor: pointer;
   }
   ::ng-deep .mat-form-field-infix{
      display: flex;
      height: 40px;
   }
  
  ::ng-deep .mat-icon-button{
      bottom: 15px;
  }
  .apply-btn{
   background-color: #EE4961;
   font-family: 'Roboto';
   font-weight: 700;
   font-size: 14px;
   line-height: 16px;
   color: #FFFFFF;
   border: 1px solid #EE4961;
   border-radius: 4px;
   width: 90px;
   height: 40px;
  }
  .clear-btn{
   font-family: 'Roboto';
   font-weight: 700;
   font-size: 14px;
   line-height: 16px;
   background-color: #F7F9FB;
   color: #000000;
   border-radius: 4px;
   border: 1px solid #F7F9FB;
   width: 90px;
   height: 40px;
  }
  .label-text{
   font-family: 'Roboto';
   font-weight: 500;
   font-size: 12px;
   line-height: 16px;
   color: #00000080;
  } 
}