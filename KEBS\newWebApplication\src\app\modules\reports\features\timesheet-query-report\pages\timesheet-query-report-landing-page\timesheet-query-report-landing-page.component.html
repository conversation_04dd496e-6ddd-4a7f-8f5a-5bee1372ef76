<div class="p-0 container-fluid ts-admin-persona">
    <div class="col-12 p-2">
        <div class="row">
            <div class="col-2">
                <mat-form-field appearance="outline">
                    <mat-label>Select Report</mat-label>
                    <mat-select [(ngModel)]="selectedReport">
                        <mat-option [value]="option.value" *ngFor="let option of report" (click)="callToCheckDateComponent(option)">{{ option.report_name
                            }}</mat-option>
                    </mat-select>
                </mat-form-field>
            </div>
            <div class="col-2 pl-4" *ngIf="isDateComponentNeedToShown">
                <mat-form-field appearance="outline">
                    <mat-label>Choose Month</mat-label>
                    <input matInput [matDatepicker]="dp"   placeholder="MMMM-YYYY"  [formControl]="dateControl">
                    <mat-datepicker-toggle matIconSuffix [for]="dp"></mat-datepicker-toggle>
                    <mat-datepicker #dp
                                    startView="multi-year"
                                    (monthSelected)="setMonthAndYear($event, dp)"
                                    >
                    </mat-datepicker>
                  </mat-form-field>
            </div>
            <div class="col-2 pl-4 row" *ngIf="isCheckboxNeedToShown">
                <section class="example-section">
                    <mat-checkbox class="example-margin" [(ngModel)]="inactive">Inactive & Exit Employees</mat-checkbox>
                </section>
            </div>
            <div class="col-2 pl-5 row" *ngIf="isCheckboxNeedToShown">
                <section class="example-section">
                    <mat-slide-toggle [(ngModel)]="autoTS"> Auto TS Employees </mat-slide-toggle>
                    <!-- <mat-checkbox class="example-margin" [(ngModel)]="autoTS">Auto Timesheet Employees</mat-checkbox> -->
                </section>
            </div>
            <div class="col-2 pl-5">
                <button mat-raised-button color="primary" (click)="downloadReport()" [disabled]="buttonDisable">View and Download Report</button>
            </div>
        </div>
    </div>
    <div class="col-12" *ngIf="selectedReport == 1 && downloadData?.length > 0">
        <dx-data-grid
            #dataGrid
            class="data-grid"
            [height]="dynamicGridReportHeight"
            [dataSource]="downloadData"
            [showBorders]="true"
            [columnAutoWidth]="true"
            [showColumnLines]="true"
            [showRowLines]="true"
          >
            <dxo-filter-row [visible]="true"></dxo-filter-row>
            <dxo-header-filter [visible]="true"></dxo-header-filter>
            <dxo-column-chooser
              [allowSearch]="true"
              [enabled]="true"
              mode="select"
            ></dxo-column-chooser>
            <dxo-scrolling mode="infinite"></dxo-scrolling>
            <dxi-column caption="Employee Id" dataField="Employee Id"></dxi-column>
            <dxi-column caption="Microsoft O365 Id" dataField="Microsoft O365 Id"></dxi-column>
            <dxi-column caption="Employee Name" dataField="Employee Name"></dxi-column>
            <dxi-column caption="Date Of Joining" dataField="Date Of Joining"></dxi-column>
            <dxi-column caption="Employment Type" dataField="Employment Type"></dxi-column>
            <dxi-column caption="Employment Status" dataField="Employment Status"></dxi-column>
            <dxi-column caption="Entity" dataField="Entity"></dxi-column>
            <dxi-column caption="Division" dataField="Division"></dxi-column>
            <dxi-column caption="Sub Division" dataField="Sub Division"></dxi-column>
            <dxi-column caption="Timesheet Status" dataField="Timesheet Status"></dxi-column>
            <dxi-column caption="Timesheet Month & Year" dataField="Timesheet Month & Year"></dxi-column>
            <dxi-column caption="Week Status" dataField="Week Status"></dxi-column>
            <dxi-column caption="Direct Manager" dataField="Direct Manager"></dxi-column>
            <dxi-column caption="Approvers" dataField="Approvers"></dxi-column>
          </dx-data-grid>
    </div>
    <div class="col-12" *ngIf="selectedReport == 2 && downloadData?.length > 0">
        <dx-data-grid
            #dataGrid
            class="data-grid"
            [height]="dynamicGridReportHeight"
            [dataSource]="downloadData"
            [showBorders]="true"
            [columnAutoWidth]="true"
            [showColumnLines]="true"
            [showRowLines]="true"
          >
            <dxo-filter-row [visible]="true"></dxo-filter-row>
            <dxo-header-filter [visible]="true"></dxo-header-filter>
            <dxo-column-chooser
              [allowSearch]="true"
              [enabled]="true"
              mode="select"
            ></dxo-column-chooser>
            <dxo-scrolling mode="infinite"></dxo-scrolling>
            <dxi-column caption="Employee Id" dataField="Employee Id"></dxi-column>
            <dxi-column caption="Employee Name" dataField="Employee Name"></dxi-column>
            <dxi-column caption="Employment Type" dataField="Employment Type"></dxi-column>
            <dxi-column caption="Employment Status" dataField="Employment Status"></dxi-column>
            <dxi-column caption="Entity" dataField="Entity"></dxi-column>
            <dxi-column caption="Division" dataField="Division"></dxi-column>
            <dxi-column caption="Sub Division" dataField="Sub Division"></dxi-column>
            <dxi-column caption="Charge Code" dataField="Charge Code"></dxi-column>
            <dxi-column caption="Charge Code Description" dataField="Charge Code Description"></dxi-column>
            <dxi-column caption="Location" dataField="Location"></dxi-column>
            <dxi-column caption="Year" dataField="Year"></dxi-column>
            <dxi-column caption="Working Hours" dataField="Working Hours"></dxi-column>
            <dxi-column caption="Date" dataField="Date"></dxi-column>
          </dx-data-grid>
    </div>
    <div class="col-12" *ngIf="selectedReport == 3 && downloadData?.length > 0">
        <dx-data-grid
            #dataGrid
            class="data-grid"
            [height]="dynamicGridReportHeight"
            [dataSource]="downloadData"
            [showBorders]="true"
            [columnAutoWidth]="true"
            [showColumnLines]="true"
            [showRowLines]="true"
          >
            <dxo-filter-row [visible]="true"></dxo-filter-row>
            <dxo-header-filter [visible]="true"></dxo-header-filter>
            <dxo-column-chooser
              [allowSearch]="true"
              [enabled]="true"
              mode="select"
            ></dxo-column-chooser>
            <dxo-scrolling mode="infinite"></dxo-scrolling>
            <dxi-column caption="Employee Id" dataField="Employee Id"></dxi-column>
            <dxi-column caption="Employee Name" dataField="Employee Name"></dxi-column>
            <dxi-column caption="Employment Type" dataField="Employment Type"></dxi-column>
            <dxi-column caption="Employment Status" dataField="Employment Status"></dxi-column>
            <dxi-column caption="Entity" dataField="Entity"></dxi-column>
            <dxi-column caption="Division" dataField="Division"></dxi-column>
            <dxi-column caption="Sub Division" dataField="Sub Division"></dxi-column>
            <dxi-column caption="Charge Code" dataField="Charge Code"></dxi-column>
            <dxi-column caption="Charge Code Description" dataField="Charge Code Description"></dxi-column>
            <dxi-column caption="Location" dataField="Location"></dxi-column>
            <dxi-column caption="Year" dataField="Year"></dxi-column>
            <dxi-column caption="Work Schedule" dataField="Work Schedule"></dxi-column>
            <dxi-column caption="Utilization Percentage" dataField="Utilization Percentage"></dxi-column>
        </dx-data-grid>
    </div>
    <div class="col-12" *ngIf="selectedReport == 4 && downloadData?.length > 0">
        <dx-data-grid
            #dataGrid
            class="data-grid"
            [height]="dynamicGridReportHeight"
            [dataSource]="downloadData"
            [showBorders]="true"
            [columnAutoWidth]="true"
            [showColumnLines]="true"
            [showRowLines]="true"
          >
            <dxo-filter-row [visible]="true"></dxo-filter-row>
            <dxo-header-filter [visible]="true"></dxo-header-filter>
            <dxo-column-chooser
              [allowSearch]="true"
              [enabled]="true"
              mode="select"
            ></dxo-column-chooser>
            <dxo-scrolling mode="infinite"></dxo-scrolling>
            <dxi-column caption="Date" dataField="Date"></dxi-column>
            <dxi-column caption="Employee Id" dataField="Employee Id"></dxi-column>
            <dxi-column caption="Employee Name" dataField="Employee Name"></dxi-column>
            <dxi-column caption="Employment Type" dataField="Employment Type"></dxi-column>
            <dxi-column caption="Employment Status" dataField="Employment Status"></dxi-column>
            <dxi-column caption="Entity" dataField="Entity"></dxi-column>
            <dxi-column caption="Division" dataField="Division"></dxi-column>
            <dxi-column caption="Sub Division" dataField="Sub Division"></dxi-column>
            <dxi-column caption="Charge Code" dataField="Charge Code"></dxi-column>
            <dxi-column caption="Charge Code Description" dataField="Charge Code Description"></dxi-column>
            <dxi-column caption="Hours" dataField="Hours"></dxi-column>
            <dxi-column caption="Month" dataField="Month"></dxi-column>
            <dxi-column caption="Year" dataField="Year"></dxi-column>
            <dxi-column caption="Holiday Name" dataField="Holiday Name"></dxi-column>
        </dx-data-grid>
    </div>
    <div class="col-12" *ngIf="selectedReport == 5 && downloadData?.length > 0">
        <dx-data-grid
            #dataGrid
            class="data-grid"
            [height]="dynamicGridReportHeight"
            [dataSource]="downloadData"
            [showBorders]="true"
            [columnAutoWidth]="true"
            [showColumnLines]="true"
            [showRowLines]="true"
          >
            <dxo-filter-row [visible]="true"></dxo-filter-row>
            <dxo-header-filter [visible]="true"></dxo-header-filter>
            <dxo-column-chooser
              [allowSearch]="true"
              [enabled]="true"
              mode="select"
            ></dxo-column-chooser>
            <dxo-scrolling mode="infinite"></dxo-scrolling>
            <dxi-column caption="Employee Id" dataField="Employee Id"></dxi-column>
            <dxi-column caption="Employee Name" dataField="Employee Name"></dxi-column>
            <dxi-column caption="Employment Type" dataField="Employment Type"></dxi-column>
            <dxi-column caption="Employment Status" dataField="Employment Status"></dxi-column>
            <dxi-column caption="Entity Name" dataField="Entity Name"></dxi-column>
            <dxi-column caption="Division Name" dataField="Division Name"></dxi-column>
            <dxi-column caption="Sub Division Name" dataField="Sub Division Name"></dxi-column>
            <dxi-column caption="Charge Code" dataField="Charge Code"></dxi-column>
            <dxi-column caption="Charge Code Description" dataField="Charge Code Description"></dxi-column>
            <dxi-column caption="Location" dataField="Location"></dxi-column>
            <dxi-column caption="Working Hours" dataField="Working Hours"></dxi-column>
            <dxi-column caption="Date" dataField="Date"></dxi-column>
        </dx-data-grid>
    </div>
    <div class="col-12" *ngIf="selectedReport == 6 && downloadData?.length > 0">
        <dx-data-grid
            #dataGrid
            class="data-grid"
            [height]="dynamicGridReportHeight"
            [dataSource]="downloadData"
            [showBorders]="true"
            [columnAutoWidth]="true"
            [showColumnLines]="true"
            [showRowLines]="true"
          >
            <dxo-filter-row [visible]="true"></dxo-filter-row>
            <dxo-header-filter [visible]="true"></dxo-header-filter>
            <dxo-column-chooser
              [allowSearch]="true"
              [enabled]="true"
              mode="select"
            ></dxo-column-chooser>
            <dxo-scrolling mode="infinite"></dxo-scrolling>
            <dxi-column caption="Employee Id" dataField="Employee Id"></dxi-column>
            <dxi-column caption="Employee Name" dataField="Employee Name"></dxi-column>
            <dxi-column caption="Employment Type" dataField="Employment Type"></dxi-column>
            <dxi-column caption="Employment Status" dataField="Employment Status"></dxi-column>
            <dxi-column caption="Entity" dataField="Entity"></dxi-column>
            <dxi-column caption="Division" dataField="Division"></dxi-column>
            <dxi-column caption="Sub Division" dataField="Sub Division"></dxi-column>
            <dxi-column caption="Charge Code" dataField="Charge Code"></dxi-column>
            <dxi-column caption="Charge Code Description" dataField="Charge Code Description"></dxi-column>
            <dxi-column caption="Location" dataField="Location"></dxi-column>
            <dxi-column caption="Year" dataField="Year"></dxi-column>
            <dxi-column caption="Work Schedule" dataField="Work Schedule"></dxi-column>
            <dxi-column caption="MIS Working Hours" dataField="MIS Working Hours"></dxi-column>
            <dxi-column caption="MIS Utilization Percentage" dataField="MIS Utilization Percentage"></dxi-column>
        </dx-data-grid>
    </div>
</div>