import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { TimesheetQueryReportRoutingModule } from './timesheet-query-report-routing.module';
import { TimesheetQueryReportLandingPageComponent } from './pages/timesheet-query-report-landing-page/timesheet-query-report-landing-page.component';

import { FormsModule, ReactiveFormsModule} from '@angular/forms';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatInputModule } from '@angular/material/input';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { DxDataGridModule, DxTemplateModule } from 'devextreme-angular';

@NgModule({
  declarations: [TimesheetQueryReportLandingPageComponent],
  imports: [
    CommonModule,
    TimesheetQueryReportRoutingModule,
    MatSelectModule,
    MatFormFieldModule,
    MatButtonModule,
    MatIconModule,
    MatTooltipModule,
    MatInputModule,
    MatDatepickerModule,
    FormsModule,
    ReactiveFormsModule,
    MatCheckboxModule,
    MatSlideToggleModule,
    DxDataGridModule,
    DxTemplateModule
  ]
})
export class TimesheetQueryReportModule { }
