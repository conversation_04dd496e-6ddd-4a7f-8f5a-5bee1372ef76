import { TsV2DailyLogTableViewService } from './../../services/ts-v2-daily-log-table-view.service';
import {
  Component,
  HostListener,
  OnInit,
  QueryList,
  ViewChild,
  ViewChildren,
} from '@angular/core';
import { FormControl } from '@angular/forms';
import { Router } from '@angular/router';
import { LoginService } from 'src/app/services/login/login.service';
import { ToasterService } from 'src/app/services/toaster-service/toaster.service';
import { MultiSelectSearchComponent } from '../../components/multi-select-search/multi-select-search.component';
import { debounceTime, takeUntil } from 'rxjs/operators';
import * as _ from 'underscore';
import * as moment from 'moment';
import { Subject } from 'rxjs';
import {
  MomentDateAdapter,
  MAT_MOMENT_DATE_ADAPTER_OPTIONS,
} from '@angular/material-moment-adapter';
import {
  DateAdapter,
  MAT_DATE_LOCALE,
  MAT_DATE_FORMATS,
} from '@angular/material/core';
import { MatStepper } from '@angular/material/stepper';
import sweetAlert from 'sweetalert2';
import { JsonToExcelService } from 'src/app/services/excel/json-to-excel.service';
import { NgxSpinnerService } from 'ngx-spinner';
import * as XLSX from 'xlsx';

export const MY_FORMATS = {
  parse: {
    dateInput: 'DD-MMM-YYYY',
  },
  display: {
    dateInput: 'DD-MMM-YYYY',
    monthYearLabel: 'MMM YYYY',
    dateA11yLabel: 'LL',
    monthYearA11yLabel: 'MMMM YYYY',
  },
};

@Component({
  selector: 'app-ts-v2-daily-log-rp-table-view-landing-page',
  templateUrl: './ts-v2-daily-log-rp-table-view-landing-page.component.html',
  styleUrls: ['./ts-v2-daily-log-rp-table-view-landing-page.component.scss'],
  providers: [
    {
      provide: DateAdapter,
      useClass: MomentDateAdapter,
      deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS],
    },
    { provide: MAT_DATE_FORMATS, useValue: MY_FORMATS },
  ],
})
export class TsV2DailyLogRpTableViewLandingPageComponent implements OnInit {
  @ViewChild('dataGrid', { static: false }) dataGrid: any;
  @ViewChildren(MultiSelectSearchComponent)
  multiSelectSearch: QueryList<MultiSelectSearchComponent>;
  @ViewChild('stepper') stepper: MatStepper;

  dynamicHeight: string;
  dynamicReportHeight: string;
  dynamicGridReportHeight: string;
  currentUser: any;
  oid: string;
  aid: number;
  token: string;
  errorData = [];
  protected _onDestroy = new Subject<void>();
  statusList = [];
  employeeList = [];
  portfolioList = [];
  sowList = [];
  logData: any = [];
  divisionList: any = [];
  subDivisionList: any = [];
  regionList: any = [];
  levelList: any = [];
  entityList: any = [];
  salesRegionList: any = [];
  workstreamList: any = [];
  approverList: any = [];
  url: string;
  downloadData = [];
  disableDownloadButton = false;

  isInitialLoading: boolean = true;
  apiInProgress: boolean = false;
  portfolioFilterApiInProgress: boolean = false;
  sowFilterApiInProgress: boolean = false;
  employeeFilterApiInProgress: boolean = false;
  approverFilterApiInProgress: boolean = false;
  subDivisionApiInProgress: boolean = false;

  startDate = new FormControl(null);
  endDate = new FormControl(null);
  portfolio = new FormControl([]);
  sow = new FormControl([]);
  employee = new FormControl([]);
  status = new FormControl([]);
  division = new FormControl([]);
  subDivision = new FormControl([]);
  region = new FormControl([]);
  level = new FormControl([]);
  entity = new FormControl([]);
  salesRegion = new FormControl([]);
  workstream= new FormControl([]);
  approver= new FormControl([]);
  totalRecords = 0;
  pages = 0;
  defaultPageSize = 1000000;
  showDownloadIcon: boolean = false;
  sow_placeholder = "Project Code - Project Name";
  entity_placeholder = "Company";
  division_placeholder = "Business Unit";
  sub_division_placeholder = "Department";
  sow_ref_caption = "Project Code";
  sow_description_caption = "SOW Description";
  entity_caption = "Zifo Legal Entity";
  activity_caption = "Activity Name";
  zifo_sow_refernece_caption = "Zifo SOW Reference";
  workstream_Caption = "Workstream";
  report_data_version = 1;
  color: string = "#EE4961";

  currentMonthStartDate: any;
  currentMonthEndDate: any;
  showSOWReference: boolean = true;
  showWorkstream: boolean = true;
  showLocation: boolean = false;
  showWeekOff: boolean = false;

  constructor(
    private router: Router,
    private toastService: ToasterService,
    private authService: LoginService,
    private reportService: TsV2DailyLogTableViewService,
    private excelService: JsonToExcelService,
    private spinnerService: NgxSpinnerService,
  ) {}

  async ngOnInit() {
    await this.getTimesheetReportAccess();
    document.documentElement.style.setProperty('--color', this.color);
    this.currentMonthStartDate = moment().startOf('month');
    this.currentMonthEndDate = moment().endOf('month');
    this.startDate.setValue(this.currentMonthStartDate);
    this.endDate.setValue(this.currentMonthEndDate);
    this.calculateDynamicContentHeight();
    this.currentUser = this.authService.getProfile().profile;
    this.oid = this.currentUser.oid;
    this.aid = this.currentUser.aid;
    this.token = this.authService.getJwtToken();

    this.getFilterData();

    await this.getErrorDetails();

    Promise.all([
      this.getEntity(),
      this.getSalesRegion(),
      this.getTimesheetStatus(),
      this.getDivision(),
      this.getSubDivision(),
      this.getRegion(),
      this.getLevel(),
      this.getProjectDetailsForDailyLogReport(),
      this.getItemDetailsForDailyLogReport(),
      this.getEmployeeDetailsForDailyLogReport(),
      this.getApproverDetailsForDailyLogReport(),
      this.getWorkstream()
    ]).then((res) => {
      this.isInitialLoading = false;
    });

    this.portfolio.valueChanges.pipe(debounceTime(1000)).subscribe(async () => {
      if (this.startDate.value != null && this.endDate.value != null) {
        this.getItemDetailsForDailyLogReport();
      }
    });

    this.sow.valueChanges.pipe(debounceTime(1000)).subscribe(async () => {
      if (this.startDate.value != null && this.endDate.value != null) {
        this.getWorkstream();
        this.getEmployeeDetailsForDailyLogReport();
        this.getApproverDetailsForDailyLogReport();
      }
    });

    this.division.valueChanges.pipe(debounceTime(1000)).subscribe(async () => {
      if (this.startDate.value != null && this.endDate.value != null && this.division.value && this.division.value.length > 0) {
        this.getSubDivision();
      }
    });
  }

  @HostListener('window:resize')
  onResize() {
    this.calculateDynamicContentHeight();
  }
  //Adjust UI size based on window size
  calculateDynamicContentHeight() {
    this.dynamicHeight = window.innerHeight - 104 + 'px';
    document.documentElement.style.setProperty(
      '--dynamicHeight',
      this.dynamicHeight
    );
    this.dynamicGridReportHeight = window.innerHeight - 240 + 'px';
    document.documentElement.style.setProperty(
      '--dynamicGridReportHeight',
      this.dynamicGridReportHeight
    );
    this.dynamicReportHeight = window.innerHeight - 223 + 'px';
    document.documentElement.style.setProperty(
      '--dynamicReportHeight',
      this.dynamicReportHeight
    );
  }

  goToReportsMainScreen() {
    this.disableDownloadButton = false;
    this.router.navigate(['/main/reports']);
  }

  goToPreviousScreen() {
    this.disableDownloadButton = false;
    this.onDeleteFilters(1);
    this.clearSortAndFilters();
    this.getFilterData();
    this.stepper.reset();
  }

  onKeyDownDateSearch(event: KeyboardEvent) {
    event.preventDefault();
  }

  onChangeInDate(event) {
    if (this.startDate.value != null && this.endDate.value != null) {
      this.getProjectDetailsForDailyLogReport();
      this.getItemDetailsForDailyLogReport();
    }
  }

  onDeleteFilters(data) {
    // this.startDate.setValue(null);
    // this.endDate.setValue(null);
    this.resetSpecificSearchComponent(0);
    this.resetSpecificSearchComponent(1);
    this.resetSpecificSearchComponent(2);
    this.resetSpecificSearchComponent(3);
    this.resetSpecificSearchComponent(4);
    this.resetSpecificSearchComponent(5);
    this.resetSpecificSearchComponent(6);
    this.resetSpecificSearchComponent(7);
    this.resetSpecificSearchComponent(8);
    // this.portfolio.setValue([]);
    // this.sow.setValue([]);
    // this.employee.setValue([]);
    // this.status.setValue([]);
    // this.division.setValue([]);
    // this.subDivision.setValue([]);
    // this.region.setValue([]);
    // this.level.setValue([]);
    // this.entity.setValue([]);
    // this.portfolioList = [];
    // this.sowList = [];
    // this.employeeList = [];
    // this.divisionList = [];
    // this.subDivisionList = [];
    // this.regionList = [];
    // this.levelList = [];
    this.logData = [];
    // this.statusList = [];
    if(data == 0){
      localStorage.removeItem('tsv2-daily-log-report-portfolio');
      localStorage.removeItem('tsv2-daily-log-report-sow');
      localStorage.removeItem('tsv2-daily-log-report-employee');
      localStorage.removeItem('tsv2-daily-log-report-status');
      localStorage.removeItem('tsv2-daily-log-report-division');
      localStorage.removeItem('tsv2-daily-log-report-subDivision');
      localStorage.removeItem('tsv2-daily-log-report-region');
      localStorage.removeItem('tsv2-daily-log-report-level');
      localStorage.removeItem('tsv2-daily-log-report-entity');
      localStorage.removeItem('tsv2-daily-log-report-salesRegion');
      localStorage.removeItem('tsv2-daily-log-report-workstream');
      localStorage.removeItem('tsv2-daily-log-report-approver');
    }
  }

  clearSortAndFilters() {
    this.dataGrid.instance.clearSorting();
    this.dataGrid.instance.clearFilter();
  }

  resetSpecificSearchComponent(index: number) {
    if (this.isInitialLoading) {
      return;
    }
    // index = 0 -> Sales Region
    // index = 1 -> Portfolio
    // index = 2 -> Cost Center
    // index = 3 -> Workstream
    // index = 4 -> Region
    // index = 5 -> Entity
    // index = 6 -> Division
    // index = 7 -> Sub Division
    // index = 8 -> Employee Name
    // index = 9 -> Status
    // index = 10 -> Approver Name
    if(this.multiSelectSearch){
      let currentIndex = this.multiSelectSearch.toArray()[index];
      if (currentIndex) currentIndex.emptyAllSelection();
    }
  }

  async onGenerateReport(isDownload) {
    if (this.startDate.value == null || this.endDate.value == null) {
      this.toastService.showWarning('Date Range is Mandatory!', '');
      return;
    }

    if (
      this.portfolioFilterApiInProgress ||
      this.sowFilterApiInProgress ||
      this.employeeFilterApiInProgress ||
      this.approverFilterApiInProgress
    ) {
      //this.spinnerService.show()
      this.toastService.showInfo(
        'Refreshing report with your input. Please wait a moment before clicking Generate Report again!',
        '',
        3000
      );
      return;
    }

    //this.spinnerService.hide()
    this.disableDownloadButton = true;
    await this.getTsDailyLogReportPageData();

    if(isDownload)
    {
      this.downloadReport();
      return;
    }

    //Need To Write all combination Restriction
    if (
      this.division.value.length == 0 &&
      this.subDivision.value.length == 0 &&
      this.region.value.length == 0 &&
      this.level.value.length == 0 &&
      this.employee.value.length == 0 &&
      this.sow.value.length == 0 &&
      this.portfolio.value.length == 0 &&
      this.status.value.length == 0 &&
      this.entity.value.length == 0 &&
      this.workstream.value.length == 0 &&
      this.approver.value.length == 0
    ) {
      sweetAlert
        .fire({
          title: 'Timesheet Daily Log Report',
          text: 'To view the report, you have to select a combination of inputs such as date range with either Project Code - Project Name or date range with either with Business Unit or Department or Region or Employee Or else, you can click on download button to download the report',
          icon: 'warning',
          showCancelButton: true,
          confirmButtonColor: '#3085d6',
          cancelButtonColor: '#d33',
          confirmButtonText: 'Download Report',
          cancelButtonText: 'Close',
        })
        .then(async (result) => {
          if (result.isConfirmed) {
            this.downloadReport();
          }
          else{
            this.disableDownloadButton = false;
          }
        });

      return;
    }

    if (this.sow.value.length > 10 || this.portfolio.value.length > 10) {
      sweetAlert
        .fire({
          title: 'Timesheet Daily Log Report',
          text: 'To view the report, you have to select maximum 10 Project Code - Project Name Or else, you can click on download button to download the report',
          icon: 'warning',
          showCancelButton: true,
          confirmButtonColor: '#3085d6',
          cancelButtonColor: '#d33',
          confirmButtonText: 'Download Report',
          cancelButtonText: 'Close',
        })
        .then(async (result) => {
          if (result.isConfirmed) {
            this.downloadReport();
          }
          else{
            this.disableDownloadButton = false;
          }
        });

      return;
    }

    if (this.division.value.length > 1) {
      sweetAlert
        .fire({
          title: 'Timesheet Daily Log Report',
          text: 'Only one Business Unit can be selected or download button to download the report',
          icon: 'warning',
          showCancelButton: true,
          confirmButtonColor: '#3085d6',
          cancelButtonColor: '#d33',
          confirmButtonText: 'Download Report',
          cancelButtonText: 'Close',
        })
        .then(async (result) => {
          if (result.isConfirmed) {
            this.downloadReport();
          }
          else{
            this.disableDownloadButton = false;
          }
        });

      return;
    }

    if (this.region.value.length > 1) {
      sweetAlert
        .fire({
          title: 'Timesheet Daily Log Report',
          text: 'Only one Region can be selected or download button to download the report',
          icon: 'warning',
          showCancelButton: true,
          confirmButtonColor: '#3085d6',
          cancelButtonColor: '#d33',
          confirmButtonText: 'Download Report',
          cancelButtonText: 'Close',
        })
        .then(async (result) => {
          if (result.isConfirmed) {
            this.downloadReport();
          }
          else{
            this.disableDownloadButton = false;
          }
        });

      return;
    }

    if (this.level.value.length > 1) {
      sweetAlert
        .fire({
          title: 'Timesheet Daily Log Report',
          text: 'Only one level can be selected or download button to download the report',
          icon: 'warning',
          showCancelButton: true,
          confirmButtonColor: '#3085d6',
          cancelButtonColor: '#d33',
          confirmButtonText: 'Download Report',
          cancelButtonText: 'Close',
        })
        .then(async (result) => {
          if (result.isConfirmed) {
            this.downloadReport();
          }
          else{
            this.disableDownloadButton = false;
          }
        });

      return;
    }

    if (this.entity.value.length > 1) {
      sweetAlert
        .fire({
          title: 'Timesheet Daily Log Report',
          text: 'Only one Company can be selected or download button to download the report',
          icon: 'warning',
          showCancelButton: true,
          confirmButtonColor: '#3085d6',
          cancelButtonColor: '#d33',
          confirmButtonText: 'Download Report',
          cancelButtonText: 'Close',
        })
        .then(async (result) => {
          if (result.isConfirmed) {
            this.downloadReport();
          }
          else{
            this.disableDownloadButton = false;
          }
        });

      return;
    }

    if (this.employee.value.length > 10) {
      sweetAlert
        .fire({
          title: 'Timesheet Daily Log Report',
          text: 'Maximum 10 employees timesheet data can be view in report or click download button to download the report',
          icon: 'warning',
          showCancelButton: true,
          confirmButtonColor: '#3085d6',
          cancelButtonColor: '#d33',
          confirmButtonText: 'Download Report',
          cancelButtonText: 'Close',
        })
        .then(async (result) => {
          if (result.isConfirmed) {
            this.downloadReport();
          }
          else{
            this.disableDownloadButton = false;
          }
        });

      return;
    }

    if (this.totalRecords > 5000) {
      sweetAlert
        .fire({
          title: 'Timesheet Daily Log Report',
          text: 'The Record Size is Huge, Kindly use the download option for better performance',
          icon: 'warning',
          showCancelButton: true,
          confirmButtonColor: '#3085d6',
          cancelButtonColor: '#d33',
          confirmButtonText: 'Download Report',
          cancelButtonText: 'Close',
        })
        .then(async (result) => {
          if (result.isConfirmed) {
            this.downloadReport();
          }
          else{
            this.disableDownloadButton = false;
          }
        });

      return;
    } else {
      await this.getTsDailyLogReportItemData(1, false, this.defaultPageSize);
    }

    this.stepper.next();
  }

  onExporting(e) {
    e.fileName = 'Timesheet Daily Log Report - Table View';
  }

  async getErrorDetails() {
    return new Promise((resolve, reject) =>
      this.reportService
        .getErrorDetails()
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res) => {
            if (res['messType'] == 'S' && res['data'].length > 0) {
              this.errorData = res['data'];
            }
            resolve(true);
          },
          error: (err) => {
            console.log(err);
            reject();
          },
        })
    );
  }

  async getTimesheetStatus() {
    this.statusList = [];
    this.resetSpecificSearchComponent(9);
    return new Promise((resolve, reject) =>
      this.reportService
        .getTimesheetStatus()
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res) => {
            if (res['messType'] == 'S') {
              if (res['data'] && res['data'].length > 0) {
                let result = res['data'].filter((obj) => {
                  return obj.id == 4 || obj.id == 2 || obj.id == 1 || obj.id == 3;
                });
                this.statusList = result;
                this.color = res['colorCode'];
                document.documentElement.style.setProperty('--color', this.color);
              }
              if(res['config_val'] == 2)
              {
                this.report_data_version = res['config_val'];
                this.entity_placeholder = "Employee Entity";
                this.entity_caption = "Employee Entity";
                this.division_placeholder = "Employee Division";
                this.sub_division_placeholder = "Employee Sub Division";
                this.activity_caption = "Task Name";
                this.sow_placeholder = "Charge Code";
                this.sow_ref_caption = "Charge Code";
                this.sow_description_caption = "Charge Code Description";
                this.zifo_sow_refernece_caption = "Charge Code Reference";
                this.workstream_Caption = "Phase";
                this.showWorkstream = false;
                this.showLocation = false;
                this.showWeekOff = false;
                this.showSOWReference = res['showWorstream'];
                this.color = res['colorCode'];
                document.documentElement.style.setProperty('--color', this.color);

              }
              if(res['config_val'] == 3){
                this.report_data_version = res['config_val'];
                this.entity_placeholder = "Employee Entity";
                this.entity_caption = "Employee Entity";
                this.division_placeholder = "Employee Division";
                this.sub_division_placeholder = "Employee Sub Division";
                this.activity_caption = "Task Name";
                this.sow_placeholder = "Cost Centre";
                this.sow_ref_caption = "Cost Centre";
                this.sow_description_caption = "Project Description";
                this.zifo_sow_refernece_caption = "Cost Centre Reference";
                this.workstream_Caption = "Phase";
                this.showSOWReference = false;
                this.showWorkstream = res['showWorstream'];
                this.color = res['colorCode'];
                this.showLocation = res['location'];
                this.showWeekOff = true;
                document.documentElement.style.setProperty('--color', this.color);
              }
            } else {
              this.report_data_version = 1;
              this.color = '#EE4961';
              this.toastService.showInfo(
                'Timesheet App Message',
                res['messText'],
                3000
              );
            }
            resolve(true);
          },
          error: (err) => {
            this.report_data_version = 1;
            this.toastService.showError(
              `Timesheet Error ${err.error.code}`,
              _.filter(this.errorData, { error_code: err.error.code })[0]
                ?.user_message,
              60000
            );
            reject();
          },
        })
    );
  }

  async getEmployeeDetailsForDailyLogReport() {
    this.employeeFilterApiInProgress = true;
    this.employeeList = [];
    this.resetSpecificSearchComponent(8);
    return new Promise((resolve, reject) =>
      this.reportService
        .getEmployeeDetailsForDailyLogReport(
          moment(this.startDate.value).format('YYYY-MM-DD'),
          moment(this.endDate.value).format('YYYY-MM-DD'),
          this.aid,
          this.oid,
          this.portfolio.value,
          this.sow.value
        )
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res) => {
            if (res['messType'] == 'S') {
              this.employeeList =
                res['data'] && res['data'].length > 0 ? res['data'] : [];
              let employee = localStorage.getItem(
                'tsv2-daily-log-report-employee'
              )
                ? JSON.parse(
                    localStorage.getItem('tsv2-daily-log-report-employee')
                  )
                : [];
              this.employee.setValue(employee);
            } else {
              let employee = localStorage.getItem(
                'tsv2-daily-log-report-employee'
              )
                ? JSON.parse(
                    localStorage.getItem('tsv2-daily-log-report-employee')
                  )
                : [];
              this.employee.setValue(employee);
              this.toastService.showInfo(
                'Timesheet App Message',
                res['messText'],
                3000
              );
            }
            this.employeeFilterApiInProgress = false;
            resolve(true);
          },
          error: (err) => {
            this.toastService.showError(
              `Timesheet Error ${err.error.code}`,
              _.filter(this.errorData, { error_code: err.error.code })[0]
                ?.user_message,
              60000
            );
            this.employeeFilterApiInProgress = false;
            reject();
          },
        })
    );
  }

  async getProjectDetailsForDailyLogReport() {
    this.portfolioFilterApiInProgress = true;
    this.portfolioList = [];
    this.resetSpecificSearchComponent(1);
    return new Promise((resolve, reject) =>
      this.reportService
        .getProjectDetailsForDailyLogReport(
          moment(this.startDate.value).format('YYYY-MM-DD'),
          moment(this.endDate.value).format('YYYY-MM-DD'),
          this.aid,
          this.oid
        )
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res) => {
            if (res['messType'] == 'S') {
              this.portfolioList =
                res['data'] && res['data'].length > 0 ? res['data'] : [];
            } else {
              this.toastService.showInfo(
                'Timesheet App Message',
                res['messText'],
                3000
              );
            }
            this.portfolioFilterApiInProgress = false;
            resolve(true);
          },
          error: (err) => {
            this.toastService.showError(
              `Timesheet Error ${err.error.code}`,
              _.filter(this.errorData, { error_code: err.error.code })[0]
                ?.user_message,
              60000
            );
            this.portfolioFilterApiInProgress = false;
            reject();
          },
        })
    );
  }

  async getItemDetailsForDailyLogReport() {
    this.sowFilterApiInProgress = true;
    this.sowList = [];
    this.resetSpecificSearchComponent(2);
    return new Promise((resolve, reject) =>
      this.reportService
        .getItemDetailsForDailyLogReport(
          moment(this.startDate.value).format('YYYY-MM-DD'),
          moment(this.endDate.value).format('YYYY-MM-DD'),
          this.aid,
          this.oid,
          this.portfolio.value
        )
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res) => {
            if (res['messType'] == 'S') {
              this.sowList =
                res['data'] && res['data'].length > 0 ? res['data'] : [];
              let sow = localStorage.getItem('tsv2-daily-log-report-sow')
                ? JSON.parse(localStorage.getItem('tsv2-daily-log-report-sow'))
                : [];
              this.sow.setValue(sow);
            } else {
              let sow = localStorage.getItem('tsv2-daily-log-report-sow')
                ? JSON.parse(localStorage.getItem('tsv2-daily-log-report-sow'))
                : [];
              this.sow.setValue(sow);
              this.toastService.showInfo(
                'Timesheet App Message',
                res['messText'],
                3000
              );
            }
            this.sowFilterApiInProgress = false;
            resolve(true);
          },
          error: (err) => {
            this.toastService.showError(
              `Timesheet Error ${err.error.code}`,
              _.filter(this.errorData, { error_code: err.error.code })[0]
                ?.user_message,
              60000
            );
            this.sowFilterApiInProgress = false;
            reject();
          },
        })
    );
  }

  async getTsDailyLogReportItemData(page, isForDownload, pageSize) {
    this.apiInProgress = true;
    return new Promise((resolve, reject) =>
      this.reportService
        .getTsDailyLogReportItemData(
          moment(this.startDate.value).format('YYYY-MM-DD'),
          moment(this.endDate.value).format('YYYY-MM-DD'),
          this.aid,
          this.oid,
          this.portfolio.value && this.portfolio.value.length > 0
            ? this.portfolio.value
            : [],
          this.sow.value && this.sow.value.length > 0 ? this.sow.value : [],
          this.employee.value && this.employee.value.length > 0
            ? this.employee.value
            : [],
          this.status.value && this.status.value.length > 0
            ? this.status.value
            : [2, 4],
          this.division.value && this.division.value.length > 0
            ? this.division.value
            : [],
          this.subDivision.value && this.subDivision.value.length > 0
            ? this.subDivision.value
            : [],
          this.region.value && this.region.value.length > 0
            ? this.region.value
            : [],
          this.level.value && this.level.value.length > 0
            ? this.level.value
            : [],
          this.entity.value && this.entity.value.length > 0 
            ? this.entity.value
            : [],
          this.salesRegion.value && this.salesRegion.value.length > 0 
            ? this.salesRegion.value
            : [],
          this.workstream.value && this.workstream.value.length > 0 
            ? this.workstream.value
            : [],
          this.approver.value && this.approver.value.length > 0
            ? this.approver.value
            : [],
          page,
          isForDownload,
          pageSize
        )
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res) => {
            if (res['messType'] == 'S') {
              this.logData = res['data'];
              if(isForDownload){
                // let temp_data = [];
                // for(let items of res['data'])
                // {
                //   temp_data.push({
                //     'Customer': items.customer,
                //     'Portfolio': items.portfolio,
                //     'SOW Ref': items.sow_reference_name,
                //     'SOW Description': items.sow,
                //     'Region': items.region,
                //     'Zifo Legal Entity': items.legal_entity,
                //     'Business Unit': items.business_unit,
                //     'Department': items.department,
                //     'Employee Id': items.associate_id,
                //     'Employee Name': items.employee_name,
                //     'Date': moment(items.date).format("DD-MM-YYYY"),
                //     'Workstream': items.workstream,
                //     'Activity Name': items.activity,
                //     'Logged Hours': items.total_hours,
                //     'Billable/Non-Billable': items.billing_type,
                //     'Approver': items.approvers,
                //     'Timesheet Status': items.status_description,
                //     'Comments': items.comments,
                //   });
                // }
                //this.downloadData = this.downloadData.concat(temp_data);
              }
            } else {
              this.toastService.showInfo(
                'Timesheet App Message',
                res['messText'],
                3000
              );
            }
            this.apiInProgress = false;
            resolve(true);
          },
          error: (err) => {
            this.toastService.showError(
              `Timesheet Error ${err.error.code}`,
              _.filter(this.errorData, { error_code: err.error.code })[0]
                ?.user_message,
              60000
            );
            this.apiInProgress = false;
            reject();
          },
        })
    );
  }

  async getDivision(){
    this.divisionList = [];
    this.resetSpecificSearchComponent(6);
    return new Promise((resolve, reject) =>
      this.reportService
        .getDivision()
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res) => {
            if (res['messType'] == 'S') {
              this.divisionList =
                res['data'] && res['data'].length > 0 ? res['data'] : [];
            } else {
              this.toastService.showInfo(
                'Timesheet App Message',
                res['messText'],
                3000
              );
            }
            resolve(true);
          },
          error: (err) => {
            this.toastService.showError(
              `Timesheet Error ${err.error.code}`,
              _.filter(this.errorData, { error_code: err.error.code })[0]
                ?.user_message,
              60000
            );
            reject();
          },
        })
    );
  }

  async getSubDivision(){
    if (!this.division.value || this.division.value.length == 0) {
      return;
    }
    this.subDivisionApiInProgress = true;
    this.subDivisionList = [];
    this.resetSpecificSearchComponent(7);
    return new Promise((resolve, reject) =>
      this.reportService
        .getSubDivision(
          this.division.value
        )
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res) => {
            if (res['messType'] == 'S') {
              this.subDivisionList =
                res['data'] && res['data'].length > 0 ? res['data'] : [];
              let subDivision = localStorage.getItem(
                'tsv2-daily-log-report-subDivision'
              )
                ? JSON.parse(
                    localStorage.getItem('tsv2-daily-log-report-subDivision')
                  )
                : [];
              this.subDivision.setValue(subDivision);
            } else {
              let subDivision = localStorage.getItem(
                'tsv2-daily-log-report-subDivision'
              )
                ? JSON.parse(
                    localStorage.getItem('tsv2-daily-log-report-subDivision')
                  )
                : [];
              this.subDivision.setValue(subDivision);
              this.toastService.showInfo(
                'Timesheet App Message',
                res['messText'],
                3000
              );
            }
            this.subDivisionApiInProgress = false;
            resolve(true);
          },
          error: (err) => {
            this.toastService.showError(
              `Timesheet Error ${err.error.code}`,
              _.filter(this.errorData, { error_code: err.error.code })[0]
                ?.user_message,
              60000
            );
            this.subDivisionApiInProgress = false;
            reject();
          },
        })
    );
  }

  async getRegion(){
    this.regionList = [];
    this.resetSpecificSearchComponent(4);
    return new Promise((resolve, reject) =>
      this.reportService
        .getRegion()
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res) => {
            if (res['messType'] == 'S') {
              this.regionList =
                res['data'] && res['data'].length > 0 ? res['data'] : [];
            } else {
              this.toastService.showInfo(
                'Timesheet App Message',
                res['messText'],
                3000
              );
            }
            resolve(true);
          },
          error: (err) => {
            this.toastService.showError(
              `Timesheet Error ${err.error.code}`,
              _.filter(this.errorData, { error_code: err.error.code })[0]
                ?.user_message,
              60000
            );
            reject();
          },
        })
    );
  }

  async getEntity(){
    this.entityList = [];
    this.resetSpecificSearchComponent(5); 
    return new Promise((resolve, reject) =>
    this.reportService
      .getEntity()
      .pipe(takeUntil(this._onDestroy))
      .subscribe({
        next: (res) => {
          if (res['messType'] == 'S') {
            this.entityList =
              res['data'] && res['data'].length > 0 ? res['data'] : [];
          } else {
            this.toastService.showInfo(
              'Timesheet App Message',
              res['messText'],
              3000
            );
          }
          resolve(true);
        },
        error: (err) => {
          this.toastService.showError(
            `Timesheet Error ${err.error.code}`,
            _.filter(this.errorData, { error_code: err.error.code })[0]
              ?.user_message,
            60000
          );
          reject();
        },
      })
  );
  }

  async getLevel(){
    return new Promise((resolve, reject) =>
      this.reportService
        .getLevel()
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res) => {
            if (res['messType'] == 'S') {
              this.levelList =
                res['data'] && res['data'].length > 0 ? res['data'] : [];
            } else {
              this.toastService.showInfo(
                'Timesheet App Message',
                res['messText'],
                3000
              );
            }
            resolve(true);
          },
          error: (err) => {
            this.toastService.showError(
              `Timesheet Error ${err.error.code}`,
              _.filter(this.errorData, { error_code: err.error.code })[0]
                ?.user_message,
              60000
            );
            reject();
          },
        })
    );
  }

  async getTsDailyLogReportPageData(){
    return new Promise((resolve, reject) =>
    this.reportService
      .getTsDailyLogReportPageData(
        moment(this.startDate.value).format('YYYY-MM-DD'),
        moment(this.endDate.value).format('YYYY-MM-DD'),
        this.aid,
        this.oid,
        this.portfolio.value && this.portfolio.value.length > 0
          ? this.portfolio.value
          : [],
        this.sow.value && this.sow.value.length > 0 ? this.sow.value : [],
        this.employee.value && this.employee.value.length > 0
          ? this.employee.value
          : [],
        this.status.value && this.status.value.length > 0
          ? this.status.value
          : [2, 4],
        this.division.value && this.division.value.length > 0
          ? this.division.value
          : [],
        this.subDivision.value && this.subDivision.value.length > 0
          ? this.subDivision.value
          : [],
        this.region.value && this.region.value.length > 0
          ? this.region.value
          : [],
        this.level.value && this.level.value.length > 0
          ? this.level.value
          : [],
        this.entity.value && this.entity.value.length > 0 
          ? this.entity.value
          : [],
        this.salesRegion.value && this.salesRegion.value.length > 0
          ? this.salesRegion.value
          : [],
        this.workstream.value && this.workstream.value.length > 0
          ? this.workstream.value
          : [],
        this.approver.value && this.approver.value.length > 0
          ? this.approver.value
          : [],
      )
      .pipe(takeUntil(this._onDestroy))
      .subscribe({
        next: (res) => {
          if (res['messType'] == 'S') {
           this.totalRecords = res['totalRecords'];
           this.pages = res['pages'];
           if(this.totalRecords == 0)
            {
              this.disableDownloadButton = false;
            }
          } else {
            this.disableDownloadButton = false;
            this.toastService.showInfo(
              'Timesheet App Message',
              res['messText'],
              3000
            );
          }
          resolve(true);
        },
        error: (err) => {
          this.disableDownloadButton = false;
          this.toastService.showError(
            `Timesheet Error ${err.error.code}`,
            _.filter(this.errorData, { error_code: err.error.code })[0]
              ?.user_message,
            60000
          );
          reject();
        },
      })
  );

  }

  async downloadReport(){
    if(this.pages == 0)
    {
      return this.toastService.showInfo("Timesheet V2 Daily Log Report Table View", "No Data Found", 3000);
    }
    this.spinnerService.show();
    // for(let i = 0; i < this.pages; i++ )
    // {
    //   this.getTsDailyLogReportItemData(i, true, this.defaultPageSize);
    //   if(i == this.pages-1)
    //   {
    //     this.spinnerService.show();
    //   }
    //   else{
    //     this.spinnerService.hide();
    //   }
    // }
    // if(this.downloadData && this.downloadData.length > 0){
    //   this.spinnerService.hide();
    //   this.toastService.showSuccess("Timesheet Daily Log Report Message", "Report Downloaded Successfully", 3000);
    //   this.excelService.exportAsExcelFile(
    //   this.downloadData,
    //   "Timesheet Daily Log Report - Table View"
    //   );
    //   this.downloadData = [];
    // }

    //New Logic Code
      // Track the number of completed API calls
      let completedCalls = 0;

      for (let i = 1; i <= this.pages; i++) {
          await this.getTsDailyLogReportItemDataNew(i, true, this.defaultPageSize)
              .then(() => {
                  completedCalls++;
                  // Check if all API calls have completed
                  if (completedCalls === this.pages) {
                      this.spinnerService.hide();
                      if (this.downloadData && this.downloadData.length > 0) {
                          this.toastService.showSuccess("Timesheet Daily Log Report Message", "Report Downloaded Successfully", 3000);
                          // this.excelService.exportAsExcelFile(
                          //     this.downloadData,
                          //     "Timesheet Daily Log Report - Table View"
                          // );
                          let worksheetData = this.downloadData.map(item => ({
                            ...item,
                            Date: item.Date ? this.convertToDate(item.Date) : "NA",
                            "Saved On": item["Saved On"] ? this.convertToDate(item["Saved On"]) : "NA",
                            "Submitted On": item["Submitted On"] ? this.convertToDate(item["Submitted On"]) : "NA",
                            "Approved On": item["Approved On"] ? this.convertToDate(item["Approved On"]) : "NA",
                            }));
                            
                            // Create a worksheet
                          const worksheet = XLSX.utils.json_to_sheet(worksheetData);
                            
                            // Set cell format for the date columns
                          const dateColumns = ['Date', 'Saved On', 'Submitted On', 'Approved On'];
                            dateColumns.forEach(col => {
                            const columnLetter = this.getColumnLetter(worksheet, col);
                            if (columnLetter) {
                            for (let row = 2; row <= worksheetData.length + 1; row++) { // Assuming header is on the first row
                            const cellAddress = `${columnLetter}${row}`;
                            if (worksheet[cellAddress]) {
                            worksheet[cellAddress].z = 'DD-MMM-YYYY';
                            }
                            }
                            }
                            });
                            
                            // Create a workbook
                            const workbook = XLSX.utils.book_new();
                            XLSX.utils.book_append_sheet(workbook, worksheet, 'data');
                            
                            // Export to Excel file
                            XLSX.writeFile(workbook, 'Timesheet Daily Log Report - Table View.xlsx');
                          this.downloadData = [];
                          this.disableDownloadButton = false;
                      }
                      else{
                        this.disableDownloadButton = false;
                        this.toastService.showSuccess("Timesheet Daily Log Report Message", "No Data Found", 3000);
                      }
                  }
              })
              .catch(() => {
                  // Handle errors if needed
                  this.disableDownloadButton = false;
                  this.toastService.showError('Timesheet Daily Log Report Message', 'Error: Server Unavailable, Kindly try after soemtime', 3000);
              });
      }
  }

  async getTsDailyLogReportItemDataNew(page, isForDownload, pageSize) {
    return new Promise((resolve, reject) =>
        this.reportService
            .getTsDailyLogReportItemData(
                moment(this.startDate.value).format('YYYY-MM-DD'),
                moment(this.endDate.value).format('YYYY-MM-DD'),
                this.aid,
                this.oid,
                this.portfolio.value && this.portfolio.value.length > 0 ? this.portfolio.value : [],
                this.sow.value && this.sow.value.length > 0 ? this.sow.value : [],
                this.employee.value && this.employee.value.length > 0 ? this.employee.value : [],
                this.status.value && this.status.value.length > 0 ? this.status.value : [2, 4],
                this.division.value && this.division.value.length > 0 ? this.division.value : [],
                this.subDivision.value && this.subDivision.value.length > 0 ? this.subDivision.value : [],
                this.region.value && this.region.value.length > 0 ? this.region.value : [],
                this.level.value && this.level.value.length > 0 ? this.level.value : [],
                this.entity.value && this.entity.value.length > 0 ? this.entity.value : [],
                this.salesRegion.value && this.salesRegion.value.length > 0 ? this.salesRegion.value : [],
                this.workstream.value && this.workstream.value.length > 0 ? this.workstream.value : [],
                this.approver.value && this.approver.value.length > 0 ? this.approver.value : [],
                page,
                isForDownload,
                pageSize
            )
            .pipe(takeUntil(this._onDestroy))
            .subscribe({
                next: (res) => {
                    if (res['messType'] == 'S') {
                        this.logData = res['data'];
                        if (isForDownload) {
                            this.downloadData = this.downloadData.concat(res['data']);
                        }
                    } else {
                        this.toastService.showInfo(
                            'Timesheet App Message',
                            res['messText'],
                            3000
                        );
                    }
                    resolve(true);
                },
                error: (err) => {
                    this.toastService.showError(
                        `Timesheet Error ${err.error.code}`,
                        _.filter(this.errorData, { error_code: err.error.code })[0]?.user_message,
                        60000
                    );
                    reject();
                },
            })
    );
  }

  convertToDate(dateStr): Date {
    if(dateStr != undefined || dateStr != '' || dateStr != 'NA' || dateStr != ' '){
      const [day, month, year] = dateStr.split('-');
      const monthIndex = new Date(`${month} 1, 2000`).getMonth();
      return new Date(Number(year), monthIndex, Number(day));
    }
  }
    
  getColumnLetter(worksheet: XLSX.WorkSheet, columnName: string): string | null {
    const range = XLSX.utils.decode_range(worksheet['!ref'] as string);
    for (let col = range.s.c; col <= range.e.c; col++) {
    const cellAddress = XLSX.utils.encode_cell({ c: col, r: 0 });
    if (worksheet[cellAddress]?.v === columnName) {
    return XLSX.utils.encode_col(col);
    }
    }
    return null;
  }

   /**
   * @description To Retrieve Sales Region
   */
   async getSalesRegion(){
    return new Promise((resolve, reject) =>
      this.reportService
        .getSalesRegion()
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res) => {
            if (res['messType'] == 'S') {
              this.salesRegionList =
                res['data'] && res['data'].length > 0 ? res['data'] : [];
            } else {
              this.toastService.showInfo(
                'Timesheet App Message',
                res['messText'],
                3000
              );
            }
            resolve(true);
          },
          error: (err) => {
            this.toastService.showError(
              `Timesheet Error ${err.error.code}`,
              _.filter(this.errorData, { error_code: err.error.code })[0]
                ?.user_message,
              60000
            );
            reject();
          },
        })
    );
   }

   saveFilterData() {
    let portfolio =
      this.portfolio.value && this.portfolio.value.length > 0
        ? JSON.stringify(this.portfolio.value)
        : JSON.stringify([]);
    let sow =
      this.sow.value && this.sow.value.length > 0
        ? JSON.stringify(this.sow.value)
        : JSON.stringify([]);
    let employee =
      this.employee.value && this.employee.value.length > 0
        ? JSON.stringify(this.employee.value)
        : JSON.stringify([]);
    let status =
      this.status.value && this.status.value.length > 0
        ? JSON.stringify(this.status.value)
        : JSON.stringify([2, 4]);
    let division =
      this.division.value && this.division.value.length > 0
        ? JSON.stringify(this.division.value)
        : JSON.stringify([]);
    let subDivision =
      this.subDivision.value && this.subDivision.value.length > 0
        ? JSON.stringify(this.subDivision.value)
        : JSON.stringify([]);
    let region =
      this.region.value && this.region.value.length > 0
        ? JSON.stringify(this.region.value)
        : JSON.stringify([]);
    let level =
      this.level.value && this.level.value.length > 0 
        ? JSON.stringify(this.level.value) 
        : JSON.stringify([]);
    let entity =
      this.entity.value && this.entity.value.length > 0
        ? JSON.stringify(this.entity.value)
        : JSON.stringify([]);
    let salesRegion =
      this.salesRegion.value && this.salesRegion.value.length > 0
        ? JSON.stringify(this.salesRegion.value)
        : JSON.stringify([]);
    let workstream =  this.workstream.value && this.workstream.value.length > 0
        ? JSON.stringify(this.workstream.value)
        : JSON.stringify([]);
    let approver = this.approver.value && this.approver.value.length > 0
    ? JSON.stringify(this.approver.value)
    : JSON.stringify([]);
    localStorage.setItem('tsv2-daily-log-report-portfolio', portfolio);
    localStorage.setItem('tsv2-daily-log-report-sow', sow);
    localStorage.setItem('tsv2-daily-log-report-employee', employee);
    localStorage.setItem('tsv2-daily-log-report-status', status);
    localStorage.setItem('tsv2-daily-log-report-division', division);
    localStorage.setItem('tsv2-daily-log-report-subDivision', subDivision);
    localStorage.setItem('tsv2-daily-log-report-region', region);
    localStorage.setItem('tsv2-daily-log-report-level', level);
    localStorage.setItem('tsv2-daily-log-report-entity', entity);
    localStorage.setItem('tsv2-daily-log-report-salesRegion', salesRegion);
    localStorage.setItem('tsv2-daily-log-report-workstream', workstream);
    localStorage.setItem('tsv2-daily-log-report-approver', approver);
    return this.toastService.showInfo(
      'Timesheet App Message',
      'Success: Filter is Saved',
      7000
    );
  }

  async getFilterData() {
    let portfolio = localStorage.getItem('tsv2-daily-log-report-portfolio') ? JSON.parse(localStorage.getItem('tsv2-daily-log-report-portfolio')) : [];
    this.portfolio.setValue(portfolio);
    let sow = localStorage.getItem('tsv2-daily-log-report-sow') ? JSON.parse(localStorage.getItem('tsv2-daily-log-report-sow')) : [];
    this.sow.setValue(sow);
    let employee = localStorage.getItem('tsv2-daily-log-report-employee') ? JSON.parse(localStorage.getItem('tsv2-daily-log-report-employee')) : [];
    this.employee.setValue(employee);
    let status = localStorage.getItem('tsv2-daily-log-report-status') ? JSON.parse(localStorage.getItem('tsv2-daily-log-report-status')) : [];
    this.status.setValue(status);
    let division = localStorage.getItem('tsv2-daily-log-report-division') ? JSON.parse(localStorage.getItem('tsv2-daily-log-report-division')) : [];
    this.division.setValue(division);
    let subDivision = localStorage.getItem('tsv2-daily-log-report-subDivision') ? JSON.parse(localStorage.getItem('tsv2-daily-log-report-subDivision')) : [];
    this.subDivision.setValue(subDivision);
    let region = localStorage.getItem('tsv2-daily-log-report-region') ? JSON.parse(localStorage.getItem('tsv2-daily-log-report-region')) : [];
    this.region.setValue(region);
    let level = localStorage.getItem('tsv2-daily-log-report-level') ? JSON.parse(localStorage.getItem('tsv2-daily-log-report-level')) : [];
    this.level.setValue(level);
    let entity = localStorage.getItem('tsv2-daily-log-report-entity') ? JSON.parse(localStorage.getItem('tsv2-daily-log-report-entity')) : [];
    this.entity.setValue(entity);
    let salesRegion = localStorage.getItem('tsv2-daily-log-report-salesRegion') ? JSON.parse(localStorage.getItem('tsv2-daily-log-report-salesRegion')) : [];
    this.salesRegion.setValue(salesRegion);
    let workstream = localStorage.getItem('tsv2-daily-log-report-workstream') ? JSON.parse(localStorage.getItem('tsv2-daily-log-report-workstream')) : [];
    this.workstream.setValue(workstream);
    let approver = localStorage.getItem('tsv2-daily-log-report-approver') ? JSON.parse(localStorage.getItem('tsv2-daily-log-report-workstream')) : [];
    this.approver.setValue(workstream);
  }

  async getWorkstream() {
    this.workstreamList = [];
    this.resetSpecificSearchComponent(3);
    return new Promise((resolve, reject) =>
      this.reportService
        .getWorkstream(
          this.sow.value.length > 0 ? this.sow.value : [],
          moment(this.startDate.value).format('YYYY-MM-DD'),
          moment(this.endDate.value).format('YYYY-MM-DD')
        )
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res) => {
            if (res['messType'] == 'S') {
              this.workstreamList =
                res['data'] && res['data'].length > 0 ? res['data'] : [];
            } else {
              this.toastService.showInfo(
                'Timesheet App Message',
                res['messText'],
                7000
              );
            }
            resolve(true);
          },
          error: (err) => {
            this.toastService.showError(
              `Timesheet Error ${err.error.code}`,
              _.filter(this.errorData, { error_code: err.error.code })[0]
                ?.user_message,
              7000
            );
            reject();
          },
        })
    );
  }

  async getApproverDetailsForDailyLogReport() {
    this.approverFilterApiInProgress = true;
    this.approverList = [];
    this.resetSpecificSearchComponent(7);
    return new Promise((resolve, reject) =>
      this.reportService
        .getApproverDetailsForDailyLogReport(
          moment(this.startDate.value).format('YYYY-MM-DD'),
          moment(this.endDate.value).format('YYYY-MM-DD'),
          this.aid,
          this.oid,
          this.portfolio.value,
          this.sow.value
        )
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res) => {
            if (res['messType'] == 'S') {
              this.approverList =
                res['data'] && res['data'].length > 0 ? res['data'] : [];
              let approver = localStorage.getItem(
                'tsv2-daily-log-report-approver'
              )
                ? JSON.parse(
                    localStorage.getItem('tsv2-daily-log-report-approver')
                  )
                : [];
              this.approver.setValue(approver);
            } else {
              let approver = localStorage.getItem(
                'tsv2-daily-log-report-approver'
              )
                ? JSON.parse(
                    localStorage.getItem('tsv2-daily-log-report-approver')
                  )
                : [];
              this.approver.setValue(approver);
              this.toastService.showInfo(
                'Timesheet App Message',
                res['messText'],
                3000
              );
            }
            this.approverFilterApiInProgress = false;
            resolve(true);
          },
          error: (err) => {
            this.toastService.showError(
              `Timesheet Error ${err.error.code}`,
              _.filter(this.errorData, { error_code: err.error.code })[0]
                ?.user_message,
              60000
            );
            this.approverFilterApiInProgress = false;
            reject();
          },
        })
    );
  }
  async getTimesheetReportAccess(){
      this.reportService
        .getTimesheetReportAccess(
          571
        )
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res) => {
           if(!res['reportAccess']){
            this.router.navigateByUrl('/main/reports');
           }
          },
          error: (err) => {
            this.toastService.showError(
              'Timesheet App Message',
              err['messText'],
              3000
            );
          },
        })
  }
}
