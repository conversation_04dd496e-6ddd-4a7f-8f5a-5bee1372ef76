<dx-data-grid
  class="data-grid"
  [dataSource]="weekDetails"
  [showBorders]="true"
  [allowColumnResizing]="true"
  [columnAutoWidth]="true"
  [showColumnLines]="true"
  [showRowLines]="true"
  [paging]="false"
>
  <dxi-column
    *ngIf="reportUiConfig['UI-STATUS-SUBTAB-001']?.is_visible"
    [caption]="reportUiConfig['UI-STATUS-SUBTAB-001']?.caption"
    cellTemplate="weekTemplate"
    [allowSorting]="false"
  >
  </dxi-column>
  <dxi-column
    *ngIf="reportUiConfig['UI-STATUS-SUBTAB-002']?.is_visible"
    [dataField]="reportUiConfig['UI-STATUS-SUBTAB-002']?.data_field"
    [caption]="reportUiConfig['UI-STATUS-SUBTAB-002']?.caption"
    [allowSorting]="false"
  >
  </dxi-column>
  <dxi-column
    *ngIf="reportUiConfig['UI-STATUS-SUBTAB-003']?.is_visible"
    [dataField]="reportUiConfig['UI-STATUS-SUBTAB-003']?.data_field"
    [caption]="reportUiConfig['UI-STATUS-SUBTAB-003']?.caption"
    [allowSorting]="false"
  >
  </dxi-column>
  <dxi-column
  *ngIf="reportUiConfig['UI-STATUS-SUBTAB-014']?.is_visible"
  [dataField]="reportUiConfig['UI-STATUS-SUBTAB-014']?.data_field"
  [caption]="reportUiConfig['UI-STATUS-SUBTAB-014']?.caption"
  [allowSorting]="false"
  >
  </dxi-column>
  <dxi-column
    *ngIf="reportUiConfig['UI-STATUS-SUBTAB-013']?.is_visible"
    [dataField]="reportUiConfig['UI-STATUS-SUBTAB-013']?.data_field"
    [caption]="reportUiConfig['UI-STATUS-SUBTAB-013']?.caption"
    [allowSorting]="false"
  >
  </dxi-column>
  <dxi-column
    *ngIf="reportUiConfig['UI-STATUS-SUBTAB-004']?.is_visible"
    [dataField]="reportUiConfig['UI-STATUS-SUBTAB-004']?.data_field"
    [caption]="reportUiConfig['UI-STATUS-SUBTAB-004']?.caption"
    [allowSorting]="false"
  >
  </dxi-column>
  <dxi-column
    *ngIf="reportUiConfig['UI-STATUS-SUBTAB-005']?.is_visible"
    [dataField]="reportUiConfig['UI-STATUS-SUBTAB-005']?.data_field"
    [caption]="reportUiConfig['UI-STATUS-SUBTAB-005']?.caption"
    [allowSorting]="false"
  >
  </dxi-column>
  <dxi-column
    *ngIf="reportUiConfig['UI-STATUS-SUBTAB-006']?.is_visible"
    [dataField]="reportUiConfig['UI-STATUS-SUBTAB-006']?.data_field"
    [caption]="reportUiConfig['UI-STATUS-SUBTAB-006']?.caption"
    [allowSorting]="false"
  >
  </dxi-column>
  <dxi-column
    *ngIf="reportUiConfig['UI-STATUS-SUBTAB-007']?.is_visible"
    [dataField]="reportUiConfig['UI-STATUS-SUBTAB-007']?.data_field"
    [caption]="reportUiConfig['UI-STATUS-SUBTAB-007']?.caption"
    [allowSorting]="false"
  >
  </dxi-column>
  <dxi-column
    *ngIf="reportUiConfig['UI-STATUS-SUBTAB-008']?.is_visible"
    [dataField]="reportUiConfig['UI-STATUS-SUBTAB-008']?.data_field"
    [caption]="reportUiConfig['UI-STATUS-SUBTAB-008']?.caption"
    [allowSorting]="false"
  >
  </dxi-column>
  <dxi-column
    *ngIf="reportUiConfig['UI-STATUS-SUBTAB-009']?.is_visible"
    [dataField]="reportUiConfig['UI-STATUS-SUBTAB-009']?.data_field"
    [caption]="reportUiConfig['UI-STATUS-SUBTAB-009']?.caption"
    [allowSorting]="false"
  >
  </dxi-column>
  <dxi-column
    *ngIf="reportUiConfig['UI-STATUS-SUBTAB-010']?.is_visible"
    [dataField]="reportUiConfig['UI-STATUS-SUBTAB-010']?.data_field"
    [caption]="reportUiConfig['UI-STATUS-SUBTAB-010']?.caption"
    [allowSorting]="false"
  >
  </dxi-column>
  <dxi-column
    *ngIf="reportUiConfig['UI-STATUS-SUBTAB-015']?.is_visible"
    [dataField]="reportUiConfig['UI-STATUS-SUBTAB-015']?.data_field"
    [caption]="reportUiConfig['UI-STATUS-SUBTAB-015']?.caption"
    [allowSorting]="false"
  >
  </dxi-column>
  <dxi-column
    *ngIf="reportUiConfig['UI-STATUS-SUBTAB-016']?.is_visible"
    [dataField]="reportUiConfig['UI-STATUS-SUBTAB-016']?.data_field"
    [caption]="reportUiConfig['UI-STATUS-SUBTAB-016']?.caption"
    [allowSorting]="false"
  >
  </dxi-column>
  <dxi-column
    *ngIf="reportUiConfig['UI-STATUS-SUBTAB-011']?.is_visible"
    [caption]="reportUiConfig['UI-STATUS-SUBTAB-011']?.caption"
    cellTemplate="statusTemplate"
    [allowSorting]="false"
  >
  </dxi-column>
  <dxi-column
    *ngIf="reportUiConfig['UI-STATUS-SUBTAB-012']?.is_visible"
    [dataField]="reportUiConfig['UI-STATUS-SUBTAB-012']?.data_field"
    [caption]="reportUiConfig['UI-STATUS-SUBTAB-012']?.caption"
    [allowSorting]="false"
  >
  </dxi-column>
  <dxi-column 
    *ngIf="reportUiConfig['UI-STATUS-SUBTAB-017']?.is_visible"
    [dataField]="reportUiConfig['UI-STATUS-SUBTAB-017']?.data_field"
    [caption]="reportUiConfig['UI-STATUS-SUBTAB-017']?.caption" 
    [allowSorting]="false">
  </dxi-column>
  <dxi-column 
    *ngIf="reportUiConfig['UI-STATUS-SUBTAB-018']?.is_visible"
    [dataField]="reportUiConfig['UI-STATUS-SUBTAB-018']?.data_field"
    [caption]="reportUiConfig['UI-STATUS-SUBTAB-018']?.caption" 
    [allowSorting]="false">
  </dxi-column>

  <div
    style="display: flex; align-items: center"
    *dxTemplate="let data of 'statusTemplate'"
  >
    <div
      [ngStyle]="{
        'background-color': data.data.status_color,
        display: (weekDetails | dataVisibility : data.rowIndex) ? '' : 'none'
      }"
      style="border-radius: 50%; width: 12px; height: 12px"
    ></div>
    <span
      [ngStyle]="{
        display: (weekDetails | dataVisibility : data.rowIndex) ? '' : 'none'
      }"
      style="margin-left: 3px"
      >{{ data.data.status }}</span
    >
  </div>

  <div
    style="display: flex; align-items: center"
    *dxTemplate="let data of 'weekTemplate'"
  >
    <div
      [ngStyle]="{
        display: (weekDetails | dataVisibility : data.rowIndex) ? '' : 'none'
      }"
    >
      {{ data.data.week_no }}
      {{ data.data.week_no | weekStartEndDate : weekData }}
    </div>
  </div>
</dx-data-grid>
