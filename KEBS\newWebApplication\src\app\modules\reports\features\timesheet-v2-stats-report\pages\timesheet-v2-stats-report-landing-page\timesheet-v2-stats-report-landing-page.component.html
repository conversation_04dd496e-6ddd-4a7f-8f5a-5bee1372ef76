<div class="bg-container">
  <div class="report-screen">
    <div
      class="align-items-center col-12 p-0"
      style="justify-content: space-between"
    >
      <div class="align-items-center col-11 p-0" style="display: flex">
        <!-- <div
          style="margin-right: 10px; margin-bottom: 7px"
          class="total-count col-1 p-0"
        >
          Total Employees: {{ totalCount }}
        </div> -->
        <div style="margin-right: 10px" class="col-2 p-0">
          <mat-form-field appearance="outline" class="month-form">
            <div style="display: flex; align-items: center; height: 17px">
              <input
                matInput
                [matDatepicker]="dp"
                [formControl]="month"
                placeholder="MMM YYYY"
                (keydown)="onKeyDownMonthSearch($event)"
                [disabled]="durationDisabled"
              />
              <mat-datepicker-toggle [for]="dp">
                <mat-icon matDatepickerToggleIcon class="calendar-icon"
                  >calendar_today</mat-icon
                >
              </mat-datepicker-toggle>
              <mat-datepicker
                #dp
                startView="multi-year"
                [startAt]="startDate"
                [opened]="monthSelected"
                (monthSelected)="onSelect($event, dp)"
              >
              </mat-datepicker>
            </div>
          </mat-form-field>
        </div>
        <div style="margin-right: 10px" class="col-2 p-0">
          <mat-form-field appearance="outline" class="week-form">
            <mat-select
              [formControl]="weekNumber"
              placeholder="Week Number"
              (selectionChange)="onValueChanged()"
            >
              <mat-option
                *ngFor="let week of weeksOfMonth"
                [value]="week.value"
              >
                {{ week.viewValue }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
        <div style="margin-right: 10px" class="col-2 p-0">
          <mat-form-field appearance="outline" class="search-form">
            <input
              matInput
              placeholder="Search"
              [(ngModel)]="udrfSearch"
              (keyup.enter)="onSearch()"
            />
            <mat-icon matSuffix>search</mat-icon>
          </mat-form-field>
        </div>
        <div
          class="col-5 p-0"
          style="display: flex; flex-direction: row; align-items: center"
        >
          <div style="margin-right: 10px; margin-bottom: 5px">
            <button
              mat-icon-button
              class="filterbtn"
              (click)="openUdrfModal()"
              matTooltip="Apply Filters"
            >
              <span class="filtericn"><mat-icon>filter_list</mat-icon></span>
            </button>
          </div>
          <div
            style="margin-right: 10px; display: flex; flex-direction: row"
            *ngIf="_udrfService.udrfData.mainFilterArray.length > 0"
          >
            <mat-icon
              (click)="goToPreviousFilter()"
              style="margin-right: 10px; margin-top: 6px; cursor: pointer"
              >keyboard_arrow_left</mat-icon
            >
            <div
              *ngFor="
                let items of _udrfService.udrfData.mainFilterArray;
                let i = index
              "
            >
              <div
                *ngIf="
                  items.multiOptionSelectSearchValues[0] && displayedFilter == i
                "
                class="searchField"
                style="display: flex !important"
              >
                <div class="searchboxes tooltip" style="display: contents">
                  <span
                    class="searchtitle titlemargin filterfield"
                    [matTooltip]="items.filterName"
                    >{{ items.filterName }}</span
                  >
                  <span class="searchtitle boxstyle"
                    ><span
                      class="filterval"
                      [matTooltip]="items.multiOptionSelectSearchValues[0]"
                      >{{ items.multiOptionSelectSearchValues[0] }}</span
                    ><mat-icon
                      class="clearonefiltericn"
                      (click)="clearonefilter(items, i)"
                      >clear</mat-icon
                    ></span
                  >
                  <mat-icon
                    *ngIf="!dropdownflag"
                    class="dropdownfilter"
                    (click)="viewdroplist(i)"
                    >keyboard_arrow_down</mat-icon
                  >
                  <mat-icon
                    *ngIf="dropdownflag"
                    class="dropdownfilter"
                    (click)="closedroplist()"
                    >keyboard_arrow_up</mat-icon
                  >

                  <mat-card
                    class="tooltiptext dropdownborder"
                    [ngClass]="
                      dropdownflag && i == selecteddropdown
                        ? 'droplistvisible'
                        : 'droplisthidden'
                    "
                  >
                    <div
                      *ngFor="let item of items.checkboxValues; let j = index"
                    >
                      <div style="display: inline-flex">
                        <p class="dropdata">{{ item.checkboxName }}</p>
                        <mat-checkbox
                          class="example-margin"
                          [checked]="item.isCheckboxSelected"
                          (change)="
                            checkboxvalue(
                              $event.checked,
                              i,
                              j,
                              item.checkboxName,
                              item.checkboxId
                            )
                          "
                        >
                        </mat-checkbox>
                      </div>
                    </div>
                  </mat-card>
                </div>
              </div>
            </div>
            <mat-icon
              (click)="goToNextFilter()"
              style="margin-left: 10px; margin-top: 6px; cursor: pointer"
              >keyboard_arrow_right</mat-icon
            >
          </div>
          <div style="margin-right: 10px; margin-bottom: 7px">
            <button mat-stroked-button class="clearbtn" (click)="onClear()">
              Clear
            </button>
          </div>
        </div>
      </div>
      <div style="display: flex; justify-content: flex-end" class="col-1 p-0">
        <div
        matTooltip="Bulk Notify"
        class="download-border"
        (click)="notifyAllEmployees()"
        style="margin-right: 10px;"
      >
        <mat-icon>send</mat-icon>
      </div>
        <!-- <div
          matTooltip="Schedule"
          class="download-border"
        >
          <mat-icon>schedule</mat-icon>
        </div> -->
        <div
          matTooltip="Excel Download"
          class="download-border"
          (click)="onDownload()"
        >
          <mat-icon *ngIf="!exportingInProgress">download</mat-icon>
          <mat-spinner *ngIf="exportingInProgress" diameter="20"></mat-spinner>
        </div>
      </div>
    </div>
    <div class="align-items-center" style="gap: 20px">
      <ng-container *ngFor="let status of allStatus; let i = index">
        <div
          class="summary-card"
          [ngStyle]="{
            'border-left': status.borderColor,
            'opacity': status.isSelected ? '1' : '0.6'
          }"
          [ngClass]="{
            'selected-summary-card': status.isSelected
          }"
          (click)="onClickSummaryCard(i)"
        >
          <div
            [ngStyle]="{
              opacity: status.isSelected ? '1' : '0.6'
            }"
          >
            <p class="summary-card-text">{{ status.viewValue }}</p>
          </div>
          <div class="align-items-btw" style="margin-bottom: 14px">
            <div>
              <p class="summary-card-total-text">
                {{ status.total }}
              </p>
            </div>
            <div
              style="border-radius: 50%; width: 12px; height: 12px"
              [ngStyle]="{
                'background-color': status.color
              }"
            ></div>
          </div>
          <!-- <div
            [ngStyle]="{
              opacity: status.isSelected ? '1' : '0.6'
            }"
            class="view-text"
          >
            View >
          </div> -->
        </div>
      </ng-container>
    </div>
    <dx-data-grid
      class="data-grid"
      #dataGrid
      [height]="dynamicTableHeight"
      [dataSource]="dataSource"
      [remoteOperations]="true"
      [showBorders]="true"
      [allowColumnResizing]="true"
      [allowColumnReordering]="true"
      [columnAutoWidth]="true"
      [showColumnLines]="true"
      [showRowLines]="true"
    >
      <dxo-column-chooser
        [allowSearch]="true"
        [enabled]="true"
        mode="select"
      ></dxo-column-chooser>
      <dxo-scrolling mode="virtual"></dxo-scrolling>
      <dxo-grouping [autoExpandAll]="false"></dxo-grouping>
      <dxo-master-detail [enabled]="true" template="detail"></dxo-master-detail>
      <dxi-column
        *ngIf="reportUiConfig['UI-STATUS-TAB-001']?.is_visible"
        [dataField]="reportUiConfig['UI-STATUS-TAB-001']?.data_field"
        [caption]="reportUiConfig['UI-STATUS-TAB-001']?.caption"
      >
      </dxi-column>
      <dxi-column
        *ngIf="reportUiConfig['UI-STATUS-TAB-002']?.is_visible"
        [dataField]="reportUiConfig['UI-STATUS-TAB-002']?.data_field"
        [caption]="reportUiConfig['UI-STATUS-TAB-002']?.caption"
      >
      </dxi-column>
      <dxi-column
        *ngIf="reportUiConfig['UI-STATUS-TAB-003']?.is_visible"
        [dataField]="reportUiConfig['UI-STATUS-TAB-003']?.data_field"
        [caption]="reportUiConfig['UI-STATUS-TAB-003']?.caption"
        [allowFiltering]="false"
      >
      </dxi-column>
      <dxi-column
        *ngIf="reportUiConfig['UI-STATUS-TAB-004']?.is_visible"
        [dataField]="reportUiConfig['UI-STATUS-TAB-004']?.data_field"
        [caption]="reportUiConfig['UI-STATUS-TAB-004']?.caption"
        [allowFiltering]="false"
      >
      </dxi-column>
      <dxi-column
        *ngIf="reportUiConfig['UI-STATUS-TAB-005']?.is_visible"
        [dataField]="reportUiConfig['UI-STATUS-TAB-005']?.data_field"
        [caption]="reportUiConfig['UI-STATUS-TAB-005']?.caption"
        [allowFiltering]="false"
      >
      </dxi-column>
      <dxi-column
        *ngIf="reportUiConfig['UI-STATUS-TAB-006']?.is_visible"
        [dataField]="reportUiConfig['UI-STATUS-TAB-006']?.data_field"
        [caption]="reportUiConfig['UI-STATUS-TAB-006']?.caption"
        [allowFiltering]="false"
      >
      </dxi-column>
      <dxi-column
        *ngIf="reportUiConfig['UI-STATUS-TAB-007']?.is_visible"
        [dataField]="reportUiConfig['UI-STATUS-TAB-007']?.data_field"
        [caption]="reportUiConfig['UI-STATUS-TAB-007']?.caption"
        [allowSorting]="false"
        [allowFiltering]="false"
      >
      </dxi-column>
      <dxi-column
        *ngIf="reportUiConfig['UI-STATUS-TAB-008']?.is_visible"
        [dataField]="reportUiConfig['UI-STATUS-TAB-008']?.data_field"
        [caption]="reportUiConfig['UI-STATUS-TAB-008']?.caption"
        [allowSorting]="false"
        [allowFiltering]="false"
      >
      </dxi-column>
      <dxi-column
        *ngIf="reportUiConfig['UI-STATUS-TAB-009']?.is_visible"
        [dataField]="reportUiConfig['UI-STATUS-TAB-009']?.data_field"
        [caption]="reportUiConfig['UI-STATUS-TAB-009']?.caption"
        [allowSorting]="false"
        [allowFiltering]="false"
      >
      </dxi-column>
      <dxi-column
        *ngIf="reportUiConfig['UI-STATUS-TAB-010']?.is_visible"
        [caption]="reportUiConfig['UI-STATUS-TAB-010']?.caption"
        cellTemplate="statusTemplate"
      >
      </dxi-column>
      <dxi-column
        *ngIf="reportUiConfig['UI-STATUS-TAB-011']?.is_visible"
        [dataField]="reportUiConfig['UI-STATUS-TAB-011']?.data_field"
        [caption]="reportUiConfig['UI-STATUS-TAB-011']?.caption"
      >
      </dxi-column>
      <dxi-column
        *ngIf="reportUiConfig['UI-STATUS-TAB-020']?.is_visible"
        [dataField]="reportUiConfig['UI-STATUS-TAB-020']?.data_field"
        [caption]="reportUiConfig['UI-STATUS-TAB-020']?.caption"
      >
      </dxi-column>
      <dxi-column
        *ngIf="reportUiConfig['UI-STATUS-TAB-012']?.is_visible"
        [dataField]="reportUiConfig['UI-STATUS-TAB-012']?.data_field"
        [caption]="reportUiConfig['UI-STATUS-TAB-012']?.caption"
      >
      </dxi-column>
      <dxi-column
        *ngIf="reportUiConfig['UI-STATUS-TAB-013']?.is_visible"
        [dataField]="reportUiConfig['UI-STATUS-TAB-013']?.data_field"
        [caption]="reportUiConfig['UI-STATUS-TAB-013']?.caption"
      >
      </dxi-column>
      <dxi-column
        *ngIf="reportUiConfig['UI-STATUS-TAB-014']?.is_visible"
        [dataField]="reportUiConfig['UI-STATUS-TAB-014']?.data_field"
        [caption]="reportUiConfig['UI-STATUS-TAB-014']?.caption"
      >
      </dxi-column>
      <dxi-column
        *ngIf="reportUiConfig['UI-STATUS-TAB-015']?.is_visible"
        [dataField]="reportUiConfig['UI-STATUS-TAB-015']?.data_field"
        [caption]="reportUiConfig['UI-STATUS-TAB-015']?.caption"
      >
      </dxi-column>
      <dxi-column
        *ngIf="reportUiConfig['UI-STATUS-TAB-016']?.is_visible"
        [dataField]="reportUiConfig['UI-STATUS-TAB-016']?.data_field"
        [caption]="reportUiConfig['UI-STATUS-TAB-016']?.caption"
      >
      </dxi-column>
      <dxi-column
        *ngIf="reportUiConfig['UI-STATUS-TAB-017']?.is_visible"
        [dataField]="reportUiConfig['UI-STATUS-TAB-017']?.data_field"
        [caption]="reportUiConfig['UI-STATUS-TAB-017']?.caption"
      >
      </dxi-column>
      <dxi-column
        *ngIf="reportUiConfig['UI-STATUS-TAB-018']?.is_visible"
        [dataField]="reportUiConfig['UI-STATUS-TAB-018']?.data_field"
        [caption]="reportUiConfig['UI-STATUS-TAB-018']?.caption"
      >
      </dxi-column>
       <dxi-column
        *ngIf="reportUiConfig['UI-STATUS-TAB-021']?.is_visible"
        [dataField]="reportUiConfig['UI-STATUS-TAB-021']?.data_field"
        [caption]="reportUiConfig['UI-STATUS-TAB-021']?.caption"
      >
      </dxi-column>
      <dxi-column
        *ngIf="reportUiConfig['UI-STATUS-TAB-021']?.is_visible"
        [dataField]="reportUiConfig['UI-STATUS-TAB-021']?.data_field"
        [caption]="reportUiConfig['UI-STATUS-TAB-021']?.caption"
        [allowSorting]="false"
      >
      </dxi-column>
      <dxi-column
        *ngIf="reportUiConfig['UI-STATUS-TAB-019']?.is_visible"
        [caption]="reportUiConfig['UI-STATUS-TAB-019']?.caption"
        cellTemplate="notificationTemplate"
      >
      </dxi-column>

      <div *dxTemplate="let order of 'detail'">
        <app-detail-grid
          [weekDetails]="order.data.week_details"
          [weekData]="weeksOfMonth"
          [reportUiConfig]="reportUiConfig"
        ></app-detail-grid>
      </div>

      <div
        style="display: flex; align-items: center; justify-content: center"
        *dxTemplate="let data of 'statusTemplate'"
      >
        <div
          [ngStyle]="{
            'background-color':
              data.data.status_id === 4 ? '#52C41A' : '#FF3A46'
          }"
          style="border-radius: 50%; width: 12px; height: 12px"
        ></div>
      </div>

      <div *dxTemplate="let data of 'notificationTemplate'">
        <mat-icon
          matTooltip="Click to Send Notification"
          style="cursor: pointer"
          (click)="sendNotification(data.data)"
          >send</mat-icon
        >
      </div>
    </dx-data-grid>
  </div>
</div>
