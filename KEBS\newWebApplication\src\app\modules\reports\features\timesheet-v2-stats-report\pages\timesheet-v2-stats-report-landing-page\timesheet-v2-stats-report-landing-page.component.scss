.bg-container {
  background-color: #e5e5e5;
  overflow: hidden;
}

.report-screen {
  margin-left: 16px;
  margin-right: 16px;
  margin-bottom: 16px;
  margin-top: 16px;
  padding: 16px 16px 16px 16px;
  min-height: var(--dynamicHeight);
  border-radius: 4px;
  background-color: white;
}

.calendar-icon {
  font-size: 16px;
}

::ng-deep
  .dx-datagrid-filter-row
  .dx-editor-cell
  .dx-editor-with-menu
  .dx-menu {
  // display: none !important;
  pointer-events: none !important;
}

.align-items-center {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.summary-card {
  width: 15%;
  height: 90px;
  background-color: white;
  border: 1px solid rgba(0, 0, 0, 0.125);
  border-radius: 4px;
  padding: 8px;
  cursor: pointer;
}

.summary-card-text {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 14px;
}

.summary-card-total-text {
  font-size: 20px;
  font-weight: 800;
  margin-bottom: 0px;
}

.align-items-btw {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.view-text {
  font-size: 12px;
  font-weight: 500;
}

.total-count {
  font-family: var(--fontFamily);
  font-size: 14px;
  font-weight: 600;
  color: #111434;
}

.selected-summary-card {
  border-bottom: 1px solid #ee4961;
}

.data-grid ::ng-deep .dx-toolbar .dx-toolbar-items-container {
  height: 50px !important;
}

.download-border {
  border: 1px solid;
  border-radius: 4px;
  height: 33px;
  padding: 4px;
  cursor: pointer;
  width: 33px;
  margin-bottom: 6px;
}

.filtericn {
  ::ng-deep.mat-icon {
    font-size: 20px;
    color: #45546e;
  }
}

.filterbtn {
  border: none;
  background-color: white;
  display: flex;
  justify-content: center;
  align-items: center;
}

.search-form {
  width: 100%;
}

.clearbtn {
  font-weight: 600;
  font-size: 12px;
  line-height: 16px;
  border: 1px solid;
  height: 34px;
  border-radius: 4px;
  border-color: #ee4961;
  background-color: #f2d4cdad;
  color: #ee4961;
}

.searchField {
  display: inline-block;
  border: solid thin #dadce2;
  border-radius: 4px;
  height: 36px;
  margin-bottom: 8px;
}

.searchboxes {
  display: flex;
  align-items: center;
}

.titlemargin {
  margin-left: 0px !important;
  margin-right: 0px !important;
}

.searchtitle {
  font-weight: 400;
  font-size: 12px;
  line-height: 16px;
  margin: auto 5px auto 5px;
  padding: 2px;
  padding-left: 10px;
  padding-right: 10px;
}

.clearonefiltericn {
  font-size: 13px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.dropdownfilter {
  font-size: 13px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  margin-top: 5px;
}

.boxstyle {
  background-color: #f2d4cdad;
  height: 1.5rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-right: 0px;
  color: #526179;
}

.filterval {
  width: 100px;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.filterfield {
  width: 57px;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.searchFieldtxt {
  outline: none;
  border: none;
  font-weight: 400;
  font-size: 12px;
  line-height: 16px;
}

.positionFieldlable {
  margin-top: 19px;
  margin-left: 4px;
  color: #a8acb2;
  font-weight: 400;
  font-size: 12px;
  line-height: 16px;
}

.positionField {
  display: inline-block;
  margin-left: 10px;
  padding-top: 5px;
  width: 147px;
  margin-right: 20px;
}

.positionFieldtxt {
  border-radius: 4px;
  height: 40px;
  width: 200px;
  border-color: #b9c0ca;
}

.tooltip {
  position: relative;
  display: inline-block;
}

.droplistvisible {
  visibility: visible !important;
}

.droplisthidden {
  visibility: hidden !important;
}

.dropdata {
  font-size: 12px;
  line-height: 16px;
  letter-spacing: 0.02em;
  text-transform: capitalize;
  color: #111434;
  width: 175px;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.example-margin {
  display: flex;
  right: 10px;
  position: absolute;
}

.dropdownborder {
  z-index: 1;
  width: 230px;
  border: 1px solid lightgrey;
  border-radius: 4px;
  margin-top: 35px;
  padding: 10px !important;
}

.tooltip .tooltiptext {
  visibility: hidden;
  background-color: #ffffff;
  border-radius: 6px;
  padding: 5px;
  padding-top: 7px;
  width: 250x;
  height: 200px;
  position: absolute;
  z-index: 2;
  border-radius: 4px;
  overflow-y: scroll;
  overflow-x: hidden;
}

.tooltip:active .tooltiptext {
  visibility: visible;
}

.month-form {
  width: 100%;
}

.week-form {
  width: 100%;
}
