import { LoginService } from 'src/app/services/login/login.service';
import { TsV2StatsService } from './../../services/ts-v2-stats.service';
import { Component, HostListener, OnInit, ViewChild } from '@angular/core';
import { takeUntil } from 'rxjs/operators';
import { Subject } from 'rxjs';
import { ToasterService } from 'src/app/services/toaster-service/toaster.service';
import * as AspNetData from 'devextreme-aspnet-data-nojquery';
import { FormControl } from '@angular/forms';
import * as moment from 'moment';
import {
  MomentDateAdapter,
  MAT_MOMENT_DATE_ADAPTER_OPTIONS,
} from '@angular/material-moment-adapter';
import {
  DateAdapter,
  MAT_DATE_LOCALE,
  MAT_DATE_FORMATS,
} from '@angular/material/core';
import { JsonToExcelService } from 'src/app/services/excel/json-to-excel.service';
import { MatDialog } from '@angular/material/dialog';
import { UdrfService } from 'src/app/services/udrf/udrf.service';
import * as _ from 'underscore';
import { catchError, finalize } from 'rxjs/operators';
import { throwError, Subscription } from 'rxjs';
import * as XLSX from 'xlsx';
import { Router } from '@angular/router';

export const MY_FORMATS = {
  parse: {
    dateInput: 'MMM YYYY',
  },
  display: {
    dateInput: 'MMM YYYY',
    monthYearLabel: 'MMM YYYY',
    dateA11yLabel: 'LL',
    monthYearA11yLabel: 'MMMM YYYY',
  },
};

@Component({
  selector: 'app-timesheet-v2-stats-report-landing-page',
  templateUrl: './timesheet-v2-stats-report-landing-page.component.html',
  styleUrls: ['./timesheet-v2-stats-report-landing-page.component.scss'],
  providers: [
    {
      provide: DateAdapter,
      useClass: MomentDateAdapter,
      deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS],
    },
    { provide: MAT_DATE_FORMATS, useValue: MY_FORMATS },
  ],
})
export class TimesheetV2StatsReportLandingPageComponent implements OnInit {
  @ViewChild('dataGrid', { static: false }) dataGrid: any;

  exportingInProgress = false;
  initialLoading = false;
  isApiInProgress = false;

  dynamicHeight: string;
  dynamicTableHeight: string;

  currentUser: any;
  oid: any;
  aid: any;

  url: string;
  dataSource: any;
  udrfSearch = '';
  applicationId = 569;
  filterConfig: any;
  excelDownloadData = [];

  monthSelected = false;
  startDate: Date = new Date();
  month = new FormControl(moment());
  weekNumber = new FormControl();

  tsMonthEndDate: any;
  monthStartDate: any;
  monthEndDate: any;
  selectedWeekNumber = null;
  selectedStatus: number = null;

  basicTsConfig: any;
  token: any;
  totalCount = 0;

  dropdownflag = false;
  selecteddropdown: any;
  displayedFilter = 0;

  reportUiConfigList = [];
  reportUiConfig = {};

  protected _onDestroy = new Subject<void>();

  weeksOfMonth = [];
  allStatus = [
    {
      value: 6,
      viewValue: 'Unsubmitted',
      borderColor: '4px solid #5F6C81',
      total: 0,
      color: '#5F6C81',
      isSelected: 0,
    },
    {
      value: 1,
      viewValue: 'Saved',
      borderColor: '4px solid #1890FF',
      total: 0,
      color: '#1890FF',
      isSelected: 0,
    },
    {
      value: 2,
      viewValue: 'Submitted',
      borderColor: '4px solid #FA8C16',
      total: 0,
      color: '#FA8C16',
      isSelected: 0,
    },
    {
      value: 3,
      viewValue: 'Rejected',
      borderColor: '4px solid #FF3A46',
      total: 0,
      color: '#FF3A46',
      isSelected: 0,
    },
    {
      value: 4,
      viewValue: 'Approved',
      borderColor: '4px solid #52C41A',
      total: 0,
      color: '#52C41A',
      isSelected: 0,
    },
  ];

  apiInProgress: boolean = true;
  dataSubscription: Subscription;

  apiInProgress2: boolean = true;
  dataSubscription2: Subscription;

  durationDisabled: boolean = true;

  constructor(
    private authService: LoginService,
    private reportService: TsV2StatsService,
    private toastService: ToasterService,
    private excelService: JsonToExcelService,
    private _dialog: MatDialog,
    public _udrfService: UdrfService,
    private router: Router,
  ) {}

  ngOnDestroy() {
    this._onDestroy.next();
    this._onDestroy.complete();
  }

  async ngOnInit() {
    await this.getTimesheetReportAccess();
    this.initialLoading = true;
    this.calculateDynamicContentHeight();
    this.currentUser = this.authService.getProfile().profile;
    this.oid = this.currentUser.oid;
    this.aid = this.currentUser.aid;
    await this.getTSStatusReportUIConfiguration();
    await this.getBasicTimesheetConfigurations();
    this.token = this.authService.getJwtToken();
    this.url = 'api/timesheetv2/Reports/getTSStatsReportsItemDataNew';
    await this.getMonthStartAndEndDate(moment());
    await this.configureUdrf();
    await this.createDataSource(
      moment(this.monthStartDate).format('YYYY-MM-DD'),
      moment(this.monthEndDate).format('YYYY-MM-DD'),
      this.selectedWeekNumber,
      this.selectedStatus,
      JSON.stringify(this.filterConfig),
      false
    );
    this.initialLoading = false;
  }

  @HostListener('window:resize')
  onResize() {
    this.calculateDynamicContentHeight();
  }

  //Adjust UI size based on window size
  calculateDynamicContentHeight() {
    this.dynamicHeight = window.innerHeight - 112 + 'px';
    document.documentElement.style.setProperty(
      '--dynamicHeight',
      this.dynamicHeight
    );
    this.dynamicTableHeight = window.innerHeight - 289 + 'px';
    document.documentElement.style.setProperty(
      '--dynamicHeight',
      this.dynamicTableHeight
    );
  }

  async getMonthStartAndEndDate(month: moment.Moment) {
    this.weeksOfMonth = [];
    this.weekNumber.patchValue(null);
    for (let i = 0; i < this.allStatus.length; i++) {
      this.allStatus[i].isSelected = 0;
    }
    this.selectedStatus = null;
    this.selectedWeekNumber = null;
    this.udrfSearch = '';
    if (this.tsMonthEndDate === 'END') {
      this.monthStartDate = moment(month).startOf('month');
      this.monthEndDate = moment(month).endOf('month');
    } else {
      if (moment(month).date() > parseInt(this.tsMonthEndDate)) {
        month = moment(month).add(1, 'month').startOf('month');
      }
      this.monthStartDate = moment(month)
        .subtract(1, 'month')
        .date(parseInt(this.tsMonthEndDate) + 1)
        .format('YYYY-MM-DD');
      this.monthEndDate = moment(month)
        .date(parseInt(this.tsMonthEndDate))
        .format('YYYY-MM-DD');
    }
    this.month.setValue(moment(this.monthEndDate));
    let startDate = moment(this.monthStartDate);
    let endDate = moment(this.monthEndDate);
    let currentMonth = moment(this.monthEndDate).month() + 1;
    let dayOfWeekIndex;
    let monthYearDetails = [];

    for (let i = 0; i < 7; i++) {
      if (moment(startDate).date() > parseInt(this.tsMonthEndDate) && i >= 4) {
        monthYearDetails.push({
          year: startDate.year(),
          month: startDate.month() + 1,
          monthName: startDate.format('MMM'),
          noOfWeeks: this.weeksOfMonth.length,
        });
        break;
      } else if (
        this.tsMonthEndDate === 'END' &&
        moment(startDate).month() != moment(this.monthStartDate).month()
      ) {
        monthYearDetails.push({
          year: moment(this.monthStartDate).year(),
          month: moment(this.monthStartDate).month() + 1,
          monthName: moment(this.monthStartDate).format('MMM'),
          noOfWeeks: this.weeksOfMonth.length,
        });
        break;
      }
      if (moment(startDate).day() === 0) {
        dayOfWeekIndex = 7;
      } else {
        dayOfWeekIndex = moment(startDate).day();
      }
      const days = Array(7).fill({ date: '', dayIndex: -1 });
      let j = 0;
      for (let day = dayOfWeekIndex; day <= 7; day++) {
        let currentDate = moment(startDate).add(j, 'days').format('YYYY-MM-DD');
        if (moment(currentDate).date() > this.tsMonthEndDate && i >= 4) {
          endDate = moment(currentDate).subtract(1, 'day');
          break;
        } else if (
          this.tsMonthEndDate === 'END' &&
          moment(currentDate).month() != moment(this.monthStartDate).month()
        ) {
          endDate = moment(currentDate).subtract(1, 'day');
          break;
        } else {
          endDate = moment(startDate).add(j, 'days');
        }
        currentDate = moment(startDate).add(j, 'days').format('YYYY-MM-DD');
        days.splice(day - 1, 1, {
          date: currentDate,
          dayIndex: day,
          costcenter: [],
        });
        j++;
      }
      this.weeksOfMonth.push({
        startDate: startDate.format('YYYY-MM-DD'),
        endDate: endDate.format('YYYY-MM-DD'),
        value: i + 1,
        viewValue:
          'Week ' +
          (i + 1) +
          ' (' +
          startDate.format('DD MMM YYYY') +
          ' - ' +
          endDate.format('DD MMM YYYY') +
          ')',
        timesheetMonth: currentMonth,
      });
      startDate = moment(startDate).add(j, 'days');
      endDate = moment(startDate).add(7, 'days');
    }
  }

  async onSelect(normalizedMonth: moment.Moment, dp) {
    dp.close();
    this.monthSelected = false;
    let month = moment(normalizedMonth);
    await this.getMonthStartAndEndDate(month);
    let dateValue = this.month.value;
    dateValue.month(normalizedMonth.month());
    dateValue.year(normalizedMonth.year());
    this.month.setValue(dateValue);
    await this.initReport();
  }

  async onValueChanged() {
    this.selectedWeekNumber = this.weekNumber.value;
    await this.initReport();
  }

  async onClickSummaryCard(index: number) {
    this.selectedStatus = null;
    if (this.allStatus[index].isSelected == 0) {
      this.selectedStatus = this.allStatus[index]?.value;
      for (let i = 0; i < this.allStatus.length; i++) {
        this.allStatus[i].isSelected = 0;
      }
      this.allStatus[index].isSelected = 1;
      await this.initReport();
    } else {
      for (let i = 0; i < this.allStatus.length; i++) {
        this.allStatus[i].isSelected = 0;
      }
      await this.initReport();
    }
  }

  async sendNotification(data: any) {
    let aid = data['TEEC|associate_id'];
    let oid = data['TEEC|oid'];
    let timesheet_action = data['timesheet_action'];
    let status = data['status_id'];
    let month = data['month'];
    await this.sendNotificationForTSStatusRp(
      aid,
      oid,
      timesheet_action,
      status,
      month
    );
  }

  async createDataSource(
    startDate: any,
    endDate: any,
    weekNumber: any,
    status: any,
    filterConfig: any,
    excelDownload: false
  ) {
    if (!this.filterConfig) {
      return;
    }
    this.isApiInProgress = true;
    // this.getTSStatsReportsItemDataCount(
    //   this.monthStartDate,
    //   this.monthEndDate,
    //   this.selectedWeekNumber,
    //   this.selectedStatus,
    //   JSON.stringify(this.filterConfig)
    // );
    this.dataSource = AspNetData.createStore({
      loadUrl: `${this.url}`,
      loadMethod: 'POST',
      loadParams: {
        aid: this.aid,
        oid: this.oid,
        startDate: startDate,
        endDate: endDate,
        weekNumber: weekNumber,
        status: status,
        filterConfig: filterConfig,
        excelDownload: excelDownload,
      },
      onBeforeSend: (method, ajaxOptions) => {
        ajaxOptions.headers = {
          Authorization: 'Bearer ' + this.token,
        };
      },
      onLoaded: (result) => {
        this.isApiInProgress = false;
        this.getTSStatsReportsItemDataCount(
          moment(this.monthStartDate).format("YYYY-MM-DD"),
          moment(this.monthEndDate).format("YYYY-MM-DD"),
          this.selectedWeekNumber,
          this.selectedStatus,
          JSON.stringify(this.filterConfig)
        );
      },
    });
  }

  onKeyDownMonthSearch(event: KeyboardEvent) {
    event.preventDefault();
  }

  async onClear() {
    if (this.isApiInProgress) {
      return;
    }
    this.dropdownflag = false;
    this.displayedFilter = 0;
    this.udrfSearch = '';
    this.selectedStatus = null;
    this.weekNumber.patchValue(null);
    this.selectedWeekNumber = null;
    this.dataGrid.instance.clearSorting();
    for (let i = 0; i < this.allStatus.length; i++) {
      this.allStatus[i].isSelected = 0;
    }
    await this._udrfService.udrfFunctions.clearSearchAndConfig();
    await this.initReport();
  }

  async clearonefilter(filterItem, index) {
    this._udrfService.udrfFunctions.clearItemConfigApply(filterItem, 1);
    this._udrfService.udrfData.appliedFilterTypeArray.splice(index, 1);
    this._udrfService.udrfData.mainFilterArray.splice(index, 1);
    this.displayedFilter = 0;
    this.dropdownflag = false;
  }

  viewdroplist(val: any) {
    this.dropdownflag = true;
    this.selecteddropdown = val;
  }

  closedroplist() {
    this.dropdownflag = false;
    this.selecteddropdown = -1;
  }

  async checkboxvalue(checkboxval, i, j, name, id) {
    this._udrfService.udrfData.mainFilterArray[i].checkboxValues[
      j
    ].isCheckboxSelected = checkboxval;
    if (checkboxval) {
      this._udrfService.udrfData.mainFilterArray[
        i
      ].multiOptionSelectSearchValues.push(name);
      if (this._udrfService.udrfData.mainFilterArray[i].isIdBased)
        this._udrfService.udrfData.mainFilterArray[
          i
        ].multiOptionSelectSearchValuesWithId.push(id);
    } else {
      //this.udrfService.udrfData.mainFilterArray[i].multiOptionSelectSearchValues.splice()
      this._udrfService.udrfData.mainFilterArray[
        i
      ].multiOptionSelectSearchValues.forEach((item, index) => {
        if (item === name) {
          this._udrfService.udrfData.mainFilterArray[
            i
          ].multiOptionSelectSearchValues.splice(index, 1);
          if (this._udrfService.udrfData.mainFilterArray[i].isIdBased)
            this._udrfService.udrfData.mainFilterArray[
              i
            ].multiOptionSelectSearchValuesWithId.splice(index, 1);
        }
      });
    }
    if (
      this._udrfService.udrfData.mainFilterArray[i]
        .multiOptionSelectSearchValues.length == 0
    ) {
      this.dropdownflag = false;
      this.displayedFilter = 0;
      this.clearonefilter(this._udrfService.udrfData.mainFilterArray[i], i);
    } else {
      await this.createDataSource(
        moment(this.monthStartDate).format('YYYY-MM-DD'),
        moment(this.monthEndDate).format('YYYY-MM-DD'),
        this.selectedWeekNumber,
        this.selectedStatus,
        JSON.stringify(this.filterConfig),
        false
      );
    }
  }

  goToPreviousFilter() {
    this.dropdownflag = false;
    if (this.displayedFilter == 0) {
      this.displayedFilter =
        this._udrfService.udrfData.mainFilterArray.length - 1;
    } else {
      this.displayedFilter--;
    }
  }

  goToNextFilter() {
    this.dropdownflag = false;
    if (
      this.displayedFilter ==
      this._udrfService.udrfData.mainFilterArray.length - 1
    ) {
      this.displayedFilter = 0;
    } else {
      this.displayedFilter++;
    }
  }

  async onDownload() {
    this.dropdownflag = false;
    const dataGridInstance = this.dataGrid.instance;
    const dataSource = dataGridInstance.getDataSource();
    const loadOptions = dataSource.loadOptions();

    if (this.exportingInProgress) {
      this.toastService.showInfo(
        'Timesheet App Message',
        'Kindly Wait As Your Download Is In Progress!',
        5000
      );
      return;
    }

    this.exportingInProgress = true;

    let modifiedRows = [];
    this.excelDownloadData = [];

    await this.getTSStatsReportsItemDataNew(
      0,
      20,
      true,
      JSON.stringify(loadOptions.sort),
      moment(this.monthStartDate).format('YYYY-MM-DD'),
      moment(this.monthEndDate).format('YYYY-MM-DD'),
      this.selectedWeekNumber,
      this.selectedStatus,
      JSON.stringify(this.filterConfig),
      true
    );

    this.excelDownloadData.forEach((row: any) => {
      const mainData = {};

      if (this.reportUiConfig['UI-STATUS-TAB-001']?.is_visible) {
        mainData[`${this.reportUiConfig['UI-STATUS-TAB-001']?.caption}`] =
          row[this.reportUiConfig['UI-STATUS-TAB-001']?.data_field];
      }
      if (this.reportUiConfig['UI-STATUS-TAB-002']?.is_visible) {
        mainData[`${this.reportUiConfig['UI-STATUS-TAB-002']?.caption}`] =
          row[this.reportUiConfig['UI-STATUS-TAB-002']?.data_field];
      }
      if (this.reportUiConfig['UI-STATUS-TAB-009']?.is_visible) {
        mainData[`${this.reportUiConfig['UI-STATUS-TAB-009']?.caption}`] =
          row[this.reportUiConfig['UI-STATUS-TAB-009']?.data_field];
      }
      if (this.reportUiConfig['UI-STATUS-SUBTAB-001']?.is_visible) {
        mainData[`${this.reportUiConfig['UI-STATUS-SUBTAB-001']?.caption}`] =
          row[this.reportUiConfig['UI-STATUS-SUBTAB-001']?.data_field];
      }
      if (this.reportUiConfig['UI-STATUS-DWNLD-001']?.is_visible) {
        mainData[`${this.reportUiConfig['UI-STATUS-DWNLD-001']?.caption}`] = '';
      }
      if (this.reportUiConfig['UI-STATUS-DWNLD-002']?.is_visible) {
        mainData[`${this.reportUiConfig['UI-STATUS-DWNLD-002']?.caption}`] = '';
      }
      if (this.reportUiConfig['UI-STATUS-SUBTAB-002']?.is_visible) {
        mainData[`${this.reportUiConfig['UI-STATUS-SUBTAB-002']?.caption}`] =
          '';
      }
      if (this.reportUiConfig['UI-STATUS-SUBTAB-003']?.is_visible) {
        mainData[`${this.reportUiConfig['UI-STATUS-SUBTAB-003']?.caption}`] =
          '';
      }
      if (this.reportUiConfig['UI-STATUS-SUBTAB-013']?.is_visible) {
        mainData[`${this.reportUiConfig['UI-STATUS-SUBTAB-013']?.caption}`] =
          '';
      }
      if (this.reportUiConfig['UI-STATUS-SUBTAB-004']?.is_visible) {
        mainData[`${this.reportUiConfig['UI-STATUS-SUBTAB-004']?.caption}`] =
          '';
      }
      if (this.reportUiConfig['UI-STATUS-SUBTAB-011']?.is_visible) {
        mainData[`${this.reportUiConfig['UI-STATUS-SUBTAB-011']?.caption}`] =
          '';
      }
      if (this.reportUiConfig['UI-STATUS-SUBTAB-005']?.is_visible) {
        mainData[`${this.reportUiConfig['UI-STATUS-SUBTAB-005']?.caption}`] =
          '';
      }
      if (this.reportUiConfig['UI-STATUS-SUBTAB-006']?.is_visible) {
        mainData[`${this.reportUiConfig['UI-STATUS-SUBTAB-006']?.caption}`] =
          '';
      }
      if (this.reportUiConfig['UI-STATUS-SUBTAB-007']?.is_visible) {
        mainData[`${this.reportUiConfig['UI-STATUS-SUBTAB-007']?.caption}`] =
          '';
      }
      if (this.reportUiConfig['UI-STATUS-SUBTAB-008']?.is_visible) {
        mainData[`${this.reportUiConfig['UI-STATUS-SUBTAB-008']?.caption}`] =
          '';
      }
      if (this.reportUiConfig['UI-STATUS-SUBTAB-009']?.is_visible) {
        mainData[`${this.reportUiConfig['UI-STATUS-SUBTAB-009']?.caption}`] =
          '';
      }
      if (this.reportUiConfig['UI-STATUS-SUBTAB-010']?.is_visible) {
        mainData[`${this.reportUiConfig['UI-STATUS-SUBTAB-010']?.caption}`] =
          '';
      }
      if (this.reportUiConfig['UI-STATUS-TAB-011']?.is_visible) {
        mainData[`${this.reportUiConfig['UI-STATUS-TAB-011']?.caption}`] =
          row[this.reportUiConfig['UI-STATUS-TAB-011']?.data_field];
      }
      if (this.reportUiConfig['UI-STATUS-TAB-012']?.is_visible) {
        mainData[`${this.reportUiConfig['UI-STATUS-TAB-012']?.caption}`] =
          row[this.reportUiConfig['UI-STATUS-TAB-012']?.data_field];
      }
      if (this.reportUiConfig['UI-STATUS-TAB-013']?.is_visible) {
        mainData[`${this.reportUiConfig['UI-STATUS-TAB-013']?.caption}`] =
          row[this.reportUiConfig['UI-STATUS-TAB-013']?.data_field];
      }
      if (this.reportUiConfig['UI-STATUS-TAB-014']?.is_visible) {
        mainData[`${this.reportUiConfig['UI-STATUS-TAB-014']?.caption}`] =
          row[this.reportUiConfig['UI-STATUS-TAB-014']?.data_field];
      }
      if (this.reportUiConfig['UI-STATUS-TAB-015']?.is_visible) {
        mainData[`${this.reportUiConfig['UI-STATUS-TAB-015']?.caption}`] =
          row[this.reportUiConfig['UI-STATUS-TAB-015']?.data_field];
      }
      if (this.reportUiConfig['UI-STATUS-TAB-016']?.is_visible) {
        mainData[`${this.reportUiConfig['UI-STATUS-TAB-016']?.caption}`] =
          row[this.reportUiConfig['UI-STATUS-TAB-016']?.data_field];
      }
      if (this.reportUiConfig['UI-STATUS-TAB-017']?.is_visible) {
        mainData[`${this.reportUiConfig['UI-STATUS-TAB-017']?.caption}`] =
          row[this.reportUiConfig['UI-STATUS-TAB-017']?.data_field];
      }
      if (this.reportUiConfig['UI-STATUS-TAB-018']?.is_visible) {
        mainData[`${this.reportUiConfig['UI-STATUS-TAB-018']?.caption}`] =
          row[this.reportUiConfig['UI-STATUS-TAB-018']?.data_field];
      }
      if (this.reportUiConfig['UI-STATUS-SUBTAB-014']?.is_visible) {
        mainData[`${this.reportUiConfig['UI-STATUS-SUBTAB-014']?.caption}`] =
          '';
      }
      if (this.reportUiConfig['UI-STATUS-TAB-020']?.is_visible) {
        mainData[`${this.reportUiConfig['UI-STATUS-TAB-020']?.caption}`] =
          row[this.reportUiConfig['UI-STATUS-TAB-020']?.data_field];
      }
       if (this.reportUiConfig['UI-STATUS-TAB-021']?.is_visible) {
        mainData[`${this.reportUiConfig['UI-STATUS-TAB-021']?.caption}`] =
          row[this.reportUiConfig['UI-STATUS-TAB-021']?.data_field];
      }
      if (this.reportUiConfig['UI-STATUS-SUBTAB-015']?.is_visible) {
        mainData[`${this.reportUiConfig['UI-STATUS-SUBTAB-015']?.caption}`] =
          '';
      }
      if (this.reportUiConfig['UI-STATUS-SUBTAB-016']?.is_visible) {
        mainData[`${this.reportUiConfig['UI-STATUS-SUBTAB-016']?.caption}`] =
          '';
      }
      if (this.reportUiConfig['UI-STATUS-SUBTAB-017']?.is_visible) {
        mainData[`${this.reportUiConfig['UI-STATUS-SUBTAB-017']?.caption}`] =
          '';
      }
      if (this.reportUiConfig['UI-STATUS-SUBTAB-018']?.is_visible) {
        mainData[`${this.reportUiConfig['UI-STATUS-SUBTAB-018']?.caption}`] =
          '';
      }
      

      // Iterate over week_details and create a new row for each object
      row.week_details.forEach((week: any) => {
        const {
          week_no,
          cost_center,
          cost_center_description,
          sub_project_name,
          location,
          status,
          formatted_hours,
          formatted_billable_hours,
          formatted_nonbillable_hours,
          formatted_overtime_hours,
          leave_count,
          wo_count,
          zifo_sow_reference,
          saved_on,
          submitted_on,
          saved_by,
          submitted_by
        } = week;
        const modifiedRow = { ...mainData };
        if (this.reportUiConfig['UI-STATUS-SUBTAB-001']?.is_visible) {
          modifiedRow[this.reportUiConfig['UI-STATUS-SUBTAB-001']?.caption] =
            week_no;
        }
        if (this.reportUiConfig['UI-STATUS-DWNLD-001']?.is_visible) {
          modifiedRow[this.reportUiConfig['UI-STATUS-DWNLD-001']?.caption] =
            moment(_.pluck(this.weeksOfMonth, 'startDate')[week_no - 1]).format("DD-MMM-YYYY");
        }
        if (this.reportUiConfig['UI-STATUS-DWNLD-002']?.is_visible) {
          modifiedRow[this.reportUiConfig['UI-STATUS-DWNLD-002']?.caption] =
           moment(_.pluck(this.weeksOfMonth, 'endDate')[week_no - 1]).format("DD-MMM-YYYY");
        }
        if (this.reportUiConfig['UI-STATUS-SUBTAB-002']?.is_visible) {
          modifiedRow[this.reportUiConfig['UI-STATUS-SUBTAB-002']?.caption] =
            cost_center;
        }
        if (this.reportUiConfig['UI-STATUS-SUBTAB-003']?.is_visible) {
          modifiedRow[this.reportUiConfig['UI-STATUS-SUBTAB-003']?.caption] =
            cost_center_description;
        }
        if (this.reportUiConfig['UI-STATUS-SUBTAB-013']?.is_visible) {
          modifiedRow[this.reportUiConfig['UI-STATUS-SUBTAB-013']?.caption] =
            sub_project_name;
        }
        if (this.reportUiConfig['UI-STATUS-SUBTAB-004']?.is_visible) {
          modifiedRow[this.reportUiConfig['UI-STATUS-SUBTAB-004']?.caption] =
            location;
        }
        if (this.reportUiConfig['UI-STATUS-SUBTAB-011']?.is_visible) {
          modifiedRow[this.reportUiConfig['UI-STATUS-SUBTAB-011']?.caption] =
            status;
        }
        if (this.reportUiConfig['UI-STATUS-SUBTAB-005']?.is_visible) {
          modifiedRow[this.reportUiConfig['UI-STATUS-SUBTAB-005']?.caption] =
            formatted_hours;
        }
        if (this.reportUiConfig['UI-STATUS-SUBTAB-006']?.is_visible) {
          modifiedRow[this.reportUiConfig['UI-STATUS-SUBTAB-006']?.caption] =
            formatted_billable_hours;
        }
        if (this.reportUiConfig['UI-STATUS-SUBTAB-007']?.is_visible) {
          modifiedRow[this.reportUiConfig['UI-STATUS-SUBTAB-007']?.caption] =
            formatted_nonbillable_hours;
        }
        if (this.reportUiConfig['UI-STATUS-SUBTAB-008']?.is_visible) {
          modifiedRow[this.reportUiConfig['UI-STATUS-SUBTAB-008']?.caption] =
            formatted_overtime_hours;
        }
        if (this.reportUiConfig['UI-STATUS-SUBTAB-009']?.is_visible) {
          modifiedRow[this.reportUiConfig['UI-STATUS-SUBTAB-009']?.caption] =
            leave_count;
        }
        if (this.reportUiConfig['UI-STATUS-SUBTAB-010']?.is_visible) {
          modifiedRow[this.reportUiConfig['UI-STATUS-SUBTAB-010']?.caption] =
            wo_count;
        }
        if (this.reportUiConfig['UI-STATUS-SUBTAB-014']?.is_visible) {
          modifiedRow[this.reportUiConfig['UI-STATUS-SUBTAB-014']?.caption] =
          zifo_sow_reference;
        }
        if (this.reportUiConfig['UI-STATUS-SUBTAB-015']?.is_visible) {
          modifiedRow[this.reportUiConfig['UI-STATUS-SUBTAB-015']?.caption] =
          saved_on;
        }
        if (this.reportUiConfig['UI-STATUS-SUBTAB-016']?.is_visible) {
          modifiedRow[this.reportUiConfig['UI-STATUS-SUBTAB-016']?.caption] =
          submitted_on;
        }
        if (this.reportUiConfig['UI-STATUS-SUBTAB-017']?.is_visible) {
          modifiedRow[this.reportUiConfig['UI-STATUS-SUBTAB-017']?.caption] =
          saved_by;
        }
        if (this.reportUiConfig['UI-STATUS-SUBTAB-018']?.is_visible) {
          modifiedRow[this.reportUiConfig['UI-STATUS-SUBTAB-018']?.caption] =
          submitted_by;
        }
        modifiedRows.push(modifiedRow);
      });
    });

    //this.excelService.exportAsExcelFile(modifiedRows, 'Timesheet_Stats_Report');
    let worksheetData = modifiedRows.map(item => ({
      ...item,
      "Week Start Date": item["Week Start Date"] ? this.convertToDate(item["Week Start Date"]) : "NA",
      "Week End Date": item["Week End Date"] ? this.convertToDate(item["Week End Date"]) : "NA",
      "Saved On": (item["Saved On"] && item["Saved On"] != 'NA') ? this.convertToDate(item["Saved On"]) : "NA",
      "Submitted On": (item["Submitted On"] && item["Submitted On"] != 'NA') ? this.convertToDate(item["Submitted On"]) : "NA",
      }));
      
      // Create a worksheet
    const worksheet = XLSX.utils.json_to_sheet(worksheetData);
      
      // Set cell format for the date columns
    const dateColumns = ['Week Start Date', 'Week End Date', 'Saved On', 'Submitted On'];
    dateColumns.forEach(col => {
      const columnLetter = this.getColumnLetter(worksheet, col);
      if (columnLetter) {
        for (let row = 2; row <= worksheetData.length + 1; row++) { // Assuming header is on the first row
          const cellAddress = `${columnLetter}${row}`;
          if (worksheet[cellAddress]) {
            worksheet[cellAddress].z = 'DD-MMM-YYYY';
          }
        }
      }
    });
    // Create a workbook
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'data');
    // Export to Excel file
    XLSX.writeFile(workbook, 'Timesheet_Stats_Report.xlsx');
    this.exportingInProgress = false;
  }

  async openUdrfModal() {
    this.dropdownflag = false;
    const { UdrfModalComponent } = await import(
      'src/app/modules/shared-lazy-loaded-components/udrf-modal/udrf-modal.component'
    );
    const openUdrfModalComponent = this._dialog.open(UdrfModalComponent, {
      minWidth: '100%',
      height: '84%',
      position: { top: '0px', left: '77px' },
      disableClose: true,
    });
  }

  async onSearch() {
    await this.initReport();
  }

  async configureUdrf() {
    this._udrfService.udrfData.applicationId = this.applicationId;
    this._udrfService.udrfData.isItemDataLoading = true;
    this._udrfService.udrfUiData.showItemDataCount = false;
    this._udrfService.udrfUiData.showSearchBar = false;
    this._udrfService.udrfUiData.showActionButtons = true;
    this._udrfService.udrfUiData.showUdrfModalButton = false;
    this._udrfService.udrfUiData.showColumnConfigButton = false;
    this._udrfService.udrfUiData.showSettingsModalButton = false;
    this._udrfService.udrfUiData.showNewReleasesButton = false;
    this._udrfService.udrfUiData.showReportDownloadButton = false;
    this._udrfService.udrfUiData.isReportDownloading = false;
    this._udrfService.udrfUiData.itemHasOpenInNewTab = false;
    this._udrfService.udrfUiData.horizontalScroll = false;
    this._udrfService.udrfUiData.itemHasQuickCta = false;
    this._udrfService.udrfUiData.collapseAll = false;
    this._udrfService.udrfUiData.showCollapseButton = false;
    this._udrfService.udrfUiData.countForOnlyThisReport = false;
    this._udrfService.udrfUiData.toggleChecked = false;
    this._udrfService.udrfUiData.countFlag = false;
    this._udrfService.udrfUiData.isMultipleView = false;
    this._udrfService.udrfUiData.itemHasDownloadButton = false;
    this._udrfService.udrfUiData.emailPluginVisible = false;
    this._udrfService.udrfUiData.itemHasDownloadButton = false;
    this._udrfService.udrfUiData.itemHasAttachFileButton = false;
    this._udrfService.udrfUiData.itemHasComments = false;
    this._udrfService.udrfUiData.isMoreOptionsNeeded = false;
    this._udrfService.udrfUiData.completeProfileBtn = false;
    this._udrfService.udrfUiData.searchPlaceholder = '';
    this._udrfService.getAppUdrfConfig(
      this.applicationId,
      await this.initReport.bind(this)
    );
  }

  async initReport() {
    this.durationDisabled = true;
    this.filterConfig = {
      mainFilterArray: this._udrfService.udrfData.mainFilterArray,
      mainSearchParameter: this.udrfSearch,
      searchTableDetails: this._udrfService.udrfData.searchTableDetails,
      txTableDetails: this._udrfService.udrfData.txTableDetails,
    };
    if (!this.initialLoading) {
      this.createDataSource(
        moment(this.monthStartDate).format('YYYY-MM-DD'),
        moment(this.monthEndDate).format('YYYY-MM-DD'),
        this.selectedWeekNumber,
        this.selectedStatus,
        JSON.stringify(this.filterConfig),
        false
      );
    }
  }

  async getBasicTimesheetConfigurations() {
    return new Promise((resolve, reject) =>
      this.reportService
        .getBasicTimesheetConfigurations(this.oid, this.aid)
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res) => {
            if (res['messType'] == 'S') {
              this.basicTsConfig = res['data'];
              this.tsMonthEndDate = this.basicTsConfig.timesheet_month_end_date;
            } else {
              this.toastService.showInfo(
                'Timesheet App Message',
                res['messText'],
                3000
              );
            }
            resolve(true);
          },
          error: (err) => {
            this.toastService.showError(
              'Timesheet App Message',
              err['messText'],
              3000
            );
            reject();
          },
        })
    );
  }

  async getTSStatsReportsItemDataNew(
    skip,
    take,
    requireTotalCount,
    sort,
    startDate,
    endDate,
    weekNumber,
    status,
    filterConfig,
    excelDownload
  ) {
    if (this.dataSubscription2) {
      this.dataSubscription2.unsubscribe();
    }
    return new Promise((resolve, reject) =>
      this.dataSubscription2 =  this.reportService
        .getTSStatsReportsItemDataNew(
          this.aid,
          this.oid,
          skip,
          take,
          requireTotalCount,
          sort,
          startDate,
          endDate,
          weekNumber,
          status,
          filterConfig,
          excelDownload
        )
        .pipe(catchError((error) => {
          this.toastService.showError(
            'Report Data Retrieval Failed',
            '',
            60000
          );
          this.apiInProgress2 = false;
          return throwError(error);
        }),
        finalize(() => {
          this.apiInProgress2 = false;
        }))
        .subscribe({
          next: (res) => {
            if (res['messType'] == 'S') {
              this.excelDownloadData = res['data'];
            } else {
              this.toastService.showInfo(
                'Timesheet App Message',
                res['messText'],
                3000
              );
            }
            resolve(true);
          },
          error: (err) => {
            this.toastService.showError(
              'Timesheet App Message',
              err['messText'],
              3000
            );
            reject();
          },
        })
    );
  }

  async getTSStatsReportsItemDataCount(
    startDate,
    endDate,
    weekNumber,
    status,
    filterConfig
  ) {
    if (this.dataSubscription) {
      this.dataSubscription.unsubscribe();
    }
    return new Promise((resolve, reject) =>
      this.dataSubscription = this.reportService
        .getTSStatsReportsItemDataCount(
          this.aid,
          this.oid,
          startDate,
          endDate,
          weekNumber,
          status,
          filterConfig
        )
        .pipe(
          catchError((error) => {
          this.toastService.showError(
            'Report Data Retrieval Failed',
            '',
            60000
          );
          this.apiInProgress = false;
          return throwError(error);
        }),
        finalize(() => {
          this.apiInProgress = false;
        }))
        .subscribe({
          next: (res) => {
            if (res['messType'] == 'S') {
              this.totalCount = res['data']?.totalCount;
              this.allStatus[0].total = res['data']?.unsubmitted;
              this.allStatus[1].total = res['data']?.saved;
              this.allStatus[2].total = res['data']?.submitted;
              this.allStatus[3].total = res['data']?.rejected;
              this.allStatus[4].total = res['data']?.approved;
              this.durationDisabled = false;
            } else {
              this.totalCount = res['data']?.totalCount;
              this.allStatus[0].total = res['data']?.unsubmitted;
              this.allStatus[1].total = res['data']?.saved;
              this.allStatus[2].total = res['data']?.submitted;
              this.allStatus[3].total = res['data']?.rejected;
              this.allStatus[4].total = res['data']?.approved;
              this.toastService.showInfo(
                'Timesheet App Message',
                res['messText'],
                3000
              );
              this.durationDisabled = false;
            }
            resolve(true);
          },
          error: (err) => {
            this.totalCount = 0;
            this.allStatus[0].total = 0;
            this.allStatus[1].total = 0;
            this.allStatus[2].total = 0;
            this.allStatus[3].total = 0;
            this.allStatus[4].total = 0;
            this.toastService.showError(
              'Timesheet App Message',
              err['messText'],
              3000
            );
            this.durationDisabled = false;
            reject();
          },
        })
    );
  }

  async sendNotificationForTSStatusRp(
    aid,
    oid,
    timesheet_action,
    status,
    month
  ) {
    return new Promise((resolve, reject) =>
      this.reportService
        .sendNotificationForTSStatusRp(
          aid,
          oid,
          timesheet_action,
          status,
          month
        )
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res) => {
            if (res['messType'] == 'S') {
              this.toastService.showSuccess(
                'Timesheet App Message',
                res['messText'],
                3000
              );
            } else {
              this.toastService.showInfo(
                'Timesheet App Message',
                res['messText'],
                3000
              );
            }
            resolve(true);
          },
          error: (err) => {
            this.toastService.showError(
              'Timesheet App Message',
              err['messText'],
              3000
            );
            reject();
          },
        })
    );
  }

  async getTSStatusReportUIConfiguration() {
    return new Promise((resolve, reject) =>
      this.reportService
        .getTSStatusReportUIConfiguration()
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res) => {
            if (res['messType'] == 'S') {
              this.reportUiConfigList = res['data'];
              this.reportUiConfigList.forEach((val) => {
                this.reportUiConfig[val.ui_config_id] = val;
              });
            } else {
              this.toastService.showInfo(
                'Timesheet App Message',
                res['messText'],
                3000
              );
            }
            resolve(true);
          },
          error: (err) => {
            this.toastService.showError(
              'Timesheet App Message',
              err['messText'],
              3000
            );
            reject();
          },
        })
    );
  }

  notifyAllEmployees() {
    return new Promise((resolve, reject) =>
      this.reportService
        .notifyAllEmployees(
          this.currentUser.aid,
          this.currentUser.oid,
          moment(this.monthStartDate).format('YYYY-MM-DD'),
          moment(this.monthEndDate).format('YYYY-MM-DD')
        )
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res) => {
            this.toastService.showInfo(
              'Timesheet App Message',
              `Notification triggered to all employees who had not submitted the timeshet for the month of ${moment(
                this.monthEndDate
              ).format('MMM YYYY')}, please note that if all employee submitted the timesheet, notification will not be sent`,
              3000
            );
            resolve(true);
          },
          error: (err) => {
            this.toastService.showError(
              'Timesheet App Message',
              err['messText'],
              3000
            );
            reject();
          },
        })
    );
  }

  convertToDate(dateStr): Date {
    if(dateStr != undefined || dateStr != '' || dateStr != 'NA' || dateStr != ' '){
      const [day, month, year] = dateStr.split('-');
      const monthIndex = new Date(`${month} 1, 2000`).getMonth();
      return new Date(Number(year), monthIndex, Number(day));
    }
  }

  getColumnLetter(worksheet: XLSX.WorkSheet, columnName: string): string | null {
    const range = XLSX.utils.decode_range(worksheet['!ref'] as string);
    for (let col = range.s.c; col <= range.e.c; col++) {
      const cellAddress = XLSX.utils.encode_cell({ c: col, r: 0 });
      if (worksheet[cellAddress]?.v === columnName) {
        return XLSX.utils.encode_col(col);
      }
    }
    return null;
  }

  async getTimesheetReportAccess(){
      this.reportService
        .getTimesheetReportAccess(
          569
        )
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res) => {
           if(!res['reportAccess']){
            this.router.navigateByUrl('/main/reports');
           }
          },
          error: (err) => {
            this.toastService.showError(
              'Timesheet App Message',
              err['messText'],
              3000
            );
          },
        })
  }
}
