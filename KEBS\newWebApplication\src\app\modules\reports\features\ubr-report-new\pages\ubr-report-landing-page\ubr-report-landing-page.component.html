<div class="container-fluid ubr-posting pl-0 pr-0">
    <!-- <udrf-header></udrf-header> -->
    <div class="row pt-2">
        <div class="col d-flex">
            <div>
            <mat-icon class="period-header pt-2">description</mat-icon>
            </div>
            <div>
            <div class="row" style="color: #cf0001; font-weight: 600; font-size: 16px;">{{displayMonthYear}}</div>
            <div class="row" style="color:#000; font-size: 14px; font-weight: 400;">UBR Report for the month Of {{displayMonthYear}}</div>
            </div>
        </div>
        <!-- <div class="col"></div> -->
        <div class="col-3">
            <mat-form-field
              appearance="outline"
              class="p-0 ml-auto"
              style="
                width: 100%;
                font-size: 13px;
                border: none;
                border-radius: 20px;
              "
            >
              <span matPrefix> </span>
              <input
                matInput
                style="padding-bottom: 3px; border: none"
                placeholder="Search by Project Code"
                [(ngModel)]="_udrfService.udrfData.mainSearchParameter"
                (keyup.enter)="_udrfService.udrfFunctions.callAllDataApis()"
              />
              <mat-icon matSuffix>
                <button
                  mat-button
                  *ngIf="_udrfService.udrfData.mainSearchParameter"
                  matSuffix
                  mat-icon-button
                  (click)="_udrfService.udrfFunctions.stopSearchingOpportunities()"
                  style="height: 30px; width: 30px; line-height: 1"
                >
                  <mat-icon
                    style="font-size: 18px !important; color: #66615b !important"
                    matTooltip="Clear Search"
                  >
                    close
                  </mat-icon>
                </button>
              </mat-icon>
            </mat-form-field>
        </div>
        <div class="col-1">
            <!-- <span>Download Report </span> -->
            <mat-icon (click)="downloadReport()" class="pt-2" style="cursor: pointer">download</mat-icon>
        </div>
    </div>
    <div class="row">
        <div class="col-2 mr-3 ml-3 pb-4 pt-2 summary-card revenue">
            <div class="row">
                <div class="col-10 p-0">
                    <div class="row ubr-card-header pt-2">
                        {{revenueSummaryCardObject.dataType}}
                    </div>
                    <div class="row card-amount pt-3" [matTooltip]="(revenueSummaryCardObject.dataTypeValue || '0') + ' ' + defaultCurrency">
                        <div class="col-2 p-0">{{defaultCurrency}}</div>
                        <div class="col content">
                            {{revenueSummaryCardObject.dataTypeValue}}
                        </div>
                    </div>
                </div>
                <div class="col p-0 pt-4">
                    <mat-icon style="color: #6B7A99">sticky_note_2</mat-icon>
                </div>
            </div>
        </div>
        <div class="col-2 mr-3 pb-4 pt-2 summary-card billing">
            <div class="row">
                <div class="col-10 p-0">
                    <div class="row ubr-card-header pt-2">
                        {{billingSummaryCardObject.dataType}}
                    </div>
                    <div class="row card-amount pt-3" [matTooltip]="(billingSummaryCardObject.dataTypeValue || '0') + ' ' + defaultCurrency">
                        <div class="col-2 p-0">{{defaultCurrency}}</div>
                        <div class="col content">
                            {{billingSummaryCardObject.dataTypeValue}}
                        </div>
                    </div>
                </div>
                <div class="col p-0 pt-4">
                    <mat-icon style="color: #6B7A99">payments</mat-icon>
                </div>
            </div>
        </div>
        <div class="col-2 mr-3 pb-4 pt-2 summary-card ubr">
            <div class="row">
                <div class="col-10 p-0">
                    <div class="row ubr-card-header pt-2">
                        {{ubrSummaryCardObject.dataType}}
                    </div>
                    <div class="row card-amount pt-3" [matTooltip]="(ubrSummaryCardObject.dataTypeValue || '0') + ' ' + defaultCurrency">
                        <div class="col-2 p-0">{{defaultCurrency}}</div>
                        <div class="col content">
                            {{ubrSummaryCardObject.dataTypeValue}}
                        </div>
                    </div>
                </div>
                <div class="col p-0 pt-4">
                    <mat-icon style="color: #6B7A99">table_chart_view</mat-icon>
                </div>
            </div>
        </div>
    </div>
    <udrf-body></udrf-body>
    <ngx-spinner size="medium" type="ball-clip-rotate"  bdColor="rgba(236, 233, 233, 0.8)" color="#cf0001"></ngx-spinner>   
</div>
