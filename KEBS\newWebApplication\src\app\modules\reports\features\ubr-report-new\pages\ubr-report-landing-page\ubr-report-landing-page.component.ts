import { Compiler, Component, OnInit } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { ActivatedRoute, Router } from '@angular/router';
import { NgxSpinnerService } from 'ngx-spinner';
import { Overlay, OverlayRef } from 'ngx-toastr';
import { Subscription, Subject } from 'rxjs';
import { ErrorService } from 'src/app/services/error/error.service';
import { JsonToExcelService } from 'src/app/services/excel/json-to-excel.service';
import { ToasterService } from 'src/app/services/toaster-service/toaster.service';
import { UdrfService } from 'src/app/services/udrf/udrf.service';
import { UtilityService } from 'src/app/services/utility/utility.service';
import {UbrReportNewService} from '../../services/ubr-report-new.service';
import * as moment from 'moment';
import { takeUntil } from 'rxjs/operators';
import { animate, state, style, transition, trigger } from '@angular/animations';
import { LoginService } from "src/app/services/login/login.service";
import *  as _ from 'underscore';
import swal from "sweetalert2";
import {UbrCostCenterDetailsDialogComponent} from "../../components/ubr-cost-center-details-dialog/ubr-cost-center-details-dialog.component";
@Component({
  selector: 'app-ubr-report-landing-page',
  templateUrl: './ubr-report-landing-page.component.html',
  styleUrls: ['./ubr-report-landing-page.component.scss']
})
export class UbrReportLandingPageComponent implements OnInit {

  applicationId = 972;
  requestDataSubscription: Subscription;
  misItemDataCurrentIndex = 0;
  protected $onDestroy = new Subject<void>();
  protected $onAppApiCalled = new Subject<void>();
  selectedItemId: number;
  overlayRef: OverlayRef;
  $misRuleDataSubscription: Subscription
  $misRuleDataTotalSubscription: Subscription
  isSideNavLoading: boolean = true
  filterId: any
  dummySideNavData = Array.from({ length: 15 }, (_, i) => i + 1)
  sideNavData = [];
  filterConfig: any;
  ruleTableDetails: any;
  legalEntityList: any;
  legalEntity = new FormControl(1);
  protected _onDestroy = new Subject<void>();
  selectedToggle: string = 'neutral';
  isExpanded = false;
  searchParameter = '';

  // Columns 
  udrfBodyColumns = [
    {
      item: 'project_name',
      header: 'Portfolio Name',
      isVisible: 'true',
      type: 'text',
      position: 1,
      isActive: true,
      colSize: '2',
      textClass: 'value13Bold',
      headerTextClass: 'text-left',
      filterId: 2,
      sortOrder: 'N',
      width: 300,
      hasColumnClick: true
    },
    {
      item: 'item_name',
      header: 'Project Name',
      isVisible: 'true',
      type: 'text',
      position: 2,
      isActive: true,
      colSize: '2',
      textClass: 'zf-theme-light',
      headerTextClass: 'text-left',
      filterId: 3,
      sortOrder: 'N',
      width: 300,
      hasColumnClick: true
    },
    {
      item: 'profit_center',
      header: 'Project Code',
      isVisible: 'true',
      type: 'text',
      position: 3,
      isActive: true,
      colSize: '2',
      textClass: 'zf-theme-light colorRed',
      headerTextClass: 'text-center',
      filterId: 4,
      sortOrder: 'N',
      width: 250,
      hasColumnClick: true
    },
    {
      item: 'currency_code',
      header: 'Currency',
      isVisible: 'true',
      type: 'text',
      position: 11,
      isActive: true,
      colSize: '2',
      textClass: 'zf-theme-light',
      headerTextClass: 'text-right',
      filterId: 9,
      sortOrder: 'N',
      width: 100,
      hasColumnClick: true
    },
    {
      item: 'revenue_amount',
      header: 'Revenue',
      isVisible: 'true',
      type: 'text',
      position: 11,
      isActive: true,
      colSize: '2',
      textClass: 'zf-theme-light right-to-left-input',
      headerTextClass: 'text-right',
      headerTextClassForAlignment: "text-center-alignment",
      headerClassText: 'text-right left-padding',
      filterId: 9,
      sortOrder: 'N',
      width: 143,
      hasColumnClick: true
    },
    {
      item: 'billing',
      header: 'Billing',
      isVisible: 'true',
      type: 'text',
      position: 11,
      isActive: true,
      colSize: '2',
      textClass: 'zf-theme-light right-to-left-input',
      headerTextClass: 'text-right',
      headerTextClassForAlignment: "text-center-alignment",
      headerClassText: 'text-right left-padding',
      filterId: 9,
      sortOrder: 'N',
      width: 143,
      hasColumnClick: true
    },
    {
      item: 'ubr',
      header: 'UBR',
      isVisible: 'true',
      type: 'text',
      position: 11,
      isActive: true,
      colSize: '2',
      textClass: 'zf-theme-light right-to-left-input',
      headerTextClass: 'text-right',
      headerTextClassForAlignment: "text-center-alignment",
      headerClassText: 'text-right left-padding',
      filterId: 9,
      sortOrder: 'N',
      width: 142,
      hasColumnClick: true
    },
  ]

  typeSelected: any;
  searchParamAfterUpdate = null;
  profile: any;
  defaultCurrency = "INR";
  isAuthorized: boolean = true;
  constructor(
    public _udrfService: UdrfService,
    public _misService: UbrReportNewService,
    public errorService: ErrorService,
    private spinnerService: NgxSpinnerService,
    private overlay: Overlay,
    private compiler: Compiler,
    private router: Router,
    private _utilityService: UtilityService,
    private dialog: MatDialog,
    private snackBar: MatSnackBar,
    private _excelService: JsonToExcelService,
    private _toaster: ToasterService,
    private route: ActivatedRoute,
    private _auth: LoginService,) {
    this.typeSelected = 'ball-fussion';
    this.profile = this._auth.getProfile().profile;
  }



  async onLegalEntityChanges(event) {
    console.log(event)
    this.selectedLegalEntity = event.value
    let entityNameSelected = this.legalEntityList.findIndex(item => item.entity_id == this.selectedLegalEntity)
    console.log("Currently Selected Entity : ",this.selectedLegalEntity," - ",this.legalEntityList[entityNameSelected].entity_name)
    console.log(entityNameSelected)
    this.selectedCurrency = this.legalEntityList[entityNameSelected]['entity_currency_code'];
    let udrfData = await this.initUdrf();
  }

  durationRanges: any;
  selectedLegalEntity: any;
  selectedCurrency: any;
  displayMonthYear: any;

  async getBillingPostingPeriod(){
    // let getBillingPostingDate  = await this._misService.getBillingPostingPeriod();
    let getMisPostingDate = await this._misService.getMisPostingPeriod();
    console.log(getMisPostingDate)
    if(getMisPostingDate.messType == "S"){
      let postingDate = getMisPostingDate['data'][0]['date'];
      const currentDate = moment().format("YYYY-MM-DD");
      const postingPeriodDate = moment(postingDate).format("YYYY-MM-DD");
      console.log(currentDate)
      console.log(postingPeriodDate)

      let postingMonth = moment(postingDate).format("YYYY-MM");
      let currentMonth = moment().format("YYYY-MM");
      this.displayMonthYear = moment(postingDate).format("MMMM YYYY");
      console.log(postingMonth);
      console.log(currentMonth);

    }
  }

  init = true;
  async ngOnInit() {
    await this.reportAuthorizationValidation();
    if(this.init == true){
      this.init = false;
      this.getBillingPostingPeriod();
    }

    // this._misService.$reloadPage.subscribe(async (item) => {
    //   console.log(item)
    //   let udrfData = await this.initUdrf();
    // })
    let daysElapsedRanges = [
      {
        checkboxexpenseId: 1,
        checkboxId: 'CDCRD',
        checkboxName: 'No Limit',
        checkboxStartValue: 0,
        checkboxEndValue: 0,
        isCheckboxDefaultSelected: true,
      },
      {
        checkboxexpenseId: 2,
        checkboxId: 'CDCRD2',
        checkboxName: ' < 10',
        checkboxStartValue: 0,
        checkboxEndValue: 9,
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxexpenseId: 3,
        checkboxId: 'CDCRD3',
        checkboxName: '10 - 20',
        checkboxStartValue: 10,
        checkboxEndValue: 20,
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxexpenseId: 4,
        checkboxId: 'CDCRD4',
        checkboxName: '20 - 30',
        checkboxStartValue: 20,
        checkboxEndValue: 30,
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxexpenseId: 5,
        checkboxId: 'CDCRD5',
        checkboxName: '> 30',
        checkboxStartValue: 30,
        checkboxEndValue: 10000,
        isCheckboxDefaultSelected: false,
      }
    ];

    this._udrfService.udrfFunctions.constructCustomRangeData(
      12,
      'number',
      daysElapsedRanges
    );

    this.durationRanges = [
      {
        checkboxId: 'CDCRD',
        checkboxName: 'Current Year',
        checkboxStartValue: moment().startOf('year').format("YYYY-MM-DD"),
        checkboxEndValue: moment().endOf('year').format("YYYY-MM-DD"),
        isCheckboxDefaultSelected: true,
      },
      {
        checkboxId: 'CDCRD4',
        checkboxName: 'Next Month',
        checkboxStartValue: this._utilityService.getFormattedDate(
          moment().add(1, 'month').startOf('month'),
          moment(moment().add(1, 'month').startOf('month')).date,
          15,
          0,
          0,
          0
        ),
        checkboxEndValue: this._utilityService.getFormattedDate(
          moment().add(1, 'month').endOf('month'),
          moment(moment().add(1, 'month').endOf('month')).date,
          15,
          0,
          0,
          0
        ),
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxId: 'CDCRD3',
        checkboxName: 'This Month',
        checkboxStartValue: this._utilityService.getFormattedDate(
          moment().startOf('month'),
          moment(moment().startOf('month')).date,
          15,
          0,
          0,
          0
        ),
        checkboxEndValue: this._utilityService.getFormattedDate(
          moment().endOf('month'),
          moment(moment().endOf('month')).date,
          15,
          0,
          0,
          0
        ),
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxId: 'CDCRD2',
        checkboxName: 'This Week',
        checkboxStartValue: this._utilityService.getFormattedDate(
          moment().startOf('week'),
          moment(moment().startOf('week')).date,
          15,
          0,
          0,
          0
        ),
        checkboxEndValue: this._utilityService.getFormattedDate(
          moment().endOf('week'),
          moment(moment().endOf('week')).date,
          15,
          0,
          0,
          0
        ),
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxId: 'CDCRD5',
        checkboxName: 'Upcoming 3 Months',
        checkboxStartValue: this._utilityService.getFormattedDate(
          moment().startOf('month'),
          moment(moment().startOf('month')).date,
          15,
          0,
          0,
          0
        ),
        checkboxEndValue: this._utilityService.getFormattedDate(
          moment().add(2, 'month').endOf('month'),
          moment(moment().add(2, 'month').endOf('month')).date,
          15,
          0,
          0,
          0
        ),
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxId: 'CDCRD6',
        checkboxName: 'Full',
        checkboxStartValue: moment("2020-01-01").format("YYYY-MM-DD"),
        checkboxEndValue: moment().format("YYYY-MM-DD"),
        isCheckboxDefaultSelected: false,
      }
    ]

    this._udrfService.udrfFunctions.constructCustomRangeData(
      13,
      'date',
      this.durationRanges
    );

    // this._udrfService.udrfFunctions.constructCustomRangeData(
    //   2,
    //   'date',
    //   durationRanges
    // );

    // this._misService.$searchRetain.subscribe(async (item) => {
    //   let udrfData = await this.initUdrf();
    //   this.searchParamAfterUpdate = item;
    // })


    let udrfData = await this.initUdrf();

    console.log("Rule Table Details", this.ruleTableDetails);
  }

  async resolveVisibleSummaryCards() {

    for (let summaryCardsItem of this._udrfService.udrfUiData.summaryCards) {

      let isVisible;

      if (this._udrfService.udrfUiData.selectedCard != null) {

        isVisible = _.contains(this._udrfService.udrfUiData.selectedCard, summaryCardsItem.dataTypeCode);
        summaryCardsItem.isVisible = isVisible;

      }

      if (this._udrfService.udrfData.udrfSummaryCardCodes != null) {

        isVisible = _.contains(this._udrfService.udrfData.udrfSummaryCardCodes, summaryCardsItem.dataTypeCode);
        summaryCardsItem.isVisible = isVisible;

      }

    }

  }

  categorisedDataTypeArray = [
    {
      categoryType: 'Status Cards',
      categoryCardCodes: [],
      categoryCards: [],
    },
  ];

  viewType = 0;

  revenueSummaryCardObject =  {
    dataType: 'Revenue',
    dataTypeCode: 'TCP',
    dataTypeValue: "0",
    originalValue: "0",
    dataTypeValueOriginal: 0,
    isActive: false,
    isVisible: true,
    isDisabled: false,
    cardType: 'status',
    isToolTip: true,
    summaryCardEllipsis: true
  };
  billingSummaryCardObject = {
    dataType: 'Billing',
    dataTypeCode: 'TCPE',
    dataTypeValue: "0",
    originalValue: "0",
    dataTypeValueOriginal: 0,
    isActive: false,
    isVisible: true,
    isDisabled: false,
    cardType: 'status',
    isToolTip: true,
    summaryCardEllipsis: true
  };
  ubrSummaryCardObject = {
    dataType: 'UBR',
    dataTypeCode: 'TADP',
    dataTypeValue: "0",
    originalValue: "0",
    dataTypeValueOriginal: 0,
    isActive: false,
    isVisible: true,
    isDisabled: false,
    cardType: 'status',
    isToolTip: true,
    summaryCardEllipsis: true
  };
  initExpenseCardSR() {
    let mainFilterArray = JSON.parse(JSON.stringify(this._udrfService.udrfData.mainFilterArray));

    let tempMainFilterArray = _.filter(this._udrfService.udrfData.filterTypeArray, { filterId: 5 });
    // tempMainFilterArray[0].multiOptionSelectSearchValues[0] = this._auth.getProfile().profile.aid;

    let filterConfig = {
      startIndex: this.ExpenseItemDataCurrentIndex,
      startDate: this._udrfService.udrfData.mainApiDateRangeStart,
      endDate: this._udrfService.udrfData.mainApiDateRangeEnd,
      mainFilterArray: mainFilterArray,
      txTableDetails: this._udrfService.udrfData.txTableDetails,
      mainSearchParameter: this._udrfService.udrfData.mainSearchParameter,
      searchTableDetails: this._udrfService.udrfData.searchTableDetails,
      viewType: this.viewType
    }

    // this._misService.getSummaryCard(filterConfig, this.conversionRateArr)
    this._misService.getSummaryCard(filterConfig)
      .pipe(takeUntil(this._onDestroy)).pipe(takeUntil(this.$onAppApiCalled))
      .subscribe(async res => {
        if (res['data']) {
          console.log(res['data'])
          let metaData = res['data'][0];
          this.defaultCurrency = metaData['currency'];
          console.log(metaData)
          this.dataTypeArray = [
            // {
            //   dataType: 'Total amount paid',
            //   dataTypeCode: 'TAP',
            //   dataTypeValue: metaData['total_amount_paid'],
            //   isActive: false,
            //   isVisible: true,
            //   isDisabled: false,
            // },
            // {
            //   dataType: 'Total amount pending',
            //   dataTypeCode: 'TAPE',
            //   dataTypeValue: metaData['amount_pending'],
            //   isActive: false,
            //   isVisible: true,
            //   isDisabled: false,
            // },
            {
              dataType: 'Revenue',
              dataTypeCode: 'TCP',
              dataTypeValue: metaData['revenue'] == null ? 0 : metaData['revenue'],
              originalValue: metaData['revenue'] == null ? "0" : metaData['revenue'],
              dataTypeValueOriginal: metaData['revenue'] == null ? 0 : metaData['revenue'],
              isActive: false,
              isVisible: true,
              isDisabled: false,
              cardType: 'status',
              isToolTip: true,
              summaryCardEllipsis: true
            },
            {
              dataType: 'Billing',
              dataTypeCode: 'TCPE',
              dataTypeValue: metaData['billing'] == null ? 0 : metaData['billing'],
              originalValue: metaData['billing'] == null ? "0" : metaData['billing'],
              dataTypeValueOriginal: metaData['billing'] == null ? 0 : metaData['billing'],
              isActive: false,
              isVisible: true,
              isDisabled: false,
              cardType: 'status',
              isToolTip: true,
              summaryCardEllipsis: true
            },
            {
              dataType: 'UBR',
              dataTypeCode: 'TADP',
              dataTypeValue: metaData['ubr'] == null ? 0 : metaData['ubr'],
              originalValue: metaData['ubr'] == null ? "0" : metaData['ubr'],
              dataTypeValueOriginal: metaData['ubr'] == null ? 0 : metaData['ubr'],
              isActive: false,
              isVisible: true,
              isDisabled: false,
              cardType: 'status',
              isToolTip: true,
              summaryCardEllipsis: true
            },
          ]
          console.log(this.dataTypeArray)
          this.revenueSummaryCardObject = this.dataTypeArray[0];
          this.billingSummaryCardObject = this.dataTypeArray[1];
          this.ubrSummaryCardObject = this.dataTypeArray[2];
          // this.initExpenseListSR();
          this._udrfService.udrfUiData.summaryCards = this.dataTypeArray;
          // console.log(this._udrfService.udrfUiData.summaryCards)
        }

        this.spinnerService.hide();


      },
        (err) => {
          this.errorService.userErrorAlert((err && err.code) ? err.code : (err && err.error) ? err.error.code : 'NIL', "Error in Report", (err && err.params) ? err.params : (err && err.error) ? err.error.params : {});
        });


  }

  getDisplayValueInInrFormat(displayValue) {

    return this._utilityService.getDisplayValueInInrFormat(displayValue);

  }

  initUdrf() {
    this.selectedItemId = 1;
    this._udrfService.getNotifyReleasesUDRF();
    this.misItemDataCurrentIndex = 0;
    this._udrfService.udrfBodyData = [];
    this._udrfService.udrfData.applicationId = this.applicationId;
    this._udrfService.udrfUiData.showNewReleasesButton = true;
    this._udrfService.udrfUiData.showItemDataCount = true
    this._udrfService.udrfUiData.itemDataType = ""
    this._udrfService.udrfUiData.totalItemDataCount = 0
    this._udrfService.udrfUiData.showSearchBar = true
    this._udrfService.udrfUiData.showActionButtons = false
    this._udrfService.udrfUiData.showUdrfModalButton = true
    this._udrfService.udrfUiData.showCreateNewComponentButton = false;
    this._udrfService.udrfUiData.showSettingsModalButton = false
    this._udrfService.udrfUiData.isReportDownloading = false
    // this._udrfService.udrfUiData.showReportDownloadButton = true;
    this._udrfService.udrfUiData.showColumnConfigButton = false
    this._udrfService.udrfUiData.itemcardSelected = this.itemCardClicked.bind(this)
    this._udrfService.udrfUiData.summaryCardsItem = {}
    this._udrfService.udrfUiData.showHierarchyData = {}
    this._udrfService.udrfUiData.inlineEditData = {}
    // this._udrfService.udrfUiData.downloadItemDataReport = () => { }
    this._udrfService.udrfUiData.openCommentsData = {}
    this._udrfService.udrfUiData.itemDataScrollDown = this.onCollectionItemDataScrollDown.bind(this)
    // this._udrfService.udrfUiData.downloadItemDataReport = this.downloadItemDataReport.bind(this);
    this._udrfService.udrfUiData.udrfBodyColumns = this.udrfBodyColumns
    this._udrfService.udrfUiData.udrfVisibleBodyColumns = this._udrfService.udrfUiData.udrfVisibleBodyColumns
    this._udrfService.udrfUiData.udrfInvisibleBodyColumns = this._udrfService.udrfUiData.udrfInvisibleBodyColumns
    this._udrfService.udrfUiData.variant = 0;
    this._udrfService.udrfUiData.itemHasQuickCta = false
    this._udrfService.udrfUiData.itemHasComments = false
    this._udrfService.udrfUiData.itemHasHierarchyView = false;
    this._udrfService.udrfUiData.itemHasInfoButton = false;
    this._udrfService.udrfUiData.itemHasMoreActions = false;
    this._udrfService.udrfUiData.showCollapseButton = false;
    this._udrfService.udrfUiData.horizontalScroll = true;
    this._udrfService.udrfUiData.showGroupByButton = false;
    this._udrfService.udrfUiData.isMultipleView = true;
    this._udrfService.udrfUiData.showReportDownloadButton = false;
    this._udrfService.udrfUiData.downloadItemDataReport = this.downloadReport.bind(this);
    this._udrfService.getAppUdrfConfig(this.applicationId, this.initReport.bind(this));
    // this._udrfService.udrfUiData.createNewComponent = this.addNewConfigurationRow.bind(this);
    this._udrfService.getNotifyReleasesUDRF();
    this._udrfService.udrfUiData.ghostButtonUI = false;



    // this._udrfService.udrfUiData.resolveVisibleSummaryCards = this.resolveVisibleSummaryCards.bind(this);
    // this._udrfService.udrfUiData.summaryCardsSelected = this.dataTypeCardSelected.bind(this);
    // this._udrfService.udrfUiData.summaryCardsItem = {};
    // this._udrfService.udrfUiData.showSettingsModalButton = false;
    this._udrfService.udrfUiData.summaryCards = this.dataTypeArray;
    // this._udrfService.udrfUiData.categorisedSummaryCards = this.categorisedDataTypeArray;
    this._udrfService.udrfUiData.columnClick = this.getExpenseItem.bind(this);
    // this._udrfService.udrfUiData.paymentHoverRow = true;
    // this._udrfService.udrfUiData.openApplyCreditsDialog = this.openACPopUp.bind(this);
    // this._udrfService.udrfUiData.openPaymentDialog = this.openRPPopUp.bind(this);


    return Promise.resolve();
  }

  async getExpenseItem(){
    console.log(this._udrfService.udrfUiData.currentColumnDetails)
    console.log(this._udrfService.udrfUiData.onColumnClickItem)
    let columnData = this._udrfService.udrfUiData.onColumnClickItem;
    if (this._udrfService.udrfUiData.currentColumnDetails['item'] == "profit_center") {
      let getUbrByCostCenter = await this._misService.getUbrByCostCenter(columnData['profit_center']);
      console.log(getUbrByCostCenter)

      
      if(getUbrByCostCenter.data.length>0){
        let UbrCostCenterDetailsDialog = this.dialog.open(UbrCostCenterDetailsDialogComponent,{
          width: "90%",
          height: "auto",
          disableClose: true,
          data: {
            costCenterDetails: getUbrByCostCenter.data,
            rowData: this._udrfService.udrfUiData.onColumnClickItem,
            defaultCurrency: this.defaultCurrency
          }
        })

        UbrCostCenterDetailsDialog.afterClosed().subscribe((res: any) => {
          if(res == "success"){
            this.ngOnInit();
          }
        });
      }
      else{
        this.snackBar.open("Revenue Details not found !","Dismiss",{duration:2000});
      }
    }
  }

  dataTypeArray = [
    // {
    //   dataType: 'Total amount paid',
    //   dataTypeCode: 'TAP',
    //   dataTypeValue: "0 INR",
    //   isActive: false,
    //   isVisible: true,
    //   isDisabled: false,
    // },
    // {
    //   dataType: 'Total amount pending',
    //   dataTypeCode: 'TAPE',
    //   dataTypeValue: "0 INR",
    //   isActive: false,
    //   isVisible: true,
    //   isDisabled: false,
    // },
    {
      dataType: 'Revenue',
      dataTypeCode: 'TCP',
      dataTypeValue: "0",
      originalValue: "0",
      dataTypeValueOriginal: 0,
      isActive: false,
      isVisible: true,
      isDisabled: false,
      cardType: 'status',
      isToolTip: true,
      summaryCardEllipsis: true
    },
    {
      dataType: 'Billing',
      dataTypeCode: 'TCPE',
      dataTypeValue: "0",
      originalValue: "0",
      dataTypeValueOriginal: 0,
      isActive: false,
      isVisible: true,
      isDisabled: false,
      cardType: 'status',
      isToolTip: true,
      summaryCardEllipsis: true
    },
    {
      dataType: 'UBR',
      dataTypeCode: 'TADP',
      dataTypeValue: "0",
      originalValue: "0",
      dataTypeValueOriginal: 0,
      isActive: false,
      isVisible: true,
      isDisabled: false,
      cardType: 'status',
      isToolTip: true,
      summaryCardEllipsis: true
    },
  ];

  cardFilter: any = "";
  isCardClicked: boolean = false;
  cardClicked = "";
  ExpenseItemDataCurrentIndex = 0;

  dataTypeCardSelected() {
    console.log(this._udrfService.udrfUiData.summaryCards)

    this._udrfService.udrfData.isItemDataLoading = true;
    this._udrfService.udrfData.noItemDataFound = false;

    let dataTypeArrayItem = this._udrfService.udrfUiData.summaryCardsItem;

    for (let i = 0; i < this.dataTypeArray.length; i++)
      if (dataTypeArrayItem["dataTypeCode"] == this.dataTypeArray[i].dataTypeCode) {

        this.dataTypeArray[i].isActive = true;
        dataTypeArrayItem["isActive"] = true;
        this._udrfService.udrfUiData.cardClicked = true;
        this.cardFilter = dataTypeArrayItem;

      }
      else {

        let summaryCard = _.where(this._udrfService.udrfUiData.summaryCards, { dataTypeCode: this.dataTypeArray[i].dataTypeCode });

        if (summaryCard.length > 0)
          summaryCard[0].isActive = false;

        this.dataTypeArray[i].isActive = false;

        this._udrfService.udrfUiData.cardClicked = false;
        this.cardFilter = "";

      }

    this.isCardClicked = true;

    this.cardClicked = dataTypeArrayItem["dataType"];

    this._udrfService.udrfBodyData = [];

    this.ExpenseItemDataCurrentIndex = 0;

    this.initReport();
      console.log(this._udrfService.udrfUiData.summaryCards)
  }

  expenseItem: any;
  // getExpenseItem() {
  //   console.log(this._udrfService.udrfUiData.currentColumnDetails)
  //   console.log(this._udrfService.udrfUiData.onColumnClickItem)
  //   if (this._udrfService.udrfUiData.currentColumnDetails['item'] == "id") {
  //     let oid = this.profile['oid'];
  //     let id = this._udrfService.udrfUiData.onColumnClickItem['id'];
  //     this._misService.getExpenseItem(oid, 0, 10000).pipe(takeUntil(this._onDestroy)).subscribe(async res => {
  //       if (res) {
  //         this.expenseItem = res;
  //         this.expenseItem = this.expenseItem.filter((item) => item.expense_header_id === id)
  //         this.saveExpenseDetailToSession();
  //       }
  //       else {
  //         let errReportingTeams = "KEBS";
  //         this._utilityService.showErrorMessage(errReportingTeams, res["messText"]);
  //       }

  //     },
  //       (err) => {
  //         let errReportingTeams = "KEBS";
  //         this._utilityService.showErrorMessage(errReportingTeams, err);
  //       });
  //   }
  // }

  saveExpenseDetailToSession = () => {
    sessionStorage.setItem("expenseDetail", JSON.stringify(this.expenseItem[0]));
    this.goToMainExpense();
  };

  goToMainExpense() {
    let role = this.expenseItem[0].role;
    let status = this.expenseItem[0].status;

    console.log("role")
    console.log(role)
    console.log(status)

    let expenseType = this.expenseItem[0].expense_type;
    let eHeaderId = this.expenseItem[0].expense_header_id;

    //Claim
    if (expenseType == 'C') {
      if (status == 'Draft' && role == "Claimer") {
        //this.openClaimForm();

      }
      else {
        if (role == "Claimer") {
          window.open("/main/expenses/expenseDetail/" +
            this.expenseItem[0].expense_header_id +
            "/claims", '_blank');
          return;
        }
        else if (role == "Approver") {
          window.open("/main/expenses/expenseDetail/" +
            this.expenseItem[0].expense_header_id +
            "/approvals", '_blank')
          return;
        } else if (role == "Treasurer") {
          window.open("/main/expenses/expenseDetail/" +
            this.expenseItem[0].expense_header_id +
            "/verifications/payment", '_blank');
          return;
        }
        else if (role == "Admin") {
          window.open("/main/expenses/expenseDetail/" +
            this.expenseItem[0].expense_header_id +
            "/verifications/payment", '_blank');
          return;
        }
      }
    }
    else if (expenseType == 'A') {
      window.open("/main/expenses/expenseDetail/advance/" + this.expenseItem[0].expense_header_id + "/adminView", '_blank');

    }

  }

  misFields: any[];
  ruleData: any;

  downloadReport() {
    // let mainFilterArray = JSON.parse(JSON.stringify(this._udrfService.udrfData.mainFilterArray));

    // let tempMainFilterArray = _.filter(this._udrfService.udrfData.filterTypeArray, { filterId: 5 });
    // tempMainFilterArray[0].multiOptionSelectSearchValues[0] = this.authService.getProfile().profile.aid;

    // if (_.filter(mainFilterArray, { filterId: 5 }).length == 0) {
    //   mainFilterArray.push(tempMainFilterArray[0]);
    // }


    // let filterConfig = {
    //   startDate: this._udrfService.udrfData.mainApiDateRangeStart,
    //   endDate: this._udrfService.udrfData.mainApiDateRangeEnd,
    //   mainFilterArray: mainFilterArray,
    //   txTableDetails: this._udrfService.udrfData.txTableDetails,
    //   mainSearchParameter: this._udrfService.udrfData.mainSearchParameter,
    //   searchTableDetails: this._udrfService.udrfData.searchTableDetails,
    //   viewType: this.viewType,
    //   cardFilter: this.cardFilter
    // }

    // this._misService.getUBRReportDownloadSR(filterConfig).pipe(takeUntil(this._onDestroy)).pipe(takeUntil(this._onAppApiCalled))
    //   .subscribe(async res => {
    //     this._udrfService.udrfUiData.isReportDownloading = false;
    //     if (res['data'] && res['messType'] == 'S') {
    //       this.excelService.exportAsExcelFile(
    //         res['data'],
    //         "Expense-Report"
    //       );
    //     }
    //     else {
    //       this._utilityService.showMessage('Report Download Failed', 'Dismiss', 3000);
    //     }

    //     this._udrfService.udrfData.isItemDataLoading = false;

    //   },
    //     (err) => {
    //       this._udrfService.udrfUiData.isReportDownloading = false;
    //       this.errorService.userErrorAlert((err && err.code) ? err.code : (err && err.error) ? err.error.code : 'NIL', "Error in Downloading Report", (err && err.params) ? err.params : (err && err.error) ? err.error.params : {});
    //     });

    this._udrfService.udrfUiData.isReportDownloading = true;
    let mainFilterArray = JSON.parse(JSON.stringify(this._udrfService.udrfData.mainFilterArray));

    let filterConfig;
    if (this.searchParamAfterUpdate != null) {


      filterConfig = {
        startIndex: this.misItemDataCurrentIndex,
        mainFilterArray: mainFilterArray,
        txTableDetails: this._udrfService.udrfData.txTableDetails,
        mainSearchParameter: this.searchParamAfterUpdate,
        searchTableDetails: this._udrfService.udrfData.searchTableDetails,
        legalEntityId: this.legalEntity.value
      };
    }
    else {

      filterConfig = {
        startIndex: this.misItemDataCurrentIndex,
        mainFilterArray: mainFilterArray,
        txTableDetails: this._udrfService.udrfData.txTableDetails,
        mainSearchParameter: this._udrfService.udrfData.mainSearchParameter,
        searchTableDetails: this._udrfService.udrfData.searchTableDetails,
        legalEntityId: this.legalEntity.value,
      };
    }

    // this._misService.
    // getUBRReportDownloadSR(filterConfig)
    // this._misService.
    // getUBRReportDownloadSR(filterConfig, this.conversionRateArr)
    this._misService.
    getUBRReportDownloadSR(filterConfig)
      .pipe(takeUntil(this.$onDestroy))
      .pipe(takeUntil(this.$onAppApiCalled)).subscribe(async res => {

        this._udrfService.udrfUiData.isReportDownloading = false;

        if (res["messType"] == "S") {
          let fileName = `UBR ${moment().format('DD-MM-YY,h:mm:ss a')}`
          res["data"] = await this._udrfService.returnVisibleBodyColumnsForDownload(res["data"]);

          //File Rename as per Front END
          let rename_fields = {
            project_name: "Portfolio Name",
            item_name: "Project Name",
            profit_center: "Project Code",
            revenue_amount: "Revenue",
            billing: "Billing",
            ubr: "UBR",
            currency_code: "Currency"
          }

          let renamed_data = []

           // Reorder the fields as on UI           
          let orderedKeys = Object.keys(rename_fields);

          // Filter keys in object2 based on the order from object1
          let reorder_data = []
          _.each(res["data"], (item) => {
            let result = {};
            orderedKeys.forEach(key => {
              if (key in item) {
                result[key] = item[key];
              }
            });

            reorder_data.push(result)
          })

          _.each(reorder_data,(item)=>{
              let keyName = Object.keys(item)
             		let temp = {}
              for (const [key, value] of Object.entries(item)) {
                const newKey = rename_fields[key] || key;
                (temp as any)[newKey] = value;
              }
              renamed_data.push(temp)
          })

          this._excelService.exportAsExcelFile(renamed_data, fileName);
          this._toaster.showSuccess("UBR Report Downloaded successfully!", '', 3000);
        }

        else {
          let errReportingTeams = "KEBS";
          this._utilityService.showErrorMessage(errReportingTeams, res["data"]);
        }

      }, err => {
        this._udrfService.udrfUiData.isReportDownloading = false;
        let errReportingTeams = "KEBS";
        this._utilityService.showErrorMessage(err, errReportingTeams);
      });


  }

  listenToParent() {
    for (let i = 0; i < this.misFields.length; i++) {
      if (this.misFields[i].parent_dependent != null && this.misFields[i].no_to_retrieve != null) {
        this.misFields[i].is_visible = 1;
        this.misFields[i].is_required = "true";
        let parentField = this.misFields[i].parent_dependent;
        let data = this.misFields.filter(element => { return element.field_name == parentField });

        for (let j = 0; j < data[0]['choices'].length; j++) {

          let fieldData = this.misFields.filter(element => { return element.field_name == data[0]['choices'][j] })
          if (fieldData.length != 0) {
            if (fieldData.length != 0 && fieldData[0].field_name != this.misFields[i].field_name) {
              fieldData[0].is_visible = 0;
              fieldData[0].is_required = false;
            }
          }
        }
      }
    }
  }
  isLoading = false;

  /**
   * Init UDRF report
   */
  async initReport() {

    this.$onAppApiCalled.next();
    // this.getUBRConversionRate();
    this.misItemDataCurrentIndex = 0;
    this._udrfService.udrfBodyData = [];
    this.selectedItemId = 1;
    this._udrfService.udrfUiData.resolveColumnConfig();
    console.log("1111111")
    this.getRuleDataList();
    console.log("aaaaaaaaa")
    console.log(this._udrfService.udrfUiData.summaryCards)
    this._udrfService.udrfUiData.checkedBodyItemIndex = [];
  }

  conversionRateArr: any;
  // async getUBRConversionRate(){
  //   let getUBRConversionRate = await this._misService.getUBRConversionRate();
  //   if(getUBRConversionRate["messType"] == "S"){
  //     this.conversionRateArr = getUBRConversionRate["data"];
  //   }
  // }


  /**
   * Get Total count
   */
  async getMisRuleCount() {
    let mainFilterArray = JSON.parse(JSON.stringify(this._udrfService.udrfData.mainFilterArray));
    let filterConfig = {
      startIndex: this.misItemDataCurrentIndex,
      mainFilterArray: mainFilterArray,
      txTableDetails: this._udrfService.udrfData.txTableDetails,
      mainSearchParameter: this._udrfService.udrfData.mainSearchParameter,
      searchTableDetails: this._udrfService.udrfData.searchTableDetails
    };

    // this._misService.getGlAccountDataCount(filterConfig)
    // .pipe(takeUntil(this.$onDestroy))
    // .subscribe(async (res : any) => {
    //   if(res.messType=='S')
    //     this._udrfService.udrfUiData.totalItemDataCount = res.data[0].record_count;
    // },(err) => {
    //   this.errorService.userErrorAlert((err && err.code) ? err.code : (err && err.error) ? err.error.code : 'NIL', "Error while retrieving MIS data", (err && err.params) ? err.params : (err && err.error) ? err.error.params : {});
    // })
  }


  configurationDetail: any;
  async getRuleDataList() {
    let expenseStartDate;
    let expenseEndDate;
    let daysElapsedStart;
    let daysElapsedEnd;
    let mainFilterArray = JSON.parse(JSON.stringify(this._udrfService.udrfData.mainFilterArray));
    console.log("mainFilterArray")
    console.log(mainFilterArray)
    if (mainFilterArray.length > 0) {
      for (let items of mainFilterArray) {
        if (items.filterId == 13 && !items.isCustomButtonActivated) {
          console.log("items.filterId")
          console.log(items.filterId)
          let dateValues = this.seperateMultipleDate(items.checkboxValues);
          console.log("items.checkboxValues")
          console.log(items.checkboxValues)
          console.log("dateValues")
          console.log(dateValues)
          expenseStartDate = moment(dateValues.startDate).format("YYYY-MM-DD");
          expenseEndDate = moment(dateValues.endDate).format("YYYY-MM-DD");
        }
        else if (items.filterId == 12 && !items.isCustomButtonActivated && items.checkboxValues.checkboxexpenseId != 1) {
          console.log("checkboxValues")
          console.log(items.checkboxValues)
          let daysElapsedValues = this.seperateDaysElapsedMultiple(items.checkboxValues);
          console.log("daysElapsedValues")
          console.log(daysElapsedValues)
          daysElapsedStart = daysElapsedValues.startValue;
          daysElapsedEnd = daysElapsedValues.endValue;
        }
      }
    }
    if (this.searchParamAfterUpdate != null) {


      this.filterConfig = {
        startIndex: this.misItemDataCurrentIndex,
        mainFilterArray: mainFilterArray,
        txTableDetails: this._udrfService.udrfData.txTableDetails,
        mainSearchParameter: this.searchParamAfterUpdate,
        searchTableDetails: this._udrfService.udrfData.searchTableDetails,
        legalEntityId: this.legalEntity.value,
        expenseDurationStartDate: expenseStartDate,
        expenseDurationEndDate: expenseEndDate,
        daysElapsedStart: daysElapsedStart,
        daysElapsedEnd: daysElapsedEnd
      };
    }
    else {

      this.filterConfig = {
        startIndex: this.misItemDataCurrentIndex,
        mainFilterArray: mainFilterArray,
        txTableDetails: this._udrfService.udrfData.txTableDetails,
        mainSearchParameter: this._udrfService.udrfData.mainSearchParameter,
        searchTableDetails: this._udrfService.udrfData.searchTableDetails,
        legalEntityId: this.legalEntity.value,
        expenseDurationStartDate: expenseStartDate,
        expenseDurationEndDate: expenseEndDate,
        daysElapsedStart: daysElapsedStart,
        daysElapsedEnd: daysElapsedEnd
      };
    }

    console.log("expenseStartDate")
    console.log(expenseStartDate)
    console.log("expenseEndDate")
    console.log(expenseEndDate)



    if (this.$misRuleDataSubscription)
      this.$misRuleDataSubscription.unsubscribe();

    this.$misRuleDataSubscription = this._misService
      .getUBRDetails(this.filterConfig)
      .pipe(takeUntil(this.$onDestroy))
      .pipe(takeUntil(this.$onAppApiCalled))
      .subscribe(
        async (res) => {
          this.configurationDetail = res['data'];
          for (let i = 0; i < this.configurationDetail.length; i++) {
            if (this.configurationDetail[i].salary_column_names != null) {
              let salaryColumn = JSON.parse(this.configurationDetail[i].salary_column_names);
              this.configurationDetail[i].salary_column_names = salaryColumn;
              this.configurationDetail[i].salary_column_names_value = salaryColumn.toString();
            }
          }

          if (
            res['messType'] == 'S' &&
            res['data'] &&
            res['data'].length > 0
          ) {
            this._udrfService.udrfBodyData = this._udrfService.udrfBodyData.concat(res['data'])
            // this.getMisRuleCount()
            this._udrfService.udrfUiData.totalItemDataCount = res['data'].length
            
          this._udrfService.udrfData.isItemDataLoading = false;
          } else {
            
          this._udrfService.udrfData.isItemDataLoading = false;
            this._udrfService.udrfBodyData = this._udrfService.udrfBodyData.concat([]);
            this._udrfService.udrfUiData.totalItemDataCount = 0
            this._udrfService.udrfData.noItemDataFound = true;
          }

            this.initExpenseCardSR();



        }, (err) => {
          this.errorService.userErrorAlert((err && err.code) ? err.code : (err && err.error) ? err.error.code : 'NIL', "Error while retrieving Invoice data", (err && err.params) ? err.params : (err && err.error) ? err.error.params : {});
        });

    this.searchParamAfterUpdate = null;
  }

  seperateMultipleDate(DateArray) {
    let dateData;

    for (let dateTypeArrayItem of DateArray) {
      if (dateTypeArrayItem.isCheckboxSelected) {
        dateData =
        {
          startDate: dateTypeArrayItem.checkboxStartValue,
          endDate: dateTypeArrayItem.checkboxEndValue,
        }
      }
    }
    return dateData;
  }

  seperateDaysElapsedMultiple(DateArray) {
    let dateData;

    for (let dateTypeArrayItem of DateArray) {
      if (dateTypeArrayItem.isCheckboxSelected) {
        dateData =
        {
          startValue: dateTypeArrayItem.checkboxStartValue,
          endValue: dateTypeArrayItem.checkboxEndValue,
        }
      }
    }
    return dateData;
  }

  /**
   * UDRF report download
   */
  //  downloadItemDataReport() {

  //   this._udrfService.udrfUiData.isReportDownloading = true;
  //   let mainFilterArray = JSON.parse(JSON.stringify(this._udrfService.udrfData.mainFilterArray));
  //   let filterConfig = {
  //     startIndex: "D",
  //     startDate: this._udrfService.udrfData.mainApiDateRangeStart,
  //     endDate: this._udrfService.udrfData.mainApiDateRangeEnd,
  //     mainFilterArray: mainFilterArray,
  //     txTableDetails: this._udrfService.udrfData.txTableDetails,
  //     mainSearchParameter: this._udrfService.udrfData.mainSearchParameter,
  //     searchTableDetails: this._udrfService.udrfData.searchTableDetails,
  //     isItemLevelApi: false
  //   };

  //   this._misService.
  //     getGlAccountData(filterConfig)
  //     .pipe(takeUntil(this.$onDestroy))
  //     .pipe(takeUntil(this.$onAppApiCalled)).subscribe(async res => {

  //       this._udrfService.udrfUiData.isReportDownloading = false;

  //       if (res["messType"] == "S") {
  //         let fileName = `GL Account ${moment().format('DD-MM-YY,h:mm:ss a')}`
  //         this._excelService.exportAsExcelFile(res["data"], fileName);
  //         this._utilityService.showToastMessage("GL Account list Downloaded successfully!");
  //       }

  //       else {
  //         let errReportingTeams = "KEBS";
  //         this._utilityService.showErrorMessage(errReportingTeams, res["data"]);
  //       }

  //     }, err => {
  //       this._udrfService.udrfUiData.isReportDownloading = false;
  //       let errReportingTeams = "KEBS";
  //       this._utilityService.showErrorMessage(err, errReportingTeams);
  //     });

  // }

  /**
   * API call while scrolling UDRF item data
   */
  async onCollectionItemDataScrollDown() {
    // let startCount = 0;
    // if (!this._udrfService.udrfData.noItemDataFound) {
    //   if (!this._udrfService.udrfData.isItemDataLoading) {
    //     this.misItemDataCurrentIndex = startCount;
    //     this._udrfService.udrfData.isItemDataLoading = true;

    //     if (this._udrfService.udrfData.appliedConfig["activeView"].length > 0) {
    //       if (this._udrfService.udrfData.appliedConfig["activeView"][0].groupByView) {
    //         this._udrfService.groupBy = true;
    //       }
    //     }
    //     else {
    //       this.getRuleDataList();
    //     }
    //   }
    // }
    if (!this._udrfService.udrfData.noItemDataFound) {
      if (!this._udrfService.udrfData.isItemDataLoading) {
        this.misItemDataCurrentIndex +=   this._udrfService.udrfData.defaultRecordsPerFetch;
        this._udrfService.udrfData.isItemDataLoading = true;
        await this.getRuleDataList()
      }
    }
  }

  /**
   * UDRF item click - redirect to BilledInvoiceDetailLandingPage
   */
  async itemCardClicked() {
    this.ruleTableDetails = this._udrfService.udrfUiData.itemCardSelecteditem;
    console.log("Rule Table Details", this.ruleTableDetails);

  }


  /**
   * 
   * @param str 
   * @returns String
   * Encode URI
   */
  encodeURIComponent(str) {
    return encodeURIComponent(str).replace(/[!'()*]/g, function (c) {
      return "%" + c.charCodeAt(0).toString(16);
    });
  }

  ngOnDestroy() {
    this.$onDestroy.next();
    this.$onDestroy.complete();
    this._udrfService.resetUdrfData();

    if (this.$misRuleDataSubscription)
      this.$misRuleDataSubscription.unsubscribe();

    if (this.$misRuleDataTotalSubscription)
      this.$misRuleDataTotalSubscription.unsubscribe();
    // this._misService.$determineRuleCode.unsubscribe();
    // this._misService.misSearchData.unsubscribe();
    // this._misService.searchParamReload.unsubscribe();
  }

  async openUdrfFilterModal() {
    const { UdrfModalComponent } = await import('src/app/modules/shared-lazy-loaded-components/udrf-modal/udrf-modal.component');

    this.dialog.open(UdrfModalComponent, {
      minWidth: "100%",
      height: "84%",
      position: { top: '0px', left: '77px' },
      disableClose: true
    });
  }

  async viewColumnConfig() {

    const { UdrfColumnConfigComponent } = await import('src/app/app-shared/app-shared-components/udrf-column-config/udrf-column-config.component');

    this.dialog.open(UdrfColumnConfigComponent, {
      height: '75%',
      minWidth: '30%',
      position: { top: '0px' },
      disableClose: false
    });

  }

  itemDataCurrentIndex: number;
  $p2pMilestoneReportSubscription: Subscription;

  $p2pMilestoneReportTotalSubscription: Subscription;
  getSideNavData() {
    this.isSideNavLoading = true;
    let mainFilterArray = JSON.parse(
      JSON.stringify(this._udrfService.udrfData.mainFilterArray)
    );

    // let activeSummaryCard = _.findWhere(this.dataTypeArray, { isActive: true });

    let filterConfig = {
      startIndex: this.misItemDataCurrentIndex,
      startDate: this._udrfService.udrfData.mainApiDateRangeStart,
      endDate: this._udrfService.udrfData.mainApiDateRangeEnd,
      mainFilterArray: mainFilterArray,
      txTableDetails: this._udrfService.udrfData.txTableDetails,
      mainSearchParameter: this._udrfService.udrfData.mainSearchParameter,
      searchTableDetails: this._udrfService.udrfData.searchTableDetails,
      // activeSummaryCard: activeSummaryCard,
    };

    if (this.$p2pMilestoneReportTotalSubscription)
      this.$p2pMilestoneReportTotalSubscription.unsubscribe();
  }

  logSelectedValue(): void {
    console.log('Selected value:', this.selectedToggle);
  }

  selectedIcon: string = 'wallet';

  selectIcon(icon: string): void {
    this.selectedIcon = icon;
    console.log('Selected Icon:', this.selectedIcon);

    if (this.selectedIcon === 'wallet') {
      this.router.navigate(['/main/payment-app/expense-bills']);
    } else if (this.selectedIcon === 'cart') {
      this.router.navigate(['/main/payment-app/p2p-bills']);
    }
  }


  openRPPopUp() {
    this.ruleTableDetails = this._udrfService.udrfUiData.itemCardSelecteditem;
    console.log("Rule Table Details RP", this.ruleTableDetails);

    // this.dialog.open(RecordPaymentComponent, {
    //   height: 'auto',
    //   width: '60%',
    //   data: { ruleTableDetails: this.ruleTableDetails }
    // });
  }

  openACPopUp() {
    this.ruleTableDetails = this._udrfService.udrfUiData.itemCardSelecteditem;
    if(this.ruleTableDetails.expense_type_code == 'A'){
      return this.snackBar.open("Credits not applicable for Advance !","Dismiss",{duration:2000});
    }
    console.log("Rule Table Details AC", this.ruleTableDetails);
    // this.dialog.open(ApplyCreditsComponent, {
    //   height: 'auto',
    //   width: '70%',
    //   data: { ruleTableDetails: this.ruleTableDetails, legalEntityId: this.legalEntity.value }
    // });
  }
  areCheckboxesChecked(): boolean {
    return this._udrfService.udrfUiData.checkedBodyItemIndex.length > 0;
  }

  stackPayment() {
    if (!this.areCheckboxesChecked()) {
      this._toaster.showWarning('Please select at least one item.', '');
      return;
    }

    let bulkData = [];
    for (let index of this._udrfService.udrfUiData.checkedBodyItemIndex) {
      bulkData.push(this._udrfService.udrfBodyData[index]);
    }
    console.log("Final Bulk", bulkData);

    let creditLedger = _.pluck(bulkData,"credit_ledger");
    console.log(creditLedger);
    console.log(_.uniq(creditLedger));

    

    // let getExpenseType = _.pluck(bulkData,"expense_type_code");
    let getExpenseType = _.filter(bulkData,{expense_type_code: "A"})
    console.log(getExpenseType)
    // let checkFOrUnique = _.uniq(getExpenseType)>1;
    // console.log(_.uniq(getExpenseType))
    // console.log(checkFOrUnique)
    if(getExpenseType.length > 0){
      return this.snackBar.open("Bulk Payment is not applicable for Advance !","Dismiss",{duration:2000})
    }

    let groupByEmployee = _.groupBy(bulkData, "employee_aid");
    console.log(groupByEmployee)
    let snackBarText = [];
    for(let item in groupByEmployee){
      let creditLedger = _.pluck(groupByEmployee[item],"credit_ledger");
      if(_.uniq(creditLedger).length != 1){
        let bill_id = _.pluck(groupByEmployee[item], "bill_id");
        snackBarText = snackBarText.concat(bill_id);
      }

    }

    if(snackBarText.length>0){
      let bills = snackBarText.toString();
      return this.snackBar.open(`Bills - ${bills} associated with different credit ledgers, have been selected !`,"Dismiss",{duration:2000})
    }

    // let bulkPaymentPopup = this.dialog.open(BulkPaymentComponent, {
    //   height: '80%',
    //   width: '70%',
    //   data: { ruleTableDetails: bulkData, legalEntity: this.legalEntity.value }
    // });

    // bulkPaymentPopup.afterClosed().subscribe((res: any) => {
    //   this._udrfService.udrfUiData.checkedBodyItemIndex = [];
    // });
  }

  toggleSearch() {
    this.isExpanded = !this.isExpanded;
    if (!this.isExpanded) {
      this.searchParameter = '';
      if(this._udrfService.udrfData.mainSearchParameter.length>0)
         this._udrfService.udrfFunctions.stopSearchingOpportunities()
    }
  }

  async reportAuthorizationValidation(){
    let roleAccessFilters = await this._misService.reportAuthorizationValidation();
     this.isAuthorized = true;
     if (roleAccessFilters['messType'] != "S"){
       this.isAuthorized = false;
       this.router.navigateByUrl("/main/reports")
       return this.snackBar.open(roleAccessFilters['messText'], 'Dismiss', { duration: 2000 })
     }
   }

}
