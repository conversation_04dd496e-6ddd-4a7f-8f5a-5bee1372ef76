import { Component, OnInit, Inject } from '@angular/core';
import * as _ from 'underscore';
import * as moment from 'moment';
import { MatSnackBar } from '@angular/material/snack-bar';

import { WinLossReportService } from '../../services/win-loss-report.service'
import { OpportunityService } from 'src/app/modules/opportunities/features/opportunities-detail/services/OpportunityService';
import { UdrfService } from 'src/app/services/udrf/udrf.service';
import { takeUntil } from 'rxjs/operators';
import { GraphApiService } from '../../../../../../services/graph/graph-api.service';
import { UtilityService } from 'src/app/services/utility/utility.service';
import { JsonToExcelService } from 'src/app/services/excel/json-to-excel.service';
import { Subject, Subscription } from 'rxjs';
import { UdrfActivityService } from 'src/app/modules/account-sales/features/display-activities/services/udrf-activity.service'
import {
  MatDialog,
  MatDialogModule,
  MatDialogRef,
  MAT_DIALOG_DATA,
} from '@angular/material/dialog';

@Component({
  selector: 'app-win-loss-report-landing-page',
  templateUrl: './win-loss-report-landing-page.component.html',
  styleUrls: ['./win-loss-report-landing-page.component.scss']
})
export class WinLossReportLandingPageComponent implements OnInit {

  applicationId = 216;

  selectedCard = [];
  isCardClicked: boolean = false;
  cardClicked = "";
  WLItemDataCurrentIndex = 0;
  requestDataSubscription: Subscription;
  accountItemDataIndex = 0;

  protected _onAppApiCalled = new Subject<void>();
  protected _onDestroy = new Subject<void>();

  dataTypeArray = [

  ];
  udrfBodyColumns = []
  // udrfBodyColumns = [
  //   {
  //     item: 'id',
  //     header: 'ID',
  //     isActive: true,
  //     isVisible: 'true',
  //     position: 1,
  //     colSize: 1,
  //     sortOrder: 'I',
  //     width: 75,
  //     filterId: 28,
  //     type: 'text1',
  //     textClass: 'value13light',


  //   },
  //   {
  //     item: 'customer_name',
  //     header: 'Account Name',
  //     isActive: true,
  //     isVisible: 'true',
  //     position: 2,
  //     colSize: 2,
  //     sortOrder: 'I',
  //     width: 260,
  //     filterId: 30,
  //     type: 'text1',
  //     textClass: 'colorRed value13Bold',
  //   },
  //   {
  //     item: 'opportunity_value',
  //     header: 'Value',
  //     isActive: true,
  //     isVisible: 'false',
  //     type: 'currency',
  //     position: 3,
  //     colSize: 1,
  //     sortOrder: 'I',
  //     width: 140,
  //     filterId: 31,
  //     textClass: "text-center",

  //   },
  //   {
  //     item: 'description',
  //     header: 'Description',
  //     isActive: true,
  //     isVisible: 'true',
  //     type: 'text1',
  //     position: 5,
  //     colSize: 2,
  //     sortOrder: 'I',
  //     width: 360,
  //     filterId: 33,
  //     textClass: 'value13Bold',
  //   },
  //   {
  //     item: 'owner_name',
  //     isActive: true,
  //     header: 'Owner',
  //     isVisible: 'false',
  //     type: 'text1',
  //     position: 12,
  //     colSize: 2,
  //     sortOrder: 'I',
  //     width: 180,
  //     filterId: 34,
  //     textClass: 'value13light',
  //   },
  //   {
  //     item: 'p_name',
  //     isActive: true,
  //     header: 'Presales Owner',
  //     isVisible: 'false',
  //     type: 'text1',
  //     position: 13,
  //     colSize: 2,
  //     sortOrder: 'I',
  //     width: 180,
  //     filterId: 35,
  //     textClass: 'value13light',
  //     // isInlineEdit: true,
  //     // inlineEditVarient: ['Activity Owner', 'search-dropdown']
  //   },
  //   {
  //     item: 'm_name',
  //     isActive: true,
  //     header: 'Marketing Owner',
  //     isVisible: 'false',
  //     type: 'text1',
  //     position: 14,
  //     colSize: 2,
  //     sortOrder: 'I',
  //     width: 180,
  //     filterId: 36,
  //     textClass: 'value13light',
  //     // isInlineEdit: true,
  //     // inlineEditVarient: ['Activity Owner', 'search-dropdown']
  //   },
  //   {
  //     item: 'start_date',
  //     header: "S.Date",
  //     isActive: true,
  //     isVisible: 'false',
  //     type: 'date',
  //     position: 15,
  //     colSize: 1,
  //     sortOrder: 'I',
  //     width: 130,
  //     filterId: 37,
  //     textClass: 'value13light',
  //   },
  //   {
  //     item: 'end_date',
  //     header: 'E.Date',
  //     isActive: true,
  //     isVisible: 'false',
  //     type: 'date',
  //     position: 16,
  //     colSize: 1,
  //     sortOrder: 'I',
  //     width: 130,
  //     filterId: 38,
  //     textClass: 'value13light',
  //   },
  //   {
  //     item: 'proposal_submission_date',
  //     header: 'PS.Date',
  //     isActive: true,
  //     isVisible: 'false',
  //     type: 'date',
  //     position: 17,
  //     colSize: 1,
  //     sortOrder: 'I',
  //     width: 130,
  //     filterId: 39,
  //     textClass: 'value13light',
  //   },
  //   {
  //     item: 'kt_gm_perc',
  //     header: 'KT GM%',
  //     isActive: true,
  //     isVisible: 'false',
  //     type: 'text',
  //     position: 6,
  //     colSize: 2,
  //     sortOrder: 'I',
  //     width: 180,
  //     textClass: 'value13Bold',

  //   },
  //   {
  //     item: 'kt_cost_price',
  //     header: 'KT Price',
  //     isActive: true,
  //     isVisible: 'false',
  //     type: 'currency',
  //     position: 3,
  //     colSize: 1,
  //     sortOrder: "N",
  //     width: 180

  //   },

  //   {
  //     item: 'status_name',
  //     header: 'Status',
  //     isActive: true,
  //     isVisible: 'true',
  //     type: 'status',
  //     position: 6,
  //     colSize: 2,
  //     sortOrder: 'I',
  //     width: 300,
  //     filterId: 40,
  //     textClass: 'value13Bold',
  //   },
  //   {
  //     item: 'bid_perc',
  //     header: 'Bid Score',
  //     isActive: true,
  //     isVisible: 'true',
  //     type: 'text',
  //     position: 6,
  //     colSize: 2,
  //     sortOrder: 'I',
  //     width: 180,
  //     textClass: 'value13Bold',

  //   },
  //   {
  //     item: 'presales_internal_score',
  //     header: 'Feedback Score',
  //     isActive: true,
  //     isVisible: 'true',
  //     type: 'text',
  //     position: 6,
  //     colSize: 2,
  //     sortOrder: 'I',
  //     width: 180,
  //     textClass: 'value13Bold',

  //   },
  //   {
  //     "colSize": 2,
  //     "header": "Win/Loss",
  //     "isActive": true,
  //     "isVisible": "true",
  //     "item": "win_or_loss",
  //     "position": 7,
  //     "sortOrder": "N",
  //     "type": "text1",
  //     "width": 180,
  //     textClass: 'value13Bold',

  //   },
  //   {
  //     "colSize": 2,
  //     "header": "Theme",
  //     "isActive": true,
  //     "isVisible": "true",
  //     "item": "win_theme",
  //     "position": 8,
  //     "sortOrder": "N",
  //     "type": "text1",
  //     "width": 180,
  //     textClass: 'value13Bold',
  //   },
  //   {
  //     "colSize": 2,
  //     "header": "Primary Reason Win",
  //     "isActive": true,
  //     "isVisible": "true",
  //     "item": "primary_win",
  //     "position": 9,
  //     "sortOrder": "N",
  //     "type": "text",
  //     "width": 180
  //   },
  //   {
  //     "colSize": 2,
  //     "header": "Secondary Reason Win",
  //     "isActive": true,
  //     "isVisible": "true",
  //     "item": "secondary_win",
  //     "position": 10,
  //     "sortOrder": "N",
  //     "type": "text1",
  //     "width": 180,
  //     textClass: 'value13light',
  //   },
  //   {
  //     "colSize": 2,
  //     "header": "Outperform",
  //     "isActive": true,
  //     "isVisible": "true",
  //     "item": "outperform_reason",
  //     "position": 11,
  //     "sortOrder": "N",
  //     "type": "text1",
  //     "width": 180,
  //     textClass: 'value13light',
  //   },
  //   {
  //     "colSize": 2,
  //     "header": "Technical Status",
  //     "isActive": true,
  //     "isVisible": "true",
  //     "item": "technical_status",
  //     "position": 20,
  //     "sortOrder": "N",
  //     "type": "text1",
  //     "width": 180,
  //     textClass: 'value13light',
  //   },
  //   {
  //     "colSize": 2,
  //     "header": "Competitor 1",
  //     "isActive": true,
  //     "isVisible": "true",
  //     "item": "competitor_1",
  //     "position": 11,
  //     "sortOrder": "N",
  //     "type": "text1",
  //     "width": 180,
  //     textClass: 'value13light',
  //   },
  //   {
  //     "colSize": 1,
  //     "header": "Competitor 1 Price",
  //     "isActive": true,
  //     "isVisible": "true",
  //     "item": "competitor_1_usd",
  //     "position": 12,
  //     "sortOrder": "N",
  //     "type": "currency",
  //     "width": 180
  //   },
  //   {
  //     "colSize": 2,
  //     "header": "Competitor 2",
  //     "isActive": true,
  //     "isVisible": "true",
  //     "item": "competitor_2",
  //     "position": 13,
  //     "sortOrder": "N",
  //     "type": "text1",
  //     "width": 180,
  //     textClass: 'value13light',
  //   },
  //   {
  //     "colSize": 1,
  //     "header": "Competitor 2 Price",
  //     "isActive": true,
  //     "isVisible": "false",
  //     "item": "competitor_2_usd",
  //     "position": 14,
  //     "sortOrder": "N",
  //     "type": "currency",
  //     "width": 180
  //   },
  //   {
  //     "colSize": 2,
  //     "header": "Competitor 3",
  //     "isActive": true,
  //     "isVisible": "true",
  //     "item": "competitor_3",
  //     "position": 15,
  //     "sortOrder": "N",
  //     "type": "text1",
  //     "width": 180,
  //     textClass: 'value13light',
  //   },
  //   {
  //     "colSize": 1,
  //     "header": "Competitor 3 Price",
  //     "isActive": true,
  //     "isVisible": "false",
  //     "item": "competitor_3_usd",
  //     "position": 16,
  //     "sortOrder": "N",
  //     "type": "currency",
  //     "width": 180
  //   },
  //   {
  //     "colSize": 2,
  //     "header": "Competitor win Reason",
  //     "isActive": true,
  //     "isVisible": "true",
  //     "item": "competitor_win_reason",
  //     "position": 17,
  //     "sortOrder": "N",
  //     "type": "text1",
  //     "width": 180,
  //     textClass: 'value13light',
  //   },
  //   {
  //     "colSize": 2,
  //     "header": "Primary Loss Reason",
  //     "isActive": true,
  //     "isVisible": "true",
  //     "item": "primary_loss",
  //     "position": 18,
  //     "sortOrder": "N",
  //     "type": "text1",
  //     "width": 180,
  //     textClass: 'value13light',
  //   },
  //   {
  //     "colSize": 2,
  //     "header": "Secondary Loss Reason",
  //     "isActive": true,
  //     "isVisible": "true",
  //     "item": "secondary_loss",
  //     "position": 19,
  //     "sortOrder": "N",
  //     "type": "text1",
  //     "width": 180,
  //     textClass: 'value13light',
  //   },
  //   {
  //     "colSize": 2,
  //     "header": "Area of Improvement",
  //     "isActive": true,
  //     "isVisible": "true",
  //     "item": "area_improvement",
  //     "position": 18,
  //     "sortOrder": "N",
  //     "type": "text1",
  //     "width": 180,
  //     textClass: 'value13light',
  //   },
  //   {
  //     item: 'action',
  //     isActive: true,
  //     header: 'Actions',
  //     isVisible: 'false',
  //     type: 'action',
  //     position: 19,
  //     colSize: 1,
  //     sortOrder: 'N',
  //     width: 140,
  //     textClass: "text-center"

  //   },
  //   {
  //     "colSize": 1,
  //     "header": "Division",
  //     "isActive": true,
  //     "isVisible": 'false',
  //     "item": "division",
  //     "position": 20,
  //     "sortOrder": "N",
  //     "type": "text",
  //     "width": 120
  //   },
  //   {
  //     "colSize": 1,
  //     "header": "Division",
  //     "isActive": true,
  //     "isVisible": 'false',
  //     "item": "division",
  //     "position": 21,
  //     "sortOrder": "N",
  //     "type": "text",
  //     "width": 120
  //   },
  //   {
  //     "colSize": 2,
  //     "header": "ABA/BK Man Months",
  //     "isActive": true,
  //     "isVisible": 'false',
  //     "item": "aba_man_months",
  //     "position": 22,
  //     "sortOrder": "N",
  //     "type": "text",
  //     "width": 180
  //   },
  //   {
  //     "colSize": 2,
  //     "header": "ABA per man month price",
  //     "isActive": true,
  //     "isVisible": 'false',
  //     "item": "aba_man_month_price",
  //     "position": 23,
  //     "sortOrder": "N",
  //     "type": "currency",
  //     "width": 180
  //   },
  //   {
  //     "colSize": 2,
  //     "header": "Competitor 1 Man month",
  //     "isActive": true,
  //     "isVisible": 'false',
  //     "item": "competitor_1_month",
  //     "position": 24,
  //     "sortOrder": "N",
  //     "type": "text",
  //     "width": 180
  //   },
  //   {
  //     "colSize": 2,
  //     "header": "Competitor 2 Man month",
  //     "isActive": true,
  //     "isVisible": 'false',
  //     "item": "competitor_2_month",
  //     "position": 25,
  //     "sortOrder": "N",
  //     "type": "text",
  //     "width": 180
  //   },
  //   {
  //     "colSize": 2,
  //     "header": "Competitor 3 Man month",
  //     "isActive": true,
  //     "isVisible": 'false',
  //     "item": "competitor_3_month",
  //     "position": 26,
  //     "sortOrder": "N",
  //     "type": "text",
  //     "width": 180
  //   },
  //   {
  //     "colSize": 2,
  //     "header": "Implementation Duration",
  //     "isActive": true,
  //     "isVisible": 'false',
  //     "item": "implementation_duration",
  //     "position": 26,
  //     "sortOrder": "N",
  //     "type": "text",
  //     "width": 180
  //   }

  // ];

  categorisedDataTypeArray = [
    {
      categoryType: 'Status Cards',
      categoryCardCodes: [],
      categoryCards: [],
    },
  ];

  udrfItemStatusColor: any[] = [
    {
      status: "Open",
      color: "#e2e2e2"
    },
    {
      status: "In Progress",
      color: "#ffa502"
    },
    {
      status: "Completed",
      color: "#009432"
    }
  ];

  minNoOfVisibleSummaryCards = 3;

  maxNoOfVisibleSummaryCards = 6;
  dateFormat: string = 'DD - MMM - YYYY';

  constructor(private winLossService: WinLossReportService,
    private snackBar: MatSnackBar,
    public udrfService: UdrfService,
    private exceltoJson: JsonToExcelService,
    private utilityService: UtilityService,
    private graphService: GraphApiService,
    private opportunityService: OpportunityService,
    public dialog: MatDialog,
    public udrfServices: UdrfActivityService,
  ) {

  }

  async ngOnInit() {
    await this.winLossService.getWinLossFieldNames(this.udrfBodyColumns).then(async(res: any)=>{
    this.udrfBodyColumns=res
     console.log(this.udrfBodyColumns)
    },(err)=>{
      this.showErrorMessage(err)
    })

    this.winLossService.getOpportunityStatusSalesGov().then(res => {
      let categoryCardCodes = []
      _.each(res, (sec) => {

        let valh = sec.udrf_summary_card
        if (valh != null) {
          let val = JSON.parse(sec.udrf_summary_card)
          categoryCardCodes.push(val.dataTypeCode)
          this.dataTypeArray.push(val)
          this.udrfItemStatusColor.push({
            color: val.color,
            status: val.dataType
          })
        }
      })
      this.categorisedDataTypeArray.push({
        categoryType: "Status Cards",
        categoryCardCodes: categoryCardCodes,
        categoryCards: []
      })


    })

      let durationRangesCreatedOn = [
          {
            checkboxId: 'OPPCON',
            checkboxName: 'Current Year',
            checkboxStartValue: this.utilityService.getFormattedDate(moment().startOf('year'),moment().startOf('year').date,0,0,0,0),
            checkboxEndValue: this.utilityService.getFormattedDate(moment().endOf('year'),moment().endOf('year').date,0,0,0,0),
            isCheckboxDefaultSelected: false,
          },
          {
            checkboxId: 'OPPCON1',
            checkboxName: 'This Week',
            checkboxStartValue: this.utilityService.getFormattedDate(
              moment().startOf('week'),
              moment(moment().startOf('week')).date,
              0,
              0,
              0,
              0
            ),
            checkboxEndValue: this.utilityService.getFormattedDate(
              moment().endOf('week'),
              moment(moment().endOf('week')).date,
              0,
              0,
              0,
              0
            ),
            isCheckboxDefaultSelected: false,
          },
          {
            checkboxId: 'OPPCON2',
            checkboxName: 'This Month',
            checkboxStartValue: this.utilityService.getFormattedDate(
              moment().startOf('month'),
              moment(moment().startOf('month')).date,
              0,
              0,
              0,
              0
            ),
            checkboxEndValue: this.utilityService.getFormattedDate(
              moment().endOf('month'),
              moment(moment().endOf('month')).date,
              0,
              0,
              0,
              0
            ),
            isCheckboxDefaultSelected: false,
          },
          {
            checkboxId: 'OPPCON3',
            checkboxName: 'Previous Month',
            checkboxStartValue: this.utilityService.getFormattedDate(
              moment().subtract(1, 'month').startOf('month'),
              moment(moment().subtract(1, 'month').startOf('month')).date,
              0,
              0,
              0,
              0
            ),
            checkboxEndValue: this.utilityService.getFormattedDate(
              moment().subtract(1, 'month').endOf('month'),
              moment(moment().subtract(1, 'month').endOf('month')).date,
              0,
              0,
              0,
              0
            ),
            isCheckboxDefaultSelected: false,
          },
          {
            checkboxId: 'OPPCON',
            checkboxName: 'Current Financial Year',
            checkboxStartValue: this.utilityService.getFormattedDate(
              moment().month() >= 3 ? moment().month(3).startOf('month') : moment().subtract(1, 'year').month(3).startOf('month'),
              1, 0, 0, 0, 0
            ),
            checkboxEndValue: this.utilityService.getFormattedDate(
              moment().month() >= 3 ? moment().add(1, 'year').month(2).endOf('month') : moment().month(2).endOf('month'),
              31, 0, 0, 0, 0
            ),
            isCheckboxDefaultSelected: false,
          }
          
        ];

        this.udrfService.udrfFunctions.constructCustomRangeData(
          14,
          'date',
          durationRangesCreatedOn
        );

    //console.log("First")
    this.udrfService.udrfBodyData = [];
    // this.udrfService.udrfUiData.groupByResponseData=[];


    this.udrfService.udrfData.applicationId = this.applicationId;
    this.udrfService.udrfUiData.showNewReleasesButton = true;
    this.udrfService.udrfUiData.showItemDataCount = true;
    this.udrfService.udrfUiData.itemDataType = "";
    this.udrfService.udrfUiData.bookmarkId = "id";
    this.udrfService.udrfUiData.totalItemDataCount = 0;
    this.udrfService.udrfUiData.showSearchBar = true;
    this.udrfService.udrfUiData.showActionButtons = true;
    this.udrfService.udrfUiData.showUdrfModalButton = true;
    this.udrfService.udrfUiData.showSettingsModalButton = true;
    this.udrfService.udrfUiData.isReportDownloading = false;
    this.udrfService.udrfUiData.showReportDownloadButton = true;
    this.udrfService.udrfUiData.showColumnConfigButton = true;
    this.udrfService.udrfUiData.showGroupByButton = true;
    this.udrfService.udrfUiData.itemHasOpenInNewTab = true;
    this.udrfService.udrfUiData.openInNewTab = this.openOpportunityPageInNewTab.bind(this);
    this.udrfService.udrfUiData.downloadItemDataReport = this.downloadWinLossReport.bind(this);
    this.udrfService.udrfUiData.itemHasOpenModal = true;
    this.udrfService.udrfUiData.openModal = this.openWinLossForm.bind(this);
    this.udrfService.udrfUiData.openModalName = "Open Win-Loss Form"
    this.udrfService.udrfUiData.udrfItemStatusColor = this.udrfItemStatusColor;
    this.udrfService.udrfUiData.udrfBodyColumns = this.udrfBodyColumns;
    this.udrfService.udrfUiData.horizontalScroll = true
    this.udrfService.udrfUiData.resolveVisibleSummaryCards = this.resolveVisibleDataTypeArray.bind(this)
    this.udrfService.udrfUiData.summaryCardsSelected = this.dataTypeCardSelected.bind(this);
    this.udrfService.udrfUiData.itemcardSelected = this.opportunityCardClicked.bind(this)
    this.udrfService.udrfUiData.summaryCardsItem = {};
    //this.udrfService.udrfUiData.callInlineEditApi = this.callInlineEditApi.bind(this)
    this.udrfService.udrfUiData.inlineEditData = {};
    this.udrfService.udrfUiData.inlineEditDropDownMasterDatas = {};
    this.udrfService.udrfUiData.summaryCards = this.dataTypeArray;
    this.udrfService.udrfUiData.udrfVisibleBodyColumns = this.udrfService.udrfUiData.udrfVisibleBodyColumns;
    this.udrfService.udrfUiData.udrfInvisibleBodyColumns = this.udrfService.udrfUiData.udrfInvisibleBodyColumns;
    this.udrfService.udrfUiData.categorisedSummaryCards = this.categorisedDataTypeArray;
    this.udrfService.udrfUiData.minNoOfVisibleSummaryCards = this.minNoOfVisibleSummaryCards;
    this.udrfService.udrfUiData.maxNoOfVisibleSummaryCards = this.maxNoOfVisibleSummaryCards;
    this.udrfService.udrfUiData.selectedCard = this.selectedCard;
    this.udrfService.udrfUiData.variant = 0;
    this.udrfService.udrfUiData.itemHasQuickCta = false;
    this.udrfService.udrfUiData.itemHasComments = false;
    this.udrfService.udrfUiData.itemHasHierarchyView = false;
    this.udrfService.udrfUiData.itemHasBookmark = false;
    this.udrfService.udrfUiData.quickCTAInput = {};
    this.udrfService.udrfUiData.commentsInput = {};
    this.udrfService.udrfUiData.commentsContext = {};
    this.udrfService.udrfUiData.countForOnlyThisReport = false;
    this.udrfService.udrfUiData.isHeaderSort = true;
    this.udrfService.getAppUdrfConfig(this.applicationId, this.initReport.bind(this));
    
    this.udrfService.udrfUiData.itemDataScrollDown = this.onOpportunityListScrollDown.bind(this);

    this.udrfService.getNotifyReleasesUDRF();
  }
 

  /**
   * @description Gets visible summary cards
   */
  resolveVisibleDataTypeArray = () => {
    for (let summaryCardsItem of this.udrfService.udrfUiData.summaryCards) {

      let isVisible;

      if (this.udrfService.udrfData.udrfSummaryCardCodes.length > 0) {

        isVisible = _.contains(this.udrfService.udrfData.udrfSummaryCardCodes, summaryCardsItem.dataTypeCode);
        summaryCardsItem.isVisible = isVisible;
        if (isVisible) {
          this.udrfService.udrfUiData.summaryCardsItem = summaryCardsItem;

        }
      }

    }

  }
  
  /**
   * @description Select type of summary cards selected
   */
  dataTypeCardSelected = () => {

    this.udrfService.udrfData.isItemDataLoading = true;
    this.udrfService.udrfData.noItemDataFound = false;

    let dataTypeArrayItem = this.udrfService.udrfUiData.summaryCardsItem;

    for (let i = 0; i < this.dataTypeArray.length; i++)
      if (dataTypeArrayItem["dataTypeCode"] == this.dataTypeArray[i].dataTypeCode) {

        this.dataTypeArray[i].isActive = true;
        dataTypeArrayItem["isActive"] = true;

      }
      else {

        let summaryCard = _.where(this.udrfService.udrfUiData.summaryCards, { dataTypeCode: this.dataTypeArray[i].dataTypeCode });

        if (summaryCard.length > 0)
          summaryCard[0].isActive = false;

        this.dataTypeArray[i].isActive = false;

      }

    this.isCardClicked = true;

    this.cardClicked = dataTypeArrayItem["dataType"];

    this.WLItemDataCurrentIndex = 0;
    this.udrfService.udrfBodyData = [];

    this.getOpportunityList();

  }

  /**
   * @description Opens opportunity when click on card
   */
  opportunityCardClicked() {
    let opportunityId = this.udrfService.udrfUiData.itemCardSelecteditem["opportunity_id"];
    let opportunityName = this.udrfService.udrfUiData.itemCardSelecteditem["opportunity_name"];
    let navigationUrl =
      window.location.origin +
      "/main/opportunities/" +
      opportunityId +
      "/" +
      this.encodeURIComponent(opportunityName);

    window.open(navigationUrl);

  }

  /**
   * 
   * @param str 
   * @description Encode the URL
   * @returns 
   */
  encodeURIComponent(str) {
    return encodeURIComponent(str).replace(/[!'()*]/g, function (c) {
      return "%" + c.charCodeAt(0).toString(16);
    });
  }


  /**
   * @description Intialize the Win Loss Report
   */
  async initReport() {
    console.log("InitReport")
    this._onAppApiCalled.next();
    this.WLItemDataCurrentIndex = 0;
    this.udrfService.udrfBodyData = [];
    for (let i = 0; i < this.dataTypeArray.length; i++) {
      this.dataTypeArray[i].isActive = false;
    }
    this.isCardClicked = false;
    this.cardClicked = "";
    this.udrfService.udrfUiData.resolveColumnConfig();
    this.getOpportunityList();

  }

  /**
   * @description This method calls win loss report list
   */
  async getOpportunityList() {

    let mainFilterArray = JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray));
    console.log("Main filter Arrya", mainFilterArray)
    let activeSummaryCard = _.where(this.dataTypeArray, { isActive: true });

    let activeStatusSummaryCard = [];

    let statusApplied = false
    if (activeSummaryCard.length > 0) {

      activeStatusSummaryCard = _.where(activeSummaryCard, { cardType: "status" });

      if (activeStatusSummaryCard.length > 0)

        if (mainFilterArray.length > 0) {

          for (let mainFilterArrayItem of mainFilterArray) {
            if (mainFilterArrayItem.filterName == "Status") {
              mainFilterArrayItem.multiOptionSelectSearchValues = [activeSummaryCard[0].dataType];
              statusApplied = true;
            }
          }
          if (statusApplied == false) {
            let statusArray = JSON.parse(JSON.stringify(_.where(this.udrfService.udrfData.filterTypeArray, { filterName: "Status" })));
            mainFilterArray.push(statusArray[0])

            for (let mainFilterArrayItem of mainFilterArray) {
              if (mainFilterArrayItem.filterName == "Status") {

                mainFilterArrayItem.multiOptionSelectSearchValues = [activeSummaryCard[0].dataType];
                statusApplied = true;
              }
            }
          }


          // let filterData = _.filter(res['messData'], function (status) {
        }
        else {

          mainFilterArray = JSON.parse(JSON.stringify(_.where(this.udrfService.udrfData.filterTypeArray, { filterName: "Status" })));

          mainFilterArray[0].multiOptionSelectSearchValues = [activeSummaryCard[0].dataType];

        }

    }

    console.log("Fileter Array", mainFilterArray)
    let filterConfig = {
      startIndex: this.WLItemDataCurrentIndex,
      startDate: this.udrfService.udrfData.mainApiDateRangeStart,
      endDate: this.udrfService.udrfData.mainApiDateRangeEnd,
      mainFilterArray: mainFilterArray,
      txTableDetails: this.udrfService.udrfData.txTableDetails,
      mainSearchParameter: this.udrfService.udrfData.mainSearchParameter,
      searchTableDetails: this.udrfService.udrfData.searchTableDetails
    };

    this.selectedCard = this.udrfService.udrfData.udrfSummaryCardCodes;
    if (this.requestDataSubscription)
      this.requestDataSubscription.unsubscribe();
    this.requestDataSubscription = this.winLossService.getWinLossReportOpportunityList(filterConfig).pipe(takeUntil(this._onDestroy)).pipe(takeUntil(this._onAppApiCalled))
      .subscribe(
        async (res) => {

          if (
            res['messType'] == 'S' &&
            res['messData'] &&
            res['messData'].length > 0
          ) {

            if (!this.isCardClicked) {
              _.each(res['messData'], i => {
                i.expanded = this.udrfService.udrfUiData.collapseAll
              })
              for(let result of res['messData'])
                {
              for (let [key, value] of Object.entries(result)) {
                if (typeof value === "object") {
                    if (value != null && value != undefined) {
                        try {
                            if (value['type'] === "date" && value['date'] !== '-') {      
                                result[key] = moment(value['date']).utc().format(this.dateFormat);
                                if (result[key] === 'Invalid date') {
                                  result[key] = '-';
                                }
                            }
                            if (value['type'] === "date" && value['date'] === '-') {     
                              result[key] = '-';
                            }
                        } catch (err) {
                            console.log(err);
                        }
                    }
                }
            }  
          }          

              this.udrfService.udrfBodyData = this.udrfService.udrfBodyData.concat(res['messData']);
              this.initWLCard()



            }

            else {
              _.each(res['messData'], i => {
                i.expanded = this.udrfService.udrfUiData.collapseAll
              })

              this.udrfService.udrfBodyData = this.udrfService.udrfBodyData.concat(res['messData']);

            }

          } else {

            this.udrfService.udrfBodyData = this.udrfService.udrfBodyData.concat([]);

            this.udrfService.udrfData.noItemDataFound = true;
            this.initWLCard()
          }


          this.udrfService.udrfData.isItemDataLoading = false;

        }, (err) => {
          this.showErrorMessage(err);
        });


  }


    downloadWinLossReport()
    { 
      let mainFilterArray = JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray));
    console.log("Main filter Arrya", mainFilterArray)
    let activeSummaryCard = _.where(this.dataTypeArray, { isActive: true });

    let activeStatusSummaryCard = [];

    let statusApplied = false
    if (activeSummaryCard.length > 0) {

      activeStatusSummaryCard = _.where(activeSummaryCard, { cardType: "status" });

      if (activeStatusSummaryCard.length > 0)

        if (mainFilterArray.length > 0) {

          for (let mainFilterArrayItem of mainFilterArray) {
            if (mainFilterArrayItem.filterName == "Status") {
              mainFilterArrayItem.multiOptionSelectSearchValues = [activeSummaryCard[0].dataType];
              statusApplied = true;
            }
          }
          if (statusApplied == false) {
            let statusArray = JSON.parse(JSON.stringify(_.where(this.udrfService.udrfData.filterTypeArray, { filterName: "Status" })));
            mainFilterArray.push(statusArray[0])

            for (let mainFilterArrayItem of mainFilterArray) {
              if (mainFilterArrayItem.filterName == "Status") {

                mainFilterArrayItem.multiOptionSelectSearchValues = [activeSummaryCard[0].dataType];
                statusApplied = true;
              }
            }
          }


          // let filterData = _.filter(res['messData'], function (status) {
        }
        else {

          mainFilterArray = JSON.parse(JSON.stringify(_.where(this.udrfService.udrfData.filterTypeArray, { filterName: "Status" })));

          mainFilterArray[0].multiOptionSelectSearchValues = [activeSummaryCard[0].dataType];

        }

    }

    console.log("Fileter Array", mainFilterArray)
    let filterConfig = {
      startIndex: this.WLItemDataCurrentIndex,
      startDate: this.udrfService.udrfData.mainApiDateRangeStart,
      endDate: this.udrfService.udrfData.mainApiDateRangeEnd,
      mainFilterArray: mainFilterArray,
      txTableDetails: this.udrfService.udrfData.txTableDetails,
      mainSearchParameter: this.udrfService.udrfData.mainSearchParameter,
      searchTableDetails: this.udrfService.udrfData.searchTableDetails
    };

    this.selectedCard = this.udrfService.udrfData.udrfSummaryCardCodes;
    if (this.requestDataSubscription)
      this.requestDataSubscription.unsubscribe();
 
     this.winLossService.getWinLossReport(filterConfig).pipe(takeUntil(this._onDestroy)).pipe(takeUntil(this._onAppApiCalled))
        .subscribe(async res => {
  
          const reportData=res['messData']
          let dateFieldArray=[]
  
          if (res['messType'] == 'S') {
  for (let result of reportData) {
              for (let [key, value] of Object.entries(result)) {
                if (typeof value == "object") {
                  if (value != null && value != undefined) {
                    try {
                      if (value['type'] == "date") {
  
  
  
                        const fieldExists = dateFieldArray.find(val => val['fieldKey'] == key);
  
                        if (!fieldExists)
                          dateFieldArray.push({
                            fieldKey: key,
                            fieldFormat: this.dateFormat
                          });
  
                        result[key] = value['value'] ? moment(value['value']).utc().format("YYYY/MM/DD") : null;
  
                        // dateFieldArray.push({fieldKey:key,fieldFormat:this.dateFormat})
  
                        // result[key] = moment(value['date']).utc().format("YYYY-MM-DD")
  
                      }
                    }
                    catch (err) {
                      console.log(err)
                    }
                  }
                }
              }
  
            }
  
            this.exceltoJson.exportAsExcelFile(reportData, res['fileName'], [], null,dateFieldArray)
  
  
          }
  
          else {
  
           this.utilityService.showMessage("Unable to download report","Dismiss",3000)
  
          }
  
          this.udrfService.udrfUiData.isReportDownloading = false;
  
  
        }, err => {
          this.showErrorMessage(err);
        });
    }
  /**
   * @description Initialize Win Loss summary cards
   */
  async initWLCard() {

    // this.wfhList = data;

    let filterConfig = {
      startIndex: this.WLItemDataCurrentIndex,
      startDate: this.udrfService.udrfData.mainApiDateRangeStart,
      endDate: this.udrfService.udrfData.mainApiDateRangeEnd,
      mainFilterArray: this.udrfService.udrfData.mainFilterArray,
      txTableDetails: this.udrfService.udrfData.txTableDetails,
      mainSearchParameter: this.udrfService.udrfData.mainSearchParameter,
      searchTableDetails: this.udrfService.udrfData.searchTableDetails
    };


    this.winLossService.getWinLossReportOpportunityCard(filterConfig).pipe(takeUntil(this._onDestroy)).pipe(takeUntil(this._onAppApiCalled))
      .subscribe(async res => {

        if (this.udrfService.udrfData.udrfSummaryCardCodes.length > 0) {
          console.log(this.udrfService.udrfData.udrfSummaryCardCodes)
          this.resolveVisibleDataTypeArray();
        }

        if (
          res['messType'] == 'S' &&
          res['messData'] &&
          res['messData'].length > 0
        ) {

          for (let i = 0; i < this.dataTypeArray.length; i++) {
            this.dataTypeArray[i].isActive = false;
            this.dataTypeArray[i].dataTypeValue = "0";
          }
          this.dataTypeArray = this.dataTypeArray.map((item, row) => {
            const found = res["messData"].find(
              element => item.dataType == element.dataType
            );
            return { ...item, ...found };
          });



          this.udrfService.udrfUiData.summaryCards = this.dataTypeArray


          this.udrfService.udrfUiData.totalItemDataCount = res['total']


        }

        else {

          for (let i = 0; i < this.dataTypeArray.length; i++) {
            this.dataTypeArray[i].isActive = false;
            this.dataTypeArray[i].dataTypeValue = "0";
          }

          this.udrfService.udrfUiData.summaryCards = this.dataTypeArray
          this.udrfService.udrfUiData.totalItemDataCount = res['total'] || 0

        }


      }, err => {
        this.showErrorMessage(err);
      });




  }
  /**
   * 
   * @param err 
   * @description Displays error msg in snack bar
   */
  showErrorMessage(err) {

    let errReportingTeams = "KEBS";

    this.utilityService.showErrorMessage("Error:Unable to fetch Data", errReportingTeams);

  }
  /**
   * @description Opens opportunity in new tab when Right click open in new tab is clicked
   */
  openOpportunityPageInNewTab() {
    let cardData = this.udrfService.udrfUiData.openinNewTabData["data"];

    let navigationUrl = `${window.location.origin}/main/opportunities/${cardData['opportunity_id']}/${cardData['description']}/overview`;
    window.open(navigationUrl);
  }
  /**
   * @description Opens Form in right Click when open PopUp button is clicked
   */
  async openWinLossForm() {
    let cardData = this.udrfService.udrfUiData.openModalData
    console.log("WinLossPop", cardData)
    if (cardData != undefined) {
      const { CustomFormModalComponent } = await import('src/app/modules/shared-lazy-loaded-components/custom-form-modal/custom-form-modal.component')
      const openCustomFormModalComponent = this.dialog.open(CustomFormModalComponent,
        {
          height: '100%',
          width: '75%',
          position: {
            right: '0px'
          },

          data:
          {
            formId: cardData['win_loss_form'],
            isEdit: true,
            entryForId: (cardData['opportunity_id']).toString(),
            formName: "Win-Loss Form"
          }
        }
      )

      openCustomFormModalComponent.afterClosed()
        .subscribe(async (res: any) => {
          console.log(res)
          if (res != undefined) {
            if (res.messType == 'S') {
              await this.opportunityService.updateWinLossForm(cardData['win_loss_form'], cardData['opportunity_id']).then((res) => {
                this.snackBar.open("Form filled successfully!", "Dismiss", { duration: 2000 })
                this.udrfService.udrfBodyData = [];
                this.initReport();
              }, (err) => {
                console.log(err)
              });


            }


          }
        })
    }
  }

  /**
   * @description Lazy loading of lead list
   */
   onOpportunityListScrollDown(){
    if (!this.udrfService.udrfData.noItemDataFound) {

      this.WLItemDataCurrentIndex += this.udrfService.udrfData.defaultRecordsPerFetch;

      console.log("Next",this.WLItemDataCurrentIndex)

      this.udrfService.udrfData.isItemDataLoading = true;

      this.getOpportunityList();

    }
  }
  async getGlobalCRMDateFormat() {
    this.udrfServices.getCRMGlobalDateFormat().subscribe(
        (response: any) => {
            if (response.messType === 'S' && response?.data && response.data.length > 0) {
                const config = JSON.parse(response.data[0].config);
                this.dateFormat = config.format || this.dateFormat;
            }
        },
        (error) => {
            console.error("Error retrieving date format", error);
        }
    );
}

  ngOnDestroy() {

    this._onDestroy.next();
    this._onDestroy.complete();

    this.udrfService.resetUdrfData();

  }









}



