import { Component, OnInit, ViewChild } from '@angular/core';
import { takeUntil } from 'rxjs/operators';
import { Subject, Subscription } from 'rxjs';
import { ToasterService } from 'src/app/services/toaster-service/toaster.service';
import { MatSnackBar } from '@angular/material/snack-bar';
import * as _ from 'underscore';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE, MatOption } from '@angular/material/core';
import { MomentDateAdapter, MAT_MOMENT_DATE_ADAPTER_OPTIONS } from '@angular/material-moment-adapter';
import * as moment from 'moment';
import { FormBuilder, FormGroup, FormArray, FormsModule, ReactiveFormsModule, Validators, FormControl } from '@angular/forms';
import { ApiserviceService } from '../services/apiservice.service';
import { error } from 'console';
import { LongPoolingServiceService } from '../services/long-pooling-service.service';
import { MatSelect } from '@angular/material/select';


@Component({
  selector: 'app-ils-zb-gl-exceptoin-report',
  templateUrl: './ils-zb-gl-exceptoin-report.component.html',
  styleUrls: ['./ils-zb-gl-exceptoin-report.component.scss'],
  providers: [
    {
      provide: DateAdapter,
      useClass: MomentDateAdapter,
      deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS]
    },
    {
      provide: MAT_DATE_FORMATS,
      useValue: {
        parse: {
          dateInput: "DD-MM-YYYY"
        },
        display: {
          dateInput: "DD-MM-YYYY",
          monthYearLabel: "MMM YYYY"
        }
      }
    },
  ]
})
export class IlsZbGlExceptoinReportComponent implements OnInit {

  isZBFormCreationCompleted = false
  availableZBConnections = []

  selectedEntityBook = new FormControl('')
  userSelectedBookEntity: any

  userSelectedEntityName = ''
  userSelectedEntityID = ''
  userSelectedBookID = 0
  userSelectedSourceURL = ''
  isTokenNotAvailable = false
  isTokenGeneratedOnRefreshToken = false

  availableZBReportVoucherTypes = []
  appliedVoucherTypesforZB = []

  authenticationFieldMapping = {
    key: "Bearer",
    value: '',
  }

  queryParamsInputForm = this.fb.group({
    date_start: ['', Validators.required],
    date_end: ['', Validators.required],
    voucher_type: ['', Validators.required],
  })

  minStartDate = new Date("1900-01-01");
  minEndDate: any;
  isStartDateSelected = false

  queryFormDisabled = true
  responseProcess = false
  body = { "url": '' }
  sampleResponse = []
  response = []
  tempResponse = []

  $onDestroy = new Subject<any>();

  viewLogs = false
  isDataRetrived = false
  isSyncOnProgress = true
  sampleOnSyncResponse: any

  ExceptionReportData = []
  isComparisionCompleted = true

  isExceptionReportAvailable = false
  longPoolingTimerConfig = 0

  longPoolingData: any;
  private pollingSubscription: Subscription;
  newSyncID = 0

  isRetrievalCompletedforSpinner = true
  spinnerStatusText = ''
  
  allSelected=false;
  @ViewChild('select') select: MatSelect;

  constructor(private fb: FormBuilder, private api: ApiserviceService, private _toaster: ToasterService,
    private snackBar: MatSnackBar, private longPollingService: LongPoolingServiceService) { }

  async ngOnInit(): Promise<void> {

    await this.api.getLongPoolingConfig().pipe(takeUntil(this.$onDestroy))
      .subscribe((res: any) => {
        if (res.messType == "S") {
          this.longPoolingTimerConfig = res["data"]
        }
      })

    await this.api.getAdminZBConnectionDetails().pipe(takeUntil(this.$onDestroy))
      .subscribe((res: any) => {
        if (res.messType == "S") {
          this.isZBFormCreationCompleted = true
          this.availableZBConnections = res["data"]
        }
        else {
          this.availableZBConnections = []
          this.userSelectedEntityName = ''
          this.userSelectedEntityID = ''

        }
      })

    await this.api.getZBVoucherTypes().pipe(takeUntil(this.$onDestroy))
      .subscribe(res => {
        if (res.messType == "S") {
          this.availableZBReportVoucherTypes = res["data"];
        }
        else {
          this.availableZBReportVoucherTypes = []
        }
      })
  }

  async changeEntity() {
    let changedEntity = this.selectedEntityBook.value

    let selectedEntityDetails = this.availableZBConnections.filter(item => item.id == changedEntity)
    this.userSelectedEntityName = selectedEntityDetails[0].entity_name
    this.userSelectedEntityID = selectedEntityDetails[0].company_id
    this.userSelectedBookID = selectedEntityDetails[0].book_id
    this.userSelectedSourceURL = selectedEntityDetails[0].source_url

    this.queryFormDisabled = false

    console.log("Changed Entity Value : ", this.userSelectedEntityName, this.userSelectedEntityID, this.userSelectedBookID)

    await this.getTokenDetails()

  }

  addOperationValue() {
    return this.fb.group({
      voucher_type: new FormControl('', Validators.required),
    })
  }
  onVoucherTypeAddRow() {
    const control = <FormArray>this.queryParamsInputForm.controls['voucherTypeArray']
    control.push(this.addOperationValue())
  }

  onVoucherTypeRemoveRow(rowIndex: any) {
    let voucherParamsControl = <FormArray>this.queryParamsInputForm.controls['voucherTypeArray']
    console.log("Control length : ", voucherParamsControl.length)
    if (voucherParamsControl.length <= 1) {
      voucherParamsControl.clear()
      this.onVoucherTypeAddRow()
    }
    else {
      voucherParamsControl.removeAt(rowIndex);
      console.log("Voucher Type afer deleting rows : ", this.queryParamsInputForm.value);
    }
  }

  async getZBReportsData() {
    let queryParamsValues
    if (this.queryParamsInputForm.valid) {
      this.isDataRetrived = true

      this.appliedVoucherTypesforZB = []

      queryParamsValues = this.queryParamsInputForm.value
      queryParamsValues.date_start_value = moment(queryParamsValues.date_start).format("YYYY-MM-DD")
      queryParamsValues.date_end_value = moment(queryParamsValues.date_end).format("YYYY-MM-DD")

      console.log("Submission QueryParams Details : ", queryParamsValues)

      let voucherParams = queryParamsValues.voucher_type
      let uniqueVoucherTypes = []

      uniqueVoucherTypes = [...new Set(voucherParams)]

      if (uniqueVoucherTypes && uniqueVoucherTypes.length > 0) {
        _.each(uniqueVoucherTypes, (item) => {
          let temp = this.availableZBReportVoucherTypes.filter(tempItem => tempItem.id == item)
          this.appliedVoucherTypesforZB.push(temp[0]?.name)
        })
      }
      queryParamsValues.selectedVouchers = this.appliedVoucherTypesforZB
      console.log("User Given Query params Values : ", queryParamsValues)



      // Date Range Binding
      if (!this.userSelectedSourceURL.includes("date_start") && !this.userSelectedSourceURL.includes("date_end")) {
        this.userSelectedSourceURL += `&date_start=${queryParamsValues.date_start_value}&date_end=${queryParamsValues.date_end_value}`
      }
      else {
        let OrgURL = this.userSelectedSourceURL
        let url = new URL(OrgURL);
        let startDate = url.searchParams.get('date_start');
        let endDate = url.searchParams.get('date_end');
        if(startDate != queryParamsValues.date_start_value){
          url.searchParams.set("date_start",queryParamsValues.date_start_value)
        }
        if(endDate != queryParamsValues.date_end_value){
          url.searchParams.set("date_end",queryParamsValues.date_end_value)
        }

        this.userSelectedSourceURL = String(url)
      }

      console.log("Source URL : ", this.userSelectedSourceURL)
      console.log("Selected Voucher Types : ", this.appliedVoucherTypesforZB)
      console.log("Authorization Details : ", this.authenticationFieldMapping)

      // Retrieve Data From ZohoBooks
      await this.getZBDataFromSourceForExceptionReport()

    }
    else {
      return this.snackBar.open("Kindly give the values for all the required fields!", "Dismiss", { duration: 3000 });
    }
  }

  async getTokenDetails() {
    await this.api.getTokenForAdminConnection(this.userSelectedBookID, this.userSelectedEntityID).pipe(takeUntil(this.$onDestroy))
      .subscribe(res => {
        if (res.messType == "S") {
          this.isTokenNotAvailable = false
          this.isTokenGeneratedOnRefreshToken = false
          this.authenticationFieldMapping["value"] = res["tokenDetails"]["token"]
        }
        else {
          this.isTokenNotAvailable = true
          this.isTokenGeneratedOnRefreshToken = false
          this.authenticationFieldMapping["value"] = ''
          this._toaster.showError(`Token Status`,
            `Token Not Generated! Kindly refresh the token ! `, 3000)
        }
      })
  }

  async getZBRefreshToken() {
    this.isTokenGeneratedOnRefreshToken = true
    await this.api.getTokenForAdminConnection(this.userSelectedBookID, this.userSelectedEntityID).pipe(takeUntil(this.$onDestroy))
      .subscribe(res => {
        if (res.messType == "S") {
          this.isTokenNotAvailable = false
          this.isTokenGeneratedOnRefreshToken = false
          this.authenticationFieldMapping["value"] = res["tokenDetails"]["token"]
        }
        else {
          this.isTokenNotAvailable = true
          this.isTokenGeneratedOnRefreshToken = false
          this.authenticationFieldMapping["value"] = ''
          this._toaster.showError(`Token Status`,
            `Token Not Generated for Sync ! Kindly reach out to KEBS team ! `, 3000)
        }
      })
  }

  async getZBDataFromSourceForExceptionReport() {

    this.isRetrievalCompletedforSpinner = false
    this.spinnerStatusText = 'Data is being retrieved from ZohoBooks'
   
    await this.api.postNewSyncIDForComparision().pipe(takeUntil(this.$onDestroy))
      .subscribe(async res => {
        if (res["messType"] == "S") {
          this.newSyncID = res["data"]


          this.isDataRetrived = true
          this.viewLogs = false
          this.responseProcess = false
          this.body.url = this.userSelectedSourceURL

          this.ExceptionReportData = []
          this.isExceptionReportAvailable = false

          this.sampleResponse = []
          this.response = []
          let endpoint = 'getZBReportsData'

          Object.assign(this.body, { "method": "GET" })
          Object.assign(this.body, { "key": this.authenticationFieldMapping.key })
          Object.assign(this.body, { "value": this.authenticationFieldMapping.key + ' ' + this.authenticationFieldMapping.value })
          Object.assign(this.body, { "uniqueVoucherTypes": this.appliedVoucherTypesforZB })
          Object.assign(this.body, { "entity_id": this.userSelectedBookID })
          Object.assign(this.body, { "sync_id": this.newSyncID })
          // this.body["uniqueVoucherTypes"] = this.appliedVoucherTypesforZB

          let data
          let requestBody = JSON.stringify(this.body)
          data = JSON.parse(requestBody)
          let params = ''
          let headers = ''
          await this.api.getZBDataFromSourceForExceptionReport(data).pipe(takeUntil(this.$onDestroy))
            .subscribe(async res => {
              if (res["messType"] === "S") {

                // this.isDataRetrived = false
                this.response.push(res["data"])
                this.tempResponse = Object.assign([], this.response)
                let responseLength = 0
                let SampleReposnetoShow = {}
                let responseVouchers = Object.keys(this.response[0])

                console.log("Response : ", this.response[0], " with length : ", responseLength)

                console.log("Compariosion Report Data : ", this.response[0])

                // this.responseProcess = true

              }
              else if (res["messType"] === "E") {
                this.responseProcess = false
                this.isDataRetrived = false
                this.response.push(res["error"])
                this.tempResponse = Object.assign([], this.response)
                console.log("Error on Response : ", this.response[0])
                this.response = this.response[0]
                this.sampleResponse = this.response

                // return this._toaster.showError("Error Status : " + "\nError Description : " + JSON.stringify(this.response["error"]), this.userSelectedSourceURL, 5000)

              }
            })

          await this.initLongPolling(this.newSyncID);
        }
        else {
          this.newSyncID = 0
        }
      })

  }

  ViewLogs() {
    this.viewLogs = !this.viewLogs
  }

  async generateExceptionReport() {
    this.isComparisionCompleted = false
    this.ExceptionReportData = []
    this.appliedVoucherTypesforZB
    let queryParamsValues = this.queryParamsInputForm.value
    queryParamsValues.date_start = moment(queryParamsValues.date_start).format("YYYY-MM-DD")
    queryParamsValues.date_end = moment(queryParamsValues.date_end).format("YYYY-MM-DD")

    await this.api.getExcepionReportForZBLedgers(this.userSelectedSourceURL, queryParamsValues.date_start, queryParamsValues.date_end, this.userSelectedBookID, this.response[0]).pipe(takeUntil(this.$onDestroy))
      .subscribe(async res => {
        if (res["messType"] === "S" && res["data"] && res["data"].length > 0) {
          this.isExceptionReportAvailable = true
          this.isComparisionCompleted = true
          this.ExceptionReportData = res["data"]
          console.log("Compariosion Report Data : ", this.ExceptionReportData)
        }
        else if (res["messType"] === "S" && res["data"].length == 0) {
          this.isExceptionReportAvailable = false
          this.isComparisionCompleted = true
          this.ExceptionReportData = []
          console.log("No Exception Found for the Selected Month and Voucher Types!")
          this._toaster.showInfo("Exception Report Status !",
            res["messText"], 5000)
        }
      })

  }

  onExporting(e: any): void {
    let report_name = 'ZohoBooks GL Exception Report'
    let exportFileName = `${report_name}`; // Set your custom file name here
    e.component.beginUpdate();
    e.fileName = exportFileName;
    e.component.endUpdate();
  }

  // async initLongPolling(syncID) {
  //  await this.longPollingService.longPollforComparision(this.longPoolingTimerConfig, syncID).subscribe(
  //     async (response) => {
  //       if (response.messType == "S" && response.messStatus != "Completed" && response.messStatus != "In-Progress" && response.messStatus != "Error") {
  //         this.longPoolingData = response;
  //         // console.log("LongPooling Reponse : ", this.longPoolingData)
  //         await this.initLongPolling(syncID); // Restart long polling after receiving data
  //       }
  //       else if ((response.messType == "S" && response.messStatus == "Completed")) {
  //         console.log("LongPooling Reponse : ", this.longPoolingData)
  //         this.isDataRetrived = false
  //         this.responseProcess = true
  //         return this._toaster.showSuccess(`ZohoBooks Reports Retrieved Successfully !`,
  //           '', 5000)
  //       }
  //       else if((response.messType == "S" && response.messStatus == "In-Progress" && response.isNoDataFound == true)){
  //         console.log("LongPooling Reponse : ", this.longPoolingData)
  //         this.isDataRetrived = false
  //         this.responseProcess = false
  //         return this._toaster.showInfo(`No Data Available to Generate Exception Report !`,
  //           '', 5000)
  //       }
  //       else if((response.messType == "E" && response.messStatus == "Error")){
  //         console.log("LongPooling Reponse : ", this.longPoolingData)
  //         this.isDataRetrived = false
  //         this.responseProcess = true
  //         return this._toaster.showError(`Error on ZohoBooks Data Rerieval !`,
  //           '', 5000)
  //       }
  //       else{
  //         await this.initLongPolling(syncID); // Restart long polling after receiving data
  //       }
  //     },
  //     (error) => {
  //       console.error('Error fetching data:', error);
  //       // Handle errors or retry logic here
  //     }
  //   );
  // }

  async initLongPolling(syncID: any) {
    this.pollingSubscription = await this.longPollingService.longPollforComparision(this.longPoolingTimerConfig, syncID).subscribe(
      async (response) => {
        if (response.messStatus === "Started" || response.messStatus === "") {
          this.updateSpinnerStatus('Data is being retrieved from ZohoBooks');
        }
        else if (response.messStatus === "In-Progress") {
          this.updateSpinnerStatus('GL Exception is being Generated');
        }
        else if (response.messType === "S" && (response.messStatus === "In-Progress" || response.messStatus === "Completed") && response.isNoDataFound === true) {
          this.handleNoDataFoundResponse(response);
        }
        else if (response.messType === "E" && response.messStatus === "Error") {
          this.handleError(response);
        }
        else if (response.messType === "S" && response.messStatus === "Completed") {
          this.handleCompletedResponse(response);
        }
        // For other cases, continue polling
      },
      (error) => {
        console.error('Error fetching data:', error);
        // Handle errors or retry logic here
      }
    );
  }


  private updateSpinnerStatus(statusText: string) {
    this.isRetrievalCompletedforSpinner = false;
    this.spinnerStatusText = statusText;
  }

  private async handleCompletedResponse(response: any) {
    this.isDataRetrived = false;
    this.responseProcess = true;
    this.isRetrievalCompletedforSpinner = true;
    this._toaster.showSuccess(`ZohoBooks GL Exception Report Generated Successfully !`, '', 5000);

    this.stopPolling();

    await this.api.getGLExceptionalReportForZB(response.id).pipe(takeUntil(this.$onDestroy))
    .subscribe(async res => {
      if(res.messType == "S"){
        this.isExceptionReportAvailable = true
        this.ExceptionReportData = (res["data"]?.report_data && res["data"]?.report_data.length > 0) ? res["data"].report_data : []
      }
      else{
        this.isExceptionReportAvailable = true
        this.ExceptionReportData = []
      }
    })

  }

  private handleNoDataFoundResponse(response: any) {
    this.isDataRetrived = false;
    this.responseProcess = true;
    this.isRetrievalCompletedforSpinner = true;
    this._toaster.showInfo(`No Exception Found for the Selected Month and Voucher Types!`, '', 5000);
    this.stopPolling();
  }

  private handleError(response: any) {
    this.isDataRetrived = false;
    this.responseProcess = true;
    this.isRetrievalCompletedforSpinner = true;
    this._toaster.showError(`Error on ZohoBooks Exception Report Generation !`, '', 5000);
    this.stopPolling();
  }

  stopPolling() {
    if (this.pollingSubscription) {
      this.pollingSubscription.unsubscribe();
      this.pollingSubscription = undefined;
    }
  }

  onStartDateChange() {
    let startDate = new Date(this.queryParamsInputForm.value.date_start);
    let startDateFormat = moment(this.queryParamsInputForm.value.date_start)
    this.minEndDate = startDate;
    if(startDateFormat){
      this.isStartDateSelected = true
    }
  }

  
  toggleAllSelection() {
    if (this.allSelected) {
      this.select.options.forEach((item: MatOption) => item.select());
    } else {
      this.select.options.forEach((item: MatOption) => item.deselect());
    }
  }
}
