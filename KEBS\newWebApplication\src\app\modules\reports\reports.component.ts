import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { RolesService } from 'src/app/services/acl/roles.service';
import { ReportsService } from './services/reports.service';
import { LoginService } from 'src/app/services/login/login.service';
import * as _ from 'underscore';
import { takeUntil } from 'rxjs/operators';
import { Subject } from 'rxjs';
import { UtilityService } from 'src/app/services/utility/utility.service';
import { ErrorService } from 'src/app/services/error/error.service';
import { KebsHelpService } from 'kebs-help-application';
import { QuoteMainService } from '../quote-builder/services/quote-main.service';
import { JsonToExcelService } from 'src/app/services/excel/json-to-excel.service';
import { ToasterService } from 'src/app/services/toaster-service/toaster.service';
import * as moment from 'moment';

@Component({
  selector: 'app-reports',
  templateUrl: './reports.component.html',
  styleUrls: ['./reports.component.scss'],
})
export class ReportsComponent implements OnInit {
  allReports = [
    {
      label: 'MIS',
      path: 'mis',
      imageUrl: 'assets/reports_images/mis.png',
      applicationId: 45,
      moduleName: 'Management',
      isPinned: false,
    },
    {
      label: 'Management',
      path: 'management',
      imageUrl: 'assets/reports_images/management.png',
      applicationId: 46,
      moduleName: 'Management',
      isPinned: false,
    },
    {
      label: 'Governance',
      path: 'governance',
      imageUrl: 'assets/reports_images/governance.png',
      applicationId: 71,
      moduleName: 'Management',
      isPinned: false,
    },
    {
      label: 'Governance (BETA)',
      path: 'governance_beta',
      imageUrl: 'assets/reports_images/governance.png',
      applicationId: 220,
      moduleName: 'Management',
      isPinned: false,
    },
    {
      label: 'P & L Governance',
      path: 'pl-governance',
      imageUrl: 'assets/reports_images/governance.png',
      applicationId: 91,
      moduleName: 'Management',
      isPinned: false,
    },
    {
      label: 'Practice Gov.(LG)',
      path: 'practiceGov',
      imageUrl: 'assets/reports_images/activity_gov.png',
      applicationId: 287,
      moduleName: 'Management',
      isPinned: false,
    },
    {
      label: 'AR',
      path: 'ar',
      imageUrl: 'assets/reports_images/ar.png',
      applicationId: 47,
      moduleName: 'Finance',
      isPinned: false,
    },
    {
      label: 'AR-UBR Days',
      path: 'ar-ubr',
      imageUrl: 'assets/reports_images/ar.png',
      applicationId: 50,
      moduleName: 'Finance',
      isPinned: false,
    },
    {
      label: 'Sales Report',
      path: 'sales',
      imageUrl: 'assets/reports_images/sales_report.png',
      applicationId: 49,
      moduleName: 'Finance',
      isPinned: false,
    },
    {
      label: 'OBV Report',
      path: 'obv',
      imageUrl: 'assets/reports_images/opportunities.png',
      applicationId: 51,
      moduleName: 'Sales',
      isPinned: false,
    },
    {
      label: 'Leads Gov.',
      path: 'leadsGovernanceReport',
      imageUrl: 'assets/reports_images/sales_governance.png',
      applicationId: 75,
      moduleName: 'Management',
      isPinned: false,
    },
    {
      label: 'Opportunity Gov.(LG)',
      path: 'opportunityGovernanceReport',
      imageUrl: 'assets/reports_images/sales_governance.png',
      applicationId: 81,
      moduleName: 'Management',
      isPinned: false,
    },
    {
      label: 'Book and Bill',
      path: 'book_and_bill',
      imageUrl: 'assets/reports_images/sales_governance.png',
      applicationId: 86,
      moduleName: 'Management',
      isPinned: false,
    },
    {
      label: 'Bid Manager',
      path: 'bidManager',
      imageUrl: 'assets/reports_images/sales_governance.png',
      applicationId: 66,
      moduleName: 'Management',
      isPinned: false,
    },
    {
      label: 'Payroll report',
      path: 'timesheet-payroll',
      imageUrl: 'assets/reports_images/timesheet.png',
      applicationId: 52,
      moduleName: 'Human Resource',
      isPinned: false,
    },
    {
      label: 'Payroll Statistics',
      path: 'timesheet-stats',
      imageUrl: 'assets/reports_images/time_stats.png',
      applicationId: 64,
      moduleName: 'Human Resource',
      isPinned: false,
    },
    {
      label: 'DFR Report',
      path: 'dfrReport',
      imageUrl: 'assets/reports_images/dfr.png',
      applicationId: 67,
      moduleName: 'Management',
      isPinned: false,
    },
    {
      label: 'Project Report',
      path: 'projectReport',
      imageUrl: 'assets/reports_images/project_report.png',
      applicationId: 69,
      moduleName: 'Finance',
      isPinned: false,
    },
    // {
    //   label: "Expense Report", path: "expense-interactive",
    //   imageUrl: "assets/reports_images/project_report.png",
    //   applicationId: 76,
    //   moduleName: "Management"
    // },
    {
      label: 'Marketing Gov.',
      path: 'marketingGovernance',
      imageUrl: 'assets/reports_images/project_report.png',
      applicationId: 74,
      moduleName: 'Marketing',
      isPinned: false,
    },
    {
      label: 'Practice MIS',
      path: 'practice-mis',
      imageUrl: 'assets/reports_images/mis.png',
      applicationId: 88,
      moduleName: 'Management',
      isPinned: false,
    },
    {
      label: 'WFH Report',
      path: 'wfh-report',
      imageUrl: 'assets/reports_images/time_stats.png',
      applicationId: 93,
      moduleName: 'WFH',
      isPinned: false,
    },
    {
      label: 'CTA Report',
      path: 'cta-report-new',
      imageUrl: 'assets/reports_images/mis.png',
      applicationId: 94,
      moduleName: 'Management',
      isPinned: false,
    },
    {
      label: 'Timesheet Statistics',
      path: 'timesheet-stats-new',
      imageUrl: 'assets/reports_images/time_stats.png',
      applicationId: 64,
      moduleName: 'Human Resource',
      isPinned: false,
    },
    {
      label: 'Expense',
      path: 'expense-report',
      imageUrl: 'assets/reports_images/time_stats.png',
      applicationId: 76,
      moduleName: 'Expense',
      isPinned: false,
    },
    {
      label: 'Opportunity Gov.',
      path: 'salesGovernanceReportNew',
      imageUrl: 'assets/reports_images/sales_governance.png',
      applicationId: 72,
      moduleName: 'Management',
      isPinned: false,
    },
    // {
    //   label: "Sales Gov.", path: "salesGovernanceReport",
    //   imageUrl: "assets/reports_images/sales_governance.png",
    //   applicationId: 72,
    //   moduleName: "Management",
    //   isPinned: false
    // },
    {
      label: 'Practice Gov.',
      path: 'practiceGovNew',
      imageUrl: 'assets/reports_images/activity_gov.png',
      applicationId: 70,
      moduleName: 'Management',
      isPinned: false,
    },
    {
      label: 'Appraisal',
      path: 'appraisal-report',
      imageUrl: 'assets/reports_images/activity_gov.png',
      applicationId: 92,
      moduleName: 'Management',
      isPinned: false,
    },

    {
      label: 'AR UBR WC Trend',
      path: 'arubrwctrend-report',
      imageUrl: 'assets/reports_images/time_stats.png',
      applicationId: 105,
      moduleName: 'Management',
      isPinned: false,
    },
    {
      label: 'SG&A Report',
      path: 'sga-report',
      imageUrl: 'assets/reports_images/time_stats.png',
      applicationId: 106,
      moduleName: 'Management',
      isPinned: false,
    },
    {
      label: 'P&L MIS Difference',
      path: 'plMisDifference-report',
      imageUrl: 'assets/reports_images/time_stats.png',
      applicationId: 107,
    },
    {
      label: 'AMS Report',
      path: 'ams-report',
      imageUrl: 'assets/reports_images/activity_gov.png',
      applicationId: 108,
      moduleName: 'Management',
      isPinned: false,
    },
    {
      label: 'AMS Report (BETA)',
      path: 'ams-report-new',
      imageUrl: 'assets/reports_images/activity_gov.png',
      applicationId: 149,
      moduleName: 'Management',
      isPinned: false,
    },
    {
      label: 'T&M Profitability',
      path: 'tm-profitability-report',
      imageUrl: 'assets/reports_images/activity_gov.png',
      applicationId: 109,
      moduleName: 'Management',
      isPinned: false,
    },
    {
      label: 'Project Gov. Report',
      path: 'projectGovernanceReport',
      imageUrl: 'assets/reports_images/project_report.png',
      applicationId: 151,
      moduleName: 'Management',
      isPinned: false,
    },
    // {
    //   label: "OKR Report (BETA)", path: "okrReport",
    //   imageUrl: "assets/reports_images/project_report.png",
    //   applicationId: 160,
    //   moduleName: "Management",
    //   isPinned: false
    // },
    {
      label: 'ISA Data Upload',
      path: 'isa-data-upload-report',
      imageUrl: 'assets/reports_images/project_report.png',
      applicationId: 208,
      moduleName: 'Management',
      isPinned: false,
    },
    {
      label: 'Win Loss Report',
      path: 'winLossReport',
      imageUrl: 'assets/reports_images/project_report.png',
      applicationId: 216,
      moduleName: 'Management',
      isPinned: false,
    },
    {
      label: 'Book and Bill (BETA)',
      path: 'book_and_bill_beta',
      imageUrl: 'assets/reports_images/sales_governance.png',
      applicationId: 213,
      moduleName: 'Management',
      isPinned: false,
    },
    {
      label: 'TS Testcase Report',
      path: 'timesheet_testcase_report',
      imageUrl: 'assets/reports_images/time_stats.png',
      applicationId: 219,
      moduleName: 'Human Resource',
      isPinned: false,
    },
    {
      label: 'Learning Report',
      path: 'lmsUserCertificationsReport',
      imageUrl: 'assets/reports_images/mis.png',
      applicationId: 239,
      moduleName: 'Human Resource',
      isPinned: false,
    },
    {
      label: 'Rewards',
      path: 'awards-report',
      imageUrl: 'assets/reports_images/activity_gov.png',
      applicationId: 101,
      moduleName: 'Management',
      isPinned: false,
    },
    {
      label: 'Contrary Book & Bill',
      path: 'contrary_book_bill',
      imageUrl: 'assets/reports_images/sales_governance.png',
      applicationId: 242,
      moduleName: 'Management',
      isPinned: false,
    },
    {
      label: 'PMO Dashoboard',
      path: 'pmo-dashboard',
      imageUrl: 'assets/reports_images/sales_governance.png',
      applicationId: 260, //260,
      moduleName: 'Report',
      isPinned: false,
    },
    {
      label: 'Bug Report',
      path: 'bugReport',
      imageUrl: 'assets/reports_images/mis.png',
      applicationId: 257,
      moduleName: 'Reports',
      isPinned: false,
    },
    {
      label: 'Task Report',
      path: 'taskReport',
      imageUrl: 'assets/reports_images/mis.png',
      applicationId: 258,
      moduleName: 'Reports',
      isPinned: false,
    },
    {
      label: 'Test Case Report',
      path: 'testCaseReport',
      imageUrl: 'assets/reports_images/mis.png',
      applicationId: 259,
      moduleName: 'Reports',
      isPinned: false,
    },
    {
      label: 'PBI Report',
      path: 'pbiReport',
      imageUrl: 'assets/reports_images/mis.png',
      applicationId: 261,
      moduleName: 'Reports',
      isPinned: false,
    },
    {
      label: 'Project Gantt Report',
      path: 'pgReport',
      imageUrl: 'assets/reports_images/mis.png',
      applicationId: 273,
      moduleName: 'Reports',
      isPinned: false,
    },
    {
      label: 'Onboarding',
      path: 'onboarding-report',
      imageUrl: 'assets/reports_images/time_stats.png',
      applicationId: 255,
      moduleName: 'RMG',
      isPinned: false,
    },
    {
      label: 'Income Statement',
      path: 'income_statement',
      imageUrl: 'assets/reports_images/sales_governance.png',
      applicationId: 256,
      moduleName: 'Management',
      isPinned: false,
    },
    {
      label: 'Management Budget',
      path: 'management_budget',
      imageUrl: 'assets/reports_images/management.png',
      applicationId: 271,
      moduleName: 'Management',
      isPinned: false,
    },
    {
      label: 'Product Bug Report',
      path: 'productBugReport',
      imageUrl: 'assets/reports_images/mis.png',
      applicationId: 285,
      moduleName: 'Reports',
      isPinned: false,
    },
    {
      label: 'Product Task Report',
      path: 'productTaskReport',
      imageUrl: 'assets/reports_images/mis.png',
      applicationId: 286,
      moduleName: 'Reports',
      isPinned: false,
    },
    {
      label: 'MIS Functions',
      path: 'mis_functions',
      imageUrl: 'assets/reports_images/management.png',
      applicationId: 283,
      moduleName: 'Management',
      isPinned: false,
    },
    {
      label: 'Effort Report',
      path: 'lcdp-effort-report',
      imageUrl: 'assets/reports_images/management.png',
      applicationId: 284,
      moduleName: 'Management',
      isPinned: false,
    },
    {
      label: 'Product Gantt',
      path: 'productGanttReport',
      imageUrl: 'assets/reports_images/mis.png',
      applicationId: 288,
      moduleName: 'Reports',
      isPinned: false,
    },
    {
      label: 'Governance Project',
      path: 'goverance_projects',
      imageUrl: 'assets/reports_images/project_report.png',
      applicationId: 300,
      moduleName: 'Reports',
      isPinned: false,
    },
    {
      label: 'Country Level MIS',
      path: 'pl-country-level-mis-report',
      imageUrl: 'assets/reports_images/management.png',
      applicationId: 289,
      moduleName: 'Management',
      isPinned: false,
    },
    {
      label: 'Org Head Count',
      path: 'org-head-count-report',
      imageUrl: 'assets/reports_images/management.png',
      applicationId: 312,
      moduleName: 'Management',
      isPinned: false,
    },
    {
      label: 'Sales & Purchase',
      path: 'sales-purchase-report',
      imageUrl: 'assets/reports_images/management.png',
      applicationId: 322,
      moduleName: 'Reports',
      isPinned: false,
    },
    {
      label: 'Salary Statement',
      path: 'salary-statement',
      imageUrl: 'assets/reports_images/management.png',
      applicationId: 319,
      moduleName: 'Management',
    },
    {
      label: 'RFID Report',
      path: 'rfidReport',
      imageUrl: 'assets/reports_images/time_stats.png',
      applicationId: 326,
      moduleName: 'Reports',
      isPinned: false,
    },
    {
      label: 'Product PBI Report',
      path: 'productUserStoryReport',
      imageUrl: 'assets/reports_images/mis.png',
      applicationId: 331,
      moduleName: 'Reports',
      isPinned: false,
    },
    {
      label: 'Product Velocity',
      path: 'productVelocityReport',
      imageUrl: 'assets/reports_images/mis.png',
      applicationId: 335,
      moduleName: 'Reports',
      isPinned: false,
    },
    {
      label: 'Product Burndown',
      path: 'productBurndownReport',
      imageUrl: 'assets/reports_images/mis.png',
      applicationId: 337,
      moduleName: 'Reports',
      isPinned: false,
    },
    {
      label: 'Sales dashboard',
      path: 'sales-dashboard',
      imageUrl: 'assets/reports_images/management.png',
      applicationId: 334,
      moduleName: 'Reports',
      isPinned: false,
    },
    {
      label: 'HR dashboard',
      path: 'hr-dashboard',
      imageUrl: 'assets/reports_images/management.png',
      applicationId: 349,
      moduleName: 'Reports',
      isPinned: false,
    },
    {
      label: 'Contract Tracker',
      path: 'contractTracker',
      imageUrl: 'assets/reports_images/management.png',
      applicationId: 347,
      moduleName: 'Reports',
      isPinned: false,
    },
    {
      label: 'TS Notification',
      path: 'timesheetNotification',
      imageUrl: 'assets/reports_images/management.png',
      applicationId: 370,
      moduleName: 'Reports',
      isPinned: false,
    },
    {
      label: 'RFID FILO Report',
      path: 'rfidfiloReport',
      imageUrl: 'assets/reports_images/time_stats.png',
      applicationId: 360,
      moduleName: 'Reports',
      isPinned: false,
    },
    {
      label: 'PMS Annual Cycle',
      path: 'pmsAnnualCycle',
      imageUrl: 'assets/reports_images/time_stats.png',
      applicationId: 92,
      moduleName: 'Reports',
    },
    {
      label: 'Product Test Case',
      path: 'productTestCaseReport',
      imageUrl: 'assets/reports_images/mis.png',
      applicationId: 408,
      moduleName: 'Reports',
      isPinned: false,
    },
    {
      label: 'RMG Governance',
      path: 'rmg-governance',
      imageUrl: 'assets/reports_images/time_stats.png',
      applicationId: 348,
      moduleName: 'Human Resource',
      isPinned: false,
    },
    {
      label: 'Project Portfolio Dashboard',
      path: 'pmo-dashboard-v1',
      imageUrl: 'assets/reports_images/sales_governance.png',
      applicationId: 372, //260,
      moduleName: 'Report',
      isPinned: false,
    },
    {
      label: 'EC Report',
      path: 'ec-report',
      imageUrl: 'assets/reports_images/time_stats.png',
      applicationId: 406,
      moduleName: 'Human Resource',
      isPinned: false,
    },
    {
      label: 'Project Dashboard',
      path: 'projectdashboard',
      imageUrl: 'assets/reports_images/time_stats.png',
      applicationId: 404,
      moduleName: 'Report',
      isPinned: false,
    },
    {
      label: 'Product Timesheet Report',
      path: 'timesheetprojectreport',
      imageUrl: 'assets/reports_images/time_stats.png',
      applicationId: 410,
      moduleName: 'Report',
      isPinned: false,
    },
    {
      label: 'TDS Report',
      path: 'tds-report',
      imageUrl: 'assets/reports_images/ar.png',
      applicationId: 458,
      moduleName: 'Reports',
      isPinned: false,
    },
    {
      label: 'Integration Exception Report',
      path: 'integration-exception-report',
      imageUrl: 'assets/reports_images/ar.png',
      applicationId: 521,
      moduleName: 'Reports',
      isPinned: false,
    },
    {
      label: 'Integration Check Report',
      path: 'integration-check-report',
      imageUrl: 'assets/reports_images/ar.png',
      applicationId: 522,
      moduleName: 'Reports',
      isPinned: false,
    },
    {
      label: 'Resource Management Dashboard',
      path: 'rm-dashboard',
      imageUrl: 'assets/reports_images/ar.png',
      applicationId: 542,
      moduleName: 'Reports',
      isPinned: false,
    },
    {
      label: 'People Allocation Dashboard',
      path: 'people-allocation-dashboard',
      imageUrl: 'assets/reports_images/ar.png',
      applicationId: 542,
      moduleName: 'Reports',
      isPinned: false,
    },
    {
      label: 'Azure Integration logs Report',
      path: 'azure-logs-report',
      imageUrl: 'assets/reports_images/ar.png',
      applicationId: 525,
      moduleName: 'Reports',
      isPinned: false,
    },
    {
      label: 'Notification Logs Report',
      path: 'notification-logs-report',
      imageUrl: 'assets/reports_images/ar.png',
      applicationId: 526,
      moduleName: 'Reports',
      isPinned: false,
    },
    {
      label: 'Error Logs Report',
      path: 'error-logging-report',
      imageUrl: 'assets/reports_images/ar.png',
      applicationId: 543,
      moduleName: 'Reports',
      isPinned: false,
    },
    {
      label: 'Project ISA Report',
      path: 'internal-stakeholders-report',
      imageUrl:
        'https://assets.kebs.app/reports_images/undraw_project_team_lc5a.png',
      applicationId: 562,
      moduleName: 'Reports',
      isPinned: false,
    },
    {
      label: 'UBR Report',
      path: 'ubr-report',
      imageUrl: 'assets/reports_images/ar.png',
      applicationId: 556,
      moduleName: 'Reports',
      isPinned: false,
    },
    {
      label: 'Timesheet Dashboard',
      path: 'timesheet-dashboard',
      imageUrl: 'assets/reports_images/time_stats.png',
      applicationId: 565,
      moduleName: 'Reports',
      isPinned: false,
    },
    {
      label: 'Timesheet Report',
      path: 'timesheet-v2-report',
      imageUrl: 'assets/reports_images/time_stats.png',
      applicationId: 567,
      moduleName: 'Reports',
      isPinned: false,
    },
    {
      label: 'Timesheet Stats Report',
      path: 'timesheet-v2-stats-report',
      imageUrl: 'assets/reports_images/time_stats.png',
      applicationId: 569,
      moduleName: 'Reports',
      isPinned: false,
    },
    {
      label: 'Leave Report(M)',
      path: 'employee-Leave-report',
      imageUrl: 'https://assets.kebs.app/reports_images/timesheet.png',
      applicationId: 566,
      moduleName: 'Reports',
      isPinned: false,
    },
    {
      label: 'AR History Report',
      path: 'ar-history-report',
      imageUrl: 'assets/reports_images/ar.png',
      applicationId: 563,
      moduleName: 'Reports',
      isPinned: false,
    },
    {
      label: 'Timesheet Query Report',
      path: 'ts-query-report',
      imageUrl: 'https://assets.kebs.app/reports_images/timesheet.png',
      applicationId: 625,
      moduleName: 'Reports',
      isPinned: false,
    },
    {
      label: 'Leave Report(BU)',
      path: 'employee-Leave-report-Sub-Division',
      imageUrl: 'https://assets.kebs.app/reports_images/timesheet.png',
      applicationId: 568,
      moduleName: 'Reports',
      isPinned: false,
    },
    {
      label: "People Allocation Gantt",
      path: "people-allocation-gantt",
      imageUrl: "https://assets.kebs.app/images/rmg-report-gantt.png",
      applicationId: 623,
      moduleName: "Reports",
      isPinned: false
    },
    {
      label: "Project Tracker", path: "projectTrackerReport",
      imageUrl: "https://assets.kebs.app/images/rmg-report-gantt.png",
      applicationId: 626,
      moduleName: "Reports",
      isPinned: false
    },
    {
      label: "Finance Dashboard",
      path: "findash",
      imageUrl: "https://assets.kebs.app/images/rmg-report-gantt.png",
      applicationId: 952,
      moduleName: "Reports",
      isPinned: false
    },
    {

      label: 'Product Dashboard',
      path: 'productDashboard',
      imageUrl: 'assets/reports_images/time_stats.png',
      applicationId: 953,
      moduleName: "Reports",
      isPinned: false,
    },
    {
      label: "AP Transaction Report",
      path: "ap-transaction-report",
      imageUrl: "https://assets.kebs.app/reports_images/ar.png",
      applicationId: 955,
      moduleName: "Reports",
      isPinned: false
    },
    {
      label: "P&L (Schedule III)",
      path: "p-and-l-report",
      imageUrl: "https://assets.kebs.app/images/rmg-report-gantt.png",
      applicationId: 624,
      moduleName: "Reports",
      isPinned: false
    },
    {
      label: "Balance Sheet (Schedule III)",
      path: "balance-sheet-report",
      imageUrl: "https://assets.kebs.app/images/rmg-report-gantt.png",
      applicationId: 641,
      moduleName: "Reports",
      isPinned: false
    },
    {
      label: "Cashflow (Schedule III)",
      path: "cashflow-report",
      imageUrl: "https://assets.kebs.app/images/rmg-report-gantt.png",
      applicationId: 906,
      moduleName: "Reports",
      isPinned: false
    },
    {
      label: "Leave Error Report",
      path: "leave-error-report",
      imageUrl: "https://assets.kebs.app/reports_images/timesheet.png",
      applicationId: 640,
      isPinned: false
    },
    {
      label: "Finance Report",
      path: "finance-report",
      imageUrl: "https://assets.kebs.app/reports_images/ar.png",
      applicationId: 970,
      isPinned: false
    },
    {
      label: 'Defect Report',
      path: 'defect-report',
      imageUrl: 'assets/reports_images/time_stats.png',
      applicationId: 964,
      moduleName: "Reports",
      isPinned: false,  
    },
    {
      label: 'Expense Item',
      path: 'expenseItemReport',
      imageUrl: 'assets/reports_images/time_stats.png',
      applicationId: 959,
      moduleName: "Reports",
      isPinned: false,  
    },
    {
      label: 'Timesheet Daily Log Report - Table View',
      path: 'timesheet-daily-log-report-table-view',
      imageUrl: 'assets/reports_images/time_stats.png',
      applicationId: 571,
      moduleName: 'Reports',
      isPinned: false,
    },
    {
      label: "Attrition Dashboard", 
      path: "attrition-dashboard",
      imageUrl: 'assets/reports_images/management.png',
      applicationId: 962,
      isPinned: false
    },
    {
      label: "Compliance Dashboard", 
      path: "compliance-dashboard",
      imageUrl: 'https://assets.kebs.app/reports_images/sales_report.png',
      applicationId: 989,
      isPinned: false
    },
    {    
      label: "RR Projections", 
      path: "rr-projections",
      imageUrl: 'assets/reports_images/management.png',
      applicationId: 1001,
      isPinned: false
    },
    {
      label: 'Sub Division MIS',
      path: 'practice-mis-report',
      imageUrl: 'assets/reports_images/ar.png',
      applicationId: 973,
      moduleName: 'Reports',
      isPinned: false,
    },
    {
      label: 'Timesheet Export Report',
      path: 'timesheet-export-report',
      imageUrl: 'assets/reports_images/time_stats.png',
      applicationId: 986,
      moduleName: "Reports",
      isPinned: false,  
    },
    {
      label: 'Project Time Tracker',
      path: 'project-time-tracker',
      imageUrl: 'assets/images/time.png',
      applicationId: 1003,
      moduleName: 'Reports',
      isPinned: false,
    },
    {
      label: 'Project Header Details',
      path: 'project-header-details',
      imageUrl: 'assets/reports_images/project_report.png',
      applicationId: 1005,
      moduleName: 'Reports',
      isPinned: false,
    },
    {
      label: 'Project Header Report',
      path: 'project-header',
      imageUrl: 'assets/reports_images/project_report.png',
      applicationId: 1030,
      moduleName: 'Reports',
      isPinned: false,
    },
    {
      label: 'Project Employee Details',
      path: 'project-employee-details',
      imageUrl: 'assets/reports_images/project_report.png',
      applicationId: 1006,
      moduleName: 'Reports',
      isPinned: false,
    },
    {
      label: 'Employee Summary Details',
      path: 'employee-details',
      imageUrl: 'assets/reports_images/project_report.png',
      applicationId: 1022,
      moduleName: 'Reports',
      isPinned: false,
    },
    {
      label: "Activity Dashboard", 
      path: "activity-dashboard",
      imageUrl: 'assets/reports_images/management.png',
      applicationId: 1010,
      isPinned: false
    },
    {
      label: 'Timesheet URS Report',
      path: 'timesheet-urs-report',
      imageUrl: 'assets/reports_images/time_stats.png',
      applicationId: 985,
      moduleName: 'Reports',
      isPinned: false,
    },
    {
      label: 'People Allocation Request Status Report',
      path: 'people-allocation-status-report',
      imageUrl: 'assets/reports_images/project_report.png',
      applicationId: 1012,
      moduleName: 'Reports',
      isPinned: false,
    },
    {
      label: 'Project Efficiency Dashboard',
      path: 'sow-dashboard',
      imageUrl: 'assets/reports_images/project_report.png',
      applicationId: 1028,
      moduleName: 'Reports',
      isPinned: false,
    },
    {
      label: 'P2P Logs Report',
      path: 'p2p-logs-report',
      imageUrl: 'https://assets.kebs.app/reports_images/ar.png',
      applicationId: 1013,
      moduleName: 'Reports',
      isPinned: false,
    },
    {
      label: 'RR Reports',
      path: 'rr-reports',
      imageUrl: 'assets/reports_images/project_report.png',
      applicationId: 1018,
      moduleName: 'Reports',
      isPinned: false,
    },
    {
      label: 'Timesheet Daily Log Report - Calendar View',
      path: 'timesheet-daily-log-report',
      imageUrl: 'assets/reports_images/ar.png',
      applicationId: 600,
      moduleName: 'Reports',
      isPinned: false,
    },
    {
      label: "Projections Report", 
      path: "rr-projections-position",
      imageUrl: 'assets/reports_images/management.png',
      applicationId: 1031,
      isPinned: false
    },
    {
      label: "Quote Monthly Projection", 
      path: "quote-monthly-projection",
      imageUrl: 'assets/reports_images/management.png',
      applicationId: 2501,
      isPinned: false
    },
    {
    label: 'Sales Report',
    path: 'sales-report',
    imageUrl: 'assets/reports_images/sales_report.png',
    applicationId: 1047,
    moduleName: 'Finance',
    isPinned: false,
  },
  {
    label: "Soft & Hard Backlog Projection", 
    path: "sbhb-projection",
    imageUrl: 'assets/reports_images/management.png',
    applicationId: 1033,
    isPinned: false,
    isForDownload: true
  },
  {
    label: "Soft & Hard Backlog Projection", 
    path: "sbhb-projection",
    imageUrl: 'assets/reports_images/management.png',
    applicationId: 1033,
    isPinned: false,
    isForDownload: true
  },
  {
    label: "Project Milestone Report", 
    path: "project-milestone-report",
    imageUrl: 'assets/reports_images/management.png',
    applicationId: 3001,
    isPinned: false,
    isForDownload: true
  },
  {
    label: "Project Header Report", 
    path: "project-header-report-details",
    imageUrl: 'assets/reports_images/management.png',
    applicationId:29388,
    isPinned: false,
    isForDownload: true
  },
  {
    label: "Schedule III Funcs.",
    path: "schedule3-admin-functions",
    imageUrl: "https://assets.kebs.app/images/rmg-report-gantt.png",
    applicationId: 631,
    moduleName: "Reports",
    isPinned: false
  },
  {
    label: "ZohoBooks GL Exception Report", 
    path: "ils-zb-exception-report",
    imageUrl: 'https://assets.kebs.app/reports_images/activity_gov.png',
    applicationId: 1419,
    isPinned: false
  },
  {
    label: "ILS Admin Reports",
    path: "ils-admin-reports",
    imageUrl: 'https://assets.kebs.app/reports_images/activity_gov.png',
    applicationId: 1420,
    isPinned: false
  },
  {
    label: "ZohoBooks Sync Log",
    path: "zb-reports-sync-logs",
    imageUrl: 'https://assets.kebs.app/reports_images/activity_gov.png',
    applicationId: 1421,
    isPinned: false
  },
  {
    label: "ZohoBooks GL Exception Report Log",
    path: "zb-exception-report-logs",
    imageUrl: 'https://assets.kebs.app/reports_images/activity_gov.png',
    applicationId: 1422,
    isPinned: false
  },
  {
    label: 'Utilization Report',
    path: 'report-dashboard',
    imageUrl: 'assets/reports_images/project_report.png',
    applicationId: 1070,
    moduleName: 'Reports',
    isPinned: false,
  },
  {
    label: 'Revenue Report',
    path: 'revenue-report',
    imageUrl: 'assets/reports_images/management.png',
    applicationId: 3005,
    moduleName: 'Reports',
    isPinned: false,
  },
  {
    label: 'UBR Report (New)',
    path: 'ubr-report-new',
    imageUrl: 'assets/reports_images/time_stats.png',
    applicationId: 972,
    moduleName: "Reports",
    isPinned: false,
  },
  {
    label: 'UBR Posting Report',
    path: 'ubr-posting',
    imageUrl: 'assets/reports_images/time_stats.png',
    applicationId: 971,
    moduleName: "Reports",
    isPinned: false
  },
  {
    label: 'Revenue Forecast',
    path: 'revenue-forecast',
    imageUrl: 'assets/reports_images/time_stats.png',
    applicationId: 3011,
    moduleName: "Reports",
    isPinned: false
  },
  {
    label: "Intercompany Reports",
    path: "intercompany-reports",
    imageUrl: 'https://assets.kebs.app/reports_images/activity_gov.png',
    applicationId: 2003,
    moduleName: "Reports",
    isPinned: false
  },
  {
    label: 'Billing Tracker Report',
    path: 'billing-tracker-report',
    imageUrl: 'assets/reports_images/project_report.png',
    applicationId: 3002,
    moduleName: 'Reports',
  },
  {
    label: "GreyT KEBS Integration Leave Report",
    path: "greyt-leave-report",
    imageUrl: 'https://assets.kebs.app/reports_images/timesheet.png',
    applicationId: 9012,
    isPinned: false
  },
  {
    label: "QuickBooks Sync Log",
    path: "qb-reports-sync-logs",
    imageUrl: 'https://assets.kebs.app/reports_images/activity_gov.png',
    applicationId: 1424,
    isPinned: false
  },
  {
    label: "QuickBooks GL Exception Report", 
    path: "ils-qb-exception-report",
    imageUrl: 'https://assets.kebs.app/reports_images/activity_gov.png',
    applicationId: 1425,
    isPinned: false
  },
  {
    label: "QuickBooks GL Exception Report Log",
    path: "qb-exception-report-logs",
    imageUrl: 'https://assets.kebs.app/reports_images/activity_gov.png',
    applicationId: 1426,
    isPinned: false
  },
  {
    label: "MIS Cockpit",
    path: "mis-cockpit",
    imageUrl: "https://assets.kebs.app/images/rmg-report-gantt.png",
    applicationId: 630,
    moduleName: "Reports",
    isPinned: false
  },
  {
    label: 'Employee level AP report',
    path: 'employee-level-ap-report',
    imageUrl: 'assets/reports_images/time_stats.png',
    applicationId: 3007,
    moduleName: "Reports",
    isPinned: false
  },
  {
    label: 'AR Aging Report',
    path: 'ar-aging',
    imageUrl: 'assets/reports_images/sales_report.png',
    applicationId: 3010,
    moduleName: 'Finance',
    isPinned: false,
  },
  {
    label: 'Invoice Details Report',
    path: 'invoice-details',
    imageUrl: 'assets/reports_images/sales_report.png',
    applicationId: 3015,
    moduleName: 'Finance',
    isPinned: false,
  },
  {
    label: 'AR Aging Report',
    path: 'ar-aging-pivot',
    imageUrl: 'assets/reports_images/sales_report.png',
    applicationId: 3021,
    moduleName: 'Finance',
    isPinned: false,
  },
  {
    label: 'Allocation Status Report',
    path: 'employee-allocation-status-report',
    imageUrl: 'assets/reports_images/management.png',
    applicationId: 4001,
    moduleName: 'People Allocation',
    isPinned: false,
  },
  {
    label: 'Employee Soft Booking Report',
    path: 'soft-booking-employee-report',
    imageUrl: 'assets/reports_images/management.png',
    applicationId: 4002,
    moduleName: 'People Allocation',
  },
  {
    label: 'Project Billing Plan Report',
    path: 'project-billing-plan',
    imageUrl: 'assets/reports_images/sales_report.png',
    applicationId:90013,
    moduleName: 'Reports',
    isPinned: false,
  },
  {
    label: 'AD - ED Exception Report',
    path: 'ils-ad-ed-exception-report',
    imageUrl: 'https://assets.kebs.app/reports_images/activity_gov.png',
    applicationId: 1431,
    moduleName: 'Reports',
    isPinned: false,
  },
  {
    label: 'AD - ED Exception Logs',
    path: 'ils-ad-ed-exception-report-logs',
    imageUrl: 'https://assets.kebs.app/reports_images/activity_gov.png',
    applicationId: 1432,
    moduleName: 'Reports',
    isPinned: false,
  },
  {
    label: 'People Allocation Dashboard',
    path: 'people-allocation-dashboard-v2',
    imageUrl: 'assets/reports_images/sales_report.png',
    applicationId: 4003,
    moduleName: 'People Allocation',
    isPinned: false,
  },
  {
    label: 'Revenue Movement Report',
    path: 'revenue-movement',
    imageUrl: 'assets/reports_images/sales_report.png',
    applicationId: 3031,
    moduleName: 'Finance',
    isPinned: false,
  },
  {
    label: "Integration Trace Logs",
    path: "integration-trace-report",
    imageUrl: 'https://assets.kebs.app/reports_images/activity_gov.png',
    applicationId: 1429,
    isPinned: false
  },
  {
    label: 'Ap Aging Report',
    path: 'ap-aging',
    imageUrl: 'assets/reports_images/sales_report.png',
    applicationId: 5002,
    moduleName: 'Finance'
  },
  {
    label: 'Country Level MIS V1.2',
  path: 'country-level-mis-pivot',
  imageUrl: 'assets/reports_images/sales_report.png',
  applicationId: 5003,
  moduleName: 'Finance'
  },
  {
    label: 'Project Allocation Report',
    path: 'project-allocation-report',
    imageUrl: 'assets/reports_images/management.png',
    applicationId: 3003,
    moduleName: 'Projects',
    isPinned: false,
  },
  {
    label: 'Claim Summary Report',
        path: 'claim-summary-report',
        imageUrl: 'assets/reports_images/sales_report.png',
        applicationId: 5001,
        moduleName: 'Finance',
        isPinned: false,
        isForDownload: true
      },
      {
        label: 'Claim details report',
        path: 'claim-details-report',
        imageUrl: 'assets/reports_images/sales_report.png',
        applicationId: 5000,
        moduleName: 'Finance',
        isPinned: false,
        isForDownload: true
      },
    {
    label: 'Resource Demand Report',
    path: 'resource-demand-report',
    imageUrl: 'assets/reports_images/management.png',
    applicationId: 8223,
    moduleName: 'People Allocation',
    isPinned: false,
  }, 
  {
    label: 'Invoice Template Portal',
    path: 'invoice-template',
    imageUrl: 'assets/reports_images/sales_report.png',
    applicationId: 3033,
    moduleName: 'Finance',
    isPinned: false,
  },
  {
    label: 'Bench Aging Report',
    path: 'unassigned-aging-report',
    imageUrl: 'assets/reports_images/management.png',
    applicationId: 8224,
    moduleName: 'People Allocation',
    isPinned: false,
  },
  {
    label: 'Project Revenue Report',
    path: 'project-revenue-report',
    imageUrl: 'assets/reports_images/sales_report.png',
    applicationId:90014,
    moduleName: 'Reports',
    isPinned: false,
  },
  {
    label: 'Employee Capacity Report',
    path: 'employee-capacity-report',
    imageUrl: 'assets/reports_images/sales_report.png',
    applicationId: 8222,
  },
  {
    label: 'MIS Custom Report',
    path: 'mis-custom-report',
    imageUrl: 'assets/reports_images/time_stats.png',
    applicationId:6664,
    moduleName: 'Reports',
    isPinned: false,
  },
  {
    label: 'Project Demobilization Report',
    path: 'project-demobilization-report',
    imageUrl: 'assets/reports_images/management.png',
    applicationId: 90118,
    moduleName: 'Reports',
    isPinned: false,
  },
  {
    label: 'Zifo Allocation Custom Report - 1',
    path: 'custom-financial-report',
    imageUrl: 'assets/reports_images/management.png',
    applicationId: 8324,
    moduleName: 'Reports',
    isPinned: false,
  },
  {    
    label: "Project Milestone Report", 
    path: "project-milestone-report-v2",
    imageUrl: 'assets/reports_images/management.png',
    applicationId: 4444,
    isPinned: false,
    isForDownload: true
  },
  {
    label: "Project Header Report", 
    path: "project-header-report-details-v2",
    imageUrl: 'assets/reports_images/management.png',
    applicationId:4445,
    isPinned: false,
    isForDownload: true
  },
  {
    label: 'Employee Activity Report',
    path: 'employee-activity-report',
    imageUrl: 'assets/reports_images/management.png',
    applicationId: 90120,
    moduleName: 'Reports',
    isPinned: false,
  },
  {
    label: "Billing Advice Report - T&M/FC",
    path: "billing-advice-report",
    imageUrl: 'assets/reports_images/management.png',
    applicationId: 8000,
    moduleName: 'Reports',
    isPinned: false,
  },
  {
    label: 'Employee Pop Up Report',
    path: 'employee-pop-up',
    imageUrl: 'assets/reports_images/management.png',
    applicationId: 8330,
    moduleName: 'Employee Pop Up Report',
    isPinned: false,
  },{
    label: 'Allocation Efficiency Report',
    path: 'allocation-efficiency-report',
    imageUrl: 'assets/reports_images/management.png',
    applicationId: 3004,
    moduleName: 'Projects',
  },
  {
    label: 'RFID Attendance Report',
    path: 'rfid-attendance-report',
    imageUrl: 'assets/reports_images/time_stats.png',
    applicationId:6665,
    moduleName: 'Reports',
    isPinned: false,
  },
  {
    label: 'Leave Report',
    path: 'leave-report',
    imageUrl: 'assets/reports_images/time_stats.png',
    applicationId:6666,
    moduleName: 'Reports',
    isPinned: false,
  }
  ];

  pinnedReports = [];

  unpinnedReports = [];

  departmentViewReports = [];

  viewByDepartmentFlag = false;

  isSearchingReports = false;

  isReportsMainPage = true;

  currentActiveReport = '';

  protected _onDestroy = new Subject<void>();

  constructor(
    private _reportsService: ReportsService,
    private _router: Router,
    private rolesService: RolesService,
    private _auth: LoginService,
    private _util: UtilityService,
    private errorService: ErrorService,
    private _help: KebsHelpService,
    private _quoteService: QuoteMainService,
    private exceltoJson: JsonToExcelService,
    private _toaster: ToasterService
  ) { }

  currentUser: any = {};

  pinnedReportIds = [];

  searchingReports = [];

  searchReportsText = '';

  isLoadingReports = false;

  async ngOnInit(): Promise<void> {
    this.currentUser = this._auth.getProfile().profile;

    let departmentViewReports = [];

    this.isLoadingReports = true;

    await this.getLCDPDashboardsInReports();

    this._reportsService
      .getPinnedReports(this.currentUser.oid)
      .pipe(takeUntil(this._onDestroy))
      .subscribe(
        async (res) => {
          this.isLoadingReports = false;

          if (res['data'] && res['data'].length > 0)
            this.pinnedReportIds = res['data'];

          for (let allReportsItem of this.allReports)
            if (
              this.rolesService.isApplicationVisibleForUser(
                allReportsItem.applicationId
              )
            ) {
              if (
                _.contains(this.pinnedReportIds, allReportsItem.applicationId)
              ) {
                allReportsItem.isPinned = true;
                this.pinnedReports.push(allReportsItem);
              } else this.unpinnedReports.push(allReportsItem);

              departmentViewReports.push(allReportsItem);
            }

          let uniqueDepartments = _.uniq(
            _.pluck(departmentViewReports, 'moduleName')
          );

          for (let uniqueDepartmentsItem of uniqueDepartments) {
            let uniqueDepartmentsData = _.where(departmentViewReports, {
              moduleName: uniqueDepartmentsItem,
            });

            this.departmentViewReports.push({
              departmentName: uniqueDepartmentsItem,
              reports: uniqueDepartmentsData,
            });
          }
        },
        (err) => {
          console.log(err);
          this.errorService.userErrorAlert(
            err && err.code
              ? err.code
              : err && err.error
                ? err.error.code
                : 'NIL',
            'Error while retrieving Master list',
            err && err.params
              ? err.params
              : err && err.error
                ? err.error.params
                : {}
          );
        }
      );
  }

  async getLCDPDashboardsInReports() {
    return new Promise((resolve, reject) => {
      this._reportsService
        .getLCDPDashboardsInReports()
        .pipe(takeUntil(this._onDestroy))
        .subscribe(
          async (res) => {
            if (res['data'] && res['data'].length > 0) {
              this.allReports = this.allReports.concat(res['data']);
            }
            resolve(true);
          },
          (err) => {
            console.log(err);
            this.errorService.userErrorAlert(
              err && err.code
                ? err.code
                : err && err.error
                  ? err.error.code
                  : 'NIL',
              'Error while retrieving LCDP Dashboards list',
              err && err.params
                ? err.params
                : err && err.error
                  ? err.error.params
                  : {}
            );
            resolve(true);
          }
        );
    });
  }

  routeToReport(report) {
    if (report.isLCDPApplication) {
      this.currentActiveReport = report.label;
      this._router.navigateByUrl(
        '/main/dash/builder/' + report.lcdpApplicationId + '/1/listDash'
      );
      this.isReportsMainPage = false;
    }

    else if (report.applicationId == 1033 && report.isForDownload)
      this.downloadSBHBReport(report);
    
    else {
      this.currentActiveReport = report.label;
      this._router.navigateByUrl('/main/reports/' + report.path);
      this.isReportsMainPage = false;
    }
  }

  backToReportsMainPage() {
    this._router.navigateByUrl('/main/reports');
    this.isReportsMainPage = true;
  }

  viewByDepartment(viewByDepartmentFlag) {
    this.viewByDepartmentFlag = viewByDepartmentFlag;

    this.isSearchingReports = false;
  }

  // openOpportunityInNewTab(opportunityId) {
  //   let navigationUrl =
  //     window.location.origin +
  //     "/main/opportunities/" +
  //     opportunityId +
  //     "/" +
  //     this.encodeURIComponent(this.opportunitiesData.opportunity_name);
  //   // console.log(navigationUrl)
  //   window.open(navigationUrl);
  // }

  searchReports() {
    this.searchingReports = [];

    if (!this.searchReportsText || this.searchReportsText == '')
      this.isSearchingReports = false;
    else {
      this.isSearchingReports = true;

      for (let pinnedReportsItem of this.pinnedReports)
        if (
          pinnedReportsItem.label
            .toLowerCase()
            .includes(this.searchReportsText.toLowerCase()) ||
          (pinnedReportsItem.moduleName &&
            pinnedReportsItem.moduleName
              .toLowerCase()
              .includes(this.searchReportsText.toLowerCase()))
        )
          this.searchingReports.push(pinnedReportsItem);

      for (let unpinnedReportsItem of this.unpinnedReports)
        if (
          unpinnedReportsItem.label
            .toLowerCase()
            .includes(this.searchReportsText.toLowerCase()) ||
          (unpinnedReportsItem.moduleName &&
            unpinnedReportsItem.moduleName
              .toLowerCase()
              .includes(this.searchReportsText.toLowerCase()))
        )
          this.searchingReports.push(unpinnedReportsItem);
    }
  }

  clearSearch() {
    this.searchReportsText = '';

    this.searchingReports = [];

    this.isSearchingReports = false;
  }

  clickPinReport(report) {
    report.isPinned = !report.isPinned;

    let existingReports = [];

    for (let reportsItem of this.unpinnedReports)
      existingReports.push(reportsItem);

    for (let reportsItem of this.pinnedReports)
      existingReports.push(reportsItem);

    this.pinnedReportIds = [];
    this.pinnedReports = [];
    this.unpinnedReports = [];

    for (let reportsItem of existingReports)
      if (reportsItem.isPinned) {
        this.pinnedReportIds.push(reportsItem.applicationId);
        this.pinnedReports.push(reportsItem);
      } else this.unpinnedReports.push(reportsItem);

    this._reportsService
      .updatePinnedReports(this.currentUser.oid, this.pinnedReportIds)
      .pipe(takeUntil(this._onDestroy))
      .subscribe(async (res) => {
        if (res['messText']) this._util.showMessage(res['messText'], 'dismiss');
      });
  }

  openHelpDialog() {
    let helpTopicId = 17;
    this._help.openHelpDialog(
      this._auth.getToken(),
      helpTopicId,
      this.currentUser
    );
  }

  downloadSBHBReport = (reportItem) => {

    reportItem.isDownloading = true;

    this._quoteService.getSBHBProjection()
      .pipe(takeUntil(this._onDestroy))
      .subscribe(res => {

        if (res['messType'] == "S" && res['data'] && res['data'].length) {

          const revenueData = res['data'];

          let dateFormatArr = [];

          for (const item of revenueData)
            for (const [key, value] of Object.entries(item)) {

              if (value && typeof value == 'object' && value['type'] && value['type'] == 'date') {

                const fieldExists = dateFormatArr.find(val => val['fieldKey'] == key);

                if (!fieldExists)
                  dateFormatArr.push({
                    fieldKey: key,
                    fieldFormat: "DD-MMM-YYYY"
                  });

                item[key] = value['value'] ? moment(value['value']).utc().format("YYYY/MM/DD") : null;

              }

            }

          this.exceltoJson.exportAsExcelFile(revenueData, res['fileName'], [], null, dateFormatArr);

        }

        else
          this._toaster.showError("Error", res["messText"], 2000);

        reportItem.isDownloading = false;

      },
        err => {
          reportItem.isDownloading = false;
          console.log(err);
          this._toaster.showError("Error", "Error in Downloading Revenue projection data", 2000);
        });

  }

  ngOnDestroy() { }
}
