.lable{
    font-weight: 500;color: 
    #f27a6c;font-size: 14px;
      line-height: 24px;
      letter-spacing: 0.02em;
      width: 175px;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
  }
.nonmanpowergrid {
    margin-top: 40px;
    margin-bottom: 30px;
  ::ng-deep .dx-datagrid-header-panel{
        display: flex;
        width: 20px;
        position: absolute;
        top: -60px;
        right: 30px;
  }
  ::ng-deep #events {
    background-color: rgba(191, 191, 191, 0.15);
    padding: 20px;
    margin-top: 20px;
  }

  ::ng-deep #events > div {
    padding-bottom: 5px;
  }

  ::ng-deep #events > div::after {
    content: "";
    display: table;
    clear: both;
  }

  ::ng-deep #events #clear {
    float: right;
  }

  ::ng-deep #events .caption {
    float: left;
    font-weight: bold;
    font-size: 115%;
    line-height: 115%;
    padding-top: 7px;
  }

  ::ng-deep #events ul {
    list-style: none;
    max-height: 100px;
    overflow: auto;
    margin: 0;
  }

  ::ng-deep #events ul li {
    padding: 7px 0;
    border-bottom: 1px solid #ddd;
  }

  ::ng-deep #events ul li:last-child {
    border-bottom: none;
  }

  ::ng-deep .dx-datagrid-header-panel{
    height: 0px;
  }
}


.manpowergrid {
    margin-top: 40px;
    margin-bottom: 30px;
    ::ng-deep .dx-datagrid-header-panel{
        display: flex;
        width: 20px;
        position: absolute;
        top: -60px;
        right: 30px;
    }
    ::ng-deep #events {
      background-color: rgba(191, 191, 191, 0.15);
      padding: 20px;
      margin-top: 20px;
    }
  
    ::ng-deep #events > div {
      padding-bottom: 5px;
    }
  
    ::ng-deep #events > div::after {
      content: "";
      display: table;
      clear: both;
    }
  
    ::ng-deep #events #clear {
      float: right;
    }
  
    ::ng-deep #events .caption {
      float: left;
      font-weight: bold;
      font-size: 115%;
      line-height: 115%;
      padding-top: 7px;
    }
  
    ::ng-deep #events ul {
      list-style: none;
      max-height: 100px;
      overflow: auto;
      margin: 0;
    }
  
    ::ng-deep #events ul li {
      padding: 7px 0;
      border-bottom: 1px solid #ddd;
    }
  
    ::ng-deep #events ul li:last-child {
      border-bottom: none;
    }
    ::ng-deep .dx-datagrid-header-panel{
      height: 0px;
    }
  }

  .sectionLabel {
    font-weight: 500;
    font-size: 12px;
    line-height: 24px;
    text-transform: capitalize;
    color: #45546e;
    padding-bottom: 2px;
  }
  