<div class="allocation" style="margin: 20px">
  <div style="display: flex;">
    <p class="title">Confirm Allocation</p>
    <div style="margin-top: 15px;
    margin-left: 26rem;cursor: pointer;"><mat-icon (click)="closeDialog(null)">clear</mat-icon></div>
  </div>
  <hr />
  <div *ngIf="isComponentLoading">
    <div class="row mt-3 mb-3 justify-content-center">
      <mat-spinner diameter="25" matTooltip="Loading ..."> </mat-spinner>
    </div>
  </div>
  <div *ngIf="!isComponentLoading">
    <div [formGroup]="confirmAllocationForm">
      <ng-container *ngIf="isSoftBookingEnabled && !isFormVisible">
        <span class="fieldheading" style="display: block; margin-bottom: 3px">Select {{placeHolder}}</span>
        <app-input-search class="field-project-search" formControlName="searchField" [placeholder]="'Search ' + placeHolder"
        [list]="filteredList" required="true" [hideMatLabel]="true"></app-input-search>    
  </ng-container>
  <ng-container *ngIf="isSoftBookingEnabled && isFormVisible">
    <span class="header-name">{{projectName}}</span>  
</ng-container>
    <ng-container *ngIf="isFormVisible">
    <div style="display: flex" class="profiledata">
      <div style="display: inline-block;margin-right: 30px;
      ">
        <app-user-image  [id]="detailData.employee_oid"  imgWidth="40px" imgHeight="40px"
        [hasDefaultImg]="true"></app-user-image>
      </div>
      <div style="display: inline-block;">
        <p class="profilename">{{detailData.employee_name ? detailData.employee_name : '-'}}<button *ngIf="isResumeButtonEnabled" style="margin-top: 0px" class="activityButton" (click)="onButtonClick(associate_id)">View Resume</button></p>
      <div style="display: inline-block; margin-right: 50px">
        <p style="margin-top: 0px; margin-bottom: 2px">Emp ID {{associate_id}}</p>
        <p style="margin-top: 0px; margin-bottom: 7px">{{detailData.department ? detailData.department : '-'}}</p>
      </div>
        <div style="display: inline-block">
          <p style="margin-top: 0px; margin-bottom: 2px">
            {{ detailData.employment_type ? detailData.employment_type : "-" }}
          </p>
          <p style="margin-top: 0px; margin-bottom: 7px">
            {{ detailData.work_location ? detailData.work_location : "-" }}
          </p>
        </div>
      </div>
    </div>
    <div >
      <div class="row" style="row-gap: 6px;"> 
        <div [ngClass]="{ 'col-6': !locationSpecific, 'col-4': locationSpecific }" class="p-0 m-0">
          <div class="width-class">
            <label
              class="fieldheading"
              for="expected_closure_date"
              >Start Date<span class="required-star"> &nbsp;*</span></label
            >
            <div class="dateFieldBorder" style="padding-left: 8px">
              <input
                [min]="compareDateMinimum(requestProjectDetails['planned_start_date'], doj)"
                [max]="
                  compareDate(
                    confirmAllocationForm.get('end_date').value,
                    requestProjectDetails['planned_end_date']
                  )
                "
                formControlName="start_date"
                class="fieldvalue"
                required="true"
                matInput
                [matDatepicker]="picker1"
                placeholder="DD-MMM-YYYY"
                style="border: none; outline: none"
                readonly
              />
              <mat-datepicker-toggle
                matSuffix
                [for]="picker1"
              ></mat-datepicker-toggle>
              <mat-datepicker #picker1></mat-datepicker>
            </div>
          </div>
        </div>
        <div [ngClass]="{ 'col-6': !locationSpecific, 'col-4': locationSpecific }" class="p-0 m-0">
          <div class="width-class">
            <label
              class="fieldheading"
              for="expected_closure_date"
              >End Date<span class="required-star"> &nbsp;*</span></label
            >
            <div class="dateFieldBorder" style="padding-left: 8px">
              <input
                class="fieldvalue"
                formControlName="end_date"
                required="true"
                matInput
                [matDatepicker]="picker2"
                placeholder="DD-MMM-YYYY"
                style="border: none; outline: none"
                [max]="getMinDate(requestProjectDetails['planned_end_date'], e360_end_date)"
                [min]="
                        confirmAllocationForm.get('start_date').value
                          ? confirmAllocationForm.get('start_date').value
                          : requestProjectDetails['planned_start_date']
                      "
                readonly
              />
              <mat-datepicker-toggle
                matSuffix
                [for]="picker2"
              ></mat-datepicker-toggle>
              <mat-datepicker #picker2></mat-datepicker>
            </div>
          </div>
        </div>
        <div [ngClass]="{ 'col-6': !locationSpecific, 'col-4': locationSpecific }" class="p-0 m-0">
          <p
          class="width-class"
            *ngIf="fieldConfig?.utilization_capacity?.is_active_field"
          >
            <label
              class="fieldheading"
              
              for="lastname"
              >Utilization Capacity<span *ngIf="fieldConfig?.utilization_capacity?.is_mandatory" class="required-star" > &nbsp;*</span>
            </label>
            <input
              matInput
              class="capacity"
              placeholder=""
              type="number"
              min="1"
              max="100"
              digitOnly
              (input)="preventDefaultCapacity($event)"
              formControlName="utilization_capacity"
              style="padding-left: 10px;outline: thin;border-radius: 8px;"

            />
          </p>
        </div>
        <div [ngClass]="{ 'col-6': !locationSpecific, 'col-4': locationSpecific }" class="p-0 m-0">
          <div class="width-class" *ngIf="fieldConfig?.booking_type?.is_active_field">
            <label
              class="fieldheading"
              
              for="booking_type"
              >Booking Type<span *ngIf="fieldConfig?.booking_type?.is_mandatory" class="required-star"> &nbsp;*</span></label
            >
            <mat-button-toggle-group
              #bookingtypetoggleBtn="matButtonToggleGroup"
              [disabled]="isBookingTypeDisabled(fieldConfig?.booking_type?.is_disabled)"
              (change)="BookingTypeToggle(bookingtypetoggleBtn.value)"
            >
              <mat-button-toggle
                style="height: 36px; display: flex"
                [ngClass]="{
                  changetoRed: item.booking_type === bookingTypeValue,
                  changetowhite: item.booking_type != bookingTypeValue
                }"
                *ngFor="let item of BookingTypeMaster"
                [value]="item.id"
              >
                {{ item.booking_type }}
              </mat-button-toggle>
            </mat-button-toggle-group>
          </div>
        </div>
        <div [ngClass]="{ 'col-6': !locationSpecific, 'col-4': locationSpecific }" class="p-0 m-0"  *ngIf="fieldConfig?.work_premisis?.is_allocate_active">
          <div class="width-class">
            <label
              class="fieldheading"
              
              >{{
                fieldConfig?.work_premisis?.field_label
                  ? fieldConfig?.work_premisis?.field_label
                  : "Work Premisis"
              }}<span *ngIf="fieldConfig?.work_premisis?.is_mandatory" class="required-star"> &nbsp;*</span></label
            >
            <app-input-search
            class="inputSearch"
            [required]="fieldConfig?.work_premisis?.is_mandatory"
            placeholder="Select {{
              fieldConfig?.work_premisis?.field_label
                ? fieldConfig?.work_premisis?.field_label
                : 'Work Premisis'
            }}"
            [list]="workPremisisMasterList"
            formControlName="work_premisis"
            [disableNone]="true"
            [hideMatLabel]="true"
          >
          </app-input-search>
          </div>
        </div>
        <div [ngClass]="{ 'col-6': !locationSpecific, 'col-4': locationSpecific }" class="p-0 m-0"  *ngIf="fieldConfig?.work_city?.is_allocate_active">
          <div class="width-class">
            <label
              class="fieldheading"
              >{{
                fieldConfig?.work_city?.field_label
                  ? fieldConfig?.work_city?.field_label
                  : "Work City"
              }}<span *ngIf="fieldConfig?.work_city?.is_mandatory" class="required-star"> &nbsp;*</span></label
            >
            <!-- <app-input-search-huge-input
                  class="inputSearch"
                  [label]="'Select One'"
                  [optionLabel]="['city_id','city_name']"
                  [apiUri]="
                    '/api/rmg/masterData/workCity'
                  " 
                  formControlName="work_city"
                >
            </app-input-search-huge-input> -->
            <app-single-select-chip class="single-chip"
            style="font-family:  'Plus Jakarta Sans' !important;font-size: 14px !important;
            font-weight: 400;color: #5f6c81 !important;"
            [masterData]="workCityMasterList"
            [selectedValue]="confirmAllocationForm.get('work_city').value"
            placeholder="Select {{
              fieldConfig?.work_city.field_label
                ? fieldConfig?.work_city?.field_label
                : 'Work City'
            }}"
            [displayClose]="false"
            (onValueChange)="onCustomSelectChanges($event)"
          ></app-single-select-chip>
          </div>
        </div>
      </div>
      <div class="row" *ngIf="isSoftBookingEnabled">
        <div class="col-4 p-0 m-0" *ngIf="reportOptionEnable">
          <span class="fieldheading">Project Head<span *ngIf="reportOptionEnable && reportsToMasterList.length == 0" class="required-star"> &nbsp;*</span></span>
          <mat-checkbox *ngIf="is_head;else projectHead"
          [checked]="confirmAllocationForm.value.is_head === 1"
          (change)="isHeadChange($event.checked ? 1 : 0)"
          class="is-head-name"
        >
          <span class="head_class">Is Head</span>
        </mat-checkbox>
        <ng-template #projectHead>
          <span class="report_text ellipsis-class" matTooltip="{{projectHeadName ? projectHeadName : '-'}}">{{projectHeadName ? projectHeadName : '-'}}</span>
        </ng-template>      
        </div>
        <div class="col-4 p-0 m-0" *ngIf="confirmAllocationForm.value.is_head != 1 && reportOptionEnable">
          <span class="fieldheading">Reports To<span *ngIf="reportOptionEnable" class="required-star"> &nbsp;*</span></span>
          <app-input-search style="width: 69%;"
            class="inputSearch"
            placeholder="Select Reporting Manager"
            [list]="reportsToMasterList"
            formControlName="reports_to"
            [disableNone]="true"
            [hideMatLabel]="true"
          >
          </app-input-search>
        </div>
        <div class="col-4 p-0 m-0" *ngIf="travelConfig?.is_active">
          <span class="fieldheading">{{ travelConfig?.field_label ? travelConfig?.field_label : 'Travel Type'}}<span class="required-star" *ngIf="travelConfig?.is_mandatory"> &nbsp;*</span></span>
          <app-input-search style="width: 69%;"
            class="inputSearch"
            placeholder="Select {{ travelConfig?.field_label ? travelConfig?.field_label : 'Travel Type'}}"
            [list]="travelTypeList"
            formControlName="travel_type"
            [disableNone]="true"
            [hideMatLabel]="true"
          >
          </app-input-search>
        </div>
        <div class="col-4 p-0 m-0" *ngIf="shiftConfig?.is_active">
          <span class="fieldheading">{{ shiftConfig?.field_label ? shiftConfig?.field_label : 'Shift'}}<span class="required-star" *ngIf="shiftConfig?.is_mandatory"> &nbsp;*</span></span>
          <app-input-search style="width: 69%;"
            class="inputSearch"
            placeholder="Select {{ shiftConfig?.field_label ? shiftConfig?.field_label : 'Shift'}}"
            [list]="shiftMasterList"
            formControlName="shift"
            [disableNone]="true"
            [hideMatLabel]="true"
          >
          </app-input-search>
        </div>
      </div>
      <div *ngIf="!isSoftBookingEnabled">
        <p class="fieldheading" style="margin-bottom: 0px;">Notes</p>
        <textarea
          class="blurName txtArea"
          matInput
          formControlName="notes"
          placeholder="Enter here"
        ></textarea>
      </div>
      <div style="margin-top: 10px; display: block" *ngIf="fieldConfig?.need_client_interview?.is_active_field && !isSoftBookingEnabled">
        <p style=" margin-right: 3px" class="blurName">
          Schedule Interview
        </p>
        <!-- <mat-slide-toggle [checked]="toggle" (change)="toggleChanges($event)">
        </mat-slide-toggle> -->
        <mat-radio-group
          formControlName="schedule_interview"
          class="example-radio-group"
          style="color: #5f6c81"
        >
            <mat-radio-button
              class="example-radio-button"
              [checked]="confirmAllocationForm.value.schedule_interview == 1"
              value=1
              style="margin-right: 50px"
            >
            Yes
            </mat-radio-button>
            <mat-radio-button
            class="example-radio-button"
            [checked]="confirmAllocationForm.value.schedule_interview == 0"
            value=0
            style="margin-right: 50px"
          >
          No
          </mat-radio-button>
        </mat-radio-group>
      </div>
      <ng-container *ngIf="!isSoftBookingEnabled">
      <p style="display: inline; margin-right: 3px" class="blurName">
        Acceptance Criteria
      </p>
      <div *ngIf="detailData.criteria != null && detailData.criteria != ''">
        <div
        style="margin-top: 10px"
        *ngFor="let criteria of detailData.criteria"
      >
        <div class="fieldValue" style="margin-bottom: 4px;font-weight: 400;
        font-size: 14px;
        line-height: 24px;
        letter-spacing: 0.02em;
        text-transform: capitalize;
        color: #5F6C81;
        display: flex;
    align-items: baseline;">
          <mat-checkbox (change)="onCheckChange($event, criteria)" style="font-size: 8px;"> </mat-checkbox>
          <span class="criteriaText">{{ criteria.criteria ? criteria.criteria : "-" }}</span>
        </div>
      </div>
      </div>
      <div *ngIf="detailData.criteria == null || detailData.criteria == ''">-</div>
      </ng-container>

      <p style="margin-top: 10px">
        <button
          (click)="closeDialog(null)"
          style="
            border: 1px solid;
            margin-right: 10px;
            border-radius: 4px;
            padding: 10px;
            line-height: 16px;
            background-color: white;
          "
        >
          Cancel
        </button>
        <button type="submit" class="submitButton" (click)="onSubmit()" [disabled]="isFormSubmitted"
        [ngClass]="{
          buttonOff: isFormSubmitted,
          buttonOn: !isFormSubmitted
        }"
        >
          Confirm Allocation
        </button>
      </p>
    </div>
    </ng-container>
    </div>
  </div>
</div>
