.resources {
  font-family: 'Plus Jakarta Sans';// Zifo theme
  height: 72.5vh;
  overflow-y: auto;
  overflow-x: hidden;
  .searchFieldBox{
    font-size: 12px;
    line-height: 16px;
    letter-spacing: 0.02em;
  }
  .mat-form-field-infix::placeholder { width:max-content }
  .topfields {
    display: flex;
    align-items: center;
    min-height: 45px;
    ::ng-deep .cdk-overlay-backdrop.cdk-overlay-backdrop-showing {
      opacity: 0 !important;
    }
    ::ng-deep.mat-slide-toggle.mat-checked .mat-slide-toggle-bar {
      background-color: #79BA44;
      width: 28px;
    }
    .filtericn {
      ::ng-deep.mat-icon {
        padding-top: 20px;
        font-size: 20px;
        color: #45546e;
      }
    }
    .dropdownborder {
      z-index: 1;
      width: 230px;
      border: 1px solid lightgrey;
      border-radius: 4px;
      margin-top: 35px;
      padding: 10px !important;
    }
    .tooltip {
      position: relative;
      display: inline-block;
    }

    .tooltip .tooltiptext {
      visibility: hidden;
      background-color: #ffffff;
      border-radius: 6px;
      padding: 5px;
      padding-top: 7px;
      width: 250x;
      height: 200px;
      position: absolute;
      z-index: 2;
      border-radius: 4px;
      overflow-y: scroll;
      overflow-x: hidden;
    }
    .tooltip:active .tooltiptext {
      visibility: visible;
    }
    .filterbtn {
      border: none;
      background-color: white;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .droplistvisible {
      visibility: visible !important;
    }
    .droplisthidden {
      visibility: hidden !important;
    }
    .dropdata {
      font-size: 12px;
      line-height: 16px;
      letter-spacing: 0.02em;
      text-transform: capitalize;
      color: #111434;
      width: 175px;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }
    .example-margin {
      display: flex;
      right: 10px;
      position: absolute;
    }
    .smartSuggestionToggleOn {
      margin-bottom: 10px;
      padding-top: 10px;
      display: inline-block;
      margin-left: 10px;
      letter-spacing: 0.02em;
      width: 100%;
      color: #5F6C81;
      font-family: 'Plus Jakarta Sans';
    }
    .smartSuggestionToggleOff {
      // padding-top: 1.4%;
      // display: inline-block;
      // margin-left: 10px;
      // letter-spacing: 0.02em;
      // color: #b9c0ca;
      // position: absolute;
      // right: 55px;
      // font-family: 'Plus Jakarta Sans';
      margin-bottom: 10px;
      padding-top: 10px;
      display: inline-block;
      margin-left: 10px;
      letter-spacing: 0.02em;
      width: 100%;
      color: #b9c0ca;
      font-family: 'Plus Jakarta Sans';
    }
    ::ng-deep.mat-slide-toggle.mat-checked .mat-slide-toggle-thumb {
      // background-color: white;
      // width: 10px;
      // height: 11px;
      // padding-top: 1px;
      // margin-top: 4px;
      // margin-left: 1px;
      background-color: #fff;
      width: 16px;
      height: 16px;
      padding-top: 1px;
      margin-top: 1px;
      margin-left: 1px;
    }
    ::ng-deep.mat-slide-toggle-bar {
      width: 28px;
      margin-right: 15px;
    }
    ::ng-deep.mat-slide-toggle-thumb {
      // width: 10px;
      // height: 11px;
      // margin-top: 4px;
      // margin-left: 2px;
      background-color: #fff;
      width: 17px;
      height: 17px;
      padding-top: 1px;
      margin-top: 1px;
      margin-left: -3px;
    }
    .searchField {
      display: inline-block;
      border: solid thin #dadce2;
      border-radius: 4px;
      height: 36px;
      margin-top: 8px;
      margin-right: 20px;
    }
    .searchboxes {
      display: flex;
      align-items: center;
    }
    .titlemargin {
      margin-left: 0px !important;
      margin-right: 0px !important;
    }
    .searchtitle {
      font-weight: 400;
      font-size: 12px;
      line-height: 16px;
      margin: auto 5px auto 5px;
      padding: 2px;
      padding-left: 10px;
      padding-right: 10px;
    }
    .clearonefiltericn {
      font-size: 13px;
      display: inline-flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
    }
    .dropdownfilter {
      font-size: 13px;
      display: inline-flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      margin-top: 5px;
    }
    .boxstyle {
      background-color: #f2d4cdad;
      height: 1.5rem;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      margin-right: 0px;
      color: #526179;
    }
    .filterval {
      width: 100px;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }
    .filterfield {
      width: 57px;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }
    .searchFieldtxt {
      outline: none;
      border: none;
      font-weight: 400;
      font-size: 12px;
      line-height: 16px;
    }
    .positionFieldlable {
      margin-top: 19px;
      margin-left: 4px;
      color: #a8acb2;
      font-weight: 400;
      font-size: 12px;
      line-height: 16px;
    }
    .positionField {
      display: inline-block;
      margin-left: 10px;
      padding-top: 5px;
      width: 147px;
      margin-right: 20px;
    }
    .positionFieldtxt {
      border-radius: 4px;
      height: 40px;
      width: 200px;
      border-color: #b9c0ca;
    }

    .clearbtn {
      font-weight: 400;
      font-size: 12px;
      line-height: 16px;
      border: 1px solid;
      height: 33px;
      margin-top: 10px;
      border-radius: 4px;
      border-color: #ee4961;
      background-color: #f2d4cdad;
      color: #ee4961;
    }
    .searchbox {
      padding-top: 10px !important;
      font-size: 18px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #515965;
      padding-top: 10px !important;
    }
  }

  .resourceTable {
    .overLayClass {
      opacity: 0 !important;
    }
    .namestyle {
      font-weight: 500;
      font-size: 12px;
      line-height: 16px;
      letter-spacing: 0.02em;
      text-transform: capitalize;
      color: #111434;
    }
    .allocatebtn {
      padding: 6px;
      border-radius: 4px;
      //background-color: #ee4961;
      background-color: #79ba44; // Zifo theme
      font-family: 'Plus Jakarta Sans'; // Zifo theme
      color: white;
      border: none;
      font-weight: 500;
      font-size: 12px;
      line-height: 16px;
      padding-bottom: 20px;
      display: flex;
      top: -7px;
      justify-content: center;
      padding: 4px 8px;
      width: 59px;
      height: 24px;
    }

    .profilebtn {
      padding: 6px;
      height: 24px;
      border-radius: 4px;
      background-color: white;
      font-weight: 700;
      font-size: 12px;
      line-height: 16px;
      padding-bottom: 20px;
      margin-left: 7px;
      border: solid thin;
    }
    .dataField {
      font-weight: 400;
      font-size: 12px;
      line-height: 16px;
      letter-spacing: 0.02em;
      text-transform: capitalize;
      color: #111434;
    }
    .ellipsisfield {
      width: 180px;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }
    .colField {
      text-align: start;
      font-weight: 400;
      font-size: 12px;
      line-height: 16px;
      letter-spacing: 0.02em;
      text-transform: capitalize;
      color: #a8acb2;
    }
    .colTable {
      z-index: 1;
      margin-bottom: 30px;
      margin-top: -9px;
      background-color: white;
      // width: 100%;
      display: inline-flex;
      width: max-content;
    }
    .infoOverlay {
      font-size: small;
      color: #b9c0ca;
      padding-top: 1px;
      margin-left: 5px;
    }
    .material-symbols-outlined {
      font-variation-settings: "FILL" 0, "wght" 400, "GRAD" 0, "opsz" 48;
    }
  }
  .forwardBtn {
    background-color: #79ba44; // Zifo theme
    color: white;
    font-family: 'Plus Jakarta Sans';
  }

  .smart-suggestion-area {
    padding-left: 25px;
    padding-right: 25px;
  }

  .smart-allocation-button {
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    line-height: 16px;
    color: #111434;
    cursor: pointer;
    font-size: 14px;
    padding: 7px 11px;
    border: 1px solid #FFBD3D;
    border-radius: 8px;
    background: linear-gradient(
      106.22deg,
      rgba(255, 189, 61, 0.5) 38.73%,
      rgba(255, 142, 61, 0.5) 60.01%
    );
    box-shadow: 0px 12px 16px 0px rgba(255, 189, 61, 0.2);
    margin-top: 3px;
    height: 32px;

    .icon-image {
      padding: 0px 8px 2px 0px;

      .icon-image-styles {
        height: 24px;
        width: 24px;
      }
    }
  }

}

::ng-deep .mat-slide-toggle-content {
  font-family: 'Plus Jakarta Sans' !important;
}

::ng-deep .smart-suggestion-theme {
  .shepherd-header {
    padding-right: 25px;
    padding-left: 25px;
    padding-bottom: 10px;
    background-color: white !important;
    .shepherd-title {
      margin-bottom: 0px !important;
      color: #79BA44 !important;
      font-weight: 700;
      font-family: 'Plus Jakarta Sans';
    }
  }
  .shepherd-text {
    color: #8b95a5;
    font-size: 12px;
    line-height: 16px;
    padding-right: 25px;
    padding-left: 15px;
    padding-bottom: 10px;
    padding-top: 0px;
    font-family: 'Plus Jakarta Sans';
  }
  .shepherd-footer {
    .next-button {
      background-color: #79BA44 !important;
      color: white;
    }
  }
}
