.smart-allocation {
  height: var(--dynamicOverallHeight);
  width: 100%;
  margin-right: auto;
  margin-left: auto;

  .request-header-section {
    display: flex;
    justify-content: space-between;
    margin: 55px 33px 13px 48px;

    .request-primary-details {
      margin-top: 12px;

      .primary-level-one-details {
        display: flex;
        gap: 8px;

        .request-status {
          background: #e7f9f9;
          padding: 3px 6px;
          border: 1px solid #13c2c2;
          border-radius: 4px;
          font-family: var(--fontFamily);
          font-size: 12px;
          font-weight: 500;
          line-height: 16.8px;
          letter-spacing: 0.02em;
          text-align: left;
          color: #13c2c2;
        }

        .request-number {
          font-family: var(--fontFamily);
          font-size: 14px;
          font-weight: 500;
          line-height: 16px;
          letter-spacing: 0.02em;
          text-align: left;
          color: #6e7b8f;
          margin-top: 3px;
        }

        .divider-one-line {
          border-left: 1px solid #515965;
          height: 11px;
          margin-top: 6px;
        }

        .request-context {
          display: flex;

          .context-value {
            color: #6e7b8f;
            font-family: var(--fontFamily);
            font-size: 14px;
            font-weight: 500;
            line-height: 16px;
            letter-spacing: 0.02em;
            text-align: left;
            margin: 3px 0px 0px 8px;
          }
        }

        .divider-two-line {
          border-left: 1px solid #515965;
          height: 11px;
          margin-top: 6px;
        }

        .project-role {
          display: flex;

          .project-role-value {
            color: #6e7b8f;
            font-family: var(--fontFamily);
            font-size: 14px;
            font-weight: 500;
            line-height: 16px;
            letter-spacing: 0.02em;
            text-align: left;
            margin: 3px 0px 0px 8px;
          }
        }
      }

      .primary-level-two-details {
        color: #111434;
        font-family: var(--fontFamily);
        font-size: 14px;
        font-weight: 600;
        line-height: 18.23px;
        text-align: left;
        margin-top: 8px;
      }

      .primary-level-three-details {
        display: flex;
        width: fit-content;
        cursor: pointer;

        .expand-collapse-icon {
          margin-top: 9px;
        }

        .expand-collapse-text {
          color: #6e7b8f;
          font-family: var(--fontFamily);
          font-size: 12px;
          font-weight: 400;
          line-height: 16px;
          letter-spacing: 0.02em;
          text-align: left;
          margin: 12px 0px 0px 4px;
        }
      }
    }

    .request-secondary-details {
      display: flex;
      flex-direction: column;

      .background-logo {
        z-index: -1;
        position: fixed;
        right: -62px;
      }

      .secondary-level-one-details {
        display: flex;
        background: #ffffff;
        margin-top: 12px;

        .position-details {
          margin-top: 3px;

          .position-header {
            color: #6e7b8f;
            font-family: var(--fontFamily);
            font-size: 14px;
            font-weight: 500;
            line-height: 16px;
            letter-spacing: 0.02em;
            text-align: right;
          }

          .position-header-value {
            color: #45546e;
            font-family: var(--fontFamily);
            font-size: 14px;
            font-weight: 500;
            line-height: 24px;
            letter-spacing: 0.02em;
            text-align: right;
          }
        }

        .divider-line {
          border: 1px solid #d4d6d8;
          margin: 6px 18px;
        }

        .project-date-details {
          .date-details {
            margin-top: 3px;

            .date-details-header {
              font-family: var(--fontFamily);
              font-size: 14px;
              font-weight: 500;
              line-height: 16px;
              letter-spacing: 0.02em;
              text-align: right;
              color: #6e7b8f;
            }

            .date-details-contents {
              color: #45546e;
              font-family: var(--fontFamily);
              font-size: 14px;
              font-weight: 500;
              line-height: 24px;
              letter-spacing: 0.02em;
              text-align: right;
            }
          }
        }
      }

      .secondary-level-two-details {
        margin-top: 13px;

        .tag-details {
          display: flex;
          gap: 8px;
          justify-content: end;

          .operational-model {
            background: #e7f9f9;
            border: 0.5px solid #13c2c2;
            border-radius: 4px;
            padding: 2px 16px;
            font-family: var(--fontFamily);
            font-size: 12px;
            font-weight: 400;
            line-height: 16px;
            letter-spacing: 0.02em;
            text-align: left;
            color: #45546e;
          }

          .commercial {
            background: #eaffe0;
            border: 0.5px solid #52c41a;
            border-radius: 4px;
            padding: 2px 16px;
            font-family: var(--fontFamily);
            font-size: 12px;
            font-weight: 400;
            line-height: 16px;
            letter-spacing: 0.02em;
            text-align: left;
            color: #45546e;
          }

          .booking-type {
            background: #f3eafd;
            border: 0.5px solid #722ed1;
            border-radius: 4px;
            padding: 2px 16px;
            font-family: var(--fontFamily);
            font-size: 12px;
            font-weight: 400;
            line-height: 16px;
            letter-spacing: 0.02em;
            text-align: left;
            color: #45546e;
          }
        }
      }
    }
  }

  .header-main-divider {
    border: 1px solid #e8e9ee;
  }

  .two-side-containers {
    display: flex;

    .left-side-request-section-container {
      border-right: 1px solid #eeeeee;
      overflow-y: scroll;
      height: var(--dynamicBodyHeight);
      position: relative;
      width: 25%;
      max-width: 0;
      transition: transform 0.3s ease, opacity 0.3s ease, max-width 0.3s ease;
      transform: translateX(-100%);
      opacity: 0;

      &.expanded {
        transform: translateX(0);
        opacity: 1;
        max-width: 25%;
        overflow-wrap: anywhere;
      }

      .request-details-side-overall {
        .request-details-header-section {
          .request-header-value {
            background: #f2f3f6;
            padding: 8px 10px;
            color: #111434;
            font-family: var(--fontFamily);
            font-size: 14px;
            font-weight: 500;
            line-height: 16px;
            letter-spacing: 0.02em;
            text-align: left;
          }
        }

        .side-request-details-section {
          .section-one-within {
            display: flex;
            margin: 20px 15px 5px 15px;
            gap: 25px;
            justify-content: space-between;

            .first-line-details {
              display: flex;
              flex-direction: column;
              gap: 24px;

              .start-date-details {
                .start-date-heading {
                  color: #8b95a5;
                  font-family: var(--fontFamily);
                  font-size: 12px;
                  font-weight: 400;
                  line-height: 16px;
                  letter-spacing: 0.02em;
                  text-align: left;
                  text-overflow: ellipsis;
                  overflow: hidden;
                  white-space: nowrap;
                }
                .start-date-value {
                  color: #272a47;
                  font-family: var(--fontFamily);
                  font-size: 12px;
                  font-weight: 500;
                  line-height: 16px;
                  letter-spacing: 0.02em;
                  text-align: left;
                  text-overflow: ellipsis;
                  overflow: hidden;
                  white-space: nowrap;
                }
              }

              .project-role-details {
                .project-role-heading {
                  color: #8b95a5;
                  font-family: var(--fontFamily);
                  font-size: 12px;
                  font-weight: 400;
                  line-height: 16px;
                  letter-spacing: 0.02em;
                  text-align: left;
                  text-overflow: ellipsis;
                  overflow: hidden;
                  white-space: nowrap;
                }
                .project-role-value {
                  color: #272a47;
                  font-family: var(--fontFamily);
                  font-size: 12px;
                  font-weight: 500;
                  line-height: 16px;
                  letter-spacing: 0.02em;
                  text-align: left;
                  text-overflow: ellipsis;
                  overflow: hidden;
                  white-space: nowrap;
                }
              }

              .request-context-details {
                .request-context-heading {
                  color: #8b95a5;
                  font-family: var(--fontFamily);
                  font-size: 12px;
                  font-weight: 400;
                  line-height: 16px;
                  letter-spacing: 0.02em;
                  text-align: left;
                  text-overflow: ellipsis;
                  overflow: hidden;
                  white-space: nowrap;
                }
                .request-context-value {
                  color: #272a47;
                  font-family: var(--fontFamily);
                  font-size: 12px;
                  font-weight: 500;
                  line-height: 16px;
                  letter-spacing: 0.02em;
                  text-align: left;
                  text-overflow: ellipsis;
                  overflow: hidden;
                  white-space: nowrap;
                }
              }

              .utilization-capacity-details {
                .utilization-heading {
                  color: #8b95a5;
                  font-family: var(--fontFamily);
                  font-size: 12px;
                  font-weight: 400;
                  line-height: 16px;
                  letter-spacing: 0.02em;
                  text-align: left;
                  text-overflow: ellipsis;
                  overflow: hidden;
                  white-space: nowrap;
                }
                .utilization-value {
                  color: #272a47;
                  font-family: var(--fontFamily);
                  font-size: 12px;
                  font-weight: 500;
                  line-height: 16px;
                  letter-spacing: 0.02em;
                  text-align: left;
                  text-overflow: ellipsis;
                  overflow: hidden;
                  white-space: nowrap;
                }
              }
            }

            .second-line-details {
              display: flex;
              flex-direction: column;
              gap: 24px;

              .end-date-details {
                .end-date-heading {
                  color: #8b95a5;
                  font-family: var(--fontFamily);
                  font-size: 12px;
                  font-weight: 400;
                  line-height: 16px;
                  letter-spacing: 0.02em;
                  text-align: left;
                  text-overflow: ellipsis;
                  overflow: hidden;
                  white-space: nowrap;
                }
                .end-date-value {
                  color: #272a47;
                  font-family: var(--fontFamily);
                  font-size: 12px;
                  font-weight: 500;
                  line-height: 16px;
                  letter-spacing: 0.02em;
                  text-align: left;
                  text-overflow: ellipsis;
                  overflow: hidden;
                  white-space: nowrap;
                }
              }

              .position-details {
                .position-heading {
                  color: #8b95a5;
                  font-family: var(--fontFamily);
                  font-size: 12px;
                  font-weight: 400;
                  line-height: 16px;
                  letter-spacing: 0.02em;
                  text-align: left;
                  text-overflow: ellipsis;
                  overflow: hidden;
                  white-space: nowrap;
                }
                .position-value {
                  color: #272a47;
                  font-family: var(--fontFamily);
                  font-size: 12px;
                  font-weight: 500;
                  line-height: 16px;
                  letter-spacing: 0.02em;
                  text-align: left;
                  text-overflow: ellipsis;
                  overflow: hidden;
                  white-space: nowrap;
                }
              }

              .request-closure-details {
                .request-closure-heading {
                  color: #8b95a5;
                  font-family: var(--fontFamily);
                  font-size: 12px;
                  font-weight: 400;
                  line-height: 16px;
                  letter-spacing: 0.02em;
                  text-align: left;
                  text-overflow: ellipsis;
                  overflow: hidden;
                  white-space: nowrap;
                }
                .request-closure-value {
                  color: #272a47;
                  font-family: var(--fontFamily);
                  font-size: 12px;
                  font-weight: 500;
                  line-height: 16px;
                  letter-spacing: 0.02em;
                  text-align: left;
                  text-overflow: ellipsis;
                  overflow: hidden;
                  white-space: nowrap;
                }
              }

              .work-location-details {
                .work-location-heading {
                  color: #8b95a5;
                  font-family: var(--fontFamily);
                  font-size: 12px;
                  font-weight: 400;
                  line-height: 16px;
                  letter-spacing: 0.02em;
                  text-align: left;
                  text-overflow: ellipsis;
                  overflow: hidden;
                  white-space: nowrap;
                }
                .work-location-value {
                  color: #272a47;
                  font-family: var(--fontFamily);
                  font-size: 12px;
                  font-weight: 500;
                  line-height: 16px;
                  letter-spacing: 0.02em;
                  text-align: left;
                  text-overflow: ellipsis;
                  overflow: hidden;
                  white-space: nowrap;
                }
              }
            }
          }

          .section-two-within {
            margin: 25px 4px 30px 15px;

            .request-tag-details {
              display: flex;
              gap: 8px;
              flex-wrap: wrap;

              .req-det-operational-model {
                background: #e7f9f9;
                border: 0.5px solid #13c2c2;
                border-radius: 4px;
                padding: 2px 16px;
                font-family: var(--fontFamily);
                font-size: 12px;
                font-weight: 400;
                line-height: 16px;
                letter-spacing: 0.02em;
                text-align: left;
                color: #45546e;
                width: fit-content;
              }

              .req-det-commercial {
                background: #eaffe0;
                border: 0.5px solid #52c41a;
                border-radius: 4px;
                padding: 2px 16px;
                font-family: var(--fontFamily);
                font-size: 12px;
                font-weight: 400;
                line-height: 16px;
                letter-spacing: 0.02em;
                text-align: left;
                color: #45546e;
                width: fit-content;
              }

              .req-det-booking-type {
                background: #f3eafd;
                border: 0.5px solid #722ed1;
                border-radius: 4px;
                padding: 2px 16px;
                font-family: var(--fontFamily);
                font-size: 12px;
                font-weight: 400;
                line-height: 16px;
                letter-spacing: 0.02em;
                text-align: left;
                color: #45546e;
                width: fit-content;
              }
            }
          }
        }

        .job-details-section {
          .job-details-header-section {
            .job-header-value {
              background: #f2f3f6;
              padding: 8px 10px;
              color: #111434;
              font-family: var(--fontFamily);
              font-size: 14px;
              font-weight: 500;
              line-height: 16px;
              letter-spacing: 0.02em;
              text-align: left;
            }
          }

          .job-details-body-section {
            display: flex;
            margin: 20px 15px 5px 15px;
            gap: 25px;
            flex-direction: column;

            .skill-set-details {
              .skill-set-header {
                color: #8b95a5;
                font-family: var(--fontFamily);
                font-size: 12px;
                font-weight: 400;
                line-height: 16px;
                letter-spacing: 0.02em;
                text-align: left;
              }
              .skill-set-value-details {
                display: flex;
                gap: 8px;
                background: #f6f6f6;
                width: fit-content;
                padding: 2px 6px;
                border-radius: 4px;
                cursor: pointer;
                margin-top: 6px;
                border: 1px solid #e8e9ee;

                .skill-set-text {
                  color: #45546e;
                  font-family: var(--fontFamily);
                  font-size: 12px;
                  font-weight: 500;
                  line-height: 20px;
                  letter-spacing: 0.02em;
                  text-align: left;
                  margin-top: 2px;
                }
              }
              .skill-set-value-details-none {
                color: #272a47;
                font-family: var(--fontFamily);
                font-size: 12px;
                font-weight: 500;
                line-height: 16px;
                letter-spacing: 0.02em;
                text-align: left;
              }
            }

            .job-desc-details {
              .job-desc-header {
                color: #8b95a5;
                font-family: var(--fontFamily);
                font-size: 12px;
                font-weight: 400;
                line-height: 16px;
                letter-spacing: 0.02em;
                text-align: left;
              }
              .job-desc-value {
                color: #272a47;
                font-family: var(--fontFamily);
                font-size: 12px;
                font-weight: 500;
                line-height: 16px;
                letter-spacing: 0.02em;
                text-align: left;
              }
            }

            .acceptance-criteria-details {
              .acceptance-criteria-header {
                color: #8b95a5;
                font-family: var(--fontFamily);
                font-size: 12px;
                font-weight: 400;
                line-height: 16px;
                letter-spacing: 0.02em;
                text-align: left;
              }
              .accpetance-criteria-value {
                color: #272a47;
                font-family: var(--fontFamily);
                font-size: 12px;
                font-weight: 500;
                line-height: 16px;
                letter-spacing: 0.02em;
                text-align: left;
              }
            }
          }
        }
      }
    }

    .right-side-other-sections-container {
      border-left: 1px solid #eeeeee;
    }
  }

  .header-section {
    display: flex;
    justify-content: space-between;
    margin-top: 30px;
    padding-left: 15px;
    padding-right: 15px;

    .primary-header-elements {
      display: flex;

      .back-icon {
        cursor: pointer;
        margin-top: 2px;
      }

      .smart-allocation-header {
        padding-left: 8px;
        font-family: var(--fontFamily);
        font-size: 16px;
        font-weight: 700;
        line-height: 24px;
      }

      .smart-allocation-count-header {
        font-family: var(--fontFamily);
        font-size: 14px;
        font-weight: 400;
        line-height: 24px;
        text-align: left;
        color: #7d838b;
        margin-left: 7px;
      }
    }

    .secondary-header-elements {
      display: flex;
      // width: 35%;
      justify-content: end;

      .search {
        display: flex;
        border: 1px solid #dadce2;
        border-radius: 6px;
        height: 32px;
        width: 308px;

        .search-icon {
          padding: 4px 0px 0px 8px;
        }

        input {
          border: 0px solid;
          text-decoration: none;
          border: 0px solid !important;
          padding: 8px 0px 0px 8px;
          font-family: var(--fontFamily);
          font-size: 12px;
          font-weight: 400;
          line-height: 16px;
          letter-spacing: 0.02em;
          text-align: left;
          width: 248px;
        }

        input:focus {
          outline: none;
          border: 0px solid !important;
        }

        .clear-icon {
          padding: 4px 8px 8px 0px;
          cursor: pointer;
        }
      }

      .view-toggle {
        display: flex;
        padding-left: 16px;

        .list-view-buttton {
          width: 32px;
          height: 32px;
          border-top-left-radius: 4px;
          border-bottom-left-radius: 4px;
          cursor: pointer;

          .list-view-selected {
            padding: 4px 0px 0px 6px;
          }

          .list-view-not-selected {
            padding: 4px 0px 0px 6px;
          }
        }

        .card-view-button {
          width: 32px;
          height: 32px;
          border-top-right-radius: 4px;
          border-bottom-right-radius: 4px;
          cursor: pointer;

          .card-view-selected {
            padding: 4px 0px 0px 6px;
          }

          .card-view-not-selected {
            padding: 4px 0px 0px 6px;
          }
        }
      }

      .filter-section {
        display: flex;
        padding: 3px 8px 6px 8px;
        background: #f6f6f6;
        border: 0.5px solid #b9c0ca;
        border-radius: 16px;
        height: 32px;
        margin-left: 16px;
        width: 67px;
        cursor: pointer;

        .filter-count-section {
          background: #ee4961;
          padding: 0px 5px 3px 5px;
          border-radius: 8px;
          margin-top: 3px;
          width: 23px;

          .filter-count {
            color: #ffffff;
            padding: 0px 0px 0px 3px;
          }
        }
      }
    }
  }

  .body-section {
    display: flex;
    margin-top: 17px;
    padding-right: 15px;

    .request-details-side-overall {
      .request-details-section {
        display: flex;
      }
    }

    .overall-list-view {
      display: flex;
      height: var(--dynamicCardContentHeight);
      width: 100%;
      position: relative;
      overflow-y: auto;
      padding-right: 15px;

      .list-section {
        width: 75%;
        margin-left: 8px;

        .variant-2-content {
          display: flex;
          flex-direction: column;
          // overflow: auto;
          // height: var(--dynamicBodyHeight);

          .header-row {
            min-height: 40px;
            position: sticky;
            top: 0;
            z-index: 1;
            background: #f2f3f6;

            .list-title {
              font-family: var(--fontFamily);
              font-size: 12px;
              font-weight: 500;
              color: #5f6c81;
              padding-right: 8px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }

            .list-title-border-section {
              background: #b9c0ca;
              color: white;
              border: 1px solid white;
              border-bottom: none;
            }

            .svg {
              cursor: pointer;
            }
          }

          .content-row-main {
            height: 48px;
            background-color: #fff;
          }
          .content-row-main.dynamic-ai-height {
            height: 64px;
          }

          .content-row {
            height: 48px;
            background-color: #fff;
            border-bottom: 1px solid #b9c0ca;

            .main-content {
              font-family: var(--fontFamily);
              font-size: 14px;
              font-weight: 500;
              color: #45546e;
              text-overflow: ellipsis;
              white-space: nowrap;
              overflow: hidden;
            }

            .sub-content {
              font-family: var(--fontFamily);
              font-size: 14px;
              font-weight: 400;
              color: #45546e;
              text-overflow: ellipsis;
              white-space: nowrap;
              overflow: hidden;
            }

            .due-date {
              display: flex;
              flex-direction: column;
              width: 100%;

              .text {
                font-family: var(--fontFamily);
                font-size: 10px;
                font-weight: 500;
                color: #8b95a5;
                text-overflow: ellipsis;
                white-space: nowrap;
                overflow: hidden;
              }
            }

            .bold-content {
              font-family: var(--fontFamily);
              font-size: 14px;
              font-weight: 500;
              color: #1b2140;
              text-overflow: ellipsis;
              white-space: nowrap;
              overflow: hidden;
            }

            .section-sub-content {
              text-align: center;
              align-content: center;
              height: 48px;
              border-left: 1px solid #b9c0ca;
              font-family: var(--fontFamily);
              font-size: 14px;
              font-weight: 400;
              color: #526179;
              text-overflow: ellipsis;
              white-space: nowrap;
              overflow: hidden;
            }

            .section-sub-content-align-left {
              text-align: left;
              align-content: center;
              height: 48px;
              border-left: 1px solid #b9c0ca;
              font-family: var(--fontFamily);
              font-size: 14px;
              font-weight: 400;
              color: #526179;
              text-overflow: ellipsis;
              white-space: nowrap;
              overflow: hidden;
            }

            .link-text {
              font-family: var(--fontFamily);
              font-size: 14px;
              font-weight: 400;
              color: #1890ff;
              text-decoration: underline;
              cursor: pointer;
            }

            .svg {
              cursor: pointer;
            }

            .progress-bar-custom {
              display: flex;
              align-items: center;
              gap: 8px;
              width: 100%;
              position: relative;

              .progress-custom {
                width: 70%;
                height: 9px;
                border-radius: 9px;
                background-color: #eef9e8;
              }

              .progress-fill-custom {
                height: 9px;
                border-radius: 9px;
                position: absolute;
                background-color: #a0d911;
              }

              .percentage {
                font-family: var(--fontFamily);
                font-size: 13px;
                font-weight: 400;
                color: #5f6c81;
              }
            }
          }
          .content-row.dynamic-ai-height {
            height: 64px;
          }

          .overall-rank-chart {
            display: flex;
            gap: 8px;

            .pie-chart {
              padding-top: 8px;
              position: relative;
            }

            .rank-details {
              display: flex;
              flex-direction: column;
              font-family: var(--fontFamily);
              font-size: 16px;
              font-weight: 700;
              line-height: 24px;
              letter-spacing: 0.02em;
              text-align: left;
              color: #45546e;

              .rank-values {
                display: flex;
                gap: 6.7px;
                margin-top: 5px;

                .rank {
                  font-family: var(--fontFamily);
                  font-size: 14px;
                  font-weight: 700;
                  line-height: 24px;
                  letter-spacing: 0.02em;
                  text-align: left;
                  padding-top: 8px;
                }

                .icon-image-styles {
                  height: 12px;
                }
              }
            }

            .chart-overlay {
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              width: 45px;
              height: 52px;
              background-color: rgba(0, 0, 0, 0);
              cursor: pointer;
              z-index: 20;
            }
          }

          ::ng-deep .custom-tooltip {
            position: absolute;
            width: 260px;
            z-index: 10;
            background: #ffffff;
            color: #45546e;
            font-family: var(--fontFamily);
            font-size: 13.66px;
            font-weight: 400;
            line-height: 16.4px;
            text-align: left;
            border-radius: 13.66px;
            box-shadow: 0px 8px 20px 0px #00000033;
          }

          ::ng-deep .custom-tooltip.adjust-arrow::after {
            top: 75%;
          }

          ::ng-deep .tooltip-header {
            display: flex;
            height: 48px;
            background: #f2f3f6;
            font-family: var(--fontFamily);
            font-size: 12px;
            font-weight: 500;
            line-height: 24px;
            letter-spacing: 0.02em;
            text-align: left;
            color: #111434;
            padding: 10px 0px 12px 9px;
            border-top-left-radius: 13.66px;
            border-top-right-radius: 13.66px;

            .header-icon-image-styles {
              height: 25px;
              width: 24px;
            }

            .header-logo {
              padding-right: 4px;
            }

            .header-text {
              padding-top: 2px;
            }
          }

          ::ng-deep .tooltip-content-elements {
            padding: 13.66px;

            .center-text {
              font-family: var(--fontFamily);
              font-size: 16px;
              font-weight: 700;
              line-height: 24px;
              letter-spacing: 0.02em;
              text-align: left;
              color: #111434;
            }

            .center-text-content {
              font-family: var(--fontFamily);
              font-size: 10px;
              font-weight: 400;
              line-height: 10px;
              letter-spacing: 0.02em;
              text-align: center;
              color: #8b95a5;
            }

            .rank-flag {
              display: flex;

              .block-flag {
                position: relative;
                padding: 4px 8px;
                width: 47px;
                text-align: right;
                color: #ffffff;
                border-top-left-radius: 2px;
                border-bottom-left-radius: 2px;

                .rank-value {
                  font-size: 14px;
                }

                &::after {
                  content: "";
                  position: absolute;
                  right: 27px;
                  top: 50%;
                  transform: translateY(-50%);
                  border-width: 11px;
                  border-style: solid;
                  border-color: transparent transparent transparent #ffffff;
                }
              }
            }
          }

          ::ng-deep .tooltip-item {
            display: flex;
            align-items: center;
            font-size: 12px;
            margin-bottom: 5px;
            padding: 0px 8px 6.83px 8px;

            .tooltip-content {
              display: flex;
              justify-content: space-between;
              width: 100%;
            }

            .text {
              flex-grow: 1;
              margin-right: 10px;
            }

            .value {
              white-space: nowrap;
            }

            .divider-line {
              border-top: 2px dashed #dadce2;
              width: 47%;
              margin: 8px 12px 0px 0px;
            }
          }

          ::ng-deep .color-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
          }

          ::ng-deep #aiRankChart {
            height: 40px;
            width: 40px;
          }
        }

        .variant-2-content::-webkit-scrollbar {
          width: 6px !important;
          height: 6px !important;
        }
      }

      .action-btn {
        border: 1px solid #45546e;
        padding: 4px 8px 4px 8px;
        font-family: var(--fontFamily);
        font-size: 12px;
        font-weight: 600;
        line-height: 16px;
        letter-spacing: -0.02em;
        text-align: left;
        border-radius: 4px;
        color: #45546e;
        cursor: pointer;
      }

      .rank-flag {
        display: flex;

        .block-flag {
          position: relative;
          padding: 4px 8px;
          width: 47px;
          text-align: right;
          color: #ffffff;
          border-top-left-radius: 2px;
          border-bottom-left-radius: 2px;

          .rank-value {
            font-size: 14px;
          }

          &::after {
            content: "";
            position: absolute;
            right: 27px;
            top: 50%;
            transform: translateY(-50%);
            border-width: 11px;
            border-style: solid;
            border-color: transparent transparent transparent #ffffff;
          }
        }
      }
    }

    .employee-empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      overflow: hidden;
      height: var(--dynamicCardContentHeight);
      padding: 0px 24px;

      .no-candidate-title {
        font-family: var(--fontFamily);
        font-size: 14px;
        font-weight: 500;
        color: #1b2140;
        margin-bottom: 4px;
      }
    }
  }

  .employee-empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    height: var(--dynamicCardContentHeight);
    padding: 0px 24px;

    .no-candidate-title {
      font-family: var(--fontFamily);
      font-size: 14px;
      font-weight: 500;
      color: #1b2140;
      margin-bottom: 4px;
    }
  }

  .filter-icon-hide {
    display: flex;
    flex-direction: column;
    align-items: center;
    background: #f2f3f6;
    width: 40px;
    height: 200px;
    border-top-right-radius: 8px;
    border-bottom-right-radius: 8px;
    border: 1px solid #e8e9ee;

    .filter-expand-icon {
      cursor: pointer;
      margin-top: 24px;

      .expand-image {
        width: 24px;
        height: 24px;
      }
    }

    .filter-count {
      margin-top: 6px;
      margin-bottom: 30px;
      font-family: var(--fontFamily);
      font-size: 12px;
      font-weight: 500;
      line-height: 16px;
      letter-spacing: 0.02em;
      text-align: left;
      background: #ee4961;
      border-radius: 12px;
      color: white;
      height: 20px;
      width: 24px;
      text-align: center;
      padding: 2px 4px;
    }

    .filter-count-no-text {
      margin-top: 16px;
      height: 20px;
      width: 24px;
      text-align: center;
      padding: 1px 4px;
    }

    .filter-text {
      transform: rotate(270deg);
      color: #45546e;
      font-family: var(--fontFamily);
      font-size: 12px;
      font-weight: 500;
      line-height: 16px;
      letter-spacing: 0.04em;
      text-align: left;
      display: inline-block;
      white-space: nowrap;
      margin-bottom: 30px;
    }
  }

  .filter-section {
    // width: 20%;
    transition: width 0.3s ease;
    height: var(--dynamicCardContentHeight);
    overflow-y: auto;

    .filter-content {
      .filter-header {
        display: flex;
        justify-content: space-between;
        background-color: #f2f3f6;
        font-family: var(--fontFamily);
        font-size: 12px;
        font-weight: 500;
        line-height: 16px;
        letter-spacing: 0.02em;
        text-align: left;
        display: flex;
        height: 40px;
        padding: 0px 8px 0px 16px;
        position: sticky;
        z-index: 10;
        top: 0;

        .filter-primary-contents {
          display: flex;
          padding-top: 6px;
          gap: 12px;

          .filter-header-text {
            color: #45546e;
            font-family: var(--fontFamily);
            font-size: 12px;
            font-weight: 700;
            line-height: 16px;
            letter-spacing: 0.02em;
            text-align: left;
            padding-top: 4px;
          }

          .filter-count-value {
            padding-top: 3px;

            .count-txt {
              padding: 1px 4px;
              color: white;
              height: 20px;
              width: 24px;
              text-align: center;
              background: #ee4961;
              border-radius: 12px;
            }

            .no-count-txt {
              height: 20px;
              width: 24px;
            }
          }
        }

        .filter-secondary-contents {
          padding-top: 6px;
          .pan-in-button {
            cursor: pointer;
          }
        }
      }

      .filter-sections {
        min-height: calc(var(--dynamicSubHeight) - 93px);
        max-height: calc(var(--dynamicSubHeight) - 93px);
        overflow-y: scroll;

        .individual-filter {
          .filter-sub-header {
            display: flex;
            padding: 8px 16px;
            // background: #f5fbe7;
            justify-content: space-between;

            .filter-primary-contents {
              .sub-header-text {
                font-family: var(--fontFamily);
                font-size: 12px;
                font-weight: 400;
                line-height: 16px;
                letter-spacing: 0.02em;
                text-align: left;
                color: #5f6c81;
              }
            }

            .filter-secondary-contents {
              display: flex;
              .expand-hide-icon {
                .sub-icon-expand {
                  margin-right: 6px;
                  cursor: pointer;
                }
                .sub-icon-hide {
                  margin-right: 6px;
                  cursor: pointer;
                }
              }

              .clear-image {
                height: 18px;
                width: 18px;
                cursor: pointer;
              }
            }
          }

          .filter-content-values {
            padding: 6px 12px 10px 12px;
            border-bottom: 1px solid #b9c0ca;

            .dropdown {
              .form-field-class {
                font-size: 12px;
                font-family: var(--fontFamily);
                border: 1px solid #dadce2;
                border-radius: 4px;
                width: 200px;

                ::ng-deep
                  .mat-form-field-appearance-outline
                  .mat-form-field-outline {
                  color: transparent; /* Remove the red underline */
                }

                ::ng-deep
                  .mat-form-field-appearance-outline
                  .mat-form-field-outline-thick {
                  color: transparent; /* Ensure no red on thick outline */
                }

                ::ng-deep .mat-select-value {
                  color: #8b95a5 !important; /* Customize the selected value color */
                  font-family: var(--fontFamily) !important;
                  font-size: 12px !important;
                }

                ::ng-deep .mat-select-trigger {
                  border-color: transparent !important; /* Remove the red border when focused */
                }

                /* Control the dropdown options */
                ::ng-deep .mat-option {
                  font-family: var(--fontFamily);
                  font-size: 11px;
                  color: #8b95a5 !important; /* Custom color for options */
                  &:hover {
                    background-color: #f0f0f0;
                  }
                }

                /* Remove the default focus border */
                ::ng-deep .mat-form-field-outline-start,
                ::ng-deep .mat-form-field-outline-end {
                  color: transparent;
                }

                /* Remove the default red color on error */
                ::ng-deep .mat-form-field-invalid .mat-form-field-outline {
                  color: transparent !important;
                }

                ::ng-deep .mat-form-field-invalid .mat-select-value {
                  color: #8b95a5 !important; /* Override the red error color */
                }

                /* Adjust padding */
                ::ng-deep .mat-form-field-wrapper {
                  padding-bottom: 0px !important;
                }

                ::ng-deep .mat-form-field.mat-focused .mat-select-trigger {
                  border-color: transparent !important; /* Remove red border on focus */
                }

                ::ng-deep .mat-select-arrow {
                  color: #5f6c81 !important; /* Custom color for the dropdown arrow */
                }

                ::ng-deep .mat-form-field.mat-focused .mat-select-arrow {
                  color: #5f6c81 !important; /* Keep the custom color on focus */
                }

                ::ng-deep .mat-select-value {
                  color: #8b95a5 !important; /* Customize the selected value color */
                  font-family: var(--fontFamily) !important;
                  font-size: 12px !important;
                }
              }
            }

            .range-filter {
              display: flex;
              flex-direction: column;

              .range-inputs {
                display: flex;
                margin-top: 10px;

                .min {
                  display: flex;
                  flex-direction: column;
                  padding: 0px 14px 0px 28px;

                  label {
                    font-family: var(--fontFamily);
                    font-size: 12px;
                    font-weight: 400;
                    line-height: 16px;
                    letter-spacing: 0.02em;
                    text-align: left;
                    color: #5f6c81;
                  }

                  input {
                    width: 60px;
                    padding: 5px;
                    border: 1px solid #dadce2;
                    border-radius: 4px;
                    font-size: 12px;
                    padding-left: 20px;
                  }
                }

                .max {
                  display: flex;
                  flex-direction: column;
                  label {
                    font-family: var(--fontFamily);
                    font-size: 12px;
                    font-weight: 400;
                    line-height: 16px;
                    letter-spacing: 0.02em;
                    text-align: left;
                    color: #5f6c81;
                  }

                  input {
                    width: 60px;
                    padding: 5px;
                    border: 1px solid #dadce2;
                    border-radius: 4px;
                    font-size: 12px;
                    padding-left: 20px;
                  }
                }

                input {
                  width: 45%;
                  padding: 5px;
                  border: 1px solid #dadce2;
                  border-radius: 4px;
                  font-size: 12px;
                }
              }

              ::ng-deep .ngx-slider {
                .ngx-slider-track {
                  background-color: #ee4961 !important; /* Track background color */
                }

                .ngx-slider-pointer {
                  background-color: #ee4961 !important; /* Color of the slider thumb */
                  border-color: #ee4961 !important; /* Border color of the thumb */
                }

                ::ng-deep .ngx-slider .ngx-slider-pointer {
                  top: -5px !important;
                  background-color: rgba(0, 0, 0, 0.12) !important;
                }

                .ngx-slider-selection {
                  background-color: #ee4961 !important; /* Color of the filled portion of the slider */
                }

                .ngx-slider-tick {
                  background-color: #ee4961 !important; /* Tick marks color */
                }

                ::ng-deep .ngx-slider-pointer {
                  width: 12px !important; /* Set thumb width */
                  height: 12px !important; /* Set thumb height */
                  top: -5px !important;

                  &::after {
                    width: 0px !important;
                    height: 0px !important;
                  }
                }
              }
            }

            .chips-container {
              display: flex;
              flex-wrap: wrap;
              max-height: 150px; /* Fixed height for scroll */
              overflow-y: auto; /* Enable vertical scrolling */
              padding: 5px;
              margin-top: 16px;

              .chip {
                display: flex;
                background-color: #f6f6f6;
                color: #45546e;
                padding: 2px 4px;
                border-radius: 4px;
                margin: 5px;
                display: flex;
                align-items: center;
                font-family: var(--fontFamily);
                font-size: 12px;
                font-weight: 500;
                line-height: 20px;
                letter-spacing: 0.02em;
                text-align: left;
                justify-content: space-between;
                gap: 8px;

                .chip-close {
                  .close-btn {
                    // margin-left: 8px;
                    cursor: pointer;
                    color: white;
                    font-weight: bold;
                    margin-bottom: 2px;
                  }
                }
              }
            }

            select {
              padding: 5px;
              margin-bottom: 10px;
              width: 200px;
              border-radius: 4px;
              border: 1px solid #dadce2;
              font-family: DM Sans;
              font-size: 11px;
              font-weight: 400;
              line-height: 24px;
              letter-spacing: 0.02em;
              text-align: left;
              color: #8b95a5;
            }
          }
        }

        .individual-experience-filter {
          .filter-sub-header-we {
            display: flex;
            padding: 8px 8px 8px 16px;
            background: #e8e9ee;

            .sub-header-text-we {
              font-family: var(--fontFamily);
              font-size: 12px;
              font-weight: 400;
              line-height: 16px;
              letter-spacing: 0.02em;
              text-align: left;
              color: #5f6c81;
              // margin: 2px 40px 0px 0px;
              white-space: nowrap;
            }

            .expand-hide-icon {
              .sub-icon-expand {
                margin-right: 6px;
                cursor: pointer;
              }
              .sub-icon-hide {
                margin-right: 6px;
                cursor: pointer;
              }
            }

            .clear-image {
              height: 18px;
              width: 18px;
              cursor: pointer;
            }
          }
        }

        .individual-education-filter {
          .filter-sub-header {
            display: flex;
            padding: 8px 10px 8px 16px;
            background: #fff8ec;

            .sub-header-text {
              font-family: var(--fontFamily);
              font-size: 12px;
              font-weight: 400;
              line-height: 16px;
              letter-spacing: 0.02em;
              text-align: left;
              color: #5f6c81;
              margin: 2px 4px 0px 0px;
              text-overflow: ellipsis;
              white-space: nowrap;
              overflow: hidden;
            }

            .expand-hide-icon {
              .sub-icon-expand {
                margin-right: 6px;
                cursor: pointer;
              }
              .sub-icon-hide {
                margin-right: 6px;
                cursor: pointer;
              }
            }

            .clear-image {
              height: 18px;
              width: 18px;
              cursor: pointer;
            }
          }
        }

        .individual-skill-filter {
          .filter-sub-header {
            display: flex;
            padding: 8px 8px 8px 16px;
            background: #e7f9f9;

            .sub-header-text {
              font-family: var(--fontFamily);
              font-size: 12px;
              font-weight: 400;
              line-height: 16px;
              letter-spacing: 0.02em;
              text-align: left;
              color: #5f6c81;
              // margin: 2px 123px 0px 0px;
              text-overflow: ellipsis;
              white-space: nowrap;
              overflow: hidden;
            }

            .expand-hide-icon {
              .sub-icon-expand {
                margin-right: 6px;
                cursor: pointer;
              }
              .sub-icon-hide {
                margin-right: 6px;
                cursor: pointer;
              }
            }

            .clear-image {
              height: 18px;
              width: 18px;
              cursor: pointer;
            }
          }
        }
      }
    }

    .filter-sections::-webkit-scrollbar {
      width: 0px !important;
      height: 0px !important;
    }

    .apply-btn {
      padding: 7px 13px 10px 13px;
      position: sticky;
      bottom: 0;
      background-color: #ffffff;
      height: 50px;
      border: 1px solid #e8e9ee;

      .apply-button {
        padding: 7px 13px 10px 13px;
        position: sticky;
        bottom: 0;
        padding: 8px 12px;
        border: 1px solid #45546e;
        border-radius: 16px;
        cursor: pointer;
        width: 100%;
        background: #ffffff;
        font-size: 16px;
        z-index: 10;
        font-family: var(--fontFamily);
        font-size: 14px;
        font-weight: 700;
        line-height: 16px;
        letter-spacing: -0.02em;
        color: #45546e;
        font-family: DM Sans;
        font-size: 14px;
        font-weight: 500;
        line-height: 16px;
        letter-spacing: -0.02em;
        text-align: center;
      }
    }
  }

  .collapsed {
    width: 0%;
    overflow: hidden;
  }

  .ailoader-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin: 0px 16px;
    padding: 16px 0px 16px 0px;
    background-color: #fff;
    border-radius: 4px;
    height: var(--dynamicOverallHeight);

    img {
      margin-left: 50px;
      height: 200px;
    }

    .ai-evaluation-loading {
      color: rgba(17, 20, 52, 1);
      font-family: "DM Sans";
      font-size: 14px;
      font-weight: 700;
      line-height: 24px;
      letter-spacing: 0.02em;
      text-align: center;
      margin: 14px 0px 0px 0px;
    }

    .loader-line {
      width: 400px;
      height: 7px;
      position: relative;
      overflow: hidden;
      background-color: #ddd;
      margin: 25px 0px 0px 0px;
      -webkit-border-radius: 20px;
      -moz-border-radius: 20px;
      border-radius: 20px;
    }

    .loader-line:before {
      content: "";
      position: absolute;
      left: -50%;
      height: 7px;
      width: 100%;
      background: linear-gradient(270deg, #dbd64c 0%, #ee4961 105.29%);
      -webkit-animation: line-anim 2s linear infinite;
      -moz-animation: line-anim 2s linear infinite;
      animation: line-anim 2s linear infinite;
      -webkit-border-radius: 20px;
      -moz-border-radius: 20px;
      border-radius: 20px;
    }

    @keyframes line-anim {
      0% {
        left: 0%;
        width: 30%;
      }

      50% {
        left: 30%;
        width: 30%;
      }

      100% {
        left: 100%;
        width: 30%;
      }
    }
  }

  .card-view-section {
    display: flex;
    margin-top: 17px;
    overflow: auto;

    .card-view-contents {
      display: flex;
      height: var(--dynamicCardContentHeight);
      // width: 100%;
    }

    .card-content {
      overflow-y: scroll;
      height: var(--dynamicCardContentHeight);
    }

    .card-container {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 24px;
      margin: 0px 50px 0px 56px;
      padding-bottom: 24px;
    }

    .card {
      border: 1px solid #e8e9ee;
      padding: 15px 0px 8px 0px;
      transition: all 0.3s ease;
      border-radius: 8px;

      .card-header {
        display: flex;
        padding: 0px;
        background-color: #ffffff;
        border-bottom: none;
        justify-content: space-between;

        .rank-section-primary {
          .rank-flag {
            display: flex;

            .block-flag {
              position: relative;
              padding: 4px 8px;
              width: 82px;
              text-align: left;
              color: #ffffff;
              border-top-right-radius: 2px;
              border-bottom-right-radius: 2px;

              .rank-value {
                font-size: 14px;
              }

              &::after {
                content: "";
                position: absolute;
                right: 0px;
                top: 50%;
                transform: translateY(-50%);
                border-width: 11px;
                border-style: solid;
                border-color: transparent #ffffff transparent transparent;
              }
            }
          }
        }

        .rank-section-secondary {
          margin-right: 14px;
          display: flex;

          .rank-badge {
            padding-right: 9px;

            .badge-icon-rank-one {
              position: relative;
              text-align: center;
              color: white;

              .badge-rank-one {
                position: absolute;
                top: 21%;
                left: 43%;
                font-family: var(--fontFamily);
                font-size: 12.8px;
                font-weight: 700;
                line-height: 12.8px;
                letter-spacing: -0.04em;
                text-align: center;
                color: #b56d16;
              }
            }

            .badge-icon-rank-two {
              position: relative;
              text-align: center;
              color: white;

              .badge-rank-two {
                position: absolute;
                top: 19%;
                left: 39%;
                font-family: var(--fontFamily);
                font-size: 12.8px;
                font-weight: 700;
                line-height: 12.8px;
                letter-spacing: -0.04em;
                text-align: center;
                color: #636362;
              }
            }

            .badge-icon-rank-three {
              position: relative;
              text-align: center;
              color: white;

              .badge-rank-three {
                position: absolute;
                top: 20%;
                left: 37%;
                font-family: var(--fontFamily);
                font-size: 12.8px;
                font-weight: 700;
                line-height: 12.8px;
                letter-spacing: -0.04em;
                text-align: center;
                color: #b56d16;
              }
            }

            .badge-icon-rank-others {
              position: relative;
              text-align: center;
              color: white;

              .badge-rank-others {
                position: absolute;
                top: 18%;
                left: 34%;
                font-family: var(--fontFamily);
                font-size: 12.8px;
                font-weight: 700;
                line-height: 12.8px;
                letter-spacing: -0.04em;
                text-align: center;
                color: #ffffff;
              }
            }
          }

          .pin-card {
            cursor: pointer;
            margin-top: 5px;
          }
        }
      }

      .card-body {
        padding: 0px 12px 23px 13px;

        .employee-basic-detail-section-hidden {
          margin-top: 15px;
          border-top: 1px solid #e8e9ee;

          .employee-name {
            font-family: var(--fontFamily);
            font-size: 14px;
            font-weight: 500;
            line-height: 24px;
            text-align: center;
            color: #45546e;
            margin-top: 9px;
          }

          .employee-designation {
            color: #7d838b;
            font-family: var(--fontFamily);
            font-size: 12px;
            font-weight: 400;
            line-height: 16px;
            text-align: center;
          }
        }

        .score-details-section-hidden {
          display: flex;
          justify-content: space-between;
          border-top: 1px solid #e8e9ee;
          margin-top: 20px;

          .overall-details-section {
            margin: 24px 0px 0px 22px;

            .overall-percent {
              .score {
                color: #45546e;
                font-family: var(--fontFamily);
                font-size: 18px;
                font-weight: 600;
                line-height: 20px;
                text-align: left;
              }

              .score-text {
                margin-top: 3px;
                color: #7d838b;
                font-family: var(--fontFamily);
                font-size: 12px;
                font-weight: 400;
                line-height: 16px;
                text-align: left;
              }
            }

            .expand-card-section {
              border: 1px solid #b9c0ca;
              border-radius: 16px;
              padding: 4px 6px;
              background: #f6f6f6;
              width: 90px;
              margin-top: 25px;
              cursor: pointer;

              .expand-card {
                color: #45546e;
                font-family: var(--fontFamily);
                font-size: 12px;
                font-weight: 400;
                line-height: 15.62px;
                text-align: center;
              }
            }

            .allocate-btn {
              background: var(--allocateBtnBackground);
              width: fit-content;
              border-radius: 16px;
              padding: 4px 8px;
              margin-top: 20px;
              cursor: pointer;

              .allocate-text {
                color: var(--allocateBtnColor);
                font-family: var(--fontFamily);
                font-size: 12px;
                font-weight: 400;
                line-height: 16px;
                letter-spacing: -0.02em;
                text-align: left;
              }
            }
          }

          .score-detail-section {
            margin: 10px 25px 0px 0px;

            .skill-score {
              display: flex;
              margin-top: 8px;

              .skill-score-style {
                font-family: var(--fontFamily);
                font-size: 16px;
                font-weight: 600;
                line-height: 24px;
                text-align: left;
                color: #fa8c16;
              }

              .skill-score-text {
                color: #7d838b;
                font-family: var(--fontFamily);
                font-size: 12px;
                font-weight: 400;
                line-height: 16px;
                text-align: left;
                margin: 4px 0px 0px 8px;
              }
            }

            .location-score {
              display: flex;
              margin-top: 8px;

              .location-score-style {
                font-family: var(--fontFamily);
                font-size: 16px;
                font-weight: 600;
                line-height: 24px;
                text-align: left;
                color: #13c2c2;
              }

              .location-score-text {
                color: #7d838b;
                font-family: var(--fontFamily);
                font-size: 12px;
                font-weight: 400;
                line-height: 16px;
                text-align: left;
                margin: 4px 0px 0px 8px;
              }
            }

            .availability-score {
              display: flex;
              margin-top: 8px;

              .availability-score-style {
                font-family: var(--fontFamily);
                font-size: 16px;
                font-weight: 600;
                line-height: 24px;
                text-align: left;
                color: #52c41a;
              }

              .availability-score-text {
                color: #7d838b;
                font-family: var(--fontFamily);
                font-size: 12px;
                font-weight: 400;
                line-height: 16px;
                text-align: left;
                margin: 4px 0px 0px 8px;
              }
            }
          }
        }

        .employee-basic-detail-section-expanded {
          margin-top: 15px;
          border-top: 1px solid #e8e9ee;

          .employee-basic-details-expanded {
            .employee-name-expanded {
              font-family: var(--fontFamily);
              font-size: 14px;
              font-weight: 500;
              line-height: 24px;
              text-align: center;
              color: #45546e;
              margin-top: 9px;
            }

            .employee-designation-expanded {
              display: flex;
              justify-content: center;
              color: #7d838b;
              font-family: var(--fontFamily);
              font-size: 12px;
              font-weight: 400;
              line-height: 16px;
              text-align: center;
            }

            .expanded-overall-score-section {
              margin-top: 21px;

              .score {
                color: #45546e;
                font-family: var(--fontFamily);
                font-size: 18px;
                font-weight: 600;
                line-height: 20px;
                text-align: center;
              }

              .score-text {
                margin-top: 3px;
                color: #7d838b;
                font-family: var(--fontFamily);
                font-size: 12px;
                font-weight: 400;
                line-height: 16px;
                text-align: center;
              }
            }
          }
        }

        .employee-score-details-expanded {
          margin-top: 12.5px;
          border-top: 1px solid #e8e9ee;
          justify-content: space-between;

          .skill-details-expanded {
            display: flex;
            justify-content: space-between;

            .skill-score {
              display: flex;
              margin-top: 9.5px;
              flex-direction: column;
              padding-left: 30px;

              .skill-score-style {
                font-family: var(--fontFamily);
                font-size: 16px;
                font-weight: 600;
                line-height: 24px;
                text-align: center;
                color: #fa8c16;
              }

              .skill-score-text {
                color: #7d838b;
                font-family: var(--fontFamily);
                font-size: 12px;
                font-weight: 400;
                line-height: 16px;
                text-align: center;
                margin: 4px 0px 0px 0px;
              }
            }

            .location-score {
              display: flex;
              margin-top: 9.5px;
              flex-direction: column;
              padding-right: 18px;

              .location-score-style {
                font-family: var(--fontFamily);
                font-size: 16px;
                font-weight: 600;
                line-height: 24px;
                text-align: center;
                color: #13c2c2;
              }

              .location-score-text {
                color: #7d838b;
                font-family: var(--fontFamily);
                font-size: 12px;
                font-weight: 400;
                line-height: 16px;
                text-align: center;
                margin: 4px 0px 0px 8px;
              }
            }

            .availability-score {
              display: flex;
              margin-top: 9.5px;
              flex-direction: column;

              .availability-score-style {
                font-family: var(--fontFamily);
                font-size: 16px;
                font-weight: 600;
                line-height: 24px;
                text-align: center;
                color: #52c41a;
              }

              .availability-score-text {
                color: #7d838b;
                font-family: var(--fontFamily);
                font-size: 12px;
                font-weight: 400;
                line-height: 16px;
                text-align: center;
                margin: 4px 0px 0px 8px;
              }
            }
          }
        }

        .scroll-container-expanded {
          margin-top: 16px;
          border-top: 1px solid #e8e9ee;
          overflow-x: hidden;
          overflow-y: auto;
          height: var(--dynamicInternalScrollHeight);

          .matched-skills-section {
            padding: 11px 8px;

            .skill-details-header {
              color: #526179;
              font-family: var(--fontFamily);
              font-size: 12px;
              font-weight: 400;
              line-height: 16px;
              text-align: left;
            }

            .skill-details-matched-exist {
              display: flex;
              margin-top: 8px;
              gap: 8px;
              flex-wrap: wrap;

              .individual-skills {
                border: 1px solid #b9c0ca;
                border-radius: 16px;
                background: #f6f6f6;

                .skill-details {
                  padding: 4px 10px;

                  .skill-text {
                    color: #45546e;
                    font-family: var(--fontFamily);
                    font-size: 12px;
                    font-weight: 400;
                    line-height: 15.62px;
                    text-align: left;
                  }
                }
              }
            }
          }

          .unmatched-skills-section {
            padding: 11px 8px;

            .unmatched-skill-details-header {
              color: #526179;
              font-family: var(--fontFamily);
              font-size: 12px;
              font-weight: 400;
              line-height: 16px;
              text-align: left;
            }

            .skill-details-unmatched-exist {
              display: flex;
              margin-top: 8px;
              gap: 8px;
              flex-wrap: wrap;

              .individual-skills {
                border: 1px solid #ff3a46;
                border-radius: 16px;
                background: #ffebec;

                .skill-details {
                  padding: 4px 10px;

                  .skill-text {
                    color: #ff3a46;
                    font-family: var(--fontFamily);
                    font-size: 12px;
                    font-weight: 400;
                    line-height: 15.62px;
                    text-align: left;
                  }
                }
              }
            }
          }

          .location-details {
            margin-top: 8px;
            border-top: 1px solid #e8e9ee;
            padding: 11px 8px;

            .location-header {
              color: #526179;
              font-family: var(--fontFamily);
              font-size: 12px;
              font-weight: 400;
              line-height: 16px;
              text-align: left;
            }

            .location {
              display: flex;
              margin-top: 8px;
              border: 1px solid #b9c0ca;
              width: fit-content;
              border-radius: 16px;
              background: #f6f6f6;

              .location-icon {
                padding: 0px 10px 4px 10px;
              }

              .location-text {
                color: #45546e;
                font-family: var(--fontFamily);
                font-size: 12px;
                font-weight: 400;
                line-height: 15.62px;
                text-align: left;
                padding: 4px 10px 4px 0px;
              }
            }
          }

          .availability-details {
            padding: 11px 8px;
            border-top: 1px solid #e8e9ee;

            .availability-header {
              color: #526179;
              font-family: var(--fontFamily);
              font-size: 12px;
              font-weight: 400;
              line-height: 16px;
              text-align: left;
              margin-top: 8px;
            }

            .available-section {
              margin-top: 8px;

              .available-section-header {
                width: fit-content;
                background: #eef9e8;
                border-radius: 4px;
                padding: 2px 6px;

                .header-text {
                  font-family: var(--fontFamily);
                  font-size: 10px;
                  font-weight: 400;
                  line-height: 13.02px;
                  text-align: left;
                  color: #52c41a;
                }
              }

              .available-date-content-present {
                font-family: var(--fontFamily);
                color: #45546e;
                font-size: 10px;
                font-weight: 400;
                line-height: 13.02px;
                text-align: left;

                .available-duration-contents {
                  padding-top: 6px;
                }
              }

              .available-date-content-not-present {
                font-family: var(--fontFamily);
              }
            }

            .booked-section {
              margin-top: 8px;

              .booked-section-header {
                background: #ffeee8;
                width: fit-content;
                border-radius: 4px;
                padding: 2px 6px;

                .header-text {
                  color: #fa541c;
                  font-family: var(--fontFamily);
                  font-size: 10px;
                  font-weight: 400;
                  line-height: 13.02px;
                  text-align: left;
                }
              }

              .booked-section-available {
                .booked-section-content {
                  .booked-date {
                    display: flex;
                    padding: 2px 6px;
                    background: #f6f6f6;
                    width: fit-content;
                    margin-top: 8px;
                    border-radius: 4px;

                    .date-range-content {
                      color: #45546e;
                      font-family: var(--fontFamily);
                      font-size: 10px;
                      font-weight: 500;
                      line-height: 13.02px;
                      text-align: left;
                      margin: 5px 0px 0px 6px;
                    }
                  }

                  .project-details {
                    margin-top: 6px;
                    display: flex;
                    flex-wrap: wrap;

                    .project-id-value {
                      font-family: var(--fontFamily);
                      font-size: 12px;
                      font-weight: 500;
                      line-height: 15.62px;
                      text-align: left;
                      color: #8b95a5;
                    }

                    .project-name-details {
                      color: #45546e;
                      font-family: vat(--fontFamily);
                      font-size: 12px;
                      font-weight: 400;
                      line-height: 15.62px;
                      text-align: left;
                      margin-left: 6px;
                    }
                  }
                }
              }
            }

            .collapse-card-section {
              border: 1px solid #b9c0ca;
              border-radius: 16px;
              padding: 4px 6px;
              background: #f6f6f6;
              width: 90px;
              margin-top: 25px;
              cursor: pointer;

              .collapse-card {
                color: #45546e;
                font-family: var(--fontFamily);
                font-size: 12px;
                font-weight: 400;
                line-height: 15.62px;
                text-align: center;
              }
            }
          }
        }
      }
    }

    .card.expanded {
      grid-column: span 1;
      grid-row: span 2;
      height: var(--dynamicCardContentHeight);
    }
  }
}

.overlay-container {
  background: white;
  padding: 32px;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  position: relative;
  min-width: 436px;
  min-height: 268px;
  z-index: 1000;
  max-width: 465px;

  .heading-section {
    display: flex;
    justify-content: space-between;

    .header-text-values {
      .header-text {
        font-family: var(--fontFamily);
        font-size: 18px;
        font-weight: 700;
        line-height: 24px;
        letter-spacing: 0.02em;
        text-align: left;
      }
      .sub-header-text {
        color: #8b95a5;
        font-family: var(--fontFamily);
        font-size: 12px;
        font-weight: 400;
        line-height: 20px;
        text-align: left;
        margin-top: 4px;
      }
    }
  }
  .body-section {
    display: flex;
    flex-wrap: wrap;
    margin-top: 16px;
    gap: 8px;

    .individual-skill-items {
      background: #f6f6f6;
      width: fit-content;
      padding: 8px 16px;
      font-family: var(--fontFamily);
      font-size: 14px;
      font-weight: 500;
      line-height: 24px;
      letter-spacing: 0.02em;
      text-align: left;
      color: #45546e;
      border-radius: 8px;
    }

    .range-filter {
      display: flex;
      flex-direction: column;

      .range-inputs {
        display: flex;
        margin-top: 10px;

        .min {
          display: flex;
          flex-direction: column;
          padding: 0px 14px 0px 28px;

          label {
            font-family: var(--fontFamily);
            font-size: 12px;
            font-weight: 400;
            line-height: 16px;
            letter-spacing: 0.02em;
            text-align: left;
            color: #5f6c81;
          }

          input {
            width: 60px;
            padding: 5px;
            border: 1px solid #dadce2;
            border-radius: 4px;
            font-size: 12px;
            padding-left: 20px;
          }
        }

        .max {
          display: flex;
          flex-direction: column;
          label {
            font-family: var(--fontFamily);
            font-size: 12px;
            font-weight: 400;
            line-height: 16px;
            letter-spacing: 0.02em;
            text-align: left;
            color: #5f6c81;
          }

          input {
            width: 60px;
            padding: 5px;
            border: 1px solid #dadce2;
            border-radius: 4px;
            font-size: 12px;
            padding-left: 20px;
          }
        }

        input {
          width: 45%;
          padding: 5px;
          border: 1px solid #dadce2;
          border-radius: 4px;
          font-size: 12px;
        }
      }

      ::ng-deep .ngx-slider {
        .ngx-slider-track {
          background-color: #79ba44 !important; /* Track background color */
        }

        .ngx-slider-pointer {
          background-color: #79ba44 !important; /* Color of the slider thumb */
          border-color: #79ba44 !important; /* Border color of the thumb */
        }

        ::ng-deep .ngx-slider .ngx-slider-pointer {
          top: -5px !important;
          background-color: rgba(0, 0, 0, 0.12) !important;
        }

        .ngx-slider-selection {
          background-color: #79ba44 !important; /* Color of the filled portion of the slider */
        }

        .ngx-slider-tick {
          background-color: #79ba44 !important; /* Tick marks color */
        }

        ::ng-deep .ngx-slider-pointer {
          width: 12px !important; /* Set thumb width */
          height: 12px !important; /* Set thumb height */
          top: -5px !important;

          &::after {
            width: 0px !important;
            height: 0px !important;
          }
        }
      }
    }
  }
}

.close-btn {
  cursor: pointer;
}
