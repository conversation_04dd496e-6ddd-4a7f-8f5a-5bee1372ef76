import {
  Component,
  NgM<PERSON>ule,
  On<PERSON><PERSON>roy,
  OnInit,
  HostListener,
} from '@angular/core';
import Swal from 'sweetalert2';
import { Subject, Subscription } from 'rxjs';

@Component({
  selector: 'app-smart-allocation',
  templateUrl: './smart-allocation.component.html',
  styleUrls: ['./smart-allocation.component.scss'],
})
export class SmartAllocationComponent implements OnInit, OnDestroy {
  subs = new SubSink();
  isLoading: boolean = false;

  /**
   * @description Style related declarations
   */
  smartAllocationFontFamily: string;
  smartAllocationLoadingText: string;
  smartAllocationLoadingImage: string;
  smartAllocationButtonIcon: string;
  allocateButtonTextColor: string;
  allocateButtonBackgroundColor: string;
  allocateButtonColor: string;
  mainHeaderText: string;
  dynamicOverallHeight: any;

  /**
   * @description General declarations
   */
  reqId: any;
  searchText: any = '';
  listViewSelected: boolean = true;
  cardViewSelected: boolean = false;
  applicationId: number = 1000;
  internalApplicationId: string = 'people_allocation_smart_allocation';
  filterSubscription$: Subscription;
  filterData: any = {};
  fieldConfig: any;
  headerHeight: string = '40px';
  list: any = [];
  doughnutChartData: { [key: number]: any[] } = {};
  customPalette = ['#FFBD3D', '#13C2C2', '#A0D911'];
  isCenterHovered: boolean = false;
  currentTooltip: any;
  maxChipWidth = '40%';
  totalListCount = 0;
  isRequestDetailsExpanded: boolean = false;
  requestData: any;
  viewMoreData: any;
  minValue: number = 0;
  maxValue: number = 90;
  tempMinValue: number = this.minValue;
  tempMaxValue: number = this.maxValue;
  sliderOptions: Options = {
    floor: 0,
    ceil: 90,
    step: 1,
  };
  tempMaxInputValue: number;
  tempMinInputValue: number;

  /**
   * @description AI Filter related declarations
   */
  isFilterCollapsed: boolean = true;
  filterCount: number = 0;
  isPrefSkillFilterVisible: boolean = true;
  isExperienceFilterVisible: boolean = true;
  isNationalityFilterVisible: boolean = true;
  isGenderFilterVisible: boolean = true;
  isPositionFilterVisible: boolean = true;
  isRegionFilterVisible: boolean = true;
  isSkillsFilterVisible: boolean = true;
  isWorkLocationFilterVisible: boolean = true;
  isSubDivisionFilterVisible: boolean = true;
  isDivisionFilterVisible: boolean = true;
  isEntityFilterVisible: boolean = true;
  isEmploymentTypeFilterVisible: boolean = true;
  isHolidayCalendarFilterVisible: boolean = true;

  prefSkillChips: any = [];
  entityChips: any = [];
  entityChipIds: any = [];
  divisionChips: any = [];
  divisionChipIds: any = [];
  subDivisionChips: any = [];
  subDivisionChipIds: any = [];
  workLocationChips: any = [];
  workLocationChipIds: any = [];
  skillsChips: any = [];
  skillsChipIds: any = [];
  regionChips: any = [];
  regionChipIds: any = [];
  positionChips: any = [];
  positionChipIds: any = [];
  genderChips: any = [];
  genderChipIds: any = [];
  nationalityChips: any = [];
  nationalityChipIds: any = [];
  employmentTypeChips: any = [];
  employmentTypeChipIds: any = [];
  holidayCalendarChips: any = [];
  holidayCalendarChipIds: any = [];

  preferrenceFilterMaster = [];
  entityFilterMaster = [];
  divisionFilterMaster = [];
  subDivisionFilterMaster = [];
  workLocationFilterMaster = [];
  skillsFilterMaster = [];
  skillsFilterMasterInitial = [];
  regionFilterMaster = [];
  positionFilterMaster = [];
  genderFilterMaster = [];
  nationalityFilterMaster = [];
  employeeExperienceFilterMaster = [];
  defaultSkillDetails = [];
  employmentTypeFilterMaster = [];
  holidayCalendarFilterMaster = [];

  selectedPreferrenceFilter: string = '';
  selectedPreferrenceFilterId: number;
  selectedEntityFilter: string = '';
  selectedDivisionFilter: string = '';
  selectedSubDivisionFilter: string = '';
  selectedWorkLocationFilter: string = '';
  selectedSkillFilter: string = '';
  selectedRegionFilter: string = '';
  selectedPositionFilter: string = '';
  selectedGenderFilter: string = '';
  selectedNationalityFilter: string = '';
  selectedEmploymentFilter: string = '';
  selectedHolidayCalendarFilter: string = '';

  entityFilterPlaceholder: string = 'Select Entity';
  preferenceFilterPlaceholder: string = 'Select Preference';
  divisionFilterPlaceholder: string = 'Select Division';
  subDivisionFilterPlaceholder: string = 'Select Sub Division';
  workLocationFilterPlaceholder: string = 'Select Work Location';
  skillFilterPlaceholder: string = 'Select Skill';
  regionFilterPlaceholder: string = 'Select Region';
  positionFilterPlaceholder: string = 'Select Position';
  genderFilterPlaceholder: string = 'Select Gender';
  nationalityFilterPlaceholder: string = 'Select Nationality';
  employmentTypeFilterPlaceholder: string = 'Select Employment Type';
  holidayCalendarFilterPlaceholder: string = 'Select Holiday Calendar';

  @ViewChild('overlayContent') overlayTemplate!: TemplateRef<any>;
  private overlayRef?: OverlayRef;

  constructor(
    private _reqDetailService: RmRequestDetailService,
    private _router: Router,
    private _rmService: RmService,
    private route: ActivatedRoute,
    private _toaster: ToasterService,
    private _overlay: Overlay,
    private _viewContainerRef: ViewContainerRef
  ) {}

  async ngOnInit() {
    this.isLoading = true;
    await this.retrieveConfigData();
    await this.setDynamicHeight();
    this.reqId = parseInt(this._router.url.split('/')[6]);
    await this.determineFilterDetails();
    await this.getRequestDetails();
    await this.getViewMoredetails();
  }

  async retrieveConfigData() {
    return new Promise((resolve, reject) => {
      this.subs.sink = this._reqDetailService.retrieveRmConfig().subscribe(
        (res: any) => {
          if (!res.err) {
            if (res && res['data']) {
              this.smartAllocationFontFamily =
                res['data']['smart_allocation_style_configs'] &&
                res['data']['smart_allocation_style_configs']['font_family']
                  ? res['data']['smart_allocation_style_configs']['font_family']
                  : 'DM Sans';
              document.documentElement.style.setProperty(
                '--fontFamily',
                this.smartAllocationFontFamily
              );
              document.documentElement.style.setProperty(
                '--atsFontFamily',
                this.smartAllocationFontFamily
              );
              this.smartAllocationLoadingText =
                res['data']['smart_allocation_style_configs'] &&
                res['data']['smart_allocation_style_configs']['loading_text']
                  ? res['data']['smart_allocation_style_configs'][
                      'loading_text'
                    ]
                  : 'Initiating AI Matching...';
              this.smartAllocationLoadingImage =
                res['data']['smart_allocation_style_configs'] &&
                res['data']['smart_allocation_style_configs'][
                  'smartAllocationLoadingImage'
                ]
                  ? res['data']['smart_allocation_style_configs'][
                      'smartAllocationLoadingImage'
                    ]
                  : 'https://assets.kebs.app/ATS-AI-Eval_loader.gif';
              this.mainHeaderText =
                res['data']['smart_allocation_style_configs'] &&
                res['data']['smart_allocation_style_configs'][
                  'main_header_text'
                ]
                  ? res['data']['smart_allocation_style_configs'][
                      'main_header_text'
                    ]
                  : 'Smart AI Allocation';
              this.fieldConfig =
                res['data']['smart_allocation_list_view_configs'];
              this.smartAllocationButtonIcon =
                res['data']['smart_allocation_style_configs'] &&
                res['data']['smart_allocation_style_configs']['button_ai_icon']
                  ? res['data']['smart_allocation_style_configs'][
                      'button_ai_icon'
                    ]
                  : 'https://assets.kebs.app/ATS-AI-Eval-Icon.png';
              this.allocateButtonBackgroundColor = res['data'][
                'smart_allocation_style_configs'
              ]['allocate_btn_background']
                ? res['data']['smart_allocation_style_configs'][
                    'allocate_btn_background'
                  ]
                : '#272a47';
              document.documentElement.style.setProperty(
                '--allocateBtnBackground',
                this.allocateButtonBackgroundColor
              );
              this.allocateButtonColor = res['data'][
                'smart_allocation_style_configs'
              ]['allocate_btn_color']
                ? res['data']['smart_allocation_style_configs'][
                    'allocate_btn_color'
                  ]
                : '#FFFFFF';
              document.documentElement.style.setProperty(
                '--allocateBtnColor',
                this.allocateButtonColor
              );
              document.documentElement.style.setProperty(
                '--atsprimaryColor',
                '#79BA44'
              );
              document.documentElement.style.setProperty(
                '--atssecondaryColor',
                '#79BA44'
              );
            }
          } else {
            // Could not retrieve details from config, setting default values
            this.smartAllocationFontFamily = 'DM Sans';
            this.smartAllocationLoadingText = 'Initiating AI Matching...';
            this.smartAllocationLoadingImage =
              'https://assets.kebs.app/ATS-AI-Eval_loader.gif';
            this.mainHeaderText = 'Smart AI Allocation';
            this.smartAllocationButtonIcon =
              'https://assets.kebs.app/ATS-AI-Eval-Icon.png';
          }
          resolve(true);
        },
        (err) => {
          console.log(err);
        }
      );
    });
  }

  /**
   * @description Retrieve preferrence details for filter
   */
  async getPreferrenceDetailsForFilter() {
    return new Promise((resolve, reject) => {
      this.subs.sink = this._reqDetailService
        .getPreferrenceDetailsForFilter()
        .subscribe(
          (res: any) => {
            if (!res.err) {
              if (res && res['data']) {
                this.preferrenceFilterMaster = res['data'];
              }
            } else {
              // Could not retrieve details from config, setting default values
              this.preferrenceFilterMaster = [];
            }
            resolve(true);
          },
          (err) => {
            console.log(err);
          }
        );
    });
  }

  /**
   * @description Retrieve entity details for filter
   */
  async getEntityDetailsForFilter() {
    return new Promise((resolve, reject) => {
      this.subs.sink = this._reqDetailService
        .getEntityDetailsForFilter()
        .subscribe(
          (res: any) => {
            if (res.messType == 'S') {
              if (res && res['data']) {
                this.entityFilterMaster = res['data'];
              }
            } else {
              // Could not retrieve details from config, setting default values
              this.entityFilterMaster = [];
            }
            resolve(true);
          },
          (err) => {
            console.log(err);
          }
        );
    });
  }

  /**
   * @description Retrieve division details for filter
   */
  async getDivisionDetailsForFilter() {
    return new Promise((resolve, reject) => {
      this.subs.sink = this._reqDetailService
        .getDivisionDetailsForFilter()
        .subscribe(
          (res: any) => {
            if (res.messType == 'S') {
              if (res && res['data']) {
                this.divisionFilterMaster = res['data'];
              }
            } else {
              // Could not retrieve details from config, setting default values
              this.divisionFilterMaster = [];
            }
            resolve(true);
          },
          (err) => {
            console.log(err);
          }
        );
    });
  }

  /**
   * @description Retrieve sub division details for filter
   */
  async getSubDivisionDetailsForFilter() {
    return new Promise((resolve, reject) => {
      this.subs.sink = this._reqDetailService
        .getSubDivisionDetailsForFilter()
        .subscribe(
          (res: any) => {
            if (res.messType == 'S') {
              if (res && res['data']) {
                this.subDivisionFilterMaster = res['data'];
              }
            } else {
              // Could not retrieve details from config, setting default values
              this.subDivisionFilterMaster = [];
            }
            resolve(true);
          },
          (err) => {
            console.log(err);
          }
        );
    });
  }

  /**
   * @description Retrieve work location details for filter
   */
  async getWorkLocationDetailsForFilter() {
    return new Promise((resolve, reject) => {
      this.subs.sink = this._reqDetailService
        .getWorkLocationDetailsForFilter()
        .subscribe(
          (res: any) => {
            if (res.messType == 'S') {
              if (res && res['data']) {
                this.workLocationFilterMaster = res['data'];
              }
            } else {
              // Could not retrieve details from config, setting default values
              this.workLocationFilterMaster = [];
            }
            resolve(true);
          },
          (err) => {
            console.log(err);
          }
        );
    });
  }

  /**
   * @description Retrieve skill details for filter
   */
  async getSkillDetailsForFilter() {
    return new Promise((resolve, reject) => {
      this.subs.sink = this._reqDetailService
        .getSkillDetailsForFilter()
        .subscribe(
          (res: any) => {
            if (!res.err) {
              if (res && res['data']) {
                this.skillsFilterMaster = res['data'];
                this.skillsFilterMasterInitial = res['data'];
              }
            } else {
              // Could not retrieve details from config, setting default values
              this.skillsFilterMaster = [];
            }
            resolve(true);
          },
          (err) => {
            console.log(err);
          }
        );
    });
  }

  /**
   * @description Retrieve region details for filter
   */
  async getRegionDetailsForFilter() {
    return new Promise((resolve, reject) => {
      this.subs.sink = this._reqDetailService
        .getRegionDetailsForFilter()
        .subscribe(
          (res: any) => {
            if (res.messType == 'S') {
              if (res && res['data']) {
                this.regionFilterMaster = res['data'];
              }
            } else {
              // Could not retrieve details from config, setting default values
              this.regionFilterMaster = [];
            }
            resolve(true);
          },
          (err) => {
            console.log(err);
          }
        );
    });
  }

  /**
   * @description Retrieve position details for filter
   */
  async getPositionDetailsForFilter() {
    return new Promise((resolve, reject) => {
      this.subs.sink = this._reqDetailService
        .getPositionDetailsForFilter()
        .subscribe(
          (res: any) => {
            if (!res.err) {
              if (res && res['data']) {
                this.positionFilterMaster = res['data'];
              }
            } else {
              // Could not retrieve details from config, setting default values
              this.positionFilterMaster = [];
            }
            resolve(true);
          },
          (err) => {
            console.log(err);
          }
        );
    });
  }

  /**
   * @description Retrieve gender details for filter
   */
  async getGenderDetailsForFilter() {
    return new Promise((resolve, reject) => {
      this.subs.sink = this._reqDetailService
        .getGenderDetailsForFilter()
        .subscribe(
          (res: any) => {
            if (!res.err) {
              if (res && res['data']) {
                this.genderFilterMaster = res['data'];
              }
            } else {
              // Could not retrieve details from config, setting default values
              this.genderFilterMaster = [];
            }
            resolve(true);
          },
          (err) => {
            console.log(err);
          }
        );
    });
  }

  /**
   * @description Retrieve nationality details for filter
   */
  async getNationalityDetailsForFilter() {
    return new Promise((resolve, reject) => {
      this.subs.sink = this._reqDetailService
        .getNationalityDetailsForFilter()
        .subscribe(
          (res: any) => {
            if (!res.err) {
              if (res && res['data']) {
                this.nationalityFilterMaster = res['data'];
              }
            } else {
              // Could not retrieve details from config, setting default values
              this.nationalityFilterMaster = [];
            }
            resolve(true);
          },
          (err) => {
            console.log(err);
          }
        );
    });
  }

  /**
   * @description Retrieve nationality details for filter
   */
  async getEmploymentTypeDetailsForFilter() {
    return new Promise((resolve, reject) => {
      this.subs.sink = this._reqDetailService
        .getEmploymentTypeDetailsForFilter()
        .subscribe(
          (res: any) => {
            if (!res.err) {
              if (res && res['data']) {
                this.employmentTypeFilterMaster = res['data'];
              }
            } else {
              // Could not retrieve details from config, setting default values
              this.employmentTypeFilterMaster = [];
            }
            resolve(true);
          },
          (err) => {
            console.log(err);
          }
        );
    });
  }

  /**
   * @description Retrieve holiday calendar details for filter
   */
  async getHolidayCalendarForFilter() {
    return new Promise((resolve, reject) => {
      this.subs.sink = this._reqDetailService
        .getHolidayCalendarForFilter()
        .subscribe(
          (res: any) => {
            if (!res.err) {
              if (res && res['data']) {
                this.holidayCalendarFilterMaster = res['data'];
              }
            } else {
              // Could not retrieve details from config, setting default values
              this.holidayCalendarFilterMaster = [];
            }
            resolve(true);
          },
          (err) => {
            console.log(err);
          }
        );
    });
  }

  /**
   * @description Retrieve default skill based on request
   */
  async determineDefaultSkillDetails() {
    return new Promise((resolve, reject) => {
      this.subs.sink = this._reqDetailService
        .getDefaultSkillDetailsBasedOnRequest(this.reqId)
        .subscribe(
          (res: any) => {
            if (!res.err) {
              if (res && res['data']) {
                this.defaultSkillDetails = res['data'];
              }
            } else {
              // Could not retrieve details from config, setting default values
              this.defaultSkillDetails = [];
            }
            resolve(true);
          },
          (err) => {
            console.log(err);
          }
        );
    });
  }

  async setDynamicHeight() {
    // Overall Height
    document.documentElement.style.setProperty(
      '--dynamicOverallHeight',
      window.innerHeight - 57 - 25 + 'px'
    );

    // Dynamic body content height
    document.documentElement.style.setProperty(
      '--dynamicBodyHeight',
      window.innerHeight - 57 - 112 + 'px'
    );

    // Card View Dynamic content height
    document.documentElement.style.setProperty(
      '--dynamicCardContentHeight',
      window.innerHeight - 57 - 112 + 35 - 112 + 'px'
    );

    // card view internal scroll dynamic height
    document.documentElement.style.setProperty(
      '--dynamicInternalScrollHeight',
      window.innerHeight - 57 - 112 - 245 + 'px'
    );
  }

  goBacktoRequest(event) {
    if (event) {
      this._router.navigateByUrl(
        this._rmService.getRouterLink('resourceAllocation', this.reqId)
      );
      this._rmService.activeLink = 'resourceAllocation';
    }
  }

  isListViewClicked() {
    this.listViewSelected = true;
    this.cardViewSelected = false;
  }

  isCardViewClicked() {
    this.cardViewSelected = true;
    this.listViewSelected = false;
  }

  toggleFilter() {
    if (this.isRequestDetailsExpanded) {
      this.isRequestDetailsExpanded = false;
    }
    this.isFilterCollapsed = !this.isFilterCollapsed;
  }

  toggleRequestDetails() {
    if (!this.isFilterCollapsed) {
      this.isFilterCollapsed = true;
    }
    this.isRequestDetailsExpanded = !this.isRequestDetailsExpanded;
  }

  async determineFilterDetails() {
    await Promise.all([
      this.getPreferrenceDetailsForFilter(),
      this.getEntityDetailsForFilter(),
      this.getDivisionDetailsForFilter(),
      this.getSubDivisionDetailsForFilter(),
      this.getWorkLocationDetailsForFilter(),
      this.getSkillDetailsForFilter(),
      this.getRegionDetailsForFilter(),
      this.getPositionDetailsForFilter(),
      this.getGenderDetailsForFilter(),
      this.getNationalityDetailsForFilter(),
      this.getEmploymentTypeDetailsForFilter(),
      this.getHolidayCalendarForFilter(),
      this.determineDefaultSkillDetails(),
    ]);
  }

  async determineChartData() {
    for (let item of this.list) {
      let current_employee_id = item?.employee_aid;
      let data = [
        {
          field: 'Skill Set',
          value: item?.suitability_percentage[0]?.skill_set,
        },
        {
          field: 'Location',
          value: item?.suitability_percentage[0]?.location,
        },
        {
          field: 'Availability',
          value: item?.suitability_percentage[0]?.availability,
        },
      ];
      this.doughnutChartData[current_employee_id] = data;
    }
  }

  /**
   * @description Tooltip data upon hover for AI rank
   * @param {number} data
   */
  customizeTooltip = (data) => {
    // Dynamically generating the HTML for each field
    return {
      html: this.generateTooltipHtml(data),
    };
  };

  getDynamicRankBackground(data) {
    let backgroundColor =
      data.rank == 1 || data.rank == 2 || data.rank == 3
        ? data.rank == 1 || data.rank == 2
          ? data.rank == 1
            ? '#FFBD3D'
            : '#13C2C2'
          : '#FA541C'
        : '#D4D6D8';
    return `background: ${backgroundColor};`;
  }

  getDynamicRankColor(data) {
    let color =
      data.rank == 1 || data.rank == 2 || data.rank == 3
        ? '#FFFFFF'
        : '#5F6C81';
    return `color: ${color};`;
  }

  /**
   * @description To generate HTML content for tooltip
   */
  generateTooltipHtml(data) {
    let finalData = [
      {
        color: '#FFBD3D',
        argumentText: 'Skill Set',
        valueText:
          data?.suitability_percentage[0]?.skill_set !== undefined &&
          data?.suitability_percentage[0]?.skill_set != null
            ? data?.suitability_percentage[0]?.skill_set.toString()
            : 0,
      },
      {
        color: '#13C2C2',
        argumentText: 'Location',
        valueText:
          data?.suitability_percentage[0]?.location !== undefined &&
          data?.suitability_percentage[0]?.location != null
            ? data?.suitability_percentage[0]?.location.toString()
            : 100,
      },
      {
        color: '#A0D911',
        argumentText: 'Availability',
        valueText:
          data?.suitability_percentage[0]?.availability !== undefined &&
          data?.suitability_percentage[0]?.availability != null
            ? data?.suitability_percentage[0]?.availability.toString()
            : 100,
      },
    ];

    let individualChartData = [
      {
        field: 'Skill Set',
        value:
          data?.suitability_percentage[0]?.skill_set !== undefined &&
          data?.suitability_percentage[0]?.skill_set != null
            ? data?.suitability_percentage[0]?.skill_set
            : 0,
      },
      {
        field: 'Location',
        value:
          data?.suitability_percentage[0]?.location !== undefined &&
          data?.suitability_percentage[0]?.location != null
            ? data?.suitability_percentage[0]?.location
            : 100,
      },
      {
        field: 'Availability',
        value:
          data?.suitability_percentage[0]?.availability !== undefined &&
          data?.suitability_percentage[0]?.availability != null
            ? data?.suitability_percentage[0]?.availability
            : 100,
      },
    ];

    let tooltipHtml = ``;
    tooltipHtml += `
        <div class="tooltip-header">
          <div class="header-logo">
            <img  class="header-icon-image-styles" src= 'https://assets.kebs.app/ATS-AI-Eval-Icon.png'/>
          </div>
          <div class="header-text">
            Suitability
          </div>
        </div>
        <div class="tooltip-content-elements">
        <div id="tooltip-content-chart" style="padding-bottom: 16px">
        <div id="rankChartContainer" style="width: 127px; height: 127px; margin-left: 23%; margin-top: 3%;"></div>
         <div
          class="center-text"
          style="position: absolute; top: 40%; left: 50%; transform: translate(-50%, -50%);">
          ${Math.round(data?.overall_score ?? 0)}%
          </div>
          <div
            class="center-text-content"
            style="position: absolute; top: 45%; left: 50%; transform: translate(-50%, -50%);">
            Total
          </div>
          <div 
            class="rank-flag"
            style="position: absolute; top: 24%; left: 91%; transform: translate(-50%, -50%);">
            <div class="block-flag"
              style="${this.getDynamicRankBackground(data)}">
              <div class="rank-value"
                style="${this.getDynamicRankColor(data)}">
                  #${data.rank}
              </div>
            </div>
          </div>
          <div 
          class="employee-id"
          style="position: absolute; top: 24%; left: 12%; transform: translate(-50%, -50%);">
          #${data.employee_aid}
          </div>
        </div>
        ${finalData
          .map(
            (point) => `
            <div class="tooltip-item">
              <span class="color-dot" style="background-color: ${point.color};"></span>
              <div class="tooltip-content">
                <div class="text">${point.argumentText}</div>
                <div class="divider-line"></div>
                <div class="value">${point.valueText}%</div>
              </div>
            </div>
          `
          )
          .join('')}
        </div>
    `;

    setTimeout(() => {
      let chartContainerElement = document.getElementById('rankChartContainer');
      if (chartContainerElement) {
        new PieChart(chartContainerElement, {
          type: 'doughnut',
          dataSource: individualChartData,
          palette: this.customPalette,
          innerRadius: 0.7,
          series: [{ argumentField: 'field', valueField: 'value' }],
          legend: { visible: false },
        });
      }
    });

    return tooltipHtml;
  }

  onCenterHover(event, item) {
    this.isCenterHovered = true;
    this.showCenterTooltip(event, item);
  }

  onCenterLeave() {
    this.isCenterHovered = false;
    this.hideCenterTooltip();
  }

  showCenterTooltip(event, item) {
    if (this.currentTooltip) {
      this.currentTooltip.remove();
    }

    const tooltipHtml = this.generateTooltipHtml(item);

    const chartContainer = document.querySelector('.pie-chart');
    const tooltipElement = document.createElement('div');
    tooltipElement.innerHTML = tooltipHtml;
    tooltipElement.className = 'custom-tooltip';

    chartContainer.appendChild(tooltipElement);

    const tooltipRect = tooltipElement.getBoundingClientRect();

    const listContainer = document.getElementById('overall-list-container');
    const containerScrollY = listContainer ? listContainer.scrollTop : 0;
    const targetRect = event.target.getBoundingClientRect();
    const containerRect = listContainer.getBoundingClientRect();

    let containerHeight = listContainer ? listContainer.clientHeight : 0;

    let tooltipX = 15 - (tooltipRect.width + targetRect.width);

    let tooltipY =
      targetRect.top +
      containerScrollY -
      containerRect.top +
      targetRect.height / 2 -
      tooltipRect.height / 2;

    const availableSpaceBelow =
      containerHeight - (tooltipY + tooltipRect.height) - 10;

    const availableSpaceAbove = tooltipY;

    if (availableSpaceBelow < 0 && availableSpaceAbove >= 0) {
      tooltipY =
        targetRect.top +
        containerScrollY -
        containerRect.top -
        tooltipRect.height -
        10;
    }

    if (tooltipY < 0) {
      tooltipY = 10 + containerScrollY;
    }

    tooltipY = Math.max(
      10 + containerScrollY,
      Math.min(tooltipY, containerHeight - tooltipRect.height - 10)
    );

    tooltipElement.style.position = 'absolute';
    tooltipElement.style.top = `${tooltipY}px`;
    tooltipElement.style.left = `${tooltipX}px`;

    this.currentTooltip = tooltipElement;
  }

  hideCenterTooltip() {
    if (this.currentTooltip) {
      this.currentTooltip.remove();
      this.currentTooltip = null;
    }
  }

  @HostListener('window:resize')
  onResize() {
    this.setDynamicHeight();
  }

  actionTriggered(item) {}

  expandCard(card: any, index: number) {
    // Reset other cards
    this.list.forEach((c) => (c.isExpanded = false));

    // Set the selected card as expanded
    card.isExpanded = true;

    // Move the expanded card to the first position
    this.list.splice(index, 1);
    this.list.unshift(card);
  }

  collapseCard(card: any, index: number) {
    card.isExpanded = false;
  }

  togglePin(card: any) {
    if (card.isPinned) {
      card.isPinned = false;
      this.list.sort((a, b) => a.rank - b.rank);
    } else {
      this.list.forEach((c) => (c.isPinned = false));
      card.isPinned = true;
      this.list.sort((a, b) => {
        if (a.isPinned) return -1;
        if (b.isPinned) return 1;
        return a.rank - b.rank;
      });
    }
  }

  /**
   * @description Filter Toggle functions
   */

  toggleprefskillFilter() {
    this.isPrefSkillFilterVisible = !this.isPrefSkillFilterVisible;
  }

  toggleEntityFilter() {
    this.isEntityFilterVisible = !this.isEntityFilterVisible;
  }

  toggleDivisionFilter() {
    this.isDivisionFilterVisible = !this.isDivisionFilterVisible;
  }

  toggleSubDivisionFilter() {
    this.isSubDivisionFilterVisible = !this.isSubDivisionFilterVisible;
  }

  toggleWorkLocationFilter() {
    this.isWorkLocationFilterVisible = !this.isWorkLocationFilterVisible;
  }

  toggleSkillsFilter() {
    this.isSkillsFilterVisible = !this.isSkillsFilterVisible;
  }

  toggleRegionFilter() {
    this.isRegionFilterVisible = !this.isRegionFilterVisible;
  }

  togglePositionFilter() {
    this.isPositionFilterVisible = !this.isPositionFilterVisible;
  }

  toggleGenderFilter() {
    this.isGenderFilterVisible = !this.isGenderFilterVisible;
  }

  toggleNationalityFilter() {
    this.isNationalityFilterVisible = !this.isNationalityFilterVisible;
  }

  toggleEmploymentTypeFilter() {
    this.isEmploymentTypeFilterVisible = !this.isEmploymentTypeFilterVisible;
  }

  toggleHolidayCalendarFilter() {
    this.isHolidayCalendarFilterVisible = !this.isHolidayCalendarFilterVisible;
  }

  toggleExperienceFilter() {
    this.isExperienceFilterVisible = !this.isExperienceFilterVisible;
  }

  /**
   * @description Add filter functions
   */

  addPreferrenceChip(event) {
    if (this.prefSkillChips && this.prefSkillChips.length == 0) {
      this.filterCount += 1;
    }
    let preferrenceId = event.val;
    if (preferrenceId) {
      let preferrenceName = this.preferrenceFilterMaster.find(
        (preferrence) => preferrence.id == preferrenceId
      );
      if (preferrenceName) {
        this.selectedPreferrenceFilter = preferrenceName?.name;
        this.selectedPreferrenceFilterId = preferrenceName?.id;
        if (
          this.prefSkillChips &&
          !this.prefSkillChips.includes(this.selectedPreferrenceFilter)
        ) {
          if (this.prefSkillChips && this.prefSkillChips.length > 0) {
            this.prefSkillChips = [];
          }
          this.prefSkillChips.push(this.selectedPreferrenceFilter);
        }
      }
      // this.selectedPreferrenceFilter = '';
      // this.selectedPreferrenceFilterId = 0;
    }
  }

  addEntityChip(event: { data: any; val: any }): void {
    const { data, val } = event;
    let newEntityIds = val || [];

    if (!val || (val && val.length === 0)) {
      this.entityChips = [];
      this.entityChipIds = [];
      this.filterCount -= 1;
      return;
    }

    for (let entityId of newEntityIds) {
      let entityName = this.entityFilterMaster.find(
        (entity) => entity.id === entityId
      );

      if (entityName) {
        const entityNameText = entityName?.name;
        const selectedEntityId = entityName?.id;

        if (entityNameText && !this.entityChips.includes(entityNameText)) {
          this.entityChips.push(entityNameText);
          this.entityChipIds.push(selectedEntityId);
          if (this.entityChips.length === 1) {
            this.filterCount += 1;
          }
        }
      }
    }

    const deselectedIds = this.entityChipIds.filter(
      (id) => !newEntityIds.includes(id)
    );
    for (let id of deselectedIds) {
      const index = this.entityChipIds.indexOf(id);
      if (index > -1) {
        this.entityChipIds.splice(index, 1);
        this.entityChips.splice(index, 1);
      }
    }

    if (this.entityChips.length === 0) {
      this.filterCount -= 1;
    }
  }

  addDivisionChip(event: { data: any; val: any }): void {
    const { data, val } = event;
    let newDivisionIds = val || [];

    if (!val || (val && val.length === 0)) {
      this.divisionChips = [];
      this.divisionChipIds = [];
      this.filterCount -= 1;
      return;
    }

    for (let divisionId of newDivisionIds) {
      let divisionName = this.divisionFilterMaster.find(
        (division) => division.id === divisionId
      );

      if (divisionName) {
        const divisionNameText = divisionName?.name;
        const selectedDivisionId = divisionName?.id;

        if (
          divisionNameText &&
          !this.divisionChips.includes(divisionNameText)
        ) {
          this.divisionChips.push(divisionNameText);
          this.divisionChipIds.push(selectedDivisionId);
          if (this.divisionChips.length === 1) {
            this.filterCount += 1;
          }
        }
      }
    }

    const deselectedIds = this.divisionChipIds.filter(
      (id) => !newDivisionIds.includes(id)
    );
    for (let id of deselectedIds) {
      const index = this.divisionChipIds.indexOf(id);
      if (index > -1) {
        this.divisionChipIds.splice(index, 1);
        this.divisionChips.splice(index, 1);
      }
    }

    if (this.divisionChips.length === 0) {
      this.filterCount -= 1;
    }
  }

  addSubDivisionChip(event: { data: any; val: any }): void {
    const { data, val } = event;
    let newSubDivisionIds = val || [];

    if (!val || (val && val.length === 0)) {
      this.subDivisionChips = [];
      this.subDivisionChipIds = [];
      this.filterCount -= 1;
      return;
    }

    for (let subDivisionId of newSubDivisionIds) {
      let subDivisionName = this.subDivisionFilterMaster.find(
        (division) => division.id === subDivisionId
      );

      if (subDivisionName) {
        const subDivisionNameText = subDivisionName?.name;
        const selectedSubDivisionId = subDivisionName?.id;

        if (
          subDivisionNameText &&
          !this.subDivisionChips.includes(subDivisionNameText)
        ) {
          this.subDivisionChips.push(subDivisionNameText);
          this.subDivisionChipIds.push(selectedSubDivisionId);
          if (this.subDivisionChips.length === 1) {
            this.filterCount += 1;
          }
        }
      }
    }

    const deselectedIds = this.subDivisionChipIds.filter(
      (id) => !newSubDivisionIds.includes(id)
    );
    for (let id of deselectedIds) {
      const index = this.subDivisionChipIds.indexOf(id);
      if (index > -1) {
        this.subDivisionChipIds.splice(index, 1);
        this.subDivisionChips.splice(index, 1);
      }
    }

    if (this.subDivisionChips.length === 0) {
      this.filterCount -= 1;
    }
  }

  addWorkLocationChip(event: { data: any; val: any }): void {
    const { data, val } = event;
    let newWorkLocationIds = val || [];

    if (!val || (val && val.length === 0)) {
      this.workLocationChips = [];
      this.workLocationChipIds = [];
      this.filterCount -= 1;
      return;
    }

    for (let locationId of newWorkLocationIds) {
      let workLocationName = this.workLocationFilterMaster.find(
        (location) => location.id === locationId
      );

      if (workLocationName) {
        const workLocationNameText = workLocationName?.name;
        const selectedworkLocationId = workLocationName?.id;

        if (
          workLocationNameText &&
          !this.workLocationChips.includes(workLocationNameText)
        ) {
          this.workLocationChips.push(workLocationNameText);
          this.workLocationChipIds.push(selectedworkLocationId);
          if (this.workLocationChips.length === 1) {
            this.filterCount += 1;
          }
        }
      }
    }

    const deselectedIds = this.workLocationChipIds.filter(
      (id) => !newWorkLocationIds.includes(id)
    );
    for (let id of deselectedIds) {
      const index = this.workLocationChipIds.indexOf(id);
      if (index > -1) {
        this.workLocationChipIds.splice(index, 1);
        this.workLocationChips.splice(index, 1);
      }
    }

    if (this.workLocationChips.length === 0) {
      this.filterCount -= 1;
    }
  }

  addSkillsChip(event: { val: any }): void {
    const { val } = event;
    const newSkillIds = val || [];

    // If no value is selected, reset everything
    if (!val || val.length === 0) {
      this.skillsChips = [];
      this.skillsChipIds = [];
      this.filterCount -= 1;
      this.skillsFilterMaster = [...this.skillsFilterMasterInitial];
      return;
    }

    for (let skillId of newSkillIds) {
      const skill = this.skillsFilterMaster.find((item) => item.id === skillId);

      if (skill) {
        const skillName = skill.name;
        const skillIdValue = skill.skill_id;
        this.skillsFilterMaster = this.skillsFilterMaster.filter(
          (item) => item.skill_id !== skillIdValue || item.id === skillId
        );

        if (!this.skillsChips.includes(skillName)) {
          this.skillsChips.push(skillName);
          this.skillsChipIds.push(skillId);
          if (this.skillsChips.length === 1) {
            this.filterCount += 1;
          }
        }
      }
    }

    const deselectedIds = this.skillsChipIds.filter(
      (id) => !newSkillIds.includes(id)
    );
    for (let id of deselectedIds) {
      const index = this.skillsChipIds.indexOf(id);
      if (index > -1) {
        this.skillsChipIds.splice(index, 1);
        this.skillsChips.splice(index, 1);

        const skillIdValue = this.skillsFilterMasterInitial.find(
          (item) => item.id === id
        )?.skill_id;
        if (skillIdValue) {
          const matchingSkills = this.skillsFilterMasterInitial.filter(
            (item) => item.skill_id === skillIdValue
          );
          this.skillsFilterMaster = [
            ...this.skillsFilterMaster,
            ...matchingSkills.filter(
              (item) => !this.skillsFilterMaster.some((m) => m.id === item.id)
            ),
          ];
        }
      }
    }

    if (this.skillsChips.length === 0) {
      this.filterCount -= 1;
    }
  }

  addRegionChip(event: { data: any; val: any }): void {
    const { data, val } = event;
    let newRegionIds = val || [];

    if (!val || (val && val.length === 0)) {
      this.regionChips = [];
      this.regionChipIds = [];
      this.filterCount -= 1;
      return;
    }

    for (let regionId of newRegionIds) {
      let regionName = this.regionFilterMaster.find(
        (region) => region.id === regionId
      );

      if (regionName) {
        const regionNameText = regionName?.name;
        const selectedRegionId = regionName?.id;

        if (regionNameText && !this.regionChips.includes(regionNameText)) {
          this.regionChips.push(regionNameText);
          this.regionChipIds.push(selectedRegionId);
          if (this.regionChips.length === 1) {
            this.filterCount += 1;
          }
        }
      }
    }

    const deselectedIds = this.regionChipIds.filter(
      (id) => !newRegionIds.includes(id)
    );
    for (let id of deselectedIds) {
      const index = this.regionChipIds.indexOf(id);
      if (index > -1) {
        this.regionChipIds.splice(index, 1);
        this.regionChips.splice(index, 1);
      }
    }

    if (this.regionChips.length === 0) {
      this.filterCount -= 1;
    }
  }

  addPositionChip(event: { data: any; val: any }): void {
    const { data, val } = event;
    let newPositionIds = val || [];

    if (!val || (val && val.length === 0)) {
      this.positionChips = [];
      this.positionChipIds = [];
      this.filterCount -= 1;
      return;
    }

    for (let positionId of newPositionIds) {
      let positionName = this.positionFilterMaster.find(
        (position) => position.id === positionId
      );

      if (positionName) {
        const positionNameText = positionName?.name;
        const selectedPositionId = positionName?.id;

        if (
          positionNameText &&
          !this.positionChips.includes(positionNameText)
        ) {
          this.positionChips.push(positionNameText);
          this.positionChipIds.push(selectedPositionId);
          if (this.positionChips.length === 1) {
            this.filterCount += 1;
          }
        }
      }
    }

    const deselectedIds = this.positionChipIds.filter(
      (id) => !newPositionIds.includes(id)
    );
    for (let id of deselectedIds) {
      const index = this.positionChipIds.indexOf(id);
      if (index > -1) {
        this.positionChipIds.splice(index, 1);
        this.positionChips.splice(index, 1);
      }
    }

    if (this.positionChips.length === 0) {
      this.filterCount -= 1;
    }
  }

  addGenderChip(event: { data: any; val: any }): void {
    const { data, val } = event;
    let newGenderIds = val || [];

    if (!val || (val && val.length === 0)) {
      this.genderChips = [];
      this.genderChipIds = [];
      this.filterCount -= 1;
      return;
    }

    for (let genderId of newGenderIds) {
      let genderName = this.genderFilterMaster.find(
        (gender) => gender.id === genderId
      );

      if (genderName) {
        const genderNameText = genderName?.name;
        const selectedGenderId = genderName?.id;

        if (genderNameText && !this.genderChips.includes(genderNameText)) {
          this.genderChips.push(genderNameText);
          this.genderChipIds.push(selectedGenderId);
          if (this.genderChips.length === 1) {
            this.filterCount += 1;
          }
        }
      }
    }

    const deselectedIds = this.genderChipIds.filter(
      (id) => !newGenderIds.includes(id)
    );
    for (let id of deselectedIds) {
      const index = this.genderChipIds.indexOf(id);
      if (index > -1) {
        this.genderChipIds.splice(index, 1);
        this.genderChips.splice(index, 1);
      }
    }

    if (this.genderChips.length === 0) {
      this.filterCount -= 1;
    }
  }

  addNationalityChip(event: { data: any; val: any }): void {
    const { data, val } = event;
    let newNationalityIds = val || [];

    if (!val || (val && val.length === 0)) {
      this.nationalityChips = [];
      this.nationalityChipIds = [];
      this.filterCount -= 1;
      return;
    }

    for (let nationalityId of newNationalityIds) {
      let nationalityName = this.nationalityFilterMaster.find(
        (nationality) => nationality.id === nationalityId
      );

      if (nationalityName) {
        const nationalityNameText = nationalityName?.name;
        const selectedNationalityId = nationalityName?.id;

        if (
          nationalityNameText &&
          !this.nationalityChips.includes(nationalityNameText)
        ) {
          this.nationalityChips.push(nationalityNameText);
          this.nationalityChipIds.push(selectedNationalityId);
          if (this.nationalityChips.length === 1) {
            this.filterCount += 1;
          }
        }
      }
    }

    const deselectedIds = this.nationalityChipIds.filter(
      (id) => !newNationalityIds.includes(id)
    );
    for (let id of deselectedIds) {
      const index = this.nationalityChipIds.indexOf(id);
      if (index > -1) {
        this.nationalityChipIds.splice(index, 1);
        this.nationalityChips.splice(index, 1);
      }
    }

    if (this.nationalityChips.length === 0) {
      this.filterCount -= 1;
    }
  }

  addEmploymentTypeChip(event: { data: any; val: any }): void {
    const { data, val } = event;
    let newEmploymentTypeIds = val || [];

    if (!val || (val && val.length === 0)) {
      this.employmentTypeChips = [];
      this.employmentTypeChipIds = [];
      this.filterCount -= 1;
      return;
    }

    for (let employmentTypeId of newEmploymentTypeIds) {
      let employmentType = this.employmentTypeFilterMaster.find(
        (empType) => empType.id === employmentTypeId
      );

      if (employmentType) {
        const employmentTypeNameText = employmentType?.name;
        const selectedemploymentTypeId = employmentType?.id;

        if (
          employmentTypeNameText &&
          !this.employmentTypeChips.includes(employmentTypeNameText)
        ) {
          this.employmentTypeChips.push(employmentTypeNameText);
          this.employmentTypeChipIds.push(selectedemploymentTypeId);
          if (this.employmentTypeChips.length === 1) {
            this.filterCount += 1;
          }
        }
      }
    }

    const deselectedIds = this.employmentTypeChipIds.filter(
      (id) => !this.employmentTypeChipIds.includes(id)
    );
    for (let id of deselectedIds) {
      const index = this.employmentTypeChipIds.indexOf(id);
      if (index > -1) {
        this.employmentTypeChipIds.splice(index, 1);
        this.employmentTypeChips.splice(index, 1);
      }
    }

    if (this.employmentTypeChips.length === 0) {
      this.filterCount -= 1;
    }
  }

  addHolidayCalendarChip(event: { data: any; val: any }): void {
    const { data, val } = event;
    let newHolidaycalendarIds = val || [];

    if (!val || (val && val.length === 0)) {
      this.holidayCalendarChips = [];
      this.holidayCalendarChipIds = [];
      this.filterCount -= 1;
      return;
    }

    for (let holidayCalendarId of newHolidaycalendarIds) {
      let holidayCalender = this.holidayCalendarFilterMaster.find(
        (holidayCal) => holidayCal.id === holidayCalendarId
      );

      if (holidayCalender) {
        const holidayCalendarNameText = holidayCalender?.name;
        const selectedholidayCalendarId = holidayCalender?.id;

        if (
          holidayCalendarNameText &&
          !this.holidayCalendarChips.includes(holidayCalendarNameText)
        ) {
          this.holidayCalendarChips.push(holidayCalendarNameText);
          this.holidayCalendarChipIds.push(selectedholidayCalendarId);
          if (this.holidayCalendarChips.length === 1) {
            this.filterCount += 1;
          }
        }
      }
    }

    const deselectedIds = this.holidayCalendarChipIds.filter(
      (id) => !this.holidayCalendarChipIds.includes(id)
    );
    for (let id of deselectedIds) {
      const index = this.holidayCalendarChipIds.indexOf(id);
      if (index > -1) {
        this.holidayCalendarChipIds.splice(index, 1);
        this.holidayCalendarChips.splice(index, 1);
      }
    }

    if (this.holidayCalendarChips.length === 0) {
      this.filterCount -= 1;
    }
  }
  /**
   * @description Reset filter functions
   */

  resetPrefSkillFilter() {
    this.selectedPreferrenceFilter = '';
    if (this.prefSkillChips.length > 0) {
      this.filterCount -= 1;
      this.prefSkillChips = [];
    }
    this.selectedPreferrenceFilterId = 0;
  }

  resetEntityFilter() {
    this.selectedEntityFilter = '';
    if (this.entityChips.length > 0) {
      this.filterCount -= 1;
      this.entityChips = [];
      this.entityChipIds = [];
    }
  }

  resetDivisionFilter() {
    this.selectedDivisionFilter = '';
    if (this.divisionChips.length > 0) {
      this.filterCount -= 1;
      this.divisionChips = [];
      this.divisionChipIds = [];
    }
  }

  resetSubDivisionFilter() {
    this.selectedSubDivisionFilter = '';
    if (this.subDivisionChips.length > 0) {
      this.filterCount -= 1;
      this.subDivisionChips = [];
      this.subDivisionChipIds = [];
    }
  }

  resetWorkLocationFilter() {
    this.selectedWorkLocationFilter = '';
    if (this.workLocationChips.length > 0) {
      this.filterCount -= 1;
      this.workLocationChips = [];
      this.workLocationChipIds = [];
    }
  }

  resetSkillFilter() {
    this.selectedSkillFilter = '';
    if (this.skillsChips.length > 0) {
      this.filterCount -= 1;
      this.skillsChips = [];
      this.skillsChipIds = [];
      this.skillsFilterMaster = this.skillsFilterMasterInitial;
    }
  }

  resetRegionFilter() {
    this.selectedRegionFilter = '';
    if (this.regionChips.length > 0) {
      this.filterCount -= 1;
      this.regionChips = [];
      this.regionChipIds = [];
    }
  }

  resetPositionFilter() {
    this.selectedPositionFilter = '';
    if (this.positionChips.length > 0) {
      this.filterCount -= 1;
      this.positionChips = [];
      this.positionChipIds = [];
    }
  }

  resetGenderFilter() {
    this.selectedGenderFilter = '';
    if (this.genderChips.length > 0) {
      this.filterCount -= 1;
      this.genderChips = [];
      this.genderChipIds = [];
    }
  }

  resetNationalityFilter() {
    this.selectedNationalityFilter = '';
    if (this.nationalityChips.length > 0) {
      this.filterCount -= 1;
      this.nationalityChips = [];
      this.nationalityChipIds = [];
    }
  }

  resetEmploymentTypeFilter() {
    this.selectedEmploymentFilter = '';
    if (this.employmentTypeChips.length > 0) {
      this.filterCount -= 1;
      this.employmentTypeChips = [];
      this.employmentTypeChipIds = [];
    }
  }

  resetHolidayCalendarFilter() {
    this.selectedHolidayCalendarFilter = '';
    if (this.holidayCalendarChips.length > 0) {
      this.filterCount -= 1;
      this.holidayCalendarChips = [];
      this.holidayCalendarChipIds = [];
    }
  }

  /**
   * @description Remove selected filter functions
   */

  removePrefSkillChip(chip: string) {
    this.prefSkillChips = this.prefSkillChips.filter((item) => item !== chip);
    if (this.prefSkillChips && this.prefSkillChips.length == 0) {
      this.selectedPreferrenceFilterId = 0;
      this.filterCount -= 1;
    }
  }

  removeEntityChip(chip: string) {
    this.entityChips = this.entityChips.filter((item) => item !== chip);
    let entity = this.entityFilterMaster.find((entity) => entity.name == chip);
    if (entity) {
      this.entityChipIds = this.entityChipIds.filter(
        (item) => item !== entity?.id
      );
    }
    if (this.entityChips && this.entityChips.length == 0) {
      this.filterCount -= 1;
    }
  }

  removeDivisionChip(chip: string) {
    this.divisionChips = this.divisionChips.filter((item) => item !== chip);
    let division = this.divisionFilterMaster.find(
      (division) => division.name == chip
    );
    if (division) {
      this.divisionChipIds = this.divisionChipIds.filter(
        (item) => item !== division?.id
      );
    }
    if (this.divisionChips && this.divisionChips.length == 0) {
      this.filterCount -= 1;
    }
  }

  removeSubDivisionChip(chip: string) {
    this.subDivisionChips = this.subDivisionChips.filter(
      (item) => item !== chip
    );
    let subDivision = this.subDivisionFilterMaster.find(
      (division) => division.name == chip
    );
    if (subDivision) {
      this.subDivisionChipIds = this.subDivisionChipIds.filter(
        (item) => item !== subDivision?.id
      );
    }
    if (this.subDivisionChips && this.subDivisionChips.length == 0) {
      this.filterCount -= 1;
    }
  }

  removeWorkLocationChip(chip: string) {
    this.workLocationChips = this.workLocationChips.filter(
      (item) => item !== chip
    );
    let workLocation = this.workLocationFilterMaster.find(
      (location) => location.name == chip
    );
    if (workLocation) {
      this.workLocationChipIds = this.workLocationChipIds.filter(
        (item) => item !== workLocation?.id
      );
    }
    if (this.workLocationChips && this.workLocationChips.length == 0) {
      this.filterCount -= 1;
    }
  }

  removeSkillChip(chip: string): void {
    this.skillsChips = this.skillsChips.filter((item) => item !== chip);

    const skill = this.skillsFilterMasterInitial.find(
      (skill) => skill.name === chip
    );

    if (skill) {
      const skillIdValue = skill.skill_id;

      this.skillsChipIds = this.skillsChipIds.filter((id) => id !== skill.id);

      const matchingSkills = this.skillsFilterMasterInitial.filter(
        (item) => item.skill_id === skillIdValue
      );
      this.skillsFilterMaster = [
        ...this.skillsFilterMaster,
        ...matchingSkills.filter(
          (item) => !this.skillsFilterMaster.some((m) => m.id === item.id)
        ),
      ];
    }

    if (this.skillsChips.length === 0) {
      this.filterCount -= 1;
    }
  }

  removeRegionChip(chip: string) {
    this.regionChips = this.regionChips.filter((item) => item !== chip);
    let region = this.regionFilterMaster.find((region) => region.name == chip);
    if (region) {
      this.regionChipIds = this.regionChipIds.filter(
        (item) => item !== region?.id
      );
    }
    if (this.regionChips && this.regionChipIds.length == 0) {
      this.filterCount -= 1;
    }
  }

  removePositionChip(chip: string) {
    this.positionChips = this.positionChips.filter((item) => item !== chip);
    let position = this.positionFilterMaster.find(
      (position) => position.name == chip
    );
    if (position) {
      this.positionChipIds = this.positionChipIds.filter(
        (item) => item !== position?.id
      );
    }
    if (this.positionChips && this.positionChipIds.length == 0) {
      this.filterCount -= 1;
    }
  }

  removeGenderChip(chip: string) {
    this.genderChips = this.genderChips.filter((item) => item !== chip);
    let gender = this.genderFilterMaster.find((gend) => gend.name == chip);
    if (gender) {
      this.genderChipIds = this.genderChipIds.filter(
        (item) => item !== gender?.id
      );
    }
    if (this.genderChips && this.genderChipIds.length == 0) {
      this.filterCount -= 1;
    }
  }

  removeNationalityChip(chip: string) {
    this.nationalityChips = this.nationalityChips.filter(
      (item) => item !== chip
    );
    let nationality = this.nationalityFilterMaster.find(
      (nationality) => nationality.name == chip
    );
    if (nationality) {
      this.nationalityChipIds = this.nationalityChipIds.filter(
        (item) => item !== nationality?.id
      );
    }
    if (this.nationalityChips && this.nationalityChipIds.length == 0) {
      this.filterCount -= 1;
    }
  }

  removeEmploymentTypeChip(chip: string) {
    this.employmentTypeChips = this.employmentTypeChips.filter(
      (item) => item !== chip
    );
    let employmentType = this.employmentTypeFilterMaster.find(
      (empType) => empType.name == chip
    );
    if (employmentType) {
      this.employmentTypeChipIds = this.employmentTypeChipIds.filter(
        (item) => item !== employmentType?.id
      );
    }
    if (this.employmentTypeChips && this.employmentTypeChipIds.length == 0) {
      this.filterCount -= 1;
    }
  }

  removeHolidayCalendarChip(chip: string) {
    this.holidayCalendarChips = this.holidayCalendarChips.filter(
      (item) => item !== chip
    );
    let holidayCalendar = this.holidayCalendarFilterMaster.find(
      (holiday) => holiday.name == chip
    );
    if (holidayCalendar) {
      this.holidayCalendarChipIds = this.holidayCalendarChipIds.filter(
        (item) => item !== holidayCalendar?.id
      );
    }
    if (this.holidayCalendarChips && this.holidayCalendarChipIds.length == 0) {
      this.filterCount -= 1;
    }
  }

  updateMinMaxValues(): void {
    this.tempMinInputValue = this.tempMinValue;
    this.tempMaxInputValue = this.tempMaxValue;
    this.minValue = this.tempMinInputValue;
    this.maxValue = this.tempMaxInputValue;
  }

  async getRequestDetails() {
    this.isLoading = true;
    let requestParams = {
      id: this.reqId,
    };
    this.subs.sink = this._rmService
      .getRequestDetailData(requestParams)
      .subscribe(
        (res: any) => {
          if (res) {
            if (res['data']) {
              this.requestData = res['data'];
              // Formatting start date and end date for request display
              this.requestData['start_date_formatted'] = this.requestData[
                'start_date'
              ]
                ? moment(this.requestData['start_date']).format('DD MMM YYYY')
                : null;
              this.requestData['end_date_formatted'] = this.requestData[
                'end_date'
              ]
                ? moment(this.requestData['end_date']).format('DD MMM YYYY')
                : null;
              this.requestData['expected_closure_date_formatted'] = this
                .requestData['expected_closure_date']
                ? moment(this.requestData['expected_closure_date']).format(
                    'DD-MMM-YYYY'
                  )
                : null;
              this.requestData['start_date_for_req_formatted'] = this
                .requestData['start_date']
                ? moment(this.requestData['start_date']).format('DD-MMM-YYYY')
                : null;
              this.requestData['end_date_for_req_formatted'] = this.requestData[
                'end_date'
              ]
                ? moment(this.requestData['end_date']).format('DD-MMM-YYYY')
                : null;
              this.setDefaultAIFiltersBasedOnRequest(this.requestData);
              this.getSmartAllocationDetails(this.requestData);
            } else {
              this._toaster.showError(
                'System Failure',
                'Could not retrieve request details!',
                3000
              );
              this.isLoading = false;
            }
          }
        },
        (err) => {
          console.log(err);
          this.isLoading = false;
        }
      );
  }

  async getViewMoredetails() {
    let requestParams = {
      id: this.reqId,
    };
    this.subs.sink = this._rmService.getViewMoreDetail(requestParams).subscribe(
      (res: any) => {
        if (res) {
          if (res['data']) {
            this.viewMoreData = res['data'];
          } else {
            this._toaster.showError(
              'System Failure',
              'Could not retrieve more details!',
              3000
            );
          }
        }
      },
      (err) => {
        console.log(err);
      }
    );
  }

  applyInputChange(): void {
    const minInputValue = Number(this.tempMinInputValue);
    const maxInputValue = Number(this.tempMaxInputValue);

    if (minInputValue < this.sliderOptions.floor) {
      this.tempMinInputValue = this.sliderOptions.floor;
    } else {
      this.tempMinInputValue = minInputValue;
    }

    if (maxInputValue > this.sliderOptions.ceil) {
      this.tempMaxInputValue = this.sliderOptions.ceil;
    } else {
      this.tempMaxInputValue = maxInputValue;
    }

    if (this.tempMinInputValue > this.tempMaxInputValue) {
      this.tempMaxInputValue = this.tempMinInputValue;
    }

    if (this.tempMaxInputValue < this.tempMinInputValue) {
      this.tempMinInputValue = this.tempMaxInputValue;
    }

    this.minValue = this.tempMinInputValue;
    this.maxValue = this.tempMaxInputValue;
  }

  onInputMinBlur(): void {
    this.tempMinValue = this.tempMinInputValue;
    this.applyInputChange();
  }

  onInputMaxBlur(): void {
    this.tempMaxValue = this.tempMaxInputValue;
    this.applyInputChange();
  }

  async setDefaultAIFiltersBasedOnRequest(requestData) {
    let position =
      requestData && requestData['POSITION'] ? requestData['POSITION'] : null;
    let workExperience =
      requestData && requestData['work_experience']
        ? requestData['work_experience']
        : null;
    let entity =
      requestData && requestData['entity'] ? requestData['entity'] : null;
    let division =
      requestData && requestData['division'] ? requestData['division'] : null;
    let subDivision =
      requestData && requestData['sub_division']
        ? requestData['sub_division']
        : null;
    let nationality =
      requestData && requestData['nationality']
        ? requestData['nationality']
        : null;
    let workLocation =
      requestData && requestData['work_location_id']
        ? requestData['work_location_id']
        : null;
    let region =
      requestData && requestData['region'] ? requestData['region'] : null;

    // Employment type filter applied check
    if (position) {
      let positionId = this.positionFilterMaster.find(
        (position) => position.id == position
      );
      if (positionId) {
        this.positionChips.push(positionId?.name);
        this.positionChipIds.push(positionId?.id);
        this.filterCount += 1;
      }
    }
    // // Work Experience filter applied check
    // if(workExperience){
    //   let workExpId = this.workLocationFilterMaster.find((location) => (location.id == workExperience));
    //   console.log("work exp id value",workExpId);
    // }
    // Nationality filter applied check
    if (nationality) {
      let nationalityId = this.nationalityFilterMaster.find(
        (national) => national.id == nationality
      );
      if (nationalityId) {
        this.nationalityChipIds.push(nationalityId?.id);
        this.nationalityChips.push(nationalityId?.name);
        this.filterCount += 1;
      }
    }
    // Work location Type filter check
    if (workLocation) {
      let workLocationId = this.workLocationFilterMaster.find(
        (location) => location.id == workLocation
      );
      if (workLocationId) {
        this.workLocationChips.push(workLocationId?.name);
        this.workLocationChipIds.push(workLocationId?.id);
        this.filterCount += 1;
      }
    }
    // Entity filter check
    if (entity) {
      let entityId = this.entityFilterMaster.find(
        (indvEntity) => indvEntity.id == entity
      );
      if (entityId) {
        this.entityChips.push(entityId?.name);
        this.entityChipIds.push(entityId?.id);
        this.filterCount += 1;
      }
    }
    // Division filter check
    if (division) {
      let divisionId = this.divisionFilterMaster.find(
        (indvDivision) => indvDivision.id == division
      );
      if (divisionId) {
        this.divisionChips.push(divisionId?.name);
        this.divisionChipIds.push(divisionId?.id);
        this.filterCount += 1;
      }
    }
    // Sub Division filter check
    if (subDivision) {
      let subDivisionId = this.subDivisionFilterMaster.find(
        (subDiv) => subDiv.id == subDivision
      );
      if (subDivisionId) {
        this.subDivisionChips.push(subDivisionId?.name);
        this.subDivisionChipIds.push(subDivisionId?.id);
        this.filterCount += 1;
      }
    }
    // Region filter check
    if (region) {
      let regionId = this.regionFilterMaster.find(
        (indvRegion) => indvRegion.id == region
      );
      if (regionId) {
        this.regionChips.push(regionId?.name);
        this.regionChipIds.push(regionId?.id);
        this.filterCount += 1;
      }
    }
    // Skill filter check
    if (this.defaultSkillDetails && this.defaultSkillDetails.length > 0) {
      for (let skill of this.defaultSkillDetails) {
        this.skillsChips.push(skill?.name);
        this.skillsChipIds.push(skill?.id);
      }
      this.filterCount += 1;
    }
  }

  async getSmartAllocationDetails(requestData) {
    let payloadFormat = {
      data: {
        requestId: requestData['request_id'],
        utilizationCapacity: requestData['utilization_capacity'],
        projectRole: requestData['project_role'],
        position: requestData['POSITION'],
        workExperience: requestData['work_experience'],
        nationality: requestData['nationality'],
        employmentType: requestData['employment_type'],
        workLocation: requestData['work_location'],
        // workLocation: null,
        startDate: requestData['start_date']
          ? moment(requestData['start_date']).format('YYYY-MM-DD')
          : null,
        endDate: requestData['end_date']
          ? moment(requestData['end_date']).format('YYYY-MM-DD')
          : null,
        entity: requestData['entity'],
        // entity: null,
        // division: null,
        division: requestData['division'],
        // subDivision: null,
        subDivision: requestData['sub_division'],
        // region: null,
        region: requestData['region'],
        projectId: requestData['project_id'],
        itemId: requestData['item_id'],
        projectCode: requestData['project_code'],
        currentDate: moment().format('YYYY-MM-DD'),
        skillSet: {
          skillsName: [1],
          expertise: [1],
        },
      },
    };

    this.isLoading = true;
    this.subs.sink = this._rmService
      .getSmartAllocationData(payloadFormat)
      .subscribe(
        (res: any) => {
          if (res) {
            this.list = res?.data;
            this.totalListCount = this.list.length;
            this.determineChartData();
            this.isLoading = false;
          }
        },
        (err) => {
          console.log(err);
          this.isLoading = false;
        }
      );
  }

  openSkillsOverlay(template: TemplateRef<any>): void {
    if (!this.overlayRef?.hasAttached()) {
      const positionStrategy = this._overlay
        .position()
        .global()
        .centerHorizontally()
        .centerVertically();

      this.overlayRef = this._overlay.create({
        positionStrategy,
        scrollStrategy: this._overlay.scrollStrategies.close(),
        hasBackdrop: true,
        panelClass: ['pop-up'],
      });

      const templatePortal = new TemplatePortal(
        template,
        this._viewContainerRef
      );

      this.overlayRef.attach(templatePortal);
    }
  }

  async applyAIfilters() {
    if (this.requestData) {
      let payloadFormat = {
        data: {
          requestId: this.requestData['request_id'],
          utilizationCapacity: this.requestData['utilization_capacity'],
          projectRole: this.requestData['project_role'],
          position: this.requestData['POSITION'],
          workExperience: this.requestData['work_experience'],
          nationality: this.requestData['nationality'],
          employmentType: this.requestData['employment_type'],
          workLocation: this.requestData['work_location_id'],
          startDate: this.requestData['start_date']
            ? moment(this.requestData['start_date']).format('YYYY-MM-DD')
            : null,
          endDate: this.requestData['end_date']
            ? moment(this.requestData['end_date']).format('YYYY-MM-DD')
            : null,
          entity: this.requestData['entity'],
          division: this.requestData['division'],
          subDivision: this.requestData['sub_division'],
          region: this.requestData['region'],
          projectId: this.requestData['project_id'],
          itemId: this.requestData['item_id'],
          projectCode: this.requestData['project_code'],
          currentDate: moment().format('YYYY-MM-DD'),
          skillSet: {
            skillsName: [1],
            expertise: [1],
          },
          aiFilters: {
            entity:
              this.entityChipIds && this.entityChipIds.length > 0
                ? this.entityChipIds
                : null,
            subDivision:
              this.subDivisionChipIds && this.subDivisionChipIds.length > 0
                ? this.subDivisionChipIds
                : null,
            division:
              this.divisionChipIds && this.divisionChipIds.length > 0
                ? this.divisionChipIds
                : null,
            employmentType:
              this.employmentTypeChipIds &&
              this.employmentTypeChipIds.length > 0
                ? this.employmentTypeChipIds
                : null,
            region:
              this.regionChipIds && this.regionChipIds.length > 0
                ? this.regionChipIds
                : null,
            skillsLevel:
              this.skillsChipIds && this.skillsChipIds.length > 0
                ? this.skillsChipIds
                : null,
            workLocation:
              this.workLocationChipIds && this.workLocationChipIds.length > 0
                ? this.workLocationChipIds
                : null,
            gender:
              this.genderChipIds && this.genderChipIds.length > 0
                ? this.genderChipIds
                : null,
            nationality:
              this.nationalityChipIds && this.nationalityChipIds.length > 0
                ? this.nationalityChipIds
                : null,
            holidayCalender:
              this.holidayCalendarChipIds &&
              this.holidayCalendarChipIds.length > 0
                ? this.holidayCalendarChipIds
                : null,
            priority: this.selectedPreferrenceFilterId
              ? this.selectedPreferrenceFilterId
              : null,
            role:
              this.positionChipIds && this.positionChipIds.length > 0
                ? this.positionChipIds
                : null,
            // experience: this.entityChipIds ? this.entityChipIds : null,
            experience: null,
          },
        },
      };

      this.isLoading = true;

      this.subs.sink = this._rmService
        .getSmartAllocationData(payloadFormat)
        .subscribe(
          (res: any) => {
            if (res) {
              this.list = res?.data;
              this.totalListCount = this.list.length;
              this.determineChartData();
              this.isLoading = false;
            }
          },
          (err) => {
            console.log(err);
            this.isLoading = false;
          }
        );
    }
  }

  closeOverlay(): void {
    this.overlayRef?.detach(); // Detach the overlay content
    this.overlayRef = undefined; // Clear the reference
  }

  ngOnDestroy(): void {
    this.subs.unsubscribe();
  }
}

import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import {
  MatDatepickerModule,
  MatDatepickerToggle,
} from '@angular/material/datepicker';
import { SharedComponentsModule } from 'src/app/app-shared/app-shared-components/components.module';
import { MatFormField, MatFormFieldModule } from '@angular/material/form-field';
import { MatRadioChange, MatRadioModule } from '@angular/material/radio';
import { CostDetailsDialogComponent } from 'src/app/modules/shared-lazy-loaded-components/team-members/pages/cost-details-dialog/cost-details-dialog/cost-details-dialog.component';
import { ActivatedRoute, Router } from '@angular/router';
import { MatDialog } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { ToasterService } from 'src/app/services/toaster-service/toaster.service';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatButtonModule } from '@angular/material/button';
import { RmService } from 'src/app/modules/resource-management/services/rm.service';
import { RmRequestDetailService } from '../../services/rm-request-detail.service';
import { SubSink } from 'subsink';
import { FormsModule } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { SharedLazyLoadedModule } from 'src/app/modules/project-management/shared-lazy-loaded/shared-lazy-loaded.module';
import { DxPieChartModule } from 'devextreme-angular';
import { BrowserModule } from '@angular/platform-browser';
import PieChart from 'devextreme/viz/pie_chart';
import { MultiSelectChipComponent } from 'src/app/modules/applicant-tracking-system/shared-components/multi-select-chip/multi-select-chip.component';
import { SingleSelectChipComponent } from 'src/app/modules/applicant-tracking-system/shared-components/single-select-chip/single-select-chip.component';
import { ApplicantTrackingSystemModule } from 'src/app/modules/applicant-tracking-system/applicant-tracking-system.module';
import * as moment from 'moment';
import { ViewChild, TemplateRef, ViewContainerRef } from '@angular/core';
import { Overlay, OverlayRef } from '@angular/cdk/overlay';
import { TemplatePortal } from '@angular/cdk/portal';
import { Options } from '@angular-slider/ngx-slider';
import { NgxSliderModule } from '@angular-slider/ngx-slider';

@NgModule({
  declarations: [],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDatepickerModule,
    SharedComponentsModule,
    MatFormFieldModule,
    MatRadioModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatButtonModule,
    FormsModule,
    MatInputModule,
    SharedLazyLoadedModule,
    DxPieChartModule,
    BrowserModule,
    ApplicantTrackingSystemModule,
    NgxSliderModule,
  ],
  exports: [],
})
export class SmartAllocationModule {}
