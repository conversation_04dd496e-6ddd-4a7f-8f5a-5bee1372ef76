import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { RmRequestDetailComponent } from './pages/rm-request-detail/rm-request-detail.component';
import { ResourcesComponent } from './features/resources/resources/resources.component';
import { RequestStatusComponent } from './features/request-status/request-status/request-status.component';
import { ForwardRequestComponent } from './features/forward-request/forward-request/forward-request.component';
import { ForwardToRecruitmentComponent } from './features/forward-to-recruitment/forward-to-recruitment.component';
import { SmartAllocationComponent } from './features/smart-allocation/smart-allocation.component';
import { RmAuthGuard } from './guards/rm-auth.guard';
const routes: Routes = [
  {
    path: ':id',
    component: RmRequestDetailComponent,
    children: [
      {
        path: '',
        redirectTo: 'allocation',
        pathMatch: 'full',
      },
      {
        path: 'allocation',
        component: ResourcesComponent,
        canActivate: [RmAuthGuard], //loadChildren: () => import('../../features/rm-request-detail/features/resources/resources/resources.component').then(m => m.ResourcesModule)
      },
      {
        path: 'forwardrequest',
        component: ForwardRequestComponent,
        canActivate: [RmAuthGuard], //loadChildren: () => import('../../features/rm-request-detail/features/forward-request/forward-request/forward-request.component').then(m => m.ForwardRequestModule)
      },
      {
        path: 'requeststatus',
        component: RequestStatusComponent,
        canActivate: [RmAuthGuard], //loadChildren: () => import('../../features/rm-request-detail/features/request-status/request-status/request-status.component').then(m => m.RequestStatusModule)
      },
      {
        path: 'forwardToRecruitment',
        component: ForwardToRecruitmentComponent,
        canActivate: [RmAuthGuard], //loadChildren: () => import('../../features/rm-request-detail/features/request-status/request-status/request-status.component').then(m => m.RequestStatusModule)
      },
      {
        path: 'activityLog',
        loadChildren: () => import("src/app/modules/shared-lazy-loaded-components/rm-activity-log/activity-log.module").then(m => m.ActivityLogModule),
        canActivate: [RmAuthGuard]
      },
    ],
  },
  {
    path: 'smartAllocation/:id',
    component: SmartAllocationComponent,
    children: [
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class RmRequestDetailRoutingModule {}
