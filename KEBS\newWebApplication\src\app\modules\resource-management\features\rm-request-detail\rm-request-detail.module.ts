import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { RmRequestDetailRoutingModule } from './rm-request-detail-routing.module';
import { RmRequestDetailComponent } from './pages/rm-request-detail/rm-request-detail.component';
import { RequestDetailCardComponent } from './components/request-detail-card/request-detail-card.component';
import { MatTabsModule } from '@angular/material/tabs';
import {MatProgressSpinnerModule} from '@angular/material/progress-spinner';
import { Routes, RouterModule } from '@angular/router';
import { ConfirmAlloactionFormComponent } from './features/resources/components/confirm-alloaction-form/confirm-alloaction-form.component';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { MatButtonModule } from '@angular/material/button';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatIconModule } from '@angular/material/icon';
import { SharedComponentsModule } from 'src/app/app-shared/app-shared-components/components.module';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatCheckboxModule } from '@angular/material/checkbox';
import {MatTooltipModule} from '@angular/material/tooltip';
import { SharedComponentModule } from 'src/app/modules/resource-management/shared-component/shared-component.module';
import {MatCardModule} from '@angular/material/card';
import { MatRadioChange, MatRadioModule } from '@angular/material/radio';
import {DirectiveModule} from '../../directives/directive.module';
import { SkillScoreComponent } from './features/resources/components/skill-score/skill-score.component'
import {MatTableModule} from '@angular/material/table';
import {ApplicantTrackingSystemModule} from 'src/app/modules/applicant-tracking-system/applicant-tracking-system.module'
import { SmartAllocationComponent } from './features/smart-allocation/smart-allocation.component';
import { SharedLazyLoadedModule } from 'src/app/modules/project-management/shared-lazy-loaded/shared-lazy-loaded.module';
import { DxPieChartModule } from 'devextreme-angular';

import { KebsProgressCircleModule } from 'kebs-progress-circle';
@NgModule({
  declarations: [RmRequestDetailComponent, RequestDetailCardComponent, ConfirmAlloactionFormComponent, SkillScoreComponent, SmartAllocationComponent],
  imports: [
    CommonModule,
    RmRequestDetailRoutingModule,
    MatTabsModule ,
    RouterModule,
    MatProgressSpinnerModule,
    ReactiveFormsModule,
    MatButtonToggleModule,
    MatButtonModule,
    MatIconModule,
    MatDatepickerModule,
    SharedComponentsModule,
    MatFormFieldModule,
    MatCheckboxModule,
    MatCardModule,
    MatTooltipModule,
    SharedComponentModule,
    MatRadioModule,
    DirectiveModule,
    MatTableModule,
    KebsProgressCircleModule,
    ApplicantTrackingSystemModule,
    SharedLazyLoadedModule,
    DxPieChartModule
  ],
  exports: [
    ConfirmAlloactionFormComponent
  ],
})
export class RmRequestDetailModule { }
