import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject, Subject } from 'rxjs';
import * as _ from 'underscore'
import { ShepherdService } from 'angular-shepherd';

@Injectable({
  providedIn: 'root'
})
export class RmRequestDetailService {

  tabInfo = [
    {
      tabName: 'Allocation',
      routeLink: 'allocation',
      allowedStatus: [1,3,6],
      tabId: 1
    },
    {
      tabName: 'Internal Forward',
      routeLink: 'forwardrequest',
      allowedStatus: [1,3],
      tabId: 2
    },
    {
      tabName:'Forward To Recruitment',
      routeLink:'forwardToRecruitment',
      allowedStatus: [1,2,3],
      tabId: 3
    },
    {
      tabName: 'Request Status',
      routeLink: 'requeststatus',
      allowedStatus: [1,2,3,4,5,6,7],
      tabId: 4
    },
    {
      tabName: 'Activities Log',
      routeLink: 'activityLog',
      allowedStatus: [1,2,3,4,5,6,7],
      tabId:5
    },
  ]

  constructor(
    private _http: HttpClient,
    private shepherdService: ShepherdService,
  ) { }

  public $requestData = new BehaviorSubject(null);

  setRequestData(data) {
    this.$requestData.next(data)
  }

  getRequestData() {
    return this.$requestData.asObservable()
  }

  validateRequestDetailRoute(activeTabPath,requestId) {
    
    return new Promise(async (resolve,reject) => {

      this.validateRequestDetailAuth(requestId).subscribe(async (res: any) => {

        if(!res['has_access'])
          resolve({
            validUser: false
          })

        //validate tabs based on request status.
        //Since forward to recruitment is tenant based it shouldn't be allowed to access for 
        // the tenants who has is_active = false
        let hasForwardToRecruitment = res['rm_config']['recruitment_forward'] 
        ? res['rm_config']['recruitment_forward']['is_active'] : false

        let tabDisabledStatus = res['rm_config']['tab_disabled_status']  ? res['rm_config']['tab_disabled_status'] : []
          
        // Filter tabs that should be active based on the allowed status
        let activeTabs = this.tabInfo.filter(tab => {
          // Check if the tab's allowedStatus does not overlap with the disabled statuses
          const isTabDisabled = tabDisabledStatus.includes(tab.tabId);
          
          // Return true only if no matching status is found in disabled status
          return !isTabDisabled;
        });

        let isSmartAllocationActive = res['rm_config']['is_smart_allocation_active'] ? res['rm_config']['is_smart_allocation_active'] : false;
        
        let activeTab = _.findWhere(activeTabs,{routeLink: activeTabPath})

        //check if the tenant has access to 'forwardToRecruitment' Tab
        if(activeTabPath == 'forwardToRecruitment' && !hasForwardToRecruitment)
          resolve({
            validUser: true,
            validTab: false
          })
          else if(activeTabPath == 'smartAllocation'){
            // Check if the tenant has access to smart allocation
            if(!isSmartAllocationActive){
              resolve({
                validUser: true,
                validTab: false
              });
            }
            else{
              // Smart Allocation can be accessed only when Allocation access is present
              activeTab = _.findWhere(this.tabInfo, {routeLink: 'allocation'});
            }
          }

        resolve({
          validUser: true,
          validTab: (activeTab && activeTab.allowedStatus.includes(res['status_info']['status_id']))
        })
          
      })


    })
  }

  retrieveRequestStatus(requestId) {
    return this._http.post('/api/rmg/requests/retrieveRequestStatus',{
      request_id: requestId
    });
  }

  validateRequestDetailAuth(requestId) {
    return this._http.post('/api/rmg/requests/validateRequestDetailAuth', {
      request_id: requestId
    })
  }

  getRmConfig() {
    return new Promise((resolve,reject) => {
      this.retrieveRmConfig()
      .subscribe((res: any) => {
        if(!res.err) {
          resolve(res.data)
        }
      },(err) => {
        console.log(err)
      })
    })
  }

  retrieveRmConfig() {
    return this._http.post('/api/rmg/utilityRoute/retrieveRmConfig', {})
  }

  retrieveSkillScore(params){
    return this._http.post('/api/rmg/resources/retrieveReqEmpSkillMatch',{
      'request_id':params.request_id,
      'resource_aid':params.resource_aid
    })
  }

  startTour(defaultStepOptions, defaultSteps) {
    this.shepherdService.defaultStepOptions = defaultStepOptions;
    this.shepherdService.modal = true;
    this.shepherdService.confirmCancel = false;
    this.shepherdService.addSteps(defaultSteps);
    this.shepherdService.start();
  };

  completeTour(){
    if (this.shepherdService && this.shepherdService.isActive)
      this.shepherdService.complete();
  };

  checkIfTourIsActive(){
    return this.shepherdService.isActive;
  };

  retrieveProjectVersion() {
    return this._http.post('/api/project/v2/checkProjectVersion', {})
  }

  getProjectV2Data(item_id) {
    return this._http.post('/api/pm/planning/getProjectDetails', {
      'item_id':item_id
    })
  }
  getEmployeeProjectDetails(associateId) {
    return this._http.post(
      '/api/employee360/organizationDetails/getEmployeeProjectDetails',
      {
        associate_id: associateId,
      }
    );
  }
  getJobCodeList() {
    return this._http.post(
      '/api/rmg/masterData/jobCodeList',
      {}
    );
  }
  getPreferrenceDetailsForFilter() {
    return this._http.post(
      '/api/atsai/getPriority',
      {}
    );
  }
  getEntityDetailsForFilter() {
    return this._http.post(
      '/api/timesheetv2/Master/getEntity',
      {}
    );
  }
  getDivisionDetailsForFilter() {
    return this._http.post(
      '/api/timesheetv2/Master/getDivision',
      {}
    );
  }
  getSubDivisionDetailsForFilter() {
    return this._http.post(
      '/api/timesheetv2/Master/getSubDivision',
      {}
    );
  }
  getWorkLocationDetailsForFilter() {
    return this._http.post(
      '/api/timesheetv2/Master/getLocationMasterData',
      {}
    );
  }
  getSkillDetailsForFilter() {
    return this._http.post(
      '/api/atsai/getSkillsLevel',
      {}
    );
  }
  getRegionDetailsForFilter() {
    return this._http.post(
      '/api/timesheetv2/Master/getRegion',
      {}
    );
  }
  getPositionDetailsForFilter() {
    return this._http.post(
      '/api/atsai/getRole',
      {}
    );
  }
  getGenderDetailsForFilter() {
    return this._http.post(
      '/api/ats/masterService/getGenderDetails',
      {}
    );
  }
  getNationalityDetailsForFilter() {
    return this._http.post(
      '/api/ats/masterService/getNationality',
      {}
    );
  }
  getEmploymentTypeDetailsForFilter() {
    return this._http.post(
      '/api/timesheetv2/Master/getEmploymentTypes',
      {}
    );
  }
  getHolidayCalendarForFilter() {
    return this._http.post(
      '/api/atsai/getHolidayCalender',
      {}
    );
  }
  getDefaultSkillDetailsBasedOnRequest(reqId){
    return this._http.post(
      '/api/atsai/peoples/getRequestSkills',
      {
        "requestId": reqId
      }
    );
  }
}
