import { Component, OnInit } from '@angular/core';
import * as _ from 'underscore';
import { RmService } from 'src/app/modules/resource-management/services/rm.service';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import {ApiData, TabListType} from '../../../../interfaces/rm-request-landing-interface'
import moment from 'moment';
import { SubSink } from 'subsink';
import { UdrfService } from 'src/app/services/udrf/udrf.service';
import { UtilityService } from 'src/app/services/utility/utility.service';
import { Subject, Subscription } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { KebsHelpService } from 'kebs-help-application';
import { LoginService } from 'src/app/services/login/login.service';
import { accessObjectMaster } from '../../../../access-object.auth';
import { ErrorService } from 'src/app/services/error/error.service';
import { ToasterService } from 'src/app/services/toaster-service/toaster.service';
import { MatDialog } from '@angular/material/dialog';
import { JsonToExcelService } from 'src/app/services/excel/json-to-excel.service';

@Component({
  selector: 'app-rm-request-landing',
  templateUrl: './rm-request-landing.component.html',
  styleUrls: ['./rm-request-landing.component.scss']
})

export class RmRequestLandingComponent implements OnInit {

  sortVar: string = '';
  keyList: any;
  dataList: ApiData[];
  startLimit = 0;
  endLimit = 15;
  sLimit = 0;
  eLimit = 15;
  selectedVal: any;
  private subs = new SubSink();
  dataListUDRF:any = null;
  filterConfig:any;
  defaultCount = 15;
  tabList:any;
  tabListCount:number = 0;
  currentUser: any = {};
  tab_id : number;
  searchPlaceHolder:string = 'by Request ID, Project';
  protected $onDestroy = new Subject<void>();
  udrfBodyColumns = [
    {      
      item: 'display_request_id',
      header: 'Request ID',
      id: '1',
      isVisible: 'true',
      isActive: true,
      type: 'text',
      colSize: '1',
      textClass: 'zf-theme-bold',
      position: 1,
      hasColumnClick: true,
      width: 140,
      sortOrder: 'I',
      filterId:1
    },
    {      
      //udrf
      item: 'request_context_item',
      header: 'Project/Opportunity',
      id: '2',
      isVisible: 'true',
      isActive: true,
      type: 'text',
      colSize: '1',
      textClass: 'zf-theme-bold',
      position: 1,
      hasColumnClick: true,
      width: 280,
      sortOrder: 'I',
      filterId:4,
      rm_config_ref_key: 'request_context'
    },
    {      
      //udrf
      item: 'SOURCE',
      header: 'Source',
      id: '2',
      isVisible: 'true',
      isActive: true,
      type: 'text',
      colSize: '1',
      textClass: 'zf-theme-light',
      position: 1,
      hasColumnClick: true,
      width: 150,
      sortOrder: 'I',
      filterId:5
    },
    {      
      item: 'requested_by',
      header: 'Requested By',
      id: '',
      isVisible: 'true',
      isActive: true,
      type: 'profile',
      colSize: '1',
      textClass: 'zf-theme-light',
      position: 1,
      hasColumnClick: true,
      width: 240,
      sortOrder: 'N',
      // filterId:6
    },
    {      
      //udrf
      item: 'expected_closure_date',
      header: 'Expected Closure Date',
      id: '',
      isVisible: 'true',
      isActive: true,
      type: 'date',
      colSize: '1',
      textClass: 'zf-theme-light',
      position: 1,
      hasColumnClick: true,
      width: 180,
      sortOrder: 'I',
      filterId:3
    },
    {      
      //udrf
      item: 'request_status',
      header: 'Request Status',
      id: '',
      isVisible: 'true',
      isActive: true,
      type: 'statusBadge',
      isInlineEdit: true,
      inlineEditVarient: ['Request status', 'minimal-dropdown'],
      colSize: '1',
      textClass: 'zf-font',
      position: 1,
      hasColumnClick: true,
      width: 200,
      sortOrder: 'I',
      filterId:7
    },
    {      
      //udrf
      item: 'priority',
      header: 'Priority',
      id: '',
      isVisible: 'false',
      isActive: false,
      type: 'text',
      isInlineEdit: false,
      colSize: '1',
      textClass: 'zf-font',
      position: 1,
      hasColumnClick: true,
      width: 200,
      sortOrder: 'I',
      filterId:16,
      rm_config_ref_key: 'priority'
    },
    {      
      //udrf
      item: 'days_ellapsed',
      header: 'Days Ellapsed',
      id: '',
      isVisible: 'false',
      isActive: false,
      type: 'text',
      isInlineEdit: false,
      colSize: '1',
      textClass: 'zf-font',
      position: 1,
      hasColumnClick: true,
      width: 200,
      sortOrder: 'N',
      rm_config_ref_key: 'days_ellapsed'
    },
    {      
      //udrf
      item: 'escalated',
      header: 'Escalated Status',
      id: '',
      isVisible: 'false',
      isActive: false,
      type: 'text',
      isInlineEdit: false,
      colSize: '1',
      textClass: 'zf-font',
      position: 1,
      hasColumnClick: true,
      width: 200,
      sortOrder: 'N',
      rm_config_ref_key: 'escalated'
    },
    {      
      //udrf
      item: 'work_location',
      header: 'Work Location',
      id: '',
      isVisible: 'true',
      isActive: true,
      type: 'text',
      colSize: '1',
      textClass: 'zf-theme-light',
      position: 1,
      hasColumnClick: true,
      width: 240,
      sortOrder: 'I',
      filterId:8
    },
    {      
      //udrf
      item: 'commercial',
      header: 'Commercial Type',
      id: '',
      isVisible: 'true',
      isActive: true,
      type: 'text',
      colSize: '1',
      textClass: 'zf-theme-light',
      position: 1,
      hasColumnClick: true,
      width: 180,
      sortOrder: 'I',
      filterId:9
    },
    {      
      //udrf
      item: 'commercial_category',
      header: 'Commercial Category',
      id: '',
      isVisible: 'false',
      isActive: false,
      type: 'text',
      isInlineEdit: false,
      colSize: '1',
      textClass: 'zf-font',
      position: 1,
      hasColumnClick: true,
      width: 200,
      sortOrder: 'N',
      rm_config_ref_key: 'commercial_category'
    },
    {
      item: 'assigned_to',
      header: 'Allocation Executive',
      isActive: true,
      isVisible: 'true',
      type: 'text1',
      isInlineEdit: true,
      textClass: 'zf-theme-light',
      inlineEditVarient: ['Assigned To', 'search-dropdown'],
      position: 8,
      colSize: 1,
      sortOrder: 'N',
      width: 180,
    },
    {
      item: 'rm_officier',
      header: 'Allocation Coordinator',
      isActive: true,
      isVisible: 'true',
      type: 'text1',
      textClass: 'zf-theme-light',
      isInlineEdit: false,
      position: 9,
      colSize: 1,
      sortOrder: 'N',
      width: 180,
    },
    {
      item: 'accepted_by',
      header: 'Accepted By',
      isVisible: 'true',
      isActive: true,
      type: 'textHyphen',
      colSize: '4',
      textClass: 'zf-theme-light',
      position: 10,
      hasColumnClick: true,
      width: 200,
      sortOrder: 'N',
      // filterId: 13,
    },
    {
      //udrf
      item: 'region_name',
      header: 'region',
      id: '',
      isVisible: 'true',
      config_ref_key: 'region',
      isActive: true,
      type: 'text',
      colSize: '1',
      textClass: 'zf-theme-light',
      position: 11,
      hasColumnClick: true,
      width: 240,
      sortOrder: 'N',
    },
    {
      //udrf
      item: 'entity_name',
      header: 'Entity',
      id: '',
      isVisible: 'true',
      isActive: true,
      type: 'text',
      config_ref_key: 'entity',
      colSize: '1',
      textClass: 'zf-theme-light',
      position: 9,
      hasColumnClick: true,
      width: 240,
      sortOrder: 'N',
    },
    {
      //udrf
      item: 'division_name',
      header: 'Division',
      id: '',
      isVisible: 'true',
      isActive: true,
      config_ref_key: 'division',
      type: 'text',
      colSize: '1',
      textClass: 'zf-theme-light',
      position: 10,
      hasColumnClick: true,
      width: 240,
      sortOrder: 'N',
    },
    {
      //udrf
      item: 'sub_division_name',
      header: 'Sub Division',
      id: '',
      isVisible: 'true',
      config_ref_key: 'subdivision',
      isActive: true,
      type: 'text',
      colSize: '1',
      textClass: 'zf-theme-light',
      position: 11,
      hasColumnClick: true,
      width: 240,
      sortOrder: 'N',
    },
    {
      //udrf
      item: 'work_experience',
      header: 'Work Experience',
      id: '',
      isVisible: 'true',
      isActive: true,
      type: 'text',
      colSize: '1',
      textClass: 'zf-theme-light',
      position: 11,
      hasColumnClick: true,
      width: 240,
      sortOrder: 'N',
      filterId:11,
      config_ref_key: 'work_experience'
    },
    {
      //udrf
      item: 'operational_model',
      header: 'Operational Model',
      id: '',
      isVisible: 'true',
      isActive: true,
      type: 'text',
      colSize: '1',
      textClass: 'zf-theme-light',
      position: 11,
      hasColumnClick: true,
      width: 240,
      sortOrder: 'N',
      filterId:12,
      config_ref_key: 'operation_model'
    },
    {
      //udrf
      item: 'start_date',
      header: 'Start Date',
      id: '',
      isVisible: 'true',
      isActive: true,
      type: 'text',
      colSize: '1',
      textClass: 'zf-theme-light',
      position: 11,
      hasColumnClick: true,
      width: 240,
      sortOrder: 'N',
      filterId:13
    },
    {
      //udrf
      item: 'end_date',
      header: 'End Date',
      id: '',
      isVisible: 'true',
      isActive: true,
      type: 'text',
      colSize: '1',
      textClass: 'zf-theme-light',
      position: 11,
      hasColumnClick: true,
      width: 240,
      sortOrder: 'N',
      filterId:14
    },
    {
      //udrf
      item: 'Skill_Set',
      header: 'Skill Set',
      id: '',
      isVisible: 'true',
      isActive: true,
      type: 'text',
      colSize: '1',
      textClass: 'zf-theme-light',
      position: 11,
      hasColumnClick: true,
      width: 240,
      sortOrder: 'N'
    }, 
    {      
      //udrf
      item: 'customer_name',
      header: 'Customer',
      id: '15',
      isVisible: 'true',
      isActive: true,
      type: 'text',
      colSize: '1',
      textClass: 'zf-theme-light',
      position: 1,
      hasColumnClick: true,
      width: 150,
      sortOrder: 'I',
      filterId:15,
      config_ref_key: 'customer'
    },   
    {
      //udrf
      item: 'allocated_opportunity',
      header: 'Opportunity',
      id: '',
      isVisible: 'true',
      isActive: true,
      type: 'text',
      colSize: '1',
      textClass: 'zf-theme-light',
      position: 11,
      hasColumnClick: true,
      width: 150,
      sortOrder: 'N',
      config_ref_key: 'opportunity_id'
    }, 
  ];
  tileBodyColumns = [
    {      
      item: 'display_request_id',
      header: 'Request ID',
      hasCardClick: true,
      tileType: 'id'
    },
    {      
      item: 'request_status',
      header: 'Request Status',
      hasCardClick: true,
      tileType: 'status'
    },
    {      
      item: 'request_context_item',
      header: 'Context',
      hasCardClick: true,
      tileType: 'header'
    },
    
    {      
      item: 'requested_by',
      header: 'Requested By',
      hasCardClick: true,
      tileType: 'textImg'
    },
    {      
      item: 'expected_closure_date',
      header: 'Expected Closure Date',
      hasCardClick: true,
      tileType: 'date'
    }
  ];
  
  router: any;
  tabFilter:any;
  //udrf
  applicationId = 415;
  skip: any = 0;
  limit: any = 15;
  udrfItemStatusColor: any;
  defaultDataRetrievalCount: any = 15;
  resolveVisibleDataTypeArray: any;
  itemDataCurrentIndex: any = 0;
  hasAccess:any = false;
  protected _onDestroy = new Subject<void>();
  // protected _onAppApiCalled = new Subject<void>()
  $mainDataSubscription: Subscription;
  udrfColumnsTenant:any;

  constructor(public _rmService:RmService,
    public _router : Router,
    public _service : RmService,
    public udrfService: UdrfService,
    private utilityService: UtilityService,
    private _help:KebsHelpService,
    private authService: LoginService,
    private _RmService: RmService,  
    private _errorService: ErrorService,
    private _toaster: ToasterService,
    private dialog: MatDialog,
    private _excelService:JsonToExcelService,  
    private _route: ActivatedRoute  
    ) { }

  async ngOnInit() {
    await this.udrfService.resetUdrfData()
    await this.handleTenantFieldLabel()
    this.hasAccess = this._RmService.checkAccessForObject(accessObjectMaster.AdminVisibility)
    this.selectedVal = "All";
    this.tab_id = 0;
    let statusMasterData = await this.getRequestStatusMasterData();
    this.initStatusData(statusMasterData);
    await this.handleTenantFieldLabel()
    this.setInlineEditStatusList(statusMasterData)
    // Extract 'requestId' from the URL query parameters
    this._route.queryParams.subscribe(params => {
      let search_id = params['requestId'] ? +params['requestId'] : null;

      if (search_id && !isNaN(search_id)) {
        // Call the search function automatically with the extracted requestId
        this.udrfService.udrfData.mainSearchParameter = search_id.toString();
      }
    });
    await this.configureUdrf();
  }
  async handleTenantFieldLabel() {
    let fieldConfig = await this.retrieveFieldConfig('create_request')
    fieldConfig = _.groupBy(fieldConfig,'field_key')
    let rmConfig = await this.retrieveRmConfig()
    let udrfConfig = rmConfig['udrfNameChange'] ? _.groupBy(rmConfig['udrfNameChange']['udrf_params'],'field_key') : null;
    let rm_executive_check_enable = rmConfig['rm_executive_check'] ? rmConfig['rm_executive_check'] : false;
    let rm_executive_check: boolean = false;
    if(rm_executive_check_enable){
      rm_executive_check = await this.getRmExecutiveCheck();
    }
    // let entityFlag = rmConfig['isEntityVisible'] ? rmConfig['isEntityVisible'] : false
    for(let l of this.udrfBodyColumns) {
      if(l['config_ref_key']) {
        let temp = fieldConfig[l['config_ref_key']] ? fieldConfig[l['config_ref_key']][0] : null
        if(temp) {
          l['header'] = temp['field_label'],
          l['isActive'] = temp['is_active_field']
        }
      }
      if(l['rm_config_ref_key'] && udrfConfig){
        let temp = udrfConfig[l['rm_config_ref_key']] ? udrfConfig[l['rm_config_ref_key']][0] : null
        if(temp){
          l['header'] = temp['field_label']
          if(temp['is_active']){
            l['isActive'] = temp['is_active']
            l['isVisible'] = temp['is_active'] ? 'true' : 'false'
          }
        }
      }
      if(rmConfig['isTenantNameChange']){
        this.searchPlaceHolder = 'by Request ID, SOW';
      }
      if(l['item'] == 'assigned_to' && rm_executive_check){
        l['isInlineEdit'] = false
      }
    }
    this.udrfColumnsTenant = this.udrfBodyColumns.filter(obj => obj.isActive !== false);
  }

  initStatusData = (data) => {
    let udrfItemStatusColor = [];
    data.forEach((e, i) => {
      udrfItemStatusColor.push({
        status: e.status,
        color: e.color,
      });
    });

    this.udrfItemStatusColor = udrfItemStatusColor;
  };

  setInlineEditStatusList(data) {
    let inlineEditList = _.where(data, {is_inline_edit_allowed: 1})
    inlineEditList = inlineEditList.map(l => {
      return {
        id: l.id,
        name: l.status
      }
    })

    this.udrfService.udrfUiData.inlineEditDropDownMasterDatas = { requestStatusList: inlineEditList }
  }

  configureUdrf() {
    let durationRanges = [
      {
        checkboxId: 'CDCRD',
        checkboxName: 'Current Year',
        checkboxStartValue: moment().startOf('year'),
        checkboxEndValue: moment().endOf('year'),
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxId: 'CDCRD2',
        checkboxName: 'This Week',
        checkboxStartValue: this.utilityService.getFormattedDate(
          moment().startOf('week'),
          moment(moment().startOf('week')).date,
          15,
          0,
          0,
          0
        ),
        checkboxEndValue: this.utilityService.getFormattedDate(
          moment().endOf('week'),
          moment(moment().endOf('week')).date,
          15,
          0,
          0,
          0
        ),
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxId: 'CDCRD3',
        checkboxName: 'This Month',
        checkboxStartValue: this.utilityService.getFormattedDate(
          moment().startOf('month'),
          moment(moment().startOf('month')).date,
          15,
          0,
          0,
          0
        ),
        checkboxEndValue: this.utilityService.getFormattedDate(
          moment().endOf('month'),
          moment(moment().endOf('month')).date,
          15,
          0,
          0,
          0
        ),
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxId: 'CDCRD4',
        checkboxName: 'Previous Month',
        checkboxStartValue: this.utilityService.getFormattedDate(
          moment().subtract(1, 'months').startOf('month'),
          moment(moment().subtract(1, 'months').startOf('month')).date,
          15,
          0,
          0,
          0
        ),
        checkboxEndValue: this.utilityService.getFormattedDate(
          moment().subtract(1, 'months').endOf('month'),
          moment(moment().subtract(1, 'months').endOf('month')).date,
          15,
          0,
          0,
          0
        ),
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxId: 'CDCRD5',
        checkboxName: 'All',
        checkboxStartValue: moment('1920-01-01').format('YYYY-MM-DD'),
        checkboxEndValue: moment('9999-12-31').format('YYYY-MM-DD'),
        isCheckboxDefaultSelected: true,
      },
    ];
    let expectedDurationRanges = [
      {
        checkboxId: 'CDCRD',
        checkboxName: 'Current Year',
        checkboxStartValue: moment().startOf('year'),
        checkboxEndValue: moment().endOf('year'),
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxId: 'CDCRD2',
        checkboxName: 'This Week',
        checkboxStartValue: this.utilityService.getFormattedDate(
          moment().startOf('week'),
          moment(moment().startOf('week')).date,
          15,
          0,
          0,
          0
        ),
        checkboxEndValue: this.utilityService.getFormattedDate(
          moment().endOf('week'),
          moment(moment().endOf('week')).date,
          15,
          0,
          0,
          0
        ),
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxId: 'CDCRD3',
        checkboxName: 'This Month',
        checkboxStartValue: this.utilityService.getFormattedDate(
          moment().startOf('month'),
          moment(moment().startOf('month')).date,
          15,
          0,
          0,
          0
        ),
        checkboxEndValue: this.utilityService.getFormattedDate(
          moment().endOf('month'),
          moment(moment().endOf('month')).date,
          15,
          0,
          0,
          0
        ),
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxId: 'CDCRD4',
        checkboxName: 'Previous Month',
        checkboxStartValue: this.utilityService.getFormattedDate(
          moment().subtract(1, 'months').startOf('month'),
          moment(moment().subtract(1, 'months').startOf('month')).date,
          15,
          0,
          0,
          0
        ),
        checkboxEndValue: this.utilityService.getFormattedDate(
          moment().subtract(1, 'months').endOf('month'),
          moment(moment().subtract(1, 'months').endOf('month')).date,
          15,
          0,
          0,
          0
        ),
        isCheckboxDefaultSelected: false,
      },
      {
        checkboxId: 'CDCRD5',
        checkboxName: 'All',
        checkboxStartValue: moment('1920-01-01').format('YYYY-MM-DD'),
        checkboxEndValue: moment('9999-12-31').format('YYYY-MM-DD'),
        isCheckboxDefaultSelected: false,
      },
    ];

    this.udrfService.udrfFunctions.constructCustomRangeData(
      2,
      'date',
      durationRanges
    );
    this.udrfService.udrfFunctions.constructCustomRangeData(
      3,
      'date',
      expectedDurationRanges
    );
    this.udrfService.udrfData.applicationId = this.applicationId;
    this.udrfService.udrfUiData.showViewForRMG = 'list';

    this.udrfService.udrfUiData.showItemDataCount = true;
    this.udrfService.udrfUiData.showSearchBar = true;
    this.udrfService.udrfUiData.showActionButtons = true;
    this.udrfService.udrfUiData.showUdrfModalButton = true;
    this.udrfService.udrfUiData.showColumnConfigButton = true;
    this.udrfService.udrfUiData.showSettingsModalButton = false;
    this.udrfService.udrfUiData.showNewReleasesButton = false;
    this.udrfService.udrfUiData.showReportDownloadButton = false;
    this.udrfService.udrfUiData.isReportDownloading = false;
    this.udrfService.udrfUiData.itemHasOpenInNewTab = false;
    this.udrfService.udrfUiData.horizontalScroll = true;
    this.udrfService.udrfUiData.itemHasQuickCta = false;
    this.udrfService.udrfUiData.collapseAll = false;
    this.udrfService.udrfUiData.showCollapseButton = false;
    this.udrfService.udrfUiData.countForOnlyThisReport = false;
    this.udrfService.udrfUiData.toggleChecked = false;
    this.udrfService.udrfUiData.countFlag = false;
    this.udrfService.udrfUiData.isMultipleView = false;
    this.udrfService.udrfUiData.itemHasDownloadButton = false;
    this.udrfService.udrfUiData.emailPluginVisible = false;
    this.udrfService.udrfUiData.itemHasDownloadButton = false;
    this.udrfService.udrfUiData.itemHasAttachFileButton = false;
    this.udrfService.udrfUiData.isMoreOptionsNeeded = true;
    this.udrfService.udrfUiData.completeProfileBtn = false;
    this.udrfService.udrfUiData.udrfBodyColumns = this.udrfColumnsTenant;
    this.udrfService.udrfUiData.udrfVisibleBodyColumns =
    this.udrfService.udrfUiData.udrfVisibleBodyColumns;
    this.udrfService.udrfUiData.udrfInvisibleBodyColumns =
    this.udrfService.udrfUiData.udrfInvisibleBodyColumns;
    this.udrfService.udrfUiData.showHelpButton = true;
    this.udrfService.udrfUiData.isModalSortVisible = false;
    this.udrfService.udrfUiData.itemDataScrollDown =
      this.onUdrfItemDataScrollDown.bind(this);
    //Admin button in more settings
    if(this.hasAccess){
      this.udrfService.udrfUiData.isAdminView = true,
      this.udrfService.udrfUiData.adminSettingButton = true
    }
    //Udrf Configuration for UDRF Body
    this.udrfService.udrfUiData.variant = 0;
    this.udrfService.udrfUiData.itemDataType= "";
    this.udrfService.udrfUiData.searchPlaceholder= this.searchPlaceHolder;
    this.udrfService.udrfUiData.totalItemDataCount = 0;
    this.udrfService.udrfUiData.itemCardSelecteditem = {};
    this.udrfService.udrfUiData.udrfItemStatusColor = this.udrfItemStatusColor;
 
    this.udrfService.udrfUiData.fetchProfileNameFromE360 = true;
    this.udrfService.udrfUiData.quickCTAInput = {};
    this.udrfService.udrfUiData.commentsInput = {};
    this.udrfService.udrfUiData.commentsContext = {};
    this.udrfService.udrfUiData.updateItemCard = () => {};
    this.udrfService.udrfUiData.attachFile = () => {};
    this.udrfService.udrfUiData.deleteFile = () => {};
    this.udrfService.udrfUiData.downloadFile = () => {};
    this.udrfService.udrfUiData.closeCTA = () => {};
    this.udrfService.udrfUiData.updateItemCardData = {};
    this.udrfService.udrfUiData.attachFileData = {};
    this.udrfService.udrfUiData.deleteFileData = {};
    this.udrfService.udrfUiData.downloadFileData = {};
    this.udrfService.udrfUiData.closeCTAData = {};
    this.udrfService.udrfUiData.onProgressItem = () => {};
    this.udrfService.udrfUiData.onCompleteItem = () => {};
    this.udrfService.udrfUiData.onCompleteItemResponse = '';
    this.udrfService.udrfUiData.onColumnClickItem = '';
    this.udrfService.udrfUiData.collapseAllCard = () => {};
    this.udrfService.udrfUiData.countResponseData = [];
    this.udrfService.udrfUiData.emailPluginData = {};
    this.udrfService.udrfUiData.summaryCards = []
    this.udrfService.udrfUiData.itemHasMoreActions = false;
    this.udrfService.udrfUiData.openComments = this.openComments.bind(this);
    this.udrfService.udrfUiData.itemHasComments = true;

    // this.udrfService.udrfUiData.moreActionItemClicked =
    //   this.moreActionItemClicked.bind(this);
    this.udrfService.udrfUiData.moreActionItemClickedData = {};

    //Binding the Card Click
    this.udrfService.udrfUiData.itemcardSelected =
      this.reqdetaildialog.bind(this);
    //Binding the Inline edit api
    this.udrfService.udrfUiData.callInlineEditApi = this.callInlineEditApi.bind(this)
    this.udrfService.udrfUiData.inlineEditData = {};
    this.udrfService.getAppUdrfConfig(
      this.applicationId,
      this.initReport.bind(this)
    );
    this.udrfService.getNotifyReleasesUDRF();
    this.udrfService.udrfFunctions.resolveVisibleColumnConfigItems();
    this.udrfService.udrfUiData.resolveColumnConfig();
    this.udrfService.udrfUiData.openHelpDialog = this.openHelpDialog.bind(this);
    this.udrfService.udrfUiData.openAdminSettingsReport = this.openAdminDialog.bind(this);
    this.udrfService.udrfUiData.downloadItemDataReport =
      this.downloadRequestReport.bind(this);
  }

  initReport = async () => {
    let rmConfig = await this.retrieveRmConfig();
    if(rmConfig['isRequestDownloadVisible']){
      this.udrfService.udrfUiData.showReportDownloadButton = true;
    }
    
    let mainFilterArray = JSON.parse(
      JSON.stringify(this.udrfService.udrfData.mainFilterArray)
    );
    let filters = mainFilterArray;
    let filterConfig = {
      startIndex: this.skip,
      startDate: this.udrfService.udrfData.mainApiDateRangeStart,
      endDate: this.udrfService.udrfData.mainApiDateRangeEnd,
      mainFilterArray: filters,
      txTableDetails: this.udrfService.udrfData.txTableDetails,
      mainSearchParameter: this.udrfService.udrfData.mainSearchParameter,
      searchTableDetails: this.udrfService.udrfData.searchTableDetails,
    };
    this.tabFilter = await this.getTabList(filterConfig);
    this.tabList = (this.tabFilter && this.tabFilter['statusList']) ? this.tabFilter['statusList'] : [];
    this.tabListCount = (this.tabFilter && this.tabFilter['totalCount']) ? this.tabFilter['totalCount'] : 0;
    // this._onAppApiCalled.next();
    this.itemDataCurrentIndex = 0;
    this.udrfService.udrfBodyData = [];
    this.udrfService.udrfUiData.resolveColumnConfig();
    this.udrfService.udrfData.isItemDataLoading = true;
    this.skip = 0;
    this.limit = this.defaultDataRetrievalCount;
    this.getRequestData(false);

  };

  downloadRequestReport(){
    let mainFilterArray = JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray));
    let filters = mainFilterArray;
    let filterConfig = {
      startIndex: "D",
      startDate: this.udrfService.udrfData.mainApiDateRangeStart,
      endDate: this.udrfService.udrfData.mainApiDateRangeEnd,
      mainFilterArray: filters,
      txTableDetails: this.udrfService.udrfData.txTableDetails,
      mainSearchParameter: this.udrfService.udrfData.mainSearchParameter,
      searchTableDetails: this.udrfService.udrfData.searchTableDetails,
    };

    

    this.udrfService.udrfUiData.isReportDownloading = true;
    if(this.$mainDataSubscription)
      this.$mainDataSubscription.unsubscribe()

    this.$mainDataSubscription = this._rmService
      .getResourceRequestList(filterConfig,this.tab_id)
      .pipe(takeUntil(this._onDestroy))
      // .pipe(takeUntil(this._onAppApiCalled))
      .subscribe(
        async (res) => {
          
          if (!res['err'] && res['data'].length > 0) {
            for(let l of res['data']) {
              l['Expected_Closure_Date'] = moment(l['Expected_Closure_Date']).utc().format('DD-MMM-YYYY');
              l['Start_Date'] = moment(l['Start_Date']).utc().format('DD-MMM-YYYY')
              l['End_Date'] = moment(l['End_Date']).utc().format('DD-MMM-YYYY')
            }
            this.udrfService.udrfUiData.isReportDownloading=false;
            this._excelService.exportAsExcelFile(res['data'],'Request Report');
            this.utilityService.showMessage('Report Downloaded !','Dismiss',3000);
            
          } 
          else if(!res['err'] && res['data'].length == 0){
            this.udrfService.udrfUiData.isReportDownloading=false;
            this.utilityService.showMessage('No Requests to Download !','Dismiss',3000);
          } else {
            this.udrfService.udrfUiData.isReportDownloading=false;
            this.utilityService.showMessage('Error in Downloading Report !','Dismiss',3000);
          }

          this.udrfService.udrfData.isItemDataLoading = false;
        },
        (err) => {
          console.log(err);
          this.udrfService.udrfUiData.isReportDownloading=false;
          this.utilityService.showMessage('Please Contact KEBS team!','Dismiss',3000);
        }
      );
  }

   /**
   * @description get request data
   */

   getRequestData = (infiniteScrollFlag) => {
    
    this.udrfService.udrfData.isItemDataLoading = true;
    let mainFilterArray = JSON.parse(
      JSON.stringify(this.udrfService.udrfData.mainFilterArray)
    );
    let filters = mainFilterArray;
    let filterConfig = {
      startIndex: this.skip,
      startDate: this.udrfService.udrfData.mainApiDateRangeStart,
      endDate: this.udrfService.udrfData.mainApiDateRangeEnd,
      mainFilterArray: filters,
      txTableDetails: this.udrfService.udrfData.txTableDetails,
      mainSearchParameter: this.udrfService.udrfData.mainSearchParameter,
      searchTableDetails: this.udrfService.udrfData.searchTableDetails,
    };
    

    if(this.$mainDataSubscription)
      this.$mainDataSubscription.unsubscribe()

    this.$mainDataSubscription = this._rmService
      .getResourceRequestList(filterConfig,this.tab_id)
      .pipe(takeUntil(this._onDestroy))
      // .pipe(takeUntil(this._onAppApiCalled))
      .subscribe(
        async (res) => {
          
          if (!res['err'] && res['data'].length > 0) {
            res['data'] = this.utcDateFormat(res['data'])
            this.udrfService.udrfBodyData =
                this.udrfService.udrfBodyData.concat(res['data']);
            
                this.udrfService.udrfData.isItemDataLoading = false
            this.udrfService.udrfUiData.totalItemDataCount = this.tabListCount;
            
          } else {
            this.udrfService.udrfBodyData =
              this.udrfService.udrfBodyData.concat([]);
            this.udrfService.udrfData.noItemDataFound = true;
            this.udrfService.udrfUiData.totalItemDataCount = this.tabListCount
            if(res['err'] && res['userMsg'] && !infiniteScrollFlag){
              this._toaster.showWarning('Warning',res['userMsg'])
            }
            else if(!infiniteScrollFlag){
              this._toaster.showWarning('Warning','Request Data not found')
            }
          }

          this.udrfService.udrfData.isItemDataLoading = false;
        },
        (err) => {
          this.udrfService.udrfBodyData = [];
          this.udrfService.udrfData.isItemDataLoading = false;
          this._toaster.showError('Error','Failed to retrieve Request Data',1000)
          console.log(err);
        }
      );
  };

  utcDateFormat(data) {
    for(let l of data) {
      l['expected_closure_date'] = moment(l['expected_closure_date']).utc().format('DD-MMM-YYYY')
      l['start_date'] = moment(l['start_date']).utc().format('DD-MMM-YYYY')
      l['end_date'] = moment(l['end_date']).utc().format('DD-MMM-YYYY')
    }
    return data
  }

  /**
   * @description on udrf item scroll down
   */
  async onUdrfItemDataScrollDown() {
    
    if (!this.udrfService.udrfData.noItemDataFound) {
      if (!this.udrfService.udrfData.isItemDataLoading) {
        this.skip += this.defaultDataRetrievalCount;

        // this.udrfService.udrfData.isItemDataLoading = true;
        
        await this.getRequestData(true);
      }
    }
  }

  onValChange = (event,tab) => {
    this.udrfService.udrfData.isItemDataLoading = true;
    this.skip = 0;
    this.udrfService.udrfData.noItemDataFound = false;
    this.selectedVal = event.value;
    this.tab_id = tab.id;

    this.initReport();
  };
  
  reqdetaildialog=()=>{
    let requestData = this.udrfService.udrfUiData.itemCardSelecteditem;
    // this._router.navigateByUrl(`/main/resource-management/home/<USER>/${requestData['request_id']}`)
    this._router.navigateByUrl(this._rmService.getRouterLink("requestDetail",requestData['request_id']))
  }
  reqTileDetail = (val)=>{
    // this._router.navigateByUrl(`/main/resource-management/home/<USER>/${val}`)
    this._router.navigateByUrl(this._rmService.getRouterLink("requestDetail",val))
  }
  getMainDataList=()=>{
      return new Promise((resolve, reject) => {
        this.subs.sink = this._rmService
          .getResourceRequestList(this.filterConfig,this.tab_id)
          .subscribe(
            (res: any) => {
              if (res.err == false) resolve(res.data);
              else reject(res);
            },
            (err) => {
              console.log(err);
              reject(err);
            }
          );
      });
  }
  getTabList=(filterConfig)=>{
    return new Promise((resolve, reject) => {
      this.subs.sink = this._rmService
        .getResourceRequestFilterList(filterConfig)
        .subscribe(
          (res: any) => {
            if (res.err == false) resolve(res.data);
            else resolve(null);
          },
          (err) => {
            console.log(err);
            resolve(null);
          }
        );
    });
}

  /**
   * @description get status master
   */
  getRequestStatusMasterData() {
    return new Promise((resolve, reject) => {
      this.subs.sink = this._rmService.getRequestStatusList().subscribe(
        (res: any) => {
          if (!res.err) {
            resolve(res.data);
          } else {
            reject(res);
          }
        },
        (err) => {
          console.log(err);
        }
      );
    });
  }

  /**
   * @description For Help Desk Dialog Open
   */

  openHelpDialog(){
    let rmgHelpId=57;
    this._help.openHelpDialog(this.authService.getToken(),rmgHelpId,this.currentUser);
  }

  openAdminDialog(){
    // this._router.navigateByUrl(`/main/resource-management/home/<USER>
    this._router.navigateByUrl(this._rmService.getRouterLink("admin",null))
  }
  
  async callInlineEditApi() {

    let request_id = this.udrfService.udrfUiData.inlineEditData['dataSelected']['request_id']
    let index = this.udrfService.udrfUiData.inlineEditData['index']

    if(this.udrfService.udrfUiData.inlineEditData['inlineEditField'] == "Request status") {
      if(this.udrfService.udrfUiData.inlineEditData['dataSelected']['request_status_id'] != 
        this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['id']) {
        let statusId = this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['id'];
        let statusName = this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['name']
        await this.rmRequestStatusUpdate(request_id,statusId,statusName,index)
      }
    }
    else if(this.udrfService.udrfUiData.inlineEditData['inlineEditField'] == "Assigned To") {
      let assigned_to_name = this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['employee_name']
      let assigned_to = this.udrfService.udrfUiData.inlineEditData['inlineEditResponse']['associate_id']
      await this.assignRequestTo(request_id,assigned_to,index,assigned_to_name)
    }
  }

  async rmRequestStatusUpdate(requestId,statusId,statusName,index) {
    this.subs.sink = this._rmService.rmRequestStatusUpdate(requestId,statusId)
    .subscribe((res: any) => {
      if(res.err) {
        let warning_type = !res['has_access'] ? "Access Denied" : "Invalid status flow"
        this._toaster.showWarning(
          warning_type,
          res['msg']
        )
      }
      else {
        this._toaster.showSuccess("","Request status updated !",3000)
        this.udrfService.udrfBodyData[index]["request_status"] = statusName
        this.udrfService.udrfBodyData[index]["request_status_id"] = statusId
      }
    })
  }

  assignRequestTo(request_id,assigned_to,index,assigned_to_name){
    return new Promise((resolve, reject) => {
      let params ={request_id:request_id,
                   assigned_to:assigned_to}
      this.subs.sink = this._rmService.assignRequestTo(params).subscribe(
        (res: any) => {
          if (!res.err) {
            this.udrfService.udrfBodyData[index]["assigned_to"] = assigned_to_name
            this._toaster.showSuccess(
              'Success',
              'Request ID - '+request_id+' has been assigned to '+assigned_to_name,
              2000
            );
            resolve(res);
          } 
          else if(res.err && !res.executive_check){	
            this._toaster.showWarning(	
              'Warning',	
              res.msg	
            );	
            resolve(res);	
          }
          else {
            reject(res);
          }
        },
        (err) => {
          console.log(err);
        }
      );
    });
  }

  async openComments() {
    let selectedRowData = this.udrfService.udrfUiData.openCommentsData['data'];
    let inputData = {
      application_id: 460,
      unique_id_1: selectedRowData['request_id'],
      unique_id_2: '',
      application_name: 'Resource Management',
      title: `Request ID # ${selectedRowData['request_id']}`,
    };

    let modalParams = {
      inputData: inputData,
      context: {
        'Request ID': selectedRowData['request_id'],
        'Request context': selectedRowData['request_context_item'],
      },
      commentBoxHeight: '100vh',
      commentBoxScrollHeight: '80%',
    };

    const { ChatCommentContextModalComponent } = await import(
      'src/app/modules/shared-lazy-loaded-components/chat-comment-context-modal/chat-comment-context-modal.component'
    );

    const openChatCommentContextModalComponent = this.dialog.open(
      ChatCommentContextModalComponent,
      {
        height: '100%',
        width: '75%',
        position: { right: '0px' },
        data: { modalParams: modalParams },
      }
    );
  }

  retrieveFieldConfig(view_key) {
    return new Promise((resolve, reject) => {
      this.subs.sink = this._rmService.retrieveFieldConfig(view_key).subscribe(
        (res: any) => {
          resolve(res.data);
        },
        (err) => {
          console.log(err);
          reject(err);
        }
      );
    });
  }
  
  retrieveRmConfig() {
    return new Promise((resolve,reject) => {
      this.subs.sink = this._rmService.retrieveRmConfig()
      .subscribe((res: any) => {
        if(!res.err)
          resolve(res.data)
      }, (err) => {
        console.log(err)
      })
    })
  }

  getRmExecutiveCheck(): Promise<boolean>{
    return new Promise((resolve, reject) => {
      this.subs.sink = this._rmService.validateRmExecutive().subscribe(
        (res: any) => {
          if (!res.err) {
            if(res.is_executive){
              resolve(res.is_executive)
            }
            else
            resolve(false);
          } 
          else {
            resolve(false);
          }
        },
        (err) => {
          this._toaster.showError(	
            'Error',	
            'Failed to Check Rm Executive',
            2000
          );
          console.log(err);
          resolve(false);
        }
      );
    });
  }


  ngOnDestroy() {
    this._onDestroy.next();
    this._onDestroy.complete();
    this.udrfService.resetUdrfData();
    this.subs.unsubscribe();

    if(this.$mainDataSubscription)
      this.$mainDataSubscription.unsubscribe()

  }
  
}