import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { RolesService } from 'src/app/services/acl/roles.service';
import { colList } from '../interfaces/rm-landing.interface';
import * as _ from 'underscore';
import { Router } from '@angular/router';
import { LoginService } from 'src/app/services/login/login.service';
 
@Injectable({
  providedIn: 'root',
})
export class RmService {
  timelineUIData = {
    isSearchBarVisible: true,
    rowTitleText:''
  }
  public peopleAllocationEmployeeRole=[]

  app_access_list : any = []
  application_id = 460;

  routerLinks:any = {
    "requestDetail": "/main/people-allocation/home/<USER>/request_Id",
    "noAccess":"/main/people-allocation/home/<USER>",
    "requests":"/main/people-allocation/home/<USER>",
    "resourceAllocation":"main/people-allocation/home/<USER>/request_Id/allocation",
    "forwardRequest":"/main/people-allocation/home/<USER>/request_Id/forwardrequest",
    "requestStatus":"/main/people-allocation/home/<USER>/request_Id/requeststatus",
    "admin":"/main/people-allocation/home/<USER>",
    "smartAllocation": "/main/people-allocation/home/<USER>/smartAllocation/request_Id",
    "forwardToRecruitment":"/main/people-allocation/home/<USER>/request_Id/forwardToRecruitment"
  }
  constructor(private _http: HttpClient, private roleService: RolesService, private _router:Router, private loginService: LoginService) {
    this.app_access_list = this.getAllRoleAccess()
  }

  activeLink :string = this._router.url.split('/').pop();
  colList: colList[] = [
    {
      colName: 'Request ID',
      key_name: 'request_id',
      width: 180,
      type: 'text',
      isActive: true,
    },
    {
      colName: 'Project/Opportunity Name',
      key_name: 'request_context_item',
      width: 240,
      type: 'text1',
      isActive: true,
    },
    {
      colName: 'Source',
      key_name: 'SOURCE',
      width: 240,
      type: 'textFrom',
      isActive: true,
    },
    {
      colName: 'Requested By',
      key_name: 'requested_by',
      width: 240,
      type: 'textImg',
      isActive: true,
    },
    {
      colName: 'Expected Closure Date',
      key_name: 'expected_closure_date',
      width: 250,
      type: 'date',
      isActive: true,
    },
    {
      colName: 'Request status',
      key_name: 'request_status',
      width: 240,
      type: 'status',
      isActive: true,
    },
    {
      colName: 'Work Location',
      key_name: 'work_location',
      width: 240,
      type: 'text',
      isActive: true,
    },
    {
      colName: 'Commercial Type',
      key_name: 'commercial',
      width: 240,
      type: 'text',
      isActive: true,
    }
  ];
  public isGroupByView = false;
  public tableView: string = 'list';

  getPriorityList() {
    return this._http.get('/api/rmg/masterData/priorityList');
  }
  getProjectRole() {
    return this._http.get('/api/rmg/masterData/projectRole');
  }
  getNationalityList() {
    return this._http.get('/api/rmg/masterData/nationalityList');
  }
  getEmploymentType() {
    return this._http.get('/api/rmg/masterData/employmentType');
  }
  getWorkLocation() {
    return this._http.get('/api/rmg/masterData/workLocation');
  }
  getOperationModel() {
    return this._http.get('/api/rmg/masterData/operationModel');
  }
  getBookingType() {
    return this._http.post('/api/rmg/masterData/bookingType',{});
  }
  getPositionList() {
    return this._http.post('/api/rmg/masterData/positionList',{});
  }
  getSkillList() {
    return this._http.post('/api/rmg/masterData/skillList',{});
  }
  getSkillExperience() {
    return this._http.get('/api/rmg/masterData/skillExperience');
  }
  getWorkExperience() {
    return this._http.post('/api/rmg/masterData/workExperience',{});
  }
  getRequestContext() {
    return this._http.get('/api/rmg/masterData/requestContext');
  }
  getOpportunityMaster() {
    return this._http.post('/api/rmg/masterData/getOpportunityMaster',{});
  }
  createResourceRequest(val: any) {
    return this._http.post('/api/rmg/request/createResourceRequest', {
      params: val,
    });
  }
  getResourceRequestList(filterConfig,id) {
    return this._http.post('api/rmg/request/requestTabListUDRF', {
      params: filterConfig,
      tab_id: id
    });
  }
  getResourceRequestFilterList(filterConfig){
    return this._http.post('api/rmg/request/requestTabFilterList',{
      params:filterConfig
    });
  }
  getRequestDetailData(params){
    return this._http.post('api/rmg/request/requestDetailList',{
      params:params
    })
  }
  getViewMoreDetail(params){
    return this._http.post('api/rmg/request/viewMoreDetail',{
      params:params
    })
  }

  determineCostDetails(val: any) {
    return this._http.post('/api/rmg/request/determineCostDetails', {
      entity: val.entity,
      division: val.division,
      sub_division: val.sub_division,
      position: val.position,
      nationality: val.nationality,
      project_location: val.project_location,
      work_location: val.work_location,
    });
  }

  getresourceSuggestionList(filterConfig,request_id){
    return this._http.post('api/rmg/resources/resourceSuggestionList', {
      filterConfig,request_id,
    });
  }

  getResourceList(filterConfig){
    return this._http.post('api/rmg/resources/resourceList',  {
      params: filterConfig,
    });
  }

  getprojectList(filterConfig){
    return this._http.post('api/rmg/projects/projectsList',{
      params:filterConfig
    })
  }

  getConfirmAllocationDetail(params){
    return this._http.post('api/rmg/resources/confirmAllocationDetail',{
      params:params
    });
  }

  initiateResourceAllocation(params){
    return this._http.post('api/rmg/resources/initaiteResourceAllocation',{
      params:params
    });
  }

  getresourceTabFilterList(params){
    return this._http.post('api/rmg/resources/resourceTabFilterList',{
      params:params
    });
  }

  allocatedResourceActionListStatus(params){
    return this._http.post('api/rmg/requests/allocatedResourceActionListStatus',{
      request_id:params
    })
  }

  editRequestDetail(params){
    return this._http.post('api/rmg/request/editRequestDetail',{
      params:params
    })
  }

  resourceRequestEdit(bodyparams){
    console.log("Body Params for edit form",bodyparams)
    return this._http.post('api/rmg/request/resourceRequestEdit',{
      params:bodyparams
    })
  }

  getrmofficerstatus(bodyParams){
    return this._http.post('api/rmg/request/determineRmOfficier',{
      params:bodyParams
    })
  }
  getrequestfilter() {
    return this._http.post('/api/rmg/request/requestfilter',{});
  }
  // triggerAllocate(){
  //   this.activeLink = this._router.url.split('/').pop();
  // }

  resourceAvailablity(params){
    return this._http.post('api/rmg/resources/resourceAvailabilitySplitup',{
      params:params
    })
  }
  getRequestStatusList(){
    return this._http.post('/api/rmg/request/getRequestStatusfilter',{})
  }

  forwardRequest(reqParams) {
    return this._http.post('/api/rmg/requests/forwardRequest',{
      params: reqParams
    })
  }

  getOrgFromProject(item_id){
    return this._http.post(' api/rmg/request/getOrgFromProject',{
      params: item_id
    })
  }

  checkAccessForObject(object_id) {
    console.log("Check ACCESS OBJECT:", this.app_access_list)
    // return _.where(this.app_access_list,{object_id: object_id}).length > 0
    if(this.app_access_list && this.app_access_list.length > 0) {
      // Resource request data access object
      // let auth_obj_id = 548
      let auth_object = _.find(this.app_access_list, obj => {
        return obj.object_id == object_id
      })

      let auth_object_value = auth_object ? auth_object.object_value : 'User'

      if (auth_object_value == 'User') {
        return false;
      }
      return true;
    }
  }

  getAllRoleAccess() {
    let accessList = _.where(this.roleService.roles, { application_id: this.application_id });
    console.log("AccessList:", accessList)
    return accessList;
  }
  getManPowerCostdetails(){
    return this._http.post('api/rmg/adminconfig/getManPowerCostdetails',{})
  }

  getNonManPowerCostdetails(){
    return this._http.post('api/rmg/adminconfig/getNonManPowerCostdetails',{})
  }
  getCurrency(){
    return this._http.post('api/rmg/adminconfig/getCurrency',{})
  }
  updateManPowerMasterTable(rowValue){
    return this._http.post('api/rmg/adminconfig/updateManPowerMasterTable',{
      params : rowValue
    })
  }
  updateNonManPowerMasterTable(rowValue){
    return this._http.post('api/rmg/adminconfig/updateNonManPowerMasterTable',{
      params : rowValue
    })
  }
  insertManPowerMasterTable(rowValue){
    return this._http.post('api/rmg/adminconfig/insertManPowerMasterTable',{
      params : rowValue
    })
  }
  insertNonManPowerMasterTable(rowValue){
    return this._http.post('api/rmg/adminconfig/insertNonManPowerMasterTable',{
      params : rowValue
    })
  }
  getErrorLogs(){
      return this._http.post('api/rmg/adminconfig/getTransactionalErrorLogs',{})
    }
  initiateProjectBooking(params) {
    return this._http.post('/api/rmg/resources/newProjectBooking',{
      params: params
    })
  }
  getRmOfficerMappingList(){
    return this._http.post('/api/rmg/adminconfig/getRmOfficerMappingList',{})
  }
  updateRmOfficerMasterTable(params){
    return this._http.post('/api/rmg/adminconfig/updateRmOfficerMasterTable',{params})
  }
  getDivision(){
    return this._http.get('api/rmg/adminconfig/getDivision')
  }
  getSubDivision(){
    return this._http.get('api/rmg/adminconfig/getSubdivision')
  }
  insertRmOfficerMasterTable(params){
    return this._http.post('/api/rmg/adminconfig/insertRmOfficerMasterTable',{params})
  }
  assignRequestTo(params){
    return this._http.post('/api/rmg/requests/assignRequestTo',{
      params:params})
  }

  retrieveRmConfig() {
    return this._http.post('/api/rmg/utilityRoute/retrieveRmConfig', {})
  }

  // retrieveInlineEditStatusList() {
  //   return this._http.get('/api/rmg/masterData/retrieveInlineEditStatusList')
  // }

  rmRequestStatusUpdate(requestId,statusId) {
    return this._http.post('/api/rmg/requests/rmRequestStatusUpdate',
    {
      request_id: requestId,
      status_id: statusId
    })
  }
      
  getResourceManpowerCost(params) {
    return this._http.post('/api/rmg/adminconfig/getResourceManpowerCost', {
      params :  params
    })
  }
  getResourceNonManpowerCost(params) {
    return this._http.post('/api/rmg/adminconfig/getResourceNonManpowerCost', {
      params: params
    })
  }
  updateResourceManpowerCost(rowValue){
    return this._http.post('api/rmg/adminconfig/updateResourceManpowerCost',{
      params : rowValue
    })
  }
  updateResourceNonManpowerCost(rowValue){
    return this._http.post('api/rmg/adminconfig/updateResourceNonManpowerCost',{
      params : rowValue
    })
  }
  insertResourceManpowerCost(rowValue){
    return this._http.post('api/rmg/adminconfig/insertResourceManpowerCost',{
      params : rowValue
    })
  }
  insertResourceNonManpowerCost(rowValue){
    return this._http.post('api/rmg/adminconfig/insertResourceNonManpowerCost',{
      params : rowValue
    })
  }
  getSkillScoreList(){
    return this._http.post('api/rmg/adminconfig/getSkillScoreList',{})
  }
  updateSkillScoreList(rowvalue){
    return this._http.post('api/rmg/adminconfig/updateSkillScoreList',{
      params : rowvalue
    })
  }
  insertSkillScoreList(rowValue){
    return this._http.post('api/rmg/adminconfig/insertSkillScoreList',{
      params : rowValue
    })
  }
  getRequestStatusMasterList(){
    return this._http.post('api/rmg/adminconfig/getRequestStatusMasterList',{})
  }
  updateRequestStatusMasterList(rowValue){
    return this._http.post('api/rmg/adminconfig/updateRequestStatusMasterList',{
      params : rowValue
    })
  }
  insertRequestStatusMasterList(rowValue){
    return this._http.post('api/rmg/adminconfig/insertRequestStatusMasterList',{
      params : rowValue
    })
  }

  forwardToRecruitment(requestId){
    return this._http.post('api/rmg/requests/forwardToRecruitment',{
      request_id : requestId
    })
  }

  fetchRecruitmentData(recruitmentReqId){
    return this._http.post('api/isa/request/getRequestById',{
      requestId : recruitmentReqId
    })
  }
  getApplicationRoleData(){
    return this._http.get('api/rmg/adminconfig/getApplicationRoleData')
  }
  getApplicationRoleNameData(){
    return this._http.get('api/rmg/adminconfig/getApplicationRoleNameData')
  }

  getRmgApplicationObject(){
    return this._http.get('api/rmg/adminconfig/getRmgApplicationObject')
  }
  updateApplicationRole(rowValue){
    return this._http.post('api/rmg/adminconfig/updateApplicationRole',{
      params : rowValue
    })
  }
  insertApplicationRole(rowValue){
    return this._http.post('api/rmg/adminconfig/insertApplicationRole',{
      params : rowValue
    })
  }
  // retrieveProfile(oid){
  //   return this._http.post('api/rmg/requests/retrieveProfile',{
  //     params : oid
  //   })
  // }

  retrieveFieldConfig(view_key) {
    return this._http.post('/api/rmg/utilityRoute/retrieveFormFieldConfig',{
      view_key:view_key
    });
  }

  getRouterLink(key,req_id){
    let requestedUrl
    if(req_id != null){
      requestedUrl = this.routerLinks[key].replace('request_Id',req_id)
    }
    else {
      requestedUrl = this.routerLinks[key]
    }
    return requestedUrl;
  }
  
  resourceReportDownload(params:any) {
    return this._http.post('/api/rmg/resources/retrieveEmpProjectReport', {
      filter_config:params
    })
  }
  getCurrencyList() {
    return this._http.post('/api/rmg/masterData/currencyList',{});
  }
  getUnitList() {
    return this._http.post('/api/rmg/masterData/quoteUnitList',{});
  }

  getResourceSoftBookingList(filterConfig){
    return this._http.post('api/rmg/resources/resourceSoftBookingList',  {
      params: filterConfig,
    });
  }
  initiateSoftBooking(params:any){
    return this._http.post('api/rmg/resources/initiateSoftBooking',{
      params:params
    });
  }
  getReportUnassigned(params:any){
    return this._http.post('/api/rmg/projects/unAllocatedEmployee', {
      params:params
    })
  }

  getRegionList(){
    return this._http.post(' api/rmg/masterData/regionList',{})
  }

  validateRmExecutive(){
    return this._http.post('/api/rmg/requests/validateRmExecutive',{})
  }

  getRequestProjectDetails(request_id){
    return this._http.post('/api/rmg/projects/getRequestProjectDetails',{
      request_id: request_id
    })
  }
  getRequestTypeDetails() {
    return this._http.post('/api/rmg/masterData/requestTypeList',{});
  }
  updateEmployeeAvailability(associate_id,project_availability_status) {
    return this._http.post('/api/rmg/resources/updateEmployeeAvailability',{
      associate_id: associate_id,
      project_availability_status: project_availability_status
    });
  }
  getWorkCityList() {
    return this._http.post('/api/rmg/masterData/workCity',{});
  }
  getWorkPremisisList() {
    return this._http.post('/api/rmg/masterData/workPremisis',{});
  }
  getEmployeeDetails(associate_id){
    return this._http.post('/api/rmg/masterData/getEmployeeDetails', {
      'associate_id':associate_id
    })
  }
  retrieveUploadedObjects = (destination_bucket, context_id) => {
    return this._http.post('/api/exPrimary/retrieveUploadedObjects', {
      bucket_name: destination_bucket,
      context_id: context_id,
    });
  };
  getHrDocuments(associateId) {
    return this._http.post('/api/employee360/employeeDetails/getHrDocuments', {
      associate_id: associateId,
    });
  }
  getDownloadUrl = (cdn_link) => {
    return this._http.post('/api/exPrimary/getDownloadUrl', {
      cdn_link: cdn_link,
    });
  };
  forwardToRecruitmentOpenApi(requestId,job_code){
    return this._http.post('api/rmg/requests/forwardToRecruitmentOpenApi',{
      request_id : requestId,
      job_code: job_code
    })
  }
  getTravelTypeList() {
    return this._http.post('/api/rmg/masterData/travelTypeList',{});
  }
  
  getShiftMasterList() {
    return this._http.post('/api/rmg/masterData/shiftMasterList',{});
  }

  getUDRFResourceList(){
    return this._http.post('/api/rmg/masterData/resourceStatusMaster',{})
  }

  checkPeopleAllocationAccess(){
    return new Promise(async(resolve, reject)=>{

      if(this.roleService.roles && this.roleService.roles.length>0)
      {
        let accessList = _.where(this.roleService.roles, { application_id: 460 });
        
        this.peopleAllocationEmployeeRole = accessList

        let projectAccess = _.where(this.peopleAllocationEmployeeRole,{object_id: 6})

        if (projectAccess.length > 0)
        {
          return resolve(true)
        }
        else
        {
          return resolve(false)
        }
      }
      else
      {
    
        const user = this.loginService.getProfile().profile;
        await this.getAccessList(user.oid, "people_allocation").subscribe((res=>{
    
          let accessList = _.where(res, { application_id: 460 });
          
          this.peopleAllocationEmployeeRole = accessList

          let projectAccess = _.where(this.peopleAllocationEmployeeRole,{object_id: 6})

          if (projectAccess.length > 0)
          {
            return resolve(true);
          }
          else
          {
            return resolve(false);
          }
        }))
      }
    })

  
    
  }

  getAccessList(userOid, type) {
    return this._http.post('/api/pm/auth/getAccessFor', {oid: userOid,type: type,});
  }

  getSmartAllocationData(payload) {
    return this._http.post('/api/atsai/peoples/allocation', payload)
  }
}

