<mat-card class="tile" (click)="dataPassing(data.request_id)">
        <!-- <div class="row d-flex">
            <span class="title"><b>{{data['SOURCE'] ? data['SOURCE'] : '-'}}</b></span>
            <mat-checkbox class="check-icon"></mat-checkbox>
        </div>
        <div class="row d-flex">
            <span class="text">REQ ID </span> 
            <span class="textValue" style="flex: 2; margin-left: 11px;">{{data['request_id'] ? data['request_id'] :'-'}}</span>
            <span class="textStatus">{{data['request_status'] ? data['request_status'] : '-'}}</span>
        </div>
        <div>
            <div *ngIf="data['requested_by']" style="width: 60%;">
                <app-user-profile type="small" [oid]="data['requested_by']">
                </app-user-profile>
            </div>
            <div *ngIf="!data['requested_by']">
                -
            </div>
        </div>
        <div>
            <span class="d-block text">Suitable Resources</span>
            <span class="textValue">{{data['resources'] ? data['resources'] : '-'}}</span>
        </div> -->
        <div class="row">
            <div class="col" *ngFor="let col of colList">
                <div class="col-10" *ngIf="col.tileType === 'id'" style="display: contents;">
                    <span class="text zf-theme">{{col.header ? col.header : "-"}}</span> 
                    <span class="textValueId zf-theme" style="margin-left: 6px;" [matTooltip]="data[col.item] ? data[col.item] : '-'">{{data[col.item] ? data[col.item] : "-"}}</span>
                </div>
                <div class="col-2 d-flex" *ngIf="col.tileType === 'status'" style="padding-left: 69px;width: 5vw; margin-bottom: -10px;margin-top: -8px;">
                    <span class="textStatus zf-theme" [ngStyle]="{background: getStatusColor(data[col.item])}"><span class="text-ellip-class" [matTooltip]="data[col.item] ? data[col.item] : '-'">{{data[col.item] ? data[col.item] : "-"}}</span></span>
                </div>
                <div class="row d-flex" *ngIf="col.tileType === 'header'">
                    <span class="title zf-theme"  [matTooltip]="data[col.item] ? data[col.item] : '-'"><b>{{data[col.item] ? data[col.item] : "-"}}</b></span>
                    <!-- <mat-checkbox class="check-icon"></mat-checkbox> -->
                </div>
                <div style="width:35vw" *ngIf="col.tileType === 'textImg'" class="col-12 pl-0">
                    <div *ngIf="col.tileType === 'textImg' && data[col.item]" style="width: 13vw;">
                        <app-user-profile type="small" [oid]="data[col.item]" [fetchProfileNameFromE360]="true">
                        </app-user-profile>
                    </div>
                    <div *ngIf="col.tileType === 'textImg' && !data[col.item]">
                        -
                    </div>
                </div>
                <div *ngIf="col.tileType === 'date'" class="col-12 pl-0">
                    <span class="d-block text zf-theme">{{col.header ? col.header : "-"}}</span>
                    <span class="textValue zf-theme">{{ getDate(data[col.item]) }}</span>
                </div>
            </div>
        </div>
        
</mat-card>