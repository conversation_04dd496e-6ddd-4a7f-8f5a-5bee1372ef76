import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import moment from 'moment';

@Component({
  selector: 'rm-tile-view',
  templateUrl: './tile-view.component.html',
  styleUrls: ['./tile-view.component.scss']
})
export class TileViewComponent implements OnInit {
  @Output() reqdetaildata = new EventEmitter();
  @Input() colList:any;
  @Input() data:any;
  @Input() requestStatusList:any;
  constructor() { }

  async ngOnInit() {
    // console.log("Tile View Data Call:",this.data);
    // console.log("Request Status List:",this.requestStatusList);
  }
  dataPassing(value) {
    this.reqdetaildata.emit(value);
  }


  getStatusColor(status) {

    for (let item of this.requestStatusList)
      if (item.status == status)
        return item.color;

  }

  getDate(date){
    if(date && date != 'Invalid date'){
      return moment(date).format('DD-MMM-YYYY');
    }
    else{
      return '-'
    }
  }


}
