<div class="row">
  <div class="col-10"></div>
  <div class="col-2">
    <button mat-icon-button (click)="downloadFile(data.selectedFileUrl)">
      <mat-icon>download</mat-icon>
    </button>
    <button mat-icon-button (click)="dialogRef.close()" matTooltip="Close">
      <mat-icon>close</mat-icon>
    </button>
  </div>
</div>
<div
  *ngIf="
    !isLoaded &&
    data.fileFormat != 'png' &&
    data.fileFormat != 'jpg' &&
    data.fileFormat != 'jpeg' &&
    data.fileFormat != 'JPEG' &&
    data.fileFormat != 'JPG' 
  "
>
  <div class="d-flex align-items-center justify-content-center">
    <img src="https://assets.kebs.app/images/spinner.svg" alt="" />
  </div>
  <div class="d-flex align-items-center justify-content-center">
    Please wait, we are opening the file!
  </div>
</div>

<ngx-doc-viewer
  *ngIf="
    data.fileFormat != 'png' &&
      data.fileFormat != 'jpg' &&
      data.fileFormat != 'JPEG' &&
      data.fileFormat != 'JPG' &&
      data.fileFormat != 'jpeg';
    else showimg
  "
  [url]="data.selectedFileUrl"
  viewer="google"
  style="height: 100%"
  (loaded)="isLoaded = true"
></ngx-doc-viewer>

<ng-template #showimg>
  <div class="d-flex align-items-center justify-content-center mt-5">
    <img [src]="data.selectedFileUrl" alt="" style="height: 80%; width: 80%" />
  </div>
</ng-template>
