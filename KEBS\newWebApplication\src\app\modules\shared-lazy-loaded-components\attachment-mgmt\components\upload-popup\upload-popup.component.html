<div class="popup" id="popup2">
  <div class="row border-bottom solid">
    <div class="col-9 pt-3">
      <strong><h3>Attachments</h3></strong>
    </div>
    <div class="col-3 d-flex justify-content-end">
      <button mat-icon-button (click)="saveChanges()"  matTooltip="Close">
        <mat-icon>close</mat-icon>
      </button>
    </div>
  </div>
  <div class="row" style="height: 81vh">
    <div
      ng2FileDrop
      class="col border-right solid"
      (fileOver)="fileOverBase($event)"
      [uploader]="uploader"
    >
      <div
        cdkDropList
        id="toUpload"
        #toUpload="cdkDropList"
        [cdkDropListData]="filesUploaded"
        (cdkDropListDropped)="drop($event)"
        [ngClass]="{ 'nv-file-over': hasBaseDropZoneOver }"
        class="well my-drop-zone mt-4"
      >
        <div class="d-flex p-3 flex-wrap">
          <div
            class="folder mr-3"
            (click)="viewFile(file)"
            *ngFor="let file of filesUploaded; let i = index"
            (contextmenu)="onContextMenu($event, file, i)"
            style="width: 40%;"
          >
            <div style="position: relative; width: 0; height: 0">
              <div style="position: absolute; left: 57px; top: -5px">
                <div
                  class="spinner-border boo-spinner"
                  role="status"
                  *ngIf="file.isUploaded == false"
                >
                  <span class="sr-only">Loading...</span>
                </div>
                <mat-icon class="boo-spinner" *ngIf="file.isUploaded == true"
                  >done</mat-icon
                >
                <mat-icon
                  class="boo-spinner"
                  *ngIf="file.isUploaded == 'failed'"
                  >refresh</mat-icon
                >
              </div>
            </div>

            <div class="row thumbnail">
              <img
                *ngIf="
                  file.file_format != 'png' &&
                    file.file_format != 'jpg' &&
                    file.file_format != 'jpeg';
                  else showimg
                "
                [src]="file.file_format | fileIcon"
                style="height: 44px; width: 44px"
                alt=""
              />
              <ng-template #showimg>
                <img
                  [src]="file.cdn_link | getUrl | async"
                  style="height: 44px; width: 44px"
                  alt=""
                />
              </ng-template>
            </div>
            <div
              class="row"
              matTooltip="{{ file.file_name }}"
              style="font-size: 12px; justify-content: center;"
            >
              {{ file.file_name }}
            </div>
          </div>
          <div
            style="visibility: hidden; position: fixed"
            [style.left]="contextMenuPosition.x"
            [style.top]="contextMenuPosition.y"
            [matMenuTriggerFor]="contextMenu"
          ></div>
          <mat-menu #contextMenu="matMenu">
            <ng-template matMenuContent let-item="item">
              <button
                mat-menu-item
                *ngFor="let action of fileActions"
                (click)="onContextMenuAction(item, action)"
              >
                {{ action }}
              </button>
            </ng-template>
          </mat-menu>
        </div>

        <ng-container *ngIf="isEditingAllowed">
          <div class="d-flex align-items-center justify-content-center">
            <mat-icon class="file_upload_icon">file_upload</mat-icon>
          </div>
          <div class="d-flex align-items-center justify-content-center">
            <strong
              ><h2>
                Drag & Drop from <span style="color: #cf0001"> My Files</span>
              </h2></strong
            >
          </div>
          <div class="d-flex align-items-center justify-content-center">
            <strong
              ><h3>
                Or &nbsp;
                <button mat-stroked-button (click)="fileInput.click()">
                  Browse file
                </button>
                <input
                  hidden
                  #fileInput
                  type="file"
                  ng2FileSelect
                  [uploader]="uploader"
                  multiple
                />
                &nbsp; from device
              </h3></strong
            >
          </div>
        </ng-container>
      </div>
    </div>
    <div class="col-5" *ngIf="isEditingAllowed">
      <div class="row border-bottom solid pt-3">
        <strong>Choose from my files</strong>
      </div>
      <div class="row">
        <div class="col-12 p-0">
          <attachment-my-files
            [myFilesDefaultFolder]="data.myFilesDefaultFolder"
          ></attachment-my-files>
        </div>
      </div>
    </div>
  </div>
  <!-- <div class="row bottom-warning">
    <div class="col-12">
      
    </div>
  </div> -->
</div>
