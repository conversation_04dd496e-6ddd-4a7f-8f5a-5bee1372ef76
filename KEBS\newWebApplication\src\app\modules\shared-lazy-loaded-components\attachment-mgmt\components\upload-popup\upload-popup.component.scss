.my-drop-zone {
  border: dotted 3px lightgray;
  height: 80%;
}
.nv-file-over {
  border: dotted 3px red;
  height: 66%;
} /* Default class applied to drop zones on over */
.another-file-over-class {
  border: dotted 3px green;
  height: 66%;
}

.folder {
  font-size: 15px;
  cursor: pointer;
  padding: 10px;
}

.folder:hover {
  background: #f9f5f5f7;
  padding: 10px;
  font-size: 15px;
  cursor: pointer;
  transform: scale(1.1);
}

.boo-spinner {
  height: 1rem;
  width: 1rem;
}
.thumbnail {
  display: flex;
  justify-content: center;
  align-items: center;
}
.file_upload_icon {
  font-size: 5rem;
  height: 84px;
  width: 84px;
  color: #b3b3b3;
}
.browse-files-text {
  color: #7b2cbf;
  font-weight: bolder;
  cursor: pointer;
}
.default-file-input {
  opacity: 0;
}
.bottom-warning {
  position: sticky;
  height: 44px;
  bottom: 0px;
  background-color: #cf0001;
}
