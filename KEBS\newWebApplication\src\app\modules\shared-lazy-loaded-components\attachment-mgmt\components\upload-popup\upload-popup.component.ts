import { Component, Inject, Input, OnInit, ViewChild } from '@angular/core';
import { FileUploader } from 'ng2-file-upload';
import {
  CdkDragDrop,
  moveItemInArray,
  transferArrayItem,
} from '@angular/cdk/drag-drop';
import { MatSnackBar } from '@angular/material/snack-bar';
import { SharedLazyLoadedComponentsService } from '../../../services/shared-lazy-loaded-components.service';
import {
  MatDialog,
  MatDialogRef,
  MAT_DIALOG_DATA,
} from '@angular/material/dialog';
import { pluck } from 'rxjs/operators';
import { DocViewerComponent } from '../doc-viewer/doc-viewer.component';
import { LoginService } from 'src/app/services/login/login.service';
import { MatMenuTrigger } from '@angular/material/menu';
import swal from 'sweetalert2';

@Component({
  selector: 'attachment-upload-popup',
  templateUrl: './upload-popup.component.html',
  styleUrls: ['./upload-popup.component.scss'],
})
export class UploadPopupComponent implements OnInit {
  destinationBucket: string;
  routingKey: string;
  contextId: string;
  isDialogOpened = false;

  URL = '/api/exPrimary/uploadObjectFromDevice';
  uploader: FileUploader;
  hasBaseDropZoneOver: boolean;
  response: string;
  filesUploaded: any = [];
  isEditingAllowed: boolean = false;
  fileActions = ['Delete', 'Rename'];
  @ViewChild(MatMenuTrigger)
  contextMenu: MatMenuTrigger;
  contextMenuPosition = { x: '0px', y: '0px' };
  expHeaderId: any;
  allowDeleteRename: boolean = true;
  documentType: string;
  constructor(
    private _snackBar: MatSnackBar,
    public dialog: MatDialog,
    private _login: LoginService,
    private _sharedService: SharedLazyLoadedComponentsService,
    public dialogRef: MatDialogRef<UploadPopupComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.data = data['data'];
    this.expHeaderId = data["expHeaderId"];
    this.contextId = this.data.contextId;
    this.routingKey = this.data.routingKey;
    this.documentType = this.data.documentType || ""
    this.destinationBucket = this.data.destinationBucket;
    if (this.data.allowEdit) {
      this.isEditingAllowed = this.data.allowEdit;
    }
    if (this.data.allowDeleteRename==false) {
      this.allowDeleteRename = this.data.allowDeleteRename;
    }
    if (this.contextId == undefined || this.contextId == null)
      this.contextId = `${this.routingKey}-${this.create_UUID()}`;
    else this.retrieveUploadedFiles();

    this.uploader = new FileUploader({
      url: this.URL,
      authToken: 'Bearer ' + this._login.getToken(),
      disableMultipart: false,
      headers: [
        {
          name: 'context-id',
          value: this.contextId,
        },
        {
          name: 'routing-key',
          value: this.routingKey,
        },
        {
          name: 'bucket-name',
          value: this.destinationBucket,
        },
        {
          name: 'document-type',
          value: this.documentType,
        },
      ],
      maxFileSize: 1024 * 1024 * 10, //10mb
    });
    this.detectUploadChanges();
  }

  ngOnInit(): void {}

  detectUploadChanges() {
    this.uploader.onProgressItem = (progress: any) => {
      console.log('in Progress');
    };
    this.uploader.onCompleteItem = (
      item: any,
      response: any,
      status: any,
      headers: any
    ) => {
      this.uploader.removeFromQueue(item);
      let meta_data = JSON.parse(response).data;
      meta_data.isUploaded = true;
      let indexes = [];
      this.filesUploaded.forEach((file, i) => {
          if (file.file_name === meta_data.file_name && !file.isUploaded) {
              indexes.push(i);
          }
      });
      
      indexes.forEach(index => {
          this.filesUploaded[index] = meta_data;
      });
    };

    this.uploader.onWhenAddingFileFailed = (item) => {
      this._snackBar.open('Failed to upload file', 'dismiss', {
        duration: 1000,
      });
    };

    this.uploader.onAfterAddingFile = (item) => {
      if (this.isEditingAllowed) {

        let fileName = '';

        if(this.containsSpecialChars(item.file.name)){

          fileName = item.file.name.replace(/[^a-zA-Z0-9. ]/g, '_');
          let temp_file_data = {
            file_name:  fileName,
            file_format: item.file.name.split('.').pop(),
            cdn_link: `https://assets.kebs.app/lms/svgs/file.svg`,
            isUploaded: false,
          };
          this.filesUploaded.push(temp_file_data);
          this.uploader.uploadItem(item);

          // swal
          // .fire({
          //   title: 'We would recommend file names without special characters',
          //   text: `The file ${item.file.name} has special characters. Do you want to rename as ${item.file.name.replace(/[^a-zA-Z0-9 ]/g, '_')}`,
          //   icon: 'warning',
          //   showCancelButton: true,
          //   confirmButtonColor: '#3085d6',
          //   cancelButtonColor: '#d33',
          //   confirmButtonText: 'Rename it',
          // })
          // .then((result) => {
          //   if (result.isConfirmed) {
          //     fileName = item.file.name.replace(/[^a-zA-Z0-9. ]/g, '_');
          //     let temp_file_data = {
          //       file_name:  fileName,
          //       file_format: item.file.name.split('.').pop(),
          //       cdn_link: `https://assets.kebs.app/lms/svgs/file.svg`,
          //       isUploaded: false,
          //     };
          //     this.filesUploaded.push(temp_file_data);
          //     this.uploader.uploadItem(item);
          //   }
          // });
        }
        else{
          fileName = item.file.name;
          let temp_file_data = {
            file_name:  fileName,
            file_format: item.file.name.split('.').pop(),
            cdn_link: `https://assets.kebs.app/lms/svgs/file.svg`,
            isUploaded: false,
          };
          this.filesUploaded.push(temp_file_data);
          this.uploader.uploadItem(item);
        }
  
      }
    };
  }

   containsSpecialChars = (str) => {
    const specialChars = /[`!@#$%^&*()_+\-=\[\]{};':"\\|,<>\/?~]/;
    return specialChars.test(str);
  }


  create_UUID() {
    var dt = new Date().getTime();
    var uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(
      /[xy]/g,
      function (c) {
        var r = (dt + Math.random() * 16) % 16 | 0;
        dt = Math.floor(dt / 16);
        return (c == 'x' ? r : (r & 0x3) | 0x8).toString(16);
      }
    );
    return uuid;
  }

  drop(event: CdkDragDrop<string[]>) {
    if (event.previousContainer.id != event.container.id) {
      let added_file: any = event.previousContainer.data[event.previousIndex];
      if (this.containsObject(added_file, this.filesUploaded))
        this._snackBar.open(
          'You have already added a file in that name',
          'close',
          {
            duration: 1000,
          }
        );
      else {
        //upload the file and save response
        let file = { isUploaded: false, ...added_file };
        this.filesUploaded.push(file);
        let uploadedIndex = this.filesUploaded.length - 1;
        console.log(this.contextId, this.routingKey, this.destinationBucket);
        this._sharedService
          .copyObjectToDestination(
            this.destinationBucket,
            this.routingKey,
            added_file,
            this.contextId
          )
          .subscribe(
            (res: any) => {
              res.data.isUploaded = true;
              this.filesUploaded[uploadedIndex] = res.data;
            },
            (err) => {
              this.filesUploaded[uploadedIndex].isUploaded = 'failed';
              console.error(err);
            }
          );
      }
    }
  }

  containsObject(obj, list) {
    var i;
    for (i = 0; i < list.length; i++) {
      if (list[i].file_name === obj.file_name) {
        return true;
      }
    }

    return false;
  }

  retrieveUploadedFiles = () => {
    this._sharedService
      .retrieveUploadedObjects(this.destinationBucket, this.contextId)
      .pipe(pluck('data'))
      .subscribe(
        (res: any) => {
          this.filesUploaded.push(...res);
        },
        (err) => {
          console.error(err);
        }
      );
  };
  
  viewFile(file) {
    let cdn_link = file.cdn_link;
    let uploadPopUpDialogRef = null;
    if(this.isDialogOpened == false){
      this.isDialogOpened = true;
    this._sharedService.getDownloadUrl(cdn_link).subscribe((res: any) => {
      uploadPopUpDialogRef = this.dialog.open(DocViewerComponent, {
        width: '100%',
        height: '100%',
        data: {
          selectedFileUrl: res.data,
          fileFormat: file.file_format,
          expHeaderId: this.expHeaderId
        },
      });

      uploadPopUpDialogRef.afterClosed().subscribe((res: any) => {
        this.isDialogOpened = false;
      });
    });
  }
}

  saveChanges() {
    this.dialogRef.close({
      contextId: this.filesUploaded.length > 0 ? this.contextId : null,
      fileCount: this.filesUploaded.length,
    });
  }

  onContextMenu(event: MouseEvent, item, index) {
    if (this.isEditingAllowed) {
      item.index = index;
      event.preventDefault();
      this.contextMenuPosition.x = event.clientX + 'px';
      this.contextMenuPosition.y = event.clientY + 'px';
      this.contextMenu.menuData = { item: item };
      this.contextMenu.openMenu();
    }
  }
  onContextMenuAction(file, action) {
    let index = file.index;
    if(this.allowDeleteRename){
    if (action == 'Delete') {
      swal
        .fire({
          title: 'Are you sure?',
          text: 'Your file will deleted permanently!',
          icon: 'warning',
          showCancelButton: true,
          confirmButtonColor: '#3085d6',
          cancelButtonColor: '#d33',
          confirmButtonText: 'Delete',
        })
        .then((result) => {
          if (result.isConfirmed) {
            this._sharedService
              .deleteObj(file, 't_app_attachments_meta')
              .subscribe(
                (res: any) => {
                  this.filesUploaded.splice(index, 1);
                },
                (err) => {
                  console.error(err);
                }
              );
          }
        });
    } else if (action == 'Rename') {
      let fileName = window.prompt(
        'Please enter the file name with .extension (sample.png)!'
      );
      if (fileName != '' && fileName != null) {
        this._sharedService
          .renameObj(file, fileName, 't_app_attachments_meta')
          .subscribe(
            (res) => {
              this.filesUploaded[index].cdn_link = this.filesUploaded[
                index
              ].cdn_link.replace(this.filesUploaded[index].file_name, fileName);
              this.filesUploaded[index].file_name = fileName;
              this._snackBar.open('File Renamed successfully', 'dismiss', {
                duration: 1000,
              });
            },
            (err) => {
              console.error(err);
            }
          );
      }
    }
  }
  else{
    this._snackBar.open('Edit is restricted', 'dismiss', {
      duration: 1000,
    });
  }
  }

  public fileOverBase(e: any): void {
    this.hasBaseDropZoneOver = e;
  }
}
