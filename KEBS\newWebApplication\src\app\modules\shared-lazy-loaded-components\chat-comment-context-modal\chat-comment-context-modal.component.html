<div class="container-fluid chat-context-styles d-flex flex-column p-0" [ngStyle]="{ height: getCommentBoxHeight() }">
  <!-- Title and Close button row-->
  <div class="row pt-0" style="border-bottom: solid 1px #cacaca">
    <div class="col-11 pt-1 pb-2 d-flex">
      <div mat-icon-button class="bubble mt-1 ml-1">
        <mat-icon class="iconButton">forum</mat-icon>
      </div>
      <span class="name my-auto ml-3">Comments in</span>
      <span class="normal-font my-auto ml-2">{{ title }}</span>
    </div>
    <div class="col-1 d-flex">
      <button mat-icon-button class="ml-auto close-button mt-1" matTooltip="Close" (click)="closeComments()">
        <mat-icon class="close-Icon">close</mat-icon>
      </button>
    </div>
  </div>

  <!-- Comments Area-->
  <div class="overflow-scroll pb-2" [ngStyle]="{ 'min-height': getCommentScrollHeight() }" #scrollFrame>
    <div class="row mt-3" style="min-width: 90%" *ngFor="let comment of comments; let index = index">
      <!-- Own Comments-->
      <div class="col-12 pl-1 d-flex" *ngIf="comment.commentor_oid == currentUser.oid && showOwnComments">
        <div class="chat-outgoing p-0 pb-1">
          <!-- Context area -->
          <div class="col-12 p-0 card context-card-comments" *ngIf="
              comment.context && getContextKeys(comment.context).length > 0
            ">
            <div class="card-body p-1">
              <div class="row pb-1">
                <div class="col-6 pl-1 pr-1">
                  <span class="context-title"> Context - </span>
                </div>
              </div>
              <div class="row">
                <div class="col-6 pr-1 pb-1 pl-0 d-flex" *ngFor="let contextKey of getContextKeys(comment.context)">
                  <div class="col-6 pl-1 pr-1">
                    <span class="name">{{ contextKey }} <span style="float: right;">:</span> </span>
                  </div>
                  <div class="col-6 pl-0 pr-0 overflow-hidden">
                    <span class="name">{{
                      modalParams.context[contextKey]
                      }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Date area -->
          <div class="row mt-2" style="height: 21px !important">
            <div class="col-12 pl-2 pr-2 d-flex">
              <span class="comment-date pl-3 ml-auto mr-1">{{
                getDateFormat(comment.time)
                }}</span>

              <button mat-icon-button aria-label="Triple dot menu" [matMenuTriggerFor]="cmntMenu" class="cmnt-menu" *ngIf="!isReadOnly">
                <mat-icon class="cmnt-menu-icon">more_vert</mat-icon>
              </button>

              <mat-menu #cmntMenu="matMenu">
                <button mat-menu-item (click)="deleteComment(index)">
                  Delete Comment
                </button>
              </mat-menu>
            </div>

            <!-- <span class="comment-user-name-left">
              {{ comment.commentor_name }}
            </span> -->
          </div>

          <span class="mx-3 my-2 chatBox">{{ comment.comment }}</span>
        </div>
        <app-user-image class="my-auto pr-1 ml-1" [id]="comment.commentor_oid" imgWidth="28px" imgHeight="28px">
        </app-user-image>
      </div>

      <!-- Others Comments-->
      <div class="col-12 pl-3 d-flex" *ngIf="comment.commentor_oid != currentUser.oid && showOthersComments">
        <app-user-image class="my-auto pr-1" [id]="comment.commentor_oid" imgWidth="28px" imgHeight="28px">
        </app-user-image>
        <div class="col-11 chat-incoming p-0 pb-1">
          <!-- Context area -->
          <div class="col-12 p-0 card context-card-comments" *ngIf="
              comment.context && getContextKeys(comment.context).length > 0
            ">
            <div class="card-body p-1">
              <div class="row pb-1">
                <div class="col-6 pl-1 pr-1">
                  <span class="context-title"> Context - </span>
                </div>
              </div>
              <div class="row">
                <div class="col-6 pb-1 pr-1 pl-0 d-flex" *ngFor="let contextKey of getContextKeys(comment.context)">
                  <div class="col-6 pl-1 pr-1">
                    <span class="name">{{ contextKey }} <span style="float: right;">:</span> </span>
                  </div>
                  <div class="col-6 pl-0 pr-0 overflow-hidden">
                    <span class="name">{{
                      modalParams.context[contextKey]
                      }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Date area -->
          <div class="row mt-2" style="height: 21px !important">
            <span class="comment-user-name pl-3 mr-3">
              {{ comment.commentor_name }}
            </span>
            <span class="comment-date mr-3">
              {{
              getDateFormat(comment.time)
              }}</span>
          </div>

          <div class="row px-3">
            {{ comment.comment }}
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Context for new comments -->
  <div style="position: relative">
    <div class="col-12 p-0 card context-card" *ngIf="modalParams.context && isContextEnabled">
      <div class="card-body p-1">
        <div class="row pb-1">
          <div class="col-6 pl-1 pr-1">
            <span class="context-title"> Context - </span>
          </div>
        </div>
        <div class="row">
          <div class="col-6 pb-1 pr-1 pl-0 d-flex" *ngFor="let contextKey of contextKeys">
            <div class="col-6 pl-1 pr-1">
              <span class="name">{{ contextKey }} <span style="float: right;">:</span> </span>
            </div>
            <div class="col-6 pl-0 pr-0 overflow-hidden">
              <span class="name" [matTooltip]="modalParams.context[contextKey]">{{ modalParams.context[contextKey]
                }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- New Comments input area-->
  <div class="row mx-0 pt-2" style="border-top: 1px solid #c1bfbf; z-index: 1; background-color: white" *ngIf="!isReadOnly">
    <div class="col-1" style="padding-top: 9px">
      <app-user-image class="my-auto pr-1" [id]="currentUser.oid" imgWidth="30px" imgHeight="30px">
      </app-user-image>
    </div>
    <div class="col-10 pl-0 pr-2 d-flex">
      <mat-form-field class="comment-box">
        <input type="text" matInput #comment placeholder="Enter comment here ..." id="comment"
          (keydown)="onKeydown($event)" [mentionConfig]="mentionConfig" (itemSelected)="selectMention($event)" />
      </mat-form-field>
      <button class="ml-auto mt-1" [ngClass]="{
          'trend-button-active': isContextEnabled,
          'trend-button-inactive': !isContextEnabled
        }" mat-icon-button (click)="toggleContext()">
        <mat-icon class="iconButton">list</mat-icon>
      </button>
    </div>
    <div class="col-1">
      <button class="mt-1 send-button" mat-icon-button (click)="enterComment($event.target.value, comments.length + 1)">
        <mat-icon style="color: white; font-size: 20px">send</mat-icon>
      </button>
    </div>
  </div>
</div>