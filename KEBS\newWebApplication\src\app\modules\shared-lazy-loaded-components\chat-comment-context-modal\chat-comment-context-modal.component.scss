.chat-context-styles {
  .comment-row {
    height: 70vh;
    // div:first-child {
    //   height: 60%;
    // }
    // div:last-child {
    //   height: 60%;
    // }
  }

  .cmnt-menu {
    height: 15px;
    width: 15px;
    line-height: 15px;

    .cmnt-menu-icon {
      font-size: 18px;
    }
  }

  .bubble {
    display: flex;
    line-height: 26px;
    border-radius: 50%;
    cursor: context-menu !important ;
    width: 26px;
    height: 26px;
    background-color: #fbfbfb;
    box-shadow: 0 4px 4px -1px rgba(0, 0, 0, 0.2),
      0 1px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 14px 0 rgba(0, 0, 0, 0.12);
    .iconButton {
      color: #cf0001;
      font-size: 18px;
      margin-top: 3px !important;
      margin-left: 4px !important;
    }
  }

  .name {
    font-size: 14px;
   // white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #1a1a1a;
  }

  .context-title {
    color: #545352;
    font-size: 14px;
    font-weight: bold;
  }
  .normal-font {
    color: #cf0001;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 14px;
    font-weight: 500;
  }

  .close-Icon {
    font-size: 18px !important;
    color: #4d4d4b !important;
  }

  .close-button {
    width: 38px !important;
    height: 38px !important;
    line-height: 38px !important;
  }

  .footer-bar {
    position: absolute;
    bottom: 0 !important;
  }
  .comment-box {
    width: 100%;
    font-size: 14px;
    input {
      height: 30px;
      padding-bottom: 9px;
    }
    ::ng-deep .mat-form-field-wrapper {
      padding-bottom: 0px !important;
    }
  }
  .comment-date {
    font-size: 11px;
    color: #464646;
  }

  .comment-user-name {
    padding-right: 10px;
    font-size: 11px;
    font-weight: normal;
    color: #464646;
    // color: #cf0001d6;
  }

  .comment-user-name-left {
    padding-left: 20px;
    font-size: 11px;
    font-weight: normal;
    color: #252424;
    // color: #cf0001d6;
  }

  // .chat-incoming {
  //   background: #f3f3f3;
  //   display: inline-block;
  //   padding: 5px 22px;
  //   padding-right: 50px;
  //   font-size: 14px;
  //   color: #454546;
  //   border-radius: 7px;
  //   max-width: 90%;
  //   text-align: left;
  //   position: relative;
  // }
  .chat-outgoing {
    margin-left: auto !important;
    background: #f3f3f3;
    display: inline-block;
    padding: 5px 22px;
    font-size: 14px;
    color: #1b1b1b;
    border-radius: 4px;
    //max-width: 85%;
    width: 900px;
    text-align: left;
    position: relative;
  }
  .chat-incoming {
    margin-right: auto !important;
    background: #ececec;
    display: inline-block;
    padding: 5px 15px;
    font-size: 14px;
    color: #1b1b1b;
    border-radius: 7px;
    max-width: 85%;
    text-align: left;
    position: relative;
  }

  .smallCardIcon {
    font-size: 18px !important;
    color: #66615b !important;
  }

  .send-button {
    width: 32px;
    height: 32px;
    line-height: 32px;
    background-color: #cf0001;
    box-shadow: 0 3px 5px -1px rgba(0, 0, 0, 0.2),
      0 6px 10px 0 rgba(0, 0, 0, 0.14), 0 1px 18px 0 rgba(0, 0, 0, 0.12);
  }

  .trend-button-inactive {
    background: #f5f5f5;
    line-height: 32px;
    width: 32px;
    height: 32px;
    box-shadow: 0 4px 4px -1px rgba(0, 0, 0, 0.2),
      0 1px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 14px 0 rgba(0, 0, 0, 0.12);
    .iconButton {
      color: #66615b;
      font-size: 18px;
    }
  }

  .trend-button-active {
    background-color: #cf0001 !important;
    // margin-top: 5px !important;
    line-height: 32px;
    width: 32px;
    height: 32px;
    // margin-right: 5px !important;
    box-shadow: 0 4px 4px -1px rgba(0, 0, 0, 0.2),
      0 1px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 14px 0 rgba(0, 0, 0, 0.12);
    .iconButton {
      color: white;
      font-size: 18px;
    }
  }
  .chatBox{
    word-wrap: break-word;
    display: block;
    overflow: hidden;
  }
  .context-card {
    max-width: 85%;
    position: absolute;
    margin-left: 15px;
    margin-right: 15px;
    bottom: -5px;
    -webkit-animation: slide-in-top 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)
      both;
    animation: slide-in-top 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
  }

  .context-card-comments {
    text-align: left;
    box-shadow: none !important;
  }
  .slide-in-top {
    -webkit-animation: slide-in-top 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)
      both;
    animation: slide-in-top 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
  }

  /**
     * ----------------------------------------
     * animation slide-in-top
     * ----------------------------------------
     */
  @-webkit-keyframes slide-in-top {
    0% {
      -webkit-transform: translateY(50px);
      transform: translateY(50px);
      opacity: 0;
    }
    100% {
      -webkit-transform: translateY(0);
      transform: translateY(0);
      opacity: 1;
    }
  }
  @keyframes slide-in-top {
    0% {
      -webkit-transform: translateY(50px);
      transform: translateY(50px);
      opacity: 0;
    }
    100% {
      -webkit-transform: translateY(0);
      transform: translateY(0);
      opacity: 1;
    }
  }
}
