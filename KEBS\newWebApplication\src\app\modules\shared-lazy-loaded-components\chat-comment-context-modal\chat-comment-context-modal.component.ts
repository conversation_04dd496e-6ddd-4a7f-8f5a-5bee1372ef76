import { <PERSON>mpo<PERSON>, On<PERSON>ni<PERSON>, Inject, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>roy, AfterViewInit, ViewChild, ElementRef } from '@angular/core';
import * as moment from 'moment';
import { LoginService } from "src/app/services/login/login.service";
import * as _ from "underscore";
import { MatDialogRef, MAT_DIALOG_DATA } from "@angular/material/dialog";
import { UtilityService } from 'src/app/services/utility/utility.service';
import { SharedLazyLoadedComponentsService } from "src/app/modules/shared-lazy-loaded-components/services/shared-lazy-loaded-components.service";
import sweetAlert from 'sweetalert2';

@Component({
  selector: 'app-chat-comment-context-modal',
  templateUrl: './chat-comment-context-modal.component.html',
  styleUrls: ['./chat-comment-context-modal.component.scss']
})

export class ChatCommentContextModalComponent implements OnInit, OnChanges, <PERSON><PERSON>iewIni<PERSON>, <PERSON><PERSON><PERSON><PERSON> {

  currentUser: any;

  modalParams: any;
  comments: any = [];

  @ViewChild('scrollFrame', {}) scrollFrame: ElementRef;

  contextKeys: any = [];

  isContextEnabled: boolean = true;

  mentionConfig: any;

  TABLE_NAME: string = "";
  COMMENT_ID: any = ""
  userSuggestions: any;

  selectedMentions: any = [];

  title: string = "";
  APPLICATION_NAME: string = "";
  is_comments_mail_notif: number =0;
  isReadOnly:boolean = false;

  showOwnComments: boolean = true;
  showOthersComments: boolean = true;

  private scrollContainer: any;

  constructor(private authhelper: LoginService, private utilityService: UtilityService,
    private sharedLazyLoadedComponentsService: SharedLazyLoadedComponentsService,
    public dialogRef: MatDialogRef<ChatCommentContextModalComponent>, @Inject(MAT_DIALOG_DATA) public inData: any) { }

  async ngOnInit() {

    await this.initDetails();

  }

  async ngOnChanges() {

    await this.initDetails();

  }

  async initDetails() {

    this.modalParams = this.inData.modalParams;

    console.log(this.modalParams)

    this.isReadOnly = this.modalParams?.isReadOnly || false;

    this.showOwnComments = this.modalParams?.showOwnComments !== undefined ? this.modalParams?.showOwnComments : true;

    this.showOthersComments = this.modalParams?.showOthersComments !== undefined ? this.modalParams?.showOthersComments : true;
    
    this.currentUser = this.authhelper.getProfile().profile;

    this.userSuggestions = await this.sharedLazyLoadedComponentsService.getEmployeesForSearch();

    // this.mentionConfig = { triggerChar: '@', items: this.userSuggestions, maxItems: 10, labelKey: 'displayName', dropUp: true }
    this.mentionConfig = {
      mentions: [
        {
          items: this.userSuggestions,
          triggerChar: '@',
          maxItems: 10,
          labelKey: 'displayName',
          dropUp: true,
          allowSpace:true
        }

        // {
        //     items: [ "Red", "Yellow", "Green", ... ],
        //     triggerChar: '#'
        // },
      ]
    }
    // this.scrollToBottom();
    setTimeout(() => { this.scrollToBottom(); }, 2000)

    if (this.modalParams.context) {
      this.contextKeys = _.keys(this.modalParams.context);
    }
    // this.mentionConfig = { triggerChar: '@', maxItems: 10, labelKey: 'displayName', items: _.pluck(this.userSuggestions, 'displayName') }

    await this.initCommentData();
    // this.scrollToBottom();

  }
  async ngAfterViewInit() {
    this.scrollContainer = this.scrollFrame.nativeElement;
  }

  async initCommentData() {
    if (this.modalParams.inputData && this.modalParams.inputData.application_id && this.modalParams.inputData.unique_id_1) {

      //ui variables init
      this.title = this.modalParams.inputData.title ? this.modalParams.inputData.title : "KEBS";
      this.APPLICATION_NAME = this.modalParams.inputData.application_name ? this.modalParams.inputData.application_name : "application id : " + this.modalParams.inputData.application_id;


      let commentsResult: any = await this.sharedLazyLoadedComponentsService.getComments(this.modalParams.inputData);
      // console.log(commentsResult)
      if (commentsResult.messType == 'S') {

        this.COMMENT_ID = commentsResult.result.id;
        this.comments = (typeof commentsResult.result.comments == 'string') ? JSON.parse(commentsResult.result.comments) : commentsResult.result.comments;
        this.TABLE_NAME = commentsResult.result.table_name;
        this.is_comments_mail_notif = commentsResult.result.is_comments_mail_notif;
        return;
      }
      else {
        console.log(commentsResult);
        this.utilityService.showMessage("Error while retreiving Comments. Please contact KEBS Team", "Dismiss");
        return;
      }
    }
    else {
      this.utilityService.showMessage("Error while getting application id and input data. Please contact KEBS Team", "Dismiss");
      return;
    }
  }

  getDateFormat(date) {
    return moment(date).format('LLL');
  }

  async enterComment(comment, number) {
    if(document.getElementById('comment')["value"]!=" " && document.getElementById('comment')["value"]!="" && !this.isReadOnly){

    let user = this.authhelper.getProfile().profile;

    console.log("user");
    console.log(user);

    this.comments.push({
      context: this.isContextEnabled ? this.modalParams.context : {},
      sequence_number: number,
      time: moment(new Date()).format('LLL'),
      commentor_oid: user["oid"],
      commentor_name: user["name"],
      comment: document.getElementById('comment')["value"],
    });

    let sendComment = {
      context: this.isContextEnabled ? this.modalParams.context : {},
      sequence_number: number,
      time: new Date(),
      commentor_oid: user["oid"],
      commentor_name: user["name"],
      comment: document.getElementById('comment')["value"],
      url : this.modalParams.url ? this.modalParams.url: ""
    }

    // this.dialogRef.close({ event: "Submit", data: { "chatCommentContextResponse": sendComment } });

    document.getElementById('comment')["value"] = '';

    let writeData = {
      application_id: this.modalParams.inputData.application_id,
      unique_id_1: this.modalParams.inputData.unique_id_1,
      unique_id_2: this.modalParams.inputData.unique_id_2 ? this.modalParams.inputData.unique_id_2 : "",
      table_name: this.TABLE_NAME,
      comment: sendComment
    }

    let writeResult = await this.sharedLazyLoadedComponentsService.writeComment(writeData);
    // console.log(writeResult);

    console.log(this.selectedMentions);

    sendComment['comment'] = "<b><i>" + sendComment['comment'] + "</i></b>" + (this.modalParams.unique_id!="" ? this.modalParams.unique_id : "")

    if (this.selectedMentions.length > 0) {
      let temp = await this.mentionSavePreFormatter(sendComment);
      console.log(temp)
      for (let i = 0; i < temp.length; i++) {
        console.log(temp[i])
        let result = await this.sharedLazyLoadedComponentsService.notifyIndividual(temp[i]);
        console.log(result);
        if (i == temp.length - 1) {
          this.selectedMentions = [];
        }
      }
    }
    this.isContextEnabled = false;
    this.scrollToBottom();
    this.hasEnteredComment = true;
    // var elem = document.getElementById('scrollDiv');
    // elem.scrollTop = elem.scrollHeight;
  }
  }

  hasEnteredComment = false;

  onKeydown(event) {
    // if (event.key === "Enter") {
    //  this.enterComment(undefined,this.comments.length+1);
    // }
  }

  getCommentBoxHeight() {
    return this.modalParams.commentBoxHeight ? this.modalParams.commentBoxHeight : "70vh";
  }

  getCommentScrollHeight() {
    return this.modalParams.commentBoxScrollHeight ? this.modalParams.commentBoxScrollHeight : "93%";
  }

  toggleContext() {
    this.isContextEnabled = !this.isContextEnabled;
  }
  getContextKeys(context) {
    return _.keys(context);
  }

  selectMention(mention) {
    this.selectedMentions.push(mention);
  }

  async mentionSavePreFormatter(sendComment) {

    let meta_data = {
      application_id: this.modalParams.inputData.application_id,
      unique_id_1: this.modalParams.inputData.unique_id_1,
      unique_id_2: this.modalParams.inputData.unique_id_2 ? this.modalParams.inputData.unique_id_2 : "",
      table_name: this.TABLE_NAME,
    }

    this.selectedMentions = _.reject(this.selectedMentions, selectedMention => {
      selectedMention.message = this.currentUser.name + ' has commented ' + sendComment.comment + ' in ' + this.title + ' under ' + this.APPLICATION_NAME;
      selectedMention.context = sendComment;
      selectedMention.link = sendComment['url'] ?  sendComment['url'] :"";
      selectedMention.insert_meta_data = meta_data;
      selectedMention.is_comments_mail_notif = this.is_comments_mail_notif;
      let split = (sendComment.comment).split('@' + selectedMention.displayName);
      return (split.length<2);
    })
    return this.selectedMentions;
  }

  // searchWithTerm(text) {
  //   console.log(this.mentionConfig)
  //   console.log(text);
  //   this.projectService.getUserSuggestionsFromDB(text)
  //   .then((res: any) => {
  //     console.log(res);
  //     this.mentionConfig.items = res ? res.value : [];

  //   })

  //   // this.userSuggestions = suggestions.value;
  //   // return suggestions.value;
  // }

  scrollToBottom() {
    this.scrollContainer.scroll({
      top: this.scrollContainer.scrollHeight,
      left: 0,
      behavior: 'smooth'
    });
    // this.scrollContainer.scroll({
    //   top: (this.scrollContainer.scrollHeight && this.scrollContainer.scrollHeight > 1000) ? this.scrollContainer.scrollHeight : 3000,
    //   left: 0,
    //   behavior: 'smooth'
    // });
  }

  deleteComment = async (index): Promise<void> => {

    if(!this.isReadOnly){

    console.log(index)

    let confirmation = await this.confirmSweetAlert("", "Are you sure you want to delete this comment?");

    if (confirmation.value) {
      this.comments.splice(index, 1);

      let result = await this.sharedLazyLoadedComponentsService.deleteComment(this.COMMENT_ID, this.TABLE_NAME, index);

      this.utilityService.showMessage("The comment has been successfully deleted", "Dismiss");
    }
    }



  }

  closeComments() {

    this.dialogRef.close({ event: "Close", hasEnteredComment: this.hasEnteredComment });

  }

  confirmSweetAlert(title, text) {
    // return sweetAlert.fire({
    //   title: title,
    //   icon: 'warning',
    //   showCancelButton: true,
    //   confirmButtonText: text,
    //   cancelButtonText: 'No, keep it',
    // });
    return sweetAlert.fire({
      customClass: {
        title: "title-class",
        confirmButton: "confirm-button-class",
        cancelButton: "confirm-button-class"
      },
      title: title,
      text: text,
      icon: "warning",
      showConfirmButton: true,
      showCancelButton: true
    })
  }


  ngOnDestroy() {
    this.modalParams.context = {};
    this.contextKeys = [];
  }

}

import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';

import { MatButtonModule } from "@angular/material/button";
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from "@angular/material/form-field";
import { MatInputModule } from "@angular/material/input";
import { SharedComponentsModule } from "../../../app-shared/app-shared-components/components.module";
import { MentionModule } from 'angular-mentions';
import { MatMenuModule } from '@angular/material/menu';

@NgModule({
  declarations: [
    ChatCommentContextModalComponent
  ],
  imports: [
    CommonModule,
    MatButtonModule,
    FormsModule,
    ReactiveFormsModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    SharedComponentsModule,
    MentionModule,
    MatMenuModule
  ],
  providers: [],
})

class ChatCommentContextModalModule { }