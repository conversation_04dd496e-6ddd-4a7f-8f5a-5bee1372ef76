import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { Observable, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class DocumentManagerService {

  constructor(private _http: HttpClient) { }

  private documentSubject = new BehaviorSubject<any>(null); 
  document$ = this.documentSubject.asObservable();

  setDocument(document: any) {
    this.documentSubject.next(document);
  }

  private applicationReferenceIdSubject = new BehaviorSubject<string | null>(null);
  applicationReferenceId$ = this.applicationReferenceIdSubject.asObservable();

  setApplicationReferenceId(id: string) {
    this.applicationReferenceIdSubject.next(id);
  }

  private applicationIdSubject = new BehaviorSubject<string | null>(null);
  applicationIdSubject$ = this.applicationIdSubject.asObservable();

  setApplicationId(id: string) {
    this.applicationIdSubject.next(id);
  }

  private docManagerParams = new BehaviorSubject<any | null>(null);
  docManagerParams$ = this.docManagerParams.asObservable();

  setDocumentData(docParams) {
    this.docManagerParams.next(docParams);
  }

  getApproversBasedOnType(details) {
    return this._http.post('/api/utilService/determineAttachmentApprovers', { details });
  }

  attachmentStatus() {
    return this._http.post('/api/opportunity/attachments/status', { });
  }

  //Submitter
  submit(details) {
    return this._http.post('/api/utilService/submit', { details });
  }

  //Approver
  approveOrReject(details) {
    return this._http.post('/api/utilService/updateApprovalWorkFlow', { details });
  }

  //other reviewers
  otherReviewers(details){
    return this._http.post('/api/utilService/getOtherReviewerStatus', { details });

  }

  retrievecrmUploadedObjects = (destination_bucket, context_id, appRefId, appId, filterConfig?, showDeleted?) => {
    return this._http.post('/api/salesMaster/retrievecrmUploadedObjects', {
      bucket_name: destination_bucket,
      context_id: context_id,
      appRefId: appRefId,
      appId: appId,
      filterConfig: filterConfig,
      showDeleted: showDeleted
    });
  };

  isGoogleViewerBlocked(): Observable<boolean> {
    return new Observable((observer) => {
      fetch('https://docs.google.com/viewer?url=https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf', {
        mode: 'no-cors',
      })
      .then(() => {
        observer.next(false); // Google Docs Viewer is accessible
        observer.complete();
      })
      .catch(() => {
        observer.next(true); // Google Docs Viewer is blocked
        observer.complete();
      });
    });
  }

  isImageFormat(fileFormat): boolean {
    return ['png', 'jpg', 'jpeg'].includes(fileFormat);
  }

  veryShortInterval = 1500;

  shortInterval = 2500;

  mediumInterval = 3500;

  longInterval = 5000;

  veryLongInterval = 6000;
  
}
