import { Component, Injector, OnInit } from '@angular/core';
import {ActivityService} from '../../services/activity.service';
import { Router } from '@angular/router';
import { SubSink } from 'subsink';
import { ToasterService } from 'src/app/services/toaster-service/toaster.service';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core';
import { MAT_MOMENT_DATE_ADAPTER_OPTIONS, MomentDateAdapter } from '@angular/material-moment-adapter';
import { LocalDateFormatPipe } from 'src/app/modules/project-management/shared-lazy-loaded/pipes/local-date-format.pipe';
@Component({
  selector: 'app-activity-log',
  templateUrl: './activity-log.component.html',
  styleUrls: ['./activity-log.component.scss'],
  // providers: [
  //   {
  //     provide: DateAdapter,
  //     useClass: MomentDateAdapter,
  //     deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS]
  //   },
  //   {
  //     provide: MAT_DATE_FORMATS,
  //     useValue: {
  //       parse: {
  //         dateInput: "DD-MMM-YYYY"
  //       },
  //       display: {
  //         dateInput: "DD-MMM-YYYY",
  //         monthYearLabel: "MMM YYYY"
  //       }
  //     }
  //   }
  // ]
  providers:[LocalDateFormatPipe]
})
export class ActivityLogComponent implements OnInit {
  private dialogRef = null;
  private dialogData;

  isDataLoaded:boolean = false;
  requestId:number;
  subs = new SubSink();
  activityData:any;
  eventType:string;
  eventList:any;
  constructor(private _activityService: ActivityService, private _router: Router,private _toaster: ToasterService, private injector: Injector,private localDateFormatPipe: LocalDateFormatPipe) { 
    this.dialogRef = this.injector.get(MatDialogRef,null);
    this.dialogData = this.injector.get(MAT_DIALOG_DATA,null)
  }

  async ngOnInit() {
    this.requestId = this.dialogData?.modalParams ? this.dialogData.modalParams : parseInt(this._router.url.split('/')[5]);
    this.eventType = "all_activities";
    // console.log("Request Id:", this.requestId);
    this.activityData = await this.getActivityLogsBasedOnReqId(this.requestId,this.eventType);
    await this.constructActivityData(this.activityData);
    this.eventList = await this.getAllEventTypes();
    console.log("Activity Log Data",this.activityData);
    console.log("length:",this.activityData.length);
    this.isDataLoaded = true;
  }

  getActivityLogsBasedOnReqId=(requestId,eventType)=>{
    let params = {
      request_id:requestId,
      activity_name:eventType
    }
    return new Promise((resolve, reject) => {
      this.subs.sink = this._activityService
        .getActivityLogsBasedOnReqId(params)
        .subscribe(
          (res: any) => {
            if (res) {
              resolve(res.result);
            }
            else reject(res);
          },
          (err) => {
            if(err.error.user_msg)
              this._toaster.showWarning("Error",err.error.msg)
            console.log(err);
            reject(err);
          }
        );
    });
  }

  getAllEventTypes=()=>{
    return new Promise((resolve, reject) => {
      this.subs.sink = this._activityService
        .getAllEventTypes()
        .subscribe(
          (res: any) => {
            if (res) {
              resolve(res.result);
            }
            else reject(res);
          },
          (err) => {
            if(err.error.user_msg)
              this._toaster.showWarning("Error",err.error.msg)
            console.log(err);
            reject(err);
          }
        );
    });
  }

  async changeEvent(val:any){
    console.log("Event Triggered!!!",val);
    this.isDataLoaded = false;

    this.activityData = await this.getActivityLogsBasedOnReqId(this.requestId,val);
    await this.constructActivityData(this.activityData);
    this.isDataLoaded = true;
  }

  constructActivityData(data){
    if(data && data.length > 0){
      const groupedData = new Map<string, any[]>();
      for(let item of data){
        item.actionDate = this.localDateFormatPipe.transform(item.created_on, "DD-MMM-YYYY");
        // Keep the original 'created_on' as a Date object for sorting purposes
        item.originalDateTime = new Date(item.created_on);

        item.created_on = this.localDateFormatPipe.transform(item.created_on, "hh:mm A");

        const dateKey = item.actionDate;
        if (!groupedData.has(dateKey)) {
          groupedData.set(dateKey, []);
        }
        groupedData.get(dateKey)?.push(item);
      }
     
      // Transform grouped data into desired format
      const sortedResult = Array.from(groupedData.entries())
        .sort(([dateA], [dateB]) => new Date(dateA).getTime() - new Date(dateB).getTime()) // Sort by action date
        .map(([actionDate, items]) => ({
          _id: actionDate,
          data: items.sort((a, b) => a.originalDateTime.getTime() - b.originalDateTime.getTime()), // Sort by original date-time
        }));
      this.activityData = sortedResult;
    }
  }

  ngOnDestroy(): void {
    this.subs.unsubscribe()
  }
}
