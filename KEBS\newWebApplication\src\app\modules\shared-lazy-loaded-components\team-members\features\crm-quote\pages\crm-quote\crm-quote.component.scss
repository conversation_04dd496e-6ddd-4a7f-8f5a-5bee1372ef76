.crm-quote{
  ::ng-deep .mat-tooltip {
    font-family: "Plus Jakarta Sans" !important;//Zifo theme
  }
    overflow: hidden;
    ::ng-deep .infinite-scroll-fixed {
        height:var(--dynamicTableHeight) !important;
    }
    ::ng-deep .udrf-body-styles .infinite-scroll-fixed{
        min-height: var(--dynamicTableHeight) !important;
        max-height: 70vh !important;
        overflow-y: scroll;
        overflow-x: scroll;
        width: var(--dynamicTableWidth) !important;
        max-width: 100vw;
        max-height: var(--dynamicTableHeight) !important;
        height:var(--dynamicTableHeight) !important;
    }
    ::ng-deep mat-sidenav-content {
        overflow-x: hidden;
    }
    ::ng-deep .udrf-body-styles .infinite-scroll-large-fixed{
        min-height: var(--dynamicTableHeight) !important;
        max-height: 70vh !important;
        overflow-y: scroll;
        overflow-x: scroll;
        width: var(--dynamicTableWidth) !important;
        max-width: 100vw;
        max-height: var(--dynamicTableHeight) !important;
        height:var(--dynamicTableHeight) !important;
    }
    ::ng-deep .udrf-body-styles .infinite-scroll-large-fixed-shortened {
        min-height: var(--dynamicTableHeightShortened) !important;
        max-height: 70vh !important;
        overflow-y: scroll;
        overflow-x: scroll;
        // height: 55vh;
        width: var(--dynamicTableWidth) !important;
        max-height: var(--dynamicTableHeight) !important;
        height:var(--dynamicTableHeightShortened) !important;
    }
    ::ng-deep .udrf-body-styles {
      margin-left: 0px ;
      margin-right: 0px;
    }
    ::ng-deep .udrf-header-styles{
        width: 100vw !important;
    }
    ::ng-deep .udrf-header-styles .divAlignItemCenter {
        display: flex;
        align-items: center;
        width: 100vw !important;
    }
    .headerUdrf{
        width: 100vw !important;
    }

    ::ng-deep .udrf-header-card {
        background-color: #fafafa !important;
        width: fit-content !important;
      }
      ::ng-deep .udrf-body-styles .smallSubtleText {
        color: #66615b;
        font-size: 11px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        display: inline;
        height: auto !important;
        line-height: 1.9vh;
    }
      ::ng-deep .udrf-body-styles .smallSubtleText, .udrf-body-styles .smallSubtleTextForColumnWidth {
        color: #66615b;
        font-size: 11px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        display: inline;
        height: auto !important;
    }

    ::ng-deep .udrf-body-styles .childCard {
      background-color: whitesmoke;
      background-clip: border-box;
      box-shadow: none;
      height: 45px;
      margin-top: 1px !important;
      margin-left: 16px !important;
      padding-top: 5px !important;
  }

  ::ng-deep .udrf-body-styles .listcard {
    transition: all 0.25s linear;
    height: 45px !important;
    width: unset;
    padding: 0 1px !important;
    background-color: #fff;
    background-clip: border-box;
    border: 1px solid rgba(0, 0, 0, 0.125);
    border-radius: 0.25rem;
    left: 10px;
    margin-top: 4px !important;
    padding-top: 6px !important;
  }
  .noData{
    position: absolute;
    top: 41%;
    left: 43%;
    display: block;
  }
  .noteImage{
    position: relative;
    height: 168px;
    margin-top: 0%;
    margin-bottom: 10px;
  }
  .description{
    position: relative;
    display: block;
    font-family: "Roboto";
    font-style: normal;
    font-weight: 700;
    font-size: 14px;
    line-height: 16px;
    text-align: center;
    text-transform: capitalize;
    color: #45546E;
    /* margin-top: 203px; */
    
  }
  .field-opp-search{
    display: block;
    width: 14vw;
  }
  .img-align{
    left: 43%;
    position: fixed;
  }
  ::ng-deep .spinner-align circle{
    stroke: #79BA44 !important;
  }
  ::ng-deep .spinner-theme circle {
    stroke: #79BA44 !important;
  }
}

