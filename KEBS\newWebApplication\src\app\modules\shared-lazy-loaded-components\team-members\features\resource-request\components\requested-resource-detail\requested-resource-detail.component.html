<div class="container-fluid pl-0 ml-0 requested-resource">
  <div class="row d-flex" style="overflow: hidden">
    <div class="col-4 pl-4 ml-0 pr-3 mr-0 pt-4 detailCard">
      <div *ngIf="!requestData">
        <div class="row mt-3 mb-3 justify-content-center">
          <mat-spinner class="spinner-theme" diameter="25" matTooltip="Loading ..."> </mat-spinner>
        </div>
      </div>
      <div *ngIf="requestData">
        <div class="row d-flex">
          <span class="status" [ngStyle]="{ background: statusColor }">{{
            requestData.status ? requestData.status : "-"
          }}</span>
          <div class="spacer"></div>
          <div
            *ngIf="
              requestData.status_id != 7 &&
              requestData.status_id != 6 &&
              requestData.status_id != 5 &&
              requestData.status_id != 2
            "
          >
            <button class="cancelButton" (click)="cancelRequest()">
              Cancel Request
            </button>
          </div>
        </div>
        <div class="row pt-2 pb-2 d-flex">
          <div class="row d-block">
            <h1 class="role">
              {{ requestData.position ? requestData.position : "-" }}
            </h1>
            <span class="subText">Req ID: {{ requestData.display_request_id ? requestData.display_request_id : '-' }}</span>
          </div>
          <!-- <div class="spacer"></div>
                    <button class="moreButton" mat-icon-button matTooltip="Know more">
                        <mat-icon class="iconClass">more_horiz</mat-icon>
                    </button> -->
        </div>
        <hr />
        <div style="overflow: scroll;height: 73vh;width: 24vw;" >
        <div class="row pt-2">
          <div class="col-6 pl-0 ml-0 d-block">
            <h4 class="colName">REQUESTED ON</h4>
            <span class="colValue">{{
              requestData.requested_on
                ? (requestData.requested_on | date : "dd-MMM-YYYY")
                : "-"
            }}</span>
          </div>
          <div class="col-6 pl-0 ml-0 d-block">
            <h4 class="colName">EXPECTED CLOSURE DATE</h4>
            <span class="colValue">{{
              requestData.expected_closure_date
                ? requestData.expected_closure_date
                : "-"
            }}</span>
          </div>
        </div>
        <div class="row pt-2">
          <!-- <div class="col-6 pl-0 ml-0 d-block">
                        <h4 class="colName"> TOTAL COST</h4>
                        <span class="colValue">-</span>
                    </div> -->
          <div class="col-6 pl-0 ml-0 d-block">
            <h4 class="colName">UTILIZATION CAPACITY</h4>
            <span class="colValue">{{
              requestData.utilization_capacity
                ? requestData.utilization_capacity + "%"
                : "-"
            }}</span>
          </div>
          <div class="col-6 pl-0 ml-0 d-block">
            <h4 class="colName">BOOKING TYPE</h4>
            <span class="colValue">{{
              requestData.booking_type ? requestData.booking_type : "-"
            }}</span>
          </div>
        </div>
        <div class="row pt-2">
          <!-- <div class="col-6 pl-0 ml-0 d-block">
                        <h4 class="colName"> TOTAL COST</h4>
                        <span class="colValue">-</span>
                    </div> -->
          <div class="col-6 pl-0 ml-0 d-block">
            <h4 class="colName">START DATE</h4>
            <span class="colValue">{{
              requestData.request_start_date
                ? requestData.request_start_date
                : "-"
            }}</span>
          </div>
          <div class="col-6 pl-0 ml-0 d-block">
            <h4 class="colName">END DATE</h4>
            <span class="colValue">{{
              requestData.request_end_date
                ? requestData.request_end_date
                : "-"
            }}</span>
          </div>
        </div>
        <div class="row pt-2">
          <div class="col-6 pl-0 ml-0 d-block">
            <h4 class="colName">SKILL SET</h4>
            <div
              class="row"
              *ngFor="let data of requestData.skillset; let i = index"
            >
              <span class="colValue" *ngIf="skillSetLength != i + 1">{{
                data.Skillset ? data.Skillset : "-"
              }}</span>
              <span class="colValue" *ngIf="skillSetLength === i + 1">{{
                data.Skillset ? data.Skillset : "-"
              }}</span>
            </div>
          </div>
          <div class="col-6 pl-0 ml-0 d-block">
                  <h4 class="colName">WORK LOCATION</h4>
                  <span class="colValue">{{requestData.work_location ? requestData.work_location : '-'}}</span>
            </div>
        </div>
        <div class="row pt-2">
          <div class="col-6 pl-0 ml-0 d-block" *ngIf="fieldConfig?.customer?.is_active_field">
                  <h4 class="colName">{{fieldConfig?.customer?.field_label ? fieldConfig?.customer?.field_label : 'Customer'}}</h4>
                  <span class="colValue"><span class="overflow-text" matTooltip="{{requestData.customer_name ? requestData.customer_name : '-'}}">{{requestData.customer_name ? requestData.customer_name : '-'}}</span></span>
            </div>
            <div class="col-6 pl-0 ml-0 d-block" *ngIf="fieldConfig?.request_type?.is_active_field">
                  <h4 class="colName">{{fieldConfig?.request_type?.field_label ? fieldConfig?.request_type?.field_label : 'Request Context'}}</h4>
                  <span class="colValue"><span class="overflow-text" matTooltip="{{requestData.request_type ? requestData.request_type : '-'}}">{{requestData.request_type ? requestData.request_type : '-'}}</span></span>
            </div>
        </div>
        <div class="row pt-2">
          <div class="col-6 pl-0 ml-0 d-block" *ngIf="fieldConfig?.commercial?.is_active_field">
                  <h4 class="colName">{{fieldConfig?.commercial?.field_label ? fieldConfig?.commercial?.field_label : 'Commercial'}}</h4>
                  <span class="colValue"><span class="overflow-text" matTooltip="{{requestData.allocation_type ? requestData.allocation_type  : '-'}}">{{requestData.allocation_type  ? requestData.allocation_type  : '-'}}</span></span>
            </div>
            <div class="col-6 pl-0 ml-0 d-block" *ngIf="fieldConfig?.commercial_category?.is_active_field && requestData.identity == 1">
                  <h4 class="colName">{{fieldConfig?.commercial_category?.field_label ? fieldConfig?.commercial_category?.field_label : 'Non-Billable Categories'}}</h4>
                  <span class="colValue"><span class="overflow-text" matTooltip="{{requestData.non_billable_category ? requestData.non_billable_category : '-'}}">{{requestData.non_billable_category ? requestData.non_billable_category : '-'}}</span></span>
            </div>
        </div>
        <hr />

        <div class="row pt-2 d-block">
          <div *ngIf="fieldConfig?.job_description?.is_active_field">
            <p class="colName">{{fieldConfig?.job_description?.field_label ? fieldConfig?.job_description?.field_label : 'Job Description'}}</p>
            <div *ngIf="requestData.job_description != '' " class="content1" [innerHtml]="requestData.job_description | safehtml"></div>
            <div *ngIf="requestData.job_description == '' || requestData.job_description == null" class="content1">-</div>
          </div>
          <hr *ngIf="fieldConfig?.job_description?.is_active_field" />
          <div style="margin-bottom: -3px;">
            <p class="colName">
              Acceptance criteria
            </p>
            <div *ngIf="requestData.criteria == null || requestData.criteria == ''" class="content1">-</div>
            <div *ngIf="requestData.criteria != null && requestData.criteria != ''">
              <ul
              style="margin-left: -19px;display: block;margin-bottom: 4px;"
              class="content1"
              *ngFor="let data of requestData?.criteria"
            >
              <li>{{ data.criteria ? data.criteria : "-" }}</li>
            </ul>
            </div>
          </div>

        </div>

        <hr />
        <ng-container *ngIf="tenantSpecificFeature.costDetails">
          <div class="row d-block pt-2 pb-2">
            <h4 class="colName">MANPOWER COST</h4>
            <span *ngIf="hasAccess; else hidedetails" class="colValue"
              >{{
                requestData.cost_details?.manpower.currency_code
                  ? requestData.cost_details?.manpower.currency_code
                  : "-"
              }}
              {{
                requestData.cost_details?.manpower.value
                  ? requestData.cost_details?.manpower.value
                  : "-"
              }}</span
            >
          </div>
          <hr />
          <h4 class="colName">NON MANPOWER COST</h4>
          <div class="row">
            <div
              style="padding-left: 0px"
              class="col-md-6 col-sm-6 col-xs-6 pl-0 pr-0"
              style="width: 330px"
            >
              <div
                *ngFor="
                  let item of requestData.cost_details?.non_manpower.value;
                  let i = index
                "
              >
                <div *ngIf="i < nonManpowerLength">
                  <div style="display: block"></div>
                  <p class="colName" style="display: block">
                    {{ item.description }}
                  </p>
                  <p
                    *ngIf="hasAccess; else hidedetails"
                    class="colValue"
                    style="display: block"
                  >
                    {{ item.currency_code ? item.currency_code : "-" }}
                    {{ item.cost ? item.cost : "-" }}
                  </p>
                </div>
              </div>
            </div>
            <div
              style="padding-left: 0px"
              class="col-md-6 col-sm-6 col-xs-6 pl-0 pr-0"
            >
              <div
                *ngFor="
                  let item of requestData.cost_details?.non_manpower.value;
                  let i = index
                "
              >
                <div *ngIf="i >= nonManpowerLength">
                  <div style="display: block"></div>
                  <p class="colName" style="display: block">
                    {{ item.description }}
                  </p>
                  <p
                    *ngIf="hasAccess; else hidedetails"
                    class="colValue"
                    style="display: block"
                  >
                    {{ item.currency_code ? item.currency_code : "-" }}
                    {{ item.cost ? item.cost : "-" }}
                  </p>
                </div>
              </div>
            </div>
          </div>
          <!-- <div class="row pt-2">
                        <div class="col-6 pl-0 ml-0 d-block">
                            <h4 class="colName">BAHRIN EXIT</h4>
                            <span class="colValue">USD 250</span>
                        </div>
                        <div class="col-6 pl-0 ml-0 d-block">
                            <h4 class="colName">VISA</h4>
                            <span class="colValue">USD 200</span>
                        </div>
                    </div>
                    <div class="row pt-2">
                        <div class="col-6 pl-0 ml-0 d-block">
                            <h4 class="colName">INSURANCE</h4>
                            <span class="colValue">USD 250</span>
                        </div>
                        <div class="col-6 pl-0 ml-0 d-block">
                            <h4 class="colName">TRAVEL</h4>
                            <span class="colValue">USD 250</span>
                        </div>
                    </div>
                    <div class="row pt-2">
                        <div class="col-6 pl-0 ml-0 d-block">
                            <h4 class="colName">ACCOMODATION</h4>
                            <span class="colValue">--</span>
                        </div>
                        <div class="col-6 pl-0 ml-0 d-block">
                            <h4 class="colName">LEASE RENT</h4>
                            <span class="colValue">USD 250</span>
                        </div>
                    </div> -->
          <hr />
          <div class="row d-flex pt-2">
            <div class="col-6 pl-0 ml-0 d-block">
              <h4 class="colName">STANDARD COST</h4>
              <span *ngIf="hasAccess; else hidedetails" class="colValue"
                >{{
                  requestData.cost_details?.standard_cost.currency_code
                    ? requestData.cost_details?.standard_cost.currency_code
                    : "-"
                }}
                {{
                  requestData.cost_details?.standard_cost.value
                    ? requestData.cost_details?.standard_cost.value
                    : "-"
                }}</span
              >
            </div>
            <div class="col-6 pl-0 ml-0 d-block">
              <h4 class="colName">TOTAL COST</h4>
              <span *ngIf="hasAccess; else hidedetails" class="colValue"
                >{{
                  requestData.cost_details?.total_cost.currency_code
                    ? requestData.cost_details?.total_cost.currency_code
                    : "-"
                }}
                {{
                  requestData.cost_details?.total_cost.value
                    ? requestData.cost_details?.total_cost.value
                    : "-"
                }}</span
              >
            </div>
          </div>
        </ng-container>
        <ng-template #hidedetails>
          <p class="colValue">****</p>
        </ng-template>
        
      </div>
        
      </div>
    </div>
    <div
      class="col-8 pl-0 ml-0 pr-0 mr-0 pt-4"
      style="overflow: hidden; height: 100vh"
    >
      <div class="row d-flex justify-content-end">
        <button class="closeButton" (click)="onNoClick()">
          <mat-icon class="iconClose">close</mat-icon>
        </button>
      </div>
      <div *ngIf="!timeLineLoader">
        <div class="row mt-3 mb-3 justify-content-center">
          <mat-spinner class="spinner-theme" diameter="25" matTooltip="Loading ..."> </mat-spinner>
        </div>
      </div>
      <div *ngIf="timeLineLoader">
        <div class="content">
         <div class="row">
          <div class="col-7">
            <div class="row">
              <h4 class="content-head">Allocation Coordinator</h4>
            </div>
            <div class="row pt-1 pb-1 d-flex">
              <app-user-image
                [id]="data.rm_officier_oid"
                imgWidth="26px"
                imgHeight="26px"
                [hasDefaultImg]="true"
              ></app-user-image>
              <div class="row d-block pl-1">
                <span class="content-item">{{
                  data.rm_officier_name ? data.rm_officier_name : "-"
                }}</span>
                <span class="content-subItem">{{
                  data.rm_officier_position ? data.rm_officier_position : "-"
                }}</span>
              </div>
            </div>
          </div>
          <div class="col-3 pl-0 ml-0 pr-0 mr-0">
            <button class="activityButton" (click)="openActivityLog()">
              Activities Log
            </button>
          </div>
         </div>
          <hr />
        </div>
        <div class="timeline">
          <div class="Ocircle">
            <div class="circle" style="background: #515965; border: none">
              <mat-icon class="circle-item">done</mat-icon>
            </div>
          </div>
          <div
            class="row"
            style="
              border-left: 1.9px solid #dadce2;
              padding-left: 18px;
              display: block;
            "
          >
            <!-- <div class="circle" style="background: #515965; border: none;"><mat-icon class="circle-item">done</mat-icon></div> -->
            <h4 class="title">Requested On</h4>
            <h6 class="sub-title" style="color: #45546e; margin-bottom: 0px">
              {{
                data.requested_on
                  ? (data.requested_on | date : "dd-MMM-YYYY")
                  : "-"
              }}
            </h6>
          </div>
          <div class="Ocircle">
            <div
              class="circle"
              style="border: 2px solid #515965; background: white"
            ></div>
          </div>
          <div>
          <form [formGroup]="form">
            <div *ngFor="let data of data.action_items; let i = index">
              <div class="Ocircle">
                <div
                  *ngIf="data.workflow_status === 'A'"
                  class="circle"
                  style="background: #515965; border: none"
                >
                  <mat-icon class="circle-item">done</mat-icon>
                </div>
                <div
                  *ngIf="data.workflow_status === 'R'"
                  class="circle"
                  style="background: #ff3a46; border: none"
                >
                  <mat-icon class="circle-item">close</mat-icon>
                </div>
                <div
                  class="circle"
                  *ngIf="data.workflow_status === null"
                  style="border: 2px solid #515965; background: white"
                ></div>
                <div
                  class="circle"
                  *ngIf="data.workflow_status === 'S'"
                  style="border: 2px solid #ffbd3d; background: white"
                ></div>
              </div>
              <div
                class="OuterCircle"
                [ngStyle]="{
                  'border-left': len != i + 1 ? '1.9px solid #DADCE2' : 'none'
                }"
              >
                <div class="row" style="display: flex">
                  <h4 class="title">
                    {{ data.item_name ? data.item_name : "-" }}
                  </h4>
                  <!-- <h4 class="title" *ngIf="data.allocated_count > 1"> ({{data.allocated_count}})</h4> -->
                  <mat-icon
                    class="arrowIcon"
                    (click)="Collapse(i)"
                    *ngIf="!data.is_collapsed && data.sub_content != null"
                    >keyboard_arrow_up</mat-icon
                  >
                  <mat-icon
                    class="arrowIcon"
                    (click)="Expand(i)"
                    *ngIf="data.is_collapsed && data.sub_content != null"
                    >keyboard_arrow_down</mat-icon
                  >
                </div>
                <h6
                  class="sub-title"
                  [ngStyle]="{ color: data.sub_title_color }"
                  *ngIf="requestData.status_id != 5"
                >
                  {{ data.sub_title ? data.sub_title : "-" }}
                </h6>
                <h6
                  class="sub-title"
                  [ngStyle]="{'color':(data.sub_title == 'Pending') ? statusColor : data.sub_title_color }"
                  *ngIf="requestData.status_id == 5"
                >
                  {{ (data.sub_title == 'Pending') ? 'Cancelled' : data.sub_title}}
                </h6>
                <div
                  class="container pl-0"
                  *ngIf="!data.is_collapsed && data.sub_content != null"
                >
                  <div class="row">
                    <span
                      class="status_badge"
                      *ngIf="data.status_badge != null"
                      >{{ data.status_badge ? data.status_badge : "" }}</span
                    >
                  </div>
                  <div class="row pt-1 pb-1 d-flex">
                    <app-user-image 
              [id]="data.sub_content.resource_oid"
              imgWidth="34px"
              imgHeight="34px"
              [hasDefaultImg]="true"
             
            ></app-user-image>
                    <!-- <img
                      src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTBQLff_3MpOyOgwMjkcxtUv1DAunc29uszUqioL-Lo1w&s"
                      class="res-img"
                    /> -->
                    <div class="row d-block pl-1">
                      <span class="res-item">{{
                        data.sub_content.resource_name
                          ? data.sub_content.resource_name
                          : "-"
                      }}</span>
                      <span class="res-subItem">{{
                        data.sub_content.associate_id
                          ? data.sub_content.associate_id
                          : "-"
                      }}</span>
                      <button  *ngIf="isprojectButtonEnabled" class="activityButton" (click)="routeProjectDetails(data.sub_content.associate_id)">
                        Project Details
                      </button>
                    </div>
                  </div>
                  <hr />
                  <div class="row pb-2 pl-0">
                    <!-- <div class="col-3">
                                            <h4 class="colName">DESIGNATION</h4>
                                            <h4 class="colValue">{{data.sub_content.designation}}</h4>
                                        </div> -->
                    <div class="col-2 pl-0 pr-0" *ngIf="rmConfig && !(rmConfig['is_day_wise_availability_allowed'] ? rmConfig['is_day_wise_availability_allowed'] : false)">
                      <div class="row">
                        <h4 class="colName">STATUS</h4>
                      </div>
                      <div class="row">
                        <h4 class="colValue">
                          {{
                            data.sub_content.status
                              ? data.sub_content.status
                              : "-"
                          }}
                        </h4>
                      </div>
                    </div>
                    <!-- <div class="col-3">
                                            <h4 class="colName">CURRENT PROJECT</h4>
                                            <h4 class="colValue">{{data.sub_content.current_project}}</h4>
                                        </div> -->
                    <!-- <div class="col-4 pl-0">
                                            <h4 class="colName">YEARS OF EXPERIENCE</h4>
                                            <h4 class="colValue">{{data.sub_content.years_of_exp ? data.sub_content.years_of_exp : '-'}}</h4>
                                        </div> -->
                    <div class="pl-0 pr-0"
                    [ngClass]="{
                      'col-2': rmConfig && (rmConfig['is_day_wise_availability_allowed'] ? rmConfig['is_day_wise_availability_allowed'] : false),
                      'col-3': !(rmConfig && (rmConfig['is_day_wise_availability_allowed'] ? rmConfig['is_day_wise_availability_allowed'] : false))
                    }"
                    >
                      <div class="row">
                        <h4 class="colName">Employment Type</h4>
                      </div>
                      <div class="row">
                        <h4 class="colValue">
                          {{
                            data.sub_content.employment_type
                              ? data.sub_content.employment_type
                              : "-"
                          }}
                        </h4>
                      </div>
                    </div>
                    <div class="col-3 pl-0 pr-0">
                      <div class="row">
                        <h4 class="colName">Utilization Capacity</h4>
                      </div>
                      <div class="row">
                        <h4 class="colValue">
                          {{
                            data.sub_content.utilization_capacity
                              ? data.sub_content.utilization_capacity + "%"
                              : "-"
                          }}
                        </h4>
                      </div>
                    </div>
                    <div class="col-4 pl-0 pr-0">
                      <div class="row">
                        <h4 class="colName">Scheduled Interview</h4>
                      </div>
                      <div class="row">
                        <h4
                          class="colValue"
                          *ngIf="data.sub_content.is_interview_scheduled!= 0 && data.sub_content.is_interview_scheduled!= 1"
                        >
                          -
                        </h4>
                        <h4
                          class="colValue"
                          *ngIf="
                            
                            data.sub_content.is_interview_scheduled == 0
                          "
                        >
                          NO
                        </h4>
                        <h4
                          class="colValue"
                          *ngIf="
                          
                            data.sub_content.is_interview_scheduled == 1
                          "
                        >
                          YES
                        </h4>
                      </div>
                    </div>
                  </div>
                  <div class="row pt-2 pb-2">
                    <div class="col-2 pl-0 pr-0">
                      <span class="itemHead">Skill &nbsp; Score</span>
                      <span
                        class="itemSub"
                        (click)="skillPopup(data.sub_content)"
                        style="cursor: pointer; display: flex;padding-top: 3px;"
                        *ngIf="data.sub_content.skill_score != null"
                        ><p>
                          {{
                            data.sub_content.skill_score + "%"
                          }}
                        </p>
                        <mat-icon class="infoOverlay material-symbols-outlined"
                          >info</mat-icon
                        ></span
                      >
                      <span
                        class="itemSub"
                        *ngIf="data.sub_content.skill_score === null"
                        >{{"NA"}}</span
                      >
                    </div>
                    <div class="col-3 pl-0 pr-0">
                      <span class="itemHead">Booking Type</span>
                      <span class="itemSub"
                        >{{
                          data.sub_content.booking_type
                            ? data.sub_content.booking_type
                            : "-"
                        }}
                      </span>
                    </div>
                    <div class="col-5 pl-0 pr-0">
                      <span class="itemHead">AVAILABLE &nbsp; FROM</span>
                      <span class="itemSub"
                        >{{
                          data.sub_content.allocated_start_date
                            ? data.sub_content.allocated_start_date
                            : "-"
                        }}
                        &nbsp; To &nbsp;
                        {{
                          data.sub_content.allocated_end_date
                            ? data.sub_content.allocated_end_date
                            : "-"
                        }}</span
                      >
                    </div>
                    
                  </div>
                  <div class="row pt-2 pb-2" *ngIf="fieldConfig?.work_premisis?.is_allocate_active || fieldConfig?.work_city?.is_allocate_active">
                    <div class="col-2 pl-0 pr-0">
                      <span class="itemHead">{{fieldConfig?.work_premisis?.field_label ? fieldConfig?.work_premisis?.field_label : 'Work Premisis'}}</span>
                      <span class="itemSub"
                        >{{
                          data.sub_content.work_premisis
                            ? data.sub_content.work_premisis
                            : "-"
                        }}
                      </span>
                    </div>
                    <div class="col-3 pl-0 pr-0">
                      <span class="itemHead">{{fieldConfig?.work_city?.field_label ? fieldConfig?.work_city?.field_label : 'Work City'}}</span>
                      <span class="itemSub"
                        >{{
                          data.sub_content.work_city
                            ? data.sub_content.work_city
                            : "-"
                        }}
                      </span>
                    </div>
                    
                  </div>
                  <div class="row pt-1 pb-1 d-block">
                    <span class="itemHead" *ngIf="data.sub_content?.criteria?.length > 0">Acceptance Criteria</span>
                    <div
                      class="row pt-1"
                      *ngFor="let criteria of data.sub_content.criteria"
                    >
                      <div
                        *ngIf="criteria.has_satisfied"
                        class="circle-content"
                        style="background: #52c41a; border: none"
                      >
                        <mat-icon class="circle-item-content">done</mat-icon>
                      </div>
                      <div
                        *ngIf="!criteria.has_satisfied"
                        class="circle-content"
                        style="background: #ff3a46; border: none"
                      >
                        <mat-icon class="circle-item-content">close</mat-icon>
                      </div>
                      <h4 class="colCriteria">{{ criteria.criteria }}</h4>
                    </div>
                  </div>
                  <div class="soft-booking-count" *ngIf="data.has_action_btn && this.rmConfig['soft_booking_count_visible'] && data.sub_content?.soft_booking_count > 0">
                    <span class="count-text">{{data.sub_content?.soft_booking_count}}</span>
                    <span>More Soft Booking is Available.</span>
                  </div>
                  <div class="notes">
                    <span class="noteHead">Notes</span>
                    <span class="noteContent" style="display: block">{{
                      data.sub_content.notes ? data.sub_content.notes : ""
                    }}</span>
                  </div>

                  <div class="row d-flex mb-2 align-class" *ngIf="(data.sub_content.booking_type_id && data.sub_content.booking_type_id == 1) ">
                    <div class="col-3 p-0 m-0" *ngIf="reportOptionEnable">
                      <span class="report_text header">Project Head<span *ngIf="data.has_action_btn && filteredReportsToList[i] && filteredReportsToList[i].length == 0" class="required-star"> &nbsp;*</span></span>
                      <mat-checkbox *ngIf="is_head && data.has_action_btn;else projectHead"
                        [checked]="data.sub_content.is_head === 1"
                        (change)="isHeadChange($event.checked ? 1 : 0, i)"
                        class="is-head-name"
                        [disabled]="!data.has_action_btn"
                      >
                        Is Head
                      </mat-checkbox>
                      <ng-template #projectHead>
                        <span class="report_text ellipsis-class" matTooltip="{{projectHeadName ? projectHeadName : '-'}}">{{projectHeadName ? projectHeadName : '-'}}</span>
                      </ng-template>

                    </div>
                    <div class="col-3 p-0 m-0" *ngIf="data.sub_content.is_head != 1 && reportOptionEnable">
                      <span class="report_text header ">Reports To<span *ngIf="data.has_action_btn" class="required-star"> &nbsp;*</span></span>
                      <mat-form-field appearance="outline" class="search-class" *ngIf="data.has_action_btn">
                        <input
                          matInput
                          [matAutocomplete]="auto"
                          placeholder="Select an Option"
                          [(ngModel)]="selectedNames[i]"
                          [ngModelOptions]="{ standalone: true }"
                          (input)="filterOptions(i)"
                          [disabled]="!data.has_action_btn"
                        />
                        <mat-autocomplete #auto="matAutocomplete" [displayWith]="displaySelectedName" (optionSelected)="onChange($event.option, i)" (opened)="disableScroll()" 
                        (closed)="enableScroll()">
                          <mat-option *ngFor="let option of filteredReportsToList[i]" [value]="option.id" matTooltip="{{(option.name) ? option.name : '-'}}" [style.background-color]="option.id === selectedNames[i] ? '#d3d3d3' : ''">
                            {{ option.name }}
                          </mat-option>
                        </mat-autocomplete>
                      </mat-form-field>
                      <span class="report_text ellipsis-class"  *ngIf="!data.has_action_btn" matTooltip="{{ getSelectionName(selectedNames[i]) }}">{{ getSelectionName(selectedNames[i]) }}</span>
                      <!-- <mat-form-field appearance="outline" class="search-class">
                        <mat-label>Select an Option</mat-label>
                        <mat-select (selectionChange)="onChange($event.value, i)">
                          
                          <mat-option>
                            <input matInput placeholder="Type to filter..." 
                                   [(ngModel)]="filterInputs[i]"
                                   (input)="filterOptions(i)" />
                          </mat-option>
                          
                          <mat-option *ngFor="let option of filteredReportsToList[i]" 
                                      [value]="option.id">
                            {{ option.name }}
                          </mat-option>
                        </mat-select>
                      </mat-form-field> -->
                    </div>
                    <div class="col-3 p-0 m-0" *ngIf="travelConfig?.is_active">
                      <span class="report_text header ">{{ travelConfig?.field_label ? travelConfig?.field_label : 'Travel Type'}}<span class="required-star" *ngIf="travelConfig?.is_mandatory"> &nbsp;*</span></span>
                      <div [formArrayName]="'travelTypes'" *ngIf="data.has_action_btn" >
                        <app-input-search
                          style="width: 69%;"
                          class="inputSearch"
                          placeholder="Select {{travelConfig?.field_label ? travelConfig?.field_label : 'Travel Type'}}"
                          [list]="travelTypeList"
                          [formControlName]="i"
                          [disableNone]="true"
                          [hideMatLabel]="true"
                          [disabled]="!data.has_action_btn"
                        >
                        </app-input-search>
                      </div>
                      <span class="report_text ellipsis-class"  *ngIf="!data.has_action_btn" matTooltip="{{ getTravelType( data?.sub_content?.travel_type) }}">{{ getTravelType( data?.sub_content?.travel_type) }}</span>
                    </div>
                    <div class="col-3 p-0 m-0" *ngIf="shiftConfig?.is_active">
                      <span class="report_text header ">{{ shiftConfig?.field_label ? shiftConfig?.field_label : 'Shift'}}<span class="required-star" *ngIf="shiftConfig?.is_mandatory"> &nbsp;*</span></span>
                      <div [formArrayName]="'shiftTypes'" *ngIf="data.has_action_btn" >
                        <app-input-search
                          style="width: 69%;"
                          class="inputSearch"
                          placeholder="Select {{shiftConfig?.field_label ? shiftConfig?.field_label : 'Shift'}}"
                          [list]="shiftMasterList"
                          [formControlName]="i"
                          [disableNone]="true"
                          [hideMatLabel]="true"
                          [disabled]="!data.has_action_btn"
                        >
                        </app-input-search>
                      </div>
                      <span class="report_text ellipsis-class"  *ngIf="!data.has_action_btn" matTooltip="{{ getShiftType( data?.sub_content?.shift) }}">{{ getShiftType( data?.sub_content?.shift) }}</span>
                    </div>
                  </div>

                  <div class="row d-flex pl-0" *ngIf="data.has_action_btn">
                    <button
                      class="confirmButton"
                      style="
                        color: #45546e;
                        background: #ffffff;
                        border: 1px solid #45546e;
                      "
                      (click)="
                        validateResource(
                          data.workflow_id,
                          'R',
                          data.sub_content.associate_id,
                          data.sub_content,
                          i
                        )
                      "
                    >
                      Reject
                    </button>
                    <button
                      class="confirmButton"
                      style="color: #ffffff; border: none"
                      (click)="
                        validateResource(
                          data.workflow_id,
                          'A',
                          data.sub_content.associate_id,
                          data.sub_content,
                          i
                        )
                      "
                    >
                      Accept
                    </button>
                  </div>
                  <div class="row d-flex pl-0" *ngIf="!data.has_action_btn">
                    <div class="row">
                      <span
                        class="status_badge"
                        *ngIf="data.workflow_status === 'A'"
                        style="background: #52c41a"
                        >Accepted</span
                      >
                      <span
                        class="status_badge"
                        *ngIf="data.workflow_status === 'R'"
                        style="background: red"
                        >Rejected</span
                      >
                      <div class="col-12 d-flex align-items-center p-0 m-0" *ngIf="data.workflow_status === 'S' && requestData.status_id == 6 && pendingWithVisible">
                        <span class="report_text header">Pending With:</span>
                        <app-people-display [people_list]="data?.workflow_items"></app-people-display>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

