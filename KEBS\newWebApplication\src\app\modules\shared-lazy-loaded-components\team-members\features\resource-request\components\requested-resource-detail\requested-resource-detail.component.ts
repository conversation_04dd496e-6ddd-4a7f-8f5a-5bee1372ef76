import { Component, Inject, OnInit, ViewChild } from '@angular/core';
import {
  MatDialog,
  MatDialogRef,
  MAT_DIALOG_DATA,
} from '@angular/material/dialog';
import { VoluntarySummaryComponent } from 'src/app/modules/exit-dashboard/components/voluntary-summary/voluntary-summary.component';
import { TmService } from '../../../../services/tm.service';
import { SubSink } from 'subsink';
import { RejectDialogComponent } from '../reject-dialog/reject-dialog.component';
import sweetAlert from 'sweetalert2';
import { ToasterService } from 'src/app/services/toaster-service/toaster.service';
import { accessObjectMaster } from '../../../../access-object.auth';
import * as _ from 'underscore';
import Swal from 'sweetalert2';
import { SkillScoreComponent } from '../../components/skill-score/skill-score.component';
import moment from 'moment';
import { RmService } from 'src/app/modules/resource-management/services/rm.service';
import { PmInternalStakeholderService } from '../../../../../../project-management/features/pm-details/features/pm-project-team/features/project-internal-stakeholders/services/pm-internal-stakeholder.service';
import { Router } from '@angular/router';
import { FormArray, FormBuilder, FormGroup } from '@angular/forms';
@Component({
  selector: 'app-requested-resource-detail',
  templateUrl: './requested-resource-detail.component.html',
  styleUrls: ['./requested-resource-detail.component.scss'],
})
export class RequestedResourceDetailComponent implements OnInit {
  data: any;
  hasAccess = false;
  timeLineLoader: boolean = false;
  len: number;
  reqId: number;
  nonManpowerLength: number;
  graph:any;
  summaryData:any;
  filterValue:any;
  skillSetLength: number;
  requestData: any = null;
  tenantSpecificFeature = {
    costDetails: false,
  };
  statusColor:string;
  fieldConfig = {};
  requestProjectDetails: Object = null;
  allocated_hours: number = 0;
  rmConfig: any = null;
  isprojectButtonEnabled:boolean=false;
  reportOptionEnable: boolean = true;
  
  is_head: boolean = true;
  reports_to_list: any = [];
  
  filteredReportsToList: any[] = []; // Holds filtered list for each item
  selectedNames: string[] = [];
  projectHeadName: string;
  travelTypeList: any = []
  travelConfig: any = {};
  shiftConfig: any = {};
  shiftMasterList: any = []
  form: FormGroup;
  travel_value: any = null;
  shift_value: any = null;
  pendingWithVisible: boolean = false;
  private subs = new SubSink();
  constructor(
    private _tmService: TmService,
    public dialog: MatDialog,
    private _toaster: ToasterService,
    public dialogRef: MatDialogRef<RequestedResourceDetailComponent>,
    @Inject(MAT_DIALOG_DATA) public inData: any,
    private _rmService:RmService,
    private PmInternalStakeholderService: PmInternalStakeholderService,
    private _router: Router,
    private fb: FormBuilder
  ) {}

  retrieveRmConfig() {
    return new Promise((resolve,reject) => {
      this.subs.sink = this._tmService.retrieveRmConfig()
      .subscribe((res: any) => {
        if(!res.err) {
          resolve(res.data)
        }
      },(err) => {
        console.log(err)
      })
    })
  }

  async ngOnInit() {
    this.hasAccess = this._tmService.checkAccessForObject(
      accessObjectMaster.costDetailsVisibility
    );
    console.log('Modal Data:', this.inData);
    await this.handleTenantSpecificFeatures()
    this.reqId = this.inData.modalParams.request_id;
    this.requestProjectDetails = await this.getRequestProjectDetails();
    this.requestData = await this.getResourceRequest();
    console.log("Data",this.requestData)
    _.find(this.inData.status,x=>{

      // console.log("FInd FUnction:",x.status)
      if(x.status === this.requestData.status){
        this.statusColor = x.color
        console.log(this.statusColor)
      }
    });
    this.nonManpowerLength = Math.ceil(
      this.requestData.cost_details?.non_manpower.value.length / 2
    );
    this.skillSetLength = this.requestData.skillset.length;
    this.data = await this.getAllocatedResourceActionList();

    this.data.action_items.forEach((item, index) => {
      this.filteredReportsToList[index] = this.reports_to_list;
      const selectedOption = this.reports_to_list.find(option => option?.id === item?.sub_content?.reports_to);
      this.selectedNames[index] = (selectedOption && selectedOption.id) ? selectedOption.id : '';
    });

    if (this.travelConfig && this.travelConfig['is_active']) {
      if (!this.form) {
          this.form = this.fb.group({}); // Initialize the form if not already done
      }
      this.form.addControl(
          'travelTypes',
          this.fb.array(
              this.data.action_items.map(() => this.fb.control('')) // Create controls for each data item
          )
      );
  }
  
  if (this.shiftConfig && this.shiftConfig['is_active']) {
      if (!this.form) {
          this.form = this.fb.group({}); // Initialize the form if not already done
      }
      this.form.addControl(
          'shiftTypes',
          this.fb.array(
              this.data.action_items.map(() => this.fb.control('')) // Create controls for each data item
          )
      );
  }

    this.timeLineLoader = true;
    this.len = this.data.action_items.length;
    // console.log(this.data.action_items);
    this.timeLineLoader = true;

    console.log(this.requestData.job_description)
  }

   async handleTenantSpecificFeatures() {
    this.rmConfig = await this.retrieveRmConfig()
    let fieldFormConfig:any = await this.retrieveFieldConfig('create_request')
    if(fieldFormConfig.length > 0) {
      fieldFormConfig.forEach((val,i) => {
        this.fieldConfig[val.field_key] = val
      })
    }
    this.reportOptionEnable = this.rmConfig['reporting_enable'] ? this.rmConfig['reporting_enable'] : false
    this.pendingWithVisible = this.rmConfig['approvers_visible'] ? this.rmConfig['approvers_visible'] : false
    this.isprojectButtonEnabled=this.rmConfig['project_button_visible'] ? this.rmConfig['project_button_visible'] : false
    if(this.reportOptionEnable){
      let currentDate = moment().format();
      let itemId = (this._router.url.split('/')[2] == 'project-management') ? parseInt(this._router.url.split('/')[5]) : parseInt(this._router.url.split('/')[7])
      await this.PmInternalStakeholderService.getreportsToMaster(
        itemId,
        currentDate
      ).then((res) => {
        
        this.reports_to_list = res['data'];
      });
  
      await this.PmInternalStakeholderService.getHeadForISA(
        itemId
      ).then((res) => {
        
        if (res['data'].length > 0) {
          this.is_head = false;
          let data = res["data"][0]
          // console.log('this.reports_to_list initial:',this.reports_to_list)
          // const head_name = this.reports_to_list.find(option => option.associate_id === data['aid']);
          const head_name = data['head_name'] ? data['head_name'] : null;
          this.projectHeadName = head_name ? head_name : null;
        }
      });
    }
    this.travelConfig = (this.rmConfig && this.rmConfig['travel_type_config']) ? this.rmConfig['travel_type_config'] : null;
    if(this.travelConfig && this.travelConfig['is_active']){
      this.travelTypeList = await this.getTravelTypeList();
    }
    this.shiftConfig = (this.rmConfig && this.rmConfig['shift_config']) ? this.rmConfig['shift_config'] : null;
    if(this.shiftConfig && this.shiftConfig['is_active']){
      this.shiftMasterList = await this.getShiftMasterList();
    }
    this.tenantSpecificFeature.costDetails = this.rmConfig['cost_details']['is_active']
  }

  onNoClick(){
    this.dialogRef.close(this.requestData);
  }
  Collapse = (index: number) => {
    for (var i = 0; i < this.data.action_items.length; i++) {
      if (i == index) {
        this.data.action_items[i].is_collapsed = true;
      }
    }
  };
  Expand = (index: number) => {
    for (var i = 0; i < this.data.action_items.length; i++) {
      if (i == index) {
        this.data.action_items[i].is_collapsed = false;
      }
    }
  };

  getResourceRequest = () => {
    let params = {
      request_id: this.reqId,
    };
    return new Promise((resolve, reject) => {
      this.subs.sink = this._tmService
        .getResourceRequestDetail(params)
        .subscribe(
          (res: any) => {
            if (res.err == false)  {
              res.data = this.utcDateFormat(res.data)
              resolve(res.data);
            }
            else reject(res);
          },
          (err) => {
            console.log(err);
            reject(err);
          }
        );
    });
  };

  utcDateFormat(data) {
    data['request_start_date'] = moment(data['request_start_date']).utc().format('DD-MMM-YYYY')
    data['request_end_date'] = moment(data['request_end_date']).utc().format('DD-MMM-YYYY')
    data['expected_closure_date'] = moment(data['expected_closure_date']).utc().format('DD-MMM-YYYY')
    return data
  }

  getAllocatedResourceActionList = () => {
    let request_id = this.reqId;
    return new Promise((resolve, reject) => {
      this.subs.sink = this._tmService
        .getAllocatedResourceActionList(request_id)
        .subscribe(
          (res: any) => {
            if (res.err == false) {
              res['data'] = this.utcDateFormatForActionList(res['data'])
              resolve(res.data);
            }
            else reject(res);
          },
          (err) => {
            console.log(err);
            reject(err);
          }
        );
    });
  };

  utcDateFormatForActionList(data) {
    for(let item of data['action_items']) {
      if(item['sub_content'] && item['sub_content']['allocated_start_date']) {
        item['sub_content']['allocated_start_date'] = 
        moment(item['sub_content']['allocated_start_date']).utc().format('DD-MMM-YYYY')
      }
      if(item['sub_content'] && item['sub_content']['allocated_end_date']) {
        item['sub_content']['allocated_end_date'] = 
          moment(item['sub_content']['allocated_end_date']).utc().format('DD-MMM-YYYY')
      } 
    }
    return data
  }

  async validateResource(id: number, val: string, associate_id: number,content:any,index) {
    if (val === 'A') {
      this.timeLineLoader = false;
      
      if(this.reportOptionEnable && content && content.booking_type_id && content.booking_type_id == 1){
        console.log('Content:',content)
        if(content.is_head == 0){
          if((content.reports_to == null || content.reports_to == '' || content.reports_to == undefined)){
            this._toaster.showWarning('Warning', 'Reporting is mandatory for this Action.');
            this.timeLineLoader = true;
            await this.patchReportingValue()
            this.len = this.data.action_items.length;
            return;
          }
        }
      }
      if(this.travelConfig && this.travelConfig['is_active']){
        const selectedValues = this.form?.value?.travelTypes; // Array of selected travel types
        console.log(this.travel_value)
        this.travel_value = this.getValueAtIndex(index);
        console.log(this.travel_value)
      }
      

      if(this.travelConfig && this.travelConfig['is_active'] && this.travelConfig['is_mandatory']  && content && content.booking_type_id && content.booking_type_id == 1){
        if((this.travel_value == null || this.travel_value == '' || this.travel_value == undefined)){
          this._toaster.showWarning('Warning', `${(this.travelConfig['field_label'] ? this.travelConfig['field_label'] : 'Travel Type')} is Mandatory`);
          this.timeLineLoader = true;
          this.len = this.data.action_items.length;
          return;
        }
      }

      if(this.shiftConfig && this.shiftConfig['is_active']){
        const selectedValues = this.form?.value?.shiftTypes; // Array of selected Shift
        console.log(this.shift_value)
        this.shift_value = this.getValueAtIndexShift(index);
        console.log(this.shift_value)
      }
      

      if(this.shiftConfig && this.shiftConfig['is_active'] && this.shiftConfig['is_mandatory']  && content && content.booking_type_id && content.booking_type_id == 1){
        if((this.shift_value == null || this.shift_value == '' || this.shift_value == undefined)){
          this._toaster.showWarning('Warning', `${(this.shiftConfig['field_label'] ? this.shiftConfig['field_label'] : 'Shift')} is Mandatory`);
          this.timeLineLoader = true;
          this.len = this.data.action_items.length;
          return;
        }
      }


      if (!this.requestProjectDetails) {
        this._toaster.showWarning('Warning', 'Project Details not found');
        this.timeLineLoader = true;
        await this.patchReportingValue()
        this.len = this.data.action_items.length;
        return;
      }

    // Safely accessing properties using optional chaining (?.)
    const billable = this.requestProjectDetails['commercial'] ? true : false;
    const qtc_project = this.requestProjectDetails['with_opportunity'] ? true : false;
    const blanket_po = this.requestProjectDetails['blanket_po'] ? true : false;
    const internal_project = this.requestProjectDetails['is_internal'] ? true : false;
    let booking_type = content.booking_type_id ? content.booking_type_id : null
    
    const allow_check = billable && qtc_project && !blanket_po && !internal_project;

    const api_response = await this.getAllocatedHours(content);
    let total_allocated_hours =  api_response?.['total_allocated_hours'] ? api_response?.['total_allocated_hours'] : 0
    this.allocated_hours = await this.calculatePlannedHours(total_allocated_hours,content?.utilization_capacity)
    console.log('Planned Allocated Hours:',this.allocated_hours)

    // Stage 1: Day-Wise Allocation Check
    const day_wise_check = this.rmConfig['is_day_wise_availability_allowed'] || false;

    if (day_wise_check  && booking_type == 1) {
      const allow_allocation = await this.dayWiseAllocationCheck(content);
      
      if (!allow_allocation) {
        this._toaster.showWarning('Warning', 'Employee is unavailable for allocation during the selected duration.');
        this.timeLineLoader = true;
        await this.patchReportingValue()
        this.len = this.data.action_items.length;
        return;
      }
    }

    // Stage 2: Checking project types & Billable 
    if (!allow_check) {
      await this.processResource(id,val,associate_id,content);
      this.timeLineLoader = true;
      await this.patchReportingValue()
      this.len = this.data.action_items.length;
      return;
    }

    //Stage 3:  Proceed with Quote Position Check
    const disable_position_check = this.rmConfig['disable_position_check'] || false;

    if (!disable_position_check && booking_type == 1) {
      await this.handleAllocationChecks(id,val,associate_id,content);
    } else {
      await this.processResource(id,val,associate_id,content);
    }
      this.timeLineLoader = true;
      await this.patchReportingValue()
      this.len = this.data.action_items.length;
    } else if (val === 'R') {
      const dialogRef = this.dialog.open(RejectDialogComponent, {
        width: '440px',
        height: '260px',
        // data: { modalParams: data },
      });
      dialogRef.afterClosed().subscribe(async (result) => {
        // console.log("Inside!!!!!!!!!!!!!!!!!!!!!");
        console.log('Dialog Data:', result);
        if (result) {
          this.timeLineLoader = false;
          await this.initiateApprovalAction(
            id,
            val,
            result.reason,
            associate_id,
            content.is_head,
            content.reports_to
          );
          this.data = await this.getAllocatedResourceActionList();
          this.requestData = await this.getResourceRequest();
    _.find(this.inData.status,x=>{

      // console.log("FInd FUnction:",x.status)
      if(x.status === this.requestData.status){
        this.statusColor = x.color
        console.log(this.statusColor)
      }
    });
          this.timeLineLoader = true;
          await this.patchReportingValue()
          this.len = this.data.action_items.length;
        }
      });
    }
    // console.log("Outside!!!!!!!!!!!!!!!!!!!!!!!");
    
    // this.timeLineLoader = true;
  }

  initiateApprovalAction = (
    id: number,
    val: string,
    reason: string,
    associate_id: number,
    is_head: number,
    reports_to: number
  ) => {
    let params = {
      request_id: this.reqId,
      action_type: val,
      workflow_id: id,
      reject_reason: reason,
      associate_id: associate_id,
      allocated_hours: this.allocated_hours,
      is_head: is_head,
      reports_to: reports_to,
      travel_type: this.travel_value ? this.travel_value : null,
      shift: this.shift_value ? this.shift_value : null
    };
    return new Promise((resolve, reject) => {
      this.subs.sink = this._tmService.initiateApprovalAction(params).subscribe(
        async (res: any) => {
          // console.log(res)
          if (!res.err) {
            if(val == "R") {
              sweetAlert.fire("Person Rejected !",'','success')
            }
            else if (val == "A") {
              sweetAlert.fire("Person Assigned Successfully !",'','success')
            }
          }
          else {
            if(res['isEmpRetired'])
              this._toaster.showWarning('Not allowed',res['msg'])
            else if(res['is_wf_approved'])
              this._toaster.showWarning("Workflow completed",
              "Workflow action completed by other approvers")
            else if(res['isa_member_restriction']){
              this._toaster.showWarning('Assignment not Allowed',res['messText']);
            }
            else if(res['isa_member_restriction_error']){
              this._toaster.showError("Error",res['messText'],3000);
            }
            else
              this._toaster.showError("Error","Something went wrong !",3000)
          }
          resolve(res);
        },
        (err) => {
          this._toaster.showError("Failed","Something went wrong !",3000)
          console.log(err);
          reject(err);
        }
      );
    });
  };

  cancelRequest(){

    Swal.fire({
      title: 'Cancel Request',
      text: "Are you sure about cancelling the request ?",
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Yes, cancel it!',
      cancelButtonText: "No, let me think",
    }).then(async (result) => {
      if(result.isConfirmed) {
        let params = {
          request_id: this.reqId,
          status_id: 5
        };
        return new Promise((resolve, reject) => {
          this.subs.sink = this._tmService.updateRequestStatus(params).subscribe(
            async (res: any) => {
              if (res.err == false) {
                if (!res.err) {
                  // sweetAlert.fire(`${res.msg}`, 'success');
                  if(res['has_access']) {
                    this._toaster.showSuccess(
                      'Success',
                      'Request Got Cancelled !',
                      2000
                    );
                    this.requestData['status'] = "Cancelled"
                    this.dialogRef.close(this.requestData);
                  }
                  else {
                    this._toaster.showWarning(
                      'Access denied',
                      'You do not have access to cancel the request.'
                    )
                  }

                }
                resolve(res.data);
              } else reject(res);
            },
            (err) => {
              console.log(err);
              reject(err);
            }
          );
        });
      }
    })
  }


  skillPopup(data:any){
    // console.log("Resource Data While Calling!!:",data);
    let dialogRef = this.dialog.open(SkillScoreComponent, {
      width: '846px',
      height: '400px',
      data:{modalParams:data.associate_id,request_id:this.requestData.request_id}
    });

    dialogRef.afterClosed().subscribe((res) => {
      console.log("Skill Score Closed!!!!");
    })
  }

  async openActivityLog(){
    const { ActivityLogComponent } = await import(
      'src/app/modules/shared-lazy-loaded-components/rm-activity-log/pages/activity-log/activity-log.component'
    );

    
    let dialogRef = this.dialog.open(ActivityLogComponent, {
      width: '980px',
      height: '518px',
      data:{modalParams:this.reqId}
    });

    dialogRef.afterClosed().subscribe((res) => {
      console.log("Skill Score Closed!!!!");
    })
  }

  retrieveFieldConfig(view_key) {
    return new Promise((resolve, reject) => {
      this.subs.sink = this._rmService.retrieveFieldConfig(view_key).subscribe(
        (res: any) => {
          resolve(res.data);
        },
        (err) => {
          console.log(err);
          reject(err);
        }
      );
    });
  }

  getRequestProjectDetails(){
    return new Promise((resolve, reject) => {
      this.subs.sink = this._rmService.getRequestProjectDetails(this.reqId).subscribe(
        (res: any) => {
          if (!res.err) {
            resolve(res.data);
          }
          else {
            this._toaster.showError("Failed","Error in Retreiving Project Details !",3000)
            resolve(null)
          }
        },
        (err) => {
          this._toaster.showError("Failed","Error in Retreiving Project Details !",3000)
          console.log(err);
          resolve(null);
        }
      );
    });
  }

  getAllocatedHours(val:any){
    let item = {
      start_date : moment(val.allocated_start_date).format('YYYY-MM-DD'),
      end_date: moment(val.allocated_end_date).format('YYYY-MM-DD'),
      associate_id: val.associate_id,
      allocation_percentage : val.utilization_capacity,
      isa_id: null,
      itemId: this.requestProjectDetails['project_id'] ? this.requestProjectDetails['project_id'] : null
    }
    return new Promise((resolve, reject) => {
      this.subs.sink = this._tmService.getPlannedAllocatedHours(item).subscribe(
        (res: any) => {
          if (res && res['messType'] == 'S') {
            resolve(res);
          }
          else {
            this._toaster.showError("Failed","Error in  fetching Planned Allocated Hours !",3000)
            resolve(null)
          }
        },
        (err) => {
          this._toaster.showError("Failed","Error in fetching Planned Allocated Hours !",3000)
          console.log(err);
          resolve(null);
        }
      );
    });
  }

  remainingBalanceCheck(val:any){
    let withOpportunity = this.requestProjectDetails['with_opportunity'] ? this.requestProjectDetails['with_opportunity'] : 0
    let item = {
      start_date : moment(val.allocated_start_date).format('YYYY-MM-DD'),
      end_date: moment(val.allocated_end_date).format('YYYY-MM-DD'),
      associate_id: val.associate_id,
      allocation_percentage : val.utilization_capacity,
      isa_id: null,
      itemId: this.requestProjectDetails['project_id'] ? this.requestProjectDetails['project_id'] : null,
      position: this.requestProjectDetails['project_role'] ? this.requestProjectDetails['project_role'] : null,
      position_type: withOpportunity ? 'QTC' : 'OTC',
      allocated_hours: this.allocated_hours,
      is_billable: this.requestProjectDetails['commercial'] ? this.requestProjectDetails['commercial'] : null
    }
    return new Promise((resolve, reject) => {
      this.subs.sink = this._tmService.getPlannedAllocatedAndRemainingBillableHours(item).subscribe(
        (res: any) => {
          if (res) {
            resolve(res);
          }
          else {
            this._toaster.showError("Failed","Error in  Checking Remainining Balance !",3000)
            resolve(null)
          }
        },
        (err) => {
          this._toaster.showError("Failed","Error in Checking Remainining Balance  !",3000)
          console.log(err);
          resolve(null);
        }
      );
    });
  }

  // Helper to check allocation and handle remaining balance
  async handleAllocationChecks(id,val,associate_id,content) {
    if(!(this.allocated_hours > 0)){
      this._toaster.showWarning('Warning', 'Planned Working Hours are 0 for the selected duration. Please select a different date range.');
      return;
    }
    const data = await this.remainingBalanceCheck(content);
    const allowAllocationCheck = data?.['allow_allocation'] || false;
    if (allowAllocationCheck) {
      await this.processResource(id,val,associate_id,content);
    } else {
      this._toaster.showWarning('Warning', 'Quote Position Balance is not available to allocate');
    }
  }

  // Helper for soft booking or initiating request
  async processResource(id,val,associate_id,content) {
    this.requestData=null
    await this.initiateApprovalAction(id, val, '', associate_id,content.is_head,content.reports_to);
    this.data = await this.getAllocatedResourceActionList();
    this.requestData = await this.getResourceRequest();
    _.find(this.inData.status,x=>{
          if(x.status === this.requestData.status){
        this.statusColor = x.color
      }
    });  
  }

  dayWiseAllocationCheck(val:any,){
    let item = {
      start_date : moment(val.allocated_start_date).format('YYYY-MM-DD'),
      end_date: moment(val.allocated_end_date).format('YYYY-MM-DD'),
      associate_id: val.associate_id,
      allocation_percentage : val.utilization_capacity,
      project_id: this.requestProjectDetails['portfolio_id'] ? this.requestProjectDetails['portfolio_id'] : null,
      item_id: this.requestProjectDetails['project_id'] ? this.requestProjectDetails['project_id'] : null
    }
    return new Promise((resolve, reject) => {
      this.subs.sink = this._tmService.checkDayWiseAllocation(item).subscribe(
        (res: any) => {
          if (res && res['messType'] == 'S') {
            resolve(res['data'] ? res['data'] : false)
          }
          else {
            this._toaster.showError("Warning","Failed in  Checking Day Wise Availability !",3000)
            resolve(false)
          }
        },
        (err) => {
          this._toaster.showError("Failed", "Error in  Checking Day Wise Availability !",3000)
          console.log(err);
          resolve(false);
        }
      );
    });
  }

  // Method to filter options based on input for each item
  filterOptions(index: number) {
    const filterValue = this.selectedNames[index]?.toLowerCase();
    this.filteredReportsToList[index] = this.reports_to_list.filter(option =>
      option.name.toLowerCase().includes(filterValue)
    );
    if(this.getSelectionName(this.selectedNames[index]) != filterValue){
      this.data.action_items[index].sub_content.reports_to = null
    }
  }
  routeProjectDetails(associateId) {
    if (associateId) {
      return new Promise((resolve, reject) => {
        this.subs.sink = this._tmService
          .getEmployeeProjectDetails(associateId)
          .subscribe(
            (res: any) => {
              this.summaryData = res.data; // or whatever relevant data you want to pass
              this.filterValue = 'RMG'; // set your caption accordingly
              this.graph = 'external'; // set your graph data accordingly
              this.openSummary();
  
              resolve(res.data);
            },
            (err) => {
              this._toaster.showError(
                'Error',
                'Failed to retrieve employee!',
                2000
              );
              console.log(err);
              reject(err);
            }
          );
      
      });
      
    }
    else{
      this._toaster.showWarning("Error","Associate Id Not Found")
    }
    
  }
  
  openSummary() {
    let data = this.summaryData; // Data to be passed to the dialog
    let caption = this.filterValue; // Caption for the dialog
    let graph = this.graph; // Graph data for the dialog
  
    console.log(data); // Log the data for debugging
  
    this.dialog.open(VoluntarySummaryComponent, {
      data: {
        columnData: data,
        caption: caption,
        graph: graph
      },
      width: '75%', // Dialog width
      height: '75%' // Dialog height
    });
  }

  // Handle selection change and update the respective data item
  onChange(option: any, index: number) {
    const selectedId = option.value;
    this.data.action_items[index].sub_content.reports_to = selectedId;

    const selectedOption = this.reports_to_list.find(item => item.id === selectedId);
    
    // Prevent patching if the same option is selected
    if (this.selectedNames[index] === selectedOption?.id) {
      return;
    }

    // Update with the selected ID only, not the name
    this.selectedNames[index] = selectedOption ? selectedOption?.id : null;
  }

  displaySelectedName = (id: number | null): string => {
    if (!id || !this.reports_to_list) {
      return '';
    }
    
    const option = (this.reports_to_list && this.reports_to_list.length > 0) ?  this.reports_to_list.find(item => item.id == id) : null;
    return option ? option.name : '';
  };

  // Handle head checkbox change
  isHeadChange(isChecked: boolean, index: number) {  
    this.data.action_items[index].sub_content.is_head = isChecked;
    if (isChecked) {
      
      this.data.action_items[index].sub_content.reports_to = null;
      this.selectedNames[index] = '';
    }
  }

  patchReportingValue(){
    
    this.data.action_items.forEach((item, index) => {
      this.filteredReportsToList[index] = this.reports_to_list;
      const selectedOption = this.reports_to_list.find(option => option?.id === item?.sub_content?.reports_to);
      this.selectedNames[index] = (selectedOption && selectedOption.id) ? selectedOption.id : '';
    });
  }

  getSelectionName(id){
    if(this.reports_to_list && this.reports_to_list.length > 0){
      const option = this.reports_to_list.find(item => item.id == id);
      return option ? option.name : '-';
    }
    else return '-'
  }


  disableScroll() {
    const timeline: HTMLElement = document.querySelector('.timeline');
    if (timeline) {
      timeline.style.overflow = 'hidden';  // Disable scrolling on .timeline
    }
  }

  enableScroll() {
    const timeline : HTMLElement = document.querySelector('.timeline');
    if (timeline) {
      timeline.style.overflow = 'scroll';  // Enable scrolling on .timeline
    }
  }

  /**
    * @description calculation Planned Hours
    * @param total_hours 
    * @param percentage 
    */
  calculatePlannedHours(total_hours:number,percentage:number){
    if(percentage && total_hours){
      let plannedValue = (percentage * total_hours) / 100;
      plannedValue = parseFloat(plannedValue.toFixed(2))
      
      return plannedValue;
    }
    else{
      return 0;
    } 
   }

   getTravelTypeList() {
    return new Promise((resolve, reject) => {
      this.subs.sink = this._rmService.getTravelTypeList().subscribe(
        (res: any) => {
          resolve(res.data);
        },
        (err) => {
          console.log(err);
          reject(err);
        }
      );
    });
  }

  getShiftMasterList() {
    return new Promise((resolve, reject) => {
      this.subs.sink = this._rmService.getShiftMasterList().subscribe(
        (res: any) => {
          resolve(res.data);
        },
        (err) => {
          console.log(err);
          reject(err);
        }
      );
    });
  }

  get travelTypes(): FormArray {
    return this.form.get('travelTypes') as FormArray;
  }

  // Function to get the value of a specific index
  getValueAtIndex(index: number): string {
    return this.travelTypes.at(index).value;
  }

  getTravelType(id){
    if (id) {
      const type = this.travelTypeList.find(type => type.id == id);
      return type ? type.name : "-";
    }
    return "-";
  }

  get shiftTypes(): FormArray {
    return this.form.get('shiftTypes') as FormArray;
  }

  // Function to get the value of a specific index
  getValueAtIndexShift(index: number): string {
    return this.shiftTypes.at(index).value;
  }

  getShiftType(id){
    if (id) {
      const type = this.shiftMasterList.find(type => type.id == id);
      return type ? type.name : "-";
    }
    return "-";
  }

}
