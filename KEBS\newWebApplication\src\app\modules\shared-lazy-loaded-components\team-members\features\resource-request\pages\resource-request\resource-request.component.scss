.resourceRequest{
  ::ng-deep .mat-tooltip {
    font-family: "Plus Jakarta Sans" !important;//Zifo theme
  }
    overflow: hidden;
    ::ng-deep .infinite-scroll-fixed {
        height:var(--dynamicTableHeight);
    }
    ::ng-deep .udrf-body-styles .infinite-scroll-fixed{
        min-height: var(--dynamicTableHeight) !important;
        max-height: 70vh !important;
        overflow-y: scroll;
        overflow-x: scroll;
        width: var(--dynamicTableWidth) !important;
        max-width: 100vw;
        height: var(--dynamicTableHeight) !important;
    }
    ::ng-deep mat-sidenav-content {
        overflow-x: hidden;
    }
    ::ng-deep .udrf-body-styles .infinite-scroll-large-fixed{
        min-height: var(--dynamicTableHeight) !important;
        max-height: 70vh !important;
        overflow-y: scroll;
        overflow-x: scroll;
        width: var(--dynamicTableWidth) !important;
        max-width: 100vw;
        height: var(--dynamicTableHeight) !important;
    }
    ::ng-deep .udrf-body-styles .infinite-scroll-large-fixed-shortened {
        min-height: var(--dynamicTableHeightShortened) !important;
        max-height: 70vh !important;
        overflow-y: scroll;
        overflow-x: scroll;
        // height: 55vh;
        width: var(--dynamicTableWidth) !important;
        height: var(--dynamicTableHeightShortened) !important;
    }
    ::ng-deep .udrf-body-styles {
      margin-left: 0px ;
      margin-right: 0px;
    }
    ::ng-deep .udrf-header-styles{
        width: var(--dynamicHeaderWidth) !important;
    }
    ::ng-deep .udrf-header-styles .divAlignItemCenter {
        display: flex;
        align-items: center;
        width: 100vw !important;
    }
    .headerUdrf{
        width: 100vw !important;
    }

    ::ng-deep .udrf-header-card {
        background-color: #fafafa !important;
        width: fit-content !important;
      }
      ::ng-deep .udrf-body-styles .smallSubtleText {
        color: #66615b;
        font-size: 11px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        display: inline;
        height: auto !important;
        line-height: 1.9vh;
    }
      ::ng-deep .udrf-body-styles .smallSubtleText, .udrf-body-styles .smallSubtleTextForColumnWidth {
        color: #66615b;
        font-size: 11px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        display: inline;
        height: auto !important;
    }
      .noteImage{
        position: relative;
        height: 168px;
        margin-top: 0%;
        margin-bottom: 10px;
      }
      .description{
        position: relative;
        display: block;
        font-family: "Roboto";
        font-style: normal;
        font-weight: 700;
        font-size: 14px;
        line-height: 16px;
        text-align: center;
        text-transform: capitalize;
        color: #45546E;
        /* margin-top: 203px; */
        
      }
      .desc1{
        position: relative;
        top: 80%;
        font-family: "Roboto";
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 16px;
        text-align: center;
        color: #8B95A5;
      }
    .requestButton{
        color: white;
        flex-direction: row;
        align-items: center;
        position: absolute;
        height: 32px;
        // margin-left: 101px;
        margin-left: 53px;
       // background: #EE4961;
        background: #79BA44; // Zifo theme
        border-radius: 4px;
        font-size: 11px;
        line-height: 16px;
        display: flex;
        align-items: center;
    }
    .tileBody{
        width: var(--dynamicTableWidth) !important;
        height: var(--dynamicTableHeight) !important;
        overflow: scroll;
    }
    .card-data {
        margin-top: 0px;
        padding: 0px;
        margin-bottom: 1px;
      }
    .noData{
        position: absolute;
        top: 41%;
        left: 43%;
        display: block;
      }
      .Table {
        width: 100%;
        // overflow-x: scroll;
        // overflow-y: scroll;
        // background: white;
        // max-height: 71vh;
        // height: 63vh;
      }
      ::ng-deep .spinner-theme circle {
        stroke: #79BA44 !important;
      }
      
      ::ng-deep .spinner-align circle{
        stroke: #79BA44 !important;
      }
      
}
