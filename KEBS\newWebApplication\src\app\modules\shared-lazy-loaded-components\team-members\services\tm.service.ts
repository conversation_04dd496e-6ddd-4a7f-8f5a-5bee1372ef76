import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { RolesService } from 'src/app/services/acl/roles.service';
import * as _ from 'underscore';
import { BehaviorSubject  } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class TmService {
  tabIdForCompilerSync:number = 1;
  app_access_list : any = []
  application_id = 460

  private quoteData$ = new BehaviorSubject<any>(null);
  selectedQuote$ = this.quoteData$.asObservable();

  constructor(private _http:HttpClient, private roleService: RolesService) {
    this.app_access_list = this.getAllRoleAccess()
  }



  getTeamMembersRequestResourceList(details) {
    return this._http.post('/api/rmg/request/teamMembersRequestListUdrf', {
      params: details,
    });
  }

  getTeamMembersRequestResourceCount(details) {
    return this._http.post('/api/rmg/request/teamMembersRequestCountUdrf', {
      params: details,
    });
  }

  getResourceRequestDetail(params){
    return this._http.post('/api/rmg/request/resourceRequestDetail',{
      params:params
    });
  }

  getAllocatedResourceActionList(params){
    return this._http.post('/api/rmg/resources/getAllocatedResourceActionList',{
      request_id:params
    });
  }

  initiateApprovalAction(params){
    return this._http.post('/api/rmg/resources/initiateApprovalAction',{
      params:params
    });
  }

  retrieveFieldConfig(view_key) {
    return this._http.post('/api/rmg/utilityRoute/retrieveFormFieldConfig',{
      view_key:view_key
    });
  }
  adminretrieveFieldConfig() {
    return this._http.post('/api/rmg/utilityRoute/adminretrieveFormFieldConfig',{
    });
  }

  setIndexForCompilerSync(index:number){
    this.tabIdForCompilerSync = index;
  }
  getIndexForCompilerSync(){
    return this.tabIdForCompilerSync;
  }
  retrievefielddatas(fieldid,fieldname,tablename){
    return this._http.post('/api/rmg/utilityRoute/retrievefielddatas',{
      id:fieldid,
      field_name:fieldname,
      table_name:tablename
    })
  }

  checkAccessForObject(object_id) {
    return _.where(this.app_access_list,{object_id: object_id}).length > 0
  }

  getAllRoleAccess() {
    let accessList = _.where(this.roleService.roles, { application_id: this.application_id });
    return accessList;
  }

  updateadminFieldConfig(params){
    return this._http.post('/api/rmg/utilityRoute/updateFormFields',{
      params:params
    })
  }

  getRequestStatusList(){
    return this._http.post('/api/rmg/request/getRequestStatusfilter',{})
  }

  retrieveRmConfig() {
    return this._http.post('/api/rmg/utilityRoute/retrieveRmConfig', {})
  }

  updateRequestStatus(params){
    return this._http.post('/api/rmg/request/updateRequestStatus',{
      params:params
    })
  }


  retrieveSkillScore(params){
    return this._http.post('/api/rmg/resources/retrieveReqEmpSkillMatch',{
      'request_id':params.request_id,
      'resource_aid':params.resource_aid
    })
  }

  getItemLevelData(params){
    return this._http.post('/api/rmg/projects/projectItemData',{
      'item_id':params
    })
  }

  getOpportunityData(item_id){
    return this._http.post('/api/rmg/projects/getprojectOpportunityData',{
      'item_id':item_id
    })
  }


  setQuoteData(data:any){
    this.quoteData$.next(data);
  }

  getCRMQuotePositionList(data) {
    return this._http.post('/api/rmg/request/crmQuoteUdrfList', {
      params: data
    });
  }

  getCRMQuotePositionCount(data){
    return this._http.post('/api/rmg/request/crmQuoteUdrfCount', {
      params: data
    });
  }

  getRateCardPositions(customerId){
    return this._http.post('/api/rmg/projects/getRateCardPositions',{
      'customer_id':customerId
    })
  }

  getPlannedAllocatedAndRemainingBillableHours(item){
    return this._http.post("/api/misFunctions/getPlannedAllocatedAndRemainingBillableHours",
      {
        project_id: item.itemId,
        position_id: item.position,
        position_type: item.position_type,
        start_date: item.start_date,
        end_date: item.end_date,
        utilization_percentage: item.allocation_percentage,
        associate_id: item.associate_id,
        is_billable: item.is_billable,
        isa_id: item.isa_id,
        allocated_hours: item.allocated_hours
      });
  }

  getPlannedAllocatedHours(item){
    return this._http.post("/api/misFunctions/getPlannedAllocatedHours",
      {
        project_id: item.itemId,
        start_date: item.start_date,
        end_date: item.end_date,
        associate_id: item.associate_id,
        isa_id: item.isa_id
      });
  }

  getCustomerDetails(){
    return this._http.post('/api/rmg/projects/getCustomerList',{})
  }

  checkDayWiseAllocation(params){
    return this._http.post("/api/pm/planning/checkDayWizeEmployeeHours",{
      start_date: params.start_date,
      end_date: params.end_date,
      associate_id: params.associate_id,
      percentage: params.allocation_percentage,
      project_id: params.project_id,
      item_id: params.item_id
    })
  }


  getPMFormCustomizeConfigV(){

        return this._http.post("/api/pm/masterData/getPMFormCustomizeConfig",{})
  
  }
  getEmployeeProjectDetails(associateId) {
    return this._http.post(
      '/api/employee360/organizationDetails/getEmployeeProjectDetails',
      {
        associate_id: associateId,
      }
    );
  }

}