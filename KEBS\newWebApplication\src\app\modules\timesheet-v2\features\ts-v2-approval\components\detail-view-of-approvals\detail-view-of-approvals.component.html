<div class="approvals-spinner" [hidden]="!initialLoading">
  <mat-spinner class="main-spinner" diameter="40"></mat-spinner>
</div>
<div class="approvals-header" [hidden]="initialLoading">
  <div
    class="align-items-row col-7 p-0"
    *ngIf="detailedViewPayload && employeeConfig"
  >
    <div class="back-btn mr30 col-1 pl-0" (click)="onCloseDetailView()">
      <mat-icon class="back-btn-icon">chevron_left</mat-icon>
      Back
    </div>
    <div style="display: flex; align-items: center" class="col-3 pl-0">
      <div style="margin-right: 8px">
        <app-user-image
          [id]="detailedViewPayload.oid"
          imgWidth="24px"
          imgHeight="24px"
        ></app-user-image>
      </div>
      <div
        style="white-space: nowrap; text-overflow: ellipsis; overflow: hidden"
      >
        <p
          class="bold-text-name"
          [matTooltip]="detailedViewPayload?.employee_name"
        >
          {{ detailedViewPayload?.employee_name }}
        </p>
        <p
          class="light-text-time"
          [matTooltip]="
            (detailedViewPayload?.start_date | dateFormat) +
            ' - ' +
            (detailedViewPayload?.end_date | dateFormat) +
            ' (' +
            (detailedViewPayload?.timesheet_month | monthFormat) +
            ')'
          "
        >
          {{ detailedViewPayload?.start_date | dateFormat }} -
          {{ detailedViewPayload?.end_date | dateFormat }}
          ({{ detailedViewPayload?.timesheet_month | monthFormat }})
        </p>
      </div>
    </div>
    <div
      class="col pl-0 overflow-div"
      *ngIf="tsUiConfig['UI-TAA-DT-007']?.is_visible"
    >
      <p
        class="light-text"
        [matTooltip]="tsUiConfig['UI-TAA-DT-007']?.config_text"
      >
        {{ tsUiConfig["UI-TAA-DT-007"]?.config_text }}
      </p>
      <p class="bold-text-header" [matTooltip]="employeeConfig[0]?.role">
        {{ employeeConfig[0]?.role ? employeeConfig[0]?.role : "-" }}
      </p>
    </div>
    <div
      class="col pl-0 overflow-div"
      *ngIf="tsUiConfig['UI-TAA-DT-004']?.is_visible"
    >
      <p
        class="light-text"
        [matTooltip]="tsUiConfig['UI-TAA-DT-004']?.config_text"
      >
        {{ tsUiConfig["UI-TAA-DT-004"]?.config_text }}
      </p>
      <p class="bold-text-header" [matTooltip]="employeeConfig[0]?.entity">
        {{ employeeConfig[0]?.entity ? employeeConfig[0]?.entity : "-" }}
      </p>
    </div>
    <div
      class="col pl-0 overflow-div"
      *ngIf="tsUiConfig['UI-TAA-DT-005']?.is_visible"
    >
      <p
        class="light-text"
        [matTooltip]="tsUiConfig['UI-TAA-DT-005']?.config_text"
      >
        {{ tsUiConfig["UI-TAA-DT-005"]?.config_text }}
      </p>
      <p
        class="bold-text-header"
        [matTooltip]="employeeConfig[0]?.division_name"
      >
        {{
          employeeConfig[0]?.division_name
            ? employeeConfig[0]?.division_name
            : "-"
        }}
      </p>
    </div>
    <div
      class="col pl-0 overflow-div"
      *ngIf="tsUiConfig['UI-TAA-DT-006']?.is_visible"
    >
      <p
        class="light-text"
        [matTooltip]="tsUiConfig['UI-TAA-DT-006']?.config_text"
      >
        {{ tsUiConfig["UI-TAA-DT-006"]?.config_text }}
      </p>
      <p
        class="bold-text-header"
        [matTooltip]="employeeConfig[0]?.sub_division_name"
      >
        {{
          employeeConfig[0]?.sub_division_name
            ? employeeConfig[0]?.sub_division_name
            : "-"
        }}
      </p>
    </div>
    <div
    class="col pl-0 overflow-div"
    *ngIf="basicTsConfig.ts_approval_overview_detail_config == '1'"
    >
    <mat-icon class="keyboard-arrow-icon"  style="cursor: pointer; color: #8B95A5; font-size: 15px; line-height: 20px;"(click)="openRejectionHistory()">mark_chat_unread</mat-icon>
  </div>
  </div>
  <div
    class="align-items-row col-5 p-0"
    style="justify-content: flex-end"
    *ngIf="detailedViewPayload"
  >
    <div *ngIf="tsUiConfig['UI-TAA-DT-001']?.is_visible">
      <p class="light-text">{{ tsUiConfig["UI-TAA-DT-001"]?.config_text }}</p>
      <p
        class="bold-text"
        [matTooltip]="detailedViewPayload?.final_hours | hoursWorkedSplit"
      >
        {{ detailedViewPayload?.final_hours | hoursWorkedSplit }}
      </p>
    </div>
    <div class="mr24" *ngIf="tsUiConfig['UI-TAA-DT-001']?.is_visible">
      <button
        style="border: none; background-color: white; padding: 0"
        [matMenuTriggerFor]="hoursPopUp"
      >
        <mat-icon class="drop-down-icon">arrow_drop_down</mat-icon>
      </button>
    </div>
    <div class="mr30" *ngIf="tsUiConfig['UI-TAA-DT-002']?.is_visible">
      <p class="light-text">{{ tsUiConfig["UI-TAA-DT-002"]?.config_text }}</p>
      <p class="bold-text" [matTooltip]="detailedViewPayload?.Leaves">
        {{ detailedViewPayload?.Leaves }}
      </p>
    </div>
    <div class="mr30" *ngIf="tsUiConfig['UI-TAA-DT-003']?.is_visible">
      <p class="light-text">{{ tsUiConfig["UI-TAA-DT-003"]?.config_text }}</p>
      <p
        class="bold-text"
        [matTooltip]="
          detailedViewPayload?.final_overtime_hours | hoursWorkedSplit
        "
      >
        {{ detailedViewPayload?.final_overtime_hours | hoursWorkedSplit }}
      </p>
    </div>
    <div class="mr16">
      <button
        mat-button
        class="reject-btn"
        (click)="onReject()"
        [disabled]="
          checkedWeekNumbers.length <= 0 ||
          (basicTsConfig | isTimesheetApprovalsLocked : endDate)
        "
      >
        Reject
      </button>
    </div>
    <div class="mr16">
      <button
        mat-button
        class="approve-btn"
        (click)="onApprove()"
        [disabled]="
          checkedWeekNumbers.length <= 0 ||
          (basicTsConfig | isTimesheetApprovalsLocked : endDate)
        "
      >
        Approve
      </button>
    </div>
    <div>
      <mat-icon class="ques-icon" (click)="openHelpDialog()">help</mat-icon>
    </div>
  </div>
</div>
<mat-divider [hidden]="initialLoading" style="color: #e8e9ee"></mat-divider>
<div
  [hidden]="initialLoading"
  class="align-items-row"
  *ngIf="expansionFeatureVisibility"
>
  <span class="expansion-text" (click)="onExpandOrCollapse()">{{
    isPanelExpanded ? "Contract All" : "Expand All"
  }}</span>
  <mat-icon class="expansion-icon" (click)="onExpandOrCollapse()">{{
    isPanelExpanded ? "expand_less" : "expand_more"
  }}</mat-icon>
</div>
<div
  style="margin-top: 22px"
  [hidden]="initialLoading"
  *ngIf="!expansionFeatureVisibility"
></div>
<div
  [hidden]="initialLoading"
  class="approvals-content"
  #scrollContainer
  *ngIf="!initialLoading"
>
  <div class="col-3 p-0 pt-2">
    <ng-container
      *ngFor="let week of weeksOfMonth?.weeks; let weekIndex = index"
    >
      <div class="align-items-row" style="margin-bottom: 16px">
        <div
          *ngIf="
            week?.data[(week?.data | getStartDayIndex)]?.costcenter[0]
              ?.statusId !== 2
          "
          style="margin-right: 26px"
        ></div>
        <div style="height: 16px">
          <mat-checkbox
            *ngIf="
              week?.data[(week?.data | getStartDayIndex)]?.costcenter[0]
                ?.statusId === 2
            "
            class="checkbox-alignment"
            [(ngModel)]="week.isChecked"
            (ngModelChange)="selectedWeekNumber()"
          ></mat-checkbox>
        </div>
        <div
          class="week-border"
          [ngStyle]="{
            background:
              week?.data[(week?.data | getStartDayIndex)]?.costcenter[0]
                ?.statusId === 4
                ? '#EEF9E8'
                : week?.data[(week?.data | getStartDayIndex)]?.costcenter[0]
                    ?.statusId === 3
                ? '#FFEBEC'
                : '#E8F4FF',
            border:
              week?.data[(week?.data | getStartDayIndex)]?.costcenter[0]
                ?.statusId === 4
                ? '1px solid #52C41A'
                : week?.data[(week?.data | getStartDayIndex)]?.costcenter[0]
                    ?.statusId === 3
                ? '1px solid #FF3A46'
                : '1px solid #1890FF'
          }"
        >
          <div class="week-text">Week {{ week?.weekNumber }}</div>
          <div
            *ngIf="
              !week.isExpanded || (basicTsConfig | isTimesheetApprovalsLocked : endDate)
            "
          >
            <mat-icon
              [matMenuTriggerFor]="approverejectMenu"
              class="week-more-vert"
              >more_vert</mat-icon
            >
            <mat-menu #approverejectMenu="matMenu" yPosition="below">
              <div class="approve-reject-menu">
                <p
                  [ngStyle]="{
                    'pointer-events':
                      week?.data[(week?.data | getStartDayIndex)]?.costcenter[0]
                        ?.statusId !== 2
                        ? 'none'
                        : 'auto',
                    opacity:
                      week?.data[(week?.data | getStartDayIndex)]?.costcenter[0]
                        ?.statusId !== 2
                        ? '0.3'
                        : '1'
                  }"
                  class="approve-reject-menu-item"
                  style="margin-bottom: 8px"
                  (click)="onApproveUsingMenu(week?.weekNumber, weekIndex)"
                >
                  Approve
                </p>
                <p
                  [ngStyle]="{
                    'pointer-events':
                      week?.data[(week?.data | getStartDayIndex)]?.costcenter[0]
                        ?.statusId !== 2
                        ? 'none'
                        : 'auto',
                    opacity:
                      week?.data[(week?.data | getStartDayIndex)]?.costcenter[0]
                        ?.statusId !== 2
                        ? '0.3'
                        : '1'
                  }"
                  class="approve-reject-menu-item"
                  (click)="onRejectUsingMenu(week?.weekNumber, weekIndex)"
                >
                  Reject
                </p>
              </div>
            </mat-menu>
          </div>
        </div>
      </div>
      <div>
        <mat-expansion-panel
          class="panel"
          [hideToggle]="true"
          [expanded]="week.isExpanded"
          [disabled]="true"
          [ngStyle]="{
            'margin-bottom': '16px'
          }"
          [ngClass]="{
            'panel-content':
              week?.data[(week?.data | getStartDayIndex)]?.costcenter[0]
                ?.tasks ||
              week?.data[(week?.data | getStartDayIndex)]?.costcenter[0]
                ?.subProjectId
                ? false
                : true
          }"
        >
          <mat-expansion-panel-header>
            <mat-panel-title class="col-12 p-0">
              <mat-icon
                *ngIf="
                  (week?.data[(week?.data | getStartDayIndex)]?.costcenter[0]
                    ?.tasks ||
                    week?.data[(week?.data | getStartDayIndex)]?.costcenter[0]
                      ?.subProjectId) &&
                  week.isExpanded
                "
                (click)="
                  week.isExpanded = !week.isExpanded;
                  onExpandOrCollapseIndividual()
                "
                class="expand-icon"
                [hidden]="
                  week?.data[(week?.data | getStartDayIndex)]?.costcenter[0]
                    ?.hoursworked
                "
                >expand_less</mat-icon
              >
              <mat-icon
                *ngIf="
                  (week?.data[(week?.data | getStartDayIndex)]?.costcenter[0]
                    ?.tasks ||
                    week?.data[(week?.data | getStartDayIndex)]?.costcenter[0]
                      ?.subProjectId) &&
                  !week.isExpanded
                "
                (click)="
                  week.isExpanded = !week.isExpanded;
                  onExpandOrCollapseIndividual()
                "
                class="contract-icon"
                [hidden]="
                  week?.data[(week?.data | getStartDayIndex)]?.costcenter[0]
                    ?.hoursworked
                "
                >expand_more</mat-icon
              >
              <div
                [ngClass]="{
                  'col-11':
                    week?.data[(week?.data | getStartDayIndex)]?.costcenter[0]
                      ?.hoursworked,
                  'col-10':
                    !week?.data[(week?.data | getStartDayIndex)]?.costcenter[0]
                      ?.hoursworked
                }"
                class="p-0"
                style="
                  display: flex;
                  flex-direction: column;
                  justify-content: flex-start;
                "
                [ngStyle]="{
                  'margin-left':
                    week?.data[(week?.data | getStartDayIndex)]?.costcenter[0]
                      ?.tasks ||
                    week?.data[(week?.data | getStartDayIndex)]?.costcenter[0]
                      ?.subProjectId
                      ? ''
                      : '26px'
                }"
              >
                <div
                  class="expansion-panel-cc-name"
                  matTooltip="{{
                    week?.data[(week?.data | getStartDayIndex)]?.costcenter[0]
                      ?.costcenter +
                      ' - ' +
                      week?.data[(week?.data | getStartDayIndex)]?.costcenter[0]
                        ?.costcentername
                  }}"
                >
                  {{
                    week?.data[(week?.data | getStartDayIndex)]?.costcenter[0]
                      ?.costcenter
                  }}
                  -
                  {{
                    week?.data[(week?.data | getStartDayIndex)]?.costcenter[0]
                      ?.costcentername
                  }}
                </div>
                <div
                  matTooltip="{{
                    week?.data[(week?.data | getStartDayIndex)]?.costcenter[0]
                      ?.subProjectName
                  }}"
                  class="expansion-panel-subcc-name"
                  *ngIf="
                    week?.data[(week?.data | getStartDayIndex)]?.costcenter[0]
                      ?.subProjectName
                  "
                >
                  {{
                    week?.data[(week?.data | getStartDayIndex)]?.costcenter[0]
                      ?.subProjectName
                  }}
                </div>
              </div>
            </mat-panel-title>
          </mat-expansion-panel-header>
          <div
            style="
              display: flex;
              flex-direction: row;
              justify-content: space-between;
              align-items: center;
            "
            [ngStyle]="{
              'margin-bottom':
                week?.data[(week?.data | getStartDayIndex)]?.costcenter[0]
                  ?.tasks.length -
                  1 ===
                taskIndex
                  ? '9px'
                  : '11px'
            }"
            *ngFor="
              let task of week?.data[(week?.data | getStartDayIndex)]
                ?.costcenter[0]?.tasks;
              let taskIndex = index
            "
          >
            <div class="col-11 p-0">
              <p class="task-name-text" matTooltip="{{ task?.taskName }}">
                {{ task?.taskName }}
                <mat-icon *ngIf="basicTsConfig?.show_billable_tag == 1 && task?.billable_act == 1" style="font-size: 12px; padding-left: 10px;">sell</mat-icon>
              </p>
              <p class="task-url-text" matTooltip="{{ task?.taskURL }}">
                {{ task?.taskURL }}
              </p>
            </div>
          </div>
          <div
            *ngIf="
              week?.data[(week?.data | getStartDayIndex)]?.costcenter[0]
                ?.tasks ||
              week?.data[(week?.data | getStartDayIndex)]?.costcenter[0]
                ?.subProjectId
                ? true
                : false
            "
            style="margin-bottom: 8px"
          ></div>
        </mat-expansion-panel>
      </div>
    </ng-container>
  </div>
  <mat-divider [vertical]="true" class="vertical-divider"></mat-divider>
  <div class="col-9 p-0 pt-2" style="display: flex">
    <ng-container
      *ngFor="let item of [1, 2, 3, 4, 5, 6, 7]; let dayIndex = index"
    >
      <div
        class="col p-0"
        style="display: flex; flex-direction: column; align-items: center"
      >
        <ng-container
          *ngFor="let currentDay of weeksOfMonth?.weeks; let i = index"
        >
          <div
            class="date-border"
            [ngStyle]="{
              background:
                currentDay?.data[dayIndex]?.dayIndex === -1
                  ? '#f7f9fb'
                  : currentDay?.data[dayIndex]?.costcenter[0]?.statusId === 4
                  ? '#EEF9E8'
                  : currentDay?.data[dayIndex]?.costcenter[0]?.statusId === 3
                  ? '#FFEBEC'
                  : currentDay?.data[dayIndex]?.costcenter[0]?.statusId === 2
                  ? '#E8F4FF'
                  : '',
              border:
                currentDay?.data[dayIndex]?.dayIndex === -1
                  ? '1px solid #dadce2'
                  : currentDay?.data[dayIndex]?.costcenter[0]?.statusId === 4
                  ? '1px solid #52C41A'
                  : currentDay?.data[dayIndex]?.costcenter[0]?.statusId === 3
                  ? '1px solid #FF3A46'
                  : currentDay?.data[dayIndex]?.costcenter[0]?.statusId === 2
                  ? '1px solid #1890FF'
                  : ''
            }"
            *ngIf="!(currentDay?.data[dayIndex]?.date == employeeEndDate) && !(isDateInLeaveArray(currentDay?.data[dayIndex]?.date, 0))"
          >
            {{ currentDay?.data[dayIndex]?.date | dateFormat2 }}
          </div>
          <div class="last-working-day-border" *ngIf="(currentDay?.data[dayIndex]?.date == employeeEndDate)" [ngStyle]="{
                      background:
                        currentDay?.data[dayIndex]?.dayIndex === -1
                          ? '#f7f9fb'
                          : currentDay?.data[dayIndex]?.costcenter[0]?.statusId === 4
                          ? '#EEF9E8'
                          : currentDay?.data[dayIndex]?.costcenter[0]?.statusId === 3
                          ? '#FFEBEC'
                          : currentDay?.data[dayIndex]?.costcenter[0]?.statusId === 2
                          ? '#E8F4FF'
                          : '',
                      border:
                        currentDay?.data[dayIndex]?.dayIndex === -1
                          ? '1px solid #dadce2'
                          : currentDay?.data[dayIndex]?.costcenter[0]?.statusId === 4
                          ? '1px solid #52C41A'
                          : currentDay?.data[dayIndex]?.costcenter[0]?.statusId === 3
                          ? '1px solid #FF3A46'
                          : currentDay?.data[dayIndex]?.costcenter[0]?.statusId === 2
                          ? '1px solid #1890FF'
                          : ''
                    }">
            <div class="last-working-day-border-overlap">
              <p style="padding-left: 20px" class="last-working-day-text">
                LWD
              </p>
            </div>
            <div style="
                                    display: flex;
                                    width: inherit;
                                    justify-content: center;
                                  ">
              <p class="hours-worked-text-on-last-working-day">
                {{ currentDay?.data[dayIndex]?.date | dateFormat2 }}
              </p>
            </div>
          </div>
          <div
            *ngIf="basicTsConfig?.show_leave_in_approval_detail_view == 1 && isDateInLeaveArray(currentDay?.data[dayIndex]?.date, 0)"
            class="leave-day-border" [ngStyle]="{
                      background:
                        currentDay?.data[dayIndex]?.dayIndex === -1
                          ? '#f7f9fb'
                          : currentDay?.data[dayIndex]?.costcenter[0]?.statusId === 4
                          ? '#EEF9E8'
                          : currentDay?.data[dayIndex]?.costcenter[0]?.statusId === 3
                          ? '#FFEBEC'
                          : currentDay?.data[dayIndex]?.costcenter[0]?.statusId === 2
                          ? '#E8F4FF'
                          : '',
                      border:
                        currentDay?.data[dayIndex]?.dayIndex === -1
                          ? '1px solid #dadce2'
                          : currentDay?.data[dayIndex]?.costcenter[0]?.statusId === 4
                          ? '1px solid #52C41A'
                          : currentDay?.data[dayIndex]?.costcenter[0]?.statusId === 3
                          ? '1px solid #FF3A46'
                          : currentDay?.data[dayIndex]?.costcenter[0]?.statusId === 2
                          ? '1px solid #1890FF'
                          : ''
                    }">
            <div class="leave-day-border-overlap">
              <p style="padding-left: 20px" class="leave-day-text" [matTooltip]="isDateInLeaveArray(currentDay?.data[dayIndex]?.date, 2)">
                {{isDateInLeaveArray(currentDay?.data[dayIndex]?.date, 1)}}
              </p>
            </div>
            <div style="
                    display: flex;
                    width: inherit;
                    justify-content: center;
                    ">
              <p class="hours-worked-text-on-leave-day">
                {{ currentDay?.data[dayIndex]?.date | dateFormat2 }}
              </p>
            </div>
          </div>
          <div
            *ngIf="
              !(currentDay?.data[dayIndex]?.dayIndex === item) &&
              !currentDay?.data[dayIndex]?.costcenter[0]?.tasks
            "
            [ngStyle]="{
              'margin-top': currentDay.isExpanded
                ? (currentDay?.data[(currentDay?.data | getStartDayIndex)]
                  | emptySpace)
                : '68px'
            }"
          ></div>
          <div
            *ngIf="
              currentDay?.data[dayIndex]?.costcenter[0]?.tasks;
              then hasTask;
              else noTask
            "
          ></div>
          <ng-template #noTask>
            <div
              class="hours-worked-border"
              *ngIf="
                [1, 7, 8].includes(
                  currentDay?.data[dayIndex]?.costcenter[0]?.attendanceTypeId
                )
              "
              [ngStyle]="{
                border:
                  basicTsConfig?.overtime_allowed_in_ts == '1' &&
                  (currentDay?.data[dayIndex]?.costcenter[0]?.overtimeHours
                    | overtimeColorStamp)
                    ? '1px solid #FF3A46'
                    : '1px solid #dadce2'
              }"
              style="margin-bottom: 16px; margin-top: 20px"
            >
              <div
                class="comments-overlap"
                *ngIf="
                  (currentDay?.data[dayIndex]?.costcenter[0]?.msg != '' &&
                    currentDay?.data[dayIndex]?.costcenter[0]?.msg != null) ||
                  (basicTsConfig?.display_hours_splitup == '1' &&
                    currentDay?.data[dayIndex]?.costcenter[0]?.hours_splitup &&
                    currentDay?.data[dayIndex]?.costcenter[0]?.hours_splitup
                      .length > 0)
                "
                (click)="
                  openCommentsDialog(
                    currentDay?.data[dayIndex]?.costcenter[0],
                    currentDay?.data[dayIndex]?.date
                  )
                "
              >
                <mat-icon
                  style="font-size: 18px; color: #45546e; cursor: pointer"
                  >chat_bubble</mat-icon
                >
              </div>
              <p
                class="hours-worked-text"
                [ngStyle]="{
                  color:
                    basicTsConfig?.overtime_allowed_in_ts == '1' &&
                    (currentDay?.data[dayIndex]?.costcenter[0]?.overtimeHours
                      | overtimeColorStamp)
                      ? '#FF3A46'
                      : '#6e7b8f'
                }"
                (click)="openRejectionComment(currentDay, currentDay?.data[dayIndex]?.date, currentDay?.data[dayIndex]?.costcenter[0]?.approvers, currentDay?.data[dayIndex]?.costcenter[0]?.costCenterId, currentDay?.data[dayIndex]?.costcenter[0]?.subProjectId, currentDay?.data[dayIndex]?.costcenter[0]?.locationId, currentDay?.data[dayIndex]?.costcenter[0]?.costcentertype, -1, dayIndex)"
              >
                {{
                  basicTsConfig?.overtime_allowed_in_ts == "1"
                    ? (currentDay?.data[dayIndex]?.costcenter[0]?.hoursworked
                      | getRegularAndOvertimeHoursInDetailView
                        : currentDay?.data[dayIndex]?.costcenter[0]
                            ?.overtimeHours
                      | hoursWorkedSplit)
                    : (currentDay?.data[dayIndex]?.costcenter[0]?.hoursworked
                      | hoursWorkedSplit)
                }}
              </p>
            </div>
            <div
              class="hours-worked-border"
              *ngIf="
                currentDay?.data[dayIndex]?.costcenter[0]?.attendanceTypeId ===
                5
              "
              style="margin-bottom: 16px; margin-top: 20px"
            >
              <div
                class="comments-overlap"
                *ngIf="
                  (currentDay?.data[dayIndex]?.costcenter[0]?.msg != '' &&
                    currentDay?.data[dayIndex]?.costcenter[0]?.msg != null) ||
                  (basicTsConfig?.display_hours_splitup == '1' &&
                    currentDay?.data[dayIndex]?.costcenter[0]?.hours_splitup &&
                    currentDay?.data[dayIndex]?.costcenter[0]?.hours_splitup
                      .length > 0)
                "
                (click)="
                  openCommentsDialog(
                    currentDay?.data[dayIndex]?.costcenter[0],
                    currentDay?.data[dayIndex]?.date
                  )
                "
              >
                <mat-icon
                  style="font-size: 18px; color: #45546e; cursor: pointer"
                  >chat_bubble</mat-icon
                >
              </div>
              <p class="hours-worked-text" (click)="openRejectionComment(currentDay, currentDay?.data[dayIndex]?.date, currentDay?.data[dayIndex]?.costcenter[0]?.approvers, currentDay?.data[dayIndex]?.costcenter[0]?.costCenterId, currentDay?.data[dayIndex]?.costcenter[0]?.subProjectId, currentDay?.data[dayIndex]?.costcenter[0]?.locationId, currentDay?.data[dayIndex]?.costcenter[0]?.costcentertype, -1, dayIndex)">WO</p>
            </div>
            <div
              class="full-day-leave-border"
              *ngIf="
                currentDay?.data[dayIndex]?.costcenter[0]?.attendanceTypeId ===
                2
              "
              style="margin-bottom: 16px; margin-top: 20px"
              [style.border-color]="
                currentDay?.data[dayIndex]?.costcenter[0]?.leaveColourCode
              "
            >
              <div
                class="comments-overlap"
                *ngIf="
                  (currentDay?.data[dayIndex]?.costcenter[0]?.msg != '' &&
                    currentDay?.data[dayIndex]?.costcenter[0]?.msg != null) ||
                  (basicTsConfig?.display_hours_splitup == '1' &&
                    currentDay?.data[dayIndex]?.costcenter[0]?.hours_splitup &&
                    currentDay?.data[dayIndex]?.costcenter[0]?.hours_splitup
                      .length > 0)
                "
                (click)="
                  openCommentsDialog(
                    currentDay?.data[dayIndex]?.costcenter[0],
                    currentDay?.data[dayIndex]?.date
                  )
                "
              >
                <mat-icon
                  style="font-size: 18px; color: #45546e; cursor: pointer"
                  >chat_bubble</mat-icon
                >
              </div>
              <p
                [style.color]="
                  currentDay?.data[dayIndex]?.costcenter[0]?.leaveColourCode
                "
                class="leave-text"
                (click)="openRejectionComment(currentDay, currentDay?.data[dayIndex]?.date, currentDay?.data[dayIndex]?.costcenter[0]?.approvers, currentDay?.data[dayIndex]?.costcenter[0]?.costCenterId, currentDay?.data[dayIndex]?.costcenter[0]?.subProjectId, currentDay?.data[dayIndex]?.costcenter[0]?.locationId, currentDay?.data[dayIndex]?.costcenter[0]?.costcentertype, -1, dayIndex)"
              >
                {{ currentDay?.data[dayIndex]?.costcenter[0]?.leaveCode }}
              </p>
            </div>
            <div
              *ngIf="
                currentDay?.data[dayIndex]?.costcenter[0]?.attendanceTypeId ===
                4
              "
              style="margin-bottom: 16px; margin-top: 20px"
              [style.border-color]="
                currentDay?.data[dayIndex]?.costcenter[0]?.leaveColourCode
              "
              class="half-day-leave-border"
            >
              <div
                class="leave-border-overlap"
                [style.background]="
                  currentDay?.data[dayIndex]?.costcenter[0]?.leaveColourCode
                "
              >
                <p style="padding-left: 7px" class="half-day-text" (click)="openRejectionComment(currentDay, currentDay?.data[dayIndex]?.date, currentDay?.data[dayIndex]?.costcenter[0]?.approvers, currentDay?.data[dayIndex]?.costcenter[0]?.costCenterId, currentDay?.data[dayIndex]?.costcenter[0]?.subProjectId, currentDay?.data[dayIndex]?.costcenter[0]?.locationId, currentDay?.data[dayIndex]?.costcenter[0]?.costcentertype, -1, dayIndex)">Half Day</p>
                <mat-icon
                  style="font-size: 10px; color: white; padding-left: 2px"
                  >history</mat-icon
                >
              </div>
              <div
                class="comments-overlap"
                *ngIf="
                  (currentDay?.data[dayIndex]?.costcenter[0]?.msg != '' &&
                    currentDay?.data[dayIndex]?.costcenter[0]?.msg != null) ||
                  (basicTsConfig?.display_hours_splitup == '1' &&
                    currentDay?.data[dayIndex]?.costcenter[0]?.hours_splitup &&
                    currentDay?.data[dayIndex]?.costcenter[0]?.hours_splitup
                      .length > 0)
                "
                (click)="
                  openCommentsDialog(
                    currentDay?.data[dayIndex]?.costcenter[0],
                    currentDay?.data[dayIndex]?.date
                  )
                "
              >
                <mat-icon
                  style="font-size: 18px; color: #45546e; cursor: pointer"
                  >chat_bubble</mat-icon
                >
              </div>
              <div style="display: flex">
                <p
                  [style.color]="
                    currentDay?.data[dayIndex]?.costcenter[0]?.leaveColourCode
                  "
                  style="padding-right: 6px"
                  class="cl-text"
                >
                  {{ currentDay?.data[dayIndex]?.costcenter[0]?.leaveCode }}
                </p>
                <p
                  *ngIf="
                    currentDay?.data[dayIndex]?.costcenter[0]?.hoursworked !=
                    '00:00'
                  "
                  class="hours-worked-text-on-leave"
                >
                  {{
                    basicTsConfig?.overtime_allowed_in_ts == "1"
                      ? (currentDay?.data[dayIndex]?.costcenter[0]?.hoursworked
                        | getRegularAndOvertimeHoursInDetailView
                          : currentDay?.data[dayIndex]?.costcenter[0]
                              ?.overtimeHours
                        | hoursWorkedSplit)
                      : (currentDay?.data[dayIndex]?.costcenter[0]?.hoursworked
                        | hoursWorkedSplit)
                  }}
                </p>
              </div>
            </div>
          </ng-template>
          <ng-template #hasTask>
            <p
              class="small-light-text"
              style="padding-top: 22px"
              [ngStyle]="{
                'margin-bottom': !currentDay.isExpanded ? '26px' : ''
              }"
            >
              {{
                currentDay?.data[dayIndex]?.costcenter[0]?.tasks
                  | totalHoursCcPerDay : basicTsConfig?.overtime_allowed_in_ts
              }}
            </p>
            <ng-container
              *ngFor="
                let task of currentDay?.data[dayIndex]?.costcenter[0]?.tasks;
                let taskIndex = index
              "
            >
              <ng-container>
                <div
                  class="hours-worked-border"
                  *ngIf="
                    [1, 7, 8].includes(task?.attendanceTypeId) &&
                    currentDay.isExpanded
                  "
                  [ngStyle]="{
                    'margin-bottom':
                      taskIndex ===
                      currentDay?.data[dayIndex]?.costcenter[0]?.tasks.length -
                        1
                        ? '26px'
                        : '0px',
                    border:
                      basicTsConfig?.overtime_allowed_in_ts == '1' &&
                      (task?.overtimeHours | overtimeColorStamp)
                        ? '1px solid #FF3A46'
                        : '1px solid #dadce2'
                  }"
                >
                  <div
                    class="comments-overlap"
                    *ngIf="
                      (task?.msg != '' && task?.msg != null) ||
                      (basicTsConfig?.display_hours_splitup == '1' &&
                        task?.hours_splitup &&
                        task?.hours_splitup.length > 0)
                    "
                    (click)="
                      openCommentsDialog(task, currentDay?.data[dayIndex]?.date)
                    "
                  >
                    <mat-icon
                      style="font-size: 18px; color: #45546e; cursor: pointer"
                      >chat_bubble</mat-icon
                    >
                  </div>
                  <p
                    class="hours-worked-text"
                    [ngStyle]="{
                      color:
                        basicTsConfig?.overtime_allowed_in_ts == '1' &&
                        (task?.overtimeHours | overtimeColorStamp)
                          ? '#FF3A46'
                          : '#6e7b8f'
                    }"
                    (click)="openRejectionComment(currentDay, currentDay?.data[dayIndex]?.date, currentDay?.data[dayIndex]?.costcenter[0]?.approvers, currentDay?.data[dayIndex]?.costcenter[0]?.costCenterId, currentDay?.data[dayIndex]?.costcenter[0]?.subProjectId, currentDay?.data[dayIndex]?.costcenter[0]?.locationId, currentDay?.data[dayIndex]?.costcenter[0]?.costcentertype, currentDay?.data[dayIndex]?.costcenter[0]?.tasks[taskIndex]?.taskId, dayIndex)"
                  >
                    {{
                      basicTsConfig?.overtime_allowed_in_ts == "1"
                        ? (task?.hoursworked
                          | getRegularAndOvertimeHoursInDetailView
                            : task?.overtimeHours
                          | hoursWorkedSplit)
                        : (task?.hoursworked | hoursWorkedSplit)
                    }}
                  </p>
                </div>
                <div
                  class="hours-worked-border"
                  *ngIf="task?.attendanceTypeId === 5 && currentDay.isExpanded"
                  [ngStyle]="{
                    'margin-bottom':
                      taskIndex ===
                      currentDay?.data[dayIndex]?.costcenter[0]?.tasks.length -
                        1
                        ? '26px'
                        : '0px'
                  }"
                >
                  <div
                    class="comments-overlap"
                    *ngIf="
                      (task?.msg != '' && task?.msg != null) ||
                      (basicTsConfig?.display_hours_splitup == '1' &&
                        task?.hours_splitup &&
                        task?.hours_splitup.length > 0)
                    "
                    (click)="
                      openCommentsDialog(task, currentDay?.data[dayIndex]?.date)
                    "
                  >
                    <mat-icon
                      style="font-size: 18px; color: #45546e; cursor: pointer"
                      >chat_bubble</mat-icon
                    >
                  </div>
                  <p class="hours-worked-text"  (click)="openRejectionComment(currentDay, currentDay?.data[dayIndex]?.date, currentDay?.data[dayIndex]?.costcenter[0]?.approvers, currentDay?.data[dayIndex]?.costcenter[0]?.costCenterId, currentDay?.data[dayIndex]?.costcenter[0]?.subProjectId, currentDay?.data[dayIndex]?.costcenter[0]?.locationId, currentDay?.data[dayIndex]?.costcenter[0]?.costcentertype, currentDay?.data[dayIndex]?.costcenter[0]?.tasks[taskIndex]?.taskId, dayIndex)">WO</p>
                </div>
                <div
                  class="full-day-leave-border"
                  *ngIf="task?.attendanceTypeId === 2 && currentDay.isExpanded"
                  [ngStyle]="{
                    'margin-bottom':
                      taskIndex ===
                      currentDay?.data[dayIndex]?.costcenter[0]?.tasks.length -
                        1
                        ? '26px'
                        : '0px'
                  }"
                  [style.border-color]="task?.leaveColourCode"
                >
                  <div
                    class="comments-overlap"
                    *ngIf="
                      (task?.msg != '' && task?.msg != null) ||
                      (basicTsConfig?.display_hours_splitup == '1' &&
                        task?.hours_splitup &&
                        task?.hours_splitup.length > 0)
                    "
                    (click)="
                      openCommentsDialog(task, currentDay?.data[dayIndex]?.date)
                    "
                  >
                    <mat-icon
                      style="font-size: 18px; color: #45546e; cursor: pointer"
                      >chat_bubble</mat-icon
                    >
                  </div>
                  <p [style.color]="task?.leaveColourCode" class="leave-text"  (click)="openRejectionComment(currentDay, currentDay?.data[dayIndex]?.date, currentDay?.data[dayIndex]?.costcenter[0]?.approvers, currentDay?.data[dayIndex]?.costcenter[0]?.costCenterId, currentDay?.data[dayIndex]?.costcenter[0]?.subProjectId, currentDay?.data[dayIndex]?.costcenter[0]?.locationId, currentDay?.data[dayIndex]?.costcenter[0]?.costcentertype, currentDay?.data[dayIndex]?.costcenter[0]?.tasks[taskIndex]?.taskId, dayIndex)">
                    {{ task?.leaveCode }}
                  </p>
                </div>
                <div
                  *ngIf="task?.attendanceTypeId === 4 && currentDay.isExpanded"
                  [ngStyle]="{
                    'margin-bottom':
                      taskIndex ===
                      currentDay?.data[dayIndex]?.costcenter[0]?.tasks.length -
                        1
                        ? '26px'
                        : '0px'
                  }"
                  [style.border-color]="task?.leaveColourCode"
                  class="half-day-leave-border"
                >
                  <div
                    class="leave-border-overlap"
                    [style.background]="task?.leaveColourCode"
                  >
                    <p style="padding-left: 7px" class="half-day-text">
                      Half Day
                    </p>
                    <mat-icon
                      style="font-size: 10px; color: white; padding-left: 2px"
                      >history</mat-icon
                    >
                  </div>
                  <div
                    class="comments-overlap"
                    *ngIf="
                      (task?.msg != '' && task?.msg != null) ||
                      (basicTsConfig?.display_hours_splitup == '1' &&
                        task?.hours_splitup &&
                        task?.hours_splitup.length > 0)
                    "
                    (click)="
                      openCommentsDialog(task, currentDay?.data[dayIndex]?.date)
                    "
                  >
                    <mat-icon
                      style="font-size: 18px; color: #45546e; cursor: pointer"
                      >chat_bubble</mat-icon
                    >
                  </div>
                  <div style="display: flex">
                    <p
                      [style.color]="task?.leaveColourCode"
                      style="padding-right: 6px"
                      class="cl-text"
                    >
                      {{ task?.leaveCode }}
                    </p>
                    <p
                      *ngIf="task?.hoursworked != '00:00'"
                      class="hours-worked-text-on-leave"
                      (click)="openRejectionComment(currentDay, currentDay?.data[dayIndex]?.date, currentDay?.data[dayIndex]?.costcenter[0]?.approvers, currentDay?.data[dayIndex]?.costcenter[0]?.costCenterId, currentDay?.data[dayIndex]?.costcenter[0]?.subProjectId, currentDay?.data[dayIndex]?.costcenter[0]?.locationId, currentDay?.data[dayIndex]?.costcenter[0]?.costcentertype, currentDay?.data[dayIndex]?.costcenter[0]?.tasks[taskIndex]?.taskId, dayIndex)"
                    >
                      {{
                        basicTsConfig?.overtime_allowed_in_ts == "1"
                          ? (task?.hoursworked
                            | getRegularAndOvertimeHoursInDetailView
                              : task?.overtimeHours
                            | hoursWorkedSplit)
                          : (task?.hoursworked | hoursWorkedSplit)
                      }}
                    </p>
                  </div>
                </div>
              </ng-container>
            </ng-container>
          </ng-template>
        </ng-container>
      </div>
      <mat-divider [vertical]="true" class="vertical-divider"></mat-divider>
    </ng-container>
    <div
      class="col"
      style="display: flex; flex-direction: column; align-items: center"
    >
      <div class="total-width">
        <p class="light-text">Total</p>
      </div>
      <ng-container *ngFor="let week of weeksOfMonth?.weeks; let i = index">
        <div
          *ngIf="
            week?.data[(week?.data | getStartDayIndex)]?.costcenter[0]?.tasks;
            then hasTaskTotal;
            else noTaskTotal
          "
        ></div>
        <ng-template #noTaskTotal>
          <div>
            <p
              class="small-light-text-m0"
              [ngStyle]="{ 'margin-top': i === 0 ? '25px' : '72px' }"
            >
              {{
                week?.data
                  | totalHoursWeekly
                    : -1
                    : basicTsConfig?.overtime_allowed_in_ts
              }}
            </p>
          </div>
        </ng-template>
        <ng-template #hasTaskTotal>
          <ng-container
            *ngFor="
              let taskTime of week?.data[(week?.data | getStartDayIndex)]
                ?.costcenter[0]?.tasks;
              let i2 = index
            "
          >
            <div *ngIf="i2 === 0">
              <p
                class="small-light-text-m0"
                style="margin-bottom: 4px"
                [ngStyle]="{
                  'margin-top':
                    i === 0
                      ? '22px'
                      : weeksOfMonth?.weeks[i - 1].isExpanded === false
                      ? '68px'
                      : '78px'
                }"
              >
                {{
                  week?.data
                    | totalHoursWeekly
                      : -1
                      : basicTsConfig?.overtime_allowed_in_ts
                }}
              </p>
            </div>
            <div *ngIf="week.isExpanded">
              <p
                class="small-light-text-m0"
                [ngStyle]="{
                  'margin-top': i2 === 0 ? '25px' : '29px'
                }"
              >
                {{
                  week?.data
                    | totalHoursWeekly
                      : i2
                      : basicTsConfig?.overtime_allowed_in_ts
                }}
              </p>
            </div>
          </ng-container>
        </ng-template>
      </ng-container>
    </div>
  </div>
</div>

<mat-menu yPosition="below" xPosition="before" #hoursPopUp="matMenu">
  <ng-template [matMenuContent]>
    <div (click)="$event.stopPropagation()" class="hours-popup">
      <div class="hours-detail">
        <p class="hours-dheading">Hours Breakdown</p>
      </div>
      <mat-divider class="divider"></mat-divider>
      <p class="cost-center" [matTooltip]="detailedViewPayload?.cost_center">
        {{ detailedViewPayload?.cost_center }}
      </p>
      <div class="hours-dialog" style="margin-bottom: 4px">
        <p class="heading">Week</p>
        <div *ngFor="let w of weeksOfMonth?.weeks">
          <p class="week-heading">W{{ w.weekNumber }}</p>
        </div>
      </div>
      <mat-divider class="divider"></mat-divider>
      <div class="hours-dialog">
        <p class="billable" style="margin-bottom: 8px">Billable</p>
        <div *ngFor="let w of weeksOfMonth?.weeks">
          <p class="week-hrs-billable" style="margin-bottom: 8px">
            {{
              w
                | totalBillableAndNonBillableWeekWise
                  : true
                  : basicTsConfig?.overtime_allowed_in_ts
            }}
          </p>
        </div>
      </div>
      <div class="hours-dialog" style="margin-bottom: 8px">
        <p class="nonbillable">Non Billable</p>
        <div *ngFor="let w of weeksOfMonth?.weeks">
          <p class="week-hrs-nonbillable">
            {{
              w
                | totalBillableAndNonBillableWeekWise
                  : false
                  : basicTsConfig?.overtime_allowed_in_ts
            }}
          </p>
        </div>
      </div>
      <div class="hours-dialog">
        <p class="heading" style="padding-top: 2px">Submitted On</p>
        <div *ngFor="let w of weeksOfMonth?.weeks">
          <p
            class="week-heading-date"
            [matTooltip]="w.submitted_on | localTimestamp"
          >
            {{ w.submitted_on | localTimestamp }}
          </p>
        </div>
      </div>
      <mat-divider class="divider"></mat-divider>
      <p class="total-bill">
        Total Hours: {{ detailedViewPayload?.final_hours | hoursWorkedSplit }}
      </p>
    </div>
  </ng-template>
</mat-menu>
