.approvals-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  height: var(--dynamicHeight);

  ::ng-deep .main-spinner circle {
    stroke: var(--color) !important;
  }
}

.approvals-header {
  height: 64px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;

  ::ng-deep .main-spinner circle {
    stroke: var(--color) !important;
  }

  .align-items-row {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
  }

  .mr16 {
    margin-right: 16px;
  }

  .mr30 {
    margin-right: 30px;
  }

  .mr24 {
    margin-right: 24px;
  }

  .back-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    color: #111434;
    font-family: var(--fontFamily);
    font-size: 11px;
    font-weight: 400;
    border: 1px solid #111434;
    border-radius: 4px;
    padding: 8px;
    cursor: pointer;

    mat-icon {
      width: 0px !important;
      height: 15px !important;
    }

    .back-btn-icon {
      font-size: 18px;
      color: #111434;
      margin-right: 20px;
      margin-bottom: 3px;
    }
  }

  .bold-text {
    font-size: 14px;
    font-weight: 700;
    color: #111434;
    margin-bottom: 0px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  .light-text {
    font-size: 12px;
    font-weight: 400;
    color: #6e7b8f;
    margin-bottom: 0px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  .bold-text-header {
    font-size: 14px;
    font-weight: 700;
    color: #111434;
    margin-bottom: 0px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  .bold-text-name {
    font-size: 14px;
    font-weight: 700;
    color: #111434;
    margin-bottom: 0px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  .light-text-time {
    font-size: 12px;
    font-weight: 400;
    color: #6e7b8f;
    margin-bottom: 0px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  .reject-btn {
    font-family: var(--fontFamily);
    font-size: 14px;
    font-weight: 700 !important;
    color: var(--pColor1);
    background-color: white;
    border: 1px solid var(--pColor1);
    border-radius: 4px;
    padding: 8px;
  }

  .approve-btn {
    font-family: var(--fontFamily);
    color: white;
    font-size: 14px;
    font-weight: 700 !important;
    border-radius: 4px;
    background-color: var(--color);
    border: 1px solid var(--color);
    padding: 8px;
  }

  .drop-down-icon {
    font-size: 18px;
    color: #111434;
    margin-top: 26px;
  }

  .ques-icon {
    color: #45546e;
    font-size: 18px;
    padding-top: 6px;
    cursor: pointer;
  }

  .reject-btn:disabled {
    font-family: var(--fontFamily);
    font-size: 14px;
    font-weight: 700 !important;
    color: #b9c0ca;
    background-color: #e8e9ee;
    border-radius: 4px;
    border: 1px solid #e8e9ee;
    padding: 8px;
  }

  .approve-btn:disabled {
    font-family: var(--fontFamily);
    color: #b9c0ca;
    font-size: 14px;
    font-weight: 700 !important;
    border-radius: 4px;
    background-color: #e8e9ee;
    border: 1px solid #e8e9ee;
    padding: 8px;
  }
}

.align-items-row {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
}

.expansion-text {
  font-family: var(--fontFamily);
  font-size: 11px;
  font-weight: 400;
  color: #6e7b8f;
  cursor: pointer;
}

.expansion-icon {
  font-size: 18px;
  color: #6e7b8f;
  cursor: pointer;
  padding-top: 3px;
}

.approvals-content {
  height: var(--dynamicSubHeight);
  overflow-x: hidden;
  overflow-y: auto;
  display: flex;
  flex-direction: row;

  ::ng-deep .mat-checkbox-checked.mat-accent .mat-checkbox-background {
    background-color: var(--color) !important;
  }

  mat-expansion-panel {
    width: 100%;
    box-shadow: none !important;
    margin-left: 0%;
  }

  .vertical-divider {
    color: #e8e9ee;
    height: var(--dividerHeight);
  }
}

.week-border {
  width: 88%;
  min-height: 24px;
  max-height: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-direction: row;
  padding-left: 4px;
  border-radius: 4px;

  .week-text {
    font-family: var(--fontFamily);
    font-size: 12px;
    font-weight: 400;
    color: #6e7b8f;
  }

  .week-more-vert {
    font-size: 15px;
    color: #45546e;
    margin-top: 10px;
    cursor: pointer;
  }
}

.panel ::ng-deep .mat-expansion-panel-header-description {
  font-family: var(--fontFamily);
  display: flex !important;
  justify-content: flex-end !important;
  margin-right: 0px !important;
  align-items: center !important;
}

.panel-content ::ng-deep .mat-expansion-panel-content {
  display: none;
}

.panel ::ng-deep .mat-expansion-panel-header {
  padding: 0px !important;
  height: 36px !important;
}

.panel ::ng-deep .mat-expansion-panel-header:hover {
  background: none !important;
}

.panel ::ng-deep .mat-expansion-panel-header .mat-expanded {
  height: 36px !important;
}

::ng-deep .expansion-panel-cc-name {
  font-family: var(--fontFamily) !important;
  font-size: 12px !important;
  font-weight: 700 !important;
  color: #45546e !important;
  margin-right: 0 !important;
  align-items: center !important;
  text-transform: capitalize;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  height: 20px;
}

::ng-deep .expansion-panel-subcc-name {
  font-family: var(--fontFamily) !important;
  font-size: 12px !important;
  font-weight: 400 !important;
  color: #8b95a5 !important;
  margin-right: 0 !important;
  align-items: center !important;
  text-transform: capitalize;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  height: 20px;
}

.panel ::ng-deep .mat-expansion-panel-header-description {
  font-family: var(--fontFamily);
  display: flex !important;
  justify-content: flex-end !important;
  margin-right: 0px !important;
  align-items: center !important;
}

.panel ::ng-deep .mat-expansion-panel-body {
  padding-left: 49px !important;
  padding-top: 8px !important;
  padding-bottom: 0px !important;
  padding-right: 0px !important;
}

.panel ::ng-deep .mat-expansion-panel-spacing {
  margin-top: 10px !important;
}

.panel {
  mat-icon {
    width: 0px !important;
    height: 0px !important;
  }
}

.expand-icon {
  font-size: 15px;
  color: #6e7b8f;
  margin-top: 6px;
  cursor: pointer;
  margin-left: 24px;
  margin-right: 25px;
}

.contract-icon {
  font-size: 15px;
  color: #6e7b8f;
  margin-top: 6px;
  cursor: pointer;
  margin-left: 24px;
  margin-right: 25px;
}

.task-name-text {
  font-family: var(--fontFamily);
  font-size: 12px;
  font-weight: 400;
  color: #45546e;
  margin: 0px;
  height: 19px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  text-transform: capitalize;
}

.task-url-text {
  font-family: var(--fontFamily);
  font-size: 12px;
  font-weight: 400;
  color: #8b95a5;
  margin: 0px;
  height: 19px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.checkbox-alignment {
  margin-top: 5px;
  margin-right: 10px;
}

.date-border {
  width: 72px;
  min-height: 24px;
  max-height: 24px;
  border-radius: 4px;
  font-family: var(--fontFamily);
  font-size: 12px;
  font-weight: 400;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #6e7b8f;
}

.last-working-day-border {
  width: 72px;
  min-height: 24px;
  max-height: 24px;
  border-radius: 4px;
  font-family: var(--fontFamily);
  font-size: 12px;
  font-weight: 400;
  display: flex;
  align-items: center;
  justify-content: center;
  align-items: center;
  position: relative;
  color: #6e7b8f;

  .last-working-day-border-overlap {
    width: 58px;
    height: 12px;
    background-color: #EE4961;
    position: absolute;
    top: 0%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1;
    border-radius: 4px;
    display: flex;
    align-items: center;
  }

  .last-working-day-text {
    font-size: 9px;
    font-weight: 400;
    margin: 0;
    text-align: center;
    color: white;
  }

  .hours-worked-text-on-last-working-day {
    font-size: 12px;
    font-weight: 400;
    color: #6e7b8f;
    margin: 0;
    margin-top: 5px;
    padding-left: 4px;
  }

}

.leave-day-border {
  width: 72px;
  min-height: 24px;
  max-height: 24px;
  border-radius: 4px;
  font-family: var(--fontFamily);
  font-size: 12px;
  font-weight: 400;
  display: flex;
  align-items: center;
  justify-content: center;
  align-items: center;
  position: relative;
  color: #6e7b8f;

  .leave-day-border-overlap {
    width: 58px;
    height: 12px;
    background-color: #eb3b5a;
    position: absolute;
    top: 0%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1;
    border-radius: 4px;
    display: flex;
    align-items: center;
  }

  .leave-day-text {
    font-size: 9px;
    font-weight: 400;
    margin: 0;
    text-align: center;
    color: white;
  }

  .hours-worked-text-on-leave-day {
    font-size: 12px;
    font-weight: 400;
    color: #6e7b8f;
    margin: 0;
    margin-top: 5px;
    padding-left: 4px;
  }

}

.hours-worked-border {
  width: 80px;
  min-height: 32px;
  max-height: 32px;
  border: 1px solid #dadce2;
  margin-top: 17px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f7f9fb;
  position: relative;
}

.hours-worked-text {
  font-family: var(--fontFamily);
  font-size: 12px;
  font-weight: 400;
  color: #6e7b8f;
  margin: 0;
}

.full-day-leave-border {
  width: 80px;
  min-height: 32px;
  max-height: 32px;
  border: 1px solid #13c2c2;
  margin-top: 17px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f7f9fb;
  position: relative;
}

.leave-text {
  font-family: var(--fontFamily);
  font-size: 12px;
  font-weight: 400;
  color: #13c2c2;
  margin: 0;
}

.half-day-leave-border {
  width: 80px;
  min-height: 32px;
  max-height: 32px;
  border: 1px solid #13c2c2;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  margin-top: 17px;
  background: #f7f9fb;

  .leave-border-overlap {
    width: 58px;
    height: 12px;
    background-color: #13c2c2;
    position: absolute;
    top: 0%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1;
    border-radius: 4px;
    display: flex;
    align-items: center;
  }

  mat-icon {
    width: 0px !important;
    height: 9px !important;
  }

  .half-day-text {
    font-size: 9px;
    font-weight: 400;
    margin: 0;
    text-align: center;
    color: white;
  }

  .cl-text {
    font-size: 12px;
    font-weight: 400;
    color: #13c2c2;
    margin: 0;
    margin-top: 5px;
  }

  .hours-worked-text-on-leave {
    font-size: 12px;
    font-weight: 400;
    color: #6e7b8f;
    margin: 0;
    margin-top: 5px;
    padding-left: 4px;
  }
}

.small-light-text {
  font-family: var(--fontFamily);
  font-size: 10px;
  font-weight: 400;
  color: #6e7b8f;
  padding-top: 16px;
  margin-bottom: 6px;
}

.comments-overlap {
  position: absolute;
  top: -22%;
  left: 88%;
  z-index: 1;
  display: flex;
  align-items: center;
}

::ng-deep .mat-menu-panel {
  min-width: 0 !important;
  min-height: 0 !important;
  max-width: none !important;
  max-height: none !important;
}

::ng-deep .mat-menu-content {
  padding: 0 !important;
}

.approve-reject-menu {
  overflow: hidden;
  padding: 16px;

  .approve-reject-menu-item {
    font-family: var(--fontFamily);
    cursor: pointer;
    font-size: 11px;
    font-weight: 400;
    color: #6e7b8f;
    margin: 0;
  }
}

.total-width {
  width: 80px;
  height: 24px;
  padding-left: 8px;
  padding-right: 8px;
  padding-top: 4px;
  padding-bottom: 4px;
  display: flex;
  justify-content: center;
  margin-left: 16px;
  margin-right: 16px;
}

.small-light-text-m0 {
  font-family: var(--fontFamily);
  font-size: 10px;
  font-weight: 400;
  color: #111434;
  margin-bottom: 0px;
}

.light-text {
  font-family: var(--fontFamily);
  font-size: 12px;
  font-weight: 400;
  color: #8b95a5;
  margin: 0;
}

.hours-popup {
  overflow: hidden;
  padding: 24px;

  .hours-detail {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    margin-left: 0;
  }

  .hours-dheading {
    font-family: var(--fontFamily);
    font-style: normal;
    font-weight: 700;
    font-size: 14px;
    line-height: 16px;
    letter-spacing: 0.02em;
    text-transform: capitalize;
    color: #111434;
  }

  .hours-dialog {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: left;
    padding: 0px;
    gap: 4px;
  }

  p {
    margin: 0px;
  }

  .cost-center {
    font-family: var(--fontFamily);
    font-style: normal;
    font-weight: 700;
    font-size: 12px;
    line-height: 16px;
    letter-spacing: 0.02em;
    text-transform: capitalize;
    color: #45546e;
    margin-bottom: 8px;
    max-width: 620px;
    height: 16px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }

  .heading {
    font-family: var(--fontFamily);
    font-style: normal;
    font-weight: 700;
    font-size: 12px;
    line-height: 16px;
    letter-spacing: 0.02em;
    text-transform: capitalize;
    color: #45546e;
    white-space: nowrap;
    width: 100px;
  }

  .week-heading {
    font-family: var(--fontFamily);
    font-style: normal;
    font-weight: 700;
    font-size: 12px;
    line-height: 16px;
    letter-spacing: 0.02em;
    text-transform: capitalize;
    color: #45546e;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    width: 100px;
    display: flex;
    justify-content: center;
    padding-left: 4px;
    padding-right: 4px;
  }

  .week-heading-date {
    font-family: var(--fontFamily);
    font-style: normal;
    font-weight: 700;
    font-size: 12px;
    height: 16px;
    letter-spacing: 0.02em;
    text-transform: capitalize;
    color: #45546e;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    width: 100px;
    padding-left: 4px;
    padding-right: 4px;
  }

  .billable {
    font-family: var(--fontFamily);
    font-style: normal;
    font-weight: 700;
    font-size: 12px;
    line-height: 16px;
    letter-spacing: 0.02em;
    text-transform: capitalize;
    white-space: nowrap;
    color: var(--color);
    width: 100px;
  }

  .nonbillable {
    font-family: var(--fontFamily);
    font-style: normal;
    font-weight: 700;
    font-size: 12px;
    line-height: 16px;
    letter-spacing: 0.02em;
    text-transform: capitalize;
    white-space: nowrap;
    color: var(--pColor1);
    width: 100px;
  }

  .week-hrs-billable {
    font-family: var(--fontFamily);
    font-style: normal;
    font-weight: 700;
    font-size: 12px;
    line-height: 16px;
    letter-spacing: 0.02em;
    text-transform: capitalize;
    color: var(--color);
    white-space: nowrap;
    display: flex;
    justify-content: center;
    width: 100px;
  }

  .week-hrs-nonbillable {
    font-family: var(--fontFamily);
    font-style: normal;
    font-weight: 700;
    font-size: 12px;
    line-height: 16px;
    letter-spacing: 0.02em;
    text-transform: capitalize;
    color: var(--pColor1);
    white-space: nowrap;
    display: flex;
    justify-content: center;
    width: 100px;
  }

  .total-bill {
    font-family: var(--fontFamily);
    font-style: normal;
    font-weight: 700;
    font-size: 12px;
    line-height: 16px;
    letter-spacing: 0.02em;
    text-transform: capitalize;
    color: #111434;
  }

  .divider {
    margin-top: 8px;
    margin-bottom: 8px;
  }
}

.overflow-div {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
