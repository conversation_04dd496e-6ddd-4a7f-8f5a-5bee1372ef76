<div class="bg-container">
  <div class="approvals" *ngIf="!detailView">
    <div class="approvals-spinner" [hidden]="!initialLoading">
      <mat-spinner class="main-spinner" diameter="40"></mat-spinner>
    </div>
    <div
      class="approvals-header"
      [hidden]="initialLoading"
      [ngStyle]="{
        'pointer-events': isApiInProgress === true ? 'none' : ''
      }"
    >
      <div style="display: flex; align-items: center; flex-direction: row">
        <div
          *ngIf="
            isHrAdmin &&
            selectedToggle == tsUiConfig['UI-TAA-BTN-001']?.config_text
          "
          class="header-icon"
          style="margin-right: 20px; cursor: pointer"
          [ngStyle]="{
            'pointer-events': isApiInProgress ? 'none' : 'auto'
          }"
        >
          <mat-icon
            (click)="resetMyTeams()"
            #myTeamsMenuTrigger="matMenuTrigger"
            [matMenuTriggerFor]="menu"
            class="supervisor"
            >group</mat-icon
          >
          <mat-menu #menu="matMenu">
            <ng-template [matMenuContent]>
              <div (click)="$event.stopPropagation()" class="my-teams-popup">
                <p class="my-team-text">My Team</p>
                <mat-form-field appearance="outline" class="search-form">
                  <input
                    (keydown.Tab)="$event.stopPropagation()"
                    matInput
                    placeholder="Search"
                    [(ngModel)]="search_param_my_team"
                    (ngModelChange)="
                          onSearchChange($event)
                        "
                  />
                  <mat-icon matSuffix class="search-icon">search</mat-icon>
                </mat-form-field>
                <div
                  *ngIf="reportingEmployeeList.length === 0"
                  style="
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    height: 256px;
                    width: 195px;
                    font-family: var(--fontFamily);
                    font-size: 12px;
                    font-weight: 600;
                    color: #45546e;
                  "
                >
                  {{
                    search_param_my_team === "" &&
                    reportingEmployeeDetailsInProgress === false
                      ? "No employee details found!"
                      : reportingEmployeeDetailsInProgress
                      ? "Please wait while employee details are being fetched!"
                      : "No employee details found!"
                  }}
                </div>
                <div
                  *ngIf="reportingEmployeeList.length > 0"
                  style="
                    display: flex;
                    flex-direction: column;
                    overflow-y: auto;
                    overflow-x: hidden;
                    max-height: 256px;
                  "
                  infinite-scroll
                  [infiniteScrollDistance]="0.1"
                  [scrollWindow]="false"
                  (scrolled)="onScrollGetEmployees()"
                >
                  <div
                    *ngFor="let items of reportingEmployeeList; let i = index"
                    style="
                      display: flex;
                      flex-direction: row;
                      align-items: center;
                    "
                    class="hover-color"
                    (click)="
                      employeeSwitchMyTeams(i); myTeamsTrigger.closeMenu()
                    "
                  >
                    <div style="margin-right: 8px">
                      <app-user-image
                        [oid]="items?.oid"
                        imgWidth="24px"
                        imgHeight="24px"
                      ></app-user-image>
                    </div>
                    <div>
                      <p
                        class="my-teams-name"
                        [matTooltip]="
                          items?.employee_name ? items.employee_name : ''
                        "
                      >
                        {{ items?.employee_name ? items.employee_name : "-" }}
                      </p>
                      <p
                        class="my-teams-desc"
                        [matTooltip]="items?.status ? items.status : ''"
                      >
                        {{ items?.status ? items.status : "-" }}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </ng-template>
          </mat-menu>
        </div>
        <div
          style="margin-right: 10px"
          *ngIf="myTeamsSubText != '' && myTeamsMainText != ''"
        >
          <app-user-image [id]="oid" imgWidth="29px" imgHeight="29px">
          </app-user-image>
        </div>
        <div
          *ngIf="myTeamsSubText != '' && myTeamsMainText != ''"
          style="
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            margin-right: 30px;
          "
        >
          <p class="my-team-bold-text" [matTooltip]="myTeamsMainText">
            <span class="my-team-approvals-text">Approvals Of</span>
            {{ myTeamsMainText }}
          </p>
          <p class="my-team-light-text" [matTooltip]="myTeamsSubText">
            {{ myTeamsSubText }}
          </p>
        </div>
        <mat-button-toggle-group
          (change)="handleSectionSelect($event)"
          [value]="selectedToggle"
        >
          <mat-button-toggle
            *ngFor="let types of requestToggleList"
            [value]="types.name"
            class="toggle-btn"
            [ngClass]="{
              'btn-toggle-selected': selectedToggle == types.name
            }"
          >
            {{ types.name }}</mat-button-toggle
          >
        </mat-button-toggle-group>
        <div class="chip-carousel" *ngIf="remMonthData?.length > 0 && basicTsConfig?.show_month_awaiting_approval == 1 && selectedToggle == 'Awaiting Request'">
          <span class="show-middle-text">
            Months Awaiting Approval
          </span>
          <button mat-icon-button (click)="scrollLeft()">
            <mat-icon>chevron_left</mat-icon>
          </button>
          <div class="chip-list-container" #chipContainer>
            <mat-chip-list>
              <mat-chip *ngFor="let chip of remMonthData"
                style="width: 90px; color: white; background-color: var(--color); cursor: pointer;"
                (click)="navigateToPendingApprovalMonth(chip)">
                {{ chip.month_name }} {{chip.year}}
              </mat-chip>
            </mat-chip-list>
          </div>
          <button mat-icon-button (click)="scrollRight()">
            <mat-icon>chevron_right</mat-icon>
          </button>
        </div>
      </div>
      <div
        *ngIf="aid"
        [hidden]="aid == currentUser.aid"
        [ngStyle]="{
          'pointer-events': isApiInProgress ? 'none' : 'auto'
        }"
      >
        <mat-icon
          matTooltip="Close"
          *ngIf="isHrAdmin"
          (click)="switchToCurrentEmployee()"
          class="header-close"
          >clear</mat-icon
        >
      </div>
    </div>
    <div class="approvals-content" [hidden]="initialLoading">
      <ng-container
        *ngIf="selectedToggle == tsUiConfig['UI-TAA-BTN-001']?.config_text"
      >
        <div class="align-items-row">
          <div
            class="total-count"
            *ngIf="tsUiConfig['UI-TAA-HD-004']?.is_visible"
          >
            {{ tsUiConfig["UI-TAA-HD-004"]?.config_text }}
            {{ pendingApprovalsCount }}
          </div>
          <mat-divider
            *ngIf="tsUiConfig['UI-TAA-HD-004']?.is_visible"
            [vertical]="true"
            class="menu-divider-vertical"
          ></mat-divider>
          <div *ngIf="tsUiConfig['UI-TAA-HD-001']?.is_visible">
            <mat-form-field appearance="outline">
              <input
                matInput
                placeholder="Search"
                [(ngModel)]="pendingSearchParam"
                (keyup.enter)="getPendingApprovalsInTimesheet('Search')"
              />
              <mat-icon matSuffix class="search-icon">search</mat-icon>
            </mat-form-field>
          </div>
          <mat-divider
            *ngIf="tsUiConfig['UI-TAA-HD-001']?.is_visible"
            [vertical]="true"
            class="menu-divider-vertical"
          ></mat-divider>
          <div>
            <mat-form-field appearance="outline">
              <div style="display: flex; align-items: center; height: 17px">
                <input
                  matInput
                  [matDatepicker]="dp"
                  [formControl]="month"
                  placeholder="MMM YYYY"
                  (keydown)="onKeyDownMonthSelection($event)"
                />
                <mat-datepicker-toggle [for]="dp">
                  <mat-icon matDatepickerToggleIcon class="calendar-icon"
                    >calendar_today</mat-icon
                  >
                </mat-datepicker-toggle>
                <mat-datepicker
                  #dp
                  startView="year"
                  [startAt]="startDate"
                  [selected]="selectedDate"
                  [opened]="monthSelected"
                  (monthSelected)="onSelectAwaitingRequests($event, dp)"
                >
                </mat-datepicker>
              </div>
            </mat-form-field>
          </div>
          <mat-divider
            [vertical]="true"
            class="menu-divider-vertical"
          ></mat-divider>
          <!-- <div
            (click)="openPendingRequestsUdrfModal()"
            class="menu-spacing"
            *ngIf="tsUiConfig['UI-TAA-HD-002']?.is_visible"
          >
            <mat-icon class="menu-icon">filter_list</mat-icon
            ><span class="menu-text">{{
              tsUiConfig["UI-TAA-HD-002"]?.config_text
            }}</span>
          </div> -->
          <div
           class="filter-icon"
           (click)="openFilterDialog()"
           matTooltip="Filter"
           *ngIf="tsUiConfig['UI-TAA-HD-002']?.is_visible"
           >
           <svg width="15" height="16" viewBox="0 0 15 16" fill="none">
            <path 
            d="M6.88476 15.5C6.63347 15.5 6.42322 15.4154 6.25398 15.2461C6.08475 15.0769 6.00013 14.8666 6.00013 14.6154V8.82691L0.402082 1.71541C0.209782 1.45899 0.181899 1.19232 0.318433 0.915407C0.454966 0.63849 0.685408 0.500031 1.00976 0.500031H13.9905C14.3148 0.500031 14.5452 0.63849 14.6818 0.915407C14.8183 1.19232 14.7904 1.45899 14.5981 1.71541L9.00008 8.82691V14.6154C9.00008 14.8666 8.91547 15.0769 8.74623 15.2461C8.577 15.4154 8.36674 15.5 8.11546 15.5H6.88476ZM7.50011 8.30001L12.4501 2.00001H2.55011L7.50011 8.30001Z"
            fill="#45546E"
            />
          </svg>
        </div>
          <mat-divider
            *ngIf="tsUiConfig['UI-TAA-HD-002']?.is_visible"
            [vertical]="true"
            class="menu-divider-vertical"
          ></mat-divider>
          <div
            class="menu-spacing"
            *ngIf="tsUiConfig['UI-TAA-HD-003']?.is_visible"
          >
            <mat-icon class="menu-icon">filter_list</mat-icon
            ><span class="menu-text">{{
              tsUiConfig["UI-TAA-HD-003"]?.config_text
            }}</span>
          </div>
          <mat-divider
            *ngIf="tsUiConfig['UI-TAA-HD-003']?.is_visible"
            [vertical]="true"
            class="menu-divider-vertical"
          ></mat-divider>
          <!-- <div
            style="margin-right: 10px; display: flex; flex-direction: row"
            *ngIf="_udrfService.udrfData.mainFilterArray.length > 0"
          >
            <mat-icon
              (click)="goToPreviousFilter()"
              style="margin-right: 10px; margin-top: 6px; cursor: pointer"
              >keyboard_arrow_left</mat-icon
            >
            <div
              *ngFor="
                let items of _udrfService.udrfData.mainFilterArray;
                let i = index
              "
            >
              <div
                *ngIf="
                  items.multiOptionSelectSearchValues[0] && displayedFilter == i
                "
                class="searchField"
                style="display: flex !important"
              >
                <div class="searchboxes tooltip" style="display: contents">
                  <span
                    class="searchtitle titlemargin filterfield"
                    [matTooltip]="items.filterName"
                    >{{ items.filterName }}</span
                  >
                  <span class="searchtitle boxstyle"
                    ><span
                      class="filterval"
                      [matTooltip]="items.multiOptionSelectSearchValues[0]"
                      >{{ items.multiOptionSelectSearchValues[0] }}</span
                    ><mat-icon
                      class="clearonefiltericn"
                      (click)="clearonefilter(items, i)"
                      >clear</mat-icon
                    ></span
                  >
                  <mat-icon
                    *ngIf="!dropdownflag"
                    class="dropdownfilter"
                    (click)="viewdroplist(i)"
                    >keyboard_arrow_down</mat-icon
                  >
                  <mat-icon
                    *ngIf="dropdownflag"
                    class="dropdownfilter"
                    (click)="closedroplist()"
                    >keyboard_arrow_up</mat-icon
                  >

                  <mat-card
                    class="tooltiptext dropdownborder"
                    [ngClass]="
                      dropdownflag && i == selecteddropdown
                        ? 'droplistvisible'
                        : 'droplisthidden'
                    "
                  >
                    <div
                      *ngFor="let item of items.checkboxValues; let j = index"
                    >
                      <div style="display: inline-flex">
                        <p class="dropdata">{{ item.checkboxName }}</p>
                        <mat-checkbox
                          class="example-margin"
                          [checked]="item.isCheckboxSelected"
                          (change)="
                            checkboxvalue(
                              $event.checked,
                              i,
                              j,
                              item.checkboxName,
                              item.checkboxId
                            )
                          "
                        >
                        </mat-checkbox>
                      </div>
                    </div>
                  </mat-card>
                </div>
              </div>
            </div>
            <mat-icon
              (click)="goToNextFilter()"
              style="margin-left: 10px; margin-top: 6px; cursor: pointer"
              >keyboard_arrow_right</mat-icon
            >
          </div>
          <div
            style="margin-right: 10px; margin-bottom: 7px"
            *ngIf="_udrfService.udrfData.mainFilterArray.length > 0"
          >
            <button mat-stroked-button class="clearbtn" (click)="onClear()">
              Clear
            </button>
          </div> -->
         
        </div>
        <div
        class="filter-display"
        *ngIf="internalApplicationId && !initialLoading"
        >
        <app-filter-display
          [applicationId]="applicationId"
          [internalApplicationId]="internalApplicationId"
        ></app-filter-display>
      </div>
        <ng-container
          *ngIf="
            approvalDetails.length <= 0 &&
            !pendingSearchApiInProgress &&
            !pendingSortApiInProgress
          "
        >
          <div class="search-spinner" style="flex-direction: column">
            <img [src]="tsUiConfig['UI-TAA-EMPT-ST-001']?.icon_name" />
            <p
              class="empty-state-text-1"
              *ngIf="tsUiConfig['UI-TAA-MA-001']?.is_visible"
            >
              {{ tsUiConfig["UI-TAA-MA-001"]?.config_text }}
            </p>
            <p
              class="empty-state-text-2"
              *ngIf="tsUiConfig['UI-TAA-MA-002']?.is_visible"
            >
              {{ tsUiConfig["UI-TAA-MA-002"]?.config_text }}
            </p>
          </div>
        </ng-container>
        <ng-container
          *ngIf="
            approvalDetails.length > 0 ||
            pendingSearchApiInProgress ||
            pendingSortApiInProgress
          "
        >
          <div class="align-items-row sort-icon-position">
            <div
              *ngFor="let item of awaitingRequestHeader; let i = index"
              [ngClass]="item?.class"
              style="display: flex; align-items: center; white-space: nowrap"
            >
              <div
                *ngIf="i === 0"
                style="width: 16px; height: 16px; margin-right: 8px"
              >
                <mat-checkbox
                  [(ngModel)]="pendingCheckAll"
                  [indeterminate]="onIndividualCheckboxSelected()"
                  (ngModelChange)="onChangePendingCheckAll()"
                ></mat-checkbox>
              </div>
              <div>
                <span
                  class="table-header-text"
                  [ngStyle]="{
                    color: item?.sort?.sortOrderId != 0 ? '#5F6C81' : '#b9c0ca'
                  }"
                  >{{ item?.title }}</span
                >
                <mat-icon
                  class="sort-icon"
                  [ngStyle]="{
                    color: item?.sort?.sortOrderId === 1 ? '#5F6C81' : '#b9c0ca'
                  }"
                  *ngIf="item?.showSortIcon === true"
                  (click)="sortPendingApprovals('ASC', i)"
                  >arrow_upward</mat-icon
                >
                <mat-icon
                  class="sort-icon"
                  [ngStyle]="{
                    color: item?.sort?.sortOrderId === 2 ? '#5F6C81' : '#b9c0ca'
                  }"
                  style="margin-left: 12px"
                  *ngIf="item?.showSortIcon === true"
                  (click)="sortPendingApprovals('DESC', i)"
                  >arrow_downward</mat-icon
                >
              </div>
            </div>
          </div>
          <mat-divider style="color: #e8e9ee"></mat-divider>
          <ng-container>
            <div
              class="search-spinner"
              [hidden]="
                !pendingSearchApiInProgress && !pendingSortApiInProgress
              "
            >
              <mat-spinner class="main-spinner" diameter="40"></mat-spinner>
            </div>
            <div
              #scrollContainer
              [hidden]="pendingSearchApiInProgress || pendingSortApiInProgress"
              class="align-items-column"
              infinite-scroll
              [infiniteScrollDistance]="0.1"
              [scrollWindow]="false"
              (scrolled)="getPendingApprovalsOnScroll()"
            >
              <ng-container *ngFor="let item of approvalDetails; let i = index">
                <div class="align-items-table-row" [ngStyle]="{'background-color': item?.is_delegated == 2 || item?.is_delegated == 3 ? '#f2d4cdad' : ''}">
                  <div
                    *ngIf="tsUiConfig['UI-TAA-TA-001']?.is_visible === 1"
                    style="display: flex; align-items: center"
                    class="table-content-text"
                    [class]="tsUiConfig['UI-TAA-TA-001']?.class_name"
                    [matTooltip]="item?.employee_name"
                  >
                    <div style="width: 16px; height: 16px; margin-right: 8px">
                      <mat-checkbox
                        [(ngModel)]="item.isChecked"
                        (ngModelChange)="
                          pendingCheckAll = false;
                          onIndividualCheckboxSelected();
                          calculateIndividualRequestsSelected()
                        "
                      ></mat-checkbox>
                    </div>
                    <div
                    *ngIf="basicTsConfig.ts_approval_overview_detail_config == 1"
                    (click)="getOverviewDetailsInPendingApprovals(item)"
                    >
                      <mat-icon class="keyboard-arrow-icon" style="line-height: 38px; cursor: pointer;">{{
                        item?.isExpanded === true
                          ? "keyboard_arrow_down"
                          : "keyboard_arrow_right"
                      }}</mat-icon>
                    </div>
                    <span
                      (click)="getDetailedViewOfApprovals(i)"
                      style="cursor: pointer; text-decoration: underline"
                      >{{
                        item?.employee_name ? item?.employee_name : "-"
                      }}</span
                    >
                  </div>
                  <div
                    *ngIf="tsUiConfig['UI-TAA-TA-002']?.is_visible === 1"
                    class="table-content-text"
                    [class]="tsUiConfig['UI-TAA-TA-002']?.class_name"
                    [matTooltip]="
                      item?.cost_center + ' - ' + item?.cost_center_description
                    "
                  >
                    {{ item?.cost_center }} -
                    {{ item?.cost_center_description }}
                  </div>
                  <div
                    *ngIf="tsUiConfig['UI-TAA-TA-009']?.is_visible === 1"
                    class="table-content-text"
                    [class]="tsUiConfig['UI-TAA-TA-009']?.class_name"
                    [matTooltip]="item?.sub_project_name"
                    [matTooltipDisabled]="!item?.sub_project_name"
                  >
                    {{ item?.sub_project_name ? item.sub_project_name : "-" }}
                  </div>
                  <div
                    *ngIf="tsUiConfig['UI-TAA-TA-007']?.is_visible === 1"
                    class="table-content-text"
                    [class]="tsUiConfig['UI-TAA-TA-007']?.class_name"
                    [matTooltip]="item?.location"
                  >
                    {{ item?.location ? item?.location : "-" }}
                  </div>
                  <div
                    *ngIf="tsUiConfig['UI-TAA-TA-010']?.is_visible === 1"
                    style="cursor: pointer; text-decoration: underline"
                    class="table-content-text"
                    (click)="openHoursLeavesDetailedView(i)"
                    [class]="tsUiConfig['UI-TAA-TA-010']?.class_name"
                    [matTooltip]="
                      (item?.final_hours | hoursWorkedSplit) +
                      (item?.Leaves > 0 ? ' / ' + item?.Leaves : '')
                    "
                  >
                    {{ item?.final_hours | hoursWorkedSplit }}
                    {{ item?.Leaves > 0 ? " / " + item?.Leaves : "" }}
                  </div>
                  <div
                    *ngIf="tsUiConfig['UI-TAA-TA-003']?.is_visible === 1"
                    class="table-content-text"
                    [class]="tsUiConfig['UI-TAA-TA-003']?.class_name"
                    [matTooltip]="item?.final_billable_hours | hoursWorkedSplit"
                  >
                    {{ item?.final_billable_hours | hoursWorkedSplit }}
                  </div>
                  <div
                    *ngIf="tsUiConfig['UI-TAA-TA-004']?.is_visible === 1"
                    class="table-content-text"
                    [class]="tsUiConfig['UI-TAA-TA-004']?.class_name"
                    [matTooltip]="
                      item?.final_non_billable_hours | hoursWorkedSplit
                    "
                  >
                    {{ item?.final_non_billable_hours | hoursWorkedSplit }}
                  </div>
                  <div
                    *ngIf="tsUiConfig['UI-TAA-TA-005']?.is_visible === 1"
                    class="table-content-text"
                    [class]="tsUiConfig['UI-TAA-TA-005']?.class_name"
                    [matTooltip]="item?.final_overtime_hours | hoursWorkedSplit"
                  >
                    {{ item?.final_overtime_hours | hoursWorkedSplit }}
                  </div>
                  <div
                    *ngIf="tsUiConfig['UI-TAA-TA-008']?.is_visible === 1"
                    class="table-content-text"
                    [class]="tsUiConfig['UI-TAA-TA-008']?.class_name"
                    [matTooltip]="
                      (item?.timesheet_month | monthFormat) + ' ' + item?.year
                    "
                  >
                    {{ item?.timesheet_month | monthFormat }} {{ item?.year }}
                  </div>
                  <div
                    *ngIf="tsUiConfig['UI-TAA-TA-011']?.is_visible === 1 && basicTsConfig.ts_approval_overview_detail_config == 1"
                    class="table-content-text"
                    [class]="tsUiConfig['UI-TAA-TA-011']?.class_name"
                  >
                  <mat-icon class="keyboard-arrow-icon" (click)="getAllCommentsData(item)" style="cursor: pointer; color: #8B95A5; font-size: 15px; line-height: 20px;">mark_chat_unread</mat-icon>
                  </div>
                  <div
                    *ngIf="tsUiConfig['UI-TAA-TA-006']?.is_visible === 1"
                    class="table-content-text"
                    [class]="tsUiConfig['UI-TAA-TA-006']?.class_name"
                    [matTooltip]="item?.submitted_on | localTimestamp"
                  >
                    {{
                      item?.submitted_on
                        ? (item?.submitted_on | localTimestamp)
                        : "-"
                    }}
                  </div>
                  <div
                    class="col-1 pl-0"
                    style="display: flex; justify-content: flex-end"
                  >
                    <button
                      class="reject-btn"
                      [disabled]="
                        pendingCheckAll ||
                        onIndividualCheckboxSelected() ||
                        (basicTsConfig | isTimesheetApprovalsLocked : monthEndDate)
                      "
                      (click)="onSingleCcReject(i)"
                    >
                      Reject
                    </button>
                    <button
                      class="approve-btn"
                      [disabled]="
                        pendingCheckAll ||
                        onIndividualCheckboxSelected() ||
                        (basicTsConfig | isTimesheetApprovalsLocked : monthEndDate)
                      "
                      (click)="onSingleCcApprove(i)"
                    >
                      Approve
                    </button>
                  </div>
                </div>
                <div *ngIf="item.isExpanded" style="background-color: #E8E9EE; border-radius: 7px;">
                  <div *ngFor="let approvalItem of item.approvalDetailViewData; let j = index">
                    <div  class="align-items-table-row pt-1 pb-1">
                    <div
                      style="display: flex; align-items: center"
                      class="table-content-text"
                      [class]="tsUiConfig['UI-TAA-TA-001']?.class_name"
                      [matTooltip]="approvalItem?.week"
                    >
                      <div style="width: 16px; height: 16px;" [ngStyle]="{'margin-right': basicTsConfig.ts_approval_overview_detail_config == 1 && approvalItem.activity_details_data && approvalItem.activity_details_data.length > 0 ? '8px' : '32px'}">
                      </div>
                      <!--Need to add activity scroll down-->
                      <div
                      *ngIf="basicTsConfig.ts_approval_overview_detail_config == 1 && approvalItem.activity_details_data && approvalItem.activity_details_data.length > 0 "
                      (click)="approvalItem.weekExpanded = !approvalItem.weekExpanded"
                      >
                        <mat-icon style="cursor: pointer; line-height: 40px;" class="keyboard-arrow-icon">{{
                          approvalItem?.weekExpanded === true
                            ? "keyboard_arrow_down"
                            : "keyboard_arrow_right"
                        }}</mat-icon>
                      </div>
                      <span
                        >{{
                          approvalItem?.week ? approvalItem?.week : "-"
                        }}</span
                      >
                    </div>
                    <div
                      class="col-2 pl-0 table-content-text"
                      [matTooltip]="
                      approvalItem?.sow + ' - ' + approvalItem?.sow_description
                      "
                    >
                      {{ approvalItem?.sow }} -
                      {{ approvalItem?.sow_description }}
                    </div>
                    <div
                      class="col-1 pl-0 table-content-text"
                      [matTooltip]="approvalItem?.workstream"
                      [matTooltipDisabled]="!approvalItem?.workstream"
                    >
                      {{ approvalItem?.workstream ? approvalItem.workstream : "-" }}
                    </div>
                    <div
                      class="col-1 pl-0 table-content-text"
                      [matTooltip]="approvalItem?.location"
                    >
                      {{ approvalItem?.location ? approvalItem?.location : "-" }}
                    </div>
                    <div
                      class="col-1 pl-0 table-content-text"
                      [matTooltip]="
                        (approvalItem?.total_hours) +
                        (approvalItem?.Leaves > 0 ? ' / ' + approvalItem?.Leaves : '')
                      "
                    >
                      {{ approvalItem?.total_hours }}
                      {{ approvalItem?.Leaves > 0 ? " / " + approvalItem?.Leaves : "" }}
                    </div>
                    <div
                      class="col-1 pl-0 table-content-text"
                      [matTooltip]="approvalItem?.billable_hours"
                    >
                      {{ approvalItem?.billable_hours}}
                    </div>
                    <div
                      class="col-1 pl-0 table-content-text"
                      [matTooltip]="
                      approvalItem?.non_billable_hours
                      "
                    >
                      {{ approvalItem?.non_billable_hours}}
                    </div>
                    <div
                      class="col-1 pl-0 table-content-text"
                      [matTooltip]="
                      approvalItem?.duration
                      "
                    >
                      {{ approvalItem?.duration}}
                    </div>
                    <div
                      class="col-2 pl-0"
                      style="display: flex; justify-content: flex-end"
                    >
                      <button
                        class="reject-btn"
                        [disabled]="
                          pendingCheckAll ||
                          onIndividualCheckboxSelected() ||
                          (basicTsConfig | isTimesheetApprovalsLocked : monthEndDate)
                        "
                        (click)="onSingleCcRejectWeekWise(i, j, approvalItem)"
                      >
                        Reject
                      </button>
                      <button
                        class="approve-btn"
                        [disabled]="
                          pendingCheckAll ||
                          onIndividualCheckboxSelected() ||
                          (basicTsConfig | isTimesheetApprovalsLocked : monthEndDate)
                        "
                        (click)="onSingleCcApproveWeekWise(i, j, approvalItem)"
                      >
                        Approve
                      </button>
                    </div>
                    </div>
                    <div *ngIf="approvalItem.weekExpanded">
                      <div *ngFor="let weekItem of approvalItem.activity_details_data; let k = index">
                        <div class="align-items-table-row pt-1 pb-1">
                          <div style="display: flex; align-items: center" class="table-content-text"
                            [class]="tsUiConfig['UI-TAA-TA-001']?.class_name" [matTooltip]="weekItem?.task_name">
                            <div style="width: 16px; height: 16px; margin-right: 32px">
                            </div>
                            <span style="overflow: hidden; text-overflow: ellipsis;">{{
                              weekItem?.task_name ? weekItem?.task_name : "-"
                              }}</span>
                          </div>
                          <div class="col-2 pl-0 table-content-text" [matTooltip]="
                                              weekItem?.sow + ' - ' + weekItem?.sow_description
                                              ">
                            {{ weekItem?.sow }} -
                            {{ weekItem?.sow_description }}
                          </div>
                          <div class="col-1 pl-0 table-content-text" [matTooltip]="weekItem?.workstream"
                            [matTooltipDisabled]="!weekItem?.workstream">
                            {{ weekItem?.workstream ? weekItem.workstream : "-" }}
                          </div>
                          <div class="col-1 pl-0 table-content-text" [matTooltip]="weekItem?.location">
                            {{ weekItem?.location ? weekItem?.location : "-" }}
                          </div>
                          <div class="col-1 pl-0 table-content-text" [matTooltip]="
                                                (weekItem?.total_hours) +
                                                (weekItem?.Leaves > 0 ? ' / ' + weekItem?.Leaves : '')
                                              ">
                            {{ weekItem?.total_hours }}
                            {{ weekItem?.Leaves > 0 ? " / " + weekItem?.Leaves : "" }}
                          </div>
                          <div class="col-1 pl-0 table-content-text" [matTooltip]="weekItem?.total_billable_hours">
                            {{ weekItem?.total_billable_hours}}
                          </div>
                          <div class="col-1 pl-0 table-content-text" [matTooltip]="
                                              weekItem?.total_non_billable_hours
                                              ">
                            {{ weekItem?.total_non_billable_hours}}
                          </div>
                          <div class="col-1 pl-0 table-content-text" [matTooltip]="
                                              weekItem?.duration
                                              ">
                            {{ weekItem?.duration}}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <mat-divider style="color: #e8e9ee; margin-top: 10px;"></mat-divider>
              </ng-container>
              <div
                class="scroll-spinner"
                [hidden]="!pendingScrollApiInProgress"
              >
                <mat-spinner class="main-spinner" diameter="40"></mat-spinner>
              </div>
            </div>
          </ng-container>
        </ng-container>
      </ng-container>
      <ng-container
        *ngIf="selectedToggle == tsUiConfig['UI-TAA-BTN-002']?.config_text"
      >
        <div class="align-items-row">
          <div *ngIf="tsUiConfig['UI-TAA-HD-001']?.is_visible">
            <mat-form-field appearance="outline">
              <input
                matInput
                placeholder="Search"
                [(ngModel)]="historySearchParam"
                (keyup.enter)="getTimesheetApprovalHistory('Search')"
              />
              <mat-icon matSuffix class="search-icon">search</mat-icon>
            </mat-form-field>
          </div>
          <mat-divider
            *ngIf="tsUiConfig['UI-TAA-HD-001']?.is_visible"
            [vertical]="true"
            class="menu-divider-vertical"
          ></mat-divider>
          <div>
            <mat-form-field appearance="outline">
              <div style="display: flex; align-items: center; height: 17px">
                <input
                  matInput
                  [matDatepicker]="dp"
                  [formControl]="month"
                  placeholder="MMM YYYY"
                  (keydown)="onKeyDownMonthSelection($event)"
                />
                <mat-datepicker-toggle [for]="dp">
                  <mat-icon matDatepickerToggleIcon class="calendar-icon"
                    >calendar_today</mat-icon
                  >
                </mat-datepicker-toggle>
                <mat-datepicker
                  #dp
                  startView="multi-year"
                  [startAt]="startDate"
                  [selected]="selectedDate"
                  [opened]="monthSelected"
                  (monthSelected)="onSelectPreviousRequests($event, dp)"
                >
                </mat-datepicker>
              </div>
            </mat-form-field>
          </div>
          <mat-divider
            [vertical]="true"
            class="menu-divider-vertical"
          ></mat-divider>
          <!-- <div
            class="menu-spacing"
            *ngIf="tsUiConfig['UI-TAA-HD-005']?.is_visible"
          >
            <mat-icon class="menu-icon">filter_list</mat-icon
            ><span class="menu-text">{{
              tsUiConfig["UI-TAA-HD-005"]?.config_text
            }}</span>
          </div> -->
          <div
           class="filter-icon"
           (click)="openFilterDialog()"
           matTooltip="Filter"
           *ngIf="tsUiConfig['UI-TAA-HD-005']?.is_visible"
           >
           <svg width="15" height="16" viewBox="0 0 15 16" fill="none">
            <path 
            d="M6.88476 15.5C6.63347 15.5 6.42322 15.4154 6.25398 15.2461C6.08475 15.0769 6.00013 14.8666 6.00013 14.6154V8.82691L0.402082 1.71541C0.209782 1.45899 0.181899 1.19232 0.318433 0.915407C0.454966 0.63849 0.685408 0.500031 1.00976 0.500031H13.9905C14.3148 0.500031 14.5452 0.63849 14.6818 0.915407C14.8183 1.19232 14.7904 1.45899 14.5981 1.71541L9.00008 8.82691V14.6154C9.00008 14.8666 8.91547 15.0769 8.74623 15.2461C8.577 15.4154 8.36674 15.5 8.11546 15.5H6.88476ZM7.50011 8.30001L12.4501 2.00001H2.55011L7.50011 8.30001Z"
            fill="#45546E"
            />
          </svg>
        </div>
          <mat-divider
            *ngIf="tsUiConfig['UI-TAA-HD-005']?.is_visible"
            [vertical]="true"
            class="menu-divider-vertical"
          ></mat-divider>
          <div
            class="menu-spacing"
            *ngIf="tsUiConfig['UI-TAA-HD-006']?.is_visible"
          >
            <mat-icon class="menu-icon">filter_list</mat-icon
            ><span class="menu-text">{{
              tsUiConfig["UI-TAA-HD-006"]?.config_text
            }}</span>
          </div>
          <mat-divider
            *ngIf="tsUiConfig['UI-TAA-HD-006']?.is_visible"
            [vertical]="true"
            class="menu-divider-vertical"
          ></mat-divider>
        </div>
        <div
        class="filter-display"
        *ngIf="internalApplicationId && !initialLoading"
        >
          <app-filter-display
          [applicationId]="applicationId"
          [internalApplicationId]="internalApplicationId"
          >
          </app-filter-display>
        </div>
        <ng-container
          *ngIf="
            approvalDetails.length <= 0 &&
            !historySearchApiInProgress &&
            !historySortApiInProgress
          "
        >
          <div class="search-spinner" style="flex-direction: column">
            <img [src]="tsUiConfig['UI-TAA-EMPT-ST-001']?.icon_name" />
            <p
              class="empty-state-text-1"
              *ngIf="tsUiConfig['UI-TAA-MA-001']?.is_visible"
            >
              {{ tsUiConfig["UI-TAA-MA-001"]?.config_text }}
            </p>
            <p
              class="empty-state-text-2"
              *ngIf="tsUiConfig['UI-TAA-AR-001']?.is_visible"
            >
              {{ tsUiConfig["UI-TAA-AR-001"]?.config_text }}
            </p>
          </div>
        </ng-container>
        <ng-container
          *ngIf="
            approvalDetails.length > 0 ||
            historySearchApiInProgress ||
            historySortApiInProgress
          "
        >
          <div class="align-items-row sort-icon-position">
            <div
              *ngFor="let item of previousRequestHeader; let i = index"
              [ngClass]="item?.class"
              style="display: flex; align-items: baseline; white-space: nowrap"
            >
              <span
                class="table-header-text"
                [ngStyle]="{
                  color: item?.sort?.sortOrderId != 0 ? '#5F6C81' : '#b9c0ca'
                }"
                >{{ item?.title }}</span
              >
              <mat-icon
                class="sort-icon"
                [ngStyle]="{
                  color: item?.sort?.sortOrderId === 1 ? '#5F6C81' : '#b9c0ca'
                }"
                *ngIf="item?.showSortIcon === true"
                (click)="sortApprovalHistory('ASC', i)"
                >arrow_upward</mat-icon
              >
              <mat-icon
                class="sort-icon"
                [ngStyle]="{
                  color: item?.sort?.sortOrderId === 2 ? '#5F6C81' : '#b9c0ca'
                }"
                style="margin-left: 12px"
                *ngIf="item?.showSortIcon === true"
                (click)="sortApprovalHistory('DESC', i)"
                >arrow_downward</mat-icon
              >
            </div>
          </div>
          <mat-divider style="color: #e8e9ee"></mat-divider>
          <ng-container>
            <div
              class="search-spinner"
              [hidden]="
                !historySearchApiInProgress && !historySortApiInProgress
              "
            >
              <mat-spinner class="main-spinner" diameter="40"></mat-spinner>
            </div>
            <div
              #scrollContainer
              [hidden]="historySearchApiInProgress || historySortApiInProgress"
              class="align-items-column"
              infinite-scroll
              [infiniteScrollDistance]="0.1"
              [scrollWindow]="false"
              (scrolled)="getApprovalsHistoryOnScroll()"
            >
              <ng-container *ngFor="let item of approvalDetails; let i = index">
                <div class="align-items-table-row">
                  <div
                    *ngIf="tsUiConfig['UI-TAA-PR-001']?.is_visible === 1"
                    class="table-content-text"
                    [class]="tsUiConfig['UI-TAA-PR-001']?.class_name"
                    [matTooltip]="item?.employee_name"
                    style="display: flex; align-items: center;"
                  >
                  <div
                  *ngIf="basicTsConfig.ts_approval_overview_detail_config == 1"
                  (click)="getOverviewDetailsInPreviousApprovals(item)"
                  >
                    <mat-icon class="keyboard-arrow-icon" style="line-height: 38px; cursor: pointer;">{{
                      item?.isExpanded === true
                        ? "keyboard_arrow_down"
                        : "keyboard_arrow_right"
                    }}</mat-icon>
                  </div>

                    {{ item?.employee_name ? item?.employee_name : "-" }}
                  </div>
                  <div
                    *ngIf="tsUiConfig['UI-TAA-PR-002']?.is_visible === 1"
                    class="table-content-text"
                    [class]="tsUiConfig['UI-TAA-PR-002']?.class_name"
                    [matTooltip]="
                      item?.cost_center + ' - ' + item?.cost_center_description
                    "
                  >
                    {{ item?.cost_center }} -
                    {{ item?.cost_center_description }}
                  </div>
                  <div
                    *ngIf="tsUiConfig['UI-TAA-PR-011']?.is_visible === 1"
                    class="table-content-text"
                    [class]="tsUiConfig['UI-TAA-PR-011']?.class_name"
                    [matTooltip]="item?.sub_project_name"
                    [matTooltipDisabled]="!item?.sub_project_name"
                  >
                    {{ item?.sub_project_name ? item.sub_project_name : "-" }}
                  </div>
                  <div
                    *ngIf="tsUiConfig['UI-TAA-PR-003']?.is_visible === 1"
                    class="table-content-text"
                    [class]="tsUiConfig['UI-TAA-PR-003']?.class_name"
                    [matTooltip]="item?.location"
                  >
                    {{ item?.location ? item?.location : "-" }}
                  </div>
                  <div
                    *ngIf="tsUiConfig['UI-TAA-PR-004']?.is_visible === 1"
                    class="table-content-text"
                    [class]="tsUiConfig['UI-TAA-PR-004']?.class_name"
                    [matTooltip]="item?.final_hours | hoursWorkedSplit"
                  >
                    {{ item?.final_hours | hoursWorkedSplit }}
                  </div>
                  <div
                    *ngIf="tsUiConfig['UI-TAA-PR-005']?.is_visible === 1"
                    class="table-content-text"
                    [class]="tsUiConfig['UI-TAA-PR-005']?.class_name"
                    [matTooltip]="item?.final_billable_hours | hoursWorkedSplit"
                  >
                    {{ item?.final_billable_hours | hoursWorkedSplit }}
                  </div>
                  <div
                    *ngIf="tsUiConfig['UI-TAA-PR-006']?.is_visible === 1"
                    class="table-content-text"
                    [class]="tsUiConfig['UI-TAA-PR-006']?.class_name"
                    [matTooltip]="
                      item?.final_non_billable_hours | hoursWorkedSplit
                    "
                  >
                    {{ item?.final_non_billable_hours | hoursWorkedSplit }}
                  </div>
                  <div
                    *ngIf="tsUiConfig['UI-TAA-PR-007']?.is_visible === 1"
                    class="table-content-text"
                    [class]="tsUiConfig['UI-TAA-PR-007']?.class_name"
                    [matTooltip]="item?.final_overtime_hours | hoursWorkedSplit"
                  >
                    {{ item?.final_overtime_hours | hoursWorkedSplit }}
                  </div>
                  <div
                    *ngIf="tsUiConfig['UI-TAA-PR-010']?.is_visible === 1"
                    class="table-content-text"
                    [class]="tsUiConfig['UI-TAA-PR-010']?.class_name"
                    [matTooltip]="
                      (item?.timesheet_month | monthFormat) +
                      ' ' +
                      item?.year +
                      ' Week ' +
                      item?.week
                    "
                  >
                    {{ item?.timesheet_month | monthFormat }} {{ item?.year }} -
                    Week {{ item?.week }}
                  </div>
                  <div
                    *ngIf="tsUiConfig['UI-TAA-PR-008']?.is_visible === 1"
                    class="table-content-text"
                    [class]="tsUiConfig['UI-TAA-PR-008']?.class_name"
                    [matTooltip]="item?.submitted_on | localTimestamp"
                  >
                    {{
                      item?.submitted_on
                        ? (item?.submitted_on | localTimestamp)
                        : "-"
                    }}
                  </div>
                  <div
                    *ngIf="tsUiConfig['UI-TAA-PR-009']?.is_visible === 1"
                    class="table-content-text"
                    [class]="tsUiConfig['UI-TAA-PR-009']?.class_name"
                    [matTooltip]="item?.action_on | localTimestamp"
                  >
                    {{
                      item?.action_on ? (item?.action_on | localTimestamp) : "-"
                    }}
                  </div>
                  <div
                    class="col-1 pl-0"
                    style="display: flex; justify-content: flex-end"
                  >
                    <div
                      [ngClass]="{
                        'history-approve-btn': item?.status === 'Approved',
                        'history-reject-btn': item?.status === 'Rejected'
                      }"
                    >
                      {{ item?.status }}
                    </div>
                    <mat-icon
                      [matMenuTriggerFor]="rejectMenu"
                      *ngIf="item?.status === 'Approved'"
                      class="reject-more-vert"
                      >more_vert</mat-icon
                    >
                    <mat-menu
                      #rejectMenu="matMenu"
                      yPosition="below"
                      xPosition="before"
                    >
                      <div class="reject-menu">
                        <p
                          class="reject-menu-item"
                          (click)="onRejectApprovedTimesheet(i)"
                          [ngStyle]="{
                            'pointer-events':
                              (basicTsConfig | isTimesheetApprovalsLocked : monthEndDate)
                                ? 'none'
                                : 'auto',
                            opacity:
                              (basicTsConfig | isTimesheetApprovalsLocked : monthEndDate)
                                ? '0.3'
                                : '1'
                          }"
                        >
                          Reject
                        </p>
                      </div>
                    </mat-menu>
                  </div>
                </div>
                <div *ngIf="item.isExpanded" style="background-color: #E8E9EE; border-radius: 7px;">
                  <div *ngFor="let approvalItem of item.approvalDetailViewData; let j = index">
                    <div  class="align-items-table-row pt-1 pb-1">
                    <div
                      style="display: flex; align-items: center"
                      class="table-content-text"
                      [class]="tsUiConfig['UI-TAA-PR-001']?.class_name"
                      [matTooltip]="approvalItem?.week"
                    >
                      <div style="width: 16px; height: 16px;" [ngStyle]="{'margin-right': basicTsConfig.ts_approval_overview_detail_config == 1 && approvalItem.activity_details_data && approvalItem.activity_details_data.length > 0 ? '8px' : '32px'}">
                      </div>
                      <!--Need to add activity scroll down-->
                      <div
                      *ngIf="basicTsConfig.ts_approval_overview_detail_config == 1 && approvalItem.activity_details_data && approvalItem.activity_details_data.length > 0 "
                      (click)="approvalItem.weekExpanded = !approvalItem.weekExpanded"
                      >
                        <mat-icon style="cursor: pointer; line-height: 40px;" class="keyboard-arrow-icon">{{
                          approvalItem?.weekExpanded === true
                            ? "keyboard_arrow_down"
                            : "keyboard_arrow_right"
                        }}</mat-icon>
                      </div>
                      <span
                        >{{
                          approvalItem?.week ? approvalItem?.week : "-"
                        }}</span
                      >
                    </div>
                    <div
                      [class]="tsUiConfig['UI-TAA-PR-002']?.class_name"
                      class="pl-0 table-content-text"
                      [matTooltip]="
                      approvalItem?.sow + ' - ' + approvalItem?.sow_description
                      "
                    >
                      {{ approvalItem?.sow }} -
                      {{ approvalItem?.sow_description }}
                    </div>
                    <div
                      class="col-1 pl-0 table-content-text"
                      [matTooltip]="approvalItem?.workstream"
                      [matTooltipDisabled]="!approvalItem?.workstream"
                    >
                      {{ approvalItem?.workstream ? approvalItem.workstream : "-" }}
                    </div>
                    <div
                      class="col-1 pl-0 table-content-text"
                      [matTooltip]="approvalItem?.location"
                    >
                      {{ approvalItem?.location ? approvalItem?.location : "-" }}
                    </div>
                    <div
                      class="col-1 pl-0 table-content-text"
                      [matTooltip]="
                        (approvalItem?.total_hours) +
                        (approvalItem?.Leaves > 0 ? ' / ' + approvalItem?.Leaves : '')
                      "
                    >
                      {{ approvalItem?.total_hours }}
                      {{ approvalItem?.Leaves > 0 ? " / " + approvalItem?.Leaves : "" }}
                    </div>
                    <div
                      class="col-1 pl-0 table-content-text"
                      [matTooltip]="approvalItem?.billable_hours"
                    >
                      {{ approvalItem?.billable_hours}}
                    </div>
                    <div
                      class="col-1 pl-0 table-content-text"
                      [matTooltip]="
                      approvalItem?.non_billable_hours
                      "
                    >
                      {{ approvalItem?.non_billable_hours}}
                    </div>
                    <div
                      class="col-1 pl-0 table-content-text"
                      [matTooltip]="
                      approvalItem?.duration
                      "
                    >
                      {{ approvalItem?.duration}}
                    </div>
                    <div
                    *ngIf="tsUiConfig['UI-TAA-PR-008']?.is_visible === 1"
                    class="table-content-text"
                    [class]="tsUiConfig['UI-TAA-PR-008']?.class_name"
                    [matTooltip]="item?.submitted_on | localTimestamp"
                  >
                    {{
                      item?.submitted_on
                        ? (item?.submitted_on | localTimestamp)
                        : "-"
                    }}
                  </div>
                  <div
                    *ngIf="tsUiConfig['UI-TAA-PR-009']?.is_visible === 1"
                    class="table-content-text"
                    [class]="tsUiConfig['UI-TAA-PR-009']?.class_name"
                    [matTooltip]="item?.action_on | localTimestamp"
                  >
                    {{
                      item?.action_on ? (item?.action_on | localTimestamp) : "-"
                    }}
                  </div>
                    </div>
                    <div *ngIf="approvalItem.weekExpanded">
                      <div *ngFor="let weekItem of approvalItem.activity_details_data; let k = index">
                        <div class="align-items-table-row pt-1 pb-1">
                          <div style="display: flex; align-items: center" class="table-content-text"
                            [class]="tsUiConfig['UI-TAA-TA-001']?.class_name" [matTooltip]="weekItem?.task_name">
                            <div style="width: 16px; height: 16px; margin-right: 32px">
                            </div>
                            <span style="overflow: hidden; text-overflow: ellipsis;">{{
                              weekItem?.task_name ? weekItem?.task_name : "-"
                              }}</span>
                          </div>
                          <div class="col-1 pl-0 table-content-text" [matTooltip]="
                                              weekItem?.sow + ' - ' + weekItem?.sow_description
                                              ">
                            {{ weekItem?.sow }} -
                            {{ weekItem?.sow_description }}
                          </div>
                          <div class="col-1 pl-0 table-content-text" [matTooltip]="weekItem?.workstream"
                            [matTooltipDisabled]="!weekItem?.workstream">
                            {{ weekItem?.workstream ? weekItem.workstream : "-" }}
                          </div>
                          <div class="col-1 pl-0 table-content-text" [matTooltip]="weekItem?.location">
                            {{ weekItem?.location ? weekItem?.location : "-" }}
                          </div>
                          <div class="col-1 pl-0 table-content-text" [matTooltip]="
                                                (weekItem?.total_hours) +
                                                (weekItem?.Leaves > 0 ? ' / ' + weekItem?.Leaves : '')
                                              ">
                            {{ weekItem?.total_hours }}
                            {{ weekItem?.Leaves > 0 ? " / " + weekItem?.Leaves : "" }}
                          </div>
                          <div class="col-1 pl-0 table-content-text" [matTooltip]="weekItem?.total_billable_hours">
                            {{ weekItem?.total_billable_hours}}
                          </div>
                          <div class="col-1 pl-0 table-content-text" [matTooltip]="
                                              weekItem?.total_non_billable_hours
                                              ">
                            {{ weekItem?.total_non_billable_hours}}
                          </div>
                          <div class="col-1 pl-0 table-content-text" [matTooltip]="
                                              weekItem?.duration
                                              ">
                            {{ weekItem?.duration}}
                          </div>
                          <div
                          *ngIf="tsUiConfig['UI-TAA-PR-008']?.is_visible === 1"
                          class="table-content-text"
                          [class]="tsUiConfig['UI-TAA-PR-008']?.class_name"
                          [matTooltip]="item?.submitted_on | localTimestamp"
                        >
                          {{
                            item?.submitted_on
                              ? (item?.submitted_on | localTimestamp)
                              : "-"
                          }}
                        </div>
                        <div
                          *ngIf="tsUiConfig['UI-TAA-PR-009']?.is_visible === 1"
                          class="table-content-text"
                          [class]="tsUiConfig['UI-TAA-PR-009']?.class_name"
                          [matTooltip]="item?.action_on | localTimestamp"
                        >
                          {{
                            item?.action_on ? (item?.action_on | localTimestamp) : "-"
                          }}
                        </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <mat-divider style="color: #e8e9ee" class="mt-2"></mat-divider>
              </ng-container>
              <div
                class="scroll-spinner"
                [hidden]="!historyScrollApiInProgress"
              >
                <mat-spinner class="main-spinner" diameter="40"></mat-spinner>
              </div>
            </div>
          </ng-container>
        </ng-container>
      </ng-container>
    </div>
  </div>
  <div class="approvals" *ngIf="detailView">
    <app-detail-view-of-approvals
      [detailedViewPayload]="detailedViewPayload"
      [basicTsConfig]="basicTsConfig"
      [tsUiConfig]="tsUiConfig"
      [aid]="aid"
      [oid]="oid"
      [filterConfig]="pendingRequestsFilterConfig"
      [startDate]="monthStartDate"
      [endDate]="monthEndDate"
      (closeDetailView)="toggleDetailView()"
    ></app-detail-view-of-approvals>
  </div>
</div>
