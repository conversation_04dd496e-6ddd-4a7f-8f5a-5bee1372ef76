.bg-container {
  background-color: #f1f3f8;
  overflow: hidden;

  ::ng-deep .mat-checkbox-checked.mat-accent .mat-checkbox-background {
    background-color: var(--color) !important;
  }

  ::ng-deep .mat-checkbox-indeterminate .mat-checkbox-background {
    background-color: var(--color) !important;
  }

  ::ng-deep .main-spinner circle {
    stroke: var(--color) !important;
  }

  ::ng-deep .mat-button-toggle-label-content {
    font-size: 12px !important;
    font-family: var(--fontFamily) !important;
  }

  ::ng-deep .mat-button-toggle-checked {
    background-color: var(--color) !important;
  }

  ::ng-deep .mat-form-field-appearance-outline:hover .mat-form-field-outline {
    color: var(--color) !important;
  }
}

.approvals {
  margin-left: 16px;
  margin-right: 16px;
  margin-bottom: 16px;
  padding: 4px 16px 16px 16px;
  height: var(--dynamicHeight);
  border-radius: 4px;
  background-color: white;
  border: 1px solid #e8e9ee;
}

.approvals-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  height: var(--dynamicHeight);
}

.approvals-header {
  height: 64px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.approvals-content {
  height: var(--dynamicSubHeight);
}

.toggle-btn {
  min-width: 7rem;
}

.btn-toggle-selected {
  font-size: 12px !important;
  font-family: var(--fontFamily) !important;
  background-color: var(--color) !important;
  color: var(--defColor);
}

.search-icon {
  color: #b9c0ca;
}

.align-items-row {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.align-items-column {
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  height: var(--dynamicTableHeight);
}

.table-header-text {
  font-family: var(--fontFamily);
  font-size: 10px;
  font-weight: 400;
  color: #b9c0ca;
}

.table-content-text {
  font-family: var(--fontFamily);
  font-size: 12px;
  font-weight: 400;
  color: #45546e;
  height: 20px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.reject-btn {
  border: 1px solid var(--pColor1);
  background-color: var(--defColor);
  color: var(--pColor1);
  border-radius: 4px;
  font-family: var(--fontFamily);
  font-size: 12px;
  font-weight: 700;
  padding: 3px 7px 3px 7px;
  margin-right: 8px;
}

.reject-btn:disabled {
  border: none;
  background-color: #e8e9ee;
  color: #b9c0ca;
  border-radius: 4px;
  font-family: var(--fontFamily);
  font-size: 12px;
  font-weight: 700;
  padding: 4px 8px 4px 8px;
  margin-right: 8px;
}

.approve-btn {
  border: none;
  background-color: var(--color);
  color: var(--defColor);
  border-radius: 4px;
  font-family: var(--fontFamily);
  font-size: 12px;
  font-weight: 700;
  padding: 4px 8px 4px 8px;
}

.approve-btn:disabled {
  border: none;
  background-color: #e8e9ee;
  color: #b9c0ca;
  border-radius: 4px;
  font-family: var(--fontFamily);
  font-size: 12px;
  font-weight: 700;
  padding: 4px 8px 4px 8px;
}

.align-items-center {
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: var(--dynamicSubHeight);
}

.empty-state-text-1 {
  font-family: var(--fontFamily);
  font-size: 14px;
  font-weight: 500;
  color: #45546e;
  margin-bottom: 0px;
  margin-top: 16px;
}

.empty-state-text-2 {
  font-family: var(--fontFamily);
  font-size: 12px;
  font-weight: 400;
  color: #8b95a5;
  margin-bottom: 0px;
  text-align: center;
}

.menu-divider-vertical {
  color: #e8e9ee;
  height: 32px;
  margin-left: 26px;
  margin-right: 26px;
  margin-bottom: 10px;
}

.align-items-table-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding-top: 12px;
  padding-bottom: 12px;
}

.sort-icon {
  color: #b9c0ca;
  font-size: 10px;
  cursor: pointer;
}

.sort-icon-position {
  padding-top: 10px;
  padding-bottom: 10px;

  mat-icon {
    width: 0px !important;
    height: 0px !important;
  }
}

.checkbox-alignment {
  margin-right: 5px;
  margin-top: 6px;
}

.menu-text {
  font-family: var(--fontFamily);
  font-size: 11px;
  font-weight: 400;
  color: #45546e;
}

.menu-icon {
  font-size: 16px;
  color: #45546e;
}

.menu-spacing {
  display: flex;
  align-items: center;
  gap: 5px;
  margin-bottom: 8px;
  cursor: pointer;

  mat-icon {
    width: 15px !important;
    height: 15px !important;
  }
}

.history-reject-btn {
  font-family: var(--fontFamily);
  font-size: 12px;
  font-weight: 400;
  color: #45546e;
  border: 1px solid #ff3a46;
  border-radius: 12px;
  width: 80px;
  height: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 24px;
  padding: 8px;
}

.history-approve-btn {
  font-family: var(--fontFamily);
  font-size: 12px;
  font-weight: 400;
  color: #45546e;
  border: 1px solid #52c41a;
  border-radius: 12px;
  width: 80px;
  height: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 8px;
}

.reject-more-vert {
  color: #45546e;
  cursor: pointer;
}

.reject-menu {
  overflow: hidden;
  padding: 16px;

  .reject-menu-item {
    font-family: var(--fontFamily);
    cursor: pointer;
    font-size: 11px;
    font-weight: 400;
    color: #6e7b8f;
    margin: 0;
  }
}

::ng-deep .mat-menu-panel {
  min-width: 0 !important;
  min-height: 0 !important;
  max-width: none !important;
  max-height: none !important;
}

::ng-deep .mat-menu-content {
  padding: 0 !important;
}

.scroll-spinner {
  display: flex;
  flex-direction: row;
  justify-content: center;
}

.search-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
  height: var(--dynamicTableHeight);
}

.calendar-icon {
  font-size: 16px;
}

::ng-deep .mat-calendar-arrow.mat-calendar-invert {
  display: none;
}

::ng-deep .mat-calendar-period-button {
  pointer-events: none;
}

::ng-deep .mat-datepicker-toggle {
  font-size: 10px;
}

.total-count {
  font-family: var(--fontFamily);
  font-size: 14px;
  font-weight: 600;
  color: #111434;
  margin-bottom: 3px;
}

.header-icon {
  padding: 8px;
  gap: 10px;
  width: 32px;
  height: 32px;
  background: #ffffff;
  border: 1px solid #dadce2;
  border-radius: 4px;
}

.supervisor {
  margin-left: -4.5px;
  margin-top: -4px;
  color: var(--color);
}

.my-teams-popup {
  padding: 16px;
  width: 227px;
  min-height: 368px;
  max-height: 368px;
  overflow: hidden;

  ::ng-deep
    .mat-form-field-appearance-outline.mat-form-field-can-float
    .mat-form-field-outline {
    color: var(--color) !important;
  }

  ::ng-deep
    .mat-form-field-appearance-outline.mat-focused
    .mat-form-field-outline,
  ::ng-deep .mat-form-field-appearance-outline:hover .mat-form-field-outline {
    border-color: var(--color) !important;
  }

  .hover-color:hover {
    background: #f2f2f2;
    cursor: pointer;
  }

  .my-team-text {
    font-family: var(--fontFamily);
    font-size: 11px;
    font-weight: 400;
    color: #6e7b8f;
  }

  .my-teams-name {
    font-family: var(--fontFamily);
    font-size: 12px;
    font-weight: 400;
    margin-bottom: 0px;
    color: #45546e;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    width: 163px;
  }

  .my-teams-desc {
    font-family: var(--fontFamily);
    font-size: 10px;
    font-weight: 400;
    margin-bottom: 0px;
    color: #8b95a5;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    width: 163px;
  }
}

.search-form {
  width: 200px;
}

.header-close {
  font-size: x-large;
  font-weight: 600;
  cursor: pointer;
  padding-top: 8px;
  color: #45546e;
}

.my-team-bold-text {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 0px;
  font-family: var(--fontFamily);
  color: #45546e;
}

.my-team-light-text {
  font-size: 12px;
  font-weight: 400;
  margin-bottom: 0px;
  font-family: var(--fontFamily);
  color: #8b95a5;
}

.my-team-approvals-text {
  font-size: 14px;
  font-weight: 400;
  margin-bottom: 0px;
  font-family: var(--fontFamily);
  color: #45546e;
}

.clearbtn {
  font-weight: 600;
  font-size: 12px;
  line-height: 16px;
  border: 1px solid;
  height: 34px;
  border-radius: 4px;
  border-color: #ee4961;
  background-color: #f2d4cdad;
  color: #ee4961;
}

.searchField {
  display: inline-block;
  border: solid thin #dadce2;
  border-radius: 4px;
  height: 36px;
  margin-bottom: 8px;
}

.searchboxes {
  display: flex;
  align-items: center;
}

.titlemargin {
  margin-left: 0px !important;
  margin-right: 0px !important;
}

.searchtitle {
  font-weight: 400;
  font-size: 12px;
  line-height: 16px;
  margin: auto 5px auto 5px;
  padding: 2px;
  padding-left: 10px;
  padding-right: 10px;
}

.clearonefiltericn {
  font-size: 13px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.dropdownfilter {
  font-size: 13px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  margin-top: 5px;
}

.boxstyle {
  background-color: #f2d4cdad;
  height: 1.5rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-right: 0px;
  color: #526179;
}

.filterval {
  width: 100px;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.filterfield {
  width: 57px;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.searchFieldtxt {
  outline: none;
  border: none;
  font-weight: 400;
  font-size: 12px;
  line-height: 16px;
}

.positionFieldlable {
  margin-top: 19px;
  margin-left: 4px;
  color: #a8acb2;
  font-weight: 400;
  font-size: 12px;
  line-height: 16px;
}

.positionField {
  display: inline-block;
  margin-left: 10px;
  padding-top: 5px;
  width: 147px;
  margin-right: 20px;
}

.positionFieldtxt {
  border-radius: 4px;
  height: 40px;
  width: 200px;
  border-color: #b9c0ca;
}

.tooltip {
  position: relative;
  display: inline-block;
}

.droplistvisible {
  visibility: visible !important;
}

.droplisthidden {
  visibility: hidden !important;
}

.dropdata {
  font-size: 12px;
  line-height: 16px;
  letter-spacing: 0.02em;
  text-transform: capitalize;
  color: #111434;
  width: 175px;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.example-margin {
  display: flex;
  right: 10px;
  position: absolute;
}

.dropdownborder {
  z-index: 1;
  width: 230px;
  border: 1px solid lightgrey;
  border-radius: 4px;
  margin-top: 35px;
  padding: 10px !important;
}

.tooltip .tooltiptext {
  visibility: hidden;
  background-color: #ffffff;
  border-radius: 6px;
  padding: 5px;
  padding-top: 7px;
  width: 250x;
  height: 200px;
  position: absolute;
  z-index: 2;
  border-radius: 4px;
  overflow-y: scroll;
  overflow-x: hidden;
}

.tooltip:active .tooltiptext {
  visibility: visible;
}
.chip-carousel {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 15px;
  border: 2px solid var(--color);
}

.show-middle-text {
  position: absolute;
  top: 96px;
  transform: translateX(-50%);
  background-color: white;
  padding: 0 5px;
  font-size: 14px;
}

button {
  background: none;
  border: none;
}

.chip-list-container {
  display: flex;
  overflow-x: hidden;
  scroll-behavior: smooth;
  padding: 8px;
  width: 300px;
  margin: 0 8px;
}

::ng-deep .chip-list-container .mat-chip-list-wrapper {
  flex-wrap: nowrap;
}

mat-chip {
  margin: 0 4px;
}

.filter-icon{
  cursor: pointer;
}

.filter-display {
  margin-bottom: 12px;
}