import {
  Component,
  ElementRef,
  HostListener,
  OnInit,
  ViewChild,
} from '@angular/core';
import { ToasterService } from 'src/app/services/toaster-service/toaster.service';
import { TsV2ApprovalService } from '../../services/ts-v2-approval.service';
import { LoginService } from 'src/app/services/login/login.service';
import * as moment from 'moment';
import { MatDialog } from '@angular/material/dialog';
import { ApprovalPopupComponent } from '../../lazy-loaded/approval-popup/approval-popup.component';
import { TsV2RejectPopupComponent } from '../../lazy-loaded/ts-v2-reject-popup/ts-v2-reject-popup.component';
import * as _ from 'underscore';
import { Subject, Subscription } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { FormControl } from '@angular/forms';
import {
  MomentDateAdapter,
  MAT_MOMENT_DATE_ADAPTER_OPTIONS,
} from '@angular/material-moment-adapter';
import {
  DateAdapter,
  MAT_DATE_LOCALE,
  MAT_DATE_FORMATS,
} from '@angular/material/core';
import { Observable, Observer, fromEvent, merge } from 'rxjs';
import { map, debounceTime } from 'rxjs/operators';
import { NoInternetPopupComponent } from 'src/app/modules/timesheet-v2/shared-components/no-internet-popup/no-internet-popup.component';
import { MatMenuTrigger, MatMenu } from '@angular/material/menu';
import { UdrfService } from 'src/app/services/udrf/udrf.service';
import { WeekWiseHoursLeaveDetailsComponent } from '../../lazy-loaded/week-wise-hours-leave-details/week-wise-hours-leave-details.component';
import { FilterServiceService } from 'src/app/modules/project-management/shared-lazy-loaded/components/filters-common-dialog/services/filter-service.service';

export const MY_FORMATS = {
  parse: {
    dateInput: 'MMM YYYY',
  },
  display: {
    dateInput: 'MMM YYYY',
    monthYearLabel: 'MMM YYYY',
    dateA11yLabel: 'LL',
    monthYearA11yLabel: 'MMMM YYYY',
  },
};
@Component({
  selector: 'app-ts-v2-approval-landing-page',
  templateUrl: './ts-v2-approval-landing-page.component.html',
  styleUrls: ['./ts-v2-approval-landing-page.component.scss'],
  providers: [
    {
      provide: DateAdapter,
      useClass: MomentDateAdapter,
      deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS],
    },
    { provide: MAT_DATE_FORMATS, useValue: MY_FORMATS },
  ],
})
export class TsV2ApprovalLandingPageComponent implements OnInit {
  @ViewChild('scrollContainer') scrollContainer: ElementRef;
  @ViewChild('myTeamsMenuTrigger') myTeamsTrigger;

  initialLoading: boolean = false;
  isApiInProgress: boolean = false;
  pendingSearchApiInProgress: boolean = false;
  historySearchApiInProgress: boolean = false;
  pendingScrollApiInProgress: boolean = false;
  historyScrollApiInProgress: boolean = false;
  pendingSortApiInProgress: boolean = false;
  historySortApiInProgress: boolean = false;
  pendingCheckAll: boolean = false;
  detailView: boolean = false;
  reportingEmployeeDetailsInProgress: boolean = false;

  selectedToggle: any;
  currentUser: any;
  oid: any;
  aid: any;
  tsMonthEndDate: any;
  monthStartDate: any;
  monthEndDate: any;
  basicTsConfig: any;
  approvalDialogRef: any;
  detailedViewPayload: any;
  requestToggleList: any;
  awaitingRequestHeader: any;
  previousRequestHeader: any;
  pendingApprovalsCount: any;
  internetConnectionDialogRef: any;
  isHrAdmin: number = 0;
  reportingEmployeeList = [];
  search_param_my_team = '';
  reportingEmployeeLimit = 0;
  myTeamsMainText = '';
  myTeamsSubText = '';

  tsUiConfigList = [];
  tsUiConfig = {};
  approvalDetails = [];
  errorData = [];

  pendingSearchParam: string = '';
  historySearchParam: string = '';
  pendingSortOrder: string = null;
  historySortOrder: string = null;
  pendingLimit: number = 0;
  historyLimit: number = 0;

  monthSelected = false;
  selectedDate!: Date;
  startDate: Date = new Date();

  color: string;
  pColor1: string;
  defColor: string;
  fontFamily: string;
  dynamicHeight: string;
  dynamicSubHeight: string;
  dynamicTableHeight: string;

  pendingRequestsApplicationId = 544;
  pendingRequestsFilterConfig: any;
  dropdownflag = false;
  selecteddropdown: any;
  displayedFilter = 0;

  protected _onDestroy = new Subject<void>();

  month = new FormControl(moment());

  approvalDetailViewData = [];

  remMonthData = [];

  @ViewChild('chipContainer', { read: ElementRef }) chipContainer!: ElementRef;
  
  searchInputChanged: Subject<string> = new Subject<string>();

  applicationId: number = 544;
  internalApplicationId: string = 'timesheet_pending_request';
  filterSubscription$: Subscription;
  filterData: any = {};
  filterQuery: string = '';
  appliedFilter: any = [];
  filterStateMap: {
    [key: string]: {
      appliedFilter: any[];
      filterQuery: string;
    };
  } = {};

  //Approvals table configurations assignment
  assignBackendConfig() {
    this.requestToggleList = [
      {
        id: 1,
        name: this.tsUiConfig['UI-TAA-BTN-001']?.config_text,
        isSelected: true,
      },
      {
        id: 2,
        name: this.tsUiConfig['UI-TAA-BTN-002']?.config_text,
        isSelected: false,
      },
    ];

    this.awaitingRequestHeader = [
      {
        title: this.tsUiConfig['UI-TAA-TA-001']?.config_text,
        showColumn: this.tsUiConfig['UI-TAA-TA-001']?.is_visible,
        showSortIcon: true,
        class: this.tsUiConfig['UI-TAA-TA-001']?.class_name,
        sort: {
          columnName: ['TEPD.first_name', 'TEPD.last_name'],
          basedOnColumn: null,
          basedOnColumnValue: null,
          basedOnColumnOpr: null,
          sortOrderId: 0, // NS as 0, ASC as 1 and DESC as 2
        },
      },
      {
        title: this.tsUiConfig['UI-TAA-TA-002']?.config_text,
        showColumn: this.tsUiConfig['UI-TAA-TA-002']?.is_visible,
        showSortIcon: true,
        class: this.tsUiConfig['UI-TAA-TA-002']?.class_name,
        sort: {
          columnName: [
            "(CASE WHEN TETSD.cost_centre_type != 'I' THEN MO.cost_centre  ELSE TPI.profit_center END)",
          ],
          basedOnColumn: null,
          basedOnColumnValue: null,
          basedOnColumnOpr: null,
          sortOrderId: 0,
        },
      },
      {
        title: this.tsUiConfig['UI-TAA-TA-009']?.config_text,
        showColumn: this.tsUiConfig['UI-TAA-TA-009']?.is_visible,
        showSortIcon: true,
        class: this.tsUiConfig['UI-TAA-TA-009']?.class_name,
        sort: {
          columnName: ['TPI2.description'],
          basedOnColumn: null,
          basedOnColumnValue: null,
          basedOnColumnOpr: null,
          sortOrderId: 0,
        },
      },
      {
        title: this.tsUiConfig['UI-TAA-TA-007']?.config_text,
        showColumn: this.tsUiConfig['UI-TAA-TA-007']?.is_visible,
        showSortIcon: true,
        class: this.tsUiConfig['UI-TAA-TA-007']?.class_name,
        sort: {
          columnName: ['MTOL.location'],
          basedOnColumn: null,
          basedOnColumnValue: null,
          basedOnColumnOpr: null,
          sortOrderId: 0,
        },
      },
      {
        title: this.tsUiConfig['UI-TAA-TA-010']?.config_text,
        showColumn: this.tsUiConfig['UI-TAA-TA-010']?.is_visible,
        showSortIcon: true,
        class: this.tsUiConfig['UI-TAA-TA-010']?.class_name,
        sort: {
          columnName: ['TETSD.hours', 'TETSD.minutes'],
          basedOnColumn: 'TETSD.is_active',
          basedOnColumnValue: 1,
          basedOnColumnOpr: 'SUM',
          sortOrderId: 0,
        },
      },
      {
        title: this.tsUiConfig['UI-TAA-TA-003']?.config_text,
        showColumn: this.tsUiConfig['UI-TAA-TA-003']?.is_visible,
        showSortIcon: true,
        class: this.tsUiConfig['UI-TAA-TA-003']?.class_name,
        sort: {
          columnName: ['TETSD.hours', 'TETSD.minutes'],
          basedOnColumn: 'TETSD.billing_type_id',
          basedOnColumnValue: 1,
          basedOnColumnOpr: 'SUM',
          sortOrderId: 0,
        },
      },
      {
        title: this.tsUiConfig['UI-TAA-TA-004']?.config_text,
        showColumn: this.tsUiConfig['UI-TAA-TA-004']?.is_visible,
        showSortIcon: true,
        class: this.tsUiConfig['UI-TAA-TA-004']?.class_name,
        sort: {
          columnName: ['TETSD.hours', 'TETSD.minutes'],
          basedOnColumn: 'TETSD.billing_type_id',
          basedOnColumnValue: 2,
          basedOnColumnOpr: 'SUM',
          sortOrderId: 0,
        },
      },
      {
        title: this.tsUiConfig['UI-TAA-TA-005']?.config_text,
        showColumn: this.tsUiConfig['UI-TAA-TA-005']?.is_visible,
        showSortIcon: true,
        class: this.tsUiConfig['UI-TAA-TA-005']?.class_name,
        sort: {
          columnName: ['TTOD.hours', 'TTOD.minutes'],
          basedOnColumn: 'TTOD.is_active',
          basedOnColumnValue: 1,
          basedOnColumnOpr: 'SUM',
          sortOrderId: 0,
        },
      },
      {
        title: this.tsUiConfig['UI-TAA-TA-008']?.config_text,
        showColumn: this.tsUiConfig['UI-TAA-TA-008']?.is_visible,
        showSortIcon: true,
        class: this.tsUiConfig['UI-TAA-TA-008']?.class_name,
        sort: {
          columnName: ['TETSD.date'],
          basedOnColumn: null,
          basedOnColumnValue: null,
          basedOnColumnOpr: null,
          sortOrderId: 0,
        },
      },
      {
        title: this.tsUiConfig['UI-TAA-TA-006']?.config_text,
        showColumn: this.tsUiConfig['UI-TAA-TA-006']?.is_visible,
        showSortIcon: true,
        class: this.tsUiConfig['UI-TAA-TA-006']?.class_name,
        sort: {
          columnName: ['TWI.created_on', 'TETSD.record_updated_on'],
          basedOnColumn: null,
          basedOnColumnValue: null,
          basedOnColumnOpr: null,
          sortOrderId: 0,
        },
      },
      {
        title: '',
        showColumn: 1,
        showSortIcon: false,
        class: 'col-2 pl-0',
        sort: {
          columnName: [],
          basedOnColumn: null,
          basedOnColumnValue: null,
          basedOnColumnOpr: null,
          sortOrderId: 0,
        },
      },
    ];

    this.previousRequestHeader = [
      {
        title: this.tsUiConfig['UI-TAA-PR-001']?.config_text,
        showColumn: this.tsUiConfig['UI-TAA-PR-001']?.is_visible,
        showSortIcon: true,
        class: this.tsUiConfig['UI-TAA-PR-001']?.class_name,
        sort: {
          columnName: ['TEPD.first_name', 'TEPD.last_name'],
          basedOnColumn: null,
          basedOnColumnValue: null,
          basedOnColumnOpr: null,
          sortOrderId: 0, // NS as 0, ASC as 1 and DESC as 2
        },
      },
      {
        title: this.tsUiConfig['UI-TAA-PR-002']?.config_text,
        showColumn: this.tsUiConfig['UI-TAA-PR-002']?.is_visible,
        showSortIcon: true,
        class: this.tsUiConfig['UI-TAA-PR-002']?.class_name,
        sort: {
          columnName: [
            "(CASE WHEN TETSD.cost_centre_type != 'I' THEN MO.cost_centre  ELSE TPI.profit_center END)",
          ],
          basedOnColumn: null,
          basedOnColumnValue: null,
          basedOnColumnOpr: null,
          sortOrderId: 0,
        },
      },
      {
        title: this.tsUiConfig['UI-TAA-PR-011']?.config_text,
        showColumn: this.tsUiConfig['UI-TAA-PR-011']?.is_visible,
        showSortIcon: true,
        class: this.tsUiConfig['UI-TAA-PR-011']?.class_name,
        sort: {
          columnName: ['TPI2.item_name'],
          basedOnColumn: null,
          basedOnColumnValue: null,
          basedOnColumnOpr: null,
          sortOrderId: 0,
        },
      },
      {
        title: this.tsUiConfig['UI-TAA-PR-003']?.config_text,
        showColumn: this.tsUiConfig['UI-TAA-PR-003']?.is_visible,
        showSortIcon: true,
        class: this.tsUiConfig['UI-TAA-PR-003']?.class_name,
        sort: {
          columnName: ['MTOL.location'],
          basedOnColumn: null,
          basedOnColumnValue: null,
          basedOnColumnOpr: null,
          sortOrderId: 0,
        },
      },
      {
        title: this.tsUiConfig['UI-TAA-PR-004']?.config_text,
        showColumn: this.tsUiConfig['UI-TAA-PR-004']?.is_visible,
        showSortIcon: true,
        class: this.tsUiConfig['UI-TAA-PR-004']?.class_name,
        sort: {
          columnName: ['TETSD.hours', 'TETSD.minutes'],
          basedOnColumn: 'TETSD.is_active',
          basedOnColumnValue: 1,
          basedOnColumnOpr: 'SUM',
          sortOrderId: 0,
        },
      },
      {
        title: this.tsUiConfig['UI-TAA-PR-005']?.config_text,
        showColumn: this.tsUiConfig['UI-TAA-PR-005']?.is_visible,
        showSortIcon: true,
        class: this.tsUiConfig['UI-TAA-PR-005']?.class_name,
        sort: {
          columnName: ['TETSD.hours', 'TETSD.minutes'],
          basedOnColumn: 'TETSD.billing_type_id',
          basedOnColumnValue: 1,
          basedOnColumnOpr: 'SUM',
          sortOrderId: 0,
        },
      },
      {
        title: this.tsUiConfig['UI-TAA-PR-006']?.config_text,
        showColumn: this.tsUiConfig['UI-TAA-PR-006']?.is_visible,
        showSortIcon: true,
        class: this.tsUiConfig['UI-TAA-PR-006']?.class_name,
        sort: {
          columnName: ['TETSD.hours', 'TETSD.minutes'],
          basedOnColumn: 'TETSD.billing_type_id',
          basedOnColumnValue: 2,
          basedOnColumnOpr: 'SUM',
          sortOrderId: 0,
        },
      },
      {
        title: this.tsUiConfig['UI-TAA-PR-007']?.config_text,
        showColumn: this.tsUiConfig['UI-TAA-PR-007']?.is_visible,
        showSortIcon: true,
        class: this.tsUiConfig['UI-TAA-PR-007']?.class_name,
        sort: {
          columnName: ['TTOD.hours', 'TTOD.minutes'],
          basedOnColumn: 'TTOD.is_active',
          basedOnColumnValue: 1,
          basedOnColumnOpr: 'SUM',
          sortOrderId: 0,
        },
      },
      {
        title: this.tsUiConfig['UI-TAA-PR-010']?.config_text,
        showColumn: this.tsUiConfig['UI-TAA-PR-010']?.is_visible,
        showSortIcon: true,
        class: this.tsUiConfig['UI-TAA-PR-010']?.class_name,
        sort: {
          columnName: ['TETSD.date'],
          basedOnColumn: null,
          basedOnColumnValue: null,
          basedOnColumnOpr: null,
          sortOrderId: 0,
        },
      },
      {
        title: this.tsUiConfig['UI-TAA-PR-008']?.config_text,
        showColumn: this.tsUiConfig['UI-TAA-PR-008']?.is_visible,
        showSortIcon: true,
        class: this.tsUiConfig['UI-TAA-PR-008']?.class_name,
        sort: {
          columnName: ['TWI.created_on', 'TETSD.record_updated_on'],
          basedOnColumn: null,
          basedOnColumnValue: null,
          basedOnColumnOpr: null,
          sortOrderId: 0,
        },
      },
      {
        title: this.tsUiConfig['UI-TAA-PR-009']?.config_text,
        showColumn: this.tsUiConfig['UI-TAA-PR-009']?.is_visible,
        showSortIcon: true,
        class: this.tsUiConfig['UI-TAA-PR-009']?.class_name,
        sort: {
          columnName: ['TWI.Changed_on', 'TETSD.record_updated_on'],
          basedOnColumn: null,
          basedOnColumnValue: null,
          basedOnColumnOpr: null,
          sortOrderId: 0,
        },
      },
      {
        title: '',
        showColumn: 1,
        showSortIcon: false,
        class: 'col-1 pl-0',
        sort: {
          columnName: [],
          basedOnColumn: null,
          basedOnColumnValue: null,
          basedOnColumnOpr: null,
          sortOrderId: 0,
        },
      },
    ];

    this.awaitingRequestHeader = this.awaitingRequestHeader.filter(
      (obj) => obj.showColumn === 1
    );
    this.previousRequestHeader = this.previousRequestHeader.filter(
      (obj) => obj.showColumn === 1
    );

    this.selectedToggle = this.requestToggleList[0]?.name;
    let defaultSelectedToggle = {
      value: this.selectedToggle,
    };
    this.loadSectionTab(defaultSelectedToggle);
  }

  constructor(
    private toastService: ToasterService,
    private approvalService: TsV2ApprovalService,
    private authService: LoginService,
    private _dialog: MatDialog,
    public _udrfService: UdrfService,
    private _filterService: FilterServiceService
  ) {
    this.createOnline$().subscribe((isOnline) => {
      if (!isOnline) {
        this.internetConnectionDialogRef = this._dialog.open(
          NoInternetPopupComponent,
          {
            disableClose: true,
            position: { top: '57px' },
            backdropClass: 'none',
            hasBackdrop: false,
          }
        );
      } else {
        if (
          this.internetConnectionDialogRef &&
          this.internetConnectionDialogRef.componentInstance
        ) {
          this.internetConnectionDialogRef.close();
        }
      }
    });
  }

  ngOnDestroy() {
    this.pendingCheckAll = false;
    this.onChangePendingCheckAll();
    this._onDestroy.next();
    this._onDestroy.complete();
    if (this.filterSubscription$) {
      this.filterSubscription$.unsubscribe();
    }
  }

  async ngOnInit() {
    this.initialLoading = true;
    this.calculateDynamicContentHeight();
    await this.getErrorDetails();
    await this.getTimesheetStyleConfig();
    document.documentElement.style.setProperty('--color', this.color);
    document.documentElement.style.setProperty('--defColor', this.defColor);
    document.documentElement.style.setProperty('--fontFamily', this.fontFamily);
    document.documentElement.style.setProperty('--pColor1', this.pColor1);
    this.currentUser = this.authService.getProfile().profile;
    this.oid = this.currentUser.oid;
    this.aid = this.currentUser.aid;
    await this.getMonthStartAndEndDate(moment());
    await this.getBasicTimesheetConfigurations();
    await this.getTimesheetApprovalsUIConfig();
    //await this.configureUdrfForPendingApprovals();
    await this.checkIfEmployeeIsApprovalAdmin('Initial');
    document.documentElement.style.setProperty('--color', this.color);
    document.documentElement.style.setProperty('--defColor', this.defColor);
    document.documentElement.style.setProperty('--fontFamily', this.fontFamily);
    document.documentElement.style.setProperty('--pColor1', this.pColor1);
    this.searchInputChanged.pipe(debounceTime(300)).subscribe((value: string) => {
          if (value && value.length >= 3) {
            this.reportingEmployeeList = [];
            this.checkIfEmployeeIsApprovalAdmin('Search');
          }
          else if (!value || value.length === 0) {
            this.reportingEmployeeList = [];
          }
        });
    await this.getFilterSubscription();
  }

  //Internet Connection Availability Check
  createOnline$() {
    return merge(
      fromEvent(window, 'offline').pipe(map(() => false)),
      fromEvent(window, 'online').pipe(map(() => true)),
      new Observable((sub: Observer<boolean>) => {
        sub.next(navigator.onLine);
        sub.complete();
      })
    );
  }

  @HostListener('window:resize')
  onResize() {
    this.calculateDynamicContentHeight();
  }

  //UI screen resize based on window size
  calculateDynamicContentHeight() {
    this.dynamicHeight = window.innerHeight - 112 + 'px';
    document.documentElement.style.setProperty(
      '--dynamicHeight',
      this.dynamicHeight
    );
    this.dynamicSubHeight = window.innerHeight - 176 + 'px';
    document.documentElement.style.setProperty(
      '--dynamicSubHeight',
      this.dynamicSubHeight
    );
    this.dynamicTableHeight = window.innerHeight - 322 + 'px';
    document.documentElement.style.setProperty(
      '--dynamicTableHeight',
      this.dynamicTableHeight
    );
  }

  //Get approvals history based on month filter
  async onSelectPreviousRequests(normalizedMonth: moment.Moment, dp) {
    dp.close();
    this.monthSelected = false;
    let month = moment(normalizedMonth);
    await this.getMonthStartAndEndDate(month);
    this.historyLimit = 0;
    this.historySortOrder = null;
    this.historySearchParam = '';
    this.approvalDetails = [];
    this.previousRequestHeader.forEach((obj) => {
      obj.sort.sortOrderId = 0;
    });
    let dateValue = this.month.value;
    dateValue.month(normalizedMonth.month());
    dateValue.year(normalizedMonth.year());
    this.month.setValue(dateValue);
    await this.getTimesheetApprovalHistory('Search');
  }

  async onSelectAwaitingRequests(normalizedMonth: moment.Moment, dp) {
    dp.close();
    this.monthSelected = false;
    let month = moment(normalizedMonth);
    await this.getMonthStartAndEndDate(month);
    this.approvalDetails = [];
    this.pendingLimit = 0;
    this.pendingSortOrder = null;
    this.pendingSearchParam = '';
    this.awaitingRequestHeader.forEach((obj) => {
      obj.sort.sortOrderId = 0;
    });
    this.retrieveTotalCountOfPendingApprovals();
    this.getPendingApprovalsInTimesheet('Initial');
  }

  //Selected tab
  handleSectionSelect(s) {
    this.requestToggleList.forEach((val, index) => {
      if (val.id == s.id) {
        val.isSelected = true;
      } else val.isSelected = false;
    });
    this.loadSectionTab(s);
  }

  //Load selected tab
  async loadSectionTab(e) {
    this.calculateDynamicContentHeight();
    this.selectedToggle = e.value;
    if (this.selectedToggle == this.tsUiConfig['UI-TAA-BTN-002']?.config_text) {
      this.internalApplicationId = "timesheet_previous_request";
      this.aid = this.currentUser.aid;
      this.oid = this.currentUser.oid;
      this.myTeamsMainText = '';
      this.myTeamsSubText = '';
      let dateValue = this.month.value;
      dateValue.month(moment().month());
      dateValue.year(moment().year());
      this.month.setValue(dateValue);
      await this.getMonthStartAndEndDate(moment());
      this.pendingCheckAll = false;
      this.onChangePendingCheckAll();
      this.approvalDetails = [];
      this.historyLimit = 0;
      this.historySortOrder = null;
      this.historySearchParam = '';
      this.previousRequestHeader.forEach((obj) => {
        obj.sort.sortOrderId = 0;
      });
      await this.getFilterSubscription();
     
    } else {
      this.internalApplicationId = "timesheet_pending_request";
      this.dropdownflag = false;
      this.displayedFilter = 0;
      this.approvalDetails = [];
      this.pendingLimit = 0;
      this.pendingSortOrder = null;
      this.pendingSearchParam = '';
      this.awaitingRequestHeader.forEach((obj) => {
        obj.sort.sortOrderId = 0;
      });
      let dateValue = this.month.value;
      dateValue.month(moment().month());
      dateValue.year(moment().year());
      this.month.setValue(dateValue);
      await this.getMonthStartAndEndDate(moment());
      if (!this.initialLoading) {
        await this.getFilterSubscription(); 
      }
    }
  }

  //Get month start and end date based on month end date config
  async getMonthStartAndEndDate(month: moment.Moment) {
    if (this.tsMonthEndDate === 'END') {
      this.monthStartDate = moment(month).startOf('month').format('YYYY-MM-DD');
      this.monthEndDate = moment(month).endOf('month').format('YYYY-MM-DD');
    } else {
      if (moment(month).date() > parseInt(this.tsMonthEndDate)) {
        month = moment(month).add(1, 'month').startOf('month');
      }
      this.monthStartDate = moment(month)
        .subtract(1, 'month')
        .date(parseInt(this.tsMonthEndDate) + 1)
        .format('YYYY-MM-DD');
      this.monthEndDate = moment(month)
        .date(parseInt(this.tsMonthEndDate))
        .format('YYYY-MM-DD');
    }
    this.month.setValue(moment(this.monthEndDate));
  }

  async getPendingApprovalsOnScroll() {
    this.pendingLimit += 15;
    await this.getPendingApprovalsInTimesheet('Scroll');
  }

  async getApprovalsHistoryOnScroll() {
    this.historyLimit += 15;
    await this.getTimesheetApprovalHistory('Scroll');
  }

  //Individual selection checkbox in in Awaiting Requests
  onIndividualCheckboxSelected() {
    if (this.approvalDetails.length <= 0) {
      return false;
    }
    let approvalDetailsLen = this.approvalDetails.length;
    let checkedApprovalDetailsLen = this.approvalDetails.filter(
      (element) => element.isChecked === true
    ).length;
    return (
      (this.approvalDetails.filter((element) => element.isChecked === true)
        .length > 0 &&
        !this.pendingCheckAll) ||
      (approvalDetailsLen !== checkedApprovalDetailsLen && this.pendingCheckAll)
    );
  }

  //Select all checkbox in Awaiting Requests
  onChangePendingCheckAll() {
    if (this.approvalDetails.length <= 0) {
      return;
    }
    if (this.pendingCheckAll === true) {
      this.approvalDetails.forEach((element) => (element.isChecked = true));
    } else {
      this.approvalDetails.forEach((element) => (element.isChecked = false));
    }
    this.calculateIndividualRequestsSelected();
  }

  //Calculates number of requests selected and opens dialog
  calculateIndividualRequestsSelected() {
    let count;
    if (this.pendingCheckAll) {
      count = this.pendingApprovalsCount;
    } else {
      count = this.approvalDetails.filter(
        (element) => element.isChecked === true
      ).length;
    }
    if (count > 0) {
      if (this.approvalDialogRef && this.approvalDialogRef.componentInstance) {
        this.approvalDialogRef.close();
      }
      this.approvalDialogRef = this._dialog.open(ApprovalPopupComponent, {
        disableClose: true,
        position: { bottom: '40px' },
        data: {
          count: count,
          basicTsConfig: this.basicTsConfig,
          monthEndDate: this.monthEndDate,
        },
        backdropClass: 'none',
        hasBackdrop: false,
      });
      this.approvalDialogRef.afterClosed().subscribe(async (result) => {
        return new Promise((resolve, reject) =>
          this.approvalService
            .checkRDSPeak()
            .pipe(takeUntil(this._onDestroy))
            .subscribe({
              next: async (res) => {
                if (res['messType'] == 'E') {
                  this.toastService.showInfo(
                    'Timesheet App Message',
                    res['messText'],
                    7000
                  );
                } else {
                  if (result === 'Yes') {
                    let details = this.approvalDetails.filter(
                      (element) => element.isChecked === true
                    );
                    this.approvalDetails = this.approvalDetails.filter(
                      (obj) => obj.isChecked === false
                    );
                    let bulkApprove = this.pendingCheckAll;
                    this.pendingCheckAll = false;
                    if (bulkApprove === true) {
                      this.toastService.showSuccess(
                        'Timesheet App Message',
                        'Selected Timesheet Requests has been sent for Approval Process. All the Requests will be Approved in sometime! You will receive a notification on this!',
                        7000
                      );
                    } else {
                      this.toastService.showSuccess(
                        'Timesheet App Message',
                        'Employee Timesheet Approved Successfully!',
                        7000
                      );
                    }
                    this.pendingApprovalsCount =
                      this.pendingApprovalsCount - count;
                    await this.updateTimesheetApprovalOrRejectionStatus(
                      'A',
                      [],
                      details,
                      4,
                      bulkApprove
                    );
                    await this.removeAwaitingApprovalCarousel(bulkApprove);
                  }
                }
                resolve(true);
              },
              error: (err) => {
                this.toastService.showError(
                  `Timesheet Error ${err.error.code}`,
                  _.filter(this.errorData, { error_code: err.error.code })[0]
                    ?.user_message,
                  7000
                );
                reject();
              },
            })
        );
      });
    } else {
      if (this.approvalDialogRef && this.approvalDialogRef.componentInstance) {
        this.approvalDialogRef.close();
      }
      this.pendingCheckAll = false;
    }
  }

  //Reject using reject button
  async onSingleCcReject(index: number) {
    let details = this.approvalDetails[index];
    const dialogRef = this._dialog.open(TsV2RejectPopupComponent, {
      data: { details: details, tsUiConfig: this.tsUiConfig },
    });

    dialogRef.afterClosed().subscribe(async (result) => {
      if (result && result.length > 0) {
        return new Promise((resolve, reject) =>
          this.approvalService
            .checkRDSPeak()
            .pipe(takeUntil(this._onDestroy))
            .subscribe({
              next: async (res) => {
                if (res['messType'] == 'E') {
                  this.toastService.showInfo(
                    'Timesheet App Message',
                    res['messText'],
                    7000
                  );
                } else {
                  this.approvalDetails.splice(index, 1);
                  await this.removeAwaitingApprovalCarousel(false);
                  this.toastService.showSuccess(
                    'Timesheet App Message',
                    'Employee Timesheet Rejected Successfully!',
                    7000
                  );
                  this.pendingApprovalsCount--;
                  await this.updateTimesheetApprovalOrRejectionStatus(
                    'R',
                    [result],
                    [details],
                    3,
                    false
                  );
                }
                resolve();
              },
              error: (err) => {
                this.toastService.showError(
                  `Timesheet Error ${err.error.code}`,
                  _.filter(this.errorData, { error_code: err.error.code })[0]
                    ?.user_message,
                  7000
                );
                reject();
              },
            })
        );
      }
    });
  }
  /**
   * To Reject the timesheet week wise in overview
   */
  async onSingleCcRejectWeekWise(index: number, dataIndex: number, item) {
    let details = this.approvalDetails[index];
    let temp_hours =
      this.approvalDetails[index]?.approvalDetailViewData[dataIndex]
        .total_hours;
    temp_hours = temp_hours.split(' ');
    let hours = temp_hours[0].replace('h', '');
    let mins = temp_hours[1].replace('m', '');
    this.approvalDetails[index].final_hours = `${hours}:${mins}`;
    this.approvalDetails[index].Leaves =
      this.approvalDetails[index]?.approvalDetailViewData[dataIndex].Leaves;
    const dialogRef = this._dialog.open(TsV2RejectPopupComponent, {
      data: { details: details, tsUiConfig: this.tsUiConfig },
    });

    dialogRef.afterClosed().subscribe(async (result) => {
      if (result && result.length > 0) {
        return new Promise((resolve, reject) =>
          this.approvalService
            .checkRDSPeak()
            .pipe(takeUntil(this._onDestroy))
            .subscribe({
              next: async (res) => {
                if (res['messType'] == 'E') {
                  this.toastService.showInfo(
                    'Timesheet App Message',
                    res['messText'],
                    7000
                  );
                } else {
                  this.approvalDetails[index]?.approvalDetailViewData.splice(
                    dataIndex,
                    1
                  );
                  if (
                    this.approvalDetails[index]?.approvalDetailViewData
                      ?.length == 0
                  ) {
                    this.approvalDetails.splice(index, 1);
                    await this.removeAwaitingApprovalCarousel(false);
                  }
                  this.toastService.showSuccess(
                    'Timesheet App Message',
                    'Employee Timesheet Rejected Successfully!',
                    7000
                  );
                  await this.updateTimesheetApprovalOrRejectionStatus(
                    'R',
                    [result],
                    [details],
                    3,
                    false,
                    [item.weekNo]
                  );
                }
                resolve();
              },
              error: (err) => {
                this.toastService.showError(
                  `Timesheet Error ${err.error.code}`,
                  _.filter(this.errorData, { error_code: err.error.code })[0]
                    ?.user_message,
                  7000
                );
                reject();
              },
            })
        );
      }
    });
  }

  //Approve using approve button
  async onSingleCcApprove(index: number) {
    return new Promise((resolve, reject) =>
      this.approvalService
        .checkRDSPeak()
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: async (res) => {
            if (res['messType'] == 'E') {
              this.toastService.showInfo(
                'Timesheet App Message',
                res['messText'],
                7000
              );
            } else {
              let details = this.approvalDetails[index];
              this.approvalDetails.splice(index, 1);
              await this.removeAwaitingApprovalCarousel(false);
              this.toastService.showSuccess(
                'Timesheet App Message',
                'Employee Timesheet Approved Successfully!',
                7000
              );
              this.pendingApprovalsCount--;
              await this.updateTimesheetApprovalOrRejectionStatus(
                'A',
                [],
                [details],
                4,
                false
              );
            }
            resolve(true);
          },
          error: (err) => {
            this.toastService.showError(
              `Timesheet Error ${err.error.code}`,
              _.filter(this.errorData, { error_code: err.error.code })[0]
                ?.user_message,
              7000
            );
            reject();
          },
        })
    );
  }
  /**
   * To approve the timesheet week wise in overview
   */
  async onSingleCcApproveWeekWise(index: number, dataIndex: number, item: any) {
    return new Promise((resolve, reject) =>
      this.approvalService
        .checkRDSPeak()
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: async (res) => {
            if (res['messType'] == 'E') {
              this.toastService.showInfo(
                'Timesheet App Message',
                res['messText'],
                7000
              );
            } else {
              let details = this.approvalDetails[index];
              this.approvalDetails[index]?.approvalDetailViewData.splice(
                dataIndex,
                1
              );
              if (
                this.approvalDetails[index]?.approvalDetailViewData?.length == 0
              ) {
                this.approvalDetails.splice(index, 1);
                await this.removeAwaitingApprovalCarousel(false);
              }
              this.toastService.showSuccess(
                'Timesheet App Message',
                'Employee Timesheet Approved Successfully!',
                7000
              );
              await this.updateTimesheetApprovalOrRejectionStatus(
                'A',
                [],
                [details],
                4,
                false,
                [item.weekNo]
              );
            }
            resolve(true);
          },
          error: (err) => {
            this.toastService.showError(
              `Timesheet Error ${err.error.code}`,
              _.filter(this.errorData, { error_code: err.error.code })[0]
                ?.user_message,
              7000
            );
            reject();
          },
        })
    );
  }

  //Rejecting approved timesheet in approvals history
  async onRejectApprovedTimesheet(index: number) {
    let details = this.approvalDetails[index];
    const dialogRef = this._dialog.open(TsV2RejectPopupComponent, {
      data: { details: details, tsUiConfig: this.tsUiConfig },
    });

    dialogRef.afterClosed().subscribe(async (result) => {
      if (result && result.length > 0) {
        this.approvalDetails[index].status = 'Rejected';
        this.approvalDetails[index].status_id = 3;
        this.toastService.showSuccess(
          'Timesheet App Message',
          'Employee Timesheet Rejected Successfully!',
          7000
        );
        await this.rejectApprovedTimesheet('R', [result], [details], 3);
      }
    });
  }

  //Detail View Component in Awaiting Requests
  getDetailedViewOfApprovals(index: number) {
    this.pendingCheckAll = false;
    this.onChangePendingCheckAll();
    this.detailedViewPayload = this.approvalDetails[index];
    this.detailView = true;
  }

  //Triggers when we get back to Approvals page from detail view
  toggleDetailView() {
    this.detailView = !this.detailView;
    this.approvalDetails = [];
    this.awaitingRequestHeader.forEach((obj) => {
      obj.sort.sortOrderId = 0;
    });
    this.pendingLimit = 0;
    this.pendingSortOrder = null;
    this.pendingSearchParam = '';
    this.retrieveTotalCountOfPendingApprovals();
    this.getPendingApprovalsInTimesheet('Initial');
  }

  //Sorting awaiting requests
  async sortPendingApprovals(val, index: number) {
    this.pendingSortApiInProgress = true;
    this.pendingCheckAll = false;
    this.onChangePendingCheckAll();
    if (val === 'ASC') {
      if (this.awaitingRequestHeader[index]?.sort?.sortOrderId === 1) {
        this.awaitingRequestHeader[index].sort.sortOrderId = 0;
      } else {
        this.awaitingRequestHeader[index].sort.sortOrderId = 1;
      }
    }
    if (val === 'DESC') {
      if (this.awaitingRequestHeader[index]?.sort?.sortOrderId === 2) {
        this.awaitingRequestHeader[index].sort.sortOrderId = 0;
      } else {
        this.awaitingRequestHeader[index].sort.sortOrderId = 2;
      }
    }
    this.pendingSortOrder = _.pluck(this.awaitingRequestHeader, 'sort');
    await this.getPendingApprovalsInTimesheet('Search');
  }

  //Sorting previous requests
  async sortApprovalHistory(val, index: number) {
    this.historySortApiInProgress = true;
    if (val === 'ASC') {
      if (this.previousRequestHeader[index]?.sort?.sortOrderId === 1) {
        this.previousRequestHeader[index].sort.sortOrderId = 0;
      } else {
        this.previousRequestHeader[index].sort.sortOrderId = 1;
      }
    }
    if (val === 'DESC') {
      if (this.previousRequestHeader[index]?.sort?.sortOrderId === 2) {
        this.previousRequestHeader[index].sort.sortOrderId = 0;
      } else {
        this.previousRequestHeader[index].sort.sortOrderId = 2;
      }
    }
    this.historySortOrder = _.pluck(this.previousRequestHeader, 'sort');
    await this.getTimesheetApprovalHistory('Search');
  }

  async resetMyTeams() {
    this.search_param_my_team = '';
    this.reportingEmployeeList = [];
    await this.checkIfEmployeeIsApprovalAdmin('Search');
  }

  async onScrollGetEmployees() {
    this.reportingEmployeeLimit += 15;
    await this.checkIfEmployeeIsApprovalAdmin('Scroll');
  }

  employeeSwitchMyTeams(index: number) {
    this.myTeamsMainText = this.reportingEmployeeList[index]?.employee_name;
    this.myTeamsSubText =
      (this.reportingEmployeeList[index]?.designation
        ? this.reportingEmployeeList[index]?.designation
        : 'NA') +
      ' - ' +
      (this.reportingEmployeeList[index]?.role
        ? this.reportingEmployeeList[index]?.role
        : 'NA');
    this.aid = this.reportingEmployeeList[index]?.associate_id;
    this.oid = this.reportingEmployeeList[index]?.oid;
    this.approvalDetails = [];
    this.pendingLimit = 0;
    this.pendingSortOrder = null;
    this.pendingSearchParam = '';
    this.awaitingRequestHeader.forEach((obj) => {
      obj.sort.sortOrderId = 0;
    });
    this.retrieveTotalCountOfPendingApprovals();
    this.getPendingApprovalsInTimesheet('Initial');
  }

  switchToCurrentEmployee() {
    this.aid = this.currentUser.aid;
    this.oid = this.currentUser.oid;
    this.myTeamsMainText = '';
    this.myTeamsSubText = '';
    this.approvalDetails = [];
    this.pendingLimit = 0;
    this.pendingSortOrder = null;
    this.pendingSearchParam = '';
    this.awaitingRequestHeader.forEach((obj) => {
      obj.sort.sortOrderId = 0;
    });
    this.retrieveTotalCountOfPendingApprovals();
    this.getPendingApprovalsInTimesheet('Initial');
  }

  onKeyDownMonthSelection(event: KeyboardEvent) {
    event.preventDefault();
  }

  async openPendingRequestsUdrfModal() {
    this.dropdownflag = false;
    const { UdrfModalComponent } = await import(
      'src/app/modules/shared-lazy-loaded-components/udrf-modal/udrf-modal.component'
    );
    const openUdrfModalComponent = this._dialog.open(UdrfModalComponent, {
      minWidth: '100%',
      height: '84%',
      position: { top: '0px', left: '77px' },
      disableClose: true,
    });
  }

  openHoursLeavesDetailedView(i: number) {
    this._dialog.open(WeekWiseHoursLeaveDetailsComponent, {
      width: '500px',
      disableClose: true,
      data: { payload: this.approvalDetails[i], tsUiConfig: this.tsUiConfig },
    });
  }

  async configureUdrfForPendingApprovals() {
    this._udrfService.udrfData.applicationId =
      this.pendingRequestsApplicationId;
    this._udrfService.udrfData.isItemDataLoading = true;
    this._udrfService.udrfUiData.showItemDataCount = false;
    this._udrfService.udrfUiData.showSearchBar = false;
    this._udrfService.udrfUiData.showActionButtons = true;
    this._udrfService.udrfUiData.showUdrfModalButton = false;
    this._udrfService.udrfUiData.showColumnConfigButton = false;
    this._udrfService.udrfUiData.showSettingsModalButton = false;
    this._udrfService.udrfUiData.showNewReleasesButton = false;
    this._udrfService.udrfUiData.showReportDownloadButton = false;
    this._udrfService.udrfUiData.isReportDownloading = false;
    this._udrfService.udrfUiData.itemHasOpenInNewTab = false;
    this._udrfService.udrfUiData.horizontalScroll = false;
    this._udrfService.udrfUiData.itemHasQuickCta = false;
    this._udrfService.udrfUiData.collapseAll = false;
    this._udrfService.udrfUiData.showCollapseButton = false;
    this._udrfService.udrfUiData.countForOnlyThisReport = false;
    this._udrfService.udrfUiData.toggleChecked = false;
    this._udrfService.udrfUiData.countFlag = false;
    this._udrfService.udrfUiData.isMultipleView = false;
    this._udrfService.udrfUiData.itemHasDownloadButton = false;
    this._udrfService.udrfUiData.emailPluginVisible = false;
    this._udrfService.udrfUiData.itemHasDownloadButton = false;
    this._udrfService.udrfUiData.itemHasAttachFileButton = false;
    this._udrfService.udrfUiData.itemHasComments = false;
    this._udrfService.udrfUiData.isMoreOptionsNeeded = false;
    this._udrfService.udrfUiData.completeProfileBtn = false;
    this._udrfService.udrfUiData.searchPlaceholder = '';
    this._udrfService.getAppUdrfConfig(
      this.pendingRequestsApplicationId,
      await this.initPendingRequests.bind(this)
    );
  }

  async initPendingRequests() {
    this.pendingRequestsFilterConfig = {
      mainFilterArray: this._udrfService.udrfData.mainFilterArray,
      searchTableDetails: this._udrfService.udrfData.searchTableDetails,
      txTableDetails: this._udrfService.udrfData.txTableDetails,
    };
    await this.getPendingApprovalsInTimesheet('Search');
  }

  async onClear() {
    this.dropdownflag = false;
    this.displayedFilter = 0;
    this.pendingSearchParam = '';
    await this._udrfService.udrfFunctions.clearSearchAndConfig();
    await this.initPendingRequests();
  }

  async clearonefilter(filterItem, index) {
    this._udrfService.udrfFunctions.clearItemConfigApply(filterItem, 1);
    this._udrfService.udrfData.appliedFilterTypeArray.splice(index, 1);
    this._udrfService.udrfData.mainFilterArray.splice(index, 1);
    this.displayedFilter = 0;
    this.dropdownflag = false;
  }

  viewdroplist(val: any) {
    this.dropdownflag = true;
    this.selecteddropdown = val;
  }

  closedroplist() {
    this.dropdownflag = false;
    this.selecteddropdown = -1;
  }

  async checkboxvalue(checkboxval, i, j, name, id) {
    this._udrfService.udrfData.mainFilterArray[i].checkboxValues[
      j
    ].isCheckboxSelected = checkboxval;
    if (checkboxval) {
      this._udrfService.udrfData.mainFilterArray[
        i
      ].multiOptionSelectSearchValues.push(name);
      if (this._udrfService.udrfData.mainFilterArray[i].isIdBased)
        this._udrfService.udrfData.mainFilterArray[
          i
        ].multiOptionSelectSearchValuesWithId.push(id);
    } else {
      //this.udrfService.udrfData.mainFilterArray[i].multiOptionSelectSearchValues.splice()
      this._udrfService.udrfData.mainFilterArray[
        i
      ].multiOptionSelectSearchValues.forEach((item, index) => {
        if (item === name) {
          this._udrfService.udrfData.mainFilterArray[
            i
          ].multiOptionSelectSearchValues.splice(index, 1);
          if (this._udrfService.udrfData.mainFilterArray[i].isIdBased)
            this._udrfService.udrfData.mainFilterArray[
              i
            ].multiOptionSelectSearchValuesWithId.splice(index, 1);
        }
      });
    }
    if (
      this._udrfService.udrfData.mainFilterArray[i]
        .multiOptionSelectSearchValues.length == 0
    ) {
      this.dropdownflag = false;
      this.displayedFilter = 0;
      this.clearonefilter(this._udrfService.udrfData.mainFilterArray[i], i);
    } else {
      await this.getPendingApprovalsInTimesheet('Search');
    }
  }

  goToPreviousFilter() {
    this.dropdownflag = false;
    if (this.displayedFilter == 0) {
      this.displayedFilter =
        this._udrfService.udrfData.mainFilterArray.length - 1;
    } else {
      this.displayedFilter--;
    }
  }

  goToNextFilter() {
    this.dropdownflag = false;
    if (
      this.displayedFilter ==
      this._udrfService.udrfData.mainFilterArray.length - 1
    ) {
      this.displayedFilter = 0;
    } else {
      this.displayedFilter++;
    }
  }

  //Timesheet style config API
  async getTimesheetStyleConfig() {
    return new Promise((resolve, reject) =>
      this.approvalService
        .getTimesheetStyleConfig()
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res) => {
            if (res['messType'] == 'S') {
              this.color = res['data'][0]?.color;
              this.pColor1 = res['data'][0]?.p_color1;
              this.defColor = res['data'][0]?.def_color;
              this.fontFamily = res['data'][0]?.fontfamily;
            } else {
              this.toastService.showInfo(
                'Timesheet App Message',
                res['messText'],
                7000
              );
            }
            resolve(true);
          },
          error: (err) => {
            this.toastService.showError(
              `Timesheet Error ${err.error.code}`,
              _.filter(this.errorData, { error_code: err.error.code })[0]
                ?.user_message,
              7000
            );
            reject();
          },
        })
    );
  }

  //Approvals UI Config API
  async getTimesheetApprovalsUIConfig() {
    return new Promise((resolve, reject) =>
      this.approvalService
        .getTimesheetApprovalsUIConfig()
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res) => {
            if (res['messType'] == 'S') {
              this.tsUiConfigList = res['data'];
              this.tsUiConfigList.forEach((val) => {
                this.tsUiConfig[val.ui_config_id] = val;
              });
              this.assignBackendConfig();
            } else {
              this.toastService.showInfo(
                'Timesheet App Message',
                res['messText'],
                7000
              );
            }
            resolve(true);
          },
          error: (err) => {
            this.toastService.showError(
              `Timesheet Error ${err.error.code}`,
              _.filter(this.errorData, { error_code: err.error.code })[0]
                ?.user_message,
              7000
            );
            reject();
          },
        })
    );
  }

  //Awaiting Requests API
  async getPendingApprovalsInTimesheet(val) {
    if (val == 'Search') {
      this.approvalDetails = [];
      this.pendingCheckAll = false;
      if (this.approvalDialogRef && this.approvalDialogRef.componentInstance) {
        this.approvalDialogRef.close();
      }
      this.onChangePendingCheckAll();
      this.pendingLimit = 0;
      this.pendingSearchApiInProgress = true;
      if (this.approvalDetails.length > 0) {
        this.scrollContainer.nativeElement.scrollTop = 0;
      }
      this.retrieveTotalCountOfPendingApprovals();
    }
    if (val == 'Scroll') {
      this.pendingScrollApiInProgress = true;
    }
    if (val != 'Search' && val != 'Scroll') {
      this.initialLoading = true;
    }
    this.isApiInProgress = true;
    return new Promise((resolve, reject) =>
      this.approvalService
        .getPendingApprovalsInTimesheet(
          this.oid,
          this.aid,
          this.pendingSearchParam,
          this.pendingLimit,
          this.pendingSortOrder,
          this.pendingRequestsFilterConfig,
          moment(this.monthStartDate).format('YYYY-MM-DD'),
          moment(this.monthEndDate).format('YYYY-MM-DD'),
          this.filterQuery
        )
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res) => {
            if (res['messType'] == 'S') {
              if (val === 'Scroll') {
                this.approvalDetails = this.approvalDetails.concat(res['data']);
                this.onChangePendingCheckAll();
                this.pendingScrollApiInProgress = false;
              }
              if (val === 'Search' || val === 'Initial') {
                this.approvalDetails = res['data'];
                this.pendingSearchApiInProgress = false;
                this.pendingSortApiInProgress = false;
              }
            } else {
              this.pendingScrollApiInProgress = false;
              this.pendingSearchApiInProgress = false;
              this.pendingSortApiInProgress = false;
            }
            this.isApiInProgress = false;
            this.initialLoading = false;
            resolve(true);
          },
          error: (err) => {
            this.toastService.showError(
              `Timesheet Error ${err.error.code}`,
              _.filter(this.errorData, { error_code: err.error.code })[0]
                ?.user_message,
              7000
            );
            this.isApiInProgress = false;
            this.initialLoading = false;
            this.pendingSearchApiInProgress = false;
            this.pendingScrollApiInProgress = false;
            this.pendingSortApiInProgress = false;
            reject();
          },
        })
    );
  }

  //Previous Requests API
  async getTimesheetApprovalHistory(val) {
    if (val == 'Search') {
      this.approvalDetails = [];
      this.historyLimit = 0;
      this.historySearchApiInProgress = true;
      if (this.approvalDetails.length > 0) {
        this.scrollContainer.nativeElement.scrollTop = 0;
      }
    }
    if (val == 'Scroll') {
      this.historyScrollApiInProgress = true;
    }
    if (val == 'Initial') {
      this.initialLoading = true;
    }
    this.isApiInProgress = true;
    return new Promise((resolve, reject) =>
      this.approvalService
        .getTimesheetApprovalHistory(
          this.oid,
          this.aid,
          this.historySearchParam,
          this.historyLimit,
          this.historySortOrder,
          moment(this.monthStartDate).format('YYYY-MM-DD'),
          moment(this.monthEndDate).format('YYYY-MM-DD'),
          this.filterQuery
        )
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res) => {
            if (res['messType'] == 'S' && res['data'].length > 0) {
              if (val === 'Scroll') {
                this.approvalDetails = this.approvalDetails.concat(res['data']);
                this.historyScrollApiInProgress = false;
              }
              if (val === 'Search' || val === 'Initial') {
                this.approvalDetails = res['data'];
                this.historySearchApiInProgress = false;
                this.historySortApiInProgress = false;
              }
            } else {
              this.historyScrollApiInProgress = false;
              this.historySearchApiInProgress = false;
              this.historySortApiInProgress = false;
            }
            this.isApiInProgress = false;
            this.initialLoading = false;
            resolve(true);
          },
          error: (err) => {
            this.toastService.showError(
              `Timesheet Error ${err.error.code}`,
              _.filter(this.errorData, { error_code: err.error.code })[0]
                ?.user_message,
              7000
            );
            this.isApiInProgress = false;
            this.initialLoading = false;
            this.historyScrollApiInProgress = false;
            this.historySearchApiInProgress = false;
            this.historySortApiInProgress = false;
            reject();
          },
        })
    );
  }

  //General Timesheet Config API
  async getBasicTimesheetConfigurations() {
    return new Promise((resolve, reject) =>
      this.approvalService
        .getBasicTimesheetConfigurations(this.oid, this.aid)
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res) => {
            if (res['messType'] == 'S') {
              this.basicTsConfig = res['data'];
              this.tsMonthEndDate = this.basicTsConfig.timesheet_month_end_date;
            } else {
              this.toastService.showInfo(
                'Timesheet App Message',
                res['messText'],
                7000
              );
            }
            resolve(true);
          },
          error: (err) => {
            this.toastService.showError(
              `Timesheet Error ${err.error.code}`,
              _.filter(this.errorData, { error_code: err.error.code })[0]
                ?.user_message,
              7000
            );
            reject();
          },
        })
    );
  }

  //Update Approval or rejection status API
  async updateTimesheetApprovalOrRejectionStatus(
    status,
    comments,
    details,
    statusId,
    bulkApprove,
    week = []
  ) {
    return new Promise((resolve, reject) =>
      this.approvalService
        .updateTimesheetApprovalOrRejectionStatus(
          this.oid,
          this.aid,
          status,
          comments,
          details,
          statusId,
          week,
          bulkApprove,
          this.pendingSearchParam,
          this.currentUser.aid,
          this.currentUser.oid,
          this.pendingRequestsFilterConfig,
          moment(this.monthStartDate).format('YYYY-MM-DD'),
          moment(this.monthEndDate).format('YYYY-MM-DD'),
          null,
          this.filterQuery
        )
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res) => {
            resolve(true);
          },
          error: (err) => {
            reject();
          },
        })
    );
  }

  //Reject approved timesheet API
  async rejectApprovedTimesheet(status, comments, details, statusId) {
    return new Promise((resolve, reject) =>
      this.approvalService
        .rejectApprovedTimesheet(
          this.oid,
          this.aid,
          status,
          comments,
          details,
          statusId
        )
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res) => {
            resolve(true);
          },
          error: (err) => {
            reject();
          },
        })
    );
  }

  //Get total count of pending approvals
  async retrieveTotalCountOfPendingApprovals() {
    return new Promise((resolve, reject) =>
      this.approvalService
        .retrieveTotalCountOfPendingApprovals(
          this.oid,
          this.aid,
          this.pendingSearchParam,
          this.pendingRequestsFilterConfig,
          moment(this.monthStartDate).format('YYYY-MM-DD'),
          moment(this.monthEndDate).format('YYYY-MM-DD'),
          this.filterQuery
        )
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res) => {
            if (res['messType'] == 'S') {
              this.pendingApprovalsCount = res['data'];
              this.remMonthData = res['remMonthData'];
            }
            resolve(true);
          },
          error: (err) => {
            this.toastService.showError(
              `Timesheet Error ${err.error.code}`,
              _.filter(this.errorData, { error_code: err.error.code })[0]
                ?.user_message,
              7000
            );
            reject();
          },
        })
    );
  }

  async getErrorDetails() {
    return new Promise((resolve, reject) =>
      this.approvalService
        .getErrorDetails()
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res) => {
            if (res['messType'] == 'S' && res['data'].length > 0) {
              this.errorData = res['data'];
            }
            resolve(true);
          },
          error: (err) => {
            console.log(err);
            reject();
          },
        })
    );
  }

  async checkIfEmployeeIsApprovalAdmin(val) {
    this.reportingEmployeeDetailsInProgress = true;
    this.isApiInProgress = true;
    if (val === 'Search') {
      this.reportingEmployeeLimit = 0;
    }
    return new Promise((resolve, reject) =>
      this.approvalService
        .checkIfEmployeeIsApprovalAdmin(
          this.currentUser.oid,
          this.currentUser.aid,
          this.search_param_my_team,
          this.reportingEmployeeLimit
        )
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res) => {
            if (res['messType'] == 'S' && val === 'Initial') {
              this.isHrAdmin = 1;
            }
            if (res['messType'] == 'S') {
              if (val === 'Scroll') {
                this.reportingEmployeeList = this.reportingEmployeeList.concat(
                  res['data']
                );
              }
              if (val === 'Search') {
                this.reportingEmployeeList = res['data'];
              }
            } else {
              if (val === 'Search') {
                this.reportingEmployeeList = [];
              }
            }
            this.isApiInProgress = false;
            this.reportingEmployeeDetailsInProgress = false;
            resolve(true);
          },
          error: (err) => {
            this.toastService.showError(
              `Timesheet Error ${err.error.code}`,
              _.filter(this.errorData, { error_code: err.error.code })[0]
                ?.user_message,
              7000
            );
            if (val === 'Initial') {
              this.isHrAdmin = 0;
            }
            this.isApiInProgress = false;
            this.reportingEmployeeDetailsInProgress = false;
            reject();
          },
        })
    );
  }
  /**
   * Timesheet Approval Overview Week Details
   */
  getOverviewDetailsInPendingApprovals(items) {
    console.log(items);
    items.isExpanded = !items.isExpanded;

    return new Promise((resolve, reject) =>
      this.approvalService
        .getOverviewDetailsInPendingApprovals(
          items.associate_id,
          items.oid,
          items.cost_center_id,
          items.location_id,
          items.start_date,
          items.end_date,
          items.sub_project_id,
          items.cost_center_type,
          this.oid,
          this.aid,
          items.cost_center,
          items.cost_center_description,
          items.submitted_on,
          items.sub_project_name,
          items.location,
          items.employee_name
        )
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res) => {
            if (
              res['messType'] == 'S' &&
              res['data'] &&
              res['data'].length > 0
            ) {
              items.approvalDetailViewData = res['data'];
            } else {
              this.toastService.showInfo(
                'Timesheet Approval Message',
                'Detail view failed to load, Kindly try after sometime',
                7000
              );
            }
            resolve(true);
          },
          error: (err) => {
            this.toastService.showError(
              `Timesheet Error ${err.error.code}`,
              _.filter(this.errorData, { error_code: err.error.code })[0]
                ?.user_message,
              7000
            );
            reject();
          },
        })
    );
  }
  /**
   * To Get the comment data for the sow
   */
  async getAllCommentsData(item) {
    const { TsV2AllCommentsComponent } = await import(
      '../../lazy-loaded/ts-v2-all-comments/ts-v2-all-comments.component'
    );
    const LeavePolicyComponentData = this._dialog.open(
      TsV2AllCommentsComponent,
      {
        height: '100%',
        width: '752px',
        position: { right: '0px' },
        data: { modalParam: item, aid: this.aid, oid: this.oid },
      }
    );
  }

  /**
   * Timesheet Approval Overview Week Details
   */
  getOverviewDetailsInPreviousApprovals(items) {
    console.log(items);
    items.isExpanded = !items.isExpanded;

    return new Promise((resolve, reject) =>
      this.approvalService
        .getOverviewDetailsInPreviousApprovals(
          items.associate_id,
          items.oid,
          items.cost_center_id,
          items.location_id,
          moment(this.monthStartDate).format('YYYY-MM-DD'),
          moment(this.monthEndDate).format('YYYY-MM-DD'),
          items.sub_project_id,
          items.cost_center_type,
          this.oid,
          this.aid,
          items.cost_center,
          items.cost_center_description,
          items.submitted_on,
          items.sub_project_name,
          items.location,
          items.employee_name,
          items.week ? items.week : null
        )
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res) => {
            if (
              res['messType'] == 'S' &&
              res['data'] &&
              res['data'].length > 0
            ) {
              items.approvalDetailViewData = res['data'];
            } else {
              this.toastService.showInfo(
                'Timesheet Approval Message',
                'Detail view failed to load, Kindly try after sometime',
                7000
              );
            }
            resolve(true);
          },
          error: (err) => {
            this.toastService.showError(
              `Timesheet Error ${err.error.code}`,
              _.filter(this.errorData, { error_code: err.error.code })[0]
                ?.user_message,
              7000
            );
            reject();
          },
        })
    );
  }

  scrollLeft(): void {
    this.chipContainer.nativeElement.scrollBy({
      left: -100,
      behavior: 'smooth',
    });
  }

  scrollRight(): void {
    this.chipContainer.nativeElement.scrollBy({
      left: 100,
      behavior: 'smooth',
    });
  }

  async navigateToPendingApprovalMonth(chip) {
    this.monthSelected = false;
    let month = moment([chip.year, chip.timesheet_month - 1]).startOf('month');
    await this.getMonthStartAndEndDate(month);
    this.approvalDetails = [];
    this.pendingLimit = 0;
    this.pendingSortOrder = null;
    this.pendingSearchParam = '';
    this.awaitingRequestHeader.forEach((obj) => {
      obj.sort.sortOrderId = 0;
    });
    this.retrieveTotalCountOfPendingApprovals();
    this.getPendingApprovalsInTimesheet('Initial');
  }

  async removeAwaitingApprovalCarousel(bulkApprove) {
    if (this.approvalDetails?.length == 0 || bulkApprove) {
      let month = moment(this.monthEndDate).month() + 1;
      let year = moment(this.monthEndDate).year();
      for (let i = this.remMonthData?.length - 1; i >= 0; i--) {
        if (
          this.remMonthData[i].timesheet_month == month &&
          this.remMonthData[i].year == year
        ) {
          this.remMonthData.splice(i, 1);
        }
      }
    }
  }
  
  onSearchChange(value: string){
    this.searchInputChanged.next(value);
  }

  /**
   * @description Subscribe the filter component
   */
  async getFilterSubscription() {
     // Unsubscribe from previous subscription if exists
     if (this.filterSubscription$) {
      this.filterSubscription$.unsubscribe();
    }
    this.filterSubscription$ = this._filterService
      .getFilterConfig(this.applicationId, this.internalApplicationId)
      .subscribe(async (filterList) => {
        await this.applyFilter();
      });
  }

  /**
   * @description Open filters dialog
   */
  async openFilterDialog() {
    this._filterService.openFilterLandingPage(
      this.applicationId,
      this.internalApplicationId,
      {}
    );
  }
  /**
   * @description Apply the filter
   */
  async applyFilter() {
    // if (this.initialLoading) {
    //   return;
    // }
    this.filterQuery = '';
    this.appliedFilter = [];

    // const savedState = this.filterStateMap[this.internalApplicationId];
    // if (savedState) {
    //   this.appliedFilter = savedState.appliedFilter;
    //   this.filterQuery = savedState.filterQuery;
  
    //   // Call API based on context
    //   if (this.internalApplicationId == "timesheet_pending_request") {
    //     this.retrieveTotalCountOfPendingApprovals();
    //     await this.getPendingApprovalsInTimesheet('Search');
    //   }
    //   if (this.internalApplicationId == "timesheet_previous_request") {
    //     await this.getTimesheetApprovalHistory('Search');
    //   }
  
    //   return;
    // }
  

    let filter = await this.getUserFilterConfig();
    if (filter && filter !== '' && filter != null) {
      let filterVal =
        filter && filter['filterConfig'] && filter['filterConfig']['filterData']
          ? filter['filterConfig']['filterData']
          : [];
      let query = this._filterService.generateConditionBasedQuery(filterVal);
      this.appliedFilter = filterVal;
      this.filterQuery = query ? query : '';
      this.filterStateMap[this.internalApplicationId] = {
        appliedFilter: filterVal,
        filterQuery: this.filterQuery,
      };
    }
    //Call The API
    if(this.internalApplicationId == "timesheet_pending_request"){
      this.retrieveTotalCountOfPendingApprovals();
      await this.getPendingApprovalsInTimesheet('Search');
    }
    if(this.internalApplicationId == "timesheet_previous_request"){
      await this.getTimesheetApprovalHistory('Search');
    }
  }

  /**
   * @description Fetching the User Filter Config
   */
  getUserFilterConfig() {
    if (!this.internalApplicationId || this.internalApplicationId == '') {
      return;
    }

    return new Promise((resolve, reject) => {
      this._filterService
        .getFilterUserConfig(
          this.applicationId,
          this.internalApplicationId
        )
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res) => {
            if (res['messType'] == 'S') {
              resolve(res['data']);
            } else {
              resolve([]);
            }
          },
          error: (err) => {
            this.toastService.showError(
              'Error',
              'Error in Fetching User Filter Config',
              7000
            );
            resolve([]);
          },
        });
    });
  }
}


