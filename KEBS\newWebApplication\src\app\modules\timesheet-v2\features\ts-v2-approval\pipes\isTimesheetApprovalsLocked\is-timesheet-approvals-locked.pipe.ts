import { Pipe, PipeTransform } from '@angular/core';
import * as moment from 'moment';

@Pipe({
  name: 'isTimesheetApprovalsLocked',
})
export class IsTimesheetApprovalsLockedPipe implements PipeTransform {
  transform(basicTsConfig: any, currentMonthEndDate: any): boolean {
    if (basicTsConfig) {
      let currentDay = moment();

      let monthApprovalsEndDate;
      // Month Approvals End Date and Time
      if (basicTsConfig.monthly_timesheet_approval_end_date !== 'END') {
        monthApprovalsEndDate = moment(currentMonthEndDate).date(
          parseInt(basicTsConfig.monthly_timesheet_approval_end_date)
        );
      } else {
        monthApprovalsEndDate = moment(currentMonthEndDate).endOf('month');
      }

      // Month Approval Falls On
      if (basicTsConfig?.monthly_timesheet_approval_falls_on == '1') {
        monthApprovalsEndDate = moment(monthApprovalsEndDate).add(1, 'month');
      }

      monthApprovalsEndDate.hour(
        parseInt(basicTsConfig.monthly_timesheet_approval_end_hours)
      );
      monthApprovalsEndDate.minute(
        parseInt(basicTsConfig.monthly_timesheet_approval_end_minutes)
      );

      monthApprovalsEndDate = monthApprovalsEndDate.format('YYYY-MM-DD HH:mm')

      if (
        moment(currentDay, 'DD-MM-YYYY HH:mm').isAfter(moment(monthApprovalsEndDate))
      ) {
        if (basicTsConfig?.allow_timesheet_approvals_for_previous_months == '1') {
          return false;
        }
        return true;
      } else {
        return false;
      }
    }
  }
}
