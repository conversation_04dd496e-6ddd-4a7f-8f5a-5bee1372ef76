<div class="settings-container">
  <div class="settings">
    <div class="settings-header">
      <div class="title d-flex">
        <div class="d-flex back-button" (click)="naviagteToTimesheetSettings()">
          <svg
            _ngcontent-gbh-c448=""
            width="16"
            height="16"
            viewBox="0 0 16 16"
            fill="none"
            class="arrow-svg"
          >
            <path
              _ngcontent-gbh-c448=""
              d="M6.00065 14.6668H10.0007C13.334 14.6668 14.6673 13.3335 14.6673 10.0002V6.00016C14.6673 2.66683 13.334 1.3335 10.0007 1.3335H6.00065C2.66732 1.3335 1.33398 2.66683 1.33398 6.00016V10.0002C1.33398 13.3335 2.66732 14.6668 6.00065 14.6668Z"
              stroke="#526179"
              stroke-width="0.8"
              stroke-linecap="round"
              stroke-linejoin="round"
            ></path>
            <path
              _ngcontent-gbh-c448=""
              d="M8.84081 10.3532L6.49414 7.99982L8.84081 5.64648"
              stroke="#526179"
              stroke-width="0.8"
              stroke-linecap="round"
              stroke-linejoin="round"
            ></path>
          </svg>
        </div>
        Timesheet Approval Settings
      </div>
      <div class="buttons">
        <div class="save-button" (click)="saveTimesheetSettings()">Save</div>
      </div>
    </div>
    <div style="padding-top: 5px">
      <mat-divider class="mat-start-divider"></mat-divider>
    </div>
    <div class="settings-content">
      <div class="col-12 pt-2 pb-2 pl-0 pr-0 row">
        <div class="col-3 header-text align-items-center">
          Monthly Timesheet Approval Period
        </div>
        <div class="col-6">
          <div>
            From
            <mat-form-field appearance="outline">
              <mat-select [(ngModel)]="monthApprovalFrom">
                <mat-option
                  *ngFor="let option of endDateArray"
                  [value]="option.value"
                >
                  {{ option.display }}
                </mat-option>
              </mat-select>
            </mat-form-field>
            of
            <mat-form-field appearance="outline">
              <mat-select [(ngModel)]="monthApprovalFromOf">
                <mat-option
                  *ngFor="let option of monthPeriod"
                  [value]="option.value"
                >
                  {{ option.display }}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>
          <div>
            To &nbsp;&nbsp;&nbsp;
            <mat-form-field appearance="outline">
              <mat-select [(ngModel)]="monthApproavlTo">
                <mat-option
                  *ngFor="let option of endDateArray"
                  [value]="option.value"
                >
                  {{ option.display }}
                </mat-option>
              </mat-select>
            </mat-form-field>
            of
            <mat-form-field appearance="outline">
              <mat-select [(ngModel)]="monthApprovalToOf">
                <mat-option
                  *ngFor="let option of monthPeriod"
                  [value]="option.value"
                >
                  {{ option.display }}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>
        </div>
      </div>
      <div class="col-12 pt-2 pb-2 pl-0 pr-0 row">
        <div class="col-3 header-text align-items-center">
          Monthly Timesheet Approval Cutoff Time
        </div>
        <div class="col-6">
          <div>
            <mat-form-field appearance="outline">
              <mat-select [(ngModel)]="monthApprovalEndHour">
                <mat-option *ngFor="let option of hours" [value]="option.value">
                  {{ option.display }}
                </mat-option>
              </mat-select>
            </mat-form-field>
            <mat-form-field appearance="outline" style="padding-left: 15px;">
              <mat-select [(ngModel)]="monthApprovalEndMin">
                <mat-option *ngFor="let option of minutes" [value]="option.value">
                  {{ option.display }}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
