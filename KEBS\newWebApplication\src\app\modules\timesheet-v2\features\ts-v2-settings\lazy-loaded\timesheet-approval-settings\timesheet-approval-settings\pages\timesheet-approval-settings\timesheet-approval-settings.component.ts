import { Component, HostListener, OnInit } from '@angular/core';
import { FormControl } from '@angular/forms';
import { Router } from '@angular/router';
import { Subject } from 'rxjs';
import { ToasterService } from 'src/app/services/toaster-service/toaster.service';
import { TimesheetSettingsServiceService } from '../../../../../services/timesheet-settings-service.service';
import { takeUntil } from 'rxjs/operators';
import { TsV2Service } from 'src/app/modules/timesheet-v2/services/ts-v2.service';
@Component({
  selector: 'app-timesheet-approval-settings',
  templateUrl: './timesheet-approval-settings.component.html',
  styleUrls: ['./timesheet-approval-settings.component.scss'],
})
export class TimesheetApprovalSettingsComponent implements OnInit {
  dynamicHeight: string;
  dynamicSubHeight: string;
  endDateArray = [];
  monthPeriod = [
    {
      id: 1,
      value: 0,
      display: 'Current Month',
    },
    {
      id: 2,
      value: 1,
      display: 'Next Month',
    },
  ];
  monthApprovalFrom: any;
  monthApprovalFromOf: any;
  monthApproavlTo: any;
  monthApprovalToOf: any;
  hours = [];
  minutes = [];
  monthApprovalEndHour: any;
  monthApprovalEndMin: any;
  protected _onDestroy = new Subject<void>();

  constructor(
    private router: Router,
    private toasterService: ToasterService,
    private tsSettingService: TimesheetSettingsServiceService,
    private tsService: TsV2Service
  ) {}

  async ngOnInit() {
    await this.checkSettingsAccess();
    for (let i = 1; i <= 31; i++) {
      this.endDateArray.push({
        id: i,
        value: i,
        display: i,
      });
    }
    this.endDateArray.push({
      id: 32,
      value: 'END',
      display: 'Monthly Calendar End Day',
    });
    for (let i = 0; i <= 23; i++) {
      this.hours.push({
        id: i,
        value: i,
        display: i,
      });
    }
    for (let i = 0; i <= 59; i++) {
      this.minutes.push({
        id: i,
        value: i,
        display: i,
      });
    }
    this.getTimesheetSetting();
  }

  @HostListener('window:resize')
  onResize() {
    this.calculateDynamicContentHeight();
  }

  calculateDynamicContentHeight() {
    this.dynamicHeight = window.innerHeight - 112 + 'px';
    document.documentElement.style.setProperty(
      '--dynamicHeight',
      this.dynamicHeight
    );
    this.dynamicSubHeight = window.innerHeight - 274 + 'px';
    document.documentElement.style.setProperty(
      '--dynamicSubHeight',
      this.dynamicSubHeight
    );
  }

  saveTimesheetSettings() {
    //Month Validations
    if (
      this.monthApprovalFromOf == this.monthApprovalToOf &&
      this.monthApproavlTo < this.monthApprovalFrom
    ) {
      return this.toasterService.showError(
        'Timesheet Setting Message',
        'Monthly Approval End Date cannot be less than Monthly Approval Start Date',
        7000
      );
    }
    if (this.monthApprovalFromOf == 1) {
      this.toasterService.showError(
        'Timesheet Setting Message',
        'Current Month Timesheet Approval Cannot Start From Next Month',
        7000
      );
    }
    let generalSettingData = {
      month_timesheet_approval_from: this.monthApprovalFrom,
      month_timesheet_approval_from_of: this.monthApprovalFromOf,
      monthly_timesheet_approval_end_date: this.monthApproavlTo,
      monthly_timesheet_approval_falls_on: this.monthApprovalToOf,
      monthly_timesheet_approval_end_hours: this.monthApprovalEndHour,
      monthly_timesheet_approval_end_minutes: this.monthApprovalEndMin
    };
    return new Promise((resolve, reject) =>
      this.tsSettingService
        .saveTimesheetSettings(generalSettingData, { data: 'No' }, 2)
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res) => {
            if (res['messType'] == 'S') {
              this.toasterService.showInfo(
                'Timesheet App Message',
                res['messText'],
                7000
              );
            } else {
              this.toasterService.showInfo(
                'Timesheet App Message',
                res['messText'],
                7000
              );
            }
            resolve(true);
          },
          error: (err) => {
            this.toasterService.showError(
              'Timesheet Setting Message',
              'Error: Timesheet Settings Save Failed, Kindly try after some time',
              7000
            );
            reject();
          },
        })
    );
  }

  getTimesheetSetting() {
    return new Promise((resolve, reject) =>
      this.tsSettingService
        .getTimesheetSettings(2)
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res) => {
            if (res['messType'] == 'S') {
              this.monthApprovalFromOf = parseInt(
                res['generalSettings']?.month_timesheet_approval_from_of
              );
              this.monthApprovalFrom =
                res['generalSettings']?.month_timesheet_approval_from != 'END'
                  ? parseInt(
                      res['generalSettings']?.month_timesheet_approval_from
                    )
                  : res['generalSettings']?.month_timesheet_approval_from;
              this.monthApproavlTo =
                res['generalSettings']?.monthly_timesheet_approval_end_date !=
                'END'
                  ? parseInt(
                      res['generalSettings']
                        ?.monthly_timesheet_approval_end_date
                    )
                  : res['generalSettings']?.monthly_timesheet_approval_end_date;
              this.monthApprovalToOf = parseInt(
                res['generalSettings']?.month_timesheet_approval_to_of
              );
              this.monthApprovalEndHour = parseInt(
                res['generalSettings']?.monthly_timesheet_approval_end_hours
              );
              this.monthApprovalEndMin = parseInt(
                res['generalSettings']?.monthly_timesheet_approval_end_minutes
              );
            } else {
              this.toasterService.showInfo(
                'Timesheet App Message',
                res['messText'],
                7000
              );
            }
            resolve(true);
          },
          error: (err) => {
            this.toasterService.showError(
              'Timesheet Setting Message',
              'Error: Timesheet Settings Save Failed, Kindly try after some time',
              7000
            );
            reject();
          },
        })
    );
  }

  naviagteToTimesheetSettings() {
    this.router.navigateByUrl('/main/timesheetv2/settings');
  }

  ngOnDestroy() {
    this._onDestroy.next();
    this._onDestroy.complete();
  }

  async checkSettingsAccess() {
    this.tsService.checkTimesheetAccess().subscribe(async (res) => {
      if (res['messType'] == 'S') {
        if (!res['settingAccess']) {
          this.router.navigateByUrl('/main/timesheetv2/submission');
        }
      }
    });
  }
}
