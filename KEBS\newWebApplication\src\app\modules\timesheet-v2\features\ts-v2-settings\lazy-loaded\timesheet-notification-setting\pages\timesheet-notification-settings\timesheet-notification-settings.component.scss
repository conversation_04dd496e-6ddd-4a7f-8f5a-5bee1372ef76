.settings-container {
  background-color: #f1f3f8;
  overflow: hidden;

  .settings {
    margin-left: 16px;
    margin-right: 16px;
    margin-bottom: 16px;
    padding: 4px 16px 14px 16px;
    height: var(--dynamicHeight);
    border-radius: 4px;
    background-color: white;
    border: 1px solid #e8e9ee;
    position: relative;
    gap: 5px;
    display: flex;
    flex-direction: column;

    .settings-header {
      display: flex;
      justify-content: space-between;
      padding: 10px;
      padding-left: 0px;

      .title {
        color: #45546e;
        font-family: var(--fontFamily);
        font-size: 14px;
        font-weight: 900;
        line-height: 16px;
        letter-spacing: 0.02em;
      }

      .buttons {
        .save-button {
          padding: 8px 12px 8px 12px;
          background-color: #79ba44;
          color: #ffffff;
          font-family: var(--fontFamily);
          font-size: 14px;
          font-weight: 600;
          line-height: 16px;
          border-radius: 5px;
          cursor: pointer;
        }
      }
    }

    .settings-content {
      overflow: auto;
      height: 100%;
    }

    .settings-content::-webkit-scrollbar {
      width: 7px !important;
    }

    .settings-content::-webkit-scrollbar-track {
      background: transparent !important;
    }

    .mat-start-divider {
      position: absolute;
      width: 98%;
      background: #45546e;
    }

    .header-text {
      font-family: var(--fontFamily);
      font-weight: 500;
      font-size: 14px;
      color: #45546e;
    }
    .header-detail {
      font-family: var(--atsfontFamily);
      font-weight: 400;
      font-size: 12px;
      color: #b9c0ca;
    }
  }

  .auto-ts-btn {
    background: linear-gradient(
      270deg,
      var(--color),
      var(--color) 105.29%
    ) !important;
    color: #ffffff;
    font-size: 12px;
    border-radius: 5px;
  }
  .example-form {
    min-width: 150px;
    max-width: 500px;
    width: 100%;
  }
  .example-full-width {
    width: 100%;
  }
  .back-button {
    cursor: pointer;
    margin-right: 15px;
  }
  .chip {
    border-radius: 24px;
    text-align: center;
    padding: 2px 12px;
    font-weight: 500;
  }
}
