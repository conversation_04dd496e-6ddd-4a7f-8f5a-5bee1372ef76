import { Component, HostListener, OnInit } from '@angular/core';
import { FormControl } from '@angular/forms';
import { ToasterService } from 'src/app/services/toaster-service/toaster.service';
import { TimesheetSettingsServiceService } from '../../../../services/timesheet-settings-service.service';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { Router } from '@angular/router';
import { TsV2Service } from 'src/app/modules/timesheet-v2/services/ts-v2.service';

@Component({
  selector: 'app-timesheet-submission-settings',
  templateUrl: './timesheet-submission-settings.component.html',
  styleUrls: ['./timesheet-submission-settings.component.scss'],
})
export class TimesheetSubmissionSettingsComponent implements OnInit {
  dynamicHeight: string;
  dynamicSubHeight: string;
  timesheetType: any;
  endDateArray = [];
  endDateValue: any;
  dayData = [
    {
      id: 1,
      value: 0,
      display: 'Sunday',
    },
    {
      id: 2,
      value: 1,
      display: 'Monday',
    },
    {
      id: 3,
      value: 2,
      display: 'Tuesday',
    },
    {
      id: 4,
      value: 3,
      display: 'Wednesday',
    },
    {
      id: 5,
      value: 4,
      display: 'Thursday',
    },
    {
      id: 6,
      value: 5,
      display: 'Friday',
    },
    {
      id: 7,
      value: 6,
      display: 'Saturday',
    },
  ];
  weekPeriod = [
    {
      id: 1,
      value: 0,
      display: 'Current Week',
    },
    {
      id: 2,
      value: 1,
      display: 'Next Week',
    },
  ];
  weekStartFrom: any;
  weekStartOf: any;
  weekEndFrom: any;
  weekEndOf: any;
  monthStartFrom: any;
  monthStartOf: any;
  monthEndFrom: any;
  monthEndOf: any;
  monthPeriod = [
    {
      id: 1,
      value: 0,
      display: 'Current Month',
    },
    {
      id: 2,
      value: 1,
      display: 'Next Month',
    },
  ];
  minHours = 0;
  maxHours = 23;
  timesheetTypeData = [
    {
      id: 1,
      value: 'weekly_monthly',
      display: 'Week',
    },
    {
      id: 2,
      value: 'monthly',
      display: 'Month',
    },
  ];
  showWeeklyTimesheetPeriod: boolean = true;
  submissionEndHour: any;
  submissionEndMin: any;
  hours = [];
  minutes = [];
  monthSubmissionEndHour: any;
  monthSubmissionEndMin: any;
  protected _onDestroy = new Subject<void>();
  constructor(
    private toasterService: ToasterService,
    private tsSettingService: TimesheetSettingsServiceService,
    private router: Router,
    private tsService: TsV2Service
  ) {}

  async ngOnInit() {
    await this.checkSettingsAccess();
    for (let i = 1; i <= 27; i++) {
      this.endDateArray.push({
        id: i,
        value: i,
        display: i,
      });
    }
    this.endDateArray.push({
      id: 32,
      value: 'END',
      display: 'Monthly Calendar End Day',
    });
    for (let i = 0; i <= 23; i++) {
      this.hours.push({
        id: i,
        value: i,
        display: i,
      });
    }
    for (let i = 0; i <= 59; i++) {
      this.minutes.push({
        id: i,
        value: i,
        display: i,
      });
    }
    this.getTimesheetSetting();
  }

  @HostListener('window:resize')
  onResize() {
    this.calculateDynamicContentHeight();
  }

  calculateDynamicContentHeight() {
    this.dynamicHeight = window.innerHeight - 112 + 'px';
    document.documentElement.style.setProperty(
      '--dynamicHeight',
      this.dynamicHeight
    );
    this.dynamicSubHeight = window.innerHeight - 274 + 'px';
    document.documentElement.style.setProperty(
      '--dynamicSubHeight',
      this.dynamicSubHeight
    );
  }

  saveTimesheetSettings() {
    //Week Validations
    if (
      this.weekStartOf == this.weekEndOf &&
      this.weekEndFrom < this.weekStartFrom
    ) {
      return this.toasterService.showError(
        'Timesheet Setting Message',
        'Week End day cannot be less than Week Start Day',
        7000
      );
    }
    if (this.weekStartOf == 1) {
      return this.toasterService.showError(
        'Timesheet Setting Message',
        'Week Start duration cannot be in future week',
        7000
      );
    }
    //Month Validations
    if (
      this.monthStartOf == this.monthEndOf &&
      this.monthEndFrom < this.monthStartFrom
    ) {
      return this.toasterService.showError(
        'Timesheet Setting Message',
        'Week End day cannot be less than Week Start Day',
        7000
      );
    }
    if (this.monthStartOf == 1) {
      return this.toasterService.showError(
        'Timesheet Setting Message',
        'Week Start duration cannot be in future week',
        7000
      );
    }
    //Hour Settings
    if(this.minHours > this.maxHours){
      return this.toasterService.showError(
        'Timesheet Setting Message',
        'Minimum Hours cannot exceed the Maximum Hours Value',
        7000
      );
    }
    if(this.minHours < 0){
      return this.toasterService.showError(
        'Timesheet Setting Message',
        'Minimum Hours cannot be less than zero',
        7000
      );
    }
    if(this.maxHours < 0){
      return this.toasterService.showError(
        'Timesheet Setting Message',
        'Maximum Hours cannot be less than zero',
        7000
      );
    }
    if(this.minHours > 24){
      return this.toasterService.showError(
        'Timesheet Setting Message',
        'Minimum Hours cannot be more than 24',
        7000
      );
    }
    if(this.maxHours > 24){
      return this.toasterService.showError(
        'Timesheet Setting Message',
        'Maximum Hours cannot be more than 24',
        7000
      );
    }
    let generalSettingData = {
      timesheet_type: this.timesheetType,
      timesheet_month_end_date: this.endDateValue,
      week_timesheet_submission_end_day: this.weekEndFrom,
      week_timesheet_subm_falls_on: this.weekEndOf,
      monthly_timesheet_submission_end_date: this.monthEndFrom,
      week_timesheet_from: this.weekStartFrom,
      month_timesheet_from: this.monthStartFrom,
      week_timesheet_of: this.weekStartOf,
      month_timesheet_of: this.monthStartOf,
      month_timesheet_subm_falls_on: this.monthEndOf,
      week_timesheet_submission_end_hours: this.submissionEndHour,
      week_timesheet_submission_end_minutes: this.submissionEndMin,
      monthly_timesheet_submission_end_hours: this.monthSubmissionEndHour,
      monthly_timesheet_submission_end_minutes: this.monthSubmissionEndMin,
    };
    let hourSettingData = {
      min_hours: this.minHours,
      max_hours: this.maxHours,
    };
    return new Promise((resolve, reject) =>
      this.tsSettingService
        .saveTimesheetSettings(generalSettingData, hourSettingData, 1)
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res) => {
            if (res['messType'] == 'S') {
              this.toasterService.showInfo(
                'Timesheet App Message',
                res['messText'],
                7000
              );
            } else {
              this.toasterService.showInfo(
                'Timesheet App Message',
                res['messText'],
                7000
              );
            }
            resolve(true);
          },
          error: (err) => {
            this.toasterService.showError(
              'Timesheet Setting Message',
              'Error: Timesheet Settings Save Failed, Kindly try after some time',
              7000
            );
            reject();
          },
        })
    );
  }

  getTimesheetSetting() {
    return new Promise((resolve, reject) =>
      this.tsSettingService
        .getTimesheetSettings(1)
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res) => {
            if (res['messType'] == 'S') {
              this.timesheetType = res['generalSettings']?.timesheet_type;
              this.endDateValue =
                res['generalSettings']?.timesheet_month_end_date != 'END'
                  ? parseInt(res['generalSettings']?.timesheet_month_end_date)
                  : res['generalSettings']?.timesheet_month_end_date;
              this.weekEndFrom = parseInt(
                res['generalSettings']?.week_timesheet_submission_end_day
              );
              this.weekEndOf = parseInt(
                res['generalSettings']?.week_timesheet_subm_falls_on
              );
              this.monthEndFrom =
                res['generalSettings']?.monthly_timesheet_submission_end_date !=
                'END'
                  ? parseInt(
                      res['generalSettings']
                        ?.monthly_timesheet_submission_end_date
                    )
                  : res['generalSettings']
                      ?.monthly_timesheet_submission_end_date;
              this.minHours = res['hourSettings']?.min_hours;
              this.maxHours = res['hourSettings']?.max_hours;
              this.weekStartFrom = parseInt(
                res['generalSettings']?.week_timesheet_from
              );
              this.weekStartOf = parseInt(
                res['generalSettings']?.week_timesheet_of
              );
              this.monthStartFrom =
                res['generalSettings']?.month_timesheet_from != 'END'
                  ? parseInt(res['generalSettings']?.month_timesheet_from)
                  : res['generalSettings']?.month_timesheet_from;
              this.monthStartOf = parseInt(
                res['generalSettings']?.month_timesheet_of
              );
              this.monthEndOf = parseInt(
                res['generalSettings']?.month_timesheet_to_of
              );
              this.monthSubmissionEndHour = parseInt(
                res['generalSettings']?.monthly_timesheet_submission_end_hours
              );
              this.monthSubmissionEndMin = parseInt(
                res['generalSettings']?.monthly_timesheet_submission_end_minutes
              );
              this.submissionEndHour = parseInt(
                res['generalSettings']?.week_timesheet_submission_end_hours
              );
              this.submissionEndMin = parseInt(
                res['generalSettings']?.week_timesheet_submission_end_minutes
              );
            } else {
              this.toasterService.showInfo(
                'Timesheet App Message',
                res['messText'],
                7000
              );
            }
            resolve(true);
          },
          error: (err) => {
            this.toasterService.showError(
              'Timesheet Setting Message',
              'Error: Timesheet Settings Save Failed, Kindly try after some time',
              7000
            );
            reject();
          },
        })
    );
  }

  naviagteToTimesheetSettings() {
    this.router.navigateByUrl('/main/timesheetv2/settings');
  }

  ngOnDestroy() {
    this._onDestroy.next();
    this._onDestroy.complete();
  }

  async checkSettingsAccess() {
    this.tsService.checkTimesheetAccess().subscribe(async (res) => {
      if (res['messType'] == 'S') {
        if (!res['settingAccess']) {
          this.router.navigateByUrl('/main/timesheetv2/submission');
        }
      }
    });
  }
}
