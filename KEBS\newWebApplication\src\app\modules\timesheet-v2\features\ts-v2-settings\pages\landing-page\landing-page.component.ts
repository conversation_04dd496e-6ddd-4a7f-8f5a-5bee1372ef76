import { Component, HostListener, OnInit } from '@angular/core';
import { takeUntil } from 'rxjs/operators';
import { ToasterService } from 'src/app/services/toaster-service/toaster.service';
import { Subject } from 'rxjs';
import { TsV2SubmissionService } from '../../../ts-v2-submission/services/ts-v2-submission.service';
import * as _ from 'underscore';
import { LoginService } from 'src/app/services/login/login.service';
import * as moment from 'moment';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { TsV2Service } from 'src/app/modules/timesheet-v2/services/ts-v2.service';

@Component({
  selector: 'app-landing-page',
  templateUrl: './landing-page.component.html',
  styleUrls: ['./landing-page.component.scss'],
})
export class LandingPageComponent implements OnInit {
  dynamicHeight: string;
  dynamicSubHeight: string;
  errorData = [];
  protected _onDestroy = new Subject<void>();
  payload: object = {};
  settingsForm: FormGroup;
  length: number;
  formValues: object = {};
  masterData: object = {};

  //New Settings Code
  settingMasterData = [];
  progressBarState: boolean = true;

  constructor(
    private toastService: ToasterService,
    private tsSubmissionService: TsV2SubmissionService,
    private _loginService: LoginService,
    private _fb: FormBuilder,
    private router: Router,
    private tsService: TsV2Service
  ) {}

  ngOnInit(): void {
    this.checkSettingsAccess();
    this.calculateDynamicContentHeight();
    this.progressBarState = true;
    this.getTimesheetSettingsMaster();
  }

  @HostListener('window:resize')
  onResize() {
    this.calculateDynamicContentHeight();
  }

  calculateDynamicContentHeight() {
    this.dynamicHeight = window.innerHeight - 112 + 'px';
    document.documentElement.style.setProperty(
      '--dynamicHeight',
      this.dynamicHeight
    );
    this.dynamicSubHeight = window.innerHeight - 274 + 'px';
    document.documentElement.style.setProperty(
      '--dynamicSubHeight',
      this.dynamicSubHeight
    );
  }

  async getErrorDetails() {
    return new Promise((resolve, reject) =>
      this.tsSubmissionService
        .getErrorDetails()
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res) => {
            if (res['messType'] == 'S' && res['data'].length > 0) {
              this.errorData = res['data'];
            }
            resolve(true);
          },
          error: (err) => {
            console.log(err);
            reject();
          },
        })
    );
  }

  //Called when timesheet settings is saved
  saveTimeSheetSettings() {
    this.toastService.showSuccess(
      'Success',
      'Timesheet Settings Saved Successfully',
      7000
    );
  }

  //Retrieve Setting Master Data
  getTimesheetSettingsMaster() {
    return new Promise((resolve, reject) =>
      this.tsSubmissionService
        .getTimesheetSettingsMaster()
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res) => {
            if (res['messType'] == 'S' && res['data'].length > 0) {
              this.progressBarState = false;
              this.settingMasterData = res['data'];
            } else {
              this.progressBarState = false;
              this.toastService.showInfo(
                'Timesheet Settings Message',
                res['messText'],
                7000
              );
            }
            resolve(true);
          },
          error: (err) => {
            this.progressBarState = false;
            console.log(err);
            reject();
          },
        })
    );
  }

  navigateToDetailComponent(id) {
    if (id == 1) {
      this.router.navigateByUrl(
        '/main/timesheetv2/settings/timesheet-submission-settings'
      );
    }
    if (id == 2) {
      this.router.navigateByUrl(
        '/main/timesheetv2/settings/timesheet-approval-settings'
      );
    }
    if (id == 3) {
      this.router.navigateByUrl(
        '/main/timesheetv2/settings/timesheet-notification-settings'
      );
    }
  }

  checkSettingsAccess() {
    this.tsService.checkTimesheetAccess().subscribe(async (res) => {
      if (res['messType'] == 'S') {
        if (!res['settingAccess']) {
          this.router.navigateByUrl('/main/timesheetv2/submission');
        }
      }
    });
  }
}
