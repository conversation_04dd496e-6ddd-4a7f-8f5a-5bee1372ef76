import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';

@Injectable({
  providedIn: 'root'
})
export class TimesheetSettingsServiceService {

  constructor(private _http: HttpClient) { }

  saveTimesheetSettings(generalSettingData, hourSettingData, type) {
    return this._http.post('api/timesheetv2/Settings/saveTimesheetSettings', {
      settingsData: generalSettingData,
      hourSettingData: hourSettingData,
      type: type
    });
  }

  getTimesheetSettings(type){
    return this._http.post('api/timesheetv2/Settings/getTimesheetSettings', {
      type: type
    });
  }
}
