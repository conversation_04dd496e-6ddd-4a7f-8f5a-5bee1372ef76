import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { LandingPageComponent } from '../ts-v2-settings/pages/landing-page/landing-page.component';

const routes: Routes = [
  {
    path: '',
    component: LandingPageComponent,
  },
  {
    path: 'timesheet-submission-settings',
    loadChildren: () =>
      import(
        './lazy-loaded/timesheet-submission-settings/timesheet-submission-settings.module'
      ).then((m) => m.TimesheetSubmissionSettingsModule),
    data: {
      breadcrumb: 'Timesheet Submission Settings',
    },
  },
  {
    path: 'timesheet-approval-settings',
    loadChildren: () =>
      import(
        './lazy-loaded/timesheet-approval-settings/timesheet-approval-settings/timesheet-approval-settings.module'
      ).then((m) => m.TimesheetApprovalSettingsModule),
    data: {
      breadcrumb: 'Timesheet Approval Settings',
    },
  },
  {
    path: 'timesheet-notification-settings',
    loadChildren: () =>
      import(
        './lazy-loaded/timesheet-notification-setting/timesheet-notification-setting.module'
      ).then((m) => m.TimesheetNotificationSettingModule),
    data: {
      breadcrumb: 'Timesheet Approval Settings',
    },
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class TsV2SettingsRoutingModule {}
