import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { TsV2SettingsRoutingModule } from './ts-v2-settings-routing.module';
import { LandingPageComponent } from './pages/landing-page/landing-page.component';

import { SharedComponentsModule } from '../../../../app-shared/app-shared-components/components.module';

import { ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatRadioModule } from '@angular/material/radio';
import { MatSelectModule } from '@angular/material/select';
import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';
import { MatTooltipModule } from '@angular/material/tooltip';
@NgModule({
  declarations: [LandingPageComponent],
  imports: [
    CommonModule,
    TsV2SettingsRoutingModule,
    SharedComponentsModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatCheckboxModule,
    MatRadioModule,
    MatSlideToggleModule,
    MatSelectModule,
    MatIconModule,
    MatDividerModule,
    MatTooltipModule,
  ],
})
export class TsV2SettingsModule {}
