.bg-container {
  background-color: #f1f3f8;
  overflow: hidden;
}

.submission {
  margin-left: 16px;
  margin-right: 16px;
  margin-bottom: 16px;
  padding: 4px 16px 16px 16px;
  height: var(--dynamicHeight);
  border-radius: 4px;
  background-color: white;
  border: 1px solid #e8e9ee;
}

.submission-content {
  ::ng-deep
    .mat-form-field-appearance-outline.mat-form-field-can-float
    .mat-form-field-outline {
    color: var(--color) !important;
  }

  ::ng-deep
    .mat-form-field-appearance-outline.mat-focused
    .mat-form-field-outline,
  ::ng-deep .mat-form-field-appearance-outline:hover .mat-form-field-outline {
    border-color: var(--color) !important;
  }

  ::ng-deep .mat-slide-toggle-bar {
    background-color: #efefef;
    width: 28px !important;
    height: 16px !important;
  }

  ::ng-deep .mat-slide-toggle-thumb {
    height: 13px !important;
    width: 13px !important;
    top: 4.5px !important;
    right: -1px !important;
    position: relative;
    box-shadow: none !important;
  }

  ::ng-deep .mat-slide-toggle.mat-checked .mat-slide-toggle-bar {
    background-color: #52c41a;
  }

  ::ng-deep .mat-slide-toggle.mat-checked .mat-slide-toggle-thumb {
    background-color: #52c41a;
    border: 1px solid #dadce2 !important;
    background-color: #fff;
    right: 2px !important;
  }

  .submission-content-header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    height: 64px;

    ::ng-deep
      .mat-calendar
      .mat-calendar-body
      .mat-calendar-body-cell.mat-calendar-selected {
      background-color: red;
      color: white;
    }

    .align-items-row {
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      align-items: center;
    }

    .btn {
      border: none;
      margin-right: 15px;
      padding: 0;
    }

    .icon {
      font-size: 18px;
      color: #b9c0ca;
    }

    .icon:hover {
      cursor: pointer;
    }

    .week-text {
      font-family: var(--fontFamily);
      font-size: 14px;
      font-weight: 600;
      color: #45546e;
      margin: 0;
      padding-bottom: 5px;
    }

    .week-badge {
      height: 24px;
      width: 24px;
      margin-right: 4px;
      margin-bottom: 6px;
      display: flex;
      flex-direction: row;
      justify-content: center;
      border-radius: 2px;
      border-width: 1px;
      border-style: solid;

      .week-badge-text {
        font-family: var(--fontFamily);
        font-size: 10px;
        font-weight: 600;
        display: flex;
        align-items: center;
        margin-bottom: 0px;
      }
    }

    .week-badge:hover {
      cursor: pointer;
    }

    .mr30 {
      margin-right: 30px;
    }

    .mr24 {
      margin-right: 24px;
    }

    .mr16 {
      margin-right: 16px;
    }

    .light-text {
      font-family: var(--fontFamily);
      font-size: 12px;
      font-weight: 400;
      color: #6e7b8f;
      margin: 0;
    }

    .bold-text {
      font-family: var(--fontFamily);
      font-size: 14px;
      font-weight: 700;
      color: #111434;
      margin: 0;
    }

    .drop-down-icon {
      font-size: 18px;
      color: #111434;
      margin-top: 26px;
    }

    .save-btn {
      font-family: var(--fontFamily);
      font-size: 14px;
      font-weight: 700 !important;
      color: var(--pColor1);
      background-color: white;
      border: 1px solid var(--pColor1);
      border-radius: 4px;
    }

    .submit-btn {
      font-family: var(--fontFamily);
      color: white;
      font-size: 14px;
      font-weight: 700 !important;
      border-radius: 4px;
      background-color: var(--color);
      border: 1px solid var(--color);
    }

    .edit-btn {
      font-family: var(--fontFamily);
      font-size: 14px;
      font-weight: 700 !important;
      color: var(--pColor1);
      background-color: white;
      border: 1px solid var(--pColor1);
      border-radius: 4px;
    }

    .more-options-btn {
      padding-top: 7px;
      padding-bottom: 0px;
      padding-left: 8px;
      padding-right: 8px;
      background-color: white;
      border: 1px solid var(--pColor1);
      border-radius: 4px;

      .more-options-icon {
        font-size: 20px;
        color: var(--pColor1);
      }
    }

    .ques-icon {
      color: #45546e;
      font-size: 18px;
      padding-top: 6px;
      cursor: pointer;
    }

    .save-btn:disabled {
      font-family: var(--fontFamily);
      font-size: 14px;
      font-weight: 700 !important;
      color: #b9c0ca;
      background-color: #e8e9ee;
      border-radius: 4px;
      border: 1px solid #e8e9ee;
    }

    .submit-btn:disabled {
      font-family: var(--fontFamily);
      color: #b9c0ca;
      font-size: 14px;
      font-weight: 700 !important;
      border-radius: 4px;
      background-color: #e8e9ee;
      border: 1px solid #e8e9ee;
    }

    .edit-btn:disabled {
      font-family: var(--fontFamily);
      font-size: 14px;
      font-weight: 700 !important;
      color: #b9c0ca;
      background-color: #e8e9ee;
      border-radius: 4px;
      border: 1px solid #e8e9ee;
    }

    .calendar-icon {
      font-size: 16px;
    }
  }

  .submission-content-display {
    display: flex;
    flex-direction: row;
    height: var(--dynamicSubHeight);
    overflow-x: hidden;
    overflow-y: auto;

    .ts-cost-center {
      padding: 16px 0px 0px 0px !important;

      .cc-title-text {
        font-family: var(--fontFamily);
        font-size: 12px;
        font-weight: 600;
        color: #45546e;
        padding: 0;
        margin-bottom: 0px;
      }

      .large-text {
        font-family: var(--fontFamily);
        font-size: 14px;
        font-weight: 500;
        color: #45546e;
      }

      .medium-light-text {
        font-family: var(--fontFamily);
        font-size: 12px;
        font-weight: 400;
        color: #8b95a5;
      }

      .add-cost-center-btn {
        font-family: var(--fontFamily);
        padding: 4px 8px;
        border-radius: 4px;
        background-color: var(--color);
        color: white;
        font-size: 12px;
        font-weight: 700;
        border: none;
      }

      .previous-week-icon {
        font-size: 17px;
        color: #6e7b8f;
        line-height: inherit;
      }

      .prefill-previous-week-text {
        font-family: var(--fontFamily);
        font-size: 10px;
        font-weight: 400;
        color: #6e7b8f;
        margin: 0;
        text-decoration: underline;
      }

      .add-initial-cc-border {
        width: 280px;
        border: 1px solid #dadce2;
        border-radius: 8px;
        margin-top: 40px;
        padding: 12px;

        .large-bold-text {
          font-family: var(--fontFamily);
          font-size: 12px;
          font-weight: 700;
          color: #45546e;
          margin: 0;
        }

        .medium-text {
          font-family: var(--fontFamily);
          font-size: 12px;
          font-weight: 500;
          color: #6e7b8f;
          margin-bottom: 0px;
        }

        .cc-form-field {
          font-family: var(--fontFamily);
          font-size: 12px !important;
          font-weight: 400 !important;
          color: #5f6c81 !important;
          ::ng-deep .mat-form-field-infix {
            width: 228px !important;
          }
          ::ng-deep .mat-autocomplete-panel {
            max-height: 150px !important;
          }
        }

        .cc-form-field input {
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
        }

        .cancel-btn {
          font-family: var(--fontFamily);
          padding: 7px 11px;
          color: #45546e;
          border: 1px solid #45546e;
          border-radius: 4px;
          font-size: 14px;
          font-weight: 700;
          margin-right: 8px;
          background-color: white;
        }

        .add-btn {
          font-family: var(--fontFamily);
          padding: 8px 12px;
          background-color: var(--color);
          color: white;
          border-radius: 4px;
          border: none;
          font-size: 14px;
          font-weight: 700;
          color: white;
        }

        .add-btn:disabled {
          background-color: #e8e9ee;
          color: #b9c0ca;
        }
      }

      .add-cc-btn {
        font-family: var(--fontFamily);
        padding: 4px 8px;
        border: 1px solid var(--color);
        font-size: 12px;
        font-weight: 700;
        color: var(--color);
        background-color: white;
        border-radius: 4px;
        margin-top: 12px;
      }

      .add-secondary-cc-border {
        width: 280px;
        border: 1px solid #dadce2;
        border-radius: 8px;
        margin-top: 40px;
        padding: 12px;

        .large-bold-text {
          font-family: var(--fontFamily);
          font-size: 12px;
          font-weight: 700;
          color: #45546e;
          margin: 0;
        }

        .medium-text {
          font-family: var(--fontFamily);
          font-size: 12px;
          font-weight: 500;
          color: #6e7b8f;
          margin-bottom: 0px;
        }

        .cc-form-field {
          font-family: var(--fontFamily) !important;
          font-size: 12px !important;
          font-weight: 400 !important;
          color: #5f6c81 !important;
          ::ng-deep .mat-form-field-infix {
            width: 228px !important;
          }
          ::ng-deep .mat-autocomplete-panel {
            max-height: 150px !important;
          }
        }

        .cc-form-field input {
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
        }

        .align-items-center {
          display: flex;
          flex-direction: row;
          justify-content: flex-start;
          align-items: center;
        }

        .mr10 {
          margin-right: 10px;
        }

        .light-text {
          font-family: var(--fontFamily);
          font-size: 12px;
          font-weight: 400;
          color: #5f6c81;
          margin-bottom: 0px;
        }

        .quick-fill-hours-form-field {
          font-family: var(--fontFamily) !important;
          font-size: 12px !important;
          font-weight: 400 !important;
          color: #5f6c81 !important;
          width: 100px;
          ::ng-deep .mat-form-field-wrapper {
            padding-bottom: 0px;
          }
        }

        .mat-input-element[type="time"] {
          height: 14px !important;
        }

        .cancel-btn {
          font-family: var(--fontFamily);
          padding: 7px 11px;
          color: #45546e;
          border: 1px solid #45546e;
          border-radius: 4px;
          font-size: 14px;
          font-weight: 700;
          margin-right: 8px;
          background-color: white;
        }

        .add-btn {
          font-family: var(--fontFamily);
          padding: 8px 12px;
          background-color: var(--color);
          color: white;
          border-radius: 4px;
          border: none;
          font-size: 14px;
          font-weight: 700;
          color: white;
        }

        .add-btn:disabled {
          background-color: #e8e9ee;
          color: #b9c0ca;
        }
      }

      .task-name-text {
        font-family: var(--fontFamily);
        font-size: 12px;
        font-weight: 400;
        color: #45546e;
        margin: 0px;
        height: 19px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        text-transform: capitalize;
      }

      .task-url-text {
        font-family: var(--fontFamily);
        font-size: 12px;
        font-weight: 400;
        color: #8b95a5;
        margin: 0px;
        height: 19px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        cursor: pointer;
      }

      .add-task-manually-text {
        font-family: var(--fontFamily);
        font-size: 12px;
        font-weight: 400;
        color: #45546e;
        margin: 0;
        cursor: pointer;
        width: max-content;
      }

      .more-vert-icon {
        font-size: 20px;
        color: #45546e;
        cursor: pointer;
        padding-top: 2px;
        pointer-events: auto;
      }

      .more-vert-icon-tasks {
        font-size: 20px;
        color: #45546e;
        cursor: pointer;
        padding-top: 7px;
      }

      .panel-content ::ng-deep .mat-expansion-panel-content {
        display: none;
      }

      .panel ::ng-deep .mat-expansion-panel-header {
        padding: 0px !important;
        height: 36px !important;
      }

      .panel-height ::ng-deep .mat-expansion-panel-header {
        padding: 0px !important;
        height: 40px !important;
      }

      .panel ::ng-deep .mat-expansion-panel-header:hover {
        background: none !important;
      }

      .panel ::ng-deep .mat-expansion-panel-header .mat-expanded {
        height: 36px !important;
      }

      .panel-height ::ng-deep .mat-expansion-panel-header .mat-expanded {
        height: 40px !important;
      }

      .expand-icon {
        font-size: 15px;
        color: #6e7b8f;
        margin-top: 12px;
        cursor: pointer;
      }

      .contract-icon {
        font-size: 15px;
        color: #6e7b8f;
        margin-top: 12px;
        cursor: pointer;
      }

      ::ng-deep .expansion-panel-cc-name {
        font-family: var(--fontFamily) !important;
        font-size: 12px !important;
        font-weight: 700 !important;
        color: #45546e !important;
        margin-right: 0 !important;
        align-items: center !important;
        text-transform: capitalize;
        height: 20px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }

      ::ng-deep .expansion-panel-subcc-name {
        font-family: var(--fontFamily) !important;
        font-size: 12px !important;
        font-weight: 400 !important;
        color: #8b95a5 !important;
        margin-right: 0 !important;
        align-items: center !important;
        text-transform: capitalize;
        height: 20px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }

      .panel ::ng-deep .mat-expansion-panel-header-description {
        font-family: var(--fontFamily);
        display: flex !important;
        justify-content: flex-end !important;
        margin-right: 0px !important;
        align-items: center !important;
      }

      .panel ::ng-deep .mat-expansion-panel-body {
        padding-left: 24px !important;
        padding-top: 4px !important;
        padding-bottom: 0px !important;
        padding-right: 0px !important;
      }

      ::ng-deep .mat-expansion-panel-spacing {
        margin-top: 0px !important;
      }
    }

    .ts-week {
      padding: 0;
      display: flex;
      flex-direction: row;
    }

    .light-text {
      font-family: var(--fontFamily);
      font-size: 12px;
      font-weight: 400;
      color: #8b95a5;
      margin: 0;
    }

    .small-light-text {
      font-family: var(--fontFamily);
      font-size: 10px;
      font-weight: 400;
      color: #5f6c81;
      padding-top: 16px;
    }

    .date-border {
      width: 80px;
      min-height: 24px;
      max-height: 24px;
      background-color: #f7f9fb;
      border-width: 1px;
      border-style: solid;
      border-radius: 4px;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .date-text {
      font-family: var(--fontFamily);
      font-size: 12px;
      font-weight: 400;
      color: #45546e;
      margin: 0;
    }

    .total-width {
      width: 80px;
      height: 24px;
      padding-left: 8px;
      padding-right: 8px;
      padding-top: 4px;
      padding-bottom: 4px;
      display: flex;
      justify-content: center;
      margin-left: 16px;
      margin-right: 16px;
    }

    .small-light-text-m0 {
      font-family: var(--fontFamily);
      font-size: 10px;
      font-weight: 400;
      color: #111434;
    }

    .hours-worked-border {
      width: 80px;
      min-height: 32px;
      max-height: 32px;
      border: 1px solid #5f6c81;
      margin-top: 16px;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .hours-worked-border:hover {
      cursor: pointer;
    }

    .hours-worked-text {
      font-family: var(--fontFamily);
      font-size: 12px;
      font-weight: 400;
      color: #111434;
      margin: 0;
      width: inherit;
      justify-content: center;
      display: flex;
    }

    .full-day-leave-border {
      width: 80px;
      min-height: 32px;
      max-height: 32px;
      border: 1px solid #13c2c2;
      margin-top: 16px;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .full-day-leave-border {
      cursor: pointer;
    }

    .leave-text {
      font-family: var(--fontFamily);
      font-size: 12px;
      font-weight: 400;
      color: #13c2c2;
      margin: 0;
      width: inherit;
      justify-content: center;
      display: flex;
    }

    .half-day-leave-border {
      width: 80px;
      min-height: 32px;
      max-height: 32px;
      border: 1px solid #13c2c2;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      cursor: pointer;
      margin-top: 16px;

      .leave-border-overlap {
        width: 58px;
        height: 12px;
        background-color: #13c2c2;
        position: absolute;
        top: 0%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 1;
        border-radius: 4px;
        display: flex;
        align-items: center;
      }

      mat-icon {
        width: 0px !important;
        height: 9px !important;
      }

      .half-day-text {
        font-size: 9px;
        font-weight: 400;
        margin: 0;
        text-align: center;
        color: white;
      }

      .cl-text {
        font-size: 12px;
        font-weight: 400;
        color: #13c2c2;
        margin: 0;
        margin-top: 5px;
      }

      .hours-worked-text-on-leave {
        font-size: 12px;
        font-weight: 400;
        color: #111434;
        margin: 0;
        margin-top: 5px;
        padding-left: 4px;
      }
    }

    .last-working-day-border {
      width: 80px;
      min-height: 24px;
      max-height: 24px;
      border: 1px solid #EE4961;
      background-color: #f7f9fb;
      border-style: solid;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;

      .last-working-day-border-overlap {
        width: 58px;
        height: 12px;
        background-color: #EE4961;
        position: absolute;
        top: 0%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 1;
        border-radius: 4px;
        display: flex;
        align-items: center;
      }

      mat-icon {
        width: 0px !important;
        height: 9px !important;
      }

      .last-working-day-text {
        font-size: 9px;
        font-weight: 400;
        margin: 0;
        text-align: center;
        color: white;
      }

      .cl-text {
        font-size: 12px;
        font-weight: 400;
        color: #13c2c2;
        margin: 0;
        margin-top: 5px;
      }

      .hours-worked-text-on-last-working-day {
        font-size: 12px;
        font-weight: 400;
        color: #111434;
        margin: 0;
        margin-top: 5px;
        padding-left: 4px;
      }

    }
  }

  .divider {
    color: #e8e9ee;
    margin-bottom: 16px;
  }

  .vertical-divider {
    color: #e8e9ee;
    height: var(--dividerHeight);
  }
}

.supervisor:hover {
  cursor: pointer;
}

.search-icon {
  color: #b9c0ca;
}

.search-form {
  width: 200px;
}

.search-icon {
  color: #b9c0ca;
}

.search-form {
  width: 195px;
  padding-left: 0px !important;
}

mat-expansion-panel {
  width: 100%;
  box-shadow: none !important;
  margin-left: 0%;
}

.icon {
  color: #45546e;
  font-size: 20px;
  cursor: pointer;
  margin-left: 0px;
}

.header-icon {
  padding: 8px;
  gap: 10px;
  width: 32px;
  height: 32px;
  background: #ffffff;
  border: 1px solid #dadce2;
  border-radius: 4px;
}

.supervisor {
  margin-left: -35.5%;
  margin-top: -25%;
  color: var(--color);
}

.submission-content-header-0 {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 64px;
}

.align-items-row {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
}

.divider {
  color: #e8e9ee;
}

.time-fill-popup {
  padding: 4px 8px;

  ::ng-deep .mat-checkbox-checked.mat-accent .mat-checkbox-background {
    background-color: var(--color) !important;
  }

  ::ng-deep .mat-radio-button.mat-accent .mat-radio-inner-circle {
    background-color: var(--color) !important;
  }

  ::ng-deep .mat-form-field-appearance-outline .mat-form-field-wrapper {
    margin: 0px !important;
  }

  ::ng-deep
    .mat-radio-button.mat-accent.mat-radio-checked
    .mat-radio-outer-circle {
    border-color: var(--color) !important;
  }

  ::ng-deep .mat-radio-outer-circle {
    height: 16px !important;
    width: 16px !important;
    margin-top: 2px;
    border-color: #dadce2;
  }

  ::ng-deep .mat-radio-inner-circle {
    height: 16px !important;
    width: 16px !important;
    margin-top: 2px;
  }

  ::ng-deep .mat-radio-label-content {
    padding-left: 0px !important;
  }

  ::ng-deep .mat-slide-toggle-bar {
    background-color: #efefef;
    width: 28px !important;
    height: 16px !important;
  }

  ::ng-deep .mat-slide-toggle-thumb {
    height: 13px !important;
    width: 13px !important;
    top: 4.5px !important;
    right: -1px !important;
    position: relative;
    box-shadow: none !important;
  }

  ::ng-deep .mat-slide-toggle.mat-checked .mat-slide-toggle-bar {
    background-color: #52c41a;
  }

  ::ng-deep .mat-slide-toggle.mat-checked .mat-slide-toggle-thumb {
    background-color: #52c41a;
    border: 1px solid #dadce2 !important;
    background-color: #fff;
    right: 2px !important;
  }

  ::ng-deep .mat-button-toggle-label-content {
    font-size: 12px !important;
    font-family: var(--fontFamily) !important;
  }

  .toggle-btn {
    width: 100%;
  }

  .btn-toggle-selected {
    font-size: 12px !important;
    font-family: var(--fontFamily) !important;
    background-color: var(--color) !important;
    color: var(--defColor);
  }

  .align-items-btw {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }

  .align-items-center {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
  }

  .fill-card-title-text {
    font-family: var(--fontFamily);
    font-size: 11px;
    font-weight: 500;
    color: #6e7b8f;
    margin: 0;
  }

  .icon-margin {
    padding-left: 12px;
    padding-top: 4px;

    mat-icon {
      height: 14px;
      width: 14px;
    }
  }

  .clear-icon {
    font-size: 15px;
    color: #45546e;
  }

  .clear-icon:hover {
    cursor: pointer;
  }

  .divider-fill-popup {
    color: #e8e9ee;
    width: 100%;
  }

  .form-label {
    font-family: var(--fontFamily);
    color: #6e7b8f;
    font-size: 10px;
    font-weight: 500;
    margin: 0;
  }

  .consumed-hours {
    width: 100px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }

  .planned-hours-form-field {
    font-family: var(--fontFamily) !important;
    font-size: 12px !important;
    font-weight: 400 !important;
    color: #5f6c81 !important;
    width: 70px;
    ::ng-deep .mat-form-field-wrapper {
      padding-bottom: 0px;
    }
  }

  .time-form-field {
    font-family: var(--fontFamily) !important;
    font-size: 12px !important;
    font-weight: 400 !important;
    color: #5f6c81 !important;
    width: 100px;
  }

  .hours-form-field {
    font-family: var(--fontFamily) !important;
    font-size: 12px !important;
    font-weight: 400 !important;
    color: #5f6c81 !important;
    width: 80px;
  }

  .hours-field-highlight {
    ::ng-deep .mat-form-field-appearance-outline .mat-form-field-outline {
      color: #d63031 !important;
    }

    ::ng-deep .mat-form-field-appearance-outline:hover .mat-form-field-outline {
      color: #d63031 !important;
    }
  }

  .from-form-field {
    font-family: var(--fontFamily) !important;
    font-size: 12px !important;
    font-weight: 400 !important;
    color: #5f6c81 !important;
    width: 80px;
  }

  .to-form-field {
    font-family: var(--fontFamily) !important;
    font-size: 12px !important;
    font-weight: 400 !important;
    color: #5f6c81 !important;
    width: 80px;
  }

  .add-msg-field {
    font-family: var(--fontFamily) !important;
    width: 100% !important;
    ::ng-deep .mat-form-field-wrapper {
      padding-bottom: 0px !important;
    }
  }

  .add-comment-field {
    font-family: var(--fontFamily) !important;
    font-size: 12px !important;
    font-weight: 400 !important;
    color: #5f6c81 !important;
    width: 240px !important;
  }

  .location-form-field {
    font-family: var(--fontFamily) !important;
    font-size: 12px !important;
    font-weight: 400 !important;
    color: #5f6c81 !important;
    width: 144px;
    display: block;
  }

  .leave-form-field {
    font-family: var(--fontFamily) !important;
    font-size: 12px !important;
    font-weight: 400 !important;
    color: #5f6c81 !important;
    ::ng-deep .mat-form-field-infix {
      width: 157px !important;
    }
  }

  .mr10 {
    margin-right: 10px;
  }

  .mr20 {
    margin-right: 20px;
  }

  .ml10 {
    margin-left: 10px;
  }

  .align-items-column-array {
    display: flex;
    flex-direction: column;
    max-height: 105px;
    min-height: 50px;
  }

  .add-split-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #d4d6d8;
    border-radius: 4px;
    padding: 4px;
    width: 25px;
    height: 25px;
    cursor: pointer;

    .add-icon {
      font-size: 14px;
      width: 14px;
      height: 14px;
      color: #5f6c81;
    }
  }

  .delete-split-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #d4d6d8;
    border-radius: 4px;
    padding: 4px;
    width: 25px;
    height: 25px;
    cursor: pointer;

    .delete-icon {
      font-size: 14px;
      width: 14px;
      height: 14px;
      color: #ff3a46;
    }
  }

  .billable-txt {
    font-family: var(--fontFamily);
    font-size: 12px;
    font-weight: 400;
    color: #6e7b8f;
    margin: 0;
    padding-bottom: 9px;
  }

  .clear-btn {
    font-family: var(--fontFamily);
    font-size: 12px;
    font-weight: 700;
    color: #45546e;
    background-color: white;
    border-radius: 4px;
    padding: 7px 8px;
    border: 1px solid #45546e;
    margin-right: 8px;
  }

  .update-btn {
    font-family: var(--fontFamily);
    font-size: 12px;
    font-weight: 700;
    color: #ffffff;
    background-color: var(--color);
    border-radius: 4px;
    padding: 8px 8px;
    border: none;
  }

  .update-btn:disabled {
    color: #b9c0ca;
    background-color: #e8e9ee;
  }

  .msg-text {
    font-family: var(--fontFamily);
    font-size: 12px;
    font-weight: 400;
    color: #6e7b8f;
    margin: 0;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    width: 200px;
    height: 24px;
  }

  .light-text {
    font-family: var(--fontFamily);
    font-size: 12px;
    font-weight: 400;
    color: #8b95a5;
    margin: 0;
  }

  .add-icon {
    font-size: 18px;
    color: #d9d9d9;
  }

  .add-icon:hover {
    cursor: pointer;
  }

  .leave-type-text {
    font-family: var(--fontFamily);
    font-size: 11px;
    font-weight: 400;
    color: #6e7b8f;
    margin-bottom: 0;
    width: 70px;
  }

  .radio-btn-txt {
    font-family: var(--fontFamily);
    font-size: 12px;
    font-weight: 400;
    color: #5f6c81;
    margin: 0;
  }

  .timer-off-bg {
    width: 40px;
    height: 32px;
    border-radius: 4px;
    border: 1px solid var(--color);
    background-color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 4px;
    cursor: pointer;
  }

  .timer-on-bg {
    background-color: var(--color);
  }

  .timer-off-icon {
    font-size: 18px;
    width: 18px;
    height: 18px;
    color: var(--color);
  }

  .timer-on-icon {
    color: white;
  }
}

.quick-settings-popup {
  overflow: hidden;
  padding: 8px;

  ::ng-deep
    .mat-form-field-appearance-outline.mat-form-field-can-float
    .mat-form-field-outline {
    color: var(--color) !important;
  }

  ::ng-deep
    .mat-form-field-appearance-outline.mat-focused
    .mat-form-field-outline,
  ::ng-deep .mat-form-field-appearance-outline:hover .mat-form-field-outline {
    border-color: var(--color) !important;
  }

  .quick-settings-text {
    font-family: var(--fontFamily);
    font-size: 11px;
    font-weight: 400;
    color: #6e7b8f;
    margin-bottom: 0px;
    cursor: pointer;
  }

  .mt8 {
    margin-top: 8px;
  }

  .align-items-column {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
  }

  .clear-btn {
    font-family: var(--fontFamily);
    font-size: 12px;
    font-weight: 700;
    color: #45546e;
    border: 1px solid #45546e;
    background-color: white;
    border-radius: 4px;
    padding-top: 7px;
    padding-bottom: 7px;
    padding-left: 11px;
    padding-right: 11px;
    cursor: pointer;
  }

  .add-btn {
    font-family: var(--fontFamily);
    color: white;
    font-size: 12px;
    font-weight: 700;
    border-radius: 4px;
    padding-top: 8px;
    padding-bottom: 8px;
    padding-left: 12px;
    padding-right: 12px;
    background-color: var(--color);
    border: none;
    margin-left: 8px;
    cursor: pointer;
  }

  .add-btn:disabled {
    color: #b9c0ca;
    background-color: #e8e9ee;
  }

  .quick-fill-hours-form-field {
    font-family: var(--fontFamily) !important;
    font-size: 12px !important;
    font-weight: 400 !important;
    color: #5f6c81 !important;
    width: 100px;
    ::ng-deep .mat-form-field-wrapper {
      padding-bottom: 0px;
    }
  }

  .planned-hours-form-field {
    font-family: var(--fontFamily) !important;
    font-size: 12px !important;
    font-weight: 400 !important;
    color: #5f6c81 !important;
    width: 70px;
    ::ng-deep .mat-form-field-wrapper {
      padding-bottom: 0px;
    }
  }

  .mat-input-element[type="time"] {
    height: 14px !important;
  }
}

::ng-deep .mat-menu-panel {
  min-width: 0 !important;
  min-height: 0 !important;
  max-width: none !important;
  max-height: none !important;
}

::ng-deep .mat-menu-content {
  padding: 0 !important;
}

input[type="time"]::-webkit-calendar-picker-indicator {
  display: none;
}

.mat-input-element[type="time"] {
  height: 14px !important;
}

.header-name {
  font-family: var(--fontFamily);
  height: 16px;
  font-style: normal;
  font-weight: 400;
  font-size: 10px;
  line-height: 16px;
  letter-spacing: 0.02em;
  text-transform: capitalize;
  color: #b9c0ca;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  margin-bottom: 0px;
}

.header-description {
  height: 16px;
  font-style: normal;
  font-weight: 500;
  font-size: 12px;
  line-height: 16px;
  letter-spacing: 0.02em;
  text-transform: capitalize;
  color: #45546e;
  margin-bottom: 0;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  margin-bottom: 0px;
}

.header-close {
  font-size: x-large;
  font-weight: 600;
  cursor: pointer;
  padding-top: 8px;
  color: #45546e;
}

.light-texts {
  font-family: var(--fontFamily);
  height: 16px;
  font-style: normal;
  font-weight: 600;
  font-size: 12px;
  letter-spacing: 0.02em;
  text-transform: capitalize;
  color: #45546e;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-bottom: 0px;
}

.bold-texts {
  font-family: var(--fontFamily);
  height: 16px;
  font-style: normal;
  font-weight: 500;
  font-size: 12px;
  letter-spacing: 0.02em;
  text-transform: capitalize;
  color: #45546e;
  margin-bottom: 0px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.approvers-extra-display {
  font-family: var(--fontFamily);
  border-radius: 50%;
  height: 24px;
  width: 24px;
  background-color: var(--pColor1);
  border: 2px solid white;
  color: white;
  font-size: 8px;
  font-weight: 700;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: -8px;
}

.hours-popup {
  overflow: hidden;
  padding: 16px;
  width: 232px;

  .light-text {
    font-family: var(--fontFamily);
    font-size: 11px;
    font-weight: 400;
    color: #45546e;
    margin-bottom: 0;
  }

  .light-text-ba {
    font-family: var(--fontFamily);
    font-size: 11px;
    font-weight: 400;
    color: var(--color);
    margin-bottom: 0;
  }

  .light-text-nba {
    font-family: var(--fontFamily);
    font-size: 11px;
    font-weight: 400;
    color: var(--pColor1);
    margin-bottom: 0;
  }

  .bold-text {
    font-family: var(--fontFamily);
    font-size: 11px;
    font-weight: 600;
    color: #45546e;
    margin-bottom: 0;
  }

  .bold-text-ba {
    font-family: var(--fontFamily);
    font-size: 11px;
    font-weight: 600;
    color: var(--color);
    margin-bottom: 0;
  }

  .bold-text-nba {
    font-family: var(--fontFamily);
    font-size: 11px;
    font-weight: 600;
    color: var(--pColor1);
    margin-bottom: 0;
  }

  .hours-divider {
    color: #e8e9ee;
  }

  .align-items-btw {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
}

.leave-popup {
  overflow: hidden;
  padding: 16px;
  width: 264px;

  .align-items-btw {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }

  .leave-divider {
    color: #e8e9ee;
    margin-top: 8px;
    margin-bottom: 8px;
  }

  .light-text {
    font-family: var(--fontFamily);
    font-size: 11px;
    font-weight: 400;
    color: #45546e;
    margin-bottom: 0;
  }

  .bold-text {
    font-family: var(--fontFamily);
    font-size: 11px;
    font-weight: 600;
    color: #45546e;
    margin-bottom: 0;
  }

  .lighter-text {
    font-family: var(--fontFamily);
    font-size: 11px;
    font-weight: 400;
    color: #45546e;
    margin-bottom: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 150px;
  }

  .bolder-text {
    font-family: var(--fontFamily);
    font-size: 11px;
    font-weight: 700;
    margin-bottom: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 150px;
  }
}

.more-options-menu {
  overflow: hidden;
  padding: 16px;

  .more-options-item {
    font-family: var(--fontFamily);
    cursor: pointer;
    font-size: 11px;
    font-weight: 400;
    color: #6e7b8f;
    margin: 0;
  }
}

::ng-deep .default-tooltip {
  background-color: var(--unsubmittedColor) !important;
}

::ng-deep .saved-tooltip {
  background-color: var(--savedColor) !important;
}

::ng-deep .submitted-tooltip {
  background-color: var(--submittedColor) !important;
}

::ng-deep .rejected-tooltip {
  background-color: var(--rejectedColor) !important;
}

::ng-deep .approved-tooltip {
  background-color: var(--approvedColor) !important;
}

::ng-deep .white-spinner circle {
  stroke: var(--defColor) !important;
}

::ng-deep .green-spinner circle {
  stroke: var(--color) !important;
}

.approval-status-text {
  font-size: 12px;
  font-weight: 600;
  font-family: var(--fontFamily);
  margin-bottom: 0px;
}

.task-url-popup {
  overflow: hidden;
  padding: 10px;
  text-transform: capitalize;

  .align-items-btw {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }

  .align-items-center {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
  }

  .clear-icon {
    font-size: 15px;
    color: #45546e;
    padding-left: 12px;
    padding-top: 7px;
    cursor: pointer;
  }

  .light-text {
    font-family: var(--fontFamily);
    font-size: 12px;
    font-weight: 400;
    color: #45546e;
    margin-bottom: 0;
  }

  .bold-text {
    font-family: var(--fontFamily);
    font-size: 14px;
    font-weight: bold;
    color: #45546e;
    margin-bottom: 0;
    width: 200px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }

  .right-arrow {
    display: inline-block;
    vertical-align: middle;
    font-size: 20px;
    padding-top: 1px;
  }

  .url-text {
    margin-top: 3px;
    color: #45546e;
    font-weight: 400;
    font-family: var(--fontFamily);
    letter-spacing: 0.02em;
    text-transform: capitalize;
    font-size: 12px;
    color: #8b95a5 !important;
  }
}

.submission-content-display-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  height: var(--dynamicSubHeight);
}

.submission-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  height: var(--dynamicHeight);
}

::ng-deep .holiday-tooltip {
  background-color: #6ab04c !important;
}

.my-teams-popup {
  padding: 16px;
  width: 227px;
  min-height: 368px;
  max-height: 368px;
  overflow: hidden;

  ::ng-deep
    .mat-form-field-appearance-outline.mat-form-field-can-float
    .mat-form-field-outline {
    color: var(--color) !important;
  }

  ::ng-deep
    .mat-form-field-appearance-outline.mat-focused
    .mat-form-field-outline,
  ::ng-deep .mat-form-field-appearance-outline:hover .mat-form-field-outline {
    border-color: var(--color) !important;
  }

  .hover-color:hover {
    background: #f2f2f2;
    cursor: pointer;
  }

  .my-team-text {
    font-family: var(--fontFamily);
    font-size: 11px;
    font-weight: 400;
    color: #6e7b8f;
  }

  .my-teams-name {
    font-family: var(--fontFamily);
    font-size: 12px;
    font-weight: 400;
    margin-bottom: 0px;
    color: #45546e;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    width: 163px;
  }

  .my-teams-desc {
    font-family: var(--fontFamily);
    font-size: 10px;
    font-weight: 400;
    margin-bottom: 0px;
    color: #8b95a5;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    width: 163px;
  }
}

.header-timesheet-status {
  font-size: 24px;
  cursor: pointer;
  padding-top: 6px;
}

.overflow-div {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

::ng-deep .custom-shepherd-class-ts {
  button {
    font-family: var(--fontFamily) !important;
    background-color: white !important;
    border: 1px solid var(--pColor1) !important;
    color: var(--color) !important;
    font-weight: 600 !important;
    font-size: 12px !important;
    padding: 0.3rem 1rem !important;
  }
}

::ng-deep .custom-shepherd-class-ts .shepherd-cancel-icon {
  background-color: white !important;
  border: none !important;
  font-size: 18px !important;
  color: #45546e !important;
}

::ng-deep .custom-shepherd-class-ts .shepherd-arrow:before {
  border-top-color: var(--pColor1) !important;
}

::ng-deep .custom-shepherd-class-ts .shepherd-header {
  height: 28px !important;
}

::ng-deep .custom-shepherd-class-ts .shepherd-text {
  font-family: var(--fontFamily) !important;
  font-size: 13px !important;
  color: var(--color) !important;
}

::ng-deep .tooltip-details-text {
  font-family: var(--fontFamily);
  font-size: 12px;
  font-weight: 500;
  color: #111434;
  margin: 0;
  display: flex;
  justify-content: flex-start;
}

::ng-deep .dx-tooltip-wrapper .dx-overlay-content .dx-popup-content {
  background-color: white;
  border: 1px solid #111434;
}

::ng-deep .dx-popover-wrapper .dx-popover-arrow {
  display: block !important;
}

::ng-deep .dx-tooltip-wrapper.dx-popover-wrapper .dx-popover-arrow {
  display: block !important;
}

::ng-deep .dx-popover-wrapper .dx-popover-arrow::after,
.dx-popover-wrapper.dx-popover-without-title .dx-popover-arrow::after {
  background: #fff;
  box-shadow: 0px 0px 0px 1px rgba(0, 0, 0, 0.1),
    0 5px 7px 3px rgba(0, 0, 0, 0.1), 0 9px 3px -6px rgba(0, 0, 0, 0.1),
    1px 0px 3px 1px rgba(0, 0, 0, 0.2);
}

::ng-deep .dx-popup-content {
  padding: 8px !important;
}

::ng-deep .dx-popup-wrapper > .dx-overlay-content {
  box-shadow: 0px 0px 0px 1px rgba(0, 0, 0, 0.1),
    0 5px 7px 3px rgba(0, 0, 0, 0.1), 0 9px 3px -6px rgba(0, 0, 0, 0.1),
    1px 0px 3px 1px rgba(0, 0, 0, 0.2);
}

::ng-deep .mat-calendar-arrow.mat-calendar-invert {
  display: none;
}

::ng-deep .mat-calendar-period-button {
  pointer-events: none;
}

::ng-deep .mat-datepicker-toggle {
  font-size: 10px;
}

::ng-deep .dx-popover-wrapper {
  z-index: 1 !important;
}

.manual-approver-name {
  font-family: var(--fontFamily);
  font-size: 12px;
  font-weight: 400;
  color: #45546e;
}

::ng-deep .mat-option.mat-active {
  background: none !important;
}

::ng-deep .mat-select-panel .mat-option.mat-selected:not(.mat-option-multiple) {
  background: rgba(0, 0, 0, 0.12) !important;
}

.timer-stop-resume {
  gap: 10px;

  .stop-button {
    width: 62px;
    height: 24px;
    border: 1px solid #ff3a46;
    border-radius: 4px;
    gap: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }

  .stop-icon {
    width: 8px;
    height: 8px;
    background-color: #ff3a46;
  }

  .stop-text {
    font-family: var(--fontFamily);
    font-size: 10px;
    font-weight: 700;
    color: #ff3a46;
  }

  .pause-button {
    width: 62px;
    height: 24px;
    border: 1px solid #13c2c2;
    border-radius: 4px;
    gap: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }

  .pause-icon {
    width: 14px;
    height: 14px;
    color: #13c2c2;
    font-size: 14px;
  }

  .pause-text {
    font-family: var(--fontFamily);
    font-size: 10px;
    font-weight: 700;
    color: #13c2c2;
  }

  .resume-button {
    width: 72px;
    height: 24px;
    border: 1px solid #52c41a;
    border-radius: 4px;
    gap: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }

  .resume-icon {
    width: 14px;
    height: 14px;
    color: #52c41a;
    font-size: 15px;
  }

  .resume-text {
    font-family: var(--fontFamily);
    font-size: 10px;
    font-weight: 700;
    color: #52c41a;
  }

  .elapsed-time {
    font-size: 14px;
    font-weight: 600;
    font-family: var(--fontFamily);
    color: #52c41a;
  }
}

.comments-overlap {
  position: absolute;
  left: 80%;
  z-index: 1;
  display: flex;
  align-items: center;
}

.current-day{
  background-color: #79BA440D;
  height: var(--dividerHeight);
}

.week-off-day{
  background-color: #F6F6F6;
  height: var(--dividerHeight);
}

.wo-disabled{
  background-color: #E8E9EE;
}

.wo-not-disabled{
  background-color: #FFFFFF;
}

.custom-autocomplete-panel{
  max-height: 200px;
  overflow-y: scroll;
}
