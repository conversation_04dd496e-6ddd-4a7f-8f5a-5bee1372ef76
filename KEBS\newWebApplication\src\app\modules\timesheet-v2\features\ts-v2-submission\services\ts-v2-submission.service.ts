import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject } from 'rxjs';
import { ShepherdService } from 'angular-shepherd';
@Injectable({
  providedIn: 'root',
})
export class TsV2SubmissionService {
  constructor(
    private _http: HttpClient,
    private shepherdService: ShepherdService
  ) {}

  getBasicTimesheetConfigurations(oid, aid) {
    return this._http.post(
      'api/timesheetv2/Settings/getBasicTimesheetConfigurations',
      {
        oid: oid,
        aid: aid,
      }
    );
  }

  getTimesheetTermsAndCondition(oid, aid) {
    return this._http.post(
      'api/timesheetv2/Settings/getTimeSheetTermsAndCondition',
      {
        oid: oid,
        aid: aid,
      }
    );
  }

  getEmployeeDetails(oid, aid) {
    return this._http.post('api/timesheetv2/Settings/getEmployeeDetails', {
      oid: oid,
      aid: aid,
    });
  }

  getTimesheetUiConfiguration(oid, aid) {
    return this._http.post(
      'api/timesheetv2/Settings/getTimesheetUiConfiguration',
      {
        oid: oid,
        aid: aid,
        id: 1,
      }
    );
  }

  getCostCenterListForSearch(
    aid,
    oid,
    startDate,
    endDate,
    isFromTs,
    search_param
  ) {
    return this._http.post(
      'api/timesheetv2/Submission/getCostCenterListForSearch',
      {
        aid: aid,
        oid: oid,
        startDate: startDate,
        endDate: endDate,
        isFromTs: isFromTs,
        search_param: search_param,
      }
    );
  }

  getHolidayCalendar(oid, aid, startDate, endDate) {
    return this._http.post('api/timesheetv2/Submission/getHolidayCalendar', {
      oid: oid,
      associate_id: aid,
      startDate: startDate,
      endDate: endDate,
    });
  }

  getWorkflowProperties(wfDetails) {
    return this._http.post('/api/wfPrimary/getWorkflowProperties', {
      applicationId: wfDetails.applicationId,
    });
  }

  getApproversHierarchy(hierarchyParams) {
    return this._http.post(
      '/api/wfPrimary/getApproversHierarchy',
      hierarchyParams
    );
  }

  getEmployeeTimesheetQuota(oid, aid) {
    return this._http.post(
      '/api/timesheetv2/Settings/getEmployeeTimesheetQuota',
      {
        oid: oid,
        aid: aid,
      }
    );
  }

  getLeaveDetailsWithBalance(oid, aid) {
    return this._http.post(
      '/api/timesheetv2/Submission/getLeaveDetailsWithBalance',
      {
        oid: oid,
        aid: aid,
      }
    );
  }

  getLocationListForTimesheet() {
    return this._http.post(
      '/api/timesheetv2/Submission/getLocationListForTimesheet',
      {}
    );
  }

  getTimesheetAttendanceType() {
    return this._http.post(
      '/api/timesheetv2/Master/getTimesheetAttendanceType',
      {}
    );
  }

  getTimesheetBillingType() {
    return this._http.post(
      '/api/timesheetv2/Master/getTimesheetBillingType',
      {}
    );
  }

  getTimesheetInsertionType() {
    return this._http.post(
      '/api/timesheetv2/Master/getTimesheetInsertionType',
      {}
    );
  }

  getTimesheetStatus() {
    return this._http.post('/api/timesheetv2/Master/getTimesheetStatus', {});
  }

  getPrefillTaskAndCostCenter(oid, aid, startDate, endDate) {
    return this._http.post(
      'api/timesheetv2/Submission/getPrefillTaskAndCostCenter',
      {
        oid: oid,
        aid: aid,
        startDate: startDate,
        endDate: endDate,
      }
    );
  }

  saveAssocTSData(data) {
    return this._http.post('api/timesheetv2/Submission/saveAssocTSData', data);
  }

  retrieveTSDataForEmployee(aid, oid, startDate, endDate) {
    return this._http.post(
      'api/timesheetv2/Retrieval/retrieveTSDataForEmployee',
      {
        aid: aid,
        oid: oid,
        startDate: startDate,
        endDate: endDate,
      }
    );
  }

  removeFromTimesheet(data) {
    return this._http.post(
      'api/timesheetv2/Submission/removeFromTimesheet',
      data
    );
  }

  submitAssocTSData(data) {
    return this._http.post(
      'api/timesheetv2/Submission/submitAssocTSData',
      data
    );
  }

  getPendingLeavesOfEmployee(oid, aid, startDate, endDate) {
    return this._http.post(
      'api/timesheetv2/Submission/getPendingLeavesOfEmployee',
      {
        oid: oid,
        aid: aid,
        startDate: startDate,
        endDate: endDate,
      }
    );
  }

  editAssocTSData(data) {
    return this._http.post('api/timesheetv2/Submission/editAssocTSData', data);
  }

  getTimesheetStyleConfig() {
    return this._http.post(
      '/api/timesheetv2/Master/getTimesheetStyleConfig',
      {}
    );
  }

  retrieveTSApprovalStatus(aid, oid, startDate, endDate, costcenter) {
    return this._http.post(
      '/api/timesheetv2/Retrieval/retrieveTSApprovalStatus',
      {
        aid: aid,
        oid: oid,
        startDate: startDate,
        endDate: endDate,
        costcenter: costcenter,
      }
    );
  }

  retrieveOverAllTSStatusWeekWise(aid, oid, weekDetails) {
    return this._http.post(
      '/api/timesheetv2/Retrieval/retrieveOverAllTSStatusWeekWise',
      {
        aid: aid,
        oid: oid,
        weekDetails: weekDetails,
      }
    );
  }

  getSubProjectList(aid, oid, startDate, endDate, costCenterId, search_param) {
    return this._http.post('/api/timesheetv2/Submission/getSubProjectList', {
      aid: aid,
      oid: oid,
      startDate: startDate,
      endDate: endDate,
      costCenterId: costCenterId,
      search_param: search_param,
    });
  }

  retrieveTaskFromCC(
    aid,
    oid,
    startDate,
    endDate,
    costCenterId,
    subProjectId,
    startIndex,
    search_param,
    costCenterType
  ) {
    return this._http.post('/api/timesheetv2/Submission/retrieveTaskFromCC', {
      aid: aid,
      oid: oid,
      startDate: startDate,
      endDate: endDate,
      costCenterId: costCenterId,
      subProjectId: subProjectId,
      startIndex: startIndex,
      search_param: search_param,
      costCenterType: costCenterType,
    });
  }

  retrieveTimesheetHistory(
    aid,
    oid,
    startDate,
    endDate,
    costCenterId,
    costCenterType
  ) {
    return this._http.post(
      'api/timesheetv2/Retrieval/retrieveTimesheetHistory',
      {
        oid: oid,
        aid: aid,
        startDate: startDate,
        endDate: endDate,
        costCenterId: costCenterId,
        costCenterType: costCenterType,
      }
    );
  }

  retrievePreviousWeekData(
    aid,
    oid,
    startDate,
    endDate,
    currStartDate,
    currEndDate,
    absenceCCFlag
  ) {
    return this._http.post(
      '/api/timesheetv2/Retrieval/retrievePreviousWeekData',
      {
        oid: oid,
        aid: aid,
        startDate: startDate,
        endDate: endDate,
        currStartDate: currStartDate,
        currEndDate: currEndDate,
        absenceCCFlag: absenceCCFlag,
      }
    );
  }

  storePlannedHoursDetails(
    aid,
    oid,
    costCenterId,
    subProjectId,
    taskId,
    taskStartDate,
    taskEndDate,
    hours
  ) {
    return this._http.post(
      '/api/timesheetv2/Submission/storePlannedHoursDetails',
      {
        oid: oid,
        aid: aid,
        costCenterId: costCenterId,
        subProjectId: subProjectId,
        taskId: taskId,
        taskStartDate: taskStartDate,
        taskEndDate: taskEndDate,
        hours: hours,
      }
    );
  }

  getReportingEmployeeDetails(
    aid,
    oid,
    search_param,
    limit,
    startDate,
    endDate
  ) {
    return this._http.post(
      'api/timesheetv2/Settings/getReportingEmployeeDetails',
      {
        aid: aid,
        oid: oid,
        search_param: search_param,
        limit: limit,
        startDate: startDate,
        endDate: endDate,
      }
    );
  }

  getPlannedHoursHistory(
    aid,
    oid,
    costCenterId,
    subProjectId,
    taskId,
    searchText,
    limit
  ) {
    return this._http.post(
      '/api/timesheetv2/Submission/getPlannedHoursHistory',
      {
        oid: oid,
        aid: aid,
        costCenterId: costCenterId,
        subProjectId: subProjectId,
        taskId: taskId,
        searchText: searchText,
        limit: limit,
      }
    );
  }

  getTimesheetOverviewUIConfiguration(oid, aid) {
    return this._http.post(
      'api/timesheetv2/Settings/getTimesheetOverviewUIConfiguration',
      {
        oid: oid,
        aid: aid,
        id: 3,
      }
    );
  }

  checkIfFirstTimeUserEntryInTS(oid, aid) {
    return this._http.post(
      'api/timesheetv2/Settings/checkIfFirstTimeUserEntryInTS',
      {
        oid: oid,
        aid: aid,
      }
    );
  }

  storeFirstTimeUserEntryInTS(oid, aid, isWalkThroughCompleted) {
    return this._http.post(
      'api/timesheetv2/Settings/storeFirstTimeUserEntryInTS',
      {
        oid: oid,
        aid: aid,
        isWalkThroughCompleted: isWalkThroughCompleted,
      }
    );
  }

  startTour = (defaultStepOptions, defaultSteps) => {
    this.shepherdService.defaultStepOptions = defaultStepOptions;
    this.shepherdService.modal = true;
    this.shepherdService.confirmCancel = false;
    this.shepherdService.addSteps(defaultSteps);
    this.shepherdService.start();
  };

  updateLastWorkingDayOfEmployee(aid, oid, date, lwd_flag) {
    return this._http.post(
      '/api/timesheetv2/Submission/updateLastWorkingDayOfEmployee',
      {
        aid: aid,
        oid: oid,
        date: date,
        flag: lwd_flag,
      }
    );
  }

  getErrorDetails() {
    return this._http.post('/api/timesheetv2/Master/getErrorDetails', {});
  }

  retrieveWalkThroughConfigurations() {
    return this._http.post(
      'api/timesheetv2/Settings/retrieveWalkThroughConfigurations',
      {}
    );
  }

  getApproverManualSelection(aid, oid, startDate, endDate, costcentreData) {
    return this._http.post(
      'api/timesheetv2/Approvals/getApproverManualSelection',
      {
        aid: aid,
        oid: oid,
        startDate: startDate,
        endDate: endDate,
        costcentreData: costcentreData,
      }
    );
  }

  getHolidayDetails(oid, aid) {
    return this._http.post('/api/timesheetv2/Submission/getHolidayDetails', {
      oid: oid,
      aid: aid,
    });
  }

  retrieveTSApprovalStatusNew(aid, oid, startDate, endDate, costcenter) {
    return this._http.post(
      '/api/timesheetv2/Retrieval/retrieveTSApprovalStatusNew',
      {
        aid: aid,
        oid: oid,
        startDate: startDate,
        endDate: endDate,
        costcenter: costcenter,
      }
    );
  }

  getTaskDetailsForTheSubProject(
    aid,
    oid,
    startDate,
    endDate,
    costCenterId,
    subProjectId,
    costCenterType
  ) {
    return this._http.post(
      '/api/timesheetv2/Submission/getTaskDetailsForTheSubProject',
      {
        aid: aid,
        oid: oid,
        startDate: startDate,
        endDate: endDate,
        costCenterId: costCenterId,
        subProjectId: subProjectId,
        costCenterType: costCenterType,
      }
    );
  }

  storeClockTimeDetails(
    oid,
    aid,
    initiatorAId,
    costCenterId,
    costcentertype,
    subProjectId,
    taskId,
    date,
    oprType,
    time,
    hoursworked,
    timeZoneOffset
  ) {
    console.log('TimeZoneOffset');
    console.log(timeZoneOffset);
    return this._http.post(
      '/api/timesheetv2/Submission/storeClockTimeDetails',
      {
        oid: oid,
        aid: aid,
        initiatorAId: initiatorAId,
        costCenterId: costCenterId,
        costcentertype: costcentertype,
        subProjectId: subProjectId,
        taskId: taskId,
        date: date,
        oprType: oprType,
        time: time,
        hoursworked: hoursworked,
        timeZoneOffset: timeZoneOffset,
      }
    );
  }

  getDocumentLink(flag) {
    return this._http.post(
      '/api/timesheetv2/Retrieval/getTimesheetHelpDocument',
      {
        flag: flag,
      }
    );
  }

  downloadEmployeeTimesheet(aid, monthStartDate, monthEndDate) {
    return this._http.post(
      '/api/timesheetv2/Retrieval/downloadEmployeeTimesheet',
      {
        aid: aid,
        monthStartDate: monthStartDate,
        monthEndDate: monthEndDate,
      }
    );
  }

  getTimesheetSettingsMaster() {
    return this._http.post(
      'api/timesheetv2/Settings/getTimesheetSettingsMaster',
      {}
    );
  }

  getNotificationTemplateMaster(){
    return this._http.post("/api/timesheetv2/Master/getNotificationMasterData",{
    });
  }

  updateNotificationMasterData(id, text){
    return this._http.post("/api/timesheetv2/Master/updateNotificationMasterData",{
      id: id,
      templateText: text
    });
  }
}
