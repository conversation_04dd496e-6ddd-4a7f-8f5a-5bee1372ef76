import { Injectable } from '@angular/core';
import {
  CanActivate,
  ActivatedRouteSnapshot,
  RouterStateSnapshot,
} from '@angular/router';
import { TsV2Service } from '../services/ts-v2.service';
import { LoginService } from 'src/app/services/login/login.service';
import { TsAccessModalComponent } from '../features/ts-v2-submission/lazy-loaded-components/ts-access-modal/ts-access-modal.component';
import { MatDialog } from '@angular/material/dialog';

@Injectable({
  providedIn: 'root',
})
export class AuthGuardTsV2Guard implements CanActivate {
  currentUser: any;
  oid: any;
  aid: any;
  isTsAccessAllowed: boolean = false;

  constructor(
    private service: TsV2Service,
    private authService: LoginService,
    private _dialog: MatDialog
  ) {
    this.currentUser = this.authService.getProfile().profile;
    this.oid = this.currentUser.oid;
    this.aid = this.currentUser.aid;
  }

  async canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Promise<boolean> {
    this.getTimesheetStyleConfig();
    await this.authGaurdForTimesheet();
    if (this.isTsAccessAllowed) {
      return true;
    } else {
      this._dialog.open(TsAccessModalComponent, {
        data: { val: '2' },
        disableClose: true,
        width: '500px',
      });
      return false;
    }
  }

  async authGaurdForTimesheet() {
    return new Promise((resolve, reject) =>
      this.service.authGaurdForTimesheet(this.oid, this.aid).subscribe({
        next: (res) => {
          if (res['messType'] == 'S') {
            this.isTsAccessAllowed = res['data'];
          } else {
            this.isTsAccessAllowed = false;
          }
          resolve(true);
        },
        error: (err) => {
          this.isTsAccessAllowed = false;
          reject();
        },
      })
    );
  }

  async getTimesheetStyleConfig() {
    return new Promise((resolve, reject) =>
      this.service.getTimesheetStyleConfig().subscribe({
        next: (res) => {
          if (res['messType'] == 'S') {
            document.documentElement.style.setProperty(
              '--color',
              res['data'][0]?.color
            );
            document.documentElement.style.setProperty(
              '--defColor',
              res['data'][0]?.def_color
            );
            document.documentElement.style.setProperty(
              '--fontFamily',
              res['data'][0]?.fontfamily
            );
            document.documentElement.style.setProperty(
              '--pColor1',
              res['data'][0]?.p_color1
            );
          }
          resolve(true);
        },
        error: (err) => {
          reject();
        },
      })
    );
  }
}
