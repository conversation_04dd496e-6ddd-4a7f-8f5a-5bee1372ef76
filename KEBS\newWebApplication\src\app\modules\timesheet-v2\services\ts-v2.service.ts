import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';

@Injectable({
  providedIn: 'root',
})
export class TsV2Service {
  constructor(private _http: HttpClient) {}

  authGaurdForTimesheet(oid, aid) {
    return this._http.post('api/timesheetv2/Settings/authGaurdForTimesheet', {
      oid: oid,
      aid: aid,
    });
  }

  getTimesheetStyleConfig() {
    return this._http.post(
      '/api/timesheetv2/Master/getTimesheetStyleConfig',
      {}
    );
  }

  checkTimesheetAccess(){
    return this._http.post('/api/timesheetv2/Settings/checkTimesheetAccess', {
    });
  }
}
