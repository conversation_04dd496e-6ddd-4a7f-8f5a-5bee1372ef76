import { AuthGuardTsV2Guard } from './guards/auth-guard-ts-v2.guard';
import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { TsV2LandingPageComponent } from './ts-v2-landing-page/ts-v2-landing-page.component';

const routes: Routes = [
  {
    path: '',
    component: TsV2LandingPageComponent,
    children: [
      {
        path: '',
        redirectTo: 'submission',
        pathMatch: 'full',
        canActivate: [AuthGuardTsV2Guard],
      },
      {
        path: 'submission',
        loadChildren: () =>
          import('./features/ts-v2-submission/ts-v2-submission.module').then(
            (m) => m.TsV2SubmissionModule
          ),
        data: { breadcrumb: 'Timesheet' },
        canActivate: [AuthGuardTsV2Guard],
      },
      {
        path: 'approvals',
        loadChildren: () =>
          import('./features/ts-v2-approval/ts-v2-approval.module').then(
            (m) => m.TsV2ApprovalModule
          ),
        data: { breadcrumb: 'Timesheet' },
        canActivate: [AuthGuardTsV2Guard],
      },
      {
        path: 'settings',
        loadChildren: () =>
          import('./features/ts-v2-settings/ts-v2-settings.module').then(
            (m) => m.TsV2SettingsModule
          ),
        data: { breadcrumb: 'Timesheet' },
        canActivate: [AuthGuardTsV2Guard],
      },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class TimesheetV2RoutingModule {}
