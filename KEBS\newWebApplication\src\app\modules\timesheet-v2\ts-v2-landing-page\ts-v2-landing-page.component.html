<div class="tab">
  <nav mat-tab-nav-bar>
    <div *ngFor="let link of timesheetTabLinks">
      <a
        class="link-color"
        mat-tab-link
        [routerLink]="link.path"
        [routerLinkActive]="['active']"
        #rla="routerLinkActive"
        [active]="rla.isActive"
        [ngClass]="{ 'active-link': rla.isActive }"
        *ngIf="link.toDisplay"
        >{{ link.label }}</a
      >
    </div>
  </nav>
</div>
<div class="project-tab-content">
  <router-outlet></router-outlet>
  <!-- <footer><div class="bottom-stroke"></div></footer> -->
</div>
