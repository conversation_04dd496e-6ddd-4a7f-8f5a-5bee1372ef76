nav {
  position: fixed;
  width: 100%;
  z-index: 14;
  background: #f1f3f8;
}

.project-tab-content {
  padding-top: 40px !important;
  background-color: #e5e5e5;
}

.tab ::ng-deep .mat-tab-header {
  border-bottom: 1px solid #f1f3f8 !important;
}

.tab ::ng-deep .mat-tab-nav-bar.mat-primary .mat-ink-bar {
  background-color: var(--color) !important;
}

.tab ::ng-deep .mat-tab-nav-bar {
  padding-left: 16px !important;
  margin-left: 0px !important;
}

.tab ::ng-deep .mat-tab-link {
  height: 39px !important;
}

.link-color {
  color: rgba(0, 0, 0, 0.87);
  font-weight: 500 !important;
  font-size: 14px !important;
  text-decoration: none !important;
}

.bottom-stroke {
  height: 8px;
  background-color: var(--color);
}

.active-link {
  color: var(--color) !important;
}
