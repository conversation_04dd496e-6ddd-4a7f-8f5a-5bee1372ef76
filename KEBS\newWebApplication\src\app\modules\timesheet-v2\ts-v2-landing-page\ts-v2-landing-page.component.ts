import { Component, OnInit, ViewChild } from '@angular/core';
import { RouterLinkActive } from '@angular/router';
import { TsV2Service } from '../services/ts-v2.service';
import { Router } from '@angular/router';

@Component({
  selector: 'app-ts-v2-landing-page',
  templateUrl: './ts-v2-landing-page.component.html',
  styleUrls: ['./ts-v2-landing-page.component.scss'],
})
export class TsV2LandingPageComponent implements OnInit {
  @ViewChild(RouterLinkActive) routerLinkActive: RouterLinkActive;

  constructor(private tsService: TsV2Service, private router: Router) {}

  async ngOnInit() {
    this.checkTimesheetAccess();
  }

  timesheetTabLinks: any = [
    { label: 'Submission', path: 'submission', toDisplay: true },
    { label: 'Approvals', path: 'approvals', toDisplay: true },
    { label: 'Settings', path: 'settings', toDisplay: true },
  ];
  /**
   * Timesheet Access Control
   */
  checkTimesheetAccess(){
    this.tsService.checkTimesheetAccess()
    .subscribe( async(res) =>{
      if(res['messType'] == "S")
        {
          this.timesheetTabLinks[0].toDisplay = res['submFlag'];
          this.timesheetTabLinks[2].toDisplay = res['settingAccess'];
          if(!res['submFlag']){
            this.router.navigateByUrl('/main/timesheetv2/approvals');
          }
        }
    });

  }
}
