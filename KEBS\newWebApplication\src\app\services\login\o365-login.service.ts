import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { AuthenticationResult, PublicClientApplication } from '@azure/msal-browser';
import { Tokens } from 'src/app/models/auth-models/token';
import { ToasterService } from '../toaster-service/toaster.service';
import { LoginService } from './login.service';
import { TenantService } from '../tenant-service/tenant.service';
import Swal from 'sweetalert2';
import { UtilityService } from '../utility/utility.service';
import { MsalService, MsalBroadcastService, MSAL_GUARD_CONFIG, MsalGuardConfiguration } from '@azure/msal-angular';

@Injectable({
  providedIn: 'root',
})
export class O365LoginService {
  clientId: string;
  previousPath: string;
  loggedIn: Boolean
  constructor(
    private _login: LoginService,
    private _http: HttpClient,
    private _toaster: ToasterService,
    private _tenant: TenantService,
    private _util: UtilityService,
    private _auth: MsalService
  ) {
    this._tenant.getTenantInfo().then((res: any) => {
      this.clientId = res.o365_base_app_id;
    });
  }
  deleteAllCookies() {
    const cookies = document.cookie.split(";");

    for (let i = 0; i < cookies.length; i++) {
      const cookie = cookies[i];
      const eqPos = cookie.indexOf("=");
      const name = eqPos > -1 ? cookie.substr(0, eqPos) : cookie;
      document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT";
    }
  }

  oLogin() {
    sessionStorage.clear();
    this.deleteAllCookies();
    return new Promise(async (resolve, reject) => {
      let accountId = '';

      const myMsal = new PublicClientApplication(this.msalConfig);
      const loginRequest = {
        scopes: [`https://graph.microsoft.com/.default`],
      };
      myMsal
        .loginPopup(loginRequest)
        .then(function (loginResponse) {
          accountId = loginResponse.account.homeAccountId;
          myMsal.setActiveAccount(loginResponse.account);
          // Display signed-in user content, call API, etc.
          console.log("Login response")
          console.log(loginResponse)
          resolve(loginResponse);
        })
        .catch(function (error) {
          //login failure
          console.log(error);
          reject(error);
        });
    });
  }

  async oLogout() {
    const myMsal = new PublicClientApplication(this.msalConfig);
    const request = {
      scopes: [`https://graph.microsoft.com/.default`],
      account: myMsal.getActiveAccount()[0],
    };
    myMsal.logoutRedirect(request);

    console.log("Inside O365 logout")
    let pinnedApps = localStorage.getItem("pinnedApps")
    const reportItems: any = this._util.getItemsByRegex('^Report_');
    console.log(reportItems);
    localStorage.clear();

    if(reportItems && reportItems.length > 0) {
      for(let i=0; i<reportItems.length; i++) {
        localStorage.setItem(reportItems[i].key, reportItems[i].value)
      }
    }
    localStorage.setItem("pinnedApps",pinnedApps)
    sessionStorage.clear();
  }

  get msalConfig() {
    return {
      // auth: {
      //   clientId: this.clientId,
      //   redirectUri: window.location.href,
      //   postLogoutRedirectUri: window.location.origin + '/login',
      // },
      auth: {
        clientId: this._tenant.tenantInfo?.o365_base_app_id || '',
        authority: `https://login.microsoftonline.com/${this._tenant.tenantInfo?.is_app_reg_multitenant ? "common" : this._tenant.tenantInfo?.o365_tenant_id}`,
        redirectUri: window.location.href,
        postLogoutRedirectUri: window.location.origin + '/login',
      },
      cache: {
        cacheLocation: 'localStorage' as 'localStorage',
        storeAuthStateInCookie: true,
      },
    };
  }

  getTokenForOlogin = (email, dbName, tenantId, token, oid) => {
    return new Promise((resolve, reject) => {
      this._http
        .post(
          '/api/auth/authenticate/o365UserLogin',
          {
            email: email,
            dbName: dbName,
            tenantId: tenantId,
            oid: oid,
          },
          {
            headers: new HttpHeaders({
              Authorization: `Bearer ${token}`,
            }),
          }
        )
        .subscribe(
          (tokens: Tokens) => {
            this._login.doLoginUser(email, tokens);
            resolve(true);
          },
          (err) => {
            this._toaster.showError(err.error.msg, '', 3000);
            reject(err);
          }
        );
    });
  };

  getToken = async () => {
    return new Promise(async (resolve, reject) => {
      let isO365LoginScreenOpened: string = localStorage.getItem(
        'isO365LoginScreenOpened'
      );

      try {
        const myMsal = new PublicClientApplication(this.msalConfig);
        const loginRequest = {
          scopes: [`https://graph.microsoft.com/.default`],
          account: myMsal.getActiveAccount()[0],
        };
        myMsal
          .acquireTokenSilent(loginRequest)
          .then(function (loginResponse) {
            resolve(loginResponse.accessToken);
          })
          .catch(function (error) {
            //login failure
            console.log(error);
            let isO365Cancelled = sessionStorage.getItem('isO365Cancelled')
            console.log(isO365Cancelled)
            if (
              (isO365LoginScreenOpened === 'false' ||
                isO365LoginScreenOpened == null) && this._tenant.tenantInfo?.is_o365_pop_up_required
            )
              // window.open(`${window.location.origin}/o365Login`);
              Swal.fire({
                title: 'It seems this application requires you to login using Microsoft O365 for proper experience',
                text: "Kindly re-login using Microsoft or continue with the application by clicking on cancel button",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Login with O365'
              }).then((result) => {
                console.log(result)
                if (result.isConfirmed) {
                  window.open(
                    `${window.location.origin}/o365Login`
                    // 'myWindow',
                    // 'width=1000,height=1000'
                  );
                }
                else {
                  sessionStorage.setItem("isO365Cancelled", "true")
                }
              })
            reject(error);
          });
      } catch (err) {
        let isO365Cancelled = sessionStorage.getItem('isO365Cancelled')
        console.log(isO365Cancelled)
        if (
          (isO365LoginScreenOpened === 'false' ||
            isO365LoginScreenOpened == null) && (isO365Cancelled === "false" || isO365Cancelled == null) && this._tenant.tenantInfo?.is_o365_pop_up_required
        )
          // window.open(
          //   `${window.location.origin}/o365Login`
          //   // 'myWindow',
          //   // 'width=1000,height=1000'
          // );
          Swal.fire({
            title: 'Some features in this application requires you to login with O365',
            text: "Kindly login with O365 or continue with the application by clicking on the cancel button",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Login with O365'
          }).then((result) => {
            console.log(result)
            if (result.isConfirmed) {
              window.open(
                `${window.location.origin}/o365Login`
                // 'myWindow',
                // 'width=1000,height=1000'
              );
            }
            else {
              sessionStorage.setItem("isO365Cancelled", "true")
            }
          })
      }
    });
  };


  async checkAndLogin() {
    const account = this._auth.instance.getActiveAccount();
    console.log(account)
    if (account) {
      // If there's an active account, try silent token acquisition
      return await this._auth.acquireTokenSilent({
        scopes: ['https://graph.microsoft.com/.default'],
        account: account
      });
    } else {
      // If no active account, try SSO silent login
      return await this._auth.ssoSilent({
        scopes: ['https://graph.microsoft.com/.default']
      });
    }
  }

  attemptSilentSSO() {
    this._auth.ssoSilent({
      scopes: ['user.read']
    }).subscribe({
      next: (result: AuthenticationResult) => {
        console.log('Silent SSO successful');
        this._auth.instance.setActiveAccount(result.account);
      },
      error: (error) => {
        console.log('Silent SSO failed, user interaction required');
        // Optionally, you can start an interactive login here
        // this.login();
      }
    });
  }

  acquireTokenSilently() {
    this._auth.acquireTokenSilent({
      scopes: ['user.read']
    }).subscribe({
      next: (result: AuthenticationResult) => {
        console.log('Token acquired silently');
        // You can now use the token for API calls
      },
      error: (error) => {
        console.error('Error acquiring token silently', error);
        // Handle error, possibly by initiating an interactive login
      }
    });
  }
  
  


}
