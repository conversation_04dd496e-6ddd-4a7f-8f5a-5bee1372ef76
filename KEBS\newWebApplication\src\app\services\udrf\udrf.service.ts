import { Injectable } from '@angular/core';
import * as moment from "moment";
import { HttpClient } from "@angular/common/http";
import * as _ from 'underscore';
import { LoginService } from "src/app/services/login/login.service";
import { UtilityService } from "src/app/services/utility/utility.service";
import { FormBuilder, FormControl } from '@angular/forms';
import { ErrorService } from '../error/error.service';
import { MatDialog } from "@angular/material/dialog";
import { MailUtilityService } from 'mail-box-modal';
import * as clone from 'clone';
import { GraphApiService } from '../graph/graph-api.service';
import { Subject } from "rxjs";
import { takeUntil } from "rxjs/operators";

@Injectable({
  providedIn: 'root',
})

export class UdrfService {

  protected _onResetUdrfData = new Subject<void>();

  ListCTAFormGroup = this.formBuilder.group({
    long_description: [""],
    secondaryOwner: [""],
    tertiaryOwner: [""],
    logs: [""],
    comments: [""],
    dueOn: [""],
    status: [""],
    values: this.formBuilder.array([])
  });

  maxFileSize = 10 * 1024 * 1024; // 10MB;

  udrfUiData = {
    udrfHasDmcConfigTemp: false,
    isMigrationCockpitMainApp: false,
    udrfHasDmcConfig: false,
    udrfDmcConfig: [],
    inlineEditTsData: {},
    activateInlineEditTs: () => { },
    onColumnClickItem: "",
    showItemDataCount: false,
    itemDataType: "",
    bookmarkId: "",
    horizontalScroll: false,
    totalItemDataCount: 0,
    buttonToggleItems: [],
    showButtonToggle: false,
    selectedButtonToggle: "",
    toggleSelectedButton: () => { },
    showSearchBar: true,
    showActionButtons: true,
    showUdrfModalButton: true,
    showSettingsModalButton: true,
    showReportDownloadButton: false,
    showPeopleReportDownloadBtn: false,
    isReportDownloading: false,
    showColumnConfigButton: true,
    showGroupByButton: false,
    notifyRelease: false,
    notifyReleaseAnimated: false,
    showNewReleasesButton: false,
    summaryCards: [],
    udrfItemStatusColor: [],
    udrfItemProposalStatusColor: [],
    onProgressItem: () => { },
    onCompleteItem: () => { },
    onCompleteItemResponse: "",
    resolveVisibleSummaryCards: () => { },
    summaryCardsSelected: () => { },
    itemcardSelected: () => [],
    downloadItemDataReport: () => { },
    itemDataScrollDown: () => { },
    callInlineEditApi: () => { },
    updateItemCard: () => { },
    s3URL: null,
    initFileUploaderOptions: () => { },
    attachFile: () => { },
    deleteFile: () => { },
    downloadFile: () => { },
    closeCTA: () => { },
    collapseAllCard: () => { },
    openComments: () => { },
    openQuickCta: () => { },
    openEdit: () => { },
    onApproval: () => { },
    openInNewTab: () => { },
    openFileDownload: () => { },
    resolveColumnConfig: () => { },
    showHierarchy: () => { },
    openDeleteButton: () => { },
    openPopUp: () => { },
    openModal: () => { },
    openModal1: () => { },
    doubleClickFn: () => { },
    wfApprovalFunction: () => { },
    showHierarchyData: {},
    openCommentsData: {},
    openCommentsDataItem: {},
    openQuickCtaData: {},
    openQuickCtaDataItem: {},
    openDeleteButtonData: {},
    openPopUpData: {},
    openModalData: {},
    openModalName: "",
    openModalMatIcon: "",
    udrfUiOpenModalDataItem: {},
    openModal1Data: {},
    openModal1Name: "",
    openModal1MatIcon: "",
    udrfUiOpenModal1DataItem: {},
    openFileDownloadData: {},
    collapseAll: false,
    showCollapseButton: false,
    updateItemCardData: {},
    attachFileData: {},
    deleteFileData: {},
    downloadFileData: {},
    closeCTAData: {},
    inlineEditData: {},
    openEditData: {},
    openApprovalData: {},
    openinNewTabData: {},
    wfApprovalFunctionData: {},
    inlineEditDropDownMasterDatas: {},
    summaryCardsItem: {},
    itemCardSelecteditem: {},
    udrfBodyColumns: [],
    udrfVisibleBodyColumns: [],
    udrfInvisibleBodyColumns: [],
    udrfColumnConfigBodyColumns: [],
    categorisedSummaryCards: [],
    currencyValues: [],
    selectedCurrencyValue: "",
    minNoOfVisibleSummaryCards: 0,
    maxNoOfVisibleSummaryCards: 6,
    selectedCard: [],
    variant: 0,
    itemHasQuickCta: false,
    itemHasComments: false,
    itemHasEdit: false,
    itemHasOpenInNewTab: false,
    itemHasApproval: false,
    itemHasHierarchyView: false,
    itemHasDeleteButton: false,
    inApproval: false,
    itemHasAttachFileButton: false,
    itemHasBookmark: false,
    itemHasOpenModal: false,
    itemHasDoubleClickFn: false,
    itemHasOpenModal1: false,
    itemHasWfApprovalFunctionBtn: false,
    quickCTAInput: {},
    commentsInput: {},
    pinFavoriteIdData: {},
    commentsContext: {},
    showCreateNewComponentButton: false,
    showCreateVendorComponentButton: false,
    createNewComponent: () => { },
    createNewVendorComponent: () => { },
    showOrgChartButton: false,
    OrgChartData: () => { },
    showHistoryButton: false,
    HistoryData: () => { },
    showInfoButton: false,
    InfoData: () => { },
    showAlignTaskButton: false,
    AlignTaskData: () => { },
    groupByData: {},
    groupByUiArray: [],
    callgroupByData: () => { },
    scrolled: false,
    scrollIndex: 0,
    groupByResponseData: [],
    clickedHeaderSort: [],
    getCountData: async () => { },
    countResponseData: [],
    durationCount: [],
    getSelectedCountData: () => { },
    selectedInputArray: [],
    countForOnlyThisReport: false,
    isHeaderSort: false,
    itemHasStatusFunctionBtn: false,
    statusFunctionData: {},
    statusFunction: () => { },
    itemHasDialogDataBtn: false,
    dialogData: {},
    dialogFunction: () => { },
    switchFunction: () => { },
    statusFunctionAddOnData: {},
    itemHasAddBtn: false,
    addButtonData: {},
    addBtnClicked: () => { },
    itemHasDeleteBtn: false,
    deleteButtonData: {},
    deleteBtnClicked: () => { },
    hasTimelineViewButton: false,
    isTimelineView: false,
    timelineSections: [],
    timelineSectionItems: [],
    timelineItems: [],
    timelineSetSectionsInSectionItems: () => { },
    timelinePeriod: [],
    timelineStartdate: "",
    openTimeline: () => { },
    changeTimelinePeriod: () => { },
    changeTimelinePeriodData: {},
    timelineNavigationNext: () => { },
    timelineNavigationPrevious: () => { },
    timelineNavigationData: "",
    timelineCurrentPeriod: "week",
    openBtnClicked: () => { },
    openButtonData: {},
    itemHasOpenBtn: false,
    itemHasDownloadButton: false,
    itemDownloadButtonToolTip: '',
    itemHasUploadButton: false,
    itemUploadButtonToolTip: '',
    itemHasVerifyButton: false,
    itemVerifyButtonToolTip: '',
    verifyItemData: {},
    onItemVerifyClicked: () => { },
    itemHasApproveRejectButton: false,
    countFlag: true,
    isReset: false,
    udrfExpansionPanelView: [],
    expansionCardClickInputData: "",
    expansionCardClick: () => { },
    expansionPanelInnerItemData: [],
    isAdminView: false,
    cardClicked: false,
    notifyingActivated: false,
    selectAllActivated: false,
    notifyAllSelected: () => { },
    selectAll: () => { },
    columnClick: () => { },
    isEvaluator: false,
    apprasialId: "",
    goToAppraisalFunctionEvl: () => { },
    goToAppraisalFunctionEmp: () => { },
    appraisalIndex: 0,
    goToAppraisalApprove: () => { },
    EvaluatorList: [],
    isApproveButton: false,
    appraisalStatus: '',
    employeeView: false,
    showVersionIcon: false,
    showFlagIcon: false,
    showDownloadExcelIcon: false,
    showToggleIcon: false,
    showFlaggedTickets: () => { },
    downloadFilteredTickets: () => { },
    openNewReleasesComponent: () => { },
    changeToSummaryView: () => { },
    showFlagged: false,
    summaryViewActivated: true,
    isDownloading: false,
    showSettingsModalButtonForAMS: false,
    headerColumnWidthExtension: false,
    isOkr: false,
    variant2View: false,
    showReportButton: false,
    openReport: () => { },
    openSLABotReport: () => { },
    openLCDPDashboard: () => { },
    CoOwnerListData: [],
    callEditFunction: () => { },
    associateId: 0,
    isMultipleView: false,
    toggleChecked: false,
    isSortEnabled: false,
    multiToolTipForSingleIcon: false,
    itemHasInfoButton: false,
    openHelp: () => { },
    openHelpData: {},
    itemHasMoreActions: false,
    moreActionItemClicked: () => { },
    moreActionItemData: {},
    moreActionItemClickedData: {},
    openSendData: {},
    itemHasSendButton: false,
    openSend: () => { },
    openHistoryData: {},
    itemHasHistoryButton: false,
    openHistory: () => { },
    handleSelectAllCheckBox: () => { },
    checkedBodyItemIndex: [],
    isSelectAllItemCheckBox: false,
    itemHasStarRating: false,
    starRatingData: {},
    starRating: () => { },
    okrCollaborativeData: {},
    getCollaborativeDetails: () => { },
    okrChildAggregativeData: {},
    getChildAggregationDetails: () => { },
    okrParentAggregativeData: {},
    getParentAggregationDetails: () => { },
    itemHasMeetingInviteBtn: false,
    openMeetingInviteData: {},
    openMeetingInviteClicked: () => { },
    gbyCategoryWiseViewData: false,
    isMoreOptionsNeeded: false,
    ghostButtonUI: true,
    showApprovalButton: false,
    closeApprovalButton: false,
    openApprovals: () => { },
    closeApprovals: () => { },
    approvalCountLCDP: 0,
    emailPluginVisible: false,
    emailPluginData: {},
    showApprovalComponentButton: false,
    showApprovalComponent: () => { },
    approvalWfData: {},
    approvalWfDataUIItem: {},
    openMeetingInviteDataItem: {},
    openProfileViewClicked: () => { },
    openProfileViewData: {},
    itemHasOpenProfileViewBtn: false,
    showActualCurrencyForCurrencyComponent: false,
    itemHasOpenInFullScreen: false,
    openInFullScreen: () => { },
    openInFullScreenData: {},
    showLCDPReportButton: false,
    showLCDPBotReportButton: false,
    showLCDPDashboardButton: false,
    flagged: () => { },
    itemHasSubmitButton: false,
    submitButtonMatIcon: "",
    submitButtonName: "",
    submitButtonData: {},
    udrfUisubmitButtonData: {},
    submitButtonClick: () => { },
    openAdminSettingsReport: () => { },
    openAdditionalView: () => { },
    adminSettingButton: false,
    additionalViewButton: false,
    openEmailPlugin: () => { },
    itemHasOpenResourceTab: false,
    openResourceTabData: {},
    openResourceTab: () => { },
    itemHasCreateEmployee: false,
    createEmployeeData: {},
    createEmployee: () => { },
    itemHasCollabarationButton: false,
    collabarationData: {},
    okrCollabaration: () => { },
    itemHasAggregationButton: false,
    AggregationData: {},
    itemHasIntegrationButton: false,
    okrAggregation: () => { },
    showProvisionalModalButtonForP2P: false,
    showViewForRMG: "",
    openProvisionalModalButtonForP2P: () => { },
    showLcdpCreateRecordButton: false,
    lcdpCreateRecordButtonLabel: "",
    openCreateRecord: () => { },
    showLcdpViewRecordButton: false,
    lcdpViewRecordButtonLabel: "",
    openViewRecord: () => { },
    showCreateBudget: false,
    createBudget: () => { },
    showProjectProjects: false,
    showDraftProjects: false,
    showDraftProjectsFn: () => { },
    showProjectProjectsFn: () => { },
    displayTitleProductrProject: "",
    showQuickActionButton: false,
    openQuickAction: () => { },
    openQuickActionData: [],
    showQuickActionItem: false,
    openQuickActionItem: () => { },
    quickActionItemData: {},
    completeProfileBtn: false,
    completeProfile: () => { },
    completeProfileData: {},
    showTallySync: false,
    performTallySync: () => { },
    crmCreateActivitiesButton: false,
    createCRMActivity: () => { },
    crmActivityRowData: [],
    createCRMActivityData: "",
    searchPlaceholder: "",
    showHelpButton: false,
    openHelpDialog: () => { },
    itemL2DataScrollDown: () => { },
    itemL2ScrollableIndex: 0,
    summaryCardScrollButtonVisibleCardCount: 5,
    resetFileInput: () => { },
    showEmpAppraisalCycleDeleteButton: false,
    openEmpAppraisalCycleDelete: () => { },
    openEmpAppraisalCycleDeleteData: {},
    showEmpAppraisalAckRevertButton: false,
    openEmpAppraisalAckRevert: () => { },
    openEmpAppraisalAckRevertData: {},
    isAppraisalAdminCheckInAppraisalReport: false,
    showLcdpTimelineViewButton: false,
    lcdpTimelineViewEnabled: false,
    showAddMember: false,
    createAddMember: () => { },
    openModal3Data: {},
    openModal3Name: "",
    openModal3MatIcon: "",
    openModal3: () => { },
    udrfUiOpenModal3DataItem: {},
    itemHasOpenModal3: false,
    itemHasEditBtn: false,
    openEditBtnData: {},
    openEditBtnClicked: () => { },
    showVendorList: false,
    openVendorList: () => { },
    showGannttView: false,
    isModalSortVisible: true,
    itemProfileImgMsGraph: false,
    openDuplicateBtn: () => { },
    itemHasDuplicate: false,
    itemHasEditV2: false,
    openEditBtnV2: () => { },
    fetchProfileNameFromE360: false,
    openAllocationRequest: () => { },
    showAllocationRequestBtn: false,
    showGroupByIcon: false,
    displayBasedOnGroupBy: () => { },
    groupByListArray: [],
    groupByListData: {},
    showRmgDownloadBtn: false,
    openRmgReportDownload: () => { },
    openPeopleReportDownload: () => { },
    showAddMemberName: "",
    itemHasAttachments: false,
    attachmentsInput: {},
    openAttachments: () => { },
    openDetailReportView: () => { },
    timesheetDetailId: -1,
    currentColumnDetails: "",
    itemHasDeleteBtnV2: false,
    iteml2CardSelectedItem:{},
    openRequestDetail: () => { },
    showInNoticeButton: false,
    openInNoticeDetails : () => {},
    inNoticeCount : 0,
    showMyOppToggle: false,
    isMyOppToggleChecked: false,
    toggleMyOpp: () => {},
    openSoftBookingRequest: () => {},
    showQMPReportDownloadButton: false,
    downloadQMPReport: () => {},
    showSBHBReportDownloadButton: false,
    downloadSBHBReport: () => {},
    paymentHoverRow: false,
    openApplyCreditsDialog:() => { },
    openPaymentDialog:() => { },
    togglePRTypeSelect: "",
    togglePRTypes : false,
    togglePRDetails : () => {},
    onHeaderCheckBoxChange:(arr, index) => { },
    selectedCurrency: new FormControl(""),
    hasSmallCard: false,
    udrfItemAvailableStatusColor: [],
    billableButton : false,
    openBillablePRs : () => {},
    customColumnSelect : false,
    expenseAttachmentsDownload: (id) => { },
    itemHasAttachmentsDownload: false,
    showAttachmentsLoader: false,
    showSelectAllCheckbox: false,
    applyCreditsHover: true,
    decimalPartsCache: {},
    maxDigitsAllowedCache: {},
    loaderConfig: {},
    updateGeneralConfigUserLevel: (config, value) => { }
  };

  udrfFormData={
    selectedCurrency: new FormControl("")
   }
  udrfUiDataOld: any;

  udrfUiDataDefault = {};

  udrfData = {
    isOutsideUdrf: false,
    sortOnlyArray: [],
    txTableDetails: [],
    defaultRecordsPerFetch: 15,
    searchTableDetails: [],
    udrfDataSource: [],
    udrfSummaryCardCodes: [],
    udrfDefaultSummaryCardCodes: [],
    pinFavoriteIdArray: [],
    currentUserOId: this.authService.getProfile().profile.oid,
    currentUserFullName: "",
    myFilterNotifyUserArray: [],
    userConfigId: 0,
    applicationId: 0,
    reportName: "",
    appliedConfig: {},
    appliedColumnConfig: [],
    areFiltersApplied: false,
    areToolbarFiltersApplied: false,
    filterTypeArray: [],
    availableFilterTypeArray: [],
    sortTabFilterArray: [],
    appliedFilterTypeArray: [],
    isOverallCustomButtonActivated: false,
    overallCustomButtonFilterName: 0,
    overallCustomButtonDataType: "",
    overallCustomButtonValueStart: "",
    overallCustomButtonValueEnd: "",
    isCustomizeFiltersActive: false,
    myFiltersArray: [],
    isMyFiltersActive: false,
    activeMyFilterName: "",
    activeMyFilterDescription: "",
    mainFilterArray: [],
    mainSearchParameter: "",
    mainApiDateRangeStart: moment(),
    mainApiDateRangeEnd: moment(),
    areSortAndFiltersLoading: false,
    noItemDataFound: false,
    isItemDataLoading: false,
    customRangeData: [],
    valuesOfConditionsData: [],
    groupByArray: [],
    countFilterArray: [],
    countApiDateRangeStart: moment(),
    countApiDateRangeEnd: moment(),
    activeView: [],
    minDate: "",
    maxDate: "",
    currencyType: [],
    customDateFilterId: null,
    customDateRangeStart: '',
    customDateRangeEnd: ''
  };

  udrfDataOld: any;

  udrfDataDefault = {};

  udrfBodyData: any[];

  udrfBodyDataOld: any;

  searchDebounce: any;

  udrfGroupByHeaderData = [];

  filterCountArray = [];

  needToShowGroupByButton = false;

  groupBy = false;

  udrfFunctions = {

    openEmailPluginMailBox: async () => {

      this.udrfFunctions.initEmailPluginMailBox();

      this._mailUtilityService.mUtilityData.getMailFromKebsApiData = {
        url: "/api/general/getMailListBasedOnApplication",
        jwtToken: this.authService.getJwtToken(),
        paramsArr: [{
          applicationId: this.udrfData.applicationId,
          objectIdArr: this.udrfUiData.emailPluginData["objectIdArr"]
        }]
      }

      this._mailUtilityService.mUtilityData.saveMailInKebsApiData = {
        url: "/api/general/saveMailItemWithObjectId",
        jwtToken: this.authService.getJwtToken(),
        paramsArr: [{
          applicationId: this.udrfData.applicationId,
          objectIdArr: this.udrfUiData.emailPluginData["objectIdArr"]
        }]
      }

      this._mailUtilityService.mUtilityData.o365Token = { token: (await this._graphService.getO365Token()) }

      const { ViewMailComponent } = await import('mail-box-modal');

      const openViewMailComponent = this.dialog.open(ViewMailComponent, {
        width: "96%",
        height: "97%",
        maxWidth: "100vw",
        maxHeight: "100vw",
        data: {},
        disableClose: true
      });

      openViewMailComponent.afterClosed().subscribe(res => {
        this._mailUtilityService.resetMailData();
      });

    },

    initEmailPluginMailBox: () => {

      this._mailUtilityService.mUtilityData.applicationId = this.udrfData.applicationId
      this._mailUtilityService.mUtilityData.recipientMailIdArr = this.udrfUiData.emailPluginData['recipientMailIdArr'];
      this._mailUtilityService.mUtilityData.currentUserMailId = this.authService.getProfile().profile.email;
      this._mailUtilityService.mailUiData.isSyncWithKebsBtn = true;

    },

    resolveVisibleColumnConfigItems: () => {
      this.udrfUiData.udrfVisibleBodyColumns = _.filter(this.udrfUiData.udrfBodyColumns, function (udrfBodyColumnsItem) {
        return udrfBodyColumnsItem?.isActive && udrfBodyColumnsItem?.isVisible == 'true' ? udrfBodyColumnsItem : '';
      });

      for (let sortTabFilterArrayItem of this.udrfData.sortTabFilterArray) {

        let udrfVisibleBodyColumn = _.where(this.udrfUiData.udrfVisibleBodyColumns, { filterId: sortTabFilterArrayItem.filterId });

        if (udrfVisibleBodyColumn.length > 0)
          udrfVisibleBodyColumn[0]["sortOrder"] = sortTabFilterArrayItem["currentSortOrder"];

      }

      this.udrfUiData.udrfInvisibleBodyColumns = _.filter(this.udrfUiData.udrfBodyColumns, function (udrfBodyColumnsItem) {
        return udrfBodyColumnsItem?.isActive && udrfBodyColumnsItem?.isVisible == 'false' ? udrfBodyColumnsItem : '';
      });

    },

    resolveVisibleColumnConfigItemsInConfig: () => {

      this.udrfUiData.udrfVisibleBodyColumns = _.filter(this.udrfUiData.udrfColumnConfigBodyColumns, function (udrfBodyColumnsItem) {
        return udrfBodyColumnsItem?.isActive && (udrfBodyColumnsItem?.isVisible == 'true' || udrfBodyColumnsItem?.isVisible == true) ? udrfBodyColumnsItem : '';
      });

      this.udrfUiData.udrfInvisibleBodyColumns = _.filter(this.udrfUiData.udrfColumnConfigBodyColumns, function (udrfBodyColumnsItem) {
        return udrfBodyColumnsItem?.isActive && (udrfBodyColumnsItem?.isVisible == 'false' || udrfBodyColumnsItem?.isVisible == false) ? udrfBodyColumnsItem : '';
      });

    },

    makeMyFiltersArrayItemActive: async (myFiltersArrayTempItem, isFromModal) => {

      for (let myFiltersArrayItem of this.udrfData.myFiltersArray)
        myFiltersArrayItem.isMyFilterActive = false;

      myFiltersArrayTempItem.isMyFilterActive = true;

      await this.udrfFunctions.setMyActiveFilter(myFiltersArrayTempItem, true);

      this.udrfFunctions.applyConfig(isFromModal);

      this.utilityService.showToastMessage("Your filter " + myFiltersArrayTempItem.myFilterName + " has been made active and applied!");

    },

    constructCheckboxValues: async (dataSourceItem, checkboxValues, checkboxLevel) => {

      let checkboxValuesTemp = [];

      for (let checkboxValuesItem of checkboxValues) {

        checkboxValuesItem.subValues = checkboxValuesItem.subValues ? checkboxValuesItem.subValues : [];

        checkboxValuesItem["checkboxValues"] = [];

        for (let checkboxValuesSubItem of checkboxValuesItem.subValues) {

          checkboxValuesSubItem.subValues = checkboxValuesSubItem.subValues ? checkboxValuesSubItem.subValues : [];

          let checkboxValuesSubTempItem = {

            checkboxId: checkboxValuesSubItem[dataSourceItem.masterDataIdField],
            checkboxName: checkboxValuesSubItem[dataSourceItem.masterDataNameField],
            isCheckboxSelected: false,
            isCheckboxVisible: true,
            isCheckboxExpanded: false,
            checkboxLevel: checkboxLevel + 4,
            hasCheckboxValues: checkboxValuesSubItem.subValues.length > 0 ? true : false,
            checkboxValues: checkboxValuesSubItem.subValues.length > 0 ? await this.udrfFunctions.constructCheckboxValues(dataSourceItem, checkboxValuesSubItem.subValues, 6) : [],
            checkboxCount: 0

          };

          checkboxValuesItem["checkboxValues"].push(checkboxValuesSubTempItem);

        }

        let checkboxValuesTempItem = {

          checkboxId: checkboxValuesItem[dataSourceItem.masterDataIdField],
          checkboxName: checkboxValuesItem[dataSourceItem.masterDataNameField],
          isCheckboxSelected: false,
          isCheckboxVisible: true,
          isCheckboxExpanded: false,
          checkboxLevel: checkboxLevel + 2,
          hasCheckboxValues: checkboxValuesItem.checkboxValues.length > 0 ? true : false,
          checkboxValues: checkboxValuesItem.subValues.length > 0 ? await this.udrfFunctions.constructCheckboxValues1(dataSourceItem, checkboxValuesItem.subValues, 3) : [],
          checkboxCount: 0

        };

        checkboxValuesTemp.push(checkboxValuesTempItem);

      }

      return checkboxValuesTemp;

    },

    constructCheckboxValues1: async (dataSourceItem, checkboxValues, checkboxLevel) => {

      let checkboxValuesTemp = [];

      for (let checkboxValuesItem of checkboxValues) {

        checkboxValuesItem.subValues = checkboxValuesItem.subValues ? checkboxValuesItem.subValues : [];

        checkboxValuesItem["checkboxValues"] = [];

        for (let checkboxValuesSubItem of checkboxValuesItem.subValues) {

          checkboxValuesSubItem.subValues = checkboxValuesSubItem.subValues ? checkboxValuesSubItem.subValues : [];

          let checkboxValuesSubTempItem = {

            checkboxId: checkboxValuesSubItem[dataSourceItem.masterDataIdField],
            checkboxName: checkboxValuesSubItem[dataSourceItem.masterDataNameField],
            isCheckboxSelected: false,
            isCheckboxVisible: true,
            isCheckboxExpanded: false,
            checkboxLevel: checkboxLevel + 2,
            hasCheckboxValues: checkboxValuesSubItem.subValues.length > 0 ? true : false,
            checkboxValues: checkboxValuesSubItem.subValues,
            checkboxCount: 0

          };

          checkboxValuesItem["checkboxValues"].push(checkboxValuesSubTempItem);

        }

        let checkboxValuesTempItem = {

          checkboxId: checkboxValuesItem[dataSourceItem.masterDataIdField],
          checkboxName: checkboxValuesItem[dataSourceItem.masterDataNameField],
          isCheckboxSelected: false,
          isCheckboxVisible: true,
          isCheckboxExpanded: false,
          checkboxLevel: checkboxLevel,
          hasCheckboxValues: checkboxValuesItem.checkboxValues.length > 0 ? true : false,
          checkboxValues: checkboxValuesItem.checkboxValues,
          checkboxCount: 0

        };

        checkboxValuesTemp.push(checkboxValuesTempItem);

      }

      return checkboxValuesTemp;

    },

    expandCheckboxValues: (filterTypeArrayItem, checkboxValuesItem, calculateAllCbs) => {

      checkboxValuesItem.isCheckboxExpanded = !checkboxValuesItem.isCheckboxExpanded;

      if (checkboxValuesItem.hasCheckboxValues && !checkboxValuesItem.isCheckboxExpanded)
        for (let checkboxValuesItemValue of checkboxValuesItem.checkboxValues)
          checkboxValuesItemValue.isCheckboxExpanded = checkboxValuesItem.isCheckboxExpanded;

      if (calculateAllCbs) {

        let areAllHierarchyCbsExpanded = true;

        let checkboxValues = this.udrfFunctions.retrieveNecessaryCheckboxes(filterTypeArrayItem);

        for (let checkboxValuesItemTemp of checkboxValues)
          if (areAllHierarchyCbsExpanded && checkboxValuesItemTemp.hasCheckboxValues && !checkboxValuesItemTemp.isCheckboxExpanded) {

            areAllHierarchyCbsExpanded = false;

            if (areAllHierarchyCbsExpanded)
              for (let checkboxValuesItemTempValue of checkboxValuesItemTemp.checkboxValues)
                if (areAllHierarchyCbsExpanded && checkboxValuesItemTempValue.hasCheckboxValues && !checkboxValuesItemTempValue.isCheckboxExpanded)
                  areAllHierarchyCbsExpanded = false;

          }

        filterTypeArrayItem.areAllHierarchyCbsExpanded = areAllHierarchyCbsExpanded;

      }

    },

    expandAllCheckboxValues: async (filterTypeArrayItem) => {

      filterTypeArrayItem.areAllHierarchyCbsExpanded = true;

      let checkboxValues = await this.udrfFunctions.retrieveNecessaryCheckboxes(filterTypeArrayItem);

      for (let checkboxValuesItem of checkboxValues)
        if (checkboxValuesItem.hasCheckboxValues && !checkboxValuesItem.isCheckboxExpanded) {

          this.udrfFunctions.expandCheckboxValues(filterTypeArrayItem, checkboxValuesItem, false);

          for (let checkboxValuesItemValue of checkboxValuesItem.checkboxValues)
            if (checkboxValuesItemValue.hasCheckboxValues && !checkboxValuesItemValue.isCheckboxExpanded)
              this.udrfFunctions.expandCheckboxValues(filterTypeArrayItem, checkboxValuesItemValue, false);

        }

    },

    collapseAllCheckboxValues: (filterTypeArrayItem) => {

      filterTypeArrayItem.areAllHierarchyCbsExpanded = false;

      let checkboxValues = this.udrfFunctions.retrieveNecessaryCheckboxes(filterTypeArrayItem);

      for (let checkboxValuesItem of checkboxValues)
        if (checkboxValuesItem.hasCheckboxValues && checkboxValuesItem.isCheckboxExpanded) {

          this.udrfFunctions.expandCheckboxValues(filterTypeArrayItem, checkboxValuesItem, false);

          for (let checkboxValuesItemValue of checkboxValuesItem.checkboxValues)
            if (checkboxValuesItemValue.hasCheckboxValues && checkboxValuesItemValue.isCheckboxExpanded)
              this.udrfFunctions.expandCheckboxValues(filterTypeArrayItem, checkboxValuesItemValue, false);

        }

    },

    transformMasterData: async (dataSourceItem, masterData) => {

      for (let masterDataItem of masterData) {

        let doesCheckboxValueExist = [];

        if (masterDataItem[dataSourceItem.masterDataIdField] != null && masterDataItem[dataSourceItem.masterDataIdField].constructor == Object) {

          let objectKeys = [];

          for (let objectKey of Object.keys(masterDataItem[dataSourceItem.masterDataIdField]))
            objectKeys.push(objectKey);

          doesCheckboxValueExist = _.filter(dataSourceItem.checkboxValues, function (checkboxValueItem) {

            let shouldReturn = true;

            if (masterDataItem[dataSourceItem.masterDataNameField] == checkboxValueItem.checkboxName) {

              for (let objectKey of objectKeys)
                if (checkboxValueItem.checkboxId[objectKey] != masterDataItem[dataSourceItem.masterDataIdField][objectKey])
                  shouldReturn = false;

            }

            else
              shouldReturn = false;

            if (shouldReturn)
              return checkboxValueItem;

          });

        }

        else
          doesCheckboxValueExist = _.where(dataSourceItem.checkboxValues, { checkboxId: masterDataItem[dataSourceItem.masterDataIdField] });

        if (doesCheckboxValueExist.length > 0)
          doesCheckboxValueExist[0].isCheckboxVisible = true;

        else {

          let checkboxValuesItem = {};

          if (dataSourceItem.hasHierarchyWithinCheckbox) {

            dataSourceItem["areAllHierarchyCbsExpanded"] = false;

            masterDataItem.subValues = masterDataItem.subValues ? masterDataItem.subValues : [];

            checkboxValuesItem = {

              checkboxId: masterDataItem[dataSourceItem.masterDataIdField],
              checkboxName: masterDataItem[dataSourceItem.masterDataNameField],
              isCheckboxSelected: false,
              isCheckboxVisible: true,
              isCheckboxExpanded: false,
              checkboxLevel: 0,
              hasCheckboxValues: masterDataItem.subValues.length > 0 ? true : false,
              checkboxValues: masterDataItem.subValues.length > 0 ? await this.udrfFunctions.constructCheckboxValues(dataSourceItem, masterDataItem.subValues, 2) : [],
              checkboxCount: 0

            };

          }

          else
            checkboxValuesItem = {

              checkboxId: masterDataItem[dataSourceItem.masterDataIdField],
              checkboxName: masterDataItem[dataSourceItem.masterDataNameField],
              isCheckboxSelected: false,
              isCheckboxVisible: true,
              checkboxLevel: 0,
              hasCheckboxValues: false,
              checkboxCount: 0

            };

          dataSourceItem.checkboxValues.push(checkboxValuesItem);

        }

      }

      return dataSourceItem.checkboxValues;

    },

    onFilterScrollDown: async (filterTypeArrayItem, isJustTheStart) => {

      if (!filterTypeArrayItem.lazyLoadingComplete)
        filterTypeArrayItem.lazyLoadingComplete = false;

      if (filterTypeArrayItem.hasLazyLoading && !filterTypeArrayItem.lazyLoadingComplete && !filterTypeArrayItem["isLazyLoadingHappening"]) {

        filterTypeArrayItem["isLazyLoadingHappening"] = true;

        if (filterTypeArrayItem["onFilterScrollDownTillEnd"] && filterTypeArrayItem.apiParams["toRetrieveCount"] < 1000) {

          if (filterTypeArrayItem.apiParams["toRetrieveCountTemp"] == null)
            filterTypeArrayItem.apiParams["toRetrieveCountTemp"] = filterTypeArrayItem.apiParams["toRetrieveCount"];

          filterTypeArrayItem.apiParams["toRetrieveCount"] = 1000;

        }

        let startIndex = isJustTheStart ? 0 : filterTypeArrayItem.apiParams["toRetrieveCount"];

        await this.udrfFunctions.retrieveUdrfMoreFilterDataCall(filterTypeArrayItem, startIndex, false, true, false, []);

        filterTypeArrayItem["isLazyLoadingHappening"] = false;

        if (filterTypeArrayItem["onFilterScrollDownTillEnd"]) {

          await this.udrfFunctions.setFilterItemAllCheckboxStates(true, filterTypeArrayItem, false);

          this.udrfFunctions.onFilterScrollDown(filterTypeArrayItem, false);

        }

      }

      else if (filterTypeArrayItem.hasLazyLoading && filterTypeArrayItem.lazyLoadingComplete && filterTypeArrayItem["onFilterScrollDownTillEnd"])
        filterTypeArrayItem["selectAllLoading"] = false;

    },

    onFilterScrollDownTillEnd: (filterTypeArrayItem) => {

      filterTypeArrayItem["onFilterScrollDownTillEnd"] = true;

      this.udrfFunctions.onFilterScrollDown(filterTypeArrayItem, true);

    },

    searchFilter: async (filterTypeArrayItem, searchParameter) => {

      clearTimeout(this.searchDebounce);

      this.searchDebounce = setTimeout(async () => {

        filterTypeArrayItem.searchParameter = searchParameter;

        if (filterTypeArrayItem.hasLazyLoading)
          await this.udrfFunctions.retrieveUdrfMoreFilterDataCall(filterTypeArrayItem, 0, true, false, false, _.pluck(filterTypeArrayItem.checkboxValues, "checkboxId"));

        await this.udrfFunctions.searchThroughFilterTree(filterTypeArrayItem, searchParameter, [filterTypeArrayItem]);

      }, 1000);

    },

    searchThroughFilterTree: async (filterTypeArrayItem, searchParameter, filterTypeArrayItemArray) => {

      for (let checkboxValuesItem of filterTypeArrayItem.checkboxValues) {

        let isVisible = await this.udrfFunctions.searchThroughFilterTreeEachLevel(checkboxValuesItem, searchParameter, filterTypeArrayItemArray);

        if (isVisible && filterTypeArrayItem.isCheckboxVisible != null) {

          filterTypeArrayItem.isCheckboxVisible = isVisible;
          filterTypeArrayItem.isCheckboxExpanded = true;

          for (let filterTypeArrayItemArrayItem of filterTypeArrayItemArray)
            if (filterTypeArrayItemArrayItem.isCheckboxVisible != null) {

              let isAnyItemsVisible = _.where(filterTypeArrayItemArrayItem.checkboxValues, { isCheckboxVisible: true });

              if (isAnyItemsVisible.length > 0) {

                filterTypeArrayItemArrayItem.isCheckboxVisible = isVisible;
                filterTypeArrayItemArrayItem.isCheckboxExpanded = true;

              }

            }

            else {

              checkboxValuesItem.isCheckboxVisible = false;

              if (checkboxValuesItem.hasCheckboxValues)
                for (let checkboxValuesItemTemp of checkboxValuesItem.checkboxValues) {

                  checkboxValuesItemTemp.isCheckboxVisible = false;

                  if (checkboxValuesItemTemp.hasCheckboxValues)
                    for (let checkboxValuesItemTempValue of checkboxValuesItemTemp.checkboxValues)
                      checkboxValuesItemTempValue.isCheckboxVisible = false;

                }

            }

          if (checkboxValuesItem.hasCheckboxValues) {

            for (let checkboxValuesItemTemp of checkboxValuesItem.checkboxValues) {

              if (checkboxValuesItemTemp.checkboxName.toString().toLowerCase().includes(searchParameter.toLowerCase())) {

                checkboxValuesItemTemp.isCheckboxVisible = true;

                checkboxValuesItem.isCheckboxExpanded = true;

                checkboxValuesItem.isCheckboxVisible = true;

              }

              if (checkboxValuesItemTemp.hasCheckboxValues)
                for (let checkboxValuesItemTempValue of checkboxValuesItemTemp.checkboxValues) {

                  if (checkboxValuesItemTempValue.checkboxName.toString().toLowerCase().includes(searchParameter.toLowerCase())) {

                    checkboxValuesItemTempValue.isCheckboxVisible = true;

                    checkboxValuesItemTemp.isCheckboxExpanded = true;

                    checkboxValuesItemTemp.isCheckboxVisible = true;

                  }

                }

            }

            for (let checkboxValuesItemTemp of checkboxValuesItem.checkboxValues)
              if (checkboxValuesItemTemp.hasCheckboxValues)
                for (let checkboxValuesItemL2 of checkboxValuesItemTemp.checkboxValues) {

                  if (checkboxValuesItemL2.checkboxName.toString().toLowerCase().includes(searchParameter.toLowerCase())) {

                    checkboxValuesItemL2.isCheckboxVisible = true;

                    checkboxValuesItemTemp.isCheckboxExpanded = true;

                    checkboxValuesItemTemp.isCheckboxVisible = true;

                  }

                  if (checkboxValuesItemL2.hasCheckboxValues)
                    for (let checkboxValuesItemL3 of checkboxValuesItemL2.checkboxValues) {

                      if (checkboxValuesItemL3.checkboxName.toString().toLowerCase().includes(searchParameter.toLowerCase())) {

                        checkboxValuesItemL3.isCheckboxVisible = true;

                        checkboxValuesItemL2.isCheckboxExpanded = true;

                        checkboxValuesItemL2.isCheckboxVisible = true;

                      }

                    }

                }

          }

        }

      }

    },

    searchThroughFilterTreeEachLevel: async (checkboxValuesItem, searchParameter, filterTypeArrayItemArray) => {

      let isVisible = true;

      if (checkboxValuesItem.checkboxName.toString().toLowerCase().includes(searchParameter.toLowerCase())) {

        isVisible = true;
        checkboxValuesItem.isCheckboxExpanded = true;

      }

      else
        isVisible = false;

      checkboxValuesItem.isCheckboxVisible = isVisible;

      if (checkboxValuesItem.hasCheckboxValues) {

        filterTypeArrayItemArray.push(checkboxValuesItem);

        await this.udrfFunctions.searchThroughFilterTree(checkboxValuesItem, searchParameter, filterTypeArrayItemArray);

      }

      return isVisible;

    },

    clearFilter: async (filterTypeArrayItem) => {

      filterTypeArrayItem.searchParameter = "";

      await this.udrfFunctions.clearFilterComplete(filterTypeArrayItem);

    },

    clearFilterComplete: (filterTypeArrayItem) => {

      for (let checkboxValuesItem of filterTypeArrayItem.checkboxValues) {

        checkboxValuesItem.isCheckboxVisible = true;

        if (checkboxValuesItem.hasCheckboxValues) {

          checkboxValuesItem.isCheckboxExpanded = false;

          for (let checkboxValuesItemTemp of checkboxValuesItem.checkboxValues) {

            checkboxValuesItemTemp.isCheckboxVisible = true;

            if (checkboxValuesItemTemp.hasCheckboxValues) {

              checkboxValuesItemTemp.isCheckboxExpanded = false;

              for (let checkboxValuesItemTempValue of checkboxValuesItemTemp.checkboxValues)
                checkboxValuesItemTempValue.isCheckboxVisible = true;

            }

          }

        }

      }

    },

    closeFilter: async (filterTypeArrayItem) => {

      await this.udrfFunctions.clearFilter(filterTypeArrayItem);

      filterTypeArrayItem.isSelectDropdownVisible = false;

    },

    clickFilterCustomButton: async (filterTypeArrayItem, isCustomButtonActivated) => {

      //subash code
      if (isCustomButtonActivated && filterTypeArrayItem.customRangeButtonDataType == "date" && filterTypeArrayItem.isMainDateRangeFilter) {

        this.udrfData.countApiDateRangeStart = moment();
        this.udrfData.countApiDateRangeEnd = moment();

      }

      this.udrfData.overallCustomButtonValueStart = isCustomButtonActivated && filterTypeArrayItem.customButtonValueStart != null ? filterTypeArrayItem.customButtonValueStart : "";
      this.udrfData.overallCustomButtonValueEnd = isCustomButtonActivated && filterTypeArrayItem.customButtonValueEnd != null ? filterTypeArrayItem.customButtonValueEnd : "";

      //subash code end's

      for (let filterTypeArrayTempItem of this.udrfData.filterTypeArray)
        filterTypeArrayTempItem.isCustomButtonActivated = false;

      filterTypeArrayItem.isCustomButtonActivated = isCustomButtonActivated;

      this.udrfData.isOverallCustomButtonActivated = isCustomButtonActivated;

      this.udrfData.overallCustomButtonFilterName = isCustomButtonActivated ? filterTypeArrayItem.filterId : 0;

      this.udrfData.overallCustomButtonDataType = isCustomButtonActivated ? filterTypeArrayItem.customRangeButtonDataType : "";

      for (let checkboxValuesItemTemp of filterTypeArrayItem.checkboxValues)
        if (isCustomButtonActivated && checkboxValuesItemTemp.isCheckboxSelected)
          await this.udrfFunctions.setFilterItemCheckboxStates(filterTypeArrayItem, checkboxValuesItemTemp);
        else if (!isCustomButtonActivated && checkboxValuesItemTemp.isDefaultWithCustomButton && !checkboxValuesItemTemp.isCheckboxSelected)
          await this.udrfFunctions.setFilterItemCheckboxStates(filterTypeArrayItem, checkboxValuesItemTemp);

    },

    changeOverallCustomRangeValue: async (isValueStart) => {

      for (let filterTypeArrayItem of this.udrfData.filterTypeArray)
        if (filterTypeArrayItem.isCustomButtonActivated)
          if (isValueStart) {
            filterTypeArrayItem.customButtonValueStart = this.udrfData.overallCustomButtonValueStart;
            if (filterTypeArrayItem.customRangeButtonDataType == "date" && filterTypeArrayItem.isMainDateRangeFilter) {
              this.udrfData.countApiDateRangeStart = moment(this.udrfData.overallCustomButtonValueStart);
            }
          }
          else {
            filterTypeArrayItem.customButtonValueEnd = this.udrfData.overallCustomButtonValueEnd;
            if (filterTypeArrayItem.customRangeButtonDataType == "date" && filterTypeArrayItem.isMainDateRangeFilter) {
              this.udrfData.countApiDateRangeEnd = moment(this.udrfData.overallCustomButtonValueEnd);
            }
          }

      if (this.udrfUiData.countForOnlyThisReport) {
        await this.udrfUiData.getCountData();
      }

      await this.udrfFunctions.determineAreFiltersApplied();

    },

    clearItemConfigSort: async (filterTypeArrayItem) => {

      filterTypeArrayItem.currentSortOrder = 'I';

      let udrfVisibleBodyColumn = _.where(this.udrfUiData.udrfVisibleBodyColumns, { filterId: filterTypeArrayItem.filterId });

      if (udrfVisibleBodyColumn.length > 0)
        udrfVisibleBodyColumn[0]["sortOrder"] = filterTypeArrayItem["currentSortOrder"];

      await this.udrfFunctions.sortByFunctionTranslateToSortByArray(filterTypeArrayItem);

    },

    clearItemConfig: async (filterTypeArrayItem) => {

      filterTypeArrayItem.currentSortOrder = 'I';

      let udrfVisibleBodyColumn = _.where(this.udrfUiData.udrfVisibleBodyColumns, { filterId: filterTypeArrayItem.filterId });

      if (udrfVisibleBodyColumn.length > 0)
        udrfVisibleBodyColumn[0]["sortOrder"] = filterTypeArrayItem["currentSortOrder"];

      await this.udrfFunctions.sortByFunctionTranslateToSortByArray(filterTypeArrayItem);

      for (let checkboxValuesItem of filterTypeArrayItem.checkboxValues) {

        if ((checkboxValuesItem.isCheckboxSelected && !checkboxValuesItem.isDefaultWithCustomButton) ||
          (!checkboxValuesItem.isCheckboxSelected && checkboxValuesItem.isDefaultWithCustomButton))
          await this.udrfFunctions.selectFilterItemCheckbox(filterTypeArrayItem, checkboxValuesItem);

        if (checkboxValuesItem.hasCheckboxValues)
          for (let checkboxValuesSubItem of checkboxValuesItem.checkboxValues) {

            if (checkboxValuesSubItem.isCheckboxSelected)
              await this.udrfFunctions.selectFilterItemCheckbox(filterTypeArrayItem, checkboxValuesSubItem);

            if (checkboxValuesSubItem.hasCheckboxValues)
              for (let checkboxValuesSubItemValue of checkboxValuesSubItem.checkboxValues)
                if (checkboxValuesSubItemValue.isCheckboxSelected)
                  await this.udrfFunctions.selectFilterItemCheckbox(filterTypeArrayItem, checkboxValuesSubItemValue);

          }

      }

      await this.udrfFunctions.setFilterItemValues(filterTypeArrayItem);

      if (filterTypeArrayItem.isCustomButtonActivated) {

        filterTypeArrayItem.customRangeButtonDataType = "";
        filterTypeArrayItem.customButtonValueStart = "";
        filterTypeArrayItem.customButtonValueEnd = "";

      }

    },

    clearItemConfigApply: async (filterTypeArrayItem, flag) => {

      if (flag == 0)
        await this.udrfFunctions.clearItemConfigSort(filterTypeArrayItem);

      else
        await this.udrfFunctions.clearItemConfig(filterTypeArrayItem);

      let isFromModal = false;

      await this.udrfFunctions.applyConfig(isFromModal);

    },

    selectionChange: async (filterTypeArrayItem, checkboxValuesItem, isFromInit) => {

      if (!this.udrfFunctions.isCheckboxForFilterWithCustomButtonDisabled(checkboxValuesItem)) {

        await this.udrfFunctions.selectionChangeAction(filterTypeArrayItem, checkboxValuesItem);

        if (!isFromInit) {

          let isFromModal = false;

          await this.udrfFunctions.applyConfig(isFromModal);

        }

      }

    },

    selectionChangeAction: async (filterTypeArrayItem, checkboxValuesItem) => {

      if (!filterTypeArrayItem.hasCustomRangeButton)
        await this.udrfFunctions.setFilterItemCheckboxStates(filterTypeArrayItem, checkboxValuesItem);

      else {

        for (let checkboxValuesItemTemp of filterTypeArrayItem.checkboxValues)
          if (checkboxValuesItemTemp.checkboxName == checkboxValuesItem.checkboxName)
            await this.udrfFunctions.setFilterItemCheckboxStates(filterTypeArrayItem, checkboxValuesItemTemp);
          else if (checkboxValuesItemTemp.checkboxName != checkboxValuesItem.checkboxName && checkboxValuesItemTemp.isCheckboxSelected)
            await this.udrfFunctions.setFilterItemCheckboxStates(filterTypeArrayItem, checkboxValuesItemTemp);

        let selectedCheckboxValues = _.where(filterTypeArrayItem.checkboxValues, { isCheckboxSelected: true });

        if (selectedCheckboxValues.length == 0)
          for (let checkboxValuesItemTemp of filterTypeArrayItem.checkboxValues)
            if (checkboxValuesItemTemp.isDefaultWithCustomButton)
              await this.udrfFunctions.setFilterItemCheckboxStates(filterTypeArrayItem, checkboxValuesItemTemp);

        filterTypeArrayItem.isCustomButtonActivated = false;

        if (filterTypeArrayItem.filterId == this.udrfData.overallCustomButtonFilterName) {

          this.udrfData.isOverallCustomButtonActivated = false;

          this.udrfData.overallCustomButtonDataType = "";

        }

      }

    },

    isCheckboxForFilterWithCustomButtonDisabled: (checkboxValuesItem) => {

      return checkboxValuesItem.isCheckboxSelected && checkboxValuesItem.isDefaultWithCustomButton;

    },

    selectionAllChange: async (isChecked, filterTypeArrayItem) => {

      if (filterTypeArrayItem["hasLazyLoading"] && isChecked)
        filterTypeArrayItem["selectAllLoading"] = true;

      await this.udrfFunctions.setFilterItemAllCheckboxStates(isChecked, filterTypeArrayItem, true);

      let isFromModal = false;

      await this.udrfFunctions.applyConfig(isFromModal);

    },

    setFilterItemAllCheckboxStates: async (isChecked, filterTypeArrayItem, isFromToolbar) => {

      if (!filterTypeArrayItem.isSearchBoxVisible && !isFromToolbar) {

        for (let checkboxValue of filterTypeArrayItem.checkboxValues) {

          checkboxValue.isCheckboxSelected = !isChecked;

          if (checkboxValue.hasCheckboxValues)
            for (let checkboxValuesItemTemp of checkboxValue.checkboxValues) {

              checkboxValuesItemTemp.isCheckboxSelected = checkboxValue.isCheckboxSelected;

              if (checkboxValuesItemTemp.hasCheckboxValues)
                for (let checkboxValuesItemTempValue of checkboxValuesItemTemp.checkboxValues)
                  checkboxValuesItemTempValue.isCheckboxSelected = checkboxValue.isCheckboxSelected;

            }

        }

        for (let checkboxValue of filterTypeArrayItem.checkboxValues)
          await this.udrfFunctions.setFilterItemCheckboxStates(filterTypeArrayItem, checkboxValue);

          filterTypeArrayItem["selectAllLoading"] = false;
      }

      else {

        let necessaryCheckboxes = this.udrfFunctions.retrieveNecessaryCheckboxes(filterTypeArrayItem);

        for (let necessaryCheckbox of necessaryCheckboxes) {

          necessaryCheckbox.isCheckboxSelected = !isChecked;

          await this.udrfFunctions.setFilterItemCheckboxStates(filterTypeArrayItem, necessaryCheckbox);

        }

        filterTypeArrayItem["selectAllLoading"] = false;
      }

    },

    selectFilterItemCheckbox: async (filterTypeArrayItem, checkboxValuesItem) => {

      await this.udrfFunctions.selectionChangeAction(filterTypeArrayItem, checkboxValuesItem);

      await this.udrfFunctions.determineAreFiltersApplied();

      await this.udrfFunctions.applyCountForFilters();

    },

    applyCountForFilters: async () => {

      this.udrfData.countFilterArray = [];

      await this.udrfFunctions.determineAvailableFilters();

      for (let filterTypeArrayItem of this.udrfData.availableFilterTypeArray) {

        filterTypeArrayItem.multiOptionSelectSearchValues = [];

        for (let checkboxValuesItem of filterTypeArrayItem.checkboxValues) {

          if (checkboxValuesItem.isCheckboxSelected)
            filterTypeArrayItem.multiOptionSelectSearchValues.push(checkboxValuesItem.checkboxName);

          if (checkboxValuesItem.hasCheckboxValues)
            for (let checkboxValuesSubItem of checkboxValuesItem.checkboxValues) {

              if (checkboxValuesSubItem.isCheckboxSelected)
                filterTypeArrayItem.multiOptionSelectSearchValues.push(checkboxValuesSubItem.checkboxName);

              if (checkboxValuesSubItem.hasCheckboxValues)
                for (let checkboxValuesSubItemValue of checkboxValuesSubItem.checkboxValues)

                  if (checkboxValuesSubItemValue.isCheckboxSelected)
                    filterTypeArrayItem.multiOptionSelectSearchValues.push(checkboxValuesSubItemValue.checkboxName);

            }

        }

        if (filterTypeArrayItem.multiOptionSelectSearchValues.length > 0) {

          if (filterTypeArrayItem.multiOptionSelectSearchValues.length == 1) {

            let filterTypeArrayItemCbValues = [];

            for (let customRangeDataItem of this.udrfData.customRangeData)
              if (filterTypeArrayItem.filterId == customRangeDataItem.filterId)
                filterTypeArrayItemCbValues = customRangeDataItem.checkboxValues;

            let isDefaultCheckboxAloneSelected = _.where(filterTypeArrayItemCbValues, { isDefaultWithCustomButton: true });

            if (!(isDefaultCheckboxAloneSelected.length > 0 &&
              filterTypeArrayItem.multiOptionSelectSearchValues[0] == isDefaultCheckboxAloneSelected[0].checkboxName))
              this.udrfData.countFilterArray.push(filterTypeArrayItem);

          }
          else
            this.udrfData.countFilterArray.push(filterTypeArrayItem);

        }

        if (filterTypeArrayItem.isFilterRange && !filterTypeArrayItem.hasMasterDataApi) {

          let activeCustomDurationName = "";

          let activeCustomDuration;

          let customRangeDataItem = _.where(this.udrfData.customRangeData, { filterId: filterTypeArrayItem.filterId });

          if (filterTypeArrayItem.customRangeButtonDataType == "date" && filterTypeArrayItem.isMainDateRangeFilter &&
            filterTypeArrayItem.isCustomButtonActivated) {

            this.udrfData.countApiDateRangeStart = filterTypeArrayItem["customButtonValueStart"];
            this.udrfData.countApiDateRangeEnd = filterTypeArrayItem["customButtonValueEnd"];

          }

          else {

            if (customRangeDataItem.length > 0) {

              activeCustomDurationName = filterTypeArrayItem.multiOptionSelectSearchValues[0];

              activeCustomDuration = _.where(customRangeDataItem[0].checkboxValues, { checkboxName: activeCustomDurationName });

              if (activeCustomDuration.length > 0) {

                if (filterTypeArrayItem.customRangeButtonDataType == "date" && filterTypeArrayItem.isMainDateRangeFilter) {

                  this.udrfData.countApiDateRangeStart = activeCustomDuration[0].checkboxStartValue;
                  this.udrfData.countApiDateRangeEnd = activeCustomDuration[0].checkboxEndValue;

                }

              }

            }

          }


        }

      }

      await this.udrfFunctions.splitCodeAndDescription(this.udrfData.countFilterArray);

      await this.udrfFunctions.splitNameAndId(this.udrfData.countFilterArray);

      if (this.udrfUiData.countForOnlyThisReport) {
        await this.udrfUiData.getCountData();
      }

    },

    selectAllFilterItemCheckbox: async (isChecked, filterTypeArrayItem) => {

      if (filterTypeArrayItem["hasLazyLoading"] && isChecked)
        filterTypeArrayItem["selectAllLoading"] = true;

      await this.udrfFunctions.setFilterItemAllCheckboxStates(isChecked, filterTypeArrayItem, false);

      await this.udrfFunctions.determineAreFiltersApplied();

    },

    setFilterItemCheckboxStates: async (filterTypeArrayItem, checkboxValuesItem) => {

      if (filterTypeArrayItem.onlyOneLevelSelectable) {

        let checkboxValuesItemCondition = checkboxValuesItem.isCheckboxSelected;

        for (let checkboxValuesTempItem of filterTypeArrayItem.checkboxValues) {

          checkboxValuesTempItem.isCheckboxSelected = false;

          if (checkboxValuesTempItem.hasCheckboxValues)
            for (let checkboxValuesItemTemp of checkboxValuesTempItem.checkboxValues) {

              checkboxValuesItemTemp.isCheckboxSelected = checkboxValuesTempItem.isCheckboxSelected;

              if (checkboxValuesItemTemp.hasCheckboxValues)
                for (let checkboxValuesItemTempValue of checkboxValuesItemTemp.checkboxValues)
                  checkboxValuesItemTempValue.isCheckboxSelected = checkboxValuesTempItem.isCheckboxSelected;

            }

        }

        checkboxValuesItem.isCheckboxSelected = !checkboxValuesItemCondition;

      }

      else
        checkboxValuesItem.isCheckboxSelected = !checkboxValuesItem.isCheckboxSelected;

      if (!filterTypeArrayItem.onlyOneLevelSelectable && checkboxValuesItem.hasCheckboxValues)
        for (let checkboxValuesItemTemp of checkboxValuesItem.checkboxValues) {

          checkboxValuesItemTemp.isCheckboxSelected = checkboxValuesItem.isCheckboxSelected;

          if (checkboxValuesItemTemp.hasCheckboxValues)
            for (let checkboxValuesItemTempValue of checkboxValuesItemTemp.checkboxValues)
              checkboxValuesItemTempValue.isCheckboxSelected = checkboxValuesItem.isCheckboxSelected;

        }

      await this.udrfFunctions.setFilterItemValues(filterTypeArrayItem);

    },

    setFilterItemValues: (filterTypeArrayItem) => {

      let selectedCheckboxValues = _.where(filterTypeArrayItem.checkboxValues, { isCheckboxSelected: true });

      for (let checkboxValuesItem of filterTypeArrayItem.checkboxValues)
        if (checkboxValuesItem.hasCheckboxValues) {

          let selectedCheckboxItemValues = _.where(checkboxValuesItem.checkboxValues, { isCheckboxSelected: true });

          if (selectedCheckboxItemValues.length > 0)
            selectedCheckboxValues = selectedCheckboxValues.concat(selectedCheckboxItemValues);

          for (let checkboxValuesItemValues of checkboxValuesItem.checkboxValues)
            if (checkboxValuesItemValues.hasCheckboxValues) {

              let selectedCheckboxItemValues = _.where(checkboxValuesItemValues.checkboxValues, { isCheckboxSelected: true });

              if (selectedCheckboxItemValues.length > 0)
                selectedCheckboxValues = selectedCheckboxValues.concat(selectedCheckboxItemValues);

            }

        }

      let selectedCheckboxNames = _.pluck(selectedCheckboxValues, 'checkboxName');

      filterTypeArrayItem.multiOptionSelectSearchValues = selectedCheckboxNames;

    },

    toggleSearchBar: async (filterTypeArrayItem) => {

      filterTypeArrayItem.isSearchBoxVisible = !filterTypeArrayItem.isSearchBoxVisible;

      if (filterTypeArrayItem.isSearchBoxVisible && filterTypeArrayItem.searchParameter != "")
        await this.udrfFunctions.searchFilter(filterTypeArrayItem, filterTypeArrayItem.searchParameter);

      else if (!filterTypeArrayItem.isSearchBoxVisible)
        await this.udrfFunctions.clearFilterComplete(filterTypeArrayItem);

    },

    areAllFilterItemCheckboxesSelected: (filterTypeArrayItem) => {

      if (!filterTypeArrayItem.isSearchBoxVisible) {

        let selectedCbs = _.where(filterTypeArrayItem.checkboxValues, { isCheckboxSelected: true });

        if (selectedCbs.length == filterTypeArrayItem.checkboxValues.length && filterTypeArrayItem.checkboxValues.length != 0) {

          if (filterTypeArrayItem.hasLazyLoading)
            this.udrfFunctions.onFilterScrollDownTillEnd(filterTypeArrayItem);

          return true;

        }

        else
          return false;

      }

      else {

        let necessaryCheckboxes = this.udrfFunctions.retrieveNecessaryCheckboxes(filterTypeArrayItem);

        let selectedCbs = _.where(necessaryCheckboxes, { isCheckboxSelected: true });

        if (selectedCbs.length == necessaryCheckboxes.length && necessaryCheckboxes.length != 0) {

          if (filterTypeArrayItem.hasLazyLoading && selectedCbs.length == filterTypeArrayItem.checkboxValues.length && filterTypeArrayItem.checkboxValues.length != 0)
            this.udrfFunctions.onFilterScrollDownTillEnd(filterTypeArrayItem);

          return true;

        }

        else
          return false;

      }

    },

    sortOnHeader: async (filterData, flag) => {

      let sortData = _.where(this.udrfData.sortTabFilterArray, { filterId: filterData.filterId });

      if (flag == 1)
        await this.udrfFunctions.clearItemConfigApply(sortData[0], 0);

      else
        await this.udrfFunctions.sortByChange(sortData[0], false);

    },

    sortByChange: async (filterTypeArrayItem, isFromModal) => {

      await this.udrfFunctions.sortByFunction(filterTypeArrayItem);

      await this.udrfFunctions.applyConfig(isFromModal);

    },

    sortBy: async (filterTypeArrayItem) => {

      await this.udrfFunctions.sortByFunction(filterTypeArrayItem);

      await this.udrfFunctions.determineAreFiltersApplied();

      this.udrfFunctions.configureColumn(false);

    },

    sortByFunction: async (filterTypeArrayItem) => {

      if (filterTypeArrayItem.currentSortOrder == 'I')
        filterTypeArrayItem.currentSortOrder = 'A';

      else if (filterTypeArrayItem.currentSortOrder == 'A')
        filterTypeArrayItem.currentSortOrder = 'D';

      else if (filterTypeArrayItem.currentSortOrder == 'D')
        filterTypeArrayItem.currentSortOrder = 'I';

      let udrfVisibleBodyColumn = _.where(this.udrfUiData.udrfVisibleBodyColumns, { filterId: filterTypeArrayItem.filterId });

      if (udrfVisibleBodyColumn.length > 0)
        udrfVisibleBodyColumn[0]["sortOrder"] = filterTypeArrayItem["currentSortOrder"];

      await this.udrfFunctions.sortByFunctionTranslateToSortByArray(filterTypeArrayItem);

      await this.udrfFunctions.sortByFunctionTranslateToMainFilterArray(filterTypeArrayItem);

    },

    sortByFunctionTranslateToSortByArray: async (filterTypeArrayItem) => {

      let sortTabFilterArrayItem = _.where(this.udrfData.sortTabFilterArray, { filterId: filterTypeArrayItem.filterId });

      if (sortTabFilterArrayItem.length > 0)
        sortTabFilterArrayItem[0].currentSortOrder = filterTypeArrayItem.currentSortOrder;

    },

    sortByFunctionTranslateToMainFilterArray: async (filterTypeArrayItem) => {

      let mainFilterFilterArrayItem = _.where(this.udrfData.mainFilterArray, { filterId: filterTypeArrayItem.filterId });

      if (mainFilterFilterArrayItem.length > 0)
        mainFilterFilterArrayItem[0].currentSortOrder = filterTypeArrayItem.currentSortOrder;

    },

    getSortedArray: (inputArray, sortByField, sortByOrder, toParse) => {

      let tempInputArray = toParse ? JSON.parse(JSON.stringify(inputArray)) : inputArray;

      return tempInputArray.sort((a, b) => {

        var o1 = isNaN(a[sortByField]) ? a[sortByField].toLowerCase() : a[sortByField];
        var o2 = isNaN(b[sortByField]) ? b[sortByField].toLowerCase() : b[sortByField];

        if (sortByOrder == 0) {

          if (o1 < o2) return -1;
          if (o1 > o2) return 1;

        }

        else if (sortByOrder == 1) {

          if (o1 > o2) return -1;
          if (o1 < o2) return 1;

        }

        return 0;

      });

    },

    determineAvailableFilters: () => {

      this.udrfData.availableFilterTypeArray = [];

      for (let filterTypeArrayItem of this.udrfData.filterTypeArray)
        if (filterTypeArrayItem.isFilterAvailable) {

          let sortTabFilterArrayItem = _.where(this.udrfData.sortTabFilterArray, { filterId: filterTypeArrayItem.filterId });

          if (sortTabFilterArrayItem.length > 0)
            filterTypeArrayItem.currentSortOrder = sortTabFilterArrayItem[0].currentSortOrder;

          this.udrfData.availableFilterTypeArray.push(filterTypeArrayItem);

        }

      this.udrfData.sortTabFilterArray = this.udrfData.filterTypeArray.concat(this.udrfData.sortOnlyArray);

      this.udrfData.sortTabFilterArray = this.udrfFunctions.getSortedArray(this.udrfData.sortTabFilterArray, 'filterName', 0, true);

      for (let sortTabFilterArrayItem of this.udrfData.sortTabFilterArray) {

        let udrfVisibleBodyColumn = _.where(this.udrfUiData.udrfVisibleBodyColumns, { filterId: sortTabFilterArrayItem.filterId });

        if (udrfVisibleBodyColumn.length > 0)
          sortTabFilterArrayItem["currentSortOrder"] = udrfVisibleBodyColumn[0]["sortOrder"];

      }

    },

    getCheckBoxList: () => {

      for (let filterTypeArrayItem of this.udrfData.filterTypeArray)
        if (!filterTypeArrayItem.isCheckboxSortDisabled) {

          let selectedCheckboxValues = _.where(filterTypeArrayItem.checkboxValues, { isCheckboxSelected: true });

          let unSelectedCheckboxValues = _.where(filterTypeArrayItem.checkboxValues, { isCheckboxSelected: false });

          if (!filterTypeArrayItem.doNotSortCbs)
            selectedCheckboxValues = this.udrfFunctions.getSortedArray(selectedCheckboxValues, 'checkboxName', 0, true);

          for (let checkboxValuesItem of selectedCheckboxValues)
            if (checkboxValuesItem.hasCheckboxValues) {

              let selectedCheckboxItemValues = _.where(checkboxValuesItem.checkboxValues, { isCheckboxSelected: true });

              let unSelectedCheckboItemValues = _.where(checkboxValuesItem.checkboxValues, { isCheckboxSelected: false });

              if (!filterTypeArrayItem.doNotSortCbs) {

                selectedCheckboxItemValues = this.udrfFunctions.getSortedArray(selectedCheckboxItemValues, "checkboxName", 0, false);

                unSelectedCheckboItemValues = this.udrfFunctions.getSortedArray(unSelectedCheckboItemValues, "checkboxName", 0, false);

              }

              checkboxValuesItem.checkboxValues = selectedCheckboxItemValues.concat(unSelectedCheckboItemValues);

              for (let checkboxValuesItemValue of checkboxValuesItem.checkboxValues)
                if (checkboxValuesItemValue.hasCheckboxValues) {

                  let selectedCheckboxItemValues = _.where(checkboxValuesItemValue.checkboxValues, { isCheckboxSelected: true });

                  let unSelectedCheckboItemValues = _.where(checkboxValuesItemValue.checkboxValues, { isCheckboxSelected: false });

                  if (!filterTypeArrayItem.doNotSortCbs) {

                    selectedCheckboxItemValues = this.udrfFunctions.getSortedArray(selectedCheckboxItemValues, "checkboxName", 0, false);

                    unSelectedCheckboItemValues = this.udrfFunctions.getSortedArray(unSelectedCheckboItemValues, "checkboxName", 0, false);

                  }

                  checkboxValuesItemValue.checkboxValues = selectedCheckboxItemValues.concat(unSelectedCheckboItemValues);

                }

            }

          if (!filterTypeArrayItem.doNotSortCbs)
            unSelectedCheckboxValues = this.udrfFunctions.getSortedArray(unSelectedCheckboxValues, 'checkboxName', 0, true);

          for (let checkboxValuesItem of unSelectedCheckboxValues)
            if (checkboxValuesItem.hasCheckboxValues) {

              let selectedCheckboxItemValues = _.where(checkboxValuesItem.checkboxValues, { isCheckboxSelected: true });

              let unSelectedCheckboItemValues = _.where(checkboxValuesItem.checkboxValues, { isCheckboxSelected: false });

              if (!filterTypeArrayItem.doNotSortCbs) {

                selectedCheckboxItemValues = this.udrfFunctions.getSortedArray(selectedCheckboxItemValues, "checkboxName", 0, false);

                unSelectedCheckboItemValues = this.udrfFunctions.getSortedArray(unSelectedCheckboItemValues, "checkboxName", 0, false);

              }

              checkboxValuesItem.checkboxValues = selectedCheckboxItemValues.concat(unSelectedCheckboItemValues);

              for (let checkboxValuesItemValue of checkboxValuesItem.checkboxValues)
                if (checkboxValuesItemValue.hasCheckboxValues) {

                  let selectedCheckboxItemValues = _.where(checkboxValuesItemValue.checkboxValues, { isCheckboxSelected: true });

                  let unSelectedCheckboItemValues = _.where(checkboxValuesItemValue.checkboxValues, { isCheckboxSelected: false });

                  if (!filterTypeArrayItem.doNotSortCbs) {

                    selectedCheckboxItemValues = this.udrfFunctions.getSortedArray(selectedCheckboxItemValues, "checkboxName", 0, false);

                    unSelectedCheckboItemValues = this.udrfFunctions.getSortedArray(unSelectedCheckboItemValues, "checkboxName", 0, false);

                  }

                  checkboxValuesItemValue.checkboxValues = selectedCheckboxItemValues.concat(unSelectedCheckboItemValues);

                }

            }

          filterTypeArrayItem.checkboxValues = selectedCheckboxValues.concat(unSelectedCheckboxValues);

        }

    },


    determineAppliedFilters: async (hasToUpdateToolbarVisibility) => {

      await this.udrfFunctions.determineAvailableFilters();

      this.udrfData.appliedFilterTypeArray = [];

      for (let filterTypeArrayItem of this.udrfData.availableFilterTypeArray) {

        filterTypeArrayItem.multiOptionSelectSearchValues = [];

        for (let checkboxValuesItem of filterTypeArrayItem.checkboxValues) {

          if (checkboxValuesItem.isCheckboxSelected)
            filterTypeArrayItem.multiOptionSelectSearchValues.push(checkboxValuesItem.checkboxName);

          if (checkboxValuesItem.hasCheckboxValues)
            for (let checkboxValuesSubItem of checkboxValuesItem.checkboxValues) {

              if (checkboxValuesSubItem.isCheckboxSelected)
                filterTypeArrayItem.multiOptionSelectSearchValues.push(checkboxValuesSubItem.checkboxName);

              if (checkboxValuesSubItem.hasCheckboxValues)
                for (let checkboxValuesSubItemValue of checkboxValuesSubItem.checkboxValues)

                  if (checkboxValuesSubItemValue.isCheckboxSelected)
                    filterTypeArrayItem.multiOptionSelectSearchValues.push(checkboxValuesSubItemValue.checkboxName);

            }

        }

        if (filterTypeArrayItem.multiOptionSelectSearchValues.length > 0) {

          if (filterTypeArrayItem.multiOptionSelectSearchValues.length == 1) {

            let filterTypeArrayItemCbValues = [];

            for (let customRangeDataItem of this.udrfData.customRangeData)
              if (filterTypeArrayItem.filterId == customRangeDataItem.filterId)
                filterTypeArrayItemCbValues = customRangeDataItem.checkboxValues;

            let isDefaultCheckboxAloneSelected = _.where(filterTypeArrayItemCbValues, { isDefaultWithCustomButton: true });

            if (!(isDefaultCheckboxAloneSelected.length > 0 &&
              filterTypeArrayItem.multiOptionSelectSearchValues[0] == isDefaultCheckboxAloneSelected[0].checkboxName))
              this.udrfData.appliedFilterTypeArray.push(filterTypeArrayItem);

            // else if (filterTypeArrayItem.currentSortOrder != 'I')
            //   this.udrfData.appliedFilterTypeArray.push(filterTypeArrayItem);

          }
          else
            this.udrfData.appliedFilterTypeArray.push(filterTypeArrayItem);

        }
        //   else if (filterTypeArrayItem.currentSortOrder != 'I')
        //     this.udrfData.appliedFilterTypeArray.push(filterTypeArrayItem);

      }

      if (hasToUpdateToolbarVisibility)
        await this.udrfFunctions.determineAreToolbarFiltersApplied();

    },

    selectAvailableFilterItemCheckbox: async (filterTypeArrayItem) => {

      filterTypeArrayItem.isFilterAvailable = !filterTypeArrayItem.isFilterAvailable;

      await this.udrfFunctions.determineAvailableFilters();

      await this.udrfFunctions.determineAreFiltersApplied();

    },

    setMyActiveFilter: async (myFiltersArrayItem, isFromSetActiveFunction) => {

      this.udrfData.isMyFiltersActive = myFiltersArrayItem.isMyFilterActive;
      this.udrfData.activeMyFilterName = myFiltersArrayItem.myFilterName;
      this.udrfData.activeMyFilterDescription = myFiltersArrayItem.myFilterDescription;

      this.udrfData.filterTypeArray = await this.udrfFunctions.getSortedArray(JSON.parse(JSON.stringify(myFiltersArrayItem.myFilterArray)), "filterOrder", 0, true);

      this.udrfData.udrfSummaryCardCodes = myFiltersArrayItem.udrfSummaryCardCodes;

      this.udrfData.pinFavoriteIdArray = myFiltersArrayItem.pinFavoriteIdArray;

      this.udrfData.appliedColumnConfig = myFiltersArrayItem.appliedColumnConfig;

      if (isFromSetActiveFunction)
        await this.udrfFunctions.determineAppliedFilters(true);

    },

    configureUdrfUserConfig: async () => {

      let appliedColumnConfig = [], activeView = [];

      if (this.udrfData.appliedConfig["areToolbarFiltersApplied"] || this.udrfData.appliedConfig["areToolbarFiltersApplied"] == false) {

        this.udrfData.isMyFiltersActive = this.udrfData.appliedConfig["isMyFiltersActive"];

        let filterTypeArray = [], udrfSummaryCardCodes = this.udrfData.udrfSummaryCardCodes, pinFavoriteIdArray = [];

        if (this.udrfData.appliedConfig["myFiltersArray"]) {

          let myFiltersArray = [];

          for (let myAppliedConfigFiltersArrayItem of this.udrfData.appliedConfig["myFiltersArray"]) {

            myAppliedConfigFiltersArrayItem.myFilterArray = await this.udrfFunctions.configureFilterTypeArray(myAppliedConfigFiltersArrayItem.myFilterArray, JSON.parse(JSON.stringify(this.udrfData.filterTypeArray)));

            if (myAppliedConfigFiltersArrayItem.isMyFilterActive) {

              filterTypeArray = JSON.parse(JSON.stringify(myAppliedConfigFiltersArrayItem.myFilterArray));

              udrfSummaryCardCodes = myAppliedConfigFiltersArrayItem.udrfSummaryCardCodes;

              pinFavoriteIdArray = myAppliedConfigFiltersArrayItem.pinFavoriteIdArray;

              appliedColumnConfig = myAppliedConfigFiltersArrayItem.appliedColumnConfig;

              activeView = myAppliedConfigFiltersArrayItem.activeView;

            }

            myFiltersArray.push(myAppliedConfigFiltersArrayItem);

          }

          this.udrfData.myFiltersArray = myFiltersArray;

        }

        if (this.udrfData.appliedConfig["activeMyFilterDescription"])
          this.udrfData.activeMyFilterDescription = this.udrfData.appliedConfig["activeMyFilterDescription"];

        if (this.udrfData.appliedConfig["activeMyFilterName"])
          this.udrfData.activeMyFilterName = this.udrfData.appliedConfig["activeMyFilterName"];

        if (!this.udrfData.appliedConfig["isMyFiltersActive"]) {

          filterTypeArray = await this.udrfFunctions.configureFilterTypeArray(this.udrfData.appliedConfig["filterTypeArray"], this.udrfData.filterTypeArray);

          udrfSummaryCardCodes = this.udrfData.appliedConfig["udrfSummaryCardCodes"];
          pinFavoriteIdArray = this.udrfData.appliedConfig["pinFavoriteIdArray"];
          appliedColumnConfig = this.udrfData.appliedConfig["appliedColumnConfig"];
          activeView = this.udrfData.appliedConfig["activeView"];

        }

        if (this.udrfData.appliedConfig["overallCustomButtonConfig"] != null) {

          this.udrfData.isOverallCustomButtonActivated = this.udrfData.appliedConfig["overallCustomButtonConfig"].isOverallCustomButtonActivated;
          this.udrfData.overallCustomButtonFilterName = this.udrfData.appliedConfig["overallCustomButtonConfig"].overallCustomButtonFilterName;
          this.udrfData.overallCustomButtonDataType = this.udrfData.appliedConfig["overallCustomButtonConfig"].overallCustomButtonDataType;
          this.udrfData.overallCustomButtonValueStart = this.udrfData.appliedConfig["overallCustomButtonConfig"].overallCustomButtonValueStart;
          this.udrfData.overallCustomButtonValueEnd = this.udrfData.appliedConfig["overallCustomButtonConfig"].overallCustomButtonValueEnd;

          for (let filterTypeArrayItem of this.udrfData.filterTypeArray)
            if (filterTypeArrayItem.filterId == this.udrfData.overallCustomButtonFilterName) {

              filterTypeArrayItem.isCustomButtonActivated = this.udrfData.isOverallCustomButtonActivated;

              if (filterTypeArrayItem.isCustomButtonActivated)
                filterTypeArrayItem.multiOptionSelectSearchValues = ["Custom"];

              if (this.udrfData.overallCustomButtonDataType == "date") {

                filterTypeArrayItem["customButtonValueStart"] = moment(this.udrfData.overallCustomButtonValueStart);
                filterTypeArrayItem["customButtonValueEnd"] = moment(this.udrfData.overallCustomButtonValueEnd);

              }

              else {

                filterTypeArrayItem["customButtonValueStart"] = this.udrfData.overallCustomButtonValueStart;
                filterTypeArrayItem["customButtonValueEnd"] = this.udrfData.overallCustomButtonValueEnd;

              }

            }

        }

        this.udrfData.filterTypeArray = await this.udrfFunctions.getSortedArray(filterTypeArray, "filterOrder", 0, true);

        this.udrfData.udrfSummaryCardCodes = udrfSummaryCardCodes;

        this.udrfData.pinFavoriteIdArray = pinFavoriteIdArray;

        this.udrfData.areToolbarFiltersApplied = this.udrfData.appliedConfig["areToolbarFiltersApplied"];

      }

      this.udrfData.appliedColumnConfig = appliedColumnConfig;

      this.udrfData.activeView = activeView;

      if (this.udrfData.appliedConfig['currencyCode'])
        this.udrfData.currencyType = [{ "name": this.udrfData.appliedConfig['currencyCode'] }]

    },

    configureFilterTypeArray: async (filterTypeArrayFrom, filterTypeArrayTo) => {

      for (let filterTypeArrayFromItem of filterTypeArrayFrom)
        for (let filterTypeArrayToItem of filterTypeArrayTo)
          if (filterTypeArrayFromItem.filterId == filterTypeArrayToItem.filterId) {

            filterTypeArrayToItem.filterOrder = filterTypeArrayFromItem.filterOrder;
            filterTypeArrayToItem.currentSortOrder = filterTypeArrayFromItem.currentSortOrder;
            filterTypeArrayToItem.isFilterAvailable = filterTypeArrayFromItem.isFilterAvailable;

            for (let checkboxValuesToItem of filterTypeArrayToItem.checkboxValues)
              checkboxValuesToItem.isCheckboxSelected = false;

            filterTypeArrayToItem.multiOptionSelectSearchValues = [];

            filterTypeArrayFromItem.selectedCheckboxIdsNotYetRetrieved = [];

            for (let checkboxValuesFromItem of filterTypeArrayFromItem.checkboxValues) {

              let isRetrieved = false;

              for (let checkboxValuesToItem of filterTypeArrayToItem.checkboxValues) {

                if ((typeof (checkboxValuesFromItem) == 'object' && _.isEqual(checkboxValuesFromItem, checkboxValuesToItem.checkboxId)) || (checkboxValuesFromItem == checkboxValuesToItem.checkboxId)) {

                  checkboxValuesToItem.isCheckboxSelected = true;
                  filterTypeArrayToItem.multiOptionSelectSearchValues.push(checkboxValuesToItem.checkboxName);
                  isRetrieved = true;

                }

                if (checkboxValuesToItem.hasCheckboxValues)
                  for (let checkboxValuesToSubItem of checkboxValuesToItem.checkboxValues) {

                    if (checkboxValuesFromItem == checkboxValuesToSubItem.checkboxId) {

                      if (!checkboxValuesToItem.isCheckboxSelected)
                        checkboxValuesToItem.isCheckboxExpanded = true;

                      checkboxValuesToSubItem.isCheckboxSelected = true;
                      filterTypeArrayToItem.multiOptionSelectSearchValues.push(checkboxValuesToSubItem.checkboxName);
                      isRetrieved = true;

                    }

                    if (checkboxValuesToSubItem.hasCheckboxValues)
                      for (let checkboxValuesToSubItemValue of checkboxValuesToSubItem.checkboxValues)
                        if (checkboxValuesFromItem == checkboxValuesToSubItemValue.checkboxId) {

                          if (!checkboxValuesToSubItem.isCheckboxSelected)
                            checkboxValuesToSubItem.isCheckboxExpanded = true;

                          checkboxValuesToSubItemValue.isCheckboxSelected = true;
                          filterTypeArrayToItem.multiOptionSelectSearchValues.push(checkboxValuesToSubItemValue.checkboxName);
                          isRetrieved = true;

                        }

                  }

              }

              if (!isRetrieved)
                filterTypeArrayFromItem.selectedCheckboxIdsNotYetRetrieved.push(checkboxValuesFromItem);

            }

            if (filterTypeArrayFromItem.selectedCheckboxIdsNotYetRetrieved.length > 0)
              await this.udrfFunctions.retrieveUdrfMoreFilterDataCall(filterTypeArrayToItem, 0, false, false, true, filterTypeArrayFromItem.selectedCheckboxIdsNotYetRetrieved);

            if (filterTypeArrayFromItem.isCustomButtonActivated) {

              this.udrfData.overallCustomButtonFilterName = filterTypeArrayFromItem.filterId;

              filterTypeArrayToItem.isCustomButtonActivated = filterTypeArrayFromItem.isCustomButtonActivated;

              this.udrfData.isOverallCustomButtonActivated = filterTypeArrayFromItem.isCustomButtonActivated;

              filterTypeArrayToItem.customRangeButtonDataType = filterTypeArrayFromItem.customRangeButtonDataType;

              this.udrfData.overallCustomButtonDataType = filterTypeArrayFromItem.customRangeButtonDataType;

              filterTypeArrayToItem.multiOptionSelectSearchValues = ["Custom"];

              filterTypeArrayToItem["customButtonValueStart"] = moment(filterTypeArrayFromItem["customButtonValueStart"]);

              filterTypeArrayToItem["customButtonValueEnd"] = moment(filterTypeArrayFromItem["customButtonValueEnd"]);

              this.udrfData.overallCustomButtonValueStart = filterTypeArrayToItem["customButtonValueStart"];

              this.udrfData.overallCustomButtonValueEnd = filterTypeArrayToItem["customButtonValueEnd"];

            }

          }

      return filterTypeArrayTo;

    },

    retrieveUdrfMoreFilterDataCall: async (filterTypeArrayItem, toRetrieveCount, isForSearch, isForLazyLoading, isForSelectedNotYetRetrieved, noToRetrieveIds) => {

      filterTypeArrayItem.apiParams["startIndex"] += toRetrieveCount;

      filterTypeArrayItem.apiParams["isForSearch"] = isForSearch;

      filterTypeArrayItem.apiParams["isForLazyLoading"] = isForLazyLoading;

      filterTypeArrayItem.apiParams["noToRetrieveIds"] = noToRetrieveIds;

      filterTypeArrayItem.apiParams["searchParameter"] = filterTypeArrayItem.searchParameter;

      await this.udrfFunctions.retrieveUdrfMoreFilterDataFunction(filterTypeArrayItem).then(async (masterData: any) => {

        filterTypeArrayItem.masterData = filterTypeArrayItem.masterData.concat(masterData);

        filterTypeArrayItem.checkboxValues = await this.udrfFunctions.transformMasterData(filterTypeArrayItem, masterData);

        filterTypeArrayItem.checkboxValues = [...new Map(filterTypeArrayItem.checkboxValues.map(checkboxValuesItem =>
          [checkboxValuesItem.checkboxId, checkboxValuesItem])).values()];

        if (isForSelectedNotYetRetrieved)
          for (let checkboxValuesFromItem of noToRetrieveIds)
            for (let checkboxValuesToItem of filterTypeArrayItem.checkboxValues) {

              if ((typeof (checkboxValuesFromItem) == 'object' && _.isEqual(checkboxValuesFromItem, checkboxValuesToItem.checkboxId)) || (checkboxValuesFromItem == checkboxValuesToItem.checkboxId)) {

                checkboxValuesToItem.isCheckboxSelected = true;
                filterTypeArrayItem.multiOptionSelectSearchValues.push(checkboxValuesToItem.checkboxName);

              }

              if (checkboxValuesToItem.hasCheckboxValues)
                for (let checkboxValuesToSubItem of checkboxValuesToItem.checkboxValues) {

                  if (checkboxValuesFromItem == checkboxValuesToSubItem.checkboxId) {

                    if (!checkboxValuesToItem.isCheckboxSelected)
                      checkboxValuesToItem.isCheckboxExpanded = true;

                    checkboxValuesToSubItem.isCheckboxSelected = true;
                    filterTypeArrayItem.multiOptionSelectSearchValues.push(checkboxValuesToSubItem.checkboxName);

                  }

                  if (checkboxValuesToSubItem.hasCheckboxValues)
                    for (let checkboxValuesToSubItemValue of checkboxValuesToSubItem.checkboxValues)
                      if (checkboxValuesFromItem == checkboxValuesToSubItemValue.checkboxId) {

                        if (!checkboxValuesToSubItem.isCheckboxSelected)
                          checkboxValuesToSubItem.isCheckboxExpanded = true;

                        checkboxValuesToSubItemValue.isCheckboxSelected = true;
                        filterTypeArrayItem.multiOptionSelectSearchValues.push(checkboxValuesToSubItemValue.checkboxName);

                      }

                }

            }

      });

    },

    retrieveUdrfMoreFilterDataFunction: (filterTypeArrayItem) => {

      return new Promise(resolve => {

        this.retrieveUdrfMoreFilterData(filterTypeArrayItem)
          .pipe(takeUntil(this._onResetUdrfData)).subscribe(async res => {

            let masterData = [];

            if (res["messType"] == "S" && res["data"] != null && res["data"].length > 0)
              masterData = res["data"];

            else if (res["messType"] == "S" && (res["data"] == null || (res["data"] != null && res["data"].length == 0)) && filterTypeArrayItem.apiParams["isForLazyLoading"]) {

              filterTypeArrayItem["lazyLoadingComplete"] = true;
              filterTypeArrayItem["selectAllLoading"] = false;

            }

            if (masterData.length == 0 && filterTypeArrayItem["onFilterScrollDownTillEnd"]) {

              filterTypeArrayItem["onFilterScrollDownTillEnd"] = false;

              filterTypeArrayItem.apiParams["toRetrieveCount"] = filterTypeArrayItem.apiParams["toRetrieveCountTemp"];

              await this.udrfFunctions.setFilterItemAllCheckboxStates(true, filterTypeArrayItem, false);

            }

            resolve(masterData);

          }, err => {

            this.errorService.userErrorAlert((err && err.code) ? err.code : (err && err.error) ? err.error.code : 'NIL', "Error while retrieving Data Try Again After Sometime", (err && err.params) ? err.params : (err && err.error) ? err.error.params : {});

            // let errReportingTeams = "KEBS";

            // this.utilityService.showErrorMessage(err, errReportingTeams);

          });

      });

    },

    reconfigureUdrfUserConfig: () => {

      let customFields = null;

      if (this.udrfData.appliedConfig["customFields"])
        customFields = this.udrfData.appliedConfig["customFields"];

      this.udrfData.appliedConfig = {
        areToolbarFiltersApplied: this.udrfData.areToolbarFiltersApplied,
        isMyFiltersActive: this.udrfData.isMyFiltersActive,
        appliedColumnConfig: this.udrfData.appliedColumnConfig,
        activeView: this.udrfData.activeView
      };

      if (this.udrfData.isOverallCustomButtonActivated)
        this.udrfData.appliedConfig["overallCustomButtonConfig"] = {
          isOverallCustomButtonActivated: this.udrfData.isOverallCustomButtonActivated,
          overallCustomButtonFilterName: this.udrfData.overallCustomButtonFilterName,
          overallCustomButtonDataType: this.udrfData.overallCustomButtonDataType,
          overallCustomButtonValueStart: this.udrfData.overallCustomButtonValueStart,
          overallCustomButtonValueEnd: this.udrfData.overallCustomButtonValueEnd
        };

      if (customFields)
        this.udrfData.appliedConfig["customFields"] = customFields;

      let myFiltersArray = [];

      for (let myFiltersArrayItem of this.udrfData.myFiltersArray) {

        if (this.udrfData.isMyFiltersActive && myFiltersArrayItem.isMyFilterActive) {

          myFiltersArrayItem.udrfSummaryCardCodes = this.udrfData.udrfSummaryCardCodes;

          myFiltersArrayItem.pinFavoriteIdArray = this.udrfData.pinFavoriteIdArray;

          myFiltersArrayItem.appliedColumnConfig = this.udrfData.appliedColumnConfig;

          myFiltersArrayItem.activeView = this.udrfData.activeView;

        }

        myFiltersArray.push({
          isMyFilterActive: myFiltersArrayItem.isMyFilterActive,
          myFilterArray: this.udrfFunctions.reconfigureFilterTypeArray(myFiltersArrayItem.myFilterArray),
          udrfSummaryCardCodes: myFiltersArrayItem.udrfSummaryCardCodes,
          pinFavoriteIdArray: myFiltersArrayItem.pinFavoriteIdArray,
          appliedColumnConfig: myFiltersArrayItem.appliedColumnConfig,
          myFilterDescription: myFiltersArrayItem.myFilterDescription,
          myFilterEditDescription: myFiltersArrayItem.myFilterEditDescription,
          myFilterEditName: myFiltersArrayItem.myFilterEditName,
          myFilterName: myFiltersArrayItem.myFilterName,
          activeView: myFiltersArrayItem.activeView
        });

      }

      this.udrfData.appliedConfig["activeMyFilterDescription"] = this.udrfData.activeMyFilterDescription;

      this.udrfData.appliedConfig["activeMyFilterName"] = this.udrfData.activeMyFilterName;

      this.udrfData.appliedConfig["myFiltersArray"] = myFiltersArray;

      if (!this.udrfData.isMyFiltersActive) {

        this.udrfData.appliedConfig["filterTypeArray"] = this.udrfFunctions.reconfigureFilterTypeArray(this.udrfData.filterTypeArray);

        this.udrfData.appliedConfig["udrfSummaryCardCodes"] = this.udrfData.udrfSummaryCardCodes;

        this.udrfData.appliedConfig["pinFavoriteIdArray"] = this.udrfData.pinFavoriteIdArray;

        this.udrfData.appliedConfig["appliedColumnConfig"] = this.udrfData.appliedColumnConfig;

        this.udrfData.appliedConfig["activeView"] = this.udrfData.activeView;

      }

    },

    reconfigureFilterTypeArray: (filterTypeArray) => {

      let filterTypeArrayTemp = [];

      for (let filterTypeArrayItem of filterTypeArray) {

        let checkboxValues = [];

        for (let checkboxValuesItem of filterTypeArrayItem.checkboxValues) {

          if (checkboxValuesItem.isCheckboxSelected)
            checkboxValues.push(checkboxValuesItem.checkboxId);

          if (checkboxValuesItem.hasCheckboxValues)
            for (let checkboxValuesSubItem of checkboxValuesItem.checkboxValues) {

              if (checkboxValuesSubItem.isCheckboxSelected)
                checkboxValues.push(checkboxValuesSubItem.checkboxId);

              if (checkboxValuesSubItem.hasCheckboxValues)
                for (let checkboxValuesSubItemValue of checkboxValuesSubItem.checkboxValues)
                  if (checkboxValuesSubItemValue.isCheckboxSelected)
                    checkboxValues.push(checkboxValuesSubItemValue.checkboxId);

            }

        }

        filterTypeArrayTemp.push({
          currentSortOrder: filterTypeArrayItem.currentSortOrder,
          filterId: filterTypeArrayItem.filterId,
          filterOrder: filterTypeArrayItem.filterOrder,
          isFilterAvailable: filterTypeArrayItem.isFilterAvailable,
          checkboxValues: checkboxValues
        });

      }

      return filterTypeArrayTemp;

    },

    saveMyFilter: () => {

      if (this.udrfData.isMyFiltersActive)
        for (let myFiltersArrayItem of this.udrfData.myFiltersArray)
          if (myFiltersArrayItem.isMyFilterActive) {

            myFiltersArrayItem.myFilterArray = JSON.parse(JSON.stringify(this.udrfData.filterTypeArray));
            myFiltersArrayItem.udrfSummaryCardCodes = JSON.parse(JSON.stringify(this.udrfData.udrfSummaryCardCodes));
            myFiltersArrayItem.pinFavoriteIdArray = JSON.parse(JSON.stringify(this.udrfData.pinFavoriteIdArray));
            myFiltersArrayItem.appliedColumnConfig = JSON.parse(JSON.stringify(this.udrfData.appliedColumnConfig));
          }

      this.udrfFunctions.updateUdrfUserConfigFunction(this.udrfData, true);

    },

    updateUdrfUserConfigFunction: async (udrfData, isFromModal) => {

      this.udrfFunctions.reconfigureUdrfUserConfig();

      await this.udrfFunctions.updateUserUdrfConfig(udrfData, isFromModal);

    },

    configureColumn: async (shouldCallSave) => {

      this.udrfData.appliedColumnConfig = [];

      for (let i = 0; i < this.udrfUiData.udrfVisibleBodyColumns.length; i++) {

        this.udrfUiData.udrfVisibleBodyColumns[i].isVisible = 'true';

        this.udrfData.appliedColumnConfig[i] = {
          item: this.udrfUiData.udrfVisibleBodyColumns[i].item,
          isActive: this.udrfUiData.udrfVisibleBodyColumns[i].isActive,
          isVisible: 'true',
          width: this.udrfUiData.udrfVisibleBodyColumns[i].width,
          sortOrder: this.udrfUiData.udrfVisibleBodyColumns[i].sortOrder,
          isHovered: this.udrfUiData.udrfVisibleBodyColumns[i].isHovered,
          isClicked: this.udrfUiData.udrfVisibleBodyColumns[i].isClicked,
        }

      }

      if (shouldCallSave)
        this.udrfFunctions.updateColumnConfig();

    },

    updateColumnConfig: async () => {

      this.udrfData.appliedConfig["appliedColumnConfig"] = this.udrfData.appliedColumnConfig;
      this.udrfFunctions.updateUdrfUserConfigFunction(this.udrfData, true);

    },

    updateUserUdrfConfig: (udrfData, isFromModal) => {
      let keyUdrfData = localStorage.getItem(`Report_`+udrfData?.applicationId)
      if(keyUdrfData){
        keyUdrfData = JSON.parse(keyUdrfData);
        let previousAppliedConfig = JSON.stringify(keyUdrfData['applied_config']);
        let updatedAppliedConfig = JSON.stringify(udrfData.appliedConfig,);
        if(!(previousAppliedConfig === updatedAppliedConfig)){
          localStorage.setItem(`Report_`+udrfData.applicationId, JSON.stringify({
            userConfigId: udrfData.userConfigId,
            application_id: udrfData.applicationId,
            report_name: udrfData.reportName,
            associate_oid: udrfData.currentUserOId,
            applied_config: udrfData.appliedConfig,
            myFilterNotifyUserArray: udrfData.myFilterNotifyUserArray,
            is_active: 1,
            modified_by: "query",
            modified_on: new Date(),
            created_by:"query",
            created_on:new Date()
          }));
          localStorage.setItem(`is_filter_updated`,"1");
        }
        this.udrfData.userConfigId = udrfData.userConfigId;
      }
      else{
        const keys: string[] = [];
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          if(key && key.startsWith(`Report_`)) {
            let keyUdrfData = localStorage.getItem(key)
            keyUdrfData = JSON.parse(keyUdrfData);
            let udrfDataValues = {
              userConfigId: keyUdrfData['userConfigId'],
              applicationId: keyUdrfData['application_id'],
              reportName: keyUdrfData['report_name'],
              currentUserOId: keyUdrfData['associate_oid'],
              appliedConfig: keyUdrfData['applied_config'],
              myFilterNotifyUserArray: keyUdrfData['myFilterNotifyUserArray']
            }
            localStorage.removeItem(key);
            if(localStorage.getItem(`is_filter_updated`) === "1"){
              this.updateUdrfUserConfig(udrfDataValues).subscribe(async res => {

                // if (isFromModal)
                //this.utilityService.showToastMessage(res["messText"]);
        
                this.udrfData.userConfigId = res["userConfigId"];
        
              }, err => {
        
                // let errReportingTeams = "KEBS";
        
                // this.utilityService.showErrorMessage(err, errReportingTeams);
        
                this.errorService.userErrorAlert((err && err.code) ? err.code : (err && err.error) ? err.error.code : 'NIL', "Error while retrieving Data Try Again After Sometime", (err && err.params) ? err.params : (err && err.error) ? err.error.params : {});
        
              });
            }
            localStorage.removeItem(`is_filter_updated`);
          }
        }
        localStorage.setItem(`Report_`+udrfData.applicationId, JSON.stringify({
          userConfigId: udrfData.userConfigId,
          application_id: udrfData.applicationId,
          report_name: udrfData.reportName,
          associate_oid: udrfData.currentUserOId,
          applied_config: udrfData.appliedConfig,
          myFilterNotifyUserArray: udrfData.myFilterNotifyUserArray,
          is_active: 1,
          modified_by: "query",
          modified_on: new Date(),
          created_by:"query",
          created_on:new Date()
        }));

        // this.updateUdrfUserConfig(udrfData).subscribe(async res => {

        //   // if (isFromModal)
        //   //this.utilityService.showToastMessage(res["messText"]);
  
        //   this.udrfData.userConfigId = res["userConfigId"];
  
        // }, err => {
  
        //   // let errReportingTeams = "KEBS";
  
        //   // this.utilityService.showErrorMessage(err, errReportingTeams);
  
        //   this.errorService.userErrorAlert((err && err.code) ? err.code : (err && err.error) ? err.error.code : 'NIL', "Error while retrieving Data Try Again After Sometime", (err && err.params) ? err.params : (err && err.error) ? err.error.params : {});
  
        // });
      }
    },

    resetUserUdrfConfig: (udrfData) => {

      this.utilityService
        .openConfirmationSweetAlertWithCustom("Reset Filters For Application?",
          "Once you confirm, all the filters will be reset and your My Filters data will be cleared!").then(async (res) => {

            if (res)
              this.resetUdrfUserConfig(udrfData).subscribe(async res => {

                this.utilityService.showToastMessage(res["messText"]);
                window.location.reload();

              }, err => {

                // let errReportingTeams = "KEBS";

                // this.utilityService.showErrorMessage(err, errReportingTeams);

                this.errorService.userErrorAlert((err && err.code) ? err.code : (err && err.error) ? err.error.code : 'NIL', "Error while retrieving Data Try Again After Sometime", (err && err.params) ? err.params : (err && err.error) ? err.error.params : {});

              });

          });

    },

    determineAreToolbarFiltersApplied: () => {

      this.udrfData["areToolbarFiltersApplied"] = false;

      if (this.udrfData.appliedFilterTypeArray.length > 0)
        this.udrfData["areToolbarFiltersApplied"] = true;

    },

    determineAreFiltersApplied: async () => {

      this.udrfData["areFiltersApplied"] = true;

    },

    retrieveNecessaryCheckboxes: (filterTypeArrayItem) => {

      let necessaryCheckboxes = [];

      for (let checkboxValuesItem of filterTypeArrayItem.checkboxValues)
        if (checkboxValuesItem.isCheckboxVisible) {

          necessaryCheckboxes.push(checkboxValuesItem);

          if (checkboxValuesItem.hasCheckboxValues && checkboxValuesItem.isCheckboxExpanded)
            for (let checkboxValuesItemTemp of checkboxValuesItem.checkboxValues)
              if (checkboxValuesItemTemp.isCheckboxVisible) {

                necessaryCheckboxes.push(checkboxValuesItemTemp);

                if (checkboxValuesItemTemp.hasCheckboxValues && checkboxValuesItemTemp.isCheckboxExpanded)
                  for (let checkboxValuesItemTempValue of checkboxValuesItemTemp.checkboxValues)
                    if (checkboxValuesItemTempValue.isCheckboxVisible) {

                      necessaryCheckboxes.push(checkboxValuesItemTempValue);

                      if (checkboxValuesItemTempValue.hasCheckboxValues && checkboxValuesItemTempValue.isCheckboxExpanded)
                        for (let subCheckboxValuesItemTempValue of checkboxValuesItemTempValue.checkboxValues)
                          if (subCheckboxValuesItemTempValue.isCheckboxVisible)
                            necessaryCheckboxes.push(subCheckboxValuesItemTempValue);

                    }

              }

        }

      return necessaryCheckboxes;

    },

    applyConfig: async (isFromModal) => {

      if (isFromModal)
        await this.udrfFunctions.determineAppliedFilters(true);

      await this.udrfFunctions.callAllDataApis();

    },

    constructCustomRangeData: async (filterId, customRangeButtonDataType, checkboxValues) => {

      let customRangeDataItem = {
        filterId: filterId,
        customRangeButtonDataType: customRangeButtonDataType,
        multiOptionSelectSearchValues: [],
        checkboxValues: []
      };

      for (let checkboxValuesItemTemp of checkboxValues) {

        let checkboxValuesItem = {

          checkboxId: checkboxValuesItemTemp.checkboxId,
          checkboxName: checkboxValuesItemTemp.checkboxName,
          isCheckboxSelected: checkboxValuesItemTemp.isCheckboxDefaultSelected,
          isCheckboxVisible: true,
          hasCheckboxValues: false,
          checkboxStartValue: checkboxValuesItemTemp.checkboxStartValue,
          checkboxEndValue: checkboxValuesItemTemp.checkboxEndValue,
          isDefaultWithCustomButton: checkboxValuesItemTemp.isCheckboxDefaultSelected,
          checkboxCount: 0

        }

        if (checkboxValuesItemTemp.isCheckboxDefaultSelected)
          customRangeDataItem.multiOptionSelectSearchValues.push(checkboxValuesItem.checkboxName);

        customRangeDataItem.checkboxValues.push(checkboxValuesItem);

      }

      this.udrfData.customRangeData.push(customRangeDataItem);

    },

    constructValuesOfConditionsData: async (filterId, checkboxValues) => {

      let customRangeDataItem = {
        filterId: filterId,
        multiOptionSelectSearchValues: [],
        checkboxValues: []
      };

      for (let checkboxValuesItemTemp of checkboxValues) {

        let checkboxValuesItem = {

          checkboxId: checkboxValuesItemTemp.checkboxId,
          checkboxName: checkboxValuesItemTemp.checkboxName,
          isCheckboxSelected: checkboxValuesItemTemp.isCheckboxDefaultSelected,
          isCheckboxVisible: true,
          hasCheckboxValues: false,
          checkboxOriginalValues: checkboxValuesItemTemp.checkboxOriginalValues,
          checkboxCount: 0

        }

        if (checkboxValuesItemTemp.isCheckboxDefaultSelected)
          customRangeDataItem.multiOptionSelectSearchValues.push(checkboxValuesItem.checkboxName);

        customRangeDataItem.checkboxValues.push(checkboxValuesItem);

      }

      this.udrfData.valuesOfConditionsData.push(customRangeDataItem);

    },

    splitNameAndId: async (filterArray) => {

      for (let mainFilterArrayItem of filterArray) {
        let tempIdArray = [];

        if (mainFilterArrayItem.isIdBased) {

          for (let checkboxValuesItem of mainFilterArrayItem.checkboxValues) {
            if (checkboxValuesItem.isCheckboxSelected) {
              tempIdArray.push(checkboxValuesItem.checkboxId);
            }
            if (mainFilterArrayItem.hasHierarchyWithinCheckbox) {
              for (let firstLevelItems of checkboxValuesItem.checkboxValues) {
                if (mainFilterArrayItem.hasHierarchyWithinCheckbox) {
                  tempIdArray.push(firstLevelItems.checkboxId);
                }
                for (let secondLevelItems of firstLevelItems.checkboxValues) {
                  if (secondLevelItems.isCheckboxSelected) {
                    tempIdArray.push(secondLevelItems.checkboxId);
                  }
                }
              }
            }
          }

          mainFilterArrayItem["multiOptionSelectSearchValuesWithId"] = tempIdArray;

          // Temporary
          //mainFilterArrayItem["multiOptionSelectSearchValues"] = tempIdArray;

        }

      }

    },

    splitCodeAndDescription: async (filterArray) => {

      for (let mainFilterArrayItem of filterArray) {

        if (!mainFilterArrayItem.isSortOnly) {

          let tempCheckboxValues = [];

          for (let checkboxValuesItem of mainFilterArrayItem.multiOptionSelectSearchValues) {

            if (isNaN(checkboxValuesItem)) {

              let checkboxValuesItemValues = checkboxValuesItem.split(" ~ ");

              tempCheckboxValues.push(checkboxValuesItemValues[0]);

            }

            else
              tempCheckboxValues.push(checkboxValuesItem);

          }

          mainFilterArrayItem.multiOptionSelectSearchValues = tempCheckboxValues;

        }

      }

    },

    clearConfig: async () => {

      for (let filterTypeArrayItem of this.udrfData.filterTypeArray) {
        filterTypeArrayItem.isCustomButtonActivated = false;
        await this.udrfFunctions.clearItemConfigSort(filterTypeArrayItem);
        await this.udrfFunctions.clearItemConfig(filterTypeArrayItem);
        if (filterTypeArrayItem.customRangeButtonDataType == "date" && filterTypeArrayItem.isMainDateRangeFilter) {

          this.udrfData.mainApiDateRangeStart = moment();
          this.udrfData.mainApiDateRangeEnd = moment();

          this.udrfData.countApiDateRangeStart = moment();
          this.udrfData.countApiDateRangeEnd = moment();

          this.udrfData.customDateFilterId = null;
          this.udrfData.customDateRangeStart = '';
          this.udrfData.customDateRangeEnd = '';

        }
      }

      for (let filterTypeArrayItem of this.udrfData.filterTypeArray)
        filterTypeArrayItem.isFilterAvailable = true;

      this.udrfData.filterTypeArray = await this.udrfFunctions.getSortedArray(this.udrfData.filterTypeArray, "filterOrder", 0, true);

      await this.udrfFunctions.determineAppliedFilters(true);

      this.udrfData.areFiltersApplied = false;

      this.udrfData.isOverallCustomButtonActivated = false;
      this.udrfData.overallCustomButtonFilterName = 0;
      this.udrfData.overallCustomButtonDataType = "";
      this.udrfData.overallCustomButtonValueStart = "";
      this.udrfData.overallCustomButtonValueEnd = "";

      await this.udrfFunctions.callAllDataApis();

    },

    clearSearchAndConfig: async () => {

      await this.udrfFunctions.clearConfig();

      await this.udrfFunctions.stopSearchingOpportunities();

    },

    stopSearchingOpportunities: async () => {

      this.udrfData.mainSearchParameter = "";

      let isFromModal = false;

      await this.udrfFunctions.applyConfig(isFromModal);

    },


    callAllDataApis: async () => {

      await this.udrfFunctions.updateUdrfUserConfigFunction(this.udrfData, false);

      this.udrfData.noItemDataFound = false;

      this.udrfData.isItemDataLoading = true;

      await this.udrfFunctions.recursivelyCallHeaderApi();

    },

    recursivelyCallHeaderApi: async () => {

      this.udrfData.mainFilterArray = [];
      this.udrfData.countFilterArray = [];

      for (let filterTypeArrayItem of this.udrfData.appliedFilterTypeArray) {

        if (filterTypeArrayItem.areValuesConditions) {

          let originalValuesForConditions = [];

          for (let searchValuesItem of filterTypeArrayItem.multiOptionSelectSearchValues) {

            let searchValuesItemData = _.where(filterTypeArrayItem.checkboxValues, { checkboxName: searchValuesItem });

            if (searchValuesItemData.length > 0)
              originalValuesForConditions = originalValuesForConditions.concat(searchValuesItemData[0].checkboxOriginalValues);

          }

          filterTypeArrayItem["originalValuesForConditions"] = originalValuesForConditions;

        }

        this.udrfData.mainFilterArray.push(filterTypeArrayItem);

        this.udrfData.countFilterArray.push(filterTypeArrayItem);

      }

      for (let sortOnlyArrayItem of this.udrfData.sortTabFilterArray)
        if (sortOnlyArrayItem.currentSortOrder != 'I') {

          let mainFilterArrayItem = _.where(this.udrfData.mainFilterArray, { filterId: sortOnlyArrayItem.filterId });

          if (mainFilterArrayItem.length == 0)
            this.udrfData.mainFilterArray.push(sortOnlyArrayItem);

        }

      for (let filterTypeArrayItem of this.udrfData.filterTypeArray)
        if (filterTypeArrayItem.isFilterAvailable && filterTypeArrayItem.isFilterRange && !filterTypeArrayItem.hasMasterDataApi) {
          let activeCustomDurationName = "";

          let activeCustomDuration;

          let customRangeDataItem = _.where(this.udrfData.customRangeData, { filterId: filterTypeArrayItem.filterId });

          if (filterTypeArrayItem.customRangeButtonDataType == "date" && filterTypeArrayItem.isMainDateRangeFilter &&
            filterTypeArrayItem.isCustomButtonActivated) {

            this.udrfData.mainApiDateRangeStart = filterTypeArrayItem["customButtonValueStart"];
            this.udrfData.mainApiDateRangeEnd = filterTypeArrayItem["customButtonValueEnd"];

            this.udrfData.countApiDateRangeStart = filterTypeArrayItem["customButtonValueStart"];
            this.udrfData.countApiDateRangeEnd = filterTypeArrayItem["customButtonValueEnd"];

            this.udrfData.customDateFilterId = filterTypeArrayItem["filterId"];
            this.udrfData.customDateRangeStart = filterTypeArrayItem["customButtonValueStart"];
            this.udrfData.customDateRangeEnd = filterTypeArrayItem["customButtonValueEnd"];
          }

          else {

            if (customRangeDataItem.length > 0) {

              activeCustomDurationName = filterTypeArrayItem.multiOptionSelectSearchValues[0];

              activeCustomDuration = _.where(customRangeDataItem[0].checkboxValues, { checkboxName: activeCustomDurationName });

              if (activeCustomDuration.length > 0) {

                if (filterTypeArrayItem.customRangeButtonDataType == "date" && filterTypeArrayItem.isMainDateRangeFilter) {

                  this.udrfData.mainApiDateRangeStart = activeCustomDuration[0].checkboxStartValue;
                  this.udrfData.mainApiDateRangeEnd = activeCustomDuration[0].checkboxEndValue;

                  this.udrfData.countApiDateRangeStart = activeCustomDuration[0].checkboxStartValue;
                  this.udrfData.countApiDateRangeEnd = activeCustomDuration[0].checkboxEndValue;

                }

                else {

                  filterTypeArrayItem.filterStartValue = activeCustomDuration[0].checkboxStartValue;
                  filterTypeArrayItem.filterEndValue = activeCustomDuration[0].checkboxEndValue;

                }

              }

              else if (filterTypeArrayItem.isCustomButtonActivated) {

                let mainFilterArrayItem = _.where(this.udrfData.mainFilterArray, { filterId: filterTypeArrayItem.filterId });
                if(mainFilterArrayItem && mainFilterArrayItem.length>0){
                mainFilterArrayItem[0].multiOptionSelectSearchValues = ["Custom"];

                mainFilterArrayItem[0].filterStartValue = filterTypeArrayItem.customButtonValueStart;
                mainFilterArrayItem[0].filterEndValue = filterTypeArrayItem.customButtonValueEnd;
                }
              }

            }

          }

        }

      await this.udrfFunctions.splitCodeAndDescription(this.udrfData.mainFilterArray);

      await this.udrfFunctions.splitNameAndId(this.udrfData.mainFilterArray);

      await this.udrfFunctions.splitCodeAndDescription(this.udrfData.countFilterArray);

      await this.udrfFunctions.splitNameAndId(this.udrfData.countFilterArray);

      await this.udrfFunctions.callAppApi();

    },

    callAppApi: () => { },

    applyStatusId(filterArray, statusName) {
      let statusId;
      for (let filterDataItems of filterArray[0].masterData) {
        if (filterDataItems.name == statusName) {
          statusId = filterDataItems.id
        }
      }

      return statusId;

    },

    pinFavoriteId: () => {

      if (_.contains(this.udrfData.pinFavoriteIdArray, this.udrfUiData.pinFavoriteIdData[this.udrfUiData.bookmarkId])) {
        this.udrfData.pinFavoriteIdArray = _.reject(this.udrfData.pinFavoriteIdArray, value => { return value == this.udrfUiData.pinFavoriteIdData[this.udrfUiData.bookmarkId] })
      }
      else {
        if (this.udrfData.pinFavoriteIdArray)
          this.udrfData.pinFavoriteIdArray.push(this.udrfUiData.pinFavoriteIdData[this.udrfUiData.bookmarkId]);
        else {
          this.udrfData.pinFavoriteIdArray = [];
          this.udrfData.pinFavoriteIdArray.push(this.udrfUiData.pinFavoriteIdData[this.udrfUiData.bookmarkId]);
        }
      }
      this.udrfFunctions.saveMyFilter();
    }

  };

  constructor(private http: HttpClient, private authService: LoginService, private utilityService: UtilityService,
    public formBuilder: FormBuilder, private errorService: ErrorService, private dialog: MatDialog,
    public _mailUtilityService: MailUtilityService,
    private _graphService: GraphApiService) {

    this.udrfDataDefault = JSON.parse(JSON.stringify(this.udrfData));

    this.udrfUiDataDefault = JSON.parse(JSON.stringify(this.udrfUiData));

    this.udrfUiData.resolveColumnConfig = this.resolveColumnConfig.bind(this);

  }

  resolveColumnConfig() {

    if (this.udrfData?.appliedConfig["appliedColumnConfig"]?.length > 0) {

      for (let i = 0; i < this.udrfUiData.udrfBodyColumns.length; i++)
        if (this.udrfUiData.udrfBodyColumns[i])
          this.udrfUiData.udrfBodyColumns[i].isVisible = 'false';

      let appliedColumnConfig = this.udrfData.appliedConfig["appliedColumnConfig"].map((item, row) => {

        const found = this.udrfUiData.udrfBodyColumns.find((element) => item?.item == element?.item);

        if (found != null)
          return { ...found, ...item };

      });

      let finalAppliedColumnConfig = [];

      for (let appliedColumnConfigItem of appliedColumnConfig)
        if (appliedColumnConfigItem != null)
          finalAppliedColumnConfig.push(appliedColumnConfigItem);

      this.udrfUiData.udrfBodyColumns = this.udrfUiData.udrfBodyColumns.map((item, row) => {

        const found = finalAppliedColumnConfig.find((element) => item?.item == element?.item);

        return { ...item, ...found };

      });

      this.udrfUiData.udrfBodyColumns = this.udrfUiData.udrfBodyColumns;

      let VisibleColumns = this.udrfUiData.udrfBodyColumns;

      this.udrfUiData.udrfVisibleBodyColumns = finalAppliedColumnConfig;

      // this.udrfUiData.udrfVisibleBodyColumns = this.udrfUiData.udrfVisibleBodyColumns.sort((a, b) => {

      //   if (a.position != null && b.position != null) {

      //     if (a.position < b.position)
      //       return -1;

      //     else if (a.position > b.position)
      //       return 1;

      //   }

      //   else
      //     return 0;

      // });

      this.udrfUiData.udrfInvisibleBodyColumns = _.where(VisibleColumns, { isVisible: "false" });

      // this.udrfFunctions.resolveVisibleColumnConfigItems();

      for (let sortTabFilterArrayItem of this.udrfData.sortTabFilterArray) {

        let udrfVisibleBodyColumn = _.where(this.udrfUiData.udrfVisibleBodyColumns, { filterId: sortTabFilterArrayItem.filterId });

        if (udrfVisibleBodyColumn.length > 0)
          udrfVisibleBodyColumn[0]["sortOrder"] = sortTabFilterArrayItem["currentSortOrder"];

      }

    }

    else {

      this.udrfFunctions.resolveVisibleColumnConfigItems();
      this.udrfUiData.udrfBodyColumns = this.udrfUiData.udrfBodyColumns;

    }

  }

  getAppUdrfConfig(applicationId, callAppApi) {

    this.udrfData.areSortAndFiltersLoading = true;

    this.udrfData.applicationId = applicationId;

    this.udrfData.currentUserOId = this.authService.getProfile().profile.oid;

    this.udrfData.currentUserFullName = this.authService.getProfile().profile.name;

    this.udrfData.isItemDataLoading = true;

    this.udrfFunctions.callAppApi = callAppApi;

    let apiParams = {
      applicationId: this.udrfData.applicationId
    }

    if (this.udrfUiData.udrfHasDmcConfig)
      apiParams["additionalParams"] = [{
        filterId: 2,
        apiParams: {
          retrievalIds: this.udrfUiData.udrfDmcConfig
        }
      }];

    this.getUdrfAppConfig(apiParams).pipe(takeUntil(this._onResetUdrfData)).subscribe(async res => {

      if (res["messType"] == "S" && res["data"]) {

        this.udrfData.reportName = res["data"]["report_name"] ? res["data"]["report_name"] : "";
        this.udrfData.txTableDetails = res["data"]["tx_table_details"] ? JSON.parse(res["data"]["tx_table_details"]) : [];
        this.udrfData.defaultRecordsPerFetch = (this.udrfData.txTableDetails.length > 0 &&
          this.udrfData.txTableDetails[0].defaultRecordsPerFetch) ? this.udrfData.txTableDetails[0].defaultRecordsPerFetch : this.udrfData.defaultRecordsPerFetch;
        this.udrfData.searchTableDetails = res["data"]["search_table_details"] ? JSON.parse(res["data"]["search_table_details"]) : [];
        this.udrfData.udrfDataSource = res["data"]["filter_api_details"] ? res["data"]["filter_api_details"] : [];
        this.udrfData.udrfSummaryCardCodes = res["data"]["default_summary_cards"] ? JSON.parse(res["data"]["default_summary_cards"]) : [];
        this.udrfData.udrfDefaultSummaryCardCodes = res["data"]["default_summary_cards"] ? JSON.parse(res["data"]["default_summary_cards"]) : [];
        

        if(typeof this.udrfData.udrfSummaryCardCodes == 'object' && this.udrfData.udrfSummaryCardCodes.hasOwnProperty('smallTileSummaryCard'))
          this.udrfUiData.hasSmallCard = this.udrfData.udrfSummaryCardCodes['smallTileSummaryCard'] || false
        
        // if (this.udrfUiData.hasSmallCard) {
        //   this.udrfData.defaultRecordsPerFetch =
        //     (this.udrfData.txTableDetails.length > 0 &&
        //       this.udrfData.txTableDetails[0].defaultRecordsPerFetch)
        //       ? this.udrfData.txTableDetails[0].defaultRecordsPerFetch + 5
        //       : this.udrfData.defaultRecordsPerFetch + 5;
        // }
        

        if (!this.udrfUiData.udrfHasDmcConfig) {

          this.udrfUiData.udrfHasDmcConfigTemp = res["data"]["dmc_config"] ? true : false;
          this.udrfUiData.udrfDmcConfig = res["data"]["dmc_config"] ? JSON.parse(res["data"]["dmc_config"]) : [];

        }

      }

      this.getUserUdrfConfig();

    }, err => {

      // let errReportingTeams = "KEBS";

      // this.utilityService.showErrorMessage(err, errReportingTeams);

      this.errorService.userErrorAlert((err && err.code) ? err.code : (err && err.error) ? err.error.code : 'NIL', "Error while retrieving Data Try Again After Sometime", (err && err.params) ? err.params : (err && err.error) ? err.error.params : {});

    });

  }

  getUserUdrfConfig() {
    if(localStorage.getItem(`Report_`+this.udrfData?.applicationId)){
      let config_id = localStorage.getItem(`Report_`+this.udrfData?.applicationId);
      let userConfigId = JSON.parse(config_id);
      this.udrfData.userConfigId = userConfigId?.userConfigId;
      this.udrfData.appliedConfig = userConfigId?.applied_config;
      this.resolveColumnConfig();
      this.calculateDefaultAppUdrfConfig();
    }else{
      this.getUdrfUserConfig(this.udrfData).pipe(takeUntil(this._onResetUdrfData)).subscribe(async res => {

        if (res["messType"] == "S" && res["data"].length > 0) {
  
          this.udrfData.userConfigId = res["data"][0]["id"] ? res["data"][0]["id"] : 0;
  
          this.udrfData.appliedConfig = res["data"][0]["applied_config"] ? JSON.parse(res["data"][0]["applied_config"]) : {};
  
        }
  
        this.resolveColumnConfig();
  
        this.calculateDefaultAppUdrfConfig();
  
      }, err => {
        console.log(err)
        // let errReportingTeams = "KEBS";
  
        // this.utilityService.showErrorMessage(err, errReportingTeams);
  
        this.errorService.userErrorAlert((err && err.code) ? err.code : (err && err.error) ? err.error.code : 'NIL', "Error while retrieving Data Try Again After Sometime", (err && err.params) ? err.params : (err && err.error) ? err.error.params : {});
  
      });
    }

  }

  async calculateDefaultAppUdrfConfig() {

    for (let dataSourceItem of this.udrfData.udrfDataSource) {

      dataSourceItem["currentSortOrder"] = "I";

      if (dataSourceItem.isgroupBy)
        this.udrfData.groupByArray.push(dataSourceItem);


      if (dataSourceItem.isSortOnly)
        this.udrfData.sortOnlyArray.push(dataSourceItem);

      else {

        dataSourceItem["searchParameter"] = "";
        dataSourceItem["isSearchBoxVisible"] = false;
        dataSourceItem["isSelectDropdownVisible"] = false;
        dataSourceItem["multiOptionSelectSearchValues"] = [];
        dataSourceItem["checkboxValues"] = [];

        if (dataSourceItem["hasCustomRangeButton"])
          dataSourceItem["isCustomRangeButtonActivated"] = false;

        if (dataSourceItem.isFilterRange && !dataSourceItem.hasMasterDataApi) {

          let currentCustomRangeData = _.where(this.udrfData.customRangeData, { filterId: dataSourceItem.filterId });

          if (currentCustomRangeData.length > 0) {

            dataSourceItem.multiOptionSelectSearchValues = currentCustomRangeData[0].multiOptionSelectSearchValues;
            dataSourceItem.checkboxValues = currentCustomRangeData[0].checkboxValues;

            if (dataSourceItem.customRangeButtonDataType == "date" && dataSourceItem.isMainDateRangeFilter) {

              let selectedCheckboxValue = _.where(currentCustomRangeData[0].checkboxValues, { isCheckboxDefaultSelected: true });

              if (selectedCheckboxValue.length > 0) {

                this.udrfData.mainApiDateRangeStart = selectedCheckboxValue[0].checkboxStartValue;

                this.udrfData.mainApiDateRangeEnd = selectedCheckboxValue[0].checkboxEndValue;

              }

            }

          }

        }

        else if (dataSourceItem.areValuesConditions && !dataSourceItem.hasMasterDataApi) {

          let currentValuesOfConditionsData = _.where(this.udrfData.valuesOfConditionsData, { filterId: dataSourceItem.filterId });

          if (currentValuesOfConditionsData.length > 0) {

            dataSourceItem.multiOptionSelectSearchValues = currentValuesOfConditionsData[0].multiOptionSelectSearchValues;
            dataSourceItem.checkboxValues = currentValuesOfConditionsData[0].checkboxValues;

          }

        }

        else
          dataSourceItem["checkboxValues"] = await this.udrfFunctions.transformMasterData(dataSourceItem, dataSourceItem.masterData);

        let doesFilterNameExist = _.where(this.udrfData.filterTypeArray, { filterId: dataSourceItem.filterId });

        if (doesFilterNameExist.length == 0)
          this.udrfData.filterTypeArray.push(dataSourceItem);

      }

    }

    this.udrfUiData.groupByUiArray.push({
      groupByItemId: 0,
      groupByItemName: "None",
      isSelected: true,
      groupByItemValues: [],
      groupBy: true,
    });

    for (let items of this.udrfData.groupByArray) {

      this.udrfUiData.groupByUiArray.push({
        groupByItemId: items.filterId,
        groupByItemName: items.filterName,
        isSelected: false,
        groupByItemValues: []
      });
    }

    if (this.udrfData.groupByArray.length > 0) {
      this.needToShowGroupByButton = true;
    }
    else {
      this.needToShowGroupByButton = false;
    }

    await this.udrfFunctions.configureUdrfUserConfig();

    this.udrfData.areSortAndFiltersLoading = false;

    this.udrfFunctions.applyConfig(true);

  }

  getUdrfAppConfig(apiParams) {

    return this.http.post('/api/udrf/getUdrfAppConfig', {
      apiParams: apiParams
    });

  }

  retrieveUdrfMoreFilterData(filterTypeArrayItem) {

    return this.http.post('/api/udrf/retrieveUdrfMoreFilterData', {
      filterTypeArrayItem: filterTypeArrayItem
    });

  }

  getUdrfUserConfig(udrfData) {

    return this.http.post('/api/udrf/getUdrfUserConfig', {
      associateOId: udrfData.currentUserOId,
      applicationId: udrfData.applicationId
    });

  }

  updateUdrfUserConfig(udrfData) {
      return this.http.post('/api/udrf/updateUdrfUserConfig', {
        userConfigId: udrfData.userConfigId,
        applicationId: udrfData.applicationId,
        reportName: udrfData.reportName,
        associateOId: udrfData.currentUserOId,
        appliedConfig: udrfData.appliedConfig,
        myFilterNotifyUserArray: udrfData.myFilterNotifyUserArray
      });
      
    }

  resetUdrfUserConfig(udrfData) {
    const keys: string[] = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if(key && key.startsWith(`Report_`)) {
        localStorage.removeItem(key)
      }
    }
    localStorage.removeItem(`is_filter_updated`);
    return this.http.post('/api/udrf/resetUdrfUserConfig', {
      userConfigId: udrfData.userConfigId
    });

  }

  resetUdrfData() {

    this._onResetUdrfData.next();
    this._onResetUdrfData.complete();

    this.udrfData = JSON.parse(JSON.stringify(this.udrfDataDefault));
    this.udrfUiData = JSON.parse(JSON.stringify(this.udrfUiDataDefault));
    this.udrfFormData.selectedCurrency=new FormControl("")
    this.udrfUiData.resolveColumnConfig = this.resolveColumnConfig.bind(this);

    this.udrfBodyData = [];

    this._onResetUdrfData = new Subject<void>();

  }

  resetUdrfDmcData() {

    this.udrfUiData.udrfHasDmcConfig = false;

    this.udrfUiData.udrfDmcConfig = [];

  }

  storeOldUdrfDataAndReset() {

    this.udrfDataOld = clone(this.udrfData);
    this.udrfUiDataOld = clone(this.udrfUiData);
    this.udrfBodyDataOld = clone(this.udrfBodyData);

    this.resetUdrfData();

    this.udrfUiData.udrfHasDmcConfig = JSON.parse(JSON.stringify(this.udrfUiDataOld.udrfHasDmcConfig));
    this.udrfUiData.udrfDmcConfig = JSON.parse(JSON.stringify(this.udrfUiDataOld.udrfDmcConfig));

  }

  restoreOldUdrfData() {

    this.udrfData = this.udrfDataOld;
    this.udrfUiData = this.udrfUiDataOld;

    this.udrfUiData.resolveColumnConfig = this.resolveColumnConfig.bind(this);

    this.udrfBodyData = this.udrfBodyDataOld;

  }

  //common functions for group by

  // groupByHeaderData()
  // {
  //   for(let i =0; i< this.udrfUiData.groupByUiArray.length;i++)
  //   {
  //     if(this.udrfUiData.groupByUiArray[i].groupByItemId == this.udrfUiData.groupByData['filterId'] )
  //     {
  //       for(let j =0; j< this.udrfGroupByHeaderData.length;j++)
  //       {
  //         this.udrfUiData.groupByUiArray[i].groupByItemValues.push(
  //           {
  //             categoryName: this.udrfGroupByHeaderData[j].status,
  //             categoryCount: this.udrfGroupByHeaderData[j].groupByCount,
  //             categoryItems: [],
  //             expandAll: true,
  //           }
  //         )
  //       }
  //     }
  //   }
  // }

  getCountForAllFilters() {
    this.filterCountArray = [];
    let i = 0;
    if (!this.udrfUiData.toggleChecked) {
      for (let filterTypeArrayItem of this.udrfData.filterTypeArray) {
        if (filterTypeArrayItem.multiOptionSelectSearchValues.length > 0) {
          this.filterCountArray.push({
            filterColumn: filterTypeArrayItem.filterColumnName,
            filterName: filterTypeArrayItem.filterName,
            filterValues: []
          })
          if (filterTypeArrayItem.hasHierarchyWithinCheckbox) {
            for (let items of filterTypeArrayItem.checkboxValues) {
              this.filterCountArray[i].filterValues.push({
                id: items.checkboxId,
                value: items.checkboxName,
                count: 0
              })
              if (items.hasCheckboxValues) {
                for (let dataItems of items.checkboxValues) {
                  this.filterCountArray[i].filterValues.push({
                    id: dataItems.checkboxId,
                    value: dataItems.checkboxName,
                    count: 0
                  })
                  if (items.hasCheckboxValues) {
                    for (let dataInnerItems of dataItems.checkboxValues) {
                      this.filterCountArray[i].filterValues.push({
                        id: dataInnerItems.checkboxId,
                        value: dataInnerItems.checkboxName,
                        count: 0
                      })
                    }

                  }
                }
              }
            }
          }
          else {
            for (let items of filterTypeArrayItem.checkboxValues) {
              this.filterCountArray[i].filterValues.push({
                id: items.checkboxId,
                value: items.checkboxName,
                count: 0
              })

            }
          }
          i++;

        }
      }
    }
    else {
      for (let filterTypeArrayItem of (this.udrfUiData.toggleChecked ? this.udrfData.filterTypeArray : this.udrfData.mainFilterArray)) {
        this.filterCountArray.push({
          filterColumn: filterTypeArrayItem.filterColumnName,
          filterName: filterTypeArrayItem.filterName,
          filterValues: []
        })
        if (filterTypeArrayItem.hasHierarchyWithinCheckbox) {
          for (let items of filterTypeArrayItem.checkboxValues) {
            this.filterCountArray[i].filterValues.push({
              id: items.checkboxId,
              value: items.checkboxName,
              count: 0
            })
            if (items.hasCheckboxValues) {
              for (let dataItems of items.checkboxValues) {
                this.filterCountArray[i].filterValues.push({
                  id: dataItems.checkboxId,
                  value: dataItems.checkboxName,
                  count: 0
                })
                if (items.hasCheckboxValues) {
                  for (let dataInnerItems of dataItems.checkboxValues) {
                    this.filterCountArray[i].filterValues.push({
                      id: dataInnerItems.checkboxId,
                      value: dataInnerItems.checkboxName,
                      count: 0
                    })
                  }

                }
              }
            }
          }
        }
        else {
          for (let items of filterTypeArrayItem.checkboxValues) {
            this.filterCountArray[i].filterValues.push({
              id: items.checkboxId,
              value: items.checkboxName,
              count: 0
            })

          }
        }
        i++;
      }
    }


  }
  async setCheckboxCount() {
    if (!this.udrfUiData.toggleChecked) {
      for (let i = 0; i < this.udrfUiData.countResponseData.length; i++) {
        for (let j = 0; j < this.udrfData.filterTypeArray.length; j++) {
          if (this.udrfUiData.countResponseData[i].filterName == this.udrfData.filterTypeArray[j].filterName && !this.udrfData.filterTypeArray[j].hasHierarchyWithinCheckbox) {
            for (let k = 0; k < this.udrfData.filterTypeArray[j].checkboxValues.length; k++) {
              this.udrfData.filterTypeArray[j].checkboxValues[k].checkboxCount = this.udrfUiData.countResponseData[i].filterValues[k].count
            }
          }
          else if (this.udrfUiData.countResponseData[i].filterName == this.udrfData.filterTypeArray[j].filterName && this.udrfData.filterTypeArray[j].hasHierarchyWithinCheckbox) {
            for (let items of this.udrfData.filterTypeArray[j].checkboxValues) {
              let itemIndex = _.findIndex(this.udrfUiData.countResponseData[i].filterValues, { value: items.checkboxName });
              items.checkboxCount = this.udrfUiData.countResponseData[i].filterValues[itemIndex].count;
              if (items.hasCheckboxValues) {
                for (let dataItems of items.checkboxValues) {
                  let itemDataIndex = _.findIndex(this.udrfUiData.countResponseData[i].filterValues, { value: dataItems.checkboxName });
                  dataItems.checkboxCount = this.udrfUiData.countResponseData[i].filterValues[itemDataIndex].count;
                  items.checkboxCount += dataItems.checkboxCount;
                  if (dataItems.hasCheckboxValues) {
                    for (let innerDataItems of dataItems.checkboxValues) {
                      let innerItemDataIndex = _.findIndex(this.udrfUiData.countResponseData[i].filterValues, { value: innerDataItems.checkboxName });
                      innerDataItems.checkboxCount = this.udrfUiData.countResponseData[i].filterValues[innerItemDataIndex].count;
                      dataItems.checkboxCount += innerDataItems.checkboxCount;
                      items.checkboxCount += innerDataItems.checkboxCount;
                    }

                  }
                }
              }
            }
          }
        }
      }

    }
    else {
      for (let i = 0; i < (this.udrfData.filterTypeArray.length && this.udrfUiData.countResponseData.length); i++) {
        if (!this.udrfData.filterTypeArray[i].hasHierarchyWithinCheckbox) {
          for (let j = 0; j < (this.udrfData.filterTypeArray[i].checkboxValues.length && this.udrfUiData.countResponseData[i].filterValues.length); j++) {
            let index = _.findIndex(this.udrfData.filterTypeArray[i].checkboxValues, { checkboxName: this.udrfUiData.countResponseData[i].filterValues[j].value })
            this.udrfData.filterTypeArray[i].checkboxValues[index].checkboxCount = this.udrfUiData.countResponseData[i].filterValues[j].count;
          }
        }
        else {
          for (let items of this.udrfData.filterTypeArray[i].checkboxValues) {
            let itemIndex = _.findIndex(this.udrfUiData.countResponseData[i].filterValues, { value: items.checkboxName });
            items.checkboxCount = this.udrfUiData.countResponseData[i].filterValues[itemIndex].count;
            if (items.hasCheckboxValues) {
              for (let dataItems of items.checkboxValues) {
                let itemDataIndex = _.findIndex(this.udrfUiData.countResponseData[i].filterValues, { value: dataItems.checkboxName });
                dataItems.checkboxCount = this.udrfUiData.countResponseData[i].filterValues[itemDataIndex].count;
                items.checkboxCount += dataItems.checkboxCount;
                if (dataItems.hasCheckboxValues) {
                  for (let innerDataItems of dataItems.checkboxValues) {
                    let innerItemDataIndex = _.findIndex(this.udrfUiData.countResponseData[i].filterValues, { value: innerDataItems.checkboxName });
                    innerDataItems.checkboxCount = this.udrfUiData.countResponseData[i].filterValues[innerItemDataIndex].count;
                    dataItems.checkboxCount += innerDataItems.checkboxCount;
                    items.checkboxCount += innerDataItems.checkboxCount;
                    // items.checkboxCount -= innerDataItems.checkboxCount
                  }
                }
              }
            }
          }
        }
      }
    }
    this.udrfUiData.durationCount = [];
  }




  async setCheckboxCountToEmpty() {
    for (let i = 0; i < this.udrfData.filterTypeArray.length; i++) {
      for (let j = 0; j < this.udrfData.filterTypeArray[i].checkboxValues.length; j++) {
        this.udrfData.filterTypeArray[i].checkboxValues[j].checkboxCount = 0;
      }
    }

  }

  async setDurationCount() {
    let index = _.findIndex(this.udrfUiData.countResponseData, { filterName: "Duration" });
    for (let i = 0; i < this.udrfUiData.countResponseData[index].filterValues.length; i++) {
      if (this.udrfUiData.countResponseData[index].filterValues[i].value == this.udrfUiData.durationCount[0].Type) {
        this.udrfUiData.countResponseData[index].filterValues[i].count = this.udrfUiData.durationCount[0].count
      }
    }
  }

  resolveMyView() {

    if (this.udrfData.appliedConfig["activeView"].length > 0) {

      for (let items of this.udrfUiData.groupByUiArray) {
        if (items.groupByItemId == this.udrfData.appliedConfig["activeView"][0].groupByViewArray[0].filterId) {

          items.isSelected = true;

        }
        else {
          items.isSelected = false;
        }
      }
    }
  }

  async openNewReleasesModalUDRF() {

    this.udrfUiData.notifyRelease = Boolean(await this.openNewReleasesModal(this.udrfData.applicationId, this.dialog));
  }

  async getNotifyReleasesUDRF() {
    this.getNotifyReleases(this.udrfData.applicationId)
      .subscribe(async res => {
        let existingItem = localStorage.getItem("Application" + this.udrfData.applicationId + "Version");

        if (existingItem != res['data']['version']) {
          this.udrfUiData.notifyReleaseAnimated = true;
          await this.openNewReleasesModalUDRF();
          this.udrfUiData.notifyReleaseAnimated = false;
          this.udrfUiData.notifyRelease = res['data']['_id'];
          localStorage.setItem("Application" + this.udrfData.applicationId + "Version", res['data']['version']);

        }
      });
  }

  async openNewReleasesModal(modalParams, dialog) {
    try {
      return new Promise(async (resolve, reject) => {


        const { NewReleasesModalComponent } = await import('../../modules/shared-lazy-loaded-components/new-releases-modal/new-releases-modal.component');

        const openNewReleasesModal = dialog.open(NewReleasesModalComponent, {
          height: '55%',
          width: '50%',
          maxWidth: '86%',
          data: modalParams
        });
        openNewReleasesModal.afterClosed().subscribe(res => {
          return resolve(res);
        })


      });
    }
    catch (err) {
      console.log(err);
    }
  }

  getNewReleases(application_id) {
    return this.http.post('/api/master/getNewReleases', {
      application_id: application_id
    });
  }

  getNotifyReleases(application_id) {
    return this.http.post('/api/master/getNotifyReleases', {
      application_id: application_id
    });
  }

  returnVisibleBodyColumnsForDownload(data) {

    let udrfVisibleColumns = [];

    for (const udrfBodyColumnsItem of this.udrfUiData.udrfVisibleBodyColumns)
      if (udrfBodyColumnsItem.isVisible != null && (udrfBodyColumnsItem.isVisible == "true" || udrfBodyColumnsItem.isVisible == true)) {

        if (udrfBodyColumnsItem.item)
          udrfVisibleColumns.push(udrfBodyColumnsItem.item);

        if (udrfBodyColumnsItem.alternateFieldName)
          udrfVisibleColumns.push(udrfBodyColumnsItem.alternateFieldName);

        if (udrfBodyColumnsItem.header)
          udrfVisibleColumns.push(udrfBodyColumnsItem.header);

      }

    udrfVisibleColumns = _.uniq(udrfVisibleColumns);

    for (let excelDataItem of data)
      for (const excelDataKey of Object.keys(excelDataItem))
        if (!_.contains(udrfVisibleColumns, excelDataKey))
          delete excelDataItem[excelDataKey];

    return data;

  }

  shouldDisplayKey(key) {

    let currentKey = _.filter(this.udrfUiData.udrfBodyColumns, function (udrfBodyColumnsItem) {

      if ((udrfBodyColumnsItem.item == key || (udrfBodyColumnsItem.alternateFieldName != null && udrfBodyColumnsItem.alternateFieldName == key) || (udrfBodyColumnsItem.header && udrfBodyColumnsItem.header == key)) &&
        (udrfBodyColumnsItem.isVisible != null && (udrfBodyColumnsItem.isVisible == "true" || udrfBodyColumnsItem.isVisible == true)))
        return udrfBodyColumnsItem;

    }, this);

    if (currentKey.length > 0)
      return true;

    else
      return false;

  }

}