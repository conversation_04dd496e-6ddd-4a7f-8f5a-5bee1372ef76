@import '~ngx-owl-carousel-o/lib/styles/scss/owl.carousel';
@import '~ngx-owl-carousel-o/lib/styles/scss/owl.theme.default';
/* Default theme. ~960B */
@import '~@vime/core/themes/default.css';

/* Optional light theme (extends default). ~400B */
@import '~@vime/core/themes/light.css';
// Custom Theming for Angular Material
// For more information: https://material.angular.io/guide/theming
@import '~@angular/material/theming';
// Plus imports for other components in your app.

// Include the common styles for Angular Material. We include this here so that you only
// have to load a single css file for Angular Material in your app.
// Be sure that you only ever include this mixin once!
@include mat-core();

// Define the palettes for your theme using the Material Design palettes available in palette.scss
// (imported above). For each palette, you can optionally specify a default, lighter, and darker
// hue. Available color palettes: https://material.io/design/color/
$newWebAppication-primary: mat-palette($mat-indigo);
$newWebAppication-accent: mat-palette($mat-pink, A200, A100, A400);

// The warn palette is optional (defaults to red).
$newWebAppication-warn: mat-palette($mat-red);

// Create the theme object. A theme consists of configurations for individual
// theming systems such as "color" or "typography".
$newWebAppication-theme: mat-light-theme((color: (primary: $newWebAppication-primary,
        accent: $newWebAppication-accent,
        warn: $newWebAppication-warn,
      )));

// Include theme styles for core and each component used in your app.
// Alternatively, you can import and @include the theme mixins for each component
// that you are using.
@include angular-material-theme($newWebAppication-theme);

/* You can add global styles to this file, and also import other style files */
/* You can add global styles to this file, and also import other style files */
// @import "~@angular/material/prebuilt-themes/purple-green.css";

html,
body {
  height: 100vh;
  width: 100%;
  background-color: #f4f3ef;
  font-family: "Roboto", "Helvetica Neue", "sans-serif" !important;
}

body {
  margin: 0;
}

.custom-bank-bottom-sheet {
  min-width: 95vw !important;
  height: 65vh;
}

.custom-pg-bottom-sheet {
  min-width: 70vw !important;
  height: 7vh
}

//Gantt - Planning board main menu

.custom-gantt-menu-btn {
  background-color: #e9e9e9 !important;
  border-radius: 0px;
  color: #000000 !important;
}

.custom-gantt-menu-btn:hover {
  background-color: #9c9c9cc9 !important;
  border-radius: 0px;
  color: #000000 !important;
}

.custom-gantt-menu-btn-panel {
  width: 4vw !important;
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
}

.activity-checklist-custom-class {
  height: 100%;
}


.flex-1 {
  flex: 1;
}

.mw-15 {
  max-width: 15% !important;
}

.mw-25 {
  max-width: 25% !important;
}

mat-dialog-container {
  padding: 0 !important;
}

.slideInRight {
  -webkit-animation-name: slideInRight;
  animation-name: slideInRight;
  -webkit-animation-duration: 0.3s;
  animation-duration: 0.3s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
  max-width: 100vw !important;

  .mat-dialog-container {
    border-radius: 0 !important;
  }
}

@media (min-width: 768px) {
  .slideInRight {
    max-width: 90vw !important;
  }
}

@media (min-width: 960px) {
  .col-lg-2-5 {
    flex: 0 0 20%;
    max-width: 20%;
  }

  .mw-lg-15 {
    max-width: 15% !important;
  }
}

.big-modal {
  max-width: 90vw !important;
}

@-webkit-keyframes slideInRight {
  0% {
    -webkit-transform: translateX(100%);
    transform: translateX(100%);
    visibility: visible;
  }

  100% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  0% {
    -webkit-transform: translateX(100%);
    transform: translateX(100%);
    visibility: visible;
  }

  100% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
}

button:focus {
  outline: none !important;
}

.cursor-pointer {
  cursor: pointer !important;
}

.overflow-scroll {
  overflow-y: scroll !important;
}

.overflow-hidden {
  overflow: hidden;
}

.mat-form-field-appearance-outline .mat-form-field-outline-thick {
  color: #d63031 !important;
}

.mat-tab-nav-bar {
  margin-left: 15px !important;
}

.mat-tab-link {
  height: 44px !important;
  font-size: small !important;
  padding: 0 20px !important;
  min-width: 155px !important;
}

.card {
  box-shadow: 0px 2px 1px -1px rgba(0, 0, 0, 0.2),
    0px 1px 1px 0px rgba(0, 0, 0, 0.14), 0px 1px 3px 0px rgba(0, 0, 0, 0.12);
}

.popover-container {
  .popover-body {
    padding: 0px !important;
  }

  mat-card-subtitle {
    color: black !important;
    font-weight: 500 !important;
  }

  mat-card-actions {
    display: flex;
    justify-content: center;
  }
}

.mat-pseudo-checkbox-checked::after {
  width: 10px !important;
  height: 5px !important;
}

.mat-button-toggle-checked {
  background-color: #e0e0e0a1 !important;
}

.mat-button-toggle-appearance-standard .mat-button-toggle-label-content {
  line-height: 30px !important;
  padding: 0 11px !important;
  font-weight: 400 !important;
  font-size: 14px !important;
}

.row {
  margin-left: 0px !important;
  margin-right: 0px !important;
}

.bg-success {
  background-color: #d4f7e3 !important;

  div {
    background-color: #2ed573 !important;
  }
}

.bg-inprogress {
  background-color: #ffedcc !important;

  div {
    background-color: #ffa502 !important;
  }
}

.bg-open {
  background-color: #e2e2e2 !important;

  div {
    background-color: #e2e2e2 !important;
  }
}

.bg-payment {
  background-color: #c3e5ce !important;

  div {
    background-color: #009432 !important;
  }
}

.mat-button {
  font-weight: normal !important;
}

//input field
.mat-form-field {
  .mat-form-field-wrapper {
    padding-bottom: 8px !important;
  }

  .mat-form-field-underline {
    bottom: 0;
  }

  .mat-form-field-infix {
    padding: 0.75em 0 0.75em 0 !important;
    border-top: 0.54375em solid transparent !important;
  }

  .mat-select-arrow {
    margin: 0px 4px 0px 4px !important;
  }
}

.mat-option {
  font-size: 14px !important;
}

.mat-input-element:disabled {
  color: #858c93 !important;
}

/**  Scroll bar */
::-webkit-scrollbar-track {
  min-height: 120px !important;
  box-shadow: inset #e0e0d1 !important;
  background-color: #f5f5f5 !important;
}

::-webkit-scrollbar {
  width: 8px !important;
  height: 8px !important;
}

// Hari to confirm - for broader scrollbar on hover
// :hover::-webkit-scrollbar{
//   width: 13px !important;
//   height: 13px !important;
// }

::-webkit-scrollbar-thumb {
  min-height: 180px;
  border-radius: 4px !important;
  background-color: #babfc4 !important;
}

//status

//project #fdcb6e, #e15f41, 3c6382
.status_1,
.draft {
  background-color: #3c6382 !important;
  color: white;
}

.status_2 {
  background-color: #e2e2e2 !important;
  color: #1e2733;
}

.status_3,
.delivery_mandate {
  background-color: #474787 !important;
  color: white;
}

.status_4,
.inprogress {
  background-color: #ffa502;
  color: white;

  * {
    color: white !important;
  }
}

.status_5,
.completed {
  background-color: #009432;
  color: white;

  * {
    color: white !important;
  }
}

.status_6,
.cancelled {
  background-color: black;
  color: white;
}

.status_7,
.completed {
  background-color: #d1d9db;
  color: #1e2733;
}

.status_8 {
  background-color: #648588;
  color: white;
}

.status_9 {
  background-color: #ff7200;
  color: white;
}

.status_10 {
  background-color: #00e64d;
  color: white;
}

.status_11 {
  background-color: #009432;
  color: white;
}

.bg_8 {
  border-left: #e2e2e2 5px solid !important;
}

//Payment Status and status border

.bg_1 {
  border-left: #e2e2e2 5px solid !important;
}

.bg_7 {
  border-left: #e2e2e2 5px solid !important;
}

.bg_2 {
  border-left: #ffa502 5px solid !important;
}

.bg_4 {
  border-left: #ffa502 5px solid !important;
}

.bg_3 {
  border-left: #009432 5px solid !important;
}

.bg_8 {
  border-left: #648588 5px solid !important;
}

.bg_9 {
  border-left: #ff7200 5px solid !important;
}

.bg_10 {
  border-left: #00e64d 5px solid !important;
}

.bg_11 {
  border-left: #009432 5px solid !important;
}

.pay_8 {
  background-color: #7f8c8d;
  color: white;
}

.pay_9 {
  background-color: #fb8c00;
  color: white;
}

.pay_10 {
  background-color: #0cc34a;
  color: white;
}

.pay_11 {
  background-color: #009432;
  color: white;
}

//Presales - Status Indicators

//Prospecting
.presales_Z01 {
  background-color: #ffb142;
  color: white;
}

.preborder_Z01 {
  border-left: 4px solid #ffb142 !important;
}

//proposal submit
.presales_Z04 {
  background-color: #484198;
  color: white;
}

.preborder_Z04 {
  border-left: 4px solid #484198 !important;
}

//Pricing/Negotiation
.presales_Z05 {
  background-color: #ee5a24;
  color: white;
}

.preborder_Z05 {
  border-left: 4px solid #ee5a24 !important;
}

//Contract
.presales_Z06 {
  background-color: #6ab04c;
  color: white;
}

.preborder_Z06 {
  border-left: 4px solid #6ab04c !important;
}

//SHS Pending
.presales_Z07 {
  background-color: #e2e2e2 !important;
  color: #1e2733;
}

.preborder_Z07 {
  border-left: 4px solid #e2e2e2 !important;
}

//Closed and won
.presales_Z08 {
  background-color: #009432;
  color: white;
}

.preborder_Z08 {
  border-left: 4px solid #009432 !important;
}

//Closed and lost
.presales_Z09 {
  background-color: #ea2027;
  color: white;
}

.preborder_Z09 {
  border-left: 4px solid #ea2027 !important;
}

//postponed
.presales_Z12 {
  background-color: #ffc312;
  color: #1e2733;
}

.preborder_Z12 {
  border-left: 4px solid #ffc312 !important;
}

//Presales
.presales_Z13 {
  background-color: #833471;
  color: white;
}

.preborder_Z13 {
  border-left: 4px solid #833471 !important;
}

//Lead status
// prospecting
.salesStatus_1 {
  background-color: #ffb142;
}

.salesStatusBorder_1 {
  border-left: 3px solid #ffb142;
}

// Moved to opportunity
.salesStatus_2 {
  background-color: #009432;
}

.salesStatusBorder_2 {
  border-left: 3px solid #009432;
}

// Lead nurturing
.salesStatus_3 {
  background-color: #9980fa;
}

.salesStatusBorder_3 {
  border-left: 3px solid #9980fa;
}

// Dropped
.salesStatus_4 {
  background-color: #c0392b;
}

.salesStatusBorder_4 {
  border-left: 3px solid #c0392b;
}

// opportunities

// Created
.salesStatus_5 {
  background-color: #fff200;
}

.salesStatusBorder_5 {
  border-left: 3px solid #fff200;
}

// qualified
.salesStatus_6 {
  background-color: #b8e994;
}

.salesStatusBorder_6 {
  border-left: 3px solid #b8e994;
}

// Response prepared
.salesStatus_7 {
  background-color: #484198;
}

.salesStatusBorder_7 {
  border-left: 3px solid #484198;
}

// Bid approved
.salesStatus_8 {
  background-color: #1dd1a1;
}

.salesStatusBorder_8 {
  border-left: 3px solid #1dd1a1;
}

// Submitted
.salesStatus_9 {
  background-color: #ff9f1a;
}

.salesStatusBorder_9 {
  border-left: 3px solid #ff9f1a;
}

// Evaluation completed
.salesStatus_10 {
  background-color: #ffb8b8;
}

.salesStatusBorder_10 {
  border-left: 3px solid #ffb8b8;
}

// Negotiation complete
.salesStatus_11 {
  background-color: #95afc0;
}

.salesStatusBorder_11 {
  border-left: 3px solid #95afc0;
}

// Closed Won
.salesStatus_12 {
  background-color: #6ab04c;
}

.salesStatusBorder_12 {
  border-left: 3px solid #6ab04c;
}

// Closed lost
.salesStatus_13 {
  background-color: #ea2027;
}

.salesStatusBorder_13 {
  border-left: 3px solid #ea2027;
}

// Postponed
.salesStatus_14 {
  background-color: #3c6382;
}

.salesStatusBorder_14 {
  border-left: 3px solid #3c6382;
}

// closed cancelled
.salesStatus_15 {
  background-color: #ff4d4d;
}

.salesStatusBorder_15 {
  border-left: 3px solid #ff4d4d;
}

// Contract signed
.salesStatus_16 {
  background-color: #78e08f;
}

.salesStatusBorder_16 {
  border-left: 3px solid #78e08f;
}

// SHS completed
.salesStatus_17 {
  background-color: #009432;
}

.salesStatusBorder_17 {
  border-left: 3px solid #009432;
}

// SHS completed
.salesStatus_20 {
  background-color: #e948db;
}

.salesStatusBorder_20 {
  border-left: 3px solid #e948db;
}

// SHS completed
.salesStatus_21 {
  background-color: #e948db;
}

.salesStatusBorder_21 {
  border-left: 3px solid #e948db;
}

//proposal status

//open
.proposalStatus_1 {
  background-color: #e2e2e2;
}

//Inprogress
.proposalStatus_2 {
  background-color: #ffa502;
}

//Inapproval
.proposalStatus_3 {
  background-color: #ff4d4d;
}

//Approved
.proposalStatus_4 {
  background-color: #6ab04c;
}

//completed
.proposalStatus_5 {
  background-color: #009432;
}

//onHold
.proposalStatus_6 {
  background-color: #fff200;
}

//others
.proposalStatus_7 {
  background-color: #3c6382;
}

//closedWon
.proposalStatus_8 {
  background-color: #78e08f;
}

//closedLost
.proposalStatus_9 {
  background-color: #c0392b;
}

//followUp
.proposalStatus_10 {
  background-color: #ffb142;
}

.my-tooltip-multi-line {
  text-align: center;
  white-space: pre;
}

.my-tooltip-max-width {
  max-width: none !important;
}

.swal2-container {
  .container-class {}

  .popup-class {
    width: 30em !important;
  }

  .header-class {
    .swal2-icon {
      width: 4em !important;
      height: 4em !important;
    }
  }

  .title-class {
    font-size: 20px !important;
    font-weight: 500 !important;
  }

  .close-button-class {}

  .image-class {}

  .content-class {}

  .input-class {}

  .actions-class {}

  .confirm-button-class {
    font-weight: 500 !important;
    font-size: 14px !important;
    //   margin-right: 35px !important;
  }

  .cancel-button-class {
    font-weight: 500 !important;
    font-size: 14px !important;
  }

  .footer-class {}
}

.md-drppicker td.active,
.md-drppicker td.active:hover,
.md-drppicker .ranges ul li button.active {
  background-color: #cf0001 !important;
}

.md-drppicker.double,
.md-drppicker {
  margin-top: 40px !important;
}

.md-drppicker .btn,
.md-drppicker .btn:hover,
.md-drppicker .btn:focus {
  text-transform: none !important;
  background-color: #cf0001 !important;
  font-size: 14px !important;
  margin-left: 5px;
}

.md-drppicker .btn.btn-default,
.md-drppicker .btn.btn-default:hover,
.md-drppicker .btn.btn-default:focus {
  background-color: transparent !important;
  box-shadow: none !important;
  font-size: 14px !important;
}

.dx-pivotgrid-container {
  .dx-area-field-content {
    font-weight: 500;
    color: #191919;
  }
}

.dx-pivotgrid .dx-pivotgrid-area td {
  color: #000 !important;
}

.dx-pivotgrid .dx-filter-header .dx-pivotgrid-fields-area {
  background-color: #f8f8f8;
}

.dx-context-menu .dx-menu-item .dx-menu-item-content {
  font-size: 14px;
}

.dx-list-select-all {
  font-size: 14px;
}

.dx-list-item-content {
  font-size: 14px;
}

.dx-list-item-content {
  padding: 14px 8px 10px;
}

.dx-list-select-all-label {
  padding: 0 28px;
}

.dx-pivotgrid-fields-container .dx-area-field.dx-area-box .dx-header-filter {
  width: 16px;
  height: 16px;
  line-height: 1;
  padding-top: 1px;
  padding-left: 1px;
  color: #ce2222;
  // background-color: #ce2222;
  // border-radius: 50%;
  // box-shadow: 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14),
  //   0 1px 5px 0 rgba(0, 0, 0, 0.12);
}

.dx-filter-header .dx-pivotgrid-fields-area {
  background-color: #f1f1f1;
}

.dx-data-header {
  background-color: #f1f1f1;
}

.dx-pivotgrid-fields-area .dx-area-fields .dx-pivotgrid-drag-action {
  background-color: #f1f1f1;
}

.dx-area-description-cell .dx-pivotgrid-background {
  background-color: #f1f1f1;
}

.dx-header-filter:not(.dx-header-filter-empty) {
  color: #cf0001;
}

.dx-loadindicator-wrapper {
  padding: 2px !important;
}

.dx-pivotgrid-fields-container .dx-area-field.dx-area-box {
  color: black;
  background-color: #f7f7f7;
  border-radius: 21px;
  border: none;
  border-color: rgba(0, 0, 0, 0.12);
  font-size: 12px;
  box-shadow: 0 3px 1px -2px rgba(85, 75, 75, 0.2),
    0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12);
  font-weight: normal;
}

.dx-area-field-content {
  font-weight: 500;
  color: black;
}

.dx-pivotgrid-fields-container .dx-area-field.dx-area-box .dx-header-filter.dx-header-filter-empty {
  color: #7e7e7e;
  background-color: transparent !important;
  border-radius: 0%;
  box-shadow: none !important;
}

//background-color: #ffe3bc66  #ffe3bc87;
.dx-sort {
  color: rgba(0, 0, 0, 0.54);
}

.dx-button-has-icon .dx-icon {
  font-size: 21px !important;
}

.dx-pivotgrid {
  .dx-overlay {
    .dx-overlay-wrapper {
      .dx-overlay-content {
        max-height: 45px !important;
        max-width: 45px !important;
      }

      .dx-loadpanel-content {
        padding: 6px;
        // .dx-loadindicator-segment2,.dx-loadindicator-segment1{
        //   .dx-loadindicator-segment-inner{
        //     border-color: #cf0001;
        //    border-bottom-color: transparent;
        //   }
        // }
      }
    }
  }
}

// File Upload Shake Animation

@keyframes shake {

  0% {
    transform: translate(1px, 1px) rotate(0deg);
  }

  10% {
    transform: translate(-1px, -2px) rotate(-1deg);
  }

  20% {
    transform: translate(-3px, 0px) rotate(1deg);
  }

  30% {
    transform: translate(3px, 2px) rotate(0deg);
  }

  40% {
    transform: translate(1px, -1px) rotate(1deg);
  }

  50% {
    transform: translate(-1px, 2px) rotate(-1deg);
  }

  60% {
    transform: translate(-3px, 1px) rotate(0deg);
  }

  70% {
    transform: translate(3px, 1px) rotate(-1deg);
  }

  80% {
    transform: translate(-1px, -1px) rotate(1deg);
  }

  90% {
    transform: translate(1px, 2px) rotate(0deg);
  }

  100% {
    transform: translate(1px, -2px) rotate(-1deg);
  }

}

html,
body {
  height: 100%;
}

body {
  margin: 0;
  font-family: Roboto, "Helvetica Neue", sans-serif;
}

.lms-learner-notes-bottom-sheet {
  width: 700px;
  overflow-x: hidden !important;
}

.mail-plugin-dialog .mat-dialog-container {
  overflow: hidden !important;
}

.small-100 {
  font-size: 100% !important;
  padding-left: 0 !important;
}

.settings-menu-arrow {
  border-color: transparent;
  border-right-color: #fff;
  border-style: dashed dashed solid;
  border-width: 8.5px 8.5px 8.5px 8.5px;
  position: absolute;
  z-index: 1001;
  height: 0;
  width: 0;
  -webkit-animation: gb__a 25ms;
  animation: gb__a 25ms;
  filter: drop-shadow(-2px 0 1px rgba(0, 0, 0, 0.05));
}

.dxc-tooltip {
  z-index: 1000; /* Ensure it's higher than the dialog's z-index */
}