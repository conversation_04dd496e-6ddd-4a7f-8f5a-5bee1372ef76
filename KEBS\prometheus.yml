global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'prd-EC2-main'
    scrape_interval: 5s
    metrics_path: '/metrics'
    static_configs:
      - targets: ['172.31.82.101:9100'] 
  - job_name: 'project_pm2_prd metrics'
    scrape_interval: 10s
    scrape_timeout: 10s
    metrics_path: /metrics
    scheme: http
    static_configs:
      - targets: ['172.31.82.101:9052']
  - job_name: 'project_default_prd metrics'
    scrape_interval: 5s
    metrics_path: '/metrics'
    static_configs:
      - targets: ['172.31.82.101:3002']
  - job_name: 'acl_pm2_prd metrics'
    scrape_interval: 10s
    scrape_timeout: 10s
    metrics_path: /metrics
    scheme: http
    static_configs:
      - targets: ['172.31.82.101:9051']
  - job_name: 'acl_default_prd metrics'
    scrape_interval: 5s
    metrics_path: '/metrics'
    static_configs:
      - targets: ['172.31.82.101:3001']
  - job_name: 'workflow_pm2_prd metrics'
    scrape_interval: 10s
    scrape_timeout: 10s
    metrics_path: /metrics
    scheme: http
    static_configs:
      - targets: ['172.31.82.101:9053']
  - job_name: 'workflow_default_prd metrics'
    scrape_interval: 5s
    metrics_path: '/metrics'
    static_configs:
      - targets: ['172.31.82.101:3012']
  - job_name: 'timesheet_pm2_prd metrics'
    scrape_interval: 10s
    scrape_timeout: 10s
    metrics_path: /metrics
    scheme: http
    static_configs:
      - targets: ['172.31.82.101:9054']
  - job_name: 'timesheet_default_prd metrics'
    scrape_interval: 5s
    metrics_path: '/metrics'
    static_configs:
      - targets: ['172.31.82.101:3013']
  - job_name: 'expenses_pm2_prd metrics'
    scrape_interval: 10s
    scrape_timeout: 10s
    metrics_path: /metrics
    scheme: http
    static_configs:
      - targets: ['172.31.82.101:9055']
  - job_name: 'expenses_default_prd metrics'
    scrape_interval: 5s
    metrics_path: '/metrics'
    static_configs:
      - targets: ['172.31.82.101:3014']
  - job_name: 'appraisal_pm2_prd metrics'
    scrape_interval: 10s
    scrape_timeout: 10s
    metrics_path: /metrics
    scheme: http
    static_configs:
      - targets: ['172.31.82.101:9056']
  - job_name: 'appraisal_default_prd metrics'
    scrape_interval: 5s
    metrics_path: '/metrics'
    static_configs:
      - targets: ['172.31.82.101:3016']
  - job_name: 'ams_pm2_prd metrics'
    scrape_interval: 10s
    scrape_timeout: 10s
    metrics_path: /metrics
    scheme: http
    static_configs:
      - targets: ['172.31.82.101:9057']
  - job_name: 'ams_default_prd metrics'
    scrape_interval: 5s
    metrics_path: '/metrics'
    static_configs:
      - targets: ['172.31.82.101:3017']
  - job_name: 'awards_pm2_prd metrics'
    scrape_interval: 10s
    scrape_timeout: 10s
    metrics_path: /metrics
    scheme: http
    static_configs:
      - targets: ['172.31.82.101:9058']
  - job_name: 'awards_default_prd metrics'
    scrape_interval: 5s
    metrics_path: '/metrics'
    static_configs:
      - targets: ['172.31.82.101:3018']
  - job_name: 'salesnode_pm2_prd metrics'
    scrape_interval: 10s
    scrape_timeout: 10s
    metrics_path: /metrics
    scheme: http
    static_configs:
      - targets: ['172.31.82.101:9059']
  - job_name: 'salesnode_default_prd metrics'
    scrape_interval: 5s
    metrics_path: '/metrics'
    static_configs:
      - targets: ['172.31.82.101:3011']
  - job_name: 'isa_pm2_prd metrics'
    scrape_interval: 10s
    scrape_timeout: 10s
    metrics_path: /metrics
    scheme: http
    static_configs:
      - targets: ['172.31.82.101:9060']
  - job_name: 'isa_default_prd metrics'
    scrape_interval: 5s
    metrics_path: '/metrics'
    static_configs:
      - targets: ['172.31.82.101:3021']
  - job_name: 'integrationsnode_pm2_prd metrics'
    scrape_interval: 10s
    scrape_timeout: 10s
    metrics_path: /metrics
    scheme: http
    static_configs:
      - targets: ['172.31.82.101:9061']
  - job_name: 'integrationsnode_default_prd metrics'
    scrape_interval: 5s
    metrics_path: '/metrics'
    static_configs:
      - targets: ['172.31.82.101:3022']
  - job_name: 'okr_pm2_prd metrics'
    scrape_interval: 10s
    scrape_timeout: 10s
    metrics_path: /metrics
    scheme: http
    static_configs:
      - targets: ['172.31.82.101:9062']
  - job_name: 'okr_default_prd metrics'
    scrape_interval: 5s
    metrics_path: '/metrics'
    static_configs:
      - targets: ['172.31.82.101:3019']
  - job_name: 'lms_pm2_prd metrics'
    scrape_interval: 10s
    scrape_timeout: 10s
    metrics_path: /metrics
    scheme: http
    static_configs:
      - targets: ['172.31.82.101:9063']
  - job_name: 'lms_default_prd metrics'
    scrape_interval: 5s
    metrics_path: '/metrics'
    static_configs:
      - targets: ['172.31.82.101:3023']
  - job_name: 'appbuilder_pm2_prd metrics'
    scrape_interval: 10s
    scrape_timeout: 10s
    metrics_path: /metrics
    scheme: http
    static_configs:
      - targets: ['172.31.82.101:9064']
  - job_name: 'appbuilder_default_prd metrics'
    scrape_interval: 5s
    metrics_path: '/metrics'
    static_configs:
      - targets: ['172.31.82.101:3020']
  - job_name: 'rabbitmq-service_pm2_prd metrics'
    scrape_interval: 10s
    scrape_timeout: 10s
    metrics_path: /metrics
    scheme: http
    static_configs:
      - targets: ['172.31.82.101:9065']
  - job_name: 'rabbitmq-service_default_prd metrics'
    scrape_interval: 5s
    metrics_path: '/metrics'
    static_configs:
      - targets: ['172.31.82.101:3900']
  - job_name: 'email-service_pm2_prd metrics'
    scrape_interval: 10s
    scrape_timeout: 10s
    metrics_path: /metrics
    scheme: http
    static_configs:
      - targets: ['172.31.82.101:9066']
  - job_name: 'email-service_default_prd metrics'
    scrape_interval: 5s
    metrics_path: '/metrics'
    static_configs:
      - targets: ['172.31.82.101:3500']
  - job_name: 'scheduler-service_pm2_prd metrics'
    scrape_interval: 10s
    scrape_timeout: 10s
    metrics_path: /metrics
    scheme: http
    static_configs:
      - targets: ['172.31.82.101:9067']
  - job_name: 'scheduler-service_default_prd metrics'
    scrape_interval: 5s
    metrics_path: '/metrics'
    static_configs:
      - targets: ['172.31.82.101:3803']
  - job_name: 'auth-service_pm2_prd metrics'
    scrape_interval: 10s
    scrape_timeout: 10s
    metrics_path: /metrics
    scheme: http
    static_configs:
      - targets: ['172.31.82.101:9068']
  - job_name: 'auth-service_default_prd metrics'
    scrape_interval: 5s
    metrics_path: '/metrics'
    static_configs:
      - targets: ['172.31.82.101:3800']
  - job_name: 'tenant-service_pm2_prd metrics'
    scrape_interval: 10s
    scrape_timeout: 10s
    metrics_path: /metrics
    scheme: http
    static_configs:
      - targets: ['172.31.82.101:9069']
  - job_name: 'tenant-service_default_prd metrics'
    scrape_interval: 5s
    metrics_path: '/metrics'
    static_configs:
      - targets: ['172.31.82.101:3801']
  - job_name: 'compliance_pm2_prd metrics'
    scrape_interval: 10s
    scrape_timeout: 10s
    metrics_path: /metrics
    scheme: http
    static_configs:
      - targets: ['172.31.82.101:9070']
  - job_name: 'compliance_default_prd metrics'
    scrape_interval: 5s
    metrics_path: '/metrics'
    static_configs:
      - targets: ['172.31.82.101:3026']
  - job_name: 'cs-service_pm2_prd metrics'
    scrape_interval: 10s
    scrape_timeout: 10s
    metrics_path: /metrics
    scheme: http
    static_configs:
      - targets: ['172.31.82.101:9071']
  - job_name: 'cs-service_default_prd metrics'
    scrape_interval: 5s
    metrics_path: '/metrics'
    static_configs:
      - targets: ['172.31.82.101:3050']
  - job_name: 'p2p_pm2_prd metrics'
    scrape_interval: 10s
    scrape_timeout: 10s
    metrics_path: /metrics
    scheme: http
    static_configs:
      - targets: ['172.31.82.101:9072']
  - job_name: 'p2p_default_prd metrics'
    scrape_interval: 5s
    metrics_path: '/metrics'
    static_configs:
      - targets: ['172.31.82.101:3750']
  - job_name: 'employee360_pm2_prd metrics'
    scrape_interval: 10s
    scrape_timeout: 10s
    metrics_path: /metrics
    scheme: http
    static_configs:
      - targets: ['172.31.82.101:9073']
  - job_name: 'employee360_default_prd metrics'
    scrape_interval: 5s
    metrics_path: '/metrics'
    static_configs:
      - targets: ['172.31.82.101:3028']
  - job_name: 'utility_pm2_prd metrics'
    scrape_interval: 10s
    scrape_timeout: 10s
    metrics_path: /metrics
    scheme: http
    static_configs:
      - targets: ['172.31.82.101:9074']
  - job_name: 'utility_default_prd metrics'
    scrape_interval: 5s
    metrics_path: '/metrics'
    static_configs:
      - targets: ['172.31.82.101:3010']
  - job_name: 'leaveapp_pm2_prd metrics'
    scrape_interval: 10s
    scrape_timeout: 10s
    metrics_path: /metrics
    scheme: http
    static_configs:
      - targets: ['172.31.82.101:9075']
  - job_name: 'leaveapp_default_prd metrics'
    scrape_interval: 5s
    metrics_path: '/metrics'
    static_configs:
      - targets: ['172.31.82.101:3751']
  - job_name: 'misfunctions_pm2_prd metrics'
    scrape_interval: 10s
    scrape_timeout: 10s
    metrics_path: /metrics
    scheme: http
    static_configs:
      - targets: ['172.31.82.101:9076']
  - job_name: 'misfunctions_default_prd metrics'
    scrape_interval: 5s
    metrics_path: '/metrics'
    static_configs:
      - targets: ['172.31.82.101:3039']
  - job_name: 'gdadmin_pm2_prd metrics'
    scrape_interval: 10s
    scrape_timeout: 10s
    metrics_path: /metrics
    scheme: http
    static_configs:
      - targets: ['172.31.82.101:9078']
  - job_name: 'gdadmin_default_prd metrics'
    scrape_interval: 5s
    metrics_path: '/metrics'
    static_configs:
      - targets: ['172.31.82.101:3902']
  - job_name: 'resource-management_pm2_prd metrics'
    scrape_interval: 10s
    scrape_timeout: 10s
    metrics_path: /metrics
    scheme: http
    static_configs:
      - targets: ['172.31.82.101:9077']
  - job_name: 'resource-management_default_prd metrics'
    scrape_interval: 5s
    metrics_path: '/metrics'
    static_configs:
      - targets: ['172.31.82.101:3030']
  - job_name: 'exit_pm2_prd metrics'
    scrape_interval: 10s
    scrape_timeout: 10s
    metrics_path: /metrics
    scheme: http
    static_configs:
      - targets: ['172.31.82.101:9080']
  - job_name: 'exit_default_prd metrics'
    scrape_interval: 5s
    metrics_path: '/metrics'
    static_configs:
      - targets: ['172.31.82.101:3031']
  - job_name: 'mismgmtapis_pm2_prd metrics'
    scrape_interval: 10s
    scrape_timeout: 10s
    metrics_path: /metrics
    scheme: http
    static_configs:
      - targets: ['172.31.82.101:9079']
  - job_name: 'mismgmtapis_default_prd metrics'
    scrape_interval: 5s
    metrics_path: '/metrics'
    static_configs:
      - targets: ['172.31.82.101:3034']
  - job_name: 'analytics_pm2_prd metrics'
    scrape_interval: 10s
    scrape_timeout: 10s
    metrics_path: /metrics
    scheme: http
    static_configs:
      - targets: ['172.31.82.101:9081']
  - job_name: 'analytics_default_prd metrics'
    scrape_interval: 5s
    metrics_path: '/metrics'
    static_configs:
      - targets: ['172.31.82.101:3802']

  - job_name: 'prd-EC2-bgj'
    scrape_interval: 5s
    metrics_path: '/metrics'
    static_configs:
      - targets: ['172.31.13.229:9100'] 
  - job_name: 'BgjNode_pm2_prd metrics'
    scrape_interval: 10s
    scrape_timeout: 10s
    metrics_path: /metrics
    scheme: http
    static_configs:
      - targets: ['172.31.13.229:9086']
  - job_name: 'BgjNode_default_prd metrics'
    scrape_interval: 5s
    metrics_path: '/metrics'
    static_configs:
      - targets: ['172.31.13.229:3025']
  - job_name: 'lcdpbgjprd_pm2_prd metrics'
    scrape_interval: 10s
    scrape_timeout: 10s
    metrics_path: /metrics
    scheme: http
    static_configs:
      - targets: ['172.31.13.229:9087']
  - job_name: 'lcdpbgjprd_default_prd metrics'
    scrape_interval: 5s
    metrics_path: '/metrics'
    static_configs:
      - targets: ['172.31.13.229:3030']
  - job_name: 'rrbgjprd_pm2_prd metrics'
    scrape_interval: 10s
    scrape_timeout: 10s
    metrics_path: /metrics
    scheme: http
    static_configs:
      - targets: ['172.31.13.229:9088']
  - job_name: 'rrbgjprd_default_prd metrics'
    scrape_interval: 5s
    metrics_path: '/metrics'
    static_configs:
      - targets: ['172.31.13.229:3040']
  - job_name: 'hubspotbgjprd_pm2_prd metrics'
    scrape_interval: 10s
    scrape_timeout: 10s
    metrics_path: /metrics
    scheme: http
    static_configs:
      - targets: ['172.31.13.229:9089']
  - job_name: 'hubspotbgjprd_default_prd metrics'
    scrape_interval: 5s
    metrics_path: '/metrics'
    static_configs:
      - targets: ['172.31.13.229:3042']
