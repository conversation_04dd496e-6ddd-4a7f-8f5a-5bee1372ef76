const logger = require("../logger").logger;
const cron = require("node-cron");
const axios = require("axios");
const DB_NAME = "tenants_management";
const mongo = require("../mongo_conn_native").Connection;
const { ObjectId } = require("mongodb");
const token_service = require("../token");
const active_tenant_service = require("../active_tenant");
const pool = require("../databaseCon").pool;
const mysql = require("mysql");
const _ = require("underscore");
var cronJob = require('cron').CronJob;
var randomstring = require("randomstring");
const moment = require("moment");

 


async function getCommonBGJobActiveTenantList(job_group_code, active_tenants) {
  try {

    let checkForBgJobActiveStatusQuery = "SELECT params_info,tenant_info \
    FROM kebs_master.m_common_bg_jobs \
    WHERE is_active = 1 AND job_code = ? ;"

    checkForBgJobActiveStatusQuery = mysql.format(checkForBgJobActiveStatusQuery, [job_group_code])

    let checkForBgJobActiveStatus = await pool(checkForBgJobActiveStatusQuery);

    if (!checkForBgJobActiveStatus)
      return { messType: "E", messText: `BG JOB - ${job_group_code} not found or inactive !` };

    if (checkForBgJobActiveStatus.length == 0)
      return { messType: "E", messText: `BG JOB - ${job_group_code} not found or inactive !` };

    let tenantInfo = checkForBgJobActiveStatus && checkForBgJobActiveStatus[0].tenant_info ? typeof checkForBgJobActiveStatus[0].tenant_info == "string" ? JSON.parse(checkForBgJobActiveStatus[0].tenant_info) : checkForBgJobActiveStatus[0].tenant_info : null;

    let paramsInfo = checkForBgJobActiveStatus && checkForBgJobActiveStatus[0].params_info ? typeof checkForBgJobActiveStatus[0].params_info == "string" ? JSON.parse(checkForBgJobActiveStatus[0].params_info) : checkForBgJobActiveStatus[0].params_info : null;

    if (!tenantInfo)
      return { messType: "E", messText: `BG JOB - ${job_group_code} , Tenant Info not found !` };

    let filterActiveTenants = _.pluck(_.where(tenantInfo, { is_active: 'true' }), "db_name");

    let bgJobActiveTenants = _.filter(active_tenants, l => {
      if (_.contains(filterActiveTenants, l.tad))
        return l;
    });
    bgJobActiveTenants = _.uniq(bgJobActiveTenants, "tad");

    return { messType: "S", active_tenant_list: bgJobActiveTenants, params_info: paramsInfo }


  } catch (err) {
    return {
      messType: "E",
      messText: "Error in retreiving Common BG Job Active Tenant List ! ",
      errMessage: {
        errMsg: err.msg,
        stack: err.stack
      }
    }
  }
}

/**  Freeze Planned Billing,Collection,OBV
* @backgroundJob
* @runtime 10 19 * * *
* @returns Plan Freeze
* <AUTHOR>
* @version 1.0
*/
cron.schedule("0 30 17 28-31 * *", async () => {
  try {
    let active_tenants = await active_tenant_service.getActiveTenants();
    active_tenants.forEach(async (tenant, index) => {
      let bodyParams = {
        job_name: "collectionBillingObvFreeze",
        job_type: "cron",
        job_subtype: "recurring",
        job_group_id: "opportunity",
        job_description: "Freeze Planned Billing,Collection,OBV",
        tenant_id: tenant.tenant_id,
        message_routing_key: `project_collection_billing_obv_freeze_job_queue_key_${process.env.NODE_ENV}`,
        tenant_info: tenant,
      };

      let config = {
        url: `http://${process.env.RABBIT_MQ_SERVICE || "localhost:3900"
          }/queue/addJob`,
        method: "POST",
        data: bodyParams,
        headers: {
          Authorization:
            "Bearer " +
            (await token_service.getTokenForApplication(
              tenant.tad,
              tenant.tenant_id
            )),
        },
      };

      let response_data = await axios(config).catch((err) => {
        console.log(err);
        return null;
      });
    });
  } catch (err) {
    console.log(err);
  }
});

/**  Compare MIS Data  (Tested)
* @backgroundJob  PROJBG1
* @runtime Every 1 Hour
* @returns Send Notification if difference in MIS Data table and its copy table
* <AUTHOR> Kumar S
* @version 1.0
*/
cron.schedule("0 * * * *", async () => {

  try {

    // logger.info("MIS Data Compare Notification !")

    let active_tenants = await active_tenant_service.getActiveTenants();

    let job_group_code = "PROJBG1";

    let get_bg_job_active_tenants = await getCommonBGJobActiveTenantList(job_group_code, active_tenants);


    if (get_bg_job_active_tenants.messType == "E")
      return get_bg_job_active_tenants;

    let bg_job_active_tenants = get_bg_job_active_tenants.active_tenant_list;
    let params_info = get_bg_job_active_tenants.params_info;

    bg_job_active_tenants.forEach(async (tenant, index) => {

      let bodyParams = {
        job_name: "misDataCompareNotif",
        job_type: "cron",
        job_subtype: "recurring",
        job_group_id: job_group_code,
        job_description: "To check MIS Data between original and copy . If any difference send notification.",
        tenant_id: tenant.tenant_id,
        message_routing_key: `project_mis_data_compare_job_queue_key_${process.env.NODE_ENV}`,
        tenant_info: tenant,
        params_info: params_info
      };

      let config = {
        url: `http://${process.env.RABBIT_MQ_SERVICE || "localhost:3900"
          }/queue/addJob`,
        method: "POST",
        data: bodyParams,
        headers: {
          Authorization:
            "Bearer " +
            (await token_service.getTokenForApplication(
              tenant.tad,
              tenant.tenant_id
            )),
        },
      };

      let response_data = await axios(config).catch((err) => {
        console.log(err);
        return null;
      });
    });
  } catch (err) {
    console.log(err);
  }
});

/** Update P&L for all the items  (Tested)
* @backgroundJob  PROJBG2
* @runtime Every Day at 12 AM UTC
* @returns
* <AUTHOR> Kumar S
* @version 1.0
*/
cron.schedule("0 0 * * *", async () => {
  try {

    // logger.info("Update P&L for Items BG JOB !")

    let active_tenants = await active_tenant_service.getActiveTenants();

    let job_group_code = "PROJBG2";

    let get_bg_job_active_tenants = await getCommonBGJobActiveTenantList(job_group_code, active_tenants);

    if (get_bg_job_active_tenants.messType == "E")
      return get_bg_job_active_tenants;

    let bg_job_active_tenants = get_bg_job_active_tenants.active_tenant_list;
    let params_info = get_bg_job_active_tenants.params_info;

    bg_job_active_tenants.forEach(async (tenant, index) => {

      let bodyParams = {
        job_name: "updatePlForAllItems",
        job_type: "cron",
        job_subtype: "recurring",
        job_group_id: job_group_code,
        job_description: "To Update P&L for all items",
        tenant_id: tenant.tenant_id,
        message_routing_key: `project_update_pl_all_items_job_queue_key_${process.env.NODE_ENV}`,
        tenant_info: tenant,
        params_info: params_info
      };

      let config = {
        url: `http://${process.env.RABBIT_MQ_SERVICE || "localhost:3900"
          }/queue/addJob`,
        method: "POST",
        data: bodyParams,
        headers: {
          Authorization:
            "Bearer " +
            (await token_service.getTokenForApplication(
              tenant.tad,
              tenant.tenant_id
            )),
        },
      };

      let response_data = await axios(config).catch((err) => {
        console.log(err);
        return null;
      });
    });
  } catch (err) {
    console.log(err);
  }
});



/** Dynamic AR UBR WC Alert Notification
 * @backgroundJob  PROJBG4
 * @runtime Every 5 Minutes
 * @returns Creates multiple Cron Jobs for AR UBR WC Alerts
 * <AUTHOR> Kumar S
 * @version 1.0
 */
cron.schedule("*/5 * * * *", async () => {
  try {
    // logger.info("Dynamic AR UBR WC Alert BG JOB !")

    let active_tenants = await active_tenant_service.getActiveTenants();

    let job_group_code = "PROJBG4";

    let get_bg_job_active_tenants = await getCommonBGJobActiveTenantList(job_group_code, active_tenants);

    if (get_bg_job_active_tenants.messType == "E")
      return get_bg_job_active_tenants;

    let bg_job_active_tenants = get_bg_job_active_tenants.active_tenant_list;
    let params_info = get_bg_job_active_tenants.params_info;

    bg_job_active_tenants.forEach(async (tenant, index) => {

      let dbName = tenant.tad;

      let getTenantDynamicBgJobs = await pool(`SELECT id,taa.oid,taa.teams_group_id,taa.notification_type_id,
      taa.type_based_id, 
      taa.days_expression,taa.days_limit,taa.amount_expression,taa.amount_limit, 
      IFNULL(taa.minute,'*') AS minute,IFNULL(taa.hour,'*') AS hour, 
      IFNULL(taa.day_of_the_month,'*') AS day_of_the_month,IFNULL(taa.month,'*') as month,ifNULL (taa.day_of_the_week,'*') as day_of_the_week, 
      taa.is_cron_active,taa.is_active
       from ${dbName}.t_ar_alerts as taa WHERE taa.is_cron_active = 0 and taa.is_active = 1;`);

      if (!getTenantDynamicBgJobs)
        return "No Dynamic AR BG Jobs Found !"

      if (getTenantDynamicBgJobs.length == 0)
        return "No Dynamic AR BG Jobs Found !"

      let bgJobs = getTenantDynamicBgJobs;
      _.each(bgJobs, l => {
        l.cron_string = `${l.minute} ${l.hour} ${l.day_of_the_month} ${l.month} ${l.day_of_the_week}`;
      });

      if (bgJobs.length > 0) {
        for (let obj of bgJobs) {

          let job_id = obj.id;

          let updateCronIsActive = await pool(`UPDATE ${dbName}.t_ar_alerts SET is_cron_active = 1 WHERE id = ?;`, [job_id]);

          let job = new cronJob({
            cronTime: obj.cron_string,
            onTick: async function () {

              let getBgJobData = await pool(`SELECT * FROM ${dbName}.t_ar_alerts WHERE is_active = 1 AND id = ? ;`, [job_id]);

              if (getBgJobData.length == 0) {

                let comment = `alert stopped !`
                let updateCronIsActive = await pool(`UPDATE ${dbName}.t_ar_alerts SET is_cron_active = null ,comment = ? WHERE id = ?;`, [comment, job_id]);

                job.stop();

              }

              else {

                let bgJobInfo = getBgJobData[0];
                bgJobInfo.dbName = dbName;

                let bodyParams = {
                  job_name: "dynamicArUbrWcAlertCronJobs",
                  job_type: "cron",
                  job_subtype: "recurring",
                  job_group_id: "PROJBG4",
                  job_description: "Dynamic AR UBR WC Alert Background Jobs",
                  tenant_id: tenant.tenant_id,
                  message_routing_key: `project_dynamic_ar_ubr_wc_alert_cron_job_queue_key_${process.env.NODE_ENV}`,
                  tenant_info: tenant,
                  bgJobInfo: [bgJobInfo]
                };

                let config = {
                  url: `http://${process.env.RABBIT_MQ_SERVICE || "localhost:3900"
                    }/queue/addJob`,
                  method: "POST",
                  data: bodyParams,
                  headers: {
                    Authorization:
                      "Bearer " +
                      (await token_service.getTokenForApplication(
                        tenant.tad,
                        tenant.tenant_id
                      )),
                  },
                };

                let response_data = await axios(config).catch((err) => {
                  console.log(err);
                  return null;
                });

              }
            },
            start: false,
            timeZone: 'Asia/Kolkata'
          });
          job.start();

        }

        return "Dynamic AR BG Jobs created successfully !"


      } else {
        return "No active Dynamic AR BG Jobs found for tenant !"
      }


    });
  } catch (err) {
    console.log(err);
  }
});




/**
* Revenue Recognition BG JOB
* @backgroundJob  PROJBG3
* @runtime Every Minute
* @returns
* <AUTHOR> Kumar S
* @version 1.0
*/
cron.schedule("* * * * *", async () => {
  try {
    let active_tenants = await active_tenant_service.getActiveTenants();

    let job_group_code = "PROJBG3";

    let get_bg_job_active_tenants = await getCommonBGJobActiveTenantList(job_group_code, active_tenants);

    if (get_bg_job_active_tenants.messType == "E")
      return get_bg_job_active_tenants;

    let bg_job_active_tenants = get_bg_job_active_tenants.active_tenant_list;
    let params_info = get_bg_job_active_tenants.params_info;

    bg_job_active_tenants.forEach(async (tenant, index) => {

      let bodyParams = {
        job_name: "revenueRecognitionBgJob",
        job_type: "cron",
        job_subtype: "recurring",
        job_group_id: job_group_code,
        job_description: "Revenue Recognition BG JOB",
        tenant_id: tenant.tenant_id,
        message_routing_key: `project_revenue_recognition_job_queue_key_${process.env.NODE_ENV}`,
        tenant_info: tenant,
        params_info: params_info
      };

      let config = {
        url: `http://${process.env.RABBIT_MQ_SERVICE || "localhost:3900"
          }/queue/addJob`,
        method: "POST",
        data: bodyParams,
        headers: {
          Authorization:
            "Bearer " +
            (await token_service.getTokenForApplication(
              tenant.tad,
              tenant.tenant_id
            )),
        },
      };

      let response_data = await axios(config).catch((err) => {
        console.log(err);
        return null;
      });
    });
  } catch (err) {
    console.log(err);
  }
});

/**
 * Revenue Recognition Check Notification BG JOB
*  @backgroundJob  PROJBG5
* @runtime Every Minute
* @returns
* <AUTHOR> Kumar S
* @version 1.0
*/
cron.schedule("* * * * *", async () => {
  try {
    let active_tenants = await active_tenant_service.getActiveTenants();

    let job_group_code = "PROJBG5";

    let get_bg_job_active_tenants = await getCommonBGJobActiveTenantList(job_group_code, active_tenants);

    if (get_bg_job_active_tenants.messType == "E")
      return get_bg_job_active_tenants;

    let bg_job_active_tenants = get_bg_job_active_tenants.active_tenant_list;
    let params_info = get_bg_job_active_tenants.params_info;

    bg_job_active_tenants.forEach(async (tenant, index) => {

      let bodyParams = {
        job_name: "revenueRecogCheckBgJob",
        job_type: "cron",
        job_subtype: "recurring",
        job_group_id: job_group_code,
        job_description: "Revenue Recognition Check Notification BG JOB",
        tenant_id: tenant.tenant_id,
        message_routing_key: `project_revenue_recognition_check_job_queue_key_${process.env.NODE_ENV}`,
        tenant_info: tenant,
        params_info: params_info
      };

      let config = {
        url: `http://${process.env.RABBIT_MQ_SERVICE || "localhost:3900"
          }/queue/addJob`,
        method: "POST",
        data: bodyParams,
        headers: {
          Authorization:
            "Bearer " +
            (await token_service.getTokenForApplication(
              tenant.tad,
              tenant.tenant_id
            )),
        },
      };

      let response_data = await axios(config).catch((err) => {
        console.log(err);
        return null;
      });
    });
  } catch (err) {
    console.log(err);
  }
});



/**
 * Planned Billing Update to Mongo BG JOB
*  @backgroundJob  PROJBG6
* @runtime Every Minute
* @returns
* <AUTHOR> Kumar S
* @version 1.0
*/
cron.schedule("0 * * * *", async () => {
  try {
    let active_tenants = await active_tenant_service.getActiveTenants();

    let job_group_code = "PROJBG6";

    let get_bg_job_active_tenants = await getCommonBGJobActiveTenantList(job_group_code, active_tenants);

    if (get_bg_job_active_tenants.messType == "E")
      return get_bg_job_active_tenants;

    let bg_job_active_tenants = get_bg_job_active_tenants.active_tenant_list;
    let params_info = get_bg_job_active_tenants.params_info;

    bg_job_active_tenants.forEach(async (tenant, index) => {

      let bodyParams = {
        job_name: "plannedBillingUpdateBgJob",
        job_type: "cron",
        job_subtype: "recurring",
        job_group_id: job_group_code,
        job_description: "Planned Billing Update Mongo",
        tenant_id: tenant.tenant_id,
        message_routing_key: `project_planned_billing_job_queue_key_${process.env.NODE_ENV}`,
        tenant_info: tenant,
        params_info: params_info
      };

      let config = {
        url: `http://${process.env.RABBIT_MQ_SERVICE || "localhost:3900"
          }/queue/addJob`,
        method: "POST",
        data: bodyParams,
        headers: {
          Authorization:
            "Bearer " +
            (await token_service.getTokenForApplication(
              tenant.tad,
              tenant.tenant_id
            )),
        },
      };

      let response_data = await axios(config).catch((err) => {
        console.log(err);
        return null;
      });
    });
  } catch (err) {
    console.log(err);
  }
});



/**
 * Actual Billing Update Mongo
*  @backgroundJob  PROJBG7
* @runtime Every Minute
* @returns
* <AUTHOR> Kumar S
* @version 1.0
*/
cron.schedule("0 * * * *", async () => {
  try {
    let active_tenants = await active_tenant_service.getActiveTenants();

    let job_group_code = "PROJBG7";

    let get_bg_job_active_tenants = await getCommonBGJobActiveTenantList(job_group_code, active_tenants);

    if (get_bg_job_active_tenants.messType == "E")
      return get_bg_job_active_tenants;

    let bg_job_active_tenants = get_bg_job_active_tenants.active_tenant_list;
    let params_info = get_bg_job_active_tenants.params_info;

    bg_job_active_tenants.forEach(async (tenant, index) => {

      let bodyParams = {
        job_name: "actualBillingUpdateBgJob",
        job_type: "cron",
        job_subtype: "recurring",
        job_group_id: job_group_code,
        job_description: "Actual Billing Update Mongo",
        tenant_id: tenant.tenant_id,
        message_routing_key: `project_actual_billing_job_queue_key_${process.env.NODE_ENV}`,
        tenant_info: tenant,
        params_info: params_info
      };

      let config = {
        url: `http://${process.env.RABBIT_MQ_SERVICE || "localhost:3900"
          }/queue/addJob`,
        method: "POST",
        data: bodyParams,
        headers: {
          Authorization:
            "Bearer " +
            (await token_service.getTokenForApplication(
              tenant.tad,
              tenant.tenant_id
            )),
        },
      };

      let response_data = await axios(config).catch((err) => {
        console.log(err);
        return null;
      });
    });
  } catch (err) {
    console.log(err);
  }
});



/**
 * Planned Collection Update Mongo
*  @backgroundJob  PROJBG8
* @runtime Every Minute
* @returns
* <AUTHOR> Kumar S
* @version 1.0
*/
cron.schedule("0 * * * *", async () => {
  try {
    let active_tenants = await active_tenant_service.getActiveTenants();

    let job_group_code = "PROJBG8";

    let get_bg_job_active_tenants = await getCommonBGJobActiveTenantList(job_group_code, active_tenants);

    if (get_bg_job_active_tenants.messType == "E")
      return get_bg_job_active_tenants;

    let bg_job_active_tenants = get_bg_job_active_tenants.active_tenant_list;
    let params_info = get_bg_job_active_tenants.params_info;

    bg_job_active_tenants.forEach(async (tenant, index) => {

      let bodyParams = {
        job_name: "plannedCollectionUpdateBgJob",
        job_type: "cron",
        job_subtype: "recurring",
        job_group_id: job_group_code,
        job_description: "Planned Collection Update Mongo",
        tenant_id: tenant.tenant_id,
        message_routing_key: `project_planned_collection_job_queue_key_${process.env.NODE_ENV}`,
        tenant_info: tenant,
        params_info: params_info
      };

      let config = {
        url: `http://${process.env.RABBIT_MQ_SERVICE || "localhost:3900"
          }/queue/addJob`,
        method: "POST",
        data: bodyParams,
        headers: {
          Authorization:
            "Bearer " +
            (await token_service.getTokenForApplication(
              tenant.tad,
              tenant.tenant_id
            )),
        },
      };

      let response_data = await axios(config).catch((err) => {
        console.log(err);
        return null;
      });
    });
  } catch (err) {
    console.log(err);
  }
});


/**
 * Actual Collection Update Mongo
*  @backgroundJob  PROJBG9
* @runtime Every Minute
* @returns
* <AUTHOR> Kumar S
* @version 1.0
*/
cron.schedule("0 * * * *", async () => {
  try {
    let active_tenants = await active_tenant_service.getActiveTenants();

    let job_group_code = "PROJBG9";

    let get_bg_job_active_tenants = await getCommonBGJobActiveTenantList(job_group_code, active_tenants);

    if (get_bg_job_active_tenants.messType == "E")
      return get_bg_job_active_tenants;

    let bg_job_active_tenants = get_bg_job_active_tenants.active_tenant_list;
    let params_info = get_bg_job_active_tenants.params_info;

    bg_job_active_tenants.forEach(async (tenant, index) => {

      let bodyParams = {
        job_name: "actualCollectionUpdateBgJob",
        job_type: "cron",
        job_subtype: "recurring",
        job_group_id: job_group_code,
        job_description: "Actual Collection Update Mongo",
        tenant_id: tenant.tenant_id,
        message_routing_key: `project_actual_collection_job_queue_key_${process.env.NODE_ENV}`,
        tenant_info: tenant,
        params_info: params_info
      };

      let config = {
        url: `http://${process.env.RABBIT_MQ_SERVICE || "localhost:3900"
          }/queue/addJob`,
        method: "POST",
        data: bodyParams,
        headers: {
          Authorization:
            "Bearer " +
            (await token_service.getTokenForApplication(
              tenant.tad,
              tenant.tenant_id
            )),
        },
      };

      let response_data = await axios(config).catch((err) => {
        console.log(err);
        return null;
      });
    });
  } catch (err) {
    console.log(err);
  }
});



/**
 * Project Item AR Value Update Mongo
*  @backgroundJob  PROJBG10
* @runtime Every Minute
* @returns
* <AUTHOR> Kumar S
* @version 1.0
*/
cron.schedule("* * * * *", async () => {
  try {

    let active_tenants = await active_tenant_service.getActiveTenants();

    let job_group_code = "PROJBG10";

    let get_bg_job_active_tenants = await getCommonBGJobActiveTenantList(job_group_code, active_tenants);

    if (get_bg_job_active_tenants.messType == "E")
      return get_bg_job_active_tenants;

    let bg_job_active_tenants = get_bg_job_active_tenants.active_tenant_list;
    let params_info = get_bg_job_active_tenants.params_info;

    bg_job_active_tenants.forEach(async (tenant, index) => {

      let auth = "Bearer " + (await token_service.getTokenForApplication(tenant.tad, tenant.tenant_id))

      let bodyParams = {
        job_name: "itemArValueUpdateMongoBgJob",
        job_type: "cron",
        job_subtype: "recurring",
        job_group_id: job_group_code,
        job_description: "Project Item AR Value Update Mongo",
        tenant_id: tenant.tenant_id,
        message_routing_key: `project_item_ar_job_queue_key_${process.env.NODE_ENV}`,
        tenant_info: tenant,
        params_info: params_info,
        auth : auth
      };

      let config = {
        url: `http://${process.env.RABBIT_MQ_SERVICE || "localhost:3900"
          }/queue/addJob`,
        method: "POST",
        data: bodyParams,
        headers: { Authorization: auth },
      };

      let response_data = await axios(config).catch((err) => {
        console.log(err);
        return null;
      });
    });
  } catch (err) {
    console.log(err);
  }
});



/**
* Move Planned Billing Month End Job BG JOB
* @backgroundJob  PROJBG12
* @runtime Every Minute
* @returns
* <AUTHOR> Kumar S
* @version 1.0
*/
cron.schedule("0 3 1 * *", async () => {
  try {
    let active_tenants = await active_tenant_service.getActiveTenants();

    let job_group_code = "PROJBG12";

    let get_bg_job_active_tenants = await getCommonBGJobActiveTenantList(job_group_code, active_tenants);

    if (get_bg_job_active_tenants.messType == "E")
      return get_bg_job_active_tenants;

    let bg_job_active_tenants = get_bg_job_active_tenants.active_tenant_list;
    let params_info = get_bg_job_active_tenants.params_info;

    bg_job_active_tenants.forEach(async (tenant, index) => {

      let bodyParams = {
        job_name: "movePlannedBillingMonthEndJobBgJob",
        job_type: "cron",
        job_subtype: "recurring",
        job_group_id: job_group_code,
        job_description: "Move Planned Billing Month End Job BG JOB",
        tenant_id: tenant.tenant_id,
        message_routing_key: `project_move_planned_billing_month_end_job_queue_key_${process.env.NODE_ENV}`,
        tenant_info: tenant,
        params_info: params_info
      };

      let config = {
        url: `http://${process.env.RABBIT_MQ_SERVICE || "localhost:3900"
          }/queue/addJob`,
        method: "POST",
        data: bodyParams,
        headers: {
          Authorization:
            "Bearer " +
            (await token_service.getTokenForApplication(
              tenant.tad,
              tenant.tenant_id
            )),
        },
      };

      let response_data = await axios(config).catch((err) => {
        console.log(err);
        return null;
      });
    });
  } catch (err) {
    console.log(err);
  }
});



/**
* Move Planned Collection Month End BG JOB
*  @backgroundJob  PROJBG13
* @runtime Every Minute
* @returns
* <AUTHOR> Kumar S
* @version 1.0
*/
cron.schedule("0 3 1 * *", async () => {
  try {
    let active_tenants = await active_tenant_service.getActiveTenants();

    let job_group_code = "PROJBG13";

    let get_bg_job_active_tenants = await getCommonBGJobActiveTenantList(job_group_code, active_tenants);

    if (get_bg_job_active_tenants.messType == "E")
      return get_bg_job_active_tenants;

    let bg_job_active_tenants = get_bg_job_active_tenants.active_tenant_list;
    let params_info = get_bg_job_active_tenants.params_info;

    bg_job_active_tenants.forEach(async (tenant, index) => {

      let bodyParams = {
        job_name: "movePlannedCollectionMonthEndJobBgJob",
        job_type: "cron",
        job_subtype: "recurring",
        job_group_id: job_group_code,
        job_description: "Move Planned Collection Month End BG JOB",
        tenant_id: tenant.tenant_id,
        message_routing_key: `project_move_planned_collection_month_end_job_queue_key_${process.env.NODE_ENV}`,
        tenant_info: tenant,
        params_info: params_info
      };

      let config = {
        url: `http://${process.env.RABBIT_MQ_SERVICE || "localhost:3900"
          }/queue/addJob`,
        method: "POST",
        data: bodyParams,
        headers: {
          Authorization:
            "Bearer " +
            (await token_service.getTokenForApplication(
              tenant.tad,
              tenant.tenant_id
            )),
        },
      };

      let response_data = await axios(config).catch((err) => {
        console.log(err);
        return null;
      });
    });
  } catch (err) {
    console.log(err);
  }
});


/**
* Planned OBV Update BG JOB
* @backgroundJob  PROJBG14
* @runtime Every Minute
* @returns
* <AUTHOR> Kumar S
* @version 1.0
*/
cron.schedule("* * * * *", async () => {
  try {
    let active_tenants = await active_tenant_service.getActiveTenants();

    let job_group_code = "PROJBG14";

    let get_bg_job_active_tenants = await getCommonBGJobActiveTenantList(job_group_code, active_tenants);

    if (get_bg_job_active_tenants.messType == "E")
      return get_bg_job_active_tenants;

    let bg_job_active_tenants = get_bg_job_active_tenants.active_tenant_list;
    let params_info = get_bg_job_active_tenants.params_info;

    bg_job_active_tenants.forEach(async (tenant, index) => {

      let bodyParams = {
        job_name: "updatePlannedOBVBgJob",
        job_type: "cron",
        job_subtype: "recurring",
        job_group_id: job_group_code,
        job_description: "Planned OBV Update BG JOB",
        tenant_id: tenant.tenant_id,
        message_routing_key: `project_planned_obv_job_queue_key_${process.env.NODE_ENV}`,
        tenant_info: tenant,
        params_info: params_info
      };

      let config = {
        url: `http://${process.env.RABBIT_MQ_SERVICE || "localhost:3900"
          }/queue/addJob`,
        method: "POST",
        data: bodyParams,
        headers: {
          Authorization:
            "Bearer " +
            (await token_service.getTokenForApplication(
              tenant.tad,
              tenant.tenant_id
            )),
        },
      };

      let response_data = await axios(config).catch((err) => {
        console.log(err);
        return null;
      });
    });
  } catch (err) {
    console.log(err);
  }
});


/**
* Actual OBV from C4C Update BG JOB
* @backgroundJob  PROJBG15
* @runtime Every Minute
* @returns
* <AUTHOR> Kumar S
* @version 1.0
*/
cron.schedule("* * * * *", async () => {
  try {
    let active_tenants = await active_tenant_service.getActiveTenants();

    let job_group_code = "PROJBG15";

    let get_bg_job_active_tenants = await getCommonBGJobActiveTenantList(job_group_code, active_tenants);

    if (get_bg_job_active_tenants.messType == "E")
      return get_bg_job_active_tenants;

    let bg_job_active_tenants = get_bg_job_active_tenants.active_tenant_list;
    let params_info = get_bg_job_active_tenants.params_info;

    bg_job_active_tenants.forEach(async (tenant, index) => {

      let bodyParams = {
        job_name: "updateActualOBVFromC4cBgJob",
        job_type: "cron",
        job_subtype: "recurring",
        job_group_id: job_group_code,
        job_description: "Actual OBV from C4C Update BG JOB",
        tenant_id: tenant.tenant_id,
        message_routing_key: `project_actual_obv_job_queue_key_${process.env.NODE_ENV}`,
        tenant_info: tenant,
        params_info: params_info
      };

      let config = {
        url: `http://${process.env.RABBIT_MQ_SERVICE || "localhost:3900"
          }/queue/addJob`,
        method: "POST",
        data: bodyParams,
        headers: {
          Authorization:
            "Bearer " +
            (await token_service.getTokenForApplication(
              tenant.tad,
              tenant.tenant_id
            )),
        },
      };

      let response_data = await axios(config).catch((err) => {
        console.log(err);
        return null;
      });
    });
  } catch (err) {
    console.log(err);
  }
});

/** Update Monthly RR for changed projects
* @backgroundJob  PROJBG16
* @runtime Every Day at 12 AM UTC
* @returns
* <AUTHOR> R
* @version 1.0
*/
cron.schedule("0 0 * * *", async () => {
  try {

    let active_tenants = await active_tenant_service.getActiveTenants();

    let job_group_code = "PROJBG16";

    let get_bg_job_active_tenants = await getCommonBGJobActiveTenantList(job_group_code, active_tenants);

    if (get_bg_job_active_tenants.messType == "E")
      return get_bg_job_active_tenants;

    let bg_job_active_tenants = get_bg_job_active_tenants.active_tenant_list;
    let params_info = get_bg_job_active_tenants.params_info;

    bg_job_active_tenants.forEach(async (tenant, index) => {

      let bodyParams = {
        job_name: "updateMonthlyRRForChangedProjects",
        job_type: "cron",
        job_subtype: "recurring",
        job_group_id: job_group_code,
        job_description: "To Update Monthly RR for changed projects",
        tenant_id: tenant.tenant_id,
        message_routing_key: `project_background_job_queue_key_${process.env.NODE_ENV}`,
        tenant_info: tenant,
        params_info: params_info

      };

      let config = {
        url: `http://${process.env.RABBIT_MQ_SERVICE || "localhost:3900"
          }/queue/addJob`,
        method: "POST",
        data: bodyParams,
        headers: {
          Authorization:
            "Bearer " +
            (await token_service.getTokenForApplication(
              tenant.tad,
              tenant.tenant_id
            )),
        },
      };

      let response_data = await axios(config).catch((err) => {
        console.log(err);
        return null;
      });
    });
  } catch (err) {
    console.log(err);
  }
});


/**
* To insert CP crossed tasks
* @backgroundJob  COL1
* @runtime 
* @returns
* <AUTHOR> Kumar S
* @version 1.0
*/
cron.schedule("30 18 * * *", async () => {
  try {
    let active_tenants = await active_tenant_service.getActiveTenants();

    let job_group_code = "COL1";

    let get_bg_job_active_tenants = await getCommonBGJobActiveTenantList(job_group_code, active_tenants);

    if (get_bg_job_active_tenants.messType == "E")
      return get_bg_job_active_tenants;

    let bg_job_active_tenants = get_bg_job_active_tenants.active_tenant_list;
    let params_info = get_bg_job_active_tenants.params_info;

    bg_job_active_tenants.forEach(async (tenant, index) => {

      let bodyParams = {
        job_name: "insertCpCrossedActivityTasks",
        job_type: "cron",
        job_subtype: "recurring",
        job_group_id: job_group_code,
        job_description: "Insert CP crossed Activities BG Job",
        tenant_id: tenant.tenant_id,
        message_routing_key: `collector_cp_crossed_activity_tasks_job_queue_key_${process.env.NODE_ENV}`,
        tenant_info: tenant,
        params_info: params_info
      };

      let config = {
        url: `http://${process.env.RABBIT_MQ_SERVICE || "localhost:3900"
          }/queue/addJob`,
        method: "POST",
        data: bodyParams,
        headers: {
          Authorization:
            "Bearer " +
            (await token_service.getTokenForApplication(
              tenant.tad,
              tenant.tenant_id
            )),
        },
      };

      let response_data = await axios(config).catch((err) => {
        console.log(err);
        return null;
      });
    });
  } catch (err) {
    console.log(err);
  }
});


/**
* create/deactivate/renewal mail subscription (change notifier for mail)
* @backgroundJob  MAILBG01
* @runtime Every 5 Minutes
* @returns
* <AUTHOR> Prasad S
* @version 1.0
*/
cron.schedule("*/5 * * * *", async () => {
  try {
    let active_tenants = await active_tenant_service.getActiveTenants();
    let job_group_code = "MAILBG01";
    let get_bg_job_active_tenants = await getCommonBGJobActiveTenantList(job_group_code,active_tenants);

    if(get_bg_job_active_tenants.messType == "E")
    return get_bg_job_active_tenants;

    let bg_job_active_tenants = get_bg_job_active_tenants.active_tenant_list;
    let params_info = get_bg_job_active_tenants.params_info;

    bg_job_active_tenants.forEach(async (tenant, index) => {

      let bodyParams = {
        job_name: "handleMailSubscription",
        job_type: "cron",
        job_subtype: "recurring",
        job_group_id: job_group_code,
        job_description: "Handle mail subscriptions for users",
        tenant_id: tenant.tenant_id,
        message_routing_key: `mail_change_notification_subscription_job_queue_key_${process.env.NODE_ENV}`,
        tenant_info: tenant,
        params_info : params_info
      };

      let config = {
        url: `http://${process.env.RABBIT_MQ_SERVICE || "localhost:3900"
          }/queue/addJob`,
        method: "POST",
        data: bodyParams,
        headers: {
          Authorization:
            "Bearer " +
            (await token_service.getTokenForApplication(
              tenant.tad,
              tenant.tenant_id
            )),
        },
      };

      let response_data = await axios(config).catch((err) => {
        console.log(err);
        return null;
      });
    });
  } catch (err) {
    console.log(err);
  }
});


/**
* @backgroundJob  PROJBG17
* @runtime Every 5 Minutes
* @description Update Item to Wave (P_and_l_id, profit_center)
* @returns
* <AUTHOR> Raam Baskar
*/
cron.schedule("*/5 * * * *", async () => {
  try {
    let active_tenants = await active_tenant_service.getActiveTenants();
    let job_group_code = "PROJBG17";
    let get_bg_job_active_tenants = await getCommonBGJobActiveTenantList(job_group_code,active_tenants);

    if(get_bg_job_active_tenants.messType == "E")
    return get_bg_job_active_tenants;

    let bg_job_active_tenants = get_bg_job_active_tenants.active_tenant_list;
    let params_info = get_bg_job_active_tenants.params_info;

    bg_job_active_tenants.forEach(async (tenant, index) => {

      let bodyParams = {
        job_name: "projectItemInfoToWave",
        job_type: "cron",
        job_subtype: "recurring",
        job_group_id: job_group_code,
        job_description: "Update Item Information to Wave",
        tenant_id: tenant.tenant_id,
        message_routing_key: `project_wave_to_item_job_queue_key_${process.env.NODE_ENV}`,
        tenant_info: tenant,
        params_info : params_info
      };

      let config = {
        url: `http://${process.env.RABBIT_MQ_SERVICE || "localhost:3900"
          }/queue/addJob`,
        method: "POST",
        data: bodyParams,
        headers: {
          Authorization:
            "Bearer " +
            (await token_service.getTokenForApplication(
              tenant.tad,
              tenant.tenant_id
            )),
        },
      };

      let response_data = await axios(config).catch((err) => {
        console.log(err);
        return null;
      });
    });
  } catch (err) {
    console.log(err);
  }
});

/**
* @backgroundJob  PROJBG18
* @runtime Every  Minute
* @description NEW RR
* @returns
* <AUTHOR> Kumar B
*/
cron.schedule("*/1 * * * *", async () => {
  try {
    let active_tenants = await active_tenant_service.getActiveTenants();
    let job_group_code = "PROJBG18";
    let get_bg_job_active_tenants = await getCommonBGJobActiveTenantList(job_group_code,active_tenants);

    if(get_bg_job_active_tenants.messType == "E")
    return get_bg_job_active_tenants;

    let bg_job_active_tenants = get_bg_job_active_tenants.active_tenant_list;
    let params_info = get_bg_job_active_tenants.params_info;

    bg_job_active_tenants.forEach(async (tenant, index) => {

      let bodyParams = {
        job_name: "GetRRNewProgram",
        job_type: "cron",
        job_subtype: "recurring",
        job_group_id: job_group_code,
        job_description: "Run the NEW revenue Recognition Program",
        tenant_id: tenant.tenant_id,
        message_routing_key: `project_NEW_revenue_recognition_job_queue_key_${process.env.NODE_ENV}`,
        tenant_info: tenant,
        params_info : params_info
      };

      let config = {
        url: `http://${process.env.RABBIT_MQ_SERVICE || "localhost:3900"
          }/queue/addJob`,
        method: "POST",
        data: bodyParams,
        headers: {
          Authorization:
            "Bearer " +
            (await token_service.getTokenForApplication(
              tenant.tad,
              tenant.tenant_id
            )),
        },
      };

      let response_data = await axios(config).catch((err) => {
        console.log(err);
        return null;
      });
    });
  } catch (err) {
    console.log(err);
  }
});

/**
* refresh hubspot token
* @backgroundJob  HUBBGOO1
* @runtime Every 20 Minutes
* @description Refresh hubspot token for 20min
* @returns
* <AUTHOR> Raam Baskar
* @version 1.0
*/
cron.schedule("*/20 * * * *", async () => {
  try {
    let active_tenants = await active_tenant_service.getActiveTenants();
    let job_group_code = "HUBBGOO1";
    let get_bg_job_active_tenants = await getCommonBGJobActiveTenantList(job_group_code,active_tenants);

    if(get_bg_job_active_tenants.messType == "E")
    return get_bg_job_active_tenants;

    let bg_job_active_tenants = get_bg_job_active_tenants.active_tenant_list;
    let params_info = get_bg_job_active_tenants.params_info;

    bg_job_active_tenants.forEach(async (tenant, index) => {

      let bodyParams = {
        job_name: "refresh_hubspot_token",
        job_type: "cron",
        job_subtype: "recurring",
        job_group_id: job_group_code,
        job_description: "Refresh App token in hubspot",
        tenant_id: tenant.tenant_id,
        message_routing_key: `refresh_hubspot_token_job_queue_key_${process.env.NODE_ENV}`,
        tenant_info: tenant,
        params_info : params_info
      };

      let config = {
        url: `http://${process.env.RABBIT_MQ_SERVICE || "localhost:3900"
          }/queue/addJob`,
        method: "POST",
        data: bodyParams,
        headers: {
          Authorization:
            "Bearer " +
            (await token_service.getTokenForApplication(
              tenant.tad,
              tenant.tenant_id
            )),
        },
      };

      let response_data = await axios(config).catch((err) => {
        console.log(err);
        return null;
      });
    });
  } catch (err) {
    console.log(err);
  }
});

/**
* Hubspot record changes
* @backgroundJob  HUBBGOO2
* @runtime 
* @description Fetch all the data based on the events and store it in staging table t_crm_hubspot_staging
* @returns
* <AUTHOR> Raam Baskar
* @version 1.0
*/
cron.schedule("*/2 * * * *", async () => {
  try {
    let active_tenants = await active_tenant_service.getActiveTenants();
    let job_group_code = "HUBBGOO2";
    let get_bg_job_active_tenants = await getCommonBGJobActiveTenantList(job_group_code,active_tenants);

    if(get_bg_job_active_tenants.messType == "E")
    return get_bg_job_active_tenants;

    let bg_job_active_tenants = get_bg_job_active_tenants.active_tenant_list;
    let params_info = get_bg_job_active_tenants.params_info;

    bg_job_active_tenants.forEach(async (tenant, index) => {

      let bodyParams = {
        job_name: "record_hubspot_change",
        job_type: "cron",
        job_subtype: "recurring",
        job_group_id: job_group_code,
        job_description: "Fetch all the data based on the events and store it in staging table t_crm_hubspot_staging",
        tenant_id: tenant.tenant_id,
        message_routing_key: `record_hubspot_change_job_queue_key_${process.env.NODE_ENV}`,
        tenant_info: tenant,
        params_info : params_info
      };

      let config = {
        url: `http://${process.env.RABBIT_MQ_SERVICE || "localhost:3900"
          }/queue/addJob`,
        method: "POST",
        data: bodyParams,
        headers: {
          Authorization:
            "Bearer " +
            (await token_service.getTokenForApplication(
              tenant.tad,
              tenant.tenant_id
            )),
        },
      };

      let response_data = await axios(config).catch((err) => {
        console.log(err);
        return null;
      });
    });
  } catch (err) {
    console.log(err);
  }
});


/**
* Hubspot record changes
* @backgroundJob  HUBBGOO3
* @runtime 
* @description Store all recently modified engagments in staging table t_crm_hubspot_engagements_staging
* @returns
* <AUTHOR> Raam Baskar
* @version 1.0
*/
cron.schedule("*/30 * * * *", async () => {
  try {
    let active_tenants = await active_tenant_service.getActiveTenants();
    let job_group_code = "HUBBGOO3";
    let get_bg_job_active_tenants = await getCommonBGJobActiveTenantList(job_group_code,active_tenants);

    if(get_bg_job_active_tenants.messType == "E")
    return get_bg_job_active_tenants;

    let bg_job_active_tenants = get_bg_job_active_tenants.active_tenant_list;
    let params_info = get_bg_job_active_tenants.params_info;

    bg_job_active_tenants.forEach(async (tenant, index) => {

      let bodyParams = {
        job_name: "record_hubspot_change_engagements",
        job_type: "cron",
        job_subtype: "recurring",
        job_group_id: job_group_code,
        job_description: "Store recently modified engagments from hubspot into staging table",
        tenant_id: tenant.tenant_id,
        message_routing_key: `record_hubspot_change_engagements_job_queue_key_${process.env.NODE_ENV}`,
        tenant_info: tenant,
        params_info : params_info
      };

      let config = {
        url: `http://${process.env.RABBIT_MQ_SERVICE || "localhost:3900"
          }/queue/addJob`,
        method: "POST",
        data: bodyParams,
        headers: {
          Authorization:
            "Bearer " +
            (await token_service.getTokenForApplication(
              tenant.tad,
              tenant.tenant_id
            )),
        },
      };

      let response_data = await axios(config).catch((err) => {
        console.log(err);
        return null;
      });
    });
  } catch (err) {
    console.log(err);
  }
});


/**
* Hubspot record changes
* @backgroundJob  HUBBGOO4
* @runtime 5min 
* @description Execute Hubspot data from staging table (t_crm_hubspot_staging) into KEBS 
* @returns
* <AUTHOR> Raam Baskar
* @version 1.0
*/
cron.schedule("*/5 * * * *", async () => {
  try {
    let active_tenants = await active_tenant_service.getActiveTenants();
    let job_group_code = "HUBBGOO4";
    let get_bg_job_active_tenants = await getCommonBGJobActiveTenantList(job_group_code,active_tenants);

    if(get_bg_job_active_tenants.messType == "E")
    return get_bg_job_active_tenants;

    let bg_job_active_tenants = get_bg_job_active_tenants.active_tenant_list;
    let params_info = get_bg_job_active_tenants.params_info;

    bg_job_active_tenants.forEach(async (tenant, index) => {

      let bodyParams = {
        job_name: "format_hubspot_data_into_kebs",
        job_type: "cron",
        job_subtype: "recurring",
        job_group_id: job_group_code,
        job_description: " Execute Hubspot data from staging table (t_crm_hubspot_staging) into KEBS ",
        tenant_id: tenant.tenant_id,
        message_routing_key: `format_hubspot_data_into_kebs_queue_key_${process.env.NODE_ENV}`,
        tenant_info: tenant,
        params_info : params_info
      };

      let config = {
        url: `http://${process.env.RABBIT_MQ_SERVICE || "localhost:3900"
          }/queue/addJob`,
        method: "POST",
        data: bodyParams,
        headers: {
          Authorization:
            "Bearer " +
            (await token_service.getTokenForApplication(
              tenant.tad,
              tenant.tenant_id
            )),
        },
      };

      let response_data = await axios(config).catch((err) => {
        console.log(err);
        return null;
      });
    });
  } catch (err) {
    console.log(err);
  }
});

/**
* Hubspot record changes
* @backgroundJob  HUBBGOO4
* @runtime 5min 
* @description Execute Hubspot data Engagments from staging table (t_crm_hubspot_staging) into KEBS 
* @returns
* <AUTHOR> Raam Baskar
* @version 1.0
*/
cron.schedule("*/20 * * * *", async () => {
  try {
    let active_tenants = await active_tenant_service.getActiveTenants();
    let job_group_code = "HUBBGOO5";
    let get_bg_job_active_tenants = await getCommonBGJobActiveTenantList(job_group_code,active_tenants);

    if(get_bg_job_active_tenants.messType == "E")
    return get_bg_job_active_tenants;

    let bg_job_active_tenants = get_bg_job_active_tenants.active_tenant_list;
    let params_info = get_bg_job_active_tenants.params_info;

    bg_job_active_tenants.forEach(async (tenant, index) => {

      let bodyParams = {
        job_name: "format_hubspot_engagements_data_into_kebs",
        job_type: "cron",
        job_subtype: "recurring",
        job_group_id: job_group_code,
        job_description: " Execute Hubspot data from staging table (t_crm_hubspot_staging) into KEBS ",
        tenant_id: tenant.tenant_id,
        message_routing_key: `format_hubspot_engagements_data_into_kebs_queue_key_${process.env.NODE_ENV}`,
        tenant_info: tenant,
        params_info : params_info
      };

      let config = {
        url: `http://${process.env.RABBIT_MQ_SERVICE || "localhost:3900"
          }/queue/addJob`,
        method: "POST",
        data: bodyParams,
        headers: {
          Authorization:
            "Bearer " +
            (await token_service.getTokenForApplication(
              tenant.tad,
              tenant.tenant_id
            )),
        },
      };

      let response_data = await axios(config).catch((err) => {
        console.log(err);
        return null;
      });
    });
  } catch (err) {
    console.log(err);
  }
});


/**
* Move Milestones to YTB
* @backgroundJob  PRJ0001
* @runtime 5min 
* @description Move Milestones to YTB
* @returns
* <AUTHOR> Raam Baskar
* @version 1.0
*/
cron.schedule("*/5 * * * *", async () => {
  try {
    let active_tenants = await active_tenant_service.getActiveTenants();
    let job_group_code = "PRJ0001";
    let get_bg_job_active_tenants = await getCommonBGJobActiveTenantList(job_group_code,active_tenants);

    if(get_bg_job_active_tenants.messType == "E")
    return get_bg_job_active_tenants;

    let bg_job_active_tenants = get_bg_job_active_tenants.active_tenant_list;
    let params_info = get_bg_job_active_tenants.params_info;

    bg_job_active_tenants.forEach(async (tenant, index) => {

      let bodyParams = {
        job_name: "automatic_milestone_ytb_status_month_end",
        job_type: "cron",
        job_subtype: "recurring",
        job_group_id: job_group_code,
        job_description: "Moves all the Month end milestones to YTB Status",
        tenant_id: tenant.tenant_id,
        message_routing_key: `automatic_milestone_ytb_status_month_end_queue_key_${process.env.NODE_ENV}`,
        tenant_info: tenant,
        params_info : params_info
      };

      let config = {
        url: `http://${process.env.RABBIT_MQ_SERVICE || "localhost:3900"
          }/queue/addJob`,
        method: "POST",
        data: bodyParams,
        headers: {
          Authorization:
            "Bearer " +
            (await token_service.getTokenForApplication(
              tenant.tad,
              tenant.tenant_id
            )),
        },
      };

      let response_data = await axios(config).catch((err) => {
        console.log(err);
        return null;
      });
    });
  } catch (err) {
    console.log(err);
  }
});


/**
* Item end date based notify
* @backgroundJob  IED0001
* @runtime 5min 
* @description Item end date based notify
* @returns
* <AUTHOR> K Vijay
* @version 1.0
*/
cron.schedule("*/5 * * * *", async () => {
  try {
    let active_tenants = await active_tenant_service.getActiveTenants();
    let job_group_code = "IED0001";
    let get_bg_job_active_tenants = await getCommonBGJobActiveTenantList(job_group_code,active_tenants);

    if(get_bg_job_active_tenants.messType == "E")
    return get_bg_job_active_tenants;

    let bg_job_active_tenants = get_bg_job_active_tenants.active_tenant_list;
    let params_info = get_bg_job_active_tenants.params_info;

    bg_job_active_tenants.forEach(async (tenant, index) => {

      let bodyParams = {
        job_name: "automatic_item_end_date_based_notify",
        job_type: "cron",
        job_subtype: "recurring",
        job_group_id: job_group_code,
        job_description: "Notification send project manager based on item end date",
        tenant_id: tenant.tenant_id,
        message_routing_key: `automatic_item_end_date_based_notify_queue_key_${process.env.NODE_ENV}`,
        tenant_info: tenant,
        params_info : params_info
      };

      let config = {
        url: `http://${process.env.RABBIT_MQ_SERVICE || "localhost:3900"
          }/queue/addJob`,
        method: "POST",
        data: bodyParams,
        headers: {
          Authorization:
            "Bearer " +
            (await token_service.getTokenForApplication(
              tenant.tad,
              tenant.tenant_id
            )),
        },
      };

      let response_data = await axios(config).catch((err) => {
        console.log(err);
        return null;
      });
    });
  } catch (err) {
    console.log(err);
  }
});


/**
*  Project Time Tracker Report
* @backgroundJob  IED0002
* @runtime 5min 
* @description Project Time Tracker Report
* @returns
* <AUTHOR> Raam Baskar
* @version 1.0
*/
cron.schedule("0 */4 * * *", async () => {
  try {
    let active_tenants = await active_tenant_service.getActiveTenants();
    let job_group_code = "IED0002";
    let get_bg_job_active_tenants = await getCommonBGJobActiveTenantList(job_group_code,active_tenants);
    
    if(get_bg_job_active_tenants.messType == "E")
    return get_bg_job_active_tenants;

    let bg_job_active_tenants = get_bg_job_active_tenants.active_tenant_list;
    let params_info = get_bg_job_active_tenants.params_info;

    bg_job_active_tenants.forEach(async (tenant, index) => {

      let bodyParams = {
        job_name: "project_time_tracker_report",
        job_type: "cron",
        job_subtype: "recurring",
        job_group_id: job_group_code,
        job_description: "Notification send project manager based on item end date",
        tenant_id: tenant.tenant_id,
        message_routing_key: `project_time_tracker_report_queue_key_${process.env.NODE_ENV}`,
        tenant_info: tenant,
        params_info : params_info
      };

      let config = {
        url: `http://${process.env.RABBIT_MQ_SERVICE || "localhost:3900"
          }/queue/addJob`,
        method: "POST",
        data: bodyParams,
        headers: {
          Authorization:
            "Bearer " +
            (await token_service.getTokenForApplication(
              tenant.tad,
              tenant.tenant_id
            )),
        },
      };

      let response_data = await axios(config).catch((err) => {
        console.log(err);
        return null;
      });
    });
  } catch (err) {
    console.log(err);
  }
});

/**
*  KEBS Outbound Integration
* @backgroundJob  IED0002
* @runtime 5min 
* @description Check Outbound Integrations
* @returns
* <AUTHOR> Raam Baskar
* @version 1.0
*/
cron.schedule("*/1 * * * *", async () => {
  try {
    let active_tenants = await active_tenant_service.getActiveTenants();
    let job_group_code = "KOB0001";
    let get_bg_job_active_tenants = await getCommonBGJobActiveTenantList(job_group_code,active_tenants);

    if(get_bg_job_active_tenants.messType == "E")
    return get_bg_job_active_tenants;

    let bg_job_active_tenants = get_bg_job_active_tenants.active_tenant_list;
    let params_info = get_bg_job_active_tenants.params_info;

    bg_job_active_tenants.forEach(async (tenant, index) => {

      let bodyParams = {
        job_name: "kebs_outbound_integration",
        job_type: "cron",
        job_subtype: "recurring",
        job_group_id: job_group_code,
        job_description: "KEBS Outbound Integration Job",
        tenant_id: tenant.tenant_id,
        message_routing_key: `kebs_outbound_integration_queue_key_${process.env.NODE_ENV}`,
        tenant_info: tenant,
        params_info : params_info
      };

      let config = {
        url: `http://${process.env.RABBIT_MQ_SERVICE || "localhost:3900"
          }/queue/addJob`,
        method: "POST",
        data: bodyParams,
        headers: {
          Authorization:
            "Bearer " +
            (await token_service.getTokenForApplication(
              tenant.tad,
              tenant.tenant_id
            )),
        },
      };

      let response_data = await axios(config).catch((err) => {
        console.log(err);
        return null;
      });
    });
  } catch (err) {
    console.log(err);
  }
});


/**
*  Build Employee Hours
* @backgroundJob  IED0002
* @runtime 5min 
* @description KEBS Build Employee Hours
* @returns
* <AUTHOR> Raam Baskar
* @version 1.0
*/
cron.schedule("*/30 * * * *", async () => {
  try {
    let active_tenants = await active_tenant_service.getActiveTenants();
    let job_group_code = "PED0001";
    let get_bg_job_active_tenants = await getCommonBGJobActiveTenantList(job_group_code,active_tenants);

    if(get_bg_job_active_tenants.messType == "E")
    return get_bg_job_active_tenants;

    let bg_job_active_tenants = get_bg_job_active_tenants.active_tenant_list;
    let params_info = get_bg_job_active_tenants.params_info;

    bg_job_active_tenants.forEach(async (tenant, index) => {

      let bodyParams = {
        job_name: "project_efficiency_dashboard",
        job_type: "cron",
        job_subtype: "recurring",
        job_group_id: job_group_code,
        job_description: "Project Efficiency Dashboard",
        tenant_id: tenant.tenant_id,
        message_routing_key: `project_efficiency_dashboard_queue_key_${process.env.NODE_ENV}`,
        tenant_info: tenant,
        params_info : params_info
      };

      let config = {
        url: `http://${process.env.RABBIT_MQ_SERVICE || "localhost:3900"
          }/queue/addJob`,
        method: "POST",
        data: bodyParams,
        headers: {
          Authorization:
            "Bearer " +
            (await token_service.getTokenForApplication(
              tenant.tad,
              tenant.tenant_id
            )),
        },
      };

      let response_data = await axios(config).catch((err) => {
        console.log(err);
        return null;
      });
    });
  } catch (err) {
    console.log(err);
  }
});




/**
* Item end date based notify
* @backgroundJob  IED0001
* @runtime 5min 
* @description Item end date based notify
* @returns
* <AUTHOR> L
* @version 1.0
*/
cron.schedule("*/5 * * * *", async () => {
  try {
    let active_tenants = await active_tenant_service.getActiveTenants();
    let job_group_code = "NOB0001";
    let get_bg_job_active_tenants = await getCommonBGJobActiveTenantList(job_group_code,active_tenants);

    if(get_bg_job_active_tenants.messType == "E")
    return get_bg_job_active_tenants;

    let bg_job_active_tenants = get_bg_job_active_tenants.active_tenant_list;
    let params_info = get_bg_job_active_tenants.params_info;

    bg_job_active_tenants.forEach(async (tenant, index) => {

      let bodyParams = {
        job_name: "automatic_po_end_date_based_notify",
        job_type: "cron",
        job_subtype: "recurring",
        job_group_id: job_group_code,
        job_description: "Notification send project manager based on po end date",
        tenant_id: tenant.tenant_id,
        message_routing_key: `automatic_po_end_date_based_notify_queue_key_${process.env.NODE_ENV}`,
        tenant_info: tenant,
        params_info : params_info
      };

      let config = {
        url: `http://${process.env.RABBIT_MQ_SERVICE || "localhost:3900"
          }/queue/addJob`,
        method: "POST",
        data: bodyParams,
        headers: {
          Authorization:
            "Bearer " +
            (await token_service.getTokenForApplication(
              tenant.tad,
              tenant.tenant_id
            )),
        },
      };

      let response_data = await axios(config).catch((err) => {
        console.log(err);
        return null;
      });
    });
  } catch (err) {
    console.log(err);
  }
});

/**
* gET EMPLOYEE AVAILABLE HOURS
* @backgroundJob  IED0002
* @runtime 5min 
* @description gET EMPLOYEE AVAILABLE HOURS
* @returns
* <AUTHOR> L
* @version 1.0
*/
cron.schedule("*/5 * * * *", async () => {
  try {
    let active_tenants = await active_tenant_service.getActiveTenants();
    let job_group_code = "NOB0002";
    let get_bg_job_active_tenants = await getCommonBGJobActiveTenantList(job_group_code,active_tenants);

    if(get_bg_job_active_tenants.messType == "E")
    return get_bg_job_active_tenants;

    let bg_job_active_tenants = get_bg_job_active_tenants.active_tenant_list;
    let params_info = get_bg_job_active_tenants.params_info;

    bg_job_active_tenants.forEach(async (tenant, index) => {

      let bodyParams = {
        job_name: "employee_available_hours",
        job_type: "cron",
        job_subtype: "recurring",
        job_group_id: job_group_code,
        job_description: "EMPLOYEE AVAILABLE HOURS",
        tenant_id: tenant.tenant_id,
        message_routing_key: `employee_available_hours_queue_key_${process.env.NODE_ENV}`,
        tenant_info: tenant,
        params_info : params_info
      };

      let config = {
        url: `http://${process.env.RABBIT_MQ_SERVICE || "localhost:3900"
          }/queue/addJob`,
        method: "POST",
        data: bodyParams,
        headers: {
          Authorization:
            "Bearer " +
            (await token_service.getTokenForApplication(
              tenant.tad,
              tenant.tenant_id
            )),
        },
      };

      let response_data = await axios(config).catch((err) => {
        console.log(err);
        return null;
      });
    });
  } catch (err) {
    console.log(err);
  }
});


/**
* SLA Notification Trigger
* @backgroundJob  PA0001
* @runtime 5min 
* @description SLA Notification Trigger
* @returns
* <AUTHOR> Raam Baskar
* @version 1.0
*/
cron.schedule("*/5 * * * *", async () => {
  try {
    let active_tenants = await active_tenant_service.getActiveTenants();
    let job_group_code = "PA0001";
    let get_bg_job_active_tenants = await getCommonBGJobActiveTenantList(job_group_code,active_tenants);

    if(get_bg_job_active_tenants.messType == "E")
    return get_bg_job_active_tenants;

    let bg_job_active_tenants = get_bg_job_active_tenants.active_tenant_list;
    let params_info = get_bg_job_active_tenants.params_info;

    bg_job_active_tenants.forEach(async (tenant, index) => {

      let bodyParams = {
        job_name: "request_sla_notification",
        job_type: "cron",
        job_subtype: "recurring",
        job_group_id: job_group_code,
        job_description: "Request SLA Notification to RMG Head",
        tenant_id: tenant.tenant_id,
        message_routing_key: `request_sla_notification_queue_key_${process.env.NODE_ENV}`,
        tenant_info: tenant,
        params_info : params_info
      };

      let config = {
        url: `http://${process.env.RABBIT_MQ_SERVICE || "localhost:3900"
          }/queue/addJob`,
        method: "POST",
        data: bodyParams,
        headers: {
          Authorization:
            "Bearer " +
            (await token_service.getTokenForApplication(
              tenant.tad,
              tenant.tenant_id
            )),
        },
      };

      let response_data = await axios(config).catch((err) => {
        console.log(err);
        return null;
      });
    });
  } catch (err) {
    console.log(err);
  }
});


/**
* Item end date based notify
* @backgroundJob  IED0001
* @runtime 5min 
* @description Item end date based notify
* @returns
* <AUTHOR> L
* @version 1.0
*/
cron.schedule("*/5 * * * *", async () => {
  try {
    let active_tenants = await active_tenant_service.getActiveTenants();
    let job_group_code = "NOB0004";
    let get_bg_job_active_tenants = await getCommonBGJobActiveTenantList(job_group_code,active_tenants);

    if(get_bg_job_active_tenants.messType == "E")
    return get_bg_job_active_tenants;

    let bg_job_active_tenants = get_bg_job_active_tenants.active_tenant_list;
    let params_info = get_bg_job_active_tenants.params_info;

    bg_job_active_tenants.forEach(async (tenant, index) => {

      let bodyParams = {
        job_name: "automatic_reports_to_allocation_based_notify",
        job_type: "cron",
        job_subtype: "recurring",
        job_group_id: job_group_code,
        job_description: "Notification send project manager based on reports to allocation",
        tenant_id: tenant.tenant_id,
        message_routing_key: `automatic_reports_to_allocation_based_notify_queue_key_${process.env.NODE_ENV}`,
        tenant_info: tenant,
        params_info : params_info
      };

      let config = {
        url: `http://${process.env.RABBIT_MQ_SERVICE || "localhost:3900"
          }/queue/addJob`,
        method: "POST",
        data: bodyParams,
        headers: {
          Authorization:
            "Bearer " +
            (await token_service.getTokenForApplication(
              tenant.tad,
              tenant.tenant_id
            )),
        },
      };

      let response_data = await axios(config).catch((err) => {
        console.log(err);
        return null;
      });
    });
  } catch (err) {
    console.log(err);
  }
});

/**
* Notification send Delivery manager/Head based on allocation end date
* @backgroundJob  NOB0005
* @runtime 5min 
* @description Notification send Delivery manager/Head based on allocation end date
* @returns
* <AUTHOR> L
* @version 1.0
*/
cron.schedule("*/5 * * * *", async () => {
  try {
    let active_tenants = await active_tenant_service.getActiveTenants();
    let job_group_code = "NOB0005";
    let get_bg_job_active_tenants = await getCommonBGJobActiveTenantList(job_group_code,active_tenants);

    if(get_bg_job_active_tenants.messType == "E")
    return get_bg_job_active_tenants;

    let bg_job_active_tenants = get_bg_job_active_tenants.active_tenant_list;
    let params_info = get_bg_job_active_tenants.params_info;

    bg_job_active_tenants.forEach(async (tenant, index) => {

      let bodyParams = {
        job_name: "automatic_po_allocation_end_date_based_notify",
        job_type: "cron",
        job_subtype: "recurring",
        job_group_id: job_group_code,
        job_description: "Notification send Delivery manager/Head based on allocation end date",
        tenant_id: tenant.tenant_id,
        message_routing_key: `automatic_po_allocation_end_date_based_notify_queue_key_${process.env.NODE_ENV}`,
        tenant_info: tenant,
        params_info : params_info
      };

      let config = {
        url: `http://${process.env.RABBIT_MQ_SERVICE || "localhost:3900"
          }/queue/addJob`,
        method: "POST",
        data: bodyParams,
        headers: {
          Authorization:
            "Bearer " +
            (await token_service.getTokenForApplication(
              tenant.tad,
              tenant.tenant_id
            )),
        },
      };

      let response_data = await axios(config).catch((err) => {
        console.log(err);
        return null;
      });
    });
  } catch (err) {
    console.log(err);
  }
});



/**
* Send Mail Notification for KAIS Reports
* @backgroundJob  NOB0005
* @runtime 5min 
* @description Check for KAIS Report 
* @returns
* <AUTHOR> Raam
* @version 1.0
*/
cron.schedule("*/1 * * * *", async () => {
  try {
    let active_tenants = await active_tenant_service.getActiveTenants();
    let job_group_code = "KAIS00001";
    let get_bg_job_active_tenants = await getCommonBGJobActiveTenantList(job_group_code,active_tenants);

    if(get_bg_job_active_tenants.messType == "E")
    return get_bg_job_active_tenants;

    let bg_job_active_tenants = get_bg_job_active_tenants.active_tenant_list;
    let params_info = get_bg_job_active_tenants.params_info;

    bg_job_active_tenants.forEach(async (tenant, index) => {

      let bodyParams = {
        job_name: "kais_query_result",
        job_type: "cron", 
        job_subtype: "recurring",
        job_group_id: job_group_code,
        job_description: "KAIS Report Mail Notification",
        tenant_id: tenant.tenant_id,
        message_routing_key: `kais_query_result_queue_key_${process.env.NODE_ENV}`,
        tenant_info: tenant,
        params_info : params_info
      };

      let config = {
        url: `http://${process.env.RABBIT_MQ_SERVICE || "localhost:3900"
          }/queue/addJob`,
        method: "POST",
        data: bodyParams,
        headers: {
          Authorization:
            "Bearer " +
            (await token_service.getTokenForApplication(
              tenant.tad,
              tenant.tenant_id
            )),
        },
      };

      let response_data = await axios(config).catch((err) => {
        console.log(err);
        return null;
      });
    });
  } catch (err) {
    console.log(err);
  }
});