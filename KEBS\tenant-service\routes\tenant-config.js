var express = require("express");
var app = express();
var router = express.Router();
const tenant_service = require("../services/tenant-config-service");

router.post("/getTenantInfo", tenant_service.getTenantInfo);

router.post("/createTenantDatabase", tenant_service.createTenantDatabase);

router.post("/cloneCollection", tenant_service.cloneCollection);

router.post("/createTenantDomain", tenant_service.createTenantDomain);

router.post("/getActiveTenants", tenant_service.getActiveTenants);

router.post(
  "/performTenantCreationOperation",
  tenant_service.performTenantCreationOperation
);


module.exports = router;
