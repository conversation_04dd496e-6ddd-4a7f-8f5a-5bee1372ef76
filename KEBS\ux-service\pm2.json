{"name": "uxprd", "script": "./server.js", "instances": "1", "env_prd": {"NODE_ENV": "prd", "PORT": 3099, "BGJ": 0, "HAS_SERVER": 1, "ACL_NODE_PORT": "acl:3001", "PROJECT_NODE_PORT": "project:3002", "WORKFLOW_NODE_PORT": "workflow:3012", "SALES_NODE_PORT": "salesnode:3011", "TESTING_NODE_PORT": "testing:3015", "TIMESHEET_NODE_PORT": "timesheet:3013", "INTEGRATION_NODE_PORT": "integrationsnode:3022", "ISA_NODE_PORT": "isa:3021", "APPBUILDER_NODE_PORT": "appbuilder:3020", "AUTH_SERVICE": "auth-service:3800", "CONTAINER_ADDRESS": "*************:4318", "CONTAINER_NAME": "uxprd"}, "exec_mode": "cluster", "wait_ready": true, "listen_timeout": 5000, "error_file": "/dev/null", "out_file": "/dev/null"}