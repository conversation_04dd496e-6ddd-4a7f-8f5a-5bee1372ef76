const mysql = require('mysql');
const logger = require(`../logger`).logger;
const _ = require('underscore');
const axios = require('axios');
const pool = require("../databaseCon").pool;
const rpool = require("../databaseCon").rpool;
const mongo_native = require("../mongo_conn_native").Connection;

module.exports.log = async (db_name, user, log) => {
    try {
        let db = mongo_native.client.db(db_name);

        let insert = {
            user: user,
            log: log,
            created_on: new Date().toISOString()
        }
        let insert_result = await db
            .collection('t_user_preferences_logs')
            .insertOne({ insert });
        return Promise.resolve({
            messType: "S",
            result: insert_result
        })
    }
    catch (err) {
        console.log(err)
        return Promise.resolve({
            messType: "E",
            error: {
                err: err?.message,
                stack: err?.stack,
                err_obj: err
            }
        })
    }
}